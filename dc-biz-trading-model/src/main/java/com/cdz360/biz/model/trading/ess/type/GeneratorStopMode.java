package com.cdz360.biz.model.trading.ess.type;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "柴油机启停模式")
@Getter
public enum GeneratorStopMode {

    UNKNOWN(0, "未知"),
    SOC(1, "SOC"),
    TIME(2, "时间"),
    MANUAL(4, "手动");

    private final int code;
    private final String desc;

    GeneratorStopMode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
