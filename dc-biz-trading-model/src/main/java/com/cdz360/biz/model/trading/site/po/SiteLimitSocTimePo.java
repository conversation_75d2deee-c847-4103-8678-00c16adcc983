package com.cdz360.biz.model.trading.site.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

/**
 * SiteLimitSocTimePo
 *
 * @since 5/21/2020 2:59 PM
 * <AUTHOR>
 */
@Data
public class SiteLimitSocTimePo {
    private Long id;
    @Schema(description = "场站id", required = true)
    private String siteId;
    @Schema(description = "开始时间,闭区间,最小0,单位 分钟", hidden = true)
    private Integer startTime;
    @Schema(description = "开始时间,00:12", required = true)
    private String startTimeStr;
    @Schema(description = "结束时间,开区间,最大14:40,单位 分钟", hidden = true)
    private Integer endTime;
    @Schema(description = "结束时间,12:34", required = true)
    private String endTimeStr;
    @Schema(description = "创建时间")
    private Date createTime;
}