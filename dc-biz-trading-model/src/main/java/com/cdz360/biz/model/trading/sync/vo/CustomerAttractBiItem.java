package com.cdz360.biz.model.trading.sync.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "引流数据")
public class CustomerAttractBiItem {

    @Schema(description = "新增普客个数")
    private Long userNum;

    @Schema(description = "新增企客个数")
    private Long corpNum;

    @Schema(description = "普客充电量")
    private BigDecimal userKwh;

    @Schema(description = "企客充电量")
    private BigDecimal corpKwh;

}
