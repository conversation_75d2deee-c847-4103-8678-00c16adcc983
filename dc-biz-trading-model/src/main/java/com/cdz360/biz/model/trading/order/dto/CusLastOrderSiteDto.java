package com.cdz360.biz.model.trading.order.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * CusLastOrderSiteVo
 *
 * @since 12/23/2020 2:45 PM
 * <AUTHOR>
 */

@Data
@Schema(description = "用户最近一次站点信息")
@Accessors(chain = true)
public class CusLastOrderSiteDto {

    @Schema(description = "客户ID")
    private Long uid;

    @Schema(description = "最近一次站点名称", example = "XXX站点")
    private String siteName;

    @Schema(description = "站点Id", example = "1234567")
    private String siteId;
}