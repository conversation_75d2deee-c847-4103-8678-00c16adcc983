package com.cdz360.biz.model.trading.ess.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "储能统计数据")
@Data
@Accessors(chain = true)
public class EssDataBi implements Serializable {

    @Schema(description = "收益", example = "123.4 可为负值")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal profit;

    @Schema(description = "总放电电量", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal outKwh;

    @Schema(description = "总放电收入，单位: 元", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal outIncome;

    @Schema(description = "总充电电量", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal inKwh;

    @Schema(description = "总充电支出，单位: 元", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal inExpend;

    @Schema(description = "储能内部耗电量", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal internalPower;

}
