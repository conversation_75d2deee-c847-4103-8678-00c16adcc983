package com.cdz360.biz.model.trading.coupon.vo;

import com.cdz360.biz.model.trading.coupon.po.ActivityCouponRulePo;
import com.cdz360.biz.model.trading.coupon.po.ActivityImagePo;
import com.cdz360.biz.model.trading.coupon.po.ActivityPo;
import com.cdz360.biz.model.trading.coupon.po.ChargePo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ActivityVo extends ActivityPo {

    private Long activityTopCommId;

    // 活动关联的券模板
    private List<CouponDictVo> dictList;

    private List<ActivityImagePo> imageList;

    private String commName;

    @Schema(description = "已领券人数")
    private Long alreadyAcquireNum;

    @Schema(description = "满赠活动信息")
    private List<ChargePo> chargeList;

    @Schema(description = "规则参数列表")
    private List<ActivityCouponRulePo> ruleParamList;

    /**
     * 是否允许编辑，同名的活动仅最新一条允许编辑，修改后的活动，之前的活动不允许再次编辑
     */
    @Schema(description = "是否允许编辑")
    private Boolean allowEdit;
}
