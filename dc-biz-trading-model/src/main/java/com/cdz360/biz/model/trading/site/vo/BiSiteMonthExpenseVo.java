package com.cdz360.biz.model.trading.site.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "运营场站月支出项")
@Data
@Accessors(chain = true)
public class BiSiteMonthExpenseVo {

    @Schema(description = "场站ID")
    @JsonInclude(Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "年月")
    @JsonInclude(Include.NON_NULL)
    private Date month;

    @Schema(description = "分类. 散客,互联企业,预付企业,后付企业,未结算")
    @JsonInclude(Include.NON_EMPTY)
    private String category;

    @Schema(description = "名称")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal elec;

    @Schema(description = "充电量,按上传时间统计,单位: kWh")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal elecFee;

    @Schema(description = "平台电费")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal servFee;

    @Schema(description = "平台服务费")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal elecCost;

    @Schema(description = "电费实收")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal servCost;

    @Schema(description = "服务费实收")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal elecProfit;

    @Schema(description = "电费收益")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal servProfit;

}
