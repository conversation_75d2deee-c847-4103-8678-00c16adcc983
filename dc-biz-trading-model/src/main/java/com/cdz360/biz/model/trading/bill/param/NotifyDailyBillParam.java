package com.cdz360.biz.model.trading.bill.param;

import com.cdz360.biz.model.trading.bill.type.DailyBillDownloadResult;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "通知对账回调参数")
@Data
@Accessors(chain = true)
public class NotifyDailyBillParam {
    @Schema(description = "对账单ID(t_zft_daily_bill.id)", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long dailyBillId;

    @Schema(description = "企业客户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long corpId;

    @Schema(description = "账单下载结果", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @NotNull(message = "账单下载结果不能为空")
    private DailyBillDownloadResult result;

    @Schema(description = "账单下载失败提示信息 账单下载失败才会存在")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String failMsg;

    @Schema(description = "阿里云OSS对应路径 上传成功才会存在")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String downloadUrl;
}
