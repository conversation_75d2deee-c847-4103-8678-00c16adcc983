package com.cdz360.biz.model.trading.hlht.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

@Data
@Schema(description = "hlht场站信息暂存")
public class HlhtSitePo {

    private Long id;

    private String partnerCode;

    private String stationId;

    private String name;

    private String evseOwnerCode;

    private String evseOwnerName;

    private String areaCode;

    private String address;

    private Integer status;

    private Integer evseNum;

    private Integer plugNum;

    private Boolean enable;

    private Date crateTime;

    private Date updateTime;
}
