package com.cdz360.biz.model.trading.profit.sett.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "充电订单时间维度")
@Getter
public enum ProfitCfgTimeTarget implements DcEnum {

    CREATE_TIME(1, "创建时间"),
    PAY_TIME(2, "支付时间"),
    STOP_TIME(3, "上传时间"),
    CHARGE_START_TIME(4, "充电开始时间"),
    CHARGE_END_TIME(5, "充电结束时间");

    @JsonValue
    private final int code;

    private final String desc;

    ProfitCfgTimeTarget(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static ProfitCfgTimeTarget valueOf(Object codeIn) {
        if (codeIn == null) {
            return null;
        }
        int code = 0;
        if (codeIn instanceof ProfitCfgTimeTarget) {
            return (ProfitCfgTimeTarget) codeIn;
        } else if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (ProfitCfgTimeTarget type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }
}
