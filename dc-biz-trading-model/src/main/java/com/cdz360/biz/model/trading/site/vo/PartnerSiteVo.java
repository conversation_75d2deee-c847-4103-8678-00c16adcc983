package com.cdz360.biz.model.trading.site.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "互联互通站点信息")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class PartnerSiteVo extends SiteVo {

    @Schema(description = "互联运营商")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String partnerCode;

    @Schema(description = "互联运营商名称，模糊")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String partnerName;

    @Schema(description = "设备所属运营商")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String evseOwnerCode;

    @Schema(description = "设备所属运营商名称，模糊")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String evseOwnerName;
}
