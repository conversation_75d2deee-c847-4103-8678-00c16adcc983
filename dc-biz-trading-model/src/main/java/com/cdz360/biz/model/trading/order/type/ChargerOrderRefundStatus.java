package com.cdz360.biz.model.trading.order.type;

import com.cdz360.base.model.base.type.DcEnum;
import lombok.Getter;

@Getter
public enum ChargerOrderRefundStatus implements DcEnum {

    UNKNOWN(0, "未知"),

    INIT(1, "初始化"),

    SUBMIT(2, "已提交"),

    FINISH(3, "已退款"),

    FAIL(4, "退款失败");

    private final int code;
    private final String desc;

    ChargerOrderRefundStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ChargerOrderRefundStatus valueOf(int code) {
        for (ChargerOrderRefundStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return ChargerOrderRefundStatus.UNKNOWN;
    }

}
