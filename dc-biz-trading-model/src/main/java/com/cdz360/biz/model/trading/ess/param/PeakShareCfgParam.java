package com.cdz360.biz.model.trading.ess.param;


import com.cdz360.biz.model.ess.po.RangeTime;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;


@Data

@Accessors(chain = true)

@Schema(description = "储能模板配置-削峰填谷")

public class PeakShareCfgParam {


    @Schema(description = "削峰填谷使能")
    private Boolean peakFillEnable;

    @Schema(description = "峰时间段, 单位: 分钟: [{start: 100, end: 600}]")
    private List<RangeTime> peakTime;

    @Schema(description = "峰时段功率, 单位: kW")
    private BigDecimal peakPower;

    @Schema(description = "谷时间段, 单位: 分钟: [{start: 100, end: 600}]")
    private List<RangeTime> valleyTime;

    @Schema(description = "谷时段功率, 单位: kW")
    private BigDecimal valleyPower;
}

