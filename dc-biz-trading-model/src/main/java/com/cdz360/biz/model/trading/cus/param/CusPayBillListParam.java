package com.cdz360.biz.model.trading.cus.param;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.biz.model.common.param.BaseListParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Set;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2020/2/14 10:29
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(description = "用户个人账户可用充值记录查询请求参数")
public class CusPayBillListParam extends BaseListParam {
    @Schema(description = "用户Id")
    private Long uid;

    @Schema(description = "顶级商户Id")
    private Long topCommId;

    @Schema(description = "商户Id")
    private Long commId;

    @Schema(description = "客户端类型")
    private AppClientType clientType;

    private Set<String> orderNoList;

    // 退款记录操作者信息
    private Long opUid;
    private String opName;
}
