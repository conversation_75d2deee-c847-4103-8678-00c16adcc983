package com.cdz360.biz.model.trading.hlht.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "查询计费业务策略信息响应")
public class CecQueryQeuipBusinessPolicyResult {

    @JsonProperty(value = "EquipBizSeq")
    @Schema(description = "业务策略查询流水号 格式”运营商 ID+唯一编号“，27 字符", required = true)
    private String equipBizSeq;

    @JsonProperty(value = "ConnectorID")
    @Schema(description = "充电设备接口编码", required = true)
    private String connectorID;

    @JsonProperty(value = "SuccStat")
    @Schema(description = "操作结果 0.成功 1.失败", required = true)
    private Integer succStat;

    @JsonProperty(value = "FailReason")
    @Schema(description = "失败原因 0.无 1.业务策略不存在 2.参数错误", required = true)
    private Integer failReason;

    @JsonProperty(value = "SumPeriod")
    @Schema(description = "时段数 0.成功 1.失败", required = true)
    private Integer sumPeriod;

    @JsonProperty(value = "PolicyInfos")
    @Schema(description = "计费信息 0.成功 1.失败", required = true)
    private List<CecPolicyInfo> policyInfos;

    @JsonProperty(value = "FullPlugNo")
    @Schema(description = "充电设备接口平台上完整编码")
    private String fullPlugNo;
}
