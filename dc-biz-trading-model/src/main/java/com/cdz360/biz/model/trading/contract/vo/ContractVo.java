package com.cdz360.biz.model.trading.contract.vo;

import com.cdz360.biz.model.trading.file.po.OssFilePo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 新增合约
 */
@Data
@Accessors(chain = true)
@Schema(description = "新增合约")
public class ContractVo {


    @Schema(description = "合约ID")
    private Long id;

    @Schema(description = "合约名称")
    private String contractName;

    @Schema(description = "合约编号")
    private String contractNo;

    @Schema(description = "费用")
    private BigDecimal fee;

    @Schema(description = "费用")
    private BigDecimal amount;

    @Schema(description = "合同类型")
    private String contractType;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "互联互通合约，客户侧标志")
    private String operatorCode;

    @Schema(description = "合约摘要")
    private String remark;

    @Schema(description = "所属商户")
    private Long commId;

    @Schema(description = "详情信息")
    private Map detail;

    @Schema(description = "所属商户顶级商户")
    private Long topCommId;

    @Schema(description = "收费方式")
    private Long payType;

    @Schema(description = "收费周期")
    private Long payCycle;

    @Schema(description = "收费期限")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    @Schema(description = "签订日期")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date signTime;

    @Schema(description = "开始日期")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @Schema(description = "截止日期")
    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    @JsonFormat(locale="zh", timezone="GMT+8", pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "质保期")
    private Date qualityTime;

    @Schema(description = "所属场站")
    private String siteIds;

    @Schema(description = "所属商户名称")
    private String commName;

    @Schema(description = "所属场站")
    private String siteNameList;

    @Schema(description = "场站总数")
    private Long  siteAmount;

    @Schema(description = "所属场站")
    private List<OssFilePo> ossFileList;

    @Schema(description = "录入人ID")
    private Long  sysUid;

    @Schema(description = "录入人")
    private String  username;

    @Schema(description = "结算成本")
    private Boolean  isSettle;

    @Schema(description = "结算详情")
    private String  settleInfo;

    @Schema(description = "合约状态")
    private Long  status;

}
