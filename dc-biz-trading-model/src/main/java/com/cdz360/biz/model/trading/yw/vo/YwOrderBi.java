package com.cdz360.biz.model.trading.yw.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "运维人员运维统计")
@Data
@Accessors(chain = true)
public class YwOrderBi {

    @Schema(description = "运维人ID", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long maintUid;

    @Schema(description = "运维人名字", required = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String maintName;

    @Schema(description = "运维记录数", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer mainRecNum;
}
