package com.cdz360.biz.model.trading.order.po;

import com.cdz360.base.model.charge.type.BatteryType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "充电订单的车辆信息")
public class ChargerOrderCarPo {

    public ChargerOrderCarPo() {

    }

    public ChargerOrderCarPo(String orderNo) {
        this.orderNo = orderNo;
    }

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "车牌号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String carNo;

    @Schema(description = "车辆Vin码")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String vin;

    @Schema(description = "车辆线路名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String lineNum;

    @Schema(description = "车队名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String carDepart;

    @Schema(description = "车辆自编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String carNum;

    @Schema(description = "品牌")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String brand;

    @Schema(description = "型号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String model;

    @Schema(description = "国标协议版本")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer gbVer;

    @Schema(description = "BMS通信协议版本号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String bmsProtocol;

    @Schema(description = "BMS软件版本号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String bmsSwVer;

    @Schema(description = "BMS辅助电压. 12V, 24V")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer bmsVoltage;

    @Schema(description = "整车最高允许充电总电压. 单位: V")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal maxVoltage;

    @Schema(description = "整车最高允许充电总电流. 单位: A")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal maxCurrent;

    @Schema(description = "整车当前电池电压. 单位: V")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal startVoltage;

    @Schema(description = "最高允许温度. 单位: ℃")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer maxTemp;

    @Schema(description = "电池类型. 1铅酸电池, 2镍氢电池, 3磷酸铁锂电池, 4锰酸锂电池, 5钴酸锂电池, 6三元材料电池, 7聚合物锂离子电池, 8钛酸锂电池")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BatteryType batteryType;


    @Schema(description = "整车电池容量. 单位: Ah")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal batteryCapacity;

    @Schema(description = "整车电池标称总能量. 单位: kWh")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal batteryPower;

    @Schema(description = "整车额定总电压. 单位: V")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal batteryVoltage;

    @Schema(description = "电池厂商名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String batteryVendor;

    @Schema(description = "电池组序号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long batterySeqNo;

    @Schema(description = "电池生产日期")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date batteryProduceDate;

    @Schema(description = "电池使用次数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long batteryUsageCount;

    @Schema(description = "电池组产权标识. 0租赁, 1车自有, 255不支持")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer batteryOwner;

    @Schema(description = "电池单体最高允许充电电压. 单位: V")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal batteryUnitMaxVoltage;

    @Schema(description = "绝缘检测结果 0正常 1故障 2告警")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer insulationResult;

    @Schema(description = "DC+绝缘检测值. 单位1Ω/V")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer insulationPositive;

    @Schema(description = "DC-绝缘检测值. 单位1Ω/V")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer insulationNegative;

    @Schema(description = "绝缘检测电压. 单位V")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal insulationVoltage;

    @Schema(description = "电池单体最低电压.单位:V", example = "11.22")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal minBatteryVoltage;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

}
