package com.cdz360.biz.model.trading.site.po;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;


@Data

@Accessors(chain = true)

@Schema(description = "停充超时收费用户列表")

public class OvertimeParkFeeOrderPo {


    @Schema(description = "充电订单号")

    @NotNull(message = "orderNo 不能为 null")

    @Size(max = 64, message = "orderNo 长度不能超过 64")

    private String orderNo;

    @Schema(description = "互联互通订单号")

    @NotNull(message = "hlhtOrderNo 不能为 null")

    @Size(max = 64, message = "hlhtOrderNo 长度不能超过 64")

    private String hlhtOrderNo;


    @Schema(description = "t_user.id")

    @NotNull(message = "uid 不能为 null")

    private Long uid;


    @Schema(description = "站点编号")

    @NotNull(message = "siteId 不能为 null")

    @Size(max = 32, message = "siteId 长度不能超过 32")

    private String siteId;


    @Schema(description = "限免时长, 单位: 分钟")

    @NotNull(message = "freeTime 不能为 null")

    private Integer freeTime;


    @Schema(description = "平台收到充电完成的时间")

    @NotNull(message = "stopTime 不能为 null")
    private Date stopTime;


    @Schema(description = "超时收费单价, 单位: 元/分钟")

    @NotNull(message = "parkingPrice 不能为 null")

    private BigDecimal parkingPrice;


    @Schema(description = "超时费, 单位: 元")

    @NotNull(message = "parkingFee 不能为 null")

    private BigDecimal parkingFee;


    @Schema(description = "计费开始时长")

    @NotNull(message = "calFromTime 不能为 null")

    private Date calFromTime;


    @Schema(description = "计费结束时长")

    @NotNull(message = "calToTime 不能为 null")

    private Date calToTime;


    @Schema(description = "订单状态:0未支付，100已支付")

    @NotNull(message = "status 不能为 null")

    private Integer status;


    @Schema(description = "支付时间")
    private Date payTime;

    @Schema(description = "取消状态：100已取消")
    @NotNull(message = "cancelStatus 不能为 null")
    private Integer cancelStatus;


    @Schema(description = "创建时间")

    @NotNull(message = "createTime 不能为 null")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "更新时间")
    private Date updateTime;


}

