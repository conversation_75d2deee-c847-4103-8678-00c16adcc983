package com.cdz360.biz.model.trading.ess.po;

import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "储能ESS挂载设备信息")
public class EssEquipPo {

    @Schema(description = "主键id")
    @NotNull(message = "id 不能为 null")
    private Long id;

    @Schema(title = "设备的唯一编号")
    private String dno;

    @Schema(description = "储能ESS唯一编号")
    @NotNull(message = "essDno 不能为 null")
    @Size(max = 16, message = "essDno 长度不能超过 16")
    private String essDno;

    @Schema(description = "储能ESS设备名称")
    private String name;

    @Schema(title = "状态", description = "设备状态.0未知;1在线;2离线;3启动中;4告警;5故障;99,下线")
    private EquipStatus status;

    @Schema(description = "设备ID(ess内唯一)")
    @NotNull(message = "equipId 不能为 null")
    private Long equipId;

    @Schema(description = "设备类型ID")
    private Integer equipTypeId;

    @Schema(description = "设备类型")
    @NotNull(message = "equipType 不能为 null")
    private EssEquipType equipType;

    @Schema(description = "设备名称(中文)")
    @NotNull(message = "equipNameCn 不能为 null")
    @Size(max = 64, message = "equipNameCn 长度不能超过 64")
    private String equipNameCn;

    @Schema(description = "设备名称(英文)")
    @NotNull(message = "equipNameEn 不能为 null")
    @Size(max = 64, message = "equipNameEn 长度不能超过 64")
    private String equipNameEn;

    @Schema(description = "是否有效")
    @NotNull(message = "enable 不能为 null")
    private Boolean enable;

}

