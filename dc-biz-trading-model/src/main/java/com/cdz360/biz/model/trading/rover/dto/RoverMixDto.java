package com.cdz360.biz.model.trading.rover.dto;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.trading.rover.po.SiteRoverAssetPo;
import com.cdz360.biz.model.trading.rover.po.SiteRoverPo;
import com.cdz360.biz.model.trading.rover.vo.SiteRoverVo;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * RoverMixDto
 *
 * @since 7/27/2022 1:56 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RoverMixDto {
    private SiteRoverVo siteRoverVo;
    private List<SiteRoverAssetPo> assetPos;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}