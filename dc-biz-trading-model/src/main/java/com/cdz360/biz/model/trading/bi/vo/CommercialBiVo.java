package com.cdz360.biz.model.trading.bi.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = false)
@Data
@Accessors(chain = true)
public class CommercialBiVo implements Serializable {

    @Schema(description = "商户Id", hidden = true)
    private Long commId;

    @ExcelField(title = "商户级别", sort = 2, groups = {32})
    @Schema(description = "商户级别")
    private Integer commLevel;

    @ExcelField(title = "商户全称", sort = 1, groups = {32})
    @Schema(description = "商户全称")
    private String commName;

    // ==========================================================================
    @ExcelField(title = "订单数", sort = 50, groups = {32}, align = 2, digits = 0)
//sort从50开始，是为了此类中字段处在Excel后半部分
    @Schema(description = "订单数量")
    private BigDecimal orderCount;

    @ExcelField(title = "累计充电时长(小时)", sort = 51, groups = {32}, align = 2)
    @Schema(description = "充电时长，单位‘小时’")
    private BigDecimal duration;

    @ExcelField(title = "累计电量（度）", sort = 52, groups = {32}, align = 2, digits = 4)
    @Schema(description = "总电量，单位kwh")
    private BigDecimal orderElectricity;

    @ExcelField(title = "累计电费（元）", sort = 53, groups = {32}, align = 2)
    @Schema(description = "总电费，单位‘元’")
    private BigDecimal elecFee;

    @ExcelField(title = "累计订单电损（元）", sort = 54, groups = {32}, align = 2)
    @Schema(description = "总电损，单位‘元’")
    private BigDecimal discount;

    @ExcelField(title = "累计服务费（元）", sort = 55, groups = {32}, align = 2)
    @Schema(description = "总服务费，单位‘元’")
    private BigDecimal servFee;

    @ExcelField(title = "累计订单总金额（元）", sort = 56, groups = {32}, align = 2)
    @Schema(description = "总价，单位‘元’")
    private BigDecimal orderFee;

    /**
     * t_charger_order.principal_amount
     */
    @ExcelField(title = "累计现金消费（元）", sort = 57, groups = {32}, align = 2)
    @Schema(description = "总现金消费，单位‘元’")
    private BigDecimal principalFee;

    /**
     * t_charger_order.free_gold_amount
     */
    @ExcelField(title = "累计优惠（元）", sort = 57, groups = {32}, align = 2)
    @Schema(description = "总优惠，单位‘元’")
    private BigDecimal freeGoldFee;

    @ExcelField(title = "尖总时长（小时）", sort = 58, groups = {32}, align = 2)
    @Schema(description = "尖总时长，单位‘小时’")
    private BigDecimal t1Duration;

    @ExcelField(title = "尖总电量（度）", sort = 59, groups = {32}, align = 2, digits = 4)
    @Schema(description = "尖总电量，单位‘kwh'")
    private BigDecimal t1Elec;

    @ExcelField(title = "尖总电费（元）", sort = 60, groups = {32}, align = 2)
    @Schema(description = "尖总电费")
    private BigDecimal t1ElecFee;

    @ExcelField(title = "尖总服务费（元）", sort = 61, groups = {32}, align = 2)
    @Schema(description = "尖总服务费")
    private BigDecimal t1ServFee;

    @ExcelField(title = "尖总金额（元）", sort = 62, groups = {32}, align = 2)
    @Schema(description = "尖总金额")
    private BigDecimal t1OrderFee;

    @ExcelField(title = "峰总时长（小时）", sort = 63, groups = {32}, align = 2)
    @Schema(description = "峰总时长，单位‘小时’")
    private BigDecimal t2Duration;

    @ExcelField(title = "峰总电量（度）", sort = 64, groups = {32}, align = 2, digits = 4)
    @Schema(description = "峰总电费")
    private BigDecimal t2Elec;

    @ExcelField(title = "峰总电费（元）", sort = 65, groups = {32}, align = 2)
    @Schema(description = "峰总电费")
    private BigDecimal t2ElecFee;

    @ExcelField(title = "峰总服务费（元）", sort = 66, groups = {32}, align = 2)
    @Schema(description = "峰总服务费")
    private BigDecimal t2ServFee;

    @ExcelField(title = "峰总金额（元）", sort = 67, groups = {32}, align = 2)
    @Schema(description = "峰总金额")
    private BigDecimal t2OrderFee;

    @ExcelField(title = "平总时长（小时）", sort = 68, groups = {32}, align = 2)
    @Schema(description = "平总时长，单位‘小时’")
    private BigDecimal t3Duration;

    @ExcelField(title = "平总电量（度）", sort = 69, groups = {32}, align = 2, digits = 4)
    @Schema(description = "平总电费")
    private BigDecimal t3Elec;

    @ExcelField(title = "平总电费（元）", sort = 70, groups = {32}, align = 2)
    @Schema(description = "平总电费")
    private BigDecimal t3ElecFee;

    @ExcelField(title = "平总服务费（元）", sort = 71, groups = {32}, align = 2)
    @Schema(description = "平总服务费")
    private BigDecimal t3ServFee;

    @ExcelField(title = "平总金额（元）", sort = 72, groups = {32}, align = 2)
    @Schema(description = "平总金额")
    private BigDecimal t3OrderFee;

    @ExcelField(title = "谷总时长（小时）", sort = 73, groups = {32}, align = 2)
    @Schema(description = "谷总时长，单位‘小时’")
    private BigDecimal t4Duration;

    @ExcelField(title = "谷总电量（度）", sort = 74, groups = {32}, align = 2, digits = 4)
    @Schema(description = "谷总电费")
    private BigDecimal t4Elec;

    @ExcelField(title = "谷总电费（元）", sort = 75, groups = {32}, align = 2)
    @Schema(description = "谷总电费")
    private BigDecimal t4ElecFee;

    @ExcelField(title = "谷总服务费（元）", sort = 76, groups = {32}, align = 2)
    @Schema(description = "谷总服务费")
    private BigDecimal t4ServFee;

    @ExcelField(title = "谷总金额（元）", sort = 77, groups = {32}, align = 2)
    @Schema(description = "谷总金额")
    private BigDecimal t4OrderFee;

    public void initTimeDivision() {
        this.t1Duration = BigDecimal.ZERO;
        this.t1Elec = BigDecimal.ZERO;
        this.t1ElecFee = BigDecimal.ZERO;
        this.t1ServFee = BigDecimal.ZERO;
        this.t1OrderFee = BigDecimal.ZERO;

        this.t2Duration = BigDecimal.ZERO;
        this.t2Elec = BigDecimal.ZERO;
        this.t2ElecFee = BigDecimal.ZERO;
        this.t2ServFee = BigDecimal.ZERO;
        this.t2OrderFee = BigDecimal.ZERO;

        this.t3Duration = BigDecimal.ZERO;
        this.t3Elec = BigDecimal.ZERO;
        this.t3ElecFee = BigDecimal.ZERO;
        this.t3ServFee = BigDecimal.ZERO;
        this.t3OrderFee = BigDecimal.ZERO;

        this.t4Duration = BigDecimal.ZERO;
        this.t4Elec = BigDecimal.ZERO;
        this.t4ElecFee = BigDecimal.ZERO;
        this.t4ServFee = BigDecimal.ZERO;
        this.t4OrderFee = BigDecimal.ZERO;
    }
}
