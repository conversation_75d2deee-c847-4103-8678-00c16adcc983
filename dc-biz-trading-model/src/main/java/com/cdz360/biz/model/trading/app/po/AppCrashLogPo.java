package com.cdz360.biz.model.trading.app.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.lang.Double;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@Accessors(chain = true)
@ApiModel(value = "app闪退日志")
public class AppCrashLogPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@ApiModelProperty(value = "应用版本")
	@Size(max = 255, message = "appVer 长度不能超过 255")
	private String appVer;

	@ApiModelProperty(value = "设备类型")
	@Size(max = 255, message = "deviceModel 长度不能超过 255")
	private String deviceModel;

	@ApiModelProperty(value = "操作系统版本")
	@Size(max = 255, message = "systemVersion 长度不能超过 255")
	private String systemVersion;

	@ApiModelProperty(value = "发时时本地时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date time;

	@ApiModelProperty(value = "异常名称 NSRangeException")
	@Size(max = 255, message = "name 长度不能超过 255")
	private String name;

	@ApiModelProperty(value = "原因 index 5 beyond bounds")
	@Size(max = 255, message = "reason 长度不能超过 255")
	private String reason;

	@ApiModelProperty(value = "调用栈详情")
	private String stackDetail;

	@ApiModelProperty(value = "用户id")
	private Long uid;

	@ApiModelProperty(value = "app id")
	private String bundleId;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date createTime;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date updateTime;


}
