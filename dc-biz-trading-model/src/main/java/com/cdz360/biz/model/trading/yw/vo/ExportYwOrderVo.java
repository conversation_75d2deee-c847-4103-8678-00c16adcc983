package com.cdz360.biz.model.trading.yw.vo;

import com.cdz360.base.model.base.type.UserType;
import com.cdz360.biz.model.FileItem;
import com.cdz360.biz.model.annotations.ExcelField;
import com.cdz360.biz.model.trading.yw.dto.CarInfo;
import com.cdz360.biz.model.trading.yw.dto.GoodsInfo;
import com.cdz360.biz.model.trading.yw.type.CreateYwOrderSourceType;
import com.cdz360.biz.model.trading.yw.type.YwOrderLevel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "运维工单导出")
@Data
@Accessors(chain = true)
public class ExportYwOrderVo implements Serializable {

    @ExcelField(title = "运维工单号", sort = 1)
    @Schema(description = "运维工单编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String ywOrderNo;

    @Schema(description = "场站编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "场站性质: 0，未知； 1，投建运营； 2，以租代售； 3，纯租赁； 4，EPC+O; 5, 销售的代收代付； 6，代运营； 7，委托运营")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer siteGcType;

    @ExcelField(title = "场站名称", sort = 6)
    @Schema(description = "场站名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;

    @Schema(description = "场站地址(不含省市区信息) t_site: address")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteAddress;

    @Schema(description = "场站经度")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal siteLng;

    @Schema(description = "场站纬度")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal siteLat;

    @Schema(description = "场站所属商户")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long siteCommId;

    @ExcelField(title = "所属商户", sort = 3)
    @Schema(description = "场站所属商户名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteCommName;

    @Schema(description = "是否过质保期: true(是); false(否)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean overExpireDate;

    @ExcelField(title = "状态", sort = 24)
    @Schema(description = "工单状态(INIT: 待接收; RECEIVED: " +
        "已接收; PROCESSING: 处理中; TRANSFERRING: 转派中;" +
        " WAIT_CHECK : 待质检; NO_PASS: 不合格; SOLVED: 已完成;)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String orderStatus;

    @ExcelField(title = "故障桩", sort = 8)
    @Schema(description = "故障的充电桩编号(可多个)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> evseNoList;

    @Schema(description = "桩显示名称列表 业务逻辑上需要赋值")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> evseNameList;

    @Schema(description = "关联充电订单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderNo;

    @Schema(description = "故障图片列表 故障报修")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> faultImages;

    @ExcelField(title = "故障描述", sort = 10)
    @Schema(description = "故障描述")
    @Size(max = 400, message = "faultDesc 长度不能超过 400")
    private String faultDesc;

    @Schema(description = "故障等级: 10(一般); 20(紧急); 30(重大)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private YwOrderLevel faultLevel;

    @Schema(description = "涉及车辆信息: " +
        "例: [{\"brand\": \"车辆品牌\", \"model\": \"车辆类型\", " +
        "\"needVoltage\": \"需求电压\", \"needCurrent\": \"需求电流\", " +
        "\"otherEvse\": true}]")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private CarInfo carInfo;

    @ExcelField(title = "是否远程解决", sort = 22)
    @Schema(description = "是否远程解决")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String remote;

    @Schema(description = "配件(运维物品): 例: [{\"name\": \"物品名称\", \"num\": 1}]")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private GoodsInfo goods;

    @Schema(description = "可能原因")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String faultReason;

    @Schema(description = "检查步骤")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String checkStep;

    @Schema(description = "处理措施及结果")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dealProcess;

    @Schema(description = "维修图片")
    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private List<String> images;
    private List<FileItem> images;

    @Schema(description = "创建工单的用户是否已经接收处理结果反馈 true -- 用户已查阅;false -- 用户还没接收查阅")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean recNotice;

    @ExcelField(title = "工单来源", sort = 12,
        convert = "com.cdz360.biz.model.trading.yw.type.CreateYwOrderSourceType")
    @Schema(description = "创建运维工单客户端. UNKNOWN,未知; C端客户(CUSTOMER_SRC);" +
        " 商户(COMM_SRC); 运维(YW_SRC); 客服(KF_SRC); 其他(OTHER_SRC)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private CreateYwOrderSourceType sourceType;

    @Schema(description = "运维人ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long maintUid;

    @ExcelField(title = "运维人", sort = 18)
    @Schema(description = "运维人名字")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String maintName;

    @Schema(description = "运维人手机号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String maintPhone;

    @ExcelField(title = "运维时间", sort = 20, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "运维人处理时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date maintTime;

    @ExcelField(title = "运维时长", sort = 21)
    @Schema(description = "运维时长（天-时-分）")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String ywDurationStr;

    @Schema(description = "质检人ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long qcUid;

    @ExcelField(title = "质检人", sort = 26)
    @Schema(description = "质检人名字")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String qcName;

    @Schema(description = "质检人处理时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date qcTime;

    @ExcelField(title = "质检人备注", sort = 28)
    @Schema(description = "质检人备注")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String qcRemark;

    @Schema(description = "创建操作人类型. 0,未知; 1,商户/平台用户; 3,企业客户; 4,系统")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private UserType createOpType;

    @Schema(description = "创建操作人ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long createOpUid;

    @ExcelField(title = "创建人", sort = 14)
    @Schema(description = "创建操作人名字")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String createOpName;

    @Schema(description = "联系方式 创建工单用户联系方式")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String createUserPhone;

    @ExcelField(title = "创建时间", sort = 16, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
