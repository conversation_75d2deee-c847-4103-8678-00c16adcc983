package com.cdz360.biz.model.trading.iot.vo;

import com.cdz360.biz.model.trading.ess.type.CfgType;
import com.cdz360.biz.model.trading.pv.po.GtiGridDispatchCfgPo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;


@Data

@Accessors(chain = true)

@Schema(description = "逆变器模板详情")

public class DevGtiCfgVo {

    @Schema(description = "主键Id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long id;

    @Schema(description = "场站ID")
    @JsonInclude(Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "模板名称")
    @JsonInclude(Include.NON_EMPTY)
    private String name;

    @Schema(description = "继承code")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String code;

    @Schema(description = "配置模板版本号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long ver;

    @Schema(description = "类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private CfgType type;

    @Schema(description = "创建者所属商户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long commId;

    @Schema(description = "采样时间间隔，单位: 秒")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer samplingTime;

    @Schema(description = "重连时间,单位: 秒")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer timeout;

    @Schema(description = "并网模式. 0,未知;1,离网;2,并网")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer gridMode;

    @Schema(description = "起机电压, 单位V")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal bootVoltage;

    @Schema(description = "市电电压下限, 单位V")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal minVoltage;

    @Schema(description = "市电电压上限, 单位V")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal maxVoltage;

    @Schema(description = "市电频率下限, 单位Hz")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal minFrequency;

    @Schema(description = "市电频率上限, 单位Hz")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal maxFrequency;

    @Schema(description = "光伏逆变器电网调度配置")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private GtiGridDispatchCfgPo gridDispatchCfgPo;

    @Schema(description = "操作人ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long opUid;

    @Schema(description = "操作人姓名")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String opName;

    @Schema(description = "是否有效")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean enable;

}

