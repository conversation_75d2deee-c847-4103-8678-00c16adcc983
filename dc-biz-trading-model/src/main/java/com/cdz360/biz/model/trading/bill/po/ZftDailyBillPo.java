package com.cdz360.biz.model.trading.bill.po;


import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.biz.model.trading.bill.type.DailyBillStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;



@Data

@Accessors(chain = true)

@Schema(description = "直付商家对账单")

public class ZftDailyBillPo {



	@Schema(description = "发票记录ID")

	@NotNull(message = "id 不能为 null")

	private Long id;



	@Schema(description = "账单文件名称")

	@NotNull(message = "name 不能为 null")

	@Size(max = 200, message = "name 长度不能超过 200")

	private String name;



	@Schema(description = "账单状态: LOADING(下载中), LOAD_FAIL(下载失败), LOAD_SUCCESS(下载成功), CHECKING(对账中), CHECK_FAIL(对账失败), CHECK_SUCCESS(完成对账)")

	@NotNull(message = "status 不能为 null")

	private DailyBillStatus status;



	@Schema(description = "直付商家ID(t_zft.id)")

	private Long zftId;



	@Schema(description = "直付商家名称")

	@NotNull(message = "zftName 不能为 null")

	@Size(max = 200, message = "zftName 长度不能超过 200")

	private String zftName;



	@Schema(description = "直付商家所属商户ID")

	private Long zftCommId;


	@Schema(description = "二级商户ID 微信或支付宝二级商户ID")
	@NotNull(message = "mchId不能为空")
	private String mchId;


	@Schema(description = "账期")

	@NotNull(message = "billDate 不能为 null")

	private Date billDate;



	@Schema(description = "渠道: 1(支付宝); 2(微信)")

	@NotNull(message = "channel 不能为 null")

	private PayChannel channel;



	@Schema(description = "账单下载地址")

	@Size(max = 200, message = "downloadUrl 长度不能超过 200")

	private String downloadUrl;



	@Schema(description = "创建时间")

	private Date createTime;



	@Schema(description = "更新时间")

	private Date updateTime;





}

