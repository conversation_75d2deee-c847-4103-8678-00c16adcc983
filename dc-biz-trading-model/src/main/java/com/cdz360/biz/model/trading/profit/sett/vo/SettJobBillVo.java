package com.cdz360.biz.model.trading.profit.sett.vo;

import com.cdz360.biz.model.trading.profit.sett.type.ProfitCfgCalSource;
import com.cdz360.biz.model.trading.profit.sett.type.ProfitCfgCategory;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "结算单查看")
public class SettJobBillVo {

    @ApiModelProperty(value = "结算单号")
    @NotNull(message = "billNo 不能为 null")
    @Size(max = 32, message = "billNo 长度不能超过 32")
    private String billNo;

    @ApiModelProperty(value = "结算任务名称(t_gc_profit_cfg.name)")
    @Size(max = 64, message = "jobName 长度不能超过 64")
    private String jobName;

    @Schema(description = "收入(INCOME)/支出(EXPENSE)")
    @NotNull(message = "category 不能为 null")
    private ProfitCfgCategory jobCategory;

    @Schema(description = "计算来源: FROM_CHARGE_ORDER(来源充电订单); FROM_BI_SYS(来源决策系统)")
    @NotNull(message = "calSource 不能为 null")
    private ProfitCfgCalSource jobCalSource;

    @ApiModelProperty(value = "场站编号")
    @Size(max = 32, message = "siteId 长度不能超过 32")
    private String siteId;

    @ApiModelProperty(value = "站点名称")
    @Size(max = 128, message = "siteName 长度不能超过 128")
    private String siteName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "结算周期(开始时间)")
    private Date settPeriodFrom;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "结算周期(结束时间)")
    private Date settPeriodTo;

    @Schema(description = "电量(单位: kW·h)")
    private BigDecimal elec;

    @ApiModelProperty(value = "电费(单位: 元)")
    private BigDecimal elecFee;

    @ApiModelProperty(value = "服务费(单位: 元)")
    private BigDecimal servFee;

    @ApiModelProperty(value = "停充超时费(单位: 元)")
    private BigDecimal parkFee;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    @NotNull(message = "createTime 不能为 null")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间")
    @NotNull(message = "updateTime 不能为 null")
    private Date updateTime;
}
