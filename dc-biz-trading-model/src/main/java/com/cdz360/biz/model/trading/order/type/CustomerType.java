package com.cdz360.biz.model.trading.order.type;

import lombok.Getter;

/**
 * 客户类型
 *
 * <AUTHOR>
 * @since 2019/11/12 15:55
 */
@Getter
public enum CustomerType {

    UNKNOWN(0, "未知"),

    CUS_GENERAL(1, "普通客户"),

    CUS_ENT(2, "企业客户");

    private final int code;
    private final String desc;

    CustomerType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CustomerType valueOf(int code) {
        for (CustomerType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return CustomerType.UNKNOWN;
    }

}
