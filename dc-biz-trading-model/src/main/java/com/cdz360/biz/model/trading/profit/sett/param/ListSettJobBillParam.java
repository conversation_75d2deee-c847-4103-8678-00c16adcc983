package com.cdz360.biz.model.trading.profit.sett.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.biz.model.trading.profit.sett.type.ProfitCfgCategory;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "获取结算列表参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListSettJobBillParam extends BaseListParam {

    @Schema(description = "结算单状态:(true-进行中;false-已删除)")
    private Boolean enable;

    @Schema(description = "结算任务ID(t_gc_profit_cfg.id)")
    private Long jobId;

    @Schema(description = "任务名称(支持模糊查询)")
    private String jobNameLike;

    @Schema(description = "场站ID(精确)")
    private String siteId;

    @Schema(description = "场站ID列表(精确)")
    private List<String> siteIdList;

    @Schema(description = "收入(INCOME)/支出(EXPENSE)")
    private ProfitCfgCategory jobCategory;

    @Schema(description = "结算周期(开始时间-结束时间)")
    private TimeFilter settPeriod;

    @Schema(description = "结算单编号(支持模糊查询)")
    @JsonInclude(Include.NON_EMPTY)
    private String billNoLike;

    @Schema(description = "结算单编号(精确)")
    @JsonInclude(Include.NON_EMPTY)
    private String billNo;

    @Schema(description = "结算单编号列表")
    private List<String> billNoList;

    @Schema(description = "查询结果不包含的结算单编号列表")
    private List<String> excludeBillNoList;

    @Schema(description = "结算周期开始时间,精确匹配")
    private Date settPeriodFrom;

    @Schema(description = "结算周期结束时间,精确匹配")
    private Date settPeriodTo;
}
