package com.cdz360.biz.model.trading.site.po;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "流程默认值配置项")
@Data
@Accessors(chain = true)
public class SiteOaDefaultConfigItem {

    @Schema(description = "配置项label值")
    @JsonInclude(Include.NON_EMPTY)
    private String label;

    @Schema(description = "配置项默认值")
    @JsonInclude(Include.NON_EMPTY)
    private String value;

    @Schema(description = "配置项表单项类型")
    @JsonInclude(Include.NON_EMPTY)
    private String type;
}
