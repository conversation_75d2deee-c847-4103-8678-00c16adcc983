package com.cdz360.biz.model.trading.site.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * SiteBaseInfoVo
 *  场站基础信息
 * @since 5/11/2023 5:20 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "场站基础信息")
public class SiteBaseInfoVo {

    //@JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;

    //@JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteNo;

    //@JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private Date settlementDate;

    //@JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    //@JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String zone;

    //@JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String province;

    //@JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String city;

    //@JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal elecTotal;//全口径电量

//    private BigDecimal elecGap;//电量差
}