package com.cdz360.biz.model.trading.site.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * SiteReportParam
 *
 * @since 5/11/2023 5:39 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "场站报告请求入参")
public class SiteReportParam {
    private String siteId;

    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date date;
}