package com.cdz360.biz.model.trading.coupon.dto;

import com.cdz360.biz.model.trading.coupon.po.CouponPo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CouponDto extends CouponPo {

    @Schema(description = "商户全称")
    private String commName;

    @Schema(description = "是否可与积分体系叠加使用")
    private Boolean extraScoreSettingEnable;

    @Schema(description = "可用场站ID集合")
    private List<String> usableSiteIdList;
}
