package com.cdz360.biz.model.trading.rover.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ExportRoverRecordVo
 *
 * @since 8/2/2022 11:02 AM
 * <AUTHOR>
 */
@Schema(description = "运营巡检列表导出")
@Data
@Accessors(chain = true)
public class ExportRoverRecordVo implements Serializable {

    @ExcelField(title = "巡检单号", sort = 1)
    @Schema(description = "巡检单号")
    private String no;

    @ExcelField(title = "所属商户", sort = 2)
    @Schema(description = "所属商户")
    private String commName;

    @ExcelField(title = "站点名称", sort = 3)
    @Schema(description = "所属商户")
    private String siteName;

    @ExcelField(title = "巡检人", sort = 4)
    @Schema(description = "所属商户")
    private String roverName;

    @ExcelField(title = "创建时间", sort = 5, dateFormat = "yyyy-MM-dd HH:mm")
    @Schema(description = "创建时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

    @ExcelField(title = "巡检时间", sort = 6, dateFormat = "yyyy-MM-dd HH:mm")
    @Schema(description = "巡检时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date roverTime;

    @ExcelField(title = "备注", sort = 7)
    @Schema(description = "备注")
    private String comment;

    @ExcelField(title = "状态", sort = 7)
    @Schema(description = "状态")
    private String status;

    @ExcelField(title = "评分人", sort = 7)
    @Schema(description = "评分人")
    private String raterName;

    @ExcelField(title = "分值", sort = 7)
    @Schema(description = "分值")
    private Integer rank;

    @ExcelField(title = "评分人备注", sort = 7)
    @Schema(description = "评分人备注")
    private String rankComment;


}