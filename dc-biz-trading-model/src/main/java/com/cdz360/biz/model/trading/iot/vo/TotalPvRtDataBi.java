package com.cdz360.biz.model.trading.iot.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class TotalPvRtDataBi {
    @Schema(description = "当天统计数据")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private SitePvRtDataBi today;

    @Schema(description = "当月统计数据")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private SitePvRtDataBi curMonth;

    @Schema(description = "当年统计数据")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private SitePvRtDataBi curYear;

    @Schema(description = "总数居")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private SitePvRtDataBi total;
}
