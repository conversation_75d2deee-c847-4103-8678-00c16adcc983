package com.cdz360.biz.model.trading.iot.type;

import lombok.Getter;

@Getter
public enum BatteryModel {

    M48112_S(1),
    M38210_S(2),
    UNKNOWN(99),
    ;

    private final int code;

    BatteryModel(int code) {
        this.code = code;
    }


    public static BatteryModel codeOf(Object codeIn) {
        if (codeIn == null) {
            return BatteryModel.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof BatteryModel) {
            return (BatteryModel) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (BatteryModel model : values()) {
            if (model.code == code) {
                return model;
            }
        }
        return BatteryModel.UNKNOWN;
    }

}
