package com.cdz360.biz.model.trading.order.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.model.base.type.UserType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.finance.type.ExpressStatus;
import com.cdz360.biz.model.finance.type.FlowInAccountType;
import com.cdz360.biz.model.finance.type.TaxStatus;
import com.cdz360.biz.model.finance.type.TaxType;
import com.cdz360.biz.model.trading.bi.dto.BiExportGroups;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.cdz360.biz.model.trading.deposit.vo.DepositSourceType;
import com.cdz360.biz.model.trading.order.type.PayBillStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 充值记录查询参数
 *
 * <AUTHOR>
 * @since 2019/11/5 16:37
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Schema(description = "充值记录查询")
public class PayBillParam extends BaseListParam {

    @Schema(description = "客户类型:UNKNOWN(0)-未知,SYS_USER(1)-商户/平台用户," +
        "CUSTOMER(2)-C端客户,CORP_USER(3)-企业用户")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<UserType> userTypeList;

    /**
     * 充值来源
     */
    @Schema(description = "充值来源: UNKNOWN-其他,WX_LITE-微信小程序,ALIPAY_LITE-支付宝小程序," +
        "ANDROID_APP-安卓APP,IOS_APP-iOS APP,MGM_WEB-平台充值,MERCHANT-企业充值,HLHT-三方平台")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<DepositSourceType> sourceTypeList;

    /**
     * 充值类型
     */
    @Schema(description = "充值类型: UNKNOWN-未知,IN_FLOW-充值,OUT_FLOW-减少")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<DepositFlowType> flowTypeList;

    @Schema(description = "充值账户: UNKNOWN-未知,PERSONAL-个人现金账户," +
        "CREDIT-集团授信账户,COMMERCIAL-商户商户会员,PREPAY-即充即退 精确匹配")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private PayAccountType accountType;

    @Schema(description = "充值账户的企业名称 支持模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String accCorpName;

    @Schema(description = "充值账户的商户名称 支持模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String accCommName;

    /**
     * 充值账户
     */
    @Schema(description = "充值账户: UNKNOWN-未知,PERSONAL-个人现金账户," +
        "CREDIT-集团授信账户,COMMERCIAL-商户商户会员,PREPAY-即充即退")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<PayAccountType> accountTypeList;

    /**
     * 充值账户
     */
    @Schema(description = "充值账户(用户查询使用)")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<PayAccount> payAccountList;

    /**
     * 支付方式
     */
    @Schema(description = "支付方式: UNKNOWN-未知,ALIPAY-支付宝,WXPAY-微信支付," +
        "BANK_CARD-银行卡支付,BUSINESS_ACCOUNT-对公转账")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<PayChannel> payChannelList;

    /**
     * 到账账户
     */
    @Schema(description = "到账账户: UNKNOWN-其他账户,ALIPAY-支付宝账户," +
        "TENPAY-微信账户,BANK_CARD-银行卡账户,BUSINESS_ACCOUNT-对公账户")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<FlowInAccountType> flowInAccountTypeList;

    /**
     * 开票类型
     */
    @Schema(description = "开票类型: UNKNOWN-未知,NONE-未开票,NORMAL_TAX-普通发票," +
        "PREPAY_TAX-预充值发票,VALUE_ADDED_TAX-增值税普通发票,SPECIAL_VAT-增值税专用发票")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TaxType> taxTypeList;
    /**
     * 开票类型
     */
    @Schema(description = "开票状态")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<TaxStatus> taxStatus;
    /**
     * 寄送状态
     */
    @Schema(description = "寄送状态: UNKNOWN-未知,INIT-未寄送,SENT-已寄送")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<ExpressStatus> expressStatusList;

    /**
     * 充值订单号
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "充值订单号(支持模糊查询)")
    private String orderId;

    /**
     * 微信支付宝订单号
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "微信支付宝订单号(支持模糊查询)")
    private String tradeNo;

    @Schema(description = "充值订单号列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> orderIdList;

    @Schema(description = "不包含充值订单号列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> exclusiveOrderIdList;

    /**
     * 搜索开始时间
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "搜索开始时间", example = "2019-11-11 15:21:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date fromTime;

    /**
     * 搜索结束时间
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "搜索结束时间", example = "2019-11-11 15:21:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date toTime;

    /**
     * 分页索引
     */
    @Schema(description = "分页索引", example = "1")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer index;

//    @Schema(description = "其实位置", example = "100")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Integer start;

    /**
     * 分页大小
     */
//    @Schema(description = "分页大小", example = "10")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Integer size;

    /**
     * 模糊查询关键字
     */
    @Schema(description = "模糊查询关键字")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String keyword;

    /**
     * 直付商家名称
     */
    @Schema(description = "模糊查询关键字")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String zftName;

    /**
     * 商户及子商户列表
     */
    @Schema(description = "商户及子商户列表")
    @Deprecated
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<Long> commIdList;

    @Schema(description = "商户ID链")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commIdChain;

    /**
     * 用户Id
     */
    @Schema(description = "用户Id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long userId;

    /**
     * 用户类型
     */
    @Schema(description = "用户类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String userType;

    /**
     * 客户名称
     */
    @Schema(description = "客户名称(支持模糊查询)")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cusName;

    /**
     * 手机号
     */
    @Schema(description = "手机号(支持模糊查询)")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cusPhone;

    /**
     * @link com.cdz360.model.trading.order.type.PayBillStatus
     */
    @Schema(description = "充值记录状态值: UNPAID(0)-未支付,PAID(1)-已支付," +
        "PAY_FAIL(2)-支付失败,SEND_PAY_SUCCESS(3)-发起支付成功," +
        "SEND_PAY_FAIL(4)-发起支付失败,TIME_OUT(5)-超时," +
        "EXHAUST(6)-余额已耗尽,UNKNOWN(99)-未知", example = "6 这个仅用于查询可减少的充值记录")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private PayBillStatus status;

    @Schema(description = "充值状态", format = "java.lang.Integer")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<PayBillStatus> statusList;

    /**
     * 订单导出分组
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BiExportGroups group;

    @Schema(description = "是否显示支付失败的记录 不传默认true")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean showFail;

    @Schema(description = "是否在企业客户申请开票页面 不做前端入参", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean inCorpInvoice;

    @Schema(description = "企业客户申请开票记录的申请单号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String applyNo;

    @Schema(description = "活动ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long activityId;

    @Schema(description = "充值时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter payTimeFilter;

    /**
     * 区域语言信息，导出用，后端自行set
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Locale locale;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
