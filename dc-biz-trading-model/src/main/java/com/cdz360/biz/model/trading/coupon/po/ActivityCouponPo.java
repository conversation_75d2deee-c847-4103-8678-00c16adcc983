package com.cdz360.biz.model.trading.coupon.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "活动关联券模板")
public class ActivityCouponPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@Schema(description = "活动id")
	@NotNull(message = "activityId 不能为 null")
	private Long activityId;

	@Schema(description = "券模板id")
	@NotNull(message = "couponDictId 不能为 null")
	private Long couponDictId;

	@NotNull(message = "createTime 不能为 null")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	private Date createTime;


}
