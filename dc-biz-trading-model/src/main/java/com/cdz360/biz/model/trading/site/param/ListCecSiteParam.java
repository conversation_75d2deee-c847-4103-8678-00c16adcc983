package com.cdz360.biz.model.trading.site.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Schema(description = "CEC 互联互通场站列表请求参数")
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class ListCecSiteParam extends BaseListParam {

    private String id;

    @Schema(description = "商户链", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String commIdChain;

    @Schema(description = "站点类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Integer> typeList;

    @Schema(description = "站点状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Integer> statusList;

    @Schema(description = "互联互通运营商名称 支持模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String partnerName;

    @Schema(description = "设备所属运营商名称 支持模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String evseOwnerName;

    @Schema(description = "省")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String provinceCode;

    @Schema(description = "市")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cityCode;

    // sk --> 请输入站点名称/站点编号/地址(模糊查询)

    @Schema(description = "所属商户")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> commIdList;
}
