package com.cdz360.biz.model.trading.hlht.vo;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.invoice.type.InvoicedType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "互联互通方需要的开票信息")
public class InvoiceHlhtCallbackVo {

    @Schema(description = "发票流水号", required = true, example = "XXXX")
    private String invoicAppNo; //发票流水号 String 是

    @Schema(description = "开票状态", required = true, example = "XXXX")
    private String invoiceStatus; //开票状态：01-开票 成功，02-开票失败，03-作废成功，04-作废失败String 是

    @Schema(description = "发票代码", required = true, example = "XXXX")
    private String invoiceCode; //发票代码 String 是

    @Schema(description = "发票号码", required = true, example = "XXXX")
    private String invoiceNum; //发票号码 String 是

    @Schema(description = "开票时间", required = true, example = "yyyy-MM-dd")
    private String invoicedate; //开票时间 String 是

    @Schema(description = "充电费", required = true, example = "XXXX")
    private BigDecimal elecAmt; //充电费 BigDecimal 否

    @Schema(description = "服务费", required = true, example = "XXXX")
    private BigDecimal serverAmt; //服务费 BigDecimal 否

    @Schema(description = "开票员", required = true, example = "XXXX")
    private String invoiceUser; //开票员 String 否

    @Schema(description = "收款人", required = true, example = "XXXX")
    private String payee; //收款人 String 否

    @Schema(description = "复核人", required = true, example = "XXXX")
    private String checker; //复核人 String 否

    @Schema(description = "发票 pdf 地址", required = true, example = "XXXX")
    private String invoicePdfUrl; //发票 pdf 地址，当开票类型为“电子发票“时必传 String 是

    @Schema(description = "发票 jpg 地址", required = true, example = "XXXX")
    private String rinvoiceJpgUrl; //发票 jpg 地址，当开票类型为“电子发票“时必传

    @Schema(description = "平台来源 0:任我充 1:朗新 2:恒大(星络通) 后续可增加")
    private Integer platformSource;

    @Schema(description = "订单号")
    private List<String> orderNoList;

    @Schema(description = "互联互通订单号")
    private List<String> hlhtOrderNoList;

    @Schema(description = "互联互通对方申请的单号")
    private String hlhtOutInvoiceId;

    @Schema(description = "申请开票的形式: 电子(ELECTRONIC); 纸质(PAPER)")
    private InvoicedType invoicedType;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
