package com.cdz360.biz.model.trading.profit.sett.type;

import com.cdz360.base.model.base.type.DcEnum;
import lombok.Getter;

@Getter
public enum ProfitRuleAccountType implements DcEnum {
    UNKNOWN(0, "未知"),
    PERSONAL(1, "个人账户"),
    CREDIT(2, "企业授信账户"),
    COMMERCIAL(3, "会员账户"),
    PREPAY(4, "即充即退"),
    CORP(5, "企业账户"),
    E_CNY(6, "数字人民币"),
    WX_CREDIT(7, "微信信用充"),
    ALIPAY_CREDIT(8, "支付宝信用充"),
    OTHER(999, "其他"),
    ALL_ACCOUNT(1000, "全部");

    private final int code;
    private final String desc;

    private ProfitRuleAccountType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ProfitRuleAccountType valueOf(Object codeIn) {
        if (codeIn == null) {
            return UNKNOWN;
        } else {
            int code = 0;
            if (codeIn instanceof ProfitRuleAccountType) {
                return (ProfitRuleAccountType) codeIn;
            } else {
                if (codeIn instanceof Integer) {
                    code = (Integer) codeIn;
                } else if (codeIn instanceof Long) {
                    code = ((Long) codeIn).intValue();
                } else if (codeIn instanceof String) {
                    String str = (String) codeIn;
                    if ("UNKNOWN".equalsIgnoreCase(str)) {
                        return UNKNOWN;
                    }

                    if ("PERSONAL".equalsIgnoreCase(str)) {
                        return PERSONAL;
                    }

                    if ("CREDIT".equalsIgnoreCase(str)) {
                        return CREDIT;
                    }

                    if ("COMMERCIAL".equalsIgnoreCase(str)) {
                        return COMMERCIAL;
                    }

                    if ("PREPAY".equalsIgnoreCase(str)) {
                        return PREPAY;
                    }

                    if ("CORP".equalsIgnoreCase(str)) {
                        return CORP;
                    }

                    if ("E_CNY".equalsIgnoreCase(str)) {
                        return E_CNY;
                    }

                    if ("WX_CREDIT".equalsIgnoreCase(str)) {
                        return WX_CREDIT;
                    }

                    if ("ALIPAY_CREDIT".equalsIgnoreCase(str)) {
                        return ALIPAY_CREDIT;
                    }

                    if ("OTHER".equalsIgnoreCase(str)) {
                        return OTHER;
                    }

                    code = Integer.parseInt((String) codeIn);
                }

                ProfitRuleAccountType[] var6 = values();
                int var3 = var6.length;

                for (int var4 = 0; var4 < var3; ++var4) {
                    ProfitRuleAccountType status = var6[var4];
                    if (status.code == code) {
                        return status;
                    }
                }

                return UNKNOWN;
            }
        }
    }
}
