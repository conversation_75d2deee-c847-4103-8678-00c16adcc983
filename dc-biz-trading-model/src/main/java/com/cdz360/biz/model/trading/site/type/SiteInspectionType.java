package com.cdz360.biz.model.trading.site.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "运维工单类型")
@Getter
public enum SiteInspectionType implements DcEnum {
    UNKNOWN(0, "未知"),
    PV(1, "光伏巡检"),
    ESS(2, "储能巡检"),
    CHARGE(3, "充电桩巡检")
    ;

    @JsonValue
    private int code;

    private String desc;

    SiteInspectionType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static SiteInspectionType valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (SiteInspectionType ywType : values()) {
            if (ywType.code == code) {
                return ywType;
            }
        }

        return SiteInspectionType.UNKNOWN;
    }
}
