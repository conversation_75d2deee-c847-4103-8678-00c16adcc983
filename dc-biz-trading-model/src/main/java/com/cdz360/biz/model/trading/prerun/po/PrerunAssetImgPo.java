package com.cdz360.biz.model.trading.prerun.po;

import com.cdz360.biz.model.trading.prerun.type.AssetType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel(value = "调试工单-配套设施信息")
public class PrerunAssetImgPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@Size(max = 32, message = "prerunNo 长度不能超过 32")
	private Long prerunId;

	private Boolean enable;

	@ApiModelProperty(value = "配套设施：消防设施、雨棚、监控、道闸、充电桩箱变")
	private AssetType type;

	@Size(max = 255, message = "url 长度不能超过 255")
	private String url;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;


}
