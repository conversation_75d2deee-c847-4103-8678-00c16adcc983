package com.cdz360.biz.model.trading.coupon.vo;

import com.cdz360.biz.model.trading.coupon.dto.CouponDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PriorityCouponVo {

    @Schema(description = "是否开启自动抵扣")
    private Boolean autoDeduct;

    @Schema(description = "可用优惠券列表")
    private List<CouponDto> list;

    @Schema(description = "券总数")
    private Long total;
}
