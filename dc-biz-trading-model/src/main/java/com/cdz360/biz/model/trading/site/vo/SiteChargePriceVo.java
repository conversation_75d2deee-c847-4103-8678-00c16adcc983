package com.cdz360.biz.model.trading.site.vo;

import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SiteChargePriceVo extends ChargePriceVo {

    @Schema(description = "模板类型，AC DC BOTH")
    private SupplyType templateType;

    @Schema(description = "免费充电标识**0-收费 1-免费**", example = "1")
    private Integer freeChargeFlag;
}
