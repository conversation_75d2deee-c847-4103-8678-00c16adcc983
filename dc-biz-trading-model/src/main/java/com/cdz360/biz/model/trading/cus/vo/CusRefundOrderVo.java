package com.cdz360.biz.model.trading.cus.vo;

import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.model.base.vo.BaseObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户申请退款单信息")
public class CusRefundOrderVo extends BaseObject {
    @Schema(description = "申请退款单号")
    private String seqNo;

    @Schema(description = "退款显示文案: 原路退回到微信")
    private String refundTip;

    /**
     * @link com.cdz360.model.cus.wallet.type.RefundStatus
     */
    @Schema(description = "状态: 0, 未知; 1, 待处理; 2, 客服审核; 3, 客服拒回; 4, 已审核; 5, 已拒绝; 20, 已打款; 21, 自动退款失败")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer status;

//    @Schema(description = "充值来源: UNKNOWN, 其他; WX_LITE, 微信小程序;" +
//            " ALIPAY_LITE, 支付宝小程序; ANDROID_APP, 安卓APP; IOS_APP, iOS APP;" +
//            " MGM_WEB, 平台充值; MERCHANT, 企业充值; HLHT, 三方平台;", description = "这个仅仅考虑微信和支付宝")
//    private DepositSourceType sourceType;

    @Schema(description = "退款到账账户类型: UNKNOWN, 未知;" +
            "ALIPAY, 支付宝;" +
            "WXPAY, 微信支付;" +
            "BANK_CARD, 银行卡支付;" +
            "BUSINESS_ACCOUNT, 对公转账;")
    private PayChannel payChannel;

    @Schema(description = "可退款金额, 单位'元', 2位小数")
    private BigDecimal refundAmount;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
