package com.cdz360.biz.model.trading.warn.po;

import com.cdz360.base.model.base.vo.BaseObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class WarningPo extends BaseObject {

    private Long id;

    private String warningCode;

    private String warningName;

    private String warningDesc;

    private Boolean enable;




}
