package com.cdz360.biz.model.trading.soc.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "场站充电限制设置")
public class SiteSocCfgPo implements Serializable {

	private static final long serialVersionUID = -416358197688212091L;

	@NotNull(message = "id 不能为 null")
	private Long id;

	@NotNull(message = "siteId 不能为 null")
	@Size(max = 32, message = "siteId 长度不能超过 32")
	private String siteId;

	@Schema(description = "配置开关")
	@NotNull(message = "allow 不能为 null")
	private Boolean allow;

	@NotNull(message = "createTime 不能为 null")
	private Date createTime;

	private Date updateTime;


}
