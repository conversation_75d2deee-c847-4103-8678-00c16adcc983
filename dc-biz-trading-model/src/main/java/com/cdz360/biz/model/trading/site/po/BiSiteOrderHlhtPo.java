package com.cdz360.biz.model.trading.site.po;


import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;



@Data

@Accessors(chain = true)

@Schema(description = "场站各账户充电消费统计")

public class BiSiteOrderHlhtPo {



	@Schema(description = "记录ID")

	@NotNull(message = "id 不能为 null")

	private Long id;



	@Schema(description = "场站各账户充电消费统计ID(t_bi_site_order_account.id)")

	@NotNull(message = "biAccountId 不能为 null")

	private Long biAccountId;


	@Schema(description = "企业客户ID(t_bloc_user.id)")

	@NotNull(message = "corpId 不能为 null")

	private Long corpId;



	@Schema(description = "充电订单量(单位: 个)")

	private Long orderCnt;



	@Schema(description = "充电订单总金额(单位: 元)")

	private BigDecimal orderFee;



	@Schema(description = "后付费金额(单位: 元)")

	private BigDecimal postSettlementFee;



	@Schema(description = "预付费金额(单位: 元)")

	private BigDecimal preSettlementFee;



	@Schema(description = "赠送金额(单位: 元)")

	private BigDecimal freeFee;



	@Schema(description = "实际金额(单位: 元)")

	private BigDecimal costFee;



	@Schema(description = "创建时间")

	private Date createTime;



	@Schema(description = "更新时间")

	private Date updateTime;





}

