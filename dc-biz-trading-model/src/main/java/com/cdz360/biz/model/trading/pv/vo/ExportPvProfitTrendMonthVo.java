package com.cdz360.biz.model.trading.pv.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * ExportPvProfitTrendMonthVo
 *
 * @since 11/1/2021 2:48 PM
 * <AUTHOR>
 */
@Schema(description = "光储收益月趋势")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ExportPvProfitTrendMonthVo extends ExportPvProfitTrendVo {

    @ExcelField(title = "日期", sort = 1, dateFormat = "yyyy-MM")
    @Schema(description = "日期")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date time;

    @Schema(description = "发电电量（度）")
    @ExcelField(title = "发电电量（度）", sort = 2)
    private BigDecimal totalKwh;

    @Schema(description = "发电收益（元）")
    @ExcelField(title = "发电收益（元）", sort = 3)
    private BigDecimal totalProfit;

    public void setTime(Date time) {
        this.time = time;
    }
}