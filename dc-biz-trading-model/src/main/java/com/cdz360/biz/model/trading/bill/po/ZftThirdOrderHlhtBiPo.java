package com.cdz360.biz.model.trading.bill.po;


import com.cdz360.biz.model.trading.bill.type.DailyBillCheckResult;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;


@Data

@Accessors(chain = true)

@Schema(description = "互联互通打款记录对账结果")

public class ZftThirdOrderHlhtBiPo {



	@Schema(description = "ID")

	@NotNull(message = "id 不能为 null")

	private Long id;



	@Schema(description = "合作方充电订单号")

	@NotNull(message = "openOrderId 不能为 null")

	private String openOrderId;



	@Schema(description = "直付金额总额")

	@NotNull(message = "直付金额总额 不能为 null")

	private BigDecimal zftTotalMoney;


	@Schema(description = "平台订单号")
	private String orderNo;


	@Schema(description = "充电订单费用")
	private BigDecimal orderFee;


	@Schema(description = "占桩费")
	private BigDecimal parkingFee;



	@Schema(description = "支付平台对账结果: FULL_MATCH(完全匹配); AMOUNT_NOT_MATCH(金额不匹配); NO_NOT_MATCH(未找到对应订单); OTHERS(其他)")

	@NotNull(message = "checkResult 不能为 null")

	private DailyBillCheckResult checkResult;

	@Schema(description = "更新时间")

	private Date updateTime;

}

