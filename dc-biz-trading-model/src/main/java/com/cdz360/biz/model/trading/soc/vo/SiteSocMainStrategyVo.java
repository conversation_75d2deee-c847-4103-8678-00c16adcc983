package com.cdz360.biz.model.trading.soc.vo;

import com.cdz360.biz.model.trading.soc.po.SiteSocMainStrategyPo;
import com.cdz360.biz.model.trading.soc.po.SiteSocStrategyPo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * SiteSocMainStrategyVo
 * 
 * @since 12/30/2021 10:22 AM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "soc限制主策略")
@EqualsAndHashCode(callSuper = true)
public class SiteSocMainStrategyVo extends SiteSocMainStrategyPo {

    @Schema(description = "时间段")
    private List<SiteSocStrategyPo> strategyList;
}