package com.cdz360.biz.model.trading.order.dto;

import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.biz.model.invoice.type.InvoiceType;
import com.cdz360.biz.model.invoice.type.InvoicedStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "查开票充电订单信息")
@EqualsAndHashCode(callSuper = true)
public class TaxOrderDto extends BaseObject {
    @Schema(description = "订单号 充电订单号/充值订单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderNo;

    @Schema(description = "发票记录Id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long invoiceId;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "申请开票的类型: 个人普票(PER_COMMON); 企业普票(ENTER_COMMON); 企业专票(ENTER_PROFESSION) 开票记录上的开票类型")
    private InvoiceType invoiceType;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "发票代码", example = "150000456123")
    private String invoiceCode;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "发票号码", example = "06350281")
    private String invoiceNumber;

    @Schema(description = "发票状态: SUBMITTED-审核中(未导出),REVIEWED-审核中(已导出)," +
            "AUDIT_FAILED-审核未通过(未导出),INVOICING_FAIL-开票失败(已导出)," +
            "COMPLETED-已开具,NOT_SUBMITTED-待提交,RED_DASHED-已红冲,INVALID-已作废",
            example = "SUBMITTED 开票记录上的状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InvoicedStatus invoicedStatus;
}
