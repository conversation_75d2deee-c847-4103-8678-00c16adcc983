package com.cdz360.biz.model.trading.iot.po;


import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.biz.model.ess.type.EquipAlertStatus;
import com.cdz360.biz.model.iot.type.GtiVendor;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;


@Data

@Accessors(chain = true)

@Schema(description = "辐射仪SRS信息")

public class SrsPo {


    @Schema(description = "主键id")

    @NotNull(message = "id 不能为 null")

    private Long id;


    @Schema(description = "储能ESS唯一编号")

    @NotNull(message = "dno 不能为 null")

    @Size(max = 16, message = "dno 长度不能超过 16")

    private String dno;


    @Schema(description = "设备名称")

    @Size(max = 32, message = "name 长度不能超过 32")

    private String name;


    @Schema(description = "串口通信(485/modbus) ID")

    @NotNull(message = "sid 不能为 null")

    private Integer sid;


    @Schema(description = "品牌名称")

    @NotNull(message = "vendor 不能为 null")

    @Size(max = 16, message = "vendor 长度不能超过 16")
    private GtiVendor vendor;


    @Schema(description = "网关编号")

    @NotNull(message = "gwno 不能为 null")

    @Size(max = 16, message = "gwno 长度不能超过 16")

    private String gwno;


    @Schema(description = "场站ID")

    @Size(max = 32, message = "siteId 长度不能超过 32")

    private String siteId;


    @Schema(description = "设备型号")

    @NotNull(message = "deviceModel 不能为 null")

    @Size(max = 32, message = "deviceModel 长度不能超过 32")

    private String deviceModel;


    @Schema(title = "状态", description = "设备状态.0未知;1在线;2离线;3启动中;4告警;5故障;99,下线")
    @NotNull(message = "status 不能为 null")
    private EquipStatus status;

    @Schema(description = "告警状态: 0,未知;1,正常;2,异常")
    private EquipAlertStatus alertStatus;


    @Schema(description = "记录创建时间")
    @NotNull(message = "createTime 不能为 null")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;


    @Schema(description = "记录最后修改时间")
    @NotNull(message = "updateTime 不能为 null")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;


}

