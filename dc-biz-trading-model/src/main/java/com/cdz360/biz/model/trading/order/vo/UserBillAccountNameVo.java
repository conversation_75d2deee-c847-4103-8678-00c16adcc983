package com.cdz360.biz.model.trading.order.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 充值记录查看
 *
 * <AUTHOR>
 * @since 2019/11/5 17:07
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ToString
@Schema(description = "用户充值支付账户，到账账户汇总")
public class UserBillAccountNameVo {

    @Schema(description = "用户userId")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long userId;

    @Schema(description = "支付账户名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String outAccountName;

    @Schema(description = "到账账户名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String inAccountName;


}
