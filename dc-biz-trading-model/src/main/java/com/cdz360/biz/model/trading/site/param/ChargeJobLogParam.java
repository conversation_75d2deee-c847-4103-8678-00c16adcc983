package com.cdz360.biz.model.trading.site.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.param.TimeFilter;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Schema(description = "定时充电任务日志列表查询入参")
@EqualsAndHashCode(callSuper = true)
public class ChargeJobLogParam extends BaseListParam {

    @Schema(description = "定时充电任务ID")
    private Long jobId;

    @Schema(description = "执行时间筛选")
    private TimeFilter createTime;

    @Schema(description = "枪头编号，模糊")
    private String plugNo;
}
