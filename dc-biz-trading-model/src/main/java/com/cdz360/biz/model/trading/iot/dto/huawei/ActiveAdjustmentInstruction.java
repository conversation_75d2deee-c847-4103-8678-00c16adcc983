package com.cdz360.biz.model.trading.iot.dto.huawei;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 有功调节指令
 */
@Getter
public enum ActiveAdjustmentInstruction implements DcEnum {

    UNKNOWN(0, "未知"),
    PERCENTAGE_REDUCTION(40125, "有功百分比降额（0.1%）"),
    FIXED_REDUCTION(40120, "有功固定值降额"),
    FIXED_REDUCTION_W(40126, "有功固定值降额（W）"),
    POWER_MAXIMUM(42178, "有功功率最大值"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    ActiveAdjustmentInstruction(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static ActiveAdjustmentInstruction valueOf(Object codeIn) {
        if (codeIn == null) {
            return ActiveAdjustmentInstruction.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof ActiveAdjustmentInstruction) {
            return (ActiveAdjustmentInstruction) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (ActiveAdjustmentInstruction type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return ActiveAdjustmentInstruction.UNKNOWN;
    }

}
