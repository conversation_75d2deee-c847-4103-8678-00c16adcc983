package com.cdz360.biz.model.trading.site.vo;

import com.cdz360.biz.model.trading.site.po.BsBoxSettingPo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2020/1/7 11:25
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Schema(description = "带场站信息的桩配置信息")
public class BsBoxSettingWithSiteInfoVo extends BsBoxSettingPo {
    @Schema(description = "场站Id")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "场站名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;
}
