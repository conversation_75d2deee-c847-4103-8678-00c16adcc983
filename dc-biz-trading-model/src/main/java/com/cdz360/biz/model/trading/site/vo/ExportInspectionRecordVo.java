package com.cdz360.biz.model.trading.site.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "巡检工单导出")
@Data
@Accessors(chain = true)
public class ExportInspectionRecordVo implements Serializable {


    @ExcelField(title = "巡检单号", sort = 1)
    @Schema(description = "巡检工单")
    private String no;

    @Schema(description = "场站所属商户")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long commId;

//    @ExcelField(title = "所属商户", sort = 3)
//    @Schema(description = "所属商户名称")
//    private String commName;

    @ExcelField(title = "所属运维组", sort = 3)
    @Schema(description = "场站运维组名称")
    private String siteGidName;

    private String siteId;

    @ExcelField(title = "站点名称", sort = 6)
    @Schema(description = "站点名称")
    private String siteName;

    @ExcelField(title = "交流桩数", sort = 8)
    @Schema(description = "交流桩数")
    private Integer acEvseNum;

    @ExcelField(title = "直流桩数", sort = 10)
    @Schema(description = "直流桩数")
    private Integer dcEvseNum;

    private Long opUid;

    @ExcelField(title = "巡检人", sort = 12)
    @Schema(description = "巡检创建人(同巡检人)")
    private String rummager;

    @ExcelField(title = "创建时间", sort = 14)
    @Schema(description = "创建时间")
    private Date createTime;

    @ExcelField(title = "巡检时间", sort = 16)
    @Schema(description = "巡检时间")
    private Date reportTime;

    @ExcelField(title = "备注", sort = 18)
    private String remark;

    @ExcelField(title = "状态", sort = 20)
    private String status;

    private Long qcUid;

    @ExcelField(title = "质检人", sort = 22)
    @Schema(description = "质检人")
    private String qcUserName;

    @ExcelField(title = "质检人备注", sort = 24)
    @Schema(description = "质检人备注")
    private String qcRemark;
}
