package com.cdz360.biz.model.trading.site.po;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "场站每月营收数据")
@Data
@Accessors(chain = true)
public class BiSiteMonthPo {

    private Long id;

    @Schema(description = "场站ID")
    @JsonInclude(Include.NON_NULL)
    private String siteId;

    @Schema(description = "充电量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal elec;

    @Schema(description = "电费收入,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal elecFee;

    @Schema(description = "自动扣款: true/false")
    @JsonInclude(Include.NON_NULL)
    private Boolean autoDebit;

    @Deprecated(since = "20220930")
    @Schema(description = "月份(记录为每月1号)")
    @JsonInclude(Include.NON_NULL)
    private LocalDate date;

    @Schema(description = "账期日期开始时间(0点)")
    @JsonInclude(Include.NON_NULL)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private LocalDate fromDate;

    @Schema(description = "账期日期结束时间(24点)")
    @JsonInclude(Include.NON_NULL)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private LocalDate toDate;

    @Schema(description = "是否有效")
    @JsonInclude(Include.NON_NULL)
    private Boolean enable;

    @Schema(description = "流程实例ID")
    @JsonInclude(Include.NON_EMPTY)
    private String procInstId;

    @Schema(description = "充电订单汇总数据")
    @JsonInclude(Include.NON_EMPTY)
    private String orderData;

    @Schema(description = "已完成充电订单数据[orderData中字段意义提示]")
    @Data
    @Accessors(chain = true)
    static class OrderData {

        @Schema(description = "充电订单完成数量")
        @JsonInclude(Include.NON_NULL)
        private Long orderNum;

        @Schema(description = "总电量（kW·h）")
        @JsonInclude(Include.NON_NULL)
        private BigDecimal totalElec;

        @Schema(description = "总电费（元）")
        @JsonInclude(Include.NON_NULL)
        private BigDecimal totalElecFee;

        @Schema(description = "总服务费（元）")
        @JsonInclude(Include.NON_NULL)
        private BigDecimal totalServFee;

        @Schema(description = "总金额（元）")
        @JsonInclude(Include.NON_NULL)
        private BigDecimal totalFee;

        @Override
        public String toString() {
            return JsonUtils.toJsonString(this);
        }
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
