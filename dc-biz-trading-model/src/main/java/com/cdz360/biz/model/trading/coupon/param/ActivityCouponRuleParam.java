package com.cdz360.biz.model.trading.coupon.param;

import com.cdz360.biz.model.trading.coupon.type.ActivityType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "活动对应的发券规则")
public class ActivityCouponRuleParam {

	/**
	 * 编辑活动时校验已领券人次用
	 */
	private Long id;

	private ActivityType type;

	@Schema(description = "开始时间")
	@NotNull(message = "timeFrom 不能为 null")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	private Date timeFrom;

	@Schema(description = "结束时间")
	@NotNull(message = "timeTo 不能为 null")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	private Date timeTo;

	@Schema(description = "活动id")
	@NotNull(message = "activityId不能为 null")
	private Long activityId;

	@Schema(description = "同一手机号是否可重复领券，0不可重复，1可重复")
	private Boolean repeatActive;

	@Schema(description = "单次发券的张数或每次领取的券数")
	@NotNull(message = "couponsPerTime 不能为 null")
	private Integer couponsPerTime;

	@Schema(description = "最大发券次数或最大领取次数")
	@NotNull(message = "maxAmountPerTime 不能为 null")
	private Integer maxAmountPerTime;

	@Schema(description = "预计发券数量,平台发券无需填写")
	private Integer totalAmount;

}
