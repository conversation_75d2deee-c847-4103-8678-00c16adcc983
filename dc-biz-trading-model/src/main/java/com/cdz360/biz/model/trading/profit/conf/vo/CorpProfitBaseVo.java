package com.cdz360.biz.model.trading.profit.conf.vo;

import com.cdz360.base.model.corp.type.CorpType;
import com.cdz360.biz.model.trading.profit.conf.po.CorpProfitBasePo;
import com.cdz360.biz.model.trading.profit.conf.po.CorpProfitSubPo;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * CorpProfitBaseVo
 *
 * @since 5/12/2022 9:20 AM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "企业收益基本公式")
@EqualsAndHashCode(callSuper = true)
public class CorpProfitBaseVo extends CorpProfitBasePo {
    private List<CorpProfitSubPo> corpProfitSubList;
    private String corpName;

    @Schema(description = "是否全额开票,true-是，false-否")
    private Boolean fullInvoicing;

    @Schema(description = "企业类型")
    private CorpType type;
}