package com.cdz360.biz.model.trading.site.param;

import com.cdz360.base.model.base.param.BaseListParam;
import java.util.List;
import java.util.Optional;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class InspectionParam extends BaseListParam {

    private List<String> siteIdList;

    private String commIdChain;

    @Override
    public String toString() {
        return "InspectionParam{" +
            "siteIdList.size=" + Optional.ofNullable(siteIdList).map(List::size).orElse(0) +
            ", commIdChain='" + commIdChain + "'}";
    }
}
