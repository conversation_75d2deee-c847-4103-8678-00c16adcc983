package com.cdz360.biz.model.trading.site.po;

import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * BiSiteSumPo
 *
 * @since 3/23/2020 1:34 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
public class BiSiteSummaryElectPo {
    private String name;
    private Long corpId;
    private List<BiSiteElectPo> biSummary;
}