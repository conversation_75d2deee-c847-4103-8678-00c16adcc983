package com.cdz360.biz.model.trading.iot.dto.huawei;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * 华为逆变器状态2
 */
@Data
public class InverterStatus2 {

    // 闭锁状态（0：闭锁，1：非闭锁）
    @JsonProperty("l")
    private Boolean latched;

    // PV连接状态（0：未连接，1：连接）
    @JsonProperty("pc")
    private Boolean pvConnect;

    // DSP数据采集状态（0：无，1：有）
    @JsonProperty("ddc")
    private Boolean dspDataCollect;

}
