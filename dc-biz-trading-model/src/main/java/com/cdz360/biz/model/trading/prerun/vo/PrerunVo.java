package com.cdz360.biz.model.trading.prerun.vo;

import com.cdz360.biz.model.finance.type.BizType;
import com.cdz360.biz.model.trading.prerun.po.PrerunPo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * PrerunVo
 *
 * @since 6/24/2022 3:21 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "调试工单VO")
@EqualsAndHashCode(callSuper = true)
public class PrerunVo extends PrerunPo implements Serializable {

    private String siteName;
    private String commName;

    @Schema(description = "场站运维组ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteGid;

    @Schema(description = "场站运维组名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteGidName;

    /**
     * {@link BizType}
     */
    @Schema(description = "运营属性. 0,未知; 1,自营; 2,非自营; 3,互联互通; 4, 脱机自营; 5, 脱机非自营;")
    private Integer siteBizType;
    private Integer gcType;

    private String siteAddress;

//    private List<ImageVo> siteImages;

}