package com.cdz360.biz.model.trading.site.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "场站基本信息")
public class SiteSimpleDto {
    /**
     * 场站ID
     */
    private String siteId;
    /**
     * 商户名称
     */
    private String commName;
    /**
     * 场站名称
     */
    private String  siteName;
}
