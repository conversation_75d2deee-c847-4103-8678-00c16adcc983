package com.cdz360.biz.model.trading.coupon.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.biz.model.trading.coupon.type.CouponStatusType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class AppletCouponParam extends BaseListParam {

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private long cusId;

    private List<CouponStatusType> statusList;
}
