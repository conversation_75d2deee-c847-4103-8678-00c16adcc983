package com.cdz360.biz.model.trading.order.type;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 充值状态
 *
 * <AUTHOR>
 * @since 2019/11/12 14:16
 */
@Getter
public enum PayBillStatus {
    UNPAID(0, "未支付"),
    PAID(1, "已支付"),
    PAY_FAIL(2, "支付失败"),
    SEND_PAY_SUCCESS(3, "发起支付成功"),
    SEND_PAY_FAIL(4, "发起支付失败"),
    TIME_OUT(5, "超时"),
    EXHAUST(6, "余额已耗尽"),
    UNKNOWN(99, "未知");

    @JsonValue
    private final int code;
    private final String desc;

    PayBillStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static PayBillStatus valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (PayBillStatus status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return PayBillStatus.UNKNOWN;
    }
}
