package com.cdz360.biz.model.trading.coupon.param;

import com.cdz360.biz.model.trading.coupon.type.ActivityStatusType;
import com.cdz360.biz.model.trading.coupon.type.ActivityType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * ListActivityParam
 *
 * @since 8/3/2020 9:39 AM
 * <AUTHOR>
 */
@Data
public class ListActivityParam {
    private String name;
    private ActivityType type;
    private Long commId;
    private String couponDictName;
    private ActivityStatusType status;

    private String couponId;

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private Long activityId;

    @Schema(description = "偏移量，需要前端根据翻页和size来计算")
    private Long start;
    @Schema(description = "每页最大显示条数")
    private Integer size;

    private String idChain;
}