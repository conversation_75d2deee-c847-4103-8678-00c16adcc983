package com.cdz360.biz.model.trading.site.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.lang.Double;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel(value = "订单占位收费详情")
public class OrderOvertimeParkDivisionPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@Size(max = 64, message = "orderNo 长度不能超过 64")
	private String orderNo;

	@ApiModelProperty(value = "t_site_overtime_park_division.id")
	private Long divisionId;

	@ApiModelProperty(value = "占用时长：分钟")
	private Integer duration;

	@ApiModelProperty(value = "该时段超时收费金额")
	private BigDecimal fee;

	private Boolean enable;

	private Date createTime;

	private Date updateTime;


}
