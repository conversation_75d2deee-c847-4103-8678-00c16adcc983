package com.cdz360.biz.model.trading.site.vo;

import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.biz.model.annotations.ExcelField;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "超停收费订单统计数据")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class OvertimeParkFeeOrderBi extends BaseObject {

    @ExcelField(title = "总计费时长 (分钟)", sort = 1)
    @Schema(description = "总计费时长, 单位: 分钟")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long totalTime;

    @ExcelField(title = "总停充超时费 (元)", sort = 6)
    @Schema(description = "总停充超时费, 单位: 元")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal totalAmount;
}
