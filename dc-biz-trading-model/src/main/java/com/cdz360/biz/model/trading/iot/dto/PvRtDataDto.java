package com.cdz360.biz.model.trading.iot.dto;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.iot.type.PvMode;
import com.cdz360.biz.model.trading.iot.dto.huawei.ActiveAdjustmentInstruction;
import com.cdz360.biz.model.trading.iot.dto.huawei.DeviceStatus;
import com.cdz360.biz.model.trading.iot.dto.huawei.InverterStatus1;
import com.cdz360.biz.model.trading.iot.dto.huawei.InverterStatus2;
import com.cdz360.biz.model.trading.iot.dto.huawei.InverterStatus3;
import com.cdz360.biz.model.trading.iot.dto.huawei.ReactiveAdjustmentInstruction;
import com.cdz360.biz.model.trading.iot.dto.huawei.ReactiveAdjustmentMode;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
@Schema(description = "逆变器运行数据")
public class PvRtDataDto {

    @JsonProperty("id")
    @Schema(description = "设备modbus id", example = "123")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer deviceId;

    @JsonProperty("dno")
    @Schema(description = "设备编号", example = "ABC123")
    @JsonInclude(Include.NON_EMPTY)
    private String dno;

    @JsonProperty("sno")
    @Schema(description = "设备铭牌编号")
    @JsonInclude(Include.NON_EMPTY)
    private String serialNo;

    // 错误代码
    @JsonProperty("err")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> errorCodeList;

    @JsonProperty("ak")
    @Schema(description = "总发电量, 单位: 1KW·h", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal totalKwh;

    @JsonProperty("ah")
    @Schema(description = "总发电时间, 单位: 1H", example = "567")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long totalHour;

    @JsonProperty("v1")
    @Schema(description = "第一路PV电压, 单位: 1V", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal pv1Voltage;

    @JsonProperty("c1")
    @Schema(description = "第一路PV电流, 单位: 1A", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal pv1Current;

    @JsonProperty("v2")
    @Schema(description = "第二路PV电压, 单位: 1V", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal pv2Voltage;

    @JsonProperty("c2")
    @Schema(description = "第二路PV电流, 单位: 1A", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal pv2Current;

    @JsonProperty("rv")
    @Schema(description = "R相电压, 单位: 1V", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal voltageR;

    @JsonProperty("rc")
    @Schema(description = "R相电流, 单位: 1A", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal currentR;

    @JsonProperty("rf")
    @Schema(description = "R相频率, 单位: 1Hz", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal frequencyR;

    @JsonProperty("sv")
    @Schema(description = "S相电压, 单位: 1V", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal voltageS;

    @JsonProperty("sc")
    @Schema(description = "S相电流, 单位: 1A", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal currentS;

    @JsonProperty("sf")
    @Schema(description = "S相频率, 单位: 1Hz", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal frequencyS;

    @JsonProperty("tv")
    @Schema(description = "T相电压, 单位: 1V", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal voltageT;

    @JsonProperty("tc")
    @Schema(description = "T相电流, 单位: 1A", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal currentT;

    @JsonProperty("tf")
    @Schema(description = "T相频率, 单位: 1Hz", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal frequencyT;

    @JsonProperty("rp")
    @Schema(description = "额定功率（只读一次）, 单位: 1kW")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal ratedPower;

    @JsonProperty("ip")
    @Schema(description = "输入功率, 单位: 1kW")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal inPower;

    @JsonProperty("p")
    @Schema(description = "输出功率, 单位: 1W", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal outPower;

    @JsonProperty("m")
    @Schema(description = "运行状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private PvMode rtMode;

    @JsonProperty("t")
    @Schema(description = "散热片温度, 单位: 1℃", example = "23.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal deviceTemp;

    @JsonProperty("tk")
    @Schema(description = "当日发电量, 单位: 1kW·h", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal todayKwh;

    // RTC 时间
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    @JsonProperty("ts")
//    private Date rtcTime;

    /**
     * 以下为华为逆变器相关字段 ↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓↓
     */

    @JsonProperty("pno")
    @Schema(description = "设备零件号（只读一次）")
    @JsonInclude(Include.NON_EMPTY)
    private String partNo;

    @JsonProperty("dm")
    @Schema(description = "设备型号（只读一次）")
    @JsonInclude(Include.NON_EMPTY)
    private String deviceModel;

    @JsonProperty("s1")
    @Schema(description = "状态1")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<InverterStatus1> status1;

    @JsonProperty("s2")
    @Schema(description = "状态2")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InverterStatus2 status2;

    @JsonProperty("s3")
    @Schema(description = "状态3")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InverterStatus3 status3;

    @JsonProperty("it")
    @Schema(description = "内部温度, 单位: 1℃")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal innerTemp;

    @JsonProperty("ii")
    @Schema(description = "绝缘阻抗, 单位: 1MΩ（1MΩ=1000000Ω）")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal insulationImpedance;

    @JsonProperty("ds")
    @Schema(description = "设备状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private DeviceStatus deviceStatus;

    @JsonProperty("fc")
    @Schema(description = "故障码")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer faultCode;

    @JsonProperty("pot")
    @Schema(description = "开机时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long powerOnTime;

    @JsonProperty("pft")
    @Schema(description = "关机时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long powerOffTime;

    @JsonProperty("st")
    @Schema(description = "系统时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long systemTime;

    @JsonProperty("gn")
    @Schema(description = "组串个数（只读一次）")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer groupNum;

    @JsonProperty("mn")
    @Schema(description = "MPPT个数（只读一次）")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer mpptNum;

    @JsonProperty("v3")
    @Schema(description = "第三路PV电压, 单位: 1A")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal pv3Voltage;

    @JsonProperty("c3")
    @Schema(description = "第三路PV电流, 单位: 1V")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal pv3Current;

    @JsonProperty("atpm")
    @Schema(description = "最大有功, 单位: 1kW")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal activePowerMax;

    @JsonProperty("apm")
    @Schema(description = "最大视在, 单位: 1kVA")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal apparentPowerMax;

    @JsonProperty("orpm")
    @Schema(description = "最大无功（向电网馈入）, 单位: 1kVar")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal outReactivePowerMax;

    @JsonProperty("irpm")
    @Schema(description = "最大无功（从电网吸收）, 单位: 1kVar")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal inReactivePowerMax;

    @JsonProperty("gabv")
    @Schema(description = "电网AB线电压, 单位: 1V")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal gridAbVoltage;

    @JsonProperty("gbcv")
    @Schema(description = "电网BC线电压, 单位: 1V")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal gridBcVoltage;

    @JsonProperty("gcav")
    @Schema(description = "电网CA线电压, 单位: 1V")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal gridCaVoltage;

    @JsonProperty("gv")
    @Schema(description = "电网A/B/C线电压, 单位: 1V")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private GtiAbcItem gridVoltage;

    @JsonProperty("gc")
    @Schema(description = "电网A/B/C线电流, 单位: 1A")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private GtiAbcItem gridCurrent;

    @JsonProperty("papt")
    @Schema(description = "当天峰值有功功率, 单位: 1kW")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal peakActivePowerToday;

    @JsonProperty("atp")
    @Schema(description = "有功功率, 单位: 1kW")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal activePower;

    @JsonProperty("rtp")
    @Schema(description = "无功功率, 单位: 1kVar")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal reactivePower;

    @JsonProperty("pf")
    @Schema(description = "功率因数, 单位: --")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal powerFactor;

    @JsonProperty("gf")
    @Schema(description = "电网频率, 单位: 1Hz")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal gridFrequency;

    @JsonProperty("e")
    @Schema(description = "效率, 单位: 1%")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal efficiency;

    @JsonProperty("aam")
    @Schema(description = "有功调节模式, 0：百分比；1：固定值；")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer activeAdjustmentMode;

    @JsonProperty("aav")
    @Schema(description = "有功调节值（随有功调节模式而变化）\n"
        + "百分比：1%\n"
        + "固定值：1kW")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal activeAdjustmentValue;

    @JsonProperty("aai")
    @Schema(description = "有功调节指令")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ActiveAdjustmentInstruction activeAdjustmentInstruction;

    @JsonProperty("ram")
    @Schema(description = "无功调节模式")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ReactiveAdjustmentMode reactiveAdjustmentMode;

    @JsonProperty("rav")
    @Schema(description = "无功调节值（随无功调节模式而变化）\n"
        + "功率因数：0.001\n"
        + "绝对值：0.001kVar\n"
        + "Q/S：0.001\n"
        + "Q-U特征曲线：固定填0\n"
        + "cosϕ-P/Pn特征曲线：固定填0\n"
        + "PF-U特征曲线：固定填0\n"
        + "Q-P特征曲线：固定填0")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal reactiveAdjustmentValue;

    @JsonProperty("rai")
    @Schema(description = "无功调节指令")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ReactiveAdjustmentInstruction reactiveAdjustmentInstruction;

    @JsonProperty("es")
    @Schema(description = "储能运行状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer essStatus;

    @JsonProperty("ecadp")
    @Schema(description = "储能充放电功率, 单位: 1W")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal essChargeAndDischargePower;

    @JsonProperty("eckt")
    @Schema(description = "储能当日充电量, 单位: 1kWh")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal essChargeKwhToday;

    @JsonProperty("edkt")
    @Schema(description = "储能当日放电量, 单位: 1kWh")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal essDischargeKwhToday;

    @JsonProperty("map")
    @Schema(description = "电表有功功率, 单位: 1W")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal meterActivePower;

    @JsonProperty("on")
    @Schema(description = "优化器个数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer optimizerNum;

    @JsonProperty("oon")
    @Schema(description = "优化器在线个数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer optimizerOnlineNum;

    /**
     * ↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑ 以上为华为逆变器相关字段
     */

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

    @Data
    public static class GtiAbcItem {

        private BigDecimal v1;// A 相电压/流

        private BigDecimal v2;// B 相电压/流

        private BigDecimal v3;// C 相电压/流
    }

}
