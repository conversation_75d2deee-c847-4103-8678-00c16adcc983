package com.cdz360.biz.model.trading.site.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.biz.model.trading.order.type.BiDependOnType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "场站充电信息统计查询")
public class ListBiSiteOrderParam extends BaseListParam {

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "商户Id链", hidden = true)
    private String commIdChain;

    @Schema(description = "场站ID列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> siteIdList;

    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String siteId;

    @Schema(description = "是否包含互联互通场站 null or true 表示包含; false 表示不包含")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean includedHlhtSite;

    @Schema(description = "场站组列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> gidList;

    @Schema(description = "查询时间类型 时间范围存在为必填")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BiDependOnType dependOn;

    @Schema(description = "查询开始时间 年月")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date fromDate;

    @Schema(description = " 年月")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date toDate;

    @Schema(description = "日期")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date time;

    @Schema(description = "是否统计各账户的消费数据")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean biAccount;
}
