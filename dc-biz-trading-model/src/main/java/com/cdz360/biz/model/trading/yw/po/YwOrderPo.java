package com.cdz360.biz.model.trading.yw.po;


import com.cdz360.base.model.base.type.UserType;
import com.cdz360.biz.model.FileItem;
import com.cdz360.biz.model.trading.yw.dto.CarInfo;
import com.cdz360.biz.model.trading.yw.dto.Goods;
import com.cdz360.biz.model.trading.yw.type.CreateYwOrderSourceType;
import com.cdz360.biz.model.trading.yw.type.YwOrderLevel;
import com.cdz360.biz.model.trading.yw.type.YwOrderStatus;
import com.cdz360.biz.model.trading.yw.type.YwOrderTag;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;


@Data

@Accessors(chain = true)

@Schema(description = "运维工单表")

public class YwOrderPo {

    @Schema(description = "运维工单编号")
    @NotNull(message = "ywOrderNo 不能为 null")
    @Size(max = 32, message = "ywOrderNo 长度不能超过 32")
    private String ywOrderNo;

    @Schema(description = "场站编号")
    @NotNull(message = "siteId 不能为 null")
    @Size(max = 32, message = "siteId 长度不能超过 32")
    private String siteId;

    @Schema(description = "客户是否满意,1-满意，2-不满意")
    private Long isPerfect;

    @Schema(description = "客户建议")
    private String advice;

    @Schema(description = "客户签名")
    private String signImage;

    @Schema(description = "运维时长(秒)")
    private Long ywDuration;

    @Schema(description = "工单状态(INIT: 待接收; RECEIVED: " +
        "已接收; PROCESSING: 处理中; TRANSFERRING: 转派中;" +
        " WAIT_CHECK : 待质检; NO_PASS: 不合格; SOLVED: 已完成;)")
    private YwOrderStatus orderStatus;

    @Schema(description = "故障的充电桩编号(可多个)")
    private List<String> evseNoList;

    @Schema(description = "是否过质保期: true(是); false(否)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean overExpireDate;

    @Schema(description = "关联充电订单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderNo;

    @Schema(description = "故障图片列表 故障报修")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> faultImages;

    @Schema(description = "故障描述")
    @Size(max = 400, message = "faultDesc 长度不能超过 400")
    private String faultDesc;

    @Schema(description = "故障等级: 10(一般); 20(紧急); 30(重大)")
    @NotNull(message = "faultLevel 不能为 null")
    private YwOrderLevel faultLevel;

    @Schema(description = "创建运维工单客户端. UNKNOWN,未知; C端客户(CUSTOMER_SRC);" +
        " 商户(COMM_SRC); 运维(YW_SRC); 客服(KF_SRC); 其他(OTHER_SRC)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @NotNull(message = "sourceType 不能为 null")
    private CreateYwOrderSourceType sourceType;

    @Schema(description = "配件(运维物品): 例: [{\"name\": \"物品名称\", \"num\": 1}]")
    private List<Goods> goods;

    @Schema(description = "涉及车辆信息: " +
        "例: [{\"brand\": \"车辆品牌\", \"model\": \"车辆类型\", " +
        "\"needVoltage\": \"需求电压\", \"needCurrent\": \"需求电流\", " +
        "\"otherEvse\": true}]")
    private CarInfo carInfo;

    @Schema(description = "运维人类型. 0,未知; 1,商户/平台用户; 3,企业客户; 4,系统")
    @NotNull(message = "maintType 不能为 null")
    private UserType maintType;

    @Schema(description = "运维人ID")
    @NotNull(message = "maintUid 不能为 null")
    private Long maintUid;

    @Schema(description = "运维人名字")
    @NotNull(message = "maintName 不能为 null")
    @Size(max = 32, message = "maintName 长度不能超过 32")
    private String maintName;

    @Schema(description = "运维人处理时间")
    private Date maintTime;

    @Schema(description = "质检人类型. 0,未知; 1,商户/平台用户; 3,企业客户; 4,系统")
    @NotNull(message = "maintType 不能为 null")
    private UserType qcType;


    @Schema(description = "质检人ID")
    @NotNull(message = "maintUid 不能为 null")
    private Long qcUid;

    @Schema(description = "质检人名字")
    @NotNull(message = "maintName 不能为 null")
    @Size(max = 32, message = "maintName 长度不能超过 32")
    private String qcName;

    @Schema(description = "质检人处理时间")
    private Date qcTime;

    @Schema(description = "质检人备注")
    private String qcRemark;

    @Schema(description = "是否远程解决")
    private Boolean remote;

    @Schema(description = "可能原因")
    @Size(max = 400, message = "faultReason 长度不能超过 400")
    private String faultReason;

    @Schema(description = "检查步骤")
    @Size(max = 400, message = "checkStep 长度不能超过 400")
    private String checkStep;

    @Schema(description = "处理措施及结果")
    @Size(max = 400, message = "dealProcess 长度不能超过 400")
    private String dealProcess;

    @Schema(description = "维修图片")
//	private List<String> images;
    private List<FileItem> images;

    @Schema(description = "创建工单的用户是否已经接收处理结果反馈 true -- 用户已查阅;false -- 用户还没接收查阅")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean recNotice;

    @Schema(description = "创建操作人类型. 0,未知; 1,商户/平台用户; 3,企业客户; 4,系统")
    @NotNull(message = "createOpType 不能为 null")
    private UserType createOpType;

    @Schema(description = "创建操作人ID")
    @NotNull(message = "createOpUid 不能为 null")
    private Long createOpUid;

    @Schema(description = "创建操作人名字")
    @NotNull(message = "createOpName 不能为 null")
    @Size(max = 32, message = "createOpName 长度不能超过 32")
    private String createOpName;


    @Schema(description = "操作人类型. 0,未知; 1,商户/平台用户; 3,企业客户; 4,系统")
    @NotNull(message = "updateOpType 不能为 null")
    private UserType updateOpType;

    @Schema(description = "操作人ID")
    @NotNull(message = "updateOpUid 不能为 null")
    private Long updateOpUid;

    @Schema(description = "操作人名字")
    @NotNull(message = "updateOpName 不能为 null")
    @Size(max = 32, message = "updateOpName 长度不能超过 32")
    private String updateOpName;

    @NotNull(message = "createTime 不能为 null")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Schema(description = "运维工单类型 1：光； 2：储； 3：充；")
    private Integer ywType;

    @Schema(description = "质检分数")
    private Long score;

    @Schema(description = "考核标签(1-考核;0-不考核)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private YwOrderTag tag;

    @Schema(description = "备注")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String remark;
}

