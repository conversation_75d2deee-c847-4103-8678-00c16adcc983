package com.cdz360.biz.model.trading.invoice.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "企业开票回款计划")
public class CorpInvoiceReturnPlanVo {

    @Schema(description = "回款标题")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String planTitle;

    @Schema(description = "回款金额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal planMoney;

    @Schema(description = "回款日期")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date planTime;
}
