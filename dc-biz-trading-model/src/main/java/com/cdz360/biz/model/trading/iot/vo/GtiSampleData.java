package com.cdz360.biz.model.trading.iot.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "逆变器数据采样")
@Data
@Accessors(chain = true)
public class GtiSampleData {

    @Schema(description = "日期 前端根据查询粒度进行显示")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private LocalDateTime time;

    @Schema(description = "总发电量, 单位: kW·h", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal kwh;

    @Schema(description = "收益, 单位: 元", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal profit;

    @Schema(description = "等效时长, 单位: 小时")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal equivalentTime;
}
