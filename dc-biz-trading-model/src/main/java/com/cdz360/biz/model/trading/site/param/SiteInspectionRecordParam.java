package com.cdz360.biz.model.trading.site.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.param.TimeFilter;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SiteInspectionRecordParam extends BaseListParam {

    private String siteId;

    @Schema(description = "巡检人List")
    private List<Long> opUidList;

    @Schema(description = "巡检时间")
    private TimeFilter time;

}
