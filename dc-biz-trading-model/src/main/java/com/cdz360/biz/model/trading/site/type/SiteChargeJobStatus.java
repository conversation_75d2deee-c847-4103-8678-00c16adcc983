package com.cdz360.biz.model.trading.site.type;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum SiteChargeJobStatus {

    VALID(0, "有效"),
    BLOCK_UP(1, "停用"),
    DELETED(2, "删除"),
    EXPIRES(3, "过期");
    @JsonValue
    private Integer code;
    private String value;

    SiteChargeJobStatus(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    @JsonCreator
    public static SiteChargeJobStatus valueOfCode(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (SiteChargeJobStatus type : SiteChargeJobStatus.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
