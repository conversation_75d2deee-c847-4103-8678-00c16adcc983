package com.cdz360.biz.model.trading.order.param;

import java.text.MessageFormat;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ListCusOrderBiParam {

    private List<Long> uidList;

    private String siteId;

    private String commIdChain;

    private List<String> orderNoList;

    private List<Long> corpIdList;

    @Override
    public String toString() {
        return MessageFormat.format("uidList.size: {0}, siteId: {1}, commIdChain: {2}",
                this.uidList.size(), this.siteId, this.commIdChain);
    }

}
