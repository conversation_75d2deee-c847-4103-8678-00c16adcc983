package com.cdz360.biz.model.trading.site.param;

import com.cdz360.biz.model.trading.site.dto.SiteOaDefaultConfigDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "更新场站流程默认值配置项参数")
@Data
@Accessors(chain = true)
public class UpdateSiteOaDefaultValueParam {

    @Schema(description = "场站ID", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_NULL)
    private String siteId;

    @Schema(description = "流程默认值配置")
    @JsonInclude(Include.NON_NULL)
    private List<SiteOaDefaultConfigDto> oaDefaultValueList;
}
