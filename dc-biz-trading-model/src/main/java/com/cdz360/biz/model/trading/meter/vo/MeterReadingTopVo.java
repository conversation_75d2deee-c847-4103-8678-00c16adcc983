package com.cdz360.biz.model.trading.meter.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * MeterReadingTopVo
 *
 * @since 9/28/2020 11:22 AM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MeterReadingTopVo extends MeterEvseVo {

    @Schema(description = "订单电量累计")
    private BigDecimal orderElectricity = BigDecimal.ZERO;

    @Schema(description = "电表电量累计")
    private BigDecimal meterReadingElectricity = BigDecimal.ZERO;

    @Schema(description = "抄表有效日电量累计")
    private BigDecimal meterOrderElectricity;
}