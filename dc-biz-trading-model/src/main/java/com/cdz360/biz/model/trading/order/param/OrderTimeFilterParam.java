package com.cdz360.biz.model.trading.order.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.param.TimeFilter;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * 充电订单时间筛选通用类
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "充电订单时间筛选通用类")
public class OrderTimeFilterParam extends BaseListParam {

    @Schema(description = "订单创建时间筛选")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter chargeCreateTimeFilter;

    @Schema(description = "充电开始时间筛选")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter chargeStartTimeFilter;

    @Schema(description = "充电结束时间筛选")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter chargeStopTimeFilter;

    @Schema(description = "平台（收到）结束充电的时间（即上传时间）")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter stopTimeFilter;

    @Schema(description = "支付时间筛选")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter paymentTimeFilter;

    @Schema(description = "订单最后更新时间筛选（有时把订单最后更新时间作为结算时间）")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter chargeUpdateTimeFilter;

}
