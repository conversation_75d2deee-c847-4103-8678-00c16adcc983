package com.cdz360.biz.model.trading.bi.dto;

import lombok.Getter;

/**
 * 订单导出分组
 */
@Getter
public enum BiExportGroups {
    VINBI(1, "车辆统计"),//位于企业平台
    CORPCUSBI(2, "企业客户报表"),//位于充电平台
    ONLINECARDBI(3, "在线卡报表"),//位于充电平台
    EMERGENCYCARDBI(4, "紧急充电卡报表"),//位于充电平台
    ONLINECARDBIONCORP(5, "在线卡统计"),//位于企业平台
    CORPCREDITCUSBI(6, "授信账户统计"),//位于企业平台
    ORGBI(7, "组织统计"),//位于企业平台

    USERRECHARGEBI(8, "普通客户-充值信息"),
    CORPUSERRECHARGEBI(9, "企业客户-充值信息"),
    FINANCERECHARGEBI(10, "财务-充值管理充值记录"),

    WARNINGBI(11, "告警明细"),

    ZFTCHARGEBI(12, "直付到账-对账账单"),

    SITE_BI(30, "场站-充电汇总统计"),
    VIN_BI(31, "车辆(VIN)-充电汇总统计"),
    COMM_BI(32, "商户-充电汇总统计"),

    OVERTIME_PARK_FEE_ORDER(50, "超停收费充电订单"),
    VIN_ORDER(51, "车辆详情订单信息"),

    ZFT_THIRD_ORDER(56, "对账管理订单"),
    ZFT_THIRD_ORDER_TRADE(57, "直付收款订单"),

    CORP_INVOICE_ORDER(60, "企业开票订单详情"),
    CUS_INVOICE_RECORD(80, "用户开票记录"),
    YW_ORDER(91, "运维工单"),
    INSPECTION_RECORD(92, "巡检记录"),
    COMM_SETT_REC_CONSUME(100, "消费清分订单"),
    COMM_SETT_REC_CHARGE(101, "充值清分订单"),
    COMM_SETT_REC_SERVER(102, "平台服务费订单"),
    PV_ELEC_FEE_PROFIT_TREND(110, "电量收益趋势"),
    SITE_ESS_BI(120, "储能趋势"),
    SIM_LIST(125, "SIM卡列表"),
    PRERUN_LIST(126, "调试工单列表"),
    ROVER_LIST(127, "运营巡查列表"),
    PARTS_LIST(130, "物料列表"),
    OA_LIST(130, "oa列表"),
    TJ_DAILY_CHARGING_DURATION_LIST(140, "日充电时长正常估算"),
    USER_COUPON_LIST(150, "活动券领取用户列表"),
    SITE_METER_DATA_LIST(160, "场站抄表数据列表"),
    ;

    private final Integer code;
    private final String name;

    BiExportGroups(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
