package com.cdz360.biz.model.trading.ess.param;


import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;


@Data

@Accessors(chain = true)

@Schema(description = "储能ESS调频控制配置")

public class EssFmCtrlCfgParam {

    @Schema(description = "调频控制使能")
    private Boolean fmCtrlEnable;

    @Schema(description = "频率启动偏差")
    private Integer freqDeviation;

    @Schema(description = "调节范围,单位: HZ")
    private Integer scope;

    @Schema(description = "调节步长,单位: HZ")
    private Integer stepLength;

    @Schema(description = "充放电功率, 单位: kW")
    private BigDecimal inOutPower;

    @Schema(description = "爬坡时间, 单位: 分钟")
    private Integer climbingTime;

    @Schema(description = "下垂, 单位: %")
    private Integer droop;

    @Schema(description = "死区, 单位: MHZ")
    private Integer deadZone;

    @Schema(description = "负频率响应错误")
    private Integer negFreqRespError;

    @Schema(description = "正频率响应错误")
    private Integer posFreqRespError;

    @Schema(description = "内在的力量死区")
    private Integer innerStrDeadZone;

    @Schema(description = "积累时间, 单位: s")
    private Integer accumTime;

    @Schema(description = "积累门槛, 单位: pu")
    private Integer accumThreshold;

    @Schema(description = "沉降时间, 单位: s")
    private Integer settlingTime;

    @Schema(description = "等待时间, 单位: min")
    private Integer waitingTime;

    @Schema(description = "电源复位坡度, 单位: pu/min")
    private Integer powerResetSlope;

}

