package com.cdz360.biz.model.trading.site.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2019/12/23 19:21
 */
@Data
@Accessors(chain = true)
@Schema(description = "计费模板下发场站信息")
public class PriceSchemeSiteVo {
    @Schema(description = "场站Id")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "场站名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;

    @Schema(description = "计费模板下发状态: 0, 成功; 200(待下发); 300, 下发中; 301, 超时;")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer status;

//    @Schema(description = "计费模板Id")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Long priceSchemeId;

    @Schema(description = "计费模板code值")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String priceSchemeCode;
}
