package com.cdz360.biz.model.trading.camera.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "摄像设备")
public class CameraPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@Schema(description = "门店id")
	@NotNull(message = "cameraSiteId 不能为 null")
	private Long cameraSiteId;

	@Schema(description = "设备ID")
	@Size(max = 64, message = "deviceId 长度不能超过 64")
	private String deviceId;

	@Schema(description = "设备名称")
	@Size(max = 64, message = "deviceName 长度不能超过 64")
	private String deviceName;

	@Schema(description = "设备型号")
	@Size(max = 64, message = "deviceModel 长度不能超过 64")
	private String deviceModel;

	@Schema(description = "设备序列号")
	@Size(max = 64, message = "deviceSerial 长度不能超过 64")
	private String deviceSerial;

	@Schema(description = "通道ID")
	@Size(max = 64, message = "channelId 长度不能超过 64")
	private String channelId;

	@Schema(description = "通道名")
	@Size(max = 64, message = "channelName 长度不能超过 64")
	private String channelName;

	@Schema(description = "通道号")
	private Integer channelNo;

	@Schema(description = "状态，0：离线，1：在线")
	private Integer channelStatus;

	@Schema(description = "通道封面图片URL")
	@Size(max = 255, message = "channelPicUrl 长度不能超过 255")
	private String channelPicUrl;

	@Schema(description = "直播地址")
	@Size(max = 255, message = "liveAddress 长度不能超过 255")
	private String liveAddress;

	@Schema(description = "直播地址高清")
	@Size(max = 255, message = "liveAddressHD 长度不能超过 255")
	private String liveAddressHD;

	@Schema(description = "直播地址(hls格式)")
	@Size(max = 255, message = "hlsLiveAddress 长度不能超过 255")
	private String hlsLiveAddress;

	@Schema(description = "直播地址高清(hls格式)")
	@Size(max = 255, message = "hlsLiveAddressHD 长度不能超过 255")
	private String hlsLiveAddressHD;

	@Schema(description = "直播地址过期时间")
	private Date liveExpireTime;

	@Schema(description = "当前捕获照片地址")
	@Size(max = 255, message = "cameraCaptureUrl 长度不能超过 255")
	private String cameraCaptureUrl;

	@Schema(description = "1有效 0无效")
	@NotNull(message = "enable 不能为 null")
	private Boolean enable;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;

	@Schema(description = "对应的录像机id")
	private Long recorderId;

	@Schema(description = "备注-摄像头序列号")
	private String cameraSerial;

	@Schema(description = "备注-摄像机验证码")
	private String validateCode;

	@Schema(description = "备注-摄像机密码")
	private String password;

	@Schema(description = "摄像头长宽比")
	private String aspectRatio;
}
