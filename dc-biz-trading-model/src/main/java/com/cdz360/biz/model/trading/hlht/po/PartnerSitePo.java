package com.cdz360.biz.model.trading.hlht.po;

import com.cdz360.base.model.charge.type.HlhtType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PartnerSitePo {

    private Long id;

    @Schema(description = "合作方ID")
    private Long pid;

    @Schema(description = "合作方编号")
    private String partnerCode;

    private Long topCommId;

    private Long commId;

    private HlhtType hlhtType;

    private Long siteCommId;

    private String siteId;

    private String siteName;

    @Schema(description = "合作方场站ID")
    private String pSiteId;

    private String cityCode;

    private Boolean enable;

    private Date createTime;

    @Schema(description = "记录最后修改时间")
    private Date updateTime;
}
