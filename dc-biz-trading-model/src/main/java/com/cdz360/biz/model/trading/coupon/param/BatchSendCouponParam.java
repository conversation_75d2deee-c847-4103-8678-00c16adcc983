package com.cdz360.biz.model.trading.coupon.param;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * UpdateActivityParam
 *
 * @since 7/31/2020 10:07 AM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "批量发券")
public class BatchSendCouponParam {
    @Schema(description = "活动ID")
    private Long activityId;

    @Schema(description = "活动ID")
    private List<DictNumParam> dictNum;

    @Schema(description = "手机号列表")
    private List<String> phoneList;
    @Schema(description = "集团商户ID",hidden = true)
    private Long topCommId;

    @Schema(description = "登录商户所属ID",hidden = true)
    private Long commId;
    @Schema(description = "登录账号ID",hidden = true)
    private Long sysUid;
    @Schema(description = "登录账户用户名",hidden = true)
    private String sysUserName;
}