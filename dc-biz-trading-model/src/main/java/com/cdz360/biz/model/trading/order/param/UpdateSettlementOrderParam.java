package com.cdz360.biz.model.trading.order.param;

import com.cdz360.base.model.base.vo.BaseObject;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "更新充电订单的账单编号")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class UpdateSettlementOrderParam extends BaseObject {
    public enum OpType {
        INSERT,
        UPDATE
    }

    @Schema(description = "操作类型")
    private OpType opType;

    @Schema(description = "账单编号")
    private String billNo;

    @Schema(description = "本次操作充电订单号列表")
    private List<String> orderNoList;
}
