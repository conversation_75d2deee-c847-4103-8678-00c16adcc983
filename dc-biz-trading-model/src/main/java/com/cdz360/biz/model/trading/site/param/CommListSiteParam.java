package com.cdz360.biz.model.trading.site.param;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2019/9/19 11:24
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Schema(description = "查询集团商户场站列表参数")
public class CommListSiteParam extends ListSiteParam {
    @Schema(description = "集团商户Id以及子商户Id列表, 不传表示查询所有的.")
    private List<Long> commIdList;

    @Schema(description = "集团商户Id", example = "123")
    private Long topCommId;

    public CommListSiteParam() {
    }

    public CommListSiteParam(ListSiteParam param) {
        this.setFilterIdlePlug(param.getFilterIdlePlug())
                .setGeoNear(param.getGeoNear())
                .setGeoWith(param.getGeoWith())
                .setParkingFeeTypeList(param.getParkingFeeTypeList())
                .setScopeList(param.getScopeList())
                .setStatusList(param.getStatusList())
                .setSupplyTypeList(param.getSupplyTypeList())
                .setEnable(param.getEnable())
                .setSk(param.getSk())
                .setSorts(param.getSorts())
                .setSize(param.getSize())
                .setStart(param.getStart());
    }
}
