package com.cdz360.biz.model.trading.site.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "桩配置信息")
public class BsBoxSettingPo {
    // 主键ID
    @Schema(description = "主键ID")
    private Long id;
    // 桩设备ID
    @Schema(description = "桩设备ID")
    private String boxCode;
    // 设备序列号/桩号
    @Schema(description = "设备序列号/桩号")
    private String boxOutFactoryCode;
    // 计费模板id
    @Schema(description = "计费模板id")
    private Long chargeId;
    // 1下发成功2失败3下发中
    @Schema(description = "1下发成功2失败3下发中")
    private Integer status;
    // 管理员密码
    @Schema(description = "管理员密码")
    private String adminPassword;
    // 二级管理员密码
    @Schema(description = "二级管理员密码")
    private String level2Password;
    // 白天音量
    @Schema(description = "白天音量")
    private Integer dayVolume;
    // 夜晚音量
    @Schema(description = "夜晚音量")
    private Integer nightVolume;
    // 二维码url
    @Schema(description = "二维码url")
    private String url;
    // 是否支持充电记录查询 （1是0否）
    @Schema(description = "是否支持充电记录查询 （1是0否）")
    private Boolean isQueryChargeRecord;
    // 是否支持定时充电 （1是0否）
    @Schema(description = "是否支持定时充电 （1是0否）")
    private Boolean isTimedCharge;
    // 是否支持无卡充电 （1是0否）
    @Schema(description = "是否支持无卡充电 （1是0否）")
    private Boolean isNoCardCharge;
    // 是否支持扫码充电 （1是0否）
    @Schema(description = "是否支持扫码充电 （1是0否）")
    private Boolean isScanCharge;
    // 是否支持Vin码充电 （1是0否）
    @Schema(description = "是否支持Vin码充电 （1是0否）")
    private Boolean isVinCharge;
    // 是否支持刷卡充电 （1是0否）
    @Schema(description = "是否支持刷卡充电 （1是0否）")
    private Boolean isCardCharge;
    // 是否支持定额电量充电 （1是0否）
    @Schema(description = "是否支持定额电量充电 （1是0否）")
    private Boolean isQuotaEleCharge;
    // 是否支持固定金额充电 （1是0否）
    @Schema(description = "是否支持固定金额充电 （1是0否）")
    private Boolean isQuotaMoneyCharge;
    // 是否支持固定时长充电 （1是0否）
    @Schema(description = "是否支持固定时长充电 （1是0否）")
    private Boolean isQuotaTimeCharge;
    // 国际协议
    @Schema(description = "国际协议")
    private String internationalAgreement;
    // 自动停充 （1是0否）
    @Schema(description = "自动停充 （1是0否）")
    private Boolean isAutoStopCharge;
    // 均/轮充设置 0均充 1轮充
    @Schema(description = "均/轮充设置 0均充 1轮充")
    private Integer avgOrTurnCharge;
    // 合充开关 （1开0关）
    @Schema(description = "合充开关 （1开0关）")
    private Boolean isCombineCharge;

    @Schema(description = "是否支持辅电手动切换")
    private Boolean heating;

    @Schema(description = "辅电电压设置")
    private Integer heatingVoltage;

    @Schema(description = "是否支持电池反接检测")
    private Boolean batteryCheck;

    @Schema(description = "是否支持主动安全检测")
    private Boolean securityCheck;

    @Schema(description = "是否支持不拔枪充电")
    private Boolean constantCharge;

    @Schema(description = "是否支持插枪获取VIN")
    private Boolean vinDiscover;

    @Schema(description = "是否可见订单信息隐私")
    private Boolean orderPrivacySetting;

    @Schema(description = "订单账号显示类型")
    private Integer accountDisplayType;

    // 操作人id
    @Schema(description = "操作人id")
    private Long updateByUserid;

    // 创建时间
    @Schema(description = "创建时间")
    private Date createTime;
    // 更新时间
    @Schema(description = "更新时间")
    private Date updateTime;

    // 紧急充电卡下发状态1下发成功2失败3下发中
    @Schema(description = "紧急充电卡下发状态1下发成功2失败3下发中")
    private Long whiteCardsStatus;
    // 紧急充电卡列表，用,分隔
    @Schema(description = "紧急充电卡列表，用,分隔")
    private String whiteCardList;
    // 管理员账号配置结果,0x00: 成功 其他表示失败
    @Schema(description = "管理员账号配置结果,0x00: 成功 其他表示失败")
    private Integer adminCodeResult;
    // 各种开关项配置结果,0x00: 成功 其他表示失败
    @Schema(description = "各种开关项配置结果,0x00: 成功 其他表示失败")
    private Integer triggerResult;
    // 电价配置结果,0x00: 成功 其他表示失败
    @Schema(description = "电价配置结果,0x00: 成功 其他表示失败")
    private Integer chargeResult;
    // 二维码配置结果,0x00: 成功 其他表示失败
    @Schema(description = "二维码配置结果,0x00: 成功 其他表示失败")
    private Integer qrResult;
    // 主模板编号**唯一标识**
    @Schema(description = "主模板编号**唯一标识**")
    private String templateCode;
}
