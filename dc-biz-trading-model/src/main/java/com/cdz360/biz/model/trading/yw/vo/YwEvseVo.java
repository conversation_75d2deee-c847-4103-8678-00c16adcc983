package com.cdz360.biz.model.trading.yw.vo;

import com.cdz360.base.model.base.type.SupplyType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class YwEvseVo {

    private String evseId;

    private String name;

    @Schema(description = "铭牌编号")
    private String physicalNo;

    @Schema(description = "电流类型")
    private SupplyType supply;

    @Schema(description = "型号")
    private String model;

    @Schema(description = "功率")
    private Integer power;

    @Schema(description = "生产日期(出厂日期)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date produceDate;

    @Schema(description = "质保到期日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expireDate;

}
