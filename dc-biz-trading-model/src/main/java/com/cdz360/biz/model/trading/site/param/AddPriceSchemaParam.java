package com.cdz360.biz.model.trading.site.param;

import com.cdz360.biz.model.iot.vo.ChargeV2;
import com.cdz360.biz.model.site.type.PriceTemplateCalcType;
import com.cdz360.biz.model.site.type.TemplateUsage;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
//@ToString(callSuper = true)
//@EqualsAndHashCode(callSuper = true)
@Schema(description = "新增计费模板参数")
public class AddPriceSchemaParam {

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String name;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long topCommId;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commIdChain;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long commId;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "免费充电标识**0-收费 1-免费**", example = "1")
    private Integer freeChargeFlag;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "UNIFY_PRICE-所有时段统一计费 TIME_BASE_PRICE-按不同时段分别计费", example = "21")
    private PriceTemplateCalcType calculateType;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "计费模板分段信息")
    private List<ChargeV2> priceItemList;

    @Schema(description = "创建人账号, 由后端填写", example = "12345", hidden = true)
    private Long creatorUserId;

    @Schema(description = "创建人名称(登陆账号), 由后端填写", example = "xxx账号", hidden = true)
    private String creatorName;

    @Schema(description = "创建人手机号(登陆账号), 由后端填写", example = "13000000000", hidden = true)
    private String creatorPhone;

    @Schema(description = "用途: 1(充电收益计算); 2(光伏/储能使用)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TemplateUsage usage;
}
