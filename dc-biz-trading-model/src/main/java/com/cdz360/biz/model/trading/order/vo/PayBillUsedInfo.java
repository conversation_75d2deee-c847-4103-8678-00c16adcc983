package com.cdz360.biz.model.trading.order.vo;

import com.cdz360.biz.model.finance.type.TaxStatus;
import com.cdz360.biz.model.finance.type.TaxType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 充值记录使用详情
 *
 * <AUTHOR>
 * @since 2019/12/3 19:27
 */
@Data
@Accessors(chain = true)
@ToString
@Schema(description = "充值记录使用信息")
public class PayBillUsedInfo {
    @Schema(description = "充值订单号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String orderId;

    @Schema(description = "发票类型(税种): UNKNOWN(0)-未知," +
            "NORMAL_TAX(2)-个人普通发票,PREPAY_TAX(3)-企业普通发票," +
            "SPECIAL_VAT(5)-企业专业发票", example = "UNKNOWN")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TaxType taxType;

    @Schema(description = "税票号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String taxNo;

    @Schema(description = "开票状态", example = "NO")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TaxStatus taxStatus;

    @Schema(description = "充值金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal amount;

    @Schema(description = "赠送金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal freeAmount;

    // 可用
    @Schema(description = "实际可用金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal costAvailable;

    // 可用
    @Schema(description = "可用金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal available;

    // 已结算
    @Schema(description = "已结算金额汇总, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal settlementTotal;

    // 已冻结
    @Schema(description = "已冻结金额汇总, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal frozenTotal;

    // 已开票
    @Schema(description = "已开票金额汇总, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal taxTotal;

    // 不可开票
    @Schema(description = "不可开票金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal notTaxTotal;

    // 未开票
    @Schema(description = "未开票金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal unTaxTotal;
}
