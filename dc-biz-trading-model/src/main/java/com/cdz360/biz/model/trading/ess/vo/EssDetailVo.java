package com.cdz360.biz.model.trading.ess.vo;

import com.cdz360.biz.model.trading.ess.param.AddEssCfgParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


@Schema(description = "配置模板详情")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class EssDetailVo extends AddEssCfgParam {


}
