package com.cdz360.biz.model.trading.rover.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Schema(description = "OA列表导出")
@Data
@Accessors(chain = true)
public class ExportOaVo implements Serializable {

    @ExcelField(title = "申请单号", sort = 1)
    @Schema(description = "申请单号")
    private String processInstanceId;

    @ExcelField(title = "流程类型", sort = 2)
    @Schema(description = "流程类型")
    private String oaName;

    @ExcelField(title = "摘要", sort = 3)
    @Schema(description = "摘要")
    private String summary;

    @ExcelField(title = "提交时间\r\n结束时间", sort = 3)
    @Schema(description = "提交时间  结束时间")
    private String time;

    @ExcelField(title = "提交人", sort = 4)
    @Schema(description = "提交人")
    private String oname;

    @ExcelField(title = "当前节点", sort = 5)
    @Schema(description = "摘要")
    private String name;

    @ExcelField(title = "节点处理人", sort = 6)
    @Schema(description = "节点处理人")
    private String assigneeName;

}