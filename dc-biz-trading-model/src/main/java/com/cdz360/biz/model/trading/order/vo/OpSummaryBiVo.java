package com.cdz360.biz.model.trading.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Schema(description = "运营大屏头部统计数据")
public class OpSummaryBiVo extends ChargeOrderBiVo{



    @Schema(description = "累计上线时间(天)", example = "1234")
    private Long days;
}
