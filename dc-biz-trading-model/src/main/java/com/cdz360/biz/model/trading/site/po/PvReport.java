package com.cdz360.biz.model.trading.site.po;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Schema(description = "光伏常规检查")
public class PvReport {

    @Schema(description = "光伏逆变器是否正常")
    private Base isPvGitNormal;

    @Schema(description = "光伏板是否正常")
    private Base isPvPanelNormal;

    @Data
    @Accessors(chain = true)
    public static class Base {
        private Boolean normal;

        private String remark;
    }
}

