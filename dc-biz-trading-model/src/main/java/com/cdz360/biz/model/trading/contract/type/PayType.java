package com.cdz360.biz.model.trading.contract.type;

import lombok.Getter;

/**
 * 收费方式
 */
@Getter
public enum PayType {

    PAY_P0(0, "其他"),

    PAY_P1(1, "按枪头收费"),

    PAY_P2(2, "按电量收费");

    private final int code;
    private final String desc;

    PayType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PayType valueOf(int code) {
        for (PayType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return PayType.PAY_P0;
    }

}
