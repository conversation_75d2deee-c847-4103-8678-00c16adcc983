package com.cdz360.biz.model.trading.site.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "计费模板定时下发信息")
public class EvseCfgSchedulePo {

    private Long id;

    @Schema(description = "桩号")
    private String evseNo;

    @Schema(description = "计费模板定时下发时间", example = "123")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date scheduleTime;

    @Schema(description = "使用的计费模板Id", example = "123")
    private Long priceSchemeId;

    @Schema(description = "场站默认: true -- 场站默认; false -- 不是场站默认")
    private Boolean siteDefault;

    @Schema(description = "操作人id", example = "123")
    private Long opUid;

    @Schema(description = "是否有效", example = "true")
    private Boolean enable;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @Schema(description = "场站Id")
    private String siteId;

    @Schema(description = "流程Id")
    private String processInstanceId;

}
