package com.cdz360.biz.model.trading.site.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * BiSiteGcMonthExpenseStatisticVo
 * 
 * @since 5/11/2023 2:56 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "运营场站月支出")
public class BiSiteGcMonthExpenseStatisticVo {
    @Schema(description = "类型")
    private String type;

    @Schema(description = "小类")
    private String subType;

    @Schema(description = "金额")
    private BigDecimal fee;

}