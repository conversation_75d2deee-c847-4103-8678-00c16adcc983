package com.cdz360.biz.model.trading.iot.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListWhiteCardEvseParam extends BaseListParam {

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String evseNo;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String whiteCardNo;

    //private Integer sendStatus;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Integer> sendStatusList;


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
