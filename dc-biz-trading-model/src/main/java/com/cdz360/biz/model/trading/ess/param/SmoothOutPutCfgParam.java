package com.cdz360.biz.model.trading.ess.param;


import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;


@Data

@Accessors(chain = true)

@Schema(description = "储能模板配置-平滑输出")

public class SmoothOutPutCfgParam {


    @Schema(description = "平滑输出使能")
    private Boolean smoothOutputEnable;

    @Schema(description = "监测周期")
    private Long monitoringPeriod;

    @Schema(description = "变化幅值")
    private Long amplitude;

    @Schema(description = "目标额定功率")
    private BigDecimal targetPowerRating;
}

