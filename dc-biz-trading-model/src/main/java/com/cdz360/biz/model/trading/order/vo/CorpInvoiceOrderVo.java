package com.cdz360.biz.model.trading.order.vo;

import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.biz.model.finance.type.TaxStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "企业客户开票充电订单信息")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CorpInvoiceOrderVo extends BaseObject implements Serializable {

    @Schema(description = "充电订单号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String orderNo;

    /**
     * 状态,-500：硬件故障；-40：上报超时；-35：断连超时；-30关电超时；-29：结束充电失败；-20：链接超时；-10：打开电闸超时；
     * -7：开启充电失败，PIN可能错误；-5:系统繁忙；0：订单未激活；100：电闸打开；200：充电中；250：关电闸，充电停止；
     * 300：充电完成；800：订单结束；1000：用户已支付；2000：钱已到账
     */
    @Schema(description = "订单状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer status;

    @Schema(description = "手机号码")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String mobilePhone;

    @Schema(description = "站点名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String stationName;

    @Schema(description = "可开票金额: 单位，元")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal invoiceAmount;

    @Schema(description = "订单金额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal orderPrice;

    @Schema(description = "本金(实际收入): 单位,元")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal principalAmount;

    @Schema(description = "赠送金: 单位,元")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal freeGoldAmount;

    @Schema(description = "已开票金额: 单位，元")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal invoicedAmount;

    @Schema(description = "开票状态", example = "NO")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TaxStatus taxStatus;

    @Schema(description = "订单创建时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date createTime;

    @Schema(description = "订单支付时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date payTime;

    @Schema(description = "充电开始时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long chargeStartTime;

    @Schema(description = "充电结束时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long chargeEndTime;

    @Schema(description = "订单上传时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date stopTime;

    @Schema(description = "车牌号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String carNo;

    @Schema(description = "车队")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String carDepart;

    @Schema(description = "车辆线路")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String lineNum;

    @Schema(description = "车辆自编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String carNum;
}
