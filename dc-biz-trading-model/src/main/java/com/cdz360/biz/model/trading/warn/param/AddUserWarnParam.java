package com.cdz360.biz.model.trading.warn.param;


import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 添加关注
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "添加关注")
public class AddUserWarnParam {
    /**
     * 用户ID
     */
    private Long sysUid;

    /**
     * 订阅场站
     */
    private List<String> siteIdList;

    /**
     * 订阅告警码
     */
    private List<String> codeList;

}
