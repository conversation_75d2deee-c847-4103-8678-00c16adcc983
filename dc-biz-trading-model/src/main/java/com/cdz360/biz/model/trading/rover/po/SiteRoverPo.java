package com.cdz360.biz.model.trading.rover.po;

import com.cdz360.biz.model.trading.rover.type.RoverStatusType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "运营巡查记录")
public class SiteRoverPo {

    @NotNull(message = "id 不能为 null")
    private Long id;

    @ApiModelProperty(value = "巡查单号")
    @NotNull(message = "no 不能为 null")
    @Size(max = 32, message = "no 长度不能超过 32")
    private String no;

    @NotNull(message = "siteId 不能为 null")
    @Size(max = 32, message = "siteId 长度不能超过 32")
    private String siteId;

    @ApiModelProperty(value = "是否删除")
    private Boolean enable;

    @ApiModelProperty(value = "状态:待处理,待评分,已完结,已取消")
    private RoverStatusType status;

    @ApiModelProperty(value = "巡查人uid")
    private Long roverUid;

    @ApiModelProperty(value = "巡查人姓名")
    @Size(max = 32, message = "roverName 长度不能超过 32")
    private String roverName;

    @ApiModelProperty(value = "巡查时间,巡查单提交时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date roverTime;

    @ApiModelProperty(value = "评分人")
    private Long raterUid;

    @ApiModelProperty(value = "评分人姓名")
    @Size(max = 32, message = "raterName 长度不能超过 32")
    private String raterName;

    @ApiModelProperty(value = "分值")
    private Integer rank;

    @ApiModelProperty(value = "分值备注")
    @Size(max = 32, message = "rankComment 长度不能超过 255")
    private String rankComment;

    @ApiModelProperty(value = "备注")
    @Size(max = 32, message = "comment 长度不能超过 255")
    private String comment;

    @ApiModelProperty(value = "取消人uid")
    private Long cancellerUid;

    @ApiModelProperty(value = "取消人姓名")
    @Size(max = 32, message = "roverName 长度不能超过 32")
    private String cancellerName;

    @ApiModelProperty(value = "取消时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date cancelTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date updateTime;


}
