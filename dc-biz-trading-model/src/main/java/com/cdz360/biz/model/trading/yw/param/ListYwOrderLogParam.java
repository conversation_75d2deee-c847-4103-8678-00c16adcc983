package com.cdz360.biz.model.trading.yw.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "获取运维工单操作流水列表参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListYwOrderLogParam extends BaseListParam {

    @Schema(description = "运维工单编号列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> ywOrderNoList;

    /**
     * {@link com.cdz360.biz.model.trading.yw.type.YwOrderOpType}
     */
    @Schema(description = "操作类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Integer> typeList;
}
