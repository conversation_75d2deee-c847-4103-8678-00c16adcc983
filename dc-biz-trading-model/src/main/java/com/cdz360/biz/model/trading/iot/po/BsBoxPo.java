package com.cdz360.biz.model.trading.iot.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class BsBoxPo {

    @Schema(description = "桩编号")
    private String evseNo;

    @Schema(description = "桩名称")
    private String evseName;

    @Schema(description = "场站编号")
    private String siteId;

    @Schema(description = "是否关联场站默认计费(1：是 0：否)")
    private Integer isAssociateSiteTemplate;

    @Schema(description = "是否使用场站默认设置", example = "true")
    private Boolean useSiteSetting;

    @Schema(description = "桩额定功率", example = "123")
    private Integer power;

    @Schema(description = "价格模板编号", example = "123")
    private Long priceCode;

    /**
     * 代理商ID
     */
    private String businessId;

    private Integer plugNum;

    /**
     * 交直流类型**0-交流 1-直流 2-交直流**
     */
    private Integer currentType;

    /**
     * 桩的额定功率
     */
    private Integer ratedPower;

    private Date updateTime;

    /**
     * 电桩型号, 如: G4-001
     */
    private String modelName;
    /**
     *     桩固件(软件)版本
     */
    private String firmwareVer;
}
