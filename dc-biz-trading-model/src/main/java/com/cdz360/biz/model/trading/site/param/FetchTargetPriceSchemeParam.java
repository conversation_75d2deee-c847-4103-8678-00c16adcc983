package com.cdz360.biz.model.trading.site.param;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.trading.site.type.TargetPriceSchemeType;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "获取目标设置价格模板信息参数")
@Data
@Accessors(chain = true)
public class FetchTargetPriceSchemeParam {

    @Schema(description = "查询目标类型", requiredMode = RequiredMode.REQUIRED)
    private TargetPriceSchemeType targetType;

    @Schema(description = "目标ID列表(充电桩类型则等价evseNoList;场站类型则等价siteIdList)")
    private List<String> noList;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
