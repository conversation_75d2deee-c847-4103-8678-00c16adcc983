package com.cdz360.biz.model.trading.site.vo;

import com.cdz360.biz.model.trading.site.type.SiteOrderAccountType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "场站各账户统计数据")
@Data
@Accessors(chain = true)
public class SiteOrderAccountBi {

    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "堆叠图横坐标数据点")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> timeList;

    @Schema(description = "各账户数据")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Map<SiteOrderAccountType, List<SiteOrderAccountData>> accData;
}
