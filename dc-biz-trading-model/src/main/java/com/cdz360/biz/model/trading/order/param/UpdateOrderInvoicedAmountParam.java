package com.cdz360.biz.model.trading.order.param;

import com.cdz360.base.model.base.vo.BaseObject;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "充电订单上已开发票金额操作参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class UpdateOrderInvoicedAmountParam extends BaseObject {
    public enum OpType {
        ADD, // 增加
        SUB  // 减少
    }

    @Schema(description = "操作类型")
    private OpType opType;

    @Schema(description = "充电订单号")
    private String orderNo;

    @Schema(description = "操作前的已开票金额")
    private BigDecimal beforeInvoicedAmount;

    @Schema(description = "操作金额, 单位: 元")
    private BigDecimal amount;

    @Schema(description = "开票记录ID")
    private Long invoiceId;
}
