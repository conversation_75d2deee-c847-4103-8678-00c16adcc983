package com.cdz360.biz.model.trading.meter.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "电表当日读数")
public class BiMeterPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@NotNull(message = "date 不能为 null")
	private Date date;

	@NotNull(message = "siteId 不能为 null")
	@Size(max = 32, message = "siteId 长度不能超过 32")
	private String siteId;

	@NotNull(message = "meterId 不能为 null")
	private Long meterId;

	@NotNull(message = "electricity 不能为 null")
	@Schema(description = "取正向有功")
	private BigDecimal electricity;

	@NotNull(message = "createTime 不能为 null")
	private Date createTime;

	@NotNull(message = "updateTime 不能为 null")
	private Date updateTime;


}
