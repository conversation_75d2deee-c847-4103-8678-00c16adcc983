package com.cdz360.biz.model.trading.prerun.po;

import com.cdz360.biz.model.trading.prerun.type.ChargeType;
import com.cdz360.biz.model.trading.prerun.type.PrerunStatusType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel(value = "调试工单")
public class PrerunPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@ApiModelProperty(value = "调试工单类型:")
	private Integer category;

	@NotNull(message = "prerunNo 不能为 null")
	@Size(max = 32, message = "prerunNo 长度不能超过 32")
	private String prerunNo;

	@NotNull(message = "siteId 不能为 null")
	@Size(max = 32, message = "siteId 长度不能超过 32")
	private String siteId;

	@ApiModelProperty(value = "工单状态: 待处理、待质检、已完成、不合格、已取消")
	private PrerunStatusType status;

	@ApiModelProperty(value = "是否删除")
	private Boolean enable;

	@ApiModelProperty(value = "开通调试人uid")
	private Long prerunnerUid;

	@ApiModelProperty(value = "开通调试人姓名")
	@Size(max = 32, message = "deliverName 长度不能超过 32")
	private String prerunnerName;

	@ApiModelProperty(value = "开通调试日期")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date prerunTime;

	@ApiModelProperty(value = "交付人uid")
	private Long deliverUid;

	@ApiModelProperty(value = "交付人姓名")
	@Size(max = 32, message = "deliverName 长度不能超过 32")
	private String deliverName;

	@ApiModelProperty(value = "交付人电话")
	@Size(max = 255, message = "deliverPhone 长度不能超过 255")
	private String deliverPhone;

	@ApiModelProperty(value = "桩群信息表提供人uid")
	private Long infoSupplyUid;

	@ApiModelProperty(value = "桩群信息表提供人姓名")
	@Size(max = 32, message = "infoSupplyName 长度不能超过 32")
	private String infoSupplyName;

	@ApiModelProperty(value = "桩群信息表提供人电话")
	@Size(max = 255, message = "infoSupplyPhone 长度不能超过 255")
	private String infoSupplyPhone;

	@ApiModelProperty(value = "客户联系人")
	@Size(max = 32, message = "clientName 长度不能超过 32")
	private String clientName;

	@Size(max = 255, message = "clientPhone 长度不能超过 255")
	private String clientPhone;

	@ApiModelProperty(value = "站点负责人")
	@Size(max = 32, message = "masterName 长度不能超过 32")
	private String masterName;

	@ApiModelProperty(value = "站点负责人电话")
	@Size(max = 255, message = "masterPhone 长度不能超过 255")
	private String masterPhone;

	@ApiModelProperty(value = "销售uid")
	private Long salesUid;

	@ApiModelProperty(value = "销售姓名")
	@Size(max = 32, message = "salesName 长度不能超过 32")
	private String salesName;

	@ApiModelProperty(value = "销售电话")
	@Size(max = 255, message = "salesPhone 长度不能超过 255")
	private String salesPhone;

	@ApiModelProperty(value = "运维uid")
	private Long maintainUid;

	@ApiModelProperty(value = "运维姓名")
	@Size(max = 32, message = "maintainName 长度不能超过 32")
	private String maintainName;

	@ApiModelProperty(value = "运维电话")
	@Size(max = 255, message = "maintainPhone 长度不能超过 255")
	private String maintainPhone;

	@ApiModelProperty(value = "是否接入平台")
	private Boolean platformConn;

	@ApiModelProperty(value = "充电方式数组: VIN, CARD, QRCODE")
	private List<ChargeType> chargeTypes;

	@ApiModelProperty(value = "客户是否满意,0-默认项，1-满意,2-不满意")
	private Integer clientRank;

	@ApiModelProperty(value = "客户意见")
	@Size(max = 255, message = "clientComment 长度不能超过 255")
	private String clientComment;

	@ApiModelProperty(value = "客户签名图片地址")
	@Size(max = 255, message = "clientSignPic 长度不能超过 255")
	private String clientSignPic;

	@ApiModelProperty(value = "现场图片列表")
	private List<String> scenePic;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;


}
