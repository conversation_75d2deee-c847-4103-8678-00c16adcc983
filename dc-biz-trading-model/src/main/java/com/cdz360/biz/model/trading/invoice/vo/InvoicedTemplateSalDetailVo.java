package com.cdz360.biz.model.trading.invoice.vo;

import com.cdz360.biz.model.trading.invoice.dto.InvoicedTemplateSalDetailDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * InvoicedTemplateSalDetailVo
 *
 * @since 3/28/2023 2:22 PM
 * <AUTHOR>
 */
@Schema(description = "商品行信息")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class InvoicedTemplateSalDetailVo extends InvoicedTemplateSalDetailDTO {

    @Schema(description = "开票金额 中间计算值")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal amount;

    @Schema(description = "修正金额 中间计算值")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal fixAmount;
    public InvoicedTemplateSalDetailDTO toDTO() {
        // 调整一些字段信息
        return this;
    }
}