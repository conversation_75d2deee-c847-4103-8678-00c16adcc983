package com.cdz360.biz.model.trading.site.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

/**
 * SiteLimitSocPo
 *
 * @since 5/21/2020 3:00 PM
 * <AUTHOR>
 */
@Data
public class SiteLimitSocPo {
    private Long id;
    @Schema(description = "场站id")
    private String siteId;
    @Schema(description = "企业Id")
    private Long corpId;
    @Schema(description = "限制的最大SOC, 0表示禁止充电")
    private Integer soc;
    @Schema(description = "创建时间")
    private Date createTime;
}