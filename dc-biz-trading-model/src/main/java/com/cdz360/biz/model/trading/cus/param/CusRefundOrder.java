package com.cdz360.biz.model.trading.cus.param;

import com.cdz360.base.model.base.vo.BaseObject;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * CusRefundOrder
 *
 * @since 7/9/2021 4:04 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Schema(description = "指定退款的充值单")
public class CusRefundOrder extends BaseObject {
    @Schema(description = "充值号")
    private String orderId;

    @Schema(description = "退款号")
    private String refundOrderId;

    @Schema(description = "退款实际金额")
    private BigDecimal amount;

    @Schema(description = "退款赠送金额")
    private BigDecimal freeAmount;

    // 给充值记录使用的2个字段
    private Long opUid;
    private String opName;
}