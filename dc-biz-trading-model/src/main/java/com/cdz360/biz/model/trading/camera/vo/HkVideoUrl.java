package com.cdz360.biz.model.trading.camera.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * HkVideoUrl
 *
 * @since 7/29/2021 4:48 PM
 * <AUTHOR>
 */
@Schema(description = "设备回放地址")
@Data
@Accessors(chain = true)
public class HkVideoUrl {
    private String url;
    private String id;
    @JsonFormat(/*shape = JsonFormat.Shape.NUMBER, */pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expireTime;
}