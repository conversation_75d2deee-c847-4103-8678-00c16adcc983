package com.cdz360.biz.model.trading.iot.vo;

import com.cdz360.base.model.base.type.SupplyType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "按电流类型统计桩/枪数量")
public class PlugSupplyBiVo {
    @Schema(description = "电流类型")
    private SupplyType supply;


    @Schema(description = "桩数量", example = "123")
    private Long evseNum;

    @Schema(description = "枪头数量", example = "123")
    private Long plugNum;
}
