package com.cdz360.biz.model.trading.order.vo;

import com.cdz360.base.model.base.type.PayChannel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(title = "充电订单创建结果")
public class AddChargeOrderResult {

    @Schema(title = "支付方式")
    private PayChannel payChannel;

    @Schema(title = "订单号")
    private String orderNo;

    @Schema(title = "微信/支付宝我方支付单号. ")
    private String tradeNo;

    @Schema(title = "微信/支付宝对方支付单号. ")
    private String thirdPayOrderNo;

    @Schema(title = "时间戳. 仅用于即充即退支付")
    private String timeStamp;

    @Schema(title = "随机字符串. 仅用于即充即退支付")
    private String nonce;

    @Schema(title = "支付请求报文. 仅用于即充即退支付",
        description = "微信支付的package部分; 支付宝支付的orderStr部分; 农行支付URL")
    private String payReqMsg;

    @Schema(title = "签名方式. MD5")
    private String paySignType;

    @Schema(title = "签名")
    private String paySign;

    @Schema(title = "微信子商户appId")
    private String appId;

    @Schema(title = "商户号")
    private String mchId;//商户号

    @Schema(title = "预会话Id")
    private String prepayId;//预会话Id

    @Schema(title = "信用充服务ID")
    private String payServiceId;

    @Schema(title = "微信分跳转类型")
    private String businessType;    // 跳转类型, 微信分使用，固定为 wxpayScoreDetail

    @Schema(title = "是否需要确认", description = "true为确认模式,确认模式不能直接启动充电; false为免确认模式,可直接开启充电")
    private Boolean needUserConfirm;

    @Schema(title = "h5充电url", description = "")
    private String mweb_url;
}
