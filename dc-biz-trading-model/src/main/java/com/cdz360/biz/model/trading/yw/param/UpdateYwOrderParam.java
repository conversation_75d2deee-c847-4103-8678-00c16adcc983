package com.cdz360.biz.model.trading.yw.param;

import com.cdz360.biz.model.trading.yw.type.YwOrderTag;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "更新运维工单参数")
@Data
@Accessors(chain = true)
public class UpdateYwOrderParam {

    @Schema(description = "运维工单编号", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String ywOrderNo;

    @Schema(description = "考核标签(1-考核;0-不考核)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private YwOrderTag tag;

    @Schema(description = "备注")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String remark;

}
