package com.cdz360.biz.model.trading.coupon.vo;

import com.cdz360.biz.model.trading.coupon.po.ChargePo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * CouponVo
 *
 * @since 7/31/2020 5:10 PM
 * <AUTHOR>
 */

@Data
@Accessors(chain = true)

@Schema(description = "活动折扣信息")
public class DiscountVo  {

    @Schema(description = "活动ID")
    private Long activityId;

//    @Schema(description = "充金额")
//    private BigDecimal chargeAmount;

    @Schema(description = "送金额")
    private BigDecimal discountAmount;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date timeFrom;

    @Schema(description = "开始结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date timeTo;

    @Schema(description = "备注")
    private String comment;

    @Schema(description = "满赠活动信息")
    private List<ChargePo> chargeList;
}