package com.cdz360.biz.model.trading.site.vo;

import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.biz.model.annotations.ExcelField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "超停收费订单信息")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class OvertimeParkFeeOrderVo extends BaseObject
    implements Serializable {

    @ExcelField(title = "订单号", sort = 1)
    @Schema(description = "充电订单号")
    @NotNull(message = "orderNo 不能为 null")
    @Size(max = 64, message = "orderNo 长度不能超过 64")
    private String orderNo;

    @ExcelField(title = "互联互通订单号", sort = 5)
    @Schema(description = "互联互通订单号")
    @NotNull(message = "hlhtOrderNo 不能为 null")
    @Size(max = 64, message = "hlhtOrderNo 长度不能超过 64")
    private String hlhtOrderNo;


    @ExcelField(title = "客户ID", sort = 9)
    @Schema(description = "t_user.id")
    @NotNull(message = "uid 不能为 null")
    private Long uid;


    @Schema(description = "站点编号")
    @NotNull(message = "siteId 不能为 null")
    @Size(max = 32, message = "siteId 长度不能超过 32")
    private String siteId;


    @ExcelField(title = "限免时长 (分钟)", sort = 25)
    @Schema(description = "限免时长, 单位: 分钟")
    @NotNull(message = "freeTime 不能为 null")
    private Integer freeTime;


    @ExcelField(title = "充电结束时间", sort = 20, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "平台收到充电完成的时间")
    @NotNull(message = "stopTime 不能为 null")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date stopTime;


//    @ExcelField(title = "计费标准 (元/分钟)", sort = 36)
    @Schema(description = "超时收费单价, 单位: 元/分钟")
    @NotNull(message = "parkingPrice 不能为 null")
    private BigDecimal parkingPrice;


    @ExcelField(title = "计费标准", sort = 37)
    @Schema(description = "超时收费单价")
    @NotNull(message = "overtimePriceComment 不能为 null")
    private String overtimePriceComment;


    @ExcelField(title = "停充超时费 (元)", sort = 40)
    @Schema(description = "超时费, 单位: 元")
    @NotNull(message = "parkingFee 不能为 null")
    private BigDecimal parkingFee;


    @ExcelField(title = "计费开始时间", sort = 29, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "计费开始时长")
    @NotNull(message = "calFromTime 不能为 null")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date calFromTime;


    @ExcelField(title = "计费结束时间", sort = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "计费结束时长")
    @NotNull(message = "calToTime 不能为 null")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date calToTime;

    @ExcelField(title = "支付时间", sort = 31, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date payTime;

    @Schema(description = "取消状态：100已取消")
    @NotNull(message = "cancelStatus 不能为 null")
    private Integer cancelStatus;

    private Long status;

    ///////////////// 下面是额外字段 //////////////////////

    @ExcelField(title = "计费时长 (分钟)", sort = 35)
    @Schema(description = "计费时长, 单位: 分钟")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long distanceTime;

    ////////////////////////// 用户信息 ////////////////////////////
    @ExcelField(title = "客户名称", sort = 16)
    @Schema(description = "用户名称 t_user.username")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String username;

    @Schema(description = "用户手机号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String phone;

    /**
     * 付款账户类型 1个人现金；2集团授信；3商户会员
     */
    @Schema(description = "付款账户类型 1个人现金；2集团授信；3商户会员")
    private Integer defaultPayType;

    /**
     * 付款账户名称
     */
    @Schema(description = "付款账户名称")
    private String payAccountName;

    @Schema(description = "是否存在占位费分时信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean hasOvertimeDivision;
}
