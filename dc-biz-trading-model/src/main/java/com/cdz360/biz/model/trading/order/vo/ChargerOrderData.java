package com.cdz360.biz.model.trading.order.vo;

import com.cdz360.biz.model.trading.order.po.ChargerOrderCarPo;
import com.cdz360.biz.model.trading.order.po.ChargerOrderPayPo;
import com.cdz360.biz.model.trading.order.po.ChargerOrderPo;
import com.cdz360.biz.model.trading.site.po.OvertimeParkFeeOrderPo;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 充电订单封装类
 */
@Data
@Accessors(chain = true)
public class ChargerOrderData {

    private ChargerOrderPo order;
    private ChargerOrderCarPo car;
    private ChargerOrderPayPo pay;

    private OvertimeParkFeeOrderPo parkOrder;


    public ChargerOrderData() {

    }

    public ChargerOrderData(ChargerOrderPo order, ChargerOrderCarPo car, ChargerOrderPayPo pay) {
        this.order = order;
        this.car = car;
        this.pay = pay;
    }
}
