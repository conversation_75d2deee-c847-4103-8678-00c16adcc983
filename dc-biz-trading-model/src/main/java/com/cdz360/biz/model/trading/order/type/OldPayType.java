package com.cdz360.biz.model.trading.order.type;

import lombok.Getter;

/**
 * 充值记录的支付类型：1,订单支付。2,余额充值。3,保证金支付,4 保证金提现 5商户会员充值 6即充即退订单充值
 *
 * <AUTHOR>
 * @since 2019/11/11 20:01
 */
@Getter
public enum OldPayType {
    UNKNOWN(0, "未知"),
    ORDER_PAY(1, "订单支付"),
    BALANCE_RECHARGE(2, "余额充值"),
    DEPOSIT_PAY(3, "保证金支付"),
    DEPOSIT_WITHDRAW(4, "保证金提现"),
    COMMERCIAL_RECHARGE(5, "商户会员充值"),
    PREPAY_RECHARGE(6, "即充即退订单充值"),
    ORDER_DEBT(7, "订单补缴"),
    OVERTIME_PARK_DEBT(8, "占位费补缴");

    private final int code;
    private final String desc;

    OldPayType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static OldPayType valueOf(int code) {
        for (OldPayType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return OldPayType.UNKNOWN;
    }
}
