package com.cdz360.biz.model.trading.yw.po;


import com.cdz360.base.model.base.type.UserType;
import com.cdz360.biz.model.trading.yw.type.YwOrderStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;



@Data

@Accessors(chain = true)

@Schema(description = "运维工单流转记录")

public class YwOrderLogPo {



	@NotNull(message = "id 不能为 null")

	private Long id;



	@Schema(description = "运维工单编号")

	@NotNull(message = "ywOrderNo 不能为 null")

	@Size(max = 32, message = "ywOrderNo 长度不能超过 32")

	private String ywOrderNo;



	@Schema(description = "工单状态(INIT: 待接收; RECEIVED: " +
			"已接收; PROCESSING: 处理中; TRANSFERRING: 转派中;" +
			" WAIT_CHECK : 待质检; NO_PASS: 不合格; SOLVED: 已完成;)")

	private YwOrderStatus orderStatus;


	/**
	 * {@link com.cdz360.biz.model.trading.yw.type.YwOrderOpType}
	 */
	@Schema(description = "操作类型. 0,未知; ")

	@NotNull(message = "opType 不能为 null")

	private Integer type;


	@Schema(description = "目标人类型. 0,未知; ")

	private UserType targetUserType;



	@Schema(description = "目标人ID")

	private Long targetUid;



	@Schema(description = "目标人名字")

	@Size(max = 32, message = "opName 长度不能超过 32")

	private String targetUserName;



	@Schema(description = "操作人类型. 0,未知; ")

	@NotNull(message = "opType 不能为 null")

	private UserType opUserType;



	@Schema(description = "操作人ID")

	@NotNull(message = "opUid 不能为 null")

	private Long opUid;



	@Schema(description = "操作人名字")

	@NotNull(message = "opName 不能为 null")

	@Size(max = 32, message = "opName 长度不能超过 32")

	private String opUserName;



	@Schema(description = "操作人操作时间")

	@NotNull(message = "opTime 不能为 null")

	private Date opTime;





}

