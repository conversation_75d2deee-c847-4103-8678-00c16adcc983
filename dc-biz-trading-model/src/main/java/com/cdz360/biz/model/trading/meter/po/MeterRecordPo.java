package com.cdz360.biz.model.trading.meter.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "电表原始读数")
public class MeterRecordPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@NotNull(message = "siteId 不能为 null")
	@Size(max = 32, message = "siteId 长度不能超过 32")
	private String siteId;

	@NotNull(message = "meterId 不能为 null")
	private Long meterId;

    @Schema(description = "读表时间，此处精确到小时或半小时")
    private Date readingTime;

	@Schema(description = "总组合有功")
	@NotNull(message = "combineTotal 不能为 null")
	private BigDecimal combineTotal;

	@NotNull(message = "combineA 不能为 null")
	private BigDecimal combineA;

	@NotNull(message = "combineB 不能为 null")
	private BigDecimal combineB;

	@NotNull(message = "combineC 不能为 null")
	private BigDecimal combineC;

	@NotNull(message = "combineD 不能为 null")
	private BigDecimal combineD;

	@Schema(description = "正向总有功")
	@NotNull(message = "positiveTotal 不能为 null")
	private BigDecimal positiveTotal;

	@NotNull(message = "positiveA 不能为 null")
	private BigDecimal positiveA;

	@NotNull(message = "positiveB 不能为 null")
	private BigDecimal positiveB;

	@NotNull(message = "positiveC 不能为 null")
	private BigDecimal positiveC;

	@NotNull(message = "positiveD 不能为 null")
	private BigDecimal positiveD;

	@Schema(description = "反向总有功")
	@NotNull(message = "negativeTotal 不能为 null")
	private BigDecimal negativeTotal;

	@NotNull(message = "negativeA 不能为 null")
	private BigDecimal negativeA;

	@NotNull(message = "negativeB 不能为 null")
	private BigDecimal negativeB;

	@NotNull(message = "negativeC 不能为 null")
	private BigDecimal negativeC;

	@NotNull(message = "negativeD 不能为 null")
	private BigDecimal negativeD;

	@Schema(description = "正向总无功")
	@NotNull(message = "positiveIdleTotal 不能为 null")
	private BigDecimal positiveIdleTotal;

	@NotNull(message = "positiveIdleA 不能为 null")
	private BigDecimal positiveIdleA;

	@NotNull(message = "positiveIdleB 不能为 null")
	private BigDecimal positiveIdleB;

	@NotNull(message = "positiveIdleC 不能为 null")
	private BigDecimal positiveIdleC;

	@NotNull(message = "positiveIdleD 不能为 null")
	private BigDecimal positiveIdleD;

	@Schema(description = "反向总无功")
	@NotNull(message = "negativeIdleTotal 不能为 null")
	private BigDecimal negativeIdleTotal;

	@NotNull(message = "negativeIdleA 不能为 null")
	private BigDecimal negativeIdleA;

	@NotNull(message = "negativeIdleB 不能为 null")
	private BigDecimal negativeIdleB;

	@NotNull(message = "negativeIdleC 不能为 null")
	private BigDecimal negativeIdleC;

	@NotNull(message = "negativeIdleD 不能为 null")
	private BigDecimal negativeIdleD;

	@NotNull(message = "createTime 不能为 null")
	private Date createTime;


}
