package com.cdz360.biz.model.trading.profit.sett.po;

import com.cdz360.biz.model.trading.profit.sett.type.ProfitRuleAccountType;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "充电结算公式")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ChargeOrderRule extends CalculateRuleBase {

    @Schema(description = "启动充电的账户类型", requiredMode = RequiredMode.REQUIRED)
    private ProfitRuleAccountType accountType;

    @Schema(description = "账户ID(可根据账户类型来决定账户ID)", example = "corpId(企业客户)")
    private Long accountId;

    @Schema(description = "账户名称(可根据账户类型来决定账户名称)", example = "corpName(企业客户)")
    private String accountName;
}
