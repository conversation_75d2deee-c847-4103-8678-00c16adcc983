package com.cdz360.biz.model.trading.coupon.vo;

import com.cdz360.biz.model.trading.coupon.po.ActivityPo;
import com.cdz360.biz.model.trading.coupon.type.CouponDictType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * ActivityRunningVo
 *
 * @since 7/26/2023 10:12 AM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ActivityRunningVo extends ActivityPo {
    private String siteId;

    @Schema(description = "使用条件金额")
    private BigDecimal conditionAmount;

    @Schema(description = "减金额")
    private BigDecimal amount;

    @Schema(description = "已领取的用户数目")
    private Integer obtainedUserCount;

    @Schema(description = "活动发券类型")
    private CouponDictType couponType;

    @Schema(description = "是否已获取券")
    private Boolean obtained;

    @Schema(description = "显示的使用条件金额")
    private BigDecimal showConditionAmount;
}