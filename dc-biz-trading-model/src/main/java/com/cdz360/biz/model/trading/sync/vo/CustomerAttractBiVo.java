package com.cdz360.biz.model.trading.sync.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

@Data
@Schema(description = "引流汇总数据")
public class CustomerAttractBiVo {

    @Schema(description = "本月")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private CustomerAttractBiItem currMonth;

    @Schema(description = "上月")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private CustomerAttractBiItem lastMonth;

    @Schema(description = "累积")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private CustomerAttractBiItem total;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long userId;

    @Schema(description = "用户名")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String userName;

    @Schema(description = "用户名")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long corpId;

    @Schema(description = "企客名")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String corpName;

    @Schema(description = "充电电量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal kwh;

    @Schema(description = "注册时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date regTime;

}
