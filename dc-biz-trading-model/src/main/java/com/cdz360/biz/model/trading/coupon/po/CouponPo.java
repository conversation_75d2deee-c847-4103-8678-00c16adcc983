package com.cdz360.biz.model.trading.coupon.po;

import com.cdz360.biz.model.trading.coupon.type.CouponDictType;
import com.cdz360.biz.model.trading.coupon.type.CouponStatusType;
import com.cdz360.biz.model.trading.coupon.type.CouponValidType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "券")
public class CouponPo {

    @NotNull(message = "id 不能为 null")
    private Long id;

    @NotNull(message = "activityId 不能为 null")
    private Long activityId;

    @NotNull(message = "couponDictId 不能为 null")
    private Long couponDictId;

    @NotNull(message = "userId 不能为 null")
    private Long userId;

    private Long topCommId;

    @Size(max = 64, message = "orderNo 长度不能超过 64")
    private String orderNo;

    @Schema(description = "券类型 SERV_FEE_FIX：服务费满减")
    private CouponDictType type;

    @Schema(description = "个人账户可用")
    private Boolean personalEnable;

    @Schema(description = "即充即退可用")
    private Boolean prepayEnable;

    @Schema(description = "商户会员可用")
    private Boolean commEnable;

    @Schema(description = "同时支持微信/支付宝的先充后付")
    private Boolean wxCreditEnable;

    @NotNull(message = "status 不能为 null")
    private CouponStatusType status;

    @Schema(description = "有效期类型, 固定期限 | 自领取时间")
    private CouponValidType validType;

    @Schema(description = "有效期-开始，闭区间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date validTimeFrom;

    @Schema(description = "有效期-截至，开区间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date validTimeTo;

    @Schema(description = "过期日, 自领取时间")
    private Integer validRelateDay;

    @Schema(description = "使用条件金额")
    private BigDecimal conditionAmount;

    @Schema(description = "显示的使用条件金额")
    private BigDecimal showConditionAmount;

    @Schema(description = "减金额")
    private BigDecimal amount;

    @Schema(description = "操作人ID")
    private Long opUid;

    @Schema(description = "操作人")
    private String opName;
    @Schema(description = "后台发券批次")
    private String batch;
    @Schema(description = "规则id")
    private Long ruleId;
    @NotNull(message = "createTime 不能为 null")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date updateTime;


}
