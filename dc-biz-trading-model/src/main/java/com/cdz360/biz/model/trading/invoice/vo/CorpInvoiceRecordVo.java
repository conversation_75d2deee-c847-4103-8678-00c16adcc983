package com.cdz360.biz.model.trading.invoice.vo;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.trading.invoice.dto.CorpInvoiceRecordDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(description = "企业开票记录查看")
public class CorpInvoiceRecordVo extends CorpInvoiceRecordDto {

    @Schema(description = "电子票PDF")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String pdfUrl;

    @Schema(description = "资源链接")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> urlList;

    @Schema(description = "电子票JPG")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String jpgUrl;

    @Schema(description = "电子票JPG")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> jpgUrlList;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "发票代码", example = "150000456123")
    private String invoiceCode;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "发票号码", example = "06350281")
    private String invoiceNumber;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
