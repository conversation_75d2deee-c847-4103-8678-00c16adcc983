package com.cdz360.biz.model.trading.site.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "场站定时任务执行结果")
public class SiteJobChangePo {

    /**
     * 站点保留定时任务
     */
    List<String> enableTask;

    /**
     * 站点删除的任务
     */
    List<String>   unEnableTask;

}
