package com.cdz360.biz.model.trading.iot.vo;

import com.cdz360.biz.model.trading.iot.type.BatteryModel;
import com.cdz360.biz.model.trading.iot.type.EssModel;
import com.cdz360.biz.model.trading.iot.type.PcsModel;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class EssDataInTimeVo extends EquipInfoInTimeVo {

    @Schema(description = "系统SN")
    @JsonProperty("ss")
    private String systemSn;

    @Schema(description = "EMS通讯协议号")
    @JsonProperty("cpv")
    private String communicationProtocolVersion;

    @Schema(description = "EMS硬件版本号")
    @JsonProperty("hvn")
    private String hardwareVersionNum;

    @Schema(description = "EMS软件版本号")
    @JsonProperty("svn")
    private String softwareVersionNum;

    @Schema(description = "EMS在线个数")
    @JsonProperty("eon")
    private Long emsOnlineNum;

    @Schema(description = "PCS在线个数")
    @JsonProperty("pon")
    private Long pcsOnlineNum;

    @Schema(description = "BMS在线个数")
    @JsonProperty("bon")
    private Long bmsOnlineNum;

    @Schema(description = "EMS型号 0. ALPHA_EMS2_5" +
            "1. ALPHA_EMS3_0" +
            "2. HT_EMS1_0")
    @JsonProperty("em")
    private EssModel emsModel;

    @Schema(description = "PCS型号 0：PWS2_30K_E" +
            "1：PWG2_50K_E" +
            "2：PWG2_100K_E" +
            "3：PWS1_50K_E" +
            "4：PWS1_100K_E" +
            "5：PWS1_150K_E" +
            "6：PWS2_50K_E" +
            "7：PWS2_100K_E" +
            "8：PWS1_250K_E" +
            "9：PWS1_250K_4H_E" +
            "10：PWS1_500K_E")
    @JsonProperty("pm")
    private PcsModel pcsModel;

    @Schema(description = "电池型号 1：M48112-S" +
            "2：M38210-S")
    @JsonProperty("bm")
    private BatteryModel batteryModel;

    @Schema(description = "电池装机容量")
    @JsonProperty("ibc")
    private BigDecimal installedBatteryCapacity;

    @Schema(description = "自耗电量")
    @JsonProperty("ice")
    private BigDecimal internallyConsumesElec;

    @Schema(description = "自耗电功率")
    @JsonProperty("icp")
    private BigDecimal internallyConsumesPower;

    @Schema(description = "系统型号")
    @JsonProperty("sm")
    private Integer systemModel;
}
