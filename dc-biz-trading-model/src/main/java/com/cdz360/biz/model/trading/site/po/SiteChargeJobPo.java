package com.cdz360.biz.model.trading.site.po;

import com.cdz360.biz.model.trading.site.type.SiteChargeJobStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "场站定时任务")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SiteChargeJobPo {

    private Long id;

    private String siteId;
    @Schema(description = "任务名称")
    private String name;
    @Schema(description = "关联的枪头个数")
    private Integer plugNum;
    @Schema(description = "创建任务的商户ID")
    private Long commId;
    @Schema(description = "0,未知; 2, 集团授权账户; 3, 商户专属账户")
    private Integer payAccountType;
    @Schema(description = "defaultPayType=2时为t_r_bloc_user.id; defaultPayType=3时为t_comm_cus_ref.id;")
    private Long payAccountId;
    @Schema(description = "操作人ID")
    private Long opId;

    private Date createTime;

    private Date updateTime;
    /**
     * {@link SiteChargeJobStatus}
     */
    @Schema(description = "0,有效; 1,停用; 2,删除; 3,过期")
    private Integer status;

    @Schema(name = "停充SOC", description = "充电订单SOC限制")
    @JsonInclude(Include.NON_NULL)
    private Integer stopSoc;
}
