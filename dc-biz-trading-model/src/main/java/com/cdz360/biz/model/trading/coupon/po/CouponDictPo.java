package com.cdz360.biz.model.trading.coupon.po;

import com.cdz360.biz.model.trading.coupon.type.CouponDictStatusType;
import com.cdz360.biz.model.trading.coupon.type.CouponDictType;
import com.cdz360.biz.model.trading.coupon.type.CouponValidType;
import com.cdz360.biz.model.trading.coupon.type.ExtraScoreSettingType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "券模板")
public class CouponDictPo {

    @NotNull(message = "id 不能为 null")
    private Long id;

    @NotNull(message = "name 不能为 null")
    @Size(max = 255, message = "name 长度不能超过 255")
    private String name;

    @Schema(description = "服务费满减")
    private CouponDictType type;

    @Schema(description = "个人账户可用")
    @NotNull(message = "personalEnable 不能为 null")
    private Boolean personalEnable;

    @Schema(description = "即充即退可用")
    @NotNull(message = "prepayEnable 不能为 null")
    private Boolean prepayEnable;

    @Schema(description = "商户会员")
    @NotNull(message = "commEnable 不能为 null")
    private Boolean commEnable;

    @Schema(description = "同时支持微信/支付宝的先充后付")
    @NotNull(message = "wxCreditEnable 不能为 null")
    private Boolean wxCreditEnable;
    @NotNull(message = "commId 不能为 null")
    private Long commId;

    @NotNull(message = "status 不能为 null")
    @Schema(description = "状态, 可用 | 禁用 | 过期")
    private CouponDictStatusType status;

    @Schema(description = "有效期类型, 固定期限 | 自领取时间")
    private CouponValidType validType;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date validTimeFrom;

    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date validTimeTo;

    @Schema(description = "自领取时间")
    private Integer validRelateDay;

    @Schema(description = "使用条件金额")
    private BigDecimal conditionAmount;

    @Schema(description = "减金额")
    private BigDecimal amount;

    @Schema(description = "显示的使用条件金额")
    private BigDecimal showConditionAmount;

    @NotNull(message = "createTime 不能为 null")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date updateTime;

    @Schema(description = "叠加积分体系的方式")
    private ExtraScoreSettingType scoreSettingType;
}
