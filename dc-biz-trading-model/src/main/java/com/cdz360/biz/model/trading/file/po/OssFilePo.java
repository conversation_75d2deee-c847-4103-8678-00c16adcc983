package com.cdz360.biz.model.trading.file.po;

import com.cdz360.biz.model.trading.file.type.FileType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
/**
 * OSS图片
 */
@Data
@Accessors(chain = true)
@Schema(description = "OSS图片")
public class OssFilePo {

    /**
     * 文件ID
     */
    @Schema(description = "文件ID")
    private Long id;

    /**
     * 文件类型
     */
    @Schema(description = "文件ID")
    private FileType type;

    /**
     * 原文件名称
     */
    @Schema(description = "原文件名称")
    private String realName;

    /**
     * 云文件名称
     */
    @Schema(description = "云文件名称")
    private String fileName;

    /**
     * 文件地址
     */
    @Schema(description = "文件地址")
    private String path;
}
