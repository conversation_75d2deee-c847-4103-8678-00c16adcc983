package com.cdz360.biz.model.trading.ess.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "场站储能趋势图导出 按天")
public class ExportExportEssDataBiDayVo extends ExportEssDataBiVo {

    @ExcelField(title = "日期", sort = 1, dateFormat = "yyyy-MM-dd")
    @Schema(description = "日期")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date time;

    @ExcelField(title = "交流充电量（kW·h）", sort = 2)
    @Schema(description = "总充电电量（kW·h）", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal inKwh;

    @ExcelField(title = "交流放电量（kW·h）", sort = 3)
    @Schema(description = "总放电电量（kW·h）", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal outKwh;

    @ExcelField(title = "储能内部耗电量（kW·h）", sort = 4)
    @Schema(description = "总放电电量（kW·h）", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal internalPower;

    @ExcelField(title = "充电支出（元）", sort = 5)
    @Schema(description = "总充电支出，单位: 元", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal inExpend;

    @ExcelField(title = "放电收入（元）", sort = 6)
    @Schema(description = "总放电收入，单位: 元", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal outIncome;

    @ExcelField(title = "储能收益（元）", sort = 7)
    @Schema(description = "收益", example = "123.4 可为负值")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal profit;

    @Override
    public void setTime(Date time) {
        this.time = time;
    }

    @Override
    public ExportExportEssDataBiDayVo fillNull() {
        if (inKwh == null) {
            inKwh = BigDecimal.ZERO;
        }
        if (outKwh == null) {
            outKwh = BigDecimal.ZERO;
        }
        if (internalPower == null) {
            internalPower = BigDecimal.ZERO;
        }
        if (inExpend == null) {
            inExpend = BigDecimal.ZERO;
        }
        if (outIncome == null) {
            outIncome = BigDecimal.ZERO;
        }
        if (profit == null) {
            profit = BigDecimal.ZERO;
        }
        return this;
    }
}
