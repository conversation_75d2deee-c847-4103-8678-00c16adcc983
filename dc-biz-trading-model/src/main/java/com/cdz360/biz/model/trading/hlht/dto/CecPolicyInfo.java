package com.cdz360.biz.model.trading.hlht.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "计费信息")
public class CecPolicyInfo {

    @JsonProperty(value = "StartTime")
    @Schema(description = "时段起始时间 格式”HHmmss“,6 字符")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String startTime;

    @JsonProperty(value = "ElecPrice")
    @Schema(description = "时段电费 小数点后 4 位")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal elecPrice;

    @JsonProperty(value = "SevicePrice")
    @Schema(description = "时段服务费 小数点后 4 位")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal sevicePrice;
}
