package com.cdz360.biz.model.trading.iot.vo;

import com.cdz360.biz.model.trading.site.vo.TimePowerBiVo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "充电电力统计")
public class EvsePowerBiVo {

    @Schema(description = "总功率", example = "123")
    private Long totalPower;

    @Schema(description = "充电中功率", example = "123")
    private Long busyPower;

    private List<TimePowerBiVo> todayPowerList;

    private List<TimePowerBiVo> yesterdayPowerList;

    private List<TimePowerBiVo> beforeYesterdayPowerList;


}
