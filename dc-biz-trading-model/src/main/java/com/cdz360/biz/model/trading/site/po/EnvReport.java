package com.cdz360.biz.model.trading.site.po;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "环境安全检查项")
public class EnvReport {

    @Schema(description = "电缆沟是否有积水现象，场站地面基础是否有塌陷、下沉、积水情况")
    private Boolean isHydrops;

    @Schema(description = "排水管道井、沟渠有无堵塞")
    private Boolean isPipeBlockage;

    @Schema(description = "场站照明运行是否正常")
    private Boolean isLightingNormal;

    @Schema(description = "消防警示标志是否完整醒目")
    private Boolean isLogoEyeCatching;

    @Schema(description = "灭火器是否在有效期内、月检卡是否正常")
    private Boolean isExtinguisherNormal;

    @Schema(description = "消防沙箱砂体是否干燥、充足")
    private Boolean isSandBoxDry;

    @Schema(description = "监控摄像系统是否安装运行正常")
    private Boolean isMonitorNormal;

    @Schema(description = "移动伸缩门运行是否正常")
    private Boolean isDoorNormal;

    @Schema(description = "道闸是否起降正常")
    private Boolean isSignoNormal;

}
