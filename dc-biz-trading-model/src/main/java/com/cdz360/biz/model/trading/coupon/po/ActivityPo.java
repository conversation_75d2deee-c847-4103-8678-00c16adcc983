package com.cdz360.biz.model.trading.coupon.po;

import com.cdz360.biz.model.trading.coupon.type.ActivityStatusType;
import com.cdz360.biz.model.trading.coupon.type.ActivityType;
import com.cdz360.biz.model.trading.coupon.type.CouponSendType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "活动")
public class ActivityPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

    @NotNull(message = "name 不能为 null")
    @Size(max = 255, message = "name 长度不能超过 255")
    private String name;

	private ActivityType type;

	@Schema(description = "活动链接")
	private String url;

	@Schema(description = "开始时间")
	@NotNull(message = "timeFrom 不能为 null")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	private Date timeFrom;

	@Schema(description = "开始结束")
	@NotNull(message = "timeTo 不能为 null")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	private Date timeTo;

	@NotNull(message = "commId 不能为 null")
	private Long commId;

	@Schema(description = "单次领券数")
	@NotNull(message = "acquireCount 不能为 null")
	private Integer acquireCount;

	@Schema(description = "领券人数")
	@NotNull(message = "quantityAccount 不能为 null")
	private Integer quantityAccount;

	@Schema(description = "已发布|进行中|已停止|已结束")
	private ActivityStatusType status;

	@Schema(description = "备注")
	private String comment;

	@Schema(description = "适用账户")
	private Long accountType;

	@Schema(description = "是否显示到移动端和小程序，目前仅type为FREE_ACQUIRE才需要关注这个字段")
	private Boolean showInMobile;

	@NotNull(message = "createTime 不能为 null")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	private Date createTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	private Date updateTime;

	@Schema(description = "发券规则")
	private CouponSendType sendCouponRule;

}
