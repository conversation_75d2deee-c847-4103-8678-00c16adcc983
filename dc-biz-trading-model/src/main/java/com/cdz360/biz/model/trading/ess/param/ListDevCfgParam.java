package com.cdz360.biz.model.trading.ess.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.biz.model.trading.ess.type.CfgType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "查询光储配置模板列表")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListDevCfgParam extends BaseListParam {


    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "配置模板名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String name;

    @Schema(description = "模板类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private CfgType type;

    @Schema(description = "模板类型列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<CfgType> typeList;

    @Schema(description = "商户链")
    private String commIdChain;

}
