package com.cdz360.biz.model.trading.yw.vo;

import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.biz.model.trading.iot.vo.EvseInfoVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "充电桩详情")
@Data
@Accessors(chain = true)
public class EvseDetailVo {

    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "场站名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;

    @Schema(description = "场站地址(不含省市区信息) t_site: address")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteAddress;

    @Schema(description = "场站性质: 0，未知； 1，投建运营； 2，以租代售； 3，纯租赁； 4，EPC+O; 5, 销售的代收代付； 6，代运营； 7，委托运营")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer siteGcType;

    @Schema(description = "充电站的电流形式: AC(交流); DC(直流) 充电桩类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private SupplyType siteSupplyType;

    @Schema(description = "充电桩列表信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<EvseInfo> evseInfoList;

    @Schema(description = "充电桩信息")
    @Data
    @Accessors(chain = true)
    static class EvseInfo {
        @Schema(description = "桩编号")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String evseNo;

        @Schema(description = "桩名称")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String evseName;

        @Schema(description = "铭牌编号")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String physicalNo;

        @Schema(description = "充电桩型号")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String model;

        @Schema(description = "供电类型 AC(交流);DC(直流);BOTH(交直流一体)")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private SupplyType supplyType;

        @Schema(description = "功率")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Integer power;

        @Schema(description = "出厂日期")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
        private Date produceDate;

        @Schema(description = "桩质保到期时间 未设质保期为空")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
        private Date evseExpireDate;

        @Schema(description = "桩质保期: (质保期内/已过质保期) 未设质保期为空")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String expireDate;

        EvseInfo(Date expireDate) {
            if (expireDate != null) {
                this.evseExpireDate = expireDate;
                this.expireDate = expireDate.getTime() > new Date().getTime() ? "质保期内" : "已过质保期";
            }
        }
    }

    public EvseDetailVo initEvseInfoList(List<EvseInfoVo> evseList) {
        this.evseInfoList = evseList.stream()
            .map(v -> new EvseInfo(v.getExpireDate())
                .setEvseNo(v.getEvseNo())
                .setEvseName(v.getName())
                .setPhysicalNo(v.getPhysicalNo())
                .setModel(v.getModelName())
                .setPower(v.getPower())
                .setProduceDate(v.getProduceDate())
                .setEvseExpireDate(v.getExpireDate())
                .setSupplyType(v.getSupplyType()))
            .collect(Collectors.toList());
        return this;
    }
}
