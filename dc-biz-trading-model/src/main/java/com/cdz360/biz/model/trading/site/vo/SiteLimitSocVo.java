package com.cdz360.biz.model.trading.site.vo;

import com.cdz360.biz.model.trading.site.po.SiteLimitSocPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * SiteLimitSocVo
 *
 * @since 5/26/2020 2:31 PM
 * <AUTHOR>
 */
@Schema(description = "soc限制对象")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SiteLimitSocVo extends SiteLimitSocPo {
    @Schema(description = "企业名")
    private String corpName;
}