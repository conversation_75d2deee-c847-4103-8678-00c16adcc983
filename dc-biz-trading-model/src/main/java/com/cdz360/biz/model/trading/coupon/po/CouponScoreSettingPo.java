package com.cdz360.biz.model.trading.coupon.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

@Data
@Accessors(chain = true)
@Schema(description = "券模板可叠加的部分积分体系列表")
public class CouponScoreSettingPo {

    @NotNull(message = "id 不能为 null")
    private Long id;

    @NotNull(message = "couponDictId 不能为 null")
    private Long couponDictId;

    @NotNull(message = "scoreSettingId 不能为 null")
    private Long scoreSettingId;

    // 由于无法跨库访问，因此这里存储name方便查询
    @NotNull(message = "scoreSettingName 不能为 null")
    private String scoreSettingName;

    @NotNull(message = "createTime 不能为 null")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;


}
