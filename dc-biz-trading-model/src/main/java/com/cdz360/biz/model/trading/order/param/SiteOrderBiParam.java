package com.cdz360.biz.model.trading.order.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
@Data
@Schema(description = "场站充电统计参数")
public class SiteOrderBiParam {

    @Schema(description = "场站ID")
    private String siteId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "统计开始时间(闭区间)", example = "2019-10-01 00:00:00")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "统计结束时间(开区间)", example = "2019-10-02 00:00:00")
    private Date endTime;

}
