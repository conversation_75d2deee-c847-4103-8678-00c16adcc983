package com.cdz360.biz.model.trading.cus.vo;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.biz.model.trading.deposit.vo.DepositSourceType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
@Schema(description = "客户端用户充值记录信息")
public class CusPayBillVo extends BaseObject {
    @Schema(description = "充值订单号")
    private String orderId;

    @Schema(description = "第三方交易号")
    private String tradeNo;

    @Schema(description = "充值实际金额, 单位'元', 2位小数")
    private BigDecimal amount;

    @Schema(description = "充值赠送金额, 单位'元', 2位小数")
    private BigDecimal freeAmount;

    @Schema(description = "充值来源: UNKNOWN, 其他; WX_LITE, 微信小程序;" +
            " ALIPAY_LITE, 支付宝小程序; ANDROID_APP, 安卓APP; IOS_APP, iOS APP;" +
            " MGM_WEB, 平台充值; MERCHANT, 企业充值; HLHT, 三方平台;")
    private DepositSourceType sourceType;

    @Schema(description = "该笔充值记录可用余额(含赠送金额), 单位'元', 2位小数")
    private BigDecimal available;

    @Schema(description = "可退款金额, 单位'元', 2位小数")
    private BigDecimal canRefundAmount;

    @Schema(description = "cus-balance 中对应的资金块Id", hidden = true)
    private Long recId;

    @Schema(description = "充值记录支付方式: UNKNOWN, 未知;" +
            "ALIPAY, 支付宝;" +
            "WXPAY, 微信支付;" +
            "BANK_CARD, 银行卡支付;" +
            "BUSINESS_ACCOUNT, 对公转账;")
    private PayChannel payChannel;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private Integer orderType;
    private Integer payType;
    private Long userId;
    private Long topCommId;
    private Long commId;
    @Schema(description = "充值账户类型. 个人账户/商户会员/企业客户")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private PayAccountType accountType;
}
