package com.cdz360.biz.model.trading.yw.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import com.cdz360.biz.model.trading.yw.type.CreateYwOrderSourceType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "运维工单列表导出数据体")
@Data
@Accessors(chain = true)
public class ExportYwOrderListVo implements Serializable {

    @ExcelField(title = "维修工单号", sort = 1)
    @Schema(description = "维修工单编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String ywOrderNo;

//    @ExcelField(title = "所属商户", sort = 3)
//    @Schema(description = "场站所属商户名称")
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String siteCommName;

    @ExcelField(title = "所属场站组", sort = 3)
    @Schema(description = "场站所属场站组名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteGidName;

    @ExcelField(title = "场站名称", sort = 5)
    @Schema(description = "场站名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;

    @ExcelField(title = "故障桩", sort = 7)
    @Schema(description = "故障桩编号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String evseStr;

    @ExcelField(title = "铭牌编号", sort = 9)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String physicalNo;

    @ExcelField(title = "桩类型", sort = 11)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String supplyStr;

    @ExcelField(title = "桩型号", sort = 13)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String model;

    @ExcelField(title = "桩功率（kW）", sort = 15)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer power;

    @ExcelField(title = "桩出厂日期", sort = 17, dateFormat = "yyyy-MM-dd")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date produceDate;

    @ExcelField(title = "质保期限", sort = 19, dateFormat = "yyyy-MM-dd")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date expireDate;

    @ExcelField(title = "故障描述", sort = 21)
    @Schema(description = "故障描述")
    @Size(max = 400, message = "faultDesc 长度不能超过 400")
    private String faultDesc;

    @ExcelField(title = "处理措施及结果", sort = 21)
    @Schema(description = "处理措施及结果")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dealProcess;

    @ExcelField(title = "是否更换器件", sort = 22)
    @Schema(description = "是否更换器件")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String replacedDevice;

    @ExcelField(title = "器件名称", sort = 22)
    @Schema(description = "器件名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String goodsStr;

    @ExcelField(title = "工单来源", sort = 23,
        convert = "com.cdz360.biz.model.trading.yw.type.CreateYwOrderSourceType")
    @Schema(description = "创建运维工单客户端. UNKNOWN,未知; C端客户(CUSTOMER_SRC);" +
        " 商户(COMM_SRC); 运维(YW_SRC); 客服(KF_SRC); 其他(OTHER_SRC)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private CreateYwOrderSourceType sourceType;

    @ExcelField(title = "创建人", sort = 25)
    @Schema(description = "创建操作人名字")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String createOpName;

    @ExcelField(title = "创建时间", sort = 27, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ExcelField(title = "维修人", sort = 29)
    @Schema(description = "维修人名字")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String maintName;

    @ExcelField(title = "维修时间", sort = 31, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "维修人处理时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date maintTime;

    @ExcelField(title = "维修时长", sort = 33)
    @Schema(description = "维修时长（天-时-分）")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String ywDurationStr;

    @ExcelField(title = "是否远程解决", sort = 35)
    @Schema(description = "是否远程解决")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String remote;

    @ExcelField(title = "状态", sort = 37)
    @Schema(description = "工单状态(INIT: 待接收; RECEIVED: " +
        "已接收; PROCESSING: 处理中; TRANSFERRING: 转派中;" +
        " WAIT_CHECK : 待质检; NO_PASS: 不合格; SOLVED: 已完成;)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String orderStatus;

    @ExcelField(title = "质检人", sort = 39)
    @Schema(description = "质检人名字")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String qcName;

    @ExcelField(title = "质检人备注", sort = 41)
    @Schema(description = "质检人备注")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String qcRemark;

}
