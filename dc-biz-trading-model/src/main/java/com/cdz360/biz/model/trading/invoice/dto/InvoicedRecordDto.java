package com.cdz360.biz.model.trading.invoice.dto;

import com.cdz360.biz.model.invoice.type.InvoiceChannel;
import com.cdz360.biz.model.trading.site.po.InvoicedRecordPo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * InvoicedRecordDto
 *
 * @since 3/31/2023 3:25 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class InvoicedRecordDto extends InvoicedRecordPo {
    @Schema(description = "开具方式(开票渠道): AUTO -- 系统自动; MANUAL -- 后台手动")
    @JsonInclude(Include.NON_EMPTY)
    private InvoiceChannel channel;
}