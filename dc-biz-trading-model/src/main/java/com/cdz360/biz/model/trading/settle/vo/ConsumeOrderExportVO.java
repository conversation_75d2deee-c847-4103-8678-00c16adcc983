package com.cdz360.biz.model.trading.settle.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "商户结算单充电订单导出")
@Data
@Accessors(chain = true)
public class ConsumeOrderExportVO implements Serializable {

    @ExcelField(title = "订单号", sort = 1, groups = {100, 102})
    @Schema(description = "充电订单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderNo;

    @ExcelField(title = "手机号", sort = 3, groups = {100, 102})
    @Schema(description = "手机号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cusPhone;

    @ExcelField(title = "扣款账号", sort = 6, groups = {100, 102})
    @Schema(description = "扣款账号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String accountName;

    @ExcelField(title = "站点名称", sort = 9, groups = {100, 102})
    @Schema(description = "站点名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;

    @ExcelField(title = "所属商户", sort = 12, groups = {100, 102})
    @Schema(description = "所属商户")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commName;

    @ExcelField(title = "创建时间", sort = 15, groups = {100, 102})
    @Schema(description = "创建时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date createTime;

    @ExcelField(title = "上传时间", sort = 18, groups = {100, 102})
    @Schema(description = "上传时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date stopTime;

    @ExcelField(title = "总电量", sort = 21, groups = 100)
    @Schema(description = "总电量")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal totalElec;

    @ExcelField(title = "总电费", sort = 23, groups = {100, 102})
    @Schema(description = "总电费 实际 + 赠送")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal totalElecFee;

    @ExcelField(title = "电费实际金额消费", sort = 26, groups = 100)
    @Schema(description = "elecSettleAmount 实际电费, 单位元, 保留2位小数	")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal elecSettleAmount;
    @ExcelField(title = "电费实消清分比例(%)", sort = 27, groups = 100)
    @Schema(description = "elecShareRate 电费对运营商分成比例,保留两位小数,单位%")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal elecShareRate;
    @ExcelField(title = "电费清分金额", sort = 28, groups = 100)
    @Schema(description = "elecShareRateFee 电费对运营商分成比例费用")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal elecShareRateFee;

    @ExcelField(title = "服务费实际金额消费", sort = 30, groups = 100)
    @Schema(description = "servSettleAmount 实际服务费, 单位元, 保留2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal servSettleAmount;
    @ExcelField(title = "服务费实消清分比例(%)", sort = 31, groups = 100)
    @Schema(description = "servShareRate 服务费对运营商分成比例,保留两位小数,单位%")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal servShareRate;
    @ExcelField(title = "服务费清分金额", sort = 32, groups = 100)
    @Schema(description = "servShareRateFee 服务费对运营商分成比例费用")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal servShareRateFee;

    @ExcelField(title = "订单总金额", sort = 36, groups = {100, 102})
    @Schema(description = "订单总金额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal totalOrderFee;

    @ExcelField(title = "清分后总金额", sort = 38, groups = 100)
    @Schema(description = "清分后总金额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal totalClearFee;

    ////////////////////////////////////////////

    @ExcelField(title = "总服务费", sort = 32, groups = 102)
    @Schema(description = "总服务费 实际 + 赠送")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal totalServFee;

    @ExcelField(title = "总电量", sort = 38, groups = 102)
    @Schema(description = "总电量, 单位: 度 清分规则涉及平台服务费时有值")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal platformSettleElec;
    @ExcelField(title = "电量单价", sort = 40, groups = 102)
    @Schema(description = "平台费用电费单价, 单位: 元/每度电 平台费用(平台服务费)计算使用")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal platformElecPrice;
    @ExcelField(title = "平台服务费", sort = 44, groups = 102)
    @Schema(description = "平台费用(平台服务费)对运营商分成比例费用, 单位: 元 平台费用(平台服务费)计算后的金额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal platformElecPriceFee;
}
