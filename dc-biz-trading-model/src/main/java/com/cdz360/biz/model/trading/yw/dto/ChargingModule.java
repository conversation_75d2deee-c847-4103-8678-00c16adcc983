package com.cdz360.biz.model.trading.yw.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "运维工单使用配件(物品)")
@Data
@Accessors(chain = true)
public class ChargingModule {

        @Schema(description = "序列")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Integer idx;

        @Schema(description = "退回到上一次")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Boolean rollback;

        @Schema(description = "现原材编号")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String newDeviceNo;

        @Schema(description = "旧原材编号")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String oldDeviceNo;

        @Schema(description = "桩编号")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String evseNo;

}
