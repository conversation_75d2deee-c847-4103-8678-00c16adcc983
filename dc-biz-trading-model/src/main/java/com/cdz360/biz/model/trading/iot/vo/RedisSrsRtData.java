package com.cdz360.biz.model.trading.iot.vo;

import com.cdz360.biz.model.trading.iot.dto.SrsRtDataDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

@Data
@EqualsAndHashCode(callSuper = true)
public class RedisSrsRtData extends SrsRtDataDto {

    @Schema(description = "存入Redis时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime time;

    public static RedisSrsRtData convert(SrsRtDataDto data) {
        RedisSrsRtData result = new RedisSrsRtData();
        BeanUtils.copyProperties(data, result);
        result.setTime(LocalDateTime.now());
        return result;
    }
}
