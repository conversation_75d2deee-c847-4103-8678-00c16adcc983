package com.cdz360.biz.model.trading.park.api.dto;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * ParkBaseRes
 *
 * @since 8/16/2022 10:59 AM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ParkBaseRes<T> extends ObjectResponse<T> {
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String seqNo;
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private T data;
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private T data;
}