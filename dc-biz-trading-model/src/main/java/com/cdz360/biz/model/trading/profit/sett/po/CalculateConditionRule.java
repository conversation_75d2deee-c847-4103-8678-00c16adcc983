package com.cdz360.biz.model.trading.profit.sett.po;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "条件计算规则")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CalculateConditionRule extends CalculateRuleBase {

    @Schema(description = "条件SpEL计算规则配置")
    private String condition;

    @Schema(description = "优先级")
    private Integer priority;
}
