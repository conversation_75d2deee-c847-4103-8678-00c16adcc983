package com.cdz360.biz.model.trading.yw.param;

import com.cdz360.base.model.base.type.UserType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "操作人员信息参数")
@Data
@Accessors(chain = true)
public class BaseOpParam {
    @Schema(description = "操作人类型", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private UserType opType;  // UserType

    @Schema(description = "操作人ID", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long opUid;

    @Schema(description = "操作人姓名", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String opName;
}
