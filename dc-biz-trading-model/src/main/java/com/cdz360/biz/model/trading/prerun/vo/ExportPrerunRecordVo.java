package com.cdz360.biz.model.trading.prerun.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ExportPrerunRecordVo
 *
 * @since 6/29/2022 4:56 PM
 * <AUTHOR>
 */
@Schema(description = "调试工单导出")
@Data
@Accessors(chain = true)
public class ExportPrerunRecordVo implements Serializable {

    @ExcelField(title = "开通调试工单号", sort = 1)
    @Schema(description = "开通调试工单号")
    private String prerunNo;

//    @ExcelField(title = "所属商户", sort = 2)
//    @Schema(description = "所属商户")
//    private String commName;

    @ExcelField(title = "所属运维组", sort = 2)
    @Schema(description = "所属运维组名称")
    private String siteGidName;

    @ExcelField(title = "站点名称", sort = 3)
    @Schema(description = "站点名称")
    private String siteName;

    @ExcelField(title = "站点性质", sort = 4)
    @Schema(description = "站点性质")
    private String gcType;

    @ExcelField(title = "桩类型", sort = 5)
    @Schema(description = "桩类型")
    private String supply;

    @ExcelField(title = "铭牌编号", sort = 6)
    @Schema(description = "铭牌编号")
    private String physicalNo;

    @ExcelField(title = "桩型号", sort = 7)
    @Schema(description = "桩型号")
    private String model;

    @ExcelField(title = "桩功率(kW)", sort = 8)
    @Schema(description = "桩功率(kW)")
    private Integer power;

    @ExcelField(title = "桩出厂日期", sort = 9, dateFormat = "yyyy-MM-dd")
    @Schema(description = "桩出厂日期")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date produceDate;

    @ExcelField(title = "质保期限", sort = 10, dateFormat = "yyyy-MM-dd")
    @Schema(description = "质保期限")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date expireDate;

    @ExcelField(title = "是否故障", sort = 11)
    @Schema(description = "是否故障")
    private String fault;

    @ExcelField(title = "故障描述", sort = 12)
    @Schema(description = "故障描述")
    private String faultDesc;

    @ExcelField(title = "解决详情", sort = 13)
    @Schema(description = "解决详情")
    private String faultFix;

    @ExcelField(title = "开通调试人", sort = 14)
    @Schema(description = "开通调试人")
    private String prerunnerName;

    @ExcelField(title = "开通调试日期", sort = 15, dateFormat = "yyyy-MM-dd")
    @Schema(description = "开通调试日期")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date prerunTime;

    @ExcelField(title = "状态", sort = 16)
    @Schema(description = "状态")
    private String status;

    @ExcelField(title = "质检人", sort = 17)
    @Schema(description = "质检人")
    private String checkerName;

    @ExcelField(title = "质检人备注", sort = 18)
    @Schema(description = "质检人备注")
    private String checkerComment;
}