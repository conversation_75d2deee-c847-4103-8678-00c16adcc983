package com.cdz360.biz.model.trading.site.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SitePriceVo  {

    @Schema(description = "场站ID")
    private String siteId;

    @Schema(description = "计费模板ID")
    private Long chargeId;

    @Schema(description = "t_site_template中ID")
    private Long id;

}
