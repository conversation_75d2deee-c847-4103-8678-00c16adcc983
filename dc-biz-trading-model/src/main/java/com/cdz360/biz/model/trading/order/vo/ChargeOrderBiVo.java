package com.cdz360.biz.model.trading.order.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ChargeOrderBiVo {

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String axis;    // x轴坐标名称

    @Schema(description = "累计订单数(单)", example = "1234")
    private Long orderNum;

    @Schema(description = "累计充电量(kWh)", example = "1234")
    private BigDecimal elec;

    @Schema(description = "累计充电金额(元)", example = "1234")
    private BigDecimal fee;

    @Schema(description = "累计服务费收益(元)", example = "1234")
    private BigDecimal servFee;

    @Schema(description = "用户数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long userCount;

    @Schema(description = "今日订单数(单)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long todayOrderNum;

    @Schema(description = "今日充电量(kWh)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal todayElec;

    @Schema(description = "今日充电金额(元)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal todayFee;

    @Schema(description = "今日服务费收益(元)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal todayServFee;

}
