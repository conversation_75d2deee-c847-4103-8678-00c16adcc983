package com.cdz360.biz.model.trading.site.vo;

import com.cdz360.base.model.base.vo.BaseObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.concurrent.TimeUnit;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "站点充电订单数据统计")
public class SiteOrderStatsVo extends BaseObject {
    @Schema(description = "时间单位")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeUnit timeUnit;

    @Schema(description = "时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long time;

    @Schema(description = "充电次数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long orderTime;

    @Schema(description = "充电量，单位: kWh")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal orderKwh;

    @Schema(description = "充电金额，单位: 元")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal orderPrice;
}
