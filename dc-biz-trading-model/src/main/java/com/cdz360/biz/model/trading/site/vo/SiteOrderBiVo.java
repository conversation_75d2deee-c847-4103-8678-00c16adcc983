package com.cdz360.biz.model.trading.site.vo;

import com.cdz360.biz.model.common.vo.BiDaily;
import com.cdz360.biz.model.common.vo.BiHourly;
import com.cdz360.biz.model.common.vo.BiMonthly;
import com.cdz360.biz.model.common.vo.BiWeekly;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import com.cdz360.biz.model.site.dto.SiteGeoDto;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SiteOrderBiVo extends SiteGeoDto
    implements BiHourly, BiDaily, BiWeekly, BiMonthly {

    private SiteBiSampleType timeType;

    private Long orderNum;

    private Long plugNum;

    private BigDecimal elec;

    private BigDecimal elecFee;

    private BigDecimal servFee;

    /**
     * 按小时统计时为 YYYY-MM-dd HH 按周统计时为 YYYY-W (2020-W3) 按天统计时为 YYYY-MM-dd 按月统计时为 YYYY-MM
     */
    private String time;

    @Schema(description = "该场站挂载摄像机")
    private Boolean hasCamera;

    @Override
    @JsonIgnore
    public LocalDateTime getFullTime() {
        if (time == null) {
            return null;
        }
        return LocalDateTime.parse(time, DateTimeFormatter.ofPattern("yyyy-MM-dd HH"));
    }

    @Override
    @JsonIgnore
    public LocalDate getFullDate() {
        if (time == null) {
            return null;
        }
        return LocalDate.parse(time, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }

    @Override
    @JsonIgnore
    public LocalDate getFullWeek() {
        if (time == null) {
            return null;
        }
        String timex = this.time + "-1";
        return LocalDate.parse(timex, DateTimeFormatter.ISO_WEEK_DATE);
    }

    @Override
    @JsonIgnore
    public LocalDate getFullMonth() {
        if (time == null) {
            return null;
        }
        String timex = this.time + "-01";
        return LocalDate.parse(timex, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }
}
