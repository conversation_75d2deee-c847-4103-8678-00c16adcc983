package com.cdz360.biz.model.trading.cus.param;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.param.BaseListParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Schema(description = "获取用户申请退款记录请求参数")
public class CusRefundOrderListParam extends BaseListParam {

    @Schema(description = "用户Id", hidden = true)
    private Long uid;

    @Schema(description = "顶级商户Id", hidden = true)
    private Long topCommId;

    @Schema(description = "商户Id", hidden = true)
    private Long commId;

    @Schema(description = "客户端类型")
    private AppClientType clientType;
}
