package com.cdz360.biz.model.trading.iot.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "光伏统计数据")
@Data
@Accessors(chain = true)
public class PvDataBi {

    @Schema(description = "总发电量", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal totalKwh;

    @Schema(description = "收益", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal totalProfit;
}
