package com.cdz360.biz.model.trading.site.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "场站消费统计信息")
@Data
@Accessors(chain = true)
public class BiSiteOrderVo {

    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "场站名称/账户类型名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String name;

    @Schema(description = "充电订单量(单位: 个)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long orderCnt;

    @Schema(description = "充电订单总金额(单位: 元)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal orderFee;

    @Schema(description = "无结算账户金额(单位: 元)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal noAccountFee;

    @Schema(description = "后付费金额(单位: 元)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal postSettlementFee;

    @Schema(description = "预付费金额(单位: 元)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal preSettlementFee;

    @Schema(description = "赠送金额(单位: 元)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal freeFee;

    @Schema(description = "实际金额(单位: 元)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal costFee;

    @Schema(description = "该条件下各账户的消费统计")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<BiSiteOrderVo> biAccountOrderList;
}
