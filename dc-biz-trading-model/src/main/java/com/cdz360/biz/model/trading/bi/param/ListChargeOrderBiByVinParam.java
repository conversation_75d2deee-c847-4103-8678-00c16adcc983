package com.cdz360.biz.model.trading.bi.param;

import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.order.param.ListBiParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "订单统计入参")
public class ListChargeOrderBiByVinParam extends ListBiParam {

    @Schema(description = "模糊匹配车队名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String carDepart;

    @Schema(description = "模糊匹配车架号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String vin;

    @Schema(description = "精确匹配车架号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String vinExact;

    @Schema(description = "精确匹配车架号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long vinId;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer cardType;

    @Schema(description = "精确匹配物理卡")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String cardId;

    @Schema(description = "模糊匹配物理卡号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cardChipNo;

    @Schema(description = "模糊匹配卡名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cardName;

    @Schema(description = "模糊匹配车牌")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String carNo;

    @Schema(description = "模糊匹配车辆自编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String carNum;

    @Schema(description = "模糊匹配车辆线路号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String carLineNum;

    @Schema(description = "精确匹配客户")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long cusUserId;


    @Schema(description = "客户的企业组织ID数组", example = "[1,2,5]")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> corpOrgIds;

    @Schema(description = "商户ID列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Deprecated
    private List<Long> commIds;

    @Schema(description = "模糊匹配企业客户名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String corpCusName;

    @Schema(description = "精确匹配企业客户")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long corpCusId;

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ExcelPosition excelPosition;//excel位置

    @Schema(description = "商户Id链", required = true, hidden = true)
    private String commIdChain;

    @Schema(description = "要精确匹配的VIN列表", hidden = true)
    private List<String> vinList;

    @Schema(description = "要精确匹配的客户ID列表", hidden = true)
    private List<Long> cusIdList;
}
