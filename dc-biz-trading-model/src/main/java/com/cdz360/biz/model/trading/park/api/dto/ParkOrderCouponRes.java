package com.cdz360.biz.model.trading.park.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ParkOrderCouponRes
 *
 * @since 8/15/2022 5:15 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ParkOrderCouponRes {
    private String chargeOrderNo;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date chargeStartTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date chargeStopTime;
    private Integer freeMinutes;
}