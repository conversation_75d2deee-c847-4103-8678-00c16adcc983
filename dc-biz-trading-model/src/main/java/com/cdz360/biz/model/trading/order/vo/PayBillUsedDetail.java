package com.cdz360.biz.model.trading.order.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 充值记录使用详情
 *
 * <AUTHOR>
 * @since 2019/12/3 19:52
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@Schema(description = "充值记录使用详情")
public class PayBillUsedDetail {
    @Schema(description = "充值记录使用信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private PayBillUsedInfo usedInfo = new PayBillUsedInfo();

    @Schema(description = "充电订单使用情况")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<PayBillLinkChargeOrderVo> chargeOrderVoList = new ArrayList<>();

    // 退款记录
    @Schema(description = "充值记录: 本次充值，针对充值减少的充值")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<PayBillVo> payBillVoList = new ArrayList<>();
}
