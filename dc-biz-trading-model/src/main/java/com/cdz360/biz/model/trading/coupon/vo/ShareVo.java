package com.cdz360.biz.model.trading.coupon.vo;

import com.cdz360.biz.model.trading.coupon.po.ActivityImagePo;
import com.cdz360.biz.model.trading.coupon.po.ActivityPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ShareVo  {

    /**
     * 公众号唯一标识
     */
    private String  appId;

    /**
     * 生成签名的时间戳
     */
    private Long timeStamp;

    /**
     * 生成签名的随机字符串
     */
    private String nonceStr;

    /**
     * 签名
     */
    private String signature;

    /**
     * 请求的url
     */
    private String url;


}
