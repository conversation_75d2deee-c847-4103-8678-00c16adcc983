package com.cdz360.biz.model.trading.site.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * BiPlugPo
 * 
 * @since 3/23/2020 5:45 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@Schema(description = "枪使用信息")
public class BiPlugPo {
    private Long id;
    private String siteId;
    private String evseNo;
    private String dno;
    private Integer plugId;
    private Integer errorCount;//故障次数，当plugId为1时使用
    private Integer offlineCount;//离线次数，当plugId为1时使用
    private Date date;
    private Integer duration;

    /**
     * type = 0 桩信息，type=1 逆变器信息 type=2 储能ESS告警信息
     */
    private Long type;
    private Integer available;
    private Integer orderCount;
    private BigDecimal elecFee;
    private BigDecimal servFee;
    private BigDecimal fee;
    private BigDecimal electricity;
    private BigDecimal elecTag1;
    private BigDecimal elecTag2;
    private BigDecimal elecTag3;
    private BigDecimal elecTag4;
    private Date createTime;
    private Date updateTime;
    private BigDecimal powerUseRate = BigDecimal.ZERO;
}
