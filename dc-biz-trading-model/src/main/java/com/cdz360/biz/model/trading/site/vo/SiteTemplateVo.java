package com.cdz360.biz.model.trading.site.vo;

import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.biz.model.site.po.PriceItemPo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SiteTemplateVo {

    @Schema(description = "场站ID")
    private String siteId;

    @Schema(description = "模板ID")
    private Long templateId;

    @Schema(description = "模板名称")
    private String templateName;

    @Schema(description = "版本号")
    private Long version;

    @Schema(description = "免费充电标识**0-收费 1-免费**", example = "1")
    private Integer freeChargeFlag;

    @Schema(description = "code值")
    private String code;

    @Schema(description = "模板类型")
    private SupplyType templateType;

    @Schema(description = "模板信息",hidden = true)
    List<PriceItemPo> items;

}
