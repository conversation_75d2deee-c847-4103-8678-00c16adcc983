package com.cdz360.biz.model.trading.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;
@Data
@Accessors(chain = true)
public class OrderBi {

    @Schema(description = "累计充电时长, 单位'秒'")
    private Long duration;

    @Schema(description = "累计充电电量, 单位'kwh'")
    private BigDecimal kwh;

    @Schema(description = "累计充电订单数量")
    private Long orderNum;

    @Schema(description = "总金额, 单位'元'")
    private BigDecimal fee;

    @Schema(description = "总电费, 单位'元'")
    private BigDecimal elecFee;

    @Schema(description = "总服务费, 单位'元'")
    private BigDecimal servFee;
}
