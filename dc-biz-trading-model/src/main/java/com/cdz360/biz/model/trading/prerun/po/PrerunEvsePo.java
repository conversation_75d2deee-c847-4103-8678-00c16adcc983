package com.cdz360.biz.model.trading.prerun.po;

import com.cdz360.base.model.base.type.SupplyType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.lang.Double;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel(value = "调试工单-场站设备信息")
public class PrerunEvsePo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@Size(max = 32, message = "prerunNo 长度不能超过 32")
	private Long prerunId;

	@Size(max = 32, message = "prerunNo 长度不能超过 32")
	private Boolean enable;

	@Size(max = 32, message = "evseNo 长度不能超过 32")
	private String evseNo;

	@Size(max = 64, message = "evseName 长度不能超过 64")
	private String evseName;

	@ApiModelProperty(value = "设备型号")
	@Size(max = 64, message = "model 长度不能超过 64")
	private String model;

	@ApiModelProperty(value = "设备型号id")
	private Long modelId;

	@ApiModelProperty(value = "电流类型")
	private SupplyType supply;

	@ApiModelProperty(value = "设备功率")
	private Integer power;

	@ApiModelProperty(value = "桩铭牌编号")
	@Size(max = 32, message = "physicalNo 长度不能超过 32")
	private String physicalNo;

	@ApiModelProperty(value = "出厂日期")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date produceDate;

	@ApiModelProperty(value = "质保到期日")
	@JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
	private Date expireDate;

	@ApiModelProperty(value = "桩软件版本")
	@Size(max = 32, message = "firmwareVer 长度不能超过 32")
	private String firmwareVer;

	@ApiModelProperty(value = "模块类型")
	@Size(max = 64, message = "moduleType 长度不能超过 64")
	private String moduleType;

	@ApiModelProperty(value = "是否故障")
	private Boolean fault;

	@ApiModelProperty(value = "故障描述")
	@Size(max = 255, message = "faultDesc 长度不能超过 255")
	private String faultDesc;

	@ApiModelProperty(value = "故障解决详情")
	@Size(max = 255, message = "faultFix 长度不能超过 255")
	private String faultFix;

	@ApiModelProperty(value = "备注")
	@Size(max = 255, message = "comment 长度不能超过 255")
	private String comment;

	@ApiModelProperty(value = "是否已校验")
	private Integer validate;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;


}
