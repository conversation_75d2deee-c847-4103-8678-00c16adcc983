package com.cdz360.biz.model.trading.hlht.vo;

import com.cdz360.biz.model.trading.hlht.po.SiteHlhtPo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CommercialHlhtSiteVo extends SiteHlhtPo {

    private String siteName;

    private String provinceName;

    private String cityName;

    private String address;

    private Integer siteStatus;

    private Integer evseTotal;

    private Integer plugTotal;
}
