package com.cdz360.biz.model.trading.order.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.trading.bill.type.DailyBillCheckResult;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 直付通订单查询
 *
 * <AUTHOR>
 * @since 2019/11/5 16:37
 */
@Data
@Accessors(chain = true)
@Schema(description = "直付通记录查询")
@EqualsAndHashCode(callSuper = true)
public class ZftBillParam extends BaseListParam {

    /**
     * 平台订单号
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "平台订单号(支持模糊查询)")
    private String orderId;

    /**
     * 微信/支付宝订单号
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "微信/支付宝订单号(支持模糊查询)")
    private String tradeNo;

    /**
     * 充电订单号
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "充电订单号(支持模糊查询)")
    private String chargeOrderNo;

    /**
     * 直付通商家名称
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "直付通商家名称(支持模糊查询)")
    private String zftName;

    /**
     * 交易渠道
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "交易渠道（微信-WX,支付宝-ALIPAY）")
    private String payWay;

    /**
     * 财务类型
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "财务类型（即充即退充电-PREPAY，在线充值-PERSON，功能订阅-FUNCTION，免充值先充电-CREDIT）")
    private String financialType;

    /**
     * 手机号
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "手机号（支持模糊查询）")
    private String phone;

    /**
     * 所属商户
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "所属商户")
    private Long commId;

    @Schema(description = "商户ID链")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commIdChain;

    @Schema(description = "操作时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter payTimeFilter;

    @Schema(description = "收支类型: IN_FLOW(收入), OUT_FLOW(支出)",
            format = "java.lang.Integer")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private DepositFlowType flowType;

    @Schema(description = "支付平台对账结果 FULL_MATCH(完全匹配); AMOUNT_NOT_MATCH(金额不匹配); NO_NOT_MATCH(未找到对应订单); OTHERS(其他)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private DailyBillCheckResult checkResult;

    @Schema(description = "关联对账单名称 t_zft_daily_bill.name")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dailyBillName;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
