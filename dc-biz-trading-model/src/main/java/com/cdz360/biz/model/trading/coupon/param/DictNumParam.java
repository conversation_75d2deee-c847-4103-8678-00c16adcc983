package com.cdz360.biz.model.trading.coupon.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * UpdateActivityParam
 *
 * @since 7/31/2020 10:07 AM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "批量发券")
public class DictNumParam {
    @Schema(description = "券ID")
    private Long dictId;

    @Schema(description = "发放数量")
    private Integer num;

}