package com.cdz360.biz.model.trading.order.po;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * ChargerOrderTimeDivisionEx
 *
 * @since 12/14/2020 3:20 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ChargerOrderTimeDivisionEx extends ChargerOrderTimeDivision {
    private Date startDateTime;
    private Date stopDateTime;

    private String evseNo;
    private Integer plugId;

    // 时段内最后一个心跳的电量
    @JsonIgnore
    private BigDecimal lastHbkWh;
}