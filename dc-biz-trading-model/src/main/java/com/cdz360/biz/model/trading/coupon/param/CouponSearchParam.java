package com.cdz360.biz.model.trading.coupon.param;

import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.biz.model.trading.coupon.type.CouponStatusType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * CouponSearchParam
 *
 * @since 7/31/2020 4:56 PM
 * <AUTHOR>
 */
@Data
public class CouponSearchParam {

    @Schema(description = "手机号模糊")
    private String phone;
    @Schema(description = "券号模糊")
    private String couponId;
    @Schema(description = "券状态")
    private CouponStatusType status;
    @Schema(description = "活动id")
    private Long activityId;
    @Schema(description = "活动id列表")
    private List<Long> activityIdList;

    @Schema(description = "页码，从1开始")
    private Integer index;

    @Schema(description = "分页start")
    private Integer start;

    @Schema(description = "每页最大显示条数")
    private Integer size;

    // 下方为客户详情界面使用的查询参数
    @Schema(description = "券模版名模糊")
    private String dictName;

    @Schema(description = "充电订单号模糊")
    private String orderNo;

    @Schema(description = "领取时间筛选")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected TimeFilter createTimeFilter;

    @Schema(description = "订单创建时间筛选")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected TimeFilter orderCreateTimeFilter;

    @Schema(description = "客户id")
    protected Long userId;
}