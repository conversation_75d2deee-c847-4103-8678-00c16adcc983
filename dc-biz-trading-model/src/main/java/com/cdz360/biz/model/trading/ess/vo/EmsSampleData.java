package com.cdz360.biz.model.trading.ess.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "EMS设备运行数据采样")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class EmsSampleData extends SampleBase {

    @Schema(description = "电池剩余电量（SOC）0.1%")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal soc;
}
