package com.cdz360.biz.model.trading.meter.vo;

import com.cdz360.biz.model.trading.meter.po.MeterPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * MeterVo
 *
 * @since 9/21/2020 9:36 AM
 * <AUTHOR>
 */

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MeterVo extends MeterPo {
    @Schema(description = "绑定桩个数")
    private Integer bindEvseCount;

    private String siteName;
}