package com.cdz360.biz.model.trading.site.dto;

import com.cdz360.biz.model.trading.site.type.SiteInspectionStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "场站最近一次巡检记录")
public class RecentInspectionRecordDto {

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long siteCommId;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;

    @Schema(description = "巡检周期")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer cycle;

    @Schema(description = "最近一次巡检人")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String rummager;

    @Schema(description = "最近一次质检状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private SiteInspectionStatus status;

    @Schema(description = "最近一次巡检时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date time;

    @Schema(description = "距离上次巡检已*天")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long days;

}
