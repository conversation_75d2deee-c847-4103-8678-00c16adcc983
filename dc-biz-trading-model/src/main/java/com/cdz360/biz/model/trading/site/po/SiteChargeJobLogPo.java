package com.cdz360.biz.model.trading.site.po;

import com.cdz360.biz.model.trading.site.type.TimerOperation;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "场站定时任务执行结果")
public class SiteChargeJobLogPo {
    private Long id;
    @Schema(description = "任务ID")
    private Long jobId;
    @Schema(description = "第N次启动时间")
    private Integer timeIdx;

    @Schema(description = "操作方式", example = "START")
    private TimerOperation operation;

    private String siteId;

    private String evseNo;
    @Schema(description = "枪头序号")
    private Integer plugIdx;

    private String orderNo;
    @Schema(description = "0,成功; 1,失败; 2,超时")
    private Integer result;
    @Schema(description = "失败原因")
    private String failReason;
    @Schema(description = "执行时间")
    private Date createTime;

    private Date updateTime;
}
