package com.cdz360.biz.model.trading.iot.vo;

import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.biz.model.trading.iot.dto.PvRtDataDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "逆变器实时数据信息")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class GtiDataInTimeVo extends EquipInfoInTimeVo {
    // t_gti

//    @Schema(description = "串口通信(485/modbus) ID")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Integer sid;

    // t_gti_cfg
    @Schema(description = "重连时间,单位: 秒")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer timeout;

    @Schema(description = "起机电压, 单位V")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal bootVoltage;

    @Schema(description = "市电电压下限, 单位V")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal minVoltage;

    @Schema(description = "市电电压上限, 单位V")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal maxVoltage;

    @Schema(description = "市电频率下限, 单位Hz")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal minFrequency;

    @Schema(description = "市电频率上限, 单位Hz")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal maxFrequency;

    @Schema(title = "状态", description = "设备状态.0未知;1在线;2离线;3启动中;4告警;5故障;99,下线")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EquipStatus status;

    @Schema(description = "实时数据")
    private PvRtDataDto realtimeData;

}
