package com.cdz360.biz.model.trading.order.dto;

import com.cdz360.biz.model.trading.site.vo.SiteOrderBiVo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class GeoOrderBiDto {

//    private String code;
//    private String name;

//    @Schema(description = "场站数量")
//    private Long siteNum;
    private Long onlineEvseNum;
    private Long offlineEvseNum;
    private Long errorEvseNum;

    @Schema(description = "总功率")
    private Long totalPower;

    @Schema(description = "充电中功率")
    private Long busyPower;
    private List<SiteOrderBiVo> orderBi;


}
