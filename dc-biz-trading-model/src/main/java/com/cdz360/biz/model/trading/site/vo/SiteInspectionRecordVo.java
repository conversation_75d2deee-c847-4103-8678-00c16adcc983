package com.cdz360.biz.model.trading.site.vo;

import com.cdz360.biz.model.trading.site.po.SiteInspectionRecordPo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SiteInspectionRecordVo extends SiteInspectionRecordPo {

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;

    @Schema(description = "负责人")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String opName;

    @Schema(description = "质检人名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String qcName;

    @Schema(description = "省份名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String provinceName;

    @Schema(description = "城市名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cityName;

    @Schema(description = "区名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String areaName;

    @Schema(description = "地址")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String address;

    @Schema(description = "经度")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal longitude;

    @Schema(description = "纬度")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal latitude;

    @Schema(description = "质保到期日")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date expireDate;

}
