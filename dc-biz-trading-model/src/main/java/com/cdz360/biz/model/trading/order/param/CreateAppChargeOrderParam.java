package com.cdz360.biz.model.trading.order.param;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Schema(description = "创建充电订单参数")
@Accessors(chain = true)
public class CreateAppChargeOrderParam {

    @Schema(description = "枪头编号(桩上二维码)", example = "****************", required = true)
    private String plugNo;

    @Schema(description = "枪头二维码", example = "hlht://aaaa.bbb HLHT需要, 9月版本新增")
    private String qrCode;

    // 1个人账户 2集团授权账户 3商户会员
    // OrderPayType
    //private Integer payType;
    @Schema(description = "支付账户类型", required = true)
    private PayAccountType accountType;

    // 付款账户对应的(商户会员的 商户id（子商户id）, 现金账户的 集团商户id， 集团授信账户的t_r_bloc_user.id）
    @Schema(description = "支付充电费用账户ID", example = "34474")
    private Long accountId;

    @Schema(description = "支付方式(仅用于即充即退)")
    private PayChannel payChannel;

    @Schema(description = "充值金额(仅用于即充即退), 单位'元', 2位小数")
    private BigDecimal amount;

    @Schema(description = "微信openid/支付宝buyer_id(仅用于即充即退)/高德小程序openid")
    private String openid;

    @Schema(description = "合作方订单号. 仅对互联互通创建的订单有效")
    private String partnerOrderNo;

    @Schema(description = "互联互通合作方唯一编号")
    private String partnerNo;

    /**
     * 集团商户ID
     */
    @Schema(description = "集团商户ID")
    private long topCommId;

    @Schema(description = "微信/支付宝小程序/APP的AppId")
    private String appId;

    /**
     * 微信小程序formId
     * https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/template-message/templateMessage.send.html
     * <p>
     * 支付宝小程序模板消息推送 https://docs.alipay.com/mini/api/templatemessage
     */
    @Schema(description = "微信/支付宝小程序formId")
    private String formId;

    /**
     * ip
     */
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String remoteIp;

    /**
     * 客户id
     */
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private long cusId;
    /**
     * 客户名称
     */
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String cusName;

    private Boolean oversea;

    /**
     * 客户端类型
     */
    @Schema(hidden = true)
    private AppClientType appClientType;

    @Schema(description = "用户手机号 数字货币支付时为数币账号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String phone;

//    @Schema(description = "是否使用数字货币 使用数字货币时必填，不然按照旧的处理逻辑")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Boolean useDigiccy;

    @Schema(description = "微信登录session")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String sessionKey;

    @Schema(description = "微信登录加密参数")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String encryptedData;

    @Schema(description = "微信登录加密参数")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String iv;

    @Schema(description = "数币账户 授权码/手机号(现阶段使用手机号)", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String digiccyAccount;

    @Schema(name = "优惠券编号(农行优惠查询返回的优惠券编号)")
    private String couponId;

    @Schema(name = "绑定的车牌号id")
    private Long vinId;

    @Schema(name = "绑定的车牌号")
    private String carNo;

    /**
     * 不传（null）表示旧版本，自动选择最优积分体系；0表示专门没选；指定id表示选定的积分体系
     */
    @Schema(name = "积分体系id")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long scoreSettingId;

    /**
     * 大于0表示有选择优惠券，0和null不做处理
     */
    @Schema(name = "优惠券id")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long couponSelectedId;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
