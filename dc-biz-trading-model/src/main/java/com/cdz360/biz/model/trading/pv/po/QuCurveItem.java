package com.cdz360.biz.model.trading.pv.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "Q-U特征曲线")
public class QuCurveItem {

    private Integer idx;

    @Schema(description = "U/Un值(%) [80,136]支持一位小数")
    private BigDecimal uun;

    @Schema(description = "Q/S值 [-0.6,0.6]支持三位小数")
    private BigDecimal qs;
}
