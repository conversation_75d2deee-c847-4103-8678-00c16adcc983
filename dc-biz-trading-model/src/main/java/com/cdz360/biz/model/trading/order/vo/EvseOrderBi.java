package com.cdz360.biz.model.trading.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Schema(description = "充电桩使用统计")
@EqualsAndHashCode(callSuper = true)
public class EvseOrderBi extends OrderBi {

    @Schema(description = "桩编号")
    private String evseNo;

    @Schema(description = "枪头使用统计")
    private List<PlugOrderBi> plugs;
}
