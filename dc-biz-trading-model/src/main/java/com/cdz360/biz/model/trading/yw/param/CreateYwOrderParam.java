package com.cdz360.biz.model.trading.yw.param;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.UserType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.model.trading.yw.dto.CarInfo;
import com.cdz360.biz.model.trading.yw.type.AssignWay;
import com.cdz360.biz.model.trading.yw.type.CreateYwOrderSourceType;
import com.cdz360.biz.model.trading.yw.type.YwOrderLevel;
import com.cdz360.biz.model.trading.yw.type.YwOrderTag;
import com.cdz360.biz.model.trading.yw.type.YwType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "创建运维工单参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CreateYwOrderParam extends BaseOpParam {

    @Schema(description = "场站编号", required = true)
    @Size(max = 32, message = "siteId 长度不能超过 32")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "场站商户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long siteCommId;

    @Schema(description = "场站名称", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;

    @Schema(description = "关联充电订单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderNo;

    @Schema(description = "故障的充电桩编号(可多个)", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> evseNoList;

    @Schema(description = "充电桩编号 + 桩名称 后端逻辑生成")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> evseStrList;

    @Schema(description = "故障图片 故障报修提供")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> faultImages;

    @Schema(description = "故障描述 C端前端拼接传入", required = true)
    @Size(max = 400, message = "faultDesc 长度不能超过 400")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String faultDesc;

    @Schema(description = "故障等级: 10(一般); 20(紧急); 30(重大)", required = true)
    @NotNull(message = "faultLevel 不能为 null")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private YwOrderLevel faultLevel;

    @Schema(description = "故障等级: 10(一般); 20(紧急); 30(重大)", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private YwOrderTag tag;

    // ====================================================================
    @Schema(description = "车辆品牌")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String carBrand;

    @Schema(description = "车辆类型")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String carModel;

    @Schema(description = "需求电压, 单位: V")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal carNeedVoltage;

    @Schema(description = "需求电流, 单位: A")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal carNeedCurrent;

    @Schema(description = "车辆品牌")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean carOtherEvse;

    //    =================================================================
    @Schema(description = "创建运维工单客户端. UNKNOWN,未知; C端客户(CUSTOMER_SRC);" +
        " 商户(COMM_SRC); 运维(YW_SRC); 客服(KF_SRC); 其他(OTHER_SRC)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private CreateYwOrderSourceType sourceType;

//    @Schema(description = "操作类型. 0,未知; ")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Integer opType;
//
//    @Schema(description = "操作人ID")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Long opUid;
//
//    @Schema(description = "操作人名字")
//    @Size(max = 32, message = "opName 长度不能超过 32")
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String opName;

    @Schema(description = "是否过质保期: true(是); false(否)", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean overExpireDate;

    // ===================== 运维人的信息 =======================
    @Schema(description = "运维人类型. 0,未知; 1,商户/平台用户; 3,企业客户; 4,系统", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private UserType maintType;

    @Schema(description = "派单方式")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private AssignWay assignWay;

    @Schema(description = "运维人ID", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long maintUid;

    @Schema(description = "运维人名字", hidden = true)
    @Size(max = 32, message = "maintName 长度不能超过 32")
    @JsonInclude(JsonInclude.Include.NON_DEFAULT)
    private String maintName;

    @Schema(description = "运维人所属商户ID", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long maintCommId;

    @Schema(description = "运维工单类型 1：光； 2：储； 3：充；")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer ywType;

    @Schema(description = "是否为故障报修入口")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean equipRepairEntry;

    @Schema(description = "故障告警忽略的告警码")
    private List<String> ignoreWarningCodeList;

    public static void checkParam(CreateYwOrderParam param) {
        if (StringUtils.isBlank(param.getSiteId())) {
            throw new DcArgumentException("场站ID不能为空");
        }

        if ((param.getYwType() == null || YwType.CHARGE.getCode() == param.getYwType())
            && CollectionUtils.isEmpty(param.getEvseNoList())) {
            throw new DcArgumentException("桩编号不能为空");
        }

        if (StringUtils.isBlank(param.getFaultDesc())) {
            throw new DcArgumentException("故障描述不能为空");
        }

        if (null == param.getFaultLevel() || YwOrderLevel.UNKNOWN.equals(param.getFaultLevel())) {
            throw new DcArgumentException("请选择有效的故障等级");
        }

    }

    public CarInfo carInfo() {
        CarInfo carInfo = null;
        if (StringUtils.isNotBlank(this.carBrand)) {
            carInfo = new CarInfo().setBrand(this.carBrand);
        }

        if (StringUtils.isNotBlank(this.carModel)) {
            if (null == carInfo) {
                carInfo = new CarInfo();
            }
            carInfo.setModel(this.carModel);
        }

        if (null != this.carNeedVoltage) {
            if (null == carInfo) {
                carInfo = new CarInfo();
            }
            carInfo.setNeedVoltage(this.carNeedVoltage);
        }

        if (null != this.carNeedCurrent) {
            if (null == carInfo) {
                carInfo = new CarInfo();
            }
            carInfo.setNeedCurrent(this.carNeedCurrent);
        }

        if (null != this.carOtherEvse) {
            if (null == carInfo) {
                carInfo = new CarInfo();
            }
            carInfo.setOtherEvse(this.carOtherEvse);
        }
        return carInfo;
    }
}
