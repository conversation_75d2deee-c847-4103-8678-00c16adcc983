package com.cdz360.biz.model.trading.order.dto;

import com.cdz360.base.model.base.type.PayAccountType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Schema(description = "账户类型统计充电订单")
@Accessors(chain = true)
public class OrderAccountTypeBiDto {

    private PayAccountType accType;

    private BigDecimal elecFee;

    private BigDecimal servFee;
}
