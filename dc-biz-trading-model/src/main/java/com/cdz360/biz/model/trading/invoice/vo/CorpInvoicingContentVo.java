package com.cdz360.biz.model.trading.invoice.vo;

import com.cdz360.biz.model.oa.vo.InvoicingContentVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "企业开票内容")
@EqualsAndHashCode(callSuper = true)
public class CorpInvoicingContentVo extends InvoicingContentVo {

//    @Schema(description = "名称")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private String productName;
//
//    @Schema(description = "类型")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private ProductType productType;
//
//    @Schema(description = "税率")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private BigDecimal taxRate;
//
//    @Schema(description = "实开金额")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private BigDecimal fixAmount;
//
//    @Schema(description = "开票数量")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Long num;
//
//    @Schema(description = "单价")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private BigDecimal price;
}
