package com.cdz360.biz.model.trading.site.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.biz.model.site.type.TemplateUsage;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ListPriceTemplateParam extends BaseListParam {

    @Schema(description = "计费模板名称")
    private String name;

    private List<Long> idList;

    @Schema(description = "商户ID链", hidden = true)
    private String commIdChain;

    @Schema(description = "计费模板编号")
    private String code;

    @Schema(description = "计费模板使能状态")
    private Boolean enable;

    private Boolean deleteFlag;
    /**
     * 查询计费模板时，是否包括下面这种模板
     * 任意上级商户对当前登录商户的场站下发时所指定的计费模板（且勾选作为场站计费信息）
     */
    private Boolean forListQueries;

    @Schema(description = "用途: 1(充电收益计算); 2(光伏/储能使用)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TemplateUsage usage;
}
