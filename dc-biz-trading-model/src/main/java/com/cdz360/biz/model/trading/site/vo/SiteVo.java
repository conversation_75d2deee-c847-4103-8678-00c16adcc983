package com.cdz360.biz.model.trading.site.vo;

import com.cdz360.biz.model.sys.po.SiteGroupPo;
import com.cdz360.biz.model.trading.site.po.SiteDefaultSettingPo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SiteVo extends SitePo {


    /**
     * 集团商户简称
     */
    @Schema(description = "集团商户简称")
    private String topCommShortName;

    /**
     * 场站商户简称
     */
    @Schema(description = "场站商户简称")
    private String commShortName;


    /**
     * 省份名称
     */
    @Schema(description = "省份名称")
    private String provinceName;

    /**
     * 城市名称
     */
    @Schema(description = "城市名称")
    private String cityName;

    /**
     * 区名称
     **/
    @Schema(description = "区名称")
    private String areaName;

    @Schema(description = "场站配置信息")
    private SiteDefaultSettingPo setting;

    @Schema(description = "场站总功率，所有桩功率总和")
    private BigDecimal totalPower;

    @Schema(description = "站点图片")
    private List<ImageVo> imageList;

    @Schema(description = "所属商户的idChain")
    private String idChain;


    @Schema(description = "场站所属商户")
    private Long siteCommId;

    @Schema(description = "场站综合评级 level平均值, 保留一位小数", example = "3.6")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal avgLevel;

    @Schema(description = "场站性质")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String gcTypeDesc;

    @Schema(description = "质保到期日")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date expireDate;

    @Schema(description = "最近巡检时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date recentInspection;

    @Schema(description = "并网日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date onGridDate;

    @Schema(description = "移动端开票主体(移动端允许开票需要配置)")
    @JsonInclude(Include.NON_NULL)
    private Long mobileTempSalId;

    @Schema(description = "价格模板列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<SiteTemplateVo> templateList;

    @Schema(description = "场站组信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<SiteGroupPo> siteGroupList;

}
