package com.cdz360.biz.model.trading.camera.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * CameraSitePo
 *
 * @since 7/30/2021 1:44 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "海康云眸门店信息")
public class CameraSitePo {

    @NotNull(message = "id 不能为 null")
    private Long id;

    @Schema(description = "场站编号，用于关联t_site")
    @Size(max = 32, message = "siteId 长度不能超过 32")
    private String siteId;

    @Schema(description = "接入账号id")
    @NotNull(message = "accountId 不能为 null")
    private Long accountId;

    @Schema(description = "门店ID")
    @Size(max = 64, message = "cameraSiteId 长度不能超过 64")
    private String cameraSiteId;

    @Schema(description = "门店名")
    @Size(max = 255, message = "cameraSiteName 长度不能超过 255")
    private String cameraSiteName;

    @Schema(description = "门店面积")
    @Size(max = 255, message = "cameraSiteMeasure 长度不能超过 255")
    private String cameraSiteMeasure;

    @Schema(description = "门店详细地址")
    @Size(max = 255, message = "cameraSiteDetailAddress 长度不能超过 255")
    private String cameraSiteDetailAddress;

    @Schema(description = "省")
    @Size(max = 255, message = "addressProvince 长度不能超过 255")
    private String addressProvince;

    @Schema(description = "市")
    @Size(max = 255, message = "addressCity 长度不能超过 255")
    private String addressCity;

    @Schema(description = "区")
    @Size(max = 255, message = "addressCounty 长度不能超过 255")
    private String addressCounty;

    @Schema(description = "街道地址")
    @Size(max = 255, message = "addressDetail 长度不能超过 255")
    private String addressDetail;

    @Schema(description = "店长姓名")
    @Size(max = 255, message = "mangerName 长度不能超过 255")
    private String mangerName;

    @Schema(description = "店长手机号码")
    @Size(max = 255, message = "mangerTel 长度不能超过 255")
    private String mangerTel;

    @Schema(description = "门店电话")
    @Size(max = 255, message = "cameraSiteTel 长度不能超过 255")
    private String cameraSiteTel;

    @Schema(description = "门店编号")
    private String cameraSiteNo;

    @Schema(description = "门店经度")
    private String cameraSiteLng;

    @Schema(description = "门店纬度")
    private String cameraSiteLat;

    @Schema(description = "门店描述")
    @Size(max = 255, message = "cameraSiteRemark 长度不能超过 255")
    private String cameraSiteRemark;

    @Schema(description = "	门店区域组织路径")
    @Size(max = 255, message = "areaPath 长度不能超过 255")
    private String areaPath;

    @Schema(description = "数据入库时间")
    private String insertTime;

    @Schema(description = "数据更新时间")
    private String updateTime;

    @Schema(description = "1有效 0无效")
    @NotNull(message = "enable 不能为 null")
    private Boolean enable;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateAt;


}