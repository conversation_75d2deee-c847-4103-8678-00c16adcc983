package com.cdz360.biz.model.trading.site.vo;

import com.cdz360.biz.model.trading.site.type.TimerOperation;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "充电任务队列")
public class TimeChargeVO {

    @Schema(description = "任务ID")
    private Long jobId;

    @Schema(description = "第N次启动时间")
    private Integer timeIdx;

    @Schema(description = "操作方式", example = "START", required = true)
    private TimerOperation operation;

    @Schema(description = "配置的下发时间")
    private LocalDateTime startTime;

}
