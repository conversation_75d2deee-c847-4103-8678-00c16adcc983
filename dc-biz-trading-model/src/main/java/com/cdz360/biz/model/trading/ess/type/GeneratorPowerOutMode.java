package com.cdz360.biz.model.trading.ess.type;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "柴油机功率输出模式")
@Getter
public enum GeneratorPowerOutMode {

    UNKNOWN(0, "未知"),
    CHARGE_POWER(1, "电能输出"),
    GENERATOR(2, "柴油机输出"),

    ;

    private final int code;
    private final String desc;

    GeneratorPowerOutMode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
