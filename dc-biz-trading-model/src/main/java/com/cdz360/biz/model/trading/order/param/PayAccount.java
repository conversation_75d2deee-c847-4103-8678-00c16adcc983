package com.cdz360.biz.model.trading.order.param;

import com.cdz360.base.model.base.type.PayAccountType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2019/11/12 11:34
 */
@Data
@EqualsAndHashCode
@ToString
@Accessors(chain = true)
@Schema(description = "用户充值账户")
public class PayAccount {

    @Schema(description = "充值账户类型. 个人账户/商户会员: UNKNOWN(0)-未知,PERSONAL(1)-个人现金账户,CREDIT(2)-集团授信账户,COMMERCIAL(3)-商户商户会员,PREPAY(4)-即充即退")
    private PayAccountType accountType;

    @Schema(description = "账户编号. 个人账户为集团商户编号; 商户会员为商户编号")
    private Long accountCode;
}
