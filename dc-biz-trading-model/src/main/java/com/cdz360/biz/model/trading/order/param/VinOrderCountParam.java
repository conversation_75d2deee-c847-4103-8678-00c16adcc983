package com.cdz360.biz.model.trading.order.param;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * vin码启动订单统计
 *
 * <AUTHOR>
 * @since 2019/12/28 9:26
 */
@Data
@Accessors(chain = true)
@Schema(description = "vin码启动订单统计参数")
public class VinOrderCountParam {
    @Schema(description = "vin码列表")
    private List<String> vinList;

    @Schema(description = "用户Id")
    private Long userId;

    private String commIdChain;
}
