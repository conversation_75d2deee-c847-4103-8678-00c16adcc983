package com.cdz360.biz.model.trading.cus.vo;

import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.biz.model.trading.deposit.vo.DepositSourceType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "退款传输信息")
@Data
@Accessors(chain = true)
public class RefundResult {

    public enum ResultCode {
        SUCCESS, // 成功
        FAIL,  // 失败
        SKIP  // 跳过
    }

    @Schema(description = "退款状态值")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ResultCode code;

    @Schema(description = "减少序列号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long seqNo;

    @Schema(description = "是否追加PayBill记录")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean appendBill;

    @Schema(description = "充值来源")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private DepositSourceType sourceType;

    @Schema(description = "支付方式")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private PayChannel payChannel;
}
