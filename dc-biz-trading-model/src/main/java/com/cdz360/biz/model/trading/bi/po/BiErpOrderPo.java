package com.cdz360.biz.model.trading.bi.po;

import com.cdz360.biz.model.trading.bi.type.ErpSiteStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "Erp推送的充电订单")
public class BiErpOrderPo {
    @Schema(description = "ID")
    private Long id;

    @Schema(description = "场站推送记录ID(t_bi_erp_site)")
    private Long erpSiteId;

    @Schema(description = "充电订单号")
    private String orderNo;

    @Schema(description = "收入时间")
    private LocalDateTime incomeTime;

    @Schema(description = "记录推送状态")
    private ErpSiteStatus status;

    @Schema(description = "场站充电订单总电量，单位:元")
    private BigDecimal orderElectricity;

    @Schema(description = "场站充电订单总费用，单位:元")
    private BigDecimal orderPrice;

    @Schema(description = "场站充电订单总电费，单位: 元")
    private BigDecimal electricPrice;

    @Schema(description = "场站充电订单总服务费，单位: 元")
    private BigDecimal servicePrice;

    @Schema(description = "记录创建时间")
    private Date createTime;

    @Schema(description = "记录更新时间")
    private Date updateTime;
}
