package com.cdz360.biz.model.trading.site.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * BiSiteSumPo
 *
 * @since 3/23/2020 1:34 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
public class BiSiteSummaryPo {
    private BigDecimal total = BigDecimal.ZERO;
    private String name;
    private List<BiSiteSumPo> biSummary;
}