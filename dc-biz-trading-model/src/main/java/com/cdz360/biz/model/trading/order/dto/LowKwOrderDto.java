package com.cdz360.biz.model.trading.order.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

@Data
@Schema(description = "小电流充电订单")
public class LowKwOrderDto {
    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "商户ID")
    private Long commId;

    @Schema(description = "商户名称")
    private String commName;

    @Schema(description = "场站ID")
    private String siteId;

    @Schema(description = "场站名字")
    private String siteName;

    @Schema(description = "客户ID")
    private Long cusId;

    @Schema(description = "客户名称")
    private String cusName;

    @Schema(description = "车牌号")
    private String carNo;

    @Schema(description = "车辆VIN")
    private String vin;

    @Schema(description = "订单停充上传时间")
    private Date stopTime;

    @Schema(description = "小功率充电时长")
    private Integer lowKwDur;
}
