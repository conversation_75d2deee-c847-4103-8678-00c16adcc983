package com.cdz360.biz.model.trading.site.po;

import com.cdz360.biz.model.FileItem;
import com.cdz360.biz.model.trading.site.type.SiteInspectionStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SiteInspectionRecordPo {

    private Long id;

    @Schema(description = "巡检单号")
    private String no;

    private String siteId;

    @Schema(description = "工单状态")
    private SiteInspectionStatus status;

    @Schema(description = "常规检查项")
    private EvseReport evseReport;

    @Schema(description = "环境安全检查项")
    private EnvReport envReport;

    @Schema(description = "光伏常规检查")
    private PvReport pvReport;

    @Schema(description = "储能常规检查")
    private EssReport essReport;

    @Schema(description = "现场照片")
    private List<FileItem> photos;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "巡检人ID")
    private Long opUid;

    @Schema(description = "工单提交时间")
    private Date reportTime;

    @Schema(description = "质检人ID")
    private Long qcUid;

    @Schema(description = "客户满意度,1-满意，2-不满意")
    private Long isPerfect;

    @Schema(description = "客户建议")
    private String advice;

    @Schema(description = "客户签名")
    private String signImage;

    @Schema(description = "质检时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date qcTime;

    @Schema(description = "质检人备注")
    private String qcRemark;

    @Schema(description = "工单创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "工单最后更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date updateTime;

    @Schema(description = "巡检单类型 1：光； 2：储； 3：充；")
    private Integer inspectionType;

}
