package com.cdz360.biz.model.trading.iot.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class BsChargerPo {

    /**
     * 主键ID
     */
    private Long bcId;

    /**
     * 桩编号
     */
    private String evseNo;

    @Schema(description = "枪头编号")
    private String plugNo;

    /**
     * 充电接口序号
     */
    private Integer connectorId;

    /**
     * 代理商ID
     */
    private String businessId;
    /**
     * 站点ID
     */
    private String stationCode;
    /**
     * 交直流类型**0-交流 1-直流**
     */
    private Integer currentType;

    private String chargerName;

    /**
     * 最后修改时间
     */
    private Date lastModifyTime;
}
