package com.cdz360.biz.model.trading.yw.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "运维工单类型")
@Getter
public enum YwType implements DcEnum {
    UNKNOWN(0, "未知"),
    PV(1, "光伏工单"),
    ESS(2, "储能工单"),
    CHARGE(3, "充电桩工单")
    ;

    @JsonValue
    private int code;

    private String desc;

    YwType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static YwType valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (YwType ywType : values()) {
            if (ywType.code == code) {
                return ywType;
            }
        }

        return YwType.UNKNOWN;
    }
}
