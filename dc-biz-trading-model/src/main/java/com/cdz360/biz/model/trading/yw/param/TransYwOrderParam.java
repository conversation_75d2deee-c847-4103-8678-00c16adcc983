package com.cdz360.biz.model.trading.yw.param;

import com.cdz360.base.model.base.type.UserType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "运维工单转派参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class TransYwOrderParam extends BaseOpParam {

    @Schema(description = "运维工单编号", required = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String ywOrderNo;

    @Schema(description = "转派指定的用户ID", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long uid;

    @Schema(description = "转派指定的用户类型: 0,未知; 1,商户/平台用户; 3,企业客户; 4,系统", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private UserType userType;

    @Schema(description = "转派指定的用户名称", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String userName;

    @Schema(description = "运维人所属商户ID", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long maintCommId;
}
