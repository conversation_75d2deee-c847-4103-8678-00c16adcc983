package com.cdz360.biz.model.trading.rover.vo;

import com.cdz360.biz.model.trading.rover.po.SiteRoverCfgPo;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * SiteRoverCfgVo
 *
 * @since 8/3/2022 9:10 AM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SiteRoverCfgVo extends SiteRoverCfgPo {
    private String siteName;
    private String gid;
    private Long topCommId;
    private List<String> gidList;
}