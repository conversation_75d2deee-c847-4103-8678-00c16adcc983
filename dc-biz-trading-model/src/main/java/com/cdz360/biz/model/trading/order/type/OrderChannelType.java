package com.cdz360.biz.model.trading.order.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 订单来源
 */
@Getter
public enum OrderChannelType implements DcEnum {

    UNKNOWN(0, "未知"),

    EVSE(1, "设备触发"),

    CUS_APP(2, "用户应用"),

    HLHT(3, "互联互通"),

    MGM_WEB(4, "管理后台"),

    ;

    @JsonValue
    private final int code;
    private final String desc;

    OrderChannelType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static OrderChannelType valueOf(int code) {
        for (OrderChannelType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return OrderChannelType.UNKNOWN;
    }


}
