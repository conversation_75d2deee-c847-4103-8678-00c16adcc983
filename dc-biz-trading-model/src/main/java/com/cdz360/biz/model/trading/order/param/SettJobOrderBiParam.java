package com.cdz360.biz.model.trading.order.param;

import com.cdz360.base.model.base.param.TimeFilter;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "充电订单统计参数")
public class SettJobOrderBiParam {

    @Schema(description = "场站Id")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "场站Id列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> siteIdList;

    @Schema(description = "账户类型")
    @JsonInclude(Include.NON_NULL)
    private Integer accountType;

    @Schema(description = "企业客户ID")
    @JsonInclude(Include.NON_NULL)
    private Long corpId;

    @Schema(description = "不包含企业客户ID列表")
    @JsonInclude(Include.NON_NULL)
    private List<Long> excludeCorpIdList;

    @Schema(description = "充电结束时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter chargeStopTimeFilter;

    @Schema(description = "充电开始时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter chargeStartTimeFilter;

    @Schema(description = "订单创建时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter chargeCreateTimeFilter;

    @Schema(description = "以订单最后更新时间作为结算时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter chargeUpdateTimeFilter;

    @Schema(description = "平台（收到）结束充电的时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter stopTimeFilter;

    @Schema(description = "支付时间筛选")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter paymentTimeFilter;

}
