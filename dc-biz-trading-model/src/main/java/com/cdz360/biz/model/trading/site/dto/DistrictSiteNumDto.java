package com.cdz360.biz.model.trading.site.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(description = "按区/县统计场站数量")
public class DistrictSiteNumDto {

    private String provinceCode;
    private String cityCode;
    private String districtPinyin;
    private String districtCode;
    private String districtName;
    @Schema(description = "区/县经度", example = "123.45678")
    private BigDecimal districtLng;

    @Schema(description = "区/县纬度", example = "23.45678")
    private BigDecimal districtLat;

    @Schema(description = "场站数量", example = "123")
    private Long num;

    @Schema(description = "充电桩功率", example = "123")
    private Long cePower;

    @Schema(description = "光伏装机容量", example = "123")
    private Long pvPower;

    @Schema(description = "枪头数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long plugNum;
}
