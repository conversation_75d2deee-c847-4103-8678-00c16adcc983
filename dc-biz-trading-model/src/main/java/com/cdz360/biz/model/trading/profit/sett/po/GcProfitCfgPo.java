package com.cdz360.biz.model.trading.profit.sett.po;

import com.cdz360.biz.model.trading.profit.sett.type.ProfitCfgCalSource;
import com.cdz360.biz.model.trading.profit.sett.type.ProfitCfgCategory;
import com.cdz360.biz.model.trading.profit.sett.type.ProfitCfgTimeTarget;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "结算任务")
public class GcProfitCfgPo {

    @Schema(description = "主键")
    @NotNull(message = "id 不能为 null")
    private Long id;

    @Schema(description = "任务名称")
    @Size(max = 64, message = "name 长度不能超过 64")
    private String name;

    @Schema(description = "收入(INCOME)/支出(EXPENSE)")
    @NotNull(message = "category 不能为 null")
    private ProfitCfgCategory category;

    @Schema(description = "计算来源: FROM_CHARGE_ORDER(来源充电订单); FROM_BI_SYS(来源决策系统)")
    @NotNull(message = "calSource 不能为 null")
    private ProfitCfgCalSource calSource;

    @Schema(description = "结算周期(1~28)")
    private Integer monthDay;

    @Schema(description = "充电订单时间维度【创建时间(1)，支付时间(2)，上传时间(3)，充电开始时间(4)，充电结束时间(5)】")
    private ProfitCfgTimeTarget timeTarget;

    @Schema(description = "结算单生成日期(1~28)")
    private Integer generateDay;

    @Schema(description = "是否有效. true/false")
    private Boolean enable;

    @Schema(description = "关联场站ID列表")
    private List<String> siteIdList;

    @Schema(description = "充电结算公式:{}")
    private List<ChargeOrderRule> chargeOrderRules;

    @Schema(description = "数据决策结算公式:{base:{}, range:[{condition: '', {}},...]}")
    private BiRule biRule;

    @Schema(description = "备注")
    @Size(max = 255, message = "remark 长度不能超过 255")
    private String remark;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间")
    @NotNull(message = "createTime 不能为 null")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "更新时间")
    @NotNull(message = "updateTime 不能为 null")
    private Date updateTime;
}

