package com.cdz360.biz.model.trading.site.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class SiteWeatherPo {

    private Long id;

    private String siteId;

    private String city;

    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date date;

    @Schema(description = "天气")
    private String weather;

    @Schema(description = "温度")
    private String temp;

    @Schema(description = "最高温度")
    private String tempHigh;

    @Schema(description = "最低温度")
    private String tempLow;

    @Schema(description = "天气对应图片的序号")
    private String img;

    @Schema(description = "湿度")
    private String humidity;

    @Schema(description = "气压")
    private String pressure;

    @Schema(description = "风速")
    private String windSpeed;

    @Schema(description = "风向")
    private String windDirect;

    @Schema(description = "风级")
    private String windPower;

    private List<WeatherDaily> daily;

    private List<WeatherHourly> hourly;

}
