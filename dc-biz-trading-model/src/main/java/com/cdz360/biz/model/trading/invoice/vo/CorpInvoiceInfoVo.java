package com.cdz360.biz.model.trading.invoice.vo;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.model.invoice.type.InvoiceChannel;
import com.cdz360.biz.model.invoice.type.InvoiceType;
import com.cdz360.biz.model.trading.invoice.dto.CorpInvoiceInfoDto;
import com.cdz360.biz.model.trading.invoice.dto.InvoicedSalTempRefDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * CorpInvoiceInfoVo
 *
 * @since 3/28/2023 2:20 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "企业开票设置信息")
public class CorpInvoiceInfoVo extends CorpInvoiceInfoDto {
    @Schema(description = "开票设置id", example = "123")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long id;

    @Schema(description = "企业客户所属商户", example = "123")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long commId;

    @Schema(description = "企业平台开票功能是否打开: true -- 打开; false -- 非打开", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean invoiceOpen;

    @Schema(description = "结算类型: UNKNOWN(0)-未知,BALANCE(1)-账户余额扣减(先付费),GUARANTEE(2)-担保消费结算," +
        "POSTPAID(3)-预消费结算(后付费),PARTNER(4)-外部平台结算", format = "java.lang.Integer")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private SettlementType settlementType;

    @Schema(description = "纳税人识别号(开票主体) 通过这个可以查找到开票主体")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String saleTin;

    @Schema(description = "开票主体名称", required = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String saleName;

    @Schema(description = "开具方式")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InvoiceChannel channel;

    @Schema(description = "商品行模板 页面显示使用")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InvoicedSalTempRefDTO tempRefVo;

    // 个人发票模板信息
    @Schema(description = "发票类型: " +
        "PER_COMMON -- 个人普票;" +
        "ENTER_COMMON -- 企业普票;" +
        "ENTER_PROFESSION -- 企业专票")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InvoiceType invoiceType;

    @Schema(description = "企业客户开票抬头模板ID invoiced_model.id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long modelId;

    @Schema(description = "发票抬头")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String name;

    @Schema(description = "电子邮件")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String email;

    @Schema(description = "纳税人识别号(企业专票需要填写)")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String tin;

//    @Schema(description = "省")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private String province;
//
//    @Schema(description = "市")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private String city;
//
//    @Schema(description = "区")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private String area;

    @Schema(description = "详细街道信息(企业地址)")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String address;

    @Schema(description = "联系方式(企业联系方式)")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String tel;

    @Schema(description = "开户银行")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String bank;

    @Schema(description = "银行账号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String bankAccount;

    @Schema(description = "收件人")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String receiverName;//收件人

    @Schema(description = "收件人手机号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String receiverMobilePhone;//收件人手机号

    @Schema(description = "收件人省")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String receiverProvince;    //收件人省

    @Schema(description = "收件人市")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String receiverCity; // 收件人市

    @Schema(description = "收件人区")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String receiverArea;// 收件人区

    @Schema(description = "收件人详细地址")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String receiverAddress;//收件人地址

    @Schema(description = "开户行 银行开户行(支行)")
    private String saleBank;

    @Schema(description = "开户行账号 银行开户账号")
    private String saleAccount;

    /**
     * 更新企业开票信息校验
     */
    public void saveCheck() {
        if (this.getInvoiceOpen() == null) {
            throw new DcArgumentException("企业平台开票功能是否打开值标识缺失");
        }

        if (this.getCorpId() == null
            || this.getUid() == null
            || StringUtils.isBlank(this.getCorpName()) // 用于记录日志
        ) {
            throw new DcArgumentException("企业客户信息不全");
        }

        if (!this.getInvoiceOpen()) {
            return;
        }

        if (this.getInvoiceWay() == null) {
            throw new DcArgumentException("请选择开票方式");
        }

//        if (StringUtils.isBlank(this.getSaleTin())) {
//            throw new DcArgumentException("请提供开票主体纳税人识别号");
//        }

        if (null == this.getTempSalId()) {
            throw new DcArgumentException("请提供开票主体ID");
        }

        if (this.getProductTempId() == null) {
            throw new DcArgumentException("请选择商品行模板");
        }

        if (this.getInvoiceType() == null) {
            throw new DcArgumentException("请选择开票种类");
        }

        if (StringUtils.isBlank(this.getName())) {
            throw new DcArgumentException("请输入发票抬头");
        }

        switch (this.getInvoiceType()) {
            case ENTER_PROFESSION:
//                if (StringUtils.isBlank(this.getSaleTin())) {
//                    throw new DcArgumentException("请输入纳税人识别号");
//                }

                if (StringUtils.isBlank(this.getBankAccount()) ||
                    StringUtils.isBlank(this.getBankAccount())) {
                    throw new DcArgumentException("请提供完整的开户行信息");
                }

                if (StringUtils.isBlank(this.getAddress()) ||
                    StringUtils.isBlank(this.getTel())) {
                    throw new DcArgumentException("请提供完整的企业信息");
                }

                if (StringUtils.isBlank(this.getReceiverName()) ||
                    StringUtils.isBlank(this.getReceiverMobilePhone()) ||
                    StringUtils.isBlank(this.getReceiverProvince()) ||
                    StringUtils.isBlank(this.getReceiverCity()) ||
                    StringUtils.isBlank(this.getReceiverArea()) ||
                    StringUtils.isBlank(this.getReceiverAddress())) {
                    throw new DcArgumentException("请提供完整的收件人信息");
                }
                break;
            case ENTER_COMMON:
//                if (StringUtils.isBlank(this.getSaleTin())) {
//                    throw new DcArgumentException("请输入纳税人识别号");
//                }
            case PER_COMMON:
                if (StringUtils.isBlank(email)) {
                    throw new DcArgumentException("请提供电子邮件");
                }
                break;
            default:
                throw new DcArgumentException("请选择正确的开票种类");
        }
    }
}