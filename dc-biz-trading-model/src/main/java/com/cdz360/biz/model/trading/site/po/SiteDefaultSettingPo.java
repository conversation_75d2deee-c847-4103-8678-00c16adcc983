package com.cdz360.biz.model.trading.site.po;

import com.cdz360.biz.model.site.type.OvertimeParkingChargeUserType;
import com.cdz360.biz.model.trading.site.type.OvertimeParkingChargePartType;
import com.cdz360.biz.model.trading.site.type.OvertimeParkingLimitType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SiteDefaultSettingPo {
    private Long id;
    /**
     * 场站编号
     */
    private String siteId;
    /**
     * 计费模板id
     */
    private Long chargeId;
    /**
     * 管理员密码
     */
    private String adminPassword;
    /**
     * 二级管理员密码
     */
    private String level2Password;
    /**
     * 白天音量
     */
    private Integer dayVolume;
    /**
     * 夜晚音量
     */
    private Integer nightVolume;
    /**
     * 二维码url
     */
    private String url;
    /**
     * 是否支持充电记录查询 （1是0否）
     */
    private Boolean isQueryChargeRecord;
    /**
     * 是否支持充电记录查询 （1是0否）
     */
    private Boolean isTimedCharge;
    /**
     * 是否支持无卡充电 （1是0否）
     */
    private Boolean isNoCardCharge;
    /**
     * 是否支持扫码充电 （1是0否）
     */
    private Boolean isScanCharge;
    /**
     * 是否支持Vin码充电 （1是0否）
     */
    private Boolean isVinCharge;
    /**
     * 是否支持刷卡充电 （1是0否）
     */
    private Boolean isCardCharge;
    /**
     * 是否支持定额电量充电 （1是0否）
     */
    private Boolean isQuotaEleCharge;
    /**
     * 是否支持固定金额充电 （1是0否）
     */
    private Boolean isQuotaMoneyCharge;
    /**
     * 是否支持固定时长充电 （1是0否）
     */
    private Boolean isQuotaTimeCharge;
    /**
     * 国际协议
     */
    private String internationalAgreement;
    /**
     * 自动停充 （1是0否）
     */
    private Integer isAutoStopCharge;
    /**
     * 均/轮充设置 0均充 1轮充
     */
    private Integer avgOrTurnCharge;
    /**
     * 合充开关 （1开0关）
     */
    private Integer isCombineCharge;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 操作人id
     */
    private Long updateByUserid;

    /**
     * 是否启用SOC限制. true: 启用soc限制
     */
    private Boolean limitSoc;

//    /**
//     * 无卡启动订单结算账户的商户会员id，设为0表示不开启
//     */
//    private Long noCardChargeMerchantId;
    @Schema(description = "无卡启动订单结算账户 结合noCardPayAccountType使用，" +
                    "授信账户为(t_corp_user.id);" +
                    "商户会员为(t_comm_cus_ref.id);" +
                    "个人账户为(t_user.id)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long noCardPayAccountId;

    @Schema(description = "无卡启动充电订单结算账户类型: 1(个人账户); 2(授信账户); 3(会员账户) com.cdz360.base.model.base.type.PayAccountType")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer noCardPayAccountType;

    @Schema(description = "停充超时充电允许次数 停充超时收费启用时必填")
    private Integer overtimeParkingNum;

    @Schema(description = "停充超时单次允许超时时间,单位: 分钟 停充超时收费启用时必填")
    private Integer overtimeParkingTime;

    @Deprecated
    @Schema(description = "超时收费单价, 单位: 元/分钟")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal overtimeParkingPrice;

    @Schema(description = "停车满减电量,入参为0将修改数据库字段为null，入参为null将不做修改")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer parkCouponKwh;

    @Schema(description = "满电量减停车分钟,入参为0将修改数据库字段为null，入参为null将不做修改")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer parkCouponTime;

    @Schema(description = "车场编号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long parkId;

    @Schema(description = "车厂编号-数据签名")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String parkSignKey;

    @Schema(description = "第三方停车对接类型: IST艾视特，ZK中控")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String parkType;

    @Schema(description = "ZK中控-AppId、宁停车appKey")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String parkAppId;

    @Schema(description = "ZK中控-AppSecret、宁停车secret")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String parkAppSecret;

    @Schema(description = "停车、道闸自定义配置")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String parkConfig;

    //使用t_site.parkTimeoutFee作为开关
//    @Schema(description = "停充超时充电控制 开关. true: 启用; false: 停用")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Boolean overtimeParkingTimeoutEnable;

    @Schema(description = "停充超时充电控制，限制方式")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private OvertimeParkingLimitType overtimeParkingLimitType;

    @Schema(description = "停充超时，收费用户 类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private OvertimeParkingChargePartType overtimeParkingChargePartType;

    @Schema(description = "停充超时，设置方式 类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private OvertimeParkingChargeUserType overtimeParkingChargeUserType;

}
