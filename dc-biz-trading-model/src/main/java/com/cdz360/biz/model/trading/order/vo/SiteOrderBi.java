package com.cdz360.biz.model.trading.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.text.MessageFormat;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
@Data
@Schema(description = "场站充电统计")
@EqualsAndHashCode(callSuper = true)
public class SiteOrderBi extends OrderBi {


    @Schema(description = "桩的充电统计")
    private List<EvseOrderBi> evseList;

    @Override
    public String toString() {
        return MessageFormat.format("{0}, evseList.size: {1}", super.toString(), this.evseList.size());
    }
}
