package com.cdz360.biz.model.trading.appoint.vo;

import com.cdz360.biz.model.trading.hlht.po.HlhtSitePo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class AppointOrderVo  {

   private String appointNo;

   private String carNumber;

   @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
   private Date endTime;

}
