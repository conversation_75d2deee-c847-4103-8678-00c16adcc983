package com.cdz360.biz.model.trading.order.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum OrderCompleteCode implements DcEnum {

    UNKNOWN(999, 999, "未知"),
    C00(0, 0, "充满"),
    C01(1, 1, "车端停充"),
    C02(2, 2, "金额停充"),
    C03(3, 3, "SOC停充"),
    C04(4, 4, "电量停充"),
    C05(5, 5, "时间停充"),
    C06(6, 6, "桩端手动停充-刷卡"),
    C07(7, 7, "桩端手动停充-停充码"),
    C08(8, 8, "桩端手动停充-停充按钮"),
    C09_1(9, 9001, "平台停充"),
    C09_2(9, 9002, "用户停充"),
    C09_3(9, 9003, "互联互通停充"),
    CFF(255, 255, "异常停充"),
    ;

    private final int label;

    @JsonValue
    private final int code;

    private final String desc;

    OrderCompleteCode(int label, int code, String desc) {
        this.label = label;
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static OrderCompleteCode valueOfByCode(Object codeIn) {
        if (codeIn == null) {
            return OrderCompleteCode.UNKNOWN;
        }
        long code = 0;
        if (codeIn instanceof OrderCompleteCode) {
            return (OrderCompleteCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn);
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (OrderCompleteCode status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return OrderCompleteCode.UNKNOWN;
    }

    public static OrderCompleteCode valueOfByLabel(Object codeIn) {
        if (codeIn == null) {
            return OrderCompleteCode.UNKNOWN;
        }
        long code = 0;
        if (codeIn instanceof OrderCompleteCode) {
            return (OrderCompleteCode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn);
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (OrderCompleteCode status : values()) {
            if (status.label == code) {
                return status;
            }
        }
        return OrderCompleteCode.UNKNOWN;
    }

}
