package com.cdz360.biz.model.trading.park.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * ParkBaseReq
 *
 * @since 8/16/2022 1:29 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ParkBaseReq {
    private String siteId;
    private String carNo;
    private String seqNo;
    private String sign;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inTime;
}