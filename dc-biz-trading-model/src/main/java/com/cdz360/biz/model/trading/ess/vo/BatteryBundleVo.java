package com.cdz360.biz.model.trading.ess.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class BatteryBundleVo {

    @Schema(title = "电池蔟设备序列号")
    @JsonInclude(Include.NON_EMPTY)
    private String dno;


    @Schema(title = "电池堆设备序列号")
    @JsonInclude(Include.NON_EMPTY)
    private String stackDno;


    @Schema(title = "BMS设备序列号")
    @JsonInclude(Include.NON_EMPTY)
    private String bmsDno;

    @Schema(title = "电池蔟在电池堆内的序号")
    @JsonInclude(Include.NON_NULL)
    private Integer idx;


    @Schema(description = "ESS设备序号")
    @NotNull(message = "essDno 不能为 null")
    @Size(max = 32, message = "essDno 长度不能超过 32")
    private String essDno;
}
