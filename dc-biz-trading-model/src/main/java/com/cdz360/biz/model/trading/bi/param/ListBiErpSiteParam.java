package com.cdz360.biz.model.trading.bi.param;

import com.cdz360.biz.model.common.param.BaseListParam;
import com.cdz360.biz.model.trading.bi.type.ErpSiteStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "Erp推送记录查询参数")
@EqualsAndHashCode(callSuper = true)
public class ListBiErpSiteParam extends BaseListParam {
    @Schema(description = "推送状态")
    private List<ErpSiteStatus> statusList;

    @Schema(description = "场站ID")
    private List<String> siteIdList;

    @Schema(description = "收入时间: 月份的第一天", example = "2020-03-01 00:00:00")
    private LocalDateTime incomeTime;
}
