package com.cdz360.biz.model.trading.invoice.po;

import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.base.model.base.vo.BaseObject;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "企业开票记录和订单关联表")
public class InvoiceRecordOrderRefPo extends BaseObject {

    @Schema(description = "企业客户开票方式: 充值预开票(PRE_PAY)、充电后开票(POST_CHARGER)、后付费账单开票(POST_SETTLEMENT)")
    private InvoicingMode invoiceWay;

    @Schema(description = "订单号: 开票方式决定属于哪一类订单")
    private String orderNo;

    @Schema(description = "订单对应的可开票金额")
    private BigDecimal invoiceAmount;

    @Schema(description = "企业申请开票记录单号")
    private String applyNo;

    @Schema(description = "用来存储账单名称，用来查询使用")
    private String remark;
}
