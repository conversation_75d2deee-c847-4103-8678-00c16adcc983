package com.cdz360.biz.model.trading.evse;

import com.cdz360.biz.model.iot.vo.ChargeV2;
import com.cdz360.biz.model.iot.vo.WhiteCardCfgVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class EvseInfo {

    private String siteId;

    private String siteName;

    private String evseName;

    @Schema(description = "桩协议版本")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer protocolVer;

    private String templateCode;

    private String templateName;

    private List<ChargeV2> templateList;

    @Schema(description = "白天音量.")
    private Integer dayVolume;

    @Schema(description = "夜间音量.")
    private Integer nightVolume;

    @Schema(description = "桩端显示的二维码 URL.")
    private String qrUrl;

    @Schema(description = "是否支持定时充电.")
    private Boolean timedCharge;

    @Schema(description = "是否支持充电记录查询.")
    private Boolean queryChargeRecord;

    @Schema(description = "是否支持无卡充电.")
    private Boolean noCardCharge;

    @Schema(description = "是否支持扫码充电.")
    private Boolean qrCharge;

    @Schema(description = "是否支持VIN充电.")
    private Boolean vinCharge;

    @Schema(description = "是否支持刷卡充电.")
    private Boolean cardCharge;

    @Schema(description = "是否支持按金额充.")
    private Boolean amount;

    @Schema(description = "是否支持按电量充.")
    private Boolean kwh;

    @Schema(description = "是否支持按时间充.")
    private Boolean time;

    @Schema(description = "国际协议")
    private String internationalAgreement;

    @Schema(description = "自动停充 （1是0否）")
    private Boolean isAutoStopCharge;

    @Schema(description = "均/轮充设置 0均充 1轮充")
    private Integer avgOrTurnCharge;

    @Schema(description = "合充开关 （1开0关）")
    private Boolean isCombineCharge;

    @Schema(description = "是否支持辅电手动切换")
    private Boolean heating;

    @Schema(description = "辅电电压设置")
    private Integer heatingVoltage;

    @Schema(description = "是否支持电池反接检测")
    private Boolean batteryCheck;

    @Schema(description = "是否支持主动安全检测")
    private Boolean securityCheck;

    @Schema(description = "是否支持不拔枪充电")
    private Boolean constantCharge;

    @Schema(description = "是否支持插枪获取VIN")
    private Boolean vinDiscover;

    @Schema(description = "是否可见订单信息隐私")
    private Boolean orderPrivacySetting;

    @Schema(description = "订单账号显示类型")
    private Integer accountDisplayType;

    @Schema(description = "紧急充电卡列表")
    private List<WhiteCardCfgVo> whiteCards;
}
