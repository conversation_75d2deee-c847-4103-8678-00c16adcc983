package com.cdz360.biz.model.trading.site.type;

import lombok.Getter;

@Getter
public enum SiteGcType {

    UNKNOWN(0, "未知"),
    TJYY(1, "投建运营"),
    YZDS(2, "以租代售"),
    CZL(3, "纯租赁"),
    EPC_O(4, "EPC+O"),
    DSDF(5, "销售的代收代付"),
    DYY(6, "代运营"),
    WTYY(7, "委托运营"),
    XSCZ(8, "销售场站"),
    BOT(9, "BOT"),
    SG(10, "收购"),
    TZYY(11, "停止运营"),
    HZYY(12, "合作运营"),
    XS_ZFT(14, "销售+直付通"),
    ;

    private final int code;
    private final String desc;

    SiteGcType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SiteGcType valueOf(int code) {
        for (SiteGcType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return SiteGcType.UNKNOWN;
    }
}
