package com.cdz360.biz.model.trading.bill.po;


import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.biz.model.trading.bill.type.DailyBillCheckResult;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;



@Data

@Accessors(chain = true)

@Schema(description = "外部对账单")
@EqualsAndHashCode(callSuper = true)
public class ZftThirdOrderPo extends BaseObject {



	@Schema(description = "平台支付流水号")

	@Size(max = 64, message = "platformNo 长度不能超过 64")

	private String platformNo;



	@Schema(description = "渠道流水号")

	@Size(max = 64, message = "channelNo 长度不能超过 64")

	private String channelNo;



	@Schema(description = "支付平台对账结果: FULL_MATCH(完全匹配); AMOUNT_NOT_MATCH(金额不匹配); NO_NOT_MATCH(未找到对应订单); OTHERS(其他)")

	@NotNull(message = "checkResult 不能为 null")

	private DailyBillCheckResult checkResult;



	@Schema(description = "交易类型: IN_FLOW(收入), OUT_FLOW(支出)")

	@NotNull(message = "tradeType 不能为 null")

	private DepositFlowType tradeType;



	@Schema(description = "交易金额（单位: 元）")

	@NotNull(message = "tradeAmount 不能为 null")

	private BigDecimal tradeAmount;



	@Schema(description = "手续费（单位: 元）")

	private BigDecimal payFee;



	@Schema(description = "交易时间")

	@NotNull(message = "tradeTime 不能为 null")

	private Date tradeTime;



	@Schema(description = "对账单ID(t_zft_daily_bill.id)")

	@NotNull(message = "dailyBillId 不能为 null")

	private Long dailyBillId;

	@Schema(description = "企业ID")

	private Long corpId;

	@Schema(description = "合作方订单号")

	private String openOrderId;

}

