package com.cdz360.biz.model.trading.site.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SiteWeatherVo {

    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date date;

    @Schema(description = "天气")
    private String weather;

    @Schema(description = "温度")
    private String temp;

    @Schema(description = "天气对应图片的序号")
    private String img;

    @Schema(description = "湿度")
    private String humidity;

    @Schema(description = "风速")
    private String windSpeed;

    @Schema(description = "降水量")
    private String precipitation;

    @Schema(description = "能见度")
    private String visibility;

    @Schema(description = "云量")
    private String cloudage;

    @Schema(description = "气压")
    private String pressure;

    @Schema(description = "日出时间", example = "06:36")
    private String sunRise;

    @Schema(description = "日落时间", example = "19:14")
    private String sunSet;

    @Schema(description = "未来三天天气")
    private List<SimpleWeather> nextThreeDays;

    @Data
    @Accessors(chain = true)
    public static class SimpleWeather {
        @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
        private Date date;

        @Schema(description = "云量")
        private String cloudage;

        @Schema(description = "最高气温")
        private String tempHigh;

        @Schema(description = "最低气温")
        private String tempLow;

        private String weather;
    }

}
