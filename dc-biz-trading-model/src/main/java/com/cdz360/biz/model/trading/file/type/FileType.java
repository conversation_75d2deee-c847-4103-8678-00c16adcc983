package com.cdz360.biz.model.trading.file.type;

import lombok.Getter;

/**
 * 收费周期
 */
@Getter
public enum FileType {

    IMG(0, "图片"),

    FILE(1, "文件");
    private final int code;
    private final String desc;

    FileType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static FileType valueOf(int code) {
        for (FileType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return FileType.IMG;
    }

}
