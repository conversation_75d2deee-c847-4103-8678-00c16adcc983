package com.cdz360.biz.model.trading.contract.param;

import com.cdz360.biz.model.trading.contract.type.ContractType;
import com.cdz360.biz.model.trading.contract.type.PayCycle;
import com.cdz360.biz.model.trading.contract.type.PayType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 新增合约
 */
@Data
@Accessors(chain = true)
@Schema(description = "新增合约")
public class AddContractParam {

    @Schema(description = "合约ID")
    private Long contractId;

    @Schema(description = "合约名称", required = true)
    private String contractName;

    @Schema(description = "合约编号")
    private String contractNo;

    @Schema(description = "合同类型", required = true)
    private ContractType contractType;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "互联互通合约时，运营商标志")
    private String operatorCode;

    @Schema(description = "合约文本", required = true)
    private List<Long> contractLinks;

    @Schema(description = "所属商户", required = true)
    private Long commId;

    @Schema(description = "收费方式", required = true)
    private PayType payType;

    @Schema(description = "费用", required = true)
    private BigDecimal fee;

    @Schema(description = "合约金额")
    private BigDecimal amount;

    @Schema(description = "合约详情")
    private Map detail;

    @Schema(description = "收费周期", required = true)
    private PayCycle payCycle;

    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "收费期限", required = true)
    private Date payTime;

    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "签订日期", required = true)
    private Date signTime;

    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "开始日期")
    private Date startTime;

    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "截止日期")
    private Date endTime;

    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "质保期")
    private Date qualityTime;

    @Schema(description = "所属场站", required = true)
    private List<String> siteIdList;

    @Schema(description = "ID链", hidden = true)
    private String idChain;

    @Schema(description = "录入人ID", hidden = true)
    private Long sysUid;

    @Schema(description = "结算成本")
    private Boolean isSettle;

    @Schema(description = "结算详情")
    private String settleInfo;

    @Schema(description = "状态")
    private Long status;

    @Schema(description = "合约摘要")
    private String remark;


}
