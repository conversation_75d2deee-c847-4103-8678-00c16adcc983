package com.cdz360.biz.model.trading.site.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.ToString;

/**
 * SiteConfStartList
 *
 * @since 10/27/2020 1:28 PM
 * <AUTHOR>
 */
@Schema(description = "后台启动配置保留/删除列表")
@Data
@ToString(callSuper = true)
public class SiteConfStartList {
    private List<SiteVo> removeList = new ArrayList<>();
    private List<SiteVo> remainList = new ArrayList<>();
}