package com.cdz360.biz.model.trading.order.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.model.base.type.PayAccountType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Schema(description = "充值列表查询参数")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListPayBillParam extends BaseListParam {

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "集团商户ID", example = "34474")
    private Long topCommId;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "商户ID链", example = "34474,34475", hidden = true)
    private String commIdChain;

    @Schema(description = "用户Id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long userId;

    @Schema(description = "商户会员所属商户ID 用于查询商户会员充值")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long commId;

    @Schema(description = "充值订单号 支持模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderId;

    @Schema(description = "充值订单号列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> orderIdList;

    @Schema(description = "不包含充值订单号列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> exclusiveOrderIdList;

    @Schema(description = "企业客户申请开票记录的申请单号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String applyNo;

    @Schema(description = "充值时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter payTimeFilter;

    @Schema(description = "充值账户: UNKNOWN-未知,PERSONAL-个人现金账户," +
        "CREDIT-集团授信账户,COMMERCIAL-商户商户会员,PREPAY-即充即退")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<PayAccountType> accountTypeList;
}
