package com.cdz360.biz.model.trading.site.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.biz.model.trading.site.type.SiteChargeJobStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Schema(description = "定时充电任务列表查询入参")
@EqualsAndHashCode(callSuper = true)
public class ChargeJobParam extends BaseListParam {

    @Schema(description = "任务ID")
    private Long jobId;

    @Schema(description = "任务名称")
    private String jobName;

    @Schema(description = "任务状态名称")
    private SiteChargeJobStatus jobStatus;

    @Schema(description = "站点名称")
    private String siteId;

    @Schema(required = false)
    private String commIdChain;
}
