package com.cdz360.biz.model.trading.site.vo;

import com.cdz360.biz.model.trading.site.po.BiSiteGcMonthIncomePo;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * BiSiteGcMonthIncomeVo
 *
 * @since 5/11/2023 1:15 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "运营场站月收入项扩展")
@EqualsAndHashCode(callSuper = true)
public class    BiSiteGcMonthIncomeVo extends BiSiteGcMonthIncomePo {
    @Schema(description = "充电收入")
    private BigDecimal elecAndServFee;

    @Schema(description = "收益")
    private BigDecimal elecAndServProfit;

    @Schema(description = "服务费收入")
    private BigDecimal servProfitIncome;

}