package com.cdz360.biz.model.trading.profit.sett.param;

import com.cdz360.biz.model.common.param.BaseListParam;
import com.cdz360.biz.model.trading.profit.sett.type.ProfitCfgCategory;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "获取收益配置列表参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListProfitCfgParam extends BaseListParam {

    @Schema(description = "任务名称(支持模糊查询)")
    @JsonInclude(Include.NON_EMPTY)
    private String nameLike;

    @Schema(description = "场站ID")
    @JsonInclude(Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "场站ID列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> siteIdList;

    @Schema(description = "任务状态:(true-进行中;false-已停用)")
    @JsonInclude(Include.NON_NULL)
    private Boolean enable;

    @Schema(description = "结算任务生成日期")
    @JsonInclude(Include.NON_NULL)
    private Integer generateDay;

    @Schema(description = "收入(INCOME)/支出(EXPENSE)")
    @JsonInclude(Include.NON_NULL)
    private ProfitCfgCategory category;
}
