package com.cdz360.biz.model.trading.site.param;

import com.cdz360.biz.model.trading.profit.sett.type.ProfitCfgTimeTarget;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@Accessors(chain = true)
@Schema(description = "收益统计参数")
public class SettJobProfitBiParam {

    @Schema(description = "场站Id")
    @JsonInclude(Include.NON_EMPTY)
    private String siteId;

    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @Schema(description = "年月(不考虑)")
    private LocalDate month;

    @Schema(description = "充电订单时间维度【创建时间(1)，支付时间(2)，上传时间(3)，充电开始时间(4)，充电结束时间(5)】")
    private ProfitCfgTimeTarget timeTarget;
}
