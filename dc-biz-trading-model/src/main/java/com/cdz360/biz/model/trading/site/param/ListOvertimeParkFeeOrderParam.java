package com.cdz360.biz.model.trading.site.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "获取超停收费订单列表")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListOvertimeParkFeeOrderParam extends BaseListParam {

    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "用户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long uid;

    @Schema(description = "企业ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long corpId;

    @Schema(description = "支付状态0、100")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer status;

    @Schema(description = "支付状态0、100列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Integer> statusList;

    @Schema(description = "取消状态0未取消、100已取消")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer cancelStatus;

    @Schema(description = "超停收费充电订单计算结束时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter calTimeFilter;

    @Schema(description = "精确订单编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderNo;

    @Schema(description = "仅用作excel导出使用", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ExcelPosition excelPosition;
}
