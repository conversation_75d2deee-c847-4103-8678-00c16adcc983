package com.cdz360.biz.model.trading.site.po;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class WeatherHourly {

    @Schema(description = "时间点", example = "16:00")
    private String time;

    private String weather;

    @Schema(description = "温度")
    private String temp;

    @Schema(description = "天气对应图片的序号")
    private String img;
}
