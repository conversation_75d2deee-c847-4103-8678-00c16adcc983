package com.cdz360.biz.model.trading.contract.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
@Schema(description = "合约类型列表")
public class ContractTypeDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private String label;
    private  int code;
    private String desc;
}
