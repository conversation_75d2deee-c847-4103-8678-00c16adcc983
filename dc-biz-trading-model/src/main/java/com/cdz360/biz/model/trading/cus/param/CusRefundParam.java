package com.cdz360.biz.model.trading.cus.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Schema(description = "用户退款请求参数")
public class CusRefundParam extends CusRefundAllParam {

    @Schema(description = "退款原始充值订单号")
    private String orderId;

}
