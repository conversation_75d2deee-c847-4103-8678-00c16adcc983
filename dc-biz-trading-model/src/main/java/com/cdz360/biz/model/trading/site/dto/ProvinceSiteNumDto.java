package com.cdz360.biz.model.trading.site.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;


@Data
@Schema(description = "按省统计场站数量")
public class ProvinceSiteNumDto {

    private String provincePinyin;
    private String provinceCode;
    private String provinceName;
    @Schema(description = "省份经度", example = "123.45678")
    private BigDecimal provinceLng;

    @Schema(description = "省份纬度", example = "23.45678")
    private BigDecimal provinceLat;

    @Schema(description = "场站数量", example = "123")
    private Long num;

    @Schema(description = "充电桩功率", example = "123")
    private Long cePower;

    @Schema(description = "光伏装机容量", example = "123")
    private Long pvPower;

    @Schema(description = "枪头数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long plugNum;
}
