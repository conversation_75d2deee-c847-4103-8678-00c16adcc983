package com.cdz360.biz.model.trading.site.param;

import com.cdz360.base.model.base.param.BaseListParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Schema(description = "用户收藏场站列表列表查询请求")
public class ListFavorSiteParam extends BaseListParam {
    @Schema(description = "场站Id列表", required = false, hidden = true)
    private List<String> siteIds;
}
