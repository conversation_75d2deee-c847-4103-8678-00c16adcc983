package com.cdz360.biz.model.trading.site.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class WeatherDaily {

    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date date;

    @Schema(description = "星期几")
    private String week;

    @Schema(description = "日出时间", example = "06:36")
    private String sunRise;

    @Schema(description = "日落时间", example = "19:14")
    private String sunSet;

    private Info day;

    private Info night;

    @Data
    @Accessors(chain = true)
    public static class Info {

        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String weather;

        @Schema(description = "最高温度")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String tempHigh;

        @Schema(description = "最低温度")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String tempLow;

        @Schema(description = "天气对应图片的序号")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String img;

        @Schema(description = "风向")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String windDirect;

        @Schema(description = "风级")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private String windPower;
    }
}
