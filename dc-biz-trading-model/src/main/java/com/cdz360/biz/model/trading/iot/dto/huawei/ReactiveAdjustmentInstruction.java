package com.cdz360.biz.model.trading.iot.dto.huawei;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 无功调节指令
 */
@Getter
public enum ReactiveAdjustmentInstruction implements DcEnum {

    UNKNOWN(0, "未知"),
    POWER_FACTOR(40122, "功率因数"),
    Q_S_ADJUSTMENT(40123, "Q/S调节"),
    REACTIVE_POWER_COMPENSATION_NIGHT(40129, "夜间无功功率补偿（kVar）"),
    REACTIVE_NIGHT_Q_S(42809, "夜间无功Q/S"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    ReactiveAdjustmentInstruction(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static ReactiveAdjustmentInstruction valueOf(Object codeIn) {
        if (codeIn == null) {
            return ReactiveAdjustmentInstruction.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof ReactiveAdjustmentInstruction) {
            return (ReactiveAdjustmentInstruction) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (ReactiveAdjustmentInstruction type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return ReactiveAdjustmentInstruction.UNKNOWN;
    }

}
