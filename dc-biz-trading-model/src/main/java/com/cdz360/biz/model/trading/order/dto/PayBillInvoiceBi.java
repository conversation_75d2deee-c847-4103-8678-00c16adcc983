package com.cdz360.biz.model.trading.order.dto;

import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.biz.model.annotations.ExcelField;
import com.cdz360.biz.model.trading.deposit.vo.DepositSourceType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "企业客户开票数据统计")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class PayBillInvoiceBi extends BaseObject implements Serializable {

    @ExcelField(title = "订单号", sort = 1)
    @Schema(description = "充值订单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderId;

    @ExcelField(title = "实际金额(元)", sort = 12)
    @Schema(description = "充值实际金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal amount;

    @ExcelField(title = "赠送金额(元)", sort = 14)
    @Schema(description = "充值赠送金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal freeAmount;

    @ExcelField(title = "充值来源", sort = 4,
        convert = "com.cdz360.biz.model.trading.deposit.vo.DepositSourceType")
    @Schema(description = "充值来源")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private DepositSourceType sourceType;

    @ExcelField(title = "充值时间", sort = 18, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "支付时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payTime;

    @ExcelField(title = "开票金额(元)", sort = 6)
    @Schema(description = "可开票金额")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal canInvoiceAmount;

    @ExcelField(title = "充值总额(元)", sort = 10)
    @Schema(description = "充值总额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal totalAmount;

    @Schema(description = "已完结的增加申请的关联流程ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String procInstId;

    @Schema(description = "未完结的关联流程ID, 仅减少的流程有值，给开票申请锁定用的")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String runProcInstId;

    @Schema(description = "支付账户名称")
    private String outAccountName;

}
