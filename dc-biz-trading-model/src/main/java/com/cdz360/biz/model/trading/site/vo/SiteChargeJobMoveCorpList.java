package com.cdz360.biz.model.trading.site.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.ToString;

/**
 * SiteChargeJobMoveCorpList
 * 
 * @since 10/27/2020 4:30 PM
 * <AUTHOR>
 */
@Schema(description = "启动定时任务配置保留/删除列表")
@Data
@ToString(callSuper = true)
public class SiteChargeJobMoveCorpList {
    private List<SiteChargeJobVo> removeList = new ArrayList<>();
    private List<SiteChargeJobVo> remainList = new ArrayList<>();
}