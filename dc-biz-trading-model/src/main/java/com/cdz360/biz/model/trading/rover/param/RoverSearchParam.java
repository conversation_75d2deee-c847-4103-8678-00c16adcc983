package com.cdz360.biz.model.trading.rover.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.trading.rover.type.RoverStatusType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * RoverSearchParam
 *
 * @since 7/27/2022 2:34 PM
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class RoverSearchParam extends BaseListParam {
    private String roverNoLike;
    private String roverNameLike;

    @Schema(description = "巡查时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter roverTimeRange;

    private List<RoverStatusType> statusTypes;
    private String siteNameLike;
    private Long siteCommId;

    private String siteId;

    private Long roverUid;
    private String commIdChain;

    // 下载导出文件名
    private String exFileName;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}