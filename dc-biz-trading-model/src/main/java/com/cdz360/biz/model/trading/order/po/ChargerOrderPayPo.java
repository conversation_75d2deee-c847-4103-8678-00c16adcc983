package com.cdz360.biz.model.trading.order.po;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.model.charge.type.OrderPayStatus;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.trading.order.type.ChargerOrderRefundStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "订单结算信息表")
public class ChargerOrderPayPo {

    public ChargerOrderPayPo() {

    }

    public ChargerOrderPayPo(String orderNo) {
        this.orderNo = orderNo;
    }

    @Schema(description = "订单号")
    @NotNull(message = "orderNo 不能为 null")
    @Size(max = 32, message = "orderNo 长度不能超过 32")
    private String orderNo;

    @Schema(description = "结算账户类型. 0,未知; 1,平台余额账户; 2,企业授信账户; 3,商户会员账户; 4,即充即退")
    private PayAccountType accountType;

    @Schema(description = "账户ID")
    private Long accountId;

    @Schema(description = "结算方式，0:未知，1：后付费，2：预付费，3：先付费（固定收费：不退还费用），4：先付费（实时计费：退还费用）")
    private Integer payMode;

    @Schema(description = "支付终端")
    private Integer payClient;

    @Schema(description = "0未知; 1,待支付; 2,已支付(即充即退); 3,待退款(即充即退); 4,退款失败(即充即退); 8,超额待支付; 9,已支付(结束);10,已取消(未支付结束)")
    private OrderPayStatus payStatus;

    @Schema(description = "退款状态. 0,未知/无需退款. 1，初始化，2，已提交，3已退款， 4，失败")
    @NotNull(message = "refundStatus 不能为 null")
    private ChargerOrderRefundStatus refundStatus;

    @Schema(description = "订单金额, 单位'元'")
    private BigDecimal orderOriginFee;

    @Schema(description = "订单金额, 单位'元'. 使用协议价/卡券抵扣结算后金额")
    private BigDecimal orderFee;

    @Schema(description = "电费原始金额，桩端传递金额/按电价结算金额")
    private BigDecimal elecOriginFee;

    @Schema(description = "电费. 如果有协议价，则为协议价结算/卡券抵扣后的金额，否则同elecOriginFee")
    private BigDecimal elecFee;

    @Schema(description = "电费实收金额, 现金支付部分的金额(不含赠送金额消费)")
    private BigDecimal elecCostFee;

    @Schema(description = "赠送金抵扣电费")
    private BigDecimal elecFreeFee;

    @Schema(description = "服务费原始金额，桩端传递金额/按服务费原价结算金额")
    private BigDecimal servOriginFee;

    @Schema(description = "服务费, 单位'元'")
    private BigDecimal servFee;

    @Schema(description = "服务费实收金额,现金支付部分的金额(不含赠送金额消费)")
    private BigDecimal servCostFee;

    @Schema(description = "赠送金抵扣服务费金额")
    private BigDecimal servFreeFee;

    @Schema(description = "优惠券金额（单位分）")
    private BigDecimal couponAmount;

    @Schema(description = "积分体系优惠金额（单位分）")
    private BigDecimal scoreAmount;

    @Schema(description = "积分体系，服务费折扣，单位：%")
    private BigDecimal scoreServFeeDiscount;

    @Schema(description = "即充即退充值金额")
    private BigDecimal prepayAmount;

    @Schema(description = "即充即退的支付单号")
    @Size(max = 40, message = "prepayTradeNo 长度不能超过 40")
    private String prepayTradeNo;


    @Schema(description = "即充即退的支付单号(微信支付/支付宝)")
    @Size(max = 40, message = "prepay3rdTradeNo 长度不能超过 40")
    private String prepay3rdTradeNo;

    @Schema(description = "微信/支付宝信用充订单服务号")
    private String creditOrderNo;

    @Schema(description = "即充即退退款金额")
    private BigDecimal refundAmount;

    @Schema(description = "即充即退的退款单号")
    @Size(max = 40, message = "refundTradeNo 长度不能超过 40")
    private String refundTradeNo;


    @Schema(description = "即充即退的退款单号(微信支付/支付宝)")
    @Size(max = 40, message = "refund3rdTradeNo 长度不能超过 40")
    private String refund3rdTradeNo;

    @Schema(description = "停充超时费用")
    private BigDecimal overTimeParkFee;

    @Schema(description = "欠款金额")
    private BigDecimal debtAmount;

    @Schema(description = "欠款部分金额的支付单号. 仅即充即退订单")
    @Size(max = 40, message = "debtTradeNo 长度不能超过 40")
    private String debtTradeNo;

    @Schema(description = "欠款部分金额的第三方支付单号(微信支付/支付宝). 仅即充即退订单")
    @Size(max = 40, message = "debt3rdTradeNo 长度不能超过 40")
    private String debt3rdTradeNo;

    @Schema(description = "计费模板ID")
    private Long priceCode;

    @Schema(description = "0不适用; 1待支付; 2已支付")
    private Integer corpPayStatus;

    @Schema(description = "结算单号")
    @Size(max = 16, message = "billNo 长度不能超过 16")
    private String billNo;

    @Schema(description = "结算时间")
    private Date payTime;

    @Schema(description = "优惠券id")
    private Long activityId;

    @Schema(description = "鼎充开票Id（0：表示 没有关联开票申请Id,大于0表示已经关联开票ID） ")
    @NotNull(message = "invoicedId 不能为 null")
    private Long invoicedId;

    @Schema(description = "剩余可开票金额, 单位'元'")
    @NotNull(message = "invoiceAmount 不能为 null")
    private BigDecimal invoiceAmount;

    @Schema(description = "已开票金额: 单位: 元")
    @NotNull(message = "invoicedAmount 不能为 null")
    private BigDecimal invoicedAmount;

    @Schema(description = "0,未知; 1,账户余额扣减; 2,担保消费结算; 3,授信后结算; 4,外部平台结算")
    @NotNull(message = "settlementType 不能为 null")
    private SettlementType settlementType;

    @Schema(description = "优惠ID")
    private Long discountRefId;

    @Schema(description = "电费收益")
    private BigDecimal elecProfit;

    @Schema(description = "服务费收益")
    private BigDecimal servProfit;

    @Schema(description = "总收益")
    private BigDecimal totalProfit;

    @Schema(description = "支付渠道，非t_charger_order_pay表中字段")
    private PayChannel payChannel;

    @Schema(description = "农行优惠金额，非t_charger_order_pay表中字段")
    private BigDecimal abcCouponAmount;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "修改时间")
    private Date updateTime;


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
