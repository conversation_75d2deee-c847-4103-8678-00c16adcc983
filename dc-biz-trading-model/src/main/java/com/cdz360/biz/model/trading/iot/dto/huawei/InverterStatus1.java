package com.cdz360.biz.model.trading.iot.dto.huawei;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 华为逆变器状态1
 */
@Getter
public enum InverterStatus1 implements DcEnum {

    UNKNOWN(0, "未知"),
    STANDBY(1, "待机"),
    ON_GRID(2, "并网"),
    ON_GRID_NORMAL(3, "正常并网"),
    POWER_RATIONING_DERATING_ON_GRID(4, "限电降额并网"),
    SELF_DERATING_ON_GRID(5, "自降额并网"),
    OUTAGE(6, "正常停运"),
    FAULT_OUTAGE(7, "故障停运"),
    POWER_RATIONING_OUTAGE(8, "限电停运"),
    OFF(9, "关机"),
    SPOT_CHECK(10, "点检"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    InverterStatus1(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static InverterStatus1 valueOf(Object codeIn) {
        if (codeIn == null) {
            return InverterStatus1.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof InverterStatus1) {
            return (InverterStatus1) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (InverterStatus1 type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return InverterStatus1.UNKNOWN;
    }

}
