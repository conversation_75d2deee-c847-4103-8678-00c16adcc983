package com.cdz360.biz.model.trading.iot.po;

import com.cdz360.base.model.base.type.SupplyType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "充电桩型号")
public class EvseModelPo {

    private Long id;

    @Schema(description = "型号名称")
    private String model;

    @Schema(description = "品牌")
    private String brand;

    @Schema(description = "系列")
    private String series;

    @Schema(description = "电桩类型")
    private SupplyType supply;

    @Schema(description = "额定功率")
    private Integer power;

    @Schema(description = "枪头数量")
    private Integer plugNum;

    @Schema(description = "标志位")
    private List<Integer> flags;

    @Schema(description = "0：非分体机，1：250A终端单枪，2：250A终端双枪")
    private Integer splitFlag;

    @Schema(description = "状态")
    private Boolean status;

    private Boolean enable;
}
