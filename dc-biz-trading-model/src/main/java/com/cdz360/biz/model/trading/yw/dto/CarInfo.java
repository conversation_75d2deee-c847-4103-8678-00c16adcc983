package com.cdz360.biz.model.trading.yw.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "运维工单涉及车辆信息")
@Data
@Accessors(chain = true)
public class CarInfo {
    @Schema(description = "车辆品牌")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String brand;

    @Schema(description = "车辆类型")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String model;

    @Schema(description = "需求电压, 单位: V")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal needVoltage;

    @Schema(description = "需求电流, 单位: A")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal needCurrent;

    @Schema(description = "车辆品牌")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean otherEvse;
}
