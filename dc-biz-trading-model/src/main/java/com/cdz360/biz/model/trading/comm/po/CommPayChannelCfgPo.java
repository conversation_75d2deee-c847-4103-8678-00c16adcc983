package com.cdz360.biz.model.trading.comm.po;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.type.PayChannel;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 场站支付信息配置表
 */
@Data
@Accessors(chain = true)
public class CommPayChannelCfgPo {

    private Long commId;

    private AppClientType clientType;

    private PayChannel payChannel;

    private String payCfgUniqueNo;

    private Date updateTime;
}
