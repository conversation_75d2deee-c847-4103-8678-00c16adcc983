package com.cdz360.biz.model.trading.iot.po;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class WhiteCardEvsePo {
    /**
     * 主键
     */
    private Long id;
    /**
     * 物理卡号
     */
    private String whiteCardNo;
    /**
     * 桩号
     */
    private String evseId;
    /**
     * 紧急充电卡密码
     */
    private String passWord;

    /**
     * 拟下发密码
     */
    private String passWordTmp;
    /**
     * 逻辑卡号
     */
    private String cardNo;
    /**
     * 发送状态
     * 1-下发成功
     * 2-失败
     * 3-下发中
     * 4-弃用
     * 5-弃用失败
     * 6-弃用中
     */
    private Integer sendStatus;
}
