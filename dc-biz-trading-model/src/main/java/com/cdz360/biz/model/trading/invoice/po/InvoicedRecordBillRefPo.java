package com.cdz360.biz.model.trading.invoice.po;

import com.cdz360.base.model.base.type.InvoicingMode;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "开票记录和账单关联表(充值单/充电订单)")
public class InvoicedRecordBillRefPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@Schema(description = "invoice_record.id")
	@NotNull(message = "recordId 不能为 null")
	private Long recordId;

	@Schema(description = "类型")
	private InvoicingMode type;

	@Schema(description = "充值单流水号(t_pay_bill.order_id)")
	@NotNull(message = "czOrderId 不能为 null")
	@Size(max = 50, message = "czOrderId 长度不能超过 50")
	private String czOrderId;

	@Schema(description = "开票前充值单已开票金额")
	@NotNull(message = "czBeforeAmount 不能为 null")
	private BigDecimal czBeforeAmount;

	@Schema(description = "充值单本次可开票金额")
	private BigDecimal czInvoiceAmount;

	@Schema(description = "充电订单号")
	@Size(max = 32, message = "orderNo 长度不能超过 32")
	private String orderNo;

	@Schema(description = "开票前充电单可开票金额")
	private BigDecimal orderBeforeInvoiceAmount;

	@Schema(description = "开票前充电单已开票金额")
	private BigDecimal orderBeforeInvoicedAmount;

	@Schema(description = "充电单本次可开票金额")
	private BigDecimal orderInvoiceAmount;

	@Schema(description = "创建时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	private Date createTime;

	@Schema(description = "最后更新时间")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	private Date updateTime;

	@Schema(description = "是否有效")
	private Boolean enable;


}
