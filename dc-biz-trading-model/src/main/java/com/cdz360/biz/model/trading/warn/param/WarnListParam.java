package com.cdz360.biz.model.trading.warn.param;

import com.cdz360.base.model.base.param.BaseListParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "查询告警列表参数")
public class WarnListParam extends BaseListParam {

    /**
     * 告警码
     */
    private String warningCode;
    /**
     * 告警名称
     */
    private String warningName;
    /**
     * 补充说明
     */
    private String warningDesc;
    /**
     * 是否支持订阅
     */
    private Boolean enable;
}
