package com.cdz360.biz.model.trading.invoice.po;

import com.cdz360.biz.model.invoice.type.ProductType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel(value = "invoiced_record对应的开票内容")
public class InvoicedRecordContentPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@ApiModelProperty(value = "invoiced_template_sal_detail.code")
	@Size(max = 32, message = "code 长度不能超过 32")
	private String code;

	@ApiModelProperty(value = "invoiced_record.id")
	@NotNull(message = "invoiceId 不能为 null")
	private Long invoiceId;

	@ApiModelProperty(value = "商品或服务名称")
	@Size(max = 30, message = "productName 长度不能超过 30")
	private String productName;

	@ApiModelProperty(value = "商品行模板类型")
	@Size(max = 60, message = "productType 长度不能超过 60")
	private ProductType productType;

	@ApiModelProperty(value = "商品税率 16,6表示16%，6%")
	private BigDecimal taxRate;

	@ApiModelProperty(value = "实开金额")
	private BigDecimal fixAmount;

	@ApiModelProperty(value = "开票数量")
	private BigDecimal num;

	@ApiModelProperty(value = "单价")
	private BigDecimal price;

	private Boolean enable;

	private Date createTime;

	private Date updateTime;

	@ApiModelProperty(value = "规格")
	private String spec;

	@ApiModelProperty(value = "单位")
	private String unit;


}
