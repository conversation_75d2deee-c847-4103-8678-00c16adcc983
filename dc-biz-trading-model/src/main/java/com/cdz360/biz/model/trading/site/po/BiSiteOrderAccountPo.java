package com.cdz360.biz.model.trading.site.po;


import com.cdz360.biz.model.trading.site.type.SiteOrderAccountType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;



@Data

@Accessors(chain = true)

@Schema(description = "场站各账户充电消费统计")

public class BiSiteOrderAccountPo {



	@Schema(description = "记录ID")

	@NotNull(message = "id 不能为 null")

	private Long id;



	@Schema(description = "场站充电信息统计小时表ID(t_bi_site_order.id)")

	@NotNull(message = "biSiteOrderId 不能为 null")

	private Long biSiteOrderId;



	@Schema(description = "账户类型: UNKNOWN(无结算账户)；PREPAY(即充即退)；PERSONAL(个人账户)；COMMERCIAL(商户会员)；NORMAL_CORP(普通企业客户)；HLHT_CORP(互联企业客户)")

	private SiteOrderAccountType accountType;



	@Schema(description = "充电订单量(单位: 个)")

	private Long orderCnt;



	@Schema(description = "充电订单总金额(单位: 元)")

	private BigDecimal orderFee;



	@Schema(description = "无结算账户金额(单位: 元)")

	private BigDecimal noAccountFee;



	@Schema(description = "后付费金额(单位: 元)")

	private BigDecimal postSettlementFee;



	@Schema(description = "预付费金额(单位: 元)")

	private BigDecimal preSettlementFee;



	@Schema(description = "赠送金额(单位: 元)")

	private BigDecimal freeFee;



	@Schema(description = "实际金额(单位: 元)")

	private BigDecimal costFee;



	@Schema(description = "创建时间")

	private Date createTime;



	@Schema(description = "更新时间")

	private Date updateTime;





}

