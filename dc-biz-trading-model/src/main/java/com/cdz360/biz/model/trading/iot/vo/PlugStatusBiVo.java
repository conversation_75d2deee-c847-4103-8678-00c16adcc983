package com.cdz360.biz.model.trading.iot.vo;

import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.PlugStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "按状态统计枪数量")
public class PlugStatusBiVo {
    @Schema(description = "枪头状态")
    private PlugStatus plugStatus;

    @Schema(description = "桩状态, 可能为null")
    private EvseStatus evseStatus;

    @Schema(description = "桩数量", example = "123")
    private Long evseNum;

    @Schema(description = "枪头数量", example = "123")
    private Long plugNum;
}
