package com.cdz360.biz.model.trading.soc.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "用户充电限制时段")
@Deprecated
public class UserSocTimePo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@Schema(description = "用户充电限制策略id")
	@NotNull(message = "strategyId 不能为 null")
	private Long strategyId;

	private Integer soc;

	private Boolean allow;

	@Schema(description = "开始时间,闭区间,最小0,单位分钟")
	@NotNull(message = "startTime 不能为 null")
	private Integer startTime;

	@Schema(description = "开始时间,闭区间,最小00:00")
	private String startTimeStr;

	@Schema(description = "结束时间,开区间,最大1440,单位分钟")
	@NotNull(message = "endTime 不能为 null")
	private Integer endTime;

	@Schema(description = "结束时间,开区间,最大24:00")
	private String endTimeStr;

	@NotNull(message = "createTime 不能为 null")
	private Date createTime;


}
