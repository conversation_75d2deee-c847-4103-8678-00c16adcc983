package com.cdz360.biz.model.trading.iot.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "光伏站运行时数据统计")
@Data
@Accessors(chain = true)
public class SitePvRtDataBi {

    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "发电量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal totalKwh;

    @Schema(description = "发电收益")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal totalProfit;

    @Schema(description = "输出功率")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal outPower;

    @Schema(description = "发电总天数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long totalDay;
}
