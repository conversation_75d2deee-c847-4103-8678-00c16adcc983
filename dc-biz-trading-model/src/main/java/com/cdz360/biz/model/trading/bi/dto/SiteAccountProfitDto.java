package com.cdz360.biz.model.trading.bi.dto;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.model.corp.type.CorpType;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SiteAccountProfitDto {
    @Schema(description = "账户类型")
    private PayAccountType accountType;

    @Schema(description = "结算方式,预付/后付")
    private SettlementType settlementType;

    private Long accountId;

    @Schema(description = "账户名称 企业名称、商户名称")
    private String accountName;

    @Schema(description = "场站ID")
    private String siteId;

    @Schema(description = "月份")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    private LocalDate month;

    private CorpType corpType;

    private String billNo;

    @Schema(description = "充电量")
    private BigDecimal elec;

    @Schema(description = "订单原始电费金额")
    private BigDecimal elecOriginFee;

    @Schema(description = "根据订单统计的电费实付金额")
    private BigDecimal elecCostFee;

    @Schema(description = "根据订单统计的服务费实付金额")
    private BigDecimal servCostFee;


    @Schema(description = "根据订单统计的电费收益")
    private BigDecimal orderElecProfit;

    @Schema(description = "根据订单统计的服务费收益")
    private BigDecimal orderServProfit;


    @Schema(description = "总收入")
    public BigDecimal getIncome() {
        if (orderElecProfit == null && orderServProfit == null) {
            return BigDecimal.ZERO;
        } else if (orderElecProfit == null) {
            return orderServProfit;
        } else if (orderServProfit == null) {
            return orderElecProfit;
        } else {
            return orderElecProfit.add(orderServProfit);
        }
    }

    @Schema(description = "实际服务费收入")
    public BigDecimal getServProfit() {
        return getIncome().subtract(elecOriginFee);
    }


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
