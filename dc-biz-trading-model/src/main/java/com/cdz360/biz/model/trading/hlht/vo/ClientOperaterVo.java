package com.cdz360.biz.model.trading.hlht.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ClientOperaterVo {

    private Long id;

    @Schema(description = "运营商名称")
    private String name;

    @Schema(description = "运营商标识")
    private String code;

    @Schema(description = "停充超时是否回传")
    private Boolean billingBack;

    private List<AccountVo> accountVoList;

}
