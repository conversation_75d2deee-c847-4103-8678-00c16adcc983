package com.cdz360.biz.model.trading.order.param;

import com.cdz360.base.utils.JsonUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
public class PayOrderParam {
    /**
     * 充电订单号
     */
    private String orderNo;
    /**
     * 客户ID
     */
    private Long cusId;

    /**
     * 集团商户ID
     */
    private long commercialId;

    /**
     * 发起支付请求方的IP
     */
    private String ipAddr;
    private int sourceId;
    /**
     * 操作人ID
     */
    private long opId;
    /**
     * 操作人名称
     */
    private String opName;
    /**
     * 优惠券ID，不传（null）去判断是否开启自动抵扣，开启的话就默认使用，不开启不使用；0表示专门没选；大于0表示指定的优惠券
     */
    private Long couponId;

    /**
     * 积分体系ID，不传（null）表示旧版本，自动选择最优积分体系；0表示专门没选；指定id表示选定的积分体系
     */
    private Long scoreSettingId;

    /**
     * 无卡充电，自动支付
     */
    private boolean noCardAutoPay = false;
    private String userName;//无卡充电
    private String userPhone;//无卡充电
    private String commName;//无卡充电


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
