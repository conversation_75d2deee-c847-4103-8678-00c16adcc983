package com.cdz360.biz.model.trading.invoice.po;

import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.base.model.base.type.UserType;
import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.biz.model.invoice.type.InvoiceType;
import com.cdz360.biz.model.invoice.type.InvoicedStatus;
import com.cdz360.biz.model.oa.vo.InvoiceCompareVo;
import com.cdz360.biz.model.oa.vo.InvoicingContentVo;
import com.cdz360.biz.model.trading.invoice.vo.CorpInvoiceReturnPlanVo;
import com.cdz360.biz.model.trading.invoice.vo.CorpInvoicingContentVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "企业开票记录")
public class CorpInvoiceRecordPo extends BaseObject {

    @Schema(description = "申请单号")
    private String applyNo;

    @Schema(description = "流程实例ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String procInstId;

    @Schema(description = "t_corp的id")
    private Long corpId;

    @Schema(description = "流程状态: " +
        "草稿(NOT_SUBMITTED); 审核中(SUBMITTED); " +
        "开票中(REVIEWED); 审核未通过(AUDIT_FAILED); " +
        "开票未通过(INVOICING_FAIL);已开具(COMPLETED); " +
        "已作废(INVALID)[含红冲状态];" +
        "已删除(DELETED,由OA流程创建的记录才有此状态,否则会物理删除);"
    )
    private InvoicedStatus status;

    @Schema(description = "开票主体ID 通过这个可以查找到开票主体")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long tempSalId;

//    @Schema(description = "开票主体纳税人识别号")
//    private String saleTin;

    @Schema(description = "商品行模板ID(t_invoiced_sal_temp.id)")
    private Long productTempId;

    @Schema(description = "企业客户开票抬头模板ID invoiced_model.id")
    private Long modelId;

    @Schema(description = "开票抬头 联表查询")
    private String invoiceName;

    @Schema(description = "开票种类: PER_COMMON(个人普票); ENTER_COMMON(企业普票); ENTER_PROFESSION(企业专票) 联表查询")
    private InvoiceType invoiceType;

    @Schema(description = "企业客户开票方式: 充值预开票(PRE_PAY)、充电后开票(POST_CHARGER)、后付费账单开票(POST_SETTLEMENT)")
    private InvoicingMode invoiceWay;

    @Schema(description = "应开电费")
    private BigDecimal actualElecFee;

    @Schema(description = "修正电费")
    private BigDecimal fixElecFee;

    private BigDecimal elecNum;

    @Schema(description = "应开服务费")
    private BigDecimal actualServFee;

    @Schema(description = "修正服务费")
    private BigDecimal fixServFee;

    @Schema(description = "应开技术服务费")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal actualTechServFee;

    @Schema(description = "实开技术服务费 调整后的开票金额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal fixTechServFee;

    private BigDecimal servNum;

    @Schema(description = "应开总额")
    private BigDecimal totalFee;

    @Schema(description = "修正总额")
    private BigDecimal fixTotalFee;

    @Schema(description = "开票内容")
    private List<CorpInvoicingContentVo> invoicingContent;

    @Schema(description = "开票数据对比")
    private List<InvoiceCompareVo> actualData;

    @Schema(description = "开票备注")
    private String invoicingRemark;

    @Schema(description = "回款情况")
    private Integer returnFlag;

    @Schema(description = "回款计划")
    private List<CorpInvoiceReturnPlanVo> returnPlanVoList;

    @Schema(description = "回款图片")
    private List<String> returnImages;

    @Schema(description = "创建操作人类型. 0,未知; 1,商户/平台用户; 3,企业客户; 4,系统")
    private UserType createOpType;

    @Schema(description = "创建操作人ID")
    private Long createOpId;

    @Schema(description = "创建操作人名字")
    private String createOpName;

    @Schema(description = "操作人类型. 0,未知; 1,商户/平台用户; 3,企业客户; 4,系统")
    private UserType updateOpType;

    @Schema(description = "操作人ID")
    private Long updateOpId;

    @Schema(description = "操作人名字")
    private String updateOpName;

    @Schema(description = "申请备注")
    private String applyRemark;

    @Schema(description = "图片")
    private List<String> images;

    @Schema(description = "申请失败原因")
    private String failRemark;

    @Schema(description = "开具附属信息")
    private String issuedRemark;

    @Schema(description = "审核建议")
    private String auditRemark;

    @Schema(description = "审核时间")
    private Date auditTime;

    @Schema(description = "审核人")
    private String auditName;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
}
