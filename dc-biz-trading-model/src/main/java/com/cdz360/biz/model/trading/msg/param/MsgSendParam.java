package com.cdz360.biz.model.trading.msg.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;


/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "微信模板消息发送")
public class MsgSendParam {
    /**
     * 控制器编号
     */
    private String ctrlNo;
    /**
     * 告警记录id-告警报表中‘告警编号’
     */
    private Long warningId;

    /**
     * 电桩型号, 如: G4-001
     */
    private String modelName;
    /**
     * 桩名称
     */
    private String evseName;
    /**
     * 枪头名称
     */
    private String plugName;
    /**
     *     桩固件(软件)版本
     */
    private String firmwareVer;

    /**
     * 告警报表中‘上报设备/编号’
     * 为空时，取boxOutFactoryCode值
     */
    private String sourceNo;

    /**
     * 告警编码-告警报表中‘告警代码’
     */
    private String warningCode;
    /**
     * 告警名-告警报表中‘告警备注’
     */
    private String warningName;
    /**
     * 告警处理说明-告警报表中‘补充说明’
     */
    private String warningInstructions;

    /**
     * 设备id
     */
    private String deviceId;


    /**
     * 设备序列号/桩号-告警报表中‘告警对象/编号’
     */
    private String boxOutFactoryCode;

    /**
     * 充电接口(枪头)序号
     */
    private Integer connectorId;

    /**
     * 站点id-告警报表中‘告警所属场站’
     */
    private String siteId;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 商户id
     */
    private String businessId;

    /**
     * 顶级商户
     */
    private Long topCommId;

    /**
     * 告警等级
     */
    private Integer level;

    /**
     * 告警产生方式 0：桩端上报告警,1：桩端上报故障,2：告警逻辑生成告警
     * -告警报表中‘告警类型’
     */
    private Integer warningType;

    /**
     * 开始时间-告警报表中‘发生时间’
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;
    /**
     * 结束时间-告警报表中‘结束时间’
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;
    /**
     * 告警最后上报时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date warningUpdateTime;

    /**
     * 告警状态（0未结束   1自动结束   2手动结束)
     * -告警报表中‘告警状态’
     */
    private Integer status;

    /**
     * 持续时长单位秒（暂时由计算得出）
     */
    private Long duration;

    /**
     * 备注信息（用于存储说明信息）
     */
    private String remark;
    /**
     * 错误描述
     */
    private String error;
    /**
     * 温度 温度异常时有值
     */
    private Integer temp;

    /**
     * 设备最后心跳时间
     */
    private Long heartBeatTime;

    /**
     * 操作人id
     */
    private Long updateBy;

    private String gwno;

    private String appName;

    /**
     * 控制器负载率
     */
    private Integer loadRatio;

    /**
     * 配电柜温度
     */
    private Integer pwrTemp;

    /**
     * 将相关的控制器上报告警记录连接起来
     * 使用UUID生成
     * 留作备用
     */
    private String linkId;

    /**
     * 微信公众号appid
     */
    private String wechatAppid;
    /**
     * 微信公众号secret
     */
    private String wechatAppSecret;

    /**
     * 发送的openId
     */
    private List<String> userOpenIdList;

    /**
     * 桩管家小程序appid
     */
    private String wxLiteMgmAppId;

    /**
     * 订单号
     */
    private String orderNo;

    private Boolean isEvseCfgAlarmMsg;

    // 公众号模板消息自定义备注，非必填
    @Nullable
    private String customWarningDesc;

}
