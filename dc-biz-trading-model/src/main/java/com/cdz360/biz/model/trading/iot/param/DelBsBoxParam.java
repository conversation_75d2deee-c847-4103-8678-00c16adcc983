package com.cdz360.biz.model.trading.iot.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "删除操作参数")
@Data
@Accessors(chain = true)
public class DelBsBoxParam {
    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "需要保留的桩编号列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> evseNoList;
}
