package com.cdz360.biz.model.trading.invoice.dto;

import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.biz.model.trading.invoice.vo.InvoicedTemplateSalDetailVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * InvoicedSalTempRefDTO
 *
 * @since 3/28/2023 2:22 PM
 * <AUTHOR>
 */
@Schema(description = "商品行模板传输信息")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class InvoicedSalTempRefDTO extends BaseObject {
    @Schema(description = "主键")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long id;

    @Schema(description = "'商品行模板名称'")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String name;

    @Schema(description = "开票主体ID 通过这个可以查找到开票主体")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long tempSalId;

//    @Schema(description = "'纳税人识别号'")
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String saleTin;

    @Schema(description = "使用对象: MOBILE -- 移动端; PLATFORM -- 平台端")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Deprecated(since = "2024-09-24")
    private String target;

//    @Schema(description = "'创建时间'")
//    @JsonIgnore
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    @JsonSerialize(using = ZonedDateTimeSerializer.class)
//    private ZonedDateTime createTime;

//    @Schema(description = "'更新时间'")
//    @JsonIgnore
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    @JsonSerialize(using = ZonedDateTimeSerializer.class)
//    private ZonedDateTime updateTime;

    @Schema(description = "是否有效. true/false")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean enable;

    @Schema(description = "商品行信息列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<InvoicedTemplateSalDetailVo> detailVoList;
}