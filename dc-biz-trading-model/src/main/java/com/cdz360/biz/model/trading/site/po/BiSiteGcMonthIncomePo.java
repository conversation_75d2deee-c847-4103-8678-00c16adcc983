package com.cdz360.biz.model.trading.site.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.lang.Double;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel(value = "运营场站月收入项")
public class BiSiteGcMonthIncomePo {

	@Schema(description = "场站ID")
	@NotNull(message = "siteId 不能为 null")
	@Size(max = 32, message = "siteId 长度不能超过 32")
	private String siteId;

	@Schema(description = "年月")
	@NotNull(message = "month 不能为 null")
	private Date month;

	@Schema(description = "分类. 散客,互联企业,预付企业,后付企业,未结算")
	@NotNull(message = "category 不能为 null")
	@Size(max = 16, message = "category 长度不能超过 16")
	private String category;

	@Schema(description = "名称")
	@NotNull(message = "name 不能为 null")
	@Size(max = 32, message = "name 长度不能超过 32")
	private String name;

	@Schema(description = "充电量,按上传时间统计,单位\"kwh\"")
	private BigDecimal elec;

	@Schema(description = "平台电费")
	private BigDecimal elecFee;

	@Schema(description = "平台服务费")
	private BigDecimal servFee;

	@Schema(description = "电费实收")
	private BigDecimal elecCost;

	@Schema(description = "服务费实收")
	private BigDecimal servCost;

	@Schema(description = "电费收益")
	private BigDecimal elecProfit;

	@Schema(description = "服务费收益")
	private BigDecimal servProfit;

	@Schema(description = "关联的oa申请单ID")
	@Size(max = 64, message = "oaId 长度不能超过 64")
	private String oaId;

	@Schema(description = "账单编号")
	@Size(max = 32, message = "billNo 长度不能超过 32")
	private String billNo;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;


}
