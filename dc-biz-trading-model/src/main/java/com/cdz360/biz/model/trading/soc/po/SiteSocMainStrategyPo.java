package com.cdz360.biz.model.trading.soc.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@Accessors(chain = true)
@Schema(description = "soc限制主策略")
public class SiteSocMainStrategyPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@Schema(description = "策略名称")
	@Size(max = 255, message = "name 长度不能超过 255")
	private String name;

	@Size(max = 32, message = "siteId 长度不能超过 32")
	private String siteId;

	@Schema(description = "生效时间星期[1,2,3...7]")
	private List<Integer> enableDays;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date createTime;

	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date updateTime;


}
