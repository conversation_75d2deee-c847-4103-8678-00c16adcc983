package com.cdz360.biz.model.trading.rover.po;

import com.cdz360.biz.model.FileItem;
import com.cdz360.biz.model.trading.rover.type.RoverAssertType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "巡查详情")
public class SiteRoverAssetPo {

    @NotNull(message = "id 不能为 null")
    private Long id;

    @ApiModelProperty(value = "t_site_rover.id")
    private Long roverId;

    @ApiModelProperty(value = "资产类型依次:充电桩,配电房,雨棚/车挡,监控,照明,消防器材,场站地面,场站形象,卫生间,休息室")
    private RoverAssertType type;

    private Boolean enable;

    @ApiModelProperty(value = "是否异常:0无,1正常,2异常")
    private Integer fault;

    @ApiModelProperty(value = "异常描述")
    @Size(max = 255, message = "faultDesc 长度不能超过 255")
    private String faultDesc;

    @ApiModelProperty(value = "处理状态:1已处理,2未处理")
    private Integer fix;

    @ApiModelProperty(value = "处理措施描述")
    @Size(max = 255, message = "fixDesc 长度不能超过 255")
    private String fixDesc;

    @JsonInclude(Include.NON_EMPTY)
    @ApiModelProperty(value = "上传照片列表")
    private List<FileItem> images;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date updateTime;


}
