package com.cdz360.biz.model.trading.bill.vo;

import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "直付数据统计")
@Data
@Accessors(chain = true)
public class TradeOrderBi {

    @Schema(description = "交易类型: IN_FLOW(收入), OUT_FLOW(支出)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private DepositFlowType tradeType;

    @Schema(description = "交易总金额（单位: 元）")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal tradeAmount;
}
