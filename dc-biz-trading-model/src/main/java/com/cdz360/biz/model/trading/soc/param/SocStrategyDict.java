package com.cdz360.biz.model.trading.soc.param;

import com.cdz360.biz.model.trading.soc.po.SocStrategyPo;
import com.cdz360.biz.model.trading.soc.po.UserSocTimePo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * SocStrategyDict
 *
 * @since 8/10/2020 2:07 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "场站soc限制-优先策略模板")
@Deprecated
public class SocStrategyDict {
    @Schema(description = "策略")
    private SocStrategyPo socStrategy;
    @Schema(description = "时间列表")
    private List<UserSocTimePo> timeList;

    @Schema(description = "限制对象-授信账户个数")
    private long creditCusCount = 0L;

    @Schema(description = "限制对象-vin个数")
    private long vinCount = 0L;
}