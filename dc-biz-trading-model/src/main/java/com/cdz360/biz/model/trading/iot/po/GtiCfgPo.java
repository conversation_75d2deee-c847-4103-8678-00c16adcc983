package com.cdz360.biz.model.trading.iot.po;

import com.cdz360.biz.model.common.request.Update;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * GtiCfgPo
 *
 * @since 8/31/2021 4:33 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "光伏逆变器配置模板")

public class GtiCfgPo {

    @Schema(description = "主键id")
    @NotNull(groups = {Update.class}, message = "id 不能为 null")
    private Long id;

    @Schema(description = "场站ID")
    @Size(max = 32, message = "siteId 长度不能超过 32")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "模板名称")
    @NotBlank(message = "模板名称不能为空")
    @Size(max = 20, message = "模板名称长度不能超过20")
    private String name;

    @Schema(description = "采样时间间隔，单位: 秒")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer samplingTime;

    @Schema(description = "重连时间,单位: 秒")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer timeout;

    @Schema(description = "起机电压, 单位V")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal bootVoltage;

    @Schema(description = "市电电压下限, 单位V")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal minVoltage;

    @Schema(description = "市电电压上限, 单位V")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal maxVoltage;

    @Schema(description = "市电频率下限, 单位Hz")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal minFrequency;

    @Schema(description = "市电频率上限, 单位Hz")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal maxFrequency;

    @Schema(description = "并网模式. 0,未知;1,离网;2,并网")
    @NotNull(message = "并网模式不能为空")
    @Max(value = 2, message = "并网模式值范围0~2")
    @Min(value = 0, message = "并网模式值范围0~2")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer gridMode;

    @Schema(description = "操作人ID")
//    @NotNull(message = "操作人ID不能为空")
    private Long opUid;

    @Schema(description = "操作人姓名")
//    @NotNull(message = "opName 不能为 null")
    @Size(max = 32, message = "opName 长度不能超过 32")
    private String opName;

    @Schema(description = "是否有效")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean enable;

    @Schema(description = "记录创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "记录最后修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}