package com.cdz360.biz.model.trading.yw.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "运维工单标签")
@Getter
public enum YwOrderTag implements DcEnum {
    UN_ASSESS(0, "不考核"),
    ASSESS(1, "考核");

    @JsonValue
    private int code;

    private String desc;

    YwOrderTag(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static YwOrderTag valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (YwOrderTag tag : values()) {
            if (tag.code == code) {
                return tag;
            }
        }

        return null;
    }
}
