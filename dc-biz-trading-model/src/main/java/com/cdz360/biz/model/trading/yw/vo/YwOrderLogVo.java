package com.cdz360.biz.model.trading.yw.vo;


import com.cdz360.biz.model.trading.yw.type.YwOrderStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;


@Data

@Accessors(chain = true)

@Schema(description = "运维工单流转记录查看")

public class YwOrderLogVo {

	@Schema(description = "运维工单编号")
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private String ywOrderNo;

	@Schema(description = "工单状态(INIT: 待接收; RECEIVED: " +
			"已接收; PROCESSING: 处理中; TRANSFERRING: 转派中;" +
			" WAIT_CHECK : 待质检; NO_PASS: 不合格; SOLVED: 已完成;)")
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private YwOrderStatus orderStatus;


	/**
     * {@link com.cdz360.biz.model.trading.yw.type.YwOrderOpType}
	 */
	@Schema(description = "操作类型. 0,未知; ")
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private Integer type;

	@Schema(description = "目标人ID")
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private Long targetUid;

	@Schema(description = "目标人名字")
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private String targetUserName;

	@Schema(description = "操作人ID")
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private Long opUid;

	@Schema(description = "操作人名字")
	@NotNull(message = "opName 不能为 null")
	@Size(max = 32, message = "opName 长度不能超过 32")
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private String opUserName;

	@Schema(description = "操作人操作时间")
	@NotNull(message = "opTime 不能为 null")
	@JsonInclude(JsonInclude.Include.NON_NULL)
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date opTime;
}

