package com.cdz360.biz.model.trading.iot.type;

import lombok.Getter;

@Getter
public enum PcsModel {

    PWS2_30K_E(0),
    PWG2_50K_E(1),
    PWG2_100K_E(2),
    PWS1_50K_E(3),
    PWS1_100K_E(4),
    PWS1_150K_E(5),
    PWS2_50K_E(6),
    PWS2_100K_E(7),
    PWS1_250K_E(8),
    PWS1_250K_4H_E(9),
    PWS1_500K_E(10),
    UNKNOWN(99),
    ;

    private final int code;

    PcsModel(int code) {
        this.code = code;
    }


    public static PcsModel codeOf(Object codeIn) {
        if (codeIn == null) {
            return PcsModel.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof PcsModel) {
            return (PcsModel) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (PcsModel model : values()) {
            if (model.code == code) {
                return model;
            }
        }
        return PcsModel.UNKNOWN;
    }

}
