package com.cdz360.biz.model.trading.order.vo;

import com.cdz360.base.model.base.type.ChargeOrderStatus;
import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.biz.model.annotations.ExcelField;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "结算单相关充电订单信息")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SettlementOrderVo extends BaseObject implements Serializable {

    @ExcelField(title = "订单编号", sort = 1)
    @Schema(description = "订单编号")
    private String orderNo;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "新添加的订单状态字段")
    private ChargeOrderStatus orderStatus;


    @Schema(description = "充电订单直接关联的客户名称")
    private String cusName;

    @Schema(description = "充电订单直接关联的客户手机号")
    private String cusPhone;

    @Schema(description = "订单启动方式 0--未知,\n" +
        "    1--紧急充电卡,\n" +
        "    2--设备端自主,\n" +
        "    17--在线卡鉴权,\n" +
        "    18--VIN识别鉴权,\n" +
        "    33--管理端手动,\n" +
        "    34--批量启动,\n" +
        "    35--外部平台请求,\n" +
        "    36--定时充电任务,\n" +
        "    50--微信小程序,\n" +
        "    51--iOS APP,\n" +
        "    52--安卓APP,\n" +
        "    53--支付宝应用",
        format = "java.lang.Integer")
    private OrderStartType orderType;

    @Schema(description = "站点编号")
    private String siteId;

    @Schema(description = "站点名称")
    private String siteName;

    @Schema(description = "其他时段电量")
    private BigDecimal kwhOther;

    @Schema(description = "尖时段电量")
    private BigDecimal kwhJian;

    @Schema(description = "峰时段电量")
    private BigDecimal kwhFeng;

    @Schema(description = "平时段电量")
    private BigDecimal kwhPing;

    @Schema(description = "谷时段电量")
    private BigDecimal kwhGu;

    @Schema(description = "充电订单总电量")
    private BigDecimal orderElec;

    @Schema(description = "充电订单总电费")
    private BigDecimal elecPrice;

    @Schema(description = "充电订单总服务费")
    private BigDecimal servPrice;

    @Schema(description = "充电订单总金额")
    private BigDecimal orderPrice;

    @Schema(description = "充电订单创建时间")
    private Date createTime;

    @Schema(description = "订单上传时间")
    private Date stopTime;

    @Schema(description = "充电开始时间(桩端开始充电时间)")
    private Long chargeStartTime;

    @Schema(description = "充电结束时间(桩端结束充电时间)")
    private Long chargeEndTime;

    @ExcelField(title = "订单号", sort = 1)
    @Schema(description = "关联账单编号")
    private String billNo;
}
