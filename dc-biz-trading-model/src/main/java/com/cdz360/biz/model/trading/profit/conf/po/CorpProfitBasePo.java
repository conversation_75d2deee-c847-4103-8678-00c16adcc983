package com.cdz360.biz.model.trading.profit.conf.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel(value = "企业收益基本公式")
public class CorpProfitBasePo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@NotNull(message = "corpId 不能为 null")
	private Long corpId;

	@ApiModelProperty(value = "电费收益公式")
	@Size(max = 256, message = "elecFeeExpression 长度不能超过 256")
	private String elecFeeExpression;

	@ApiModelProperty(value = "服务费收益公式")
	@Size(max = 256, message = "servFeeExpression 长度不能超过 256")
	private String servFeeExpression;

	@ApiModelProperty(value = "电费收益封顶公式")
	@Size(max = 256, message = "elecFeeUpExpression 长度不能超过 256")
	private String elecFeeUpExpression;

	@ApiModelProperty(value = "电费收益保底公式")
	@Size(max = 256, message = "elecFeeLowExpression 长度不能超过 256")
	private String elecFeeLowExpression;

	@ApiModelProperty(value = "服务费收益封顶公式")
	@Size(max = 256, message = "servFeeUpExpression 长度不能超过 256")
	private String servFeeUpExpression;

	@ApiModelProperty(value = "服务费收益保底公式")
	@Size(max = 256, message = "servFeeLowExpression 长度不能超过 256")
	private String servFeeLowExpression;

	@NotNull(message = "enable 不能为 null")
	private Boolean enable;

	@NotNull(message = "createTime 不能为 null")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;


}
