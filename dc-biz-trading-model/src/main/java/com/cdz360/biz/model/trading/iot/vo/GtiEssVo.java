package com.cdz360.biz.model.trading.iot.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class GtiEssVo {
    @Schema(description = "控制器编号")
    private String gwno;

    @Schema(description = "设备编号")
    private String dno;

    @Schema(description = "设备名称")
    private String name;

    @Schema(description = "设备状态.0,未知;1,正常;2,异常;3,待机;99,下线")
    @NotNull(message = "status 不能为 null")
    private Long status;

    @Schema(description = "品牌")
    private String vendor;

    @Schema(description = "1-光伏逆变器2-储能ESS")
    private Long type;

}
