package com.cdz360.biz.model.trading.site.po;

import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.base.vo.BaseObject;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SiteTemplatePo extends BaseObject implements Serializable {

    private static final long serialVersionUID = 6251248055347687785L;
    /**
     * 站点Id
     */
    @Schema(title = "场站ID")
    private String siteId;
    @Schema(title = "计费模板ID")
    private Long templateId;
    @Schema(title = "计费模板类型")
    private SupplyType templateType;

}
