package com.cdz360.biz.model.trading.score.po;

import com.cdz360.biz.model.trading.score.type.DiscountType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel(value = "积分折扣")
public class ScoreDiscountPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	private Long scoreSettingId;

	@Size(max = 64, message = "orderNo 长度不能超过 64")
	private String orderNo;

	private DiscountType discountType;

	@ApiModelProperty(value = "服务费折扣")
	private BigDecimal servFeeDiscount;

	@ApiModelProperty(value = "阶梯单价(固定总单价)")
	private BigDecimal fixedTotalDiscount;

	@NotNull(message = "createTime 不能为 null")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;


}
