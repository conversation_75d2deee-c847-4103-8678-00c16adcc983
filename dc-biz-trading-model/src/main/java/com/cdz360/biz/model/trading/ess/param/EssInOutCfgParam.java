package com.cdz360.biz.model.trading.ess.param;


import com.cdz360.biz.model.ess.po.RangeTime;
import com.cdz360.biz.model.trading.ess.type.InOutCoupleMode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;


@Data

@Accessors(chain = true)

@Schema(description = "储能ESS充放电时段参数配置")

public class EssInOutCfgParam {

    @Schema(description = "模板类型: AC(AC模式); DC(DC模式); HYBRID(混合模式)")
    private InOutCoupleMode coupleMode;

    @Schema(description = "需量控制使能")
    private Boolean demandCtrlEnable;

    @Schema(description = "最大需量阈值")
    private Integer demand;

    @Schema(description = "变压器容量")
    private Integer capacity;

    @Schema(description = "充电使能")
    private Boolean inEnable;

    @Schema(description = "充电功率, 单位: kW")
    private BigDecimal inPower;

    @Schema(description = "充电时间段, 单位: 分钟: [{start: 100, end: 600}]")
    private List<RangeTime> inTime;

    @Schema(description = "充电截止SOC")
    private Integer inStopSoc;

    @Schema(description = "放电使能")
    private Boolean outEnable;

    @Schema(description = "放电截止SOC")
    private Integer outStopSoc;

    @Schema(description = "充电时间段, 单位: 分钟: [{start: 100, end: 600}]")
    private List<RangeTime> outTime;


}

