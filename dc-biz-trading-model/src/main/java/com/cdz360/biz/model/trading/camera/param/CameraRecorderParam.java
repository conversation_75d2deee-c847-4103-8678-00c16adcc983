package com.cdz360.biz.model.trading.camera.param;

import com.cdz360.biz.model.trading.camera.vo.CameraRecorderVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * CameraRecorderParam
 * @since    9/27/2021 10:47 AM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "硬盘录像机设备")
@EqualsAndHashCode(callSuper = true)
public class CameraRecorderParam extends CameraRecorderVo {
    private String idChain;
    private String deviceSerialLike;
    private String siteNameLike;
    @Schema(description = "分页开始")
    private Long start;
    @Schema(description = "查询最大数量")
    private Integer size;
}