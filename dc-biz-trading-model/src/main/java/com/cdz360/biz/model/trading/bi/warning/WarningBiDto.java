package com.cdz360.biz.model.trading.bi.warning;

import com.cdz360.biz.model.annotations.ExcelField;
import com.cdz360.biz.model.site.type.AlarmEventTypeEnum;
import com.cdz360.biz.model.site.type.AlarmStatusEnum;
import com.cdz360.biz.model.trading.bi.dto.BiExportGroups;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * groups 字段归属组 {@link BiExportGroups}
 */
@Data
@Accessors(chain = true)
@Schema(description = "告警查询出参")
public class WarningBiDto implements Serializable {

    @ExcelField(title = "告警编号", sort = 1, groups = 11, align = 2)
    @Schema(description = "告警编号")
    private Long warningId;

    @ExcelField(title = "发生时间", sort = 2, groups = 11, align = 2, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "发生时间")
    private Date startTime;

    @ExcelField(title = "结束时间", sort = 3, groups = 11, align = 2, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "结束时间")
    private Date endTime;

    @ExcelField(title = "告警对象/编号", sort = 4, groups = 11, align = 2)
    @Schema(description = "告警对象/编号")
    private String boxOutFactoryCode;

    @ExcelField(title = "桩运营状态", sort = 4, groups = 11, align = 2)
    @Schema(description = "桩运营状态")
    private String evseStatus;

    @Schema(description = "枪头序号")
    @ExcelField(title = "枪头序号", sort = 5, groups = 11, align = 2)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer plugIdx;

    /**
     * 为空时，取boxOutFactoryCode值
     */
    @ExcelField(title = "上报设备/编号", sort = 6, groups = 11, align = 2)
    @Schema(description = "上报设备/编号")
    private String sourceNo;

    private String siteId;

    @ExcelField(title = "告警所属场站", sort = 7, groups = 11, align = 2)
    @Schema(description = "告警所属场站")
    private String siteName;

    @ExcelField(title = "场站状态", sort = 7, groups = 11, align = 2)
    @Schema(description = "场站状态")
    private String siteStatusStr;

    @ExcelField(title = "告警类型", sort = 8, groups = 11, align = 2, value = "warningType.label")
    @Schema(description = "告警类型 （0：桩端上报告警,1：桩端上报故障,2：平台告警逻辑生成告警,3:场站控制器生成告警）")
    private AlarmEventTypeEnum warningType;

    /**
     * 告警编码-告警报表中‘告警代码’
     */
    @ExcelField(title = "告警代码", sort = 9, groups = 11, align = 2)
    @Schema(description = "告警代码")
    private String warningCode;

    @ExcelField(title = "告警状态", sort = 10, groups = 11, align = 2, value = "status.label")
    @Schema(description = "告警状态（0：未结束，进行中,1：自动结束,2：手动结束）")
    private AlarmStatusEnum status;

    @ExcelField(title = "故障时长", sort = 10, groups = 11, align = 2)
    @Schema(description = "故障时长")
    private String duration;

    /**
     * 告警名-告警报表中‘告警备注’
     */
    @ExcelField(title = "告警名称", sort = 11, groups = 11, align = 2)
    @Schema(description = "告警名称")
    private String warningName;

    /**
     * 告警处理说明-告警报表中‘补充说明’
     */
    @ExcelField(title = "补充说明", sort = 12, groups = 11, align = 2)
    @Schema(description = "补充说明")
    private String warningInstructions;

    @Schema(description = "桩名称", hidden = true)
    private String evseName;

    @Schema(description = "设备ID", hidden = true)
    private String deviceId;

    @Schema(description = "订单号", hidden = true)
    private String orderNo;

    @Schema(description = "枪头名称", hidden = true)
    private String plugName;
}
