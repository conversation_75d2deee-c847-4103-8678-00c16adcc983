package com.cdz360.biz.model.trading.bi.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.biz.model.site.type.AlarmEventTypeEnum;
import com.cdz360.biz.model.site.type.AlarmStatusEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import java.util.Locale;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "告警查询入参")
public class WarningBiParam extends BaseListParam {

    @Schema(description = "筛选告警发生时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter warnStartTimeFilter;

    @Schema(description = "筛选告警结束时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter warnEndTimeFilter;

    @Schema(description = "告警编号")
    private Long warningId;

    @Schema(description = "告警对象/编号")
    private String boxOutFactoryCode;

    @Schema(description = "上报设备/编号")
    private String sourceNo;

    @Schema(description = "告警类型 （0：桩端上报告警,1：桩端上报故障,2：平台告警逻辑生成告警,3：场站控制器生成告警,4：场站控制器生成故障）")
    private List<AlarmEventTypeEnum> warningType;

    @Schema(description = "告警代码")
    private String warningCode;

    @Schema(description = "告警状态（0：未结束，进行中,1：自动结束,2：手动结束）")
    private List<AlarmStatusEnum> status;

    @Schema(description = "告警备注")
    private String warningName;

    @Schema(description = "所属场站")
    private List<String> siteIdList;

    @Schema(description = "商户列表")
    private List<Long> commList;

    @Schema(description = "补充说明")
    private String warningInstructions;

    @Schema(description = "告警代码列表")
    private List<String> warningCodeList;

    @Schema(description = "查询忽略的告警代码列表")
    private List<String> ignoreWarningCodeList;

    @Schema(description = "桩名称")
    private String evseName;

    @Schema(description = "桩编号")
    private String evseNo;

    @Schema(title = "告警延续时长，单位：分钟")
    private Long warningDuration;

    @Schema(description = "桩编号列表")
    private List<String> evseNoList;

    @Schema(description = "是否包含桩离线状态")
    private Boolean isContainOffline;

    @Schema(description = "区域语言信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Locale locale;
}
