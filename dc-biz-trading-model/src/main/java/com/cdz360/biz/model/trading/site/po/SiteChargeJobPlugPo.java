package com.cdz360.biz.model.trading.site.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

@Data
@Schema(description = "场站定时任务关联的枪头")
public class SiteChargeJobPlugPo {
    private Long id;
    @Schema(description = "任务ID")
    private Long jobId;

    private String evseNo;
    @Schema(description = "枪头序号")
    private Integer plugIdx;

    private Date createTime;
}
