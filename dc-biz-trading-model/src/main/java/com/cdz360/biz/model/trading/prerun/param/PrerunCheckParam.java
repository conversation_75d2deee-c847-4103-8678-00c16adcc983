package com.cdz360.biz.model.trading.prerun.param;

import com.cdz360.biz.model.trading.prerun.type.PrerunStatusType;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * PrerunCheckParam
 *  质检（批量）
 * @since 6/23/2022 1:10 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PrerunCheckParam {
    private List<Long> prerunIds;
    private PrerunStatusType status;
    private String comment;
    private Long opUid;
    private String opName;
}