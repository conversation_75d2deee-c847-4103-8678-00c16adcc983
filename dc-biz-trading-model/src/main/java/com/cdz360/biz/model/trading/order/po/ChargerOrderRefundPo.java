package com.cdz360.biz.model.trading.order.po;

import com.cdz360.biz.model.common.po.BasePo;
import com.cdz360.biz.model.trading.order.type.ChargerOrderRefundStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ChargerOrderRefundPo extends BasePo {

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "请求单号")
    private String seqNo;

    @Schema(description = "客户ID")
    private long cusId;

    @Schema(description = "退款金额")
    private BigDecimal amount;

    @Schema(description = "状态")
    private ChargerOrderRefundStatus status;

    @Schema(description = "三方(微信/支付宝)退款交易流水号")
    private String thirdTradeNo;

}
