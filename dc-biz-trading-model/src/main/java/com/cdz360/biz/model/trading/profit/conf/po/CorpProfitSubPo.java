package com.cdz360.biz.model.trading.profit.conf.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel(value = "特殊区间")
public class CorpProfitSubPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@ApiModelProperty(value = "基本公式id")
	@NotNull(message = "baseId 不能为 null")
	private Long baseId;

	@ApiModelProperty(value = "区间公式")
	@Size(max = 256, message = "intervalExpression 长度不能超过 256")
	private String intervalExpression;

	@ApiModelProperty(value = "电费收益公式")
	@Size(max = 256, message = "elecFeeSubExpression 长度不能超过 256")
	private String elecFeeSubExpression;

	@ApiModelProperty(value = "服务费收益公式")
	@Size(max = 256, message = "servFeeSubExpression 长度不能超过 256")
	private String servFeeSubExpression;

	@NotNull(message = "enable 不能为 null")
	private Boolean enable;

	@NotNull(message = "createTime 不能为 null")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;


}
