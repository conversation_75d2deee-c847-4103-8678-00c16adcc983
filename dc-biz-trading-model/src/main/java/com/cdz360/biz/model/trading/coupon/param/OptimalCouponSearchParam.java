package com.cdz360.biz.model.trading.coupon.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.biz.model.trading.coupon.type.CouponStatusType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.web.bind.annotation.RequestParam;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "获取优惠券选择列表的参数，按最优排序后")
public class OptimalCouponSearchParam extends BaseListParam {

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private long userId;

    @Schema(description = "积分体系id")
    private Long scoreSettingId;

    @Schema(description = "场站id")
    private String siteId;

    @Schema(description = "即冲即退可用")
    private Boolean prepayEnable;

    @Schema(description = "微信信用可用")
    private Boolean wxCreditEnable;
}
