package com.cdz360.biz.model.trading.cus.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CusOrderBiVo {

    @Schema(description = "个人用户7日订单金额")
    private BigDecimal personalOrderFee7;

    @Schema(description = "企业用户7日订单金额")
    private BigDecimal corpOrderFee7;

    @Schema(description = "个人用户30日订单金额")
    private BigDecimal personalOrderFee30;

    @Schema(description = "企业用户30日订单金额")
    private BigDecimal corpOrderFee30;


}
