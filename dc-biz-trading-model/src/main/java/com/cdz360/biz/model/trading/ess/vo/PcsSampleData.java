package com.cdz360.biz.model.trading.ess.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "PCS设备运行数据采样")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class PcsSampleData extends SampleBase {

    @Schema(description = "直流电压")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal dcVoltage;

    @Schema(description = "采样点功率，单位: W 有正负之分，正表示充电；负标识放电")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal dcPower;

    @Schema(description = "采样点充电功率，单位: W 充电功率")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal inPower;

    @Schema(description = "采样点放电功率，单位: W 放电功率")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal outPower;
}
