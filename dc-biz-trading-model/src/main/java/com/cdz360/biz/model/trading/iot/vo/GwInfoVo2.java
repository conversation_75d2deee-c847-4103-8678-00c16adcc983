package com.cdz360.biz.model.trading.iot.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "场站控制器信息")
public class GwInfoVo2 {

    private String siteId;

    private List<GwInfo> gwInfoList;

    @Schema(description = "充电桩信息")
    @Data
    @Accessors(chain = true)
    public static class GwInfo {

        @Schema(description = "控制器编号 网关编号")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String gwno;

        @Schema(description = "挂载逆变器个数")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Long gtiNum;

    }
}
