package com.cdz360.biz.model.trading.site.po;

import com.cdz360.biz.model.trading.site.type.InspectionRemindType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;

@Data
@Schema(description = "场站巡检配置表")
public class SiteInspectionCfgPo {

    private Long id;

    private String siteId;

    @Schema(description = "场站巡检提醒类型")
    private InspectionRemindType type;

    @Schema(description = "巡检周期(天)")
    private Integer cycle;

    private String remark;

    private Date createTime;

    private Date updateTime;

}
