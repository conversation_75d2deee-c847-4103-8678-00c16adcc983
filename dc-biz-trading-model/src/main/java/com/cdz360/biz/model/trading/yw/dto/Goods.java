package com.cdz360.biz.model.trading.yw.dto;

import com.cdz360.biz.model.parts.param.PartsYwOrderRefParam.AssociationOpType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "运维工单使用配件(物品)")
@Data
@Accessors(chain = true)
public class Goods {

    @Schema(description = "兼容老版本，器件名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private  String name;

    @Schema(description = "兼容老版本，数量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private  Long num;

    @Schema(description = "桩名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private  String evseName;

    @Schema(description = "规格型号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private  String fullModel;

    @Schema(description = "桩编号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private  String evseNo;

    @Schema(description = "序列")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer idx;

    @Schema(description = "退回到上一次")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean rollback;

    @Schema(description = "现原材编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String newDeviceNo;

    @Schema(description = "旧原材编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String oldDeviceNo;

    @Schema(description = "操作方式", required = true)
    @JsonInclude(Include.NON_NULL)
    private AssociationOpType opType;

    @Schema(description = "使用新的物料ID")
    @JsonInclude(Include.NON_EMPTY)
    private String newPartsCode;

    @Schema(description = "旧的物料规格名称 设备使用，可能没有")
    @JsonInclude(Include.NON_EMPTY)
    private String typeName;

    @Schema(description = "物料编码ID 旧物料ID不存在则提供物料规格编码ID")
    @JsonInclude(Include.NON_NULL)
    private Long typeId;

    @Schema(description = "器件名称 仅在新增，更换时存在")
    @JsonInclude(Include.NON_NULL)
    @JsonProperty("gTypeName")
    private String gTypeName;

    @Schema(description = "新器件编码 仅在新增，更换时存在")
    @JsonInclude(Include.NON_NULL)
    private String newTypeCode;

    @Schema(description = "旧器件编码 仅在拆除，更换时存在")
    @JsonInclude(Include.NON_NULL)
    private String oldTypeCode;

    @Schema(description = "新规格型号 仅在新增，更换时存在")
    @JsonInclude(Include.NON_NULL)
    private String newFullModel;

    @Schema(description = "旧规格型号 仅在拆除，更换时存在")
    @JsonInclude(Include.NON_NULL)
    private String oldFullModel;

    @Schema(description = "拆除器件同步物料库后保存的物料ID")
    @JsonInclude(Include.NON_NULL)
    private String inNewCode;



}
