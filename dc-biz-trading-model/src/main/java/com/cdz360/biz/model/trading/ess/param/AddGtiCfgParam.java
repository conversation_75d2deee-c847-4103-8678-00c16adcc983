package com.cdz360.biz.model.trading.ess.param;


import com.cdz360.biz.model.trading.ess.type.CfgType;
import com.cdz360.biz.model.trading.pv.po.GtiGridDispatchCfgPo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;


@Data

@Accessors(chain = true)

@Schema(description = "新增光伏逆变器模板")

public class AddGtiCfgParam {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "场站ID")
    private String siteId;

    @Schema(description = "模板名称")
    private String name;

    @Schema(description = "继承code")
    private String code;

    @Schema(description = "配置模板版本号")
    private Long ver;

    @Schema(description = "类型")
    private CfgType type;

    @Schema(description = "创建者所属商户ID")
    private Long commId;

    @Schema(description = "采样时间间隔，单位: 秒")
    private Integer samplingTime;

    @Schema(description = "重连时间,单位: 秒")
    private Integer timeout;

    @Schema(description = "并网模式. 0,未知;1,离网;2,并网")
    private Integer gridMode;

    @Schema(description = "起机电压, 单位V")
    private BigDecimal bootVoltage;

    @Schema(description = "市电电压下限, 单位V")
    private BigDecimal minVoltage;

    @Schema(description = "市电电压上限, 单位V")
    private BigDecimal maxVoltage;

    @Schema(description = "市电频率下限, 单位Hz")
    private BigDecimal minFrequency;

    @Schema(description = "市电频率上限, 单位Hz")
    private BigDecimal maxFrequency;

    @Schema(description = "电网调度配置")
    private GtiGridDispatchCfgPo gridDispatchCfgPo;

    @Schema(description = "操作人ID")
    private Long opUid;

    @Schema(description = "操作人姓名")
    private String opName;

    @Schema(description = "是否有效")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean enable;

    @Schema(description = "商户链")
    private String commIdChain;

}

