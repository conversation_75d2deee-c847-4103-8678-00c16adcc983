package com.cdz360.biz.model.trading.order.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.param.TimeFilter;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "充电订单查询参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListChargerOrderParamX extends BaseListParam {

    @Schema(description = "充电订单号 支持模糊查询")
    @JsonInclude(Include.NON_EMPTY)
    private String orderNoLike;

    @Schema(description = "充电订单号")
    @JsonInclude(Include.NON_EMPTY)
    private String orderNo;

    ////////////////////// 用户信息 ////////////////////////
    @Schema(description = "用户手机号 支持模糊查询")
    @JsonInclude(Include.NON_EMPTY)
    private String cusPhoneLike;

    @Schema(description = "用户手机号")
    @JsonInclude(Include.NON_EMPTY)
    private String cusPhone;

    @Schema(description = "用户ID t_user.id")
    @JsonInclude(Include.NON_NULL)
    private Long cusId;

    /////////////////////////////// 👇 订单时间维度 ///////////////////
    @Schema(description = "充电开始时间 桩端")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter chargeStartTime;

    @Schema(description = "充电结束时间 桩端")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter chargeEndTime;

    @Schema(description = "订单创建时间 平台")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter createTime;

    @Schema(description = "平台（收到）结束充电的时间 平台")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter stopTime;

    @Schema(description = "支付时间筛选 平台")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter payTime;
    /////////////////////////////// 👆 订单时间维度 ///////////////////
}
