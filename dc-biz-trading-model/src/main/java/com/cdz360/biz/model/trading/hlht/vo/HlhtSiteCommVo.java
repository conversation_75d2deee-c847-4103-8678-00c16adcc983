package com.cdz360.biz.model.trading.hlht.vo;

import com.cdz360.biz.model.trading.hlht.po.HlhtSitePo;
import com.cdz360.biz.model.trading.hlht.po.PartnerSitePo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class HlhtSiteCommVo  {

    private Long commId;

    private String commName;

    private List<PartnerSitePo> siteList;
}
