package com.cdz360.biz.model.trading.prerun.po;

import com.cdz360.biz.model.trading.prerun.type.PrerunStatusType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel(value = "质检记录")
public class PrerunCheckPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@Size(max = 32, message = "prerunNo 长度不能超过 32")
	private Long prerunId;

	@ApiModelProperty(value = "操作人ID")
	private Long opUid;

	@ApiModelProperty(value = "操作人名字")
	@Size(max = 32, message = "opName 长度不能超过 32")
	private String opName;

	@ApiModelProperty(value = "前状态")
	private PrerunStatusType fromStatus;

	@ApiModelProperty(value = "后状态")
	private PrerunStatusType toStatus;

	@ApiModelProperty(value = "备注")
	private String comment;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;


}
