package com.cdz360.biz.model.trading.site.po;

import com.cdz360.biz.model.trading.order.type.BiDependOnType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.Month;
import java.time.YearMonth;
import java.util.Date;

/**
 * BiSiteSumPo
 *
 * @since 3/23/2020 1:34 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BiSiteSumPo {

    private BigDecimal amount = BigDecimal.ZERO;

    private Long corpId;

    private String name;

    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date day;

    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date month;


}