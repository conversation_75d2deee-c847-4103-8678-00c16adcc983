package com.cdz360.biz.model.trading.order.dto;

import lombok.Data;

@Data
public class SyncOrderDto {
    /**
     * 桩企订单编号
     */
    private String orderId;

    /**
     * 曹操订单编号
     */
    private String outOrderId;

    /**
     * 24小时制！开始充电时间yyyy-MM-dd HH:mm:ss
     */
    private String beginTime;

    /**
     * 24小时制！结束充电时间yyyy-MM-dd HH:mm:ss
     */
    private String endTime;

    /**
     * 充电总电量，精确到4位小数
     */
    private String power;

    /**
     * 单个订单总金额(单位:元,精确两位)，包含充电金额和服务费，按协商价格计算
     */
    private String fee;

    /**
     * 订单详情
     * {@link SyncOrderDtoDetail}
     * CSV中的chargeDetails列，因为数据中含有逗号，而CSV文件是以逗号作为列分割符，因此需要对该列进行转义，对该列数据多加一层双引号；数据中有双引号的需要再加上一层双引号。
     *
     * 如：[{"detailStartTime":"2017-03-06 08:05:16","detailEndTime":"2017-03-06 09:00:00"}] 的数据，
     *
     * 用程序生成时应该写成
     *
     * ​ "[{""detailStartTime"":""2017-03-06 08:05:16"", ""detailEndTime"":""2017-03-06 09:00:00""}]"
     */
    private String chargeDetails;
}
