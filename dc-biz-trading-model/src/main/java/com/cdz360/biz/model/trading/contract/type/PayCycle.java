package com.cdz360.biz.model.trading.contract.type;

import lombok.Getter;

/**
 * 收费周期
 */
@Getter
public enum PayCycle {

    PAY_C0(0, "其他"),

    PAY_C1(1, "月付"),

    PAY_C2(2, "季付"),

    PAY_C3(3, "半年付"),

    PAY_C4(4, "年付");

    private final int code;
    private final String desc;

    PayCycle(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static PayCycle valueOf(int code) {
        for (PayCycle status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return PayCycle.PAY_C0;
    }

}
