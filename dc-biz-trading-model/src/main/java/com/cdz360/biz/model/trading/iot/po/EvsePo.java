package com.cdz360.biz.model.trading.iot.po;

import com.cdz360.base.model.base.type.EvseProtocolType;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.iot.type.DtuType;
import com.cdz360.base.model.iot.type.NetType;
import com.cdz360.biz.model.iot.type.EvseBizType;
import com.cdz360.biz.model.iot.type.UpdateTaskStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

@Data
@Slf4j
@Accessors(chain = true)
public class EvsePo {

    private Long id;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String gwno;

    private String evseId;
    private String name;
    private Integer power; // 额定功率
    private EvseStatus evseStatus = EvseStatus.UNKNOWN;
    private SupplyType supply = SupplyType.UNKNOWN;
    private NetType net = NetType.UNKNOWN;

    private DtuType dtuType;

    private EvseBizType bizType;

    private String ip;

    private String iccid;

    private String imsi;

    private String imei;
    /**
     * 桩型号
     */
    private String model;

    private Long modelId;

    // 铭牌编号
    private String physicalNo;

    private Integer plugNum;
    /**
     * 场站ID
     */
    private String siteId;
    /**
     * 场站对应的商户ID
     */
    private Long commId;

    /**
     * 价格模板ID
     */
    private Long priceCode;

    /**
     * 桩协议版本
     */
    private Integer protocolVer;
    /**
     * 桩固件(软件)版本
     */
    private String firmwareVer;

    /**
     * 枪口编号   0+桩号+0+枪口号
     */
    private String plugNo;

    /**
     * PC01版本号, 格式为: 硬件版本号-软件版本号-定制号
     */
    private String pc01Ver;
    /**
     * PC02版本号, 格式为: 硬件版本号-软件版本号-定制号
     */
    private String pc02Ver;
    /**
     * PC03版本号, 格式为: 硬件版本号-软件版本号-定制号
     */
    private String pc03Ver;

    private EvseProtocolType protocol;//桩协议类型 DC/CCTIA
    /**
     * 电桩型号, 如: G4-001 请用model,暂时没用到这个字段
     */
    private String modelName;

    /**
     * 额定电压,单位"V"
     */
    private BigDecimal voltage;

    /**
     * 额定电流,单位"A"
     */
    private BigDecimal current;

    @Schema(description = "是否支持插枪状态")
    private Boolean connSupport;

    @Schema(description = "当前最新的密钥版本号")
    private Long passcodeVer;
    @Schema(description = "是否开启debug")
    private Boolean debugTag;

    @Schema(description = "是否使用场站默认配置", example = "true")
    private Boolean useSiteCfg;

    @Schema(description = "最近一次升级状态 null表示没升级过")
    private UpdateTaskStatusEnum upgradeStatus;

    @Schema(description = "生产单号(出厂编号)")
    @Deprecated
    private String produceNo;

    @Schema(description = "生产日期(出厂日期)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date produceDate;

    @Schema(description = "质保到期日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date expireDate;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Schema(description = "数据创建时间, UNIX时间戳", format = "java.lang.Long")
    private Date createTime;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER)
    @Schema(description = "数据最后更新时间, UNIX时间戳", format = "java.lang.Long")
    private Date updateTime;

    @Override
    public String toString() {
        String ret = "";
        ObjectMapper om = new ObjectMapper();
        try {
            ret = om.writeValueAsString(this);
        } catch (JsonProcessingException e) {
            log.error(e.getMessage(), e);
        }
        return ret;
    }

    public String supplyFormat() {
        if (supply == null) {
            return null;
        } else if (SupplyType.UNKNOWN == supply) {
            return "未知";
        } else if (SupplyType.AC == supply) {
            return "交流";
        } else if (SupplyType.DC == supply) {
            return "直流";
        } else if (SupplyType.BOTH == supply) {
            return "交直流";
        } else {
            return null;
        }
    }

}
