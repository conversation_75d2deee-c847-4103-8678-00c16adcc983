package com.cdz360.biz.model.trading.order.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Schema(description = "枪头使用统计")
@EqualsAndHashCode(callSuper = true)
public class PlugOrderBi extends OrderBi {
    /**
     * 序号
     */
    @Schema(description = "序号")
    private int idx;

    /**
     * 桩号+抢号
     */
    @Schema(description = "桩号+抢号")
    private String plugNo;

    @JsonIgnore
    private String evseNo;

}
