package com.cdz360.biz.model.trading.bi.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "告警分类汇总传参")
public class WarningSummaryParam extends BaseListParam {

    @Schema(description = "所属场站")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> siteIdList;

    @Schema(description = "筛选告警发生时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date startTime;

    @Schema(description = "筛选告警结束时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date endTime;

    @Schema(description = "告警代码")
    private List<String> warningCodeList;

    @Schema(description = "设备型号")
    private List<String> modelNameList;

    @Schema(description = "软件版本")
    private List<String> firmwareVerList;

    @Schema(description = "分组字段")
    private String groupBy;

    @Schema(description = "排序字段")
    private String orderBy;


}
