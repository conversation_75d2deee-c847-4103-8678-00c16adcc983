package com.cdz360.biz.model.trading.bi.po;

import com.cdz360.biz.model.trading.bi.type.ErpSiteStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "Erp推送记录")
public class BiErpSitePo {

    @Schema(description = "记录ID")
    private Long id;

    @Schema(description = "重推用来指定推送前一条数据的ID")
    private Long lastId;

    @Schema(description = "场站编号")
    private String siteNo;

    @Schema(description = "场站Id")
    private String siteId;

    @Schema(description = "收入时间")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private LocalDateTime incomeTime;

    @Schema(description = "记录推送状态")
    private ErpSiteStatus status;

//    @Schema(description = "场站总功率，单位kw")
//    private BigDecimal totalPower;

    @Schema(description = "场站充电订单总费用，单位:元")
    private BigDecimal totalOrderPrice;

    @Schema(description = "场站充电订单总电量，单位:kwh, 4位小数")
    private BigDecimal totalElectricity;

    @Schema(description = "场站充电订单总电费，单位: 元")
    private BigDecimal totalElectricPrice;

    @Schema(description = "场站充电订单总服务费，单位: 元")
    private BigDecimal totalServicePrice;

    @Schema(description = "推送记录编号(erp系统中的FBillNo字段)")
    private String seqNo;

    @Schema(description = "推送记录ID(erp系统中的FID字段")
    private String erpId;

    @Schema(description = "ERP系统关帐时数据，有值代表关账，没有则没有关账")
    private String erpCostNumber;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "记录创建时间")
    private Date createTime;

    @Schema(description = "记录更新时间")
    private Date updateTime;
}
