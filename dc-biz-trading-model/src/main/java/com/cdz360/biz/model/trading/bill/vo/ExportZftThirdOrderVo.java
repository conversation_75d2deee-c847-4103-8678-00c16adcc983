package com.cdz360.biz.model.trading.bill.vo;

import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.biz.model.annotations.ExcelField;
import com.cdz360.biz.model.trading.bill.type.DailyBillCheckResult;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "对账管理中对账订单导出")
@Data
@Accessors(chain = true)
public class ExportZftThirdOrderVo implements Serializable {

    /**
     * {@link DailyBillCheckResult}
     */
    @ExcelField(title = "订单对账结果", sort = 3)
    @Schema(description = "支付平台对账结果: FULL_MATCH(完全匹配); AMOUNT_NOT_MATCH(金额不匹配); NO_NOT_MATCH(未找到对应订单); OTHERS(其他)")
    @NotNull(message = "checkResult 不能为 null")
    private String checkResult;


    @ExcelField(title = "商家订单号", sort = 6)
    @Schema(description = "平台支付流水号")
    private String platformNo;

//    @ExcelField(title = "支付平台订单号", sort = 8)
//    @Schema(description = "渠道流水号")
//    private String channelNo;


    /**
     * {@link DepositFlowType}
     */
    @ExcelField(title = "收支类型", sort = 20)
    @Schema(description = "交易类型: IN_FLOW(收入), OUT_FLOW(支出)")
    private String tradeType;


    @ExcelField(title = "收入(元)", sort = 22)
    @Schema(description = "收入（单位: 元）")
    private BigDecimal inAmount;

    @ExcelField(title = "支出(元)", sort = 26)
    @Schema(description = "支出（单位: 元）")
    private BigDecimal outAmount;


    @ExcelField(title = "交易时间", sort = 10, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "交易时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "tradeTime 不能为 null")
    private Date tradeTime;

    //    =========== 关联表查询数据(t_zft_daily_bill) 👇 ===============
    @ExcelField(title = "支付平台账单名称", sort = 8)
    @Schema(description = "支付平台账单名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dailyBillName;


    @ExcelField(title = "所属直付商家", sort = 12)
    @Schema(description = "所属直付商家")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String zftName;


    @ExcelField(title = "商家所属商户", sort = 16)
    @Schema(description = "支付商家所属商户")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String zftCommName;


    /**
     * {@link PayChannel}
     */
    @ExcelField(title = "交易渠道", sort = 18)
    @Schema(description = "交易渠道")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String channel;
//    =========== 关联表查询数据(t_zft_daily_bill) 👆 ===============

}
