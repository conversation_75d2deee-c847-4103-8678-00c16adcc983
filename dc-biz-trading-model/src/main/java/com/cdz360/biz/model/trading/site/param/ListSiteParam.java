package com.cdz360.biz.model.trading.site.param;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.finance.type.BizType;
import com.cdz360.biz.model.geo.param.GeoNearParam;
import com.cdz360.biz.model.geo.vo.GpsPointVo;
import com.cdz360.biz.model.site.type.SiteCategory;
import com.cdz360.biz.model.site.type.SiteStatus;
import com.cdz360.biz.model.sys.constant.SiteGroupType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "查询场站列表参数")
public class ListSiteParam extends BaseListParam {


    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private Long topCommId;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private List<Long> commIdList;


    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "场站归属商户ID", hidden = true)
    private Long siteCommId;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private String commIdChain;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private List<Long> corpIdList;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String provinceCode;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cityCode;

    /**
     * 站点类型**0-未知 1-公共 2-个人 3-运营**
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer type;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "场站状态, 不传表示查询所有的. 0, 不可用; 1, 待上线; 2, 可用; 3, 维护中")
    private List<SiteStatus> statusList;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "场站状态, 不传表示查询所有的. 1, 对外开放; 2, 内部使用")
    private List<Integer> scopeList;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "获取4个点圈出的多边形内的场站列表. geoWith 和 geoNear 只能同时使用一个")
    private List<GpsPointVo> geoWith;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "使用距离的方式来搜索场站. geoWith 和 geoNear 只能同时使用一个")
    private GeoNearParam geoNear;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "充电类型, 不传查询所有. AC, 交流; DC, 支流")
    private List<SupplyType> supplyTypeList;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "是否过滤可用枪头, 空表示查询所有")
    private Boolean filterIdlePlug;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "场站ID列表")
    private List<String> siteIdList;

//    @Schema(description = "是否过滤收藏的站点, 空表示查询所有的 查询收藏站点时忽略地理位置查询")
//    private Boolean filterFavorite;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "停车费, 不传查询所有. 0, 未知; 1, 收费; 2, 免费; 3, 限时免费")
    private List<Integer> parkingFeeTypeList;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "支付渠道")
    private List<String> payChannelList;

    /**
     * {@link BizType}
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "运营类型, 不传查询所有. 0, 未知; 1, 自营; 2, 非自营; 3, 互联互通; 4, 脱机自营; 5, 脱机非自营;")
    private List<Integer> bizTypeList;

    @Schema(description = "站点ID或站点编号 模糊匹配")
    private String siteStr;

    @Schema(description = "站点ID或站点名称或站点编号 模糊匹配")
    private String idAndNameAndNoStr;

    /**
     * 站点名称 用于运营支撑-站点管理
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;

    @Schema(description = "场站名称列表，精确查询")
    @JsonInclude(Include.NON_NULL)
    private List<String> exactSiteNameList;

    /**
     * 地址 用于运营支撑-站点管理
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String address;

    @Schema(description = "存在场站编号的场站标识")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean siteNoExist;

    @Schema(description = "0，未知； 1，投建运营； 2，以租代售； 3，纯租赁； 4，EPC+O; 5, 销售的代收代付； 6，代运营； 7，委托运营")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Integer> gcTypeList;

    /**
     * 鼎充专用 开票标识
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer invoicedValid;

    @Schema(description = "开票主体名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String tempSalName;

    @Schema(description = "平台开票")
    private Boolean platformInvoicedValid;
    
    @Schema(description = "是否统计场站总功率(所有桩的功率总和)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean totalPower;

    private Boolean fetchImageList;

    @Schema(description = "是否包含互联互通场站 null or true 表示包含; false 表示不包含")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean includedHlhtSite;

    @Schema(description = "是否只查询有电表的场站 or true 表示 是; null or false 表示 否")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean onlySiteWithMeter;

    @Schema(description = "客户端类型,后端填充", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private AppClientType appClientType;

    @Schema(description = "光储充类型")
    private SiteCategory siteCategory;

    @Schema(description = "场站光储充类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<SiteCategory> categoryList;

    @Schema(description = "场站光储充类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<SiteCategory> andCategoryList;

    @Schema(description = "场站组id列表 仅用于管理后台相关接口")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> gids;

    @Schema(description = "组类型 0未知,1混合,2运营,3运维,4销售")
    private SiteGroupType groupType;

    @Schema(description = "查询结果是否包含组信息")
    private Boolean includeGroup;

    @Schema(description = "请求的用户id")
    private Long userId;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
