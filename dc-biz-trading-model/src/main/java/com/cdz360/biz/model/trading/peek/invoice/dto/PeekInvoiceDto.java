package com.cdz360.biz.model.trading.peek.invoice.dto;

import com.cdz360.base.model.base.vo.BaseObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * PeekInvoiceDto
 *
 * @since 3/22/2023 4:41 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "企业开票记录传输")
public class PeekInvoiceDto extends BaseObject implements Serializable {

    @Schema(description = "应开电费")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal actualElecFee;

    @Schema(description = "应开服务费")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal actualServFee;

    @Schema(description = "应开总额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal totalFee;

}