package com.cdz360.biz.model.trading.profit.sett.po;


import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.trading.profit.sett.type.ProfitCfgCalSource;
import com.cdz360.biz.model.trading.profit.sett.type.ProfitCfgCategory;
import com.cdz360.biz.model.trading.profit.sett.type.ProfitCfgTimeTarget;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
@Schema(description = "结算单")
public class SettJobBillPo {

    @Schema(description = "结算单号")
    @NotNull(message = "billNo 不能为 null")
    @Size(max = 32, message = "billNo 长度不能超过 32")
    private String billNo;

    @Schema(description = "结算任务名称(t_gc_profit_cfg.name)")
    @Size(max = 64, message = "jobName 长度不能超过 64")
    private String jobName;

    @Schema(description = "收入(INCOME)/支出(EXPENSE)")
    @NotNull(message = "category 不能为 null")
    private ProfitCfgCategory jobCategory;

    @Schema(description = "计算来源: FROM_CHARGE_ORDER(来源充电订单); FROM_BI_SYS(来源决策系统)")
    @NotNull(message = "calSource 不能为 null")
    private ProfitCfgCalSource jobCalSource;

    @Schema(description = "场站编号")
    @Size(max = 32, message = "siteId 长度不能超过 32")
    private String siteId;

    @Schema(description = "站点名称")
    @Size(max = 128, message = "siteName 长度不能超过 128")
    private String siteName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "结算周期(开始时间)")
    private Date settPeriodFrom;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "结算周期(结束时间)")
    private Date settPeriodTo;

    @Schema(description = "电量(单位: kW·h)")
    private BigDecimal elec;

    @Schema(description = "电费(单位: 元)")
    private BigDecimal elecFee;

    @Schema(description = "服务费(单位: 元)")
    private BigDecimal servFee;

    @Schema(description = "停充超时费(单位: 元)")
    private BigDecimal parkFee;

    @Schema(description = "计算数据源")
    private String dataSource;

    @Schema(description = "充电订单时间维度【创建时间(1)，支付时间(2)，上传时间(3)，充电开始时间(4)，充电结束时间(5)】")
    private ProfitCfgTimeTarget timeTarget;

    @Schema(description = "(计算规则备份)充电结算公式:{}")
    private List<ChargeOrderRule> chargeOrderRules;

    @Schema(description = "(计算规则备份)数据决策结算公式:{base:{}, range:[{condition: '', {}},...]}")
    private BiRule biRule;

    @Schema(description = "结算任务ID(t_gc_profit_cfg.id)")
    private Long jobId;

    @Schema(description = "是否有效. true/false")
    private Boolean enable;

    @Schema(description = "备注")
    private String remark;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间")
    @NotNull(message = "createTime 不能为 null")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "更新时间")
    @NotNull(message = "updateTime 不能为 null")
    private Date updateTime;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}

