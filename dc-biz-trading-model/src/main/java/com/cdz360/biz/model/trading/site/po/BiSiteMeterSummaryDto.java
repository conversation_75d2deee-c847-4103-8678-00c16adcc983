package com.cdz360.biz.model.trading.site.po;

import com.cdz360.biz.model.iot.type.MeterRecordParamType;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * BiSiteMeterSummaryDto
 *
 * @since 11/8/2021 9:38 AM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
public class BiSiteMeterSummaryDto {
    private Long meterId;
    private String meterName;
    private MeterRecordParamType paramType;
    private List<BiMeterSumPo> biSummaryList;
}