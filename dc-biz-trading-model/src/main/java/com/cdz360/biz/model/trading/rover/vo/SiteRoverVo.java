package com.cdz360.biz.model.trading.rover.vo;

import com.cdz360.biz.model.trading.rover.po.SiteRoverPo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * SiteRoverVo
 *
 * @since 7/27/2022 2:42 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SiteRoverVo extends SiteRoverPo implements Serializable {
    private String commName;
    private String siteName;
    private String siteAddress;
    private Integer expireDays;// 超期时间，负数表示超期，正数表示未来

    @Schema(description = "省份名称")
    private String provinceName;
    @Schema(description = "城市名称")
    private String cityName;
    @Schema(description = "区名称")
    private String areaName;
}