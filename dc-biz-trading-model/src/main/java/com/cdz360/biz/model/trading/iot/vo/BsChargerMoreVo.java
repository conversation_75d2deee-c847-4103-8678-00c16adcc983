package com.cdz360.biz.model.trading.iot.vo;

import com.cdz360.biz.model.trading.iot.po.BsChargerPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class BsChargerMoreVo extends BsChargerPo {


    @Schema(description = "桩名称")
    private String evseName;
}
