package com.cdz360.biz.model.trading.order.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 充值前的账户信息
 *
 * <AUTHOR>
 * @since 2019/11/12 20:48
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode
@ToString
@Schema(description = "充值前的账户信息")
public class PayBillAccountDetailVo {
    @Schema(description = "可用余额, 单位'元', 2位小数")
    private BigDecimal available;

    @Schema(description = "冻结金额, 单位'元', 2位小数")
    private BigDecimal frozen;

    @Schema(description = "变动后,实际成本, 单位'元', 2位小数")
    private BigDecimal cost;

    @Schema(description = "变动后,冻结的成本, 单位'元', 2位小数")
    private BigDecimal frozenCost;
}
