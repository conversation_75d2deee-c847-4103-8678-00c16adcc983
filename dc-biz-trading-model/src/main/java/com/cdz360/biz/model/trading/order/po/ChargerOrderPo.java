package com.cdz360.biz.model.trading.order.po;

import com.cdz360.base.model.base.type.ChargeOrderStatus;
import com.cdz360.base.model.charge.type.HlhtType;
import com.cdz360.base.model.charge.type.OrderAbnormalReason;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.model.charge.type.OrderStopCode;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.cus.type.CusPostStatus;
import com.cdz360.biz.model.trading.order.type.OrderChannelType;
import com.cdz360.biz.model.trading.order.type.OrderCompleteCode;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ChargerOrderPo {

    public ChargerOrderPo() {

    }

    public ChargerOrderPo(String orderNo) {
        this.orderNo = orderNo;
    }

    @Schema(description = "订单号")
    private String orderNo;

    @Schema(description = "集团商户ID", example = "12345")
    private Long topCommId;

    @Schema(description = "用户ID")
    private Long customerId;

    @Schema(description = "现金账户/商户会员支付时为商户ID;授信账户支付时为授信账户ID")
    private Long payAccountId;

    @Schema(description = "1个人账户(t_balance)2集团授权账户(t_r_bloc_user)")
    private Integer defaultPayType;

    @Schema(description = "订单冻结金额, 单位'元'")
    private BigDecimal frozenAmount;

    @Schema(description = "微信小程序formId")
    private String formId;

    @Schema(description = "状态,-500：硬件故障；-40：上报超时；-35：断连超时；-30关电超时；-29：结束充电失败；-20：链接超时；-10：打开电闸超时；-7：开启充电失败，PIN可能错误；-5:系统繁忙；0：订单未激活；100：电闸打开；200：充电中；250：关电闸，充电停止；300：充电完成；800：待支付；1000：用户已支付；2000：钱已到账")
    private Integer status;

    @Schema(description = "订单状态(枚举 字符)")
    private ChargeOrderStatus orderStatus;

//    @Schema(description = "订单ID(废弃)")
//    private Long orderId;

//    @Schema(description = "顶级商户ID")
//    private Long commercialId;

    @Schema(description = "站点ID")
    private String stationId;

    @Schema(description = "app订单号")
    private String openOrderId;

    @Schema(description = "互联互通渠道方订单对账结果")
    private Integer hlhtConfirm;

    @Schema(description = "设备运营商名称")
    private String commercialName;

//    @Schema(description = "客户运营商ID")
//    private Long customerCommercialId;

    @Schema(description = "客户运营商名称")
    private String customerCommercialName;

    @Schema(description = "充电卡号/身份唯一识别号")
    private String cardNo;

    @Schema(description = "卡片芯片号 物理卡号")
    private String cardChipNo;

    @Schema(description = "卡名称")
    private String cardName;

    @Schema(description = "订单来源类型")
    private OrderChannelType channelId;

    @Schema(description = "充电启动方式")
    private OrderStartType orderType;

    @Deprecated // 订单结算流程不要使用
    @Schema(description = "订单金额, 单位'元'")
    private BigDecimal orderPrice;  // 写操作已替换

    @Deprecated // 订单结算流程不要使用
    @Schema(description = "服务费, 单位'元'")
    private BigDecimal servicePrice; // 写操作已替换

    @Schema(description = "服务费实收金额, 单位'元'")
    private BigDecimal servActualFee; // 写操作已替换

    @Deprecated // 订单结算流程不要使用
    @Schema(description = "电费, 单位'元'")
    private BigDecimal elecPrice;// 写操作已替换

    @Schema(description = "电费实收金额, 单位'元'")
    private BigDecimal elecActualFee;// 写操作已替换

    @Schema(description = "是否有手动修改")
    private Boolean manual;

    @Schema(description = "人工调整后金额, 单位'元'")
    private BigDecimal manualPrice;

    @Deprecated // 订单结算流程不要使用
    @Schema(description = "客户实付金额, 单位'元'")
    private BigDecimal actualPrice; // 写操作已替换

    @Schema(description = "电损金额. 单位'元'")
    private BigDecimal discount;

    @Schema(description = "结算方式")
    private Integer clearingMode;

    @Schema(description = "订单电量, 单位'kwh', 4位小数")
    private BigDecimal orderElectricity;

//    @Schema(description = "星标标志，默认0，0：非星标；1：星标；")
//    private Integer starFlag;

    @Schema(description = "计费模板ID")
    private Long priceSchemeId;


    @Schema(description = "备注")
    private String remark;

    @Schema(description = "订单创建时间")
    private Date createTime;

    @Schema(description = "订单结束时间")
    private Date stopTime;

    @Schema(description = "订单状态修改时间")
    private Date updateTime;

//    @Schema(description = "显示字段 内容：出厂编号+evseId+connectorId")
//    private String showId;

    public String getShowId() {
        return this.plugNo;
    }

    @Schema(description = "充电接口(枪)编号")
    private String connectId;

    @Schema(description = "充电开始时间")
    private Long chargeStartTime;

    @Schema(description = "充电结束时间")
    private Long chargeEndTime;

    @Schema(description = "充电开始电量,单位'kwh'")
    private BigDecimal startElectricity;

    @Schema(description = "充电结束电量,单位'kwh'")
    private BigDecimal endElectricity;

    @Schema(description = "platformId区分app或者微信或者其他平台发起的充电的回传标示")
    private String platformId;

    @Schema(description = "手机号码")
    private String mobilePhone;

    @Schema(description = "站点名称")
    private String stationName;

    @Schema(description = "支付方式")
    private Integer payModes;

    @Schema(description = "结算方式")
    private SettlementType settlementType;

    @Schema(description = "是否支持实时续费")
    private Boolean renewal;

    @Schema(description = "支付状态")
    private Integer payStatus;

    @Schema(description = "企业ID")
    private Long corpId;

    @Schema(description = "企业支付状态")
    private Integer corpPayStatus;

    @Schema(description = "支付单号")
    private String billNo;

    @Schema(description = "结算时间")
    private Date payTime;

    @Schema(description = "客户名称")
    private String customerName;

    @Schema(description = "合作方（互联互通）编号")
    private String openOperatorId;

    @Schema(description = "互联互通类型")
    private HlhtType hlhtType;

    @Schema(description = "设备运营商ID")
    private Long deviceCommercialId;

    @Schema(description = "core推送发起充电超时，通知第三方结束订单标志 0:正常 其他异常")
    private Integer timeoutStatus;

    @Schema(description = "本金（实际收入）元")
    private BigDecimal principalAmount;// 写操作已替换

    @Schema(description = "本金（实际收入）元")
    private BigDecimal freeGoldAmount;  // 写操作已替换

    @Schema(description = "盒子编码")
    private String boxCode;

    @Schema(description = "枪头编号")
    private String plugNo;

    @Schema(description = "充电时长")
    private Integer duration;

    @Schema(description = "客户端版本号")
    private String version;

    @Schema(description = "订单异常结算原因")
    private String exceptionReason;

    @Schema(description = "插座二维码")
    private String qrCode;

    @Schema(description = "插座序列号")
    private Integer connectorId;

    @Schema(description = "充电是否提前结束标示")
    private Integer endUnexpected;

    private Integer startSoc;

    private Integer stopSoc;

    @Schema(description = "限制的最大SOC")
    private Integer limitSoc;

    @Schema(description = "期望限制SOC，仅做消息推送，不存入redis")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer expectLimitSoc;

    @Schema(description = "充电完成原因")
    private OrderCompleteCode completeCode;

    @Schema(description = "停止原因")
    private String stopReason;

    @Schema(description = "停车原因 0表示正常")
    private OrderStopCode stopCode;

    private Integer currentSoc;

    @Schema(description = "单次充电优惠金额--等级优惠金额(单位分)")
    private Long discountMoney;// 后续版本可移除

    @Schema(description = "优惠券金额 元")
    private BigDecimal couponMoney;// 写操作已替换

    @Schema(description = "积分优惠金额 元")
    private BigDecimal scoreAmount;

    @Schema(description = "活动ID")
    private Long activityId;// 写操作已替换

    @Schema(description = "鼎充开票Id（0：表示 没有关联开票申请Id,大于0表示已经关联开票ID） ")
    private Long invoicedId;

    @Schema(description = "可开票金额, 单位'元'")
    private BigDecimal invoiceAmount;// 写操作已替换

    @Schema(description = "已开票金额: 单位: 元")
    private BigDecimal invoicedAmount;  // 写操作已替换

    @Schema(description = "枪头名称")
    private String chargerName;

    @Schema(description = "null, 正常; 0, 未知异常; 1, 心跳超时; 2, 订单更新超时; 3, 订单电量越限; 4, 订单金额越限; 5, 充电中超时; 6, 启动中超时; 7, 充电中停用")
    private OrderAbnormalReason abnormal;

    @Schema(description = "是否停充超时")
    private Integer overtimeParking;

    @Schema(description = "小功率充电时长")
    private Integer lowKwDur;

    @Schema(description = "评论状态: UNKNOWN(待评论), SUBMITTED(已评论), REPLIED(已回复)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private CusPostStatus cusPostStatus;

    public void copyNonNull(ChargerOrderPo in) {
        if (in.orderNo != null) {
            this.orderNo = in.orderNo;
        }
        if (in.topCommId != null) {
            this.topCommId = in.topCommId;
        }
        if (in.customerId != null) {
            this.customerId = in.customerId;
        }
        if (in.payAccountId != null) {
            this.payAccountId = in.payAccountId;
        }
        if (in.defaultPayType != null) {
            this.defaultPayType = in.defaultPayType;
        }
        if (in.frozenAmount != null) {
            this.frozenAmount = in.frozenAmount;
        }
        if (in.formId != null) {
            this.formId = in.formId;
        }
        if (in.status != null) {
            this.status = in.status;
        }
        if (in.orderStatus != null) {
            this.orderStatus = in.orderStatus;
        }
//        if (in.orderId != null) {
//            this.orderId = in.orderId;
//        }
        if (in.stationId != null) {
            this.stationId = in.stationId;
        }
        if (in.openOrderId != null) {
            this.openOrderId = in.openOrderId;
        }
        if (in.commercialName != null) {
            this.commercialName = in.commercialName;
        }
        if (in.customerCommercialName != null) {
            this.customerCommercialName = in.customerCommercialName;
        }
        if (in.cardNo != null) {
            this.cardNo = in.cardNo;
        }
        if (in.cardChipNo != null) {
            this.cardChipNo = in.cardChipNo;
        }
        if (in.cardName != null) {
            this.cardName = in.cardName;
        }
        if (in.channelId != null) {
            this.channelId = in.channelId;
        }
        if (in.orderType != null) {
            this.orderType = in.orderType;
        }
        if (in.manual != null) {
            this.manual = in.manual;
        }
        if (in.manualPrice != null) {
            this.manualPrice = in.manualPrice;
        }

        if (in.discount != null) {
            this.discount = in.discount;
        }
        if (in.clearingMode != null) {
            this.clearingMode = in.clearingMode;
        }
        if (in.orderElectricity != null) {
            this.orderElectricity = in.orderElectricity;
        }
        if (in.priceSchemeId != null) {
            this.priceSchemeId = in.priceSchemeId;
        }
        if (in.remark != null) {
            this.remark = in.remark;
        }
        if (in.createTime != null) {
            this.createTime = in.createTime;
        }
        if (in.stopTime != null) {
            this.stopTime = in.stopTime;
        }
        if (in.updateTime != null) {
            this.updateTime = in.updateTime;
        }
        if (in.connectId != null) {
            this.connectId = in.connectId;
        }
        if (in.chargeStartTime != null) {
            this.chargeStartTime = in.chargeStartTime;
        }
        if (in.chargeEndTime != null) {
            this.chargeEndTime = in.chargeEndTime;
        }
        if (in.startElectricity != null) {
            this.startElectricity = in.startElectricity;
        }
        if (in.endElectricity != null) {
            this.endElectricity = in.endElectricity;
        }
        if (in.platformId != null) {
            this.platformId = in.platformId;
        }
        if (in.mobilePhone != null) {
            this.mobilePhone = in.mobilePhone;
        }
        if (in.stationName != null) {
            this.stationName = in.stationName;
        }
        if (in.payModes != null) {
            this.payModes = in.payModes;
        }
        if (in.settlementType != null) {
            this.settlementType = in.settlementType;
        }
        if (in.renewal != null) {
            this.renewal = in.renewal;
        }
        if (in.payStatus != null) {
            this.payStatus = in.payStatus;
        }
        if (in.corpPayStatus != null) {
            this.corpPayStatus = in.corpPayStatus;
        }
        if (in.billNo != null) {
            this.billNo = in.billNo;
        }
        if (in.payTime != null) {
            this.payTime = in.payTime;
        }
        if (in.customerName != null) {
            this.customerName = in.customerName;
        }
        if (in.openOperatorId != null) {
            this.openOperatorId = in.openOperatorId;
        }
        if (in.hlhtType != null) {
            this.hlhtType = in.hlhtType;
        }
        if (in.deviceCommercialId != null) {
            this.deviceCommercialId = in.deviceCommercialId;
        }
        if (in.timeoutStatus != null) {
            this.timeoutStatus = in.timeoutStatus;
        }

        if (in.boxCode != null) {
            this.boxCode = in.boxCode;
        }
        if (in.plugNo != null) {
            this.plugNo = in.plugNo;
        }
        if (in.duration != null) {
            this.duration = in.duration;
        }
        if (in.version != null) {
            this.version = in.version;
        }
        if (in.exceptionReason != null) {
            this.exceptionReason = in.exceptionReason;
        }
        if (in.qrCode != null) {
            this.qrCode = in.qrCode;
        }
        if (in.connectorId != null) {
            this.connectorId = in.connectorId;
        }
        if (in.endUnexpected != null) {
            this.endUnexpected = in.endUnexpected;
        }
        if (in.startSoc != null) {
            this.startSoc = in.startSoc;
        }
        if (in.stopSoc != null) {
            this.stopSoc = in.stopSoc;
        }
        if (in.limitSoc != null) {
            this.limitSoc = in.limitSoc;
        }
        if (in.stopReason != null) {
            this.stopReason = in.stopReason;
        }
        if (in.stopCode != null) {
            this.stopCode = in.stopCode;
        }
        if (in.currentSoc != null) {
            this.currentSoc = in.currentSoc;
        }

        if (in.chargerName != null) {
            this.chargerName = in.chargerName;
        }
        if (in.abnormal != null) {
            this.abnormal = in.abnormal;
        }
        if (in.overtimeParking != null) {
            this.overtimeParking = in.overtimeParking;
        }

    }


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
