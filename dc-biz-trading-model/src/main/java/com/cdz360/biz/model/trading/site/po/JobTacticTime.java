package com.cdz360.biz.model.trading.site.po;

import com.cdz360.biz.model.trading.site.type.TimerOperation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class JobTacticTime {

    @Schema(description = "操作方式", example = "START", required = true)
    private TimerOperation operation;

    @Schema(description = "时间", example = "12:00", required = true)
    private String time;
}
