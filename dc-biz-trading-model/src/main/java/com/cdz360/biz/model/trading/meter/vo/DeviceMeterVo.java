package com.cdz360.biz.model.trading.meter.vo;

import com.cdz360.biz.model.trading.meter.po.DeviceMeterPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * EvseMeterVo
 *
 * @since 1/27/2021 1:31 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@Schema(description = "设备(桩或PCS)-电表绑定关系Vo")
@EqualsAndHashCode(callSuper = true)
public class DeviceMeterVo extends DeviceMeterPo {
    private String deviceName;
}