package com.cdz360.biz.model.trading.site.vo;

import com.cdz360.biz.model.trading.site.po.SiteOvertimeParkDivisionPo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * SiteOvertimeParkDivisionVo
 *
 * @since 4/28/2024 9:38 AM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "占位分时详情 vo")
public class SiteOvertimeParkDivisionVo extends SiteOvertimeParkDivisionPo {

    // 实际时长，分钟
    @JsonInclude(Include.NON_EMPTY)
    private long duration;

    // 分段开始时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonInclude(Include.NON_EMPTY)
    private Date divisionStartTime;

    // 分段结束时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonInclude(Include.NON_EMPTY)
    private Date divisionEndTime;

    // 该时段收费 = 时间 * 单价
    @JsonInclude(Include.NON_EMPTY)
    private BigDecimal divisionFee;
}