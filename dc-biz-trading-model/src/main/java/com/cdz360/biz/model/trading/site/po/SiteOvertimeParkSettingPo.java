package com.cdz360.biz.model.trading.site.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.lang.Double;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel(value = "场站占位分时设置")
public class SiteOvertimeParkSettingPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@Size(max = 32, message = "siteId 长度不能超过 32")
	private String siteId;

	@ApiModelProperty(value = "单次封顶")
	private BigDecimal maxFee;

	private Boolean enable;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;


}
