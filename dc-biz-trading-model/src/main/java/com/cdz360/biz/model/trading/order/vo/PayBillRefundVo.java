package com.cdz360.biz.model.trading.order.vo;

import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.biz.model.trading.deposit.vo.DepositSourceType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "充值记录(包含可退款金额)")
@Data
@Accessors(chain = true)
public class PayBillRefundVo {

    @Schema(description = "充值订单号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String orderId;

    @Schema(description = "充值总金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal totalAmount;

    @Schema(description = "实际金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal amount;

    @Schema(description = "赠送金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal freeAmount;

    @Schema(description = "可退款金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal canRefundAmount;

    @Schema(description = "充值来源")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private DepositSourceType sourceType;

    @Schema(description = "支付方式")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private PayChannel payChannel;

    @Schema(description = "创建时间: yyyy-MM-dd HH:mm:ss", example = "2019-11-11 19:22:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date createTime;

    @Schema(description = "支付时间: yyyy-MM-dd HH:mm:ss", example = "2019-11-11 19:22:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date payTime;
}
