package com.cdz360.biz.model.trading.coupon.vo;

import com.cdz360.biz.model.trading.coupon.po.CouponPo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * CouponVo
 *
 * @since 7/31/2020 5:10 PM
 * <AUTHOR>
 */

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "活动券列表")
public class CouponVo extends CouponPo {
    private String phone;

    private String dictName;

    private String activityName;

    @Schema(description = "总电量")
    private BigDecimal orderElec;

    @Schema(description = "标准电费")
    private BigDecimal elecOriginFee;

    @Schema(description = "总电费")
    private BigDecimal elecFee;

    @Schema(description = "标准服务费")
    private BigDecimal servOriginFee;

    @Schema(description = "总服务费")
    private BigDecimal servFee;

    @Schema(description = "充电总金额")
    private BigDecimal orderFee;

    @Schema(description = "订单所属场站")
    private String siteName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date orderCreateTime;
}