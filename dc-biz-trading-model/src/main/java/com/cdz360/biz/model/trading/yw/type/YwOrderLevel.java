package com.cdz360.biz.model.trading.yw.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "运维工单故障等级")
@Getter
public enum YwOrderLevel implements DcEnum {
    UNKNOWN(0, "未知"),
    NORMAL(10, "一般"),
    URGENT(20, "紧急"),
    IMPORTANT(30, "重大")
    ;

    @JsonValue
    private int code;

    private String desc;

    YwOrderLevel(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static YwOrderLevel valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (YwOrderLevel level : values()) {
            if (level.code == code) {
                return level;
            }
        }

        return YwOrderLevel.UNKNOWN;
    }
}
