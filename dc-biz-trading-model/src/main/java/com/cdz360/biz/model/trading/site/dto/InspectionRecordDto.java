package com.cdz360.biz.model.trading.site.dto;

import com.cdz360.biz.model.trading.site.type.SiteInspectionStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "巡检工单")
public class InspectionRecordDto implements Serializable {

    private Long id;

    @Schema(description = "巡检工单")
    private String no;

    @Schema(description = "场站所属商户")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long commId;

    @Schema(description = "所属商户名称")
    private String commName;

    @Schema(description = "场站运维组ID")
    private String siteGid;

    @Schema(description = "场站运维组名称")
    private String siteGidName;

    private String siteId;

    @Schema(description = "站点名称")
    private String siteName;

    @Schema(description = "直流桩数")
    private Integer acEvseNum;

    @Schema(description = "交流桩数")
    private Integer dcEvseNum;

    private Long opUid;

    @Schema(description = "巡检创建人(同巡检人)")
    private String rummager;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "巡检时间")
    private Date reportTime;

    private String remark;

    private SiteInspectionStatus status;

    private Long qcUid;

    @Schema(description = "质检人")
    private String qcUserName;

    @Schema(description = "质检人备注")
    private String qcRemark;

    @Schema(description = "巡检单类型")
    private Integer inspectionType;

    @Schema(description = "质检评分")
    private Long score;
}
