package com.cdz360.biz.model.trading.coupon.vo;

import com.cdz360.biz.model.trading.coupon.type.OrderCouponType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "充电订单优惠券信息")
@Data
@Accessors(chain = true)
public class OrderCouponVo {

    @Schema(description = "充电订单优惠券类型")
    private OrderCouponType orderCouponType;

    @Schema(description = "农行优惠券ID")
    @NotNull(message = "couponId 不能为 null")
    private String couponId;

    @Schema(description = "优惠券金额（单位元）")
    private BigDecimal couponAmount;

    /**
     * @see com.cdz360.biz.model.trading.coupon.type.AbcCouponUsed
     */
    @Schema(description = "优惠券使用状态")
    private Integer used;
}
