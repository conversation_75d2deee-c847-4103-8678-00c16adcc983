package com.cdz360.biz.model.trading.yw.type;

import com.cdz360.base.model.base.type.DcEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

/**
 * 0) 运维
 * 1) C段客户故障报修 -- C端客户
 * 2) 账户所属商户与顶级商户不一样 -- 商户
 * 3) 账户所属商户与顶级商户一样:
 *       ① 有客户角色 -- 客服
 *       ② 没有客服角色 -- 其他
 */
@Schema(description = "运维工单来源(创建人所属角色归类)")
@Getter
public enum CreateYwOrderSourceType implements DcEnum {
    UNKNOWN(0, "未知"),

    CUSTOMER_SRC(2, "C端客户"),

    COMM_SRC(20, "商户"),

    YW_SRC(23, "运维"),

    KF_SRC(30, "客服"),

    DEVICE_WARNING(50, "设备告警"),

    OTHER_SRC(99, "其他")
    ;


    private final int code;
    private final String desc;

    CreateYwOrderSourceType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static CreateYwOrderSourceType valueOf(int code) {
        for (CreateYwOrderSourceType source : values()) {
            if (source.code == code) {
                return source;
            }
        }
        return CreateYwOrderSourceType.UNKNOWN;
    }

}
