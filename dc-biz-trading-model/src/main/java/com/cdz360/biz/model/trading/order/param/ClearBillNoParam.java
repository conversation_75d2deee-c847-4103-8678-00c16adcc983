package com.cdz360.biz.model.trading.order.param;

import com.cdz360.base.model.base.vo.BaseObject;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "清空账单编号")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ClearBillNoParam extends BaseObject {

    @Schema(description = "账单编号")
    private String billNo;

    @Schema(description = "充电订单号")
    private List<String> orderNoList;


}
