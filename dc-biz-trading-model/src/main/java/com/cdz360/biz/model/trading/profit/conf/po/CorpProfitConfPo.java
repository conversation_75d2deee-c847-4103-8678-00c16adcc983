package com.cdz360.biz.model.trading.profit.conf.po;

import com.cdz360.biz.model.trading.profit.conf.type.CalculateRuleType;
import com.cdz360.biz.model.trading.profit.conf.type.CorpProfitConfType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "收益计算配置")
public class CorpProfitConfPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	private Long corpId;
	private Boolean enable;

	@Schema(description = "算法：金额比例，电量，汇总梯度，自定义算法")
	@NotNull(message = "type 不能为 null")
	private CorpProfitConfType type;

	@Schema(description = "电费百分比：70表示70%")
	private BigDecimal elecFeeRate;

	@Schema(description = "服务费百分比：30.5表示30.5%")
	private BigDecimal servFeeRate;

	@Schema(description = "每度电收益（元）")
	private BigDecimal powerFee;

	@Schema(description = "开始电量")
	private BigDecimal elecRangeFrom;

	@Schema(description = "结束电量")
	private BigDecimal elecRangeTo;

	@Schema(description = "扣减每度电电费金额(元)")
	private BigDecimal elecFeePowerDeduction;

	@Schema(description = "扣减每度电服务费金额(元)")
	private BigDecimal servFeePowerDeduction;

	@Schema(description = "规则1 扣减 规则2 百分比")
	private CalculateRuleType calculateRule;

	@Schema(description = "注释")
	private String comment;

	@NotNull(message = "createTime 不能为 null")
	private Date createTime;

	private Date updateTime;


}
