package com.cdz360.biz.model.trading.camera.dto;

import com.cdz360.biz.model.trading.camera.po.CameraSitePo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "摄像头所在场站信息")
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CameraSiteDto extends CameraSitePo {

    @Schema(description = "场站对应的省份名称")
    private String province;

    @Schema(description = "场站对应的城市名称")
    private String city;

    @Schema(description = "场站对应的区县名称")
    private String district;

    @Schema(description = "场站对应的区县编号")
    private String siteDistrictCode;
}
