package com.cdz360.biz.model.trading.warn.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "查询告警列表参数")
public class UserWarnListParam extends WarnListParam {
    private Long sysUid;
    private Boolean userEnable;
}
