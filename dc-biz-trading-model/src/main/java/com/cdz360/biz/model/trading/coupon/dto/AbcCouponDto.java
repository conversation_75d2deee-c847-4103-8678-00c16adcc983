package com.cdz360.biz.model.trading.coupon.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "DTO农行优惠券信息")
public class AbcCouponDto {

    @Schema(description = "用户手机号")
    @NotNull(message = "phone 不能为 null")
    @Size(max = 20, message = "phone 长度不能超过 20")
    private String phone;

    @Schema(description = "农行优惠券ID")
    @NotNull(message = "couponId 不能为 null")
    @Size(max = 128, message = "couponId 长度不能超过 128")
    private String couponId;

    @Schema(description = "总金额（单位元）")
    @NotNull(message = "totalAmount 不能为 null")
    private BigDecimal totalAmount;

    @Schema(description = "实付金额（单位元）")
    @NotNull(message = "payAmount 不能为 null")
    private BigDecimal payAmount;

    @Schema(description = "优惠券金额（单位元）")
    @NotNull(message = "couponAmount 不能为 null")
    private BigDecimal couponAmount;

    @Schema(description = "交易订单号(我方)")
    @NotNull(message = "tradeNo 不能为 null")
    @Size(max = 32, message = "tradeNo 长度不能超过 32")
    private String tradeNo;

    @Schema(description = "充电订单号")
    @NotNull(message = "orderNo 不能为 null")
    @Size(max = 32, message = "orderNo 长度不能超过 32")
    private String orderNo;

    @Schema(description = "核销时间")
    @NotNull(message = "checkTime 不能为 null")
    private Date checkTime;

}
