package com.cdz360.biz.model.trading.order.dto;

import com.cdz360.base.utils.JsonUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Schema(description = "充电订单统计")
@Accessors(chain = true)
public class OrderThinBiDto {

    @Schema(description = "订单数", example = "123")
    private Long orderNum;

    @Schema(description = "电量", example = "123.3456")
    private BigDecimal elec;

    @Schema(description = "电费", example = "123.12")
    private BigDecimal elecFee;

    @Schema(description = "服务费", example = "123.45")
    private BigDecimal servFee;

    @Schema(description = "超停费", example = "123.45")
    private BigDecimal overParkFee;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
