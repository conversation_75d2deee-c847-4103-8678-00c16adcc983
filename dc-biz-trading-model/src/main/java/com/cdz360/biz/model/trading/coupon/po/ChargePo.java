package com.cdz360.biz.model.trading.coupon.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * 充赠活动，满减金额
 */
@Data
@Accessors(chain = true)
public class ChargePo {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "活动ID")
    private Long activityId;

    @Schema(description = "满（元）")
    private BigDecimal chargeAmount;

    @Schema(description = "减（元）")
    private BigDecimal discountAmount;

    @Schema(description = "是否可用")
    private Boolean enable;
}