package com.cdz360.biz.model.trading.site.po;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "运营场站月收入项")
@Data
@Accessors(chain = true)
public class BiSiteMonthIncomePo {

    @Schema(description = "场站ID")
    @JsonInclude(Include.NON_NULL)
    private String siteId;

    @Schema(description = "年月")
    @JsonInclude(Include.NON_NULL)
    private Date month;

    @Schema(description = "分类. 电费,租金,劳务,分成,引流,运维,折旧,其他")
    @JsonInclude(Include.NON_EMPTY)
    private String category;

    @Schema(description = "名称")
    @JsonInclude(Include.NON_EMPTY)
    private String name;

    @Schema(description = "金额,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal fee;

    @Schema(description = "数量. 电费时为电量")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal number;
}
