package com.cdz360.biz.model.trading.hlht.vo;

import com.cdz360.biz.model.trading.hlht.po.HlhtSitePo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class HlhtSiteVo extends HlhtSitePo {

    private Long partnerId;

    private String partnerName;

    @Schema(description = "绑定的商户ID")
    private List<Long> bindCommIdList;

    private String provinceName;

    private String cityName;

}
