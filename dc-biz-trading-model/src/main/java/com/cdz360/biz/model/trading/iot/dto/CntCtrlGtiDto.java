package com.cdz360.biz.model.trading.iot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * CntCtrlGtiDto
 *
 * @since 9/3/2021 1:27 PM
 * <AUTHOR>
 */
@Schema(description = "光伏站控制器/逆变器数量信息")
@Data
@Accessors(chain = true)
public class CntCtrlGtiDto {
    @Schema(description = "光伏站控制器数量")
    private int ctrlNum;

    @Schema(description = "逆变器数量")
    private int gtiNum;

    @Schema(description = "光储Ess数量")
    private int essNum;
}