package com.cdz360.biz.model.trading.deposit.vo;

import com.cdz360.base.model.base.type.DcEnum;
import lombok.Getter;

/**
 * 充值类型. 充值/减少
 */
@Getter
public enum DepositFlowType implements DcEnum {
    UNKNOWN(0, "未知"),

    IN_FLOW(1, "充值"),

    OUT_FLOW(2, "减少"),


    ;

    private final int code;
    private final String desc;

    DepositFlowType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DepositFlowType valueOf(int code) {
        for (DepositFlowType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return DepositFlowType.UNKNOWN;
    }

}
