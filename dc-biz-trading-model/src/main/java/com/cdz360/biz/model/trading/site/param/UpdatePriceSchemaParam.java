package com.cdz360.biz.model.trading.site.param;

import com.cdz360.base.utils.JsonUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)

@EqualsAndHashCode(callSuper = true)
@Schema(description = "修改计费模板参数")
public class UpdatePriceSchemaParam extends AddPriceSchemaParam {

    @Schema(description = "计费模板ID", example = "1234")
    private Long id;

    @Schema(description = "主模板编号**唯一标识**", example = "ABCD1234")
    private String code;


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
