package com.cdz360.biz.model.trading.site.vo;

import com.cdz360.biz.model.trading.site.type.SiteOrderAccountType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "场站客群趋势数据")
@Data
@Accessors(chain = true)
public class SiteOrderAccountData {

    @Schema(description = "时间 横坐标显示使用，按月份的时候前端需要调整显示格式")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date time;

    @Schema(description = "充电账户类型 UNKNOWN(无结算账户)；PREPAY(即充即退)；PERSONAL(个人账户)；COMMERCIAL(商户会员)；NORMAL_CORP(普通企业客户)；HLHT_CORP(互联企业客户)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private SiteOrderAccountType accountType;

    @Schema(description = "账户充电订单总金额(单位: 元)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal orderFee;

    @Schema(description = "账户充电订单量(单位: 个)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long orderCnt;
}
