package com.cdz360.biz.model.trading.site.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "站点oa流程默认值配置")
@Data
@Accessors(chain = true)
public class SiteOaDefaultConfigPo {

    @Schema(description = "场站ID")
    @JsonInclude(Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "oa流程KEY")
    @JsonInclude(Include.NON_EMPTY)
    private String procDefKey;

    @Schema(description = "oa流程名称")
    @JsonInclude(Include.NON_EMPTY)
    private String procDefName;

    @Schema(description = "默认值配置")
    @JsonInclude(Include.NON_EMPTY)
    private List<SiteOaDefaultConfigItem> defaultValue;

    @Schema(description = "创建时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "更新时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
