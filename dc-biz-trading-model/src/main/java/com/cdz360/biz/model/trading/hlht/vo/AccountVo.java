package com.cdz360.biz.model.trading.hlht.vo;

import com.cdz360.base.model.charge.type.SettlementType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class AccountVo {
    private Long accountId;

    private Long corpId;

    private String corpName;

    private Long topCommId;

    private Long commId;

    @Schema(description = "所属商户名称")
    private String commName;

    private SettlementType settlementType;

    @Schema(description = "余额")
    private BigDecimal balance;

    @Schema(description = "可用站点")
    private List<String> siteIdList;

    @Schema(description = "状态")
    private Boolean corpEnable;
}
