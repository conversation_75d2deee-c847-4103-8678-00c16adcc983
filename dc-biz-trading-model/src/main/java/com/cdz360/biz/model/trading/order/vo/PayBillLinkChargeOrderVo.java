package com.cdz360.biz.model.trading.order.vo;

import com.cdz360.biz.model.finance.type.TaxStatus;
import com.cdz360.biz.model.finance.type.TaxType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 充值订单关联充电订单
 *
 * <AUTHOR>
 * @since 2019/11/11 18:58
 */
@Data
@EqualsAndHashCode
@ToString
@Accessors(chain = true)
@Schema(description = "充值订单关联充电订单")
public class PayBillLinkChargeOrderVo {
    @Schema(description = "充值订单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderId;

    @Schema(description = "充电订单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderNo;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @Schema(description = "充电订单创建时间", example = "2019-11-11 15:21:00")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date createTime;

    @Schema(description = "客户手机号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String phone;

    @Schema(description = "扣款账户Id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long payAccountId;

    @Schema(description = "扣款账户名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String payAccountName;

    @Schema(description = "站点名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;

    @Schema(description = "充电订单总金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal totalAmount;

    @Schema(description = "充电订单赠送金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal orderFreeAmount;

    @Schema(description = "充电订单使用资金块的实际金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal amount;

    @Schema(description = "充电订单使用资金块的赠送金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal freeAmount;

    @Schema(description = "充电订单对应充值记录的可开票金额: 单位，元")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal payBillOrderInvoiceAmount;

    @Schema(description = "充值记录已开票金额: 单位，元")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal payBillInvoicedAmount;

    @Schema(description = "发票类型(税种): UNKNOWN(0)-未知," +
            "NORMAL_TAX(2)-个人普通发票,PREPAY_TAX(3)-企业普通发票," +
            "SPECIAL_VAT(5)-企业专业发票 充电订单的开票类型，与充值记录无关(可根据invoiceId来判断获取)", example = "UNKNOWN"
    )
    private TaxType taxType;

    @Schema(description = "发票状态: NO(0)-未开票, YES(1)-已开票 充电订单的发票状态，与充值记录无关", example = "NO")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TaxStatus taxStatus;

    @Schema(description = "税票号 充电订单的税票号，与充值记录无关(可根据invoiceId来判断获取)")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String taxNo;

//    @Schema(description = "对应发票的开票状态", description = "新逻辑调整后该字段无意义")
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String invoicedStatus;

    @Schema(description = "充电订单状态: 2000 时为已结算，其他是占用")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer orderStatus;

    @Schema(description = "服务费")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal servicePrice;//'服务费, 单位"元"' decimal(10,2)

    @Schema(description = "电费")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal elecPrice;//电费, 单位"元"' decimal(10,2)

    @Schema(description = "可开票金额: 单位，元")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal invoiceAmount;

    @Schema(description = "已开票金额: 单位，元")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal invoicedAmount;
}
