package com.cdz360.biz.model.trading.pv.po;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "光伏逆变器电网调度配置")
public class GtiGridDispatchCfgPo {

    @Schema(description = "主键cfgId")
    @NotNull(message = "cfgId 不能为 null")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long cfgId;

    @Schema(description = "采样时间间隔，单位: 秒")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer samplingTime;

    @Schema(description = "开关机（0：关机；1：开机）")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean systemSwitch;

    @Schema(description = "系统时间（0：不同步；1：同步）")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean systemTime;

    @Schema(description = "时区(分钟) [-720,840]")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer timezone;

    @Schema(description = "Q-U特征曲线模式（0：非滞环；1：滞环）")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean quCurveMode;

    @Schema(description = "Q-U调度触发功率百分比 [0,100]")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer quDispatchPowerPct;

    @Schema(description = "有功功率固定值降额(kW) [0,Pmax]")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal activePowerFixedDeratingKw;

    @Schema(description = "无功功率补偿PF (-1, -0.8]U[0.8, 1]")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal reactivePowerCompensationPf;

    @Schema(description = "无功功率补偿Q/S [-1,1]")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal reactivePowerCompensationQs;

    @Schema(description = "有功功率百分比降额(0.1%) [0,100]")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal activePowerPctDerating;

    @Schema(description = "有功功率固定值降额(W) [0,Pmax]")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal activePowerFixedDeratingW;

    @Schema(description = "夜间无功功率补偿(kVar) [-Qmax,Qmax]")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal nightReactivePowerCompensation;

    @Schema(description = "无功功率调整时间(秒) [1,120]")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer reactivePowerAdjustmentTime;

    @Schema(description = "Q-U调度退出功率百分比(%) [0,100]")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer quExitPowerPct;

    @Schema(description = "无功功率变化梯度(%/s) [0.1,1000]")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal reactivePowerChangeGradient;

    @Schema(description = "有功功率变化梯度(%/s) [0.1,1000]")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal activePowerChangeGradient;

    @Schema(description = "调度指令持续时间(秒) [0,86400] 0表示永久生效")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer schedulingCommandMaintenanceTime;

    @Schema(description = "电网标准码")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer gridStandardCode;

    @Schema(description = "cosϕ-P/Pn特征曲线")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<PpnCurveItem> ppnCurve;

    @Schema(description = "Q-U特征曲线")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<QuCurveItem> quCurve;

    @Schema(description = "PF-U特征曲线")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<PfuCurveItem> pfuCurve;

}

