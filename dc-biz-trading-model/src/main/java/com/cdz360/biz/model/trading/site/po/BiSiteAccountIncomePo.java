package com.cdz360.biz.model.trading.site.po;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.charge.type.SettlementType;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "场站按账户运营收入统计")
public class BiSiteAccountIncomePo {

    @NotNull(message = "id 不能为 null")
    private Long id;

    @Schema(description = "场站ID")
    @NotNull(message = "siteId 不能为 null")
    @Size(max = 32, message = "siteId 长度不能超过 32")
    private String siteId;

    @Schema(description = "月份")
    @NotNull(message = "month 不能为 null")
    private Date month;

    @Schema(description = "结算账户类型. 0,未知; 1,平台余额账户; 2,企业授信账户; 3,商户会员账户; 4,即充即退")
    @NotNull(message = "accountType 不能为 null")
    private PayAccountType accountType;

    @Schema(description = "结算账户类型. 0,未知; 1,预付费结算; 2,担保消费结算; 3,后付费线下结算; 4,外部平台结算; 5,后付费线上结算")
    @NotNull(message = "settlementType 不能为 null")
    private SettlementType settlementType;

    private Long accountId;

    @Schema(description = "账户/企业	名称")
    @NotNull(message = "accountName 不能为 null")
    @Size(max = 64, message = "accountName 长度不能超过 64")
    private String accountName;

    @Schema(description = "结算单号 仅用于后付费")
    private String billNo;

    @Schema(description = "充电量,单位\"kwh\"")
    private BigDecimal elec;

    @Schema(description = "订单原始电费金额")
    private BigDecimal elecOriginFee;

    @Schema(description = "根据订单统计的电费实付金额")
    private BigDecimal elecCostFee;

    @Schema(description = "根据订单统计的服务费实付金额")
    private BigDecimal servCostFee;

    @Schema(description = "根据订单统计的电费收益")
    private BigDecimal orderElecProfit;

    @Schema(description = "根据订单统计的服务费收益")
    private BigDecimal orderServProfit;

    @Schema(description = "总收入")
    private BigDecimal totalIncome;

    @Schema(description = "实际服务费收入")
    private BigDecimal servProfit;

    @Schema(description = "true有效, false禁用")
    private Boolean enable;

    private Date createTime;

    private Date updateTime;


}
