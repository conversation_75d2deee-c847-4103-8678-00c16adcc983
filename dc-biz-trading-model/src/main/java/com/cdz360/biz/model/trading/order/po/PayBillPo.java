package com.cdz360.biz.model.trading.order.po;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.model.base.type.UserType;
import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.biz.model.finance.type.ExpressStatus;
import com.cdz360.biz.model.finance.type.FlowInAccountType;
import com.cdz360.biz.model.finance.type.InvoiceMode;
import com.cdz360.biz.model.finance.type.TaxStatus;
import com.cdz360.biz.model.finance.type.TaxType;
import com.cdz360.biz.model.trading.bill.type.DailyBillCheckResult;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.cdz360.biz.model.trading.deposit.vo.DepositSourceType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

/**
 * 充值记录
 *
 * <AUTHOR>
 * @since 2019/11/6 9:01
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString
public class PayBillPo extends BaseObject {

    @Schema(description = "充值记录Id", hidden = true)
    private Long id;

    @Nullable
    @Schema(description = "企业Id", hidden = true)
    private Long corpId;

    @Schema(description = "用户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long userId;

    @Schema(description = "客户名称")
    private String username;//用于记录日志

    @Schema(description = "客户手机号")
    private String phone;//用于记录日志


    @Schema(description = "客户邮箱")
    private String email;

    @Schema(description = "所属商户的顶级商户id", hidden = true)
    private Long topCommId;

    @Schema(description = "账户所属商户id" +
            "个人或: 顶级商户Id; 商户会员: 会员所属商户Id; 企业客户: 企业客户所属顶级商户Id", hidden = true)
    private Long commId;

    /**
     * 所属商户名称
     */
    @Schema(description = "所属商户名称")
    private String commercialName;

    @Schema(description = "充值订单号", hidden = true)
    private String orderId;

    @Schema(description = "支付平台对账结果: FULL_MATCH(完全匹配); AMOUNT_NOT_MATCH(金额不匹配); NO_NOT_MATCH(未找到对应订单); OTHERS(其他)")
    private DailyBillCheckResult checkResult;

    @Schema(description = "直付通商户名称", hidden = true)
    private String zftName;

    @Schema(description = "微信支付（子）商户ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String wxSubMchId;

    @Schema(description = "支付宝子商户ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String alipaySubMchId;

    /**
     * 支付使用的第三方支付渠道:0：alipay 支付宝 App 支付;
     * 1：alipay_applet 支付宝小程序支付 (单笔当面付);2：wx 微信 App 支付;
     * 3：wx_pub 微信公众号支付;4：wx_lite 微信小程序支付
     */
    @Schema(description = "支付使用的第三方支付渠道", example = "alipay", hidden = true)
//    @Deprecated
    private String channel;

    /**
     * 充值实际金额，单位'元'
     */
    @Schema(description = "充值实际金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal amount;

    @Schema(description = "充值赠送金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal freeAmount;

    @Schema(description = "减少实际金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal outFlowAmount;

    @Schema(description = "减少赠送金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal outFlowFreeAmount;

    @Schema(description = "充值类型. 增加/减少")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private DepositFlowType flowType;

    @Schema(description = "充值来源")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private DepositSourceType sourceType;

    @Schema(description = "支付方式")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private PayChannel payChannel;

    @Schema(description = "充值账户类型. 个人账户/商户会员/企业账户")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private PayAccountType accountType;

    @Schema(description = "账户编号. 个人账户/企业账户为集团商户编号; 商户会员为商户编号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long accountCode;

    @Schema(description = "收款账户类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private FlowInAccountType flowInAccountType;

    @Schema(description = "flowType为OUT_FLOW时,对应的充值单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String refBillNo;

    @Schema(description = "发票类型(税种): UNKNOWN(0)-未知," +
            "NORMAL_TAX(2)-个人普通发票,PREPAY_TAX(3)-企业普通发票," +
            "SPECIAL_VAT(5)-企业专业发票", example = "UNKNOWN")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TaxType taxType;

    @Schema(description = "开票状态 这个字段仅仅记录创建的时候开票状态(保留原来设置逻辑)", example = "NO")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TaxStatus taxStatus;

    @Schema(description = "发票类型: UNKNOWN(0)-未知,ONLINE(1)-电子票,PAPER(2)-纸质票", example = "PAPER")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InvoiceMode invoiceMode;

    @Schema(description = "税票号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String taxNo;

    @Schema(description = "发票寄送状态: UNKNOWN(0)-未知,INIT(1)-未寄送,SENT(2)-已寄送", example = "SENT")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ExpressStatus expressStatus;

    @Schema(description = "物流公司名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String expressCompany;

    @Schema(description = "物流单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String expressNo;

    @Schema(description = "已开票金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal invoicedAmount;

    @Schema(description = "充值前余额, 单位'元', 2位小数. 含赠送余额", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal amountBefore;

    @Schema(description = "充值后余额, 单位'元', 2位小数. 含赠送余额", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal amountAfter;

    /**
     * 商品标题
     */
    @Schema(description = "商品标题", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String subject;
    /**
     * 发起支付请求客户端的 IPv4 地址
     */
    @Schema(description = "起支付请求客户端的 IPv4 地址")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String clientIp;
    /**
     * 商品描述信息
     */
    @Schema(description = "商品描述信息", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String body;
    /**
     * 商户下的用户唯一标示
     */
    @Schema(description = "商户下的用户唯一标示", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String openId;
    /**
     * 支付类型：1订单支付; 2余额充值; 3保证金支付; 4保证金提现; 5商户会员充值; 6即充即退订单充值; 7订单补缴; 8占位费补缴
     */
    @Schema(description = "支付类型", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer payType;
    /**
     * 参见 com.cdz360.biz.model.trading.order.type.PayBillStatus
     */
    @Schema(description = "支付状态: 0未支付,1已支付,2支付失败,3发起支付成功(C端响应),4发起支付失败(C端响应),5超时,6余额已耗尽", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer status;

    /**
     * 第三方交易号
     */
    @Schema(description = "第三方交易号", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String tradeNo;
    /**
     * 支付时间
     */
    @Schema(description = "支付时间", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date payTime;
    /**
     * 支付中心回调返回交易类型：1 为支付(pay)，2 为退款(refund)
     */
    @Schema(description = "支付中心回调返回交易类型：1 为支付(pay)，2 为退款(refund)", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer notifyType;
    /**
     * 微信支付交易类型 ，JSAPI、NATIVE、APP
     */
    @Schema(description = "微信支付交易类型 ，JSAPI、NATIVE、APP", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String tradeType;
    /**
     * 微信支付付款银行
     */
    @Schema(description = "微信支付付款银行", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String bankType;
    /**
     * 退款：商户退款单号
     */
    @Schema(description = "退款：商户退款单号", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String outRefundNo;
    /**
     * 微信退款：退款入账账户
     */
    @Schema(description = "微信退款：退款入账账户", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String refundRecvAccout;
    /**
     * 微信退款：退款发起来源
     */
    @Schema(description = "微信退款：退款发起来源", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String refundRequestSource;

    @Schema(description = "付款账户名称: 账户类型 + 账户名称(页面查看字段) 查询组合而成")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String payAccountName;

    /**
     * 备注
     */
    @Schema(description = "备注")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Size(max = 200, message = "备注太长")
    private String remark;
    /**
     * 退款原因
     */
    @Schema(description = "退款原因")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cusNote;
    /**
     * 退款：退款时，原订单单号
     */
    @Schema(description = "退款：退款时，原订单单号", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String refundOldOrderId;
    /**
     * 充电订单号（即充即退）
     */
    @Schema(description = "充电订单号（即充即退）", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String chargeOrderNo;

    @Schema(description = "支付账户名称")
    @Size(max = 256, message = "支付账户名称太长")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String outAccountName;

    @Schema(description = "支付账户银行名称")
    @Size(max = 256, message = "支付账户银行名称太长")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String outBankName;

    @Schema(description = "支付账户银行卡号")
    @Size(max = 256, message = "支付账户银行卡号太长")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String outAccountNo;

    @Schema(description = "收款账户名称")
    @Size(max = 256, message = "收款账户名称太长")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String inAccountName;

    @Schema(description = "收款账户银行名称")
    @Size(max = 256, message = "收款账户银行名称太长")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String inBankName;

    @Schema(description = "收款账户银行卡号")
    @Size(max = 256, message = "收款账户银行卡号太长")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String inAccountNo;

    @Schema(description = "账户流水序号数组,使用逗号分隔", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String flowSeqNo;

    @Schema(description = "操作人类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private UserType opUserType;  // UserType

    @Schema(description = "操作人ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long opUid;

    @Schema(description = "操作人姓名")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String opName;

    @Schema(description = "所属客户名称")
    private String cusName;

    @Schema(description = "所属客户手机号")
    private String cusPhone;

    @Schema(description = "客户类型:UNKNOWN(0)-未知,SYS_USER(1)-商户/平台用户,CUSTOMER(2)-C端客户,CORP_USER(3)-企业用户")
    private UserType userType;

    @Schema(description = "对账单ID(t_zft_daily_bill.id)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long dailyBillId;

    @Schema(description = "关联活动ID")
    private Long activityId;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date createTime;
    /**
     * 更新时间
     */
    @Schema(description = "更新时间", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date updateTime;
}
