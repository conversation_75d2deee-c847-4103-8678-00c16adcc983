package com.cdz360.biz.model.trading.site.vo;

import com.cdz360.biz.model.trading.site.po.SiteChargeJobPlugPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SiteChargeJobPlugVo extends SiteChargeJobPlugPo {

    @Schema(description = "任务名称")
    private String jobName;
    @Schema(description = "0,有效; 1,停用; 2,删除; 3,过期")
    private Integer jobStatus;
}
