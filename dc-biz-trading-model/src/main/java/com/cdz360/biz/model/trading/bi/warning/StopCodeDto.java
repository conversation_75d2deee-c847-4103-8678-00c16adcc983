package com.cdz360.biz.model.trading.bi.warning;

import com.cdz360.biz.model.trading.bi.dto.BiExportGroups;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * groups 字段归属组 {@link BiExportGroups}
 */
@Data
@Accessors(chain = true)
@Schema(description = "停充原因列表")
public class StopCodeDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private  int stopCode;
    private String stopReason;
}
