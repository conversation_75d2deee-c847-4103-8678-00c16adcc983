package com.cdz360.biz.model.trading.bi.warning;

import com.cdz360.biz.model.trading.bi.dto.BiExportGroups;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * groups 字段归属组 {@link BiExportGroups}
 */
@Data
@Accessors(chain = true)
@Schema(description = "告警查询出参")
public class WarningSummaryDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 告警名称
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String warningName;

    /**
     * 站点名称
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;

    /**
     * 设备型号
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String modelName;

    /**
     * 软件版本
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String firmwareVer;

    /**
     * 告警数
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long warningAccount;

    /**
     * 站点分布汇总
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> siteIdList;

    /**
     * 设备型号分布
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> modelNameList;

    /**
     * 软件版本分布
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> firmwareList;

    /**
     * 告警分布
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> warningCodeList;

    /**
     * 告警名称
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> warningNameList;

    /**
     * 场站列表
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> siteNameList;


}
