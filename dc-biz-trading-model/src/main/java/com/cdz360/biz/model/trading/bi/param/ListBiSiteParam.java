package com.cdz360.biz.model.trading.bi.param;

import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.order.param.ListBiParam;
import com.cdz360.biz.model.site.type.SiteStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "站点订单汇总查询参数")
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class ListBiSiteParam extends ListBiParam {

    @Schema(description = "商户Id链", required = true, hidden = true)
    private String commIdChain;

    @Schema(description = "场站Id列表")
    private List<String> siteIdList;

    @Schema(description = "省编号")
    private Integer province;

    @Schema(description = "市编号")
    private Integer city;

//    @Schema(description = "查询时间类型: CREATE_TIME--创建时间; " +
//            "STOP_TIME--上传时间; " +
//            "CHARGE_START_TIME--充电开始时间; " +
//            "CHARGE_END_TIME--充电结束时间; " +
//            "PAY_TIME--支付时间")
//    private BiDependOnType type;
//
//    @Schema(description = "查询开始时间，结合时间类型使用(type)")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
//    private Date fromDate;
//
//    @Schema(description = "查询结束时间，结合时间类型使用(type)")
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
//    private Date toDate;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "场站状态, 不传表示查询所有的. 0, 不可用; 1, 待上线; 2, 可用; 3, 维护中")
    private List<SiteStatus> statusList;

    @Schema(description = "仅用作excel导出使用", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ExcelPosition excelPosition;
}
