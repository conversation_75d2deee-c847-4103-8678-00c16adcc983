package com.cdz360.biz.model.trading.hlht.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "商户绑定互联互通场站")
public class SiteHlhtPo {

    private Long id;

    private Long topCommId;

    private Long commId;

    private String siteId;

    @Schema(description = "合作方编号(互联运营商ID)")
    private String partnerCode;

    @Schema(description = "合作方名称(互联运营商名称)")
    private String partnerName;

    @Schema(description = "设备所属运营商")
    private String evseOwnerCode;

    @Schema(description = "设备所属运营商名称")
    private String evseOwnerName;

    private Boolean enable;

    private Date createTime;

    private Date updateTime;
}
