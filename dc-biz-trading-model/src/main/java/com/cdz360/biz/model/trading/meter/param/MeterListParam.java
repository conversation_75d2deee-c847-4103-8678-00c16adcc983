package com.cdz360.biz.model.trading.meter.param;

import com.cdz360.biz.model.common.param.BaseListParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * MeterListParam
 *
 * @since 9/18/2020 5:14 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MeterListParam extends BaseListParam {
    private String siteId;
    private List<String> siteIdList;
    private String gwno;// 关联的控制器
    private Boolean powerLoss;// 是否计算电损，1启用 0禁用
    private String idChain;
    private List<Long> meterIdList; // 指定显示电表列表
    private String siteNameLike; // 场站名称模糊查询
    private String meterNoLike; // 电表编号模糊查询
}