package com.cdz360.biz.model.trading.site.param;

import com.cdz360.biz.model.trading.site.type.SiteInspectionStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class ChangeRecordParam {

    private List<Long> recordIdList;

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private Long sysUserId;

    private SiteInspectionStatus status;

    private String qcRemark;

    private Long score;
}
