package com.cdz360.biz.model.trading.cus.param;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.vo.BaseObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2020/2/14 12:56
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Schema(description = "用户一键退款请求参数")
public class CusRefundAllParam extends BaseObject {

    @Schema(description = "用户Id")
    private Long uid;

    @Schema(description = "顶级商户Id")
    private Long topCommId;

    @Schema(description = "商户Id 商户会员填写")
    private Long commId;

    @Schema(description = "客户端类型")
    private AppClientType clientType;

    @Schema(description = "退款原因")
    private String cusNote;

    // 退款记录操作者信息
    private Long opUid;
    private String opName;
}
