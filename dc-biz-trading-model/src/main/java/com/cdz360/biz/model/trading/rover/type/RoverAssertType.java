package com.cdz360.biz.model.trading.rover.type;

import lombok.Getter;

/**
 * RoverAssertType
 *  资产类型依次: 充电桩,配电房,雨棚/车挡,监控,照明,消防器材,场站地面,场站形象,卫生间,休息室
 * @since 7/27/2022 1:25 PM
 * <AUTHOR>
 */
@Getter
public enum RoverAssertType {
    EVSE("充电桩", "充电桩设备齐全、 完好、 运行正常"),
    TRANS("配电房", "配电房年检正常， 围挡正常、 周边无异常"),
    RAIN("雨棚/车挡", "雨棚及结构无异常、 漏雨、 严重生锈"),
    CAMERA("监控", "监控设备齐全、 完好、 运行正常"),
    LIGHT("照明", "照明设备齐全、 完好、 运行正常"),
    FIRE("消防器材", "消防器材齐全、 完好、 运行正常"),
    FLOOR("场站地面", "场站地面整洁、 无杂物"),
    PERCEPTION("场站形象", "充电区域内无广告贴、标识指引完好"),
    WASHROOM("卫生间", "卫生间干净、 完好、 无杂物异物"),
    RESTROOM("休息室", "休息室干净整洁、 物品完好、 已异味");
    private String desc;
    private String requirement;
    RoverAssertType(String s1, String s2) {
        this.desc = s1;
        this.requirement = s2;
    }
}