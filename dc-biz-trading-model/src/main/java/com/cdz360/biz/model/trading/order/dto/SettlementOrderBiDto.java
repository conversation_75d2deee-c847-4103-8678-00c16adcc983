package com.cdz360.biz.model.trading.order.dto;

import com.cdz360.base.model.base.vo.BaseObject;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "企业为生成账单订单统计")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SettlementOrderBiDto extends BaseObject {

    @Schema(description = "关联的订单数")
    private Integer orderNum;

    @Schema(description = "充电订单电量")
    private BigDecimal orderKwh;

    @Schema(description = "其他时段电量")
    private BigDecimal kwhOther;

    @Schema(description = "尖时段电量")
    private BigDecimal kwhJian;

    @Schema(description = "峰时段电量")
    private BigDecimal kwhFeng;

    @Schema(description = "平时段电量")
    private BigDecimal kwhPing;

    @Schema(description = "谷时段电量")
    private BigDecimal kwhGu;

    @Schema(description = "(原始)电费")
    private BigDecimal orderElecFee;

    @Schema(description = "(原始)服务费")
    private BigDecimal orderServFee;

    @Schema(description = "(原始)订单电费收益,单位: 元")
    private BigDecimal orderElecProfit;

    @Schema(description = "(原始)订单服务费收益,单位: 元")
    private BigDecimal orderServProfit;

    @Schema(description = "(原始)订单总收益,单位: 元")
    private BigDecimal orderTotalProfit;

    @Schema(description = "关联的场站名称 用来保存相关的场站信息")
    private String siteNameList;

    @Schema(description = "关联的场站编号 用来保存场站相关的编号信息")
    private String siteNoList;

}
