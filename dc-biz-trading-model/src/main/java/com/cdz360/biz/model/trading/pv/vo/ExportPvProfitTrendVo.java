package com.cdz360.biz.model.trading.pv.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * ExportPvProfitTrendVo
 * 
 * @since 10/29/2021 4:19 PM
 * <AUTHOR>
 */
@Schema(description = "光储收益趋势")
@Data
@Accessors(chain = true)
public abstract class ExportPvProfitTrendVo implements Serializable {
//    @ExcelField(title = "日期", sort = 1, dateFormat = "yyyy-MM-dd")
//    @Schema(description = "日期", description = "按月份查询时需要前端去掉具体日")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    @JsonDeserialize(using = LocalDateDeserializer.class)
//    @JsonSerialize(using = LocalDateSerializer.class)
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")

//    @ExcelField(title = "日期", sort = 1, dateFormat = "yyyy-MM-dd")
//    @Schema(description = "日期")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
//    private Date time;

//    @Schema(description = "发电电量（度）")
//    @ExcelField(title = "发电电量（度）", sort = 2)
//    private BigDecimal totalKwh;
//
//    @Schema(description = "发电收益（元）")
//    @ExcelField(title = "发电收益（元）", sort = 3)
//    private BigDecimal totalProfit;

    public abstract void setTime(Date time);
}