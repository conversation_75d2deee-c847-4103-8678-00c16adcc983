package com.cdz360.biz.model.trading.coupon.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * 充赠活动，满减金额
 */
@Data
@Accessors(chain = true)
public class ChargeVo {

    @Schema(description = "满（元）")
    private BigDecimal chargeAmount;

    @Schema(description = "减（元）")
    private BigDecimal discountAmount;
}