package com.cdz360.biz.model.trading.pv.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "cosϕ-P/Pn特征曲线")
public class PpnCurveItem {

    private Integer idx;

    @Schema(description = "P/Pn值(%) [0,100]")
    private BigDecimal ppn;

    @Schema(description = "cosϕ值 (-1,-0.8]∪[0.8,1]支持三位小数")
    private BigDecimal cos;
}
