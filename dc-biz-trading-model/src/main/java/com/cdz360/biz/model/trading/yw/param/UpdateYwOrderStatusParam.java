package com.cdz360.biz.model.trading.yw.param;

import com.cdz360.biz.model.trading.yw.type.YwOrderOpType;
import com.cdz360.biz.model.trading.yw.type.YwOrderStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "更新运维工单状态参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class UpdateYwOrderStatusParam extends BaseOpParam {
    @Schema(description = "运维工单编号列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> ywOrderNoList;

    @Schema(description = "工单状态(INIT: 待接收; RECEIVED: " +
            "已接收; PROCESSING: 处理中; TRANSFERRING: 转派中;" +
            " WAIT_CHECK : 待质检; NO_PASS: 不合格; SOLVED: 已完成;)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private YwOrderStatus orderStatus;

    @Schema(description = "质检人备注")
    private String qcRemark;

    @Schema(description = "质检分数")
    private Long score;

    public Integer getType() {
        switch (orderStatus) {
            case RECEIVED:
                return YwOrderOpType.YW_ORDER_RECEIVED;
            case PROCESSING:
                return YwOrderOpType.YW_ORDER_START_PROCESS;
            case NO_PASS:
            case SOLVED:
                return YwOrderOpType.YW_ORDER_CHECK;
            case DELETED:
                return YwOrderOpType.YW_ORDER_DELETED;
            case SUSPEND:
                return YwOrderOpType.YW_ORDER_SUSPEND;
        }

        return YwOrderOpType.YW_ORDER_UNKNOWN;
    }
}
