package com.cdz360.biz.model.trading.meter.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 电表相关数据VO
 */
@Data
@Accessors(chain = true)
public class MeterDataVo {

    @Schema(description = "场站ID")
    private String siteId;

    @Schema(description = "场站名称")
    private String siteName;

    @Schema(description = "场站编号")
    private String siteNo;

    @Schema(description = "省份编号")
    private Integer province;

    @Schema(description = "省份名称")
    private String provinceName;

    @Schema(description = "城市编号")
    private Integer city;

    @Schema(description = "城市名称")
    private String cityName;

    @Schema(description = "区域编号")
    private Integer district;

    @Schema(description = "区域名称")
    private String districtName;

    @Schema(description = "场站总功率，所有桩功率总和")
    private BigDecimal totalPower;

    @Schema(description = "月份")
    private String monthDate;

    @Schema(description = "表号")
    private String dno;

    @Schema(description = "订单电量kWh")
    private BigDecimal orderElec;

    @Schema(description = "抄表电量kWh")
    private BigDecimal meterElec;

    @Schema(description = "尖电量kWh")
    private BigDecimal sharpPeakElec;

    @Schema(description = "峰电量kWh")
    private BigDecimal peakElec;

    @Schema(description = "平电量kWh")
    private BigDecimal offPeakElec;

    @Schema(description = "谷电量kWh")
    private BigDecimal valleyElec;

    @Schema(description = "深谷电量kWh")
    private BigDecimal deepValleyElec;

    @Schema(description = "开始总读数kWh")
    private BigDecimal startTotalReadingElec;

    @Schema(description = "结束总读数kWh")
    private BigDecimal endTotalReadingElec;

    @Schema(description = "电损%")
    private BigDecimal discount;
}