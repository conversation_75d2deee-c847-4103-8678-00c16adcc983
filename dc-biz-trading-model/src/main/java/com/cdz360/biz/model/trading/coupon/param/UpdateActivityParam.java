package com.cdz360.biz.model.trading.coupon.param;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * UpdateActivityParam
 *
 * @since 7/31/2020 10:07 AM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "编辑活动")
public class UpdateActivityParam extends CreateActivityParam {
    private Long id;
}