package com.cdz360.biz.model.trading.prerun.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.biz.model.trading.prerun.type.PrerunStatusType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * PrerunSearchParam
 *  开通调试工单搜索入参数
 * @since 6/22/2022 4:09 PM
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PrerunSearchParam extends BaseListParam {
    private String prerunNoKeyword;
    private String prerunnerKeyword;

    @Schema(description = "开通调试时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter prerunTimeRange;

    // 工单状态
    private List<PrerunStatusType> statusTypes;

    // 工单类型
    private List<Integer> categories;

    private String siteNameKeyword;

    private Long siteCommId;

    // 下载导出文件名
    private String exFileName;

    // 导出的工单id
    private List<Long> prerunIds;

    private String siteId;

    private Long prerunnerUid;

    private String commIdChain;

    @Schema(description = "场站组id列表 仅用于管理后台相关接口")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> gids;

}