package com.cdz360.biz.model.trading.site.po;

import com.cdz360.base.model.base.vo.BaseObject;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CommPo extends BaseObject {

    /**
     * 商户ID
     */
    @Schema(description = "商户ID")
    private Long id;

    @Schema(description = "商户名称")
    private String name;

    private Long topCommId;
    private String idChain;
}
