package com.cdz360.biz.model.trading.iot.param;

import com.cdz360.biz.model.iot.param.ListGtiParam;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * PvProfitTrendParam
 *
 * @since 10/28/2021 3:47 PM
 * <AUTHOR>
 */
@Schema(description = "查询逆变器列表参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class PvProfitTrendParam extends ListGtiParam {

    @Schema(description = "开始时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private LocalDateTime endTime;

    @Schema(description = "采样时间，按小时、天、月（不支持小时）", required = true)
    private SiteBiSampleType sampleType;

    public void resetTime() {

        // 分秒都为0
        LocalDateTime fromTime = this.startTime;
        LocalDateTime toTime = this.endTime;

        if (sampleType == SiteBiSampleType.DAY ||
            sampleType == SiteBiSampleType.MONTH) {
            fromTime = fromTime.withHour(0);
            toTime = toTime.withHour(0);
        }

        if (sampleType == SiteBiSampleType.MONTH) {
            // 当月第一天
            fromTime = fromTime.with(TemporalAdjusters.firstDayOfMonth());
            toTime = toTime.with(TemporalAdjusters.firstDayOfMonth());
        }

        this.startTime = fromTime;
        this.endTime = toTime;
    }

}