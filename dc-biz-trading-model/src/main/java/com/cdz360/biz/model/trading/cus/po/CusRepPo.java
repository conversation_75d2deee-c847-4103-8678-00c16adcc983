package com.cdz360.biz.model.trading.cus.po;

import com.cdz360.biz.model.common.po.BasePo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 客户信息同步表的PO
 * cus replicate po
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CusRepPo extends BasePo {


    @Schema(description = "商户Id", example = "123")
    private Long commId;

    @Schema(description = "用户名(昵称)")
    private String username;

    @Schema(description = "用户姓名")
    private String name;

    @Schema(description = "性别. 1.男,2.女", example = "1")
    private Integer sex;

    @Schema(description = "用户状态（10000-删除，10001-正常,10002-加入黑名单）???", example = "123")
    private Integer status;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "城市编码")
    private String cityCode;

    @Schema(description = "默认扣款账户ID. 现金账户/商户会员时为商户ID; 授信账户时为授信账户ID", example = "123")
    private Long balanceId;

    @Schema(description = "1, 基本账户(t_balance); 2, 集团授权账户(t_r_bloc_user); 3, 权益(商户)账户;", example = "1")
    private Integer defaultPayType;

    @Schema(description = "最近订单no", example = "1123123123")
    private String latestOrderNo;


}
