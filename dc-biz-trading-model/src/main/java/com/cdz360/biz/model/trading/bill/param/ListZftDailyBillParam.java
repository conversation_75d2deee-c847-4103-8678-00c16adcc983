package com.cdz360.biz.model.trading.bill.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.type.PayChannel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "支付平台账单查询参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListZftDailyBillParam extends BaseListParam {

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "商户ID链", example = "34474,34475")
    private String commIdChain;

    @Schema(description = "直付商家ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long zftId;

    @Schema(description = "账期开始时间", example = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date fromDate;

    @Schema(description = "账期结束时间", example = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date toDate;

    @Schema(description = "支付渠道 1(支付宝);2(微信)", example = "1",
            format = "java.lang.Integer")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private PayChannel channel;
}
