package com.cdz360.biz.model.trading.corp.po;

import com.cdz360.base.model.corp.dto.CorpSyncDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=false)
public class CorpPo extends CorpSyncDto {
    @Schema(description = "营业执照")
    private String  businessImage;
    private Long sysUid;
    private  String blocUserName;

//    @JsonIgnore
//    @Schema(description = "企业结算方式: 1,账户余额扣减; 3,后付费", format = "java.lang.Integer")
//    private SettlementType settlementType;
}
