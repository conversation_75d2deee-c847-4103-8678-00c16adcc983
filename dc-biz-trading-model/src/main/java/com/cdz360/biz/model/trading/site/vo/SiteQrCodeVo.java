package com.cdz360.biz.model.trading.site.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class SiteQrCodeVo {

    @Schema(description = "运营商企业代码")
    @JsonInclude(Include.NON_EMPTY)
    private String operateCorpCode;

    @Schema(description = "枪头对三方平台的枪头号位数(12或14)")
    @JsonInclude(Include.NON_NULL)
    private Integer outPlugSize;

}
