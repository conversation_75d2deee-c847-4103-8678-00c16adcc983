package com.cdz360.biz.model.trading.site.dto;

import com.cdz360.biz.model.trading.site.po.SiteOaDefaultConfigItem;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "站点oa流程默认值")
@Data
@Accessors(chain = true)
public class SiteOaDefaultConfigDto {

//    @Schema(description = "场站ID")
//    @JsonInclude(Include.NON_EMPTY)
//    private String siteId;

    @Schema(description = "oa流程KEY")
    @JsonInclude(Include.NON_EMPTY)
    private String procDefKey;

    @Schema(description = "oa流程名称")
    @JsonInclude(Include.NON_EMPTY)
    private String procDefName;

    @Schema(description = "默认值配置")
    @JsonInclude(Include.NON_EMPTY)
    private List<SiteOaDefaultConfigItem> defaultValue;

}
