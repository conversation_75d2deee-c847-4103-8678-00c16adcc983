package com.cdz360.biz.model.trading.order.vo;

import com.cdz360.biz.model.trading.bill.type.DailyBillCheckResult;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 直付通记录查看
 *
 * <AUTHOR>
 * @since 2019/11/5 17:07
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ToString
@Schema(description = "直付通记录查看")
public class ZftBillVo implements Serializable {

    @Schema(description = "支付平台对账结果 FULL_MATCH(完全匹配); AMOUNT_NOT_MATCH(金额不匹配); NO_NOT_MATCH(未找到对应订单); OTHERS(其他)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private DailyBillCheckResult checkResult;

    /**
     * 操作时间
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "交易时间", example = "2019-11-11 15:21:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date payTime;

    /**
     * 直付商家
     */
    @Schema(description = "直付商家")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String zftName;

    /**
     * 所属商户名称
     */
    @Schema(description = "操作记录所属商户名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commName;

    /**
     * 用户手机号
     */
    @Schema(description = "用户手机号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String phone;

    /**
     * 账户名称
     */
    @Schema(description = "账户名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String accountType;

    /**
     * 平台订单号
     */
    @Schema(description = "平台订单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderId;

    /**
     * 充值订单号
     */
    @Schema(description = "充电订单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String chargeOrderNo;

    /**
     * 交易渠道
     */
    @Schema(description = "交易渠道")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String payWay;

    /**
     * 微信、支付宝订单号
     */
    @Schema(description = "微信、支付宝订单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String tradeNo;

    /**
     * 财务类型
     */
    @Schema(description = "财务类型")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String financialType;

//    /**
//     * 入账金额
//     */
//    @Schema(description = "入账金额")
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private BigDecimal chargeAmount;
//
//    /**
//     * 退款金额
//     */
//    @Schema(description = "退款金额")
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private BigDecimal refundAmount;

//    /**
//     * 实收金额
//     */
//    @Schema(description = "实收金额")
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private BigDecimal realAmount;

    @Schema(description = "交易金额（单位: 元） 结合收支类型显示正负")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal tradeAmount;

//    /**
//     * 充值类型
//     */
//    @Schema(description = "充值类型,1-支付宝  2-财付通")
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private Long flowInAccountType;

//    /**
//     * 订单总数
//     */
//    @Schema(description = "订单总数")
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private Long orderNum;

    @Schema(description = "收支类型: IN_FLOW(收入), OUT_FLOW(支出)",
            format = "java.lang.Integer")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private DepositFlowType tradeType;

    @Schema(description = "关联支付平台账单名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dailyBillName;
}
