package com.cdz360.biz.model.trading.bi.vo;

import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.biz.model.annotations.ExcelField;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "活动券领取用户统计数据")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class UserActivityCouponBiVo extends BaseObject implements Serializable {


    @ExcelField(title = "活动名称", sort = 1)
    @Schema(description = "活动名称")
    private String activityName;

    @ExcelField(title = "券号", sort = 2)
    @Schema(description = "券号")
    private Long id;

    @ExcelField(title = "券模版名", sort = 3)
    @Schema(description = "券模版名")
    private String dictName;

    @ExcelField(title = "发放人", sort = 4)
    @Schema(description = "操作人")
    private String opName;

    @ExcelField(title = "券状态", sort = 5)
    @NotNull(message = "status 不能为 null")
    private String status;

    @ExcelField(title = "券有效期", sort = 6)
    private String validTime;

    @ExcelField(title = "领取手机号", sort = 7)
    private String phone;

    @ExcelField(title = "领取时间", sort = 8)
    private String createDate;

    @ExcelField(title = "关联订单", sort = 9)
    @Size(max = 64, message = "orderNo 长度不能超过 64")
    private String orderNo;

    @ExcelField(title = "场站名称", sort = 10)
    private String siteName;

    @ExcelField(title = "总电量", sort = 11)
    @Schema(description = "总电量")
    private BigDecimal orderElec;

    @ExcelField(title = "标准电费", sort = 12)
    @Schema(description = "标准电费")
    private BigDecimal elecOriginFee;

    @ExcelField(title = "总电费", sort = 13)
    @Schema(description = "总电费")
    private BigDecimal elecFee;

    @ExcelField(title = "标准服务费", sort = 14)
    @Schema(description = "标准服务费")
    private BigDecimal servOriginFee;

    @ExcelField(title = "总服务费", sort = 15)
    @Schema(description = "总服务费")
    private BigDecimal servFee;

    @ExcelField(title = "券抵扣金额", sort = 16)
    @Schema(description = "券抵扣金额")
    private BigDecimal amount;

    @ExcelField(title = "充电总金额", sort = 17)
    @Schema(description = "充电总金额")
    private BigDecimal orderFee;

    @ExcelField(title = "订单创建时间", sort = 18)
    @Schema(description = "订单创建时间")
    private String orderCreateTime;

}
