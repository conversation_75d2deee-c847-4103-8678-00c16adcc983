package com.cdz360.biz.model.trading.coupon.param;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "用户领取优惠券查询参数")
public class ActivityUserCouponParam {

    @Schema(description = "活动ID")
    private Long activityId;

    @Schema(description = "手机号列表")
    private List<String> phoneList;

}