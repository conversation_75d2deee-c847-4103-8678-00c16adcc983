package com.cdz360.biz.model.trading.park.po;

import com.cdz360.biz.model.trading.park.type.ParkOrderStatusType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "停车订单")
public class ParkOrderPo {

	@NotNull(message = "id 不能为 null")
	private Integer id;

	@Schema(description = "订单当前状态，已入场 | 已出场")
	@NotNull(message = "status 不能为 null")
	private ParkOrderStatusType status;

	@Schema(description = "车牌号")
	@Size(max = 10, message = "carNo 长度不能超过 10")
	private String carNo;

	@Schema(description = "车场id")
	@NotNull(message = "parkId 不能为 null")
	private Long parkId;

	@Schema(description = "进场时间")
	private Date inTime;

	@Schema(description = "出场时间")
	private Date outTime;

	@Schema(description = "停车订单")
	@Size(max = 32, message = "parkOrderId 长度不能超过 32")
	private String parkOrderId;

	@Schema(description = "充电订单")
	@Size(max = 64, message = "chargeOrderNo 长度不能超过 64")
	private String chargeOrderNo;

	@Schema(description = "停车时长")
	private Integer duration;

	@Schema(description = "停车免费时长，分钟")
	private Integer couponDuration;

	@Schema(description = "车型")
	@Size(max = 64, message = "carType 长度不能超过 64")
	private String carType;

	@Schema(description = "支付类型")
	@Size(max = 64, message = "payType 长度不能超过 64")
	private String payType;

	@Schema(description = "实时订单金额")
	private BigDecimal totalAmount;

	@Schema(description = "减免金额")
	private BigDecimal reduceAmount;

	@Schema(description = "出场图片")
	private String pidAddr;

	private Date createTime;

	private Date updateTime;


}
