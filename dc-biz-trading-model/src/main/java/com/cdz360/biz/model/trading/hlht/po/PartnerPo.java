package com.cdz360.biz.model.trading.hlht.po;

import com.cdz360.base.model.charge.type.HlhtType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * Partner
 *  TODO
 * @since 2019/9/11
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class PartnerPo {

    private Long id;

    /**
     * 合作方渠道类型
     */
    private String channelType;
    /**
     * 合作方编号
     */
    private String code;
    /**
     * 合作方名称
     */
    private String name;
    /**
     * 集团商户id
     */
    private Long topCommId;
    /**
     * 商户id
     */
    private Long commId;

    /**
     * 互联互通类型
     */
    private HlhtType hlhtType;
    /**
     * 绑定账号id
     */
    private Long uid;
    /**
     * 停充超时计费是否回传
     */
    private Boolean billingBack;
    /**
     * 是否有效
     */
    private Boolean enable;
    /**
     * 记录创建时间
     */
    private Date createTime;
    /**
     * 记录最后修改时间
     */
    private Date updateTime;
}