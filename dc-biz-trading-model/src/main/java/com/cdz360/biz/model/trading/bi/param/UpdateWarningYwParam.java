package com.cdz360.biz.model.trading.bi.param;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@Schema(description = "告警信息绑定运维工单")
public class UpdateWarningYwParam  {

    @Schema(description = "桩编号列表")
    private List<String> evseNoList;

    @Schema(description = "运维工单号")
    private String ywOrderNo;


    @Schema(description = "忽略的告警码")
    private List<String>  ignoreWarningCodeList;



}
