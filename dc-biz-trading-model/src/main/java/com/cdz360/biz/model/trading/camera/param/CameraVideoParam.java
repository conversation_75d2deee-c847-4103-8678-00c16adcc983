package com.cdz360.biz.model.trading.camera.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * CameraVideoParam
 *
 * @since 7/29/2021 4:42 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "录像回放请求")
public class CameraVideoParam {
    @Schema(description = "相机id")
    private Long cameraId;//相机id
    @Schema(description = "本地录像回放开始时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;//本地录像回放开始时间
    @Schema(description = "本地录像回放结束时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stopTime;//本地录像回放结束时间
}