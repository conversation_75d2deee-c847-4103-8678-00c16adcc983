package com.cdz360.biz.model.trading.camera.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * ListCameraParam
 *
 * @since 7/28/2021 5:29 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "场站摄像头")
public class ListCameraParam extends BaseListParam {

//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    @Schema(description = "商户Id链", hidden = true)
//    private String commIdChain;

    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String siteId;

    @Schema(description = "场站名称，模糊匹配")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String siteName;

    @Schema(description = "场站ID列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> siteIdList;

    @Schema(description = "设备名称模糊")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String deviceNameLike;

    @Schema(description = "设备序列号模糊")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String deviceSerialLike;

    @Schema(description = "相机id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long cameraId;

    @Schema(description = "可用", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean enable = true;

    @Schema(description = "id chain", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String idChain;

    @Schema(description = "场站组列表", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> gids;

//    @Schema(description = "查询时间类型", description = "时间范围存在为必填")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private BiDependOnType dependOn;
//
//    @Schema(description = "查询开始时间", description = "年月")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date fromDate;
//
//    @Schema(description = "", description = "年月")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private Date toDate;
//
//    @Schema(description = "是否统计各账户的消费数据")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Boolean biAccount;
}