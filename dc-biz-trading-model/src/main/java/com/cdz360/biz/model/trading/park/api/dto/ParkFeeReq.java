package com.cdz360.biz.model.trading.park.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * ParkFeeReq
 *
 * @since 8/16/2022 1:28 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ParkFeeReq extends ParkBaseReq {
    @ApiModelProperty(value = "充电云返回的充电订单号")
    private String chargeOrderNo;
    @ApiModelProperty(value = "停车系统中唯一的停车订单号,最长不超过16个字母")
    private String parkOrderNo;
    @ApiModelProperty(value = "停车入场时间,GMT+8时区,格式为 YYYY-MM-DD hh:mm:ss, 如 2022-10-25 20:45:52")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date inTime;
    @ApiModelProperty(value = "停车出场时间,GMT+8时区,格式为 YYYY-MM-DD hh:mm:ss, 如 2022-10-25 20:45:52")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date outTime;
    @ApiModelProperty(value = "实际减免的免费停车时长")
    private Integer freeMinutes;
    @ApiModelProperty(value = "停车费,单位元,保留2位小数. 用于做对账")
    private BigDecimal parkFee;
}