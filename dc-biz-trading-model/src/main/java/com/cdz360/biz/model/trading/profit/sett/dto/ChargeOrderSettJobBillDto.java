package com.cdz360.biz.model.trading.profit.sett.dto;

import com.cdz360.biz.model.trading.order.dto.OrderThinBiDto;
import com.cdz360.biz.model.trading.profit.sett.type.ProfitRuleAccountType;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "账户充电数据统计")
@Data
@Accessors(chain = true)
public class ChargeOrderSettJobBillDto {

    @Schema(description = "启动充电的账户类型", requiredMode = RequiredMode.REQUIRED)
    private ProfitRuleAccountType accountType;

    @Schema(description = "账户ID(可根据账户类型来决定账户ID)", example = "corpId(企业客户)")
    private Long accountId;

    @Schema(description = "统计数据")
    private OrderThinBiDto statistics;
}
