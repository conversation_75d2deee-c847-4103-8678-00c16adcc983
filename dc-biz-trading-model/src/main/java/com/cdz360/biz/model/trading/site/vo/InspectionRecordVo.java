package com.cdz360.biz.model.trading.site.vo;

import com.cdz360.biz.model.trading.site.type.SiteInspectionStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class InspectionRecordVo {

    private Long id;

    @Schema(description = "巡检单号")
    private String no;

    private Long opUid;

    @Schema(description = "巡检人")
    private String opName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date reportTime;

    private String remark;

    private SiteInspectionStatus status;


}
