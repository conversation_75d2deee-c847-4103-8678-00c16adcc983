package com.cdz360.biz.model.trading.iot.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class SitePvProfileDto {

    @Schema(description = "今日总发电量(kw·h)")
    private BigDecimal todayKwh = BigDecimal.ZERO;

    @Schema(description = "今日发电收益(元)")
    private BigDecimal todayProfit = BigDecimal.ZERO;

    @Schema(description = "总发电量(kw·h)")
    private BigDecimal totalKwh = BigDecimal.ZERO;

    @Schema(description = "总发电收益(元)")
    private BigDecimal totalProfit = BigDecimal.ZERO;

    @Schema(description = "当前输出功率")
    private BigDecimal curOutPower = BigDecimal.ZERO;

    @Schema(description = "今日等效时长")
    private BigDecimal curDuration = BigDecimal.ZERO;

    @Schema(description = "日均发电量(kw·h)")
    private BigDecimal perKwh = BigDecimal.ZERO;

    @Schema(description = "日均等效时长")
    private BigDecimal perDuration = BigDecimal.ZERO;

}
