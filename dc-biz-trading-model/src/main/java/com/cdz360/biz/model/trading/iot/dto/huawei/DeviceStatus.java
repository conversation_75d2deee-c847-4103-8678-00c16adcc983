package com.cdz360.biz.model.trading.iot.dto.huawei;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 设备状态
 */
@Getter
public enum DeviceStatus implements DcEnum {

    UNKNOWN(0xFF, "未知"),
    INIT(0x00, "待机：初始化"),
    INSULATION_IMPEDANCE_DETECTION(0x01, "待机：绝缘阻抗检测"),
    LIGHT_DETECTION(0x02, "待机：光照检测"),
    GRID_DETECTION(0x03, "待机：电网检测"),
    STARTED(0x0100, "启动"),
    ON_GRID(0x0200, "并网（离网模式：运行）"),
    ON_GRID_POWER_LIMITING(0x0201, "并网：限功率（离网模式：运行：限功率）"),
    ON_GRID_SELF_REDUCTION(0x0202, "并网：自降额（离网模式：运行：自降额）"),
    OFF_ABNORMAL(0x0300, "关机：异常关机"),
    OFF_DIRECTIVE(0x0301, "关机：指令关机"),
    OFF_OVGR(0x0302, "关机：OVGR"),
    OFF_COMMUNICATION_BROKEN(0x0303, "关机：通信断链"),
    OFF_POWER_LIMITING(0x0304, "关机：限功率"),
    OFF_MANUAL_BOOT_REQUIRED(0x0305, "关机：需手动开机"),
    OFF_DC_SWITCH_DISCONNECTED(0x0306, "关机：直流开关断开"),
    OFF_QUICKLY(0x0307, "关机：快速关断"),
    OFF_INPUT_UNDERPOWER(0x0308, "关机：输入欠功率"),
    GRID_DISPATCH_COS_P(0x0401, "电网调度：cosϕ-P曲线"),
    GRID_DISPATCH_Q_U(0x0402, "电网调度：Q-U曲线"),
    GRID_DISPATCH_QF_U(0x0403, "电网调度：PF-U曲线"),
    GRID_DISPATCH_DRY_CONTACTS(0x0404, "电网调度：干接点"),
    GRID_DISPATCH_Q_P(0x0405, "电网调度：Q-P曲线"),
    POINT_CHECK_READY(0x0500, "点检就绪"),
    POINT_CHECK(0x0501, "点检中"),
    INSPECTION(0x0600, "巡检中"),
    AFCI_SELF_CHECKING(0x0700, "AFCI自检"),
    IV_SCANNING(0x0800, "IV扫描中"),
    DC_INPUT_DETECTION(0x0900, "直流输入检测"),
    OFF_GRID_CHARGING(0x0A00, "运行：脱网充电"),
    NO_LIGHT(0xA000, "待机：无光照"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    DeviceStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static DeviceStatus valueOf(Object codeIn) {
        if (codeIn == null) {
            return DeviceStatus.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof DeviceStatus) {
            return (DeviceStatus) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (DeviceStatus type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return DeviceStatus.UNKNOWN;
    }

}
