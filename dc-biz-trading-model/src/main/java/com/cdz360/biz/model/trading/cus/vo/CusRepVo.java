package com.cdz360.biz.model.trading.cus.vo;

import com.cdz360.biz.model.trading.cus.po.CusRepPo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 客户信息同步表的PO
 * cus replicate po
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CusRepVo extends CusRepPo {

    @Schema(description = "用户所属商户名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commName;

    @Schema(description = "顶级商户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long topCommId;

}
