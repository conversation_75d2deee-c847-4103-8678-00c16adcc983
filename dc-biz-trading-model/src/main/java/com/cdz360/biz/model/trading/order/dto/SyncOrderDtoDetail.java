package com.cdz360.biz.model.trading.order.dto;

import com.cdz360.base.utils.JsonUtils;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class SyncOrderDtoDetail {
    private String orderNo;
    /**
     * 24小时制！区间充电的开始时间yyyy-MM-dd HH:mm:ss
     */
    private String detailStartTime;

    /**
     * 24小时制！区间充电的结束充电时间yyyy-MM-dd HH:mm:ss
     */
    private String detailEndTime;

    /**
     * 区间充电电量，需要精确到4位小数
     */
    private BigDecimal detailPower;

    /**
     * 与优行科技协商的电量费用（单价：元），精确到4位小数
     */
    private BigDecimal feeElectric;

    /**
     * 与优行科技协商的服务费用（单价：元），精确到4位小数
     */
    private BigDecimal feeService;

    /**
     * 桩企对外展示的电费费用（单价：元），精确到4位小数
     */
    private BigDecimal outFeeElectric;

    /**
     * 桩企对外展示的服务费费用（单价：元），精确到4位小数
     */
    private BigDecimal outFeeService;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
