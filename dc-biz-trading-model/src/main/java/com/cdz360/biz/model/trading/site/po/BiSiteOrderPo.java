package com.cdz360.biz.model.trading.site.po;

import com.cdz360.biz.model.trading.order.type.BiDependOnType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * BiSiteOrderPo
 *
 * @since 3/23/2020 1:34 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
public class BiSiteOrderPo {
    private Long id = 0L;
    private String siteId;
    private Date time;
    private BiDependOnType dependOn;
    private Long orderCount = 0L;
    private Long userCount = 0L;


    @Schema(description = "功率", example = "123")
    private Long power = 0L;

    private BigDecimal elecFee = BigDecimal.ZERO;
    private BigDecimal servFee = BigDecimal.ZERO;
    private BigDecimal fee = BigDecimal.ZERO;

    @Schema(description = "无结算账户金额(单位: 元)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal noAccountFee;

    @Schema(description = "后付费金额(单位: 元)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal postSettlementFee;

    @Schema(description = "预付费金额(单位: 元)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal preSettlementFee;

    @Schema(description = "赠送金额(单位: 元)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal freeFee;

    @Schema(description = "实际金额(单位: 元)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal costFee;

    private BigDecimal electricity = BigDecimal.ZERO;
    private BigDecimal elecTag1 = BigDecimal.ZERO;//尖
    private BigDecimal elecTag2 = BigDecimal.ZERO;//峰
    private BigDecimal elecTag3 = BigDecimal.ZERO;//平
    private BigDecimal elecTag4 = BigDecimal.ZERO;//谷
    private Date createTime;
    private Date updateTime;

}