package com.cdz360.biz.model.trading.order.wrapper;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.charge.type.OrderPayStatus;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.biz.model.trading.order.po.ChargerOrderPayPo;
import com.cdz360.biz.model.trading.order.type.ChargerOrderRefundStatus;

import java.math.BigDecimal;
import java.util.Date;

/**
 * ChargerOrderPayPo 更新包装类
 */
public class ChargerOrderPayWrapper {


    public static void setPayMode(Integer payMode, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setPayMode(payMode);
        obj4Update.setPayMode(payMode);
    }

    public static void setAccountType(PayAccountType accountType, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setAccountType(accountType);
        obj4Update.setAccountType(accountType);
    }

    public static void setAccountId(Long accountId, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setAccountId(accountId);
        obj4Update.setAccountId(accountId);
    }

    public static void setPayClient(Integer payClient, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setPayClient(payClient);
        obj4Update.setPayClient(payClient);
    }

    public static void setPayStatus(OrderPayStatus payStatus, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setPayStatus(payStatus);
        obj4Update.setPayStatus(payStatus);
    }

    /**
     * 退款状态. 0,未知/无需退款. 1，初始化，2，已提交，3已退款， 4，失败
     */
    public static void setRefundStatus(ChargerOrderRefundStatus refundStatus, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setRefundStatus(refundStatus);
        obj4Update.setRefundStatus(refundStatus);
    }

    /**
     * 订单金额, 单位"元"
     */
    public static void setOrderOriginFee(BigDecimal orderOriginFee, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setOrderOriginFee(orderOriginFee);
        obj4Update.setOrderOriginFee(orderOriginFee);
        if (curObj.getOrderFee() == null) {
            setOrderFee(orderOriginFee, curObj, obj4Update);
        }
    }

    /**
     * 订单金额, 单位"元". 使用协议价/卡券抵扣结算后金额
     */
    public static void setOrderFee(BigDecimal orderFee, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setOrderFee(orderFee);
        obj4Update.setOrderFee(orderFee);
    }

    /**
     * 电费原始金额，桩端传递金额/按电价结算金额
     */
    public static void setElecOriginFee(BigDecimal elecOriginFee, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setElecOriginFee(elecOriginFee);
        obj4Update.setElecOriginFee(elecOriginFee);
        if (curObj.getElecFee() == null) {
            setElecFee(elecOriginFee, curObj, obj4Update);
        }
    }

    public static void setElecFee(BigDecimal elecFee, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setElecFee(elecFee);
        obj4Update.setElecFee(elecFee);
    }

    public static void setElecCostFee(BigDecimal elecCostFee, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setElecCostFee(elecCostFee);
        obj4Update.setElecCostFee(elecCostFee);
    }

    public static void setElecFreeFee(BigDecimal elecFreeFee, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setElecFreeFee(elecFreeFee);
        obj4Update.setElecFreeFee(elecFreeFee);
    }

    /**
     * 服务费原始金额，桩端传递金额/按服务费原价结算金额
     */
    public static void setServOriginFee(BigDecimal servOriginFee, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setServOriginFee(servOriginFee);
        obj4Update.setServOriginFee(servOriginFee);
        if (curObj.getServFee() == null) {
            setServFee(servOriginFee, curObj, obj4Update);
        }
    }

    public static void setServFee(BigDecimal servFee, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setServFee(servFee);
        obj4Update.setServFee(servFee);
    }

    public static void setServCostFee(BigDecimal servCostFee, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setServCostFee(servCostFee);
        obj4Update.setServCostFee(servCostFee);
    }

    public static void setServFreeFee(BigDecimal servFreeFee, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setServFreeFee(servFreeFee);
        obj4Update.setServFreeFee(servFreeFee);
    }

    public static void setCouponAmount(BigDecimal couponAmount, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setCouponAmount(couponAmount);
        obj4Update.setCouponAmount(couponAmount);
    }

    public static void setScoreAmount(BigDecimal scoreAmount, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setScoreAmount(scoreAmount);
        obj4Update.setScoreAmount(scoreAmount);
    }

    public static void setPrepayAmount(BigDecimal prepayAmount, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setPrepayAmount(prepayAmount);
        obj4Update.setPrepayAmount(prepayAmount);
    }

    public static void setPrepayTradeNo(String prepayTradeNo, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setPrepayTradeNo(prepayTradeNo);
        obj4Update.setPrepayTradeNo(prepayTradeNo);
    }

    public static void setPrepay3rdTradeNo(String prepay3rdTradeNo, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setPrepay3rdTradeNo(prepay3rdTradeNo);
        obj4Update.setPrepay3rdTradeNo(prepay3rdTradeNo);
    }

    public static void setCreditOrderNo(String creditOrderNo, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setCreditOrderNo(creditOrderNo);
        obj4Update.setCreditOrderNo(creditOrderNo);
    }

    public static void setRefundAmount(BigDecimal refundAmount, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setRefundAmount(refundAmount);
        obj4Update.setRefundAmount(refundAmount);
    }

    public static void setRefundTradeNo(String refundTradeNo, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setRefundTradeNo(refundTradeNo);
        obj4Update.setRefundTradeNo(refundTradeNo);
    }

    public static void setRefund3rdTradeNo(String refund3rdTradeNo, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setRefund3rdTradeNo(refund3rdTradeNo);
        obj4Update.setRefund3rdTradeNo(refund3rdTradeNo);
    }

    public static void setOverTimeParkFee(BigDecimal overTimeParkFee, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setOverTimeParkFee(overTimeParkFee);
        obj4Update.setOverTimeParkFee(overTimeParkFee);
    }

    public static void setDebtAmount(BigDecimal debtAmount, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setDebtAmount(debtAmount);
        obj4Update.setDebtAmount(debtAmount);
    }

    public static void setDebtTradeNo(String debtTradeNo, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setDebtTradeNo(debtTradeNo);
        obj4Update.setDebtTradeNo(debtTradeNo);
    }

    public static void setDebt3rdTradeNo(String debt3rdTradeNo, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setDebt3rdTradeNo(debt3rdTradeNo);
        obj4Update.setDebt3rdTradeNo(debt3rdTradeNo);
    }

    public static void setPriceCode(Long priceCode, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setPriceCode(priceCode);
        obj4Update.setPriceCode(priceCode);
    }

    public static void setCorpPayStatus(Integer corpPayStatus, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setCorpPayStatus(corpPayStatus);
        obj4Update.setCorpPayStatus(corpPayStatus);
    }

    public static void setBillNo(String billNo, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setBillNo(billNo);
        obj4Update.setBillNo(billNo);
    }

    public static void setPayTime(Date payTime, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setPayTime(payTime);
        obj4Update.setPayTime(payTime);
    }

    public static void setActivityId(Long activityId, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setActivityId(activityId);
        obj4Update.setActivityId(activityId);
    }

    public static void setInvoicedId(Long invoicedId, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setInvoicedId(invoicedId);
        obj4Update.setInvoicedId(invoicedId);
    }

    public static void setInvoiceAmount(BigDecimal invoiceAmount, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setInvoiceAmount(invoiceAmount);
        obj4Update.setInvoiceAmount(invoiceAmount);
    }

    public static void setInvoicedAmount(BigDecimal invoicedAmount, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setInvoicedAmount(invoicedAmount);
        obj4Update.setInvoicedAmount(invoicedAmount);
    }

    public static void setSettlementType(SettlementType settlementType, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setSettlementType(settlementType);
        obj4Update.setSettlementType(settlementType);
    }

    public static void setDiscountRefId(Long discountRefId, ChargerOrderPayPo curObj, ChargerOrderPayPo obj4Update) {
        curObj.setDiscountRefId(discountRefId);
        obj4Update.setDiscountRefId(discountRefId);
    }

}
