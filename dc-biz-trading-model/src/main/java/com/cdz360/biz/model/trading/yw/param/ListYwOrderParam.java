package com.cdz360.biz.model.trading.yw.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.biz.model.trading.yw.type.CreateYwOrderSourceType;
import com.cdz360.biz.model.trading.yw.type.YwOrderStatus;
import com.cdz360.biz.model.trading.yw.type.YwOrderTag;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "获取运维工单列表参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListYwOrderParam extends BaseListParam {

    @Schema(description = "运维工单编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String ywOrderNo;

    @Schema(description = "运维人名字 支持模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String maintName;

    @Schema(description = "创建人名字 支持模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String createOpName;

    @Schema(description = "运维工单状态列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<YwOrderStatus> orderStatusList;

    @Schema(description = "运维人ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long maintUid;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> maintUidList;

    @Schema(description = "查看运维工单列表的用户ID 运维用户需要在历史工单中查看自己转派出去的记录(仅用来查询历史工单)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long lookUpUid;

    @Schema(description = "场站ID 只查询某个场站运维工单")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "场站名称 支持模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;

    @Schema(description = "场站所属商户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long siteCommId;

    @Schema(description = "桩编号 获取桩维修记录使用")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String evseNo;

    @Schema(description = "运维工单来源. 0,未知; 20,充电管理平台; 21,运营支撑平台; 22,企业客户平台; 23,桩管家小程序")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<CreateYwOrderSourceType> sourceTypeList;

    @Schema(description = "场站商户链", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commIdChain;

    @Schema(description = "场站组id列表 仅用于管理后台相关接口")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> gids;

    @Schema(description = "运维时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter maintTimeRange;

    @Schema(description = "创建人ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long createUid;

    @Schema(description = "用户是否已接收处理反馈 C端用户获取未推送处理结果的标识(true -- 已告知用户; false -- 未告知用户)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean recNotice;

    @Schema(description = "运维工单类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer ywType;

    @Schema(description = "是否更换器件")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean replacedDevice;

    @Schema(description = "器件名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String goodsStr;

    @Schema(description = "考核标签(1-考核;0-不考核)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private YwOrderTag tag;

}
