package com.cdz360.biz.model.trading.iot.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "商户首页光伏站概况")
public class PvProfileDto {

    @Schema(description = "光伏站总数量")
    private Integer pvSiteNum = 0;

    @Schema(description = "总装机功率(kw)")
    private BigDecimal installedCapacityTotal = BigDecimal.ZERO;

    @Schema(description = "逆变器数量(个)")
    private Long inverterNum = 0L;

    @Schema(description = "今日总发电量(kw·h)")
    private BigDecimal todayKwh = BigDecimal.ZERO;

    @Schema(description = "今日总发电收益(元)")
    private BigDecimal todayProfit = BigDecimal.ZERO;

    @Schema(description = "本月总发电量(kw·h)")
    private BigDecimal currMonthKwh = BigDecimal.ZERO;

    @Schema(description = "本月总发电收益(元)")
    private BigDecimal currMonthProfit = BigDecimal.ZERO;

    @Schema(description = "本年总发电量(kw·h)")
    private BigDecimal currYearKwh = BigDecimal.ZERO;

    @Schema(description = "本年总发电收益(元)")
    private BigDecimal currYearProfit = BigDecimal.ZERO;

    @Schema(description = "总发电量(kw·h)")
    private BigDecimal totalKwh = BigDecimal.ZERO;

    @Schema(description = "总发电收益(元)")
    private BigDecimal totalProfit = BigDecimal.ZERO;

    @Schema(description = "累计节约标准煤(吨)")
    private BigDecimal coal = BigDecimal.ZERO;

    @Schema(description = "CO2累计减排(吨)")
    private BigDecimal co2 = BigDecimal.ZERO;

    @Schema(description = "SO2累计减排(吨)")
    private BigDecimal so2 = BigDecimal.ZERO;

    @Schema(description = "累计植树(棵)")
    private BigDecimal tree = BigDecimal.ZERO;

    @Schema(description = "并网日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date onGridDate;

}
