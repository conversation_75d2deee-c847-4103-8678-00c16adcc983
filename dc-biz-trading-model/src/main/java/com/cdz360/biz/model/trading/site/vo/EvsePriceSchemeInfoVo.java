package com.cdz360.biz.model.trading.site.vo;

import com.cdz360.biz.model.iot.vo.ChargeV2;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2020/1/7 17:27
 */
@Schema(description = "桩计费详情")
@Data
@Accessors(chain = true)
public class EvsePriceSchemeInfoVo {
    @Schema(description = "桩编号")
    private String evseNo;

    @Schema(description = "计费模板Id")
    private Long priceSchemeId;

    @Schema(description = "计费模板名称")
    private String priceSchemeName;

    @Schema(description = "分时段计费方案")
    private List<ChargeV2> price;
}
