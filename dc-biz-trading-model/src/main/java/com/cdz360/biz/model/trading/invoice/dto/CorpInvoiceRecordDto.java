package com.cdz360.biz.model.trading.invoice.dto;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.base.model.base.type.UserType;
import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.model.invoice.type.InvoiceChannel;
import com.cdz360.biz.model.invoice.type.InvoiceType;
import com.cdz360.biz.model.invoice.type.InvoicedStatus;
import com.cdz360.biz.model.invoice.type.ProductType;
import com.cdz360.biz.model.oa.constant.OaConstants;
import com.cdz360.biz.model.oa.vo.InvoiceCompareVo;
import com.cdz360.biz.model.oa.vo.InvoicingContentVo;
import com.cdz360.biz.model.trading.invoice.vo.CorpInvoiceReturnPlanVo;
import com.cdz360.biz.model.trading.invoice.vo.CorpInvoicingContentVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "企业开票记录传输")
public class CorpInvoiceRecordDto extends BaseObject implements Serializable {

    private static final BigDecimal MAX_FIX = BigDecimal.valueOf(10000000000L);

    @Schema(description = "申请单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String applyNo;

    @Schema(description = "流程实例ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String procInstId;

    @Schema(description = "oa流程定义KEY")
    private String procDefKey;

    @Schema(description = "是否为企客对账开票流程后台发起的请求 是(true) 否(false or null)")
    private Boolean billingProcessRequest;

    @Schema(description = "t_corp的id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long corpId;

    @Schema(description = "企业客户用户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long uid;

    @Schema(description = "企业名称 关联查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String corpName;

    @Schema(description = "流程状态: " +
        "草稿(NOT_SUBMITTED); 审核中(SUBMITTED); " +
        "开票中(REVIEWED); 审核未通过(AUDIT_FAILED); " +
        "开票未通过(INVOICING_FAIL);已开具(COMPLETED); " +
        "已作废(INVALID)[含红冲状态]")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InvoicedStatus status;

    @Schema(description = "开票主体ID 通过这个可以查找到开票主体")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long tempSalId;

    @Schema(description = "开具方式")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InvoiceChannel channel;

    @Schema(description = "开票主体名称 关联查询")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String saleName;

    @Schema(description = "开票主体纳税人识别号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String saleTin;

    @Schema(description = "商品行模板名称 关联查询")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String productTempName;

    @Schema(description = "商品行模板ID(t_invoiced_sal_temp.id)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long productTempId;

    @Schema(description = "企业客户开票抬头模板ID invoiced_model.id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long modelId;

    @Schema(description = "开票抬头")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String invoiceName;

    @Schema(description = "企业所属商户")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long commId;

    @Schema(description = "开票种类: PER_COMMON(个人普票); ENTER_COMMON(企业普票); ENTER_PROFESSION(企业专票)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InvoiceType invoiceType;

    @Schema(description = "企业客户开票方式: 充值预开票(PRE_PAY)、充电后开票(POST_CHARGER)、后付费账单开票(POST_SETTLEMENT)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InvoicingMode invoiceWay;

    @Schema(description = "明细订单数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer orderCnt;

    @Schema(description = "应开电费")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal actualElecFee;

    @Schema(description = "实开电费 调整后的开票金额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal fixElecFee;
    @Schema(description = "应开服务费")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal actualServFee;

    @Schema(description = "实开服务费 调整后的开票金额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal fixServFee;

    @Schema(description = "应开预付卡费")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal actualPrepaidCardFee;

    @Schema(description = "实开预付卡费 调整后的开票金额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal fixPrepaidCardFee;

    @Schema(description = "应开技术服务费")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal actualTechServFee;

    @Schema(description = "实开技术服务费 调整后的开票金额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal fixTechServFee;

    @Schema(description = "电费数量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal elecNum;

    @Schema(description = "服务费数量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal servNum;

    @Schema(description = "预付卡费数量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal prepaidCardNum;

    @Schema(description = "应开总额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal totalFee;

    @Schema(description = "实开总额 调整后的开票金额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal fixTotalFee;

    @Schema(description = "开票内容(实开)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<CorpInvoicingContentVo> invoicingContent;

    @Schema(description = "开票数据对比")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<InvoiceCompareVo> actualData;

    @Schema(description = "开票备注")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String invoicingRemark;

    @Schema(description = "回款情况")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer returnFlag;

    @Schema(description = "回款计划")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<CorpInvoiceReturnPlanVo> returnPlanVoList;

    @Schema(description = "回款图片")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> returnImages;

    @Schema(description = "申请备注")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String applyRemark;

    @Schema(description = "图片")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> images;

    @Schema(description = "开具时间时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date issuedTime;

    @Schema(description = "开具附属信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String issuedRemark;

    @Schema(description = "申请失败原因")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String failRemark;

    @Schema(description = "审核时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date auditTime;

    @Schema(description = "审核建议")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String auditRemark;

    @Schema(description = "审核人")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String auditName;

    @Schema(description = "创建操作人类型. 0,未知; 1,商户/平台用户; 3,企业客户; 4,系统")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private UserType createOpType;

    @Schema(description = "创建操作人ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long createOpId;

    @Schema(description = "创建操作人名字")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String createOpName;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "操作人类型. 0,未知; 1,商户/平台用户; 3,企业客户; 4,系统")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private UserType updateOpType;

    @Schema(description = "操作人ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long updateOpId;

    @Schema(description = "操作人名字")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String updateOpName;

    @Schema(description = "发票代码")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String invoiceCode;

    @Schema(description = "发票号码")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String invoiceNumber;

    @Schema(description = "开具时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date reviewTime;

    public boolean fromPrepaidOrderInvoicingProcess() {
        return StringUtils.equalsIgnoreCase(OaConstants.PD_KEY_PREPAID_ORDER_INVOICING, procDefKey);
    }

    public static void submitCheck(CorpInvoiceRecordDto dto) {
        if (dto.getCorpId() == null) {
            throw new DcArgumentException("请提供企业客户ID");
        }

        if (dto.getInvoiceWay() == null) {
            throw new DcArgumentException("需要提供企业客户的开票方式");
        }

        if (StringUtils.isBlank(dto.getApplyNo())) {
            throw new DcArgumentException("开票记录申请单号不能为空");
        }

//        if (StringUtils.isBlank(dto.getSaleTin())) {
//            throw new DcArgumentException("开票主体纳税人识别号不能为空");
//        }
        if (null == dto.getTempSalId()
            && !dto.fromPrepaidOrderInvoicingProcess() // 预付订单开票流程时忽略
        ) {
            throw new DcArgumentException("开票主体记录ID不能为空");
        }

        if (null == dto.getProductTempId()
            && !dto.fromPrepaidOrderInvoicingProcess() // 预付订单开票流程时忽略
        ) {
            throw new DcArgumentException("商品行模板ID不能为空");
        }

        if (null == dto.getActualElecFee() && null == dto.getActualServFee()
            && null == dto.getActualPrepaidCardFee()
            && !dto.fromPrepaidOrderInvoicingProcess() // 预付订单开票流程时忽略
        ) {
            throw new DcArgumentException("应开电费和应开服务费和应开预付卡费不能同时为空");
        }

        boolean checkFixAmount = true; // 是否检查修正电费和修正服务费不能同时为空
        if (CollectionUtils.isNotEmpty(dto.getInvoicingContent())) {

            Set<ProductType> collect = dto.getInvoicingContent().stream()
                .map(InvoicingContentVo::getProductType).filter(Objects::nonNull)
                .collect(Collectors.toSet());

            List<ProductType> needCheckTypes = List.of(ProductType.ELEC_ACTUAL_FEE,
                ProductType.SERV_ACTUAL_FEE, ProductType.PREPAID_CARD_FEE);

            // 实开内容只包含电费和服务费和预付卡费三种时，需检查
            checkFixAmount = collect.size() == needCheckTypes.size()
                && collect.containsAll(needCheckTypes);
            // 实开内容只包含电费或服务费或预付卡费时，需检查
            checkFixAmount = checkFixAmount || (collect.size() == 1 && collect.stream()
                .anyMatch(needCheckTypes::contains));
        }

        if (checkFixAmount && (null == dto.getFixElecFee() ||
            DecimalUtils.isZero(dto.getFixElecFee())) &&
            (null == dto.getFixServFee() ||
                DecimalUtils.isZero(dto.getFixServFee())) &&
            (null == dto.getFixPrepaidCardFee() ||
                DecimalUtils.isZero(dto.getFixPrepaidCardFee()))) {
            throw new DcArgumentException("修正电费和修正服务费和修正预付卡费不能同时为空");
        }

        if ((dto.getFixElecFee() != null && DecimalUtils.gt(dto.getFixElecFee(), MAX_FIX)) ||
            (dto.getFixServFee() != null && DecimalUtils.gt(dto.getFixServFee(), MAX_FIX)) ||
            (dto.getFixPrepaidCardFee() != null && DecimalUtils.gt(dto.getFixPrepaidCardFee(), MAX_FIX))) {
            throw new DcArgumentException("修正电费或修正服务费或修正预付卡费金额过大");
        }
    }
}
