package com.cdz360.biz.model.trading.soc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.ToString;

/**
 * MoveCorpUserSocStrategyList
 *
 * @since 10/28/2020 3:53 PM
 * <AUTHOR>
 */
@Schema(description = "soc限制配置保留/删除列表")
@Data
@ToString(callSuper = true)
public class MoveCorpUserSocStrategyList {
    private List<MoveCorpUserSocStrategyVo> removeList = new ArrayList<>();
    private List<MoveCorpUserSocStrategyVo> remainList = new ArrayList<>();
}