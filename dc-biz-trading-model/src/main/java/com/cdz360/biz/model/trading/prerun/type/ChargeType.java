package com.cdz360.biz.model.trading.prerun.type;

import com.fasterxml.jackson.annotation.JsonCreator;
import lombok.Getter;

/**
 * ChargeType
 * 
 * @since 6/21/2022 4:59 PM
 * <AUTHOR>
 */
@Getter
public enum ChargeType/* implements DcEnum*/ {
    UNKNOWN("未知")/*(0, "UNKNOWN")*/,
    VIN("VIN码")/*(1, "VIN")*/,
    CARD("刷卡")/*(2, "CARD")*/,
    QRCODE("扫码")/*(3, "QRCODE")*/;

    private String desc;
    ChargeType(String str) {
        this.desc = str;
    }

//    @JsonCreator
//    public static ChargeType valueOf(Object value) {
//        if (value instanceof ChargeType) {
//            return (ChargeType) value;
//        } else if (value instanceof String) {
//            for (ChargeType dep : ChargeType.values()) {
//                if (dep.name().equals(value)) {
//                    return dep;
//                }
//            }
//        }
//        return null;
//    }
}