package com.cdz360.biz.model.trading.site.po;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel(value = "")
public class InvoicedRecordPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@Size(max = 255, message = "invoiced_type 长度不能超过 255")
	private String invoicedType;

	@Size(max = 255, message = "invoice_type 长度不能超过 255")
	private String invoiceType;

	private Long userId;

	@ApiModelProperty(value = "申请单号")
	@Size(max = 16, message = "applyNo 长度不能超过 16")
	private String applyNo;

	@Size(max = 255, message = "invoiced_status 长度不能超过 255")
	private String invoicedStatus;

	@Size(max = 255, message = "invoice_code 长度不能超过 255")
	private String invoiceCode;

	@Size(max = 255, message = "invoice_number 长度不能超过 255")
	private String invoiceNumber;

	private Date invoiceDate;

	@ApiModelProperty(value = "开具时间")
	private Date issuedTime;

	@NotNull(message = "invoice_amount 不能为 null")
	private Integer invoiceAmount;

	@ApiModelProperty(value = "电费实收金额")
	private BigDecimal elecActualFee;

	@ApiModelProperty(value = "服务费实收金额")
	private BigDecimal servActualFee;

	@ApiModelProperty(value = "停充超时实收金额")
	private BigDecimal parkActualFee;

	@ApiModelProperty(value = "预付卡实收金额")
	private BigDecimal prepaidCardActualFee;

	@Size(max = 255, message = "invoice_name 长度不能超过 255")
	private String invoiceName;

	@Size(max = 255, message = "invoice_tin 长度不能超过 255")
	private String invoiceTin;

	@Size(max = 255, message = "invoice_address 长度不能超过 255")
	private String invoiceAddress;

	@Size(max = 20, message = "invoice_tel 长度不能超过 20")
	private String invoiceTel;

	@Size(max = 255, message = "invoice_bank 长度不能超过 255")
	private String invoiceBank;

	@Size(max = 255, message = "invoice_account 长度不能超过 255")
	private String invoiceAccount;

	@Size(max = 255, message = "invoice_desc 长度不能超过 255")
	private String invoiceDesc;

	private Date createdDate;

	private Date reviewedDate;

	@Size(max = 255, message = "creator_name 长度不能超过 255")
	private String creatorName;

	@Size(max = 255, message = "email 长度不能超过 255")
	private String email;

	@Size(max = 255, message = "tracking_number 长度不能超过 255")
	private String trackingNumber;

	@Size(max = 255, message = "province 长度不能超过 255")
	private String province;

	@Size(max = 255, message = "city 长度不能超过 255")
	private String city;

	@Size(max = 255, message = "area 长度不能超过 255")
	private String area;

	@ApiModelProperty(value = "审核日期-客服")
	private Date auditorDate;

	@ApiModelProperty(value = "审核人姓名-客服")
	@Size(max = 255, message = "auditor_name 长度不能超过 255")
	private String auditorName;

	@ApiModelProperty(value = "审核日期-财务")
	private Date approverDate;

	@ApiModelProperty(value = "审核人姓名-财务")
	@Size(max = 255, message = "approver_name 长度不能超过 255")
	private String approverName;

	@ApiModelProperty(value = "物流唯一订单号")
	@Size(max = 30, message = "tracking_order_no 长度不能超过 30")
	private String trackingOrderNo;

	@ApiModelProperty(value = "收件人")
	@Size(max = 50, message = "receiver_name 长度不能超过 50")
	private String receiverName;

	@ApiModelProperty(value = "收件人手机号")
	@Size(max = 50, message = "receiver_mobile_phone 长度不能超过 50")
	private String receiverMobilePhone;

	@ApiModelProperty(value = "收件人省")
	@Size(max = 50, message = "receiver_province 长度不能超过 50")
	private String receiverProvince;

	@ApiModelProperty(value = "收件人市")
	@Size(max = 50, message = "receiver_city 长度不能超过 50")
	private String receiverCity;

	@ApiModelProperty(value = "收件人区")
	@Size(max = 50, message = "receiver_area 长度不能超过 50")
	private String receiverArea;

	@ApiModelProperty(value = "收件人详细地址")
	@Size(max = 255, message = "receiver_address 长度不能超过 255")
	private String receiverAddress;

	@ApiModelProperty(value = "来源：0 用户主动开票 1 pc开票 2 定时任务开票")
	@Size(max = 10, message = "source 长度不能超过 10")
	private String source;

	@ApiModelProperty(value = "场站id（对应东正库）")
	@Size(max = 32, message = "station_id 长度不能超过 32")
	private String stationId;

	private Long commercialId;

	@ApiModelProperty(value = "互联互通的申请单号")
	@Size(max = 20, message = "hlht_apply_no 长度不能超过 20")
	private String hlhtApplyNo;

	@ApiModelProperty(value = "0:任我充 1:朗新 后续可增加")
	private Integer platformSource;

	@ApiModelProperty(value = "驳回原因")
	@Size(max = 255, message = "reject_reason 长度不能超过 255")
	private String rejectReason;

	@ApiModelProperty(value = "互联互通用户标识")
	@Size(max = 30, message = "hlht_user_no 长度不能超过 30")
	private String hlhtUserNo;

	@ApiModelProperty(value = "流程实例ID")
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private String procInstId;

	@Schema(description = "开票主体ID(invoiced_template_sal.id)")
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private Long tempSalId;

	@Schema(description = "商品行模板ID(t_invoiced_sal_temp.id)")
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private Long productTempId;

	@Schema(description = "是否启用开票功能 1启用0不启用 C端开票默认启用 流程默认不启用，到开票这一步才是启用")
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private Boolean invoicedEnabled;

}
