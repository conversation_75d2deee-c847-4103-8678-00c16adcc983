package com.cdz360.biz.model.trading.settle.vo;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.biz.model.annotations.ExcelField;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "商户结算单充值订单导出")
@Data
@Accessors(chain = true)
public class ChargeOrderExportVO implements Serializable {

    @ExcelField(title = "订单号", sort = 1)
    @Schema(description = "充电订单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderNo;

    @ExcelField(title = "所属客户", sort = 3)
    @Schema(description = "所属客户")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cusName;

    @ExcelField(title = "手机号", sort = 6)
    @Schema(description = "手机号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cusPhone;

    @ExcelField(title = "充值账户类型", sort = 9, convert = "com.cdz360.base.model.base.type.PayAccountType")
    @Schema(description = "充值账户类型")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private PayAccountType accountType;

    @ExcelField(title = "支付方式", sort = 12, convert = "com.cdz360.base.model.base.type.PayChannel")
    @Schema(description = "支付方式")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private PayChannel payChannel;

    @ExcelField(title = "充值总金额", sort = 15)
    @Schema(description = "充值总金额")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal totalAmount;

    @ExcelField(title = "赠送金额", sort = 17)
    @Schema(description = "赠送金额")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal freeAmount;

    @ExcelField(title = "实际金额", sort = 22)
    @Schema(description = "实际金额")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private BigDecimal costAmount;

    @ExcelField(title = "实际金额清分比例(%)", sort = 24)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "对运营商分成比例 新调整意义: 充值清分时的清分比例. 保留两位小数,单位%")
    private BigDecimal shareRate;
    @ExcelField(title = "清分金额", sort = 28)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "对运营商分成比例费用 新调整意义: 充值清分时的清分费用")
    private BigDecimal shareRateFee;
}
