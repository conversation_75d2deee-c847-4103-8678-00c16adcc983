package com.cdz360.biz.model.trading.order.vo;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.model.base.type.UserType;
import com.cdz360.biz.model.FileItem;
import com.cdz360.biz.model.finance.type.ExpressStatus;
import com.cdz360.biz.model.finance.type.FlowInAccountType;
import com.cdz360.biz.model.finance.type.InvoiceMode;
import com.cdz360.biz.model.finance.type.TaxStatus;
import com.cdz360.biz.model.finance.type.TaxType;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.cdz360.biz.model.trading.deposit.vo.DepositSourceType;
import com.cdz360.biz.model.trading.order.type.PayBillStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 充值记录查看
 *
 * <AUTHOR>
 * @since 2019/11/5 17:07
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ToString
@Schema(description = "充值记录查看")
public class PayBillVo implements Serializable {

    @Schema(description = "充值记录Id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long id;

    @Schema(description = "用户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long userId;

    @Schema(description = "所属商户id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long commId;

    private Long topCommId;

    /**
     * 所属商户名称
     */
    @Schema(description = "所属商户名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commercialName;

    @Schema(description = "充值金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal amount;

    @Schema(description = "赠送金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal freeAmount;

    @Schema(description = "充值剩余金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal remainAmount;

    @Schema(description = "赠送剩余金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal remainFreeAmount;

    @Schema(description = "充值订单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderId;

    @Schema(description = "充值来源")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private DepositSourceType sourceType;

    @Schema(description = "支付方式")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private PayChannel payChannel;

    @Schema(description = "充值账户类型. 个人账户/商户会员/企业客户")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private PayAccountType accountType;

    @Schema(description = "账户编号. 个人账户/企业客户为集团商户编号; 商户会员为商户编号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long accountCode;

    @Schema(description = "到账账户")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private FlowInAccountType flowInAccountType;

    @Schema(description = "充值类型. 充值/减少")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private DepositFlowType flowType;

    @Schema(description = "flowType为OUT_FLOW时,对应的充值单号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String refBillNo;

    @Schema(description = "发票类型(税种): UNKNOWN(0)-未知," +
        "NORMAL_TAX(2)-个人普通发票,PREPAY_TAX(3)-企业普通发票," +
        "SPECIAL_VAT(5)-企业专业发票", example = "UNKNOWN")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TaxType taxType;
    @Schema(description = "开票状态", example = "NO")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TaxStatus taxStatus;
    @Schema(description = "发票类型: UNKNOWN(0)-未知," +
        "ONLINE(1)-电子票,PAPER(2)-纸质票", example = "PAPER")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InvoiceMode invoiceMode;

    @Schema(description = "税票号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String taxNo;

    @Schema(description = "发票寄送状态: UNKNOWN(0)-未知,INIT(1)-未寄送,SENT(2)-已寄送", example = "SENT")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ExpressStatus expressStatus;

    @Schema(description = "物流公司名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String expressCompany;

    @Schema(description = "物流单号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String expressNo;

    @Schema(description = "充值前余额, 单位'元', 2位小数. 含赠送余额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal amountBefore;

    @Schema(description = "充值后余额, 单位'元', 2位小数. 含赠送余额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal amountAfter;

    @Schema(description = "直付通商户名称", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String zftName;

    /**
     * 支付状态：0未支付，1已支付，2支付失败 3发起支付(C端响应)，4发起支付(C端响应)，5超时 6余额已耗尽 {@link PayBillStatus}
     */
    @Schema(description = "支付状态: UNPAID(0)未支付," +
        "PAID(1)已支付,PAY_FAIL(2)支付失败," +
        "SEND_PAY_SUCCESS(3)发起支付成功,SEND_PAY_FAIL(4)发起支付失败," +
        "TIME_OUT(5)超时,EXHAUST(6)余额已耗尽,UNKNOWN(99)未知", example = "1")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer status;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间: yyyy-MM-dd HH:mm:ss", example = "2019-11-11 19:22:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间: yyyy-MM-dd HH:mm:ss", example = "2019-11-11 19:22:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date updateTime;

    /**
     * 第三方交易号
     */
    @Schema(description = "第三方交易号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String tradeNo;

    /**
     * 支付时间
     */
    @Schema(description = "支付时间: yyyy-MM-dd HH:mm:ss", example = "2019-11-11 19:22:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date payTime;

    /**
     * 支付中心回调返回交易类型：1 为支付(pay)，2 为退款(refund)
     */
    @Schema(description = "支付中心回调返回交易类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer notifyType;

    /**
     * 充电订单号（即充即退）
     */
    @Schema(description = "充电订单号（即充即退）")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String chargeOrderNo;

    @Schema(description = "支付账户名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String outAccountName;

    @Schema(description = "支付账户银行名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String outBankName;

    @Schema(description = "支付账户银行卡号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String outAccountNo;

    @Schema(description = "收款账户名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String inAccountName;

    @Schema(description = "收款账户银行名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String inBankName;

    @Schema(description = "收款账户银行卡号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String inAccountNo;

    @Schema(description = "账户流水序号数组,使用逗号分隔")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String flowSeqNo;

    @Schema(description = "操作人类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private UserType opUserType;  // UserType

    @Schema(description = "操作人ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long opUid;

    @Schema(description = "操作人姓名")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String opName;

    @Schema(description = "所属客户名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String cusName;

    @Schema(description = "所属客户手机号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String cusPhone;

    @Schema(description = "客户类型:UNKNOWN(0)-未知,SYS_USER(1)-商户/平台用户," +
        "CUSTOMER(2)-C端客户,CORP_USER(3)-企业用户")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private UserType userType;

    @Schema(description = "flowType为OUT_FLOW时,对应的充值实际金额, 单位'元', 2位小数.")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal refBillAmount;

    @Schema(description = "flowType为OUT_FLOW时,对应的充值赠送金额, 单位'元', 2位小数.")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal refBillFreeAmount;

    @Schema(description = "flowType为OUT_FLOW时,对应的充值剩余实际金额, 单位'元', 2位小数.")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal refBillRemainAmount;

    @Schema(description = "flowType为OUT_FLOW时,对应的充值剩余赠送金额, 单位'元', 2位小数.")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal refBillRemainFreeAmount;

    @Schema(description = "充值前的账户信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private PayBillAccountDetailVo refBillAccountInfo;

    @Schema(description = "flowType为OUT_FLOW时,对应充值的创建时间: yyyy-MM-dd HH:mm:ss", example = "2019-11-11 19:22:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date refBillCreateTime;

    @Schema(description = "flowType为OUT_FLOW时,对应充值的发票类型(税种): UNKNOWN(0)-未知,NONE(1)-未开票,NORMAL_TAX(2)-普通发票,PREPAY_TAX(3)-预充值发票,VALUE_ADDED_TAX(4)-增值税普通发票,SPECIAL_VAT(5)-增值税专用发票", example = "NONE")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TaxType refBillTaxType;

    @Schema(description = "flowType为OUT_FLOW时,对应发票状态: NO(0)-未开票,YES(1)-已开票", example = "NO")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TaxStatus refBillTaxStatus;

    @Schema(description = "充值金额，实际金额+赠送金额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal totalAmount;

    @Schema(description = "付款账户名称: 账户类型 + 账户名称(页面查看字段)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String payAccountName;

    @Schema(description = "备注")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String remark;

    @Schema(description = "已开票金额: 单位，元")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal invoicedAmount;


    private List<Integer> statusList;

    @Schema(description = "申请单号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long applyId;

    @Schema(description = "附件oss列表,json列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<FileItem> attachment;

    @Schema(description = "可退款金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal canRefundAmount;

    @Schema(description = "减少实际金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal outFlowAmount;

    @Schema(description = "可开票金额, 单位'元', 2位小数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal canInvoiceAmount;

    @Schema(description = "已完结的增加申请的关联流程ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String procInstId;

    @Schema(description = "未完结的关联流程ID, 仅减少的流程有值，给开票申请锁定用的")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String runProcInstId;
}
