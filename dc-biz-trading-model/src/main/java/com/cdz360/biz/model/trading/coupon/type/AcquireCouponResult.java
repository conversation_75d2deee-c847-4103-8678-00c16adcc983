package com.cdz360.biz.model.trading.coupon.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
@Schema(description = "优惠券领取结果")
public enum AcquireCouponResult implements DcEnum {

    UNKNOWN(999, "未知"),
    ALREADY_ACQUIRE(1, "账号已领取过"),
    QUANTITY_ACCOUNT_LIMIT_REACHED(2, "领券人数已满活动结束"),
    SUCCEED(3, "领取成功"),
    NON_NEW_GUEST(4, "非新客户"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    AcquireCouponResult(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static AcquireCouponResult valueOf(Object codeIn) {
        if (codeIn == null) {
            return AcquireCouponResult.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof AcquireCouponResult) {
            return (AcquireCouponResult) codeIn;
        } else if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (AcquireCouponResult type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return AcquireCouponResult.UNKNOWN;
    }

}
