package com.cdz360.biz.model.trading.yw.type;

public interface YwOrderOpType {
    int YW_ORDER_UNKNOWN = 0; // 未知
    int YW_ORDER_CREATE = 1; // 运维工单创建
    int YW_ORDER_RECEIVED = 2; // 接收运维工单
    int YW_ORDER_START_PROCESS = 3; // 开始处理运维工单
    int YW_ORDER_UPDATE_PROCESS = 4; // 更新运维工单
    int YW_ORDER_TRANSFER = 5; // 运维工单转派
    int YW_ORDER_CHECK = 6; // 运维工单质检
    int YW_ORDER_SUSPEND = 7; // 运维工单挂起
    int YW_ORDER_DELETED = 8; // 运维工单删除
}