package com.cdz360.biz.model.trading.profit.sett.dto;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "场站收益统计数据")
@Data
@Accessors(chain = true)
public class BiSysSettJobBillDto {

    @Schema(description = "场站ID")
    @JsonInclude(Include.NON_NULL)
    private String siteId;

    // ========================= 收入项 =====================================
    @Schema(description = "运营收入，单位: 元(充电收入: elecFee + servFee)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal income;

    @Schema(description = "平台电量(充电订单电量)，通过订单上传时间直接汇总所得,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal orderElec;

    // ========================= 支出项 =====================================
    @Schema(description = "总支出金额,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal fee;

    @Schema(description = "电费支出金额,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal elecFee;

    @Schema(description = "租金支出金额,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal rentFee;

    @Schema(description = "分成支出金额,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal divideFee;

    @Schema(description = "劳务支出金额,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal laborFee;

    @Schema(description = "引流支出金额,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal attractFee;

    @Schema(description = "运维支出金额,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal opsFee;

    @Schema(description = "折旧支出金额,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal depreciationFee;

    @Schema(description = "其他支出金额,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal otherFee;

    // ========================== 场站汇总信息 ==========================
    @Schema(description = "功率利用率，单位: %, 2位小数", example = "98.76")
    private BigDecimal powerUseRate;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
