package com.cdz360.biz.model.trading.site.po;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Schema(description = "常规检查项")
public class EvseReport {

    @Schema(description = "充电桩、终端、安全指示牌及电价公示牌等是否正常")
    private Base isSignBoardNormal;

    @Schema(description = "整机外表面平整、无明显锈蚀、起泡、划伤等不良现象，封堵齐全")
    private Base isEvseAppearanceNormal;

    @Schema(description = "枪线无变形弯曲、破皮、枪锁功能正常")
    private Base isPlugLineNormal;

    @Schema(description = "充电桩分流器、接触器无烧蚀老化痕迹")
    private Base isEvseShuntNormal;

    @Schema(description = "机箱内清洁无杂物")
    private Base isInternalClean;

    @Schema(description = "散热正常")
    private Base isCoolingNormal;

    @Schema(description = "机柜前门、后门开关自如、锁紧可靠")
    private Base isDoorLockNormal;

    @Schema(description = "远程功能开启是否正常，后台监控无离线情况")
    private Base isRemoteFeaturesNormal;

    @Schema(description = "电价参数一级充电时电价是否正常")
    private Base isElectrovalenceNormal;

    @Schema(description = "充电记录无丢失及异常故障代码")
    private Base isRechargeRecordNormal;

    @Schema(description = "抽查检测APP、微信扫码、刷卡充电，充电桩能正常响应")
    private Base isWorksProperly;

    @Data
    @Accessors(chain = true)
    public static class Base {
        private Boolean normal;

        private List<String> evseNoList;

        private String remark;
    }

    public List<String> mergeAllEvseNo() {
        List<String> allEvseNo = new ArrayList<>();
        allEvseNo.addAll(this.getIsSignBoardNormal().getEvseNoList());
        allEvseNo.addAll(this.getIsEvseAppearanceNormal().getEvseNoList());
        allEvseNo.addAll(this.getIsPlugLineNormal().getEvseNoList());
        allEvseNo.addAll(this.getIsEvseShuntNormal().getEvseNoList());
        allEvseNo.addAll(this.getIsInternalClean().getEvseNoList());
        allEvseNo.addAll(this.getIsCoolingNormal().getEvseNoList());
        allEvseNo.addAll(this.getIsDoorLockNormal().getEvseNoList());
        allEvseNo.addAll(this.getIsRemoteFeaturesNormal().getEvseNoList());
        allEvseNo.addAll(this.getIsElectrovalenceNormal().getEvseNoList());
        allEvseNo.addAll(this.getIsRechargeRecordNormal().getEvseNoList());
        allEvseNo.addAll(this.getIsWorksProperly().getEvseNoList());
        return allEvseNo;
    }

    /**
     * 填充前缀-桩名称
     * @param evseMap {@code <evseNo, evseName>}
     */
    public void fillEvseName(Map<String, String> evseMap) {

        if (CollectionUtils.isNotEmpty(this.getIsSignBoardNormal().getEvseNoList())) {
            this.getIsSignBoardNormal().setEvseNoList(
                    this.getIsSignBoardNormal().getEvseNoList().stream().map(e -> {
                        String name = evseMap.get(e);
                        if (StringUtils.isNotBlank(name)) {
                            e = name.concat(" ").concat(e);
                        }
                        return e;
                    }).collect(Collectors.toList())
            );
        }
        if (CollectionUtils.isNotEmpty(this.getIsEvseAppearanceNormal().getEvseNoList())) {
            this.getIsEvseAppearanceNormal().setEvseNoList(
                    this.getIsEvseAppearanceNormal().getEvseNoList().stream().map(e -> {
                        String name = evseMap.get(e);
                        if (StringUtils.isNotBlank(name)) {
                            e = name.concat(" ").concat(e);
                        }
                        return e;
                    }).collect(Collectors.toList())
            );
        }
        if (CollectionUtils.isNotEmpty(this.getIsPlugLineNormal().getEvseNoList())) {
            this.getIsPlugLineNormal().setEvseNoList(
                    this.getIsPlugLineNormal().getEvseNoList().stream().map(e -> {
                        String name = evseMap.get(e);
                        if (StringUtils.isNotBlank(name)) {
                            e = name.concat(" ").concat(e);
                        }
                        return e;
                    }).collect(Collectors.toList())
            );
        }
        if (CollectionUtils.isNotEmpty(this.getIsEvseShuntNormal().getEvseNoList())) {
            this.getIsEvseShuntNormal().setEvseNoList(
                    this.getIsEvseShuntNormal().getEvseNoList().stream().map(e -> {
                        String name = evseMap.get(e);
                        if (StringUtils.isNotBlank(name)) {
                            e = name.concat(" ").concat(e);
                        }
                        return e;
                    }).collect(Collectors.toList())
            );
        }
        if (CollectionUtils.isNotEmpty(this.getIsInternalClean().getEvseNoList())) {
            this.getIsInternalClean().setEvseNoList(
                    this.getIsInternalClean().getEvseNoList().stream().map(e -> {
                        String name = evseMap.get(e);
                        if (StringUtils.isNotBlank(name)) {
                            e = name.concat(" ").concat(e);
                        }
                        return e;
                    }).collect(Collectors.toList())
            );
        }
        if (CollectionUtils.isNotEmpty(this.getIsCoolingNormal().getEvseNoList())) {
            this.getIsCoolingNormal().setEvseNoList(
                    this.getIsCoolingNormal().getEvseNoList().stream().map(e -> {
                        String name = evseMap.get(e);
                        if (StringUtils.isNotBlank(name)) {
                            e = name.concat(" ").concat(e);
                        }
                        return e;
                    }).collect(Collectors.toList())
            );
        }
        if (CollectionUtils.isNotEmpty(this.getIsDoorLockNormal().getEvseNoList())) {
            this.getIsDoorLockNormal().setEvseNoList(
                    this.getIsDoorLockNormal().getEvseNoList().stream().map(e -> {
                        String name = evseMap.get(e);
                        if (StringUtils.isNotBlank(name)) {
                            e = name.concat(" ").concat(e);
                        }
                        return e;
                    }).collect(Collectors.toList())
            );
        }
        if (CollectionUtils.isNotEmpty(this.getIsRemoteFeaturesNormal().getEvseNoList())) {
            this.getIsRemoteFeaturesNormal().setEvseNoList(
                    this.getIsRemoteFeaturesNormal().getEvseNoList().stream().map(e -> {
                        String name = evseMap.get(e);
                        if (StringUtils.isNotBlank(name)) {
                            e = name.concat(" ").concat(e);
                        }
                        return e;
                    }).collect(Collectors.toList())
            );
        }
        if (CollectionUtils.isNotEmpty(this.getIsElectrovalenceNormal().getEvseNoList())) {
            this.getIsElectrovalenceNormal().setEvseNoList(
                    this.getIsElectrovalenceNormal().getEvseNoList().stream().map(e -> {
                        String name = evseMap.get(e);
                        if (StringUtils.isNotBlank(name)) {
                            e = name.concat(" ").concat(e);
                        }
                        return e;
                    }).collect(Collectors.toList())
            );
        }
        if (CollectionUtils.isNotEmpty(this.getIsRechargeRecordNormal().getEvseNoList())) {
            this.getIsRechargeRecordNormal().setEvseNoList(
                    this.getIsRechargeRecordNormal().getEvseNoList().stream().map(e -> {
                        String name = evseMap.get(e);
                        if (StringUtils.isNotBlank(name)) {
                            e = name.concat(" ").concat(e);
                        }
                        return e;
                    }).collect(Collectors.toList())
            );
        }
        if (CollectionUtils.isNotEmpty(this.getIsWorksProperly().getEvseNoList())) {
            this.getIsWorksProperly().setEvseNoList(
                    this.getIsWorksProperly().getEvseNoList().stream().map(e -> {
                        String name = evseMap.get(e);
                        if (StringUtils.isNotBlank(name)) {
                            e = name.concat(" ").concat(e);
                        }
                        return e;
                    }).collect(Collectors.toList())
            );
        }
    }
}

