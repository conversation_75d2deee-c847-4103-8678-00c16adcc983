package com.cdz360.biz.model.trading.sim.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "SIM卡列表")
@Data
@Accessors(chain = true)
public class SimExportVo implements Serializable {

    @ExcelField(title = "ICCID", sort = 1)
    @Schema(description = "SIM卡卡号")
    private String iccid;

    @ExcelField(title = "MSISDN", sort = 2)
    @Schema(description = "移动台国际ISDN号码")
    private String msisdn;

    @ExcelField(title = "运营商", sort = 3)
    @Schema(description = "运营商名称")
    private String vendorName;

    @ExcelField(title = "IMSI", sort = 4)
    @Schema(description = "国际移动用户识别码")
    private String imsi;

    @ExcelField(title = "IMEI", sort = 5)
    @Schema(description = "移动设备国际识别码")
    private String imei;

    @ExcelField(title = "在线状态", sort = 6)
    @Schema(description = "在线状态（1：在线；2：离线；）")
    private String statusName;

    @ExcelField(title = "卡状态", sort = 7)
    private String cardStatus;

    @ExcelField(title = "首次激活日期", sort = 8)
    @Schema(description = "首次激活日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date activatedDate;

    @ExcelField(title = "当月用量(KB)", sort = 9)
    @Schema(description = "当月用量（单位：KB）")
    private BigDecimal usage;

    @ExcelField(title = "站点名称", sort = 10)
    @Schema(description = "站点名称")
    private String siteName;

    @ExcelField(title = "桩编号", sort = 11)
    @Schema(description = "桩编号")
    private String evseNo;

    @ExcelField(title = "备注", sort = 11)
    @Schema(description = "备注")
    private String remark;

    @ExcelField(title = "更新时间", sort = 12)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
