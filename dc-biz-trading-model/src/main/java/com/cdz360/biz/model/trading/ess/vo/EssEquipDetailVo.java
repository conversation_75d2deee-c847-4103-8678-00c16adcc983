package com.cdz360.biz.model.trading.ess.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "储能设备信息")
@Data
@Accessors(chain = true)
public class EssEquipDetailVo {

    @Schema(description = "采样点时间", example = "01:00")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private LocalDateTime time;

    @Schema(description = "SOC 0.1%")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal soc;

    @Schema(description = "电池总电压")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal totalBatteryV;

    @Schema(description = "最高单体温度")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal maxBatteryTemp;

    @Schema(description = "总交流有功功率 kW")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal acActivePowerTotal;

    @Schema(description = "总交流无功功率 kVAR")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal acReactivePowerTotal;

    @Schema(description = "总交流视在功率 kVAR")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal acApparentPowerTotal;

}
