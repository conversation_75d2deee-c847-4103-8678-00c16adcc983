package com.cdz360.biz.model.trading.site.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * BiSiteSumPo
 *
 * @since 3/23/2020 1:34 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BiSiteElectPo {

    /**
     * 总电量
     */
    private BigDecimal electricity = BigDecimal.ZERO;
    /**
     * 尖
     */
    private BigDecimal elecTag1 = BigDecimal.ZERO;
    /**
     * 峰
     */
    private BigDecimal elecTag2 = BigDecimal.ZERO;
    /**
     * 平
     */
    private BigDecimal elecTag3 = BigDecimal.ZERO;
    /**
     * 谷
     */
    private BigDecimal elecTag4 = BigDecimal.ZERO;

    /**
     * 总费用
     */
    private BigDecimal fee = BigDecimal.ZERO;

    /**
     * 总电费
     */
    private BigDecimal electFee = BigDecimal.ZERO;

    /**
     * 总服务费
     */
    private BigDecimal serveFee = BigDecimal.ZERO;

    private String name;

    /**
     * 企业ID
     */
    private  Long corpId;

    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date day;

    @JsonFormat(pattern = "yyyy-MM", locale = "zh", timezone = "GMT+8")
    private Date month;


}