package com.cdz360.biz.model.trading.hlht.param;

import com.cdz360.base.model.base.param.BaseListParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "互联站点列表入参")
@EqualsAndHashCode(callSuper = true)
public class HlhtSiteParam extends BaseListParam {

    private List<Long> hlhtSiteId;

    private Long commId;

//    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
//    private List<Long> commIdList;

    @Schema(description = "绑定商户，精确")
    private String commName;

    @Schema(description = "站点编号，模糊")
    private String siteId;

    @Schema(description = "站点名称，模糊")
    private String siteName;

    @Schema(description = "互联运营商")
    private String partnerCode;

    @Schema(description = "互联运营商名称，模糊")
    private String partnerName;

    @Schema(description = "设备所属运营商")
    private String evseOwnerCode;

    @Schema(description = "设备所属运营商名称，模糊")
    private String evseOwnerName;
}
