package com.cdz360.biz.model.trading.bi.vo;

import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.biz.model.annotations.ExcelField;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "场站电表抄表数据统计")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SiteMeterDataBiVo extends BaseObject implements Serializable {

    @ExcelField(title = "场站ID", sort = 1)
    @Schema(description = "场站ID")
    private String siteId;

    @ExcelField(title = "场站名称", sort = 2)
    @Schema(description = "场站名称")
    private String siteName;

    @ExcelField(title = "场站编号", sort = 3)
    @Schema(description = "场站编号")
    private String siteNo;

    @ExcelField(title = "月份", sort = 4)
    @Schema(description = "月份")
    private String monthDate;

    @ExcelField(title = "省/市/区", sort = 5)
    @Schema(description = "省/市/区")
    private String address;

    @ExcelField(title = "装机功率", digits = 0, sort = 6)
    @Schema(description = "场站总功率，所有桩功率总和")
    private BigDecimal totalPower;

    @ExcelField(title = "订单电量kWh", digits = 4, sort = 7)
    @Schema(description = "订单电量kWh")
    private BigDecimal orderElec;

    @ExcelField(title = "电损%", digits = 1, sort = 8)
    @Schema(description = "电损%")
    private String discount;

    @ExcelField(title = "抄表电量kWh", sort = 9)
    @Schema(description = "抄表电量kWh")
    private BigDecimal meterElec;

    @ExcelField(title = "尖电量kWh", sort = 10)
    @Schema(description = "尖电量kWh")
    private BigDecimal sharpPeakElec;

    @ExcelField(title = "峰电量kWh", sort = 11)
    @Schema(description = "峰电量kWh")
    private BigDecimal peakElec;

    @ExcelField(title = "平电量kWh", sort = 12)
    @Schema(description = "平电量kWh")
    private BigDecimal offPeakElec;

    @ExcelField(title = "谷电量kWh", sort = 13)
    @Schema(description = "谷电量kWh")
    private BigDecimal valleyElec;

}
