package com.cdz360.biz.model.trading.hlht.param;

import com.cdz360.biz.model.trading.hlht.po.PartnerSitePo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class AddAccountParam {

    private Long partnerId;

    private Long corpId;

    private Long corpTopCommId;

    private String corpName;

    private Long corpUserId;

    private List<PartnerSitePo> partnerSitePoList;
}
