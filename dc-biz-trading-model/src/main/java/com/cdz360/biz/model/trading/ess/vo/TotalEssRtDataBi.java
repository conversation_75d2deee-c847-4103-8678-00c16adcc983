package com.cdz360.biz.model.trading.ess.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class TotalEssRtDataBi {

    @Schema(description = "当天统计数据")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EssDataBi today;

    @Schema(description = "昨天统计数据")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EssDataBi yesterday;

    @Schema(description = "当月统计数据")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EssDataBi month;

    @Schema(description = "总数居")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EssDataBi total;
}
