package com.cdz360.biz.model.trading.invoice.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "企业开票记录和订单关联数据查询")
public class ListInvoiceRecordOrderRefParam extends BaseListParam {

    @Schema(description = "企业申请开票记录单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String applyNo;

    @Schema(description = "关联订单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> orderNoList;
}
