package com.cdz360.biz.model.trading.ess.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "储能站汇总信息")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class TotalEssDataBi extends TotalEssRtDataBi {

    @Schema(description = "储能站数量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long essSiteCnt;

    @Schema(description = "总装机容量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal essTotalCapacity;

    @Schema(description = "总装机功率")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal essTotalPower;

    @Schema(description = "储能ESS数量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long essCnt;

    public TotalEssDataBi() {

    }

    public TotalEssDataBi(boolean init) {
        if (init) {
            EssDataBi bi = new EssDataBi();
            this.setToday(bi)
                .setYesterday(bi)
                .setMonth(bi)
                .setTotal(bi);
        }
    }
}
