package com.cdz360.biz.model.trading.coupon.vo;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobVo;
import com.cdz360.biz.model.trading.soc.vo.SocCorpUserVo;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * CouponDictVo
 *
 * @since 7/29/2020 1:12 PM
 * <AUTHOR>
 */
@Data
@ToString(callSuper = true)
@Accessors(chain = true)
public class ChangeVo {
    /**
     * 保留站点的券模板
     */
    List<CouponDictVo> enableCouponTemplate;

    /**
     * 删除站点的券模板
     */
    List<CouponDictVo> unEnableCouponTemplate;

    /**
     * 删除站点后作废的券模板
     */
    List<CouponDictVo> invalidCouponTemplate;

    /**
     * 券模板变更停用的活动
     */
    List<ActivityVo> unEnableActivity;

    /**
     * 站点保留定时任务
     */
    List<SiteChargeJobVo> siteChargeJob;

    /**
     * 配置保留的充电限制企业人数
     */
    List<SocCorpUserVo> enableChargeLimitCorp;

    /**
     * 配置删除的充电限制企业
     */
    List<SocCorpUserVo> unEnableChargeLimitCorp;

    /**
     * 0, 禁用; 1, 个人基本账户; 2, 集团授权账户; 3, 商户专属账户; 999, 启动时选择
     */
    PayAccountType defaultPayType;

    /**
     * defaultPayType=1时t_user.id; defaultPayType=2时为t_r_bloc_user.id; defaultPayType=3时为t_comm_cus_ref.id;
     */
    Long payAccountId;

    /**
     * 可用无卡结算账户
     */
    UserInfoVo enableNoCardUserInfo;

    /**
     * 不可用无卡结算账户
     */
    UserInfoVo unEnableNoCardUserInfo;
}