package com.cdz360.biz.model.trading.coupon.param;

import com.cdz360.biz.model.trading.coupon.po.CouponPo;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.lang.Nullable;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CouponParam extends CouponPo {

    /**
     * 筛选券有效期
     */
    @Nullable
    private Date date;

    /**
     * 订单服务费，筛选券使用条件金额
     */
    @Nullable
    private BigDecimal servicePrice;

    /**
     * 筛选券有效场站
     */
    @Nullable
    private List<String> siteList;

    /**
     * 券模板对应商户 (订单支付账户为商户会员时，需限定商户)
     */
    @Nullable
    private Long couponDictCommId;

    /**
     * 订单金额
     */
    private BigDecimal orderPrice;

    /**
     * 必须包含的券id，sql查询的级别最高
     */
//    private Long includedCouponId;
}
