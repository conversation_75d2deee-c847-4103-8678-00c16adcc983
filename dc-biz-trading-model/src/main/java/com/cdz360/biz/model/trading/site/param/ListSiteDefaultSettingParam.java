package com.cdz360.biz.model.trading.site.param;

import com.cdz360.base.model.base.param.BaseListParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class ListSiteDefaultSettingParam extends BaseListParam {
    private Long id;
    /**
     * 场站编号
     */
    private String siteId;
    /**
     * 计费模板id
     */
    private Long chargeId;
    /**
     * 管理员密码
     */
    private String adminPassword;
    /**
     * 二级管理员密码
     */
    private String level2Password;
    /**
     * 白天音量
     */
    private Integer dayVolume;
    /**
     * 夜晚音量
     */
    private Integer nightVolume;
    /**
     * 二维码url
     */
    private String url;
    /**
     * 是否支持充电记录查询 （1是0否）
     */
    private Boolean isQueryChargeRecord;
    /**
     * 是否支持充电记录查询 （1是0否）
     */
    private Boolean isTimedCharge;
    /**
     * 是否支持无卡充电 （1是0否）
     */
    private Boolean isNoCardCharge;
    /**
     * 是否支持扫码充电 （1是0否）
     */
    private Boolean isScanCharge;
    /**
     * 是否支持Vin码充电 （1是0否）
     */
    private Boolean isVinCharge;
    /**
     * 是否支持刷卡充电 （1是0否）
     */
    private Boolean isCardCharge;
    /**
     * 是否支持定额电量充电 （1是0否）
     */
    private Boolean isQuotaEleCharge;
    /**
     * 是否支持固定金额充电 （1是0否）
     */
    private Boolean isQuotaMoneyCharge;
    /**
     * 是否支持固定时长充电 （1是0否）
     */
    private Boolean isQuotaTimeCharge;
    /**
     * 国际协议
     */
    private String internationalAgreement;
    /**
     * 自动停充 （1是0否）
     */
    private Integer isAutoStopCharge;
    /**
     * 均/轮充设置 0均充 1轮充
     */
    private Integer avgOrTurnCharge;
    /**
     * 合充开关 （1开0关）
     */
    private Integer isCombineCharge;
    /**
     * 1是0否
     */
    private Integer isUseDefaultSetting;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 操作人id
     */
    private Long updateByUserid;
}
