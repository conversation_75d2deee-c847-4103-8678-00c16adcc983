package com.cdz360.biz.model.trading.meter.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * SiteMeterVO场站和电表的对应关系
 *
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@Schema(description = "场站-电表绑定关系Vo")
public class SiteMeterVo {

    private String siteId;
    private List<MeterEvseVo> meterEvseList;
}