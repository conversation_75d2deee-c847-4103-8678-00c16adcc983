package com.cdz360.biz.model.trading.iot.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "场站某天的数据")
@Accessors(chain = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class DaySitePvRtDataBi extends SitePvRtDataBi {

    @Schema(description = "日期")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date date;

    @Schema(description = "额定功率")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long totalPower;

    @Schema(description = "等效时长")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal equivalentDuration;

    @Schema(description = "按照月份统计每月第一天")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date firstDayOfMonth;

    @Schema(description = "发电收益")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal totalProfit;
}
