package com.cdz360.biz.model.trading.coupon.param;

import com.cdz360.biz.model.trading.coupon.type.ActivityType;
import com.cdz360.biz.model.trading.coupon.type.CouponSendType;
import com.cdz360.biz.model.trading.coupon.vo.ChargeVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * CreateActivityParam
 *
 * @since 7/30/2020 3:00 PM
 * <AUTHOR>
 */
@Data
public class CreateActivityParam {
    @Schema(description = "活动名称")
    private String name;

    @Schema(description = "活动类型")
    private ActivityType type;

    private Long commId;

    @Schema(description = "关联券模板列表")
    private List<Long> couponDictIdList;

    @Schema(description = "单次领券数")
    private Integer acquireCount;

    @Schema(description = "领券人数")
    private Integer quantityAccount;

    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date timeFrom;

    @Schema(description = "开始结束")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date timeTo;

    @Schema(description = "活动图片列表")
    private List<String> imageUrlList;

    @Schema(description = "备注")
    private String comment;

    @Schema(description = "满减活动，满减信息")
    private List<ChargeVo> chargeList;

    @Schema(description = "满减活动，适用账户")
    private Long accountType;

    @Schema(description = "发券规则")
    private CouponSendType sendCouponRule;

    @Schema(description = "规则参数列表")
    private List<ActivityCouponRuleParam> ruleParamList;


}