package com.cdz360.biz.model.trading.order.vo;

import com.cdz360.biz.model.trading.order.po.ChargerOrderCarPo;
import com.cdz360.biz.model.trading.order.po.ChargerOrderPayPo;
import com.cdz360.biz.model.trading.order.po.ChargerOrderPo;
import com.cdz360.biz.model.trading.site.po.OvertimeParkFeeOrderPo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ChargerOrderUpdateData extends ChargerOrderData {

    private ChargerOrderPo orderUpdate;

    private ChargerOrderCarPo carUpdate;

    private ChargerOrderPayPo payUpdate;

    public ChargerOrderUpdateData() {
        super();
    }

    public ChargerOrderUpdateData(ChargerOrderPo order, ChargerOrderCarPo car, ChargerOrderPayPo pay) {
        super(order, car, pay);
        this.orderUpdate = new ChargerOrderPo(order.getOrderNo());
        this.payUpdate = new ChargerOrderPayPo(order.getOrderNo());
    }
    public ChargerOrderUpdateData(ChargerOrderPo order,
                                  ChargerOrderCarPo car,
                                  ChargerOrderPayPo pay,
                                  OvertimeParkFeeOrderPo parkOrder) {
        super(order, car, pay);
        this.orderUpdate = new ChargerOrderPo(order.getOrderNo());
        this.payUpdate = new ChargerOrderPayPo(order.getOrderNo());
        this.setParkOrder(parkOrder);
    }
}
