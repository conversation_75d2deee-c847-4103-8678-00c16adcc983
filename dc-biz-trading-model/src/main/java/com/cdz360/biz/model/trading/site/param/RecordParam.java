package com.cdz360.biz.model.trading.site.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.biz.model.trading.site.type.SiteInspectionStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "巡检工单列表查询参数")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class RecordParam extends BaseListParam {

    private String no;

    @Schema(description = "巡检人")
    private String rummager;

    private TimeFilter time;

    private SiteInspectionStatus status;

    @Schema(description = "巡检状态列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<SiteInspectionStatus> statusList;

    private String siteName;

    private Long commId;

    private String commIdChain;

    @Schema(description = "场站组id列表 仅用于管理后台相关接口")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> gids;

    @Schema(description = "巡检人ID列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> opUidList;

    private List<Long> qcUidList;

    @Schema(description = "巡检单类型")
    private Integer inspectionType;
}
