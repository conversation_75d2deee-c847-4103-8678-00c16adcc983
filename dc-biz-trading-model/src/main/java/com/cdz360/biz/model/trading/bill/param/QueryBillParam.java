package com.cdz360.biz.model.trading.bill.param;

import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "对账单查询参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class QueryBillParam extends BaseObject {
    @Data
    @Accessors(chain = true)
    @EqualsAndHashCode(callSuper = true)
    public static class ZftMchInfo extends BaseObject {
        @Schema(description = "直付商名称", required = true)
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String zftName;

        @Schema(description = "对账单ID(t_zft_daily_bill.id)", required = true)
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Long dailyBillId;

        @Schema(description = "微信支付(子)商户ID")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String mchId;

        @Schema(description = "包含文件后缀在内的完整路径 参数传递，文件上传使用", hidden = true)
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        @JsonIgnore
        private String filePath;

        @Override
        public String toJsonString() {
            return JsonUtils.toJsonString(this);
        }
    }

    @Data
    @Accessors(chain = true)
    @EqualsAndHashCode(callSuper = true)
    public static class CorpMchInfo extends BaseObject {
        @Schema(description = "企业客户ID")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Long corpId;

        @Schema(description = "企业客户名称")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String corpName;

        @Schema(description = "指定对账字段索引值 对账字段索引根据对账csv中确定(从0开始)", example = "3")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private int regexFieldIdx;

        @Schema(description = "匹配规则")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String regex;

        @Schema(description = "包含文件后缀在内的完整路径 参数传递，文件上传使用", hidden = true)
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        @JsonIgnore
        private String filePath;
    }

    @Schema(description = "顶级商户ID", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long topCommId;

    @Schema(description = "直付商名称", required = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String zftName;

    @Schema(description = "是否为服务商对账单", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @NotEmpty(message = "是否为服务商对账单")
    private Boolean provider;

    @Schema(description = "对账单ID(t_zft_daily_bill.id)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long dailyBillId;

//    @Schema(description = "直付商名称", required = true)
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String zftName;
//
//    @Schema(description = "对账单ID(t_zft_daily_bill.id)", required = true)
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Long dailyBillId;

    // START WX >>>>>
//    @Schema(description = "微信支付(子)商户ID")
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String wxSubMchId;

    @Schema(description = "微信支付(子)商户信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ZftMchInfo wxMchInfo;
    // END WX <<<<<

    // START ALI >>>>>
//    @Schema(description = "支付宝(子)商户ID")
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String aliMchId;

    @Schema(description = "提取单个")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean splitOne;

    @Schema(description = "支付宝(子)商户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<ZftMchInfo> aliMchInfoList;
    // END ALI <<<<<

    @Schema(description = "指定提取企业对账信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<CorpMchInfo> corpMchInfoList;

    @Schema(description = "对账单日期", example = "yyyy-MM-dd", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @NotEmpty(message = "对账单日期不能为空")
    private String billDate;

    @Override
    public String toJsonString() {
        return JsonUtils.toJsonString(this);
    }
}
