package com.cdz360.biz.model.trading.site.vo;

import com.cdz360.biz.model.trading.site.po.EvseCfgSchedulePo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@Schema(description = "计费模板定时下发信息")
public class EvseCfgScheduleVo extends EvseCfgSchedulePo {
    @Schema(description = "使用的计费模板名称")
    private String priceSchemeName;

    @Schema(description = "定时下发计费模板Code值")
    private String priceSchemeCode;
}
