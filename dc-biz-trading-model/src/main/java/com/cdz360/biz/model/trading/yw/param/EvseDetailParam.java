package com.cdz360.biz.model.trading.yw.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "运维工单查询桩信息参数")
@Data
@Accessors(chain = true)
public class EvseDetailParam {

    @Schema(required = false, description = "脱机场站时需必传")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "枪头编号列表 扫码入参",
            example = "0101234567899802")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> plugNoList;

    @Schema(description = "桩编号列表 这里直接传入桩编号，不是枪头编号",
            example = "012345678998")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> evseNoList;
}
