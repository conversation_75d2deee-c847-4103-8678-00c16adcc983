package com.cdz360.biz.model.trading.site.type;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "场站消费统计账户类型")
@Getter
public enum SiteOrderAccountType {
    UNKNOWN(0, "无结算账户"),

    PREPAY(4, "即充即退"),
    PERSONAL(1, "个人账户"),
    COMMERCIAL(3, "商户会员"),

    E_CNY(6, "数字人民币"),
    WX_CREDIT(7, "微信信用充"),
    ALIPAY_CREDIT(8, "支付宝信用充"),

    NORMAL_CORP(20, "普通企业客户"),
    HLHT_CORP(21, "互联企业客户"),

    OTHER(99, "其他");

    private int code;
    private String desc;

    SiteOrderAccountType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
