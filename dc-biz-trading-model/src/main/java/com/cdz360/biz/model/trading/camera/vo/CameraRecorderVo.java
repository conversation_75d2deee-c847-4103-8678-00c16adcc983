package com.cdz360.biz.model.trading.camera.vo;

import com.cdz360.biz.model.trading.camera.po.CameraRecorderPo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * CameraRecorderVo
 * 
 * @since 9/23/2021 5:27 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "硬盘录像机设备")
@EqualsAndHashCode(callSuper = true)
public class CameraRecorderVo extends CameraRecorderPo {
    private String siteId;
    private String siteName;
    private List<CameraVo> cameraList;
    @Schema(description = "有场站的具体id，忽略idChain，没有就用idChain")
    private List<String> siteIds;
}