package com.cdz360.biz.model.trading.iot.vo;

import com.cdz360.biz.model.iot.type.GtiVendor;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class EquipInfoInTimeVo {

    @Schema(description = "逆变器唯一编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dno;

    @Schema(description = "设备类型 目前值为: '光伏逆变器'")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String type;

    @Schema(description = "设备名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String name;

    @Schema(description = "品牌名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private GtiVendor vendor;

}
