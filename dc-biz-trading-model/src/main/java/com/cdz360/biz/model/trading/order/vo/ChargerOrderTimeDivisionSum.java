package com.cdz360.biz.model.trading.order.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @since 2019-03-11 16:47
 */
@Data
public class ChargerOrderTimeDivisionSum implements Serializable{

    protected static final long serialVersionUID = 1L;

    private Double tipElectricity;//尖时电量
    private Double tipServicePrice;//尖时服务费
    private Double tipElecPrice;//尖时电费
    private Double tipSumPrice;//尖时合计费用
    private BigDecimal tipElectricUnit;//尖时电费单价
    private BigDecimal tipServiceUnit;//尖时服务费单价
    private Double peakElectricity;//峰时电量
    private Double peakServicePrice;//峰时服务费
    private Double peakElecPrice;//峰时电费
    private Double peakSumPrice;//峰时合计费用
    private BigDecimal peakElectricUnit;//峰时电费单价
    private BigDecimal peakServiceUnit;//峰时服务费单价
    private Double flatElectricity;//平时电量
    private Double flatServicePrice;//平时服务费
    private Double flatElecPrice;//平时电费
    private Double flatSumPrice;//平时合计费用
    private BigDecimal flatElectricUnit;//平时电费单价
    private BigDecimal flatServiceUnit;//平时服务费单价
    private Double valleyElectricity;//谷时电量
    private Double valleyServicePrice;//谷时服务费
    private Double valleyElecPrice;//谷时电费
    private Double valleySumPrice;//谷时合计费用
    private BigDecimal valleyElectricUnit;//谷时电费单价
    private BigDecimal valleyServiceUnit;//谷时服务费单价
}
