package com.cdz360.biz.model.trading.site.vo;

import com.cdz360.biz.model.trading.site.po.SiteChargeJobLogPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Schema(description = "场站定时任务执行结果")
public class SiteChargeJobLogVo extends SiteChargeJobLogPo {

    @Schema(description = "枪头编号")
    private String plugNo;

    @Schema(description = "桩名称")
    private String boxName;

    @Schema(description = "枪名称")
    private String chargerName;

}
