package com.cdz360.biz.model.trading.comm.dto;

import com.cdz360.base.utils.JsonUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "商户的场站/桩/枪统计")
public class CommSiteEvseCount {

    @Schema(description = "场站数量", example = "123")
    private Long siteNum;

    @Schema(description = "桩数量", example = "123")
    private Long evseNum;

    @Schema(description = "枪头数量", example = "123")
    private Long plugNum;

    @Schema(description = "总功率", example = "123")
    private Long power;


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
