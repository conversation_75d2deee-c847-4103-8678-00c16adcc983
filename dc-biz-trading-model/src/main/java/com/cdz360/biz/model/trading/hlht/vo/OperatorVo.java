package com.cdz360.biz.model.trading.hlht.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OperatorVo {

    private Long id;

    private Long topCommId;

    @Schema(description = "运营商标识")
    private String operatorCode;

    @Schema(description = "运营商名称")
    private String operatorName;

    @Schema(description = "停充超时计费是否回传")
    private Boolean billingBack;

    @Schema(description = "标志位")
    private List<Integer> flags;

    @Schema(description = "运营商密钥")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String operatorSecret;

    @Schema(description = "消息密钥")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dataSecret;

    @Schema(description = "消息密钥初始化向量")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dataSecretIV;

    @Schema(description = "签名密钥")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String sigSecret;

    @Schema(description = "我方运营商标识")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String ourOperatorCode;

    @Schema(description = "我方运营商密钥")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String ourOperatorSecret;

    @Schema(description = "我方消息密钥")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String ourDataSecret;

    @Schema(description = "我方消息密钥初始化向量")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String ourDataSecretIV;

    @Schema(description = "我方签名密钥")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String ourSigSecret;

    @Schema(description = "结算账户个数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long accountCount;

    @Schema(description = "结算账户可用站点")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> siteNameList;

}
