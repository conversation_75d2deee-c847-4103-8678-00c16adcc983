package com.cdz360.biz.model.trading.order.param;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.ChargeOrderStatus;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.util.Assert;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class PrepaidOrderListParam extends OrderTimeFilterParam {

    public enum PageType {
        ADDIBLE, // 添加订单页
        ADDED, // 开票订单页
    }

    @Schema(description = "接口请求来源页")
    private PageType pageType;

    @Schema(description = "订单扣款账户类型")
    @JsonInclude(Include.NON_NULL)
    private PayAccountType accountType;

    @JsonInclude(Include.NON_NULL)
    private Long payAccountId;

    @JsonInclude(Include.NON_NULL)
    private Long userId;

    @JsonInclude(Include.NON_NULL)
    private Long corpId;

    @Schema(description = "车辆信息模糊查询关键字")
    @JsonInclude(Include.NON_NULL)
    private String carKeyword;

    @Schema(description = "订单号或手机号模糊查询")
    @JsonInclude(Include.NON_NULL)
    private String numberStr;

    @JsonInclude(Include.NON_NULL)
    private String siteId;

    @JsonInclude(Include.NON_NULL)
    private List<String> orderNoList;

    private String interimCode;

    private Boolean useInterimTable;

    private Boolean operateAll;

    private String applyNo;

    private Boolean append;

    @Schema(description = "无需前端传入", hidden = true)
    private Integer invoicedId;

    @JsonInclude(Include.NON_NULL)
    private List<String> excludeOrderNoList;

    @Schema(description = "无需前端传入", hidden = true)
    @JsonInclude(Include.NON_NULL)
    private Integer status;

    @Schema(description = "无需前端传入", hidden = true)
    @JsonInclude(Include.NON_NULL)
    private ChargeOrderStatus orderStatus;

    @Schema(description = "场站开票标识（无需前端传入）true: 可开票; false: 不可开票", hidden = true)
    @JsonInclude(Include.NON_NULL)
    private Boolean siteInvoicedValid;

    @Schema(description = "无需前端传入", hidden = true)
    @JsonInclude(Include.NON_NULL)
    private SettlementType excludeSettlementType;

    private String procInstId;

    @Schema(description = "是否联合查询（无需前端传入）", hidden = true)
    @JsonInclude(Include.NON_NULL)
    private Boolean unionQuery;

    public void paramCheckAndFilter() {
        Assert.notNull(pageType, "接口请求来源页不能为空");
//        if (PageType.ADDED.equals(pageType)) {
//            Assert.isTrue(CollectionUtils.isNotEmpty(orderNoList), "订单列表不能为空");
//        }

        Assert.notNull(accountType, "账户类型不能为空");
        switch (accountType) {
            case PERSONAL:
            case COMMERCIAL:
                Assert.isTrue(payAccountId != null && userId != null, "缺少必要参数");
                corpId = null;
                break;
            case CREDIT:
                Assert.isTrue(corpId != null, "缺少必要参数");
                payAccountId = null;
                userId = null;
                break;
            default:
                throw new DcArgumentException("暂不支持的账户类型");
        }
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
