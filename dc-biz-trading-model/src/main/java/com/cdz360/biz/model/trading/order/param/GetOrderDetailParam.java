package com.cdz360.biz.model.trading.order.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class GetOrderDetailParam {

    @Schema(description = "操作人商户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long commId;

    @Schema(description = "订单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderNo;

    @Schema(description = "商户id列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> commIdList;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commIdChain;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long userId;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> gids;
}
