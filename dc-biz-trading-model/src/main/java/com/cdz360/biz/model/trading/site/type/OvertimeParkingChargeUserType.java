//package com.cdz360.biz.model.trading.site.type;
//
//import com.cdz360.base.model.base.type.DcEnum;
//
///**
// * ParkingChargeUserType
// *
// * @since 1/21/2022 4:02 PM
// * <AUTHOR>
// */
//public enum OvertimeParkingChargeUserType implements DcEnum {
//    FREE(0), //免费
//    CHARGE(1)//收费
//    ;
//    private final int code;
//    OvertimeParkingChargeUserType(int code) {
//        this.code = code;
//    }
//
//    @Override
//    public int getCode() {
//        return this.code;
//    }
//}