package com.cdz360.biz.model.trading.iot.dto.huawei;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

/**
 * 无功调节模式
 */
@Getter
public enum ReactiveAdjustmentMode implements DcEnum {

    UNKNOWN(99, "未知"),
    POWER_FACTOR(0, "功率因数"),
    ABSOLUTE_VALUE(1, "绝对值"),
    Q_S(2, "Q/S"),
    Q_U_CHARACTERISTIC_CURVE(3, "Q-U特征曲线（指令ID填0）"),
    P_PN_CHARACTERISTIC_CURVE(4, "cosϕ-P/Pn特征曲线（指令ID填0）"),
    PF_U_CHARACTERISTIC_CURVE(5, "PF-U特征曲线（指令ID填0）"),
    Q_P_CHARACTERISTIC_CURVE(6, "Q-P特征曲线（指令ID填0）"),
    ;

    @JsonValue
    private final int code;
    private final String desc;

    ReactiveAdjustmentMode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @JsonCreator
    public static ReactiveAdjustmentMode valueOf(Object codeIn) {
        if (codeIn == null) {
            return ReactiveAdjustmentMode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof ReactiveAdjustmentMode) {
            return (ReactiveAdjustmentMode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (ReactiveAdjustmentMode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return ReactiveAdjustmentMode.UNKNOWN;
    }

}
