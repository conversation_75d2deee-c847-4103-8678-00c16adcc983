package com.cdz360.biz.model.trading.order.vo;

import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 充值记录统计
 *
 * <AUTHOR>
 * @since 2019/11/6 15:51
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode
@ToString
@Schema(description = "充值记录统计")
public class PayBillBi {
    @Schema(description = "记录数")
    private long num;

    @Schema(description = "充值类型: OUT_FLOW(充值), IN_FLOW(减少)", example = "IN_FLOW")
    private DepositFlowType flowType;

    @Schema(description = "完成数")
    private long completedNum;

    @Schema(description = "记录操作总额: 单位，元")
    private BigDecimal total;

    @Schema(description = "到账总额(含赠送金额): 单位，元")
    private BigDecimal arrivalTotal;

    @Schema(description = "实际总额: 单位，元")
    private BigDecimal costTotal;

    @Schema(description = "赠送总额: 单位，元")
    private BigDecimal freeTotal;
}
