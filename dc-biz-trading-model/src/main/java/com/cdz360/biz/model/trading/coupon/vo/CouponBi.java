package com.cdz360.biz.model.trading.coupon.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * CouponBi
 *
 * @since 7/31/2020 2:39 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CouponBi {

    @Schema(description = "单次领券数")
    private Integer acquireCount;

    @Schema(description = "领券人数")
    private Integer quantityAccount;

    @Schema(description = "已领券人数")
    private Integer alreadyAcquireNum;

    @Schema(description = "已领券数")
    private Integer alreadyAcquireCouponNum;

    @Schema(description = "已使用券数")
    private Integer usedCouponNum;

    @Schema(description = "未使用券数")
    private Integer unusedCouponNum;

    @Schema(description = "已过期券数")
    private Integer expiredCouponNum;

    @Schema(description = "不可用券数")
    private Integer disableCouponNum;
}