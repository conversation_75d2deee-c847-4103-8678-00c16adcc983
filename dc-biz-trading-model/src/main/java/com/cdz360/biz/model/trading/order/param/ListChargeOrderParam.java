package com.cdz360.biz.model.trading.order.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.model.charge.type.OrderAbnormalReason;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "查询充电订单列表参数")
public class ListChargeOrderParam extends BaseListParam {

    @Schema(description = "场站Id列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> siteIdList;

    @Schema(description = "充电结束时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter chargeStopTimeFilter;

    @Schema(description = "充电开始时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter chargeStartTimeFilter;

    @Schema(description = "订单创建时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter chargeCreateTimeFilter;

    @Schema(description = "以订单最后更新时间作为结算时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter chargeUpdateTimeFilter;

    @Schema(description = "平台（收到）结束充电的时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter stopTimeFilter;

    @Schema(description = "支付时间筛选")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter paymentTimeFilter;

    @Schema(description = "是否是互联互动通订单")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean isHlhtOrder;

    @Schema(description = "充电订单编号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String orderNo;

    @Schema(description = "充电订单编号列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> orderNoList;

    private String interimCode;

    @Schema(description = "省份code列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> provinceCodeList;

    @Schema(description = "城市code列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> cityCodeList;


    @Schema(description = "顶级商户Id(参数传递使用，不做查询条件)", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long topCommId;

    @Schema(description = "商户Id列表(commercial_id)")
    @Deprecated
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> commIdList;

    @Schema(description = "商户Id链")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commIdChain;

    @Schema(description = "用户Id(customer_id)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long cusId;

    @Schema(description = "充电订单状态(status)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Integer> statusList;

    @Schema(description = "充电订单: true -- 查询异常中的订单，false -- 查询正常的订单，null -- 所有订单" +
            "(仅作查询过滤，没有对应的字段)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean abnormal;

    @Schema(description = "充电订单异常类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<OrderAbnormalReason> abnormalList;

    /**
     * 小程序查询时是否排除orderStatus为Cancel的订单
     */
    @Schema(description = "小程序查询时是否排除orderStatus为Cancel的订单")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean excludeCancelOrder;

    @Schema(description = "是否在企业客户申请开票页面 不做前端入参", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean inCorpInvoice;

    @Schema(description = "企业客户申请开票记录的申请单号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String applyNo;

    @Schema(description = "企业客户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long corpId;

    @Schema(description = "搜索车架号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String vin;

    @Schema(description = "场站名称搜索关键词")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String skSiteName;


    @Schema(description = "小功率充电级别. 1,5分钟以上; 2,10分钟以上; 3,30分钟以上; 4,60分钟以上")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer lowKwLevel;

    @Schema(description = "车牌号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String carNo;

    @Schema(description = "车队")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String carDepart;

    @Schema(description = "车辆线路")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String lineNum;

    @Schema(description = "车辆自编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String carNum;

    @Schema(description = "结算类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private SettlementType settlementType;

    @Schema(description = "互联互通充电订单编号列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> hlhtOrderNoList;

    @Schema(description = "所属的场站组")
    private List<String> gids;

    @Schema(description = "充电结束时间开始")
    private Long chargeStopTimeStartTime;

    @Schema(description = "充电结束时间结束")
    private Long chargeStopTimeEndTime;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
