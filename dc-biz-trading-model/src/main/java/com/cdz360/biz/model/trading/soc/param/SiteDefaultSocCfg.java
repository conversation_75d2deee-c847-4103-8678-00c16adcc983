package com.cdz360.biz.model.trading.soc.param;

import com.cdz360.biz.model.trading.soc.po.SiteSocCfgPo;
import com.cdz360.biz.model.trading.soc.po.SiteSocStrategyPo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * SiteDefaultSocCfg
 *
 * @since 8/10/2020 1:56 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "场站soc限制-默认策略")
public class SiteDefaultSocCfg {
    @Deprecated
    @Schema(description = "默认策略")
    private SiteSocCfgPo siteSocCfg;
    @Schema(description = "时间列表")
    private List<SiteSocStrategyPo> timeList;
}