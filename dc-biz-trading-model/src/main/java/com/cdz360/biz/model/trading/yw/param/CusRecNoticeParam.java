package com.cdz360.biz.model.trading.yw.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "推送给C端用户运维结果后前端反馈")
@Data
@Accessors(chain = true)
public class CusRecNoticeParam {

    @Schema(description = "运维工单编号列表", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> ywOrderNoList;

    @Schema(description = "用户ID", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long cusId;

}
