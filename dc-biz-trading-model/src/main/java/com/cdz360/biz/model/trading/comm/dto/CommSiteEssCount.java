package com.cdz360.biz.model.trading.comm.dto;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

@Data
@Schema(description = "商户的场站/储能设备数/功率统计")
public class CommSiteEssCount {
    @Schema(description = "场站数量", example = "123")
    private Long siteNum;

    @Schema(name = "总功率,单位kw")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal power;

    @Schema(name = "总容量,单位kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal capacity;



    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
