package com.cdz360.biz.model.trading.meter.po;

import com.cdz360.base.model.iot.type.NetType;
import com.cdz360.biz.model.iot.type.GtiVendor;
import com.cdz360.biz.model.trading.meter.type.MeterStatusType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "电表")
public class MeterPo {

    @NotNull(message = "id 不能为 null")
    private Long id;

    @NotNull(message = "dno 不能为 null")
    @Size(max = 20, message = "电表唯一编号 长度不能超过 20")
    private String dno;

    @NotNull(message = "no 不能为 null")
    @Size(max = 20, message = "no 长度不能超过 20")
    private String no;

    private String siteId;

    @Schema(description = "名称")
    @NotNull(message = "name 不能为 null")
    @Size(max = 100, message = "name 长度不能超过 100")
    private String name;

    @NotNull(message = "status 不能为 null")
    private MeterStatusType status;

    @Schema(description = "网络类型")
    private NetType net;

    @Schema(description = "品牌名称")
    private GtiVendor vendor;

    @Schema(description = "是否计量其他大功率用电设备")
    @NotNull(message = "otherDevice 不能为 null")
    private Boolean otherDevice;

    @Schema(description = "注释")
    @NotNull(message = "comment 不能为 null")
    @Size(max = 255, message = "comment 长度不能超过 255")
    private String comment;

    @Schema(description = "网关编号")
    private String gwno;

    @Schema(description = "通信协议")
    private String protocol;

    @Schema(description = "串口通信(485/modbus) ID 1~247")
    private Integer sid;

    @Schema(description = "是否计算电损，1启用 0禁用")
    private Boolean powerLoss;

    private Date createTime;

    private Date updateTime;


}
