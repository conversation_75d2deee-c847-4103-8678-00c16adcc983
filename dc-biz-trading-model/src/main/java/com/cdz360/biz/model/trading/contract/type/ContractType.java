package com.cdz360.biz.model.trading.contract.type;

import lombok.Getter;

/**
 * 合同类型
 */
@Getter
public enum ContractType {

    CON_C1(1, "场地租赁合约"),

    CON_C2(2, "施工合约"),

    CON_C3(3, "EPC总包"),

    CON_C4(4, "销售合约"),

    CON_C5(5, "设备维保合约"),

    CON_C6(6, "平台服务合约"),

    CON_C12(12, "互联互通对接合约"), // 放到这边是前端排序要求

    CON_C7(7, "售后服务合约"),

    CON_C8(8, "场站合作运营合约"),

    CON_C9(9, "收购合约"),

    CON_C10(10, "BOT合约"),

    CON_C11(11, "设备租赁合约"),
    CON_CORP_C13(13, "企业客户合约"),

    CON_C0(0, "其他");

    private final int code;
    private final String desc;

    ContractType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ContractType valueOf(int code) {
        for (ContractType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return ContractType.CON_C0;
    }

}
