package com.cdz360.biz.model.trading.ess.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "查询储能设备信息参数")
@Data
@Accessors(chain = true)
public class EssEquipDetailParam {

    @Schema(description = "云端给ESS分配的唯一编号", required = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String essDno;

    @Schema(description = "设备ID(ess内唯一)", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long equipId;
}
