package com.cdz360.biz.model.trading.bi.param;

import com.cdz360.base.model.base.param.BaseListParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListBiVINParam extends BaseListParam {

    @Schema(description = "VIN 查询,支持模糊查询")
    private String vin;

    @Schema(description = "车牌号查询,支持模糊查询")
    private String carNo;

    @Schema(description = "车队名称,支持模糊查询")
    private String carDepart;

    @Schema(description = "线路,支持模糊查询")
    private String carLineNum;

    @Schema(description = "车辆自编号,支持模糊查询")
    private String carNum;

    @Schema(description = "客户名称,支持模糊查询")
    private String cusName;

    @Schema(description = "客户手机号,支持模糊查询")
    private String cusPhone;

    @Schema(description = "企业客户名称,支持模糊查询")
    private String corpName;

}
