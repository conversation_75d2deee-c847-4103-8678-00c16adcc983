package com.cdz360.biz.model.trading.site.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "结算任务统计功率利用率")
@Data
@Accessors(chain = true)
public class SettJobPowerUseRateVo {

    @Schema(description = "场站Id")
    @JsonInclude(Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "场站总充电量,单位: kW·h, 4位小数")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal elec;

    @Schema(description = "场站总有效功率,单位: W")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal power;
}
