package com.cdz360.biz.model.trading.meter.param;

import com.cdz360.base.model.base.param.TimeFilter2;
import com.cdz360.biz.model.common.param.BaseListParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * MeterDataListParam电表抄表数据参数
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class MeterDataListParam extends BaseListParam {

    @Schema(description = "场站ID列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> siteIdList;

    @Schema(description = "场站组ID列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> gidList;

    private String idChain;

    @Schema(description = "开始时间过滤")
    @JsonInclude(Include.NON_NULL)
    private TimeFilter2 startTimeFilter;
}