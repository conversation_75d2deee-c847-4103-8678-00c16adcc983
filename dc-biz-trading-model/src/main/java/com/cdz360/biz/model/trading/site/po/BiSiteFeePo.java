package com.cdz360.biz.model.trading.site.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * BiSiteSumPo
 *
 * @since 3/23/2020 1:34 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BiSiteFeePo {

    /**
     * 总金额
     */
    private BigDecimal fee = BigDecimal.ZERO;
    /**
     * 服务费
     */
    private BigDecimal servFee = BigDecimal.ZERO;
    /**
     * 电费总金额
     */
    private BigDecimal elecFee = BigDecimal.ZERO;

    private String name;

    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date day;

    @JsonFormat(pattern = "yyyy-MM", locale = "zh", timezone = "GMT+8")
    private Date month;


}