package com.cdz360.biz.model.trading.soc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * SocAlgorithmVo
 *  soc限制规则
 * @since 8/11/2020 4:43 PM
 * <AUTHOR>
 */
@Data
public class SocAlgorithmVo {

    private Integer startTime;

    private Integer endTime;

    private Integer soc;

    @Schema(description = "true: SOC允许充电;false: SOC限制充电")
    private Boolean allow;
}