package com.cdz360.biz.model.trading.profit.sett.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "数据汇总结算公式")
@Data
@Accessors(chain = true)
public class BiRule {

    @Schema(description = "基本计算公式")
    private CalculateRuleBase base;

    @Schema(description = "特殊区间计算公式")
    private List<CalculateConditionRule> range;
}
