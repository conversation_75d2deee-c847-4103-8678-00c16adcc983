package com.cdz360.biz.model.trading.site.vo;

import com.cdz360.biz.model.trading.site.po.SitePo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "场站信息(携带配置项)")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SiteWithConfVo extends SitePo {

    @Schema(description = "停车满减电量,入参为0将修改数据库字段为null，入参为null将不做修改")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer parkCouponKwh;

    @Schema(description = "满电量减停车分钟,入参为0将修改数据库字段为null，入参为null将不做修改")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer parkCouponTime;

}
