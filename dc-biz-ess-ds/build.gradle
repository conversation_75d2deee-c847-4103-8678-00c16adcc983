plugins {
    id 'java'
}

bootJar {
    enabled = false
}

jar {
    enabled = true
}
sourceSets {
    main {
        resources {
            srcDirs "src/main/java"
        }
    }
}
test {
    include 'com/cdz360/biz/ds/trading/**'

}

dependencies {
    implementation project(':dc-biz-common-model')
    implementation project(':dc-biz-utils')
    implementation project(':dc-biz-ess-model')
//    implementation project(':dc-biz-trading-model')

    implementation("com.cdz360.cloud:dc-base-ds:${dcCloudVersion}")
    implementation("org.mybatis.spring.boot:mybatis-spring-boot-starter:${mybatisSpringVersion}")

    implementation("com.github.pagehelper:pagehelper:${pagehelperVersion}")

    // https://mvnrepository.com/artifact/org.locationtech.jts/jts-core
    implementation group: 'org.locationtech.jts', name: 'jts-core', version: '1.19.0'

    testRuntimeOnly('com.h2database:h2:1.4.199')
    testImplementation "org.springframework.boot:spring-boot-starter-test"
}
