package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.ant.service.ActivityService;
import com.cdz360.biz.ant.service.WxShareService;
import com.cdz360.biz.ant.service.sysLog.CommercialSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.trading.coupon.param.ActivityUserCouponParam;
import com.cdz360.biz.model.trading.coupon.param.BatchSendCouponParam;
import com.cdz360.biz.model.trading.coupon.param.CouponSearchParam;
import com.cdz360.biz.model.trading.coupon.param.CreateActivityParam;
import com.cdz360.biz.model.trading.coupon.param.ListActivityParam;
import com.cdz360.biz.model.trading.coupon.param.ShareParam;
import com.cdz360.biz.model.trading.coupon.param.UpdateActivityParam;
import com.cdz360.biz.model.trading.coupon.type.AcquireCouponResult;
import com.cdz360.biz.model.trading.coupon.vo.ActivityVo;
import com.cdz360.biz.model.trading.coupon.vo.CouponBi;
import com.cdz360.biz.model.trading.coupon.vo.CouponVo;
import com.cdz360.biz.model.trading.coupon.vo.DiscountVo;
import com.cdz360.biz.model.trading.coupon.vo.ShareVo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @since 2018/11/21
 */
@Slf4j
@RestController
@RequestMapping("/api/activity")
@Tag(name = "活动相关接口", description = "活动")
public class ActivityRest extends BaseController {

    @Autowired
    private ActivityService activityService;

    @Autowired
    private WxShareService wxShareService;
    @Autowired
    private CommercialSysLogService commLogService;

    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;


    @Operation( summary = "创建活动")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public BaseResponse create(ServerHttpRequest request, @RequestBody CreateActivityParam req) {
        log.info("创建活动: {}", JsonUtils.toJsonString(req));
        var res = activityService.create(req);
        commLogService.activityCreateLog(req.getName(), request);
        return res;
    }

    @Operation( summary = "活动列表")
    @RequestMapping(value = "/list", method = RequestMethod.POST)
    public ListResponse<ActivityVo> list(ServerHttpRequest request, @RequestBody ListActivityParam req) {

        String commIdChain = super.getCommIdChain2(request);
        req.setIdChain(commIdChain);

        log.info("活动列表: {}", JsonUtils.toJsonString(req));
        return activityService.list(req);
    }

    @Operation( summary = "更新活动")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public BaseResponse update(ServerHttpRequest request, @RequestBody UpdateActivityParam req) {
        log.info("更新活动: {}", JsonUtils.toJsonString(req));
        var res = activityService.update(req);
        commLogService.activityUpdateLog(req.getName(), request);
        return res;
    }

    @Operation( summary = "启用活动")
    @RequestMapping(value = "/active", method = RequestMethod.GET)
    public BaseResponse active(ServerHttpRequest request,
                               @RequestParam("id") Long id,
                               @RequestParam("name") String name) {
        log.info("启用活动: {}", id);
        var res = activityService.activeActivity(id);
        commLogService.activitySwitchoverLog(name, request);
        return res;
    }

    @Operation( summary = "停用活动")
    @RequestMapping(value = "/abort", method = RequestMethod.GET)
    public BaseResponse abort(ServerHttpRequest request,
        @RequestParam("id") Long id,
        @RequestParam("name") String name) {
        log.info("停用活动: {}", id);
        var res = activityService.abortActivity(id);
        commLogService.activitySwitchoverLog(name, request);
        return res;
    }

    @Operation( summary = "设定活动在APP显示")
    @RequestMapping(value = "/showInMobile", method = RequestMethod.GET)
    public BaseResponse showInMobile(ServerHttpRequest request,
                              @RequestParam("id") Long id,
                              @RequestParam("name") String name) {
        log.info("设定活动在APP显示: {}", id);
        var res = activityService.showInMobile(id);
        commLogService.activitySwitchShowInMobileLog(name, "显示",request);
        return res;
    }

    @Operation( summary = "设定活动在APP隐藏")
    @RequestMapping(value = "/hideInMobile", method = RequestMethod.GET)
    public BaseResponse hideInMobile(ServerHttpRequest request,
                              @RequestParam("name") String name,
                              @RequestParam("id") Long id) {
        log.info("设定活动在APP隐藏: {}", id);
        var res = activityService.hideInMobile(id);
        commLogService.activitySwitchShowInMobileLog(name, "隐藏", request);
        return res;
    }

    @Operation( summary = "查询活动详情")
    @RequestMapping(value = "/getDetail", method = RequestMethod.GET)
    public ObjectResponse<ActivityVo> getDetail(ServerHttpRequest request, @RequestParam("id") Long id) {
        log.info("查询活动详情: {}", id);
        return activityService.getActivityDetail(id);
    }

    @Operation( summary = "活动券领取统计")
    @RequestMapping(value = "/couponBi", method = RequestMethod.GET)
    public ObjectResponse<CouponBi> couponBi(ServerHttpRequest request, @RequestParam("id") Long id) {
        log.info("活动券领取统计: {}", id);
        return activityService.couponBi(id);
    }

    @Operation( summary = "活动券用户领取领取列表")
    @RequestMapping(value = "/userCouponList", method = RequestMethod.POST)
    public ListResponse<CouponVo> userCouponList(ServerHttpRequest request, @RequestBody CouponSearchParam req) {
        log.info("活动券用户领取领取列表: {}", JsonUtils.toJsonString(req));
        return activityService.userActivityCouponList(req);
    }

    @Operation( summary = "用户手机号领取券(返回结果：1-已领取过；2-领券人数已满活动结束；3-领取成功；)")
    @RequestMapping(value = "/acquireCoupon", method = RequestMethod.GET)
    public ObjectResponse<AcquireCouponResult> acquireCoupon(ServerHttpRequest request,
                                                 @RequestParam("activityId") Long activityId,
                                                 @RequestParam("phone") String phone) {
        log.info("用户领取券: activityId: {}, phone: {}", activityId, phone);
        return activityService.acquireCoupon(activityId, phone);
    }

    @Operation(summary = "用户手机号是否领取过优惠券统计")
    @PostMapping(value = "/hasUserAcquiredCoupon")
    public ObjectResponse<Map<String, Boolean>> hasUserAcquiredCoupon(ServerHttpRequest request,
        @RequestBody ActivityUserCouponParam param) {
        log.info("用户手机号是否领取过优惠券统计: param: {}", JsonUtils.toJsonString(param));
        return activityService.hasUserAcquiredCoupon(param);
    }

    @Operation( summary = "微信好友、朋友圈分享获取appId等信息")
    @RequestMapping(value = "/getShareInfo", method = RequestMethod.POST)
    public ObjectResponse<ShareVo> getShareInfo(ServerHttpRequest request,
                                                @RequestBody ShareParam req) {

            ShareVo shareVo = wxShareService.getShareInfo(req.getUrl());
            return new ObjectResponse<>(shareVo);
    }

    @GetMapping(value = "/discountInfo")
    public ObjectResponse<DiscountVo> discountInfo(@RequestParam("commId") Long commId,
        @RequestParam("amount") BigDecimal amount) {
        log.info("满送信息,commId={},amount={}", commId, amount);
        return activityService.discountInfo(commId,amount);
    }

    @GetMapping(value = "/hasActivity")
    public ObjectResponse<ActivityVo> hasActivity(@RequestParam("commId") Long commId,
        @RequestParam("accountType") Long accountType) {
        log.info("是否存在同类的活动信息,commId={},amount={}", commId, accountType);
        return activityService.hasActivity(commId,accountType);
    }

    @Operation(summary = "后台批量发券")
    @PostMapping(value = "/batchSendCoupon")
    public BaseResponse batchSendCoupon(ServerHttpRequest request ,@RequestBody BatchSendCouponParam param) {
        param.setTopCommId(AntRestUtils.getTopCommId(request))
            .setCommId(AntRestUtils.getCommId(request))
            .setSysUid(AntRestUtils.getSysUid(request))
            .setSysUserName(AntRestUtils.getSysUserName(request));
        return activityService.batchSendCoupon(param);
    }

    @Operation( summary = "活动券用户领取列表导出")
    @PostMapping(value = "/exportUserCouponListBi")
    public Mono<ObjectResponse<ExcelPosition>> exportUserCouponListBi(ServerHttpRequest request, @RequestBody CouponSearchParam req) {
        String param = JsonUtils.toJsonString(req);
        log.info("活动券用户领取列表导出: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), param);

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("活动券领取用户列表")
            .setFunctionMap(DownloadFunctionType.USER_COUPON_LIST)
            .setReqParam(param);
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
    }

}
