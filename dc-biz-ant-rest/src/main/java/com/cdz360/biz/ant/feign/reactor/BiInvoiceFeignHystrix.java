package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.invoice.param.ListCorpInvoiceRecordParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.ListInvoicedRecordParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

import java.util.function.Function;

@Slf4j
@Component
public class BiInvoiceFeignHystrix
    implements FallbackFactory<BiInvoiceFeignClient> {

    @Override
    public BiInvoiceFeignClient apply(Throwable throwable) {
        return new BiInvoiceFeignClient() {

            @Override
            public Mono<ObjectResponse<ExcelPosition>> exportCorpInvoiceOrder(String applyNo) {
                log.error(
                    "【服务熔断】: Service = {}, api = exportCorpInvoiceOrder (企业开票涉及的订单导出), applyNo = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_BI, applyNo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<ExcelPosition>> exportInvoicedRecord(
                ListInvoicedRecordParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = exportInvoicedRecord (C端用户开票记录导出), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_BI, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<ExcelPosition>> exportSettRecExcel(String settleId) {
                log.error(
                    "【服务熔断】: Service = {}, api = exportSettRecExcel (商户结算单明细导出Excel), settleId = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_BI, settleId);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<ExcelPosition>> exportCorpInvoiceRecord(
                ListCorpInvoiceRecordParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = exportCorpInvoiceRecord (企业开票记录导出), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_BI, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }
        };
    }

    @Override
    public <V> Function<V, BiInvoiceFeignClient> compose(
        Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_BI);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(
        Function<? super BiInvoiceFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_BI);
        return null;
    }
}
