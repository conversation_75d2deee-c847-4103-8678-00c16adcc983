package com.cdz360.biz.ant.rest.site;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.service.site.TjAreaSiteRelService;
import com.cdz360.biz.model.tj.kc.param.ListSiteWithinTjAreaParam;
import com.cdz360.biz.model.tj.kc.vo.SiteWithinTjVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "与投建关联的场站相关操作接口", description = "与投建关联的场站相关操作接口")
@Slf4j
@RestController
@RequestMapping("/api/site/tj/rel")
public class TjAreaSiteRelRest {

    @Autowired
    private TjAreaSiteRelService tjAreaSiteRelService;

    @Operation(summary = "获取投建区域内的场站列表")
    @PostMapping(value = "/findSiteWithinTjArea")
    public Mono<ListResponse<SiteWithinTjVo>> findSiteWithinTjArea(
        @RequestBody ListSiteWithinTjAreaParam param) {
        log.info("获取勘察场站列表: param = {}", JsonUtils.toJsonString(param));
        return tjAreaSiteRelService.findSiteWithinTjArea(param);
    }

}
