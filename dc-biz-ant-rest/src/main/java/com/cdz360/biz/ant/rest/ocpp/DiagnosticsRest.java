package com.cdz360.biz.ant.rest.ocpp;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.config.LocalFileStorageProperties;
import com.cdz360.data.cache.RedisIotReadService;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/api/ocpp/diagnostics")
public class DiagnosticsRest {

    private static final String EVSE_DIAGNOSTICS_FILE_NAME = "iot:evse:diagnosticsFileName:";
    private static final String EVSE_DIAGNOSTICS_FILE_NAME_LIMIT_IP = "iot:evse:diagnosticsFileName:limitIp:";

    private static final String DIAGNOSTICS_DIR = "diagnostics";
    private static final String DEFAULT_STORAGE_PATH = "/tmp/ocpp";

    private static final int MAX_REQUESTS_PER_MINUTE_PER_IP = 5; // 每个IP每分钟最多5次
    private static final long MAX_TOTAL_STORAGE_SIZE = 1024 * 1024 * 1024; // 1GB

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(
        "yyyy-MM-dd");

    @Autowired
    private LocalFileStorageProperties localFileStorageProperties;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private RedisIotReadService redisIotReadService;

    private String generateEvseDiagnosticsFileNameKey(String evseNo) {
        return EVSE_DIAGNOSTICS_FILE_NAME + evseNo;
    }

    /**
     * OCPP 1.6 诊断日志文件上传接口 接口路径: PUT /api/ocpp/diagnostics/upload/{evseNo}
     *
     * @param evseNo         充电桩ID
     * @param dataBufferFlux HTTP请求数据流
     * @param filename       可选的文件名参数
     * @param request        HTTP请求对象
     * @return 上传结果
     */
    @PutMapping("/upload/{evseNo}")
    public Mono<BaseResponse> uploadDiagnosticsFile(@PathVariable("evseNo") String evseNo,
        @RequestBody Flux<DataBuffer> dataBufferFlux,
        @RequestParam(value = "filename", required = false) String filename,
        ServerHttpRequest request) {
        // 获取客户端IP
        String clientIp = getClientIp(request);
        log.info("收到诊断日志上传请求. evseNo: {}, clientIp: {}", evseNo, clientIp);

        // 验证充电桩ID
        if (!StringUtils.hasText(evseNo)) {
            log.warn("充电桩ID为空");
            return Mono.just(
                RestUtils.fail(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "充电桩ID不能为空"));
        }
        if (redisIotReadService.getEvseRedisCache(evseNo) == null) {
            return Mono.just(
                RestUtils.fail(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "充电桩不存在"));
        }

        // 1. IP频率限制检查
        if (!checkIpRateLimit(clientIp)) {
            log.warn("IP {} 请求过于频繁", clientIp);
            return Mono.just(
                RestUtils.fail(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "请求过于频繁，请稍后再试"));
        }

        try {
            // 确定存储路径
            String basePath;
            if (localFileStorageProperties.isEnable()) {
                basePath = localFileStorageProperties.getOcppPath();
                log.info("使用配置的存储路径: {}", basePath);
            } else {
                basePath = DEFAULT_STORAGE_PATH;
                log.info("本地文件存储未启用，使用默认临时存储路径: {}", basePath);
            }

            // 2. 检查总存储空间限制
            if (!checkStorageSpaceLimit(basePath)) {
                log.warn("系统存储空间不足");
                return Mono.just(RestUtils.fail(DcConstants.KEY_RES_CODE_SERVICE_ERROR,
                    "系统存储空间不足，请联系管理员"));
            }

            // 创建存储目录结构
            String today = LocalDateTime.now().format(DATE_FORMATTER);
            Path diagnosticsPath = Paths.get(basePath, DIAGNOSTICS_DIR, today, evseNo);

            // 确保目录存在
            Files.createDirectories(diagnosticsPath);

            // 获取真实文件名
            String realFilename = getRealFilename(filename, evseNo);
            log.info("使用文件名: {}", realFilename);

            // 创建目标文件路径
            Path targetPath = diagnosticsPath.resolve(realFilename);

            // 保存原始请求数据流
            return DataBufferUtils.write(dataBufferFlux, targetPath).then(Mono.fromCallable(() -> {
                log.info("成功保存充电桩 {} 的诊断日志文件: {}", evseNo, targetPath.toString());
                return RestUtils.success();
            })).onErrorResume(e -> {
                log.error("处理充电桩 {} 的诊断日志上传时发生未知错误. error: {}", evseNo,
                    e.getMessage(), e);
                return Mono.just(
                    RestUtils.fail(DcConstants.KEY_RES_CODE_SERVICE_ERROR, e.getMessage()));
            });

        } catch (Exception e) {
            log.error("处理充电桩 {} 的诊断日志上传时发生未知错误. error: {}", evseNo,
                e.getMessage(), e);
            return Mono.just(
                RestUtils.fail(DcConstants.KEY_RES_CODE_SERVICE_ERROR, e.getMessage()));
        }
    }

    /**
     * 获取真实的文件名 优先级：请求参数 > Redis中的文件名 > 默认生成
     *
     * @param paramFilename 请求参数中的文件名
     * @param evseNo        充电桩编号
     * @return 文件名
     */
    private String getRealFilename(String paramFilename, String evseNo) {
        // 1. 优先使用请求参数中的文件名
        if (StringUtils.hasText(paramFilename)) {
            log.info("使用请求参数中的文件名: {}", paramFilename);
            return sanitizeFilename(paramFilename);
        }

        // 2. 尝试从Redis中获取文件名
        try {
            String redisKey = generateEvseDiagnosticsFileNameKey(evseNo);
            String redisFilename = redisTemplate.opsForValue().get(redisKey);
            if (StringUtils.hasText(redisFilename)) {
                log.info("从Redis中获取文件名: {}, key: {}", redisFilename, redisKey);
                return sanitizeFilename(redisFilename);
            } else {
                log.info("Redis中未找到文件名, key: {}", redisKey);
            }
        } catch (Exception e) {
            log.warn("从Redis获取文件名时发生异常: {}", e.getMessage());
        }

        // 3. 如果以上都没有，生成默认文件名
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmmss"));
        String defaultFilename = timestamp + "_diagnostics.zip";
        log.info("未找到原始文件名，使用默认生成的文件名: {}", defaultFilename);
        return defaultFilename;
    }

    /**
     * 清理文件名，移除不安全的字符
     *
     * @param filename 原始文件名
     * @return 清理后的文件名
     */
    private String sanitizeFilename(String filename) {
        if (!StringUtils.hasText(filename)) {
            return "unknown_file";
        }

        // 移除路径分隔符和其他不安全字符
        String sanitized = filename.replaceAll("[/\\\\:*?\"<>|]", "_");

        // 如果文件名为空或只有扩展名，添加前缀
        if (sanitized.isEmpty() || sanitized.startsWith(".")) {
            sanitized = "diagnostics" + sanitized;
        }

        return sanitized;
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIp(ServerHttpRequest request) {
        String xForwardedFor = request.getHeaders().getFirst("X-Forwarded-For");
        if (StringUtils.hasText(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeaders().getFirst("X-Real-IP");
        if (StringUtils.hasText(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddress() != null ? request.getRemoteAddress().getAddress()
            .getHostAddress() : "unknown";
    }

    /**
     * 检查IP频率限制
     */
    private boolean checkIpRateLimit(String clientIp) {
        try {
            String key = EVSE_DIAGNOSTICS_FILE_NAME_LIMIT_IP + clientIp;
            String count = redisTemplate.opsForValue().get(key);

            if (count == null) {
                redisTemplate.opsForValue().set(key, "1", Duration.ofMinutes(1));
                return true;
            }

            int currentCount = Integer.parseInt(count);
            if (currentCount >= MAX_REQUESTS_PER_MINUTE_PER_IP) {
                return false;
            }

            redisTemplate.opsForValue().increment(key);
            return true;
        } catch (Exception e) {
            log.warn("检查IP频率限制时发生异常: {}", e.getMessage());
            return true; // 异常时允许通过
        }
    }

    /**
     * 检查总存储空间限制
     */
    private boolean checkStorageSpaceLimit(String basePath) {
        try {
            Path diagnosticsPath = Paths.get(basePath, DIAGNOSTICS_DIR);
            if (!Files.exists(diagnosticsPath)) {
                return true;
            }

            long totalSize = Files.walk(diagnosticsPath).filter(Files::isRegularFile)
                .mapToLong(path -> {
                    try {
                        return Files.size(path);
                    } catch (IOException e) {
                        return 0;
                    }
                }).sum();

            return totalSize < MAX_TOTAL_STORAGE_SIZE;
        } catch (IOException e) {
            log.warn("检查存储空间限制时发生异常: {}", e.getMessage());
            return true; // 异常时允许通过
        }
    }
}
