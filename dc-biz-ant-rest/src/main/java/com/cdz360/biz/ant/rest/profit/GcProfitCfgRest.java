package com.cdz360.biz.ant.rest.profit;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.service.profit.ProfitCfgService;
import com.cdz360.biz.ant.service.sysLog.GcProfitCfgSysLogService;
import com.cdz360.biz.model.trading.profit.sett.param.ListProfitCfgParam;
import com.cdz360.biz.model.trading.profit.sett.param.SaveProfitCfgParam;
import com.cdz360.biz.model.trading.profit.sett.vo.ProfitCfgVo;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/api/profitCfg")
@Tag(name = "收益配置相关操作接口", description = "收益配置相关操作接口")
public class GcProfitCfgRest {

    @Autowired
    private ProfitCfgService profitCfgService;

    @Autowired
    private GcProfitCfgSysLogService profitCfgSysLogService;

    @Operation(summary = "新增收益配置")
    @PostMapping(value = "/addCfg")
    public Mono<ObjectResponse<ProfitCfgVo>> addGcProfitCfg(
        ServerHttpRequest request,
        @RequestBody SaveProfitCfgParam param) {
        log.info("新增收益配置: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        return profitCfgService.addGcProfitCfg(param)
            .doOnNext(FeignResponseValidate::check)
            .doOnNext(x -> profitCfgSysLogService.addGcProfitCfgLog(param.getName(), request));
    }

    @Operation(summary = "编辑收益配置")
    @PostMapping(value = "/editCfg")
    public Mono<ObjectResponse<ProfitCfgVo>> editGcProfitCfg(
        ServerHttpRequest request,
        @RequestBody SaveProfitCfgParam param) {
        log.info("编辑收益配置: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        return profitCfgService.editGcProfitCfg(param)
            .doOnNext(FeignResponseValidate::check)
            .doOnNext(x -> profitCfgSysLogService.editGcProfitCfgLog(param.getName(), request));
    }

    @Operation(summary = "收益配置停用或启用")
    @GetMapping(value = "/enableCfg")
    public Mono<ObjectResponse<ProfitCfgVo>> enableGcProfitCfg(
        ServerHttpRequest request,
        @RequestParam("id") Long id,
        @RequestParam("enable") Boolean enable) {
        log.info("收益配置停用或启用: {}", LoggerHelper2.formatEnterLog(request));
        return profitCfgService.enableGcProfitCfg(id, enable)
            .doOnNext(FeignResponseValidate::check)
            .doOnNext(
                x -> profitCfgSysLogService.disableOrEnableLog(x.getData().getName(), request));
    }

    @Operation(summary = "删除收益配置(物理删除)")
    @DeleteMapping(value = "/deleteCfg")
    public Mono<ObjectResponse<ProfitCfgVo>> deleteGcProfitCfg(
        ServerHttpRequest request,
        @RequestParam("id") Long id) {
        log.info("删除收益配置(物理删除): {}", LoggerHelper2.formatEnterLog(request));
        return profitCfgService.deleteGcProfitCfg(id)
            .doOnNext(FeignResponseValidate::check)
            .doOnNext(
                x -> profitCfgSysLogService.delGcProfitCfgLog(x.getData().getName(), request))
            .doOnNext(x -> log.info("删除结算任务: {}", x.getData()));
    }

    @Operation(summary = "获取收益配置列表")
    @PostMapping(value = "/findAll")
    public Mono<ListResponse<ProfitCfgVo>> findGcProfitCfg(
        ServerHttpRequest request,
        @RequestBody ListProfitCfgParam param) {
        log.info("获取收益配置列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        return profitCfgService.findGcProfitCfg(param);
    }

    @Operation(summary = "获取收益配置", description = "无效ID返回data为null")
    @GetMapping(value = "/getCfg")
    public Mono<ObjectResponse<ProfitCfgVo>> getGcProfitCfg(
        ServerHttpRequest request,
        @RequestParam("id") Long id) {
        log.info("获取收益配置: {}", LoggerHelper2.formatEnterLog(request));
        return profitCfgService.getGcProfitCfg(id);
    }

}
