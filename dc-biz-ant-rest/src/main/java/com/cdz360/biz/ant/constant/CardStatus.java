package com.cdz360.biz.ant.constant;

/**
 * <AUTHOR>
 *   卡状态
 * @since 2019/5/16
 **/
public enum CardStatus {
    //10000未激活，10001已激活，10002卡锁定(已挂失)，10005已失效(黑名单)，10006已过期，10007下发中，10008下发成功，10009下发失败，20000已删除
    INACTIVE("10000", "未激活"),
    ACTIVE ("10001", "已激活"),
    LOCK("10002", "已挂失") ,
    FAILURE("10005", "已失效(黑名单)") ,
    EXPIRED("10006", "已过期") ,
    ISSUE("10007", "下发中") ,
    ISSUE_SUCCESS("10008", "下发成功") ,
    ISSUE_FAIL("10009", "下发失败") ,
    DELETED("20000", "已删除") ,
//    OFFLINE_CARD_NORMAL("20001", "离线卡正常") ,
    ;
    private final String code;
    private final String name;

    CardStatus(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName(){
        return name;
    }

    public static CardStatus valueOfCode(String code) {
        for (CardStatus type : CardStatus.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
