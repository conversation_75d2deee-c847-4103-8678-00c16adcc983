package com.cdz360.biz.ant.service.sysLog;

import com.cdz360.base.model.base.vo.KvAny;
import com.cdz360.biz.auth.user.type.LogOpType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class AccRelativeLogService {

    @Autowired
    private BaseSysLogService sysUserLogService;

    public void addAccRelative(ServerHttpRequest request, String name, String acc) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                List.of(KvAny.of("运维人员账号", acc),
                        KvAny.of("运维人员姓名", name)),
                request);
    }

    public void editAccRelative(ServerHttpRequest request, String name, String acc) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                List.of(KvAny.of("运维人员账号", acc),
                        KvAny.of("运维人员姓名", name)),
                request);
    }

    public void deleteBySysUid(ServerHttpRequest request, String name, String acc) {
        this.sysUserLogService.buildOpLog(LogOpType.DISABLE,
                List.of(KvAny.of("运维人员账号", acc),
                        KvAny.of("运维人员姓名", name)),
                request);
    }
}
