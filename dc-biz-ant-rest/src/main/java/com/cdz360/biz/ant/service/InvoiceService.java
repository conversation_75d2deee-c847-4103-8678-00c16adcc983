package com.cdz360.biz.ant.service;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.model.corp.type.CorpType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.domain.request.InvoicedModelSearchParam;
import com.cdz360.biz.ant.domain.vo.DescVo;
import com.cdz360.biz.ant.domain.vo.DzCommonVo;
import com.cdz360.biz.ant.domain.vo.IdListVo;
import com.cdz360.biz.ant.domain.vo.InvoicedChargerOrderVo;
import com.cdz360.biz.ant.domain.vo.InvoicedConfigMegVo;
import com.cdz360.biz.ant.domain.vo.InvoicedRecordChargerOrderSaveVo;
import com.cdz360.biz.ant.domain.vo.InvoicedRecordUpdateStatusVo;
import com.cdz360.biz.ant.domain.vo.InvoicedSiteValidYNVo;
import com.cdz360.biz.ant.domain.vo.InvoicedUserAutoAmountVo;
import com.cdz360.biz.ant.domain.vo.InvoicedUserVo;
import com.cdz360.biz.ant.domain.vo.SiteSimpleInfoVo;
import com.cdz360.biz.ant.feign.AntUserFeignClient;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.feign.CorpSettlementFeignClient;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.InvoiceFeignClient;
import com.cdz360.biz.ant.feign.InvoiceUserFeignClient;
import com.cdz360.biz.ant.feign.SiteFeignClient;
import com.cdz360.biz.ant.feign.TradingFeignClient;
import com.cdz360.biz.ant.feign.reactor.BiInvoiceFeignClient;
import com.cdz360.biz.ant.feign.reactor.DataCoreInvoiceFeignClient;
import com.cdz360.biz.ant.rest.oa.DepositInvoiceRest;
import com.cdz360.biz.ant.service.site.SiteService;
import com.cdz360.biz.auth.user.po.SysUserPo;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.cus.corp.dto.CorpInvoiceInfoDto;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.invoice.DcInvoice;
import com.cdz360.biz.model.cus.settlement.param.ListSettlementParam;
import com.cdz360.biz.model.cus.settlement.type.SettlementStatusEnum;
import com.cdz360.biz.model.cus.settlement.vo.SettlementVo;
import com.cdz360.biz.model.finance.type.TaxStatus;
import com.cdz360.biz.model.invoice.type.InvoicedStatus;
import com.cdz360.biz.model.invoice.type.ProductType;
import com.cdz360.biz.model.oa.constant.OaConstants;
import com.cdz360.biz.model.site.type.SiteCategory;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.cdz360.biz.model.trading.invoice.dto.CorpInvoiceRecordDto;
import com.cdz360.biz.model.trading.invoice.dto.InvoiceRecordOrderRefDto;
import com.cdz360.biz.model.trading.invoice.param.ListCorpInvoiceRecordParam;
import com.cdz360.biz.model.trading.invoice.param.ListInvoiceRecordOrderRefParam;
import com.cdz360.biz.model.trading.invoice.po.CorpInvoiceRecordPo;
import com.cdz360.biz.model.trading.invoice.vo.CorpInvoiceRecordVo;
import com.cdz360.biz.model.trading.order.dto.PayBillInvoiceBi;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.param.ListPayBillParam;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.model.trading.order.vo.CorpInvoiceOrderVo;
import com.chargerlinkcar.framework.common.constant.OrderStatus;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.domain.invoice.DcInvoiceOrderVo;
import com.chargerlinkcar.framework.common.domain.invoice.InvoicedRecordVo;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoicedModelDTO;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoicedTemplateSalDTO;
import com.chargerlinkcar.framework.common.domain.invoice.dto.UpdateIdDTO;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceInfoParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordAuditParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordManualParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordUpdateParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.ListInvoicedRecordParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.ListInvoicedTempSalParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.ListSiteTempSalParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceInfoVo;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceRecordDetail;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedModelVo;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedTemplateSalDetailVo;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedTemplateSalVo;
import com.chargerlinkcar.framework.common.domain.param.SiteListRequest;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderSite;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderBiVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderTimeShareBiVo;
import com.chargerlinkcar.framework.common.feign.CommercialFeignClient;
import com.chargerlinkcar.framework.common.feign.UserCommercialFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import reactor.core.publisher.Mono;

/**
 * InvoiceServiceImpl
 *
 * <AUTHOR>
 * @since 2019/7/3 11:13
 */
@Slf4j
@Service
public class InvoiceService //implements IInvoiceService
{

    @Autowired
    private InvoiceFeignClient invoiceFeignClient;

    @Autowired
    private AntUserFeignClient userFeignClient;

    @Autowired
    private CorpSettlementFeignClient corpSettlementFeignClient;

    @Autowired
    private UserCommercialFeignClient userCommercialFeignClient;

//    @Autowired
//    private MerchantFeignClient merchantFeignClient;

    @Autowired
    private InvoiceUserFeignClient invoiceUserFeignClient;

    @Autowired
    private TradingFeignClient tradingFeignClient;

    @Autowired
    private SiteService siteService;

    @Autowired
    private SiteFeignClient siteFeignClient;

    @Autowired
    private CommercialFeignClient commercialFeignClient;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private DataCoreInvoiceFeignClient dataCoreInvoiceFeignClient;

    @Autowired
    private BiInvoiceFeignClient biInvoiceFeignClient;
    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    /**
     * 获取开票主体信息
     *
     * @param saleTin 纳税人识别号
     * @return
     */
    public ObjectResponse<InvoicedTemplateSalVo> getInvoicedTempSal(Long commId, String saleTin) {
        if (StringUtils.isBlank(saleTin)) {
            throw new DcArgumentException("纳税人识别号无效");
        }

        if (null == commId) {
            throw new DcArgumentException("商户ID不能为空");
        }

        return invoiceFeignClient.getInvoicedTempSal(commId, saleTin);
    }

//    /**
//     * 设置发票模板头
//     *
//     * @param invoicedTemplateSalVo
//     * @return
//     */
//
//    public ObjectResponse<String> saveInvoicedTemplateSal(InvoicedTemplateSalVo invoicedTemplateSalVo) {
//        ObjectResponse<String> res = invoiceFeignClient.saveInvoicedTemplateSal(invoicedTemplateSalVo);
//        if (res.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
//            FeignResponseValidate.check(res);
//        }
//        log.info("调整商户开票模板: result = {}", JsonUtils.toJsonString(res.getData()));
//        return res;
//    }

    /**
     * 获取开票主体信息
     *
     * @param id 主体ID
     * @return
     */
    public ObjectResponse<InvoicedTemplateSalVo> getInvoicedTempSalById(Long id) {
        IotAssert.isNotNull(id, "主体ID无效");
        return invoiceFeignClient.getInvoicedTempSalById(id);
    }

    /**
     * 查询场站列表信息
     *
     * @param param
     * @param page
     * @param size
     * @return
     */

    public ListResponse<SiteSimpleInfoVo> getAllInvoicedRecords(
        ListSiteTempSalParam param, Long topCommId, String commIdChain, List<String> gids,
        int page, int size) throws UnsupportedEncodingException {

        SiteListRequest siteListRequest = new SiteListRequest();

        if (!ObjectUtils.isEmpty(param.getKeywords())) {
            String keywords = java.net.URLDecoder.decode(param.getKeywords(), "UTF-8");
            siteListRequest.setIdAndNameAndNoStr(keywords);
        }

        if (!ObjectUtils.isEmpty(param.getIdAndNameAndNoStr())) {
            String idAndNameAndNoStr = java.net.URLDecoder.decode(param.getIdAndNameAndNoStr(),
                "UTF-8");
            siteListRequest.setIdAndNameAndNoStr(idAndNameAndNoStr);
        }

        if (!CollectionUtils.isEmpty(param.getStatusList())) {
            siteListRequest.setStatusList(param.getStatusList());
        }

        if (param.getInvoicedValidFlag() != null) {
            siteListRequest.setInvoicedValid(param.getInvoicedValidFlag() ? 1 : 0);
        }

        if (param.getPlatformInvoicedValid() != null) {
            siteListRequest.setPlatformInvoicedValid(param.getPlatformInvoicedValid());
        }

        if (StringUtils.isNotBlank(param.getTempSalName())) {
            siteListRequest.setTempSalName(param.getTempSalName());
        }

        if (StringUtils.isNotBlank(param.getSiteId())) {
            siteListRequest.setSiteId(param.getSiteId());
        }

        if (param.getProvinceCode() != null) {
            siteListRequest.setProvinceCode(param.getProvinceCode());
        }

        if (param.getCityCode() != null) {
            siteListRequest.setCityCode(param.getCityCode());
        }

        if (param.getSorts() != null && CollectionUtils.isNotEmpty(param.getSorts())) {
            siteListRequest.setSorts(param.getSorts());
        }

        if (param.getCategory() != null && !SiteCategory.UNKNOWN.equals(param.getCategory())) {
            // 光储充类型不是null并且不是未知
            siteListRequest.setCategory(param.getCategory());
        }

        siteListRequest.setPage(page);
        siteListRequest.setRows(size);

        ListResponse<SiteSimpleInfoVo> res = this.siteService.getPagedSiteSimpleList(
            siteListRequest, topCommId, commIdChain, gids);
        log.info("站点列表结果: size = {}", res.getData().size());
        return res;
    }

    /**
     * 设置场站可开票标记(多个）
     *
     * @param vo
     * @return
     */

    @Deprecated
    public ObjectResponse<DzCommonVo> saveInvoicedValid(InvoicedSiteValidYNVo vo) {
//        log.info("查询场站列表信息的请求参数-------------------{}", JsonUtils.toJsonString(vo));
        ObjectResponse<DzCommonVo> res = invoiceFeignClient.saveInvoicedValid(vo);
        if (res.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            FeignResponseValidate.check(res);
        }
//        log.info("查询场站列表信息的接口数据返回-------------------{}", res.getData());
        return res;
    }

    /**
     * 根据商户id获取可开票商品说明
     *
     * @param commercialId
     * @return
     */

    public ObjectResponse<DescVo> getInvoicedProductDesc(Long commercialId) {
        log.info("获取可开票商品说明的请求参数-------------------{}", commercialId);
        ObjectResponse<DescVo> res = invoiceFeignClient.getInvoicedProductDesc(commercialId);
        if (res.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            FeignResponseValidate.check(res);
        }
        log.info("获取可开票商品说明的接口数据返回-------------------{}", res.getData());
        return res;
    }

    /**
     * 获取开票商品行模板by code
     *
     * @param code
     * @return
     */
    @Deprecated(since = "20201221")
    public ObjectResponse<InvoicedTemplateSalDetailVo> getInvoicedTemplateSalDetailByCode(
        String code) {
        throw new DcServiceException("已经废弃");
    }

    /**
     * 获取开票商品行模板列表by invoiceType
     *
     * @param invoiceType
     * @param commercialId
     * @param _index
     * @param _size
     * @return
     */

    public ListResponse<InvoicedTemplateSalDetailVo> getInvoicedTemplateSalDetailsByInvoiceType(
        String invoiceType, Long commercialId, int _index, int _size) {
//        log.info("获取开票商品行模板的请求参数----{}----{}----{}-------{}", invoiceType, commercialId, _index, _size);
        ListResponse<InvoicedTemplateSalDetailVo> res = invoiceFeignClient.getInvoicedTemplateSalDetailsByInvoiceType(
            invoiceType, commercialId, _index, _size);
        FeignResponseValidate.check(res);
        log.info("查询商品行信息模板结果: size = {}, total = {}", res.getData().size(),
            res.getTotal());
        return res;
    }

    /**
     * 查询用户信息
     *
     * @param keyword
     * @param status
     * @param index
     * @param size
     * @return
     */
    public ListResponse<InvoicedUserVo> getAllInvoicedUsers(
        String keyword, Boolean status, Long topCommId, String commIdChain, int index, int size) {
        ListResponse<DcInvoice> res = invoiceUserFeignClient.queryUserList(
            keyword, null != status && status ? 1 : 0, index, size, topCommId, commIdChain);
        FeignResponseValidate.check(res);

        List<InvoicedUserVo> result = res.getData().stream().map(invoice -> {
            InvoicedUserVo vo = new InvoicedUserVo();
            vo.setId(invoice.getId());
            vo.setCommId(invoice.getCommId());
            vo.setUsername(invoice.getUsername());
            vo.setName(invoice.getName());
            vo.setStatus(Integer.valueOf(1).equals(invoice.getStats()));
            vo.setPhone(invoice.getPhone());
            vo.setEmail(invoice.getEmail());
//        vo.setProvince(invoice.getProvince());
//        vo.setCity(invoice.getCity());
//        vo.setDistrict(invoice.getDistrict());
            vo.setStreet(invoice.getStreet());
            vo.setAddress(invoice.getAddress());
            vo.setInvoicedAmount(invoice.getInvoicedAmount());
//        vo.setCreateTime(invoice.getCreateTime() != null ?
//                ZonedDateTime.parse(invoice.getCreateTime(),
//                        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS+00:00")
//                                .withZone(ZoneId.systemDefault())) : null);
            vo.setAuto(invoice.getAuto() != null && Integer.valueOf(1).equals(invoice.getAuto()));
            return vo;
        }).collect(Collectors.toList());

        log.info("查询用户信息记录结果: size = {}, total = {}", result.size(), res.getTotal());
        return RestUtils.buildListResponse(result, res.getTotal());
    }

    /**
     * 查询用户开票金额、周期 和自动开票标识 b端c端都是以发票数据库数据为准
     *
     * @param userId
     * @param commId
     * @return
     */

    public ObjectResponse<InvoicedUserAutoAmountVo> getInvoicedUserAutoAmountVo(Long userId,
        Long commId) {
//        log.info("查询用户开票标识的请求参数----{}---------------{}", userId, commId);
//        ObjectResponse<InvoicedUserAutoAmountVo> res = invoiceFeignClient.getInvoicedUserAutoAmountVo(userId, commId);
//        if (res.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
//            FeignResponseValidate.check(res);
//        }
//        log.info("查询用户开票标识的接口数据返回-------------------{}", JsonUtils.toJsonString(res));
        return invoiceFeignClient.getInvoicedUserAutoAmountVo(userId, commId);
    }

    /**
     * 设置用户开票金额和周期、自动开票标识 b端
     *
     * @param invoicedUserAutoAmountVo
     * @return
     */

    public BaseResponse saveInvoicedUserAutoAmount(
        InvoicedUserAutoAmountVo invoicedUserAutoAmountVo) {
        log.info("设置用户开票标识的请求参数-------------------{}",
            JsonUtils.toJsonString(invoicedUserAutoAmountVo));
        BaseResponse res = invoiceFeignClient.saveInvoicedUserAutoAmount(invoicedUserAutoAmountVo);
        if (res.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            FeignResponseValidate.check(res);
        }
        log.info("设置用户开票标识的接口数据返回res-------------------{}",
            JsonUtils.toJsonString(res));
        ObjectResponse r = userFeignClient.saveInvoicedUserAutoAmount(
            invoicedUserAutoAmountVo.getUserId(),
            invoicedUserAutoAmountVo.getInvoicedAmount(),
            invoicedUserAutoAmountVo.getMonthDay(),
            invoicedUserAutoAmountVo.getAuto() ? 1 : 0);
        if (r.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            FeignResponseValidate.check(r);
        }
        log.info("设置用户开票标识的接口数据返回r-------------------{}",
            JsonUtils.toJsonString(r));
        return new BaseResponse();
    }

    /**
     * b端标记已开票
     *
     * @param invoicedRecordChargerOrderSaveVo
     * @param token
     * @return
     */

    public ObjectResponse<InvoicedRecordVo> signHadExportToInvoice(
        InvoicedRecordChargerOrderSaveVo invoicedRecordChargerOrderSaveVo,
        String token) {
        log.info("b端设置标记已开票的请求参数-------------------{}",
            JsonUtils.toJsonString(invoicedRecordChargerOrderSaveVo));
        ObjectResponse<InvoicedRecordVo> res = invoiceFeignClient.signHadExportToInvoice(
            invoicedRecordChargerOrderSaveVo, token);
        if (res.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            FeignResponseValidate.check(res);
        }
        log.info("b端设置标记已开票的接口数据返回-------------------{}",
            JsonUtils.toJsonString(res));
        return res;
    }

    /**
     * b端标记不可开票
     *
     * @param orderNoList
     * @param token
     * @return
     */

    public BaseResponse signForbiddenExportToInvoice(List<String> orderNoList, String token) {
        log.info("b端设置标记不开票的请求参数---------{}----------{}",
            JsonUtils.toJsonString(orderNoList), token);
        BaseResponse res = invoiceFeignClient.signForbiddenExportToInvoice(orderNoList, token);
        if (res.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            FeignResponseValidate.check(res);
        }
        log.info("b端设置标记不开票的接口数据返回-------------------{}",
            JsonUtils.toJsonString(res));
        return res;
    }

    /**
     * 查询用户开票记录
     *
     * @return
     */
    public ListResponse<InvoicedRecordVo> getAllInvoicedRecords(ListInvoicedRecordParam param) {
        ListResponse<InvoicedRecordVo> res = invoiceFeignClient.getAllInvoicedRecords(param);
        FeignResponseValidate.check(res);
        log.info("查询用户开票记录结果: size = {}, total = {}", res.getData().size(),
            res.getTotal());
        return res;
    }

    /**
     * 导出用户开票记录列表
     *
     * @return
     */
    public Mono<ObjectResponse<ExcelPosition>> exportInvoicedRecord(ListInvoicedRecordParam param) {
        return biInvoiceFeignClient.exportInvoicedRecord(param)
            .doOnNext(res -> log.info("导出用户开票记录列表: position = {}",
                JsonUtils.toJsonString(res.getData())));
    }

    /**
     * 导出企业开票记录
     *
     * @param param
     * @return
     */
    public Mono<ObjectResponse<ExcelPosition>> exportCorpInvoiceRecord(
        ListCorpInvoiceRecordParam param) {
        return biInvoiceFeignClient.exportCorpInvoiceRecord(param)
            .doOnNext(res -> log.info("导出企业开票记录列表: position = {}",
                JsonUtils.toJsonString(res.getData())));
    }

    /**
     * 根据用户ID查询开票记录
     *
     * @param userId
     * @param _index
     * @param _size
     * @return
     */

    public ListResponse<InvoicedRecordVo> getAllInvoicedRecordsByUserId(Long userId, int _index,
        int _size) {
        log.info("查询用户开票记录的请求参数----{}-----{}----{}", userId, _index, _size);

        if (userId == null) {
            throw new DcArgumentException("参数错误");
        }

        // 调整页面索引值
        _index = _index - 1;
        ListResponse<InvoicedRecordVo> res = invoiceFeignClient.getAllInvoicedRecordsByUserId(
            userId, _index, _size);
        if (res.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            FeignResponseValidate.check(res);
        }
        log.info("查询用户开票记录结果: size = {}, total = {}", res.getData().size(),
            res.getTotal());
        return res;
    }

    /**
     * 根据userId和状态获取所有开票记录
     *
     * @param userId
     * @param status
     * @param _index
     * @param _size
     * @return
     */

    public ListResponse<InvoicedRecordVo> getAllInvoicedRecordsByUserIdAndStatus(Long userId,
        String status, int _index, int _size) {
        log.info("查询用户开票记录的请求参数----{}----{}-----{}----{}", userId, status, _index,
            _size);

        ListResponse<InvoicedRecordVo> res = invoiceFeignClient.getAllInvoicedRecordsByUserIdAndStatus(
            userId, status, _index, _size);
        if (res.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            FeignResponseValidate.check(res);
        }
        log.info("查询用户开票记录结果: size = {}, total = {}", res.getData().size(),
            res.getTotal());
        return res;
    }

    /**
     * 变更发票记录的状态
     *
     * @param dto
     * @return
     */

    public ObjectResponse<InvoicedRecordVo> changeStatus(InvoicedRecordUpdateStatusVo dto) {
        log.info("变更发票记录状态的请求参数-------------------{}", JsonUtils.toJsonString(dto));

        ObjectResponse<InvoicedRecordVo> res = invoiceFeignClient.changeStatus(dto);

        if (res.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            FeignResponseValidate.check(res);
        }
        log.info("变更发票记录状态的接口数据返回-------------------{}",
            JsonUtils.toJsonString(res));
        return res;
    }

    /**
     * 变更发票记录状态为审批不通过
     *
     * @param idList
     * @return
     */

    public ListResponse<InvoicedRecordVo> changeSomeStatus(IdListVo idList) {
//        log.info("变更发票记录状态的请求参数--------{}-----------{}", JsonUtils.toJsonString(idList), token);
        ListResponse<InvoicedRecordVo> res = invoiceFeignClient.changeSomeStatus(idList);
        if (res.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            FeignResponseValidate.check(res);
        }
        log.info("变更发票记录结果: size = {}, total = {}", res.getData().size(), res.getTotal());
        return res;
    }

    /**
     * 导入开票系统
     *
     * @param idList
     * @param token
     * @return
     */

    public ListResponse<InvoicedRecordVo> exportToInvoice(IdListVo idList, String token) {
//        log.info("导入开票系统的请求参数--------{}-----------{}", JsonUtils.toJsonString(idList), token);
        ListResponse<InvoicedRecordVo> res = invoiceFeignClient.exportToInvoice(idList, token);
        FeignResponseValidate.check(res);
        return res;
    }

    /**
     * 根据id获取pdf的url
     *
     * @param id
     * @return
     */

    public ObjectResponse<Map<String, String>> getPdfUrl(Long id) {
//        log.info("getPdfUrl的请求参数--------{}-----------{}", id);
        ObjectResponse<Map<String, String>> res = invoiceFeignClient.getPdfUrl(id);
        if (res.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            FeignResponseValidate.check(res);
        }
        log.info("getPdfUrl的接口数据返回-------------------{}", JsonUtils.toJsonString(res));
        return res;
    }

    /**
     * 生成物流单
     *
     * @param idList
     * @param token
     * @return
     */

    public ObjectResponse<String> createTrackingNumber(IdListVo idList, String token) {
        log.info("生成物流单的请求参数--------{}-----------{}", JsonUtils.toJsonString(idList),
            token);

        ObjectResponse<String> res = invoiceFeignClient.createTrackingNumber(idList, token);
        if (res.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            FeignResponseValidate.check(res);
        }
        log.info("生成物流单的接口数据返回-------------------{}", JsonUtils.toJsonString(res));
        return res;
    }

    /**
     * 编辑开票控制信息
     *
     * @param invoicedConfigMegVo
     * @return
     */

    public ObjectResponse<InvoicedConfigMegVo> saveMegConfig(
        InvoicedConfigMegVo invoicedConfigMegVo) {
//        log.info("编辑开票控制信息的请求参数--------{}-----------{}", JsonUtils.toJsonString(invoicedConfigMegVo));

        ObjectResponse<InvoicedConfigMegVo> res = invoiceFeignClient.saveMegConfig(
            invoicedConfigMegVo);
        if (res.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            FeignResponseValidate.check(res);
        }
//        log.info("编辑开票控制信息的接口数据返回-------------------{}", JsonUtils.toJsonString(res));
        return res;
    }

    /**
     * 获取开票控制信息
     *
     * @param token
     * @return
     */

    public ObjectResponse<InvoicedConfigMegVo> getInvoicedMegConfig(String token) {
        ObjectResponse<InvoicedConfigMegVo> res = invoiceFeignClient.getInvoicedMegConfig(token);
        if (res.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            FeignResponseValidate.check(res);
        }
        log.info("");
        log.info("获取开票控制信息的接口数据返回-------------------{}",
            JsonUtils.toJsonString(res));
        return res;
    }

    /**
     * 查询用户的充电记录
     *
     * @param customerId
     * @param status
     * @param invoicedFlag
     * @param keyword
     * @param startDate
     * @param endDate
     * @param token
     * @param page
     * @param size
     * @return
     */

    public ListResponse<InvoicedChargerOrderVo> getAllInvoicedChargerOrders(Long customerId,
        Integer status, Boolean invoicedFlag,//是否已经关联了开票申请
        String keyword, String startDate,
        String endDate, String token,
        int page, int size) {
        log.info(
            "查询用户的充电记录的请求参数----{}----{}----{}----{}----{}----{}----{}----{}----{}",
            customerId, status, invoicedFlag, keyword, startDate, endDate, token, page, size);

        ListResponse<InvoicedChargerOrderVo> res = invoiceFeignClient.getAllInvoicedChargerOrders(
            customerId, status, invoicedFlag, keyword, startDate, endDate, token, page, size);
        if (res.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            FeignResponseValidate.check(res);
        }
        log.info("查询结果: size = {}, total = {}", res.getData().size(), res.getTotal());
        return res;
    }

    /**
     * 查询发票申请对应的订单记录
     *
     * @param invoicedId
     * @param page
     * @param size
     * @return
     */

    public ListResponse<InvoicedChargerOrderVo> getAllInvoicedChargerOrders(String token,
        Long invoicedId, int page, int size) {
//        log.info("查询发票申请对应的订单记录的请求参数----{}----{}----{}----{}", invoicedId, token, page, size);

//        ListResponse<InvoicedChargerOrderVo> res = invoiceFeignClient.getAllInvoicedChargerOrders(invoicedId, page, size);
//        FeignResponseValidate.check(res);

        ListResponse<DcInvoiceOrderVo> res = tradingFeignClient.queryChargerOrderList(token,
            null, null, null, invoicedId, page, size, null, null);
        FeignResponseValidate.check(res);

        // 遍历初始化
        List<InvoicedChargerOrderVo> result = res.getData().stream().map(order -> {
            InvoicedChargerOrderVo vo = new InvoicedChargerOrderVo();
            vo.setOrderId(order.getOrderNo());
            vo.setOrderNo(order.getOrderNo());
//            vo.setCommercialId(order.getCommercialId());
            vo.setStationId(order.getStationId());

            // 充电用户信息
            vo.setCustomerId(order.getUserId());
            vo.setCustomerName(order.getUsername());
            vo.setCustomerPhone(order.getUserPhone());

            vo.setCommercialName(order.getCommercialName());

            vo.setOrderPrice(order.getOrderPrice());
            vo.setActualPrice(order.getActualPrice());

            vo.setInvoiceAmount(order.getInvoiceAmount());
            vo.setStatus(order.getStatus());
            vo.setMobilePhone(order.getMobilePhone());
            vo.setStationName(order.getStationName());
            //dto.setCustomerName(customerName;);
            vo.setInvoicedId(order.getInvoicedId());
            vo.setChargeStartTime((long) order.getChargeStartTime());
            vo.setChargeEndTime((long) order.getChargeEndTime());
//            vo.setCreateTime(order.getCreateTime() != null ?
//                    ZonedDateTime.parse(row.getCreateTime(),
//                            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS+00:00")
//                                    .withZone(ZoneId.systemDefault())) : null);
//            vo.setStopTime(order.getStopTime() != null ?
//                    ZonedDateTime.parse(order.getStopTime(),
//                            DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS+00:00")
//                                    .withZone(ZoneId.systemDefault())) : null);
            vo.setOrderTime(order.getOrderTime());
            vo.setChargingTimes(order.getChargingTimes());
            vo.setServActualFee(order.getServActualFee());
            vo.setElecActualFee(order.getElecActualFee());
            if (order.getParkCancelStatus() != null) {
                vo.setParkActualFee(
                    100 == order.getParkCancelStatus() ? BigDecimal.ZERO
                        : order.getParkActualFee());
            }
            return vo;
        }).collect(Collectors.toList());

        log.info("查询结果: size = {}, total = {}", res.getData().size(), res.getTotal());
        return RestUtils.buildListResponse(result, res.getTotal());
    }

    /**
     * 查询用户的充电记录2c
     *
     * @param customerId
     * @param status
     * @param invoicedFlag
     * @param keyword
     * @param token
     * @param page
     * @param size
     * @return
     */

    public ListResponse<InvoicedChargerOrderVo> getAllInvoicedChargerOrders2c(Long customerId,
        Integer status, Boolean invoicedFlag,//是否已经关联了开票申请
        String keyword, String token, int page, int size) {
        log.info("查询发票申请对应的订单记录的请求参数----{}----{}----{}----{}----{}----{}----{}",
            customerId, status, invoicedFlag, keyword, token, page, size);

        ListResponse<InvoicedChargerOrderVo> res = invoiceFeignClient.getAllInvoicedChargerOrders2c(
            customerId, status, invoicedFlag, keyword, token, page, size);
        if (res.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            FeignResponseValidate.check(res);
        }
        log.info("查询结果: size = {}, total = {}", res.getData().size(), res.getTotal());
        return res;

    }

    /**
     * 获取用户默认模板
     *
     * @param userId
     * @return
     */

    public ObjectResponse<InvoicedModelVo> getDefaultInvoicedModel(Long userId) {
        log.info("获取用户默认模板的请求参数-------------------{}", userId);
        ObjectResponse<InvoicedModelVo> res = invoiceFeignClient.getDefaultInvoicedModel(userId);
        if (res.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            FeignResponseValidate.check(res);
        }
        log.info("获取用户默认模板的接口数据返回-------------------{}",
            JsonUtils.toJsonString(res));
        return res;
    }

//    /**
//     * 保存所有商品行模板信息
//     *
//     * @param dtoList
//     * @return
//     */
//    @Deprecated
//    public ObjectResponse<Integer> saveAll(List<InvoicedTemplateSalDetailVo> dtoList) {
//        // 商品行模板约束
//        if (!this.valid(dtoList)) {
//            log.info("保存信息校验不通过");
//            throw new DcArgumentException("保存失败：配置校验不通过.");
//        }
//
//        return invoiceFeignClient.saveAll(dtoList);
//    }

//    private boolean valid(List<InvoicedTemplateSalDetailVo> dtoList) {
//        List<ProductType> collect = dtoList.stream().map(InvoicedTemplateSalDetailVo::getProductType)
//                .distinct().collect(Collectors.toList());
//
//        if (collect.size() != dtoList.size()) {
//            log.info("商品类型不匹配");
//            return false;
//        }
//
//        if (collect.size() == 1 &&
//                !collect.get(0).equals(ProductType.ACTUAL_FEE)) {
//            log.info("商品行仅有一个的时候只能为: 实际金额消费");
//            return false;
//        }
//
//        if (collect.size() == 2 &&
//                collect.contains(ProductType.ACTUAL_FEE)) {
//            log.info("商品行有两个的时候不能含有: 实际金额消费");
//            return false;
//        }
//
//        return true;
//    }

    /**
     * 获取开票使用开票模板的企业总数
     *
     * @param productId
     * @return
     */
    public ObjectResponse getCorpInfoByProduct(Long productId) {
        return userFeignClient.getCorpInfoByProduct(productId);
    }

    public ListResponse<InvoicedTemplateSalVo> getInvoicedTempSalList(
        ListInvoicedTempSalParam param) {
        return invoiceFeignClient.getInvoicedTempSalList(param);
    }

    public ListResponse<InvoicedTemplateSalVo> getInvoicedTempSalListByCommId(
        String commId) {
        return invoiceFeignClient.getInvoicedTempSalListByCommId(commId);
    }

    public ObjectResponse<InvoicedTemplateSalDTO> addInvoicedTempSal(InvoicedTemplateSalVo vo) {
        // 数据校验
        InvoicedTemplateSalVo.updateCheck(vo);

        // 初始化一些数据
        vo.getTempRefVoList().forEach(ref -> {
            ref.setUpdateTime(ZonedDateTime.now())
                .setCreateTime(ZonedDateTime.now());
        });

        return invoiceFeignClient.addInvoicedTempSal(vo);
    }

    public Integer updateInvoicedTempSal(InvoicedTemplateSalVo vo) {
        // 数据校验
        InvoicedTemplateSalVo.updateCheck(vo);
        if (null == vo.getId()) {
            throw new DcArgumentException("开票主体ID不能为空");
        }

        // 初始化一些数据
        vo.getTempRefVoList().forEach(ref -> {
            ref.setUpdateTime(ZonedDateTime.now())
                .setCreateTime(ZonedDateTime.now());
        });

        ListResponse<UpdateIdDTO> res = invoiceFeignClient.updateInvoicedTempSal(vo);
        FeignResponseValidate.check(res);

        if (CollectionUtils.isNotEmpty(res.getData())) {
//            ObjectResponse<Integer> response = dataCoreFeignClient.updateCorpInvoiceRecordProTempId(res.getData());
            ObjectResponse<Integer> response = userFeignClient.updateCorpInvoiceInfoProTempId(
                res.getData());
        }

        return res.getData().size();
    }

    public InvoicedTemplateSalDTO disableInvoicedTempSal(Long commId, String saleTin) {
        if (null == commId) {
            throw new DcArgumentException("商户ID不能为空");
        }

        if (StringUtils.isBlank(saleTin)) {
            throw new DcArgumentException("纳税人识别号无效");
        }

        ObjectResponse<InvoicedTemplateSalDTO> result = invoiceFeignClient.disableInvoicedTempSal(
            commId, saleTin);
        FeignResponseValidate.check(result);
        return result.getData();
    }

    /**
     * 获取企业开票设置信息
     *
     * @param corpId
     * @return
     */
    public CorpInvoiceInfoVo getCorpInvoiceInfo(Long corpId, Long productTempId) {
        CorpInvoiceInfoVo result = new CorpInvoiceInfoVo();

        // ant -> user: /api/invoice/getCorpInvoiceInfo
        ObjectResponse<CorpInvoiceInfoVo> corpInvoiceInfo = userFeignClient.getCorpInvoiceInfo(
            corpId);
        FeignResponseValidate.checkIgnoreData(corpInvoiceInfo);

        // 企业客户没有设置开票
        if (null == corpInvoiceInfo.getData() ||
            !corpInvoiceInfo.getData().getEnable()) {
            result.setInvoiceOpen(false);
            return result;
        }

        // 获取企业开票主体信息和开票模板信息
        // ant -> invoice
        CorpInvoiceInfoParam param = new CorpInvoiceInfoParam();
        param.setUid(corpInvoiceInfo.getData().getUid())
            .setTempSalId(corpInvoiceInfo.getData().getTempSalId())
            .setProductTempId(
                Optional.ofNullable(productTempId)
                    .orElse(corpInvoiceInfo.getData().getProductTempId()));
        ObjectResponse<CorpInvoiceInfoVo> tempSal = invoiceFeignClient.getCorpInvoiceDetail(param);
        FeignResponseValidate.check(tempSal);

        // 组合数据返回
        BeanUtils.copyProperties(tempSal.getData(), result);
        result.setInvoiceOpen(true);
        result.setCommId(corpInvoiceInfo.getData().getCommId())
            .setCorpId(corpInvoiceInfo.getData().getCorpId())
            .setEnable(corpInvoiceInfo.getData().getEnable())
            .setInvoiceWay(corpInvoiceInfo.getData().getInvoiceWay());

        return result;
    }

    /**
     * 更新企业开票设置信息
     *
     * @param vo
     * @return
     */
    public Long saveCorpInvoiceInfo(CorpInvoiceInfoVo vo) {
        // 数据校验
        vo.saveCheck();

        // 获取企业配置信息
        ObjectResponse<CorpInvoiceInfoVo> corpInvoiceInfo = userFeignClient.getCorpInvoiceInfo(
            vo.getCorpId());
        FeignResponseValidate.checkIgnoreData(corpInvoiceInfo);

        boolean invoiceOpen = false;
        if (null != corpInvoiceInfo.getData() &&
            corpInvoiceInfo.getData().getEnable()) {
            invoiceOpen = true;
        }

        // true false
        if (invoiceOpen) {
            boolean needCheck = false;
            if (vo.getInvoiceOpen() &&
                !corpInvoiceInfo.getData().getInvoiceWay().equals(vo.getInvoiceWay())) {
                needCheck = true;
            } else if (!vo.getInvoiceOpen()) {
                needCheck = true;
            }

            if (needCheck) {
                ListCorpInvoiceRecordParam param = new ListCorpInvoiceRecordParam();
                param.setCorpId(vo.getCorpId())
                    .setStatusList(List.of(
                        InvoicedStatus.SUBMITTED, InvoicedStatus.REVIEWED,
                        InvoicedStatus.AUDIT_FAILED,
                        InvoicedStatus.INVOICING_FAIL, InvoicedStatus.RED_DASHED,
                        InvoicedStatus.INVALID));
                ListResponse<CorpInvoiceRecordDto> recList = dataCoreFeignClient.findCorpInvoiceRecordList(
                    param);
                FeignResponseValidate.check(recList);

                if (CollectionUtils.isNotEmpty(recList.getData())) {
                    throw new DcServiceException("需要将开票记录删除，才允许调整企业开票方式");
                }
            }
        }

        // 更新企业开票配置
        // ant -> user: /api/invoice/saveCorpInvoiceInfo
        if (vo.getInvoiceOpen()) {
            CorpInvoiceInfoDto dto = new CorpInvoiceInfoDto();
            BeanUtils.copyProperties(vo, dto);
            ObjectResponse<Integer> save = userFeignClient.saveCorpInvoiceInfo(dto);
            FeignResponseValidate.check(save);
        } else {
            ObjectResponse<Integer> disable = userFeignClient.disableByCorpId(vo.getCorpId());
            FeignResponseValidate.check(disable);
        }

        // 更新配置模板信息
        // ant -> invoice: /api/invoice/updateCorpInvoiceModel
        InvoicedModelDTO modelDTO = new InvoicedModelDTO();
        BeanUtils.copyProperties(vo, modelDTO);
        modelDTO.setUserId(vo.getUid());
        ObjectResponse<UpdateIdDTO> update = invoiceFeignClient.updateCorpInvoiceModel(modelDTO);
        FeignResponseValidate.check(update);

//        // 需要调整企业开票模板ID值
//        if (update.getData().getOldId() != null && update.getData().getNewId() != null) {
//            ObjectResponse<Integer> res = dataCoreFeignClient.updateCorpInvoiceRecordModelId(update.getData());
//        }

        return update.getData().getNewId();
    }

    /**
     * 企业抬头更新
     *
     * @param modelDTO
     * @return
     */
    public Long saveCorpInvoiceModelInfo(InvoicedModelDTO modelDTO) {
        ObjectResponse<UpdateIdDTO> update = invoiceFeignClient.updateCorpInvoiceModel(modelDTO);
        FeignResponseValidate.check(update);
        return update.getData().getNewId();
    }

    /**
     * 个人、会员抬头更新
     *
     * @param modelDTO
     * @return
     */
    public Long saveUserInvoiceModelInfo(InvoicedModelDTO modelDTO) {
        ObjectResponse<UpdateIdDTO> update = invoiceFeignClient.updateUserInvoiceModel(modelDTO);
        FeignResponseValidate.check(update);
        return update.getData().getNewId();
    }

    public ObjectResponse<CorpInvoiceRecordDetail> corpInvoiceAppendOrder(
        CorpInvoiceRecordUpdateParam param) {
        // 参数校验
        if (StringUtils.isNotBlank(param.getInterimCode())) {
            // 存在时不校验
        } else if (!Boolean.TRUE.equals(param.getOpAll()) // 部分添加
            && !param.fromBillingProcess() // 不是OA流程请求
            && CollectionUtils.isEmpty(param.getOrderNoList())
        ) {
            throw new DcArgumentException("操作订单号列表不能为空");
        }

        if (null == param.getCorpId()) {
            throw new DcArgumentException("请提供企业客户ID");
        }

        // 获取企业配置信息
        CorpInvoiceInfoVo corpInvoiceInfo = this.getCorpInvoiceInfo(param.getCorpId(), null);
        if (!corpInvoiceInfo.getInvoiceOpen()) {
            throw new DcArgumentException("该企业客户没有打开平台开票, 请先设置");
        }
        if (param.fromBillingProcess()) {
            // 改为账单后开票
            corpInvoiceInfo.setInvoiceWay(InvoicingMode.POST_SETTLEMENT);
        }

        if (param.fromPrepaidOrderInvoicingProcess()
            && !InvoicingMode.POST_CHARGER.equals(corpInvoiceInfo.getInvoiceWay())) {
            throw new DcServiceException(
                "请先到企业客户开票设置页面将开票方式配置为预付费充电开票，再刷新页面重试");
        }

        final InvoicingMode SupportInvoicingMode = DepositInvoiceRest.CORP_SUPPORT_MODE;
        if (StringUtils.equalsIgnoreCase(OaConstants.PD_KEY_DEPOSIT_PROCESS,
            param.getProcDefKey())) {
            IotAssert.isTrue(SupportInvoicingMode.equals(corpInvoiceInfo.getInvoiceWay()),
                "企客仅支持开票方式:" + SupportInvoicingMode.getDesc());
        }

        param.setCorpInvoiceInfoVo(corpInvoiceInfo);
        ObjectResponse<CorpInvoiceRecordVo> res = dataCoreFeignClient.corpInvoiceAppendOrder(param);
        FeignResponseValidate.check(res);

        CorpInvoiceRecordDetail detail = new CorpInvoiceRecordDetail();
        BeanUtils.copyProperties(res.getData(), detail);
        this.initCorpInvoiceInfo(detail, corpInvoiceInfo);
        return RestUtils.buildObjectResponse(detail);
    }

    public ObjectResponse<CorpInvoiceRecordDetail> corpInvoiceRemoveOrder(
        CorpInvoiceRecordUpdateParam param) {
        // 参数校验
        if (!Boolean.TRUE.equals(param.getOpAll()) && CollectionUtils.isEmpty(
            param.getOrderNoList())) {
            throw new DcArgumentException("操作订单号列表不能为空");
        }

        if (null == param.getCorpId()) {
            throw new DcArgumentException("请提供企业客户ID");
        }

        // 获取企业配置信息
        CorpInvoiceInfoVo corpInvoiceInfo = this.getCorpInvoiceInfo(param.getCorpId(), null);
        if (!corpInvoiceInfo.getInvoiceOpen()) {
            throw new DcArgumentException("该企业客户没有打开平台开票, 请先设置");
        }
        if (param.fromBillingProcess()) {
            // 改为账单后开票
            corpInvoiceInfo.setInvoiceWay(InvoicingMode.POST_SETTLEMENT);
        }

        if (param.fromPrepaidOrderInvoicingProcess()
            && !InvoicingMode.POST_CHARGER.equals(corpInvoiceInfo.getInvoiceWay())) {
            throw new DcServiceException(
                "请先到企业客户开票设置页面将开票方式配置为预付费充电开票，再刷新页面重试");
        }

        param.setCorpInvoiceInfoVo(corpInvoiceInfo);
        ObjectResponse<CorpInvoiceRecordVo> res = dataCoreFeignClient.corpInvoiceRemoveOrder(param);
        FeignResponseValidate.check(res);

        CorpInvoiceRecordDetail detail = new CorpInvoiceRecordDetail();
        BeanUtils.copyProperties(res.getData(), detail);
        this.initCorpInvoiceInfo(detail, corpInvoiceInfo);
        return RestUtils.buildObjectResponse(detail);
    }

    public ObjectResponse<CorpInvoiceRecordDetail> corpInvoiceRecordDetail(String applyNo) {
        if (StringUtils.isBlank(applyNo)) {
            throw new DcArgumentException("申请单号不能为空");
        }

        ObjectResponse<CorpInvoiceRecordDetail> res = dataCoreFeignClient.corpInvoiceRecordDetail(
            applyNo);
        FeignResponseValidate.check(res);

        // 企业开票配置信息
//        CorpInvoiceInfoVo corpInvoiceInfo = this.getCorpInvoiceInfo(
//                res.getData().getCorpId(),
//                InvoicedStatus.COMPLETED.equals(res.getData().getStatus()) ?
//                        res.getData().getProductTempId() : null);
        CorpInvoiceInfoParam param = new CorpInvoiceInfoParam();
        param.setUid(res.getData().getUid())
            .setTempSalId(res.getData().getTempSalId())
            .setProductTempId(res.getData().getProductTempId());
        ObjectResponse<CorpInvoiceInfoVo> tempSal = invoiceFeignClient.getCorpInvoiceDetail(param);
        FeignResponseValidate.check(tempSal);

        this.initCorpInvoiceInfo(res.getData(), tempSal.getData());

        // 获取审核人姓名
        if (StringUtils.isNotBlank(res.getData().getAuditName())) {
            ObjectResponse<SysUserPo> sysUserRes = authCenterFeignClient.getByUserNameAndPlatform(
                res.getData().getAuditName(), AppClientType.MGM_WEB);
            FeignResponseValidate.checkIgnoreData(sysUserRes);

            SysUserPo sysUser = sysUserRes.getData();
            if (null != sysUser) {
                res.getData().setAuditFullName(sysUser.getName());
            }
        }

        return res;
    }

    private void initCorpInvoiceInfo(CorpInvoiceRecordDetail detail,
        CorpInvoiceInfoVo corpInvoiceInfo) {
        detail.setTempRefVo(corpInvoiceInfo.getTempRefVo())
            .setName(corpInvoiceInfo.getName())
            .setEmail(corpInvoiceInfo.getEmail())
            .setTin(corpInvoiceInfo.getTin())
            .setAddress(corpInvoiceInfo.getAddress())
            .setTel(corpInvoiceInfo.getTel())
            .setBank(corpInvoiceInfo.getBank())
            .setBankAccount(corpInvoiceInfo.getBankAccount())
            .setReceiverName(corpInvoiceInfo.getReceiverName())
            .setReceiverMobilePhone(corpInvoiceInfo.getReceiverMobilePhone())
            .setReceiverProvince(corpInvoiceInfo.getReceiverProvince())
            .setReceiverCity(corpInvoiceInfo.getReceiverCity())
            .setReceiverArea(corpInvoiceInfo.getReceiverArea())
            .setReceiverAddress(corpInvoiceInfo.getReceiverAddress())
            .setUid(corpInvoiceInfo.getUid())
            .setChannel(corpInvoiceInfo.getChannel());

        // 开票主体信息
        detail.setSaleName(corpInvoiceInfo.getSaleName());
        detail.setProductTempName(corpInvoiceInfo.getTempRefVo().getName());

        // 审核状态
        List<InvoicedStatus> statusList = List.of(InvoicedStatus.SUBMITTED,
            InvoicedStatus.NOT_SUBMITTED);
        if (detail.getAuditTime() != null) {
            if (detail.getStatus() == InvoicedStatus.AUDIT_FAILED) {
                detail.setAuditResult(false);
            } else if (statusList.contains(detail.getStatus())) {
                detail.setAuditResult(null);
            } else {
                detail.setAuditResult(true);
            }
        }

        // 商品行开票金额信息
        detail.getTempRefVo().getDetailVoList().forEach(vo -> {
            if (vo.getProductType() == ProductType.SERV_ACTUAL_FEE) {
                vo.setAmount(detail.getActualServFee())
                    .setFixAmount(detail.getFixServFee());
            } else if (vo.getProductType() == ProductType.ELEC_ACTUAL_FEE) {
                vo.setAmount(detail.getActualElecFee())
                    .setFixAmount(detail.getFixElecFee());
            } else {
                vo.setAmount(detail.getTotalFee())
                    .setFixAmount(detail.getFixTotalFee());
            }
        });
    }

    public Mono<ObjectResponse<Boolean>> corpInvoiceRecordManual(
        CorpInvoiceRecordManualParam param) {
        // 参数校验
        CorpInvoiceRecordManualParam.check(param);
        return Mono.just(dataCoreFeignClient.corpInvoiceRecordManual(param));
    }

    public ObjectResponse<Integer> corpInvoiceRecordAudit(CorpInvoiceRecordAuditParam param) {
        // 参数校验
        CorpInvoiceRecordAuditParam.check(param);
        return dataCoreFeignClient.corpInvoiceRecordAudit(param);
    }

    public ListResponse<CorpInvoiceRecordDto> findCorpInvoiceRecordList(
        ListCorpInvoiceRecordParam param) {
        return dataCoreFeignClient.findCorpInvoiceRecordList(param);
    }

    public ListResponse<CorpInvoiceOrderVo> getChargerOrderList(ListChargeOrderParam param) {
        // 产品: 默认是预付费的充电订单 20200806
        // 前端入参: siteIdList, orderNo, 时间分段信息, corpId, applyNo

        // 新增获取: corpId, billNo = ''(不关联账单), invoicedAmount == 0(已开票金额为0)
        if (null == param.getCorpId()) {
            throw new DcArgumentException("请提供企业客户ID");
        } else if (param.getCorpId() > 0L) {
            ObjectResponse<CorpPo> corpRes = this.authCenterFeignClient.getCorp(param.getCorpId());
            FeignResponseValidate.check(corpRes);
            if (CorpType.HLHT == corpRes.getData().getType()
                && param.getSettlementType() == null) {
                param.setSettlementType(SettlementType.PARTNER);
            }
        }
        param.setInCorpInvoice(true);
        param.setStatusList(List.of(OrderStatus.ORDER_STATUS_RECEIVE_MONEY)); // 已结算的充电订单

        ListResponse<ChargerOrderVo> res = dataCoreFeignClient.listChargerOrder(param);
        FeignResponseValidate.check(res);
        log.info("订单list.size = {}, total = {}", res.getData().size(), res.getTotal());
        return RestUtils.buildListResponse(res.getData().stream().map(order -> {
            CorpInvoiceOrderVo vo = new CorpInvoiceOrderVo();
            BeanUtils.copyProperties(order, vo);
            if (DecimalUtils.isZero(order.getInvoicedAmount())) {
                vo.setTaxStatus(TaxStatus.NO);
            } else if (DecimalUtils.eq(order.getInvoiceAmount(), order.getInvoiceAmount())) {
                vo.setTaxStatus(TaxStatus.YES);
            } else {
                vo.setTaxStatus(TaxStatus.PART);
            }
            return vo;
        }).collect(Collectors.toList()), res.getTotal());
    }

    public Mono<ListResponse<CorpInvoiceOrderVo>> includeChargerOrderList(
        ListChargeOrderParam param) {
        return dataCoreInvoiceFeignClient.includeChargerOrderList(param)
            .doOnNext(FeignResponseValidate::check)
            .map(res -> RestUtils.buildListResponse(res.getData().stream().map(order -> {
                CorpInvoiceOrderVo vo = new CorpInvoiceOrderVo();
                BeanUtils.copyProperties(order, vo);
                if (DecimalUtils.isZero(order.getInvoicedAmount())) {
                    vo.setTaxStatus(TaxStatus.NO);
                } else if (DecimalUtils.eq(order.getInvoiceAmount(), order.getInvoiceAmount())) {
                    vo.setTaxStatus(TaxStatus.YES);
                } else {
                    vo.setTaxStatus(TaxStatus.PART);
                }
                return vo;
            }).collect(Collectors.toList()), res.getTotal()));
    }

    public ObjectResponse<OrderBiVo> chargeOrderBiForCorp(ListChargeOrderParam param) {
        if (null == param.getCorpId()) {
            throw new DcArgumentException("请提供企业客户ID");
        } else if (param.getCorpId() > 0L) {
            ObjectResponse<CorpPo> corpRes = this.authCenterFeignClient.getCorp(param.getCorpId());
            FeignResponseValidate.check(corpRes);
            if (CorpType.HLHT == corpRes.getData().getType()
                && param.getSettlementType() == null) {
                param.setSettlementType(SettlementType.PARTNER);
            }
        }
        param.setInCorpInvoice(true);
        param.setStatusList(List.of(OrderStatus.ORDER_STATUS_RECEIVE_MONEY)); // 已结算的充电订单
        return dataCoreFeignClient.chargeOrderBiForCorp(param);
    }

    public Mono<ObjectResponse<OrderBiVo>> includeChargerOrderBi(ListChargeOrderParam param) {
        return dataCoreInvoiceFeignClient.includeChargerOrderBi(param);
    }

    public ListResponse<ChargerOrderSite> chargerOrderGroupBySite(ListChargeOrderParam param) {
        // 产品: 默认是预付费的充电订单 20200806
        // 前端入参: siteIdList, orderNo, 时间分段信息, corpId, applyNo

        // 新增获取: corpId, billNo = ''(不关联账单), invoicedAmount == 0(已开票金额为0)
        if (null == param.getCorpId()) {
            throw new DcArgumentException("请提供企业客户ID");
        } else if (param.getCorpId() > 0L) {
            ObjectResponse<CorpPo> corpRes = this.authCenterFeignClient.getCorp(param.getCorpId());
            FeignResponseValidate.check(corpRes);
            if (CorpType.HLHT == corpRes.getData().getType()
                && param.getSettlementType() == null) {
                param.setSettlementType(SettlementType.PARTNER);
            }
        }
        param.setInCorpInvoice(true);
        param.setStatusList(List.of(OrderStatus.ORDER_STATUS_RECEIVE_MONEY)); // 已结算的充电订单

        return dataCoreFeignClient.chargerOrderGroupBySite(param);
    }

    public ListResponse<OrderTimeShareBiVo> chargerOrderGroupByTimeShareFee(
        ListChargeOrderParam param) {
        if (null == param.getCorpId()) {
            throw new DcArgumentException("请提供企业客户ID");
        } else if (param.getCorpId() > 0L) {
            ObjectResponse<CorpPo> corpRes = this.authCenterFeignClient.getCorp(param.getCorpId());
            FeignResponseValidate.check(corpRes);
            if (CorpType.HLHT == corpRes.getData().getType()
                && param.getSettlementType() == null) {
                param.setSettlementType(SettlementType.PARTNER);
            }
        }
        param.setInCorpInvoice(true);
        param.setStatusList(List.of(OrderStatus.ORDER_STATUS_RECEIVE_MONEY)); // 已结算的充电订单

        return dataCoreFeignClient.chargerOrderGroupByTimeShareFee(param);
    }


    public ListResponse<PayBillInvoiceBi> getPayBillOrderList(ListPayBillParam param) {
        // 查询使用: userId, applyNo
        if (null == param.getUserId()) {
            throw new DcArgumentException("请提供企业客户用户ID");
        }

        PayBillParam billParam = new PayBillParam();
        billParam.setCommIdChain(param.getCommIdChain());
        billParam.setUserId(param.getUserId());
        billParam.setOrderId(param.getOrderId());
        billParam.setApplyNo(param.getApplyNo());
        billParam.setPayTimeFilter(param.getPayTimeFilter());
        billParam.setInCorpInvoice(true);
        billParam.setFlowTypeList(List.of(DepositFlowType.IN_FLOW));
        billParam.setAccountTypeList(List.of(PayAccountType.CORP)); // 查询企业客户的充值
        billParam.setTaxStatus(StringUtils.isBlank(param.getApplyNo()) ?
            List.of(TaxStatus.NO) : List.of(TaxStatus.NO, TaxStatus.YES)); // 未开票
        billParam.setOrderIdList(param.getOrderIdList());
        billParam.setExclusiveOrderIdList(param.getExclusiveOrderIdList());
        billParam.setSorts(param.getSorts());

        if (null != param.getTotal() && param.getTotal()) {
            if (param.getStart() == null) {
                param.setStart(0L);
            }

            if (param.getSize() == null || param.getSize() <= 0) {
                param.setSize(10);
            }

            billParam.setIndex((int) (param.getStart() / param.getSize() + 1));
            billParam.setSize(param.getSize());
        }

        ListResponse<PayBillInvoiceBi> res = dataCoreFeignClient.invoiceOrderList(billParam);
        FeignResponseValidate.check(res);

        // 调整开票状态
//        res.getData().forEach(vo -> {
//            if (DecimalUtils.isZero(vo.getInvoicedAmount())) {
//                vo.setTaxStatus(TaxStatus.NO);
//            } else if (DecimalUtils.eq(vo.getAmount(), vo.getInvoicedAmount())) {
//                vo.setTaxStatus(TaxStatus.YES);
//            } else {
//                vo.setTaxStatus(TaxStatus.PART);
//            }
//            vo.setTotalAmount(vo.getAmount().add(vo.getFreeAmount()));
//        });
        return res;
    }

    public ObjectResponse<OrderBiVo> invoiceOrderBi(ListPayBillParam param) {
        // 查询使用: userId, applyNo
        if (null == param.getUserId()) {
            throw new DcArgumentException("请提供用户ID");
        }

        PayBillParam billParam = new PayBillParam();
        billParam.setCommIdChain(param.getCommIdChain());
        billParam.setUserId(param.getUserId());
        billParam.setOrderId(param.getOrderId());
        billParam.setApplyNo(param.getApplyNo());
        billParam.setPayTimeFilter(param.getPayTimeFilter());
//        billParam.setInCorpInvoice(true);
        billParam.setFlowTypeList(List.of(DepositFlowType.IN_FLOW));
        billParam.setAccountTypeList(param.getAccountTypeList()); // 查询企业客户的充值
        billParam.setTaxStatus(List.of(TaxStatus.NO, TaxStatus.PART));

        return dataCoreFeignClient.invoiceOrderBiForCorp(billParam);
    }

    public ObjectResponse<OrderBiVo> invoiceOrderBiForCorp(ListPayBillParam param) {
        // 查询使用: userId, applyNo
        if (null == param.getUserId()) {
            throw new DcArgumentException("请提供企业客户用户ID");
        }

        PayBillParam billParam = new PayBillParam();
        billParam.setCommIdChain(param.getCommIdChain());
        billParam.setUserId(param.getUserId());
        billParam.setOrderId(param.getOrderId());
        billParam.setApplyNo(param.getApplyNo());
        billParam.setPayTimeFilter(param.getPayTimeFilter());
        billParam.setInCorpInvoice(true);
        billParam.setFlowTypeList(List.of(DepositFlowType.IN_FLOW));
        billParam.setAccountTypeList(List.of(PayAccountType.CORP)); // 查询企业客户的充值
        billParam.setTaxStatus(StringUtils.isBlank(param.getApplyNo()) ?
            List.of(TaxStatus.NO) : List.of(TaxStatus.NO, TaxStatus.YES)); // 未开票

        return dataCoreFeignClient.invoiceOrderBiForCorp(billParam);
    }

    public ListResponse<SettlementVo> getSettlementOrderList(ListSettlementParam param) {
        if (null == param.getCorpId()) {
            throw new DcArgumentException("请提供企业客户ID");
        }
        param.setInCorpInvoice(true);
        param.setStatusList(List.of(SettlementStatusEnum.PAID));

        ListResponse<InvoiceRecordOrderRefDto> res = null;
        if (StringUtils.isNotBlank(param.getApplyNo())) {
            // -> dataCore: 获取开票关联的订单列表
            ListInvoiceRecordOrderRefParam refParam = new ListInvoiceRecordOrderRefParam();
            refParam.setApplyNo(param.getApplyNo())
                .setTotal(param.getTotal())
                .setSize(param.getSize())
                .setStart(param.getStart())
                .setSk(param.getSk());
            res = dataCoreFeignClient.getInvoiceRecordOrderList(refParam);
            FeignResponseValidate.check(res);

            // 开票关联订单为空，则返回空列表
            if (CollectionUtils.isEmpty(res.getData())) {
                return RestUtils.buildListResponse(List.of(), 0L);
            }

            // 账单列表
            param.setBillNoList(res.getData().stream()
                .map(InvoiceRecordOrderRefDto::getOrderNo).collect(Collectors.toList()));
        }

        // -> user: 获取账单列表
        ListResponse<SettlementVo> result = corpSettlementFeignClient.findSettlementList(param);
        result.getData().forEach(vo -> {
            if (DecimalUtils.isZero(vo.getInvoicedAmount())) {
                vo.setTaxStatus(TaxStatus.NO);
            } else if (DecimalUtils.eq(vo.getSettlementTotalFee(), vo.getInvoicedAmount())) {
                vo.setTaxStatus(TaxStatus.YES);
            } else {
                vo.setTaxStatus(TaxStatus.PART);
            }
        });
        return RestUtils.buildListResponse(result.getData(),
            null == res ? result.getTotal() : res.getTotal());
    }

    public ObjectResponse<OrderBiVo> settlementBiForCorp(ListSettlementParam param) {
        if (null == param.getCorpId()) {
            throw new DcArgumentException("请提供企业客户ID");
        }
        param.setInCorpInvoice(true);
        param.setStatusList(List.of(SettlementStatusEnum.PAID));

        ListResponse<InvoiceRecordOrderRefDto> res = null;
        if (StringUtils.isNotBlank(param.getApplyNo())) {
            // -> dataCore: 获取开票关联的订单列表
            ListInvoiceRecordOrderRefParam refParam = new ListInvoiceRecordOrderRefParam();
            refParam.setApplyNo(param.getApplyNo())
                .setTotal(param.getTotal())
                .setSize(param.getSize())
                .setStart(param.getStart())
                .setSk(param.getSk());
            res = dataCoreFeignClient.getInvoiceRecordOrderList(refParam);
            FeignResponseValidate.check(res);

            // 开票关联订单为空，则返回空列表
            if (CollectionUtils.isEmpty(res.getData())) {
                OrderBiVo orderBiVo = new OrderBiVo();
                return RestUtils.buildObjectResponse(orderBiVo);
            }

            // 账单列表
            param.setBillNoList(res.getData().stream()
                .map(InvoiceRecordOrderRefDto::getOrderNo).collect(Collectors.toList()));
        }

        // -> user: 获取账单汇总信息
        return corpSettlementFeignClient.settlementBiForCorp(param);

    }

    public ListResponse<ChargerOrderSite> recordOrderGroupBySite(ListSettlementParam param) {
        ListInvoiceRecordOrderRefParam refParam = new ListInvoiceRecordOrderRefParam();
        refParam.setApplyNo(param.getApplyNo());
        return dataCoreFeignClient.recordOrderGroupBySite(refParam);
    }

    public ListResponse<OrderTimeShareBiVo> recordOrderGroupByTimeShareFee(
        ListSettlementParam param) {
        ListInvoiceRecordOrderRefParam refParam = new ListInvoiceRecordOrderRefParam();
        refParam.setApplyNo(param.getApplyNo());
        return dataCoreFeignClient.recordOrderGroupByTimeShareFee(refParam);
    }

    public ObjectResponse<Integer> deleteCorpInvoiceRecordByApplyNo(String applyNo) {
        if (StringUtils.isBlank(applyNo)) {
            throw new DcArgumentException("申请单号不能为空");
        }

        return dataCoreFeignClient.deleteCorpInvoiceRecordByApplyNo(applyNo);
    }

    public ObjectResponse<Integer> updateCorpInvoiceRecordReturnFlag(CorpInvoiceRecordPo po) {
        if (StringUtils.isBlank(po.getApplyNo())) {
            throw new DcArgumentException("申请单号不能为空");
        }

        return dataCoreFeignClient.updateCorpInvoiceRecordReturnFlag(po);
    }

    public ObjectResponse<Integer> corpInvoiceRecordSubmit2Audit(CorpInvoiceRecordDto dto) {
        // 参数校验
        CorpInvoiceRecordDto.submitCheck(dto);

        CorpInvoiceInfoVo corpInvoiceInfo = this.getCorpInvoiceInfo(dto.getCorpId(), null);

        if (Boolean.TRUE.equals(dto.getBillingProcessRequest())) {
            // 企客对账开票流程后台发起的请求不检查开票方式
        } else {
            // 企业客户配置信息: 开票方式不一致不让提交
            if (!corpInvoiceInfo.getInvoiceOpen() ||
                !corpInvoiceInfo.getInvoiceWay().equals(dto.getInvoiceWay())) {
                // 更新为原来的开票方式，可以避免
                throw new DcArgumentException("企业开票信息已变更，建议删除订单后重新创建开票申请");
            }
        }

        // 重新更新开票信息
        dto.setInvoiceType(corpInvoiceInfo.getInvoiceType())
            .setProductTempId(corpInvoiceInfo.getProductTempId())
            .setModelId(corpInvoiceInfo.getModelId())
            .setTempSalId(corpInvoiceInfo.getTempSalId());
        return dataCoreFeignClient.corpInvoiceRecordSubmit2Audit(dto);
    }

    public ObjectResponse<ExcelPosition> exportCorpInvoiceOrder(String applyNo) {
        if (StringUtils.isBlank(applyNo)) {
            throw new DcArgumentException("企业开票申请单号无效");
        }

        return biInvoiceFeignClient.exportCorpInvoiceOrder(applyNo)
            .doOnNext(res -> log.info("企业开票涉及的订单导出: position = {}",
                JsonUtils.toJsonString(res.getData())))
            .block(Duration.ofSeconds(50L));
    }

    /**
     * 我的开票模板列表
     *
     * @param param
     * @return
     */
    public ListResponse<InvoicedModelVo> getInvoicedModelsByUserIdAndTypeSortable(
        InvoicedModelSearchParam param) {
        log.info("我的开票模板列表的请求参数: {}", JsonUtils.toJsonString(param));
        ListResponse<InvoicedModelVo> res =
            invoiceFeignClient.getInvoicedModelsByUserIdAndTypeSorted(param);
        if (res.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            FeignResponseValidate.check(res);
        }
        log.info("查询用户开票模板列表结果: size = {}, total = {}", res.getData().size(),
            res.getTotal());
        return res;
    }

    public ObjectResponse<InvoicedModelDTO> getCorpInvoiceModel(Long userId) {
        return invoiceFeignClient.getCorpInvoiceModel(userId);
    }
}
