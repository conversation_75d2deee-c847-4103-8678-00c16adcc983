package com.cdz360.biz.ant.rest.oa;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.reactor.OaFeignClient;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.oa.param.CreateProcessInstanceParam;
import com.cdz360.biz.oa.param.ListFlowDefinitionParam;
import com.cdz360.biz.oa.vo.ProcessDefinitionVo;
import com.cdz360.biz.oa.vo.ProcessInstanceVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/oa/process-definitions")
public class OaProcDefinitionRest {

    @Autowired
    private OaFeignClient oaFeignClient;

    @ApiOperation(value = "流程定义列表")
    @PostMapping(value = "/findList")
    public Mono<ListResponse<ProcessDefinitionVo>> processDefinitions(
            ServerHttpRequest request, @RequestBody ListFlowDefinitionParam param) {
        log.info("获取流程定义列表: {}", JsonUtils.toJsonString(param));
        final Long topCommId = AntRestUtils.getTopCommId(request);
        param.setTenantId(null != topCommId ? topCommId.toString() : "");
        return this.oaFeignClient.processDefinitions(BeanMap.create(param));
    }

    @ApiOperation(value = "获取流程启动需要的表单数据")
    @GetMapping(value = "/startForm")
    public Mono<ObjectResponse<String>> procDefStartForm(
            ServerHttpRequest request, @RequestParam String procDefId) {
        log.info("获取流程启动需要的表单数据: {}", LoggerHelper2.formatEnterLog(request));
        if (StringUtils.isBlank(procDefId)) {
            throw new DcArgumentException("流程定义ID不能为空");
        }
        return this.oaFeignClient.getProcessDefinitionStartForm(procDefId)
                .map(RestUtils::buildObjectResponse);
    }

    @ApiOperation(value = "启动新的流程实例")
    @PostMapping(value = "/startProcessInstances")
    public Mono<ObjectResponse<ProcessInstanceVo>> startNewProcessInstance(
            ServerHttpRequest request,
            @RequestBody CreateProcessInstanceParam param) {
        log.info("启动新的流程实例: param = {}", JsonUtils.toJsonString(param));
        final Long topCommId = AntRestUtils.getTopCommId(request);
        if (null == topCommId) throw new DcArgumentException("商户信息无效");
        param.setTenantId(topCommId.toString())
                .setTenantIdChain(AntRestUtils.getCommIdChain(request))
                .setCreateByUid(AntRestUtils.getSysUid(request).toString());
        return this.oaFeignClient.startNewProcessInstance(param);
    }

    @ApiOperation(value = "获取流程定义信息")
    @GetMapping(value = "/info")
    public Mono<ObjectResponse<ProcessDefinitionVo>> procDefInfo(
            ServerHttpRequest request, @RequestParam String procDefId) {
        log.info("获取流程启动需要的表单数据: {}", LoggerHelper2.formatEnterLog(request));
        if (StringUtils.isBlank(procDefId)) {
            throw new DcArgumentException("流程定义ID不能为空");
        }
        return this.oaFeignClient.getProcessDefinitionInfo(procDefId)
                .map(RestUtils::buildObjectResponse);
    }

}
