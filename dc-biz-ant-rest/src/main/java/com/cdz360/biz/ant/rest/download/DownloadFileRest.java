package com.cdz360.biz.ant.rest.download;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.ant.service.download.DownloadFileService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.download.param.DownloadFileParam;
import com.cdz360.biz.model.download.vo.DownloadJobVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import java.util.Locale;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "文件下载，文件导出", description = "文件下载/导出")
@RestController
@Slf4j
public class DownloadFileRest {

    @Autowired
    private DownloadFileService downloadFileService;

    @Operation(summary = "获取当前用户下载任务列表")
    @GetMapping(value = "/api/download/userDownloadJob")
    public Mono<ListResponse<DownloadJobVo>> userDownloadJob(ServerHttpRequest request) {
        log.info("获取当前用户下载任务列表: {}", LoggerHelper2.formatEnterLog(request, false));
        Locale locale = AntRestUtils.getLocale(request);
        return downloadFileService.userDownloadJob(AntRestUtils.getSysUid(request), locale);
    }

    // 替换: /api/order/download
    @Operation(summary = "下载任务文件下载")
    @PostMapping(value = "/api/download/downloadFile")
    public Mono<Void> downloadFile(ServerHttpRequest request, ServerHttpResponse response,
        @RequestBody DownloadFileParam param) {
        log.info("下载任务文件下载: {}, param = {}", LoggerHelper2.formatEnterLog(request), param);
        return downloadFileService.downloadFile(param, response);
    }
}
