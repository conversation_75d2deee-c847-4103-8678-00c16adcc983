package com.cdz360.biz.ant.domain.request;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.domain.vo.PvRtDataGroupItem;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

@Data
@Accessors(chain = true)
@Schema(description = "光伏组串入参")
public class PvGroupParam {

    @Schema(description = "1：电压；2：电流", required = true)
    private Integer type;

    @Schema(required = true)
    private String siteId;

    @Schema(description = "PV组串", required = false)
    private List<Group> groupList;

    @Schema(required = true)
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date date;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

    public void selfCheck() {
        IotAssert.isTrue(type != null && StringUtils.isNotBlank(siteId)
            && date != null, "缺少必传入参");
        if (CollectionUtils.isNotEmpty(groupList)) {
            IotAssert.isTrue(groupList.stream()
                .allMatch(e -> e.getIdx() != null
                    && StringUtils.isNotBlank(e.getDno())
                    && e.getId() != null), "入参不符合逻辑");
            IotAssert.isTrue(groupList.stream()
                    .map(Group::getIdx).distinct().count() == groupList.size(),
                "入参不符合逻辑");
        }
    }

    @Data
    @Accessors(chain = true)
    public static class Group {

        /**
         * 与{@link PvRtDataGroupItem#idx}值一致
         */
        @Schema(description = "组串序号", required = true)
        private Integer idx;

        @Schema(description = "PV唯一编号", required = true)
        private String dno;

        @Schema(description = "PV从属储能ESS设备equipId", required = false)
        private Long ownEquipId;

        @Schema(description = "PV从属储能ESS设备号", required = false)
        private String essDno;

        @Schema(description = "PV组串ID", required = true)
        private Integer id;
    }

}
