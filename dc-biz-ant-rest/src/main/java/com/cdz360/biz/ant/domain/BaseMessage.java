package com.cdz360.biz.ant.domain;

//import com.alibaba.fastjson.JSON;
//import lombok.Data;
//
///**
// * 消息中心通用的消息类型
// * 实体中只包含消息指向的topic和消息体,如指定用户发送可使用子类集成，暂不实现
// * <AUTHOR>
// * @since Created on 16:15 2019/2/13.
// */
//@Data
//public class BaseMessage{
//    /**
//     * 所在的项目ID，当多项目使用时，使用此字段区分
//     */
//    private String appId;
//    /**
//     * 消息通知的对象
//     */
//    private String topic;
//    /**
//     * 消息内容-此处是用Object类型传递，如后期不满足条件再行处理
//     */
//    private Object message;
//
//    @Override
//    public String toString() {
//        return JsonUtils.toJsonString(this);
//    }
//
//}
