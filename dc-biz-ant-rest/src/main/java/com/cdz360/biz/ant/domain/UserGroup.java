/*
 *
 * UserGroup.java
 * Copyright(C) 2017-2020 fendo公司
 * @since 2018-11-26
 */
package com.cdz360.biz.ant.domain;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class UserGroup implements Serializable {

    /**
     * t_user_group
     */
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 分组名称
     */
    private String groupName;
    /**
     * 分组描述
     */
    private String groupDescrition;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 商户ID
     */
    private Long createBy;

}