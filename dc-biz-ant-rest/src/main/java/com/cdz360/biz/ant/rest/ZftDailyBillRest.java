package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.service.sysLog.ZftDailyBillLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.trading.bill.param.ListZftDailyBillParam;
import com.cdz360.biz.model.trading.bill.vo.ZftDailyBillVo;
import com.cdz360.biz.utils.feign.bill.DailyBillDataCoreFeignClient;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@Tag(name = "支付平台账单相关接口", description = "支付平台账单相关接口")
@RestController
public class ZftDailyBillRest {

    @Autowired
    private ZftDailyBillLogService zftDailyBillLogService;

    @Autowired
    private DailyBillDataCoreFeignClient dailyBillDataCoreFeignClient;

    @Operation(summary = "获取支付平台账单记录")
    @PostMapping(value = "/api/dailyBill/findAllZftDailyBill")
    public Mono<ListResponse<ZftDailyBillVo>> findAllZftDailyBill(
            ServerHttpRequest request,
            @RequestBody ListZftDailyBillParam param) {
        log.info("获取支付平台账单记录: {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        param.setCommIdChain(AntRestUtils.getCommIdChain(request));
        return dailyBillDataCoreFeignClient.findAllZftDailyBill(param);
    }

    @Schema(description = "对账重试 失败后可以重试")
    @GetMapping(value = "/api/dailyBill/retryCheckBill")
    public Mono<BaseResponse> retryCheckBill(
            ServerHttpRequest request,
            @Parameter(name = "直付商家名称") @RequestParam(value = "zftName") String zftName,
            @Parameter(name = "渠道") @RequestParam(value = "channel") PayChannel channel,
            @Parameter(name = "账期") @RequestParam(value = "billDate") String billDate,
            @Parameter(name = "对账单ID") @RequestParam(value = "dailyBillId") Long dailyBillId) {
        log.info("对账重试: {}", LoggerHelper2.formatEnterLog(request));
        Long topCommId = AntRestUtils.getTopCommId(request);
        return dailyBillDataCoreFeignClient.retryCheckBill(topCommId, dailyBillId)
                .doOnNext(res -> zftDailyBillLogService.retryCheckBill(request, zftName, channel, billDate));
    }
}
