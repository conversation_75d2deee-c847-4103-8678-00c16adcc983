package com.cdz360.biz.ant.rest.sys;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcServerException;
import com.cdz360.biz.auth.sys.param.ListSysMenuParam;
import com.cdz360.biz.auth.sys.vo.SysMenuVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/sys/menu")
public class SysMenuRest {


    @PostMapping("/getSysMenuList")
    @Operation(summary = "获取系统菜单列表")
    public ListResponse<SysMenuVo> getSysMenuList(ServerHttpRequest request, @RequestBody ListSysMenuParam param) {
        log.debug(LoggerHelper2.formatEnterLog(request));
        throw new DcServerException("等待6月版本实现");
//        return this.sysRoleService.getSysRoleList(param);
    }
}
