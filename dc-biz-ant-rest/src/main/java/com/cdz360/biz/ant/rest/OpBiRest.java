package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.domain.request.SiteGeoBiParam;
import com.cdz360.biz.ant.service.cus.CusBiService;
import com.cdz360.biz.ant.service.iot.IotBiService;
import com.cdz360.biz.ant.service.order.OrderBiService;
import com.cdz360.biz.ant.service.site.SiteBiService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.ess.model.data.param.DataBiParam;
import com.cdz360.biz.model.bi.site.SiteUtilization;
import com.cdz360.biz.model.cus.user.dto.CusBiDto;
import com.cdz360.biz.model.iot.param.SiteBiParam;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import com.cdz360.biz.model.trading.cus.vo.CusOrderBiVo;
import com.cdz360.biz.model.trading.iot.vo.EvsePowerBiVo;
import com.cdz360.biz.model.trading.iot.vo.PlugStatusBiVo;
import com.cdz360.biz.model.trading.iot.vo.PlugSupplyBiVo;
import com.cdz360.biz.model.trading.order.dto.GeoOrderBiDto;
import com.cdz360.biz.model.trading.order.dto.OrderStartTypeBiDto;
import com.cdz360.biz.model.trading.order.vo.ChargeOrderBiVo;
import com.cdz360.biz.model.trading.order.vo.OpSummaryBiVo;
import com.cdz360.biz.model.trading.site.po.SiteDailyBiPo;
import com.cdz360.biz.model.trading.site.vo.SiteOrderAccountData;
import com.cdz360.biz.model.trading.site.vo.SiteOrderBiVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "运营大屏接口", description = "运营大屏")
public class OpBiRest {

    // 平台上线日期
    private static final LocalDate ONLINE_DATE = LocalDate.of(2019, 1, 1);

    @Autowired
    private OrderBiService orderBiService;

    @Autowired
    private CusBiService cusBiService;

    @Autowired
    private IotBiService iotBiService;

    @Autowired
    private SiteBiService siteBiService;

    @Operation(summary = "获取运营大屏的顶部统计数据")
    @GetMapping("/api/bi/op/getSummaryBi")
    public Mono<ObjectResponse<OpSummaryBiVo>> getSummaryBi(ServerHttpRequest request,
        @RequestParam(required = false) String siteId,
        @RequestParam(required = false) Boolean count,
        @RequestParam(required = false) String bizTypeList) {
        log.debug(LoggerHelper2.formatEnterLog(request));
        OpSummaryBiVo result = new OpSummaryBiVo();
        LocalDateTime today = LocalDateTime.now();
        result.setDays(Duration.between(ONLINE_DATE.atStartOfDay(), today).toDays());
        List<Integer> bizTypes = null;
        if (StringUtils.isNotEmpty(bizTypeList)) {
            bizTypes = Arrays.asList(bizTypeList.split(",")).stream().map(Integer::valueOf).collect(
                Collectors.toList());
        }
        List<Integer> finalBizTypes = bizTypes;
        final String commIdChain = CollectionUtils.isEmpty(AntRestUtils.getSysUserGids(request)) ?
            AntRestUtils.getCommIdChainAndFilter(request) : null;
        return Mono.just(result)
            .filter(x -> Boolean.TRUE.equals(count))
            .flatMap(x -> orderBiService.getOrderBiOfCommChain(
                commIdChain, siteId, result, finalBizTypes))
            .map(RestUtils::buildObjectResponse)
            .switchIfEmpty(Mono.just(RestUtils.buildObjectResponse(result)));
    }

    @Operation(summary = "充电电力统计")
    @GetMapping("/api/bi/op/getEvsePowerBi")
    public ObjectResponse<EvsePowerBiVo> getEvsePowerBi(ServerHttpRequest request,
        @RequestParam(value = "siteId", required = false) String siteId) {
        EvsePowerBiVo result = this.iotBiService.getEvsePowerBi(
            AntRestUtils.getCommIdChainAndFilter(request), siteId);
        return RestUtils.buildObjectResponse(result);
    }

    @Operation(summary = "充电功率采集", description = "当天数据没有汇总(不能获取当天数据)")
    @PostMapping("/api/bi/op/getSiteDailyPower")
    public Mono<ListResponse<SiteDailyBiPo>> getSiteDailyPower(
        ServerHttpRequest request, @RequestBody DataBiParam param) {
        log.info("充电功率采集: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        param.setCommIdChain(AntRestUtils.getCommIdChain(request));
        return Mono.just(param)
            .map(p -> this.iotBiService.getSiteDailyPower(param));
    }


    @Operation(summary = "根据电流类型统计桩/枪数量")
    @GetMapping("/api/bi/op/getPlugSupplyBi")
    public ListResponse<PlugSupplyBiVo> getPlugSupplyBi(ServerHttpRequest request) {
        return this.iotBiService.getPlugSupplyBi(AntRestUtils.getCommIdChainAndFilter(request));
    }

    @Operation(summary = "根据状态统计桩/枪数量")
    @GetMapping("/api/bi/op/getPlugStatusBi")
    public ListResponse<PlugStatusBiVo> getPlugStatusBi(ServerHttpRequest request) {
        return this.iotBiService.getPlugStatusBi(AntRestUtils.getCommIdChainAndFilter(request));
    }

    @Operation(summary = "充电电量和营收")
    @PostMapping("/api/bi/op/getChargeOrderBiList")
    public ListResponse<ChargeOrderBiVo> getChargeOrderBiList(ServerHttpRequest request,
        @RequestParam(value = "siteId", required = false) String siteId,
        @RequestParam(value = "timeType") SiteBiSampleType timeType) {
        log.info("getChargeOrderBiList siteId: {}, timeType: {}", siteId, timeType);
        if (timeType == null) {
            timeType = SiteBiSampleType.HOUR;
        }
        String commIdChain = null;
        if (CollectionUtils.isEmpty(AntRestUtils.getSysUserGids(request))) {
            commIdChain = AntRestUtils.getCommIdChainAndFilter(request);
        }
        return this.orderBiService.getTimeGroupingOrderBiList(timeType, siteId, commIdChain);
    }

    @Operation(summary = "充电启动方式")
    @PostMapping("/api/bi/op/getOrderStartTypeBi")
    public ListResponse<OrderStartTypeBiDto> getOrderStartTypeBi(ServerHttpRequest request) {
        ListResponse<OrderStartTypeBiDto> res = this.orderBiService.getOrderStartTypeBiList7(
            AntRestUtils.getCommIdChainAndFilter(request));
        return res;
    }

    @Operation(summary = "用户周报")
    @GetMapping("/api/bi/op/getWeeklyUserBi")
    public ObjectResponse<CusBiDto> getWeeklyUserBi(ServerHttpRequest request) {
        var res = this.cusBiService.getWeeklyUserBi(AntRestUtils.getCommIdChainAndFilter(request));
        return res;
    }

    @Operation(summary = "近7/30日营收统计(按客户类型统计)")
    @GetMapping("/api/bi/op/getCusOrderBi")
    public ObjectResponse<CusOrderBiVo> getCusOrderBi(ServerHttpRequest request) {
        ObjectResponse<CusOrderBiVo> res = this.orderBiService.getAccTypeGroupingFeeBiList(
            AntRestUtils.getCommIdChainAndFilter(request));
        return res;
    }

    @Operation(summary = "站点地图分布图")
    @PostMapping("/api/bi/op/getSiteGeoBiList")
    public ListResponse<SiteOrderBiVo> getSiteGeoBiList(ServerHttpRequest request,
        @RequestBody SiteGeoBiParam param) {
        log.info(LoggerHelper2.formatEnterLog(request) + " param = {}", param);
        ListResponse<SiteOrderBiVo> res;
        List<String> gids = AntRestUtils.getSysUserGids(request);
        if (CollectionUtils.isNotEmpty(gids)) {

            if (CollectionUtils.isNotEmpty(param.getGids())) {

                if (!new HashSet<>(gids).containsAll(param.getGids())) {
                    // 若无权限则返回空数据
                    return RestUtils.buildListResponse(List.of());
                }
                gids = param.getGids();
            }
            res = this.siteBiService.getSiteGeoBiList(null, gids,
                param.getBizTypeList(), param.getCheckCamera(), param.getIncludeGroup());
        } else {
            res = this.siteBiService.getSiteGeoBiList(AntRestUtils.getCommIdChainAndFilter(request),
                null,
                param.getBizTypeList(), param.getCheckCamera(), param.getIncludeGroup());
        }
        return res;
    }

    @Operation(summary = "充电站充电量排名")
    @PostMapping("/api/bi/op/getSiteOrderBi")
    public ListResponse<SiteOrderBiVo> getSiteOrderBi(ServerHttpRequest request,
        @RequestBody SiteBiParam param) {
        log.info(LoggerHelper2.formatEnterLog(request) + " param = {}", param);
        var res = this.siteBiService.getSiteBiList(param,
            AntRestUtils.getCommIdChainAndFilter(request));
        return res;
    }

    @Operation(summary = "按城市统计场站营收")
    @PostMapping("/api/bi/op/getGeoOrderBi")
    public ObjectResponse<GeoOrderBiDto> getGeoOrderBi(ServerHttpRequest request,
        @RequestParam(required = false) String provinceCode,
        @RequestParam(required = false) String cityCode,
        @RequestParam(required = false) String siteId) {
        log.info(LoggerHelper2.formatEnterLog(request));
        ObjectResponse<GeoOrderBiDto> res = this.orderBiService.getGeoOrderBi(
            StringUtils.blank2Null(provinceCode),
            StringUtils.blank2Null(cityCode),
            StringUtils.blank2Null(siteId),
            StringUtils.blank2Null(AntRestUtils.getCommIdChainAndFilter(request)));
        return res;
    }

    @Operation(summary = "充电桩使用率")
    @GetMapping("/api/bi/op/getUsageRateBi")
    public ListResponse<SiteUtilization> getUsageRateBi(ServerHttpRequest request) {
        return this.orderBiService.getDateGroupingPlugUsageBiList(
            AntRestUtils.getCommIdChainAndFilter(request));
    }

    @Operation(summary = "近n天充电用户统计(按上传时间统计，不含当天)")
    @GetMapping(value = "/api/bi/op/orderAccountBi")
    public Mono<ListResponse<SiteOrderAccountData>> getOrderAccountBi(
        @RequestParam(value = "days") Integer days,
        @RequestParam(value = "siteId", required = false) String siteId) {
        log.debug("充电用户统计: days = {}, siteId = {}", days, siteId);
        return orderBiService.getOrderAccountBi(days, siteId);
    }

}
