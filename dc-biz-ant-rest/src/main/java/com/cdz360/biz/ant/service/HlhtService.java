package com.cdz360.biz.ant.service;

//
///**
// * <AUTHOR>
// * :
// * @since 2019-02-19 15:47
// */
//@Service
//public class HlhtService //implements IHlhtService
//{
//
//    @Autowired
//    private AntUserFeignClient userFeignClient;
//
//    /**
//     * 获取所有的运营商信息基本信息（客户运营商、设备运营商）
//     *
//     * @return
//     */
//
//    public List<OperatorInfoVo> queryAllOperatorInfo() {
//        ListResponse<OperatorInfoVo> listResponse = userFeignClient.queryAllOperators();
//        FeignResponseValidate.check(listResponse);
////        String operatorInfoString = jsonObject.getString("data");
////        List<OperatorInfoVo> operatorInfoVos = null;
////        if(StringUtils.isNotEmpty(operatorInfoString)){
////            operatorInfoVos = JSONArray.parseArray(operatorInfoString, OperatorInfoVo.class);
////        }
//        return listResponse.getData();
//    }
//
//    /**
//     * 新增运营商关系配置
//     *
//     * @param relOperator
//     * @return
//     */
//    public boolean insertBatchRelOperator(RelOperator relOperator) {
//        boolean response = false;
//        BaseResponse jsonObject = userFeignClient.insertBatchRelOperator(relOperator);
//        if (jsonObject != null) {
//            response = jsonObject.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS;
//        }
//        return response;
//    }
//}
