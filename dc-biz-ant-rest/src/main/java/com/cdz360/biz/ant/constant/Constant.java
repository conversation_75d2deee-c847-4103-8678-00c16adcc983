package com.cdz360.biz.ant.constant;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * 常量定义
 */
public interface Constant {

    /**
     * 卡片类型-离线卡
     */
    int IS_PACKAGED_OFF_LINE = 3;
    /**
     * 卡片类型-鉴权卡
     */
    int IS_PACKAGED_AUTH = 4;

    /**
     * 卡状态：未激活
     */
    String CARD_STATUS_SLEEP = "10000" ;
    /**
     * 卡状态：正常
     */
    String CARD_STATUS_NORMAL = "10001" ;
    /**
     * 卡状态：已失效(黑名单)
     * <AUTHOR>
     *  调整卡状态，具体意义使用参照类 chargerlink-dev-ops 中的 CardStatus.java
     */
    String CARD_STATUS_BLACK = "10005" ;
    /**
     * 卡状态：已挂失
     * <AUTHOR>
     *  调整卡状态，具体意义使用参照类 chargerlink-dev-ops 中的 CardStatus.java
     */
    String CARD_STATUS_LOCK = "10002" ;
    /**
     * 卡有效标志1有效0无效
     */
    String CARD_YX_BZ = "0" ;

    /**
     * 卡类别1.实体,2.虚拟
     */
    int CARD_ENTITY = 1;
    /**
     * 卡类别1.实体,2.虚拟
     */
    int CARD_VIRTUAL = 2;
    /**
     * 卡片渠道-发卡方(1:商户自添加 2:平台制卡 3:虚拟卡生成）
     */
    String CARD_CHANNAL_OPERATOR = "1";
    /**
     * 卡片渠道-发卡方(1:商户自添加 2:平台制卡 3:虚拟卡生成）
     */
    String CARD_CHANNAL_MANAGER = "2";
    /**
     * 卡片渠道-发卡方(1:商户自添加 2:平台制卡 3:虚拟卡生成）
     */
    String CARD_CHANNAL_VIRTUAL = "3";

    /**
     * 禁用状态
     */
    int DISABLE = 0;

    /**
     * 启用状态
     */
    int ENABLE=1;
    /**
     * 商户账号：为管理员身份
     */
    long PID=0;
    /**
     * 商户账号：账号为已经修改
     */
    int IS_UPDATE=1;
    /**
     * 商户账号：账号为未修改
     */
    int NOT_UPDATE=0;

    Integer PAY_PERSON_TYPE = 1;//个人账户支付

    Integer PAY_BLOC_TYPE = 2;//集团授信账户支付

//    /**
//     * mq队列名，搭配GUID生成唯一的队列名
//     */
//    String MQ_QUEUE_PLUG_STATUS = "websocket.plug";
//
//    /**
//     * mq队列名，搭配GUID生成唯一的队列名
//     */
//    String MQ_QUEUE_ORDER_STATUS = "websocket.order";
//
//    /**
//     * websocket监听地址
//     */
//    String WS_TOPIC_PLUG_STATUS_SITE = "/topic/plugStatus/site/";

    /**
     * 光伏发电电量相关转换规则
     */
    BigDecimal ruleCoal = new BigDecimal("0.333"); // 1kw·h = 0.333kg(煤)

    BigDecimal ruleCo2 = new BigDecimal("0.785"); // 1kw·h = 0.785kg(Co2)

    BigDecimal ruleSo2 = new BigDecimal("64.38"); // 1kw·h = 64.38g(So2)

    BigDecimal ruleTree = new BigDecimal(500); // 500kg(Co2) = 1棵(树)

}