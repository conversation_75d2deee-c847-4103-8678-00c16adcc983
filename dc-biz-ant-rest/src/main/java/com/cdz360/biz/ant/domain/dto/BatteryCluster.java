package com.cdz360.biz.ant.domain.dto;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * BatteryCluster
 *
 * @since 10/12/2021 7:53 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BatteryCluster {

    @Schema(description = "电池簇号 第N簇电池信息 register adrress = 第0簇电池信息 register adrress + 50* N)\n" +
                    "N代表簇号N(0,ClusterNum) ClusterNum参考583")
    private Long clusterNo;

    @Schema(description = "设备ID(ess内唯一)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long equipId;

    //簇电压
    @JsonProperty("cv")
    private BigDecimal clusterVoltage;
    //簇电流
    @JsonProperty("cc")
    private BigDecimal clusterCurrent;
    //电池剩余电量
    @JsonProperty("soc")
    private BigDecimal soc;
    //电池健康状态
    @JsonProperty("soh")
    private BigDecimal soh;
    //绝缘阻抗
    @JsonProperty("ir")
    private Integer insulatedResistance;
    // 单体最低电压, 0.001V
    @JsonProperty("minMV")
    private BigDecimal minMonomerVoltage;
    // 单体最低电压簇号. Minimum voltage cluster number of the monomer
    @JsonProperty("minMVClstN")
    private Integer minMonomerVoltageClusterNo;
    // 单体最低电压电池号. Single lowest voltage battery number
    @JsonProperty("minMVBN")
    private Integer minMonomerVoltageBatteryNo;
    // 单体最低电压电芯号. Minimum voltage cell number
    @JsonProperty("minMVCellN")
    private Integer minMonomerVoltageCellNo;
    // 单体最高电压, 0.001V
    @JsonProperty("maxMV")
    private BigDecimal maxMonomerVoltage;
    // 单体最高电压簇号. The highest voltage cluster number of the monomer
    @JsonProperty("maxMVClstN")
    private Integer maxMonomerVoltageClusterNo;
    // 单体最高电压电池号. Maximum single voltage battery number
    @JsonProperty("maxMVBN")
    private Integer maxMonomerVoltageBatteryNo;
    // 单体最高电压电芯号. Maximum voltage cell number
    @JsonProperty("maxMVCellN")
    private Integer maxMonomerVoltageCellNo;
    // 电池最低温度, 0.1℃
    @JsonProperty("minBT")
    private BigDecimal minBatteryTemp;
    // 单体最低温度簇号
    @JsonProperty("minBTClstN")
    private Integer minBatteryTempClusterNo;
    // 单体最低温度电池号
    @JsonProperty("minBTBN")
    private Integer minBatteryTempBatteryNo;
    // 单体最低温度电芯号
    @JsonProperty("minBTCellN")
    private Integer minBatteryTempCellNo;
    // 电池最高温度, 0.1℃
    @JsonProperty("maxBT")
    private BigDecimal maxBatteryTemp;
    // 单体最高温度簇号
    @JsonProperty("maxBTClstN")
    private Integer maxBatteryTempClusterNo;
    // 单体最高温度电池号
    @JsonProperty("maxBTBN")
    private Integer maxBatteryTempBatteryNo;
    // 单体最高温度电芯号
    @JsonProperty("maxBTCellN")
    private Integer maxBatteryTempCellNo;

    @Schema(description = "电池组数据")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<BatteryPack> packList;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}