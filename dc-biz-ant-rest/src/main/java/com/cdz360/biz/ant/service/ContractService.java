package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.charge.vo.SiteVo;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorSiteDataCoreFeignClient;
import com.cdz360.biz.model.trading.contract.param.AddContractParam;
import com.cdz360.biz.model.trading.contract.param.ContractListParam;
import com.cdz360.biz.model.trading.contract.vo.ContractVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;


@Slf4j
@Service
public class ContractService {
    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    ReactorSiteDataCoreFeignClient reactorSiteDataCoreFeignClient;


    public BaseResponse addContract(AddContractParam param) {
        return dataCoreFeignClient.addContract(param);
    }

    public BaseResponse delContract(Long id, String idChain, Long sysUid) {
        return dataCoreFeignClient.delContract(id, idChain, sysUid);
    }

    public BaseResponse updateContract(AddContractParam param) {
        return dataCoreFeignClient.updateContract(param);
    }

    public ObjectResponse<ContractVo> getContractById(Long contractId, String idChain) {
        return dataCoreFeignClient.getContractById(contractId, idChain);
    }

    public ListResponse<ContractVo> getContractList(ContractListParam param) {
        return dataCoreFeignClient.getContractList(param);
    }

    public Mono<ListResponse<ContractVo>> getContractBySiteId(String siteId, Long size) {
        return reactorSiteDataCoreFeignClient.getContractBySiteId(siteId, size);
    }

    public Mono<ListResponse<SiteVo>> getSiteListByContractId(Long contractId) {
        return reactorSiteDataCoreFeignClient.getSiteListByContractId(contractId);
    }


}
