package com.cdz360.biz.ant.domain.bundle;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "控制器升级参数")
@Data
@Accessors(chain = true)
public class UpgradeParam {

    @Schema(description = "控制器编号列表", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> gwnoList;

    @Schema(description = "关联的包id(t_evse_bundle.id)", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long bundleId;
}
