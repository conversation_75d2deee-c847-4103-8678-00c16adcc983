package com.cdz360.biz.ant.service.parts;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.feign.DeviceMgmFeignClient;
import com.cdz360.biz.model.common.constant.ExcelCheckResult;
import com.cdz360.biz.model.cus.param.SysUserCheckParam;
import com.cdz360.biz.model.parts.param.PartsCheckParam;
import com.cdz360.biz.model.parts.vo.PartsImportItem;
import com.cdz360.biz.model.parts.vo.PartsImportVo;
import com.chargerlinkcar.framework.common.service.excel.BufferedExcelConvertor;
import com.chargerlinkcar.framework.common.service.excel.ExcelConvertor;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PartsExcelConvertor extends ExcelConvertor<PartsImportVo> {

    private static final Integer CELL_LENGTH = 6;

    @Autowired
    private BufferedExcelConvertor excelConvertor;
    @Autowired
    private DeviceMgmFeignClient deviceMgmFeignClient;
    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @PostConstruct
    public void init() {
        this.excelConvertor.addExcelConvertorMap(ExcelCheckResult.PARTS_VALID, this);
    }

    @Override
    public Integer getCellLength() {
        return CELL_LENGTH;
    }

    @Override
    public PartsImportVo parse(List<List<String>> lineList) {
        List<PartsImportItem> valid = new ArrayList<>(); // 有效
        List<PartsImportItem> invalid = new ArrayList<>(); // 无效
        lineList.forEach(item -> {
            PartsImportItem vo = new PartsImportItem();
            vo.setTypeName(
                null == item.get(0) || item.get(0) == "" ? null : item.get(0).trim()); // 物料名称
            vo.setTypeFullModel(
                null == item.get(1) || item.get(1) == "" ? null : item.get(1).trim()); // 规格型号
            vo.setTypeCode(
                null == item.get(2) || item.get(2) == "" ? null : item.get(2).trim()); // 物料编码
            vo.setOpName(
                null == item.get(3) || item.get(3) == "" ? null : item.get(3).trim()); // 所属运维人
            vo.setOpUsername(
                null == item.get(4) || item.get(4) == "" ? null : item.get(4).trim()); // 所属帐号
            vo.setAvailableStr(
                null == item.get(5) || item.get(5) == "" ? null : item.get(5).trim()); // 是否可用

            ExcelCheckResult checkResult = checkFormat(vo);
            if (checkResult.getCode() != ExcelCheckResult.PARTS_VALID.getCode()) {
                vo.setDetail(checkResult.getDesc());
                invalid.add(vo);
            } else {
                valid.add(vo);
                vo.fillAvailable();
            }
        });
        return new PartsImportVo(valid, invalid);
    }

    private ExcelCheckResult checkFormat(PartsImportItem item) {
        // 有效性检测
        if (StringUtils.isEmpty(item.getTypeName())
            || StringUtils.isEmpty(item.getTypeFullModel())
            || StringUtils.isEmpty(item.getTypeCode())
            || StringUtils.isEmpty(item.getAvailableStr())) {
            return ExcelCheckResult.PARTS_INVALID;
        }

        // available 必须为 可用或已坏
        if (!item.getAvailableStr().equals("可用")
            && !item.getAvailableStr().equals("已坏")) {
            return ExcelCheckResult.PARTS_AVAILABLE_ERROR;
        }

        return ExcelCheckResult.PARTS_VALID;
    }

    @Override
    public PartsImportVo verify(PartsImportVo t) {
        PartsImportVo importVo = checkInDatebase(t.getValid(), t.getInvalid());
        log.info("valid.size = {}, invalid.size = {}", importVo.getValid().size(),
            importVo.getInvalid().size());
        return importVo;
    }

    public PartsImportVo checkInDatebase(List<PartsImportItem> itemList,
        List<PartsImportItem> invalid) {
        PartsImportVo ListVo = new PartsImportVo();
        List<PartsImportItem> valid = new ArrayList<>(); // 新的有效数据集合

        if (CollectionUtils.isEmpty(itemList)) {
            ListVo.setValid(valid);
            ListVo.setInvalid(invalid);
            return ListVo;
        }

        List<PartsCheckParam> params = itemList.stream().map(e -> {
            PartsCheckParam temp = new PartsCheckParam();
            temp.setTypeName(e.getTypeName());
            temp.setTypeFullModel(e.getTypeFullModel());
            temp.setTypeCode(e.getTypeCode());
            return temp;
        }).distinct().collect(Collectors.toList());
        ListResponse<PartsCheckParam> response = deviceMgmFeignClient.checkInDB(params);
        FeignResponseValidate.check(response);
        Map<String, PartsCheckParam> partsMap = response.getData().stream()
            .collect(Collectors.toMap(PartsCheckParam::getTypeCode, o -> o, (v1, v2) -> v1));

        List<SysUserCheckParam> userCheckParam = itemList.stream().map(e -> {
            SysUserCheckParam temp = new SysUserCheckParam();
            temp.setName(e.getOpName());
            temp.setUsername(e.getOpUsername());
            return temp;
        }).distinct().collect(Collectors.toList());
        ListResponse<SysUserCheckParam> userResponse = authCenterFeignClient.checkInDB(
            userCheckParam);
        FeignResponseValidate.check(userResponse);
        Map<String, SysUserCheckParam> userMap = userResponse.getData().stream()
            .collect(Collectors.toMap(SysUserCheckParam::getUsername, o -> o, (v1, v2) -> v1));

        itemList.forEach(item -> {
            ExcelCheckResult checkResult = null;
            PartsCheckParam partsCheckRes = partsMap.get(item.getTypeCode());
            SysUserCheckParam userCheckRes =
                StringUtils.isNotBlank(item.getOpUsername()) ? userMap.get(item.getOpUsername())
                    : null;

            if (Boolean.FALSE.equals(partsCheckRes.getIsSubsistent())) {
                checkResult = ExcelCheckResult.PARTS_NO_NOT_EXIST;
            } else if (StringUtils.isNotBlank(item.getOpUsername())
                && userCheckRes != null
                && Boolean.FALSE.equals(userCheckRes.getIsSubsistent())) {
                checkResult = ExcelCheckResult.PARTS_USER_NO_NOT_EXIST;
            } else {
                item.setTypeId(partsCheckRes.getPartsTypeId());
                item.setOpId(userCheckRes != null ? userCheckRes.getSysUserId() : null);
                checkResult = ExcelCheckResult.PARTS_VALID;
            }
            item.setDetail(checkResult.getDesc());
            if (checkResult.getCode() != ExcelCheckResult.PARTS_VALID.getCode()) {
                invalid.add(item);
            } else {
                valid.add(item);
            }
        });
        ListVo.setValid(valid);
        ListVo.setInvalid(invalid);
        return ListVo;
    }

}
