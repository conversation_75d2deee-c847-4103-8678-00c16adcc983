package com.cdz360.biz.ant.domain;
//
//import lombok.Data;
//
//import java.io.Serializable;
//import java.math.BigDecimal;
//import java.util.Date;
//
///**
// * <AUTHOR>
// */
//@Data
//public class Balance implements Serializable {
//    /**
//     *
//     */
//    private String id;
//
//    /**
//     * 客户ID
//     */
//    private String userId;
//
//    /**
//     * 账户状态（1：冻结；0:正常；）
//     */
//    private Integer status;
//
//    /**
//     * 金额(单位为元)
//     */
//    private BigDecimal balance;
//
//    /**
//     * 冻结金额
//     */
//    private BigDecimal frozenAmount;
//
//    /**
//     * 当前活跃卡号
//     */
//    private Long cardId;
//
//    /**
//     * 积分
//     */
//    private Long integral;
//
//    /**
//     * 创建时间
//     */
//    private Date createTime;
//
//    /**
//     * 保证金
//     */
//    private Long bond;
//
//    /**
//     * 赠送金
//     */
//    private BigDecimal freeGold;
//
//    /**
//     * 个人现金账户（集团商户id）
//     */
//    private Long commId;
//
//    /**
//     * t_balance
//     */
//    private static final long serialVersionUID = 1L;
//
//}