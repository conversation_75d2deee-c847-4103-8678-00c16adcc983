package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.trading.profit.sett.param.ListProfitCfgParam;
import com.cdz360.biz.model.trading.profit.sett.param.SaveProfitCfgParam;
import com.cdz360.biz.model.trading.profit.sett.vo.ProfitCfgVo;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE,
    fallbackFactory = DataCoreProfitFeignHystrix.class)
public interface DataCoreProfitFeignClient {

    // 更新收益配置
    @PostMapping(value = "/dataCore/profitCfg/saveCfg")
    Mono<ObjectResponse<ProfitCfgVo>> saveGcProfitCfg(
        @RequestBody SaveProfitCfgParam param);

    // 收益配置停用或启用
    @GetMapping(value = "/dataCore/profitCfg/enableCfg")
    Mono<ObjectResponse<ProfitCfgVo>> enableGcProfitCfg(
        @RequestParam("id") Long id,
        @RequestParam("enable") Boolean enable);

    // 删除收益配置(物理删除)
    @DeleteMapping(value = "/dataCore/profitCfg/deleteCfg")
    Mono<ObjectResponse<ProfitCfgVo>> deleteGcProfitCfg(
        @RequestParam("id") Long id);

    // 获取收益配置列表
    @PostMapping(value = "/dataCore/profitCfg/findAll")
    Mono<ListResponse<ProfitCfgVo>> findGcProfitCfg(
        @RequestBody ListProfitCfgParam param);

    // 获取收益配置(无效ID返回data为null)
    @GetMapping(value = "/dataCore/profitCfg/getCfg")
    Mono<ObjectResponse<ProfitCfgVo>> getGcProfitCfg(
        @RequestParam("id") Long id);
}
