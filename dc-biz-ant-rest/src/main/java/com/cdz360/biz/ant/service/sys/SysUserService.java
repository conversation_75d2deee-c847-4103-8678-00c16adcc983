package com.cdz360.biz.ant.service.sys;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.domain.vo.SysRole;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.reactor.OaFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorAuthCenterFeignClient;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.ant.utils.RedisUtil;
import com.cdz360.biz.auth.sys.param.BatchAddRoleUserParam;
import com.cdz360.biz.auth.sys.param.RoleUserListParam;
import com.cdz360.biz.auth.sys.param.RoleUserUpdateParam;
import com.cdz360.biz.auth.user.dto.SysUserLoginResult;
import com.cdz360.biz.auth.user.param.ListSysUserParam;
import com.cdz360.biz.auth.user.param.SysUserLoginParam;
import com.cdz360.biz.model.common.request.TokenRequest;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.sys.vo.SiteGroupVo;
import com.cdz360.biz.oa.dto.account.OaAccountDto;
import com.cdz360.biz.oa.param.account.ListOaGroupUserParam;
import com.cdz360.biz.utils.feign.hlht.OpenHlhtFeignClient;
import com.chargerlinkcar.framework.common.domain.SysUser;
import com.chargerlinkcar.framework.common.domain.TCommercialUser;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.WxDataCryptUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class SysUserService {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private OpenHlhtFeignClient openHlhtFeignClient;

    @Autowired
    private OaFeignClient oaFeignClient;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private ReactorAuthCenterFeignClient reactorAuthCenterFeignClient;

    public Mono<ObjectResponse<String>> wxworkJsCode2Session(Long topCommId, String code) {
        return openHlhtFeignClient.jsCode2Session(topCommId, code)
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .map(x -> {
                redisUtil.saveWxworkSession(x, code);
                return x.getUserId();
            })
            .map(RestUtils::buildObjectResponse);
    }

    /**
     * 系统用户登录
     *
     * @param param
     * @return
     */
    public ObjectResponse<SysUserLoginResult> sysUserLogin(SysUserLoginParam param) {
        if (null == param.getEnv()) {
            IotAssert.isNotBlank(param.getUsername(), "username:must not be blank");
            IotAssert.isNotBlank(param.getPassword(), "password:must not be blank");
        } else {
            IotAssert.isNotBlank(param.getEnv().getAppId(), "应用ID不能为空");
            IotAssert.isNotBlank(param.getEnv().getEnvCode(), "运行环境code不能为空");
            IotAssert.isNotBlank(param.getEnv().getUniqueId(), "用户在运行环境中的唯一键不能为空");
            IotAssert.isNotBlank(param.getEnv().getEncryptedData(), "加密数据不能为空");
            IotAssert.isNotBlank(param.getEnv().getIv(), "初始向量不能为空");

            val session = redisUtil.getWxworkSession(param.getEnv().getUniqueId(),
                param.getEnv().getEnvCode());
            IotAssert.isNotNull(session, "会话已经失效，请稍后重试");

            val mobile = WxDataCryptUtil.wxworkMobileDecrypt(
                param.getEnv().getAppId(), session.getSessionKey(),
                param.getEnv().getEncryptedData(), param.getEnv().getIv());
            IotAssert.isNotBlank(mobile, "无法获取有效手机号，请稍后重试");

            param.setMobile(mobile);
        }

        ObjectResponse<SysUserLoginResult> res = authCenterFeignClient.login(param);
        FeignResponseValidate.check(res);
        return res;
    }

    public BaseResponse sysUserLoginOut(TokenRequest tokenRequest) {
        return authCenterFeignClient.logout(tokenRequest);
    }

    public ObjectResponse<SysUser> getUserByToken(@RequestBody @Valid TokenRequest tokenRequest) {
        return authCenterFeignClient.getUserByToken(tokenRequest);
    }

    /**
     * 免密切换用户
     */
    public Mono<ObjectResponse<SysUserLoginResult>> switchUser(String token, Long targetSysUid) {
        if (null == targetSysUid) {
            throw new DcArgumentException("目标用户ID无效");
        }

        return Mono.just(authCenterFeignClient.switchUser(token, targetSysUid));
    }

    /**
     * 企业平台免密登陆登录
     *
     * @param corpId
     * @return
     */
    public ObjectResponse<String> getLoginTmpKey(ServerHttpRequest request, Long corpId,
        String idChain) {
        //SysUserLoginResult result = new SysUserLoginResult();

//        log.info("在idChain: {} 寻找corp: {}", idChain, corpId);

//        ListCorpParam listCorpParam = new ListCorpParam();
//        listCorpParam.setCommIdChain(idChain);
//        ListResponse<CorpVo> corpList = authCenterFeignClient.getCorpList(listCorpParam);
//        FeignResponseValidate.check(corpList);
//
//        IotAssert.isTrue(corpList.getData()
//                .stream()
//                .map(CorpVo::getId)
//                .collect(Collectors.toList())
//                .contains(corpId),
//                "该企业当前不属于此商户");

//        ObjectResponse<SysUserLoginResult> res =  authCenterFeignClient.authLogin(corpId, idChain);
        ObjectResponse<String> res = authCenterFeignClient.getLoginTmpKey(
            AntRestUtils.getToken2(request), corpId, idChain);
        FeignResponseValidate.check(res);
        return res;
    }

    public ObjectResponse<SysUserLoginResult> getLoginInfoByKey(ServerHttpRequest request,
        String key) {
        ObjectResponse<SysUserLoginResult> res = authCenterFeignClient.getLoginInFoByKey(
            AntRestUtils.getToken2(request), key);
        FeignResponseValidate.check(res);
        return res;
    }

    public BaseResponse getOpenId(ServerHttpRequest request, String code) {
        return authCenterFeignClient.getOpenId(AntRestUtils.getToken2(request), code);
    }

    public BaseResponse edit(ServerHttpRequest request, TCommercialUser tcu) {
        BaseResponse res = authCenterFeignClient.edit(AntRestUtils.getToken2(request), tcu);
        FeignResponseValidate.check(res);
        return res;
    }

    public BaseResponse add(ServerHttpRequest request, SysUser bodyEntity) {
        BaseResponse res = authCenterFeignClient.add(AntRestUtils.getToken2(request), bodyEntity);
        FeignResponseValidate.check(res);
        return res;
    }

    public BaseResponse modify(ServerHttpRequest request, SysUser bodyEntity) {
        BaseResponse res = authCenterFeignClient.modify(AntRestUtils.getToken2(request),
            bodyEntity);
        FeignResponseValidate.check(res);
        return res;
    }

    public BaseResponse changeState(ServerHttpRequest request, SysUser bodyEntity) {
        BaseResponse res = authCenterFeignClient.changeState(AntRestUtils.getToken2(request),
            bodyEntity);
        FeignResponseValidate.check(res);
        return res;
    }

    public ListResponse<SysRole> getRoleListByUserId(ServerHttpRequest request,
        RoleUserListParam params) {
        return authCenterFeignClient.getRoleListByUserId(AntRestUtils.getToken2(request), params);
    }

    public ListResponse<SysRole> getRoleByUserId(ServerHttpRequest request, String keyWord,
        Long platform, Long userId, Long size) {
        return authCenterFeignClient.getRoleByUserId(AntRestUtils.getToken2(request), keyWord,
            platform, userId, size);
    }

    public BaseResponse batchUpdateRoleUserByUserId(ServerHttpRequest request,
        RoleUserUpdateParam params) {
        return authCenterFeignClient.batchUpdateRoleUserByUserId(AntRestUtils.getToken2(request),
            params);
    }

    public BaseResponse batchAddRoleUserByUserId(ServerHttpRequest request,
        BatchAddRoleUserParam params) {
        return authCenterFeignClient.batchAddRoleUserByUserId(AntRestUtils.getToken2(request),
            params);
    }

    public Mono<ListResponse<SysUserVo>> inGroupSysUserList(ListOaGroupUserParam param) {
        return this.oaFeignClient.groupUserList(param)
            .doOnNext(FeignResponseValidate::check)
            .flatMap(res -> {
                final List<OaAccountDto> userList = res.getData();
                final Map<Long, List<OaAccountDto>> uidMap = userList.stream()
                    .collect(Collectors.groupingBy(OaAccountDto::getUid));
                return reactorAuthCenterFeignClient.getSysUserByIdList(
                        new ArrayList<>(uidMap.keySet()))
                    .doOnNext(FeignResponseValidate::check)
                    .map(ListResponse::getData)
                    .map(x -> RestUtils.buildListResponse(x,
                        res.getTotal() != null ? res.getTotal() : 0));
            });
    }

    public ListResponse<SiteGroupVo> getSiteGroupsList(String token, List<Integer> types) {
        return authCenterFeignClient.getSiteGroupsList(token, types);
    }

    public ListResponse<SiteGroupVo> getSiteGroupsByUid(String token, Integer type) {
        return authCenterFeignClient.getSiteGroupsByUid(token,type);
    }

    public ListResponse<SysUserVo> getUserListBySiteId(String siteId) {
        return dataCoreFeignClient.getUserListBySiteId(siteId);
    }

    public Mono<ListResponse<SysUserVo>> sameCorpWxAppNameSysUser(
        String token, ListSysUserParam param) {
        return reactorAuthCenterFeignClient.sameCorpWxAppNameSysUser(token, param);
    }

    public Mono<ListResponse<SysUserVo>> findSysUserList(String token, ListSysUserParam param) {
        return reactorAuthCenterFeignClient.findSysUserList(token, param);
    }
}
