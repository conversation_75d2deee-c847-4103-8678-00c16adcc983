package com.cdz360.biz.ant.rest.sys;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.utils.feign.auth.AuthSiteGroupUserFeignClient;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "场站组用户相关操作接口", description = "场站组用户相关操作接口")
@Slf4j
@RestController
@RequestMapping("/api/siteGroup/user")
public class SiteGroupUserRest {

    @Autowired
    private AuthSiteGroupUserFeignClient authSiteGroupUserFeignClient;

    @Operation(summary = "获取场站组下的用户")
    @GetMapping("/getByGid")
    public Mono<ListResponse<SysUserVo>> getUserBySiteGroupGid(
        ServerHttpRequest request, @ApiParam("场站组ID") @RequestParam("gid") String gid) {
        log.info("获取场站组下的用户: {}", LoggerHelper2.formatEnterLog(request));
        return authSiteGroupUserFeignClient.getUserBySiteGroupGid(gid);
    }
}
