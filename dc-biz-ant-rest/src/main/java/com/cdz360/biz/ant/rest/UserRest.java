package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.aop.CheckAuth;
import com.cdz360.biz.ant.domain.vo.AccountRemainInfo;
import com.cdz360.biz.ant.domain.vo.CusOrderBiVo;
import com.cdz360.biz.ant.domain.vo.UserAndDynamicAndCardsVo;
import com.cdz360.biz.ant.domain.vo.UserVo;
import com.cdz360.biz.ant.service.AuthCenterService;
import com.cdz360.biz.ant.service.BalanceService;
import com.cdz360.biz.ant.service.UserService;
import com.cdz360.biz.ant.service.sysLog.CustomerSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.auth.sys.vo.SysRoleVo;
import com.cdz360.biz.model.cus.user.dto.CusSampleDto;
import com.cdz360.biz.model.cus.user.param.ListCustomerParam;
import com.cdz360.biz.model.cus.user.param.ListCustomerSiteParam;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.domain.BlocUserDto;
import com.chargerlinkcar.framework.common.domain.CommercialSample;
import com.chargerlinkcar.framework.common.domain.vo.AccountExVo;
import com.chargerlinkcar.framework.common.domain.vo.AccountInfoVo;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.domain.vo.UserPropVO;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;


/**
 * 管理用户信息
 *
 * <AUTHOR>
 * @since 2018-09-27 9:50
 *  
 **/
@Slf4j
@RestController
@Tag(name = "用户相关接口", description = "User")
@RequestMapping("/api/user")
public class UserRest extends BaseController {


    @Autowired
    private UserService userService;
    @Autowired
    private CustomerSysLogService customerSysLogService;

    @Autowired
    private BalanceService balanceService;

    @Autowired
    private AuthCenterService iAuthCenterService;

    @Operation(summary = "个人账户信息统计")
    @PostMapping(value = "/userAccountRemainInfo")
    @CheckAuth(code = "userOnlineRefund")
    public Mono<ObjectResponse<AccountRemainInfo>> getUserAccRemainInfo(
        ServerHttpRequest request,
        @Parameter(name = "用户ID", required = true) @RequestParam("uid") Long uid) {
        log.info("个人账户信息统计: {}", LoggerHelper2.formatEnterLog(request));

        Long topCommId = AntRestUtils.getTopCommId(request);
        if (null == topCommId) {
            throw new DcArgumentException("用户登录信息已失效，请刷新后重试");
        }
        return balanceService.getUserAccRemainInfo(topCommId, uid)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "商户会员账户信息统计")
    @PostMapping(value = "/commAccountRemainInfo")
    @CheckAuth(code = "commOnlineRefund")
    public Mono<ObjectResponse<AccountRemainInfo>> getCommAccRemainInfo(
        ServerHttpRequest request,
        @Parameter(name = "商户ID", required = true) @RequestParam("commId") Long commId,
        @Parameter(name = "用户ID", required = true) @RequestParam("uid") Long uid) {
        log.info("商户会员账户信息统计: {}", LoggerHelper2.formatEnterLog(request));

        Long topCommId = AntRestUtils.getTopCommId(request);
        if (null == topCommId) {
            throw new DcArgumentException("用户登录信息已失效，请刷新后重试");
        }
        return balanceService.getCommAccRemainInfo(topCommId, commId, uid)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "个人账户退款", description = "充电管理平台<在线退款>")
    @PostMapping(value = "/userAccountRefund")
    @CheckAuth(code = "userOnlineRefund")
    public Mono<BaseResponse> userAccountRefund(
        ServerHttpRequest request, @RequestBody AccountRemainInfo param) {
        log.info("个人账户退款: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        Long topCommId = AntRestUtils.getTopCommId(request);
        if (null == topCommId) {
            throw new DcArgumentException("用户登录信息已失效，请刷新后重试");
        }
        Long opUid = AntRestUtils.getSysUid(request);
        String opName = AntRestUtils.getSysUserName(request);

        return balanceService.userAccountRefund(topCommId, param, opUid, opName)
            .doOnNext(res -> customerSysLogService.accountRefund(
                param.getUid().toString(), request))
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "商户会员账户退款", description = "充电管理平台<在线退款>")
    @PostMapping(value = "/commAccountRefund")
    @CheckAuth(code = "commOnlineRefund")
    public Mono<BaseResponse> commAccountRefund(
        ServerHttpRequest request, @RequestBody AccountRemainInfo param) {
        log.info("商户会员账户退款: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        Long topCommId = AntRestUtils.getTopCommId(request);
        if (null == topCommId) {
            throw new DcArgumentException("用户登录信息已失效，请刷新后重试");
        }
        return balanceService.commAccountRefund(topCommId, param)
            .doOnNext(res -> customerSysLogService.accountRefund(
                param.getUid() + "_" + param.getCommId(), request))
            .map(RestUtils::buildObjectResponse);
    }

    @ResponseBody
    @PostMapping(value = "/getCusOrderBiList")
    @Operation(summary = "获取客户列表（含充电统计数据）")
    public Mono<ListResponse<CusOrderBiVo>> getCusOrderBiList(ServerHttpRequest request,
        @RequestBody ListCustomerParam param) {
        log.debug(LoggerHelper2.formatEnterLog(request) + "param = {}", param);
        param.setBalance(true)
            .setTopCommId(AntRestUtils.getTopCommId(request))
            .setCommIdChain(AntRestUtils.getCommIdChain(request));

        if (param.getLeaderCommId() != null && !param.getLeaderCommId()
            .equals(AntRestUtils.getTopCommId(request))) {
            ObjectResponse<Commercial> commercial = commercialFeignClient.getCommercial(
                param.getLeaderCommId());
            FeignResponseValidate.check(commercial);
            param.setCommIdChain(commercial.getData().getIdChain());
            log.debug("leaderCommId -> idChain: {} -> {}", param.getLeaderCommId(),
                commercial.getData().getIdChain());
        }

        return this.userService.getCusOrderBiList(param);
    }

    @ResponseBody
    @PostMapping(value = "/getCusUserList")
    @Operation(summary = "获取客户列表")
    public Mono<ListResponse<CusSampleDto>> getCusUserList(ServerHttpRequest request,
        @RequestBody ListCustomerParam param) {
        log.debug(LoggerHelper2.formatEnterLog(request) + "param = {}", param);
        param.setBalance(true)
            .setTopCommId(AntRestUtils.getTopCommId(request))
            .setCommIdChain(AntRestUtils.getCommIdChain(request));

        if (param.getLeaderCommId() != null && !param.getLeaderCommId()
            .equals(AntRestUtils.getTopCommId(request))) {
            ObjectResponse<Commercial> commercial = commercialFeignClient.getCommercial(
                param.getLeaderCommId());
            FeignResponseValidate.check(commercial);
            param.setCommIdChain(commercial.getData().getIdChain());
            log.debug("leaderCommId -> idChain: {} -> {}", param.getLeaderCommId(),
                commercial.getData().getIdChain());
        }

        return Mono.just(userService.getCusUserList(param));
    }

    @ResponseBody
    @PostMapping(value = "/getCusBySiteId")
    @Operation(summary = "获取场站下客户列表")
    public Mono<ListResponse<CusSampleDto>> getCusBySiteId(ServerHttpRequest request,
        @RequestBody ListCustomerSiteParam param) {
        log.debug(LoggerHelper2.formatEnterLog(request) + "param = {}", param);
        param.setTopCommId(AntRestUtils.getTopCommId(request))
            .setCommIdChain(AntRestUtils.getCommIdChain(request));

        return this.userService.getCusBySiteId(param);
    }

    @ResponseBody
    @PostMapping(value = "/getCorpUpBySiteId")
    @Operation(summary = "获取场站上级及本身企业客户列表")
    public Mono<ListResponse<BlocUserDto>> getCorpUpBySiteId(ServerHttpRequest request,
        @RequestBody ListCustomerSiteParam param) {
        log.debug(LoggerHelper2.formatEnterLog(request) + "param = {}", param);
        param.setTopCommId(AntRestUtils.getTopCommId(request))
            .setCommIdChain(AntRestUtils.getCommIdChain(request));

        return this.userService.getUpperCorpBySiteId(param);
    }


    /**
     * 根据用户ID查询基本资料及资金情况
     *
     * @param uid 用户ID
     * @return
     *   
     */
    @ResponseBody
    @GetMapping(value = "/findByUserId")
    public ObjectResponse<UserAndDynamicAndCardsVo> findUserAndDynamicByCommIdAndUid(
        @RequestParam("uid") Long uid, ServerHttpRequest request) {

        long topCommId = AntRestUtils.getTopCommId(request);
        IotAssert.isTrue(topCommId != 0, "获取商户id失败，请重试。");
        //commIdList = List.of(topCommId);

        //判断当前商户是否为登录的商户下的商户
        if (uid == null) {
            throw new DcArgumentException("参数错误");
        }

        ObjectResponse<UserAndDynamicAndCardsVo> result = userService.findDetailsByUserId(uid,
            topCommId, super.getCommIdChain2(request));
        log.info("查询获取数据 result={}", result);
        //获取当前商户ID
        CommercialSample comm = this.getCommercialSample2(request);
        if (ObjectUtils.isEmpty(comm)) {
            throw new DcArgumentException("当前操作用户信息不存在，请重新登录");
        }
        Long commId = comm.getComId();
        if (commId == null || commId <= 0) {
            throw new DcArgumentException("当前操作用户商户信息不明确");
        }
        log.info("当前登陆账户ID : {}", commId);
        //非顶级商户不能查看个人用户可用金额
        if (commId != topCommId) {
            result.getData().setAvailableAmount(null);
        }

        return result;
    }


    /**
     * 用户加入黑名单
     *
     * @param uid 用户id
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/userToBlack", method = RequestMethod.POST)
    @CheckAuth(code = "userBlackList")
    public BaseResponse userToBlack(Long uid, String phone, ServerHttpRequest request) {

        //判断当前商户是否为登录的商户下的商户
        if (uid == null) {
            throw new DcArgumentException("参数错误");
        }

        BaseResponse resultEntity = userService.userToBlack(uid, AntRestUtils.getTopCommId(request),
            super.getCommIdChain2(request));
        customerSysLogService.userModifyStatusLog(phone, request);
        return resultEntity;
    }

    /**
     * 用户解除黑名单
     *
     * @param uid
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/userUnBlack", method = RequestMethod.POST)
    public BaseResponse userUnBlack(Long uid, String phone, ServerHttpRequest request) {

        //判断当前商户是否为登录的商户下的商户
        if (uid == null) {
            throw new DcArgumentException("参数错误");
        }

        BaseResponse resultEntity = userService.userUnBlack(uid,
            AntRestUtils.getTopCommId(request));
        customerSysLogService.userModifyStatusLog(phone, request);
        return resultEntity;
    }


    /**
     * 通过手机号获取用户信息
     *
     * @param phone 用户手机号
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/findUserByPhone", method = RequestMethod.GET)
    public ObjectResponse findUserByPhone(String phone, ServerHttpRequest request) {
        List<Long> commIdList = this.getCommIdList2(request);
        log.info("commIdList：" + JsonUtils.toJsonString(commIdList));
        if (CollectionUtils.isEmpty(commIdList)) {
            throw new DcArgumentException("参数错误");
        }
        if (StringUtils.isBlank(phone)) {
            throw new DcArgumentException("参数错误");
        }
        //查询用户信息
        ObjectResponse<UserVo> resultEntity = userService.findUserByPhone(commIdList, phone);

        if (resultEntity == null || resultEntity.getStatus() != ResultConstant.RES_SUCCESS_CODE
            || resultEntity.getData() == null) {
            return resultEntity;
        }

        UserVo userVo = resultEntity.getData();

        if (ObjectUtils.isEmpty(userVo)) {
            throw new DcServiceException("获取失败");
        }

        return new ObjectResponse<>(userVo);
    }


    /**
     * 根据phone和blocUserId查询集团用户
     *
     * @param rBlocUser(phone和blocUserId)
     * @return
     */
    @PostMapping("/selectRBlocUserByPhone")
    public ObjectResponse<RBlocUser> selectRBlocUserByPhone(@RequestBody RBlocUser rBlocUser) {
        return userService.selectRBlocUserByPhone(rBlocUser);
    }

    /**
     * 根据手机号和商户id查询客户信息
     *
     * @param phone
     * @param commId
     * @return
     */
    @GetMapping("/findByPhone")
    public ObjectResponse<UserPropVO> findByPhone(@RequestParam("phone") String phone,
        @RequestParam("commId") Long commId,
        ServerHttpRequest request) {

        Long topCommId = AntRestUtils.getTopCommId(request);
        IotAssert.isNotNull(topCommId, "无法获取顶级商户Id，请重试。");

        UserPropVO userPropVO = userService.findByPhone(phone, topCommId);

        if (userPropVO != null
            && userService.findUserRef(userPropVO.getUserId(), commId)) {
            // 用户与当前商户已建立关联时，直接返回，否则返回null
            return new ObjectResponse<>(userPropVO);
        } else {
            return new ObjectResponse<>(null);
        }
    }

    /**
     * 查询用户下个人账户
     *
     * @param uId
     * @return
     */
    @ResponseBody
    @GetMapping("/getAccountByUserId")
    @Operation(summary = "根据UserId得到所有账户信息")
    public ListResponse<AccountExVo> getAccountByUserId(
        @Parameter(name = "用户id") @RequestParam Long uId, ServerHttpRequest request) {

        String commIdChain = super.getCommIdChain2(request);
        return userService.getAccountByUserId(commIdChain, uId, AntRestUtils.getTopCommId(request));
    }

    /**
     * 查询用户下个人账户
     *
     * @param uId
     * @return
     */
    @ResponseBody
    @GetMapping("/getAccountByUserIdV2")
    @Operation(summary = "根据UserId得到所有账户信息（将金额单位改成元，原接口暂时不改动）")
    public ListResponse<AccountInfoVo> getAccountByUserIdV2(ServerHttpRequest request,
        @Parameter(name = "用户id") @RequestParam Long uId) {
        log.info(LoggerHelper2.formatEnterLog(request));

        String commIdChain = super.getCommIdChain2(request);
        return userService.getAccountByUserIdV2(AntRestUtils.getCommId(request),
            commIdChain, uId, AntRestUtils.getTopCommId(request));
    }


    /**
     * 修改默认扣款账户
     *
     * @param accountId
     * @param defaultPayType
     * @return
     */
    @ResponseBody
    @PostMapping("/setDefaultAccount")
    @Operation(summary = "设置默认账户")
    @CheckAuth(code = "debitAccount")
    public ObjectResponse setDefaultAccount(
        @Parameter(name = "用户id") @RequestParam Long uId,
        @Parameter(name = "用户手机号") @RequestParam String phone,
        @Parameter(name = "账户id") @RequestParam Long accountId,
        @Parameter(name = "默认付款类型") @RequestParam Integer defaultPayType,
        ServerHttpRequest request) {
        ObjectResponse ObjectResponse = userService.setDefaultAccount(uId, accountId,
            defaultPayType);
        customerSysLogService.setDefaultAccountLog(phone, request);
        return new ObjectResponse<>("true");
    }

    /**
     * 查询当前用户的角色
     *
     * @return
     */
    @Operation(summary = "查询当前用户的角色")
    @ResponseBody
    @RequestMapping(value = "/queryUserRole", method = RequestMethod.POST)
    public ListResponse<SysRoleVo> queryUserRole(ServerHttpRequest request) {
        CommercialSample commercialSample = this.getCommercialSample2(request);
        if (ObjectUtils.isEmpty(commercialSample)) {
            throw new DcArgumentException("参数错误");
        }
        Long commId = commercialSample.getComId();
        if (commId == null || commId <= 0) {
            throw new DcArgumentException("参数错误");
        }
        Long merchantId = commercialSample.getId();
        if (merchantId == null || merchantId <= 0) {
            throw new DcArgumentException("参数错误");
        }

        List<SysRoleVo> sysRoleList = iAuthCenterService.findRolesByUserId(merchantId,
            getToken2(request));
        return new ListResponse<>(sysRoleList);
    }

    @Operation(summary = "用户设置券自动抵扣")
    @GetMapping(value = "/setCouponAutoDeduct")
    public BaseResponse setCouponAutoDeduct(ServerHttpRequest request,
        @RequestParam("userId") Long userId,
        @RequestParam("phone") String phone,
        @RequestParam("autoDeduct") Boolean autoDeduct) {
        log.info(LoggerHelper2.formatEnterLog(request) + "userId = {}, autoDeduct = {}", userId,
            autoDeduct);
        BaseResponse res = userService.setCouponAutoDeduct(userId, autoDeduct);
        customerSysLogService.setCouponAutoDeductLog(phone, request);
        return res;
    }

}
