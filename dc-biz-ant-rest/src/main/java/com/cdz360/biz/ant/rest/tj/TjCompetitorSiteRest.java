package com.cdz360.biz.ant.rest.tj;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.service.tj.TjCompetitorSiteService;
import com.cdz360.biz.model.tj.kc.dto.TjCompetitorSiteDto;
import com.cdz360.biz.model.tj.kc.param.ListTjCompetitorSiteParam;
import com.cdz360.biz.model.tj.kc.vo.TjCompetitorSiteVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "竞争对手场站相关操作接口", description = "竞争对手场站相关操作接口")
@Slf4j
@RestController
@RequestMapping("/api/tj/competitor/site")
public class TjCompetitorSiteRest {

    @Autowired
    private TjCompetitorSiteService tjCompetitorSiteService;

    @Operation(summary = "获取投建竞争者场站列表")
    @PostMapping("/findSite")
    public Mono<ListResponse<TjCompetitorSiteVo>> findTjCompetitorSite(
        ServerHttpRequest request,
        @RequestBody ListTjCompetitorSiteParam param) {
        log.info("获取投建竞争者场站列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), param);
        return tjCompetitorSiteService.findTjCompetitorSite(param);
    }

    @Operation(summary = "更新竞争场站附加信息")
    @PostMapping("/saveAttachInfo")
    public Mono<ObjectResponse<TjCompetitorSiteVo>> saveTjCompetitorSiteAttachInfo(
        ServerHttpRequest request,
        @RequestBody TjCompetitorSiteDto dto) {
        log.info("更新竞争场站附加信息: {}, param = {}", LoggerHelper2.formatEnterLog(request, false),
            dto);
        return tjCompetitorSiteService.saveTjCompetitorSiteAttachInfo(dto);
    }
}
