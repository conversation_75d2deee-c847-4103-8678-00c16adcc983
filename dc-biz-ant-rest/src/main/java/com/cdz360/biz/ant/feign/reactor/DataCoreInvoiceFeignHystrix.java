package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.oa.param.PrepaidInvoicingEditParam;
import com.cdz360.biz.model.oa.vo.OaInvoicedVo;
import com.cdz360.biz.model.trading.invoice.dto.InvoicedRecordDto;
import com.cdz360.biz.model.trading.invoice.param.UserInvoiceRecordParam;
import com.cdz360.biz.model.trading.invoice.po.CorpInvoiceRecordPo;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderBiVo;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class DataCoreInvoiceFeignHystrix
        implements FallbackFactory<DataCoreInvoiceFeignClient> {
    @Override
    public DataCoreInvoiceFeignClient apply(Throwable throwable) {
        return new DataCoreInvoiceFeignClient() {
            @Override
            public Mono<ListResponse<ChargerOrderVo>> includeChargerOrderList(ListChargeOrderParam param) {
                log.error("【服务熔断】: Service = {}, api = includeChargerOrderList (获取已申请企业开票的充电订单列表), param = {}",
                        DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<OrderBiVo>> includeChargerOrderBi(ListChargeOrderParam param) {
                log.error("【服务熔断】: Service = {}, api = includeChargerOrderBi (获取已申请企业开票的充电订单汇总信息), param = {}",
                        DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<Long>> prepaidInvoiceSubmit2Audit(
                PrepaidInvoicingEditParam param) {
                log.error("【服务熔断】: Service = {}, "
                        + "api = prepaidInvoiceSubmit2Audit(预付订单开票流程提交审核(非企业扣款账户订单)), "
                        + "param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<OaInvoicedVo>> getInvoiceVo4PrepaidProcess(
                String procInstId) {
                log.error("【服务熔断】: Service = {}, "
                        + "api = getInvoiceVo4PrepaidProcess(获取预付订单流程页面需展示的开票信息), "
                        + "param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, procInstId);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<Integer>> updateCorpInvoiceRecord(CorpInvoiceRecordPo invoiceRecordPo) {
                log.error("【服务熔断】: Service = {}, api = updateCorpInvoiceRecord (修改企业开票记录), invoiceRecordPo = {}",
                        DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, JsonUtils.toJsonString(invoiceRecordPo));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<Integer>> updateUserInvoiceRecord(
                InvoicedRecordDto invoicedRecordDto) {
                log.error("【服务熔断】: Service = {}, api = updateUserInvoiceRecord (修改个人&商户会员开票记录), invoicedRecordDto = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, JsonUtils.toJsonString(invoicedRecordDto));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<InvoicedRecordDto>> userInvoiceRecordSubmit2Audit(
                UserInvoiceRecordParam dto) {
                log.error("【服务熔断】: Service = {}, api = userInvoiceRecordSubmit2Audit, dto = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, JsonUtils.toJsonString(dto));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }
        };
    }

    @Override
    public <V> Function<V, DataCoreInvoiceFeignClient> compose(
            Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(
            Function<? super DataCoreInvoiceFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
        return null;
    }
}
