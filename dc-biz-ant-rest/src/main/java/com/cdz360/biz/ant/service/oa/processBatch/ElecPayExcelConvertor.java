package com.cdz360.biz.ant.service.oa.processBatch;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DateUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.DeviceMgmFeignClient;
import com.cdz360.biz.model.common.constant.ExcelCheckResult;
import com.cdz360.biz.model.merchant.dto.SiteTinyDto;
import com.cdz360.biz.model.oa.vo.OaExcel.ElecPayExcelItemVo;
import com.cdz360.biz.model.oa.vo.OaExcel.ExcelImportVo;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.chargerlinkcar.framework.common.service.excel.BufferedExcelConvertor;
import com.chargerlinkcar.framework.common.service.excel.ExcelConvertor;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.OptionalUtils;
import com.chargerlinkcar.framework.common.utils.RegularExpressionUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ElecPayExcelConvertor extends ExcelConvertor<ExcelImportVo<ElecPayExcelItemVo>> {

    private static final Integer CELL_LENGTH = 17;

    @Autowired
    private BufferedExcelConvertor excelConvertor;
    @Autowired
    private DeviceMgmFeignClient deviceMgmFeignClient;
    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;
    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @PostConstruct
    public void init() {
        this.excelConvertor.addExcelConvertorMap(ExcelCheckResult.PROCESS_VALID, this);
    }

    @Override
    public Integer getCellLength() {
        return CELL_LENGTH;
    }

    @Override
    public ExcelImportVo<ElecPayExcelItemVo> parse(List<List<String>> lineList) {
        List<ElecPayExcelItemVo> valid = new ArrayList<>(); // 有效
        List<ElecPayExcelItemVo> invalid = new ArrayList<>(); // 无效
        lineList.forEach(item -> {
            ElecPayExcelItemVo vo = new ElecPayExcelItemVo();
            vo.setSiteName(StringUtils.isEmpty(item.get(0)) ? null : item.get(0).trim()); // 站点名称

            TimeFilter range = new TimeFilter();
            String startDateStr = item.get(1); // 账期开始日期
            OptionalUtils.ofBlankStrAble(startDateStr)
                .filter(RegularExpressionUtil::isDate)
                .ifPresentOrElse(dateStr -> {
                    String newDateStr = dateStr.trim() + " 00:00:00";
                    Date date = null;
                    try {
                        date = DateUtils.fromYyyyMmDdHhMmSs(newDateStr);
                    } catch (Exception ex) {
                        log.warn("ElecPayExcelItemVo parse error: {}", ex.getMessage(), ex);
                    } finally {
                        Optional.ofNullable(date)
                            .ifPresentOrElse(e -> {
                                    vo.setBillStartDate(DateUtils.toYyyyMmDdHhMmSs(e));
                                    range.setStartTime(e);
                                },
                                () -> vo.putErrorInfo("billStartDate",
                                    ExcelCheckResult.PROCESS_DATE_FORMAT_ERROR.getDesc()));
                    }
                }, () -> {
                    vo.setBillStartDate(startDateStr);
                    vo.putErrorInfo("billStartDate",
                        ExcelCheckResult.PROCESS_DATE_FORMAT_ERROR.getDesc());
                });

            String endDateStr = item.get(2); // 账期结束日期
            OptionalUtils.ofBlankStrAble(endDateStr)
                .filter(RegularExpressionUtil::isDate)
                .ifPresentOrElse(dateStr -> {
                    String newDateStr = dateStr.trim() + " 23:59:59";
                    Date date = null;
                    try {
                        date = DateUtils.fromYyyyMmDdHhMmSs(newDateStr);
                    } catch (Exception ex) {
                        log.warn("ElecPayExcelItemVo parse error: {}", ex.getMessage(), ex);
                    } finally {
                        Optional.ofNullable(date)
                            .ifPresentOrElse(e -> {
                                    vo.setBillEndDate(DateUtils.toYyyyMmDdHhMmSs(e));
                                    range.setEndTime(e);
                                },
                                () -> vo.putErrorInfo("billEndDate",
                                    ExcelCheckResult.PROCESS_DATE_FORMAT_ERROR.getDesc()));
                    }
                }, () -> {
                    vo.setBillEndDate(endDateStr);
                    vo.putErrorInfo("billEndDate",
                        ExcelCheckResult.PROCESS_DATE_FORMAT_ERROR.getDesc());
                });
            if (vo.getBillStartDate() != null && vo.getBillEndDate() != null) {
                vo.setBillDateRange(range);
            }

            vo.setElecFee(StringUtils.isEmpty(item.get(3)) ? null
                : item.get(3).trim()); // 电费单金额

            vo.setElec(StringUtils.isEmpty(item.get(4)) ? null
                : item.get(4).trim()); // 电费单电量

            vo.setAutoDeduction(StringUtils.isEmpty(item.get(5)) ? null
                : item.get(5).trim()); // 自动扣款

            vo.setPaymentCompany(
                StringUtils.isEmpty(item.get(6)) ? null : item.get(6).trim()); // 付款公司名称

            vo.setSupplier(
                StringUtils.isEmpty(item.get(7)) ? null : item.get(7).trim()); // 供应商名称

            vo.setAccountBank(
                StringUtils.isEmpty(item.get(8)) ? null : item.get(8).trim()); // 开户行

            vo.setAccount(
                StringUtils.isEmpty(item.get(9)) ? null : item.get(9).trim()); // 账号

            if (StringUtils.isNotEmpty(item.get(10))) { // 最后付款时间
                String str = item.get(10).trim();
                vo.setLastPaymentTime(str);
                Date date = null;
                try {
                    date = DateUtils.fromYyyyMmDd(str);
                } catch (Exception ex) {
                    log.warn("ElecPayExcelItemVo parse error: {}", ex.getMessage(), ex);
                } finally {
                    if (date == null) {
                        vo.putErrorInfo("lastPaymentTime",
                            ExcelCheckResult.PROCESS_DATE_FORMAT_ERROR.getDesc());
                    }
                }
            }

            vo.setNote(
                StringUtils.isEmpty(item.get(11)) ? null : item.get(11).trim()); // 备注

            List<String> attachmentList = new ArrayList<>();
            Optional.ofNullable(item.get(12))
                .filter(StringUtils::isNotEmpty)
                .ifPresent(attachmentList::add);
            Optional.ofNullable(item.get(13))
                .filter(StringUtils::isNotEmpty)
                .ifPresent(attachmentList::add);
            Optional.ofNullable(item.get(14))
                .filter(StringUtils::isNotEmpty)
                .ifPresent(attachmentList::add);
            Optional.ofNullable(item.get(15))
                .filter(StringUtils::isNotEmpty)
                .ifPresent(attachmentList::add);
            Optional.ofNullable(item.get(16))
                .filter(StringUtils::isNotEmpty)
                .ifPresent(attachmentList::add);
            if (CollectionUtils.isNotEmpty(attachmentList)) {
                vo.setAttachmentNameList(attachmentList);
            }

            this.checkFormat(vo);
            if (vo.getErrorFields() == null || vo.getErrorFields().size() == 0) {
                valid.add(vo);
            } else {
                invalid.add(vo);
            }
        });
        return new ExcelImportVo<>(valid, invalid);
    }

    private void checkFormat(ElecPayExcelItemVo item) {
        // 有效性检测
        if (StringUtils.isEmpty(item.getSiteName())) {
            item.putErrorInfo("siteName", ExcelCheckResult.PROCESS_INVALID.getDesc());
        }
        if (StringUtils.isEmpty(item.getBillStartDate())) {
            item.putErrorInfo("billStartDate", ExcelCheckResult.PROCESS_INVALID.getDesc());
        }
        if (StringUtils.isEmpty(item.getBillEndDate())) {
            item.putErrorInfo("billEndDate", ExcelCheckResult.PROCESS_INVALID.getDesc());
        }
        if (item.getElecFee() == null) {
            item.putErrorInfo("elecFee", ExcelCheckResult.PROCESS_INVALID.getDesc());
        }
        if (item.getElec() == null) {
            item.putErrorInfo("elec", ExcelCheckResult.PROCESS_INVALID.getDesc());
        }
        if (StringUtils.isEmpty(item.getAutoDeduction())) {
            item.putErrorInfo("autoDeduction", ExcelCheckResult.PROCESS_INVALID.getDesc());
        }
        if (StringUtils.isEmpty(item.getPaymentCompany())) {
            item.putErrorInfo("paymentCompany", ExcelCheckResult.PROCESS_INVALID.getDesc());
        }
        if (StringUtils.isEmpty(item.getSupplier())) {
            item.putErrorInfo("supplier", ExcelCheckResult.PROCESS_INVALID.getDesc());
        }
        if (StringUtils.isEmpty(item.getAccountBank())) {
            item.putErrorInfo("accountBank", ExcelCheckResult.PROCESS_INVALID.getDesc());
        }
        if (StringUtils.isEmpty(item.getAccount())) {
            item.putErrorInfo("account", ExcelCheckResult.PROCESS_INVALID.getDesc());
        }

        boolean valid = false;
        try {
            valid = new BigDecimal(item.getElecFee()).scale() <= 2;
        } catch (Exception ex) {
            log.warn("checkFormat error: {}", ex.getMessage(), ex);
        } finally {
            if (!valid) {
                item.putErrorInfo("elecFee",
                    ExcelCheckResult.PROCESS_NUMBER_FORMAT_ERROR.getDesc());
            }
        }

        valid = false;
        try {
            valid = new BigDecimal(item.getElec()).scale() <= 4;
        } catch (Exception ex) {
            log.warn("checkFormat error: {}", ex.getMessage(), ex);
        } finally {
            if (!valid) {
                item.putErrorInfo("elec",
                    ExcelCheckResult.PROCESS_NUMBER_FORMAT_ERROR.getDesc());
            }
        }

        if (StringUtils.isNotEmpty(item.getAutoDeduction())
            && !List.of("是", "否").contains(item.getAutoDeduction())) {
            item.putErrorInfo("autoDeduction",
                ExcelCheckResult.PROCESS_AUTO_DEDUCTION_FORMAT_ERROR.getDesc());
        }

        Optional.ofNullable(item.getBillDateRange())
            .filter(e -> e.getStartTime() != null && e.getEndTime() != null
                && !e.getStartTime().before(e.getEndTime()))
            .ifPresent(e -> {
                item.putErrorInfo(List.of("billStartDate", "billEndDate"),
                    ExcelCheckResult.PROCESS_DATE_LOGIC_ERROR.getDesc());
            });

        if (item.getAttachmentNameList() != null && item.getAttachmentNameList().size() > 5) {
            item.putErrorInfo("attachmentNameList",
                ExcelCheckResult.PROCESS_EXCESSIVE_ATTACHMENT_ERROR.getDesc());
        }

    }

    @Override
    public ExcelImportVo<ElecPayExcelItemVo> verify(ExcelImportVo<ElecPayExcelItemVo> t) {
        ExcelImportVo<ElecPayExcelItemVo> importVo = checkInDatebase(t.getValid(), t.getInvalid());
        log.info("valid.size = {}, invalid.size = {}", importVo.getValid().size(),
            importVo.getInvalid().size());
        return importVo;
    }

    public ExcelImportVo<ElecPayExcelItemVo> checkInDatebase(List<ElecPayExcelItemVo> itemList,
        List<ElecPayExcelItemVo> invalid) {
        ExcelImportVo<ElecPayExcelItemVo> ListVo = new ExcelImportVo<>();
        List<ElecPayExcelItemVo> valid = new ArrayList<>(); // 新的有效数据集合

        if (CollectionUtils.isEmpty(itemList)) {
            ListVo.setValid(valid);
            ListVo.setInvalid(invalid);
            return ListVo;
        }

        ListSiteParam param = new ListSiteParam();
        param.setExactSiteNameList(
            itemList.stream().map(ElecPayExcelItemVo::getSiteName).collect(Collectors.toList()));
        ListResponse<SiteTinyDto> siteTinyList = dataCoreFeignClient.getSiteTinyList(param);
        FeignResponseValidate.check(siteTinyList);
        Map<String, String> siteNameMap = siteTinyList.getData().stream()
            .collect(Collectors.toMap(SiteTinyDto::getSiteName, SiteTinyDto::getId));

        Map<String, Boolean> billDateMap = new HashMap<>(); // <场站名称，账期是否重叠>
        Map<String, List<TimeFilter>> collect = itemList.stream()
            .collect(Collectors.groupingBy(ElecPayExcelItemVo::getSiteName, Collectors.mapping(
                ElecPayExcelItemVo::getBillDateRange, Collectors.toList())));
        collect.forEach((k, v) -> {
            if (v.size() > 1) {
                billDateMap.put(k, DateUtil.overlap(v, false));
            }
        });

        itemList.forEach(item -> {
            item.setSiteId(siteNameMap.get(item.getSiteName()));

            if (StringUtils.isEmpty(item.getSiteId())) {
                item.putErrorInfo("siteName",
                    ExcelCheckResult.PROCESS_SITE_NOT_EXIST.getDesc());
                invalid.add(item);
            } else if (Boolean.TRUE.equals(billDateMap.get(item.getSiteName()))) {
                item.putErrorInfo(List.of("siteName", "billStartDate", "billEndDate"),
                    ExcelCheckResult.PROCESS_SITE_DATE_LOGIC_ERROR.getDesc());
                invalid.add(item);
            } else {
                valid.add(item);
            }
        });
        ListVo.setValid(valid);
        ListVo.setInvalid(invalid);
        return ListVo;
    }

}
