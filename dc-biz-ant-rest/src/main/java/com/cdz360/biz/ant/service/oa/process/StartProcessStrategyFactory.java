package com.cdz360.biz.ant.service.oa.process;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class StartProcessStrategyFactory {

    private Map<String, StartProcessStrategy> strategyMap = new ConcurrentHashMap<>();

    protected void addStrategy(String proDefKey, StartProcessStrategy strategy) {
        this.strategyMap.put(proDefKey, strategy);
    }

    public StartProcessStrategy getStrategy(String proDefKey) {
        return this.strategyMap.get(proDefKey);
    }
}
