package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.domain.bundle.ListUpgradeLogParam;
import com.cdz360.biz.model.upgradepg.type.BundleType;
import com.cdz360.biz.model.upgradepg.vo.UpgradeLogVo;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class DeviceBundleFeignHystrix
    implements FallbackFactory<DeviceBundleFeignClient> {

    @Override
    public DeviceBundleFeignClient apply(Throwable throwable) {
        log.debug("err = {}", throwable.getMessage(), throwable);
        return new DeviceBundleFeignClient() {

            @Override
//            public Mono<ObjectResponse<Long>> uploadEvseBundle(UploadBundleParam bundleParam) {
//            public Mono<ObjectResponse<Long>> uploadEvseBundle(MultipartFile file) {
            public Mono<ObjectResponse<Long>> uploadEvseBundle(
                FilePart file, BundleType type, String swVer, Long opId, String opName) {
                log.error(
                    "【服务熔断】: Service = {}, api = uploadEvseBundle (上传升级包), type = {}",
                    DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, type);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<UpgradeLogVo>> mgcUpgradeLogList(ListUpgradeLogParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = mgcUpgradeLogList (控制器升级记录), param = {}",
                    DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, param);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }
        };
    }

    @Override
    public <V> Function<V, DeviceBundleFeignClient> compose(
        Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_DEVICE_MGM);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(
        Function<? super DeviceBundleFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_DEVICE_MGM);
        return null;
    }
}
