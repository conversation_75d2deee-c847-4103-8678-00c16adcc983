package com.cdz360.biz.ant.rest.profit;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.service.profit.SettJobBillService;
import com.cdz360.biz.ant.service.sysLog.SettBillSysLogService;
import com.cdz360.biz.model.trading.profit.sett.param.ListSettJobBillParam;
import com.cdz360.biz.model.trading.profit.sett.vo.SettJobBillDetailVo;
import com.cdz360.biz.model.trading.profit.sett.vo.SettJobBillVo;
import com.cdz360.biz.oa.dto.OaSettJobBillDto;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/api/settJobBill")
@Tag(name = "结算单相关操作接口", description = "结算单相关操作接口")
public class SettJobBillRest {

    @Autowired
    private SettJobBillService settJobBillService;

    @Autowired
    private SettBillSysLogService settBillSysLogService;

    @Operation(summary = "获取结算单列表")
    @PostMapping(value = "/findAll")
    public Mono<ListResponse<SettJobBillVo>> findSettJobBill(
        ServerHttpRequest request,
        @RequestBody ListSettJobBillParam param) {
        log.debug("获取结算单列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        return settJobBillService.findSettJobBill(param);
    }

    @Operation(summary = "检查结算单列表变更")
    @PostMapping(value = "/checkSettJobBill")
    public Mono<ListResponse<OaSettJobBillDto>> checkSettJobBill(
        ServerHttpRequest request,
        @RequestBody List<OaSettJobBillDto> param) {
        log.debug("检查结算单列表变更: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        return settJobBillService.checkSettJobBill(param);
    }

    @Operation(summary = "结算单数据更新")
    @GetMapping(value = "/recalculateBill")
    public Mono<ObjectResponse<SettJobBillVo>> recalculateSettJobBill(
        ServerHttpRequest request,
        @RequestParam("billNo") String billNo,
        @ApiParam("重新计算方式: 1(使用关联任务最新规则计算); 2(使用原来的计算规则进行计算)")
        @RequestParam("recalculateWay") Integer recalculateWay) {
        log.info("结算单数据更新: {}", LoggerHelper2.formatEnterLog(request));
        return settJobBillService.recalculateSettJobBill(billNo, recalculateWay)
            .doOnNext(FeignResponseValidate::check)
            .doOnNext(x -> settBillSysLogService.editSettJobBillLog(billNo, request));
    }

    @Operation(summary = "删除结算单(逻辑删除)")
    @DeleteMapping(value = "/deleteBill")
    public Mono<ObjectResponse<SettJobBillVo>> deleteSettJobBill(
        ServerHttpRequest request,
        @RequestParam("billNo") String billNo) {
        log.info("删除结算单(逻辑删除): {}", LoggerHelper2.formatEnterLog(request));
        return settJobBillService.deleteSettJobBill(billNo)
            .doOnNext(FeignResponseValidate::check)
            .doOnNext(x -> settBillSysLogService.delSettJobBillLog(billNo, request))
            .doOnNext(x -> log.info("删除结算单: {}", x.getData()));
    }

    @Operation(summary = "获取结算单")
    @GetMapping(value = "/getBill")
    public Mono<ObjectResponse<SettJobBillDetailVo>> getSettJobBill(
        ServerHttpRequest request,
        @RequestParam("billNo") String billNo) {
        log.info("获取结算单: {}", LoggerHelper2.formatEnterLog(request));
        return settJobBillService.getSettJobBill(billNo)
            .doOnNext(x -> log.info("获取结算单: {}", x.getData()));
    }
}
