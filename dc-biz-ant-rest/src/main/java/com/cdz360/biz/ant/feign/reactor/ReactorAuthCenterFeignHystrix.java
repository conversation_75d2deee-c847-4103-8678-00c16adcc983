package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.domain.BlocUser;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.auth.user.param.ListSysUserParam;
import com.cdz360.biz.auth.user.po.SysUserPo;
import com.cdz360.biz.model.cus.corp.dto.UserIdDto;
import com.cdz360.biz.model.cus.corp.po.CorpOrgPo;
import com.cdz360.biz.model.cus.message.po.MessagePo;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.sys.vo.SystemVo;
import com.cdz360.biz.oa.param.account.OaModifyGroupParam;
import java.util.List;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class ReactorAuthCenterFeignHystrix
    implements FallbackFactory<ReactorAuthCenterFeignClient> {

    @Override
    public ReactorAuthCenterFeignClient apply(Throwable throwable) {
        return new ReactorAuthCenterFeignClient() {
            @Override
            public Mono<ListResponse<SysUserVo>> getSysUserByIdList(List<Long> ids) {
                log.error("服务[{}]接口熔断 - 获取系统用户列表, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, JsonUtils.toJsonString(ids));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<Integer>> addMessage(String token, MessagePo message) {
                log.error("【服务熔断】: Service = {}, api = addMessage (添加站内信), message = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_BI, JsonUtils.toJsonString(message));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            public Mono<ObjectResponse<SysUserPo>> getSysUserById(
                @RequestParam(value = "id") Long id) {
                log.error("【服务熔断】。Service = {}, 获取系统用户信息 id = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, id);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<CorpPo>> getCorpByUid(String token, Long corpUid) {
                log.error("【服务熔断】。Service = {}, api = getCorpByUid (通过UID获取企业信息)",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<com.cdz360.biz.model.cus.corp.po.CorpPo>> updateBlocUser(
                BlocUser blocUser) {
                log.error("【服务熔断】。Service = {}, api = updateBlocUser (更新集团信息)",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<Long>> addCorp(
                com.cdz360.biz.model.cus.corp.po.CorpPo corp) {
                log.error("【服务熔断】。Service = {}, api = addCorp (新增集团)",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<CorpOrgPo>> getOrgInfoByLevel(Long corpId,
                Integer orgLevel) {
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<Integer>> allocateSysUserGroup(OaModifyGroupParam param) {
                log.error("服务[{}]接口熔断 - 分配用户组, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<String>> groupIdByKeyWord(String sk) {
                log.error("服务[{}]接口熔断 - 通过关键词查询审核组ID, sk = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, sk);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<Long>> teamCatalogUserIdList(Long uid, String uname) {
                log.error("服务[{}]接口熔断 - 和指定用户团队隔离标签一致的用户ID, uid = {}, uname = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, uid, uname);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<UserIdDto>> searchUserIdList(
                Long teamCatalogUid, String uname) {
                log.error("服务[{}]接口熔断 - 多条件获取用户ID列表, teamCatalogUid = {}, uname = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, teamCatalogUid, uname);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<Boolean>> sameTeamCatalog(Long uid1, Long uid2) {
                log.error("服务[{}]接口熔断 - 两个用户团队标签是否一致, uid1 = {}, uid2 = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, uid1, uid2);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<SysUserVo>> sameCorpWxAppNameSysUser(
                String token, ListSysUserParam param) {
                log.error("服务[{}]接口熔断 - 获取当前用户相同团队标签的用户列表, token = {}, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, token, param);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<SysUserVo>> findSysUserList(
                String token, ListSysUserParam param) {
                log.error("服务[{}]接口熔断 - 获取系统用户列表, token = {}, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, token, param);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<SystemVo>> sysMenuBySysId(String token, Long sysId,
                String name) {
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }
        };
    }

    @Override
    public <V> Function<V, ReactorAuthCenterFeignClient> compose(
        Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_AUTH);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(
        Function<? super ReactorAuthCenterFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_AUTH);
        return null;
    }
}
