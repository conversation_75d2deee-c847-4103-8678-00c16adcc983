package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.domain.OrderInMongo;
import com.cdz360.biz.model.ads.param.CreateAdsParam;
import com.cdz360.biz.model.ads.param.ListAdsParam;
import com.cdz360.biz.model.ads.param.UpdateAdsParam;
import com.cdz360.biz.model.ads.vo.AdsVo;
import com.cdz360.biz.model.bi.dashboard.SiteAndPlugBiVo;
import com.cdz360.biz.model.cus.soc.vo.SocCorpVo;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.cus.wallet.vo.RefundAnalyzeVo;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.iot.param.ModifyEvseCfgParam;
import com.cdz360.biz.model.iot.vo.OfflineEvseImportVo;
import com.cdz360.biz.model.merchant.dto.SiteTinyDto;
import com.cdz360.biz.model.oa.vo.BillInvoiceVo;
import com.cdz360.biz.model.order.param.ListSettlementOrderParam;
import com.cdz360.biz.model.site.type.SiteCategory;
import com.cdz360.biz.model.trading.contract.param.AddContractParam;
import com.cdz360.biz.model.trading.contract.param.ContractListParam;
import com.cdz360.biz.model.trading.contract.vo.ContractVo;
import com.cdz360.biz.model.trading.coupon.param.ActivityUserCouponParam;
import com.cdz360.biz.model.trading.coupon.param.BatchSendCouponParam;
import com.cdz360.biz.model.trading.coupon.param.CouponDictParam;
import com.cdz360.biz.model.trading.coupon.param.CouponDictSearchParam;
import com.cdz360.biz.model.trading.coupon.param.CouponSearchParam;
import com.cdz360.biz.model.trading.coupon.param.CreateActivityParam;
import com.cdz360.biz.model.trading.coupon.param.ListActivityParam;
import com.cdz360.biz.model.trading.coupon.param.UpdateActivityParam;
import com.cdz360.biz.model.trading.coupon.type.AcquireCouponResult;
import com.cdz360.biz.model.trading.coupon.vo.ActivityVo;
import com.cdz360.biz.model.trading.coupon.vo.CouponBi;
import com.cdz360.biz.model.trading.coupon.vo.CouponDictVo;
import com.cdz360.biz.model.trading.coupon.vo.CouponVo;
import com.cdz360.biz.model.trading.coupon.vo.DiscountVo;
import com.cdz360.biz.model.trading.evse.EvseInfo;
import com.cdz360.biz.model.trading.file.po.OssFilePo;
import com.cdz360.biz.model.trading.hlht.param.BindHlhtParam;
import com.cdz360.biz.model.trading.hlht.vo.HlhtSiteCommVo;
import com.cdz360.biz.model.trading.invoice.dto.CorpInvoiceRecordDto;
import com.cdz360.biz.model.trading.invoice.dto.InvoiceRecordOrderRefDto;
import com.cdz360.biz.model.trading.invoice.dto.InvoicedRecordDto;
import com.cdz360.biz.model.trading.invoice.param.ListCorpInvoiceRecordParam;
import com.cdz360.biz.model.trading.invoice.param.ListInvoiceRecordOrderRefParam;
import com.cdz360.biz.model.trading.invoice.param.UserInvoiceRecordParam;
import com.cdz360.biz.model.trading.invoice.po.CorpInvoiceRecordPo;
import com.cdz360.biz.model.trading.invoice.po.InvoiceRecordOrderRefPo;
import com.cdz360.biz.model.trading.invoice.vo.CorpInvoiceRecordVo;
import com.cdz360.biz.model.trading.iot.vo.EvseInfoVo;
import com.cdz360.biz.model.trading.meter.param.MeterDataListParam;
import com.cdz360.biz.model.trading.meter.vo.MeterDataVo;
import com.cdz360.biz.model.trading.order.dto.CusCorpOrderBiDto;
import com.cdz360.biz.model.trading.order.dto.CusLastOrderSiteDto;
import com.cdz360.biz.model.trading.order.dto.CusOrderBiDto;
import com.cdz360.biz.model.trading.order.dto.LowKwOrderDto;
import com.cdz360.biz.model.trading.order.dto.PayBillInvoiceBi;
import com.cdz360.biz.model.trading.order.param.BillInvoiceVoParam;
import com.cdz360.biz.model.trading.order.param.CzOrderPointParam;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.param.ListCusOrderBiParam;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.model.trading.order.param.PrepaidOrderListParam;
import com.cdz360.biz.model.trading.order.param.ZftBillParam;
import com.cdz360.biz.model.trading.order.po.ChargerOrderTimeDivision;
import com.cdz360.biz.model.trading.order.po.PayBillPo;
import com.cdz360.biz.model.trading.order.vo.PayBillAccountDetailVo;
import com.cdz360.biz.model.trading.order.vo.PayBillBi;
import com.cdz360.biz.model.trading.order.vo.PayBillLinkChargeOrderVo;
import com.cdz360.biz.model.trading.order.vo.PayBillUsedDetail;
import com.cdz360.biz.model.trading.order.vo.PayBillVo;
import com.cdz360.biz.model.trading.order.vo.SettlementOrderVo;
import com.cdz360.biz.model.trading.order.vo.UserBillAccountNameVo;
import com.cdz360.biz.model.trading.order.vo.ZftBillBi;
import com.cdz360.biz.model.trading.order.vo.ZftBillVo;
import com.cdz360.biz.model.trading.peek.invoice.dto.PeekInvoiceDto;
import com.cdz360.biz.model.trading.profit.conf.vo.CorpProfitBaseVo;
import com.cdz360.biz.model.trading.site.dto.SiteSimpleDto;
import com.cdz360.biz.model.trading.site.param.ChargeJobLogParam;
import com.cdz360.biz.model.trading.site.param.ChargeJobParam;
import com.cdz360.biz.model.trading.site.param.ListOvertimeParkFeeOrderParam;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.po.EvseCfgSchedulePo;
import com.cdz360.biz.model.trading.site.po.OvertimeParkFeeOrderPo;
import com.cdz360.biz.model.site.po.PriceTemplatePo;
import com.cdz360.biz.model.trading.site.po.SiteChargeJobPo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.site.vo.EvseCfgScheduleVo;
import com.cdz360.biz.model.trading.site.vo.MoveCorpNoCardList;
import com.cdz360.biz.model.trading.site.vo.MoveCorpNoCardVo;
import com.cdz360.biz.model.trading.site.vo.OvertimeParkFeeOrderBi;
import com.cdz360.biz.model.trading.site.vo.OvertimeParkFeeOrderVo;
import com.cdz360.biz.model.trading.site.vo.PriceSchemeSiteVo;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobLogVo;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobMoveCorpList;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobPlugVo;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobVo;
import com.cdz360.biz.model.trading.site.vo.SiteConfStartList;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import com.cdz360.biz.model.trading.soc.param.QueryStrategyParam;
import com.cdz360.biz.model.trading.soc.param.SocStrategyDict;
import com.cdz360.biz.model.trading.soc.vo.MoveCorpUserSocStrategyList;
import com.cdz360.biz.model.trading.soc.vo.UserSocStrategyCreditCusVo;
import com.cdz360.biz.model.trading.soc.vo.UserSocStrategyVinVo;
import com.cdz360.biz.model.trading.warn.param.AddUserWarnParam;
import com.cdz360.biz.model.trading.warn.param.UserWarnListParam;
import com.cdz360.biz.model.trading.warn.param.WarnListParam;
import com.cdz360.biz.model.trading.warn.param.WarnSubParam;
import com.cdz360.biz.model.trading.warn.po.WarningPo;
import com.cdz360.biz.model.trading.warn.vo.UserWarningVo;
import com.cdz360.biz.model.wallet.vo.RefundReasonVo;
import com.cdz360.biz.model.oss.OssStsDto;
import com.chargerlinkcar.framework.common.constant.CheckTaxStatus;
import com.chargerlinkcar.framework.common.domain.OrderReserveVo;
import com.chargerlinkcar.framework.common.domain.PointRecDto;
import com.chargerlinkcar.framework.common.domain.SitePersonaliseDTO;
import com.chargerlinkcar.framework.common.domain.invoice.dto.UpdateIdDTO;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordAuditParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordManualParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordUpdateParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceRecordDetail;
import com.chargerlinkcar.framework.common.domain.param.ListPriceSchemeSiteUseParam;
import com.chargerlinkcar.framework.common.domain.param.SiteChargeJobParam;
import com.chargerlinkcar.framework.common.domain.peek.invoice.param.PeekInvoiceParam;
import com.chargerlinkcar.framework.common.domain.request.StartChargerRequest;
import com.chargerlinkcar.framework.common.domain.request.StopChargerRequest;
import com.chargerlinkcar.framework.common.domain.vo.BatteryVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderSite;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderBiVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderOvertimeParkInfoVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderTimeShareBiVo;
import com.chargerlinkcar.framework.common.domain.vo.UpdateOrderVo;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @since 2019/11/6 15:24
 */
@Slf4j
@Component
public class DataCoreFeignClientHystrixFactory implements FallbackFactory<DataCoreFeignClient> {

    @Override
    public DataCoreFeignClient create(Throwable cause) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE,
            cause.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE,
            cause.getStackTrace());

        return new DataCoreFeignClient() {
//            @Override
//            public ListResponse getOrderTimeDivisionData(Long startTime, Long endTime, List<Long> commIdList, String siteId, Integer province, Integer city) {
//                return RestUtils.serverBusy4ListResponse();
//            }

            @Override
            public ListResponse<PayBillVo> payBillList(PayBillParam payBillParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<ZftBillVo> zftBillList(ZftBillParam payBillParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<UserBillAccountNameVo> userBillAccountName(
                PayBillParam payBillParam) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<PayBillInvoiceBi> invoiceOrderList(PayBillParam payBillParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<OrderBiVo> invoiceOrderBiForCorp(PayBillParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<PayBillBi> payBillBi(PayBillParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<ZftBillBi> zftBillBi(ZftBillParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<Integer> updateById(PayBillPo po) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Integer> updateByOrderId(PayBillPo po) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<PayBillUsedDetail> pointRecLog(String orderId, String orderNo) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<PointRecDto> getCzOrderPointsInfo(CzOrderPointParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<PayBillAccountDetailVo> getAccountDetail(String orderId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<PayBillVo> payBillView(String orderId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<PayBillLinkChargeOrderVo> orderPointRecLog(String orderNo) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<CheckTaxStatus> checkTaxStatus(
                @RequestParam("orderId") String orderId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<RefundAnalyzeVo> refundAnalyze(Date startDate, Date stopDate) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<RefundReasonVo> refundList(String cusName, String cusPhone,
                String cusNote, int start, int size) {
                return RestUtils.serverBusy4ListResponse();
            }
//            @Override
//            public ListResponse<PriceTemplatePo> getPriceTemplateList(ListPriceTemplateParam param) {
//                return RestUtils.serverBusy4ListResponse();
//            }

            @Override
            public BaseResponse enable(String token, Long id, Boolean enable) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse deletePvPriceSchema(List<Long> priceIdList) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<Integer> batchInsert(List<EvseCfgSchedulePo> poList) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse priceTempDown(ModifyEvseCfgParam param) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse modifyEvsePrice(ModifyEvseCfgParam param) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<PriceSchemeSiteVo> getByPriceSchemeId(
                ListPriceSchemeSiteUseParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<String> downFilter(List<String> evseNoList) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<EvseCfgScheduleVo> getEvseCfgScheduleByEvseNo(
                List<String> evseNoList) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<PriceTemplatePo> getPriceSchemeByEvesNo(String evseNo) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse updateEvseSetting(ModifyEvseCfgParam param) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<OrderInMongo> getChargerOrderInfo(String orderNo) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse webStopCharger(List<StopChargerRequest> stopRequest) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<OrderReserveVo> getReserveInfoByPlugNoList(
                List<String> plugNoList) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SiteChargeJobPlugVo> getSiteJobByPlugNoList(
                List<String> plugNoList) {
                return RestUtils.serverBusy4ListResponse();
            }


            @Override
            public BaseResponse changeJobStatus(Long jobId, Integer jobStatus) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse webStartCharger(StartChargerRequest chargerRequest) {
                return null;
            }

            @Override
            public BaseResponse bindingJob(SiteChargeJobParam param) {
                return null;
            }

            @Override
            public BaseResponse modifyChargeJob(SiteChargeJobParam param) {
                return null;
            }

            @Override
            public ListResponse<SiteChargeJobLogVo> getChargeJobLogList(ChargeJobLogParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<String> getUserSubSiteList(Long sysUid) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SiteVo> getUserSubSiteInfoList(Long sysUid, Long start, Long size) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<String> getUserSubCodeList(Long sysUid) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse addOrUpdateUserWarn(AddUserWarnParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse queryWarningSub(WarnSubParam param) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<UserWarningVo> getUserWarnList(UserWarnListParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<WarningPo> getWarnList(WarnListParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse unbindingJob(List<String> plugNoList) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<SiteChargeJobVo> jobList(ChargeJobParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<SitePersonaliseDTO> getPersonalise(String siteId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse updatePersonalise(SitePersonaliseDTO dto) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<SiteChargeJobPo> getSiteChargeJobBySiteId(String siteId,
                String jobName) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<CusOrderBiDto> getCusOrderBiList(ListCusOrderBiParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<CusCorpOrderBiDto> getCusAndCorpOrderBiList(
                ListCusOrderBiParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<OvertimeParkFeeOrderPo> getCusOvertimeParkOrderBiList(
                ListCusOrderBiParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<CusLastOrderSiteDto> getCusOrderLastSiteInfoList(
                ListCusOrderBiParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse checkWhetherThePrepaidOrderCanBeInvoiced(
                PrepaidOrderListParam param) {
                log.error("【服务熔断】: Service = {}, "
                    + "api = checkWhetherThePrepaidOrderCanBeInvoiced (检查所选预付订单是否可开票),"
                    + " param = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, param);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<BillInvoiceVo> getBillInvoiceVoList(BillInvoiceVoParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = getBillInvoiceVoList, param: {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, param.toString());
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<String> sendPriceSchema(String evseNo) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SettlementOrderVo> getNotSettlementOrderList(
                ListSettlementOrderParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<AcquireCouponResult> acquireCoupon(Long activityId, String phone) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Map<String, Boolean>> hasUserAcquiredCoupon(
                ActivityUserCouponParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse dictCreate(CouponDictParam req) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<CouponDictVo> getDictList(CouponDictSearchParam req) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse disableDict(Long id) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse createActivity(CreateActivityParam req) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse updateActivity(UpdateActivityParam req) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<HlhtSiteCommVo> getSiteByPartnerCode(String code) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse activeActivity(Long id) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse abortActivity(Long id) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse showInMobile(Long id) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse hideInMobile(Long id) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ActivityVo> getActivityDetail(Long id) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<CouponBi> couponBi(Long id) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<CouponVo> userActivityCouponList(CouponSearchParam req) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<CorpInvoiceRecordVo> corpInvoiceAppendOrder(
                CorpInvoiceRecordUpdateParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<PeekInvoiceDto> peekInvoice(PeekInvoiceParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<CorpInvoiceRecordVo> corpInvoiceRemoveOrder(
                CorpInvoiceRecordUpdateParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<CorpInvoiceRecordDetail> corpInvoiceRecordDetail(String applyNo) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Boolean> corpInvoiceRecordManual(
                CorpInvoiceRecordManualParam param) {
                log.error("企业手动开票熔断: {}, param = {}",
                    "corpInvoiceRecordManual", JsonUtils.toJsonString(param));
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Integer> corpInvoiceRecordAudit(
                CorpInvoiceRecordAuditParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<ActivityVo> listActivity(ListActivityParam req) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<CorpInvoiceRecordDto> findCorpInvoiceRecordList(
                ListCorpInvoiceRecordParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<CorpInvoiceRecordDto> getRecordByProcInstId(String procInstId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<ChargerOrderVo> listChargerOrder(ListChargeOrderParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<OrderBiVo> chargeOrderBiForCorp(ListChargeOrderParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<ChargerOrderSite> chargerOrderGroupBySite(
                ListChargeOrderParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<OrderTimeShareBiVo> chargerOrderGroupByTimeShareFee(
                ListChargeOrderParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<Integer> deleteCorpInvoiceRecordByApplyNo(String applyNo) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Integer> deleteCorpInvoiceRecordByOa(String procInstId,
                Boolean physicalDeletion) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Integer> updateCorpInvoiceRecordReturnFlag(
                CorpInvoiceRecordPo po) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<ChargerOrderSite> recordOrderGroupBySite(
                ListInvoiceRecordOrderRefParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<OrderTimeShareBiVo> recordOrderGroupByTimeShareFee(
                ListInvoiceRecordOrderRefParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<InvoiceRecordOrderRefDto> getInvoiceRecordOrderList(
                ListInvoiceRecordOrderRefParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<InvoiceRecordOrderRefPo> getInvoiceRecordOrderListByOa(
                String procInstId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<Integer> corpInvoiceRecordSubmit2Audit(CorpInvoiceRecordDto dto) {
                return RestUtils.serverBusy4ObjectResponse();
            }

//            @Override
//            public ObjectResponse<InvoicedRecordDto> userInvoiceRecordSubmit2Audit(UserInvoiceRecordParam dto) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

            @Override
            public ListResponse<SocCorpVo> queryBlocUserForSiteSoc(String siteId) {
                return RestUtils.serverBusy4ListResponse();
            }

//            @Override
//            public ListResponse<SocStrategyDict> queryStrategy(QueryStrategyParam param) {
//                return RestUtils.serverBusy4ListResponse();
//            }

            @Override
            public ObjectResponse<Boolean> checkRefPayBill(String refBillNo) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<EvseInfo> getEvseInfo(String evseNo) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse downDefultSetting2AllEvse(String siteId) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<ModifyEvseCfgParam> getModifyEvseCfgParam(String siteId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<OfflineEvseImportVo> parseOfflineEvseExcel(
                List<List<String>> list) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<EvseInfoVo> getOfflineEvseList(ListEvseParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse downSettingByEvse(String evseNo) {
                return RestUtils.serverBusy();
            }

//            @Override
//            public ObjectResponse<Integer> updateCorpInvoiceRecordModelId(UpdateIdDTO data) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

//            @Override
//            public ObjectResponse<Integer> updateCorpInvoiceRecordProTempId(
//                List<UpdateIdDTO> data) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

            @Override
            public ListResponse<OvertimeParkFeeOrderVo> findOvertimeParkFeeOrderAll(
                ListOvertimeParkFeeOrderParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<OrderOvertimeParkInfoVo> getOverTimeParkDivisionByOrderNo(
                String orderNo) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<OvertimeParkFeeOrderBi> overtimeParkFeeOrderBi(
                ListOvertimeParkFeeOrderParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse bindHlhtSite(BindHlhtParam param) {
                log.error("【服务熔断】。Service = {}, 商户绑定互联站点 param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_USER, param);
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse unbindHlhtSite(BindHlhtParam param) {
                log.error("【服务熔断】。Service = {}, 商户解绑互联站点 param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_USER, param);
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<SiteAndPlugBiVo> siteAndPlugStatusFillHlhtData(Long commId,
                String commIdChain) {
                log.error("【服务熔断】。Service = {},  commId = {},  commIdChain = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, commId, commIdChain);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<SiteConfStartList> getMoveCorpSiteConfStart(Long corpId,
                Long commId) {
                log.error("【服务熔断】。Service = {},  corpId = {}, commId = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, corpId, commId);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<MoveCorpUserSocStrategyList> getMoveCorpSoc(Long corpId,
                Long commId) {
                log.error("【服务熔断】。Service = {},  corpId = {}, commId = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, corpId, commId);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<SiteChargeJobMoveCorpList> getMoveCorpDetail(Long corpId,
                Long commId) {
                log.error("【服务熔断】。Service = {},  corpId = {}, commId = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, corpId, commId);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SiteSimpleDto> getSiteListByIdChain(String idChain) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<PayBillVo> payOrderQuery(String orderId) {
                log.error("【服务熔断】。Service = {},  orderId = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, orderId);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<ChargerOrderTimeDivision> estimateOrderTimeDivisionList(
                UpdateOrderVo updateOrderVo) {
                return RestUtils.serverBusy4ListResponse();
            }

//            @Override
//            public BaseResponse createCorpSocStrategy(SocStrategyDict param) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

            @Override
            public ObjectResponse<BatteryVo> getBatteryVo(String vin, String commIdChain) {
                return RestUtils.serverBusy4ObjectResponse();
            }

//            @Override
//            public BaseResponse updateCorpStrategy(SocStrategyDict param) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

//            @Override
//            public BaseResponse deleteCorpStrategy(Long id) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

            @Override
            public ObjectResponse<CorpProfitBaseVo> getCorpProfitConf(Long corpId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse addCorpProfitConf(CorpProfitBaseVo confList) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse disableCorpProfitConf(Long corpId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

//            @Override
//            public ListResponse<UserSocStrategyCreditCusVo> queryCorpStrategyCreditCus(
//                QueryStrategyParam param) {
//                return RestUtils.serverBusy4ListResponse();
//            }

//            @Override
//            public ListResponse<UserSocStrategyVinVo> queryCorpStrategyVin(
//                QueryStrategyParam param) {
//                return RestUtils.serverBusy4ListResponse();
//            }

//            @Override
//            public ObjectResponse<Integer> addCorpStrategyCreditCus(
//                List<QueryStrategyParam> params) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

//            @Override
//            public ObjectResponse<Integer> removeCorpStrategyCreditCus(
//                List<QueryStrategyParam> params) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

//            @Override
//            public ObjectResponse<Integer> addCorpStrategyVin(List<QueryStrategyParam> params) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

            @Override
            public ObjectResponse<OssStsDto> getSts() {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<OssStsDto> getPrivateSts() {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse addContract(AddContractParam param) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse delContract(Long id, String idChain, Long sysUid) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse updateContract(AddContractParam param) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<ContractVo> getContractById(Long contractId, String idChain) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<ContractVo> getContractList(ContractListParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<OssFilePo> addFile(OssFilePo param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SiteCategory> ywUserSiteCategoryList(List<String> siteIdList) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<MoveCorpNoCardList> getMoveCorpNoCard(Long corpId, Long commId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<MoveCorpNoCardVo> getCorpNoCardList(Long corpId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SiteVo> getCommNoCardList(Long commId, Long userId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<SitePo> getSiteById(String siteId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SiteTinyDto> getSiteTinyList(ListSiteParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<DiscountVo> discountInfo(Long commId, BigDecimal amount) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ActivityVo> hasActivity(Long commId, Long accountType) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Integer> overtimeParkFeeOrderCancel(
                ListOvertimeParkFeeOrderParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<String> getSiteListByGids(ListSiteParam params) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse batchSendCoupon(BatchSendCouponParam param) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<SysUserVo> getUserListBySiteId(String siteId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse createAds(CreateAdsParam req) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse updateAds(UpdateAdsParam req) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse abortAds(Long id) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse activeAds(Long id) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<AdsVo> getAdsDetail(Long id) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<AdsVo> listAds(ListAdsParam req) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<MeterDataVo> getMeterDataList(MeterDataListParam param) {
                return RestUtils.serverBusy4ListResponse();
            }
        };
    }
}
