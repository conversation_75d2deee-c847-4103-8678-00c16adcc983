package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.domain.OrderInMongo;
import com.cdz360.biz.model.ads.param.CreateAdsParam;
import com.cdz360.biz.model.ads.param.ListAdsParam;
import com.cdz360.biz.model.ads.param.UpdateAdsParam;
import com.cdz360.biz.model.ads.vo.AdsVo;
import com.cdz360.biz.model.bi.dashboard.SiteAndPlugBiVo;
import com.cdz360.biz.model.cus.soc.vo.SocCorpVo;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.cus.wallet.vo.RefundAnalyzeVo;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.iot.param.ModifyEvseCfgParam;
import com.cdz360.biz.model.iot.vo.OfflineEvseImportVo;
import com.cdz360.biz.model.merchant.dto.SiteTinyDto;
import com.cdz360.biz.model.oa.vo.BillInvoiceVo;
import com.cdz360.biz.model.order.param.ListSettlementOrderParam;
import com.cdz360.biz.model.site.type.SiteCategory;
import com.cdz360.biz.model.trading.contract.param.AddContractParam;
import com.cdz360.biz.model.trading.contract.param.ContractListParam;
import com.cdz360.biz.model.trading.contract.vo.ContractVo;
import com.cdz360.biz.model.trading.coupon.param.ActivityUserCouponParam;
import com.cdz360.biz.model.trading.coupon.param.BatchSendCouponParam;
import com.cdz360.biz.model.trading.coupon.param.CouponDictParam;
import com.cdz360.biz.model.trading.coupon.param.CouponDictSearchParam;
import com.cdz360.biz.model.trading.coupon.param.CouponSearchParam;
import com.cdz360.biz.model.trading.coupon.param.CreateActivityParam;
import com.cdz360.biz.model.trading.coupon.param.ListActivityParam;
import com.cdz360.biz.model.trading.coupon.param.UpdateActivityParam;
import com.cdz360.biz.model.trading.coupon.type.AcquireCouponResult;
import com.cdz360.biz.model.trading.coupon.vo.ActivityVo;
import com.cdz360.biz.model.trading.coupon.vo.CouponBi;
import com.cdz360.biz.model.trading.coupon.vo.CouponDictVo;
import com.cdz360.biz.model.trading.coupon.vo.CouponVo;
import com.cdz360.biz.model.trading.coupon.vo.DiscountVo;
import com.cdz360.biz.model.trading.evse.EvseInfo;
import com.cdz360.biz.model.trading.file.po.OssFilePo;
import com.cdz360.biz.model.trading.hlht.param.BindHlhtParam;
import com.cdz360.biz.model.trading.hlht.vo.HlhtSiteCommVo;
import com.cdz360.biz.model.trading.invoice.dto.CorpInvoiceRecordDto;
import com.cdz360.biz.model.trading.invoice.dto.InvoiceRecordOrderRefDto;
import com.cdz360.biz.model.trading.invoice.param.ListCorpInvoiceRecordParam;
import com.cdz360.biz.model.trading.invoice.param.ListInvoiceRecordOrderRefParam;
import com.cdz360.biz.model.trading.invoice.po.CorpInvoiceRecordPo;
import com.cdz360.biz.model.trading.invoice.po.InvoiceRecordOrderRefPo;
import com.cdz360.biz.model.trading.invoice.vo.CorpInvoiceRecordVo;
import com.cdz360.biz.model.trading.iot.vo.EvseInfoVo;
import com.cdz360.biz.model.trading.meter.param.MeterDataListParam;
import com.cdz360.biz.model.trading.meter.vo.MeterDataVo;
import com.cdz360.biz.model.trading.order.dto.CusCorpOrderBiDto;
import com.cdz360.biz.model.trading.order.dto.CusLastOrderSiteDto;
import com.cdz360.biz.model.trading.order.dto.CusOrderBiDto;
import com.cdz360.biz.model.trading.order.dto.PayBillInvoiceBi;
import com.cdz360.biz.model.trading.order.param.BillInvoiceVoParam;
import com.cdz360.biz.model.trading.order.param.CzOrderPointParam;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.param.ListCusOrderBiParam;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.model.trading.order.param.PrepaidOrderListParam;
import com.cdz360.biz.model.trading.order.param.ZftBillParam;
import com.cdz360.biz.model.trading.order.po.ChargerOrderTimeDivision;
import com.cdz360.biz.model.trading.order.po.PayBillPo;
import com.cdz360.biz.model.trading.order.vo.PayBillAccountDetailVo;
import com.cdz360.biz.model.trading.order.vo.PayBillBi;
import com.cdz360.biz.model.trading.order.vo.PayBillLinkChargeOrderVo;
import com.cdz360.biz.model.trading.order.vo.PayBillUsedDetail;
import com.cdz360.biz.model.trading.order.vo.PayBillVo;
import com.cdz360.biz.model.trading.order.vo.SettlementOrderVo;
import com.cdz360.biz.model.trading.order.vo.UserBillAccountNameVo;
import com.cdz360.biz.model.trading.order.vo.ZftBillBi;
import com.cdz360.biz.model.trading.order.vo.ZftBillVo;
import com.cdz360.biz.model.trading.peek.invoice.dto.PeekInvoiceDto;
import com.cdz360.biz.model.trading.profit.conf.vo.CorpProfitBaseVo;
import com.cdz360.biz.model.trading.site.dto.SiteSimpleDto;
import com.cdz360.biz.model.trading.site.param.ChargeJobLogParam;
import com.cdz360.biz.model.trading.site.param.ChargeJobParam;
import com.cdz360.biz.model.trading.site.param.ListOvertimeParkFeeOrderParam;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.po.EvseCfgSchedulePo;
import com.cdz360.biz.model.trading.site.po.OvertimeParkFeeOrderPo;
import com.cdz360.biz.model.site.po.PriceTemplatePo;
import com.cdz360.biz.model.trading.site.po.SiteChargeJobPo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.site.vo.EvseCfgScheduleVo;
import com.cdz360.biz.model.trading.site.vo.MoveCorpNoCardList;
import com.cdz360.biz.model.trading.site.vo.MoveCorpNoCardVo;
import com.cdz360.biz.model.trading.site.vo.OvertimeParkFeeOrderBi;
import com.cdz360.biz.model.trading.site.vo.OvertimeParkFeeOrderVo;
import com.cdz360.biz.model.trading.site.vo.PriceSchemeSiteVo;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobMoveCorpList;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobPlugVo;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobVo;
import com.cdz360.biz.model.trading.site.vo.SiteConfStartList;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import com.cdz360.biz.model.trading.soc.vo.MoveCorpUserSocStrategyList;
import com.cdz360.biz.model.trading.warn.param.AddUserWarnParam;
import com.cdz360.biz.model.trading.warn.param.UserWarnListParam;
import com.cdz360.biz.model.trading.warn.param.WarnListParam;
import com.cdz360.biz.model.trading.warn.param.WarnSubParam;
import com.cdz360.biz.model.trading.warn.po.WarningPo;
import com.cdz360.biz.model.trading.warn.vo.UserWarningVo;
import com.cdz360.biz.model.wallet.vo.RefundReasonVo;
import com.cdz360.biz.model.oss.OssStsDto;
import com.chargerlinkcar.framework.common.constant.CheckTaxStatus;
import com.chargerlinkcar.framework.common.domain.OrderReserveVo;
import com.chargerlinkcar.framework.common.domain.PointRecDto;
import com.chargerlinkcar.framework.common.domain.SitePersonaliseDTO;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordAuditParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordManualParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordUpdateParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceRecordDetail;
import com.chargerlinkcar.framework.common.domain.param.ListPriceSchemeSiteUseParam;
import com.chargerlinkcar.framework.common.domain.param.SiteChargeJobParam;
import com.chargerlinkcar.framework.common.domain.peek.invoice.param.PeekInvoiceParam;
import com.chargerlinkcar.framework.common.domain.request.StartChargerRequest;
import com.chargerlinkcar.framework.common.domain.request.StopChargerRequest;
import com.chargerlinkcar.framework.common.domain.vo.BatteryVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderSite;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderBiVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderOvertimeParkInfoVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderTimeShareBiVo;
import com.chargerlinkcar.framework.common.domain.vo.UpdateOrderVo;
import io.swagger.v3.oas.annotations.Operation;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 *
 * @since 2019.3.21
 */
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, fallbackFactory = DataCoreFeignClientHystrixFactory.class)
public interface DataCoreFeignClient {


    /**
     * 获取充值记录列表
     *
     * @param payBillParam
     * @return
     */
    @PostMapping("/dataCore/paybill/orderList")
    ListResponse<PayBillVo> payBillList(@RequestBody PayBillParam payBillParam);

    /**
     * 获取直付通记录列表
     *
     * @param payBillParam
     * @return
     */
    @PostMapping("/dataCore/paybill/getZftBillList")
    ListResponse<ZftBillVo> zftBillList(@RequestBody ZftBillParam payBillParam);

    @PostMapping("/dataCore/paybill/userBillAccountName")
    ObjectResponse<UserBillAccountNameVo> userBillAccountName(
        @RequestBody PayBillParam payBillParam);

    /**
     * 企业客户获取充值记录
     *
     * @param payBillParam
     * @return
     */
    @PostMapping("/dataCore/paybill/invoiceOrderList")
    ListResponse<PayBillInvoiceBi> invoiceOrderList(@RequestBody PayBillParam payBillParam);

    @PostMapping("/dataCore/paybill/invoiceOrderBiForCorp")
    ObjectResponse<OrderBiVo> invoiceOrderBiForCorp(@RequestBody PayBillParam param);

    /**
     * 统计充值记录数据
     *
     * @param param
     * @return
     */
    @PostMapping("/dataCore/paybill/bi")
    ListResponse<PayBillBi> payBillBi(@RequestBody PayBillParam param);

    @PostMapping("/dataCore/zftbill/bi")
    ListResponse<ZftBillBi> zftBillBi(@RequestBody ZftBillParam param);

    /**
     * 通过充值记录Id更新充值信息
     *
     * @param po
     * @return
     */
    @PostMapping("/dataCore/paybill/updateById")
    ObjectResponse<Integer> updateById(@RequestBody PayBillPo po);

    /**
     * 通过充值记录Id更新充值信息
     *
     * @param po
     * @return
     */
    @PostMapping("/dataCore/paybill/updateByOrderId")
    ObjectResponse<Integer> updateByOrderId(@RequestBody PayBillPo po);

    /**
     * 获取充值记录资金块详情
     *
     * @param orderId
     * @param orderNo
     * @return
     */
    @GetMapping("/dataCore/paybill/pointRecLog")
    ObjectResponse<PayBillUsedDetail> pointRecLog(
        @RequestParam(value = "orderId") String orderId,
        @RequestParam(value = "orderNo") String orderNo);

    /**
     * 获取单号当前积分块信息
     * @param param
     * @return
     */
    @PostMapping("/dataCore/paybill/getCzOrderPointsInfo")
    ListResponse<PointRecDto> getCzOrderPointsInfo(@RequestBody CzOrderPointParam param);

    /**
     * 充值前账户信息
     *
     * @param orderId
     * @return
     */
    @GetMapping("/dataCore/paybill/accountDetail")
    ObjectResponse<PayBillAccountDetailVo> getAccountDetail(
        @RequestParam("orderId") String orderId);

    /**
     * 充值记录查看
     *
     * @param orderId
     * @return
     */
    @GetMapping("/dataCore/paybill/view")
    ObjectResponse<PayBillVo> payBillView(@RequestParam("orderId") String orderId);


    @GetMapping("/dataCore/paybill/orderPointRecLog")
    ListResponse<PayBillLinkChargeOrderVo> orderPointRecLog(
        @RequestParam("orderNo") String orderNo);

    /**
     * 判断充值订单是否存在资金块在发票中心已开票或审核中的开票订单
     *
     * @param orderId
     * @return
     */
    @PostMapping("/dataCore/paybill/checkTaxStatus")
    ObjectResponse<CheckTaxStatus> checkTaxStatus(@RequestParam("orderId") String orderId);

    @Operation(summary = "退款数据分析")
    @GetMapping("/dataCorder/refund/analyze")
    ObjectResponse<RefundAnalyzeVo> refundAnalyze(
        @RequestParam(value = "startDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
        @RequestParam(value = "stopDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date stopDate);

    @Operation(summary = "退款原因列表")
    @GetMapping("/dataCorder/refund/list")
    ListResponse<RefundReasonVo> refundList(
        @RequestParam(value = "cusName", required = false) String cusName,
        @RequestParam(value = "cusPhone", required = false) String cusPhone,
        @RequestParam(value = "cusNote", required = false) String cusNote,
        @RequestParam(value = "start") int start,
        @RequestParam(value = "size") int size);


    /**
     * 计费模板使能状态调整
     *
     * @param id
     * @param enable
     * @return
     */
    @GetMapping("/dataCore/priceTemp/enablePriceSchema")
    BaseResponse enable(@RequestParam(value = "token") String token,
        @RequestParam(value = "id") Long id,
        @RequestParam(value = "enable") Boolean enable);

    // 光伏收益计费模板批量禁用
    @PostMapping("/dataCore/priceTemp/deletePvPriceSchema")
    BaseResponse deletePvPriceSchema(@RequestBody List<Long> priceIdList);

    /**
     * 批量插入计费模板数据
     *
     * @param poList
     * @return
     */
    @PostMapping("/dataCore/evseCfgSchedule/batchInsert")
    ObjectResponse<Integer> batchInsert(@RequestBody List<EvseCfgSchedulePo> poList);

    /**
     * 即时下发计费模板
     *
     * @param param
     * @return
     */
    @PostMapping("/dataCore/priceTemp/down")
    BaseResponse priceTempDown(@RequestBody ModifyEvseCfgParam param);

    /**
     * 修改桩计费模板-用于不支持计费下发的桩
     *
     * @param param
     * @return
     */
    @PostMapping("/dataCore/priceTemp/modifyEvsePrice")
    BaseResponse modifyEvsePrice(@RequestBody ModifyEvseCfgParam param);

    /**
     * 获取计费模板定时下发的场站信息
     *
     * @param param
     * @return
     */
    @PostMapping("/dataCore/evseCfgSchedule/getByPriceSchemeId")
    ListResponse<PriceSchemeSiteVo> getByPriceSchemeId(
        @RequestBody ListPriceSchemeSiteUseParam param);

    /**
     * 过滤可以下发的桩编号
     *
     * @param evseNoList
     * @return
     */
    @PostMapping("/dataCore/evseCfgSchedule/downFilter")
    ListResponse<String> downFilter(@RequestBody List<String> evseNoList);

    /**
     * 获取桩定时信息
     *
     * @param evseNoList
     * @return
     */
    @PostMapping("/dataCore/evseCfgSchedule/getByEvseNo")
    ListResponse<EvseCfgScheduleVo> getEvseCfgScheduleByEvseNo(
        @RequestBody List<String> evseNoList);

    /**
     * 获取桩的计费模板信息
     *
     * @param evseNo
     * @return
     */
    @GetMapping("/dataCore/priceTemp/getByEvesNo")
    ObjectResponse<PriceTemplatePo> getPriceSchemeByEvesNo(
        @RequestParam(value = "evseNo") String evseNo);

    /**
     * 更新桩的配置信息
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/priceTemp/evseSetting")
    BaseResponse updateEvseSetting(@RequestBody ModifyEvseCfgParam param);

    /**
     * 获取枪头订单信息列表
     *
     * @param orderNo
     * @return
     */
    @GetMapping(value = "/dataCore/chargerQuery/getChargerOrderInfo")
    @Deprecated
    ObjectResponse<OrderInMongo> getChargerOrderInfo(
        @RequestParam(value = "orderNo") String orderNo);

    /**
     * 后台开启充电
     *
     * @param chargerRequest
     * @return
     */
    @PostMapping("/dataCore/orderCharger/webStartCharger")
    BaseResponse webStartCharger(@RequestBody StartChargerRequest chargerRequest);

    /**
     * 后台关闭充电
     *
     * @param stopRequest
     * @return
     */
    @PostMapping("/dataCore/orderCharger/webStopCharger")
    BaseResponse webStopCharger(@RequestBody List<StopChargerRequest> stopRequest);

    /**
     * 获取云端充电请求队列下发情况
     *
     * @param plugNoList
     * @return
     */
    @PostMapping("/dataCore/orderCharger/getReserveInfoByPlugNoList")
    ObjectResponse<OrderReserveVo> getReserveInfoByPlugNoList(@RequestBody List<String> plugNoList);

    /**
     * 根据plugNo查询定时充电任务信息
     *
     * @param plugNoList
     * @return
     */
    @PostMapping("/dataCore/siteChargeJob/getSiteJobByPlugNoList")
    ListResponse<SiteChargeJobPlugVo> getSiteJobByPlugNoList(@RequestBody List<String> plugNoList);

    /**
     * 绑定枪的定时充电任务
     *
     * @param param
     * @return
     */
    @PostMapping("/dataCore/siteChargeJobs/bindingJob")
    BaseResponse bindingJob(@RequestBody SiteChargeJobParam param);

    /**
     * 定时充电任务-启用/停用
     *
     * @param jobId
     * @param jobStatus
     * @return
     */
    @GetMapping("/dataCore/siteChargeJobs/changeJobStatus")
    BaseResponse changeJobStatus(@RequestParam(value = "jobId") Long jobId,
        @RequestParam(value = "jobStatus") Integer jobStatus);

    /**
     * 修改定时充电任务
     *
     * @param param
     * @return
     */
    @PostMapping("/dataCore/siteChargeJobs/modifyChargeJob")
    BaseResponse modifyChargeJob(@RequestBody SiteChargeJobParam param);

    /**
     * 解绑枪的定时充电任务
     *
     * @param plugNoList
     * @return
     */
    @PostMapping("/dataCore/siteChargeJobs/unbindingJob")
    BaseResponse unbindingJob(@RequestBody List<String> plugNoList);

    @GetMapping("/dataCore/siteChargeJobs/getSiteChargeJobBySiteId")
    ListResponse<SiteChargeJobPo> getSiteChargeJobBySiteId(
        @RequestParam(value = "siteId", required = true)
        String siteId,
        @RequestParam(value = "jobName", required = false)
        String jobName);

    /**
     * 获取场站下的定时充电任务
     *
     * @param param
     * @return
     */
    @PostMapping("/dataCore/siteChargeJobs/jobList")
    ListResponse<SiteChargeJobVo> jobList(@RequestBody ChargeJobParam param);

    /**
     * 定时充电任务日志列表
     *
     * @param param
     * @return
     */
    @PostMapping("/dataCore/siteChargeJobs/getChargeJobLogList")
    ListResponse getChargeJobLogList(@RequestBody ChargeJobLogParam param);

    /**
     * 管理员订阅场站列表
     *
     * @param sysUid
     * @return
     */
    @GetMapping("/dataCore/warn/getUserSubSiteList")
    ListResponse<String> getUserSubSiteList(@RequestParam("sysUid") Long sysUid);

    /**
     * 管理员订阅场站列表
     *
     * @param sysUid
     * @return
     */
    @GetMapping("/dataCore/warn/getUserSubSiteInfoList")
    ListResponse<SiteVo> getUserSubSiteInfoList(@RequestParam("sysUid") Long sysUid,
        @RequestParam("start") Long start,
        @RequestParam("size") Long size);

    /**
     * 管理员订阅告警码列表
     *
     * @param sysUid
     * @return
     */
    @GetMapping("/dataCore/warn/getUserSubCodeList")
    ListResponse<String> getUserSubCodeList(@RequestParam("sysUid") Long sysUid);

    /**
     * 充电管理平台，管理员订阅
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/warn/addOrUpdateUserWarn")
    BaseResponse addOrUpdateUserWarn(@RequestBody AddUserWarnParam param);

    /**
     * 支撑平台消息订阅
     *
     * @param param
     * @return
     */
    @PostMapping("/dataCore/warn/queryWarningSub")
    BaseResponse queryWarningSub(@RequestBody WarnSubParam param);

    /**
     * 充电管理平台消息订阅列表
     *
     * @param param
     * @return
     */
    @PostMapping("/dataCore/warn/getUserWarnList")
    ListResponse<UserWarningVo> getUserWarnList(@RequestBody UserWarnListParam param);

    /**
     * 支撑平台告警列表
     *
     * @param param
     * @return
     */
    @PostMapping("/dataCore/warn/getWarnList")
    ListResponse<WarningPo> getWarnList(@RequestBody WarnListParam param);

    /**
     * 获取场站个性化设置
     *
     * @param siteId
     * @return
     */
    @GetMapping(value = "/dataCore/site/getPersonalise")
    ObjectResponse<SitePersonaliseDTO> getPersonalise(
        @RequestParam(value = "siteId") String siteId);


    /**
     * 变更场站个性化设置
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/dataCore/site/updatePersonalise")
    BaseResponse updatePersonalise(@RequestBody SitePersonaliseDTO dto);


    /**
     * 获取客户充电统计信息
     *
     * @param param
     * @return
     */
    @Deprecated
    @PostMapping("/dataCore/orderData/getCusOrderBiList")
    ListResponse<CusOrderBiDto> getCusOrderBiList(@RequestBody ListCusOrderBiParam param);

    @PostMapping("/dataCore/orderData/getCusAndCorpOrderBiList")
    ObjectResponse<CusCorpOrderBiDto> getCusAndCorpOrderBiList(
        @RequestBody ListCusOrderBiParam param);

    @PostMapping("/dataCore/orderData/getCusOvertimeParkOrderBiList")
    ListResponse<OvertimeParkFeeOrderPo> getCusOvertimeParkOrderBiList(
        @RequestBody ListCusOrderBiParam param);

    /**
     * 获取用户最后一次充电场站信息
     *
     * @param param
     * @return
     */
    @Deprecated
    @PostMapping("/dataCore/orderData/getCusOrderLastSiteInfoList")
    ListResponse<CusLastOrderSiteDto> getCusOrderLastSiteInfoList(
        @RequestBody ListCusOrderBiParam param);

    // 检查所选预付订单是否可开票
    @PostMapping(value = "/dataCore/orderData/checkPrepaidOrderList")
    BaseResponse checkWhetherThePrepaidOrderCanBeInvoiced(
        @RequestBody PrepaidOrderListParam param);

    @PostMapping(value = "/dataCore/orderData/getBillInvoiceVoList")
    ListResponse<BillInvoiceVo> getBillInvoiceVoList(@RequestBody BillInvoiceVoParam param);

    @GetMapping(value = "/dataCore/priceTemp/sendPriceSchema")
    ObjectResponse<String> sendPriceSchema(@RequestParam(value = "evseNo") String evseNo);

    /**
     * 获取未生成账单的充电订单
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/order/getNotSettlementOrderList")
    ListResponse<SettlementOrderVo> getNotSettlementOrderList(
        @RequestBody ListSettlementOrderParam param);

    /**
     * 手机号领券
     *
     * @param phone
     * @param activityId
     * @return
     */
    @RequestMapping(value = "/dataCore/activity/acquireCoupon", method = RequestMethod.GET)
    ObjectResponse<AcquireCouponResult> acquireCoupon(@RequestParam("activityId") Long activityId,
        @RequestParam("phone") String phone);

    /**
     * 用户手机号是否领取过优惠券统计
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/activity/hasUserAcquiredCoupon")
    ObjectResponse<Map<String, Boolean>> hasUserAcquiredCoupon(
        @RequestBody ActivityUserCouponParam param);

    @PostMapping(value = "/dataCore/couponDict/dictCreate")
    BaseResponse dictCreate(@RequestBody CouponDictParam req);

    @PostMapping(value = "/dataCore/couponDict/getDictList")
//    ListResponse<CouponDictPo> getDictList(@RequestBody CouponDictSearchParam req,
//                                           @RequestParam("_index") Integer _index,
//                                           @RequestParam("_size") Integer _size);
    ListResponse<CouponDictVo> getDictList(@RequestBody CouponDictSearchParam req);

    @PostMapping(value = "/dataCore/couponDict/disableDict")
    BaseResponse disableDict(@RequestParam(value = "id") Long id);

    @PostMapping(value = "/dataCore/activity/createActivity")
    BaseResponse createActivity(@RequestBody CreateActivityParam req);

    @PostMapping(value = "/dataCore/activity/updateActivity")
    BaseResponse updateActivity(@RequestBody UpdateActivityParam req);

    @GetMapping(value = "/dataCore/partner/getSiteByPartnerCode")
    ObjectResponse<HlhtSiteCommVo> getSiteByPartnerCode(
        @RequestParam(value = "partnerCode") String code);

    @GetMapping(value = "/dataCore/activity/activeActivity")
    BaseResponse activeActivity(@RequestParam("id") Long id);

    @GetMapping(value = "/dataCore/activity/abortActivity")
    BaseResponse abortActivity(@RequestParam("id") Long id);

    @GetMapping(value = "/dataCore/activity/showInMobile")
    BaseResponse showInMobile(@RequestParam("id") Long id);

    @GetMapping(value = "/dataCore/activity/hideInMobile")
    BaseResponse hideInMobile(@RequestParam("id") Long id);

    @GetMapping(value = "/dataCore/activity/getActivityDetail")
    ObjectResponse<ActivityVo> getActivityDetail(@RequestParam("id") Long id);

    @GetMapping(value = "/dataCore/activity/couponBi")
    ObjectResponse<CouponBi> couponBi(@RequestParam("id") Long id);

    @PostMapping(value = "/dataCore/activity/userActivityCouponList")
    ListResponse<CouponVo> userActivityCouponList(@RequestBody CouponSearchParam req);

    /**
     * 企业客户开票追加订单(充值/充电/账单)
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/invoice/corpInvoiceAppendOrder")
    ObjectResponse<CorpInvoiceRecordVo> corpInvoiceAppendOrder(
        @RequestBody CorpInvoiceRecordUpdateParam param);

    /**
     * 预计算，客户开票追加订单(充值/充电/账单)
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/invoice/peekInvoice")
    ObjectResponse<PeekInvoiceDto> peekInvoice(@RequestBody PeekInvoiceParam param);

    /**
     * 企业客户开票移除订单(充值/充电/账单)
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/invoice/corpInvoiceRemoveOrder")
    ObjectResponse<CorpInvoiceRecordVo> corpInvoiceRemoveOrder(
        @RequestBody CorpInvoiceRecordUpdateParam param);

    /**
     * 企业开票记录详情
     *
     * @param applyNo
     * @return
     */
    @GetMapping(value = "/dataCore/invoice/corpInvoiceRecordDetail")
    ObjectResponse<CorpInvoiceRecordDetail> corpInvoiceRecordDetail(
        @RequestParam(value = "applyNo") String applyNo);

    /**
     * 企业开票审核
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/invoice/corpInvoiceRecordManual")
    ObjectResponse<Boolean> corpInvoiceRecordManual(
        @RequestBody CorpInvoiceRecordManualParam param);

    /**
     * 企业开票审核
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/invoice/corpInvoiceRecordAudit")
    ObjectResponse<Integer> corpInvoiceRecordAudit(@RequestBody CorpInvoiceRecordAuditParam param);

    @PostMapping(value = "/dataCore/activity/listActivity")
    ListResponse<ActivityVo> listActivity(@RequestBody ListActivityParam req);

    /**
     * 获取企业开票列表
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/invoice/findCorpInvoiceRecordList")
    ListResponse<CorpInvoiceRecordDto> findCorpInvoiceRecordList(
        @RequestBody ListCorpInvoiceRecordParam param);

    // 获取企业开票记录
    @GetMapping(value = "/dataCore/invoice/getRecordByProcInstId")
    ObjectResponse<CorpInvoiceRecordDto> getRecordByProcInstId(
        @RequestParam(value = "procInstId") String procInstId);

    /**
     * 获取充电订单列表
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/order/listChargerOrder")
    ListResponse<ChargerOrderVo> listChargerOrder(@RequestBody ListChargeOrderParam param);

    /**
     * 企业订单汇总
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/order/chargeOrderBiForCorp")
    ObjectResponse<OrderBiVo> chargeOrderBiForCorp(@RequestBody ListChargeOrderParam param);

    /**
     * 按照场站汇总企业开票订单
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/order/chargerOrderGroupBySite")
    ListResponse<ChargerOrderSite> chargerOrderGroupBySite(@RequestBody ListChargeOrderParam param);

    /**
     * 获取企业客户开票充电订单尖峰平谷金额汇总
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/order/chargerOrderGroupByTimeShareFee")
    ListResponse<OrderTimeShareBiVo> chargerOrderGroupByTimeShareFee(
        @RequestBody ListChargeOrderParam param);

    /**
     * 企业开票记录删除
     *
     * @param applyNo
     * @return
     */
    @GetMapping(value = "/dataCore/invoice/deleteCorpInvoiceRecordByApplyNo")
    ObjectResponse<Integer> deleteCorpInvoiceRecordByApplyNo(
        @RequestParam(value = "applyNo") String applyNo);

    // 企业开票记录删除(用于企客开票流程调用)
    @GetMapping(value = "/dataCore/invoice/deleteCorpInvoiceRecordByOa")
    ObjectResponse<Integer> deleteCorpInvoiceRecordByOa(
        @RequestParam(value = "procInstId") String procInstId,
        @RequestParam(value = "physicalDeletion") Boolean physicalDeletion);

    /**
     * 企业开票记录修改回款状态
     *
     * @param po
     * @return
     */
    @PostMapping(value = "/dataCore/invoice/updateCorpInvoiceRecordReturnFlag")
    ObjectResponse<Integer> updateCorpInvoiceRecordReturnFlag(@RequestBody CorpInvoiceRecordPo po);

    /**
     * 账单开票按照场站汇总
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/invoice/recordOrderGroupBySite")
    ListResponse<ChargerOrderSite> recordOrderGroupBySite(
        @RequestBody ListInvoiceRecordOrderRefParam param);

    /**
     * 获取企业客户开票账单 金额数据
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/invoice/recordOrderGroupByTimeShareFee")
    ListResponse<OrderTimeShareBiVo> recordOrderGroupByTimeShareFee(
        @RequestBody ListInvoiceRecordOrderRefParam param);

    /**
     * 获取企业开票记录关联的订单列表
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/invoice/getInvoiceRecordOrderList")
    ListResponse<InvoiceRecordOrderRefDto> getInvoiceRecordOrderList(
        @RequestBody ListInvoiceRecordOrderRefParam param);

    // 通过OA获取企业开票记录关联的订单列表
    @GetMapping(value = "/dataCore/invoice/getInvoiceRecordOrderListByOa")
    ListResponse<InvoiceRecordOrderRefPo> getInvoiceRecordOrderListByOa(
        @RequestParam(value = "procInstId") String procInstId);

    /**
     * 企业客户开票记录提交到审核(非财务)
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/dataCore/invoice/corpInvoiceRecordSubmit2Audit")
    ObjectResponse<Integer> corpInvoiceRecordSubmit2Audit(@RequestBody CorpInvoiceRecordDto dto);
//
//    /**
//     * 个人&商户会员开票记录提交到审核
//     * @param dto
//     * @return
//     */
//    @PostMapping(value = "/dataCore/invoice/userInvoiceRecordSubmit2Audit")
//    ObjectResponse<InvoicedRecordDto> userInvoiceRecordSubmit2Audit(@RequestBody UserInvoiceRecordParam dto);

    @GetMapping("/dataCore/soc/queryBlocUserForSiteSoc")
    ListResponse<SocCorpVo> queryBlocUserForSiteSoc(@RequestParam(value = "siteId") String siteId);

//    @PostMapping("/dataCore/soc/queryStrategy")
//    ListResponse<SocStrategyDict> queryStrategy(@RequestBody QueryStrategyParam param);

//    @PostMapping("/dataCore/soc/queryCorpStrategyCreditCus")
//    ListResponse<UserSocStrategyCreditCusVo> queryCorpStrategyCreditCus(QueryStrategyParam param);

//    @PostMapping("/dataCore/soc/queryCorpStrategyVin")
//    ListResponse<UserSocStrategyVinVo> queryCorpStrategyVin(QueryStrategyParam param);

//    @PostMapping("/dataCore/soc/addCorpStrategyCreditCus")
//    ObjectResponse<Integer> addCorpStrategyCreditCus(List<QueryStrategyParam> params);

//    @PostMapping("/dataCore/soc/removeCorpStrategyCreditCus")
//    ObjectResponse<Integer> removeCorpStrategyCreditCus(List<QueryStrategyParam> params);

//    @PostMapping("/dataCore/soc/addCorpStrategyVin")
//    ObjectResponse<Integer> addCorpStrategyVin(List<QueryStrategyParam> params);

    /**
     * 校验充值记录是否可以减少
     *
     * @param refBillNo
     * @return true -- 已经关联开票
     */
    @GetMapping("/dataCore/paybill/checkRefPayBill")
    ObjectResponse<Boolean> checkRefPayBill(@RequestParam(value = "refBillNo") String refBillNo);

    /**
     * 获取桩信息
     *
     * @param evseNo
     * @return
     */
    @GetMapping(value = "/dataCore/evseManager/getEvseInfo")
    ObjectResponse<EvseInfo> getEvseInfo(@RequestParam(value = "evseNo") String evseNo);

    /**
     * 按场站下发默认配置(场站下所有桩)
     *
     * @param siteId
     * @return
     */
    @GetMapping(value = "/dataCore/evseManager/downDefultSetting2AllEvse")
    BaseResponse downDefultSetting2AllEvse(@RequestParam("siteId") String siteId);

    /**
     * 拼装下发计费模板所需的入参
     *
     * @param siteId
     * @return
     */
    @GetMapping(value = "/dataCore/evseManager/getModifyEvseCfgParam")
    ObjectResponse<ModifyEvseCfgParam> getModifyEvseCfgParam(@RequestParam("siteId") String siteId);

    /**
     * 解析支撑平台脱机桩excel文件
     *
     * @param list
     * @return
     */
    @PostMapping("/dataCore/evseManager/parseOfflineEvseExcel")
    ObjectResponse<OfflineEvseImportVo> parseOfflineEvseExcel(@RequestBody List<List<String>> list);


    /**
     * 获取脱机桩列表
     *
     * @param param
     * @return
     */
    @PostMapping("/dataCore/evseManager/getOfflineEvseList")
    ListResponse<EvseInfoVo> getOfflineEvseList(@RequestBody ListEvseParam param);

    /**
     * 按桩重新下发场站默认配置
     *
     * @param evseNo
     * @return
     */
    @GetMapping(value = "/dataCore/evseManager/downSettingByEvse")
    BaseResponse downSettingByEvse(@RequestParam("evseNo") String evseNo);

//    /**
//     * 企业客户更新开票抬头信息
//     *
//     * @param data
//     * @return
//     */
//    @PostMapping(value = "/dataCore/invoice/updateCorpInvoiceRecordModelId")
//    ObjectResponse<Integer> updateCorpInvoiceRecordModelId(@RequestBody UpdateIdDTO data);

//    /**
//     * 企业客户更新开票模板信息
//     *
//     * @param updateIdDTOList
//     * @return
//     */
//    @PostMapping(value = "/dataCore/invoice/updateCorpInvoiceRecordProTempId")
//    ObjectResponse<Integer> updateCorpInvoiceRecordProTempId(
//        @RequestBody List<UpdateIdDTO> updateIdDTOList);


    /**
     * 获取超停收费订单列表
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/overtimeParkFeeOrder/findAll")
    ListResponse<OvertimeParkFeeOrderVo> findOvertimeParkFeeOrderAll(
        @RequestBody ListOvertimeParkFeeOrderParam param);

    /**
     * 获取超停收费订单分时详情
     * @param orderNo
     * @return
     */
    @GetMapping(value = "/dataCore/overtimeParkFeeOrder/getOverTimeParkDivisionByOrderNo")
    ObjectResponse<OrderOvertimeParkInfoVo> getOverTimeParkDivisionByOrderNo(
        @RequestParam(value = "orderNo") String orderNo);

    /**
     * 超停收费订单统计
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/overtimeParkFeeOrder/orderBi")
    ObjectResponse<OvertimeParkFeeOrderBi> overtimeParkFeeOrderBi(
        @RequestBody ListOvertimeParkFeeOrderParam param);

    /**
     * 商户绑定互联站点
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/siteHlht/bindHlhtSite")
    BaseResponse bindHlhtSite(@RequestBody BindHlhtParam param);

    /**
     * 商户解绑互联站点
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/siteHlht/unbindHlhtSite")
    BaseResponse unbindHlhtSite(@RequestBody BindHlhtParam param);

    @GetMapping(value = "/dataCore/orderData/siteAndPlugStatusFillHlhtData")
    ObjectResponse<SiteAndPlugBiVo> siteAndPlugStatusFillHlhtData(
        @RequestParam(value = "commId", required = false) Long commId,
        @RequestParam(value = "commIdChain", required = false) String commIdChain);


    @RequestMapping(value = "/dataCore/site/getMoveCorpSiteConfStart", method = RequestMethod.GET)
    ObjectResponse<SiteConfStartList> getMoveCorpSiteConfStart(@RequestParam("corpId") Long corpId,
        @RequestParam("commId") Long commId);

    @RequestMapping(value = "/dataCore/site/getMoveCorpSoc", method = RequestMethod.GET)
    ObjectResponse<MoveCorpUserSocStrategyList> getMoveCorpSoc(@RequestParam("corpId") Long corpId,
        @RequestParam("commId") Long commId);

    @GetMapping("/dataCore/siteChargeJobs/getMoveCorpDetail")
    ObjectResponse<SiteChargeJobMoveCorpList> getMoveCorpDetail(@RequestParam("corpId") Long corpId,
        @RequestParam("commId") Long commId);

    @GetMapping("/dataCore/site/getSiteListByIdChain")
    ListResponse<SiteSimpleDto> getSiteListByIdChain(
        @RequestParam(value = "idChain", required = false) String idChain);

    /**
     * 查询充值单
     *
     * @param orderId
     * @return
     */
    @GetMapping("/dataCore/paybill/payOrderQuery")
    ObjectResponse<PayBillVo> payOrderQuery(@RequestParam(value = "orderId") String orderId);

    @PostMapping("/dataCore/orderData/estimateOrderTimeDivisionList")
    ListResponse<ChargerOrderTimeDivision> estimateOrderTimeDivisionList(
        @RequestBody UpdateOrderVo updateOrderVo);

//    @PostMapping("/dataCore/site/createCorpSocStrategy")
//    BaseResponse createCorpSocStrategy(@RequestBody SocStrategyDict param);

    @GetMapping(value = "/dataCore/orderData/getBatteryVo")
    ObjectResponse<BatteryVo> getBatteryVo(@RequestParam(value = "vin") String vin,
        @RequestParam(value = "commIdChain") String commIdChain);

//    @PostMapping("/dataCore/site/updateCorpStrategy")
//    BaseResponse updateCorpStrategy(@RequestBody SocStrategyDict param);

//    @GetMapping("/dataCore/site/deleteCorpStrategy")
//    BaseResponse deleteCorpStrategy(@RequestParam(value = "id") Long id);


    @GetMapping("/dataCore/corpProfitConf/getCorpProfitConf")
    ObjectResponse<CorpProfitBaseVo> getCorpProfitConf(@RequestParam("corpId") Long corpId);

    @PostMapping("/dataCore/corpProfitConf/addCorpProfitConf")
    BaseResponse addCorpProfitConf(@RequestBody CorpProfitBaseVo confList);

    @PostMapping("/dataCore/corpProfitConf/disableCorpProfitConf")
    BaseResponse disableCorpProfitConf(@RequestParam("corpId") Long corpId);

    /**
     * 获取文件上传的STS信息
     *
     * @return
     */
    @GetMapping(value = "/dataCore/oss/getSts")
    ObjectResponse<OssStsDto> getSts();

    @GetMapping(value = "/dataCore/oss/getPrivateSts")
    ObjectResponse<OssStsDto> getPrivateSts();

    /**
     * 添加合约
     *
     * @param param
     * @return
     */
    @PostMapping("/dataCore/contract/addContract")
    BaseResponse addContract(@RequestBody AddContractParam param);

    /**
     * 删除合约
     *
     * @param id
     * @param idChain
     * @param sysUid
     * @return
     */
    @GetMapping("/dataCore/contract/delContract")
    BaseResponse delContract(@RequestParam(value = "id") Long id,
        @RequestParam(value = "idChain", required = false) String idChain,
        @RequestParam(value = "sysUid") Long sysUid);

    /**
     * 编辑合约
     *
     * @param param
     * @return
     */
    @PostMapping("/dataCore/contract/updateContract")
    BaseResponse updateContract(@RequestBody AddContractParam param);

    /**
     * 合约详情
     *
     * @param contractId
     * @param idChain
     * @return
     */
    @GetMapping("/dataCore/contract/getContractById")
    ObjectResponse<ContractVo> getContractById(@RequestParam("contractId") Long contractId,
        @RequestParam(value = "idChain", required = false) String idChain);

    /**
     * 合约列表
     *
     * @param param
     * @return
     */
    @PostMapping("/dataCore/contract/getContractList")
    ListResponse<ContractVo> getContractList(@RequestBody ContractListParam param);

    @PostMapping("/dataCore/file/addFile")
    ObjectResponse<OssFilePo> addFile(@RequestBody OssFilePo param);


    /**
     * 根据场站集合获取有哪些类型(光储充)
     *
     * @param siteIdList
     * @return
     */
    @PostMapping("/dataCore/site/ywUserSiteCategoryList")
    ListResponse<SiteCategory> ywUserSiteCategoryList(@RequestBody List<String> siteIdList);

    @GetMapping(value = "/dataCore/site/getMoveCorpNoCard")
    ObjectResponse<MoveCorpNoCardList> getMoveCorpNoCard(@RequestParam("corpId") Long corpId,
        @RequestParam("commId") Long commId);

    @GetMapping("/dataCore/site/getCorpNoCardList")
    ListResponse<MoveCorpNoCardVo> getCorpNoCardList(@RequestParam(value = "corpId") Long corpId);

    @GetMapping("/dataCore/site/getCommNoCardList")
    ListResponse<SiteVo> getCommNoCardList(@RequestParam(value = "commId") Long commId,
        @RequestParam(value = "userId") Long userId);

    @PostMapping("/dataCore/site/getSiteById")
    ObjectResponse<SitePo> getSiteById(@RequestParam("siteId") String siteId);

    @PostMapping("/dataCore/site/getSiteTinyList")
    ListResponse<SiteTinyDto> getSiteTinyList(@RequestBody ListSiteParam param);

    @GetMapping(value = "/dataCore/activity/discountInfo")
    ObjectResponse<DiscountVo> discountInfo(@RequestParam("commId") Long commId,
        @RequestParam("amount") BigDecimal amount);

    @GetMapping(value = "/dataCore/activity/hasActivity")
    ObjectResponse<ActivityVo> hasActivity(@RequestParam("commId") Long commId,
        @RequestParam("accountType") Long accountType);

    @PostMapping(value = "/dataCore/overtimeParkFeeOrder/cancel")
    ObjectResponse<Integer> overtimeParkFeeOrderCancel(
        @RequestBody ListOvertimeParkFeeOrderParam param);

    @PostMapping("/dataCore/site/getSiteListByGids")
    ListResponse<String> getSiteListByGids(@RequestBody ListSiteParam params);

    @PostMapping(value = "/dataCore/coupon/batchSendCoupon")
    BaseResponse batchSendCoupon(@RequestBody BatchSendCouponParam param);

    @GetMapping(value = "/dataCore/site/getUserListBySiteId")
    ListResponse<SysUserVo> getUserListBySiteId(@RequestParam(value = "siteId") String siteId);

    @PostMapping(value = "/dataCore/ads/createAds")
    BaseResponse createAds(@RequestBody CreateAdsParam req);

    @PostMapping(value = "/dataCore/ads/updateAds")
    BaseResponse updateAds(@RequestBody UpdateAdsParam req);

    @GetMapping(value = "/dataCore/ads/abortAds")
    BaseResponse abortAds(@RequestParam(value = "id") Long id);

    @GetMapping(value = "/dataCore/ads/activeAds")
    BaseResponse activeAds(@RequestParam(value = "id") Long id);

    @GetMapping(value = "/dataCore/ads/getAdsDetail")
    ObjectResponse<AdsVo> getAdsDetail(@RequestParam(value = "id") Long id);

    @PostMapping(value = "/dataCore/ads/listAds")
    ListResponse<AdsVo> listAds(@RequestBody ListAdsParam req);

    @PostMapping("/dataCore/meterData/list")
    ListResponse<MeterDataVo> getMeterDataList(@RequestBody MeterDataListParam param);
}
