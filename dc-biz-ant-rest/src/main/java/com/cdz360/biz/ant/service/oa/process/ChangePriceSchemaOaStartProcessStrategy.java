package com.cdz360.biz.ant.service.oa.process;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.feign.reactor.DataCoreSmartPriceFeignClient;
import com.cdz360.biz.model.oa.constant.OaConstants;
import com.cdz360.biz.model.site.type.SitePriceSmartStrategyType;
import com.cdz360.biz.model.site.type.SiteStatus;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import com.cdz360.biz.oa.dto.ChangePriceSchemaFormDto;
import com.cdz360.biz.oa.dto.SmartStrategyElecPrice;
import com.cdz360.biz.oa.dto.TargetPriceSchemeInfo;
import com.cdz360.biz.oa.param.OaStartProcessParam;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.framework.common.feign.SiteDataCoreFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class ChangePriceSchemaOaStartProcessStrategy extends AbstractStartProcessStrategy {

    @Autowired
    private SiteDataCoreFeignClient siteDataCoreFeignClient;

    @Autowired
    private DataCoreSmartPriceFeignClient dataCoreSmartPriceFeignClient;

    @Autowired
    private RedisIotReadService redisIotReadService;

    private static final String PRO_DEF_KEY = OaConstants.PD_KEY_CHARGE_FEE;

    @PostConstruct
    public void init() {
        this.startProcessStrategyFactory.addStrategy(PRO_DEF_KEY, this);
    }

    @Override
    public Mono<Map<String, Object>> paramConversion(Map<String, Object> req) {
        return null;
    }

    @Override
    public Mono<OaStartProcessParam> validate(OaStartProcessParam param) {
        // 转换成对应的数据结构
        IotAssert.isNotNull(param.getData(), "表单数据不能为空");
        ChangePriceSchemaFormDto form = JsonUtils.fromJson(
            JsonUtils.toJsonString(param.getData()), ChangePriceSchemaFormDto.class);
        IotAssert.isNotNull(form, "表单数据不能为空");

        // 结算单信息验证
        IotAssert.isTrue(List.of("EVSE", "SITE", "SITE_PART").contains(form.getTarget()),
            "操作对象类型无效");

        Boolean isTwice = false;
        if (Objects.nonNull(form.getRound()) && form.getRound().equals(2)) {
            isTwice = true;
        }
        if (List.of("EVSE", "SITE").contains(form.getTarget())) {
            if (!form.isSmartPrice()) {
                IotAssert.isNotNull(form.getPriceSchemeId(), "请选择电价模板");
            } else {
                // 首轮智能电价校验
//                IotAssert.isNotNull(form.getElecPrice(), "请输入首轮新电价");
//                IotAssert.isNotNull(form.getSmartStrategy(), "请选择首轮智能策略");
                checkSmartStrategyAndElecPrice(form.getSmartStrategy(), form.getElecPrice(),
                    form.getTimeBasedElecPrice(),
                    true);
            }
            if (!form.isSmartPrice2()) {
                if (isTwice) {
                    IotAssert.isNotNull(form.getPriceSchemeId2(), "请选择次轮电价模板");
                }
            } else {
                // 次轮智能电价校验
//                IotAssert.isNotNull(form.getElecPrice2(), "请输入次轮新电价");
//                IotAssert.isNotNull(form.getSmartStrategy2(), "请选择次轮智能策略");
                checkSmartStrategyAndElecPrice(form.getSmartStrategy2(), form.getElecPrice2(),
                    form.getTimeBasedElecPrice2(),
                    false);
            }
        }
        if ("SITE_PART".equals(form.getTarget())) {
            if (!form.isSmartPrice()) {
                IotAssert.isTrue(form.getDcPriceSchemeId() != null
                    && form.getAcPriceSchemeId() != null, "请选择电价模板");
            } else {
                // 首轮智能电价校验
//                IotAssert.isNotNull(form.getElecPrice(), "请输入首轮新电价");
//                IotAssert.isNotNull(form.getSmartStrategy(), "请选择首轮智能策略");
                checkSmartStrategyAndElecPrice(form.getSmartStrategy(), form.getElecPrice(),
                    form.getTimeBasedElecPrice(),
                    true);
            }
            if (!form.isSmartPrice2()) {
                if (isTwice) {
                    IotAssert.isTrue(form.getDcPriceSchemeId2() != null
                        && form.getAcPriceSchemeId2() != null, "请选择次轮电价模板");
                }
            } else {
                // 次轮智能电价校验
//                IotAssert.isNotNull(form.getElecPrice2(), "请输入次轮新电价");
//                IotAssert.isNotNull(form.getSmartStrategy2(), "请选择次轮智能策略");
                checkSmartStrategyAndElecPrice(form.getSmartStrategy2(), form.getElecPrice2(),
                    form.getTimeBasedElecPrice2(),
                    false);
            }
        }
//        IotAssert.isNotNull(form.getPriceSchemeId(), "请选择电价模板");

        IotAssert.isTrue(
            List.of("IN_TIME", "SPECIAL_DATE", "SPECIAL_TIME").contains(form.getExeTime()),
            "下发时间类型无效");
        if (isTwice) {
            IotAssert.isTrue(
                List.of("IN_TIME", "SPECIAL_DATE", "SPECIAL_TIME").contains(form.getExeTime2()),
                "次轮下发时间类型无效");
        }
        IotAssert.isTrue(CollectionUtils.isNotEmpty(form.getSiteIdList()), "请选择目标场站");

        if (!"IN_TIME".equals(form.getExeTime())) {
            IotAssert.isNotBlank(form.getExeDate(), "请指定下发具体时间");
        }

        if ("EVSE".equals(form.getTarget())) {
            IotAssert.isTrue(CollectionUtils.isNotEmpty(form.getEvseNoList()), "请选择目标桩");

            List<EvseVo> evseList = redisIotReadService.getEvseList(form.getEvseNoList());
            IotAssert.isTrue(form.getEvseNoList().size() == evseList.size(),
                "缓存中无法获取对应桩信息");
            if (CollectionUtils.isNotEmpty(evseList)) {
                form.setTargetInfoList(evseList.stream().map(
                        x -> new TargetPriceSchemeInfo().setNo(x.getEvseNo()).setName(x.getName())
                            .setPriceSchemeId(x.getPriceCode()))
                    .collect(Collectors.toList()));
            }


        } else { // == SITE
//            ListSiteParam baseListParam = new ListSiteParam()
//                .setStatusList(
//                    List.of(SiteStatus.ONLINE, SiteStatus.OPENING, SiteStatus.UNAVAILABLE))
//                .setSiteIdList(form.getSiteIdList());
//            baseListParam.setSize(form.getSiteIdList().size())
//                .setTotal(false);
//            ListResponse<SiteVo> res = siteDataCoreFeignClient.getSiteVoList(baseListParam);
//            FeignResponseValidate.check(res);
//            IotAssert.isTrue(form.getSiteIdList().size() == res.getData().size(),
//                "提交存在无效场站ID");
//            if (CollectionUtils.isNotEmpty(res.getData())) {
//                form.setTargetInfoList(res.getData().stream().map(
//                        x -> new TargetPriceSchemeInfo().setNo(x.getId()).setName(x.getSiteName())
//                            .setPriceSchemeId(x.getTemplateId()))
//                    .collect(Collectors.toList()));
//            }
            ListSiteParam baseListParam = new ListSiteParam()
                .setStatusList(
                    List.of(SiteStatus.ONLINE, SiteStatus.OPENING, SiteStatus.UNAVAILABLE))
                .setSiteIdList(form.getSiteIdList());
            baseListParam.setSize(form.getSiteIdList().size())
                .setTotal(false);
            ListResponse<SiteVo> res = siteDataCoreFeignClient.getSiteVoList(baseListParam);
            FeignResponseValidate.check(res);
            if (CollectionUtils.isNotEmpty(res.getData())) {
                List<TargetPriceSchemeInfo> targetInfoList = res.getData().stream()
                    .filter(x -> CollectionUtils.isNotEmpty(x.getTemplateList()))
                    .flatMap(x -> x.getTemplateList().stream()
                        .map(v -> new TargetPriceSchemeInfo()
                            .setNo(x.getId())
                            .setName(x.getSiteName())
                            .setPriceSchemeId(v.getTemplateId())
                            .setTemplateType(v.getTemplateType())))
                    .collect(Collectors.toList());
                form.setTargetInfoList(targetInfoList);
                if (isTwice) {
                    form.setTargetInfoList2(targetInfoList);
                }
            }
        }

        form.setOpId(param.getOpId())
            .setOpName(param.getOpName());

        // 加上校验
        OaStartProcessParam data = param.setData(form.toMap());
        String validateRes = "success";
        try {
            validateRes = dataCoreSmartPriceFeignClient.validateGenerateSmartPrice(data).block()
                .getData();
        } catch (Exception e) {
            log.error("智能调价参数校验方法失败, {}", e);
            throw new DcServiceException("智能调价参数校验方法失败");
        }
        if (!"success".equals(validateRes)) {
            log.error("智能调价参数校验失败 {}", validateRes);
            throw new DcServiceException(validateRes);
        }
        return Mono.just(data); // 暂时不调整写入数据结构
//        return Mono.just(param.setData(new HashMap<String, Object>() {{
//            put(OaConstants.PI_VARIABLE_FORM_VARIABLES, form);
//        }}));
    }

    /**
     * 校验智能策略和新电价
     * @param smartStrategy
     * @param elecPrice
     * @param timeBaseElecPrice 分时电价
     * @param firstFlag
     */
    private void checkSmartStrategyAndElecPrice(String smartStrategy, String elecPrice,
        String timeBaseElecPrice, Boolean firstFlag) {
        String msg = Boolean.TRUE.equals(firstFlag) ? "首轮" : "次轮";
        // 请选择首轮智能策略
        // 或 请选择次轮智能策略
        IotAssert.isNotNull(smartStrategy, "请选择" + msg + "智能策略");
        SitePriceSmartStrategyType smartStrategyType = SitePriceSmartStrategyType.getSmartStrategyByCode(
            smartStrategy);
        IotAssert.isNotNull(smartStrategyType, "请选择" + msg + "智能策略");

        // 请输入首轮新电价
        // 或 请输入次轮新电价
        List<SitePriceSmartStrategyType> elecCheckList = List.of(
            SitePriceSmartStrategyType.FIX_ELEC_FEE, SitePriceSmartStrategyType.FIX_SERV_FEE,
            SitePriceSmartStrategyType.FIX_TOTAL_FEE);
        if (elecCheckList.contains(smartStrategyType)) {
            IotAssert.isNotNull(elecPrice, "请输入" + msg + "新电价");
        }

        List<SitePriceSmartStrategyType> timeElecCheckList = List.of(
            SitePriceSmartStrategyType.FIX_ELEC_FEE_TIME_BASED_PRICE,
            SitePriceSmartStrategyType.FIX_SERV_FEE_TIME_BASED_PRICE,
            SitePriceSmartStrategyType.FIX_TOTAL_FEE_TIME_BASED_PRICE);
        if (timeElecCheckList.contains(smartStrategyType)) {
            IotAssert.isNotNull(timeBaseElecPrice, "请输入" + msg + "新电价");
            SmartStrategyElecPrice smartStrategyElecPrice = JsonUtils.fromJson(timeBaseElecPrice,
                SmartStrategyElecPrice.class);
            IotAssert.isNotNull(smartStrategyElecPrice, "请输入" + msg + "新电价");
            // 请输入首轮尖时新电价
            // 或 请输入次轮尖时新电价
            IotAssert.isNotNull(smartStrategyElecPrice.getSharpPeakElecPrice(),
                "请输入" + msg + "尖时新电价");
            // 请输入首轮峰时新电价
            // 或 请输入次轮峰时新电价
            IotAssert.isNotNull(smartStrategyElecPrice.getPeakElecPrice(),
                "请输入" + msg + "峰时新电价");
            // 请输入首轮平时新电价
            // 或 请输入次轮平时新电价
            IotAssert.isNotNull(smartStrategyElecPrice.getOffPeakElecPrice(),
                "请输入" + msg + "平时新电价");
            // 请输入首轮谷时新电价
            // 或 请输入次轮谷时新电价
            IotAssert.isNotNull(smartStrategyElecPrice.getValleyElecPrice(),
                "请输入" + msg + "谷时新电价");
        }

    }
}
