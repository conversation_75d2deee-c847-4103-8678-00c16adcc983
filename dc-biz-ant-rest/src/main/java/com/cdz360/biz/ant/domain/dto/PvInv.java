package com.cdz360.biz.ant.domain.dto;

import com.cdz360.biz.ant.domain.dto.type.PvInvWorkMode;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 储能设备  -- 光伏逆变器
 */
@Data
@Accessors(chain = true)
public class PvInv {

    // 机器型号
    @JsonProperty("mm")
    private Integer machineModel;
    // 有功调节量
    @JsonProperty("ar")
    private Integer activeRegulator;
    // V L1相电压
    @JsonProperty("v1")
    private BigDecimal voltageL1;
    // V L2相电压
    @JsonProperty("v2")
    private BigDecimal voltageL2;
    // V L3相电压
    @JsonProperty("v3")
    private BigDecimal voltageL3;
    // A L1相电流
    @JsonProperty("c1")
    private BigDecimal currentL1;
    // A L2相电流
    @JsonProperty("c2")
    private BigDecimal currentL2;
    // A L3相电流
    @JsonProperty("c3")
    private BigDecimal currentL3;
    // Hz L1相频率
    @JsonProperty("f1")
    private BigDecimal frequencyL1;
    // Hz L2相频率
    @JsonProperty("f2")
    private BigDecimal frequencyL2;
    // Hz L3相频率
    @JsonProperty("f3")
    private BigDecimal frequencyL3;
    // W 发电功率
    @JsonProperty("fp")
    private Long feedingPower;
    // 工作模式
    @JsonProperty("wm")
    private PvInvWorkMode workMode;
    // degree C 逆变器内部温度
    @JsonProperty("it")
    private BigDecimal internalTemp;
    // 故障信息
    @JsonProperty("fil")
    private List<Integer> faultInfoList;
//    // 故障信息预留1
//    @JsonProperty("fir1")
//    private Integer faultInfoReserve1;
    // kWh 总发电量
    @JsonProperty("ak")
    private BigDecimal totalKwh;
    // 软件版本
    @JsonProperty("fv")
    private Integer firmwareVersion;
    // 硬件版本
    @JsonProperty("hv")
    private Integer hardwareVersion;
    // V PBUS电压
    @JsonProperty("pv")
    private BigDecimal PbusVoltage;
    // V NBUS电压
    @JsonProperty("nv")
    private BigDecimal NbusVoltage;
    // mA GFCI值
    @JsonProperty("gv")
    private Integer GfciValue;
    // kWh 当天发电量
    @JsonProperty("tk")
    private BigDecimal todayKwh;
}
