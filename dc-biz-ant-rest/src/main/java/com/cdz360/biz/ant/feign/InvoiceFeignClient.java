package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.domain.request.InvoicedModelSearchParam;
import com.cdz360.biz.ant.domain.vo.DescVo;
import com.cdz360.biz.ant.domain.vo.DzCommonVo;
import com.cdz360.biz.ant.domain.vo.IdListVo;
import com.cdz360.biz.ant.domain.vo.InvoicedChargerOrderVo;
import com.cdz360.biz.ant.domain.vo.InvoicedConfigMegVo;
import com.cdz360.biz.ant.domain.vo.InvoicedRecordChargerOrderSaveVo;
import com.cdz360.biz.ant.domain.vo.InvoicedRecordUpdateStatusVo;
import com.cdz360.biz.ant.domain.vo.InvoicedSiteValidYNVo;
import com.cdz360.biz.ant.domain.vo.InvoicedUserAutoAmountVo;
import com.cdz360.biz.model.oa.vo.OaInvoicedVo;
import com.chargerlinkcar.framework.common.domain.invoice.InvoicedRecordVo;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoicedModelDTO;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoicedTemplateSalDTO;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoicedTemplateSalDetailDTO;
import com.chargerlinkcar.framework.common.domain.invoice.dto.UpdateIdDTO;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceInfoParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.ListInvoiceSphTemplateParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.ListInvoicedRecordParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.ListInvoicedTempSalParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceInfoVo;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedModelVo;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedSalTempRefVo;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedTemplateSalDetailVo;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedTemplateSalVo;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @since 2019/7/3 11:19
 * <AUTHOR>
 */
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_INVOICE, fallbackFactory = InvoiceFeignClientHystrixFactory.class)
public interface InvoiceFeignClient {

    @GetMapping("/api/invoice/getInvoicedTempSal")
    ObjectResponse<InvoicedTemplateSalVo> getInvoicedTempSal(
        @RequestParam(value = "commId") Long commId,
        @RequestParam(value = "saleTin") String saleTin);

    @GetMapping("/api/invoice/getInvoicedTempSalById")
    ObjectResponse<InvoicedTemplateSalVo> getInvoicedTempSalById(
        @RequestParam(value = "id") Long id);

    @PostMapping(value = "/api/invoice/getCorpInvoiceDetail")
    ObjectResponse<CorpInvoiceInfoVo> getCorpInvoiceDetail(@RequestBody CorpInvoiceInfoParam param);

    @GetMapping(value = "/api/invoice/getCorpInvoiceModel")
    ObjectResponse<InvoicedModelDTO> getCorpInvoiceModel(@RequestParam("userId") Long uid);

    @RequestMapping(value = "/api/invoiced-temp-sal-dtlV4/getInvoicedProductDesc", method = RequestMethod.GET)
    ObjectResponse<DescVo> getInvoicedProductDesc(
        @RequestParam(value = "commercialId") Long commercialId);

    @Deprecated
    @PostMapping(value = "/api/invoiced-sitesV4/invoicedValid")
    ObjectResponse<DzCommonVo> saveInvoicedValid(@RequestBody InvoicedSiteValidYNVo vo);

//    @Deprecated(since = "20201221")
//    @RequestMapping(value = "/api/invoiced-temp-sal-dtlV4/byCode", method = RequestMethod.GET)
//    ObjectResponse<InvoicedTemplateSalDetailVo> getInvoicedTemplateSalDetailByCode(@RequestParam(value = "code") String code);

    @GetMapping(value = "/api/invoiced-temp-sal-dtlV4/byInvoiceType")
    ListResponse<InvoicedTemplateSalDetailVo> getInvoicedTemplateSalDetailsByInvoiceType(
        @RequestParam(value = "invoiceType") String invoiceType,
        @RequestParam(value = "commercialId") Long commercialId,
        @RequestParam(value = "page") int page,
        @RequestParam(value = "size") int size);

//    @Deprecated(since = "20201222")
//    @RequestMapping(value = "/api/invoiced-temp-sal-dtlV4/save", method = RequestMethod.POST)
//    ObjectResponse<InvoicedTemplateSalDetailVo> saveInvoicedTemplateSalDetailVo(@RequestBody InvoicedTemplateSalDetailVo invoicedTemplateSalDetailDTO);//需要商户id

    @GetMapping(value = "/api/invoiced-usersV4/getInvoicedUserAutoAmount")
    ObjectResponse<InvoicedUserAutoAmountVo> getInvoicedUserAutoAmountVo(
        @RequestParam(value = "userId") Long userId,
        @RequestParam("commId") Long commId);

    @RequestMapping(value = "/api/invoiced-usersV4/saveInvoicedUserAutoAmount", method = RequestMethod.POST)
    BaseResponse saveInvoicedUserAutoAmount(
        @RequestBody InvoicedUserAutoAmountVo invoicedUserAutoAmountVo);

    @RequestMapping(value = "/api/invoiced-recordsV4/signHadExportToInvoice", method = RequestMethod.POST)
    ObjectResponse<InvoicedRecordVo> signHadExportToInvoice(
        @RequestBody InvoicedRecordChargerOrderSaveVo invoicedRecordChargerOrderSaveVo,
        @RequestParam(value = "token") String token);

    @RequestMapping(value = "/api/invoiced-recordsV4/signForbiddenExportToInvoice", method = RequestMethod.POST)
    BaseResponse signForbiddenExportToInvoice(
        @RequestBody List<String> orderNoList, @RequestParam(value = "token") String token);

    @PostMapping(value = "/api/invoiced-recordsV4")
    ListResponse<InvoicedRecordVo> getAllInvoicedRecords(
        @RequestBody ListInvoicedRecordParam param);

    @GetMapping(value = "/api/invoiced-records-userV4")
    ListResponse<InvoicedRecordVo> getAllInvoicedRecordsByUserId(
        @RequestParam(value = "userId") Long userId,
        @RequestParam(value = "page") int page,
        @RequestParam(value = "size") int size);

    @RequestMapping(value = "/api/invoiced-recordsV4/getInvoicedRecordsByUserId", method = RequestMethod.GET)
    ListResponse<InvoicedRecordVo> getAllInvoicedRecordsByUserIdAndStatus(
        @RequestParam(value = "userId") Long userId,
        @RequestParam(value = "invoicedStatus") String invoicedStatus,
        @RequestParam(value = "page") int page,
        @RequestParam(value = "size") int size);

    @RequestMapping(value = "/api/invoiced-recordsV4/changeStatus", method = RequestMethod.POST)
    ObjectResponse<InvoicedRecordVo> changeStatus(@RequestBody InvoicedRecordUpdateStatusVo dto);

    @PostMapping(value = "/api/invoiced-recordsV4/changeSomeStatus")
    ListResponse<InvoicedRecordVo> changeSomeStatus(@RequestBody IdListVo idList);

    @RequestMapping(value = "/api/invoiced-recordsV4/exportToInvoice", method = RequestMethod.POST)
    ListResponse<InvoicedRecordVo> exportToInvoice(@RequestBody IdListVo idList,
        @RequestParam(value = "token") String token);

    @RequestMapping(value = "/api/invoiced-recordsV4/getPdfUrl", method = RequestMethod.GET)
    ObjectResponse<Map<String, String>> getPdfUrl(@RequestParam(value = "id") Long id);

    @RequestMapping(value = "/api/invoiced-recordsV4/trackingNumber", method = RequestMethod.POST)
    ObjectResponse<String> createTrackingNumber(@RequestBody IdListVo idList,
        @RequestParam(value = "token") String token);

    // 开票流程创建前的开票主体与商品行模板检查
    @GetMapping(value = "/api/invoiced-records/templateSalCheckBeforeInvoicingProcess")
    ObjectResponse<OaInvoicedVo> templateSalCheckBeforeInvoicingProcess(
        @RequestParam("modelId") Long modelId, @RequestParam("tempSalId") Long tempSalId,
        @RequestParam("productTempId") Long productTempId);

    @PostMapping(value = "/api/invoiced-configsV4/saveConfig")
    ObjectResponse<InvoicedConfigMegVo> saveMegConfig(
        @RequestBody InvoicedConfigMegVo invoicedConfigMegVo);

    @GetMapping(value = "/api/invoiced-configsV4/getInvoicedMegConfig")
    ObjectResponse<InvoicedConfigMegVo> getInvoicedMegConfig(
        @RequestParam(value = "token") String token);

    @RequestMapping(value = "/api/invoiced-charger-ordersV4", method = RequestMethod.GET)
    ListResponse<InvoicedChargerOrderVo> getAllInvoicedChargerOrders(
        @RequestParam(value = "customerId") Long customerId,
        @RequestParam(value = "status") Integer status,
        @RequestParam(value = "invoicedFlag") Boolean invoicedFlag,//是否已经关联了开票申请
        @RequestParam(value = "keyword") String keyword,
        @RequestParam(value = "startDate") String startDate,
        @RequestParam(value = "endDate") String endDate,
        @RequestParam(value = "token") String token,
        @RequestParam(value = "page") int page,
        @RequestParam(value = "size") int size);

//    @Deprecated
//    @GetMapping(value = "/api/invoiced-charger-ordersV4/byInvoicedId")
//    ListResponse<InvoicedChargerOrderVo> getAllInvoicedChargerOrders(
//            @RequestParam(value = "invoicedId") Long invoicedId,
//            @RequestParam(value = "page") int page,
//            @RequestParam(value = "size") int size);

    @RequestMapping(value = "/api/invoiced-charger-orders2cV4", method = RequestMethod.GET)
    ListResponse<InvoicedChargerOrderVo> getAllInvoicedChargerOrders2c(
        @RequestParam(value = "customerId") Long customerId,
        @RequestParam(value = "status") Integer status,
        @RequestParam(value = "invoicedFlag") Boolean invoicedFlag,//是否已经关联了开票申请
        @RequestParam(value = "keyword") String keyword,
        @RequestParam(value = "token") String token,
        @RequestParam(value = "page") int page,
        @RequestParam(value = "size") int size);

    @RequestMapping(value = "/api/invoiced-modelsV4/getDefaultInvoicedModel", method = RequestMethod.GET)
    ObjectResponse<InvoicedModelVo> getDefaultInvoicedModel(
        @RequestParam(value = "userId") Long userId);

    /**
     * 获取可见的开票主体列表
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/invoice/getInvoicedTempSalList")
    ListResponse<InvoicedTemplateSalVo> getInvoicedTempSalList(
        @RequestBody ListInvoicedTempSalParam param);

    /**
     * 获取指定商户可见的开票主体列表
     *
     * @param commId
     * @return
     */
    @GetMapping(value = "/api/invoice/getInvoicedTempSalListByCommId")
    ListResponse<InvoicedTemplateSalVo> getInvoicedTempSalListByCommId(
            @RequestParam(value = "commId") String commId);

    /**
     * 更新商户开票主体信息
     *
     * @param vo
     * @return
     */
    @PostMapping(value = "/api/invoice/addInvoicedTempSal")
    ObjectResponse<InvoicedTemplateSalDTO> addInvoicedTempSal(
        @RequestBody InvoicedTemplateSalVo vo);

    /**
     * 更新商户开票主体信息
     *
     * @param vo
     * @return
     */
    @PostMapping(value = "/api/invoice/updateInvoicedTempSal")
    ListResponse<UpdateIdDTO> updateInvoicedTempSal(@RequestBody InvoicedTemplateSalVo vo);

    /**
     * 删除开票主体
     *
     * @param commId
     * @param saleTin
     * @return
     */
    @PostMapping(value = "/api/invoice/disableInvoicedTempSal")
    ObjectResponse<InvoicedTemplateSalDTO> disableInvoicedTempSal(
        @RequestParam(value = "commId") Long commId,
        @RequestParam(value = "saleTin") String saleTin);

    /**
     * 更新企业客户开票模板信息
     *
     * @param modelDTO
     * @return
     */
    @PostMapping(value = "/api/invoice/updateCorpInvoiceModel")
    ObjectResponse<UpdateIdDTO> updateCorpInvoiceModel(@RequestBody InvoicedModelDTO modelDTO);

    /**
     * 更新个人、会员开票模板信息
     *
     * @param modelDTO
     * @return
     */
    @PostMapping(value = "/api/invoice/updateUserInvoiceModel")
    ObjectResponse<UpdateIdDTO> updateUserInvoiceModel(@RequestBody InvoicedModelDTO modelDTO);

    /**
     * 获取开票信息列表熔断
     * @param userId
     * @param page
     * @param size
     * @return
     */
//    @RequestMapping(value = "/api/invoiced-modelsV4/getInvoicedModelsByUserIdAndType", method = RequestMethod.GET)
//    ListResponse<InvoicedModelVo> getInvoicedModelsByUserIdAndType(@RequestParam(value = "userId") Long userId,
//        @RequestParam(value = "invoiceType") InvoiceType invoiceType,
//        @RequestParam(value = "page") int page,
//        @RequestParam(value = "size") int size);

    /**
     * 获取开票信息列表熔断
     *
     * @param param
     * @return
     */
    @RequestMapping(value = "/api/invoiced-modelsV4/getInvoicedModelsByUserIdAndTypeSorted", method = RequestMethod.POST)
    ListResponse<InvoicedModelVo> getInvoicedModelsByUserIdAndTypeSorted(
        @RequestBody InvoicedModelSearchParam param);

    // 获取可见的商品行模板列表
    @PostMapping(value = "/api/invoice/sph/template/findAll")
    ListResponse<InvoicedSalTempRefVo> findAllInvoiceSphTemplate(
        @RequestBody ListInvoiceSphTemplateParam param);

    // 通过商品行模板ID获取商品行配置信息
    @GetMapping("/api/invoice/sph/detail/getByTempRefId")
    ListResponse<InvoicedTemplateSalDetailDTO> getInvoiceSphDetail(
        @RequestParam(value = "tempRefId") Long tempRefId);
}
