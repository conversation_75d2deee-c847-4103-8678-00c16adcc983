package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.domain.request.RefundOrderParam;
import com.cdz360.biz.ant.feign.AntUserFeignClient;
import com.cdz360.biz.model.cus.wallet.param.ListRefundOrderParam;
import com.cdz360.biz.model.cus.wallet.po.RefundOrderPo;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * WalletServiceImpl
 *  TODO
 * @since 2019/11/4 14:26
 * <AUTHOR>
 */
@Slf4j
@Service
public class WalletService //implements IWalletService
{

    @Autowired
    private AntUserFeignClient userFeignClient;

    /**
     * 获取提现信息列表
     *
     * @param param
     * @return
     */

    public ListResponse<RefundOrderPo> listRefundOrder(RefundOrderParam param) {
        log.info("[提现信息查询] 请求参数param：{} ", JsonUtils.toJsonString(param));
        ListRefundOrderParam listRefundOrderParam = new ListRefundOrderParam();
        BeanUtils.copyProperties(param, listRefundOrderParam);
        if (param.getCreateTimeFrom() != null) {
            listRefundOrderParam.setCreateTimeFrom(DateUtil.getTimestamp2Date(param.getCreateTimeFrom()));
        }
        if (param.getCreateTimeTo() != null) {
            listRefundOrderParam.setCreateTimeTo(DateUtil.getTimestamp2Date(param.getCreateTimeTo()));
        }
        if (param.getFinishTimeFrom() != null) {
            listRefundOrderParam.setFinishTimeFrom(DateUtil.getTimestamp2Date(param.getFinishTimeFrom()));
        }
        if (param.getFinishTimeTo() != null) {
            listRefundOrderParam.setFinishTimeTo(DateUtil.getTimestamp2Date(param.getCreateTimeTo()));
        }
        ListResponse<RefundOrderPo> resList = userFeignClient.listRefundOrder(listRefundOrderParam);
        return resList;
    }

}
