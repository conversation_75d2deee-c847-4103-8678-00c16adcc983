package com.cdz360.biz.ant.service.camera;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.reactor.ReactorSiteDataCoreFeignClient;
import com.cdz360.biz.model.geo.po.DistrictPo;
import com.cdz360.biz.model.trading.camera.dto.CameraSiteDto;
import com.cdz360.biz.model.trading.camera.param.ListCameraParam;
import com.cdz360.biz.utils.feign.camera.CameraFeignClient;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class CameraBizService {

    @Autowired
    private CameraFeignClient cameraFeignClient;

    @Autowired
    private ReactorSiteDataCoreFeignClient siteDataCoreFeignClient;

    /**
     * 获取含摄像头的场站列表
     * @param param
     * @return
     */
    public Mono<List<CameraSiteDto>> getCameraSiteList(
        ListCameraParam param) {
        return cameraFeignClient.getCameraSiteList(param)
            .flatMap(res -> this.getCameraSiteCities(res.getData()));
    }

    /**
     * 从 dataCore 获取区县的详细信息，并且把省份、城市、区县信息填到场站里
     */
    private Mono<List<CameraSiteDto>> getCameraSiteCities(List<CameraSiteDto> siteListIn) {
        if (CollectionUtils.isEmpty(siteListIn)) {
            return Mono.empty();
        }
        List<String> districtCodes = siteListIn.stream()
            .map(CameraSiteDto::getSiteDistrictCode)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(districtCodes)) {
            return Mono.empty();
        }
        return siteDataCoreFeignClient.getDistrictByList(
                districtCodes)         // 从 dataCore 获取区县的详细信息
            .map(distRes -> this.fillSiteGeoInfo(siteListIn,
                distRes.getData()));   //把省份、城市、区县信息填到场站列表 siteListIn 里
    }

    /**
     * 把省份、城市、区县信息填到场站列表 siteListIn 里
     */
    private List<CameraSiteDto> fillSiteGeoInfo(List<CameraSiteDto> siteListIn,
        List<DistrictPo> distList) {
        if (CollectionUtils.isEmpty(siteListIn)
            || CollectionUtils.isEmpty(distList)) {
            return siteListIn;
        }
        Map<String, DistrictPo> distMap = distList.stream()
            .collect(Collectors.toMap(DistrictPo::getCode, o -> o));
        siteListIn.stream()
            .filter(s -> StringUtils.isNotBlank(s.getSiteDistrictCode()))
            .forEach(s -> {
                DistrictPo dist = distMap.get(s.getSiteDistrictCode());
                if (dist != null) {
                    s.setProvince(dist.getProvinceName())
                        .setCity(dist.getCityName())
                        .setDistrict(dist.getName());
                }
            });
        return siteListIn;
    }
}
