package com.cdz360.biz.ant.domain.vo;


import com.cdz360.biz.model.cus.siteAuthCard.vo.SiteAuthCardVo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;


/**
 * 卡片列表查询卡片详情
 * <AUTHOR>
 */
@Data
@Schema(description = "在线卡信息")
public class CardListdetailVO implements Serializable{

    @Schema(description = "在线卡Id")
    private Long cardId;
	/**
     * 逻辑卡号
	 */
    @Schema(description = "逻辑卡号")
    private String cardNo;
	// 物理卡号
    @Schema(description = "物理卡号")
    private String cardChipNo;
	// 卡片名称
    @Schema(description = "卡片名称")
    private String cardName;
	/**
	 * 卡片激活日期
	 */
    @Schema(description = "卡片激活日期")
    private Date activationDate;

    @Schema(description = "--")
    private String activationCode;
	/**
	 * CardStatus
	 * 卡状态：10000未激活，10001已激活，10002卡锁定(已挂失)，10005已失效(黑名单)，10006已过期
	 */
    @Schema(description = "卡状态：10000未激活，10001已激活，10002卡锁定(已挂失)，10005已失效(黑名单)，10006已过期")
    private String cardStatus;
	/**
	 * group by card_no时订单总计
	 */
    @Schema(description = "group by card_no时订单总计")
    private int orderNum;

    @Schema(description = "商户会员手机号")
    private String mobile;

    @Schema(description = "企业客户手机号")
    private String corpPhone;

	/**
	 * 添加时间
	 */
    @Schema(description = "添加时间")
    private Date cardCreateDate;
	/**
	 * 用户Id
	 */
    @Schema(description = "用户Id", example = "123")
    private Long userId;
    /**
     * 用户名称
     */
    @Schema(description = "用户名称")
    private String userName;

    @Schema(description = "企业ID")
    private Long corpId;
    @Schema(description = "企业名称")
    private String corpName;

    @Schema(description = "组织ID")
    private Long corpOrgId;

    @Schema(description = "组织名称")
    private String corpOrgName;

    /**
     * 商户Id
     */
    @Schema(description = "商户Id")
    private Long commId;
    /**
	 * 商户名称
	 */
    @Schema(description = "商户名称")
    private String commName;
	/**
	 * 卡片面值
	 */
    @Schema(description = "卡片面值")
    private long cardDenomination;

    @Deprecated
    @Schema(description = "鉴权卡所属站点（多个站点用‘，’分割）")
    private String stations;
    /**
     * 指定场站数量
     */
    @Schema(description = "指定场站数量")
    private Integer usableStationCount;

    @Schema(description = "场站名称")
    private String stationName;

    @Schema(description = "场站列表")
    private List<String> stationList;

    @Schema(description = "--")
    private Integer stationNum;

    @Schema(description = "--")
    private String[] stationArr;

    @Schema(description = "--")
    private String beginTime;

    @Schema(description = "--")
    private String endTime;

    @Schema(description = "卡片类型: 0-在线卡; 1-紧急冲电卡; 2-待定")
    private Integer cardType;

    @Schema(description = "卡片面值")
    private Double cardDnomination;

    @Schema(description = "--")
    private Integer vehicleCount;

    @Schema(description = "从数据库中获取: 0:其他,1:套餐卡, 2:月卡,3:离线卡;4-鉴权卡")
    private Integer isPackage;
	/**
	 * 紧急充电卡集团客户id
	 */
//	@Deprecated
//    @Schema(description = "紧急充电卡集团客户id")
//    private Long merchantId;

	/**
	 * 紧急充电卡集团客户名称
	 */
//	@Deprecated
//    @Schema(description = "紧急充电卡集团客户名称")
//    private String merchantName;
	/**
	 * 紧急卡已成功下发的桩数
	 */
    @Schema(description = "紧急卡已成功下发的桩数")
    private Integer sendBoxSuccess;
	/**
	 * 紧急卡配置充电站点充电桩总数
	 */
    @Schema(description = "紧急卡配置充电站点充电桩总数")
    private Integer	sendBoxTotal;
	/**
	 * 备注
	 */
    @Schema(description = "备注")
    private String remark;
	/**
	 * 卡密
	 */
    @Schema(description = "卡密")
    private String cardActivationCode;


    /**
     * 车牌号
     */
    @Schema(description = "车牌号")
    private String carNo;

    /**
     * 车辆自编号
     */
    @Schema(description = "车辆自编号")
    private String carNum;

    /**
     * 线路
     */
    @Schema(description = "线路")
    private String lineNum;

    /**
     * 车队名称
     */
    @Schema(description = "车队名称")
    private String carDepart;

    /**
     * 扣款账户名称
     */
    @Schema(description = "扣款账户名称")
    private String debitAccountName;

    /**
     * 能否编辑
     */
    @Schema(description = "能否编辑")
    private Boolean isEdit;

    @Schema(description = "兼容充值卡，否0，是1")
    private Integer deposit;

    @Schema(description = "CARD本地鉴权场站数")
    private Integer localAuthSiteAmount;

    @Schema(description = "是否开启本地鉴权")
    private Boolean isLocalAuth;

    @Schema(description = "CARD本地鉴权场站数")
    private List<SiteAuthCardVo> localAuthSiteList;
}
