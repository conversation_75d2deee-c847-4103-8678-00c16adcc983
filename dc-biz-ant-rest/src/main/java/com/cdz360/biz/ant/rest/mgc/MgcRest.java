package com.cdz360.biz.ant.rest.mgc;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.ant.domain.bundle.EvseBundleDto;
import com.cdz360.biz.ant.domain.bundle.ListUpgradeLogParam;
import com.cdz360.biz.ant.domain.bundle.UpgradeParam;
import com.cdz360.biz.ant.feign.reactor.DeviceBundleFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorIotWorkerFeignClient;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "微网控制器相关接口", description = "微网控制器接口")
@Slf4j
@RestController
public class MgcRest {

    @Autowired
    private ReactorIotWorkerFeignClient workerFeignClient;

    @Operation(summary = "微网控制器升级")
    @PostMapping(value = "/api/mgc/upgrade")
    public Mono<BaseResponse> mgcUpgrade(
            ServerHttpRequest request,
            @RequestBody UpgradeParam param) {
        log.info("微网控制器升级: {}, param = {}", LoggerHelper2.formatEnterLog(request, false), param);
        return workerFeignClient.mgcUpgrade(param);
    }

}
