package com.cdz360.biz.ant.rest.invoice;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.service.invoice.NsrInvoiceService;
import com.chargerlinkcar.framework.common.domain.invoice.nsr.ImportNSRInvoiceResultVo;
import com.chargerlinkcar.framework.common.domain.invoice.nsr.NSRInvoiceFileResultVo;
import com.chargerlinkcar.framework.common.domain.invoice.nsr.NSRInvoiceResultVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "全电发票相关接口", description = "全电发票相关接口")
@RequestMapping("/api/invoice/nsr")
public class NsrInvoiceRest {

    @Autowired
    private NsrInvoiceService nsrInvoiceService;

    @Operation(summary = "导入全电申请结果(EXCEL)")
    @PostMapping("/importNsrResult")
    public Mono<ObjectResponse<ImportNSRInvoiceResultVo<NSRInvoiceResultVo>>> importNsrResult(
        ServerHttpRequest request,
        @RequestHeader("Content-Length") long size,
        @RequestPart("file") FilePart file) {
        log.info("导入全电申请结果: {}B / {}", size, file.filename());
        return nsrInvoiceService.importNsrResult(file);
    }

    @Operation(summary = "导入全电申请结果文件")
    @PostMapping("/importNsrFileResult")
    public Mono<ObjectResponse<ImportNSRInvoiceResultVo<NSRInvoiceFileResultVo>>> importNsrFileResult(
        ServerHttpRequest request,
        @RequestHeader("Content-Length") long size,
        @RequestPart("file") FilePart file) {
        log.info("导入全电申请结果文件: {}B / {}", size, file.filename());
        return nsrInvoiceService.importNsrFileResult(file);
    }

}
