package com.cdz360.biz.ant.aop;

/**
 * 用于检查token
 *
 * <AUTHOR>
 * @since 2019/5/14 13:45
 */

import jakarta.servlet.http.HttpServletRequest;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface CheckToken {

    boolean login() default false;//强制检查开启，默认是关闭

    String check() default "";//检查时调用的方法名

    Class clazz() default HttpServletRequest.class;//FIXME 这个参数需要搭配check()来使用，这里写得不太好
}
