package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.domain.BlocUser;
import com.cdz360.biz.ant.domain.vo.SysRole;
import com.cdz360.biz.auth.corp.param.BalanceRemindParam;
import com.cdz360.biz.auth.subscribe.param.AddSubscribeParam;
import com.cdz360.biz.auth.subscribe.param.CreatePayOrderParam;
import com.cdz360.biz.auth.subscribe.param.SubLogListParam;
import com.cdz360.biz.auth.subscribe.param.SubscribeListParam;
import com.cdz360.biz.auth.subscribe.vo.CommVo;
import com.cdz360.biz.auth.subscribe.vo.SubscribeDetailVo;
import com.cdz360.biz.auth.subscribe.vo.SubscribeLogVo;
import com.cdz360.biz.auth.subscribe.vo.SubscribeOrderDetailVo;
import com.cdz360.biz.auth.subscribe.vo.SubscribeVo;
import com.cdz360.biz.auth.sys.param.AccRelativeParam;
import com.cdz360.biz.auth.sys.param.AddAccRelativeParam;
import com.cdz360.biz.auth.sys.param.BatchAddRoleUserParam;
import com.cdz360.biz.auth.sys.param.RoleUserListParam;
import com.cdz360.biz.auth.sys.param.RoleUserUpdateParam;
import com.cdz360.biz.auth.sys.param.YwUserParam;
import com.cdz360.biz.auth.sys.vo.AccRelativeVo;
import com.cdz360.biz.auth.sys.vo.RoleUserVo;
import com.cdz360.biz.auth.sys.vo.SysRoleSimpleVo;
import com.cdz360.biz.auth.sys.vo.SysRoleVo;
import com.cdz360.biz.auth.user.dto.SysUserLoginResult;
import com.cdz360.biz.auth.user.param.SysUserLogParam;
import com.cdz360.biz.auth.user.param.SysUserLoginParam;
import com.cdz360.biz.auth.user.po.SysUserPo;
import com.cdz360.biz.auth.user.vo.SysUserLogVo;
import com.cdz360.biz.auth.zft.dto.ZftDto;
import com.cdz360.biz.auth.zft.param.ListZftParam;
import com.cdz360.biz.auth.zft.vo.ZftVo;
import com.cdz360.biz.model.common.request.TokenRequest;
import com.cdz360.biz.model.cus.corp.param.ListCorpParam;
import com.cdz360.biz.model.cus.corp.po.CorpOrgPo;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.corp.vo.CorpOrgVO;
import com.cdz360.biz.model.cus.corp.vo.CorpSimpleVo;
import com.cdz360.biz.model.cus.corp.vo.CorpVo;
import com.cdz360.biz.model.cus.message.param.ListMessageParam;
import com.cdz360.biz.model.cus.message.po.MessagePo;
import com.cdz360.biz.model.cus.message.vo.MessageVo;
import com.cdz360.biz.model.cus.message.vo.UserMessageVo;
import com.cdz360.biz.model.cus.param.SysUserCheckParam;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.merchant.dto.CommercialDto;
import com.cdz360.biz.model.merchant.param.ListCommercialParam;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import com.cdz360.biz.model.sys.vo.SiteGroupVo;
import com.cdz360.biz.model.sys.vo.SubscribeOrderVo;
import com.chargerlinkcar.framework.common.domain.SysUser;
import com.chargerlinkcar.framework.common.domain.TCommercialUser;
import com.chargerlinkcar.framework.common.domain.request.AddAuthorityGroupRequest;
import com.chargerlinkcar.framework.common.domain.request.AddUserGroupRequest;
import com.chargerlinkcar.framework.common.domain.vo.Authority;
import com.chargerlinkcar.framework.common.domain.vo.AuthorityGroup;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import javax.validation.Valid;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 权限中心用户操作的FeignClient
 *
 */
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_AUTH, fallbackFactory = AuthCenterFeignClientHystrixFactory.class)
public interface AuthCenterFeignClient {

    /**
     * 根据id集合查询用户信息
     *
     * @param idList
     */
    @PostMapping(value = "/data/users/querySysUserByIds")
    ListResponse<SysUserVo> querySysUserByIds(@RequestBody List<Long> idList);

    /**
     * 根据userId查询用户权限信息
     *
     * @param userId
     */
    @RequestMapping(value = "/data/roles/user/{userId}", method = RequestMethod.GET)
    List<SysRoleVo> findByUserId(@PathVariable(value = "userId") Long userId,
        @RequestHeader("token") String token);

    @PostMapping(value = "/data/roles")
    BaseResponse add(@RequestHeader("token") String token, @RequestBody SysRole role);

    @PutMapping(value = "/data/roles/{id}")
    BaseResponse modify(@RequestHeader("token") String token, @PathVariable(value = "id") Long id,
        @RequestBody SysRole role);

    @GetMapping("/api/authorityGroup/getAuthorityList")
    ListResponse<Authority> getAuthorityList(
        @RequestHeader("token") String token);

    @PostMapping("/api/authorityGroup/addGroup")
    BaseResponse AddGroup(
        @RequestHeader("token") String token,
        @RequestBody AddAuthorityGroupRequest addAuthorityGroupRequest);

    @GetMapping("/api/authorityGroup/getGroupList")
    ListResponse<AuthorityGroup> getGroups(
        @RequestHeader("token") String token);

    @GetMapping("/api/authorityGroup/getGroupById")
    ObjectResponse<AuthorityGroup> getGroupById(
        @RequestHeader("token") String token,
        @RequestParam("id") Long id);

    @DeleteMapping("/api/authorityGroup/deleteGroupById")
    BaseResponse deleteGroupById(
        @RequestHeader("token") String token,
        @RequestParam("id") Long id);

    @GetMapping("/api/authorityGroup/getUserAuthoritiesByUid")
    ListResponse<Authority> getUserAuthoritiesByUid(
        @RequestHeader("token") String token,
        @RequestParam("id") Long uid);

    @GetMapping("/api/authorityGroup/modifyUserGroupRef")
    BaseResponse modifyUserGroupRef(
        @RequestHeader("token") String token,
        @RequestBody AddUserGroupRequest addUserGroupRequest);

    @PostMapping(value = "/api/commercials/info/token")
    ObjectResponse<TCommercialUser> getCommercialsUserByToken(@RequestHeader("token") String token);

    @PostMapping("/api/msg/addMessage")
    ObjectResponse addMessage(@RequestParam(value = "token", required = false) String token,
        @RequestBody MessagePo message);

    @GetMapping("/api/msg/editMessage")
    ObjectResponse editMessage(@RequestParam("token") String token,
        @RequestParam("msgId") Long msgId);

    @GetMapping("/api/msg/getMessage")
    ObjectResponse<UserMessageVo> getMessage(@RequestParam("token") String token,
        @RequestParam("msgId") Long msgId);

    @GetMapping("/api/msg/getUnReadCount")
    ObjectResponse getUnReadCount(@RequestParam("token") String token,
        @RequestParam("platform") Long platform);

    @PostMapping("/api/msg/getMsgList")
    ListResponse<MessageVo> getMsgList(@RequestBody ListMessageParam reqParam);

    @PostMapping("/api/msg/getUserMsgList")
    ListResponse<UserMessageVo> getUserMsgList(@RequestParam("token") String token,
        @RequestBody ListMessageParam reqParam);

    @PostMapping("/api/sys/user/login")
    ObjectResponse<SysUserLoginResult> login(@RequestBody SysUserLoginParam param);

    @PostMapping("/api/logout")
    BaseResponse logout(@RequestBody @Valid TokenRequest tokenRequest);

    @PostMapping("/api/info/token")
     ObjectResponse<SysUser> getUserByToken(@RequestBody @Valid TokenRequest tokenRequest);

    /**
     * 免密切换用户
     *
     * @param token
     * @param targetSysUid
     * @return
     */
    @PostMapping("/api/sys/user/switch")
    ObjectResponse<SysUserLoginResult> switchUser(@RequestHeader("token") String token,
//                                                  @RequestParam(value = "sysUid") Long sysUid,
        @RequestParam(value = "targetSysUid") Long targetSysUid);

    /**
     * 企业平台免密登陆登录
     *
     * @param corpId
     * @param idChain
     * @return
     */
    @PostMapping("/api/sys/user/getLoginTmpKey")
    ObjectResponse<String> getLoginTmpKey(
        @RequestHeader("token") String token,
        @RequestParam(value = "corpId") Long corpId,
        @RequestParam(value = "idChain") String idChain);

    @PostMapping("/api/sys/user/getLoginInFoByKey")
    ObjectResponse<SysUserLoginResult> getLoginInFoByKey(
        @RequestHeader("token") String token,
        @RequestParam(value = "key") String key);

    @GetMapping("/api/sys/user/getOpenId")
    BaseResponse getOpenId(
        @RequestParam("token") String token,
        @RequestParam(value = "code") String code);

    @GetMapping("/api/sys/user/refreshCorpTokenValue")
    BaseResponse refreshCorpTokenValue(
        @RequestHeader("token") String token,
        @RequestParam(value = "username") String username);

    @PostMapping("/api/commercials/user/edit")
    BaseResponse edit(
        @RequestHeader("token") String token,
        @RequestBody TCommercialUser tcu);

    @PostMapping("/data/users")
    BaseResponse add(
        @RequestHeader("token") String token,
        @RequestBody SysUser bodyEntity);

    @PutMapping("/data/users/modify")
    BaseResponse modify(
        @RequestHeader("token") String token,
        @RequestBody SysUser bodyEntity);

    @PutMapping("/data/users/changeState")
    BaseResponse changeState(
        @RequestHeader("token") String token,
        @RequestBody SysUser bodyEntity);

    @PostMapping("/api/sys/user/getLoginLog")
    ListResponse<SysUserLogVo> getLoginLog(@RequestBody SysUserLogParam param);

    @PostMapping("/api/sys/user/getOpLog")
    ListResponse<SysUserLogVo> getOpLog(@RequestBody SysUserLogParam param);

    @GetMapping(value = "/api/corp/getOrgList")
    ListResponse<CorpOrgVO> getOrgList(
        @RequestHeader("token") String token,
        @RequestParam("corpId") Long corpId, @RequestParam("_index") Integer _index,
        @RequestParam("_size") Integer _size);

    @GetMapping(value = "/api/corp/getOrgByLevel")
    ListResponse<CorpOrgVO> getOrgByLevel(
        @RequestHeader("token") String token,
        @RequestParam("corpId") Long corpId, @RequestParam("orgLevel") Integer level);

    @GetMapping(value = "/api/corp/getOrgByUserId")
    ListResponse<CorpOrgVO> getOrgByUserId(
        @RequestHeader("token") String token,
        @RequestParam("corpId") Long corpId, @RequestParam("cusId") Integer cusId);

    /**
     * 新增或者更新企业组织
     *
     * @param corpOrgPo
     * @return
     */
    @PostMapping(value = "/api/corp/addOrUpdateCorpOrg")
    BaseResponse addOrUpdateCorpOrg(
        @RequestHeader("token") String token,
        @RequestBody CorpOrgPo corpOrgPo);

    @PostMapping("/api/corp/addCorp")
    ObjectResponse<Long> addCorp(@RequestBody CorpPo corp);

    @GetMapping("/api/corp/getCorp")
    ObjectResponse<CorpPo> getCorp(@RequestParam(value = "corpId") Long corpId);

    /**
     * 更新集团信息
     *
     * @param blocUser
     * @return
     */
    @RequestMapping(value = "/api/corp/updateBlocUser", method = RequestMethod.POST)
    ObjectResponse<CorpPo> updateBlocUser(@RequestBody BlocUser blocUser);

//    /**
//     * 获取当前账户所在组织以及子组织下的成员
//     * @param userId
//     * @return
//     */
//    @GetMapping(value = "/data/users/getUserByOrg")
//    ListResponse<Long> getUserByOrg(@RequestParam(value = "userId") Long userId);

    @Operation(summary = "通过UID获取企业信息")
    @GetMapping(value = "/api/corp/getCorpByUid")
    ObjectResponse<com.cdz360.biz.auth.corp.po.CorpPo> getCorpByUid(
        @RequestHeader("token") String token,
        @RequestParam(value = "corpUid") Long corpUid);

    @PostMapping("/api/corp/getCorpList")
    ListResponse<CorpVo> getCorpList(
        @RequestHeader("token") String token,
        @RequestBody ListCorpParam param);

    @GetMapping("/api/corp/getCorpByCommId")
    ListResponse<CorpSimpleVo> getCorpByCommId(
        @RequestHeader("token") String token,
        @RequestParam(value = "commIdChain") String commIdChain,
        @RequestParam(value = "corpId", required = false) Long corpId);

    // 通过企业ID获取所属场站组
    @GetMapping(value = "/api/corp/getGidsById")
    ListResponse<String> getGidsById(@RequestParam(value = "corpId") Long corpId);

    @PostMapping("/api/corp/setRenewReminderAmount")
    BaseResponse setRenewReminderAmount(@RequestBody BalanceRemindParam param);

    /**
     * 企业客户充值后，按条件重置邮件发送状态
     *
     * @param corpId
     * @return
     */
    @GetMapping(value = "/api/corp/resetEmailSendStatus")
    BaseResponse resetEmailSendStatus(@RequestParam(value = "corpId", required = false) Long corpId,
        @RequestParam(value = "corpUid", required = false) Long corpUid);

    /**
     * 获取直付商家列表
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/zft/zftList")
    ListResponse<ZftVo> zftList(
        @RequestHeader("token") String token,
        @RequestBody ListZftParam param);

    /**
     * 获取直付商家的信息
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/api/zft/getZft")
    ObjectResponse<ZftVo> getZft(
        @RequestHeader("token") String token,
        @RequestParam(value = "id") Long id);

    /**
     * 更新直付商家信息
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/api/zft/updateZft")
    ObjectResponse<Long> updateZft(
        @RequestHeader("token") String token,
        @RequestBody ZftDto dto);

    /**
     * 商户编辑互联站点支持的账户类型
     *
     * @param commId
     * @param payTypeList
     * @return
     */
    @GetMapping(value = "/api/comm/editHlhtSitePayType")
    BaseResponse editHlhtSitePayType(@RequestParam(value = "commId") Long commId,
        @RequestParam(value = "payTypeList") List<String> payTypeList);

    /**
     * 获取商户信息列表
     *
     * @param param
     * @return
     */
    @PostMapping("/api/comm/getCommList")
    ListResponse<CommercialDto> getCommList(@RequestBody ListCommercialParam param);

    @PostMapping("/api/accRelative/list")
    ListResponse<AccRelativeVo> getVoList(@RequestHeader("token") String token,
        @RequestBody AccRelativeParam param);

    @GetMapping("/api/accRelative/syncOrderNum")
    BaseResponse syncOrderNum(@RequestHeader("token") String token);

    // 获取运维账号信息
    @GetMapping("/api/accRelative/getYwAccount")
    ObjectResponse<AccRelativeVo> getYwAccount(
        @RequestHeader("token") String token,
        @RequestParam("sysUid") Long sysUid);

    @GetMapping("/api/accRelative/findByAccount")
    ObjectResponse<AccRelativeVo> findByAccount(@RequestHeader("token") String token,
        @RequestParam(value = "account") String account,
        @RequestParam(value = "commIdChain") String commIdChain);

    @PostMapping(value = "/api/accRelative/add")
    ListResponse<SysUserVo> addAccRelative(@RequestHeader("token") String token,
        @RequestBody AddAccRelativeParam param);

    // 检查运维账户下是否存在未完成的工单
    @GetMapping(value = "/api/accRelative/checkForOutstandingOrder")
    BaseResponse checkForOutstandingOrder(@RequestHeader("token") String token,
        @RequestParam("sysUid") Long sysUid);

    @PostMapping(value = "/api/accRelative/edit")
    ListResponse<SysUserVo> editAccRelative(@RequestHeader("token") String token,
        @RequestBody AddAccRelativeParam param);

    @GetMapping(value = "/api/accRelative/delete")
    ObjectResponse<SysUserVo> deleteBySysUid(@RequestHeader("token") String token,
        @RequestParam("sysUid") Long sysUid);

    // 获取运维人员列表(通过场站组来获取)
    @PostMapping(value = "/api/accRelative/findYwUser")
    ListResponse<SysUser> findYwUser(@RequestBody YwUserParam param);

    @Operation(summary = "获取用户所属运维组其他人员列表", description = "通过场站组来获取")
    @GetMapping(value = "/api/accRelative/getYwGroupOtherUser")
    ListResponse<SysUser> getYwGroupOtherUser(
        @ApiParam(value = "用户ID", required = true) @RequestParam(value = "uid") Long uid,
        @ApiParam(value = "是否同组", required = true) @RequestParam(value = "same") Boolean same,
        @ApiParam("运维场站组ID") @RequestParam(value = "gidList", required = false) List<String> gidList);

    @PostMapping("/api/sys/user/getSysUserByIdList")
    ListResponse<SysUserVo> getSysUserByIdList(@RequestBody List<Long> ids);

    @PostMapping("/api/sys/user/getByUserNameLike")
    ListResponse<SysUserPo> getByUserNameLike(@RequestParam(value = "username") String username);

    @GetMapping("/api/comm/getCommPlatInfo")
    ObjectResponse<CommercialDto> getCommPlatInfo(@RequestParam(value = "commId") Long commId);

    // 获取商户信息
    @GetMapping("/api/comm/findSimpleVoById")
    ObjectResponse<CommercialSimpleVo> findSimpleVoById(@RequestParam("commId") Long commId);

    // 获取用户信息
    @PostMapping("/api/sys/user/getByUserNameAndPlatform")
    ObjectResponse<SysUserPo> getByUserNameAndPlatform(
        @RequestParam(value = "username") String username,
        @RequestParam(value = "platform") AppClientType platform);

    /**
     * 批量修改角色关联的账号
     *
     * @param params
     * @return
     */
    @PostMapping("/data/users/batchUpdateRoleUser")
    BaseResponse batchUpdateRoleUser(@RequestBody RoleUserUpdateParam params);

    /**
     * 角色批量修改账号
     *
     * @param params
     * @return
     */
    @PostMapping("/data/users/batchAddRoleUser")
    BaseResponse batchAddRoleUser(@RequestBody BatchAddRoleUserParam params);

    @GetMapping("/data/users/getUserByRoleId")
    ListResponse<RoleUserVo> getUserByRoleId(@RequestParam("keyWord") String keyWord,
        @RequestParam("platform") Long platform,
        @RequestParam("roleId") Long roleId,
        @RequestParam("size") Long size);

    @PostMapping("/data/users/getUserListByRoleId")
    ListResponse<RoleUserVo> getUserListByRoleId(@RequestBody RoleUserListParam params);

    // sys_user相关检查
    @PostMapping("/data/users/checkInDB")
    ListResponse<SysUserCheckParam> checkInDB(@RequestBody List<SysUserCheckParam> list);

    @PostMapping("/data/roles/getRoleListByUserId")
    ListResponse<SysRole> getRoleListByUserId(@RequestHeader("token") String token,
        @RequestBody RoleUserListParam params);

    @GetMapping("/data/roles/getRoleByUserId")
    ListResponse<SysRole> getRoleByUserId(@RequestHeader("token") String token,
        @RequestParam(value = "keyWord", required = false) String keyWord,
        @RequestParam("platform") Long platform,
        @RequestParam("userId") Long userId,
        @RequestParam(value = "size", required = false) Long size);

    @PostMapping("/data/roles/batchUpdateRoleUserByUserId")
    BaseResponse batchUpdateRoleUserByUserId(@RequestHeader("token") String token,
        @RequestBody RoleUserUpdateParam params);

    @PostMapping("/data/roles/batchAddRoleUserByUserId")
    BaseResponse batchAddRoleUserByUserId(@RequestHeader("token") String token,
        @RequestBody BatchAddRoleUserParam params);

    @PostMapping("/api/sub/add")
    BaseResponse add(@RequestBody AddSubscribeParam params);

    @PostMapping("/api/sub/update")
    BaseResponse update(@RequestBody AddSubscribeParam params);

    @GetMapping("api/sub/updateStatus")
    BaseResponse updateStatus(@RequestParam("sysUid") Long sysUid,
        @RequestParam("subId") Long subId);

    @PostMapping("/api/sub/getList")
    ListResponse<SubscribeVo> getList(@RequestBody SubscribeListParam params);

    @GetMapping("/api/sub/getCommList")
    ListResponse<CommVo> getCommList(@RequestParam("subId") Long subId);

    @GetMapping("/api/sub/getRoleList")
    ListResponse<SysRoleSimpleVo> getRoleList(@RequestParam("subId") Long subId);

    @GetMapping("/api/sub/getDetail")
    ObjectResponse<SubscribeDetailVo> getDetail(@RequestParam("subId") Long subId);


    @GetMapping("/api/sub/getRoleListByPayNo")
    ListResponse<SubscribeOrderDetailVo> getRoleListByPayNo(
        @RequestParam(value = "payNo") String payNo);

    @PostMapping("/api/sub/getSubLogList")
    ListResponse<SubscribeLogVo> getSubLogList(@RequestBody SubLogListParam params);

    @GetMapping("/api/sub/getOrderById")
    ObjectResponse<SubscribeOrderVo> getOrderById(@RequestParam(value = "payNo") String payNo);

    @PostMapping("/api/sub/createPayOrder")
    ObjectResponse<String> createPayOrder(@RequestBody CreatePayOrderParam params);

    @GetMapping("/api/sub/getListByUser")
    ListResponse<SubscribeDetailVo> getListByUser(@RequestHeader(value = "token") String token,
        @RequestParam(value = "status", required = false) Boolean status);

    @GetMapping("/api/sub/addNote")
    BaseResponse addNote(@RequestParam(value = "payNo") String payNo,
        @RequestParam(value = "note") String note);

    @GetMapping(value = "/api/sys/user/getSiteGroupsList")
    ListResponse<SiteGroupVo> getSiteGroupsList(@RequestHeader(value = "token") String token,
        @RequestParam("types") List<Integer> types);

    @GetMapping("/api/sys/user/getSiteGroupsByUid")
    ListResponse<SiteGroupVo> getSiteGroupsByUid(@RequestHeader(value = "token") String token,
        @RequestParam(value = "type", required = false) Integer type);
}
