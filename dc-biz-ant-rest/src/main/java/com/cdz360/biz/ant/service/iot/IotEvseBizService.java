package com.cdz360.biz.ant.service.iot;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.domain.vo.AntEvseInfoVo;
import com.cdz360.biz.ant.utils.FileUtil;
import com.cdz360.biz.model.finance.type.BizType;
import com.cdz360.biz.model.iot.param.BindEvseParam;
import com.cdz360.biz.model.iot.param.EvseTinyParam;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.iot.param.ModifyEvseInfoParam;
import com.cdz360.biz.model.iot.param.UnbindEvseParam;
import com.cdz360.biz.model.iot.param.UpgradeRecordVo;
import com.cdz360.biz.model.iot.param.UpgradeTaskListRequest;
import com.cdz360.biz.model.iot.type.EvseBizType;
import com.cdz360.biz.model.iot.vo.EvseImportVo;
import com.cdz360.biz.model.iot.vo.EvseModelVo;
import com.cdz360.biz.model.merchant.dto.CommercialDto;
import com.cdz360.biz.model.merchant.dto.SiteTinyDto;
import com.cdz360.biz.model.merchant.param.ListCommercialParam;
import com.cdz360.biz.model.trading.iot.dto.EvseTinyDto;
import com.cdz360.biz.model.trading.iot.po.BsBoxPo;
import com.cdz360.biz.model.trading.iot.po.EvseModelPo;
import com.cdz360.biz.model.trading.iot.vo.EvseInfoVo;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.vin.po.SiteAuthVinLogPo;
import com.chargerlinkcar.framework.common.domain.vo.SiteAuthVinParam;
import com.chargerlinkcar.framework.common.feign.CommercialFeignClient;
import com.chargerlinkcar.framework.common.feign.DeviceDataCoreFeignClient;
import com.chargerlinkcar.framework.common.feign.DzDeviceFeignClient;
import com.chargerlinkcar.framework.common.feign.IotBizClient;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.feign.SiteDataCoreFeignClient;
import com.chargerlinkcar.framework.common.feign.UserFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class IotEvseBizService {


    @Autowired
    private IotBizClient iotBizClient;
    @Autowired
    private EvseProcessor evseProcessor;


    @Autowired
    private DzDeviceFeignClient dzDeviceFeignClient;

    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;

    @Autowired
    private SiteDataCoreFeignClient siteDataCoreFeignClient;

    @Autowired
    private DeviceDataCoreFeignClient deviceDataCoreFeignClient;

    @Autowired
    private CommercialFeignClient commercialFeignClient;

    @Autowired
    private UserFeignClient userFeignClient;

    public AntEvseInfoVo getEvseInfo(String evseNo) {
        if (StringUtils.isBlank(evseNo)) {
            throw new DcArgumentException("参数错误,桩编号不能为空");
        }
        ObjectResponse<EvseInfoVo> evseRes = iotDeviceMgmFeignClient.getEvseInfo(evseNo);
        FeignResponseValidate.check(evseRes);
        EvseInfoVo evseInfoVo = evseRes.getData();
        IotAssert.isNotNull(evseInfoVo, "桩不存在");

        this.fillSiteCommInfo(List.of(evseInfoVo));
        AntEvseInfoVo evse = new AntEvseInfoVo();
        BeanUtils.copyProperties(evseInfoVo, evse);

        ObjectResponse<BsBoxPo> boxRes = deviceDataCoreFeignClient.getBsBox(evseNo);
        if (boxRes.getData() != null) {
            // 是否使用场站默认配置
            evse.setUseSiteSetting(boxRes.getData().getUseSiteSetting());
        }
        return evse;
    }

    public AntEvseInfoVo getEvseSimpleInfo(String evseNo, List<String> gids) {
        if (StringUtils.isBlank(evseNo)) {
            throw new DcArgumentException("参数错误,桩编号不能为空");
        }
        ObjectResponse<EvseInfoVo> evseRes = iotDeviceMgmFeignClient.getEvseInfo(evseNo);
        FeignResponseValidate.check(evseRes);
        EvseInfoVo evseInfoVo = evseRes.getData();
        IotAssert.isNotNull(evseInfoVo, "桩不存在");

        AntEvseInfoVo evse = new AntEvseInfoVo();
        evse.setSiteId(evseInfoVo.getSiteId())
            .setSiteName(evseInfoVo.getSiteName())
            .setSiteCommName(evseInfoVo.getSiteCommName())
            .setSiteCommId(evseInfoVo.getSiteCommId())
            .setEvseNo(evseInfoVo.getEvseNo());

        if (CollectionUtils.isNotEmpty(gids)) {
            ListSiteParam params = new ListSiteParam();
            params.setGids(gids);
            ListResponse<String> response = siteDataCoreFeignClient.getSiteListByGids(params);
            FeignResponseValidate.check(response);
            IotAssert.isTrue(response.getData().contains(evse.getSiteId()),"场站信息不存在");
        }
        return evse;
    }

    public ListResponse<EvseInfoVo> getEvseInfoList(ListEvseParam param) {

//        if (StringUtils.isNotBlank(param.getSiteName())) {
//            // 根据传入的场站名字获取场站列表
//            ListSiteParam listSiteParam = new ListSiteParam();
//            listSiteParam.setTopCommId(param.getTopCommId())
//                    .setSiteName(param.getSiteName());
//            ListResponse<SiteTinyDto> siteListRes = siteDataCoreFeignClient.getSiteTinyList(listSiteParam);
//            if (siteListRes.getData() != null) {
//                param.setSiteIdList(siteListRes.getData()
//                        .stream().map(SiteTinyDto::getId)
//                        .collect(Collectors.toList()));
//            }
//        }
        ListResponse<EvseInfoVo> evseRes = iotDeviceMgmFeignClient.getEvseInfoList(param);
        FeignResponseValidate.check(evseRes);

        if (CollectionUtils.isNotEmpty(evseRes.getData())) {
            this.fillSiteCommInfo(evseRes.getData());
        }
        return evseRes;
    }

    public ListResponse<EvseTinyDto> getEvseTinyList(EvseTinyParam param) {
        return iotDeviceMgmFeignClient.getEvseTinyList(param);
    }

    public ListResponse<EvseInfoVo> getEvseListForVinAuth(ListEvseParam param) {
        if (StringUtils.isNotEmpty(param.getVin())) {
            ListResponse<String> listResponse = userFeignClient.getEvseListByVin(param.getSiteId(),param.getVin());
            if (listResponse != null && CollectionUtils.isNotEmpty(listResponse.getData())) {
                param.setEvseNoList(listResponse.getData());
            } else {
                return RestUtils.buildListResponse(null);
            }
        }
        ListResponse<EvseInfoVo> evseRes = iotDeviceMgmFeignClient.getEvseInfoList(param);
        FeignResponseValidate.check(evseRes);
        //桩最新下发时间
        if (CollectionUtils.isNotEmpty(evseRes.getData())) {
            List<String> evseNolist = evseRes.getData().stream().map(EvseInfoVo::getEvseNo).collect(Collectors.toList());
            SiteAuthVinParam siteAuthVinParam = new SiteAuthVinParam();
            siteAuthVinParam.setEvseNoList(evseNolist);
            ListResponse<SiteAuthVinLogPo> listResponse = userFeignClient.getSiteAuthVinTime(siteAuthVinParam);
            if (listResponse != null && CollectionUtils.isNotEmpty(listResponse.getData())) {
                Map<String,SiteAuthVinLogPo> map = listResponse.getData().stream().collect(Collectors.toMap(SiteAuthVinLogPo::getEvseId,siteAuthVinLogPo -> siteAuthVinLogPo));
                evseRes.getData().stream().forEach(e->{
                    if (map.containsKey(e.getEvseNo())) {
                        e.setIssuedTime(map.get(e.getEvseNo()).getUpdateTime());
                    }
                });
            }
        }
        return evseRes;
    }

    public ListResponse<UpgradeRecordVo> getEvseUpgradeList(UpgradeTaskListRequest req) {
        return iotDeviceMgmFeignClient.getUpgradeRecordVo(req);
    }

    public void fillSiteCommInfo(List<EvseInfoVo> evseList) {
        if (CollectionUtils.isEmpty(evseList)) {
            return;
        }
        List<String> siteIdList = evseList.stream().map(EvseInfoVo::getSiteId).collect(Collectors.toList());
        // 根据传入的场站名字获取场站列表
        ListSiteParam listSiteParam = new ListSiteParam();
        listSiteParam.setSiteIdList(siteIdList);
        ListResponse<SiteTinyDto> siteListRes = siteDataCoreFeignClient.getSiteTinyList(listSiteParam);
        if (CollectionUtils.isEmpty(siteListRes.getData())) {
            return;
        }
        List<Long> commIdList = siteListRes.getData().stream().map(SiteTinyDto::getCommId).collect(Collectors.toList());

        ListCommercialParam listCommParam = new ListCommercialParam();
        listCommParam.setCommIdList(commIdList);
        ListResponse<CommercialDto> listCommRes = this.commercialFeignClient.getCommList(listCommParam);
        if (CollectionUtils.isEmpty(listCommRes.getData())) {
            return;
        }
        evseList.stream().forEach(evse -> {
            if (StringUtils.isNotBlank(evse.getSiteId())) {
                for (CommercialDto comm : listCommRes.getData()) {
                    if (NumberUtils.equals(comm.getId(), evse.getSiteCommId())) {
                        evse.setSiteCommName(comm.getCommName())
                                .setTopCommId(comm.getTopCommId())
                                .setTopCommName(comm.getTopCommName());
                        break;
                    }
                }
            }
        });
    }


    /**
     * 桩绑定到场站
     *
     * @param param
     */
    public void bindEvse2Site(BindEvseParam param) {

        Assert.isTrue(com.cdz360.base.utils.StringUtils.isNotBlank(param.getSiteId()), "新增充电桩失败，站点不能为空");
        Assert.isTrue(com.cdz360.base.utils.StringUtils.isNotBlank(param.getEvseNo()), "新增充电桩失败，桩号不能为空");
        Assert.isTrue(com.cdz360.base.utils.StringUtils.isNotBlank(param.getName()), "新增充电桩失败，桩名称不能为空");
        Assert.notNull(param.getPower(), "新增充电桩失败，桩的额定功率不能空");
        Assert.isTrue(param.getPower() <= 99999, "新增充电桩失败，桩的额定功率最大输入设定为99999kw");

        ObjectResponse<SitePo> response = siteDataCoreFeignClient.getSiteById(param.getSiteId());
        FeignResponseValidate.check(response);
        param.setBizType(BizType.isOffline(response.getData().getBizType()) ? EvseBizType.OFFLINE : EvseBizType.CONNECTED);

        BaseResponse res = iotBizClient.bindEvse2Site(param);
        FeignResponseValidate.check(res);

        // 以下逻辑转移至data-core SyncIotInfoEventListener
//        // 调用原有的动正代码逻辑
//        BoxActivateRequest paramx = new BoxActivateRequest();
//        paramx.setBoxName(param.getName());
//        paramx.setIsUseSiteDefaultSetting(Boolean.TRUE.equals(param.getUseSiteSetting()) ? 1 : 0);
//        paramx.setSerialNumber(param.getEvseNo());
//        paramx.setSiteId(param.getSiteId());
//        paramx.setRatedPower(param.getPower());
//        ObjectResponse<Boolean> res2 = this.dzDeviceFeignClient.activateBoxInfo(paramx);
//        FeignResponseValidate.check(res2);

        //更新t_site中桩枪数量等信息
        evseProcessor.recordEvsePlugInfo(List.of(param.getSiteId()));
    }

    /**
     * 桩从场站解绑
     *
     * @param param
     * @param commId
     * @return
     */
    public ObjectResponse<Boolean> unbindEvse(UnbindEvseParam param, Long commId) {
        log.info("param = {}, commId = {}", param, commId);
        BaseResponse res1 = this.iotBizClient.unbindEvse2Site(param);
        FeignResponseValidate.check(res1);

        // 以下逻辑转移至data-core SyncIotInfoEventListener
//        BoxUnActivateRequest paramX = new BoxUnActivateRequest();
//        paramX.setSerialNumber(param.getEvseNo()).setOperatorId(commId);
//        ObjectResponse<Boolean> res = dzDeviceFeignClient.unActivateBoxInfo(paramX);

        //更新t_site中桩枪数量等信息, 解绑相关定时充电任务
        evseProcessor.recordEvsePlugInfo(param.getSiteId(), param.getEvseNoList());
        return RestUtils.buildObjectResponse(Boolean.TRUE);
    }


    /**
     * 修改桩信息
     *
     * @param param
     */
    public void updateEvseInfo(ModifyEvseInfoParam param) {
        if (StringUtils.isBlank(param.getEvseNo())) {
            throw new DcArgumentException("参数错误,桩编号不能为空");
        } else if (param.getPower() != null && param.getPower() > 99999) {
            throw new DcArgumentException("请输入合理的桩功率");
        } else if (param.getName() != null && param.getName().length() > 32) {
            throw new DcArgumentException("您输入的桩名称太长");
        }

        BaseResponse res = this.iotBizClient.updateEvseInfo(param);
        FeignResponseValidate.check(res);

        BsBoxPo box = new BsBoxPo();
        box.setEvseNo(param.getEvseNo())
                .setEvseName(param.getName())//Y2020-2315
                .setUseSiteSetting(param.getUseSiteSetting())
                .setSiteId(param.getSiteId())
                .setPower(param.getPower());
        res = this.deviceDataCoreFeignClient.updateBsBox(box);
        FeignResponseValidate.check(res);
    }


    /**
     * 批量编辑桩信息 (只包含：是否使用场站通用配置，设备型号，出厂日期，质保期限，直流模块)
     * @param param
     */
    public void batchUpdateEvseInfo(ModifyEvseInfoParam param) {
        if (CollectionUtils.isEmpty(param.getEvseNoList())) {
            throw new DcArgumentException("参数错误,桩编号不能为空");
        } else if (param.getPower() != null && param.getPower() > 99999) {
            throw new DcArgumentException("请输入合理的桩功率");
        } else if (param.getName() != null && param.getName().length() > 32) {
            throw new DcArgumentException("您输入的桩名称太长");
        }

        BaseResponse res = this.iotBizClient.batchUpdateEvseInfo(param);
        FeignResponseValidate.check(res);

        evseProcessor.batchUpdateBsBox(param);
    }

    public ListResponse<EvseModelPo> getModelList(Long start, Integer size, String keyword) {
        return iotDeviceMgmFeignClient.getModelList(start, size, keyword);
    }

    public Mono<ObjectResponse<EvseImportVo>> parseEvseExcel(FilePart file) {
        log.info(">>parseEvseExcel excel 文件: {}", file.filename());

        try {
            return FileUtil.filePartToMultipartFile(file)
                .flatMap(e -> Mono.just(deviceDataCoreFeignClient.parseEvseExcel(e)))
                .doOnNext(FeignResponseValidate::check);
        } catch (Exception e) {
            log.warn("error: {}", e.getMessage(), e);
            throw new DcServiceException("文件上传失败");
        }
//        Mono<ObjectResponse<EvseImportVo>> m = null;
//        try {
//            File f = new File("/tmp/" + file.filename());
//            m = file.transferTo(f)
//                .then(Mono.just("文件上传成功"))
//                .map(a -> {
//                    try {
//
//                        InputStream inputStream = new FileInputStream(f);
////                            log.info("inputStream: {}", inputStream);
//                        List<List<String>> list = com.cdz360.biz.ant.utils.ExcelUtils.getEvseListByExcel(inputStream, file.filename(), 8);
//                        log.info("list.size = {}", list == null ? null : list.size());
//                        ObjectResponse<EvseImportVo> result = deviceDataCoreFeignClient.parseEvseExcel(list);
//                        log.info("result = {}", result);
//                        FeignResponseValidate.check(result);
//
//                        return result;
//                    } catch (DcServiceException e) {
//                        throw new DcServiceException(e.getMessage(), e);
//                    } catch (Exception e) {
//                        log.warn("<< 解析excel文件异常. message = {}", e.getMessage(), e);
//                        throw new DcServiceException("解析excel文件异常，请求确认文件内容.");
//                    }
//                });
////                    .map(a -> RestUtils.buildObjectResponse(a));
//        } catch (Exception e) {
//            log.warn("msg: {}", e.getMessage(), e);
//            throw new DcServiceException("文件上传失败");
//        }
//        return m;
    }

    public BaseResponse batchImport(List<EvseModelVo> param) {
        String siteId = param.get(0).getSiteId();
        IotAssert.isTrue(StringUtils.isNotBlank(siteId), "导入失败（参数错误）");
        ObjectResponse<SitePo> response = siteDataCoreFeignClient.getSiteById(siteId);
        FeignResponseValidate.check(response);
        return iotBizClient.batchImport(param.stream()
                .peek(t -> t.setBizType(BizType.isOffline(response.getData().getBizType()) ? EvseBizType.OFFLINE : EvseBizType.CONNECTED))
                .collect(Collectors.toList()));
    }

    public BaseResponse rebootEvse(String evseNo) {
        return iotBizClient.rebootEvse(evseNo);
    }

    public ListResponse<String> getFirmwareVerList(BaseListParam param) {
        return iotDeviceMgmFeignClient.getFirmwareVerList(param);
    }

}
