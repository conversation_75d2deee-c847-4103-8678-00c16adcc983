package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.model.order.type.OrderPayType;
import com.chargerlinkcar.framework.common.constant.AccountType;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.constant.TransTypeEnum;
import com.chargerlinkcar.framework.common.domain.PointPo;
import com.chargerlinkcar.framework.common.domain.param.MerchantBalanceParam;
import com.chargerlinkcar.framework.common.domain.vo.AccountInfoVo;
import com.chargerlinkcar.framework.common.domain.vo.CommCusRef;
import com.chargerlinkcar.framework.common.domain.vo.PointPoVo;
import com.chargerlinkcar.framework.common.feign.CommercialFeignClient;
import com.chargerlinkcar.framework.common.feign.UserFeignClient;
import com.chargerlinkcar.framework.common.service.DcCusBalanceService;
import com.chargerlinkcar.framework.common.service.DcUserService;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * 
 * @since 2019/8/1
 **/
@Slf4j
@Service
public class MerchantBalanceService //implements IMerchantBalanceService
{


    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private DcCusBalanceService dcCusBalanceService;


    @Autowired
    private DcUserService dcUserService;


    @Autowired
    private CommercialFeignClient commercialFeignClient;


    public BaseResponse initMerchantBalance(MerchantBalanceParam param) {
        param.setBalanceType(AccountType.COMMERCIAL.getCode());
        BaseResponse baseResponse = userFeignClient.initMerchantBalance(param);
        FeignResponseValidate.check(baseResponse);
        return baseResponse;
    }


    public BaseResponse disableOrEnable(long userId, long topCommId, long subCommId, int status,int defaultPayType) {
        BaseResponse baseResponse = userFeignClient.disableOrEnableMerchantBalance(userId, topCommId, subCommId, status,defaultPayType);
        FeignResponseValidate.check(baseResponse);
        return baseResponse;
    }


    public BaseResponse updateMerchantName(long userId, long topCommId, String userName, long commId) {
        BaseResponse baseResponse = userFeignClient.updateMerchantName(userId, topCommId, userName, commId);
        FeignResponseValidate.check(baseResponse);
        return baseResponse;
    }


    public BaseResponse updateMerchantBalance(long userId, Long topCommId, long subCommId, BigDecimal amount, TransTypeEnum transType, String reason, String remark) {
        BaseResponse baseResponse = userFeignClient.updateMerchantBalance(userId, topCommId, subCommId, amount, transType, reason, remark);
        if (baseResponse.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
            throw new DcServiceException("充值失败");//测试要求提示信息改成"充值失败"
        }
        return baseResponse;
    }


    /**
     * 查询商户会员详情信息
     *
     * @param userId
     * @param commId
     * @return
     */

    public ObjectResponse<PointPoVo> findMerchantBalance(long userId, Long topCommId, long commId) {
        log.info("查询账户详情 userId: {}, commId", userId, commId);

        ObjectResponse<CommCusRef> commCusRefResponse = findCommCusRef(userId, commId);
        CommCusRef commCusRef = commCusRefResponse.getData();

        List<Long> uidList = new ArrayList<>();
        uidList.add(userId);

        ListResponse<PointPo> pointPoListResponse = dcCusBalanceService.queryPointPo(PayAccountType.COMMERCIAL, uidList, topCommId, commId, true, null);

        FeignResponseValidate.check(pointPoListResponse);

        PointPoVo pointPoVo = new PointPoVo();
        pointPoVo.setId(commCusRef.getId());
        pointPoVo.setUserId(commCusRef.getUserId());
        pointPoVo.setUserName(commCusRef.getUserName());
        pointPoVo.setUserPhone(commCusRef.getUserPhone());
        pointPoVo.setCommercialId(commCusRef.getCommId());
        pointPoVo.setCommercialName(commCusRef.getCommName());
        pointPoVo.setCommercialPhone(commCusRef.getCommPhone());
        pointPoVo.setCommLevel(commCusRef.getCommLevel());
        pointPoVo.setCreateTime(commCusRef.getCreateTime());
        pointPoVo.setUpdateTime(commCusRef.getUpdateTime());

        List<PointPo> pointPoList = pointPoListResponse.getData();

        if (pointPoList == null || pointPoList.size() == 0) {
            throw new DcServiceException("没有查询到对应的商户会员信息");
        }

        for (PointPo pointPo : pointPoList) {
            if (pointPo.getUid().equals(commCusRef.getUserId().toString())) {
                pointPoVo.setPoint(pointPo.getPoint());
                pointPoVo.setAvailable(pointPo.getAvailable());
                pointPoVo.setUsed(pointPo.getUsed());
                pointPoVo.setFrozen(pointPo.getFrozen());
                pointPoVo.setCost(pointPo.getCost());
                pointPoVo.setFrozenCost(pointPo.getFrozenCost());
                pointPoVo.setExpire(pointPo.getExpire());
                //pointPoVo.setIsDefault(pointPo.getIsDefault());
                pointPoVo.setEnable(pointPo.getEnable() ? 1 : 0);
            }
        }

        log.info("查询账户列表结果-----------{}", pointPoVo);
        return new ObjectResponse<>(pointPoVo);
    }

    /**
     * 根据用户id和所属商户id查询商户客户关系表
     *
     * @param userId
     * @param commId
     * @return
     */
    public ObjectResponse<CommCusRef> findCommCusRef(long userId, long commId) {
        ListResponse<CommCusRef> commCusRefList = userFeignClient.queryCommCusRefs(null, null, null, null, commId, userId, null);
        FeignResponseValidate.check(commCusRefList);
        CommCusRef commCusRef = commCusRefList.getData().get(0);
        return new ObjectResponse<>(commCusRef);
    }

    /**
     * 查询商户会员的信息
     *
     * @param uid
     * @return Balance
     */

    public ObjectResponse<AccountInfoVo> getCommercialAccountByUser(Long topCommId, String commIdChain, Long uid, Long commId) {

        log.info("查询商户会员的信息uid:{},commId:{}", uid, commId);

//        if (commId != null && commId.longValue() > 0L) {
//            // 如果有传入商户ID， 则使用指定的商户
//            ObjectResponse<Commercial> commRes = this.commercialFeignClient.getCommercial(commId);
//            FeignResponseValidate.check(commRes);
//            commIdChain = commRes.getData().getIdChain();
//        }
        if (uid == null) {
            throw new DcArgumentException("参数错误");
        }

        //商户会员
        ListResponse<PointPoVo> commercialAccountRes = dcUserService.getCommercialBalanceByUid(topCommId, commIdChain, uid//, Arrays.asList(commId)
        );
        if (commercialAccountRes == null || commercialAccountRes.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            throw new DcServiceException("查询用户下商户会员失败");
        }

        List<AccountInfoVo> accountInfoVoList = new ArrayList<>();

        AccountInfoVo acc = null;
        if (!CollectionUtils.isEmpty(commercialAccountRes.getData())) {
            for (PointPoVo pointPoVo : commercialAccountRes.getData()) {
                AccountInfoVo accountInfoVo = new AccountInfoVo();
                accountInfoVo.setPayAccountId(pointPoVo.getCommercialId());
                accountInfoVo.setDefaultPayType(String.valueOf(OrderPayType.MERCHANT.getCode()));
                accountInfoVo.setAccountName("商户会员" + "-" + pointPoVo.getCommercialName());

                accountInfoVo.setAvailableAmount(null == pointPoVo.getAvailable() ? BigDecimal.ZERO : pointPoVo.getAvailable());//可用余额
                accountInfoVo.setAmount(null == pointPoVo.getPoint() ? BigDecimal.ZERO : pointPoVo.getPoint());//总余额
                accountInfoVo.setFrozenAmount(null == pointPoVo.getFrozen() ? BigDecimal.ZERO : pointPoVo.getFrozen());//冻结金额
                accountInfoVo.setCostAmount(null == pointPoVo.getCost() ?
                        BigDecimal.ZERO : pointPoVo.getCost().subtract(
                        (null == pointPoVo.getFrozenCost() ? BigDecimal.ZERO : pointPoVo.getFrozenCost())));//实际成本(实际余额)
                accountInfoVo.setFreeAmount(accountInfoVo.getAvailableAmount().subtract(accountInfoVo.getCostAmount()));//赠送余额 = 可用余额 - 实际成本（实际余额）
                accountInfoVo.setFrozenCostAmount(null == pointPoVo.getFrozenCost() ? BigDecimal.ZERO : pointPoVo.getFrozenCost());//冻结实际金额
                accountInfoVo.setFrozenFreeAmount(accountInfoVo.getFrozenAmount().subtract(accountInfoVo.getFrozenCostAmount()));//冻结赠送金额 = 冻结金额 - 冻结实际金额

                accountInfoVo.setUserId(uid);
                accountInfoVoList.add(accountInfoVo);

                if (accountInfoVo.getPayAccountId().longValue() == commId) {
                    acc = accountInfoVo;
                }
            }
        }

        ObjectResponse<AccountInfoVo> res = new ObjectResponse<>(acc);

        log.info("查询商户会员的信息结果:{}", res);

        return res;
    }

    public ObjectResponse<Long> countByCondition(@RequestBody CommCusRef param) {
        return userFeignClient.countByCondition(param);
    }

}
