package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.cus.param.CustomerAttractListParam;
import com.cdz360.biz.model.trading.sync.vo.CustomerAttractBiVo;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class DataCoreAttractFeignHystrix implements FallbackFactory<DataCoreAttractFeignClient> {


    @Override
    public DataCoreAttractFeignClient apply(Throwable throwable) {
        return new DataCoreAttractFeignClient() {
            @Override
            public Mono<ObjectResponse<CustomerAttractBiVo>> getCustomerAttractBi(
                CustomerAttractListParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = getCustomerAttractBi (获取引流汇总数据), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<CustomerAttractBiVo>> getAttractBiList(
                CustomerAttractListParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = includeChargerOrderList (获取已申请企业开票的充电订单列表), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }
        };
    }

    @NotNull
    @Override
    public <V> Function<V, DataCoreAttractFeignClient> compose(
        @NotNull Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
        return null;
    }

    @NotNull
    @Override
    public <V> Function<Throwable, V> andThen(
        @NotNull Function<? super DataCoreAttractFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
        return null;
    }
}
