package com.cdz360.biz.ant.utils;

import com.cdz360.base.model.base.exception.DcServiceException;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.IOException;
import java.io.InputStream;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * excel解析
 * <AUTHOR>
 */
@Slf4j
public class ExcelBatchUtil {

    /**
     * 企业卡excel
     * @param in
     * @param fileName
     * @return
     */
    public static List<List<Object>> getCardListByExcelOnCorp(InputStream in, String fileName) {
        log.info(">> parse excel: file name={}", fileName);

        // 创建excel工作簿
        Workbook work = getWorkbook(in, fileName);
//        log.info("work: {}", JsonUtils.toJsonString(work));
        try {
            if (null == work) {
                throw new Exception("创建Excel工作薄为空！");
            }
        } catch (Exception e) {
            throw new DcServiceException("系统异常");
        }

        Sheet sheet = null;
        Row row = null;
        Cell cell = null;

        List<List<Object>> list = new ArrayList<>();
        log.info("work.getNumberOfSheets(): {}", work.getNumberOfSheets());
        for (int i = 0; i < work.getNumberOfSheets(); i++) {

            sheet = work.getSheetAt(i);
//            log.info("sheet: {}", JsonUtils.toJsonString(sheet));
            if(sheet == null) {
                continue;
            }
            // 需求：从第4行到10003行认为是有效数据
            // 有效值从0开始，所以不能大于10002
            if (sheet.getLastRowNum() > 5002)
                throw new DcServiceException("导入的卡数量最大不能超过5000条");
            int startCol = 0;
            int endCol = 0;
            log.info("sheet.getFirstRowNum(): {}", sheet.getFirstRowNum());
            log.info("sheet.getLastRowNum(): {}", sheet.getLastRowNum());
            for (int j = 3; j <= sheet.getLastRowNum(); j++) {
                row = sheet.getRow(j);
//                log.info("row: {}", row);
                if (isEmptyRow(row))
                    continue;
                startCol = row.getFirstCellNum();
                endCol = row.getLastCellNum();
                List<Object> li = new ArrayList<>();
//                log.info("row.getFirstCellNum(): {}", row.getFirstCellNum());
//                log.info("row.getLastCellNum(): {}", row.getLastCellNum());
                for (int y = startCol; y < endCol; y++) {
                    cell = row.getCell(y);
//                    log.info("cell: {}", cell);
                    li.add(cellHandle(cell));
                }
                if(isObjectEmpty(li.get(0))&&
                        isObjectEmpty(li.get(1))&&
                        isObjectEmpty(li.get(2)))
                    continue;
//                log.info("li: {}", li);
                list.add(li);
            }
        }

//        work.close();

        log.info(">> parse excel end.");
        return list;
    }

    public static Object cellHandle(Cell cell) {
        if (null != cell && cell.getCellType() == HSSFCell.CELL_TYPE_NUMERIC) {
            DecimalFormat df = new DecimalFormat("0");
            return df.format(cell.getNumericCellValue());
        }
        return cell;
    }

    /**
     * 企业VIN码excel
     * @param in
     * @param fileName
     * @return
     */
    public static List<List<Object>> getVinListByExcelOnCorp(InputStream in, String fileName) {
        log.info(">> parse excel: file name={}", fileName);

        // 创建excel工作簿
        Workbook work = getWorkbook(in, fileName);
//        log.info("work: {}", JsonUtils.toJsonString(work));
        try {
            if (null == work) {
                throw new Exception("创建Excel工作薄为空！");
            }
        } catch (Exception e) {
            throw new DcServiceException("系统异常");
        }

        Sheet sheet = null;
        Row row = null;
        Cell cell = null;

        List<List<Object>> list = new ArrayList<>();
        log.info("work.getNumberOfSheets(): {}", work.getNumberOfSheets());
        for (int i = 0; i < work.getNumberOfSheets(); i++) {

            sheet = work.getSheetAt(i);
//            log.info("sheet: {}", JsonUtils.toJsonString(sheet));
            if(sheet == null) {
                continue;
            }
            // 需求：从第4行到10003行认为是有效数据
            // 有效值从0开始，所以不能大于10002
            if (sheet.getLastRowNum() > 5002)
                throw new DcServiceException("导入的VIN码数量最大不能超过5000条");
            int startCol = 0;
            int endCol = 0;
            log.info("sheet.getFirstRowNum(): {}", sheet.getFirstRowNum());
            log.info("sheet.getLastRowNum(): {}", sheet.getLastRowNum());
            for (int j = 3; j <= sheet.getLastRowNum(); j++) {
                row = sheet.getRow(j);
//                log.info("row: {}", row);
                if (isEmptyRow(row))
                    continue;
                startCol = row.getFirstCellNum();
                endCol = row.getLastCellNum();
                List<Object> li = new ArrayList<>();
//                log.info("row.getFirstCellNum(): {}", row.getFirstCellNum());
//                log.info("row.getLastCellNum(): {}", row.getLastCellNum());
                for (int y = startCol; y < endCol; y++) {
                    cell = row.getCell(y);
//                    log.info("cell: {}", cell);
                    li.add(cellHandle(cell));
                }
                if(isObjectEmpty(li.get(0))&&
                        isObjectEmpty(li.get(1))&&
                        isObjectEmpty(li.get(2)))
                    continue;
//                log.info("li: {}", li);
                list.add(li);
            }
        }

//        work.close();

        log.info(">> parse excel end.");
        return list;
    }

    /**
     * 课程excel
     * @param in
     * @param fileName
     * @return
     */
    public static List<List<Object>> getCardListByExcel(InputStream in, String fileName) {
        log.info(">> parse excel: file name={}", fileName);

        // 创建excel工作簿
        Workbook work = getWorkbook(in, fileName);
//        log.info("work: {}", JsonUtils.toJsonString(work));
        try {
            if (null == work) {
                throw new Exception("创建Excel工作薄为空！");
            }
        } catch (Exception e) {
            throw new DcServiceException("系统异常");
        }

        Sheet sheet = null;
        Row row = null;
        Cell cell = null;

        List<List<Object>> list = new ArrayList<>();
        log.info("work.getNumberOfSheets(): {}", work.getNumberOfSheets());
        for (int i = 0; i < work.getNumberOfSheets(); i++) {

            sheet = work.getSheetAt(i);
//            log.info("sheet: {}", JsonUtils.toJsonString(sheet));
            if(sheet == null) {
                continue;
            }
            if (sheet.getLastRowNum() > 10000)
                throw new DcServiceException("导入的卡数量最大不能超过1万条");

            // 滤过第一行标题
            int startCol = 0;
            int endCol = 0;
            log.info("sheet.getFirstRowNum(): {}", sheet.getFirstRowNum());
            log.info("sheet.getLastRowNum(): {}", sheet.getLastRowNum());
            boolean actualStart = false;
            for (int j = sheet.getFirstRowNum(); j <= sheet.getLastRowNum(); j++) {
                row = sheet.getRow(j);
                //log.info("row: {}", row);
                if (isEmptyRow(row))
                    continue;
                //log.info("sheet.getFirstRowNum(): {}", sheet.getFirstRowNum());
                // 空行过滤，第一非空行需要记录开始列和结束列位置值
                if (row == null || !actualStart) {
                    if (null != row) {
                        actualStart = true;

                        startCol = row.getFirstCellNum();
                        endCol = row.getLastCellNum();
                        //log.info("row.getFirstCellNum(): {}", row.getFirstCellNum());
                        //log.info("row.getLastCellNum(): {}", row.getLastCellNum());
                    }
                    continue;
                }

                List<Object> li = new ArrayList<>();
                //log.info("row.getFirstCellNum(): {}", row.getFirstCellNum());
                //log.info("row.getLastCellNum(): {}", row.getLastCellNum());
                for (int y = startCol; y < endCol; y++) {
                    cell = row.getCell(y);
                    //log.info("cell: {}", cell);
                    li.add(cell);
                }
                if (isObjectEmpty(li.get(0)) &&
                        isObjectEmpty(li.get(1)) &&
                        isObjectEmpty(li.get(2)))
                    continue;
                list.add(li);
            }
        }

//        work.close();

        log.info(">> parse excel end.");
        return list;
    }

    public static Boolean isObjectEmpty(Object obj){
        if (obj == null)
            return true;
        if (obj == "")
            return true;
        if ((obj instanceof List)) {
            return ((List) obj).size() == 0;
        }
        if ((obj instanceof String)) {
            return ((String) obj).trim().equals("");
        }
        return false;
    }

    /**
     * 判断文件格式
     * @param in
     * @param fileName
     * @return
     */
    private static Workbook getWorkbook(InputStream in, String fileName) {
        Workbook book = null;
        String fileType = fileName.substring(fileName.lastIndexOf("."));
        try {
            if (".xls".equals(fileType)) {
                book = new HSSFWorkbook(in);
            } else if (".xlsx".equals(fileType)) {
                book = new XSSFWorkbook(in);
            } else {
                throw new Exception("上传 excel 文件后缀不正确.");
            }
            in.close();
        } catch (IOException e) {
            throw new DcServiceException("导入失败");
        } catch (Exception e) {
            throw new DcServiceException("导入失败");
        }
        return book;
    }

    /**
     *  判断是否为空行
     * @param row
     * @return
     */
    private static Boolean isEmptyRow(Row row) {
        if (row == null || row.toString().isEmpty()) {
            return true;
        } else {
            Iterator<Cell> it = row.iterator();
            boolean emptyFlag = true;
            while (it.hasNext()) {
                Cell cell = it.next();
                if (cell.getCellType() == Cell.CELL_TYPE_BLANK) {
                    emptyFlag = true;
                } else {
                    emptyFlag = false;
                    break;
                }
                return emptyFlag;
            }
        }
        return false;
    }
}
