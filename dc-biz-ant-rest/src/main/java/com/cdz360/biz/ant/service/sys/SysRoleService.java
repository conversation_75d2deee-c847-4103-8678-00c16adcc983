package com.cdz360.biz.ant.service.sys;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.auth.sys.param.BatchAddRoleUserParam;
import com.cdz360.biz.auth.sys.param.RoleUserListParam;
import com.cdz360.biz.auth.sys.param.RoleUserUpdateParam;
import com.cdz360.biz.auth.sys.vo.RoleUserVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SysRoleService {
    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    public BaseResponse batchUpdateRoleUser(RoleUserUpdateParam params) {
        return authCenterFeignClient.batchUpdateRoleUser(params);
    }

    public BaseResponse batchAddRoleUser(BatchAddRoleUserParam params) {
        return authCenterFeignClient.batchAddRoleUser(params);
    }

    public ListResponse<RoleUserVo> getUserByRoleId( String keyWord, Long platform, Long roleId, Long size){
        return authCenterFeignClient.getUserByRoleId(keyWord,platform,roleId,size);
    }

    public ListResponse<RoleUserVo> getUserListByRoleId( RoleUserListParam params){
        return authCenterFeignClient.getUserListByRoleId(params);
    }
}
