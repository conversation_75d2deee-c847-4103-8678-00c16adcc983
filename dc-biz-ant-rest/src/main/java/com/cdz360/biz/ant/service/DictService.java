package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.chargerlinkcar.framework.common.domain.Dict;
import com.chargerlinkcar.framework.common.domain.vo.PictureConfig;
import com.chargerlinkcar.framework.common.feign.DictFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class DictService //implements IDictService
{

    @Autowired
    private DictFeignClient dictFeignClient;


    public ObjectResponse<Dict> get(Long id) {
        //获取数据库数据
        ObjectResponse<Dict> res = dictFeignClient.get(id);
        FeignResponseValidate.check(res);
        return res;
    }


    public ListResponse<Dict> findList(Dict dict) {
        //获取数据库数据
        ListResponse<Dict> res = dictFeignClient.findList(dict);
        FeignResponseValidate.check(res);
        return res;
    }


    public BaseResponse save(Dict dict) {
        //保存数据库记录
        BaseResponse jsonObjectRes = dictFeignClient.insert(dict);
        FeignResponseValidate.check(jsonObjectRes);
        return jsonObjectRes;

    }


    public BaseResponse update(Dict dict) {
        BaseResponse jsonObjectRes = dictFeignClient.update(dict);
        FeignResponseValidate.check(jsonObjectRes);
        return jsonObjectRes;

    }


    public BaseResponse delete(Dict dict) {
        //删除数据库记录
        BaseResponse jsonObjectRes = dictFeignClient.delete(dict);
        FeignResponseValidate.check(jsonObjectRes);
        return jsonObjectRes;

    }


    public ListResponse<Dict> queryPage(Dict dict) {
        //根据条件查询数据库
        ListResponse<Dict> res = dictFeignClient.queryPage(dict);
        FeignResponseValidate.check(res);
        return res;
    }


    public ObjectResponse<PictureConfig> getPictureConfig() {
        ObjectResponse<PictureConfig> jsonObjectRes = dictFeignClient.getPictureConfig();
        return jsonObjectRes;

    }


    public ListResponse<Dict> findDictDataByType(String type) {
        ListResponse<Dict> res = dictFeignClient.findDictDataByType(type);
        FeignResponseValidate.check(res);
        return res;
    }
}
