package com.cdz360.biz.ant.feign;


import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.cus.user.dto.WhiteCardDto;
import com.cdz360.biz.model.trading.bi.param.WarningSummaryParam;
import com.cdz360.biz.model.trading.bi.warning.WarningSummaryDto;
import com.chargerlinkcar.framework.common.domain.vo.TemplateInfoVo;
import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeviceMonitorClientHystrixFactory implements FallbackFactory<DeviceMonitorFeignClient> {

    @Override
    public DeviceMonitorFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_IOT_MONITOR, throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_IOT_MONITOR, throwable.getStackTrace());

        return new DeviceMonitorFeignClient() {


            @Override
            public ListResponse<WarningSummaryDto> getWarningSummaryList(WarningSummaryParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

        };
    }
}

