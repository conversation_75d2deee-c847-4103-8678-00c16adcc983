package com.cdz360.biz.ant.service.ess;


import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.biz.ess.model.param.ListEssBatteryClusterParam;
import com.cdz360.biz.ess.model.vo.EssEquipBatteryClusterVo;
import com.cdz360.biz.model.ess.param.ListEssEquipParam;
import com.cdz360.biz.model.ess.po.EssPo;
import com.cdz360.biz.model.ess.vo.EssEquipBatteryPackVo;
import com.cdz360.biz.model.ess.vo.EssEquipBatteryStackVo;
import com.cdz360.biz.model.ess.vo.EssEquipVo;
import com.cdz360.biz.model.iot.param.ListCtrlParam;
import com.cdz360.biz.ess.model.dto.EssDto;
import com.cdz360.biz.model.trading.ess.param.ListEssBatteryPackParam;
import com.cdz360.biz.ess.model.param.ListEssParam;
import com.cdz360.biz.model.trading.ess.po.EssEquipPo;
import com.cdz360.biz.model.trading.ess.vo.EssEquipBatteryPackSimpleVo;
import com.cdz360.biz.model.trading.ess.vo.EssEquipPCSVo;
import com.cdz360.biz.model.trading.ess.vo.EssVo;
import com.cdz360.biz.model.trading.iot.vo.EssDataInTimeVo;
import com.cdz360.biz.utils.feign.iot.DeviceFeignClient;
import com.cdz360.biz.utils.feign.iot.IotEssFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;


@Slf4j
@Service
public class EssBizService {

    @Autowired
    private IotEssFeignClient iotEssFeignClient;

    @Autowired
    private DeviceFeignClient deviceFeignClient;

    public Mono<BaseResponse> modifyEssCfg(String dnoIn, Long cfgId) {
        return iotEssFeignClient.sendModifyEssCfgCmd(dnoIn, cfgId);
    }

    public Mono<ObjectResponse<EssPo>> getById(Long id) {
        return deviceFeignClient.getById(id);
    }

    public Mono<ListResponse<EssDto>> getByGwno(ListCtrlParam param) {
        return deviceFeignClient.getByGwno(param);
    }

    public Mono<BaseResponse> syncEssEquip(String dno) {
        return iotEssFeignClient.syncEssEquip(dno);
    }


    public Mono<ObjectResponse<EssDataInTimeVo>> essInfoInTime(String dno) {
        return deviceFeignClient.essInfoInTime(dno);
    }

    /**
     * 光出ESS列表
     *
     * @param param
     * @return
     */
    public Mono<ListResponse<EssVo>> findEssList(ListEssParam param) {
        return deviceFeignClient.findEssList(param);
    }

    /**
     * 获取光储Ess下挂载的所有设备
     *
     * @param param
     * @return
     */
    public Mono<ListResponse<EssEquipVo>> findEquipList(ListCtrlParam param) {
        return deviceFeignClient.findEquipList(param);
    }

    /**
     * 查询站点下获取光储Ess下挂载的所有设备
     *
     * @param siteId
     * @return
     */
    public Mono<ListResponse<EssEquipPo>> findEquipListBySiteId(@NonNull String siteId,
        @Nullable EssEquipType equipType) {
        return deviceFeignClient.findEquipListBySiteId(siteId, equipType);
    }


    /**
     * 光储ESS PCS列表
     *
     * @param param
     * @return
     */
    public Mono<ListResponse<EssEquipPCSVo>> findEquipPCSList(ListEssEquipParam param) {
        return deviceFeignClient.findEquipPCSList(param);
    }

    /**
     * 光储ESS 电池堆列表
     *
     * @param param
     * @return
     */
    public Mono<ListResponse<EssEquipBatteryStackVo>> findEquipBatteryStackList(
        ListEssEquipParam param) {
        return deviceFeignClient.findEquipBatteryStackList(param);
    }

    /**
     * 光储ESS 电池组列表
     *
     * @param param
     * @return
     */
    public Mono<ListResponse<EssEquipBatteryPackVo>> findEquipBatteryPackList(
        ListEssBatteryPackParam param) {
        return deviceFeignClient.findEquipBatteryPackList(param);
    }

    /**
     * 光储ESS 电池簇列表
     *
     * @param param
     * @return
     */
    public Mono<ListResponse<EssEquipBatteryClusterVo>> findEquipBatteryClusterList(
        ListEssBatteryClusterParam param) {
        return deviceFeignClient.findEquipBatteryClusterList(param);
    }

    /**
     * 根据电池堆获取电池簇列表
     *
     * @param essDno
     * @param batteryStackId
     * @return
     */
    public Mono<ListResponse<Long>> findBatteryClusterNosByBatteryStack(String essDno,
        Long batteryStackId) {
        return deviceFeignClient.findBatteryClusterNosByBatteryStack(essDno, batteryStackId);
    }

    /**
     * 根据电池簇号获取电池组列表
     *
     * @param essDno
     * @param batteryStackId
     * @param batteryClusterNo
     * @return
     */
    public Mono<ListResponse<EssEquipBatteryPackSimpleVo>> findBatteryPackNosByBatteryClusterNo(
        String essDno, Long batteryStackId, Long batteryClusterNo) {
        return deviceFeignClient.findBatteryPackNosByBatteryClusterNo(essDno, batteryStackId,
            batteryClusterNo);
    }

    /**
     * 根据essDno获取电池组列表
     *
     * @param param
     * @return
     */
    public Mono<ListResponse<EssEquipBatteryPackSimpleVo>> findBatteryPackNosByBatteryClusterNo(
        ListEssBatteryPackParam param) {
        return deviceFeignClient.findBatteryPackSimpleVoList(param);
    }

}
