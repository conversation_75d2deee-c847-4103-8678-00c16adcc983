package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.trading.hlht.po.PartnerPo;
import com.cdz360.biz.model.trading.hlht.po.PartnerSitePo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

/**
 *
 * @since 3/29/2020 9:42 PM
 * <AUTHOR>
 */
@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_OPEN_HLHT,
        fallbackFactory = ReactorOpenHlhtFeignClientHystrixFactory.class)
public interface ReactorOpenHlhtFeignClient {
    @GetMapping(value = "/open/partner/client/getSiteListByCode")
    Mono<ListResponse<PartnerSitePo>> getSiteListByCode(@RequestParam(value = "partnerCode") String code);

    @GetMapping(value = "/open/partner/client/getDetailByCode")
    Mono<ObjectResponse<PartnerPo>> getDetailByCode(@RequestParam(value = "code") String code);
}