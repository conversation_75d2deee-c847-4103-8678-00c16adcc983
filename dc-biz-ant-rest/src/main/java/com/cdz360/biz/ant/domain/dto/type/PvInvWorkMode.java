package com.cdz360.biz.ant.domain.dto.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum PvInvWorkMode implements DcEnum {

    UNKNOWN(999),
    /**
     * 逆变器等待符合发电的条件
     */
    WAIT(0),
    /**
     * 逆变器正在发电
     */
    NORMAL(1),
    /**
     * 系统异常，同时停止发电
     */
    FAULT(2),
    /**
     * 系统严重异常， 20 秒后逆变器会重启。进入此状态的条件如下：
     * 1.Grid current DC offset
     * 2.Eeprom cannot be read or write in
     * 3.Communication between CPU failure
     * 4.Bus Voltage too high
     * 5.Compare measured values from two CPU
     * 6.relay check fail
     * 7.GFCI Device check fail
     * 8.HCT check fail
     */
    PERMANENT_FAULT(3),
    ;

    @JsonValue
    private int code;

    PvInvWorkMode(int code) {
        this.code = code;
    }

    @JsonCreator
    public static PvInvWorkMode valueOf(Object codeIn) {
        if (codeIn == null) {
            return PvInvWorkMode.UNKNOWN;
        }
        int code = 0;
        if (codeIn instanceof PvInvWorkMode) {
            return (PvInvWorkMode) codeIn;
        } else if (codeIn instanceof Integer) {
            code = ((Integer) codeIn).intValue();
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }
        for (PvInvWorkMode type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return PvInvWorkMode.UNKNOWN;
    }
}
