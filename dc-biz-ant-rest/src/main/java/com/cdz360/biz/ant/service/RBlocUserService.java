package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.feign.AntUserFeignClient;
import com.cdz360.biz.ant.feign.MerchantFeignClient;
import com.cdz360.biz.utils.feign.user.UserFeignClient;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUserVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @since 2018/12/10.
 */
@Slf4j
@Service
public class RBlocUserService //implements RBlocUserService
{


    public final static String CODE = "0";
    @Autowired
    private MerchantFeignClient merchantFeignClient;
    @Autowired
    private AntUserFeignClient userFeignClient;
    @Autowired
    private UserFeignClient asyncUserFeignClient;

    /**
     * 根据集团id查询所属集团的客户列表
     *
     * @param blocUserId
     * @return
     */

    public ListResponse<Long> findRBlocUserByBlocUserId(Long blocUserId) {
        ListResponse<Long> jsonObjectRes = userFeignClient.findRBlocUserByBlocUserId(blocUserId);
        return jsonObjectRes;

    }

    /**
     * 更新集团用户信息
     * 该接口仅修改授信额度
     *
     * @param rBlocUser
     * @return
     */

//    public BaseResponse updateRBlocUser(RBlocUser rBlocUser) {
//        log.info("ant服务钱---更新集团用户信息原始数据：{}", JsonUtils.toJsonString(rBlocUser));
////        rBlocUser.setFrozenAmount(null);    // 不应该更新当前冻结的金额
////        rBlocUser.setBalance(null); // 该接口仅修改授信额度
//        BaseResponse jsonObjectRes = userFeignClient.updateRBlocUser(rBlocUser);
//        return jsonObjectRes;
//    }

    /**
     * 禁用集团授信关系
     *
     * @param rBlocUserId
     * @return
     */

//    public BaseResponse deleteRBlocUserById(Long rBlocUserId) {
//        BaseResponse jsonObjectRes = userFeignClient.deleteRBlocUserById(rBlocUserId);
//        return jsonObjectRes;
//
//    }

    /**
     * 集团客户id查询集团客户详情
     *
     * @param rBlocUserId
     * @return
     */

    public ObjectResponse<RBlocUser> findRBlocUserById(Long rBlocUserId) {
        ObjectResponse<RBlocUser> jsonObjectRes = userFeignClient.findRBlocUserById(rBlocUserId);
        return jsonObjectRes;
    }

    /**
     * 查询所属集团下的客户列表
     *
     * @param blocUserId
     * @param keyWord
     * @param page
     * @param commIdChain 商户, 子商户ID列表
     * @return
     */

    public ListResponse<com.chargerlinkcar.framework.common.domain.vo.RBlocUser> queryRBlocUser(
        Long blocUserId, String keyWord,
        OldPageParam page,
        String commIdChain) {

        // 根据条件查询数据库
        ListResponse<com.chargerlinkcar.framework.common.domain.vo.RBlocUser> jsonObjectRes = userFeignClient.queryRBlocUser(
            page.getPageNum(),
            page.getPageSize(), keyWord, blocUserId, commIdChain);
        log.info("查询所属集团下的客户列表{}", jsonObjectRes.getData());
        return jsonObjectRes;
    }

    public Mono<ObjectResponse<RBlocUserVo>> getCorpUserWithAccountInfo(Long corpUserId) {
        return asyncUserFeignClient.findRBlocUserVoById(corpUserId);
    }

//    /**
//     * 批量删除集团中的客户信息
//     *
//     * @param rBlocUserIds
//     * @return
//     */
//
//    public BaseResponse deleteBatchRBlocUser(List<Long> rBlocUserIds) {
//        BaseResponse jsonObjectRes = userFeignClient.deleteBatchRBlocUser(rBlocUserIds);
//        if (jsonObjectRes.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
//            throw new DcServiceException("请求失败");
//        }
//        return jsonObjectRes;
//
//    }
}
