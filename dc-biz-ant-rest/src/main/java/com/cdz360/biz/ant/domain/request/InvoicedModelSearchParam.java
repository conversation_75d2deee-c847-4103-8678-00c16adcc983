package com.cdz360.biz.ant.domain.request;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.biz.model.invoice.type.InvoiceType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * InvoicedModelSearchParam
 * 
 * @since 4/12/2023 11:07 AM
 * <AUTHOR>
 */

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class InvoicedModelSearchParam extends BaseListParam {
    private Long userId;
    private InvoiceType invoiceType;
}