package com.cdz360.biz.ant.domain.vo;

import com.cdz360.biz.model.trading.order.dto.CusOrderBiDto;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CusOrderBiVo extends CusOrderBiDto {

    /**
     * 商户Id
     */
    private Long commId;

    private Long topCommId;

    /**
     *
     */
    private String username;

    /**
     *
     */
    private String name;

    /**
     * 手机号
     */
    private String phone;

    @Schema(description = "邮箱")
    private String email;

    /**
     * 应用来源(1.APP,2.微信 3.平台 4.支付宝,6.绑定卡片时注册)
     */
    private Integer sourceId;

    /**
     * 注册时间
     */
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date regTime;


    @Schema(description = "账户余额", example = "123.45")
    private BigDecimal balance;

    @Schema(description = "客户状态", example = "10001")
    private Integer status;

    @Schema(description = "客户头像", example = "http://aaa.bbb/img.jpg")
    private String avatar;

    @Schema(description = "欠费状态")
    private Boolean debt;

    @Schema(description = "最近一次站点名称", example = "XXX站点")
    private String siteName;

    @Schema(description = "站点Id", example = "1234567")
    private String siteId;

    @Schema(description = "未结算订单数", example = "5")
    private Integer unliquidatedOrderNum;

    @Schema(description = "车牌号")
    private List<String> carNoList;

    @Schema(description = "引流人")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String referrer;

}
