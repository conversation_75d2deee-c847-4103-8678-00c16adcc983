package com.cdz360.biz.ant.rest.site;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.aop.CheckToken;
import com.cdz360.biz.ant.domain.vo.SiteSimpleInfoVo;
import com.cdz360.biz.ant.service.site.SiteService;
import com.cdz360.biz.ant.service.sysLog.SiteSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.discount.param.DiscountServiceParam;
import com.cdz360.biz.model.discount.vo.SiteDiscountVo;
import com.cdz360.biz.model.site.param.AddSiteParam;
import com.cdz360.biz.model.site.param.ListSiteBaseParam;
import com.cdz360.biz.model.site.param.UpdateSiteParam;
import com.cdz360.biz.model.site.po.PriceTemplatePo;
import com.cdz360.biz.model.site.type.SiteCategory;
import com.cdz360.biz.model.site.type.SiteStatus;
import com.cdz360.biz.model.trading.camera.param.CameraRecorderParam;
import com.cdz360.biz.model.trading.camera.po.CameraSitePo;
import com.cdz360.biz.model.trading.camera.vo.CameraRecorderVo;
import com.cdz360.biz.model.trading.hlht.vo.HlhtSiteVo;
import com.cdz360.biz.model.trading.site.dto.SiteSimpleDto;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.site.vo.MoveCorpNoCardList;
import com.cdz360.biz.model.trading.site.vo.MoveCorpNoCardVo;
import com.cdz360.biz.model.trading.site.vo.SiteChargePriceVo;
import com.cdz360.biz.model.trading.site.vo.SiteConfStartList;
import com.cdz360.biz.model.trading.site.vo.SiteNoVo;
import com.cdz360.biz.model.trading.site.vo.SiteQrCodeVo;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import com.cdz360.biz.model.trading.soc.vo.MoveCorpUserSocStrategyList;
import com.cdz360.biz.utils.feign.camera.CameraFeignClient;
import com.cdz360.biz.utils.feign.hlht.OpenHlhtFeignClient;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.domain.BlocUserDto;
import com.chargerlinkcar.framework.common.domain.ChangeInfo;
import com.chargerlinkcar.framework.common.domain.PlugStatusCountDto;
import com.chargerlinkcar.framework.common.domain.SiteDetailInfoVo;
import com.chargerlinkcar.framework.common.domain.SitePersonaliseDTO;
import com.chargerlinkcar.framework.common.domain.SiteSocLimitDto;
import com.chargerlinkcar.framework.common.domain.param.SiteListRequest;
import com.chargerlinkcar.framework.common.domain.request.SiteGeoListRequest;
import com.chargerlinkcar.framework.common.domain.vo.CommCusRef;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.domain.vo.SiteCardAccountVo;
import com.chargerlinkcar.framework.common.domain.vo.SiteDebitAccountVo;
import com.chargerlinkcar.framework.common.domain.vo.SiteInMongoVo;
import com.chargerlinkcar.framework.common.domain.vo.SiteSelectInfoVo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.AssertUtil;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.Duration;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * 站点相关接口
 *
 * <AUTHOR>
 * @since Created on 19:06 2019/4/22.
 */
@Slf4j
@RequestMapping("/api/site")
@RestController
@Tag(name = "站点操作相关接口")
public class SiteRest extends BaseController {

    // 以下常量命名和前端保持一致
    private static final Integer SITE_SETTING_1 = 1;
    private static final Integer SITE_SETTING_2 = 2;
    private static final Integer SITE_SETTING_3 = 3;
    private static final Integer SITE_SETTING_4 = 4;
    private static final Integer SITE_SETTING_5 = 5;
    private static final Integer SITE_SETTING_6 = 6;

    @Autowired
    private SiteService siteService;


    @Autowired
    private SiteSysLogService siteSysLogService;

    @Autowired
    private CameraFeignClient cameraFeignClient;

    @Autowired
    private OpenHlhtFeignClient openHlhtFeignClient;

    @Operation(summary = "场站服务费基础计费详情")
    @PostMapping(value = "/discountServiceFee")
    public ListResponse<SiteDiscountVo> discountServiceFee(
        ServerHttpRequest request, @RequestBody DiscountServiceParam param) {
        log.info("场站服务费基础计费详情: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return siteService.discountServiceFee(param);
    }

    @Operation(summary = "新增场站")
    @PostMapping("/addSite")
    public ObjectResponse<SitePo> addSite(ServerHttpRequest request,
        @RequestBody AddSiteParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + "param = {}", param);
        Assert.isTrue(StringUtils.isNotBlank(param.getSiteName()), "站点名称不能为空");
        Assert.notNull(param.getLongitude(), "站点经度不能为空");
        Assert.notNull(param.getLatitude(), "站点纬度不能为空");
        Assert.notNull(param.getAddress(), "站点地址不能为空");
        Assert.notNull(param.getProvince(), "省份编码不能为空");
        Assert.notNull(param.getCity(), "城市编码不能为空");
        Assert.notNull(param.getArea(), "区域编码不能为空");
//        Assert.isTrue(StringUtils.isNotBlank(param.getPhone()), "服务号码不能为空");
        Assert.notNull(param.getOperateId(), "所属运营商ID不能为空");
        Assert.isTrue(StringUtils.isNotBlank(param.getOperateName()), "所属运营商名称不能为空");
        Assert.notNull(param.getOperateCorpCode(), "运营商企业代码不能为空");
        Assert.isTrue(StringUtils.isNotEmpty(param.getInvoiceDesc()), "发票提供方不能为空");
//        Assert.notNull(request.getTemplateId(), "默认绑定计费模板id不能为空");
//        Assert.isTrue(com.cdz360.base.utils.StringUtils.isNotBlank(request.getTemplateName()), "默认绑定计费模板名称不能为空");
        Assert.notNull(param.getBizType(), "运营属性不能为空");
        Assert.notNull(param.getBizName(), "运营方名称不能为空");

        if (param.getPark() != null && param.getPark() == 1) {
            Assert.isTrue(StringUtils.isNotBlank(param.getParkFee()), "停车费不能为空");
        }

        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getPayChannels()),
            "场站支付渠道不能为空");

        param.setTopCommId(AntRestUtils.getTopCommId(request));
        ObjectResponse<SitePo> res = this.siteService.addSite(param);
        log.info(LoggerHelper2.formatLeaveLog(request) + " res = {}", res);
        siteSysLogService.addSiteLog(param.getSiteName(), request);
        return res;
    }

    @Operation(summary = "海外版新增场站")
    @PostMapping("/commercial/addSite")
    public ObjectResponse<SitePo> addEssSite(
        ServerHttpRequest request,
        @RequestBody AddSiteParam param) {
        log.info("海外版新增场站: {}", param);
        param.setTopCommId(AntRestUtils.getTopCommId(request));
        ObjectResponse<SitePo> res = this.siteService.addSite(param);
        log.info(LoggerHelper2.formatLeaveLog(request) + " res = {}", res);
        siteSysLogService.addSiteLog(param.getSiteName(), request);
        return res;
    }

    @Operation(summary = "海外版修改场站信息")
    @PostMapping("/commercial/editSite")
    public ObjectResponse<SitePo> editSite(
        ServerHttpRequest request,
        @RequestBody UpdateSiteParam param) {
        log.info("海外版修改场站: {}", param);
        param.setTopCommId(AntRestUtils.getTopCommId(request));
        ObjectResponse<SitePo> res = siteService.updateSiteInfo(param);
        log.info(LoggerHelper2.formatLeaveLog(request) + " res = {}", res);
        siteSysLogService.addSiteLog(param.getSiteName(), request);
        return res;
    }

    @Operation(summary = "新增脱机场站")
    @PostMapping("/addOffLineSite")
    public ObjectResponse<SitePo> addOffLineSite(ServerHttpRequest request,
        @RequestBody AddSiteParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + "param = {}", param);
        Assert.isTrue(StringUtils.isNotBlank(param.getSiteName()), "站点名称不能为空");
        Assert.notNull(param.getLongitude(), "站点经度不能为空");
        Assert.notNull(param.getLatitude(), "站点纬度不能为空");
        Assert.notNull(param.getAddress(), "站点地址不能为空");
        Assert.notNull(param.getProvince(), "省份编码不能为空");
        Assert.notNull(param.getCity(), "城市编码不能为空");
        Assert.notNull(param.getArea(), "区域编码不能为空");
        Assert.notNull(param.getTopCommId(), "一级运营商ID不能为空");
        Assert.notNull(param.getOperateId(), "所属运营商ID不能为空");
        Assert.isTrue(StringUtils.isNotBlank(param.getOperateName()), "所属运营商名称不能为空");

        if (param.getPark() != null && param.getPark() == 1) {
            Assert.isTrue(StringUtils.isNotBlank(param.getParkFee()), "停车费不能为空");
        }
        param.setSiteCategoryList(List.of(SiteCategory.CE)); // 脱机场站默认为‘充’
        ObjectResponse<SitePo> res = this.siteService.addSite(param);
        log.info(LoggerHelper2.formatLeaveLog(request) + " res = {}", res);
        siteSysLogService.addSiteLog(param.getSiteName(), request);
        return res;
    }

    @Operation(summary = "修改场站信息")
    @PostMapping("/updateSiteInfo")
    public ObjectResponse<SitePo> updateSiteInfo(ServerHttpRequest request,
        @RequestBody UpdateSiteParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + "param = {}", param);
        if (param.getPark() != null && param.getPark() == 1) {
            Assert.isTrue(StringUtils.isNotBlank(param.getParkFee()), "停车费不能为空");
        }
        if (AppClientType.SASS_MGM_WEB == AntRestUtils.getAppClientType(request)) {
            // nothing to do
            param.setClientType(AppClientType.SASS_MGM_WEB);
        } else {
            param.setTopCommId(AntRestUtils.getTopCommId(request));
        }
        ObjectResponse<SitePo> res = this.siteService.updateSiteInfo(param);
        this.siteSysLogService.modifySiteLog(param.getSiteName(), request);
        log.info(LoggerHelper2.formatLeaveLog(request) + " res = {}", res);
        return res;
    }


    @Operation(summary = "修改场站状态")
    @PostMapping("/updateSiteStatus")
    public ObjectResponse<SitePo> updateSiteStatus(ServerHttpRequest request,
        @RequestParam String siteId,
        @RequestParam SiteStatus status) {
        log.info(LoggerHelper2.formatEnterLog(request));

        SitePo site = this.siteService.updateSiteStatus(siteId, status);
        this.siteSysLogService.modifySiteStatusLog(site.getSiteName(), status, request);
        log.info(LoggerHelper2.formatLeaveLog(request));
        return RestUtils.buildObjectResponse(site);
    }

    /**
     * 分页获取站点简单信息列表 用于充电管理平台
     *
     * @param request
     * @return
     */
    @Operation(summary = "分页获取站点简单信息列表(用于充电管理平台)")
    @PostMapping("/getPagedSiteSimpleList")
    public ListResponse<SiteSimpleInfoVo> getPagedSiteSimpleList(ServerHttpRequest request,
        @RequestBody SiteListRequest siteListRequest) {
        log.info(LoggerHelper2.formatEnterLog(request) + "siteListRequest = {}",
            JsonUtils.toJsonString(siteListRequest));

        //log.info("站点列表请求参数result----------------------{}", JsonUtils.toJsonString(siteListRequest));
        List<String> gids = AntRestUtils.getSysUserGids(request);
        if (CollectionUtils.isNotEmpty(gids)) {
            // 按照场站组查找
            if (CollectionUtils.isNotEmpty(siteListRequest.getGidList()) && gids.containsAll(
                siteListRequest.getGidList())) {
                gids = siteListRequest.getGidList();
            }
            return siteService.getPagedSiteSimpleList(siteListRequest, null,
                null, gids);
        } else {
            return siteService.getPagedSiteSimpleList(siteListRequest,
                AntRestUtils.getTopCommId(request),
                AntRestUtils.getCommIdChain(request), AntRestUtils.getSysUserGids(request));
        }
    }

    @Operation(summary = "根据地理位置获取场站列表数据")
    @PostMapping("/getGisSiteList")
    public ListResponse<SiteInMongoVo> getGisSiteList(ServerHttpRequest request,
        @RequestBody ListSiteParam param) {
        log.info(LoggerHelper2.formatEnterLog(request) + "param = {}", param);
        param.setCommIdChain(AntRestUtils.getCommIdChain(request));
        return siteService.getSiteListMongo(param);
    }


    /**
     * 分页获取站点简单信息列表 用于运营支撑平台(无需考虑用户权限)
     *
     * @param request
     * @return
     */
    @Operation(summary = "分页获取站点简单信息列表(用于运营支撑平台)")
    @PostMapping("/getPagedSiteSimpleListOnManager")
    public ListResponse<SiteSimpleInfoVo> getPagedSiteSimpleListOnManager(
        @RequestBody SiteListRequest request) {
        log.info("站点列表请求参数result------{}", JsonUtils.toJsonString(request));
        return siteService.getPagedSiteSimpleListOnManager(request);
    }

    /**
     * 获取站点下拉列表
     *
     * @param request
     * @return
     */
    @Operation(summary = "获取站点下拉列表")
    @PostMapping("/getSiteSelectInfoList")
    public ListResponse<SiteSelectInfoVo> getSiteSelectInfoList(ServerHttpRequest request,
        @RequestBody SiteListRequest siteListRequest) {
        List<String> gids = AntRestUtils.getSysUserGids(request);
        if (CollectionUtils.isNotEmpty(gids)) {
            return siteService.getSiteSelectInfoList(siteListRequest, null,
                null, gids);
        } else {
            return siteService.getSiteSelectInfoList(siteListRequest,
                AntRestUtils.getTopCommId(request),
                AntRestUtils.getCommIdChain(request), null);
        }
    }

    @CheckToken(check = "getToken4Aspect")
    @Operation(summary = "获取站点下拉列表")
    @PostMapping("/getSiteInfoListByUserGroups")
    public Mono<ListResponse<SiteSelectInfoVo>> getSiteInfoListByUserGroups(
        ServerHttpRequest request, @RequestBody ListSiteParam param) {
        log.info("[场站组]获取站点下拉列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), param);
        return siteService.getSiteInfoListByUserGroups(AntRestUtils.getSysUid(request), param);
    }

    @Operation(summary = "通过场站组ID获取场站列表")
    @PostMapping("/getSiteByGroup")
    public Mono<ListResponse<SiteSelectInfoVo>> getSiteByGroup(
        ServerHttpRequest request, @RequestBody ListSiteParam param) {
        log.info("[场站组]通过场站组ID获取场站列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), param);
        return siteService.getSiteByGroup(param);
    }

    /**
     * 获取站点下拉列表
     *
     * @param commId
     * @return
     */
    @Operation(summary = "获取站点下拉列表")
    @GetMapping("/getSiteSelectInfoListByComm")
    public ListResponse<SiteSelectInfoVo> getSiteSelectInfoListByComm(ServerHttpRequest request,
        Long commId) {
        AssertUtil.notNull(commId, "商户ID不能为空");
        List<String> gids = AntRestUtils.getSysUserGids(request);
        if (CollectionUtils.isNotEmpty(gids)) {
            return siteService.getSiteSelectInfoListByComm(commId, gids);
        } else {
            return siteService.getSiteSelectInfoListByComm(commId, null);
        }
    }

    @Operation(summary = "设置场站的默认计费模板，isDefault如果为FALSE且场站没有默认模板，则设定传入模板为计费模板")
    @GetMapping("/setDefaultPriceScheme")
    public BaseResponse setDefaultPriceScheme(
        @Parameter(name = "设置场站默认计费模板", required = true) @RequestParam(value = "priceSchemeId") Long priceSchemeId,
        @Parameter(name = "设置场站Id", required = true) @RequestParam(value = "siteId") String siteId,
        @Parameter(name = "是否设为默认", required = true) @RequestParam(value = "isDefault") Boolean isDefault,
        ServerHttpRequest request
    ) {
        log.info(LoggerHelper2.formatEnterLog(request));
        siteService.setDefaultPriceScheme(priceSchemeId, siteId, isDefault);
        return RestUtils.success();
    }

    @Operation(summary = "场站是否绑定计费模板")
    @GetMapping("/siteUesPriceScheme")
    public ObjectResponse<PriceTemplatePo> siteUesPriceScheme(
        @Parameter(name = "设置场站Id", required = true) @RequestParam(value = "siteId") String siteId,
        ServerHttpRequest request
    ) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return siteService.getSitePriceScheme(siteId);
    }

    @Operation(summary = "获取场站后台充电扣款账户的配置")
    @GetMapping(value = "/getDebitAccount")
    public ObjectResponse<SiteDebitAccountVo> getDebitAccount(
        @RequestParam(value = "siteId") String siteId) {
        log.info("siteId: {}", siteId);
        return new ObjectResponse<>(siteService.getDebitAccount(siteId));
    }

    @Operation(summary = "获取场站个性化设置")
    @GetMapping(value = "/getPersonalise")
    public ObjectResponse<SitePersonaliseDTO> getPersonalise(
        ServerHttpRequest request,
        @Parameter(name = "siteId") @RequestParam(value = "siteId") String siteId) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return siteService.getPersonalise(siteId);
    }

    @Operation(summary = "获取场站充电限制详情")
    @GetMapping(value = "/getSocLimitInfo")
    public Mono<ObjectResponse<SiteSocLimitDto>> getSocLimitInfo(
        ServerHttpRequest request,
        @Parameter(name = "siteId") @RequestParam(value = "siteId") String siteId) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return siteService.getSocLimitInfo(siteId);
    }

    @Operation(summary = "修改场站限制状态")
    @PostMapping(value = "/updateSocLimitInfo")
    public Mono<ObjectResponse<Boolean>> updateSocLimitInfo(
        ServerHttpRequest request, @RequestBody SiteSocLimitDto param) {
        log.info(LoggerHelper2.formatEnterLog(request));
        siteSysLogService.updateSocLimitInfo(param.getSiteName(), request);
        return siteService.updateSocLimitInfo(param);
    }

    @Operation(summary = "变更场站个性化设置")
    @PostMapping(value = "/updatePersonalise")
    public BaseResponse updatePersonalise(
        ServerHttpRequest request, @RequestBody SitePersonaliseDTO dto) {
        log.info("变更场站个性化设置: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(dto));
        IotAssert.isTrue(StringUtils.isNotBlank(dto.getSiteName()), "场站名称不能为空");

        // 获取集团商户ID信息
        dto.setTopCommId(AntRestUtils.getTopCommId(request));
        BaseResponse res = siteService.updatePersonalise(dto);
        if (SITE_SETTING_6.equals(dto.getTag())) {
            this.siteSysLogService.updatePersonaliseLogPark(dto.getSiteName(), request);
        } else {
            this.siteSysLogService.updatePersonaliseLog(dto.getSiteName(), request);
        }
        return res;
    }

    @Operation(summary = "获取场站在线卡、离线卡、VIN码数量")
    @GetMapping(value = "/getCardAmountBySiteId")
    public ObjectResponse<SiteCardAccountVo> getCardAmountBySiteId(ServerHttpRequest request,
        @RequestParam(value = "siteId") String siteId) {
        log.info("siteId: {}", siteId);
        String commIdChain = AntRestUtils.getCommIdChain(request);
        IotAssert.isTrue(StringUtils.isNotBlank(commIdChain), "无法获取登录信息，请刷新后重试");
        ListSiteBaseParam param = new ListSiteBaseParam();
        param.setCommIdChain(commIdChain)
            .setGids(AntRestUtils.getSysUserGids(request))
            .setSiteId(siteId);
        return new ObjectResponse<>(siteService.getCardAmountBySiteId(param));
    }

    @Operation(summary = "修改场站后台充电扣款账户的配置")
    @PostMapping(value = "/updateDebitAccount")
    public BaseResponse updateDebitAccount(ServerHttpRequest httpServletRequest,
        @RequestBody SiteDebitAccountVo request) {
        log.info("request: {}", JsonUtils.toJsonString(request));
        IotAssert.isNotNull(request.getSiteId(), "未传入场站ID");
        IotAssert.isNotNull(request.getStartCharingEnable(), "是否开启后台启动充电？");
        Long topCommId = AntRestUtils.getTopCommId(httpServletRequest);
        IotAssert.isNotNull(topCommId, "无法获取topCommId");
        request.setTopCommId(topCommId);
        siteService.updateDebitAccount(request);
        return BaseResponse.success();
    }

    @Operation(summary = "场站后台充电扣款账户-所属商户下拉")
    @GetMapping(value = "/getSiteDebitCommIdList")
    public ListResponse<Commercial> getSiteDebitCommIdList(ServerHttpRequest request,
        @Parameter(name = "当前场站ID") @RequestParam("siteId") String siteId,
        @Parameter(name = "所属商户名称-模糊查询", required = false) @RequestParam(value = "commName", required = false) String commName) {
        String commIdChain = AntRestUtils.getCommIdChain(request);
        IotAssert.isTrue(StringUtils.isNotBlank(commIdChain), "无法获取登录信息，请刷新后重试");
        return siteService.getSiteDebitCommIdList(siteId, commName, commIdChain);
    }

    @Operation(summary = "场站后台充电扣款账户-企业客户下拉")
    @GetMapping(value = "/getSiteDebitCorpCusList")
    public ListResponse<BlocUserDto> getSiteDebitCorpCusList(
        @Parameter(name = "所属商户的ID", required = true) @RequestParam("commId") Long commId,
        @Parameter(name = "是否包含互联企业 null or true 表示包含; false 表示不包含")
        @RequestParam(value = "includedHlhtCorp", required = false) Boolean includedHlhtCorp,
        @Parameter(required = false) @RequestParam(value = "name", required = false) String name) {
        return siteService.getSiteDebitCorpCusList(commId, includedHlhtCorp, name);
    }

    @Operation(summary = "场站后台充电扣款账户-企业授信客户下拉")
    @GetMapping(value = "/getSiteDebitCorpCreditCusList")
    public ListResponse<RBlocUser> getSiteDebitCorpCreditCusList(
        @Parameter(name = "所属商户的ID", required = true) @RequestParam("commId") Long commId,
        @Parameter(required = true) @RequestParam(value = "blocUserId") Long blocUserId,
        @Parameter(required = false) @RequestParam(value = "name", required = false) String name) {
        return siteService.getSiteDebitCorpCreditCusList(commId, blocUserId, name);
    }

    @Operation(summary = "场站后台充电扣款账户-商户会员下拉")
    @GetMapping(value = "/getSiteDebitCommCusList")
    public ListResponse<CommCusRef> getSiteDebitCommCusList(
        @Parameter(name = "所属商户的ID", required = true) @RequestParam("commId") Long commId,
        @Parameter(required = false) @RequestParam(value = "name", required = false) String name) {
        return siteService.getSiteDebitCommCusList(commId, name);
    }

    @Operation(summary = "修改场站上线时间")
    @GetMapping(value = "/setOnlineDate")
    public BaseResponse setOnlineDate(
        @Parameter(name = "场站ID", required = true) @RequestParam("siteId") String siteId,
        @Parameter(name = "上线时间", example = "'2020-04-05'", required = true) @RequestParam("onlineDate") String onlineDate) {
        log.info("siteId: {}, onlineDate: {}", siteId, onlineDate);
        if (StringUtils.isBlank(siteId)) {
            throw new DcArgumentException("参数错误，场站ID不能为空");
        }
        return siteService.setOnlineDate(siteId, onlineDate);
    }


    @PostMapping(value = "/getSiteDetail")
    @Operation(summary = "获取场站详情信息")
    public ObjectResponse<SiteDetailInfoVo> getSiteDetail(ServerHttpRequest request,
        @RequestParam(value = "obtainCameraData", required = false) Boolean obtainCameraData,
        @RequestParam(value = "siteId") String siteId) {
        log.info("getSiteDetail. obtainCameraData = {}, siteId = {}", obtainCameraData, siteId);
        if (StringUtils.isBlank(siteId)) {
            log.warn("参数错误,场站编号不能为空. siteId = {}", siteId);
            throw new DcArgumentException("参数错误,场站编号不能为空");
        }
        String commIdChain = AntRestUtils.getCommIdChain(request);
        SiteGeoListRequest param = new SiteGeoListRequest();
        param.setSiteId(siteId);
        var res = this.siteService.getSiteDetail(param);

        this.addCameraFeilds(res, commIdChain, obtainCameraData, siteId);

        return res;
    }

    /**
     * 增加摄像头相关字段数据
     *
     * @param res
     * @param idChain
     * @param siteId
     */
    private void addCameraFeilds(ObjectResponse<SiteDetailInfoVo> res, String idChain,
        Boolean obtainCameraData, String siteId) {
        if (res == null || res.getData() == null) {
            return;
        }
        if (BooleanUtils.isNotTrue(obtainCameraData)) {
            return;
        }

        ObjectResponse<CameraSitePo> store = cameraFeignClient.getStore(siteId)
            .block(Duration.ofSeconds(50L));
        if (store != null && store.getData() != null) {
            res.getData().setCameraEnable(true);
        }

        CameraRecorderParam req = new CameraRecorderParam();
        req.setIdChain(idChain)
            .setSiteId(siteId);
        ListResponse<CameraRecorderVo> response = cameraFeignClient.listRecorder(req)
            .block(Duration.ofSeconds(50L));
        if (response != null && response.getData() != null && response.getData().size() > 0) {
            res.getData().setCameraExist(true);
        }
    }

    @Operation(summary = "通过站点自定义编号查找场站")
    @GetMapping("/getSiteInfoBySiteNo")
    public ObjectResponse<SiteNoVo> getSiteInfoBySiteNo(
        @Parameter(name = "站点自编号", required = true) @RequestParam(value = "siteNo") String siteNo,
        @Parameter(name = "站点Id", required = false) @RequestParam(value = "siteId", required = false) String siteId,
        ServerHttpRequest request
    ) {

        if (siteNo == null || siteNo.length() > 30) {
            throw new DcServiceException("站点编号格式不正确");
        }

        SiteNoVo siteInfo = siteService.getSiteInfoBySiteNo(
            super.getCommercialSample2(request).getTopCommId(), siteNo);
        if (StringUtils.isNotBlank(siteId) && siteInfo != null && siteInfo.getId().equals(siteId)) {
            siteInfo = null;
        }

        return RestUtils.buildObjectResponse(siteInfo);
    }

    @Operation(summary = "通过站点ID查找场站编号")
    @GetMapping("/getSiteNoById")
    public ObjectResponse<SiteNoVo> getSiteNoById(ServerHttpRequest request,
        @RequestParam(value = "siteId") String siteId) {
        return siteService.getSiteNoById(siteId);
    }

    @GetMapping("/getExistingOperateCorpCodes")
    public ListResponse<String> getExistingOperateCorpCodes() {
        return siteService.getExistingOperateCorpCodes();
    }


    @Operation(summary = "根据站点id获取站点下各状态插座数量统计")
    @GetMapping("/getChargerStatusStatisticsBySiteId")
    public ObjectResponse<PlugStatusCountDto> getChargerStatusStatisticsBySiteId(
        @RequestParam("siteId") String siteId) {
        return siteService.getChargerStatusStatisticsBySiteId(siteId);
    }

    @Operation(summary = "获取场站已存在的发票提供方信息")
    @GetMapping("/getInvoiceDescList")
    public ListResponse<String> getInvoiceDescList(ServerHttpRequest request,
        @RequestParam(value = "desc", required = false) String desc) {
        String commIdchain = super.getCommIdChain2(request);
        log.info(">> commIdchain = {} desc = {}", commIdchain, desc);
        return this.siteService.getInvoiceDescList(commIdchain, desc);
    }

    @Operation(summary = "获取站点更换商户需要更换的内容详情")
    @GetMapping("/getChangeInfo")
    public ObjectResponse<ChangeInfo> getChangeInfo(@RequestParam("siteId") String siteId,
        @RequestParam("commId") Long commId) {
        return siteService.getChangeInfo(siteId, commId);
    }

    @Operation(summary = "获取站点更换商户需要更换的内容详情")
    @GetMapping("/queryChangeCommIdBySiteId")
    public ObjectResponse<Integer> queryChangeCommIdBySiteId(@RequestParam("siteId") String siteId,
        @RequestParam("commId") Long commId) {
        return siteService.queryChangeCommIdBySiteId(siteId, commId);
    }

    @Operation(summary = "企业切换商户时，场站后台启动配置项保留/移除列表")
    @GetMapping(value = "/getMoveCorpSiteConfStart")
    public ObjectResponse<SiteConfStartList> getMoveCorpSiteConfStart(
        @RequestParam("corpId") Long corpId,
        @RequestParam("commId") Long commId,
        ServerHttpRequest request) {
        log.info("corpId = {}, commId = {}", corpId, commId);

        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");
        return siteService.getMoveCorpSiteConfStart(corpId, commId);
    }

    @Operation(summary = "企业切换商户时，场站soc限制保留/移除列表")
    @GetMapping(value = "/getMoveCorpSoc")
    public ObjectResponse<MoveCorpUserSocStrategyList> getMoveCorpSoc(
        @RequestParam("corpId") Long corpId,
        @RequestParam("commId") Long commId,
        ServerHttpRequest request) {
        log.info("corpId = {}, commId = {}", corpId, commId);

        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");
        return siteService.getMoveCorpSoc(corpId, commId);
    }

    @Operation(summary = "按照商户链返回场站信息")
    @GetMapping("/getSiteListByIdChain")
    public ListResponse<SiteSimpleDto> getSiteListByIdChain(ServerHttpRequest request) {
        log.debug(LoggerHelper2.formatEnterLog(request));
        String idChain = AntRestUtils.getCommIdChain(request);
        return this.siteService.getSiteListByIdChain(idChain);

    }

    @Operation(summary = "获取运维人员管理场站的类型(光储充)")
    @GetMapping("/ywUserSiteCategoryList")
    public ListResponse<SiteCategory> ywUserSiteCategoryList(@RequestParam("sysUid") Long sysUid) {
        log.info("sysUid = {} ", sysUid);
        return siteService.ywUserSiteCategoryList(sysUid);
    }

    @Operation(summary = "根据场站ID获取合作方信息")
    @GetMapping("/getPartnerInfoBySiteId")
    public Mono<ListResponse<HlhtSiteVo>> getPartnerInfoBySiteId(ServerHttpRequest request,
        @RequestParam("siteId") String siteId) {
        log.info("siteId = {} ", siteId);
        return openHlhtFeignClient.getPartnerInfoBySiteId(siteId);
    }

    @Operation(summary = "企业切换商户时，场站无卡启动结算账户保留/移除列表")
    @GetMapping(value = "/getMoveCorpNoCard")
    public ObjectResponse<MoveCorpNoCardList> getMoveCorpNoCard(@RequestParam("corpId") Long corpId,
        @RequestParam("commId") Long commId) {
        log.info("corpId = {}, commId = {}", corpId, commId);

        return siteService.getMoveCorpNoCard(corpId, commId);
    }

    @Operation(summary = "企业授信账户作为无卡启动结算的场站信息")
    @GetMapping("/getCorpNoCardList")
    public ListResponse<MoveCorpNoCardVo> getCorpNoCardList(
        @RequestParam(value = "corpId") Long corpId) {
        return siteService.getCorpNoCardList(corpId);
    }

    @Operation(summary = "商户会员禁用，无卡充电结算使用的场站")
    @GetMapping("/getCommNoCardList")
    public ListResponse<SiteVo> getCommNoCardList(@RequestParam(value = "commId") Long commId,
        @RequestParam(value = "userId") Long userId) {
        return siteService.getCommNoCardList(commId, userId);
    }

    @GetMapping("/getSiteQrCodeVo")
    public ObjectResponse<SiteQrCodeVo> getSiteQrCodeVo(
        @RequestParam(value = "siteId") String siteId) {
        return siteService.getSiteQrCodeVo(siteId);
    }

    @Operation(summary = "获取场站计费模板信息")
    @GetMapping("/getSitePriceList")
    public ListResponse<SiteChargePriceVo> getSitePriceList(@RequestParam("siteId") String siteId) {
        log.info("获取场站计费信息, siteId={}", siteId);
        return siteService.getSitePriceList(siteId);
    }

}
