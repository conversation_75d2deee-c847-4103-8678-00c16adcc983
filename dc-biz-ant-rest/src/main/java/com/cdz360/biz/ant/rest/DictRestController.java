package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.config.LocalFileStorageProperties;
import com.cdz360.biz.ant.service.DictService;
import com.cdz360.biz.ant.utils.FileUtil;
import com.cdz360.biz.ant.utils.OSSClientUtil;
import com.cdz360.biz.model.trading.file.po.OssFilePo;
import com.chargerlinkcar.framework.common.domain.Dict;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import java.io.IOException;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * 字典管理Controller
 *
 * <AUTHOR>
 * @version 2018-11-21
 */
@Slf4j
@RestController
@RequestMapping(value = "/v1/sys/dict")
public class DictRestController extends BaseController {

    @Autowired
    private DictService dictService;

    @Autowired
    private OSSClientUtil ossClient;

    @Autowired
    private LocalFileStorageProperties localFileStorageProperties;

    /**
     * 根据id查询
     *
     * @param dict
     * @return
     */
    @RequestMapping(value = {"/get/json"}, method = {RequestMethod.POST})
    public ObjectResponse<Dict> getRequestBody(@RequestBody(required = false) Dict dict) {
        return dictService.get(dict.getId());
    }

    /**
     * 查询全部数据
     *
     * @param dict
     * @return
     */
    @RequestMapping(value = {"/findList/json"}, method = {RequestMethod.POST})
    public ListResponse<Dict> findListRequestBody(Dict dict) {
        return dictService.findList(dict);
    }

    /**
     * 分页查询
     *
     * @param dict
     * @return
     */
    @RequestMapping(value = {"/list/json"}, method = {RequestMethod.POST})
    public ListResponse<Dict> listRequestBody(@RequestBody(required = false) Dict dict) {
        log.info(">>>>>字典分页查询：{}", JsonUtils.toJsonString(dict));
        return dictService.queryPage(dict);
    }

    /**
     * 添加字典数据
     *
     * @param dict
     * @return
     */
    @RequestMapping(value = "/save/json", method = {RequestMethod.POST})
    public BaseResponse saveRequestBody(@RequestBody Dict dict) {
        return dictService.save(dict);
    }

    /**
     * 更新字典数据
     *
     * @param dict
     * @return
     */
    @RequestMapping(value = "/update/json", method = {RequestMethod.POST})
    public BaseResponse updateRequestBody(@RequestBody Dict dict) {
        return dictService.update(dict);
    }

    /**
     * 删除字典数据
     *
     * @param dict
     * @return
     */
    @RequestMapping(value = "/delete/json", method = {RequestMethod.POST})
    public BaseResponse deleteRequestBody(@RequestBody Dict dict) {
        return dictService.delete(dict);
    }

    /**
     * 根据类型获取数据
     *
     * @param type 类型
     * @return
     */
//    @ResponseBody
    @RequestMapping(value = "/findDictDataByType") // 前端使用get / post
    public ListResponse<Dict> findDictDataByType(ServerHttpRequest request,  @RequestParam("type") String type) {
        log.info(LoggerHelper2.formatEnterLog(request) + " type = {}", type);
        return dictService.findDictDataByType(type);
    }


    /**
     * 图片上传
     *
     * @param file
     * @return
     * @throws IOException
     */
    @RequestMapping("/upload")
    public Mono<ObjectResponse<Map<String, Object>>> updateHead(
        @RequestPart FilePart file
    ) throws IOException {
        log.info(">> 上传场站图片");
        if (file == null // || file.getSize() <= 0
        ) {
            throw new DcArgumentException("图片为空");
        }
        if (localFileStorageProperties.isEnable()) {
            // 本地存储开启
            return FileUtil.uploadImg(file, localFileStorageProperties.getImagePath());
        }
        return ossClient.uploadImg2Oss(file);
//        String imgUrl = ossClient.getImgUrl(name);
//        Map<String, String> imgMap = new HashMap<>(16);
//        imgMap.put("name", name);
//        imgMap.put("url", imgUrl);
//        return new ObjectResponse<>(imgMap);
    }

    /**
     * @param file
     * @param forceDownload 不为null表示该地址强制下载
     * @return
     */
    @RequestMapping("/uploadFile")
    public Mono<ObjectResponse<OssFilePo>> uploadFile(@RequestPart FilePart file,
        @RequestParam(value = "forceDownload", required = false)
        String forceDownload) {
        log.debug("上传附件: 强制下载: {}", StringUtils.isNotBlank(forceDownload));
        if (file == null) {
            throw new DcArgumentException("文件不能为空");
        }
        return ossClient.uploadFile(file, StringUtils.isNotBlank(forceDownload));
    }
}