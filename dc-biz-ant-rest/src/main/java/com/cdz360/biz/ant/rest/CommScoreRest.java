package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.feign.AntUserFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorUserFeignClient;
import com.cdz360.biz.ant.service.sysLog.CommercialSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.cus.commScore.param.CommScoreParam;
import com.cdz360.biz.model.cus.commScore.param.ScoreClearParam;
import com.cdz360.biz.model.cus.commScore.param.UserScoreListParam;
import com.cdz360.biz.model.cus.commScore.param.UserScoreParam;
import com.cdz360.biz.model.cus.commScore.po.CommScoreLevelPo;
import com.cdz360.biz.model.cus.commScore.po.CommScoreLogPo;
import com.cdz360.biz.model.cus.commScore.vo.CommScoreVo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;
import java.util.List;


@Slf4j
@RestController
@RequestMapping(value = "/api/commScore")
public class CommScoreRest extends BaseController {

    @Autowired
    private ReactorUserFeignClient userFeignClient;
    @Autowired
    private CommercialSysLogService commercialSysLogService;

    @GetMapping(value = "/getCommScoreVo")
    public Mono<ObjectResponse<CommScoreVo>> getCommScoreVo(@RequestParam("commCusId") Long commCusId) {
        log.info("getCommScoreVo commCusId: {}", commCusId);
        return userFeignClient.getCommScoreVo(commCusId);
    }
    /**
     * 添加或修改商户会员等级
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/addCommScore")
    public Mono<BaseResponse> addCommScore(
            ServerHttpRequest request,
            @RequestBody CommScoreParam param) {
        log.info("添加修改商户会员等级,param={}", JsonUtils.toJsonString(param));
        commercialSysLogService.updateCommScoreLevelLog(param.getCommName(), request);
        return userFeignClient.addCommScore(param);
    }

    /**
     * 商户会员积分清零
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/commUserScoreRest")
    public Mono<BaseResponse> commUserScoreRest(
            ServerHttpRequest request,
            @RequestBody ScoreClearParam param) {
        log.info("商户会员积分清零,param={}", param);
        Long sysUid = AntRestUtils.getSysUid(request);
        if (sysUid == null){
            throw new DcServiceException("未登录状态");
        }
        param.setSysUid(sysUid);
        return userFeignClient.commUserScoreRest(param);
    }

    /**
     * 商户会员积分调整
     *
     * @return
     */
    @PostMapping(value = "/commUserScoreModify")
    public Mono<BaseResponse> commUserScoreModify(
            ServerHttpRequest request,
            @RequestBody UserScoreParam param) {
        log.info("商户会员积分调整,param={}", JsonUtils.toJsonString(param));
        Long sysUid = AntRestUtils.getSysUid(request);
        if (sysUid == null){
            throw new DcServiceException("未登录状态");
        }
        param.setSysUid(sysUid);
        return userFeignClient.commUserScoreModify(param);
    }

    /**
     * 商户会员积点列表
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/commUserScoreList")
    public Mono<ListResponse<CommScoreLogPo>> commUserScoreList(@RequestBody UserScoreListParam param) {
        log.info("商户会员积分列表,param={}", JsonUtils.toJsonString(param));
        return userFeignClient.commUserScoreList(param);
    }

    /**
     * 商户会员等级
     */
    @GetMapping(value = "/findLevelListByCommId")
    public Mono<ListResponse<CommScoreLevelPo>> findLevelListByCommId(@RequestParam("commId") Long commId) {
        return userFeignClient.findLevelListByCommId(commId);
    }

}
