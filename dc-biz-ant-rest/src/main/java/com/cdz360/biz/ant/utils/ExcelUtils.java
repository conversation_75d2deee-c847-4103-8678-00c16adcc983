package com.cdz360.biz.ant.utils;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.model.cus.corp.param.BatchAddCreditUserParam;
import com.cdz360.biz.model.cus.corp.type.LimitCycle;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

@Slf4j
public class ExcelUtils {

    /**
     * 企业卡excel
     *
     * @param in
     * @param fileName
     * @return
     */
    public static List<BatchAddCreditUserParam.CreditUser> parseCorpCreditAccounts(InputStream in, String fileName) {
        log.info(">> parse excel: file name={}", fileName);

        // 创建excel工作簿
        Workbook work = getWorkbook(in, fileName);
//        log.info("work: {}", JsonUtils.toJsonString(work));
        try {
            if (null == work) {
                throw new Exception("创建Excel工作薄为空！");
            }
        } catch (Exception e) {
            throw new DcServiceException("系统异常");
        }

        //Row row;
        //Cell cell = null;

        List<BatchAddCreditUserParam.CreditUser> list = new ArrayList<>();
        log.info("work.getNumberOfSheets(): {}", work.getNumberOfSheets());
        //for (int i = 0; i < work.getNumberOfSheets(); i++) {

        Sheet sheet = work.getSheetAt(0);
//            log.info("sheet: {}", JsonUtils.toJsonString(sheet));
        if (sheet == null) {
            return list;
            //continue;
        }
        // 需求：从第4行到10003行认为是有效数据
        // 有效值从0开始，所以不能大于10002
        if (sheet.getLastRowNum() > 5002)
            throw new DcServiceException("导入的授信账户数量不能超过5000条");
//        int startCol = 0;
//        int endCol = 0;
        log.info("sheet.getFirstRowNum(): {}", sheet.getFirstRowNum());
        log.info("sheet.getLastRowNum(): {}", sheet.getLastRowNum());
        for (int j = 3; j <= sheet.getLastRowNum(); j++) {
            Row row = sheet.getRow(j);
//                log.info("row: {}", row);
            if (isEmptyRow(row))
                continue;
//            startCol = row.getFirstCellNum();
//            endCol = row.getLastCellNum();
            //List<Object> li = new ArrayList<>();
//                log.info("row.getFirstCellNum(): {}", row.getFirstCellNum());
//                log.info("row.getLastCellNum(): {}", row.getLastCellNum());
            try {
                BatchAddCreditUserParam.CreditUser cu = new BatchAddCreditUserParam.CreditUser();
                String phone = ExcelUtils.getStringCellValue(row, 0);
                String name = ExcelUtils.getStringCellValue(row, 1);
                String durType = ExcelUtils.getStringCellValue(row, 2);
                BigDecimal limitMoney = ExcelUtils.getDecimalCellValue(row, 3);
                cu.setPhone(phone)
                        .setName(name)
                        .setDurType(ExcelUtils.parseTo(durType))
                        .setLimitMoney(limitMoney);
                list.add(cu);
            } catch (Exception e) {
                log.warn("rowNumber = {}, error = {}", row.getRowNum(), e.getMessage());
            }
        }
        //}

//        work.close();

        log.info(">> parse excel end.");
        return list;
    }

    private static LimitCycle parseTo(String durType) {
        if (StringUtils.isBlank(durType)) {
            return LimitCycle.UNKNOWN;
        }
        switch (durType.strip()) {
            case "每天":
                return LimitCycle.DAILY;
            case "每周":
                return LimitCycle.WEEKLY;
            case "每月":
                return LimitCycle.MONTHLY;
            case "每年":
                return LimitCycle.YEARLY;
            case "无限制":
                return LimitCycle.UNLIMITED;
            case "无周期":
                return LimitCycle.NONCYCLE;
            default:
                return LimitCycle.UNKNOWN;
        }
    }

    private static String getStringCellValue(Row row, int cellIdx) {
        try {
            Cell cell = row.getCell(cellIdx);
            if (Cell.CELL_TYPE_STRING == cell.getCellType()) {
                return cell.getStringCellValue();
            } else if (Cell.CELL_TYPE_NUMERIC == cell.getCellType()) {
                cell.setCellType(Cell.CELL_TYPE_STRING);
                return cell.getStringCellValue();
            } else {
                log.warn("unsupport cell type: {}", cell.getCellType());
                return "";
            }
        } catch (Exception e) {
            log.warn("rowNumber = {}, cellIdx = {}, error = {}",
                    row.getRowNum(), cellIdx, e.getMessage());
        }
        return "";
    }

    private static BigDecimal getDecimalCellValue(Row row, int cellIdx) {
        try {
            return BigDecimal.valueOf(row.getCell(cellIdx).getNumericCellValue());
        } catch (Exception e) {
            log.warn("rowNumber = {}, cellIdx = {}, error = {}",
                    row.getRowNum(), cellIdx, e.getMessage());
        }
        return null;
    }


    public static String cellHandle(Cell cell) {
        if (cell == null) {
            return "";
        } else if (cell.getCellType() == HSSFCell.CELL_TYPE_NUMERIC) {
            return BigDecimal.valueOf(cell.getNumericCellValue()).toPlainString();
        }
        return cell.getStringCellValue();
    }

    /**
     * 企业VIN码excel
     *
     * @param in
     * @param fileName
     * @return
     */
    public static List<List<String>> getVinListByExcelOnCorp(InputStream in, String fileName) {
        log.info(">> parse excel: file name={}", fileName);

        // 创建excel工作簿
        Workbook work = getWorkbook(in, fileName);
//        log.info("work: {}", JsonUtils.toJsonString(work));
        try {
            if (null == work) {
                throw new Exception("创建Excel工作薄为空！");
            }
        } catch (Exception e) {
            throw new DcServiceException("系统异常");
        }

        Sheet sheet = null;
        Row row = null;
        Cell cell = null;

        List<List<String>> list = new ArrayList<>();
        log.info("work.getNumberOfSheets(): {}", work.getNumberOfSheets());
        for (int i = 0; i < work.getNumberOfSheets(); i++) {

            sheet = work.getSheetAt(i);
//            log.info("sheet: {}", JsonUtils.toJsonString(sheet));
            if (sheet == null) {
                continue;
            }
            // 需求：从第4行到10003行认为是有效数据
            // 有效值从0开始，所以不能大于10002
            if (sheet.getLastRowNum() > 10002)
                throw new DcServiceException("导入的VIN码数量最大不能超过1万条");
            int startCol = 0;
            int endCol = 0;
            log.info("sheet.getFirstRowNum(): {}", sheet.getFirstRowNum());
            log.info("sheet.getLastRowNum(): {}", sheet.getLastRowNum());
            for (int j = 3; j <= sheet.getLastRowNum(); j++) {
                row = sheet.getRow(j);
//                log.info("row: {}", row);
                if (isEmptyRow(row))
                    continue;
                startCol = row.getFirstCellNum();
                endCol = row.getLastCellNum();
                List<String> li = new ArrayList<>();
//                log.info("row.getFirstCellNum(): {}", row.getFirstCellNum());
//                log.info("row.getLastCellNum(): {}", row.getLastCellNum());
                for (int y = startCol; y < endCol; y++) {
                    cell = row.getCell(y);
//                    log.info("cell: {}", cell);
                    li.add(cellHandle(cell));
                }
                if (isObjectEmpty(li.get(0)) &&
                        isObjectEmpty(li.get(1)) &&
                        isObjectEmpty(li.get(2)))
                    continue;
//                log.info("li: {}", li);
                list.add(li);
            }
        }

//        work.close();

        log.info(">> parse excel end.");
        return list;
    }

    /**
     * 脱机桩excel
     *
     * @param in
     * @param fileName
     * @return
     */
    public static List<List<String>> getOfflineEvseListByExcel(InputStream in, String fileName) {
        log.info(">> parse excel: file name={}", fileName);

        // 创建excel工作簿
        Workbook work = getWorkbook(in, fileName);
//        log.info("work: {}", JsonUtils.toJsonString(work));
        try {
            if (null == work) {
                throw new Exception("创建Excel工作薄为空！");
            }
        } catch (Exception e) {
            throw new DcServiceException("系统异常");
        }

        Sheet sheet = null;
        Row row = null;
        Cell cell = null;

        List<List<String>> list = new ArrayList<>();
        log.info("work.getNumberOfSheets(): {}", work.getNumberOfSheets());
        for (int i = 0; i < work.getNumberOfSheets(); i++) {

            sheet = work.getSheetAt(i);
//            log.info("sheet: {}", JsonUtils.toJsonString(sheet));
            if (sheet == null) {
                continue;
            }
            // 需求：从第4行到2003行认为是有效数据
            // 有效值从0开始，所以不能大于2002
            if (sheet.getLastRowNum() > 2002)
                throw new DcServiceException("导入的VIN码数量最大不能超过2千条");
            int startCol = 0;
            int endCol = 0;
            log.info("sheet.getFirstRowNum(): {}", sheet.getFirstRowNum());
            log.info("sheet.getLastRowNum(): {}", sheet.getLastRowNum());
            for (int j = 3; j <= sheet.getLastRowNum(); j++) {
                row = sheet.getRow(j);
//                log.info("row: {}", row);
                if (isEmptyRow(row))
                    continue;
                startCol = row.getFirstCellNum();
                endCol = row.getLastCellNum();
                List<String> li = new ArrayList<>();
//                log.info("row.getFirstCellNum(): {}", row.getFirstCellNum());
//                log.info("row.getLastCellNum(): {}", row.getLastCellNum());
                for (int y = startCol; y < endCol; y++) {
                    cell = row.getCell(y);
//                    log.info("cell: {}", cell);
                    li.add(cellHandle(cell));
                }
                if (isObjectEmpty(li.get(0)) &&
                        isObjectEmpty(li.get(1)) &&
                        isObjectEmpty(li.get(2)))
                    continue;
//                log.info("li: {}", li);
                list.add(li);
            }
        }

//        work.close();

        log.info(">> parse excel end.");
        return list;
    }

    /**
     * 桩批量导入excel
     *
     * @param in
     * @param fileName
     * @return
     */
    public static List<List<String>> getEvseListByExcel(InputStream in, String fileName, Integer cellLength) {
        log.info(">> parse excel: file name={}", fileName);

        // 创建excel工作簿
        Workbook work = getWorkbook(in, fileName);
//        log.info("work: {}", JsonUtils.toJsonString(work));
        try {
            if (null == work) {
                throw new Exception("创建Excel工作薄为空！");
            }
        } catch (Exception e) {
            throw new DcServiceException("系统异常");
        }

        Sheet sheet = null;
        Row row = null;
        Cell cell = null;

        List<List<String>> list = new ArrayList<>();
        log.info("work.getNumberOfSheets(): {}", work.getNumberOfSheets());
        for (int i = 0; i < work.getNumberOfSheets(); i++) {

            sheet = work.getSheetAt(i);
//            log.info("sheet: {}", JsonUtils.toJsonString(sheet));
            if (sheet == null) {
                continue;
            }
            // 需求：从第4行到2003行认为是有效数据
            // 有效值从0开始，所以不能大于2002
            if (sheet.getLastRowNum() > 2002)
                throw new DcServiceException("导入的桩数量最大不能超过2千条");
            int startCol = 0;
            int endCol = 0;
            log.info("sheet.getFirstRowNum(): {}", sheet.getFirstRowNum());
            log.info("sheet.getLastRowNum(): {}", sheet.getLastRowNum());
            for (int j = 3; j <= sheet.getLastRowNum(); j++) {
                row = sheet.getRow(j);
//                log.info("row: {}", row);
                if (isEmptyRow(row))
                    continue;
//                startCol = row.getFirstCellNum();
                startCol = 0;
                endCol = cellLength != null ? cellLength : row.getLastCellNum();
                List<String> li = new ArrayList<>();
//                log.info("row.getFirstCellNum(): {}", row.getFirstCellNum());
//                log.info("row.getLastCellNum(): {}", row.getLastCellNum());
                for (int y = startCol; y < endCol; y++) {
                    cell = row.getCell(y);
//                    log.info("cell: {}", cell);
                    li.add(cellHandle(cell));
                }
//                if (isObjectEmpty(li.get(0)) &&
//                        isObjectEmpty(li.get(1)) &&
//                        isObjectEmpty(li.get(2)))
//                    continue;
//                log.info("li: {}", li);
                list.add(li);
            }
        }

//        work.close();

        log.info(">> parse excel end.");
        return list;
    }

    /**
     * SIM批量导入excel
     *
     * @param in
     * @param fileName
     * @return
     */
    public static List<List<String>> getSimListByExcel(InputStream in, String fileName, Integer cellLength) {
        log.info(">> parse excel: file name={}", fileName);

        // 创建excel工作簿
        Workbook work = getWorkbook(in, fileName);
//        log.info("work: {}", JsonUtils.toJsonString(work));
        try {
            if (null == work) {
                throw new Exception("创建Excel工作薄为空！");
            }
        } catch (Exception e) {
            throw new DcServiceException("系统异常");
        }

        Sheet sheet = null;
        Row row = null;
        Cell cell = null;

        List<List<String>> list = new ArrayList<>();
        log.info("work.getNumberOfSheets(): {}", work.getNumberOfSheets());
        for (int i = 0; i < work.getNumberOfSheets(); i++) {

            sheet = work.getSheetAt(i);
//            log.info("sheet: {}", JsonUtils.toJsonString(sheet));
            if (sheet == null) {
                continue;
            }
            // 需求：从第4行到2003行认为是有效数据
            // 有效值从0开始，所以不能大于2002
            if (sheet.getLastRowNum() > 2002)
                throw new DcServiceException("导入的桩数量最大不能超过2千条");
            int startCol = 0;
            int endCol = 0;
            log.info("sheet.getFirstRowNum(): {}", sheet.getFirstRowNum());
            log.info("sheet.getLastRowNum(): {}", sheet.getLastRowNum());
            for (int j = 3; j <= sheet.getLastRowNum(); j++) {
                row = sheet.getRow(j);
//                log.info("row: {}", row);
                if (isEmptyRow(row))
                    continue;
//                startCol = row.getFirstCellNum();
                startCol = 0;
                endCol = cellLength != null ? cellLength : row.getLastCellNum();
                List<String> li = new ArrayList<>();
//                log.info("row.getFirstCellNum(): {}", row.getFirstCellNum());
//                log.info("row.getLastCellNum(): {}", row.getLastCellNum());
                for (int y = startCol; y < endCol; y++) {
                    cell = row.getCell(y);
//                    log.info("cell: {}", cell);
                    li.add(cellHandle(cell));
                }
//                if (isObjectEmpty(li.get(0)) &&
//                        isObjectEmpty(li.get(1)) &&
//                        isObjectEmpty(li.get(2)))
//                    continue;
//                log.info("li: {}", li);
                list.add(li);
            }
        }

//        work.close();

        log.info(">> parse excel end.");
        return list;
    }

    /**
     * 课程excel
     *
     * @param in
     * @param fileName
     * @return
     */
    public static List<List<Object>> getCardListByExcel(InputStream in, String fileName) {
        log.info(">> parse excel: file name={}", fileName);

        // 创建excel工作簿
        Workbook work = getWorkbook(in, fileName);
//        log.info("work: {}", JsonUtils.toJsonString(work));
        try {
            if (null == work) {
                throw new Exception("创建Excel工作薄为空！");
            }
        } catch (Exception e) {
            throw new DcServiceException("系统异常");
        }

        Sheet sheet = null;
        Row row = null;
        Cell cell = null;

        List<List<Object>> list = new ArrayList<>();
        log.info("work.getNumberOfSheets(): {}", work.getNumberOfSheets());
        for (int i = 0; i < work.getNumberOfSheets(); i++) {

            sheet = work.getSheetAt(i);
//            log.info("sheet: {}", JsonUtils.toJsonString(sheet));
            if (sheet == null) {
                continue;
            }
            if (sheet.getLastRowNum() > 10000)
                throw new DcServiceException("导入的卡数量最大不能超过1万条");

            // 滤过第一行标题
            int startCol = 0;
            int endCol = 0;
            log.info("sheet.getFirstRowNum(): {}", sheet.getFirstRowNum());
            log.info("sheet.getLastRowNum(): {}", sheet.getLastRowNum());
            boolean actualStart = false;
            for (int j = sheet.getFirstRowNum(); j <= sheet.getLastRowNum(); j++) {
                row = sheet.getRow(j);
                log.info("row: {}", row);
                if (isEmptyRow(row))
                    continue;
                log.info("sheet.getFirstRowNum(): {}", sheet.getFirstRowNum());
                // 空行过滤，第一非空行需要记录开始列和结束列位置值
                if (row == null || !actualStart) {
                    if (null != row) {
                        actualStart = true;

                        startCol = row.getFirstCellNum();
                        endCol = row.getLastCellNum();
                        log.info("row.getFirstCellNum(): {}", row.getFirstCellNum());
                        log.info("row.getLastCellNum(): {}", row.getLastCellNum());
                    }
                    continue;
                }

                List<Object> li = new ArrayList<>();
                log.info("row.getFirstCellNum(): {}", row.getFirstCellNum());
                log.info("row.getLastCellNum(): {}", row.getLastCellNum());
                for (int y = startCol; y < endCol; y++) {
                    cell = row.getCell(y);
                    log.info("cell: {}", cell);
                    li.add(cell);
                }
                if (isObjectEmpty(li.get(0)) &&
                        isObjectEmpty(li.get(1)) &&
                        isObjectEmpty(li.get(2)))
                    continue;
                list.add(li);
            }
        }

//        work.close();

        log.info(">> parse excel end.");
        return list;
    }

    public static Boolean isObjectEmpty(Object obj) {
        if (obj == null)
            return true;
        if (obj == "")
            return true;
        if ((obj instanceof List)) {
            return ((List) obj).size() == 0;
        }
        if ((obj instanceof String)) {
            return ((String) obj).trim().equals("");
        }
        return false;
    }

    /**
     * 判断文件格式
     *
     * @param in
     * @param fileName
     * @return
     */
    private static Workbook getWorkbook(InputStream in, String fileName) {
        Workbook book = null;
        String fileType = fileName.substring(fileName.lastIndexOf("."));
        try {
            if (".xls".equals(fileType)) {
                book = new HSSFWorkbook(in);
            } else if (".xlsx".equals(fileType)) {
                book = new XSSFWorkbook(in);
            } else {
                throw new Exception("上传 excel 文件后缀不正确.");
            }
            in.close();
        } catch (IOException e) {
            log.info("导入失败,请检查文件内容", e);
            throw new DcServiceException("导入失败,请检查文件内容");
        } catch (Exception e) {
            log.info("导入失败,请检查文件内容", e);
            throw new DcServiceException("导入失败,请检查文件内容");
        }
        return book;
    }

    /**
     *  判断是否为空行
     *
     * @param row
     * @return
     */
    private static Boolean isEmptyRow(Row row) {
        if (row == null || row.toString().isEmpty()) {
            return true;
        } else {
            Iterator<Cell> it = row.iterator();
            boolean emptyFlag = true;
            while (it.hasNext()) {
                Cell cell = it.next();
                if (cell.getCellType() == Cell.CELL_TYPE_BLANK) {
                    emptyFlag = true;
                } else {
                    emptyFlag = false;
                    break;
                }
                return emptyFlag;
            }
        }
        return false;
    }
}
