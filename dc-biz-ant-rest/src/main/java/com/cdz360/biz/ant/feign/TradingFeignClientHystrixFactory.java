package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.domain.ChargerOrder;
import com.cdz360.biz.ant.domain.request.PlugDetailRequest;
import com.cdz360.biz.ant.domain.vo.ChargerOrderFinishVo;
import com.cdz360.biz.ant.domain.vo.PlugDetailVo;
import com.cdz360.biz.model.cus.wallet.param.CreateDepositOrderParam;
import com.cdz360.biz.model.trading.order.po.ChargerOrderTimeDivision;
import com.cdz360.biz.model.trading.order.po.PayBillPo;
import com.chargerlinkcar.framework.common.domain.NoCardPayAccountInfo;
import com.chargerlinkcar.framework.common.domain.OrderTimeDivision;
import com.chargerlinkcar.framework.common.domain.OrderTimeDivisionParam;
import com.chargerlinkcar.framework.common.domain.invoice.DcInvoiceOrderVo;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.domain.param.OrderStopBiParam;
import com.chargerlinkcar.framework.common.domain.pay.PaySign;
import com.chargerlinkcar.framework.common.domain.request.ExpectLimitSocReq;
import com.chargerlinkcar.framework.common.domain.vo.OrderInfoElec;
import com.chargerlinkcar.framework.common.domain.vo.OrderStopBiVo;
import com.chargerlinkcar.framework.common.domain.vo.UpdateOrderVo;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2018/11/19
 */
@Slf4j
@Component
public class TradingFeignClientHystrixFactory implements FallbackFactory<TradingFeignClient> {

    @Override
    public TradingFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_TRADING,
            throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_TRADING,
            throwable.getStackTrace());

        return new TradingFeignClient() {
//            @Override
//            public ObjectResponse<OrderStats> queryOrderStats(List<Long> commIdList) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }
//
//            @Override
//            public ObjectResponse<OrderStats> queryOrderStatsByDate(List<Long> commIdList, String beginTime, String endTime) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

//            @Override
//            public ObjectResponse<Balance> getBalanceInfo(Long userId) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }
//
//
//            @Override
//            public BaseResponse save(Activity activity) {
//                return RestUtils.serverBusy();
//            }

//            @Override
//            public ListResponse<Activity> queryInfo(Map<String, Object> map) {
//                return RestUtils.serverBusy4ListResponse();
//            }
//
//            @Override
//            public BaseResponse deleteActivityInfo(Long activityId) {
//                return RestUtils.serverBusy();
//            }
//
//            @Override
//            public BaseResponse updateActivityStatus(Activity activity) {
//                return RestUtils.serverBusy();
//            }

//            @Override
//            public ObjectResponse<Activity> queryDetailActivityInfo(Long activityId) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

//            @Override
//            public ListResponse<Payment> queryPaymentList(Long userId, Integer _index, Integer _size, List<Long> commIdList) {
//                return RestUtils.serverBusy4ListResponse();
//            }

//            @Override
//            public ListResponse<ChargerOrderVo> queryChargeOrderList(ChargerOrderParam searchParam) {
//                return RestUtils.serverBusy4ListResponse();
//            }

//            @Override
//            public ObjectResponse<ChargerOrderDataVo> getChargerOrderData(ChargerOrderParam searchParam) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }
//
//            @Override
//            public ObjectResponse<ChargerOrderDetailVo> getChargerOrderDetail(ChargerOrderParam searchParam) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

//            @Override
//            public ObjectResponse<ChargerOrderWithBLOBs> queryOrderDetail(String orderNo, List<Long> commIdList, String commIdChain, Long opCommId) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

            @Override
            public ListResponse<OrderStopBiVo> getOrderStopSummaryList(OrderStopBiParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

//            @Override
//            public BaseResponse endActivityStatus(Activity activity) {
//                return RestUtils.serverBusy();
//            }

//            @Override
//            public BaseResponse updateActivityInfo(Activity activity) {
//                return RestUtils.serverBusy();
//            }

//            @Override
//            public ListResponse<BalanceRecord> findBalanceRecords(Long uid, Long groupUserId, List<Long> commId, Integer _index,
//                                                                  Integer _size, Integer sourceId, Integer transType, String startTime,
//                                                                  String endTime, Integer payChannel, String balanceType, String tradingNo) {
//                return RestUtils.serverBusy4ListResponse();
//            }

            @Override
            public ListResponse<ChargerOrder> selectChargerOrderListByConnectId(
                List<Long> commIdList, Integer _index, Integer _size,
                String siteId, String connectorId, String beginTime, String endTime, String status,
                Integer channelId) {
                return RestUtils.serverBusy4ListResponse();
            }

//            @Override
//            public ListResponse<Payment> queryPayMentList(PaymentRequest payment) {
//                return RestUtils.serverBusy4ListResponse();
//            }

//            @Override
//            public ListResponse<Payment> queryPipelineRecord(FinanceBill financeBill) {
//                return RestUtils.serverBusy4ListResponse();
//            }

//            @Override
//            public ListResponse<JSONObject> queryFinanceSurvey(String startTime, String endTime, String month) {
//                return RestUtils.serverBusy4ListResponse();
//            }

//            @Override
//            public ListResponse<JSONObject> daySurvey(int day) {
//                return RestUtils.serverBusy4ListResponse();
//            }

//            @Override
//            public ListResponse<JSONObject> getNowYearOrderDataByMonth(Map map) {
//                return RestUtils.serverBusy4ListResponse();
//            }
//
//            @Override
//            public ListResponse<JSONObject> getOrderDataByMonth(Map map) {
//                return RestUtils.serverBusy4ListResponse();
//            }

            //            @Override
//            public ObjectResponse<JSONObject> queryOrderCountByMonth(String siteId) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }
            @Override
            public ListResponse<JsonNode> getSiteSurvey(String siteId, String token) {
                return RestUtils.serverBusy4ListResponse();
            }
//            @Override
//            public ListResponse<JSONObject> getSiteTrend(String siteId, String days, String trendType) {
//                return RestUtils.serverBusy4ListResponse();
//            }

//            @Override
//            public ObjectResponse<JSONObject> getSiteSum(String siteId, String startTime, String endTime) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

            @Override
            public ListResponse<ChargerOrderTimeDivision> queryOrderTimeDivisionList(
                String orderNo) {
                return RestUtils.serverBusy4ListResponse();
            }

//            @Override
//            public ListResponse<KVAnalysisVo> getydltj(List<Long> commIdList) {
//                return RestUtils.serverBusy4ListResponse();
//            }
//
//            @Override
//            public ListResponse<KVAnalysisVo> getydlzs(List<Long> commIdList) {
//                return RestUtils.serverBusy4ListResponse();
//            }
//
//
//            @Override
//            public ListResponse<KVAnalysisVo> getrcdlpm(List<Long> commIdList) {
//                return RestUtils.serverBusy4ListResponse();
//            }
//
//
//
//            @Override
//            public ListResponse<KVAnalysisVo> getddtjgl(List<Long> commIdList) {
//                return RestUtils.serverBusy4ListResponse();D
//            }
//
//            @Override
//            public ListResponse<KVAnalysisVo> getddzs(List<Long> commIdList) {
//                return RestUtils.serverBusy4ListResponse();
//            }
//
//            @Override
//            public ObjectResponse<JSONObject> getElectricityByMonth(List<Long> commIdList) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }
//
//            @Override
//            public ObjectResponse<JSONObject> getOrderCountByMonth(List<Long> commIdList) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }
//
//            @Override
//            public JSONObject getChargerTempData(List<Long> commIdList, String boxCode, String nowDate) {
//                return JSON.parseObject(RestUtils.serverBusy().toJsonString());
//            }
//
//            @Override
//            public JSONObject tztjDataInit(List<Long> commIdList) {
//                return JSON.parseObject(RestUtils.serverBusy().toJsonString());
//            }
//
//            @Override
//            public JSONObject getChargerReportData(List<Long> commIdList, Integer _index, Integer _size, String currentType, String sid, String bcCode) {
//                return JSON.parseObject(RestUtils.serverBusy().toJsonString());
//            }

//            @Override
//            public ObjectResponse<Balance> getAccountByUserId(Long uid) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }


            @Override
            public BaseResponse updateOrder(UpdateOrderVo order) {
                return RestUtils.serverBusy();
            }

//            @Override
//            public ObjectResponse queryOrderInfo(String orderNo) {
//                return null;
//            }

            @Override
            public ListResponse<OrderInfoElec> orderInfoElec(List<String> orderNoList) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<ChargerOrderFinishVo> getFinishedOrderDetail(String orderNo) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<PlugDetailVo> getPlugDetail(PlugDetailRequest plugDetailRequest) {
                return RestUtils.serverBusy4ListResponse();
            }

//            @Override
//            public ListResponse<ChargerDetailVo> queryOrderSamplingInfo(String orderNo) {
//                return RestUtils.serverBusy4ListResponse();
//            }

//            @Override
//            public ObjectResponse<Boolean> checkOrderOfCard(String cardChipNo,String commIdChain) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

            @Override
            public ListResponse<String> getNoSettlementCard(ChargerOrderParam param) {
                return null;
            }

            @Override
            public ListResponse<String> getNoSettlementVin(ChargerOrderParam param) {
                return null;
            }

            @Override
            public ObjectResponse<String> refundForAbnormal(String orderNo) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<ChargerOrder> getUnfinishOrderBysiteId(String siteId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<ChargerOrder> getUnfinishOrderByCorpId(Long corpId) {
                return RestUtils.serverBusy4ListResponse();
            }


            @Override
            public ListResponse<OrderTimeDivision> orderTimeDivisionData(
                OrderTimeDivisionParam req) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<DcInvoiceOrderVo> queryChargerOrderList(String token,
                String username, Integer status, Long customerId, Long invoicedId, Integer index,
                Integer size, String createTime, String endTime) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse payNoCardOrderBySiteId(String siteId,
                NoCardPayAccountInfo accountInfo) {
                log.info("场站无卡启动订单支付熔断: {}, siteId = {}, param = {}",
                    "payNoCardOrderBySiteId", siteId, JsonUtils.toJsonString(accountInfo));
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse postDeposit(PayBillPo po) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<PaySign> createDepositOrder(CreateDepositOrderParam param) {
                log.info("创建支付单熔断: {}, param = {}", "createDepositOrder",
                    JsonUtils.toJsonString(param));
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse changeExpectLimitSoc(ExpectLimitSocReq req) {
                return RestUtils.serverBusy();
            }
        };
    }


}
