package com.cdz360.biz.ant.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 设置场站可开票标记实体
 */
@Data
@Accessors(chain = true)
public class InvoicedSiteValidYNVo implements Serializable {

    @Schema(description = "场站ID列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> ids;

    private Boolean invoicedValid;

    @Deprecated(since = "20241018")
    private Boolean platformInvoicedValid;

    @Schema(description = "场站名称列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> siteNameList; // 用于记录日志
}
