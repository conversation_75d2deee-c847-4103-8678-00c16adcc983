package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.service.camera.CameraBizService;
import com.cdz360.biz.ant.service.sysLog.CustomerSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.site.type.SiteStatus;
import com.cdz360.biz.model.trading.camera.dto.CameraSiteDto;
import com.cdz360.biz.model.trading.camera.param.CameraRecorderParam;
import com.cdz360.biz.model.trading.camera.param.CameraVideoParam;
import com.cdz360.biz.model.trading.camera.param.ListCameraParam;
import com.cdz360.biz.model.trading.camera.vo.CameraRecorderVo;
import com.cdz360.biz.model.trading.camera.vo.CameraVo;
import com.cdz360.biz.model.trading.camera.vo.HkVideoUrl;
import com.cdz360.biz.model.trading.camera.vo.OpInitCameraVo;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.utils.feign.camera.CameraFeignClient;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.Arrays;
import java.util.List;

/**
 *
 * @since 7/28/2021 5:21 PM
 * <AUTHOR>
 */
@RequestMapping("/api/camera")
@RestController
@Slf4j
@Tag(name = "场站视频监控接口", description = "场站视频监控接口")
public class CameraRest {

    @Autowired
    private CameraBizService cameraBizService;

    @Autowired
    private CameraFeignClient cameraFeignClient;

    @Autowired
    private CustomerSysLogService customerSysLogService;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Schema(description = "获取场站摄像头列表")
    @PostMapping(value = "/getCameraList")
    public Mono<ListResponse<CameraVo>> findBiSiteOrderList(
        ServerHttpRequest request,
        @RequestBody ListCameraParam param) {
        log.info("获取场站摄像头列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        return cameraFeignClient.siteCameraList(param);
    }

    @Schema(description = "获取摄像头场站列表")
    @PostMapping(value = "/getCameraSiteList")
    public Mono<ListResponse<CameraSiteDto>> getCameraSiteList(
        ServerHttpRequest request,
        @RequestBody ListCameraParam param) {
        param.setGids(AntRestUtils.getSysUserGids(request));
        if (CollectionUtils.isEmpty(param.getGids())) { // 优先使用场站组限定查询范围,场站组为空时,才限定idChain
            param.setIdChain(AntRestUtils.getCommIdChainAndFilter(request));
        }
        log.info("获取场站摄像头列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        return cameraBizService.getCameraSiteList(param)
            .map(list -> RestUtils.buildListResponse(list));
    }

    @Schema(description = "同步摄像头信息")
    @PostMapping(value = "/syncCameraBySiteId")
    public Mono<BaseResponse> syncCameraBySiteId(
        ServerHttpRequest request,
        @RequestBody ListCameraParam param) {
        log.info("同步摄像头信息: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        return cameraFeignClient.syncCameraBySiteId(param.getSiteId());
    }

    @Schema(description = "同步商户下所有摄像头信息")
    @PostMapping(value = "/syncCameraByIdChain")
    public Mono<BaseResponse> syncCameraByIdChain(
        ServerHttpRequest request,
        @RequestBody ListCameraParam param) {
        log.info("同步商户下所有摄像头信息: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        String commIdChain = AntRestUtils.getCommIdChain(request);
        return cameraFeignClient.syncCameraByIdChain(commIdChain);
    }

    @Schema(description = "获取场站摄像头回放")
    @PostMapping(value = "/getCameraVideoUrl")
    public Mono<ObjectResponse<HkVideoUrl>> getCameraVideoUrl(
        ServerHttpRequest request,
        @RequestBody CameraVideoParam param) {
        log.info("获取场站摄像头回放: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        return cameraFeignClient.getCameraVideoUrl(param);
    }

    @Operation(summary = "新增监控硬盘录像机")
    @PostMapping(value = "/addRecorder")
    public Mono<ObjectResponse<Boolean>> addRecorder(
        ServerHttpRequest request,
        @RequestBody CameraRecorderVo param) {
        log.info("新增监控硬盘录像机: siteId = {}", JsonUtils.toJsonString(param));
        return cameraFeignClient.addRecorder(param).doOnNext(e -> {
            customerSysLogService.addRecorder(param.getSiteName(),
                param.getDeviceName(),
                param.getDeviceSerial(),
                request);
        });
    }

    @Operation(summary = "删除监控硬盘录像机")
    @PostMapping(value = "/deleteRecorder")
    public Mono<BaseResponse> deleteRecorder(
        ServerHttpRequest request,
        @RequestBody CameraRecorderVo param) {
        log.info("删除监控硬盘录像机: siteId = {}", JsonUtils.toJsonString(param));
        return cameraFeignClient.deleteRecorder(param).doOnNext(e -> {
            customerSysLogService.deleteRecorder(param.getSiteName(),
                param.getDeviceName(),
                param.getDeviceSerial(),
                request);
        });
    }

    @Operation(summary = "查询监控硬盘录像机")
    @PostMapping(value = "/listRecorder")
    public Mono<ListResponse<CameraRecorderVo>> listRecorder(ServerHttpRequest request,
        @RequestBody CameraRecorderParam param) {
        String commIdChain = AntRestUtils.getCommIdChain(request);
        param.setIdChain(commIdChain);
        ListSiteParam listSiteParam = new ListSiteParam();
        listSiteParam.setGids(AntRestUtils.getSysUserGids(request));
        listSiteParam.setStatusList(Arrays.asList(new SiteStatus[] {SiteStatus.OPENING, SiteStatus.ONLINE, SiteStatus.UNAVAILABLE}));
        List<String> siteIds = dataCoreFeignClient.getSiteListByGids(listSiteParam).getData();
        param.setSiteIds(siteIds);
        log.info("查询监控硬盘录像机: siteId = {}", JsonUtils.toJsonString(param));
        return cameraFeignClient.listRecorder(param);
    }

    @Operation(summary = "修改监控硬盘录像机信息")
    @PostMapping(value = "/updateRecorder")
    public Mono<ObjectResponse<Boolean>> updateRecorder(ServerHttpRequest request,
        @RequestBody CameraRecorderVo param) {
        log.info("修改监控硬盘录像机信息: siteId = {}", JsonUtils.toJsonString(param));
        return cameraFeignClient.updateRecorder(param).doOnNext(e -> {
            customerSysLogService.updateRecorder(param.getSiteName(),
                param.getDeviceName(),
                request);
        });
    }

    @Schema(description = "编辑摄像头通道信息")
    @PostMapping(value = "/updateChannel")
    public Mono<ObjectResponse<Boolean>> updateChannel(ServerHttpRequest request,
        @RequestBody CameraVo param) {
        log.info("编辑摄像头通道信息: param = {}", JsonUtils.toJsonString(param));
        return cameraFeignClient.updateChannel(param).doOnNext(e -> {
            customerSysLogService.updateChannel(param.getSiteName(),
                param.getDeviceName(),
                request);
        });
    }

    @Schema(description = "获取定制大屏初始摄像头地址资源")
    @PostMapping(value = "/getInitLiveAddressUseForOp")
    public Mono<ListResponse<OpInitCameraVo>> getInitLiveAddressUseForOp(
        ServerHttpRequest request) {
        log.info("获取大屏初始摄像头地址资源 {}", LoggerHelper2.formatEnterLog(request, false));
        return cameraFeignClient.getInitLiveAddressUseForOp();
    }
}