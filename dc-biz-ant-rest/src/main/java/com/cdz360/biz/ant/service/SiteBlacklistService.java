package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.model.cus.site.param.ListSiteBlacklistParam;
import com.cdz360.biz.model.cus.site.param.SiteBlacklistEnableParam;
import com.cdz360.biz.model.cus.site.vo.SiteBlacklistVo;
import com.cdz360.biz.model.site.type.OvertimeParkingChargeUserType;
import com.cdz360.biz.model.trading.order.dto.CusCorpOrderBiDto;
import com.cdz360.biz.model.trading.order.dto.CusOrderBiDto;
import com.cdz360.biz.model.trading.order.param.ListCusOrderBiParam;
import com.cdz360.biz.model.trading.site.po.OvertimeParkFeeOrderPo;
import com.cdz360.biz.model.trading.site.type.OvertimeParkingChargePartType;
import com.chargerlinkcar.framework.common.domain.SitePersonaliseDTO;
import com.chargerlinkcar.framework.common.feign.UserFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SiteBlacklistService {

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    public ListResponse<SiteBlacklistVo> findSiteBlacklist(ListSiteBlacklistParam param) {
        if (StringUtils.isBlank(param.getSiteId()) && param.getCusId() == null && param.getCorpId() == null) {
            throw new DcArgumentException("需要指定场站Id值或用户id");
        }
        param.setTotal(Boolean.TRUE); // 默认统计总数

        ListResponse<SiteBlacklistVo> blacklist = this.userFeignClient.findSiteBlacklist(param);
        FeignResponseValidate.check(blacklist);

        if (CollectionUtils.isEmpty(blacklist.getData())) {
            log.info("<< no data");
            return RestUtils.buildListResponse(new ArrayList<>());
        }

        // 占位订单获取
        List<String> orderNoList = blacklist.getData().stream()
                .map(SiteBlacklistVo::getLatestOrderNo)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        ListCusOrderBiParam orderBiParam = new ListCusOrderBiParam();
        orderBiParam.setOrderNoList(orderNoList);

        ListResponse<OvertimeParkFeeOrderPo> cusOvertimeParkOrderBiList =
                dataCoreFeignClient.getCusOvertimeParkOrderBiList(orderBiParam);
        FeignResponseValidate.check(cusOvertimeParkOrderBiList);

        if(CollectionUtils.isNotEmpty(cusOvertimeParkOrderBiList.getData())) {
            Map<String, OvertimeParkFeeOrderPo> collect = cusOvertimeParkOrderBiList.getData()
                    .stream()
                    .collect(Collectors.toMap(OvertimeParkFeeOrderPo::getOrderNo, o -> o));
            blacklist.getData().stream().forEach(e -> {
                OvertimeParkFeeOrderPo overtimeParkFeeOrderPo = collect.get(e.getLatestOrderNo());
                if(overtimeParkFeeOrderPo != null) {
                    e.setOvertimeParkFee(Boolean.TRUE);
//                    e.setOvertimeParkOrderNo(overtimeParkFeeOrderPo.getOrderNo());
                    e.setChargeStopTime(overtimeParkFeeOrderPo.getStopTime());
//                    e.setOvertimeParkFreeTime(overtimeParkFeeOrderPo.getFreeTime());
                    e.setOvertimeParkCalFromTime(overtimeParkFeeOrderPo.getCalFromTime());
                    e.setOvertimeParkCalToTime(overtimeParkFeeOrderPo.getCalToTime());
                    e.setOvertimeParkingPrice(overtimeParkFeeOrderPo.getParkingPrice());
                    e.setOvertimeParkingFee(overtimeParkFeeOrderPo.getParkingFee());
                    e.setOvertimePayStatus(overtimeParkFeeOrderPo.getStatus());
                }
            });
        }

        if(StringUtils.isNotBlank(param.getSiteId())) {
            ObjectResponse<SitePersonaliseDTO> personalise = dataCoreFeignClient.getPersonalise(param.getSiteId());
            FeignResponseValidate.check(personalise);
            if (personalise.getData().getParkTimeoutFee()) {
                blacklist.getData().forEach(e -> {
                    e.setOvertimeParkFreeTime(personalise.getData().getOvertimeParkingTime());
                    e.setOvertimeParkFreeNum(personalise.getData().getOvertimeParkingNum());
                });
                if(OvertimeParkingChargePartType.ALL.equals(personalise.getData().getOvertimeParkingChargePartType())) {
                    // 所有用户需付费
                    blacklist.getData().forEach(e ->
                        e.setOvertimeParkingChargeUserType(OvertimeParkingChargeUserType.CHARGE));
                } else if(OvertimeParkingChargePartType.PART.equals(personalise.getData().getOvertimeParkingChargePartType())) {
                    // 部分用户需付费
                    blacklist.getData()
                        .stream()
                        .filter(e -> e.getOvertimeParkingChargeUserType() == null)
                        // 其他用户免费
                        .forEach(e -> e.setOvertimeParkingChargeUserType(OvertimeParkingChargeUserType.FREE));
                }
            }
        }

        // 查询用户在该场站的充电情况
        List<Long> uidList = blacklist.getData().stream()
                .map(SiteBlacklistVo::getUid)
                .filter(e -> e != null && e > 0)
                .collect(Collectors.toList());
        List<Long> corpIdList = blacklist.getData().stream()
                .map(SiteBlacklistVo::getCorpId)
                .filter(e -> e != null && e > 0)
                .collect(Collectors.toList());
//        ListCusOrderBiParam orderBiParam = new ListCusOrderBiParam();
//        orderBiParam.setUidList(uidList);
//        orderBiParam.setSiteId(param.getSiteId());

        // 仅获取场站下的用户订单统计
        orderBiParam.setUidList(uidList);
        orderBiParam.setCorpIdList(corpIdList);
        orderBiParam.setSiteId(param.getSiteId());

        ObjectResponse<CusCorpOrderBiDto> cusAndCorpOrderBiList = this.dataCoreFeignClient.getCusAndCorpOrderBiList(orderBiParam);
        FeignResponseValidate.check(cusAndCorpOrderBiList);

        this.fillOrderBiData(blacklist.getData(),
                cusAndCorpOrderBiList.getData().getUserBi(),
                cusAndCorpOrderBiList.getData().getCorpBi());

//        if (CollectionUtils.isEmpty(orderBiList.getData())) {
//            return blacklist;
//        }
//
//        Map<Long, CusOrderBiDto> biMap = orderBiList.getData()
//                .stream().collect(Collectors.toMap(CusOrderBiDto::getUid, o -> o));
//
//        blacklist.getData().forEach(item -> {
//            // 更新用户的充电信息
//            CusOrderBiDto dto = biMap.get(item.getUid());
//            if(dto != null) {
//                item.setOrderNum(dto.getOrderNum());
//                item.setElec(dto.getElec())
//                        .setElecFee(dto.getElecFee())
//                        .setServFee(dto.getServFee());
//            } else {
//                item.setOrderNum(0L);
//                item.setElec(BigDecimal.ZERO)
//                        .setElecFee(BigDecimal.ZERO)
//                        .setServFee(BigDecimal.ZERO);
//            }
//        });
        return blacklist;
    }

    private void fillOrderBiData(List<SiteBlacklistVo> list,
                                 List<CusOrderBiDto> userBiList,
                                 List<CusOrderBiDto> corpBiList) {
        if(CollectionUtils.isEmpty(list)) {
            return;
        }
        if(CollectionUtils.isNotEmpty(userBiList)) {
            Map<Long, CusOrderBiDto> biMap = userBiList
                    .stream().collect(Collectors.toMap(CusOrderBiDto::getUid, o -> o));
            list.stream().filter(e -> e.getUid() != null && e.getUid() > 0).forEach(item -> {
                // 更新用户的充电信息
                CusOrderBiDto dto = biMap.get(item.getUid());
                if(dto != null) {
                    item.setOrderNum(dto.getOrderNum());
                    item.setElec(dto.getElec())
                            .setElecFee(dto.getElecFee())
                            .setServFee(dto.getServFee())
                        .setUnPayParkingCount(dto.getUnPayParkingCount())
                        .setUnPayParkingFee(dto.getUnPayParkingFee());
                } else {
                    item.setOrderNum(0L);
                    item.setElec(BigDecimal.ZERO)
                            .setElecFee(BigDecimal.ZERO)
                            .setServFee(BigDecimal.ZERO)
                        .setUnPayParkingCount(0)
                        .setUnPayParkingFee(BigDecimal.ZERO);
                }
            });
        }
        if(CollectionUtils.isNotEmpty(corpBiList)) {
            Map<Long, CusOrderBiDto> biMap = corpBiList
                    .stream().collect(Collectors.toMap(CusOrderBiDto::getCorpId, o -> o));
            list.stream().filter(e -> e.getCorpId() != null && e.getCorpId() > 0).forEach(item -> {
                // 更新用户的充电信息
                CusOrderBiDto dto = biMap.get(item.getCorpId());
                if(dto != null) {
                    item.setOrderNum(dto.getOrderNum());
                    item.setElec(dto.getElec())
                        .setElecFee(dto.getElecFee())
                        .setServFee(dto.getServFee())
                        .setUnPayParkingCount(dto.getUnPayParkingCount())
                        .setUnPayParkingFee(dto.getUnPayParkingFee());
                } else {
                    item.setOrderNum(0L);
                    item.setElec(BigDecimal.ZERO)
                        .setElecFee(BigDecimal.ZERO)
                        .setServFee(BigDecimal.ZERO)
                        .setUnPayParkingCount(0)
                        .setUnPayParkingFee(BigDecimal.ZERO);
                }
            });
        }
    }

    public void addToBlacklist(SiteBlacklistEnableParam param) {
        param.setEnable(Boolean.TRUE);
        this.enable(param);
    }

    public BaseResponse addToBlacklistPhone(SiteBlacklistEnableParam param) {
        param.setEnable(Boolean.TRUE);
        return this.enablePhone(param);
    }

    public void rmFromBlacklist(SiteBlacklistEnableParam param) {
        param.setEnable(Boolean.FALSE);
        this.enable(param);
    }

    private void enable(SiteBlacklistEnableParam param) {
        SiteBlacklistEnableParam.checkValueEnable(param);

        this.userFeignClient.enable(param);
    }

    private BaseResponse enablePhone(SiteBlacklistEnableParam param) {
        SiteBlacklistEnableParam.checkValueEnable(param);

        return this.userFeignClient.enablePhone(param);
    }

    public ListResponse<SiteBlacklistVo> cusBlackSite(Long uid, String siteName) {
        return this.userFeignClient.cusBlackSite(uid, siteName);
    }

    public void batchRmFromBlacklist(List<Long> idList) {
        this.userFeignClient.batchRmFromBlacklist(idList);
    }
}
