package com.cdz360.biz.ant.rest.parts;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.service.parts.PartsService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.ant.utils.FileUtil;
import com.cdz360.biz.model.parts.dto.PartsBatchImportDto;
import com.cdz360.biz.model.parts.param.ListPartsParam;
import com.cdz360.biz.model.parts.param.ListPartsParamEx;
import com.cdz360.biz.model.parts.param.PartsBrokenParam;
import com.cdz360.biz.model.parts.param.PartsEditParam;
import com.cdz360.biz.model.parts.param.PartsOpBaseParam;
import com.cdz360.biz.model.parts.param.PartsRollbackParam;
import com.cdz360.biz.model.parts.param.PartsTransReviewParam;
import com.cdz360.biz.model.parts.vo.PartsImportItem;
import com.cdz360.biz.model.parts.vo.PartsImportVo;
import com.cdz360.biz.model.parts.vo.PartsTransTraceVo;
import com.cdz360.biz.model.parts.vo.PartsVo;
import com.cdz360.biz.model.parts.vo.PartsWithVo;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@Tag(name = "物料相关接口", description = "物料")
@RestController
@RequestMapping("/api/parts")
public class PartsRest {

    @Autowired
    private PartsService partsService;

    @Operation(summary = "物料发货")
    @PostMapping(value = "/transport")
    public Mono<ListResponse<PartsVo>> partsTransport(
        ServerHttpRequest request, @RequestBody PartsImportItem param) {
        log.info("物料发货: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        param.setOpUid(AntRestUtils.getSysUid(request));
        return partsService.partsTransport(param);
    }

    @Operation(summary = "物料签收")
    @PostMapping(value = "/receive")
    public Mono<ListResponse<PartsVo>> partsReceive(
        ServerHttpRequest request, @RequestBody PartsBrokenParam param) {
        log.info("物料签收: {}", LoggerHelper2.formatEnterLog(request));
        param.setOpUid(AntRestUtils.getSysUid(request));
        return partsService.partsReceive(param);
    }

    @Operation(summary = "物料报废")
    @PostMapping(value = "/broken")
    public Mono<BaseResponse> partsBroken(
        ServerHttpRequest request, @RequestBody PartsBrokenParam param) {
        log.info("物料报废: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), param);
        param.setOpUid(AntRestUtils.getSysUid(request));
        return partsService.partsBroken(param);
    }

    @Operation(summary = "物料退回")
    @PostMapping(value = "/rollback")
    public Mono<BaseResponse> partsRollback(
        ServerHttpRequest request, @RequestBody PartsRollbackParam param) {
        log.info("物料退回: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), param);
        param.setOpUid(AntRestUtils.getSysUid(request));
        return partsService.partsRollback(param);
    }

    @Operation(summary = "物料编辑", description = "编辑")
    @PostMapping(value = "/edit")
    public Mono<BaseResponse> partsEdit(
        ServerHttpRequest request, @RequestBody PartsEditParam param) {
        log.info("物料状态变更: {}, param = {}", LoggerHelper2.formatEnterLog(request), param);
        param.setOpUid(AntRestUtils.getSysUid(request));
        return partsService.partsEdit(param);
    }

    @Operation(summary = "物料调拨审批(同意/拒绝)")
    @PostMapping(value = "/transReview")
    public Mono<ListResponse<PartsVo>> partsTransReview(
        ServerHttpRequest request, @RequestBody PartsTransReviewParam param) {
        log.info("物料调拨审批(同意/拒绝): {}, param = {}",
            LoggerHelper2.formatEnterLog(request), param);
        param.setOpUid(AntRestUtils.getSysUid(request));
        return partsService.partsTransReview(param);
    }

    @Operation(summary = "物料调拨申请")
    @PostMapping(value = "/transApply")
    public Mono<ListResponse<PartsVo>> partsTransApply(
        ServerHttpRequest request, @RequestBody PartsOpBaseParam param) {
        log.info("物料调拨申请: {}", param);
        param.setOpUid(AntRestUtils.getSysUid(request));
        return partsService.partsTransApply(param);
    }

    @Operation(summary = "物料调拨申请取消")
    @PostMapping(value = "/transApplyCancel")
    public Mono<ObjectResponse<PartsVo>> partsTransApplyCancel(
        ServerHttpRequest request, @RequestBody PartsOpBaseParam param) {
        log.info("物料调拨申请取消: {}", param);
        param.setOpUid(AntRestUtils.getSysUid(request));
        return partsService.partsTransApplyCancel(param);
    }

    @Operation(summary = "物料调拨追溯")
    @GetMapping(value = "/transTrace")
    public Mono<ObjectResponse<PartsTransTraceVo>> partsTransTrace(
        ServerHttpRequest request, @RequestParam("code") String code) {
        log.info("物料调拨追溯: {}", LoggerHelper2.formatEnterLog(request));
        return partsService.partsTransTrace(code);
    }

    @Operation(summary = "物料批量导入")
    @PostMapping(value = "/batchImport")
    public Mono<BaseResponse> partsBatchImport(
        ServerHttpRequest request, @RequestBody PartsBatchImportDto dto) {
        log.info("物料批量导入: {}, items.size = {}",
            LoggerHelper2.formatEnterLog(request), dto.getItems().size());
        dto.setOpUid(AntRestUtils.getSysUid(request));
        return partsService.partsBatchImport(dto);
    }

    @Operation(summary = "个人物料库数量")
    @GetMapping(value = "/ownCount")
    public Mono<ObjectResponse<Long>> partsCount(ServerHttpRequest request) {
        log.info("个人物料库数量: {}", LoggerHelper2.formatEnterLog(request));
        return partsService.partsCount(AntRestUtils.getSysUid(request));
    }

    @Operation(summary = "个人物料库列表")
    @PostMapping(value = "/ownPartsList")
    public Mono<ListResponse<PartsWithVo>> ownPartsList(
        ServerHttpRequest request, @RequestBody ListPartsParam param) {
        log.info("个人物料库列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        param.setUidList(List.of(AntRestUtils.getSysUid(request)));
        return partsService.ownPartsList(param);
    }

    @Operation(summary = "当前用户运维组其他人员物料库列表")
    @PostMapping(value = "/ownYwGroupPartsList")
    public Mono<ListResponse<PartsWithVo>> ownYwGroupPartsList(
        ServerHttpRequest request, @RequestBody ListPartsParam param) {
        log.info("当前用户运维组其他人员物料库列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        return partsService.ownYwGroupPartsList(AntRestUtils.getSysUid(request), param);
    }

    @Operation(summary = "当前用户跨运维组人员物料库列表")
    @PostMapping(value = "/otherYwGroupPartsList")
    public Mono<ListResponse<PartsWithVo>> otherYwGroupPartsList(
        ServerHttpRequest request, @RequestBody ListPartsParam param) {
        log.info("当前用户跨运维组人员物料库列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        return partsService.otherYwGroupPartsList(AntRestUtils.getSysUid(request), param);
    }

    @Operation(summary = "他人物料库列表(除当前用户)")
    @PostMapping(value = "/otherYwPartsList")
    public Mono<ListResponse<PartsWithVo>> otherYwPartsList(
        ServerHttpRequest request, @RequestBody ListPartsParam param) {
        log.info("他人物料库列表(除当前用户): {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        param.setExUidList(List.of(AntRestUtils.getSysUid(request)));
        return partsService.otherYwPartsList(param);
    }

    @Operation(summary = "物料库列表")
    @PostMapping(value = "/findPartsList")
    public Mono<ListResponse<PartsWithVo>> findPartsList(
        ServerHttpRequest request, @RequestBody ListPartsParam param) {
        log.info("物料库列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        return partsService.findPartsList(param);
    }

    @Operation(summary = "物料导入模板解析")
    @PostMapping(value = "/parseExcel")
    public Mono<ObjectResponse<PartsImportVo>> parseExcel(
        ServerHttpRequest request, @RequestPart FilePart file) {
        FileUtil.checkExcelFile(file);
        return partsService.parsePartsExcel(file);
    }

    @Operation(summary = "物料导出")
    @PostMapping(value = "/exportExcel")
    public Mono<ObjectResponse<ExcelPosition>> partsExportExcel(
        ServerHttpRequest request, @RequestBody ListPartsParamEx param) {
        log.info("物料导出: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        IotAssert.isNotNull(param.getPage(), "请提供页面信息");

        Long sysUid = AntRestUtils.getSysUid(request);
        return partsService.partsExportExcel(sysUid, param);
    }

}
