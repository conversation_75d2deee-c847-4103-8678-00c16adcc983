package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.service.sysLog.CommercialSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.auth.zft.dto.ZftDto;
import com.cdz360.biz.auth.zft.param.ListZftParam;
import com.cdz360.biz.auth.zft.vo.ZftVo;
import com.chargerlinkcar.framework.common.domain.CommercialSample;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "直付商户相关接口", description = "直付商户相关接口")
public class ZftRest extends BaseController {

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;
    @Autowired
    private CommercialSysLogService commercialSysLogService;

    @Operation(summary = "获取直付商家列表")
    @PostMapping(value = "/api/zft/zftList")
    public Mono<ListResponse<ZftVo>> zftList(
            ServerHttpRequest request,
            @RequestBody ListZftParam param) {
        log.info("获取直付商家列表: {}, param = {}",
                LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        param.setTopCommId(AntRestUtils.getTopCommId(request));

        if (StringUtils.isNotBlank(param.getCommIdChain())) {
            param.setCommIdChain(AntRestUtils.getCommIdChain(request));
        }
        return Mono.just(authCenterFeignClient.zftList(AntRestUtils.getToken2(request), param));
    }

    @Operation(summary = "获取直付商家的信息")
    @GetMapping(value = "/api/zft/getZft")
    public ObjectResponse<ZftVo> getZft(
            ServerHttpRequest request,
            @Parameter(name = "直付商家ID", required = true) @RequestParam(value = "id") Long id) {
        log.info("获取直付商家的信息: {}, id = {}",
                LoggerHelper2.formatEnterLog(request), id);
        return authCenterFeignClient.getZft(AntRestUtils.getToken2(request), id);
    }

    @Operation(summary = "更新直付商家信息")
    @PostMapping(value = "/api/zft/updateZft")
    public ObjectResponse<Long> updateZft(
            ServerHttpRequest request,
            @RequestBody ZftDto dto) {
        log.info("更新直付商家信息: {}, dto = {}",
                LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(dto));

        // 参数校验
        ZftDto.checkParam(dto);

        // 获取登录用户相关信息
        CommercialSample comm = this.getCommercialSample2(request);
        if (ObjectUtils.isEmpty(comm)) {
            throw new DcArgumentException("当前操作用户信息不存在，请重新登录");
        }

        Long opUid = comm.getId();
        if (opUid == null || opUid <= 0) {
            throw new DcArgumentException("当前操作用户Id不存在");
        }

        Long topCommId = AntRestUtils.getTopCommId(request);
        dto.setTopCommId(topCommId);

        // 操作人信息
        dto.setUpdateOpId(opUid);
        dto.setUpdateOpName(comm.getUsername());

        ObjectResponse<Long> res = authCenterFeignClient.updateZft(AntRestUtils.getToken2(request), dto);
        if (dto.getId() == null) {
            commercialSysLogService.addZftLog(dto.getName(), request);
        } else {
            commercialSysLogService.updateZftLog(dto.getName(), request);
        }
        return res;
    }

}
