package com.cdz360.biz.ant.service.sysLog;

import com.cdz360.base.model.base.vo.KvObject;
import com.cdz360.biz.auth.user.type.LogOpType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class CusPostSysLogService {

    @Autowired
    private BaseSysLogService sysUserLogService;

    /**
     * 评论回复
     *
     * @param orderNo 充电订单号
     * @param request
     */
    public void cusPostReply(String orderNo, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.MODIFY,
                List.of(new KvObject("评论订单号", orderNo)),
                request);
    }

    /**
     * 评论状态变更
     *
     * @param orderNoList
     * @param request
     */
    public void cusPostChangeOpen(List<String> orderNoList, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.MODIFY_STATUS,
                List.of(new KvObject("评论订单号", orderNoList)),
                request);
    }

    /**
     * 评论标签变更
     *
     * @param orderNoList
     * @param request
     */
    public void cusPostChangeTag(List<String> orderNoList, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.MODIFY,
                List.of(new KvObject("评论订单号", orderNoList)),
                request);
    }
}
