package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.param.ListDownloadJobParam;
import com.cdz360.biz.model.download.vo.DownloadJobVo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE,
    fallbackFactory = DataCoreDownloadFileFeignHystrix.class)
public interface DataCoreDownloadFileFeignClient {

    // 申请下载
    @PostMapping(value = "/dataCore/download/apply")
    Mono<ObjectResponse<ExcelPosition>> downloadFileApply(
        @RequestBody DownloadApplyParam param);

    // 申请打印
    @PostMapping(value = "/dataCore/download/printApply")
    Mono<ObjectResponse<ExcelPosition>> downloadFilePrintApply(
            @RequestBody DownloadApplyParam param);

    // 下载任务列表
    @PostMapping(value = "/dataCore/download/applyList")
    Mono<ListResponse<DownloadJobVo>> downloadApplyList(
        @RequestBody ListDownloadJobParam param);
}
