package com.cdz360.biz.ant.rest.tj;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.biz.ant.service.tj.TjSurveyService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.tj.kc.param.ListTjSurveyParam;
import com.cdz360.biz.model.tj.kc.param.RepeatSurveyParam;
import com.cdz360.biz.model.tj.kc.param.TjSurveyBiParam;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyBiVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "勘察站点相关操作接口", description = "勘察站点相关操作接口")
@Slf4j
@RestController
@RequestMapping("/api/tj/survey")
public class TjSurveyRest {

    @Autowired
    private TjSurveyService tjSurveyService;

    @Operation(summary = "")
    @PostMapping(value = "/getAllOwnSurvey")
    public Mono<ListResponse<TjSurveyVo>> getAllOwnSurvey(
        ServerHttpRequest request,
        @RequestBody BaseListParam param) {
        log.info("获取用户的勘察场站列表 {} param = {}",
            LoggerHelper2.formatEnterLog(request, false),
            param);
        val areaParam = new ListTjSurveyParam();
        BeanUtils.copyProperties(param, areaParam);
        areaParam.setUid(AntRestUtils.getSysUid(request));
        return tjSurveyService.findTjSurvey(areaParam);
    }

    @Operation(summary = "")
    @PostMapping(value = "/findTjSurvey")
    public Mono<ListResponse<TjSurveyVo>> findTjSurvey(
        ServerHttpRequest request,
        @RequestBody ListTjSurveyParam param) {
        log.info("获取勘察场站列表 {} param = {}", LoggerHelper2.formatEnterLog(request, false),
            param);
        return tjSurveyService.findTjSurvey(param);
    }

    @Operation(summary = "")
    @PostMapping(value = "/tjSurveyBi")
    public Mono<ObjectResponse<TjSurveyBiVo>> tjSurveyBi(ServerHttpRequest request,
        @RequestBody TjSurveyBiParam param) {
        log.info("获取投建场站勘察汇总信息 {} param = {}",
            LoggerHelper2.formatEnterLog(request, false),
            param);
        param.setUid(AntRestUtils.getSysUid(request));
        return tjSurveyService.tjSurveyBi(param);
    }

    @Operation(summary = "")
    @PostMapping(value = "/repeatSurvey")
    public Mono<ListResponse<TjSurveyVo>> repeatSurvey(
        ServerHttpRequest request,
        @RequestBody RepeatSurveyParam param) {
        log.info("重复勘察场站判断 {} param = {}", LoggerHelper2.formatEnterLog(request, false),
            param);
        return tjSurveyService.repeatSurvey(param);
    }

    @Operation(summary = "")
    @PostMapping(value = "/addTjSurvey")
    public Mono<ObjectResponse<TjSurveyVo>> addTjSurvey(ServerHttpRequest request,
        @RequestBody TjSurveyVo survey) {
        log.info("新增场站勘察记录 {} param = {}", LoggerHelper2.formatEnterLog(request, false),
            survey);
        survey.setUid(AntRestUtils.getSysUid(request));
        return tjSurveyService.addTjSurvey(survey);
    }

    @Operation(summary = "")
    @PostMapping(value = "/editTjSurvey")
    public Mono<ObjectResponse<TjSurveyVo>> editTjSurvey(ServerHttpRequest request,
        @RequestBody TjSurveyVo survey) {
        log.info("新增或编辑场站勘察记录 {} param = {}",
            LoggerHelper2.formatEnterLog(request, false),
            survey);
        survey.setUid(AntRestUtils.getSysUid(request));
        return tjSurveyService.editTjSurvey(survey);
    }
}
