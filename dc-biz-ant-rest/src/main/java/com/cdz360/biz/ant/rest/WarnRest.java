package com.cdz360.biz.ant.rest;


import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import com.cdz360.biz.model.trading.warn.param.AddUserWarnParam;
import com.cdz360.biz.model.trading.warn.param.UserWarnListParam;
import com.cdz360.biz.model.trading.warn.param.WarnListParam;
import com.cdz360.biz.model.trading.warn.param.WarnSubParam;
import com.cdz360.biz.model.trading.warn.po.WarningPo;
import com.cdz360.biz.model.trading.warn.vo.UserWarningVo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;


/**
 * 告警订阅
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/warn")
@Tag(name = "告警订阅相关接口", description = "告警统计")
public class WarnRest extends BaseController {
    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Operation(summary = "根据条件获取告警列表")
    @PostMapping("/getWarnList")
    public ListResponse<WarningPo> getWarnList(@RequestBody WarnListParam param) {
        log.info("param = {}", param);
        return dataCoreFeignClient.getWarnList(param);
    }

    @Operation(summary = "支撑平台消息订阅")
    @PostMapping("/queryWarningSub")
    public BaseResponse queryWarningSub(@RequestBody WarnSubParam param) {
        log.info("param = {}", param);
        return dataCoreFeignClient.queryWarningSub(param);
    }

    @Operation(summary = "充电管理平台，订阅列表")
    @PostMapping("/getUserWarnList")
    public ListResponse<UserWarningVo> getUserWarnList(ServerHttpRequest request,
                                                       @RequestBody UserWarnListParam param) {
        log.info("param = {}", param);
        Long sysUid = AntRestUtils.getSysUid(request);
        if (sysUid == null) {
            throw new DcServiceException("管理员不存在");
        }
        param.setSysUid(sysUid);
        return dataCoreFeignClient.getUserWarnList(param);
    }

    @Operation(summary = "充电管理平台，用户订阅")
    @PostMapping("/addOrUpdateUserWarn")
    public BaseResponse addOrUpdateUserWarn(ServerHttpRequest request,
                                            @RequestBody AddUserWarnParam param) {
        log.info("param = {}", param);
        Long sysUid = AntRestUtils.getSysUid(request);
        if (sysUid == null) {
            throw new DcServiceException("管理员不存在");
        }
        param.setSysUid(sysUid);
        return dataCoreFeignClient.addOrUpdateUserWarn(param);
    }

    @Operation(summary = "获取管理员订阅的产站ID")
    @GetMapping("/getUserSubSiteList")
    public ListResponse<String> getUserSubSiteList(ServerHttpRequest request) {
        Long sysUid = AntRestUtils.getSysUid(request);
        if (sysUid == null) {
            throw new DcServiceException("管理员不存在");
        }
        return dataCoreFeignClient.getUserSubSiteList(sysUid);
    }

    @Operation(summary = "获取管理员订阅的产站列表", description = "桩管家")
    @GetMapping("/getUserSubSiteInfoList")
    public ListResponse<SiteVo> getUserSubSiteInfoList(ServerHttpRequest request,
                                                       @RequestParam(value = "start", required = true) Long start,
                                                       @RequestParam(value = "size", required = true) Long size) {
        Long sysUid = AntRestUtils.getSysUid(request);
        if (sysUid == null) {
            throw new DcServiceException("管理员不存在");
        }
        return dataCoreFeignClient.getUserSubSiteInfoList(sysUid, start, size);
    }

    @Operation(summary = "获取管理员订阅的告警码")
    @GetMapping("/getUserSubCodeList")
    public ListResponse<String> getUserSubCodeList(ServerHttpRequest request) {
        Long sysUid = AntRestUtils.getSysUid(request);
        if (sysUid == null) {
            throw new DcServiceException("管理员不存在");
        }
        return dataCoreFeignClient.getUserSubCodeList(sysUid);
    }
}
