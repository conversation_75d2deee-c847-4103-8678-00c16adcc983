package com.cdz360.biz.ant.feign;


import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.model.cus.invoice.DcInvoice;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_USER,
        fallbackFactory = InvoiceUserFeignClientHystrixFactory.class)
public interface InvoiceUserFeignClient {

    @PostMapping(value = "/api/invoiced/user")
    ListResponse<DcInvoice> queryUserList(
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "status", required = false) Integer status,
            @RequestParam(value = "index", required = false) Integer index,
            @RequestParam(value = "size", required = false) Integer size,
            @RequestParam(value = "topCommId", required = false) Long topCommId,
            @RequestParam(value = "commIdChain", required = false) String commIdChain);
}
