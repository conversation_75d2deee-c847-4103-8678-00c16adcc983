package com.cdz360.biz.ant.rest.invoice;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.ant.feign.InvoiceFeignClient;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoicedTemplateSalDetailDTO;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@Tag(name = "商品行相关接口", description = "商品行相关接口")
@RestController
@RequestMapping("/api/invoice/sph/detail")
public class InvoiceSphDetailRest {

    @Autowired
    private InvoiceFeignClient invoiceFeignClient;

    @Operation(summary = "通过商品行模板ID获取商品行配置信息")
    @GetMapping("/getByTempRefId")
    public Mono<ListResponse<InvoicedTemplateSalDetailDTO>> getInvoiceSphDetail(
        ServerHttpRequest request,
        @ApiParam("商品行模板ID") @RequestParam(value = "tempRefId") Long tempRefId) {
        log.info("通过商品行模板ID获取商品行配置信息: {}", LoggerHelper2.formatEnterLog(request));
        return Mono.just(invoiceFeignClient.getInvoiceSphDetail(tempRefId));
    }

}
