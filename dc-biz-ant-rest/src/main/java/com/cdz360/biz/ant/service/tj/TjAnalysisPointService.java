package com.cdz360.biz.ant.service.tj;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.ant.feign.reactor.BizTjFeignClient;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaAnalysisPointParam;
import com.cdz360.biz.model.tj.kc.param.UpdateAnalysisPointStatusParam;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisPointVo;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TjAnalysisPointService {

    @Autowired
    private BizTjFeignClient bizTjFeignClient;

    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;

    public Mono<ListResponse<TjAreaAnalysisPointVo>> findTjAnalysisPoint(
        ListTjAreaAnalysisPointParam param) {
        return bizTjFeignClient.findTjAnalysisPoint(param);
    }

    public Mono<ObjectResponse<Long>> updateTjAnalysisPointStatus(
        UpdateAnalysisPointStatusParam param) {
        return bizTjFeignClient.updateTjAnalysisPointStatus(param);
    }

    public Mono<ObjectResponse<ExcelPosition>> exportAnalysisPoint(ServerHttpRequest request,
        ListTjAreaAnalysisPointParam param) {

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("投建分析点位辐射区")
            .setFunctionMap(DownloadFunctionType.TJ_ANALYSIS_POINT)
            .setReqParam(JsonUtils.toJsonString(param));

        return Mono.justOrEmpty(param.getAnalysisId())
            .flatMap(bizTjFeignClient::getTjAnalysisById)
            .map(FeignResponseValidate::checkReturn)
            .map(analysis -> {
                IotAssert.isNotNull(analysis.getTjArea(), "投建分析ID对应投建区域不存在");
                return applyParam.setExFileName(
                    analysis.getTjArea().getName() + "半径" + analysis.getRadius()
                        + "km投建分析点位辐射区");
            })
            .switchIfEmpty(Mono.just(applyParam))
            .flatMap(dataCoreDownloadFileFeignClient::downloadFileApply);
    }
}
