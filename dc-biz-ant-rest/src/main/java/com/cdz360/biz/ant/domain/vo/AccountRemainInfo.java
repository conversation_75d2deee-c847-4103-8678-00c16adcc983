package com.cdz360.biz.ant.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "账户金额信息")
@Data
@Accessors(chain = true)
public class AccountRemainInfo {

    @Schema(description = "商户会员所属商户ID 退款接口请求时使用")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long commId;

    @Schema(description = "用户ID 退款接口请求时使用")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long uid;

    @Schema(description = "账户余额总额(单位: 元)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal remainFee; // point

    @Schema(description = "账户可用金额(单位: 元)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal availableFee; // available

    @Schema(description = "实际金额(单位: 元)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal costFee;

    @Schema(description = "赠送金额(单位: 元)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal freeFee;

    @Schema(description = "可退款金额(单位: 元)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal canRefundFee;

    @Schema(description = "不可退款金额(单位: 元)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal cannotRefundFee;

    @Schema(description = "实际_可退款金额(单位: 元)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal canRefundCostFee;

    @Schema(description = "赠送_可退款金额(单位: 元)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal canRefundFreeFee;
}
