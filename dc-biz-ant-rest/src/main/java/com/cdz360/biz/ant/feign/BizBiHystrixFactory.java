package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.type.OrderType;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ds.cus.ro.balance.param.BalanceApplicationParam;
import com.cdz360.biz.model.bi.dashboard.ChargeOrderCommBiVo;
import com.cdz360.biz.model.bi.dashboard.CommStatisticBiVo;
import com.cdz360.biz.model.bi.dashboard.SiteUtilizationTopVo;
import com.cdz360.biz.model.bi.dashboard.SubCommStatisticBiVo;
import com.cdz360.biz.model.bi.site.*;
import com.cdz360.biz.model.bi.type.OrderByType;
import com.cdz360.biz.model.cus.vin.param.VinSearchParam;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.iot.param.MeterRecordBiParam;
import com.cdz360.biz.model.iot.param.SiteBiParam;
import com.cdz360.biz.model.iot.param.SiteBiTopParam;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.bill.param.ListZftThirdOrderParam;
import com.cdz360.biz.model.trading.comm.dto.CommSiteEvseCount;
import com.cdz360.biz.model.trading.cus.vo.CusOrderBiVo;
import com.cdz360.biz.model.download.param.DownloadFileParam;
import com.cdz360.biz.model.trading.meter.vo.MeterReadingTopVo;
import com.cdz360.biz.model.trading.order.dto.GeoOrderBiDto;
import com.cdz360.biz.model.trading.order.dto.LowKwOrderDto;
import com.cdz360.biz.model.trading.order.dto.OrderStartTypeBiDto;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.model.trading.order.param.ZftBillParam;
import com.cdz360.biz.model.trading.order.vo.ChargeOrderBiVo;
import com.cdz360.biz.model.trading.site.dto.CitySiteNumDto;
import com.cdz360.biz.model.trading.site.dto.ProvinceSiteNumDto;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.po.BiSiteSummaryPo;
import com.cdz360.biz.model.trading.site.po.SiteDailyBiPo;
import com.cdz360.biz.model.trading.site.vo.CorpOrderCountVo;
import com.cdz360.biz.model.trading.site.vo.SiteOrderAccountData;
import com.cdz360.biz.model.trading.site.vo.SiteOrderBiVo;
import com.cdz360.biz.model.trading.site.vo.TimePowerBiVo;
import com.chargerlinkcar.framework.common.domain.BillExportParam;
import com.chargerlinkcar.framework.common.domain.CorpListPointLogParam;
import com.chargerlinkcar.framework.common.domain.OrderCountParam;
import com.chargerlinkcar.framework.common.domain.param.CardSearchParam;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.domain.param.CorpCardRequest;
import com.chargerlinkcar.framework.common.domain.type.DashboardOrderType;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDataVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDetailVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.VinParam;
import feign.Response;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * <AUTHOR>
 * @since 2018/11/19
 */
@Slf4j
@Component
public class BizBiHystrixFactory implements FallbackFactory<BizBiFeignClient> {

    @Override
    public BizBiFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_BI,
            throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_BI,
            throwable.getStackTrace());

        return new BizBiFeignClient() {
//            @Override
//            public ObjectResponse<Boolean> checkExportCountOver(ChargerOrderParam searchParam) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

            @Override
            public ObjectResponse writeTempExcelByChargeOrderList(ChargerOrderParam searchParam) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ExcelPosition> exportCommUserOrderList(
                ChargerOrderParam searchParam) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ExcelPosition> exportVinOrderList(ChargerOrderParam searchParam) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ExcelPosition> exportOnlineCardList(String token,
                CardSearchParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ExcelPosition> exportCardForCorp(CorpCardRequest param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ExcelPosition> exportVinForManage(VinSearchParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ExcelPosition> exportVinForCorp(VinParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ExcelBiPosition> exportBiCorpList(SiteBiParam searchParam) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ExcelBiPosition> exportBiSiteList(SiteBiParam searchParam) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ExcelBiPosition> exportBiFeeBySiteList(SiteBiParam searchParam) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ExcelBiPosition> exportBiFeeByCorpList(SiteBiParam searchParam) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ExcelBiPosition> exportBiElectBySiteList(
                SiteBiParam searchParam) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ExcelBiPosition> exportBiElectByCorpList(
                SiteBiParam searchParam) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ExcelPosition> exportExcelTrade(
                CorpListPointLogParam searchParam) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Boolean> checkExcelFileCompeleted(String type, String token,
                String subFileName) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public Response download(String type, String token, String subFileName) {
                return null;
            }

            @Override
            public ObjectResponse<ExcelPosition> payBillExportExcel(PayBillParam payBillParam) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ExcelPosition> payBillExportExcelManager(
                PayBillParam payBillParam) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ExcelPosition> zftBillExportExcelManager(
                ZftBillParam payBillParam) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ExcelPosition> exportZftThirdOrderList(
                ListZftThirdOrderParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<OrderCount> orderCountBi(SiteBiParam siteBiParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<BiSiteSummaryPo> getSummaryListBySite(SiteBiParam siteBiParam) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<BiSiteSummaryPo> getBiSiteList(SiteBiParam siteBiParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<BiSiteSummaryPo> getBiCorpList(SiteBiParam siteBiParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<UserOrderCount> userOrderCountBi(SiteBiTopParam siteBiTopParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<ChargeFee> chargeFeeBi(SiteBiParam siteBiParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<UserOrderFee> userChargeFeeBi(SiteBiTopParam siteBiTopParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<OrderElecDivision> chargeDivisionBi(SiteBiParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<UserOrderElec> userChargeDivisionBi(SiteBiTopParam siteBiTopParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SiteUtilization> utilizationBi(SiteBiParam siteBiParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<PlugUtilization> plugUtilizationBi(SiteBiTopParam siteBiTopParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SiteErrorCount> breakdownBi(SiteBiParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<PlugErrorCount> plugBreakdownBi(SiteBiTopParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<ChargeOrderBiVo> getOrderBi(String commIdChain, String siteId,
                List<Integer> bizTypeList) {
                log.warn("err={}", throwable.getMessage(), throwable);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SiteOrderBiVo> getSiteBiList(SiteBiParam param) {
                return RestUtils.serverBusy4ListResponse();

            }

            @Override
            public ListResponse<ProvinceSiteNumDto> getProvinceSiteNumList(ListSiteParam param) {

                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<CitySiteNumDto> getCitySiteNumList(ListSiteParam param) {

                return RestUtils.serverBusy4ListResponse();
            }


            @Override
            public ObjectResponse<CommSiteEvseCount> getCommSiteEvseCount(ListSiteParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ExcelPosition> exportOfflineEvse(ListEvseParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ChargeOrderCommBiVo> getLastBi(Integer days, Long commId,
                String idChain) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<CommStatisticBiVo> getLastCommBi(Integer days, Long commId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SubCommStatisticBiVo> getLastCommTopBi(Integer days,
                Long commId, /*String idChain, */DashboardOrderType orderBy) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SiteUtilizationTopVo> getUsageRateBoard(Integer size, Long commId,
                OrderByType orderByType, OrderType orderBy) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<ExcelPosition> exportSettlementByBillNo(String billNo) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<MeterReadingBi> meterReadingBi(SiteBiParam siteBiTopParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<ExcelBiPosition> exportBiSiteMeterRecord(
                MeterRecordBiParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<MeterReadingTopVo> meterReadingTopBi(SiteBiParam siteBiTopParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<TimePowerBiVo> getTimePowerBiList(String commIdChain, String siteId,
                int lastDays) {

                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<OrderStartTypeBiDto> getOrderStartTypeBiList7(Long topCommId,
                String commIdChain) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<Long> getActiveCusCount7(Long topCommId,
                String commIdChain) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SiteOrderBiVo> getTimeGroupingOrderBiList(SiteBiSampleType timeType,
                String siteId,
                String commIdChain) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<CusOrderBiVo> getAccTypeGroupingFeeBiList(String commIdChain) {
                return RestUtils.serverBusy4ObjectResponse();
            }


            @Override
            public ObjectResponse<GeoOrderBiDto> getGeoOrderBi(String provinceCode, String cityCode,
                String siteId, String commIdChain) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SiteOrderAccountData> getOrderAccountBi(Integer days,
                String siteId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SiteUtilization> getDateGroupingPlugUsageBiList(int days,
                String commIdChain) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<SiteDailyBiPo> getSiteDailyPowerLine(String siteId, Date date) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SiteDailyBiPo> getSiteDailyPowerRangeDate(String siteId,
                Date fromDate, Date toDate) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<Boolean> checkFileCompeleted(String subDir, String subFileName) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public Response download(String subDir, String subFileName) {
                return null;
            }

            @Override
            public Response downloadFile(DownloadFileParam param) {
                return null;
            }

            @Override
            public ObjectResponse<ExcelPosition> exportSiteInspection(Long id) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<ChargerOrderVo> queryChargeOrderList(
                ChargerOrderParam searchParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<ChargerOrderDataVo> getChargerOrderData(
                ChargerOrderParam searchParam) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ChargerOrderDetailVo> getChargerOrderDetail(
                ChargerOrderParam searchParam) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ExcelPosition> exportBalanceApplicationExcel(
                BalanceApplicationParam balanceApplicationParam) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<CorpOrderCountVo> corpOrderCount(OrderCountParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<BillExportParam> exportBillExcel(BillExportParam params) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<LowKwOrderDto> getLowKwOrderList(ListChargeOrderParam param) {
                return RestUtils.serverBusy4ListResponse();
            }
        };
    }


}
