package com.cdz360.biz.ant.rest.sim;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorDeviceMgmFeignClient;
import com.cdz360.biz.ant.service.sim.SimService;
import com.cdz360.biz.ant.service.sysLog.SiteSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.ant.utils.FileUtil;
import com.cdz360.biz.model.common.constant.DcBizConstants;
import com.cdz360.biz.model.sim.param.ListSimParam;
import com.cdz360.biz.model.sim.param.ModifyRelationParam;
import com.cdz360.biz.model.sim.po.SimPo;
import com.cdz360.biz.model.sim.vo.SimImportItem;
import com.cdz360.biz.model.sim.vo.SimImportVo;
import com.cdz360.biz.model.sim.vo.SimTinyVo;
import com.cdz360.biz.model.sim.vo.SimVo;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "SIM卡管理相关接口", description = "SIM卡管理")
public class SimRest {

    @Autowired
    private ReactorDeviceMgmFeignClient deviceMgmFeignClient;
    @Autowired
    private SiteSysLogService siteSysLogService;
    @Autowired
    private SimService simService;
    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;

    @Operation(summary = "SIM卡管理列表")
    @PostMapping(value = "/api/sim/getList")
    public Mono<ListResponse<SimVo>> getSimList(ServerHttpRequest request,
        @RequestBody ListSimParam param) {
        log.info("getSimList param = {}", param);
        Long commId = AntRestUtils.getCommId(request);
        AppClientType appClientType = AntRestUtils.getAppClientType(request);
        if (AppClientType.MGM_WEB.equals(appClientType)
            && !NumberUtils.equals(DcBizConstants.superTopCommId, commId)) {
            // 充电管理平台登录帐号所属商户非34474时，接口返回数据为空（此功能不会开放给下级商户）
            return Mono.just(RestUtils.buildListResponse(List.of(), 0L));
        }
        return deviceMgmFeignClient.getSimList(param);
    }

    @Operation(summary = "SIM卡下拉列表（极简）")
    @PostMapping(value = "/api/sim/getSimTinyList")
    public Mono<ListResponse<SimTinyVo>> getSimTinyList(@RequestBody ListSimParam param) {
        log.info("getSimTinyList param = {}", param);
        return deviceMgmFeignClient.getSimTinyList(param);
    }

    @Operation(summary = "SIM卡导出EXCEL")
    @PostMapping(value = "/api/sim/exportSimExcel")
    public Mono<ObjectResponse<ExcelPosition>> exportSimExcel(
        ServerHttpRequest request, @RequestBody ListSimParam param) {
        log.info("SIM卡列表导出(EXCEL): {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        String commIdChain = AntRestUtils.getCommIdChain(request);
        param.setCommIdChain(commIdChain);
        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("SIM卡列表")
            .setFunctionMap(DownloadFunctionType.SIM_LIST)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
    }

    @Operation(summary = "同步增量卡")
    @GetMapping(value = "/api/sim/syncAll")
    public Mono<BaseResponse> syncAll(ServerHttpRequest request) {
        log.info("syncAll start");
        return deviceMgmFeignClient.syncAll()
            .doOnNext(e -> siteSysLogService.simSyncAllLog(request));
    }

    @Operation(summary = "同步单个SIM信息")
    @GetMapping(value = "/api/sim/sync")
    public Mono<ObjectResponse<SimPo>> syncSim(@RequestParam(value = "simId") Long simId) {
        log.info("syncSim simId = {}", simId);
        return deviceMgmFeignClient.syncSim(simId);
    }

    @Operation(summary = "修改SIM和桩的绑定关系")
    @PostMapping(value = "/api/sim/modifyRelation")
    public Mono<BaseResponse> modifyRelation(@RequestBody ModifyRelationParam param) {
        log.info("modifyRelation param = {}", param);
        return deviceMgmFeignClient.modifyRelation(param);
    }

    @Operation(summary = "批量修改SIM和桩的绑定关系")
    @PostMapping(value = "/api/sim/batchModifyRelation")
    public Mono<BaseResponse> batchModifyRelation(@RequestBody List<SimImportItem> list) {
        log.info("modifyRelation list.size = {}", list.size());
        return deviceMgmFeignClient.batchModifyRelation(list);
    }

    @Operation(summary = "解析导入的excel文件")
    @PostMapping("/api/sim/parseExcel")
    public Mono<ObjectResponse<SimImportVo>> parseExcel(ServerHttpRequest request,
        @RequestPart FilePart file) {
        FileUtil.checkExcelFile(file);
        return simService.parseSimExcel(AntRestUtils.getCommIdChain(request), file);
    }

}
