package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.biz.model.trading.site.dto.InspectionRecordDto;
import com.cdz360.biz.model.trading.site.dto.RecentInspectionRecordDto;
import com.cdz360.biz.model.trading.site.param.ChangeRecordParam;
import com.cdz360.biz.model.trading.site.param.InspectionParam;
import com.cdz360.biz.model.trading.site.param.RecordParam;
import com.cdz360.biz.model.trading.site.param.SiteInspectionRecordParam;
import com.cdz360.biz.model.trading.site.po.SiteInspectionCfgPo;
import com.cdz360.biz.model.trading.site.po.SiteInspectionRecordPo;
import com.cdz360.biz.model.trading.site.type.SiteInspectionStatus;
import com.cdz360.biz.model.trading.site.vo.InspectionRecordBi;
import com.cdz360.biz.model.trading.site.vo.InspectionRecordVo;
import com.cdz360.biz.model.trading.site.vo.SiteInspectionRecordVo;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 *
 * @since 2019.3.21
 */
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, fallbackFactory = InspectionFeignClientHystrixFactory.class)
public interface InspectionFeignClient {

    /**
     * 获取最近一次巡检信息
     */
    @GetMapping(value = "/dataCore/inspection/getRecentRecord")
    ObjectResponse<RecentInspectionRecordDto> getRecentRecord(@RequestParam("siteId") String siteId);


    /**
     * 获取场站巡检的配置
     *
     * @param siteId
     * @return
     */
    @GetMapping(value = "/dataCore/inspection/getConfig")
    ObjectResponse<SiteInspectionCfgPo> getConfig(@RequestParam("siteId") String siteId);

    /**
     * 修改巡检配置
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/dataCore/inspection/editConfig")
    BaseResponse editConfig(@RequestBody SiteInspectionCfgPo req);

    /**
     * 巡检记录-饼图数据
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/inspection/getRecordBi")
    ListResponse<InspectionRecordBi> getRecordBi(@RequestBody SiteInspectionRecordParam param);

    /**
     * 巡检记录-表格数据
     *
     */
    @PostMapping(value = "/dataCore/inspection/getRecordVoList")
    ListResponse<InspectionRecordVo> getRecordVoList(@RequestBody SiteInspectionRecordParam param);

    /**
     * 巡检工单
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/inspection/getRecords")
    ListResponse<InspectionRecordDto> getRecords(@RequestBody RecordParam param);


    /**
     * 修改工单状态
     *
     * @param recordId
     * @param status
     * @return
     */
    @GetMapping(value = "/dataCore/inspection/changeStatus")
    BaseResponse changeStatus(@RequestParam("sysUserId") Long sysUserId,
                              @RequestParam("recordId") Long recordId,
                              @RequestParam("status") SiteInspectionStatus status);

    /**
     * 批量质检
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/inspection/changeStatusBatch")
    BaseResponse changeStatusBatch(@RequestBody ChangeRecordParam param);

    // 巡检工单删除
    @GetMapping(value = "/dataCore/inspection/del")
    BaseResponse recordDel(@RequestParam("sysUserId") Long sysUserId,
        @RequestParam("recordId") Long recordId);


    /**
     * 巡检详情
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/dataCore/inspection/getDetail")
    ObjectResponse<SiteInspectionRecordVo> getDetail(@RequestParam("id") Long id);
//
//    /**
//     * 导出巡检详情
//     *
//     * @param id
//     * @return
//     */
//    @GetMapping(value = "/dataCore/inspection/recordExport")
//    ObjectResponse<ExcelPosition> recordExport(@RequestParam("id") Long id);

    /**
     * 获取待巡检场站
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/inspection/getNeedInspectionSite")
    ListResponse<RecentInspectionRecordDto> getNeedInspectionSite(@RequestBody InspectionParam param);

    /**
     * 获取待处理巡检工单
     *
     * @param sysUserId
     * @return
     */
    @PostMapping(value = "/dataCore/inspection/getToBeInspectRecord")
    ListResponse<SiteInspectionRecordVo> getToBeInspectRecord(@RequestParam("sysUserId") Long sysUserId,
                                                              @RequestBody BaseListParam param);

    /**
     * 获取历史巡检工单
     *
     * @param sysUserId
     * @return
     */
    @PostMapping(value = "/dataCore/inspection/getHistoryRecord")
    ListResponse<SiteInspectionRecordVo> getHistoryRecord(@RequestParam("sysUserId") Long sysUserId,
                                                          @RequestBody BaseListParam param);


    /**
     * 通过枪号获取场站巡检信息
     * @param qrCode
     * @param siteId
     * @return
     */
    @GetMapping(value = "/dataCore/inspection/getSiteByPlugNo")
    ObjectResponse<SiteVo> getSiteByPlugNo(@RequestParam(value = "topCommId") Long topCommId,
                                           @RequestParam(value = "plugNo", required = false) String plugNo,
                                           @RequestParam(value = "qrCode", required = false) String qrCode,
                                           @RequestParam(value = "siteId", required = false) String siteId);

    /**
     * 创建巡检单
     * @param siteId
     * @return
     */
    @GetMapping(value = "/dataCore/inspection/create")
    ObjectResponse<SiteInspectionRecordPo> create(@RequestParam(value = "sysUserId") Long sysUserId,
                                                  @RequestParam(value = "siteId") String siteId,
                                                  @RequestParam(value = "inspectionType") Integer inspectionType);

    // 保存巡检单
    @PostMapping(value = "/dataCore/inspection/save")
    BaseResponse save(@RequestBody SiteInspectionRecordPo req);

    /**
     * 提交巡检单
     * @param req
     * @return
     */
    @PostMapping(value = "/dataCore/inspection/report")
    BaseResponse report(@RequestBody SiteInspectionRecordPo req);

    @PostMapping(value = "/dataCore/inspection/trans")
    ObjectResponse<SiteInspectionRecordPo> transInspection(
            @RequestParam("recId") Long recId, @RequestParam("opUid") Long opUid);
}
