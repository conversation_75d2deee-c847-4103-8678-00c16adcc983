package com.cdz360.biz.ant.service.pv;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.iot.dto.UpdateCtrlDto;
import com.cdz360.biz.model.iot.param.ListCtrlParam;
import com.cdz360.biz.model.iot.param.ListGtiParam;
import com.cdz360.biz.model.iot.vo.GtiVo;
import com.cdz360.biz.model.iot.vo.GwInfoVo;
import com.cdz360.biz.model.trading.iot.dto.CntCtrlGtiDto;
import com.cdz360.biz.model.trading.iot.vo.GtiDataInTimeVo;
import com.cdz360.biz.utils.feign.iot.DeviceFeignClient;
import com.cdz360.biz.utils.feign.iot.GtiFeignClient;
import com.cdz360.biz.utils.feign.iot.IotPvFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * PvDeviceService
 *  光伏设备相关
 * @since 8/30/2021 4:30 PM
 * <AUTHOR>
 */
@Slf4j
@Service
public class PvDeviceService {

    @Autowired
    private DeviceFeignClient deviceFeignClient;

    @Autowired
    private GtiFeignClient gtiFeignClient;

    @Autowired
    private IotPvFeignClient iotPvFeignClient;

    public Mono<ListResponse<GwInfoVo>> findCtrlList(ListCtrlParam param) {
        return deviceFeignClient.findCtrlList(param);
    }

    public Mono<BaseResponse> addCtrl(UpdateCtrlDto param) {
        return deviceFeignClient.addCtrl(param);
    }

    public Mono<BaseResponse> updateCtrl(UpdateCtrlDto param) {
        return deviceFeignClient.updateCtrl(param);
    }

    public Mono<ListResponse<GtiVo>> findGtiList(ListGtiParam param) {
        return gtiFeignClient.findGtiList(param);
    }

    public Mono<ObjectResponse<GwInfoVo>> getCtrl(String gwno) {
        return deviceFeignClient.getCtrl(gwno);
    }

    public Mono<BaseResponse> removeCtrl(String siteId, String gwno) {
        return deviceFeignClient.removeCtrl(siteId, gwno);
    }

    public Mono<BaseResponse> sendModifyGtiCfgCmd(String gwno, String dno, Long cfgId) {
        return iotPvFeignClient.sendModifyGtiCfgCmd(gwno, dno, cfgId);
    }

    public Mono<ObjectResponse<GtiDataInTimeVo>> gtiInfoInTime(String dno) {
        return gtiFeignClient.gtiInfoInTime(dno);
    }

    public Mono<ObjectResponse<CntCtrlGtiDto>> countCtrlGti(String siteId) {
        return gtiFeignClient.countCtrlGti(siteId);
    }
}