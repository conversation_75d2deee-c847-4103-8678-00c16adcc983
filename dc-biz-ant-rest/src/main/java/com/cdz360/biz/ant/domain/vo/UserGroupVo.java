/*
*
* UserGroup.java
* Copyright(C) 2017-2020 fendo公司
 * @since 2018-11-26
*/
package com.cdz360.biz.ant.domain.vo;

import com.cdz360.biz.ant.domain.UserGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class UserGroupVo extends UserGroup implements Serializable {
    /**
     * 主键
     */
    private Long userCount;

    /**
     * t_user_group
     */
    private static final long serialVersionUID = 1L;


}