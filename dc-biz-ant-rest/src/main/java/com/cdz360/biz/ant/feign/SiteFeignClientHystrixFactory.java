package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.domain.request.UpgradeTaskRequest;
import com.chargerlinkcar.framework.common.domain.param.BoxListRequest;
import com.chargerlinkcar.framework.common.domain.vo.BoxInfoVo;
import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SiteFeignClientHystrixFactory implements FallbackFactory<SiteFeignClient> {
    @Override
    public SiteFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_DEVICE_REST, throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_DEVICE_REST, throwable.getStackTrace());

        return new SiteFeignClient() {

//
//            @Override
//            public ListResponse<ChargerInfoVo> getPagedChargerInfoList(JSONObject paramJson) {
//                return RestUtils.serverBusy4ListResponse();
//            }

//            @Override
//            public ListResponse<ChargerInfoVo> getChargersInfo(List<Long> bcIdList) {
//                return RestUtils.serverBusy4ListResponse();
//            }

            @Override
            public ListResponse<BoxInfoVo> getPagedBoxSimpleList(BoxListRequest boxListRequest) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse startTask(String token, UpgradeTaskRequest upgradeTaskRequest) {
                return RestUtils.serverBusy();
            }
        };
    }
}