package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.service.ExpectLimitSocService;
import com.chargerlinkcar.framework.common.domain.request.ExpectLimitSocReq;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * @since 6/1/2021 1:46 PM
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "充电限制soc操作接口", description = "充电限制soc操作接口")
public class LimitSocRest {

    @Autowired
    private ExpectLimitSocService expectLimitSocService;

    @Operation( summary = "修改期望限制的soc值")
    @RequestMapping(value = "/api/limitSoc/changeExpectLimitSoc", method = RequestMethod.POST)
    public BaseResponse changeExpectLimitSoc(ServerHttpRequest request,
                               @RequestBody ExpectLimitSocReq req) {
        log.info("修改期望限制的soc值: {}", JsonUtils.toJsonString(req));
        return expectLimitSocService.changeExpectLimitSoc(req);
    }


}