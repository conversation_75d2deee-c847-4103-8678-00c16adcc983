package com.cdz360.biz.ant.service.corp;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.feign.BizBiFeignClient;
import com.cdz360.biz.ant.feign.CorpSettlementFeignClient;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.discount.param.UpdateCorpDiscountParam;
import com.cdz360.biz.model.cus.settlement.dto.SettlementDto;
import com.cdz360.biz.model.cus.settlement.param.ListSettlementParam;
import com.cdz360.biz.model.cus.settlement.param.UpdateCorpSettlementParam;
import com.cdz360.biz.model.cus.settlement.vo.CorpSettlementCfgVo;
import com.cdz360.biz.model.cus.settlement.vo.SettlementVo;
import com.cdz360.biz.model.finance.type.TaxStatus;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CorpSettlementService {

    @Autowired
    private CorpSettlementFeignClient corpSettlementFeignClient;

    @Autowired
    private BizBiFeignClient bizBiFeignClient;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    public ObjectResponse<CorpSettlementCfgVo> getSettlementCfgById(Long cfgId) {
        if (null == cfgId) {
            throw new DcArgumentException("结算配置Id不能为空");
        }

        return corpSettlementFeignClient.getSettlementCfgById(cfgId);
    }

    public ObjectResponse<CorpSettlementCfgVo> getSettlementCfg(Long corpId) {
        if (null == corpId) {
            throw new DcArgumentException("企业Id不能为空");
        }

        // blocUserId === corp.getId()
        return corpSettlementFeignClient.getSettlementCfg(corpId);
    }

    public CorpPo updateSettlementCfg(UpdateCorpSettlementParam param) {
        // 参数校验
        UpdateCorpSettlementParam.checkValue(param);

        ObjectResponse<CorpPo> res = corpSettlementFeignClient.updateSettlementCfg(param);
        FeignResponseValidate.check(res);
        return res.getData();
    }

    public ListResponse<SettlementVo> findSettlementList(ListSettlementParam param) {
        ListResponse<SettlementVo> result = corpSettlementFeignClient.findSettlementList(param);
        result.getData().forEach(vo -> {
            if (DecimalUtils.isZero(vo.getInvoicedAmount())) {
                vo.setTaxStatus(TaxStatus.NO);
            } else if (DecimalUtils.eq(vo.getSettlementTotalFee(), vo.getInvoicedAmount())) {
                vo.setTaxStatus(TaxStatus.YES);
            } else {
                vo.setTaxStatus(TaxStatus.PART);
            }
        });
        return result;
    }

    public ObjectResponse<SettlementVo> appendOrder2Settlement(SettlementDto dto) {
        SettlementDto.appendCheckValue(dto);
        return corpSettlementFeignClient.appendOrder2Settlement(dto);
    }

    public ObjectResponse<SettlementVo> removeOrder4Settlement(SettlementDto dto) {
        SettlementDto.removeCheckValue(dto);
        return corpSettlementFeignClient.removeOrder4Settlement(dto);
    }

//    public ObjectResponse<Integer> addSettlement(SettlementDto dto) {
//        // 校验参数
//        SettlementDto.checkValue(dto);
//
//        return corpSettlementFeignClient.addSettlement(dto);
//    }

    public ObjectResponse<Integer> updateSettlement(SettlementDto dto) {
        // 校验参数
//        SettlementDto.checkValue(dto);

        return corpSettlementFeignClient.updateSettlement(dto);
    }

    public ObjectResponse<SettlementVo> getSettlementByBillNo(String billNo) {
        if (StringUtils.isBlank(billNo)) {
            throw new DcArgumentException("账单编号不能为空");
        }
        return corpSettlementFeignClient.getSettlementByBillNo(billNo);
    }

    public ObjectResponse<Integer> removeSettlementByBillNo(String billNo) {
        if (StringUtils.isBlank(billNo)) {
            throw new DcArgumentException("账单编号不能为空");
        }
        return corpSettlementFeignClient.removeSettlementByBillNo(billNo);
    }

    public ObjectResponse<ExcelPosition> exportSettlementByBillNo(String billNo) {
        return bizBiFeignClient.exportSettlementByBillNo(billNo);
    }

    public ObjectResponse<Integer> settlementByBillNo(String billNo) {
        if (StringUtils.isBlank(billNo)) {
            throw new DcArgumentException("账单编号不能为空");
        }

        return corpSettlementFeignClient.settlementByBillNo(billNo);
    }

    public ObjectResponse<CorpPo> updateDiscount(UpdateCorpDiscountParam param) {
        UpdateCorpDiscountParam.checkValue(param);

        return corpSettlementFeignClient.updateDiscount(param);
    }
}
