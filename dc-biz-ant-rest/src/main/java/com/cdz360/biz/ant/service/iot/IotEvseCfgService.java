package com.cdz360.biz.ant.service.iot;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.ant.domain.CfgEvseAllV2;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.IotWorkerFeignClient;
import com.cdz360.biz.ant.service.site.SiteService;
import com.cdz360.biz.model.iot.param.ModifyEvseCfgExParam;
import com.cdz360.biz.model.iot.param.ModifyEvseCfgParam;
import com.cdz360.biz.model.iot.vo.EvseCfgResultPo;
import com.cdz360.biz.model.iot.vo.LocalCard;
import com.cdz360.biz.model.iot.vo.WhiteVin;
import com.cdz360.biz.model.trading.site.po.EvseCfgSchedulePo;
import com.cdz360.biz.model.site.po.PriceTemplatePo;
import com.cdz360.biz.model.trading.site.vo.EvsePriceSchemeInfoVo;
import com.cdz360.biz.model.trading.site.vo.SiteChargePriceVo;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.framework.common.feign.PriceSchemaFeignClient;
import com.chargerlinkcar.framework.common.feign.SiteDataCoreFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/1/2 15:53
 */
@Slf4j
@Service
public class IotEvseCfgService {

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private RedisIotReadService redisIotReadService;

    @Autowired
    private IotWorkerFeignClient iotWorkerFeignClient;

    @Autowired
    private SiteDataCoreFeignClient siteDataCoreFeignClient;

    @Autowired
    private PriceSchemaFeignClient priceTemplateFeignClient;

    @Autowired
    private SiteService siteService;

    /**
     * 下发计费模板
     *
     * @param param
     * @param opUid
     */
    public void downPriceTemplate(ModifyEvseCfgParam param, Long opUid) {
        log.info(">> 下发计费模板: opUid = {}", opUid);
        Assert.notNull(param.getPriceSchemeId(), "参数错误，计费模板Id不能为空");

        ObjectResponse<PriceTemplatePo> priceRes = priceTemplateFeignClient.getPriceSchema(param.getPriceSchemeId());
        FeignResponseValidate.check(priceRes);

        if (Boolean.FALSE.equals(priceRes.getData().getEnable())) {
            throw new DcArgumentException("该计费模板已被禁用，请选择其他计费模板");
        }

        if (null == param.getSchedule()) {
            // 是否默认场站计费
            // 接口包含逻辑: 判断场站是否第一次下发，如果是第一次下发会默认设置上的
//            siteService.setDefaultPriceScheme(
//                    param.getPriceSchemeId(), param.getSiteId(),
//                    param.getSiteDefault() == null ? false : param.getSiteDefault());

            // 即时下发
            this.dataCoreFeignClient.priceTempDown(param);
            log.info("<< 即时下发完成完成");
        } else {
            //定时下发判断当前时间是否大于下发时间
//            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
//            if (format.format(new Date()).compareTo(format.format(param.getSchedule()))>0) {
//                throw new DcArgumentException("下发时间不能早于当前时间");
//            }
            // 将定时时间的毫秒去掉在比较
            if  (System.currentTimeMillis()> param.getSchedule().getTime() / 1000 * 1000) {
                throw new DcArgumentException("下发时间不能早于当前时间");
            }

            // 定时下发
            List<EvseCfgSchedulePo> poList = param.getEvseNoList().stream().map(evseNo -> {
                EvseCfgSchedulePo po = new EvseCfgSchedulePo();
                po.setEvseNo(evseNo)
                        .setOpUid(opUid)
                        .setPriceSchemeId(param.getPriceSchemeId())
                        .setSiteDefault(param.getSiteDefault() == null ? false : param.getSiteDefault())
                        .setScheduleTime(param.getSchedule());
                return po;
            }).collect(Collectors.toList());

            ObjectResponse<Integer> res = this.dataCoreFeignClient.batchInsert(poList);
            FeignResponseValidate.check(res);
            log.info("<< 定时下发: count = {}", res.getData());
        }
    }

    /**
     * 下发计费模板
     *
     * @param param
     */
    public void modifyEvsePrice(ModifyEvseCfgParam param) {
        Assert.notNull(param.getPriceSchemeId(), "参数错误，计费模板Id不能为空");


        // 是否默认场站计费
        // 接口包含逻辑: 判断场站是否第一次下发，如果是第一次下发会默认设置上的
        siteService.setDefaultPriceScheme(
                param.getPriceSchemeId(), param.getSiteId(),
                param.getSiteDefault() == null ? false : param.getSiteDefault());


        this.dataCoreFeignClient.modifyEvsePrice(param);
        log.info("<< 即时下发完成完成");
    }

    /**
     * 配置下发桩状态校验
     *
     * @param evseNoList
     * @return
     */
    public List<String> downFilter(List<String> evseNoList) {
        // iot 中是否存在下发中的状态

        // dataCore 待下发
        ListResponse<String> res = dataCoreFeignClient.downFilter(evseNoList);
        if (null == res || null == res.getData()) {
            return new ArrayList<>();
        }

        return res.getData();
    }

    public Boolean getEvseVinAuthCfgRealTime(String evseNo) {
        log.info("获取桩当前配置的本地VIN: evseNo = {}", evseNo);
        IotAssert.isNotBlank(evseNo, "桩编号不能为空，请提供桩编号");

        EvseVo cache = redisIotReadService.getEvseRedisCache(evseNo);
        IotAssert.isNotNull(cache, "该桩还未接入平台, 请确认桩编号是否正确");

        IotAssert.isTrue(EvseStatus.OFFLINE != cache.getStatus(), "桩当前离线，无法获取实时本地VIN。");
        iotWorkerFeignClient.cfgEvseGetcfg(Collections.singletonList(evseNo));
        return true;
    }

    public List<WhiteVin> getEvseVinAuthCfg(String evseNo) {
        ObjectResponse<CfgEvseAllV2> ob = iotWorkerFeignClient.cfgEvseGetcfgInfo(evseNo);
        FeignResponseValidate.check(ob);
        log.info("获取的配置信息: ob = {}", ob);

        CfgEvseAllV2 cfgEvseAll = ob.getData();
        if(cfgEvseAll.getWhiteVinList() != null) {
            return cfgEvseAll.getWhiteVinList();
        }
        return List.of();
    }

    public EvsePriceSchemeInfoVo getPriceSchemeInfo(String evseNo) {
        log.info("获取桩的计费详情: evseNo = {}", evseNo);
        if (StringUtils.isEmpty(evseNo)) {
            throw new DcArgumentException("桩编号不能为空，请提供桩编号");
        }

        // 离线返回最近成功的
        EvseVo cache = redisIotReadService.getEvseRedisCache(evseNo);
        if (null == cache) {
            log.info("该桩还未接入平台");
            throw new DcArgumentException("该桩还未接入平台, 请确认桩编号是否正确");
        }

        // 桩状态离线: 获取桩最近一次下发成功的数据
        // t_bs_box_setting
        if (EvseStatus.OFFLINE == cache.getStatus()) {
            ObjectResponse<PriceTemplatePo> priceScheme = this.dataCoreFeignClient.getPriceSchemeByEvesNo(evseNo);
            FeignResponseValidate.check(priceScheme);

            PriceTemplatePo po = priceScheme.getData();
            EvsePriceSchemeInfoVo vo = new EvsePriceSchemeInfoVo()
                    .setEvseNo(evseNo)
                    .setPriceSchemeId(po.getId())
                    .setPriceSchemeName(po.getName())
                    .setPrice(po.getPriceItemList());
            log.info("桩的计费信息: vo = {}", vo);
            return vo;
        }

        // 桩在线: 请求下发获取桩配置
        // 前端通过接口获取桩上报的信息: /cfg/evse/getcfgInfo
        iotWorkerFeignClient.cfgEvseGetcfg(Collections.singletonList(evseNo));
        return null;
    }

    /**
     * 单个桩计费模板下发
     *
     * @param evseNo
     */
    public String sendPriceSchema(String evseNo) {
        if (StringUtils.isEmpty(evseNo)) {
            throw new DcArgumentException("桩编号不能为空，请提供");
        }

        ObjectResponse<String> res = this.dataCoreFeignClient.sendPriceSchema(evseNo);
        FeignResponseValidate.check(res);

        return res.getData(); // 避免编译器优化
    }


    public void sendEvsePrice(ModifyEvseCfgExParam param) {

        IotAssert.isNotBlank(param.getSiteId(), "请传入场站id");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getEvseNoList()) &&
                param.getEvseNoList().size() == 1 &&
                com.cdz360.base.utils.StringUtils.isNotBlank(param.getEvseNoList().get(0)),
                "桩列表参数应仅有一个非空元素");

        if(param.isSiteCommPrice()) {
            log.info("取场站通用配置计费");
            // 下发计费模板  根据场站配置的多计费模板 以及桩类型下发
//            ObjectResponse<PriceTemplatePo> sitePriceScheme =
//                    siteDataCoreFeignClient.getSitePriceScheme(param.getSiteId());

            // 获取场站计费模板
            ListResponse<SiteChargePriceVo> sitePriceResponse = siteDataCoreFeignClient.getSitePriceList(
                param.getSiteId());
            FeignResponseValidate.check(sitePriceResponse);
            List<SiteChargePriceVo> sitePriceList = sitePriceResponse.getData();

            if (CollectionUtils.isEmpty(sitePriceList)) {
                throw new DcServiceException("场站计费模板不存在");
            }
            log.info("场站计费模板,siteId={},sitePriceList={}", param.getSiteId(),
                sitePriceList.size());

            if (sitePriceList.size() == 1) { // 只有一个计费模板
                param.setPriceSchemeId(sitePriceList.get(0).getId());
            } else { // 多个计费模板   根据桩类型下发
                List<EvseVo> evseList = redisIotReadService.getEvseList(param.getEvseNoList());
                if (CollectionUtils.isEmpty(evseList)) {
                    throw new DcServiceException("桩信息不存在");
                }
                SupplyType supplyType = evseList.get(0).getSupplyType();
                Optional<SiteChargePriceVo> priceScheme = sitePriceList.stream()
                    .filter(x -> x.getTemplateType().equals(supplyType)).findFirst();

                param.setPriceSchemeId(priceScheme.map(SiteChargePriceVo::getId)
                    .orElseThrow(() -> new DcServiceException("计费模板不存在")));
            }


            // 根据桩类型
//            FeignResponseValidate.check(sitePriceScheme);
//            param.setPriceSchemeId(sitePriceScheme.getData().getId());
        } else {
            log.info(("取桩上次配置成功计费"));

            ObjectResponse<EvseCfgResultPo> evseCfgResult =
                    iotWorkerFeignClient.getEvseCfgResult(param.getEvseNoList().get(0));

            FeignResponseValidate.check(evseCfgResult);

            IotAssert.isNotNull(evseCfgResult.getData().getActualPriceCode(), "桩当前无配置成功的计费.");
            IotAssert.isTrue(evseCfgResult.getData().getActualPriceCode().intValue() > 0, "桩当前无配置成功的计费");

            param.setPriceSchemeId(evseCfgResult.getData().getActualPriceCode());
        }

        this.dataCoreFeignClient.priceTempDown(param);
    }

    public BaseResponse cfgEvseGetcfg(List<String> evseIds) {
        if (CollectionUtils.isEmpty(evseIds)) {
            throw new DcArgumentException("请指定获取的桩编号");
        }

        evseIds.forEach(evseNo -> {
            EvseVo cache = redisIotReadService.getEvseRedisCache(evseNo);
            if (null == cache) {
                log.info("该桩还未接入平台");
                throw new DcArgumentException("该桩还未接入平台, 请确认桩编号是否正确");
            }

            if (EvseStatus.OFFLINE == cache.getStatus()) {
                log.info("该桩已经离线");
                throw new DcArgumentException("该桩已经离线");
            }
        });

        return iotWorkerFeignClient.cfgEvseGetcfg(evseIds);
    }

    public List<LocalCard> getEvseCardAuthCfg(String evseNo) {
        ObjectResponse<CfgEvseAllV2> ob = iotWorkerFeignClient.cfgEvseGetcfgInfo(evseNo);
        FeignResponseValidate.check(ob);
        log.info("获取的配置信息: ob = {}", ob);

        CfgEvseAllV2 cfgEvseAll = ob.getData();
        if (cfgEvseAll.getLocalCards() != null) {
            return cfgEvseAll.getLocalCards();
        }
        return List.of();

    }

    public Boolean getEvseCardAuthCfgRealTime(String evseNo) {
        log.info("获取桩当前配置的本地Card: evseNo = {}", evseNo);
        IotAssert.isNotBlank(evseNo, "桩编号不能为空，请提供桩编号");

        EvseVo cache = redisIotReadService.getEvseRedisCache(evseNo);
        IotAssert.isNotNull(cache, "该桩还未接入平台, 请确认桩编号是否正确");

        IotAssert.isTrue(EvseStatus.OFFLINE != cache.getStatus(), "桩当前离线，无法获取实时本地CARD。");
        iotWorkerFeignClient.cfgEvseGetcfg(Collections.singletonList(evseNo));
        return true;
    }
}
