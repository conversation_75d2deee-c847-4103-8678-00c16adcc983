package com.cdz360.biz.ant.service.evse;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.ant.feign.DeviceMgmFeignClient;
import com.cdz360.biz.model.evse.param.ListPackageParam;
import com.cdz360.biz.model.evse.param.UpdateEvsePackageParam;
import com.cdz360.biz.model.evse.vo.EvsePackageVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 海外平台升级包管理
 */
@Service
@Slf4j
public class EvsePackageService {

    @Autowired
    private DeviceMgmFeignClient deviceMgmFeignClient;

    public BaseResponse deleteById(UpdateEvsePackageParam param) {
        return deviceMgmFeignClient.deleteById(param);
    }

    public BaseResponse updateStatus(UpdateEvsePackageParam param) {
        return deviceMgmFeignClient.updateStatus(param);
    }

    public BaseResponse editPackage(UpdateEvsePackageParam param) {
        return deviceMgmFeignClient.editPackage(param);
    }

    public ListResponse<EvsePackageVo> getList(ListPackageParam param) {
        return deviceMgmFeignClient.getList(param);
    }

    public BaseResponse createPackage(UpdateEvsePackageParam params) {
        return deviceMgmFeignClient.create(params);
    }

    public ListResponse<String> getBrandList() {
        return deviceMgmFeignClient.getBrandList();
    }
}
