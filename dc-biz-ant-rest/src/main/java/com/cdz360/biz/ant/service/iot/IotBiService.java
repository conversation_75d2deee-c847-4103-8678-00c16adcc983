package com.cdz360.biz.ant.service.iot;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.biz.ant.feign.BizBiFeignClient;
import com.cdz360.biz.ess.model.data.param.DataBiParam;
import com.cdz360.biz.model.trading.iot.vo.EvsePowerBiVo;
import com.cdz360.biz.model.trading.iot.vo.EvseStatusPowerBiVo;
import com.cdz360.biz.model.trading.iot.vo.PlugStatusBiVo;
import com.cdz360.biz.model.trading.iot.vo.PlugSupplyBiVo;
import com.cdz360.biz.model.trading.site.po.SiteDailyBiPo;
import com.cdz360.biz.model.trading.site.vo.TimePowerBiVo;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import java.sql.Date;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class IotBiService {


    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;

    @Autowired
    private BizBiFeignClient bizBiFeignClient;

    /**
     * 根据电流类型统计桩/枪数量
     *
     * @return
     */
    public ListResponse<PlugSupplyBiVo> getPlugSupplyBi(String commIdChain) {
        ListResponse<PlugSupplyBiVo> res = this.iotDeviceMgmFeignClient.getPlugSupplyBi(
            commIdChain);
        FeignResponseValidate.check(res);
        return res;
    }

    /**
     * 根据状态统计桩/枪数量
     *
     * @return
     */
    public ListResponse<PlugStatusBiVo> getPlugStatusBi(String commIdChain) {
        ListResponse<PlugStatusBiVo> res = this.iotDeviceMgmFeignClient.getPlugStatusBi(null, null,
            null, commIdChain);
        FeignResponseValidate.check(res);
        return res;
    }

    /**
     * 充电电力统计
     *
     * @param param
     * @return
     */
    public ListResponse<SiteDailyBiPo> getSiteDailyPower(DataBiParam param) {
        LocalDate now = LocalDate.now();
        LocalDate before2Day = now.minusDays(2);
//        LocalDate yesterday = now.plusDays(-1);
        return bizBiFeignClient.getSiteDailyPowerRangeDate(
            param.getSiteId(),
            Date.from(before2Day.atStartOfDay(ZoneId.systemDefault()).toInstant()),
            Date.from(now.atStartOfDay(ZoneId.systemDefault()).toInstant()));
    }

    /**
     * 充电电力统计
     *
     * @param commIdChain
     * @return
     */
    public EvsePowerBiVo getEvsePowerBi(String commIdChain, String siteId) {

        // 根据状态统计桩功率
        ListResponse<EvseStatusPowerBiVo> res = this.iotDeviceMgmFeignClient.getEvseStatusPowerBi(
            null,
            null, siteId, commIdChain);
        FeignResponseValidate.check(res);
        log.debug("evse status power = {}", res.getData());

        // 获取近3天功率统计
        ListResponse<TimePowerBiVo> res2 = this.bizBiFeignClient.getTimePowerBiList(commIdChain,
            siteId, 3);
        FeignResponseValidate.check(res2);

        Long busyPower = res.getData().stream()
            .filter(o -> o.getEvseStatus() == EvseStatus.BUSY)
            .mapToLong(EvseStatusPowerBiVo::getPower).sum();

        EvsePowerBiVo vo = new EvsePowerBiVo();
        vo.setTotalPower(res.getData().stream().mapToLong(EvseStatusPowerBiVo::getPower).sum())
            .setBusyPower(busyPower);

        LocalDateTime beforeYesterday = LocalDate.now().plusDays(-2).atTime(0, 0, 0);
        LocalDateTime yesterday = LocalDate.now().plusDays(-1).atTime(0, 0, 0);
        LocalDateTime today = LocalDate.now().atTime(0, 0, 0);
        LocalDateTime now = LocalDateTime.now();

        List<TimePowerBiVo> beforeYesterdayList = new ArrayList<>();
        List<TimePowerBiVo> yesterdayList = new ArrayList<>();
        List<TimePowerBiVo> todayList = new ArrayList<>();

        for (TimePowerBiVo tp : res2.getData()) {
            if (beforeYesterday.isAfter(yesterday)) {
                break;
            }
            LocalDateTime tpTime = tp.getTime().toInstant().atZone(ZoneId.systemDefault())
                .toLocalDateTime();
            while (beforeYesterday.isBefore(tpTime)) {
                // 没有数据,自动补全
                TimePowerBiVo tpx = new TimePowerBiVo();
                tpx.setTime(Date.from(beforeYesterday.toInstant(ZoneOffset.of("+8"))))
                    .setPower(0L);
                beforeYesterdayList.add(tpx);
                beforeYesterday = beforeYesterday.plusHours(1);
            }
            if (tpTime.isBefore(yesterday)) {
                beforeYesterdayList.add(tp);
                beforeYesterday = beforeYesterday.plusHours(1);
            } else {
                break;
            }
        }

        for (TimePowerBiVo tp : res2.getData()) {
            if (yesterday.isAfter(today)) {
                break;
            }
            LocalDateTime tpTime = tp.getTime().toInstant().atZone(ZoneId.systemDefault())
                .toLocalDateTime();
            if (tpTime.isBefore(beforeYesterday)) {
                continue;
            }
            while (yesterday.isBefore(tpTime)) {
                // 没有数据,自动补全
                TimePowerBiVo tpx = new TimePowerBiVo();
                tpx.setTime(Date.from(yesterday.toInstant(ZoneOffset.of("+8"))))
                    .setPower(0L);
                yesterdayList.add(tpx);
                yesterday = yesterday.plusHours(1);
            }
            if (tpTime.isBefore(today)) {
                yesterdayList.add(tp);
                yesterday = yesterday.plusHours(1);
            } else {
                break;
            }
        }
        yesterday = yesterday.plusHours(-1);
        //log.debug("yesterday = {}", yesterday);
        for (TimePowerBiVo tp : res2.getData()) {
            if (today.isAfter(now)) {
                break;
            }
            LocalDateTime tpTime = tp.getTime().toInstant().atZone(ZoneId.systemDefault())
                .toLocalDateTime();
            if (tpTime.isBefore(yesterday)) {
                continue;
            }
            while (today.isBefore(tpTime)) {
                // 没有数据,自动补全
                TimePowerBiVo tpx = new TimePowerBiVo();
                tpx.setTime(Date.from(today.toInstant(ZoneOffset.of("+8"))))
                    .setPower(0L);
                todayList.add(tpx);
                today = today.plusHours(1);
            }
            todayList.add(tp);
            today = today.plusHours(1);
        }
        vo.setTodayPowerList(todayList)
            .setYesterdayPowerList(yesterdayList)
            .setBeforeYesterdayPowerList(beforeYesterdayList);
        return vo;
    }

}
