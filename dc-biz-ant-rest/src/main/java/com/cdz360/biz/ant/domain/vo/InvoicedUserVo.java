package com.cdz360.biz.ant.domain.vo;

import com.chargerlinkcar.framework.common.domain.OldPageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.ZonedDateTime;

/**
 * InvoicedUserVo
 *
 * @since 2019/7/4 9:10
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class InvoicedUserVo extends OldPageParam implements Serializable {
    /**
     * t_user
     */
    private static final long serialVersionUID = 1L;
    /**
     * 用户UID
     */
    private long id;
    /**
     * 商户Id
     */
    private Long commId;
    /**
     * t用户名
     */
    private String username;
    /**
     * 姓名
     */
    private String name;
    /**
     * 用户状态（10000-删除，10001-正常,10002-加入黑名单）
     */
    private Boolean status;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 省
     */
    private String province;
    /**
     * 市
     */
    private String city;
    /**
     * 区
     */
    private String district;
    /**
     * 街道
     */
    private String street;
    /**
     * 地址
     */
    private String address;
    /**
     * 开票起始金额（单位分）
     */
    private long invoicedAmount;
    /**
     * 每月几号
     */
    private Integer monthDay;

    private ZonedDateTime createTime;

    private Boolean auto;//是否自动开票

}
