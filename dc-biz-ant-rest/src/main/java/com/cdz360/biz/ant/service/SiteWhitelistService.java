package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.model.cus.site.param.SiteWhitelistParam;
import com.cdz360.biz.model.cus.site.po.SiteWhitelistPo;
import com.cdz360.biz.model.cus.site.vo.SiteWhitelistVo;
import com.cdz360.biz.utils.feign.user.UserFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;

@Slf4j
@Service
public class SiteWhitelistService {

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    public Mono<ListResponse<SiteWhitelistVo>> querySiteWhitelist(SiteWhitelistPo param) {
        return userFeignClient.querySiteWhitelist(param);
    }

    public Mono<ObjectResponse<Boolean>> addWhitelist(SiteWhitelistParam param) {
        return userFeignClient.addWhitelist(param);
    }

    public Mono<ObjectResponse<Integer>> removeWhitelist(List<SiteWhitelistParam> param) {
        return userFeignClient.removeWhitelist(param);
    }


//    public ListResponse<SiteBlacklistVo> findSiteBlacklist(ListSiteBlacklistParam param) {
//        if (StringUtils.isBlank(param.getSiteId())) {
//            throw new DcArgumentException("请指定场站Id值");
//        }
//        param.setTotal(Boolean.TRUE); // 默认统计总数
//
//        ListResponse<SiteBlacklistVo> blacklist = this.userFeignClient.findSiteBlacklist(param);
//        FeignResponseValidate.check(blacklist);
//
//        if (CollectionUtils.isEmpty(blacklist.getData())) {
//            log.info("<< no data");
//            return RestUtils.buildListResponse(new ArrayList<>());
//        }
//
//        // 查询用户在该场站的充电情况
//        List<Long> uidList = blacklist.getData().stream()
//                .map(SiteBlacklistVo::getUid).collect(Collectors.toList());
//        ListCusOrderBiParam orderBiParam = new ListCusOrderBiParam();
//        orderBiParam.setUidList(uidList);
//        orderBiParam.setSiteId(param.getSiteId());
//
//        ListResponse<CusOrderBiDto> orderBiList = this.dataCoreFeignClient.getCusOrderBiList(orderBiParam);
//        FeignResponseValidate.check(orderBiList);
//
//        if (CollectionUtils.isEmpty(orderBiList.getData())) {
//            return blacklist;
//        }
//
//        Map<Long, CusOrderBiDto> biMap = orderBiList.getData()
//                .stream().collect(Collectors.toMap(CusOrderBiDto::getUid, o -> o));
//
//        blacklist.getData().forEach(item -> {
//            // 更新用户的充电信息
//            CusOrderBiDto dto = biMap.get(item.getUid());
//            item.setOrderNum(dto.getOrderNum());
//            item.setElec(dto.getElec())
//                    .setElecFee(dto.getElecFee())
//                    .setServFee(dto.getServFee());
//        });
//        return blacklist;
//    }
}
