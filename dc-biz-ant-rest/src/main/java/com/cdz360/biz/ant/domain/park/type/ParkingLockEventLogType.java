package com.cdz360.biz.ant.domain.park.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum ParkingLockEventLogType implements DcEnum {

    UNKNOWN(0, "未知"),

    LOCK(10, "闭锁"),
    UNLOCK(11, "开锁"),
    CAR_OUT(20, "出车"),
    CAR_IN(21, "进车"),
    PLUG_CONNECT(30, "插枪"),
    CHARGER_START(31, "开启充电"),
    CHARGER_FINISHED(32, "充电完成"),
    PLUG_DISCONNECT(33, "拔枪");

    @JsonValue
    private final int code;
    private final String desc;

    ParkingLockEventLogType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @JsonCreator
    public static ParkingLockEventLogType valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (ParkingLockEventLogType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return ParkingLockEventLogType.UNKNOWN;
    }

}
