package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.cus.param.CustomerAttractListParam;
import com.cdz360.biz.model.trading.sync.vo.CustomerAttractBiVo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, fallbackFactory = DataCoreAttractFeignHystrix.class)
public interface DataCoreAttractFeignClient {

    // 获取引流汇总数据
    @PostMapping(value = "/dataCore/cusAttractSync/getBiVo")
    Mono<ObjectResponse<CustomerAttractBiVo>> getCustomerAttractBi(@RequestBody CustomerAttractListParam param);

    // 根据客户类型获取引流客户详情数据
    @PostMapping(value = "/dataCore/cusAttractSync/getBiList")
    Mono<ListResponse<CustomerAttractBiVo>> getAttractBiList(@RequestBody CustomerAttractListParam param);

}
