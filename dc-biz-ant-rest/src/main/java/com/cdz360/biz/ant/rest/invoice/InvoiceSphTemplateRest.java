package com.cdz360.biz.ant.rest.invoice;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.feign.InvoiceFeignClient;
import com.chargerlinkcar.framework.common.domain.invoice.param.ListInvoiceSphTemplateParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedSalTempRefVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Tag(name = "发票商品行模板相关接口", description = "发票商品行模板相关接口")
@RequestMapping("/api/invoice/sph/template")
public class InvoiceSphTemplateRest {

    @Autowired
    private InvoiceFeignClient invoiceFeignClient;

    @Operation(summary = "获取可见的商品行模板列表")
    @PostMapping(value = "/findAll")
    public ListResponse<InvoicedSalTempRefVo> findAllInvoiceSphTemplate(
        ServerHttpRequest request, @RequestBody ListInvoiceSphTemplateParam param) {
        log.info("获取可见的商品行模板列表: {}", JsonUtils.toJsonString(param));
        return invoiceFeignClient.findAllInvoiceSphTemplate(param);
    }
}
