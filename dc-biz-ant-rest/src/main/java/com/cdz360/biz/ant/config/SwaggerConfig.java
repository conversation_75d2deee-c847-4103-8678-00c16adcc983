package com.cdz360.biz.ant.config;

import org.springframework.context.annotation.Configuration;

@Configuration
public class SwaggerConfig {

//    final String desc = "C端用户相关";
//    final ApiInfo apiInfo = new ApiInfo("chargerlink-car-ant", desc,
//            "0.0.1", "", null,
//            "", "", Collections.emptyList());
//
//    @Bean
//    public Docket petApi() {
//        return new Docket(DocumentationType.SWAGGER_2).select()
//                // 仅显示 com.cdz360.biz.ant.rest 目录下的接口
//                .apis(RequestHandlerSelectors.basePackage("com.cdz360.biz.ant.rest"))
//                .build().apiInfo(apiInfo);
//    }


}
