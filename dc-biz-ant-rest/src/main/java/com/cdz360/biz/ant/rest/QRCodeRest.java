package com.cdz360.biz.ant.rest;

import com.cdz360.biz.ant.utils.QRCodeGenerator;
import com.google.zxing.WriterException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

/**
 * <AUTHOR>
 *  //二维码生成
 * @since 2019/8/1
 **/
@Slf4j
@RestController
@RequestMapping("/api/qr")
public class QRCodeRest {


    @GetMapping(value="/qrimage")
    public ResponseEntity<byte[]> getQRImage() {

        //二维码内的信息
        String info = "This is my first QR Code";

        byte[] qrcode = null;
        try {
            qrcode = QRCodeGenerator.getQRCodeImage(info, 256, 256);
        } catch (WriterException e) {
            log.error("Could not generate QR Code, WriterException ::{} " + e.getMessage());
            e.printStackTrace();
        } catch (IOException e) {
            log.error("Could not generate QR Code, IOException :: {}" + e.getMessage());
        }

        // Set headers
        final HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.IMAGE_PNG);

        return new ResponseEntity<byte[]> (qrcode, headers, HttpStatus.CREATED);
    }
}
