package com.cdz360.biz.ant.domain.vo;

import com.cdz360.biz.model.finance.type.BizType;
import com.cdz360.biz.model.iot.vo.EvseStatusBi2;
import com.cdz360.biz.model.site.type.SiteCategory;
import com.cdz360.biz.model.sys.po.SiteGroupPo;
import com.cdz360.biz.model.trading.site.vo.ImageVo;
import com.cdz360.biz.model.trading.site.vo.SiteTemplateVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 站点简单信息
 *
 * <AUTHOR>
 * @since Create on 2018/8/6 10:36
 */
@Data
@Schema(description = "站点简单信息")
@Accessors(chain = true)
public class SiteSimpleInfoVo {

    /**
     * 站点id
     */
    @Schema(description = "站点id")
    private String siteId;
    /**
     * 站点名称
     */
    @Schema(description = "站点名称")
    private String siteName;
    /**
     * 站点编号
     */
    @Schema(description = "站点编号")
    private String siteNo;
    /**
     * 站点实时冻结金额
     */
    @Schema(description = "站点实时冻结金额")
    private BigDecimal frozenAmount;
    /**
     * 站点经度
     */
    @Schema(description = "站点经度")
    private BigDecimal longitude;
    /**
     * 站点纬度
     */
    @Schema(description = "站点纬度")
    private BigDecimal latitude;
    /**
     * 站点地址
     */
    @Schema(description = "站点地址")
    private String address;
    /**
     * 省份编码
     */
    @Schema(description = "省份编码")
    private Integer province;
    /**
     * 省份名称
     */
    @Schema(description = "省份名称")
    private String provinceName;
    /**
     * 城市编码
     */
    @Schema(description = "城市编码")
    private Integer city;
    /**
     * 城市名称
     */
    @Schema(description = "城市名称")
    private String cityName;
    /**
     * 区编码
     */
    @Schema(description = "区编码")
    private Integer district;
    /**
     * 区名称
     */
    @Schema(description = "区名称")
    private String districtName;
    /**
     * 站点编号
     */
    @Schema(description = "站点编号")
    private String idNo;

    /**
     * 站点id
     */
    @Schema(description = "站点")
    private String id;

    /**
     * 站点状态 {@link com.chargerlink.device.business.constant.SiteStatusEnum}
     */
    @Schema(description = "站点状态")
    private Integer status;
    /**
     * 顶级商户ID
     */
    private Long topCommId;
    /**
     * 运营商ID
     */
    @Schema(description = "运营商ID")
    private Long operateId;
    /**
     * 运营商全称
     */
    @Schema(description = "运营商全称")
    private String operateName;
    /**
     * 运营商简称
     */
    @Schema(description = "运营商简称")
    private String operateShortName;
    /**
     * 所属集团商户ID
     */
    @Schema(description = "所属集团商户ID")
    private Long maxCommercialId;
    /**
     * 所属集团商户名称
     */
    @Schema(description = "所属集团商户名称")
    private String maxCommercialName;
    /**
     * 站点类型**0-未知 1-公共 2-个人 3-运营**
     */
    @Schema(description = "站点类型**0-未知 1-公共 2-个人 3-运营**")
    private Integer type;
    /**
     * {@link BizType}
     */
    @Schema(description = "运营属性. 0,未知; 1,自营; 2,非自营; 3,互联互通; 4, 脱机自营; 5, 脱机非自营;")
    private Integer bizType;

    @Schema(title = "运营方名称")
    private String bizName;

    /**
     * 工作日服务时间
     */
    @Schema(description = "工作日服务时间")
    private String serviceWorkdayTime;
    /**
     * 节假日服务时间
     */
    @Schema(description = "节假日服务时间")
    private String serviceHolidayTime;
    /**
     * 使用范围**0-未知 1-对外开放 2-内部使用 3-特定人群**
     */
    @Schema(description = "使用范围**0-未知 1-对外开放 2-内部使用 3-特定人群**")
    private Integer scope;
    /**
     * 上线时间
     */
    @Schema(description = "上线时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date onlineDate;
    /**
     * 充电桩总数
     */
    @Schema(description = "充电桩总数")
    private Integer countOfBox;
    /**
     * 枪头总数
     */
    @Schema(description = "枪头总数")
    private Integer countOfCharger;
    /**
     * 收费说明
     */
    @Schema(description = "收费说明")
    private String feeDescription;
    /**
     * 站点收费范围 最低
     */
    @Schema(description = "站点收费范围 最低")
    private long feeMin;
    /**
     * 站点收费范围 最高
     */
    @Schema(description = "站点收费范围 最高")
    private long feeMax;

    /**
     * 鼎充专用 开票标识 {@link com.chargerlink.device.business.constant.SiteEnum.InvoicedValidType}
     */
//    @Schema(description = "鼎充专用 开票标识")
    @Schema(description = "移动端开票标识")
    private Boolean invoicedValid;

    @Schema(description = "移动端开票主体(移动端允许开票需要配置)")
    @JsonInclude(Include.NON_NULL)
    private Long mobileTempSalId;

    @Schema(description = "平台开票标识")
    private Boolean platformInvoicedValid;

    /**
     * 站点默认计费模板Id
     */
    @Schema(description = "站点默认计费模板Id")
    private Long templateId;

    /**
     * 站点默认计费模板名称
     */
    @Schema(description = "站点默认计费模板名称")
    private String templateName;
    /**
     * 站点名称
     */
    @Schema(description = "站点名称")
    private String name;

    /**
     * 站点手机号
     */
    @Schema(description = "站点手机号")
    private String contactsPhone;

    @Schema(description = "备注")
    private String remark;

    /**
     * 场站下的紧急卡数
     */
    @Schema(description = "场站下的紧急卡数")
    private Integer urgencyCardNum;

    /**
     * 默认扣款类型 {@link com.cdz360.base.model.base.type.PayAccountType}
     */
    private Integer defaultPayType;
    /**
     * 扣款账户ID
     */
    private Long payAccountId;

    @Schema(description = "场站总功率，所有桩功率总和")
    private BigDecimal totalPower;

    @Schema(description = "光储充类型")
    private List<SiteCategory> category;

    @Schema(description = "站点图片")
    private List<ImageVo> imageList;

    private EvseStatusBi2 evseStatusBi2;

    @Schema(description = "场站综合评级 level平均值, 保留一位小数", example = "3.6")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal avgLevel;


    @Schema(description = "光伏装机功率")
    private BigDecimal pvInstalledCapacity;

    @Schema(description = "储能充电支出模板ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long essInPriceId;

    @Schema(description = "储能放电收入模板ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long essOutPriceId;

    @Schema(description = "储能装机总功率, 单位: kW")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal essPower;

    @Schema(description = "储能装机容量, 单位: kW·h")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal essCapacity;

    @Schema(description = "价格模板列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<SiteTemplateVo> templateList;

    @Schema(description = "场站组信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<SiteGroupPo> siteGroupList;
}
