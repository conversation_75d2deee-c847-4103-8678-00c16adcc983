package com.cdz360.biz.ant.domain.request;

import io.swagger.v3.oas.annotations.Parameter;
import java.util.List;
import lombok.Data;

@Data
public class ModifyCardParam {

    @Parameter(name = "用户id")
    private Long userId;
    @Parameter(name = "车队名称")
    private String carDepart;
    @Parameter(name = "车牌号")
    private String carNo;
    @Parameter(name = "车辆自编号")
    private String carNum;
    @Parameter(name = "线路")
    private String lineNum;
    @Parameter(name = "逻辑卡号")
    private String cardNo;
    @Parameter(name = "物理卡号")
    private String cardChipNo;
    @Parameter(name = "所属商户Id")
    private Long commId;
    @Parameter(name = "卡名称")
    private String cardName;
    @Parameter(name = "可用场站Id列表，逗号分隔")
    private String stations;
    @Parameter(name = "备注信息")
    private String remark;
    @Parameter(name = "兼容充值卡")
    private Integer deposit;
    @Parameter(name = "本地鉴权")
    private Boolean isLocalAuth;
    @Parameter(name = "本地鉴权可用场站Id列表")
    private List<String> localAuthSiteList;
}
