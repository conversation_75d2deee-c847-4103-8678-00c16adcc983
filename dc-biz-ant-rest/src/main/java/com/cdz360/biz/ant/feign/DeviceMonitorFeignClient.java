package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.cus.user.dto.WhiteCardDto;
import com.cdz360.biz.model.trading.bi.param.WarningSummaryParam;
import com.cdz360.biz.model.trading.bi.warning.WarningSummaryDto;
import com.chargerlinkcar.framework.common.domain.vo.TemplateInfoVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * <AUTHOR>
 *
 * @since 2019.2.27
 */
@FeignClient(name = DcConstants.KEY_FEIGN_IOT_MONITOR)
@Component
public interface DeviceMonitorFeignClient {

    @RequestMapping(value = "/api/alarm/getWarningSummaryList",method = RequestMethod.POST)
    ListResponse<WarningSummaryDto> getWarningSummaryList(@RequestBody WarningSummaryParam param);

}
