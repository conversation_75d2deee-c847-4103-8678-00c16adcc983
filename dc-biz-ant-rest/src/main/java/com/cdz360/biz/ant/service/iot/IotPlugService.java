package com.cdz360.biz.ant.service.iot;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.biz.model.iot.param.ListPlugParam;
import com.chargerlinkcar.framework.common.feign.IotBizClient;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class IotPlugService {

    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;

    @Autowired
    private IotBizClient iotBizClient;


    public ListResponse<PlugVo> getPlugList(ListPlugParam param) {
        ListResponse<PlugVo> res = this.iotDeviceMgmFeignClient.getPlugList(param);
        FeignResponseValidate.check(res);
        return res;
    }


    public void updatePlugInfo(String evseNo, int plugIdx, String plugName) {
        iotBizClient.updatePlugInfo(evseNo, plugIdx, plugName);
    }
}
