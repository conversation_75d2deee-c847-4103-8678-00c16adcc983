package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.ant.domain.request.PlugDetailRequest;
import com.cdz360.biz.ant.domain.vo.PlugDetailVo;
import com.cdz360.biz.ant.service.TemporaryDateService;
import com.chargerlinkcar.framework.common.utils.AssertUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since Created on 15:10 2019/5/15.
 */
@Slf4j
@RestController
@RequestMapping("/api")
public class TemporaryDateRest {


    @Autowired
    private TemporaryDateService interfaceService;

    /**
     * 充电中枪头详情
     *
     * @param plugDetailRequest
     * @return
     */
    @RequestMapping(value = "/chargerTemporaryDate", method = RequestMethod.POST)
    public ListResponse<PlugDetailVo> chargerTemporaryDate(
            @RequestBody PlugDetailRequest plugDetailRequest) {
        AssertUtil.notEmpty(plugDetailRequest.getEvseId(), "桩编号不能为空");
        return interfaceService.chargerTemporaryDate(plugDetailRequest);
    }
}
