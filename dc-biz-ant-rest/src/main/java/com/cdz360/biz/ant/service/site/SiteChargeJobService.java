package com.cdz360.biz.ant.service.site;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.service.order.ChargerService;
import com.cdz360.biz.model.trading.site.param.ChargeJobLogParam;
import com.cdz360.biz.model.trading.site.param.ChargeJobParam;
import com.cdz360.biz.model.trading.site.po.SiteChargeJobPo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobLogVo;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobMoveCorpList;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobVo;
import com.chargerlinkcar.framework.common.domain.param.SiteChargeJobParam;
import com.chargerlinkcar.framework.common.feign.SiteDataCoreFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SiteChargeJobService {

    @Autowired
    private ChargerService chargerService;
    @Autowired
    private SiteService siteService;
    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;
    @Autowired
    private SiteDataCoreFeignClient siteDataCoreFeignClient;


    public BaseResponse bindingJob(SiteChargeJobParam param) {
        IotAssert.isNotNull(param.getSiteId(), "场站ID不能为空");
        IotAssert.isNotNull(param.getBindExistingJob(), "是否绑定已有任务？");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getPlugNoList()), "枪头列表不能为空");

        // 运营状态判断
        chargerService.checkPlugBizStatus(param.getPlugNoList());

        ObjectResponse<SitePo> siteSimpleInfoVoObjectResponse
            = siteDataCoreFeignClient.getSiteById(param.getSiteId());
        FeignResponseValidate.check(siteSimpleInfoVoObjectResponse);
        SitePo sitePo = siteSimpleInfoVoObjectResponse.getData();
        IotAssert.isTrue(PayAccountType.UNKNOWN != PayAccountType.valueOf(
                sitePo.getDefaultPayType()),
            "后台启动禁用模式下无法开启充电");

        return dataCoreFeignClient.bindingJob(param);
    }


    public BaseResponse changeJobStatus(Long jobId, Integer jobStatus) {
        return dataCoreFeignClient.changeJobStatus(jobId, jobStatus);
    }

    public BaseResponse modifyChargeJob(SiteChargeJobParam param) {
        IotAssert.isNotNull(param.getExistingJobId(), "修改时，任务ID不能为空");
        return dataCoreFeignClient.modifyChargeJob(param);
    }


    public BaseResponse unbindingJob(List<String> plugNoList) {
        return dataCoreFeignClient.unbindingJob(plugNoList);
    }

    public ListResponse<SiteChargeJobPo> getSiteChargeJobBySiteId(String siteId, String jobName) {
        return dataCoreFeignClient.getSiteChargeJobBySiteId(siteId, jobName);
    }

    public ListResponse<SiteChargeJobVo> getChargeJobList(ChargeJobParam param) {
        return dataCoreFeignClient.jobList(param);
    }

    public ListResponse<SiteChargeJobLogVo> getChargeJobLogList(ChargeJobLogParam param) {
        return dataCoreFeignClient.getChargeJobLogList(param);
    }

    public ObjectResponse<SiteChargeJobMoveCorpList> getMoveCorpDetail(Long corpId, Long commId) {
        return dataCoreFeignClient.getMoveCorpDetail(corpId, commId);
    }

}
