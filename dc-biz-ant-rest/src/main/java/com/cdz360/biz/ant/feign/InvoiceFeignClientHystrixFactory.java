package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.domain.request.InvoicedModelSearchParam;
import com.cdz360.biz.ant.domain.vo.DescVo;
import com.cdz360.biz.ant.domain.vo.DzCommonVo;
import com.cdz360.biz.ant.domain.vo.IdListVo;
import com.cdz360.biz.ant.domain.vo.InvoicedChargerOrderVo;
import com.cdz360.biz.ant.domain.vo.InvoicedConfigMegVo;
import com.cdz360.biz.ant.domain.vo.InvoicedRecordChargerOrderSaveVo;
import com.cdz360.biz.ant.domain.vo.InvoicedRecordUpdateStatusVo;
import com.cdz360.biz.ant.domain.vo.InvoicedSiteValidYNVo;
import com.cdz360.biz.ant.domain.vo.InvoicedUserAutoAmountVo;
import com.cdz360.biz.model.oa.vo.OaInvoicedVo;
import com.chargerlinkcar.framework.common.domain.invoice.InvoicedRecordVo;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoicedModelDTO;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoicedTemplateSalDTO;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoicedTemplateSalDetailDTO;
import com.chargerlinkcar.framework.common.domain.invoice.dto.UpdateIdDTO;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceInfoParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.ListInvoiceSphTemplateParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.ListInvoicedRecordParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.ListInvoicedTempSalParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceInfoVo;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedModelVo;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedSalTempRefVo;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedTemplateSalDetailVo;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedTemplateSalVo;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @since 2019/7/3 11:19
 * <AUTHOR>
 */
@Slf4j
@Component
public class InvoiceFeignClientHystrixFactory implements FallbackFactory<InvoiceFeignClient> {

    @Override
    public InvoiceFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_INVOICE,
            throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_INVOICE,
            throwable.getStackTrace());
        return new InvoiceFeignClient() {
            @Override
            public ObjectResponse<InvoicedTemplateSalVo> getInvoicedTempSal(Long commId,
                String saleTin) {
                log.error("获取开票主体: {}, commId = {}, saleTin = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_INVOICE, commId, saleTin);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<InvoicedTemplateSalVo> getInvoicedTempSalById(Long id) {
                log.error("获取开票主体: {}, id = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_INVOICE, id);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<CorpInvoiceInfoVo> getCorpInvoiceDetail(
                CorpInvoiceInfoParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<InvoicedModelDTO> getCorpInvoiceModel(Long uid) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<DescVo> getInvoicedProductDesc(Long commercialId) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<DzCommonVo> saveInvoicedValid(
                InvoicedSiteValidYNVo invoicedSiteValidYNVo) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

//            @Override
//            public ObjectResponse<InvoicedTemplateSalDetailVo> getInvoicedTemplateSalDetailByCode(String code) {
//                log.error(throwable.getMessage());
//                return RestUtils.serverBusy4ObjectResponse();
//            }

            @Override
            public ListResponse<InvoicedTemplateSalDetailVo> getInvoicedTemplateSalDetailsByInvoiceType(
                String invoiceType, Long commercialId, int page, int size) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ListResponse();
            }

//            @Override
//            public ObjectResponse<InvoicedTemplateSalDetailVo> saveInvoicedTemplateSalDetailVo(InvoicedTemplateSalDetailVo invoicedTemplateSalDetailVo) {
//                log.error(throwable.getMessage());
//                return RestUtils.serverBusy4ObjectResponse();
//            }

            @Override
            public ObjectResponse<InvoicedUserAutoAmountVo> getInvoicedUserAutoAmountVo(Long userId,
                Long commId) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse saveInvoicedUserAutoAmount(
                InvoicedUserAutoAmountVo invoicedUserAutoAmountVo) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<InvoicedRecordVo> signHadExportToInvoice(
                InvoicedRecordChargerOrderSaveVo invoicedRecordChargerOrderSaveVo, String token) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse signForbiddenExportToInvoice(
                List<String> orderIds, String token) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<InvoicedRecordVo> getAllInvoicedRecords(
                ListInvoicedRecordParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<InvoicedRecordVo> getAllInvoicedRecordsByUserId(Long userId,
                int page, int size) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<InvoicedRecordVo> getAllInvoicedRecordsByUserIdAndStatus(
                Long userId, String invoicedStatus, int page, int size) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<InvoicedRecordVo> changeStatus(InvoicedRecordUpdateStatusVo dto) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<InvoicedRecordVo> changeSomeStatus(IdListVo idList) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<InvoicedRecordVo> exportToInvoice(IdListVo idList, String token) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<Map<String, String>> getPdfUrl(Long id) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<String> createTrackingNumber(IdListVo idList, String token) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<OaInvoicedVo> templateSalCheckBeforeInvoicingProcess(Long modelId,
                Long tempSalId, Long productTempId) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<InvoicedConfigMegVo> saveMegConfig(
                InvoicedConfigMegVo invoicedConfigMegVo) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<InvoicedConfigMegVo> getInvoicedMegConfig(String token) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<InvoicedChargerOrderVo> getAllInvoicedChargerOrders(Long customerId,
                Integer status, Boolean invoicedFlag,//是否已经关联了开票申请
                String keyword, String startDate,
                String endDate, String token,
                int page, int size) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ListResponse();
            }

//            @Override
//            public ListResponse<InvoicedChargerOrderVo> getAllInvoicedChargerOrders(
//                    Long invoicedId, int page, int size) {
//                log.error(throwable.getMessage());
//                return RestUtils.serverBusy4ListResponse();
//            }

            @Override
            public ListResponse<InvoicedChargerOrderVo> getAllInvoicedChargerOrders2c(
                Long customerId, Integer status, Boolean invoicedFlag,//是否已经关联了开票申请
                String keyword, String token, int page, int size) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<InvoicedModelVo> getDefaultInvoicedModel(Long userId) {
                log.error(throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<InvoicedTemplateSalVo> getInvoicedTempSalList(
                ListInvoicedTempSalParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<InvoicedTemplateSalVo> getInvoicedTempSalListByCommId(String commId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<InvoicedTemplateSalDTO> addInvoicedTempSal(
                InvoicedTemplateSalVo vo) {
                log.error("添加开票主题熔断: vo = {}", JsonUtils.toJsonString(vo));
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<UpdateIdDTO> updateInvoicedTempSal(InvoicedTemplateSalVo vo) {
                log.error("更新开票主题熔断: vo = {}", JsonUtils.toJsonString(vo));
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<InvoicedTemplateSalDTO> disableInvoicedTempSal(Long commId,
                String saleTin) {
                log.error("删除开票主题熔断: api = {}, commId = {}, saleTin = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_INVOICE, commId, saleTin);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<UpdateIdDTO> updateCorpInvoiceModel(InvoicedModelDTO modelDTO) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<UpdateIdDTO> updateUserInvoiceModel(InvoicedModelDTO modelDTO) {
                return RestUtils.serverBusy4ObjectResponse();
            }

//            @Override
//            public ListResponse<InvoicedModelVo> getInvoicedModelsByUserIdAndType(Long userId,
//                InvoiceType invoiceType,
//                int page,
//                int size) {
//                log.error("获取开票信息列表熔断: userId = {}, invoiceType = {}, page = {}, size = {}",
//                    userId, invoiceType, page, size);
//                return RestUtils.serverBusy4ListResponse();
//            }

            @Override
            public ListResponse<InvoicedModelVo> getInvoicedModelsByUserIdAndTypeSorted(
                InvoicedModelSearchParam param
            ) {
                log.error("获取开票信息列表熔断: param = {}", param);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<InvoicedSalTempRefVo> findAllInvoiceSphTemplate(
                ListInvoiceSphTemplateParam param) {
                log.error("获取可见的商品行模板列表: param = {}", param);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<InvoicedTemplateSalDetailDTO> getInvoiceSphDetail(
                Long tempRefId) {
                log.error("通过商品行模板ID获取商品行配置信息: tempRefId = {}", tempRefId);
                return RestUtils.serverBusy4ListResponse();
            }
        };
    }
}
