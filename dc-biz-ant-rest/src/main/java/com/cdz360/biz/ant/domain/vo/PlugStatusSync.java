package com.cdz360.biz.ant.domain.vo;
//
//import com.cdz360.base.model.base.type.PlugStatus;
//import com.cdz360.base.model.base.type.SupplyType;
//import com.cdz360.biz.ant.constant.WSSourceType;
//import com.fasterxml.jackson.annotation.JsonInclude;
//import io.swagger.v3.oas.annotations.media.Schema;
//import lombok.Data;
//import lombok.experimental.Accessors;
//
//import java.math.BigDecimal;
//
///**
// * PlugStatusSync
// *
// * @since 2/10/2020 2:45 PM
// * <AUTHOR>
// */
//@Data
//@Accessors(chain = true)
//public class PlugStatusSync {
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String seq;
//
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private WSSourceType source;
//
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String evseNo;
//
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String plugNo;
//
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String carNo;
//
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String carNum;
//
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String carVin;
//
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String lineNum;
//
//
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Integer startSoc;
//
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Integer soc;
//    @Deprecated
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private BigDecimal voltage;
//    @Deprecated
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private BigDecimal current;
//
//    @Schema(description = "桩实时功率: 单位: Kw", description = "目前仅直流桩计算值，交流不计算(后续桩上报功率再调整)")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private BigDecimal power;
//
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private BigDecimal dcVoltageO;
//
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private BigDecimal dcCurrentO;
//
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private BigDecimal electricity;
//
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private BigDecimal acVoltageA;
//
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private BigDecimal acVoltageB;
//
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private BigDecimal acVoltageC;
//
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private BigDecimal acCurrentA;
//
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private BigDecimal acCurrentB;
//
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private BigDecimal acCurrentC;
//
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Long startTime;
//
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Long stopTime;
//
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Long updateTime;
//
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Integer remainder;
//
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String siteId;
//
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String siteName;
//
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String name;
//    /*  参考：com.chargerlinkcar.core.domain.BsCharger
//     *  状态
//     *     故障：-300
//     *     临时故障：-200
//     *     维护中：-100
//     *     禁用：-50
//     *     未激活或离线：0
//     *     空闲：10
//     *     占用：12
//     *     有订单：15
//     *     准备就绪：80
//     *     充电中：100
//     *     充电中（辅枪）：101
//     *     停止充电：140
//     *     充电完成：150
//     */
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private PlugStatus status;
//
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private SupplyType supply;
//
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String orderNo;
//
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Integer errorCode;
//
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String errorMsg;
//
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String errorLevel;
//
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String errorCause;
//
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Integer alertCode;
//
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Integer duration;
//
//    public PlugStatusSync(WSSourceType wsSourceType) {
//        this.source = wsSourceType;
//    }
//}