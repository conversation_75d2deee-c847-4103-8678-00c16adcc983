package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ess.model.data.param.DataBiParam;
import com.cdz360.biz.model.bi.dashboard.CommStatisticBiVo;
import com.cdz360.biz.model.oa.vo.OaElecPayIncomeExpenseVo;
import com.cdz360.biz.model.trading.order.vo.ChargeOrderBiVo;
import com.cdz360.biz.model.trading.site.dto.DistrictSiteNumDto;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.oa.dto.BatchTaskVo;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDetailVo;
import java.util.List;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_BI,
    fallbackFactory = ReactorBizBiFeignHystrix.class)
public interface ReactorBizBiFeignClient {

    // 充电数据采集
    @PostMapping(value = "/api/biDashboard/chargeDataSample")
    Mono<ListResponse<CommStatisticBiVo>> chargeDataSample(@RequestBody DataBiParam param);

    // 获取历史(总)充电统计数据
    @PostMapping("/bi/order/getOrderBi")
    Mono<ObjectResponse<ChargeOrderBiVo>> getOrderBi(
        @RequestParam(value = "commIdChain", required = false) String commIdChain,
        @RequestParam(value = "siteId", required = false) String siteId,
        @RequestParam(value = "bizTypeList", required = false) List<Integer> bizTypeList);

    /**
     * 根据条件查询订单统计数据(尖峰平谷)
     *
     * @param param
     * @return
     */
    @RequestMapping(value = "/bi/order/getChargerOrderDetail", method = RequestMethod.POST)
    Mono<ObjectResponse<ChargerOrderDetailVo>> getChargerOrderDetail(
        @RequestBody ChargerOrderParam param);

    // 按区/县统计场站数量
    @PostMapping("/bi/site/getDistrictSiteNumList")
    Mono<ListResponse<DistrictSiteNumDto>> getDistrictSiteNumList(@RequestBody ListSiteParam param);

    // 获取有效上期电费支付台账，用于OA电费支付批量审批
    @PostMapping("/bi/site/getOaElecPayTaskListBySiteId")
    Mono<ListResponse<OaElecPayIncomeExpenseVo>> getOaElecPayTaskListBySiteId(
        @RequestBody List<BatchTaskVo> voList);

}
