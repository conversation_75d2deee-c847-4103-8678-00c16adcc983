package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.trading.site.param.ListOvertimeParkFeeOrderParam;
import com.cdz360.biz.model.trading.site.vo.OvertimeParkFeeOrderBi;
import com.cdz360.biz.model.trading.site.vo.OvertimeParkFeeOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderOvertimeParkInfoVo;
import com.chargerlinkcar.framework.common.feign.ChargeOrderBiFeignClient;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "超停收费订单相关接口", description = "超停收费订单相关接口")
@Slf4j
@RestController
public class OvertimeParkFeeOrderRest {

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private ChargeOrderBiFeignClient chargeOrderBiFeignClient;

    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;

    @Operation(summary = "获取超停收费订单列表")
    @PostMapping(value = "/api/overtimeParkFeeOrder/findAll")
    public ListResponse<OvertimeParkFeeOrderVo> findAll(
        ServerHttpRequest request, @RequestBody ListOvertimeParkFeeOrderParam param) {
        log.info("获取超停收费订单列表: {}, {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        return dataCoreFeignClient.findOvertimeParkFeeOrderAll(param);
    }

    @Operation(summary = "根据订单号获取占位费分时信息")
    @GetMapping(value = "/api/overtimeParkFeeOrder/getOverTimeParkDivisionByOrderNo")
    public ObjectResponse<OrderOvertimeParkInfoVo> getOverTimeParkDivisionByOrderNo(
        ServerHttpRequest request,
        @Parameter(name = "订单编号") @RequestParam(value = "orderNo") String orderNo) {
        IotAssert.isTrue(StringUtils.isNotBlank(orderNo), "订单号不能为空");
        return dataCoreFeignClient.getOverTimeParkDivisionByOrderNo(orderNo);
    }

    @Operation(summary = "超停收费订单统计")
    @PostMapping(value = "/api/overtimeParkFeeOrder/orderBi")
    public ObjectResponse<OvertimeParkFeeOrderBi> orderBi(
        ServerHttpRequest request, @RequestBody ListOvertimeParkFeeOrderParam param) {
        log.info("超停收费订单统计: {}, {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        return dataCoreFeignClient.overtimeParkFeeOrderBi(param);
    }

    @Operation(summary = "取消超停收费订单")
    @PostMapping(value = "/api/overtimeParkFeeOrder/cancel")
    public ObjectResponse<Integer> cancel(
        ServerHttpRequest request, @RequestBody ListOvertimeParkFeeOrderParam param) {
        log.info("取消超停收费订单: {}, {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        IotAssert.isNotNull(param, "请传入参数");
        IotAssert.isNotBlank(param.getOrderNo(), "请传入订单号");
        return dataCoreFeignClient.overtimeParkFeeOrderCancel(param);
    }

    @Operation(summary = "超停收费订单导出")
    @PostMapping(value = "/api/overtimeParkFeeOrder/exportOrderBi")
    public Mono<ObjectResponse<ExcelPosition>> exportOrderBi(
        ServerHttpRequest request, @RequestBody ListOvertimeParkFeeOrderParam param) {
        String p = JsonUtils.toJsonString(param);
        log.info("超停收费订单导出: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), p);

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("停充超时账单明细")
            .setFunctionMap(DownloadFunctionType.OVERTIME_PARK_FEE_ORDER)
            .setReqParam(p);
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return chargeOrderBiFeignClient.exportOvertimeParkFeeOrder(param);
    }
}
