package com.cdz360.biz.ant.rest.ocr.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PrintedTextOCRResponse {

    @JsonProperty("errcode")
    private long errCode;
    @JsonProperty("errmsg")
    private String errMsg;

    private List<OcrItem> items;

    @JsonProperty("img_size")
    private ImgSize imgSize;

    @Data
    @Accessors(chain = true)
    static class ImgSize {

        private long w;

        private long h;
    }

    @Data
    @Accessors(chain = true)
    static class OcrItem {

        private String text;

        private Position pos;
    }

    @Data
    @Accessors(chain = true)
    static class Position {

        @JsonProperty("left_top")
        private ImgPosition leftTop;

        @JsonProperty("right_top")
        private ImgPosition rightTop;

        @JsonProperty("right_bottom")
        private ImgPosition rightBottom;

        @JsonProperty("left_bottom")
        private ImgPosition leftBottom;
    }

    @Data
    @Accessors(chain = true)
    static class ImgPosition {

        private long x;
        private long y;
    }
}
