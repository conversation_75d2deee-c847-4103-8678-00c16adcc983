package com.cdz360.biz.ant.service.tj;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.feign.reactor.BizTjFeignClient;
import com.cdz360.biz.model.tj.kc.param.ListTjMaterialCostParam;
import com.cdz360.biz.model.tj.kc.param.ListTjSurveyParam;
import com.cdz360.biz.model.tj.kc.param.RepeatSurveyParam;
import com.cdz360.biz.model.tj.kc.param.TjSurveyBiParam;
import com.cdz360.biz.model.tj.kc.vo.TjMaterialCostVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyBiVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyVo;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TjMaterialCostService {

    @Autowired
    private BizTjFeignClient bizTjFeignClient;

    public Mono<ListResponse<TjMaterialCostVo>> findTjMaterialCost(
        ListTjMaterialCostParam param) {
        return bizTjFeignClient.findTjMaterialCost(param);
    }

    public Mono<ObjectResponse<TjMaterialCostVo>> getTjMaterialCostById(
        Long id) {
        return bizTjFeignClient.getTjMaterialCostById(id);
    }

    public Mono<ObjectResponse<TjMaterialCostVo>> saveTjMaterialCost(
        TjMaterialCostVo tjMaterialCostVo) {

        return bizTjFeignClient.saveTjMaterialCost(tjMaterialCostVo);
    }

    public Mono<ObjectResponse<TjMaterialCostVo>> disableTjMaterialCost(
        Long id) {
        return bizTjFeignClient.disableTjMaterialCost(id);
    }
}
