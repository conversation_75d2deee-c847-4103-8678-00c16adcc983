package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.type.UserType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.reactor.DataCoreInvoiceFeignClient;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.invoice.param.InvoiceRecordParam;
import com.cdz360.biz.model.invoice.type.InvoicedStatus;
import com.cdz360.biz.model.invoice.type.ProductType;
import com.cdz360.biz.model.trading.invoice.dto.CorpInvoiceRecordDto;
import com.cdz360.biz.model.trading.invoice.dto.InvoicedRecordDto;
import com.cdz360.biz.model.trading.invoice.dto.InvoicedSalTempRefDTO;
import com.cdz360.biz.model.trading.invoice.param.UserInvoiceRecordParam;
import com.cdz360.biz.model.trading.invoice.vo.CorpInvoiceInfoVo;
import com.cdz360.biz.model.trading.invoice.vo.CorpInvoicingContentVo;
import com.cdz360.biz.model.trading.invoice.vo.InvoicedTemplateSalDetailVo;
import com.cdz360.biz.model.trading.order.dto.PayBillInvoiceBi;
import com.cdz360.biz.oa.param.DepositInvoiceParam;
import com.cdz360.biz.oa.param.DepositInvoiceUserParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceRecordDetail;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * DepositInvoiceService
 *
 * @since 3/9/2023 6:35 PM
 * <AUTHOR>
 */
@Slf4j
@Service
public class DepositInvoiceService {

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private DataCoreInvoiceFeignClient dataCoreInvoiceFeignClient;

    /**
     * 个人商户会员 发票申请
     * @param request
     * @param param
     * @return
     */
    public Mono<ListResponse<InvoicedRecordDto>> userInvoiceSubmit2Audit(
        ServerHttpRequest request, @NonNull DepositInvoiceUserParam param) {

        Long opUid = AntRestUtils.getSysUid(request);
        String userName = AntRestUtils.getSysUserName(request);
        String idChain = AntRestUtils.getCommIdChain(request);

        UserInvoiceRecordParam dto = new UserInvoiceRecordParam();

        BeanUtils.copyProperties(param, dto);
        dto.setUid(param.getUserId());

        // 个人不传commId，此处默认填0
        dto.setCommId(param.getCommId() == null ? 0 : param.getCommId());

//        dto.setApplyNo(param.getApplyNo());
//        dto.setProcInstId(param.getProcInstId());
////        dto.setBillingProcessRequest();
////        dto.setCorpId();
//        dto.setUid(param.getUid());
////        dto.setCorpName();
        /**
         *  审批流提交审核时，发票状态置为未提交，避免开票审批那里可以导出。<br />
         *  等到审批流审批结束，走到开票节点时，再将发票状态改为submitted
         */
        dto.setStatus(InvoicedStatus.NOT_SUBMITTED);
//        dto.setTempSalId(param.getTempSalId());
//        dto.setChannel(param.getChannel());
//        dto.setSaleName(param.getSaleName());
//        dto.setSaleTin(param.getSaleTin());
//        dto.setProductTempName(param.getProductTempName());
//        dto.setProductTempId(param.getProductTempId());
//        dto.setModelId(param.getModelId());
//        dto.setInvoiceName(param.getInvoiceName());
//        dto.setCommId(param.getCommId());
//        dto.setInvoiceType(param.getInvoiceType());
//        dto.setInvoiceWay(param.getInvoiceWay());
//        dto.setOrderCnt(param.getOrderCnt());
//        dto.setActualElecFee(param.getActualElecFee());
//        dto.setFixElecFee(param.getFixElecFee());
//        dto.setActualServFee(param.getActualServFee());
//        dto.setFixServFee(param.getFixServFee());
//        dto.setActualTechServFee(param.getActualTechServFee());
//        dto.setFixTechServFee(param.getFixTechServFee());
//        dto.setElecNum(param.getElecNum());
//        dto.setServNum(param.getServNum());
//        dto.setTotalFee(param.getTotalFee());
//        dto.setFixTotalFee(param.getFixTotalFee());
        if (CollectionUtils.isNotEmpty(param.getInvoiceRecords())) {
            dto.setInvoiceRecords(param.getInvoiceRecords());
        }

        if (CollectionUtils.isNotEmpty(param.getActualData())) {
            dto.setActualData(param.getActualData());
        }

        if (param.getCorpInvoiceInfoVo() != null) {
            CorpInvoiceInfoVo corpInvoiceInfoVo = new CorpInvoiceInfoVo();
            BeanUtils.copyProperties(param.getCorpInvoiceInfoVo(), corpInvoiceInfoVo);
            dto.setCorpInvoiceInfoVo(corpInvoiceInfoVo);
        }

        if (CollectionUtils.isNotEmpty(param.getPayBillChoiceList())) {
            dto.setPayBillChoiceList(param.getPayBillChoiceList().stream().map(e -> {
                PayBillInvoiceBi bi = new PayBillInvoiceBi();
                BeanUtils.copyProperties(e, bi);
                return bi;
            }).collect(Collectors.toList()));
        }

        if (param.getTempRefVo() != null) {
            InvoicedSalTempRefDTO invoicedSalTempRefDTO = new InvoicedSalTempRefDTO();
            BeanUtils.copyProperties(param.getTempRefVo(), invoicedSalTempRefDTO);
            dto.setTempRefVo(invoicedSalTempRefDTO);
            if (CollectionUtils.isNotEmpty(param.getTempRefVo().getDetailVoList())) {
                dto.getTempRefVo()
                    .setDetailVoList(param.getTempRefVo().getDetailVoList().stream().map(e -> {
                        InvoicedTemplateSalDetailVo invoicedTemplateSalDetailVo = new InvoicedTemplateSalDetailVo();
                        BeanUtils.copyProperties(e, invoicedTemplateSalDetailVo);
                        return invoicedTemplateSalDetailVo;
                    }).collect(Collectors.toList()));
            }
        }

//        if(CollectionUtils.isNotEmpty(param.getTempTotalVoList())) {
//            dto.setTempTotalVoList(
//                param.getTempTotalVoList()
//                    .stream()
//                    .map(e -> {
//                        CompareTotalVo compareTotalVo = new CompareTotalVo();
//                        BeanUtils.copyProperties(e, compareTotalVo);
//                        return compareTotalVo;
//                    })
//                    .collect(Collectors.toList()));
//        }
//        dto.setInvoicingRemark(param.getInvoicingRemark());
////        dto.setReturnFlag(param.getReturnFlag());
////        dto.setReturnPlanVoList(param.getReturnPlanVoList());
////        dto.setReturnImages(param.getReturnImages());
//        dto.setApplyRemark(param.getApplyRemark());
//        dto.setAttachmentLink(param.getAttachmentLink());
//        dto.setIssuedTime(param.getIssuedTime());
//        dto.setIssuedRemark(param.getIssuedRemark());
//        dto.setFailRemark(param.getFailRemark());
//        dto.setAuditTime(param.getAuditTime());
//        dto.setAuditRemark(param.getAuditRemark());
//        dto.setAuditName(param.getAuditName());
//        dto.setCreateOpType(param.getCreateOpType());
//        dto.setCreateOpId(param.getCreateOpId());
//        dto.setCreateOpName(param.getCreateOpName());
//        dto.setCreateTime(param.getCreateTime());
//        dto.setUpdateOpType(param.getUpdateOpType());
//        dto.setUpdateOpId(param.getUpdateOpId());
//        dto.setUpdateOpName(param.getUpdateOpName());
//        dto.setInvoiceCode(param.getInvoiceCode());
//        dto.setInvoiceNumber(param.getInvoiceNumber());
//        dto.setReviewTime(param.getReviewTime());

        // 操作人信息
        dto.setIdChain(idChain);
        dto.setOpId(opUid);
        dto.setOpName(userName);
        dto.setOpType(UserType.SYS_USER);

        dto.setInvoicedEnabled(false);

        return dataCoreInvoiceFeignClient.userInvoiceRecordSubmit2Audit(dto)
            .doOnNext(FeignResponseValidate::check);
    }

    /**
     * 企业 发票申请
     *
     * @param request
     * @param recordDetail
     * @param param
     * @return
     */
    public Mono<ObjectResponse<Integer>> invoiceSubmit2Audit(ServerHttpRequest request,
        @NonNull CorpInvoiceRecordDetail recordDetail, @NonNull DepositInvoiceParam param) {

        CorpInvoiceRecordDto dto = new CorpInvoiceRecordDto();
        dto.setCorpId(param.getCorpId()).setInvoiceWay(param.getInvoiceWay())
            .setBillingProcessRequest(Boolean.TRUE).setElecNum(BigDecimal.ZERO)
            .setServNum(BigDecimal.ZERO).setPrepaidCardNum(BigDecimal.ZERO);

        dto.setApplyNo(recordDetail.getApplyNo()).setTempSalId(recordDetail.getTempSalId())
            .setProductTempId(recordDetail.getProductTempId())
            .setActualElecFee(recordDetail.getActualElecFee())
            .setActualServFee(recordDetail.getActualServFee());

        AtomicReference<BigDecimal> fixElecFeeRef = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> fixServFeeRef = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> fixPrepaidCardFeeRef = new AtomicReference<>(BigDecimal.ZERO);
        List<CorpInvoicingContentVo> invoicingContentVoList = new ArrayList<>();    // 企业开票缺少对多张票的支持，此处先做兼容，后续再看是否要做进一步调整
        for (InvoiceRecordParam invRec : param.getInvoiceRecords()) {
            if (CollectionUtils.isNotEmpty(invRec.getContents())) {
                for (var invCtx : invRec.getContents()) {
                    if (ProductType.ELEC_ACTUAL_FEE.equals(invCtx.getProductType())) {
                        fixElecFeeRef.set(fixElecFeeRef.get().add(invCtx.getFixAmount()));
                        dto.setElecNum(dto.getElecNum().add(invCtx.getNum()));
                    } else if (ProductType.SERV_ACTUAL_FEE.equals(invCtx.getProductType())) {
                        fixServFeeRef.set(fixServFeeRef.get().add(invCtx.getFixAmount()));
                        dto.setServNum(dto.getServNum().add(invCtx.getNum()));
                    } else if (ProductType.PREPAID_CARD_FEE.equals(invCtx.getProductType())) {
                        fixPrepaidCardFeeRef.set(
                            fixPrepaidCardFeeRef.get().add(invCtx.getFixAmount()));
                        dto.setPrepaidCardNum(dto.getPrepaidCardNum().add(invCtx.getNum()));
                    }

                    CorpInvoicingContentVo invoicingContent = new CorpInvoicingContentVo();
                    invoicingContent.setProductName(invCtx.getProductName())
                        .setCode(invCtx.getCode()).setProductType(invCtx.getProductType())
                        .setTaxRate(invCtx.getTaxRate()).setFixAmount(invCtx.getFixAmount())
                        .setNum(invCtx.getNum()).setSpec(invCtx.getSpec()).setUnit(invCtx.getUnit())
                        .setPrice(invCtx.getPrice());
                    invoicingContentVoList.add(invoicingContent);
                }
            }
            if (StringUtils.isNotBlank(
                invRec.getRemark())) {    // 企业开票缺少对多张票的支持，此处先做兼容，后续再看是否要做进一步调整
                dto.setInvoicingRemark(invRec.getRemark());
            }
        }

        dto.setFixElecFee(fixElecFeeRef.get());
        dto.setFixServFee(fixServFeeRef.get());
        dto.setFixPrepaidCardFee(fixPrepaidCardFeeRef.get());
        dto.setFixTotalFee(
            fixElecFeeRef.get().add(fixServFeeRef.get()).add(fixPrepaidCardFeeRef.get()));
        dto.setInvoicingContent(invoicingContentVoList);

//        dto.setInvoicingRemark(param.getInvoicingRemark());

//        if (Boolean.TRUE.equals(param.getReturnFlag())) {
//            dto.setReturnFlag(1);
//        } else if (Boolean.FALSE.equals(param.getReturnFlag())) {
//            dto.setReturnFlag(0);
//            dto.setReturnPlanVoList(param.getReturnPlanVoList().stream().map(t -> {
//                CorpInvoiceReturnPlanVo vo = new CorpInvoiceReturnPlanVo();
//                switch (t.getIndex()) {
//                    case 1:
//                        vo.setPlanTitle("第一次");
//                        break;
//                    case 2:
//                        vo.setPlanTitle("第二次");
//                        break;
//                    case 3:
//                        vo.setPlanTitle("第三次");
//                        break;
//                }
//                vo.setPlanMoney(t.getAmount());
//                vo.setPlanTime(t.getTime());
//                return vo;
//            }).collect(Collectors.toList()));
//        }

        return Mono.just(this.corpInvoiceRecordSubmit2Audit(request, dto))
            .doOnNext(FeignResponseValidate::check);
    }

    public ObjectResponse<Integer> corpInvoiceRecordSubmit2Audit(ServerHttpRequest request,
        CorpInvoiceRecordDto dto) {
        log.info("企业客户开票记录提交到审核(非财务): {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(dto));
        return invoiceService.corpInvoiceRecordSubmit2Audit(dto);
    }
}