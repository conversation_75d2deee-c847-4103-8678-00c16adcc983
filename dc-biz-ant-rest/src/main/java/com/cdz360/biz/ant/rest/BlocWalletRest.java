package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.UserType;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.aop.CheckAuth;
import com.cdz360.biz.ant.service.AuthCenterService;
import com.cdz360.biz.ant.service.BalanceService;
import com.cdz360.biz.ant.service.sysLog.CustomerSysLogService;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.trading.deposit.vo.DepositSourceType;
import com.cdz360.biz.model.trading.order.po.PayBillPo;
import com.chargerlinkcar.framework.common.domain.CommercialSample;
import com.chargerlinkcar.framework.common.feign.CorpFeignClient;
import com.chargerlinkcar.framework.common.feign.DcCusBalanceFeignClient;
import com.chargerlinkcar.framework.common.feign.UserFeignClient;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * 集团钱包功能
 *
 * <AUTHOR>
 * @since 2018.11.21
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/BlocWallet")
@Tag(name = "集团钱包功能")
public class BlocWalletRest extends BaseController {

    @Autowired
    private BalanceService balanceService;
    @Autowired
    private CustomerSysLogService customerSysLogService;

    @Autowired
    private AuthCenterService iAuthCenterService;

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private CorpFeignClient corpFeignClient;

    @Autowired
    private DcCusBalanceFeignClient dcCusBalanceFeignClient;

    /**
     * 商户给集团账户手工调整余额
     *
     * @param request
     * @return
     */
    @Operation(summary = "集团账户手工调整余额")
    @RequestMapping(value = "/updateBlocWallet", method = RequestMethod.POST)
    @CheckAuth(code = "corpUserDeposit")
    public BaseResponse updateBlocWallet(ServerHttpRequest request, @RequestBody @Valid PayBillPo po) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " po = {}", po);
        IotAssert.isTrue(StringUtils.isNotBlank(po.getUsername()), "客户名称不能为空");
        IotAssert.isTrue(StringUtils.isNotBlank(po.getPhone()), "客户手机号不能为空");
        List<Long> commIdList = this.getCommIdList2(request);
        if (org.springframework.util.CollectionUtils.isEmpty(commIdList)) {
            throw new DcArgumentException("参数错误");
        }


        CommercialSample commercialSample = this.getCommercialSample2(request);
        if (ObjectUtils.isEmpty(commercialSample)) {
            throw new DcArgumentException("参数错误");
        }
        Long commId = commercialSample.getComId();
        if (commId == null || commId <= 0) {
            throw new DcArgumentException("参数错误");
        }
        Long merchantId = commercialSample.getId();
        if (merchantId == null || merchantId <= 0) {
            throw new DcArgumentException("参数错误");
        }

        ObjectResponse<CorpPo> corpRes = corpFeignClient.getCorp(po.getUserId());
        FeignResponseValidate.check(corpRes);
        CorpPo corp = corpRes.getData();

        po.setCorpId(corp.getId());
        // 用户Id调整
        po.setUserId(corp.getUid());
        po.setCusPhone(corp.getPhone());
        po.setCusName(corp.getCorpName());

        // 商户信息
        po.setTopCommId(corp.getTopCommId());
        po.setCommId(corp.getCommId());

        Long opUid = commercialSample.getId();
        if (opUid == null || opUid <= 0) {
            throw new DcArgumentException("当前操作用户Id不存在");
        }

        // 账户信息
        po.setAccountType(PayAccountType.CORP);
        po.setAccountCode(corp.getTopCommId());

        // 操作人信息
        po.setOpUid(opUid);
        po.setOpName(commercialSample.getName());
        po.setOpUserType(UserType.SYS_USER);

        // 被操作用户类型
        po.setUserType(UserType.CORP_USER);

        // 充值来源
        po.setSourceType(DepositSourceType.MGM_WEB);

        ObjectResponse<Integer> res = balanceService.updateBalanceV2(po);
        customerSysLogService.rechargeLog(po.getUsername(), po.getPhone(),
                po.getFlowType(), DecimalUtils.add(po.getAmount(), po.getFreeAmount()), request);
        return res;
    }

}
