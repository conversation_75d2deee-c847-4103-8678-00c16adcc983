package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.domain.request.UpgradeTaskRequest;
import com.cdz360.biz.model.iot.param.UpgradeTaskInfoRequest;
import com.cdz360.biz.model.iot.vo.UpgradeTaskDetailVo;
import com.cdz360.biz.model.iot.vo.UpgradeTaskInfoVo;
import com.cdz360.biz.model.iot.vo.UpgradeTaskVo;
import com.cdz360.biz.model.iot.param.UpgradeTaskDetailRequest;
import com.cdz360.biz.model.iot.param.UpgradeTaskListRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, fallbackFactory = IotDeviceMgmFeignClientHystrixFactory.class)
public interface IotDeviceMgmFeignClient {

    /**
     * 海外版-发起桩升级
     *
     * @param upgradeTaskRequest
     * @return
     */
    @PostMapping("/device/upgrade/commercial/startTask")
    BaseResponse startEssTask(@RequestBody UpgradeTaskRequest upgradeTaskRequest);

    /**
     * 分页获取 获取场站下升级记录列表
     *
     * @return
     */
    @PostMapping("/device/upgrade/commercial/getUpgradeTaskListBySite")
    ListResponse<UpgradeTaskVo> getEssUpgradeTaskListBySite(
        @RequestBody UpgradeTaskListRequest updateTaskListRequest);

    /**
     * 获取升级记录详情列表，不分页
     *
     * @return
     */
    @PostMapping("/device/upgrade/getUpgradeTaskDetailListByTaskId")
    ListResponse<UpgradeTaskDetailVo> getUpgradeTaskDetailListByTaskId(
        @RequestBody UpgradeTaskDetailRequest updateTaskDetailRequest);

    /**
     * 海外版-获取升级详情信息
     *
     * @param upgradeTaskInfoRequest
     * @return
     */
    @PostMapping("/device/upgrade/commercial/getUpgradeTaskInfo")
    ObjectResponse<UpgradeTaskInfoVo> getEssUpgradeTaskInfo(
        UpgradeTaskInfoRequest upgradeTaskInfoRequest);
}
