package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.discount.param.UpdateCorpDiscountParam;
import com.cdz360.biz.model.cus.settlement.dto.SettlementDto;
import com.cdz360.biz.model.cus.settlement.param.ListSettlementParam;
import com.cdz360.biz.model.cus.settlement.param.UpdateCorpSettlementParam;
import com.cdz360.biz.model.cus.settlement.vo.CorpSettlementCfgVo;
import com.cdz360.biz.model.cus.settlement.vo.SettlementVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderBiVo;
import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CorpSettlementFeignHystrix
        implements FallbackFactory<CorpSettlementFeignClient> {

    @Override
    public CorpSettlementFeignClient create(Throwable cause) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER, cause.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER, cause.getStackTrace());

        return new CorpSettlementFeignClient() {
            @Override
            public ObjectResponse<CorpSettlementCfgVo> getSettlementCfgById(Long cfgId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<CorpSettlementCfgVo> getSettlementCfg(Long corpId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<CorpPo> updateSettlementCfg(UpdateCorpSettlementParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SettlementVo> findSettlementList(ListSettlementParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<OrderBiVo> settlementBiForCorp(ListSettlementParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

//            @Override
//            public ObjectResponse<Integer> addSettlement(SettlementDto dto) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

            @Override
            public ObjectResponse<Integer> updateSettlement(SettlementDto dto) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<SettlementVo> getSettlementByBillNo(String billNo) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Integer> removeSettlementByBillNo(String billNo) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<SettlementVo> removeOrder4Settlement(SettlementDto dto) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<SettlementVo> appendOrder2Settlement(SettlementDto dto) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Integer> settlementByBillNo(String billNo) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<CorpPo> updateDiscount(UpdateCorpDiscountParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }
        };
    }
}
