package com.cdz360.biz.ant.service;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.utils.RedisUtil;
import com.cdz360.biz.model.common.constant.Constant;
import com.cdz360.biz.model.cus.corp.po.CorpOrgLoginVo;
import com.chargerlinkcar.framework.common.domain.SysUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * 企业管理平台登录
 */
@Slf4j
@Service
public class LoginService {

    public static final String USER2TOKEN_PREFIX = "authc:user-name-token";
    public static final String TOKEN2USER_PREFIX = "authc:token-user";
    private static final String CORPPREFIX = "corp:";
    private final long authExpire = 3600 * 7 * 24;
    @Autowired
    private RedisUtil redisUtil;

    public String getTokenAndSave(CorpOrgLoginVo user) {
        String key = USER2TOKEN_PREFIX + user.getAccount();
        String token = redisUtil.get(key);
        if (token == null) {
            token = TokenGenerator.generateValue();
        }
        redisUtil.put(token, JsonUtils.toJsonString(user), authExpire);
        redisUtil.put(key, token, authExpire);
        return token;
    }

    public void logout(CorpOrgLoginVo user) {
        String key = USER2TOKEN_PREFIX + user.getAccount();
        String token = redisUtil.get(key);
        if (token == null) {
            return;
        }
        redisUtil.del(token);
    }

    public String getUserJson(String token) {
        return redisUtil.get(TOKEN2USER_PREFIX, token);
//        return redisUtil.get(token);
    }

    public CorpOrgLoginVo getUserByRequest(ServerHttpRequest request) {
        String token = request.getHeaders().getFirst(Constant.CURRENT_USER_TOKEN);
        return this.getUser(token);
    }

    public CorpOrgLoginVo getUser(String token) {
        log.info("toke ================== {}", token);
        String userJson = getUserJson(token);
        log.info("user ============= {}", userJson);
        if (StringUtils.isEmpty(userJson)) {
            return null;
        }

        SysUser sysUser = JsonUtils.fromJson(userJson, SysUser.class);
        if (null == sysUser) {
            return null;
        }

        CorpOrgLoginVo result = new CorpOrgLoginVo();
        result.setCommIdChain(sysUser.getCommIdChain())
            .setOrgIds(sysUser.getOrgIds())
            .setGids(sysUser.getGids())
            .setCorpPo(sysUser.getCorpPo())
            .setId(sysUser.getId())
            .setCorpId(sysUser.getCorpId())
            .setOrgLevel(sysUser.getOrgLevel())
            .setAccount(sysUser.getUsername())
            .setCommId(sysUser.getCommId());

        log.info("================{}", JsonUtils.toJsonString(result));
        return result;
    }

}
