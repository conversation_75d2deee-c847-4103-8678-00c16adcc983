package com.cdz360.biz.ant.rest.iot;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.service.iot.IotEvseModelService;
import com.cdz360.biz.model.trading.iot.po.EvseModelPo;
import com.chargerlinkcar.framework.common.domain.param.ListEvseModelParam;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Tag(name = "设备型号相关接口", description = "设备型号")
public class IotEvseModelRest {

    @Autowired
    private IotEvseModelService service;

    @PostMapping("/api/evseModel/list")
    public ListResponse<EvseModelPo> listEvseBundlePage(@RequestBody ListEvseModelParam param) {
        log.info("listEvseBundlePage param: {}", param);
        return service.listEvseBundlePage(param);
    }

    @PostMapping("/api/evseModel/add")
    public BaseResponse addEvseModel(@RequestBody EvseModelPo po) {
        log.info("addEvseModel po: {}", po);
        return service.addEvseModel(po);
    }

    @PostMapping("/api/evseModel/edit")
    public BaseResponse editEvseModel(@RequestBody EvseModelPo po) {
        log.info("editEvseModel param: {}", po);
        return service.editEvseModel(po);
    }

    @PostMapping("/api/evseModel/changeStatus")
    public BaseResponse changeStatus(@RequestParam("id") Long id,
                                     @RequestParam("enable") Boolean enable) {
        log.info("changeStatus id: {} enable: {}", id, enable);
        return service.changeStatus(id, enable);
    }

    @PostMapping("/api/evseModel/remove")
    public BaseResponse remove(@RequestParam("id") Long id) {
        log.info("remove id: {}", id);
        return service.remove(id);
    }

    @GetMapping("/api/evseModel/getBrandList")
    public ListResponse<String> getBrandList() {
        return service.getBrandList();
    }

    @GetMapping("/api/evseModel/findById")
    public ObjectResponse<EvseModelPo> findById(@RequestParam("id") Long id) {
        return service.findById(id);
    }

}
