package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.model.cus.post.param.CusPostChangeStatusParam;
import com.cdz360.biz.model.cus.post.param.CusPostChangeTagParam;
import com.cdz360.biz.model.cus.post.param.CusPostReplyParam;
import com.cdz360.biz.model.cus.post.param.ListCusPostParam;
import com.cdz360.biz.model.cus.post.vo.CusPostVo;
import com.cdz360.biz.model.cus.vo.SiteCommentTotal;
import com.cdz360.biz.utils.feign.user.UserFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class CusPostService {

    @Autowired
    private UserFeignClient userFeignClient;

    public Mono<ListResponse<CusPostVo>> cusPostList(ListCusPostParam param) {
        return userFeignClient.cusPostList(param);
    }

    public Mono<ObjectResponse<SiteCommentTotal>> siteCommentTotal(String siteId, Boolean open) {
        if (StringUtils.isBlank(siteId)) {
            throw new DcArgumentException("场站编号不能为空");
        }

        return userFeignClient.siteCommentTotal(siteId, open);
    }

    public Mono<BaseResponse> cusPostChangeOpen(CusPostChangeStatusParam param) {
        CusPostChangeStatusParam.check(param);

        return userFeignClient.cusPostChangeOpen(param);
    }

    public Mono<BaseResponse> cusPostChangeTag(CusPostChangeTagParam param) {
        CusPostChangeTagParam.check(param);

        return userFeignClient.cusPostChangeTag(param);
    }

    public Mono<BaseResponse> cusPostReply(CusPostReplyParam param) {
        CusPostReplyParam.check(param);
        return userFeignClient.cusPostReply(param);
    }
}
