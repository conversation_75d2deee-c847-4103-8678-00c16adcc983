package com.cdz360.biz.ant.config;

import com.cdz360.biz.ant.interceptor.AntResponseBodyI18nHandler;
import java.util.Locale;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.http.codec.ServerCodecConfigurer;
import org.springframework.web.reactive.accept.RequestedContentTypeResolver;
import org.springframework.web.reactive.config.WebFluxConfigurer;

@Configuration
public class AntLocalConfigurer implements WebFluxConfigurer {

    @Bean("messageSource")
    public ResourceBundleMessageSource resourceBundleMessageSource() {
        ResourceBundleMessageSource ms = new ResourceBundleMessageSource();
        ms.setBasenames("msg");
        ms.setDefaultLocale(Locale.CHINA);
        ms.setDefaultEncoding("UTF-8");
        ms.setUseCodeAsDefaultMessage(true);
        return ms;
    }

    @Bean
    public AntResponseBodyI18nHandler responseWrapper(
        ServerCodecConfigurer serverCodecConfigurer,
        RequestedContentTypeResolver requestedContentTypeResolver) {
        return new AntResponseBodyI18nHandler(serverCodecConfigurer.getWriters(),
            requestedContentTypeResolver, resourceBundleMessageSource());
    }
}
