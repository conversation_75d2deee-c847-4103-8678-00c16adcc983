package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.trading.site.dto.InspectionRecordDto;
import com.cdz360.biz.model.trading.site.dto.RecentInspectionRecordDto;
import com.cdz360.biz.model.trading.site.param.ChangeRecordParam;
import com.cdz360.biz.model.trading.site.param.InspectionParam;
import com.cdz360.biz.model.trading.site.param.RecordParam;
import com.cdz360.biz.model.trading.site.param.SiteInspectionRecordParam;
import com.cdz360.biz.model.trading.site.po.SiteInspectionCfgPo;
import com.cdz360.biz.model.trading.site.po.SiteInspectionRecordPo;
import com.cdz360.biz.model.trading.site.type.SiteInspectionStatus;
import com.cdz360.biz.model.trading.site.vo.InspectionRecordBi;
import com.cdz360.biz.model.trading.site.vo.InspectionRecordVo;
import com.cdz360.biz.model.trading.site.vo.SiteInspectionRecordVo;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2019/11/6 15:24
 */
@Slf4j
@Component
public class InspectionFeignClientHystrixFactory implements FallbackFactory<InspectionFeignClient> {
    @Override
    public InspectionFeignClient create(Throwable cause) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, cause.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, cause.getStackTrace());

        return new InspectionFeignClient() {

            @Override
            public ObjectResponse<RecentInspectionRecordDto> getRecentRecord(String siteId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<SiteInspectionCfgPo> getConfig(String siteId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse editConfig(SiteInspectionCfgPo req) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<InspectionRecordBi> getRecordBi(SiteInspectionRecordParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<InspectionRecordVo> getRecordVoList(SiteInspectionRecordParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<InspectionRecordDto> getRecords(RecordParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse changeStatus(Long sysUserId, Long recordId, SiteInspectionStatus status) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse changeStatusBatch(ChangeRecordParam param) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse recordDel(Long sysUserId, Long recordId) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<SiteInspectionRecordVo> getDetail(Long id) {
                return RestUtils.serverBusy4ObjectResponse();
            }

//            @Override
//            public ObjectResponse<ExcelPosition> recordExport(Long id) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

            @Override
            public ListResponse<RecentInspectionRecordDto> getNeedInspectionSite(InspectionParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SiteInspectionRecordVo> getToBeInspectRecord(Long sysUserId, BaseListParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SiteInspectionRecordVo> getHistoryRecord(Long sysUserId, BaseListParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<SiteVo> getSiteByPlugNo(Long topCommId, String plugNo, String qrCode, String siteId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<SiteInspectionRecordPo> create(Long sysUserId, String siteId, Integer inspectionType) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse save(SiteInspectionRecordPo req) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse report(SiteInspectionRecordPo req) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<SiteInspectionRecordPo> transInspection(Long id, Long id1) {
                return RestUtils.serverBusy4ObjectResponse();
            }
        };
    }
}
