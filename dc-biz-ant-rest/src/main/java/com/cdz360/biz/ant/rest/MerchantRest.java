package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.biz.ant.domain.CustomUi;
import com.cdz360.biz.ant.service.CustomUiService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.merchant.dto.CommercialDto;
import com.chargerlinkcar.framework.common.rest.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.reactive.function.server.ServerRequest;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 *
 * @since 2018.12.7
 */
@Slf4j
@RestController
@RequestMapping("/api/merchant")
public class MerchantRest extends BaseController {

    @Autowired
    private CustomUiService customUiService;

    /**
     * 保存界面设置
     *
     * @param customUi
     * @return
     */
    @RequestMapping(value = "/customUiSaveOrUpdate", method = RequestMethod.POST)
    @ResponseBody
    public ObjectResponse customUiSaveOrUpdate(@RequestBody CustomUi customUi, ServerHttpRequest request) {

        log.info("customUi: {}", customUi);
        //使用商户logo，判断图片不能为空
//        int useCommercialLogo = customUi.getUseCommercialLogo();
//        if (useCommercialLogo != 0) {
//            if (StringUtils.isEmpty(customUi.getLogo())) {
//                throw new DcArgumentException("指定使用本商户logo，图片不能为空");
//            }
//        }
        //使用商户域名,校验域名是否合法
//        String domainName = customUi.getDomainName();
//        Integer useCommercialDomain = customUi.getUseCommercialDomain();
//        //不指定域名访问
//        if (useCommercialDomain != null && useCommercialDomain != 0) {
//            String domainNameRex = "^(http://|https://)?([a-zA-Z0-9-]|([a-zA-Z0-9-]+[a-zA-Z0-9-\\.]?[a-zA-Z0-9-]+))+\\.(cn|com|net|coop)$";
//            Pattern pattern = Pattern.compile(domainNameRex);
//            Matcher matcher = pattern.matcher(domainName);
//            if (!matcher.find()) {
//                throw new DcArgumentException("域名不合法");
//            }
//        }
        String token = getToken2(request);
        return customUiService.customUiSaveOrUpdate(customUi, token);
    }

    /**
     * 查询界面设置
     *
     * @param domainName 域名
     * @param merchants  商户号
     * @return
     */
    @RequestMapping("/getCustomUi")
    @ResponseBody
    public ObjectResponse<CustomUi> getCustomUi(@RequestParam(value = "domainName", required = false) String domainName,
                                                @RequestParam(value = "merchants", required = false) String merchants) {
        log.info("domainName: {}, merchants: {}", domainName, merchants);
        CustomUi customUi = new CustomUi();
        //参数判断
        if (StringUtils.isBlank(domainName) && (StringUtils.isBlank(merchants))) {
            throw new DcArgumentException("domainName和merchants不能都为空");
        } else if (StringUtils.isNotBlank(merchants)) {
            //封装查询条件
            customUi.setMerchants(merchants);
        } else if (StringUtils.isNotBlank(domainName)) {
            //封装查询条件
            customUi.setDomainName(domainName);
        }

        return customUiService.queryCustomUi(customUi);

    }

    @RequestMapping("/getCommPlatInfo")
    @ResponseBody
    public ObjectResponse<CommercialDto> getCommPlatInfo(ServerHttpRequest request) {
        Long commId = AntRestUtils.getCommId(request);
        return customUiService.getCommPlatInfo(commId);
    }


}
