//package com.cdz360.biz.ant.domain.vo;
//
//import com.cdz360.biz.model.invoice.type.InvoiceType;
//import com.cdz360.biz.model.invoice.type.InvoicedStatus;
//import com.cdz360.biz.model.invoice.type.InvoicedType;
//import com.fasterxml.jackson.annotation.JsonInclude;
//import io.swagger.v3.oas.annotations.media.Schema;
//import java.io.Serializable;
//import java.math.BigDecimal;
//import java.time.LocalDate;
//import java.time.ZonedDateTime;
//import javax.validation.constraints.NotNull;
//import javax.validation.constraints.Size;
//import lombok.Data;
//
///**
// * A DTO for the InvoicedRecord entity.
// */
//@Schema(description = "发票申请记录")
//@Data
//public class InvoicedRecordVo implements Serializable {
//
//    private Long id;
//
//    @Schema(description = "发票形式: ELECTRONIC -- 电子发票; PAPER -- 纸质发票")
//    @NotNull
//    private InvoicedType invoicedType;
//
//    @Schema(description = "发票类型: PER_COMMON -- 个人普票; ENTER_COMMON -- 企业普票; ENTER_PROFESSION -- 企业专票")
//    private InvoiceType invoiceType;
//
//    @Schema(description = "用户ID")
//    private Long userId;
//
//    @Schema(description = "客户名称")
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String userName;
//
//    @Schema(description = "客户手机号")
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String userPhone;
//
//    @Schema(description = "申请发票记录状态: SUBMITTED -- 审核中(未导出); REVIEWED -- 审核中(已导出);\n"
//        +
//        "AUDIT_FAILED -- 审核未通过(未导出); INVOICING_FAIL -- 开票失败(已导出); COMPLETED -- 已开具;\n"
//        +
//        "NOT_SUBMITTED -- 待提交; RED_DASHED -- 已红冲; INVALID -- 已作废")
//    private InvoicedStatus invoicedStatus;
//
//    @Schema(description = "发票代码")
//    private String invoiceCode;
//
//    @Schema(description = "发票号码")
//    private String invoiceNumber;
//
//    @Schema(description = "")
//    private LocalDate invoiceDate;
//
//    @Schema(description = "申请开票金额")
//    @NotNull
//    private BigDecimal invoiceAmount;
//
//    @Schema(description = "申请开票实际服务费: 单位元")
//    private BigDecimal servActualFee;
//
//    @Schema(description = "申请开票时间电费: 单位元")
//    private BigDecimal elecActualFee;
//
//    @Schema(description = "申请开票停充超时费: 单位元")
//    private BigDecimal parkActualFee;
//
//    @Schema(description = "发票抬头")
//    private String invoiceName;
//
//    @Schema(description = "--")
//    private String invoiceTin;
//
//    // 省
//    private String province;
//
//    // 市
//    private String city;
//
//    // 区
//    private String area;
//
//    // 详细街道信息
//    private String invoiceAddress;
//
//    @Size(max = 20)
//    private String invoiceTel;
//
//    private String invoiceBank;
//
//    private String invoiceAccount;
//
//    private String invoiceDesc;
//
//    private ZonedDateTime createdDate;
//
//    private ZonedDateTime reviewedDate;
//
//    private String creatorName;
//
//    // 邮箱
//    private String email;
//
//    // 物流单号(快递单号)
//    private String trackingNumber;
//
//    private String pdfUrl;
//
//    private String jpgUrl;
//
//    private String trackingOrderNo;//物流唯一订单号
//    private String receiverName;//收件人
//    private String receiverMobilePhone;//收件人手机号
//    private String receiverProvince;    //收件人省
//    private String receiverCity; // 收件人市
//    private String receiverArea;// 收件人区
//    private String receiverAddress;//收件人地址
//
//    private String source;//来源：0 用户主动开票 1 pc开票 2 定时任务开票
//    private String stationId;//场站id
//    private Long commercialId;//商户号id
//
//    private String hlhtApplyNo;
//    private Integer platformSource;
//    private String rejectReason;
//    private String hlhtUserNo;
//
//    @Schema(description = "oa流程实例ID")
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String procInstId;
//
//}
