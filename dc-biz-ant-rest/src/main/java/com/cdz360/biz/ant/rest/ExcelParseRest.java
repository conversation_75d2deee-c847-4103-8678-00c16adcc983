package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.service.CardService;
import com.cdz360.biz.ant.utils.FileUtil;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * excel解析
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/excel")
public class ExcelParseRest {

    @Autowired
    private CardService cardService;

    /**
     * 解析 excel文件
     *
     * @return
     */
    @PostMapping("/parse")
    public Mono<ObjectResponse<JsonNode>> parseExcel(@RequestPart FilePart file) {
        FileUtil.checkExcelFile(file);
        return cardService.parseExcel(file);
    }

    /**
     * 解析企业平台在线卡excel文件-临时存放
     *
     * @return
     */
    @Deprecated
    @PostMapping("/parseCorpCardExcel")
    public Mono<ObjectResponse<JsonNode>> parseCorpCardExcel(@RequestPart FilePart file) {
        FileUtil.checkExcelFile(file);
        return cardService.parseCorpCardExcel(file, 46l);
    }
}
