package com.cdz360.biz.ant.service.site;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.domain.Notice;
import com.cdz360.biz.ant.domain.vo.NoticeVo;
import com.cdz360.biz.ant.feign.MerchantFeignClient;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 站点公告
 * <p>
 * ISiteNoticeService
 *
 * <AUTHOR>
 * @since 2019.2.18
 */
@Service
@Slf4j
public class SiteNoticeService //implements ISiteNoticeService
{

    @Autowired
    private MerchantFeignClient merchantFeignClient;

    /**
     * 查询站点公告
     *
     * @param notice
     * @return
     */

    public ListResponse<Notice> queryNoticeBySite(NoticeVo notice, OldPageParam page) {
        log.info("ant分页查询站点公告{}--{}", JsonUtils.toJsonString(notice),
            JsonUtils.toJsonString(page));
        ListResponse<Notice> jsonResult = merchantFeignClient.queryNoticeBySite(notice,
            page.getPageSize(), page.getPageNum());
        return jsonResult;
    }

    /**
     * 新增站点公告
     *
     * @param notice 站点公告
     * @param commId
     * @return
     */

    public BaseResponse addNotice(Notice notice, String token, Long commId) {

        log.info("ant新增站点公告{}", JsonUtils.toJsonString(notice));
        //获取商户id和用户id
//        ObjectResponse<MerchantCommVo> jsonObjectmerchantCommVo = merchantFeignClient.getCurrentMerchant(token);
//        log.info("根据{}获取商户id和用户id{}", token, JsonUtils.toJsonString(jsonObjectmerchantCommVo));
//        if (jsonObjectmerchantCommVo == null || ResultConstant.RES_SUCCESS_CODE != jsonObjectmerchantCommVo.getStatus()) {
//            log.info("获取当前商户信息失败: {}", JsonUtils.toJsonString(jsonObjectmerchantCommVo));
//            throw new DcServiceException("获取当前商户信息失败");
//        }
        //得到商户id和用户id
//        MerchantCommVo merchantCommVo = jsonObjectmerchantCommVo.getData();

        //查询用户信息
        ObjectResponse<JsonNode> merchantJson = merchantFeignClient.findMerchantById(commId, token);
        if (merchantJson == null || ResultConstant.RES_SUCCESS_CODE != merchantJson.getStatus()
        ) {
            log.info("获取当前账号信息失败: {}", commId);
            throw new DcServiceException("获取账号信息失败");
        }
        //得到用户信息
        JsonNode userJson = merchantJson.getData();
        //拼接数据
        //公告属性：1站点公告  2定时充电  3系统维护 4硬件升级
        notice.setNoticeType(1);

        notice.setAccount(userJson.get("phone").asText());
        notice.setCommId(commId);

        BaseResponse jsonResult = merchantFeignClient.addNotice(notice);
        FeignResponseValidate.check(jsonResult);
        return jsonResult;
//        if (jsonResult == null ){
//            throw new DcServiceException("操作失败");
//        }else if (jsonResult.getInteger("status") != ResultConstant.RES_SUCCESS_CODE){
//            throw new DcServiceException(jsonResult.toJSONString());
//        } else {
//            return JSONObject.parseObject(jsonResult.toString(), ObjectResponse.class);
//        }
    }
}
