package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.type.OrderType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.feign.AntUserFeignClient;
import com.cdz360.biz.ant.feign.BizBiFeignClient;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.service.DashboardBiService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.bi.dashboard.*;
import com.cdz360.biz.model.bi.type.OrderByType;
import com.cdz360.biz.ess.model.data.param.DataBiParam;
import com.chargerlinkcar.framework.common.domain.type.DashboardOrderType;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

/**
 * 
 * @since 6/22/2020 10:13 AM
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/commDashboard")
@Tag(name = "管理后台商户首页", description = "管理后台首页")
public class CommDashboardRest {

//    private FakeUtils fakeUtils = new FakeUtils();

    @Autowired
    private BizBiFeignClient bizBiFeignClient;

    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;

    @Autowired
    private AntUserFeignClient antUserFeignClient;
    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private DashboardBiService dashboardBiService;

    @GetMapping("/getLastBi")
    @Operation( summary = "数据统计和快速入口-近n天充电订单统计")
    public ObjectResponse<ChargeOrderCommBiVo> getLastBi(//@RequestParam("days") Integer days,
                                                         @Parameter(name = "商户id") @RequestParam(value = "commId", required = false) Long commId,
                                                         @Parameter(name = "获取任意天的数据，默认是30天") @RequestParam(value = "days", required = false) Integer days,
                                                         ServerHttpRequest request) {
        log.debug("数据统计和快速入口-近 {} 天充电订单统计: {}", days, LoggerHelper2.formatEnterLog(request));
        log.debug("current_commid: {}", AntRestUtils.getCommId(request));
        String idChain = AntRestUtils.getCommIdChain(request);
        log.debug("idChain: {}", idChain);

        if (null == commId) {
            commId = AntRestUtils.getCommId(request);
        }

//        ChargeOrderCommBiVo res = (ChargeOrderCommBiVo) fakeUtils.reflaction(new ChargeOrderCommBiVo());

        return bizBiFeignClient.getLastBi(days, commId, idChain);
    }

    @GetMapping("/getSiteAndPlugStatus")
    @Operation( summary = "数据统计和快速入口-充电站 & 枪头实时监控")
    public ObjectResponse<SiteAndPlugBiVo> getSiteAndPlugStatus(@Parameter(name = "商户id(不传时获取名下商户)") @RequestParam(value = "commId", required = false) Long commId,
                                                                ServerHttpRequest request) {
        log.debug("数据统计和快速入口-充电站 & 枪头实时监控: {}", LoggerHelper2.formatEnterLog(request));
        log.debug("commId: {}", commId);
        String commIdChain = commId != null ? null : AntRestUtils.getCommIdChainAndFilter(request);

        ObjectResponse<SiteAndPlugBiVo> res = iotDeviceMgmFeignClient.getSiteAndPlugStatus(commId, commIdChain);
        FeignResponseValidate.check(res);
        ObjectResponse<SiteAndPlugBiVo> fillHlhtData = dataCoreFeignClient.siteAndPlugStatusFillHlhtData(commId, commIdChain);
        FeignResponseValidate.check(fillHlhtData);
        res.getData().setHlhtSiteCount(fillHlhtData.getData().getHlhtSiteCount())
                .setHlhtPower(fillHlhtData.getData().getHlhtPower());
        return res;
    }

    @GetMapping("/getCommBi")
    @Operation( summary = "数据统计和快速入口-获取商户信息统计")
    public ObjectResponse<CommPropertyCountBiVo> getCommBi(@Parameter(name = "商户id") @RequestParam(value = "commId") Long commId,
                                                           ServerHttpRequest request) {
        log.debug("数据统计和快速入口-获取商户信息统计: {}", LoggerHelper2.formatEnterLog(request));
        log.debug("current_commid: {}", AntRestUtils.getCommId(request));
        String idChain = AntRestUtils.getCommIdChain(request);
        log.debug("idChain: {}", idChain);

        return antUserFeignClient.getCommBi(commId, idChain);
    }

//    @GetMapping("/getLastDivisionBi")
//    @Operation( summary = "分时电量|电费服务费饼形图")
//    public BaseResponse getLastDivisionBi(@RequestParam("days") Integer days, ServerHttpRequest request) {
//        AntRestUtils.getCommId(request);
//        return null;
//    }

    @Operation( summary = "充电数据采集")
    @PostMapping(value = "/chargeDataSample")
    public Mono<ListResponse<CommStatisticBiVo>> chargeDataSample(
            ServerHttpRequest request, @RequestBody DataBiParam param) {
        log.info("充电数据采集: param = {}", JsonUtils.toJsonString(param));
        param.setCommIdChain(AntRestUtils.getCommIdChainAndFilter(request));
        return this.dashboardBiService.chargeDataSample(param);
    }

    @GetMapping("/getLastCommBi")
    @Operation( summary = "近 n 天营收数据")
    public ListResponse<CommStatisticBiVo> getLastCommBi(//@RequestParam("days") Integer days,
                                                         @Parameter(name = "商户id(不传时查询所有商户)") @RequestParam(value = "commId", required = false) Long commId,
                                                         @Parameter(name = "获取任意天的数据，默认是30天") @RequestParam(value = "days", required = false) Integer days,
                                                         ServerHttpRequest request) {
        log.debug("{} 近 {} 天营收数据 commId: {}", LoggerHelper2.formatEnterLog(request), days, commId);
        log.debug("current_commid: {}", AntRestUtils.getCommId(request));


//        List<CommStatisticBiVo> list = fakeUtils.reflactionList(new CommStatisticBiVo());
//        ListResponse<CommStatisticBiVo> res = bizBiFeignClient.getLastCommBi(null, idChain);

        var res = bizBiFeignClient.getLastCommBi(days, commId);
        FeignResponseValidate.check(res);
        return res;
    }

    @GetMapping("/getLastCommTopBi")
    @Operation( summary = "近 30 天营收数据对比")
    public ListResponse<SubCommStatisticBiVo> getLastCommTopBi(//@RequestParam("days") Integer days,
                                                               @Parameter(name = "商户id") @RequestParam(value = "commId") Long commId,
                                                               @Parameter(name = "排序按利用率、按充电量、按充电金额") @RequestParam("orderBy") DashboardOrderType orderBy,
                                                               ServerHttpRequest request) {
        log.debug("近 30 天营收数据对比: {}", LoggerHelper2.formatEnterLog(request));
        log.debug("current_commid: {}", AntRestUtils.getCommId(request));
//        String idChain = AntRestUtils.getCommIdChain(request);
        log.debug("commId: {}", commId);

//        List<SubCommStatisticBiVo> list = fakeUtils.reflactionList(new SubCommStatisticBiVo());

        return bizBiFeignClient.getLastCommTopBi(null, commId, /*idChain, */orderBy);
    }

    @GetMapping("/getUsageRateBoard")
    @Operation( summary = "昨日充电站利用率排名")
    public ListResponse<SiteUtilizationTopVo> getUsageRateBoard(@RequestParam("size") Integer size,
                                                                @Parameter(name = "商户id") @RequestParam(value = "commId") Long commId,
                                                                @RequestParam(value = "orderByType", required = false) OrderByType orderByType,
                                                                @RequestParam(value = "orderBy") OrderType orderBy,
                                                                ServerHttpRequest request) {
        log.debug("昨日充电站利用率排名: {}", LoggerHelper2.formatEnterLog(request));
        log.debug("size: {}, 商户id: {}, orderBy: {}", size, commId, orderBy);

        if(orderByType == null) {
            log.info("传入空排序，使用默认排序，使用利用率排序");
            orderByType = OrderByType.useRate;
        }

//        List<SiteUtilizationTopVo> list = fakeUtils.reflactionList(new SiteUtilizationTopVo());

        return bizBiFeignClient.getUsageRateBoard(size, commId, orderByType, orderBy);
    }
}