package com.cdz360.biz.ant.domain.dto;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * BatteryPack
 *
 * @since 10/12/2021 7:53 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BatteryPack {

    @Schema(description = "电池组号 电池箱X-Y register adrress = 电池箱0-0 register address + (X*LMUnum+Y)*40 )\n" +
                    "X代表簇号 X(0,ClusterNum),Y代表电池号Y(0,LMUnum) ClusterNum参考583, LMUnum参考584+A42")
    private Long packNo;

    // LMU SN
    @JsonProperty("lmuSn")
    private Integer lmuSn;
    // 温度点1,0.1℃
    @JsonProperty("tp1")
    private BigDecimal tempPoint1;
    // 温度点2,0.1℃
    @JsonProperty("tp2")
    private BigDecimal tempPoint2;
    // 温度点3,0.1℃
    @JsonProperty("tp3")
    private BigDecimal tempPoint3;
    // 温度点4,0.1℃
    @JsonProperty("tp4")
    private BigDecimal tempPoint4;
    // 正极柱温度采样点,0.1℃
    @JsonProperty("pctsp")
    private BigDecimal positiveColumnTempSamplingPoint;
    // 负极柱温度采样点,0.1℃
    @JsonProperty("nctsp")
    private BigDecimal negativeColumnTempSamplingPoint;
    // 电芯电压1,0.001V
    @JsonProperty("cv1")
    private BigDecimal cellVoltage1;
    // 电芯电压2,0.001V
    @JsonProperty("cv2")
    private BigDecimal cellVoltage2;
    // 电芯电压3,0.001V
    @JsonProperty("cv3")
    private BigDecimal cellVoltage3;
    // 电芯电压4,0.001V
    @JsonProperty("cv4")
    private BigDecimal cellVoltage4;
    // 电芯电压5,0.001V
    @JsonProperty("cv5")
    private BigDecimal cellVoltage5;
    // 电芯电压6,0.001V
    @JsonProperty("cv6")
    private BigDecimal cellVoltage6;
    // 电芯电压7,0.001V
    @JsonProperty("cv7")
    private BigDecimal cellVoltage7;
    // 电芯电压8,0.001V
    @JsonProperty("cv8")
    private BigDecimal cellVoltage8;
    // 电芯电压9,0.001V
    @JsonProperty("cv9")
    private BigDecimal cellVoltage9;
    // 电芯电压10,0.001V
    @JsonProperty("cv10")
    private BigDecimal cellVoltage10;
    // 电芯电压11,0.001V
    @JsonProperty("cv11")
    private BigDecimal cellVoltage11;
    // 电芯电压12,0.001V
    @JsonProperty("cv12")
    private BigDecimal cellVoltage12;
    // 电芯电压13,0.001V
    @JsonProperty("cv13")
    private BigDecimal cellVoltage13;
    // 电芯电压14,0.001V
    @JsonProperty("cv14")
    private BigDecimal cellVoltage14;
    // 电芯电压15,0.001V
    @JsonProperty("cv15")
    private BigDecimal cellVoltage15;
    // 电芯电压16,0.001V
    @JsonProperty("cv16")
    private BigDecimal cellVoltage16;
    // 电芯内阻1,Ω
    @JsonProperty("ir1")
    private Integer internalResistance1;
    // 电芯内阻2,Ω
    @JsonProperty("ir2")
    private Integer internalResistance2;
    // 电芯内阻3,Ω
    @JsonProperty("ir3")
    private Integer internalResistance3;
    // 电芯内阻4,Ω
    @JsonProperty("ir4")
    private Integer internalResistance4;
    // 电芯内阻5,Ω
    @JsonProperty("ir5")
    private Integer internalResistance5;
    // 电芯内阻6,Ω
    @JsonProperty("ir6")
    private Integer internalResistance6;
    // 电芯内阻7,Ω
    @JsonProperty("ir7")
    private Integer internalResistance7;
    // 电芯内阻8,Ω
    @JsonProperty("ir8")
    private Integer internalResistance8;
    // 电芯内阻9,Ω
    @JsonProperty("ir9")
    private Integer internalResistance9;
    // 电芯内阻10,Ω
    @JsonProperty("ir10")
    private Integer internalResistance10;
    // 电芯内阻11,Ω
    @JsonProperty("ir11")
    private Integer internalResistance11;
    // 电芯内阻12,Ω
    @JsonProperty("ir12")
    private Integer internalResistance12;
    // 电芯内阻13,Ω
    @JsonProperty("ir13")
    private Integer internalResistance13;
    // 电芯内阻14,Ω
    @JsonProperty("ir14")
    private Integer internalResistance14;
    // 电芯内阻15,Ω
    @JsonProperty("ir15")
    private Integer internalResistance15;
    // 电芯内阻16,Ω
    @JsonProperty("ir16")
    private Integer internalResistance16;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}