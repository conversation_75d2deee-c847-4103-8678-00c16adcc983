package com.cdz360.biz.ant.rest.site;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.feign.AntUserFeignClient;
import com.cdz360.biz.ant.service.CorpService;
import com.cdz360.biz.ant.service.sysLog.CustomerSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.cus.site.vo.BatchUpdateSiteAuthVo;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
public class SiteAuthRest {

    @Autowired
    private AntUserFeignClient antUserFeignClient;
    @Autowired
    private CustomerSysLogService customerSysLogService;
    @Autowired
    private CorpService corpService;

    @Operation(summary = "卡片管理、车辆管理批量修改站点功能")
    @PostMapping("/api/siteAuth/batchUpdate")
    public BaseResponse batchUpdate(ServerHttpRequest request, @RequestBody BatchUpdateSiteAuthVo vo) {
        log.info("req: {}", JsonUtils.toJsonString(vo));
        List<String> noSettlementCardList = null;
        if (NumberUtils.equals(vo.getAuthMediaType(), 1)) {
            noSettlementCardList = corpService.getNoSettlementCard(null, vo.getCardChipNoList());
        } else if (NumberUtils.equals(vo.getAuthMediaType(), 2)) {
            noSettlementCardList = corpService.getNoSettlementVin(vo.getAccountList(), AntRestUtils.getTopCommId(request));
        }
        if (CollectionUtils.isNotEmpty(noSettlementCardList)) {
            throw new DcServiceException("存在订单尚未结算，请处理后再进行操作");
        }
        antUserFeignClient.batchUpdate(vo);

        if (NumberUtils.equals(vo.getAuthMediaType(), 1)) {
            customerSysLogService.modifyCard(vo.getCardChipNoList(), request);
        } else if (NumberUtils.equals(vo.getAuthMediaType(), 2)) {
            customerSysLogService.modifyVin(vo.getAccountList(), request);
        }
        return RestUtils.success();
    }

}
