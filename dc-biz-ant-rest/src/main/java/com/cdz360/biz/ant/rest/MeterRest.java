package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.ant.service.sysLog.SiteSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.trading.meter.app.CurPowerDataEx;
import com.cdz360.biz.model.trading.meter.param.MeterDataListParam;
import com.cdz360.biz.model.trading.meter.param.MeterListParam;
import com.cdz360.biz.model.trading.meter.vo.MeterDataVo;
import com.cdz360.biz.model.trading.meter.vo.MeterEvseVo;
import com.cdz360.biz.model.trading.meter.vo.MeterVo;
import com.cdz360.biz.utils.feign.iot.MeterFeignClient;
import com.cdz360.biz.utils.feign.iot.MeterReactiveFeignClient;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 *
 * @since 9/18/2020 4:48 PM
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "电表接口", description = "meter")
@RequestMapping("/api/meter")
public class MeterRest {

    @Autowired
    private MeterFeignClient meterfeignClient;

    @Autowired
    private MeterReactiveFeignClient meterReactiveFeignClient;
    @Autowired
    private SiteSysLogService siteLogService;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;

    @Operation(summary = "电表列表")
    @PostMapping("/list")
    public ListResponse<MeterEvseVo> list(
        ServerHttpRequest request, @RequestBody(required = false) MeterListParam param) {
        log.info("电表列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        String commIdChain = AntRestUtils.getCommIdChain(request);
        param.setIdChain(commIdChain);
        return meterfeignClient.getMeterList(param);
    }

    @Operation(summary = "电表列表")
    @PostMapping("/findMeterList")
    public ListResponse<MeterVo> findMeterList(
        ServerHttpRequest request, @RequestBody(required = false) MeterListParam param) {
        log.info("电表列表-gwno: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return meterfeignClient.findMeterList(param);
    }

    @Operation(summary = "创建电表")
    @PostMapping("/createMeter")
    public BaseResponse createMeter(
        ServerHttpRequest request, @RequestBody MeterEvseVo param) {
        log.info("创建电表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        var res = meterfeignClient.createMeter(param);
        siteLogService.createMeterLog(param.getNo(), request);
        return res;
    }

    @Operation(summary = "更新电表")
    @PostMapping("/updateMeter")
    public BaseResponse updateMeter(
        ServerHttpRequest request, @RequestBody MeterEvseVo param) {
        log.info("更新电表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        var res = meterfeignClient.updateMeter(param);
        siteLogService.updateMeterLog(param.getNo(), request);
        return res;
    }

    @Operation(summary = "删除电表")
    @PostMapping("/deleteMeter")
    public BaseResponse deleteMeter(ServerHttpRequest request,
        @RequestParam(value = "id") Long id,
        @RequestParam(value = "no") String no) {
        log.info("删除电表: {}, id = {} no = {}",
            LoggerHelper2.formatEnterLog(request, false), id, no);
        var res = meterfeignClient.deleteMeter(id);
        siteLogService.deleteMeterLog(no, request);
        return res;
    }

    @GetMapping("/getMeterReading")
    public Mono<ObjectResponse<CurPowerDataEx>> getMeterReading(
        ServerHttpRequest request, @RequestParam(value = "meterNo") String meterNo) {
        log.info("电表实时读数: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), meterNo);
        return meterReactiveFeignClient.getMeterReading(meterNo);
    }

    @Operation(summary = "获取电表抄表数据")
    @PostMapping("/getMeterDataList")
    public ListResponse<MeterDataVo> getMeterDataList(
        ServerHttpRequest request, @RequestBody(required = false) MeterDataListParam param) {
        log.info("获取电表抄表数据: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        String commIdChain = AntRestUtils.getCommIdChain(request);
        param.setIdChain(commIdChain);
        param.setGidList(AntRestUtils.getSysUserGids(request));
        return dataCoreFeignClient.getMeterDataList(param);
    }

    @Operation(summary = "电表抄表数据导出")
    @PostMapping(value = "/exportMeterDataListBi")
    public Mono<ObjectResponse<ExcelPosition>> exportMeterDataListBi(ServerHttpRequest request,
        @RequestBody(required = false) MeterDataListParam param) {
        log.info("电表抄表数据导出: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        String commIdChain = AntRestUtils.getCommIdChain(request);
        param.setIdChain(commIdChain);
        param.setGidList(AntRestUtils.getSysUserGids(request));
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("电表抄表数据列表")
            .setFunctionMap(DownloadFunctionType.SITE_METER_DATA_LIST)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
    }

}