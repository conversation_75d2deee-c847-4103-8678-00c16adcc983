package com.cdz360.biz.ant.service.profit;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.feign.reactor.DataCoreProfitFeignClient;
import com.cdz360.biz.model.trading.profit.sett.param.ListProfitCfgParam;
import com.cdz360.biz.model.trading.profit.sett.param.SaveProfitCfgParam;
import com.cdz360.biz.model.trading.profit.sett.vo.ProfitCfgVo;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class ProfitCfgService {

    @Autowired
    private DataCoreProfitFeignClient profitFeignClient;

    public Mono<ObjectResponse<ProfitCfgVo>> addGcProfitCfg(SaveProfitCfgParam param) {
        IotAssert.isNull(param.getId(), "新增不需要提供配置ID");
        SaveProfitCfgParam.checkParam(param);
        return profitFeignClient.saveGcProfitCfg(param);
    }

    public Mono<ObjectResponse<ProfitCfgVo>> editGcProfitCfg(SaveProfitCfgParam param) {
        IotAssert.isNotNull(param.getId(), "请提供配置ID");
        SaveProfitCfgParam.checkParam(param);
        return profitFeignClient.saveGcProfitCfg(param);
    }

    public Mono<ObjectResponse<ProfitCfgVo>> enableGcProfitCfg(Long id, Boolean enable) {
        IotAssert.isNotNull(id, "请提供配置ID");
        IotAssert.isNotNull(enable, "请提供使能标识");
        return profitFeignClient.enableGcProfitCfg(id, enable);
    }

    public Mono<ObjectResponse<ProfitCfgVo>> deleteGcProfitCfg(Long id) {
        IotAssert.isNotNull(id, "请提供配置ID");
        return profitFeignClient.deleteGcProfitCfg(id);
    }

    public Mono<ListResponse<ProfitCfgVo>> findGcProfitCfg(ListProfitCfgParam param) {
        return profitFeignClient.findGcProfitCfg(param);
    }

    public Mono<ObjectResponse<ProfitCfgVo>> getGcProfitCfg(Long id) {
        IotAssert.isNotNull(id, "请提供配置ID");
        return profitFeignClient.getGcProfitCfg(id);
    }
}
