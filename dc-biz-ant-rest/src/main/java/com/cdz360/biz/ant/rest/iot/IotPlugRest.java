package com.cdz360.biz.ant.rest.iot;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.service.iot.IotPlugService;
import com.cdz360.biz.ant.service.sysLog.SiteSysLogService;
import com.cdz360.biz.model.iot.param.ListPlugParam;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@Tag(name = "枪头相关接口", description = "枪头")
public class IotPlugRest {

    @Autowired
    private IotPlugService iotPlugService;
    @Autowired
    private SiteSysLogService siteSysLogService;


    @Operation(summary = "获取枪头列表")
    @PostMapping("/api/plug/getPlugListByEvseNo")
    public ListResponse<PlugVo> getPlugListByEvseNo(ServerHttpRequest request,
                                                    @RequestParam String evseNo) {
        log.info(LoggerHelper2.formatEnterLog(request));
        if (StringUtils.isBlank(evseNo)) {
            throw new DcArgumentException("参数错误,桩号不能为空");
        }
        ListPlugParam param = new ListPlugParam();
        param.setEvseNoList(List.of(evseNo));
        ListResponse<PlugVo> res = iotPlugService.getPlugList(param);
        return res;
    }

    @Operation(summary = "修改枪头信息")
    @PostMapping("/api/plug/updatePlugInfo")
    public BaseResponse updatePlugInfo(ServerHttpRequest request,
                                       @RequestParam String evseNo,
                                       @RequestParam Integer plugIdx,
                                       @RequestParam String name) {
        log.info(LoggerHelper2.formatEnterLog(request));
        if (StringUtils.isBlank(evseNo)) {
            throw new DcArgumentException("参数错误,桩号不能为空");
        } else if (plugIdx == null) {
            throw new DcArgumentException("参数错误,枪头序号不能为空");
        }
        this.iotPlugService.updatePlugInfo(evseNo, plugIdx, name);
        siteSysLogService.updatePlugInfoLog(evseNo, request);
        return RestUtils.success();
    }

}
