package com.cdz360.biz.ant.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 站点表 t_ite
 * <AUTHOR>
 */
@Data
public class Site implements Serializable {

	/** 站点编号 */
	private Long id;
	private String idNo;
	/** 是否是采集数据,0否，1是**/
	private int collectData;
	/** 站点名称 */
	private String name;
	/** 站点类型(0:全部,1:公共,2:个人,3:营运) */
	private Integer type;
	/** 经度 */
	private Double longitude;
	/** 纬度 */
	private Double latitude;
	/** 站点地址 */
	private String address;
	/** 省 */
	private String province;
	/** 市 */
	private String city;
	/** 区 */
	private String area;
	/** 服务号码 */
	private String phone;
	/** 工作日服务时间 */
	private String serviceWorkdayTime;
	/** 使用范围 1:对外开放 2：内部使用 */
	private Integer scope;
	/** 停车是否收费（1：未知 2：收费 3：免费） */
	private Integer park;
	/** 停车费 */
	private String parkFee;
	/** 图片地址，多个用逗号分隔 */
	private String images;
	/** 小图的图片地址 */
	private String scaleImgs;
	/** 联网状态(0:全部, 1: 已联网, 2:未联网) */
	private Integer networkStatus;
	/** 节假日服务时间 */
	private String serviceHolidayTime;
	/** 特定用户(1: 只针对特定用户开放, 2: 对所有用户开放) */
	private Integer specilic;
	/** 支持扫码(1: 支持. 2: 不支持) */
	private Integer qrcode;
	/** 服务号码转 */
	private String throug;
	/** 站点状态(0、已删除， 1、待上线  2：已上线  3、已隐藏 4：维护中 */
	private Integer status;
	/** 备注 **/
	private String remark;
	/** 运营商名称 **/
	private String operateName;
	/**
	 * 保存经纬度对应的geohash,用于计算附近的站点
	 */
	private String geohash;

	@Schema(title = "时区", description = "如: +8")
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private String timeZone;

	/** 运营商ID **/
	private Long operateId;

	private Date createTime;

	private Date updateTime;
	/**
	 * 收费说明
	 */
	private String feeDescription;
	/**
	 * 收费范围（最低）
	 */
    private long feeMin;
	/**
	 * 收费范围（最高）
	 */
    private long feeMax;
	/**
	 app支付(1001:充电网APP,1002:奥能APP)
	 */
	private String appPay;
	/**
	 微信支付(2001:充电网微信公众号  2002：奥能微信公众号  2003： e充网公众账号 )
	 */
	private String wxPay;
	/**
	 刷卡支付(3001:上海地铁公交卡 3002： 普天充电卡 3003：充电网充电卡  3004：招行闪付卡)
	 */
	private String cardPay;
	/**
	 现金支付（1:支持  0：不支持）
	 */
	private Integer cashPay;
	/**最大功率 **/
	private double maxPower;
	/**
	 * 是否需要预约
	 */
	private Integer appoint;
	/**
	 * 公告，已作废
	 */
	private String notice;
	/**
	 * 是否加入互联互通
	 */
	private boolean joinUnion;
	/** 该站点互联互通的id **/
	private String openSiteId;
    /**
     * 联系人姓名
     **/
    private String contacts;
    /**
     * 联系人电话
     **/
    private String contactsPhone;
    /**
     * 支付方式描述
     **/
    private String payDescription;
    /**
     * 是否为app分享站点 ，默认0-否;1-是
     **/
    private Integer isShareSite;
    /**
     * 子账号主键
     **/
    private Long merchantId;
//	/** 统一补贴金额(固定补贴) */
//    private long unifiedSubsidyAmount;
//	/** 补贴时长(单位：分) */
//	private  String subsidyDuration;
//	/** '补贴单价(单位：分) */
//    private long subsidyUnitPrice;
//	/** (0:禁用 默认  1:启用) */
//	private  Integer subsidyStatus;
//	/** 补贴类型(1:固定补贴 ) */
//	private  Integer subsidyType ;
//	/**  补贴是否分润(0:不参与分润  1:参与分润 ) */
//	private  Integer subsidyShareProfitStatus ;
    /**
     * 预计上线时间
     */
    private Date onlineDate;

}


