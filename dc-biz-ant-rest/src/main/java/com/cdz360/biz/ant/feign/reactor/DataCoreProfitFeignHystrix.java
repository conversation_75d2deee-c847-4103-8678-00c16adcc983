package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.trading.profit.sett.param.ListProfitCfgParam;
import com.cdz360.biz.model.trading.profit.sett.param.SaveProfitCfgParam;
import com.cdz360.biz.model.trading.profit.sett.vo.ProfitCfgVo;
import lombok.extern.slf4j.Slf4j;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
public class DataCoreProfitFeignHystrix
    implements FallbackFactory<DataCoreProfitFeignClient> {

    @Override
    public DataCoreProfitFeignClient apply(Throwable throwable) {
        log.error("{}", throwable.getMessage(), throwable);
        return new DataCoreProfitFeignClient() {

            @Override
            public Mono<ObjectResponse<ProfitCfgVo>> saveGcProfitCfg(SaveProfitCfgParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = saveGcProfitCfg (更新收益配置), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<ProfitCfgVo>> enableGcProfitCfg(Long id, Boolean enable) {
                log.error(
                    "【服务熔断】: Service = {}, api = enableGcProfitCfg (收益配置停用或启用), id = {}, enable = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, id, enable);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<ProfitCfgVo>> deleteGcProfitCfg(Long id) {
                log.error(
                    "【服务熔断】: Service = {}, api = deleteGcProfitCfg (删除收益配置(物理删除)), id = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, id);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<ProfitCfgVo>> findGcProfitCfg(ListProfitCfgParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = findGcProfitCfg (获取收益配置列表), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<ProfitCfgVo>> getGcProfitCfg(Long id) {
                log.error(
                    "【服务熔断】: Service = {}, api = getGcProfitCfg (获取收益配置(无效ID返回data为null)), id = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, id);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }
        };
    }

}
