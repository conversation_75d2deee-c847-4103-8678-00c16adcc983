package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.type.OrderType;
import com.cdz360.biz.ds.cus.ro.balance.param.BalanceApplicationParam;
import com.cdz360.biz.model.bi.dashboard.ChargeOrderCommBiVo;
import com.cdz360.biz.model.bi.dashboard.CommStatisticBiVo;
import com.cdz360.biz.model.bi.dashboard.SiteUtilizationTopVo;
import com.cdz360.biz.model.bi.dashboard.SubCommStatisticBiVo;
import com.cdz360.biz.model.bi.site.ChargeFee;
import com.cdz360.biz.model.bi.site.ExcelBiPosition;
import com.cdz360.biz.model.bi.site.MeterReadingBi;
import com.cdz360.biz.model.bi.site.OrderCount;
import com.cdz360.biz.model.bi.site.OrderElecDivision;
import com.cdz360.biz.model.bi.site.PlugErrorCount;
import com.cdz360.biz.model.bi.site.PlugUtilization;
import com.cdz360.biz.model.bi.site.SiteErrorCount;
import com.cdz360.biz.model.bi.site.SiteUtilization;
import com.cdz360.biz.model.bi.site.UserOrderCount;
import com.cdz360.biz.model.bi.site.UserOrderElec;
import com.cdz360.biz.model.bi.site.UserOrderFee;
import com.cdz360.biz.model.bi.type.OrderByType;
import com.cdz360.biz.model.cus.vin.param.VinSearchParam;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.iot.param.MeterRecordBiParam;
import com.cdz360.biz.model.iot.param.SiteBiParam;
import com.cdz360.biz.model.iot.param.SiteBiTopParam;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.bill.param.ListZftThirdOrderParam;
import com.cdz360.biz.model.trading.comm.dto.CommSiteEvseCount;
import com.cdz360.biz.model.trading.cus.vo.CusOrderBiVo;
import com.cdz360.biz.model.download.param.DownloadFileParam;
import com.cdz360.biz.model.trading.meter.vo.MeterReadingTopVo;
import com.cdz360.biz.model.trading.order.dto.GeoOrderBiDto;
import com.cdz360.biz.model.trading.order.dto.LowKwOrderDto;
import com.cdz360.biz.model.trading.order.dto.OrderStartTypeBiDto;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.model.trading.order.param.ZftBillParam;
import com.cdz360.biz.model.trading.order.vo.ChargeOrderBiVo;
import com.cdz360.biz.model.trading.site.dto.CitySiteNumDto;
import com.cdz360.biz.model.trading.site.dto.ProvinceSiteNumDto;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.po.BiSiteSummaryPo;
import com.cdz360.biz.model.trading.site.po.SiteDailyBiPo;
import com.cdz360.biz.model.trading.site.vo.CorpOrderCountVo;
import com.cdz360.biz.model.trading.site.vo.SiteOrderAccountData;
import com.cdz360.biz.model.trading.site.vo.SiteOrderBiVo;
import com.cdz360.biz.model.trading.site.vo.TimePowerBiVo;
import com.chargerlinkcar.framework.common.domain.BillExportParam;
import com.chargerlinkcar.framework.common.domain.CorpListPointLogParam;
import com.chargerlinkcar.framework.common.domain.OrderCountParam;
import com.chargerlinkcar.framework.common.domain.param.CardSearchParam;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.domain.param.CorpCardRequest;
import com.chargerlinkcar.framework.common.domain.type.DashboardOrderType;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDataVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDetailVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.VinParam;
import feign.Response;
import io.micrometer.observation.annotation.Observed;
import java.util.Date;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * ost feign
 *
 * <AUTHOR>
 */
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_BI, fallbackFactory = BizBiHystrixFactory.class)
//@Observed(name = "BizBiFeignClient")
public interface BizBiFeignClient {

    /**
     * 用car-trading中getChargerOrderData方法返回字段chargerOrderNumber代替
     *
     * @param searchParam
     * @return
     */
//    @Deprecated
//    @PostMapping("/api/order/checkExportCountOver")
//    ObjectResponse<Boolean> checkExportCountOver(@RequestBody ChargerOrderParam searchParam);
    @Deprecated
    //异步生成excel
    @PostMapping("/api/order/writeTempExcelByChargeOrderList")
    ObjectResponse<ExcelPosition> writeTempExcelByChargeOrderList(
        @RequestBody ChargerOrderParam searchParam);

    @Deprecated
    @PostMapping("/api/order/exportCommUserOrderList")
    ObjectResponse<ExcelPosition> exportCommUserOrderList(
        @RequestBody ChargerOrderParam searchParam);

    @PostMapping("/api/order/exportVinOrderList")
    ObjectResponse<ExcelPosition> exportVinOrderList(@RequestBody ChargerOrderParam searchParam);

    @Deprecated
    @RequestMapping(value = "/api/card/exportCardForManage", method = RequestMethod.POST)
    ObjectResponse<ExcelPosition> exportOnlineCardList(@RequestParam("token") String token,
        @RequestBody CardSearchParam param);

    @PostMapping("/api/card/exportCardForCorp")
    ObjectResponse<ExcelPosition> exportCardForCorp(@RequestBody CorpCardRequest param);

    @Deprecated
    @PostMapping("/api/vin/exportVinForManage")
    ObjectResponse<ExcelPosition> exportVinForManage(@RequestBody VinSearchParam param);

    @PostMapping("/api/vin/exportVinForCorp")
    ObjectResponse<ExcelPosition> exportVinForCorp(@RequestBody VinParam param);

    /**
     * 异步生成excel,按照商户汇总
     *
     * @param searchParam
     * @return
     */
    @Deprecated
    @PostMapping("/bi/corp/exportBiCorpList")
    ObjectResponse<ExcelBiPosition> exportBiCorpList(@RequestBody SiteBiParam searchParam);

    /**
     * 异步生成excel,按照场站汇总
     *
     * @param searchParam
     * @return
     */
    @Deprecated
    @PostMapping("/bi/site/exportBiSiteList")
    ObjectResponse<ExcelBiPosition> exportBiSiteList(@RequestBody SiteBiParam searchParam);

    /**
     * 按照场站导出消费情况
     *
     * @param searchParam
     * @return
     */
    @Deprecated
    @PostMapping("/bi/site/exportBiFeeBySiteList")
    ObjectResponse<ExcelBiPosition> exportBiFeeBySiteList(@RequestBody SiteBiParam searchParam);

    /**
     * 按照商户导出消费汇总
     *
     * @param searchParam
     * @return
     */
    @Deprecated
    @PostMapping("/bi/corp/exportBiFeeByCorpList")
    ObjectResponse<ExcelBiPosition> exportBiFeeByCorpList(@RequestBody SiteBiParam searchParam);

    /**
     * 按照场站汇总导出尖峰平谷电量汇总
     *
     * @param searchParam
     * @return
     */
    @PostMapping("/bi/site/exportBiElectBySiteList")
    ObjectResponse<ExcelBiPosition> exportBiElectBySiteList(@RequestBody SiteBiParam searchParam);


    /**
     * 获取场站日充电功率曲线
     */
    @GetMapping(value = "/bi/site/getSiteDailyPowerLine")
    ObjectResponse<SiteDailyBiPo> getSiteDailyPowerLine(
        @RequestParam("siteId") String siteId,
        @RequestParam("date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date);

    /**
     * 获取场站日充电功率曲线
     */
    @GetMapping(value = "/bi/site/getSiteDailyPowerRangeDate")
    ListResponse<SiteDailyBiPo> getSiteDailyPowerRangeDate(
        @RequestParam("siteId") String siteId,
        @RequestParam("fromDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date fromDate,
        @RequestParam("toDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date toDate);

    /**
     * 按照商户汇总导出尖峰平谷电量情况
     *
     * @param searchParam
     * @return
     */
    @Deprecated
    @PostMapping("/bi/corp/exportBiElectByCorpList")
    ObjectResponse<ExcelBiPosition> exportBiElectByCorpList(@RequestBody SiteBiParam searchParam);

    //异步生成excel
    @PostMapping("/api/corp/exportExcelTrade")
    ObjectResponse<ExcelPosition> exportExcelTrade(@RequestBody CorpListPointLogParam searchParam);

    //检查excel是否生成好了
    @RequestMapping(value = "/api/order/checkExcelFileCompeleted")
    ObjectResponse<Boolean> checkExcelFileCompeleted(@RequestParam(value = "type") String type,
        @RequestParam(value = "subDir") String subDir,
        @RequestParam(value = "subFileName") String subFileName);

    //下载excel文件
    @RequestMapping(value = "/api/order/download", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Response download(@RequestParam(value = "type") String type,
        @RequestParam(value = "subDir") String subDir,
        @RequestParam(value = "subFileName") String subFileName);

    /**
     * 充值记录excel导出
     *
     * @param payBillParam
     * @return
     */
    @Deprecated
    @PostMapping("/api/paybill/exportExcel")
    ObjectResponse<ExcelPosition> payBillExportExcel(@RequestBody PayBillParam payBillParam);

    /**
     * 充值记录excel导出(运营管理导出使用)
     *
     * @param payBillParam
     * @return
     */
    @Deprecated
    @PostMapping("/api/paybill/exportExcelManager")
    ObjectResponse<ExcelPosition> payBillExportExcelManager(@RequestBody PayBillParam payBillParam);

    /**
     * 直付通列表导出
     *
     * @param payBillParam
     * @return
     */
    @Deprecated
    @PostMapping("/api/paybill/exportZftList")
    ObjectResponse<ExcelPosition> zftBillExportExcelManager(@RequestBody ZftBillParam payBillParam);

    /**
     * 对账管理对账单列表下载
     *
     * @param param
     * @return
     */
    @Deprecated
    @PostMapping("/api/paybill/exportZftThirdOrderList")
    ObjectResponse<ExcelPosition> exportZftThirdOrderList(
        @RequestBody ListZftThirdOrderParam param);


    @PostMapping(value = "/bi/order/orderCountBi")
    ListResponse<OrderCount> orderCountBi(@RequestBody SiteBiParam siteBiParam);

    @PostMapping(value = "/bi/site/getSummaryListBySite")
    ObjectResponse<BiSiteSummaryPo> getSummaryListBySite(@RequestBody SiteBiParam siteBiParam);

    @PostMapping(value = "/bi/site/getBiSiteList")
    ListResponse<BiSiteSummaryPo> getBiSiteList(@RequestBody SiteBiParam siteBiParam);

    @PostMapping(value = "/bi/corp/getBiCorpList")
    ListResponse<BiSiteSummaryPo> getBiCorpList(@RequestBody SiteBiParam siteBiParam);

    @PostMapping(value = "/bi/order/userOrderCountBi")
    ListResponse<UserOrderCount> userOrderCountBi(@RequestBody SiteBiTopParam siteBiTopParam);

    @PostMapping(value = "/bi/order/chargeFeeBi")
    ListResponse<ChargeFee> chargeFeeBi(@RequestBody SiteBiParam siteBiParam);

    @PostMapping(value = "/bi/order/userChargeFeeBi")
    ListResponse<UserOrderFee> userChargeFeeBi(@RequestBody SiteBiTopParam siteBiTopParam);

    @PostMapping(value = "/bi/order/chargeDivisionBi")
    ListResponse<OrderElecDivision> chargeDivisionBi(@RequestBody SiteBiParam param);

    @PostMapping(value = "/bi/order/userChargeDivisionBi")
    ListResponse<UserOrderElec> userChargeDivisionBi(@RequestBody SiteBiTopParam siteBiTopParam);

    @PostMapping(value = "/bi/order/utilizationBi")
    ListResponse<SiteUtilization> utilizationBi(SiteBiParam siteBiParam);

    @PostMapping(value = "/bi/order/plugUtilizationBi")
    ListResponse<PlugUtilization> plugUtilizationBi(SiteBiTopParam siteBiTopParam);

    @PostMapping(value = "/bi/order/breakdownBi")
    ListResponse<SiteErrorCount> breakdownBi(@RequestBody SiteBiParam param);

    @PostMapping(value = "/bi/order/plugBreakdownBi")
    ListResponse<PlugErrorCount> plugBreakdownBi(@RequestBody SiteBiTopParam param);

    /**
     * 获取历史(总)充电统计数据
     *
     * @param commIdChain
     * @return
     */
    @Deprecated(since = "请使用reactor方式")
    @PostMapping("/bi/order/getOrderBi")
    ObjectResponse<ChargeOrderBiVo> getOrderBi(
        @RequestParam(value = "commIdChain", required = false) String commIdChain,
        @RequestParam(value = "siteId", required = false) String siteId,
        @RequestParam(value = "bizTypeList", required = false) List<Integer> bizTypeList);


    /**
     * 查询最近几天(自然日)的时间汇总功率统计
     *
     * @param commIdChain
     * @param lastDays    1,查今天; 2,查昨天开始; 3,查前天开始
     * @return
     */
    @GetMapping("/bi/order/getTimePowerBiList")
    ListResponse<TimePowerBiVo> getTimePowerBiList(
        @RequestParam(value = "commIdChain", required = false) String commIdChain,
        @RequestParam(value = "siteId", required = false) String siteId,
        @RequestParam(value = "lastDays") int lastDays);

    /**
     * 获取过去7天的充电启动方式统计
     *
     * @param topCommId
     * @param commIdChain
     * @return
     */
    @PostMapping("/bi/order/getOrderStartTypeBiList7")
    ListResponse<OrderStartTypeBiDto> getOrderStartTypeBiList7(
        @RequestParam(value = "topCommId", required = false) Long topCommId,
        @RequestParam(value = "commIdChain", required = false) String commIdChain);

    /**
     * 过去7天的活跃用户数统计
     *
     * @param topCommId
     * @param commIdChain
     * @return
     */
    @PostMapping("/bi/order/getActiveCusCount7")
    ObjectResponse<Long> getActiveCusCount7(
        @RequestParam(value = "topCommId", required = false) Long topCommId,
        @RequestParam(value = "commIdChain", required = false) String commIdChain);


    /**
     * 按时间分组统计*
     */
    @PostMapping("/bi/order/getTimeGroupingOrderBiList")
    ListResponse<SiteOrderBiVo> getTimeGroupingOrderBiList(
        @RequestParam(value = "timeType", required = false) SiteBiSampleType timeType,
        @RequestParam(value = "siteId", required = false) String siteId,
        @RequestParam(value = "commIdChain", required = false) String commIdChain);


    /**
     * 按日期分组统计枪头使用量数据*
     */
    @PostMapping("/bi/order/getDateGroupingPlugUsageBiList")
    ListResponse<SiteUtilization> getDateGroupingPlugUsageBiList(
        @RequestParam(value = "days") int days,
        @RequestParam(value = "commIdChain", required = false) String commIdChain);

    /**
     * 按客户类型统计7/30日充电订单
     *
     * @param commIdChain
     * @return
     */
    @PostMapping("/bi/order/getAccTypeGroupingFeeBiList")
    ObjectResponse<CusOrderBiVo> getAccTypeGroupingFeeBiList(
        @RequestParam(value = "commIdChain", required = false) String commIdChain);


    /**
     * 统计城市的今天,本周,本月,今年充电数据
     *
     * @param cityCode
     * @param commIdChain
     * @return
     */
    @PostMapping("/bi/order/getGeoOrderBi")
    ObjectResponse<GeoOrderBiDto> getGeoOrderBi(
        @RequestParam(value = "provinceCode", required = false) String provinceCode,
        @RequestParam(value = "cityCode", required = false) String cityCode,
        @RequestParam(value = "siteId", required = false) String siteId,
        @RequestParam(value = "commIdChain", required = false) String commIdChain);

    // 近n天充电用户统计(按上传时间统计，不含当天)
    @GetMapping(value = "/bi/order/orderAccountBi")
    ListResponse<SiteOrderAccountData> getOrderAccountBi(@RequestParam(value = "days") Integer days,
        @RequestParam(value = "siteId", required = false) String siteId);

    /**
     * 按场站分组统计一段时间内的电量,收入,订单数
     *
     * @param param
     * @return
     */
    @PostMapping("/bi/site/getSiteBiList")
    ListResponse<SiteOrderBiVo> getSiteBiList(@RequestBody SiteBiParam param);

    /**
     * 按省统计场站数量
     */
    @PostMapping("/bi/site/getProvinceSiteNumList")
    ListResponse<ProvinceSiteNumDto> getProvinceSiteNumList(@RequestBody ListSiteParam param
//            @RequestParam(value = "includedHlht", required = false) Boolean includedHlht,
//                                                            @RequestParam(value = "commIdChain", required = false) String commIdChain
    );

    /**
     * 按城市统计场站数量
     */
    @PostMapping("/bi/site/getCitySiteNumList")
    ListResponse<CitySiteNumDto> getCitySiteNumList(@RequestBody ListSiteParam param
//            @RequestParam(value = "includedHlht", required = false) Boolean includedHlht,
//                                                    @RequestParam(value = "commIdChain", required = false) String commIdChain
    );


    /**
     * 统计商户下场站数量，桩/枪数量
     */
    @PostMapping("/bi/site/getCommSiteEvseCount")
    ObjectResponse<CommSiteEvseCount> getCommSiteEvseCount(@RequestBody ListSiteParam param
//            @RequestParam(value = "includedHlht", required = false) Boolean includedHlht,
//                                                           @RequestParam(value = "commIdChain", required = false) String commIdChain
    );

    /**
     * 导出脱机桩列表
     *
     * @param param
     * @return
     */
    @PostMapping("/bi/site/exportOfflineEvse")
    ObjectResponse<ExcelPosition> exportOfflineEvse(@RequestBody ListEvseParam param);

    /**
     * 数据统计和快速入口-近30天充电订单统计
     *
     * @param days
     * @param idChain
     * @return
     */
    @GetMapping("/api/biDashboard/getLastBi")
    ObjectResponse<ChargeOrderCommBiVo> getLastBi(
        @RequestParam(value = "days", required = false) Integer days,
        @RequestParam("commId") Long commId,
        @RequestParam("idChain") String idChain);

    /**
     * 数据统计和快速入口-近 30 天营收数据
     *
     * @param days
     * @return
     */
    @GetMapping("/api/biDashboard/getLastCommBi")
    ListResponse<CommStatisticBiVo> getLastCommBi(
        @RequestParam(value = "days", required = false) Integer days,
        @RequestParam(value = "commId", required = false) Long commId);

    /**
     * 数据统计和快速入口-近 30 天营收数据对比
     *
     * @param days //     * @param idChain
     * @return
     */
    @GetMapping("/api/biDashboard/getLastCommTopBi")
    ListResponse<SubCommStatisticBiVo> getLastCommTopBi(
        @RequestParam(value = "days", required = false) Integer days,
        @RequestParam("commId") Long commId,
//                                                        @RequestParam("idChain") String idChain,
        @RequestParam("sort") DashboardOrderType orderBy);

    /**
     * 昨日充电站利用率排名
     *
     * @param size
     * @param commId
     * @param orderBy
     * @return
     */
    @GetMapping("/api/biDashboard/getUsageRateBoard")
    ListResponse<SiteUtilizationTopVo> getUsageRateBoard(@RequestParam("size") Integer size,
        @RequestParam(value = "commId") Long commId,
        @RequestParam("orderByType") OrderByType orderByType,
        @RequestParam("orderBy") OrderType orderBy);

    /**
     * 导出账单
     *
     * @param billNo
     * @return
     */
    @Deprecated(since = "20220318")
    @PostMapping("/api/order/exportSettlementByBillNo")
    ObjectResponse<ExcelPosition> exportSettlementByBillNo(@RequestParam("billNo") String billNo);


    @PostMapping(value = "/bi/meter/meterReadingBi")
    ListResponse<MeterReadingBi> meterReadingBi(@RequestBody SiteBiParam siteBiTopParam);

    @Deprecated
    @PostMapping(value = "/bi/meter/exportBiSiteMeterRecord")
    ObjectResponse<ExcelBiPosition> exportBiSiteMeterRecord(@RequestBody MeterRecordBiParam param);

    @PostMapping(value = "/bi/meter/meterReadingTopBi")
    ListResponse<MeterReadingTopVo> meterReadingTopBi(@RequestBody SiteBiParam siteBiTopParam);

    @RequestMapping(value = "/bi/pdf/checkFileCompeleted")
    ObjectResponse<Boolean> checkFileCompeleted(@RequestParam(value = "subDir") String subDir,
        @RequestParam(value = "subFileName") String subFileName);

    @RequestMapping(value = "/bi/pdf/download", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    Response download(@RequestParam(value = "subDir") String subDir,
        @RequestParam(value = "subFileName") String subFileName);

    @PostMapping(value = "/bi/download/downloadFile")
    Response downloadFile(@RequestBody DownloadFileParam param);

    /**
     * 导出巡检详情
     *
     * @param id
     * @return
     */
    @Deprecated
    @GetMapping(value = "/bi/pdf/exportSiteInspection")
    ObjectResponse<ExcelPosition> exportSiteInspection(@RequestParam("id") Long id);

    /**
     * 订单列表
     *
     * @param searchParam 查询条件列表对象
     * @return
     */
    @RequestMapping(value = "/bi/order/queryChargeOrderList", method = RequestMethod.POST)
    ListResponse<ChargerOrderVo> queryChargeOrderList(@RequestBody ChargerOrderParam searchParam);

    /**
     * 根据条件查询订单统计数据
     *
     * @param searchParam
     * @return
     */
    @RequestMapping(value = "/bi/order/getChargerOrderData", method = RequestMethod.POST)
    ObjectResponse<ChargerOrderDataVo> getChargerOrderData(
        @RequestBody ChargerOrderParam searchParam);

    /**
     * 根据条件查询订单统计数据(尖峰平谷)
     *
     * @param searchParam
     * @return
     */
    @RequestMapping(value = "/bi/order/getChargerOrderDetail", method = RequestMethod.POST)
    ObjectResponse<ChargerOrderDetailVo> getChargerOrderDetail(
        @RequestBody ChargerOrderParam searchParam);

    @Deprecated
    @PostMapping("/bi/balanceApplication/exportExcel")
    ObjectResponse<ExcelPosition> exportBalanceApplicationExcel(
        @RequestBody BalanceApplicationParam balanceApplicationParam);

    /**
     * 历史订单金额，待支付订单数目
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/bi/order/corpOrderCount")
    ListResponse<CorpOrderCountVo> corpOrderCount(@RequestBody OrderCountParam param);

    @PostMapping(value = "/api/corp/exportBillExcel")
    ObjectResponse<BillExportParam> exportBillExcel(@RequestBody BillExportParam params);

    @PostMapping(value = "/bi/order/getLowKwOrderList")
    ListResponse<LowKwOrderDto> getLowKwOrderList(@RequestBody ListChargeOrderParam param);
}
