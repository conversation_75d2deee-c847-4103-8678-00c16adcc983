package com.cdz360.biz.ant.rest.oa;

import static com.cdz360.biz.model.oa.constant.OaConstants.PD_KEY_PREPAID_ORDER_INVOICING;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.InvoiceFeignClient;
import com.cdz360.biz.ant.feign.reactor.DataCoreInvoiceFeignClient;
import com.cdz360.biz.ant.feign.reactor.OaFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorDataCoreOrderFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorInvoiceFeignClient;
import com.cdz360.biz.ant.rest.InvoiceProcesser;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.cus.corp.vo.CorpSimpleVo;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import com.cdz360.biz.model.oa.param.PrepaidInvoicingEditParam;
import com.cdz360.biz.model.oa.param.PrepaidInvoicingParam;
import com.cdz360.biz.model.oa.vo.BillInvoiceVo;
import com.cdz360.biz.model.oa.vo.OaInvoicedVo;
import com.cdz360.biz.model.trading.invoice.po.CorpInvoiceRecordPo;
import com.cdz360.biz.model.trading.order.param.BillInvoiceVoParam;
import com.cdz360.biz.model.trading.order.param.PrepaidOrderListParam;
import com.cdz360.biz.model.trading.order.param.RewriteInterimParam;
import com.cdz360.biz.model.trading.order.vo.PrepaidOperationVo;
import com.cdz360.biz.oa.param.PrepaidInvoiceProcessParam;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoicedRecordDTO;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDataVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.OptionalUtils;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * 预付订单开票流程相关接口
 */
@Slf4j
@RestController
@RequestMapping("/oa/prepaidInvoicing")
public class OaPrepaidInvoicingRest {

    @Autowired
    private OaFeignClient oaFeignClient;
    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;
    @Autowired
    private ReactorDataCoreOrderFeignClient reactorDataCoreOrderFeignClient;
    @Autowired
    private InvoiceFeignClient invoiceFeignClient;
    @Autowired
    private ReactorInvoiceFeignClient reactorInvoiceFeignClient;
    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;
    @Autowired
    private DataCoreInvoiceFeignClient dataCoreInvoiceFeignClient;
    @Autowired
    private InvoiceProcesser invoiceProcesser;

    @Operation(summary = "根据PageType获取预付订单")
    @PostMapping(value = "/getPrepaidOrderList")
    public Mono<ListResponse<ChargerOrderVo>> getPrepaidOrderList(
        @RequestBody PrepaidOrderListParam param) {
        log.info("getPrepaidOrderList. param: {}", param);
        return reactorDataCoreOrderFeignClient.getPrepaidOrderList(param);
    }

    @Operation(summary = "根据PageType获取预付订单总数")
    @PostMapping(value = "/getPrepaidOrderListCount")
    public Mono<ObjectResponse<Long>> getPrepaidOrderListCount(
        @RequestBody PrepaidOrderListParam param) {
        log.info("getPrepaidOrderListCount. param: {}", param);
        return reactorDataCoreOrderFeignClient.getPrepaidOrderListCount(param);
    }

    @Operation(summary = "预付订单操作")
    @PostMapping(value = "/orderOperation")
    public Mono<ObjectResponse<String>> appendOrder(@RequestBody PrepaidOrderListParam param) {
        log.info("prepaidOrderOperation. param: {}", param);
        return reactorDataCoreOrderFeignClient.prepaidOrderOperation(param);
    }

    @Operation(summary = "获取全部能开票的预付订单号")
    @PostMapping(value = "/getAllPrepaidOrderNos")
    public Mono<ObjectResponse<PrepaidOperationVo>> getAllPrepaidOrderNos(
        @RequestBody PrepaidOrderListParam param) {
        log.info("getAllPrepaidOrderNos. param: {}", param);
        return reactorDataCoreOrderFeignClient.getAllPrepaidOrderNos(param);
    }

    @Operation(summary = "获取预付订单流程详情页的订单详细数据")
    @PostMapping(value = "/queryProcessOrderDetailList")
    public Mono<ListResponse<ChargerOrderVo>> queryProcessOrderDetailList(
        @RequestBody PrepaidOrderListParam param) {
        log.debug("queryProcessOrderDetailList. param: {}", param);
        return reactorDataCoreOrderFeignClient.queryPrePaidOrderDetailList(param);
    }

    @Operation(summary = "获取预付订单的统计信息")
    @PostMapping(value = "/getOrderDataVo")
    public Mono<ObjectResponse<ChargerOrderDataVo>> getOrderDataVo(
        @RequestBody PrepaidOrderListParam param) {
        log.info("getOrderDataVo. param: {}", param);
        return reactorDataCoreOrderFeignClient.getPrepaidOrderDataVo(param);
    }

    @Operation(summary = "预付订单开票流程提交")
    @PostMapping("/startProcess")
    public Mono<ObjectResponse<String>> startChargeFeeProcess(
        ServerHttpRequest request, @RequestBody PrepaidInvoicingParam param) {
        log.info("预付订单开票流程提交. param: {}", param);
        param.checkAndFilterField();
        param.setOpName(AntRestUtils.getSysUserName(request));

        // STEP0.检查入参订单是否可开票
        PrepaidOrderListParam checkOrderReq = new PrepaidOrderListParam();
        checkOrderReq.setAccountType(param.getAccountType())
            .setPayAccountId(param.getCommId())
            .setUserId(param.getUserId())
            .setCorpId(param.getCorpId())
            .setInterimCode(param.getInterimCode())
            .setOrderNoList(param.getOrderNoList());
        BaseResponse response = dataCoreFeignClient.checkWhetherThePrepaidOrderCanBeInvoiced(
            checkOrderReq);
        FeignResponseValidate.check(response);

        ObjectResponse<OaInvoicedVo> objResponse = invoiceFeignClient.templateSalCheckBeforeInvoicingProcess(
            param.getInvoiceTitleId(), param.getTempSalId(), param.getProductTempId());
        FeignResponseValidate.check(objResponse);
        param.setInvoiceTitle(objResponse.getData().getName());
        param.setSaleName(objResponse.getData().getSaleName());

        ListResponse<BillInvoiceVo> voListResponse = dataCoreFeignClient.getBillInvoiceVoList(
            new BillInvoiceVoParam(param.getInterimCode(), param.getOrderNoList(), null));
        FeignResponseValidate.check(voListResponse);
        final List<BillInvoiceVo> billInvoiceVoList = voListResponse.getData();

        ObjectResponse<CommercialSimpleVo> resp = authCenterFeignClient.findSimpleVoById(
            param.getCommId());
        if (resp == null || resp.getData() == null) {
            throw new DcServiceException("商户信息不存在");
        }
        param.setCommName(resp.getData().getCommName());

        Mono<BaseResponse> mono;
        // STEP1.根据账户类型做不同的处理
        switch (param.getAccountType()) {
            case PERSONAL:
            case COMMERCIAL:
                mono = this.personalOrCommercialInvoicingHandler(request, param);
                break;
            case CREDIT:
                mono = this.creditInvoicingHandler(request, param);
                break;
            default:
                throw new DcArgumentException("暂不支持的账户类型");
        }

        return mono
            .flatMap(e -> this.startPrepaidProcess(request, billInvoiceVoList, param)); // STEP2.流程创建
    }

    /**
     * 预付订单开票-个人账户或商户会员开票处理
     *
     * @param request
     * @param param
     * @return
     */
    private Mono<BaseResponse> personalOrCommercialInvoicingHandler(ServerHttpRequest request,
        PrepaidInvoicingParam param) {

        PrepaidInvoicingEditParam req = JsonUtils.fromJson(JsonUtils.toJsonString(param),
            PrepaidInvoicingEditParam.class);
        return dataCoreInvoiceFeignClient.prepaidInvoiceSubmit2Audit(req)
            .doOnNext(FeignResponseValidate::check)
            .doOnNext(e -> param.setInvoiceRecordId(e.getData()))
            .map(e -> RestUtils.success());
    }

    /**
     * 预付订单开票-企业客户开票处理
     *
     * @param request
     * @param param
     * @return
     */
    private Mono<BaseResponse> creditInvoicingHandler(ServerHttpRequest request,
        PrepaidInvoicingParam param) {

        ListResponse<CorpSimpleVo> response = authCenterFeignClient.getCorpByCommId(
            AntRestUtils.getToken2(request),
            AntRestUtils.getCommIdChain(request), param.getCorpId());
        if (response == null || CollectionUtils.isEmpty(response.getData())) {
            throw new DcServiceException("企客信息不存在");
        }
        param.setCorpName(response.getData().get(0).getCorpName());

        return invoiceProcesser.corpAppendOrderByOa(request, PD_KEY_PREPAID_ORDER_INVOICING,
                null, null,
                param.getCorpId(), param.getInterimCode(), null) // 模拟企客开票追加订单
            .doOnNext(recordDetail -> {
                param.setApplyNo(recordDetail.getApplyNo());
                // 异步执行
                invoiceProcesser.invoiceSubmit2Audit(request, recordDetail,
                        param) // 模拟企客开票记录提交到审核
                    .subscribe();
            })
            .map(e -> RestUtils.success());
    }

    /**
     * 预付订单开票-流程创建
     *
     * @param request
     * @param param
     * @return
     */
    private Mono<ObjectResponse<String>> startPrepaidProcess(ServerHttpRequest request,
        List<BillInvoiceVo> billInvoiceVoList,
        PrepaidInvoicingParam param) {

        param.setBillInvoiceVoList(billInvoiceVoList);

        PrepaidInvoiceProcessParam processParam = new PrepaidInvoiceProcessParam(
            AntRestUtils.getSysUid(request),
            AntRestUtils.getSysUserName(request),
            AntRestUtils.getSysUserPhone(request),
            AntRestUtils.getTopCommId(request),
            AntRestUtils.getCommIdChain(request),
            param);
        return this.oaFeignClient.startPrepaidProcess(processParam) // STEP3.预付订单开票流程创建
            .doOnNext(FeignResponseValidate::check)
            .doOnNext(e -> {
                OptionalUtils.ofBlankStrAble(param.getInterimCode())
                    .ifPresent(code -> {
                        RewriteInterimParam temp = new RewriteInterimParam();
                        temp.setInterimCode(code)
                            .setAccountType(param.getAccountType())
                            .setApplyNo(param.getApplyNo())
                            .setProcInstId(e.getData());
                        reactorDataCoreOrderFeignClient.rewriteInterimCodeByOa(temp)
                            .subscribe();// 异步执行
                    });
            })
            .flatMap(objectResponse -> {
                // STEP4.根据账户类型做不同的后续处理
                if (PayAccountType.CREDIT.equals(param.getAccountType())) {
                    return Mono.just(objectResponse)
                        .filter(e -> param.getApplyNo() != null)
                        .flatMap(resp -> {
                            CorpInvoiceRecordPo recordPo = new CorpInvoiceRecordPo();
                            recordPo.setApplyNo(param.getApplyNo())
                                .setProcInstId(resp.getData());
                            // STEP5.流程实例ID写入到企业开票记录
                            return dataCoreInvoiceFeignClient.updateCorpInvoiceRecord(recordPo)
                                .doOnNext(FeignResponseValidate::check)
                                .map(t -> resp);
                        })
                        .switchIfEmpty(Mono.just(objectResponse));
                } else {
                    return Mono.just(objectResponse)
                        .filter(e -> param.getInvoiceRecordId() != null)
                        .flatMap(resp -> {
                            InvoicedRecordDTO recordPo = new InvoicedRecordDTO();
                            recordPo.setId(param.getInvoiceRecordId())
                                .setProcInstId(resp.getData());
//                            // STEP5.流程实例ID写入到开票记录
                            return reactorInvoiceFeignClient.editRecordById(recordPo)
                                .doOnNext(FeignResponseValidate::check)
                                .map(e -> resp);
//                            reactorInvoiceFeignClient.editRecordById(recordPo)
//                                .subscribe(x -> log.info("流程实例ID写入到开票记录: {}",
//                                    JsonUtils.toJsonString(recordPo)));
                        })
                        .switchIfEmpty(Mono.just(objectResponse));
                }
            });
    }

    @Operation(summary = "获取页面需展示的开票信息")
    @GetMapping(value = "/getInvoiceVo")
    public Mono<ObjectResponse<OaInvoicedVo>> getInvoiceVo(
        @RequestParam(value = "procInstId") String procInstId) {
        log.info("getInvoiceVo. param: {}", procInstId);
        return dataCoreInvoiceFeignClient.getInvoiceVo4PrepaidProcess(procInstId);
    }

}
