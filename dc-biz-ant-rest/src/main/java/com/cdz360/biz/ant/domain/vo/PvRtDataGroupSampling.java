package com.cdz360.biz.ant.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "组串采样数据")
public class PvRtDataGroupSampling {

    @Schema(description = "采样点时间", example = "01:00")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String time;

    @Schema(description = "采样数据")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<PvRtDataGroupItem> items;

}
