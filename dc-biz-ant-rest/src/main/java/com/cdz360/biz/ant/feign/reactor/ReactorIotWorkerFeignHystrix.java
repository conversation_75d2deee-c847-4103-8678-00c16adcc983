package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.domain.bundle.EvseBundleDto;
import com.cdz360.biz.ant.domain.bundle.UpgradeParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

import java.util.function.Function;

/**
 *
 * @since 10/15/2021 1:49 PM
 * <AUTHOR>
 */
@Slf4j
@Component
public class ReactorIotWorkerFeignHystrix
        implements FallbackFactory<ReactorIotWorkerFeignClient> {
    @Override
    public ReactorIotWorkerFeignClient apply(Throwable throwable) {
        return new ReactorIotWorkerFeignClient() {

            @Override
            public Mono<ObjectResponse<String>> findMgcGwnoByKeyword(String keyword) {
                log.error("【服务熔断】 查询控制器编号. Service = {}, api = findMgcGwnoByKeyword. {}",
                        DcConstants.KEY_FEIGN_IOT_WORKER, keyword);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> mgcUpgrade(UpgradeParam param) {
                log.error("【服务熔断】 微网控制器升级. Service = {}, api = mgcUpgrade. {}",
                        DcConstants.KEY_FEIGN_IOT_WORKER, param);
                return Mono.just(RestUtils.serverBusy());
            }
        };
    }

    @Override
    public <V> Function<V, ReactorIotWorkerFeignClient> compose(Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_WORKER);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(Function<? super ReactorIotWorkerFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_WORKER);
        return null;
    }
}