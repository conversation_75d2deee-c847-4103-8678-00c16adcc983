package com.cdz360.biz.ant.service;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.domain.vo.AccountRemainInfo;
import com.cdz360.biz.ant.feign.BizBiFeignClient;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.model.cus.wallet.vo.RefundAnalyzeVo;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.bill.param.ListZftThirdOrderParam;
import com.cdz360.biz.model.trading.cus.param.CusPayBillListParam;
import com.cdz360.biz.model.trading.cus.vo.CusPayBillVo;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.cdz360.biz.model.trading.order.param.CzOrderPointParam;
import com.cdz360.biz.model.trading.order.param.ListPayBillParam;
import com.cdz360.biz.model.trading.order.param.PayAccount;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.model.trading.order.param.ZftBillParam;
import com.cdz360.biz.model.trading.order.po.PayBillPo;
import com.cdz360.biz.model.trading.order.type.PayBillStatus;
import com.cdz360.biz.model.trading.order.vo.*;
import com.cdz360.biz.model.wallet.vo.RefundReasonVo;
import com.cdz360.biz.utils.feign.user.UserFeignClient;
import com.chargerlinkcar.framework.common.constant.CheckTaxStatus;
import com.chargerlinkcar.framework.common.domain.PointRecDto;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import feign.Response;
import java.time.Duration;
import java.util.HashSet;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.buffer.DefaultDataBuffer;
import org.springframework.core.io.buffer.DefaultDataBufferFactory;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * PayBillServiceImpl
 *  TODO
 * @since 2019/11/5 10:42
 * <AUTHOR>
 */
@Slf4j
@Service
public class PayBillService //implements IPayBillService
{

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private BizBiFeignClient ostFeignClient;

    @Autowired
    private UserFeignClient userFeignClient;

    /**
     * 查询充值信息列表
     *
     * @param payBillParam
     * @return
     */
    public ListResponse<PayBillVo> orderList(PayBillParam payBillParam) {
        return dataCoreFeignClient.payBillList(payBillParam);
    }

    /**
     * 查询直付通信息列表
     *
     * @param payBillParam
     * @return
     */
    public ListResponse<ZftBillVo> zftOrderList(ZftBillParam payBillParam) {
        return dataCoreFeignClient.zftBillList(payBillParam);
    }

    public ObjectResponse<UserBillAccountNameVo> userBillAccountName(PayBillParam payBillParam) {
        return dataCoreFeignClient.userBillAccountName(payBillParam);
    }


    public ObjectResponse<ExcelPosition> exportExcel(PayBillParam payBillParam) {
        ObjectResponse<ExcelPosition> result = ostFeignClient.payBillExportExcel(payBillParam);
        log.info("excel 位置: {}", result);
        FeignResponseValidate.check(result);
        return result;
    }


    public ObjectResponse<ExcelPosition> exportExcelManager(PayBillParam payBillParam) {
        ObjectResponse<ExcelPosition> result = ostFeignClient.payBillExportExcelManager(
            payBillParam);
        log.info("excel 位置: {}", result);
        FeignResponseValidate.check(result);
        return result;
    }

    /**
     * 直付通列表导出
     *
     * @param payBillParam
     * @return
     */
    public ObjectResponse<ExcelPosition> exportZftList(ZftBillParam payBillParam) {
        ObjectResponse<ExcelPosition> result = ostFeignClient.zftBillExportExcelManager(
            payBillParam);
        log.info("excel 位置: {}", result);
        FeignResponseValidate.check(result);
        return result;
    }

    public Mono<ObjectResponse<ExcelPosition>> exportZftThirdOrderList(
        ListZftThirdOrderParam param) {
        ObjectResponse<ExcelPosition> result =
            ostFeignClient.exportZftThirdOrderList(param);
        log.info("excel 位置: {}", result);
        FeignResponseValidate.check(result);
        return Mono.just(result);
    }


    public ObjectResponse<Boolean> exportCompleted(ExcelPosition excelPosition) {
        ObjectResponse<Boolean> result = ostFeignClient.checkExcelFileCompeleted(null,
            excelPosition.getSubDir(), excelPosition.getSubFileName());
        log.info("excel 生成结果: {}", result);
        FeignResponseValidate.check(result);
        return result;
    }


    public Mono<Void> exportDownload(ExcelPosition excelPosition, ServerHttpResponse response) {
        ObjectResponse<Boolean> completedRes = this.exportCompleted(excelPosition);
        if (completedRes.getData()) {
            String fileName = excelPosition.getSubFileName() + ".xlsx";

            try {
                response.getHeaders()
                    .setContentType(MediaType.valueOf("application/vnd..ms-excel"));
                response.getHeaders().add("content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
                //读取指定路径下面的文件
                Response res = ostFeignClient.download(null, excelPosition.getSubDir(),
                    excelPosition.getSubFileName());
                Response.Body body = res.body();

                InputStream in = body.asInputStream();

                DefaultDataBuffer bf = new DefaultDataBufferFactory().allocateBuffer();

                byte[] buff = new byte[1024];
                //所读取的内容使用n来接收
                int n;
                //当没有读取完时,继续读取,循环
                OutputStream os = bf.asOutputStream();
                while ((n = in.read(buff)) != -1) {
                    //将字节数组的数据全部写入到输出流中
//                    outputStream.write(buff, 0, n);

                    os.write(buff, 0, n);
                    log.info("write {}", n);

                }
                os.flush();

                in.close();

                return response.writeWith(Mono.just(bf))
                    .then(Mono.just(Boolean.TRUE))
                    .flatMap(a -> {
                        try {
                            log.info("aaaaaaaaaaaaaa");
                            os.close();
                        } catch (IOException e) {
                            log.warn("exception = {}", e.getMessage(), e);
                        }
                        return Mono.empty();
                    })

                    ;
                //创建存放文件内容的数组
            } catch (Exception e) {
                log.error("导出订单excel失败,{}", e.getMessage());
            }
        } else {
            log.error("excel文件不存在");
        }

        return Mono.empty();
    }


    public ListResponse<PayBillBi> payBillBi(PayBillParam param) {
        return dataCoreFeignClient.payBillBi(param);
//        ListResponse<PayBillBi> res = dataCoreFeignClient.payBillBi(param);
//        FeignResponseValidate.check(res);
//
//        // 统计
//        // 充值总额: 是否包含减少，怎样计算
//        PayBillBi bi = new PayBillBi();
//        bi.setNum(res.getData().stream().mapToLong(PayBillBi::getNum).sum())
//                .setCompletedNum(res.getData().stream().mapToLong(PayBillBi::getCompletedNum).sum());
//
//        // 仅计算充值增加部分
//        bi.setTotal(BigDecimal.ZERO);
//        bi.setArrivalTotal(BigDecimal.ZERO);
//        bi.setFreeTotal(BigDecimal.ZERO);
//
//        res.getData().stream().filter(item -> item.getFlowType() == DepositFlowType.IN_FLOW).collect(Collectors.toList()).forEach(item -> {
//            bi.setTotal(bi.getTotal().add(item.getTotal()));
//            bi.setArrivalTotal(bi.getArrivalTotal().add(item.getArrivalTotal()));
//            bi.setFreeTotal(bi.getFreeTotal().add(item.getFreeTotal()));
//        });
//
//        return RestUtils.buildObjectResponse(bi);
    }

    /**
     * 直付通汇总信息
     *
     * @param param
     * @return
     */
    public ListResponse<ZftBillBi> zftBillBi(ZftBillParam param) {
        return dataCoreFeignClient.zftBillBi(param);
    }


    public ObjectResponse<Integer> updateById(PayBillPo po) {
        if (null == po.getId()) {
            log.info("参数中没有提供充值记录的ID值");
            throw new DcArgumentException("请提供充值记录的Id值");
        }

        return dataCoreFeignClient.updateById(po);
    }


    public ObjectResponse<Integer> updateByOrderId(PayBillPo po) {
        if (StringUtils.isBlank(po.getOrderId())) {
            log.info("参数中没有提供充值记录的ID值");
            throw new DcArgumentException("请提供充值记录的Id值");
        }

        return dataCoreFeignClient.updateByOrderId(po);
    }


    public ObjectResponse<PayBillUsedDetail> pointRecLog(String orderId, String orderNo) {
        if (StringUtils.isBlank(orderId)) {
            throw new DcArgumentException("请提供需要查询的充值记录订单号");
        }

        return dataCoreFeignClient.pointRecLog(orderId, orderNo);
    }


    /**
     * 充值订单列表的资金块信息
     *
     * @param param
     * @return
     */
    public ListResponse<PointRecDto> getCzOrderPointsInfo(CzOrderPointParam param) {
        IotAssert.isNotNull(param, "请传入参");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getOrderIdList()), "请传充值单列表");

        return dataCoreFeignClient.getCzOrderPointsInfo(param);
    }


    public ObjectResponse<PayBillAccountDetailVo> getAccountDetail(String orderId) {
        if (StringUtils.isBlank(orderId)) {
            throw new DcArgumentException("请提供需要查询的充值记录订单号");
        }

        return dataCoreFeignClient.getAccountDetail(orderId);
    }


    public ObjectResponse<PayBillVo> payBillView(Long topCommId, String orderId) {
        if (StringUtils.isBlank(orderId)) {
            throw new DcArgumentException("请提供需要查询的充值记录订单号");
        }

        ObjectResponse<PayBillVo> payBillVoObjectResponse = dataCoreFeignClient.payBillView(
            orderId);

        if (payBillVoObjectResponse.getData() != null) {
            CusPayBillListParam cusPayBillListParam = new CusPayBillListParam();
            Set<String> orderNoList = new HashSet<>();
            orderNoList.add(orderId);
            cusPayBillListParam.setUid(payBillVoObjectResponse.getData().getUserId());
            cusPayBillListParam.setClientType(AppClientType.MGM_WEB);
            cusPayBillListParam.setTopCommId(topCommId);
            cusPayBillListParam.setOrderNoList(orderNoList);
            if (payBillVoObjectResponse.getData().getAccountType() == PayAccountType.COMMERCIAL) {
                cusPayBillListParam.setCommId(payBillVoObjectResponse.getData().getCommId());
            }
            AccountRemainInfo accountRemainInfo = userFeignClient.getCusPayBillList(
                    cusPayBillListParam)
                .doOnNext(FeignResponseValidate::check)
                .map(ListResponse::getData)
                .map(cusPayBillList -> {
                    AccountRemainInfo result = new AccountRemainInfo();

                    BigDecimal canRefundFee = cusPayBillList.stream()
                        .map(CusPayBillVo::getCanRefundAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                    return result
                        .setCanRefundFee(canRefundFee);
                }).block(Duration.ofSeconds(50L));
            payBillVoObjectResponse.getData()
                .setCanRefundAmount(accountRemainInfo.getCanRefundFee());
        }
        return payBillVoObjectResponse;
    }


    public ListResponse<PayBillLinkChargeOrderVo> orderPointRecLog(String orderNo) {
        if (StringUtils.isBlank(orderNo)) {
            throw new DcArgumentException("请提供需要查询的充电订单号");
        }

        return dataCoreFeignClient.orderPointRecLog(orderNo);
    }

    /**
     * 判断充值订单是否存在资金块在发票中心已开票或审核中的开票订单
     *
     * @param orderId
     * @return
     */

    public ObjectResponse<CheckTaxStatus> checkTaxStatus(String orderId) {
        if (StringUtils.isBlank(orderId)) {
            throw new DcArgumentException("请提供需要查询的充值订单号");
        }
        return dataCoreFeignClient.checkTaxStatus(orderId);
    }

    public ObjectResponse<RefundAnalyzeVo> refundAnalyze(Date startDate, Date stopDate) {

        return dataCoreFeignClient.refundAnalyze(startDate, stopDate);
    }

    public ListResponse<RefundReasonVo> refundList(String cusName, String cusPhone, String cusNote,
        int start, int size) {

        return dataCoreFeignClient.refundList(cusName, cusPhone, cusNote, start, size);
    }

    public Mono<ListResponse<PayBillRefundVo>> getCorpPayBillList(ListPayBillParam param) {
        if (null == param.getUserId()) {
            throw new DcArgumentException("企业客户信息有误，请刷新后重试");
        }

        // 获取用户可退款的充值记录(参考个人账户)
        // /v2/cus/wallet/getPayBillList
        CusPayBillListParam cusPayBillListParam = new CusPayBillListParam();
        cusPayBillListParam.setClientType(AppClientType.CORP_WEB)
            .setUid(param.getUserId())
            .setTopCommId(param.getTopCommId());

        // 获取充值记录
        PayBillParam billParam = new PayBillParam();
        billParam.setUserId(param.getUserId());
        billParam.setOrderId(param.getOrderId());
        billParam.setPayTimeFilter(param.getPayTimeFilter());
        billParam.setFlowTypeList(List.of(DepositFlowType.IN_FLOW));
        billParam.setStatusList(List.of(PayBillStatus.PAID, PayBillStatus.EXHAUST));

        if (null != param.getTotal() && param.getTotal()) {
            if (param.getStart() == null) {
                param.setStart(0L);
            }

            if (param.getSize() == null || param.getSize() <= 0) {
                param.setSize(10);
            }

            billParam.setIndex((int) (param.getStart() / param.getSize() + 1));
            billParam.setSize(param.getSize());
        }
        Mono<ListResponse<PayBillVo>> payBill = Mono.just(
                dataCoreFeignClient.payBillList(billParam))
            .doOnNext(FeignResponseValidate::check);

        return Mono.zip(userFeignClient.getCusPayBillList(cusPayBillListParam)
                .doOnNext(FeignResponseValidate::check)
                .map(ListResponse::getData),
            payBill
        ).flatMap(e -> {
            Map<String, CusPayBillVo> collect = e.getT1().stream()
                .collect(Collectors.toMap(CusPayBillVo::getOrderId, o -> o));

            List<PayBillRefundVo> result = this.map2PayBillRefundVo(e.getT2().getData());
            result.forEach(item -> {
                CusPayBillVo cusPayBillVo = collect.get(item.getOrderId());
                item.setCanRefundAmount(null == cusPayBillVo ?
                    BigDecimal.ZERO : cusPayBillVo.getCanRefundAmount());
            });

            return Mono.just(RestUtils.buildListResponse(result, e.getT2().getTotal()));
        });

//        return userFeignClient.getCusPayBillList(cusPayBillListParam)
//                .doOnNext(FeignResponseValidate::check)
//                .map(ListResponse::getData)
//                .map(cusPayBillList -> {
//                    Map<String, CusPayBillVo> collect = cusPayBillList.stream()
//                            .collect(Collectors.toMap(CusPayBillVo::getOrderId, o -> o));
//
//                    // 获取充值记录
//                    PayBillParam billParam = new PayBillParam();
//                    billParam.setUserId(param.getUserId());
//                    billParam.setOrderId(param.getOrderId());
//                    billParam.setPayTimeFilter(param.getPayTimeFilter());
//                    billParam.setFlowTypeList(List.of(DepositFlowType.IN_FLOW));
//                    billParam.setStatusList(List.of(PayBillStatus.PAID, PayBillStatus.EXHAUST));
//
//                    if (null != param.getTotal() && param.getTotal()) {
//                        if (param.getStart() == null) {
//                            param.setStart(0L);
//                        }
//
//                        if (param.getSize() == null || param.getSize() <= 0) {
//                            param.setSize(10);
//                        }
//
//                        billParam.setIndex((int)(param.getStart() / param.getSize() + 1));
//                        billParam.setSize(param.getSize());
//                    }
//
//                    ListResponse<PayBillVo> res = dataCoreFeignClient.payBillList(billParam);
//                    FeignResponseValidate.check(res);
//
//                    // 转换返回前端显示
//                    List<PayBillRefundVo> result = this.map2PayBillRefundVo(res.getData());
//                    result.forEach(item -> {
//                                CusPayBillVo cusPayBillVo = collect.get(item.getOrderId());
//                                item.setCanRefundAmount(null == cusPayBillVo ?
//                                        BigDecimal.ZERO : cusPayBillVo.getCanRefundAmount());
//                            });
//                    return RestUtils.buildListResponse(result, res.getTotal());
//                });
    }

    private List<PayBillRefundVo> map2PayBillRefundVo(List<PayBillVo> billVoList) {
        return billVoList.stream().map(this::map2PayBillRefundVo).collect(Collectors.toList());
    }

    private PayBillRefundVo map2PayBillRefundVo(PayBillVo billVo) {
        return new PayBillRefundVo().setOrderId(billVo.getOrderId())
            .setAmount(billVo.getAmount())
            .setFreeAmount(billVo.getFreeAmount())
            .setTotalAmount(billVo.getAmount().add(billVo.getFreeAmount()))
            .setSourceType(billVo.getSourceType())
            .setPayChannel(billVo.getPayChannel())
            .setCreateTime(billVo.getCreateTime())
            .setPayTime(billVo.getPayTime());
    }

    public Mono<ListResponse<PayBillRefundVo>> getCusPayBillList(ListPayBillParam param) {
        if (null == param.getUserId()) {
            throw new DcArgumentException("企业客户信息有误，请刷新后重试");
        }

        // 获取用户可退款的充值记录(参考个人账户)
        // /v2/cus/wallet/getPayBillList
        CusPayBillListParam cusPayBillListParam = new CusPayBillListParam();
        cusPayBillListParam.setClientType(AppClientType.MGM_WEB)
            .setUid(param.getUserId())
            .setTopCommId(param.getTopCommId());

        // 商户会员
        if (null != param.getCommId()) {
            cusPayBillListParam.setCommId(param.getCommId());
        }

        // 获取充值记录
        PayBillParam billParam = new PayBillParam();
        billParam.setUserId(param.getUserId());
        billParam.setOrderId(param.getOrderId());
        billParam.setPayTimeFilter(param.getPayTimeFilter());
        billParam.setFlowTypeList(List.of(DepositFlowType.IN_FLOW));
        billParam.setStatusList(List.of(PayBillStatus.PAID, PayBillStatus.EXHAUST));

        // 商户会员
        if (null != param.getCommId()) {
            billParam.setPayAccountList(List.of(new PayAccount()
                .setAccountCode(param.getCommId())
                .setAccountType(PayAccountType.COMMERCIAL)));
        } else {
            billParam.setPayAccountList(List.of(new PayAccount()
                .setAccountCode(param.getTopCommId())
                .setAccountType(PayAccountType.PERSONAL)));
        }

        if (null != param.getTotal() && param.getTotal()) {
            if (param.getStart() == null) {
                param.setStart(0L);
            }

            if (param.getSize() == null || param.getSize() <= 0) {
                param.setSize(10);
            }

            billParam.setIndex((int) (param.getStart() / param.getSize() + 1));
            billParam.setSize(param.getSize());
        }

        return Mono.zip(userFeignClient.getCusPayBillList(cusPayBillListParam),
                Mono.just(dataCoreFeignClient.payBillList(billParam)))
            .flatMap(e -> {
                FeignResponseValidate.check(e.getT1());
                Map<String, CusPayBillVo> collect = e.getT1().getData().stream()
                    .collect(Collectors.toMap(CusPayBillVo::getOrderId, o -> o));
                FeignResponseValidate.check(e.getT2());
                ListResponse<PayBillVo> t2 = e.getT2();

                List<PayBillRefundVo> result = this.map2PayBillRefundVo(t2.getData());
                result.forEach(item -> {
                    CusPayBillVo cusPayBillVo = collect.get(item.getOrderId());
                    item.setCanRefundAmount(null == cusPayBillVo ?
                        BigDecimal.ZERO : cusPayBillVo.getCanRefundAmount());
                });
                return Mono.just(RestUtils.buildListResponse(result, t2.getTotal()));
            });

//        return userFeignClient.getCusPayBillList(cusPayBillListParam)
//                .doOnNext(FeignResponseValidate::check)
//                .map(ListResponse::getData)
//                .map(cusPayBillList -> {
//                    Map<String, CusPayBillVo> collect = cusPayBillList.stream()
//                            .collect(Collectors.toMap(CusPayBillVo::getOrderId, o -> o));
//
//                    // 获取充值记录
//                    PayBillParam billParam = new PayBillParam();
//                    billParam.setUserId(param.getUserId());
//                    billParam.setOrderId(param.getOrderId());
//                    billParam.setPayTimeFilter(param.getPayTimeFilter());
//                    billParam.setFlowTypeList(List.of(DepositFlowType.IN_FLOW));
//                    billParam.setStatusList(List.of(PayBillStatus.PAID, PayBillStatus.EXHAUST));
//
//                    // 商户会员
//                    if (null != param.getCommId()) {
//                        billParam.setPayAccountList(List.of(new PayAccount()
//                                .setAccountCode(param.getCommId())
//                                .setAccountType(PayAccountType.COMMERCIAL)));
//                    } else {
//                        billParam.setPayAccountList(List.of(new PayAccount()
//                                .setAccountCode(param.getTopCommId())
//                                .setAccountType(PayAccountType.PERSONAL)));
//                    }
//
//                    if (null != param.getTotal() && param.getTotal()) {
//                        if (param.getStart() == null) {
//                            param.setStart(0L);
//                        }
//
//                        if (param.getSize() == null || param.getSize() <= 0) {
//                            param.setSize(10);
//                        }
//
//                        billParam.setIndex((int)(param.getStart() / param.getSize() + 1));
//                        billParam.setSize(param.getSize());
//                    }
//
//                    ListResponse<PayBillVo> res = dataCoreFeignClient.payBillList(billParam);
//                    FeignResponseValidate.check(res);
//
//                    // 转换返回前端显示
//                    List<PayBillRefundVo> result = this.map2PayBillRefundVo(res.getData());
//                    result.forEach(item -> {
//                        CusPayBillVo cusPayBillVo = collect.get(item.getOrderId());
//                        item.setCanRefundAmount(null == cusPayBillVo ?
//                                BigDecimal.ZERO : cusPayBillVo.getCanRefundAmount());
//                    });
//                    return RestUtils.buildListResponse(result, res.getTotal());
//                });
    }
}
