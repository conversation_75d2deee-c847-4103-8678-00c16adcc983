package com.cdz360.biz.ant.domain.bundle;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;


/**
 * PC0XDto
 *  版本支持描述
 * <EMAIL>
 * <AUTHOR>
 * @since  2019/9/16 8:52
 */

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class PC0XDto extends PC0X {

    private String adaptiveHws;
    private String adaptiveSws;
    private String adaptiveOders;
    private String path;
}
