package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.domain.User;
import com.cdz360.biz.model.cus.commScore.param.CommScoreParam;
import com.cdz360.biz.model.cus.commScore.param.ScoreClearParam;
import com.cdz360.biz.model.cus.commScore.param.UserScoreListParam;
import com.cdz360.biz.model.cus.commScore.param.UserScoreParam;
import com.cdz360.biz.model.cus.commScore.po.CommScoreLevelPo;
import com.cdz360.biz.model.cus.commScore.po.CommScoreLogPo;
import com.cdz360.biz.model.cus.commScore.vo.CommScoreVo;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.discount.param.UpdateCorpDiscountParam;
import com.cdz360.biz.model.cus.score.dto.ScoreUserDto;
import com.cdz360.biz.model.cus.score.param.ScoreUserAddParam;
import com.cdz360.biz.model.cus.score.param.ScoreUserParam;
import com.cdz360.biz.model.cus.score.param.SearchScoreUserParam;
import com.cdz360.biz.model.cus.settlement.param.SettlementEditParam;
import com.cdz360.biz.model.cus.score.dto.ScoreLevelDto;
import com.cdz360.biz.model.cus.score.dto.ScoreLogDto;
import com.cdz360.biz.model.cus.score.dto.ScoreSettingDto;
import com.cdz360.biz.model.cus.score.param.ScoreSettingParam;
import com.cdz360.biz.model.cus.score.param.ScoreUpdateParam;
import com.cdz360.biz.model.cus.score.param.SearchScoreLogParam;
import com.cdz360.biz.model.cus.score.param.SearchScoreSettingParam;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class ReactorUserFeignHystrix implements FallbackFactory<ReactorUserFeignClient> {
    @Override
    public ReactorUserFeignClient apply(Throwable throwable) {
        log.info("err = {}", throwable.getMessage(), throwable);
        return new ReactorUserFeignClient() {


            @Override
            public Mono<ObjectResponse<User>> setBasicInfo(User user) {
                log.error("【服务熔断】。Service = {}, api = setBasicInfo (设置用户资料信息)",
                        DcConstants.KEY_FEIGN_DC_BIZ_USER);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<CorpPo>> updateDiscount(UpdateCorpDiscountParam param) {
                log.error("【服务熔断】。Service = {}, api = updateDiscount (更新企业客户协议价配置)",
                        DcConstants.KEY_FEIGN_DC_BIZ_USER);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<Integer>> updateSettlementBatch(SettlementEditParam param) {
                log.error("【服务熔断】。Service = {}, api = updateSettlementBatch (批量更新账单)",
                    DcConstants.KEY_FEIGN_DC_BIZ_USER);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<RBlocUser>> selectRBlocUserByPhone(RBlocUser rBlocUser) {
                log.error("【服务熔断】。Service = {}, api = selectRBlocUserByPhone (根据phone和blocUserId查询集团用户)",
                        DcConstants.KEY_FEIGN_DC_BIZ_USER);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<CommScoreVo>> getCommScoreVo(Long commCusId) {
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> addCommScore(CommScoreParam param) {
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<BaseResponse> commUserScoreRest(ScoreClearParam param) {
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<BaseResponse> commUserScoreModify(UserScoreParam param) {
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<ListResponse<CommScoreLogPo>> commUserScoreList(UserScoreListParam param) {
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<CommScoreLevelPo>> findLevelListByCommId(Long commId) {
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<Integer>> getUserCount(String idChain, Long topCommId) {
                log.error("服务[{}]接口熔断 - 获取商户下用户数, idChain = {}, topCommId = {}",
                        DcConstants.KEY_FEIGN_DC_BIZ_USER, idChain, topCommId);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> addScoreSetting(ScoreSettingParam param) {
                log.error("【服务熔断】。Service = {}, api = addScoreSetting, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_USER, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<ListResponse<ScoreSettingDto>> getScoreSettingList(
                SearchScoreSettingParam param) {
                log.error("【服务熔断】。Service = {}, api = getScoreSettingList, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_USER, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<BaseResponse> deleteScoreSetting(Long id) {
                log.error("【服务熔断】。Service = {}, api = deleteScoreSetting, id = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_USER, id);
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<BaseResponse> startScoreSetting(Long id) {
                log.error("【服务熔断】。Service = {}, api = startScoreSetting, id = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_USER, id);
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<BaseResponse> stopScoreSetting(Long id) {
                log.error("【服务熔断】。Service = {}, api = stopScoreSetting, id = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_USER, id);
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<BaseResponse> updateScoreSetting(ScoreSettingParam param) {
                log.error("【服务熔断】。Service = {}, api = updateScoreSetting, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_USER, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<BaseResponse> updateScore(ScoreUpdateParam param) {
                log.error("【服务熔断】。Service = {}, api = updateScore, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_USER, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<ObjectResponse<ScoreLevelDto>> getScoreLevel(Long userId, Long scoreSettingId) {
                log.error("【服务熔断】。Service = {}, api = stopScoreSetting, userId = {}, scoreSettingId = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_USER, userId, scoreSettingId);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<ScoreLogDto>> getScoreList(SearchScoreLogParam param) {
                log.error("【服务熔断】。Service = {}, api = getScoreList, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_USER, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<Boolean>> checkAccountStatus(PayAccountType accountType,
                Long commId, Long userId) {
                log.error("【服务熔断】。Service = {}, api = checkAccountStatus, accountType = {}, commId = {}, userId = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_USER, accountType, commId, userId);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<ScoreUserDto>> getScoreUserList(SearchScoreUserParam param) {
                log.error("【服务熔断】。Service = {}, api = getScoreUserList, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_USER, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<BaseResponse> deleteScoreUser(ScoreUserParam param) {
                log.error("【服务熔断】。Service = {}, api = deleteScoreUser,param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_USER, param);
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<BaseResponse> addScoreUser(ScoreUserAddParam param) {
                log.error("【服务熔断】。Service = {}, api = addScoreUser,param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_USER, param);
                return Mono.just(RestUtils.serverBusy());
            }
        };
    }

    @Override
    public <V> Function<V, ReactorUserFeignClient> compose(Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(Function<? super ReactorUserFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER);
        return null;
    }
}
