package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.charge.type.OrderAbnormalReason;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.biz.ant.constant.DownloadType;
import com.cdz360.biz.ant.domain.ChargerOrder;
import com.cdz360.biz.ant.domain.vo.ChargerOrderFinishVo;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.TradingFeignClient;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.ant.service.DictService;
import com.cdz360.biz.ant.service.order.ChargerOrderService;
import com.cdz360.biz.ant.service.sysLog.CommercialSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.common.constant.DcBizConstants;
import com.cdz360.biz.model.cus.discount.vo.DiscountStrategyVo;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.order.param.ListSettlementOrderParam;
import com.cdz360.biz.model.order.param.NotInSettlementOrderParam;
import com.cdz360.biz.model.trading.order.dto.LowKwOrderDto;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.param.ListChargerOrderParamX;
import com.cdz360.biz.model.trading.order.param.OrderTimeDivisionDiscountParam;
import com.cdz360.biz.model.trading.order.po.ChargerOrderTimeDivision;
import com.cdz360.biz.model.trading.order.vo.SettlementOrderVo;
import com.cdz360.biz.utils.feign.order.DataCoreOrderFeignClient;
import com.chargerlinkcar.framework.common.constant.OrderStatus;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.Dict;
import com.chargerlinkcar.framework.common.domain.OrderTimeDivisionParam;
import com.chargerlinkcar.framework.common.domain.PointLog;
import com.chargerlinkcar.framework.common.domain.order.ChargerOrderWithBLOBs;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.domain.vo.ChargerDetailVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDataVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDetailVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.UpdateOrderVo;
import com.chargerlinkcar.framework.common.exception.ChargerlinkException;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import com.chargerlinkcar.framework.common.utils.PlugNoUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR> 订单相关
 * @since 2018/11/28 11:08
 */
@Slf4j
@RestController
//@Observed
@Tag(name = "订单相关操作接口", description = "订单相关操作接口")
public class OrderRest extends BaseController {


    @Autowired
    private ChargerOrderService chargerOrderService;

    @Autowired
    private DictService dictService;
    @Autowired
    private CommercialSysLogService commLogService;

    @Autowired
    private TradingFeignClient tradingFeignClient;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private DataCoreOrderFeignClient dataCoreOrderFeignClient;

    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;

    @Operation(summary = "充电订单列表查询", description = "优化版本，不建议使用")
    @PostMapping("/pai/order/queryChargeOrderListX")
    public Mono<ListResponse<ChargerOrderVo>> queryChargeOrderListX(
        ServerHttpRequest request, @RequestBody ListChargerOrderParamX param) {
        log.info("充电订单列表查询: {}, {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        return this.chargerOrderService.queryChargeOrderListX(param);
    }

    /**
     * 订单列表
     *
     * @return headerparam token
     */
    @Operation(summary = "充电订单列表查询")
    @PostMapping("/api/order/queryChargeOrderList")

    public ListResponse<ChargerOrderVo> queryChargeOrderList(ServerHttpRequest request,
        ServerWebExchange exh,
        @RequestBody ChargerOrderParam searchParam) {
        this.queryChargeOrderListUnify(searchParam, request, exh);
        searchParam.setLocale(AntRestUtils.getLocale(request));
        log.debug("param = {}", searchParam);
        return chargerOrderService.queryChargeOrderList(searchParam);
    }

    /**
     * 处理searchParam
     *
     * @param searchParam
     * @param request
     */
    private void queryChargeOrderListUnify(ChargerOrderParam searchParam, ServerHttpRequest request,
        ServerWebExchange exh) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param={}", searchParam);

        List<String> gids = AntRestUtils.getSysUserGids(request);
        if (CollectionUtils.isNotEmpty(gids)) {
            searchParam.setGids(AntRestUtils.getSysUserGids(request));
        } else {
            //List<Long> commIdList = this.getCommIdList(request);
            if (StringUtils.isNotBlank(searchParam.getSiteCommIdChain())) {
                // 查询入参商户链
            } else if (NumberUtils.equals(AntRestUtils.getCommId(request),
                DcBizConstants.superTopCommId)) {
                // 集团商户后门
                searchParam.setTopCommId(-1L);
            } else {
                String commIdChain = super.getCommIdChain2(request);
                //searchParam.setCommIdList(commIdList);
                searchParam.setSiteCommIdChain(commIdChain);
            }
        }
        // 设置分页参数
        OldPageParam page = getPage2(10, request, exh, true);
        log.info("page index={}, size={}, count={}", page.getPageNum(), page.getPageSize(),
            page.getCountTotal());
        searchParam.setCurrent(page.getPageNum());
        searchParam.setSize(page.getPageSize());
        if (null != searchParam.getTotal()) {
            searchParam.setTotal(searchParam.getTotal());
        } else {
            searchParam.setTotal(page.getCountTotal());
        }

        // 数据格式 桩号-枪号
        String bcCode = searchParam.getBcCode();
        if (StringUtils.isNotBlank(bcCode)) {
            if (bcCode.contains("-")) {
                String[] spstrBcCode = bcCode.split("\\-");
                searchParam.setBoxOutFactoryCode(spstrBcCode[0]);
                if (spstrBcCode[1] != null) {
                    searchParam.setConnectorId(spstrBcCode[1]);
                }
            } else {
                searchParam.setBoxOutFactoryCode(bcCode);
            }
        }
        //订单来源类型,0:微信渠道;1:APP在线发起充电;2:APP蓝牙发起充电:3:刷卡充电:4:桩上报离线数据5定时充电6手动开启7曹操专车开启
        //searchParam.setChannelIdList(getChannelIdList()); // 保留原来的逻辑
//        searchParam.setOrderTypeList(getOrderTypeList());

        if (searchParam.getAbnormal() != null) {
            searchParam.setAbnormalList(Arrays.asList(searchParam.getAbnormal()));
        }
    }

    /**
     * 根据条件查询订单统计数据
     *
     * @param searchParam
     * @return
     */
    @PostMapping("/api/order/getChargerOrderData")
    public ObjectResponse<ChargerOrderDataVo> getChargerOrderData(ServerHttpRequest request,
        ServerWebExchange exh,
        @RequestBody ChargerOrderParam searchParam) {

        this.queryChargeOrderListUnify(searchParam, request, exh);
        ObjectResponse<ChargerOrderDataVo> resultEntity = chargerOrderService.getChargerOrderData(
            searchParam);
        return resultEntity;
    }

    /**
     * 根据条件查询订单统计数据（尖峰平谷）
     *
     * @param param
     * @return
     */

    @PostMapping("/api/order/getChargerOrderDetail")
    @Operation(summary = "订单统计数据（尖峰平谷）")
    public Mono<ObjectResponse<ChargerOrderDetailVo>> getChargerOrderDetail(
        ServerWebExchange exh,
        @RequestBody ChargerOrderParam param, ServerHttpRequest request) {
        log.debug(LoggerHelper2.formatEnterLog(request) + "param = {}", param);
        this.queryChargeOrderListUnify(param, request, exh);
        Mono<ObjectResponse<ChargerOrderDetailVo>> resultEntity = chargerOrderService.getChargerOrderDetail(
                param)
            .doOnNext(res -> log.debug(LoggerHelper2.formatLeaveLog(request) + " res = {}", res));
        return resultEntity;
    }


    /**
     * 导出在线订单列表(异步生成ecxel）
     *
     * @param searchParam
     * @throws ChargerlinkException
     */
    @Operation(summary = "导出在线订单列表")
    @PostMapping("/api/order/exportExcelByChargeOrderListV2")
    public Mono<ObjectResponse<ExcelPosition>> exportExcelByChargeOrderListV2(
        ServerWebExchange exh,
        @RequestBody ChargerOrderParam searchParam,
        ServerHttpRequest request) throws ChargerlinkException {
        log.info(LoggerHelper2.formatEnterLog(request) + "导出在线订单列表 param = {}", searchParam);

        String token = getToken2(request);
        searchParam.setSubDir(token);

        this.queryChargeOrderListUnify(searchParam, request, exh);

        // 用于写入excel时，数值转换为文字显示
        List<Dict> orderSourceLst = dictService.findDictDataByType("orderSource").getData();
        List<Dict> orderStatusLst = dictService.findDictDataByType("orderStatus").getData();
        searchParam.setOrderSourceLst(orderSourceLst);
        searchParam.setOrderStatusLst(orderStatusLst);

        if (searchParam.getPlatform() == null) { // 这个REST接口会被另一个REST接口调用，所以可能不为null
            // 若为null，则标记来自充电管理平台WEB的请求
            searchParam.setPlatform(21);
        } else {
            // 企业管理平台不考虑
            searchParam.setSiteCommIdChain(null);
        }

//        ExcelPosition position = chargerOrderService.checkAndWriteTempExcelByChargeOrderList(searchParam);
//        log.info("excel 位置: {}", position);

        Long sysUid = AntRestUtils.getSysUid(request);

        // 设置语言环境
        Locale locale = AntRestUtils.getLocale(request);
        // 如果是null就设置为null
        searchParam.setLocale(locale);

        return chargerOrderService.checkAndWriteTempExcelByChargeOrderList(
            AntRestUtils.getAppClientType(request), sysUid, searchParam);
    }

    @Operation(summary = "导出车辆详情订单信息")
    @PostMapping("/api/order/exportVinOrderList")
    public Mono<ObjectResponse<ExcelPosition>> exportVinOrderList(
        ServerWebExchange exh,
        @RequestBody ChargerOrderParam searchParam,
        ServerHttpRequest request) throws ChargerlinkException {
        log.info(LoggerHelper2.formatEnterLog(request) + "导出车辆详情订单信息 param = {}", searchParam);

        String token = getToken2(request);
        searchParam.setSubDir(token);

        this.queryChargeOrderListUnify(searchParam, request, exh);
        searchParam.setTopCommId(AntRestUtils.getTopCommId(request));

        // 用于写入excel时，数值转换为文字显示
        List<Dict> orderSourceLst = dictService.findDictDataByType("orderSource").getData();
        List<Dict> orderStatusLst = dictService.findDictDataByType("orderStatus").getData();
        List<Dict> payTypeLst = dictService.findDictDataByType("payType").getData();
        searchParam.setOrderSourceLst(orderSourceLst);
        searchParam.setOrderStatusLst(orderStatusLst);
        searchParam.setPayTypeDictList(payTypeLst);

        return chargerOrderService.checkAndWriteTempExcelByVinOrderList(request, searchParam);
//        log.info("excel 位置: {}", position);
//
//        return new ObjectResponse<>(position);
    }

    @RequestMapping(value = "/api/order/checkExcelFileCompeleted")
    public ObjectResponse checkExcelFileCompeleted(
        @Parameter(name = "文件类型", example = "pdf/xlsx")
        @RequestParam(value = "type", required = false, defaultValue = "xlsx") String type,
        @RequestParam(value = "subFileName") String subFileName,
        @RequestParam(value = "subDir") String subDir,
        ServerHttpRequest request) {
        log.info("检查excel/pdf是否生成, type:{}, subFileName:{}, subDir:{}", type, subFileName,
            subDir);
        String token = getToken2(request);
        if (StringUtils.isEmpty(token) || StringUtils.isEmpty(subFileName) || StringUtils.isEmpty(
            subDir)) {
            throw new DcArgumentException("参数错误");
        }
        boolean exist = chargerOrderService.existsDownFile(subDir, type, subFileName);

        return new ObjectResponse<>(exist);
    }

    @Deprecated(since = "20220223") // /api/download/downloadFile
    //下载excel/pdf文件
    @RequestMapping(value = "/api/order/download")
    public Mono<Void> download(
        ServerHttpRequest request, ServerHttpResponse response,
        @Parameter(name = "文件类型", example = "pdf/xlsx")
        @RequestParam(value = "type", required = false, defaultValue = "xlsx") String type,
        @RequestParam(value = "subFileName") String subFileName,
        @RequestParam(value = "subDir") String subDir) throws IOException {
        log.info("下载excel/pdf, type:{}, subFileName:{}, subDir:{}", type, subFileName, subDir);
        String token = getToken2(request);
        if (StringUtils.isEmpty(token) || StringUtils.isEmpty(subFileName) || StringUtils.isEmpty(
            subDir)) {
            throw new DcArgumentException("参数错误");
        }
        return chargerOrderService.downFile(subDir, type, subFileName, response);
    }


    /**
     * 订单列表（包含在线订单和离线订单和异常订单）
     *
     * @return headerparam commIdList
     */
    @PostMapping("/api/order/queryChargeOrderListAll")
    public ListResponse<ChargerOrderVo> queryChargeOrderListAll(ServerHttpRequest request,
        ServerWebExchange exh,
        @RequestBody ChargerOrderParam searchParam) {
        List<Long> commIdList = this.getCommIdList2(request);

        if (CollectionUtils.isEmpty(commIdList)) {
            throw new DcArgumentException("参数错误");
        }
        Long opTopCommId = AntRestUtils.getTopCommId(request);

        List<String> gids = AntRestUtils.getSysUserGids(request);
        if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(gids)) {
            searchParam.setGids(AntRestUtils.getSysUserGids(request));
        } else {

            searchParam.setTopCommId(opTopCommId);
            if (NumberUtils.equals(AntRestUtils.getCommId(request),
                DcBizConstants.superTopCommId)) {
                searchParam.setTopCommId(-1L);
            }
        }
        OldPageParam page = getPage2(request, exh, true);
        //searchParam.setCommIdList(commIdList);
        searchParam.setSiteCommIdChain(super.getCommIdChain2(request));
        searchParam.setCurrent(page.getPageNum());
        searchParam.setSize(page.getPageSize());
        searchParam.setLocale(AntRestUtils.getLocale(request));
        String bcCode = searchParam.getBcCode();
        if (StringUtils.isNotBlank(bcCode)) {
            if (StringUtils.equals(bcCode, "-")) {
                searchParam.setBoxOutFactoryCode(bcCode);
            } else {
                if (bcCode.contains("-")) {
                    String[] spstrBcCode = bcCode.split("\\-");
                    if (spstrBcCode.length > 0) {
                        searchParam.setBoxOutFactoryCode(spstrBcCode[0]);
                        if (spstrBcCode.length > 1) {
                            searchParam.setConnectorId(spstrBcCode[1]);
                        }
                    }
                } else {
                    searchParam.setBoxOutFactoryCode(bcCode);
                }
            }
        }

        // TODO: 2019/9/28 改为用order_type来查询
        if (DownloadType.OFFLINE_ORDER.getCode().equals(searchParam.getType())) {
            List<Integer> orderTypeList = Arrays.asList(OrderStartType.EVSE_OFFLINE_CARD.getCode(),
                OrderStartType.EVSE_AUTO.getCode(),
                OrderStartType.OFFLINE_CARD.getCode());
            searchParam.setOrderTypeList(orderTypeList);
        }
        if (DownloadType.ABNORMAL_ORDER.getCode().equals(searchParam.getType())) {
            // 异常订单
            log.info("查询异常订单");

            long commId = AntRestUtils.getCommId(request);  // super.getCommIdLong2(request);
            if (opTopCommId.longValue() == DcBizConstants.superTopCommId
                && opTopCommId.longValue() == commId) {
                searchParam.setTopCommId(-1L); // 这个是个后门代码....用于查询所有的异常订单
            } else {
                searchParam.setTopCommId(opTopCommId);
            }

            if (CollectionUtils.isEmpty(searchParam.getAbnormalList())) {
                searchParam.setAbnormalList(
                    Arrays.stream(OrderAbnormalReason.values()).collect(Collectors.toList()));
            }

            // 查询结果针对于订单状态为已结算的订单不在异常订单列表内可查询。
            if (CollectionUtils.isEmpty(searchParam.getStatusList())) {//如果入参没有订单状态，默认查询非已结算的订单
                searchParam.setStatusList(
                    Arrays.asList(
                        OrderStatus.ORDER_STATUS_UNACTIVATED,
                        OrderStatus.ORDER_STATUS_CHARGING,
                        OrderStatus.ORDER_STATUS_COMPLETE,
                        OrderStatus.ORDER_STATUS_ERROR_CP));
            } else {
                // 排除已结算后的订单状态集合
                List<Integer> statusLst = searchParam.getStatusList().stream()
                    .filter(s -> !s.equals(OrderStatus.ORDER_STATUS_RECEIVE_MONEY))
                    .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(statusLst)) {//订单状态只有已结算，查询结果为空
                    return new ListResponse<>();
                }
                searchParam.setStatusList(statusLst);
            }
        }

        return chargerOrderService.queryChargeOrderList(searchParam);
    }

    @Operation(summary = "导出商户会员订单列表")
    @PostMapping("/api/order/exportCommUserOrderList")
    public Mono<ObjectResponse<ExcelPosition>> exportCommUserOrderList(
        ServerWebExchange exh,
        @RequestBody ChargerOrderParam searchParam,
        ServerHttpRequest request) throws ChargerlinkException {
        log.info(LoggerHelper2.formatEnterLog(request) + "导出商户会员订单列表 param = {}", searchParam);

        String token = getToken2(request);
        searchParam.setSubDir(token);

        Long opTopCommId = AntRestUtils.getTopCommId(request);
        searchParam.setTopCommId(opTopCommId);
        List<String> gids = AntRestUtils.getSysUserGids(request);
        if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(gids)) {
            searchParam.setGids(AntRestUtils.getSysUserGids(request));
        } else {
            searchParam.setSiteCommIdChain(super.getCommIdChain2(request));
        }

        OldPageParam page = getPage2(request, exh, true);
        searchParam.setCurrent(page.getPageNum());
        searchParam.setSize(page.getPageSize());

        // 用于写入excel时，数值转换为文字显示
        List<Dict> orderSourceList = dictService.findDictDataByType("orderSource").getData();
        List<Dict> orderStatusList = dictService.findDictDataByType("orderStatus").getData();
        List<Dict> payModesList = dictService.findDictDataByType("payChannel").getData();

        searchParam.setOrderSourceLst(orderSourceList);
        searchParam.setOrderStatusLst(orderStatusList);
        searchParam.setPayModesLst(payModesList);

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("商户会员订单")
            .setFunctionMap(DownloadFunctionType.COMM_USER_ORDER_RECORD)
            .setReqParam(JsonUtils.toJsonString(searchParam));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
    }

    /**
     * 分页查询枪头订单列表
     *
     * @param beginTime
     * @param endTime
     * @param status
     * @param channelId
     * @return
     */
    @RequestMapping("/api/order/queryChargerOrderListByConnectId")
    public ListResponse<ChargerOrder> queryChargerOrderListByConnectId(
        ServerWebExchange exh,
        String plugNo, String beginTime,
        String endTime, String status,
        Integer channelId, ServerHttpRequest request) {
//        List<Long> commIdList = this.getCommIdList2(request);
//
//        if (CollectionUtils.isEmpty(commIdList)) {
//            throw new DcArgumentException("参数错误");
//        }

        if (StringUtils.isBlank(plugNo)) {
            throw new DcArgumentException("参数错误");
        }

        Pair<String, Integer> tmp = PlugNoUtils.splitPlugNo(plugNo);
        String boxOutFactoryCode = tmp.getFirst();
        String connectorId = tmp.getSecond().toString();

        if (StringUtils.isBlank(boxOutFactoryCode)) {
            throw new DcArgumentException("参数错误");
        }

        if (StringUtils.isBlank(connectorId)) {
            throw new DcArgumentException("参数错误");
        }

        OldPageParam page = getPage2(request, exh, false);

        return chargerOrderService.selectChargerOrderListByConnectId(page, boxOutFactoryCode,
            connectorId, beginTime,
            endTime, status, channelId);
    }

    /**
     * 获取该商户列表下的客户 订单详情
     *
     * @param orderNo 订单NO
     * @return headerparam token
     */
    @GetMapping("/api/order/queryOrderDetail")
    public Mono<ObjectResponse<ChargerOrderWithBLOBs>> queryOrderDetail(
        @RequestParam(value = "orderNo", required = false) String orderNo,
        ServerHttpRequest request) {

        Long opTopCommId = AntRestUtils.getTopCommId(request);
        //searchParam.setTopCommId(opTopCommId);
        List<Long> commIdList = this.getCommIdList2(request);
        Long commId = AntRestUtils.getCommId(request);
        String commIdChain = super.getCommIdChain2(request);
        List<String> gids = AntRestUtils.getSysUserGids(request);
        log.debug("topCommId = {}, commId = {}, commIdChain = {}, gids = {}",
            opTopCommId, commId, commIdChain, gids);

        if (CollectionUtils.isNotEmpty(gids)) {
            commIdChain = "";
        } else if (opTopCommId.longValue() == DcBizConstants.superTopCommId
            && opTopCommId.longValue() == commId) {
            commIdChain = ""; // 这个是个后门代码....用于查询所有的异常订单
        } else if (CollectionUtils.isEmpty(commIdList)) {
            throw new DcArgumentException("参数错误");
        }

        return chargerOrderService.queryOrderDetail2(commIdChain, orderNo, commId, gids);
    }

    @PostMapping("/api/order/getDivisionDiscount")
    public Mono<ListResponse<ChargerOrderTimeDivision>> getDivisionDiscount(
        @RequestBody OrderTimeDivisionDiscountParam param,
        ServerHttpRequest request) {
        log.info("查询协议价: {}", JsonUtils.toJsonString(param));
        return chargerOrderService.getDivisionDiscount(param);
    }

    @GetMapping("/api/order/getDiscountStrategy")
    public Mono<ObjectResponse<DiscountStrategyVo>> getDiscountStrategy(
        @Parameter(name = "协议价策略id") @RequestParam(value = "discountRefId") Long discountRefId,
        ServerHttpRequest request) {
        log.info("协议价策略： {}", discountRefId);
        return chargerOrderService.getDiscountStrategy(discountRefId);
    }


    /**
     * 获取订单的充电实况 -- 电压电流采样情况
     *
     * @param orderNo
     */
    @Operation(summary = "获取订单的充电实况")
    @GetMapping(value = "/api/order/samplingInfo")
    public Mono<ListResponse<ChargerDetailVo>> queryOrderInfo(
        @Parameter(name = "充电订单号") @RequestParam(value = "orderNo") String orderNo,
        ServerHttpRequest request) {
//        log.info(">> 获取订单信息, orderNo={}", orderNo);
        log.info(">> 获取订单信息" + LoggerHelper2.formatEnterLog(request));

        Long commId = AntRestUtils.getCommId(request);    // this.getCommId(request);
        if (commId == null || commId < 1L) {
            throw new DcArgumentException("参数错误");
        }

        if (StringUtils.isBlank(orderNo)) {
            log.info("<< 订单编号不能为空.");
            throw new DcArgumentException("订单编号不能为空.");
        }

        // ListResponse<ChargerDetailVo> listResponse =
        return dataCoreOrderFeignClient.getOrderSamplingInfo(orderNo)
            .doOnNext(res -> {
                log.info("<< Feign 调用成功返回: status = {}, size = {}",
                    res.getStatus(),
                    res.getData() != null ? res.getData().size() : null);
            });
//        return listResponse;
    }

    /**
     * 获取估算分时数据
     *
     * @param request
     * @param updateOrderVo
     * @return
     */
    @PostMapping(value = "/api/order/queryEstimatedOrderTimeDivisionList")
    public ListResponse<ChargerOrderTimeDivision> queryEstimatedOrderTimeDivisionList(
        ServerHttpRequest request,
        @RequestBody UpdateOrderVo updateOrderVo) {

        log.info("获取估算的分时数据, updateOrderVo: {}", JsonUtils.toJsonString(updateOrderVo));
        return dataCoreFeignClient.estimateOrderTimeDivisionList(updateOrderVo);
    }


    /**
     * 查询分时订单列表
     *
     * @param orderNo
     */
    @PostMapping(value = "/api/order/queryOrderTimeDivisionList")
    public ListResponse<ChargerOrderTimeDivision> queryOrderTimeDivisionList(
        ServerHttpRequest request,
        @RequestParam String orderNo) {

        Long commId = AntRestUtils.getCommId(request);    // this.getCommId(request);
        if (commId == null || commId < 1L) {
            throw new DcArgumentException("参数错误");
        }
        if (com.cdz360.base.utils.StringUtils.isBlank(orderNo)) {
            throw new DcArgumentException("参数错误");
        }

        ListResponse<ChargerOrderTimeDivision> resultEntity = chargerOrderService.queryOrderTimeDivisionList(
            orderNo);

        return resultEntity;
    }

    /**
     * 获取分时统计相关数据：订单数量，充电时长，电费，服务费，充电电量
     *
     * @param startTime 查询开始时间戳
     * @param endTime   查询结束时间戳
     * @param province
     * @param city
     * @return servicePrice：服务费(分)，electricPrice：电费（分），
     * duration：时长(秒)，orderCount：订单数，electric：订单电量(单位0.01度)
     */
    @RequestMapping("/api/order/getOrderTimeDivisionData")
    public ListResponse getOrderTimeDivisionData(
        @RequestParam("startTime") Long startTime,
        @RequestParam("endTime") Long endTime,
        @RequestParam(value = "siteId", required = false) String siteId,
        @RequestParam(value = "province", required = false) Integer province,
        @RequestParam(value = "city", required = false) Integer city,
        ServerHttpRequest request) throws ChargerlinkException {
        log.info(LoggerHelper2.formatEnterLog(request));
        List<Long> commIdList = this.getCommIdList2(request);
        if (CollectionUtils.isEmpty(commIdList)) {
            throw new DcArgumentException("参数错误");
        }

        if (startTime == null || startTime <= 0) {
            throw new DcArgumentException("开始时间不能为空");
        }

        if (endTime == null || endTime <= 0) {
            throw new DcArgumentException("结束时间不能为空");
        }

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        OrderTimeDivisionParam param = new OrderTimeDivisionParam();
        param.setStartTime(format.format(new Date(startTime)))
            .setEndTime(format.format(new Date(endTime)))
            .setCommIdList(commIdList)
            .setCity(city)
            .setProvince(province);
        return tradingFeignClient.orderTimeDivisionData(param);
    }


    /**
     * 手动修改异常订单
     *
     * @param updateOrderVo
     * @return
     */
    @RequestMapping(value = "/api/order/updateOrder", method = RequestMethod.POST)
    public BaseResponse updateOrder(ServerHttpRequest request,
        @RequestBody UpdateOrderVo updateOrderVo) {
        log.info(">> 手动修改异常订单: order={}", updateOrderVo);

        String orderNo = updateOrderVo.getOrderNo();
        if (StringUtils.isBlank(orderNo)) {
            log.info("<< 订单号为空，终止处理.");
            throw new DcArgumentException("订单号不能为空");
        }

        // 判断当前用户是否具有修改订单权限 -- 暂时不考虑

        BaseResponse res = chargerOrderService.updateOrder(updateOrderVo);
        commLogService.updateOrderLog(updateOrderVo.getOrderNo(), request);
        log.info("<< 手动修改订单结果: result={}", res);
        return res;
    }

    @PostMapping(value = "/api/order/refundForAbnormal")
    public BaseResponse refundForAbnormal(ServerHttpRequest request,
        @RequestParam("orderNo") String orderNo) {
        log.info(">> 即充即退手动退款: {}", LoggerHelper2.formatEnterLog(request));
        if (StringUtils.isBlank(orderNo)) {
            log.info("<< 订单号为空，终止处理.");
            throw new DcArgumentException("订单号不能为空");
        }
        BaseResponse res = tradingFeignClient.refundForAbnormal(orderNo);
        log.info("<< refundForAbnormal结果: result={}", res);
        return res;
    }

    /**
     * 根据站点ID获取未完成的订单
     *
     * @param siteId
     * @return
     */
    @GetMapping(value = "/api/order/getUnfinishOrderBysiteId")
    public ListResponse<ChargerOrder> getUnfinishOrderBysiteId(
        @RequestParam(value = "siteId", required = true) String siteId) {
        log.info(">> 站点编号: siteId={}", siteId);
        if (StringUtils.isBlank(siteId)) {
            log.info("<<站点编号不能为空.");
            throw new DcArgumentException("站点编号不能为空");
        }
        return tradingFeignClient.getUnfinishOrderBysiteId(siteId);
    }

    /**
     * 根据企业ID获取未完成的订单
     *
     * @param corpId
     * @return
     */
    @GetMapping(value = "/api/order/getUnfinishOrderByCorpId")
    public ListResponse<ChargerOrder> getUnfinishOrderBysiteId(
        @RequestParam(value = "corpId") Long corpId) {
        return tradingFeignClient.getUnfinishOrderByCorpId(corpId);
    }

    /**
     * 获取已完成的订单详情
     *
     * @param orderNo
     * @return
     */
    @GetMapping("/api/order/getFinishedOrderDetail")
    public ObjectResponse<ChargerOrderFinishVo> getFinishedOrderDetail(
        @RequestParam(value = "orderNo", required = false) String orderNo) {

        if (ObjectUtils.isEmpty(orderNo)) {
            //return new CommonRpcResponse<>(null, ResultConstant.RES_FAIL_CODE, "参数错误");
            throw new DcArgumentException("参数错误");
        }

        try {
            return chargerOrderService.queryFinishedOrderDetail(orderNo);
        } catch (Exception e) {
            log.error("获取订单详情失败:" + e.getMessage(), e);
            //return new CommonRpcResponse<>(null, ResultConstant.RES_FAIL_CODE, "获取订单详情失败");
            throw new DcServiceException("获取订单详情失败");
        }

    }


    /**
     * 订单的资金流水
     *
     * @param request
     * @return
     */
    @Operation(summary = "订单的资金流水")
    @GetMapping(value = "/api/order/queryPointLogByOrderNo")
    public Mono<ListResponse<PointLog>> queryPointLogByOrderNo(ServerHttpRequest request,
        @Parameter(name = "订单号") @RequestParam(value = "orderNo") String orderNo) {
        log.info(LoggerHelper2.formatEnterLog(request) + "查询账户列表 orderNo: {}"
            , orderNo);

        List<Long> commIdList = this.getCommIdList2(request);
        if (CollectionUtils.isEmpty(commIdList)) {
            throw new DcArgumentException("参数错误");
        }
        Long topCommId = AntRestUtils.getTopCommId(request);
        if (topCommId == null || topCommId.longValue() < 1L) {
            log.error("参数错误,缺少参数topCommId");
            throw new DcArgumentException("参数错误,缺少参数topCommId");
        }

        Long commId = AntRestUtils.getCommId(request);
        List<String> gids = AntRestUtils.getSysUserGids(request);
        // CommercialSample comm = super.getCommercialSample(request);
        String commIdChain;
        if (CollectionUtils.isNotEmpty(gids)) {
            commIdChain = "";
        } else if (NumberUtils.equals(commId, DcBizConstants.superTopCommId)) {
            commIdChain = "";
        } else {
            commIdChain = AntRestUtils.getCommIdChain(request);
        }
//        ListResponse<PointLog> pointLogListResponse =
        return chargerOrderService.queryPointLogByOrderNo(topCommId, orderNo, commIdChain, commId,
            gids);

//        return pointLogListResponse;
    }

    @Operation(summary = "确认是否存在异常订单未加入到指定账单")
    @PostMapping(value = "/api/order/notInSettlementOrders")
    public Mono<ObjectResponse<Integer>> notInSettlementOrders(
        ServerHttpRequest request, @RequestBody NotInSettlementOrderParam param) {
        log.info("确认是否存在异常订单未加入到指定账单: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return chargerOrderService.notInSettlementOrders(param);
    }

    @Operation(summary = "获取企业客户的充电订单列表")
    @PostMapping(value = "/api/order/getNotSettlementOrderList")
    public ListResponse<SettlementOrderVo> getNotSettlementOrderList(
        ServerHttpRequest request, @RequestBody ListSettlementOrderParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        return chargerOrderService.getNotSettlementOrderList(param);
    }

    @GetMapping(value = "/api/order/payNoCardOrder")
    @Operation(summary = "尝试结算该场站下所有未支付订单")
    public BaseResponse payNoCardOrder(
        @Parameter(name = "场站id") @RequestParam(value = "siteId") String siteId) {
        log.info("尝试结算该场站下所有未支付订单: siteId = {}", siteId);
        IotAssert.isNotBlank(siteId, "场站id不能为空");
        return this.chargerOrderService.payNoCardOrderBySiteId(siteId);
    }


    @PostMapping("/api/order/getLowKwOrderList")
    @Operation(summary = "查询小电流充电订单列表")
    public ListResponse<LowKwOrderDto> getLowKwOrderList(ServerHttpRequest request,
        @RequestBody ListChargeOrderParam param) {
        log.info(LoggerHelper2.formatEnterLog(request) + "param = {}", param);

        List<String> gids = AntRestUtils.getSysUserGids(request);
        if (CollectionUtils.isNotEmpty(gids)) {
            param.setGids(AntRestUtils.getSysUserGids(request));
        } else {
            param.setCommIdChain(AntRestUtils.getCommIdChain(request));
        }
        ListResponse<LowKwOrderDto> res = chargerOrderService.getLowKwOrderList(param);
        return res;
    }
}
