package com.cdz360.biz.ant.service.parts;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.parts.dto.PartsTypeDto;
import com.cdz360.biz.model.parts.param.ListPartsTypeParam;
import com.cdz360.biz.model.parts.vo.PartsTypeVo;
import com.cdz360.biz.utils.feign.iot.DevicePartsFeignClient;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class PartsTypeService {

    @Autowired
    private DevicePartsFeignClient devicePartsFeignClient;

    public Mono<ListResponse<PartsTypeVo>> findPartsType(ListPartsTypeParam param) {
        return devicePartsFeignClient.findPartsType(param);
    }

    public Mono<ObjectResponse<PartsTypeVo>> addPartsType(PartsTypeDto dto) {
        IotAssert.isNotBlank(dto.getCode(), "请输入物料编码");
        IotAssert.isNotBlank(dto.getName(), "请输入物料名称");
        IotAssert.isNotBlank(dto.getFullModel(), "请输入物料规格型号");
        return devicePartsFeignClient.addPartsType(dto);
    }
}
