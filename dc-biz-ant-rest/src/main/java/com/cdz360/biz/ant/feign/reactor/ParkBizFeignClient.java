package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.domain.park.type.ParkingLockStatus;
import com.cdz360.biz.ant.domain.park.vo.ParkChannelVo;
import com.cdz360.biz.ant.domain.park.vo.ParkingLockVo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_IOT_PARK,
        fallbackFactory = ParkBizFeignHystrix.class)
public interface ParkBizFeignClient {

    // 获取场站道闸信息列表
    @GetMapping(value = "/inner/channel/getParkChannelBySiteId")
    Mono<ListResponse<ParkChannelVo>> getParkChannelBySiteId(@RequestParam(value = "siteId") String siteId);

    // 开闸操作
    @GetMapping(value = "/inner/channel/upLiftRod")
    Mono<ObjectResponse<Boolean>> upLiftRod(@RequestParam(value = "id") Long id);

    // 开闭锁
    @GetMapping(value = "/park/parkingLock/switchLock")
    Mono<BaseResponse> switchLock(
            @RequestParam(value = "lockId") Long lockId, @RequestParam(value = "open") Boolean open);

    // 远程断电锁(断电以防水淹事故)
    @GetMapping(value = "/park/parkingLock/cutPower")
    Mono<BaseResponse> cutPower(@RequestParam(value = "lockId") Long lockId);

    // 远程重启锁
    @GetMapping(value = "/park/parkingLock/rebootLock")
    Mono<BaseResponse> rebootLock(@RequestParam(value = "lockId") Long lockId);

    // 向地锁云查询地锁
    @GetMapping(value = "/park/parkingLock/lookForLock")
    Mono<ObjectResponse<ParkingLockVo>> lookForLock(
            @RequestParam(value = "siteId") String siteId, @RequestParam(value = "remoteLockId") String remoteLockId);

    // 同步场站下地锁信息
    @GetMapping(value = "/park/parkingLock/fetchLockInfo")
    Mono<BaseResponse> fetchLockInfo(@RequestParam(value = "siteId") String siteId);

    // 锁状态查询/地锁状态同步
    @GetMapping(value = "/park/parkingLock/lockStatusInTime")
    Mono<ObjectResponse<ParkingLockStatus>> lockStatusInTime(@RequestParam(value = "lockId") Long lockId);
}
