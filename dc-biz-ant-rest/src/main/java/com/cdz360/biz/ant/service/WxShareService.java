package com.cdz360.biz.ant.service;


import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.utils.RedisUtil;
import com.cdz360.biz.model.trading.coupon.vo.ShareVo;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.security.MessageDigest;
import java.time.Instant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @version 1.0
 * @since 2018/11/20
 */
@Slf4j
@Service
public class WxShareService {


    /**
     * 请求配置信息
     */
    private static final RequestConfig config = RequestConfig.custom()
        .setSocketTimeout(10000)
        .setConnectTimeout(30000)
        .build();
    /**
     * 请求客户端
     */
    private static final CloseableHttpClient client = HttpClients.createDefault();
    /**
     * 定义环境
     */
    String env = "test";
    /**
     * 正式appId
     */
    String appId = "wxdada70e851e60c7d";
    /**
     * 正式secret
     */
    String secret = "ebcf37e8a98af26b50efd42fc0cf5583";
    /**
     * 测试appId
     */
    String testAppId = "wx0b3b281871c52516";
    /**
     * 测试secret
     */
    String testSecret = "cf3839c1a2110b2d1a103b09a5ffb68e";
    String ticket = "jsapi_ticket";
    @Autowired
    private RedisUtil redisUtil;

    /**
     * sha1加密
     *
     * @param str
     * @return
     */
    public static String getSha1(String str) {

        char hexDigits[] = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9',
            'a', 'b', 'c', 'd', 'e', 'f'};
        try {
            MessageDigest mdTemp = MessageDigest.getInstance("SHA1");
            mdTemp.update(str.getBytes("UTF-8"));
            byte[] md = mdTemp.digest();
            int j = md.length;
            char buf[] = new char[j * 2];
            int k = 0;
            for (int i = 0; i < j; i++) {
                byte byte0 = md[i];
                buf[k++] = hexDigits[byte0 >>> 4 & 0xf];
                buf[k++] = hexDigits[byte0 & 0xf];
            }
            return new String(buf);
        } catch (Exception e) {
            return null;
        }
    }

    public ShareVo getShareInfo(String url) {
        if (StringUtils.isBlank(url)) {
            throw new DcServiceException("当前页面请求地址不能为空");
        }

        //测试环境
        if (url.contains(env)) {
            appId = testAppId;
            secret = testSecret;
        }
        ShareVo shareInfo = new ShareVo();
        shareInfo.setAppId(appId);
        shareInfo.setTimeStamp(Instant.now().getEpochSecond());
        String jsApiTicket = redisUtil.get(ticket);
        shareInfo.setUrl(url);
        shareInfo.setNonceStr(RandomStringUtils.randomAlphanumeric(10));
        log.info("账号信息：appId={},secret={}", appId, secret);
        if (StringUtils.isBlank(jsApiTicket)) {
            try {
                //获取access_token
                String getTokenUrl =
                    "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid="
                        + appId + "&secret=" + secret;
                JsonNode result = sendGet(getTokenUrl);
                if (result != null) {
                    String access_token = result.get("access_token").asText();
                    //获取ticket
                    String getTickUrl =
                        "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token="
                            + access_token + "&type=jsapi";
                    JsonNode ret = sendGet(getTickUrl);
                    jsApiTicket = ret.get("ticket").asText();
                    redisUtil.put(ticket, jsApiTicket, 7200);
                } else {
                    throw new DcServiceException("获取access_token失败");
                }
            } catch (Exception e) {
                log.info("信息获取失败", e.getMessage(), e);
            }
        }
        String sign =
            "jsapi_ticket=" + jsApiTicket + "&noncestr=" + shareInfo.getNonceStr() + "&timestamp="
                + shareInfo.getTimeStamp() + "&url=" + url;
        shareInfo.setSignature(getSha1(sign));
        log.info("请求数据返回:ticket={},shareInfo={}", jsApiTicket, shareInfo);
        return shareInfo;
    }

    /**
     * 发送get请求
     *
     * @param url
     * @return
     */
    public JsonNode sendGet(String url) {
        try {
            URL requestUrl = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) requestUrl.openConnection();
            connection.setRequestMethod("GET");
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.connect();
            InputStream inputStream = connection.getInputStream();
            int size = inputStream.available();
            byte[] bs = new byte[size];
            inputStream.read(bs);
            String message = new String(bs, "UTF-8");
            JsonNode jsonObject = JsonUtils.fromJson(message);
            return jsonObject;
        } catch (Exception e) {
            log.info("请求失败", e.getMessage(), e);
            return null;
        }
    }
}
