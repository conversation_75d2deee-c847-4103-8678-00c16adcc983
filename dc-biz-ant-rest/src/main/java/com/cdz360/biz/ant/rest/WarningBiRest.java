package com.cdz360.biz.ant.rest;


import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.service.WarningBiService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.ess.model.dto.EssEquipAlarmLangDto;
import com.cdz360.biz.model.trading.bi.param.WarningSummaryParam;
import com.cdz360.biz.model.trading.bi.warning.StopCodeDto;
import com.cdz360.biz.model.trading.bi.warning.WarningSummaryDto;
import com.cdz360.biz.model.trading.site.dto.SiteSimpleDto;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.chargerlinkcar.framework.common.domain.param.OrderStopBiParam;
import com.chargerlinkcar.framework.common.domain.vo.OrderStopBiVo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * 告警统计相关接口
 */
@Slf4j
@RestController
@RequestMapping("/api/warning")
@Tag(name = "告警统计相关接口", description = "告警统计")
public class WarningBiRest extends BaseController {

    @Autowired
    private WarningBiService warningBiService;
    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Operation( summary = "")
    @RequestMapping(value = "/queryWarningList", method = RequestMethod.POST)
    public ObjectResponse queryWarningList(ServerHttpRequest request) {
        return warningBiService.queryWarningList();
    }

    @Operation(summary = "根据条件获取不同设备类型、软件版本")
    @GetMapping(value = "/getEvseModelOrFirm")
    public ListResponse<String> getEvseModelOrFirm(@RequestParam("type") String type) {
        if (StringUtils.isBlank(type)) {
            throw new DcServiceException("类型不能为空");
        }
        return warningBiService.getEvseModelOrFirm(type);

    }

    @Operation(summary = "获取停充原因列表")
    @GetMapping(value = "/getOrderStopCodeList")
    public ListResponse<StopCodeDto> getOrderStopCodeList() {
        return warningBiService.getOrderStopCodeList();

    }

    /**
     * 根据条件获取告警分类列表
     * @param request
     * @param param
     * @return
     */
    @PostMapping(value = "/getWarningSummaryList")
    public ListResponse<WarningSummaryDto> getWarningSummaryList(ServerHttpRequest request,
                                                                 @RequestBody WarningSummaryParam param) {
        log.info("param: {}", JsonUtils.toJsonString(param));
        List<String> gids = AntRestUtils.getSysUserGids(request);
        List<String> siteIdList;
        // 场站组
        if (CollectionUtils.isNotEmpty(gids)) {
            ListSiteParam siteParams = new ListSiteParam().setGids(gids);
            ListResponse<String> response = dataCoreFeignClient.getSiteListByGids(siteParams);
            FeignResponseValidate.check(response);
            siteIdList = response.getData();
        } else { // 商户层级
            ListResponse<SiteSimpleDto> list = dataCoreFeignClient.getSiteListByIdChain(super.getCommIdChain2(request));
            FeignResponseValidate.check(list);
            siteIdList = CollectionUtils.isNotEmpty(list.getData()) ? list.getData().stream().map(SiteSimpleDto::getSiteId).collect(Collectors.toList()) : null;
        }
        param.setSiteIdList(CollectionUtils.isEmpty(param.getSiteIdList()) ? siteIdList : param.getSiteIdList());
        //当前登录商户及子商户站点集合
//        if (CollectionUtils.isEmpty(param.getSiteIdList())) {
//            String idChain = super.getCommIdChain2(request);
//            if (StringUtils.isBlank(idChain)) {
//                throw new DcServiceException("商户信息有误");
//            }
//            ListResponse<SiteSimpleDto> list = dataCoreFeignClient.getSiteListByIdChain(idChain);
//            List<String> siteIdList = list.getData().stream().map(SiteSimpleDto::getSiteId).collect(Collectors.toList());
//            param.setSiteIdList(siteIdList);
//        }
        return warningBiService.getWarningSummaryList(param);
    }

    @PostMapping(value = "/getOrderStopSummaryList")
    public ListResponse<OrderStopBiVo> getOrderStopSummaryList(ServerHttpRequest request,
                                                               @RequestBody OrderStopBiParam param) {
        log.info("param: {}", JsonUtils.toJsonString(param));
        String idChain = super.getCommIdChain2(request);
        if (StringUtils.isBlank(idChain)) {
            throw new DcServiceException("商户信息有误");
        }
        param.setIdChain(idChain);
        return warningBiService.getOrderStopSummaryList(param);
    }

    @GetMapping(value = "/getAlarmList")
    public ListResponse<EssEquipAlarmLangDto> getAlarmList(ServerHttpRequest request) {
        Locale locale = AntRestUtils.getLocale(request);
        IotAssert.isTrue(locale!=null,  "请选择语言");
        return warningBiService.getAlarmList(locale);
    }
}
