package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.charge.vo.SiteVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.feign.reactor.ReactorOpenHlhtFeignClient;
import com.cdz360.biz.ant.service.ContractService;
import com.cdz360.biz.ant.service.sysLog.CommercialSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.trading.contract.dto.ContractTypeDto;
import com.cdz360.biz.model.trading.contract.param.AddContractParam;
import com.cdz360.biz.model.trading.contract.param.ContractListParam;
import com.cdz360.biz.model.trading.contract.type.ContractType;
import com.cdz360.biz.model.trading.contract.vo.ContractVo;
import com.cdz360.biz.model.trading.hlht.po.PartnerPo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;


/**
 * 合约管理
 */
@Tag(name = "合约管理相关接口")
@Slf4j
@RestController
@RequestMapping("/api/contract")
public class ContractRest extends BaseController {

    @Autowired
    private ContractService contractService;
    @Autowired
    private CommercialSysLogService commercialSysLogService;
    @Autowired
    private ReactorOpenHlhtFeignClient reactorOpenHlhtFeignClient;

    /**
     * 添加合约
     *
     * @param param
     * @return
     */
    @PostMapping("/addContract")
    public BaseResponse addContract(ServerHttpRequest request,
                                    @RequestBody AddContractParam param) {
        log.debug("param = {}", JsonUtils.toJsonString(param));
        String idChain = AntRestUtils.getCommIdChain(request);
        IotAssert.isNotBlank(idChain, "无法获取idChain");
        param.setIdChain(idChain);
        param.setSysUid(AntRestUtils.getSysUid(request));
        // 互联互通合约客户名称取支撑平台客户侧
        if (param.getContractType().equals(ContractType.CON_C12)) {
            ObjectResponse<PartnerPo> block = reactorOpenHlhtFeignClient.getDetailByCode(param.getOperatorCode())
                .block(Duration.ofSeconds(30L));
            if (block != null && block.getData() != null) {
                param.setCustomerName(block.getData().getName());
            } else {
                param.setCustomerName("");
            }
        }
        commercialSysLogService.addContractLog(param.getContractName(), param.getCustomerName(), request);
        return contractService.addContract(param);
    }

    @GetMapping("/delContract")
    public BaseResponse delContract(ServerHttpRequest request,
                                    @RequestParam(value = "contractName") String contractName,
                                    @RequestParam(value = "customerName") String customerName,
                                    @RequestParam(value = "id") Long id) {

        // 34474顶级商户下忽略
        String idChain = AntRestUtils.getCommIdChainAndFilter(request);

        BaseResponse response = contractService.delContract(id, idChain, AntRestUtils.getSysUid(request));
        if (response.getStatus() == 0) {
            commercialSysLogService.delContractLog(contractName, customerName, request);
        }
        return response;
    }

    /**
     * 更新合约
     *
     * @param param
     * @return
     */
    @PostMapping("/updateContract")
    public BaseResponse updateContract(ServerHttpRequest request,
                                       @RequestBody AddContractParam param) {
        log.debug("param = {}", JsonUtils.toJsonString(param));

        // 34474顶级商户下忽略权限
        String idChain = AntRestUtils.getCommIdChainAndFilter(request);
        param.setIdChain(idChain);
        param.setSysUid(AntRestUtils.getSysUid(request));

        // 互联互通合约客户名称取支撑平台客户侧
        if (param.getContractType().equals(ContractType.CON_C12)) {
            ObjectResponse<PartnerPo> block = reactorOpenHlhtFeignClient.getDetailByCode(param.getOperatorCode())
                .block(Duration.ofSeconds(50L));
            if (block != null && block.getData() != null) {
                param.setCustomerName(block.getData().getName());
            } else {
                param.setCustomerName("");
            }
        }
        commercialSysLogService.editContractLog(param.getContractName(), param.getCustomerName(), request);
        return contractService.updateContract(param);
    }

    /**
     * 合约详情
     *
     * @param contractId
     * @return
     */
    @GetMapping("/getContractById")
    public ObjectResponse<ContractVo> getContractById(ServerHttpRequest request,
                                                      @RequestParam("contractId") Long contractId) {
        log.debug("contractId = {}", contractId);
        // 34474顶级商户下忽略
        String idChain = AntRestUtils.getCommIdChainAndFilter(request);
        IotAssert.isNotNull(contractId, "请传入合约ID");

        return contractService.getContractById(contractId, idChain);
    }

    /**
     * 合约列表
     *
     * @param param
     * @return
     */
    @PostMapping("/getContractList")
    public ListResponse<ContractVo> getContractList(ServerHttpRequest request,
                                                    @RequestBody ContractListParam param) {

        // 34474 顶级商户下忽略全选
        String idChain = AntRestUtils.getCommIdChainAndFilter(request);
        param.setIdChain(idChain);

        return contractService.getContractList(param);
    }

    /**
     * 合约类型集合
     *
     * @param request
     * @return
     */
    @GetMapping("/getContractTypeList")
    public ListResponse<ContractTypeDto> getContractTypeList(ServerHttpRequest request) {
        List<ContractType> list = Arrays.asList(ContractType.values());
        List<ContractTypeDto> result = new ArrayList<>();
        list.forEach(e -> {
            ContractTypeDto contractTypeDto = new ContractTypeDto();
            contractTypeDto.setLabel(e.toString());
            contractTypeDto.setCode(e.getCode());
            contractTypeDto.setDesc(e.getDesc());
            result.add(contractTypeDto);
        });
        return new ListResponse<>(result);
    }

    /**
     * 场站最近若干条合约
     * 如果产站没有合约，则取场站所属商户上级没有指定产站的合约
     *
     * @return
     */
    @GetMapping("/getContractBySiteId")
    public Mono<ListResponse<ContractVo>> getContractBySiteId(@RequestParam(value = "siteId") String siteId,
                                                              @RequestParam(value = "size", required = false) Long size) {
        log.info("siteId={},size={}", siteId, size);
        return contractService.getContractBySiteId(siteId, size);
    }

    /**
     * 获取合约场站信息
     *
     * @param request
     * @param contractId
     * @return
     */
    @GetMapping("/getSiteListByContractId")
    public Mono<ListResponse<SiteVo>> getSiteListByContractId(ServerHttpRequest request,
                                                              @RequestParam(value = "contractId") Long contractId) {
        log.debug("contractId = {}", contractId);
        IotAssert.isNotNull(contractId, "合约ID不能为空");
        return contractService.getSiteListByContractId(contractId);
    }
}
