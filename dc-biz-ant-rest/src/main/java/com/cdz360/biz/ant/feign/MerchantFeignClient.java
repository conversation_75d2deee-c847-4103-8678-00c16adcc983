package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.domain.CustomUi;
import com.cdz360.biz.ant.domain.Notice;
import com.cdz360.biz.ant.domain.vo.BlocWalletVO;
import com.cdz360.biz.ant.domain.vo.NoticeVo;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.domain.BlocUserDto;
import com.chargerlinkcar.framework.common.domain.vo.SiteDebitAccountVo;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 商户操作的FeignClient
 *
 * <AUTHOR> 商户操作的FeignClient
 * @since 2018.11.20
 */
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_USER, fallbackFactory = MerchantFeignClientFHystrixactory.class)
public interface MerchantFeignClient {

    /**
     * 场站后台充电扣款账户-所属商户下拉
     *
     * @param vo
     * @return
     */
    @PostMapping(value = "/api/merchant/getSiteDebitCommIdList")
    ListResponse<Commercial> getSiteDebitCommIdList(@RequestBody SiteDebitAccountVo vo);

    /**
     * 保存界面设置
     *
     * @param token
     * @param customUi 界面设置
     * @return
     */
    @RequestMapping(value = "/api/customUi/customUiSaveOrUpdate", method = RequestMethod.POST)
    BaseResponse customUiSaveOrUpdate(@RequestParam("token") String token,
        @RequestBody CustomUi customUi);

    /**
     * 根据条件查询界面设置
     *
     * @param customUi
     * @return
     */
    @RequestMapping(value = "/api/customUi/queryCustomUi", method = RequestMethod.POST)
    ObjectResponse<CustomUi> queryCustomUi(@RequestBody CustomUi customUi);

    /**
     * 根据id查询商户账户
     *
     * @param merchantId 商户账户id
     * @param token
     * @return
     */
    @RequestMapping(value = "/api/merchant/findMerchantById", method = RequestMethod.POST)
    ObjectResponse<JsonNode> findMerchantById(@RequestParam("merchantId") Long merchantId,
        @RequestParam("token") String token);

    /**
     * 禁用集团
     *
     * @param blocUserId
     * @return
     */
    @RequestMapping(value = "/api/blocUser/deleteBlocUserById", method = RequestMethod.GET)
    ObjectResponse<BlocUserDto> deleteBlocUserById(
        @RequestParam(value = "blocUserId") Long blocUserId);

    /**
     * 启用集团
     *
     * @param blocUserId
     * @return
     */
    @RequestMapping(value = "/api/blocUser/enableBlocUserById", method = RequestMethod.GET)
    ObjectResponse<BlocUserDto> enableBlocUserById(
        @RequestParam(value = "blocUserId") Long blocUserId);

    /**
     * 校验集团重复 暂对账户account/集团名称blocUserName做重复校验
     *
     * @param account
     * @param blocUserName
     * @return
     */
    @RequestMapping(value = "/api/blocUser/queryBlocUserByCondition", method = RequestMethod.GET)
    ObjectResponse<Integer> queryBlocUserByCondition(
        @RequestParam(value = "account") String account,
        @RequestParam(value = "blocUserName") String blocUserName);


    /**
     * 查询站点值班人数
     *
     * @param siteId 站点id
     * @return
     */
    @RequestMapping(value = "/api/siteDuty/getDutyCount")
    ObjectResponse<Integer> getDutyCount(@RequestParam("siteId") String siteId);

    /**
     * 查询站点值班表
     *
     * @param siteId 站点id
     * @return
     */
    @RequestMapping(value = "/api/siteDuty/getSiteDuty")
    ObjectResponse<JsonNode> getSiteDuty(@RequestParam("siteId") String siteId);

    /**
     * 保存站点值班表
     *
     * @param siteId   站点id
     * @param siteDuty json格式值班列表
     * @return
     */
    @RequestMapping(value = "/api/siteDuty/saveSiteDuty")
    BaseResponse saveSiteDuty(@RequestParam("siteId") String siteId,
        @RequestParam("siteDuty") String siteDuty);

    /**
     * 查询站点公告
     *
     * @param notice
     * @return
     */
    @RequestMapping(value = "/api/siteNotice/queryNoticeBySite")
    ListResponse<Notice> queryNoticeBySite(@RequestBody NoticeVo notice,
        @RequestParam("_size") Integer _size, @RequestParam("_index") Integer _index);

    /**
     * 保存站点公告
     *
     * @param notice
     * @return
     */
    @RequestMapping(value = "/api/siteNotice/addNotice")
    BaseResponse addNotice(Notice notice);


    @RequestMapping(value = "/api/blocUser/getBlocUserByBlocUserId", method = RequestMethod.GET)
    ObjectResponse<BlocWalletVO> getBlocUserByBlocUserId(
        @RequestParam(value = "blocUserId") Long blocUserId);


}
