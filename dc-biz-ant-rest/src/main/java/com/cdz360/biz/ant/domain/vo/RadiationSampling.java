package com.cdz360.biz.ant.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "辐射值采样点")
@Data
@Accessors(chain = true)
public class RadiationSampling {

    @Schema(description = "采样点时间", example = "01:00")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String time;

    @Schema(description = "采样点辐射值(单位: W/㎡)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer radiation;
}
