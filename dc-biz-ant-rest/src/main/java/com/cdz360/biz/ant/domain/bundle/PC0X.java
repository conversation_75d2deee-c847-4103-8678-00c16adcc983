package com.cdz360.biz.ant.domain.bundle;

import lombok.Data;

import java.util.List;


/**
 * PC0X
 *  版本支持描述
 * <EMAIL>
 * <AUTHOR> 
 * @since  2019/9/16 8:52
 */
@Data
public class PC0X {
    /**
     * type: PC01,PC01_1,PC01_2,PC02,PC03
     * hw : 104
     * sw : 15
     * oder : 11
     * adaptiveList : {"hw":[101,102],"sw":[9,13,14],"oder":[13]}
     */
    private String binName;
    private String type;
    private Integer hw;
    private Integer sw;
    private Integer order;
    private Adaptive adaptive;

    @Data
    public static class Adaptive {
        private List<Integer> hw;
        private List<Integer> sw;
        private List<Integer> order;
    }
}
