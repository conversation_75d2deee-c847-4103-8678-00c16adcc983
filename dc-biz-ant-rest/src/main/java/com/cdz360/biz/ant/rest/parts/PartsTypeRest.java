package com.cdz360.biz.ant.rest.parts;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.service.parts.PartsTypeService;
import com.cdz360.biz.model.parts.dto.PartsTypeDto;
import com.cdz360.biz.model.parts.param.ListPartsTypeParam;
import com.cdz360.biz.model.parts.vo.PartsTypeVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@Tag(name = "物料规格相关接口", description = "物料规格")
@RestController
@RequestMapping("/api/parts")
public class PartsTypeRest {

    @Autowired
    private PartsTypeService partsTypeService;

    @Operation(summary = "物料规格列表")
    @PostMapping(value = "/findPartsType")
    public Mono<ListResponse<PartsTypeVo>> findPartsType(@RequestBody ListPartsTypeParam param) {
        log.info("物料规格列表: param = {}", JsonUtils.toJsonString(param));
        return partsTypeService.findPartsType(param);
    }

    @Operation(summary = "新增物料规格")
    @PostMapping(value = "/addPartsType")
    public Mono<ObjectResponse<PartsTypeVo>> addPartsType(@RequestBody PartsTypeDto dto) {
        log.info("新增物料规格: dto = {}", JsonUtils.toJsonString(dto));
        return partsTypeService.addPartsType(dto);
    }
}
