package com.cdz360.biz.ant.rest.site;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.ant.domain.Notice;
import com.cdz360.biz.ant.domain.vo.NoticeVo;
import com.cdz360.biz.ant.service.site.SiteNoticeService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.rest.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;

/**
 * 站点公告
 * <p>
 * SiteNoticeRest
 *
 * <AUTHOR>
 * @since 2019.2.18
 */
@Slf4j
@RestController
@RequestMapping("/api/siteNotice")
public class SiteNoticeRest extends BaseController {

    @Autowired
    private SiteNoticeService siteNoticeService;

    /**
     * 查询站点公告
     *
     * @param notice siteId    站点编号,commId    商户主键,startTime 查询开始时间, endTime   查询结束时间,content
     *               查询内容关键字
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/queryNoticeBySite")
    public ListResponse<Notice> queryNoticeBySite(
        ServerWebExchange exh,
        @RequestBody NoticeVo notice, ServerHttpRequest request) {
        //分页对象
        OldPageParam page = getPage2(request, exh, true);

        return siteNoticeService.queryNoticeBySite(notice, page);
    }


    /**
     * 新增站点公告
     *
     * @param notice 站点公告
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/addNotice")
    public BaseResponse addNotice(@RequestBody Notice notice, ServerHttpRequest request) {

        //获取当前用户token
        String token = getToken2(request);

        return siteNoticeService.addNotice(notice, token, AntRestUtils.getCommId(request));
    }

}
