package com.cdz360.biz.ant.domain.dto;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 储能设备 -- 电池堆
 */
@Data
public class BatteryStack {
    // 允许最大充电电流, 单位: 0.1A
    @JsonProperty("maxICA")
    private BigDecimal maxInCurrentAllowed;
    // 允许最大放电电流, 单位: 0.1A
    @JsonProperty("maxOCA")
    private BigDecimal maxOutCurrentAllowed;
    /**
     * Bit0:0 forbidden charge 1 allowed charge
     * Bit1:0 did not apply for strong charge 1 applied for strong charge
     * Bit2:0 no release 1 allowed
     */
    // 允许充放电标志位
    @JsonProperty("aiom")
    private Integer allowsInOutMarker;
    // 电池总电压, 单位: 0.1 V
    @JsonProperty("bvt")
    private BigDecimal batteryVoltageTotal;
    // 电池总电流, 单位: 0.1 A
    @JsonProperty("bct")
    private BigDecimal batteryCurrentTotal;
    // 绝缘阻抗, 单位: kΩ
    @JsonProperty("ir")
    private Integer insulatedResistance;
    //电池剩余电量, 单位: 0.1%
    @JsonProperty("soc")
    private BigDecimal soc;
    //电池健康状态, 单位: 0.1%
    @JsonProperty("soh")
    private BigDecimal soh;
    // 系统簇数量
    @JsonProperty("spcn")
    private Integer sysParallelClustersNum;
    // 簇LMU个数
    @JsonProperty("cln")
    private Integer clustersLmuNum;
    // 一节电池容量, 单位: 0.01kWh
    @JsonProperty("obc")
    private BigDecimal oneBatteryCapacity;
    // EMS和BMS通讯状态
    @JsonProperty("ebcs")
    private Integer emsBmsCommunicationStatus;
    // 工作簇数量
    @JsonProperty("wcn")
    private Integer workClusterNum;
    // 最高极柱温度, 单位: 0.1℃
    @JsonProperty("mpt")
    private BigDecimal maxPoleTemperature;
    // 单体最低电压, 单位: 0.001V
    @JsonProperty("minMV")
    private BigDecimal minMonomerVoltage;
    // 单体最低电压簇号
    @JsonProperty("minMVClstN")
    private Integer minMonomerVoltageClusterNo;
    // 单体最低电压电池号
    @JsonProperty("minMVBN")
    private Integer minMonomerVoltageBatteryNo;
    // 单体最低电压电芯号
    @JsonProperty("minMVCellN")
    private Integer minMonomerVoltageCellNo;
    // 单体最高电压, 单位: 0.001V
    @JsonProperty("maxMV")
    private BigDecimal maxMonomerVoltage;
    // 单体最高电压簇号
    @JsonProperty("maxMVClstN")
    private Integer maxMonomerVoltageClusterNo;
    // 单体最高电压电池号
    @JsonProperty("maxMVBN")
    private Integer maxMonomerVoltageBatteryNo;
    // 单体最高电压电芯号
    @JsonProperty("maxMVCellN")
    private Integer maxMonomerVoltageCellNo;
    // 电池最低温度, 单位: 0.1℃
    @JsonProperty("minBT")
    private BigDecimal minBatteryTemperature;
    // 单体最低温度簇号
    @JsonProperty("minTClstN")
    private Integer minTemperatureClusterNo;
    // 单体最低温度电池号
    @JsonProperty("minTBN")
    private Integer minTemperatureBatteryNo;
    // 单体最低温度电芯号
    @JsonProperty("minTCellN")
    private Integer minTemperatureCellNo;
    // 单体最高温度, 单位: 0.1℃
    @JsonProperty("maxBT")
    private BigDecimal maxBatteryTemperature;
    // 单体最高温度簇号
    @JsonProperty("maxTClstN")
    private Integer maxTemperatureClusterNo;
    // 单体最高温度电池号
    @JsonProperty("maxTBN")
    private Integer maxTemperatureBatteryNo;
    // 单体最高温度电芯号
    @JsonProperty("maxTCellN")
    private Integer maxTemperatureCellNo;


    // BMS故障信息,
    @JsonProperty("bfil")
    private List<Integer> bmsFaultInfoList;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}
