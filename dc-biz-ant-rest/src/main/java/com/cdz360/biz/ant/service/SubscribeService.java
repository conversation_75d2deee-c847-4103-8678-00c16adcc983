package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.auth.subscribe.param.AddSubscribeParam;
import com.cdz360.biz.auth.subscribe.param.CreatePayOrderParam;
import com.cdz360.biz.auth.subscribe.param.SubLogListParam;
import com.cdz360.biz.auth.subscribe.param.SubscribeListParam;
import com.cdz360.biz.auth.subscribe.po.SubscribePo;
import com.cdz360.biz.auth.subscribe.vo.*;
import com.cdz360.biz.auth.sys.vo.SysRoleSimpleVo;
import com.cdz360.biz.model.sys.vo.SubscribeOrderVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

@Slf4j
@Service
public class SubscribeService {

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    public BaseResponse add(AddSubscribeParam params) {
        return authCenterFeignClient.add(params);
    }

    public BaseResponse update(AddSubscribeParam params) {
        return authCenterFeignClient.update(params);
    }

    public BaseResponse updateStatus(Long sysUid, Long subId) {
        return authCenterFeignClient.updateStatus(sysUid, subId);
    }

    public ListResponse<SubscribeVo> getList(SubscribeListParam params) {
        return authCenterFeignClient.getList(params);
    }

    public ListResponse<CommVo> getCommList(Long subId) {
        return authCenterFeignClient.getCommList(subId);
    }

    public ListResponse<SysRoleSimpleVo> getRoleList(Long subId) {
        return authCenterFeignClient.getRoleList(subId);
    }

    public ObjectResponse<SubscribeDetailVo> getDetail(Long subId) {
        return authCenterFeignClient.getDetail(subId);
    }

    public ListResponse<SubscribeDetailVo> getListByUser(String token, Boolean status) {
        return authCenterFeignClient.getListByUser(token, status);
    }


    public ObjectResponse<String> createPayOrder(CreatePayOrderParam params) {
        return authCenterFeignClient.createPayOrder(params);
    }

    public ObjectResponse<SubscribeOrderVo> getOrderById(String payNo) {
        return authCenterFeignClient.getOrderById(payNo);
    }

    public ListResponse<SubscribeLogVo> getSubLogList(SubLogListParam params) {
        return authCenterFeignClient.getSubLogList(params);
    }

    public ListResponse<SubscribeOrderDetailVo> getRoleListByPayNo(String payNo) {
        return authCenterFeignClient.getRoleListByPayNo(payNo);
    }

    public BaseResponse addNote(String payNo, String note) {
        return authCenterFeignClient.addNote(payNo, note);
    }


}
