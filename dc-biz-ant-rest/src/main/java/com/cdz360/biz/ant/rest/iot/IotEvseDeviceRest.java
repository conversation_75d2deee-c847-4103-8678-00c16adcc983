package com.cdz360.biz.ant.rest.iot;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.service.iot.IotEvseDeviceService;
import com.cdz360.biz.model.iot.param.AddEvseParam;
import com.cdz360.biz.model.iot.param.DeviceParam;
import com.cdz360.biz.model.iot.param.EditEvseParam;
import com.cdz360.biz.model.iot.param.FindDeviceParam;
import com.cdz360.biz.model.iot.vo.DeviceVo;
import com.cdz360.biz.model.iot.vo.EvseDeviceVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "桩器件相关接口", description = "桩器件")
public class IotEvseDeviceRest {

    @Autowired
    private IotEvseDeviceService service;


    /**
     *
     * @param evseNo
     * @param deviceName
     * @param start
     * @param size
     * @param siteAllEvse 尝试获取桩的关联场站下，所有桩器件信息
     * @return
     */
    @GetMapping("/api/evseDevice/getByEvseNo")
    public Mono<ListResponse<DeviceVo>> getByEvseNo(@RequestParam(value = "evseNo") String evseNo,
        @RequestParam(value = "deviceName", required = false) String deviceName,
        @RequestParam(value = "siteAllEvse",
            required = false,
            defaultValue = "false") Boolean siteAllEvse,
        @RequestParam(value = "start") Long start,
        @RequestParam(value = "size") Long size) {
        log.info("getByEvseNo evseNo: {}, deviceName: {}, siteAllEvse: {}, start: {}, size: {}",
            evseNo, deviceName, siteAllEvse, start, size);
        return service.getByEvseNo(evseNo, deviceName, siteAllEvse, start, size);
    }

    @PostMapping("/api/evseDevice/getByDeviceNo")
    public Mono<ObjectResponse<EvseDeviceVo>> getByDeviceNo(@RequestBody FindDeviceParam param) {
        log.info("getByDeviceNo deviceNo: {}, evseNoList.size: {}", param.getDeviceNo(), param.getEvseNoList().size());
        return service.getByDeviceNo(param);
    }

    @PostMapping("/api/evseDevice/addEvse")
    public Mono<BaseResponse> addEvse(@RequestBody AddEvseParam param) {
        log.info("evseDevice AddEvse param: {}", param);
        return service.addEvse(param);
    }

    @PostMapping("/api/evseDevice/editEvse")
    public Mono<BaseResponse> editEvse(@RequestBody EditEvseParam param) {
        log.info("evseDevice EditEvse param: {}", param);
        return service.editEvse(param);
    }

    @PostMapping("/api/evseDevice/addDevice")
    public Mono<BaseResponse> addDevice(@RequestBody DeviceParam param) {
        log.info("addDevice param: {}", param);
        return service.addDevice(param);
    }

    @PostMapping("/api/evseDevice/editDevice")
    public Mono<BaseResponse> editDevice(@RequestBody DeviceParam param) {
        log.info("editDevice param: {}", param);
        return service.editDevice(param);
    }
}
