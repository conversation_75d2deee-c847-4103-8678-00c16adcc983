package com.cdz360.biz.ant.service.site;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.feign.BizBiFeignClient;
import com.cdz360.biz.model.iot.param.SiteBiParam;
import com.cdz360.biz.model.site.dto.SiteGeoDto;
import com.cdz360.biz.model.trading.camera.param.ListCameraParam;
import com.cdz360.biz.model.trading.camera.po.CameraSitePo;
import com.cdz360.biz.model.trading.iot.dto.SiteDeviceBiDto;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.vo.SiteOrderBiVo;
import com.cdz360.biz.utils.feign.camera.CameraFeignClient;
import com.chargerlinkcar.framework.common.feign.AuthCenterFeignClient;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.feign.SiteDataCoreFeignClient;
import com.chargerlinkcar.framework.common.utils.DateUtils;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import java.time.Duration;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SiteBiService {

    @Autowired
    private CameraFeignClient cameraFeignClient;

    @Autowired
    private BizBiFeignClient bizBiFeignClient;

    @Autowired
    private SiteDataCoreFeignClient siteDataCoreFeignClient;

    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;


    /**
     * 返回场站列表,含城市,省份信息
     *
     * @param commIdChain
     * @param checkCamera
     * @return
     */
    public ListResponse<SiteOrderBiVo> getSiteGeoBiList(String commIdChain, List<String> gids,
        List<Integer> bizTypeList, Boolean checkCamera, Boolean includeGroup) {
        int size = 4999;
        ListSiteParam listSiteParam = new ListSiteParam();
        listSiteParam.setIncludedHlhtSite(false)
            .setBizTypeList(bizTypeList)
            .setCommIdChain(commIdChain)
            .setGids(gids)
            .setIncludeGroup(includeGroup)
            .setStart(0L)
            .setSize(size);
        ListResponse<SiteGeoDto> res = siteDataCoreFeignClient.getSiteGeoList(listSiteParam);
        FeignResponseValidate.check(res);

        SiteBiParam param = new SiteBiParam();
        param.setFromTime(DateUtils.toDate(LocalDate.now().minusDays(31)))
            .setToTime(new Date())
            .setGids(gids)
            .setCommIdChain(commIdChain)
            .setStart(0L)
            .setSize(size);
        ListResponse<SiteOrderBiVo> orderBiRes = this.bizBiFeignClient.getSiteBiList(param);
        FeignResponseValidate.check(orderBiRes);
        Map<String, SiteOrderBiVo> orderBiMap = orderBiRes.getData().stream()
            .collect(Collectors.toMap(SiteOrderBiVo::getSiteId, o -> o));

        if (Boolean.TRUE.equals(checkCamera)) {
            ListCameraParam listCameraParam = new ListCameraParam();
            listCameraParam.setSiteIdList(
                res.getData().stream().map(SiteGeoDto::getSiteId).distinct()
                    .collect(Collectors.toList()));
            return cameraFeignClient.getCameraSiteList(listCameraParam)
                .map(FeignResponseValidate::checkReturn)
                .map(cameraList -> {
                    Set<String> cameraSiteIdList = cameraList.stream().map(CameraSitePo::getSiteId)
                        .collect(Collectors.toSet());

                    List<SiteOrderBiVo> list = res.getData().stream()
                        .map(s -> {
                            SiteOrderBiVo vo = this.toSiteOrderBi(s);
                            SiteOrderBiVo bi = orderBiMap.get(s.getSiteId());
                            if (bi != null) {
                                vo.setOrderNum(bi.getOrderNum());
                            } else {
                                vo.setOrderNum(0L);
                            }

                            // 是否挂在摄像机
                            vo.setHasCamera(cameraSiteIdList.contains(s.getSiteId()));

                            return vo;
                        })
                        .collect(Collectors.toList());

                    return new ListResponse<>(list);
                }).block(Duration.ofSeconds(50L));
        }

        List<SiteOrderBiVo> list = res.getData().stream()
            .map(s -> {
                SiteOrderBiVo vo = this.toSiteOrderBi(s);
                SiteOrderBiVo bi = orderBiMap.get(s.getSiteId());
                if (bi != null) {
                    vo.setOrderNum(bi.getOrderNum());
                } else {
                    vo.setOrderNum(0L);
                }
                return vo;
            })
            .collect(Collectors.toList());

        return new ListResponse<>(list);
    }

    private SiteOrderBiVo toSiteOrderBi(SiteGeoDto s) {
        SiteOrderBiVo vo = new SiteOrderBiVo();
        vo.setSiteId(s.getSiteId())
            .setSiteName(s.getSiteName())
            .setSiteShortName(s.getSiteShortName())
            .setSiteLng(s.getSiteLng())
            .setSiteLat(s.getSiteLat())
            .setSiteAddress(s.getSiteAddress())
            .setCityPinyin(s.getCityPinyin())
            .setCityCode(s.getCityCode())
            .setCityName(s.getCityName())
            .setCityLng(s.getCityLng())
            .setCityLat(s.getCityLat())
            .setProvincePinyin(s.getProvincePinyin())
            .setProvinceCode(s.getProvinceCode())
            .setProvinceName(s.getProvinceName())
            .setProvinceLng(s.getProvinceLng())
            .setProvinceLat(s.getProvinceLat())
            .setCePower(s.getCePower())
            .setPvPower(s.getPvPower())
            .setEssCapacity(s.getEssCapacity())
            .setEssPower(s.getEssPower())
            .setSiteGroupList(s.getSiteGroupList());
        return vo;
    }

    /**
     * 获取近30天充电量前10的场站
     *
     * @return
     */
    public ListResponse<SiteOrderBiVo> getSiteBiList(SiteBiParam param,
        @Nullable String commIdChain) {
        param.setCommIdChain(commIdChain);
        if (param.getStartTime() != null && param.getFromTime() == null) {
            param.setFromTime(new Date(param.getStartTime()));
        }
        if (param.getEndTime() != null && param.getToTime() == null) {
            param.setToTime(new Date(param.getEndTime()));
        }
        ListResponse<SiteOrderBiVo> res = this.bizBiFeignClient.getSiteBiList(param);
        FeignResponseValidate.check(res);

        List<String> siteIdList = res.getData().stream().map(SiteOrderBiVo::getSiteId)
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(siteIdList)) {
            ListResponse<SiteDeviceBiDto> res2 = iotDeviceMgmFeignClient.getSiteDeviceBiList(
                siteIdList);
            FeignResponseValidate.check(res2);
            Map<String, SiteDeviceBiDto> map = res2.getData().stream()
                .collect(Collectors.toMap(SiteDeviceBiDto::getSiteId, o -> o));
            res.getData().stream().forEach(vo -> {
                SiteDeviceBiDto bi = map.get(vo.getSiteId());
                if (bi != null) {
                    vo.setPlugNum(bi.getPlugNum());
                }
            });
        }
        return RestUtils.buildListResponse(res.getData());
    }
}
