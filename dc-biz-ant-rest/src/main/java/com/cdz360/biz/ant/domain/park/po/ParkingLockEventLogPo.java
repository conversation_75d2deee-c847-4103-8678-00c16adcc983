package com.cdz360.biz.ant.domain.park.po;


import com.cdz360.biz.ant.domain.park.type.ParkingLockEventLogType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;


@Data

@Accessors(chain = true)

@Schema(description = "地锁事件日志")

public class ParkingLockEventLogPo {


    @Schema(description = "主键id")

    @NotNull(message = "id 不能为 null")

    private Long id;


    @Schema(description = "t_parking_lock.id")

    @NotNull(message = "parkingLockId 不能为 null")

    private Long parkingLockId;


    @Schema(description = "事件类型. 0未知,10闭锁,11开锁,20出车,21进车(车牌识别),30插枪,31开启充电,32充电完成,33拔枪")

    @NotNull(message = "eventType 不能为 null")

    private ParkingLockEventLogType eventType;


    @Schema(description = "车牌号(地锁)")

    @Size(max = 32, message = "carNo 长度不能超过 32")

    private String carNo;


    @Schema(description = "充电订单号(枪头)")

    @Size(max = 64, message = "orderNo 长度不能超过 64")

    private String orderNo;


    @Schema(description = "记录创建时间")
    @NotNull(message = "createTime 不能为 null")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;


}

