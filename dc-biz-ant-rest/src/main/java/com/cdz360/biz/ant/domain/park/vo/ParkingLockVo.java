package com.cdz360.biz.ant.domain.park.vo;

import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.biz.ant.domain.park.type.ParkingLockPartner;
import com.cdz360.biz.ant.domain.park.type.ParkingLockStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "地锁")
public class ParkingLockVo {

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long id;

    @Schema(description = "设备供应商: 未知(UNKNOWN)，安效停(ANNEFFI)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ParkingLockPartner partner;

    @Schema(description = "供应商地锁ID(交互使用字段)")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String serialNumber;

    @Schema(description = "车位编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String positionCode;

    @Schema(description = "地锁状态: 未知(UNKNOWN)，平台离线(PLATFORM_OFFLINE)，" +
        "正常空闲(NORMAL)，有车开锁状态(OPEN_CAR_IN)，无车开锁状态(OPEN_NOT_CAR)，故障(ERROR)，被远程断电(CUT_POWER)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ParkingLockStatus status;

    @Schema(description = "地锁类型")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String type;

    @Schema(description = "地锁电量(%)-直接供电是100%")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal electricQuantity;

    @Schema(description = "地锁所在场所ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String parkingLotId;

    @Schema(description = "地锁所在场所名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String parkingLotName;

    @Schema(description = "地锁所在场所的编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String parkingSpaceCode;

    @Schema(description = "桩编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String evseNo;

    @Schema(description = "充电枪ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer plugId;

    @Schema(description = "占用车牌号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String carNo;

    @Schema(description = "供应商地锁元数据")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String srcDetail;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    //    =========================

    @Schema(description = "最新地锁状态更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date statusUpdateTime;

    //    =========================
    @Schema(description = "充电桩名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String evseName;

    @Schema(description = "充电枪名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String plugName;

    @Schema(description = "枪头状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private PlugStatus plugStatus;

    @Schema(description = "充电枪充电中的订单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderNo;

    @Schema(description = "所属场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "所属场站名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;
}
