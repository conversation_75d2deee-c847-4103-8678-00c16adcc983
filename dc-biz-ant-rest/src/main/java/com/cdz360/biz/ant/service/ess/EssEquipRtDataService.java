package com.cdz360.biz.ant.service.ess;


import static java.time.temporal.ChronoField.DAY_OF_MONTH;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.domain.dto.BatteryCluster;
import com.cdz360.biz.ant.domain.dto.BatteryPackEx;
import com.cdz360.biz.ant.domain.dto.BatteryStack;
import com.cdz360.biz.ant.domain.dto.MgcAlert;
import com.cdz360.biz.ant.domain.dto.Pcs;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorIotWorkerFeignClient;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.ess.model.data.param.DataBiParam;
import com.cdz360.biz.ess.model.data.param.DayKwhParam;
import com.cdz360.biz.ess.model.data.vo.DaySiteEssRtDataBi;
import com.cdz360.biz.ess.model.data.vo.EquipSampleData;
import com.cdz360.biz.model.ess.dto.LineEssRtData;
import com.cdz360.biz.model.ess.vo.EssEquipVo;
import com.cdz360.biz.model.ess.vo.RedisEquipRtData;
import com.cdz360.biz.model.iot.param.ListCtrlParam;
import com.cdz360.biz.model.iot.param.SiteBiParam;
import com.cdz360.biz.model.iot.type.NetType;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import com.cdz360.biz.model.site.type.SiteCategory;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.trading.ess.param.EssEquipDetailParam;
import com.cdz360.biz.model.trading.ess.param.EssEquipDetailParamEx;
import com.cdz360.biz.model.trading.ess.param.MgcDetailParam;
import com.cdz360.biz.model.trading.ess.po.EssEquipPo;
import com.cdz360.biz.model.trading.ess.vo.DayEssDataBi;
import com.cdz360.biz.model.trading.ess.vo.EmsSampleData;
import com.cdz360.biz.model.trading.ess.vo.EquipPowerBiVo;
import com.cdz360.biz.model.trading.ess.vo.EssEquipDetailVo;
import com.cdz360.biz.model.trading.ess.vo.EssStatusBi;
import com.cdz360.biz.model.trading.ess.vo.PcsSampleData;
import com.cdz360.biz.model.trading.ess.vo.TotalEssDataBi;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import com.cdz360.biz.utils.feign.iot.DeviceFeignClient;
import com.cdz360.biz.utils.feign.iot.IotEssFeignClient;
import com.cdz360.biz.utils.feign.site.SiteDataCoreFeignClient;
import com.cdz360.biz.utils.service.OssArchiveService;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssEquipRtDataService {

    private static final DateTimeFormatter TIME_POINT_FORMATTER = DateTimeFormatter.ofPattern(
        "HH:mm");
    private static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern(
        "yyyy-MM-dd HH:mm:ss.SSS");

    @Autowired
    private IotEssFeignClient iotEssFeignClient;

    @Autowired
    private DeviceFeignClient deviceFeignClient;

    @Autowired
    private ReactorIotWorkerFeignClient reactorIotWorkerFeignClient;
    @Autowired
    private OssArchiveService ossArchiveService;

    @Autowired
    private SiteDataCoreFeignClient siteDataCoreFeignClient;

    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;

    @Autowired
    private RedisEssEquipRtDataService redisEssEquipRtDataService;

    public Mono<ListResponse<DayEssDataBi>> essOutBi7(String siteId) {
        if (StringUtils.isBlank(siteId)) {
            throw new DcArgumentException("场站ID无效");
        }

        return deviceFeignClient.siteRtData7Day(siteId)
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .flatMapMany(Flux::fromIterable)
            .collect(Collectors.toMap(DayEssDataBi::getDate, o -> o))
            .flatMap(dateMap -> Flux.fromIterable(
                    DateUtil.rangeDate(LocalDate.now().minusDays(7L), LocalDate.now()))
                .map(date -> {
                    DayEssDataBi result = dateMap.get(date);
                    if (result == null) {
                        result = new DayEssDataBi();
                        result.setDate(date);
                    }
                    return result;
                })
                .collectList())
            .map(RestUtils::buildListResponse);
    }

    public Mono<ListResponse<DayEssDataBi>> essInOutBi30(String commIdChain, String siteId,
        Integer recentDays) {

        if (null == recentDays) {
            recentDays = 30;
        }

        Date toDate = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(toDate);
        calendar.add(Calendar.DAY_OF_MONTH, -recentDays);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        TimeFilter timeFilter = new TimeFilter()
            .setStartTime(calendar.getTime())
            .setEndTime(toDate);
        DayKwhParam param = new DayKwhParam()
            .setCommIdChain(commIdChain)
            .setDate(timeFilter); // 30 天范围

        if (StringUtils.isNotBlank(siteId)) {
            param.setSiteIdList(List.of(siteId));
        }

        return deviceFeignClient.siteDayOfRangeKwh(param)
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .flatMapMany(Flux::fromIterable)
            .collect(Collectors.toMap(DaySiteEssRtDataBi::getDate, o -> o, (o1, o2) -> {
                o1.setProfit(DecimalUtils.add(o1.getProfit(), o2.getProfit()));
                o1.setInFee(DecimalUtils.add(o1.getInFee(), o2.getInFee()));
                o1.setInKwh(DecimalUtils.add(o1.getInKwh(), o2.getInKwh()));
                o1.setOutFee(DecimalUtils.add(o1.getOutFee(), o2.getOutFee()));
                o1.setOutKwh(DecimalUtils.add(o1.getOutKwh(), o2.getOutKwh()));
                return o1;
            }))
            .flatMap(dateMap -> Flux.fromIterable(
                    DateUtil.rangeDate(
                        DateUtil.dateToLocalDate(calendar.getTime()),
                        DateUtil.dateToLocalDate(toDate)))
                .map(date -> {
                    DayEssDataBi result = new DayEssDataBi();
                    DaySiteEssRtDataBi data = dateMap.get(date);
                    if (data != null) {
                        BeanUtils.copyProperties(data, result);
                    }
                    return result.setDate(date);
                })
                .collectList())
            .map(RestUtils::buildListResponse);
    }

    public Mono<TotalEssDataBi> essTotalBi(DayKwhParam param) {
        ListSiteParam request = new ListSiteParam();
        request.setCommIdChain(param.getCommIdChain())
            .setSiteCategory(SiteCategory.ESS)
            .setSiteIdList(param.getSiteIdList())
            .setStart(0L)
            .setSize(999);

        // TODO: 暂时查询列表来统计
        return this.siteDataCoreFeignClient.getSiteVoList(request)
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .filter(CollectionUtils::isNotEmpty)
            .zipWith(this.deviceFeignClient.essTotalBi(param)
                .doOnNext(FeignResponseValidate::check)
                .map(ObjectResponse::getData))
            .map(tuple -> {
                List<SiteVo> siteList = tuple.getT1();
                BigDecimal essCapacity = siteList.stream()
                    .filter(item -> item.getEssCapacity() != null)
                    .map(SiteVo::getEssCapacity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                BigDecimal essPower = siteList.stream()
                    .filter(item -> item.getEssPower() != null)
                    .map(SiteVo::getEssPower)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                TotalEssDataBi result = tuple.getT2();
                result.setEssSiteCnt((long) siteList.size())
                    .setEssTotalPower(essPower)
                    .setEssTotalCapacity(essCapacity);
                return result;
            })
            .switchIfEmpty(Mono.just(new TotalEssDataBi(true)));
    }

    public Mono<List<EssEquipDetailVo>> essPcsDetailBi24h(EssEquipDetailParam param) {
        if (StringUtils.isBlank(param.getEssDno())) {
            throw new DcArgumentException("ESS编号无效");
        }

        if (null == param.getEquipId()) {
            throw new DcArgumentException("设备ID无效");
        }

        return redisEssEquipRtDataService.batteryStackDataSampling24h(param.getEssDno(),
            param.getEquipId(), 5);
    }

    public Mono<List<EssEquipDetailVo>> detailBi24hUseForOp(EssEquipDetailParam param) {
        if (param != null
            && param.getEquipId() != null
            && StringUtils.isNotBlank(param.getEssDno())) {
            return redisEssEquipRtDataService.batteryStackDataSampling24h(param.getEssDno(),
                param.getEquipId(), 5);
        }

        return defaultBiData();
    }

    private Mono<EssEquipVo> destEquip(ListCtrlParam ctrlParam) {
        return deviceFeignClient.findEquipList(ctrlParam)
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .doOnNext(l -> {
                if (CollectionUtils.isEmpty(l)) {
                    throw new DcArgumentException("当前储能ESS没有挂载有效的设备");
                }
            })
            .map(l -> l.get(0)); // 注: 这里默认ESS和PCS一对一关系(产品已确认)
    }

    public Mono<List<EmsSampleData>> emsSample24h(EssEquipDetailParam param) {
        return Mono.just(param)
            .doOnNext(p -> {
                if (StringUtils.isBlank(p.getEssDno())) {
                    throw new DcArgumentException("储能ESS编号不能为空");
                }
            })
            .flatMap(p -> {
                ListCtrlParam ctrlParam = new ListCtrlParam();
                ctrlParam.setEssDno(p.getEssDno())
                    .setEquipTypeList(List.of(EssEquipType.EMS));
                return this.destEquip(ctrlParam);
            })
            .flatMap(ems -> redisEssEquipRtDataService.emsSampling24h(
                param.getEssDno(), ems.getEquipId(), 5));
    }

    public Mono<List<PcsSampleData>> pcsSample24h(EssEquipDetailParam param) {
        return Mono.just(param)
            .doOnNext(p -> {
                if (StringUtils.isBlank(p.getEssDno())) {
                    throw new DcArgumentException("储能ESS编号不能为空");
                }
            })
            .flatMap(p -> {
                ListCtrlParam ctrlParam = new ListCtrlParam();
                ctrlParam.setEssDno(p.getEssDno())
                    .setEquipTypeList(List.of(EssEquipType.PCS));
                return this.destEquip(ctrlParam);
            })
            .flatMap(pcs -> redisEssEquipRtDataService.pcsSampling24h(
                param.getEssDno(), pcs.getEquipId(), 5));
    }

    /**
     * 定制大屏的默认电池堆数据
     *
     * @return
     */
    private Mono<List<EssEquipDetailVo>> defaultBiData() {
        ListCtrlParam param = new ListCtrlParam();
        param.setEquipTypeList(List.of(EssEquipType.BATTERY_STACK));
        return Mono.just(param)
            .flatMap(e -> deviceFeignClient.findEquipList(param))
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .map(list -> list.stream().filter(essEquipPo ->
                    redisEssEquipRtDataService.latestRtData(essEquipPo.getEssDno(),
                        essEquipPo.getEquipId()) != null)
                .findFirst())
            .filter(Optional::isPresent)
            .map(Optional::get)
            .flatMap(essEquipPo -> redisEssEquipRtDataService.batteryStackDataSampling24h(
                essEquipPo.getEssDno(), essEquipPo.getEquipId(), 5))
            .switchIfEmpty(Mono.just(List.of()));
    }

    public Mono<List<EssEquipDetailVo>> essPowerBi(String essDno, Date date) {
        int dis = 1; // 分钟

        LocalDate localDate = DateUtil.dateToLocalDate(date);
        AtomicReference<List<LocalDateTime>> times = new AtomicReference<>();
        if (LocalDate.now().isEqual(localDate)) {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime mid = LocalDate.now().atStartOfDay();
            times.set(RedisEssEquipRtDataService.splitDay(mid, now.plusMinutes(dis), dis));
        } else {
            times.set(RedisEssEquipRtDataService.splitDay(localDate, dis));
        }

        return deviceFeignClient.getEquipPowerBiVo(essDno)
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .filter(e -> StringUtils.isNotBlank(e.getSiteId()) && (e.getPcsEquipId() != null
                || e.getBatteryStackEquipId() != null))
            .flatMap(vo -> {
                if (LocalDate.now().isEqual(localDate)) {
                    return redisEssEquipRtDataService.essPowerBiToday(essDno, times.get(),
                        vo.getPcsEquipId(), vo.getBatteryStackEquipId());
                }

                return dayPowerSampling4File(essDno, vo, localDate, times.get());
            })
            .switchIfEmpty(Flux.fromIterable(times.get())
                .map(RedisEssEquipRtDataService::generatePcsZeroSampling)
                .collectList());
    }

    private Mono<List<EssEquipDetailVo>> dayPowerSampling4File(String essDno,
        EquipPowerBiVo biVo, LocalDate date, final List<LocalDateTime> times) {
        if (biVo.getSiteId() == null) {
            return Mono.empty();
        }
        if (biVo.getBatteryStackEquipId() == null && biVo.getPcsEquipId() == null) {
            return Mono.empty();
        }
        return Mono.zip(this.getOSSPcsData(essDno, biVo, date, times),
                this.getOSSBatteryStackData(essDno, biVo, date, times))
            .map(e -> {
                if (e.getT1().isEmpty() && e.getT2().isEmpty()) {
                    return List.of();
                }
                AtomicReference<Map<LocalDateTime, EssEquipDetailVo>> pcsMapAtomicReference = new AtomicReference<>();
                AtomicReference<Map<LocalDateTime, EssEquipDetailVo>> batteryStackMapAtomicReference = new AtomicReference<>();
                List<EssEquipDetailVo> res = new ArrayList<>();

                e.getT1().ifPresentOrElse(vo -> {
                    pcsMapAtomicReference.set(
                        vo.stream().collect(Collectors.toMap(EssEquipDetailVo::getTime, o -> o)));
                }, () -> {
                    pcsMapAtomicReference.set(new HashMap<>());
                });
                e.getT2().ifPresentOrElse(vo -> {
                    batteryStackMapAtomicReference.set(
                        vo.stream().collect(Collectors.toMap(EssEquipDetailVo::getTime, o -> o)));
                }, () -> {
                    batteryStackMapAtomicReference.set(new HashMap<>());
                });

                Set<LocalDateTime> keySet = pcsMapAtomicReference.get().size() > 0
                    ? pcsMapAtomicReference.get().keySet()
                    : batteryStackMapAtomicReference.get().keySet();
                for (LocalDateTime time : keySet) {
                    EssEquipDetailVo pcsData = pcsMapAtomicReference.get().get(time);
                    EssEquipDetailVo batteryStackData = batteryStackMapAtomicReference.get()
                        .get(time);
                    res.add(new EssEquipDetailVo()
                        .setTime(time)
                        .setSoc(
                            batteryStackData != null ? batteryStackData.getSoc() : BigDecimal.ZERO)
                        .setAcApparentPowerTotal(
                            pcsData != null ? pcsData.getAcApparentPowerTotal() : BigDecimal.ZERO)
                        .setAcReactivePowerTotal(
                            pcsData != null ? pcsData.getAcReactivePowerTotal() : BigDecimal.ZERO)
                        .setAcActivePowerTotal(
                            pcsData != null ? pcsData.getAcActivePowerTotal() : BigDecimal.ZERO));
                }
                return res.stream()
                    .sorted(Comparator.comparing(EssEquipDetailVo::getTime))
                    .collect(Collectors.toList());

            });
    }

    private Mono<Optional<List<EssEquipDetailVo>>> getOSSPcsData(String essDno,
        EquipPowerBiVo biVo, LocalDate date, final List<LocalDateTime> times) {
        return ossArchiveService.getOSSDeviceRtData(biVo.getSiteId(), essDno, biVo.getPcsEquipId(),
                NetType.ESS, date)
            .filter(Optional::isPresent)
            .map(Optional::get)
            .map(ossObject -> {
                List<EssEquipDetailVo> samplingList = new ArrayList<>();
                try (InputStream gzipStream = new GZIPInputStream(ossObject.getObjectContent());
                    Reader decoder = new InputStreamReader(gzipStream, Charset.defaultCharset());
                    BufferedReader reader = new BufferedReader(decoder)) {

                    boolean hasLine = true;
                    LineEssRtData<Pcs> last = null;
                    for (LocalDateTime time : times) {
                        if (null != last) {
                            LocalDateTime temp = last.getTime()
                                .withSecond(0)
                                .withNano(0);
                            if (time.isBefore(temp)) {
                                RedisEssEquipRtDataService.addPcsRepeatAcDataSampling(samplingList,
                                    time, last);
                                continue;
                            } else if (time.isEqual(temp)) {
                                samplingList.add(new EssEquipDetailVo()
                                    .setTime(time)
                                    .setAcActivePowerTotal(last.getRtData().getAcActivePowerTotal())
                                    .setAcReactivePowerTotal(
                                        last.getRtData().getAcReactivePowerTotal())
                                    .setAcApparentPowerTotal(
                                        last.getRtData().getAcApparentPowerTotal()));
                                continue;
                            }
                        }

                        while (hasLine) {
                            String line = null;
                            try {
                                line = reader.readLine();
                            } catch (IOException e) {
                                // nothing
                                log.error("err = {}", e.getMessage(), e);
                            }

                            if (line == null) {
                                hasLine = false;
                                break;
                            }

                            LineEssRtData<Pcs> rtData = lineData(line, Pcs.class);
                            if (null == rtData || rtData.getTime() == null
                                || rtData.getRtData() == null) {
                                continue;
                            }

                            LocalDateTime temp = rtData.getTime()
                                .withSecond(0)
                                .withNano(0);

                            if (temp.isBefore(time)) {
                                continue;
                            } else if (temp.isEqual(time)) {
                                samplingList.add(new EssEquipDetailVo()
                                    .setTime(time)
                                    .setAcActivePowerTotal(
                                        rtData.getRtData().getAcActivePowerTotal())
                                    .setAcReactivePowerTotal(
                                        rtData.getRtData().getAcReactivePowerTotal())
                                    .setAcApparentPowerTotal(
                                        rtData.getRtData().getAcApparentPowerTotal()));
                            } else if (temp.isAfter(time)) {
                                if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(samplingList)
                                    && samplingList.get(samplingList.size() - 1)
                                    .getAcActivePowerTotal() != null
                                    && DecimalUtils.gtZero(samplingList.get(samplingList.size() - 1)
                                    .getAcActivePowerTotal())) {
                                    samplingList.add(new EssEquipDetailVo()
                                        .setTime(time)
                                        .setAcActivePowerTotal(
                                            RedisEssEquipRtDataService.calcAverage(last, 1,
                                                rtData.getRtData().getAcActivePowerTotal()))
                                        .setAcReactivePowerTotal(
                                            RedisEssEquipRtDataService.calcAverage(last, 2,
                                                rtData.getRtData().getAcReactivePowerTotal()))
                                        .setAcApparentPowerTotal(
                                            RedisEssEquipRtDataService.calcAverage(last, 3,
                                                rtData.getRtData().getAcApparentPowerTotal())));
                                } else {
                                    RedisEssEquipRtDataService.addPcsZeroSampling(samplingList,
                                        time);
                                }
                            }
                            last = rtData;
                            rtData = null;
                            break;
                        }

                        if (!hasLine) {
                            RedisEssEquipRtDataService.addPcsZeroSampling(samplingList, time);
                        }
                    }
                } catch (Exception e) {
                    // nothing
                    log.error("err = {}", e.getMessage(), e);
                }
                return samplingList;
            })
            .map(Optional::of)
            .switchIfEmpty(Mono.just("OSS文件不存在").map(t -> (Optional.empty())));

    }

    private Mono<Optional<List<EssEquipDetailVo>>> getOSSBatteryStackData(String essDno,
        EquipPowerBiVo biVo, LocalDate date, final List<LocalDateTime> times) {
        return ossArchiveService.getOSSDeviceRtData(biVo.getSiteId(), essDno,
                biVo.getBatteryStackEquipId(), NetType.ESS, date)
            .filter(Optional::isPresent)
            .map(Optional::get)
            .map(ossObject -> {
                List<EssEquipDetailVo> samplingList = new ArrayList<>();
                try (InputStream gzipStream = new GZIPInputStream(ossObject.getObjectContent());
                    Reader decoder = new InputStreamReader(gzipStream, Charset.defaultCharset());
                    BufferedReader reader = new BufferedReader(decoder)) {

                    boolean hasLine = true;
                    LineEssRtData<BatteryStack> last = null;
                    for (LocalDateTime time : times) {
                        if (null != last) {
                            LocalDateTime temp = last.getTime()
                                .withSecond(0)
                                .withNano(0);
                            if (time.isBefore(temp)) {
                                RedisEssEquipRtDataService.addPcsRepeatSocSampling(samplingList,
                                    time, last);
                                continue;
                            } else if (time.isEqual(temp)) {
                                samplingList.add(new EssEquipDetailVo()
                                    .setTime(time)
                                    .setSoc(last.getRtData().getSoc()));
                                continue;
                            }
                        }

                        while (hasLine) {
                            String line = null;
                            try {
                                line = reader.readLine();
                            } catch (IOException e) {
                                // nothing
                                log.error("err = {}", e.getMessage(), e);
                            }

                            if (line == null) {
                                hasLine = false;
                                break;
                            }

                            LineEssRtData<BatteryStack> rtData = lineData(line, BatteryStack.class);
                            if (null == rtData || rtData.getTime() == null) {
                                continue;
                            }

                            LocalDateTime temp = rtData.getTime()
                                .withSecond(0)
                                .withNano(0);

                            if (temp.isBefore(time)) {
                                continue;
                            } else if (temp.isEqual(time)) {
                                samplingList.add(new EssEquipDetailVo()
                                    .setTime(time)
                                    .setSoc(rtData.getRtData().getSoc()));
                            } else if (temp.isAfter(time)) {
                                if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(samplingList)
                                    && samplingList.get(samplingList.size() - 1).getSoc() != null
                                    && DecimalUtils.gtZero(
                                    samplingList.get(samplingList.size() - 1).getSoc())) {
                                    BigDecimal lastSoc = last != null && last.getRtData() != null
                                        & last.getRtData().getSoc() != null
                                        ? last.getRtData().getSoc() : BigDecimal.ZERO;
                                    BigDecimal value = DecimalUtils.add(lastSoc,
                                            rtData.getRtData().getSoc())
                                        .divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
                                    samplingList.add(new EssEquipDetailVo()
                                        .setTime(time)
                                        .setSoc(value));
                                } else {
                                    RedisEssEquipRtDataService.addPcsZeroSampling(samplingList,
                                        time);
                                }
                            }
                            last = rtData;
                            rtData = null;
                            break;
                        }

                        if (!hasLine) {
                            RedisEssEquipRtDataService.addPcsZeroSampling(samplingList, time);
                        }
                    }
                } catch (Exception e) {
                    // nothing
                    log.error("err = {}", e.getMessage(), e);
                }
                return samplingList;
            })
            .map(Optional::of)
            .switchIfEmpty(Mono.just("OSS文件不存在").map(t -> (Optional.empty())));

    }

    private static <T> LineEssRtData<T> lineData(String line, Class<T> tClass) {
        String[] split = line.split(" \\| ");
        if (split.length == 3) {
            LineEssRtData<T> rtData = new LineEssRtData<T>();
            rtData.setTime(LocalDateTime.parse(split[0], TIME_FORMATTER));

            String[] serial = split[1].split("/");
            rtData.setDno(serial[0])
                .setEquipId(Integer.valueOf(serial[1]));

            rtData.setRtData(JsonUtils.fromJson(split[2], tClass));
            return rtData;
        }

        return null;
    }

    public Mono<List<RedisEquipRtData<MgcAlert>>> mgcAlertDataSampling(MgcDetailParam param) {
        IotAssert.isNotNull(param, "请传入参数");
        IotAssert.isNotNull(param.getKeyword(), "请传入关键字");
        IotAssert.isNotNull(param.getStartTime(), "清输入开始时间");
        IotAssert.isNotNull(param.getEndTime(), "清输入结束时间");
//        IotAssert.isTrue(param.getStartTime().getYear() == param.getEndTime().getYear() &&
//                param.getStartTime().getDayOfYear() == param.getEndTime().getDayOfYear(), "当前暂不支持跨天查找");
        IotAssert.isTrue(param.getEndTime().minusDays(1).isBefore(param.getStartTime()) ||
                param.getEndTime().minusDays(1).isEqual(param.getStartTime()),
            "时间范围最大不超过24小时");

        return reactorIotWorkerFeignClient.findMgcGwnoByKeyword(param.getKeyword())
            .map(e -> {
                log.info("mgc res: {}", e);
                IotAssert.isNotBlank(e.getData(), "控制器不存在");
                List<RedisEquipRtData<MgcAlert>> ret = redisEssEquipRtDataService.rtData(
                    RedisEssEquipRtDataService.formatMgcKey(
                        e.getData(),
                        param.getStartTime()),
                    param.getStartTime(),
                    param.getEndTime(),
                    MgcAlert.class
                );
                if (param.getStartTime().getDayOfYear() != param.getEndTime().getDayOfYear()) {
                    // 跨天的数据增补
                    LocalDateTime prevDay = param.getStartTime();
                    LocalDateTime nextDay00_00_00 = prevDay.plusDays(1)
                        .minusHours(prevDay.getHour())
                        .minusMinutes(prevDay.getMinute())
                        .minusSeconds(prevDay.getSecond());
                    List<RedisEquipRtData<MgcAlert>> retNext = redisEssEquipRtDataService.rtData(
                        RedisEssEquipRtDataService.formatMgcKey(
                            e.getData(),
                            nextDay00_00_00),
                        nextDay00_00_00,
                        param.getEndTime(),
                        MgcAlert.class
                    );
                    ret.addAll(retNext);
                }
                return ret;
            });
    }

    public Mono<List<BatteryPackEx>> batteryClusterDataSampling(EssEquipDetailParamEx param) {
        if (StringUtils.isBlank(param.getEssDno())) {
            throw new DcArgumentException("ESS编号无效");
        }

        if (null == param.getEquipId()) {
            throw new DcArgumentException("设备ID无效");
        }

        IotAssert.isNotNull(param.getStartTime(), "清输入开始时间");
        IotAssert.isNotNull(param.getEndTime(), "清输入结束时间");
//        IotAssert.isTrue(param.getStartTime().getYear() == param.getEndTime().getYear() &&
//                param.getStartTime().getDayOfYear() == param.getEndTime().getDayOfYear(), "当前暂不支持跨天查找");
        IotAssert.isTrue(param.getEndTime().minusDays(1).isBefore(param.getStartTime()) ||
                param.getEndTime().minusDays(1).isEqual(param.getStartTime()),
            "时间范围最大不超过24小时");

        IotAssert.isNotNull(param.getLmuSn(), "请输入电池组SN");
        List<RedisEquipRtData<BatteryCluster>> redisBatteryClusters = redisEssEquipRtDataService.rtData(
            RedisEssEquipRtDataService.formatEssKey(
                param.getEssDno(),
                param.getEquipId(),
                param.getStartTime()),
            param.getStartTime(),
            param.getEndTime(),
            BatteryCluster.class);

        if (param.getStartTime().getDayOfYear() != param.getEndTime().getDayOfYear()) {
            // 跨天的数据增补
            LocalDateTime prevDay = param.getStartTime();
            LocalDateTime nextDay00_00_00 = prevDay.plusDays(1)
                .minusHours(prevDay.getHour())
                .minusMinutes(prevDay.getMinute())
                .minusSeconds(prevDay.getSecond());
            List<RedisEquipRtData<BatteryCluster>> retNext = redisEssEquipRtDataService.rtData(
                RedisEssEquipRtDataService.formatEssKey(
                    param.getEssDno(),
                    param.getEquipId(),
                    nextDay00_00_00),
                nextDay00_00_00,
                param.getEndTime(),
                BatteryCluster.class
            );
            redisBatteryClusters.addAll(retNext);
        }

        List<BatteryPackEx> allPack = new ArrayList<>();
        redisBatteryClusters.forEach(e -> {
            e.getData().getPackList()
                .stream()
                .forEach(pack -> {
                    BatteryPackEx packEx = new BatteryPackEx();
                    BeanUtils.copyProperties(pack, packEx);
                    packEx.setTime(e.getTime());
                    allPack.add(packEx);
                });
        });

        List<BatteryPackEx> ret = allPack.stream()
            .filter(e -> e.getLmuSn() != null && NumberUtils.equals(e.getLmuSn().longValue(),
                param.getLmuSn()))
            .collect(Collectors.toList());

        return Mono.just(ret);
    }

    public Mono<ListResponse<DayEssDataBi>> siteEssBi(SiteBiParam param) {
        if (null == param.getSampleType()) {
            throw new DcArgumentException("请选择采样时间类型");
        }

        if (null == param.getStartTime()) {
            throw new DcArgumentException("请选择开始时间");
        }

        if (null == param.getEndTime()) {
            throw new DcArgumentException("请选择结束时间");
        }

//        if (StringUtils.isBlank(param.getSiteId())) {
//            throw new DcArgumentException("请提供场站ID");
//        }

        if (SiteBiSampleType.DAY.equals(param.getSampleType())) {
            return this.daySiteEssBi(new DayKwhParam().setSiteIdList(List.of(param.getSiteId()))
                .setDate(new TimeFilter()
                    .setStartTime(new Date(param.getStartTime()))
                    .setEndTime(new Date(param.getEndTime()))));
        } else if (SiteBiSampleType.MONTH.equals(param.getSampleType())) {
            // 开始时间调整
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date(param.getStartTime()));
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            Date startTime = calendar.getTime();

            // 结束时间调整
            calendar.setTime(new Date(param.getEndTime()));
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            Date endTime = calendar.getTime();

            return this.monthSiteEssBi(new DayKwhParam().setSiteIdList(List.of(param.getSiteId()))
                .setDate(new TimeFilter()
                    .setStartTime(startTime)
                    .setEndTime(endTime)));
        } else {
            throw new DcArgumentException("采样时间类型不支持");
        }
    }

    public Mono<ObjectResponse<ExcelPosition>> exportSiteEssBi(ServerHttpRequest request,
        SiteBiParam param) {
        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("储能趋势")
            .setFunctionMap(DownloadFunctionType.ESS_BI_TREND)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return this.siteEssBi(param)
//                .doOnNext(FeignResponseValidate::check)
//                .map(ListResponse::getData)
//                .flatMap(data -> asyncBizBiFeignClient.exportSiteEssBiExcel(param.getSampleType(), data));
    }

    public Mono<ListResponse<DayEssDataBi>> daySiteEssBi(DayKwhParam param) {
        if (null == param.getDate() ||
            null == param.getDate().getStartTime() ||
            null == param.getDate().getEndTime()) {
            throw new DcArgumentException("请指定时间范围");
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(param.getDate().getEndTime());
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        param.getDate().setEndTime(calendar.getTime());

        if (CollectionUtils.isEmpty(param.getSiteIdList())) {
            throw new DcArgumentException("请选择场站");
        }

        return deviceFeignClient.siteDayOfRangeKwh(param)
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .flatMapMany(Flux::fromIterable)
            .collect(Collectors.toMap(DaySiteEssRtDataBi::getDate, o -> o, (o1, o2) -> {
                o1.setProfit(DecimalUtils.add(o1.getProfit(), o2.getProfit()));
                o1.setInFee(DecimalUtils.add(o1.getInFee(), o2.getInFee()));
                o1.setInKwh(DecimalUtils.add(o1.getInKwh(), o2.getInKwh()));
                o1.setOutFee(DecimalUtils.add(o1.getOutFee(), o2.getOutFee()));
                o1.setOutKwh(DecimalUtils.add(o1.getOutKwh(), o2.getOutKwh()));
                return o1;
            }))
            .flatMap(dateMap -> Flux.fromIterable(
                    DateUtil.rangeDate(
                        DateUtil.dateToLocalDate(param.getDate().getStartTime()),
                        DateUtil.dateToLocalDate(param.getDate().getEndTime())))
                .map(date -> {
                    DayEssDataBi result = new DayEssDataBi();
                    DaySiteEssRtDataBi data = dateMap.get(date);
                    if (data != null) {
                        BeanUtils.copyProperties(data, result);
                    }
                    return result.setDate(date);
                })
                .collectList())
            .map(RestUtils::buildListResponse);
    }

    public Mono<ListResponse<DayEssDataBi>> monthSiteEssBi(DayKwhParam param) {
        if (null == param.getDate() ||
            null == param.getDate().getStartTime() ||
            null == param.getDate().getEndTime()) {
            throw new DcArgumentException("请指定时间范围");
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(param.getDate().getEndTime());
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        param.getDate().setEndTime(calendar.getTime());

        if (CollectionUtils.isEmpty(param.getSiteIdList())) {
            throw new DcArgumentException("请选择场站");
        }

        return deviceFeignClient.siteDayOfRangeKwh(param)
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .flatMapMany(Flux::fromIterable)
            .collect(
                Collectors.toMap(o -> o.getDate().format(MONTH_FORMATTER), o -> o, (o1, o2) -> {
                    o1.setProfit(DecimalUtils.add(o1.getProfit(), o2.getProfit()));
                    o1.setInFee(DecimalUtils.add(o1.getInFee(), o2.getInFee()));
                    o1.setInKwh(DecimalUtils.add(o1.getInKwh(), o2.getInKwh()));
                    o1.setOutFee(DecimalUtils.add(o1.getOutFee(), o2.getOutFee()));
                    o1.setOutKwh(DecimalUtils.add(o1.getOutKwh(), o2.getOutKwh()));
                    return o1;
                }))
            .flatMap(dateMap -> Flux.fromIterable(
                    DateUtil.rangeMonthDate(
                        DateUtil.dateToLocalDate(param.getDate().getStartTime()),
                        DateUtil.dateToLocalDate(param.getDate().getEndTime())))
                .map(date -> {
                    DayEssDataBi result = new DayEssDataBi();
                    DaySiteEssRtDataBi data = dateMap.get(date.format(MONTH_FORMATTER));
                    if (data != null) {
                        BeanUtils.copyProperties(data, result);
                        data.setDate(date);
                    }
                    return result.setDate(date);
                })
                .collectList())
            .map(RestUtils::buildListResponse);
    }

    public Mono<ListResponse<EssStatusBi>> getEssStatusBi(String commIdChain, String siteId) {
        return deviceFeignClient.getEssStatusBi(commIdChain, siteId);
    }

    public Mono<ListResponse<Object>> siteDateInOutPower(String siteId) {
        // FIXME: 待需求确认
        return null;
    }

    public Mono<ListResponse<EquipSampleData>> equipRtDataSample(DataBiParam param) {
        return this.deviceFeignClient.equipRtDataSample(param)
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .map(data -> {
                if (null != param.getShortcut() && data.size() != param.getShortcut()) {
                    List<LocalDateTime> times = new ArrayList<>();
                    switch (param.getSampleType()) {
                        case DAY:
                            if (null != param.getShortcut()) {
                                LocalDateTime now = LocalDate.now().atStartOfDay();
                                LocalDateTime start = now.minusDays(param.getShortcut() - 1);
                                while (start.isBefore(now)) {
                                    times.add(start);
                                    start = start.plusDays(1);
                                }
                                times.add(now);
                            } else if (param.getFromDate() != null && null != param.getToDate()) {
                                LocalDateTime toDate = param.getToDate().toLocalDate()
                                    .atStartOfDay();
                                LocalDateTime start = param.getFromDate().toLocalDate()
                                    .atStartOfDay();
                                while (start.isBefore(toDate)) {
                                    times.add(start);
                                    start = start.plusDays(1);
                                }
                                times.add(toDate);
                            }
                            break;
                        case MONTH:
                            times = new ArrayList<>();
                            if (null != param.getShortcut()) {
                                LocalDateTime now = LocalDate.now()
                                    .with(DAY_OF_MONTH, 1).atStartOfDay();
                                LocalDateTime start = now.minusMonths(param.getShortcut() - 1);
                                while (start.isBefore(now)) {
                                    times.add(start);
                                    start = start.plusMonths(1);
                                }
                                times.add(now);
                            } else if (param.getFromDate() != null && null != param.getToDate()) {
                                LocalDateTime toDate = param.getToDate().toLocalDate()
                                    .atStartOfDay();
                                LocalDateTime start = param.getFromDate().toLocalDate()
                                    .atStartOfDay();
                                while (start.isBefore(toDate)) {
                                    times.add(start);
                                    start = start.plusMonths(1);
                                }
                                times.add(toDate);
                            }
                            break;
                    }

                    List<LocalDateTime> hasTimes = data.stream()
                        .map(i -> i.getTime().toLocalDate().atStartOfDay())
                        .collect(Collectors.toList());
                    times.stream().filter(t -> !hasTimes.contains(t))
                        .forEach(t -> data.add(new EquipSampleData().setTime(t)));
                }
                return data.stream()
                    .sorted(Comparator.comparing(EquipSampleData::getTime))
                    .collect(Collectors.toList());
            })
            .map(RestUtils::buildListResponse);
    }
}
