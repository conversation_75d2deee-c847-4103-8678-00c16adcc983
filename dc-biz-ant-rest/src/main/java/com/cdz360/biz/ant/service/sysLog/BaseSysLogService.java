package com.cdz360.biz.ant.service.sysLog;

import com.cdz360.base.model.base.vo.KvObject;
import com.cdz360.base.model.base.vo.KvString;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.auth.user.po.SysUserLogPo;
import com.cdz360.biz.auth.user.type.LogOpType;
import com.cdz360.data.sync.service.DcEventPublisher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class BaseSysLogService {

    @Autowired
    private DcEventPublisher dcEventPublisher;

    public void buildOpLog(LogOpType opType, KvString opObject, ServerHttpRequest request) {
        KvObject object = new KvObject();
        object.setKey(opObject.getKey());
        object.setValue(opObject.getValue());
        this.buildObjectOpLog(opType, List.of(object), request);
    }

    public void buildOpLog(LogOpType opType, List<KvString> opObject, ServerHttpRequest request) {
        List<KvObject> temp = opObject.stream().map(e -> {
            KvObject object = new KvObject();
            object.setKey(e.getKey());
            object.setValue(e.getValue());
            return object;
        }).collect(Collectors.toList());
        this.buildObjectOpLog(opType, temp, request);
    }

    public void buildObjectOpLog(LogOpType opType, List<KvObject> opObject, ServerHttpRequest request) {
        SysUserLogPo uLog = new SysUserLogPo();
        uLog.setTopCommId(AntRestUtils.getTopCommId(request))
                .setCommId(AntRestUtils.getCommId(request))
                .setSysUid(AntRestUtils.getSysUid(request))
                .setLoginName(AntRestUtils.getSysUserLoginName(request))
                .setLoginUser(AntRestUtils.getSysUserName(request))
                .setOpType(opType)
                .setIp(AntRestUtils.getIpAddress(request))
                .setClientType(AntRestUtils.getAppClientType(request))
                .setOpObject(opObject);
        log.info("uLog = {}", uLog);
        dcEventPublisher.publishSysUserLog(uLog.toString());
    }

    public void buildLoginOpLog(SysUserLogPo uLog, ServerHttpRequest request) {
        uLog.setOpType(LogOpType.LOGIN)
                .setIp(AntRestUtils.getIpAddress(request))
                .setClientType(AntRestUtils.getAppClientType(request));
        log.info("uLog = {}", uLog);
        dcEventPublisher.publishSysUserLog(uLog.toString());
    }
}
