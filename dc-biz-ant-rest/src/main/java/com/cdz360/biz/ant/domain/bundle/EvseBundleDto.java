package com.cdz360.biz.ant.domain.bundle;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;


/**
 * EvseBundleDto
 *  桩升级包数据传输DTO
 * <EMAIL>
 * <AUTHOR>
 * @since  2019/9/18 13:13
 */
@Schema(description = "com.chargerlink.device.business.entity.po.EvseBundleDto")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EvseBundleDto extends EvseBundle {
    private static final long serialVersionUID = 1L;

    private List<PC0XDto> pc0XList;

    public List<PC0XDto> getPc0XList() {
        return pc0XList;
    }

    public EvseBundleDto setPc0XList(List<PC0XDto> pc0XList) {
        this.pc0XList = pc0XList;
        return this;
    }
}