package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.oa.param.PrepaidInvoicingEditParam;
import com.cdz360.biz.model.oa.vo.OaInvoicedVo;
import com.cdz360.biz.model.trading.invoice.dto.InvoicedRecordDto;
import com.cdz360.biz.model.trading.invoice.param.UserInvoiceRecordParam;
import com.cdz360.biz.model.trading.invoice.po.CorpInvoiceRecordPo;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderBiVo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE,
        fallbackFactory = DataCoreInvoiceFeignHystrix.class)
public interface DataCoreInvoiceFeignClient {
    // 获取已申请企业开票的充电订单列表
    @PostMapping(value = "/dataCore/invoice/includeChargerOrderList")
    Mono<ListResponse<ChargerOrderVo>> includeChargerOrderList(@RequestBody ListChargeOrderParam param);

    // 获取已申请企业开票的充电订单汇总信息
    @PostMapping(value = "/dataCore/invoice/includeChargerOrderBi")
    Mono<ObjectResponse<OrderBiVo>> includeChargerOrderBi(@RequestBody ListChargeOrderParam param);

    // 预付订单开票流程提交审核(非企业扣款账户订单)
    @PostMapping(value = "/dataCore/invoice/prepaidInvoiceSubmit2Audit")
    Mono<ObjectResponse<Long>> prepaidInvoiceSubmit2Audit(
        @RequestBody PrepaidInvoicingEditParam param);

    // 获取预付订单流程页面需展示的开票信息
    @GetMapping(value = "/dataCore/invoice/getInvoiceVo4PrepaidProcess")
    Mono<ObjectResponse<OaInvoicedVo>> getInvoiceVo4PrepaidProcess(
        @RequestParam(value = "procInstId") String procInstId);

    // 修改企业开票记录
    @PostMapping(value = "/dataCore/invoice/updateCorpInvoiceRecord")
    Mono<ObjectResponse<Integer>> updateCorpInvoiceRecord(@RequestBody CorpInvoiceRecordPo invoiceRecordPo);

    // 修改个人&商户会员开票记录
    @PostMapping(value = "/dataCore/invoice/updateUserInvoiceRecord")
    Mono<ObjectResponse<Integer>> updateUserInvoiceRecord(@RequestBody InvoicedRecordDto invoicedRecordDto);

    @PostMapping(value = "/dataCore/invoice/userInvoiceRecordSubmit2Audit")
    Mono<ListResponse<InvoicedRecordDto>> userInvoiceRecordSubmit2Audit(@RequestBody UserInvoiceRecordParam dto);
}
