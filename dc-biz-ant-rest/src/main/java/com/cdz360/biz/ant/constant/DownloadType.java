package com.cdz360.biz.ant.constant;

/**
 * <AUTHOR>
 *   下载数据类型
 * @since 2019/5/16
 **/
public enum DownloadType {
    ONLINE_ORDER("online", "在线订单"),
    OFFLINE_ORDER("offline", "离线订单"),
    ABNORMAL_ORDER("abnormal", "异常订单")
    ;
    private final String code;
    private final String name;

    DownloadType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName(){
        return name;
    }

    public static DownloadType valueOfCode(String code) {
        for (DownloadType type : DownloadType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}
