package com.cdz360.biz.ant.domain.vo;

import com.cdz360.base.model.base.vo.BaseObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 枪头详情
 *
 * <AUTHOR>
 * @since 2019/7/8 12:50
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class PlugDetailVo extends BaseObject {
    // 电池温度
    private Integer batteryTemp;

    // 桩温度
    private Integer evseTemp;

    // 枪温度
    private Integer plugTemp;

    // 功率，单位: 0.01W
    private Integer power;

    // 直流电压，单位: 0.1V
    private Integer dcVoltage;

    // 直流电流，单位: 0.1A
    private Integer dcCurrent;

    // 交流A相电压，单位: 0.1V
    private Integer acVoltageA;

    // 交流A相电流，单位: 0.1A
    private Integer acCurrentA;

    // 交流B相电压，单位: 0.1V
    private Integer acVoltageB;

    // 交流B相电流，单位: 0.1A
    private Integer acCurrentB;

    // 交流C相电压，单位: 0.1V
    private Integer acVoltageC;

    // 交流C相电流，单位: 0.1A
    private Integer acCurrentC;

    // 数据采集时间，UNIX 时间戳
    private Integer timestamp;

    // 订单号
    private String orderNo;

    // 需求电压
    private Integer needVoltage;

    // 需求电流
    private Integer needCurrent;
}
