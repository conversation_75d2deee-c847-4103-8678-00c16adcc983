package com.cdz360.biz.ant.service;


import com.cdz360.biz.ant.feign.AntUserFeignClient;
import com.cdz360.biz.ant.feign.DeviceFeignClient;
import com.cdz360.biz.ant.feign.TradingFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 数据分析
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AnalysisService //implements IAnalysisService
{

    @Autowired
    private TradingFeignClient tradingFeignClient;

    @Autowired
    private AntUserFeignClient userFeignClient;

    @Autowired
    private DeviceFeignClient deviceFeignClient;


    /**
     * 运营拆分接口 -- 用电量统计
     *
     * @param commIdList 当前商户及子商户id列表
     * @return
     */

//    public ListResponse<KVAnalysisVo> getydltj(List<Long> commIdList) {
//        ListResponse<KVAnalysisVo> jsonObjectRes = tradingFeignClient.getydltj(commIdList);
//        FeignResponseValidate.check(jsonObjectRes);
//        return jsonObjectRes;
//
////        if(jsonObjectRes == null || jsonObjectRes.get("status") == null){
////            throw new DcServiceException("获取失败");
////        }
////
////        if ( jsonObjectRes.getInteger("status") != ResultConstant.RES_SUCCESS_CODE) {
////
////            if(jsonObjectRes.get("error")==null){
////                throw new DcServiceException("获取失败");
////            }else{
////                throw new DcServiceException(jsonObjectRes.get("error").toString());
////            }
////        }
////
//        //        return new ObjectResponse<>(jsonObjectRes.get("data"));
//    }

    /**
     * 运营拆分接口 -- 用电量走势
     *
     * @param commIdList 当前商户及子商户id列表
     * @return
     */

//    public ListResponse<KVAnalysisVo> getydlzs(List<Long> commIdList) {
//        ListResponse<KVAnalysisVo> jsonObjectRes = tradingFeignClient.getydlzs(commIdList);
//        FeignResponseValidate.check(jsonObjectRes);
//        return jsonObjectRes;
//
////        if(jsonObjectRes == null || jsonObjectRes.get("status") == null){
////            throw new DcServiceException("获取失败");
////        }
////
////        if ( jsonObjectRes.getInteger("status") != ResultConstant.RES_SUCCESS_CODE) {
////
////            if(jsonObjectRes.get("error")==null){
////                throw new DcServiceException("获取失败");
////            }else{
////                throw new DcServiceException(jsonObjectRes.get("error").toString());
////            }
////        }
////
//        //        return new ObjectResponse<>(jsonObjectRes.get("data"));
//    }

    /**
     * 运营拆分接口 --  站点统计概览
     *
     * @param commIdList 当前商户及子商户id列表
     * @return
     */
//
//    public ObjectResponse getzdtjgl(List<Long> commIdList) {
//
//        ObjectResponse jsonObjectRes = deviceFeignClient.getSiteStatisticalOverview(commIdList);
//
//        log.info("jsonObjectRes:::" + jsonObjectRes);
//        FeignResponseValidate.check(jsonObjectRes);
//        return jsonObjectRes;
////        if(jsonObjectRes == null || jsonObjectRes.get("status") == null){
////            throw new DcServiceException("获取失败");
////        }
////
////        if ( jsonObjectRes.getInteger("status") != ResultConstant.RES_SUCCESS_CODE) {
////
////            if(jsonObjectRes.get("error")==null){
////                throw new DcServiceException("获取失败");
////            }else{
////                throw new DcServiceException(jsonObjectRes.get("error").toString());
////            }
////        }
//        //        return new ObjectResponse<>(jsonObjectRes.get("data"));
//
//    }

    /**
     * 运营拆分接口 --  站点统计柱状图，本年度
     *
     * @param commIdList 当前商户及子商户id列表
     * @return
     */

//    public ObjectResponse getzdtjzzt(List<Long> commIdList) {
//
//        ObjectResponse jsonObjectRes = deviceFeignClient.getSiteStatisticalHistogram(commIdList);
//
//        log.info("jsonObjectRes:::" + jsonObjectRes);
//        FeignResponseValidate.check(jsonObjectRes);
//        return jsonObjectRes;
//
////        if(jsonObjectRes == null || jsonObjectRes.get("status") == null){
////            throw new DcServiceException("获取失败");
////        }
////
////        if ( jsonObjectRes.getInteger("status") != ResultConstant.RES_SUCCESS_CODE) {
////
////            if(jsonObjectRes.get("error")==null){
////                throw new DcServiceException("获取失败");
////            }else{
////                throw new DcServiceException(jsonObjectRes.get("error").toString());
////            }
////        }
//        //        return new ObjectResponse<>(jsonObjectRes.get("data"));
//
//    }

    /**
     * 运营拆分接口 -- 充电站昨日充电量排名
     *
     * @param commIdList 当前商户及子商户id列表
     * @return
     */

//    public ListResponse<KVAnalysisVo> getrcdlpm(List<Long> commIdList) {
//        ListResponse<KVAnalysisVo> jsonObjectRes = tradingFeignClient.getrcdlpm(commIdList);
//        return jsonObjectRes;
//
////        if(jsonObjectRes == null || jsonObjectRes.get("status") == null){
////            throw new DcServiceException("获取失败");
////        }
////
////        if ( jsonObjectRes.getInteger("status") != ResultConstant.RES_SUCCESS_CODE) {
////
////            if(jsonObjectRes.get("error")==null){
////                throw new DcServiceException("获取失败");
////            }else{
////                throw new DcServiceException(jsonObjectRes.get("error").toString());
////            }
////        }
////
//        //        return new ObjectResponse<>(jsonObjectRes.get("data"));
//    }

    /**
     * 运营拆分接口 --  客户统计概览
     *
     * @param commIdList 当前商户及子商户id列表
     * @return
     */
//
//    public ObjectResponse getkhtjgl(List<Long> commIdList) {
//        JSONObject jsonObjectRes = userFeignClient.getkhtjgl(commIdList);
//
//        if (jsonObjectRes == null || jsonObjectRes.get("status") == null) {
//            throw new DcServiceException("获取失败");
//        }
//
//        if (jsonObjectRes.getInteger("status") != ResultConstant.RES_SUCCESS_CODE) {
//
//            if (jsonObjectRes.get("error") == null) {
//                throw new DcServiceException("获取失败");
//            } else {
//                throw new DcServiceException(jsonObjectRes.get("error").toString());
//            }
//        }
//
//        return new ObjectResponse<>(jsonObjectRes.get("data"));
//    }

    /**
     * 运营拆分接口 -- 客户增长走势
     *
     * @param commIdList 当前商户及子商户id列表
     * @return
     */

//    public ObjectResponse getkhzzzs(List<Long> commIdList) {
//        JSONObject jsonObjectRes = userFeignClient.getkhzzzs(commIdList);
//
//        if (jsonObjectRes == null || jsonObjectRes.get("status") == null) {
//            throw new DcServiceException("获取失败");
//        }
//
//        if (jsonObjectRes.getInteger("status") != ResultConstant.RES_SUCCESS_CODE) {
//
//            if (jsonObjectRes.get("error") == null) {
//                throw new DcServiceException("获取失败");
//            } else {
//                throw new DcServiceException(jsonObjectRes.get("error").toString());
//            }
//        }
//
//        return new ObjectResponse<>(jsonObjectRes.get("data"));
//    }

    /**
     * 运营拆分接口 -- 客户统计柱状图,本年度
     *
     * @param commIdList 当前商户及子商户id列表
     * @return
     */

//    public ObjectResponse getkhtjzzt(List<Long> commIdList) {
//        JSONObject jsonObjectRes = userFeignClient.getkhtjzzt(commIdList);
//
//        if (jsonObjectRes == null || jsonObjectRes.get("status") == null) {
//            throw new DcServiceException("获取失败");
//        }
//
//        if (jsonObjectRes.getInteger("status") != ResultConstant.RES_SUCCESS_CODE) {
//
//            if (jsonObjectRes.get("error") == null) {
//                throw new DcServiceException("获取失败");
//            } else {
//                throw new DcServiceException(jsonObjectRes.get("error").toString());
//            }
//        }
//
//        return new ObjectResponse<>(jsonObjectRes.get("data"));
//    }

    /**
     * 运营拆分接口 -- 订单统计概览
     *
     * @param commIdList 当前商户及子商户id列表
     * @return
     */

//    public ListResponse<KVAnalysisVo> getddtjgl(List<Long> commIdList) {
//        ListResponse<KVAnalysisVo> jsonObjectRes = tradingFeignClient.getddtjgl(commIdList);
//        return jsonObjectRes;
//
////        if(jsonObjectRes == null || jsonObjectRes.get("status") == null){
////            throw new DcServiceException("获取失败");
////        }
////
////        if ( jsonObjectRes.getInteger("status") != ResultConstant.RES_SUCCESS_CODE) {
////
////            if(jsonObjectRes.get("error")==null){
////                throw new DcServiceException("获取失败");
////            }else{
////                throw new DcServiceException(jsonObjectRes.get("error").toString());
////            }
////        }
////
//        //        return new ObjectResponse<>(jsonObjectRes.get("data"));
//    }

    /**
     * 运营拆分接口 -- 订单走势
     *
     * @param commIdList 当前商户及子商户id列表
     * @return
     */

//    public ListResponse<KVAnalysisVo> getddzs(List<Long> commIdList) {
//        ListResponse<KVAnalysisVo> jsonObjectRes = tradingFeignClient.getddzs(commIdList);
//        FeignResponseValidate.check(jsonObjectRes);
//        return jsonObjectRes;
//
////        if(jsonObjectRes == null || jsonObjectRes.get("status") == null){
////            throw new DcServiceException("获取失败");
////        }
////
////        if ( jsonObjectRes.getInteger("status") != ResultConstant.RES_SUCCESS_CODE) {
////
////            if(jsonObjectRes.get("error")==null){
////                throw new DcServiceException("获取失败");
////            }else{
////                throw new DcServiceException(jsonObjectRes.get("error").toString());
////            }
////        }
////
//        //        return new ObjectResponse<>(jsonObjectRes.get("data"));
//    }

    /**
     * 用电量统计概览柱状图（查询月份及对应的用电量）
     *
     * @param commIdList 当前商户及子商户id列表
     * @return
     */

//    public ObjectResponse<JSONObject> getElectricityByMonth(List<Long> commIdList) {
//
//        ObjectResponse<JSONObject> jsonObjectRes = tradingFeignClient.getElectricityByMonth(commIdList);
//        FeignResponseValidate.check(jsonObjectRes);
//        return jsonObjectRes;
////        if(jsonObjectRes == null || jsonObjectRes.get("status") == null){
////            throw new DcServiceException("获取失败");
////        }
////
////        if ( jsonObjectRes.getInteger("status") != ResultConstant.RES_SUCCESS_CODE) {
////
////            if(jsonObjectRes.get("error")==null){
////                throw new DcServiceException("获取失败");
////            }else{
////                throw new DcServiceException(jsonObjectRes.get("error").toString());
////            }
////        }
//        //        return new ObjectResponse<>(jsonObjectRes.get("data"));
//    }


    /**
     * 用电量统计概览柱状图（查询月份及对应的用电量）
     *
     * @param commIdList 当前商户及子商户id列表
     * @return
     */

//    public ObjectResponse<JSONObject> getOrderCountByMonth(List<Long> commIdList) {
//
//        ObjectResponse<JSONObject> jsonObjectRes = tradingFeignClient.getOrderCountByMonth(commIdList);
//        return jsonObjectRes;
////        if(jsonObjectRes == null || jsonObjectRes.get("status") == null){
////            throw new DcServiceException("获取失败");
////        }
////
////        if ( jsonObjectRes.getInteger("status") != ResultConstant.RES_SUCCESS_CODE) {
////
////            if(jsonObjectRes.get("error")==null){
////                throw new DcServiceException("获取失败");
////            }else{
////                throw new DcServiceException(jsonObjectRes.get("error").toString());
////            }
////        }
//        //        return new ObjectResponse<>(jsonObjectRes.get("data"));
//    }

    /**
     * 获取枪头监控信息
     *
     * @param map
     * @param page
     * @return
     */

//    public ObjectResponse getChargerReportData(List<Long> commIdList, Page<ChargerReportDataVo> page,
//                                               String currentType, String sid, String bcCode) {
//
//        Integer _index = page.getPageNum();
//
//        Integer _size = page.getPageSize();
//
//        JSONObject jsonObjectRes = tradingFeignClient.getChargerReportData(commIdList, _index, _size, currentType, sid, bcCode);
//
//        if (jsonObjectRes == null || jsonObjectRes.get("status") == null) {
//            throw new DcServiceException("获取失败");
//        }
//
//        if (jsonObjectRes.getInteger("status") != ResultConstant.RES_SUCCESS_CODE) {
//
//            if (jsonObjectRes.get("error") == null) {
//                throw new DcServiceException("获取失败");
//            } else {
//                throw new DcServiceException(jsonObjectRes.get("error").toString());
//            }
//        }
//        return new ObjectResponse<>(jsonObjectRes.get("data"));
//
//    }


}