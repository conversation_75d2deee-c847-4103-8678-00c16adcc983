package com.cdz360.biz.ant.service.download;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.biz.ant.feign.BizBiFeignClient;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.model.download.param.DownloadFileParam;
import com.cdz360.biz.model.download.param.ListDownloadJobParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadJobStatus;
import com.cdz360.biz.model.download.vo.DownloadJobVo;
import feign.Response;
import java.util.Locale;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.buffer.DefaultDataBuffer;
import org.springframework.core.io.buffer.DefaultDataBufferFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Service;
import org.springframework.util.StreamUtils;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

@Slf4j
@Service
public class DownloadFileService {

    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;

    @Autowired
    private BizBiFeignClient bizBiFeignClient;

    public Mono<ListResponse<DownloadJobVo>> userDownloadJob(Long sysUid, Locale locale) {
        ListDownloadJobParam jobParam = new ListDownloadJobParam();
        jobParam.setUid(sysUid)
            .setStatusList(List.of(
                DownloadJobStatus.WAIT,
                DownloadJobStatus.PROCESSING,
//                        DownloadJobStatus.PROCESSING_FAIL,
                DownloadJobStatus.COMPLETED))
            .setSize(10);
        jobParam.setLocale(locale);
        return dataCoreDownloadFileFeignClient.downloadApplyList(jobParam);
    }

    public Mono<Void> downloadFile(DownloadFileParam param, ServerHttpResponse response) {
        List<DownloadFileType> typeList = List.of(DownloadFileType.EXCEL_XLSX,
            DownloadFileType.PDF);
        if (!typeList.contains(param.getFileType())) {
            throw new DcArgumentException("不支持的文件类型");
        }

        String fileName = param.getSubFileName() + "." + param.getFileType().getSuffix();
        response.getHeaders().set(HttpHeaders.CONTENT_DISPOSITION,
            "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
        response.getHeaders().setContentType(MediaType.APPLICATION_OCTET_STREAM);

        Response.Body body = bizBiFeignClient.downloadFile(param).body();
        DefaultDataBuffer bf = new DefaultDataBufferFactory().allocateBuffer();
        try {
            StreamUtils.copy(body.asInputStream(), bf.asOutputStream());
        } catch (Exception e) {
            log.error("下载文件失败: err = {}", e.getMessage(), e);
        }
        return response.writeWith(Mono.just(bf));
    }

    public Mono<Void> downloadPrintFile(DownloadFileParam param, ServerHttpResponse response) {
        log.info("开始downloadPrintFile");
        List<DownloadFileType> typeList = List.of(DownloadFileType.EXCEL_XLSX,
                DownloadFileType.PDF);
        if (!typeList.contains(param.getFileType())) {
            throw new DcArgumentException("不支持的文件类型");
        }

        String fileName = param.getSubFileName() + "." + param.getFileType().getSuffix();
        return Mono.fromCallable(() -> {
            Response.Body body = bizBiFeignClient.downloadFile(param).body();
            return body.asInputStream();
        })
                .subscribeOn(Schedulers.boundedElastic())
                .flatMap(inputStream -> {
                    DefaultDataBuffer bf = new DefaultDataBufferFactory().allocateBuffer();
                    try {
                        StreamUtils.copy(inputStream, bf.asOutputStream());
                    } catch (Exception e) {
                        log.error("下载文件失败: err = {}", e.getMessage(), e);
                    }
                    response.getHeaders().set(HttpHeaders.CONTENT_DISPOSITION,
                            "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
                    response.getHeaders().setContentType(MediaType.APPLICATION_OCTET_STREAM);
                    return response.writeWith(Mono.just(bf));

//                    DataBuffer buffer = null;
//                    try {
//                        buffer = response.bufferFactory().wrap(inputStream.readAllBytes());
//                    } catch (Exception e) {
//                        log.error("下载文件失败: err = {}", e.getMessage(), e);
//                    }
//                    response.setStatusCode(HttpStatus.OK);
//                    response.getHeaders().set(HttpHeaders.CONTENT_TYPE, "application/pdf");
//                    response.getHeaders().set(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
//                    return response.writeWith(Mono.just(buffer));
                })
                .then();
    }
}
