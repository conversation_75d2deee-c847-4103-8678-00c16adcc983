package com.cdz360.biz.ant.service.order;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.param.SortParam;
import com.cdz360.base.model.base.type.ChargeOrderStatus;
import com.cdz360.base.model.base.type.OrderType;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.domain.ChargerOrder;
import com.cdz360.biz.ant.domain.vo.ChargerOrderFinishVo;
import com.cdz360.biz.ant.feign.BizBiFeignClient;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.DeviceFeignClient;
import com.cdz360.biz.ant.feign.TradingFeignClient;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorBizBiFeignClient;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.cus.corp.type.LimitCycle;
import com.cdz360.biz.model.cus.discount.vo.DiscountStrategyVo;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.order.param.ListSettlementOrderParam;
import com.cdz360.biz.model.order.param.NotInSettlementOrderParam;
import com.cdz360.biz.model.trading.order.dto.LowKwOrderDto;
import com.cdz360.biz.model.trading.order.param.GetOrderDetailParam;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.param.ListChargerOrderParamX;
import com.cdz360.biz.model.trading.order.param.OrderTimeDivisionDiscountParam;
import com.cdz360.biz.model.trading.order.po.ChargerOrderTimeDivision;
import com.cdz360.biz.model.trading.order.vo.SettlementOrderVo;
import com.cdz360.biz.utils.feign.his.HisOrderFeignClient;
import com.cdz360.biz.utils.feign.order.BiOrderFeignClient;
import com.cdz360.biz.utils.feign.order.DataCoreOrderFeignClient;
import com.cdz360.biz.utils.feign.user.UserFeignClient;
import com.chargerlinkcar.framework.common.domain.CorpListPointLogParam;
import com.chargerlinkcar.framework.common.domain.NoCardPayAccountInfo;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.PointLog;
import com.chargerlinkcar.framework.common.domain.SitePersonaliseDTO;
import com.chargerlinkcar.framework.common.domain.order.ChargerOrderWithBLOBs;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDataVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDetailVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUserVo;
import com.chargerlinkcar.framework.common.domain.vo.UpdateOrderVo;
import com.chargerlinkcar.framework.common.service.AsyncCusBalanceService;
import com.chargerlinkcar.framework.common.service.DcCusBalanceService;
import com.chargerlinkcar.framework.common.service.discount.DiscountDealWithService;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.HistoryDataUtils;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.github.pagehelper.Page;
import feign.Response;
import jakarta.annotation.Nullable;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.buffer.DefaultDataBuffer;
import org.springframework.core.io.buffer.DefaultDataBufferFactory;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR> ${description}
 * @since 2018/11/28 11:20
 */
@Slf4j
@Service
public class ChargerOrderService {

    final private static Integer USER_STATUS_NORMAL = 10001;
    @Autowired
    private TradingFeignClient tradingFeignClient;
    @Autowired
    private BizBiFeignClient bizBiFeignClient;
    @Autowired
    private ReactorBizBiFeignClient asyncBizBiFeignClient;
    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private DeviceFeignClient deviceFeignClient;
    @Autowired
    private DcCusBalanceService dcCusBalanceService;
    @Autowired
    private AsyncCusBalanceService asyncCusBalanceService;
    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;
    @Autowired
    private DataCoreOrderFeignClient dataCoreOrderFeignClient;
    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;
    @Autowired
    private BiOrderFeignClient biOrderFeignClient;
    @Autowired
    private DiscountDealWithService discountDealWithService;
    @Autowired
    private HisOrderFeignClient hisOrderFeignClient;
    @Autowired
    private HistoryDataUtils historyDataUtils;

    /**
     * 注意注意注意!!!!!!!
     * <p>测试阶段，不建议使用</p>
     *
     * @param param
     * @return
     */
    public Mono<ListResponse<ChargerOrderVo>> queryChargeOrderListX(ListChargerOrderParamX param) {
        return biOrderFeignClient.queryChargeOrderListX(param);
    }

    /**
     * 分页查询订单列表 订单来源类型,0:微信渠道;1:APP在线发起充电;2:APP蓝牙发起充电:3:刷卡充电:4:桩上报离线数据 (离线订单 列表  channelId 为4的时候是
     * 离线订单列表。)
     *
     * @param searchParam 对象查询参数
     * @return
     */

    public ListResponse<ChargerOrderVo> queryChargeOrderList(ChargerOrderParam searchParam) {
        ListResponse<ChargerOrderVo> jsonObjectRes = null;
        if (historyDataUtils.checkListOrderParam(searchParam, true)) {
            jsonObjectRes = bizBiFeignClient.queryChargeOrderList(
                searchParam);
        } else {
            jsonObjectRes = hisOrderFeignClient.getOrderList(searchParam)
                .block(Duration.ofSeconds(50L));
        }
        FeignResponseValidate.check(jsonObjectRes);
        log.debug("list.size = {}", jsonObjectRes.getData().size());
        return jsonObjectRes;
    }

    /**
     * 根据条件查询订单统计数据
     *
     * @param searchParam
     * @return
     */

    public ObjectResponse<ChargerOrderDataVo> getChargerOrderData(ChargerOrderParam searchParam) {

        ObjectResponse<ChargerOrderDataVo> res = null;

        res = bizBiFeignClient.getChargerOrderData(searchParam);

        FeignResponseValidate.check(res);
        return res;
    }

    /**
     * 根据条件查询订单统计数据(尖峰平谷)
     *
     * @param searchParam
     * @return
     */

    public Mono<ObjectResponse<ChargerOrderDetailVo>> getChargerOrderDetail(
        ChargerOrderParam searchParam) {
        Mono<ObjectResponse<ChargerOrderDetailVo>> mono = null;
        if (historyDataUtils.checkListOrderParam(searchParam, true)) {
            mono = asyncBizBiFeignClient.getChargerOrderDetail(searchParam);
        } else {
            mono = hisOrderFeignClient.getChargeOrderCountData(searchParam);
        }
        mono.doOnNext(res -> FeignResponseValidate.check(res));
        return mono;
    }


    public Mono<ListResponse<ChargerOrderTimeDivision>>
    getDivisionDiscount(OrderTimeDivisionDiscountParam param) {

        return discountDealWithService.getDiscountStrategy(param.getDiscountRefId())
            .doOnNext(e -> e.ifPresentOrElse(
                    ie -> discountDealWithService.timeDivisionDiscount(ie, param.getList()),
                    () -> IotAssert.isTrue(false, "协议价不存在")
                )
            ).map(e -> param.getList())
            .map(RestUtils::buildListResponse);
    }

    public Mono<ObjectResponse<DiscountStrategyVo>> getDiscountStrategy(Long discountRefId) {
        return discountDealWithService.getDiscountStrategy(discountRefId)
            .map(e -> e.orElseThrow(
                () -> new DcServiceException("找不到协议策略: " + discountRefId)))
            .map(RestUtils::buildObjectResponse);
    }


    public Mono<ObjectResponse<ChargerOrderWithBLOBs>> queryOrderDetail2(String commIdChain,
        String orderNo,
        @Nullable Long opCommId,
        List<String> gids) {
        log.info("orderNo = {}, commIdChain = {}, opCommId = {}, gids = {}",
            orderNo, commIdChain, opCommId, gids);
        if (StringUtils.isBlank(orderNo)) {
            throw new DcArgumentException("参数出错");
        }

        GetOrderDetailParam param = new GetOrderDetailParam();
        param.setCommIdChain(commIdChain)
            .setOrderNo(orderNo)
            .setCommId(opCommId)
            .setGids(gids);
//        ObjectResponse<ChargerOrderWithBLOBs> jsonObjectRes =
        return dataCoreOrderFeignClient.getOrderDetail(param)
            .doOnNext(jsonObjectRes -> {
                FeignResponseValidate.check(jsonObjectRes);
                ChargerOrderWithBLOBs chargerOrderWithBLOBsVo = jsonObjectRes.getData();
                //log.info("queryOrderDetail订单数据:{}", chargerOrderWithBLOBsVo);
                Long priceSchemeId = chargerOrderWithBLOBsVo.getPriceSchemeId();

                /**
                 * 转换枪头编号格式：桩号-枪头序号
                 */
                String bcCode = chargerOrderWithBLOBsVo.getBoxCode() + "-"
                    + chargerOrderWithBLOBsVo.getConnectorId();
                chargerOrderWithBLOBsVo.setBcCode(bcCode);

                // 可开票金额和不可开票金额
                if (chargerOrderWithBLOBsVo.getInvoicedId() > 0) { // 已开票
                    chargerOrderWithBLOBsVo.setAlreadyInvoiceAmount(
                        chargerOrderWithBLOBsVo.getPrincipalAmount());
                    chargerOrderWithBLOBsVo.setNoInvoiceAmount(BigDecimal.ZERO);
                } else {
                    if (null != chargerOrderWithBLOBsVo.getPrincipalAmount() &&
                        null != chargerOrderWithBLOBsVo.getInvoicedAmount()) {
                        chargerOrderWithBLOBsVo.setNoInvoiceAmount(
                            chargerOrderWithBLOBsVo.getPrincipalAmount()
                                .subtract(chargerOrderWithBLOBsVo.getInvoicedAmount()));
                        chargerOrderWithBLOBsVo.setAlreadyInvoiceAmount(
                            chargerOrderWithBLOBsVo.getInvoicedAmount());
                    }
                }

                // 若订单为充电中，下列字段返回null，页面需显示为“--”
                if (chargerOrderWithBLOBsVo.getOrderStatus() != null
                    && chargerOrderWithBLOBsVo.getOrderStatus() == ChargeOrderStatus.START) {
                    chargerOrderWithBLOBsVo.setAlreadyInvoiceAmount(null)
                        .setNoInvoiceAmount(null)
                        .setPrincipalAmount(null)
                        .setFreeGoldAmount(null);
                }
            });

//        return new ObjectResponse<>(chargerOrderWithBLOBsVo);

    }


    /**
     * 分页查询枪头订单列表
     *
     * @param page              分页
     * @param boxOutFactoryCode 桩号
     * @param connectorId       枪头
     * @param beginTime         开始时间
     * @param endTime           结束时间
     * @param status            订单状态
     * @param channelId
     * @return
     */

    public ListResponse<ChargerOrder> selectChargerOrderListByConnectId(OldPageParam page,
        String boxOutFactoryCode, String connectorId, String beginTime,
        String endTime, String status, Integer channelId) {

        Integer _index = page.getPageNum();

        Integer _size = page.getPageSize();

        if (StringUtils.isNotBlank(endTime)) {
            // 结束时间，取当日闭区间
            try {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(new Date(Long.valueOf(endTime)));
                calendar.add(Calendar.DATE, 1);
                endTime = String.valueOf(calendar.getTime().getTime());
            } catch (Exception ex) {
                log.error("日期处理错误: {}", endTime);
            }
        }

        ListResponse<ChargerOrder> jsonObjectRes = tradingFeignClient.selectChargerOrderListByConnectId(
            null, _index, _size,
            boxOutFactoryCode, connectorId, beginTime,
            endTime, status, channelId);
        FeignResponseValidate.check(jsonObjectRes);
        return jsonObjectRes;

    }

    /**
     * 查询分时订单列表
     *
     * @param orderNo 订单id
     */

    public ListResponse<ChargerOrderTimeDivision> queryOrderTimeDivisionList(String orderNo) {

        //String orderIdStr = String.valueOf(orderId);
        ListResponse<ChargerOrderTimeDivision> jsonObjectRes = tradingFeignClient.queryOrderTimeDivisionList(
            orderNo);
        if (jsonObjectRes == null || CollectionUtils.isEmpty(jsonObjectRes.getData())) {
            // 充电中订单
            ObjectResponse<Boolean> existsObject = hisOrderFeignClient.queryOrderExists(orderNo)
                .block(Duration.ofSeconds(50L));
            if (existsObject != null && Boolean.TRUE.equals(existsObject.getData())) {
                jsonObjectRes = hisOrderFeignClient.queryOrderTimeDivisionList(orderNo)
                    .block(Duration.ofSeconds(50L));
            }
        }
        FeignResponseValidate.check(jsonObjectRes);
        return jsonObjectRes;

    }


    public BaseResponse updateOrder(UpdateOrderVo order) {
        log.info(">> Feign 远程调用手动修改异常订单: order={}.", order);
        BaseResponse result = tradingFeignClient.updateOrder(order);
        log.info(">> 手动修改异常订单返回结果: order={}.", order);
        return result;
    }


    public ObjectResponse<ChargerOrderFinishVo> queryFinishedOrderDetail(String orderNo) {
        log.info(">> Feign 远程调用查询订单: orderNo={}", orderNo);
        ObjectResponse<ChargerOrderFinishVo> objectRes = tradingFeignClient.getFinishedOrderDetail(
            orderNo);
        FeignResponseValidate.check(objectRes);
        log.info(">> Feign 远程调用查询订单的返回结果:", JsonUtils.toJsonString(objectRes));
        return objectRes;
    }


    public Mono<ObjectResponse<ExcelPosition>> checkAndWriteTempExcelByChargeOrderList(
        AppClientType clientType, Long sysUid, ChargerOrderParam searchParam) {
        //检查是否超出数据量
        ObjectResponse<ChargerOrderDataVo> checkOverRes = bizBiFeignClient.getChargerOrderData(
            searchParam);
        FeignResponseValidate.check(checkOverRes);
        Long orderNumber = checkOverRes.getData().getChargerOrderNumber();
        boolean isOver = orderNumber != null && orderNumber > 100000L;
        if (isOver) {
            throw new DcServiceException("数据量超过10万条,无法导出");
        }

        if (AppClientType.MGM_WEB.equals(clientType)) {
            DownloadApplyParam applyParam = new DownloadApplyParam();
            applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
                .setUid(sysUid)
                .setExFileName("订单汇总")
                .setFunctionMap(DownloadFunctionType.CHARGER_ORDER)
                .setReqParam(JsonUtils.toJsonString(searchParam));
            return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
        } else {
//            // 异步写入临时文件
//            ObjectResponse<ExcelPosition> res = ostFeignClient.writeTempExcelByChargeOrderList(searchParam);
//            FeignResponseValidate.check(res);
            return Mono.just(bizBiFeignClient.writeTempExcelByChargeOrderList(searchParam));
        }
    }

    /**
     * 检查并导出车辆详情订单信息
     *
     * @param searchParam
     * @return
     */
    public Mono<ObjectResponse<ExcelPosition>> checkAndWriteTempExcelByVinOrderList(
        ServerHttpRequest request, ChargerOrderParam searchParam) {
        //检查是否超出数据量
        ObjectResponse<ChargerOrderDataVo> checkOverRes = bizBiFeignClient.getChargerOrderData(
            searchParam);
        FeignResponseValidate.check(checkOverRes);
        Long orderNumber = checkOverRes.getData().getChargerOrderNumber();
        boolean isOver = orderNumber != null && orderNumber > 100000L;
        if (isOver) {
            throw new DcServiceException("数据量超过10万条,无法导出");
        }

        final AppClientType clientType = AntRestUtils.getAppClientType(request);
        if (AppClientType.MGM_WEB.equals(clientType)) {
            Long sysUid = AntRestUtils.getSysUid(request);
            DownloadApplyParam applyParam = new DownloadApplyParam();
            applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
                .setUid(sysUid)
                .setExFileName("车辆订单")
                .setFunctionMap(DownloadFunctionType.VIN_ORDER)
                .setReqParam(JsonUtils.toJsonString(searchParam));
            return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
        } else {
            //异步写入临时文件
//            ObjectResponse<ExcelPosition> res = ostFeignClient.exportVinOrderList(searchParam);
//            FeignResponseValidate.check(res);
            return Mono.just(bizBiFeignClient.exportVinOrderList(searchParam))
                .doOnNext(FeignResponseValidate::check);
        }
    }

    /**
     * 商户会员订单导出检测
     *
     * @param searchParam
     * @return
     */
    public ExcelPosition checkAndWriteTempExcelByCommUserOrderList(ChargerOrderParam searchParam) {
        //检查是否超出数据量
        ObjectResponse<ChargerOrderDataVo> checkOverRes = bizBiFeignClient.getChargerOrderData(
            searchParam);
        FeignResponseValidate.check(checkOverRes);
        Long orderNumber = checkOverRes.getData().getChargerOrderNumber();
        boolean isOver = orderNumber != null && orderNumber > 100000L;
        if (isOver) {
            throw new DcServiceException("数据量超过10万条,无法导出");
        }

        //异步写入临时文件
        ObjectResponse<ExcelPosition> res = bizBiFeignClient.exportCommUserOrderList(searchParam);
        FeignResponseValidate.check(res);
        return res.getData();
    }


    public ExcelPosition exportExcelTrade(CorpListPointLogParam searchParam) {
        //检查是否超出数据量
        searchParam.setTotal(true);
        searchParam.setSize(1); // 这里只要是查总数，所以设置1
        searchParam.setPayAccountType(PayAccountType.PERSONAL);
        ListResponse<PointLog> checkRes = dcCusBalanceService.listPointLog(searchParam);
        FeignResponseValidate.check(checkRes);
        IotAssert.isTrue(checkRes.getTotal() < 100000, "数据量超过10万条,无法导出");

        //异步写入临时文件
        ObjectResponse<ExcelPosition> res = bizBiFeignClient.exportExcelTrade(searchParam);
        FeignResponseValidate.check(res);
        return res.getData();
    }


    public boolean existsDownFile(String subDir, String type, String subFileName) {
        if ("pdf".equals(type)) {
            ObjectResponse<Boolean> res = bizBiFeignClient.checkFileCompeleted(subDir, subFileName);
            FeignResponseValidate.check(res);
            return res.getData();
        } else if ("xlsx".equals(type)) {
            ObjectResponse<Boolean> res = bizBiFeignClient.checkExcelFileCompeleted(type, subDir,
                subFileName);
            FeignResponseValidate.check(res);
            return res.getData();
        } else {
            throw new DcArgumentException("不支持的文件类型");
        }
    }


    public Mono<Void> downFile(String subDir, String type, String subFileName,
        ServerHttpResponse response) {
        List<String> typeList = List.of("xlsx", "pdf");
        if (!typeList.contains(type)) {
            throw new DcArgumentException("不支持的文件类型");
        }
        if (existsDownFile(subDir, type, subFileName)) {
            String fileName = subFileName + "." + type;

            try {
//                ServerHttpResponse zeroCopyResponse = (ServerHttpResponse) response;
                response.getHeaders()
                    .setContentType(MediaType.valueOf("application/vnd..ms-excel"));
//                response.setContentType("application/vnd..ms-excel");
                response.getHeaders().add("content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
//                response.setHeader("content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
                //读取指定路径下面的文件
                //InputStream in = new FileInputStream(filePath);
                Response res = null;
                if ("pdf".equals(type)) {
                    res = bizBiFeignClient.download(subDir, subFileName);
                } else if ("xlsx".equals(type)) {
                    res = bizBiFeignClient.download(type, subDir, subFileName);
                }
                Response.Body body = res.body();

                InputStream in = body.asInputStream();

//               BufferedInputStream bis = new BufferedInputStream(body.asInputStream());
//               FileInputStream fis = new FileInputStream(bis);
//               File f = new File(bis);

//                BodyInserters.fro(bis);

                DefaultDataBuffer bf = new DefaultDataBufferFactory().allocateBuffer();

                byte[] buff = new byte[1024];
                //所读取的内容使用n来接收
                int n;
                //当没有读取完时,继续读取,循环
                OutputStream os = bf.asOutputStream();
                while ((n = in.read(buff)) != -1) {
                    //将字节数组的数据全部写入到输出流中
//                    outputStream.write(buff, 0, n);

                    os.write(buff, 0, n);
                    log.info("write {}", n);

                }
                //强制将缓存区的数据进行输出
//                outputStream.flush();
                //关流
//                outputStream.close();

                os.flush();

                in.close();

//                OutputStream outputStream = new BufferedOutputStream();

                return response.writeWith(Mono.just(bf))
                    .then(Mono.just(Boolean.TRUE))
                    .flatMap(a -> {
                        try {
                            os.close();
                        } catch (IOException e) {
                            log.warn("exception = {}", e.getMessage(), e);
                        }
                        return Mono.empty();
                    });


                //创建存放文件内容的数组

            } catch (Exception e) {
                log.error("导出订单excel失败,{}", e.getMessage());
            }
        } else {
            log.error("excel文件不存在");
        }
        return Mono.empty();
    }


    public Mono<ListResponse<PointLog>> queryPointLogByOrderNo(Long topCommId,
        String orderNo,
        String commIdChain,
        @Nullable Long opCommId,
        List<String> gids) {
//        ObjectResponse<ChargerOrderWithBLOBs> res =
        return queryOrderDetail2(commIdChain, orderNo, opCommId, gids)
            .flatMap(res -> {
                FeignResponseValidate.check(res);
                ChargerOrderWithBLOBs order = res.getData();

                ListResponse<PointLog> logRes = new ListResponse<>();

                if (order.getDefaultPayType() == null || order.getCustomerId() == null
                    || order.getPayAccountId() == null) {
                    log.warn("订单有问题,无法获取资金明细, order:{}", order);
                    return Mono.just(logRes);
                }

                if (PayAccountType.CREDIT.getCode() == order.getDefaultPayType()) {
                    return userFeignClient.selectBlocPointLogsByOrderNo(orderNo);
                } else {

                    CorpListPointLogParam param = new CorpListPointLogParam();
                    param.setPayAccountType(PayAccountType.valueOf(order.getDefaultPayType()))
                        .setTopCommId(topCommId)
                        .setCommId(order.getPayAccountId())
                        .setUid(order.getCustomerId())
                        .setOrderNo(orderNo)
                        .setSorts(List.of(SortParam.as("log.id", OrderType.asc)))
                        .setSize(1000); // 订单流水最大数
                    return asyncCusBalanceService.listPointLog4Corp(param)
                        .map(resx -> {
                            //type not exist
                            if (resx != null && 1100 == resx.getStatus()) {
                                return new ListResponse<>();
                            } else {
                                return resx;
                            }
                        });


                }

//                    return logRes;
            });

    }

    public Mono<ObjectResponse<Integer>> notInSettlementOrders(
        NotInSettlementOrderParam param) {
        IotAssert.isNotNull(param.getCorpId(), "企业客户ID不能为空");
        IotAssert.isNotNull(param.getSettStartDateDay(), "账期结束年月不能为空");
        IotAssert.isNotNull(param.getSettEndDateDay(), "账期结束年月不能为空");
        return dataCoreOrderFeignClient.notInSettlementOrders(param);
    }

    public ListResponse<SettlementOrderVo> getNotSettlementOrderList(
        ListSettlementOrderParam param) {
        return dataCoreFeignClient.getNotSettlementOrderList(param);
    }

    public BaseResponse payNoCardOrderBySiteId(String siteId) {

        ObjectResponse<SitePersonaliseDTO> personalise = this.dataCoreFeignClient.getPersonalise(
            siteId);
        FeignResponseValidate.check(personalise);

        NoCardPayAccountInfo noCardPayAccountInfo = personalise.getData().getNoCardPayAccountInfo();
        IotAssert.isNotNull(noCardPayAccountInfo, "场站未设置支付账号");

        IotAssert.isTrue(noCardPayAccountInfo.getEnable() == 1 &&
                USER_STATUS_NORMAL.equals(noCardPayAccountInfo.getCusStatus()),
            "用户当前状态异常，无法支付");

        boolean needCheck = true;
        if (SettlementType.POSTPAID.equals(noCardPayAccountInfo.getSettlementType())) {
            RBlocUserVo corpUser = userFeignClient.findRBlocUserVoById(
                    noCardPayAccountInfo.getAccountId())
                .doOnNext(FeignResponseValidate::check)
                .map(ObjectResponse::getData)
                .block(Duration.ofSeconds(50L));

            needCheck = !LimitCycle.UNLIMITED.equals(corpUser.getLimitCycle());
        }

        if (needCheck) {
            PayAccountType accountType = PayAccountType.valueOf(
                noCardPayAccountInfo.getAccountType());
            String tips = PayAccountType.COMMERCIAL.equals(accountType) ? "商户会员" : "授信账户";
            IotAssert.isNotNull(noCardPayAccountInfo.getAvailable(), tips + "无可用金额");

            ChargerOrderParam searchParam = new ChargerOrderParam();
            searchParam.setCommStationIds(List.of(siteId))
                .setStatusList(List.of(800))
                .setOrderTypeList(List.of(OrderStartType.EVSE_AUTO.getCode()));
            ObjectResponse<ChargerOrderDetailVo> res = bizBiFeignClient.getChargerOrderDetail(
                searchParam);
            FeignResponseValidate.check(res);
            IotAssert.isNotNull(res.getData().getOrderPriceAmount().getTotal(),
                "场站下待支付订单无合计金额");

            BigDecimal orderPriceTotal = null;
            try {
                orderPriceTotal = new BigDecimal(
                    res.getData().getOrderPriceAmount().getTotal().replaceAll(",", ""));
            } catch (Exception e) {
                IotAssert.isTrue(false,
                    "无法解析订单总金额: " + res.getData().getOrderPriceAmount().getTotal());
            }

            IotAssert.isTrue(DecimalUtils.gte(noCardPayAccountInfo.getAvailable(),
                orderPriceTotal), tips + "可用金额不足，请充值后重试");
        }

        return tradingFeignClient.payNoCardOrderBySiteId(siteId, noCardPayAccountInfo);
    }

    public ListResponse<LowKwOrderDto> getLowKwOrderList(ListChargeOrderParam param) {
        var res = this.bizBiFeignClient.getLowKwOrderList(param);
        FeignResponseValidate.check(res);
        return res;
    }
}
