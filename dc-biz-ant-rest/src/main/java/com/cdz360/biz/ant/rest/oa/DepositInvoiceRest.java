package com.cdz360.biz.ant.rest.oa;

import static com.cdz360.biz.model.oa.constant.OaConstants.PD_KEY_DEPOSIT_PROCESS;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.reactor.DataCoreInvoiceFeignClient;
import com.cdz360.biz.ant.feign.reactor.OaFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorUserFeignClient;
import com.cdz360.biz.ant.rest.InvoiceProcesser;
import com.cdz360.biz.ant.service.DepositInvoiceService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.cus.corp.vo.CorpSimpleVo;
import com.cdz360.biz.model.cus.settlement.param.SettlementEditParam;
import com.cdz360.biz.model.trading.invoice.dto.InvoicedRecordDto;
import com.cdz360.biz.model.trading.invoice.po.CorpInvoiceRecordPo;
import com.cdz360.biz.model.trading.order.dto.PayBillInvoiceBi;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.model.trading.order.vo.PayBillVo;
import com.cdz360.biz.model.trading.site.po.InvoicedRecordPo;
import com.cdz360.biz.oa.param.DepositInvoiceParam;
import com.cdz360.biz.oa.param.DepositInvoiceProcessParam;
import com.cdz360.biz.oa.param.DepositInvoiceUserParam;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * DepositInvoiceRest
 *  充值开票
 * @since 3/8/2023 4:22 PM
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/oa/depositInvoice")
public class DepositInvoiceRest {

    public static final InvoicingMode CORP_SUPPORT_MODE = InvoicingMode.PRE_PAY;

    @Autowired
    private OaFeignClient oaFeignClient;

    @Autowired
    private DataCoreInvoiceFeignClient dataCoreInvoiceFeignClient;

    @Autowired
    private ReactorUserFeignClient userFeignClient;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private InvoiceProcesser invoiceProcesser;

    @Autowired
    private DepositInvoiceService depositInvoiceService;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;


    @ApiOperation(value = "企客充值开票流程提交")
    @PostMapping("/corpStartProcess")
    public Mono<ObjectResponse<String>> startDepositCorpProcess(
        ServerHttpRequest request, @RequestBody DepositInvoiceParam param) {
        IotAssert.isNotNull(param, "请传入入参");

        IotAssert.isTrue(
            PayAccountType.CORP.equals(param.getAccountType()), "目前仅支持企业开票审批");

        IotAssert.isTrue(CORP_SUPPORT_MODE.equals(param.getInvoiceWay()),
            "企业仅支持开票类型: " + CORP_SUPPORT_MODE.getDesc());

        log.info("企客充值开票流程提交: {}", JsonUtils.toJsonString(param));

        IotAssert.isNotNull(param.getCorpInvoiceInfoVo(), "请传入抬头");
        param.setChannel(param.getCorpInvoiceInfoVo().getChannel());

        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getInvoiceRecords()), "请传入开票内容");

        param.checkAndFilterField();

        ListResponse<CorpSimpleVo> response = authCenterFeignClient.getCorpByCommId(
            AntRestUtils.getToken2(request),
            AntRestUtils.getCommIdChain(request), param.getCorpId());
        if (response == null || CollectionUtils.isEmpty(response.getData())) {
            throw new DcServiceException("企客信息不存在");
        }

        // FIXME 可能走不到这里，因为禁用的企业，不会被返回
        final CorpSimpleVo corpSimpleVo = response.getData().get(0);
        IotAssert.isTrue(corpSimpleVo.getEnable(), "开票企业状态异常");
        param.setCorpName(corpSimpleVo.getCorpName());

        return Mono.just(param)
            .flatMap(e ->
                Mono.fromCallable(() -> {
                        PayBillParam payBillParam = new PayBillParam();
                        payBillParam.setOrderIdList(param.getOrderNoList());
                        ListResponse<PayBillInvoiceBi> payBillRes = dataCoreFeignClient.invoiceOrderList(
                            payBillParam);
                        if (payBillRes != null && CollectionUtils.isNotEmpty(payBillRes.getData())) {
                            payBillRes.getData().forEach(payBillInvoiceBi -> {
                                IotAssert.isNotNull(payBillInvoiceBi, "充值单不存在");

                                IotAssert.isTrue(
                                    StringUtils.isBlank(payBillInvoiceBi.getRunProcInstId()),
                                    "该充值单有正在处理中的余额减少流程： procInstId: "
                                        + payBillInvoiceBi.getRunProcInstId() + ", 充值单号:"
                                        + payBillInvoiceBi.getOrderId());
                            });
                        }
                        return e;
                    })
                    .subscribeOn(Schedulers.boundedElastic())
            )
            .flatMap(e -> invoiceProcesser.corpAppendOrderByOa(request, PD_KEY_DEPOSIT_PROCESS,
                    null, null,
                    param.getCorpId(), param.getOrderNoList()) // STEP1.模拟企业客户开票追加订单
                .flatMap(recordDetail -> {
                    param.setApplyNo(recordDetail.getApplyNo());
                    return depositInvoiceService.invoiceSubmit2Audit(request, recordDetail,
                        param); // STEP2.模拟企业客户开票记录提交到审核
                })
                .map(t -> e))
            .switchIfEmpty(Mono.just(param))
            .flatMap(e -> {
                return this.startDepositProcess(request, param); // STEP3.创建企客充值开票流程
            })
            .flatMap(objectResponse -> {
                return Mono.just(objectResponse)
                    .filter(e -> param.getApplyNo() != null) // STEP4.判断申请单号是否为空
                    .flatMap(resp -> {
                        CorpInvoiceRecordPo recordPo = new CorpInvoiceRecordPo();
                        recordPo.setApplyNo(param.getApplyNo())
                            .setProcInstId(resp.getData());
                        // STEP5.流程实例ID写入到企业开票记录
                        return dataCoreInvoiceFeignClient.updateCorpInvoiceRecord(recordPo)
                            .doOnNext(FeignResponseValidate::check)
                            .filter(e -> CollectionUtils.isNotEmpty(param.getOrderNoList()))
                            .map(e -> {
                                SettlementEditParam dto = new SettlementEditParam();
                                dto.setBillNoList(param.getOrderNoList())
                                    .setProcInstId(resp.getData());
                                return dto;
                            })
//                            .flatMap(e -> userFeignClient.updateSettlementBatch(
//                                e)) // STEP6.流程实例ID写入到结算单记录
//                            .doOnNext(FeignResponseValidate::check)
                            .map(e -> resp)
                            .switchIfEmpty(Mono.just(resp));
                    })
                    .switchIfEmpty(Mono.just(objectResponse));
            });
    }


    @ApiOperation(value = "个人&商户会员充值开票流程提交")
    @PostMapping("/userStartProcess")
    public Mono<ObjectResponse<String>> userStartProcess(
        ServerHttpRequest request, @RequestBody DepositInvoiceUserParam param) {

        IotAssert.isTrue(
            List.of(PayAccountType.PERSONAL, PayAccountType.COMMERCIAL)
                .contains(param.getAccountType()), "目前仅支持个人或商户会员开票审批");

        log.info("个人&商户会员充值开票流程提交: {}", JsonUtils.toJsonString(param));
        param.checkAndFilterField();

        return Mono.just(param)
            .flatMap(e -> userFeignClient.checkAccountStatus(e.getAccountType(),
                    e.getCommId(),
                    e.getUserId())
                .doOnNext(FeignResponseValidate::check)
                .map(ObjectResponse::getData)
                .doOnNext(chk -> {
                    log.info("chk: {}", chk);
                    IotAssert.isTrue(chk, "开票帐户状态异常");
                })
                .map(m -> e))
            .flatMap(e ->
                Mono.fromCallable(() -> {
                        PayBillParam payBillParam = new PayBillParam();
                        payBillParam.setOrderIdList(param.getOrderNoList());
                        ListResponse<PayBillVo> payBillRes = dataCoreFeignClient.payBillList(
                            payBillParam);
                        if (payBillRes != null && CollectionUtils.isNotEmpty(
                            payBillRes.getData())) {
                            payBillRes.getData().forEach(payBillVo -> {
                                IotAssert.isNotNull(payBillVo, "充值单不存在");

                                IotAssert.isTrue(
                                    StringUtils.isBlank(payBillVo.getRunProcInstId()),
                                    "该充值单有正在处理中的余额减少流程： procInstId: "
                                        + payBillVo.getRunProcInstId() + ", 充值单号:"
                                        + payBillVo.getOrderId());
                            });
                        }
                        return e;
                    })
                    .subscribeOn(Schedulers.boundedElastic())
            )
            .flatMap(e -> {
                return depositInvoiceService.userInvoiceSubmit2Audit(request,
                    param);// STEP1.模拟个人开票追加订单，个人开票基于充电订单，此处基于充值单，提交审核
            })
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .flatMap(e -> {
                param.setInvRecIds(
                    e.stream().map(InvoicedRecordPo::getId).collect(Collectors.toList()));
                log.info("创建个人和商户开票信息: {}", param.getInvRecIds());
                return this.startDepositProcess(request, param); // STEP3.创建个人、商户充值开票流程

//                return Mono.just(new ObjectResponse<>("1234567"));
//                return this.startBillingProcess(request, param); // STEP3.创建个人、商户充值开票流程
            })
            .flatMap(objectResponse -> {
//                return Mono.just(objectResponse);
                return Mono.just(objectResponse)
                    .filter(e -> CollectionUtils.isNotEmpty(
                        param.getInvRecIds())) // STEP4.判断开票申请记录id是否为空
                    .flatMap(resp -> {
                        InvoicedRecordDto recordPo = new InvoicedRecordDto();
                        recordPo.setId(
                                param.getInvRecIds().get(0)) // 暂时先只支持一张票，后续要开多张票时此处需要调整
                            .setProcInstId(resp.getData());
                        // STEP5.流程实例ID写入到个人&商户会员开票记录
                        return dataCoreInvoiceFeignClient.updateUserInvoiceRecord(recordPo)
                            .doOnNext(FeignResponseValidate::check)
                            .map(e -> resp)
                            .switchIfEmpty(Mono.just(resp));
                    })
                    .switchIfEmpty(Mono.just(objectResponse));
            });
    }

    /**
     * STEP3.创建企客、个人、商户充值开票流程
     */
    private Mono<ObjectResponse<String>> startDepositProcess(ServerHttpRequest request,
        DepositInvoiceParam param) {
        DepositInvoiceProcessParam processParam = new DepositInvoiceProcessParam(
            AntRestUtils.getSysUid(request),
            AntRestUtils.getSysUserName(request),
            AntRestUtils.getSysUserPhone(request),
            AntRestUtils.getTopCommId(request),
            AntRestUtils.getCommIdChain(request),
            param);
        return this.oaFeignClient.depositInvoiceStartProcess(processParam)
            .doOnNext(FeignResponseValidate::check);
    }
}