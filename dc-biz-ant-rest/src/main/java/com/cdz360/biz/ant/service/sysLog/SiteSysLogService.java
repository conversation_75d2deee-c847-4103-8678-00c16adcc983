package com.cdz360.biz.ant.service.sysLog;

import com.cdz360.base.model.base.vo.KvAny;
import com.cdz360.base.model.base.vo.KvObject;
import com.cdz360.biz.auth.user.type.LogOpType;
import com.cdz360.biz.model.site.type.SiteStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 场站相关的系统操作日志
 */
@Slf4j
@Service
public class SiteSysLogService {

    @Autowired
    private BaseSysLogService sysUserLogService;

    /**
     * 新增场站日志
     */
    public void addSiteLog(String siteName, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                KvAny.of("站点名称", siteName),
                request);
    }

    /**
     * 修改场站状态日志
     */
    public void modifySiteStatusLog(String siteName, SiteStatus siteStatus, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY_STATUS,
                List.of(KvAny.of("站点名称", siteName),
                        KvAny.of("场站状态", siteStatus.getDesc())),
                request);
    }

    /**
     * 修改场站日志
     */
    public void modifySiteLog(String siteName, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("站点名称", siteName),
                request);
    }

    /**
     * 变更场站个性化设置日志
     */
    public void updatePersonaliseLog(String siteName, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("站点名称", siteName),
                request);
    }

    /**
     * 变更场站个性化-停车费优惠设置日志
     */
    public void updatePersonaliseLogPark(String siteName, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                List.of(KvAny.of("站点名称", siteName),
                        KvAny.of("停车费优惠策略", siteName)),
                request);
    }

    /**
     * 新增计费模板
     */
    public void addPriceSchema(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                KvAny.of("计费模板名", name),
                request);
    }

    /**
     * 修改计费模板
     */
    public void updatePriceSchema(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("计费模板名", name),
                request);
    }

    /**
     * 修改计费模板状态
     */
    public void updatePriceSchemaStatus(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY_STATUS,
                KvAny.of("计费模板名", name),
                request);
    }

    /**
     * 下发桩配置
     */
    public void downPriceTemplate(String priceSchemeName, List<String> evseList, Boolean siteDefault, String siteName, ServerHttpRequest request) {
        KvObject object1 = new KvObject();
        object1.setKey("计费模板名");
        object1.setValue(priceSchemeName);
        KvObject object2 = new KvObject();
        object2.setKey("电桩编号");
        object2.setValue(evseList);
        this.sysUserLogService.buildObjectOpLog(LogOpType.DELIVERY,
                List.of(object1, object2),
                request);

        if (siteDefault) {
            object2.setKey("站点名称");
            object2.setValue(siteName);
            this.sysUserLogService.buildObjectOpLog(LogOpType.DELIVERY,
                    List.of(object1, object2),
                    request);
        }
    }


    /**
     * 按场站下发桩配置
     */
    public void downDefultTemplate2AllEvse(String priceSchemeName, String siteName, ServerHttpRequest request) {
        KvObject object1 = new KvObject();
        object1.setKey("计费模板名");
        object1.setValue(priceSchemeName);
        KvObject object2 = new KvObject();
        object2.setKey("站点名称");
        object2.setValue(siteName);
        this.sysUserLogService.buildObjectOpLog(LogOpType.DELIVERY,
                List.of(object1, object2),
                request);
    }

    /**
     * 按桩下发桩配置
     */
    public void sendPriceSchema(String priceSchemeName, String evseNo, ServerHttpRequest request) {
        KvObject object1 = new KvObject();
        object1.setKey("计费模板名");
        object1.setValue(priceSchemeName);
        KvObject object2 = new KvObject();
        object2.setKey("电桩编号");
        object2.setValue(evseNo);
        this.sysUserLogService.buildObjectOpLog(LogOpType.DELIVERY,
                List.of(object1, object2),
                request);
    }

    /**
     * 桩管家 按桩重新下发桩配置
     */
    public void downSettingByEvseLog(String evseNo, ServerHttpRequest request) {
        KvObject object2 = new KvObject();
        object2.setKey("桩编号 电桩编号");
        object2.setValue(evseNo);
        this.sysUserLogService.buildObjectOpLog(LogOpType.DELIVERY,
                List.of(object2),
                request);
    }

    /**
     * 桩管家 按场站重新下发桩配置
     */
    public void downDefultSetting2AllEvseLog(String siteName, ServerHttpRequest request) {
        KvObject object2 = new KvObject();
        object2.setKey("桩编号 站点名称");
        object2.setValue(siteName);
        this.sysUserLogService.buildObjectOpLog(LogOpType.DELIVERY,
                List.of(object2),
                request);
    }

    /**
     * 桩管家 下发紧急卡到站点
     */
    public void sendWhiteCardLog(List<String> cardChipNoList, String siteName, ServerHttpRequest request) {
        KvObject object1 = new KvObject();
        object1.setKey("紧急卡号");
        object1.setValue(cardChipNoList);
        KvObject object2 = new KvObject();
        object2.setKey("站点名称");
        object2.setValue(siteName);
        this.sysUserLogService.buildObjectOpLog(LogOpType.DELIVERY,
                List.of(object1, object2),
                request);
    }

    public void startTask(String softwareName, List<String> evseNoList, ServerHttpRequest request) {
        KvObject object1 = new KvObject();
        object1.setKey("桩软件");
        object1.setValue(softwareName);
        KvObject object2 = new KvObject();
        object2.setKey("电桩编号");
        object2.setValue(evseNoList);
        this.sysUserLogService.buildObjectOpLog(LogOpType.DELIVERY,
                List.of(object1, object2),
                request);
    }

    /**
     * 绑桩
     */
    public void bindToSiteLog(String evseNo, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                KvAny.of("电桩编号", evseNo),
                request);
    }

    /**
     * 解绑桩
     */
    public void unbindLog(List<String> evseNoList, ServerHttpRequest request) {
        KvObject object2 = new KvObject();
        object2.setKey("电桩编号");
        object2.setValue(evseNoList);
        this.sysUserLogService.buildObjectOpLog(LogOpType.DISABLE,
                List.of(object2),
                request);
    }

    /**
     * 编辑桩
     */
    public void updateEvseInfoLog(String evseNo, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("电桩编号", evseNo),
                request);
    }

    /**
     * 编辑桩运营状态
     */
    public void updateEvseBizStatusLog(String evseNo, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY_BIZSTATUS,
            KvAny.of("电桩编号", evseNo),
            request);
    }

    /**
     * 编辑桩
     */
    public void batchUpdateEvseInfoLog(List<String> evseNoList, ServerHttpRequest request) {
        KvObject object2 = new KvObject();
        object2.setKey("电桩编号");
        object2.setValue(evseNoList);
        this.sysUserLogService.buildObjectOpLog(LogOpType.MODIFY,
                List.of(object2),
                request);
    }

    /**
     * 修改枪头信息
     */
    public void updatePlugInfoLog(String evseNo, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("电桩编号", evseNo),
                request);
    }

    /**
     * 批量开启充电
     */
    public void listStartChargerLog(List<String> plugNoList, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.DELIVERY,
                List.of(new KvObject("启停枪号", plugNoList)),
                request);
    }

    /**
     * 批量停止充电
     */
    public void stopBChargerLog(List<String> plugNoList, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.DELIVERY,
                List.of(new KvObject("启停枪号", plugNoList)),
                request);
    }

    /**
     * 枪头绑定定时任务
     */
    public void bindingJobLog(List<String> plugNoList, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.MODIFY_JOB,
                List.of(new KvObject("枪头编号", plugNoList)),
                request);
    }

    /**
     * 枪头解绑定时任务
     */
    public void unbindingJobLog(List<String> plugNoList, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.MODIFY_JOB,
                List.of(new KvObject("枪头编号", plugNoList)),
                request);
    }

    /**
     * 定时任务修改
     */
    public void modifyChargeJobLog(String jobName, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("任务名称", jobName),
                request);
    }

    /**
     * 定时任务修改状态
     */
    public void changeJobStatusLog(String jobName, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY_STATUS,
                KvAny.of("任务名称", jobName),
                request);
    }

    /**
     * 控制器新增日志
     */
    public void siteCtrlAddLog(String num, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                KvAny.of("控制器编号", num),
                request);
    }

    /**
     * 控制器编辑日志
     */
    public void siteCtrlEditLog(String num, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("控制器编号", num),
                request);
    }

    /**
     * 控制器配置下发日志
     */
    public void siteCtrlCfgAddLog(String num, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("控制器编号", num),
                request);
    }

    /**
     * 控制器解绑日志
     */
    public void siteCtrlDisableLog(String num, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.DISABLE,
                KvAny.of("控制器编号", num),
                request);
    }

    /**
     * 控制器重启日志
     */
    public void siteCtrlRebootLog(String num, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY_STATUS,
                KvAny.of("控制器编号", num),
                request);
    }

    /**
     * 创建电表
     * 日志
     */
    public void createMeterLog(String no, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                KvAny.of("电表编号", no),
                request);
    }

    /**
     * 修改电表
     * 日志
     */
    public void updateMeterLog(String no, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("电表编号", no),
                request);
    }

    /**
     * 删除电表
     * 日志
     */
    public void deleteMeterLog(String no, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.DISABLE,
                KvAny.of("电表编号", no),
                request);
    }
    public void updateSocLimitInfo(String siteName, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("站点名称", siteName),
                request);
    }

    /**
     * 同步增量卡
     * 日志
     */
    public void simSyncAllLog(ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                KvAny.of("同步增量卡", ""),
                request);
    }

}
