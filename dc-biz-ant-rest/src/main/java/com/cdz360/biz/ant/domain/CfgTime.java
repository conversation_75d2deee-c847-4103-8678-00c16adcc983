package com.cdz360.biz.ant.domain;

public class CfgTime {
    private Integer code;//电价编码. 电价编码必须在 priceSchemaList 里存在
    private String startTime;//计价开始时间, 格式为 HH:MM, 从 00:00 开始, 开始时间取闭区间.
    private String stopTime;//计价结束时间, 格式为 HH:MM, 至 24:00 为止, 结束时间取开区间

    public Integer getCode() {
        return code;
    }

    public CfgTime setCode(Integer code) {
        this.code = code;
        return this;
    }

    public String getStartTime() {
        return startTime;
    }

    public CfgTime setStartTime(String startTime) {
        this.startTime = startTime;
        return this;
    }

    public String getStopTime() {
        return stopTime;
    }

    public CfgTime setStopTime(String stopTime) {
        this.stopTime = stopTime;
        return this;
    }
}
