package com.cdz360.biz.ant.rest.tj;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.service.tj.TjMaterialCostService;
import com.cdz360.biz.model.tj.kc.param.ListTjMaterialCostParam;
import com.cdz360.biz.model.tj.kc.vo.TjMaterialCostVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "物料成本相关操作接口", description = "物料成本相关操作接口")
@Slf4j
@RestController
@RequestMapping("/api/tj/materialCost")
public class TjMaterialCostRest {

    @Autowired
    private TjMaterialCostService tjMaterialCostService;

    @Operation(summary = "获取物料成本")
    @PostMapping(value = "/findTjMaterialCost")
    public Mono<ListResponse<TjMaterialCostVo>> findTjMaterialCost(
        ServerHttpRequest request,
        @RequestBody ListTjMaterialCostParam param) {
        log.info("{} 获取获取物料成本: param = {}", LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return tjMaterialCostService.findTjMaterialCost(param);
    }

    @Operation(summary = "获取物料成本")
    @GetMapping(value = "/getTjMaterialCostById")
    public Mono<ObjectResponse<TjMaterialCostVo>> getTjMaterialCostById(
        ServerHttpRequest request,
        @ApiParam("物料成本唯一ID") @RequestParam("id") Long id) {
        log.info("{}  获取物料成本: {}", LoggerHelper2.formatEnterLog(request, false), id);
        return tjMaterialCostService.getTjMaterialCostById(id);
    }

    @Operation(summary = "新建或编辑物料成本")
    @PostMapping(value = "/saveTjMaterialCost")
    public Mono<ObjectResponse<TjMaterialCostVo>> saveTjMaterialCost(
        ServerHttpRequest request,
        @RequestBody TjMaterialCostVo tjMaterialCostVo) {
        log.info("{} 保存物料成本: param = {}", LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(tjMaterialCostVo));
        return tjMaterialCostService.saveTjMaterialCost(tjMaterialCostVo);
    }

    @Operation(summary = "删除物料成本")
    @GetMapping(value = "/disableTjMaterialCost")
    public Mono<ObjectResponse<TjMaterialCostVo>> disableTjMaterialCost(
        ServerHttpRequest request,
        @ApiParam("物料成本唯一ID") @RequestParam("id") Long id) {
        log.info("{} 删除物料成本: {}", LoggerHelper2.formatEnterLog(request, false), id);
        return tjMaterialCostService.disableTjMaterialCost(id);
    }
}
