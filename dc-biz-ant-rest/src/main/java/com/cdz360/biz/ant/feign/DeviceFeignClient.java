package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.cus.user.dto.WhiteCardDto;
import com.chargerlinkcar.framework.common.domain.vo.TemplateInfoVo;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2019.2.27
 */
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DEVICE_REST)
@Component
public interface DeviceFeignClient {

//    /**
//     * 配置站点下设备定时充电信息新增或更新
//     * @return
//     */
//    @RequestMapping(value = "/api/siteTime/siteTimeInfoDdl",method = RequestMethod.POST)
//    BaseResponse insertSiteTimeInfo(@RequestBody SiteTimeInsertVo siteTimeInsertVo);
//    /**
//     * 根据站点id查询站点下定时任务的详情(包含枪头列表)
//     * @param siteTimeInsertVo 主要是siteId 站点id
//     * @return
//     */
//    @RequestMapping(value = "/api/siteTime/querySiteTimeBoxInfo",method = RequestMethod.POST)
//    ObjectResponse<JSONObject> querySiteTimeBoxInfo(@RequestBody SiteTimeInsertVo siteTimeInsertVo);

    /**
     * 充电管理平台 营销 分页获取计费模板列表
     *
     * @param map keywords 模板名 commercialId 商户id
     * @return
     */
    @RequestMapping(value = "/api/template/getTemplateInfoList", method = RequestMethod.POST)
    ListResponse<TemplateInfoVo> getTemplateInfoList(@RequestBody Map map);

    /**
     * •根据ID获取计费模板详情
     *
     * @param templateId
     * @return
     */
    @RequestMapping(value = "/api/template/getTemplateDetailById", method = RequestMethod.POST)
    ObjectResponse<TemplateInfoVo> getTemplateDetailById(
        @RequestParam(value = "templateId") Long templateId);


    /**
     * 单场站紧急充电卡下发
     *
     * @param whiteCardDto
     */
    @PostMapping("/api/boxsetting/sendWhiteCard")
    ListResponse<String> sendWhiteCard(@RequestBody WhiteCardDto whiteCardDto);
}
