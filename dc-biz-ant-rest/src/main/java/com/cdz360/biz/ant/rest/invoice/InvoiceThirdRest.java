package com.cdz360.biz.ant.rest.invoice;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.ant.feign.reactor.ReactorInvoiceFeignClient;
import com.cdz360.biz.model.invoice.dto.ThirdInvoiceZtDto;
import com.chargerlinkcar.framework.common.domain.invoice.param.ListThirdKpztParam;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@Tag(name = "发票第三方数据接口", description = "发票第三方数据接口")
@RestController
@RequestMapping("/api/invoice/third")
public class InvoiceThirdRest {

    @Autowired
    private ReactorInvoiceFeignClient invoiceFeignClient;

    @Operation(summary = "刷新开票主体列表")
    @PostMapping(value = "/refreshKpzt")
    public Mono<ListResponse<ThirdInvoiceZtDto>> refreshThirdKpztList(ServerHttpRequest request) {
        log.info("刷新开票主体列表: {}", LoggerHelper2.formatEnterLog(request));
        return invoiceFeignClient.thirdForceRefreshKpzt();
    }

    @Operation(summary = "获取第三方开票主体列表")
    @PostMapping(value = "/fetchKpzt")
    public Mono<ListResponse<ThirdInvoiceZtDto>> fetchThirdKpztList(
        ServerHttpRequest request, @RequestBody ListThirdKpztParam param) {
        log.info("获取第三方开票主体列表: {}, {}", LoggerHelper2.formatEnterLog(request), param);
        return invoiceFeignClient.thirdInvoiceZtList(param);
    }
}
