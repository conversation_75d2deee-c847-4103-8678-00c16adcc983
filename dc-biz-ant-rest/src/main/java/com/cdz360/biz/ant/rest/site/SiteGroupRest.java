package com.cdz360.biz.ant.rest.site;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.aop.CheckToken;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.sys.param.ListSiteGroupParam;
import com.cdz360.biz.model.sys.vo.SiteGroupVo;
import com.cdz360.biz.utils.feign.auth.AuthSiteGroupFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "场站组相关接口", description = "场站组相关接口")
public class SiteGroupRest {

    @Autowired
    private AuthSiteGroupFeignClient authSiteGroupFeignClient;

    @CheckToken(check = "getToken4Aspect")
    @Operation(summary = "获取场站组列表",
        description = "接口逻辑: 当前用户已经属于指定场站组，则只能看自己所属组;没有分配组，则能看所有组")
    @PostMapping(value = "/api/siteGroup/findSiteGroup")
    public Mono<ListResponse<SiteGroupVo>> findSiteGroup(
        ServerHttpRequest request,
        @RequestBody ListSiteGroupParam param) {
        log.debug("获取场站组列表: param = {}", JsonUtils.toJsonString(param));
        final List<String> gids = AntRestUtils.getSysUserGids(request);
        if (Boolean.TRUE.equals(param.getOtherGroup())) {
            param.setExGidList(gids);
        } else {
            if (CollectionUtils.isNotEmpty(gids)) {
                if (CollectionUtils.isNotEmpty(param.getGidList())) {
                    param.setGidList(param.getGidList().stream()
                        .filter(x -> StringUtils.isNotBlank(x) && gids.contains(x)).collect(
                            Collectors.toList()));
                } else {
                    param.setGidList(gids);
                }
            }
        }
        return authSiteGroupFeignClient.findSiteGroup(param);
    }

    @CheckToken(check = "getToken4Aspect")
    @Operation(summary = "获取场站组列表")
    @PostMapping(value = "/api/siteGroup/findAllSiteGroup")
    public Mono<ListResponse<SiteGroupVo>> findAllSiteGroup(
        ServerHttpRequest request,
        @RequestBody ListSiteGroupParam param) {
        log.debug("获取场站组列表: param = {}", JsonUtils.toJsonString(param));
        return authSiteGroupFeignClient.findSiteGroup(param)
            .doOnNext(FeignResponseValidate::check)
            .map(res -> {
                // 获取各场站组关联场站信息
                List<SiteGroupVo> data = res.getData();
                return res;
            });
    }

    @CheckToken(check = "getToken4Aspect")
    @Operation(summary = "获取当前登录用户所属场站组列表")
    @PostMapping(value = "/api/siteGroup/loginUserSiteGroup")
    public Mono<ListResponse<SiteGroupVo>> loginUserSiteGroup(ServerHttpRequest request) {
        log.debug("获取当前登录用户所属场站组列表: {}",
            LoggerHelper2.formatEnterLog(request, false));
        final List<String> gids = AntRestUtils.getSysUserGids(request);
        if (CollectionUtils.isEmpty(gids)) {
            return Mono.just(RestUtils.buildListResponse(List.of()));
        }
        return authSiteGroupFeignClient.findSiteGroup(new ListSiteGroupParam().setGidList(gids));
    }

    @Operation(summary = "获取场站组列表,以及成员信息")
    @PostMapping(value = "/api/siteGroup/findSiteGroupAndUser")
    public Mono<ListResponse<SiteGroupVo>> findSiteGroupAndUser(
        @RequestBody ListSiteGroupParam param) {
        log.debug("获取场站组列表,以及成员信息: param = {}", JsonUtils.toJsonString(param));
        return authSiteGroupFeignClient.findSiteGroupAndUser(param);
    }
}
