package com.cdz360.biz.ant.service.tj;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.feign.reactor.BizTjFeignClient;
import com.cdz360.biz.model.tj.kc.param.ListTjSurveyParam;
import com.cdz360.biz.model.tj.kc.param.RepeatSurveyParam;
import com.cdz360.biz.model.tj.kc.param.TjSurveyBiParam;
import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationExpensesPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationIncomePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesOtherPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesPo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyBiVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyCalculationInfoVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyCalculationResultVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyChargeAreaVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyVo;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TjSurveyCalculationService {

    @Autowired
    private BizTjFeignClient bizTjFeignClient;

    public Mono<ListResponse<TjSurveyChargeAreaVo>> findTjSurveyChargeAreaBySurveyNo(String surveyNo) {
        return bizTjFeignClient.findTjSurveyChargeAreaBySurveyNo(surveyNo);
    }

    public Mono<ObjectResponse<TjSurveyChargeAreaVo>> saveTjSurveyChargeArea(TjSurveyChargeAreaVo tjSurveyChargeAreaVo) {
        return bizTjFeignClient.saveTjSurveyChargeArea(tjSurveyChargeAreaVo);
    }

    public Mono<ObjectResponse<TjSurveySupportingFacilitiesPo>> findTjSurveySupportingFacilitiesBySurveyNo(
        String surveyNo) {
        return bizTjFeignClient.findTjSurveySupportingFacilitiesBySurveyNo(surveyNo);
    }

    public Mono<ObjectResponse<TjSurveySupportingFacilitiesPo>> saveTjSurveySupportingFacilities(
        TjSurveySupportingFacilitiesPo tjSurveySupportingFacilitiesPo) {
        return bizTjFeignClient.saveTjSurveySupportingFacilities(tjSurveySupportingFacilitiesPo);
    }

    public Mono<ListResponse<TjSurveySupportingFacilitiesOtherPo>> findTjSurveySupportingFacilitiesOtherBySurveyNo(
        String surveyNo) {
        return bizTjFeignClient.findTjSurveySupportingFacilitiesOtherBySurveyNo(surveyNo);
    }

    public Mono<ObjectResponse<TjSurveySupportingFacilitiesOtherPo>> saveTjSurveySupportingFacilitiesOther(
        TjSurveySupportingFacilitiesOtherPo tjSurveySupportingFacilitiesOtherPo) {
        return bizTjFeignClient.saveTjSurveySupportingFacilitiesOther(tjSurveySupportingFacilitiesOtherPo);
    }

    public Mono<ListResponse<TjSurveyHighVoltagePo>> findTjSurveyHighVoltageBySurveyNo(
        String surveyNo) {
        return bizTjFeignClient.findTjSurveyHighVoltageBySurveyNo(surveyNo);
    }

    public Mono<ObjectResponse<TjSurveyHighVoltagePo>> saveTjSurveyHighVoltage(
        TjSurveyHighVoltagePo tjSurveyHighVoltagePo) {
        return bizTjFeignClient.saveTjSurveyHighVoltage(tjSurveyHighVoltagePo);
    }

    public Mono<ObjectResponse<TjSurveyOperationIncomePo>> findTjSurveyOperationIncomeBySurveyNo(
        String surveyNo) {
        return bizTjFeignClient.findTjSurveyOperationIncomeBySurveyNo(surveyNo);
    }

    public Mono<ObjectResponse<TjSurveyOperationIncomePo>> saveTjSurveyOperationIncome(
        TjSurveyOperationIncomePo tjSurveyOperationIncomePo) {
        return bizTjFeignClient.saveTjSurveyOperationIncome(tjSurveyOperationIncomePo);
    }

    public Mono<ObjectResponse<TjSurveyOperationExpensesPo>> findTjSurveyOperationExpensesBySurveyNo(
        String surveyNo) {
        return bizTjFeignClient.findTjSurveyOperationExpensesBySurveyNo(surveyNo);
    }

    public Mono<ObjectResponse<TjSurveyOperationExpensesPo>> saveTjSurveyOperationExpenses(
        TjSurveyOperationExpensesPo tjSurveyOperationExpensesPo) {
        return bizTjFeignClient.saveTjSurveyOperationExpenses(tjSurveyOperationExpensesPo);
    }

    public Mono<ObjectResponse<TjSurveyCalculationInfoVo>> saveTjSurveyCalculationInfo(
        TjSurveyCalculationInfoVo tjSurveyCalculationInfoVo) {
        return bizTjFeignClient.saveTjSurveyCalculationInfo(tjSurveyCalculationInfoVo);
    }

    public Mono<ListResponse<TjSurveyCalculationResultVo>> calculationResult(
        String surveyNo) {
        return bizTjFeignClient.calculationResult(surveyNo);
    }
}
