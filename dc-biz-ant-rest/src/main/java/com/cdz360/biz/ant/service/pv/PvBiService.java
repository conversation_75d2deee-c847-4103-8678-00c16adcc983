package com.cdz360.biz.ant.service.pv;

import static java.time.temporal.ChronoField.DAY_OF_MONTH;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.constant.Constant;
import com.cdz360.biz.ant.domain.request.PvGroupParam;
import com.cdz360.biz.ant.domain.request.PvGroupParam.Group;
import com.cdz360.biz.ant.domain.vo.PvRtDailyPower;
import com.cdz360.biz.ant.domain.vo.PvRtDataGroupItem;
import com.cdz360.biz.ant.domain.vo.PvRtDataGroupSampling;
import com.cdz360.biz.ant.domain.vo.PvRtDataPowerSampling;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.ess.model.data.param.DataBiParam;
import com.cdz360.biz.ess.model.data.param.DayKwhParam;
import com.cdz360.biz.model.iot.param.ListGtiParam;
import com.cdz360.biz.model.iot.vo.GtiVo;
import com.cdz360.biz.model.site.type.SiteCategory;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.trading.iot.dto.PvProfileDto;
import com.cdz360.biz.model.trading.iot.dto.SitePvProfileDto;
import com.cdz360.biz.model.trading.iot.param.PvProfitTrendParam;
import com.cdz360.biz.model.trading.iot.vo.DayPvDataBi;
import com.cdz360.biz.model.trading.iot.vo.DaySitePvRtDataBi;
import com.cdz360.biz.model.trading.iot.vo.DeviceInfoVo;
import com.cdz360.biz.model.trading.iot.vo.DeviceInfoVoDetail;
import com.cdz360.biz.model.trading.iot.vo.GtiSampleData;
import com.cdz360.biz.model.trading.iot.vo.GtiStatusBi;
import com.cdz360.biz.model.trading.iot.vo.PvRtDataPowerProfit;
import com.cdz360.biz.model.trading.iot.vo.SitePvRtDataBi;
import com.cdz360.biz.model.trading.iot.vo.TotalPvRtDataBi;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.site.vo.SiteWeatherVo;
import com.cdz360.biz.utils.feign.iot.DeviceFeignClient;
import com.cdz360.biz.utils.feign.iot.GtiFeignClient;
import com.cdz360.biz.utils.feign.iot.IotPvFeignClient;
import com.cdz360.biz.utils.feign.iot.PvRtDataFeignClient;
import com.cdz360.biz.utils.feign.site.SiteDataCoreFeignClient;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.DateUtils;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class PvBiService {

    private static final LocalDate PV_TOTAL_FROM_DATE = LocalDate.of(2021, 8, 1);
    private final int DEFAULT_GROUP_NUM = 2;

    @Autowired
    private PvRtDataService pvRtDataService;
    @Autowired
    private IotPvFeignClient iotPvFeignClient;

    @Autowired
    private PvRtDataFeignClient pvRtDataFeignClient;

    @Autowired
    private DeviceFeignClient deviceFeignClient;

    @Autowired
    private GtiFeignClient gtiFeignClient;

    @Autowired
    private SiteDataCoreFeignClient siteDataCoreFeignClient;

    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;

    public Mono<ListResponse<DayPvDataBi>> powerGeneration7(String siteId) {
        IotAssert.isTrue(StringUtils.isNotBlank(siteId), "siteId不能为空");

        return Mono.just(siteId)
            .flatMap(e -> pvRtDataFeignClient.siteRtData7Day(e))
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .flatMap(this::packagePowerGeneration7ResData)
            .map(RestUtils::buildListResponse);
    }

    private Mono<List<DayPvDataBi>> packagePowerGeneration7ResData(List<DayPvDataBi> resp) {

        Map<LocalDate, DayPvDataBi> map = resp.stream()
            .collect(Collectors.toMap(DayPvDataBi::getDate, o -> o));

        return Flux.fromIterable(DateUtil.rangeDate(LocalDate.now().minusDays(7L),
                LocalDate.now()))
            .map(date -> {
                DayPvDataBi res = new DayPvDataBi();
                res.setDate(date);

                com.cdz360.biz.model.trading.iot.vo.DayPvDataBi temp = map.get(date);
                if (temp != null) {
                    res.setTotalKwh(temp.getTotalKwh())
                        .setTotalProfit(temp.getTotalProfit());
                }
                return res;
            })
            .collectList();
    }


    public Mono<ObjectResponse<SitePvProfileDto>> sitePvProfile(String siteId) {
        IotAssert.isTrue(StringUtils.isNotBlank(siteId), "siteId不能为空");

        DayKwhParam req = new DayKwhParam();
        req.setSiteIdList(List.of(siteId));

        return Mono.just(req)
            .flatMap(e -> pvRtDataFeignClient.rtDataTotal(e))
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .flatMap(resp -> {
                return this.packageSitePvProfileResData(resp, siteId);
            })
            .map(RestUtils::buildObjectResponse);
    }

    public Mono<ObjectResponse<SiteWeatherVo>> siteWeather(String siteId) {
        IotAssert.isTrue(StringUtils.isNotBlank(siteId), "siteId不能为空");

        return Mono.just(siteId)
            .flatMap(e -> siteDataCoreFeignClient.findBySiteId(e))
            .doOnNext(FeignResponseValidate::check);
    }

    public Mono<ObjectResponse<ExcelPosition>> exportPowerProfitTrend(
        ServerHttpRequest request, PvProfitTrendParam param) {
        IotAssert.isNotBlank(param.getSiteId(), "请传入场站id");
        IotAssert.isNotNull(param.getSampleType(), "请传入采样时间单位");
        IotAssert.isNotNull(param.getStartTime(), "请传入开始时间");
        IotAssert.isNotNull(param.getEndTime(), "请传入结束时间");

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("电量收益趋势")
            .setFunctionMap(DownloadFunctionType.PV_POWER_PROFIT_TREND)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return asyncBizBiFeignClient.exportPowerProfitTrendExcel(param);
    }

    public Mono<ListResponse<GtiSampleData>> gtiRtDataSample(DataBiParam param) {
        return this.deviceFeignClient.gtiRtDataSample(param)
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .map(data -> {
                if (null != param.getShortcut() && data.size() != param.getShortcut()) {
                    List<LocalDateTime> times = new ArrayList<>();
                    switch (param.getSampleType()) {
                        case DAY:
                            if (null != param.getShortcut()) {
                                LocalDateTime now = LocalDate.now().atStartOfDay();
                                LocalDateTime start = now.minusDays(param.getShortcut() - 1);
                                while (start.isBefore(now)) {
                                    times.add(start);
                                    start = start.plusDays(1);
                                }
                                times.add(now);
                            } else if (param.getFromDate() != null && null != param.getToDate()) {
                                LocalDateTime toDate = param.getToDate().toLocalDate()
                                    .atStartOfDay();
                                LocalDateTime start = param.getFromDate().toLocalDate()
                                    .atStartOfDay();
                                while (start.isBefore(toDate)) {
                                    times.add(start);
                                    start = start.plusDays(1);
                                }
                                times.add(toDate);
                            }
                            break;
                        case MONTH:
                            times = new ArrayList<>();
                            if (null != param.getShortcut()) {
                                LocalDateTime now = LocalDate.now()
                                    .with(DAY_OF_MONTH, 1).atStartOfDay();
                                LocalDateTime start = now.minusMonths(param.getShortcut() - 1);
                                while (start.isBefore(now)) {
                                    times.add(start);
                                    start = start.plusMonths(1);
                                }
                                times.add(now);
                            } else if (param.getFromDate() != null && null != param.getToDate()) {
                                LocalDateTime toDate = param.getToDate().toLocalDate()
                                    .atStartOfDay();
                                LocalDateTime start = param.getFromDate().toLocalDate()
                                    .atStartOfDay();
                                while (start.isBefore(toDate)) {
                                    times.add(start);
                                    start = start.plusMonths(1);
                                }
                                times.add(toDate);
                            }
                            break;
                    }

                    List<LocalDateTime> hasTimes = data.stream()
                        .map(i -> i.getTime().toLocalDate().atStartOfDay())
                        .collect(Collectors.toList());
                    times.stream().filter(t -> !hasTimes.contains(t))
                        .forEach(t -> data.add(new GtiSampleData().setTime(t)));
                }
                return data.stream()
                    .sorted(Comparator.comparing(GtiSampleData::getTime))
                    .collect(Collectors.toList());
            })
            .map(RestUtils::buildListResponse);
    }

    public Mono<ListResponse<PvRtDataPowerProfit>> powerProfitTrend(PvProfitTrendParam param) {

        return gtiFeignClient.powerProfitTrend(param);
    }

    public Mono<ListResponse<PvRtDataPowerSampling>> powerCurve(String siteId, Date date) {
        IotAssert.isTrue(StringUtils.isNotBlank(siteId), "siteId不能为空");

        final int dis = 1; // 分钟

        return Mono.just(siteId)
            .flatMap(e -> gtiFeignClient.findCtrlVoList(List.of(e)))
            .doOnNext(FeignResponseValidate::check)
            .map(res -> {
                List<String> gwnoList = new ArrayList<>();
                res.getData().stream().filter(
                        e -> siteId.equals(e.getSiteId()) && CollectionUtils.isNotEmpty(
                            e.getGwInfoList()))
                    .map(DeviceInfoVo::getGwInfoList)
                    .forEach(e -> {
                        e.forEach(t -> {
                            if (StringUtils.isNotBlank(t.getGwno())) {
                                gwnoList.add(t.getGwno());
                            }
                        });
                    });
                return gwnoList;
            })
            .filter(CollectionUtils::isNotEmpty)
            .flatMap(e -> {
                return pvRtDataService.dayPowerSampling(e, DateUtil.dateToLocalDate(date), dis)
                    .collectList();
            })
            .switchIfEmpty(Mono.just("站点下无微网控制器，返回空数据").map(i -> {
                log.info(i);
                return List.of();
            }))
            .map(RestUtils::buildListResponse);
    }

    private Mono<SitePvProfileDto> packageSitePvProfileResData(TotalPvRtDataBi resp,
        String siteId) {
        SitePvProfileDto res = new SitePvProfileDto();
        return Mono.just(siteId)
            .flatMap(e -> {
                // STEP1.获取下属场站
                ListSiteParam request = new ListSiteParam();
                request.setSiteCategory(SiteCategory.PV)
                    .setSiteIdList(List.of(e))
                    .setStart(0L)
                    .setSize(1);
                return this.siteDataCoreFeignClient.getSiteVoList(request);
            })
            .doOnNext(FeignResponseValidate::check)
            .map(e -> {
                //装机功率
                BigDecimal installPower = e.getData().stream()
                    .filter(t -> t.getPvInstalledCapacity() != null)
                    .map(SitePo::getPvInstalledCapacity).reduce(BigDecimal.ZERO, BigDecimal::add);

                if (resp.getToday() != null) {
                    res.setTodayKwh(resp.getToday().getTotalKwh());
                    res.setTodayProfit(resp.getToday().getTotalProfit());
                    //今日输出功率
                    res.setCurOutPower(resp.getToday().getOutPower());
                    /**
                     * 今日等效时长 = 今日发电量/装机总功率
                     */
                    if (DecimalUtils.gtZero(resp.getToday().getTotalKwh()) && DecimalUtils.gtZero(
                        installPower)) {
                        res.setCurDuration(resp.getToday().getTotalKwh()
                            .divide(installPower, 4, RoundingMode.HALF_UP));
                    }
                }

                if (resp.getTotal() != null) {
                    res.setTotalKwh(resp.getTotal().getTotalKwh());
                    res.setTotalProfit(resp.getTotal().getTotalProfit());
                    /**
                     * 日均发电量 = 总发电量/总发电天数
                     *  日均等效时长 = 总发电量/总发电天数/装机功率
                     */
                    if (DecimalUtils.gtZero(resp.getTotal().getTotalKwh())
                        && resp.getTotal().getTotalDay() > 0) {
                        res.setPerKwh(resp.getTotal().getTotalKwh()
                            .divide(BigDecimal.valueOf(resp.getTotal().getTotalDay()), 4,
                                RoundingMode.HALF_UP));
                    }
                    if (DecimalUtils.gtZero(res.getPerKwh()) && DecimalUtils.gtZero(installPower)) {
                        res.setPerDuration(
                            res.getPerKwh().divide(installPower, 4, RoundingMode.HALF_UP));
                    }
                }
                return res;
            });
    }

    public Mono<ObjectResponse<PvProfileDto>> powerGenerationProfile(String commIdChain,
        String siteId) {

        PvProfileDto res = new PvProfileDto();

        return Mono.just(commIdChain)
            .flatMap(e -> {
                // STEP1.获取下属场站
                ListSiteParam request = new ListSiteParam();
                request.setCommIdChain(e)
                    .setSiteCategory(SiteCategory.PV)
                    .setStart(0L)
                    .setSize(999);
                //单个场站汇总
                if (StringUtils.isNotEmpty(siteId)) {
                    request.setSiteIdList(List.of(siteId));
                }
                return this.siteDataCoreFeignClient.getSiteVoList(request);
            })
            .doOnNext(FeignResponseValidate::check)
            .map(e -> {
                res.setPvSiteNum(e.getData().size());
                res.setInstalledCapacityTotal(
                    e.getData().stream().filter(t -> t.getPvInstalledCapacity() != null)
                        .map(SitePo::getPvInstalledCapacity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));

                //按照场站查询获取并网日期
                if (StringUtils.isNotEmpty(siteId)) {
                    if (CollectionUtils.isNotEmpty(e.getData())) {
                        res.setOnGridDate(e.getData().get(0).getOnGridDate());
                    }
                }
                return e.getData().stream().map(SitePo::getId).collect(Collectors.toList());
            })
            .filter(CollectionUtils::isNotEmpty)
            .flatMap(e -> gtiFeignClient.findCtrlVoList(e))
            .doOnNext(FeignResponseValidate::check)
            .doOnNext(resp -> {
                resp.getData().forEach(e -> {
                    if (CollectionUtils.isNotEmpty(e.getGwInfoList())) {
                        res.setInverterNum(res.getInverterNum() + e.getGwInfoList().stream()
                            .filter(s -> s.getNum() != null)
                            .mapToLong(DeviceInfoVoDetail::getNum).sum());
                    }
                });
            })
            .flatMap(t -> this.setPvData(res, commIdChain, siteId)) // STEP2. 赋值当天、当月数据、总数据
            .map(e -> {

                // STEP3.换算得到环保数据
                if (DecimalUtils.gtZero(res.getTotalKwh())) {

                    res.setCoal(res.getTotalKwh().multiply(Constant.ruleCoal))
                        .setCo2(res.getTotalKwh().multiply(Constant.ruleCo2))
                        .setSo2(res.getTotalKwh().multiply(Constant.ruleSo2));
                    res.setTree(res.getCo2().divide(Constant.ruleTree, 2, RoundingMode.HALF_UP));
                }
                return res;
            })
            .map(e -> {
                // STEP4.调整单位
                if (DecimalUtils.gtZero(e.getTotalKwh())) {
                    e.setCoal(e.getCoal().movePointLeft(3))
                        .setCo2(e.getCo2().movePointLeft(3))
                        .setSo2(e.getSo2().movePointLeft(3).movePointLeft(3))
                        .setTree(e.getTree());
                }
                return RestUtils.buildObjectResponse(e);
            })
            .switchIfEmpty(Mono.just("无下属光伏站，返回空数据").map(i -> {
                log.info(i);
                return RestUtils.buildObjectResponse(res);
            }));
    }

    private Mono<PvProfileDto> setPvData(PvProfileDto res, String commIdChain, String siteId) {

        DayKwhParam param = new DayKwhParam();
        param.setCommIdChain(commIdChain);
        //单个场站下
        if (StringUtils.isNotEmpty(siteId)) {
            param.setSiteIdList(List.of(siteId));
        }
        return pvRtDataFeignClient.rtDataTotal(param)
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .map(dataBi -> {

                if (dataBi.getToday() != null) {
                    res.setTodayKwh(dataBi.getToday().getTotalKwh())
                        .setTodayProfit(dataBi.getToday().getTotalProfit());
                }

                if (dataBi.getCurMonth() != null) {
                    res.setCurrMonthKwh(dataBi.getCurMonth().getTotalKwh())
                        .setCurrMonthProfit(dataBi.getCurMonth().getTotalProfit());
                }

                if (dataBi.getCurYear() != null) {
                    res.setCurrYearKwh(dataBi.getCurYear().getTotalKwh())
                        .setCurrYearProfit(dataBi.getCurYear().getTotalProfit());
                }

                if (dataBi.getTotal() != null) {
                    res.setTotalKwh(dataBi.getTotal().getTotalKwh())
                        .setTotalProfit(dataBi.getTotal().getTotalProfit());
                }
                return res;
            });
    }

//    private Mono<PvProfileDto> getPvData(List<String> gwnoList, List<Object> tempList) {
//        return Mono.zip(this.pvRtDataService.dayPvDataBi(gwnoList, LocalDate.now(), tempList), // 当天电量及收益
//                this.pvRtDataService.monthDayPvDataBi(gwnoList, LocalDate.now().getMonthValue(), tempList)
//                        .collectList(), // 当月电量及收益
//                this.totalPvData(gwnoList, tempList)  // FIXME: 临时累计电量及收益，后续统计到数据库(iot.t_gti_daily)
//                )
//                .map(tuple -> {
//                    DayPvDataBi day = tuple.getT1();
//                    List<DayPvDataBi> month = tuple.getT2();
//                    List<DayPvDataBi> year = tuple.getT3();
//
//                    PvProfileDto dto = new PvProfileDto();
//                    dto.setTodayKwh(day.getTotalKwh())
//                            .setTodayProfit(day.getTotalProfit());
//
//                    BigDecimal monthKwh = month.stream()
//                            .map(DayPvDataBi::getTotalKwh)
//                            .reduce(BigDecimal.ZERO, BigDecimal::add);
//                    BigDecimal monthProfit = month.stream()
//                            .map(DayPvDataBi::getTotalProfit)
//                            .reduce(BigDecimal.ZERO, BigDecimal::add);
//                    dto.setCurrMonthKwh(monthKwh);
//                    dto.setCurrMonthProfit(monthProfit);
//
//                    BigDecimal yearKwh = year.stream()
//                            .map(DayPvDataBi::getTotalKwh)
//                            .reduce(BigDecimal.ZERO, BigDecimal::add);
//                    BigDecimal yearProfit = year.stream()
//                            .map(DayPvDataBi::getTotalProfit)
//                            .reduce(BigDecimal.ZERO, BigDecimal::add);
//                    dto.setTotalKwh(yearKwh);
//                    dto.setTotalProfit(yearProfit);
//                    return dto;
//                });
//
////        return Mono.just(data)
////                .flatMap(e -> this.setTodayPvData(data, gwnoList, tempList))
////                .flatMap(e -> this.setCurrMonthPvData(data, gwnoList, tempList))
////                .flatMap(e -> this.setTotalPvData(data, gwnoList, tempList))
////                .flatMap(e -> Mono.just(data));
//    }
//
//    private Mono<PvProfileDto> setTodayPvData(PvProfileDto data, List<String> gwnoList, List<Object> tempList) {
//
//        // 获取今日电量
//        return pvRtDataService.dayPvDataBi(gwnoList, LocalDate.now(), tempList)
//                .map(e -> {
//                    data.setTodayKwh(e.getTotalKwh())
//                            .setTodayProfit(e.getTotalProfit());
//                    return data;
//                });
//    }
//
//    private Mono<List<DayPvDataBi>> setCurrMonthPvData(PvProfileDto data, List<String> gwnoList, List<Object> tempList) {
//
////        LocalDate fromDate = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
////        LocalDate toDate = LocalDate.now().with(TemporalAdjusters.lastDayOfMonth());
//        // 获取当月数据
//        return pvRtDataService.monthDayPvDataBi(gwnoList, LocalDate.now().getMonthValue(), tempList)
//                .collectList()
//                .doOnNext(t -> {
//                    t.forEach(s -> {
//                        data.setCurrMonthKwh(DecimalUtils.add(data.getCurrMonthKwh(), s.getTotalKwh()));
//                        data.setCurrMonthProfit(DecimalUtils.add(data.getCurrMonthProfit(), s.getTotalProfit()));
//                    });
//                })
//                .doOnNext(t -> {
//                    // 加上当天数据
//                    data.setCurrMonthKwh(DecimalUtils.add(data.getCurrMonthKwh(), data.getTodayKwh()));
//                    data.setCurrMonthProfit(DecimalUtils.add(data.getCurrMonthProfit(), data.getTodayProfit()));
//                });
//    }

//    private Mono<List<DayPvDataBi>> totalPvData(List<String> gwnoList, List<Object> tempList) {
//
//        // 计算累计电量: 默认从 PV_TOTAL_FROM_DATE 开始统计
//        LocalDate endDate = LocalDate.now().plusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
//
//        List<Integer> monthList = new ArrayList<>();
//        LocalDate startDate = PV_TOTAL_FROM_DATE;
//        while (startDate.isBefore(endDate)) {
//            monthList.add(startDate.getMonthValue());
//            startDate = startDate.plusMonths(1);
//        }
//
//        return Flux.fromIterable(monthList)
//                .flatMap(month -> pvRtDataService.monthDayPvDataBi(gwnoList, month, tempList))
//                .collectList();
//
////        LocalDate fromDate = LocalDate.of(2021, 8, 15);
////        LocalDate toDate = LocalDate.now().with(TemporalAdjusters.firstDayOfMonth());
////        // 获取历史数据（不含当月）
////        return pvRtDataService.rangeDayPvDataBi(gwnoList, PV_TOTAL_FROM_DATE, toDate, tempList)
////                .collectList()
////                .doOnNext(t -> {
////                    t.forEach(s -> {
////                        data.setTotalKwh(DecimalUtils.add(data.getTotalKwh(), s.getTotalKwh()));
////                        data.setTotalProfit(DecimalUtils.add(data.getTotalProfit(), s.getTotalProfit()));
////                    });
////                })
////                .doOnNext(t -> {
////                    // 加上当月数据
////                    data.setTotalKwh(DecimalUtils.add(data.getTotalKwh(), data.getCurrMonthKwh()));
////                    data.setTotalProfit(DecimalUtils.add(data.getTotalProfit(), data.getCurrMonthProfit()));
////                });
//    }

    public Mono<ListResponse<DaySitePvRtDataBi>> powerGenerationByMonth(String commIdChain,
        Integer year, Integer month) {
//        IotAssert.isTrue(StringUtils.isNotBlank(commIdChain), "获取个人信息失败");
        IotAssert.isTrue(month != null && (1 <= month && month <= 12), "月份不合法");

        if (null == year) {
            year = LocalDate.now().getYear();
        }

        final int finalYear = year;
        return Mono.just(commIdChain)
            .flatMap(e -> {
                // STEP1.获取下属场站
                ListSiteParam request = new ListSiteParam();
                request.setCommIdChain(e)
                    .setSiteCategory(SiteCategory.PV)
                    .setStart(0L)
                    .setSize(999);
                return this.siteDataCoreFeignClient.getSiteVoList(request);
            })
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .flatMap(e -> {
                // STEP2.获取场站下指定月份各天的发电量
                DayKwhParam param = new DayKwhParam();
                param.setMonth(month)
                    .setYear(finalYear)
                    .setSiteIdList(e.stream().map(SitePo::getId).collect(Collectors.toList()));
                return pvRtDataFeignClient.siteDayOfMonthKwh(param)
                    .doOnNext(FeignResponseValidate::check)
                    .map(ListResponse::getData)
                    .flatMap(t -> this.generateData(DateUtil.rangeDate(finalYear, month), t));
            })
            .map(RestUtils::buildListResponse);
    }

    public Mono<ListResponse<DaySitePvRtDataBi>> powerGenerationByYear(String commIdChain,
        Integer year, Integer recentMonth, String siteId) {
//        IotAssert.isTrue(StringUtils.isNotBlank(commIdChain), "获取个人信息失败");
        IotAssert.isTrue(year != null || recentMonth != null, "数据请求不正确");

        return Mono.just(commIdChain)
            .flatMap(e -> {
                // STEP1.获取下属场站
                ListSiteParam request = new ListSiteParam();
                request.setCommIdChain(e)
                    .setSiteCategory(SiteCategory.PV)
                    .setStart(0L)
                    .setSize(999);
                //场站下数据按照月芬统计
                if (StringUtils.isNotEmpty(siteId)) {
                    request.setSiteIdList(List.of(siteId));
                }
                return this.siteDataCoreFeignClient.getSiteVoList(request);
            })
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .flatMap(e -> {
                // STEP2.获取场站下指定月份各天的发电量
                DayKwhParam param = new DayKwhParam();
                param.setYear(year)
                    .setSiteIdList(e.stream().map(SitePo::getId).collect(Collectors.toList()));

                TimeFilter timeFilter = new TimeFilter();
                if (null != year) { //按照年份统计
                    timeFilter.setStartTime(DateUtil.localDateToDate(LocalDate.of(year, 1, 1)))
                        .setEndTime(DateUtil.localDateToDate(LocalDate.of(year, 12, 31)));
                } else { //按照最近n月统计
                    //上个月最后一天
                    LocalDate endTime = LocalDate.now().minusMonths(1)
                        .with(TemporalAdjusters.lastDayOfMonth());
                    //前n个月第一天
                    LocalDate startTime = endTime.minusMonths(recentMonth - 1)
                        .with(TemporalAdjusters.firstDayOfMonth());
                    timeFilter.setEndTime(DateUtil.localDateToDate(endTime))
                        .setStartTime(DateUtil.localDateToDate(startTime));
                }
                param.setDate(timeFilter);
                return pvRtDataFeignClient.siteDayOfYearKwh(param)
                    .doOnNext(FeignResponseValidate::check)
                    .map(ListResponse::getData)
                    .flatMap(t -> this.generateData(
                        DateUtil.rangeMonthDate(DateUtil.dateToLocalDate(timeFilter.getStartTime()),
                            DateUtil.dateToLocalDate(timeFilter.getEndTime())), t));
            })
            .map(RestUtils::buildListResponse);
    }

    public Mono<ListResponse<DaySitePvRtDataBi>> powerGenerationByRecentDays(String commIdChain,
        Integer recentDays, String siteId) {
        IotAssert.isNotNull(recentDays, "recentDays参数为空");

        return Mono.just(commIdChain)
            .filter(StringUtils::isNotBlank)
            .flatMap(e -> {
                // STEP1.获取下属场站
                ListSiteParam request = new ListSiteParam();
                request.setCommIdChain(e)
                    .setSiteCategory(SiteCategory.PV)
                    .setStart(0L)
                    .setSize(999);
                if (StringUtils.isNotEmpty(siteId)) {
                    request.setSiteIdList(List.of(siteId));
                }
                return this.siteDataCoreFeignClient.getSiteVoList(request);
            })
            .doOnNext(FeignResponseValidate::check)
            .map(e -> Optional.of(e.getData()))
            .switchIfEmpty(Mono.just("").map(e -> Optional.empty()))
            .flatMap(optional -> {
                // STEP2.获取场站下近n天的发电量及收益
                DayKwhParam param = new DayKwhParam();
                param.setRecentDays(recentDays);

                //单个场站请求
                if (StringUtils.isNotEmpty(siteId)) {
                    param.setSiteIdList(List.of(siteId));
                } else {
                    optional.ifPresent(siteVos -> param.setSiteIdList(
                        siteVos.stream().map(SitePo::getId).collect(Collectors.toList())));
                }
                return pvRtDataFeignClient.siteRecentDaysKwh(param)
                    .doOnNext(FeignResponseValidate::check)
                    .map(ListResponse::getData)
                    .flatMap(t -> this.generateData(
                        DateUtil.rangeDate(LocalDate.now().minusDays(recentDays), LocalDate.now()),
                        t));
            })
            .map(RestUtils::buildListResponse);
    }

    private Mono<List<DaySitePvRtDataBi>> generateData(List<LocalDate> localDateList,
        List<DaySitePvRtDataBi> dataBiList) {

        Map<Date, List<DaySitePvRtDataBi>> map = dataBiList.stream()
            .collect(Collectors.groupingBy(DaySitePvRtDataBi::getDate));

        return Flux.fromIterable(localDateList)
            .map(e -> {
                Date time = DateUtils.toDate(e);
                DaySitePvRtDataBi bi = new DaySitePvRtDataBi();
                bi.setDate(time);

                List<DaySitePvRtDataBi> temp = map.get(time);
                if (CollectionUtils.isNotEmpty(temp)) {

                    bi.setTotalKwh(temp.stream().filter(
                            s -> s.getTotalKwh() != null && DecimalUtils.gtZero(s.getTotalKwh()))
                        .map(SitePvRtDataBi::getTotalKwh).reduce(BigDecimal.ZERO, BigDecimal::add));

                    //发电收益
                    bi.setTotalProfit(temp.stream().filter(
                            s -> s.getTotalProfit() != null && DecimalUtils.gtZero(s.getTotalProfit()))
                        .map(SitePvRtDataBi::getTotalProfit)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));

                    temp.forEach(t -> {
                        if (t.getTotalPower() != null && t.getTotalPower() > 0) {
                            bi.setEquivalentDuration(DecimalUtils.add(bi.getEquivalentDuration(),
                                t.getTotalKwh().divide(BigDecimal.valueOf(t.getTotalPower()), 4,
                                    RoundingMode.HALF_UP)));
                        }
                    });
                }

                return bi;
            })
            .collectList();
    }

    public Mono<ListResponse<GtiStatusBi>> getGtiStatusBi(String commIdChain, String siteId) {

        return Mono.just(commIdChain)
            .flatMap(e -> pvRtDataFeignClient.getGtiStatusBi(e, siteId));
    }

    public Mono<ListResponse<PvRtDailyPower>> powerSampling(String commIdChain, String siteId) {

        return Mono.just(commIdChain)
            .flatMap(e -> {
                if (StringUtils.isNotBlank(e)) {
                    //单个场站
                    if (StringUtils.isNotEmpty(siteId)) {
                        return Mono.just(List.of(siteId));
                    } else {
                        //不限制场站
                        ListSiteParam request = new ListSiteParam();
                        request.setCommIdChain(e)
                            .setSiteCategory(SiteCategory.PV)
                            .setStart(0L)
                            .setSize(999);
                        return this.siteDataCoreFeignClient.getSiteVoList(request)
                            .doOnNext(FeignResponseValidate::check)
                            .flatMap(res -> {
                                var list = res.getData().stream().map(SitePo::getId)
                                    .collect(Collectors.toList());
                                return Mono.justOrEmpty(
                                    CollectionUtils.isNotEmpty(list) ? list : null);
                            });
                    }
                } else {
                    // 忽略商户级别的限制，查询所有，单个场站
                    if (StringUtils.isNotEmpty(siteId)) {
                        return Mono.just(List.of(siteId));
                    } else {
                        return Mono.just(new ArrayList<String>());
                    }
                }
            })
            .flatMap(e -> {
                return gtiFeignClient.findCtrlVoList(e);
            })
            .doOnNext(FeignResponseValidate::check)
            .map(res -> {
                // STEP2.获取下属控制器编号
                List<String> gwnoList = new ArrayList<>();
                res.getData().stream().filter(e -> CollectionUtils.isNotEmpty(e.getGwInfoList()))
                    .map(DeviceInfoVo::getGwInfoList)
                    .forEach(e -> {
                        e.forEach(t -> {
                            if (StringUtils.isNotBlank(t.getGwno())) {
                                gwnoList.add(t.getGwno());
                            }
                        });
                    });
                return gwnoList;
            })
            .flatMap(e -> {
                LocalDate time = LocalDate.now();
                LocalDate fromDate = time.minusDays(2);
                LocalDate toDate = time.plusDays(1);
                // STEP3.获取发电功率数据
                return pvRtDataService.rangeDayPowerSampling(e, fromDate, toDate, 30)
                    .collectList()
                    .map(RestUtils::buildListResponse);
            })
            .switchIfEmpty(Mono.just("无下属光伏站，返回空数据").map(i -> {
                log.info(i);
                return RestUtils.buildListResponse(List.of());
            }));
    }

    public Mono<List<PvRtDataGroupSampling>> getGroupInfo(PvGroupParam param) {
        IotAssert.isNotNull(param, "入参不能为空");
        param.selfCheck();
        final int dis = 1; // 每小时

        return Mono.just(param)
            .flatMap(p -> gtiFeignClient.findGtiList(new ListGtiParam()
                .setSiteId(p.getSiteId())))
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .map(e -> {
                if (CollectionUtils.isEmpty(param.getGroupList())) { //若未筛选PV组串，则查询场站下所有
                    List<Group> groupList = new ArrayList<>();
                    AtomicInteger idxRef = new AtomicInteger(0);
                    e.forEach(t -> {
                        int endInclusive = t.getGroupNum() != null && t.getGroupNum() > 0
                            ? t.getGroupNum() : DEFAULT_GROUP_NUM;
                        IntStream.rangeClosed(1, endInclusive)
                            .forEach(r -> {
                                Group temp = new Group();
                                temp.setId(r)
                                    .setIdx(idxRef.getAndIncrement())
                                    .setOwnEquipId(t.getOwnEquipId())
                                    .setEssDno(t.getEssDno())
                                    .setDno(t.getDno());
                                groupList.add(temp);
                            });
                    });
                    param.setGroupList(groupList);
                } else { //需筛选PV组串
                    Map<String, GtiVo> collect = e.stream()
                        .collect(Collectors.toMap(GtiVo::getDno, s -> s));
                    param.setGroupList(param.getGroupList().stream().peek(t -> {
                        GtiVo temp = collect.get(t.getDno());
                        if (temp != null) {
                            t.setOwnEquipId(temp.getOwnEquipId())
                                .setEssDno(temp.getEssDno());
                        }
                    }).collect(Collectors.toList()));
                }
                return param;
            })
            .flatMap(e -> {
                return pvRtDataService.dayGroupSampling(e, dis)
                    .collect(Collectors.toMap(PvRtDataGroupSampling::getTime,
                        PvRtDataGroupSampling::getItems,
                        (l1, l2) -> Stream.concat(l1.stream(), l2.stream()).collect(
                            Collectors.toList())));
            })
            .map(map -> {
                return map.keySet().stream()
                    .sorted(Comparator.comparing(e -> Integer.valueOf(e.replace(":", ""))))
                    .map(key -> {
                        PvRtDataGroupSampling data = new PvRtDataGroupSampling();
                        data.setTime(key)
                            .setItems(map.get(key).stream()
                                .sorted(Comparator.comparing(PvRtDataGroupItem::getIdx))
                                .collect(Collectors.toList()));
                        return data;
                    }).collect(Collectors.toList());
            });
    }

}
