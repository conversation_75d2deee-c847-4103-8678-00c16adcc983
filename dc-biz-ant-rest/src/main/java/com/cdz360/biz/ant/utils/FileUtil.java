package com.cdz360.biz.ant.utils;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.chargerlinkcar.framework.common.utils.CommonsMultipartFile;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileAttribute;
import java.nio.file.attribute.PosixFilePermission;
import java.nio.file.attribute.PosixFilePermissions;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.multipart.MultipartFile;
//import org.springframework.web.multipart.commons.CommonsMultipartFile;
import reactor.core.publisher.Mono;

@Slf4j
public class FileUtil {

    private static final String prefixPath = "/tmp/";

    public static Mono<MultipartFile> filePartToMultipartFile(FilePart file) {
        String zipPath = prefixPath + file.filename();
        return file.transferTo(Paths.get(zipPath))
            .then(Mono.just(zipPath))
            .map(filePath -> {
                FileItemFactory factory = new DiskFileItemFactory(16, null);
                FileItem item = factory.createItem("file", "text/plain", true, filePath);
                int bytesRead = 0;
                byte[] buffer = new byte[8192];
                try (FileInputStream fis = new FileInputStream(zipPath);
                    OutputStream os = item.getOutputStream();) {
                    while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
                        os.write(buffer, 0, bytesRead);
                    }
                } catch (Exception e) {
                    log.error("<< 文件处理异常. message = {}", e.getMessage(), e);
                    throw new DcServiceException("文件处理异常，请确认文件内容");
                }

                Mono.delay(Duration.ofSeconds(30))
                    .subscribe(e -> {
                        try {
                            Files.deleteIfExists(Paths.get(zipPath));
                        } catch (IOException ex) {
                            // nothing to do
                        }
                    });

                return new CommonsMultipartFile(item);
            });
    }

    public static void checkExcelFile(FilePart file) {
        if (null == file) {
            log.info("没有上传excel文件: MultipartFile={}.", file);
            throw new DcServiceException("没有提供excel文件, 文件字段使用 { file: xxx }");
        }

        String fileName = file.filename();
        if (null == fileName) {
            log.info("没有上传excel源文件");
            throw new DcServiceException("请上传excel文件, 文件字段使用 { file: xxx }");
        }

        if (fileName.lastIndexOf(".") == -1) {
            log.info("上传excel源文件没有后缀: file name={}", fileName);
            throw new DcServiceException("请上传正确的excel文件, excel文件后缀不存在");
        }

        String fileType = fileName.substring(fileName.lastIndexOf("."));
        if (!(".xls".equals(fileType) || ".xlsx".equals(fileType))) {
            log.info("上传excel文件后缀不支持, excel文件后缀支持: *.xls 或 *.xlsx, file name={}.",
                fileName);
            throw new DcServiceException(
                "请上传正确的excel文件, excel文件后缀支持: *.xls 或 *.xlsx");
        }
    }


    public static Mono<ObjectResponse<Map<String, Object>>> uploadImg(FilePart file, String path) {
        if (StringUtils.isBlank(path)) {
            path = prefixPath;
        }
        String originalFilename = file.filename();
        String substring = originalFilename.substring(originalFilename.lastIndexOf("."))
            .toLowerCase();
        Random random = new Random();
        String name = random.nextInt(10000) + System.currentTimeMillis() + substring;
        Mono<ObjectResponse<Map<String, Object>>> m;
        // 保存文件
        try {
            // 创建存储目录
            Path directory = Paths.get(path);
            if (!Files.exists(directory)) {
                Set<PosixFilePermission> perms = PosixFilePermissions.fromString("rwxr-xr-x");
                FileAttribute<Set<PosixFilePermission>> attr = PosixFilePermissions.asFileAttribute(
                    perms);
                Files.createDirectories(directory, attr);
            }
            // 生成唯一的文件名
            Path filePath = directory.resolve(name);

            m = file.transferTo(filePath)
                .then(Mono.just("文件上传成功"))
                .map(a -> {
                    // 设置文件权限为 rw-r--r--
                    Set<PosixFilePermission> filePerms = PosixFilePermissions.fromString(
                        "rw-r--r--");
                    try {
                        Files.setPosixFilePermissions(filePath, filePerms);
                    } catch (IOException e) {
                        throw new RuntimeException("设置文件权限失败", e);
                    }

                    Map<String, Object> map = new HashMap<>();
                    map.put("name", name);
                    String imgUrl = filePath.toString();
                    map.put("url", imgUrl);
                    return map;
                })
                .map(a -> RestUtils.buildObjectResponse(a))
                .onErrorResume(e -> {
                    log.error("文件上传失败: {}", e.getMessage(), e);
                    return Mono.error(new DcServiceException("文件上传失败"));
                });
        } catch (Exception e) {
            log.error("上传文件失败: {}", e.getMessage(), e);
            throw new DcServiceException("文件上传失败");
        }
        return m;
    }

}
