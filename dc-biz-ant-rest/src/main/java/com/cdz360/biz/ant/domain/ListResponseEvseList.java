package com.cdz360.biz.ant.domain;

import com.cdz360.base.model.base.dto.ListResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * ListResponseEvseList
 *
 * @since 7/12/2019 10:50 AM
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ListResponseEvseList<T> extends ListResponse {

    public ListResponseEvseList() {
        super();
    }

    public ListResponseEvseList(List<T> data) {
        super(data);
    }

    public ListResponseEvseList(List<T> data, Long total) {
        super(data, total);
    }

    private long onlineCount = 0;
    private long offlineCount = 0;
    private Integer currentCardStatus;
}