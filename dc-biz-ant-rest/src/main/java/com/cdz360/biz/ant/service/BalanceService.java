package com.cdz360.biz.ant.service;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.domain.vo.AccountRemainInfo;
import com.cdz360.biz.ant.domain.vo.UserVo;
import com.cdz360.biz.ant.feign.*;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.finance.type.TaxStatus;
import com.cdz360.biz.model.finance.type.TaxType;
import com.cdz360.biz.model.trading.cus.param.CusPayBillListParam;
import com.cdz360.biz.model.trading.cus.param.CusRefundAllParam;
import com.cdz360.biz.model.trading.cus.vo.CusPayBillVo;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.cdz360.biz.model.trading.order.po.PayBillPo;
import com.cdz360.biz.utils.feign.user.UserFeignClient;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.constant.TransTypeEnum;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.PointPo;
import com.chargerlinkcar.framework.common.domain.vo.CommCusRef;
import com.chargerlinkcar.framework.common.domain.vo.PointPoVo;
import com.chargerlinkcar.framework.common.feign.CommercialFeignClient;
import com.chargerlinkcar.framework.common.feign.UserCommercialFeignClient;
import com.chargerlinkcar.framework.common.service.DcCusBalanceService;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class BalanceService //implements IBalanceService
{


    @Autowired
    private TradingFeignClient tradingFeignClient;

    @Autowired
    private AntUserFeignClient userFeignClient;

    @Autowired
    private com.chargerlinkcar.framework.common.feign.UserFeignClient userFeignCommonClient;

    @Autowired
    private MerchantFeignClient merchantFeignClient;

    @Autowired
    private UserCommercialFeignClient userCommercialFeignClient;

    @Autowired
    private CommercialFeignClient commercialFeignClient;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private DcCusBalanceService dcCusBalanceService;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private UserFeignClient asyncUserFeignClient;


    /**
     * 商户手动更新用户余额
     *
     * @param userId    客户id
     * @param commId    商户id
     * @param amount    金额
     * @param transType 增加/减少
     * @param remark    备注
     * @param reason    原因
     * @return
     */

    public ObjectResponse updateBalance(long userId, long commId, BigDecimal amount, TransTypeEnum transType,
                                        String reason, String remark) {

        BaseResponse baseResponse = userFeignCommonClient.updateBalance(userId, commId, amount, transType, reason, remark);
        if (baseResponse.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
            throw new DcServiceException("充值失败");//测试要求提示信息改成"充值失败"
        }

        return new ObjectResponse<>("余额更新成功");
    }

    /**
     * 商户手动更新用户余额
     *
     * @param po
     * @return
     */

    public ObjectResponse<Integer> updateBalanceV2(PayBillPo po) {
        log.info("手动更新账户余额参数。param: {}", po);

        // 操作类型
        if (null == po.getFlowType()) {
            throw new DcArgumentException("请提供操作类型(增加/减少)");
        }
        this.checkParam(po);

        // 较少操作需要校验充值记录是否已经开票
        if (po.getFlowType() == DepositFlowType.OUT_FLOW) {
            ObjectResponse<Boolean> res = dataCoreFeignClient.checkRefPayBill(po.getRefBillNo());
            FeignResponseValidate.check(res);

            if (res.getData()) {
                throw new DcArgumentException("操作失败：该笔充值已关联了开票申请");
            }
        }

        ObjectResponse<Integer> res = userFeignCommonClient.updateBalanceV2(po);
        FeignResponseValidate.check(res);

        // 充值后逻辑处理
        if (DepositFlowType.IN_FLOW.equals(po.getFlowType())) {
            BaseResponse result = tradingFeignClient.postDeposit(po);

            if (PayAccountType.CORP.equals(po.getAccountType()) && po.getCorpId() != null) {
                authCenterFeignClient.resetEmailSendStatus(po.getCorpId(), null);
            }
        }
        return res;
    }

    private void checkParam(PayBillPo po) {
        log.info("参数校验: po={}", po);

        // 参数调整
        if (ObjectUtils.isEmpty(po.getAmount())) {
            po.setAmount(BigDecimal.ZERO);
        }
        if (ObjectUtils.isEmpty(po.getFreeAmount())) {
            po.setFreeAmount(BigDecimal.ZERO);
        }

        // 实际金额/赠送金额
        if (po.getAmount().compareTo(BigDecimal.ZERO) < 0 ||
                po.getFreeAmount().compareTo(BigDecimal.ZERO) < 0 ||
                (po.getAmount().compareTo(BigDecimal.ZERO) == 0 && po.getFreeAmount().compareTo(BigDecimal.ZERO) == 0)) {
            throw new DcArgumentException("金额数值无效，请输入正确充值金额");
        }


        if (DecimalUtils.getDecimalPlacesNum(po.getAmount()) > 2 ||
                DecimalUtils.getDecimalPlacesNum(po.getFreeAmount()) > 2) {
            throw new DcArgumentException("金额仅允许输入两位小数正数");
        }

        if (ObjectUtils.isEmpty(po.getAccountType()) || ObjectUtils.isEmpty(po.getAccountCode())) {
            throw new DcArgumentException("操作账户信息不能为空");
        }


        // 区分操作参数校验
        if (po.getFlowType() == DepositFlowType.IN_FLOW) {

            // 实际金额为零的情况不能设置为开票
            if (po.getAmount().compareTo(BigDecimal.ZERO) == 0 &&
                    po.getTaxStatus() == TaxStatus.YES) {
                throw new DcArgumentException("实际金额为0的情况，不能选择已开票状态");
            }

            // 开票状态: 开票/未开票参数不一样
            if (ObjectUtils.isEmpty(po.getTaxType())) {
                throw new DcArgumentException("开票状态或开票种类不能为空");
            }

            // 已开票情况
            if (po.getTaxStatus() == TaxStatus.YES && po.getTaxType() != TaxType.UNKNOWN) {
                // 开票种类，开票类型，税票号必填(产品)
                if (ObjectUtils.isEmpty(po.getTaxNo()) || ObjectUtils.isEmpty(po.getTaxNo()) || ObjectUtils.isEmpty(po.getTaxNo())) {
                    throw new DcArgumentException("开票种类，开票类型，税票号必填");
                }

                if (!NumberUtils.isNumber(po.getTaxNo())) {
                    throw new DcArgumentException("税票号仅允许输入数字");
                }

                if (po.getTaxNo().length() > 12) {
                    throw new DcArgumentException("税票号最多不超过12位");
                }

                if (!ObjectUtils.isEmpty(po.getExpressCompany()) && po.getExpressCompany().length() > 50) {
                    throw new DcArgumentException("物流公司名称最多50个字符");
                }
            }
        } else if (po.getFlowType() == DepositFlowType.OUT_FLOW) {
            if (DepositFlowType.OUT_FLOW.equals(po.getFlowType()) && ObjectUtils.isEmpty(po.getRefBillNo())) {
                throw new DcArgumentException("充值类型：减少时，对应的充值单号为空");
            }

            // 关联充值记录
            if (null == po.getRefBillNo()) {
                log.info("没有选择关联的充值订单");
                throw new DcArgumentException("请选择充值订单");
            }
        } // end else
    }


//    /**
//     * 获取用户余额
//     *
//     * @param uid 用户id
//     * @return
//     */
//
//    public ObjectResponse<Balance> getBalanceInfo(Long uid) {
//
//        ObjectResponse<Balance> jsonObjectRes = tradingFeignClient.getBalanceInfo(uid);
//        return jsonObjectRes;
//
//    }

//    /**
//     * 查询客户账户明细
//     *
//     * @param transType   交易类型
//     * @param payChannel  支付类型
//     * @param sourceId    来源类型
//     * @param startTime   开始时间
//     * @param endTime     结束时间
//     * @param payChannel  支付类型(1微信 2支付宝 3银联 ，4线下支付,5余额支付,6NFC储值卡,7无需支付,8其他)
//     * @param balanceType 账户类型
//     * @param tradingNo   订单号
//     * @return
//     *   
//     */
//
//    public ListResponse<BalanceRecord> findBalanceRecords(Page<BalanceRecord> page, Integer sourceId,
//                                                          Integer transType, String startTime, String endTime,
//                                                          Long uid, Integer payChannel, String balanceType, String tradingNo,
//                                                          Long topCommId, String commIdChain) {
//
//        ObjectResponse<com.chargerlinkcar.framework.common.domain.vo.UserVo> jsonObjectUserRes = userFeignCommonClient.findInfoByUid(uid, topCommId,
//                commIdChain);
//
//        if (jsonObjectUserRes.getStatus() != ResultConstant.RES_SUCCESS_CODE
//        ) {
//            throw new DcServiceException("当前商户没有权限查看此用户充值提现记录");
//        }
//
//        log.info("data:::" + jsonObjectUserRes.getData());
//        com.chargerlinkcar.framework.common.domain.vo.UserVo userVo = jsonObjectUserRes.getData();
//
//        if (ObjectUtils.isEmpty(userVo) || userVo.getCommId() == null) {
//            throw new DcServiceException("当前商户没有权限查看此用户充值提现记录");
//        }
//
//        Long commId = userVo.getCommId();
//
//        Integer _index = page.getPageNum();
//
//        Integer _size = page.getPageSize();
//
//        ListResponse<Long> commIdListRes = commercialFeignClient.getSubCommIdList(commIdChain);
//        FeignResponseValidate.check(commIdListRes);
//        ListResponse<BalanceRecord> jsonObjectRes = tradingFeignClient.findBalanceRecords(uid, null,
//                commIdListRes.getData(), _index, _size, sourceId, transType,
//                startTime, endTime, payChannel, balanceType, tradingNo);
//
//        FeignResponseValidate.check(jsonObjectRes);
//
//        List<BalanceRecord> balanceRecordList = jsonObjectRes.getData();
//
//        String idList = balanceRecordList.stream().filter(Objects::nonNull)
//                .map(s -> s.getOperatorId() + "").distinct().collect(Collectors.joining(","));
//
//        if (idList != null && !"".equals(idList)) {
//            ListResponse<SysUserVo> sysUserListRes = authCenterFeignClient.querySysUserByIds(idList);
//
//            if (sysUserListRes != null && sysUserListRes.getStatus() == ResultConstant.RES_SUCCESS_CODE) {
//                List<SysUserVo> sysUserVoList = sysUserListRes.getData();
//
//                Map<String, String> sysUserMap = new HashMap<>();
//                if (sysUserVoList != null && !sysUserVoList.isEmpty()) {
//                    for (SysUserVo su : sysUserVoList) {
//                        sysUserMap.put(su.getId() + "", su.getName());
//                    }
//                } else {
//                    sysUserMap = new HashMap<>();
//                }
//
//                for (BalanceRecord balanceRecord : balanceRecordList) {
//                    String name = sysUserMap.get(balanceRecord.getOperatorId());
//                    if (name != null) {
//                        balanceRecord.setOperator(name);
//                    }
//                }
//            }
//        }
//        jsonObjectRes.setData(balanceRecordList);
//        return jsonObjectRes;
//    }

//    /**
//     * 查询集团客户账户明细
//     *
//     * @param commIdList 当前商户及子商户id列表
//     * @param transType  交易类型
//     * @param payChannel 支付类型
//     * @param sourceId   来源类型
//     * @param startTime  开始时间
//     * @param endTime    结束时间
//     * @param payChannel 支付类型(1微信 2支付宝 3银联 ，4线下支付,5余额支付,6NFC储值卡,7无需支付,8其他)
//     * @return
//     *   
//     */
//
//    public ListResponse<BalanceRecord> findBalanceRecordsGroup(List<Long> commIdList, Page<BalanceRecord> page, Integer sourceId,
//                                                               Integer transType, String startTime, String endTime,
//                                                               Long groupUserId, Integer payChannel) {
//
//        ObjectResponse<BlocWalletVO> result = merchantFeignClient.getBlocUserByBlocUserId(groupUserId);
//
//        if (result.getStatus() != ResultConstant.RES_SUCCESS_CODE
//        ) {
//            throw new DcServiceException("当前商户没有权限查看此用户充值提现记录");
//        }
//
//        log.info("data:::" + result.getData());
//        BlocWalletVO blocWalletVO = result.getData();
//
//        if (ObjectUtils.isEmpty(blocWalletVO) || blocWalletVO.getCommId() == null) {
//            throw new DcServiceException("当前商户没有权限查看此用户充值提现记录");
//        }
//
//        Long commId = blocWalletVO.getCommId();
//
//        /*if(!commIdList.contains(commId)){
//            throw new DcServiceException("当前商户没有权限查看此用户充值提现记录");
//        }*/
//
//        Integer _index = page.getPageNum();
//
//        Integer _size = page.getPageSize();
//
//        ListResponse<BalanceRecord> jsonObjectRes = tradingFeignClient.findBalanceRecords(null, groupUserId,
//                commIdList, _index, _size, sourceId, transType,
//                startTime, endTime, payChannel, null, null);
//
//        FeignResponseValidate.check(jsonObjectRes);
//
//        List<BalanceRecord> balanceRecordList = jsonObjectRes.getData();
//
//        String idList = balanceRecordList.stream()
//                .filter(Objects::nonNull).map(s -> s.getOperatorId() + "").distinct().collect(Collectors.joining(","));
//
//        if (idList != null && !"".equals(idList)) {
//            ListResponse<SysUserVo> sysUserListRes = authCenterFeignClient.querySysUserByIds(idList);
//
//            if (sysUserListRes != null && sysUserListRes.getStatus() == ResultConstant.RES_SUCCESS_CODE) {
//                List<SysUserVo> sysUserVoList = sysUserListRes.getData();
//
//                Map<String, String> sysUserMap = new HashMap<>();
//                if (sysUserVoList != null && !sysUserVoList.isEmpty()) {
//                    for (SysUserVo su : sysUserVoList) {
//                        sysUserMap.put(su.getId() + "", su.getName());
//                    }
//                } else {
//                    sysUserMap = new HashMap<>();
//                }
//
//                for (BalanceRecord balanceRecord : balanceRecordList) {
//                    String name = sysUserMap.get(balanceRecord.getOperatorId());
//                    if (name != null) {
//                        balanceRecord.setOperator(name);
//                    }
//                }
//            }
//        }
//        jsonObjectRes.setData(balanceRecordList);
//        return jsonObjectRes;
//
//    }


    /**
     * 根据账户类型查询账户
     *
     * @param page
     * @param phone          用户手机号
     * @param commId         所属商户/子商户id
     * @param userId         用户id
     * @param enable         状态（1启用，0禁用）
     * @param payAccountType 账户类型
     * @param commIdChain    当前用户的商户及子商户列表
     * @return
     */
    public ListResponse<PointPoVo> queryPointPoByType(OldPageParam page, String phone, Long topCommId, Long commId, Long userId,
                                                      String cusName,
                                                      Boolean enable, PayAccountType payAccountType, String commIdChain) {
        ListResponse<PointPoVo> res = new ListResponse<>();
        log.info("查询账户列表 page: {}, phone: {},commId: {},enable: {},commIdChain: {},payAccountType: {}",
                page, phone, commId, enable, commIdChain, payAccountType);

        if (payAccountType == null) {
            throw new DcServiceException("账户类型为空");
        }

        if (StringUtils.isBlank(commIdChain)) {
            throw new DcArgumentException("参数错误,商户及子商户ID必须传入!");
        }

        switch (payAccountType) {
            case COMMERCIAL:
                res = getCommercialBalance(page, phone, enable, topCommId, commId, userId, cusName, commIdChain, payAccountType);
                break;
            case PERSONAL://个人不用去关系表查询
                //                res = getPersonalBalance(page, payAccountType, commId, commIdList);
                break;
            default:
                throw new DcServiceException("账户类型错误");
        }

        return res;
    }

    /**
     * 查询商户会员列表
     *
     * @param page
     * @param userPhone
     * @param enable
     * @param commId
     * @param commIdChain
     * @param payAccountType
     * @return
     */
    public ListResponse<PointPoVo> getCommercialBalance(OldPageParam page, String userPhone,
                                                        Boolean enable, Long topCommId, Long commId, Long userId,
                                                        String cusName,
                                                        String commIdChain, PayAccountType payAccountType) {

        ListResponse<CommCusRef> commCusRefList = userFeignClient.queryCommCusRefs(page.getPageNum(), page.getPageSize(), userPhone,
                enable, commId, userId, cusName, commIdChain);

        if (commCusRefList == null || commCusRefList.getStatus() != ResultConstant.RES_SUCCESS_CODE || commCusRefList.getData() == null) {
            throw new DcServiceException("查询商户用户关系表出错");
        }

        List<PointPoVo> pointPoVoList = new ArrayList<>();
        Map<Long, List<PointPo>> pointPoMap = new HashMap();
        Map<Long, List<Long>> uidMap = new HashMap();
        Map<String, CommCusRef> comCusRefMap = new HashMap<>();
        List<PointPo> pointList = new ArrayList<>();

        //子商户id集合
        Set<Long> commIdSet = new HashSet<>();
        ListResponse<Long> commIdListRes = this.commercialFeignClient.getSubCommIdList(commIdChain);
        FeignResponseValidate.check(commIdListRes);
        List<Long> commIdList = commIdListRes.getData();


        for (CommCusRef ccr : commCusRefList.getData()) {
            if (ccr.getCommId() != null && commIdList.contains(ccr.getCommId())) {
                commIdSet.add(ccr.getCommId());
            }
            comCusRefMap.put(String.valueOf(ccr.getUserId()) + ccr.getCommId(), ccr);
        }
        Set<Long> uidSet = new HashSet<>();
        List<Long> uidList = new ArrayList<>();
        for (Long cId : commIdSet) {
            //List<Long> uidList = new ArrayList<>();
            for (CommCusRef ccr : commCusRefList.getData()) {
                if (ccr.getCommId() != null && cId - ccr.getCommId() == 0) {
                    //uidList.add(ccr.getUserId());
                    uidSet.add(ccr.getUserId());
                }
            }
            //uidMap.put(cId, uidList);
        }
        uidList.addAll(uidSet);
        if (commIdSet != null && commIdSet.size() > 0) {
            for (Long cId : commIdSet) {
                ListResponse<PointPo> res = dcCusBalanceService.queryPointPo(payAccountType, uidList, topCommId, cId, enable, null);
                if (res != null && res.getStatus() == ResultConstant.RES_SUCCESS_CODE && res.getData() != null) {
                    //pointPoMap.put(cId, res.getData());
                    pointList.addAll(res.getData());
                }
            }
        }

        Map<String, PointPo> pointMap = new HashMap<>();
        for (PointPo p : pointList) {
            pointMap.put(p.getUid() + p.getSubUid(), p);
        }
        for (CommCusRef ccr : commCusRefList.getData()) {
            //for(PointPo p : pointList) {
//            CommCusRef ccr = comCusRefMap.get(String.valueOf(p.getUid()) + p.getSubUid());
//            if(ccr == null) {
//                continue;
//            }else if(!String.valueOf(ccr.getCommId()).equals(p.getSubUid())) {
//                continue;
//            }
            PointPoVo pointPoVo = new PointPoVo();
            pointPoVo.setId(ccr.getId());
            pointPoVo.setUserId(ccr.getUserId());
            pointPoVo.setUserName(ccr.getUserName());
            pointPoVo.setUserDefaultPayType(ccr.getUserDefaultPayType());
            pointPoVo.setUserPhone(ccr.getUserPhone());
            pointPoVo.setCommercialId(ccr.getCommId());
            pointPoVo.setCommercialName(ccr.getCommName());
            pointPoVo.setCommercialPhone(ccr.getCommPhone());
            pointPoVo.setCommLevel(ccr.getCommLevel());
            pointPoVo.setUserStatus(ccr.getUserStatus());
            pointPoVo.setEnable(ccr.getEnable());
            pointPoVo.setCreateTime(ccr.getCreateTime());
            pointPoVo.setUpdateTime(ccr.getUpdateTime());
            pointPoVo.setPoint(DecimalUtils.ZERO);
            pointPoVo.setAvailable(DecimalUtils.ZERO);
            pointPoVo.setUsed(DecimalUtils.ZERO);
            pointPoVo.setFrozen(DecimalUtils.ZERO);
            pointPoVo.setCost(DecimalUtils.ZERO);
            pointPoVo.setFrozenCost(DecimalUtils.ZERO);
            pointPoVo.setAvatar(ccr.getAvatar());
            pointPoVo.setScore(ccr.getScore());
            pointPoVo.setLevel(ccr.getLevel());

            PointPo p = pointMap.get(String.valueOf(ccr.getUserId()) + ccr.getCommId());
            //List<PointPo> pointPoList = pointPoMap.get(ccr.getCommId());
            //if (pointPoList != null) {
            //    for (PointPo pointPo : pointPoList) {
            //        if (pointPo.getUid().equals(ccr.getUserId() + "")) {
            if (p != null) {
                pointPoVo.setPoint(p.getPoint());
                pointPoVo.setAvailable(p.getAvailable());
                pointPoVo.setUsed(p.getUsed());
                pointPoVo.setFrozen(p.getFrozen());
                pointPoVo.setCost(p.getCost());
                pointPoVo.setFrozenCost(p.getFrozenCost());
                pointPoVo.setExpire(p.getExpire());
                //pointPoVo.setIsDefault(pointPo.getIsDefault());
                //pointPoVo.setEnable(pointPo.getEnable() ? 1 : 0);
            }
            //     }
            //}
            pointPoVoList.add(pointPoVo);
        }

        ListResponse<PointPoVo> res = new ListResponse<>(pointPoVoList, commCusRefList.getTotal());
        return res;
    }



    /**
     * 获取现金账户
     *
     * @param userId
     * @param appCommId
     * @return
     */

    public PointPoVo getPersonalBalance(long userId, long appCommId) {


        PointPo pointPo = dcCusBalanceService.getPoint(PayAccountType.PERSONAL, appCommId, appCommId, userId);
        if (pointPo == null) {
            return null;
        }
        //        if (pointPo == null) {
        //            pointPo = new PointPo(){{
        //                setUid(String.valueOf(userId));
        //                setPoint(0L);
        //                setAvailable(0L);
        //                setFrozen(0L);
        //                setEnable(true);
        //            }};
        //        }


        //查询集团商户信息
        ObjectResponse<Commercial> resultEntity = commercialFeignClient.getCommercial(appCommId);
        log.info("【查询集团商户信息】,res==>commercial:{};", resultEntity);
        if (resultEntity == null || resultEntity.getStatus() != ResultConstant.RES_SUCCESS_CODE) {
            throw new DcServiceException("获取失败");
        }
        Commercial commercial = resultEntity.getData();

        if (ObjectUtils.isEmpty(commercial)) {
            throw new DcServiceException("获取失败");
        }

        ObjectResponse<UserVo> userVoObjectResponse = userFeignClient.findInfoByUid(userId, appCommId, String.valueOf(appCommId));
        FeignResponseValidate.check(userVoObjectResponse);
        UserVo userVo = userVoObjectResponse.getData();

        if (userVo == null) {
            throw new DcServiceException("获取用户信息失败");
        }
        if (ObjectUtils.isEmpty(userVo)) {
            throw new DcServiceException("获取用户信息为空");
        }


        PointPoVo pointPoVo = new PointPoVo();
        pointPoVo.setUserId(userId);

        pointPoVo.setUserName(userVo.getUsername());
        pointPoVo.setUserPhone(userVo.getPhone());

        pointPoVo.setCommercialId(commercial.getId());
        pointPoVo.setCommercialName(commercial.getCommName());
        pointPoVo.setCommercialPhone(commercial.getPhone());
        pointPoVo.setCommLevel(commercial.getCommLevel());

        pointPoVo.setPoint(pointPo.getPoint());
        pointPoVo.setAvailable(pointPo.getAvailable());
        pointPoVo.setUsed(pointPo.getUsed());
        pointPoVo.setFrozen(pointPo.getFrozen());
        pointPoVo.setCost(pointPo.getCost());
        pointPoVo.setFrozenCost(pointPo.getFrozenCost());
        pointPoVo.setExpire(pointPo.getExpire());
        //pointPoVo.setIsDefault(pointPo.getIsDefault());
        pointPoVo.setEnable(pointPo.getEnable() ? 1 : 0);
        pointPoVo.setCreateTime(pointPo.getCreateTime());
        pointPoVo.setUpdateTime(pointPo.getUpdateTime());

        return pointPoVo;
    }

    public Mono<AccountRemainInfo> getCorpAccountInfo(CorpPo corpPo) {
        CusPayBillListParam cusPayBillListParam = new CusPayBillListParam();
        cusPayBillListParam.setClientType(AppClientType.CORP_WEB)
                .setUid(corpPo.getUid())
                .setTopCommId(corpPo.getTopCommId());
        return this.accountRemainInfo(PayAccountType.CORP, cusPayBillListParam);
    }

    private Mono<AccountRemainInfo> accountRemainInfo(
            PayAccountType accountType, CusPayBillListParam cusPayBillListParam) {

        Mono<AccountRemainInfo> info = asyncUserFeignClient.getCusPayBillList(cusPayBillListParam)
                .doOnNext(FeignResponseValidate::check)
                .map(ListResponse::getData)
                .map(cusPayBillList -> {
                    AccountRemainInfo result = new AccountRemainInfo();

//                    BigDecimal availableFee = cusPayBillList.stream()
//                            .map(CusPayBillVo::getAvailable)
//                            .reduce(BigDecimal.ZERO, BigDecimal::add);

//                    BigDecimal costFee = cusPayBillList.stream()
//                            .map(CusPayBillVo::getAmount)
//                            .reduce(BigDecimal.ZERO, BigDecimal::add);
//
//                    BigDecimal freeFee = cusPayBillList.stream()
//                            .map(CusPayBillVo::getFreeAmount)
//                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal canRefundFee = cusPayBillList.stream()
                            .map(CusPayBillVo::getCanRefundAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    return result
//                            .setAvailableFee(availableFee)
//                            .setCostFee(costFee)
//                            .setFreeFee(freeFee)
                            .setCanRefundFee(canRefundFee);
                });


        Long commId = PayAccountType.COMMERCIAL.equals(accountType) ?
                cusPayBillListParam.getCommId() : cusPayBillListParam.getTopCommId();

        Mono<Optional<PointPo>> pointPoMono = Mono.just(Optional.ofNullable(dcCusBalanceService.getPoint(
                accountType, cusPayBillListParam.getTopCommId(),
                commId, cusPayBillListParam.getUid())));

        return Mono.zip(info, pointPoMono)
                .flatMap(e -> {
                    AccountRemainInfo remain = e.getT1();
                    PointPo pointPo = e.getT2().orElse(null);
                    if (null != pointPo) {
                        remain.setAvailableFee(pointPo.getAvailable());
                        remain.setCostFee(pointPo.getCost().subtract(pointPo.getFrozenCost()));
                        remain.setFreeFee(pointPo.getAvailable().subtract(remain.getCostFee()));

                        remain.setRemainFee(null == pointPo.getPoint() ? BigDecimal.ZERO : pointPo.getPoint());
                        remain.setCannotRefundFee(remain.getRemainFee().subtract(remain.getCanRefundFee()));

                        // [实际_可退款金额] 等于 [可退款金额]
                        remain.setCanRefundCostFee(remain.getCanRefundFee());

                        // [可用金额] 减去 [实际_可退款金额] 等于 [赠送_可退款金额]
                        remain.setCanRefundFreeFee(remain.getAvailableFee().subtract(remain.getCanRefundCostFee()));

                    }
                    return Mono.just(remain);
                });

//        return asyncUserFeignClient.getCusPayBillList(cusPayBillListParam)
//                .doOnNext(FeignResponseValidate::check)
//                .map(ListResponse::getData)
//                .map(cusPayBillList -> {
//                    AccountRemainInfo result = new AccountRemainInfo();
//
////                    BigDecimal availableFee = cusPayBillList.stream()
////                            .map(CusPayBillVo::getAvailable)
////                            .reduce(BigDecimal.ZERO, BigDecimal::add);
//
////                    BigDecimal costFee = cusPayBillList.stream()
////                            .map(CusPayBillVo::getAmount)
////                            .reduce(BigDecimal.ZERO, BigDecimal::add);
////
////                    BigDecimal freeFee = cusPayBillList.stream()
////                            .map(CusPayBillVo::getFreeAmount)
////                            .reduce(BigDecimal.ZERO, BigDecimal::add);
//
//                    BigDecimal canRefundFee = cusPayBillList.stream()
//                            .map(CusPayBillVo::getCanRefundAmount)
//                            .reduce(BigDecimal.ZERO, BigDecimal::add);
//
//                    return result
////                            .setAvailableFee(availableFee)
////                            .setCostFee(costFee)
////                            .setFreeFee(freeFee)
//                            .setCanRefundFee(canRefundFee);
//                })
//                .doOnNext(remain -> {
//                    Long commId = PayAccountType.COMMERCIAL.equals(accountType) ?
//                            cusPayBillListParam.getCommId() : cusPayBillListParam.getTopCommId();
//
//                    PointPo pointPo = dcCusBalanceService.getPoint(
//                            accountType, cusPayBillListParam.getTopCommId(),
//                            commId, cusPayBillListParam.getUid());
//                    if (null != pointPo) {
//                        remain.setAvailableFee(pointPo.getAvailable());
//                        remain.setCostFee(pointPo.getCost().subtract(pointPo.getFrozenCost()));
//                        remain.setFreeFee(pointPo.getAvailable().subtract(remain.getCostFee()));
//
//                        remain.setRemainFee(null == pointPo.getPoint() ? BigDecimal.ZERO : pointPo.getPoint());
//                        remain.setCannotRefundFee(remain.getRemainFee().subtract(remain.getCanRefundFee()));
//                    }
//                });
    }

    public Mono<Integer> corpAccountRefund(CorpPo corpPo, AccountRemainInfo remainInfo) {
        CusPayBillListParam cusPayBillListParam = new CusPayBillListParam();
        cusPayBillListParam.setClientType(AppClientType.CORP_WEB)
                .setUid(corpPo.getUid())
                .setTopCommId(corpPo.getTopCommId());
        return this.accountRefund(cusPayBillListParam, remainInfo);
    }

    private Mono<Integer> accountRefund(CusPayBillListParam cusPayBillListParam, AccountRemainInfo remainInfo) {
        return asyncUserFeignClient.getCusPayBillList(cusPayBillListParam)
                .doOnNext(FeignResponseValidate::check)
                .map(ListResponse::getData)
                .map(cusPayBillList -> {
                    AccountRemainInfo result = new AccountRemainInfo();

//                    BigDecimal availableFee = cusPayBillList.stream()
//                            .map(CusPayBillVo::getAvailable)
//                            .reduce(BigDecimal.ZERO, BigDecimal::add);
//
//                    BigDecimal costFee = cusPayBillList.stream()
//                            .map(CusPayBillVo::getAmount)
//                            .reduce(BigDecimal.ZERO, BigDecimal::add);
//
//                    BigDecimal freeFee = cusPayBillList.stream()
//                            .map(CusPayBillVo::getFreeAmount)
//                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    BigDecimal canRefundFee = cusPayBillList.stream()
                            .map(CusPayBillVo::getCanRefundAmount)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                    return result
//                            .setAvailableFee(availableFee)
//                            .setCostFee(costFee)
//                            .setFreeFee(freeFee)
                            .setCanRefundFee(canRefundFee);
                })
                .doOnNext(remain -> {
                    if (!DecimalUtils.eq(remain.getCanRefundFee(), remainInfo.getCanRefundFee())) {
                        throw new DcServiceException("退款金额发生变更，请刷新页面重试！");
                    }
                })
                .map(remain -> {
                    CusRefundAllParam refundAllParam = new CusRefundAllParam();
                    refundAllParam.setClientType(cusPayBillListParam.getClientType())
                            .setCommId(cusPayBillListParam.getCommId())
                            .setUid(cusPayBillListParam.getUid())
                            .setTopCommId(cusPayBillListParam.getTopCommId())
                            .setOpUid(cusPayBillListParam.getOpUid())
                            .setOpName(cusPayBillListParam.getOpName());
                    return refundAllParam;
                })
                .flatMap(asyncUserFeignClient::refundAll)
                .map(res -> 1);
    }

    public Mono<AccountRemainInfo> getUserAccRemainInfo(Long topCommId, Long uid) {
        CusPayBillListParam cusPayBillListParam = new CusPayBillListParam();
        cusPayBillListParam.setClientType(AppClientType.MGM_WEB)
                .setUid(uid)
                .setTopCommId(topCommId);
        return this.accountRemainInfo(PayAccountType.PERSONAL, cusPayBillListParam);
    }

    public Mono<AccountRemainInfo> getCommAccRemainInfo(Long topCommId, Long commId, Long uid) {
        CusPayBillListParam cusPayBillListParam = new CusPayBillListParam();
        cusPayBillListParam.setClientType(AppClientType.MGM_WEB)
                .setCommId(commId)
                .setUid(uid)
                .setTopCommId(topCommId);
        return this.accountRemainInfo(PayAccountType.COMMERCIAL, cusPayBillListParam);
    }

    public Mono<Integer> userAccountRefund(Long topCommId, AccountRemainInfo remainInfo, Long opUid, String opName) {
        if (null == remainInfo.getUid()) {
            throw new DcArgumentException("用户ID无效");
        }

        CusPayBillListParam cusPayBillListParam = new CusPayBillListParam();
        cusPayBillListParam.setClientType(AppClientType.MGM_WEB)
                .setUid(remainInfo.getUid())
                .setTopCommId(topCommId)
                .setOpUid(opUid)
                .setOpName(opName);
        return this.accountRefund(cusPayBillListParam, remainInfo);
    }

    public Mono<Integer> commAccountRefund(Long topCommId, AccountRemainInfo remainInfo) {
        if (null == remainInfo.getUid()) {
            throw new DcArgumentException("用户ID无效");
        }

        if (null == remainInfo.getCommId()) {
            throw new DcArgumentException("商户ID无效");
        }

        CusPayBillListParam cusPayBillListParam = new CusPayBillListParam();
        cusPayBillListParam.setClientType(AppClientType.MGM_WEB)
                .setCommId(remainInfo.getCommId())
                .setUid(remainInfo.getUid())
                .setTopCommId(topCommId);
        return this.accountRefund(cusPayBillListParam, remainInfo);
    }
}
