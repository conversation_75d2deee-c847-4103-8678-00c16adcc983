package com.cdz360.biz.ant.domain;
//
//import com.chargerlinkcar.framework.common.domain.BasePage;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
//import java.io.Serializable;
//import java.util.Date;
//
//@Data
//@EqualsAndHashCode(callSuper = true)
//public class FinanceBill extends BasePage implements Serializable {
//    /**
//     *
//     */
//    private String billId;
//
//    /**
//     * 业务类型(1为充值，2为余额抵扣（支出），3.提现，4.保证金提取（保证金提现），5.保证金缴纳，6.充电入账，7充电退款,8充电退款（退至余额）,9赠送金支出,10BYD卡片充值,11BYD卡消费）
//     */
//    private Integer type;
//
//    /**
//     * 创建时间
//     */
//    private Date createTime;
//
//    /**
//     * 结束时间
//     */
//    private Date endTime;
//
//    /**
//     * 流水金额(以分为单位)
//     */
//    private Long billAmount;
//
//    /**
//     * 商户编号
//     */
//    private Long commercialId;
//
//    /**
//     * 备注
//     */
//    private String remark;
//
//    /**
//     * 用户id
//     */
//    private Long userId;
//
//    /**
//     * 去向账户类型（1商户账户，2商户代收账户，3用户微信，4用户支付宝）
//     */
//    private Integer whereaboutsType;
//
//    /**
//     * 来源账户类型（1商户账户，2商户代收账户，3用户微信，4用户支付宝）
//     */
//    private Integer originateType;
//
//    /**
//     * 交易ip地址
//     */
//    private String payIdAddr;
//
//    /**
//     * 账号类型（0-个人，1-集团）
//     */
//    private Integer accountType;
//
//    /**
//     * 用户名
//     */
//    private String userName;
//
//    public String getUserName() {
////        if (Base64Util.isBase64(userName)) {
////            return Base64Util.getFromBase64(userName);
////        } else {
//            return userName;
////        }
//    }
//
//    /**
//     * 设备运营商
//     */
//    private String deviceCarrieroperator;
//
//    /**
//     * 订单id
//     */
//    private String orderId;
//
//    /**
//     * 支付方式（1~19微信 20~39支付宝 40银联 ，50线下支付,60余额支付,70NFC储值卡,80无需支付、90集团支付、100其它预留、110BYD卡支付）
//     */
//    private Integer payModes;
//
//    /**
//     * 支付流水号（微信或者支付宝产生的）
//     */
//    private String payCertificateId;
//
//    /**
//     * 渠道类型（0:微信发起充电;1:APP在线发起充电;2:APP蓝牙发起充电;3:刷卡充电;4:桩上报离线数据）
//     */
//    private Integer channelId;
//
//    /**
//     * 平台类型（1公有云，2互联互通，3第三方)
//     */
//    private Integer platformType;
//
//    /**
//     * 收支类型（1实收，2实付，3代收，4代付）
//     */
//    private Integer balanceType;
//
//    /**
//     * 关联openId
//     */
//    private String connectionOpenId;
//
//    /**
//     * t_finance_bill
//     */
//    private static final long serialVersionUID = 1L;
//
//    /**
//     * 开始时间
//     */
//    private String beginTime;
//
//    /**
//     * 结束时间
//     */
//    private String stopTime;
//
//    /**
//     * 设备运营商ID
//     */
//    private Long deviceCommercialId;
//
//
//}