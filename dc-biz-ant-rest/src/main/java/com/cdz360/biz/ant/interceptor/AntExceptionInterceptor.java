package com.cdz360.biz.ant.interceptor;


import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.exception.DcException;
import com.cdz360.base.utils.StringUtils;
import com.chargerlinkcar.framework.common.exception.ChargerlinkException;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import org.springframework.web.server.ServerWebExchange;


/**
 * <AUTHOR>
 * @since Created on 9:36 2019/6/12.
 * 异常拦截器
 */
@Slf4j
@ControllerAdvice
public class AntExceptionInterceptor {

    @ExceptionHandler({DcException.class})
    @ResponseBody
    public BaseResponse handleDcException(DcException ex, ServerWebExchange exh) {
        LoggerHelper2.logException(log, ex, exh);
        BaseResponse result = new BaseResponse();
        result.setStatus(ex.getStatus());
        result.setError(ex.getMessage());
        return result;
    }

    @ExceptionHandler({IllegalArgumentException.class})
    @ResponseBody
    public BaseResponse handleAssertException(IllegalArgumentException ex, ServerWebExchange exh) {
        log.warn("exception message = {}, url = {}",
                ex.getMessage(), exh.getRequest().getPath().toString(), ex);
        BaseResponse result = new BaseResponse();
        result.setStatus(DcConstants.KEY_RES_CODE_ARGUMENT_ERROR);
        result.setError(ex.getMessage());
        return result;
    }

    @ExceptionHandler({MaxUploadSizeExceededException.class})
    @ResponseBody
    public BaseResponse handleMaxUploadSizeException(MaxUploadSizeExceededException ex, ServerWebExchange exh) {
        log.warn("exception message = {}, url = {}",
                ex.getMessage(), exh.getRequest().getPath().toString(), ex);
        BaseResponse result = new BaseResponse();
        result.setStatus(DcConstants.KEY_RES_CODE_SERVICE_ERROR);
        result.setError("文件大小超出最大限制！");
        return result;
    }

    @ExceptionHandler
    @ResponseBody
    public BaseResponse handle(Exception ex, ServerWebExchange exh) {
        log.warn("exception message = {}, url = {}",
                ex.getMessage(), exh.getRequest().getPath().toString(), ex);
        BaseResponse result = new BaseResponse();
        result.setStatus(DcConstants.KEY_RES_CODE_UNKNOWN_ERROR);
        result.setError("系统错误");
        if (ex instanceof IllegalArgumentException) {
            // Assert 失败的异常
            result.setStatus(2000);
            result.setError(ex.getMessage());
        } else {
            log.error("error = {}", ex.getMessage(), ex);
        }
        // TODO: 需要按异常类型做处理, 返回不同的status和error

        return result;
    }

    /**
     * 处理validate相关的异常
     */
    @ExceptionHandler({MethodArgumentNotValidException.class})
    @ResponseBody
    public BaseResponse handleValidateExcetion(MethodArgumentNotValidException ex, ServerWebExchange exh) {
        log.warn("exception message = {}, url = {}",
                ex.getMessage(), exh.getRequest().getPath().toString(), ex);
        String errorMsg;
        FieldError fieldError = ex.getBindingResult().getFieldError();
        if (fieldError != null) {
            errorMsg = fieldError.getDefaultMessage();
        } else {
            errorMsg = "参数错误..";
        }

        BaseResponse result = new BaseResponse();
        result.setStatus(DcConstants.KEY_RES_CODE_ARGUMENT_ERROR);
        result.setError(errorMsg);
        return result;
    }

    @ExceptionHandler({ChargerlinkException.class})
    @ResponseBody
    public BaseResponse handle(ChargerlinkException ex, ServerWebExchange exh) {
        log.warn("exception message = {}, url = {}",
                ex.getMessage(), exh.getRequest().getPath().toString(), ex);
        BaseResponse result = new BaseResponse();
        if (ex.getErrorCode() != null
            && ex.getErrorCode().intValue() != DcConstants.KEY_RES_CODE_SUCCESS) {
            result.setStatus(ex.getErrorCode());
        } else {
            result.setStatus(DcConstants.KEY_RES_CODE_SERVICE_ERROR);
        }
        if (StringUtils.isNotBlank(ex.getErrorMessage())) {
            result.setError(ex.getErrorMessage());
        } else if (StringUtils.isNotBlank(ex.getMessage())) {
            result.setError(ex.getMessage());
        } else {
            result.setError("服务异常(applet)");
        }
        return result;
    }

}
