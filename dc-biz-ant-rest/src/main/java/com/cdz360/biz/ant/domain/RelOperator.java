package com.cdz360.biz.ant.domain;

import com.cdz360.biz.ant.domain.vo.OperatorInfoVo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 *  客户运营商和设备运营商关系
 * @since 2019-02-20 9:57
 */
@Data
public class RelOperator implements Serializable {

    /**
     * 商户对应关联ID
     */
    private Long id;

    /**
     *公有云客户商户Id
     */
    private Long clientCommercialId;

    /**
     * 客户运营商
     */
    private String clientOperatorName;

    /**
     * 分享站点范围（0.全部、1.部分站点、2.不分享）
     */
    private Integer shareSiteType;

    /** 分享站点数 */
    private Integer sharSiteCount;

    private List<OperatorInfoVo> commercialList;
}
