//package com.cdz360.biz.ant.service.pv;
//
//import com.cdz360.base.model.base.dto.BaseResponse;
//import com.cdz360.base.model.base.dto.ListResponse;
//import com.cdz360.base.model.base.dto.ObjectResponse;
//import com.cdz360.biz.model.trading.iot.param.ListGtiCfgParam;
//import com.cdz360.biz.model.trading.iot.po.GtiCfgPo;
//import com.cdz360.biz.model.trading.iot.vo.GtiCfgVo;
//import com.cdz360.biz.utils.feign.iot.IotPvFeignClient;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import reactor.core.publisher.Mono;
//
///**
// * PvCfgService
// * 
// * @since 8/31/2021 4:31 PM
// * <AUTHOR>
// */
//@Slf4j
//@Service
//public class PvCfgService {
//
//    @Autowired
//    private IotPvFeignClient iotPvFeignClient;
//
//    public Mono<ListResponse<GtiCfgVo>> list(ListGtiCfgParam param) {
//        return iotPvFeignClient.list(param);
//    }
//
//    public Mono<ObjectResponse<GtiCfgPo>> getById(Long cfgId) {
//        return iotPvFeignClient.getById(cfgId);
//    }
//
//    public Mono<BaseResponse> add(GtiCfgPo param) {
//        return iotPvFeignClient.add(param);
//    }
//
//    public Mono<BaseResponse> edit(GtiCfgPo param) {
//        return iotPvFeignClient.edit(param);
//    }
//
//    public Mono<BaseResponse> del(Long cfgId) {
//        return iotPvFeignClient.del(cfgId);
//    }
//}