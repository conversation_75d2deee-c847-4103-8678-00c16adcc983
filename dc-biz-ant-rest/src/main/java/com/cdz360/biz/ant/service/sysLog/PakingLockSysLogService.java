package com.cdz360.biz.ant.service.sysLog;

import com.cdz360.base.model.base.vo.KvAny;
import com.cdz360.biz.auth.user.type.LogOpType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 客户相关系统操作日志
 */
@Slf4j
@Service
public class PakingLockSysLogService {

    @Autowired
    private BaseSysLogService sysUserLogService;

    /**
     * 新增地锁日志
     */
    public void addParkingLock(String siteName, String serialNumber, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                List.of(KvAny.of("地锁ID", serialNumber), KvAny.of("场站名称", siteName)),
                request);
    }

    /**
     * 新增地锁日志
     */
    public void removeParkingLock(String siteName, String serialNumber, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.DISABLE,
                List.of(KvAny.of("地锁ID", serialNumber), KvAny.of("场站名称", siteName)),
                request);
    }

    public void switchLock(String siteName, String serialNumber, Boolean open, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.DELIVERY,
                KvAny.of(open ? "开锁" : "闭锁", siteName + "/" + serialNumber),
                request);
    }

    public void cutPower(String siteName, String serialNumber, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.DELIVERY,
                KvAny.of("断电", siteName + "/" + serialNumber),
                request);
    }

    public void rebootLock(String siteName, String serialNumber, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.DELIVERY,
                KvAny.of("重启", siteName + "/" + serialNumber),
                request);
    }

}
