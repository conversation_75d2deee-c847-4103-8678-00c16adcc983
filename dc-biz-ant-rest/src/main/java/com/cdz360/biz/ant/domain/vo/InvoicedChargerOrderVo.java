package com.cdz360.biz.ant.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "开票充电订单信息")
@Data
@Accessors(chain = true)
public class InvoicedChargerOrderVo implements Serializable {

    @Schema(description = "是否存在占位费分时信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean hasOvertimeDivision;

    @Schema(description = "旧版充电订单号 建议使用字段: orderNo", hidden = true)
    @Deprecated
    private String orderId;//东正的id超出了前端的范围,所以这边处理一下

    @Schema(description = "充电订单号")
    private String orderNo;

    @Schema(description = "商户Id")
    private Long commercialId;

    @Schema(description = "场站ID")
    private String stationId;

    @Schema(description = "充电用户ID")
    private Long customerId;
    @Schema(description = "充电用户手机号")
    private String customerPhone;

    @Schema(description = "商户名称")
    private String commercialName;

    @Schema(description = "充电订单金额")
    private BigDecimal orderPrice;

    @Schema(description = "充电订单实付金额")
    private BigDecimal actualPrice;

    @Schema(description = "开票金额")
    private BigDecimal invoiceAmount;

    @Schema(description = "服务费实收金额(不含赠送)", example = "0.00")
    private BigDecimal servActualFee;

    @Schema(description = "电费实收金额(不含赠送)", example = "0.00")
    private BigDecimal elecActualFee;

    @Schema(description = "停充超时收费", example = "0.00")
    private BigDecimal parkActualFee;

    @Schema(description = "充电订单状态 已废弃")
    @Deprecated
    private Integer status;

    @Schema(description = "--")
    private String mobilePhone;

    @Schema(description = "场站名称")
    private String stationName;

    @Schema(description = "充电用户名称")
    private String customerName;

    @Schema(description = "发票记录ID")
    private Long invoicedId;

    /////////////////////////////////////////////////发票使用//////////////////////////////////////
    private Long chargeStartTime;//充电开始时间 毫秒
    private Long chargeEndTime;//充电结束时间 毫秒
    private ZonedDateTime createTime;//订单创建时间
    private ZonedDateTime stopTime;//订单结束时间

    @Schema(description = "充电订单创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date orderTime;//下单时间"2019-01-06 10:00:00"，从东正系统获取,为满足前端需要临时这么写

    private String chargingTimes;//充电时长，从东正系统获取,为满足前端需要临时这么写


}
