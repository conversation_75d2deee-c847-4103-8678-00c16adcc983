package com.cdz360.biz.ant.service.oa.process;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.DateUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.feign.reactor.ReactorBizBiFeignClient;
import com.cdz360.biz.model.oa.constant.OaConstants;
import com.cdz360.biz.model.oa.vo.OaExcel.ElecPayExcelItemVo;
import com.cdz360.biz.model.oa.vo.OaStatisticsInfo;
import com.cdz360.biz.model.oa.vo.OaStatisticsInfo.DiscountData;
import com.cdz360.biz.model.oa.vo.OaStatisticsInfo.PlatformData;
import com.cdz360.biz.oa.param.OaStartProcessParam;
import com.cdz360.biz.utils.feign.data.DeviceDataCoreClient;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class PayElecFeeOaStartProcessStrategy extends AbstractStartProcessStrategy {

    @Autowired
    private DeviceDataCoreClient deviceDataCoreClient;
    @Autowired
    private ReactorBizBiFeignClient reactorBizBiFeignClient;

    @Autowired
    private RedisIotReadService redisIotReadService;

    private static final String PRO_DEF_KEY = OaConstants.PD_ELEC_PAY;

    @PostConstruct
    public void init() {
        this.startProcessStrategyFactory.addStrategy(PRO_DEF_KEY, this);
    }

    @Override
    public Mono<Map<String, Object>> paramConversion(Map<String, Object> req) {

        ElecPayExcelItemVo form = JsonUtils.fromJson(
            JsonUtils.toJsonString(req), ElecPayExcelItemVo.class);

        Map<String, Object> res = new HashMap<>();

        res.put("oaKey", PRO_DEF_KEY);

        Optional.ofNullable(req.get("attachmentLink"))
            .ifPresent(e -> res.put("attachmentLink", e));

        res.put("siteId", form.getSiteId());
        res.put("siteName", form.getSiteName());

        /*
        真正提交时，参数校验代码逻辑会填入siteRemark，所以此处屏蔽
        ObjectResponse<SiteNoVo> siteNoVoRes = siteDataCoreFeignClient.getSiteNoById(
            form.getSiteId());
        FeignResponseValidate.checkIgnoreData(siteNoVoRes);
        Optional.ofNullable(siteNoVoRes.getData())
            .map(SiteNoVo::getRemark)
            .ifPresent(e -> res.put("siteRemark", e));
            */

        res.put("billDate", List.of(form.getBillStartDate(), form.getBillEndDate()));

        res.put("billAmount", form.getElecFee());

        res.put("billElec", form.getElec());

        res.put("autoDebit", form.getAutoDeduction());

        res.put("payCompanyName", form.getPaymentCompany());

        res.put("supplierName", form.getSupplier());

        res.put("bankName", form.getAccountBank());

        res.put("bankAccount", form.getAccount());

        res.put("payTimeFinal", form.getLastPaymentTime());

        res.put("note", form.getNote());

        ChargerOrderParam tempReq = new ChargerOrderParam();
        tempReq.setCommStationIds(List.of(form.getSiteId()))
            .setStopTimeFrom(
                String.valueOf(DateUtils.fromYyyyMmDdHhMmSs(form.getBillStartDate()).getTime()))
            .setStopTimeTo(
                String.valueOf(DateUtils.fromYyyyMmDdHhMmSs(form.getBillEndDate()).getTime()));
        return Mono.just(tempReq)
            .flatMap(reactorBizBiFeignClient::getChargerOrderDetail)
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .map(detailVo -> {
                PlatformData platformVo = new PlatformData();
                if (detailVo != null) {
                    Map<String, String> map = new HashMap<>();
                    map.put("orderNum", detailVo.getOrderNum().getTotal());
                    map.put("totalElec", detailVo.getElectricityAmount().getTotal());
                    map.put("totalElecFee", detailVo.getElecPriceAmount().getTotal());
                    map.put("totalServFee", detailVo.getServicePriceAmount().getTotal());
                    map.put("totalFee", detailVo.getOrderPriceAmount().getTotal());
                    platformVo = PlatformData.builder(JsonUtils.toJsonString(map));
                }

                DiscountData discountVo = new DiscountData();
                discountVo.setBillElec(Optional.of(form.getElec())
                    .map(e -> e.replaceAll(",", ""))
                    .map(BigDecimal::new)
                    .orElse(BigDecimal.ZERO));
                discountVo.setBillFee(Optional.of(form.getElecFee())
                    .map(e -> e.replaceAll(",", ""))
                    .map(BigDecimal::new)
                    .orElse(BigDecimal.ZERO));

                BigDecimal totalFee = Optional.of(platformVo.getTotalFee())
                    .orElse(BigDecimal.ZERO);
                discountVo.setActualServFee(totalFee.subtract(discountVo.getBillFee()));

                if (DecimalUtils.isZero(discountVo.getBillElec())) {
                    discountVo.setDiscount(BigDecimal.ZERO);
                } else {
                    BigDecimal totalElec = Optional.of(platformVo.getTotalElec())
                        .orElse(BigDecimal.ZERO);
                    BigDecimal divide = (discountVo.getBillElec().subtract(totalElec))
                        .divide(discountVo.getBillElec(), 4, RoundingMode.HALF_UP);
                    discountVo.setDiscount(divide.multiply(BigDecimal.valueOf(100)));
                }

                OaStatisticsInfo vo = new OaStatisticsInfo();
                vo.setPlatform(platformVo);
                vo.setDiscount(discountVo);
                res.put("statistics", JsonUtils.toJsonString(vo));

                log.info("paramConversion res: {}", JsonUtils.toJsonString(res));
                return res;
            });
    }

    @Override
    public Mono<OaStartProcessParam> validate(OaStartProcessParam param) {
        IotAssert.isNotNull(param.getData(), "表单数据不能为空");

        String siteId = param.getData().get("siteId").toString();
        IotAssert.isNotBlank(siteId, "场站ID无效");

        return deviceDataCoreClient.getSiteById(siteId)
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .map(site -> {
                param.getData().put("siteName", site.getSiteName());
                param.getData().put("siteRemark", site.getRemark());

                // FIXME: 重新计算平台统计数据，避免有更新

                param.setAutoForm(true); // 配置表单
                return param;
            });
    }

    @Override
    public Mono<String> resubmit(OaStartProcessParam param) {
        throw new DcServiceException("暂时不支持重新提交，请使用旧版本接口执行重新提交");
    }
}
