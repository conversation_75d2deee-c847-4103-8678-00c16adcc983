package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.ant.service.CommercialService;
import com.cdz360.biz.ant.service.LoginService;
import com.cdz360.biz.ant.service.WarningBiService;
import com.cdz360.biz.ant.service.order.OrderBiService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.cus.corp.po.CorpOrgLoginVo;
import com.cdz360.biz.model.cus.corp.vo.CorpOrgVO;
import com.cdz360.biz.model.site.type.AlarmEventTypeEnum;
import com.cdz360.biz.model.site.type.AlarmStatusEnum;
import com.cdz360.biz.model.trading.bi.dto.ChargerOrderBiDto;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.bi.param.ListBiCommercialParam;
import com.cdz360.biz.model.trading.bi.param.ListBiSiteParam;
import com.cdz360.biz.model.trading.bi.param.ListChargeOrderBiByVinParam;
import com.cdz360.biz.model.trading.bi.param.WarningBiParam;
import com.cdz360.biz.model.trading.bi.vo.CommercialBiVo;
import com.cdz360.biz.model.trading.bi.vo.SiteBiVo;
import com.cdz360.biz.model.trading.bi.vo.VinBiVo;
import com.cdz360.biz.model.trading.bi.warning.WarningBiDto;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.chargerlinkcar.framework.common.domain.WWarningRecord;
import com.chargerlinkcar.framework.common.feign.AuthCenterFeignClient;
import com.chargerlinkcar.framework.common.feign.ChargeOrderBiFeignClient;
import com.chargerlinkcar.framework.common.feign.CorpFeignClient;
import com.chargerlinkcar.framework.common.feign.DeviceMonitorFeignClient;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/bi/order")
@Tag(name = "管理平台-报表相关接口", description = "管理平台-报表相关接口")
public class ChargeOrderBiRest extends BaseController {

    @Autowired
    private LoginService loginService;
    @Autowired
    private ChargeOrderBiFeignClient chargeOrderBiFeignClient;
    @Autowired
    private CorpFeignClient corpFeignClient;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;
    @Autowired
    private DeviceMonitorFeignClient deviceMonitorFeignClient;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private CommercialService commercialService;

    @Autowired
    private OrderBiService orderBiService;

    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;

    @Autowired
    private WarningBiService warningBiService;

    private CorpOrgLoginVo getCorpByRequest(ServerHttpRequest request) {
        return loginService.getUserByRequest(request);
    }

    @Operation(summary = "企业平台车辆报表-组织下拉数据")
    @GetMapping(value = "/getUsableCorpOrg", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<CorpOrgVO> getUsableCorpOrg(ServerHttpRequest request,
        @RequestParam(value = "keyword", required = false) String keyword) {
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "请重新登录后再试");
        return authCenterFeignClient.getOrgByUserId(AntRestUtils.getToken2(request));
    }

    @Operation(summary = "根据车辆统计充电数据(用于企业平台)")
    @PostMapping(value = "/getBiListByVin", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListByVin(
        ServerHttpRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        if (param.getTopCommId() == null || param.getTopCommId() < 1L) {
//            log.info("param中未获取到有效topCommId,采用header中topCommId");
            Long topCommId = AntRestUtils.getTopCommId(request);
            if (topCommId == null || topCommId < 1L) {
                throw new DcArgumentException("参数错误,集团商户ID错误");
            }
            param.setTopCommId(topCommId);
        }
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "无法获取登录信息");
        if (CollectionUtils.isEmpty(param.getCorpOrgIds())) {
            param.setCorpOrgIds(corpOrgLoginVo.getOrgIds());
        }
            //原来使用的
//        ListResponse<ChargerOrderBiDto> res = chargeOrderBiFeignClient.getBiListByVin(param);
        ListResponse<ChargerOrderBiDto> res = chargeOrderBiFeignClient.getBiOrderListByVin(param);
        log.info(LoggerHelper2.formatLeaveLog(request));
        return res;
    }

    @Operation(summary = "根据车辆统计充电数据详情(用于企业平台)")
    @PostMapping(value = "/getBiListDetailByVin", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListDetailByVin(
        ServerHttpRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        if (param.getTopCommId() == null || param.getTopCommId() < 1L) {
//            log.info("param中未获取到有效topCommId,采用header中topCommId");
            Long topCommId = AntRestUtils.getTopCommId(request);
            if (topCommId == null || topCommId < 1L) {
                throw new DcArgumentException("参数错误,集团商户ID错误");
            }
            param.setTopCommId(topCommId);
        }
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "无法获取登录信息");
        if (CollectionUtils.isEmpty(param.getCorpOrgIds())) {
            param.setCorpOrgIds(corpOrgLoginVo.getOrgIds());
        }

        ListResponse<ChargerOrderBiDto> res = chargeOrderBiFeignClient.getBiListDetailByVin(param);
        log.info(LoggerHelper2.formatLeaveLog(request));
        return res;
    }

    @Operation(summary = "导出车辆充电数据(用于企业平台)")
    @PostMapping(value = "/exportBiListByVin", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ObjectResponse<ExcelPosition> exportBiListByVin(ServerHttpRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        if (param.getTopCommId() == null || param.getTopCommId().longValue() < 1L) {
//            log.info("param中未获取到有效topCommId,采用header中topCommId");
            Long topCommId = AntRestUtils.getTopCommId(request);
            if (topCommId == null || topCommId.longValue() < 1L) {
                throw new DcArgumentException("参数错误,集团商户ID错误");
            }
            param.setTopCommId(topCommId);
        }
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "无法获取登录信息");
        if (CollectionUtils.isEmpty(param.getCorpOrgIds())) {
            param.setCorpOrgIds(corpOrgLoginVo.getOrgIds());
        }
        return chargeOrderBiFeignClient.exportBiListByVin(param);
    }

    @Operation(summary = "根据企业客户统计充电数据(用于充电平台)")
    @PostMapping(value = "/getBiListByCorpCus", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListByCorpCus(ServerHttpRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        if (param.getTopCommId() == null || param.getTopCommId().longValue() < 1L) {
//            log.info("param中未获取到有效topCommId,采用header中topCommId");
            Long topCommId = AntRestUtils.getTopCommId(request);
            if (topCommId == null || topCommId.longValue() < 1L) {
                throw new DcArgumentException("参数错误,集团商户ID错误");
            }
            param.setTopCommId(topCommId);
        }
//        List<Long> commIdList = this.getCommIdList2(request);
        List<String> gids = AntRestUtils.getSysUserGids(request);
        if (CollectionUtils.isNotEmpty(gids)) {
            param.setGids(gids);
        } else {
            param.setCommIdChain(super.getCommIdChain2(request));
        }

        ListResponse<ChargerOrderBiDto> res = chargeOrderBiFeignClient.getBiListByCorpCus(param);
        log.info(LoggerHelper2.formatLeaveLog(request));
        return res;
    }

    @Operation(summary = "根据企业客户统计充电数据详情(用于充电平台)")
    @PostMapping(value = "/getBiListDetailByCorpCus", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListDetailByCorpCus(ServerHttpRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        if (param.getTopCommId() == null || param.getTopCommId().longValue() < 1L) {
//            log.info("param中未获取到有效topCommId,采用header中topCommId");
            Long topCommId = AntRestUtils.getTopCommId(request);
            if (topCommId == null || topCommId.longValue() < 1L) {
                throw new DcArgumentException("参数错误,集团商户ID错误");
            }
            param.setTopCommId(topCommId);
        }
//        List<Long> commIdList = this.getCommIdList2(request);
        param.setCommIdChain(super.getCommIdChain2(request));
        ListResponse<ChargerOrderBiDto> res = chargeOrderBiFeignClient.getBiListDetailByCorpCus(
            param);
        log.info(LoggerHelper2.formatLeaveLog(request));
        return res;
    }

    @Operation(summary = "导出企业客户充电数据(用于充电平台)")
    @PostMapping(value = "/exportBiListByCorpCus", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Mono<ObjectResponse<ExcelPosition>> exportBiListByCorpCus(
        ServerHttpRequest request, @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        if (param.getTopCommId() == null || param.getTopCommId().longValue() < 1L) {
//            log.info("param中未获取到有效topCommId,采用header中topCommId");
            Long topCommId = AntRestUtils.getTopCommId(request);
            if (topCommId == null || topCommId.longValue() < 1L) {
                throw new DcArgumentException("参数错误,集团商户ID错误");
            }
            param.setTopCommId(topCommId);
        }

        List<String> gids = AntRestUtils.getSysUserGids(request);
        if (CollectionUtils.isNotEmpty(gids)) {
            param.setGids(gids);
        } else {
            param.setCommIdChain(super.getCommIdChain2(request));
        }
        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("企业客户订单")
            .setFunctionMap(DownloadFunctionType.ORDER_DATA_BI_CORP)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return chargeOrderBiFeignClient.exportBiListByCorpCus(param);
    }

    @Operation(summary = "根据企业授信客户统计充电数据(用于企业平台)")
    @PostMapping(value = "/getBiListByCorpCreditCus", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListByCorpCreditCus(ServerHttpRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        if (param.getTopCommId() == null || param.getTopCommId().longValue() < 1L) {
//            log.info("param中未获取到有效topCommId,采用header中topCommId");
            Long topCommId = AntRestUtils.getTopCommId(request);
            if (topCommId == null || topCommId.longValue() < 1L) {
                throw new DcArgumentException("参数错误,集团商户ID错误");
            }
            param.setTopCommId(topCommId);
        }
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "无法获取登录信息");
        if (CollectionUtils.isEmpty(param.getCorpOrgIds())) {
            param.setCorpOrgIds(corpOrgLoginVo.getOrgIds());
        }

        ListResponse<ChargerOrderBiDto> res = chargeOrderBiFeignClient.getBiListByCorpCreditCus(
            param);
        log.info(LoggerHelper2.formatLeaveLog(request));
        return res;
    }

    @Operation(summary = "根据企业授信客户统计充电数据详情(用于企业平台)")
    @PostMapping(value = "/getBiListDetailByCorpCreditCus", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListDetailByCorpCreditCus(ServerHttpRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        if (param.getTopCommId() == null || param.getTopCommId().longValue() < 1L) {
//            log.info("param中未获取到有效topCommId,采用header中topCommId");
            Long topCommId = AntRestUtils.getTopCommId(request);
            if (topCommId == null || topCommId.longValue() < 1L) {
                throw new DcArgumentException("参数错误,集团商户ID错误");
            }
            param.setTopCommId(topCommId);
        }
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "无法获取登录信息");
        if (CollectionUtils.isEmpty(param.getCorpOrgIds())) {
            param.setCorpOrgIds(corpOrgLoginVo.getOrgIds());
        }

        ListResponse<ChargerOrderBiDto> res = chargeOrderBiFeignClient.getBiListDetailByCorpCreditCus(
            param);
        log.info(LoggerHelper2.formatLeaveLog(request));
        return res;
    }

    @Operation(summary = "导出企业授信客户充电数据(用于企业平台)")
    @PostMapping(value = "/exportBiListByCorpCreditCus", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ObjectResponse<ExcelPosition> exportBiListByCorpCreditCus(ServerHttpRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        if (param.getTopCommId() == null || param.getTopCommId().longValue() < 1L) {
//            log.info("param中未获取到有效topCommId,采用header中topCommId");
            Long topCommId = AntRestUtils.getTopCommId(request);
            if (topCommId == null || topCommId.longValue() < 1L) {
                throw new DcArgumentException("参数错误,集团商户ID错误");
            }
            param.setTopCommId(topCommId);
        }
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "无法获取登录信息");
        if (CollectionUtils.isEmpty(param.getCorpOrgIds())) {
            param.setCorpOrgIds(corpOrgLoginVo.getOrgIds());
        }
        return chargeOrderBiFeignClient.exportBiListByCorpCreditCus(param);
    }

    @Operation(summary = "根据在线卡统计充电数据(用于充电平台)")
    @PostMapping(value = "/getBiListByOnlineCard", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListByOnlineCard(ServerHttpRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        if (param.getTopCommId() == null || param.getTopCommId().longValue() < 1L) {
//            log.info("param中未获取到有效topCommId,采用header中topCommId");
            Long topCommId = AntRestUtils.getTopCommId(request);
            if (topCommId == null || topCommId.longValue() < 1L) {
                throw new DcArgumentException("参数错误,集团商户ID错误");
            }
            param.setTopCommId(topCommId);
        }
        List<String> gids = AntRestUtils.getSysUserGids(request);
        if (CollectionUtils.isNotEmpty(gids)) {
            param.setGids(gids);
        } else {
            param.setCommIdChain(AntRestUtils.getCommIdChain(request));
        }
        ListResponse<ChargerOrderBiDto> res = orderBiService.getBiListByOnlineCard(param);
        log.info(LoggerHelper2.formatLeaveLog(request));
        return res;
    }

    @Operation(summary = "根据在线卡统计充电数据详情(用于充电平台)")
    @PostMapping(value = "/getBiListDetailByOnlineCard", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListDetailByOnlineCard(ServerHttpRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        if (param.getTopCommId() == null || param.getTopCommId().longValue() < 1L) {
//            log.info("param中未获取到有效topCommId,采用header中topCommId");
            Long topCommId = AntRestUtils.getTopCommId(request);
            if (topCommId == null || topCommId.longValue() < 1L) {
                throw new DcArgumentException("参数错误,集团商户ID错误");
            }
            param.setTopCommId(topCommId);
        }
        param.setCommIdChain(AntRestUtils.getCommIdChain(request));
        ListResponse<ChargerOrderBiDto> res = chargeOrderBiFeignClient.getBiListDetailByOnlineCard(
            param);
        log.info(LoggerHelper2.formatLeaveLog(request));
        return res;
    }

    @Operation(summary = "导出在线卡充电数据(用于充电平台)")
    @PostMapping(value = "/exportBiListByOnlineCard", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Mono<ObjectResponse<ExcelPosition>> exportBiListByOnlineCard(ServerHttpRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        if (param.getTopCommId() == null || param.getTopCommId().longValue() < 1L) {
//            log.info("param中未获取到有效topCommId,采用header中topCommId");
            Long topCommId = AntRestUtils.getTopCommId(request);
            if (topCommId == null || topCommId.longValue() < 1L) {
                throw new DcArgumentException("参数错误,集团商户ID错误");
            }
            param.setTopCommId(topCommId);
        }
        List<String> gids = AntRestUtils.getSysUserGids(request);
        if (CollectionUtils.isNotEmpty(gids)) {
            param.setGids(gids);
        } else {
            param.setCommIdChain(AntRestUtils.getCommIdChain(request));
        }

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("在线卡订单")
            .setFunctionMap(DownloadFunctionType.ORDER_DATA_BI_ONLINE_CARD)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return chargeOrderBiFeignClient.exportBiListByOnlineCard(param);
    }

    @Operation(summary = "根据在线卡统计充电数据(用于企业平台)")
    @PostMapping(value = "/getBiListByOnlineCardOnCorp", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListByOnlineCardOnCorp(ServerHttpRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        if (param.getTopCommId() == null || param.getTopCommId().longValue() < 1L) {
//            log.info("param中未获取到有效topCommId,采用header中topCommId");
            Long topCommId = AntRestUtils.getTopCommId(request);
            if (topCommId == null || topCommId.longValue() < 1L) {
                throw new DcArgumentException("参数错误,集团商户ID错误");
            }
            param.setTopCommId(topCommId);
        }
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "无法获取登录信息");
        if (CollectionUtils.isEmpty(param.getCorpOrgIds())) {
            param.setCorpOrgIds(corpOrgLoginVo.getOrgIds());
        }
        ListResponse<ChargerOrderBiDto> res = chargeOrderBiFeignClient.getBiListByOnlineCardOnCorp(
            param);
        log.info(LoggerHelper2.formatLeaveLog(request));
        return res;
    }

    @Operation(summary = "根据在线卡统计充电数据详情(用于企业平台)")
    @PostMapping(value = "/getBiListDetailByOnlineCardOnCorp", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListDetailByOnlineCardOnCorp(
        ServerHttpRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        if (param.getTopCommId() == null || param.getTopCommId().longValue() < 1L) {
//            log.info("param中未获取到有效topCommId,采用header中topCommId");
            Long topCommId = AntRestUtils.getTopCommId(request);
            if (topCommId == null || topCommId.longValue() < 1L) {
                throw new DcArgumentException("参数错误,集团商户ID错误");
            }
            param.setTopCommId(topCommId);
        }
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "无法获取登录信息");
        if (CollectionUtils.isEmpty(param.getCorpOrgIds())) {
            param.setCorpOrgIds(corpOrgLoginVo.getOrgIds());
        }
        ListResponse<ChargerOrderBiDto> res = chargeOrderBiFeignClient.getBiListDetailByOnlineCardOnCorp(
            param);
        log.info(LoggerHelper2.formatLeaveLog(request));
        return res;
    }

    @Operation(summary = "导出在线卡充电数据(用于企业平台)")
    @PostMapping(value = "/exportBiListByOnlineCardOnCorp")
    public Mono<ObjectResponse<ExcelPosition>> exportBiListByOnlineCardOnCorp(
        ServerHttpRequest request, @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        if (param.getTopCommId() == null || param.getTopCommId().longValue() < 1L) {
//            log.info("param中未获取到有效topCommId,采用header中topCommId");
            Long topCommId = AntRestUtils.getTopCommId(request);
            if (topCommId == null || topCommId.longValue() < 1L) {
                throw new DcArgumentException("参数错误,集团商户ID错误");
            }
            param.setTopCommId(topCommId);
        }
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "无法获取登录信息");
        if (CollectionUtils.isEmpty(param.getCorpOrgIds())) {
            param.setCorpOrgIds(corpOrgLoginVo.getOrgIds());
        }

        AppClientType clientType = AntRestUtils.getAppClientType(request);
        if (AppClientType.MGM_WEB.equals(clientType)) {
            Long sysUid = AntRestUtils.getSysUid(request);
            DownloadApplyParam applyParam = new DownloadApplyParam();
            applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
                .setUid(sysUid)
                .setFunctionMap(DownloadFunctionType.ORDER_DATA_BI_ONLINE_CARD)
                .setReqParam(JsonUtils.toJsonString(param));
            return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
        } else {
            return Mono.just(chargeOrderBiFeignClient.exportBiListByOnlineCardOnCorp(param));
        }
    }

    @Operation(summary = "根据紧急卡统计充电数据(用于充电平台)")
    @PostMapping(value = "/getBiListByEmergencyCard", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListByEmergencyCard(ServerHttpRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        if (param.getTopCommId() == null || param.getTopCommId().longValue() < 1L) {
//            log.info("param中未获取到有效topCommId,采用header中topCommId");
            Long topCommId = AntRestUtils.getTopCommId(request);
            if (topCommId == null || topCommId.longValue() < 1L) {
                throw new DcArgumentException("参数错误,集团商户ID错误");
            }
            param.setTopCommId(topCommId);
        }
        List<String> gids = AntRestUtils.getSysUserGids(request);
        if (CollectionUtils.isNotEmpty(gids)) {
            param.setGids(gids);
        } else {
            param.setCommIdChain(super.getCommIdChain2(request));
        }
//        List<Long> commIdList = this.getCommIdList2(request);
//        param.setCommIds(commIdList);
        ListResponse<ChargerOrderBiDto> res = orderBiService.getBiListByEmergencyCard(param);
        log.info(LoggerHelper2.formatLeaveLog(request));
        return res;
    }

    @Operation(summary = "导出紧急卡充电数据(用于充电平台)")
    @PostMapping(value = "/exportBiListByEmergencyCard")
    public Mono<ObjectResponse<ExcelPosition>> exportBiListByEmergencyCard(
        ServerHttpRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        if (param.getTopCommId() == null || param.getTopCommId().longValue() < 1L) {
//            log.info("param中未获取到有效topCommId,采用header中topCommId");
            Long topCommId = AntRestUtils.getTopCommId(request);
            if (topCommId == null || topCommId.longValue() < 1L) {
                throw new DcArgumentException("参数错误,集团商户ID错误");
            }
            param.setTopCommId(topCommId);
        }

        List<String> gids = AntRestUtils.getSysUserGids(request);
        if (CollectionUtils.isNotEmpty(gids)) {
            param.setGids(gids);
        } else {
            param.setCommIdChain(super.getCommIdChain2(request));
        }
//        List<Long> commIdList = this.getCommIdList2(request);
//        param.setCommIds(commIdList);

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("紧急卡订单")
            .setFunctionMap(DownloadFunctionType.ORDER_DATA_BI_EMERGENCY_CARD)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return chargeOrderBiFeignClient.exportBiListByEmergencyCard(param);
    }

    @Operation(summary = "根据组织统计充电数据(用于企业平台)")
    @PostMapping(value = "/getBiListByOrg", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListByOrg(ServerHttpRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        if (param.getTopCommId() == null || param.getTopCommId().longValue() < 1L) {
//            log.info("param中未获取到有效topCommId,采用header中topCommId");
            Long topCommId = AntRestUtils.getTopCommId(request);
            if (topCommId == null || topCommId.longValue() < 1L) {
                throw new DcArgumentException("参数错误,集团商户ID错误");
            }
            param.setTopCommId(topCommId);
        }
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "无法获取登录信息");
        if (CollectionUtils.isEmpty(param.getCorpOrgIds())) {
            param.setCorpOrgIds(corpOrgLoginVo.getOrgIds());
        }
        ListResponse<ChargerOrderBiDto> res = chargeOrderBiFeignClient.getBiListByOrg(param);
//        log.info(LoggerHelper2.formatLeaveLog(request));
        return res;
    }

    @Operation(summary = "根据组织统计充电数据(用于企业平台)")
    @PostMapping(value = "/getBiListDetailByOrg", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListDetailByOrg(ServerHttpRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        if (param.getTopCommId() == null || param.getTopCommId().longValue() < 1L) {
//            log.info("param中未获取到有效topCommId,采用header中topCommId");
            Long topCommId = AntRestUtils.getTopCommId(request);
            if (topCommId == null || topCommId.longValue() < 1L) {
                throw new DcArgumentException("参数错误,集团商户ID错误");
            }
            param.setTopCommId(topCommId);
        }
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "无法获取登录信息");
        if (CollectionUtils.isEmpty(param.getCorpOrgIds())) {
            param.setCorpOrgIds(corpOrgLoginVo.getOrgIds());
        }
        ListResponse<ChargerOrderBiDto> res = chargeOrderBiFeignClient.getBiListDetailByOrg(param);
//        log.info(LoggerHelper2.formatLeaveLog(request));
        return res;
    }

    @Operation(summary = "导出组织充电数据(用于企业平台)")
    @PostMapping(value = "/exportBiListByOrg", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ObjectResponse<ExcelPosition> exportBiListByOrg(ServerHttpRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        if (param.getTopCommId() == null || param.getTopCommId().longValue() < 1L) {
//            log.info("param中未获取到有效topCommId,采用header中topCommId");
            Long topCommId = AntRestUtils.getTopCommId(request);
            if (topCommId == null || topCommId.longValue() < 1L) {
                throw new DcArgumentException("参数错误,集团商户ID错误");
            }
            param.setTopCommId(topCommId);
        }
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        IotAssert.isNotNull(corpOrgLoginVo, "无法获取登录信息");
        if (CollectionUtils.isEmpty(param.getCorpOrgIds())) {
            param.setCorpOrgIds(corpOrgLoginVo.getOrgIds());
        }
        return chargeOrderBiFeignClient.exportBiListByOrg(param);
    }

    @Operation(summary = "根据条件获取告警列表")
    @PostMapping(value = "/getWarningBiList", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<WarningBiDto> getWarningBiList(ServerHttpRequest request,
        @RequestBody WarningBiParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        param.setLocale(AntRestUtils.getLocale(request));
        return warningBiService.getWarningBiList(param,
            AntRestUtils.getCommIdChain(request),
            AntRestUtils.getSysUserGids(request));
    }

    @Operation(summary = "根据桩列表获取最近几条不同告警")
    @PostMapping(value = "/getRecentWarning", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ObjectResponse<Map<String, List<WWarningRecord>>> getRecentWarning(
        ServerHttpRequest request,
        @RequestBody WarningBiParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));

        // 确保查到的记录是本商户及下级相关
        String idChain = AntRestUtils.getCommIdChain(request);
        ListResponse<Long> subCommIdRes = commercialService.getSubCommIdList(idChain);
        FeignResponseValidate.check(subCommIdRes);
        param.setCommList(subCommIdRes.getData());

        return deviceMonitorFeignClient.getRecentWarning(param);

    }

    @Operation(summary = "桩管家获取订阅的列表", description = "桩管家获取订阅的列表")
    @PostMapping(value = "/getWarningBiListForWxLite", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<WarningBiDto> getWarningBiListForWxLite(ServerHttpRequest request,
        @RequestBody WarningBiParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));

        //确保当前用户订阅的场站，如果从桩管家模板消息过来，传递warningId,不再做场站告警码验证
        Long sysUid = AntRestUtils.getSysUid(request);
        if (sysUid == null) {
            throw new DcArgumentException("未登录状态");
        }

        //获取订阅的场站
        if (CollectionUtils.isEmpty(param.getSiteIdList())) {
            ListResponse<String> siteListResponse = dataCoreFeignClient.getUserSubSiteList(sysUid);
            if (siteListResponse != null && CollectionUtils.isNotEmpty(
                siteListResponse.getData())) {
                param.setSiteIdList(siteListResponse.getData());
            } else {
                return RestUtils.buildListResponse(null, 0L);
            }
        }
        //获取订阅故障码
        ListResponse<String> warningCodeListResponse = dataCoreFeignClient.getUserSubCodeList(
            sysUid);
        if (warningCodeListResponse != null && CollectionUtils.isNotEmpty(
            warningCodeListResponse.getData())) {
            param.setWarningCodeList(warningCodeListResponse.getData());
        } else {
            return RestUtils.buildListResponse(null, 0L);
        }

        ListResponse<WWarningRecord> response = deviceMonitorFeignClient.getWarningBiList(param);
        FeignResponseValidate.check(response);
        return RestUtils.buildListResponse(this.mapWarningBiDto(response.getData()),
            response.getTotal());
    }

    @Operation(summary = "根据条件导出告警列表")
    @PostMapping(value = "/exportWarningBiList", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Mono<ObjectResponse<ExcelPosition>> exportWarningBiList(
        ServerHttpRequest request, @RequestBody WarningBiParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));

        // 确保查到的记录是本商户及下级相关
        String idChain = AntRestUtils.getCommIdChain(request);
        ListResponse<Long> subCommIdRes = commercialService.getSubCommIdList(idChain);
        FeignResponseValidate.check(subCommIdRes);
        param.setCommList(subCommIdRes.getData());

        // 设置语言环境
        Locale locale = AntRestUtils.getLocale(request);
        // 如果是null就设置为null
        param.setLocale(locale);

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("告警统计")
            .setFunctionMap(DownloadFunctionType.WARNING_DETAIL)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return chargeOrderBiFeignClient.exportWarningBiList(param);
    }

    private List<WarningBiDto> mapWarningBiDto(List<WWarningRecord> list) {
        return list.stream().map(e -> {
            WarningBiDto dto = new WarningBiDto();
            dto.setWarningId(e.getWarningId())
                .setStartTime(e.getStartTime())
                .setEndTime(e.getEndTime())
                .setBoxOutFactoryCode(e.getBoxOutFactoryCode())
                .setPlugIdx(e.getConnectorId())
                .setSourceNo(e.getSourceNo())
                .setSiteId(e.getSiteId())
                .setSiteName(e.getSiteName())
                .setWarningType(AlarmEventTypeEnum.valueOf(e.getWarningType()))
                .setWarningCode(e.getWarningCode())
                .setStatus(AlarmStatusEnum.valueOf(e.getStatus()))
                .setWarningName(e.getWarningName())
                .setDeviceId(e.getDeviceId())
                .setEvseName(e.getEvseName())
                .setOrderNo(e.getOrderNo())
                .setPlugName(e.getPlugName())
                .setWarningInstructions(e.getWarningInstructions());
            return dto;
        }).collect(Collectors.toList());
    }

    //    @CheckToken(check = "getToken4Aspect")
    @PostMapping(value = "/getVinBi", consumes = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "车辆(VIN)订单汇总")
    public ListResponse<VinBiVo> getVinBi(
        ServerHttpRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false)
            + " param = {}", JsonUtils.toJsonString(param));

        // 获取当前登录用户的 idChain
        List<String> gids = AntRestUtils.getSysUserGids(request);
        if (CollectionUtils.isNotEmpty(gids)) {
            param.setGids(gids);
        } else {
            param.setCommIdChain(super.getCommIdChain2(request));
        }
        param.setTotal(true);

        return orderBiService.getVinBi(param);
    }

    @PostMapping(value = "/getVinBiDetail", consumes = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "车辆(VIN)订单汇总详情")
    public ListResponse<VinBiVo> getVinBiDetail(
        ServerHttpRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false)
            + " param = {}", JsonUtils.toJsonString(param));

//        // 获取当前登录用户的 idChain
//        param.setCommIdChain(super.getCommIdChain2(request));
        // 获取当前登录用户的 idChain
        List<String> gids = AntRestUtils.getSysUserGids(request);
        if (CollectionUtils.isNotEmpty(gids)) {
            param.setGids(gids);
        } else {
            param.setCommIdChain(super.getCommIdChain2(request));
        }

        return chargeOrderBiFeignClient.getVinBiDetail(param);
    }

    //    @CheckToken(check = "getToken4Aspect")
    @Operation(summary = "商户充电订单汇总")
    @PostMapping(value = "/getCommBi", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<CommercialBiVo> getCommBi(
        ServerHttpRequest request, @RequestBody ListBiCommercialParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false)
            + " param = {}", JsonUtils.toJsonString(param));

        // 获取当前登录用户的 idChain
        param.setCommIdChain(super.getCommIdChain2(request));
        param.setTotal(true);

        return this.orderBiService.getCommBi(param);
    }

    //    @CheckToken(check = "getToken4Aspect")
    @Operation(summary = "获取场站订单汇总数据")
    @PostMapping(value = "/getSiteBi", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<SiteBiVo> getSiteBi(
        ServerHttpRequest request, @RequestBody ListBiSiteParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) +
            " param = {}", JsonUtils.toJsonString(param));

        List<String> gids = AntRestUtils.getSysUserGids(request);
        if (CollectionUtils.isNotEmpty(gids)) {
            param.setGids(gids);
        } else {
            param.setCommIdChain(super.getCommIdChain2(request));
        }
        param.setTotal(true);

        return this.orderBiService.getSiteBi(param);
    }

    @Operation(summary = "导出车辆(VIN)订单汇总")
    @PostMapping(value = "/exportVinBi", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Mono<ObjectResponse<ExcelPosition>> exportVinBi(
        ServerHttpRequest request, @RequestBody ListChargeOrderBiByVinParam param) {
        log.info("导出车辆(VIN)订单汇总: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        // 获取当前登录用户的 idChain
        List<String> gids = AntRestUtils.getSysUserGids(request);
        if (CollectionUtils.isNotEmpty(gids)) {
            param.setGids(gids);
        } else {
            param.setCommIdChain(super.getCommIdChain2(request));
        }

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("VIN码订单")
            .setFunctionMap(DownloadFunctionType.ORDER_DATA_BI_VIN)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return this.chargeOrderBiFeignClient.exportVinBi(param);
    }

    @Operation(summary = "导出商户充电订单汇总")
    @PostMapping(value = "/exportCommBi", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Mono<ObjectResponse<ExcelPosition>> exportCommBi(
        ServerHttpRequest request,
        @RequestBody ListBiCommercialParam param) {
        log.info("导出商户充电订单汇总: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        // 获取当前登录用户的 idChain
        param.setCommIdChain(super.getCommIdChain2(request));

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("商户订单")
            .setFunctionMap(DownloadFunctionType.ORDER_DATA_BI_COMM)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return this.chargeOrderBiFeignClient.exportCommBi(param);
    }

    @Operation(summary = "导出场站充电订单汇总")
    @PostMapping(value = "/exportSiteBi", consumes = MediaType.APPLICATION_JSON_VALUE)
    public Mono<ObjectResponse<ExcelPosition>> exportSiteBi(
        ServerHttpRequest request, @RequestBody ListBiSiteParam param) {
        log.info("导出场站充电订单汇总: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        // 获取当前登录用户的 idChain
        List<String> gids = AntRestUtils.getSysUserGids(request);
        if (CollectionUtils.isNotEmpty(gids)) {
            param.setGids(gids);
        } else {
            param.setCommIdChain(super.getCommIdChain2(request));
        }

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("站点订单")
            .setFunctionMap(DownloadFunctionType.ORDER_DATA_BI_SITE)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return this.chargeOrderBiFeignClient.exportSiteBi(param);
    }
}
