package com.cdz360.biz.ant.domain.bundle;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.biz.model.iot.type.EvseVendor;
import com.cdz360.biz.model.upgradepg.type.BundleType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.util.Assert;

@Schema(description = "升级包上传参数")
@Data
@Accessors(chain = true)
public class UploadBundleParam {

    @Schema(description = "升级包文件", required = true)
    private FilePart file;

    @Schema(description = "包类型", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BundleType type;

    @Schema(description = "供应商（桩软件必填，控制器软件不填）")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EvseVendor vendor;

    @Schema(description = "软件版本号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long swVerCode;

    @Schema(description = "操作人ID", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long opId;

    @Schema(description = "操作人ID", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String opName;

    public void preCheck() {
        Assert.notNull(file, "请选中升级包文件上传");
        Assert.notNull(type, "请指定上传包类型");

        if (BundleType.EVSE_SOFT.equals(type) && vendor == null) {
            throw new DcArgumentException("请选择供应商");
        }
    }

}
