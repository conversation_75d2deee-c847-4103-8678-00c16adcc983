package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.service.ContractService;
import com.cdz360.biz.ant.service.LoginService;
import com.cdz360.biz.model.cus.corp.po.CorpOrgLoginVo;
import com.cdz360.biz.model.trading.contract.param.ContractListParam;
import com.cdz360.biz.model.trading.contract.vo.ContractVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;


@Tag(name = "企业客户合约管理相关接口")
@Slf4j
@RestController
@RequestMapping("/api/contract/corp")
public class CorpContractRest {

    @Autowired
    private LoginService loginService;

    @Autowired
    private ContractService contractService;

    private CorpOrgLoginVo getCorpByRequest(ServerHttpRequest request) {
        return loginService.getUserByRequest(request);
    }

    @Operation(summary = "企业合约列表")
    @PostMapping("/getContractList")
    public Mono<ListResponse<ContractVo>> getContractList(ServerHttpRequest request,
        @RequestBody ContractListParam param) {
        log.info("企业合约列表: {}", JsonUtils.toJsonString(param));
        CorpOrgLoginVo corpOrgLoginVo = getCorpByRequest(request);
        param.setCustomerName(corpOrgLoginVo.getCorpPo().getCorpName());

        return Mono.just(param)
            .map(contractService::getContractList);
    }
}
