package com.cdz360.biz.ant.service.yw;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.UserType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.reactor.AsyncBizBiFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorAuthCenterFeignClient;
import com.cdz360.biz.model.cus.message.po.MessagePo;
import com.cdz360.biz.model.cus.message.type.BroadCastType;
import com.cdz360.biz.model.cus.message.type.MsgType;
import com.cdz360.biz.model.cus.message.type.PlatformType;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.sys.param.GetYwUserParam;
import com.cdz360.biz.model.trading.iot.vo.EvseInfoVo;
import com.cdz360.biz.model.trading.yw.param.CreateYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.EvseDetailParam;
import com.cdz360.biz.model.trading.yw.param.ListYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.SolvedYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.TransYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.UpdateYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.UpdateYwOrderStatusParam;
import com.cdz360.biz.model.trading.yw.po.YwOrderPo;
import com.cdz360.biz.model.trading.yw.type.AssignWay;
import com.cdz360.biz.model.trading.yw.type.CreateYwOrderSourceType;
import com.cdz360.biz.model.trading.yw.type.YwOrderStatus;
import com.cdz360.biz.model.trading.yw.type.YwType;
import com.cdz360.biz.model.trading.yw.vo.EvseDetailVo;
import com.cdz360.biz.model.trading.yw.vo.YwOrderBi;
import com.cdz360.biz.model.trading.yw.vo.YwOrderLogVo;
import com.cdz360.biz.model.trading.yw.vo.YwOrderVo;
import com.cdz360.biz.utils.feign.user.UserFeignClient;
import com.cdz360.biz.utils.feign.yw.YwOrderClient;
import com.chargerlinkcar.framework.common.domain.SiteDetailInfoVo;
import com.chargerlinkcar.framework.common.domain.request.SiteGeoListRequest;
import com.chargerlinkcar.framework.common.feign.AuthCenterFeignClient;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.feign.SiteDataCoreFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.PlugNoParser;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class YwOrderService {

    private static final List<String> GOODS_LIST = List.of(
        "急停盖板",
        "过滤网",
        "高压直流充电模块",
        "塑壳断路器",
        "交流接触器",
        "直流接触器,输出",
        "直流接触器,功率分配",
        "熔断器",
        "散热风扇",
        "分流器",
        "电能表",
        "直流充电枪",
        "开关电源,控制电源",
        "开关电源,BMS,24V",
        "开关电源,BMS,12V",
        "触摸屏",
        "读卡器板",
        "4G模块",
        "存储卡",
        "物联网卡",
        "绝缘检测模块",
        "安全管理板",
        "充电桩控制板",
        "计费UI板",
        "功率分配控制板",
        "充电控制模组"
    );

//    @Autowired
//    private RedisIotReadService redisIotReadService;

    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;

    @Autowired
    private SiteDataCoreFeignClient siteDataCoreFeignClient;

    @Autowired
    private YwOrderClient ywOrderClient;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private ReactorAuthCenterFeignClient reactorAuthCenterFeignClient;

    @Autowired
    private AsyncBizBiFeignClient asyncBizBiFeignClient;

    @Autowired
    private UserFeignClient userFeignClient;

    public Mono<EvseDetailVo> evseDetail(List<String> gids, EvseDetailParam param) {
        return Mono.just(param)
            .doOnNext(p -> {
                // 这里存在问题: 手动输入和扫码入参不一样
                if (CollectionUtils.isNotEmpty(p.getPlugNoList())) {
                    List<String> evseNoList = p.getPlugNoList()
                        .stream()
                        .map(plugNo -> new PlugNoParser(plugNo).getEvseNo())
                        .collect(Collectors.toList());
                    param.setEvseNoList(evseNoList);
                }
            })
            .doOnNext(p -> {
                if (CollectionUtils.isEmpty(param.getEvseNoList())) {
                    throw new DcArgumentException("桩编号列表不能为空");
                }
            })
            .map(p -> iotDeviceMgmFeignClient.getEvseInfoList(
                new ListEvseParam().setEvseNoList(p.getEvseNoList())))
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .map(evseList -> {
                if (CollectionUtils.isEmpty(evseList)) {
                    throw new DcArgumentException("桩编号无效,请联系管理员");
                }

                Optional<EvseInfoVo> first = evseList.stream()
                    .filter(e -> StringUtils.isNotBlank(e.getSiteId()))
                    .findFirst();
                if (first.isEmpty()) {
                    throw new DcArgumentException("桩还没有绑定场站");
                }

                // 获取场站信息
                ObjectResponse<SiteDetailInfoVo> siteDetail = siteDataCoreFeignClient.getSiteDetail(
                    new SiteGeoListRequest().setSiteId(first.get().getSiteId()));
                FeignResponseValidate.check(siteDetail);
                SiteDetailInfoVo site = siteDetail.getData();

                return new EvseDetailVo()
                    .setSiteId(site.getId())
                    .setSiteName(site.getSiteName())
                    .setSiteAddress(site.getAddress())
                    .setSiteGcType(site.getGcType())
                    .setSiteSupplyType(site.getSupplyType())
                    .initEvseInfoList(evseList);
            });
    }

    public Mono<List<String>> goodsList() {
        return Mono.just(GOODS_LIST);
    }

    public Mono<ObjectResponse<YwOrderVo>> updateYwOrderTag(UpdateYwOrderParam param) {
        IotAssert.isNotBlank(param.getYwOrderNo(), "运维工单无效");
        IotAssert.isNotNull(param.getTag(), "请提供考核标签");
        return Mono.just(param)
            .flatMap(ywOrderClient::updateYwOrderTag);
    }

    public Mono<ListResponse<YwOrderVo>> findYwOrder(ListYwOrderParam param) {
        return Mono.just(param)
            .flatMap(ywOrderClient::findYwOrder);
    }

    public Mono<ListResponse<YwOrderBi>> ywOrderBi(ListYwOrderParam param) {
        return Mono.just(param)
            .flatMap(ywOrderClient::ywOrderBi);
    }

    public Mono<ObjectResponse<YwOrderVo>> getYwOrderDetail(String ywOrderNo) {
        if (StringUtils.isBlank(ywOrderNo)) {
            throw new DcArgumentException("运维工单编号无效");
        }

        return Mono.just(ywOrderNo)
            .flatMap(ywOrderClient::getYwOrderDetail)
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .flatMap(vo -> {
                // 获取运维人的手机号(联系方式)
                if (vo.getMaintUid() != null) {
                    return reactorAuthCenterFeignClient.getSysUserById(vo.getMaintUid())
                        .doOnNext(FeignResponseValidate::check)
                        .map(ObjectResponse::getData)
                        .map(sysUser -> vo.setMaintPhone(sysUser.getPhone()));
                } else {
                    return Mono.just(vo);
                }
            })
            .flatMap(vo -> {
                // 获取创建人的手机号(联系方式)
                Mono<YwOrderVo> mono = Mono.just(vo);
                if (UserType.CUSTOMER.equals(vo.getCreateOpType())) {
                    mono = mono.flatMap(
                            v -> userFeignClient.queryUserByUidAndCommId(vo.getCreateOpUid(), null))
                        .doOnNext(res -> FeignResponseValidate.check(res))
                        .doOnNext(res -> vo.setCreateUserPhone(res.getData().getPhone()))
                        .map(v -> vo);
                } else {
                    mono = mono.flatMap(v -> {
                        return reactorAuthCenterFeignClient.getSysUserById(vo.getMaintUid())
                            .doOnNext(FeignResponseValidate::check)
                            .map(ObjectResponse::getData)
                            .map(sysUser -> vo.setCreateUserPhone(sysUser.getPhone()));
                    });
                }

                return mono;
            })
            .map(RestUtils::buildObjectResponse);
    }

    public Mono<ObjectResponse<YwOrderPo>> createYwOrder(
        List<String> gids, CreateYwOrderParam param) {
        return Mono.just(param)
            .doOnNext(CreateYwOrderParam::checkParam)
            .doOnNext(p -> {
                ListResponse<EvseInfoVo> evseInfoList = iotDeviceMgmFeignClient.getEvseInfoList(
                    new ListEvseParam().setEvseNoList(p.getEvseNoList()));
                FeignResponseValidate.checkIgnoreData(evseInfoList);

                if (CollectionUtils.isEmpty(evseInfoList.getData())) {
                    throw new DcArgumentException("桩编号无效,请联系管理员");
                }

                if (param.getYwType() == null || YwType.CHARGE.getCode() == param.getYwType()) {
                    if (CollectionUtils.isEmpty(evseInfoList.getData()) ||
                        evseInfoList.getData().size() != p.getEvseNoList().size()) {
                        throw new DcArgumentException("桩编号无效,请联系管理员");
                    }

                    // 是否过质保期
                    // 全部为空，则不存在质保期一说
                    // 只要存在未过质保期的桩则为否
                    Optional<EvseInfoVo> first = evseInfoList.getData().stream()
                        .filter(evse -> null != evse.getExpireDate() &&
                            evse.getExpireDate().getTime() > new Date().getTime())
                        .findFirst();

                    if (first.isPresent()) {
                        param.setOverExpireDate(false);
                    } else {
                        // 需要判断全为空
                        first = evseInfoList.getData().stream()
                            .filter(evse -> null != evse.getExpireDate())
                            .findFirst();

                        if (first.isPresent()) {
                            param.setOverExpireDate(true);
                        } // end else: null
                    }

                }
                // 场站名称: 站内新使用
                param.setSiteName(evseInfoList.getData().get(0).getSiteName());
                param.setSiteCommId(evseInfoList.getData().get(0).getSiteCommId());
                param.setEvseStrList(evseInfoList.getData().stream()
                    .map(e -> {
                        StringBuffer str = new StringBuffer();
                        str.append(StringUtils.isNotBlank(e.getEvseNo()) ? e.getEvseNo() : "");
                        str.append(StringUtils.isNotBlank(e.getName()) ? e.getName() : "");
                        return str.toString();
                    }).collect(Collectors.toList()));
            })
            .doOnNext(p -> {
                // 创建人 --> 判断工单来源
                ObjectResponse<CreateYwOrderSourceType> ywOrderSrc = authCenterFeignClient.getYwOrderSrc(
                    p.getOpUid());
                FeignResponseValidate.check(ywOrderSrc);
                param.setSourceType(ywOrderSrc.getData());
            })
            .doOnNext(p -> {
                // 运维人员自动选择
                // 当前登录人为运维人员，则默认自己为工单运维人员
                // 否则，自动选择当前场站所属商户的其中一个运维运维人员
                SysUserVo ywUser;
                if (param.getAssignWay() == null || param.getAssignWay().equals(AssignWay.AUTO)) {
                    GetYwUserParam ywUserParam = new GetYwUserParam();
                    ywUserParam.setEquipRepairEntry(p.getEquipRepairEntry())
                        .setUid(p.getOpUid())
                        .setSiteId(p.getSiteId())
                        .setSiteCommId(p.getSiteCommId());
                    ObjectResponse<Optional<SysUserVo>> resp = authCenterFeignClient.getYwUser(
                        ywUserParam);
                    FeignResponseValidate.check(resp);
                    if (resp.getData().isEmpty()) {
                        throw new DcServiceException("场站未配置运维负责人");
                    }
                    ywUser = resp.getData().get();

                } else {
                    ObjectResponse<SysUserVo> resp = authCenterFeignClient.getSysUserVoByUid(
                        param.getMaintUid());
                    FeignResponseValidate.check(resp);
                    ywUser = resp.getData();
                }
//                    ObjectResponse<SysUserVo> ywUser = authCenterFeignClient.getYwUser(
//                            p.getOpUid(), p.getSiteId(), p.getSiteCommId());
//                    FeignResponseValidate.check(ywUser);

                param.setMaintName(ywUser.getName())
                    .setMaintUid(ywUser.getId())
                    .setMaintType(UserType.SYS_USER)
                    .setMaintCommId(ywUser.getCommId());
            })
            .flatMap(ywOrderClient::createYwOrder)
            .doOnNext(FeignResponseValidate::check)
            .doOnNext(res -> {
                MessagePo messagePo = new MessagePo();
                messagePo.setBroadcast(BroadCastType.PART)
                    .setTargetUid(List.of(param.getMaintUid()))
                    .setCommIdList(List.of(param.getMaintCommId()))
                    .setContent("<p>该场站有新的待接收运维工单，请及时处理</p>")
                    .setMsgType(MsgType.YW_REMINDER)
                    .setPlatformList(List.of(PlatformType.MANAGE))
                    .setTitle(param.getSiteName()); // 场站名称
                reactorAuthCenterFeignClient.addMessage(null, messagePo)
                    .subscribe();
            });
    }

    public Mono<ObjectResponse<YwOrderVo>> transYwOrder(TransYwOrderParam param) {
        return Mono.just(param)
            .map(p -> {
                if (null == p.getUid()) {
                    throw new DcArgumentException("请指定运维用户ID");
                }

//                    if (StringUtils.isBlank(p.getUserName())) {
//                        throw new DcArgumentException("用户名称不能为空");
//                    }

                // 获取用户信息
                return authCenterFeignClient.getSysUserById(p.getUid());
            })
            .doOnNext(FeignResponseValidate::checkIgnoreData)
            .map(ObjectResponse::getData)
            .map(user -> {
                if (null == user) {
                    throw new DcArgumentException("用户ID无效");
                }

                return param.setUserType(UserType.SYS_USER)
                    .setUserName(user.getName())
                    .setMaintCommId(user.getCommId());
            })
            .flatMap(ywOrderClient::transYwOrder)
            .doOnNext(FeignResponseValidate::check)
            .doOnNext(res -> {
                MessagePo messagePo = new MessagePo();
                messagePo.setBroadcast(BroadCastType.PART)
                    .setTargetUid(List.of(res.getData().getMaintUid()))
                    .setCommIdList(List.of(param.getMaintCommId()))
                    .setContent("<p>该场站有新的待接收运维工单，请及时处理</p>")
                    .setMsgType(MsgType.YW_REMINDER)
                    .setPlatformList(List.of(PlatformType.MANAGE))
                    .setTitle(res.getData().getSiteName()); // 场站名称
                reactorAuthCenterFeignClient.addMessage(null, messagePo)
                    .subscribe();
            });
    }

    public Mono<BaseResponse> receivedYwOrder(UpdateYwOrderStatusParam param) {
        if (CollectionUtils.isEmpty(param.getYwOrderNoList())) {
            throw new DcArgumentException("运维工单编号无效");
        }

        return Mono.just(param)
            .map(p -> p.setOrderStatus(YwOrderStatus.RECEIVED))
            .flatMap(ywOrderClient::updateOrderStatus);
    }

    public Mono<BaseResponse> checkYwOrder(UpdateYwOrderStatusParam param) {
        if (CollectionUtils.isEmpty(param.getYwOrderNoList())) {
            throw new DcArgumentException("运维工单编号无效");
        }

        if (null == param.getOrderStatus()) {
            throw new DcArgumentException("请指定质检结果状态值");
        }

        return Mono.just(param)
            .flatMap(ywOrderClient::updateOrderStatus);
    }

    public Mono<BaseResponse> orderDel(UpdateYwOrderStatusParam param) {
        return ywOrderClient.updateOrderStatus(param);
    }

    public Mono<BaseResponse> startYwOrder(UpdateYwOrderStatusParam param) {
        if (CollectionUtils.isEmpty(param.getYwOrderNoList())) {
            throw new DcArgumentException("运维工单编号无效");
        }

        return Mono.just(param)
            .map(p -> p.setOrderStatus(YwOrderStatus.PROCESSING))
            .flatMap(ywOrderClient::updateOrderStatus);
    }

    public Mono<BaseResponse> suspendYwOrder(UpdateYwOrderStatusParam param) {
        if (CollectionUtils.isEmpty(param.getYwOrderNoList())) {
            throw new DcArgumentException("运维工单编号无效");
        }

        return Mono.just(param)
            .map(p -> p.setOrderStatus(YwOrderStatus.SUSPEND))
            .flatMap(ywOrderClient::updateOrderStatus);
    }

    public Mono<ObjectResponse<YwOrderPo>> saveYwOrder(SolvedYwOrderParam param) {
        return Mono.just(param)
            .doOnNext(SolvedYwOrderParam::checkParam)
            .flatMap(ywOrderClient::saveYwOrder);
    }

    public Mono<ObjectResponse<YwOrderPo>> solvedYwOrder(SolvedYwOrderParam param) {
        return Mono.just(param)
            .doOnNext(SolvedYwOrderParam::checkParam)
            .flatMap(ywOrderClient::solvedYwOrder);
    }

    public Mono<ListResponse<YwOrderLogVo>> ywOrderTransList(String ywOrderNo) {
        if (StringUtils.isBlank(ywOrderNo)) {
            throw new DcArgumentException("运维工单编号无效");
        }

        return Mono.just(ywOrderNo)
            .flatMap(ywOrderClient::ywOrderTransList);
    }

//    public Mono<ObjectResponse<ExcelPosition>> exportYwOrderPdf(String ywOrderNo) {
//        if (StringUtils.isBlank(ywOrderNo)) {
//            throw new DcArgumentException("运维工单编号无效");
//        }
//
//        return asyncBizBiFeignClient.exportYwOrder(ywOrderNo);
//    }

    public Mono<ObjectResponse<YwOrderVo>> getSiteLatestRec(String siteId) {
        if (StringUtils.isBlank(siteId)) {
            throw new DcArgumentException("场站ID无效");
        }

        return ywOrderClient.getSiteLatestRec(siteId);
    }

//    public Mono<ObjectResponse<ExcelPosition>> exportYwOrderExcel(ListYwOrderParam param) {
//        return asyncBizBiFeignClient.exportYwOrderExcel(param);
//    }
}
