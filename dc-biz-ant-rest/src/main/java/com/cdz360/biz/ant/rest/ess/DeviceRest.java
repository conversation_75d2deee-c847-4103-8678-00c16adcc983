package com.cdz360.biz.ant.rest.ess;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.service.ess.DeviceService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.trading.ess.param.AddEssCfgParam;
import com.cdz360.biz.model.trading.ess.param.AddGtiCfgParam;
import com.cdz360.biz.model.trading.ess.param.ListDevCfgParam;
import com.cdz360.biz.model.trading.ess.vo.DevCfgVo;
import com.cdz360.biz.model.trading.ess.vo.EssDetailVo;
import com.cdz360.biz.model.trading.iot.vo.DevGtiCfgVo;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;


@Slf4j
@RestController
@Tag(name = "光储配置模板相关接口", description = "光储配置模板")
@RequestMapping(value = "/api/device")
public class DeviceRest {

    @Autowired
    private DeviceService deviceService;

    @Operation(summary = "新增光储配置模板")
    @PostMapping(value = "/addEss")
    public Mono<ObjectResponse<Long>> addEss(ServerHttpRequest request,
        @RequestBody AddEssCfgParam param) {
        log.info("光储模板新增参数,param={}", JsonUtils.toJsonString(param));
        param.setOpUid(AntRestUtils.getSysUid(request))
            .setCommId(AntRestUtils.getCommId(request))
            .setCommIdChain(AntRestUtils.getCommIdChain(request))
            .setOpName(AntRestUtils.getSysUserName(request));
        return deviceService.addEss(param);
    }

    @Operation(summary = "编辑光储配置模板")
    @PostMapping(value = "/editEss")
    public Mono<ObjectResponse<Long>> editEss(ServerHttpRequest request,
        @RequestBody AddEssCfgParam param) {
        log.info("光储模板编辑参数,param={}", JsonUtils.toJsonString(param));
        param.setOpUid(AntRestUtils.getSysUid(request))
            .setCommId(AntRestUtils.getCommId(request))
            .setCommIdChain(AntRestUtils.getCommIdChain(request))
            .setOpName(AntRestUtils.getSysUserName(request));
        return deviceService.editEss(param);
    }

    @Operation(summary = "新增逆变器模板")
    @PostMapping(value = "/addGtiCfg")
    public Mono<BaseResponse> addGtiCfg(ServerHttpRequest request,
        @RequestBody AddGtiCfgParam param) {
        log.info("光伏逆变器新增参数,param={}", JsonUtils.toJsonString(param));
        param.setOpUid(AntRestUtils.getSysUid(request))
            .setCommId(AntRestUtils.getCommId(request))
            .setCommIdChain(AntRestUtils.getCommIdChain(request))
            .setOpName(AntRestUtils.getSysUserName(request));
        return deviceService.addGtiCfg(param);
    }

    @Operation(summary = "模板列表")
    @PostMapping(value = "/getDevCfgList")
    public Mono<ListResponse<DevCfgVo>> getDevCfgList(ServerHttpRequest request,
        @RequestBody ListDevCfgParam param) {
        log.info("光储配置模板列表,param={}", JsonUtils.toJsonString(param));
        param.setCommIdChain(AntRestUtils.getCommIdChain(request));
        return deviceService.getDevCfgList(param);
    }

    @Operation(summary = "编辑逆变器模板")
    @PostMapping(value = "/editGtiCfg")
    public Mono<BaseResponse> editGtiCfg(ServerHttpRequest request,
        @RequestBody AddGtiCfgParam param) {
        log.info("光储模板编辑参数,param={}", JsonUtils.toJsonString(param));
        param.setOpUid(AntRestUtils.getSysUid(request))
            .setOpName(AntRestUtils.getSysUserName(request))
            .setCommIdChain(AntRestUtils.getCommIdChain(request));
        return deviceService.editGtiCfg(param);
    }

    @Operation(summary = "模板删除")
    @GetMapping(value = "/delDevCfg")
    public Mono<BaseResponse> delDevCfg(ServerHttpRequest request,
        @RequestParam(value = "id") Long id) {
        AddGtiCfgParam param = new AddGtiCfgParam();
        param.setOpUid(AntRestUtils.getSysUid(request))
            .setOpName(AntRestUtils.getSysUserName(request))
            .setCommIdChain(AntRestUtils.getCommIdChain(request))
            .setId(id);
        return deviceService.delDevCfg(param);
    }

    @Operation(summary = "光储ESS配置模板详情")
    @GetMapping(value = "/getDevCfgDetail")
    public Mono<ObjectResponse<EssDetailVo>> getDevCfgDetail(ServerHttpRequest request,
        @RequestParam(value = "id") Long id) {
        String commIdChain = AntRestUtils.getCommIdChain(request);
        IotAssert.isNotNull(commIdChain, "未登录状态");
        return deviceService.getDevCfgDetail(id, commIdChain);
    }

    @Operation(summary = "逆变器模板详情")
    @GetMapping(value = "/getGtiCfgDetail")
    public Mono<ObjectResponse<DevGtiCfgVo>> getGtiCfgDetail(ServerHttpRequest request,
        @RequestParam(value = "id") Long id) {
        String commIdChain = AntRestUtils.getCommIdChain(request);
        IotAssert.isNotNull(commIdChain, "未登录状态");
        return deviceService.getGtiCfgDetail(id, commIdChain);
    }
}
