package com.cdz360.biz.ant.service.corp;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.biz.ant.feign.AntUserFeignClient;
import com.cdz360.biz.model.cus.corp.vo.CorpOrgVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class CorpOrgService {
    @Autowired
    private AntUserFeignClient userFeignClient;

    public Mono<ListResponse<CorpOrgVO>> listCorpOrg(Long corpId) {
        if (null == corpId) {
            throw new DcArgumentException("企业ID不能为空");
        }

        return Mono.just(corpId)
                .map(id -> userFeignClient.getOrgTree(corpId, 1, 999));
    }
}
