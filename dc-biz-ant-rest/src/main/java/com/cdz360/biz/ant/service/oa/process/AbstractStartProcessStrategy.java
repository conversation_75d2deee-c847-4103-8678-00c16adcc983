package com.cdz360.biz.ant.service.oa.process;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.feign.reactor.DataCoreSettJobBillFeignClient;
import com.cdz360.biz.ant.feign.reactor.OaFeignClient;
import com.cdz360.biz.oa.param.OaStartProcessParam;
import com.cdz360.biz.oa.vo.ProcessInstanceVo;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public abstract class AbstractStartProcessStrategy implements StartProcessStrategy {

    @Autowired
    protected StartProcessStrategyFactory startProcessStrategyFactory;

    @Autowired
    protected OaFeignClient oaFeignClient;

    @Autowired
    protected DataCoreSettJobBillFeignClient settJobBillFeignClient;

    abstract public Mono<OaStartProcessParam> validate(OaStartProcessParam param);

    @Override
    public Mono<String> start(OaStartProcessParam param) {
        return this.validate(param)
            .flatMap(oaFeignClient::startOaProcess)
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .map(ProcessInstanceVo::getId);
    }

    @Override
    public Mono<String> resubmit(OaStartProcessParam param) {
        IotAssert.isNotBlank(param.getTaskId(), "任务ID不能为空");
        return this.validate(param)
            .flatMap(oaFeignClient::resubmitOaProcess)
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .map(ProcessInstanceVo::getId);
    }
}
