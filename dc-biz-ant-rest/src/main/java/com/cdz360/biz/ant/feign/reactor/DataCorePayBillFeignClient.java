package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.trading.order.vo.PayBillVo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE,
    fallbackFactory = DataCorePayBillFeignHystrix.class)
public interface DataCorePayBillFeignClient {

    @GetMapping(value = "/dataCore/paybill/tkView")
    Mono<ObjectResponse<PayBillVo>> tkView(
        @RequestParam("outRefundNo") String outRefundNo);

}
