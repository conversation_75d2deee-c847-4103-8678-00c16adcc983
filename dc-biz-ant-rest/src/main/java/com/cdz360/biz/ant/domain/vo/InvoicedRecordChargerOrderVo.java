package com.cdz360.biz.ant.domain.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.ZonedDateTime;

/**
 * A DTO for the InvoicedRecord entity.
 */
@Data
public class InvoicedRecordChargerOrderVo implements Serializable {

    private long invoicedId;
    private String orderId;//东正的id超出了前端的范围,所以这边处理一下
    private long orderPrice;
    private long actualPrice;
    private BigDecimal invoiceAmount;
    private long servicePrice;
    private long elecPrice;
    private Long creatorId;
    private String creatorName;
    private ZonedDateTime createdDate;
    private String stationId;

}
