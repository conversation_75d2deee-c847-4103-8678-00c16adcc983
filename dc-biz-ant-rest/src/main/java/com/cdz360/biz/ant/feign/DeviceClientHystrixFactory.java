package com.cdz360.biz.ant.feign;


import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.cus.user.dto.WhiteCardDto;
import com.chargerlinkcar.framework.common.domain.vo.TemplateInfoVo;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class DeviceClientHystrixFactory implements FallbackFactory<DeviceFeignClient> {

    @Override
    public DeviceFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}",
            DcConstants.KEY_FEIGN_DC_BIZ_DEVICE_REST, throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}",
            DcConstants.KEY_FEIGN_DC_BIZ_DEVICE_REST, throwable.getStackTrace());

        return new DeviceFeignClient() {
            @Override
            public ListResponse<TemplateInfoVo> getTemplateInfoList(Map map) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<TemplateInfoVo> getTemplateDetailById(Long templateId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<String> sendWhiteCard(WhiteCardDto whiteCardDto) {
                return RestUtils.serverBusy4ListResponse();
            }
        };
    }
}

