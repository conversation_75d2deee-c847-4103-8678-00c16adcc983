package com.cdz360.biz.ant.task;

import com.cdz360.biz.ant.config.LocalFileStorageProperties;
import java.io.IOException;
import java.nio.file.DirectoryStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.atomic.AtomicInteger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 诊断日志文件定时清理任务 每天凌晨2点15分执行，清理三个月之前的诊断日志文件
 * <p>
 * 存储结构: basePath/diagnostics/yyyy-MM-dd/evseNo/files 清理策略: 按日期目录过滤，过期就删除整个文件夹
 */
@Slf4j
@Component
public class DiagnosticsCleanupTask {

    private static final String DIAGNOSTICS_DIR = "diagnostics";
    private static final String DEFAULT_STORAGE_PATH = "/tmp/ocpp";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(
        "yyyy-MM-dd");
    private static final int CLEANUP_MONTHS = 1; // 清理1个月之前的文件

    @Autowired
    private LocalFileStorageProperties localFileStorageProperties;

    /**
     * 定时清理任务
     */
    @Scheduled(cron = "0 15 2 * * ?")
    public void cleanupOldDiagnosticsFiles() {
        log.info("开始执行诊断日志文件清理任务");

        try {
            // 确定清理路径 - 与DiagnosticsRest保持一致
            String basePath;
            if (localFileStorageProperties.isEnable()) {
                basePath = localFileStorageProperties.getOcppPath();
                log.info("使用配置的OCPP存储路径: {}", basePath);
            } else {
                basePath = DEFAULT_STORAGE_PATH;
                log.info("使用默认存储路径: {}", basePath);
            }

            // 获取诊断日志目录路径
            Path diagnosticsPath = Paths.get(basePath, DIAGNOSTICS_DIR);

            if (!Files.exists(diagnosticsPath)) {
                log.info("诊断日志目录不存在: {}", diagnosticsPath);
                return;
            }

            // 计算1个月前的日期
            LocalDate cutoffDate = LocalDate.now().minus(CLEANUP_MONTHS, ChronoUnit.MONTHS);
            log.info("清理 {} 之前的诊断日志文件，清理目录: {}", cutoffDate, diagnosticsPath);

            AtomicInteger deletedDirs = new AtomicInteger(0);

            // 遍历日期目录进行清理
            try (DirectoryStream<Path> stream = Files.newDirectoryStream(diagnosticsPath)) {
                for (Path dateDir : stream) {
                    if (!Files.isDirectory(dateDir)) {
                        continue;
                    }

                    String dirName = dateDir.getFileName().toString();

                    try {
                        // 尝试解析日期目录名
                        LocalDate dirDate = LocalDate.parse(dirName, DATE_FORMATTER);

                        if (dirDate.isBefore(cutoffDate)) {
                            // 整个日期目录都过期，直接删除
                            log.info("删除过期日期目录: {} (日期: {})", dateDir, dirDate);
                            deleteDirectoryRecursively(dateDir);
                            deletedDirs.incrementAndGet();
                        }

                    } catch (Exception e) {
                        // 如果目录名不是日期格式，跳过
                        log.error("跳过非日期格式目录: {}. error: {}", dirName, e.getMessage(), e);
                    }
                }
            } catch (IOException e) {
                log.error("遍历诊断日志目录时发生错误: {}. error: {}", diagnosticsPath,
                    e.getMessage(), e);
            }

            log.info("诊断日志文件清理任务完成，删除日期目录: {} 个", deletedDirs.get());

        } catch (Exception e) {
            log.error("执行诊断日志文件清理任务时发生错误. error: {}", e.getMessage(), e);
        }
    }

    /**
     * 递归删除整个目录
     *
     * @param directory 要删除的目录
     */
    private void deleteDirectoryRecursively(Path directory) {
        try {
            Files.walk(directory).sorted((a, b) -> b.compareTo(a)) // 先删除文件，再删除目录
                .forEach(path -> {
                    try {
                        Files.delete(path);
                        log.info("删除: {}", path);
                    } catch (IOException e) {
                        log.error("删除失败: {}. error: {}", path, e.getMessage(), e);
                    }
                });
        } catch (IOException e) {
            log.error("递归删除目录时发生错误: {}. error: {}", directory, e.getMessage(), e);
        }
    }

}