package com.cdz360.biz.ant.aop;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.chargerlinkcar.framework.common.exception.ChargerlinkException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.servlet.http.HttpServletRequest;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/**
 *  用于检查token
 * @since 2019/5/14 13:45
 * <AUTHOR>
 */

@Component
@Aspect
public class CheckTokenAround {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private Object aroundX(ProceedingJoinPoint point) throws InstantiationException, IllegalAccessException, IllegalArgumentException, InvocationTargetException, NoSuchMethodException, SecurityException {
        logger.info(">>");
        long start = System.currentTimeMillis();

        Method method = ((MethodSignature) point.getSignature()).getMethod();
        //Object[] args = point.getArgs();
        // HttpServletRequest request = (HttpServletRequest) args[0];

        Object objRt = null;
        try {
            CheckToken shelter = method.getAnnotation(CheckToken.class);
            boolean checkLogin = shelter.login();

            HttpServletRequest httpServletRequest = null;
            if(shelter.clazz() == HttpServletRequest.class) {
                //查找参数中的HttpServletRequest，
                for(Object arg : point.getArgs()) {
                    if(arg instanceof HttpServletRequest) {
                        httpServletRequest = (HttpServletRequest) arg;
                        checkLogin = true;
                        break;
                    }
                }
                //找不到，则没有办法进行token检查，检查将被跳过
            }

            if (checkLogin) {
                Object targetObject  = point.getTarget();
                Method m = targetObject.getClass().getMethod(shelter.check(), shelter.clazz());
                if((boolean) m.invoke(targetObject, httpServletRequest)) {
                    throw new DcServiceException("token为空");
                }
            }

            objRt = point.proceed(point.getArgs());
        } catch (IllegalArgumentException | ChargerlinkException e) {// Assert 失败
            logger.error(e.getMessage(), e);
            throw e;
        } catch (Throwable e) {
            logger.error(e.getMessage(), e);
            throw new DcServiceException(e.getMessage());

        }
        logger.info("<< {} 耗时：{}", point.getSignature().getDeclaringTypeName() + "." + point.getSignature().getName(),
                System.currentTimeMillis() - start);
        return objRt;
    }

    @Around(value = "@annotation(com.cdz360.biz.ant.aop.CheckToken)")
    public Object around(ProceedingJoinPoint point) {
        try {
            return aroundX(point);
        } catch (IllegalArgumentException e) {
            throw e;
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException | NoSuchMethodException | SecurityException e) {
            logger.error(e.getMessage(), e);
            throw new DcServiceException(e.getMessage());
        }
    }
}
