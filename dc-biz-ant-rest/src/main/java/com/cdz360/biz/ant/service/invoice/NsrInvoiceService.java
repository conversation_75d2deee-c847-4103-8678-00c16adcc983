package com.cdz360.biz.ant.service.invoice;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.utils.feign.invoice.InvoiceFeignClient;
import com.cdz360.biz.utils.service.OssService;
import com.chargerlinkcar.framework.common.domain.invoice.nsr.ImportNSRInvoiceResultVo;
import com.chargerlinkcar.framework.common.domain.invoice.nsr.NSRInvoiceFileResultVo;
import com.chargerlinkcar.framework.common.domain.invoice.nsr.NSRInvoiceResult;
import com.chargerlinkcar.framework.common.domain.invoice.nsr.NSRInvoiceResultVo;
import com.chargerlinkcar.framework.common.utils.ExcelReadUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.NsrZipFileUtils;
import java.io.File;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class NsrInvoiceService {

    @Autowired
    private InvoiceFeignClient invoiceFeignClient;

    @Autowired
    private OssService ossService;

    public Mono<ObjectResponse<ImportNSRInvoiceResultVo<NSRInvoiceFileResultVo>>> importNsrFileResult(
        FilePart file) {
        ExcelReadUtil.RowParse<NSRInvoiceFileResultVo> parse = list -> {
            NSRInvoiceFileResultVo r = new NSRInvoiceFileResultVo();
            r.setNsrNo(list.get(0))
                .setXsfName(list.get(1))
                .setGmfName(list.get(2))
                .setResult(list.get(3));
            return r;
        };

        NsrZipFileUtils<NSRInvoiceFileResultVo> zip =
            NsrZipFileUtils.<NSRInvoiceFileResultVo>builder(file);
        List<NSRInvoiceFileResultVo> dataList = zip.extractTotal(parse);

        return invoiceFeignClient.nsrDataValidation(dataList)
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .flatMap(result -> {
                List<NSRInvoiceFileResultVo> validDataList = dataList.stream().filter(item -> {
                    if (CollectionUtils.isNotEmpty(result.getErrList())) {
                        return result.getErrList().stream()
                            .filter(k -> k.getNsrNo().equals(item.getNsrNo())).findFirst()
                            .isEmpty();
                    }
                    return true;
                }).peek(item -> {
                    String path = zip.concatFilePath((dir, nameList) -> nameList.stream()
                        .filter(name -> {
                            String[] i = name.split("_");
                            return i.length > 2 && item.getNsrNo().equals(i[1]);
                        }).findFirst().map(s -> dir + File.separator + s).orElse(null));
                    if (StringUtils.isNotBlank(path)) {
                        // 上传文件
                        String url = ossService.nsrUploadFile(path);
                        item.setUrl(url);
                    }
                }).collect(Collectors.toList());

                if (CollectionUtils.isEmpty(validDataList)) {
                    log.info(">>> 有效数据为空 <<<");
                    return Mono.just(result);
                }

                // 发票文件存储处理
                return invoiceFeignClient.nsrInvoiceUpdate(validDataList)
                    .doOnNext(FeignResponseValidate::check)
                    .map(ObjectResponse::getData)
                    .map(res -> {
                        if (CollectionUtils.isNotEmpty(res.getErrList())) {
                            result.getErrList().addAll(res.getErrList());
                        }
                        return result;
                    });
            })
            .doOnNext(x -> zip.close())
            .map(RestUtils::buildObjectResponse); // 清空解压文件
    }

    public Mono<ObjectResponse<ImportNSRInvoiceResultVo<NSRInvoiceResultVo>>> importNsrResult(
        FilePart file) {
        ExcelReadUtil.RowParse<NSRInvoiceResult> parse = list -> new NSRInvoiceResult()
            .setNo(list.get(0))
            .setType(list.get(1))
            .setNsrNo(list.get(2))
            .setResult(list.get(6))
            .setFailCause(list.get(7));

        try {
            List<NSRInvoiceResult> dataList = ExcelReadUtil.<NSRInvoiceResult>builder(file)
                .activeRowNum(1)
                .loopReadLine(parse)
                .close();

            List<NSRInvoiceResult> successList = dataList.stream()
                .filter(x -> "成功".equals(x.getResult()) && x.getNo().startsWith("NSR_"))
                .collect(Collectors.toList());
            List<NSRInvoiceResultVo> faiList = dataList.stream()
                .filter(x -> !("成功".equals(x.getResult()) && x.getNo().startsWith("NSR_")))
                .map(x -> {
                    NSRInvoiceResultVo tmp = new NSRInvoiceResultVo()
                        .setErrMsg("开具失败/流水号不符合规则[" + x.getFailCause() + "]");
                    tmp.setType(x.getType())
                        .setNo(x.getNo())
                        .setNsrNo(x.getNsrNo())
                        .setResult(x.getResult());
                    return tmp;
                }).collect(Collectors.toList());
            return invoiceFeignClient.importNsrResult(successList)
                .doOnNext(FeignResponseValidate::check)
                .map(ObjectResponse::getData)
                .map(x -> {
                    x.setImportTotal(dataList.size());

                    if (CollectionUtils.isNotEmpty(faiList)) {
                        if (null == x.getErrList()) {
                            x.setErrList(faiList);
                        } else {
                            x.getErrList().addAll(faiList);
                        }
                    }
                    return x;
                })
                .map(RestUtils::buildObjectResponse);
        } catch (Exception e) {
            // nothing to do
            log.error(">>>> 解析数据异常: {}", e.getMessage(), e);
            throw new DcServiceException("解析数据异常");
        }
    }
}
