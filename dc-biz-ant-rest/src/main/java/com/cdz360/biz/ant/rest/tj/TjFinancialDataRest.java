package com.cdz360.biz.ant.rest.tj;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.ant.service.tj.TjCashOutflowService;
import com.cdz360.biz.ant.service.tj.TjDailyChargingDurationService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.ant.utils.FileUtil;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.common.param.BaseListParam;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.sim.param.ListSimParam;
import com.cdz360.biz.model.sim.vo.SimImportVo;
import com.cdz360.biz.model.tj.kc.param.ListTjDailyChargingDurationParam;
import com.cdz360.biz.model.tj.kc.param.ListTjMaterialCostParam;
import com.cdz360.biz.model.tj.kc.po.TjCashOutflowPo;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationCoefficientPo;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo;
import com.cdz360.biz.model.tj.kc.po.TjDepreciationPo;
import com.cdz360.biz.model.tj.kc.vo.ImportTjDailyChargingDurationVo;
import com.cdz360.biz.model.tj.kc.vo.TjDailyChargingDurationImportVo;
import com.cdz360.biz.model.tj.kc.vo.TjDailyChargingDurationVo;
import com.chargerlinkcar.framework.common.domain.invoice.nsr.ImportNSRInvoiceResultVo;
import com.chargerlinkcar.framework.common.domain.invoice.nsr.NSRInvoiceResultVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "财务数据相关操作接口", description = "财务数据相关操作接口")
@Slf4j
@RestController
@RequestMapping("/api/tj/financialData")
public class TjFinancialDataRest {

    @Autowired
    private TjDailyChargingDurationService tjDailyChargingDurationService;

    @Autowired
    private TjCashOutflowService tjCashOutflowService;

    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;

    @Operation(summary = "获取日充电时长")
    @PostMapping(value = "/findTjDailyChargingDuration")
    public Mono<ListResponse<TjDailyChargingDurationPo>> findTjDailyChargingDuration(
        ServerHttpRequest request,
        @RequestBody ListTjDailyChargingDurationParam param) {
        log.info("{} 获取日充电时长: param = {}", LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return tjDailyChargingDurationService.findTjDailyChargingDuration(param);
    }

    @Operation(summary = "获取日充电时长")
    @GetMapping(value = "/getTjDailyChargingDurationById")
    public Mono<ObjectResponse<TjDailyChargingDurationPo>> getTjDailyChargingDurationById(
        ServerHttpRequest request,
        @ApiParam("日充电时长唯一ID") @RequestParam("id") Long id) {
        log.info("{} 获取日充电时长: {}", LoggerHelper2.formatEnterLog(request, false), id);
        return tjDailyChargingDurationService.getTjDailyChargingDurationById(id);
    }

    @Operation(summary = "新建或编辑日充电时长")
    @PostMapping(value = "/saveTjDailyChargingDuration")
    public Mono<ObjectResponse<TjDailyChargingDurationPo>> saveTjDailyChargingDuration(
        ServerHttpRequest request,
        @RequestBody TjDailyChargingDurationPo tjDailyChargingDurationPo) {
        log.info("{} 保存日充电时长: param = {}", LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(tjDailyChargingDurationPo));
        return tjDailyChargingDurationService.saveTjDailyChargingDuration(tjDailyChargingDurationPo);
    }

    @Operation(summary = "删除日充电时长")
    @GetMapping(value = "/disableTjDailyChargingDuration")
    public Mono<ObjectResponse<TjDailyChargingDurationPo>> disableTjDailyChargingDuration(
        ServerHttpRequest request,
        @ApiParam("日充电时长唯一ID") @RequestParam("id") Long id) {
        log.info("{} 删除日充电时长: {}", LoggerHelper2.formatEnterLog(request, false), id);
        return tjDailyChargingDurationService.disableTjDailyChargingDuration(id);
    }

    @Operation(summary = "获取日充电时长乐悲观指数")
    @GetMapping(value = "/findTjDailyChargingDurationCoefficient")
    public Mono<ObjectResponse<TjDailyChargingDurationCoefficientPo>> findTjDailyChargingDurationCoefficient() {
        return tjDailyChargingDurationService.findTjDailyChargingDurationCoefficient();
    }

    @Operation(summary = "修改乐悲观指数")
    @PostMapping(value = "/saveTjDailyChargingDurationCoefficient")
    public Mono<ObjectResponse<TjDailyChargingDurationCoefficientPo>> saveTjDailyChargingDurationCoefficient(
        ServerHttpRequest request,
        @RequestBody TjDailyChargingDurationCoefficientPo tjDailyChargingDurationCoefficientPo) {
        log.info("{} 保存修改乐悲观指数: param = {}", LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(tjDailyChargingDurationCoefficientPo));
        return tjDailyChargingDurationService.saveTjDailyChargingDurationCoefficient(tjDailyChargingDurationCoefficientPo);
    }

    @Operation(summary = "日充电时长导出EXCEL")
    @PostMapping(value = "/exportTjDailyChargingDurationExcel")
    public Mono<ObjectResponse<ExcelPosition>> exportSimExcel(
        ServerHttpRequest request, @RequestBody ListTjDailyChargingDurationParam param) {
        log.info("{} 日充电时长导出EXCEL: param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("日充电时长正常估算")
            .setFunctionMap(DownloadFunctionType.TJ_DAILY_CHARGING_DURATION)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
    }

    @Operation(summary = "导入日充电时长EXCEL")
    @PostMapping("/importTjDailyChargingDurationExcel")
    public Mono<ObjectResponse<ImportTjDailyChargingDurationVo<TjDailyChargingDurationVo>>> importTjDailyChargingDurationExcel(
        ServerHttpRequest request,
        @RequestHeader("Content-Length") long size,
        @RequestPart FilePart file) {
        log.info("导入日充电时长EXCEL: {}B / {}", size, file.filename());
        return tjDailyChargingDurationService.importTjDailyChargingDurationExcel(file);
    }

    @Operation(summary = "获取设备折旧配置")
    @GetMapping(value = "/findTjDepreciation")
    public Mono<ObjectResponse<TjDepreciationPo>> findTjDepreciation() {
        return tjCashOutflowService.findTjDepreciation();
    }

    @Operation(summary = "修改设备折旧配置")
    @PostMapping(value = "/saveTjDepreciation")
    public Mono<ObjectResponse<TjDepreciationPo>> saveTjDepreciation(
        ServerHttpRequest request,
        @RequestBody TjDepreciationPo tjDepreciationPo) {
        log.info("{} 修改设备折旧配置: param = {}", LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(tjDepreciationPo));
        return tjCashOutflowService.saveTjDepreciation(tjDepreciationPo);
    }

    @Operation(summary = "获取通用现金流出配置")
    @PostMapping(value = "/findTjCashOutflow")
    public Mono<ListResponse<TjCashOutflowPo>> findTjCashOutflow(
        ServerHttpRequest request,
        @RequestParam("type") Integer type) {
        log.info("{} 获取通用现金流出配置: type = {}", LoggerHelper2.formatEnterLog(request, false), type);
        return tjCashOutflowService.findTjCashOutflow(type);
    }

    @Operation(summary = "获取通用现金流出配置")
    @GetMapping(value = "/getTjCashOutflowById")
    public Mono<ObjectResponse<TjCashOutflowPo>> getTjCashOutflowById(
        ServerHttpRequest request,
        @ApiParam("通用现金流出配置唯一ID") @RequestParam("id") Long id) {
        log.info("{} 获取通用现金流出配置: id = {}", LoggerHelper2.formatEnterLog(request, false), id);
        return tjCashOutflowService.getTjCashOutflowById(id);
    }

    @Operation(summary = "新建或编辑通用现金流出配置")
    @PostMapping(value = "/saveTjCashOutflow")
    public Mono<ObjectResponse<TjCashOutflowPo>> saveTjCashOutflow(
        ServerHttpRequest request,
        @RequestBody TjCashOutflowPo tjCashOutflowPo) {
        log.info("{} 新建或编辑通用现金流出配置: param = {}", LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(tjCashOutflowPo));
        return tjCashOutflowService.saveTjCashOutflow(tjCashOutflowPo);
    }

    @Operation(summary = "删除通用现金流出配置")
    @GetMapping(value = "/disableTjCashOutflow")
    public Mono<ObjectResponse<TjCashOutflowPo>> disableTjCashOutflow(
        ServerHttpRequest request,
        @ApiParam("通用现金流出配置唯一ID") @RequestParam("id") Long id) {
        log.info("{} 删除通用现金流出配置: {}", LoggerHelper2.formatEnterLog(request, false), id);
        return tjCashOutflowService.disableTjCashOutflow(id);
    }
}
