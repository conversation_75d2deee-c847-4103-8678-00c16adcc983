package com.cdz360.biz.ant.constant;

/**
 * BindType
 *  紧急充电卡客户类型
 * @since 2019/7/10
 * <AUTHOR>
 */
public enum BindType {
    PERSONAL_CUSTOMER("1", "个人客户") ,
    GROUP_CUSTOMER("2", "集团客户") ,
    COMMERCIAL_CUSTOMER("3", "商户会员") ,
    ;
    private final String code;
    private final String name;

    BindType(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static BindType valueOfCode(String code) {
        for (BindType type : BindType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName(){
        return name;
    }
}
