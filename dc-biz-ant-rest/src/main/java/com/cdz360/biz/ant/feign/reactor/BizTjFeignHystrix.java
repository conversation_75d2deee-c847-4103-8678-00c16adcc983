package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.common.param.BaseListParam;
import com.cdz360.biz.model.tj.kc.dto.TjCompetitorSiteDto;
import com.cdz360.biz.model.tj.kc.param.ListSiteWithinTjAreaParam;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaAnalysisParam;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaAnalysisPointParam;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaParam;
import com.cdz360.biz.model.tj.kc.param.ListTjCompetitorParam;
import com.cdz360.biz.model.tj.kc.param.ListTjCompetitorSiteParam;
import com.cdz360.biz.model.tj.kc.param.ListTjDailyChargingDurationParam;
import com.cdz360.biz.model.tj.kc.param.ListTjMaterialCostParam;
import com.cdz360.biz.model.tj.kc.param.ListTjSurveyParam;
import com.cdz360.biz.model.tj.kc.param.RepeatSurveyParam;
import com.cdz360.biz.model.tj.kc.param.TjDailyChargingDurationImportParam;
import com.cdz360.biz.model.tj.kc.param.TjSurveyBiParam;
import com.cdz360.biz.model.tj.kc.param.UpdateAnalysisPointStatusParam;
import com.cdz360.biz.model.tj.kc.param.UpdateTjCompetitorParam;
import com.cdz360.biz.model.tj.kc.po.TjCashOutflowPo;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationCoefficientPo;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo;
import com.cdz360.biz.model.tj.kc.po.TjDepreciationPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationExpensesPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationIncomePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesOtherPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesPo;
import com.cdz360.biz.model.tj.kc.vo.ImportTjDailyChargingDurationVo;
import com.cdz360.biz.model.tj.kc.vo.SiteWithinTjVo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisPointVo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisVo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisWithPointVo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaVo;
import com.cdz360.biz.model.tj.kc.vo.TjCompetitorSiteVo;
import com.cdz360.biz.model.tj.kc.vo.TjCompetitorVo;
import com.cdz360.biz.model.tj.kc.vo.TjDailyChargingDurationImportItem;
import com.cdz360.biz.model.tj.kc.vo.TjDailyChargingDurationImportVo;
import com.cdz360.biz.model.tj.kc.vo.TjDailyChargingDurationVo;
import com.cdz360.biz.model.tj.kc.vo.TjMaterialCostVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyBiVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyCalculationInfoVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyCalculationResultVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyChargeAreaVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class BizTjFeignHystrix
    implements FallbackFactory<BizTjFeignClient> {

    @Override
    public BizTjFeignClient apply(Throwable throwable) {
        log.error("{}", throwable.getMessage(), throwable);
        return new BizTjFeignClient() {
            @Override
            public Mono<ListResponse<TjAreaVo>> findArea(ListTjAreaParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = findArea (获取投建区域列表), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<TjAreaVo>> findUserArea(ListTjAreaParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = findUserArea (获取用户的投建区域), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<TjAreaVo>> getTjAreaByAid(Long aid) {
                log.error(
                    "【服务熔断】: Service = {}, api = getTjAreaByAid (获取投建区域), aid = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, aid);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<TjAreaVo>> saveTjArea(TjAreaVo area) {
                log.error(
                    "【服务熔断】: Service = {}, api = saveTjArea (新建或编辑投建区域), area = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, JsonUtils.toJsonString(area));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<TjAreaVo>> disableTjArea(Long aid) {
                log.error(
                    "【服务熔断】: Service = {}, api = disableTjArea (删除投建区域), aid = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, aid);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<TjSurveyPo>> findTjSurvey(ListTjSurveyParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = findTjSurvey (获取勘察场站列表), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<TjSurveyBiVo>> tjSurveyBi(TjSurveyBiParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = tjSurveyBi (获取投建场站勘察汇总信息), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<TjSurveyVo>> repeatSurvey(RepeatSurveyParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = repeatSurvey (重复勘察场站判断), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<TjSurveyVo>> saveTjSurvey(TjSurveyVo survey) {
                log.error(
                    "【服务熔断】: Service = {}, api = saveTjSurvey (新增或编辑场站勘察记录), survey = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, JsonUtils.toJsonString(survey));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<TjCompetitorSiteVo>> findTjCompetitorSite(
                ListTjCompetitorSiteParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = findTjCompetitorSite (获取投建竞争者场站列表), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<TjCompetitorSiteVo>> saveTjCompetitorSiteAttachInfo(
                TjCompetitorSiteDto dto) {
                log.error(
                    "【服务熔断】: Service = {}, api = saveTjCompetitorSiteAttachInfo (更新竞争场站附加信息), dto = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, JsonUtils.toJsonString(dto));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<TjCompetitorVo>> findTjCompetitor(
                ListTjCompetitorParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = findTjCompetitor (获取投建竞争者), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<TjCompetitorVo>> saveCompetitor(
                UpdateTjCompetitorParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = saveCompetitor (新增或编辑竞争者)",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> syncCompetitorSite(
                String provinceCode, String cityCode) {
                log.error(
                    "【服务熔断】: Service = {}, api = syncCompetitorSite (同步竞争者场站信息), {}, {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, provinceCode, cityCode);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<TjCompetitorVo>> disableCompetitor(Long competitorId) {
                log.error(
                    "【服务熔断】: Service = {}, api = disableCompetitor (删除竞争者), competitorId = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, competitorId);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<TjAreaAnalysisVo>> findTjAnalysis(
                ListTjAreaAnalysisParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = findTjAnalysis (获取投建分析列表), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<TjAreaAnalysisWithPointVo>> saveAnalysis(
                TjAreaAnalysisWithPointVo param) {
                log.error(
                    "【服务熔断】: Service = {}, api = saveAnalysis (新增或编辑投建分析)",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<TjAreaAnalysisVo>> disableAnalysis(Long analysisId) {
                log.error(
                    "【服务熔断】: Service = {}, api = disableAnalysis (删除投建分析), analysisId = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, analysisId);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<TjAreaAnalysisWithPointVo>> getTjAnalysisById(
                Long analysisId) {
                log.error(
                    "【服务熔断】: Service = {}, api = getTjAnalysisById (通过ID获取投建分析)",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<TjAreaAnalysisPointVo>> findTjAnalysisPoint(
                ListTjAreaAnalysisPointParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = findTjAnalysisPoint (获取投建分析划分点), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<Long>> updateTjAnalysisPointStatus(
                UpdateAnalysisPointStatusParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = updateTjAnalysisPointStatus (更新指定投建分析下的划分点状态)",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<SiteWithinTjVo>> findSiteWithinTjArea(
                ListSiteWithinTjAreaParam param) {
                log.error(
                    "【服务熔断】 获取投建区域内的场站列表. Service = {}, api = findSiteWithinTjArea. param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, param);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<TjDailyChargingDurationPo>> findTjDailyChargingDuration(
                ListTjDailyChargingDurationParam param) {
                log.error(
                    "【服务熔断】 获取日充电时长. Service = {}, api = findTjDailyChargingDuration. param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, param);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<TjDailyChargingDurationPo>> getTjDailyChargingDurationById(
                Long id) {
                log.error(
                    "【服务熔断】 获取日充电时长. Service = {}, api = getTjDailyChargingDurationById. id = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, id);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<TjDailyChargingDurationPo>> saveTjDailyChargingDuration(
                TjDailyChargingDurationPo tjDailyChargingDurationPo) {
                log.error(
                    "【服务熔断】 保存日充电时长. Service = {}, api = saveTjDailyChargingDuration. param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, tjDailyChargingDurationPo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<TjDailyChargingDurationPo>> disableTjDailyChargingDuration(
                Long id) {
                log.error(
                    "【服务熔断】 删除日充电时长. Service = {}, api = disableTjDailyChargingDuration. id = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, id);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<ImportTjDailyChargingDurationVo<TjDailyChargingDurationVo>>> importTjDailyChargingDurationExcel(
                List<TjDailyChargingDurationImportItem> dataList) {
                log.error(
                    "【服务熔断】 导入日充电时长. Service = {}, api = disableTjDailyChargingDuration. param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, dataList);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<TjDailyChargingDurationCoefficientPo>> findTjDailyChargingDurationCoefficient() {
                log.error(
                    "【服务熔断】 获取日充电时长乐悲观指数. Service = {}, api = findTjDailyChargingDurationCoefficient.",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<TjDailyChargingDurationCoefficientPo>> saveTjDailyChargingDurationCoefficient(
                TjDailyChargingDurationCoefficientPo tjDailyChargingDurationCoefficientPo) {
                log.error(
                    "【服务熔断】 保存修改乐悲观指数. Service = {}, api = saveTjDailyChargingDurationCoefficient. param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, tjDailyChargingDurationCoefficientPo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<TjDepreciationPo>> findTjDepreciation() {
                log.error(
                    "【服务熔断】 获取设备折旧配置. Service = {}, api = findTjDepreciation.",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<TjDepreciationPo>> saveTjDepreciation(
                TjDepreciationPo tjDepreciationPo) {
                log.error(
                    "【服务熔断】 修改设备折旧配置. Service = {}, api = saveTjDepreciation. param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, tjDepreciationPo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<TjCashOutflowPo>> findTjCashOutflow(
                Integer type) {
                log.error(
                    "【服务熔断】 获取通用现金流出配置. Service = {}, api = findTjCashOutflow. type = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, type);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<TjCashOutflowPo>> getTjCashOutflowById(
                Long id) {
                log.error(
                    "【服务熔断】 获取通用现金流出配置. Service = {}, api = getTjCashOutflowById. id = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, id);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<TjCashOutflowPo>> saveTjCashOutflow(
                TjCashOutflowPo tjCashOutflowPo) {
                log.error(
                    "【服务熔断】 新建或编辑通用现金流出配置. Service = {}, api = saveTjCashOutflow. param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, tjCashOutflowPo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<TjCashOutflowPo>> disableTjCashOutflow(
                Long id) {
                log.error(
                    "【服务熔断】 删除通用现金流出配置. Service = {}, api = disableTjCashOutflow. id = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, id);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<TjMaterialCostVo>> findTjMaterialCost(
                ListTjMaterialCostParam param) {
                log.error(
                    "【服务熔断】 获取获取物料成本. Service = {}, api = findTjMaterialCost. param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, param);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<TjMaterialCostVo>> getTjMaterialCostById(
                Long id) {
                log.error(
                    "【服务熔断】 获取物料成本. Service = {}, api = getTjMaterialCostById. id = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, id);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<TjMaterialCostVo>> saveTjMaterialCost(
                TjMaterialCostVo tjMaterialCostVo) {
                log.error(
                    "【服务熔断】 删除通用现金流出配置. Service = {}, api = saveTjMaterialCost. param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, tjMaterialCostVo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<TjMaterialCostVo>> disableTjMaterialCost(
                Long id) {
                log.error(
                    "【服务熔断】 删除物料成本. Service = {}, api = disableTjMaterialCost. id = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, id);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<TjSurveyChargeAreaVo>> findTjSurveyChargeAreaBySurveyNo(
                String surveyNo) {
                log.error(
                    "【服务熔断】 获取勘察站充电区域查询. Service = {}, api = findTjSurveyChargeAreaBySurveyNo. surveyNo = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, surveyNo);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<TjSurveyChargeAreaVo>> saveTjSurveyChargeArea(
                TjSurveyChargeAreaVo tjSurveyChargeAreaVo) {
                log.error(
                    "【服务熔断】 新增或编辑勘察站充电区域. Service = {}, api = saveTjSurveyChargeArea. param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, tjSurveyChargeAreaVo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<TjSurveySupportingFacilitiesPo>> findTjSurveySupportingFacilitiesBySurveyNo(
                String surveyNo) {
                log.error(
                    "【服务熔断】 获取勘察站标准设施查询. Service = {}, api = findTjSurveySupportingFacilitiesBySurveyNo. surveyNo = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, surveyNo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<TjSurveySupportingFacilitiesPo>> saveTjSurveySupportingFacilities(
                TjSurveySupportingFacilitiesPo tjSurveySupportingFacilitiesPo) {
                log.error(
                    "【服务熔断】 新增或编辑勘察站标准设施. Service = {}, api = saveTjSurveySupportingFacilities. param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, tjSurveySupportingFacilitiesPo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<TjSurveySupportingFacilitiesOtherPo>> findTjSurveySupportingFacilitiesOtherBySurveyNo(
                String surveyNo) {
                log.error(
                    "【服务熔断】 获取勘察站其他设施查询. Service = {}, api = findTjSurveySupportingFacilitiesOtherBySurveyNo. surveyNo = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, surveyNo);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<TjSurveySupportingFacilitiesOtherPo>> saveTjSurveySupportingFacilitiesOther(
                TjSurveySupportingFacilitiesOtherPo tjSurveySupportingFacilitiesOtherPo) {
                log.error(
                    "【服务熔断】 新增或编辑勘察站其他设施. Service = {}, api = saveTjSurveySupportingFacilitiesOther. param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, tjSurveySupportingFacilitiesOtherPo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<TjSurveyHighVoltagePo>> findTjSurveyHighVoltageBySurveyNo(
                String surveyNo) {
                log.error(
                    "【服务熔断】 获取勘察站高压查询. Service = {}, api = findTjSurveyHighVoltageBySurveyNo. surveyNo = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, surveyNo);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<TjSurveyHighVoltagePo>> saveTjSurveyHighVoltage(
                TjSurveyHighVoltagePo tjSurveyHighVoltagePo) {
                log.error(
                    "【服务熔断】 新增或编辑勘察站高压信息. Service = {}, api = saveTjSurveyHighVoltage. param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, tjSurveyHighVoltagePo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<TjSurveyOperationIncomePo>> findTjSurveyOperationIncomeBySurveyNo(
                String surveyNo) {
                log.error(
                    "【服务熔断】 获取勘察场站运营收入. Service = {}, api = findTjSurveyOperationIncomeBySurveyNo. surveyNo = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, surveyNo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<TjSurveyOperationIncomePo>> saveTjSurveyOperationIncome(
                TjSurveyOperationIncomePo tjSurveyOperationIncomePo) {
                log.error(
                    "【服务熔断】 新增或编辑场站勘察站运营收入. Service = {}, api = saveTjSurveyOperationIncome. param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, tjSurveyOperationIncomePo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<TjSurveyOperationExpensesPo>> findTjSurveyOperationExpensesBySurveyNo(
                String surveyNo) {
                log.error(
                    "【服务熔断】 获取勘察场站运营支出. Service = {}, api = findTjSurveyOperationExpensesBySurveyNo. surveyNo = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, surveyNo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<TjSurveyOperationExpensesPo>> saveTjSurveyOperationExpenses(
                TjSurveyOperationExpensesPo tjSurveyOperationExpensesPo) {
                log.error(
                    "【服务熔断】 新增或编辑场站勘察站运营支出. Service = {}, api = saveTjSurveyOperationExpenses. param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, tjSurveyOperationExpensesPo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<TjSurveyCalculationInfoVo>> saveTjSurveyCalculationInfo(
                TjSurveyCalculationInfoVo tjSurveyCalculationInfoVo) {
                log.error(
                    "【服务熔断】 新增或编辑场站勘察站智能测算录入信息. Service = {}, api = saveTjSurveyCalculationInfo. param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, tjSurveyCalculationInfoVo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<TjSurveyCalculationResultVo>> calculationResult(
                String surveyNo) {
                log.error(
                    "【服务熔断】 智能测算. Service = {}, api = calculationResult. param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, surveyNo);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }
        };
    }
}
