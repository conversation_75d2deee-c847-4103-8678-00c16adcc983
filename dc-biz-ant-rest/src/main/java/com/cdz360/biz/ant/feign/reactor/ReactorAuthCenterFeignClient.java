package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.domain.BlocUser;
import com.cdz360.biz.auth.user.param.ListSysUserParam;
import com.cdz360.biz.auth.user.po.SysUserPo;
import com.cdz360.biz.model.cus.corp.dto.UserIdDto;
import com.cdz360.biz.model.cus.corp.po.CorpOrgPo;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.message.po.MessagePo;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.sys.vo.SystemVo;
import com.cdz360.biz.oa.param.account.OaModifyGroupParam;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_AUTH,
    fallbackFactory = ReactorAuthCenterFeignHystrix.class)
public interface ReactorAuthCenterFeignClient {

    // 获取系统用户列表
    @PostMapping("/api/sys/user/getSysUserByIdList")
    Mono<ListResponse<SysUserVo>> getSysUserByIdList(@RequestBody List<Long> ids);

    // 添加站内信
    @PostMapping("/api/msg/addMessage")
    Mono<ObjectResponse<Integer>> addMessage(
        @RequestParam(value = "token", required = false) String token,
        @RequestBody MessagePo message);

    @PostMapping("/api/sys/user/getSysUserById")
    Mono<ObjectResponse<SysUserPo>> getSysUserById(@RequestParam(value = "id") Long id);


    // 通过UID获取企业信息
    @GetMapping(value = "/api/corp/getCorpByUid")
    Mono<ObjectResponse<CorpPo>> getCorpByUid(
        @RequestHeader("token") String token,
        @RequestParam(value = "corpUid") Long corpUid);

    // 更新集团信息
    @PostMapping(value = "/api/corp/updateBlocUser")
    Mono<ObjectResponse<CorpPo>> updateBlocUser(@RequestBody BlocUser blocUser);

    // 新增集团
    @PostMapping("/api/corp/addCorp")
    Mono<ObjectResponse<Long>> addCorp(@RequestBody CorpPo corp);

    @GetMapping(value = "/api/corp/getOrgInfoByLevel")
    Mono<ObjectResponse<CorpOrgPo>> getOrgInfoByLevel(@RequestParam("corpId") Long corpId,
        @RequestParam("orgLevel") Integer orgLevel);

    @PostMapping("/data/users/allocateGroup")
    Mono<ObjectResponse<Integer>> allocateSysUserGroup(@RequestBody OaModifyGroupParam param);

    @PostMapping("/data/users/groupIdByKeyWord")
    Mono<ListResponse<String>> groupIdByKeyWord(@RequestParam("sk") String sk);

    // 和指定用户团队隔离标签一致的用户ID
    @GetMapping("/data/users/teamCatalogUserIdList")
    Mono<ListResponse<Long>> teamCatalogUserIdList(
        @RequestParam("uid") Long uid,
        @RequestParam(value = "uname", required = false) String uname);

    // 多条件获取用户ID列表
    @GetMapping("/data/users/searchUserIdList")
    Mono<ObjectResponse<UserIdDto>> searchUserIdList(
        @RequestParam("teamCatalogUid") Long teamCatalogUid,
        @RequestParam(value = "uname", required = false) String uname);

    // 两个用于的团队标签是否一致
    @GetMapping("/data/users/sameTeamCatalog")
    Mono<ObjectResponse<Boolean>> sameTeamCatalog(
        @RequestParam("uid1") Long uid1,
        @RequestParam("uid2") Long uid2);

    // 获取当前用户相同团队标签的用户列表
    @PostMapping("/data/users/sameCorpWxAppNameSysUser")
    Mono<ListResponse<SysUserVo>> sameCorpWxAppNameSysUser(
        @RequestHeader("token") String token, @RequestBody ListSysUserParam param);

    // 查询系统用户列表
    @PostMapping("/data/users/findSysUserList")
    Mono<ListResponse<SysUserVo>> findSysUserList(
        @RequestHeader("token") String token, @RequestBody ListSysUserParam param);

    // 获取系统菜单信息
    @GetMapping("/data/menus/group-by-sys")
    Mono<ListResponse<SystemVo>> sysMenuBySysId(
        @RequestHeader("token") String token,
        @RequestParam(value = "sysId", required = false) Long sysId,
        @RequestParam(value = "name", required = false) String name);
}
