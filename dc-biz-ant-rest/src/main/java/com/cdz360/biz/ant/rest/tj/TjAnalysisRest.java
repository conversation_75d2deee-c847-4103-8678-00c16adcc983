package com.cdz360.biz.ant.rest.tj;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.service.tj.TjAnalysisService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaAnalysisParam;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisVo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisWithPointVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "投建竞争对手相关操作接口", description = "投建竞争对手相关操作接口")
@Slf4j
@RestController
@RequestMapping("/api/tj/area/analysis")
public class TjAnalysisRest {

    @Autowired
    private TjAnalysisService tjAnalysisService;

    @Operation(summary = "获取投建分析")
    @PostMapping("/findTjAnalysis")
    public Mono<ListResponse<TjAreaAnalysisVo>> findTjAnalysis(
        ServerHttpRequest request,
        @RequestBody ListTjAreaAnalysisParam param) {
        log.info("{} 获取投建分析: param = {}", LoggerHelper2.formatEnterLog(request, false),
            param);
        param.setGidList(AntRestUtils.getSysUserGids(request));
        return tjAnalysisService.findTjAnalysis(param);
    }

    @Operation(summary = "通过ID获取投建分析")
    @GetMapping(value = "/getTjAnalysisById")
    public Mono<ObjectResponse<TjAreaAnalysisWithPointVo>> getTjAnalysisById(
        ServerHttpRequest request,
        @ApiParam("投建分析唯一ID") @RequestParam("analysisId") Long analysisId) {
        log.info("通过ID获取投建分析 {} ", LoggerHelper2.formatEnterLog(request));
        return tjAnalysisService.getTjAnalysisById(analysisId);
    }

    @Operation(summary = "新增投建分析")
    @PostMapping("/addAnalysis")
    public Mono<ObjectResponse<TjAreaAnalysisWithPointVo>> addAnalysis(
        ServerHttpRequest request,
        @RequestBody TjAreaAnalysisWithPointVo param) {
        log.info("新增投建分析 {} param = {}", LoggerHelper2.formatEnterLog(request, false),
            param);
        return tjAnalysisService.addAnalysis(param);
    }

    @Operation(summary = "删除投建分析")
    @GetMapping(value = "/disableAnalysis")
    public Mono<ObjectResponse<TjAreaAnalysisVo>> disableAnalysis(
        ServerHttpRequest request,
        @ApiParam("投建分析唯一ID") @RequestParam("analysisId") Long analysisId) {
        log.info("删除投建分析 {} ", LoggerHelper2.formatEnterLog(request));
        return tjAnalysisService.disableTjAreaAnalysis(analysisId);
    }
}
