package com.cdz360.biz.ant.domain.request;
//
//import com.chargerlinkcar.framework.common.domain.BasePage;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
///**
// * <AUTHOR>
// * @since Created on 9:13 2019/5/6.
// */
//@Data
//@EqualsAndHashCode(callSuper = true)
//public class PaymentRequest extends BasePage {
//
//    /**
//     * 支付ID
//     */
//    private String payId;
//
//    /**
//     * 订单编号
//     */
//    private String orderId;
//
//    /**
//     * 支付状态,1:待支付; 2:已支付;3: 支付失败
//     */
//    private Integer payStatus;
//
//    /**
//     * 客户编号
//     */
//    private String customerId;
//
//    /**
//     * 开始时间
//     */
//    private String beginTime;
//
//    /**
//     * 结束时间
//     */
//    private String endTime;
//
//    /**
//     * 用户名
//     */
//    private String userName;
//
//
//}
