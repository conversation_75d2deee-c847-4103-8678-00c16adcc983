package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.trading.profit.sett.param.ListSettJobBillParam;
import com.cdz360.biz.model.trading.profit.sett.vo.SettJobBillDetailVo;
import com.cdz360.biz.model.trading.profit.sett.vo.SettJobBillVo;
import com.cdz360.biz.oa.dto.OaSettJobBillDto;
import java.util.List;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE,
    fallbackFactory = DataCoreSettJobBillFeignHystrix.class)
public interface DataCoreSettJobBillFeignClient {

    // 获取结算单
    @DeleteMapping(value = "/dataCore/settJobBill/getBill")
    Mono<ObjectResponse<SettJobBillDetailVo>> getSettJobBill(
        @RequestParam("billNo") String billNo);

    // 获取结算单列表
    @PostMapping(value = "/dataCore/settJobBill/findAll")
    Mono<ListResponse<SettJobBillVo>> findSettJobBill(
        @RequestBody ListSettJobBillParam param);

    // 检查结算单列表变更
    @PostMapping(value = "/dataCore/settJobBill/checkSettJobBill")
    Mono<ListResponse<OaSettJobBillDto>> checkSettJobBill(
        @RequestBody List<OaSettJobBillDto> param);

    // 结算单数据更新
    @GetMapping(value = "/dataCore/settJobBill/recalculateBill")
    Mono<ObjectResponse<SettJobBillVo>> recalculateSettJobBill(
        @RequestParam("billNo") String billNo,
        @RequestParam("recalculateWay") Integer recalculateWay);

    // 删除结算单(逻辑删除)
    @DeleteMapping(value = "/dataCore/settJobBill/deleteBill")
    Mono<ObjectResponse<SettJobBillVo>> deleteSettJobBill(
        @RequestParam("billNo") String billNo);
}
