package com.cdz360.biz.ant.service.iot;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.BizBiFeignClient;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.iot.param.ModifyEvseInfoParam;
import com.cdz360.biz.model.iot.param.OfflineEvseParam;
import com.cdz360.biz.model.iot.vo.OfflineEvseImportVo;
import com.cdz360.biz.model.iot.vo.OfflineEvseVo;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.iot.vo.EvseInfoVo;
import com.chargerlinkcar.framework.common.feign.IotBizClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import reactor.core.publisher.Mono;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class IotOfflineEvseService {

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;
    @Autowired
    private IotBizClient iotBizClient;
    @Autowired
    private BizBiFeignClient bizBiFeignClient;
    @Autowired
    private EvseProcessor evseProcessor;

    public ListResponse<EvseInfoVo> getOfflineEvseList(ListEvseParam param) {
        return dataCoreFeignClient.getOfflineEvseList(param);
    }


    /**
     * 更新脱机桩信息
     *
     * @param param
     */
    public BaseResponse updateEvseInfo(ModifyEvseInfoParam param) {
        if (param.getPower() != null && param.getPower() > 99999) {
            throw new DcArgumentException("请输入合理的桩功率");
        } else if (param.getName() != null && param.getName().length() > 32) {
            throw new DcArgumentException("您输入的桩名称太长");
        }

        BaseResponse response = iotBizClient.updateOfflineEvse(param);
        FeignResponseValidate.check(response);

        // 更新t_site中桩枪数量等信息
        List<String> siteIdList = null;
        if (CollectionUtils.isNotEmpty(param.getOriginalSiteIdList())
            && StringUtils.isNotBlank(param.getSiteId())) {

            if (param.getOriginalSiteIdList().size() == 1 &&
                !param.getSiteId().equals(param.getOriginalSiteIdList().get(0))) {
                // 单个编辑且修改了所属场站
                siteIdList = List.of(param.getOriginalSiteIdList().get(0), param.getSiteId());
            } else if (param.getOriginalSiteIdList().size() > 1) {
                // 批量编辑
                siteIdList = param.getOriginalSiteIdList();
                siteIdList.add(param.getSiteId());
            }
        }
        if (CollectionUtils.isNotEmpty(siteIdList)) {
            evseProcessor.recordEvsePlugInfo(
                siteIdList.stream().distinct().collect(Collectors.toList()));
        }

        return response;
    }

    /**
     * 移除脱机桩
     *
     * @param param
     */
    public BaseResponse removeOfflineEvse(List<OfflineEvseParam> param) {
        BaseResponse response = this.iotBizClient.removeOfflineEvse(param);
        FeignResponseValidate.check(response);

        evseProcessor.recordEvsePlugInfo(param.stream()
            .map(OfflineEvseParam::getSiteId).collect(Collectors.toList()));

        return response;
    }

    public Mono<ObjectResponse<OfflineEvseImportVo>> parseOfflineEvseExcel(FilePart file) {
        log.info(">>parseOfflineEvseExcel excel 文件: {}", file.filename());

        Mono<ObjectResponse<OfflineEvseImportVo>> m = null;

        try {
            File f = new File("/tmp/" + file.filename());
            m = file.transferTo(f)
                .then(Mono.just("文件上传成功"))
                .map(a -> {
                    try {

                        InputStream inputStream = new FileInputStream(f);
//                            log.info("inputStream: {}", inputStream);
                        List<List<String>> list = com.cdz360.biz.ant.utils.ExcelUtils.getOfflineEvseListByExcel(
                            inputStream, file.filename());
                        log.info("list.size = {}", list == null ? null : list.size());
                        ObjectResponse<OfflineEvseImportVo> result = dataCoreFeignClient.parseOfflineEvseExcel(
                            list);
                        log.info("result = {}", result);
                        FeignResponseValidate.check(result);

                        return result;
                    } catch (DcServiceException e) {
                        throw new DcServiceException(e.getMessage(), e);
                    } catch (Exception e) {
                        log.warn("<< 解析excel文件异常. message = {}", e.getMessage(), e);
                        throw new DcServiceException("解析excel文件异常，请求确认文件内容.");
                    }
                });
//                    .map(a -> RestUtils.buildObjectResponse(a));
        } catch (Exception e) {
            log.warn("msg: {}", e.getMessage(), e);
            throw new DcServiceException("文件上传失败");
        }
        return m;

//        return result;
    }


    /**
     * 批量新增脱机桩
     *
     * @param param
     * @return
     */
    public BaseResponse batchAddOfflineEvse(List<OfflineEvseVo> param) {
        BaseResponse response = iotBizClient.batchAddOfflineEvse(param);
        FeignResponseValidate.check(response);

        evseProcessor.recordEvsePlugInfo(param.stream()
            .map(OfflineEvseVo::getSiteId).collect(Collectors.toList()));

        return response;
    }

    /**
     * 导出脱机桩列表
     *
     * @param param
     * @return
     */
    public ObjectResponse<ExcelPosition> export(@RequestBody ListEvseParam param) {
        return bizBiFeignClient.exportOfflineEvse(param);
    }

}
