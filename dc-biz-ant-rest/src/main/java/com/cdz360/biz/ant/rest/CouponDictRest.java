package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.service.sysLog.CommercialSysLogService;
import com.cdz360.biz.model.trading.coupon.param.CouponDictParam;
import com.cdz360.biz.model.trading.coupon.param.CouponDictSearchParam;
import com.cdz360.biz.model.trading.coupon.type.CouponValidType;
import com.cdz360.biz.model.trading.coupon.vo.CouponDictVo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;

/**
 *
 * @since 7/27/2020 1:20 PM
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/api/couponDict")
@Tag(name = "券模板相关接口", description = "券模板")
public class CouponDictRest extends BaseController {

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;
    @Autowired
    private CommercialSysLogService commLogService;

    @Operation( summary = "新增券模板")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public BaseResponse create(ServerHttpRequest request, @RequestBody CouponDictParam req) {
        log.info("新增券模板: {}", JsonUtils.toJsonString(req));
        var res = dataCoreFeignClient.dictCreate(req);
        commLogService.couponDictCreateLog(req.getName(), request);
        return res;
    }

    @Operation( summary = "查询券模板列表")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    public ListResponse<CouponDictVo> getList(ServerHttpRequest request, @RequestBody CouponDictSearchParam req) {

        String commIdChain = super.getCommIdChain2(request);
        req.setIdChain(commIdChain);

        log.info("查询券模板列表: {}", JsonUtils.toJsonString(req));
//        Page<CorpOrgVO> pageVo = new Page<>(req.getIndex(), req.getSize());//getPage(request);
//        return tradingFeignClient.getDictList(req, pageVo.getPageNum(), pageVo.getPageSize());

        ListResponse<CouponDictVo> ret = dataCoreFeignClient.getDictList(req);

        if(ret != null && CollectionUtils.isNotEmpty(ret.getData())) {
            ret.getData().stream().forEach(e -> {
                if(CouponValidType.FIX.equals(e.getValidType())) {
                    e.setValidTimeTo(DateUtil.getPrevDate(e.getValidTimeTo()));
                }
            });
        }

        return ret;
    }

    @Operation( summary = "*查询券模板详情")
    @RequestMapping(value = "/getDetail", method = RequestMethod.POST)
    public BaseResponse getDetail(ServerHttpRequest request) {
        log.info("*查询券模板详情:");
        return null;
    }

    @Operation( summary = "作废券模板")
    @RequestMapping(value = "/disable", method = RequestMethod.GET)
    public BaseResponse disable(ServerHttpRequest request,
                                @RequestParam(value = "id") Long id,
                                @RequestParam(value = "name") String name) {
        log.info("作废券模板: id: {}, name: {}", id, name);
        var res = dataCoreFeignClient.disableDict(id);
        commLogService.couponDictDisableLog(name, request);
        return res;
    }
}