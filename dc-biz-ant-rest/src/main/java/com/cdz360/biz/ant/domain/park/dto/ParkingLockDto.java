package com.cdz360.biz.ant.domain.park.dto;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "地锁数据传输")
@Data
@Accessors(chain = true)
public class ParkingLockDto {

    @Schema(description = "供应商地锁ID(交互使用字段)", required = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String serialNumber;

    @Schema(description = "桩编号", required = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String evseNo;

    @Schema(description = "充电枪ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer plugId;

    @Schema(description = "车位编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String positionCode;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
