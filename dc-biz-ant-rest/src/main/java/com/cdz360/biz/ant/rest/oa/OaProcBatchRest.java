package com.cdz360.biz.ant.rest.oa;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.service.oa.OaProcessService;
import com.cdz360.biz.ant.service.oa.processBatch.OaProcBatchService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.ant.utils.FileUtil;
import com.cdz360.biz.model.oa.param.OaExcelImportParam;
import com.cdz360.biz.model.oa.vo.OaExcel.BatchStartResult;
import com.cdz360.biz.model.oa.vo.OaExcel.ExcelImportVo;
import com.cdz360.biz.oa.param.OaStartProcessParam;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.annotations.ApiOperation;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/oa/procBatch")
public class OaProcBatchRest {

    @Autowired
    private OaProcessService oaProcessService;
    @Autowired
    private OaProcBatchService oaProcBatchService;

    @PostMapping("/parseExcel")
    public Mono<ObjectResponse<ExcelImportVo<?>>> parseExcel(ServerHttpRequest request,
        @ModelAttribute OaExcelImportParam param) {
        FileUtil.checkExcelFile(param.getFile());
        return oaProcBatchService.parseProcessExcel(param)
            .map(RestUtils::buildObjectResponse);
    }

    @ApiOperation("批量启动OA流程")
    @PostMapping("/start")
    public Mono<BaseResponse> oaBatchStartProcess(
        ServerHttpRequest request, @RequestBody OaStartProcessParam param) {
        log.info("启动OA流程: {}, param = {}", LoggerHelper2.formatEnterLog(request), param);
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getDataList()), "流程表单参数不能为空");
        param.setTopCommId(Objects.requireNonNull(AntRestUtils.getTopCommId(request)).toString())
            .setCommIdChain(AntRestUtils.getCommIdChain(request))
            .setOpId(AntRestUtils.getSysUid(request).toString())
            .setOpName(AntRestUtils.getSysUserName(request));
        oaProcessService.oaBatchStartProcess(param);
        return Mono.just(RestUtils.success());
    }

    @ApiOperation("查看OA批量启动结果")
    @GetMapping("/viewResult")
    public Mono<ListResponse<BatchStartResult>> viewStartResult(ServerHttpRequest request,
        @RequestParam(value = "acquiredNum", required = false) Integer acquiredNum,
        @RequestParam("processDefinitionKey") String processDefinitionKey) {
        log.info("viewStartResult: {}, acquiredNum = {}, processDefinitionKey = {}",
            LoggerHelper2.formatEnterLog(request), acquiredNum, processDefinitionKey);
        String opId = AntRestUtils.getSysUid(request).toString();
        return Mono.just(oaProcessService.viewStartResult(acquiredNum, processDefinitionKey, opId))
            .map(RestUtils::buildListResponse);
    }

}
