package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.biz.ant.domain.User;
import com.cdz360.biz.model.cus.commScore.param.CommScoreParam;
import com.cdz360.biz.model.cus.commScore.param.ScoreClearParam;
import com.cdz360.biz.model.cus.commScore.param.UserScoreListParam;
import com.cdz360.biz.model.cus.commScore.param.UserScoreParam;
import com.cdz360.biz.model.cus.commScore.po.CommScoreLevelPo;
import com.cdz360.biz.model.cus.commScore.po.CommScoreLogPo;
import com.cdz360.biz.model.cus.commScore.vo.CommScoreVo;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.discount.param.UpdateCorpDiscountParam;
import com.cdz360.biz.model.cus.score.dto.ScoreUserDto;
import com.cdz360.biz.model.cus.score.param.ScoreUserAddParam;
import com.cdz360.biz.model.cus.score.param.ScoreUserParam;
import com.cdz360.biz.model.cus.score.param.SearchScoreUserParam;
import com.cdz360.biz.model.cus.settlement.param.SettlementEditParam;
import com.cdz360.biz.model.cus.score.dto.ScoreLevelDto;
import com.cdz360.biz.model.cus.score.dto.ScoreLogDto;
import com.cdz360.biz.model.cus.score.dto.ScoreSettingDto;
import com.cdz360.biz.model.cus.score.param.ScoreSettingParam;
import com.cdz360.biz.model.cus.score.param.ScoreUpdateParam;
import com.cdz360.biz.model.cus.score.param.SearchScoreLogParam;
import com.cdz360.biz.model.cus.score.param.SearchScoreSettingParam;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_USER,
        fallbackFactory = ReactorUserFeignHystrix.class)
public interface ReactorUserFeignClient {

    // 设置用户资料信息
    @PostMapping(value = "/api/user/setBasicInfo")
    Mono<ObjectResponse<User>> setBasicInfo(@RequestBody User user);

    @Operation(summary = "更新企业客户协议价配置")
    @PostMapping(value = "/api/corp/updateDiscount")
    Mono<ObjectResponse<CorpPo>> updateDiscount(@RequestBody UpdateCorpDiscountParam param);

    // 批量更新账单
    @PostMapping(value = "/api/corp/updateSettlementBatch")
    Mono<ObjectResponse<Integer>> updateSettlementBatch(@RequestBody SettlementEditParam param);

    // 根据phone和blocUserId查询集团用户
    @PostMapping("/api/rblocUser/selectRBlocUserByPhone")
    Mono<ObjectResponse<RBlocUser>> selectRBlocUserByPhone(@RequestBody RBlocUser rBlocUser);


    @GetMapping(value = "/api/commScore/getCommScoreVo")
    Mono<ObjectResponse<CommScoreVo>> getCommScoreVo(@RequestParam("commCusId") Long commCusId);

    @PostMapping(value = "/api/commScore/addCommScore")
    Mono<BaseResponse> addCommScore(@RequestBody CommScoreParam param);

    @PostMapping(value = "/api/commScore/commUserScoreRest")
    Mono<BaseResponse> commUserScoreRest(@RequestBody ScoreClearParam param);

    @PostMapping(value = "/api/commScore/commUserScoreModify")
    Mono<BaseResponse> commUserScoreModify(@RequestBody UserScoreParam param);

    @PostMapping(value = "/api/commScore/commUserScoreList")
    Mono<ListResponse<CommScoreLogPo>> commUserScoreList(@RequestBody UserScoreListParam param);

    @GetMapping(value = "/api/commScore/findLevelListByCommId")
    Mono<ListResponse<CommScoreLevelPo>> findLevelListByCommId(@RequestParam(value = "commId") Long commId);

    // 获取商户下用户数
    @GetMapping(value = "/api/user/getUserCount")
    Mono<ObjectResponse<Integer>> getUserCount(@RequestParam(value = "idChain", required = false) String idChain,
                                         @RequestParam(value = "topCommId", required = false) Long topCommId);

    /**
     * 新增积分体系
     * @param param
     * @return
     */
    @PostMapping(value = "/api/accountScore/addScoreSetting")
    Mono<BaseResponse> addScoreSetting(
        @RequestBody ScoreSettingParam param);

    /**
     * 获取积分体系列表
     * @param param
     * @return
     */
    @PostMapping(value = "/api/accountScore/getScoreSettingList")
    Mono<ListResponse<ScoreSettingDto>> getScoreSettingList(
        @RequestBody SearchScoreSettingParam param);

    /**
     * 删除积分体系
     * @param id
     * @return
     */
    @PostMapping(value = "/api/accountScore/deleteScoreSetting")
    Mono<BaseResponse> deleteScoreSetting(
        @RequestParam("id") Long id);

    /**
     * 启动积分体系
     * @param id
     * @return
     */
    @PostMapping(value = "/api/accountScore/startScoreSetting")
    Mono<BaseResponse> startScoreSetting(
        @RequestParam("id") Long id);

    /**
     * 停止积分体系
     * @param id
     * @return
     */
    @PostMapping(value = "/api/accountScore/stopScoreSetting")
    Mono<BaseResponse> stopScoreSetting(
        @RequestParam("id") Long id);

    /**
     * 编辑积分体系
     * @param param
     * @return
     */
    @PostMapping(value = "/api/accountScore/updateScoreSetting")
    Mono<BaseResponse> updateScoreSetting(
        @RequestBody ScoreSettingParam param);

    /**
     * 修改积分（增/减）
     * @param param
     * @return
     */
    @PostMapping(value = "/api/accountScore/updateScore")
    Mono<BaseResponse> updateScore(
        @RequestBody ScoreUpdateParam param);

    /**
     * 获取用户积分等级信息
     * @param userId
     * @return
     */
    @GetMapping(value = "/api/accountScore/getScoreLevel")
    Mono<ObjectResponse<ScoreLevelDto>> getScoreLevel(
        @RequestParam("userId") Long userId,
        @RequestParam("scoreSettingId") Long scoreSettingId);

    /**
     * 获取用户积分列表
     * @param param
     * @return
     */
    @PostMapping(value = "/api/accountScore/getScoreList")
    Mono<ListResponse<ScoreLogDto>> getScoreList(
        @RequestBody SearchScoreLogParam param);

    /**
     * 判断账号状态
     * @param accountType
     * @param commId
     * @param userId
     * @return
     */
    @GetMapping(value = "/api/user/checkAccountStatus")
    Mono<ObjectResponse<Boolean>> checkAccountStatus(
        @RequestParam(value = "accountType") PayAccountType accountType,
        @RequestParam(value = "commId") Long commId,
        @RequestParam(value = "userId") Long userId);

    @PostMapping(value = "/api/accountScore/getScoreUserList")
    Mono<ListResponse<ScoreUserDto>> getScoreUserList(@RequestBody SearchScoreUserParam param);

    @GetMapping(value = "/api/accountScore/deleteScoreUser")
    Mono<BaseResponse> deleteScoreUser(@RequestBody ScoreUserParam param);

    @PostMapping(value = "/api/accountScore/addScoreUser")
     Mono<BaseResponse> addScoreUser( @RequestBody ScoreUserAddParam param);
}
