package com.cdz360.biz.ant.service;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.domain.ErrorMsgDetailVo;
import com.cdz360.biz.ant.utils.RedisUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * PlugErrorMessageServiceImpl
 *
 * @since 2/29/2020 4:40 PM
 * <AUTHOR>
 */
@Service
@Slf4j
public class PlugErrorMessageService {

    public static final String ERROR_MESSAGE_REDIS_CONTEXT_KEY = "error_message_context";

    private Map<String, ErrorMsgDetailVo> errorMsgDetailVoMap;

    @Autowired
    private RedisUtil redisUtil;

    //    @PostConstruct
    public synchronized void init() {
        if(errorMsgDetailVoMap == null) {
            errorMsgDetailVoMap = new ConcurrentHashMap();
        }
//        redisUtil.put(ERROR_MESSAGE_REDIS_CONTEXT_KEY, "", -1);
        String ctx = redisUtil.get(ERROR_MESSAGE_REDIS_CONTEXT_KEY);
        if(StringUtils.isNotBlank(ctx)) {
            log.debug("重置异常对应表: {}", JsonUtils.toJsonString(ctx));
            Map<String, ErrorMsgDetailVo> mapCtx = JsonUtils.fromJson(redisUtil.get(ERROR_MESSAGE_REDIS_CONTEXT_KEY),
                    new TypeReference<HashMap<String, ErrorMsgDetailVo>>(){});
            if(mapCtx != null) {
                errorMsgDetailVoMap.clear();
                errorMsgDetailVoMap.putAll(mapCtx);
            }
        } else {
            log.error("找不到redis对应表: " + ERROR_MESSAGE_REDIS_CONTEXT_KEY);
        }
    }

    public ErrorMsgDetailVo getErrorMsg(Integer errorCode) {
        if (errorMsgDetailVoMap == null) init();
        if (errorMsgDetailVoMap == null ||
                errorCode == null ||
                errorMsgDetailVoMap.get(errorCode.toString()) == null) {
            return null;
        }
        return errorMsgDetailVoMap.get(errorCode.toString());
    }

    public String getContext() {
        return JsonUtils.toJsonString(errorMsgDetailVoMap);
    }
}