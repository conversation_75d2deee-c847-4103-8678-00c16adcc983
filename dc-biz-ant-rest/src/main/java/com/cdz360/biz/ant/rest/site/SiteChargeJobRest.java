package com.cdz360.biz.ant.rest.site;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.service.site.SiteChargeJobService;
import com.cdz360.biz.ant.service.sysLog.SiteSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.trading.site.param.ChargeJobLogParam;
import com.cdz360.biz.model.trading.site.param.ChargeJobParam;
import com.cdz360.biz.model.trading.site.po.SiteChargeJobPo;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobLogVo;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobMoveCorpList;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobVo;
import com.chargerlinkcar.framework.common.domain.param.SiteChargeJobParam;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;

@Tag(name = "定时开启充电相关接口", description = "定时开启充电相关接口")
@Slf4j
@RestController
public class SiteChargeJobRest extends BaseController {

    @Autowired
    private SiteChargeJobService siteChargeJobService;
    @Autowired
    private SiteSysLogService siteSysLogService;


    @Operation(summary = "绑定")
    @PostMapping("/api/siteChargeJobs/bindingJob")
    public BaseResponse bindingJob(ServerHttpRequest request,
                                   @RequestBody SiteChargeJobParam param) {
        log.info("param: {}", JsonUtils.toJsonString(param));
        param.setTopCommId(AntRestUtils.getTopCommId(request));
        param.setCurrCommId(AntRestUtils.getCommId(request));   // super.getCommIdLong(request));
        param.setSysUserId(super.getUserIdLong2(request));
        BaseResponse res = siteChargeJobService.bindingJob(param);
        siteSysLogService.bindingJobLog(param.getPlugNoList(), request);
        return res;
    }

    @Operation(summary = "解绑枪的定时充电任务")
    @PostMapping("/api/siteChargeJobs/unbindingJob")
    public BaseResponse unbindingJob(ServerHttpRequest request,
                                     @RequestBody SiteChargeJobParam param) {
        IotAssert.isNotNull(param.getPlugNoList(), "枪头列表不能为空");
        log.info("plugNoList: {}", JsonUtils.toJsonString(param.getPlugNoList()));
        BaseResponse res = siteChargeJobService.unbindingJob(param.getPlugNoList());
        siteSysLogService.unbindingJobLog(param.getPlugNoList(), request);
        return res;
    }

    @Operation(summary = "获取场站下的定时充电任务")
    @GetMapping("/api/siteChargeJobs/getSiteChargeJobBySiteId")
    public ListResponse<SiteChargeJobPo> getSiteChargeJobBySiteId(@RequestParam(value = "siteId", required = true)
                                                                          String siteId,
                                                                  @RequestParam(value = "jobName", required = false)
                                                                          String jobName) {
        return siteChargeJobService.getSiteChargeJobBySiteId(siteId, jobName);
    }

    @Operation(summary = "定时充电任务列表")
    @PostMapping("/api/siteChargeJobs/getChargeJobList")
    public ListResponse<SiteChargeJobVo> getChargeJobList(ServerHttpRequest request,
                                                          @RequestBody ChargeJobParam param) {
        param.setCommIdChain(super.getCommIdChain2(request));
        return siteChargeJobService.getChargeJobList(param);
    }

    @Operation(summary = "定时充电任务-启用/停用")
    @GetMapping("/api/siteChargeJobs/changeJobStatus")
    public BaseResponse changeJobStatus(ServerHttpRequest request,
                                        @RequestParam(value = "jobId") Long jobId,
                                        @RequestParam(value = "jobName") String jobName,
                                        @RequestParam(value = "jobStatus") Integer jobStatus) {

        BaseResponse res = siteChargeJobService.changeJobStatus(jobId, jobStatus);
        siteSysLogService.changeJobStatusLog(jobName, request);
        return res;
    }

    @Operation(summary = "定时充电任务-修改")
    @PostMapping("/api/siteChargeJobs/modifyChargeJob")
    public BaseResponse modifyChargeJob(ServerHttpRequest request,
                                        @RequestBody SiteChargeJobParam param) {
        log.info("param: {}", JsonUtils.toJsonString(param));
        param.setTopCommId(AntRestUtils.getTopCommId(request));
        param.setCurrCommId(AntRestUtils.getCommId(request));   // super.getCommIdLong(request));
        param.setSysUserId(super.getUserIdLong2(request));
        BaseResponse res = siteChargeJobService.modifyChargeJob(param);
        siteSysLogService.modifyChargeJobLog(param.getJobName(), request);
        return res;
    }

    @Operation(summary = "定时充电任务日志列表")
    @PostMapping("/api/siteChargeJobs/getChargeJobLogList")
    public ListResponse<SiteChargeJobLogVo> getChargeJobLogList(ServerHttpRequest request,
                                                                @RequestBody ChargeJobLogParam param) {
        return siteChargeJobService.getChargeJobLogList(param);
    }

    @Operation(summary = "企业修改商户，获取相关保留/删除的定时任务列表")
    @GetMapping("/api/siteChargeJobs/getMoveCorpDetail")
    public ObjectResponse<SiteChargeJobMoveCorpList> getMoveCorpDetail(@RequestParam("corpId") Long corpId,
                                                                       @RequestParam("commId") Long commId,
                                                                       ServerHttpRequest request) {
        log.info("企业修改商户 corpId: {}, commId: {}", corpId, commId);
        return siteChargeJobService.getMoveCorpDetail(corpId, commId);
    }
}
