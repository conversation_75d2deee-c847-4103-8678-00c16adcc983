package com.cdz360.biz.ant.rest.tj;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.service.tj.TjSurveyCalculationService;
import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationExpensesPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationIncomePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesOtherPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesPo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyCalculationInfoVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyCalculationResultVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyChargeAreaVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "投建勘察智能测算录入相关操作接口", description = "投建勘察智能测算录入相关操作接口")
@Slf4j
@RestController
@RequestMapping("/api/tj/survey/calculation")
public class TjSurveyCalculationRest {

    @Autowired
    private TjSurveyCalculationService tjSurveyCalculationService;

    @Operation(summary = "勘察站充电区域查询")
    @GetMapping(value = "/findTjSurveyChargeAreaBySurveyNo")
    public Mono<ListResponse<TjSurveyChargeAreaVo>> findTjSurveyChargeAreaBySurveyNo(
        ServerHttpRequest request,
        @RequestParam("surveyNo") String surveyNo) {
        log.info("{} 获取勘察站充电区域查询: param = {}", LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(surveyNo));
        return tjSurveyCalculationService.findTjSurveyChargeAreaBySurveyNo(surveyNo);
    }

    @Operation(summary = "新增或编辑勘察站充电区域")
    @PostMapping(value = "/saveTjSurveyChargeArea")
    public Mono<ObjectResponse<TjSurveyChargeAreaVo>> saveTjSurveyChargeArea(
        ServerHttpRequest request,
        @RequestBody TjSurveyChargeAreaVo tjSurveyChargeAreaVo) {
        log.info("{} 新增或编辑勘察站充电区域: param = {}", LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(tjSurveyChargeAreaVo));
        return tjSurveyCalculationService.saveTjSurveyChargeArea(tjSurveyChargeAreaVo);
    }

    @Operation(summary = "勘察站标准设施查询")
    @GetMapping(value = "/findTjSurveySupportingFacilitiesBySurveyNo")
    public Mono<ObjectResponse<TjSurveySupportingFacilitiesPo>> findTjSurveySupportingFacilitiesBySurveyNo(
        ServerHttpRequest request,
        @RequestParam("surveyNo") String surveyNo) {
        log.info("{} 获取勘察站标准设施查询: param = {}", LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(surveyNo));
        return tjSurveyCalculationService.findTjSurveySupportingFacilitiesBySurveyNo(surveyNo);
    }

    @Operation(summary = "新增或编辑勘察站标准设施")
    @PostMapping(value = "/saveTjSurveySupportingFacilities")
    public Mono<ObjectResponse<TjSurveySupportingFacilitiesPo>> saveTjSurveySupportingFacilities(
        ServerHttpRequest request,
        @RequestBody TjSurveySupportingFacilitiesPo tjSurveySupportingFacilitiesPo) {
        log.info("{} 新增或编辑勘察站标准设施: param = {}", LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(tjSurveySupportingFacilitiesPo));
        return tjSurveyCalculationService.saveTjSurveySupportingFacilities(tjSurveySupportingFacilitiesPo);
    }

    @Operation(summary = "勘察站其他设施查询")
    @GetMapping(value = "/findTjSurveySupportingFacilitiesOtherBySurveyNo")
    public Mono<ListResponse<TjSurveySupportingFacilitiesOtherPo>> findTjSurveySupportingFacilitiesOtherBySurveyNo(
        ServerHttpRequest request,
        @RequestParam("surveyNo") String surveyNo) {
        log.info("{} 获取勘察站其他设施查询: param = {}", LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(surveyNo));
        return tjSurveyCalculationService.findTjSurveySupportingFacilitiesOtherBySurveyNo(surveyNo);
    }

    @Operation(summary = "新增或编辑勘察站其他设施")
    @PostMapping(value = "/saveTjSurveySupportingFacilitiesOther")
    public Mono<ObjectResponse<TjSurveySupportingFacilitiesOtherPo>> saveTjSurveySupportingFacilitiesOther(
        ServerHttpRequest request,
        @RequestBody TjSurveySupportingFacilitiesOtherPo tjSurveySupportingFacilitiesOtherPo) {
        log.info("{} 新增或编辑勘察站其他设施: param = {}", LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(tjSurveySupportingFacilitiesOtherPo));
        return tjSurveyCalculationService.saveTjSurveySupportingFacilitiesOther(tjSurveySupportingFacilitiesOtherPo);
    }

    @Operation(summary = "勘察站高压查询")
    @GetMapping(value = "/findTjSurveyHighVoltageBySurveyNo")
    public Mono<ListResponse<TjSurveyHighVoltagePo>> findTjSurveyHighVoltageBySurveyNo(
        ServerHttpRequest request,
        @RequestParam("surveyNo") String surveyNo) {
        log.info("{} 获取勘察站高压查询: param = {}", LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(surveyNo));
        return tjSurveyCalculationService.findTjSurveyHighVoltageBySurveyNo(surveyNo);
    }

    @Operation(summary = "新增或编辑勘察站高压信息")
    @PostMapping(value = "/saveTjSurveyHighVoltage")
    public Mono<ObjectResponse<TjSurveyHighVoltagePo>> saveTjSurveyHighVoltage(
        ServerHttpRequest request,
        @RequestBody TjSurveyHighVoltagePo tjSurveyHighVoltagePo) {
        log.info("{} 新增或编辑勘察站高压信息: param = {}", LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(tjSurveyHighVoltagePo));
        return tjSurveyCalculationService.saveTjSurveyHighVoltage(tjSurveyHighVoltagePo);
    }

    @Operation(summary = "勘察站运营收入查询")
    @GetMapping(value = "/findTjSurveyOperationIncomeBySurveyNo")
    public Mono<ObjectResponse<TjSurveyOperationIncomePo>> findTjSurveyOperationIncomeBySurveyNo(
        ServerHttpRequest request,
        @RequestParam("surveyNo") String surveyNo) {
        log.info("{} 获取勘察场站运营收入: param = {}", LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(surveyNo));
        return tjSurveyCalculationService.findTjSurveyOperationIncomeBySurveyNo(surveyNo);
    }

    @Operation(summary = "新增或编辑场站勘察站运营收入")
    @PostMapping(value = "/saveTjSurveyOperationIncome")
    public Mono<ObjectResponse<TjSurveyOperationIncomePo>> saveTjSurveyOperationIncome(
        ServerHttpRequest request,
        @RequestBody TjSurveyOperationIncomePo tjSurveyOperationIncomePo) {
        log.info("{} 新增或编辑场站勘察站运营收入: param = {}", LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(tjSurveyOperationIncomePo));
        return tjSurveyCalculationService.saveTjSurveyOperationIncome(tjSurveyOperationIncomePo);
    }

    @Operation(summary = "勘察站运营支出查询")
    @GetMapping(value = "/findTjSurveyOperationExpensesBySurveyNo")
    public Mono<ObjectResponse<TjSurveyOperationExpensesPo>> findTjSurveyOperationExpensesBySurveyNo(
        ServerHttpRequest request,
        @RequestParam("surveyNo") String surveyNo) {
        log.info("获取勘察场站运营支出: param = {}", LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(surveyNo));
        return tjSurveyCalculationService.findTjSurveyOperationExpensesBySurveyNo(surveyNo);
    }

    @Operation(summary = "新增或编辑场站勘察站运营支出")
    @PostMapping(value = "/saveTjSurveyOperationExpenses")
    public Mono<ObjectResponse<TjSurveyOperationExpensesPo>> saveTjSurveyOperationExpenses(
        ServerHttpRequest request,
        @RequestBody TjSurveyOperationExpensesPo tjSurveyOperationExpensesPo) {
        log.info("新增或编辑场站勘察站运营支出: param = {}", LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(tjSurveyOperationExpensesPo));
        return tjSurveyCalculationService.saveTjSurveyOperationExpenses(tjSurveyOperationExpensesPo);
    }

    @Operation(summary = "新增或编辑场站勘察站智能测算信息")
    @PostMapping(value = "/saveTjSurveyCalculationInfo")
    public Mono<ObjectResponse<TjSurveyCalculationInfoVo>> saveTjSurveyCalculationInfo(
        ServerHttpRequest request,
        @RequestBody TjSurveyCalculationInfoVo tjSurveyCalculationInfoVo) {
        log.info("{} 新增或编辑场站勘察站智能测算录入信息: param = {}", LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(tjSurveyCalculationInfoVo));
        return tjSurveyCalculationService.saveTjSurveyCalculationInfo(tjSurveyCalculationInfoVo);
    }

    @Operation(summary = "智能测算")
    @GetMapping(value = "/calculationResult")
    public Mono<ListResponse<TjSurveyCalculationResultVo>> calculationResult(
        ServerHttpRequest request,
        @RequestParam("surveyNo") String surveyNo) {
        log.info("{} 智能测算: param = {}", LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(surveyNo));
        return tjSurveyCalculationService.calculationResult(surveyNo);
    }
}
