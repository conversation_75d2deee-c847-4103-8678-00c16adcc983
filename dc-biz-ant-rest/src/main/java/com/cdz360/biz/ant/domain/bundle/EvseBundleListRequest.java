package com.cdz360.biz.ant.domain.bundle;

import com.cdz360.biz.model.upgradepg.type.BundleType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 *  升级包查询请求
 * <EMAIL>
 * <AUTHOR>
 * @since  2019/9/17 15:19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EvseBundleListRequest extends BaseRequest {

    private List<String> evseIds;

    @Schema(description = "排序方式 1：按新增时间倒序 2：按目标版本号顺序")
    private Integer sortType;

    @Schema(description = "升级包类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<BundleType> typeList;

    @Schema(description = "状态（0已停用；1正常；）")
    private List<Integer> statusList;

}
