package com.cdz360.biz.ant.rest.site;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.service.sysLog.SiteSysLogService;
import com.cdz360.biz.model.common.request.SiteCtrlRequest;
import com.cdz360.biz.model.iot.dto.SiteCtrlDto;
import com.cdz360.biz.model.iot.vo.SiteCtrlCfgVo;
import com.chargerlinkcar.framework.common.feign.IotBizClient;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@Tag(name = "场站控制器接口", description = "场站控制器接口")
public class SiteCtrlRest {

    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;

    @Autowired
    private IotBizClient iotBizClient;
    @Autowired
    private SiteSysLogService logService;

    @PostMapping("/api/siteCtrl/add")
    @Operation(summary = "控制器新增")
    public BaseResponse add(ServerHttpRequest request,
                            @RequestBody SiteCtrlRequest req) {
        log.info("req: {}", req);
        BaseResponse res = iotDeviceMgmFeignClient.add(req);
        logService.siteCtrlAddLog(req.getNum(), request);
        return res;
    }

    @PostMapping("/api/siteCtrl/edit")
    @Operation(summary = "控制器修改")
    public BaseResponse edit(ServerHttpRequest request,
                             @RequestBody SiteCtrlRequest req) {
        log.info("req: {}", req);
        BaseResponse res = iotDeviceMgmFeignClient.edit(req);
        logService.siteCtrlEditLog(req.getNum(), request);
        return res;
    }

    @GetMapping("/api/siteCtrl/list")
    @Operation(summary = "查询控制器列表")
    public ListResponse<SiteCtrlDto> list(@RequestParam(value = "keyword", required = false) String keyword,
                                          @RequestParam(value = "siteId") String siteId,
                                          @RequestParam(value = "start") long start,
                                          @RequestParam(value = "size") long size) {
        log.info("keyword: {}, siteId: {}, start: {}, size: {}", keyword, siteId, start, size);
        return iotDeviceMgmFeignClient.list(keyword, siteId, start, size);
    }

    @GetMapping("/api/siteCtrl/disable")
    @Operation(summary = "控制器解绑")
    public BaseResponse disable(ServerHttpRequest request,
                                @RequestParam(value = "num") String num) {
        log.info("num: {}", num);
        BaseResponse res = iotDeviceMgmFeignClient.disable(num);
        logService.siteCtrlDisableLog(num, request);
        return res;
    }

    @GetMapping("/api/siteCtrl/reboot")
    @Operation(summary = "控制器重启")
    public BaseResponse reboot(ServerHttpRequest request,
                               @RequestParam(value = "num") String num) {
        log.info("num: {}", num);
        var res = iotBizClient.reboot(num);
        logService.siteCtrlRebootLog(num, request);
        return res;
    }

    @GetMapping("/api/siteCtrlCfg/send")
    @Operation(summary = "控制器配置更新")
    public BaseResponse siteCtrlSend(@RequestParam(value = "num") String num) {
        log.info("num: {}", num);
        iotBizClient.siteCtrlSend(num);
        return RestUtils.success();
    }

    @Operation(summary = "新增场站配置模板或更新")
    @PostMapping(value = "/api/siteCtrlCfg/add")
    public BaseResponse addOrUpdate(
            ServerHttpRequest request,
            @RequestBody SiteCtrlCfgVo po) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}", JsonUtils.toJsonString(po));

        po.setPwrCtrlLmt(SiteCtrlCfgVo.convertVo2PowerCtrlLmt(po.getPowerCtrlLmtVo()));

        BaseResponse res = iotDeviceMgmFeignClient.addOrUpdate(po);
        logService.siteCtrlCfgAddLog(po.getCtrlNum(), request);
        return res;
    }

    @Operation(summary = "获取场站最新的配置模板信息", description = "保存数据最新")
    @GetMapping(value = "/api/siteCtrlCfg/get")
    public ObjectResponse<SiteCtrlCfgVo> findByCtrlNo(
            ServerHttpRequest request,
            @Parameter(name = "场站控制器编号", required = true) @RequestParam(value = "ctrlNum") String ctrlNum) {
        log.info(LoggerHelper2.formatEnterLog(request));
        ObjectResponse<SiteCtrlCfgVo> res = iotDeviceMgmFeignClient.findByCtrlNo(ctrlNum);
        FeignResponseValidate.check(res);

        if(res.getData().getPwrCtrlLmt() != null && res.getData().getPwrCtrlLmt().size() == 1) {
            res.getData().setPowerCtrlLmtVo(SiteCtrlCfgVo.convertPowerCtrlLmt2Vo(res.getData().getPwrCtrlLmt()));
        } else {
            log.warn("配电策略组数目不正确，请确认: {}", JsonUtils.toJsonString(res));
        }
        return res;
    }

    @Operation(summary = "查看场站控制器配置", description = "从redis中获取场站控制器上报配置信息")
    @GetMapping(value = "/api/siteCtrlCfg/getInfo")
    public ObjectResponse<SiteCtrlCfgVo> getBySiteCtrl(
            ServerHttpRequest request,
            @Parameter(name = "场站控制器编号", required = true) @RequestParam(value = "ctrlNum") String ctrlNum) {
        log.info(LoggerHelper2.formatEnterLog(request));
        ObjectResponse<SiteCtrlCfgVo> res = iotDeviceMgmFeignClient.getBySiteCtrl(ctrlNum);
        FeignResponseValidate.check(res);

        if(res.getData().getPwrCtrlLmt() != null && res.getData().getPwrCtrlLmt().size() == 1) {
            res.getData().setPowerCtrlLmtVo(SiteCtrlCfgVo.convertPowerCtrlLmt2Vo(res.getData().getPwrCtrlLmt()));
        } else {
            log.warn("配电策略组数目不正确，请确认: {}", JsonUtils.toJsonString(res));
        }
        return res;
    }

    @Operation(summary = "下发获取控制器配置指令", description = "配置查看控制器配置接口使用，该接口仅做下发")
    @GetMapping(value = "/api/siteCtrlCfg/send2GetCfg")
    public BaseResponse send2GetCfg(
            ServerHttpRequest request,
            @Parameter(name = "场站控制器编号", required = true) @RequestParam(value = "ctrlNum") String ctrlNum) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return iotDeviceMgmFeignClient.send2GetCfg(ctrlNum);
    }
}
