package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.oa.dto.ProcessInstanceExDto;
import com.cdz360.biz.oa.dto.account.OaAccountDto;
import com.cdz360.biz.oa.dto.account.OaGroupDto;
import com.cdz360.biz.oa.param.AddSignTaskParam;
import com.cdz360.biz.oa.param.ApproveParam;
import com.cdz360.biz.oa.param.BatchAddSignTaskParam;
import com.cdz360.biz.oa.param.BatchApproveParam;
import com.cdz360.biz.oa.param.BillingProcessParam;
import com.cdz360.biz.oa.param.CreateProcessInstanceParam;
import com.cdz360.biz.oa.param.DepositInvoiceProcessParam;
import com.cdz360.biz.oa.param.FastSearchParam;
import com.cdz360.biz.oa.param.ListTaskParam;
import com.cdz360.biz.oa.param.MarkDeleteCommentParam;
import com.cdz360.biz.oa.param.OaProcessTagParam;
import com.cdz360.biz.oa.param.OaStartProcessParam;
import com.cdz360.biz.oa.param.PrepaidInvoiceProcessParam;
import com.cdz360.biz.oa.param.RechargeParam;
import com.cdz360.biz.oa.param.UserApproveParam;
import com.cdz360.biz.oa.param.account.ListOaGroupParam;
import com.cdz360.biz.oa.param.account.ListOaGroupUserParam;
import com.cdz360.biz.oa.param.account.OaModifyGroupParam;
import com.cdz360.biz.oa.vo.BatchApproveResult;
import com.cdz360.biz.oa.vo.FormModelVo;
import com.cdz360.biz.oa.vo.OaRechargeInfoVo;
import com.cdz360.biz.oa.vo.OaTaskVo;
import com.cdz360.biz.oa.vo.ProcessDefinitionVo;
import com.cdz360.biz.oa.vo.ProcessInstanceVo;
import java.util.Map;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class OaFeignHystrix
    implements FallbackFactory<OaFeignClient> {

    @Override
    public OaFeignClient apply(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_OA,
            throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_OA,
            throwable.getStackTrace());

        return new OaFeignClient() {
            @Override
            public Mono<ListResponse<OaAccountDto>> groupUserList(ListOaGroupUserParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = groupUserList (获取审核组用户列表), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<OaGroupDto>> groupList(ListOaGroupParam param) {
                log.error("【服务熔断】: Service = {}, api = groupList (获取审核组列表), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<String>> modifyGroup(OaModifyGroupParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = modifyGroup (创建/修改审核组信息), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<OaGroupDto>> removeGroup(String gid) {
                log.error("【服务熔断】: Service = {}, api = removeGroup (删除审核组信息), gid = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, gid);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<OaTaskVo>> getAssigneeApplyTasks(ListTaskParam param) {
                log.error("服务[{}]接口熔断 - 用户获取自己申请的任务列表, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<OaTaskVo>> getAssigneeAuditTasks(ListTaskParam param) {
                log.error("服务[{}]接口熔断 - 用户获取自己审核的任务列表, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<OaTaskVo>> getAssigneeTasks(ListTaskParam param) {
                log.error("服务[{}]接口熔断 - 用户获取任务列表, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<BaseResponse> assigneeHandleTask(ApproveParam param) {
                log.error("服务[{}]接口熔断 - 审核人执行批准操作, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<ObjectResponse<BatchApproveResult>> handleTaskBatch(
                BatchApproveParam param) {
                log.error("服务[{}]接口熔断 - 审核人执行批量批准操作, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<String>> addSignTaskAfter(AddSignTaskParam param) {
                log.error("服务[{}]接口熔断 - 任务向后加签, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<BatchApproveResult>> batchAddSignAfter(
                BatchAddSignTaskParam param) {
                log.error("服务[{}]接口熔断 - 任务批量向后加签, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> assigneeHandleTaskComment(ApproveParam param) {
                log.error("服务[{}]接口熔断 - 流程审核人附言, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<ObjectResponse<BatchApproveResult>> handleTaskCommentBatch(
                BatchApproveParam params) {
                log.error("服务[{}]接口熔断 - 流程审核人批量附言, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(params));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<OaRechargeInfoVo>> processDetail(String procInstId,
                String oUid) {
                log.error("服务[{}]接口熔断 - 获取充值申请流程信息, procInstId = {}, oUid = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, procInstId, oUid);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<ProcessDefinitionVo>> processDefinitions(
                Map<String, String> params) {
                log.error("服务[{}]接口熔断 - 流程定义列表, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(params));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<String> getProcessDefinitionStartForm(String processDefinitionId) {
                log.error("服务[{}]接口熔断 - 流程定义启动表单数据, processDefinitionId = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, processDefinitionId);
                return Mono.just("");
            }

            @Override
            public Mono<ProcessDefinitionVo> getProcessDefinitionInfo(String processDefinitionId) {
                log.error("服务[{}]接口熔断 - 流程定义信息, processDefinitionId = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, processDefinitionId);
                return Mono.just(new ProcessDefinitionVo());
            }

            @Override
            public Mono<ObjectResponse<OaGroupDto>> getOaGroupById(String gid) {
                log.error("服务[{}]接口熔断 - 获取审核组, gid = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, gid);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<ProcessInstanceVo>> startNewProcessInstance(
                CreateProcessInstanceParam param) {
                log.error("服务[{}]接口熔断 - 启动新的流程实例, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<FormModelVo>> procInstStartFormInfo(
                String processInstanceId) {
                log.error("服务[{}]接口熔断 - 获取流程实例的开始表单, processInstanceId = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, processInstanceId);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<ProcessInstanceExDto>> fastSearch(FastSearchParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = fastSearch (获取相关审批流), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<BaseResponse> saveTag(OaProcessTagParam param) {
                log.error("【服务熔断】: Service = {}, api = saveTag (记录标签), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, param);
                return Mono.just(RestUtils.serverBusy());
            }


            @Override
            public Mono<BaseResponse> batchSaveTag(OaProcessTagParam param) {
                log.error("【服务熔断】: Service = {}, api = batchSaveTag (记录标签), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, param);
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<ObjectResponse<String>> startChargeFeeProcess(RechargeParam param) {
                log.error("服务[{}]接口熔断 - 启动电价下发申请, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> userHandleTask(UserApproveParam params) {
                log.error("服务[{}]接口熔断 - 提交人重新编辑, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(params));
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<ObjectResponse<String>> startBillingProcess(BillingProcessParam params) {
                log.error("服务[{}]接口熔断 - 创建企客对账开票流程, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(params));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<String>> startPrepaidProcess(
                PrepaidInvoiceProcessParam params) {
                log.error("服务[{}]接口熔断 - 创建预付订单开票流程, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(params));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<String>> depositInvoiceStartProcess(
                DepositInvoiceProcessParam params) {
                log.error("服务[{}]接口熔断 - 创建企客充值开票流程, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(params));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<Integer>> markDeleteComment(MarkDeleteCommentParam param) {
                log.error("服务[{}]接口熔断 - 审批流标记删除, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<ProcessInstanceVo>> startOaProcess(
                OaStartProcessParam param) {
                log.error("服务[{}]接口熔断 - 启动新的流程实例, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<ProcessInstanceVo>> resubmitOaProcess(
                OaStartProcessParam param) {
                log.error("服务[{}]接口熔断 - 流程实例驳回后重新提交, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }
        };
    }

    @Override
    public <V> Function<V, OaFeignClient> compose(
        Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_OA);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(
        Function<? super OaFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_OA);
        return null;
    }
}
