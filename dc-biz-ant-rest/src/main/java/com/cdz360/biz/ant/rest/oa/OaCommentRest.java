package com.cdz360.biz.ant.rest.oa;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.service.oa.OaCommentService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.oa.param.MarkDeleteCommentParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "审批流留言相关接口", description = "审批流留言")
@Slf4j
@RestController
@RequestMapping("/oa/comment")
public class OaCommentRest {

    @Autowired
    private OaCommentService oaCommentService;

    @Operation(summary = "审批流标记删除")
    @PostMapping(value = "/markDelete")
    public Mono<ObjectResponse<Integer>> markDeleteComment(
        ServerHttpRequest request, @RequestBody MarkDeleteCommentParam param) {
        log.info("审批流标记删除: param = {}", JsonUtils.toJsonString(param));
        param.setOUid(AntRestUtils.getSysUid(request));
        return this.oaCommentService.markDeleteComment(param);
    }

}
