package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.service.DataReportService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.trading.site.param.SiteReportParam;
import com.cdz360.biz.model.trading.site.vo.BiSiteGcMonthExpenseStatisticVo;
import com.cdz360.biz.model.trading.site.vo.BiSiteGcMonthIncomeVo;
import com.cdz360.biz.model.trading.site.vo.SiteBaseInfoVo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR> 数据统计及报表相关
 * @since 2019.2.12
 */
@Slf4j
@RestController
@RequestMapping("/api/dataReport")
public class DataReportRest extends BaseController {

    @Autowired
    private DataReportService dataReportService;
//    @Autowired
//    private MerchantFeignClient merchantFeignClient;


    /**
     * 站点详情页面，根据站点统计昨天、近7天、近30天的充电次数、电量、金额
     *
     * @param siteId
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getSiteSurvey")
    public ListResponse<JsonNode> getSiteSurvey(ServerHttpRequest request,
        @RequestParam(value = "siteId") String siteId) {
        String token = getToken2(request);
        //调用服务
        return dataReportService.getSiteSurvey(siteId, token);
    }

    /**
     * 场站基础信息
     *
     * @param param
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getSiteBaseInfo")
    public Mono<ObjectResponse<SiteBaseInfoVo>> getSiteBaseInfo(ServerHttpRequest request,
        @RequestBody SiteReportParam param) {
        log.info("场站基础信息: {}", JsonUtils.toJsonString(param));
        final Long sysUid = AntRestUtils.getSysUid(request);
        IotAssert.isNotNull(sysUid, "请登录");
        //调用服务
        return dataReportService.getSiteBaseInfo(param);
    }

    /**
     * 场站月运营数据
     *
     * @param request
     * @param param
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getSiteMonthSurvey")
    public Mono<ListResponse<BiSiteGcMonthIncomeVo>> getSiteMonthSurvey(ServerHttpRequest request,
        @RequestBody SiteReportParam param) {
        log.info("场站月运营数据: {}", param);
        final Long sysUid = AntRestUtils.getSysUid(request);
        IotAssert.isNotNull(sysUid, "请登录");
        //调用服务
        return dataReportService.getSiteMonthSurvey(param);
    }

    /**
     * 运营场站月支出
     *
     * @param request
     * @param param
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/getSiteExpenseMonthly")
    public Mono<ListResponse<BiSiteGcMonthExpenseStatisticVo>> getSiteExpenseMonthly(
        ServerHttpRequest request,
        @RequestBody SiteReportParam param) {
        log.info("运营场站月支出: {}", param);
        final Long sysUid = AntRestUtils.getSysUid(request);
        IotAssert.isNotNull(sysUid, "请登录");
        //调用服务
        return dataReportService.getSiteExpenseMonthly(param);
    }


}
