package com.cdz360.biz.ant.rest.settle;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.aop.CheckToken;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.TradingFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorAuthCenterFeignClient;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.settle.dto.SettRuleConfig2DTO;
import com.cdz360.biz.model.settle.param.ListSettRuleParam;
import com.cdz360.biz.model.settle.type.SettAccountType;
import com.cdz360.biz.model.settle.vo.SettRuleConfig2;
import com.cdz360.biz.model.settle.vo.SettRuleConfig2VO;
import com.cdz360.biz.model.settle.vo.SettRuleConfigWithAccountVo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.utils.feign.sett.SettleServerFeignClient;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.util.HashMap;
import java.util.Map;

@Tag(name = "商户结算任务相关接口", description = "商户结算任务相关接口")
@Slf4j
@RestController
public class SettRuleConfigRest {

    @Autowired
    private SettleServerFeignClient settleServerFeignClient;

//    @Autowired
//    private AntUserFeignClient antUserFeignClient;

//    @Autowired
//    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private ReactorAuthCenterFeignClient reactorAuthCenterFeignClient;
    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private TradingFeignClient tradingFeignClient;
//    @Operation(summary = "接口测试测试")
//    @GetMapping(value = "/api/test/testSettle")
//    public Mono<ObjectResponse<String>> testActive() {
//        return settleServerFeignClient.testActive()
//                .doOnNext(res -> log.info("res : {}", JsonUtils.toJsonString(res)));
//    }

    @Operation(summary = "分页查询清结算规则")
    @PostMapping(value = "/api/sett/rule/search/page")
    public Mono<ListResponse<SettRuleConfig2VO>> search(
            ServerHttpRequest request, @RequestBody ListSettRuleParam param) {
        log.info("分页查询清结算规则: param = {}", JsonUtils.toJsonString(param));

        param.setCommIdChain(AntRestUtils.getCommIdChain(request));
        String token2 = AntRestUtils.getToken2(request);
        return settleServerFeignClient.search(param)
                .doOnNext(FeignResponseValidate::check)
                .flatMap(res -> {
                    final Map<Long, String> corpNameMap = new HashMap<>();
                    return Flux.fromIterable(res.getData())
                            .flatMap(item -> this.updateAccount(item, corpNameMap, token2))
                            .collectList()
                            .map(i -> res);
                });
    }

    @Operation(summary = "场站结算任务配置")
    @GetMapping(value = "/api/sett/rule/getTransferInfoById")
    public Mono<ObjectResponse<SettRuleConfig2>> getTransferInfoById(ServerHttpRequest request,
                                                                     @RequestParam(value = "commId") Long commId,
                                                                     @RequestParam(value = "siteId") String siteId) {
        log.info("commId = {}", commId);
        IotAssert.isNotNull(commId, "商户ID不能为空");
        IotAssert.isNotNull(siteId, "场站信息不能为空");
        ObjectResponse<SitePo> siteResponse = dataCoreFeignClient.getSiteById(siteId);
        FeignResponseValidate.check(siteResponse);
        return settleServerFeignClient.getTransferInfoById(commId).map(p -> {
            if (null != p && null != p.getData()) {
                p.getData().setFirstTransferTime(siteResponse.getData().getFirstTransferTime());
            }
            return p;
        });
    }

    private Mono<String> updateAccount(SettRuleConfig2VO item,
                                       final Map<Long, String> corpNameMap, String token) {
        return Mono.just(token)
                .filter(t -> CollectionUtils.isNotEmpty(item.getConsumeRule()))
                .map(t -> item.getConsumeRule())
                .flatMapMany(Flux::fromIterable)
                .flatMap(rule -> this.getAccountName(rule.getAccountType(), rule.getAccountId(), corpNameMap, token)
                        .doOnNext(rule::setAccountName))
                .collectList()
                .map(l -> token)
                .switchIfEmpty(Mono.just(token))
                .filter(t -> null != item.getServRule() &&
                        CollectionUtils.isNotEmpty(item.getServRule().getFeeAccList()))
                .map(t -> item.getServRule().getFeeAccList())
                .flatMapMany(Flux::fromIterable)
                .flatMap(rule -> this.getAccountName(rule.getAccountType(), rule.getAccountId(), corpNameMap, token)
                        .doOnNext(rule::setAccountName))
                .collectList()
                .map(l -> token)
                .filter(t -> CollectionUtils.isNotEmpty(item.getChargeRule()))
                .doOnNext(t -> item.getChargeRule().forEach(rule -> {
                    switch (rule.getAccountType()) {
                        case COMMERCIAL:
                            rule.setAccountName("商户会员充值");
                            break;
                        case NORMAL_CORP:
                            rule.setAccountName("平台企客充值");
                            break;
                    }
                }))
                .switchIfEmpty(Mono.just(token));

//        if (CollectionUtils.isNotEmpty(item.getConsumeRule())) {
//            item.getConsumeRule().forEach(rule ->
//                    this.getAccountName(rule.getAccountType(), rule.getAccountId(), corpNameMap, token)
//                            .subscribe(rule::setAccountName));
//        }
//
//        if (null != item.getServRule() &&
//                CollectionUtils.isNotEmpty(item.getServRule().getFeeAccList())) {
//            item.getServRule().getFeeAccList()
//                    .forEach(rule ->
//                            this.getAccountName(rule.getAccountType(), rule.getAccountId(), corpNameMap, token)
//                                    .subscribe(rule::setAccountName));
//        }
//
//        if (CollectionUtils.isNotEmpty(item.getChargeRule())) {
//            item.getChargeRule().forEach(rule -> {
//                switch (rule.getAccountType()) {
//                    case COMMERCIAL:
//                        rule.setAccountName("商户会员充值");
//                        break;
//                    case NORMAL_CORP:
//                        rule.setAccountName("平台企客充值");
//                        break;
//                }
//            });
//        }
    }

    private Mono<String> getAccountName(SettAccountType accountType, Long accountId,
                                        Map<Long, String> corpNameMap, String token) {
        String corpName = "";
        if (SettAccountType.HLHT_CORP.equals(accountType)) {
            corpName = corpNameMap.get(accountId);
            if (null == corpName) {
                return reactorAuthCenterFeignClient.getCorpByUid(token, accountId)
                        .doOnNext(FeignResponseValidate::checkIgnoreData)
                        .map(corpRes -> {
                            if (ResultConstant.RES_SUCCESS_CODE == corpRes.getStatus() &&
                                    null != corpRes.getData()) {
                                corpNameMap.put(accountId, corpRes.getData().getCorpName());
                                return corpRes.getData().getCorpName();
                            }
                            return "";
                        });
//                // 获取企业客户信息
//                ObjectResponse<CorpPo> corpRes = authCenterFeignClient.getCorpByUid(token, accountId);
//                FeignResponseValidate.checkIgnoreData(corpRes);
//
//                if (ResultConstant.RES_SUCCESS_CODE == corpRes.getStatus() &&
//                        null != corpRes.getData()) {
//                    corpName = corpRes.getData().getCorpName();
//                    corpNameMap.put(accountId, corpName);
//                }
            }
        } else {
            switch (accountType) {
                case PREPAY:
                    corpName = "即充即退消费";
                    break;
                case PERSONAL:
                    corpName = "个人账户消费";
                    break;
                case COMMERCIAL:
                    corpName = "商户会员消费";
                    break;
                case NORMAL_CORP:
                    corpName = "平台企客消费";
                    break;
            }
        }
        return Mono.just(corpName);
    }

    @Operation(summary = "根据ID获取清结算规则")
    @GetMapping(value = "/api/sett/rule/getById")
    public Mono<ObjectResponse<SettRuleConfigWithAccountVo>> getRuleById(
            ServerHttpRequest request, @RequestParam(value = "id") Long id) {
        log.info("根据ID获取清结算规则: id = {}", id);
        String token2 = AntRestUtils.getToken2(request);
        return settleServerFeignClient.getRuleById(id)
                .doOnNext(FeignResponseValidate::check)
                .flatMap(res -> {
                    final Map<Long, String> corpNameMap = new HashMap<>();
                    return this.updateAccount(res.getData(), corpNameMap, token2)
                            .map(i -> res);
                });
    }

    @CheckToken
    @Operation(summary = "清结算规则新增")
    @PostMapping(value = "/api/sett/rule/add")
    public Mono<ObjectResponse<SettRuleConfig2VO>> ruleAdd(
            ServerHttpRequest request,
            @RequestBody SettRuleConfig2DTO dto) {
        log.info("清结算规则新增: {}", JsonUtils.toJsonString(dto));

        dto.setOpAccount(AntRestUtils.getSysUserLoginName(request));
        return settleServerFeignClient.ruleAdd(dto);
    }

    @CheckToken
    @Operation(summary = "清结算规则更新")
    @PostMapping(value = "/api/sett/rule/update")
    public Mono<ObjectResponse<SettRuleConfig2VO>> ruleUpdate(
            ServerHttpRequest request,
            @RequestBody SettRuleConfig2DTO dto) {
        log.info("清结算规则更新: {}", JsonUtils.toJsonString(dto));

        dto.setOpAccount(AntRestUtils.getSysUserLoginName(request));
        return settleServerFeignClient.ruleUpdate(dto);
    }

    @CheckToken
    @Operation(summary = "清结算规则删除")
    @GetMapping(value = "/api/sett/rule/delete")
    public Mono<ObjectResponse<Boolean>> ruleRemove(
            ServerHttpRequest request, @RequestParam("id") Long id) {
        log.info("清结算规则删除: id = {}", id);
        return settleServerFeignClient.ruleRemove(id);
    }

    @CheckToken
    @Operation(summary = "清结算规则停用")
    @GetMapping(value = "/api/sett/rule/enable")
    public Mono<ObjectResponse<Boolean>> ruleEnable(
            ServerHttpRequest request,
            @Parameter(name = "记录ID") @RequestParam("id") Long id,
            @Parameter(name = "true(启用);false(禁用)") @RequestParam("enable") Boolean enable) {
        log.info("清结算规则停用: {}", LoggerHelper2.formatEnterLog(request));
        return settleServerFeignClient.ruleEnable(id, enable);
    }
}
