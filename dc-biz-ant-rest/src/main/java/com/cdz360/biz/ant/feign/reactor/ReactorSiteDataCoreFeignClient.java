package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.charge.vo.SiteVo;
import com.cdz360.biz.model.geo.param.ListCitiesParam;
import com.cdz360.biz.model.geo.param.ListCountriesParam;
import com.cdz360.biz.model.geo.po.DistrictPo;
import com.cdz360.biz.model.geo.vo.GeoCitiesVo;
import com.cdz360.biz.model.geo.vo.GeoCountriesVo;
import com.cdz360.biz.model.merchant.dto.SiteTinyDto;
import com.cdz360.biz.model.trading.contract.vo.ContractVo;
import com.cdz360.biz.model.trading.cus.vo.UnliquidatedOrderVo;
import com.cdz360.biz.model.trading.hlht.dto.CecQueryQeuipBusinessPolicyResult;
import com.cdz360.biz.model.trading.order.dto.CusLastOrderSiteDto;
import com.cdz360.biz.model.trading.order.dto.CusOrderBiDto;
import com.cdz360.biz.model.trading.order.param.ListCusOrderBiParam;
import com.cdz360.biz.model.trading.site.param.ListCecSiteParam;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.param.UpdateSiteOaDefaultValueParam;
import com.cdz360.biz.model.site.po.PriceTemplatePo;
import com.cdz360.biz.model.trading.site.po.SiteOaDefaultConfigPo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.site.vo.PartnerSiteVo;
import com.cdz360.biz.model.trading.site.vo.SiteOrderStatsVo;
import com.cdz360.data.sync.model.Site;
import com.chargerlinkcar.framework.common.domain.PartnerSiteDetailInfoVo;
import com.chargerlinkcar.framework.common.domain.SiteSocLimitDto;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE,
    fallbackFactory = ReactorSiteDataCoreFeignHystrix.class)
public interface ReactorSiteDataCoreFeignClient {

    @PostMapping(value = "/dataCore/cec/siteList")
    Mono<ListResponse<PartnerSiteVo>> cecSiteList(@RequestBody ListCecSiteParam param);

    @GetMapping(value = "/dataCore/cec/siteDetail")
    Mono<ObjectResponse<PartnerSiteDetailInfoVo>> cecSiteDetail(
        @RequestParam("siteId") String siteId);

//    @GetMapping(value = "/dataCore/cec/sitePlugStatusStats")
//    Mono<ObjectResponse<PlugStatusCountDto>> cecSitePlugStatusStats(
//            @RequestParam("siteId") String siteId);

    @GetMapping(value = "/dataCore/site/orderStats")
    Mono<ListResponse<SiteOrderStatsVo>> siteOrderStats(
        @RequestParam("siteId") String siteId);

    @PostMapping(value = "/dataCore/ces/getDistrictByList")
    Mono<ListResponse<DistrictPo>> getDistrictByList(@RequestBody List<String> code);

    @GetMapping("/dataCore/moveCorp/move")
    Mono<ObjectResponse<Boolean>> moveCorp(
        @RequestParam(value = "corpId") Long corpId,
        @RequestParam(value = "commId") Long commId);

    /**
     * 获取客户充电统计信息
     *
     * @param param
     * @return
     */
    @PostMapping("/dataCore/orderData/getCusOrderBiList")
    Mono<ListResponse<CusOrderBiDto>> getCusOrderBiList(@RequestBody ListCusOrderBiParam param);

    /**
     * 获取用户最后一次充电场站信息
     *
     * @param param
     * @return
     */
    @PostMapping("/dataCore/orderData/getCusOrderLastSiteInfoList")
    Mono<ListResponse<CusLastOrderSiteDto>> getCusOrderLastSiteInfoList(
        @RequestBody ListCusOrderBiParam param);

    @PostMapping(value = "/dataCore/orderData/getUnliquidatedNum")
    Mono<ListResponse<UnliquidatedOrderVo>> getUnliquidatedNum(
        @RequestBody ListCusOrderBiParam param);

    /**
     * 获取场站充电限制状态详情
     *
     * @param siteId
     * @return
     */
    @GetMapping(value = "/dataCore/site/getSocLimitInfo")
    Mono<ObjectResponse<SiteSocLimitDto>> getSocLimitInfo(
        @RequestParam(value = "siteId") String siteId);

    /**
     * 修改场站充电限制状态详情
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/site/updateSocLimitInfo")
    Mono<ObjectResponse<Boolean>> updateSocLimitInfo(@RequestBody SiteSocLimitDto param);


    @PostMapping("/dataCore/site/getSiteList")
    Mono<ListResponse<Site>> getSiteList(
        @RequestParam(value = "commId", required = false) Long commId,
        @RequestParam(value = "cityCode", required = false) String cityCode,
        @RequestParam(value = "siteIdList", required = false) List<String> siteIdList,
        @RequestParam(value = "start", defaultValue = "0") long start,
        @RequestParam(value = "size", defaultValue = "5") int size);

    @GetMapping("/dataCore/contract/getContractBySiteId")
    Mono<ListResponse<ContractVo>> getContractBySiteId(
        @RequestParam(value = "siteId") String siteId,
        @RequestParam(value = "size", required = false) Long size);

    @GetMapping("/dataCore/contract/getSiteListByContractId")
    Mono<ListResponse<SiteVo>> getSiteListByContractId(
        @RequestParam(value = "contractId") Long contractId);


    @PostMapping("/dataCore/site/getSiteTinyList")
    Mono<ListResponse<SiteTinyDto>> getSiteTinyList(@RequestBody ListSiteParam param);


    // 更新场站流程默认值配置项
    @PostMapping(value = "/dataCore/site/setting/updateOaDefaultValue")
    Mono<ObjectResponse<SitePo>> updateOaDefaultValue(
        @RequestBody UpdateSiteOaDefaultValueParam param);

    // 获取场站流程默认值配置项
    @GetMapping(value = "/dataCore/site/setting/fetchOaDefaultValue")
    Mono<ListResponse<SiteOaDefaultConfigPo>> fetchOaDefaultValue(
        @RequestParam("siteId") String siteId);

    // 获取场站指定流程默认值配置项
    @GetMapping(value = "/dataCore/site/setting/getOaDefaultValue")
    Mono<ObjectResponse<SiteOaDefaultConfigPo>> getOaDefaultValue(
        @RequestParam("siteId") String siteId,
        @RequestParam("procDefKey") String procDefKey);

    // 获取互联互通同步过来的价格模板
    @PostMapping(value = "/dataCore/priceTemp/getHlhtSitePriceTemplateList")
    Mono<ListResponse<PriceTemplatePo>> getHlhtSitePriceTemplateList(
        @RequestParam("siteId") String siteId);

    @PostMapping(value = "/dataCore/site/syncHlhtTemplate")
    Mono<BaseResponse> syncHlhtTemplate(
        @RequestBody CecQueryQeuipBusinessPolicyResult cecQueryQeuipBusinessPolicyResult);

    // 获取国家列表列表
    @PostMapping(value = "/dataCore/geo/findCountries")
    Mono<ListResponse<GeoCountriesVo>> findCountries(
        @RequestBody ListCountriesParam param);

    // 获取城市列表
    @PostMapping(value = "/dataCore/geo/findCities")
    Mono<ListResponse<GeoCitiesVo>> findCities(
        @RequestBody ListCitiesParam param);
}
