package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.service.order.ChargerService;
import com.cdz360.biz.ant.service.sysLog.SiteSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.chargerlinkcar.framework.common.domain.OrderReserveVo;
import com.chargerlinkcar.framework.common.domain.request.StartChargerRequest;
import com.chargerlinkcar.framework.common.domain.request.StopChargerRequest;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *  商户端充电开启结束
 * @since 2019/1/4 10:30
 */
@Slf4j
@RestController
@RequestMapping("/api/chargerorder")
public class ChargeSwitchRest extends BaseController{

    @Autowired
    private ChargerService chargerService;
    @Autowired
    private SiteSysLogService siteSysLogService;

    /**
     * 启动充电
     *
     * @param chargerRequest
     * @return
     */
    @Operation( summary = "启动充电")
    @RequestMapping(value = "/startCharger", method = RequestMethod.POST)
    public BaseResponse startCharger(ServerHttpRequest request,
                                     @RequestBody StartChargerRequest chargerRequest) {
        log.info("单枪开启充电 request:{}", JsonUtils.toJsonString(chargerRequest));
        IotAssert.isTrue(chargerRequest.getPlugNo() != null, "plugNo不能为空");
        Long topCommId = AntRestUtils.getTopCommId(request);
        chargerRequest.setTopCommId(topCommId);
//        String token = request.getHeader(Constant.CURRENT_USER_TOKEN);
        chargerRequest.setSysUserId(super.getUserIdLong2(request));
        BaseResponse resultEntity = chargerService.startBCharger(chargerRequest);
        siteSysLogService.listStartChargerLog(List.of(chargerRequest.getPlugNo()), request);
        return resultEntity;
    }

    /**
     * 结束充电 不走队列，直接下发停止
     *
     * @param stopRequest
     * @return
     */
    @Operation( summary = "结束充电 不走队列，直接下发停止")
    @RequestMapping(value = "/stopCharger", method = RequestMethod.POST)
    public BaseResponse stopCharger(ServerHttpRequest request,
                                    @RequestBody StopChargerRequest stopRequest) {
        log.info("结束充电 request:{}", JsonUtils.toJsonString(stopRequest));
        IotAssert.isNotNull(stopRequest.getPlugNo(), "枪头号不能为空");

        BaseResponse res = chargerService.stopBCharger(stopRequest);
        siteSysLogService.stopBChargerLog(List.of(stopRequest.getPlugNo()),
                request);
        return res;
    }


    /**
     * 批量开启充电
     *
     * @param chargerRequest
     * @return
     */
    @Operation( summary = "批量开启充电")
    @RequestMapping(value = "/listStartCharger", method = RequestMethod.POST)
    public BaseResponse listStartCharger(ServerHttpRequest request,
                                         @RequestBody StartChargerRequest chargerRequest) {
        log.info("批量开启充电 request:{}", JsonUtils.toJsonString(chargerRequest));
        IotAssert.isTrue(CollectionUtils.isNotEmpty(chargerRequest.getPlugNoList()), "枪号列表不能为空");
        Long topCommId = AntRestUtils.getTopCommId(request);
        chargerRequest.setTopCommId(topCommId);
//        String token = request.getHeader(Constant.CURRENT_USER_TOKEN);
        chargerRequest.setSysUserId(super.getUserIdLong2(request));
        BaseResponse res = chargerService.listStartCharger(chargerRequest);
        siteSysLogService.listStartChargerLog(chargerRequest.getPlugNoList(), request);
        return res;
    }


    /**
     * 批量结束充电
     *
     * @param stopRequest
     * @return
     */
    @Operation( summary = "批量结束充电")
    @RequestMapping(value = "/listStopCharger", method = RequestMethod.POST)
    public BaseResponse listStopCharger(ServerHttpRequest request,
                                    @RequestBody List<StopChargerRequest> stopRequest) {
        log.info("批量结束充电 request:{}", JsonUtils.toJsonString(stopRequest));
        if (CollectionUtils.isEmpty(stopRequest)) {
            throw new DcArgumentException("枪头号不能为空");
        }
        BaseResponse res = chargerService.listStopBCharger(stopRequest);
        siteSysLogService.stopBChargerLog(stopRequest.stream().map(StopChargerRequest::getPlugNo).collect(Collectors.toList()),
                request);
        return res;
    }

    @Operation( summary = "获取云端充电请求队列下发情况")
    @PostMapping("/getReserveInfoByPlugNoList")
    public ObjectResponse<OrderReserveVo> getReserveInfoByPlugNoList(ServerHttpRequest request,
                                                                     @RequestBody List<String> plugNoList) {
        IotAssert.isTrue(CollectionUtils.isNotEmpty(plugNoList), "plugNoList不能为空");
        return chargerService.getReserveInfoByPlugNoList(plugNoList);
    }


}
