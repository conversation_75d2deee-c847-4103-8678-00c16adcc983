package com.cdz360.biz.ant.domain.dto;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 储能设备 -- EMS
 */
@Data
@Accessors(chain = true)
public class Ems {

    // 系统SN
    @JsonProperty("ss")
    private String systemSn;
    // EMS硬件版本号 For example: data=120, V1.20
    @JsonProperty("hvn")
    private String hardwareVersionNum;
    // EMS软件版本号 For example: data=10188, V1.01.88
    @JsonProperty("svn")
    private String softwareVersionNum;
    // EMS通讯协议版本号 For example: data=120, V1.20
    @JsonProperty("cpv")
    private String communicationProtocolVersion;
    // 储能内部耗电电量 0.1kWh
    @JsonProperty("ice")
    private BigDecimal internallyConsumesElec;
    // 储能内部耗电功率 0.1kWh
    @JsonProperty("icp")
    private BigDecimal internallyConsumesPower;
    // EMS状态
    @JsonProperty("s")
    private Integer status;
    // AC有功功率遥调 0.1kW
    @JsonProperty("aaprc")
    private BigDecimal acActivePowerRemoteControl;
    // DC功率遥调 0.1kW
    @JsonProperty("dprc")
    private BigDecimal dcPowerRemoteControl;
    // 最大充电电流 0.1kW
    @JsonProperty("mcc")
    private BigDecimal maxChargingCurrent;
    // 最大放电电流 0.1kW
    @JsonProperty("mdc")
    private BigDecimal maxDischargeCurrent;
    // 远程调度功率 0.1kW
    @JsonProperty("rdp")
    private BigDecimal remoteDispatchingPower;
    // 强制开机标记
    @JsonProperty("fsm")
    private Integer forcedStartMark;
    // 强制开机计数
    @JsonProperty("fbc")
    private Integer forcedBootCount;
    // 遥控开关机标记
    @JsonProperty("rsmm")
    private Integer remoteSwitchMachineMarking;
    // 遥控并离网标记
    @JsonProperty("rcaogm")
    private Integer remoteControlAndOffGridMarking;
    // BMS最大允许充电功率
    @JsonProperty("macp")
    private Integer maxAllowableChargingPower;
    // BMS最大允许放电功率
    @JsonProperty("madp")
    private Integer maxAllowableDischargePower;
    // 最大允许馈网功率 0.1kW
    @JsonProperty("mafnp")
    private BigDecimal maxAllowableFeedNetworkPower;
    // 充电限制功率 0.1kW
    @JsonProperty("cl")
    private BigDecimal chargingLimit;
    // 放电限制功率 0.1kW
    @JsonProperty("dlp")
    private BigDecimal dischargeLimitingPower;
    // PCS允许最大充电功率 0.1kW
    @JsonProperty("pafmcp")
    private BigDecimal pcsAllowsForMaxChargingPower;
    // PCS允许最大放电功率 0.1kW
    @JsonProperty("pafmdp")
    private BigDecimal pcsAllowsForMaxDischargePower;
    // 电网关口限制取电功率 0.1kW
    @JsonProperty("galtaopt")
    private BigDecimal gridAccessLimitsTheAmountOfPowerTaken;
    // AC有功功率遥调限制值 0.1kW
    @JsonProperty("aaprcl")
    private BigDecimal acActivePowerRemoteControlLimit;
    // PV INV有功功率遥调
    @JsonProperty("piapra")
    private Integer pvInvActivePowerRemoteAdjustment;
    // PV INV看到的负载大小 0.1kW
    @JsonProperty("pistls")
    private BigDecimal pvInvSeeTheLoadSize;
    // PV INV装机总功率 0.1kW
    @JsonProperty("piitp")
    private BigDecimal pvInvInstalledTotalPower;
    // 削峰期望值 0.1kW
    @JsonProperty("pce")
    private BigDecimal peakClippingExpectation;

    // EMS调节步长(预留)
//    private String emsAdjustmentStepSize;
    //  PCS最小调节量(预留)
//    private String miniAdjustmentOfPcs;
    //  预留开机电流(预留)
//    private String reservedStartingCurrent;
    //  系统自耗(预留)
//    private String systemSinceTheConsumption;

    // EMS控制参数27
    @JsonProperty("ecp27")
    private Integer emsControlParameter27;
    // EMS 通讯状态(多设备按位解析)
    @JsonProperty("ecs")
    private Integer emsCommunicationStatus;
    // PCS 通讯状态(多设备按位解析)
    @JsonProperty("pcs")
    private Integer pcsCommunicationStatus;
    // STS 通讯状态(多设备按位解析)
    @JsonProperty("scs")
    private Integer stsCommunicationStatus;
    // 集控通讯状态(多设备按位解析)
    @JsonProperty("cccs")
    private Integer centralControlCommunicationStatus;
    // BMS通讯状态(多设备按位解析)
    @JsonProperty("bcs")
    private Integer bmsCommunicationStatus;
    // 光伏逆变器通讯状态(多设备按位解析)
    @JsonProperty("pics")
    private Integer pvInvCommunicationStatus;
    // 光伏汇流箱通讯状态(多设备按位解析)
    @JsonProperty("pjbcs")
    private Integer pvJunctionBoxCommunicationStatus;
    // 直流充电桩通讯状态(多设备按位解析)
    @JsonProperty("dcpcs")
    private Integer dcChargingPileCommunicationStatus;
    // 交流充电桩通讯状态(多设备按位解析)
    @JsonProperty("acpcs")
    private Integer acChargingPileCommunicationStatus;
    // 总电网关口电表通讯状态(多设备按位解析)
    @JsonProperty("gmcs")
    private Integer gridMeterCommunicationStatus;
    // 储能并网点电表通讯状态(多设备按位解析)
    @JsonProperty("esgmcs")
    private Integer energyStorageGridMeterCommunicationStatus;
    // 光伏并网点电表通讯状态(多设备按位解析)
    @JsonProperty("pigmcs")
    private Integer pvInvGridMeterCommunicationStatus;
    // 充电桩计量表通讯状态(多设备按位解析)
    @JsonProperty("cpmcs")
    private Integer chargingPileMeterCommunicationStatus;
    // 风能并网点电表通讯状态(多设备按位解析)
    @JsonProperty("wcgmcs")
    private Integer windCombinedGridMeterCommunicationStatus;
    // 负载用电电表通讯状态(多设备按位解析)
    @JsonProperty("lmcs")
    private Integer loadMeterCommunicationStatus;
    // ACDC通讯状态(多设备按位解析)
    @JsonProperty("acs")
    private Integer acdcCommunicationStatus;
    // DCDC通讯状态(多设备按位解析)
    @JsonProperty("dcs")
    private Integer dcdcCommunicationStatus;
    // 空调通讯状态(多设备按位解析)
    @JsonProperty("accs")
    private Integer airConditionCommunicationStatus;
    // 消防通讯状态(多设备按位解析)
    @JsonProperty("ffcs")
    private Integer fireFightingCommunicationStatus;
    // UPS通讯状态(多设备按位解析)
    @JsonProperty("ucs")
    private Integer upsCommunicationStatus;
    // 柴油机通讯状态(多设备按位解析)
    @JsonProperty("decs")
    private Integer dieselEngineCommunicationStatus;
    // DI开关量状态
    @JsonProperty("dsqs")
    private Long diSwitchQuantityState;
    // 储能内部电表通讯状态
    @JsonProperty("imcs")
    private Integer internalMeterCommunicationStatus;
    // 高压侧电表通讯状态
    @JsonProperty("hvsmcs")
    private Integer highVoltageSideMeterCommunicationStatus;
    // 电池装机容量 0.1kW
    @JsonProperty("ibc")
    private BigDecimal installedBatteryCapacity;
    // EMS型号
    @JsonProperty("em")
    private Integer emsModel;
    // 系统型号
    @JsonProperty("sm")
    private Integer systemModel;
    // 电池型号
    @JsonProperty("bm")
    private Integer batteryModel;
    // 空调型号
    @JsonProperty("acm")
    private Integer airConditionModel;
    // PV汇流箱型号
    @JsonProperty("pjbm")
    private Integer pvJunctionBoxModel;
    // PV
    @JsonProperty("pim")
    private Integer pvInvModel;
    // PCS
    @JsonProperty("pm")
    private Integer pcsModel;
    // TopBmu
    @JsonProperty("tbm")
    private Integer topBmuModel;
    // BMU
    @JsonProperty("bmuM")
    private Integer bmuModel;
    // ISO
    @JsonProperty("im")
    private Integer isoModel;
    // LMU
    @JsonProperty("lm")
    private Integer lmuModel;
    // EMS软件版本
    @JsonProperty("esv")
    private Integer emsSoftwareVer;
    // TopBMU软件版本
    @JsonProperty("tbsv")
    private Integer topBmuSoftwareVer;
    // BMU软件版本
    @JsonProperty("bsv")
    private Integer bmuSoftwareVer;
    // ISO软件版本
    @JsonProperty("isv")
    private Integer iosSoftwareVer;
    // LMU软件版本
    @JsonProperty("lsv")
    private Integer lmuSoftwareVer;
    // AC软件版本
    @JsonProperty("asv")
    private Integer acSoftwareVer;
    // DC软件版本
    @JsonProperty("dsv")
    private Integer dcSoftwareVer;
    // STS软件版本
    @JsonProperty("ssv")
    private Integer stsSoftwareVer;
    // 当天储能交流充电电量（参考） 0.1kW
    @JsonProperty("dcetap")
    private BigDecimal dailyChargedEnergyThroughAcPort;
    // 当天储能交流放电电量（参考） 0.1kW
    @JsonProperty("ddetap")
    private BigDecimal dailyDischargedEnergyThroughAcPort;
    // 电网关口电表入户总能量 0.01kwh
    @JsonProperty("ggmteith")
    private BigDecimal gridGatewayMeterTotalEnergyIntoTheHome;
    // 电网关口电表出户总能量 0.01kwh
    @JsonProperty("ggmteoth")
    private BigDecimal gridGatewayMeterTotalEnergyOutTheHome;
    // 高压侧电表入户总能量 0.01kwh
    @JsonProperty("teothvsm")
    private BigDecimal totalEnergyOfTheHighVoltageSideMeter;
    // 高压侧电表出户总能量 0.01kwh
    @JsonProperty("tteothvsm")
    private BigDecimal theTotalEnergyOfTheHighVoltageSideMeter;
    // 光伏并网点电表入户总能量 0.01kwh
    @JsonProperty("teopgmith")
    private BigDecimal totalEnergyOfPhotovoltaicGridMeterIntoTheHome;
    // 充电桩计量表入户总能量 0.01kwh
    @JsonProperty("tcpiutmtteith")
    private BigDecimal theChargingPileIsUsedToMeasureTheTotalEnergyIntoTheHome;
    // 风能并网点电表入户总能量 0.01kwh
    @JsonProperty("teowecgmith")
    private BigDecimal totalEnergyOfWindEnergyCombinedGridMeterIntoTheHome;
    // 负载用电电表入户总能量 0.01kwh
    @JsonProperty("lmteith")
    private BigDecimal loadMeterTotalEnergyIntoTheHome;
    // 储能并网点充电总能量 0.1kwh
    @JsonProperty("tteoescwcp")
    private BigDecimal theTotalEnergyOfEnergyStorageCombinedWithChargingPoints;
    // 储能并网点放电总能量 0.1kwh
    @JsonProperty("ttdeoesj")
    private BigDecimal theTotalDischargeEnergyOfEnergyStorageJunction;
    // 储能PCS DC侧充电总能量 0.1kwh
    @JsonProperty("dstce")
    private BigDecimal dcSideTotalChargeEnergy;
    // 储能PCS DC侧放电总能量 0.1kwh
    @JsonProperty("dstde")
    private BigDecimal dcSideTotalDischargeEnergy;
    // 储能PCS DC侧光伏发电总能量 0.1kwh
    @JsonProperty("espdsppgte")
    private BigDecimal energyStoragePcsDcSidePhotovoltaicPowerGenerationTotalEnergy;
    // 油机发电量 0.1kwh
    @JsonProperty("tomce")
    private BigDecimal theOilMachineConsumesElectricity;
    // 电网关口电表总有功功率 0.1kW
    @JsonProperty("tpogm")
    private BigDecimal totalPowerOfGatewayMeter;
    // 高压侧电表总有功功率 0.1kW
    @JsonProperty("tpohvsm")
    private BigDecimal totalPowerOfHighVoltageSideMeter;
    // 光伏并网点电表总有功功率 0.1kW
    @JsonProperty("tpopgm")
    private BigDecimal totalPowerOfPhotovoltaicGridMeter;
    // 充电桩计量表总有功功率 0.1kW
    @JsonProperty("tpocpm")
    private BigDecimal totalPowerOfChargingPileMeter;
    // 风能并网点电表总有功功率 0.1kW
    @JsonProperty("tpocgm")
    private BigDecimal totalPowerOfCombinedGridMeters;
    // 负载用电电表总有功功率 0.1kW
    @JsonProperty("ltpotm")
    private BigDecimal loadTotalPowerOfTheMeter;
    // 储能并网点总有功功率 0.1kW
    @JsonProperty("tapoesj")
    private BigDecimal totalActivePowerOfEnergyStorageJunction;
    // 储能PCS DC侧总有功功率 0.1kW
    @JsonProperty("tpospds")
    private BigDecimal totalPowerOfStoredPcsDcSide;
    // 储能PCS DC侧光伏发电总有功功率 0.1kW
    @JsonProperty("tppgcospds")
    private BigDecimal totalPhotovoltaicPowerGenerationCapacityOfStoredPcsDcSide;
    // 油机发电总有功功率 0.1kW
    @JsonProperty("tpgbom")
    private BigDecimal totalPowerGeneratedByOilMachine;
    // EMS计算得到负载有功功率 0.1kW
    @JsonProperty("tlpicbe")
    private BigDecimal theLoadPowerIsCalculatedByEms;
    // 电池剩余电量（SOC） 0.1%
    @JsonProperty("soc")
    private BigDecimal soc;
    // EMS报错信息列表
    @JsonProperty("efil")
    private List<Integer> emsFaultInformationList;
//    // EMS报错信息1
//    @JsonProperty("efi1")
//    private Integer emsFaultInformation1;
//    // EMS报错信息2
//    @JsonProperty("efi2")
//    private Integer emsFaultInformation2;
//    // EMS报错信息3
//    @JsonProperty("efi3")
//    private Integer emsFaultInformation3;
//    // EMS报错信息4
//    @JsonProperty("efi4")
//    private Integer emsFaultInformation4;
    // BMS报错信息列表
    @JsonProperty("bfil")
    private List<Integer> bmsFaultInformationList;
//    // BMS报错信息1
//    @JsonProperty("bfi1")
//    private Integer bmsFaultInformation1;
//    // BMS报错信息2
//    @JsonProperty("bfi2")
//    private Integer bmsFaultInformation2;
//    // BMS报错信息3
//    @JsonProperty("bfi3")
//    private Integer bmsFaultInformation3;
//    // BMS报错信息4
//    @JsonProperty("bfi4")
//    private Integer bmsFaultInformation4;
    // STS报错信息列表
    @JsonProperty("sfil")
    private List<Integer> stsFaultInformationList;
//    // STS报错信息1
//    @JsonProperty("sfi1")
//    private Integer stsFaultInformation1;
//    // STS报错信息2
//    @JsonProperty("sfi2")
//    private Integer stsFaultInformation2;
    // DCAC模块故障状态
    @JsonProperty("dms")
    private List<Integer> dcacModuleStatus;
    // DCAC其他故障
    @JsonProperty("of")
    private List<Integer> otherFault;
    // DCAC故障信息1
    @JsonProperty("dafml")
    private List<Integer> dcacFaultMessageList;
//    // DCAC故障信息1
//    @JsonProperty("dafm1")
//    private Integer dcacFaultMessage1;
//    // DCAC故障信息2
//    @JsonProperty("dafm2")
//    private Integer dcacFaultMessage2;
//    // DCAC故障信息3
//    @JsonProperty("dafm3")
//    private Integer dcacFaultMessage3;
    // DCDC模块故障状态
    @JsonProperty("edm")
    private List<Integer> errDcModule;
    // DCDC模块故障状态列表
    @JsonProperty("ddfml")
    private List<Integer> dcdcFaultMessageList;
//    // DCDC模块故障状态1
//    @JsonProperty("ddfm1")
//    private Integer dcdcFaultMessage1;
//    // DCDC模块故障状态2
//    @JsonProperty("ddfm2")
//    private Integer dcdcFaultMessage2;
//    // DCDC模块故障状态3
//    @JsonProperty("ddfm3")
//    private Integer dcdcFaultMessage3;
    // 空调故障信息列表
    @JsonProperty("acfil")
    private List<Integer> airConditioningFaultInformationList;
//    // 空调故障信息1
//    @JsonProperty("acfi1")
//    private Integer airConditioningFaultInformation1;
//    // 空调故障信息2
//    @JsonProperty("acfi2")
//    private Integer airConditioningFaultInformation2;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

    public static List<Boolean> parseCommunicationStatus(Integer communicationStatus) {
        List<Boolean> res = new ArrayList<>();
        if (communicationStatus == null) {
            return res;
        }

        byte bitIdx = 0;
        while (bitIdx < 32) {
            res.add((communicationStatus & (1 << bitIdx)) > 0);
            bitIdx++;
        }
        return res;
    }

}
