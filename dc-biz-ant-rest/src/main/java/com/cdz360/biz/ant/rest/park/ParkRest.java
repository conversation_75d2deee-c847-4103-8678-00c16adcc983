package com.cdz360.biz.ant.rest.park;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.domain.park.vo.ParkChannelVo;
import com.cdz360.biz.ant.feign.reactor.ParkBizFeignClient;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "场站停车相关接口", description = "场站停车相关接口")
@Slf4j
@RestController
public class ParkRest {

    @Autowired
    private ParkBizFeignClient parkBizFeignClient;

    @Operation(summary = "获取场站道闸信息列表")
    @GetMapping(value = "/api/parkChannel/getParkChannelBySiteId")
    public Mono<ListResponse<ParkChannelVo>> getParkChannelBySiteId(
            ServerHttpRequest request,
            @Parameter(name = "场站ID", required = true)
            @RequestParam(value = "siteId") String siteId) {
        log.info("获取场站道闸信息列表: {}", LoggerHelper2.formatEnterLog(request));

        if (StringUtils.isBlank(siteId)) {
            throw new DcArgumentException("场站ID无效");
        }

        return parkBizFeignClient.getParkChannelBySiteId(siteId);
    }

    @Operation(summary = "开闸操作")
    @GetMapping(value = "/api/parkChannel/upLiftRod")
    public Mono<ObjectResponse<Boolean>> upLiftRod(
            ServerHttpRequest request,
            @Parameter(name = "道闸ID(自己系统内ID值)", required = true)
            @RequestParam(value = "id") Long id) {
        log.info("开闸操作: {}", LoggerHelper2.formatEnterLog(request));

        if (null == id) {
            throw new DcArgumentException("道闸ID无效");
        }

        return parkBizFeignClient.upLiftRod(id);
    }
}
