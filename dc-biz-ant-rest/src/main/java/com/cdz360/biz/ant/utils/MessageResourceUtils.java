package com.cdz360.biz.ant.utils;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.springframework.context.MessageSource;
import org.springframework.lang.Nullable;
import org.springframework.web.server.ServerWebExchange;

public class MessageResourceUtils {

    // 错误消息模式与国际化键的映射
    private static final Map<String, String> MESSAGE_TO_I18N_KEY = new HashMap<>();
    // 错误消息模式与正则表达式的映射
    private static final Map<String, Pattern> MESSAGE_TO_PATTERN = new HashMap<>();

    static {
        // 初始化映射关系
        // 带占位符的特殊处理
        addMapping("新增客户引流记录失败。customerAttractPo: \\S+",
            "user.add.attractRecordFailed", "customerAttractPo: (\\S+)");
        addMapping("新增客户引流记录失败。customerId: \\S+, error: \\S+",
            "user.add.attractRecordFailed.msg", "customerId: (\\S+), error: (\\S+)");
        addMapping("查无该充值记录: orderId=\\S+", "recharge.order.notFound", "orderId=(\\S+)");
        addMapping("该充值记录没有关联的资金块信息: orderId=\\S+",
            "recharge.order.noFundingBlock", "orderId=(\\S+)");
        addMapping("充值对应的资金块明细seq不存在: orderId=\\S+", "recharge.order.seqNotFound",
            "orderId=(\\S+)");
        addMapping("获取失败!失败原因:\\S+", "failed.msg", "失败原因:(\\S+)");
        addMapping("当前已存在\\d+个下载任务，请稍后再试", "download.task.maxAmount",
            "当前已存在(\\d+)个下载任务，请稍后再试");
        addMapping("请分开查询 \\S+ 前的数据", "search.time.separate",
            "请分开查询 (\\S+) 前的数据");
        addMapping("暂不支持查询 \\S+ 前的数据", "search.time.notSupport",
            "暂不支持查询 (\\S+) 前的数据");
        addMapping("无法找到该场站. 场站ID =\\S+", "site.notFound", "场站ID =(\\S+)");
        addMapping("场站 id=\\S+默认配置记录不存在", "site.conf.notFound",
            "场站 id=(\\S+)默认配置记录不存在");
        addMapping("场站默认配置数据修改失败，主键：\\S+", "site.conf.updateFailed",
            "主键：(\\S+)");
        addMapping("找不到场站信息: \\S+", "site.info.notFound", "找不到场站信息: (\\S+)");
        addMapping("存在重复的桩编号: \\S+", "pile.duplicate", "存在重复的桩编号: (\\S+)");
        addMapping("找不到桩: \\S+", "pile.notFound", "找不到桩: (\\S+)");
        addMapping("SIM卡已绑定，桩：\\S+", "sim.isBounded", "桩：(\\S+)");
        addMapping("SIM卡已绑定，桩：\\S+, 场站：\\S+", "sim.site.isBounded",
            "桩：(\\S+), 场站：(\\S+)");
        addMapping("无法找到该设备型号. 设备型号 =\\S+", "device.model.notFound",
            "设备型号 =(\\S+)");
        addMapping("根据siteId=\\S+查询场站默认配置,无记录", "site.conf.noRecord",
            "根据siteId=(\\S+)查询场站默认配置,无记录");
        addMapping("根据siteId=\\S+查询场站默认配置结果不唯一", "site.conf.repeat",
            "根据siteId=(\\S+)查询场站默认配置结果不唯一");
        addMapping("找不到任务: \\S+", "download.task.notFound", "找不到任务: (\\S+)");
        addMapping("升级失败，场站\\S+下，找不到桩[\\S+]", "site.pile.notFound",
            "场站(\\S+)下，找不到桩[(\\S+)]");
        addMapping("无分时计费模板信息: \\S+", "order.timeDivision.notFound",
            "无分时计费模板信息: (\\S+)");
        addMapping("找不到升级记录id: \\S+", "site.pile.upgrade.taskIdNotFound",
            "找不到升级记录id: (\\S+)");
        addMapping("找不到升级记录链接信息: \\S+", "site.pile.upgrade.taskUrlNotFound",
            "找不到升级记录链接信息: (\\S+)");
        addMapping("发现重复的桩编号，请检查。桩号: \\S+", "site.pile.upgrade.duplicatePile",
            "发现重复的桩编号，请检查。桩号: (\\S+)");
        addMapping("升级记录中找不到这些桩: \\S+", "site.pile.upgrade.pileRecordsNotFound",
            "升级记录中找不到这些桩: (\\S+)");
        addMapping("升级状态不正确，可能发生变化，请刷新页面后再试。桩号：\\S+", "site.pile.upgrade.statusRefreshError",
            "升级状态不正确，可能发生变化，请刷新页面后再试。桩号： (\\S+)");
    }

    private static void addMapping(String patternStr, String i18nKey, String regexStr) {
        MESSAGE_TO_I18N_KEY.put(patternStr, i18nKey);
        MESSAGE_TO_PATTERN.put(patternStr, Pattern.compile(regexStr));
    }

    private static String getI18nKey(String error) {
        for (Map.Entry<String, String> entry : MESSAGE_TO_I18N_KEY.entrySet()) {
            if (error.matches(entry.getKey())) {
                return entry.getValue();
            }
        }
        return null;
    }

    private static Pattern getPattern(String error) {
        for (Map.Entry<String, Pattern> entry : MESSAGE_TO_PATTERN.entrySet()) {
            if (error.matches(entry.getKey())) {
                return entry.getValue();
            }
        }
        return null;
    }

    public static String codeConvert(
        MessageSource messageSource, ServerWebExchange exh, String code) {
        Locale locale = AntRestUtils.getLocale(exh);
        return null == locale ? code : changeMsgToI18nMsg(messageSource, code, locale);
    }

    public static String codeConvert(
        MessageSource messageSource, ServerWebExchange exh, String code, @Nullable Object[] args) {
        if (null == args) {
            return codeConvert(messageSource, exh, code);
        }
        Locale locale = AntRestUtils.getLocale(exh);
        return null == locale ? code
            : messageSource.getMessage(code, args, AntRestUtils.getLocale(exh));
    }

    /**
     * msg转成海外版msg，带占位符的特殊处理
     *
     * @param messageSource
     * @param code
     * @param locale
     * @return
     */
    private static String changeMsgToI18nMsg(MessageSource messageSource, String code,
        Locale locale) {
        // 获取对应的国际化键和正则表达式
        String i18nKey = getI18nKey(code);
        Pattern pattern = getPattern(code);

        if (i18nKey != null && pattern != null) {
            Matcher matcher = pattern.matcher(code);
            if (matcher.find()) {
                // 提取占位符值
                int groupCount = matcher.groupCount();
                Object[] placeholders = new Object[groupCount];
                for (int i = 0; i < groupCount; i++) {
                    placeholders[i] = matcher.group(i + 1);
                }
                return messageSource.getMessage(i18nKey, placeholders, locale);
            }
        }
        return messageSource.getMessage(code, null, locale);

    }
}
