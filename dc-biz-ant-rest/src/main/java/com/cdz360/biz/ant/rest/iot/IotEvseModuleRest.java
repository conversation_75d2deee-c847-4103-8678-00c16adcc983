package com.cdz360.biz.ant.rest.iot;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.service.iot.IotEvseModuleService;
import com.cdz360.biz.model.trading.iot.dto.EvseModuleDto;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "充电模块相关接口", description = "充电模块")
public class IotEvseModuleRest {

    @Autowired
    private IotEvseModuleService moduleService;

    @GetMapping("/api/evseModule/list")
    public Mono<ObjectResponse<EvseModuleDto>> getEvseModuleList(@RequestParam("evseNo") String evseNo) {
        log.info("getEvseModuleList. evseNo: {}", evseNo);
        return moduleService.getEvseModuleList(evseNo);
    }

}
