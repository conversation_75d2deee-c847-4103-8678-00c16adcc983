package com.cdz360.biz.ant.service.sysLog;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.vo.KvAny;
import com.cdz360.base.model.base.vo.KvObject;
import com.cdz360.biz.auth.user.type.LogOpType;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.feign.CommercialFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 商户相关系统操作日志
 */
@Slf4j
@Service
public class CommercialSysLogService {
    @Autowired
    private CommercialFeignClient commercialFeignClient;
    @Autowired
    private BaseSysLogService sysUserLogService;

    /**
     * 新增商户会员日志
     */
    public void initMerchantBalanceLog(String phone, Long commId, ServerHttpRequest request) {
        ObjectResponse<Commercial> commercialObjectResponse = commercialFeignClient.getCommercial(commId);
        FeignResponseValidate.check(commercialObjectResponse);
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                List.of(KvAny.of("会员手机号", phone),
                        KvAny.of("所属商户", commercialObjectResponse.getData().getCommName())),
                request);
    }

    /**
     * 商户会员停用、启用日志
     */
    public void disableOrEnableLog(String phone, Long commId, ServerHttpRequest request) {
        ObjectResponse<Commercial> commercialObjectResponse = commercialFeignClient.getCommercial(commId);
        FeignResponseValidate.check(commercialObjectResponse);
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY_STATUS,
                List.of(KvAny.of("会员手机号", phone),
                        KvAny.of("所属商户", commercialObjectResponse.getData().getCommName())),
                request);
    }


    /**
     * 直付商家日志
     */
    public void addZftLog(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                KvAny.of("直付商家名", name),
                request);
    }

    public void updateZftLog(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("直付商家名", name),
                request);
    }

    /**
     * 账号管理/新增子账号
     */
    public void commUserAddLog(String account, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                KvAny.of("账号", account),
                request);
    }

    /**
     * 异常订单修改
     * 日志
     */
    public void updateOrderLog(String orderNo, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("充电订单号", orderNo),
                request);
    }

    /**
     * 新增券模板
     * 日志
     */
    public void couponDictCreateLog(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                KvAny.of("券模板名", name),
                request);
    }

    /**
     * 作废券模板
     * 日志
     */
    public void couponDictDisableLog(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY_STATUS,
                KvAny.of("券模板名", name),
                request);
    }

    /**
     * 创建活动
     * 日志
     */
    public void activityCreateLog(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                KvAny.of("活动名", name),
                request);
    }

    /**
     * 编辑活动
     * 日志
     */
    public void activityUpdateLog(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("活动名", name),
                request);
    }

    /**
     * 停用/启用活动
     * 日志
     */
    public void activitySwitchoverLog(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY_STATUS,
                KvAny.of("活动名", name),
                request);
    }

    /**
     * 在移动段显示/隐藏活动
     * 日志
     */
    public void activitySwitchShowInMobileLog(String name, String showOrHide, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY_STATUS,
            List.of(KvAny.of("活动名", name),
                KvAny.of("是否显示在移动端", showOrHide)),
                request);
    }

    /**
     * 充值管理-财务/编辑
     * 日志
     */
    public void updateByOrderIdLog(String orderId, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("充值订单号", orderId),
                request);
    }


    /**
     * 导出至开票系统
     * 日志
     */
    public void exportToInvoiceLog(List<Long> userIdList, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.REVIEW,
                List.of(new KvObject("用户编号", userIdList)),
                request);
    }

    /**
     * 设置发票状态为未通过
     * 日志
     */
    public void changeSomeStatusLog(List<Long> userIdList, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.REVIEW,
                List.of(new KvObject("用户编号", userIdList)),
                request);
    }

    /**
     * 开票控制
     * 日志
     */
    public void saveMegConfigLog(ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("开票控制", ""),
                request);
    }

    /**
     * 新增开票主体信息
     * 日志
     */
    public void addInvoicedTempSalLog(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                KvAny.of("开票主体", name),
                request);
    }

    /**
     * 更新开票主体信息
     * 日志
     */
    public void updateInvoicedTempSalLog(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("开票主体", name),
                request);
    }

    /**
     * 更新商户会员等级
     * @param name
     * @param request
     */
    public void updateCommScoreLevelLog(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("商户会员等级", name),
                request);
    }

    /**
     * 更新商户充值状态
     * @param name
     * @param request
     */
    public void updateCommPayLog(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("商户充值开关", name),
                request);
    }

    /**
     * 删除开票主体信息
     * 日志
     */
    public void disableInvoicedTempSal(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.DISABLE,
                KvAny.of("开票主体", name),
                request);
    }

    /**
     * 设置场站能否开票
     * 日志
     */
    public void invoicedVaildLog(List<String> siteNameList, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.MODIFY_INVOICE,
                List.of(new KvObject("站点名称", siteNameList)),
                request);
    }

    /**
     * 新增合约
     * 日志
     */
    public void addContractLog(String contractName, String customerName, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                List.of(KvAny.of("合约名称", contractName),
                        KvAny.of("客户名称", customerName)),
                request);
    }

    /**
     * 删除合约日志
     */
    public void delContractLog(String contractName, String customerName, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.DISABLE,
                List.of(KvAny.of("合约名称", contractName),
                        KvAny.of("客户名称", customerName)),
                request);
    }

    /**
     * 编辑合约
     * 日志
     */
    public void editContractLog(String contractName, String customerName, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                List.of(KvAny.of("合约名称", contractName),
                        KvAny.of("客户名称", customerName)),
                request);
    }


    /**
     * 创建广告
     * 日志
     */
    public void adsCreateLog(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
            KvAny.of("广告标题", name),
            request);
    }

    /**
     * 编辑广告
     * 日志
     *
     */
    public void adsUpdateLog(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
            KvAny.of("广告标题", name),
            request);
    }

    /**
     * 停用/启用广告
     * 日志
     */
    public void adsSwitchoverLog(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY_STATUS,
            KvAny.of("广告标题", name),
            request);
    }
}
