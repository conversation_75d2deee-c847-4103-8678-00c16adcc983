package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.domain.request.UpgradeTaskRequest;
import com.cdz360.biz.model.evse.po.PackageInfoItem;
import com.cdz360.biz.model.iot.param.UpgradeTaskInfoRequest;
import com.cdz360.biz.model.iot.vo.UpgradeTaskDetailVo;
import com.cdz360.biz.model.iot.vo.UpgradeTaskInfoVo;
import com.cdz360.biz.model.iot.vo.UpgradeTaskVo;
import com.cdz360.biz.ant.feign.IotDeviceMgmFeignClient;
import com.cdz360.biz.ant.feign.SiteFeignClient;
import com.cdz360.biz.ant.service.sysLog.SiteSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.iot.param.UpgradeTaskDetailRequest;
import com.cdz360.biz.model.iot.param.UpgradeTaskListRequest;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequestMapping("/api/upgrade")
@RestController
@Tag(name = "桩升级记录、升级操作接口")
public class EvseUpgradeRest {

    @Autowired
    private SiteFeignClient siteFeignClient;

    @Autowired
    private SiteSysLogService siteSysLogService;

    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;

    @Operation(summary = "发起桩升级")
    @PostMapping("/startTask")
    public BaseResponse startTask(ServerHttpRequest request,
        @RequestBody UpgradeTaskRequest param) {
        log.info(">> 发起桩升级请求: {}", JsonUtils.toJsonString(param));
        IotAssert.isTrue(StringUtils.isNotBlank(param.getBundleName())
            && CollectionUtils.isNotEmpty(param.getEvseIds()), "缺少入参信息");
        BaseResponse res = siteFeignClient.startTask(AntRestUtils.getToken2(request), param);
        siteSysLogService.startTask(param.getBundleName(), param.getEvseIds(), request);// 记录日志
        return res;
    }

    @Operation(summary = "发起桩升级-海外版专属")
    @PostMapping("/commercial/startTask")
    public BaseResponse startEssTask(ServerHttpRequest request,
        @RequestBody UpgradeTaskRequest param) {
        log.info(">> 发起海外版桩升级请求: {}", JsonUtils.toJsonString(param));
        if (param.getTaskId() == null) {
            IotAssert.isTrue(StringUtils.isNotBlank(param.getBundleName())
                    && CollectionUtils.isNotEmpty(param.getEvseIds())
                    && CollectionUtils.isNotEmpty(param.getPackageInfo()),
                "缺少入参信息");
            List<PackageInfoItem> packageInfo = param.getPackageInfo().stream().filter(
                packageInfoItem -> StringUtils.isNotBlank(packageInfoItem.getFileName())
                    && StringUtils.isNotBlank(packageInfoItem.getUrl())).toList();
            IotAssert.isTrue(CollectionUtils.isNotEmpty(packageInfo), "缺少入参信息");
        } else {
            IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getEvseIds())
                    && param.getTaskId() > 0L,
                "缺少入参信息");
        }

        param.setOpId(AntRestUtils.getSysUid(request));
        param.setOpName(AntRestUtils.getSysUserName(request));
        BaseResponse res = iotDeviceMgmFeignClient.startEssTask(param);
        siteSysLogService.startTask(param.getBundleName(), param.getEvseIds(), request);// 记录日志
        return res;
    }

    /**
     * 分页获取 获取场站下升级记录列表
     *
     * @return
     */
    @ApiOperation(value = "分页获取 获取场站下升级记录列表")
    @PostMapping("/commercial/getUpgradeTaskListBySite")
    public ListResponse<UpgradeTaskVo> getEssUpgradeTaskListBySite(
        @RequestBody UpgradeTaskListRequest upgradeTaskListRequest) {
        log.info(">> 获取场站下升级记录列表: req={}", upgradeTaskListRequest);
        if (upgradeTaskListRequest != null && upgradeTaskListRequest.getTaskId() != null) {
            IotAssert.isTrue(upgradeTaskListRequest.getTaskId() != 0L, "请传入正确的任务id");
        }
        ListResponse<UpgradeTaskVo> ret = iotDeviceMgmFeignClient.getEssUpgradeTaskListBySite(
            upgradeTaskListRequest);
        log.info("<< {}", JsonUtils.toJsonString(ret));
        return ret;
    }

    /**
     * 获取升级记录详情列表，不分页
     *
     * @return
     */
    @ApiOperation(value = "获取升级记录详情列表，不分页")
    @PostMapping("/commercial/getUpgradeTaskDetailListByTaskId")
    public ListResponse<UpgradeTaskDetailVo> getUpgradeTaskDetailListByTaskId(
        @RequestBody UpgradeTaskDetailRequest upgradeTaskDetailRequest) {
        log.info(">> 获取升级记录详情列表: req = {}", upgradeTaskDetailRequest);
        IotAssert.isTrue(
            upgradeTaskDetailRequest != null && upgradeTaskDetailRequest.getTaskId() != null
                && upgradeTaskDetailRequest.getTaskId() != 0L, "请传入正确的任务id");
        ListResponse<UpgradeTaskDetailVo> list = iotDeviceMgmFeignClient.getUpgradeTaskDetailListByTaskId(
            upgradeTaskDetailRequest);
        log.info("<< {}", JsonUtils.toJsonString(list));
        return list;
    }

    @Operation(summary = "海外版获取升级详情信息")
    @PostMapping("/commercial/getUpgradeTaskInfo")
    public ObjectResponse<UpgradeTaskInfoVo> getEssUpgradeTaskInfo(
        @RequestBody UpgradeTaskInfoRequest upgradeTaskInfoRequest) {
        log.info(">> 海外版获取升级详情信息: req={}",
            JsonUtils.toJsonString(upgradeTaskInfoRequest));
        IotAssert.isTrue(
            upgradeTaskInfoRequest != null && upgradeTaskInfoRequest.getTaskId() != null
                && upgradeTaskInfoRequest.getTaskId() != 0L, "请传入正确的任务id");
        ObjectResponse<UpgradeTaskInfoVo> obj = iotDeviceMgmFeignClient
            .getEssUpgradeTaskInfo(upgradeTaskInfoRequest);
        log.info("<< {}", JsonUtils.toJsonString(obj));
        return obj;
    }
}
