package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.discount.param.UpdateCorpDiscountParam;
import com.cdz360.biz.model.cus.settlement.dto.SettlementDto;
import com.cdz360.biz.model.cus.settlement.param.ListSettlementParam;
import com.cdz360.biz.model.cus.settlement.param.UpdateCorpSettlementParam;
import com.cdz360.biz.model.cus.settlement.vo.CorpSettlementCfgVo;
import com.cdz360.biz.model.cus.settlement.vo.SettlementVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderBiVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_USER,
        fallbackFactory = CorpSettlementFeignHystrix.class)
public interface CorpSettlementFeignClient {
    /**
     * 通过计算配置ID获取企业客户结算信息
     *
     * @param cfgId
     * @return
     */
    @GetMapping(value = "/api/corp/getSettlementCfgById")
    ObjectResponse<CorpSettlementCfgVo> getSettlementCfgById(@RequestParam(value = "cfgId") Long cfgId);

    @GetMapping(value = "/api/corp/getSettlementCfg")
    ObjectResponse<CorpSettlementCfgVo> getSettlementCfg(@RequestParam(value = "corpId") Long corpId);

    @PostMapping(value = "/api/corp/updateSettlementCfg")
    ObjectResponse<CorpPo> updateSettlementCfg(
            @RequestBody UpdateCorpSettlementParam param);

    @PostMapping(value = "/api/corp/findSettlementList")
    ListResponse<SettlementVo> findSettlementList(@RequestBody ListSettlementParam param);

    @PostMapping(value = "/api/corp/settlementBiForCorp")
    ObjectResponse<OrderBiVo> settlementBiForCorp(@RequestBody ListSettlementParam param);

    @PostMapping(value = "/api/corp/removeOrder4Settlement")
    ObjectResponse<SettlementVo> removeOrder4Settlement(@RequestBody SettlementDto dto);

    @PostMapping(value = "/api/corp/appendOrder2Settlement")
    ObjectResponse<SettlementVo> appendOrder2Settlement(@RequestBody SettlementDto dto);

//    @PostMapping(value = "/api/corp/addSettlement")
//    ObjectResponse<Integer> addSettlement(@RequestBody SettlementDto dto);

    @PostMapping(value = "/api/corp/updateSettlement")
    ObjectResponse<Integer> updateSettlement(@RequestBody SettlementDto dto);

    @GetMapping(value = "/api/corp/getSettlementByBillNo")
    ObjectResponse<SettlementVo> getSettlementByBillNo(@RequestParam(value = "billNo") String billNo);

    @GetMapping(value = "/api/corp/removeSettlementByBillNo")
    ObjectResponse<Integer> removeSettlementByBillNo(@RequestParam(value = "billNo") String billNo);

    /**
     * 账单结算
     *
     * @param billNo
     * @return
     */
    @GetMapping(value = "/api/corp/settlementByBillNo")
    ObjectResponse<Integer> settlementByBillNo(@RequestParam(value = "billNo") String billNo);

    /**
     * 更新企业客户协议价配置
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/corp/updateDiscount")
    ObjectResponse<CorpPo> updateDiscount(@RequestBody UpdateCorpDiscountParam param);
}
