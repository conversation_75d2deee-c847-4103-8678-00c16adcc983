package com.cdz360.biz.ant.service.oa.process;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.oa.constant.OaConstants;
import com.cdz360.biz.model.trading.profit.sett.param.ListSettJobBillParam;
import com.cdz360.biz.model.trading.profit.sett.type.ProfitCfgCategory;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import com.cdz360.biz.oa.dto.ExpenseSiteFormDto;
import com.cdz360.biz.oa.dto.OaSettJobBillDto;
import com.cdz360.biz.oa.param.OaStartProcessParam;
import com.chargerlinkcar.framework.common.feign.SiteDataCoreFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class ExpenseSiteOaStartProcessStrategy extends AbstractStartProcessStrategy {

    @Autowired
    private SiteDataCoreFeignClient siteDataCoreFeignClient;

    private static final String PRO_DEF_KEY = OaConstants.PD_KEY_EXPENSE_SITE_OA;

    @PostConstruct
    public void init() {
        this.startProcessStrategyFactory.addStrategy(PRO_DEF_KEY, this);
    }

    @Override
    public Mono<Map<String, Object>> paramConversion(Map<String, Object> req) {
        return null;
    }

    @Override
    public Mono<OaStartProcessParam> validate(OaStartProcessParam param) {
        // 转换成对应的数据结构
        IotAssert.isNotNull(param.getData(), "表单数据不能为空");
        ExpenseSiteFormDto form = JsonUtils.fromJson(
            JsonUtils.toJsonString(param.getData()), ExpenseSiteFormDto.class);
        IotAssert.isNotNull(form, "表单数据不能为空");

        IotAssert.isNotBlank(form.getCustomerName(), "请填写分成客户名称");
        IotAssert.isNotBlank(form.getBankName(), "请填写开户行");
        IotAssert.isNotBlank(form.getBankAccount(), "请填写银行账号");
        IotAssert.isNotBlank(form.getPayCompanyName(), "请填写付款公司");

        // 结算单信息验证
        IotAssert.isNotNull(form.getSettBillSummary(), "请设置结算单信息");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(
            form.getSettBillSummary().getBillNoList()), "请选择结算单");
        IotAssert.isNotNull(form.getSettBillSummary().getActualFee(), "请设置实际结算信息");

        // 账期日期
        IotAssert.isNotNull(form.getBillDateRange(), "请设置账期日期");
        IotAssert.isNotNull(form.getBillDateRange().getStartTime(), "请设置账期日期开始时间");
        IotAssert.isNotNull(form.getBillDateRange().getEndTime(), "请设置账期日期结束时间");

        // 经办节点
        if (CollectionUtils.isNotEmpty(form.getHandleNodes())) {
        }

        // 填充必要信息
        IotAssert.isTrue(CollectionUtils.isNotEmpty(form.getSiteIdList()), "场站ID不能为空");
        ListSiteParam req = new ListSiteParam();
        req.setSiteIdList(form.getSiteIdList())
            .setSize(form.getSiteIdList().size())
            .setTotal(Boolean.FALSE);
        ListResponse<SiteVo> siteRes = siteDataCoreFeignClient.getSiteVoList(req);
        FeignResponseValidate.check(siteRes);

        List<SiteVo> siteVos = siteRes.getData();
        Map<String, String> siteNameMap = siteVos.stream()
            .collect(Collectors.toMap(SitePo::getId, SitePo::getSiteName));
        form.setSiteNameList( // 要按照原本的顺序排列
            form.getSiteIdList().stream().map(siteNameMap::get).filter(Objects::nonNull)
                .collect(Collectors.toList()));
        siteVos.stream().filter(e -> form.getSiteIdList().get(0).equals(e.getId())).findFirst()
            .ifPresent(x -> form.setSiteRemark(x.getRemark()));

        return settJobBillFeignClient.findSettJobBill(new ListSettJobBillParam()
                .setJobCategory(ProfitCfgCategory.EXPENSE)
                .setBillNoList(form.getSettBillSummary().getBillNoList()))
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .map(x -> {
                IotAssert.isTrue(
                    x.size() == form.getSettBillSummary().getBillNoList().size(),
                    "结算单存在无效记录，请调整已选结算单");

                form.getSettBillSummary().setSettJobBillList(x.stream().map(k -> {
                    OaSettJobBillDto tmp = new OaSettJobBillDto();
                    BeanUtils.copyProperties(k, tmp);
                    return tmp;
                }).collect(Collectors.toList()));
                return param.setData(form.toMap());
            });
    }

}
