package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.param.ListDownloadJobParam;
import com.cdz360.biz.model.download.vo.DownloadJobVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

import java.util.function.Function;

@Slf4j
@Component
public class DataCoreDownloadFileFeignHystrix
    implements FallbackFactory<DataCoreDownloadFileFeignClient> {

    @Override
    public DataCoreDownloadFileFeignClient apply(Throwable throwable) {
        return new DataCoreDownloadFileFeignClient() {
            @Override
            public Mono<ObjectResponse<ExcelPosition>> downloadFileApply(DownloadApplyParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = downloadFileApply (申请下载), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<ExcelPosition>> downloadFilePrintApply(DownloadApplyParam param) {
                log.error(
                        "【服务熔断】: Service = {}, api = downloadFilePrintApply (申请打印), param = {}",
                        DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<DownloadJobVo>> downloadApplyList(ListDownloadJobParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = downloadApplyList (下载任务列表), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }
        };
    }

    @Override
    public <V> Function<V, DataCoreDownloadFileFeignClient> compose(
        Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(
        Function<? super DataCoreDownloadFileFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
        return null;
    }
}
