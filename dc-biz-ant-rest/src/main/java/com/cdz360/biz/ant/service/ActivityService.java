package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.ant.feign.AntUserFeignClient;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.model.trading.coupon.param.ActivityUserCouponParam;
import com.cdz360.biz.model.trading.coupon.param.BatchSendCouponParam;
import com.cdz360.biz.model.trading.coupon.param.CouponSearchParam;
import com.cdz360.biz.model.trading.coupon.param.CreateActivityParam;
import com.cdz360.biz.model.trading.coupon.param.ListActivityParam;
import com.cdz360.biz.model.trading.coupon.param.UpdateActivityParam;
import com.cdz360.biz.model.trading.coupon.type.AcquireCouponResult;
import com.cdz360.biz.model.trading.coupon.type.CouponValidType;
import com.cdz360.biz.model.trading.coupon.vo.ActivityVo;
import com.cdz360.biz.model.trading.coupon.vo.CouponBi;
import com.cdz360.biz.model.trading.coupon.vo.CouponVo;
import com.cdz360.biz.model.trading.coupon.vo.DiscountVo;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * ActivityServiceImpl
 * <AUTHOR>
 *
 * @since 2018/11/20
 * @version  1.0
 */
@Slf4j
@Service
public class ActivityService //implements IActivityService
{

    @Autowired
    private AntUserFeignClient antUserFeignClient;
    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    public BaseResponse create(CreateActivityParam req) {
        return dataCoreFeignClient.createActivity(req);
    }

    public BaseResponse update(UpdateActivityParam req) {
        return dataCoreFeignClient.updateActivity(req);
    }

    public BaseResponse abortActivity(Long id) {
        return dataCoreFeignClient.abortActivity(id);
    }

    public BaseResponse showInMobile(Long id) {
        return dataCoreFeignClient.showInMobile(id);
    }

    public BaseResponse hideInMobile(Long id) {
        return dataCoreFeignClient.hideInMobile(id);
    }

    public BaseResponse activeActivity(Long id) {
        return dataCoreFeignClient.activeActivity(id);
    }

    public ObjectResponse<ActivityVo> getActivityDetail(Long id) {
        ObjectResponse<ActivityVo> ret = dataCoreFeignClient.getActivityDetail(id);

        if(ret != null && ret.getData() != null) {
//            ret.getData().setTimeTo(DateUtil.getPrevDate(ret.getData().getTimeTo()));

            if(CollectionUtils.isNotEmpty(ret.getData().getDictList())) {
                ret.getData().getDictList().stream().forEach(e -> {
                    if(CouponValidType.FIX.equals(e.getValidType())) {
                        e.setValidTimeTo(DateUtil.getPrevDate(e.getValidTimeTo()));
                    }
                });
            }
        }

//        if(ret != null && ret.getData() != null && CollectionUtils.isNotEmpty(ret.getData().getDictList())) {
//            ret.getData().getDictList().stream().forEach(e -> {
//                if(CouponValidType.FIX.equals(e.getValidType())) {
//                    e.setValidTimeTo(DateUtil.getPrevDate(e.getValidTimeTo()));
//                }
//            });
//        }

        return ret;
    }

    public ObjectResponse<CouponBi> couponBi(Long id) {
        return dataCoreFeignClient.couponBi(id);
    }

    public ObjectResponse<AcquireCouponResult> acquireCoupon(Long activityId, String phone) {
        return dataCoreFeignClient.acquireCoupon(activityId, phone);
    }

    public ObjectResponse<DiscountVo> discountInfo(Long commId, BigDecimal amount) {
        return dataCoreFeignClient.discountInfo(commId,amount);
    }

    public ObjectResponse<ActivityVo> hasActivity( Long commId, Long accountType){
        return dataCoreFeignClient.hasActivity(commId,accountType);
    }

    public ListResponse<CouponVo> userActivityCouponList(CouponSearchParam req) {
        ListResponse<CouponVo> ret = dataCoreFeignClient.userActivityCouponList(req);
        if(ret != null && CollectionUtils.isNotEmpty(ret.getData())) {
            ret.getData().stream().forEach(e -> {
                if(e.getValidTimeTo() != null) {
                    // 设定，有效期-截至
                    e.setValidTimeTo(DateUtil.getPrevDate(e.getValidTimeTo()));
                } else if(e.getValidRelateDay() != null && e.getValidTimeFrom() != null) {
                    // 计算，有效期-截至
                    e.setValidTimeTo(DateUtil.addDate(e.getValidTimeFrom(),
                        e.getValidRelateDay() - 1));
                } else {
                    // 取当前
                    e.setValidTimeTo(DateUtil.getThisDate(new Date()));
                }
            });
        }
        return ret;
    }

    public ListResponse<ActivityVo> list(ListActivityParam req) {
        ListResponse<ActivityVo> ret = dataCoreFeignClient.listActivity(req);
//        if(ret != null && CollectionUtils.isNotEmpty(ret.getData())) {
//            ret.getData().stream().forEach(e -> e.setTimeTo(DateUtil.getPrevDate(e.getTimeTo())));
//        }
        return ret;
    }

    public BaseResponse batchSendCoupon(BatchSendCouponParam param) {
        return dataCoreFeignClient.batchSendCoupon(param);
    }

    public ObjectResponse<Map<String, Boolean>> hasUserAcquiredCoupon(ActivityUserCouponParam param) {
        return dataCoreFeignClient.hasUserAcquiredCoupon(param);
    }

//    @Autowired
//    private TradingFeignClient tradingFeignClient;
//
//
//    @Autowired
//    private MerchantService merchantService;
//
//    public final static String CODE = "0";

    /**
     * 分页查询活动、模糊查询活动列表
     *
     * @param page
     * @param keyWord 查询条件
     * @param token
     * @return
     */
//    
//    public ListResponse<Activity> queryActivityList(Page<Activity> page, String keyWord, String token) {
//
//        Map<String, Object> maps = new HashMap<>(16);
//        //根据token获取商户ID及子商户ID集合
//        List<Long> commIdList = merchantService.getCommIdListByToken(token);
////        if (resultEntityCommIdList == null || resultEntityCommIdList.getStatus() != ResultConstant.RES_SUCCESS_CODE || resultEntityCommIdList.getData() == null) {
//////            return resultEntityCommIdList;
////            throw new DcServiceException("");
////        }
////        List<Long> commIdList = (List<Long>) resultEntityCommIdList.getData();
//        if (CollectionUtils.isEmpty(commIdList)) {
////            return resultEntityCommIdList;
//            throw new DcServiceException("");
//        }
//
//        Integer _num = page.getPageNum();
//        Integer _size = page.getPageSize();
//        StringBuffer sb = new StringBuffer();
//        for (int i = 0; i < commIdList.size(); i++) {
//            sb.append(commIdList.get(i).toString()).append(",");
//        }
//        //根据条件查询数据库
//        String result = sb.substring(0, sb.length() - 1);
//        ListResponse<Activity> activityList = tradingFeignClient.queryActivityList(_num, _size, keyWord, result);
//        FeignResponseValidate.check(activityList);
//        return activityList;
////        if (activityList != null && StringUtils.equals(CODE, activityList.get("status").toString())) {
//        //            return new ObjectResponse<>(activityList.get("data"));
////        } else {
////            if (activityList == null){
////                throw new DcServiceException("数据获取失败");
////            }else{
////                throw new DcServiceException(activityList.getString("error"));
////            }
////        }
//    }

//    /**
//     * 查询活动详情
//     *
//     * @param activityId
//     * @return
//     */
//
//    public ObjectResponse<Activity> selectById(Long activityId) {
//        ObjectResponse<Activity> result = tradingFeignClient.queryDetailActivityInfo(activityId);
//        return result;
////        if (result != null && StringUtils.equals(CODE, result.get("status").toString())) {
//        //            return new ObjectResponse<>(result.get("data"));
////        } else {
////            if (result == null){
////                throw new DcServiceException("数据获取失败");
////            }else{
////                throw new DcServiceException(result.getString("error"));
////            }
////        }
//    }
//
//    /**
//     * 保存活动信息
//     *
//     * @param activity 活动的对象
//     * @return
//     */
//
//    public BaseResponse save(Activity activity) {
//        BaseResponse result = tradingFeignClient.save(activity);
//        return result;
////        if (result != null && StringUtils.equals(CODE, result.get("status").toString())) {
//        //            return new ObjectResponse<>(result.get("data"));
////        } else {
////            if (result == null){
////                throw new DcServiceException("数据获取失败");
////            }else{
////                throw new DcServiceException(result.getString("error"));
////            }
////        }
//    }
//
//    /**
//     * 删除活动信息
//     *
//     * @param activityId 活动id
//     * @return
//     */
//
//    public BaseResponse deleteActivityInfo(Long activityId) {
//        BaseResponse result = tradingFeignClient.deleteActivityInfo(activityId);
//        return result;
////        if (result != null && StringUtils.equals(CODE, result.get("status").toString())) {
//        //            return new ObjectResponse<>(result.get("data"));
////        } else {
////            if (result == null){
////                throw new DcServiceException("数据操作失败");
////            }else{
////                throw new DcServiceException(result.getString("error"));
////            }
////        }
//    }
//
//    /**
//     * 逻辑删除活动信息
//     *
//     * @param activityId
//     * @return
//     */
//
//    public BaseResponse updateActivityStatus(Long activityId, Integer status) {
//        Activity activity = new Activity();
//        activity.setId(activityId);
//        activity.setStatus(status);
//        BaseResponse result = tradingFeignClient.updateActivityStatus(activity);
//        return result;
////        if (result != null && StringUtils.equals(CODE, result.get("status").toString())) {
//        //            return new ObjectResponse<>(result.get("data"));
////        } else {
////            if (result == null){
////                throw new DcServiceException("数据操作失败");
////            }else{
////                throw new DcServiceException(result.getString("error"));
////            }
////        }
//    }

//    /**
//     * 结束活动
//     *
//     * @param activityId
//     * @return
//     */
//
//    public BaseResponse endActivityStatus(Long activityId, Integer status) {
//        Activity activity = new Activity();
//        activity.setId(activityId);
//        activity.setStatus(status);
//        BaseResponse result = tradingFeignClient.endActivityStatus(activity);
//        return result;
////        if (result != null && StringUtils.equals(CODE, result.get("status").toString())) {
//        //            return new ObjectResponse<>(result.get("data"));
////        } else {
////            if (result == null){
////                throw new DcServiceException("数据操作失败");
////            }else{
////                throw new DcServiceException(result.getString("error"));
////            }
////        }
//    }

//    /**
//     * 编辑活动信息
//     *
//     * @param activity
//     * @return
//     */
//
//    public BaseResponse updateActivityInfo(Activity activity) {
//        BaseResponse result = tradingFeignClient.updateActivityInfo(activity);
//        return result;
////        if (result != null && StringUtils.equals(CODE, result.get("status").toString())) {
//        //            return new ObjectResponse<>(result.get("data"));
////        } else {
////            if (result == null){
////                throw new DcServiceException("数据操作失败");
////            }else{
////                throw new DcServiceException(result.getString("error"));
////            }
////        }
//    }

//    /**
//     * 查询该商户中是否有正在进行的活动
//     *
//     * @param activity
//     * @return
//     */
//    public int queryActivity(Activity activity) {
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd 08:00:00");
//        String startDate = sdf.format(activity.getStartTime());
//        String endDate = sdf.format(activity.getEndTime());
//        Map<String, Object> map = new HashMap<>(16);
//        map.put("commercialId", activity.getCommercialId());
//        map.put("status", ActivityConstants.STATUSED);
//        map.put("statusType", ActivityConstants.STATUS_TYPE_RECHARGE);
//        map.put("currentTimeStartTime", startDate);
//        map.put("currentTimeEndTime", endDate);
//        ListResponse<Activity> having = tradingFeignClient.queryInfo(map);
//        if (null != having && having.getData().size() > 0) {
//            // 表示当前有或者在进行的活动
//            return 3;
//        } else {
//            return 0;
//        }
//    }
}
