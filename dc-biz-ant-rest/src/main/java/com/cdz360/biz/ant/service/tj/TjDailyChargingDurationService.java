package com.cdz360.biz.ant.service.tj;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.feign.reactor.BizTjFeignClient;
import com.cdz360.biz.model.common.param.BaseListParam;
import com.cdz360.biz.model.sim.vo.SimImportParam;
import com.cdz360.biz.model.sim.vo.SimImportVo;
import com.cdz360.biz.model.tj.kc.param.ListTjDailyChargingDurationParam;
import com.cdz360.biz.model.tj.kc.param.ListTjMaterialCostParam;
import com.cdz360.biz.model.tj.kc.param.ListTjSurveyParam;
import com.cdz360.biz.model.tj.kc.param.RepeatSurveyParam;
import com.cdz360.biz.model.tj.kc.param.TjDailyChargingDurationImportParam;
import com.cdz360.biz.model.tj.kc.param.TjSurveyBiParam;
import com.cdz360.biz.model.tj.kc.po.TjCashOutflowPo;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationCoefficientPo;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo;
import com.cdz360.biz.model.tj.kc.po.TjDepreciationPo;
import com.cdz360.biz.model.tj.kc.vo.ImportTjDailyChargingDurationVo;
import com.cdz360.biz.model.tj.kc.vo.TjDailyChargingDurationImportItem;
import com.cdz360.biz.model.tj.kc.vo.TjDailyChargingDurationImportVo;
import com.cdz360.biz.model.tj.kc.vo.TjDailyChargingDurationVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyBiVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyVo;
import com.chargerlinkcar.framework.common.domain.invoice.nsr.ImportNSRInvoiceResultVo;
import com.chargerlinkcar.framework.common.domain.invoice.nsr.NSRInvoiceResult;
import com.chargerlinkcar.framework.common.domain.invoice.nsr.NSRInvoiceResultVo;
import com.chargerlinkcar.framework.common.utils.ExcelReadUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TjDailyChargingDurationService {

    @Autowired
    private BizTjFeignClient bizTjFeignClient;

    public Mono<ListResponse<TjDailyChargingDurationPo>> findTjDailyChargingDuration(
        ListTjDailyChargingDurationParam param) {
        return bizTjFeignClient.findTjDailyChargingDuration(param);
    }

    public Mono<ObjectResponse<TjDailyChargingDurationPo>> getTjDailyChargingDurationById(
        Long id) {
        return bizTjFeignClient.getTjDailyChargingDurationById(id);
    }

    public Mono<ObjectResponse<TjDailyChargingDurationPo>> saveTjDailyChargingDuration(
        TjDailyChargingDurationPo tjDailyChargingDurationPo) {
        return bizTjFeignClient.saveTjDailyChargingDuration(tjDailyChargingDurationPo);
    }

    public Mono<ObjectResponse<TjDailyChargingDurationPo>> disableTjDailyChargingDuration(
        Long id) {
        return bizTjFeignClient.disableTjDailyChargingDuration(id);
    }

    public Mono<ObjectResponse<TjDailyChargingDurationCoefficientPo>> findTjDailyChargingDurationCoefficient() {
        return bizTjFeignClient.findTjDailyChargingDurationCoefficient();
    }

    public Mono<ObjectResponse<TjDailyChargingDurationCoefficientPo>> saveTjDailyChargingDurationCoefficient(
        TjDailyChargingDurationCoefficientPo tjDailyChargingDurationCoefficientPo) {
        return bizTjFeignClient.saveTjDailyChargingDurationCoefficient(tjDailyChargingDurationCoefficientPo);
    }

    public Mono<ObjectResponse<ImportTjDailyChargingDurationVo<TjDailyChargingDurationVo>>> importTjDailyChargingDurationExcel(
        FilePart file) {
        ExcelReadUtil.RowParse<TjDailyChargingDurationImportItem> parse = list -> {
            TjDailyChargingDurationImportItem rs = new TjDailyChargingDurationImportItem();
            rs.setProvinceName(list.get(1))
                .setCityName(list.get(2))
                .setAreaName(list.get(3))
                .setFirstYearThirdMonth(list.get(4))
                .setFirstYearLastNinthMonth(list.get(5))
                .setSecondYear(list.get(6))
                .setThirdYear(list.get(7))
                .setFourthYear(list.get(8))
                .setFifthYear(list.get(9))
                .setSixthYear(list.get(10))
                .setSeventhYear(list.get(11))
                .setEighthYear(list.get(12))
                .setNinthYear(list.get(13))
                .setTenthYear(list.get(14));
            return rs;
        };

        try {
            List<TjDailyChargingDurationImportItem> dataList = ExcelReadUtil.<TjDailyChargingDurationImportItem>builder(file)
                .activeRowNum(1)
                .loopReadLine(parse)
                .close();

            if (CollectionUtils.isEmpty(dataList)) {
                throw new DcServiceException("数据信息为空");
            }

            return bizTjFeignClient.importTjDailyChargingDurationExcel(dataList)
                .doOnNext(FeignResponseValidate::check)
                .map(ObjectResponse::getData)
                .map(x -> {
                    x.setImportTotal(dataList.size());
                    return x;
                })
                .map(RestUtils::buildObjectResponse);
        } catch (Exception e) {
            // nothing to do
            log.error(">>>> 解析数据异常: {}", e.getMessage(), e);
            throw new DcServiceException("解析数据异常");
        }
    }

}
