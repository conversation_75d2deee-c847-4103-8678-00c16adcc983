package com.cdz360.biz.ant.domain;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = true)
public class UserAndBalanceVo extends AccountVo implements Serializable {

    /**
     * 金额(单位为分)
     */
    private Long balance;


    /**
     * 当前活跃卡号
     */
    private Long cardId;


    /**
     * 是否缴保证金(0.否,1.是)
     */
    private Integer isBond;
    /**
     * 保证金
     */
    private Long bond;

    /**
     * 赠送金
     */
    private Long freeGold;

    /**
     * 是否允许开启多笔订单 ，默认0-未开启;1-已开启
     */
    private Boolean isMulti;

    private static final long serialVersionUID = 1L;

    /**
     * 冻结金额
     */
    private long frozenAmount;

    private Long balanceId;

    private Integer defaultPayType;

    private Integer status;

}
