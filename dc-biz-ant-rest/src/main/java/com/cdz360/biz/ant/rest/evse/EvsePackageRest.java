package com.cdz360.biz.ant.rest.evse;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.ant.service.evse.EvsePackageService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.evse.param.ListPackageParam;
import com.cdz360.biz.model.evse.param.UpdateEvsePackageParam;
import com.cdz360.biz.model.evse.vo.EvsePackageVo;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * 海外平台升级包管理
 */
@Slf4j
@RestController
@RequestMapping("/device/evsePackage")
public class EvsePackageRest {

    @Autowired
    private EvsePackageService evsePackageService;

    @Operation(summary = "根据ID删除升级包")
    @GetMapping("/deleteById")
    public BaseResponse deleteById(ServerHttpRequest request, @RequestParam(value = "id") Long id) {
        UpdateEvsePackageParam params = new UpdateEvsePackageParam();
        params.setId(id)
            .setEnable(Boolean.FALSE)
            .setOpId(AntRestUtils.getSysUid(request))
            .setOpName(AntRestUtils.getSysUserName(request));
        log.info("删除升级包. param = {}", params);
        return evsePackageService.deleteById(params);
    }

    @Operation(summary = "修改升级包状态")
    @GetMapping("/updateStatus")
    public BaseResponse updateStatus(ServerHttpRequest request,
        @RequestParam(value = "id") Long id, @RequestParam(value = "status") Long status) {
        UpdateEvsePackageParam params = new UpdateEvsePackageParam();
        params.setId(id)
            .setStatus(status)
            .setOpId(AntRestUtils.getSysUid(request))
            .setOpName(AntRestUtils.getSysUserName(request));
        log.info("修改升级包状态. param = {}", params);
        return evsePackageService.updateStatus(params);
    }

    @Operation(summary = "编辑升级包")
    @PostMapping("/edit")
    public BaseResponse editPackage(ServerHttpRequest request,@RequestBody UpdateEvsePackageParam param) {
            param.setOpId(AntRestUtils.getSysUid(request))
            .setOpName(AntRestUtils.getSysUserName(request));
        log.info("编辑升级包. param = {}", param);
        return evsePackageService.editPackage(param);
    }

    @Operation(summary = "获取列表")
    @PostMapping("/getList")
    public ListResponse<EvsePackageVo> getList(
        @RequestBody ListPackageParam param) {
        log.info("获取升级包列表. param = {}", param);
        return evsePackageService.getList(param);
    }

    @Operation(summary = "新增升级包")
    @PostMapping("/create")
    public BaseResponse create(ServerHttpRequest request,
        @RequestBody UpdateEvsePackageParam param) {
        log.info("新增升级包. param = {}", param);
        param.setOpId(AntRestUtils.getSysUid(request))
            .setOpName(AntRestUtils.getSysUserName(request));
        return evsePackageService.createPackage(param);
    }

    @Operation(summary = "获取品牌列表")
    @GetMapping("/getBrandList")
    public ListResponse<String> getBrandList() {
        return evsePackageService.getBrandList();
    }

}