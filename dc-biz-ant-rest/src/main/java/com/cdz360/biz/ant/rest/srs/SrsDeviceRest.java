package com.cdz360.biz.ant.rest.srs;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.domain.vo.RadiationSampling;
import com.cdz360.biz.ant.service.srs.SrsDeviceService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.iot.param.ListCtrlParam;
import com.cdz360.biz.model.trading.iot.po.SrsPo;
import com.cdz360.biz.model.trading.iot.vo.RedisSrsRtData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "辐射仪设备信息相关接口", description = "辐射仪设备信息相关接口")
@RequestMapping(value = "/api/srsDevice")
public class SrsDeviceRest {

    @Autowired
    private SrsDeviceService srsDeviceService;

    @Operation(summary = "获取控制器下的辐射仪列表")
    @PostMapping(value = "/findSrsList")
    public Mono<ListResponse<SrsPo>> findSrsList(ServerHttpRequest request,
        @RequestBody ListCtrlParam param) {
        log.info("获取控制器下的辐射仪列表: param = {}", JsonUtils.toJsonString(param));
        String commIdChain = AntRestUtils.getCommIdChain(request);
        if (null == commIdChain) {
            throw new DcServiceException("未登录状态");
        }
        param.setCommIdChain(commIdChain);
        return srsDeviceService.findSrsList(param);
    }

    @Operation(summary = "站点详情-太阳辐射值曲线")
    @GetMapping(value = "/srCurve")
    public Mono<ListResponse<RadiationSampling>> srCurve(
        @RequestParam(value = "siteId") String siteId,
        @RequestParam(value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        log.info("srCurve. siteId: {}, date: {}", siteId, date);
        return srsDeviceService.srCurve(siteId, date);
    }


    @Operation(summary = "辐射仪逆变器实时数据")
    @GetMapping(value = "/srsInfoInTime")
    public Mono<ObjectResponse<RedisSrsRtData>> srsInfoInTime(
        @Parameter(name = "辐射仪设备编号", required = true) @RequestParam(value = "dno") String dno) {
        log.info("辐射仪实时数据: dno = {}", dno);
        return srsDeviceService.srsInfoInTime(dno);
    }

}