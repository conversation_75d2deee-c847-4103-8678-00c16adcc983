package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.trading.profit.sett.param.ListSettJobBillParam;
import com.cdz360.biz.model.trading.profit.sett.vo.SettJobBillDetailVo;
import com.cdz360.biz.model.trading.profit.sett.vo.SettJobBillVo;
import com.cdz360.biz.oa.dto.OaSettJobBillDto;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
public class DataCoreSettJobBillFeignHystrix
    implements FallbackFactory<DataCoreSettJobBillFeignClient> {

    @Override
    public DataCoreSettJobBillFeignClient apply(Throwable throwable) {
        log.error("{}", throwable.getMessage(), throwable);
        return new DataCoreSettJobBillFeignClient() {
            @Override
            public Mono<ObjectResponse<SettJobBillDetailVo>> getSettJobBill(String billNo) {
                log.error(
                    "【服务熔断】: Service = {}, api = getSettJobBill (获取结算单), billNo = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, billNo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<SettJobBillVo>> findSettJobBill(ListSettJobBillParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = findSettJobBill (获取结算单列表), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<OaSettJobBillDto>> checkSettJobBill(
                List<OaSettJobBillDto> param) {
                log.error(
                    "【服务熔断】: Service = {}, api = checkSettJobBill (检查结算单列表变更), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<SettJobBillVo>> recalculateSettJobBill(
                String billNo, Integer recalculateWay) {
                log.error("【服务熔断】: Service = {}, api = recalculateSettJobBill (结算单数据更新),"
                        + " billNo = {}, recalculateWay = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, billNo, recalculateWay);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<SettJobBillVo>> deleteSettJobBill(String billNo) {
                log.error(
                    "【服务熔断】: Service = {}, api = deleteSettJobBill (删除结算单(逻辑删除)), billNo = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, billNo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }
        };
    }

}
