package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.domain.vo.ZhuoFuResponse;
import com.cdz360.biz.ant.domain.vo.ZhuoFuUserVo;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.HttpPlusUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 *  互联互通有关配置接口
 * @since 2019-02-19 11:03
 */
@RestController
@Slf4j
@Tag(name = "互联互通有关配置接口", description = "hlht")
@RequestMapping("/api/hlht")
public class HlhtRset extends BaseController {

    //    @Autowired
//    private HlhtService hlhtService;
    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;
    @Value("${zhuofuLoginUrl:}")
    private String zhuofuLoginUrl;


    /**
     * 卓付链接
     *
     */
    @RequestMapping("/fundsTrans")
    public ObjectResponse<String> fundsTrans(ServerHttpRequest request) {
        long userId = getUserIdLong2(request);
        ListResponse<SysUserVo> users = authCenterFeignClient.querySysUserByIds(List.of(userId));
        SysUserVo user = users.getData().get(0);
        ZhuoFuUserVo zhuoFuUserVo = new ZhuoFuUserVo();
        zhuoFuUserVo.setUser_id(String.valueOf(user.getId()));
        zhuoFuUserVo.setUser_name(user.getUsername());
        String result = HttpPlusUtil.doPost(zhuofuLoginUrl, JsonUtils.toJsonString(zhuoFuUserVo), null);
        ZhuoFuResponse response = JsonUtils.fromJson(result, ZhuoFuResponse.class);
        if (!response.getSuccess()) {
            ObjectResponse<String> objectResponse = new ObjectResponse<>();
            objectResponse.setError(response.getErrmsg());
            return objectResponse;
        }
        return new ObjectResponse(response.getResult().getToken());
    }


}
