package com.cdz360.biz.ant.service.cus;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.feign.BizBiFeignClient;
import com.cdz360.biz.model.cus.user.dto.CusBiDto;
import com.chargerlinkcar.framework.common.feign.UserFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CusBiService {

    @Autowired
    private BizBiFeignClient bizBiFeignClient;

    @Autowired
    private UserFeignClient userFeignClient;


    /**
     * 一周用户简报
     * @param commIdChain
     * @return
     */
    public ObjectResponse<CusBiDto> getWeeklyUserBi(String commIdChain) {
        ObjectResponse<CusBiDto> res = this.userFeignClient.getCusBi(null, commIdChain);
        FeignResponseValidate.check(res);
        ObjectResponse<Long> activeUserNumRes = this.bizBiFeignClient.getActiveCusCount7(null, commIdChain);
        FeignResponseValidate.check(activeUserNumRes);
        res.getData().setActiveUserNum(activeUserNumRes.getData());
        return res;
    }
}
