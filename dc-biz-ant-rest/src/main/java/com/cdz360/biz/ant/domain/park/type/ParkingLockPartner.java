package com.cdz360.biz.ant.domain.park.type;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Getter
public enum ParkingLockPartner {
    UNKNOWN(null, null, "未知"),
    ANNEFFI("anneffiClientHandler", "anneffiClientCommander", "安效停"),
    ;

    @Schema(description = "接收信息处理器 开发实现")
    private final String handlerBeanName;
    @Schema(description = "请求信息处理器 开发实现")
    private final String commanderBeanName;
    private final String desc;

    ParkingLockPartner(String handlerBeanName, String commanderBeanName, String desc) {
        this.handlerBeanName = handlerBeanName;
        this.commanderBeanName = commanderBeanName;
        this.desc = desc;
    }

}
