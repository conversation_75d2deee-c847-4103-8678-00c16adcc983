package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.ant.service.PayBillService;
import com.cdz360.biz.ant.service.sysLog.CommercialSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.cus.wallet.vo.RefundAnalyzeVo;
import com.cdz360.biz.model.trading.bi.dto.BiExportGroups;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.bill.param.ListZftThirdOrderParam;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.trading.order.param.CzOrderPointParam;
import com.cdz360.biz.model.trading.order.param.ListPayBillParam;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.model.trading.order.param.ZftBillParam;
import com.cdz360.biz.model.trading.order.po.PayBillPo;
import com.cdz360.biz.model.trading.order.vo.*;
import com.cdz360.biz.model.wallet.vo.RefundReasonVo;
import com.chargerlinkcar.framework.common.constant.CheckTaxStatus;
import com.chargerlinkcar.framework.common.domain.PointRecDto;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Locale;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import javax.validation.Valid;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

/**
 * @since 2019/11/5 10:01
 * <AUTHOR>
 */
@Slf4j
@Validated
@RestController
@Tag(name = "充值信息查询接口", description = "充值查询")
public class PayBillRest extends BaseController {

    @Autowired
    private PayBillService payBillService;
    @Autowired
    private CommercialSysLogService commSysLogService;

    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;

    @Operation(summary = "充电订单的资金块信息")
    @GetMapping("/api/paybill/orderPointRecLog")
    public ListResponse<PayBillLinkChargeOrderVo> orderPointRecLog(
        @Parameter(name = "充电订单号", example = "1194797842524704768") @RequestParam(value = "orderNo", required = true) String orderNo,
        ServerHttpRequest request
    ) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return payBillService.orderPointRecLog(orderNo);
    }

    @Operation(summary = "判断充值订单是否存在资金块在发票中心已开票或审核中的开票订单")
    @GetMapping("/api/paybill/checkTaxStatus")
    public ObjectResponse<CheckTaxStatus> checkTaxStatus(
        @Parameter(name = "充值订单号", example = "********************") @RequestParam(value = "orderId", required = true) String orderId,
        ServerHttpRequest request
    ) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return payBillService.checkTaxStatus(orderId);
    }

    @Operation(summary = "充值记录查看")
    @GetMapping("/api/paybill/view")
    public ObjectResponse<PayBillVo> payBillView(
        @Parameter(name = "充值记录订单号", example = "********************", required = true) @RequestParam(value = "orderId") String orderId,
        ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request));
        Long topCommId = AntRestUtils.getTopCommId(request);
        if (null == topCommId) {
            throw new DcArgumentException("用户登录信息已失效，请刷新后重试");
        }
        return payBillService.payBillView(topCommId, orderId);
    }

    @Operation(summary = "充值信息列表")
    @RequestMapping(value = "/api/paybill/orderList", method = RequestMethod.POST)
    public ListResponse<PayBillVo> orderList(ServerHttpRequest request,
        @RequestBody PayBillParam payBillParam) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param={}", payBillParam);
        payBillParam.setCommIdChain(super.getCommIdChain2(request));

        return payBillService.orderList(payBillParam);
    }

    @Operation(summary = "直付通信息列表")
    @RequestMapping(value = "/api/paybill/zftOrderList", method = RequestMethod.POST)
    public ListResponse<ZftBillVo> zftOrderList(ServerHttpRequest request,
        @RequestBody ZftBillParam payBillParam) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param={}", payBillParam);
        payBillParam.setCommIdChain(super.getCommIdChain2(request));

        return payBillService.zftOrderList(payBillParam);
    }

    @Operation(summary = "用户充值账户汇总")
    @RequestMapping(value = "/api/paybill/userBillAccountName", method = RequestMethod.POST)
    public ObjectResponse<UserBillAccountNameVo> userBillAccountName(ServerHttpRequest request,
        @RequestBody PayBillParam payBillParam) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param={}", payBillParam);
        payBillParam.setCommIdChain(super.getCommIdChain2(request));

        return payBillService.userBillAccountName(payBillParam);
    }

    /**
     * 充值信息列表excel导出
     *
     * @param request
     * @param payBillParam
     * @return
     */
    @Operation(summary = "充值信息列表excel导出")
    @PostMapping("/api/paybill/exportExcel")
    public Mono<ObjectResponse<ExcelPosition>> exportExcelCus(
        ServerHttpRequest request,
        @RequestBody PayBillParam payBillParam) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param={}", payBillParam);
        List<Long> commIdList = this.getCommIdList2(request);
        if (CollectionUtils.isEmpty(commIdList)) {
            throw new DcArgumentException("该接口需要提供商户Id");
        }
        IotAssert.isNotNull(payBillParam.getGroup(), "订单导出分组不能为空");
        payBillParam.setCommIdList(commIdList);

        Locale locale = AntRestUtils.getLocale(request);
        // 获取到的是null就设置为null
        payBillParam.setLocale(locale);

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName(
                "充值订单" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")))
            .setFunctionMap(DownloadFunctionType.PAY_BILL_RECORD_CUS)
            .setReqParam(JsonUtils.toJsonString(payBillParam));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return payBillService.exportExcel(payBillParam);
    }

    /**
     * 充值信息列表excel导出
     *
     * @param request
     * @param payBillParam
     * @return
     */
    @Operation(summary = "充值信息列表excel导出(运营管理导出使用)")
    @PostMapping("/api/paybill/exportExcelManager")
    public Mono<ObjectResponse<ExcelPosition>> exportExcelManager(
        ServerHttpRequest request,
        @RequestBody PayBillParam payBillParam) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param={}", payBillParam);
        List<Long> commIdList = this.getCommIdList2(request);
        if (CollectionUtils.isEmpty(commIdList)) {
            throw new DcArgumentException("该接口需要提供商户Id");
        }
        payBillParam.setCommIdList(commIdList);
        payBillParam.setGroup(BiExportGroups.FINANCERECHARGEBI);


        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName(
                "充值订单" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")))
            .setFunctionMap(DownloadFunctionType.PAY_BILL_RECORD)
            .setReqParam(JsonUtils.toJsonString(payBillParam));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return payBillService.exportExcelManager(payBillParam);
    }

    @Operation(summary = "直付通对账订单导出")
    @PostMapping("/api/paybill/exportZftList")
    public Mono<ObjectResponse<ExcelPosition>> exportZftList(
        ServerHttpRequest request,
        @RequestBody ZftBillParam payBillParam) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param={}", payBillParam);

        payBillParam.setCommIdChain(super.getCommIdChain2(request));

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("交易订单")
            .setFunctionMap(DownloadFunctionType.PAY_BILL_ZFT)
            .setReqParam(JsonUtils.toJsonString(payBillParam));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return payBillService.exportZftList(payBillParam);
    }

    @Operation(summary = "对账管理对账订单导出")
    @PostMapping("/api/paybill/exportZftThirdOrderList")
    public Mono<ObjectResponse<ExcelPosition>> exportZftThirdOrderList(
        ServerHttpRequest request,
        @RequestBody ListZftThirdOrderParam param) {
        log.info("对账管理对账订单导出: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), param);
        param.setCommIdChain(AntRestUtils.getCommIdChain(request));

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName(StringUtils.isNotBlank(param.getExFileName()) ? param.getExFileName()
                : "对账管理订单")
            .setFunctionMap(DownloadFunctionType.PAY_BILL_ZFT_THIRD)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return Mono.just(param)
//                .flatMap(payBillService::exportZftThirdOrderList);
    }

    @Operation(summary = "确认充值信息列表excel是否生成")
//    @CheckToken(login = true)
    @PostMapping(value = "/api/paybill/exportCompleted")
    public ObjectResponse<Boolean> exportCompleted(
        @RequestBody ExcelPosition excelPosition, ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param={}", excelPosition);
        String token = getToken2(request);
        if (StringUtils.isEmpty(token)) {
            throw new DcArgumentException("请登录");
        }

        return payBillService.exportCompleted(excelPosition);
    }

    //下载excel文件
    @Operation(summary = "充值信息列表excel下载")
//    @CheckToken(login = true)
    @PostMapping(value = "/api/paybill/exportDownload")
    public Mono<Void> exportDownload(
        @RequestBody ExcelPosition excelPosition,
        ServerHttpRequest request, ServerHttpResponse response) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param={}", excelPosition);
        String token = getToken2(request);
        if (StringUtils.isEmpty(token)) {
            throw new DcArgumentException("请登录");
        }

        return payBillService.exportDownload(excelPosition, response);
    }


    @Operation(summary = "统计充值记录数据")
    @PostMapping("/api/paybill/bi")
    public ListResponse<PayBillBi> payBillBi(
        @RequestBody PayBillParam param,
        ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param={}" + param);
        String commIdChain = this.getCommIdChain2(request);
        if (StringUtils.isBlank(commIdChain)) {
            throw new DcArgumentException("登录无效，请重新登录");
        }
        param.setCommIdChain(commIdChain);
        return payBillService.payBillBi(param);
    }

    @Operation(summary = "直付通记录数据")
    @PostMapping("/api/zftbill/bi")
    public ListResponse<ZftBillBi> zftBillBi(
        @RequestBody ZftBillParam param,
        ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param={}" + param);
        param.setCommIdChain(super.getCommIdChain2(request));
        return payBillService.zftBillBi(param);
    }

    @Operation(summary = "通过充值记录Id更新充值信息")
    @PostMapping("/api/paybill/updateById")
    public ObjectResponse<Integer> updateById(
        @RequestBody PayBillPo po,
        ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param={}" + po);
        return payBillService.updateById(po);
    }

    @Operation(summary = "通过充值记录Id更新充值信息")
    @PostMapping("/api/paybill/updateByOrderId")
    public ObjectResponse<Integer> updateByOrderId(
        @RequestBody @Valid PayBillPo po,
        ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}", po);
        var res = payBillService.updateByOrderId(po);
        commSysLogService.updateByOrderIdLog(po.getOrderId(), request);
        return res;
    }

    @Operation(summary = "获取充值记录资金块详情")
    @GetMapping("/api/paybill/pointRecLog")
    public ObjectResponse<PayBillUsedDetail> pointRecLog(
        @Parameter(name = "充值记录订单号", example = "********************", required = true) @RequestParam(value = "orderId") String orderId,
        @Parameter(name = "充电订单号", example = "1194797842524704768") @RequestParam(value = "orderNo", required = false) String orderNo,
        ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return payBillService.pointRecLog(orderId, orderNo);
    }

    @Operation(summary = "获取充值记录列表当前资金块详情")
    @PostMapping("/api/paybill/getCzOrderPointsInfo")
    public ListResponse<PointRecDto> getCzOrderPointsInfo(
        @RequestBody CzOrderPointParam param,
        ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return payBillService.getCzOrderPointsInfo(param);
    }

    @Operation(summary = "获取充值记录充值时的账户信息")
    @GetMapping("/api/paybill/accountDetail")
    public ObjectResponse<PayBillAccountDetailVo> getAccountDetail(
        @Parameter(name = "充值记录订单号", example = "********************", required = true) @RequestParam(value = "orderId") String orderId,
        ServerHttpRequest request
    ) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return payBillService.getAccountDetail(orderId);
    }

    @Operation(summary = "退款数据分析")
    @GetMapping("/api/paybill/refundAnalyze")
    public ObjectResponse<RefundAnalyzeVo> refundAnalyze(ServerHttpRequest request,
        @RequestParam(value = "startDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startDate,
        @RequestParam(value = "stopDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date stopDate) {
        log.info("request: {}, startDate: {}, stopDate: {}", LoggerHelper2.formatEnterLog(request),
            startDate, stopDate);

        return payBillService.refundAnalyze(startDate, stopDate);
    }

    @Operation(summary = "退款原因列表")
    @GetMapping("/api/paybill/refundList")
    public ListResponse<RefundReasonVo> refundList(ServerHttpRequest request,
        @RequestParam(value = "cusName", required = false) String cusName,
        @RequestParam(value = "cusPhone", required = false) String cusPhone,
        @RequestParam(value = "cusNote", required = false) String cusNote,
        @RequestParam(value = "start") int start,
        @RequestParam(value = "size") int size) {
        log.info("request: {}, cusName: {}, cusPhone: {}, cusNote: {}, start: {}, size: {}",
            LoggerHelper2.formatEnterLog(request), cusName, cusPhone, cusNote, start, size);

        return payBillService.refundList(cusName, cusPhone, cusNote, start, size);
    }

    @Operation(summary = "获取客户充值列表", description = "客户退款页面使用")
    @PostMapping(value = "/api/paybill/cusPayBillList")
    public Mono<ListResponse<PayBillRefundVo>> getCusPayBillList(
        ServerHttpRequest request, @RequestBody ListPayBillParam param) {
        log.info("企业客户获取充值列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        Long topCommId = AntRestUtils.getTopCommId(request);
        if (null == topCommId) {
            throw new DcArgumentException("用户登录信息已失效，请刷新后重试");
        }

        param.setTopCommId(topCommId);
        param.setCommIdChain(AntRestUtils.getCommIdChain(request));
        return payBillService.getCusPayBillList(param);
    }
}
