package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.domain.request.UpgradeTaskRequest;
import com.cdz360.biz.model.iot.param.UpgradeTaskInfoRequest;
import com.cdz360.biz.model.iot.vo.UpgradeTaskDetailVo;
import com.cdz360.biz.model.iot.vo.UpgradeTaskInfoVo;
import com.cdz360.biz.model.iot.vo.UpgradeTaskVo;
import com.cdz360.biz.model.iot.param.UpgradeTaskDetailRequest;
import com.cdz360.biz.model.iot.param.UpgradeTaskListRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class IotDeviceMgmFeignClientHystrixFactory implements
    FallbackFactory<IotDeviceMgmFeignClient> {

    @Override
    public IotDeviceMgmFeignClient create(Throwable cause) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_IOT_DEVICE_MGM,
            cause.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_IOT_DEVICE_MGM,
            cause.getStackTrace());

        return new IotDeviceMgmFeignClient() {
            @Override
            public BaseResponse startEssTask(UpgradeTaskRequest upgradeTaskRequest) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<UpgradeTaskVo> getEssUpgradeTaskListBySite(
                UpgradeTaskListRequest updateTaskListRequest) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<UpgradeTaskDetailVo> getUpgradeTaskDetailListByTaskId(
                UpgradeTaskDetailRequest updateTaskDetailRequest) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<UpgradeTaskInfoVo> getEssUpgradeTaskInfo(
                UpgradeTaskInfoRequest upgradeTaskInfoRequest) {
                return RestUtils.serverBusy4ObjectResponse();
            }
        };
    }
}
