package com.cdz360.biz.ant.service.partner;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorSiteDataCoreFeignClient;
import com.cdz360.biz.model.geo.po.DistrictPo;
import com.cdz360.biz.model.merchant.dto.CommercialDto;
import com.cdz360.biz.model.merchant.param.ListCommercialParam;
import com.cdz360.biz.model.order.type.OrderPayType;
import com.cdz360.biz.model.trading.hlht.param.BindHlhtParam;
import com.cdz360.biz.model.trading.hlht.param.HlhtSiteParam;
import com.cdz360.biz.model.trading.hlht.param.SyncSiteParam;
import com.cdz360.biz.model.trading.hlht.vo.HlhtSiteVo;
import com.cdz360.biz.model.trading.hlht.vo.OperatorVo;
import com.cdz360.biz.utils.feign.hlht.OpenHlhtFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PartnerEquipService {

    @Autowired
    private OpenHlhtFeignClient openHlhtFeignClient;
    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;
    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;
    @Autowired
    private ReactorSiteDataCoreFeignClient reactorSiteDataCoreFeignClient;

    public Mono<ListResponse<OperatorVo>> partnerList(String code, String name, Long start, Integer size) {
        return openHlhtFeignClient.partnerList(code, name, start, size);
    }

    public Mono<BaseResponse> upsertPartner(OperatorVo vo) {
        return openHlhtFeignClient.upsertPartner(vo);
    }

    public Mono<BaseResponse> delPartner(Long pid) {
        return openHlhtFeignClient.delPartner(pid);
    }

    public Mono<BaseResponse> syncSiteByPartner(Long topCommId, String partnerCode) {

        return openHlhtFeignClient.syncSitesInfo(topCommId, partnerCode, List.of());
    }

    public Mono<BaseResponse> syncSiteByStationId(SyncSiteParam param) {
        log.debug("param = {}", JsonUtils.toJsonString(param));
        return openHlhtFeignClient.syncSitesInfo(param.getTopCommId(), null, param.getStationIdList());
    }

    public Mono<ListResponse<HlhtSiteVo>> hlhtSiteList(HlhtSiteParam param) {
        if (StringUtils.isNotBlank(param.getCommName())) {
            ListCommercialParam req = new ListCommercialParam();
            req.setCommNameExact(param.getCommName()).setStart(0L).setSize(1);
            ListResponse<CommercialDto> dtoListResponse = authCenterFeignClient.getCommList(req);
            if (CollectionUtils.isNotEmpty(dtoListResponse.getData())) {
                param.setCommId(dtoListResponse.getData().stream().findFirst().map(CommercialDto::getId).get());
            } else {
                return Mono.just(RestUtils.buildListResponse(List.of(), 0));
            }
        }

        return openHlhtFeignClient.hlhtSiteList(param)
                .doOnNext(FeignResponseValidate::check)
//                .map(ListResponse::getData)
                .flatMap(li -> {
                    List<String> areaCodeList = li.getData().stream()
                            .filter(site -> StringUtils.isNotBlank(site.getAreaCode()))
                            .map(HlhtSiteVo::getAreaCode).collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(areaCodeList)) {
                        return Mono.just(li);
                    }

                    return reactorSiteDataCoreFeignClient.getDistrictByList(areaCodeList)
                            .doOnNext(FeignResponseValidate::check)
                            .map(res -> res.getData().stream().collect(Collectors.toMap(DistrictPo::getCode, o -> o)))
                            .map(dicMap -> {
                                li.getData().forEach(site -> {
                                    DistrictPo temp = dicMap.get(site.getAreaCode());
                                    if (temp != null) {
                                        site.setProvinceName(temp.getProvinceName())
                                                .setCityName(temp.getCityName());
                                    }
                                });
                                return li.getData();
                            })
                            .map(l -> RestUtils.buildListResponse(l, li.getTotal()));
                });
    }

    public BaseResponse bindHlhtSite(BindHlhtParam param) {
        return dataCoreFeignClient.bindHlhtSite(param);
    }

    public BaseResponse unbindHlhtSite(BindHlhtParam param) {
        return dataCoreFeignClient.unbindHlhtSite(param);
    }

    public BaseResponse editHlhtSitePayType(Long commId, List<Integer> payTypeList) {
        return authCenterFeignClient.editHlhtSitePayType(commId, payTypeList.stream().map(e -> Objects.requireNonNull(OrderPayType.getValueByCode(e)).name()).collect(Collectors.toList()));
    }

}
