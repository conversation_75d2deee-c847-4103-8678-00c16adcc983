package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.ant.domain.request.RefundOrderParam;
import com.cdz360.biz.ant.service.WalletService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.cus.wallet.po.RefundOrderPo;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ServerWebExchange;

/**
 * @since 2019/11/4 14:24
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "个人钱包接口")
@RequestMapping("/api/wallet")
public class CusWalletRest extends BaseController {

    @Autowired
    private WalletService iWalletService;


//    @ServiceLog(operation = "提现信息列表", level = LogTypeEnum.INFO)
    @ResponseBody
    @Operation(summary = "提现信息列表")
    @RequestMapping(value = "/listRefundOrder", method = RequestMethod.POST)
    public ListResponse<RefundOrderPo> listRefundOrder(ServerHttpRequest request,
                                                       ServerWebExchange exh,
                                                       @RequestBody RefundOrderParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param={}", param);
        long commercialId = AntRestUtils.getTopCommId(request);    // super.getCommIdLong(request);   // 集团商户ID
        Assert.isTrue(commercialId > 0L, "请重新登录...");
        param.setTopCommId(commercialId);

        // 设置分页参数
        OldPageParam page = getPage2(request, exh, true);
        Long start = page.getPageNum() > 0 ? (page.getPageNum() - 1) * page.getPageSize() : 0L;
        Integer size = page.getPageSize();

        param.setStart(start);
        param.setSize(size);

        ListResponse<RefundOrderPo> res = iWalletService.listRefundOrder(param);
        return res;
    }


}
