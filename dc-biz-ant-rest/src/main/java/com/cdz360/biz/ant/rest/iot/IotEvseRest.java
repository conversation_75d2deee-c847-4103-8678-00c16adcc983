package com.cdz360.biz.ant.rest.iot;


import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.domain.vo.AntEvseInfoVo;
import com.cdz360.biz.ant.service.iot.IotEvseBizService;
import com.cdz360.biz.ant.service.sysLog.SiteSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.ant.utils.FileUtil;
import com.cdz360.biz.model.common.constant.DcBizConstants;
import com.cdz360.biz.model.iot.param.BindEvseParam;
import com.cdz360.biz.model.iot.param.EvseTinyParam;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.iot.param.ModifyEvseInfoParam;
import com.cdz360.biz.model.iot.param.UnbindEvseParam;
import com.cdz360.biz.model.iot.param.UpgradeRecordVo;
import com.cdz360.biz.model.iot.param.UpgradeTaskListRequest;
import com.cdz360.biz.model.iot.vo.EvseImportVo;
import com.cdz360.biz.model.iot.vo.EvseModelVo;
import com.cdz360.biz.model.trading.iot.dto.EvseTinyDto;
import com.cdz360.biz.model.trading.iot.po.EvseModelPo;
import com.cdz360.biz.model.trading.iot.vo.EvseInfoVo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "桩相关接口", description = "桩")
public class IotEvseRest extends BaseController {


    @Autowired
    private IotEvseBizService iotEvseBizService;
    @Autowired
    private SiteSysLogService siteSysLogService;


    @Operation(summary = "获取桩信息")
    @GetMapping("/api/evse/getEvseInfo")
    public ObjectResponse<AntEvseInfoVo> getEvseInfo(ServerHttpRequest request, @RequestParam String evseNo) {
        log.info(LoggerHelper2.formatEnterLog(request));
        AntEvseInfoVo evse = iotEvseBizService.getEvseInfo(evseNo);
        return RestUtils.buildObjectResponse(evse);
    }

    @Operation(summary = "获取桩信息,包含场站组验证")
    @GetMapping("/api/evse/getEvseSimpleInfo")
    public ObjectResponse<AntEvseInfoVo> getEvseSimpleInfo(ServerHttpRequest request, @RequestParam String evseNo) {
        log.info(LoggerHelper2.formatEnterLog(request));
        List<String> gids = AntRestUtils.getSysUserGids(request);
        AntEvseInfoVo evse = iotEvseBizService.getEvseSimpleInfo(evseNo,gids);
        return RestUtils.buildObjectResponse(evse);
    }

    @Operation(summary = "获取桩列表")
    @PostMapping("/api/evse/getEvseInfoList")
    public ListResponse<EvseInfoVo> getEvseInfoList(ServerHttpRequest request,
                                                    @RequestBody ListEvseParam param) {
        log.info(LoggerHelper2.formatEnterLog(request) + "param = {}", param);
        param.setTopCommId(AntRestUtils.getTopCommId(request));
        List<String> gids = AntRestUtils.getSysUserGids(request);
        if (CollectionUtils.isNotEmpty(gids)) {
            param.setGids(gids);
        } else if (DcBizConstants.superTopCommId.equals(param.getTopCommId())) {
            param.setTopCommId(null);   // 后门,让34474的集团商户权限帐号可以查所有的信息
        }
        var res = iotEvseBizService.getEvseInfoList(param);
        return res;
    }

    @Operation(summary = "获取桩下拉列表")
    @PostMapping("/api/evse/getEvseTinyList")
    public ListResponse<EvseTinyDto> getEvseTinyList(ServerHttpRequest request,
                                                     @RequestBody EvseTinyParam param) {
        log.info(LoggerHelper2.formatEnterLog(request) + "param = {}", param);
        param.setCommIdChain(AntRestUtils.getCommIdChainAndFilter(request));
        return iotEvseBizService.getEvseTinyList(param);
    }

    @Operation(summary = "本地鉴权vin获取桩列表")
    @PostMapping("/api/evse/getEvseListForVinAuth")
    public ListResponse<EvseInfoVo> getEvseListForVinAuth(ServerHttpRequest request,
                                                          @RequestBody ListEvseParam param) {
        log.info(LoggerHelper2.formatEnterLog(request) + "param = {}", param);
        param.setTopCommId(AntRestUtils.getTopCommId(request));
        if (DcBizConstants.superTopCommId.equals(param.getTopCommId())) {
            param.setTopCommId(null);   // 后门,让34474的集团商户权限帐号可以查所有的信息
        }
        var res = iotEvseBizService.getEvseListForVinAuth(param);
        return res;
    }

    @Operation(summary = "获取桩升级记录")
    @PostMapping("/api/evse/getEvseUpgradeList")
    public ListResponse<UpgradeRecordVo> getEvseUpgradeList(ServerHttpRequest request,
                                                            @RequestBody UpgradeTaskListRequest req) {
        log.info(LoggerHelper2.formatEnterLog(request) + "req = {}", req);
        return iotEvseBizService.getEvseUpgradeList(req);
    }

    @Operation(summary = "桩绑定到场站")
    @PostMapping("/api/evse/bindToSite")
    public BaseResponse bindToSite(ServerHttpRequest request, @RequestBody BindEvseParam param) {
        log.info(LoggerHelper2.formatEnterLog(request) + " param = {}", param);
        iotEvseBizService.bindEvse2Site(param);
        siteSysLogService.bindToSiteLog(param.getEvseNo(), request);
        return RestUtils.success();
    }

    @Operation(summary = "桩解绑")
    @PostMapping("/api/evse/unbind")
    public ObjectResponse<Boolean> unbind(ServerHttpRequest request, @RequestBody UnbindEvseParam param) {
        log.info(LoggerHelper2.formatEnterLog(request) + " param = {}", param);
        ObjectResponse<Boolean> res = iotEvseBizService.unbindEvse(param,
                AntRestUtils.getCommId(request)
                //        super.getCommIdLong(request)
        );
        siteSysLogService.unbindLog(param.getEvseNoList(), request);
        return res;
    }

    @Operation(summary = "修改桩信息")
    @PostMapping("/api/evse/updateEvseInfo")
    public BaseResponse updateEvseInfo(ServerHttpRequest request, @RequestBody ModifyEvseInfoParam param) {
        log.info(LoggerHelper2.formatEnterLog(request) + " param = {}", param);
        iotEvseBizService.updateEvseInfo(param);
        siteSysLogService.updateEvseInfoLog(param.getEvseNo(), request);
        return RestUtils.success();
    }

    /**
     * @param param
     */
    @Operation(summary = "批量修改桩信息(只包含：是否使用场站通用配置，设备型号，出厂日期，质保期限，直流模块)")
    @PostMapping("/api/evse/batchUpdateEvseInfo")
    public BaseResponse batchUpdateEvseInfo(ServerHttpRequest request,
                                            @RequestBody ModifyEvseInfoParam param) {
        log.info(LoggerHelper2.formatEnterLog(request) + " param = {}", param);
        iotEvseBizService.batchUpdateEvseInfo(param);
        siteSysLogService.batchUpdateEvseInfoLog(param.getEvseNoList(), request);
        return RestUtils.success();
    }

    @Operation(summary = "获取设备型号")
    @GetMapping("/api/evse/getModelList")
    public ListResponse<EvseModelPo> getModelList(ServerHttpRequest request,
        @RequestParam(value = "start") Long start,
        @RequestParam(value = "size") Integer size,
        @RequestParam(value = "keyword", required = false) String keyword) {
        log.info(LoggerHelper2.formatEnterLog(request) + " start = {}, size = {}", start, size);
        return iotEvseBizService.getModelList(start, size, keyword);
    }

    @Operation(summary = "解析管理平台桩导入excel文件")
    @PostMapping("/api/evse/parseExcel")
    public Mono<ObjectResponse<EvseImportVo>> parseExcel(ServerHttpRequest request,
                                                         @RequestPart FilePart file) {
        FileUtil.checkExcelFile(file);
        return iotEvseBizService.parseEvseExcel(file);
    }

    @Operation(summary = "批量导入桩")
    @PostMapping("/api/evse/batchImport")
    public BaseResponse batchImport(@RequestBody List<EvseModelVo> param) {
        log.info("batchImport param.size = {}", param.size());
        return iotEvseBizService.batchImport(param);
    }

    @Operation(summary = "桩远程重启")
    @GetMapping(value = "/api/evse/reboot")
    public BaseResponse rebootEvse(@RequestParam("evseNo") String evseNo) {
        log.info("rebootEvse evseNo = {}", evseNo);
        return iotEvseBizService.rebootEvse(evseNo);
    }

    @Operation(summary = "软件版本下拉框数据")
    @PostMapping(value = "/api/evse/getFirmwareVerList")
    public ListResponse<String> getFirmwareVerList(@RequestBody BaseListParam param) {
        return iotEvseBizService.getFirmwareVerList(param);
    }


}
