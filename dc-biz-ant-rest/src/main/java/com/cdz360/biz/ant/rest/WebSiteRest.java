package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.ant.utils.RedisUtil;
import com.cdz360.biz.model.cus.website.dto.CusExpectDto;
import com.cdz360.biz.utils.feign.website.CusExpectClient;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "官网相关接口")
public class WebSiteRest {

    @Value("${cus.expect.ip.times:5}")
    private Integer ipPerMinutes;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private CusExpectClient cusExpectClient;

    @Operation(summary = "客户意向新增")
    @PostMapping(value = "/api/website/addCusExpect")
    public Mono<BaseResponse> addCusExpect(ServerHttpRequest request,
                                           @RequestBody CusExpectDto dto) {
        log.info("客户意向新增: po = {}", dto);

        if (StringUtils.isBlank(dto.getName())) {
            throw new DcArgumentException("请您输入有效的姓名");
        }

        if (StringUtils.isBlank(dto.getPhone())) {
            throw new DcArgumentException("请您输入有效的联系电话");
        }

        if (StringUtils.isBlank(dto.getProvince()) || StringUtils.isBlank(dto.getCity())) {
            throw new DcArgumentException("请您选择有效的城市信息");
        }

        if (CollectionUtils.isEmpty(dto.getContext())) {
            throw new DcArgumentException("请您选择当前情况或期望");
        }

        if (StringUtils.isBlank(dto.getMind())) {
            throw new DcArgumentException("请留下您宝贵的想法");
        }

        // 小黑屋限制: 10分钟内允许5次，被关时间(1小时)
        String ip = AntRestUtils.getIpAddress(request);
        Object isBlock = redisUtil.get("cus.expect.blacklist.ip", ip);
        if (isBlock != null) {
            log.warn("ip is blocked: ip = {}", ip);
            return Mono.just(RestUtils.success());
        }

        int times = NumberUtils.parseInt(redisUtil.get("cus.expect.ip." + ip), 0);
        if (times >= ipPerMinutes) {
            redisUtil.put("cus.expect.blacklist.ip", ip, 60 * 60);
            log.error("cus.expect.ip is blocked: {}", ip);
            return Mono.just(RestUtils.success());
        } else {
            times = times + 1;
            redisUtil.put("cus.expect.ip." + ip, String.valueOf(times), 10 * 60);
        }

        return cusExpectClient.addCusExpect(dto);
    }
}
