package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.UserType;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.constant.AppletConstants;
import com.cdz360.biz.ant.service.AccountService;
import com.cdz360.biz.ant.service.AuthCenterService;
import com.cdz360.biz.ant.service.BalanceService;
import com.cdz360.biz.ant.service.MerchantBalanceService;
import com.cdz360.biz.ant.service.UserService;
import com.cdz360.biz.ant.service.order.ChargerOrderService;
import com.cdz360.biz.ant.service.sysLog.CommercialSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.CommercialSample;
import com.chargerlinkcar.framework.common.domain.CorpListPointLogParam;
import com.chargerlinkcar.framework.common.domain.PointLog;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.domain.param.MerchantBalanceParam;
import com.chargerlinkcar.framework.common.domain.param.MerchantEnableParam;
import com.chargerlinkcar.framework.common.domain.param.MerchantUpdateParam;
import com.chargerlinkcar.framework.common.domain.vo.AccountInfoVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.CommCusRef;
import com.chargerlinkcar.framework.common.domain.vo.PointPoVo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.service.DcCusBalanceService;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.math.BigDecimal;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;

/**
 * <AUTHOR>
 * @since 2019/08/08
 */
@Slf4j
@RestController
@Tag(name = "商户会员相关接口", description = "MerchantBalance")
@RequestMapping("/api/merchantBalance")
public class MerchantBalanceRest extends BaseController {

    @Autowired
    private MerchantBalanceService merchantBalanceService;
    @Autowired
    private CommercialSysLogService commercialSyslogService;

    @Autowired
    private DcCusBalanceService dcCusBalanceService;

    @Autowired
    private AccountService accountService;

    @Autowired
    private AuthCenterService iAuthCenterService;

    @Autowired
    private ChargerOrderService chargerOrderService;

    @Autowired
    private BalanceService balanceService;

    @Autowired
    private UserService userService;

    /**
     * 新增商户会员
     *
     * @param param 入参
     * @return
     */
    @Operation(summary = "新增商户会员")
    @PostMapping(value = "/initMerchantBalance")
    public BaseResponse initMerchantBalance(ServerHttpRequest request,
        @RequestBody MerchantBalanceParam param) {
        log.info("商户会员初始化param:{}", JsonUtils.toJsonString(param));

        if (ObjectUtils.isEmpty(param.getPhone()) || ObjectUtils.isEmpty(param.getSubCommId())
            || ObjectUtils.isEmpty(param.getAmount())) {
            throw new DcArgumentException("参数错误");
        }

        CommercialSample commercialSample = this.getCommercialSample2(request);
        if (ObjectUtils.isEmpty(commercialSample)) {
            throw new DcArgumentException("参数错误");
        }
        Long commId = commercialSample.getComId();
        if (commId == null || commId <= 0) {
            throw new DcArgumentException("参数错误");
        }
        Long merchantId = commercialSample.getId();
        if (merchantId == null || merchantId <= 0) {
            throw new DcArgumentException("参数错误");
        }

        Long opUid = commercialSample.getId();
        if (opUid == null || opUid <= 0) {
            throw new DcArgumentException("当前操作用户Id不存在");
        }

        // 操作人信息
        param.setTopCommId(commercialSample.getTopCommId());
        param.setOpUid(opUid);
        param.setOpName(commercialSample.getUsername());
        param.setOpUserType(UserType.SYS_USER);

        BaseResponse baseResponse = merchantBalanceService.initMerchantBalance(param);
        commercialSyslogService.initMerchantBalanceLog(param.getPhone(), param.getSubCommId(),
            request);
        return baseResponse;
    }

    /**
     * 禁用、启用商户会员
     *
     * @param param 入参
     * @return
     */
    @Operation(summary = "启用禁用商户会员")
    @PostMapping(value = "/disableOrEnable")
    public BaseResponse disableOrEnable(ServerHttpRequest request,
        @RequestBody MerchantEnableParam param) {
        log.info("商户会员启用禁用账户param:{}", JsonUtils.toJsonString(param));

        if (ObjectUtils.isEmpty(param.getUserId()) || ObjectUtils.isEmpty(param.getSubCommId())
            || ObjectUtils.isEmpty(param.getStatus())
            || ObjectUtils.isEmpty(param.getPhone())) {
            throw new DcArgumentException("参数错误");
        }
        Long topCommId = AntRestUtils.getTopCommId(request);
        if (topCommId == null || topCommId.longValue() < 1L) {
            log.error("参数错误,缺少参数topCommId");
            throw new DcArgumentException("参数错误,缺少参数topCommId");
        }
        BaseResponse baseResponse = merchantBalanceService.disableOrEnable(param.getUserId(),
            topCommId,
            param.getSubCommId(), param.getStatus(), param.getDefaultPayType());
        commercialSyslogService.disableOrEnableLog(param.getPhone(), param.getSubCommId(), request);
        return baseResponse;
    }

    /**
     * 编辑商户会员名称
     *
     * @param param 入参
     * @return
     */
    @Operation(summary = "编辑商户会员名称")
    @PostMapping(value = "/updateMerchantName")
    public BaseResponse updateMerchantName(ServerHttpRequest request,
        @RequestBody MerchantUpdateParam param) {
        log.info("商户会员编辑param:{}", JsonUtils.toJsonString(param));

        if (ObjectUtils.isEmpty(param.getUserId()) || ObjectUtils.isEmpty(param.getUserName())) {
            throw new DcArgumentException("参数错误");
        }
        Long topCommId = AntRestUtils.getTopCommId(request);
        if (topCommId == null || topCommId.longValue() < 1L) {
            log.error("参数错误,缺少参数topCommId");
            throw new DcArgumentException("参数错误,缺少参数topCommId");
        }
        BaseResponse baseResponse = merchantBalanceService.updateMerchantName(param.getUserId(),
            topCommId, param.getUserName(), param.getCommId());
        return baseResponse;
    }

    /**
     * 商户手动更新商户会员余额
     *
     * @param param 入参
     * @return
     */
    @RequestMapping(value = "/updateMerchantBalance", method = RequestMethod.POST)
    public BaseResponse updateBalance(@RequestBody MerchantBalanceParam param,
        ServerHttpRequest request) {
        log.info("商户会员手动充值param:{}", JsonUtils.toJsonString(param));
        if (ObjectUtils.isEmpty(param.getUserId()) || ObjectUtils.isEmpty(param.getSubCommId())
            || ObjectUtils.isEmpty(param.getAmount())) {
            throw new DcArgumentException("参数错误");
        }

        List<Long> commIdList = this.getCommIdList2(request);
        if (org.springframework.util.CollectionUtils.isEmpty(commIdList)) {
            throw new DcArgumentException("参数错误");
        }

        CommercialSample commercialSample = this.getCommercialSample2(request);
        if (ObjectUtils.isEmpty(commercialSample)) {
            throw new DcArgumentException("参数错误");
        }
        Long topCommId = AntRestUtils.getTopCommId(request);
        if (topCommId == null || topCommId.longValue() < 1L) {
            log.error("参数错误,缺少参数topCommId");
            throw new DcArgumentException("参数错误,缺少参数topCommId");
        }
        //集团商户id
        Long commId = commercialSample.getComId();
        if (commId == null || commId <= 0) {
            throw new DcArgumentException("参数错误");
        }
        //操作用户id
        Long merchantId = commercialSample.getId();
        if (merchantId == null || merchantId <= 0) {
            throw new DcArgumentException("参数错误");
        }

        if (DecimalUtils.gt(param.getAmount(), BigDecimal.valueOf(20000000))) {
            throw new DcArgumentException("请输入合理的金额");
        }

        BaseResponse baseResponse = merchantBalanceService.updateMerchantBalance(param.getUserId(),
            topCommId, param.getSubCommId(), param.getAmount(),
            param.getTransType(), param.getReason(), param.getRemark());
        return baseResponse;
    }


    /**
     * 商户会员流水
     *
     * @param request
     * @return
     */
    @GetMapping(value = "/queryMerchantBalanceLog")
    public ListResponse<PointLog> queryBalanceLog(ServerHttpRequest request,
        ServerWebExchange exh,
        @RequestParam(value = "accountId", required = true) Long accountId) {

        String token = getToken2(request);
        Long userId = accountService.getUserIdByToken(token);
        String appCommIdStr = request.getHeaders()
            .getFirst(AppletConstants.CURRENT_USER_APP_COMM_ID);

        if (userId == null || userId <= 0) {
            log.error("[token:" + token + ", userId is null]  >>>>> reqest end");
            throw new DcServiceException("获取用户信息失败");
        }
        Long topCommId = AntRestUtils.getTopCommId(request);
        if (topCommId == null || topCommId.longValue() < 1L) {
            log.error("参数错误,缺少参数topCommId");
            throw new DcArgumentException("参数错误,缺少参数topCommId");
        }

        if (StringUtils.isBlank(appCommIdStr)) {
            throw new DcArgumentException("参数错误");
        }

        Long appCommId = Long.valueOf(appCommIdStr);

        if (accountId == null || accountId <= 0) {
            log.error("[token:" + token + ", userId is null]  >>>>> reqest end");
            throw new DcServiceException("账户参数错误");
        }

        OldPageParam page = getPage2(request, exh, false);

        log.info(LoggerHelper2.formatEnterLog(request) + "查询账户列表 userId: {},appCommId: {}"
            , userId, appCommId);

        CorpListPointLogParam param = new CorpListPointLogParam();
        param.setTopCommId(topCommId)
            .setCommId(appCommId)
            .setUid(userId)
            .setPid(accountId);

        if (page != null) {
            long start = page.getPageNum() == 0 ? 0 : (page.getPageNum() - 1) * page.getPageSize();
            Integer size = page.getPageSize();
            param.setStart(start);
            param.setSize(size);
        }

        param.setPayAccountType(PayAccountType.COMMERCIAL);
        ListResponse<PointLog> ObjectResponse = dcCusBalanceService.listPointLog(param);

        return ObjectResponse;
    }


    /**
     * 订单列表（包含在线订单和离线订单和异常订单）
     *
     * @return headerparam commIdList
     */
    @RequestMapping("/queryChargeOrderList")
    public ListResponse<ChargerOrderVo> queryChargeOrderList(ServerHttpRequest request,
        ServerWebExchange exh,
        @RequestParam(value = "accountId", required = true) Long accountId) {
        ChargerOrderParam searchParam = new ChargerOrderParam();

        OldPageParam page = getPage2(request, exh, false);
        //searchParam.setCommIdList(commIdList);
        searchParam.setSiteCommIdChain(super.getCommIdChain2(request));
        searchParam.setCurrent(page.getPageNum());
        searchParam.setSize(page.getPageSize());

        return chargerOrderService.queryChargeOrderList(searchParam);
    }

    /**
     * 根据账户类型查询账户（商户会员）
     *
     * @param request
     * @param phone     手机号
     * @param subCommId 所属商户/子商户id(非必填)
     * @param enable    状态（1启用，0禁用）(非必填)
     * @return
     */
    @GetMapping(value = "/queryMerchantBalanceList")
    @Operation(summary = "根据账户类型查询账户（商户会员）")
    public ListResponse<PointPoVo> queryPointPoByType(ServerHttpRequest request,
        ServerWebExchange exh,
        @Parameter(name = "用户手机号") @RequestParam(value = "cusPhone", required = false) String phone,
        @Parameter(name = "所属商户id") @RequestParam(value = "subCommId", required = false) Long subCommId,
        @Parameter(name = "用户id") @RequestParam(value = "cusId", required = false) Long cusId,
        @Parameter(name = "用户姓名") @RequestParam(value = "cusName", required = false) String cusName,
        @Parameter(name = "状态（1启用，0禁用）") @RequestParam(value = "enable", required = false) Boolean enable,
        @Parameter(name = "用于转换成idChain") @RequestParam(value = "leaderCommId", required = false) Long leaderCommId) {

        log.info(LoggerHelper2.formatEnterLog(request));
        Long topCommId = AntRestUtils.getTopCommId(request);
        if (topCommId == null || topCommId.longValue() < 1L) {
            log.error("参数错误,缺少参数topCommId");
            throw new DcArgumentException("参数错误,缺少参数topCommId");
        }

        String commIdChain = super.getCommIdChain2(request);
        if (leaderCommId != null) {
            ObjectResponse<Commercial> commercial = commercialFeignClient.getCommercial(
                leaderCommId);
            FeignResponseValidate.check(commercial);
            commIdChain = commercial.getData().getIdChain();
            log.debug("leaderCommId -> idChain: {} -> {}", leaderCommId,
                commercial.getData().getIdChain());
        }

        OldPageParam page = getPage2(request, exh, false);
        log.info("查询账户列表 phone: {},commId: {},enable: {},commIdChain: {}",
            phone, subCommId, enable, commIdChain);

        ListResponse<PointPoVo> res = balanceService.queryPointPoByType(page,
            phone, topCommId, subCommId, cusId, cusName, enable,
            PayAccountType.COMMERCIAL, commIdChain);

        return res;

    }

    /**
     * 查询商户会员详情信息
     *
     * @param userId    用户id
     * @param subCommId 所属商户id
     * @return
     */
    @GetMapping(value = "/queryMerchantBalance")
    @Operation(summary = "查询账户详情（商户会员）")
    public ObjectResponse<PointPoVo> findMerchantBalance(ServerHttpRequest request,
        @Parameter(name = "用户id") @RequestParam(value = "userId") long userId,
        @Parameter(name = "所属商户id") @RequestParam(value = "subCommId") long subCommId) {
        log.info(LoggerHelper2.formatEnterLog(request) + "查询账户详情 userId: {}, subCommId",
            userId, subCommId);
        Long topCommId = AntRestUtils.getTopCommId(request);
        if (topCommId == null || topCommId.longValue() < 1L) {
            log.error("参数错误,缺少参数topCommId");
            throw new DcArgumentException("参数错误,缺少参数topCommId");
        }
        ObjectResponse<PointPoVo> res = merchantBalanceService.findMerchantBalance(userId,
            topCommId, subCommId);
        log.info("查询账户详情的结果{}", res);
        return res;

    }

    /**
     * 商户会员流水
     *
     * @param request
     * @return
     */
    @Operation(summary = "商户会员流水")
    @GetMapping(value = "/queryCommercialBalanceLog")
    public ListResponse<PointLog> queryCommercialBalanceLog(ServerHttpRequest request,
        ServerWebExchange exh,
        @Parameter(name = "用户id") @RequestParam(value = "userId", required = true) long userId,
        @Parameter(name = "所属商户id") @RequestParam(value = "subCommId", required = true) long subCommId) {

        if (userId <= 0) {
            log.error("[userId is null]  >>>>> reqest end");
            throw new DcArgumentException("用户id参数错误");
        }

        if (subCommId <= 0) {
            throw new DcArgumentException("所属商户id参数错误");
        }
        Long topCommId = AntRestUtils.getTopCommId(request);
        if (topCommId == null || topCommId.longValue() < 1L) {
            log.error("参数错误,缺少参数topCommId");
            throw new DcArgumentException("参数错误,缺少参数topCommId");
        }
        OldPageParam page = getPage2(request, exh, false);

        log.info(LoggerHelper2.formatEnterLog(request) + "查询账户列表 userId: {}, subCommId: {}"
            , userId, subCommId);

        CorpListPointLogParam param = new CorpListPointLogParam();
        param.setTopCommId(topCommId)
            .setCommId(subCommId)
            .setUid(userId);

        if (page != null) {
            long start = page.getPageNum() == 0 ? 0 : (page.getPageNum() - 1) * page.getPageSize();
            Integer size = page.getPageSize();
            param.setStart(start);
            param.setSize(size);
        }

        param.setPayAccountType(PayAccountType.COMMERCIAL);
        ListResponse<PointLog> ObjectResponse = dcCusBalanceService.listPointLog(param);

        return ObjectResponse;
    }

    /**
     * 查询单个商户会员的信息
     *
     * @param userId
     * @return
     */
    @ResponseBody
    @GetMapping("/getCommercialAccountByUser")
    @Operation(summary = "获取商户会员的信息")
    public ObjectResponse<AccountInfoVo> getCommercialAccountByUser(
        @Parameter(name = "用户id") @RequestParam Long userId,
        @Parameter(name = "账户标识") @RequestParam Long payAccountId,
        ServerHttpRequest request) {
        log.info("查询商户会员的信息uId:{},payAccountId:{}", userId, payAccountId);
        Long topCommId = AntRestUtils.getTopCommId(request);
        if (topCommId == null || topCommId.longValue() < 1L) {
            log.error("参数错误,缺少参数topCommId");
            throw new DcArgumentException("参数错误,缺少参数topCommId");
        }
        String commIdChain = super.getCommIdChain2(request);
        ObjectResponse<AccountInfoVo> res = merchantBalanceService.getCommercialAccountByUser(
            topCommId, commIdChain, userId, payAccountId);
        log.info("查询商户会员的信息结果:{}", res);
        return res;
    }

    @PostMapping(value = "/countByCondition")
    public ObjectResponse<Long> countByCondition(@RequestBody CommCusRef param) {
        return merchantBalanceService.countByCondition(param);
    }

}
