package com.cdz360.biz.ant.rest.sys;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.service.sys.SysRoleService;
import com.cdz360.biz.auth.sys.param.BatchAddRoleUserParam;
import com.cdz360.biz.auth.sys.param.RoleUserListParam;
import com.cdz360.biz.auth.sys.param.RoleUserUpdateParam;
import com.cdz360.biz.auth.sys.vo.RoleUserVo;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/sys/role")
public class SysRoleRest {

    @Autowired
    private SysRoleService sysRoleService;

    @Operation(summary = "批量修改角色关联的账户")
    @PostMapping("/batchUpdateRoleUser")
    public BaseResponse batchUpdateRoleUser(@RequestBody RoleUserUpdateParam params) {
        log.info("批量修改角色关联的账户,params={}", JsonUtils.toJsonString(params));
        return sysRoleService.batchUpdateRoleUser(params);
    }

    @Operation(summary = "角色批量新增账号")
    @PostMapping("/batchAddRoleUser")
    public BaseResponse batchAddRoleUser(@RequestBody BatchAddRoleUserParam params) {
        log.info("批量新增账号，params={}",JsonUtils.toJsonString(params));
        return sysRoleService.batchAddRoleUser(params);
    }

    @Operation(summary = "获取角色尚未绑定的账号")
    @GetMapping("/getUserByRoleId")
    public ListResponse<RoleUserVo> getUserByRoleId(@RequestParam("keyWord") String keyWord,
                                                    @RequestParam("platform") Long platform,
                                                    @RequestParam("roleId") Long roleId,
                                                    @RequestParam("size") Long size) {
        log.info("获取尚未绑定该角色账号信息:keyWord={},platform={},roleId={},size={}", keyWord, platform, roleId, size);
        return sysRoleService.getUserByRoleId(keyWord,platform,roleId,size);
    }

    @Operation(summary = "角色绑定账号列表")
    @PostMapping("/getUserListByRoleId")
    public ListResponse<RoleUserVo> getUserListByRoleId(@RequestBody RoleUserListParam params) {
        log.info("角色绑定的管理平台账号,params={}", JsonUtils.toJsonString(params));
        return sysRoleService.getUserListByRoleId(params);
    }
}
