package com.cdz360.biz.ant.rest.parts;

import com.cdz360.biz.ant.service.parts.PartsTransService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@Tag(name = "物料调拨相关接口", description = "物料调拨")
public class PartsTransRest {

    @Autowired
    private PartsTransService partsTransService;
}
