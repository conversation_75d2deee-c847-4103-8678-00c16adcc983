package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.cus.invoice.DcInvoice;
import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class InvoiceUserFeignClientHystrixFactory implements FallbackFactory<InvoiceUserFeignClient> {
    @Override
    public InvoiceUserFeignClient create(Throwable cause) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER, cause.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER, cause.getStackTrace());

        return new InvoiceUserFeignClient() {
            @Override
            public ListResponse<DcInvoice> queryUserList(
                    String keyword, Integer status, Integer index, Integer size, Long topCommId, String commIdChain) {
                return RestUtils.serverBusy4ListResponse();
            }
        };
    }
}
