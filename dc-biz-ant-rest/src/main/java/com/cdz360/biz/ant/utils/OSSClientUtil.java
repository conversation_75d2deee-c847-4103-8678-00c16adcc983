package com.cdz360.biz.ant.utils;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectResult;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.model.trading.file.po.OssFilePo;
import com.cdz360.biz.model.trading.file.type.FileType;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Mono;

/**
 * 阿里云 OSS文件类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class OSSClientUtil {


    /**
     * 空间
     */
    @Getter
    @Value("${oss.sts.endpoint}")
    private String endpoin;

    @Getter
    @Value("${oss.sts.accessKeyId}")
    private String accessKeyId;

    @Getter
    @Value("${oss.sts.accessKeySecret}")
    private String accessKeySecret;

    @Getter
    @Value("${oss.sts.bucketName}")
    private String bucketName;

    /**
     * 文件存储目录
     */
    private String filedir = "";

    private OSSClient ossClient;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;


    public OSSClientUtil() {
    }

    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        log.info("oss初始化信息：endpoin:{},  accessKeyId:{}, accessKeySecret:{}", endpoin,
            accessKeyId, accessKeySecret);
        ossClient = new OSSClient(endpoin, accessKeyId, accessKeySecret);
    }

    /**
     * 销毁
     */
    public void destory() {
        ossClient.shutdown();
    }


    public Mono<ObjectResponse<Map<String, Object>>> uploadImg2Oss(FilePart file) {
        String originalFilename = file.filename();
        String substring = originalFilename.substring(originalFilename.lastIndexOf("."))
            .toLowerCase();
        Random random = new Random();
        String name = random.nextInt(10000) + System.currentTimeMillis() + substring;
        Mono<ObjectResponse<Map<String, Object>>> m;
        try {
            File f = new File("/tmp/" + file.filename());
            m = file.transferTo(f)
                .then(Mono.just("文件上传成功"))
                .map(a -> {
                    try {

                        FileInputStream img = new FileInputStream(f);
                        int imgLength = img.available();
//                            if (imgLength> 1024*1024) {
//                                throw  new DcServiceException("图片最大上传1M");
//                            }
                        ByteArrayOutputStream out = new ByteArrayOutputStream();
                        Thumbnails.of(img).scale(1f).outputQuality(1f).toOutputStream(out);
                        ByteArrayInputStream swapStream = new ByteArrayInputStream(
                            out.toByteArray());
                        this.uploadFile2OSS(swapStream, name, false);
                        Map<String, Object> map = new HashMap<>();
                        map.put("name", name);
                        String imgUrl = this.getImgUrl(name);
                        map.put("url", imgUrl);
                        return map;
                    } catch (IOException e) {
                        log.error("上传文件失败: {}", e.getMessage(), e);
                        throw new DcServiceException("文件上传失败");
                    }
                })
                .map(a -> RestUtils.buildObjectResponse(a));
        } catch (Exception e) {
            log.warn("msg: {}", e.getMessage(), e);
            throw new OSSException("图片上传失败");
        }
        return m;
    }


    public Mono<ObjectResponse<OssFilePo>> uploadFile(FilePart file, boolean forceDownload) {
        String originalFilename = file.filename();
        String substring = originalFilename.substring(originalFilename.lastIndexOf("."))
            .toLowerCase();
        String realName = originalFilename.substring(0, originalFilename.lastIndexOf("."));
        Random random = new Random();
        String name = random.nextInt(10000) + System.currentTimeMillis() + substring;
        Mono<ObjectResponse<OssFilePo>> m;
        try {
            File f = new File("/tmp/" + file.filename());
            m = file.transferTo(f)
                .then(Mono.just("文件上传成功"))
                .map(a -> {
                    try {
                        FileInputStream fileInputStream = new FileInputStream(f);
                        this.uploadFile2OSS(fileInputStream, name, forceDownload);
                        String imgUrl = this.getImgUrl(name);
                        OssFilePo ossFilePo = new OssFilePo();
                        ossFilePo.setFileName(name)
                            .setRealName(realName)
                            .setType(FileType.FILE)
                            .setPath(imgUrl);
                        return dataCoreFeignClient.addFile(ossFilePo);
                    } catch (IOException e) {
                        log.error("上传文件失败.....");
                        throw new DcServiceException("文件上传失败");
                    }
                });
        } catch (Exception e) {
            log.warn("msg: {}", e.getMessage(), e);
            throw new OSSException("文件上传失败");
        }
        return m;
    }

    /**
     * 获得图片路径
     *
     * @param fileUrl
     * @return
     */
    public String getImgUrl(String fileUrl) {
        if (!StringUtils.isEmpty(fileUrl)) {
            String[] split = fileUrl.split("/");
            return this.getUrl(this.filedir + split[split.length - 1]);
        }
        return null;
    }

    /**
     * 上传到OSS服务器  如果同名文件会覆盖服务器上的
     *
     * @param instream      文件流
     * @param fileName      文件名称 包括后缀名
     * @param forceDownload 强制在浏览器打开连接时提示文件下载
     * @return 出错返回"" ,唯一MD5数字签名
     * @return
     */
    public String uploadFile2OSS(InputStream instream, String fileName, boolean forceDownload) {
        return uploadFile2OSS(instream, fileName, forceDownload ? "attachement" : "inline");
    }

    private String uploadFile2OSS(InputStream instream, String fileName, String disposition) {
        String ret = "";
        try {
            //创建上传Object的Metadata
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentLength(instream.available());
            objectMetadata.setCacheControl("no-cache");
            objectMetadata.setHeader("Pragma", "no-cache");
            objectMetadata.setContentType(
                getcontentType(fileName.substring(fileName.lastIndexOf("."))));
            objectMetadata.setContentDisposition(disposition + ";filename=" + fileName);
            //上传文件
            PutObjectResult putResult = ossClient.putObject(
                bucketName, fileName, instream, objectMetadata);
            ret = putResult.getETag();
            log.info("OSS :: {}", ret);
        } catch (IOException e) {
            log.error(e.getMessage(), e);
        } finally {
            try {
                if (instream != null) {
                    instream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return ret;
    }

    /**
     * 图片上传
     *
     */
    public Boolean uploadFile(InputStream instream, String fileName) throws Exception {
        OSSClient ossClient = null;
        try {
            ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentLength(instream.available());
            objectMetadata.setCacheControl("no-cache");
            objectMetadata.setHeader("Pragma", "no-cache");
            objectMetadata.setContentDisposition("inline;filename=" + fileName);
            ossClient = new OSSClient(endpoin, accessKeyId, accessKeySecret);
            PutObjectResult putResult = ossClient.putObject(bucketName, "/chargerlink" + fileName,
                instream, objectMetadata);
            log.info("PutResultCode----------------{}", putResult.getETag());
            return true;
        } catch (Exception e) {
            log.error("图片上传失败-------{}", e.getMessage());
        } finally {
            if (ossClient != null) {
                ossClient.shutdown();
            }
            instream.close();
        }
        return false;
    }


    /**
     * Description: 判断OSS服务文件上传时文件的contentType
     *
     * @param filenameExtension 文件后缀
     * @return String
     */
    public static String getcontentType(String filenameExtension) {
        if (".bmp".equalsIgnoreCase(filenameExtension)) {
            return "image/bmp";
        }
        if (".gif".equalsIgnoreCase(filenameExtension)) {
            return "image/gif";
        }
        if (".jpeg".equalsIgnoreCase(filenameExtension) ||
            ".jpg".equalsIgnoreCase(filenameExtension) ||
            ".png".equalsIgnoreCase(filenameExtension)) {
            return "image/jpeg";
        }
        if (".html".equalsIgnoreCase(filenameExtension)) {
            return "text/html";
        }
        if (".txt".equalsIgnoreCase(filenameExtension)) {
            return "text/plain";
        }
        if (".vsd".equalsIgnoreCase(filenameExtension)) {
            return "application/vnd.visio";
        }
        if (".pptx".equalsIgnoreCase(filenameExtension) ||
            ".ppt".equalsIgnoreCase(filenameExtension)) {
            return "application/vnd.ms-powerpoint";
        }
        if (".docx".equalsIgnoreCase(filenameExtension) ||
            ".doc".equalsIgnoreCase(filenameExtension)) {
            return "application/msword";
        }
        if (".xml".equalsIgnoreCase(filenameExtension)) {
            return "text/xml";
        }
        if (".pdf".equalsIgnoreCase(filenameExtension)) {
            return "application/pdf";
        }
        return "image/jpeg";
    }

    /**
     * 获得url链接
     *
     * @param key
     * @return
     */
    public String getUrl(String key) {
        // 设置URL过期时间为10年  3600l* 1000*24*365*10
        Date expiration = new Date(System.currentTimeMillis() + 3600L * 1000 * 24 * 365 * 10);
        // 生成URL
        URL url = ossClient.generatePresignedUrl(bucketName, key, expiration);
        if (url != null) {
            return url.toString();
        }
        return null;
    }


}