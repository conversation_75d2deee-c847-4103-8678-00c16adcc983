package com.cdz360.biz.ant.service.site;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.model.site.dto.UpdateSiteInvoicedValidDto;
import com.cdz360.biz.utils.feign.site.SiteDataCoreFeignClient;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class SiteInvoiceService {

    @Autowired
    private SiteDataCoreFeignClient siteDataCoreFeignClient;

    public Mono<BaseResponse> updateInvoicedValid(UpdateSiteInvoicedValidDto dto) {
        IotAssert.isTrue(CollectionUtils.isNotEmpty(dto.getSiteIdList()), "请提供场站ID");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(dto.getSiteNameList()), "请提供场站名称");
        IotAssert.isNotNull(dto.getInvoicedValid(), "请选择移动端开票状态");
//        IotAssert.isNotNull(dto.getPlatformInvoicedValid(), "请选择平台开票状态");
        if (dto.getInvoicedValid()) {
            IotAssert.isNotNull(dto.getMobileTempSalId(), "请选择移动端开票主体");
        }
        return siteDataCoreFeignClient.updateSiteInvoicedValid(dto);
    }
}
