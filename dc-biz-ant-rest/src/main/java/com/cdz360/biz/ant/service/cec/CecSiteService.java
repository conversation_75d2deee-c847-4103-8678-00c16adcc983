package com.cdz360.biz.ant.service.cec;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.ChargePriceCategory;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.feign.reactor.ReactorSiteDataCoreFeignClient;
import com.cdz360.biz.model.iot.vo.ChargeV2;
import com.cdz360.biz.model.trading.hlht.dto.CecPolicyInfo;
import com.cdz360.biz.model.trading.site.param.ListCecSiteParam;
import com.cdz360.biz.model.trading.site.vo.PartnerSiteVo;
import com.cdz360.biz.model.trading.site.vo.SiteOrderStatsVo;
import com.cdz360.biz.utils.feign.hlht.OpenHlhtFeignClient;
import com.chargerlinkcar.framework.common.domain.PartnerSiteDetailInfoVo;
import com.chargerlinkcar.framework.common.domain.PlugStatusCountDto;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CecSiteService {

    @Autowired
    private ReactorSiteDataCoreFeignClient reactorSiteDataCoreFeignClient;

    @Autowired
    private OpenHlhtFeignClient openHlhtFeignClient;

    public Mono<ListResponse<PartnerSiteVo>> cecSiteList(ListCecSiteParam param) {
        return reactorSiteDataCoreFeignClient.cecSiteList(param);
    }

    public Mono<ObjectResponse<PartnerSiteDetailInfoVo>> cecSiteDetail(String siteId) {
        return reactorSiteDataCoreFeignClient.cecSiteDetail(siteId);
    }

    public Mono<ObjectResponse<PlugStatusCountDto>> cecSitePlugStatusStats(Long topCommId, String partnerCode, String siteId) {
        log.debug("topCommId = {}", topCommId);
        Mono<ListResponse<PlugVo>> plugList = openHlhtFeignClient.getPlugList(topCommId, partnerCode, siteId);

        return plugList.doOnNext(FeignResponseValidate::check)
                .map(ListResponse::getData)
                .flatMap(list -> {
                    long idle = list.stream().filter(p -> PlugStatus.IDLE.equals(p.getStatus())).count();
                    long connect = list.stream().filter(p -> PlugStatus.CONNECT.equals(p.getStatus())).count();
                    long busy = list.stream().filter(p -> PlugStatus.BUSY.equals(p.getStatus())).count();
                    long join = list.stream().filter(p -> PlugStatus.JOIN.equals(p.getStatus())).count();
                    long err = list.stream().filter(p -> PlugStatus.ERROR.equals(p.getStatus())).count();
                    long stop = list.stream().filter(p -> PlugStatus.RECHARGE_END.equals(p.getStatus())).count();
                    long offline = list.stream().filter(p -> PlugStatus.OFFLINE.equals(p.getStatus())).count();
                    long off = list.stream().filter(p -> PlugStatus.OFF.equals(p.getStatus())).count();
                    long unknown = list.size() - (idle + connect + busy + join + err + stop + offline + off);

                    PlugStatusCountDto dto = new PlugStatusCountDto();
                    dto.setUnknownCount((int) unknown);
                    dto.setIdleCount((int) idle);
                    dto.setConnectCount((int) connect);
                    dto.setBusyCount((int) busy);
                    dto.setJoinCount((int) join);
                    dto.setErrorCount((int) err);
                    dto.setRechargeEndCount((int) stop);
                    dto.setOfflineCount((int) offline);
                    dto.setOffCount((int) off);
                    return Mono.just(RestUtils.buildObjectResponse(dto));
                });
    }

    public Mono<ListResponse<SiteOrderStatsVo>> cecSiteOrderStats(String siteId) {
        return reactorSiteDataCoreFeignClient.siteOrderStats(siteId);
    }

    public Mono<ListResponse<PlugVo>> cecPlugList(Long topCommId, String partnerCode, String siteId) {
        return openHlhtFeignClient.getPlugList(topCommId, partnerCode, siteId);
    }

    public Mono<ListResponse<ChargeV2>> cecPlugPriceInfo(
            Long topCommId, String partnerCode, String plugNo) {
        String[] split = plugNo.split(":");
        String connectorId = split[split.length - 1];
        return openHlhtFeignClient.getPriceInfo(topCommId, partnerCode, connectorId)
                .doOnNext(FeignResponseValidate::check)
                .doOnNext(res -> {
                    if (res.getData().getSuccStat() != 0) {
                        Integer failReason = res.getData().getFailReason();
                        String reason = failReason == 1 ? "业务策略不存在" : (failReason == 2 ? "参数错误" : "未知原因");
                        log.warn("获取枪头计费信息失败: fail = {}, reason = {}", failReason, reason);
                        throw new DcServiceException("获取枪头计费信息失败，请稍后操作");
                    }
                })
                .flatMap(res -> {
                    res.getData().setFullPlugNo(plugNo);
                    return reactorSiteDataCoreFeignClient.syncHlhtTemplate(res.getData()).map(r -> {
                        return res;
                    });
                })
                .map(res -> this.toPriceItemList(res.getData().getPolicyInfos()))
                .map(RestUtils::buildListResponse);
    }

    private List<ChargeV2> toPriceItemList(List<CecPolicyInfo> policyInfos) {
        List<ChargeV2> collect = policyInfos.stream().map(this::toPriceItemList)
                .sorted(Comparator.comparing(ChargeV2::getStartTime))
                .collect(Collectors.toList());

        String last = "24:00";
        for(int i = collect.size(); i > 0; i--) {
            collect.get(i - 1).setStopTime(last);
            last = collect.get(i - 1).getStartTime();
        }

        return collect;
    }

    private ChargeV2 toPriceItemList(CecPolicyInfo policy) {
        ChargeV2 v2 = new ChargeV2();
        v2.setElecPrice(policy.getElecPrice())
                .setServPrice(policy.getSevicePrice());
        v2.setCategory(ChargePriceCategory.PRICE_TAG_OTHER);
        v2.setStartTime(HHmmss2HHmm(policy.getStartTime()));
        return v2;
    }

    private String HHmmss2HHmm(String time) {
        StringBuilder builder = new StringBuilder(time.substring(0, time.length() - 2));
        builder.insert(2, ':');
        return builder.toString();
    }
}
