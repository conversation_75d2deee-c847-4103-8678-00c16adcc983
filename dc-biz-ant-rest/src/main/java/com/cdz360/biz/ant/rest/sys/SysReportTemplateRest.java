package com.cdz360.biz.ant.rest.sys;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.model.sys.po.SysUserReportTemplatePo;
import com.chargerlinkcar.framework.common.feign.SysReportTemplateFeignClient;
import com.chargerlinkcar.framework.common.rest.BaseController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@Tag(name = "报表自定义模板", description = "报表自定义模板")
@RequestMapping(value = "/api/sys/reportTemplate", produces = MediaType.APPLICATION_JSON_VALUE)
public class SysReportTemplateRest extends BaseController {

    @Autowired
    private SysReportTemplateFeignClient client;

    @GetMapping(value = "/getInfo")
    @Operation(summary = "获取模板信息")
    public ListResponse<SysUserReportTemplatePo> getInfo(@RequestParam(value = "sysUserId") Long sysUserId,
                                                         @RequestParam(value = "page", required = false) Integer page) {
        log.info("getInfo sysUserId: {}, page: {}", sysUserId, page);
        return client.getInfo(sysUserId, page);
    }

    @PostMapping(value = "/add")
    @Operation(summary = "新增模板")
    public BaseResponse add(ServerHttpRequest request,
                            @RequestBody @Validated SysUserReportTemplatePo template) {
        template.setSysUserId(super.getUserIdLong2(request));
        log.info("add template: {}", template);
        return client.add(template);
    }

    @GetMapping(value = "/delete")
    @Operation(summary = "删除模板")
    public BaseResponse delete(@RequestParam(value = "templateId") Long templateId) {
        log.info("delete templateId: {}", templateId);
        return client.delete(templateId);
    }
}
