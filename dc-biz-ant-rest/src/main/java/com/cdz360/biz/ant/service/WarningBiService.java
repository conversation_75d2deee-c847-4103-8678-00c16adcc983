package com.cdz360.biz.ant.service;


import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.charge.type.OrderStopCode;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.domain.ErrorMsgDetailVo;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.DeviceMgmFeignClient;
import com.cdz360.biz.ant.feign.TradingFeignClient;
import com.cdz360.biz.ant.utils.RedisUtil;
import com.cdz360.biz.ess.model.dto.EssEquipAlarmLangDto;
import com.cdz360.biz.ess.model.param.ListAlarmLangParam;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.site.type.AlarmEventTypeEnum;
import com.cdz360.biz.model.site.type.AlarmStatusEnum;
import com.cdz360.biz.model.trading.bi.param.WarningBiParam;
import com.cdz360.biz.model.trading.bi.param.WarningSummaryParam;
import com.cdz360.biz.model.trading.bi.warning.StopCodeDto;
import com.cdz360.biz.model.trading.bi.warning.WarningBiDto;
import com.cdz360.biz.model.trading.bi.warning.WarningSummaryDto;
import com.cdz360.biz.model.trading.meter.vo.SiteMeterVo;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.framework.common.domain.WWarningRecord;
import com.chargerlinkcar.framework.common.domain.param.OrderStopBiParam;
import com.chargerlinkcar.framework.common.domain.vo.OrderStopBiVo;
import com.chargerlinkcar.framework.common.feign.DeviceMonitorFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.fasterxml.jackson.core.type.TypeReference;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;


/**
 * 告警统计相关
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class WarningBiService {

    public static final String ERROR_MESSAGE_REDIS_CONTEXT_KEY = "error_message_context";
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private DeviceMgmFeignClient deviceMgmFeignClient;
    @Autowired
    private DeviceMonitorFeignClient deviceMonitorFeignClient;
    @Autowired
    private TradingFeignClient tradingFeignClient;
    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;
    @Autowired
    private CommercialService commercialService;
    @Autowired
    private RedisIotReadService redisIotReadService;


    public ListResponse<WarningBiDto> getWarningBiList(WarningBiParam param, String idChain,
        List<String> gids) {
        log.info("idChain= {}, gids= {}", idChain, gids);

        String dno = param.getEvseNo();
        if (StringUtils.isBlank(dno)) {
            dno = param.getBoxOutFactoryCode();
        }
        if (StringUtils.isNotBlank(dno)) {   // 如果有传设备编号，仅查这个设备编号对应的告警数据
            log.info("仅查设备 {} 对应的告警数据", dno);
        } else if (CollectionUtils.isNotEmpty(gids)) { // 存在场站组
            ListSiteParam siteParams = new ListSiteParam().setGids(gids);
            ListResponse<String> response = dataCoreFeignClient.getSiteListByGids(siteParams);
            FeignResponseValidate.check(response);
            if (CollectionUtils.isEmpty(param.getSiteIdList())) {
                param.setSiteIdList(response.getData());
            }
        } else {
//            String idChain = AntRestUtils.getCommIdChain(request);
            ListResponse<Long> subCommIdRes = commercialService.getSubCommIdList(idChain);
            FeignResponseValidate.check(subCommIdRes);
            param.setCommList(subCommIdRes.getData());
        }

        ListResponse<WWarningRecord> response = deviceMonitorFeignClient.getWarningBiList(param);
        FeignResponseValidate.check(response);
        return RestUtils.buildListResponse(this.mapWarningBiDto(response.getData(),param.getLocale()),
            response.getTotal());
    }

    public ObjectResponse queryWarningList() {
        String ctx = redisUtil.get(ERROR_MESSAGE_REDIS_CONTEXT_KEY);
        if (StringUtils.isNotBlank(ctx)) {
            Map<String, ErrorMsgDetailVo> mapCtx = JsonUtils.fromJson(
                redisUtil.get(ERROR_MESSAGE_REDIS_CONTEXT_KEY),
                new TypeReference<HashMap<String, ErrorMsgDetailVo>>() {
                });
            return new ObjectResponse(mapCtx);
        } else {
            log.error("找不到redis对应表: " + ERROR_MESSAGE_REDIS_CONTEXT_KEY);
            return new ObjectResponse();
        }
    }

    /**
     * 获取所有桩 设备类型、软件版本
     *
     * @param type
     * @return
     */
    public ListResponse<String> getEvseModelOrFirm(String type) {
        return deviceMgmFeignClient.getEvseModelOrFirm(type);
    }

    public ListResponse<StopCodeDto> getOrderStopCodeList() {
        List<OrderStopCode> list = Arrays.asList(OrderStopCode.values());
        List<StopCodeDto> result = new ArrayList<>();
        list.forEach(e -> {
            if (!e.equals(OrderStopCode.C00)) {
                StopCodeDto stopCodeDto = new StopCodeDto();
                stopCodeDto.setStopCode(e.getCode());
                stopCodeDto.setStopReason(e.getDesc());
                result.add(stopCodeDto);
            }
        });
        return new ListResponse<>(result);
    }

    public ListResponse<WarningSummaryDto> getWarningSummaryList(WarningSummaryParam param) {
        return deviceMonitorFeignClient.getWarningSummaryList(param);
    }

    public ListResponse<OrderStopBiVo> getOrderStopSummaryList(OrderStopBiParam param) {
        return tradingFeignClient.getOrderStopSummaryList(param);
    }

    public ListResponse<EssEquipAlarmLangDto> getAlarmList(Locale locale) {
        ListAlarmLangParam listAlarmLangParam = new ListAlarmLangParam()
            .setLang(locale.getLanguage())
            .setEquipTypes(List.of(EssEquipType.EVSE));
        return deviceMgmFeignClient.getAlarmLangList(listAlarmLangParam);
    }


    private List<WarningBiDto> mapWarningBiDto(List<WWarningRecord> list, Locale locale) {
        Map<String, EssEquipAlarmLangDto> codeMap = new HashMap<>();
        if (locale != null && CollectionUtils.isNotEmpty(list)) {
            List<String> warningCodeList = list.stream().map(WWarningRecord::getWarningCode)
                .distinct()
                .collect(Collectors.toList());

            ListAlarmLangParam listAlarmLangParam = new ListAlarmLangParam()
                .setLang(locale.getLanguage())
                .setCodes(warningCodeList)
                .setEquipTypes(List.of(EssEquipType.EVSE));
            ListResponse<EssEquipAlarmLangDto> response = deviceMgmFeignClient.getAlarmLangList(
                listAlarmLangParam);
            FeignResponseValidate.check(response);
            codeMap = response.getData().stream()
                .collect(Collectors.toMap(EssEquipAlarmLangDto::getCode, o -> o));

        }
        Map<String, EssEquipAlarmLangDto> finalCodeMap = codeMap;
        return list.stream().map(e -> {
            WarningBiDto dto = new WarningBiDto();
            // 多语言
            if (finalCodeMap.containsKey(e.getWarningCode())) {
                dto.setWarningName(finalCodeMap.get(e.getWarningCode()).getName());
                dto.setWarningInstructions(finalCodeMap.get(e.getWarningCode()).getPrompt());
            } else {
                dto.setWarningName(e.getWarningName());
                dto.setWarningInstructions(e.getWarningInstructions());
            }
            dto.setWarningId(e.getWarningId())
                .setStartTime(e.getStartTime())
                .setEndTime(e.getEndTime())
                .setBoxOutFactoryCode(e.getBoxOutFactoryCode())
                .setPlugIdx(e.getConnectorId())
                .setSourceNo(e.getSourceNo())
                .setSiteId(e.getSiteId())
                .setSiteName(e.getSiteName())
                .setWarningType(AlarmEventTypeEnum.valueOf(e.getWarningType()))
                .setWarningCode(e.getWarningCode())
                .setStatus(AlarmStatusEnum.valueOf(e.getStatus()))
//                .setWarningName(e.getWarningName())
                .setDeviceId(e.getDeviceId())
                .setEvseName(e.getEvseName())
                .setOrderNo(e.getOrderNo())
                .setPlugName(e.getPlugName());
//                .setWarningInstructions(e.getWarningInstructions());
            return dto;
        }).collect(Collectors.toList());
    }
}
