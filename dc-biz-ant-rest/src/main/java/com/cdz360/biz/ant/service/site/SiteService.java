package com.cdz360.biz.ant.service.site;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServerException;
import com.cdz360.base.model.base.param.SortParam;
import com.cdz360.base.model.base.type.EvseBizStatus;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.OrderType;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.domain.BlocUser;
import com.cdz360.biz.ant.domain.ErrorMsgDetailVo;
import com.cdz360.biz.ant.domain.vo.SiteSimpleInfoVo;
import com.cdz360.biz.ant.feign.AntUserFeignClient;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.feign.BizBiFeignClient;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.DeviceMgmFeignClient;
import com.cdz360.biz.ant.feign.MerchantFeignClient;
import com.cdz360.biz.ant.feign.SiteFeignClient;
import com.cdz360.biz.ant.feign.TradingFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorBizBiFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorSiteDataCoreFeignClient;
import com.cdz360.biz.ant.service.PlugErrorMessageService;
import com.cdz360.biz.auth.sys.vo.AccRelativeVo;
import com.cdz360.biz.model.bi.dashboard.SiteAndPlugBiVo;
import com.cdz360.biz.model.discount.param.DiscountServiceParam;
import com.cdz360.biz.model.discount.vo.SiteDiscountVo;
import com.cdz360.biz.model.finance.type.BizType;
import com.cdz360.biz.model.iot.dto.EvseDto;
import com.cdz360.biz.model.iot.param.ListDeviceParam;
import com.cdz360.biz.model.iot.param.ListEvseCfgResultParam;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.iot.param.ListPlugParam;
import com.cdz360.biz.model.iot.type.DzDeviceStatusEnum;
import com.cdz360.biz.model.iot.type.EvseModelFlag;
import com.cdz360.biz.model.iot.vo.DeviceBi;
import com.cdz360.biz.model.iot.vo.EvseCfgResultVo;
import com.cdz360.biz.model.iot.vo.EvseModelVo;
import com.cdz360.biz.model.iot.vo.EvseStatusBi2;
import com.cdz360.biz.model.iot.vo.TransformerPlugsVo;
import com.cdz360.biz.model.merchant.dto.SiteTinyDto;
import com.cdz360.biz.model.site.param.AddSiteParam;
import com.cdz360.biz.model.site.param.ListSiteBaseParam;
import com.cdz360.biz.model.site.param.SiteAndPlugBiParam;
import com.cdz360.biz.model.site.param.UpdateSiteParam;
import com.cdz360.biz.model.site.po.PriceTemplatePo;
import com.cdz360.biz.model.site.type.SiteCategory;
import com.cdz360.biz.model.site.type.SiteStatus;
import com.cdz360.biz.model.sys.constant.SiteGroupType;
import com.cdz360.biz.model.sys.vo.SiteGroupVo;
import com.cdz360.biz.model.trading.appoint.param.AppointParam;
import com.cdz360.biz.model.trading.appoint.vo.AppointOrderVo;
import com.cdz360.biz.model.trading.comm.dto.CommSiteEvseCount;
import com.cdz360.biz.model.trading.order.po.CardPo;
import com.cdz360.biz.model.trading.site.dto.CitySiteNumDto;
import com.cdz360.biz.model.trading.site.dto.DistrictSiteNumDto;
import com.cdz360.biz.model.trading.site.dto.ProvinceSiteNumDto;
import com.cdz360.biz.model.trading.site.dto.SiteSimpleDto;
import com.cdz360.biz.model.trading.site.param.ListPriceTemplateParam;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.po.EvseCfgSchedulePo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.site.vo.EvseCfgScheduleVo;
import com.cdz360.biz.model.trading.site.vo.MoveCorpNoCardList;
import com.cdz360.biz.model.trading.site.vo.MoveCorpNoCardVo;
import com.cdz360.biz.model.trading.site.vo.SiteCardCount;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobPlugVo;
import com.cdz360.biz.model.trading.site.vo.SiteChargePriceVo;
import com.cdz360.biz.model.trading.site.vo.SiteConfStartList;
import com.cdz360.biz.model.trading.site.vo.SiteNoVo;
import com.cdz360.biz.model.trading.site.vo.SiteQrCodeVo;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import com.cdz360.biz.model.trading.soc.po.SiteSocStrategyPo;
import com.cdz360.biz.model.trading.soc.vo.MoveCorpUserSocStrategyList;
import com.cdz360.biz.model.vin.po.SiteAuthVinLogPo;
import com.cdz360.biz.utils.feign.auth.AuthSiteGroupFeignClient;
import com.cdz360.data.cache.RedisChargeOrderReadService;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.domain.BlocUserDto;
import com.chargerlinkcar.framework.common.domain.ChangeInfo;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.PlugStatusCountDto;
import com.chargerlinkcar.framework.common.domain.SiteDetailInfoVo;
import com.chargerlinkcar.framework.common.domain.SitePersonaliseDTO;
import com.chargerlinkcar.framework.common.domain.SiteSocLimitDto;
import com.chargerlinkcar.framework.common.domain.param.BoxListRequest;
import com.chargerlinkcar.framework.common.domain.param.SiteListRequest;
import com.chargerlinkcar.framework.common.domain.request.SiteGeoListRequest;
import com.chargerlinkcar.framework.common.domain.vo.BoxInfoVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerInfoVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrder;
import com.chargerlinkcar.framework.common.domain.vo.CommCusRef;
import com.chargerlinkcar.framework.common.domain.vo.OrderInfoElec;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUserVo;
import com.chargerlinkcar.framework.common.domain.vo.SiteAuthVinParam;
import com.chargerlinkcar.framework.common.domain.vo.SiteCardAccountVo;
import com.chargerlinkcar.framework.common.domain.vo.SiteDebitAccountVo;
import com.chargerlinkcar.framework.common.domain.vo.SiteInMongoVo;
import com.chargerlinkcar.framework.common.domain.vo.SiteSelectInfoVo;
import com.chargerlinkcar.framework.common.feign.CardFeignClient;
import com.chargerlinkcar.framework.common.feign.CommercialFeignClient;
import com.chargerlinkcar.framework.common.feign.CorpFeignClient;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.feign.OpenHlhtFeignClient;
import com.chargerlinkcar.framework.common.feign.PriceSchemaFeignClient;
import com.chargerlinkcar.framework.common.feign.SiteDataCoreFeignClient;
import com.chargerlinkcar.framework.common.feign.UserFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.PlugNoUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @since Created on 20:01 2019/4/22.
 */
@Slf4j
@Service
public class SiteService //implements ISiteService
{

    private static final Integer SERVFEE_TIME_DIVISION_PROTOVER = 350;
    private static final String LAST_TIME = "24:00";
    @Deprecated
    @Autowired
    private SiteFeignClient siteFeignClient;
    @Autowired
    private SiteDataCoreFeignClient siteDataCoreFeignClient;
    @Autowired
    private MerchantFeignClient merchantFeignClient;
    @Autowired
    private AntUserFeignClient antUserFeignClient;
    @Autowired
    private CardFeignClient cardFeignClient;
    @Autowired
    private RedisIotReadService redisIotReadService;
    @Autowired
    private RedisChargeOrderReadService redisChargeOrderReadService;
    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;
    @Autowired
    private PriceSchemaFeignClient priceTemplateFeignClient;
    @Autowired
    private CorpFeignClient corpFeignClient;
    @Autowired
    private TradingFeignClient tradingFeignClient;
    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;
    @Autowired
    private ReactorSiteDataCoreFeignClient reactorSiteDataCoreFeignClient;
    @Autowired
    private CommercialFeignClient commercialFeignClient;
    @Autowired
    private BizBiFeignClient bizBiFeignClient;

    @Autowired
    private ReactorBizBiFeignClient reactorBizBiFeignClient;

    @Autowired
    private PlugErrorMessageService plugErrorMessageService;
    @Autowired
    private OpenHlhtFeignClient openHlhtFeignClient;
    @Autowired
    private DeviceMgmFeignClient deviceMgmFeignClient;
    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private AuthSiteGroupFeignClient authSiteGroupFeignClient;

    @Autowired
    private UserFeignClient userFeignClient;

    /**
     * 创建场站
     *
     * @param param
     * @return
     */
    public ObjectResponse<SitePo> addSite(AddSiteParam param) {
        ObjectResponse<SitePo> res = this.siteDataCoreFeignClient.addSite(param);
        FeignResponseValidate.check(res);
        return res;
    }

    /**
     * 修改场站信息
     *
     * @param param
     * @return
     */
    public ObjectResponse<SitePo> updateSiteInfo(UpdateSiteParam param) {
        if (StringUtils.isBlank(param.getSiteId())) {
            throw new DcArgumentException("参数错误,siteId不能为空");
        }
        ObjectResponse<SitePo> res = this.siteDataCoreFeignClient.updateSiteInfo(param);
        FeignResponseValidate.check(res);
        return res;
    }


    public SitePo updateSiteStatus(String siteId, SiteStatus status) {
        ObjectResponse<SitePo> res = this.siteDataCoreFeignClient.updateSiteStatus(siteId, status);
        FeignResponseValidate.check(res);
        return res.getData();
    }

    public ListResponse<SiteSimpleInfoVo> getPagedSiteSimpleListOnManager(
        SiteListRequest siteListRequest) {
        log.info("获取站点列表请求参数result-------------------{}", siteListRequest);
        SortParam sortParam = new SortParam();
        sortParam.setColumns(List.of("s.`create_time`"))
            .setOrder(OrderType.desc);
        siteListRequest.setSorts(List.of(sortParam));
        ListResponse<SiteSimpleInfoVo> pagedSiteSimpleList = this.getPagedSiteSimpleList(
            siteListRequest, null, null, null);
        log.info("获取站点列表结果集result.size = {}", pagedSiteSimpleList.getData().size());

        //查询场站下的紧急卡数
        List<String> siteIdList = pagedSiteSimpleList.getData()
            .stream().map(SiteSimpleInfoVo::getSiteId)
            .collect(Collectors.toList());
        ListResponse<SiteCardCount> siteVoListResponse = cardFeignClient.findUrgencyCardNumCount(
            siteIdList);
        FeignResponseValidate.check(siteVoListResponse);
        for (SiteSimpleInfoVo siteSimpleInfoVo : pagedSiteSimpleList.getData()) {
            for (SiteCardCount siteVo : siteVoListResponse.getData()) {
                if (siteVo.getSiteId().equals(siteSimpleInfoVo.getSiteId())) {
                    siteSimpleInfoVo.setUrgencyCardNum(siteVo.getUrgencyCardNum());
                }
            }
        }
        return pagedSiteSimpleList;
        //return new ObjectResponse<>(pagedSiteSimpleList.get("data"));
    }


    public ListResponse<SiteSimpleInfoVo> getPagedSiteSimpleList(
        SiteListRequest siteListRequest, Long topCommId, String commIdChain, List<String> gids) {
        ListSiteParam paramX = new ListSiteParam();
        paramX.setTopCommId(siteListRequest.getMaxCommercialId())
            .setGids(gids)
            .setSiteCategory(siteListRequest.getCategory())
            .setCategoryList(siteListRequest.getCategoryList())
            .setAndCategoryList(siteListRequest.getAndCategoryList())
            .setFetchImageList(siteListRequest.getFetchImageList())
            .setIncludedHlhtSite(siteListRequest.getIncludedHlhtSite())
            .setType(siteListRequest.getType())
            .setSiteName(siteListRequest.getSiteName())
            .setAddress(siteListRequest.getAddress())
            .setInvoicedValid(siteListRequest.getInvoicedValid())
            .setPlatformInvoicedValid(siteListRequest.getPlatformInvoicedValid())
            .setBizTypeList(siteListRequest.getBizTypeList())
            .setSiteStr(siteListRequest.getSiteStr())
            .setSk(siteListRequest.getKeywords())
            .setTotal(true);

        // 场站组交集处理
        if (CollectionUtils.isNotEmpty(gids) &&
            CollectionUtils.isNotEmpty(siteListRequest.getGidList())) {
            paramX.setGids(Stream.concat(gids.stream(),
                    siteListRequest.getGidList().stream())
                .distinct()
                .collect(Collectors.toList()));
        } else if (CollectionUtils.isNotEmpty(siteListRequest.getGidList())) {
            paramX.setGids(siteListRequest.getGidList());
        }

        if (paramX.getTopCommId() == null || paramX.getTopCommId() < 1L) {
            paramX.setTopCommId(topCommId);
        }
        if (siteListRequest.getPage() != null && siteListRequest.getRows() != null) {
            paramX.setStart((long) (siteListRequest.getPage() - 1) * siteListRequest.getRows())
                .setSize(siteListRequest.getRows());
        } else {
            paramX.setStart(0L).setSize(10);
        }
        if (com.cdz360.base.utils.StringUtils.isNotBlank(siteListRequest.getSiteId())) {
            paramX.setSiteIdList(List.of(siteListRequest.getSiteId()));
        } else if (CollectionUtils.isNotEmpty(siteListRequest.getSiteIdList())) {
            paramX.setSiteIdList(siteListRequest.getSiteIdList());
        }
        if (siteListRequest.getCommercialId() == null || siteListRequest.getCommercialId() < 1L) {
            //CommercialSample comm = super.getCommercialSample(request);
            // 不存在商户查询条件  优先取组，没有组取idChain
            if (CollectionUtils.isEmpty(gids)) {
                paramX.setCommIdChain(commIdChain);
            }
//            paramX.setCommIdChain(commIdChain);
        } else {
            ObjectResponse<Commercial> commRes = this.commercialFeignClient.getCommercial(
                siteListRequest.getCommercialId());
            FeignResponseValidate.check(commRes);
            paramX.setCommIdChain(commRes.getData().getIdChain());
        }
        if (siteListRequest.getProvinceCode() != null && siteListRequest.getProvinceCode() > 0) {
            paramX.setProvinceCode(String.valueOf(siteListRequest.getProvinceCode()));
        }
        if (siteListRequest.getCityCode() != null && siteListRequest.getCityCode() > 0) {
            paramX.setCityCode(String.valueOf(siteListRequest.getCityCode()));
        }
        if (StringUtils.isNotBlank(siteListRequest.getTempSalName())) {
            paramX.setTempSalName(siteListRequest.getTempSalName());
        }
        if (StringUtils.isNotBlank(siteListRequest.getIdAndNameAndNoStr())) {
            paramX.setIdAndNameAndNoStr(siteListRequest.getIdAndNameAndNoStr());
        }

        if (CollectionUtils.isNotEmpty(siteListRequest.getStatusList())) {
            final List<Integer> SiteStatusIntegerList =
                Arrays.asList(SiteStatus.values())
                    .stream()
                    .map(SiteStatus::getCode)
                    .collect(Collectors.toList());
            paramX.setStatusList(siteListRequest.getStatusList()
                .stream()
                .filter(e -> SiteStatusIntegerList.contains(e))
                .map(e -> SiteStatus.valueOf(e))
                .collect(Collectors.toList()));
        } else {
            if (siteListRequest.getStatus() != null) {
                paramX.setStatusList(List.of(SiteStatus.valueOf(siteListRequest.getStatus())));
            } else {
                paramX.setStatusList(
                    List.of(SiteStatus.ONLINE, SiteStatus.OPENING, SiteStatus.UNAVAILABLE));
            }
        }

        if (StringUtils.isBlank(siteListRequest.getOrderByDesc())) {
            siteListRequest.setOrderByDesc("createTime");
        }

//        SortParam sortParam = SortParam.as(siteListRequest.getOrderByDesc(), OrderType.desc);
//        paramX.setSorts(List.of(sortParam));
        //默认 按照上线时间倒序
        paramX.setSorts(siteListRequest.getSorts());
        paramX.setTotalPower(true); // 需要统计场站总功率
        paramX.setIncludeGroup(siteListRequest.getIncludeGroup());
        log.info("获取站点列表请求参数 param = {}", paramX);
        ListResponse<SiteVo> siteListRes = siteDataCoreFeignClient.getSiteVoList(paramX);

        List<SiteSimpleInfoVo> list = siteListRes.getData().stream().map(this::map2SiteSimpleInfoVo)
            .collect(Collectors.toList());

        if (Boolean.TRUE.equals(siteListRequest.getFillPlugStatusBi())) {
            this.fillPlugStatusBi(list);
        }

        //ListResponse<SiteSimpleInfoVo> pagedSiteSimpleList = siteFeignClient.getPagedSiteSimpleList(siteListRequest);
        log.info("获取站点列表结果集 siteList.size = {}", list.size());
        return new ListResponse<>(list, siteListRes.getTotal());
        //return new ObjectResponse<>(pagedSiteSimpleList.get("data"));
    }

    /**
     * 同/v2/iot/device/getPlugStatusBiList 接口
     *
     * @param res
     */
    public void fillPlugStatusBi(List<SiteSimpleInfoVo> res) {

        ListDeviceParam param = new ListDeviceParam();
        param.setSiteIdList(
            res.stream().map(SiteSimpleInfoVo::getSiteId).collect(Collectors.toList()));
        ListResponse<DeviceBi> sitesDevice = iotDeviceMgmFeignClient.getSitesDevice(param);
        FeignResponseValidate.check(sitesDevice);

        List<DeviceBi> list = sitesDevice.getData();
        for (int i = 0; i < list.size(); i++) {
            DeviceBi item = list.get(i);

            EvseStatusBi2 bi = new EvseStatusBi2();
            bi.setAcOffline(item.getEvseStatusBi().stream().filter(
                        o1 -> o1.getSupplyType() == SupplyType.AC && o1.getStatus() == EvseStatus.OFFLINE)
                    .mapToLong(o -> o.getNum()).sum())
                .setAcOnline(item.getEvseStatusBi().stream().filter(
                        o1 -> o1.getSupplyType() == SupplyType.AC
                            && o1.getStatus() != EvseStatus.OFFLINE)
                    .mapToLong(o -> o.getNum()).sum())
                .setDcOffline(item.getEvseStatusBi().stream().filter(
                        o1 -> o1.getSupplyType() == SupplyType.DC
                            && o1.getStatus() == EvseStatus.OFFLINE)
                    .mapToLong(o -> o.getNum()).sum())
                .setDcOnline(item.getEvseStatusBi().stream().filter(
                        o1 -> o1.getSupplyType() == SupplyType.DC
                            && o1.getStatus() != EvseStatus.OFFLINE)
                    .mapToLong(o -> o.getNum()).sum());
            res.get(i).setEvseStatusBi2(bi);
        }
    }

    public ListResponse<SiteInMongoVo> getSiteListMongo(ListSiteParam param) {
        ListResponse<SiteInMongoVo> res = siteDataCoreFeignClient.getSiteListFromMongo(param);
        FeignResponseValidate.check(res);
        return res;
    }

    private SiteSimpleInfoVo map2SiteSimpleInfoVo(SiteVo in) {
        SiteSimpleInfoVo o = new SiteSimpleInfoVo();
        o.setId(in.getId())
            .setSiteId(in.getId())
            .setTotalPower(in.getTotalPower())
            .setSiteNo(in.getSiteNo())
            .setName(in.getSiteName())
            .setSiteName(in.getSiteName())
            .setFrozenAmount(in.getFrozenAmount())
            .setLongitude(in.getLongitude())
            .setLatitude(in.getLatitude())
            .setAddress(in.getAddress())
            .setProvince(in.getProvince())
            .setProvinceName(in.getProvinceName())
            .setCity(in.getCity())
            .setCityName(in.getCityName())
            .setDistrict(in.getArea())
            .setDistrictName(in.getAreaName())
            .setIdNo(in.getIdNo())
            .setStatus(in.getStatus())
            .setTopCommId(in.getTopCommId())
            .setOperateId(in.getOperateId())
            .setOperateName(in.getOperateName())
            .setOperateShortName(in.getCommShortName())
            .setMaxCommercialId(in.getTopCommId())
            .setMaxCommercialName(in.getTopCommShortName())
            .setType(in.getType())
            .setBizType(in.getBizType())
            .setServiceWorkdayTime(in.getServiceWorkdayTime())
            .setServiceHolidayTime(in.getServiceHolidayTime())
            .setScope(in.getScope())
            .setOnlineDate(in.getOnlineDate())
            .setFeeDescription(in.getFeeDescription())
            .setFeeMin(in.getFeeMin() == null ? 0L : in.getFeeMin())
            .setFeeMax(in.getFeeMax() == null ? 0L : in.getFeeMax())
            .setTemplateId(in.getTemplateId())
            .setTemplateName(in.getTemplateName())
            .setDefaultPayType(in.getDefaultPayType())
            .setPayAccountId(in.getPayAccountId())
            .setContactsPhone(in.getContactsPhone())
            .setRemark(in.getRemark())
            .setImageList(in.getImageList())
            .setInvoicedValid(in.getInvoicedValid() != null && in.getInvoicedValid() == 1)
            .setMobileTempSalId(in.getMobileTempSalId())
            .setPlatformInvoicedValid(in.getPlatformInvoicedValid())
            .setAvgLevel(in.getAvgLevel())
            .setPvInstalledCapacity(in.getPvInstalledCapacity())
            .setTemplateList(in.getTemplateList())
            .setSiteGroupList(in.getSiteGroupList())
            .setCategory(in.getCategory());
        if (in.getBizName() != null) {
            o.setBizName(in.getBizName());
        }

        if (CollectionUtils.isEmpty(o.getCategory()) &&
            CollectionUtils.isNotEmpty(in.getCategoryIntList())) { // 脱机站点
            o.setCategory(in.getCategoryIntList().stream()
                .map(SiteCategory::valueOf)
                .collect(Collectors.toList()));
        }

        o.setCountOfBox(this.sum(in.getAcEvseNum(), in.getDcEvseNum()));
        o.setCountOfCharger(this.sum(in.getAcPlugNum(), in.getDcPlugNum()));

        // 储能相关字段
        o.setEssInPriceId(in.getEssInPriceId());
        o.setEssOutPriceId(in.getEssOutPriceId());
        o.setEssCapacity(in.getEssCapacity());
        o.setEssPower(in.getEssPower());

        return o;
    }

    private int sum(Integer left, Integer right) {
        if (left == null && right != null) {

            return right;
        } else if (left != null && right == null) {

            return left;
        } else if (left != null && right != null) {

            return left + right;
        } else {

            return 0;
        }
    }

    /**
     * 分页获取枪头信息列表(充电接口列表)
     *
     * @param commercialId 商户id
     * @param siteIds      站点id
     * @param plugNo       枪头编号
     * @param page         分页
     * @return
     */

    public ListResponse<ChargerInfoVo> getPagedChargerInfoList(
        List<String> gidList,
        String commIdChain,
        String commercialId,
        String plugNo,
        List<String> siteIds,
        OldPageParam page,
        String connectorId,
        SupplyType supplyType,
        String serialNumber,
        List<EvseBizStatus> bizStatusList,
        PlugStatus plugStatus,
        String jobName,
        Boolean byUpdateTime,
        List<Integer> statusList) {

        List<ChargerInfoVo> res = new ArrayList<>();

        SortParam sortParam = new SortParam();

        if (Boolean.TRUE.equals(byUpdateTime)) {
            sortParam.setColumns(List.of("updateTime"))
                .setOrder(OrderType.desc);
        } else {
            sortParam.setColumns(List.of("evseName", "idx"))
                .setOrder(OrderType.asc);
        }

        ListPlugParam param = new ListPlugParam();
        param.setSiteIdList(siteIds);
        param.setStatusList(statusList);

        if (StringUtils.isNotBlank(serialNumber)) {
            //param.setEvseNoList(List.of(serialNumber));
            param.setSk(serialNumber);
        }
        if (CollectionUtils.isNotEmpty(bizStatusList)) {
            param.setBizStatusList(bizStatusList);
        }
        if (supplyType != null) {
            param.setSupplyType(supplyType);
        }
        if (plugStatus != null) {
            if (PlugStatus.BUSY.equals(plugStatus) || PlugStatus.JOIN.equals(plugStatus)) {
                param.setPlugStatusList(List.of(PlugStatus.BUSY, PlugStatus.JOIN));
            } else {
                param.setPlugStatusList(List.of(plugStatus));
            }
        }
        if (StringUtils.isNotBlank(plugNo)) {
//            param.setPlugNoList(List.of(plugNo));
            param.setSk(plugNo);
        }
        if (sortParam != null) {
            param.setSorts(List.of(sortParam));
        }

        if (CollectionUtils.isNotEmpty(gidList)) {
            param.setGidList(gidList);
        } else {
            param.setCommIdChain(commIdChain);
        }
        param.setStart((long) ((page.getPageNum() - 1) * page.getPageSize()))
            .setSize(page.getPageSize());
        ListResponse<PlugVo> plugVoListResponse = iotDeviceMgmFeignClient.getPlugList(param);

        //设置正在充电的枪头的订单号
        if (plugVoListResponse != null && CollectionUtils.isNotEmpty(
            plugVoListResponse.getData())) {

            List<PlugVo> plugVoList = plugVoListResponse.getData();

            List<String> plugNoList = plugVoList.stream().map(PlugVo::getPlugNo)
                .collect(Collectors.toList());

            log.info("到redis获取枪头信息: {}", JsonUtils.toJsonString(plugNoList));

            ListResponse<SiteChargeJobPlugVo> siteChargeJobPlugVoListResponse =
                dataCoreFeignClient.getSiteJobByPlugNoList(plugNoList);
            Map<String, SiteChargeJobPlugVo> plugJobMap =
                (siteChargeJobPlugVoListResponse != null && CollectionUtils.isNotEmpty(
                    siteChargeJobPlugVoListResponse.getData()))
                    ? siteChargeJobPlugVoListResponse.getData().stream()
                    .collect(Collectors.toMap(
                        e -> PlugNoUtils.formatPlugNo(e.getEvseNo(), e.getPlugIdx()), o -> o))
                    : new HashMap<>();

            List<PlugVo> redisPlugList = redisIotReadService.getPlugList(plugNoList);
            Map<String, PlugVo> redisMap = redisPlugList.stream()
                .collect(Collectors.toMap(PlugVo::getPlugNo, o -> o));

            List<String> redisOrderNoList = redisPlugList.stream()
                .filter(e -> StringUtils.isNotBlank(e.getOrderNo()))
                .map(PlugVo::getOrderNo)
                .filter(Predicate.not(String::isEmpty))
                .collect(Collectors.toList());

            // TODO 此处需要改成 Pipelined
            Map<String, ChargerOrder> orderMap =
                CollectionUtils.isEmpty(redisOrderNoList) ? new HashMap<>() :
                    redisOrderNoList.stream()
                        .map(e -> redisChargeOrderReadService.getOrder(e, ChargerOrder.class))
                        .filter(Predicate.not(Objects::isNull))
                        .collect(Collectors.toMap(ChargerOrder::getOrderNo, o -> o));

            // TODO 此处需要改成 Pipelined
            Map<String, Integer> expectLimitSocMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(redisOrderNoList)) {
                for (String orderNo : redisOrderNoList) {
                    Integer expectLimitSoc = redisChargeOrderReadService.getExpectLimitSoc(orderNo);
                    if (expectLimitSoc != null) {
                        expectLimitSocMap.put(orderNo, expectLimitSoc);
                    }
                }
            }

            log.debug("从redis获取到的订单信息: {}", JsonUtils.toJsonString(orderMap));

            plugVoList.forEach(e -> {
                ChargerInfoVo temp = new ChargerInfoVo();
                temp.setBizStatus(e.getBizStatus());
                temp.setDeviceId(e.getEvseNo());
                temp.setStatus(e.getStatus());
                temp.setPlugNo(e.getPlugNo());
                temp.setChargerName(e.getName());
                temp.setEvseNo(e.getEvseNo());
                temp.setEvseName(e.getEvseName());
                temp.setBusinessId(e.getSiteCommId());
                temp.setSiteId(e.getSiteId());
                temp.setSiteName(e.getSiteName());
                temp.setSupplyType(e.getSupply());
                temp.setConnectorId(e.getIdx());
                temp.setOrderByUpdateTime(e.getUpdateTime());

                PlugVo plugCache = redisMap.get(e.getPlugNo());
//                final String orderNo = redisMap.get(e.getPlugNo()).getOrderNo();
                if (plugCache != null
                    && orderMap.containsKey(plugCache.getOrderNo())
                    && orderMap.get(plugCache.getOrderNo()) != null
                    && (plugCache.getErrorCode() == null
                    || (null != plugCache.getErrorCode()
                    && plugCache.getErrorCode() == 0))) {
                    final String orderNo = plugCache.getOrderNo();
                    temp.setLineNum(orderMap.get(orderNo).getLineNum());
                    temp.setCarNo(orderMap.get(orderNo).getCarNo());
                    temp.setCarNum(orderMap.get(orderNo).getCarNum());
                    temp.setCarVin(orderMap.get(orderNo).getVin());
                    temp.setLimitSoc(orderMap.get(orderNo).getLimitSoc());
//                    temp.setExpectLimitSoc(expectLimitSocMap.get(orderNo).getExpectLimitSoc());
                    if (expectLimitSocMap.get(orderNo) != null) {
                        temp.setExpectLimitSoc(expectLimitSocMap.get(orderNo));
                    }
                }
                if (plugCache != null) {
                    temp.setErrorCode(plugCache.getErrorCode());
                    temp.setErrorMsg(plugCache.getErrorMsg());
                    temp.setAlertCode(plugCache.getAlertCode());
                    temp.setLockNo(plugCache.getLockNo());
                    temp.setLockStatus(plugCache.getLockStatus());
                    temp.setAssignPower(plugCache.getAssignPower());
                    temp.setConstantCharge(plugCache.getConstantCharge());

                    if (plugCache.getErrorCode() != null) {
                        ErrorMsgDetailVo errorInfo = this.plugErrorMessageService.getErrorMsg(
                            plugCache.getErrorCode());
                        if (errorInfo != null) {
                            temp.setErrorLevel(errorInfo.getLevel());
                            temp.setErrorCause(errorInfo.getCause());
                        }
                    }

                    temp.setOrderNo(plugCache.getOrderNo());

                    temp.setUpdateTime(plugCache.getUpdateTime() == null ?
                        null : Math.toIntExact(plugCache.getUpdateTime().getTime() / 1000)
                    );
                }
                SiteChargeJobPlugVo plugJob = plugJobMap.get(e.getPlugNo());
                if (plugJob != null) {
                    temp.setPlugJobId(plugJob.getJobId());
                    temp.setPlugJobName(plugJob.getJobName());
                    temp.setPlugJobStatus(plugJob.getJobStatus());
                }

                if (StringUtils.isNotBlank(jobName) &&
                    !StringUtils.equals(jobName, temp.getPlugJobName())) {
                    // 如果传入了筛选条件-任务名称，且枪上无job或不匹配，则不添加到res中
                    return;
                }
                res.add(temp);
            });

            //预约锁定的订单获取车辆信息
            List<ChargerInfoVo> lockChargers = res.stream()
                .filter(chargerInfoVo -> chargerInfoVo.getLockStatus() != null
                    && chargerInfoVo.getLockStatus().equals(true))
                .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(lockChargers)) {
                List<String> appointNoList = lockChargers.stream()
                    .filter(e -> StringUtils.isNotBlank(e.getLockNo()))
                    .map((ChargerInfoVo::getLockNo)).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(appointNoList)) {
                    AppointParam appointParam = new AppointParam();
                    appointParam.setAppointNoList(appointNoList);
                    ListResponse<AppointOrderVo> result = openHlhtFeignClient.queryAppointListByAppointNo(
                        appointParam);
                    if (result.getStatus() == ResultConstant.RES_SUCCESS_CODE &&
                        CollectionUtils.isNotEmpty(result.getData())) {
                        Map<String, AppointOrderVo> appointOrderVoMap = result.getData()
                            .stream()
                            .collect(Collectors.toMap(AppointOrderVo::getAppointNo,
                                o -> o,
                                (oldValue, newValue) -> newValue));
                        lockChargers.stream()
                            .filter(e -> StringUtils.isNotBlank(e.getLockNo()))
                            .forEach(e -> {
                                AppointOrderVo appointOrderVo = appointOrderVoMap.get(
                                    e.getLockNo());
                                if (null != appointOrderVo) {
                                    e.setCarNo(appointOrderVo.getCarNumber());
                                    e.setEndTime(appointOrderVo.getEndTime());
                                }
                            });
                    }
                }
            }

            // 进行中、充电完成的订单 提取电力读数
            List<ChargerInfoVo> chargerings = res.stream()
                .filter(chargerInfoVo -> PlugStatus.BUSY == chargerInfoVo.getStatus() ||
                    PlugStatus.RECHARGE_END == chargerInfoVo.getStatus())
                .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(chargerings)) {

                List<String> orderNoList = chargerings.stream()
                    .filter(e -> StringUtils.isNotBlank(e.getOrderNo()))
                    .map(ChargerInfoVo::getOrderNo)
                    .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(orderNoList)) {
                    ListResponse<OrderInfoElec> ret = tradingFeignClient.orderInfoElec(orderNoList);

                    if (ret.getStatus() == ResultConstant.RES_SUCCESS_CODE &&
                        CollectionUtils.isNotEmpty(ret.getData())) {
                        Map<String, OrderInfoElec> orderInfoElecMap = ret.getData()
                            .stream()
                            .collect(Collectors.toMap(OrderInfoElec::getOrderNo,
                                o -> o,
                                (oldValue, newValue) -> newValue));

                        chargerings.stream()
                            .filter(e -> StringUtils.isNotBlank(e.getOrderNo()))
                            .forEach(e -> {
                                OrderInfoElec orderInfoElec = orderInfoElecMap.get(e.getOrderNo());
                                if (null != orderInfoElec) {
//                                    e.setCarNo(orderInfoElec.getCarNo());
                                    e.setSoc(orderInfoElec.getCurrSoc());
                                    e.setStartSoc(orderInfoElec.getStartSoc());
                                    e.setStartTime(orderInfoElec.getStartTime());
                                    e.setStopTime(orderInfoElec.getStopTime());
                                    //除了【充电中】，其余状态，剩余时间字段为null，页面将显示为‘--’
                                    if (PlugStatus.BUSY == e.getStatus()) {
                                        e.setRemainder(orderInfoElec.getRemainingTime() == null ?
                                            null :
                                            orderInfoElec.getRemainingTime().intValue());
                                    } else if (PlugStatus.RECHARGE_END == e.getStatus()) {
                                        e.setRemainder(null);
                                        e.setCurrent(BigDecimal.ZERO);
                                        e.setVoltage(BigDecimal.ZERO);
                                        e.setElectric(BigDecimal.ZERO);
                                    }
                                    e.setDuration(orderInfoElec.getDuration() == null ?
                                        null :
                                        orderInfoElec.getDuration().intValue());
                                    e.setElectricity(orderInfoElec.getElectricity());

                                    e.setDcVoltageO(orderInfoElec.getDcVoltageO());
                                    e.setDcCurrentO(orderInfoElec.getDcCurrentO());
                                    e.setAcVoltageA(orderInfoElec.getAcVoltageA());
                                    e.setAcCurrentA(orderInfoElec.getAcCurrentA());
                                    e.setAcVoltageB(orderInfoElec.getAcVoltageB());
                                    e.setAcCurrentB(orderInfoElec.getAcCurrentB());
                                    e.setAcVoltageC(orderInfoElec.getAcVoltageC());
                                    e.setAcCurrentC(orderInfoElec.getAcCurrentC());
                                    e.setUpdateTime(orderInfoElec.getUpdateTime());

                                    // 计算功率
                                    if (orderInfoElec.getDcCurrentO() != null &&
                                        orderInfoElec.getDcVoltageO() != null) {
                                        e.setPower(e.getDcCurrentO().multiply(e.getDcVoltageO())
                                            .divide(BigDecimal.valueOf(1000), 4,
                                                RoundingMode.HALF_DOWN));
                                    }
                                }
                            });
                    }
                }

            }

        }

        return RestUtils.buildListResponse(res);
    }

    public ListResponse<TransformerPlugsVo> getPlugListByTransformerId(List<String> siteIds,
        Long transformerId) {

        String siteId = siteIds.get(0);
        return deviceMgmFeignClient.getPlugListByTransformerId(siteId, transformerId);
    }

    /**
     * 获取当前可用于绑定在变压器的装列表，以便绘制场站拓扑图
     *
     * @param request
     * @return
     */
    public ListResponse<BoxInfoVo> getTopologyFreeBoxSimpleList(BoxListRequest request) {
        ListEvseParam listEvseParam = new ListEvseParam();
        long start = 0L;
        int size = 999;
        if (request.getPage() != null && request.getRows() != null) {
            start = (request.getPage() - 1) * request.getRows();
            size = request.getRows();
        }

        com.cdz360.base.model.base.param.SortParam sortParam = new com.cdz360.base.model.base.param.SortParam();
        sortParam.setColumns(List.of("name"))
            .setOrder(OrderType.asc);
        listEvseParam.setSiteIdList(List.of(request.getSiteId()))
            .setStart(start)
            .setSize(size)
            .setSorts(List.of(sortParam));

        ListResponse<EvseDto> evseListRes = this.iotDeviceMgmFeignClient.getEvseListForTopology(
            listEvseParam);
        FeignResponseValidate.check(evseListRes);
        List<EvseDto> evseList = evseListRes.getData();
        log.info("evse list size = {}", evseList.size());
        if (CollectionUtils.isEmpty(evseList)) {
            log.info("<< 桩列表为空");
            return RestUtils.buildListResponse(List.of());
        }

        List<BoxInfoVo> voList = evseList.stream()
            .map(evse -> this.toBoxInfo(evse, new HashMap<>(), new HashMap<>()))
            .collect(Collectors.toList());

        return new ListResponse<>(voList);
    }

    /**
     * 查询设备信息
     *
     * @param request
     * @return
     */

    public ListResponse<BoxInfoVo> getPagedBoxSimpleList(BoxListRequest request) {
        ListEvseParam listEvseParam = new ListEvseParam();

        // @Nathan 单个场站获取桩列表不需要传分页信息, 默认调整到999
        long start = 0L;
        int size = 999;

        if (request.getPage() != null && request.getRows() != null) {
            start = (request.getPage() - 1) * request.getRows();
            size = request.getRows();
        }

//        com.cdz360.base.model.base.param.SortParam sortParam = new com.cdz360.base.model.base.param.SortParam();
//        sortParam.setColumns(List.of("name"))
//                .setOrder(OrderType.asc);
        listEvseParam.setSiteIdList(List.of(request.getSiteId()))
            .setEvseNoList(request.getDeviceIdList())
            .setFlags(request.getFlags())
            .setSupplyType(request.getSupplyType())
            .setStart(start)
            .setSize(size)
            .setSorts(request.getSorts());

        ListResponse<EvseModelVo> evseListRes = this.iotDeviceMgmFeignClient.getEvseModelVoList(
            listEvseParam);
        FeignResponseValidate.check(evseListRes);
        List<EvseModelVo> evseList = evseListRes.getData();
        log.info("evse list size = {}", evseList.size());
        if (CollectionUtils.isEmpty(evseList)) {
            log.info("<< 桩列表为空");
            return RestUtils.buildListResponse(List.of());
        }

        ListPriceTemplateParam listPtParam = new ListPriceTemplateParam();
        listPtParam.setIdList(
            evseList.stream().map(EvseDto::getPriceCode).collect(Collectors.toList()));

        ListResponse<PriceTemplatePo> ptRes = priceTemplateFeignClient.getPriceSchemaList(
            listPtParam);
        Map<Long, PriceTemplatePo> ptMap = new HashMap<>();
        if (ptRes.getData() != null) {
            ptMap.putAll(
                ptRes.getData().stream().collect(Collectors.toMap(PriceTemplatePo::getId, o -> o)));
        }
//        List<BoxInfoVo> list = evseList.stream().map(evse -> this.map2BoxInfoVo(evse, site, ptMap)).collect(Collectors.toList());

        // @Nathan 单个场站获取桩列表不需要传分页信息, 默认调整到999
        if (request.getPage() == null || request.getRows() == null) {
            request.setPage(1);
            request.setRows(999);
        }

        ListResponse<BoxInfoVo> jsonObject = siteFeignClient.getPagedBoxSimpleList(request);
        FeignResponseValidate.check(jsonObject);
        log.info("size = {}", jsonObject.getData().size());

        Map<String, EvseDto> evseMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(jsonObject.getData())) {
            List<BoxInfoVo> boxList = jsonObject.getData();
            final List<String> evseNoList = boxList.stream()
                .map(BoxInfoVo::getSerialNumber)
                .collect(Collectors.toList());
            ListEvseParam param = new ListEvseParam();
            param.setEvseNoList(evseNoList);
            final ListResponse<EvseDto> evseListData = iotDeviceMgmFeignClient.getEvseList(param);
            FeignResponseValidate.check(evseListData);
            evseMap = evseListData.getData()
                .stream()
                .collect(Collectors.toMap(EvseDto::getEvseId, o -> o, (n, o) -> o));
        }
        final Map<String, EvseDto> finalEvseMap = evseMap;
        //return new ObjectResponse<>(jsonObject.get("data"));

        // 是否存在定时下发计费信息
        Map<String, BoxInfoVo> voMap = jsonObject.getData().stream()
            .collect(Collectors.toMap(BoxInfoVo::getSerialNumber, o -> o));
        List<BoxInfoVo> voList = evseList.stream()
            .map(evse -> this.toBoxInfo(evse, ptMap, voMap, finalEvseMap))
            .collect(Collectors.toList());

        // 所有桩编号
        List<String> evseNoList = voList.stream().map(BoxInfoVo::getSerialNumber)
            .collect(Collectors.toList());

        // 桩最近下发本地vin成功记录
        if (CollectionUtils.isNotEmpty(evseNoList)) {
            SiteAuthVinParam siteAuthVinParam = new SiteAuthVinParam();
            siteAuthVinParam.setEvseNoList(evseNoList);
            ListResponse<SiteAuthVinLogPo> siteAuthVinTime = userFeignClient.getSiteAuthVinTime(
                siteAuthVinParam);
            if (siteAuthVinTime != null && CollectionUtils.isNotEmpty(siteAuthVinTime.getData())) {
                List<SiteAuthVinLogPo> authVinLogPoList = siteAuthVinTime.getData();
                Map<String, SiteAuthVinLogPo> collect = authVinLogPoList.stream()
                    .collect(Collectors.toMap(SiteAuthVinLogPo::getEvseId, o -> o, (o, n) -> n));
                voList.forEach(e -> e.setAuthVinLog(collect.get(e.getSerialNumber())));
            }
        }

        ListResponse<EvseCfgScheduleVo> response = dataCoreFeignClient.getEvseCfgScheduleByEvseNo(
            evseNoList);
        if (null != response && CollectionUtils.isNotEmpty(response.getData())) {
            log.info("更新桩的配置定时下发信息");
            List<EvseCfgScheduleVo> poList = response.getData();

            // 过滤赋值
            voList.forEach(vo -> {
                Predicate<EvseCfgSchedulePo> scheduleFilter = o -> o.getEvseNo()
                    .equals(vo.getSerialNumber());
                poList.stream().filter(scheduleFilter).forEach(o -> {
                    vo.setSchedulePriceSchemeId(o.getPriceSchemeId());
                    vo.setSchedulePriceSchemeCode(o.getPriceSchemeCode());
                    vo.setSchedulePriceSchemeName(o.getPriceSchemeName());
                    vo.setSchedulePriceSchemeDate(o.getScheduleTime());
                });
            });
        }

        // 桩计费模板最新下发时间
        ListEvseCfgResultParam param = new ListEvseCfgResultParam();
        param.setEvseNoList(evseNoList);
        // FIXME: 这里获取时间不准确
        ListResponse<EvseCfgResultVo> downRes = this.iotDeviceMgmFeignClient.getEvseCfgResultList(
            param);
        if (null != downRes && CollectionUtils.isNotEmpty(downRes.getData())) {
            log.info("更新桩的配置下发时间");
            List<EvseCfgResultVo> evseCfgVoList = downRes.getData();

            // 过滤赋值
            voList.forEach(vo -> {
                Predicate<EvseCfgResultVo> cfgFilter = o -> vo.getTemplateId() != null
                    && vo.getTemplateId() > 0 &&
                    o.getEvseNo().equals(vo.getSerialNumber());
                evseCfgVoList.parallelStream().filter(cfgFilter).forEach(o -> {
                    vo.setPriceSchemeDownDate(o.getPriceCodeEffectiveTime());
                    vo.setSendStatus(this.statusConvert(o.getCfgResult()));
                    vo.setPriceCodeResult(o.getPriceCodeResult());
                });
            });
        }

        return new ListResponse<>(voList);
    }

    private BoxInfoVo toBoxInfo(EvseModelVo evse,
        Map<Long, PriceTemplatePo> ptMap,
        Map<String, BoxInfoVo> voMap,    // 未来再移除。。
        Map<String, EvseDto> evseMap
    ) {
        EvseDto evseDto = new EvseDto();
        BeanUtils.copyProperties(evse, evseDto);
        BoxInfoVo res = this.toBoxInfo(evseDto, ptMap, voMap);
        res.setBrand(evse.getBrand())
            .setSeries(evse.getSeries())
            .setFlags(EvseModelFlag.valueOf(evse.getFlags()))
            .setEvseModulePoList(evse.getEvseModulePoList())
            .setPlugList(evse.getPlugList())
            .setSimIccid(evse.getSimIccid())
            .setSupplyType(evse.getSupply())
            .setSimMsisdn(evse.getSimMsisdn());
        if (evseMap.get(evse.getEvseId()) != null) {
            res.setPhysicalNo(evseMap.get(evse.getEvseId()).getPhysicalNo());
        }
        return res;
    }

    private BoxInfoVo toBoxInfo(EvseDto evse,
        Map<Long, PriceTemplatePo> ptMap,
        Map<String, BoxInfoVo> voMap    // 未来再移除。。
    ) {
        BoxInfoVo box = new BoxInfoVo();
        box.setDeviceId(evse.getEvseId());
        box.setPlugNo(evse.getPlugNo());
        box.setSerialNumber(evse.getEvseId());
        box.setBoxName(evse.getName());
        //box.setBusinessId(String.valueOf(evse.getCommId()));
        box.setSiteId(evse.getSiteId());
        box.setBizStatus(evse.getBizStatus());
        box.setStatus(DzDeviceStatusEnum.fromEvseStatus(evse.getEvseStatus()).getCode());
        box.setEvseStatus(evse.getEvseStatus());
        box.setValidateConnectorCount(evse.getPlugNum());
        box.setPower(evse.getPower());
        box.setProtocolVer(evse.getProtocolVer());
        box.setFirmwareVer(evse.getFirmwareVer());
        box.setIccid(evse.getIccid());
        box.setImsi(evse.getImsi());
        box.setImei(evse.getImei());
        box.setModel(evse.getModel());
        box.setModelId(evse.getModelId());
        box/*.setProduceNo(evse.getProduceNo())*/
            .setProduceDate(evse.getProduceDate())
            .setExpireDate(evse.getExpireDate());
        if (evse.getSupply() == SupplyType.AC) {
            box.setCurrentType(0);
        } else if (evse.getSupply() == SupplyType.DC) {
            box.setCurrentType(1);
        }
        if (evse.getPriceCode() != null) {
            box.setTemplateId(evse.getPriceCode());
            box.setActiveTemplateId(evse.getPriceCode());
            PriceTemplatePo pt = ptMap.get(evse.getPriceCode());
            if (pt != null) {
                box.setTemplateName(pt.getName());
                box.setActiveTemplateName(pt.getName());
            }

        }
        if (evse.getProtocolVer() != null
            && evse.getProtocolVer() >= SERVFEE_TIME_DIVISION_PROTOVER) {
            // 3.5以上的协议版本支持服务费分时
            box.setServFeeTimeDivision(true);
        } else {
            box.setServFeeTimeDivision(false);
        }
        box.setBusinessId(String.valueOf(evse.getCommId()));
        BoxInfoVo vo = voMap.get(evse.getEvseId());
        if (vo != null) {
            // 下面这些字段是暂时还没有解决的。。。
//            box.setBusinessId(vo.getBusinessId());
//            box.setTemplateId(vo.getTemplateId());
//            box.setTemplateName(vo.getTemplateName());
            box.setIsAssociateSiteTemplate(vo.getIsAssociateSiteTemplate());
            box.setIsUseSiteDefaultSetting(vo.getIsUseSiteDefaultSetting());
        }
        return box;
    }

    // FIXME: 暂时使用转换的方案，后续删除
    private int statusConvert(Integer srcStatus) {
        if (null == srcStatus) {
            return 2;
        }

        if (0 == srcStatus) {
            return 1;
        }

        if (300 == srcStatus) {
            return 3;
        }

        return 2;
    }


    /**
     * 获取站点下拉列表
     *
     * @param param
     * @return
     */

    public ListResponse<SiteSelectInfoVo> getSiteSelectInfoList(SiteListRequest param,
        Long topCommId,
        String commIdChain,
        List<String> loginUserGids) {
        //调用设备接口
        log.info(
            "获取站点下拉列表请求参数 param = {}, topCommId = {}, commIdChain = {}, loginUserGids = {}",
            JsonUtils.toJsonString(param), topCommId, commIdChain, loginUserGids);
        ListSiteParam paramX = new ListSiteParam();
        paramX.setTopCommId(param.getMaxCommercialId())
            .setSiteCategory(param.getCategory())
            .setCategoryList(param.getCategoryList())
            .setAndCategoryList(param.getAndCategoryList())
            .setSiteCommId(param.getSiteCommId())
            .setType(param.getType())
            .setBizTypeList(param.getBizTypeList())
            .setIncludedHlhtSite(param.getIncludedHlhtSite())
            .setSiteName(param.getSiteName())
            .setAddress(param.getAddress())
            .setInvoicedValid(param.getInvoicedValid())
            .setSk(param.getKeywords())
            .setTotal(true);
        if (paramX.getTopCommId() == null || paramX.getTopCommId() < 1L) {
            paramX.setTopCommId(topCommId);
        }
        if (param.getPage() != null && param.getRows() != null) {
            paramX.setStart((long) (param.getPage() - 1) * param.getRows())
                .setSize(param.getRows());
        } else {
            paramX.setStart(0L).setSize(10);
        }

        List<String> siteIdList = new ArrayList<>();
        if (com.cdz360.base.utils.StringUtils.isNotBlank(param.getSiteId())) {
            siteIdList.add(param.getSiteId());
        }
        if (CollectionUtils.isNotEmpty(param.getSiteIdList())) {
            siteIdList.addAll(param.getSiteIdList());
        }
        paramX.setSiteIdList(siteIdList);

        List<String> corpGids = null;
        if (param.getCorpId() != null) {
            ListResponse<String> resp = authCenterFeignClient.getGidsById(param.getCorpId());
            FeignResponseValidate.checkIgnoreData(resp);
            corpGids = resp.getData();
        }

        if (CollectionUtils.isNotEmpty(corpGids)) {
            paramX.setGids(corpGids);
        } else if (CollectionUtils.isNotEmpty(loginUserGids)) {
            paramX.setGids(loginUserGids);
        } else if (param.getCommercialId() == null || param.getCommercialId() < 1L) {
            //CommercialSample comm = super.getCommercialSample(request);
            paramX.setCommIdChain(commIdChain);
        } else {
            ObjectResponse<Commercial> commRes = this.commercialFeignClient.getCommercial(
                param.getCommercialId());
            FeignResponseValidate.check(commRes);
            paramX.setCommIdChain(commRes.getData().getIdChain());
        }

        if (param.getProvinceCode() != null && param.getProvinceCode() > 0) {
            paramX.setProvinceCode(String.valueOf(param.getProvinceCode()));
        }
        if (param.getCityCode() != null && param.getCityCode() > 0) {
            paramX.setCityCode(String.valueOf(param.getCityCode()));
        }

        if (CollectionUtils.isNotEmpty(param.getStatusList())) {
            final List<Integer> SiteStatusIntegerList =
                Arrays.asList(SiteStatus.values()).stream().map(SiteStatus::getCode)
                    .collect(Collectors.toList());
            paramX.setStatusList(param.getStatusList()
                .stream()
                .filter(e -> SiteStatusIntegerList.contains(e))
                .map(e -> SiteStatus.valueOf(e))
                .collect(Collectors.toList()));
        } else {
            if (param.getStatus() != null) {
                paramX.setStatusList(List.of(SiteStatus.valueOf(param.getStatus())));
            } else {
                paramX.setStatusList(
                    List.of(SiteStatus.ONLINE, SiteStatus.OPENING, SiteStatus.UNAVAILABLE));
            }
        }

        if (Boolean.FALSE.equals(param.getIsQueryByChain())) {
            paramX.setTopCommId(null)
                .setCommIdChain(null);
        }

        ListResponse<SiteTinyDto> siteListRes = siteDataCoreFeignClient.getSiteTinyList(paramX);
        FeignResponseValidate.check(siteListRes);
        List<SiteSelectInfoVo> list = siteListRes.getData().stream()
            .map(o -> {
                SiteSelectInfoVo ret = new SiteSelectInfoVo();
                ret.setSiteId(o.getId());
                ret.setSiteName(o.getSiteName());
                return ret;
            }).collect(Collectors.toList());
        //ListResponse<SiteSelectInfoVo> result = siteFeignClient.getSiteSelectInfoList(param);

        return RestUtils.buildListResponse(list);
    }

    public Mono<ListResponse<SiteSelectInfoVo>> getSiteByGroup(ListSiteParam param) {
        return reactorSiteDataCoreFeignClient.getSiteTinyList(param)
            .doOnNext(FeignResponseValidate::check)
            .map(res -> RestUtils.buildListResponse(res.getData().stream()
                    .map(o -> {
                        SiteSelectInfoVo ret = new SiteSelectInfoVo();
                        ret.setSiteId(o.getId());
                        ret.setSiteName(o.getSiteName());
                        return ret;
                    }).collect(Collectors.toList()),
                null != res.getTotal() ? res.getTotal() : 0L));
    }

    public Mono<ListResponse<SiteSelectInfoVo>> getSiteInfoListByUserGroups(
        Long uid, ListSiteParam paramX) {
        // 获取用户归属场站组和负责的场站
        // 运维目前所负责场站是归属场站组的场站子集，其他角色目前还不存在该现象
        return authSiteGroupFeignClient.userOwnerSite(uid, paramX.getGroupType())
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .flatMap(x -> {
                if (CollectionUtils.isNotEmpty(x.getGroupVoList())) {
                    paramX.setGids(x.getGroupVoList().stream()
                        .map(SiteGroupVo::getGid)
                        .collect(Collectors.toList()));
                } else {
                    return Mono.just(RestUtils.buildListResponse(List.of()));
                }

                if (CollectionUtils.isNotEmpty(x.getSiteIdList())) {
                    paramX.setSiteIdList(x.getSiteIdList());
                } else if (SiteGroupType.YW.equals(paramX.getGroupType())) {
                    // 运维人员负责场站为空
                    return Mono.just(RestUtils.buildListResponse(List.of()));
                }

                return reactorSiteDataCoreFeignClient.getSiteTinyList(paramX)
                    .doOnNext(FeignResponseValidate::check)
                    .map(res -> RestUtils.buildListResponse(res.getData().stream()
                            .map(o -> {
                                SiteSelectInfoVo ret = new SiteSelectInfoVo();
                                ret.setSiteId(o.getId());
                                ret.setSiteName(o.getSiteName());
                                return ret;
                            }).collect(Collectors.toList()),
                        null != res.getTotal() ? res.getTotal() : 0L));
            });
    }

    /**
     * 获取商户子商户下站点下拉列表
     *
     * @return
     */

    public ListResponse<SiteSelectInfoVo> getSiteSelectInfoListByComm(Long commId,
        List<String> gids) {
//        ListResponse<Long> entity = merchantFeignClient.getCommIdListByCommId(commId);
//        List<Long> commercialIdList = entity.getData();

        ObjectResponse<Commercial> commRes = this.commercialFeignClient.getCommercial(commId);
        FeignResponseValidate.check(commRes);

        //调用设备接口
        log.info("commIdChain = {}", JsonUtils.toJsonString(commRes.getData().getIdChain()));
        ListSiteParam request = new ListSiteParam();
        //request.setCommercialIdList(commercialIdList);
        request.setCommIdChain(commRes.getData().getIdChain())
            .setGids(gids);
        ListResponse<SiteTinyDto> res = this.siteDataCoreFeignClient.getSiteTinyList(request);
        List<SiteSelectInfoVo> list = res.getData().stream()
            .map(o -> {
                SiteSelectInfoVo ret = new SiteSelectInfoVo();
                ret.setSiteId(o.getId());
                ret.setSiteName(o.getSiteName());
                return ret;
            }).collect(Collectors.toList());
        //return siteFeignClient.getSiteSelectInfoList(siteListRequest);
        return RestUtils.buildListResponse(list);
        // ListResponse<SiteSelectInfoVo> result = siteFeignClient.getSiteSelectInfoList(request);

        //return result;
    }

    public void setDefaultPriceScheme(Long priceSchemeId, String siteId, Boolean isDefault) {
        log.info(">> 设置场站默认的计费模板: priceSchemeId = {}, siteId = {}", priceSchemeId,
            siteId);

        ObjectResponse<PriceTemplatePo> res = new ObjectResponse();
        res.setStatus(ResultConstant.RES_FAIL_CODE);

        if (Boolean.FALSE.equals(isDefault)) {
            res = siteDataCoreFeignClient.getSitePriceScheme(siteId);

        }

        if (Boolean.TRUE.equals(isDefault) ||
            (res.getStatus() == ResultConstant.RES_SUCCESS_CODE && res.getData() == null)) {
            // 设置默认模板，或，场站原先不存在默认模板
            siteDataCoreFeignClient.setDefaultPriceScheme(priceSchemeId, siteId);
        } else {
            log.info("不设定场站默认计费模板");
        }
        log.info("<<");
    }


    public ObjectResponse<PriceTemplatePo> getSitePriceScheme(String siteId) {
        log.info(">> 查询场站是否计费模板: siteId = {}", siteId);
        return siteDataCoreFeignClient.getSitePriceScheme(siteId);
    }


    public SiteDebitAccountVo getDebitAccount(String siteId) {
        ObjectResponse<SitePo> siteSimpleInfoVoObjectResponse
            = siteDataCoreFeignClient.getSiteById(siteId);
        //ObjectResponse<SiteSimpleInfoVo> siteSimpleInfoVoObjectResponse = siteFeignClient.getSiteSimpleInfoById(siteId);
        FeignResponseValidate.check(siteSimpleInfoVoObjectResponse);
        SitePo vo = siteSimpleInfoVoObjectResponse.getData();
        log.info("vo: {}", JsonUtils.toJsonString(vo));
        PayAccountType payAccountType = PayAccountType.valueOf(vo.getDefaultPayType());
        SiteDebitAccountVo result = new SiteDebitAccountVo();
        switch (payAccountType) {
            case PERSONAL:
                ObjectResponse<com.cdz360.biz.ant.domain.vo.UserVo> userVoObjectResponse = antUserFeignClient.findInfoByUid(
                    vo.getPayAccountId(), null, null);
                FeignResponseValidate.check(userVoObjectResponse);
                com.cdz360.biz.ant.domain.vo.UserVo userVo = userVoObjectResponse.getData();
                result.setStartCharingEnable(true);
                result.setSettlementMethod(2);
                result.setPayType(PayAccountType.PERSONAL);
                result.setUserName(userVo.getUsername());
                result.setPhone(userVo.getPhone());
                break;
            case CREDIT:
                // TODO: 2020/3/16 校验 corpUserId
                ObjectResponse<RBlocUserVo> rBlocUserListResponse = antUserFeignClient.findRBlocUserVoById(
                    vo.getPayAccountId());
                FeignResponseValidate.check(rBlocUserListResponse);
                RBlocUserVo rBlocUserVo = rBlocUserListResponse.getData();
                result.setStartCharingEnable(true);
                result.setSettlementMethod(2);
                result.setPayType(PayAccountType.CREDIT);
                result.setBlocUserId(rBlocUserVo.getBlocUserId());
                result.setBlocUserName(rBlocUserVo.getBlocUserName());
                result.setCorpUserId(rBlocUserVo.getId());
                result.setCorpUserName(rBlocUserVo.getName());
                result.setCommId(rBlocUserVo.getCommId());
                result.setCommName(rBlocUserVo.getCommName());
                result.setPhone(rBlocUserVo.getPhone());
                break;
            case COMMERCIAL:
                // TODO: 2020/3/16 校验commId
                ObjectResponse<CommCusRef> commCusRefObjectResponse = antUserFeignClient.merFindById(
                    vo.getPayAccountId());
                FeignResponseValidate.check(commCusRefObjectResponse);
                CommCusRef commCusRef = commCusRefObjectResponse.getData();
                result.setStartCharingEnable(true);
                result.setSettlementMethod(2);
                result.setPayType(PayAccountType.COMMERCIAL);
                result.setCommUserName(commCusRef.getUserName());
                result.setCommId(commCusRef.getCommId());
                result.setCommName(commCusRef.getCommName());
                result.setPhone(commCusRef.getUserPhone());
                break;
            case UNKNOWN:
                result.setStartCharingEnable(false);
                break;
            case OTHER:
                result.setStartCharingEnable(true);
                result.setSettlementMethod(1);
                break;
            default:
                log.info("场站后台充电-扣款账户默认扣款类型异常, defaultPayType:{}",
                    vo.getDefaultPayType());
                return null;
        }
        return result;
    }

    /**
     * 根据场站ID获取在线卡、离线卡、VIN码数量
     */
    public SiteCardAccountVo getCardAmountBySiteId(ListSiteBaseParam param) {
        ListResponse<CardPo> siteCardAmountResponse
            = siteDataCoreFeignClient.getCardAmountBySiteId(param);
        log.info("site = {}", siteCardAmountResponse);
        FeignResponseValidate.check(siteCardAmountResponse);
        List<CardPo> vo = siteCardAmountResponse.getData();
        SiteCardAccountVo result = new SiteCardAccountVo();

        result.setSiteId(param.getSiteId());
        vo.forEach(e -> {
            if (e.getCardType().equals(0L)) {
                result.setOnlineCardAmount(e.getAmount());
            } else if (e.getCardType().equals(1L)) {
                result.setEmergencyCardAmount(e.getAmount());
            } else if (e.getCardType().equals(4L)) {
                result.setOfflineCardAmount(e.getAmount());
            } else {
                result.setVinAmount(e.getAmount());
            }
        });

        return result;
    }


    public void updateDebitAccount(SiteDebitAccountVo request) {
//        CloudChargeVo result = this.siteDebitAccountCheck(request);
        BaseResponse response = siteDataCoreFeignClient.updateSiteDebitAccent(request);
        FeignResponseValidate.check(response);
    }

    /**
     * 场站后台充电扣款账户-所属商户下拉
     *
     * @param siteId
     * @param commercialName
     * @param commIdChain
     * @return
     */

    public ListResponse<Commercial> getSiteDebitCommIdList(String siteId, String commercialName,
        String commIdChain) {
        log.info("siteId: {}, commercialName: {}, commIdChain: {}", siteId, commercialName,
            commIdChain);
        ObjectResponse<SitePo> siteSimpleInfoVoObjectResponse
            = siteDataCoreFeignClient.getSiteById(siteId);
        //ObjectResponse<SiteSimpleInfoVo> siteSimpleInfoVoObjectResponse = siteFeignClient.getSiteSimpleInfoById(siteId);
        FeignResponseValidate.check(siteSimpleInfoVoObjectResponse);
        SiteDebitAccountVo vo = new SiteDebitAccountVo();
        vo.setSiteCommId(siteSimpleInfoVoObjectResponse.getData().getOperateId());
        vo.setCommercialName(commercialName);
        vo.setCommIdChain(commIdChain);
        ListResponse<Commercial> commercialListResponse = merchantFeignClient.getSiteDebitCommIdList(
            vo);
        FeignResponseValidate.check(commercialListResponse);
        return commercialListResponse;
    }


    public ListResponse<BlocUserDto> getSiteDebitCorpCusList(Long commId, Boolean includedHlhtCorp,
        String name) {
        log.info("commId: {}, name: {}", commId, name);
        BlocUser blocUser = new BlocUser();
        blocUser.setCommId(commId);
        blocUser.setIncludedHlhtCorp(includedHlhtCorp);
        blocUser.setBlocUserName(name);
        return antUserFeignClient.blocUserFindByCondition(blocUser);
    }


    public ListResponse<RBlocUser> getSiteDebitCorpCreditCusList(Long commId, Long blocUserId,
        String name) {
        log.info("commId: {}, blocUserId: {} name: {}", commId, blocUserId, name);
        RBlocUser rBlocUser = new RBlocUser();
        rBlocUser.setCommId(commId);
        rBlocUser.setName(name);
        rBlocUser.setBlocUserId(blocUserId);
        return antUserFeignClient.findByCondition(rBlocUser);
    }


    public ListResponse<CommCusRef> getSiteDebitCommCusList(Long commId, String name) {
        log.info("commId: {}, name: {}", commId, name);
        CommCusRef ref = new CommCusRef();
        ref.setCommId(commId);
        ref.setUserName(name);
        ref.setEnable(1);
        return antUserFeignClient.findByCondition(ref);
    }


    public BaseResponse setOnlineDate(String siteId, String onlineDate) {
        return siteDataCoreFeignClient.setOnlineDate(siteId, onlineDate);
    }

    public ObjectResponse<SiteDetailInfoVo> getSiteDetail(@RequestBody SiteGeoListRequest param) {
        ObjectResponse<SiteDetailInfoVo> siteDetail = siteDataCoreFeignClient.getSiteDetail(param);
        FeignResponseValidate.check(siteDetail);

        // 直付商家信息
        Long commId = siteDetail.getData().getOperateId();
        if (null != commId) {
            ObjectResponse<Commercial> commercial = commercialFeignClient.getCommercial(commId);
            FeignResponseValidate.check(commercial);

            String zftName = commercial.getData().getZftName();
            if (StringUtils.isNotBlank(zftName)) {
                StringBuilder builder = new StringBuilder();
                builder.append(zftName);

                boolean hasPre = false;
                Boolean enableBalance = commercial.getData().getEnableBalance();
                if (enableBalance != null && enableBalance) {
                    builder.append("    个人账户");
                    hasPre = true;
                }

                String wxSubMchId = commercial.getData().getWxSubMchId();
                if (StringUtils.isNotBlank(wxSubMchId)) {
                    builder.append(hasPre ? " | 微信" : " 微信");
                    hasPre = true;
                }

                String alipaySubMchId = commercial.getData().getAlipaySubMchId();
                if (StringUtils.isNotBlank(alipaySubMchId)) {
                    builder.append(hasPre ? " | 支付宝" : "    支付宝");
                }

                siteDetail.getData().setZftInfo(builder.toString());
            }
        }

        return siteDetail;
    }


    public SiteNoVo getSiteInfoBySiteNo(Long topCommId, String siteNo) {
        var res = this.siteDataCoreFeignClient.getSiteInfoBySiteNo(topCommId, siteNo);
        return res.getData();
    }

    public ObjectResponse<SiteNoVo> getSiteNoById(String siteId) {
        return this.siteDataCoreFeignClient.getSiteNoById(siteId);
    }

    public ListResponse<String> getExistingOperateCorpCodes() {
        return siteDataCoreFeignClient.getExistingOperateCorpCodes();
    }

    public ObjectResponse<SitePersonaliseDTO> getPersonalise(String siteId) {

        if (StringUtils.isBlank(siteId)) {
            throw new DcArgumentException("请提供场站ID值");
        }

        return this.dataCoreFeignClient.getPersonalise(siteId);
    }

    public Mono<ObjectResponse<SiteSocLimitDto>> getSocLimitInfo(String siteId) {

        IotAssert.isNotBlank(siteId, "请提供场站ID值");

        return this.reactorSiteDataCoreFeignClient.getSocLimitInfo(siteId);
    }

    public Mono<ObjectResponse<Boolean>> updateSocLimitInfo(SiteSocLimitDto param) {

        IotAssert.isNotNull(param, "请传入参数");
        IotAssert.isNotBlank(param.getSiteId(), "请提供场站ID值");

        if (CollectionUtils.isNotEmpty(param.getStrategyMainList())) {
            // 检查主策略下的分时，是否正确
            param.getStrategyMainList().forEach(e -> {
                IotAssert.isTrue(CollectionUtils.isNotEmpty(e.getStrategyList()),
                    e.getName() + "：策略内容不能为空");
                SiteSocStrategyPo siteSocStrategyPo = e.getStrategyList()
                    .get(e.getStrategyList().size() - 1);
                IotAssert.isTrue(LAST_TIME.equals(siteSocStrategyPo.getEndTimeStr()),
                    e.getName() + "：策略内容应包含一整天");
            });
        }

        return this.reactorSiteDataCoreFeignClient.updateSocLimitInfo(param);
    }

    public BaseResponse updatePersonalise(SitePersonaliseDTO dto) {
        // 参数校验
        SitePersonaliseDTO.checkValue(dto);
        return this.dataCoreFeignClient.updatePersonalise(dto);
    }

    /**
     * 根据站点id获取站点下各状态插座数量统计
     *
     * @param siteId
     * @return
     */
    public ObjectResponse<PlugStatusCountDto> getChargerStatusStatisticsBySiteId(String siteId) {
        return siteDataCoreFeignClient.getChargerStatusStatisticsBySiteId(siteId);
    }


    /**
     * 按省统计场站数量
     *
     * @param commIdChain
     * @return
     */
    public ListResponse<ProvinceSiteNumDto> getProvinceSiteNumList(String commIdChain,
        List<String> siteIdList,
        List<String> gids) {
        var param = new ListSiteParam();
        param.setIncludedHlhtSite(false)
            .setCommIdChain(commIdChain)
            .setSiteIdList(siteIdList)
            .setGids(gids);
        var res = this.bizBiFeignClient.getProvinceSiteNumList(param);
        FeignResponseValidate.check(res);
        return res;
    }

    /**
     * 按城市统计场站数量
     *
     * @param commIdChain
     * @return
     */
    public ListResponse<CitySiteNumDto> getCitySiteNumList(String commIdChain,
        List<String> siteIdList,
        List<String> gids) {
        var param = new ListSiteParam();
        param.setIncludedHlhtSite(false)
            .setCommIdChain(commIdChain)
            .setSiteIdList(siteIdList)
            .setGids(gids);
        var res = this.bizBiFeignClient.getCitySiteNumList(param);
        FeignResponseValidate.check(res);
        return res;
    }

    /**
     * 按城市统计场站数量
     *
     * @param commIdChain
     * @return
     */
    public Mono<ListResponse<DistrictSiteNumDto>> getDistrictSiteNumList(String commIdChain,
        List<String> siteIdList,
        List<String> gids) {
        var param = new ListSiteParam();
        param.setIncludedHlhtSite(false)
            .setCommIdChain(commIdChain)
            .setSiteIdList(siteIdList)
            .setGids(gids);
        return this.reactorBizBiFeignClient.getDistrictSiteNumList(param);
    }


    /**
     * 统计商户下场站数量，桩/枪数量
     *
     * @param commIdChain 商户idChain
     * @return
     */
    public ObjectResponse<CommSiteEvseCount> getCommSiteEvseCount(String commIdChain,
        List<String> siteIdList,
        List<String> gids) {
        var param = new ListSiteParam();
        param.setIncludedHlhtSite(false)
            .setCommIdChain(commIdChain)
            .setSiteIdList(siteIdList)
            .setGids(gids);
        var res = this.bizBiFeignClient.getCommSiteEvseCount(param);
        FeignResponseValidate.check(res);
        return res;
    }

    public ObjectResponse<SiteAndPlugBiVo> getSiteAndPlugBiVo(SiteAndPlugBiParam param) {
        if (CollectionUtils.isEmpty(param.getSiteStatusList())) {
            param.setSiteStatusList(
                List.of(SiteStatus.ONLINE.getCode()));
        }
        if (CollectionUtils.isEmpty(param.getBizTypeList())) {
            param.setBizTypeList(Stream.of(BizType.UNKNOWN, BizType.SELF, BizType.NON_SELF)
                .map(BizType::getCode).collect(Collectors.toList()));
        }
        return iotDeviceMgmFeignClient.getSiteAndPlugBiVo(param);
    }

    public ListResponse<String> getInvoiceDescList(String commIdchain, String desc) {
        return this.siteDataCoreFeignClient.getInvoiceDescList(commIdchain, desc);
    }

    public ObjectResponse<ChangeInfo> getChangeInfo(String siteId, Long commId) {
        return antUserFeignClient.getChangeInfo(siteId, commId);
    }

    public ObjectResponse<Integer> queryChangeCommIdBySiteId(String siteId, Long commId) {
        return antUserFeignClient.queryChangeCommIdBySiteId(siteId, commId);
    }

    public ObjectResponse<SiteConfStartList> getMoveCorpSiteConfStart(Long corpId, Long commId) {
        return dataCoreFeignClient.getMoveCorpSiteConfStart(corpId, commId);
    }

    public ObjectResponse<MoveCorpUserSocStrategyList> getMoveCorpSoc(Long corpId, Long commId) {
        return dataCoreFeignClient.getMoveCorpSoc(corpId, commId);
    }

    public ObjectResponse<MoveCorpNoCardList> getMoveCorpNoCard(Long corpId, Long commId) {
        return dataCoreFeignClient.getMoveCorpNoCard(corpId, commId);
    }

    public ListResponse<MoveCorpNoCardVo> getCorpNoCardList(Long corpId) {
        return dataCoreFeignClient.getCorpNoCardList(corpId);
    }

    public ListResponse<SiteVo> getCommNoCardList(Long commId, Long userId) {
        return dataCoreFeignClient.getCommNoCardList(commId, userId);
    }

    public ObjectResponse<SiteQrCodeVo> getSiteQrCodeVo(String siteId) {
        return siteDataCoreFeignClient.getSiteQrCodeVo(siteId);
    }

    public ListResponse<SiteChargePriceVo> getSitePriceList(String siteId) {
        return siteDataCoreFeignClient.getSitePriceList(siteId);
    }

    public ListResponse<SiteSimpleDto> getSiteListByIdChain(String idChain) {
        return dataCoreFeignClient.getSiteListByIdChain(idChain);
    }

    public ListResponse<SiteCategory> ywUserSiteCategoryList(Long sysUid) {
        ObjectResponse<AccRelativeVo> accRelativeVo = authCenterFeignClient.getYwAccount(null,
            sysUid);
        if (accRelativeVo.getData() == null) {
            throw new DcServerException("抱歉，您非运维人员，运维和巡检只开放给运维人员");
        }
        List<String> siteIdList = accRelativeVo.getData().getSiteIdList();
        if (CollectionUtils.isEmpty(siteIdList)) {
            throw new DcServerException("抱歉，账号没有绑定运维场站，请联系管理员");
        }
        return dataCoreFeignClient.ywUserSiteCategoryList(siteIdList);
    }

    public ListResponse<SiteDiscountVo> discountServiceFee(DiscountServiceParam param) {
        if (CollectionUtils.isEmpty(param.getSiteIdList())) {
            throw new DcArgumentException("场站ID列表不能为空");
        }

        if (null != param.getType() &&
            (null == param.getDiscount() && null == param.getDiscountCustomFee())) {
            switch (param.getType()) {
                case FIXED_SERVICE:
                case DISCOUNT:
                case FIXED_TOTAL:
                    if (null == param.getDiscount()) {
                        throw new DcArgumentException("协议价(discount)不能为空");
                    }
                    break;
                case CUSTOM_J_F_P_G:
                case CUSTOM_SERV_J_F_P_G:
                    if (null == param.getDiscountCustomFee()) {
                        throw new DcArgumentException("协议价(discountCustomFee)不能为空");
                    }
                    break;
            }
        }

        if ((null != param.getDiscount() || null != param.getDiscountCustomFee())
            && null == param.getType()) {
            throw new DcArgumentException("协议价类型(type)不能为空");
        }

        return siteDataCoreFeignClient.discountServiceFee(param);
    }

    public BaseResponse updateBizStatus(String evseNo, Long bizStatus) {
        return deviceMgmFeignClient.updateBizStatus(evseNo, bizStatus);
    }
}
