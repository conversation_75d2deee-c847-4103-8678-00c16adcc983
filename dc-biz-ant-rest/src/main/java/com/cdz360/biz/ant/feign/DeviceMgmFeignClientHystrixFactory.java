package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.domain.bundle.EvseBundleDto;
import com.cdz360.biz.ant.domain.bundle.EvseBundleParam;
import com.cdz360.biz.ant.domain.bundle.UpgradeStatusParam;
import com.cdz360.biz.ess.model.dto.EssEquipAlarmLangDto;
import com.cdz360.biz.ess.model.param.ListAlarmLangParam;
import com.cdz360.biz.model.evse.param.ListPackageParam;
import com.cdz360.biz.model.evse.param.UpdateEvsePackageParam;
import com.cdz360.biz.model.evse.vo.EvsePackageVo;
import com.cdz360.biz.model.iot.type.EvseVendor;
import com.cdz360.biz.model.iot.vo.TransformerPlugsVo;
import com.cdz360.biz.model.iot.vo.TransformerVo;
import com.cdz360.biz.model.parts.param.PartsCheckParam;
import com.cdz360.biz.model.upgradepg.type.BundleType;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;


@Slf4j
@Component
public class DeviceMgmFeignClientHystrixFactory implements FallbackFactory<DeviceMgmFeignClient> {

    @Override
    public DeviceMgmFeignClient create(Throwable cause) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_IOT_DEVICE_MGM,
            cause.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_IOT_DEVICE_MGM,
            cause.getStackTrace());

        return new DeviceMgmFeignClient() {

            @Override
            public ListResponse<String> getEvseModelOrFirm(String type) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<TransformerVo> listTransformer(String keyword,
                Long transformerId,
                String siteId,
                long start,
                long size) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse addTransformer(TransformerVo req) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse editTransformer(TransformerVo req) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse disableTransformer(long id) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<TransformerPlugsVo> getPlugListByTransformerId(String siteId,
                Long transformerId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<EvseBundleDto> evseBundlePage(EvseBundleParam evseBundleParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse deleteEvseBundle(Long id) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse changeStatus(UpgradeStatusParam param) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<Long> uploadEvseBundle(MultipartFile file, BundleType type,
                EvseVendor vendor,
                Long swVerCode, Long opId, String opName) {
                log.error(
                    "【服务熔断】: Service = {}, api = uploadEvseBundle (上传升级包), type = {}",
                    DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, type);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<PartsCheckParam> checkInDB(List<PartsCheckParam> items) {
                log.error(
                    "【服务熔断】: Service = {}, api = checkInDB (物料导入前检查), items.size = {}",
                    DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, items.size());
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse deleteById(UpdateEvsePackageParam param) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse updateStatus(UpdateEvsePackageParam param) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<EvsePackageVo> getList(ListPackageParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse create(UpdateEvsePackageParam param) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<String> getBrandList() {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse editPackage(UpdateEvsePackageParam param) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<EssEquipAlarmLangDto> getAlarmLangList(ListAlarmLangParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse updateBizStatus(String evseNo, Long bizStatus) {
                return RestUtils.serverBusy();
            }
        };
    }
}
