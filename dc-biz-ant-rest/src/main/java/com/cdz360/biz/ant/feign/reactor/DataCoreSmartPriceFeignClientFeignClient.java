package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.oa.param.OaStartProcessParam;
import lombok.extern.slf4j.Slf4j;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
public class DataCoreSmartPriceFeignClientFeignClient
    implements FallbackFactory<DataCoreSmartPriceFeignClient> {

    @Override
    public DataCoreSmartPriceFeignClient apply(Throwable throwable) {
        log.error("{}", throwable.getMessage(), throwable);
        return new DataCoreSmartPriceFeignClient() {
            @Override
            public Mono<ObjectResponse<String>> generateSmartPrice(OaStartProcessParam params,
                String procInstId) {
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<String>> validateGenerateSmartPrice(OaStartProcessParam params) {
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }
        };
    }
}
