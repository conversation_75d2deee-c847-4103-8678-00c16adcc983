package com.cdz360.biz.ant.service.oa.processBatch;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.biz.model.common.constant.ExcelCheckResult;
import com.cdz360.biz.model.oa.constant.OaConstants;
import com.cdz360.biz.model.oa.param.OaExcelImportParam;
import com.cdz360.biz.model.oa.vo.OaExcel.ElecPayExcelItemVo;
import com.cdz360.biz.model.oa.vo.OaExcel.ExcelImportVo;
import com.chargerlinkcar.framework.common.service.excel.BufferedExcelConvertor;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class OaProcBatchService {

    private Map<String, ExcelCheckResult> oaProcessExcelCheckMap = new HashMap<>();

    @Autowired
    private BufferedExcelConvertor excelConvertor;

    @PostConstruct
    public void init() {
        this.oaProcessExcelCheckMap.put(OaConstants.PD_ELEC_PAY, ExcelCheckResult.PROCESS_VALID);
    }

    public Mono<ExcelImportVo<?>> parseProcessExcel(OaExcelImportParam param) {
        FilePart file = param.getFile();
        log.info(">>parseProcessExcel 文件: {}", file.filename());
        try {
            File f = new File("/tmp/" + file.filename());
            return file.transferTo(f)
                .then(Mono.just("文件上传成功"))
                .map(a -> {
                    try (InputStream inputStream = new FileInputStream(f)) {
                        ExcelCheckResult excelCheckResult = oaProcessExcelCheckMap.get(
                            param.getProcDefKey());
                        ExcelImportVo<ElecPayExcelItemVo> importVo = excelConvertor.convert(
                            excelCheckResult, inputStream,
                            file.filename());
                        return importVo;
                    } catch (IOException e) {
                        log.warn("<< 解析excel文件异常. message = {}", e.getMessage(), e);
                        throw new DcServiceException("解析excel文件异常，请求确认文件内容.");
                    }
                });
        } catch (Exception e) {
            log.warn("error: {}", e.getMessage(), e);
            throw new DcServiceException("文件上传失败");
        }
    }

}
