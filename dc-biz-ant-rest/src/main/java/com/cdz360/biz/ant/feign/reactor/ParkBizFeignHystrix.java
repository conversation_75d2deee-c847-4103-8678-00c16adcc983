package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.domain.park.type.ParkingLockStatus;
import com.cdz360.biz.ant.domain.park.vo.ParkChannelVo;
import com.cdz360.biz.ant.domain.park.vo.ParkingLockVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

import java.util.function.Function;

@Slf4j
@Component
public class ParkBizFeignHystrix
        implements FallbackFactory<ParkBizFeignClient> {
    @Override
    public ParkBizFeignClient apply(Throwable throwable) {
        log.debug("err = {}", throwable.getMessage(), throwable);

        return new ParkBizFeignClient() {
            @Override
            public Mono<ListResponse<ParkChannelVo>> getParkChannelBySiteId(String siteId) {
                log.error("【服务熔断】: Service = {}, api = getParkChannelBySiteId (获取场站道闸信息列表), siteId = {}",
                        DcConstants.KEY_FEIGN_IOT_PARK, siteId);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<Boolean>> upLiftRod(Long id) {
                log.error("【服务熔断】: Service = {}, api = upLiftRod (开闸操作), id = {}",
                        DcConstants.KEY_FEIGN_IOT_PARK, id);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> switchLock(Long lockId, Boolean open) {
                log.error("【服务熔断】: Service = {}, api = switchLock (开闭锁), lockId = {}, open = {}",
                        DcConstants.KEY_FEIGN_IOT_PARK, lockId, open);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> cutPower(Long lockId) {
                log.error("【服务熔断】: Service = {}, api = cutPower (远程断电锁(断电以防水淹事故)), lockId = {}",
                        DcConstants.KEY_FEIGN_IOT_PARK, lockId);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> rebootLock(Long lockId) {
                log.error("【服务熔断】: Service = {}, api = rebootLock (远程重启锁), lockId = {}",
                        DcConstants.KEY_FEIGN_IOT_PARK, lockId);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<ParkingLockVo>> lookForLock(String siteId, String remoteLockId) {
                log.error("【服务熔断】: Service = {}, api = lookForLock (向地锁云查询地锁), siteId = {} remoteLockId = {}",
                        DcConstants.KEY_FEIGN_IOT_PARK, siteId, remoteLockId);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> fetchLockInfo(String siteId) {
                log.error("【服务熔断】: Service = {}, api = fetchLockInfo (同步场站下地锁信息), siteId = {}",
                        DcConstants.KEY_FEIGN_IOT_PARK, siteId);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<ParkingLockStatus>> lockStatusInTime(Long lockId) {
                log.error("【服务熔断】: Service = {}, api = lockStatusInTime (锁状态查询/地锁状态同步), lockId = {}",
                        DcConstants.KEY_FEIGN_IOT_PARK, lockId);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }
        };
    }

    @Override
    public <V> Function<V, ParkBizFeignClient> compose(
            Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_PARK);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(
            Function<? super ParkBizFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_PARK);
        return null;
    }
}
