package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.common.param.BaseListParam;
import com.cdz360.biz.model.tj.kc.dto.TjCompetitorSiteDto;
import com.cdz360.biz.model.tj.kc.param.ListSiteWithinTjAreaParam;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaAnalysisParam;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaAnalysisPointParam;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaParam;
import com.cdz360.biz.model.tj.kc.param.ListTjCompetitorParam;
import com.cdz360.biz.model.tj.kc.param.ListTjCompetitorSiteParam;
import com.cdz360.biz.model.tj.kc.param.ListTjDailyChargingDurationParam;
import com.cdz360.biz.model.tj.kc.param.ListTjMaterialCostParam;
import com.cdz360.biz.model.tj.kc.param.ListTjSurveyParam;
import com.cdz360.biz.model.tj.kc.param.RepeatSurveyParam;
import com.cdz360.biz.model.tj.kc.param.TjDailyChargingDurationImportParam;
import com.cdz360.biz.model.tj.kc.param.TjSurveyBiParam;
import com.cdz360.biz.model.tj.kc.param.UpdateAnalysisPointStatusParam;
import com.cdz360.biz.model.tj.kc.param.UpdateTjCompetitorParam;
import com.cdz360.biz.model.tj.kc.po.TjCashOutflowPo;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationCoefficientPo;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo;
import com.cdz360.biz.model.tj.kc.po.TjDepreciationPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationExpensesPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationIncomePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesOtherPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesPo;
import com.cdz360.biz.model.tj.kc.vo.ImportTjDailyChargingDurationVo;
import com.cdz360.biz.model.tj.kc.vo.SiteWithinTjVo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisPointVo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisVo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisWithPointVo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaVo;
import com.cdz360.biz.model.tj.kc.vo.TjCompetitorSiteVo;
import com.cdz360.biz.model.tj.kc.vo.TjCompetitorVo;
import com.cdz360.biz.model.tj.kc.vo.TjDailyChargingDurationImportItem;
import com.cdz360.biz.model.tj.kc.vo.TjDailyChargingDurationImportVo;
import com.cdz360.biz.model.tj.kc.vo.TjDailyChargingDurationVo;
import com.cdz360.biz.model.tj.kc.vo.TjMaterialCostVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyBiVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyCalculationInfoVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyCalculationResultVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyChargeAreaVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyVo;
import com.cdz360.biz.model.trading.site.po.PvReport.Base;
import io.swagger.annotations.ApiParam;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_TJ,
    fallbackFactory = BizTjFeignHystrix.class)
public interface BizTjFeignClient {

    // 获取投建区域列表
    @PostMapping(value = "/tj/area/findArea")
    Mono<ListResponse<TjAreaVo>> findArea(@RequestBody ListTjAreaParam param);

    // 获取用户的投建区域
    @PostMapping(value = "/tj/area/findUserArea")
    Mono<ListResponse<TjAreaVo>> findUserArea(@RequestBody ListTjAreaParam param);

    // 获取投建区域
    @GetMapping(value = "/tj/area/getTjAreaByAid")
    Mono<ObjectResponse<TjAreaVo>> getTjAreaByAid(@RequestParam("aid") Long aid);

    // 新建或编辑投建区域
    @PostMapping(value = "/tj/area/saveTjArea")
    Mono<ObjectResponse<TjAreaVo>> saveTjArea(@RequestBody TjAreaVo area);

    // 删除投建区域
    @GetMapping(value = "/tj/area/disableTjArea")
    Mono<ObjectResponse<TjAreaVo>> disableTjArea(@RequestParam("aid") Long aid);

    // 获取勘察场站列表
    @PostMapping(value = "/tj/survey/findTjSurvey")
    Mono<ListResponse<TjSurveyPo>> findTjSurvey(@RequestBody ListTjSurveyParam param);

    // 获取投建场站勘察汇总信息
    @PostMapping(value = "/tj/survey/tjSurveyBi")
    Mono<ObjectResponse<TjSurveyBiVo>> tjSurveyBi(@RequestBody TjSurveyBiParam param);

    // 重复勘察场站判断
    @PostMapping(value = "/tj/survey/repeatSurvey")
    Mono<ListResponse<TjSurveyVo>> repeatSurvey(@RequestBody RepeatSurveyParam param);

    // 新增或编辑场站勘察记录
    @PostMapping(value = "/tj/survey/saveTjSurvey")
    Mono<ObjectResponse<TjSurveyVo>> saveTjSurvey(@RequestBody TjSurveyVo survey);

    // 获取投建竞争者场站列表
    @PostMapping("/tj/competitor/site/findSite")
    Mono<ListResponse<TjCompetitorSiteVo>> findTjCompetitorSite(
        @RequestBody ListTjCompetitorSiteParam param);

    // 更新竞争场站附加信息
    @PostMapping("/tj/competitor/site/saveAttachInfo")
    Mono<ObjectResponse<TjCompetitorSiteVo>> saveTjCompetitorSiteAttachInfo(
        @RequestBody TjCompetitorSiteDto dto);


    // 获取投建竞争者
    @PostMapping("/tj/competitor/findTjCompetitor")
    Mono<ListResponse<TjCompetitorVo>> findTjCompetitor(
        @RequestBody ListTjCompetitorParam param);

    // 新增或编辑竞争者
    @PostMapping("/tj/competitor/saveCompetitor")
    Mono<ObjectResponse<TjCompetitorVo>> saveCompetitor(
        @RequestBody UpdateTjCompetitorParam param);


    // 同步竞争者场站信息
    @GetMapping("/tj/competitor/syncCompetitorSite")
    Mono<BaseResponse> syncCompetitorSite(
        @RequestParam(value = "provinceCode", required = false) String provinceCode,
        @RequestParam(value = "cityCode", required = false) String cityCode);

    // 删除竞争者
    @GetMapping(value = "/tj/competitor/disableCompetitor")
    Mono<ObjectResponse<TjCompetitorVo>> disableCompetitor(
        @RequestParam("competitorId") Long competitorId);

    // 获取投建分析列表
    @PostMapping("/tj/area/analysis/findTjAnalysis")
    Mono<ListResponse<TjAreaAnalysisVo>> findTjAnalysis(
        @RequestBody ListTjAreaAnalysisParam param);

    // 新增或编辑投建分析
    @PostMapping("/tj/area/analysis/saveAnalysis")
    Mono<ObjectResponse<TjAreaAnalysisWithPointVo>> saveAnalysis(
        @RequestBody TjAreaAnalysisWithPointVo param);

    // 删除投建分析
    @GetMapping(value = "/tj/area/analysis/disableAnalysis")
    Mono<ObjectResponse<TjAreaAnalysisVo>> disableAnalysis(
        @ApiParam("投建分析唯一ID") @RequestParam("analysisId") Long analysisId);

    // 通过ID获取投建分析
    @GetMapping(value = "/tj/area/analysis/getTjAnalysisById")
    Mono<ObjectResponse<TjAreaAnalysisWithPointVo>> getTjAnalysisById(
        @RequestParam("analysisId") Long analysisId);

    // 获取投建分析划分点
    @PostMapping("/tj/area/analysis/point/findTjAnalysisPoint")
    Mono<ListResponse<TjAreaAnalysisPointVo>> findTjAnalysisPoint(
        @RequestBody ListTjAreaAnalysisPointParam param);

    // 更新指定投建分析下的划分点状态
    @PostMapping("/tj/area/analysis/point/updateAnalysisPointStatus")
    Mono<ObjectResponse<Long>> updateTjAnalysisPointStatus(
        @RequestBody UpdateAnalysisPointStatusParam param);

    // 获取投建区域内的场站列表
    @PostMapping(value = "/tj/site/rel/findSiteWithinTjArea")
    Mono<ListResponse<SiteWithinTjVo>> findSiteWithinTjArea(
        @RequestBody ListSiteWithinTjAreaParam param);

    @PostMapping(value = "/tj/financialData/findTjDailyChargingDuration")
    Mono<ListResponse<TjDailyChargingDurationPo>> findTjDailyChargingDuration(
        @RequestBody ListTjDailyChargingDurationParam param);

    @GetMapping(value = "/tj/financialData/getTjDailyChargingDurationById")
    Mono<ObjectResponse<TjDailyChargingDurationPo>> getTjDailyChargingDurationById(
        @RequestParam("id") Long id);

    @PostMapping(value = "/tj/financialData/saveTjDailyChargingDuration")
    Mono<ObjectResponse<TjDailyChargingDurationPo>> saveTjDailyChargingDuration(
        @RequestBody TjDailyChargingDurationPo tjDailyChargingDurationPo);

    @GetMapping(value = "/tj/financialData/disableTjDailyChargingDuration")
    Mono<ObjectResponse<TjDailyChargingDurationPo>> disableTjDailyChargingDuration(
        @RequestParam("id") Long id);

    @PostMapping(value = "/tj/financialData/importTjDailyChargingDurationExcel")
    Mono<ObjectResponse<ImportTjDailyChargingDurationVo<TjDailyChargingDurationVo>>> importTjDailyChargingDurationExcel(
        @RequestBody List<TjDailyChargingDurationImportItem> dataList);


    @GetMapping(value = "/tj/financialData/findTjDailyChargingDurationCoefficient")
    Mono<ObjectResponse<TjDailyChargingDurationCoefficientPo>> findTjDailyChargingDurationCoefficient();

    @PostMapping(value = "/tj/financialData/saveTjDailyChargingDurationCoefficient")
    Mono<ObjectResponse<TjDailyChargingDurationCoefficientPo>> saveTjDailyChargingDurationCoefficient(
        @RequestBody TjDailyChargingDurationCoefficientPo tjDailyChargingDurationCoefficientPo);

    @GetMapping(value = "/tj/financialData/findTjDepreciation")
    Mono<ObjectResponse<TjDepreciationPo>> findTjDepreciation();

    @PostMapping(value = "/tj/financialData/saveTjDepreciation")
    Mono<ObjectResponse<TjDepreciationPo>> saveTjDepreciation(
        @RequestBody TjDepreciationPo tjDepreciationPo);

    @PostMapping(value = "/tj/financialData/findTjCashOutflow")
    Mono<ListResponse<TjCashOutflowPo>> findTjCashOutflow(
        @RequestParam("type") Integer type);

    @GetMapping(value = "/tj/financialData/getTjCashOutflowById")
    Mono<ObjectResponse<TjCashOutflowPo>> getTjCashOutflowById(
        @RequestParam("id") Long id);

    @PostMapping(value = "/tj/financialData/saveTjCashOutflow")
    Mono<ObjectResponse<TjCashOutflowPo>> saveTjCashOutflow(
        @RequestBody TjCashOutflowPo tjCashOutflowPo);

    @GetMapping(value = "/tj/financialData/disableTjCashOutflow")
    Mono<ObjectResponse<TjCashOutflowPo>> disableTjCashOutflow(
        @RequestParam("id") Long id);

    @PostMapping(value = "/tj/materialCost/findTjMaterialCost")
    Mono<ListResponse<TjMaterialCostVo>> findTjMaterialCost(
        @RequestBody ListTjMaterialCostParam param);

    @GetMapping(value = "/tj/materialCost/getTjMaterialCostById")
    Mono<ObjectResponse<TjMaterialCostVo>> getTjMaterialCostById(
        @ApiParam("物料成本唯一ID") @RequestParam("id") Long id);

    @PostMapping(value = "/tj/materialCost/saveTjMaterialCost")
    Mono<ObjectResponse<TjMaterialCostVo>> saveTjMaterialCost(
        @RequestBody TjMaterialCostVo tjMaterialCostVo);

    @GetMapping(value = "/tj/materialCost/disableTjMaterialCost")
    Mono<ObjectResponse<TjMaterialCostVo>> disableTjMaterialCost(
        @ApiParam("物料成本唯一ID") @RequestParam("id") Long id);

    @GetMapping(value = "/tj/survey/calculation/findTjSurveyChargeAreaBySurveyNo")
    Mono<ListResponse<TjSurveyChargeAreaVo>> findTjSurveyChargeAreaBySurveyNo(
        @RequestParam("surveyNo") String surveyNo);

    @PostMapping(value = "/tj/survey/calculation/saveTjSurveyChargeArea")
    Mono<ObjectResponse<TjSurveyChargeAreaVo>> saveTjSurveyChargeArea(
        @RequestBody TjSurveyChargeAreaVo tjSurveyChargeAreaVo);

    @GetMapping(value = "/tj/survey/calculation/findTjSurveySupportingFacilitiesBySurveyNo")
    Mono<ObjectResponse<TjSurveySupportingFacilitiesPo>> findTjSurveySupportingFacilitiesBySurveyNo(
        @RequestParam("surveyNo") String surveyNo);

    @PostMapping(value = "/tj/survey/calculation/saveTjSurveySupportingFacilities")
    Mono<ObjectResponse<TjSurveySupportingFacilitiesPo>> saveTjSurveySupportingFacilities(
        @RequestBody TjSurveySupportingFacilitiesPo tjSurveySupportingFacilitiesPo);

    @GetMapping(value = "/tj/survey/calculation/findTjSurveySupportingFacilitiesOtherBySurveyNo")
    Mono<ListResponse<TjSurveySupportingFacilitiesOtherPo>> findTjSurveySupportingFacilitiesOtherBySurveyNo(
        @RequestParam("surveyNo") String surveyNo);

    @PostMapping(value = "/tj/survey/calculation/saveTjSurveySupportingFacilitiesOther")
    Mono<ObjectResponse<TjSurveySupportingFacilitiesOtherPo>> saveTjSurveySupportingFacilitiesOther(
        @RequestBody TjSurveySupportingFacilitiesOtherPo tjSurveySupportingFacilitiesOtherPo);

    @GetMapping(value = "/tj/survey/calculation/findTjSurveyHighVoltageBySurveyNo")
    Mono<ListResponse<TjSurveyHighVoltagePo>> findTjSurveyHighVoltageBySurveyNo(
        @RequestParam("surveyNo") String surveyNo);

    @PostMapping(value = "/tj/survey/calculation/saveTjSurveyHighVoltage")
    Mono<ObjectResponse<TjSurveyHighVoltagePo>> saveTjSurveyHighVoltage(
        @RequestBody TjSurveyHighVoltagePo tjSurveyHighVoltagePo);

    @GetMapping(value = "/tj/survey/calculation/findTjSurveyOperationIncomeBySurveyNo")
    Mono<ObjectResponse<TjSurveyOperationIncomePo>> findTjSurveyOperationIncomeBySurveyNo(
        @RequestParam("surveyNo") String surveyNo);

    @PostMapping(value = "/tj/survey/calculation/saveTjSurveyOperationIncome")
    Mono<ObjectResponse<TjSurveyOperationIncomePo>> saveTjSurveyOperationIncome(
        @RequestBody TjSurveyOperationIncomePo tjSurveyOperationIncomePo);

    @GetMapping(value = "/tj/survey/calculation/findTjSurveyOperationExpensesBySurveyNo")
    Mono<ObjectResponse<TjSurveyOperationExpensesPo>> findTjSurveyOperationExpensesBySurveyNo(
        @RequestParam("surveyNo") String surveyNo);

    @PostMapping(value = "/tj/survey/calculation/saveTjSurveyOperationExpenses")
    Mono<ObjectResponse<TjSurveyOperationExpensesPo>> saveTjSurveyOperationExpenses(
        @RequestBody TjSurveyOperationExpensesPo tjSurveyOperationExpensesPo);


    @PostMapping(value = "/tj/survey/calculation/saveTjSurveyCalculationInfo")
    Mono<ObjectResponse<TjSurveyCalculationInfoVo>> saveTjSurveyCalculationInfo(
        @RequestBody TjSurveyCalculationInfoVo tjSurveyCalculationInfoVo);

    @GetMapping(value = "/tj/survey/calculation/calculationResult")
    Mono<ListResponse<TjSurveyCalculationResultVo>> calculationResult(
        @RequestParam("surveyNo") String surveyNo);

}
