//package com.cdz360.biz.ant.domain.vo;
//
//import com.cdz360.biz.ant.domain.dto.BatteryCluster;
//import com.fasterxml.jackson.annotation.JsonFormat;
//import com.fasterxml.jackson.annotation.JsonInclude;
//import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
//import com.fasterxml.jackson.databind.annotation.JsonSerialize;
//import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
//import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
//import io.swagger.v3.oas.annotations.media.Schema;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
//import java.time.LocalDateTime;
//
///**
// * RedisBatteryCluster
// *
// * @since 10/12/2021 8:37 PM
// * <AUTHOR>
// */
//@Data
//@EqualsAndHashCode(callSuper = true)
//public class RedisBatteryCluster extends BatteryCluster {
//    @Schema(description = "日期")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
//    @JsonSerialize(using = LocalDateTimeSerializer.class)
//    @JsonFormat(pattern = "yyyyMMdd HH:mm:ss", locale = "zh", timezone = "GMT+8")
//    private LocalDateTime time;
//}