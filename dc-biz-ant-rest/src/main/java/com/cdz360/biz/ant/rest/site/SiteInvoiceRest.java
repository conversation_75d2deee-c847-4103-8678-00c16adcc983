package com.cdz360.biz.ant.rest.site;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.service.site.SiteInvoiceService;
import com.cdz360.biz.ant.service.sysLog.CommercialSysLogService;
import com.cdz360.biz.model.site.dto.UpdateSiteInvoicedValidDto;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/api/site/invoice")
@Tag(name = "站点开票相关接口")
public class SiteInvoiceRest {

    @Autowired
    private CommercialSysLogService commSysLogService;

    @Autowired
    private SiteInvoiceService siteInvoiceService;

    @Operation(summary = "调整场站的开票状态")
    @PostMapping(value = "/updateInvoiced")
    public Mono<BaseResponse> updateInvoicedValid(
        ServerHttpRequest request,
        @RequestBody UpdateSiteInvoicedValidDto dto) {
        log.info("调整场站的开票状态: {}, dto = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(dto));
        return siteInvoiceService.updateInvoicedValid(dto)
            .doOnNext(res -> {
                if (res != null && res.getStatus() == ResultConstant.RES_SUCCESS_CODE) {
                    commSysLogService.invoicedVaildLog(dto.getSiteNameList(), request);
                }
            });
    }
}
