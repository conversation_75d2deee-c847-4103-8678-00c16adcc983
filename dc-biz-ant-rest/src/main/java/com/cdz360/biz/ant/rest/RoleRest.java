package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.biz.ant.domain.vo.SysRole;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.service.sysLog.CorpSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
public class RoleRest {

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;
    @Autowired
    private CorpSysLogService corpSysLogService;

    @PostMapping(value = "/api/role/add")
    public BaseResponse add(ServerHttpRequest request,
                            @RequestBody SysRole role) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " role = {}", role);
        IotAssert.isNotNull(role.getName(), "角色名不能为空");

        BaseResponse res = authCenterFeignClient.add(AntRestUtils.getToken2(request), role);
        corpSysLogService.roleAdd(role.getName(), request);
        return res;
    }

    @PutMapping("/api/role/modify/{id}")
    public BaseResponse modify(ServerHttpRequest request, @PathVariable Long id, @RequestBody SysRole role) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " role = {}", role);
        IotAssert.isNotNull(role.getName(), "角色名不能为空");

        BaseResponse res = authCenterFeignClient.modify(AntRestUtils.getToken2(request), id, role);
        corpSysLogService.roleModify(role.getName(), request);
        return res;
    }

    @PutMapping("/api/role/modifyStatus/{id}")
    public BaseResponse modifyStatus(ServerHttpRequest request, @PathVariable Long id, @RequestBody SysRole role) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " role = {}", role);
        IotAssert.isNotNull(role.getName(), "角色名不能为空");

        BaseResponse res = authCenterFeignClient.modify(AntRestUtils.getToken2(request), id, role);
        corpSysLogService.roleModifyStatus(role.getName(), request);
        return res;
    }

}
