package com.cdz360.biz.ant.service.srs;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.biz.ant.domain.dto.LineSrsRtData;
import com.cdz360.biz.ant.domain.vo.RadiationSampling;
import com.cdz360.biz.model.iot.param.ListCtrlParam;
import com.cdz360.biz.model.iot.type.NetType;
import com.cdz360.biz.model.trading.iot.dto.SrsRtDataDto;
import com.cdz360.biz.model.trading.iot.po.SrsPo;
import com.cdz360.biz.utils.feign.iot.SrsFeignClient;
import com.cdz360.biz.utils.service.OssArchiveService;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.nio.charset.Charset;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class SrsRtDataService {

    private static final DateTimeFormatter TIME_POINT_FORMATTER = DateTimeFormatter.ofPattern(
        "HH:mm");
    private static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern(
        "yyyy-MM-dd HH:mm:ss.SSS");
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Autowired
    private OssArchiveService ossArchiveService;
    @Autowired
    private RedisSrsRtDataService redisSrsRtDataService;
    @Autowired
    private SrsFeignClient srsFeignClient;

    /**
     * 指定微网控制器列表某天的辐射值采集
     *
     * @param gwnoList 微网控制器编号列表
     * @param date     日期
     * @param dis      采样间隔, 单位: 分钟(采样点与前一个采样点的间隔时间)
     * @return
     */
    public Flux<RadiationSampling> dayRadiationSampling(List<String> gwnoList, LocalDate date,
        int dis) {
        return Flux.fromIterable(gwnoList)
            .flatMap(gwno -> this.dayRadiationSampling(gwno, date, dis))
            .collectList()
            .map(list -> {
                Map<String, Integer> collect = list.stream().collect(Collectors.groupingBy(
                    RadiationSampling::getTime,
                    Collectors.summingInt(RadiationSampling::getRadiation)));

                List<RadiationSampling> result = new ArrayList<>();
                for (String time : collect.keySet()) {
                    result.add(new RadiationSampling()
                        .setTime(time)
                        .setRadiation(collect.get(time)));
                }
                return result.stream()
                    .sorted(Comparator.comparing(RadiationSampling::getTime));
            })
            .flatMapMany(Flux::fromStream);
    }

    /**
     * 微网控制器某一天的辐射值采集
     *
     * @param gwno 微网控制器编号
     * @param date 日期
     * @param dis  采样间隔, 单位: 分钟(采样点与前一个采样点的间隔时间)
     * @return
     */
    public Flux<RadiationSampling> dayRadiationSampling(String gwno, LocalDate date, int dis) {
        final List<LocalDateTime> times = splitDay(date, dis);
        log.debug("时段: time = {}", times.size());

        return Mono.just(new ListCtrlParam().setGwno(gwno))
            .flatMap(srsFeignClient::findSrsList)
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .flatMap(srsList -> {
                List<String> dnoList = srsList.stream()
                    .map(SrsPo::getDno).distinct().collect(Collectors.toList());

                if (LocalDate.now().isEqual(date)) {
                    return this.redisSrsRtDataService.dayRadiationSampling4Redis(dnoList, date,
                            times)
                        .collectList();
                }

                return this.dayRadiationSampling4File(srsList, date, times)
                    .collectList();
            })
            .flatMapMany(Flux::fromIterable);
    }

    private Flux<RadiationSampling> dayRadiationSampling4File(
        List<SrsPo> srsList, LocalDate date, final List<LocalDateTime> times) {
        if (CollectionUtils.isEmpty(srsList)) {
            return Flux.fromIterable(times)
                .map(time -> new RadiationSampling()
                    .setTime(time.format(TIME_POINT_FORMATTER))
                    .setRadiation(0));
        }

        return Flux.fromIterable(srsList)
            .flatMap(gti -> this.dayPowerSampling4File(gti, date, times))
            .collectList()
            .map(list -> {
                Map<String, Integer> collect = list.stream().collect(Collectors.groupingBy(
                    RadiationSampling::getTime,
                    Collectors.summingInt(RadiationSampling::getRadiation)));

                List<RadiationSampling> result = new ArrayList<>();
                for (String time : collect.keySet()) {
                    result.add(new RadiationSampling()
                        .setTime(time)
                        .setRadiation(collect.get(time)));
                }
                return result.stream()
                    .sorted(Comparator.comparing(RadiationSampling::getTime));
            })
            .flatMapMany(Flux::fromStream);
    }

    private Flux<RadiationSampling> dayPowerSampling4File(
        SrsPo po, LocalDate date, final List<LocalDateTime> times) {
        return ossArchiveService.getOSSDeviceRtData(po.getSiteId(), po.getDno(), null, NetType.SRS,
                date)
            .filter(Optional::isPresent)
            .map(Optional::get)
            .map(ossObject -> {
                List<RadiationSampling> samplingList = new ArrayList<>();
                try (InputStream gzipStream = new GZIPInputStream(ossObject.getObjectContent());
                    Reader decoder = new InputStreamReader(gzipStream, Charset.defaultCharset());
                    BufferedReader reader = new BufferedReader(decoder)) {

                    boolean hasLine = true;
                    LineSrsRtData last = null;
                    for (LocalDateTime time : times) {
                        if (null != last) {
                            LocalDateTime temp = last.getTime()
                                .withSecond(0)
                                .withNano(0);
                            if (time.isBefore(temp)) {
                                addZeroSampling(samplingList, time);
                                continue;
                            } else if (time.isEqual(temp)) {
                                samplingList.add(new RadiationSampling()
                                    .setTime(time.format(TIME_POINT_FORMATTER))
                                    .setRadiation(
                                        this.radiationFormat(last.getRtData().getValue())));
                                continue;
                            }
                        }

                        while (hasLine) {
                            String line = null;
                            try {
                                line = reader.readLine();
                            } catch (IOException e) {
                                // nothing
                                log.error("err = {}", e.getMessage(), e);
                            }

                            if (line == null) {
                                hasLine = false;
                                break;
                            }

                            LineSrsRtData rtData = lineData(line);
                            if (null == rtData || rtData.getTime() == null
                                || rtData.getRtData() == null) {
                                continue;
                            }

                            LocalDateTime temp = rtData.getTime()
                                .withSecond(0)
                                .withNano(0);

                            if (temp.isBefore(time)) {
                                continue;
                            } else if (temp.isEqual(time)) {
                                samplingList.add(new RadiationSampling()
                                    .setTime(time.format(TIME_POINT_FORMATTER))
                                    .setRadiation(
                                        this.radiationFormat(rtData.getRtData().getValue())));
                            } else if (temp.isAfter(time)) {
                                if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(samplingList)
                                    && samplingList.get(samplingList.size() - 1).getRadiation()
                                    != null
                                    && samplingList.get(samplingList.size() - 1).getRadiation()
                                    > 0) {
                                    Integer lastV = last != null && last.getRtData() != null
                                        && last.getRtData().getValue() != null
                                        ? last.getRtData().getValue() : 0;
                                    Integer value =
                                        NumberUtils.sum(lastV, rtData.getRtData().getValue()) / 2;
                                    samplingList.add(new RadiationSampling()
                                        .setTime(time.format(TIME_POINT_FORMATTER))
                                        .setRadiation(this.radiationFormat(value)));
                                } else {
                                    addZeroSampling(samplingList, time);
                                }
                            }
                            last = rtData;
                            rtData = null;
                            break;
                        }

                        if (!hasLine) {
                            addZeroSampling(samplingList, time);
                        }
                    }
                } catch (Exception e) {
                    // nothing
                    log.error("err = {}", e.getMessage(), e);
                }
                return samplingList;
            })
            .flatMapMany(Flux::fromIterable)
            .switchIfEmpty(Flux.fromIterable(times)
                .map(time -> new RadiationSampling()
                    .setTime(time.format(TIME_POINT_FORMATTER))
                    .setRadiation(0)));

    }

    private Integer radiationFormat(Integer value) {
        return value != null ? value : 0;
    }

    private static void addZeroSampling(List<RadiationSampling> list, LocalDateTime time) {
        list.add(new RadiationSampling()
            .setTime(time.format(TIME_POINT_FORMATTER))
            .setRadiation(0));
    }

    private static List<LocalDateTime> splitDay(LocalDate date, int dis) {
        List<LocalDateTime> times = new ArrayList<>();
        LocalDateTime start = date.atStartOfDay();
        LocalDateTime end = date.plusDays(1).atStartOfDay();
        LocalDateTime now = LocalDateTime.now();
        while (start.isBefore(end) && start.isBefore(now)) {
            times.add(start);
            start = start.plusMinutes(dis);
        }
        return times;
    }

    private static LineSrsRtData lineData(String line) {
        String[] split = line.split(" \\| ");
        if (split.length == 3) {
            LineSrsRtData rtData = new LineSrsRtData();
            rtData.setTime(LocalDateTime.parse(split[0], TIME_FORMATTER));

            String[] serial = split[1].split("/");
            rtData.setDno(serial[0])
                .setSid(Integer.valueOf(serial[1]));

            rtData.setRtData(
                JsonUtils.fromJson(split[2].replaceAll("\r|\n", ""), SrsRtDataDto.class));
            return rtData;
        }

        return null;
    }

}
