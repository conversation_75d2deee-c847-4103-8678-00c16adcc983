package com.cdz360.biz.ant.rest.yw;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.aop.CheckToken;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.feign.BizBiFeignClient;
import com.cdz360.biz.ant.feign.InspectionFeignClient;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.auth.sys.vo.AccRelativeVo;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.trading.site.dto.InspectionRecordDto;
import com.cdz360.biz.model.trading.site.dto.RecentInspectionRecordDto;
import com.cdz360.biz.model.trading.site.param.ChangeRecordParam;
import com.cdz360.biz.model.trading.site.param.InspectionParam;
import com.cdz360.biz.model.trading.site.param.RecordParam;
import com.cdz360.biz.model.trading.site.param.SiteInspectionRecordParam;
import com.cdz360.biz.model.trading.site.po.SiteInspectionCfgPo;
import com.cdz360.biz.model.trading.site.po.SiteInspectionRecordPo;
import com.cdz360.biz.model.trading.site.type.SiteInspectionStatus;
import com.cdz360.biz.model.trading.site.vo.InspectionRecordBi;
import com.cdz360.biz.model.trading.site.vo.InspectionRecordVo;
import com.cdz360.biz.model.trading.site.vo.SiteInspectionRecordVo;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import com.chargerlinkcar.framework.common.domain.CommercialSample;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.Duration;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "巡检工单相关接口", description = "巡检工单")
@Slf4j
@RestController
@RequestMapping(value = "/api/inspection")
public class InspectionRest extends BaseController {

    @Autowired
    private InspectionFeignClient inspectionFeignClient;
    @Autowired
    private BizBiFeignClient bizBiFeignClient;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

//    @Autowired
//    private AsyncBizBiFeignClient asyncBizBiFeignClient;

    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;

    @Operation(summary = "获取最近一次巡检信息")
    @GetMapping(value = "/getRecentRecord")
    public ObjectResponse<RecentInspectionRecordDto> getRecentRecord(
        @RequestParam("siteId") String siteId) {
        log.info("getRecentRecord siteId: {}", siteId);
        return inspectionFeignClient.getRecentRecord(siteId);
    }

    @Operation(summary = "获取场站巡检的配置")
    @GetMapping(value = "/getConfig")
    public ObjectResponse<SiteInspectionCfgPo> getConfig(@RequestParam("siteId") String siteId) {
        log.info("getConfig siteId: {}", siteId);
        return inspectionFeignClient.getConfig(siteId);
    }

    @Operation(summary = "修改巡检配置")
    @PostMapping(value = "/editConfig")
    public BaseResponse editConfig(@RequestBody SiteInspectionCfgPo req) {
        log.info("editConfig req: {}", req);
        return inspectionFeignClient.editConfig(req);
    }

    @Operation(summary = "巡检记录-饼图数据")
    @PostMapping(value = "/getRecordBi")
    public ListResponse<InspectionRecordBi> getRecordBi(
        @RequestBody SiteInspectionRecordParam param) {
        log.info("getRecordBi param: {}", param);
        return inspectionFeignClient.getRecordBi(param);
    }

    @Operation(summary = "巡检记录-表格数据")
    @PostMapping(value = "/getRecordVoList")
    public ListResponse<InspectionRecordVo> getRecordVoList(
        @RequestBody SiteInspectionRecordParam param) {
        log.info("getRecordVoList param: {}", param);
        return inspectionFeignClient.getRecordVoList(param);
    }

    @Operation(summary = "巡检工单")
    @PostMapping(value = "/getRecords")
    public ListResponse<InspectionRecordDto> getRecords(ServerHttpRequest request,
        @RequestBody RecordParam param) {
        if (CollectionUtils.isEmpty(param.getGids())) {
            List<String> gids = AntRestUtils.getSysUserGids(request);
            if (CollectionUtils.isNotEmpty(gids)) {
                param.setGids(gids);
            } else {
                param.setCommIdChain(AntRestUtils.getCommIdChainAndFilter(request));
            }
        }
        log.info("getRecords param: {}", param);
        return inspectionFeignClient.getRecords(param);
    }

    @Operation(summary = "修改工单状态")
    @GetMapping(value = "/changeStatus")
    public BaseResponse changeStatus(ServerHttpRequest request,
        @RequestParam("recordId") Long recordId,
        @RequestParam("status") SiteInspectionStatus status) {
        long sysUserId = super.getUserIdLong2(request);
        log.info("changeStatus recordId: {}, status: {}, sysUserId: {}", recordId, status,
            sysUserId);
        return inspectionFeignClient.changeStatus(sysUserId, recordId, status);
    }

    @Operation(summary = "批量质检")
    @PostMapping(value = "/changeStatusBatch")
    public BaseResponse changeStatusBatch(ServerHttpRequest request,
        @RequestBody ChangeRecordParam param) {
        long sysUserId = super.getUserIdLong2(request);
        param.setSysUserId(sysUserId);
        log.info("changeStatusBatch param: {}", param);
        return inspectionFeignClient.changeStatusBatch(param);
    }

    @Operation(summary = "删除巡检工单")
    @GetMapping(value = "/del")
    public BaseResponse recordDel(ServerHttpRequest request,
        @RequestParam("recordId") Long recordId) {
        long sysUserId = super.getUserIdLong2(request);
        log.info("recordDel sysUserId: {}, sysUserId: {}", sysUserId, recordId);
        return inspectionFeignClient.recordDel(sysUserId, recordId);
    }

    @Operation(summary = "巡检详情")
    @GetMapping(value = "/getDetail")
    public ObjectResponse<SiteInspectionRecordVo> getDetail(@RequestParam("id") Long id) {
        log.info("getDetail id: {}", id);
        return inspectionFeignClient.getDetail(id);
    }

    @Operation(summary = "导出巡检详情")
    @GetMapping(value = "/recordExport")
    public Mono<ObjectResponse<ExcelPosition>> recordExport(
        ServerHttpRequest request,
        @ApiParam(value = "下载导出文件名") @RequestParam(value = "exFileName", required = false) String exFileName,
        @RequestParam("id") Long id) {
        log.info("recordExport id: {}", id);
        Long sysUid = AntRestUtils.getSysUid(request);
        ObjectNode reqParam = new ObjectMapper().createObjectNode();
        reqParam.put("recordId", id);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.PDF)
            .setUid(sysUid)
            .setExFileName(StringUtils.isNotBlank(exFileName) ? exFileName : "巡检详情")
            .setFunctionMap(DownloadFunctionType.XJ_ORDER_DETAIL)
            .setReqParam(reqParam.toString());
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return bizBiFeignClient.exportSiteInspection(id);
    }

    @Operation(summary = "获取待巡检场站")
    @PostMapping(value = "/getNeedInspectionSite")
    public ListResponse<RecentInspectionRecordDto> getNeedInspectionSite(ServerHttpRequest request,
        @RequestBody BaseListParam param) {
        String commIdChain = super.getCommIdChain2(request);
        log.info("getNeedInspectionSite commIdChain: {}, param: {}", commIdChain, param);
        ObjectResponse<AccRelativeVo> voObjectResponse = authCenterFeignClient.getYwAccount(
            super.getToken2(request), AntRestUtils.getSysUid(request));
        FeignResponseValidate.checkIgnoreData(voObjectResponse);
        if (voObjectResponse.getData() == null
            || CollectionUtils.isEmpty(voObjectResponse.getData().getSiteIdList())) {
            return RestUtils.buildListResponse(null);
        }
        InspectionParam req = new InspectionParam();
        BeanUtils.copyProperties(param, req);
        req.setCommIdChain(commIdChain)
            .setSiteIdList(voObjectResponse.getData().getSiteIdList());
        return inspectionFeignClient.getNeedInspectionSite(req);
    }

    @Operation(summary = "获取待处理巡检工单")
    @PostMapping(value = "/getToBeInspectRecord")
    public ListResponse<SiteInspectionRecordVo> getToBeInspectRecord(ServerHttpRequest request,
        @RequestBody BaseListParam param) {
        long sysUserId = super.getUserIdLong2(request);
        log.info("getToBeInspectRecord sysUserId: {}, param: {}", sysUserId, param);
        return inspectionFeignClient.getToBeInspectRecord(sysUserId, param);
    }

    @Operation(summary = "获取历史巡检工单")
    @PostMapping(value = "/getHistoryRecord")
    public ListResponse<SiteInspectionRecordVo> getHistoryRecord(ServerHttpRequest request,
        @RequestBody BaseListParam param) {
        long sysUserId = super.getUserIdLong2(request);
        log.info("getHistoryRecord sysUserId: {}, param: {}", sysUserId, param);
        return inspectionFeignClient.getHistoryRecord(sysUserId, param);
    }

    @Operation(summary = "通过枪号获取场站巡检信息")
    @GetMapping(value = "/getSiteByPlugNo")
    public ObjectResponse<SiteVo> getSiteByPlugNo(ServerHttpRequest request,
        @RequestParam(value = "plugNo", required = false) String plugNo,
        @RequestParam(value = "qrCode", required = false) String qrCode,
        @RequestParam(value = "siteId", required = false) String siteId) {
        log.info("getSiteByPlugNo plugNo: {}, siteId: {}", plugNo, siteId);
        CommercialSample comm = super.getCommercialSample2(request);
        Long topCommId = comm == null ? null : comm.getTopCommId();
        return inspectionFeignClient.getSiteByPlugNo(topCommId, plugNo, qrCode, siteId);
    }

    @CheckToken
    @Operation(summary = "创建前检查")
    @GetMapping(value = "/preCheck")
    public Mono<BaseResponse> preCheck(ServerHttpRequest request,
        @RequestParam(value = "siteId") String siteId) {
        long sysUserId = super.getUserIdLong2(request);
        log.info("preCheck sysUserId: {}, siteId: {}", sysUserId, siteId);
        return Mono.just(sysUserId)
            .map(uid -> authCenterFeignClient.getYwAccount(AntRestUtils.getToken2(request), uid))
            .doOnNext(FeignResponseValidate::checkIgnoreData)
            .doOnNext(AccRelativeVoResp -> {
                AccRelativeVo user = AccRelativeVoResp.getData();
                // 判断是否包含该场站
                if (user == null ||
                    CollectionUtils.isEmpty(user.getSiteIdList()) ||
                    !user.getSiteIdList().contains(siteId)) {
                    throw new DcServiceException(
                        "当前场站非您运维负责的场站，不可创建巡检单，请确认场站！");
                }
            })
            .map(e -> RestUtils.success());
    }

    @CheckToken
    @Operation(summary = "创建巡检单")
    @GetMapping(value = "/create")
    public ObjectResponse<SiteInspectionRecordPo> create(ServerHttpRequest request,
        @RequestParam(value = "siteId") String siteId,
        @RequestParam(value = "inspectionType") Integer inspectionType) {
        long sysUserId = super.getUserIdLong2(request);
        log.info("create sysUserId: {}, siteId: {}, inspectionType: {}", sysUserId, siteId,
            inspectionType);
        return Mono.just(sysUserId)
            .doOnNext(AccRelativeVoResp -> {
                this.preCheck(request, siteId);
            })
            .map(u -> inspectionFeignClient.create(sysUserId, siteId, inspectionType))
            .block(Duration.ofSeconds(50L));
    }

    @Operation(summary = "提交巡检单")
    @PostMapping(value = "/save")
    public BaseResponse save(ServerHttpRequest request,
        @RequestBody SiteInspectionRecordPo req) {
        long sysUserId = super.getUserIdLong2(request);
        log.info("save sysUserId: {}, req: {}", sysUserId, req);
        req.setOpUid(sysUserId);
        return inspectionFeignClient.save(req);
    }

    @Operation(summary = "提交巡检单")
    @PostMapping(value = "/report")
    public BaseResponse report(ServerHttpRequest request,
        @RequestBody SiteInspectionRecordPo req) {
        long sysUserId = super.getUserIdLong2(request);
        log.info("report sysUserId: {}, req: {}", sysUserId, req);
        req.setOpUid(sysUserId);
        return inspectionFeignClient.report(req);
    }

    @Operation(summary = "巡检工单列表导出(EXCEL)")
    @PostMapping(value = "/exportRecordExcel")
    public Mono<ObjectResponse<ExcelPosition>> exportRecordExcel(
        ServerHttpRequest request, @RequestBody RecordParam param) {
        log.info("巡检工单列表导出(EXCEL): {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

//        String commIdChain = AntRestUtils.getCommIdChain(request);
//        param.setCommIdChain(commIdChain);

        if (CollectionUtils.isEmpty(param.getGids())) {
            List<String> gids = AntRestUtils.getSysUserGids(request);
            if (CollectionUtils.isNotEmpty(gids)) {
                param.setGids(gids);
            } else {
                param.setCommIdChain(AntRestUtils.getCommIdChainAndFilter(request));
            }
        }

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("巡检工单")
            .setFunctionMap(DownloadFunctionType.XJ_ORDER)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return asyncBizBiFeignClient.exportInspectionRecExcel(param);
    }
}
