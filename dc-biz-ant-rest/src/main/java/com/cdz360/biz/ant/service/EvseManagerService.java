package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.ant.constant.CardStatus;
import com.cdz360.biz.ant.feign.AntUserFeignClient;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.DeviceFeignClient;
import com.cdz360.biz.ant.service.iot.IotEvseCfgService;
import com.cdz360.biz.model.cus.user.dto.WhiteCard;
import com.cdz360.biz.model.cus.user.dto.WhiteCardDto;
import com.cdz360.biz.model.cus.user.param.WhiteCardRequest;
import com.cdz360.biz.model.iot.param.ModifyEvseCfgParam;
import com.cdz360.biz.model.iot.vo.WhiteCardCfgVo;
import com.cdz360.biz.model.trading.evse.EvseInfo;
import com.cdz360.biz.model.trading.site.vo.SiteChargePriceVo;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.framework.common.feign.SiteDataCoreFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class EvseManagerService {

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;
    @Autowired
    private AntUserFeignClient antUserFeignClient;
    @Autowired
    private DeviceFeignClient deviceFeignClient;
    @Autowired
    private IotEvseCfgService iotEvseCfgService;

    @Autowired
    private SiteDataCoreFeignClient siteDataCoreFeignClient;

    @Autowired
    private RedisIotReadService redisIotReadService;

    public ObjectResponse<EvseInfo> getEvseInfo(String evseNo) {
        ObjectResponse<EvseInfo> evseInfoObjectResponse = dataCoreFeignClient.getEvseInfo(evseNo);
        FeignResponseValidate.check(evseInfoObjectResponse);
        if(CollectionUtils.isNotEmpty(evseInfoObjectResponse.getData().getWhiteCards())) {

            List<WhiteCardCfgVo> collect = evseInfoObjectResponse.getData()
                    .getWhiteCards()
                    .stream()
                    .filter(e -> !CardStatus.DELETED.getCode().equals(e.getCardStatus()))
                    .collect(Collectors.toList());

            evseInfoObjectResponse.getData().setWhiteCards(collect);
        }
        return evseInfoObjectResponse;
    }

    public BaseResponse downDefultSetting2AllEvse(String siteId) {
        BaseResponse response = dataCoreFeignClient.downDefultSetting2AllEvse(siteId);
        FeignResponseValidate.check(response);
        return response;
    }

    public ModifyEvseCfgParam downDefultTemplate2AllEvse(String siteId, Long opUid) {
        ObjectResponse<ModifyEvseCfgParam> response = dataCoreFeignClient.getModifyEvseCfgParam(siteId);
        FeignResponseValidate.check(response);
        /**
         * 桩管家整站下发计费模板
         * 按照场站绑定的计费模板  以及桩类型下发
         */
        ListResponse<SiteChargePriceVo> sitePriceResponse = siteDataCoreFeignClient.getSitePriceList(siteId);
        FeignResponseValidate.check(sitePriceResponse);

        List<SiteChargePriceVo> sitePriceList = sitePriceResponse.getData();
        if (CollectionUtils.isEmpty(sitePriceList)) {
            throw new DcArgumentException("该场站未绑定计费模板");
        }
        if(sitePriceList.stream().anyMatch(x->Boolean.FALSE.equals(x.isEnable()))) {
            throw new DcArgumentException("该场站存在已失效计费模板");
        }
        ModifyEvseCfgParam cfgParam = response.getData();
        if (sitePriceList.size() == 1) {
            // 场站默认计费下发
            cfgParam.setPriceSchemeId(sitePriceList.get(0).getId());
            this.iotEvseCfgService.downPriceTemplate(cfgParam, opUid);
        } else {
            // 交直流分开下发
            List<EvseVo> evseList = redisIotReadService.getEvseList(cfgParam.getEvseNoList());
            Map<SupplyType, Long> priceSchemeMap = sitePriceList.stream()
                .collect(Collectors.toMap(SiteChargePriceVo::getTemplateType, SiteChargePriceVo::getId));

            // 交流下发
            if (evseList.stream().anyMatch(x -> SupplyType.AC.equals(x.getSupplyType()))) {
                List<String> acList = evseList.stream()
                    .filter(x -> SupplyType.AC.equals(x.getSupplyType()))
                    .map(EvseVo::getEvseNo)
                    .collect(Collectors.toList());
                cfgParam.setPriceSchemeId(priceSchemeMap.get(SupplyType.AC));
                cfgParam.setEvseNoList(acList);
                this.iotEvseCfgService.downPriceTemplate(cfgParam, opUid);
            }

            // 直流下发
            if (evseList.stream().anyMatch(x -> SupplyType.DC.equals(x.getSupplyType()))) {
                List<String> dcList = evseList.stream()
                    .filter(x -> SupplyType.DC.equals(x.getSupplyType()))
                    .map(EvseVo::getEvseNo)
                    .collect(Collectors.toList());
                cfgParam.setPriceSchemeId(priceSchemeMap.get(SupplyType.DC));
                cfgParam.setEvseNoList(dcList);
                this.iotEvseCfgService.downPriceTemplate(response.getData(), opUid);
            }
        }
//        this.iotEvseCfgService.downPriceTemplate(response.getData(), opUid);
        return response.getData();
    }

    public BaseResponse downDefultTemplate2AllEvse(String evseNo) {
        BaseResponse response = dataCoreFeignClient.downSettingByEvse(evseNo);
        FeignResponseValidate.check(response);
        return response;
    }

    public List<String> sendWhiteCard(String siteId, String evseNo) {
        WhiteCardRequest whiteCardRequest = new WhiteCardRequest();
        whiteCardRequest.setCardChipNoList(null)
                .setIsAbandon(false)
                .setSite(siteId);
        ListResponse<WhiteCardDto> response = antUserFeignClient.queryWhiteCardDtoBySiteList(whiteCardRequest);
        FeignResponseValidate.check(response);
        WhiteCardDto dto = response.getData().get(0);
        dto.setSingleEvseNo(evseNo);

        ListResponse<String> stringListResponse = deviceFeignClient.sendWhiteCard(dto);
        FeignResponseValidate.check(stringListResponse);
        return dto.getWhiteCardList().stream().map(WhiteCard::getCardChipNo).collect(Collectors.toList());
    }
}
