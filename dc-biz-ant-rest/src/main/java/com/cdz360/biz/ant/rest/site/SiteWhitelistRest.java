package com.cdz360.biz.ant.rest.site;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.service.SiteWhitelistService;
import com.cdz360.biz.ant.service.sysLog.CustomerSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.cus.site.param.SiteWhitelistParam;
import com.cdz360.biz.model.cus.site.po.SiteWhitelistPo;
import com.cdz360.biz.model.cus.site.vo.SiteWhitelistVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * SiteWhitelistRest
 *
 * @since 12/25/2020 4:06 PM
 * <AUTHOR>
 */

@Tag(name = "场站用户白名单相关接口", description = "场站用户白名单")
@Slf4j
@RestController
public class SiteWhitelistRest {

    @Autowired
    private SiteWhitelistService siteWhitelistService;

    @Autowired
    private CustomerSysLogService customerSysLogService;

    @Operation(summary = "查询场站白名单列表")
    @PostMapping(value = "/api/siteWhitelist/find")
    public Mono<ListResponse<SiteWhitelistVo>> find(
            ServerHttpRequest request, @RequestBody SiteWhitelistPo param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}", JsonUtils.toJsonString(param));
        return this.siteWhitelistService.querySiteWhitelist(param);
    }

    @Operation(summary = "新增场站白名单列表")
    @PostMapping(value = "/api/siteWhitelist/addWhitelist")
    public Mono<ObjectResponse<Boolean>> addWhitelist(
            ServerHttpRequest request, @RequestBody SiteWhitelistParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}", JsonUtils.toJsonString(param));
        param.setTopCommId(AntRestUtils.getTopCommId(request));
        return this.siteWhitelistService
                .addWhitelist(param)
                .doOnNext(e -> customerSysLogService.addWhitelist(param, request));
    }

    @Operation(summary = "删除场站白名单列表")
    @PostMapping(value = "/api/siteWhitelist/removeWhitelist")
    public Mono<ObjectResponse<Integer>> removeWhitelist(
            ServerHttpRequest request, @RequestBody List<SiteWhitelistParam> param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}", JsonUtils.toJsonString(param));
        return this.siteWhitelistService.removeWhitelist(param).doOnNext(e -> {
            customerSysLogService.removeWhitelist(param, request);
        });
    }
}