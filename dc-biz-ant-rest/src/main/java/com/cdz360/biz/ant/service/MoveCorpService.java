package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.ant.feign.TradingFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorSiteDataCoreFeignClient;
import com.cdz360.biz.ant.service.site.SiteChargeJobService;
import com.cdz360.biz.ant.service.site.SiteService;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * MoveCorpService
 * 
 * @since 11/2/2020 5:13 PM
 * <AUTHOR>
 */
@Slf4j
@Service
public class MoveCorpService {

    @Autowired
    private TradingFeignClient tradingFeignClient;

    @Autowired
    private CardService cardService;

    @Autowired
    private SiteService siteService;

    @Autowired
    private VinService vinService;

    @Autowired
    private SiteChargeJobService siteChargeJobService;

    @Autowired
    private ReactorSiteDataCoreFeignClient reactorSiteDataCoreFeignClient;

    public Mono<ObjectResponse<Boolean>> move(Long corpId, Long commId) {

        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");


        return Mono.just(tradingFeignClient.getUnfinishOrderByCorpId(corpId))
                .doOnNext(e -> {
                    FeignResponseValidate.check(e);
                    IotAssert.isTrue(CollectionUtils.isEmpty(e.getData()),
                            "当前企业存在未结算充电订单待处理，无法修改商户");
                })
                .map(e -> cardService.getEmergencyCardByCorpId(corpId, commId))
                .doOnNext(e -> {
                    FeignResponseValidate.check(e);
                    IotAssert.isTrue(CollectionUtils.isEmpty(e.getData().getRemoveList()),
                            "修改商户后，存在关联的紧急卡的扣款账户与站点不匹配，请先弃用相关紧急卡");
                }).flatMap(e -> {
//                    siteService.getMoveCorpSiteConfStart(corpId, commId);dc
//                    siteChargeJobService.getMoveCorpDetail(corpId, commId);dc
//                    cardService.getMoveCorpCardByCorpId(corpId, commId);ur
//                    vinService.getMoveCorpVINByCorpId(corpId, commId);ur
//                    siteService.getMoveCorpSoc(corpId, commId);dc
                    return reactorSiteDataCoreFeignClient.moveCorp(corpId, commId);
//                    return new ObjectResponse<>();
//                    return Mono.just(new ObjectResponse<>(Boolean.TRUE));
                });
    }
}