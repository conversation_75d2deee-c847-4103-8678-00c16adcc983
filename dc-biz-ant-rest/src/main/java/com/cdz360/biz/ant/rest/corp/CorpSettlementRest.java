package com.cdz360.biz.ant.rest.corp;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.UserType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.feign.BizBiFeignClient;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.ant.service.corp.CorpSettlementService;
import com.cdz360.biz.ant.service.sysLog.CorpSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.discount.param.UpdateCorpDiscountParam;
import com.cdz360.biz.model.cus.settlement.dto.SettlementDto;
import com.cdz360.biz.model.cus.settlement.param.ListSettlementParam;
import com.cdz360.biz.model.cus.settlement.param.UpdateCorpSettlementParam;
import com.cdz360.biz.model.cus.settlement.vo.CorpSettlementCfgVo;
import com.cdz360.biz.model.cus.settlement.vo.SettlementVo;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.chargerlinkcar.framework.common.domain.BillExportParam;
import com.chargerlinkcar.framework.common.domain.CommercialSample;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "企业结算相关接口", description = "企业结算相关接口")
@Slf4j
@RestController
public class CorpSettlementRest extends BaseController {

    @Autowired
    private CorpSettlementService corpSettlementService;
    @Autowired
    private CorpSysLogService corpSysLogService;

    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;

    @Autowired
    private BizBiFeignClient bizBiFeignClient;

    @Operation(summary = "通过计算配置ID获取企业客户结算信息")
    @GetMapping(value = "/api/corp/getSettlementCfgById")
    public ObjectResponse<CorpSettlementCfgVo> getSettlementCfgById(
        ServerHttpRequest request,
        @Parameter(name = "结算配置Id", required = true) @RequestParam(value = "cfgId") Long cfgId) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return corpSettlementService.getSettlementCfgById(cfgId);
    }

    @Operation(summary = "获取企业客户结算配置信息")
    @GetMapping(value = "/api/corp/getSettlementCfg")
    public ObjectResponse<CorpSettlementCfgVo> getSettlementCfg(
        ServerHttpRequest request,
        @Parameter(name = "企业Id(传blocUserId值)", required = true) @RequestParam(value = "corpId") Long corpId) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return corpSettlementService.getSettlementCfg(corpId);
    }

    @Operation(summary = "更新企业客户预付费协议价")
    @PostMapping(value = "/api/corp/updateDiscount")
    public ObjectResponse<CorpPo> updateDiscount(
        ServerHttpRequest request, @RequestBody UpdateCorpDiscountParam param) {
        log.info(
            LoggerHelper2.formatEnterLog(request, false) + " param = " + JsonUtils.toJsonString(
                param));

        // 获取登录用户相关信息
        CommercialSample comm = this.getCommercialSample2(request);
        if (ObjectUtils.isEmpty(comm)) {
            throw new DcArgumentException("当前操作用户信息不存在，请重新登录");
        }

        Long opUid = comm.getId();
        if (opUid == null || opUid <= 0) {
            throw new DcArgumentException("当前操作用户Id不存在");
        }

        // 操作人信息
        param.setUpdateOpId(opUid);
        param.setUpdateOpName(comm.getUsername());

        ObjectResponse<CorpPo> res = corpSettlementService.updateDiscount(param);
        FeignResponseValidate.check(res);
        corpSysLogService.updateBlocUserLog(res.getData().getCorpName(), request);
        return res;
    }

    @Operation(summary = "更新企业客户结算配置信息")
    @PostMapping(value = "/api/corp/updateSettlementCfg")
    public BaseResponse updateSettlementCfg(
        ServerHttpRequest request, @RequestBody UpdateCorpSettlementParam param) {
        log.info(
            LoggerHelper2.formatEnterLog(request, false) + " param = " + JsonUtils.toJsonString(
                param));
        CorpPo corpPo = corpSettlementService.updateSettlementCfg(param);
        corpSysLogService.updateBlocUserLog(corpPo.getCorpName(), request);
        return corpPo != null ? RestUtils.success() : RestUtils.serverBusy();
    }

    @Operation(summary = "查询帐单列表")
    @PostMapping(value = "/api/corp/findSettlementList")
    public ListResponse<SettlementVo> findSettlementList(
        ServerHttpRequest request, @RequestBody ListSettlementParam param) {
        log.info(
            LoggerHelper2.formatEnterLog(request, false) + " param = " + JsonUtils.toJsonString(
                param));

        // 获取登录用户相关信息
        if (BooleanUtils.isNotTrue(param.getIgnoreCommIdChain())) {
            String idChain = AntRestUtils.getCommIdChain(request);
            param.setCommIdChain(idChain);
        }

        return corpSettlementService.findSettlementList(param);
    }

    @Operation(summary = "账单结算")
    @GetMapping(value = "/api/corp/settlementByBillNo")
    public ObjectResponse<Integer> settlementByBillNo(
        ServerHttpRequest request,
        @Parameter(name = "账单编号", required = true) @RequestParam(value = "billNo") String billNo) {
        log.info(LoggerHelper2.formatEnterLog(request));
        var res = corpSettlementService.settlementByBillNo(billNo);
        corpSysLogService.settlementEditLog(billNo, request);
        return res;
    }

    @Operation(summary = "账单追加订单到账单")
    @PostMapping(value = "/api/corp/appendOrder2Settlement")
    public ObjectResponse<SettlementVo> appendOrder2Settlement(
        ServerHttpRequest request, @RequestBody SettlementDto dto) {
        log.info(
            LoggerHelper2.formatEnterLog(request, false) + " dto = " + JsonUtils.toJsonString(dto));

        Long opUid = AntRestUtils.getSysUid(request);
        if (opUid <= 0) {
            throw new DcArgumentException("当前操作用户Id不存在");
        }
        String uname = AntRestUtils.getSysUserName(request);

        // 操作人信息
        dto.setOpId(opUid);
        dto.setOpName(uname);
        dto.setOpType(UserType.SYS_USER);

        ObjectResponse<SettlementVo> res = corpSettlementService.appendOrder2Settlement(dto);
        // 需要区分追加和编辑操作
        if (SettlementDto.OpPage.ADD == dto.getOpPage()) {
            corpSysLogService.settlementAddLog(res.getData().getBillNo(), request);
        } else if (SettlementDto.OpPage.EDIT == dto.getOpPage()) {
            corpSysLogService.settlementEditLog(res.getData().getBillNo(), request);
        }
        return res;
    }

    @Operation(summary = "将账单中的订单移除")
    @PostMapping(value = "/api/corp/removeOrder4Settlement")
    public ObjectResponse<SettlementVo> removeOrder4Settlement(
        ServerHttpRequest request, @RequestBody SettlementDto dto) {
        log.info(
            LoggerHelper2.formatEnterLog(request, false) + " dto = " + JsonUtils.toJsonString(dto));

        // 获取登录用户相关信息
        CommercialSample comm = this.getCommercialSample2(request);
        if (ObjectUtils.isEmpty(comm)) {
            throw new DcArgumentException("当前操作用户信息不存在，请重新登录");
        }
        Long sysUserCommId = comm.getComId();
        if (sysUserCommId == null || sysUserCommId <= 0) {
            throw new DcArgumentException("当前操作用户商户信息不明确");
        }

        Long opUid = comm.getId();
        if (opUid == null || opUid <= 0) {
            throw new DcArgumentException("当前操作用户Id不存在");
        }

        // 操作人信息
        dto.setOpId(opUid);
        dto.setOpName(comm.getUsername());
        dto.setOpType(UserType.SYS_USER);

        var res = corpSettlementService.removeOrder4Settlement(dto);
        corpSysLogService.settlementEditLog(dto.getBillNo(), request);
        return res;
    }

    @Operation(summary = "更新账单")
    @PostMapping(value = "/api/corp/updateSettlement")
    public ObjectResponse<Integer> updateSettlement(
        ServerHttpRequest request, @RequestBody SettlementDto dto) {
        log.info(
            LoggerHelper2.formatEnterLog(request, false) + " dto = " + JsonUtils.toJsonString(dto));

        // 不需要变更操作人信息
        var res = corpSettlementService.updateSettlement(dto);
        corpSysLogService.settlementEditLog(dto.getBillNo(), request);
        return res;
    }

    @Operation(summary = "查看账单")
    @GetMapping(value = "/api/corp/getSettlementByBillNo")
    public ObjectResponse<SettlementVo> getSettlementByBillNo(
        ServerHttpRequest request,
        @Parameter(name = "账单编号", required = true) @RequestParam(value = "billNo") String billNo) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return corpSettlementService.getSettlementByBillNo(billNo);
    }

    @Operation(summary = "删除账单")
    @GetMapping(value = "/api/corp/removeSettlementByBillNo")
    public ObjectResponse<Integer> removeSettlementByBillNo(
        ServerHttpRequest request,
        @Parameter(name = "账单编号", required = true) @RequestParam(value = "billNo") String billNo) {
        log.info(LoggerHelper2.formatEnterLog(request));
        var res = corpSettlementService.removeSettlementByBillNo(billNo);
        corpSysLogService.settlementRemoveLog(billNo, request);
        return res;
    }

    @Operation(summary = "导出账单")
    @GetMapping(value = "/api/corp/exportSettlementByBillNo")
    public Mono<ObjectResponse<ExcelPosition>> exportSettlementByBillNo(
        ServerHttpRequest request,
        @Parameter(name = "账单编号", required = true) @RequestParam(value = "billNo") String billNo,
        @Parameter(name = "导出文件名称") @RequestParam(value = "exFileName") String exFileName) {
        log.info(LoggerHelper2.formatEnterLog(request));

        Long sysUid = AntRestUtils.getSysUid(request);
        ObjectNode reqParam = new ObjectMapper().createObjectNode();
        reqParam.put("billNo", billNo);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName(exFileName)
            .setFunctionMap(DownloadFunctionType.SETTLEMENT_ORDER)
            .setReqParam(reqParam.toString());
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return corpSettlementService.exportSettlementByBillNo(billNo);
    }

    /**
     * 用于企客对账OA导出至附件
     *
     * @param params
     * @return
     */
    @PostMapping(value = "/api/corp/exportBillExcel")
    public ObjectResponse<BillExportParam> exportBillExcel(@RequestBody BillExportParam params) {
        log.info("临时账单导出,params={}", JsonUtils.toJsonString(params));
        return bizBiFeignClient.exportBillExcel(params);
    }
}
