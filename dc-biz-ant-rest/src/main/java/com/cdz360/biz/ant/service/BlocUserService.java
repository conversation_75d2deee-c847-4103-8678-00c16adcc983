package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.model.corp.type.CorpType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.domain.BlocUser;
import com.cdz360.biz.ant.domain.vo.AccountRemainInfo;
import com.cdz360.biz.ant.domain.vo.BlocUserVo;
import com.cdz360.biz.ant.domain.vo.BlocWalletVO;
import com.cdz360.biz.ant.domain.vo.CorpLoginVo;
import com.cdz360.biz.ant.feign.AntUserFeignClient;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.feign.BizBiFeignClient;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.MerchantFeignClient;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.auth.corp.param.BalanceRemindParam;
import com.cdz360.biz.model.cus.corp.param.BatchModifyVinParam;
import com.cdz360.biz.model.cus.corp.param.ListCorpParam;
import com.cdz360.biz.model.cus.corp.po.CorpOrgLoginVo;
import com.cdz360.biz.model.cus.corp.po.CorpOrgPo;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.corp.vo.CorpOrgVO;
import com.cdz360.biz.model.cus.corp.vo.CorpSimpleVo;
import com.cdz360.biz.model.cus.corp.vo.CorpVo;
import com.cdz360.biz.model.cus.discount.dto.SelectDiscount;
import com.cdz360.biz.model.cus.discount.dto.SiteDiscount;
import com.cdz360.biz.model.cus.discount.type.DiscountStatus;
import com.cdz360.biz.model.cus.site.po.SiteAuthPo;
import com.cdz360.biz.model.cus.soc.param.QueryStrategyParam;
import com.cdz360.biz.model.cus.soc.param.SocStrategyDict;
import com.cdz360.biz.model.cus.soc.vo.SocCorpVo;
import com.cdz360.biz.model.cus.soc.vo.UserSocStrategyCreditCusVo;
import com.cdz360.biz.model.cus.soc.vo.UserSocStrategyVinVo;
import com.cdz360.biz.model.order.type.OrderPayType;
import com.cdz360.biz.model.trading.order.param.ListPayBillParam;
import com.cdz360.biz.model.trading.order.vo.PayBillRefundVo;
import com.cdz360.biz.utils.feign.user.UserFeignClient;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.constant.OrderStatus;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.BlocUserDto;
import com.chargerlinkcar.framework.common.domain.PayAccount;
import com.chargerlinkcar.framework.common.domain.PointPo;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.domain.vo.AccountInfoVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import com.chargerlinkcar.framework.common.feign.CommercialFeignClient;
import com.chargerlinkcar.framework.common.feign.CorpFeignClient;
import com.chargerlinkcar.framework.common.service.DcCusBalanceService;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.google.common.hash.Hashing;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * 集团用户功能
 * <p>
 * BlocUserServiceImpl
 *
 * <AUTHOR> 集团用户功能
 * @since 2018.11.21
 */
@Slf4j
@Service
public class BlocUserService //implements IBlocUserService
{

    public final static String CODE = "0";
    /**
     * 商户
     */
    @Autowired
    private MerchantFeignClient merchantFeignClient;
    @Autowired
    private BizBiFeignClient bizBiFeignClient;
    @Autowired
    private LoginService loginService;
    @Autowired
    private CorpService corpService;
    //    @Autowired
//    private MerchantService merchantService;
    @Autowired
    private AntUserFeignClient userFeignClient;

    @Autowired
    private UserFeignClient monoUserFeignClient;

    @Autowired
    private CorpFeignClient corpFeignClient;
    @Autowired
    private DcCusBalanceService dcCusBalanceService;

    @Autowired
    private CommercialFeignClient commercialFeignClient;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private PayBillService payBillService;

    @Autowired
    private BalanceService balanceService;

//    public static void main(String[] args) {
//        String pass = "aaaa";
//        String salt = "bbbbb";
//        byte[] bytes;
//        String result = new Sha256Hash(pass, salt).toHex();
//        System.out.println(result);
//        result = new Sha256Hash(pass, salt).toBase64();
//        System.out.println(result);
//        bytes = Hashing.sha256().hashString(salt + pass, Charset.defaultCharset()).asBytes();
//        System.out.println(Hex.encodeHex(bytes));
//        result = Base64.getEncoder().encodeToString(bytes);
//        System.out.println(result);
//
//        bytes = new BlocUserService().sha256(pass, salt);
//        System.out.println(Hex.encodeHex(bytes));
//        result = Base64.getEncoder().encodeToString(bytes);
//        System.out.println(result);
//    }

    /**
     * 获取当前用户下的集团列表
     *
     * @param page
     * @param keyWord (条件查询)
     * @return
     */

    public ListResponse<BlocUserVo> queryBlocUser(
        ServerHttpRequest request,
        OldPageParam page, String keyWord, Long commId, String corpName, CorpType corpType,
        String commIdChain, Boolean enable, Long settlementType, String invoiceWay,
        String referrer,
        List<Long> corpIdList) {

        ListCorpParam param = new ListCorpParam();
        //根据token获取商户ID及子商户ID集合
        //List<Long> commIdList = merchantService.getCommIdListByToken(token);
        if (commId != null && commId > 0L) {
            //存在商户ID的时候查询当前商户下的所有企业
            param.setCommId(commId);
        } else {
            param.setCommIdChain(commIdChain);
        }
//        StringBuffer sb = new StringBuffer();
//        for (int i = 0; i < commIdList.size(); i++) {
//            sb.append(commIdList.get(i).toString()).append(",");
//        }
        //根据条件查询数据库
//        String commIdLists = sb.substring(0,sb.length()-1).toString();
        param.setStart((long) ((page.getPageNum() - 1)) * page.getPageSize());
        param.setSize(page.getPageSize());
        param.setSk(keyWord);
        param.setCorpName(corpName);
        param.setCorpType(corpType);
        param.setSettlementType(settlementType);
        param.setInvoiceWay(invoiceWay);
        param.setEnable(enable);
        param.setReferrer(referrer);
        //param.setCommId(commId);
        //param.setCommIdList(commIdList);
        param.setIdList(corpIdList);
        ListResponse<CorpVo> corpRes = this.authCenterFeignClient.getCorpList(
            AntRestUtils.getToken2(request), param);
        FeignResponseValidate.check(corpRes);

        List<BlocUserVo> list = corpRes.getData().stream().map(c -> {
            BlocUserVo bu = new BlocUserVo();
            bu.setAccount(c.getAccount());
            bu.setUid(c.getUid());
            bu.setId(c.getId());
            bu.setBlocUserName(c.getCorpName());
            bu.setPhone(c.getPhone());
            bu.setType(c.getType());
            bu.setContactName(c.getContactName());
            bu.setAddress(c.getAddress());
            bu.setEnable(c.getEnable());
            bu.setCommId(c.getCommId()).setCommName(c.getCommName());
            if (c.getAmount() != null) {
                bu.setBlocBalance(String.valueOf(c.getAmount()));
            } else {
                bu.setBlocBalance("");
            }
            bu.setAvailableAmount(c.getAvailableAmount());
            bu.setFrozenAmount(c.getFrozenAmount());
            bu.setSettlementType(c.getSettlementType());
            bu.setInvoiceWay(c.getInvoiceWay());
            bu.setReferrer(c.getCreatorName());

            // 获取企业客户对应的协议价场站
            // TODO: 这样处理不好
            if (c.getSettlementType() != null &&
                SettlementType.BALANCE.equals(c.getSettlementType())) {
                SelectDiscount selectDiscount = new SelectDiscount();
                selectDiscount.setUid(c.getUid())
                    .setAccountType(PayAccountType.CORP)
                    .setAccountCode(c.getTopCommId())
                    .setStatusList(List.of(DiscountStatus.ENABLE));
                ListResponse<SiteDiscount> block = monoUserFeignClient.findSiteDiscount(selectDiscount)
                    .block(Duration.ofSeconds(50L));
                FeignResponseValidate.checkIgnoreData(block);
                bu.setUseDiscount(
                    block != null && block.getData() != null && block.getData().size() > 0);
            }
            return bu;
        }).collect(Collectors.toList());

        return RestUtils.buildListResponse(list, corpRes.getTotal());

    }

    /**
     * 根据集团Id查询集团基础信息(新增余额字段)
     *
     * @param blocUserId
     * @return
     */

    public BlocWalletVO getBlocUserByBlocUserId(ServerHttpRequest request, Long blocUserId) {

        ObjectResponse<CorpPo> corpRes = authCenterFeignClient.getCorp(blocUserId);
        log.info("corpRes = {}", corpRes);
        FeignResponseValidate.check(corpRes);
        CorpPo corp = corpRes.getData();
        BlocWalletVO result = new BlocWalletVO();
        BeanUtils.copyProperties(corp, result);
        corp.setUid(corp.getId());
        PointPo corpAcc = this.dcCusBalanceService.getPoint(PayAccountType.PERSONAL,
            corp.getTopCommId(), corp.getTopCommId(), corp.getUid());
        if (corpAcc != null) {
            result.setBalance(corpAcc.getPoint());
        }
        return result;

    }

    public ListResponse<CorpSimpleVo> getCorpByCommId(String token,
        String commIdChain,
        Long corpId) {
        return authCenterFeignClient.getCorpByCommId(token, commIdChain, corpId);
    }

    /**
     * 校验集团重复 暂对账户account/集团名称blocUserName做重复校验
     *
     * @param account      集团账户
     * @param blocUserName 集团名称
     * @return
     */

    public ObjectResponse<Integer> queryBlocUserByCondition(String account, String blocUserName) {
        ObjectResponse<Integer> jsonResult = merchantFeignClient.queryBlocUserByCondition(account,
            blocUserName);
        return jsonResult;

    }

    /**
     * 新增集团
     *
     * @param blocUser
     * @return
     */

    public BaseResponse insertBlocUser(ServerHttpRequest request, BlocUser blocUser,
        Long topCommId) {
        // 根据token获取商户ID及账户ID
        //ObjectResponse<MerchantCommVo> jsonObjectmerchantCommVo = merchantFeignClient.getCurrentMerchant(token);

//        if (jsonObjectmerchantCommVo == null || ResultConstant.RES_SUCCESS_CODE != jsonObjectmerchantCommVo.getStatus()) {
//            throw new DcServiceException("获取当前商户信息失败");
//        }
//
//        MerchantCommVo merchantCommVo = jsonObjectmerchantCommVo.getData();
//
//        if (ObjectUtils.isEmpty(merchantCommVo) || merchantCommVo.getMerchantId() == null || merchantCommVo.getMerchantId() <= 0) {
//            throw new DcServiceException("获取当前商户信息失败");
//        }
        //blocUser.setCommId(merchantCommVo.getCommId());
        // BaseResponse result = merchantFeignClient.insertBlocUser(blocUser);
        CorpPo corp = new CorpPo();
        corp.setTopCommId(topCommId)//TODO: 要填集团商户ID
            .setCommId(blocUser.getCommId())
            .setCorpName(blocUser.getBlocUserName())
            .setContactName(blocUser.getContactName())
            .setPhone(blocUser.getPhone())
            .setEmail(blocUser.getEmail())
            .setProvince(blocUser.getProvince())
            .setCity(blocUser.getCity()).setDistrict(blocUser.getDistrict())
            .setAddress(blocUser.getAddress()).setOrganizationImage(blocUser.getOrganizationImage())
            .setBusinessImage(blocUser.getBusinessImage()).setAccount(blocUser.getAccount())
            .setPassword(Base64.getEncoder()
                .encodeToString(Hashing.sha256()
                    .hashString(blocUser.getPhone() + blocUser.getPassword(),
                        Charset.defaultCharset()).asBytes()))
//            .setPassword(new Sha256Hash(, ).toBase64())
            .setPassword(blocUser.getPassword())
            .setDigest(blocUser.getDigest())
            .setCreatorId(AntRestUtils.getSysUid(request))
            .setCreatorName(AntRestUtils.getSysUserName(request))
            .setType(CorpType.PLATFORM);
        return authCenterFeignClient.addCorp(corp);
    }

    /**
     * 更新集团信息
     *
     * @param blocUser
     * @return
     */

    public BaseResponse updateBlocUser(ServerHttpRequest request, BlocUser blocUser) {
        log.info("更新集团信息----ant服务前：{}", JsonUtils.toJsonString(blocUser));
        if (StringUtils.isNotBlank(blocUser.getPassword())) {
            ListResponse<CorpOrgVO> corpOrgVOList = corpFeignClient.getOrgByLevel(blocUser.getId(),
                1);

            CorpOrgPo corpOrgPo = new CorpOrgPo();
            corpOrgPo.setAccount(blocUser.getAccount())
                .setPassword(Base64.getEncoder()
                    .encodeToString(Hashing.sha256()
                        .hashString(blocUser.getBlocUserName() + blocUser.getPassword(),
                            Charset.defaultCharset()).asBytes()))
//                .setPassword(
//                    new Sha256Hash(blocUser.getPassword(), blocUser.getBlocUserName()).toBase64())
                .setOrgLevel(1).setCorpId(blocUser.getId()).setOrgName(blocUser.getBlocUserName());
            if (CollectionUtils.isNotEmpty(corpOrgVOList.getData())) {
                //只有一个1级组织
                corpOrgPo.setId(corpOrgVOList.getData().get(0).getId());
            }
            corpFeignClient.addOrUpdateCorpOrg(corpOrgPo);
        }
        //清掉密码
//        blocUser.setPassword("");
        BaseResponse result = authCenterFeignClient.updateBlocUser(blocUser);

        log.info("更新集团信息----ant服务后：{}", result);
        return result;
//        if (result != null && StringUtils.equals(CODE, result.get("status").toString())) {
        //            return new ObjectResponse<>(result.get("data"));
//        } else {
        //            return new ObjectResponse<>(result.get("error").toString());
//        }
    }

//    public byte[] sha256(String pass, String salt) {
//        try {
//            MessageDigest digest = MessageDigest.getInstance("SHA-256");
//            digest.reset();
//            digest.update(salt.getBytes(Charset.defaultCharset()));
//            return digest.digest(pass.getBytes(Charset.defaultCharset()));
////            return digest.digest();
//        } catch (NoSuchAlgorithmException e) {
//            log.error("计算sha256失败. error = {}", e.getMessage(), e);
//        }
//        return null;
//    }

    /**
     * 删除集团
     *
     * @param blocUserId
     * @return
     */

    public BlocUserDto deleteBlocUserById(Long blocUserId) {
        ObjectResponse<BlocUserDto> jsonObjectRes = merchantFeignClient.deleteBlocUserById(
            blocUserId);
        FeignResponseValidate.check(jsonObjectRes);
        return jsonObjectRes.getData();
//        if (jsonObjectRes == null || jsonObjectRes.getInteger("status") == null) {
//            throw new DcServiceException("请求失败");
//        }
//
//        if (jsonObjectRes.getInteger("status") != ResultConstant.RES_SUCCESS_CODE) {
//
//            if (jsonObjectRes.getString("error") == null) {
//                throw new DcServiceException("请求失败");
//            }else{
//                throw new DcServiceException(jsonObjectRes.getString("error"));
//            }
//        }
//
        //        return new ObjectResponse<>(jsonObjectRes.get("data"));
    }


    public void batchDisableCreditAccount(List<Long> accountIds,
        Long topCommId,
        Long commId,
        Long corpId,
        Long opUid) {
        List<PayAccount> payAccList = accountIds.stream().map(id -> {
            PayAccount acc = new PayAccount();
            acc.setDefaultPayType(OrderPayType.BLOC)
                .setPayAccountId(id);
            return acc;
        }).collect(Collectors.toList());
        ChargerOrderParam listOrderParam = new ChargerOrderParam();
        listOrderParam.setDefaultPayType(PayAccountType.CREDIT.getCode())
            .setPayAccountList(payAccList)
            .setPayStatus(1)    // 1 = 未支付
            .setStatusList(List.of(OrderStatus.ORDER_STATUS_ERROR_CP,
                OrderStatus.ORDER_STATUS_UNACTIVATED,
                OrderStatus.ORDER_STATUS_SWITCH_ON,
                OrderStatus.ORDER_STATUS_CHARGING,
                OrderStatus.ORDER_STATUS_COMPLETE))
            .setSiteCommIdChain(String.valueOf(topCommId))
            .setStart(0L).setSize(5);
        ListResponse<ChargerOrderVo> orderList = bizBiFeignClient.queryChargeOrderList(
            listOrderParam);
        FeignResponseValidate.check(orderList);
        if (orderList.getData().size() > 0L) {
            log.warn("删除失败：存在账户下有未结算的充电订单。 orderList.size = {}",
                orderList.getData().size());
            throw new DcServiceException("删除失败：存在账户下有未结算的充电订单。");
        }
        BaseResponse res = this.corpFeignClient.batchDisableCreditAccount(accountIds, topCommId,
            commId, corpId, opUid);
        FeignResponseValidate.check(res);
    }

//    /**
//     * 登录集团
//     *
//     * @param blocUserLoginVo
//     * @return
//     */

//    public ObjectResponse<BlocUserVo> loginBlocUser(BlocUserLoginVo blocUserLoginVo) {
//        ObjectResponse<BlocUserVo> jsonResult = merchantFeignClient.loginBlocUser(blocUserLoginVo);
//        return jsonResult;
//
//    }


    public ObjectResponse<CorpLoginVo> loginBlocUserForCorpPlatform(String username,
        String passWord) {
        ObjectResponse<CorpOrgLoginVo> jsonResult = userFeignClient.getOrgByAccount(username);
        CorpOrgLoginVo corpOrgLoginVo = jsonResult.getData();
        if (corpOrgLoginVo == null) {
            ObjectResponse<CorpLoginVo> result = new ObjectResponse<>();
            result.setError("用户名或者密码错误").setStatus(4011);
            return result;
        }
        CorpPo corpPo = corpOrgLoginVo.getCorpPo();
//        if (corpOrgLoginVo != null) { // 上面已经判空
        String input = Base64.getEncoder()
            .encodeToString(Hashing.sha256()
                .hashString(corpOrgLoginVo.getOrgName() + passWord, Charset.defaultCharset())
                .asBytes());
//        String input = new Sha256Hash(passWord, corpOrgLoginVo.getOrgName()).toBase64();
        log.info("企业用户登录username:{},passWord:{},corpOrgLoginVo:{}", username, input,
            corpOrgLoginVo);
        if (input.equals(corpOrgLoginVo.getPassword())) {
            //List<Long> comIds = merchantFeignClient.getCommIdListByCommId(corpPo.getCommId()).getData();
            //corpOrgLoginVo.setComIds(comIds);
            String token = loginService.getTokenAndSave(corpOrgLoginVo);
            CorpLoginVo corpLoginVo = new CorpLoginVo();
            corpLoginVo.setBlocUserName(corpPo.getCorpName())
                .setCommId(corpPo.getCommId()).setId(corpPo.getId())
                .setToken(token).setPhone(corpPo.getPhone())
                .setTopCommId(corpPo.getTopCommId()).setOrgName(corpOrgLoginVo.getOrgName())
                .setOrgLevel(corpOrgLoginVo.getOrgLevel()).setOrgId(corpOrgLoginVo.getId());
            return new ObjectResponse<>(corpLoginVo);
        }
//        }
        ObjectResponse<CorpLoginVo> result = new ObjectResponse<>();
        result.setError("用户名或者密码错误").setStatus(4011);
        return result;
    }


    public void logOut(CorpOrgLoginVo corpOrgLoginVo) {
        loginService.logout(corpOrgLoginVo);
    }

    public ListResponse<BlocUserDto> selectSubBlocUserByCommIdChain(String commIdChain,
        Boolean includedHlhtCorp,
        Long commId) {
        if (null != commId) {
            ObjectResponse<Commercial> commercial = commercialFeignClient.getCommercial(commId);
            FeignResponseValidate.check(commercial);
            commIdChain = commercial.getData().getIdChain();
        }
        log.debug("commIdChain = {}", commIdChain);
        return userFeignClient.selectSubBlocUserByCommIdChain(commIdChain, includedHlhtCorp);
    }

    public ListResponse<BlocUserDto> selectSubBlocUserByTokenOnOperate() {
        return userFeignClient.selectSubBlocUserByTokenOnOperate();
    }


    public BlocUserDto enableBlocUserById(Long blocUserId) {
        ObjectResponse<BlocUserDto> jsonObjectRes = merchantFeignClient.enableBlocUserById(
            blocUserId);
        FeignResponseValidate.check(jsonObjectRes);
        return jsonObjectRes.getData();
    }


    public ObjectResponse<AccountInfoVo> getBlocUserAccount(long blocUserId) {

        log.info("根据集团id获取集团账户信息blocUserId:{}", blocUserId);

        ObjectResponse<BlocUserDto> dtoObjectResponse = userFeignClient.findById(blocUserId);
        try {
            FeignResponseValidate.check(dtoObjectResponse);
        } catch (DcException e) {
            throw new DcServiceException("该集团客户不存在");
        }
        BlocUserDto blocUserDto = dtoObjectResponse.getData();

        PointPo corpAcc = dcCusBalanceService.getPoint(PayAccountType.PERSONAL,
            blocUserDto.getTopCommId(),
            blocUserDto.getTopCommId(), blocUserDto.getUid());

        if (corpAcc == null) {
            corpAcc = new PointPo() {{
                setId(0L);
                setAvailable(DecimalUtils.ZERO);
                setPoint(DecimalUtils.ZERO);
                setFrozen(DecimalUtils.ZERO);
                setCost(DecimalUtils.ZERO);
                setFrozenCost(DecimalUtils.ZERO);
            }};
        }

        AccountInfoVo accountInfoVo = new AccountInfoVo();
        accountInfoVo.setPayAccountId(blocUserDto.getTopCommId());
        accountInfoVo.setDefaultPayType(
            String.valueOf(OrderPayType.PERSON.getCode()));//集团的账户，归类于个人账户
        accountInfoVo.setAccountName(blocUserDto.getBlocUserName());

        accountInfoVo.setAvailableAmount(
            null == corpAcc.getAvailable() ? BigDecimal.ZERO : corpAcc.getAvailable());//可用余额
        accountInfoVo.setAmount(
            null == corpAcc.getPoint() ? BigDecimal.ZERO : corpAcc.getPoint());//总余额
        accountInfoVo.setFrozenAmount(
            null == corpAcc.getFrozen() ? BigDecimal.ZERO : corpAcc.getFrozen());//冻结金额
        accountInfoVo.setCostAmount(null == corpAcc.getCost() ? BigDecimal.ZERO
            : corpAcc.getCost().subtract(corpAcc.getFrozenCost()));//实际成本(实际余额)
        accountInfoVo.setFreeAmount(accountInfoVo.getAvailableAmount()
            .subtract(accountInfoVo.getCostAmount()));//赠送余额 = 可用余额 - 实际成本（实际余额）
        accountInfoVo.setFrozenCostAmount(
            null == corpAcc.getFrozenCost() ? BigDecimal.ZERO : corpAcc.getFrozenCost());//冻结实际金额
        accountInfoVo.setFrozenFreeAmount(accountInfoVo.getFrozenAmount()
            .subtract(accountInfoVo.getFrozenCostAmount()));//冻结赠送金额 = 冻结金额 - 冻结实际金额

        accountInfoVo.setUserId(blocUserDto.getUid());

        log.info("根据集团id获取集团账户信息结果:{}", accountInfoVo);
        return new ObjectResponse<>(accountInfoVo);

    }


    public void batchModifyVin(BatchModifyVinParam param) {
        List<String> noSettlementCardList = null;
        List<String> needCheckList = new ArrayList<>();
        //  新增场站不做校验，减少场站做校验
        ListResponse<SiteAuthPo> listResponse = corpFeignClient.getAuthSiteList(param);
        if (listResponse != null && CollectionUtils.isNotEmpty(listResponse.getData())) {
            Map<String, List<SiteAuthPo>> map = listResponse.getData().stream()
                .collect(Collectors.groupingBy(SiteAuthPo::getAccount));
            param.getAccountList().forEach(e -> {
                if (map.containsKey(e)) {
                    List<String> siteIdList = map.get(e).stream().map(SiteAuthPo::getSiteId)
                        .collect(Collectors.toList());
                    if (!param.getSiteIdList().containsAll(siteIdList)) {
                        needCheckList.add(e);
                    }
                }
            });
        }
        if (NumberUtils.equals(param.getType(), 1) && needCheckList.size() > 0) {
            noSettlementCardList = corpService.getNoSettlementCard(needCheckList, null);
        } else if (NumberUtils.equals(param.getType(), 2) && needCheckList.size() > 0) {
            noSettlementCardList = corpService.getNoSettlementVin(needCheckList,
                param.getTopCommId());
        }
        if (CollectionUtils.isNotEmpty(noSettlementCardList)) {
            throw new DcServiceException("存在订单尚未结算，请处理后再进行操作");
        }
        this.corpFeignClient.batchModifyVin(param);
    }

    public ListResponse<SocCorpVo> queryBlocUserForSiteSoc(String siteId) {
        return dataCoreFeignClient.queryBlocUserForSiteSoc(siteId);
    }

    public ListResponse<SocStrategyDict> queryStrategy(QueryStrategyParam param) {
//        return dataCoreFeignClient.queryStrategy(param);
        return userFeignClient.queryStrategy(param);
    }

    public BaseResponse setRenewReminderAmount(BalanceRemindParam param) {
        return authCenterFeignClient.setRenewReminderAmount(param);
    }

    public ListResponse<UserSocStrategyCreditCusVo> queryCorpStrategyCreditCus(
        QueryStrategyParam param) {
//        dataCoreFeignClient.queryCorpStrategyCreditCus(param);
        return userFeignClient.queryCorpStrategyCreditCus(param);
    }

    public ListResponse<UserSocStrategyVinVo> queryCorpStrategyVin(QueryStrategyParam param) {
//        dataCoreFeignClient.queryCorpStrategyVin(param);
        return userFeignClient.queryCorpStrategyVin(param);
    }

    public ObjectResponse<Integer> addCorpStrategyCreditCus(List<QueryStrategyParam> params) {
//        return dataCoreFeignClient.addCorpStrategyCreditCus(params);
        return userFeignClient.addCorpStrategyCreditCus(params);
    }

    public ObjectResponse<Integer> removeCorpStrategyCreditCus(List<QueryStrategyParam> params) {
//        return dataCoreFeignClient.removeCorpStrategyCreditCus(params);
        return userFeignClient.removeCorpStrategyCreditCus(params);
    }

    public ObjectResponse<Integer> addCorpStrategyVin(List<QueryStrategyParam> params) {
//        return dataCoreFeignClient.addCorpStrategyVin(params);
        return userFeignClient.addCorpStrategyVin(params);
    }

    public ObjectResponse<Integer> removeCorpStrategyVin(List<QueryStrategyParam> params) {
        // 同删除企业soc策略-授信账户
//        return dataCoreFeignClient.removeCorpStrategyCreditCus(params);
        return userFeignClient.removeCorpStrategyCreditCus(params);
    }

    public BaseResponse updateCorpStrategy(SocStrategyDict param) {
        return userFeignClient.updateCorpStrategy(param);
    }

    public BaseResponse deleteCorpStrategy(Long id) {
        return userFeignClient.deleteCorpStrategy(id);
    }

    public BaseResponse createCorpSocStrategy(SocStrategyDict param) {
        return userFeignClient.createCorpSocStrategy(param);
    }

    public Mono<AccountRemainInfo> getCorpAccountRemainInfo(CorpPo corpPo) {
        return balanceService.getCorpAccountInfo(corpPo);
    }

    public Mono<Integer> corpAccountRefund(CorpPo corpPo, AccountRemainInfo remainInfo) {
        return balanceService.corpAccountRefund(corpPo, remainInfo);
    }

    public Mono<ListResponse<PayBillRefundVo>> getCorpPayBillList(ListPayBillParam param) {
        return payBillService.getCorpPayBillList(param);
    }
}
