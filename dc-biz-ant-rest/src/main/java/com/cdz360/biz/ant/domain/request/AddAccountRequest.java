package com.cdz360.biz.ant.domain.request;

import com.cdz360.base.model.charge.type.SettlementType;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class AddAccountRequest {

    private Long partnerId;

    private String partnerCode;

    private String corpName;

    private SettlementType settlementType;

    @Schema(required = false)
    private Long topCommId; //由系统逻辑赋值

    private Long commId;

    private List<String> siteIdList;

    @Schema(required = false)
    private Long corpId; //由系统逻辑赋值

    @Schema(required = false)
    private String phone; //由系统逻辑赋值

    public static void check(AddAccountRequest request) {
        IotAssert.isNotNull(request.getPartnerId(), "partnerId不能为空");
        IotAssert.isNotNull(request.getPartnerCode(), "partnerCode不能为空");
        IotAssert.isNotNull(request.getCorpName(), "corpName不能为空");
    }

}
