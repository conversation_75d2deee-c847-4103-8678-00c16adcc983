package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.domain.BlocUser;
import com.cdz360.biz.ant.domain.vo.SysRole;
import com.cdz360.biz.auth.corp.param.BalanceRemindParam;
import com.cdz360.biz.auth.subscribe.param.AddSubscribeParam;
import com.cdz360.biz.auth.subscribe.param.CreatePayOrderParam;
import com.cdz360.biz.auth.subscribe.param.SubLogListParam;
import com.cdz360.biz.auth.subscribe.param.SubscribeListParam;
import com.cdz360.biz.auth.subscribe.vo.CommVo;
import com.cdz360.biz.auth.subscribe.vo.SubscribeDetailVo;
import com.cdz360.biz.auth.subscribe.vo.SubscribeLogVo;
import com.cdz360.biz.auth.subscribe.vo.SubscribeOrderDetailVo;
import com.cdz360.biz.auth.subscribe.vo.SubscribeVo;
import com.cdz360.biz.auth.sys.param.AccRelativeParam;
import com.cdz360.biz.auth.sys.param.AddAccRelativeParam;
import com.cdz360.biz.auth.sys.param.BatchAddRoleUserParam;
import com.cdz360.biz.auth.sys.param.RoleUserListParam;
import com.cdz360.biz.auth.sys.param.RoleUserUpdateParam;
import com.cdz360.biz.auth.sys.param.YwUserParam;
import com.cdz360.biz.auth.sys.vo.AccRelativeVo;
import com.cdz360.biz.auth.sys.vo.RoleUserVo;
import com.cdz360.biz.auth.sys.vo.SysRoleSimpleVo;
import com.cdz360.biz.auth.sys.vo.SysRoleVo;
import com.cdz360.biz.auth.user.dto.SysUserLoginResult;
import com.cdz360.biz.auth.user.param.SysUserLogParam;
import com.cdz360.biz.auth.user.param.SysUserLoginParam;
import com.cdz360.biz.auth.user.po.SysUserPo;
import com.cdz360.biz.auth.user.vo.SysUserLogVo;
import com.cdz360.biz.auth.zft.dto.ZftDto;
import com.cdz360.biz.auth.zft.param.ListZftParam;
import com.cdz360.biz.auth.zft.vo.ZftVo;
import com.cdz360.biz.model.common.request.TokenRequest;
import com.cdz360.biz.model.cus.corp.param.ListCorpParam;
import com.cdz360.biz.model.cus.corp.po.CorpOrgPo;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.corp.vo.CorpOrgVO;
import com.cdz360.biz.model.cus.corp.vo.CorpSimpleVo;
import com.cdz360.biz.model.cus.corp.vo.CorpVo;
import com.cdz360.biz.model.cus.message.param.ListMessageParam;
import com.cdz360.biz.model.cus.message.po.MessagePo;
import com.cdz360.biz.model.cus.message.vo.MessageVo;
import com.cdz360.biz.model.cus.message.vo.UserMessageVo;
import com.cdz360.biz.model.cus.param.SysUserCheckParam;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.merchant.dto.CommercialDto;
import com.cdz360.biz.model.merchant.param.ListCommercialParam;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import com.cdz360.biz.model.sys.vo.SiteGroupVo;
import com.cdz360.biz.model.sys.vo.SubscribeOrderVo;
import com.chargerlinkcar.framework.common.domain.SysUser;
import com.chargerlinkcar.framework.common.domain.TCommercialUser;
import com.chargerlinkcar.framework.common.domain.request.AddAuthorityGroupRequest;
import com.chargerlinkcar.framework.common.domain.request.AddUserGroupRequest;
import com.chargerlinkcar.framework.common.domain.vo.Authority;
import com.chargerlinkcar.framework.common.domain.vo.AuthorityGroup;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * Created by Administrator on 2018/12/12.
 */
@Slf4j
@Component
public class AuthCenterFeignClientHystrixFactory implements FallbackFactory<AuthCenterFeignClient> {

    @Override
    public AuthCenterFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_AUTH,
            throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_AUTH,
            throwable.getStackTrace());

        return new AuthCenterFeignClient() {

            @Override
            public ListResponse<SysUserVo> querySysUserByIds(List<Long> idList) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public List<SysRoleVo> findByUserId(Long userId, String token) {
                return null;
            }

            @Override
            public BaseResponse add(String token, SysRole role) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse modify(String token, Long id, SysRole role) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<Authority> getAuthorityList(String token) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse AddGroup(String token,
                AddAuthorityGroupRequest addAuthorityGroupRequest) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<AuthorityGroup> getGroups(String token) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<AuthorityGroup> getGroupById(String token, Long id) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse deleteGroupById(String token, Long id) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<Authority> getUserAuthoritiesByUid(String token, Long uid) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse modifyUserGroupRef(String token,
                AddUserGroupRequest addUserGroupRequest) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<TCommercialUser> getCommercialsUserByToken(String token) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse addMessage(String token, MessagePo message) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse editMessage(String token, Long msgId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<UserMessageVo> getMessage(String token, Long msgId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse getUnReadCount(String token, Long platform) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<MessageVo> getMsgList(ListMessageParam reqParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<UserMessageVo> getUserMsgList(String token,
                ListMessageParam reqParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<SysUserLoginResult> login(@RequestBody SysUserLoginParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse logout(TokenRequest tokenRequest) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<SysUser> getUserByToken(TokenRequest tokenRequest) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<SysUserLoginResult> switchUser(String token, Long targetSysUid) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<String> getLoginTmpKey(String token, Long corpId,
                String idChain) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<SysUserLoginResult> getLoginInFoByKey(String token, String key) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse getOpenId(String token, String code) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse refreshCorpTokenValue(String token, String username) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse edit(String token, TCommercialUser tcu) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse add(String token, SysUser bodyEntity) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse modify(String token, SysUser bodyEntity) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse changeState(String token, SysUser bodyEntity) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<SysUserLogVo> getLoginLog(SysUserLogParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SysUserLogVo> getOpLog(SysUserLogParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<CorpOrgVO> getOrgList(String token, Long corpId, Integer _index,
                Integer _size) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<CorpOrgVO> getOrgByLevel(String token, Long corpId, Integer level) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<CorpOrgVO> getOrgByUserId(String token, Long corpId,
                Integer cusId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse addOrUpdateCorpOrg(String token, CorpOrgPo corpOrgPo) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<Long> addCorp(CorpPo corp) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<CorpPo> getCorp(Long corpId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<CorpPo> updateBlocUser(BlocUser blocUser) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<com.cdz360.biz.auth.corp.po.CorpPo> getCorpByUid(String token,
                Long corpUid) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<CorpVo> getCorpList(String token, ListCorpParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<CorpSimpleVo> getCorpByCommId(String token, String commIdChain,
                Long corpId) {
                log.error("【服务熔断】。Service = {}, getCorpByCommId commIdChain = {} corpId = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, commIdChain, corpId);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<String> getGidsById(Long corpId) {
                log.error("【服务熔断】。Service = {}, getGidsById corpId = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, corpId);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse setRenewReminderAmount(BalanceRemindParam param) {
                log.error(
                    "【服务熔断】。Service = {}, setRenewReminderAmount(根据企业id设置续费提醒金额), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, param);
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse resetEmailSendStatus(Long corpId, Long corpUid) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<ZftVo> zftList(String token, ListZftParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<ZftVo> getZft(String token, Long id) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Long> updateZft(String token, ZftDto dto) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse editHlhtSitePayType(Long commId, List<String> payTypeList) {
                log.error(
                    "【服务熔断】。Service = {}, 商户编辑互联站点支持的账户类型 commId = {} payTypeList = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, commId, payTypeList);
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<CommercialDto> getCommList(ListCommercialParam param) {
                log.error("【服务熔断】。Service = {}, 获取商户信息列表 param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, param);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<AccRelativeVo> getVoList(String token, AccRelativeParam param) {
                log.error("【服务熔断】。Service = {}, 获取运维人员列表 param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, param);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse syncOrderNum(String token) {
                log.error("【服务熔断】。Service = {}, 同步运维工单数 token = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, token);
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<AccRelativeVo> getYwAccount(String token, Long sysUid) {
                log.error("【服务熔断】。Service = {}, 获取运维人员信息 sysUid = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, sysUid);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<AccRelativeVo> findByAccount(String token, String account,
                String commIdChain) {
                log.error("【服务熔断】。Service = {}, 获取运维人员信息 account = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, account);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SysUserVo> addAccRelative(String token, AddAccRelativeParam param) {
                log.error("【服务熔断】。Service = {}, 新增运维人员 param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, param);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse checkForOutstandingOrder(String token, Long sysUid) {
                log.error("【服务熔断】。Service = {}, 检查运维账户下是否存在未完成的工单 sysUid = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, sysUid);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SysUserVo> editAccRelative(String token,
                AddAccRelativeParam param) {
                log.error("【服务熔断】。Service = {}, 修改运维人员 param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, param);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<SysUserVo> deleteBySysUid(String token, Long sysUid) {
                log.error("【服务熔断】。Service = {}, 删除运维人员 sysUid = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, sysUid);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SysUser> findYwUser(YwUserParam param) {
                log.error("【服务熔断】。Service = {}, 获取运维人员列表: param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, param);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SysUser> getYwGroupOtherUser(Long uid, Boolean same,
                List<String> gidList) {
                log.error(
                    "【服务熔断】。Service = {}, 获取用户所属运维组其他人员列表: uid = {}, same = {}, gidList = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, uid, same, gidList);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SysUserVo> getSysUserByIdList(List<Long> ids) {
                log.error("【服务熔断】。Service = {}, 获取用户列表 param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, ids);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SysUserPo> getByUserNameLike(String username) {
                log.error("【服务熔断】。Service = {}, 查询用户列表 param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, username);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<CommercialDto> getCommPlatInfo(Long commId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<CommercialSimpleVo> findSimpleVoById(Long commId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<SysUserPo> getByUserNameAndPlatform(String username,
                AppClientType platform) {
                log.error("【服务熔断】。Service = {}, 获取系统用户: username = {}, platform = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH, username, platform);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse batchUpdateRoleUser(RoleUserUpdateParam params) {
                log.error("【服务熔断】。Service = {}, 角色批量修改账号",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH);
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse batchAddRoleUser(BatchAddRoleUserParam params) {
                log.error("【服务熔断】。Service = {}, 角色批量新增账号",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH);
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<RoleUserVo> getUserByRoleId(String keyWord, Long platform,
                Long roleId, Long size) {
                log.error("【服务熔断】。Service = {}, 查询角色尚未绑定的账号信息",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<RoleUserVo> getUserListByRoleId(RoleUserListParam params) {
                log.error("【服务熔断】。Service = {}, 获取角色绑定的账号列表",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SysUserCheckParam> checkInDB(List<SysUserCheckParam> list) {
                log.error("【服务熔断】。Service = {}, sys_user相关检查",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SysRole> getRoleListByUserId(String token,
                RoleUserListParam params) {
                log.error("【服务熔断】。Service = {}, 获取账号绑定的角色列表",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SysRole> getRoleByUserId(String token, String keyWord,
                Long platform, Long userId, Long size) {
                log.error("【服务熔断】。Service = {}, 根据账号获取未绑定的角色列表",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse batchUpdateRoleUserByUserId(String token,
                RoleUserUpdateParam params) {
                log.error("【服务熔断】。Service = {}, 根据账号批量修改角色",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH);
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse batchAddRoleUserByUserId(String token,
                BatchAddRoleUserParam params) {
                log.error("【服务熔断】。Service = {}, 账号批量新增角色",
                    DcConstants.KEY_FEIGN_DC_BIZ_AUTH);
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse add(AddSubscribeParam params) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse update(AddSubscribeParam params) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse updateStatus(Long sysUid, Long subId) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<SubscribeVo> getList(SubscribeListParam params) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<CommVo> getCommList(Long subId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SysRoleSimpleVo> getRoleList(Long subId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<SubscribeDetailVo> getDetail(Long subId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SubscribeOrderDetailVo> getRoleListByPayNo(String payNo) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SubscribeLogVo> getSubLogList(SubLogListParam params) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<SubscribeOrderVo> getOrderById(String payNo) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<String> createPayOrder(CreatePayOrderParam params) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SubscribeDetailVo> getListByUser(String token, Boolean status) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse addNote(String payNo, String note) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<SiteGroupVo> getSiteGroupsList(String token, List<Integer> types) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SiteGroupVo> getSiteGroupsByUid(String token, Integer type) {
                return RestUtils.serverBusy4ListResponse();
            }
        };
    }
}
