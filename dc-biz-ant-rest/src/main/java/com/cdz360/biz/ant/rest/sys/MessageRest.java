package com.cdz360.biz.ant.rest.sys;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.model.cus.message.param.ListMessageParam;
import com.cdz360.biz.model.cus.message.po.MessagePo;
import com.cdz360.biz.model.cus.message.vo.MessageVo;
import com.cdz360.biz.model.cus.message.vo.UserMessageVo;
import com.cdz360.biz.utils.feign.order.TdChargeFeignClient;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

/**
 * 站内信
 *
 * <AUTHOR>
 */
@RestController
@Slf4j
@Tag(name = "站内信相关接口", description = "站内信")
public class MessageRest extends BaseController {

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private TdChargeFeignClient tdChargeFeignClient;

    @PostMapping("/api/msg/addMessage")
    @Operation(summary = "新增站内信")
    public ObjectResponse addMessage(ServerHttpRequest request, @RequestBody MessagePo message) {
        return authCenterFeignClient.addMessage(this.getToken2(request), message);
    }

    @GetMapping("/api/msg/editMessage")
    @Operation(summary = "站内信撤回")
    public ObjectResponse editMessage(ServerHttpRequest request, @RequestParam("msgId") Long msgId) {
        return authCenterFeignClient.editMessage(this.getToken2(request), msgId);
    }

    @GetMapping("/api/msg/getMessage")
    @Operation(summary = "站内信详情")
    public ObjectResponse<UserMessageVo> getMessage(ServerHttpRequest request, @RequestParam("msgId") Long msgId) {
        return authCenterFeignClient.getMessage(this.getToken2(request), msgId);
    }

    @GetMapping("/api/msg/getUnReadCount")
    @Operation(summary = "获取未读站内信数量")
    public ObjectResponse getUnReadCount(ServerHttpRequest request, @RequestParam("platform") Long platform) {
        return authCenterFeignClient.getUnReadCount(this.getToken2(request), platform);
    }

    @PostMapping("/api/msg/getMsgList")
    @Operation(summary = "站内信列表，支撑平台")
    public ListResponse<MessageVo> getMsgList(ServerHttpRequest request, @RequestBody ListMessageParam reqParam) {
        return authCenterFeignClient.getMsgList(reqParam);
    }

    @PostMapping("/api/msg/getUserMsgList")
    @Operation(summary = "站内信列表，个人")
    public ListResponse<UserMessageVo> getUserMsgList(ServerHttpRequest request, @RequestBody ListMessageParam reqParam) {
        return authCenterFeignClient.getUserMsgList(this.getToken2(request), reqParam);
    }

    @PostMapping("/test")
    public Mono<ObjectResponse<JsonNode>> test(@RequestParam(value = "delay") Integer delay,
                                               @RequestBody JsonNode body) {
        log.info("delay = {}, body = {}", delay, body);
        return this.tdChargeFeignClient.echoBack(delay, body)
                .doOnNext(res -> {
                    log.info("res = {}", res);
                });
    }

}
