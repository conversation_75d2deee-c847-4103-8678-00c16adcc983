package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.model.invoice.dto.ThirdInvoiceZtDto;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoicedRecordDTO;
import com.chargerlinkcar.framework.common.domain.invoice.param.ListThirdKpztParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_INVOICE,
    fallbackFactory = ReactorDataCoreOrderFeignHystrix.class)
public interface ReactorInvoiceFeignClient {

    @PostMapping("/api/invoiced-records/editRecordById")
    Mono<BaseResponse> editRecordById(@RequestBody InvoicedRecordDTO param);

    // 第三方开票主体列表
    @PostMapping("/api/invoice/third/kpzt")
    Mono<ListResponse<ThirdInvoiceZtDto>> thirdInvoiceZtList(
        @RequestBody ListThirdKpztParam param);

    // 强制刷新乐享开票主体数据
    @PostMapping("/api/invoice/third/forceRefreshKpzt")
    Mono<ListResponse<ThirdInvoiceZtDto>> thirdForceRefreshKpzt();
}
