package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.feign.reactor.DataCoreAttractFeignClient;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.cus.param.CustomerAttractListParam;
import com.cdz360.biz.model.trading.sync.vo.CustomerAttractBiVo;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@RestController
public class CustomerAttractRest {

    @Autowired
    private DataCoreAttractFeignClient attractFeignClient;


    @Operation(summary = "获取引流汇总数据")
    @PostMapping(value = "/api/cusAttract/getBiVo")
    public Mono<ObjectResponse<CustomerAttractBiVo>> getCustomerAttractBi(ServerHttpRequest request,
        @RequestBody CustomerAttractListParam param) {
        param.setSysUid(AntRestUtils.getSysUid(request));
        return attractFeignClient.getCustomerAttractBi(param);
    }

    @Operation(summary = "根据客户类型获取引流客户详情数据")
    @PostMapping(value = "/api/cusAttract/getBiList")
    public Mono<ListResponse<CustomerAttractBiVo>> getAttractBiList(ServerHttpRequest request,
        @RequestBody CustomerAttractListParam param) {
        param.setSysUid(AntRestUtils.getSysUid(request));
        param.setCommIdChain(AntRestUtils.getCommIdChain(request));
        return attractFeignClient.getAttractBiList(param);
    }

}
