package com.cdz360.biz.ant.rest.oa;

import static com.cdz360.biz.model.oa.constant.OaConstants.PD_KEY_BILLING;
import static com.cdz360.biz.model.oa.constant.OaConstants.PD_KEY_DEPOSIT_PROCESS;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.AntUserFeignClient;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.InvoiceFeignClient;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.ant.feign.reactor.DataCoreInvoiceFeignClient;
import com.cdz360.biz.ant.feign.reactor.OaFeignClient;
import com.cdz360.biz.ant.rest.InvoiceProcesser;
import com.cdz360.biz.ant.service.DepositInvoiceService;
import com.cdz360.biz.ant.service.download.DownloadFileService;
import com.cdz360.biz.ant.service.oa.OaService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.cus.balance.po.BalanceApplicationPo;
import com.cdz360.biz.model.cus.corp.vo.CorpSimpleVo;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.param.DownloadFileParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import com.cdz360.biz.model.oa.constant.OaConstants;
import com.cdz360.biz.model.oa.param.PrepaidInvoicingEditParam;
import com.cdz360.biz.model.oa.vo.BillInvoiceVo;
import com.cdz360.biz.model.oa.vo.OaInvoicedVo;
import com.cdz360.biz.model.trading.invoice.dto.CorpInvoiceRecordDto;
import com.cdz360.biz.model.trading.invoice.po.InvoiceRecordOrderRefPo;
import com.cdz360.biz.model.trading.order.dto.PayBillInvoiceBi;
import com.cdz360.biz.model.trading.order.param.BillInvoiceVoParam;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.model.trading.order.vo.PayBillVo;
import com.cdz360.biz.oa.param.AddSignTaskParam;
import com.cdz360.biz.oa.param.ApproveParam;
import com.cdz360.biz.oa.param.BatchAddSignTaskParam;
import com.cdz360.biz.oa.param.BatchApproveParam;
import com.cdz360.biz.oa.param.BillingEditParam;
import com.cdz360.biz.oa.param.DepositInvoiceEditParam;
import com.cdz360.biz.oa.param.ListTaskParam;
import com.cdz360.biz.oa.param.OaProcessTagParam;
import com.cdz360.biz.oa.param.PayElecFeeTaskParam;
import com.cdz360.biz.oa.param.UserApproveParam;
import com.cdz360.biz.oa.vo.BatchApproveResult;
import com.cdz360.biz.oa.vo.OaElecPayTaskVo;
import com.cdz360.biz.oa.vo.OaTaskVo;
import com.cdz360.biz.utils.feign.user.UserFeignClient;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceRecordDetail;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.Duration;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Slf4j
@RestController
@RequestMapping("/oa/task")
public class OaTaskRest {

    @Autowired
    private OaService oaService;

    @Autowired
    private OaFeignClient oaFeignClient;

    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;

    @Autowired
    private DataCoreInvoiceFeignClient dataCoreInvoiceFeignClient;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private InvoiceFeignClient invoiceFeignClient;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private AntUserFeignClient antUserFeignClient;

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private InvoiceProcesser invoiceProcesser;

    @Autowired
    private DepositInvoiceService depositInvoiceService;

    @Autowired
    private DownloadFileService downloadFileService;

    @Operation(summary = "用户获取自己申请的任务列表")
    @PostMapping(value = "/assignee/getApplyTasks")
    public Mono<ListResponse<OaTaskVo>> getAssigneeApplyTasks(
        ServerHttpRequest request, @RequestBody ListTaskParam param) {
        log.info("用户获取自己申请的任务列表. param = {}", JsonUtils.toJsonString(param));
        param.setOUid(AntRestUtils.getSysUid(request));
        param.setTopCommId(AntRestUtils.getTopCommId(request));
        return this.oaService.getAssigneeApplyTasks(param);
    }

    @Operation(summary = "用户获取自己审核的任务列表")
    @PostMapping(value = "/assignee/getAuditTasks")
    public Mono<ListResponse<OaTaskVo>> getAssigneeAuditTasks(
        ServerHttpRequest request, @RequestBody ListTaskParam param) {
        log.info("用户获取自己审核的任务列表. param = {}", JsonUtils.toJsonString(param));
        param.setOUid(AntRestUtils.getSysUid(request));
        param.setTopCommId(AntRestUtils.getTopCommId(request));
        return this.oaService.getAssigneeAuditTasks(param);
    }

    @Operation(summary = "用户获取任务列表")
    @PostMapping(value = "/assignee/getTasks")
    public Mono<ListResponse<OaTaskVo>> getAssigneeTasks(
        ServerHttpRequest request, @RequestBody ListTaskParam param) {
        log.info("用户获取任务列表. param = {}", JsonUtils.toJsonString(param));
//        param.setUid(AntRestUtils.getSysUid(request));
        param.setTopCommId(AntRestUtils.getTopCommId(request));
        param.setCommIdChain(AntRestUtils.getCommIdChain(request));
        return this.oaService.getAssigneeTasks(param);
    }

    @Operation(summary = "审核人执行批准操作")
    @PostMapping(value = "/assignee/handleTask")
    public Mono<BaseResponse> handleTask(
        ServerHttpRequest request, @RequestBody ApproveParam params) {
        log.info("审核人执行审批操作. params = {}", params);
        if (params.getApprove() == null) {
            throw new DcArgumentException("请选择审批结果");
        } else if (Boolean.FALSE.equals(params.getApprove())
            && StringUtils.isBlank(params.getNote())) {
            throw new DcArgumentException("请在“处理人意见”处填写驳回原因");
        } else if (StringUtils.isBlank(params.getTaskId())) {
            throw new DcArgumentException("任务ID不能为空");
        }
        if (StringUtils.isBlank(params.getNote())) {
            params.setNote("同意");
        }
        params.setUid(AntRestUtils.getSysUid(request));
        return this.oaFeignClient.assigneeHandleTask(params);
    }

    @Operation(summary = "审核人执行批量批准操作")
    @PostMapping(value = "/assignee/handleTaskBatch")
    public Mono<ObjectResponse<BatchApproveResult>> taskBatchAudit(ServerHttpRequest request,
        @RequestBody BatchApproveParam params) {
        log.info("taskBatchAudit. params = {}", params);
        if (params.getApprove() == null) {
            throw new DcArgumentException("请选择审批结果");
        } else if (Boolean.FALSE.equals(params.getApprove()) && StringUtils.isBlank(
            params.getNote())) {
            throw new DcArgumentException("请在“处理人意见”处填写驳回原因");
        }
        boolean listInvalid = params.getTaskList().stream().anyMatch(
            e -> StringUtils.isEmpty(e.getProcInstId()) || StringUtils.isEmpty(e.getTaskId()));
        if (listInvalid) {
            throw new DcArgumentException("流程ID和任务ID不能为空");
        }
        if (StringUtils.isBlank(params.getNote())) {
            params.setNote("同意");
        }
        params.setUid(AntRestUtils.getSysUid(request));
        return oaFeignClient.handleTaskBatch(params);
    }

    @Operation(summary = "获取待批量审核的电费支付")
    @PostMapping(value = "/assignee/getPayElecFeeTasks")
    public Mono<ListResponse<OaElecPayTaskVo>> getPayElecFeeTasks(ServerHttpRequest request,
        @RequestBody PayElecFeeTaskParam param) {
        log.info("getPayElecFeeTasks. param = {}", JsonUtils.toJsonString(param));
        IotAssert.isTrue(param != null && CollectionUtils.isNotEmpty(param.getProcInstIdList()),
            "请传入流程ID");
        param.setOUid(AntRestUtils.getSysUid(request));
        param.setTopCommId(AntRestUtils.getTopCommId(request));
        return this.oaService.getPayElecFeeTasks(param);
    }

    @Operation(summary = "流程审核人附言")
    @PostMapping(value = "/assignee/handleTaskComment")
    public Mono<BaseResponse> handleTaskComment(
        ServerHttpRequest request, @RequestBody ApproveParam params) {
        log.info("流程审核人附言. params = {}", params);
        if (params.getApprove() == null) {
            throw new DcArgumentException("请选择审批结果");
        } else if (StringUtils.isBlank(params.getNote())) {
            throw new DcArgumentException("请填写附言");
        } else if (StringUtils.isBlank(params.getProcInstId())) {
            throw new DcArgumentException("流程ID不能为空");
        }
        params.setUid(AntRestUtils.getSysUid(request));
        return this.oaFeignClient.assigneeHandleTaskComment(params);
    }

    @Operation(summary = "流程审核人批量附言")
    @PostMapping(value = "/assignee/handleTaskCommentBatch")
    public Mono<ObjectResponse<BatchApproveResult>> handleTaskCommentBatch(
        ServerHttpRequest request, @RequestBody BatchApproveParam params) {
        log.info("流程审核人批量附言. params = {}", params);
        if (params.getApprove() == null) {
            throw new DcArgumentException("请选择审批结果");
        } else if (StringUtils.isBlank(params.getNote())) {
            throw new DcArgumentException("请填写附言");
        }
        boolean listInvalid = params.getTaskList().stream().anyMatch(
            e -> StringUtils.isEmpty(e.getProcInstId()) || StringUtils.isEmpty(e.getTaskId()));
        if (listInvalid) {
            throw new DcArgumentException("流程ID和任务ID不能为空");
        }
        params.setUid(AntRestUtils.getSysUid(request));
        return this.oaFeignClient.handleTaskCommentBatch(params);
    }

    @Operation(summary = "任务向后加签")
    @PostMapping("/addSignAfter")
    public Mono<ObjectResponse<String>> addSignTaskAfter(
        ServerHttpRequest request, @RequestBody AddSignTaskParam param) {
        log.info("变更受理人. param = {}", JsonUtils.toJsonString(param));
        IotAssert.isNotBlank(param.getTaskId(), "任务ID不能为空");
        if (CollectionUtils.isEmpty(param.getSignUidList())) {
            throw new DcArgumentException("请选择加签用户");
        }
        param.setOpUid(AntRestUtils.getSysUid(request).toString());
        return this.oaFeignClient.addSignTaskAfter(param);
    }

    @Operation(summary = "任务批量向后加签")
    @PostMapping("/batchAddSignAfter")
    public Mono<ObjectResponse<BatchApproveResult>> batchAddSignAfter(
        ServerHttpRequest request, @RequestBody BatchAddSignTaskParam param) {
        log.info("批量变更受理人. param = {}", JsonUtils.toJsonString(param));
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getTaskIdList()), "任务ID不能为空");
        if (CollectionUtils.isEmpty(param.getSignUidList())) {
            throw new DcArgumentException("请选择加签用户");
        }
        param.setOpUid(AntRestUtils.getSysUid(request).toString());
        return oaFeignClient.batchAddSignAfter(param);
    }

    @Operation(summary = "导出oa申请详情")
    @GetMapping(value = "/exportOaDetail")
    public Mono<ObjectResponse<ExcelPosition>> exportOaDetail(
        ServerHttpRequest request,
        @ApiParam(value = "下载导出文件名") @RequestParam(value = "exFileName", required = false) String exFileName,
        @Parameter(name = "申请单号", required = true) @RequestParam("procInstId") String procInstId) {
        log.info("导出oa申请详情: procInstId = {}", procInstId);
        IotAssert.isNotBlank(procInstId, "申请单号无效");

        Long sysUid = AntRestUtils.getSysUid(request);
        ObjectNode reqParam = new ObjectMapper().createObjectNode();
        reqParam.put("procInstId", procInstId);
        reqParam.put("sysUid", sysUid);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.PDF)
            .setUid(sysUid)
            .setExFileName(StringUtils.isNotBlank(exFileName) ? exFileName : "申请详情")
            .setFunctionMap(DownloadFunctionType.OA_DETAIL)
            .setReqParam(reqParam.toString());
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
    }

    @Operation(summary = "打印oa申请详情")
    @GetMapping(value = "/printOaDetail")
    public Mono<Void> printOaDetail(
        ServerHttpRequest request, ServerHttpResponse response,
        @ApiParam(value = "下载导出文件名") @RequestParam(value = "exFileName", required = false) String exFileName,
        @Parameter(name = "申请单号", required = true) @RequestParam("procInstId") String procInstId) {
        log.info("打印oa申请详情: procInstId = {}", procInstId);
        IotAssert.isNotBlank(procInstId, "申请单号无效");
        Long sysUid = AntRestUtils.getSysUid(request);
        ObjectNode reqParam = new ObjectMapper().createObjectNode();
        reqParam.put("procInstId", procInstId);
        reqParam.put("sysUid", sysUid);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.PDF)
            .setUid(sysUid)
            .setExFileName(StringUtils.isNotBlank(exFileName) ? exFileName : "申请详情")
            .setFunctionMap(DownloadFunctionType.OA_DETAIL)
            .setReqParam(reqParam.toString());
        return dataCoreDownloadFileFeignClient.downloadFilePrintApply(applyParam)
            .flatMap(res -> {
                if (res.getStatus() == ResultConstant.RES_SUCCESS_CODE) {
                    log.info("准备downloadPrintFile");
                    DownloadFileParam param = new DownloadFileParam();
                    param.setFileType(applyParam.getFileType());
                    param.setSubDir(res.getData().getSubDir());
                    param.setSubFileName(res.getData().getSubFileName());
                    return Mono.just(param)
                        .delayElement(Duration.ofSeconds(2))
                        .flatMap(paramInner -> downloadFileService.downloadPrintFile(paramInner,
                            response));
//                        return downloadFileService.downloadPrintFile(param, response);
                } else {
                    return Mono.error(
                        new RuntimeException("Generate file failed: " + res.getError()));
                }
            }).onErrorResume(throwable -> {
                log.error("Failed to generate or download file: {}", throwable.getMessage(),
                    throwable);
                return Mono.error(throwable);
            });
    }

    @Operation(summary = "申请管理列表导出")
    @PostMapping(value = "/assignee/exportOaList")
    public Mono<ObjectResponse<ExcelPosition>> exportOaList(
        ServerHttpRequest request,
        @RequestBody ListTaskParam param) {
        log.info("导出oa列表,oaList={}", JsonUtils.toJsonString(param));
        param.setTopCommId(AntRestUtils.getTopCommId(request));
        param.setCommIdChain(AntRestUtils.getCommIdChain(request));
        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("OA申请列表")
            .setFunctionMap(DownloadFunctionType.OA_LIST)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
    }

    @Operation(summary = "提交人重新提交审核")
    @PostMapping(value = "/assignee/userHandleTask")
    public Mono<BaseResponse> userHandleTask(ServerHttpRequest request,
        @RequestBody UserApproveParam params) {
        log.info("提交人重新提交审核. params = {}", params);
        params.setUid(AntRestUtils.getSysUid(request));
        params.setOName(AntRestUtils.getSysUserName(request));
        params.setOPhone(AntRestUtils.getSysUserPhone(request));
        params.setTopCommId(AntRestUtils.getTopCommId(request));
        params.setCommIdChain(AntRestUtils.getCommIdChain(request));

        Mono<UserApproveParam> mono = Mono.just(params);

        // 充值申请,需要重新冻结金额
        if (params.getBalanceApplicationPo() != null) {
            params.getBalanceApplicationPo().setApplierId(AntRestUtils.getSysUid(request));
            params.getBalanceApplicationPo().setApplierCommId(AntRestUtils.getCommId(request));
            ObjectResponse<BalanceApplicationPo> res = antUserFeignClient.addBalanceApplication(
                params.getBalanceApplicationPo());
            FeignResponseValidate.check(res);
            params.setDataId(res.getData().getId().toString());
        } else if (params.getBillingEditParam() != null) {
            mono = this.billingProcessResubmitHandler(request, params.getBillingEditParam())
                .map(e -> params);
        } else if (params.getDepositInvoiceEditParam() != null) {
            // 企业 充值开票-编辑
            IotAssert.isTrue(
                PayAccountType.CORP.equals(params.getDepositInvoiceEditParam().getAccountType()),
                "目前仅支持企业开票审批");

            // 对充值单进行校验
            mono = mono.flatMap(e ->
                    Mono.fromCallable(() -> {
                            PayBillParam payBillParam = new PayBillParam();
                            payBillParam.setOrderIdList(
                                params.getDepositInvoiceEditParam().getOrderNoList());
                            ListResponse<PayBillInvoiceBi> payBillRes = dataCoreFeignClient.invoiceOrderList(
                                payBillParam);
                            if (payBillRes != null && CollectionUtils.isNotEmpty(payBillRes.getData())) {
                                payBillRes.getData().forEach(payBillInvoiceBi -> {
                                    IotAssert.isNotNull(payBillInvoiceBi, "充值单不存在");

                                    IotAssert.isTrue(
                                        StringUtils.isBlank(payBillInvoiceBi.getRunProcInstId()),
                                        "该充值单有正在处理中的余额减少流程： procInstId: "
                                            + payBillInvoiceBi.getRunProcInstId() + ", 充值单号:"
                                            + payBillInvoiceBi.getOrderId());
                                });
                            }
                            return e;
                        })
                        .subscribeOn(Schedulers.boundedElastic())
                )
                .flatMap(e -> this.depositInvoiceProcessResubmitHandler(request,
                    params.getDepositInvoiceEditParam()))
                .map(e -> params);
        } else if (params.getDepositInvoiceUserEditParam() != null) {
            // 个人&商户会员 充值开票-编辑
            IotAssert.isTrue(
                List.of(PayAccountType.PERSONAL, PayAccountType.COMMERCIAL)
                    .contains(params.getDepositInvoiceUserEditParam().getAccountType()),
                "目前仅支持个人、商户会员开票审批");
            params.getDepositInvoiceUserEditParam().checkAndFilterField();

            // 对充值单进行校验
            mono = mono.flatMap(e ->
                    Mono.fromCallable(() -> {
                        PayBillParam payBillParam = new PayBillParam();
                        payBillParam.setOrderIdList(
                            params.getDepositInvoiceUserEditParam().getOrderNoList());
                        ListResponse<PayBillVo> payBillRes = dataCoreFeignClient.payBillList(
                            payBillParam);
                        if (payBillRes != null && CollectionUtils.isNotEmpty(payBillRes.getData())) {
                            payBillRes.getData().forEach(payBillVo -> {
                                IotAssert.isNotNull(payBillVo, "充值单不存在");

                                IotAssert.isTrue(StringUtils.isBlank(payBillVo.getRunProcInstId()),
                                    "该充值单有正在处理中的余额减少流程： procInstId: "
                                        + payBillVo.getRunProcInstId() + ", 充值单号:"
                                        + payBillVo.getOrderId());
                            });
                        }
                        return e;
                    }).subscribeOn(Schedulers.boundedElastic())
                )
                .flatMap(e -> depositInvoiceService.userInvoiceSubmit2Audit(request,
                    params.getDepositInvoiceUserEditParam()))
                .map(e -> params);// 模拟个人开票追加订单，个人开票基于充电订单，此处基于充值单，提交审核
        } else if (params.getPrepaidInvoicingEditParam() != null) {

            params.getPrepaidInvoicingEditParam().setOpName(params.getOName());
            mono = this.prepaidInvoiceProcessResubmitHandler(request,
                    params.getPrepaidInvoicingEditParam())
                .map(e -> params);

        }

        return mono.flatMap(e -> this.oaFeignClient.userHandleTask(params))
            .doOnNext(res -> {
                if (params.getBalanceApplicationPo() != null) {
                    if (res.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS) {
                        // 更新申请记录中的流程ID
                        userFeignClient.updateApplyOaProcessInstanceId(
                                Long.valueOf(params.getDataId()),
                                params.getBalanceApplicationPo().getProcInstId())
                            .subscribe(
                                x -> log.debug("流程实例ID[{}]已写入充值记录[{}]",
                                    params.getDataId(),
                                    params.getBalanceApplicationPo().getProcInstId()),
                                ex -> log.error("写库失败, recordId={}, processId={}",
                                    params.getDataId(),
                                    params.getBalanceApplicationPo().getProcInstId(), ex)
                            );
                    } else {
                        log.error("充值申请重新提交数据异常，需要开发接入: {}",
                            params.getBalanceApplicationPo().getProcInstId());
                    }
                }
            })
            .map(res -> res.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS
                ? RestUtils.buildObjectResponse(Boolean.TRUE)
                : RestUtils.buildObjectResponse(Boolean.FALSE));
    }

    /**
     * 充值开票编辑，重提交
     *
     * @param request
     * @param param
     * @return
     */
    private Mono<ObjectResponse<Integer>> depositInvoiceProcessResubmitHandler(
        ServerHttpRequest request, DepositInvoiceEditParam param) {
        IotAssert.isNotNull(param, "请传入入参");
        param.checkAndFilterField();

        ListResponse<CorpSimpleVo> response = authCenterFeignClient.getCorpByCommId(
            AntRestUtils.getToken2(request),
            AntRestUtils.getCommIdChain(request), param.getCorpId());

        if (response == null || CollectionUtils.isEmpty(response.getData())) {
            throw new DcServiceException("企客信息不存在");
        }
        param.setCorpName(response.getData().get(0).getCorpName());

        IotAssert.isTrue(DepositInvoiceRest.CORP_SUPPORT_MODE.equals(param.getInvoiceWay()),
            "企业仅支持开票类型: " + DepositInvoiceRest.CORP_SUPPORT_MODE.getDesc());

        IotAssert.isNotNull(param.getCorpInvoiceInfoVo(), "请传入抬头");
        param.setChannel(param.getCorpInvoiceInfoVo().getChannel());

        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getInvoiceRecords()), "请传入开票内容");

        return Mono.just(param)
            .flatMap(e -> this.depositInvoiceOrderNoProcessing(request,
                param)) // STEP0.根据新提交的billNoList，分别追加和移除订单
            .filter(Optional::isPresent)
            .map(Optional::get)
            .doOnNext(e -> param.setApplyNo(e.getApplyNo()))
            .flatMap(
                recordDetail -> depositInvoiceService.invoiceSubmit2Audit(request, recordDetail,
                    param)) // STEP1.模拟企业客户开票记录提交到审核
            .switchIfEmpty(Mono.just(RestUtils.buildObjectResponse(0)));
    }

    private Mono<ObjectResponse<Integer>> billingProcessResubmitHandler(ServerHttpRequest request,
        BillingEditParam param) {
        param.checkAndFilterField();

        ListResponse<CorpSimpleVo> response = authCenterFeignClient.getCorpByCommId(
            AntRestUtils.getToken2(request),
            AntRestUtils.getCommIdChain(request), param.getCorpId());
        if (response == null || CollectionUtils.isEmpty(response.getData())) {
            throw new DcServiceException("企客信息不存在");
        }
        param.setCorpName(response.getData().get(0).getCorpName());

        return Mono.just(param)
            .flatMap(e -> this.billOrderNoProcessing(request,
                param)) // STEP0.根据新提交的billNoList，分别追加和移除订单
            .filter(Optional::isPresent)
            .map(Optional::get)
            .doOnNext(e -> param.setApplyNo(e.getApplyNo()))
            .flatMap(recordDetail -> invoiceProcesser.invoiceSubmit2Audit(request, recordDetail,
                param)) // STEP1.模拟企业客户开票记录提交到审核
            .switchIfEmpty(Mono.just(RestUtils.buildObjectResponse(0)));
    }

    /**
     * 根据新提交的billNoList，分别追加和移除订单
     */
    private Mono<Optional<CorpInvoiceRecordDetail>> billOrderNoProcessing(ServerHttpRequest request,
        BillingEditParam param) {

        var resp = dataCoreFeignClient.getRecordByProcInstId(param.getProcInstId());
        FeignResponseValidate.checkIgnoreData(resp);
        Optional<CorpInvoiceRecordDto> originOpt = Optional.ofNullable(resp.getData());

        if (Boolean.FALSE.equals(param.getInvoice())) {
            // 若本次不开票，则删除已有的企业开票记录
            originOpt.ifPresent(t -> {
                var res = dataCoreFeignClient.deleteCorpInvoiceRecordByOa(param.getProcInstId(),
                    true);
                FeignResponseValidate.check(res);
            });
            return Mono.just(Optional.empty());
        }

        originOpt.ifPresent(originRecord -> {
            param.setApplyNo(originRecord.getApplyNo());
        });

        // 获取已关联的订单信息
        var response = dataCoreFeignClient.getInvoiceRecordOrderListByOa(param.getProcInstId());
        FeignResponseValidate.check(response);
        List<String> originOrderNoList = response.getData().stream()
            .map(InvoiceRecordOrderRefPo::getOrderNo)
            .collect(Collectors.toList());

        List<String> appendOrderNoList;
        List<String> removeOrderNoList;

        if (CollectionUtils.isNotEmpty(originOrderNoList)) { // 库中已有关联订单

            if (Boolean.TRUE.equals(param.getInvoice())) { // 本次开票

                if (CollectionUtils.isNotEmpty(param.getBillNoList())) { // 本次选了结算单

                    appendOrderNoList = param.getBillNoList().stream()
                        .filter(e -> !originOrderNoList.contains(e))
                        .collect(Collectors.toList());

                    removeOrderNoList = originOrderNoList.stream()
                        .filter(e -> !param.getBillNoList().contains(e))
                        .collect(Collectors.toList());

                } else { // 本次没选结算单
                    appendOrderNoList = null;
                    removeOrderNoList = originOrderNoList;
                }

            } else { // 本次不开票
                appendOrderNoList = null;
                removeOrderNoList = originOrderNoList;
            }

        } else { // 库中无关联订单

            if (Boolean.TRUE.equals(param.getInvoice())) { // 本次开票

                if (CollectionUtils.isNotEmpty(param.getBillNoList())) { // 本次选了结算单
                    appendOrderNoList = param.getBillNoList();
                    removeOrderNoList = null;
                } else { // 本次没选结算单
                    appendOrderNoList = null;
                    removeOrderNoList = null;
                }

            } else { // 本次不开票
                appendOrderNoList = null;
                removeOrderNoList = null;
            }

        }

        Mono<Optional<CorpInvoiceRecordDetail>> mono = Mono.just(Optional.empty());

        if (CollectionUtils.isNotEmpty(removeOrderNoList)) {
            IotAssert.isNotBlank(param.getApplyNo(), "企业客户申请单号无效");
            mono = mono.flatMap(e -> invoiceProcesser.corpRemoveOrder(request, PD_KEY_BILLING,
                    param.getCorpId(),
                    param.getApplyNo(), removeOrderNoList))
                .map(Optional::ofNullable);
        }

        if (CollectionUtils.isNotEmpty(appendOrderNoList)) {
            mono = mono.flatMap(e -> invoiceProcesser.corpAppendOrderByOa(request, PD_KEY_BILLING,
                    param.getApplyNo(),
                    param.getProcInstId(),
                    param.getCorpId(), appendOrderNoList))
                .map(Optional::ofNullable);
        } else if (CollectionUtils.isEmpty(appendOrderNoList)
            && originOpt.isEmpty()
            && Boolean.TRUE.equals(param.getInvoice())) {
            // 特殊情况处理：库中无企业开票记录，本次需要开票但没有选结算单
            mono = mono.flatMap(e -> invoiceProcesser.corpAppendOrderByOa(request, PD_KEY_BILLING,
                    param.getApplyNo(),
                    param.getProcInstId(),
                    param.getCorpId(),
                    null))
                .map(Optional::ofNullable);
        }

        return mono
            .filter(Optional::isPresent)
            .switchIfEmpty(
                Mono.just(originOpt) // “本次未变化关联订单”且“之前存在关联的企业开票记录”
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .map(originRecord -> {
                        CorpInvoiceRecordDetail detail = new CorpInvoiceRecordDetail();
                        detail.setApplyNo(originRecord.getApplyNo())
                            .setTempSalId(originRecord.getTempSalId())
                            .setProductTempId(originRecord.getProductTempId())
                            .setActualElecFee(originRecord.getActualElecFee())
                            .setActualServFee(originRecord.getActualServFee());
                        return Optional.of(detail);
                    })
                    .switchIfEmpty(Mono.just(Optional.empty()))
            );
    }

    private Mono<Optional<CorpInvoiceRecordDetail>> depositInvoiceOrderNoProcessing(
        ServerHttpRequest request,
        DepositInvoiceEditParam param) {

        var resp = dataCoreFeignClient.getRecordByProcInstId(param.getProcInstId());
        FeignResponseValidate.checkIgnoreData(resp);
        Optional<CorpInvoiceRecordDto> originOpt = Optional.ofNullable(resp.getData());

//        originOpt.ifPresent(t -> {
//            var res = dataCoreFeignClient.deleteCorpInvoiceRecordByOa(param.getProcInstId(), true);
//            FeignResponseValidate.check(res);
//        });

        originOpt.ifPresent(originRecord -> {
            param.setApplyNo(originRecord.getApplyNo());
        });

        // 获取已关联的订单信息
        var response = dataCoreFeignClient.getInvoiceRecordOrderListByOa(param.getProcInstId());
        FeignResponseValidate.check(response);
        List<String> originOrderNoList = response.getData().stream()
            .map(InvoiceRecordOrderRefPo::getOrderNo)
            .collect(Collectors.toList());

        List<String> appendOrderNoList;
        List<String> removeOrderNoList;

        appendOrderNoList = param.getOrderNoList().stream()
            .filter(e -> !originOrderNoList.contains(e))
            .collect(Collectors.toList());

        removeOrderNoList = originOrderNoList.stream()
            .filter(e -> !param.getOrderNoList().contains(e))
            .collect(Collectors.toList());

        final List<String> finalRemoveOrderNoList = removeOrderNoList;
        final List<String> finalAppendOrderNoList = appendOrderNoList;

        Mono<Optional<CorpInvoiceRecordDetail>> mono = Mono.just(Optional.empty());

        // 需要移除的订单
        if (CollectionUtils.isNotEmpty(finalRemoveOrderNoList)) {
            IotAssert.isNotBlank(param.getApplyNo(), "企业客户申请单号无效");

            mono = mono.flatMap(e -> invoiceProcesser.corpRemoveOrder(request,
                    PD_KEY_DEPOSIT_PROCESS,
                    param.getCorpId(),
                    param.getApplyNo(), finalRemoveOrderNoList))
                .map(Optional::ofNullable);
        }

        // 需要新增的订单
        if (CollectionUtils.isNotEmpty(finalAppendOrderNoList)) {
            mono = mono.flatMap(
                    e -> invoiceProcesser.corpAppendOrderByOa(request, PD_KEY_DEPOSIT_PROCESS,
                        param.getApplyNo(),
                        param.getProcInstId(),
                        param.getCorpId(), finalAppendOrderNoList))
                .map(Optional::ofNullable);
        }

        return mono
            .filter(Optional::isPresent)
            .switchIfEmpty(
                Mono.just(originOpt) // “本次未变化关联订单”且“之前存在关联的企业开票记录”
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .map(originRecord -> {
                        CorpInvoiceRecordDetail detail = new CorpInvoiceRecordDetail();
                        detail.setApplyNo(originRecord.getApplyNo())
                            .setTempSalId(originRecord.getTempSalId())
                            .setProductTempId(originRecord.getProductTempId())
                            .setActualElecFee(originRecord.getActualElecFee())
                            .setActualServFee(originRecord.getActualServFee())
                            .setActualPrepaidCardFee(originRecord.getActualPrepaidCardFee());
                        return Optional.of(detail);
                    })
                    .switchIfEmpty(Mono.just(Optional.empty()))
            );
    }

    public Mono<BaseResponse> prepaidInvoiceProcessResubmitHandler(ServerHttpRequest request,
        PrepaidInvoicingEditParam editParam) {
        editParam.checkAndFilterField();

        // STEP0.开票主体与商品行模板检查
        ObjectResponse<OaInvoicedVo> objResponse = invoiceFeignClient.templateSalCheckBeforeInvoicingProcess(
            editParam.getInvoiceTitleId(), editParam.getTempSalId(), editParam.getProductTempId());
        FeignResponseValidate.check(objResponse);
        editParam.setInvoiceTitle(objResponse.getData().getName());
        editParam.setSaleName(objResponse.getData().getSaleName());

        ListResponse<BillInvoiceVo> voListResponse = dataCoreFeignClient.getBillInvoiceVoList(
            new BillInvoiceVoParam(editParam.getInterimCode(), editParam.getOrderNoList(),
                editParam.getProcInstId()));
        FeignResponseValidate.check(voListResponse);
        final List<BillInvoiceVo> billInvoiceVoList = voListResponse.getData();

        ObjectResponse<CommercialSimpleVo> resp = authCenterFeignClient.findSimpleVoById(
            editParam.getCommId());
        if (resp == null || resp.getData() == null) {
            throw new DcServiceException("商户信息不存在");
        }
        editParam.setCommName(resp.getData().getCommName());

        Mono<BaseResponse> mono;
        // STEP1.根据账户类型做不同的处理
        switch (editParam.getAccountType()) {
            case PERSONAL:
            case COMMERCIAL:
                mono = dataCoreInvoiceFeignClient.prepaidInvoiceSubmit2Audit(editParam)
                    .doOnNext(FeignResponseValidate::check)
                    .map(e -> (RestUtils.success()));
                break;
            case CREDIT:
                mono = this.prepaidCorpResubmitHandler(request, editParam)
                    .map(e -> (RestUtils.success()));
                break;
            default:
                throw new DcArgumentException("暂不支持的账户类型");
        }
        return mono
            .doOnNext(e -> editParam.setBillInvoiceVoList(billInvoiceVoList));
    }

    private Mono<ObjectResponse<Integer>> prepaidCorpResubmitHandler(
        ServerHttpRequest request, PrepaidInvoicingEditParam param) {

        ListResponse<CorpSimpleVo> response = authCenterFeignClient.getCorpByCommId(
            AntRestUtils.getToken2(request),
            AntRestUtils.getCommIdChain(request), param.getCorpId());
        if (response == null || CollectionUtils.isEmpty(response.getData())) {
            throw new DcServiceException("企客信息不存在");
        }
        param.setCorpName(response.getData().get(0).getCorpName());

        return Mono.just(param)
            .flatMap(e -> this.prepaidInvoiceOrderNoProcessing(request,
                param)) // 根据新提交的orderNoList，分别追加和移除订单
            .filter(Optional::isPresent)
            .map(Optional::get)
            .doOnNext(e -> param.setApplyNo(e.getApplyNo()))
            .flatMap(recordDetail -> {
                // 异步执行
                invoiceProcesser.invoiceSubmit2Audit(request, recordDetail,
                        param) // 模拟企业客户开票记录提交到审核
                    .delayElement(Duration.ofMinutes(2L))
                    .subscribe();
                return Mono.just(RestUtils.buildObjectResponse(1));
            })
            .switchIfEmpty(Mono.just(RestUtils.buildObjectResponse(0)));
    }

    /**
     * 根据新提交的orderNoList，分别追加和移除订单
     */
    private Mono<Optional<CorpInvoiceRecordDetail>> prepaidInvoiceOrderNoProcessing(
        ServerHttpRequest request,
        PrepaidInvoicingEditParam param) {

        var resp = dataCoreFeignClient.getRecordByProcInstId(param.getProcInstId());
        FeignResponseValidate.checkIgnoreData(resp);
        Optional<CorpInvoiceRecordDto> originOpt = Optional.ofNullable(resp.getData());

        AtomicBoolean hasOrderRef = new AtomicBoolean(false);
        originOpt.ifPresent(originRecord -> {
            param.setApplyNo(originRecord.getApplyNo());
            hasOrderRef.set(NumberUtils.gtZero(originRecord.getOrderCnt()));
        });

        /*
        // 获取已关联的订单信息
        var response = dataCoreFeignClient.getInvoiceRecordOrderListByOa(param.getProcInstId());
        FeignResponseValidate.check(response);
        List<String> originOrderNoList = response.getData().stream()
            .map(InvoiceRecordOrderRefPo::getOrderNo)
            .collect(Collectors.toList());

        List<String> appendOrderNoList;
        List<String> removeOrderNoList;

        if (CollectionUtils.isNotEmpty(originOrderNoList)) { // 库中已有关联订单

            appendOrderNoList = param.getOrderNoList().stream()
                .filter(e -> !originOrderNoList.contains(e))
                .collect(Collectors.toList());

            removeOrderNoList = originOrderNoList.stream()
                .filter(e -> !param.getOrderNoList().contains(e))
                .collect(Collectors.toList());

        } else { // 库中无关联订单
            appendOrderNoList = param.getOrderNoList();
            removeOrderNoList = null;
        }
         */

        Mono<Optional<CorpInvoiceRecordDetail>> mono = Mono.just(Optional.empty());

        if (hasOrderRef.get()) {
            IotAssert.isNotBlank(param.getApplyNo(), "企业客户申请单号无效");
            mono = mono.flatMap(e -> invoiceProcesser.corpRemoveOrder(request,
                    OaConstants.PD_KEY_PREPAID_ORDER_INVOICING,
                    param.getCorpId(),
                    param.getApplyNo(),
                    true,
                    null))
                .map(Optional::ofNullable);
        }

        mono = mono.flatMap(e -> invoiceProcesser.corpAppendOrderByOa(request,
                OaConstants.PD_KEY_PREPAID_ORDER_INVOICING,
                param.getApplyNo(),
                param.getProcInstId(),
                param.getCorpId(),
                param.getInterimCode(),
                null))
            .map(Optional::ofNullable);

        return mono
            .filter(Optional::isPresent)
            .switchIfEmpty(
                Mono.just(originOpt) // “本次未变化关联订单”且“之前存在关联的企业开票记录”
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .map(originRecord -> {
                        CorpInvoiceRecordDetail detail = new CorpInvoiceRecordDetail();
                        detail.setApplyNo(originRecord.getApplyNo())
                            .setTempSalId(originRecord.getTempSalId())
                            .setProductTempId(originRecord.getProductTempId())
                            .setActualElecFee(originRecord.getActualElecFee())
                            .setActualServFee(originRecord.getActualServFee());
                        return Optional.of(detail);
                    })
                    .switchIfEmpty(Mono.just(Optional.empty()))
            );
    }

    @Schema(description = "记录标签")
    @PostMapping(value = "/saveTag")
    public Mono<BaseResponse> saveTag(
        ServerHttpRequest request, @RequestBody OaProcessTagParam param) {
        log.debug("saveTag. param = {}", JsonUtils.toJsonString(param));
        return this.oaService.saveTag(param);
    }

    @Schema(description = "批量记录标签")
    @PostMapping(value = "/batchSaveTag")
    public Mono<BaseResponse> batchSaveTag(
        ServerHttpRequest request, @RequestBody OaProcessTagParam param) {
        log.debug("batchSaveTag. param = {}", JsonUtils.toJsonString(param));
        return this.oaService.batchSaveTag(param);
    }

}
