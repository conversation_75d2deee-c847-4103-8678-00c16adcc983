package com.cdz360.biz.ant.domain;

import com.cdz360.base.model.base.type.ChargeOrderStatus;
import com.cdz360.base.model.charge.type.OrderAbnormalReason;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class ChargerOrder implements Serializable {
    /**
     * 公有云订单编号
     */
    private String orderId;

    /**
     * 公有云订单编号
     */
    private String orderNo;

    private Long topCommId;

    /**
     * 商户ID
     */
    //private Integer commercialId;

    /**
     * 站点ID
     */
    private String stationId;

    /**
     * app订单号
     */
    private String openOrderId;

    /**
     * 用户ID
     */
    private Long customerId;

    /**
     * 设备运营商名称
     */
    private String commercialName;

    /**
     * 设备运营商商户全称
     */
    private String commercialFullName;

    /**
     * 客户运营商ID
     */
    //private Long customerCommercialId;

    /**
     * 客户运营商名称
     */
    private String customerCommercialName;

    /**
     * 充电卡号/身份唯一识别号
     */
    private String cardNo;

    /**
     * 订单来源类型,0:微信渠道;1:APP在线发起充电;2:APP蓝牙发起充电:3:刷卡充电:4:桩上报离线数据5定时充电6手动开启7曹操专车开启
     */
    private Integer channelId;

    /**
     * 订单类型，0:未知，1：平台，2：互联互通，3：第三方，301：曹操专车
     */
    private Integer orderType;

    /**
     * 订单金额
     */
//    private Long orderPrice;
    private BigDecimal orderPrice;

    /**
     * 服务费
     */
//    private Long servicePrice;
    private BigDecimal servicePrice;

    /**
     * 电费
     */
//    private Long elecPrice;
    private BigDecimal elecPrice;

    /**
     * 人工调整后金额
     */
//    private Long manualPrice;
    private BigDecimal manualPrice;

    /**
     * 客户实付金额
     */
//    private Long actualPrice;
    private BigDecimal actualPrice;

//    /**
//     * 结算方式，0:未知，1：后付费，2：预付费，3：先付费（固定收费：不退还费用），4：先付费（实时计费：退还费用）
//     */
//    private Integer clearingMode;

    /**
     * 订单电量
     */
//    private Long orderElectricity;
    private BigDecimal orderElectricity;

    /**
     * 状态,-500：硬件故障；-40：上报超时；-35：断连超时；-30关电超时；-29：结束充电失败；-20：链接超时；-10：打开电闸超时；-7：开启充电失败，PIN可能错误；-5:系统繁忙；0：订单未激活；100：电闸打开；200：充电中；250：关电闸，充电停止；300：充电完成；800：订单结束；1000：用户已支付；2000：钱已到账
     */
    private Integer status;

    /**
     * 订单状态
     *
     *  与iot中订单状态保持一致
     */
    private ChargeOrderStatus orderStatus;

    /**
     * 星标标志，默认0，0：非星标；1：星标；
     */
//    private Integer starFlag;

    /**
     * 计费模板ID
     */
    private Long priceSchemeId;

    /**
     * 充电车位ID
     */
//    private Long carportId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 订单创建时间
     */
    private Date createTime;

    /**
     * 订单支付时间
     */
    private Date payTime;

    /**
     * 订单结束时间
     */
    private Date stopTime;

    /**
     * 订单状态修改时间
     */
    private Date updateTime;

    /**
     * CORE订单ID
     */
//    private Long coreOrderId;

    /**
     * 硬件流水号
     */
    private String tradeNo;

    /**
     * 厂商盒子类型
     */
    private Integer boxType;

    /**
     * 显示字段 内容：出厂编号+evseId+connectorId
     */
    private String plugNo;
    private String showId;

    /**
     * 充电接口(枪)编号
     */
    private String connectId;

    /**
     * 充电开始时间
     */
    private Integer chargeStartTime;

    /**
     * 充电结束时间
     */
    private Integer chargeEndTime;

    /**
     * 充电开始电量
     */
//    private Long startElectricity;
    private BigDecimal startElectricity;

    /**
     * 充电结束电量
     */
//    private Long endElectricity;
    private BigDecimal endElectricity;

    /**
     * 充电二维码后四位更换1位蓝牙位，1位网络位，2位pin码
             
     */
    private String pin;

    /**
     * platformId区分app或者微信或者其他平台发起的充电的回传标示
     */
    private String platformId;

    /**
     * 手机号码
     */
    private String mobilePhone;

    /**
     * 站点名称
     */
    private String stationName;

    /**
     * 支付方式
     */
    private Integer payModes;

    /**
     * 支付状态
     */
    private Integer payStatus;

    /**
     * 客户名称
     */
    private String customerName;

    public String getCustomerName() {
//        if (Base64Util.isBase64(customerName)) {
//            return Base64Util.getFromBase64(customerName);
//        } else {
            return customerName;
//        }
    }
    /**
     * 充电车位
     */
//    private String carportName;

    /**
     * 
     */
    private String openOperatorId;

    /**
     * 预付金额
     */
//    private Integer prepayAmount;
//    private BigDecimal prepayAmount;

    /**
     * 是否桩端计费  0否，1是
     */
//    private Integer calcPriceByCharger;

    /**
     * 向客户运营商(客户)收取的金额
     */
//    private Integer customerPrice;

    /**
     * 设备运营商ID
     */
    private Long deviceCommercialId;

    /**
     * 预充电时长 单位秒
     */
//    private Integer preDuration;

    /**
     * core推送发起充电超时，通知第三方结束订单标志 0:正常 其他异常
     */
    private Integer timeoutStatus;

    /**
     * 物业子账号id
     */
//    private Long merchantId;

    @Schema(description = "本金(实际收入): 单位,元")
    private BigDecimal principalAmount;

    @Schema(description = "赠送金: 单位,元")
    private BigDecimal freeGoldAmount;

    /**
     * 盒子编码
     */
    private String boxCode;

    /**
     * 盒子出厂编码
     */
//    private String boxOutFactoryCode;

    /**
     * 桩名称
     */
    private String evseName;

    /**
     * 充电时长
     */
    private String duration;

    /**
     * 客户端版本号
     */
    private String version;

    /**
     * 实时功率
     */
    private String rtPower;

    /**
     * 客户实际消费功率与客户选择的功率对比 0：相等 1：不等
     */
    private String templatePowerIsChanged;

    /**
     * 订单异常结束原因
     */
    private String exceptionReason;

    /**
     * 插座二维码
     */
    private String qrCode;

    /**
     * 插座序列号
     */
    private String connectorId;

    /**
     * 上报订单的设备类型
     */
//    private String orderDiviceType;

    /**
     * 充电是否提前结束标示
     */
    private Integer endUnexpected;

    /**
     * 实际补贴金额(单位：元)
     */
//    private Integer actualSubsidyAmount;
//    private BigDecimal actualSubsidyAmount;

    /**
     * 补贴是否分润(0:不参与分润  1:参与分润 )
     */
//    private Integer subsidyShareProfitStatus;

    /**
     * 补贴类型(1:固定补贴 )
     */
//    private Integer subsidyType;

    /**
     * 补贴时长(单位：分)
     */
//    private String subsidyDuration;

    /**
     * 车辆Vin码ID
     */
    private Long vinId;

    /**
     * 车辆Vin码
     */
    private String vin;

    /**
     * 
     */
    private String startSoc;

    /**
     * 
     */
    private String stopSoc;

    /**
     * 结束原因
     */
    private String stopReason;

    /**
     * 
     */
    private String currentSoc;

    /**
     * 单次充电优惠金额--等级优惠金额(单位分)
     */
    private Long discountMoney;

    /**
     * 电损金额 单位元
     */
    private BigDecimal discount;

    /**
     * 优惠券金额（单位分）
     */
    private Long couponMoney;

    /**
     * 当前数据的记录创建时间
     */
//    private Date createDateTime;

    /**
     * 优惠券id
     */
    private Long activityId;

    /**
     * 枪头名称
     */
    private String chargerName;

    /**
     * 支付账户ID
     */
    private Long payAccountId;

    /**
     * 支付默认账户类型
     */
    private Integer defaultPayType;

    /**
     * 冻结金额
     */
//    private Long frozenAmount;
    private BigDecimal frozenAmount;

    /**
     * 物理卡号
     */
    private String cardChipNo;

    /**
     * t_charger_order
     */
    private static final long serialVersionUID = 1L;

    /**
     * 是否属于异常订单
     */
    private OrderAbnormalReason abnormal;

    /**
     * 用户绑定的车牌
     */
    private String carNo;

    /**
     * 卡名称
     */
    private String cardName;

    /**
     * 卡片类型0在线卡1离线卡
     */
    private Integer cardType;

    /**
     * 车队名称
     */
    private String carDepart;

    /**
     * 线路
     */
    private String lineNum;

    /**
     * 车辆自编号
     */
    private String carNum;

    @Schema(description = "品牌")
    private String brand;

    @Schema(description = "型号")
    private String model;

    @Schema(description = "国标协议版本")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer gbVer;

    @Schema(description = "BMS辅助电压. 12V, 24V")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer bmsVoltage;

//    /**
//     * 订单客诉状态
//     */
//    private ComplainStatus complainStatus;

    /**
     * 发票Id
     */
    private Long invoicedId;

    @Schema(description = "可开票金额: 单位，元")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal invoiceAmount;

    @Schema(description = "已开票金额: 单位，元")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal invoicedAmount;

    @Schema(description = "关联账单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String billNo;

    @Schema(description = "优惠ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long discountRefId;
}