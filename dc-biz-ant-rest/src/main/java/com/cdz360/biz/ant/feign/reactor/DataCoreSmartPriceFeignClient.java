package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.oa.param.OaStartProcessParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE,
    fallbackFactory = DataCoreSmartPriceFeignClientFeignClient.class)
public interface DataCoreSmartPriceFeignClient {

    @PostMapping(value = "/dataCore/smartPrice/generateSmartPrice")
    Mono<ObjectResponse<String>> generateSmartPrice(@RequestBody OaStartProcessParam params,
        @RequestParam(value = "procInstId") String procInstId
    );

    @PostMapping(value = "/dataCore/smartPrice/validateGenerateSmartPrice")
    Mono<ObjectResponse<String>> validateGenerateSmartPrice(@RequestBody OaStartProcessParam params);
}
