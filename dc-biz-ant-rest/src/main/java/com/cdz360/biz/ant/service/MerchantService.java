package com.cdz360.biz.ant.service;

///**
// * <AUTHOR>
// * @version  1.0
// * MerchantServiceImpl
// * @since 2018/11/21
// */
//@Slf4j
//@Service
//public class MerchantService //implements IMerchantService
//{
//
//    @Autowired
//    private MerchantFeignClient merchantFeignClient;
//
//    /**
//     * 根据id查询商户账户
//     *
//     * @param merchantId 商户账户id
//     * @return
//     */
//
//    public ObjectResponse<JSONObject> findMerchantById(Long merchantId, String token) {
//        ObjectResponse<JSONObject> jsonResult = merchantFeignClient.findMerchantById(merchantId, token);
//        log.info("请求结果是" + jsonResult);
//        //返回成功
//        if (jsonResult != null && 0 == jsonResult.getStatus()) {
//            return jsonResult;
//        } else {
//            throw new DcServiceException("");
//        }
//    }
//
//
//}
