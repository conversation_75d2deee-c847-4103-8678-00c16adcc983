package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.service.PlugErrorMessageService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * WebsocketRest
 *  这个接口仅用于测试WS推送，不对外使用
 * @since 2/3/2020 7:26 PM
 * <AUTHOR>
 */
@RestController
@Slf4j
@Tag(name = "这个接口仅用于测试WS推送，不对外使用")
@RequestMapping("/api/websocket")
public class WebsocketRest {


    @Autowired
    private PlugErrorMessageService plugErrorMessageService;


    @MessageMapping("/hello")
    @SendTo("/topic/greeting")
    public String greeting(String msg) {
        log.info("greeting: {}", msg);
        return "hello";
    }


    @GetMapping("/resetPlugErrorCodeMap")
    public BaseResponse resetPlugErrorCodeMap() {
        log.info("重置枪头异常码对应异常");
        plugErrorMessageService.init();
        return BaseResponse.success();
    }

    @GetMapping("/getPlugErrorCodeMap")
    public ObjectResponse<String> getPlugErrorCodeMap() {
        log.info("获取枪头异常码对应异常");
        return new ObjectResponse(plugErrorMessageService.getContext());
    }
}