package com.cdz360.biz.ant.rest.sys;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.aop.CheckToken;
import com.cdz360.biz.ant.service.sys.AccRelativeService;
import com.cdz360.biz.ant.service.sysLog.AccRelativeLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.auth.sys.param.AccRelativeParam;
import com.cdz360.biz.auth.sys.param.AddAccRelativeParam;
import com.cdz360.biz.auth.sys.param.YwUserParam;
import com.cdz360.biz.auth.sys.vo.AccRelativeVo;
import com.cdz360.biz.auth.sys.vo.AccRelativeWithVo;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.chargerlinkcar.framework.common.domain.SysUser;
import com.chargerlinkcar.framework.common.rest.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "运维人员管理接口", description = "运维人员管理")
@RequestMapping("/api/accRelative")
public class AccRelativeRest extends BaseController {

    @Autowired
    private AccRelativeLogService accRelativeLogService;

    @Autowired
    private AccRelativeService accRelativeService;

    @PostMapping(value = "/list")
    public Mono<ListResponse<AccRelativeWithVo>> getVoList(ServerHttpRequest request,
        @RequestBody AccRelativeParam param) {
        param.setCommIdChain(AntRestUtils.getCommIdChainAndFilter(request));
        log.info("getVoList param: {}", param);
        String token = this.getToken2(request);
        return Mono.just(accRelativeService.getVoList(token, param))
            .doOnNext(e -> accRelativeService.syncOrderNum(token));
    }

    @GetMapping(value = "/findByAccount")
    public ObjectResponse<AccRelativeVo> findByAccount(ServerHttpRequest request,
        @RequestParam(value = "account") String account) {
        log.info("findByAccount account: {}", account);
        return accRelativeService.findByAccount(this.getToken2(request), account,
            AntRestUtils.getCommIdChainAndFilter(request));
    }

    @CheckToken
    @Operation(summary = "运维账户新增操作")
    @PostMapping(value = "/add")
    public Mono<ObjectResponse<SysUserVo>> addAccRelative(
        ServerHttpRequest request,
        @RequestBody AddAccRelativeParam param) {
        log.info("addAccRelative param: {}", param);
        param.setUpdateOpType(1)
            .setUpdateOpUid(AntRestUtils.getSysUid(request))
            .setUpdateOpName(AntRestUtils.getSysUserName(request));
        return accRelativeService.addAccRelative(this.getToken2(request), param)
            .doOnNext(user -> accRelativeLogService.addAccRelative(
                request,
                user.getName(),
                user.getUsername()))
            .map(RestUtils::buildObjectResponse);
    }

    @CheckToken
    @Operation(summary = "运维账户编辑操作")
    @PostMapping(value = "/edit")
    public Mono<ObjectResponse<SysUserVo>> editAccRelative(
        ServerHttpRequest request,
        @RequestBody AddAccRelativeParam param) {
        log.info("editAccRelative param: {}", param);
        param.setUpdateOpType(1)
            .setUpdateOpUid(AntRestUtils.getSysUid(request))
            .setUpdateOpName(AntRestUtils.getSysUserName(request));
        return accRelativeService.editAccRelative(this.getToken2(request), param)
            .doOnNext(user -> accRelativeLogService.editAccRelative(
                request,
                user.getName(),
                user.getUsername()))
            .map(RestUtils::buildObjectResponse);
    }

    @CheckToken
    @Operation(summary = "运维账户删除操作")
    @GetMapping(value = "/delete")
    public Mono<ObjectResponse<SysUserVo>> deleteBySysUid(
        ServerHttpRequest request,
        @Parameter(name = "系统用户ID") @RequestParam("sysUid") Long sysUid) {
        log.info("deleteBySysUid sysUid: {}", sysUid);
        return accRelativeService.deleteBySysUid(
                AntRestUtils.getToken2(request), AntRestUtils.getSysUid(request),
                AntRestUtils.getSysUserName(request), sysUid)
            .doOnNext(user -> accRelativeLogService.deleteBySysUid(
                request, user.getName(), user.getUsername()))
            .map(RestUtils::buildObjectResponse);
    }

    @CheckToken(check = "getToken4Aspect")
    @Operation(summary = "获取运维人员列表", description = "通过场站组来获取")
    @PostMapping(value = "/findYwUserList")
    public Mono<ListResponse<SysUser>> findYwUserList(
        ServerHttpRequest request, @RequestBody YwUserParam param) {
        log.info("获取运维人员列表: {}", param);
        param.setUid(AntRestUtils.getSysUid(request))
            .setTopCommId(AntRestUtils.getTopCommId(request));
        return accRelativeService.findYwUserList(param);
    }
}
