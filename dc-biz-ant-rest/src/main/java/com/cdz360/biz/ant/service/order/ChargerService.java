package com.cdz360.biz.ant.service.order;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.EvseBizStatus;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.model.iot.param.ListPlugParam;
import com.cdz360.biz.utils.feign.order.TdChargeFeignClient;
import com.chargerlinkcar.framework.common.domain.OrderReserveVo;
import com.chargerlinkcar.framework.common.domain.request.StartChargerRequest;
import com.chargerlinkcar.framework.common.domain.request.StopChargerRequest;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 充电相关接口
 *
 * <AUTHOR>
 * @since Created on 10:45 2019/3/1.
 */

@Slf4j
@Service
public class ChargerService //implements IChargerService
{

    //
//    @Autowired
//    private SiteFeignClient siteFeignClient;
    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;
    //    @Autowired
//    private SiteDataCoreFeignClient siteDataCoreFeignClient;
//    @Autowired
//    private AuthCenterFeignClient authCenterFeignClient;
    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;
//    @Autowired
//    private AntUserFeignClient userFeignClient;
//    @Autowired
//    private SiteService siteService;
//    @Autowired
//    private RedisUtil redisUtil;
//    @Autowired
//    private RedisIotReadService redisIotReadService;

//    @Value("${iot.fee.min:500}")
//    private Integer cloudChargeStartAmount;//默认启动金额作为平台单枪开启充电最小金额

    @Autowired
    private TdChargeFeignClient tdChargeFeignClient;

    /**
     * 开启充电
     *
     * @param chargerRequest
     * @return
     */

    public BaseResponse startBCharger(StartChargerRequest chargerRequest) {
        this.checkPlugBizStatus(List.of(chargerRequest.getPlugNo()));
        chargerRequest.setStartType(OrderStartType.MGM_WEB_MANUAL);
        return dataCoreFeignClient.webStartCharger(chargerRequest);
    }


    /**
     * 结束充电 不走队列，直接下发停止
     *
     * @param stopRequest
     * @return
     */
    public BaseResponse stopBCharger(StopChargerRequest stopRequest) {
        log.info("平台结束充电参数stopRequest：{}", JsonUtils.toJsonString(stopRequest));

        //STEP 1.验证枪头是否在充电,并赋值orderNO
        this.preCheck(List.of(stopRequest));

        //STEP 2.不进入队列，直接通过trading下发
        BaseResponse jsonObject = tdChargeFeignClient.stopCharge(
                stopRequest.getOrderNo(), AppClientType.MGM_WEB)
            .block(Duration.ofSeconds(50L));
        FeignResponseValidate.check(jsonObject);
        return jsonObject;
    }

    /**
     * 批量结束充电
     *
     * @param stopRequest
     * @return
     */
    public BaseResponse listStopBCharger(List<StopChargerRequest> stopRequest) {
        log.info("平台批量结束充电参数stopRequest：{}", JsonUtils.toJsonString(stopRequest));

        //STEP 1.验证枪头是否在充电,并赋值orderNO
        this.preCheck(stopRequest);

        //STEP 2.停充请求进入队列
        BaseResponse jsonObject = dataCoreFeignClient.webStopCharger(stopRequest);
        FeignResponseValidate.check(jsonObject);
        return jsonObject;
    }

    private void preCheck(List<StopChargerRequest> stopRequest) {
        List<String> plugNoList = stopRequest.stream().map(StopChargerRequest::getPlugNo)
            .collect(Collectors.toList());
        ListPlugParam param = new ListPlugParam().setPlugNoList(plugNoList);
        ListResponse<PlugVo> plugVoListResponse = iotDeviceMgmFeignClient.getPlugList(param);
        FeignResponseValidate.check(plugVoListResponse);
        List<PlugVo> plugVoList = plugVoListResponse.getData();

        IotAssert.isTrue(
            CollectionUtils.isNotEmpty(plugVoList) && plugVoList.size() == plugNoList.size(),
            "无法找到枪头信息，请刷新后重试");
        Map<String, PlugVo> plugVoMap = plugVoList.stream()
            .collect(Collectors.toMap(PlugVo::getPlugNo, o -> o));
        stopRequest.forEach(req -> {
            PlugVo e = plugVoMap.get(req.getPlugNo());
            if (!(e.getStatus().equals(PlugStatus.BUSY) || e.getStatus().equals(PlugStatus.JOIN))) {
                throw new DcServiceException(e.getEvseNo() + "桩" + e.getIdx() + "枪未在充电");
            } else {
                if (StringUtils.isBlank(e.getOrderNo())) {
                    throw new DcServiceException(
                        String.format("%s桩%d枪无充电订单，请确认订单%s是否已经结束",
                            e.getEvseNo(), e.getIdx(), req.getOrderNo()));
                }
                req.setOrderNo(e.getOrderNo());
            }
        });
    }

    /**
     * 获取云端充电请求队列下发情况
     *
     * @param plugNoList
     * @return
     */
    public ObjectResponse<OrderReserveVo> getReserveInfoByPlugNoList(List<String> plugNoList) {
        return dataCoreFeignClient.getReserveInfoByPlugNoList(plugNoList);
    }

    /**
     * 批量开启充电
     *
     * @param chargerRequest
     * @return
     */
    public BaseResponse listStartCharger(StartChargerRequest chargerRequest) {
        this.checkPlugBizStatus(chargerRequest.getPlugNoList());
        chargerRequest.setStartType(OrderStartType.MGM_WEB_BATCH);
        return dataCoreFeignClient.webStartCharger(chargerRequest);
    }

    /**
     * 充电枪头运营状态确认
     *
     * @param plugNoList
     */
    public void checkPlugBizStatus(List<String> plugNoList) {
        if (CollectionUtils.isEmpty(plugNoList)) {
            return;
        }

        ListResponse<PlugVo> res = iotDeviceMgmFeignClient.getPlugList(
            new ListPlugParam().setPlugNoList(plugNoList));
        FeignResponseValidate.check(res);

        final List<PlugVo> plugs = res.getData();
        if (CollectionUtils.isEmpty(plugs)) {
            return;
        }

        final Optional<PlugVo> bizStopPlug = plugs.stream()
            .filter(x -> null != x.getBizStatus() &&
                EvseBizStatus.STOP.equals(x.getBizStatus()))
            .findFirst();
        if (bizStopPlug.isPresent()) {
            final String msg = String.format("%s 当前运营状态为停用，无法开启充电",
                bizStopPlug.get().getEvseName());
            throw new DcArgumentException(msg);
        }
    }

}
