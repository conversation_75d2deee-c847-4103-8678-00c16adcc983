package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.invoice.dto.ThirdInvoiceZtDto;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoicedRecordDTO;
import com.chargerlinkcar.framework.common.domain.invoice.param.ListThirdKpztParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class ReactorInvoiceFeignHystrix implements FallbackFactory<ReactorInvoiceFeignClient> {

    @Override
    public ReactorInvoiceFeignClient apply(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_INVOICE,
            throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_INVOICE,
            throwable.getStackTrace());
        return new ReactorInvoiceFeignClient() {
            @Override
            public Mono<BaseResponse> editRecordById(InvoicedRecordDTO param) {
                log.error("【服务熔断】: Service = {}, api = editRecordById, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_INVOICE, param);
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<ListResponse<ThirdInvoiceZtDto>> thirdInvoiceZtList(
                ListThirdKpztParam param) {
                log.error("【服务熔断】: Service = {}, api = thirdInvoiceZtList, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_INVOICE, param);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<ThirdInvoiceZtDto>> thirdForceRefreshKpzt() {
                log.error("【服务熔断】: Service = {}, api = thirdForceRefreshKpzt",
                    DcConstants.KEY_FEIGN_DC_BIZ_INVOICE);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }
        };
    }
}
