package com.cdz360.biz.ant.rest.park;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.domain.park.dto.ParkingLockDto;
import com.cdz360.biz.ant.domain.park.param.ListParkingLockParam;
import com.cdz360.biz.ant.domain.park.vo.ParkingLockVo;
import com.cdz360.biz.ant.feign.reactor.ParkBizFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorDeviceMgmFeignClient;
import com.cdz360.biz.ant.service.sysLog.PakingLockSysLogService;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.annotations.Api;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

@Api(value = "场站地锁相关接口", tags = "场站地锁相关接口")
@Slf4j
@RestController
@RequestMapping("/api/parkingLock")
public class ParkingLockRest {

    @Autowired
    private PakingLockSysLogService pakingLockSysLogService;

    @Autowired
    private ReactorDeviceMgmFeignClient deviceMgmFeignClient;

    @Autowired
    private ParkBizFeignClient parkBizFeignClient;

    @Operation(summary = "获取地锁列表")
    @PostMapping(value = "/parkingLockList")
    public Mono<ListResponse<ParkingLockVo>> parkingLotList(
            ServerHttpRequest request, @RequestBody ListParkingLockParam param) {
        log.info("获取地锁列表: param = {}", param);
        return deviceMgmFeignClient.parkingLotList(param);
    }

    @Operation(summary = "添加地锁")
    @PostMapping(value = "/addParkingLock")
    public Mono<ObjectResponse<ParkingLockVo>> addParkingLock(
            ServerHttpRequest request, @RequestBody ParkingLockDto dto) {
        log.info("添加地锁: dto = {}", dto);
        return deviceMgmFeignClient.addParkingLock(dto)
                .doOnNext(x -> pakingLockSysLogService.addParkingLock(
                        x.getData().getSiteName(), dto.getSerialNumber(), request));
    }

    @Operation(summary = "删除地锁")
    @GetMapping(value = "/removeParkingLock")
    public Mono<ObjectResponse<ParkingLockVo>> removeParkingLock(
            ServerHttpRequest request,
            @Parameter(name = "地锁ID(平台内部记录ID)", required = true) @RequestParam Long lockId) {
        log.info("删除地锁: {}", LoggerHelper2.formatEnterLog(request));
        return deviceMgmFeignClient.removeParkingLock(lockId)
                .doOnNext(x -> pakingLockSysLogService.removeParkingLock(
                        x.getData().getSiteName(), x.getData().getSerialNumber(), request));
    }

    @Operation(summary = "开闭锁")
    @GetMapping(value = "/switchLock")
    public Mono<BaseResponse> switchLock(
            ServerHttpRequest request,
            @Parameter(name = "场站名称", description = "操作日志", required = true) @RequestParam String siteName,
            @Parameter(name = "地锁ID(供应商)", description = "操作日志", required = true) @RequestParam String serialNumber,
            @Parameter(name = "地锁ID(平台内部记录ID)", required = true) @RequestParam Long lockId,
            @Parameter(name = "打开(true)/关闭(false)", required = true) @RequestParam Boolean open) {
        log.info("开闭锁: {}", LoggerHelper2.formatEnterLog(request));
        return parkBizFeignClient.switchLock(lockId, open)
                .doOnNext(x -> pakingLockSysLogService.switchLock(
                        siteName, serialNumber, open, request));
    }

    @Operation(summary = "远程断电锁(断电以防水淹事故)")
    @GetMapping(value = "/cutPower")
    public Mono<BaseResponse> cutPower(
            ServerHttpRequest request,
            @Parameter(name = "场站名称", description = "操作日志", required = true) @RequestParam String siteName,
            @Parameter(name = "地锁ID(供应商)", description = "操作日志", required = true) @RequestParam String serialNumber,
            @Parameter(name = "地锁ID(平台内部记录ID)", required = true) @RequestParam Long lockId) {
        log.info("远程断电锁: {}", LoggerHelper2.formatEnterLog(request));
        return parkBizFeignClient.cutPower(lockId)
                .doOnNext(x -> pakingLockSysLogService.cutPower(
                        siteName, serialNumber, request));
    }

    @Operation(summary = "远程重启锁")
    @GetMapping(value = "/rebootLock")
    public Mono<BaseResponse> rebootLock(
            ServerHttpRequest request,
            @Parameter(name = "场站名称", description = "操作日志", required = true) @RequestParam String siteName,
            @Parameter(name = "地锁ID(供应商)", description = "操作日志", required = true) @RequestParam String serialNumber,
            @Parameter(name = "地锁ID(平台内部记录ID)", required = true) @RequestParam Long lockId) {
        log.info("远程重启锁: {}", LoggerHelper2.formatEnterLog(request));
        return parkBizFeignClient.rebootLock(lockId)
                .doOnNext(x -> pakingLockSysLogService.rebootLock(
                        siteName, serialNumber, request));
    }

    @Operation(summary = "向地锁云查询地锁")
    @GetMapping(value = "/lookForLock")
    public Mono<ObjectResponse<ParkingLockVo>> lookForLock(
            ServerHttpRequest request,
            @Parameter(name = "场站ID", required = true) @RequestParam String siteId,
            @Parameter(name = "地锁ID(地锁云记录ID)", required = true) @RequestParam String remoteLockId) {
        log.info("向地锁云查询地锁: {}", LoggerHelper2.formatEnterLog(request));
        return parkBizFeignClient.lookForLock(siteId, remoteLockId);
    }

    @Operation(summary = "同步地锁信息")
    @GetMapping(value = "/fetchLockInfo")
    public Mono<BaseResponse> fetchLockInfo(
            ServerHttpRequest request,
            @Parameter(name = "场站ID", required = true) @RequestParam String siteId) {
        log.info("同步地锁信息: {}", LoggerHelper2.formatEnterLog(request));
        return parkBizFeignClient.fetchLockInfo(siteId);
    }
}
