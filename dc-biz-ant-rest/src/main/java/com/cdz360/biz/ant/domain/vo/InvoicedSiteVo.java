package com.cdz360.biz.ant.domain.vo;

import lombok.Data;

import java.io.Serializable;

/**
 * A DTO for the InvoicedSite entity.
 */
@Data
public class InvoicedSiteVo implements Serializable {

    private String id;//东正的id超出了前端的范围,所以这边处理一下
    private String idNo;
    private String name;
    private String siteNo; //站点编号
    private String type;
    private Double longitude;
    private Double latitude;
    private String address;
    private String province;
    private String city;
    private String area;
    private String status;
    private Boolean invoicedValid;


}
