package com.cdz360.biz.ant.service.sim;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.biz.model.sim.vo.SimImportParam;
import com.cdz360.biz.model.sim.vo.SimImportVo;
import com.chargerlinkcar.framework.common.feign.DeviceDataCoreFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.List;

@Slf4j
@Service
public class SimService {

    @Autowired
    private DeviceDataCoreFeignClient deviceDataCoreFeignClient;

    public Mono<ObjectResponse<SimImportVo>> parseSimExcel(String commIdChain, FilePart file) {
        log.info(">>parseSimExcel excel 文件: {}", file.filename());

        Mono<ObjectResponse<SimImportVo>> m = null;

        try {
            File f = new File("/tmp/" + file.filename());
            m = file.transferTo(f)
                    .then(Mono.just("文件上传成功"))
                    .map(a -> {
                        try {

                            InputStream inputStream = new FileInputStream(f);
//                            log.info("inputStream: {}", inputStream);
                            List<List<String>> list = com.cdz360.biz.ant.utils.ExcelUtils.getSimListByExcel(inputStream, file.filename(), 4);
                            log.info("list.size = {}", list == null ? null : list.size());
                            SimImportParam param = new SimImportParam();
                            param.setCommIdChain(commIdChain);
                            param.setList(list);
                            ObjectResponse<SimImportVo> result = deviceDataCoreFeignClient.parseSimExcel(param);
                            log.info("result = {}", result);
                            FeignResponseValidate.check(result);

                            return result;
                        } catch (DcServiceException e) {
                            throw new DcServiceException(e.getMessage(), e);
                        } catch (Exception e) {
                            log.warn("<< 解析excel文件异常. message = {}", e.getMessage(), e);
                            throw new DcServiceException("解析excel文件异常，请求确认文件内容.");
                        }
                    });
//                    .map(a -> RestUtils.buildObjectResponse(a));
        } catch (Exception e) {
            log.warn("msg: {}", e.getMessage(), e);
            throw new DcServiceException("文件上传失败");
        }
        return m;


//        return result;
    }
}
