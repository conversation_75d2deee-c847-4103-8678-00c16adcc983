package com.cdz360.biz.ant.domain.request;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import java.util.Optional;
import lombok.Data;

@Data
public class SiteGeoBiParam {

    private List<String> gids;

    private List<String> siteIdList;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "运营类型, 不传查询所有. 0, 未知; 1, 自营; 2, 非自营; 3, 互联互通; 4, 脱机自营; 5, 脱机非自营;")
    private List<Integer> bizTypeList;

    @Schema(description = "检查摄像机存在与否")
    private Boolean checkCamera;

    @Schema(description = "是否包含场站组信息")
    private Boolean includeGroup;

    @Override
    public String toString() {
        return "SiteGeoBiParam{" +
            "gids=" + Optional.ofNullable(gids).map(List::size).orElse(0) +
            ", siteIdList=" + Optional.ofNullable(siteIdList).map(List::size).orElse(0) +
            ", bizTypeList=" + Optional.ofNullable(bizTypeList).map(List::size).orElse(0) +
            '}';
    }
}
