package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.DeviceFeignClient;
import com.cdz360.biz.ant.feign.IotWorkerFeignClient;
import com.cdz360.biz.ant.feign.MerchantFeignClient;
import com.cdz360.biz.model.site.po.PriceTemplatePo;
import com.cdz360.biz.model.site.type.SiteStatus;
import com.cdz360.biz.model.site.vo.PriceSchemaVo;
import com.cdz360.biz.model.site.vo.PriceTemplateModVo;
import com.cdz360.biz.model.trading.site.param.AddPriceSchemaParam;
import com.cdz360.biz.model.trading.site.param.FetchTargetPriceSchemeParam;
import com.cdz360.biz.model.trading.site.param.ListPriceTemplateParam;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.param.UpdatePriceSchemaParam;
import com.cdz360.biz.model.trading.site.type.TargetPriceSchemeType;
import com.cdz360.biz.model.trading.site.vo.PriceSchemeSiteVo;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import com.cdz360.biz.oa.dto.TargetPriceSchemeInfo;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.param.ListPriceSchemeSiteUseParam;
import com.chargerlinkcar.framework.common.domain.vo.TemplateInfoVo;
import com.chargerlinkcar.framework.common.feign.PriceSchemaFeignClient;
import com.chargerlinkcar.framework.common.feign.SiteDataCoreFeignClient;
import com.chargerlinkcar.framework.common.feign.UserCommercialFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import reactor.core.publisher.Mono;

/**
 * TemplateServiceImpl
 * <p>
 * TemplateServiceImpl
 *
 * <AUTHOR>
 * @since 2019.3.15
 */
@Slf4j
@Service
public class TemplateService {

    @Autowired
    private RedisIotReadService redisIotReadService;

    @Autowired
    private SiteDataCoreFeignClient siteDataCoreFeignClient;

    @Autowired
    private DeviceFeignClient deviceFeignClient;
    @Autowired
    private MerchantFeignClient merchantFeignClient;

    @Autowired
    private UserCommercialFeignClient commercialFeignClient;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private PriceSchemaFeignClient priceTemplateFeignClient;

    @Autowired
    private IotWorkerFeignClient iotWorkerFeignClient;

    /**
     * 分页获取计费模板列表
     *
     * @param keywords    模板名
     * @param commIdChain 商户id链
     * @param page        分页
     * @return
     */

    public ListResponse<TemplateInfoVo> getPagedTemplateInfoList(String keywords,
        String commIdChain,
        Long commId,
        //List<Long> commIdList,
        OldPageParam page, String token, Boolean enable) {

        //组装参数
        Map paramMap = new HashMap();
        paramMap.put("keywords", keywords);
        //paramMap.put("commercialIdList", commIdList);
        paramMap.put("opCommId", commId);
        paramMap.put("commIdChain", commIdChain);
        paramMap.put("page", page.getPageNum());
        paramMap.put("rows", page.getPageSize());
        paramMap.put("enable", enable);
        //查询计费模板信息
        ListResponse<TemplateInfoVo> jsonObject = deviceFeignClient.getTemplateInfoList(paramMap);
//        ListResponse<TemplateInfoVo> jsonObject = deviceFeignClient.getPagedTemplateInfoList(paramMap);
//        log.info("得到计费模板列表{}--参数{}", jsonObject);
        FeignResponseValidate.check(jsonObject);
        if (jsonObject.getData().isEmpty()) {
            return RestUtils.buildListResponse(new ArrayList<>(), 0L);
        }

        //取出数据
        List<TemplateInfoVo> templateList = jsonObject.getData();
//                JSONArray rowsJSONArray = data.getJSONArray("rows");
        log.info("得到数据大小: size = {}", templateList.size());

        // 查询权限中心商户列表
        ListResponse<Commercial> commercialsJson = commercialFeignClient.getCommercials(token,
            commIdChain);
        FeignResponseValidate.check(commercialsJson);

        log.info("根据token查询权限中心商户列表{}", commercialsJson);
        //转换商户对象
        List<Commercial> commList = commercialsJson.getData();
        log.info("得到权限中心商户列表{}", JsonUtils.toJsonString(commList));

        // 计费模板是否被使用
        List<Long> priceSchemeIdList = templateList.stream().map(TemplateInfoVo::getId)
            .collect(Collectors.toList());
        ListResponse<PriceSchemeSiteVo> priceSchemeSiteInfo = this.getPriceSchemeSiteInfo(
            priceSchemeIdList, commIdChain);
        FeignResponseValidate.check(priceSchemeSiteInfo);

        List<PriceSchemeSiteVo> priceSchemeSiteVoList = priceSchemeSiteInfo.getData();

        for (TemplateInfoVo tmp : templateList) {
            //Map map = templateList.get(i);
            //定义过滤器
            Predicate<Commercial> commFilter = (p) -> ((p.getId()).equals(tmp.getCommercialId()));
            //取值赋值
            commList.stream().filter(commFilter)
                .forEach((p) -> tmp.setCommercialName(p.getCommName()));

            // 计费模板是否被使用
            Predicate<PriceSchemeSiteVo> schemeFilter = o -> tmp.getCode()
                .equals(o.getPriceSchemeCode());
            priceSchemeSiteVoList.stream().filter(schemeFilter).forEach(o -> tmp.setUsed(true));
            tmp.setUsed(tmp.getUsed() != null); // 必须赋值
        }

        return jsonObject;

    }


    public void enable(String token, Long id, Boolean enable) {
        log.info("计费模板使能状态调整: id = {}, enable = {}", id, enable);
        BaseResponse res = dataCoreFeignClient.enable(token, id, enable);
        FeignResponseValidate.check(res);
    }

    public BaseResponse deletePvPriceSchema(List<Long> priceIdList) {
        BaseResponse res = dataCoreFeignClient.deletePvPriceSchema(priceIdList);
        FeignResponseValidate.check(res);
        return res;
    }


    public ListResponse<PriceSchemeSiteVo> getPriceSchemeSiteInfo(List<Long> priceSchemeIdList,
        //List<Long> commIdList
        String commIdChain
    ) {
        log.info("获取计费模板下发场站的信息: priceSchemeId = {}", priceSchemeIdList);
        if (null == priceSchemeIdList || priceSchemeIdList.isEmpty()) {
            throw new DcArgumentException("计费模板的Id必须提供");
        }

        // 待下发的场站
        // dataCore 中
        ListPriceSchemeSiteUseParam param = new ListPriceSchemeSiteUseParam();
        param//.setCommIdList(commIdList)
            .setCommIdChain(commIdChain)
            .setPriceSchemeIdList(priceSchemeIdList);
        ListResponse<PriceSchemeSiteVo> dataScheduleDown = dataCoreFeignClient.getByPriceSchemeId(
            param);
        FeignResponseValidate.check(dataScheduleDown);
        List<PriceSchemeSiteVo> scheduleResult = dataScheduleDown.getData();

        log.info("size = {}", scheduleResult.size());
        return RestUtils.buildListResponse(scheduleResult);
    }

    public ListResponse<PriceSchemaVo> getPriceSchemaList(ListPriceTemplateParam param,
        Long opCommId, String commIdChain) {
        ListResponse<PriceTemplatePo> res = this.priceTemplateFeignClient.getPriceSchemaList(param);
        FeignResponseValidate.check(res);
        if (res.getData().isEmpty()) {
            return RestUtils.buildListResponse(new ArrayList<>(), 0L);
        }
        // 计费模板是否被使用
        List<Long> priceSchemeIdList = res.getData().stream().map(PriceTemplatePo::getId)
            .collect(Collectors.toList());
        ListResponse<PriceSchemeSiteVo> priceSchemeSiteInfo = this.getPriceSchemeSiteInfo(
            priceSchemeIdList, commIdChain);
        FeignResponseValidate.check(priceSchemeSiteInfo);

        List<PriceSchemeSiteVo> priceSchemeSiteVoList = priceSchemeSiteInfo.getData();

        List<PriceSchemaVo> list = new ArrayList<>();
        res.getData().stream().forEach(p -> {
            PriceSchemaVo priceScheme = this.toPriceSchemaVo(p, opCommId);

            // 计费模板是否被使用
            Predicate<PriceSchemeSiteVo> schemeFilter = o -> p.getCode()
                .equals(o.getPriceSchemeCode());
            priceSchemeSiteVoList.stream().filter(schemeFilter).findFirst()
                .ifPresentOrElse(x -> priceScheme.setUsed(true), () -> priceScheme.setUsed(false));

            list.add(priceScheme);
        });
        return RestUtils.buildListResponse(list, res.getTotal());
    }


    public ListResponse<PriceTemplatePo> getPriceSchemaListByCode(ListPriceTemplateParam param) {
        ListResponse<PriceTemplatePo> res = this.priceTemplateFeignClient.getPriceSchemaList(param);
        FeignResponseValidate.check(res);
        return res;
    }

    /**
     * 获取计费模板信息（含分时明细）
     *
     * @param priceId
     * @param opCommId
     * @return
     */
    public ObjectResponse<PriceSchemaVo> getPriceSchema(Long priceId, Long opCommId) {
        Assert.notNull(priceId, "计费模板ID不能为空");
        ObjectResponse<PriceTemplatePo> priceRes = priceTemplateFeignClient.getPriceSchema(priceId);
        FeignResponseValidate.check(priceRes);
        PriceSchemaVo priceScheme = this.toPriceSchemaVo(priceRes.getData(), opCommId);
        return new ObjectResponse<>(priceScheme);
    }

    private PriceSchemaVo toPriceSchemaVo(PriceTemplatePo priceSchemaPo, Long opCommId) {
        PriceSchemaVo priceScheme = new PriceSchemaVo();
        BeanUtils.copyProperties(priceSchemaPo, priceScheme);
        // 当前商户是否可以编辑

        if (null != priceScheme.getCommercialId() &&
            priceScheme.getCommercialId().longValue() == opCommId) {
            priceScheme.setEditable(true);
        } else {
            priceScheme.setEditable(false);
        }
        return priceScheme;
    }

    /**
     * 新增计费模板
     *
     * @param param
     * @return
     */
    public ObjectResponse<PriceTemplatePo> addPriceSchema(AddPriceSchemaParam param) {
        var res = this.priceTemplateFeignClient.addPriceSchema(param);
        FeignResponseValidate.check(res);
        return res;
    }


    /**
     * 修改计费模板
     */
    public ObjectResponse<Long> updatePriceSchema(UpdatePriceSchemaParam param) {
        var res = this.priceTemplateFeignClient.updatePriceSchema(param);
        FeignResponseValidate.check(res);
        return res;
    }

    public Mono<ListResponse<TargetPriceSchemeInfo>> fetchTargetPriceScheme(
        FetchTargetPriceSchemeParam param) {
        IotAssert.isNotNull(param.getTargetType(), "请指定目标类型");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getNoList()), "请指定查询目标");

        if (TargetPriceSchemeType.EVSE.equals(param.getTargetType())) {
            List<EvseVo> evseList = redisIotReadService.getEvseList(param.getNoList());
            IotAssert.isTrue(param.getNoList().size() == evseList.size(),
                "缓存中无法获取对应桩信息");
            return Mono.just(RestUtils.buildListResponse(evseList.stream().map(
                    x -> new TargetPriceSchemeInfo().setNo(x.getEvseNo()).setName(x.getName())
                        .setPriceSchemeId(x.getPriceCode()))
                .collect(Collectors.toList())));
        } else { // == SITE
            ListSiteParam baseListParam = new ListSiteParam()
                .setStatusList(
                    List.of(SiteStatus.ONLINE, SiteStatus.OPENING, SiteStatus.UNAVAILABLE))
                .setSiteIdList(param.getNoList());
            baseListParam.setSize(param.getNoList().size())
                .setTotal(false);
            ListResponse<SiteVo> res = siteDataCoreFeignClient.getSiteVoList(baseListParam);
            FeignResponseValidate.check(res);
            IotAssert.isTrue(param.getNoList().size() == res.getData().size(),
                "提交存在无效场站ID");

            List<TargetPriceSchemeInfo> list = res.getData().stream()
                .filter(x -> CollectionUtils.isNotEmpty(x.getTemplateList()))
                .flatMap(x -> x.getTemplateList().stream()
                    .map(v -> new TargetPriceSchemeInfo()
                        .setNo(x.getId())
                        .setName(x.getSiteName())
                        .setTemplateType(v.getTemplateType())
                        .setPriceSchemeId(v.getTemplateId())))
                .collect(Collectors.toList());

            return Mono.just(RestUtils.buildListResponse(list));

//            return Mono.just(RestUtils.buildListResponse(res.getData().stream().map(
//                    x -> new TargetPriceSchemeInfo().setNo(x.getId()).setName(x.getSiteName())
//                        .setPriceSchemeId(x.getTemplateId()))
//                .collect(Collectors.toList())));
        }
    }

    public ListResponse<PriceTemplateModVo> getPriceSchemaModList(ListPriceTemplateParam param) {
        ListResponse<PriceTemplateModVo> res = this.priceTemplateFeignClient.getPriceSchemaModList(param);
        FeignResponseValidate.check(res);
        return res;
    }
}
