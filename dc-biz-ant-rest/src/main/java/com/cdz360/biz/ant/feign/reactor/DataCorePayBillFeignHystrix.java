package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.trading.order.vo.PayBillVo;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class DataCorePayBillFeignHystrix
    implements FallbackFactory<DataCorePayBillFeignClient> {

    @Override
    public DataCorePayBillFeignClient apply(Throwable throwable) {
        return new DataCorePayBillFeignClient() {
            @Override
            public Mono<ObjectResponse<PayBillVo>> tkView(String outRefundNo) {
                log.error("【服务熔断】: Service = {}, api = tkView(退款记录查看), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, outRefundNo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }
        };
    }

    @Override
    public <V> Function<V, DataCorePayBillFeignClient> compose(
        Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(
        Function<? super DataCorePayBillFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
        return null;
    }
}
