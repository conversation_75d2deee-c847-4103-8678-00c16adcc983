package com.cdz360.biz.ant.rest.tj;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.service.tj.TjCompetitorService;
import com.cdz360.biz.model.tj.kc.param.ListTjCompetitorParam;
import com.cdz360.biz.model.tj.kc.param.UpdateTjCompetitorParam;
import com.cdz360.biz.model.tj.kc.vo.TjCompetitorVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "投建竞争对手相关操作接口", description = "投建竞争对手相关操作接口")
@Slf4j
@RestController
@RequestMapping("/api/tj/competitor")
public class TjCompetitorRest {

    @Autowired
    private TjCompetitorService tjCompetitorService;

    @Operation(summary = "获取投建竞争者")
    @PostMapping("/findTjCompetitor")
    public Mono<ListResponse<TjCompetitorVo>> findTjCompetitor(
        ServerHttpRequest request,
        @RequestBody ListTjCompetitorParam param) {
        log.info("获取投建竞争者 {} param = {}", LoggerHelper2.formatEnterLog(request, false),
            param);
        return tjCompetitorService.findTjCompetitor(param);
    }

    @Operation(summary = "新增或编辑竞争者")
    @PostMapping("/addCompetitor")
    public Mono<ObjectResponse<TjCompetitorVo>> addCompetitor(
        ServerHttpRequest request, @RequestBody UpdateTjCompetitorParam param) {
        log.info("新增或编辑竞争者 {} param = {}", LoggerHelper2.formatEnterLog(request, false),
            param);
        return tjCompetitorService.addCompetitor(param);
    }

    @Operation(summary = "删除竞争者")
    @GetMapping(value = "/disableCompetitor")
    public Mono<ObjectResponse<TjCompetitorVo>> disableCompetitor(
        ServerHttpRequest request,
        @ApiParam("竞争者唯一ID") @RequestParam("competitorId") Long competitorId) {
        log.info("删除竞争者 {} competitorId = {}", LoggerHelper2.formatEnterLog(request), competitorId);
        return tjCompetitorService.disableCompetitor(competitorId);
    }
}
