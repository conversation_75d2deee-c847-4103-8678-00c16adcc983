package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.trading.bill.dto.ZftThirdOrderDto;
import com.cdz360.biz.model.trading.bill.param.ListZftThirdOrderParam;
import com.cdz360.biz.model.trading.bill.vo.TradeOrderBi;
import com.cdz360.biz.utils.feign.bill.DailyBillDataCoreFeignClient;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@Tag(name = "支付平台账单订单相关接口", description = "支付平台账单订单相关接口")
@RestController
public class ZftThirdOrderRest {

    @Autowired
    private DailyBillDataCoreFeignClient dailyBillDataCoreFeignClient;

    @Operation(summary = "获取支付平台账单订单列表")
    @PostMapping(value = "/api/thirdOrder/findAllZftThirdOrder")
    public Mono<ListResponse<ZftThirdOrderDto>> findAllZftThirdOrder(
            ServerHttpRequest request,
            @RequestBody ListZftThirdOrderParam param) {
        log.info("获取支付平台账单订单列表: {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        param.setCommIdChain(AntRestUtils.getCommIdChain(request));
        return dailyBillDataCoreFeignClient.findAllZftThirdOrder(param);
    }

    @Operation(summary = "企业直付交易记录统计")
    @PostMapping(value = "/api/thirdOrder/tradeOrderBi")
    public Mono<ListResponse<TradeOrderBi>> tradeOrderBi(
            ServerHttpRequest request,
            @RequestBody ListZftThirdOrderParam param) {
        log.info("企业直付交易记录统计: {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        param.setCommIdChain(AntRestUtils.getCommIdChain(request));
        return dailyBillDataCoreFeignClient.tradeOrderBi(param);
    }
}
