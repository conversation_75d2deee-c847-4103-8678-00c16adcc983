package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.domain.bundle.ListUpgradeLogParam;
import com.cdz360.biz.model.upgradepg.type.BundleType;
import com.cdz360.biz.model.upgradepg.vo.UpgradeLogVo;
import org.springframework.http.MediaType;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_IOT_DEVICE_MGM,
    fallbackFactory = DeviceBundleFeignHystrix.class)
public interface DeviceBundleFeignClient {

    // 设备升级包上传
    @PostMapping(value = "/device/mgm/evsebundle/uploadEvseBundle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
//    Mono<ObjectResponse<Long>> uploadEvseBundle(@RequestPart("file") MultipartFile file);
    Mono<ObjectResponse<Long>> uploadEvseBundle(
        @RequestPart("file") FilePart file,
        @RequestParam("type") BundleType type,
        @RequestParam("swVer") String swVer,
        @RequestParam("opId") Long opId,
        @RequestParam("opName") String opName);

    // 控制器升级记录
    @PostMapping(value = "/device/mgm/mgc/upgradeLogList")
    Mono<ListResponse<UpgradeLogVo>> mgcUpgradeLogList(@RequestBody ListUpgradeLogParam param);
}
