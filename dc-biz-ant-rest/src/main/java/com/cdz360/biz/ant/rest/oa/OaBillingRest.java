package com.cdz360.biz.ant.rest.oa;

import static com.cdz360.biz.model.oa.constant.OaConstants.PD_KEY_BILLING;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.reactor.DataCoreInvoiceFeignClient;
import com.cdz360.biz.ant.feign.reactor.OaFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorUserFeignClient;
import com.cdz360.biz.ant.rest.InvoiceProcesser;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.cus.corp.vo.CorpSimpleVo;
import com.cdz360.biz.model.cus.settlement.param.SettlementEditParam;
import com.cdz360.biz.model.oa.param.BillingParam;
import com.cdz360.biz.model.trading.invoice.dto.CorpInvoiceRecordDto;
import com.cdz360.biz.model.trading.invoice.po.CorpInvoiceRecordPo;
import com.cdz360.biz.model.trading.invoice.po.InvoiceRecordOrderRefPo;
import com.cdz360.biz.oa.param.BillingEditParam;
import com.cdz360.biz.oa.param.BillingProcessParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceRecordDetail;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/oa/billing")
public class OaBillingRest {

    @Autowired
    private OaFeignClient oaFeignClient;
    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;
    @Autowired
    private DataCoreInvoiceFeignClient dataCoreInvoiceFeignClient;
    @Autowired
    private ReactorUserFeignClient userFeignClient;
    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;
    @Autowired
    private InvoiceProcesser invoiceProcesser;

    @ApiOperation(value = "企客对账开票流程提交")
    @PostMapping("/startProcess")
    public Mono<ObjectResponse<String>> startProcess(
        ServerHttpRequest request, @RequestBody BillingParam param) {
        log.info("企客对账开票流程提交: {}", param);
        param.checkAndFilterField();

        ListResponse<CorpSimpleVo> response = authCenterFeignClient.getCorpByCommId(
            AntRestUtils.getToken2(request),
            AntRestUtils.getCommIdChain(request), param.getCorpId());
        if (response == null || CollectionUtils.isEmpty(response.getData())) {
            throw new DcServiceException("企客信息不存在");
        }
        param.setCorpName(response.getData().get(0).getCorpName());

        return Mono.just(param)
            .filter(e -> Boolean.TRUE.equals(e.getInvoice())) // STEP0.判断是否开票（为否时，不建立账单关联关系）
            .flatMap(e -> invoiceProcesser.corpAppendOrderByOa(request, PD_KEY_BILLING,
                    null, null,
                    param.getCorpId(), param.getBillNoList()) // STEP1.模拟企业客户开票追加订单
                .flatMap(recordDetail -> {
                    param.setApplyNo(recordDetail.getApplyNo());
                    return invoiceProcesser.invoiceSubmit2Audit(request, recordDetail,
                        param); // STEP2.模拟企业客户开票记录提交到审核
                })
                .map(t -> e))
            .switchIfEmpty(Mono.just(param))
            .flatMap(e -> {
                return this.startBillingProcess(request, param); // STEP3.创建企客对账开票流程
            })
            .flatMap(objectResponse -> {
                return Mono.just(objectResponse)
                    .filter(e -> param.getApplyNo() != null) // STEP4.判断申请单号是否为空
                    .flatMap(resp -> {
                        CorpInvoiceRecordPo recordPo = new CorpInvoiceRecordPo();
                        recordPo.setApplyNo(param.getApplyNo())
                            .setProcInstId(resp.getData());
                        // STEP5.流程实例ID写入到企业开票记录
                        return dataCoreInvoiceFeignClient.updateCorpInvoiceRecord(recordPo)
                            .doOnNext(FeignResponseValidate::check)
                            .filter(e -> CollectionUtils.isNotEmpty(param.getBillNoList()))
                            .map(e -> {
                                SettlementEditParam dto = new SettlementEditParam();
                                dto.setBillNoList(param.getBillNoList())
                                    .setProcInstId(resp.getData());
                                return dto;
                            })
                            .flatMap(e -> userFeignClient.updateSettlementBatch(
                                e)) // STEP6.流程实例ID写入到结算单记录
                            .doOnNext(FeignResponseValidate::check)
                            .map(e -> resp)
                            .switchIfEmpty(Mono.just(resp));
                    })
                    .switchIfEmpty(Mono.just(objectResponse));
            });
    }

    /**
     * STEP3.创建企客对账开票流程
     */
    private Mono<ObjectResponse<String>> startBillingProcess(ServerHttpRequest request,
        BillingParam param) {
        BillingProcessParam processParam = new BillingProcessParam(
            AntRestUtils.getSysUid(request),
            AntRestUtils.getSysUserName(request),
            AntRestUtils.getSysUserPhone(request),
            AntRestUtils.getTopCommId(request),
            AntRestUtils.getCommIdChain(request),
            param);
        return this.oaFeignClient.startBillingProcess(processParam)
            .doOnNext(FeignResponseValidate::check);
    }

    @ApiOperation(value = "企客对账开票流程-删除平台账单")
    @PostMapping("/deleteOrderInProcess")
    public Mono<BaseResponse> deleteOrderInProcess(ServerHttpRequest request,
        @RequestBody BillingEditParam param) {
        Long corpId = param.getCorpId();
        String procInstId = param.getProcInstId();
        List<String> reqBillNoList = param.getBillNoList();
        if (null == corpId) {
            throw new DcArgumentException("请选择企客名称");
        }
        if (null == procInstId) {
            throw new DcArgumentException("缺少流程实例ID入参");
        }
        if (CollectionUtils.isEmpty(reqBillNoList)) {
            throw new DcArgumentException("请选择要删除的账单");
        }

        var resp = dataCoreFeignClient.getRecordByProcInstId(procInstId);
        FeignResponseValidate.checkIgnoreData(resp);
        Optional<CorpInvoiceRecordDto> originOpt = Optional.ofNullable(resp.getData());

        originOpt.ifPresent(originRecord -> {
            param.setApplyNo(originRecord.getApplyNo());
        });

        // 获取已关联的订单信息
        var response = dataCoreFeignClient.getInvoiceRecordOrderListByOa(procInstId);
        FeignResponseValidate.check(response);
        List<String> originOrderNoList = response.getData().stream()
            .map(InvoiceRecordOrderRefPo::getOrderNo)
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(originOrderNoList)) {
            // 无需删除账单
            return Mono.just(new BaseResponse());
        }

        // 取交集
        List<String> removeOrderNoList = originOrderNoList.stream()
            .filter(reqBillNoList::contains)
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(removeOrderNoList)) {
            // 无需删除账单
            return Mono.just(new BaseResponse());
        }

        IotAssert.isNotBlank(param.getApplyNo(), "企业客户申请单号无效");
        return invoiceProcesser.corpRemoveOrder(request, PD_KEY_BILLING,
                corpId,
                param.getApplyNo(), removeOrderNoList)
            .map(Optional::ofNullable)
            .filter(Objects::nonNull)
            .map(e -> RestUtils.success())
            .switchIfEmpty(Mono.just(
                RestUtils.fail(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "删除账单失败")));
    }

}
