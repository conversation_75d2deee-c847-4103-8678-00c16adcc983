package com.cdz360.biz.ant.rest.yw;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.UserType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.aop.CheckToken;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.ant.service.yw.YwOrderService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.trading.yw.param.CreateYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.EvseDetailParam;
import com.cdz360.biz.model.trading.yw.param.ListYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.SolvedYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.TransYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.UpdateYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.UpdateYwOrderStatusParam;
import com.cdz360.biz.model.trading.yw.po.YwOrderPo;
import com.cdz360.biz.model.trading.yw.type.YwOrderStatus;
import com.cdz360.biz.model.trading.yw.vo.EvseDetailVo;
import com.cdz360.biz.model.trading.yw.vo.YwOrderBi;
import com.cdz360.biz.model.trading.yw.vo.YwOrderLogVo;
import com.cdz360.biz.model.trading.yw.vo.YwOrderVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "运维工单相关操作接口", description = "运维工单相关操作接口")
@Slf4j
@RestController
public class YwOrderRest {

    @Autowired
    private YwOrderService ywOrderService;

    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;

    @Operation(summary = "运维工单获取桩信息")
    @PostMapping("/api/ywOrder/evseDetail")
    public Mono<ObjectResponse<EvseDetailVo>> evseDetail(
        ServerHttpRequest request, @RequestBody EvseDetailParam param) {
        log.info("运维工单获取桩信息: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return ywOrderService.evseDetail(AntRestUtils.getSysUserGids(request), param)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "获取物品列表")
    @GetMapping(value = "/api/ywOrder/goodsList")
    public Mono<ListResponse<String>> goodsList(ServerHttpRequest request) {
        return ywOrderService.goodsList()
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "获取场站最近一条运维工单记录")
    @GetMapping(value = "/api/ywOrder/getSiteLatestRec")
    public Mono<ObjectResponse<YwOrderVo>> getSiteLatestRec(
        ServerHttpRequest request,
        @Parameter(name = "场站ID", required = true)
        @RequestParam(value = "siteId") String siteId) {
        log.info("获取场站最近一条运维工单记录: {}", LoggerHelper2.formatEnterLog(request));
        return ywOrderService.getSiteLatestRec(siteId);
    }

    @Operation(summary = "获取故障报修列表", description = "获取自己提交的故障报修运维工单")
    @PostMapping(value = "/api/ywOrder/listOrder")
    public Mono<ListResponse<YwOrderVo>> listYwOrder(
        ServerHttpRequest request, @RequestBody ListYwOrderParam param) {
        log.info("获取故障报修列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        param.setCreateUid(AntRestUtils.getSysUid(request));
        return ywOrderService.findYwOrder(param);
    }

    @Operation(summary = "更新运维工单标签")
    @PostMapping(value = "/api/ywOrder/updateTag")
    public Mono<ObjectResponse<YwOrderVo>> updateYwOrderTag(@RequestBody UpdateYwOrderParam param) {
        log.debug("更新运维工单标签: param = {}", JsonUtils.toJsonString(param));
        return ywOrderService.updateYwOrderTag(param);
    }

    @Operation(summary = "获取运维工单列表")
    @PostMapping(value = "/api/ywOrder/findAll")
    public Mono<ListResponse<YwOrderVo>> findYwOrder(
        ServerHttpRequest request, @RequestBody ListYwOrderParam param) {
        log.info("获取运维工单列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        if (CollectionUtils.isEmpty(param.getGids())) {
            List<String> gids = AntRestUtils.getSysUserGids(request);
            if (CollectionUtils.isNotEmpty(gids)) {
                param.setGids(gids);
            } else {
                param.setCommIdChain(AntRestUtils.getCommIdChainAndFilter(request));
            }
        }

        // 桩管家登录只能看属于自己的运维工单
        AppClientType appClientType = AntRestUtils.getAppClientType(request);
        if (AppClientType.MGM_WX_LITE.equals(appClientType)) {
            if (param.getOrderStatusList().contains(YwOrderStatus.SOLVED)) { // 历史工单
                param.setLookUpUid(AntRestUtils.getSysUid(request));
            } else {
                param.setMaintUid(AntRestUtils.getSysUid(request));
            }
        }

        return ywOrderService.findYwOrder(param);
    }

    @Operation(summary = "统计运维人员运维数据")
    @PostMapping(value = "/api/ywOrder/ywOrderBi")
    public Mono<ListResponse<YwOrderBi>> ywOrderBi(
        ServerHttpRequest request, @RequestBody ListYwOrderParam param) {
        log.info("获取运维工单列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        String commIdChain = AntRestUtils.getCommIdChain(request);
        param.setCommIdChain(commIdChain);

        return ywOrderService.ywOrderBi(param);
    }

    @Operation(summary = "获取运维工单详情")
    @GetMapping(value = "/api/ywOrder/getYwOrderDetail")
    public Mono<ObjectResponse<YwOrderVo>> getYwOrderDetail(
        ServerHttpRequest request,
        @Parameter(name = "运维工单编号", required = true) @RequestParam(value = "ywOrderNo") String ywOrderNo) {
        log.info("获取运维工单详情: {}", LoggerHelper2.formatEnterLog(request));
        return ywOrderService.getYwOrderDetail(ywOrderNo);
    }

    @Operation(summary = "创建运维工单")
    @PostMapping(value = "/api/ywOrder/createOrder")
    public Mono<ObjectResponse<YwOrderPo>> createYwOrder(
        ServerHttpRequest request, @RequestBody CreateYwOrderParam param) {
        log.info("创建运维工单: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        param.setOpUid(AntRestUtils.getSysUid(request))
            .setOpName(AntRestUtils.getSysUserName(request))
            .setOpType(UserType.SYS_USER);
        return ywOrderService.createYwOrder(AntRestUtils.getSysUserGids(request), param);
    }

    @Operation(summary = "运维工单转派")
    @PostMapping(value = "/api/ywOrder/trans")
    public Mono<ObjectResponse<YwOrderVo>> transYwOrder(
        ServerHttpRequest request, @RequestBody TransYwOrderParam param) {
        log.info("运维工单转派: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        param.setOpUid(AntRestUtils.getSysUid(request))
            .setOpName(AntRestUtils.getSysUserName(request))
            .setOpType(UserType.SYS_USER);
        return ywOrderService.transYwOrder(param);
    }

    @Operation(summary = "运维工单接单")
    @GetMapping(value = "/api/ywOrder/received")
    public Mono<BaseResponse> receivedYwOrder(
        ServerHttpRequest request,
        @Parameter(name = "运维工单编号") @RequestParam(value = "ywOrderNo") String ywOrderNo) {
        log.info("运维工单接单: {}", LoggerHelper2.formatEnterLog(request));

        if (StringUtils.isBlank(ywOrderNo)) {
            throw new DcArgumentException("运维工单编号无效");
        }

        UpdateYwOrderStatusParam param = new UpdateYwOrderStatusParam();
        param.setYwOrderNoList(List.of(ywOrderNo))
            .setOpUid(AntRestUtils.getSysUid(request))
            .setOpName(AntRestUtils.getSysUserName(request))
            .setOpType(UserType.SYS_USER);
        return ywOrderService.receivedYwOrder(param);
    }

    @Operation(summary = "运维工单质检")
    @PostMapping(value = "/api/ywOrder/check")
    public Mono<BaseResponse> checkYwOrder(
        ServerHttpRequest request, @RequestBody UpdateYwOrderStatusParam param) {
        log.info("运维工单质检: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        param.setOpUid(AntRestUtils.getSysUid(request))
            .setOpName(AntRestUtils.getSysUserName(request))
            .setOpType(UserType.SYS_USER);
        return ywOrderService.checkYwOrder(param);
    }

    @Operation(summary = "运维工单删除")
    @GetMapping(value = "/api/ywOrder/del")
    public Mono<BaseResponse> checkYwOrder(ServerHttpRequest request,
        @RequestParam("ywOrderNo") String ywOrderNo) {
        log.info("运维工单删除: {}, ywOrderNo = {}",
            LoggerHelper2.formatEnterLog(request, false), ywOrderNo);

        UpdateYwOrderStatusParam param = new UpdateYwOrderStatusParam();
        param.setYwOrderNoList(List.of(ywOrderNo))
            .setOrderStatus(YwOrderStatus.DELETED)
            .setOpUid(AntRestUtils.getSysUid(request))
            .setOpName(AntRestUtils.getSysUserName(request))
            .setOpType(UserType.SYS_USER);
        return ywOrderService.orderDel(param);
    }

    @Operation(summary = "运维工单开始")
    @GetMapping(value = "/api/ywOrder/start")
    public Mono<BaseResponse> startYwOrder(
        ServerHttpRequest request,
        @Parameter(name = "运维工单编号") @RequestParam(value = "ywOrderNo") String ywOrderNo) {
        log.info("运维工单开始: {}", LoggerHelper2.formatEnterLog(request));

        UpdateYwOrderStatusParam param = new UpdateYwOrderStatusParam();
        param.setYwOrderNoList(List.of(ywOrderNo))
            .setOpUid(AntRestUtils.getSysUid(request))
            .setOpName(AntRestUtils.getSysUserName(request))
            .setOpType(UserType.SYS_USER);
        return ywOrderService.startYwOrder(param);
    }

    @Operation(summary = "运维工单挂起")
    @GetMapping(value = "/api/ywOrder/suspend")
    public Mono<BaseResponse> suspendYwOrder(ServerHttpRequest request,
        @Parameter(name = "运维工单编号") @RequestParam(value = "ywOrderNo") String ywOrderNo) {
        log.info("运维工单挂起: {}", LoggerHelper2.formatEnterLog(request));

        if (StringUtils.isBlank(ywOrderNo)) {
            throw new DcArgumentException("运维工单编号无效");
        }

        UpdateYwOrderStatusParam param = new UpdateYwOrderStatusParam();
        param.setYwOrderNoList(List.of(ywOrderNo))
            .setOpUid(AntRestUtils.getSysUid(request))
            .setOpName(AntRestUtils.getSysUserName(request))
            .setOpType(UserType.SYS_USER);
        return ywOrderService.suspendYwOrder(param);
    }

    @Operation(summary = "运维工单保存操作")
    @PostMapping(value = "/api/ywOrder/save")
    public Mono<ObjectResponse<YwOrderPo>> saveYwOrder(
        ServerHttpRequest request, @RequestBody SolvedYwOrderParam param) {
        log.info("运维工单保存操作: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        param.setOpUid(AntRestUtils.getSysUid(request))
            .setOpName(AntRestUtils.getSysUserName(request))
            .setOpType(UserType.SYS_USER);
        return ywOrderService.saveYwOrder(param);
    }

    @Operation(summary = "运维工单解决提交操作")
    @PostMapping(value = "/api/ywOrder/solved")
    public Mono<ObjectResponse<YwOrderPo>> solvedYwOrder(
        ServerHttpRequest request, @RequestBody SolvedYwOrderParam param) {
        log.info("运维工单解决提交操作: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        param.setOpUid(AntRestUtils.getSysUid(request))
            .setOpName(AntRestUtils.getSysUserName(request))
            .setOpType(UserType.SYS_USER);
        return ywOrderService.solvedYwOrder(param);
    }

    @Operation(summary = "运维工单转派记录列表")
    @GetMapping(value = "/api/ywOrder/transList")
    public Mono<ListResponse<YwOrderLogVo>> ywOrderTransList(
        ServerHttpRequest request,
        @Parameter(name = "运维工单编号", required = true)
        @RequestParam("ywOrderNo") String ywOrderNo) {
        log.info("运维工单转派记录列表: {}", LoggerHelper2.formatEnterLog(request));
        return ywOrderService.ywOrderTransList(ywOrderNo);
    }


    @Operation(summary = "导出运维工单详情")
    @GetMapping(value = "/api/ywOrder/exportYwOrderPdf")
    public Mono<ObjectResponse<ExcelPosition>> exportYwOrderPdf(
        ServerHttpRequest request,
        @ApiParam(value = "下载导出文件名") @RequestParam(value = "exFileName", required = false) String exFileName,
        @Parameter(name = "运维工单编号", required = true) @RequestParam("ywOrderNo") String ywOrderNo) {
        log.info("导出运维工单详情PDF: ywOrderNo = {}", ywOrderNo);

        if (StringUtils.isBlank(ywOrderNo)) {
            throw new DcArgumentException("运维工单编号无效");
        }

        Long sysUid = AntRestUtils.getSysUid(request);
        ObjectNode reqParam = new ObjectMapper().createObjectNode();
        reqParam.put("ywOrderNo", ywOrderNo);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.PDF)
            .setUid(sysUid)
            .setExFileName(StringUtils.isNotBlank(exFileName) ? exFileName : "运维详情")
            .setFunctionMap(DownloadFunctionType.YW_ORDER_DETAIL)
            .setReqParam(reqParam.toString());
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return ywOrderService.exportYwOrderPdf(ywOrderNo);
    }

    @CheckToken
    @Operation(summary = "运维工单列表导出(EXCEL)")
    @PostMapping(value = "/api/ywOrder/exportYwOrderExcel")
    public Mono<ObjectResponse<ExcelPosition>> exportYwOrderExcel(
        ServerHttpRequest request, @RequestBody ListYwOrderParam param) {
        log.info("运维工单列表导出(EXCEL): {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

//        String commIdChain = AntRestUtils.getCommIdChain(request);
//        param.setCommIdChain(commIdChain);

        if (CollectionUtils.isEmpty(param.getGids())) {
            List<String> gids = AntRestUtils.getSysUserGids(request);
            if (CollectionUtils.isNotEmpty(gids)) {
                param.setGids(gids);
            } else {
                param.setCommIdChain(AntRestUtils.getCommIdChainAndFilter(request));
            }
        }

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("运维工单")
            .setFunctionMap(DownloadFunctionType.YW_ORDER)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return ywOrderService.exportYwOrderExcel(param);
    }
}
