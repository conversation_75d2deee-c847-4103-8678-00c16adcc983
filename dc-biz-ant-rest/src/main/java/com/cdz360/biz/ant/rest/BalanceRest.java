package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.UserType;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.aop.CheckAuth;
import com.cdz360.biz.ant.service.AuthCenterService;
import com.cdz360.biz.ant.service.BalanceService;
import com.cdz360.biz.ant.service.sysLog.CustomerSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.trading.order.po.PayBillPo;
import com.chargerlinkcar.framework.common.constant.TransTypeEnum;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.CommercialSample;
import com.chargerlinkcar.framework.common.domain.CorpListPointLogParam;
import com.chargerlinkcar.framework.common.domain.PointLog;
import com.chargerlinkcar.framework.common.domain.PointPo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.service.DcCusBalanceService;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import java.util.Locale;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.couchbase.CouchbaseProperties.Io;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.server.ServerWebExchange;

import javax.validation.Valid;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2018/11/21
 */
@Slf4j
@Validated
@RestController
@Tag(name = "个人钱包接口", description = "支付")
@RequestMapping("/api/balance")
public class BalanceRest extends BaseController {

    @Autowired
    private BalanceService balanceService;

    @Autowired
    private CustomerSysLogService customerSysLogService;

    @Autowired
    private AuthCenterService iAuthCenterService;

    @Autowired
    private DcCusBalanceService dcCusBalanceService;


    /**
     * 商户手动更新用户余额
     *
     * @param tradingAmount 交易金额
     * @param uid           用户ID
     * @param remark        备注
     * @param reason        原因
     * @return
     */
//    @ServiceLog(operation = "商户手动更新用户余额", level = LogTypeEnum.INFO)
//    @ResponseBody
    @RequestMapping(value = "/updateBalance", method = RequestMethod.POST)
    @CheckAuth(code = "userDeposit", codesOr = {"depositUserOfComm"})
    public ObjectResponse<PointPo> updateBalance(
            ServerHttpRequest request,
            @RequestParam(value = "uid", required = false) Long uid,
            @RequestParam(value = "tradingAmount", required = true) BigDecimal tradingAmount,
            @RequestParam(value = "transType", required = true) TransTypeEnum transType,
            @RequestParam(value = "remark", required = false) String remark,
            @RequestParam(value = "reason", required = false) String reason) {

        if (uid == null) {
            throw new DcArgumentException("用户ID不能为空");
        }

        CommercialSample commercialSample = this.getCommercialSample2(request);
        if (ObjectUtils.isEmpty(commercialSample)) {
            throw new DcArgumentException("参数错误");
        }
        Long commId = commercialSample.getComId();
        if (commId == null || commId <= 0) {
            throw new DcArgumentException("参数错误");
        }
        Long merchantId = commercialSample.getId();
        if (merchantId == null || merchantId <= 0) {
            throw new DcArgumentException("参数错误");
        }


        if (DecimalUtils.lteZero(tradingAmount)
                || DecimalUtils.gt(tradingAmount, BigDecimal.valueOf(20000000))) {
            throw new DcArgumentException("请输入合理的金额");
        }

        ObjectResponse resultaAddBalance = balanceService.updateBalance(uid, commId, tradingAmount, transType, reason, remark);
        return resultaAddBalance;
    }

    /**
     * 商户手动更新用户余额
     *
     * @param request
     * @param po
     * @return
     */
//    @ServiceLog(operation = "商户手动更新用户余额", level = LogTypeEnum.INFO)
    @Operation( summary = "商户手动更新用户余额")
    @RequestMapping(value = "/updateBalanceV2", method = RequestMethod.POST)
    @CheckAuth(code = "userDeposit", codesOr = {"depositUserOfComm"})
    public ObjectResponse<Integer> updateBalanceV2(
            ServerHttpRequest request,
            @RequestBody @Valid PayBillPo po) {
        log.info(LoggerHelper2.formatEnterLog(request, false) +
                " param = " + JsonUtils.toJsonString(po));

        Locale locale = AntRestUtils.getLocale(request);
        IotAssert.isTrue(StringUtils.isNotBlank(po.getUsername()), "客户名称不能为空");

        if (locale == null) {
            IotAssert.isTrue(StringUtils.isNotBlank(po.getPhone()), "客户手机号不能为空");
        }


        // 当前登录商户
        CommercialSample comm = this.getCommercialSample2(request);
        if (ObjectUtils.isEmpty(comm)) {
            throw new DcArgumentException("当前操作用户信息不存在，请重新登录");
        }
        Long sysUserCommId = comm.getComId();
        if (sysUserCommId == null || sysUserCommId <= 0) {
            throw new DcArgumentException("当前操作用户商户信息不明确");
        }

        Long opUid = comm.getId();
        if (opUid == null || opUid <= 0) {
            throw new DcArgumentException("当前操作用户Id不存在");
        }


        // 操作人信息
        po.setOpUid(opUid);
        po.setOpName(comm.getName());
        po.setOpUserType(UserType.SYS_USER);

        ObjectResponse<Integer> res = balanceService.updateBalanceV2(po);
        customerSysLogService.rechargeLog(po.getUsername(),
            StringUtils.isNotEmpty(po.getPhone()) ? po.getPhone() : po.getEmail(),
            po.getFlowType(), DecimalUtils.add(po.getAmount(), po.getFreeAmount()), request);
        return res;
    }


    /**
     * 账户流水
     *
     * @param request
     * @return
     */
    @Operation(summary = "账户流水")
    @GetMapping(value = "/queryPersonalBalanceLog")
    public ListResponse<PointLog> queryPersonalBalanceLog(
            ServerHttpRequest request,
            ServerWebExchange exh,
            @Parameter(name = "所属商户id") @RequestParam(value = "commId", required = true) long commId,
            @Parameter(name = "用户id") @RequestParam(value = "userId", required = true) long userId) {

        if (userId <= 0) {
            log.error("[userId is null]  >>>>> reqest end");
            throw new DcArgumentException("用户id参数错误");
        }

        if (commId <= 0) {
            throw new DcArgumentException("所属商户id参数错误");
        }
        Long topCommId = AntRestUtils.getTopCommId(request);
        if (topCommId == null || topCommId.longValue() < 1L) {
            log.error("参数错误,缺少参数topCommId");
            throw new DcArgumentException("参数错误,缺少参数topCommId");
        }
        OldPageParam page = getPage2(request, exh, false);

        log.info(LoggerHelper2.formatEnterLog(request) + "查询账户列表 userId: {}, subCommId: {}"
                , userId, commId);

        CorpListPointLogParam  param = new CorpListPointLogParam();
        param.setTopCommId(topCommId)
                .setCommId(topCommId)
                .setUid(userId);

        if (page != null) {
            long start = page.getPageNum() == 0 ? 0 : (page.getPageNum() - 1) * page.getPageSize();
            Integer size = page.getPageSize();
            param.setStart(start);
            param.setSize(size);
        }

        param.setPayAccountType(PayAccountType.PERSONAL);
        ListResponse<PointLog> ObjectResponse = dcCusBalanceService.listPointLog(param);
        return ObjectResponse;
    }

}
