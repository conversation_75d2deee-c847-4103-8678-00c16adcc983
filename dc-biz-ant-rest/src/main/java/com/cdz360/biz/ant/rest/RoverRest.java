package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.trading.rover.dto.RoverMixDto;
import com.cdz360.biz.model.trading.rover.param.RoverSearchParam;
import com.cdz360.biz.model.trading.rover.param.SiteListRoverCycleParam;
import com.cdz360.biz.model.trading.rover.po.SiteRoverCfgPo;
import com.cdz360.biz.model.trading.rover.po.SiteRoverPo;
import com.cdz360.biz.model.trading.rover.vo.SiteRoverVo;
import com.cdz360.biz.utils.feign.rover.RoverFeignClient;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * RoverRest
 *
 * <AUTHOR>
 * @since 7/27/2022 4:06 PM
 */
@Slf4j
@RestController
@Tag(name = "运营巡查", description = "运营巡查")
@RequestMapping("/api/rover")
public class RoverRest {

    @Autowired
    private RoverFeignClient roverFeignClient;

    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;

    @Operation(summary = "创建运营巡查信息")
    @PostMapping(value = "/createRover")
    public Mono<ObjectResponse<RoverMixDto>> createRover(
        ServerHttpRequest request, @RequestBody RoverMixDto param) {
        log.info("创建运营巡查信息: {}", JsonUtils.toJsonString(param));

        Long opUid = AntRestUtils.getSysUid(request);
        String opName = AntRestUtils.getSysUserName(request);
        IotAssert.isNotNull(opUid, "请登录");
        IotAssert.isNotNull(param.getSiteRoverVo(), "请传入运营巡查信息");
        param.getSiteRoverVo().setRoverUid(opUid);
        param.getSiteRoverVo().setRoverName(opName);

        return roverFeignClient.createRover(param);
    }

    @Operation(summary = "修改运营巡查信息")
    @PostMapping(value = "/updateRover")
    public Mono<ObjectResponse<RoverMixDto>> updateRover(
        ServerHttpRequest request, @RequestBody RoverMixDto param) {
        log.info("修改运营巡查信息: {}", JsonUtils.toJsonString(param));
        return roverFeignClient.updateRover(param);
    }

    @Operation(summary = "取消运营巡查")
    @PostMapping(value = "/cancelRover")
    public Mono<ObjectResponse<Boolean>> cancelRover(
        ServerHttpRequest request, @RequestParam(value = "roverId") Long roverId) {
        log.info("取消运营巡查: {}", roverId);
        Long opUid = AntRestUtils.getSysUid(request);
        String opName = AntRestUtils.getSysUserName(request);
        IotAssert.isNotNull(opUid, "请登录");
        return roverFeignClient.cancelRover(roverId, opUid, opName);
    }

    @Operation(summary = "删除运营巡查")
    @PostMapping(value = "/deleteRover")
    public Mono<ObjectResponse<Boolean>> deleteRover(
        ServerHttpRequest request, @RequestParam(value = "roverId") Long roverId) {
        log.info("删除运营巡查: {}", roverId);
        return roverFeignClient.deleteRover(roverId);
    }

    @Operation(summary = "打分运营巡查")
    @PostMapping(value = "/rankRover")
    public Mono<ObjectResponse<SiteRoverPo>> rankRover(
        ServerHttpRequest request, @RequestBody SiteRoverPo param) {
        log.info("打分运营巡查: {}", JsonUtils.toJsonString(param));
        Long opUid = AntRestUtils.getSysUid(request);
        String opName = AntRestUtils.getSysUserName(request);
        IotAssert.isNotNull(opUid, "请登录");
        param.setRaterName(opName).setRaterUid(opUid);
        return roverFeignClient.rankRover(param);
    }

    @Operation(summary = "列表查询")
    @PostMapping(value = "/searchRoverList")
    public Mono<ListResponse<RoverMixDto>> searchRoverList(
        ServerHttpRequest request, @RequestBody RoverSearchParam param) {
        log.info("查询运营巡查信息: {}", JsonUtils.toJsonString(param));
        Long roverUid = null;
        String commIdChain = null;
        if (AppClientType.MGM_WX_LITE.equals(AntRestUtils.getAppClientType(request))) {
            roverUid = AntRestUtils.getSysUid(request);
            log.info("使用'{}'访问, 入参增加创建者uid= {}",
                AntRestUtils.getAppClientType(request).getDesc(),
                roverUid);
        } else {
            commIdChain = AntRestUtils.getCommIdChain(request);
            log.info("使用'{}'访问, 入参增加idChain= {}",
                AntRestUtils.getAppClientType(request).getDesc(),
                commIdChain);
        }
        param.setRoverUid(roverUid);
        param.setCommIdChain(commIdChain);
        log.info("param = {}", JsonUtils.toJsonString(param));
        return roverFeignClient.searchRoverList(param);
    }

    @Operation(summary = "获取运营巡查配置")
    @PostMapping(value = "/getSiteRoverCfg")
    public Mono<ObjectResponse<SiteRoverCfgPo>> getSiteRoverCfg(
        ServerHttpRequest request, @RequestParam(value = "siteId") String siteId) {
        log.info("获取运营巡查配置: {}", siteId);
        return roverFeignClient.getSiteRoverCfg(siteId);
    }

    @Operation(summary = "设置运营巡查配置")
    @PostMapping(value = "/setSiteRoverCfg")
    public Mono<ObjectResponse<SiteRoverCfgPo>> setSiteRoverCfg(
        ServerHttpRequest request, @RequestBody SiteRoverCfgPo param) {
        log.info("设置运营巡查配置: {}", JsonUtils.toJsonString(param));
        return roverFeignClient.setSiteRoverCfg(param);
    }

    @Operation(summary = "导出运营巡查详情")
    @GetMapping(value = "/exportRoverDetailPdf")
    public Mono<ObjectResponse<ExcelPosition>> exportRoverDetailPdf(
        ServerHttpRequest request,
        @ApiParam(value = "下载导出文件名") @RequestParam(value = "exFileName", required = false) String exFileName,
        @Parameter(name = "运营巡查id", required = true) @RequestParam("roverId") Long roverId) {
        log.info("导出运营巡查详情PDF: roverId = {}", roverId);

        IotAssert.isNotNull(roverId, "运营巡查编号无效");

        Long sysUid = AntRestUtils.getSysUid(request);
        ObjectNode reqParam = new ObjectMapper().createObjectNode();
        reqParam.put("roverId", roverId);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.PDF)
            .setUid(sysUid)
            .setExFileName(StringUtils.isNotBlank(exFileName) ? exFileName : "运营巡查详情")
            .setFunctionMap(DownloadFunctionType.ROVER_DETAIL)
            .setReqParam(reqParam.toString());
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return ywOrderService.exportYwOrderPdf(ywOrderNo);
    }

    @Operation(summary = "导出运营巡检列表excel")
    @PostMapping(value = "/exportRoverListExcel")
    public Mono<ObjectResponse<ExcelPosition>> exportRoverListExcel(
        ServerHttpRequest request,
        @RequestBody RoverSearchParam param) {
        log.info("导出运营巡检列表excel: param = {}", JsonUtils.toJsonString(param));

        IotAssert.isNotNull(param, "运营巡检查询入参无效");

        Long sysUid = AntRestUtils.getSysUid(request);
        ObjectNode reqParam = new ObjectMapper().createObjectNode();
        reqParam.put("param", JsonUtils.toJsonString(param));
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName(
                StringUtils.isNotBlank(param.getExFileName()) ? param.getExFileName() : "运营巡检")
            .setFunctionMap(DownloadFunctionType.ROVER_LIST)
            .setReqParam(reqParam.toString());
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
    }

    @Operation(summary = "待巡查场站列表")
    @GetMapping(value = "/getSiteRoverCycleList")
    public Mono<ListResponse<SiteRoverVo>> getSiteRoverCycleList(
        ServerHttpRequest request) {
        SiteListRoverCycleParam param = new SiteListRoverCycleParam();

        IotAssert.isNotNull(param, "待巡查场站列表");

        Long sysUid = AntRestUtils.getSysUid(request);
        IotAssert.isNotNull(sysUid, "请登录");
        param.setUid(sysUid);
        param.setToken(AntRestUtils.getToken2(request));

        log.info("待巡查场站列表: param = {}", JsonUtils.toJsonString(param));

//        return Mono.just(new ListResponse<>(siteRoverService.getSiteRoverCycleList(param)));
        return roverFeignClient.getSiteRoverCycleList(param);
    }

    @Operation(summary = "最近一次场站巡查信息")
    @GetMapping(value = "/getSiteRoverLatest")
    public Mono<ObjectResponse<SiteRoverVo>> getSiteRoverLatest(
        ServerHttpRequest request, @RequestParam("siteId") String siteId) {
        log.info("最近一次场站巡查信息: siteId = {}", siteId);
        IotAssert.isNotBlank(siteId, "请传入场站id");
        return roverFeignClient.getSiteRoverLatest(siteId);
    }
}