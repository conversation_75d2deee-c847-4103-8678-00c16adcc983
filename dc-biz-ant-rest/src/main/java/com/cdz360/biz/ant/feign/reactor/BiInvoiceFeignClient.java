package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.invoice.param.ListCorpInvoiceRecordParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.ListInvoicedRecordParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_BI,
    fallbackFactory = BiInvoiceFeignHystrix.class)
public interface BiInvoiceFeignClient {

    // 企业开票涉及的订单导出
    @Deprecated(since = "20220317")
    @PostMapping(value = "/bi/invoice/exportCorpInvoiceOrder")
    Mono<ObjectResponse<ExcelPosition>> exportCorpInvoiceOrder(
        @RequestParam("applyNo") String applyNo);

    // C端用户开票记录导出
    @Deprecated
    @PostMapping(value = "/bi/invoice/exportInvoicedRecord")
    Mono<ObjectResponse<ExcelPosition>> exportInvoicedRecord(
        @RequestBody ListInvoicedRecordParam param);

    // 商户结算单明细导出Excel
    @PostMapping(value = "/bi/sett/record/exportExcel")
    Mono<ObjectResponse<ExcelPosition>> exportSettRecExcel(
        @RequestParam(value = "settleId") String settleId);

    //企业开票申请列表
    @Deprecated
    @PostMapping(value = "/bi/invoice/exportCorpInvoiceRecord")
    Mono<ObjectResponse<ExcelPosition>> exportCorpInvoiceRecord(
        @RequestBody ListCorpInvoiceRecordParam param);
}
