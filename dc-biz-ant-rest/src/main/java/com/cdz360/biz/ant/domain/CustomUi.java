package com.cdz360.biz.ant.domain;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

/**
 *
 * 商户界面设置
 * <AUTHOR>
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CustomUi implements Serializable {

	/** 表主键 */
	protected Long id;

	/** 商户号 */
	protected String merchants;

	/** 商户logo */
	protected String logo;

	/** 商户平台名称*/
	protected String platformName;

	/**
	 * 是否使用商户logo，0不使用，1使用
	 */
	protected Integer useCommercialLogo;
	
	/**
	 * 是否使用商户域名，0不使用，1使用
	 */
	protected Integer useCommercialDomain;
	
	/** 左侧菜单背景颜色 */
	protected String menuColor;

	/** 域名 */
	protected String domainName;

	/** 重点按钮颜色 */
	protected String btnColor;

	/**
	 * 公司名称
	 */
	private String commercialName;
	
	/**
	 * 公司简称
	 */
	private String commercialShortName;
	

}
