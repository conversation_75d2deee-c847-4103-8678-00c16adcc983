package com.cdz360.biz.ant.service.tj;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.feign.reactor.BizTjFeignClient;
import com.cdz360.biz.model.tj.kc.param.ListTjSurveyParam;
import com.cdz360.biz.model.tj.kc.param.RepeatSurveyParam;
import com.cdz360.biz.model.tj.kc.param.TjSurveyBiParam;
import com.cdz360.biz.model.tj.kc.po.TjCashOutflowPo;
import com.cdz360.biz.model.tj.kc.po.TjDepreciationPo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyBiVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyVo;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TjCashOutflowService {

    @Autowired
    private BizTjFeignClient bizTjFeignClient;


    public Mono<ObjectResponse<TjDepreciationPo>> findTjDepreciation() {
        return bizTjFeignClient.findTjDepreciation();
    }

    public Mono<ObjectResponse<TjDepreciationPo>> saveTjDepreciation(
        TjDepreciationPo tjDepreciationPo) {
        return bizTjFeignClient.saveTjDepreciation(tjDepreciationPo);
    }

    public Mono<ListResponse<TjCashOutflowPo>> findTjCashOutflow(
        Integer type) {
        return bizTjFeignClient.findTjCashOutflow(type);
    }

    public Mono<ObjectResponse<TjCashOutflowPo>> getTjCashOutflowById(
        Long id) {
        return bizTjFeignClient.getTjCashOutflowById(id);
    }

    public Mono<ObjectResponse<TjCashOutflowPo>> saveTjCashOutflow(
        TjCashOutflowPo tjCashOutflowPo) {
        return bizTjFeignClient.saveTjCashOutflow(tjCashOutflowPo);
    }

    public Mono<ObjectResponse<TjCashOutflowPo>> disableTjCashOutflow(
        Long id) {
        return bizTjFeignClient.disableTjCashOutflow(id);
    }
}
