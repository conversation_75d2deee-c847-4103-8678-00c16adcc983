package com.cdz360.biz.ant.rest.site;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.domain.request.BatchRmParam;
import com.cdz360.biz.ant.domain.request.RemoveSiteBlacklistParam;
import com.cdz360.biz.ant.service.SiteBlacklistService;
import com.cdz360.biz.ant.service.sysLog.CustomerSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.cus.site.param.ListSiteBlacklistParam;
import com.cdz360.biz.model.cus.site.param.SiteBlacklistEnableParam;
import com.cdz360.biz.model.cus.site.vo.SiteBlacklistVo;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

@Tag(name = "场站用户黑名单相关接口", description = "场站用户黑名单")
@Slf4j
@RestController
public class SiteBlacklistRest {

    @Autowired
    private SiteBlacklistService siteBlacklistService;
    @Autowired
    private CustomerSysLogService customerSysLogService;

    @Operation(summary = "用户场站黑名单列表")
    @PostMapping(value = "/api/siteBlacklist/cusBlackSite")
    public ListResponse<SiteBlacklistVo> cusBlackSite(
            ServerHttpRequest request, @RequestParam("uid") Long uid,
            @RequestParam(value = "siteName", required = false) String siteName) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return this.siteBlacklistService.cusBlackSite(uid, siteName);
    }

    @Operation(summary = "场站黑名单列表查询")
    @PostMapping(value = "/api/siteBlacklist/find")
    public ListResponse<SiteBlacklistVo> find(
            ServerHttpRequest request, @RequestBody ListSiteBlacklistParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}", JsonUtils.toJsonString(param));

        param.setTopCommId(AntRestUtils.getTopCommId(request));

        return this.siteBlacklistService.findSiteBlacklist(param);
    }

    @Operation(summary = "将客户添加到场站黑名单，忽略phone入参")
    @PostMapping(value = "/api/siteBlacklist/addToBlacklist")
    public BaseResponse addToBlacklist(
            ServerHttpRequest request, @RequestBody SiteBlacklistEnableParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}", JsonUtils.toJsonString(param));

//        param.setTopCommId(AntRestUtils.getTopCommId(request));

        siteBlacklistService.addToBlacklist(param);

        BatchRmParam logParam = new BatchRmParam();
        logParam.setPhone(param.getPhone());
        logParam.setSiteNameList(List.of(param.getSiteName()));
        customerSysLogService.batchRmFromBlacklistLog(logParam, request);
        return RestUtils.success();
    }

    @Operation(summary = "将客户添加到场站黑名单，允许phone入参")
    @PostMapping(value = "/api/siteBlacklist/addToBlacklistPhone")
    public BaseResponse addToBlacklistPhone(
            ServerHttpRequest request, @RequestBody SiteBlacklistEnableParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}", JsonUtils.toJsonString(param));

//        param.setTopCommId(AntRestUtils.getTopCommId(request));

        BaseResponse baseResponse = siteBlacklistService.addToBlacklistPhone(param);
        FeignResponseValidate.check(baseResponse);

        BatchRmParam logParam = new BatchRmParam();
        logParam.setPhone(param.getPhone());
        logParam.setSiteNameList(List.of(param.getSiteName()));
        logParam.setCorpName(param.getCorpName());
        customerSysLogService.batchRmFromBlacklistLog(logParam, request);
        return RestUtils.success();
    }

    @Operation(summary = "将客户从场站黑名单中移除")
    @PostMapping(value = "/api/siteBlacklist/rmFromBlacklist")
    public BaseResponse rmFromBlacklist(
            ServerHttpRequest request, @RequestBody SiteBlacklistEnableParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}", JsonUtils.toJsonString(param));
        siteBlacklistService.rmFromBlacklist(param);

        BatchRmParam logParam = new BatchRmParam();
        logParam.setPhone(param.getPhone());
        logParam.setSiteNameList(List.of(param.getSiteName()));
        customerSysLogService.batchRmFromBlacklistLog(logParam, request);
        return RestUtils.success();
    }

    @Operation(summary = "批量将客户从场站黑名单中移除")
    @PostMapping(value = "/api/siteBlacklist/batchRmFromBlacklist")
    public BaseResponse batchRmFromBlacklist(
            ServerHttpRequest request, @RequestBody BatchRmParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}", param);
        siteBlacklistService.batchRmFromBlacklist(param.getIdList());
        customerSysLogService.batchRmFromBlacklistLog(param, request);
        return RestUtils.success();
    }

    @Operation(summary = "批量将客户从场站黑名单中移除")
    @PostMapping(value = "/api/siteBlacklist/removeBlacklist")
    public BaseResponse removeBlacklist(
            ServerHttpRequest request, @RequestBody RemoveSiteBlacklistParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}", param);
        siteBlacklistService.batchRmFromBlacklist(param.getList().stream()
                .map(SiteBlacklistVo::getId)
                .collect(Collectors.toList()));
        customerSysLogService.removeBlacklistBySite(param, request);
        return RestUtils.success();
    }
}
