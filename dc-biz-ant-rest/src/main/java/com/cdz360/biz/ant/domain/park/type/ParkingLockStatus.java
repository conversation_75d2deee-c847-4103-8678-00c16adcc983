package com.cdz360.biz.ant.domain.park.type;

import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum ParkingLockStatus {
    UNKNOWN("未知"),

    PLATFORM_OFFLINE("平台离线"),

    NORMAL("正常空闲"),

    OPEN_CAR_IN("有车开锁状态"),

    OPEN_NOT_CAR("无车开锁状态"),

    ERROR("故障"),

    CUT_POWER("被远程断电"),
    ;

    private String desc;

    ParkingLockStatus(String desc) {
        this.desc = desc;
    }

    // 安效停地锁状态转换
    private static final Map<Integer, ParkingLockStatus> ANNEFFI_MAP = new HashMap<>(){{
        put(0, NORMAL);
        put(1, OPEN_CAR_IN);
        put(2, OPEN_NOT_CAR);
        put(3, ERROR);
        put(4, CUT_POWER);
    }};

    public static ParkingLockStatus anneffiSwap(int status) {
        ParkingLockStatus lockStatus = ANNEFFI_MAP.get(status);
        return lockStatus != null ? lockStatus : ParkingLockStatus.UNKNOWN;
    }
}
