package com.cdz360.biz.ant.domain.bundle;

import com.cdz360.base.model.base.param.BaseListParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "查询升级记录参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListUpgradeLogParam extends BaseListParam {

    @Schema(description = "控制器编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String gwno;

    @Schema(description = "控制器编号列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> gwnoList;
}
