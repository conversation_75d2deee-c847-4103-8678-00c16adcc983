package com.cdz360.biz.ant.rest.site;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.EvseBizStatus;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.domain.request.ListChargerParam;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.service.site.SiteService;
import com.cdz360.biz.ant.service.sysLog.SiteSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.iot.vo.TransformerPlugsVo;
import com.cdz360.biz.model.site.type.SiteStatus;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.param.BoxListRequest;
import com.chargerlinkcar.framework.common.domain.vo.BoxInfoVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerInfoVo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;

/**
 * 站点、桩、枪头相关接口
 *
 * @ClassName SiteBoxChargerRest
 * <AUTHOR>
 * @Description
 * @Date 2019.4.25
 */
@Slf4j
@RequestMapping("/api/charger")
@RestController
public class SiteBoxChargerRest extends BaseController {

    @Autowired
    private SiteService siteService;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private SiteSysLogService siteSysLogService;


    @ApiOperation(value = "获取枪头列表信息")
    @PostMapping(value = "/getPagedChargerInfoListX")
    public ListResponse<ChargerInfoVo> getPagedChargerInfoListX(
        ServerWebExchange exh, ServerHttpRequest request, @RequestBody ListChargerParam param) {
        log.info("分页获取站点简单信息列表: {}", JsonUtils.toJsonString(param));
        return this.getPagedChargerInfoList(exh, request, param);
    }

    /**
     * 分页获取站点简单信息列表
     *
     * @return
     */
    @PostMapping(value = "/getPagedChargerInfoList", consumes = {
        MediaType.MULTIPART_FORM_DATA_VALUE,
        MediaType.APPLICATION_FORM_URLENCODED_VALUE
    })
    public ListResponse<ChargerInfoVo> getPagedChargerInfoList(
        ServerWebExchange exh,
        ServerHttpRequest request,
        @ModelAttribute ListChargerParam param) {
        log.info(LoggerHelper2.formatEnterLog(request) + "param = {}", param);

        //分页数据
        OldPageParam page = getPage2(request, exh, true);
        List<Long> commIdList = getCommIdList2(request);
        if (commIdList == null && commIdList.size() == 0) {
            //return new ObjectResponse<>("商户ID不能为空");
            throw new DcArgumentException("商户ID不能为空");
        }
        MultiValueMap<String, String> queryParams = request.getQueryParams();
        List<String> statusStringList = queryParams.get("statusList");
        List<Integer> statusList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(statusStringList)) {
            statusStringList.forEach(item -> {
                statusList.add(Integer.parseInt(item));
            });
        }

        List<String> gidList = CollectionUtils.isNotEmpty(param.getGidList()) ?
            param.getGidList() : AntRestUtils.getSysUserGids(request);
        return siteService.getPagedChargerInfoList(
            gidList,
            super.getCommIdChain2(request),
            param.getCommercialId(),
            param.getPlugNo(),
            param.getSiteId(),
            page,
            param.getConnectorId(),
            param.getSupplyType(),
            param.getSerialNumber(),
            null != param.getBizStatusList() ?
                param.getBizStatusList() : List.of(EvseBizStatus.NORMAL),  // 仅查询在正常运营状态的枪头
            param.getPlugStatus(),
            param.getJobName(),
            param.getByUpdateTime(),
            statusList);
    }

    @PostMapping(value = "/getPlugListByTransformerId")
    ListResponse<TransformerPlugsVo> getPlugListByTransformerId(
        ServerWebExchange exh,
        ServerHttpRequest request,
        @ModelAttribute ListChargerParam param) {
        log.info(LoggerHelper2.formatEnterLog(request) + "获取场站下变压器相关枪头 param = {}",
            param);

        List<Long> commIdList = getCommIdList2(request);
        if (commIdList == null && commIdList.size() == 0) {
            //return new ObjectResponse<>("商户ID不能为空");
            throw new DcArgumentException("商户ID不能为空");
        }

        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getSiteId()), "请传入场站id");

        return siteService.getPlugListByTransformerId(param.getSiteId(), param.getTransformerId());
    }


    /**
     * 设备列表查询
     */
    @PostMapping("/getPagedBoxSimpleList")
    public ListResponse<BoxInfoVo> getPagedBoxSimpleList(
        ServerHttpRequest request,
        @RequestBody BoxListRequest boxRequest) {
        log.info(
            LoggerHelper2.formatEnterLog(request, false) + " param = {}" + JsonUtils.toJsonString(
                boxRequest));

        return siteService.getPagedBoxSimpleList(boxRequest);
    }

    @PostMapping("/getTopologyFreeBoxSimpleList")
    public ListResponse<BoxInfoVo> getTopologyFreeBoxSimpleList(
        ServerHttpRequest request,
        @RequestBody BoxListRequest boxRequest) {
        log.info(
            LoggerHelper2.formatEnterLog(request, false) + " param = {}" + JsonUtils.toJsonString(
                boxRequest));

        return siteService.getTopologyFreeBoxSimpleList(boxRequest);
    }

    @Operation(summary = "更新桩运营状态")
    @GetMapping(value = "/updateBizStatus")
    public BaseResponse updateBizStatus(ServerHttpRequest request,
        @RequestParam("evseNo") String evseNo, @RequestParam("bizStatus") Long bizStatus) {
        log.info("更新桩运营状态。 evseNo = {}，bizStatus = {}", evseNo, bizStatus);
        IotAssert.isNotBlank(AntRestUtils.getToken2(request), "未登录状态");
        siteSysLogService.updateEvseBizStatusLog(evseNo, request);
        return siteService.updateBizStatus(evseNo, bizStatus);
    }
}
