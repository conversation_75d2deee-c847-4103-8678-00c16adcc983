package com.cdz360.biz.ant.domain;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * :
 * @since 2019-03-11 16:47
 */
@Data
public class ChargerOrderTimeDivision implements Serializable{

    protected static final long serialVersionUID = 1L;

    /**
     * 分时订单Id
     */
    private String timeDivisionOrderId;

    /**
     * 订单Id
     */
    private String orderId;

    /**
     * 分时订单开始时间
     */
    private String startTime;

    /**
     * 分时订单结束时间
     */
    private String stopTime;

    /**
     * 分时时长
     */
    private Integer duration;

    /**
     * 分时电量
     */
    private BigDecimal electric;

    /**
     * 分时电费
     */
    private BigDecimal electricPrice;

    /**
     * 分时服务费
     */
    private BigDecimal servicePrice;

    /**
     *分时订单费用
     */
    private BigDecimal orderPrice;

    /**
     * 计费标签（0, 未知; 1, 尖; 2, 峰; 3, 平; 4, 谷）
     */
    private Integer tag;

    /**
     * 计费模板Id
     */
    private Long templateId;

    /**
     * 计费模板此段开始时间（0-1440分钟数）
     */
    private Integer templateStartTime;

    /**
     * 计费模板此段结束时间（0-1440分钟数）
     */
    private Integer templateStopTime;

    /**
     * 计费方式 0：电量计费 1：时长计费
     */
    private Integer templateType;

    /**
     * 电费单价
     */
    private BigDecimal electricUnit;

    /**
     * 服务费单价
     */
    private BigDecimal serviceUnit;

    private BigDecimal electricUnit2;
}
