package com.cdz360.biz.ant.service.tj;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.feign.reactor.BizTjFeignClient;
import com.cdz360.biz.model.tj.kc.param.ListTjSurveyParam;
import com.cdz360.biz.model.tj.kc.param.RepeatSurveyParam;
import com.cdz360.biz.model.tj.kc.param.TjSurveyBiParam;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyBiVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyVo;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TjDailyCharingDurationService {

    @Autowired
    private BizTjFeignClient bizTjFeignClient;

    public Mono<ListResponse<TjSurveyVo>> findTjSurvey(ListTjSurveyParam param) {
        return bizTjFeignClient.findTjSurvey(param)
            .doOnNext(FeignResponseValidate::check)
            .map(res -> RestUtils.buildListResponse(res.getData().stream()
                .map(x -> {
                    val vo = new TjSurveyVo();
                    BeanUtils.copyProperties(x, vo);
                    return vo;
                }).collect(Collectors.toList()), res.getTotal()));
    }

    public Mono<ObjectResponse<TjSurveyBiVo>> tjSurveyBi(TjSurveyBiParam param) {
        return bizTjFeignClient.tjSurveyBi(param);
    }

    public Mono<ListResponse<TjSurveyVo>> repeatSurvey(RepeatSurveyParam param) {
        IotAssert.isNotBlank(param.getSiteName(), "场站名称不能为空");
        IotAssert.isNotBlank(param.getAddress(), "场站地址不能为空");
        IotAssert.isNotNull(param.getLongitude(), "场站位置经度不能为空");
        IotAssert.isNotNull(param.getLatitude(), "场站位置维度不能为空");
        return bizTjFeignClient.repeatSurvey(param);
    }

    public Mono<ObjectResponse<TjSurveyVo>> addTjSurvey(TjSurveyVo survey) {
        IotAssert.isNotNull(survey.getSiteName(), "场站名称不能为空");
        IotAssert.isNotNull(survey.getUid(), "勘察用户ID不能为空");
        IotAssert.isNotNull(survey.getLongitude(), "场站位置经度不能为空");
        IotAssert.isNotNull(survey.getLatitude(), "场站位置维度不能为空");
//        IotAssert.isNotNull(survey.getProvince(), "场站所属省编码不能为空");
//        IotAssert.isNotNull(survey.getCity(), "场站所属市编码不能为空");
//        IotAssert.isNotNull(survey.getArea(), "场站所属区编码不能为空");
        IotAssert.isNotNull(survey.getAddress(), "场站地址不能为空");
        IotAssert.isNotNull(survey.getScore(), "初评结果不能为空");
        return bizTjFeignClient.saveTjSurvey(survey);
    }

    public Mono<ObjectResponse<TjSurveyVo>> editTjSurvey(TjSurveyVo survey) {
        IotAssert.isNotNull(survey.getNo(), "勘察编号不能为空");
        IotAssert.isNotNull(survey.getSiteName(), "场站名称不能为空");
        IotAssert.isNotNull(survey.getUid(), "勘察用户ID不能为空");
        IotAssert.isNotNull(survey.getLongitude(), "场站位置经度不能为空");
        IotAssert.isNotNull(survey.getLatitude(), "场站位置维度不能为空");
//        IotAssert.isNotNull(survey.getProvince(), "场站所属省编码不能为空");
//        IotAssert.isNotNull(survey.getCity(), "场站所属市编码不能为空");
//        IotAssert.isNotNull(survey.getArea(), "场站所属区编码不能为空");
        IotAssert.isNotNull(survey.getAddress(), "场站地址不能为空");
        IotAssert.isNotNull(survey.getScore(), "初评结果不能为空");
        return bizTjFeignClient.saveTjSurvey(survey);
    }
}
