package com.cdz360.biz.ant.service.iot;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.IotWorkerFeignClient;
import com.cdz360.biz.model.trading.iot.dto.EvseModuleDto;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class IotEvseModuleService {

    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;
    @Autowired
    private IotWorkerFeignClient iotWorkerFeignClient;

    public Mono<ObjectResponse<EvseModuleDto>> getEvseModuleList(String evseNo) {
        if (StringUtils.isBlank(evseNo)) {
            throw new DcArgumentException("桩编号不能为空");
        }

        return Mono.just(evseNo)
            .map(iotWorkerFeignClient::moduleQuery)
            .doOnNext(FeignResponseValidate::check)
            .map(res -> iotDeviceMgmFeignClient.getEvseModuleList(evseNo));
    }
}
