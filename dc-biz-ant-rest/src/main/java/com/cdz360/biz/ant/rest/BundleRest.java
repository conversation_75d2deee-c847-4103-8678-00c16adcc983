package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.param.SortParam;
import com.cdz360.base.model.base.type.OrderType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.biz.ant.domain.bundle.EvseBundleDto;
import com.cdz360.biz.ant.domain.bundle.EvseBundleListRequest;
import com.cdz360.biz.ant.domain.bundle.EvseBundleParam;
import com.cdz360.biz.ant.domain.bundle.ListUpgradeLogParam;
import com.cdz360.biz.ant.domain.bundle.UpgradeStatusParam;
import com.cdz360.biz.ant.domain.bundle.UploadBundleParam;
import com.cdz360.biz.ant.feign.DeviceMgmFeignClient;
import com.cdz360.biz.ant.feign.reactor.DeviceBundleFeignClient;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.ant.utils.FileUtil;
import com.cdz360.biz.model.upgradepg.vo.UpgradeLogVo;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

// 下面接口从 monitor-device 中迁移过来的
@Tag(name = "升级包相关接口", description = "升级包")
@Slf4j
@RestController
public class BundleRest {


    @Autowired
    private DeviceBundleFeignClient deviceBundleFeignClient;

    @Autowired
    private DeviceMgmFeignClient deviceMgmFeignClient;

    /**
     *  分页查询升级包列表
     * <AUTHOR>
     * @since  16:44 2019/9/16
     */
    @Operation(summary = "升级包分页查询")
    @PostMapping(value = "/api/evsebundle/page")
    public ListResponse<EvseBundleDto> evseBundlePage(@RequestBody EvseBundleListRequest request) {
        log.info("【桩升级包】分页查询开始。request = {}", request);
        Assert.notNull(request, "请求参数不能为空");
        EvseBundleParam evseBundleParam = this.convertEvseBundleParam(request, true);
        ListResponse<EvseBundleDto> evseBundleDtoRes = deviceMgmFeignClient.evseBundlePage(
            evseBundleParam);
        log.info("【桩升级包】分页查询结束。list.size = {}",
            evseBundleDtoRes.getData() == null ? "null" : evseBundleDtoRes.getData().size());
        return evseBundleDtoRes;
    }

    @Operation(summary = "(控制器/桩)升级包上传")
    @PostMapping(value = "/api/evsebundle/upload")
    public Mono<ObjectResponse<Long>> uploadBunble(
        ServerHttpRequest request,
        @RequestHeader("Content-Length") long contentLength,
//            @Parameter(name = "桩升级包文件(ZIP格式)") @RequestParam("file") MultipartFile file,
        @ModelAttribute UploadBundleParam bundleParam) {
        log.info("升级包上传: {}, len = {}, param = {}", LoggerHelper2.formatEnterLog(request),
            contentLength, bundleParam);
        bundleParam.preCheck();

        Long opUid = AntRestUtils.getSysUid(request);
        String opName = AntRestUtils.getSysUserName(request);

        return FileUtil.filePartToMultipartFile(bundleParam.getFile())
            .flatMap(multipartFile -> Mono.just(deviceMgmFeignClient.uploadEvseBundle(
                multipartFile,
                bundleParam.getType(),
                bundleParam.getVendor(),
                bundleParam.getSwVerCode(),
                opUid,
                opName)))
            ;
    }

    /**
     *  删除升级包
     * <AUTHOR>
     * @since  16:44 2019/9/16
     */
    @Operation(summary = "桩升级包删除")
    @GetMapping(value = "/api/evsebundle/delete/{id}")
    public BaseResponse deleteEvseBundle(
        @Parameter(name = "升级包主键ID") @PathVariable("id") Long id) {
        log.info("【桩升级包】删除开始。id = {}", id);
        Assert.notNull(id, "主键ID不能为空");
        BaseResponse baseResponse = deviceMgmFeignClient.deleteEvseBundle(id);
        log.info("【桩升级包】删除结束。");
        return baseResponse;
    }

    @Operation(summary = "桩升级包-修改状态")
    @PostMapping(value = "/api/evsebundle/changeStatus")
    public BaseResponse changeStatus(ServerHttpRequest request,
        @RequestBody UpgradeStatusParam param) {
        param.setOpId(AntRestUtils.getSysUid(request));
        param.setOpName(AntRestUtils.getSysUserName(request));
        return deviceMgmFeignClient.changeStatus(param);
    }

    /**
     *  升级包列表
     * <AUTHOR>
     * @since  16:44 2019/9/16
     */
    @Operation(summary = "升级包列表查询,获取这些桩可用的升级包")
    @PostMapping(value = "/api/evsebundle/list")
    public ListResponse<EvseBundleDto> evseBundleList(@RequestBody EvseBundleListRequest request) {
        log.info("【桩升级包】列表查询开始: {}", request);
        Assert.notNull(request, "请求参数不能为空");
        EvseBundleParam evseBundleParam = this.convertEvseBundleParam(request, false);
        evseBundleParam.setStatusList(List.of(1)); // 只展示状态正常的升级包

        ListResponse<EvseBundleDto> evseBundleRes = deviceMgmFeignClient.evseBundlePage(
            evseBundleParam);
        log.info("【桩升级包】分页查询结束。list.size = {}",
            evseBundleRes.getData() == null ? "null" : evseBundleRes.getData().size());
        IotAssert.isTrue(CollectionUtils.isNotEmpty(evseBundleRes.getData()), "无可用升级包");
        return evseBundleRes;
    }

    @Operation(summary = "获取微网控制器升级记录")
    @PostMapping(value = "/api/bundle/mgc/upgradeLogList")
    public Mono<ListResponse<UpgradeLogVo>> mgcUpgradeLogList(
        ServerHttpRequest request,
        @RequestBody ListUpgradeLogParam param) {
        log.info("获取微网控制器升级记录: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), param);
        return deviceBundleFeignClient.mgcUpgradeLogList(param);
    }

    private EvseBundleParam convertEvseBundleParam(EvseBundleListRequest request,
        Boolean pageFlag) {
        Integer rows = request.getRows();
        Integer page = request.getPage();
        String keywords = request.getKeywords();
        Integer start = rows * (page - 1);
        EvseBundleParam evseBundleParam = new EvseBundleParam();
        evseBundleParam.setPageFlag(pageFlag)
            .setEvseNoList(request.getEvseIds())
            .setTypeList(request.getTypeList())
            .setStatusList(request.getStatusList())
            .setSize(rows)
            .setStart(start.longValue())
            .setSk(keywords)
            .setEnable(true);

        if (NumberUtils.equals(1, request.getSortType())) {

            SortParam sort = new SortParam();
            sort.setColumns(List.of("createTime"))
                .setOrder(OrderType.desc);
            evseBundleParam.setSorts(List.of(sort));
        } else if (NumberUtils.equals(2, request.getSortType())) {

            SortParam sort = new SortParam();
            sort.setColumns(List.of("pcType"))
                .setOrder(OrderType.asc);
            evseBundleParam.setSorts(List.of(sort));
        }
        return evseBundleParam;
    }
}
