package com.cdz360.biz.ant.service.srs;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.domain.vo.RadiationSampling;
import com.cdz360.biz.model.iot.param.ListCtrlParam;
import com.cdz360.biz.model.trading.iot.po.SrsPo;
import com.cdz360.biz.model.trading.iot.vo.DeviceInfoVo;
import com.cdz360.biz.model.trading.iot.vo.RedisSrsRtData;
import com.cdz360.biz.utils.feign.iot.SrsFeignClient;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class SrsDeviceService {

    @Autowired
    private SrsRtDataService srsRtDataService;
    @Autowired
    private SrsFeignClient srsFeignClient;

    public Mono<ListResponse<SrsPo>> findSrsList(ListCtrlParam param) {
        return srsFeignClient.findSrsList(param);
    }

    public Mono<ListResponse<RadiationSampling>> srCurve(String siteId, Date date) {
        IotAssert.isTrue(StringUtils.isNotBlank(siteId), "siteId不能为空");

        final int dis = 1; // 分钟

        return Mono.just(siteId)
            .flatMap(e -> srsFeignClient.findSrsVoList(List.of(e)))
            .doOnNext(FeignResponseValidate::check)
            .map(res -> {
                List<String> gwnoList = new ArrayList<>();
                res.getData().stream().filter(
                        e -> siteId.equals(e.getSiteId()) && CollectionUtils.isNotEmpty(
                            e.getGwInfoList()))
                    .map(DeviceInfoVo::getGwInfoList)
                    .forEach(e -> {
                        e.forEach(t -> {
                            if (StringUtils.isNotBlank(t.getGwno())) {
                                gwnoList.add(t.getGwno());
                            }
                        });
                    });
                return gwnoList;
            })
            .filter(CollectionUtils::isNotEmpty)
            .flatMap(e -> {
                return srsRtDataService.dayRadiationSampling(e, DateUtil.dateToLocalDate(date), dis)
                    .collectList();
            })
            .switchIfEmpty(Mono.just("站点下无微网控制器，返回空数据").map(i -> {
                log.info(i);
                return List.of();
            }))
            .map(RestUtils::buildListResponse);
    }

    public Mono<ObjectResponse<RedisSrsRtData>> srsInfoInTime(String dno) {
        return srsFeignClient.srsInfoInTime(dno);
    }

}