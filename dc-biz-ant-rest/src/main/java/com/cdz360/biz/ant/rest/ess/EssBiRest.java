package com.cdz360.biz.ant.rest.ess;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.domain.dto.BatteryPackEx;
import com.cdz360.biz.ant.domain.dto.MgcAlert;
import com.cdz360.biz.ant.service.ess.EssEquipRtDataService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.ess.model.data.param.DataBiParam;
import com.cdz360.biz.ess.model.data.param.DayKwhParam;
import com.cdz360.biz.ess.model.data.vo.EquipSampleData;
import com.cdz360.biz.model.ess.vo.RedisEquipRtData;
import com.cdz360.biz.model.iot.param.SiteBiParam;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.ess.param.EssEquipDetailParam;
import com.cdz360.biz.model.trading.ess.param.EssEquipDetailParamEx;
import com.cdz360.biz.model.trading.ess.param.MgcDetailParam;
import com.cdz360.biz.model.trading.ess.vo.DayEssDataBi;
import com.cdz360.biz.model.trading.ess.vo.EmsSampleData;
import com.cdz360.biz.model.trading.ess.vo.EssEquipDetailVo;
import com.cdz360.biz.model.trading.ess.vo.EssStatusBi;
import com.cdz360.biz.model.trading.ess.vo.PcsSampleData;
import com.cdz360.biz.model.trading.ess.vo.TotalEssDataBi;
import com.cdz360.biz.utils.feign.auth.AuthCommFeignClient;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "储能相关接口", description = "储能服务")
@RequestMapping("/api/ess/bi")
public class EssBiRest {

    @Autowired
    private EssEquipRtDataService essEquipRtDataService;

    @Autowired
    private AuthCommFeignClient authCommFeignClient;

    @Operation(summary = "近7日放电统计数据", description = "站点详情")
    @GetMapping(value = "/outBi7")
    public Mono<ListResponse<DayEssDataBi>> essOutBi7(
        ServerHttpRequest request,
        @RequestParam(value = "siteId") String siteId) {
        log.info("储能近七天放电数据统计: {}", LoggerHelper2.formatEnterLog(request));
        return this.essEquipRtDataService.essOutBi7(siteId);
    }

    @Operation(summary = "场站储能当天充放电功率")
    @GetMapping(value = "/dateInOutPower")
    public Mono<ListResponse<Object>> siteDateInOutPower(
        ServerHttpRequest request,
        @RequestParam(value = "siteId") String siteId) {
        log.info("场站储能当天充放电功率: {}", LoggerHelper2.formatEnterLog(request));
        return this.essEquipRtDataService.siteDateInOutPower(siteId);
    }

    @Operation(summary = "场站储能数据查询", description = "场站各天汇总数据")
    @PostMapping(value = "/siteEssBi")
    public Mono<ListResponse<DayEssDataBi>> siteEssBi(
        ServerHttpRequest request, @RequestBody SiteBiParam param) {
        log.info("场站储能数据查询: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        param.setCommIdChain(AntRestUtils.getCommIdChain(request));
        return this.essEquipRtDataService.siteEssBi(param);
    }

    @Operation(summary = "场站各天汇总数据到Excel", description = "场站各天汇总数据到Excel")
    @PostMapping(value = "/exportSiteEssBi")
    public Mono<ObjectResponse<ExcelPosition>> exportSiteEssBi(
        ServerHttpRequest request, @RequestBody SiteBiParam param) {
        log.info("场站各天汇总数据到Excel: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        param.setCommIdChain(AntRestUtils.getCommIdChain(request));
        return this.essEquipRtDataService.exportSiteEssBi(request, param);
    }

    @Operation(summary = "商户近30日储能统计数据", description = "商户首页")
    @GetMapping(value = "/inOutBi30")
    public Mono<ListResponse<DayEssDataBi>> essInOutBi30(
        ServerHttpRequest request,
        @Parameter(name = "场站ID") @RequestParam(value = "siteId", required = false) String siteId,
        @RequestParam(value = "recentDays", defaultValue = "30") Integer recentDays,
        @RequestParam(value = "commId", required = false) Long commId) {
        log.info("商户近30日储能统计数据: {}", LoggerHelper2.formatEnterLog(request));
        String commIdChain = null;
        if (null != commId) {
            ObjectResponse<Commercial> commercial = authCommFeignClient.getCommercial(commId)
                .block(Duration.ofSeconds(50L));
            FeignResponseValidate.check(commercial);
            commIdChain = commercial.getData().getIdChain();
        } else {
            commIdChain = AntRestUtils.getCommIdChainAndFilter(request);
        }
        return this.essEquipRtDataService.essInOutBi30(
            commIdChain, siteId, recentDays);
    }


    @Operation(summary = "储能设备运行数据采样")
    @PostMapping(value = "/equipRtDataSample")
    public Mono<ListResponse<EquipSampleData>> equipRtDataSample(
        ServerHttpRequest request, @RequestBody DataBiParam param) {
        log.info("储能设备运行数据采样: param = {}", JsonUtils.toJsonString(param));
        param.setCommIdChain(AntRestUtils.getCommIdChainAndFilter(request));
        return this.essEquipRtDataService.equipRtDataSample(param);
    }

    @Operation(summary = "商户储能站数据汇总", description = "商户首页")
    @GetMapping(value = "/totalBi")
    public Mono<ObjectResponse<TotalEssDataBi>> essTotalBi(
        ServerHttpRequest request,
        @RequestParam(value = "siteId", required = false) String siteId,
        @RequestParam(value = "commId", required = false) Long commId) {
        log.info("商户储能站数据汇总: {}", LoggerHelper2.formatEnterLog(request, true));
        DayKwhParam param = new DayKwhParam();

        if (null != commId) {
            ObjectResponse<Commercial> commercial = authCommFeignClient.getCommercial(commId)
                .block(Duration.ofSeconds(50L));
            FeignResponseValidate.check(commercial);
            param.setCommIdChain(commercial.getData().getIdChain());
        } else {
            param.setCommIdChain(AntRestUtils.getCommIdChainAndFilter(request));
        }

        if (StringUtils.isNotBlank(siteId)) {
            param.setSiteIdList(List.of(siteId));
        }

        return this.essEquipRtDataService.essTotalBi(param)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "电池堆近24小时储能统计数据", description = "商户首页")
    @PostMapping(value = "/detailBi24h")
    public Mono<ListResponse<EssEquipDetailVo>> essPcsDetailBi24h(
        ServerHttpRequest request,
        @RequestBody EssEquipDetailParam param) {
        log.info("储能近七天放电数据统计: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        return this.essEquipRtDataService.essPcsDetailBi24h(param)
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "电池堆近24小时储能统计数据(专用于大屏)", description = "商户首页")
    @PostMapping(value = "/detailBi24hUseForOp")
    public Mono<ListResponse<EssEquipDetailVo>> detailBi24hUseForOp(
        ServerHttpRequest request,
        @RequestBody EssEquipDetailParam param) {
        log.info("detailBi24hUseForOp: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        return this.essEquipRtDataService.detailBi24hUseForOp(param)
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "储能PCS近24小时功率采样")
    @PostMapping(value = "/emsSample24h")
    public Mono<ListResponse<EmsSampleData>> emsSample24h(
        ServerHttpRequest request,
        @RequestBody EssEquipDetailParam param) {
        log.info("储能EMS近24小时功率采样: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return this.essEquipRtDataService.emsSample24h(param)
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "储能PCS近24小时功率采样")
    @PostMapping(value = "/pcsSample24h")
    public Mono<ListResponse<PcsSampleData>> pcsSample24h(
        ServerHttpRequest request,
        @RequestBody EssEquipDetailParam param) {
        log.info("储能PCS近24小时功率采样: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return this.essEquipRtDataService.pcsSample24h(param)
            .map(RestUtils::buildListResponse);
    }

    @GetMapping(value = "/essPowerBi")
    public Mono<ListResponse<EssEquipDetailVo>> essPowerBi(ServerHttpRequest request,
        @RequestParam(value = "essDno") String essDno,
        @RequestParam(value = "date")
        @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        log.info("essPowerBi: {}, essDno = {}, date = {}",
            LoggerHelper2.formatEnterLog(request), essDno, date);
        return this.essEquipRtDataService.essPowerBi(essDno, date)
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "电池组指标数据采样", description = "电池组指标")
    @PostMapping(value = "/batteryClusterDataSampling")
    public Mono<ListResponse<BatteryPackEx>> batteryClusterDataSampling(
        ServerHttpRequest request,
        @RequestBody EssEquipDetailParamEx param) {
        log.info("电池组指标数据采样: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));

        return this.essEquipRtDataService.batteryClusterDataSampling(param)
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "微网控制器健康数据采样", description = "微网控制器")
    @PostMapping(value = "/mgcAlertDataSampling")
    public Mono<ListResponse<RedisEquipRtData<MgcAlert>>> mgcAlertDataSampling(
        ServerHttpRequest request,
        @RequestBody MgcDetailParam param) {
        log.info("微网控制器健康数据采样: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));

        return this.essEquipRtDataService.mgcAlertDataSampling(param)
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "获取储能状态统计数据", description = "定制大屏")
    @PostMapping(value = "/getEssStatusBi")
    public Mono<ListResponse<EssStatusBi>> getEssStatusBi(ServerHttpRequest request,
        @RequestParam(value = "commIdChain", required = false) String commIdChain,
        @RequestParam(value = "siteId", required = false) String siteId) {
        log.info("获取储能状态统计数据: {}", LoggerHelper2.formatEnterLog(request));
        return this.essEquipRtDataService.getEssStatusBi(
            AntRestUtils.getCommIdChainAndFilter(request), siteId);
    }
}
