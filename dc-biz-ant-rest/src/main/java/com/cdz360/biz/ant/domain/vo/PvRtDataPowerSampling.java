package com.cdz360.biz.ant.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "功率采样点")
@Data
@Accessors(chain = true)
public class PvRtDataPowerSampling {

    @Schema(description = "采样点时间", example = "01:00")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String time;

    @Schema(description = "采样点输出功率(单位: W)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long outPower;
}
