package com.cdz360.biz.ant.service.ess;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.domain.dto.BatteryStack;
import com.cdz360.biz.ant.domain.dto.Ems;
import com.cdz360.biz.ant.domain.dto.Pcs;
import com.cdz360.biz.model.ess.dto.LineEssRtData;
import com.cdz360.biz.model.ess.vo.RedisEquipRtData;
import com.cdz360.biz.model.trading.ess.vo.EmsSampleData;
import com.cdz360.biz.model.trading.ess.vo.EssEquipDetailVo;
import com.cdz360.biz.model.trading.ess.vo.PcsSampleData;
import com.cdz360.biz.model.trading.ess.vo.SampleBase;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple3;
import reactor.util.function.Tuples;

@Slf4j
@Service
public class RedisEssEquipRtDataService {

    private static final DateTimeFormatter TIME_POINT_FORMATTER = DateTimeFormatter.ofPattern(
        "HH:mm");
    private final static DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(
        "yyyyMMdd HH:mm:ss");

    private static final String PRE_REDIS_KEY = "ess:";
    private static final String PRE_MGC_REDIS_KEY = "mgc:";

//    private static final Map<EssEquipType, CollectFunc<SampleBase>> COLLECT_FUNC_MAP = new HashMap<>();
//    private static final Map<EssEquipType, ConvertFunc<SampleBase, RedisEquipRtData<Object>>> CONVERT_FUNC_MAP = new HashMap<>();
//
//    static {
//        CONVERT_FUNC_MAP.put(EssEquipType.PCS, data -> {
//            PcsSampleData item = new PcsSampleData();
//            item.setTime(data.getTime());
//            Pcs pcs = (Pcs) data.getData();
//            return item.setDcPower(pcs.getDcPower())
//                    .setDcVoltage(pcs.getDcVoltage());
//        });
//        CONVERT_FUNC_MAP.put(EssEquipType.EMS, data -> {
//            EmsSampleData item = new EmsSampleData();
//            item.setTime(data.getTime());
//            Ems ems = (Ems) data.getData();
//            return item.setSoc(ems.getSoc());
//        });
//
//
//        COLLECT_FUNC_MAP.put(EssEquipType.PCS, (collect, time, item) -> {
//            if (null == item) {
//                item = new PcsSampleData();
//                item.setTime(time);
//                ((PcsSampleData) item).setDcPower(BigDecimal.ZERO);
//                ((PcsSampleData) item).setDcVoltage(BigDecimal.ZERO);
//            }
//
//            if (null == collect) collect = new ArrayList<>();
//
//            collect.add(item);
//        });
//        COLLECT_FUNC_MAP.put(EssEquipType.EMS, (collect, time, item) -> {
//            if (null == item) {
//                item = new EmsSampleData();
//                item.setTime(time);
//                ((EmsSampleData) item).setSoc(BigDecimal.ZERO);
//            }
//
//            if (null == collect) collect = new ArrayList<>();
//
//            collect.add(item);
//        });
//    }


    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * ESS挂载设备运行时数据压入
     *
     * @param essDno  ESS设备编号
     * @param equipId 设备ID
     * @param data    数据
     */
    public void pushRtData(String essDno, Long equipId, JsonNode data) {
        ObjectNode objectNode = data.deepCopy();
        objectNode.put("time", DATE_TIME_FORMATTER.format(LocalDateTime.now()));
        String key = formatKey(essDno, equipId, LocalDate.now());

        redisTemplate.opsForList()
            .rightPush(key, JsonUtils.toJsonString(objectNode));
        redisTemplate.expire(key, 2, TimeUnit.DAYS); // 近24h可用
    }

    /**
     * 获取最近一条运行时数据
     *
     * @param essDno  ESS设备编号
     * @param equipId 设备ID
     * @return
     */
    public JsonNode latestRtData(String essDno, Long equipId) {
        return this.dateRtData(essDno, equipId, LocalDate.now());
    }

    /**
     * 获取最近一条运行时数据
     *
     * @param essDno  ESS设备编号
     * @param equipId 设备ID
     * @param date    日期
     * @return
     */
    public JsonNode dateRtData(String essDno, Long equipId, LocalDate date) {
        String key = formatKey(essDno, equipId, date);
        return this.dateRtData(key);
    }

    /**
     * 获取最近一条运行时数据
     *
     * @param key redis中key
     * @return
     */
    public JsonNode dateRtData(String key) {
        Long size = redisTemplate.opsForList().size(key);
        if (null == size || size == 0) {
            return null;
        }

        String value = redisTemplate.opsForList()
            .index(key, size - 1);
        if (StringUtils.isBlank(value)) {
            return null;
        }
        return JsonUtils.fromJson(value);
    }

    private static String formatKey(String essDno, Long equipId, LocalDate date) {
        return PRE_REDIS_KEY + essDno + ":" + equipId + ":" + date.format(DATE_FORMATTER);
    }

    public static String formatEssKey(String essDno, Long equipId, LocalDateTime dateTime) {
        return PRE_REDIS_KEY + essDno + ":" + equipId + ":" + dateTime.toLocalDate()
            .format(DATE_FORMATTER);
    }

    public static String formatMgcKey(String gwno, LocalDateTime dateTime) {
        return PRE_MGC_REDIS_KEY + gwno + ":" + dateTime.toLocalDate().format(DATE_FORMATTER);
    }

    /**
     * 获取PCS运行时数据
     *
     * @param start
     * @param end
     * @return
     */
    public List<RedisEquipRtData<Pcs>> findPcsRtData(String key, long start, long end) {
        Long size = redisTemplate.opsForList().size(key);
        if (null == size) {
            return List.of();
        }

        if (end > size) {
            end = -1;
        }

        List<String> range = redisTemplate.opsForList()
            .range(key, start, end);
        if (CollectionUtils.isEmpty(range)) {
            return List.of();
        }
        return range.stream()
            .map(value -> JsonUtils.fromJson(value, new TypeReference<RedisEquipRtData<Pcs>>() {
            }))
            .collect(Collectors.toList());
    }

    /**
     * 获取电池堆运行时数据
     *
     * @param start
     * @param end
     * @return
     */
    public List<RedisEquipRtData<BatteryStack>> findBatteryStackRtData(String key, long start,
        long end) {
        Long size = redisTemplate.opsForList().size(key);
        if (null == size) {
            return List.of();
        }

        if (end > size) {
            end = -1;
        }

        List<String> range = redisTemplate.opsForList()
            .range(key, start, end);
        if (CollectionUtils.isEmpty(range)) {
            return List.of();
        }
        return range.stream()
            .map(value -> JsonUtils.fromJson(value,
                new TypeReference<RedisEquipRtData<BatteryStack>>() {
                }))
            .collect(Collectors.toList());
    }

    /**
     * 获取电池簇运行时数据
     *
     * @param start
     * @param end
     * @return
     */
    public List<RedisEquipRtData<JsonNode>> findBatteryClusterRtData(String key, long start,
        long end) {
        Long size = redisTemplate.opsForList().size(key);
        if (null == size) {
            return List.of();
        }

        if (end > size) {
            end = -1;
        }

        List<String> range = redisTemplate.opsForList()
            .range(key, start, end);
        if (CollectionUtils.isEmpty(range)) {
            return List.of();
        }
        return range.stream()
            .map(
                value -> JsonUtils.fromJson(value, new TypeReference<RedisEquipRtData<JsonNode>>() {
                }))
            .collect(Collectors.toList());
    }

    /**
     * 电池堆24h数据采样
     *
     * @param essDno  ESS设备编号
     * @param equipId 设备ID
     * @param dis     时间间隔，单位: 分钟
     * @return
     */
    public Mono<List<EssEquipDetailVo>> batteryStackDataSampling24h(String essDno, Long equipId,
        int dis) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime start;
        LocalDateTime mid = LocalDate.now().atStartOfDay();
        if (now.getMinute() > 20) {
            start = now.minusHours(23);
        } else {
            start = now.minusHours(24);
        }
        start = start.withMinute(0);
        start = start.withSecond(0);

        List<LocalDateTime> yesterday = splitDay(start, mid, dis);
        List<LocalDateTime> today = splitDay(mid, now.plusHours(1).withMinute(0), dis);

        return Flux.concat(
                this.batteryStackDataSampling24h(essDno, equipId, start.toLocalDate(), yesterday),
                this.batteryStackDataSampling24h(essDno, equipId, now.toLocalDate(), today))
            .sort(Comparator.comparing(EssEquipDetailVo::getTime))
            .collectList();
    }

    private Flux<EssEquipDetailVo> batteryStackDataSampling24h(
        String essDno, Long equipId, LocalDate date, List<LocalDateTime> times) {
        return this.batteryStackDataSampling24h(formatKey(essDno, equipId, date), times);
    }

    private Flux<EssEquipDetailVo> batteryStackDataSampling24h(String key,
        List<LocalDateTime> times) {
        List<EssEquipDetailVo> samplingList = new ArrayList<>();
        try {
            boolean hasLine = true;
            RedisEquipRtData<BatteryStack> last = null;
            int start = 0;
            final int size = 100;

            List<RedisEquipRtData<BatteryStack>> dataList = this.findBatteryStackRtData(key, start,
                size);
            if (org.springframework.util.CollectionUtils.isEmpty(dataList)) {
                hasLine = false;
            }

            int dataSize = dataList.size();
            int idx = 0;
            for (LocalDateTime time : times) {
                if (null != last) {
                    LocalDateTime temp = last.getTime()
                        .withSecond(0)
                        .withNano(0);
                    if (time.isBefore(temp)) {
                        addBatteryStackRepeatSampling(samplingList, time, last);
                        continue;
                    }
                }

                if (hasLine) {
                    while (true) {
                        if (idx == dataSize) {
                            start = start + size;
                            dataList = this.findBatteryStackRtData(key, start, start + 100);
                            if (CollectionUtils.isEmpty(dataList)) {
                                hasLine = false;
                                break;
                            }

                            dataSize = dataList.size();
                            idx = 0;
                        }

                        RedisEquipRtData<BatteryStack> rtData = dataList.get(idx);
                        if (null == rtData || rtData.getTime() == null
                            || rtData.getData() == null) {
                            continue;
                        }

                        LocalDateTime temp = rtData.getTime()
                            .withSecond(0)
                            .withNano(0);

                        if (temp.isBefore(time)) {
                            idx = idx + 1;
                            continue;
                        } else if (temp.isEqual(time)) {
                            samplingList.add(new EssEquipDetailVo()
                                .setTime(time)
                                .setSoc(rtData.getData().getSoc())
                                .setMaxBatteryTemp(rtData.getData().getMaxBatteryTemperature())
                                .setTotalBatteryV(rtData.getData().getBatteryVoltageTotal()));
                            idx = idx + 1;
                            last = rtData;
                        } else if (temp.isAfter(time)) {
                            if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(samplingList)
                                && samplingList.get(samplingList.size() - 1).getSoc() != null
                                && DecimalUtils.gtZero(
                                samplingList.get(samplingList.size() - 1).getSoc())) {
                                samplingList.add(new EssEquipDetailVo()
                                    .setTime(time)
                                    .setSoc(calcAverageBS(last, 1, rtData.getData().getSoc()))
                                    .setMaxBatteryTemp(calcAverageBS(last, 2,
                                        rtData.getData().getMaxBatteryTemperature()))
                                    .setTotalBatteryV(calcAverageBS(last, 3,
                                        rtData.getData().getBatteryVoltageTotal())));
                            } else {
                                addBatteryStackZeroSampling(samplingList, time);
                            }
                        } else {
                            idx = idx + 1;
                        }
                        rtData = null;
                        break;
                    }
                }

                if (!hasLine) {
                    addBatteryStackZeroSampling(samplingList, time);
                }
            }
        } catch (Exception e) {
            // nothing
            log.error("err = {}", e.getMessage(), e);
        }
        return Flux.fromIterable(samplingList);
    }

    public static BigDecimal calcAverageBS(RedisEquipRtData<BatteryStack> last, int type,
        BigDecimal currValue) {
        BigDecimal lastV = BigDecimal.ZERO;
        if (last != null && last.getData() != null) {
            switch (type) {
                case 1:
                    lastV = last.getData().getSoc();
                    break;
                case 2:
                    lastV = last.getData().getMaxBatteryTemperature();
                    break;
                case 3:
                    lastV = last.getData().getBatteryVoltageTotal();
                    break;
            }
        }
        return DecimalUtils.add(lastV, currValue)
            .divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
    }

    /**
     * 采样：SOC、总交流有功功率、总交流无功功率、总交流视在功率
     *
     * @param essDno              ESS设备编号
     * @param pcsEquipId          设备ID
     * @param batteryStackEquipId 设备ID
     * @param today               时间间隔，单位: 分钟
     * @return
     */
    public Mono<List<EssEquipDetailVo>> essPowerBiToday(String essDno, List<LocalDateTime> today,
        @Nullable Long pcsEquipId,
        @Nullable Long batteryStackEquipId) {

        return Mono.zip(this.pcsDataSampling24h(essDno, pcsEquipId, LocalDate.now(), today)
                    .collectList(),
                this.batteryStackDataSampling24h(essDno, batteryStackEquipId, LocalDate.now(), today)
                    .collectList())
            .map(e -> {
                if (CollectionUtils.isEmpty(e.getT1()) && CollectionUtils.isEmpty(e.getT2())) {
                    return List.of();
                }
                Map<LocalDateTime, EssEquipDetailVo> pcsMap = new HashMap<>();
                Map<LocalDateTime, EssEquipDetailVo> batteryStackMap = new HashMap<>();
                List<EssEquipDetailVo> res = new ArrayList<>();

                if (CollectionUtils.isNotEmpty(e.getT1())) {
                    pcsMap = e.getT1().stream()
                        .collect(Collectors.toMap(EssEquipDetailVo::getTime, o -> o));
                }
                if (CollectionUtils.isNotEmpty(e.getT2())) {
                    batteryStackMap = e.getT2().stream()
                        .collect(Collectors.toMap(EssEquipDetailVo::getTime, o -> o));
                }
                for (LocalDateTime time : pcsMap.keySet()) {
                    EssEquipDetailVo pcsData = pcsMap.get(time);
                    EssEquipDetailVo batteryStackData = batteryStackMap.get(time);
                    res.add(new EssEquipDetailVo()
                        .setTime(time)
                        .setSoc(
                            batteryStackData != null ? batteryStackData.getSoc() : BigDecimal.ZERO)
                        .setAcApparentPowerTotal(pcsData.getAcApparentPowerTotal())
                        .setAcReactivePowerTotal(pcsData.getAcReactivePowerTotal())
                        .setAcActivePowerTotal(pcsData.getAcActivePowerTotal()));
                }
                return res.stream()
                    .sorted(Comparator.comparing(EssEquipDetailVo::getTime))
                    .collect(Collectors.toList());
            });
    }

    private Flux<EssEquipDetailVo> pcsDataSampling24h(
        String essDno, Long equipId, LocalDate date, List<LocalDateTime> times) {
        return this.pcsDataSampling24h(formatKey(essDno, equipId, date), times);
    }

    private Flux<EssEquipDetailVo> pcsDataSampling24h(String key, List<LocalDateTime> times) {
        List<EssEquipDetailVo> samplingList = new ArrayList<>();
        try {
            boolean hasLine = true;
            RedisEquipRtData<Pcs> last = null;
            int start = 0;
            final int size = 100;

            List<RedisEquipRtData<Pcs>> dataList = this.findPcsRtData(key, start, size);
            if (org.springframework.util.CollectionUtils.isEmpty(dataList)) {
                hasLine = false;
            }

            int dataSize = dataList.size();
            int idx = 0;
            for (LocalDateTime time : times) {
                if (null != last) {
                    LocalDateTime temp = last.getTime()
                        .withSecond(0)
                        .withNano(0);
                    if (time.isBefore(temp)) {
                        addPcsRepeatSampling(samplingList, time, last);
                        continue;
                    }
                }

                if (hasLine) {
                    while (true) {
                        if (idx == dataSize) {
                            start = start + size;
                            dataList = this.findPcsRtData(key, start, start + 100);
                            if (CollectionUtils.isEmpty(dataList)) {
                                hasLine = false;
                                break;
                            }

                            dataSize = dataList.size();
                            idx = 0;
                        }

                        RedisEquipRtData<Pcs> rtData = dataList.get(idx);
                        if (null == rtData || rtData.getTime() == null
                            || rtData.getData() == null) {
                            continue;
                        }

                        LocalDateTime temp = rtData.getTime()
                            .withSecond(0)
                            .withNano(0);

                        if (temp.isBefore(time)) {
                            idx = idx + 1;
                            continue;
                        } else if (temp.isEqual(time)) {
                            samplingList.add(new EssEquipDetailVo()
                                .setTime(time)
                                .setAcActivePowerTotal(rtData.getData().getAcActivePowerTotal())
                                .setAcReactivePowerTotal(rtData.getData().getAcReactivePowerTotal())
                                .setAcApparentPowerTotal(
                                    rtData.getData().getAcApparentPowerTotal()));
                            idx = idx + 1;
                            last = rtData;
                        } else if (temp.isAfter(time)) {
                            if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(samplingList)
                                && samplingList.get(samplingList.size() - 1).getAcActivePowerTotal()
                                != null
                                && DecimalUtils.gtZero(samplingList.get(samplingList.size() - 1)
                                .getAcActivePowerTotal())) {
                                samplingList.add(new EssEquipDetailVo()
                                    .setTime(time)
                                    .setAcActivePowerTotal(
                                        calcAverage(last, 1,
                                            rtData.getData().getAcActivePowerTotal()))
                                    .setAcReactivePowerTotal(
                                        calcAverage(last, 2,
                                            rtData.getData().getAcReactivePowerTotal()))
                                    .setAcApparentPowerTotal(
                                        calcAverage(last, 3,
                                            rtData.getData().getAcApparentPowerTotal())));
                            } else {
                                addPcsZeroSampling(samplingList, time);
                            }
                        } else {
                            idx = idx + 1;
                        }
                        rtData = null;
                        break;
                    }
                }

                if (!hasLine) {
                    addPcsZeroSampling(samplingList, time);
                }
            }
        } catch (Exception e) {
            // nothing
            log.error("err = {}", e.getMessage(), e);
        }
        return Flux.fromIterable(samplingList);
    }

    public static BigDecimal calcAverage(RedisEquipRtData<Pcs> last, int type,
        BigDecimal currValue) {
        BigDecimal lastV = BigDecimal.ZERO;
        if (last != null && last.getData() != null) {
            switch (type) {
                case 1:
                    lastV = last.getData().getAcActivePowerTotal();
                    break;
                case 2:
                    lastV = last.getData().getAcReactivePowerTotal();
                    break;
                case 3:
                    lastV = last.getData().getAcApparentPowerTotal();
                    break;
            }
        }
        return DecimalUtils.add(lastV, currValue)
            .divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
    }

    /**
     * 获取 redis中的采样数据
     *
     * @param key
     * @param start 时间区间 闭
     * @param end   时间区间 开
     * @param clazz
     * @return
     */
    public <T> List<RedisEquipRtData<T>> rtData(String key, LocalDateTime start, LocalDateTime end,
        Class<T> clazz) {
        LocalDate date = start.toLocalDate();
        List<RedisEquipRtData<JsonNode>> redisEquipRtData =
            this.rtJsonData(key, start, end);

        return redisEquipRtData.stream()
            .map(e -> {
                RedisEquipRtData<T> ret = new RedisEquipRtData<>();
                BeanUtils.copyProperties(e, ret);
                ret.setData(JsonUtils.fromJson(e.getData(), clazz));
                return ret;
            })
            .collect(Collectors.toList());
    }

    /**
     * @param key
     * @param start 时间区间 闭
     * @param end   时间区间 开
     * @return
     */
    private List<RedisEquipRtData<JsonNode>> rtJsonData(String key, LocalDateTime start,
        LocalDateTime end) {
        List<RedisEquipRtData<JsonNode>> ret = new ArrayList<>();
        int startIdx = 0;
        final int step = 100;
        List<RedisEquipRtData<JsonNode>> batteryClusterRtData = this.findBatteryClusterRtData(key,
            startIdx, startIdx + step);
        boolean hasNext = CollectionUtils.isNotEmpty(batteryClusterRtData);
        while (hasNext) {

            for (RedisEquipRtData<JsonNode> one : batteryClusterRtData) {
                startIdx++;
                if ((start.isBefore(one.getTime()) || start.isEqual(one.getTime())) &&
                    end.isAfter(one.getTime())) {
                    ret.add(one);
                }
            }

            if (batteryClusterRtData.size() < step) {
                hasNext = false;
            } else {
                batteryClusterRtData = this.findBatteryClusterRtData(key, startIdx,
                    startIdx + step);
                hasNext = CollectionUtils.isNotEmpty(batteryClusterRtData);
            }
        }
        return ret;
    }

    public static void addBatteryStackRepeatSampling(List<EssEquipDetailVo> list,
        LocalDateTime time, RedisEquipRtData<BatteryStack> last) {
        if (CollectionUtils.isEmpty(list) || last == null) {
            return;
        }
        EssEquipDetailVo vo = list.get(list.size() - 1);

        list.add(new EssEquipDetailVo()
            .setTime(time)
            .setSoc(calcAverageBS(last, 1, vo.getSoc()))
            .setMaxBatteryTemp(calcAverageBS(last, 1, vo.getMaxBatteryTemp()))
            .setTotalBatteryV(calcAverageBS(last, 2, vo.getTotalBatteryV())));
    }

    public static void addBatteryStackZeroSampling(List<EssEquipDetailVo> list,
        LocalDateTime time) {
        list.add(new EssEquipDetailVo()
            .setTime(time)
            .setSoc(BigDecimal.ZERO)
            .setMaxBatteryTemp(BigDecimal.ZERO)
            .setTotalBatteryV(BigDecimal.ZERO));
    }

    public static void addPcsRepeatSocSampling(List<EssEquipDetailVo> list, LocalDateTime time,
        LineEssRtData<BatteryStack> last) {
        if (CollectionUtils.isEmpty(list) || last == null) {
            return;
        }
        EssEquipDetailVo vo = list.get(list.size() - 1);

        list.add(new EssEquipDetailVo()
            .setTime(time)
            .setSoc(DecimalUtils.add(vo.getSoc(), last.getRtData().getSoc())
                .divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP))
            .setAcActivePowerTotal(BigDecimal.ZERO)
            .setAcReactivePowerTotal(BigDecimal.ZERO)
            .setAcApparentPowerTotal(BigDecimal.ZERO));
    }

    public static void addPcsRepeatAcDataSampling(List<EssEquipDetailVo> list, LocalDateTime time,
        LineEssRtData<Pcs> last) {
        if (CollectionUtils.isEmpty(list) || last == null) {
            return;
        }
        EssEquipDetailVo vo = list.get(list.size() - 1);

        list.add(new EssEquipDetailVo()
            .setTime(time)
            .setSoc(BigDecimal.ZERO)
            .setAcActivePowerTotal(calcAverage(last, 1, vo.getAcActivePowerTotal()))
            .setAcReactivePowerTotal(calcAverage(last, 2, vo.getAcReactivePowerTotal()))
            .setAcApparentPowerTotal(calcAverage(last, 3, vo.getAcApparentPowerTotal())));
    }

    public static BigDecimal calcAverage(LineEssRtData<Pcs> last, int type, BigDecimal currValue) {
        BigDecimal lastV = BigDecimal.ZERO;
        if (last != null && last.getRtData() != null) {
            switch (type) {
                case 1:
                    lastV = last.getRtData().getAcActivePowerTotal();
                    break;
                case 2:
                    lastV = last.getRtData().getAcReactivePowerTotal();
                    break;
                case 3:
                    lastV = last.getRtData().getAcApparentPowerTotal();
                    break;
            }
        }
        return DecimalUtils.add(lastV, currValue)
            .divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
    }

    public static void addPcsZeroSampling(List<EssEquipDetailVo> list, LocalDateTime time) {
        list.add(new EssEquipDetailVo()
            .setTime(time)
            .setSoc(BigDecimal.ZERO)
            .setAcActivePowerTotal(BigDecimal.ZERO)
            .setAcReactivePowerTotal(BigDecimal.ZERO)
            .setAcApparentPowerTotal(BigDecimal.ZERO));
    }

    public static void addPcsRepeatSampling(List<EssEquipDetailVo> list, LocalDateTime time,
        RedisEquipRtData<Pcs> last) {
        if (CollectionUtils.isEmpty(list) || last == null) {
            return;
        }
        EssEquipDetailVo vo = list.get(list.size() - 1);

        list.add(new EssEquipDetailVo()
            .setTime(time)
            .setSoc(BigDecimal.ZERO)
            .setAcActivePowerTotal(calcAverage(last, 1, vo.getAcActivePowerTotal()))
            .setAcReactivePowerTotal(calcAverage(last, 2, vo.getAcReactivePowerTotal()))
            .setAcApparentPowerTotal(calcAverage(last, 3, vo.getAcApparentPowerTotal())));
    }

    public static EssEquipDetailVo generatePcsZeroSampling(LocalDateTime time) {
        return new EssEquipDetailVo()
            .setTime(time)
            .setSoc(BigDecimal.ZERO)
            .setAcActivePowerTotal(BigDecimal.ZERO)
            .setAcReactivePowerTotal(BigDecimal.ZERO)
            .setAcApparentPowerTotal(BigDecimal.ZERO);
    }

    public static List<LocalDateTime> splitDay(LocalDateTime start, LocalDateTime end, int dis) {
        List<LocalDateTime> times = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        while (start.isBefore(end)) {
            times.add(start);
            start = start.plusMinutes(dis);
        }
        return times;
    }

    public static List<LocalDateTime> splitDay(LocalDate date, int dis) {
        List<LocalDateTime> times = new ArrayList<>();
        LocalDateTime start = date.atStartOfDay();
        LocalDateTime end = date.plusDays(1).atStartOfDay();
        LocalDateTime now = LocalDateTime.now();
        while (start.isBefore(end) && start.isBefore(now)) {
            times.add(start);
            start = start.plusMinutes(dis);
        }
        return times;
    }

    /**
     * 查询缓存中数据列表
     *
     * @param key           Redis 中 key 值
     * @param start         开始索引值
     * @param end           结束索引值
     * @param typeReference 类型状态
     * @param <T>           目标数据类
     * @return 缓存中数据列表
     */
    public <T> List<T> findRtData(String key, long start, long end,
        TypeReference<T> typeReference) {
        Long size = redisTemplate.opsForList().size(key);
        if (null == size) {
            return List.of();
        }

        List<String> range = redisTemplate.opsForList()
            .range(key, start, end);
        if (CollectionUtils.isEmpty(range)) {
            return List.of();
        }

        return range.stream()
            .map(value -> JsonUtils.fromJson(value, typeReference))
            .collect(Collectors.toList());
    }

    /**
     * 近24小时分割
     *
     * @param dis 时间间隔，单位: 分钟
     * @return <p>t1 采样开始时间; <br />t2 是昨天数据时间点列表; <br />t3 是当天的采样时间点列表</p>
     */
    private static Tuple3<LocalDate, List<LocalDateTime>, List<LocalDateTime>> recent24h(int dis) {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime start;
        LocalDateTime mid = LocalDate.now().atStartOfDay();
        if (now.getMinute() > 20) {
            start = now.minusHours(23);
        } else {
            start = now.minusHours(24);
        }
        start = start.withMinute(0);
        start = start.withSecond(0);

        List<LocalDateTime> yesterday = splitDay(start, mid, dis);
        List<LocalDateTime> today = splitDay(mid, now.plusHours(1).withMinute(0), dis);
        return Tuples.of(start.toLocalDate(), yesterday, today);
    }

    /**
     * PCS近24h数据采样
     *
     * @param essDno  ESS设备编号
     * @param equipId 设备ID
     * @param dis     时间间隔，单位: 分钟
     * @return
     */
    public Mono<List<PcsSampleData>> pcsSampling24h(String essDno, Long equipId, int dis) {
        Tuple3<LocalDate, List<LocalDateTime>, List<LocalDateTime>> tuples = recent24h(dis);
        return Flux.concat(this.pcsSampling24h(essDno, equipId, tuples.getT1(), tuples.getT2()),
                this.pcsSampling24h(essDno, equipId, LocalDate.now(), tuples.getT3()))
            .sort(Comparator.comparing(PcsSampleData::getTime))
            .collectList();
    }

//    private <T extends SampleBase> Flux<T> rtDataSampling24h(
//            EssEquipType type, String essDno, Long equipId, LocalDate date, List<LocalDateTime> times) {
//        CollectFunc<T> collectFunc = COLLECT_FUNC_MAP.get(type);
//        if (null == collectFunc) throw new DcArgumentException("该设备类型没有定义收集函数");
//        ConvertFunc<T, RedisEquipRtData<Object>> convertFunc = CONVERT_FUNC_MAP.get(type);
//        if (null == convertFunc) throw new DcArgumentException("该设备类型没有定义转换函数");
//
//        return this.samplingData(formatKey(essDno, equipId, date), times, collectFunc, convertFunc);
//    }

    private Flux<PcsSampleData> pcsSampling24h(
        String essDno, Long equipId, LocalDate date, List<LocalDateTime> times) {
        return this.samplingData(formatKey(essDno, equipId, date), times,
            data -> {
                PcsSampleData item = new PcsSampleData();
                item.setTime(data.getTime());
                Pcs pcs = JsonUtils.fromJson(JsonUtils.toJsonString(data.getData()), Pcs.class);

                if (null == pcs.getDcPower()) {
                    pcs.setDcPower(BigDecimal.ZERO);
                }
                if (null == pcs.getDcVoltage()) {
                    pcs.setDcVoltage(BigDecimal.ZERO);
                }

                return item.setDcPower(pcs.getDcPower())
                    .setInPower(
                        DecimalUtils.gtZero(pcs.getDcPower()) ? pcs.getDcPower() : BigDecimal.ZERO)
                    .setOutPower(
                        DecimalUtils.ltZero(pcs.getDcPower()) ? pcs.getDcPower() : BigDecimal.ZERO)
                    .setDcVoltage(pcs.getDcVoltage());
            },
            (collect, time, item) -> {
                if (null == item) {
                    item = new PcsSampleData();
                    item.setTime(time);
                    item.setDcPower(BigDecimal.ZERO);
                    item.setInPower(BigDecimal.ZERO);
                    item.setOutPower(BigDecimal.ZERO);
                    item.setDcVoltage(BigDecimal.ZERO);
                }

                if (null == collect) {
                    collect = new ArrayList<>();
                }

                collect.add(item);
            });
    }

    /**
     * EMS近24h数据采样
     *
     * @param essDno  ESS设备编号
     * @param equipId 设备ID
     * @param dis     时间间隔，单位: 分钟
     * @return
     */
    public Mono<List<EmsSampleData>> emsSampling24h(String essDno, Long equipId, int dis) {
        Tuple3<LocalDate, List<LocalDateTime>, List<LocalDateTime>> tuples = recent24h(dis);
        return Flux.concat(this.emsSampling24h(essDno, equipId, tuples.getT1(), tuples.getT2()),
                this.emsSampling24h(essDno, equipId, LocalDate.now(), tuples.getT3()))
            .sort(Comparator.comparing(EmsSampleData::getTime))
            .collectList();
    }

    private Flux<EmsSampleData> emsSampling24h(
        String essDno, Long equipId, LocalDate date, List<LocalDateTime> times) {
        return this.samplingData(formatKey(essDno, equipId, date), times,
            data -> {
                EmsSampleData item = new EmsSampleData();
                item.setTime(data.getTime());
                Ems ems = JsonUtils.fromJson(JsonUtils.toJsonString(data.getData()), Ems.class);
                if (null == ems.getSoc()) {
                    ems.setSoc(BigDecimal.ZERO);
                }
                return item.setSoc(ems.getSoc());
            },
            (collect, time, item) -> {
                if (null == item) {
                    item = new EmsSampleData();
                    item.setTime(time);
                    item.setSoc(BigDecimal.ZERO);
                }

                if (null == collect) {
                    collect = new ArrayList<>();
                }

                collect.add(item);
            });
    }

    @FunctionalInterface
    interface CollectFunc<ITEM extends SampleBase> {

        void collect(List<ITEM> collect, LocalDateTime time, ITEM item);
    }

    @FunctionalInterface
    interface ConvertFunc<ITEM extends SampleBase, DEST> {

        ITEM convert(DEST dest);
    }

    /**
     * 运行数据采样
     *
     * @param key         Redis 中 key 值
     * @param times       已经分割好的采样时间点
     * @param convertFunc 数据对象转换函数
     * @param collectFunc 收集采样函数
     * @param <RES>       采样点反馈数据类
     * @param <IN_CASE>   数据收集数据类
     * @return 采样点数据列表
     */
    private <RES extends SampleBase, IN_CASE> Flux<RES> samplingData(
        String key, List<LocalDateTime> times,
        ConvertFunc<RES, RedisEquipRtData<IN_CASE>> convertFunc, CollectFunc<RES> collectFunc) {
        List<RES> samplingList = new ArrayList<>();
        try {
            boolean hasLine = true;
            RedisEquipRtData<IN_CASE> last = null;
            int start = 0;
            for (LocalDateTime time : times) {
                if (null != last) {
                    if (time.isBefore(last.getTime())) {
                        collectFunc.collect(samplingList, time, null);
                        continue;
                    }
                }

                if (hasLine) {
                    List<RedisEquipRtData<IN_CASE>> dataList = this.findRtData(
                        key, start, start + 100, new TypeReference<>() {
                        });
                    if (CollectionUtils.isEmpty(dataList)) {
                        hasLine = false;
                    } else {
                        int size = dataList.size();
                        int idx = 0;
                        while (true) {
                            if (idx == size) {
                                dataList = this.findRtData(
                                    key, start, start + 100, new TypeReference<>() {
                                    });
                                if (CollectionUtils.isEmpty(dataList)) {
                                    hasLine = false;
                                    break;
                                }

                                size = dataList.size();
                                idx = 0;
                            }

                            start++;
                            RedisEquipRtData<IN_CASE> rtData = dataList.get(idx++);
                            if (null == rtData || rtData.getTime() == null) {
                                continue;
                            }

                            if (null != rtData.getData()) {
                                if (rtData.getTime().isBefore(time)) {
                                    continue;
                                }

                                if (null != last || rtData.getTime().isBefore(time)) {
                                    collectFunc.collect(samplingList, time,
                                        convertFunc.convert(rtData));
                                } else if (time.isBefore(rtData.getTime())) {
                                    collectFunc.collect(samplingList, time, null);
                                }
                                last = rtData;
                                rtData = null;
                                break;
                            }
                        }
                    }
                }

                if (!hasLine) {
                    collectFunc.collect(samplingList, time, null);
                }
            }
        } catch (Exception e) {
            // nothing
            log.error("err = {}", e.getMessage(), e);
        }
        return Flux.fromIterable(samplingList);
    }
}
