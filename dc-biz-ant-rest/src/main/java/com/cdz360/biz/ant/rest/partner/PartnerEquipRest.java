package com.cdz360.biz.ant.rest.partner;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.ant.service.partner.PartnerEquipService;
import com.cdz360.biz.model.trading.hlht.param.BindHlhtParam;
import com.cdz360.biz.model.trading.hlht.param.HlhtSiteParam;
import com.cdz360.biz.model.trading.hlht.param.SyncSiteParam;
import com.cdz360.biz.model.trading.hlht.vo.HlhtSiteVo;
import com.cdz360.biz.model.trading.hlht.vo.OperatorVo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/api/partner/equip")
@Tag(name = "设备侧互联相关接口", description = "设备侧互联")
public class PartnerEquipRest extends BaseController {

    @Autowired
    public PartnerEquipService partnerService;

    @Operation(summary = "互联运营商列表")
    @GetMapping(value = "/partnerList")
    public Mono<ListResponse<OperatorVo>> partnerList(@RequestParam(value = "start", defaultValue = "0") Long start,
                                                      @RequestParam(value = "size", defaultValue = "10") Integer size,
                                                      @RequestParam(value = "code", required = false) String code,
                                                      @RequestParam(value = "name", required = false) String name) {
        log.info("partnerList start: {}, size: {}, code: {}, name: {}", start, size, code, name);

        return partnerService.partnerList(code, name, start, size);
    }

    @Operation(summary = "更新插入互联运营商")
    @PostMapping(value = "/upsertPartner")
    public Mono<BaseResponse> upsertPartner(@RequestBody OperatorVo vo) {
        log.info("upsertPartner OperatorVo: {}", vo);

        return partnerService.upsertPartner(vo);
    }

    @Operation(summary = "删除互联运营商")
    @GetMapping(value = "/delPartner")
    public Mono<BaseResponse> delPartner(@RequestParam(value = "pId") Long pId) {
        log.info("delPartner pId: {}", pId);

        return partnerService.delPartner(pId);
    }

    @Operation(summary = "根据互联运营商同步站点信息")
    @GetMapping(value = "/syncSiteByPartner")
    public Mono<BaseResponse> syncSiteByPartner(@RequestParam(value = "topCommId", defaultValue = "0") Long topCommId,
                                                @RequestParam(value = "partnerCode") String partnerCode) {
        log.info("syncSiteByPartner topCommId: {} partnerCode: {}", topCommId, partnerCode);

        return partnerService.syncSiteByPartner(topCommId, partnerCode);
    }

    @Operation(summary = "根据互联站点同步站点信息")
    @PostMapping(value = "/syncSiteByStationId")
    public Mono<BaseResponse> syncSiteByStationId(ServerHttpRequest request,
                                                  @RequestBody SyncSiteParam param) {
        log.info("syncSiteByStationId param: {}", param);
        param.setTopCommId(0L);
        return partnerService.syncSiteByStationId(param);
    }


    @Operation(summary = "互联站点列表")
    @PostMapping(value = "/hlhtSiteList")
    public Mono<ListResponse<HlhtSiteVo>> hlhtSiteList(@RequestBody HlhtSiteParam param) {
        log.info("hlhtSiteList param: {}", param);
        return partnerService.hlhtSiteList(param);
    }

    @Operation(summary = "商户绑定互联站点")
    @PostMapping(value = "/bindHlhtSite")
    public BaseResponse bindHlhtSite(@RequestBody BindHlhtParam param) {
        log.info("bindHlhtSite param: {}", param);

        return partnerService.bindHlhtSite(param);
    }

    @Operation(summary = "商户解绑互联站点")
    @PostMapping(value = "/unbindHlhtSite")
    public BaseResponse unbindHlhtSite(@RequestBody BindHlhtParam param) {
        log.info("unbindHlhtSite param: {}", param);

        return partnerService.unbindHlhtSite(param);
    }

    @Operation(summary = "商户编辑互联站点支持的账户类型")
    @GetMapping(value = "/editHlhtSitePayType")
    public BaseResponse editHlhtSitePayType(@RequestParam(value = "commId") Long commId,
                                            @RequestParam(value = "payTypeList") List<Integer> payTypeList) {
        log.info("editHlhtSitePayType param: {}, payTypeList: {}", commId, payTypeList);

        return partnerService.editHlhtSitePayType(commId, payTypeList);
    }


}
