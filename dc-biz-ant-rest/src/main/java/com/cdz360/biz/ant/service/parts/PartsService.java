package com.cdz360.biz.ant.service.parts;

import static com.cdz360.biz.model.parts.param.ListPartsParamEx.ExPage.PARTS_LIST_SAME_GROUP;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorAuthCenterFeignClient;
import com.cdz360.biz.auth.sys.param.YwUserParam;
import com.cdz360.biz.model.common.constant.ExcelCheckResult;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.parts.dto.PartsBatchImportDto;
import com.cdz360.biz.model.parts.param.ListPartsParam;
import com.cdz360.biz.model.parts.param.ListPartsParamEx;
import com.cdz360.biz.model.parts.param.PartsBrokenParam;
import com.cdz360.biz.model.parts.param.PartsEditParam;
import com.cdz360.biz.model.parts.param.PartsOpBaseParam;
import com.cdz360.biz.model.parts.param.PartsRollbackParam;
import com.cdz360.biz.model.parts.param.PartsTransReviewParam;
import com.cdz360.biz.model.parts.type.PartsStatus;
import com.cdz360.biz.model.parts.vo.PartsImportItem;
import com.cdz360.biz.model.parts.vo.PartsImportVo;
import com.cdz360.biz.model.parts.vo.PartsOpLogVo;
import com.cdz360.biz.model.parts.vo.PartsTransTraceVo;
import com.cdz360.biz.model.parts.vo.PartsVo;
import com.cdz360.biz.model.parts.vo.PartsWithVo;
import com.cdz360.biz.model.sys.constant.SiteGroupType;
import com.cdz360.biz.model.sys.dto.UserOwnerSiteGroupDto;
import com.cdz360.biz.model.sys.param.ListSiteGroupParam;
import com.cdz360.biz.model.sys.po.SiteGroupPo;
import com.cdz360.biz.model.sys.vo.SiteGroupVo;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.utils.feign.auth.AuthSiteGroupFeignClient;
import com.cdz360.biz.utils.feign.iot.DevicePartsFeignClient;
import com.chargerlinkcar.framework.common.domain.SysUser;
import com.chargerlinkcar.framework.common.service.excel.BufferedExcelConvertor;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class PartsService {

    @Autowired
    private AuthSiteGroupFeignClient authSiteGroupFeignClient;

    @Autowired
    private ReactorAuthCenterFeignClient reactorAuthCenterFeignClient;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private DevicePartsFeignClient devicePartsFeignClient;

    @Autowired
    private BufferedExcelConvertor excelConvertor;

    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;

    public Mono<ListResponse<PartsVo>> partsTransport(PartsImportItem param) {
        IotAssert.isNotNull(param.getTypeId(), "物料规格信息请选择");
        IotAssert.isNotNull(param.getApplyId(), "请选择申请人");
        IotAssert.isNotBlank(param.getApplyName(), "请选择申请人");
        IotAssert.isNotBlank(param.getExpressName(), "请填写快递名称");
        IotAssert.isNotBlank(param.getExpressNo(), "请填写快递单号");
        IotAssert.isNotNull(param.getApplyNum(), "请输入申请数量");
        IotAssert.isTrue(param.getApplyNum() > 0, "申请数量需要大于0");
        return devicePartsFeignClient.partsTransport(param);
    }

    public Mono<ListResponse<PartsVo>> partsReceive(PartsBrokenParam param) {
        IotAssert.isTrue(StringUtils.isNotBlank(param.getCode()) ||
            CollectionUtils.isNotEmpty(param.getCodeList()), "物料ID不能为空");
        IotAssert.isNotNull(param.getOpUid(), "操作人ID不能为空");

        if (CollectionUtils.isEmpty(param.getCodeList())) {
            param.setCodeList(List.of(param.getCode()));
        }
        return devicePartsFeignClient.partsReceive(param);
    }

    public Mono<BaseResponse> partsBroken(PartsBrokenParam param) {
        if (CollectionUtils.isEmpty(param.getCodeList())) {
            throw new DcArgumentException("物料ID不能为空");
        }
        IotAssert.isNotNull(param.getOpUid(), "操作人ID不能为空");
        return devicePartsFeignClient.partsBroken(param);
    }

    public Mono<BaseResponse> partsRollback(PartsRollbackParam param) {
        IotAssert.isTrue(StringUtils.isNotBlank(param.getCode()) ||
            CollectionUtils.isNotEmpty(param.getCodeList()), "物料ID不能为空");
        IotAssert.isNotNull(param.getOpUid(), "操作人ID不能为空");
        IotAssert.isNotBlank(param.getExpressName(), "请填写快递名称");
        IotAssert.isNotBlank(param.getExpressNo(), "请填写快递单号");

        if (CollectionUtils.isEmpty(param.getCodeList())) {
            param.setCodeList(List.of(param.getCode()));
        }
        return devicePartsFeignClient.partsRollback(param);
    }

    public Mono<BaseResponse> partsEdit(PartsEditParam param) {
        if (CollectionUtils.isEmpty(param.getCodeList())) {
            throw new DcArgumentException("物料ID不能为空");
        }
//        IotAssert.isNotNull(param.getTypeId(), "物料规格ID不能为空");
        IotAssert.isNotNull(param.getOpUid(), "操作人ID不能为空");
        IotAssert.isNotNull(param.getToStatus(), "目标状态不能为空");
        IotAssert.isTrue(!PartsStatus.DISCARD.equals(param.getToStatus()),
            "当前接口不允许进行报废操作"); // 请使用报废接口
        return devicePartsFeignClient.partsEdit(param);
    }

    public Mono<ListResponse<PartsVo>> partsTransReview(PartsTransReviewParam param) {
        IotAssert.isTrue(StringUtils.isNotBlank(param.getCode()) ||
            CollectionUtils.isNotEmpty(param.getCodeList()), "物料ID不能为空");
        IotAssert.isNotNull(param.getAgree(), "同意/拒绝标识需要指明");
        IotAssert.isNotNull(param.getOpUid(), "操作人ID不能为空");
        IotAssert.isNotBlank(param.getApplyName(), "请传入申请人名称");
        if (CollectionUtils.isEmpty(param.getCodeList())) {
            param.setCodeList(List.of(param.getCode()));
        }
        return devicePartsFeignClient.partsTransReview(param);
    }

    public Mono<ListResponse<PartsVo>> partsTransApply(PartsOpBaseParam param) {
        IotAssert.isTrue(StringUtils.isNotBlank(param.getCode()) ||
            CollectionUtils.isNotEmpty(param.getCodeList()), "物料ID不能为空");
        IotAssert.isNotNull(param.getOpUid(), "操作人ID不能为空");

        if (CollectionUtils.isEmpty(param.getCodeList())) {
            param.setCodeList(List.of(param.getCode()));
        }
        return devicePartsFeignClient.partsTransApply(param);
    }

    public Mono<ObjectResponse<PartsVo>> partsTransApplyCancel(PartsOpBaseParam param) {
        IotAssert.isNotBlank(param.getCode(), "物料ID不能为空");
        IotAssert.isNotNull(param.getOpUid(), "操作人ID不能为空");
        return devicePartsFeignClient.partsTransApplyCancel(param);
    }

    public Mono<ObjectResponse<PartsTransTraceVo>> partsTransTrace(String code) {
        IotAssert.isNotBlank(code, "物料ID不能为空");
        return devicePartsFeignClient.partsTransTrace(code)
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .flatMap(x -> {
                if (CollectionUtils.isNotEmpty(x.getOpLogVoList())) {
                    return reactorAuthCenterFeignClient.getSysUserByIdList(
                            x.getOpLogVoList().stream()
                                .map(PartsOpLogVo::getOpUid)
                                .distinct()
                                .collect(Collectors.toList()))
                        .doOnNext(FeignResponseValidate::check)
                        .map(ListResponse::getData)
                        .map(list -> {
                            // 用户名称调整
                            final Map<Long, String> uidMap = list.stream()
                                .collect(Collectors.toMap(SysUserVo::getId, SysUserVo::getName));

                            for (PartsOpLogVo log : x.getOpLogVoList()) {
                                if (null != log.getOpUid() && uidMap.containsKey(log.getOpUid())) {
                                    log.setOpName(uidMap.get(log.getOpUid()));
                                }
                            }
                            return x;
                        });
                }

                return Mono.just(x);
            })
            .map(RestUtils::buildObjectResponse);
    }

    public Mono<BaseResponse> partsBatchImport(PartsBatchImportDto dto) {
        if (CollectionUtils.isEmpty(dto.getItems())) {
            throw new DcArgumentException("请提供导入物料数据");
        }
        return devicePartsFeignClient.partsBatchImport(dto);
    }

    public Mono<ObjectResponse<Long>> partsCount(Long uid) {
        if (null == uid) {
            throw new DcArgumentException("用户ID不能为空");
        }
        return devicePartsFeignClient.partsCount(uid);
    }

    public Mono<ListResponse<PartsWithVo>> ownPartsList(ListPartsParam param) {
        if (CollectionUtils.isEmpty(param.getUidList())) {
            throw new DcArgumentException("用户ID无效");
        }
        return this.findPartsList(param);
    }

    public Mono<ListResponse<PartsWithVo>> ownYwGroupPartsList(Long uid, ListPartsParam param) {
        // 获取运维组其他人员ID列表
        final boolean result = this.ywGroupOtherUser(true, uid, param);
        if (result) {
            return Mono.just(RestUtils.buildListResponse(List.of()));
        }
        return this.findPartsList(param);
    }

    /**
     * 获取运维人员
     *
     * @param same  是否同组
     * @param uid   当前用户ID
     * @param param 查询相关参数
     * @return 判断是否为空的标识(true : 标识可以直接返回空 ; false : 上层逻辑需要继续)
     */
    private boolean ywGroupOtherUser(boolean same, Long uid, ListPartsParam param) {
        final ListResponse<SysUser> ywUser = authCenterFeignClient.getYwGroupOtherUser(
            uid, same, param.getGidList());
        FeignResponseValidate.check(ywUser);
        param.setGidList(null); // 避免后续重复操作

        if (CollectionUtils.isEmpty(ywUser.getData())) {
            return true;
        }

        // 用户ID去重
        final List<Long> uidList = ywUser.getData().stream()
            .map(SysUser::getId).distinct()
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(param.getUidList())) {
            param.setUidList(param.getUidList().stream().filter(uidList::contains)
                .collect(Collectors.toList()));
        } else {
            param.setUidList(uidList);
        }

        return false;
    }

    public Mono<ListResponse<PartsWithVo>> otherYwGroupPartsList(Long uid, ListPartsParam param) {
        // 获取运维组其他人员ID列表
        final boolean result = this.ywGroupOtherUser(false, uid, param);
        if (result) {
            return Mono.just(RestUtils.buildListResponse(List.of()));
        }
        return this.findPartsList(param);
    }

    public Mono<ListResponse<PartsWithVo>> otherYwPartsList(ListPartsParam param) {
        return this.findPartsList(param);
    }

    public Mono<ListResponse<PartsWithVo>> findPartsList(ListPartsParam param) {
        if (CollectionUtils.isNotEmpty(param.getGidList())) {
            final ListResponse<SysUser> ywUser = authCenterFeignClient.findYwUser(
                new YwUserParam().setGidList(param.getGidList()));
            FeignResponseValidate.check(ywUser);

            if (CollectionUtils.isEmpty(ywUser.getData())) {
                return Mono.just(RestUtils.buildListResponse(List.of()));
            }

            // 用户ID去重
            final List<Long> uidList = ywUser.getData().stream()
                .map(SysUser::getId).distinct()
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(param.getUidList())) {
                param.setUidList(Stream.concat(param.getUidList().stream(), uidList.stream())
                    .distinct().collect(Collectors.toList()));
            } else {
                param.setUidList(uidList);
            }
        }

        return devicePartsFeignClient.findParts(param)
            .doOnNext(FeignResponseValidate::check)
            .flatMap(res -> {
                Set<Long> uidSet = new HashSet<>();
                Set<Long> uidSetX = new HashSet<>();
                for (PartsVo item : res.getData()) {
                    if (null != item.getCreateBy()) {
                        uidSet.add(item.getCreateBy());
                    }

                    if (null != item.getApplyBy()) {
                        uidSet.add(item.getApplyBy());
                    }

                    if (null != item.getStorageUid()) {
                        uidSetX.add(item.getStorageUid());
                    }
                }

                ListSiteGroupParam sgParam = new ListSiteGroupParam();
                sgParam.setUidList(new ArrayList<>(uidSetX))
                    .setTypeList(List.of(SiteGroupType.YW)); // 仅关注运维
                return Mono.zip(Mono.just(uidSetX).filter(CollectionUtils::isNotEmpty)
                            .flatMap(x -> authSiteGroupFeignClient.userOwnerSiteGroup(sgParam))
                            .doOnNext(FeignResponseValidate::check)
                            .map(ListResponse::getData)
                            .switchIfEmpty(Mono.just(List.of())), // 所属场站组
                        Mono.just(uidSet).filter(CollectionUtils::isNotEmpty)
                            .flatMap(x -> reactorAuthCenterFeignClient.getSysUserByIdList(
                                new ArrayList<>(x)))
                            .doOnNext(FeignResponseValidate::check)
                            .map(ListResponse::getData)
                            .switchIfEmpty(Mono.just(List.of()))) // 用户名称调整显示
                    .map(data -> {
                        final List<UserOwnerSiteGroupDto> userGroupList = data.getT1();
                        final List<SysUserVo> userList = data.getT2();
                        // 用户场站组调整
                        final Map<Long, List<SiteGroupVo>> uidGroupMap = userGroupList.stream()
                            .collect(Collectors.toMap(UserOwnerSiteGroupDto::getUid,
                                UserOwnerSiteGroupDto::getGroupVoList));

                        // 用户名称调整
                        final Map<Long, String> uidMap = userList.stream()
                            .collect(Collectors.toMap(SysUserVo::getId, SysUserVo::getName));

                        for (PartsWithVo x : res.getData()) {
                            if (null != x.getCreateBy() && uidMap.containsKey(x.getCreateBy())) {
                                x.setCreateByName(uidMap.get(x.getCreateBy()));
                            }
                            if (null != x.getApplyBy() && uidMap.containsKey(x.getApplyBy())) {
                                x.setApplyByName(uidMap.get(x.getApplyBy()));
                            }

                            if (null != x.getStorageUid() &&
                                uidGroupMap.containsKey(x.getStorageUid())) {
                                final List<SiteGroupVo> list = uidGroupMap.get(
                                    x.getStorageUid());
                                x.setSiteGroupList(list);
                                x.setGidList(list.stream().map(SiteGroupPo::getGid).collect(
                                    Collectors.toList()));
                                x.setGroupNameList(list.stream().map(SiteGroupPo::getName).collect(
                                    Collectors.toList()));
                            }
                        }
                        return res;
                    });
            });
    }

    public Mono<ObjectResponse<PartsImportVo>> parsePartsExcel(FilePart file) {
        log.info(">>parsePartsExcel excel 文件: {}", file.filename());
        try {
            File f = new File("/tmp/" + file.filename());
            return file.transferTo(f)
                .then(Mono.just("文件上传成功"))
                .map(a -> {
                    try (InputStream inputStream = new FileInputStream(f)) {
                        PartsImportVo importVo = excelConvertor.convert(
                            ExcelCheckResult.PARTS_VALID, inputStream,
                            file.filename());
                        return RestUtils.buildObjectResponse(importVo);
                    } catch (IOException e) {
                        log.warn("<< 解析excel文件异常. message = {}", e.getMessage(), e);
                        throw new DcServiceException("解析excel文件异常，请求确认文件内容.");
                    }
                });
        } catch (Exception e) {
            log.warn("error: {}", e.getMessage(), e);
            throw new DcServiceException("文件上传失败");
        }
    }

    public Mono<ObjectResponse<ExcelPosition>> partsExportExcel(
        Long sysUid, ListPartsParamEx param) {

        // 页面影响参数
        switch (param.getPage()) {
            case PARTS_MGM:
            case PARTS_LIST_TOP:
                break;
            case PARTS_LIST_OWNER:
                param.setUidList(List.of(sysUid));
                break;
            case PARTS_LIST_SAME_GROUP:
            case PARTS_LIST_OTHER_GROUP:
                this.ywGroupOtherUser(PARTS_LIST_SAME_GROUP.equals(param.getPage()), sysUid, param);
                break;
        }

        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName(StringUtils.isNotBlank(param.getExFileName())
                ? param.getExFileName() : "物料列表")
            .setFunctionMap(DownloadFunctionType.PARTS_LIST)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
    }
}
