package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.service.CusPostService;
import com.cdz360.biz.ant.service.sysLog.CusPostSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.cus.post.param.CusPostChangeStatusParam;
import com.cdz360.biz.model.cus.post.param.CusPostChangeTagParam;
import com.cdz360.biz.model.cus.post.param.CusPostReplyParam;
import com.cdz360.biz.model.cus.post.param.ListCusPostParam;
import com.cdz360.biz.model.cus.post.vo.CusPostVo;
import com.cdz360.biz.model.cus.vo.SiteCommentTotal;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "用户评论相关接口")
@Slf4j
@RestController
public class CusPostRest {

    @Autowired
    private CusPostSysLogService cusPostSysLogService;

    @Autowired
    private CusPostService cusPostService;

    @Operation(summary = "获取场站评论综合信息")
    @GetMapping(value = "/api/post/siteCommentTotal")
    public Mono<ObjectResponse<SiteCommentTotal>> siteCommentTotal(
            ServerHttpRequest request,
            @Parameter(name = "场站ID", required = true) @RequestParam("siteId") String siteId,
            @Parameter(name = "是否公开") @RequestParam(value = "open", required = false) Boolean open) {
        log.info("获取场站评论综合信息: {}", LoggerHelper2.formatEnterLog(request));
        return cusPostService.siteCommentTotal(siteId, open);
    }

    @Operation(summary = "用户评论列表")
    @PostMapping(value = "/api/post/list")
    public Mono<ListResponse<CusPostVo>> cusPostList(
            ServerHttpRequest request, @RequestBody ListCusPostParam param) {
        log.info("用户评论列表: {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        List<String> gidList = AntRestUtils.getSysUserGids(request);
        if (CollectionUtils.isNotEmpty(gidList)) {
            param.setGidList(gidList);
        } else {
            param.setCommIdChain( AntRestUtils.getCommIdChain(request));
        }
        return cusPostService.cusPostList(param);
    }

    @Operation(summary = "切换用户评论公开状态")
    @PostMapping(value = "/api/post/changeOpen")
    public Mono<BaseResponse> cusPostChangeOpen(
            ServerHttpRequest request, @RequestBody CusPostChangeStatusParam param) {
        log.info("切换用户评论公开状态: {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        Long sysUid = AntRestUtils.getSysUid(request);
        String userLoginName = AntRestUtils.getSysUserLoginName(request);
        if (sysUid <= 0) {
            throw new DcArgumentException("登录失效，请重新登录");
        }

        param.setOpUid(sysUid);
        param.setOpName(userLoginName);
        return cusPostService.cusPostChangeOpen(param)
                .doOnNext(FeignResponseValidate::check)
                .doOnNext(res -> cusPostSysLogService.cusPostChangeOpen(param.getOrderNoList(), request));
    }

    @Operation(summary = "切换用户评论公开状态")
    @PostMapping(value = "/api/post/changeTag")
    public Mono<BaseResponse> cusPostChangeTag(
            ServerHttpRequest request, @RequestBody CusPostChangeTagParam param) {
        log.info("切换用户评论公开状态: {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        Long sysUid = AntRestUtils.getSysUid(request);
        String userLoginName = AntRestUtils.getSysUserLoginName(request);
        if (sysUid <= 0) {
            throw new DcArgumentException("登录失效，请重新登录");
        }

        param.setOpUid(sysUid);
        param.setOpName(userLoginName);
        return cusPostService.cusPostChangeTag(param)
                .doOnNext(FeignResponseValidate::check)
                .doOnNext(res -> cusPostSysLogService.cusPostChangeTag(param.getOrderNoList(), request));
    }


    @Operation(summary = "用户评论回复")
    @PostMapping(value = "/api/post/reply")
    public Mono<BaseResponse> cusPostReply(
            ServerHttpRequest request, @RequestBody CusPostReplyParam param) {
        log.info("用户评论回复: {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        Long sysUid = AntRestUtils.getSysUid(request);
        String userLoginName = AntRestUtils.getSysUserLoginName(request);
        if (sysUid <= 0) {
            throw new DcArgumentException("登录失效，请重新登录");
        }

        param.setOpUid(sysUid);
        param.setOpName(userLoginName);
        return cusPostService.cusPostReply(param)
                .doOnNext(FeignResponseValidate::check)
                .doOnNext(res ->
                        cusPostSysLogService.cusPostReply(param.getOrderNo(), request));
    }
}
