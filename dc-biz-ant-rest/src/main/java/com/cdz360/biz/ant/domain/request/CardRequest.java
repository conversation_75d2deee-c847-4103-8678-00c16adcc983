package com.cdz360.biz.ant.domain.request;

import com.chargerlinkcar.framework.common.domain.request.BaseRequest;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * CardRequest
 * 
 * @since 2019/7/9
 * <AUTHOR>
 */
@Data
@Schema
@EqualsAndHashCode(callSuper = true)
public class CardRequest extends BaseRequest {
    private Long id;

    /**
     * 逻辑卡/物理卡号
     */
    @Schema(description = "逻辑卡号",required = true)
    private String cardNo;
    @Schema(description = "物理卡号",required = true)
    private String cardChipNo;
    @Schema(description = "车牌号",example = "沪A18588")
    private String carNo;
    @Schema(description = "卡名称")
    private String cardName;
    @Schema(description = "车队名称")
    private String carDepart;
    @Schema(description = "线路")
    private String lineNum;
    @Schema(description = "车辆自编号")
    private String carNum;

    @Schema(description = "同步金额")
    private BigDecimal syncAmount;

    // 是否来自制卡软件
    private Boolean cardMaker = true;
    /**
     * 只用于企业平台新增在线卡时，校验组织名称与手机号的绑定关系
     */
    @Schema(description = "组织名称id",required = true)
    private Long corpOrgId;
    /**
     * 1：个人客户
     * 2：集团客户
     */
    private Integer bindType;
    /**
     * 客户手机号
     */
    @Schema(description = "客户手机号",required = true)
    private String mobile;
    /**
     * 创建时间开始
     */
    private String startTime;
    /**
     * 创建时间结束
     */
    private String endTime;
    /**
     * 卡片状态
     * 已激活，已下发，已弃用
     */
    @Schema(description = "卡片状态")
    private String cardStatus;

    @Schema(description = "卡片类型 0-在线卡，4-离线卡")
    private Long cardType;
    /**
     * 查询方式
     * 1、卡号/2、卡名称/3、客户名称/4、客户手机号
     */
    private Integer queryType;
    /**
     * 查询关键字
     */
    private String queryStr;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 企业Id
     */
    private Long corpId;

    private Long topCommId;

    private List<String> cardNoList;
    /**
     * 归属商户ID
     */
    private Long commId;
    /**
     * 在线卡时能传多个id，以','号分割
     * 紧急充电卡只能传一个id
     */
    @Schema(description = "可用场站", required = true)
    private String stations;
    /**
     * 备注
     */
    private String remark;
    /**
     * 站点名称
     */
    private String siteName;
    /**
     * 集团客户ID
     * @deprecated 使用corpId替换
     */
//    @Deprecated
//    private Long merchantId;
    /**
     * 要排除的卡状态
     */
    private List<String> excludeCardStatusList;
    /**
     * 桩编号
     */
    private String evseId;

    private String evse;

    /**
     * 分页做在这里
     */
    private Integer _index;
    private Integer _size;

    private String commIdChain;

    @Schema(description = "所属的场站组")
    private List<String> gids;

    @Schema(description = "当前登录账户组织列表")
    private List<Long> orgIds;

    @Schema(description = "用于转换成idChain")
    private Long leaderCommId;
}