//package com.cdz360.biz.ant.domain.dto.type;
//
//import com.cdz360.base.model.base.type.DcEnum;
//import com.fasterxml.jackson.annotation.JsonCreator;
//import com.fasterxml.jackson.annotation.JsonValue;
//import lombok.Getter;
//
///**
// * MgcAlertStatus
// *
// * @since 10/15/2021 1:14 PM
// * <AUTHOR>
// */
//@Getter
//public enum MgcAlertStatus  implements DcEnum {
//
//    OK(0, "正常"),
//    ABNORMAL(1, "异常") // 异常
//    ;
//
//
//    @JsonValue
//    private final int code;
//    private final String desc;
//
//    MgcAlertStatus(int code, String desc) {
//        this.code = code;
//        this.desc = desc;
//    }
//
//    @JsonCreator
//    public static MgcAlertStatus valueOf(Object codeIn) {
//        if (codeIn == null) {
//            return MgcAlertStatus.ABNORMAL;
//        }
//        int code = 0;
//        if (codeIn instanceof MgcAlertStatus) {
//            return (MgcAlertStatus) codeIn;
//        } else if (codeIn instanceof Integer) {
//            code = ((Integer) codeIn).intValue();
//        } else if (codeIn instanceof Long) {
//            code = ((Long) codeIn).intValue();
//        } else if (codeIn instanceof String) {
//            code = Integer.parseInt((String) codeIn);
//        }
//        for (MgcAlertStatus type : values()) {
//            if (type.code == code) {
//                return type;
//            }
//        }
//        return MgcAlertStatus.ABNORMAL;
//    }
//}