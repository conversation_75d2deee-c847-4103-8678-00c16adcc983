package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.domain.request.PlugDetailRequest;
import com.cdz360.biz.ant.domain.vo.PlugDetailVo;
import com.cdz360.biz.ant.feign.TradingFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since Created on 15:25 2019/5/15.
 */
@Slf4j
@Service
public class TemporaryDateService //implements ITemporaryDateService
{

    @Autowired
    private TradingFeignClient tradingFeignClient;

    public ListResponse<PlugDetailVo> chargerTemporaryDate(PlugDetailRequest plugDetailRequest) {
        log.info(">> Feign 获取充电中枪头详情: request={}", JsonUtils.toJsonString(plugDetailRequest));
        // 重构枪头详情 @Nathan
        ListResponse<PlugDetailVo> result = tradingFeignClient.getPlugDetail(plugDetailRequest);
        log.info("<< Feign 获取充电中枪头详情结束");
        return result;
    }
}
