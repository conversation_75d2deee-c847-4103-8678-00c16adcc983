package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.service.TemplateService;
import com.cdz360.biz.ant.service.sysLog.SiteSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.site.po.PriceTemplatePo;
import com.cdz360.biz.model.site.vo.PriceSchemaVo;
import com.cdz360.biz.model.site.vo.PriceTemplateModVo;
import com.cdz360.biz.model.trading.site.param.AddPriceSchemaParam;
import com.cdz360.biz.model.trading.site.param.FetchTargetPriceSchemeParam;
import com.cdz360.biz.model.trading.site.param.ListPriceTemplateParam;
import com.cdz360.biz.model.trading.site.param.UpdatePriceSchemaParam;
import com.cdz360.biz.model.trading.site.vo.PriceSchemeSiteVo;
import com.cdz360.biz.oa.dto.TargetPriceSchemeInfo;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.CommercialSample;
import com.chargerlinkcar.framework.common.domain.vo.TemplateInfoVo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * 计费模板接口（主要转发设备侧接口）
 * <p>
 * TemplateRest
 *
 * <AUTHOR>
 * @since 2019.3.15
 */
@Slf4j
@RestController
@Tag(name = "计费模板接口", description = "计费模板接口")
@RequestMapping("/api/template")
public class TemplateRest extends BaseController {

    @Autowired
    private TemplateService templateService;
    @Autowired
    private SiteSysLogService siteSysLogService;

    /**
     * 分页获取计费模板列表
     *
     * @param keywords 关键字：计费模板名称
     * @return
     * @deprecated 使用 getPriceSchemaList 替换
     */
    @Operation(summary = "获取计费模板列表")
    @PostMapping(value = "/getPagedTemplateInfoList")
    @Deprecated
    public ListResponse<TemplateInfoVo> getPagedTemplateInfoList(
        ServerWebExchange exh,
        @Parameter(name = "关键词: 计费模板名称模糊查询") @RequestParam(value = "keywords", required = false) String keywords,
        @Parameter(name = "计费模板的使能状态") @RequestParam(value = "enable", required = false) Boolean enable,
        ServerHttpRequest request) {
        log.error("deprecated !!!!! TemplateRest.getPagedTemplateInfoList");
        log.info(LoggerHelper2.formatEnterLog(request));
        String token = getToken2(request);
        OldPageParam page = getPage2(request, exh, true);
        //List<Long> commIdList = getCommIdList(request);
        String commIdChain = super.getCommIdChain2(request);
        Long commId = AntRestUtils.getCommId(request);  // super.getCommIdLong(request);
        log.info("分页获取计费模板列表参数{}----------{}----------token-{}", keywords, commIdChain,
            token);
        return templateService.getPagedTemplateInfoList(keywords, commIdChain, commId, page, token,
            enable);
    }


    @Operation(summary = "获取计费模板列表")
    @PostMapping(value = "/getPriceSchemaList")
    public ListResponse<PriceSchemaVo> getPriceSchemaList(
        ServerHttpRequest request,
        ServerWebExchange exh,
        @ModelAttribute ListPriceTemplateParam param
    ) {
        log.info(LoggerHelper2.formatEnterLog(request));
        OldPageParam page = getPage2(request, exh, true);
        String commIdChain = super.getCommIdChain2(request);
        Long commId = AntRestUtils.getCommId(request);
        param.setCommIdChain(commIdChain)
            .setDeleteFlag(false)
            .setForListQueries(true)
            .setStart((long) (page.getPageNum() - 1) * page.getPageSize())
            .setSize(page.getPageSize());
        log.info("分页获取计费模板列表参数 = {} ", param);
        var res = templateService.getPriceSchemaList(param, commId, super.getCommIdChain2(request));
        return res;
    }


    @Operation(summary = "使用计费模板code获取历史计费模板列表")
    @PostMapping("/getPriceSchemaListByCode")
    public ListResponse<PriceTemplatePo> getPriceSchemaListByCode(ServerHttpRequest request,
        @RequestParam("code") String code) {
        Assert.isTrue(StringUtils.isNotBlank(code), "计费模板编号不能为空");
        ListPriceTemplateParam param = new ListPriceTemplateParam();
        param.setCode(code)
            .setForListQueries(true)
            .setCommIdChain(super.getCommIdChain2(request))
            .setStart(0L).setSize(100);
        ListResponse<PriceTemplatePo> res = templateService.getPriceSchemaListByCode(param);
        return res;
    }

    @Operation(summary = "查看计费模板下发场站统计详情")
    @GetMapping("/getTemplateSendDown")
    public ListResponse<PriceSchemeSiteVo> getTemplateSendDown(
        @Parameter(name = "计费模板Id", required = true) @RequestParam(value = "id") Long id,
        ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request));

        // 需要添加商户信息
        String commIdChain = super.getCommIdChain2(request);

        return templateService.getPriceSchemeSiteInfo(Collections.singletonList(id), commIdChain);
    }

    @Operation(summary = "计费模板启用")
    @GetMapping("/enable")
    public BaseResponse enable(
        @Parameter(name = "计费模板Id", required = true) @RequestParam(value = "id") Long id,
        @Parameter(name = "计费模板名称", required = true) @RequestParam(value = "name") String name,
        ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request));
        templateService.enable(getToken2(request), id, true);
        siteSysLogService.updatePriceSchemaStatus(name, request);
        return RestUtils.success();
    }

    @Operation(summary = "计费模板禁用")
    @GetMapping("/disable")
    public BaseResponse disable(ServerHttpRequest request,
        @Parameter(name = "计费模板Id", required = true) @RequestParam(value = "id") Long id,
        @Parameter(name = "计费模板名称", required = true) @RequestParam(value = "name") String name
    ) {
        log.info(LoggerHelper2.formatEnterLog(request));
        // 未被使用的计费模板以及定时下发的计费模板可以禁用
        templateService.enable(getToken2(request), id, false);
        siteSysLogService.updatePriceSchemaStatus(name, request);
        return RestUtils.success();
    }

    @Operation(summary = "光伏收益计费模板批量删除")
    @PostMapping("/deletePvPriceSchema")
    public BaseResponse deletePvPriceSchema(ServerHttpRequest request,
        @RequestBody List<Long> priceIdList) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return templateService.deletePvPriceSchema(priceIdList);
    }

    @PostMapping("/getPriceSchema")
    @Operation(summary = "获取计费模板信息")
    public ObjectResponse<PriceSchemaVo> getPriceSchema(ServerHttpRequest request,
        @RequestParam("priceId") Long priceId) {
        Assert.notNull(priceId, "计费模板ID不能为空");
        ObjectResponse<PriceSchemaVo> res = templateService.getPriceSchema(priceId,
            AntRestUtils.getCommId(request)
        );
        return res;
    }


    @PostMapping("/addPriceSchema")
    @Operation(summary = "新增计费模板")
    public ObjectResponse<PriceTemplatePo> addPriceSchema(ServerHttpRequest request,
        @RequestBody AddPriceSchemaParam param) {
        Assert.notNull(param.getName(), "模板名称不能为空");
        Assert.notNull(param.getCommId(), "代理商ID不能为空");
        Assert.notNull(param.getFreeChargeFlag(), "免费充电标识不能为空");

        CommercialSample user = super.getCommercialSample2(request);
        param.setTopCommId(user.getTopCommId())
            .setCommIdChain(super.getCommIdChain2(request))
            .setCreatorUserId(user.getId())
            .setCreatorName(user.getName())
            .setCreatorPhone(user.getPhone());
        var res = this.templateService.addPriceSchema(param);

        log.info("新建计费模板: id = {}, code = {}", res.getData().getId(),
            res.getData().getCode());
        siteSysLogService.addPriceSchema(param.getName(), request);
        return res;
    }


    @PostMapping("/updatePriceSchema")
    @Operation(summary = "修改计费模板")
    public ObjectResponse<Long> updatePriceSchema(ServerHttpRequest request,
        @RequestBody UpdatePriceSchemaParam param) {
        Assert.notNull(param.getName(), "模板名称不能为空");
        Assert.notNull(param.getCommId(), "代理商ID不能为空");
        Assert.notNull(param.getFreeChargeFlag(), "免费充电标识不能为空");

        param.setTopCommId(super.getCommercialSample2(request).getTopCommId())
            .setCommIdChain(super.getCommIdChain2(request));
        var res = this.templateService.updatePriceSchema(param);
        siteSysLogService.updatePriceSchema(param.getName(), request);
        log.info("修改计费模板: id = {}", res.getData());
        return res;
    }

    @PostMapping("/fetchTargetPriceScheme")
    @Operation(summary = "获取目标设置价格配置信息")
    public Mono<ListResponse<TargetPriceSchemeInfo>> fetchTargetPriceScheme(
        ServerHttpRequest request, @RequestBody FetchTargetPriceSchemeParam param) {
        log.info("获取目标设置价格配置信息: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), param);
        return templateService.fetchTargetPriceScheme(param);
    }

    @Operation(summary = "获取计费模板的模版列表")
    @PostMapping(value = "/getPriceSchemaModList")
    public ListResponse<PriceTemplateModVo> getPriceSchemaModList(
        ServerHttpRequest request,
        ServerWebExchange exh,
        @ModelAttribute ListPriceTemplateParam param
    ) {
        log.info(LoggerHelper2.formatEnterLog(request));
        OldPageParam page = getPage2(request, exh, true);
        String commIdChain = super.getCommIdChain2(request);
        param.setCommIdChain(commIdChain)
            .setDeleteFlag(false)
            .setForListQueries(false)
            .setStart((long) (page.getPageNum() - 1) * page.getPageSize())
            .setSize(page.getPageSize());
        log.info("分页获取计费模板的模版列表参数 = {} ", param);
        return templateService.getPriceSchemaModList(param);
    }
}
