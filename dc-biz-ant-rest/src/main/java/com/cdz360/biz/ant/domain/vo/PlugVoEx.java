package com.cdz360.biz.ant.domain.vo;

import com.cdz360.base.model.iot.vo.PlugVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * PlugVoEx
 *
 * @since 5/15/2020 3:32 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PlugVoEx extends PlugVo {
    @Schema(description = "桩编号")
    private String evseName;
}