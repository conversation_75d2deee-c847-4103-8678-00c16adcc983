package com.cdz360.biz.ant.domain;
//
//import lombok.Data;
//
//import java.io.Serializable;
//import java.util.Date;
//
///**
// * <AUTHOR>
// *  余额交易记录表
// * @since 2018/11/22 21:44
// */
//@Data
//public class BalanceRecord implements Serializable {
//
//
//    protected static final long serialVersionUID = 1L;
//
//    /**
//     *
//     **/
//    protected Long id;
//
//    /**
//     * 订单Id
//     **/
//    protected Long orderId;
//
//    /**
//     *  交易金额
//     */
//    protected Long tradingAmount;
//
//    /**
//     * 交易前金额
//     */
//    protected Long amountBeforeTrans;
//
//    /**
//     * 交易后金额
//     */
//    protected Long amountAfterTrans;
//
//    /**
//     * 创建时间
//     */
//    protected Date createTime;
//
//    /**
//     * 交易类型 1为充值，2为支出，3.提现，4.保证金提取，5.保证金缴纳，6.充电入账，7充电退款  11实时计费,预付金额退还
//     **/
//    protected Integer transType;
//
//    /**
//     * 来源应用来源(\r\n0:微信渠道,1:APP在线发起充,
//     * 2:APP蓝牙发起充电,app上报离线数据,3:刷卡发起充电,
//     * 4:桩上报离线数据5:桩主,6:互联互通向公有云发起的充电,7
//     * :公有云向互联互通发起的充电,8:公有云)
//     **/
//    private Integer sourceId;
//
//    /**
//     * 用户ID
//     **/
//    private Long uid;
//
//    /**
//     *商户ID
//     **/
//    private Long commId;
//
//    /**
//     * 交易ip地址
//     **/
//    private String ipAddress;
//
//    /**
//     * 交易备注
//     **/
//    private String remark;
//
//    /**
//     * 交易号
//     **/
//    private String tradingNo;
//
//    /**
//     * 第三方订单号
//     **/
//    private String thridTradeNo;
//
//    /**
//     * 支付类型(1.微信刷卡支付,2.微信公众号支付,3.微信扫码支付,4.微信app支付,
//     * 20.支付宝APP支付,21.支付宝手机网页支付40.银联支付,50.线下支付,60.余额支付,70NFC储值卡,80无需支付、100其它)
//     **/
//    private Integer payChannel;
//
//    /**
//     * 状态(1.交易成功,2.交易失败,3.未完成)
//     **/
//    private Integer status;
//
//    /**
//     * 卡Id
//     **/
//    private Long cardId;
//
//    /**
//     * 数据类型 1 用户数据 2 卡数据
//     **/
//    private Integer dataType;
//
//
//    private String openId;
//
//    /**
//     * 交易天数(天)
//     **/
//    private Integer tradingDays;
//
//    /**
//     * 月卡或年卡的生效时间
//     **/
//    private Date takeEffectTime;
//
//    /**
//     * 月卡或年卡的失效时间
//     **/
//    private Date pastDueTime;
//
//    /**
//     * 交易次数
//     **/
//    private Integer tradingTimes;
//
//    /**
//     * 交易前次数
//     **/
//    private Integer timesBeforeTrans;
//
//    /**
//     * 交易后次数
//     **/
//    private Integer timesAfterTrans;
//
//
//    /**
//     *  小程序 消息通知formId
//     */
//    private String formId;
//
//    private String operatorId;
//
//    /**
//     * 操作人
//     */
//    private String operator;
//
//    /**
//     * 账户类型
//     */
//    private String balanceType;
//}
