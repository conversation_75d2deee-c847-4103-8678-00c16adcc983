package com.cdz360.biz.ant.rest.iot;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.ant.feign.DeviceMgmFeignClient;
import com.cdz360.biz.model.iot.vo.TransformerVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * IotTransformerRest
 *
 * @since 1/21/2021 10:53 AM
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/transformer")
@Tag(name = "变压器相关接口", description = "变压器")
public class IotTransformerRest {
    @Autowired
    private DeviceMgmFeignClient deviceMgmFeignClient;
    @GetMapping("/listTransformer")
    public ListResponse<TransformerVo> listTransformer(@RequestParam(value = "keyword", required = false) String keyword,
                                                  @RequestParam(value = "transformerId", required = false) Long transformerId,
                                                  @RequestParam(value = "siteId") String siteId,
                                                  @RequestParam(value = "start") long start,
                                                  @RequestParam(value = "size") long size) {
        log.info("keyword: {}, transformerId: {}, siteId: {}, start: {}, size: {}",
                keyword, transformerId, siteId, start, size);
        return deviceMgmFeignClient.listTransformer(keyword, transformerId, siteId, start, size);
    }

    @PostMapping("/addTransformer")
    public BaseResponse addTransformer(@RequestBody TransformerVo req) {
        log.info("add req: {}", req);
        return deviceMgmFeignClient.addTransformer(req);
    }

    @PostMapping("/editTransformer")
    public BaseResponse editTransformer(@RequestBody TransformerVo req) {
        log.info("req: {}", req);
        return deviceMgmFeignClient.editTransformer(req);
    }

    @GetMapping("/disableTransformer")
    public BaseResponse disableTransformer(@RequestParam(value = "id") long id) {
        log.info("id: {}", id);
        return deviceMgmFeignClient.disableTransformer(id);
    }
}