package com.cdz360.biz.ant.domain.vo;

import com.cdz360.biz.ant.domain.request.PvGroupParam.Group;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class PvRtDataGroupItem {

    /**
     * 组串序号 与{@link Group#idx}值一致
     */
    private Integer idx;

    @Schema(description = "采样点数据(电压单位：V；电流单位：A；)")
    private BigDecimal value;

}
