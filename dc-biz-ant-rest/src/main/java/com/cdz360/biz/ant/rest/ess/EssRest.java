package com.cdz360.biz.ant.rest.ess;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.service.ess.EssBizService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.ess.model.param.ListEssBatteryClusterParam;
import com.cdz360.biz.ess.model.vo.EssEquipBatteryClusterVo;
import com.cdz360.biz.model.ess.param.ListEssEquipParam;
import com.cdz360.biz.model.ess.po.EssPo;
import com.cdz360.biz.model.ess.vo.EssEquipBatteryPackVo;
import com.cdz360.biz.model.ess.vo.EssEquipBatteryStackVo;
import com.cdz360.biz.model.ess.vo.EssEquipVo;
import com.cdz360.biz.model.iot.param.ListCtrlParam;
import com.cdz360.biz.ess.model.dto.EssDto;
import com.cdz360.biz.model.trading.ess.param.ListEssBatteryPackParam;
import com.cdz360.biz.ess.model.param.ListEssParam;
import com.cdz360.biz.model.trading.ess.po.EssEquipPo;
import com.cdz360.biz.model.trading.ess.vo.EssEquipBatteryPackSimpleVo;
import com.cdz360.biz.model.trading.ess.vo.EssEquipPCSVo;
import com.cdz360.biz.model.trading.ess.vo.EssVo;
import com.cdz360.biz.model.trading.iot.vo.EssDataInTimeVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;


@Slf4j
@RestController
@Tag(name = "储能相关接口", description = "储能服务")
@RequestMapping("/api/ess")
public class EssRest {

    @Autowired
    private EssBizService essBizService;

    @Operation(summary = "设置ESS信息")
    @PostMapping(value = "/sendModifyEssCfgCmd")
    public Mono<BaseResponse> sendModifyEssCfgCmd(
        @Parameter(name = "ESS编号", required = true) @RequestParam String dno,
        @Parameter(name = "配置模板ID", required = true) @RequestParam Long cfgId) {
        log.info("下发逆变器配置信息。dno = {}, cfgId = {}", dno, cfgId);
        return essBizService.modifyEssCfg(dno, cfgId)
            .map(res -> RestUtils.success());
    }

    @Operation(summary = "获取光储Ess信息")
    @GetMapping(value = "/getById")
    public Mono<ObjectResponse<EssPo>> getById(@RequestParam(value = "id") Long id) {
        log.info("获取光储ESS信息。id = {}", id);
        if (null == id) {
            throw new DcServiceException("ID不能为空");
        }
        return essBizService.getById(id);
    }

    @Operation(summary = "根据gwno获取光储Ess列表")
    @PostMapping(value = "/getByGwno")
    public Mono<ListResponse<EssDto>> getByGwno(@RequestBody ListCtrlParam param) {
        log.info("根据gwno获取光储Ess列表。param = {}", param);
        return essBizService.getByGwno(param);
    }

    @Operation(summary = "ESS同步网元指令下发")
    @GetMapping(value = "/syncEssEquip")
    public Mono<BaseResponse> syncEssEquip(@RequestParam(value = "dno") String dno) {
        log.info("ESS同步网元指令下发 dno: {}", dno);
        return essBizService.syncEssEquip(dno);
    }

    @Operation(summary = "ESS实时数据获取")
    @GetMapping(value = "/essInfoInTime")
    public Mono<ObjectResponse<EssDataInTimeVo>> essInfoInTime(
        @RequestParam(value = "dno") String dno) {
        log.info("ESS实时数据获取 dno: {}", dno);
        return essBizService.essInfoInTime(dno);
    }

    @Operation(summary = "获取光储Ess列表")
    @PostMapping(value = "/findEssList")
    public Mono<ListResponse<EssVo>> findEssList(
        ServerHttpRequest request,
        @RequestBody ListEssParam param) {
        log.info("获取光储ESS列表。param = {}", param);
        String idChain = AntRestUtils.getCommIdChain(request);
        if (null == idChain) {
            throw new DcServiceException("未登录状态");
        }
        param.setCommIdChain(idChain);
        return essBizService.findEssList(param);
    }

    @Operation(summary = "获取光储Ess下挂载的所有设备")
    @PostMapping(value = "/findEquipList")
    public Mono<ListResponse<EssEquipVo>> findEquipList(@RequestBody ListCtrlParam param) {
        log.info("获取光储Ess下挂载的所有设备。param = {}", param);
        return essBizService.findEquipList(param);
    }

    @Operation(summary = "查询站点下获取光储Ess下挂载的所有设备")
    @GetMapping(value = "/findEquipListBySiteId")
    public Mono<ListResponse<EssEquipPo>> findEquipListBySiteId(
        @RequestParam(value = "siteId") String siteId,
        @RequestParam(value = "equipType", required = false) EssEquipType equipType) {
        log.info("查询站点下获取光储Ess下挂载的所有设备。siteId = {}, equipType = {}", siteId,
            equipType);
        return essBizService.findEquipListBySiteId(siteId, equipType);
    }

    @Operation(summary = "获取光储Ess挂载设备(PCS)信息列表")
    @PostMapping(value = "/findEquipPCSList")
    public Mono<ListResponse<EssEquipPCSVo>> findEquipPCSList(
        @RequestBody ListEssEquipParam param) {
        log.info("获取光储Ess挂载设备(PCS)信息列表。param = {}", param);
        return essBizService.findEquipPCSList(param);
    }

    @Operation(summary = "获取光储Ess挂载设备(电池堆)信息列表")
    @PostMapping(value = "/findEquipBatteryStackList")
    public Mono<ListResponse<EssEquipBatteryStackVo>> findEquipBatteryStackList(
        @RequestBody ListEssEquipParam param) {
        log.info("获取光储Ess挂载设备(电池堆)信息列表。param = {}", param);
        return essBizService.findEquipBatteryStackList(param);
    }

    @Operation(summary = "获取电池簇列表")
    @PostMapping(value = "/findEquipBatteryClusterList")
    public Mono<ListResponse<EssEquipBatteryClusterVo>> findEquipBatteryClusterList(
        @RequestBody ListEssBatteryClusterParam param) {
        log.info("获取电池簇列表: param = {}", JsonUtils.toJsonString(param));
        return essBizService.findEquipBatteryClusterList(param);
    }

    @Operation(summary = "获取光储Ess挂载设备(电池组)信息列表")
    @PostMapping(value = "/findEquipBatteryPackList")
    public Mono<ListResponse<EssEquipBatteryPackVo>> findEquipBatteryPackList(
        ServerHttpRequest request,
        @RequestBody ListEssBatteryPackParam param) {
        log.info("获取光储Ess挂载设备(电池组)信息列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request),
            param);
        return essBizService.findEquipBatteryPackList(param);
    }

    @Operation(summary = "根据电池堆获取电池簇列表")
    @GetMapping(value = "/findBatteryClusterNosByBatteryStack")
    public Mono<ListResponse<Long>> findBatteryClusterNosByBatteryStack(
        @RequestParam(value = "essDno") String essDno,
        @RequestParam(value = "batteryStackId") Long batteryStackId) {
        log.info("根据电池堆获取电池簇列表。essDno = {},batteryStackId = {}", essDno);
        return essBizService.findBatteryClusterNosByBatteryStack(essDno, batteryStackId);
    }

    @Operation(summary = "根据电池簇号获取电池组列表")
    @GetMapping(value = "/findBatteryPackNosByBatteryClusterNo")
    public Mono<ListResponse<EssEquipBatteryPackSimpleVo>> findBatteryPackNosByBatteryClusterNo(
        @RequestParam(value = "essDno") String essDno,
        @RequestParam(value = "batteryStackId") Long batteryStackId,
        @RequestParam(value = "batteryClusterNo") Long batteryClusterNo) {
        log.info("根据电池簇号获取电池组列表。essDno = {},batteryStackId = {},batteryClusterNo = {}",
            essDno,
            batteryStackId, batteryClusterNo);
        return essBizService.findBatteryPackNosByBatteryClusterNo(essDno, batteryStackId,
            batteryClusterNo);
    }

    @Operation(summary = "根据essDno获取电池组列表")
    @PostMapping(value = "/findBatteryPackSimpleVoList")
    public Mono<ListResponse<EssEquipBatteryPackSimpleVo>> findBatteryPackSimpleVoList(
        @RequestBody ListEssBatteryPackParam param) {
        return essBizService.findBatteryPackNosByBatteryClusterNo(param);
    }

}
