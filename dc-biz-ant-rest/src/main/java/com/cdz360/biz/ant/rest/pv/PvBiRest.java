package com.cdz360.biz.ant.rest.pv;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.domain.request.PvGroupParam;
import com.cdz360.biz.ant.domain.vo.PvRtDailyPower;
import com.cdz360.biz.ant.domain.vo.PvRtDataGroupSampling;
import com.cdz360.biz.ant.domain.vo.PvRtDataPowerSampling;
import com.cdz360.biz.ant.service.pv.PvBiService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.iot.dto.PvProfileDto;
import com.cdz360.biz.model.trading.iot.dto.SitePvProfileDto;
import com.cdz360.biz.ess.model.data.param.DataBiParam;
import com.cdz360.biz.model.trading.iot.param.PvProfitTrendParam;
import com.cdz360.biz.model.trading.iot.vo.DayPvDataBi;
import com.cdz360.biz.model.trading.iot.vo.DaySitePvRtDataBi;
import com.cdz360.biz.model.trading.iot.vo.GtiSampleData;
import com.cdz360.biz.model.trading.iot.vo.GtiStatusBi;
import com.cdz360.biz.model.trading.iot.vo.PvRtDataPowerProfit;
import com.cdz360.biz.model.trading.site.vo.SiteWeatherVo;
import com.cdz360.biz.utils.feign.auth.AuthCommFeignClient;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "光伏相关接口", description = "光伏相关接口")
@RequestMapping(value = "/api/pvBi")
public class PvBiRest {

    @Autowired
    private PvBiService pvBiService;

    @Autowired
    private AuthCommFeignClient authCommFeignClient;

    @Operation(summary = "站点详情-近7日发电量统计数据")
    @GetMapping(value = "/powerGeneration7")
    public Mono<ListResponse<DayPvDataBi>> powerGeneration7(
        @RequestParam(value = "siteId") String siteId) {
        log.info("powerGeneration7. siteId: {}", siteId);
        return pvBiService.powerGeneration7(siteId);
    }

    @Operation(summary = "站点详情-光伏发电相关数据")
    @GetMapping(value = "/sitePvProfile")
    public Mono<ObjectResponse<SitePvProfileDto>> sitePvProfile(
        @RequestParam(value = "siteId") String siteId) {
        log.info("sitePvProfile. siteId: {}", siteId);
        return pvBiService.sitePvProfile(siteId);
    }

    @Operation(summary = "站点详情-天气")
    @GetMapping(value = "/siteWeather")
    public Mono<ObjectResponse<SiteWeatherVo>> siteWeather(
        @RequestParam(value = "siteId") String siteId) {
        log.info("siteWeather. siteId: {}", siteId);
        return pvBiService.siteWeather(siteId);
    }

    @Operation(summary = "站点详情-功率曲线")
    @GetMapping(value = "/powerCurve")
    public Mono<ListResponse<PvRtDataPowerSampling>> powerCurve(
        @RequestParam(value = "siteId") String siteId,
        @RequestParam(value = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        log.info("powerCurve. siteId: {}, date: {}", siteId, date);
        return pvBiService.powerCurve(siteId, date);
    }

    @Operation(summary = "逆变器运行数据采样")
    @PostMapping(value = "/gtiRtDataSample")
    public Mono<ListResponse<GtiSampleData>> gtiRtDataSample(
        ServerHttpRequest request,
        @RequestBody DataBiParam param) {
        log.info("逆变器运行数据采样: param = {}", JsonUtils.toJsonString(param));
        param.setCommIdChain(AntRestUtils.getCommIdChainAndFilter(request));
        return this.pvBiService.gtiRtDataSample(param);
    }

    @Operation(summary = "电量收益趋势")
    @PostMapping(value = "/powerProfitTrend")
    public Mono<ListResponse<PvRtDataPowerProfit>> powerProfitTrend(ServerHttpRequest request,
        @RequestBody PvProfitTrendParam param) {
        log.info("powerProfitTrend. param: {}", JsonUtils.toJsonString(param));
        param.setCommIdChain(AntRestUtils.getCommIdChainAndFilter(request));
        return pvBiService.powerProfitTrend(param);
    }

    @Operation(summary = "电量收益趋势到excel")
    @PostMapping(value = "/exportPowerProfitTrend")
    public Mono<ObjectResponse<ExcelPosition>> exportPowerProfitTrend(
        ServerHttpRequest request,
        @RequestBody PvProfitTrendParam param) {
        log.info("powerProfitTrend. param: {}", JsonUtils.toJsonString(param));
        return pvBiService.exportPowerProfitTrend(request, param);
    }

    @Operation(summary = "商户首页-光伏站发电概况,可切换商户")
    @GetMapping(value = "/powerGenerationProfile")
    public Mono<ObjectResponse<PvProfileDto>> powerGenerationProfile(ServerHttpRequest request,
        @RequestParam(value = "siteId", required = false) String siteId,
        @RequestParam(value = "commId", required = false) Long commId) {
        log.info("powerGenerationProfile. {}", LoggerHelper2.formatEnterLog(request));

        return Mono.justOrEmpty(commId)
            .flatMap(authCommFeignClient::getCommercial)
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .map(Commercial::getIdChain)
            .switchIfEmpty(Mono.just(AntRestUtils.getCommIdChainAndFilter(request)))
            .flatMap(commIdChain -> pvBiService.powerGenerationProfile(commIdChain, siteId));
    }

    @Operation(summary = "商户首页-按月查询发电统计数据")
    @GetMapping(value = "/powerGenerationByMonth")
    public Mono<ListResponse<DaySitePvRtDataBi>> powerGenerationByMonth(
        ServerHttpRequest request,
        @Parameter(name = "年份") @RequestParam(value = "year", required = false) Integer year,
        @Parameter(name = "商户ID") @RequestParam(value = "commId", required = false) Long commId,
        @Parameter(name = "月份") @RequestParam(value = "month") Integer month) {
        log.info("powerGenerationByMonth. {}", LoggerHelper2.formatEnterLog(request));

        return Mono.justOrEmpty(commId)
            .flatMap(authCommFeignClient::getCommercial)
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .map(Commercial::getIdChain)
            .switchIfEmpty(Mono.just(AntRestUtils.getCommIdChainAndFilter(request)))
            .flatMap(commIdChain -> pvBiService.powerGenerationByMonth(commIdChain, year, month));
    }

    @Operation(summary = "桩管家，定制大屏-按年份或最近n月查询发电统计数据")
    @GetMapping(value = "/powerGenerationByYear")
    public Mono<ListResponse<DaySitePvRtDataBi>> powerGenerationByYear(
        ServerHttpRequest request,
        @Parameter(name = "年份") @RequestParam(value = "year", required = false) Integer year,
        @Parameter(name = "最近几个月份") @RequestParam(value = "recentMonth", required = false) Integer recentMonth,
        @Parameter(name = "场站") @RequestParam(value = "siteId", required = false) String siteId,
        @Parameter(name = "商户ID") @RequestParam(value = "commId", required = false) Long commId) {
        log.info("powerGenerationByYear. {}", LoggerHelper2.formatEnterLog(request));

        return Mono.justOrEmpty(commId)
            .flatMap(authCommFeignClient::getCommercial)
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .map(Commercial::getIdChain)
            .switchIfEmpty(Mono.just(AntRestUtils.getCommIdChainAndFilter(request)))
            .flatMap(
                commIdChain -> pvBiService.powerGenerationByYear(commIdChain, year, recentMonth,
                    siteId));
    }

    @Operation(summary = "定制大屏-获取场站下近n天的发电量及收益(不含当前日)")
    @GetMapping(value = "/powerGenerationByRecentDays")
    public Mono<ListResponse<DaySitePvRtDataBi>> powerGenerationByRecentDays(
        ServerHttpRequest request,
        @RequestParam(value = "siteId", required = false) String siteId,
        @Parameter(name = "近n天") @RequestParam(value = "recentDays") Integer recentDays) {
        log.info("powerGenerationByRecentDays. {} ", LoggerHelper2.formatEnterLog(request));
        return pvBiService.powerGenerationByRecentDays(AntRestUtils.getCommIdChain(request),
            recentDays, siteId);
    }

    @Operation(summary = "获取逆变器状态统计数据")
    @GetMapping(value = "/getGtiStatusBi")
    public Mono<ListResponse<GtiStatusBi>> getGtiStatusBi(ServerHttpRequest request,
        @RequestParam(value = "siteId", required = false) String siteId) {
        log.info("getGtiStatusBi. {}", LoggerHelper2.formatEnterLog(request));
        return pvBiService.getGtiStatusBi(AntRestUtils.getCommIdChainAndFilter(request), siteId);
    }

    @Operation(summary = "商户首页-发电功率对比(近三日)")
    @GetMapping(value = "/powerSampling")
    public Mono<ListResponse<PvRtDailyPower>> powerSampling(ServerHttpRequest request,
        @RequestParam(value = "siteId", required = false) String siteId,
        @RequestParam(value = "commId", required = false) Long commId) {
        log.info("powerSampling. {}", LoggerHelper2.formatEnterLog(request));

        return Mono.justOrEmpty(commId)
            .flatMap(authCommFeignClient::getCommercial)
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .map(Commercial::getIdChain)
            .switchIfEmpty(Mono.just(AntRestUtils.getCommIdChainAndFilter(request)))
            .flatMap(commIdChain -> pvBiService.powerSampling(commIdChain, siteId));
    }

    @Operation(summary = "获取光伏组串数据")
    @PostMapping(value = "/getGroupInfo")
    public Mono<ListResponse<PvRtDataGroupSampling>> getGroupInfo(@RequestBody PvGroupParam param) {
        log.info("getGroupInfo. param = {}", param);
        return pvBiService.getGroupInfo(param)
            .map(RestUtils::buildListResponse);
    }

}
