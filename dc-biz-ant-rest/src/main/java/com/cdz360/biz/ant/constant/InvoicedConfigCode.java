package com.cdz360.biz.ant.constant;

/**
 * The InvoicedConfig enumeration.
 * 发票控制配置项
 */
public enum InvoicedConfigCode {

    IS_OPEN("开票功能开关", "IS_OPEN"),
    MIN_AA("免邮最小金额", "MIN_AA"),//免邮金额
    MIN_DAYS("开票最小时间", "MIN_DAYS"),//延迟时间
    MAX_MONTHS("可开票时间", "MAX_MONTHS"),//有效时间
    MAX_APY_AA("开票审核金额", "MAX_APY_AA"),//开票无需审核的金额上限
    //SEND_AA("免邮金额","SEND_AA"),//免邮金额
    PRINT_NAME("物流单打印机名称", "PRINT_NAME")//物流单打印机, 值保存在DESC 字段中
    ;

    private final String desc;
    private final String code;

    InvoicedConfigCode(String desc, String code) {
        this.desc = desc;
        this.code = code;
    }

    public final String getCode() {
        return this.code;
    }

    public final String getDesc() {
        return this.desc;
    }
}
