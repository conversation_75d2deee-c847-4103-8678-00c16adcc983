package com.cdz360.biz.ant.service.sysLog;

import com.cdz360.base.model.base.vo.KvAny;
import com.cdz360.base.model.base.vo.KvObject;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.auth.user.type.LogOpType;
import com.cdz360.biz.model.trading.invoice.po.CorpInvoiceRecordPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 企业相关的系统操作日志
 */
@Slf4j
@Service
public class CorpSysLogService {
    @Autowired
    private BaseSysLogService sysUserLogService;

    /**
     * 新增企业客户日志
     */
    public void insertBlocUserLog(String corpName, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                KvAny.of("企业名称", corpName),
                request);
    }

    /**
     * 修改企业客户日志
     */
    public void updateBlocUserLog(String corpName, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("企业名称", corpName),
                request);
    }

    /**
     * 企业客户禁用、启用日志
     */
    public void deleteOrEnableLog(String corpName, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY_STATUS,
                KvAny.of("企业名称", corpName),
                request);
    }


    /**
     * 企业新增/更新/逻辑删除授信账户
     */
    public void addUpdateCorpCreditAccountLog(String phone, String type, ServerHttpRequest request) {
        LogOpType opType = LogOpType.UNKNOWN;
        if (StringUtils.equals(type, LogOpType.CREATE.name())) {
            opType = LogOpType.CREATE;
        } else if (StringUtils.equals(type, LogOpType.MODIFY.name())) {
            opType = LogOpType.MODIFY;
        } else if (StringUtils.equals(type, LogOpType.DISABLE.name())) {
            opType = LogOpType.DISABLE;
        }
        this.sysUserLogService.buildOpLog(opType,
                KvAny.of("授信手机号", phone),
                request);
    }

    /**
     * 企业批量删除授信账户
     */
    public void batchDisableCreditAccount(List<String> phone, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.DISABLE,
                List.of(new KvObject("授信手机号", phone)),
                request);
    }

    /**
     * 企业批量新增授信账户
     */
    public void batchAddCreditAccount(List<String> phone, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.CREATE,
                List.of(new KvObject("授信手机号", phone)),
                request);
    }


    /**
     * 新增卡片
     * 日志
     */
    public void grantCard(List<String> cardChipNo, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.CREATE,
                List.of(new KvObject("在线卡号", cardChipNo)),
                request);
    }

    /**
     * 修改卡片
     * 日志
     */
    public void modifyCard(List<String> cardChipNo, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.MODIFY,
                List.of(new KvObject("在线卡号", cardChipNo)),
                request);
    }

    /**
     * 修改卡片状态
     * 日志
     */
    public void modifyCardStatus(String cardChipNo, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.MODIFY_STATUS,
                List.of(new KvObject("在线卡号", cardChipNo)),
                request);
    }

    /**
     * 删除卡片
     * 日志
     */
    public void delCard(List<String> cardNoList, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.DISABLE,
                List.of(new KvObject("在线卡号", cardNoList)),
                request);
    }

    /**
     * 新增VIN
     * 日志
     */
    public void grantVin(List<String> vinList, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.CREATE,
                List.of(new KvObject("VIN码", vinList)),
                request);
    }

    /**
     * 修改VIN
     * 日志
     */
    public void modifyVin(List<String> vinList, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.MODIFY,
                List.of(new KvObject("VIN码", vinList)),
                request);
    }

    /**
     * 修改卡片状态
     * 日志
     */
    public void modifyVinStatus(String cardChipNo, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.MODIFY_STATUS,
                List.of(new KvObject("VIN码", cardChipNo)),
                request);
    }

    /**
     * 删除卡片
     * 日志
     */
    public void delVin(List<String> cardNoList, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.DISABLE,
                List.of(new KvObject("VIN码", cardNoList)),
                request);
    }

    /**
     * 新增组织
     * 日志
     */
    public void addOrg(String orgName, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.CREATE,
                List.of(new KvObject("组织名", orgName)),
                request);
    }

    /**
     * 编辑组织
     * 日志
     */
    public void editOrg(String orgName, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.MODIFY,
                List.of(new KvObject("组织名", orgName)),
                request);
    }

    /**
     * 新增角色
     * 日志
     */
    public void roleAdd(String roleName, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                KvAny.of("角色名", roleName),
                request);
    }

    /**
     * 修改角色
     * 日志
     */
    public void roleModify(String roleName, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("角色名", roleName),
                request);
    }

    /**
     * 停用启用角色
     * 日志
     */
    public void roleModifyStatus(String roleName, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY_STATUS,
                KvAny.of("角色名", roleName),
                request);
    }

    /**
     * 根据企业id设置续费提醒金额
     */
    public void setRenewReminderAmount(String corpName, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("企业名称", corpName),
                request);
    }

    public void createDepositOrder(String corpName, BigDecimal amount, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.RECHARGE,
                List.of(new KvObject("企业名称", corpName), new KvObject("充值金额", amount + "(元)")),
                request);
    }

    /**
     * 财务-账单管理-新增账单
     * 日志
     */
    public void settlementAddLog(String billNo, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                KvAny.of("账单号", billNo),
                request);
    }

    /**
     * 财务-账单管理-修改账单
     * 日志
     */
    public void settlementEditLog(String billNo, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("账单号", billNo),
                request);
    }

    /**
     * 财务-账单管理-删除账单
     * 日志
     */
    public void settlementRemoveLog(String billNo, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.DISABLE,
                KvAny.of("账单号", billNo),
                request);
    }

    /**
     * 企业客户开票追加订单
     * 日志
     */
    public void corpInvoiceAddLog(String applyNo, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                KvAny.of("企业开票单号", applyNo),
                request);
    }

    /**
     * 企业客户开票追加订单
     * 日志
     */
    public void corpInvoiceEditLog(String applyNo, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("企业开票单号", applyNo),
                request);
    }

    /**
     * 企业客户开票移除订单
     * 日志
     */
    public void corpInvoiceRemoveOrderLog(String applyNo, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("企业开票单号", applyNo),
                request);
    }

    /**
     * 企业客户开票提交审核
     * 日志
     */
    public void corpInvoiceRecordSubmit2AuditLog(String applyNo, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY_STATUS,
                KvAny.of("企业开票单号", applyNo),
                request);
    }

    /**
     * 删除企业开票申请
     * 日志
     */
    public void deleteCorpInvoiceRecordByApplyNoLog(String applyNo, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.DISABLE,
                KvAny.of("企业开票单号", applyNo),
                request);
    }

    /**
     * 企业开票记录修改回款状态
     * 日志
     */
    public void updateCorpInvoiceRecordReturnFlagLog(CorpInvoiceRecordPo po, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("企业开票单号", po.getApplyNo()),
                request);
    }

    /**
     * 审核企业开票
     * 日志
     */
    public void corpInvoiceRecordAuditLog(String applyNo, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.REVIEW,
                KvAny.of("企业开票单号", applyNo),
                request);
    }

    public void createCorpStrategy(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.CREATE,
                List.of(new KvObject("策略名", name)),
                request);
    }

    public void updateCorpStrategy(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.MODIFY,
                List.of(new KvObject("策略名", name)),
                request);
    }


    public void deleteCorpStrategy(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.DISABLE,
                List.of(new KvObject("策略名", name)),
                request);
    }

    public void corpAccountRefund(String corpName, ServerHttpRequest request) {
        this.sysUserLogService.buildObjectOpLog(LogOpType.REFUND,
                List.of(new KvObject("企业", corpName)),
                request);
    }
}
