/*
 *
 * Payment.java
 *
 * @since 2018-11-26
 */
package com.cdz360.biz.ant.domain;
//
//import com.chargerlinkcar.framework.common.domain.BasePage;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
//import java.io.Serializable;
//import java.util.Date;
//
///**
// * <AUTHOR>
// */
//@Data
//@EqualsAndHashCode(callSuper = true)
//public class Payment extends BasePage implements Serializable {
//    /**
//     * 支付ID
//     */
//    private String payId;
//
//    /**
//     * 订单编号
//     */
//    private String orderId;
//
//    /**
//     * 支付时间
//     */
//    private Date payTime;
//
//    /**
//     * 支付凭证ID
//     */
//    private String payCertificateId;
//
//    /**
//     * 支付状态,1:待支付; 2:已支付;3: 支付失败
//     */
//    private Integer payStatus;
//
//    /**
//     * 支付方式 (1~19微信 20~39支付宝 40银联 ，50线下支付,60余额支付,70NFC储值卡,80无需支付、90集团支付、100其它预留)
//     */
//    private Integer payModes;
//
//    /**
//     * 应付金额
//     */
//    private Long payablePrice;
//
//    /**
//     * 实付金额
//     */
//    private Long actualPrice;
//
//    /**
//     * 应用来源,0:微信渠道;1:APP在线发起充电;2:APP蓝牙发起充电:3:刷卡充电:4:桩上报离线数据
//     */
//    private Integer channelId;
//
//    /**
//     * 客户编号
//     */
//    private Long customerId;
//
//    /**
//     * 商户编号
//     */
//    private Long commercialId;
//
//    /**
//     * 支付说明
//     */
//    private String payRemark;
//
//    /**
//     * 创建时间
//     */
//    private Date createTime;
//
//    /**
//     * 业务类型(1为充值，2为余额抵扣（支出），3.提现，4.保证金提取（保证金提现），5.保证金缴纳，6.充电入账，7充电退款）
//     */
//    private Integer businessType;
//
//    /**
//     * 对账单id
//     */
//    private String billId;
//
//    /**
//     * 平台类型（1公有云，2互联互通，3第三方)
//     */
//    private Integer platformType;
//
//    /**
//     * 卡ID
//     */
//    private Long cardId;
//
//    /**
//     * 数据类型 1 用户数据 2 卡数据
//     */
//    private Integer dataType;
//
//    /**
//     * 卡ID
//     */
//    private String cardNo;
//
//    /**
//     * 开始时间
//     */
//    private String beginTime;
//
//    /**
//     * 结束时间
//     */
//    private String endTime;
//
//    /**
//     * 用户名
//     */
//    private String userName;
//
//    /**
//     * 设备运营商ID
//     */
//    private Long deviceCommercialId;
//
//    /**
//     * t_payment
//     */
//    private static final long serialVersionUID = 1L;
//
//
//}