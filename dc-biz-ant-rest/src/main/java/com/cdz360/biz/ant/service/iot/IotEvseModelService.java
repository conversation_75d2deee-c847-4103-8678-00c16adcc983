package com.cdz360.biz.ant.service.iot;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.trading.iot.po.EvseModelPo;
import com.chargerlinkcar.framework.common.domain.param.ListEvseModelParam;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmEvseModelFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class IotEvseModelService {

    @Autowired
    private IotDeviceMgmEvseModelFeignClient evseModelFeignClient;

    public ListResponse<EvseModelPo> listEvseBundlePage(ListEvseModelParam param) {
        return evseModelFeignClient.listEvseBundlePage(param);
    }

    public BaseResponse addEvseModel(EvseModelPo po) {
        return evseModelFeignClient.addEvseModel(po);
    }

    public BaseResponse editEvseModel(EvseModelPo po) {
        return evseModelFeignClient.editEvseModel(po);
    }

    public BaseResponse changeStatus(long id, boolean enable) {
        return evseModelFeignClient.changeStatus(id, enable);
    }

    public BaseResponse remove(long id) {
        return evseModelFeignClient.remove(id);
    }

    public ListResponse<String> getBrandList() {
        return evseModelFeignClient.getBrandList();
    }

    public ObjectResponse<EvseModelPo> findById(Long id) {
        return evseModelFeignClient.findById(id);
    }

}
