package com.cdz360.biz.ant.domain.bundle;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.biz.model.upgradepg.type.BundleType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "升级包参数")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class EvseBundleParam extends BaseListParam {

    private Boolean pageFlag;

    @Schema(description = "升级包类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<BundleType> typeList;

    @Schema(description = "状态（0已停用；1正常；）")
    private List<Integer> statusList;

    private List<String> evseNoList;

//    public Boolean getPageFlag() {
//        return pageFlag;
//    }
//
//    public EvseBundleParam setPageFlag(Boolean pageFlag) {
//        this.pageFlag = pageFlag;
//        return this;
//    }
//
//    public List<String> getEvseNoList() {
//        return evseNoList;
//    }
//
//    public EvseBundleParam setEvseNoList(List<String> evseNoList) {
//        this.evseNoList = evseNoList;
//        return this;
//    }
}
