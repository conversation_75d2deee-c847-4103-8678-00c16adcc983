package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.domain.park.dto.ParkingLockDto;
import com.cdz360.biz.ant.domain.park.param.ListParkingLockParam;
import com.cdz360.biz.ant.domain.park.vo.ParkingLockEventLogVo;
import com.cdz360.biz.ant.domain.park.vo.ParkingLockVo;
import com.cdz360.biz.model.sim.param.ListSimParam;
import com.cdz360.biz.model.sim.param.ModifyRelationParam;
import com.cdz360.biz.model.sim.po.SimPo;
import com.cdz360.biz.model.sim.vo.SimImportItem;
import com.cdz360.biz.model.sim.vo.SimTinyVo;
import com.cdz360.biz.model.sim.vo.SimVo;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_IOT_DEVICE_MGM,
    fallbackFactory = ReactorDeviceMgmFeignHystrix.class)
public interface ReactorDeviceMgmFeignClient {

    // 获取地锁列表
    @PostMapping(value = "/device/mgm/parkingLock/parkingLockList")
    Mono<ListResponse<ParkingLockVo>> parkingLotList(@RequestBody ListParkingLockParam param);

    // 添加地锁
    @PostMapping(value = "/device/mgm/parkingLock/addParkingLock")
    Mono<ObjectResponse<ParkingLockVo>> addParkingLock(@RequestBody ParkingLockDto dto);

    // 删除地锁
    @PostMapping(value = "/device/mgm/parkingLock/removeParkingLock")
    Mono<ObjectResponse<ParkingLockVo>> removeParkingLock(
        @RequestParam(value = "lockId") Long lockId);

    // 获取地锁近20天事件日志
    @GetMapping(value = "/device/mgm/parkingLock/eventLog/recent20")
    Mono<ListResponse<ParkingLockEventLogVo>> parkingLockEventLogRecent20(
        @RequestParam(value = "parkingLockId") Long parkingLockId);

    // 获取SIM卡信息
    @PostMapping(value = "/device/mgm/sim/getList")
    Mono<ListResponse<SimVo>> getSimList(@RequestBody ListSimParam param);

    // SIM卡下拉列表（极简）
    @PostMapping(value = "/device/mgm/sim/getSimTinyList")
    Mono<ListResponse<SimTinyVo>> getSimTinyList(@RequestBody ListSimParam param);

    // 同步增量卡
    @GetMapping(value = "/device/mgm/sim/syncAll")
    Mono<BaseResponse> syncAll();

    // 同步单个SIM信息
    @GetMapping(value = "/device/mgm/sim/sync")
    Mono<ObjectResponse<SimPo>> syncSim(@RequestParam(value = "simId") Long simId);

    // 修改关系
    @PostMapping(value = "/device/mgm/sim/modifyRelation")
    Mono<BaseResponse> modifyRelation(@RequestBody ModifyRelationParam param);

    // 批量修改SIM和桩的绑定关系
    @PostMapping(value = "/device/mgm/sim/batchModifyRelation")
    Mono<BaseResponse> batchModifyRelation(@RequestBody List<SimImportItem> list);

}
