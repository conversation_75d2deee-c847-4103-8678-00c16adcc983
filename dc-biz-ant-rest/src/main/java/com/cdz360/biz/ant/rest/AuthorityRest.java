package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.service.UserService;
import com.cdz360.biz.ant.service.sysLog.CustomerSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.chargerlinkcar.framework.common.domain.SysUser;
import com.chargerlinkcar.framework.common.domain.TCommercialUser;
import com.chargerlinkcar.framework.common.domain.UserCommRef;
import com.chargerlinkcar.framework.common.domain.request.AddAuthorityGroupReq;
import com.chargerlinkcar.framework.common.domain.request.AddAuthorityGroupRequest;
import com.chargerlinkcar.framework.common.domain.request.AddUserGroupRequest;
import com.chargerlinkcar.framework.common.domain.request.AddUserRequest;
import com.chargerlinkcar.framework.common.domain.vo.Authority;
import com.chargerlinkcar.framework.common.domain.vo.AuthorityGroup;
import com.chargerlinkcar.framework.common.domain.vo.UserPropVO;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;

import java.util.stream.Collectors;

/**
 * @since 2020/2/14 17:23
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/authority")
@Tag(name = "/api/authority", description = "权限相关接口")
public class AuthorityRest {
    @Autowired
    AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private UserService userService;
    @Autowired
    private CustomerSysLogService customerSysLogService;

    @GetMapping("/getAuthorityList")
    @Operation( summary = "获取全部权限列表")
    public ListResponse<Authority> getAuthorityList(ServerHttpRequest request) {
        return authCenterFeignClient.getAuthorityList(AntRestUtils.getToken2(request));
    }

    @PostMapping("/addGroup")
    @Operation( summary = "增加/编辑权限组")
    public BaseResponse AddGroup(ServerHttpRequest request,
                                 @RequestBody AddAuthorityGroupReq addAuthorityGroupReq) {

        // 应前端需求
        log.info("增加/编辑权限组: {}", JsonUtils.toJsonString(addAuthorityGroupReq));

        AddAuthorityGroupRequest addAuthorityGroupRequest = new AddAuthorityGroupRequest();
        addAuthorityGroupRequest.setAuthorityList(
                addAuthorityGroupReq.getAuthorityList().stream().map(e -> {
                    Authority authority = new Authority();
                    authority.setId(e);
                    return authority;
                }).collect(Collectors.toList())
        );

        AuthorityGroup authorityGroup = new AuthorityGroup();
        authorityGroup.setCreateTime(addAuthorityGroupReq.getCreateTime());
        authorityGroup.setGroupDesc(addAuthorityGroupReq.getGroupDesc());
        authorityGroup.setGroupName(addAuthorityGroupReq.getGroupName());
        authorityGroup.setId(addAuthorityGroupReq.getId());
        authorityGroup.setOpId(addAuthorityGroupReq.getOpId());
        authorityGroup.setOpName(addAuthorityGroupReq.getOpName());

        addAuthorityGroupRequest.setAuthorityGroup(authorityGroup);

        return authCenterFeignClient.AddGroup(AntRestUtils.getToken2(request), addAuthorityGroupRequest);
    }

    @GetMapping("/getGroupList")
    @Operation( summary = "获得全部权限组")
    public ListResponse<AuthorityGroup> getGroups(ServerHttpRequest request) {
        return authCenterFeignClient.getGroups(AntRestUtils.getToken2(request));
    }

    @GetMapping("/getGroupById")
    @Operation( summary = "通过ID获取权限组")
    public ObjectResponse<AuthorityGroup> getGroupById(ServerHttpRequest request,
                                                       @RequestParam("id") Long id) {
        return authCenterFeignClient.getGroupById(AntRestUtils.getToken2(request), id);
    }

    @DeleteMapping("/deleteGroupById")
    @Operation( summary = "通过Id删除权限组")
    public BaseResponse deleteGroupById(ServerHttpRequest request,
                                        @RequestParam("id") Long id) {
        return authCenterFeignClient.deleteGroupById(AntRestUtils.getToken2(request), id);
    }

    @GetMapping("/getUserAuthoritiesByUid")
    @Operation( summary = "通过uid获取用户权限")
    public ListResponse<Authority> getUserAuthoritiesByUid(ServerHttpRequest request,
                                                           @RequestParam("id") Long uid) {
        return authCenterFeignClient.getUserAuthoritiesByUid(AntRestUtils.getToken2(request), uid);
    }

    @PostMapping("/modifyUserGroupRef")
    @Operation( summary = "用户权限分组修改")
    public BaseResponse modifyUserGroupRef(ServerHttpRequest request,
                                           @RequestBody AddUserGroupRequest addUserGroupRequest
    ) {
        SysUser user = userService.getCurrentUser(request);
        IotAssert.isNotNull(user, "无法获取用户信息，请确认是否登陆");

        addUserGroupRequest.setOpId(user.getId());
        addUserGroupRequest.setOpName(user.getName());

        return authCenterFeignClient.modifyUserGroupRef(AntRestUtils.getToken2(request), addUserGroupRequest);
    }

    @GetMapping("/findUserByPhone")
    @Operation( summary = "使用手机号码精确查找商户下的用户")
    public ObjectResponse<UserPropVO> findUserByPhone(@RequestParam("phone") String phone,
                                               ServerHttpRequest request) {
        log.info("查找用户: {}", phone);
        IotAssert.isNotBlank(phone, "手机号不能为空");

        String token = userService.getToken(request);
        IotAssert.isNotBlank(token, "token不能为空，请确认。");

        ObjectResponse<TCommercialUser> commUserRes = authCenterFeignClient.getCommercialsUserByToken(token);
        FeignResponseValidate.check(commUserRes);

        Long topCommId = AntRestUtils.getTopCommId(request);
        IotAssert.isNotNull(topCommId, "无法获取顶级商户Id，请重试。");

        return new ObjectResponse<>(userService.findByPhone(phone, topCommId));
    }

    @GetMapping("/findUserByEmail")
    @Operation( summary = "使用邮箱精确查找商户下的用户")
    public ObjectResponse<UserPropVO> findUserByEmail(@RequestParam("email") String email,
        ServerHttpRequest request) {
        log.info("查找用户: {}", email);
        IotAssert.isNotBlank(email, "手机号不能为空");

        String token = userService.getToken(request);
        IotAssert.isNotBlank(token, "token不能为空，请确认。");

        ObjectResponse<TCommercialUser> commUserRes = authCenterFeignClient.getCommercialsUserByToken(token);
        FeignResponseValidate.check(commUserRes);

        Long topCommId = AntRestUtils.getTopCommId(request);
        IotAssert.isNotNull(topCommId, "无法获取顶级商户Id，请重试。");

        return new ObjectResponse<>(userService.findByEmail(email, topCommId));
    }

    @PostMapping("/createUser")
    @Operation( summary = "新增普通用户，并为此用户新增商户关联关系")
    public BaseResponse createUser(@RequestBody AddUserRequest addUserRequest,
                            ServerHttpRequest request) {
        log.info("新增普通用户: {}", JsonUtils.toJsonString(addUserRequest));

        userService.addUser(addUserRequest, request);
        customerSysLogService.createUserLog(addUserRequest.getMobile(), request);
        return BaseResponse.success();
    }

    @PostMapping("/addUserRef")
    @Operation( summary = "新增用户商户关联关系")
    public BaseResponse addUserRef(@RequestBody UserCommRef userCommRef,
                            ServerHttpRequest request) {
        log.info("新增用户商户关联关系: {}", JsonUtils.toJsonString(userCommRef));

        return userService.addUserRef(userCommRef, request);
    }

    @PostMapping("/createUserByEmail")
    @Operation( summary = "根据邮箱新增普通用户，并为此用户新增商户关联关系")
    public ObjectResponse<String> createUserByEmail(@RequestBody AddUserRequest params,
        ServerHttpRequest request) {
        log.info("根据邮箱新增普通用户: {}", JsonUtils.toJsonString(params));
        String ret = userService.addUserByEmail(params, request);
        customerSysLogService.createUserLog(params.getEmail(), request);
        return RestUtils.buildObjectResponse(ret);
    }


}
