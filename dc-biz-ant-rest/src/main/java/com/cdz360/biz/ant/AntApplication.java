package com.cdz360.biz.ant;

import com.netflix.discovery.EurekaClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import reactivefeign.spring.config.EnableReactiveFeignClients;

import jakarta.annotation.PreDestroy;
import reactor.core.publisher.Hooks;

/**
 * <AUTHOR>
 */
@EnableDiscoveryClient
@SpringBootApplication
@EnableReactiveFeignClients(basePackages = {
        "com.cdz360.biz.utils.feign",
        "com.cdz360.biz.ant.feign.reactor"
})
@EnableFeignClients(basePackages = {
        "com.cdz360.biz.ant.feign",
        "com.chargerlinkcar.core.client",
        "com.chargerlinkcar.framework.common.feign",
        "com.cdz360.biz.utils.feign.iot"
})
@Slf4j
@ComponentScan(basePackages = {"com.cdz360.biz",
        "com.chargerlinkcar.core",
        "com.chargerlinkcar.framework.common",
        "com.cdz360.charger",
        "com.cdz360.data"})
@EnableScheduling
public class AntApplication {

    @Autowired
    private EurekaClient discoveryClient;

    /**
     * 主函数
     */
    public static void main(String[] args) {
//        SpringApplication.run(AntApplication.class, args);
        log.info("starting dc-biz-ant!!!");
        Hooks.enableAutomaticContextPropagation();  // 输出调用链traceId
        new SpringApplicationBuilder(AntApplication.class).web(WebApplicationType.REACTIVE).run(args);
    }


    @PreDestroy
    public void destroy() {
        log.info("going to shutdown");

        //DiscoveryManager.getInstance().shutdownComponent();
        discoveryClient.shutdown();
        log.info("eureka de-registered.....");
    }
}
