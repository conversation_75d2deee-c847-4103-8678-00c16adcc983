package com.cdz360.biz.ant.rest.oa;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.feign.reactor.OaFeignClient;
import com.cdz360.biz.ant.service.oa.OaProcessService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.ant.utils.RedisUtil;
import com.cdz360.biz.oa.dto.ProcessInstanceExDto;
import com.cdz360.biz.oa.param.FastSearchParam;
import com.cdz360.biz.oa.param.OaStartProcessParam;
import com.cdz360.biz.oa.vo.FormModelVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/oa/process-instance")
public class OaProcInstanceRest {

    @Autowired
    private OaFeignClient oaFeignClient;

    @Autowired
    private OaProcessService oaProcessService;

    @Autowired
    private RedisUtil redisUtil;


    @ApiOperation("获取启动表单提交信息")
    @GetMapping(value = "/startFormInfo")
    public Mono<ObjectResponse<FormModelVo>> procInstStartFormInfo(
        @ApiParam("流程实例ID") @RequestParam String processInstanceId) {
        log.info("获取启动表单提交信息: processInstanceId = {}", processInstanceId);
        return this.oaFeignClient.procInstStartFormInfo(processInstanceId);
    }

    @ApiOperation("启动OA流程")
    @PostMapping("/start")
    public Mono<ObjectResponse<String>> oaStartProcess(
        ServerHttpRequest request, @RequestBody OaStartProcessParam param) {
        log.info("启动OA流程: {}", LoggerHelper2.formatEnterLog(request));
        param.setTopCommId(Objects.requireNonNull(AntRestUtils.getTopCommId(request)).toString())
            .setCommIdChain(AntRestUtils.getCommIdChain(request))
            .setOpId(AntRestUtils.getSysUid(request).toString())
            .setOpName(AntRestUtils.getSysUserName(request))
            .setCommId(AntRestUtils.getCommId(request))
            .setSysUserPhone(AntRestUtils.getSysUserPhone(request));
        log.info("param = {}", JsonUtils.toJsonString(param));
        return oaProcessService.oaStartProcess(param);
    }

    @ApiOperation("启动OA流程驳回后重新提交")
    @PostMapping("/resubmit")
    public Mono<ObjectResponse<String>> oaProcessResubmit(
        ServerHttpRequest request, @RequestBody OaStartProcessParam param) {
        log.info("启动OA流程驳回后重新提交: {}",
            LoggerHelper2.formatEnterLog(request));
        param.setTopCommId(Objects.requireNonNull(AntRestUtils.getTopCommId(request)).toString())
            .setCommIdChain(AntRestUtils.getCommIdChain(request))
            .setOpId(AntRestUtils.getSysUid(request).toString())
            .setOpName(AntRestUtils.getSysUserName(request))
            .setCommId(AntRestUtils.getCommId(request))
            .setSysUserPhone(AntRestUtils.getSysUserPhone(request));
        log.info("param = {}", JsonUtils.toJsonString(param));
        return oaProcessService.oaProcessResubmit(param);
    }


    @ApiOperation("获取相关审批流列表")
    @PostMapping(value = "/fastSearch")
    public Mono<ListResponse<ProcessInstanceExDto>> fastSearch(
        @RequestBody FastSearchParam param) {
        log.info("获取相关审批流: param = {}", param);
        return this.oaFeignClient.fastSearch(param);
    }
}
