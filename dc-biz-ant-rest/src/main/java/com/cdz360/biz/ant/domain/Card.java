package com.cdz360.biz.ant.domain;
//
//import lombok.Data;
//
//import java.io.Serializable;
//import java.util.Date;
//
///**
// * <AUTHOR>
// */
//@Data
//public class Card implements Serializable {
//    /**
//     * 自动增长列,卡片ID
//     */
//    private Long id;
//
//    /**
//     * 卡片号码
//     */
//    private String cardNo;
//
//    /**
//     * 卡片类型0在线卡1离线卡
//     */
//    private Integer cardType;
//
//    /**
//     * 卡片芯片号
//     */
//    private String cardChipNo;
//
//    /**
//     * 卡片渠道-发卡方(1:商户自添加 2:平台制卡 3:虚拟卡生成）
//     */
//    private String cardChannel;
//
//    /**
//     * 卡片面值（分）
//     */
//    private Long cardDenomination;
//
//    /**
//     * CardStatus
//     * 卡状态（10000未激活，10001已激活，10002卡锁定(已挂失)，10005已失效(黑名单)，10006已过期，20000离线卡已删除，20001离线卡正常）
//     */
//    private String cardStatus;
//    /**
//     * 卡片激活码/紧急充电卡密码
//     */
//    private String cardActivationCode;
//
//    /**
//     * 卡片激活日期
//     */
//    private Date cardActivationDate;
//
//    /**
//     * 创建时间
//     */
//    private Date cardCreateDate;
//
//    /**
//     * 修改时间
//     */
//    private Date cardUpdateDate;
//
//    /**
//     * 商户Id
//     */
//    private Long commId;
//
//    /**
//     * 手机号
//     */
//    private String mobile;
//
//    /**
//     * 用户id
//     */
//    private Long userId;
//
//    /**
//     * 状态（1活跃，0休眠）(废弃)
//     */
//    private Integer status;
//
//    /**
//     * 卡类别（0虚拟卡，1实体卡）(废弃)
//     */
//    private Integer type;
//
//    /**
//     * 0:其他,1:套餐卡, 2:月卡,3:离线卡;4-鉴权卡
//     */
//    private Integer isPackage;
//    /**
//     * 紧急充电卡集团客户id
//     */
//    private Long merchantId;
//
//    /**
//     * 有效标志1有效0无效
//     */
//    private String yxBz;
//
//    /**
//     * 鉴权卡所属站点（多个站点用‘，’分割）
//     */
//    private String stations;
//
//    /**
//     * 备注
//     */
//    private String remark;
//
//    /**
//     * 卡名称
//     */
//    private String cardName;
//
//    /**
//     * 卡读写密码
//     */
//    private String cardKey;
//
//    /**
//     * t_card
//     */
//    private static final long serialVersionUID = 1L;
//
//}