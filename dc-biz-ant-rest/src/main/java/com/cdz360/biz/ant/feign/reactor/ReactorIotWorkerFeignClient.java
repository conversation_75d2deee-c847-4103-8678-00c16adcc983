package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.domain.bundle.EvseBundleDto;
import com.cdz360.biz.ant.domain.bundle.UpgradeParam;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

/**
 * ReactorIotWorkerFeignClient
 *
 * @since 10/15/2021 1:48 PM
 * <AUTHOR>
 */
@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_IOT_WORKER,
        fallbackFactory = ReactorIotWorkerFeignHystrix.class)
public interface ReactorIotWorkerFeignClient {

    @GetMapping(value = "/mgc/findMgcGwnoByKeyword")
    Mono<ObjectResponse<String>> findMgcGwnoByKeyword(@RequestParam(value = "keyword") String keyword);

    @Operation(summary = "微网控制器升级")
    @PostMapping(value = "/mgc/upgrade")
    Mono<BaseResponse> mgcUpgrade(@RequestBody UpgradeParam param);
}