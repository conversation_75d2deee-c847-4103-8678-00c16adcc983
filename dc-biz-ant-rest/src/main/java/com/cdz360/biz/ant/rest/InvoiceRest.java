package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServerException;
import com.cdz360.base.model.base.exception.DcTokenException;
import com.cdz360.base.model.base.type.UserType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.aop.CheckToken;
import com.cdz360.biz.ant.domain.request.InvoicedModelSearchParam;
import com.cdz360.biz.ant.domain.vo.DescVo;
import com.cdz360.biz.ant.domain.vo.IdListVo;
import com.cdz360.biz.ant.domain.vo.InvoicedChargerOrderVo;
import com.cdz360.biz.ant.domain.vo.InvoicedConfigMegVo;
import com.cdz360.biz.ant.domain.vo.InvoicedRecordChargerOrderSaveVo;
import com.cdz360.biz.ant.domain.vo.InvoicedRecordUpdateStatusVo;
import com.cdz360.biz.ant.domain.vo.InvoicedSiteValidYNVo;
import com.cdz360.biz.ant.domain.vo.InvoicedUserAutoAmountVo;
import com.cdz360.biz.ant.domain.vo.InvoicedUserVo;
import com.cdz360.biz.ant.domain.vo.SiteSimpleInfoVo;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.ant.service.InvoiceService;
import com.cdz360.biz.ant.service.site.SiteService;
import com.cdz360.biz.ant.service.sysLog.CommercialSysLogService;
import com.cdz360.biz.ant.service.sysLog.CorpSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.common.constant.DcBizConstants;
import com.cdz360.biz.model.cus.settlement.param.ListSettlementParam;
import com.cdz360.biz.model.cus.settlement.vo.SettlementVo;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.trading.invoice.dto.CorpInvoiceRecordDto;
import com.cdz360.biz.model.trading.invoice.param.ListCorpInvoiceRecordParam;
import com.cdz360.biz.model.trading.invoice.po.CorpInvoiceRecordPo;
import com.cdz360.biz.model.trading.order.dto.PayBillInvoiceBi;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.param.ListPayBillParam;
import com.cdz360.biz.model.trading.order.vo.CorpInvoiceOrderVo;
import com.cdz360.biz.model.trading.peek.invoice.dto.PeekInvoiceDto;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.chargerlinkcar.framework.common.domain.CommercialSample;
import com.chargerlinkcar.framework.common.domain.invoice.InvoicedRecordVo;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoicedModelDTO;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoicedTemplateSalDTO;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordAuditParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordManualParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordUpdateParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.ListInvoicedRecordParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.ListInvoicedTempSalParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.ListSiteTempSalParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceInfoVo;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceRecordDetail;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedModelVo;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedTemplateSalDetailVo;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedTemplateSalVo;
import com.chargerlinkcar.framework.common.domain.peek.invoice.param.PeekInvoiceParam;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderSite;
import com.chargerlinkcar.framework.common.domain.vo.OrderBiVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderTimeShareBiVo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.UnsupportedEncodingException;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "发票相关接口", description = "发票相关接口")
public class InvoiceRest extends BaseController {

    @Autowired
    private InvoiceService invoiceService;
    @Autowired
    private InvoiceProcesser invoiceProcesser;
    @Autowired
    private CorpSysLogService corpSysLogService;
    @Autowired
    private CommercialSysLogService commSysLogService;

    @Autowired
    private SiteService iSiteService;

    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;

    @Operation(summary = "企业开票涉及的订单导出")
    @PostMapping(value = "/api/invoice/exportCorpInvoiceOrder")
    public Mono<ObjectResponse<ExcelPosition>> exportCorpInvoiceOrder(
        ServerHttpRequest request,
        @Parameter(name = "申请单号", required = true) @RequestParam("applyNo") String applyNo) {
        log.info("企业开票涉及的订单导出: {}, applyNo = {}",
            LoggerHelper2.formatEnterLog(request, false), applyNo);

        Long sysUid = AntRestUtils.getSysUid(request);
        ObjectNode reqParam = new ObjectMapper().createObjectNode();
        reqParam.put("applyNo", applyNo);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("企业开票订单列表")
            .setFunctionMap(DownloadFunctionType.CORP_INVOICE_ORDER)
            .setReqParam(reqParam.toString());
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return invoiceService.exportCorpInvoiceOrder(applyNo);
    }

    @Operation(summary = "企业客户开票记录提交到审核(非财务)")
    @PostMapping(value = "/api/invoice/corpInvoiceRecordSubmit2Audit")
    public ObjectResponse<Integer> corpInvoiceRecordSubmit2Audit(
        ServerHttpRequest request, @RequestBody CorpInvoiceRecordDto dto) {
        ObjectResponse<Integer> response = invoiceProcesser.corpInvoiceRecordSubmit2Audit(request,
            dto);
        corpSysLogService.corpInvoiceRecordSubmit2AuditLog(dto.getApplyNo(), request);
        return response;
    }

    @Operation(summary = "企业手动开票")
    @PostMapping(value = "/api/invoice/corpInvoiceRecordManual")
    public Mono<ObjectResponse<Boolean>> corpInvoiceRecordManual(
        ServerHttpRequest request, @RequestBody CorpInvoiceRecordManualParam param) {
        log.info("企业手动开票: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        var res = invoiceService.corpInvoiceRecordManual(param);
        corpSysLogService.corpInvoiceRecordAuditLog(param.getApplyNo(), request);
        return res;
    }

    @Operation(summary = "企业开票审核(财务)")
    @PostMapping(value = "/api/invoice/corpInvoiceRecordAudit")
    public ObjectResponse<Integer> corpInvoiceRecordAudit(
        ServerHttpRequest request, @RequestBody CorpInvoiceRecordAuditParam param) {
        log.info("企业开票审核: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        // 获取登录用户相关信息
        CommercialSample comm = this.getCommercialSample2(request);
        if (ObjectUtils.isEmpty(comm)) {
            throw new DcArgumentException("当前操作用户信息不存在，请重新登录");
        }
        Long sysUserCommId = comm.getComId();
        if (sysUserCommId == null || sysUserCommId <= 0) {
            throw new DcArgumentException("当前操作用户商户信息不明确");
        }

        Long opUid = comm.getId();
        if (opUid == null || opUid <= 0) {
            throw new DcArgumentException("当前操作用户Id不存在");
        }

        // 操作人信息
        param.setOpId(opUid);
        param.setOpName(comm.getUsername());
        param.setOpType(UserType.SYS_USER);

        var res = invoiceService.corpInvoiceRecordAudit(param);
        corpSysLogService.corpInvoiceRecordAuditLog(param.getApplyNo(), request);
        return res;
    }

    @Operation(summary = "企业开票记录详情")
    @GetMapping(value = "/api/invoice/corpInvoiceRecordDetail")
    public ObjectResponse<CorpInvoiceRecordDetail> corpInvoiceRecordDetail(
        ServerHttpRequest request, @RequestParam(value = "applyNo") String applyNo) {
        log.info("企业开票记录详情: {}", LoggerHelper2.formatEnterLog(request));
        return invoiceService.corpInvoiceRecordDetail(applyNo);
    }

    @Operation(summary = "获取企业开票列表")
    @PostMapping(value = "/api/invoice/findCorpInvoiceRecordList")
    public ListResponse<CorpInvoiceRecordDto> findCorpInvoiceRecordList(
        ServerHttpRequest request, @RequestBody ListCorpInvoiceRecordParam param) {
        log.debug("获取企业开票列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        String commIdChain = AntRestUtils.getCommIdChain(request);
        if (StringUtils.isBlank(commIdChain)) {
            throw new DcArgumentException("用户登录已失效，请重新登录");
        }

        param.setCommIdChain(commIdChain);
        return invoiceService.findCorpInvoiceRecordList(param);
    }

    @Operation(summary = "导出企业开票记录列表")
    @PostMapping(value = "/api/invoice/exportCorpInvoiceRecord")
    public Mono<ObjectResponse<ExcelPosition>> exportCorpInvoiceRecord(
        ServerHttpRequest request, @RequestBody ListCorpInvoiceRecordParam param) {
        log.info("导出企业开票记录列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));

        String commIdChain = AntRestUtils.getCommIdChain(request);
        if (StringUtils.isBlank(commIdChain)) {
            throw new DcArgumentException("用户登录已失效，请重新登录");
        }

        param.setCommIdChain(commIdChain); // Long.valueOf(commId));

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("企业开票申请列表")
            .setFunctionMap(DownloadFunctionType.CORP_INVOICED_RECORD)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return invoiceService.exportCorpInvoiceRecord(param);
    }

    @Operation(summary = "导出企业开票详情")
    @GetMapping(value = "/api/invoice/exportCorpInvoiceDetail")
    public Mono<ObjectResponse<ExcelPosition>> exportYwOrderPdf(
        ServerHttpRequest request,
        @ApiParam(value = "下载导出文件名") @RequestParam(value = "exFileName", required = false) String exFileName,
        @Parameter(name = "申请单号", required = true) @RequestParam("applyNo") String applyNo) {
        log.info("导出企业开票详情: applyNo = {}", applyNo);

        if (StringUtils.isBlank(applyNo)) {
            throw new DcArgumentException("申请单号无效");
        }

        Long sysUid = AntRestUtils.getSysUid(request);
        ObjectNode reqParam = new ObjectMapper().createObjectNode();
        reqParam.put("applyNo", applyNo);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.PDF)
            .setUid(sysUid)
            .setExFileName(StringUtils.isNotBlank(exFileName) ? exFileName : "企业开票详情")
            .setFunctionMap(DownloadFunctionType.CORP_INVOICE_DETAIL)
            .setReqParam(reqParam.toString());
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
    }

    @Operation(summary = "获取企业开票列表[审核页面]",
        description = "登录用户只能查看自己所属商户与开票主体所属商户一致的审核记录")
    @PostMapping(value = "/api/invoice/getCorpInvoiceRecordList")
    public ListResponse<CorpInvoiceRecordDto> getCorpInvoiceRecordList(
        ServerHttpRequest request, @RequestBody ListCorpInvoiceRecordParam param) {
        log.debug("获取企业开票列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

//        Long commId = AntRestUtils.getCommId(request);
//        if (null == commId) {
//            throw new DcArgumentException("用户登录已失效，请重新登录");
//        }
//        param.setCommId(commId);
        String commIdChain = AntRestUtils.getCommIdChain(request);
        if (null == commIdChain) {
            throw new DcArgumentException("用户登录已失效，请重新登录");
        }
        param.setCommIdChain(commIdChain);
        return invoiceService.findCorpInvoiceRecordList(param);
    }

    @Operation(summary = "企业客户开票追加订单(充值/充电/账单)")
    @PostMapping(value = "/api/invoice/corpInvoiceAppendOrder")
    public ObjectResponse<CorpInvoiceRecordDetail> corpInvoiceAppendOrder(
        ServerHttpRequest request, @RequestBody CorpInvoiceRecordUpdateParam param) {
        ObjectResponse<CorpInvoiceRecordDetail> res = invoiceProcesser.corpInvoiceAppendOrderProcess(
            request, param);
        if (param.getApplyNo() == null) {
            corpSysLogService.corpInvoiceAddLog(res.getData().getApplyNo(), request);
        } else {
            corpSysLogService.corpInvoiceEditLog(param.getApplyNo(), request);
        }
        return res;
    }

    @Operation(summary = "预计算，客户开票追加订单(充值/充电/账单)")
    @PostMapping(value = "/api/invoice/peekInvoice")
    public ObjectResponse<PeekInvoiceDto> peekInvoice(
        ServerHttpRequest request, @RequestBody PeekInvoiceParam param) {
        return invoiceProcesser.peekInvoice(request, param);
    }

    @Operation(summary = "企业客户开票移除订单(充值/充电/账单)")
    @PostMapping(value = "/api/invoice/corpInvoiceRemoveOrder")
    public ObjectResponse<CorpInvoiceRecordDetail> corpInvoiceRemoveOrder(
        ServerHttpRequest request, @RequestBody CorpInvoiceRecordUpdateParam param) {
        ObjectResponse<CorpInvoiceRecordDetail> res = invoiceProcesser.corpInvoiceRemoveOrderProcess(
            request, param);
        corpSysLogService.corpInvoiceRemoveOrderLog(param.getApplyNo(), request);
        return res;
    }

    @Operation(summary = "获取企业开票配置信息")
    @GetMapping(value = "/api/invoice/getCorpInvoiceInfo")
    public ObjectResponse<CorpInvoiceInfoVo> getCorpInvoiceInfo(
        ServerHttpRequest request, @RequestParam(value = "corpId") Long corpId) {
        log.info("获取企业开票配置信息: {}", LoggerHelper2.formatEnterLog(request));
        return RestUtils.buildObjectResponse(invoiceService.getCorpInvoiceInfo(corpId, null));
    }

    @Operation(summary = "更新企业客户的开票设置")
    @PostMapping(value = "/api/invoice/saveCorpInvoiceInfo")
    public ObjectResponse<Long> saveCorpInvoiceInfo(
        ServerHttpRequest request, @RequestBody CorpInvoiceInfoVo vo) {
        log.info("更新企业客户的开票设置: {}, dto = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(vo));
        Long res = invoiceService.saveCorpInvoiceInfo(vo);
        corpSysLogService.updateBlocUserLog(vo.getCorpName(), request);
        return RestUtils.buildObjectResponse(res);
    }

    @Operation(summary = "更新企业客户的开票抬头")
    @PostMapping(value = "/api/invoice/saveCorpInvoiceModelInfo")
    public ObjectResponse<Long> saveCorpInvoiceModelInfo(
        ServerHttpRequest request, @RequestBody InvoicedModelDTO modelDTO) {
        log.info("更新企业客户的开票抬头: {}, dto = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(modelDTO));
        Long res = invoiceService.saveCorpInvoiceModelInfo(modelDTO);
//        corpSysLogService.updateBlocUserLog(vo.getCorpName(), request);
        return RestUtils.buildObjectResponse(res);
    }

    @Operation(summary = "更新个人、会员客户的开票抬头")
    @PostMapping(value = "/api/invoice/saveUserInvoiceModelInfo")
    public ObjectResponse<Long> saveUserInvoiceModelInfo(
        ServerHttpRequest request, @RequestBody InvoicedModelDTO modelDTO) {
        log.info("更新个人、会员客户的开票抬头: {}, dto = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(modelDTO));
        Long res = invoiceService.saveUserInvoiceModelInfo(modelDTO);
//        corpSysLogService.updateBlocUserLog(vo.getCorpName(), request);
        return RestUtils.buildObjectResponse(res);
    }

    @Operation(summary = "获取当前登录用户可见开票主体列表")
    @PostMapping(value = "/api/invoice/getInvoicedTempSalList")
    public ListResponse<InvoicedTemplateSalVo> getInvoicedTempSalList(
        ServerHttpRequest request, @RequestBody ListInvoicedTempSalParam param) {
        String commIdChain = AntRestUtils.getCommIdChain(request);
        log.info("获取当前登录用户可见开票主体列表: commIdChain = {}", commIdChain);
        if (StringUtils.isBlank(commIdChain)) {
            throw new DcArgumentException("用户登录信息失效，请重新登录");
        }

        // 临时调整
        param.setCommIdChain(commIdChain);
        param.setTopCommId(AntRestUtils.getTopCommId(request));
        return invoiceService.getInvoicedTempSalList(param);
    }

    @Operation(summary = "获取指定商户可见的开票主体列表")
    @GetMapping(value = "/api/invoice/getInvoicedTempSalListByCommId")
    public ListResponse<InvoicedTemplateSalVo> getInvoicedTempSalListByCommId(
        ServerHttpRequest request, @RequestParam String commId) {
        log.info("获取指定商户可见的开票主体列表: commId = {}", commId);
        return invoiceService.getInvoicedTempSalListByCommId(commId);
    }

    @Operation(summary = "企业开票记录删除")
    @GetMapping(value = "/api/invoice/deleteCorpInvoiceRecordByApplyNo")
    public ObjectResponse<Integer> deleteCorpInvoiceRecordByApplyNo(
        ServerHttpRequest request,
        @Parameter(name = "申请单号") @RequestParam(value = "applyNo") String applyNo) {
        log.info("企业开票记录删除: {}", LoggerHelper2.formatEnterLog(request));
        var res = invoiceService.deleteCorpInvoiceRecordByApplyNo(applyNo);
        corpSysLogService.deleteCorpInvoiceRecordByApplyNoLog(applyNo, request);
        return res;
    }

    @Operation(summary = "企业开票记录修改回款状态")
    @PostMapping(value = "/api/invoice/updateCorpInvoiceRecordReturnFlag")
    public ObjectResponse<Integer> updateCorpInvoiceRecordReturnFlag(
        ServerHttpRequest request, @RequestBody CorpInvoiceRecordPo po) {
        log.info("企业开票记录修改回款状态: {} po = {}", LoggerHelper2.formatEnterLog(request),
            JsonUtils.toJsonString(po));
        // 获取登录用户相关信息
        CommercialSample comm = this.getCommercialSample2(request);
        if (ObjectUtils.isEmpty(comm)) {
            throw new DcArgumentException("当前操作用户信息不存在，请重新登录");
        }
        Long sysUserCommId = comm.getComId();
        if (sysUserCommId == null || sysUserCommId <= 0) {
            throw new DcArgumentException("当前操作用户商户信息不明确");
        }

        Long opUid = comm.getId();
        if (opUid == null || opUid <= 0) {
            throw new DcArgumentException("当前操作用户Id不存在");
        }

        // 操作人信息
        po.setUpdateOpId(opUid);
        po.setUpdateOpName(comm.getUsername());
        po.setUpdateOpType(UserType.SYS_USER);
        var res = invoiceService.updateCorpInvoiceRecordReturnFlag(po);
        corpSysLogService.updateCorpInvoiceRecordReturnFlagLog(po, request);
        return res;
    }

    @Operation(summary = "获取企业客户开票充电订单列表")
    @PostMapping(value = "/api/invoice/getChargerOrderList")
    public ListResponse<CorpInvoiceOrderVo> getChargerOrderList(
        ServerHttpRequest request, @RequestBody ListChargeOrderParam param) {
        log.info("获取充电订单列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        String commIdChain = AntRestUtils.getCommIdChain(request);
        if (StringUtils.isBlank(commIdChain)) {
            throw new DcArgumentException("用户登录信息失效，请重新登录");
        }

        if (StringUtils.isBlank(param.getApplyNo())) {
            param.setCommIdChain(commIdChain);
        }

        if (NumberUtils.equals(AntRestUtils.getCommId(request), DcBizConstants.superTopCommId)) {
            param.setTopCommId(-1L); // 集团商户后门
        }
        return invoiceService.getChargerOrderList(param);
    }

    @Operation(summary = "获取已申请企业开票的充电订单列表")
    @PostMapping(value = "/api/invoice/includeChargerOrderList")
    public Mono<ListResponse<CorpInvoiceOrderVo>> includeChargerOrderList(
        ServerHttpRequest request, @RequestBody ListChargeOrderParam param) {
        log.info("获取已申请企业开票的充电订单列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        String commIdChain = AntRestUtils.getCommIdChain(request);
        if (StringUtils.isBlank(commIdChain)) {
            throw new DcArgumentException("用户登录信息失效，请重新登录");
        }

        if (StringUtils.isBlank(param.getApplyNo())) {
            throw new DcArgumentException("请指定申请编号");
        }

        return invoiceService.includeChargerOrderList(param);
    }

    @Operation(summary = "获取企业充电订单汇总")
    @PostMapping(value = "/api/invoice/chargeOrderBiForCorp")
    public ObjectResponse<OrderBiVo> chargeOrderBiForCorp(
        ServerHttpRequest request, @RequestBody ListChargeOrderParam param) {
        log.info("获取企业充电订单汇总: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        String commIdChain = AntRestUtils.getCommIdChain(request);
        if (StringUtils.isBlank(commIdChain)) {
            throw new DcArgumentException("用户登录信息失效，请重新登录");
        }

        if (StringUtils.isBlank(param.getApplyNo())) {
            param.setCommIdChain(commIdChain);
        }

        if (NumberUtils.equals(AntRestUtils.getCommId(request), DcBizConstants.superTopCommId)) {
            param.setTopCommId(-1L); // 集团商户后门
        }
        return invoiceService.chargeOrderBiForCorp(param);
    }

    @Operation(summary = "获取企业开票充电订单汇总")
    @PostMapping(value = "/api/invoice/includeChargerOrderBi")
    public Mono<ObjectResponse<OrderBiVo>> includeChargerOrderBi(
        ServerHttpRequest request, @RequestBody ListChargeOrderParam param) {
        log.info("获取企业开票充电订单汇总: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        String commIdChain = AntRestUtils.getCommIdChain(request);
        if (StringUtils.isBlank(commIdChain)) {
            throw new DcArgumentException("用户登录信息失效，请重新登录");
        }

        if (StringUtils.isBlank(param.getApplyNo())) {
            throw new DcArgumentException("请指定申请编号");
        }

        return invoiceService.includeChargerOrderBi(param);
    }

    @Operation(summary = "获取企业客户开票充电订单汇总，按照场站汇总")
    @PostMapping(value = "/api/invoice/chargerOrderGroupBySite")
    public ListResponse<ChargerOrderSite> chargerOrderGroupBySite(
        ServerHttpRequest request, @RequestBody ListChargeOrderParam param) {
        log.info("获取充电订单按照场站汇总: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        String commIdChain = AntRestUtils.getCommIdChain(request);
        if (StringUtils.isBlank(commIdChain)) {
            throw new DcArgumentException("用户登录信息失效，请重新登录");
        }

        if (StringUtils.isBlank(param.getApplyNo())) {
            param.setCommIdChain(commIdChain);
        }

        if (NumberUtils.equals(AntRestUtils.getCommId(request), DcBizConstants.superTopCommId)) {
            param.setTopCommId(-1L); // 集团商户后门
        }
        return invoiceService.chargerOrderGroupBySite(param);
    }

    @Operation(summary = "获取企业客户开票充电订单尖峰平谷金额汇总")
    @PostMapping(value = "/api/invoice/chargerOrderGroupByTimeShareFee")
    public ListResponse<OrderTimeShareBiVo> chargerOrderGroupByTimeShareFee(
        ServerHttpRequest request, @RequestBody ListChargeOrderParam param) {
        log.info("获取企业客户开票充电订单尖峰平谷金额汇总: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        String commIdChain = AntRestUtils.getCommIdChain(request);
        if (StringUtils.isBlank(commIdChain)) {
            throw new DcArgumentException("用户登录信息失效，请重新登录");
        }

        if (StringUtils.isBlank(param.getApplyNo())) {
            param.setCommIdChain(commIdChain);
        }
        return invoiceService.chargerOrderGroupByTimeShareFee(param);
    }

    @Operation(summary = "获取企业客户开票充值订单列表")
    @PostMapping(value = "/api/invoice/getPayBillOrderList")
    public ListResponse<PayBillInvoiceBi> getPayBillOrderList(
        ServerHttpRequest request, @RequestBody ListPayBillParam param) {
        log.info("获取充值订单列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        String commIdChain = AntRestUtils.getCommIdChain(request);
        if (StringUtils.isBlank(commIdChain)) {
            throw new DcArgumentException("用户登录信息失效，请重新登录");
        }

        if (StringUtils.isBlank(param.getApplyNo())) {
            param.setCommIdChain(commIdChain);
        }
        return invoiceService.getPayBillOrderList(param);
    }

    @Operation(summary = "获取客户充值记录汇总")
    @PostMapping(value = "/api/invoice/invoiceOrderBi")
    public ObjectResponse<OrderBiVo> invoiceOrderBi(
        ServerHttpRequest request, @RequestBody ListPayBillParam param) {
        log.info("获取客户充值记录汇总: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        String commIdChain = AntRestUtils.getCommIdChain(request);
        if (StringUtils.isBlank(commIdChain)) {
            throw new DcArgumentException("用户登录信息失效，请重新登录");
        }

        if (StringUtils.isBlank(param.getApplyNo())) {
            param.setCommIdChain(commIdChain);
        }
        return invoiceService.invoiceOrderBi(param);
    }

    @Operation(summary = "获取企业客户充值记录汇总")
    @PostMapping(value = "/api/invoice/invoiceOrderBiForCorp")
    public ObjectResponse<OrderBiVo> invoiceOrderBiForCorp(
        ServerHttpRequest request, @RequestBody ListPayBillParam param) {
        log.info("获取企业客户充值记录汇总: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        String commIdChain = AntRestUtils.getCommIdChain(request);
        if (StringUtils.isBlank(commIdChain)) {
            throw new DcArgumentException("用户登录信息失效，请重新登录");
        }

        if (StringUtils.isBlank(param.getApplyNo())) {
            param.setCommIdChain(commIdChain);
        }
        return invoiceService.invoiceOrderBiForCorp(param);
    }

    @Operation(summary = "获取企业客户开票账单订单列表")
    @PostMapping(value = "/api/invoice/getSettlementOrderList")
    public ListResponse<SettlementVo> getSettlementOrderList(
        ServerHttpRequest request, @RequestBody ListSettlementParam param) {
        log.info("获取账单订单列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        String commIdChain = AntRestUtils.getCommIdChain(request);
        if (StringUtils.isBlank(commIdChain)) {
            throw new DcArgumentException("用户登录信息失效，请重新登录");
        }

        if (StringUtils.isBlank(param.getApplyNo())) {
            param.setCommIdChain(commIdChain);
        }
        return invoiceService.getSettlementOrderList(param);
    }

    @Operation(summary = "账单汇总")
    @PostMapping(value = "/api/invoice/settlementBiForCorp")
    public ObjectResponse<OrderBiVo> settlementBiForCorp(
        ServerHttpRequest request, @RequestBody ListSettlementParam param) {
        log.info("获取账单订单汇总: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        String commIdChain = AntRestUtils.getCommIdChain(request);
        if (StringUtils.isBlank(commIdChain)) {
            throw new DcArgumentException("用户登录信息失效，请重新登录");
        }

        if (StringUtils.isBlank(param.getApplyNo())) {
            param.setCommIdChain(commIdChain);
        }
        return invoiceService.settlementBiForCorp(param);
    }

    @Operation(summary = "获取企业客户开票账单，按照场站汇总")
    @PostMapping(value = "/api/invoice/recordOrderGroupBySite")
    public ListResponse<ChargerOrderSite> recordOrderGroupBySite(
        ServerHttpRequest request, @RequestBody ListSettlementParam param) {
        log.info("获取账单订单列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        String commIdChain = AntRestUtils.getCommIdChain(request);
        if (StringUtils.isBlank(commIdChain)) {
            throw new DcArgumentException("用户登录信息失效，请重新登录");
        }

        if (StringUtils.isBlank(param.getApplyNo())) {
            throw new DcArgumentException("申请单号不能为空");
        }
        return invoiceService.recordOrderGroupBySite(param);
    }

    @Operation(summary = "获取企业客户开票账单 金额数据")
    @PostMapping(value = "/api/invoice/recordOrderGroupByTimeShareFee")
    public ListResponse<OrderTimeShareBiVo> recordOrderGroupByTimeShareFee(
        ServerHttpRequest request, @RequestBody ListSettlementParam param) {
        log.info("获取账单订单列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        String commIdChain = AntRestUtils.getCommIdChain(request);
        if (StringUtils.isBlank(commIdChain)) {
            throw new DcArgumentException("用户登录信息失效，请重新登录");
        }

        if (StringUtils.isBlank(param.getApplyNo())) {
            throw new DcArgumentException("申请单号不能为空");
        }
        return invoiceService.recordOrderGroupByTimeShareFee(param);
    }

    @Operation(summary = "通过主体ID获取商户开票主体信息")
    @GetMapping(value = "/api/invoice/getInvoicedTempSalById")
    public ObjectResponse<InvoicedTemplateSalVo> getInvoicedTempSalById(
        ServerHttpRequest request,
        @Parameter(name = "ID", required = true) @RequestParam(value = "id") Long id) {
        log.info("通过主体ID获取商户开票主体信息: {}", LoggerHelper2.formatEnterLog(request));
        return invoiceService.getInvoicedTempSalById(id);
    }

    @Operation(summary = "通过纳税人识别号获取商户开票主体信息")
    @GetMapping(value = "/api/invoice/getInvoicedTempSal")
    public ObjectResponse<InvoicedTemplateSalVo> getInvoicedTempSal(
        ServerHttpRequest request,
        @Parameter(name = "商户ID", required = true) @RequestParam(value = "commId") Long commId,
        @Parameter(name = "纳税人识别号", required = true) @RequestParam(value = "saleTin") String saleTin) {
        log.info("获取开票主体信息: {}", LoggerHelper2.formatEnterLog(request));
        return invoiceService.getInvoicedTempSal(commId, saleTin);
    }

//    @Deprecated(since = "20201215")
//    @Operation(summary = "通过商户Id获取商户开票主体信息")
//    @GetMapping(value = "/api/invoice/getInvoicedTempSalByCommId")
//    public ObjectResponse<InvoicedTemplateSalVo> getInvoicedTempSalByCommId(
//            ServerHttpRequest request, @RequestParam(value = "commId") Long commId) {
//        throw new DcServiceException("接口已废弃");
////        log.info("获取当前登录用户所属商户的开票主体: commId = {}", commId);
////        return invoiceService.getInvoicedTempSalByCommId(commId);
//    }

    @Operation(summary = "新增开票主体")
    @PostMapping(value = "/api/invoice/addInvoicedTempSal")
    public ObjectResponse<InvoicedTemplateSalDTO> addInvoicedTempSal(
        ServerHttpRequest request, @RequestBody InvoicedTemplateSalVo vo) {
        log.info("新增开票主体: {}", JsonUtils.toJsonString(vo));
        Long topCommId = AntRestUtils.getTopCommId(request);
        vo.setTopCommId(topCommId);
        var res = invoiceService.addInvoicedTempSal(vo);
        commSysLogService.addInvoicedTempSalLog(vo.getSaleName(), request);
        return res;
    }

    @Operation(summary = "更新开票主体信息")
    @PostMapping(value = "/api/invoice/updateInvoicedTempSal")
    public ObjectResponse<Integer> updateInvoicedTempSal(
        ServerHttpRequest request, @RequestBody InvoicedTemplateSalVo vo) {
        log.info("更新开票主体信息: {}", JsonUtils.toJsonString(vo));
        Long topCommId = AntRestUtils.getTopCommId(request);
        vo.setTopCommId(topCommId);
        var res = invoiceService.updateInvoicedTempSal(vo);
        commSysLogService.updateInvoicedTempSalLog(vo.getSaleName(), request);
        return RestUtils.buildObjectResponse(res);
    }

    @Operation(summary = "删除开票主体信息")
    @PostMapping(value = "/api/invoice/disableInvoicedTempSal")
    public ObjectResponse<InvoicedTemplateSalDTO> disableInvoicedTempSal(
        ServerHttpRequest request,
        @Parameter(name = "商户ID", required = true) @RequestParam(value = "commId") Long commId,
        @Parameter(name = "纳税人识别号", required = true) @RequestParam(value = "saleTin") String saleTin) {
        log.info("删除开票主体信息: {}", LoggerHelper2.formatEnterLog(request));
        var res = invoiceService.disableInvoicedTempSal(commId, saleTin);
        commSysLogService.disableInvoicedTempSal(res.getSaleName(), request);
        return RestUtils.buildObjectResponse(res);
    }

//    @Deprecated(since = "20200728")
//    @Operation(summary = "调整商户开票模板信息")
//    @PostMapping(value = "/api/invoiced-template-sal/saveInvoicedTemplateSal")
//    public ObjectResponse<String> saveInvoicedTemplateSal(
//            ServerHttpRequest request,
//            @RequestBody InvoicedTemplateSalVo invoicedTemplateSalVo) {
//        log.info("调整商户开票模板信息: {}", JsonUtils.toJsonString(invoicedTemplateSalVo));
//        throw new DcArgumentException("已经废弃");
//    }

    @Operation(summary = "获取场站列表(是否可开票控制)")
    @PostMapping(value = "/api/invoiced-sites")
    public ListResponse<SiteSimpleInfoVo> getAllInvoicedRecords(
        ServerWebExchange exh,
        @RequestBody ListSiteTempSalParam param,
//        @RequestParam(value = "status", required = false) Integer status,//TODO 建议场站状态入参修改成数组
//        @RequestParam(value = "invoicedValid", required = false) Boolean invoicedValid,
//        @RequestParam(value = "platformInvoicedValid", required = false) Boolean platformInvoicedValid,
//        @RequestParam(value = "keyword", required = false) String keyword
        ServerHttpRequest request) throws UnsupportedEncodingException {
        log.info("获取场站列表: {}", LoggerHelper2.formatEnterLog(request));

        OldPageParam page = getPage2(request, exh, true);

        return invoiceService.getAllInvoicedRecords(param, AntRestUtils.getTopCommId(request),
            super.getCommIdChain2(request), AntRestUtils.getSysUserGids(request),
            page.getPageNum(), page.getPageSize());
    }

    /**
     * 设置场站可开票标记(多个）
     *
     * @param vo
     * @return
     */
    @Deprecated(since = "2023-06-26")
    @Operation(summary = "调整场站的可开票状态")
    @PostMapping(value = "/api/invoiced-sites/invoicedVaild")
    public ObjectResponse saveInvoicedValid(
        ServerHttpRequest request,
        @RequestBody InvoicedSiteValidYNVo vo) {
        log.info("调整场站的可开票状态: {}, vo = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(vo));
        if (vo.getIds() == null) {
            throw new DcArgumentException("参数错误");
        }
        if (vo.getInvoicedValid() == null) {
            throw new DcArgumentException("参数错误");
        }
        IotAssert.isTrue(com.cdz360.base.utils.CollectionUtils.isNotEmpty(vo.getSiteNameList()),
            "参数错误");

//        ObjectResponse<DzCommonVo> res = invoiceService.saveInvoicedValid(vo);
//        log.info("设置场站可开票标记(多个)的返回值----------------------{}",
//            JsonUtils.toJsonString(res));
//        if (res != null && res.getStatus() == ResultConstant.RES_SUCCESS_CODE) {
//            commSysLogService.invoicedVaildLog(vo.getSiteNameList(), request);
//            return new ObjectResponse<>("修改成功");
//        }
//        throw new DcServerException("操作失败");
        throw new DcServerException("接口已废弃");
    }

    /**
     * 根据商户id获取可开票商品说明
     *
     * @return
     */
    @RequestMapping(value = "/api/invoiced-temp-sal-dtl/getInvoicedProductDesc", method = RequestMethod.GET)
    public ObjectResponse<DescVo> getInvoicedProductDesc(ServerHttpRequest request) {
        Long commId = AntRestUtils.getCommId(request);
        ObjectResponse<DescVo> res = invoiceService.getInvoicedProductDesc(commId);
        return res;
    }

//    /**
//     * 获取开票商品行模板by code
//     *
//     * @param code
//     * @return
//     */
//    @RequestMapping(value = "/api/invoiced-temp-sal-dtl/byCode", method = RequestMethod.GET)
//    public ObjectResponse<InvoicedTemplateSalDetailVo> getInvoicedTemplateSalDetailByCode(
//            @RequestParam(value = "code", required = false) String code) {
//        throw new DcArgumentException("已经废弃");
//    }

    @Operation(summary = "通过发票类型获取商品行模板信息")
    @GetMapping(value = "/api/invoiced-temp-sal-dtl/byInvoiceType")
    public ListResponse<InvoicedTemplateSalDetailVo> getInvoicedTemplateSalDetailsByInvoiceType(
        ServerHttpRequest request,
        ServerWebExchange exh,
        @Parameter(name = "放票类型", example = "PER_COMMON") @RequestParam(value = "invoiceType") String invoiceType) {
        log.info(LoggerHelper2.formatEnterLog(request));
        OldPageParam page = getPage2(request, exh, false);
        Long topCommId = AntRestUtils.getTopCommId(request);
        return invoiceService.getInvoicedTemplateSalDetailsByInvoiceType(
            invoiceType, topCommId,
            page.getPageNum() - 1, page.getPageSize()    // 保持和原有代码的一致
//                getIndex2(request) - 1, getSize2(request)
        );
    }

//    @Operation(summary = "保存所有商品行模板信息")
//    @PostMapping(value = "/api/invoiced-temp-sal-dtl/saveAll")
//    public ObjectResponse<Integer> saveAll(
//            ServerHttpRequest request,
//            @RequestBody List<InvoicedTemplateSalDetailVo> dtoList) {
//        log.info("保存所有商品行模板信息: {}, param = {}",
//                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(dtoList));
//        throw new DcArgumentException("已经废弃");
//    }

//    /**
//     * 设置商品行模板
//     *
//     * @param invoicedTemplateSalDetailVo
//     * @return
//     */
//    // 逻辑已经调整了, 使用接口: /api/invoiced-temp-sal-dtl/saveAll
//    @Deprecated
//    @RequestMapping(value = "/api/invoiced-temp-sal-dtl/save", method = RequestMethod.POST)
//    public ObjectResponse<InvoicedTemplateSalDetailVo> saveInvoicedTemplateSalDetailVo(
//            @RequestBody InvoicedTemplateSalDetailVo invoicedTemplateSalDetailVo, ServerHttpRequest request) {
//        throw new DcArgumentException("接口已废弃");
//    }

    /**
     * 删除商品行模板
     *
     * @param code
     * @return
     */
    @Deprecated
    @RequestMapping(value = "/api/invoiced-temp-sal-dtl/{code}/del", method = RequestMethod.POST)
    public ObjectResponse delete(@PathVariable(value = "code", required = false) String code) {
        throw new DcArgumentException("接口已废弃");
//        if (code == null) {
//            throw new DcArgumentException("参数错误");
//        }
//
//        ObjectResponse res = invoiceService.delete(code);
//        return res;
    }


    @Operation(summary = "开票用户列表")
    @GetMapping(value = "/api/invoiced-users")
    public ListResponse<InvoicedUserVo> getAllInvoicedUsers(
        ServerWebExchange exh,
        @RequestParam(value = "status", required = false) Boolean status,
        @RequestParam(value = "keyword", required = false) String keyword,
        ServerHttpRequest request) {
        log.info("获取开票用户列表: {}", LoggerHelper2.formatEnterLog(request));
//        String token = this.getToken(request);

//        if (StringUtils.isEmpty(token)) {
//            throw new DcTokenException("token为空");
//        }
        String commIdChain = AntRestUtils.getCommIdChain(request);
        Long topCommId = AntRestUtils.getTopCommId(request);

        OldPageParam page = getPage2(request, exh, false);

        return invoiceService.getAllInvoicedUsers(keyword, status, topCommId, commIdChain,
            page.getPageNum(), page.getPageSize());
    }

    @Operation(summary = "查询用户开票金额、周期 和自动开票标识 b端c端都是以发票数据库数据为准")
    @GetMapping(value = "/api/invoiced-users/getInvoicedUserAutoAmount")
    public ObjectResponse<InvoicedUserAutoAmountVo> getInvoicedUserAutoAmountVo(
        ServerHttpRequest request,
        @RequestParam(value = "userId") Long userId) {
        log.info("查询用户自动开票设置信息: {}", LoggerHelper2.formatEnterLog(request));

//        String token = this.getToken2(request);
//
//        if (StringUtils.isEmpty(token)) {
//            throw new DcTokenException("token为空");
//        }
//        Commercial commercial = invoiceService.queryCommercial(token);
        Long commId = AntRestUtils.getCommId(request);
        if (null == commId) {
            throw new DcArgumentException("登录无效，请重新登录");
        }

//        ObjectResponse<InvoicedUserAutoAmountVo> res =

        return invoiceService.getInvoicedUserAutoAmountVo(userId, commId);
    }

    /**
     * 设置用户开票金额和周期、自动开票标识 b端
     *
     * @param invoicedUserAutoAmountVo
     * @return
     */
    @PostMapping(value = "/api/invoiced-users/saveInvoicedUserAutoAmount")
    public BaseResponse saveInvoicedUserAutoAmount(
        ServerHttpRequest request,
        @RequestBody InvoicedUserAutoAmountVo invoicedUserAutoAmountVo) {
        if (invoicedUserAutoAmountVo.getUserId() == null) {
            throw new DcArgumentException("参数错误");
        }

        String token = this.getToken2(request);

        if (StringUtils.isEmpty(token)) {
            throw new DcTokenException("token为空");
        }
//        Commercial commercial = invoiceService.queryCommercial(token);

        BaseResponse res = invoiceService.saveInvoicedUserAutoAmount(invoicedUserAutoAmountVo);

        return res;
    }

    /**
     * b端标记已开票
     *
     * @param invoicedRecordChargerOrderSaveVo
     * @return
     */
    @PostMapping(value = "/api/invoiced-records/signHadExportToInvoice")
    public ObjectResponse<InvoicedRecordVo> signHadExportToInvoice(
        @RequestBody InvoicedRecordChargerOrderSaveVo invoicedRecordChargerOrderSaveVo,
        ServerHttpRequest request) {
        String token = this.getToken2(request);

        if (StringUtils.isEmpty(token)) {
            throw new DcTokenException("token为空");
        }

        if (invoicedRecordChargerOrderSaveVo == null) {
            throw new DcArgumentException("参数错误");
        }

        if (invoicedRecordChargerOrderSaveVo.getInvoicedRecordDTO() == null) {
            throw new DcArgumentException("操作失败，参数不全");
        }

        if (CollectionUtils.isEmpty(
            invoicedRecordChargerOrderSaveVo.getInvoicedRecordChargerOrderDTOs())) {
            throw new DcArgumentException("订单信息不全");
        }

        ObjectResponse<InvoicedRecordVo> res = invoiceService.signHadExportToInvoice(
            invoicedRecordChargerOrderSaveVo, token);
        return res;
    }

    /**
     * b端标记不可开票
     *
     * @param orderNoList
     * @return
     */
    @PostMapping(value = "/api/invoiced-records/signForbiddenExportToInvoice")
    public BaseResponse signForbiddenExportToInvoice(
        @RequestBody List<String> orderNoList, ServerHttpRequest request) {
        String token = this.getToken2(request);

        if (StringUtils.isEmpty(token)) {
            throw new DcTokenException("token为空");
        }

        if (CollectionUtils.isEmpty(orderNoList)) {
            throw new DcArgumentException("参数错误");
        }

        BaseResponse res = invoiceService.signForbiddenExportToInvoice(orderNoList, token);
        return res;

    }

    @Operation(summary = "获取用户开票记录列表")
    @PostMapping(value = "/api/invoiced-records")
    public ListResponse<InvoicedRecordVo> getAllInvoicedRecords(
        ServerHttpRequest request, @RequestBody ListInvoicedRecordParam param) {
        log.info(LoggerHelper2.formatEnterLog(request) + " param = {}", param);
        param.setCommId(AntRestUtils.getCommId(request));   // Long.valueOf(commId));
        return invoiceService.getAllInvoicedRecords(param);
    }

    @Operation(summary = "导出用户开票记录列表")
    @PostMapping(value = "/api/invoice/exportInvoicedRecord")
    public Mono<ObjectResponse<ExcelPosition>> exportInvoicedRecord(
        ServerHttpRequest request, @RequestBody ListInvoicedRecordParam param) {
        log.info("导出用户开票记录列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));

        param.setCommId(AntRestUtils.getCommId(request));   // Long.valueOf(commId));

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("开票记录列表")
            .setFunctionMap(DownloadFunctionType.INVOICED_RECORD)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return invoiceService.exportInvoicedRecord(param);
    }

    @Operation(summary = "导出用户开票记录列表(全电发票模板)")
    @PostMapping(value = "/api/invoice/exportInvoicedNSR")
    public Mono<ObjectResponse<ExcelPosition>> exportInvoicedNSR(
        ServerHttpRequest request, @RequestBody ListInvoicedRecordParam param) {
        log.info("导出用户开票记录列表(全电发票模板): {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));

        param.setCommId(AntRestUtils.getCommId(request));   // Long.valueOf(commId));

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("NSR-全电开票批量导入记录")
            .setFunctionMap(DownloadFunctionType.NSR_INVOICED_RECORD)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
    }

    @Operation(summary = "根据用户ID查询开票记录")
    @GetMapping(value = "/api/invoiced-records-user")
    public ListResponse<InvoicedRecordVo> getAllInvoicedRecordsByUserId(
        ServerHttpRequest request,
        ServerWebExchange exh,
        @RequestParam(value = "userId", required = false) Long userId) {
        String token = this.getToken2(request);

        if (StringUtils.isEmpty(token)) {
            throw new DcTokenException("token为空");
        }
        OldPageParam page = getPage2(request, exh, false);
        return invoiceService.getAllInvoicedRecordsByUserId(userId, page.getPageNum(),
            page.getPageSize());
    }

    /**
     * 根据userId和状态获取所有开票记录
     *
     * @param userId
     * @param status
     * @return
     */
    @GetMapping(value = "/api/invoiced-records/getInvoicedRecordsByUserId")
    public ListResponse<InvoicedRecordVo> getAllInvoicedRecordsByUserIdAndStatus(
        ServerWebExchange exh,
        @RequestParam(value = "userId", required = false) Long userId,
        @RequestParam(value = "invoicedStatus", required = false) String status,
        ServerHttpRequest request) {

        if (userId == null) {
            throw new DcArgumentException("参数错误");
        }

        if (status == null) {
            throw new DcArgumentException("参数错误");
        }
        OldPageParam page = getPage2(request, exh, false);
        ListResponse<InvoicedRecordVo> res = invoiceService.getAllInvoicedRecordsByUserIdAndStatus(
            userId, status, page.getPageNum(), page.getPageSize());
        return res;
    }

    /**
     * 变更发票记录的状态
     *
     * @param dto
     * @return
     */
    @RequestMapping(value = "/api/invoiced-records/changeStatus", method = RequestMethod.POST)
    public ObjectResponse<InvoicedRecordVo> changeStatus(
        @RequestBody InvoicedRecordUpdateStatusVo dto) {

        if (dto.getId() == null) {
            throw new DcArgumentException("参数错误");
        }

        if (dto.getStatus() == null) {
            throw new DcArgumentException("参数错误");
        }

        ObjectResponse<InvoicedRecordVo> res = invoiceService.changeStatus(dto);
        return res;
    }

    @CheckToken
    @Operation(summary = "变更发票记录状态为审批不通过")
    @PostMapping(value = "/api/invoiced-records/changeSomeStatus")
    public Mono<ListResponse<InvoicedRecordVo>> changeSomeStatus(
        @RequestBody IdListVo idList, ServerHttpRequest request) {
        log.info("变更发票记录状态为审批不通过: idList = {}", idList.getIdList());
        if (com.cdz360.base.utils.CollectionUtils.isEmpty(idList.getIdList())) {
            throw new DcArgumentException("请提供发票记录ID");
        }

//        IotAssert.isTrue(com.cdz360.base.utils.CollectionUtils.isNotEmpty(idList.getUserIdList()), "缺少入参");
//        String token = this.getToken2(request);
//
//        if (StringUtils.isEmpty(token)) {
//            throw new DcTokenException("token为空");
//        }
//
//        if (idList.getIdList() == null) {
//            throw new DcArgumentException("参数错误");
//        }

//        ListResponse<InvoicedRecordVo> res = invoiceService.changeSomeStatus(idList, token);
//        ListResponse<InvoicedRecordVo> res = invoiceService.changeSomeStatus(idList);
//        commSysLogService.changeSomeStatusLog(idList.getUserIdList().stream().distinct().collect(Collectors.toList()),
//                request);

        return Mono.just(idList)
            .map(invoiceService::changeSomeStatus)
            .doOnNext(res -> commSysLogService.changeSomeStatusLog(
                idList.getUserIdList().stream().distinct().collect(Collectors.toList()), request));
    }

    /**
     * 导入开票系统
     *
     * @param idList
     * @return
     */
    @Operation(summary = "将开票记录导入至开票系统")
    @RequestMapping(value = "/api/invoiced-records/exportToInvoice", method = RequestMethod.POST)
    public ListResponse<InvoicedRecordVo> exportToInvoice(
        ServerHttpRequest request,
        @RequestBody IdListVo idList) {
        log.info("将开票记录导入至开票系统: {}", LoggerHelper2.formatEnterLog(request));
        IotAssert.isTrue(com.cdz360.base.utils.CollectionUtils.isNotEmpty(idList.getUserIdList()),
            "缺少入参");
        String token = this.getToken2(request);

        if (StringUtils.isEmpty(token)) {
            throw new DcTokenException("token为空");
        }

        if (idList.getIdList() == null) {
            throw new DcArgumentException("参数错误");
        }

        ListResponse<InvoicedRecordVo> res = invoiceService.exportToInvoice(idList, token);
        commSysLogService.exportToInvoiceLog(
            idList.getUserIdList().stream().distinct().collect(Collectors.toList()),
            request);
        return res;
    }

    @Operation(summary = "获取电子发票PDF")
    @RequestMapping(value = "/api/invoiced-records/getPdfUrl", method = RequestMethod.GET)
    public ObjectResponse<Map<String, String>> getPdfUrl(
        ServerHttpRequest request,
        @RequestParam(value = "id", required = false) Long id) {
        log.info(LoggerHelper2.formatEnterLog(request));

        if (id == null) {
            throw new DcArgumentException("参数错误");
        }

        return invoiceService.getPdfUrl(id);
    }

    /**
     * 生成物流单
     *
     * @param idList
     * @return
     */
    @PostMapping(value = "/api/invoiced-records/trackingNumber")
    public ObjectResponse<String> createTrackingNumber(@RequestBody IdListVo idList,
        ServerHttpRequest request) {
        String token = this.getToken2(request);

        if (StringUtils.isEmpty(token)) {
            throw new DcTokenException("token为空");
        }

        if (idList.getIdList() == null) {
            throw new DcArgumentException("参数错误");
        }
        ObjectResponse<String> res = invoiceService.createTrackingNumber(idList, token);
        return res;
    }

    /**
     * 编辑开票控制信息
     *
     * @param invoicedConfigMegVo
     * @return
     */
    @PostMapping(value = "/api/invoiced-configs/saveConfig")
    public ObjectResponse<InvoicedConfigMegVo> saveMegConfig(
        ServerHttpRequest request,
        @RequestBody InvoicedConfigMegVo invoicedConfigMegVo) {
//        String token = this.getToken2(request);
//
//        if (StringUtils.isEmpty(token)) {
//            throw new DcTokenException("token为空");
//        }
        Long topCommId = AntRestUtils.getTopCommId(request);
        if (null == topCommId) {
            throw new DcTokenException("登录信息已经失效，请重新登录");
        }

        invoicedConfigMegVo.setCommercialId(topCommId);
        ObjectResponse<InvoicedConfigMegVo> res = invoiceService.saveMegConfig(invoicedConfigMegVo);
        commSysLogService.saveMegConfigLog(request);
        return res;
    }

    /**
     * 获取开票控制信息
     *
     * @return
     */
    @Operation(summary = "获取开票控制信息")
    @GetMapping(value = "/api/invoiced-configs/getInvoicedMegConfig")
    public ObjectResponse<InvoicedConfigMegVo> getInvoicedMegConfig(ServerHttpRequest request) {
        String token = getToken2(request);

        if (token == null) {
            throw new DcTokenException("token为空");
        }

        ObjectResponse<InvoicedConfigMegVo> res = invoiceService.getInvoicedMegConfig(token);
        return res;
    }

    /**
     * 查询用户的充电记录
     *
     * @param status
     * @param invoicedFlag
     * @param keyword
     * @param startDate
     * @param endDate
     * @return
     */
    @Operation(summary = "获取用户的充电记录")
    @GetMapping(value = "/api/invoiced-charger-orders")
    public ListResponse<InvoicedChargerOrderVo> getAllInvoicedChargerOrders(
        ServerWebExchange exh,
        @RequestParam(value = "customerId", required = false) Long customerId,
        @RequestParam(value = "status", required = false) Integer status,
        @RequestParam(value = "invoicedFlag", required = false) Boolean invoicedFlag,//是否已经关联了开票申请
        @RequestParam(value = "keyword", required = false) String keyword,
        @RequestParam(value = "startDate", required = false) String startDate,
        @RequestParam(value = "endDate", required = false) String endDate,
        ServerHttpRequest request) {
        String token = this.getToken2(request);

        if (StringUtils.isEmpty(token)) {
            throw new DcTokenException("token为空");
        }
        if (customerId == null) {
            throw new DcArgumentException("参数错误");
        }

        OldPageParam page = getPage2(request, exh, false);

        return invoiceService.getAllInvoicedChargerOrders(
            customerId, status, invoicedFlag, keyword, startDate, endDate, token, page.getPageNum(),
            page.getPageSize());
    }

    @Operation(summary = "查看开票记录的消费明细")
    @GetMapping(value = "/api/invoiced-charger-orders/byInvoicedId")
    public ListResponse<InvoicedChargerOrderVo> getAllInvoicedChargerOrders(
        ServerHttpRequest request,
        ServerWebExchange exh,
        @Parameter(name = "申请开票记录ID", required = true) @RequestParam(value = "invoicedId") Long invoicedId) {
        log.info(LoggerHelper2.formatEnterLog(request));

        if (invoicedId == null) {
            throw new DcArgumentException("参数错误");
        }

        String token = this.getToken2(request);

        IotAssert.isNotBlank(token, "请登陆");

//        String token = this.getToken(request);
//
//        if (StringUtils.isEmpty(token)) {
//            throw new DcTokenException("token为空");
//        }

        OldPageParam page = getPage2(request, exh, false);
        return invoiceService.getAllInvoicedChargerOrders(token, invoicedId, page.getPageNum(),
            page.getPageSize());
    }

    /**
     * 查询用户的充电记录2c
     *
     * @param customerId
     * @param status
     * @param invoicedFlag
     * @param keyword
     * @return
     */
    @GetMapping(value = "/api/invoiced-charger-orders2c")
    public ListResponse<InvoicedChargerOrderVo> getAllInvoicedChargerOrders2c(
        ServerWebExchange exh,
        @RequestParam(value = "customerId", required = true) Long customerId,
        @RequestParam(value = "status", required = false) Integer status,
        @RequestParam(value = "invoicedFlag", required = false) Boolean invoicedFlag,//是否已经关联了开票申请
        @RequestParam(value = "keyword", required = false) String keyword,
        ServerHttpRequest request) {

        String token = this.getToken2(request);

        if (StringUtils.isEmpty(token)) {
            throw new DcTokenException("token为空");
        }
        if (customerId == null) {
            throw new DcArgumentException("参数错误");
        }
        OldPageParam page = getPage2(request, exh, false);
        ListResponse<InvoicedChargerOrderVo> res = invoiceService.getAllInvoicedChargerOrders2c(
            customerId, status, invoicedFlag, keyword, token, page.getPageNum(),
            page.getPageSize());
        return res;
    }

    /**
     * 获取用户默认模板
     *
     * @param userId
     * @return
     */
    @PostMapping(value = "/api/invoiced-models/getDefaultInvoicedModel")
    public ObjectResponse<InvoicedModelVo> getDefaultInvoicedModel(
        @RequestParam(value = "userId", required = false) Long userId) {
        if (userId == null) {
            throw new DcArgumentException("参数错误");
        }
        ObjectResponse<InvoicedModelVo> res = invoiceService.getDefaultInvoicedModel(userId);
        return res;
    }

    @GetMapping(value = "/api/invoiced-models/getCorpInfoByProduct")
    public ObjectResponse<InvoicedModelVo> getCorpInfoByProduct(
        @RequestParam(value = "productId") Long productId) {
        if (productId == null) {
            throw new DcArgumentException("参数错误");
        }
        ObjectResponse<InvoicedModelVo> res = invoiceService.getCorpInfoByProduct(productId);
        return res;
    }

    @Operation(summary = "用户开票模板列表")
    @PostMapping(value = "/api/invoiced-models/getInvoicedModelsByUserIdAndType")
    public ListResponse<InvoicedModelVo> getInvoicedModelsByUserIdAndType(
        @RequestBody InvoicedModelSearchParam param) {
        log.info("用户开票模板列表: {}", JsonUtils.toJsonString(param));

        return invoiceService.getInvoicedModelsByUserIdAndTypeSortable(param);
    }

    @Operation(summary = "获取企业客户开票设置抬头信息")
    @GetMapping(value = "/api/invoiced-models/getCorpInvoiceModel")
    public ObjectResponse<InvoicedModelDTO> getCorpInvoiceModel(
        @RequestParam(value = "userId") Long userId) {
        log.info("获取企业客户开票设置抬头信息: {}", userId);

        return invoiceService.getCorpInvoiceModel(userId);
    }

}
