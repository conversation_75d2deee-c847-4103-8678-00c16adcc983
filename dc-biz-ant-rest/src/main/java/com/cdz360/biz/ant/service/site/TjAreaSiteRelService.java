package com.cdz360.biz.ant.service.site;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.ant.feign.reactor.BizTjFeignClient;
import com.cdz360.biz.model.tj.kc.param.ListSiteWithinTjAreaParam;
import com.cdz360.biz.model.tj.kc.vo.SiteWithinTjVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TjAreaSiteRelService {

    @Autowired
    private BizTjFeignClient bizTjFeignClient;

    public Mono<ListResponse<SiteWithinTjVo>> findSiteWithinTjArea(
        ListSiteWithinTjAreaParam param) {
//        IotAssert.isTrue(CollectionUtils.isNotEmpty(
//            param.getWithinAidList()), "投建区域ID列表不能为空");
        return bizTjFeignClient.findSiteWithinTjArea(param);
    }
}
