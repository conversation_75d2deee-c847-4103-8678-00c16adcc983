package com.cdz360.biz.ant.aop;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.utils.RedisUtil;
import com.cdz360.biz.model.common.constant.Constant;
import com.chargerlinkcar.framework.common.domain.SysUser;
import com.chargerlinkcar.framework.common.domain.vo.Authority;
import com.chargerlinkcar.framework.common.exception.ChargerlinkException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.lang.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;

/**
 * 用于检查token
 *
 * <AUTHOR>
 * @since 2019/5/14 13:45
 */

@Component
@Aspect
public class CheckAuthAround {

    //public static final String USER2TOKEN_PREFIX = "authc:user-name-token";
    public static final String TOKEN2USER_PREFIX = "authc:token-user";
    @Autowired
    private RedisUtil redisUtil;
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    private Object aroundX(ProceedingJoinPoint point)
        throws InstantiationException, IllegalAccessException, IllegalArgumentException, InvocationTargetException, NoSuchMethodException, SecurityException {
        Method method = ((MethodSignature) point.getSignature()).getMethod();
        Object objRt = null;
        try {
            CheckAuth shelter = method.getAnnotation(CheckAuth.class);
            String code = shelter.code();
            String[] codesOr = shelter.codesOr();
            ServerHttpRequest serverHttpRequest = null;
            //查找参数中的HttpServletRequest，
            for (Object arg : point.getArgs()) {
                if (arg instanceof ServerHttpRequest) {
                    serverHttpRequest = (ServerHttpRequest) arg;
                    break;
                }
            }
            if (serverHttpRequest == null) {
                throw new DcServiceException("token为空");
            }
            String token = this.getToken(serverHttpRequest);
            SysUser sysUser = this.getUser(token);
            if (sysUser == null) {
                throw new DcServiceException("token无效");
            }
            List<Authority> authorityList = sysUser.getAuthorityList();
            if (CollectionUtils.isEmpty(authorityList)) {
                throw new DcServiceException("用户无权限");
            }
            boolean hasCode = false;
            for (Authority e : authorityList) {
                if (code.equals(e.getCode())) {
                    hasCode = true;
                    break;
                }

            }
            if (CollectionUtils.isNotEmpty(authorityList) && codesOr.length > 0) {
                Set userAuth = authorityList.stream().map(Authority::getCode)
                    .collect(Collectors.toSet());
                Set interfaceAuth = List.of(codesOr).stream().collect(Collectors.toSet());
                int allCount = userAuth.size() + interfaceAuth.size();
                // auth mix
                interfaceAuth.addAll(userAuth);
                if (interfaceAuth.size() < allCount) {
                    hasCode = true;
                }
            }
            if (!hasCode) {
                throw new DcServiceException("用户无权限");
            }

            objRt = point.proceed(point.getArgs());
        } catch (IllegalArgumentException | ChargerlinkException e) {// Assert 失败
            logger.error(e.getMessage(), e);
            throw e;
        } catch (Throwable e) {
            logger.error(e.getMessage(), e);
            throw new DcServiceException(e.getMessage());

        }

        return objRt;
    }

    @Around(value = "@annotation(com.cdz360.biz.ant.aop.CheckAuth)")
    public Object around(ProceedingJoinPoint point) {
        try {
            return aroundX(point);
        } catch (IllegalArgumentException e) {
            throw e;
        } catch (InstantiationException | IllegalAccessException | InvocationTargetException |
                 NoSuchMethodException | SecurityException e) {
            logger.error(e.getMessage(), e);
            throw new DcServiceException(e.getMessage());
        }
    }

    private String getToken(ServerHttpRequest req) {
        String token = req.getHeaders().getFirst(Constant.CURRENT_USER_TOKEN);
        if (StringUtils.isBlank(token)) {
            token = req.getQueryParams().getFirst("token");
        }
        return token;
    }

    private SysUser getUser(String token) {
        String userJson = getUserJson(token);
        if (StringUtils.isEmpty(userJson)) {
            return null;
        }
        return JsonUtils.fromJson(userJson, SysUser.class);
//        return JSONObject.parseObject(userJson, SysUser.class);
    }

    private String getUserJson(String token) {
        return redisUtil.get(TOKEN2USER_PREFIX, token);
    }
}
