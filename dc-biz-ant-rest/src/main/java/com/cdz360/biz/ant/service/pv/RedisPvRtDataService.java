package com.cdz360.biz.ant.service.pv;

import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.domain.dto.PvInv;
import com.cdz360.biz.ant.domain.request.PvGroupParam.Group;
import com.cdz360.biz.ant.domain.vo.PvRtDataGroupItem;
import com.cdz360.biz.ant.domain.vo.PvRtDataGroupSampling;
import com.cdz360.biz.ant.domain.vo.PvRtDataPowerSampling;
import com.cdz360.biz.ant.domain.vo.RedisPvRtData;
import com.cdz360.biz.model.trading.iot.dto.PvRtDataDto;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Flux;

@Slf4j
@Service
public class RedisPvRtDataService {
    private static final DateTimeFormatter TIME_POINT_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");
    private final static DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    private static final String PRE_REDIS_KEY = "pv:";

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 获取逆变器运行时数据数量
     *
     * @param dno 逆变器编号
     * @param date 日期
     * @return
     */
    public Long countGtiRtData(String dno, LocalDate date) {
        String key = formatKey(dno, date);
        return redisTemplate.opsForList().size(key);
    }

    /**
     * 获取逆变器运行时数据
     *
     * @param dno 逆变器编号
     * @param date 日期
     * @param start
     * @param end
     * @return
     */
    public List<RedisPvRtData> findGtiRtData(String dno, LocalDate date, long start, long end) {
        String key = formatKey(dno, date);
        return findGtiRtData(key, start, end);
    }

    /**
     * 获取逆变器运行时数据
     *
     * @param start
     * @param end
     * @return
     */
    public List<RedisPvRtData> findGtiRtData(String key, long start, long end) {
        Long size = redisTemplate.opsForList().size(key);
        if (null == size) {
            return List.of();
        }

        if (end > size) {
            end = -1;
        }

        return redisTemplate.opsForList()
                .range(key, start, end)
                .stream()
                .map(value -> JsonUtils.fromJson(value, RedisPvRtData.class))
                .collect(Collectors.toList());
    }

    /**
     * 从redis中采样功率
     *
     * @param dnoList 逆变器编号
     * @param date 日期
     * @param dis  采样间距
     * @return
     */
    public Flux<PvRtDataPowerSampling> dayPowerSampling4Redis(
            List<String> dnoList, LocalDate date, int dis) {
        final List<LocalDateTime> times = splitDay(date, dis);
        log.debug("时段: time = {}", times.size());
        return this.dayPowerSampling4Redis(dnoList, date, times);
    }

    /**
     * 从redis中采样功率
     *
     * @param dnoList 逆变器编号
     * @param date    日期
     * @param times   采样时间点
     * @return
     */
    public Flux<PvRtDataPowerSampling> dayPowerSampling4Redis(
            List<String> dnoList, LocalDate date, final List<LocalDateTime> times) {
        if (CollectionUtils.isEmpty(dnoList)) {
            return Flux.fromIterable(times)
                    .map(time -> new PvRtDataPowerSampling()
                            .setTime(time.format(TIME_POINT_FORMATTER))
                            .setOutPower(0L));
        }

        return Flux.fromIterable(dnoList)
                .flatMap(dno -> this.dayPowerSampling4Redis(formatKey(dno, date), times))
                .collectList()
                .map(list -> {
                    Map<String, Long> collect = list.stream().collect(Collectors.groupingBy(
                            PvRtDataPowerSampling::getTime,
                            Collectors.summingLong(PvRtDataPowerSampling::getOutPower)));

                    List<PvRtDataPowerSampling> result = new ArrayList<>();
                    for(String time: collect.keySet()) {
                        result.add(new PvRtDataPowerSampling()
                                .setTime(time)
                                .setOutPower(collect.get(time)));
                    }
                    return result.stream()
                            .sorted(Comparator.comparing(PvRtDataPowerSampling::getTime));
                })
                .flatMapMany(Flux::fromStream);
    }

    private Flux<PvRtDataPowerSampling> dayPowerSampling4Redis(String key, List<LocalDateTime> times) {
        List<PvRtDataPowerSampling> samplingList = new ArrayList<>();
        try {
            boolean hasLine = true;
            RedisPvRtData last = null;
            int start = 0;
            final int size = 100;

            List<RedisPvRtData> dataList = this.findGtiRtData(key, start, size);
            if (CollectionUtils.isEmpty(dataList)) {
                hasLine = false;
            }

            int dataSize = dataList.size();
            int idx = 0;
            for (LocalDateTime time : times) {
                if (null != last) {
                    LocalDateTime temp = last.getTime()
                            .withSecond(0)
                            .withNano(0);
                    if (time.isBefore(temp)) {
                        addZeroSampling(samplingList, time);
                        continue;
                    }
                }

                if (hasLine) {
                    while (true) {
                        if (idx == dataSize) {
                            start = start + size;
                            dataList = this.findGtiRtData(key, start, start + 100);
                            if (CollectionUtils.isEmpty(dataList)) {
                                hasLine = false;
                                break;
                            }

                            dataSize = dataList.size();
                            idx = 0;
                        }

                        RedisPvRtData rtData = dataList.get(idx);
                        if (null == rtData || rtData.getTime() == null) {
                            continue;
                        }

                        LocalDateTime temp = rtData.getTime()
                                .withSecond(0)
                                .withNano(0);

                        if (temp.isBefore(time)) {
                            idx = idx + 1;
                            continue;
                        } else if (temp.isEqual(time)) {
                            samplingList.add(new PvRtDataPowerSampling()
                                    .setTime(time.format(TIME_POINT_FORMATTER))
                                    .setOutPower(this.outPowerFormat(rtData.getOutPower())));
                            idx = idx + 1;
                            last = rtData;
                            break;
                        } else if (temp.isAfter(time)) {
                            if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(samplingList)
                                    && samplingList.get(samplingList.size() - 1).getOutPower() != null
                                    && samplingList.get(samplingList.size() - 1).getOutPower() > 0) {
                                BigDecimal lastV = last != null && last.getOutPower() != null ? last.getOutPower() : BigDecimal.ZERO;
                                BigDecimal value = DecimalUtils.add(lastV, rtData.getOutPower()).divide(BigDecimal.valueOf(2), 2,
                                    RoundingMode.HALF_UP);
                                samplingList.add(new PvRtDataPowerSampling()
                                        .setTime(time.format(TIME_POINT_FORMATTER))
                                        .setOutPower(this.outPowerFormat(value)));
                            } else {
                                addZeroSampling(samplingList, time);
                            }
                            break;
                        } else {
                            idx = idx + 1;
                        }
                        rtData = null;
                    }
                }

                if (!hasLine) {
                    addZeroSampling(samplingList, time);
                }
            }
        } catch (Exception e) {
            // nothing
            log.error("err = {}", e.getMessage(), e);
        }
        return Flux.fromIterable(samplingList);
    }

    private Long outPowerFormat(BigDecimal value) {
        return value != null ? value.longValue() : 0;
    }

    private static List<LocalDateTime> splitDay(LocalDate date, int dis) {
        List<LocalDateTime> times = new ArrayList<>();
        LocalDateTime start = date.atStartOfDay();
        LocalDateTime end = date.plusDays(1).atStartOfDay();
        LocalDateTime now = LocalDateTime.now();
        while (start.isBefore(end) && start.isBefore(now)) {
            times.add(start);
            start = start.plusMinutes(dis);
        }
        return times;
    }

    private static void addZeroSampling(List<PvRtDataPowerSampling> list, LocalDateTime time) {
        list.add(new PvRtDataPowerSampling()
                .setTime(time.format(TIME_POINT_FORMATTER))
                .setOutPower(0L));
    }

    private static void addZeroGroupSampling(List<PvRtDataGroupSampling> list, LocalDateTime time,
        List<Group> pvGroupIdList) {
        list.add(new PvRtDataGroupSampling()
            .setTime(time.format(TIME_POINT_FORMATTER))
            .setItems(pvGroupIdList.stream()
                .map(e -> new PvRtDataGroupItem().setIdx(e.getIdx()).setValue(BigDecimal.ZERO))
                .collect(Collectors.toList())));
    }

    /**
     * 获取最近一条运行时数据
     *
     * @param dno
     * @param date
     * @return
     */
    public RedisPvRtData latestRtData(String dno, LocalDate date) {
        String key = formatKey(dno, date);
        return this.latestRtData4RedisKey(key);
    }

    /**
     * 获取最近一条运行时数据
     *
     * @param key
     * @return
     */
    public RedisPvRtData latestRtData4RedisKey(String key) {
        Long size = redisTemplate.opsForList().size(key);
        if (null == size) {
            return null;
        }

        String value = redisTemplate.opsForList()
                .index(key, size - 1);
        if (StringUtils.isBlank(value)) {
            return null;
        }
        return JsonUtils.fromJson(value, RedisPvRtData.class);
    }

    private static String formatKey(String dno, LocalDate date) {
        return PRE_REDIS_KEY + dno + ":" + date.format(DATE_FORMATTER);
    }

    private static String formatKey(String dno, String date) {
        return PRE_REDIS_KEY + dno + ":" + date;
    }

    /**
     * 从redis中采样组串信息
     *
     * @param date    日期
     * @param times   采样时间点
     * @return
     */
    public Flux<PvRtDataGroupSampling> dayGroupSampling4Redis(final Integer type,
        final String dno, final List<Group> pvGroupIdList,
        final LocalDate date, final List<LocalDateTime> times) {
        if (StringUtils.isBlank(dno)) {
            return Flux.fromIterable(times)
                .map(time -> new PvRtDataGroupSampling()
                    .setTime(time.format(TIME_POINT_FORMATTER))
                    .setItems(pvGroupIdList.stream()
                        .map(e -> new PvRtDataGroupItem().setIdx(e.getIdx())
                            .setValue(BigDecimal.ZERO))
                        .collect(Collectors.toList())));
        }

        return this.dayGroupSampling4Redis(type, pvGroupIdList, formatKey(dno, date), times);
    }

    private Flux<PvRtDataGroupSampling> dayGroupSampling4Redis(
        final Integer type, final List<Group> pvGroupIdList,
        final String key, final List<LocalDateTime> times) {
        final boolean subordinateToEss = false;
        List<PvRtDataGroupSampling> samplingList = new ArrayList<>();
        try {
            boolean hasLine = true;
            RedisPvRtData last = null;
            int start = 0;
            final int size = 100;

            List<RedisPvRtData> dataList = this.findGtiRtData(key, start, size);
            if (CollectionUtils.isEmpty(dataList)) {
                hasLine = false;
            }

            int dataSize = dataList.size();
            int idx = 0;
            for (LocalDateTime time : times) {
                if (null != last) {
                    LocalDateTime temp = last.getTime()
                        .withSecond(0)
                        .withNano(0);
                    if (time.isBefore(temp)) {
                        addZeroGroupSampling(samplingList, time, pvGroupIdList);
                        continue;
                    }
                }

                if (hasLine) {
                    while (true) {
                        if (idx == dataSize) {
                            start = start + size;
                            dataList = this.findGtiRtData(key, start, start + 100);
                            if (CollectionUtils.isEmpty(dataList)) {
                                hasLine = false;
                                break;
                            }

                            dataSize = dataList.size();
                            idx = 0;
                        }

                        RedisPvRtData rtData = dataList.get(idx);
                        if (null == rtData || rtData.getTime() == null) {
                            continue;
                        }

                        LocalDateTime temp = rtData.getTime()
                            .withSecond(0)
                            .withNano(0);

                        if (temp.isBefore(time)) {
                            idx = idx + 1;
                            continue;
                        } else if (temp.isEqual(time)) {
                            samplingList.add(new PvRtDataGroupSampling()
                                .setTime(time.format(TIME_POINT_FORMATTER))
                                .setItems(achieveValue(type, pvGroupIdList, subordinateToEss, rtData)));
                            idx = idx + 1;
                            last = rtData;
                            break;
                        } else if (temp.isAfter(time)) {
                            boolean tempBoo =
                                com.cdz360.base.utils.CollectionUtils.isNotEmpty(samplingList)
                                    && samplingList.get(samplingList.size() - 1).getItems()
                                    .stream().anyMatch(e -> DecimalUtils.gtZero(e.getValue()));
                            if (tempBoo) { //前一个值是非零的
                                samplingList.add(new PvRtDataGroupSampling()
                                    .setTime(time.format(TIME_POINT_FORMATTER))
                                    .setItems(achieveValue(type, pvGroupIdList, subordinateToEss, last, rtData)));
                            } else {
                                addZeroGroupSampling(samplingList, time, pvGroupIdList);
                            }
                            break;
                        } else {
                            idx = idx + 1;
                        }
                        rtData = null;
                    }
                }

                if (!hasLine) {
                    addZeroGroupSampling(samplingList, time, pvGroupIdList);
                }
            }
        } catch (Exception e) {
            // nothing
            log.error("err = {}", e.getMessage(), e);
        }
        return Flux.fromIterable(samplingList);
    }

    /**
     * 获取组串电流电压数据
     * @param type 类型 1电压 2电流
     * @param pvGroupList 要获取的组串
     * @param subordinateToEss 是否从属于ESS
     * @param dto 数据来源
     * @return
     */
    public static List<PvRtDataGroupItem> achieveValue(Integer type, List<Group> pvGroupList,
        boolean subordinateToEss, Object dto) {
        return pvGroupList.stream().map(e -> {
            Optional<BigDecimal> valueOpt = Optional.empty();
            if (type == null || dto == null) {
                return new PvRtDataGroupItem()
                    .setIdx(e.getIdx())
                    .setValue(valueOpt.orElse(BigDecimal.ZERO));
            }
            String fieldName = null;
            if (type == 1) {
                fieldName = subordinateToEss ? ("voltageL" + e.getId())
                    : ("pv" + e.getId() + "Voltage");
            } else if (type == 2) {
                fieldName = subordinateToEss ? ("currentL" + e.getId())
                    : ("pv" + e.getId() + "Current");
            }
            if (fieldName != null) {
                try {
                    Field field;
                    if (subordinateToEss) {
                        Class<PvInv> clazz = PvInv.class;
                        field = clazz.getDeclaredField(fieldName);
                    } else {
                        Class<PvRtDataDto> clazz = PvRtDataDto.class;
                        field = clazz.getDeclaredField(fieldName);
                    }
                    field.setAccessible(true);
                    valueOpt = Optional.ofNullable((BigDecimal) field.get(dto));
                } catch (Exception ex) {
                    log.error("achieveValue error: {}", ex.getMessage());
                }
            }
            return new PvRtDataGroupItem()
                .setIdx(e.getIdx())
                .setValue(valueOpt.orElse(BigDecimal.ZERO));
        }).collect(Collectors.toList());
    }

    public static List<PvRtDataGroupItem> achieveValue(Integer type, List<Group> pvGroupIdList,
        boolean subordinateToEss,
        Object lastDto, Object currDto) {
        return pvGroupIdList.stream().map(e -> {
            BigDecimal value = BigDecimal.ZERO;
            if (type == null || lastDto == null || currDto == null) {
                return new PvRtDataGroupItem()
                    .setIdx(e.getIdx())
                    .setValue(value);
            }
            String fieldName = null;
            if (type == 1) {
                fieldName = subordinateToEss ? ("voltageL" + e.getId())
                    : ("pv" + e.getId() + "Voltage");
            } else if (type == 2) {
                fieldName = subordinateToEss ? ("currentL" + e.getId())
                    : ("pv" + e.getId() + "Current");
            }
            if (fieldName != null) {
                try {
                    Field field;
                    if (subordinateToEss) {
                        Class<PvInv> clazz = PvInv.class;
                        field = clazz.getDeclaredField(fieldName);
                    } else {
                        Class<PvRtDataDto> clazz = PvRtDataDto.class;
                        field = clazz.getDeclaredField(fieldName);
                    }
                    field.setAccessible(true);
                    BigDecimal lastValue = (BigDecimal) field.get(lastDto);
                    BigDecimal currValue = (BigDecimal) field.get(currDto);
                    value = DecimalUtils.add(lastValue, currValue)
                        .divide(BigDecimal.valueOf(2), 2, RoundingMode.HALF_UP);
                } catch (Exception ex) {
                    log.error("achieveValue error: {}", ex.getMessage());
                }
            }
            return new PvRtDataGroupItem()
                .setIdx(e.getIdx())
                .setValue(value);
        }).collect(Collectors.toList());
    }

}
