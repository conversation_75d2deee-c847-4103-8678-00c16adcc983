package com.cdz360.biz.ant.constant;

/**
 * WhiteCardsStatus
 *
 * @since 7/12/2019 9:38 AM
 * <AUTHOR>
 */
public enum  WhiteCardsStatus {
    ISSUE_SUCCESS("1", "下发成功") ,
    ISSUE_FAIL("2", "失败") ,
    ISSUE("3", "下发中") ,
    ;
    private final String code;
    private final String name;

    WhiteCardsStatus(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static WhiteCardsStatus valueOfCode(String code) {
        for (WhiteCardsStatus type : WhiteCardsStatus.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getName(){
        return name;
    }
}