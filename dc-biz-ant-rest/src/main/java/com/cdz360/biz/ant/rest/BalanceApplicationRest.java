package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.AntUserFeignClient;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.feign.BizBiFeignClient;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.ant.feign.reactor.DataCorePayBillFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorAuthCenterFeignClient;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.auth.user.po.SysUserPo;
import com.cdz360.biz.ds.cus.ro.balance.param.BalanceApplicationCheckParam;
import com.cdz360.biz.ds.cus.ro.balance.param.BalanceApplicationParam;
import com.cdz360.biz.model.cus.balance.po.BalanceApplicationCheckPo;
import com.cdz360.biz.model.cus.balance.po.BalanceApplicationPo;
import com.cdz360.biz.model.cus.balance.type.BalanceCheckResultType;
import com.cdz360.biz.model.cus.balance.type.BalanceCheckType;
import com.cdz360.biz.model.cus.balance.vo.BalanceApplicationCheckVo;
import com.cdz360.biz.model.cus.balance.vo.BalanceApplicationVo;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.oa.param.ApproveParam;
import com.cdz360.biz.oa.param.RechargeParam;
import com.cdz360.biz.utils.feign.oa.OaRechargeClient;
import com.cdz360.biz.utils.feign.user.UserFeignClient;
import com.cdz360.biz.utils.service.OaRechargeFormConvert;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * @since 6/25/2021 5:30 PM
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "账户金额变更", description = "账户金额变更")
@RequestMapping("/api/balanceApplication")
public class BalanceApplicationRest extends BaseController {

    @Autowired
    private AntUserFeignClient antUserFeignClient;

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private ReactorAuthCenterFeignClient reactorAuthCenterFeignClient;

    @Autowired
    private BizBiFeignClient bizBiFeignClient;

    @Autowired
    private OaRechargeClient oaRechargeClient;

    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;

    @Autowired
    private DataCorePayBillFeignClient dataCorePayBillFeignClient;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    public BalanceApplicationRest() {
    }

    @Operation(summary = "获取充值申请记录信息")
    @GetMapping(value = "/getById")
    public Mono<ObjectResponse<BalanceApplicationVo>> getBalanceApplyById(
        ServerHttpRequest request, @RequestParam("id") Long id) {
        log.info("获取充值申请记录信息: {}", LoggerHelper2.formatEnterLog(request));
        return userFeignClient.getBalanceApplyById(id)
            .doOnNext(FeignResponseValidate::check)
            .flatMap(x -> {
                if (null != x.getData().getApplierId()) {
                    return reactorAuthCenterFeignClient.getSysUserByIdList(
                            List.of(x.getData().getApplierId()))
                        .doOnNext(FeignResponseValidate::check)
                        .map(sysUser -> {
                            x.getData().setApplierName(sysUser.getData().get(0).getName());
                            return x;
                        });
                }
                return Mono.just(x);
            })
            .flatMap(x -> {
                if (null != x.getData().getRefundOrderId() &&
                    com.cdz360.biz.model.cus.balance.type.DepositFlowType.OUT_FLOW.equals(
                        x.getData().getFlowType())) {
                    return dataCorePayBillFeignClient.tkView(x.getData().getRefundOrderId())
                        .doOnNext(FeignResponseValidate::check)
                        .map(payBillVoObjectResponse -> {
                            x.getData().setPayTime(payBillVoObjectResponse.getData().getPayTime())
                                .setPayChannel(payBillVoObjectResponse.getData().getPayChannel());
                            return x;
                        });
                }
                return Mono.just(x);
            });
    }

    @PostMapping(value = "/search")
    public ListResponse<BalanceApplicationVo> search(ServerHttpRequest request,
        @RequestBody BalanceApplicationParam params) {
        log.info("查询申请列表。param: {}", params);

        // 当前登陆账户是否是顶级商户
        Long commId = AntRestUtils.getCommId(request);
        Long topCommId = AntRestUtils.getTopCommId(request);
        IotAssert.isTrue(commId != null && topCommId != null, "请登陆");
        params.setTopComm(commId.equals(topCommId));

        String commIdChain = AntRestUtils.getCommIdChain(request);
        IotAssert.isNotBlank(commIdChain, "请登陆后再尝试");
        params.setIdChain(commIdChain);

        if (StringUtils.isNotBlank(params.getApplier())) {
            // 根据申请人账号名 转 账号id
            ListResponse<SysUserPo> byUserNameLike = authCenterFeignClient.getByUserNameLike(
                params.getApplier());
            FeignResponseValidate.check(byUserNameLike);

            List<Long> applierIds = byUserNameLike.getData()
                .stream()
                .map(SysUserPo::getId)
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(applierIds)) {
                params.setApplierIds(applierIds);
            } else {
                // 找不到任何申请人信息，直接返回
                return new ListResponse<>();
            }
        }

        ListResponse<BalanceApplicationVo> appRes = antUserFeignClient.searchBalanceApplication(
            params);
        FeignResponseValidate.check(appRes);

        if (CollectionUtils.isEmpty(appRes.getData())) {
            return new ListResponse<>();
        }

        List<Long> userIds = appRes.getData()
            .stream()
            .map(BalanceApplicationPo::getApplierId)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());

        ListResponse<SysUserVo> sysUsers = authCenterFeignClient.getSysUserByIdList(userIds);
        FeignResponseValidate.check(sysUsers);

        Map<Long, SysUserVo> userMap = sysUsers.getData()
            .stream()
            .collect(Collectors.toMap(SysUserVo::getId, o -> o));

        return new ListResponse<>(
            appRes.getData()
                .stream()
                .map(e -> {
//                    BalanceApplicationVo ret = new BalanceApplicationVo();
//                    BeanUtils.copyProperties(e, ret);
                    SysUserVo sysUserVo = userMap.get(e.getApplierId());
                    if (sysUserVo != null) {
                        e.setApplierName(sysUserVo.getName());
                    }
                    return e;
                })
                .collect(Collectors.toList()),
            appRes.getTotal()
        );


    }

    @PostMapping(value = "/add")
    public Mono<ObjectResponse<Boolean>> add(ServerHttpRequest request,
        @RequestBody BalanceApplicationPo param) {
//        long userIdLong2 = this.getUserIdLong2(request);
        param.setApplierId(AntRestUtils.getSysUid(request));
        AntRestUtils.getSysUserName(request);
        Long commId = AntRestUtils.getCommId(request);
        param.setApplierCommId(commId);
        log.info("增加申请。param: {}", param);

        // 较少操作需要校验充值记录是否已经开票
        if (com.cdz360.biz.model.cus.balance.type.DepositFlowType.OUT_FLOW.equals(
            param.getFlowType())) {
            //flowType为OUT_FLOW时,对应的充值单号
            ObjectResponse<Boolean> checkRes = dataCoreFeignClient.checkRefPayBill(
                param.getOrderId());
            FeignResponseValidate.check(checkRes);

            if (checkRes.getData()) {
                throw new DcArgumentException("操作失败：该笔充值已关联了开票申请");
            }
        }

        ObjectResponse<BalanceApplicationPo> res = antUserFeignClient.addBalanceApplication(param);
        if (res.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS || res.getData() == null) {
            return Mono.just(RestUtils.buildObjectResponse(Boolean.FALSE));
        }

        // 较少操作需要校验充值记录是否已经开票
        if (com.cdz360.biz.model.cus.balance.type.DepositFlowType.OUT_FLOW.equals(
            param.getFlowType())) {
            //flowType为OUT_FLOW时,对应的充值单号
            ObjectResponse<Boolean> checkRes = dataCoreFeignClient.checkRefPayBill(
                param.getOrderId());
            FeignResponseValidate.check(res);

            if (checkRes.getData()) {
                throw new DcArgumentException("操作失败：该笔充值已关联了开票申请");
            }
        }

        if (Boolean.TRUE.equals(param.getDebugger())) {
            return Mono.just(RestUtils.buildObjectResponse(Boolean.TRUE));  // 旧逻辑兼容测试
        }
        BalanceApplicationPo ba = res.getData();
        RechargeParam oaParam = new RechargeParam();
        oaParam.setDataId(ba.getId())
            .setPhone(param.getPhone())
            .setUsername(param.getUsername())
            .setOUid(AntRestUtils.getSysUid(request))
            .setOName(AntRestUtils.getSysUserName(request))
            .setOPhone(AntRestUtils.getSysUserPhone(request))
            .setTopCommId(AntRestUtils.getTopCommId(request))
            .setTenantIdChain(AntRestUtils.getCommIdChain(request))
            .setFormVariables(OaRechargeFormConvert.bean2FormVar(param));

        return oaRechargeClient.startProcess(oaParam)
            .doOnNext(res2 -> {
                if (res2.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS &&
                    StringUtils.isNotBlank(res2.getData())) {
                    // 更新申请记录中的流程ID
                    userFeignClient.updateApplyOaProcessInstanceId(ba.getId(), res2.getData())
                        .subscribe(
                            x -> log.debug("流程实例ID[{}]已经写入充值记录[{}]", res2.getData(),
                                ba.getId()));
                } else {
                    log.error("充值申请数据异常，需要开发接入: {}", ba.getId());
                }
            })
            .map(res2 -> res2.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS
                ? RestUtils.buildObjectResponse(Boolean.TRUE)
                : RestUtils.buildObjectResponse(Boolean.FALSE));
    }

    @PostMapping(value = "/check")
    public Mono<ObjectResponse<Boolean>> check(
        ServerHttpRequest request, @RequestBody BalanceApplicationCheckParam param) {
        log.info("审批: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        long userIdLong2 = this.getUserIdLong2(request);
        param.setOperatorId(userIdLong2);

        IotAssert.isNotNull(param, "请传入参数");
        IotAssert.isNotNull(param.getApplicationId(), "请传入申请单号");
        IotAssert.isNotNull(param.getResult(), "请传入审批结果");
        IotAssert.isNotNull(param.getType(), "请传入审批类型");
        return Mono.just(param)
            .map(antUserFeignClient::checkBalanceApplication)
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .flatMap(x -> {
                if (x) {
                    if (BalanceCheckType.REVIEW.equals(param.getType())) {
                        return Mono.just(true); // 复核不需要
                    }
                    ApproveParam approveParam = new ApproveParam();
                    approveParam.setDataId(param.getApplicationId().toString());
                    approveParam.setUid(userIdLong2);
                    approveParam.setTaskId(""); // 这里标识旧逻辑与新逻辑适配方式
                    approveParam.setApprove(BalanceCheckResultType.ALLOW.equals(param.getResult()));
                    approveParam.setNote(param.getRemark());
                    approveParam.setAttachmentList(param.getAttachmentList());
                    return oaRechargeClient.assigneeHandleTask(approveParam);
                } else {
                    throw new DcServiceException("审批操作失败");
                }
            })
            .map(x -> RestUtils.buildObjectResponse(true));

    }

    @PostMapping(value = "/batchCheckReview")
    public ObjectResponse<Boolean> batchCheckReview(ServerHttpRequest request,
        @RequestBody BalanceApplicationCheckParam param) {

        long userIdLong2 = this.getUserIdLong2(request);
        param.setOperatorId(userIdLong2);
        log.info("审批。param: {}", param);

        return antUserFeignClient.batchCheckReviewBalanceApplication(param);
    }

    @PostMapping(value = "/getCheckList")
    public ListResponse<BalanceApplicationCheckVo> getCheckList(ServerHttpRequest request,
        @RequestBody BalanceApplicationCheckParam param) {
        log.info("审批列表。param: {}", param);

        ListResponse<BalanceApplicationCheckPo> checkListRes = antUserFeignClient.getCheckList(
            param);
        FeignResponseValidate.check(checkListRes);

        if (CollectionUtils.isEmpty(checkListRes.getData())) {
            return new ListResponse<>();
        }

        List<Long> userIds = checkListRes.getData()
            .stream()
            .map(BalanceApplicationCheckPo::getOperatorId)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());

        ListResponse<SysUserVo> sysUsers = authCenterFeignClient.getSysUserByIdList(userIds);
        FeignResponseValidate.check(sysUsers);

        Map<Long, SysUserVo> userMap = sysUsers.getData()
            .stream()
            .collect(Collectors.toMap(SysUserVo::getId, o -> o));

        return new ListResponse<>(
            checkListRes.getData()
                .stream()
                .map(e -> {
                    BalanceApplicationCheckVo ret = new BalanceApplicationCheckVo();
                    BeanUtils.copyProperties(e, ret);
                    SysUserVo sysUserVo = userMap.get(e.getOperatorId());
                    if (sysUserVo != null) {
                        ret.setOperatorName(sysUserVo.getName());
                    }
                    return ret;
                })
                .collect(Collectors.toList())
        );
    }

    @PostMapping(value = "/exportExcel")
    public Mono<ObjectResponse<ExcelPosition>> exportExcel(
        ServerHttpRequest request, @RequestBody BalanceApplicationParam param) {
        log.info("导出列表到excel: {}", param);
        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName(
                "金额变更申请" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")))
            .setFunctionMap(DownloadFunctionType.ACCOUNT_BALANCE_APPLY)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return bizBiFeignClient.exportBalanceApplicationExcel(param);
    }
}