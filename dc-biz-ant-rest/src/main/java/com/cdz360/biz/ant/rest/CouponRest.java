package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.chargerlinkcar.framework.common.rest.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 *
 * @since 7/27/2020 1:38 PM
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/api/coupon")
@Tag(name = "券模板相关接口", description = "券模板")
public class CouponRest extends BaseController {

    @Operation( summary = "查询用户券领取列表")
    @RequestMapping(value = "/getList", method = RequestMethod.POST)
    public BaseResponse getList(ServerHttpRequest request) {
        return null;
    }

    @Operation( summary = "获取最优抵扣券")
    @RequestMapping(value = "/getPriority", method = RequestMethod.POST)
    public BaseResponse getPriority(ServerHttpRequest request) {
        return null;
    }
}