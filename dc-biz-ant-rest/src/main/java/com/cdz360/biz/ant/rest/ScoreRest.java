package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.feign.reactor.ReactorUserFeignClient;
import com.cdz360.biz.ant.service.sysLog.CustomerSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.cus.score.dto.ScoreLevelDto;
import com.cdz360.biz.model.cus.score.dto.ScoreLogDto;
import com.cdz360.biz.model.cus.score.dto.ScoreSettingDto;
import com.cdz360.biz.model.cus.score.dto.ScoreUserDto;
import com.cdz360.biz.model.cus.score.param.ScoreSettingParam;
import com.cdz360.biz.model.cus.score.param.ScoreUpdateParam;
import com.cdz360.biz.model.cus.score.param.ScoreUserAddParam;
import com.cdz360.biz.model.cus.score.param.ScoreUserParam;
import com.cdz360.biz.model.cus.score.param.SearchScoreLogParam;
import com.cdz360.biz.model.cus.score.param.SearchScoreSettingParam;
import com.cdz360.biz.model.cus.score.param.SearchScoreUserParam;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * ScoreRest
 *
 * @since 1/5/2023 2:32 PM
 * <AUTHOR>
 */

@Slf4j
@RestController
@RequestMapping(value = "/api/score")
public class ScoreRest extends BaseController {

    @Autowired
    private ReactorUserFeignClient userFeignClient;

    @Autowired
    private CustomerSysLogService customerSysLogService;

    /**
     * 新增积分体系
     * @param request
     * @param param
     * @return
     */
    @PostMapping(value = "/addScoreSetting")
    public Mono<BaseResponse> addScoreSetting(
        ServerHttpRequest request,
        @RequestBody ScoreSettingParam param) {
        final Long sysUid = AntRestUtils.getSysUid(request);
        IotAssert.isNotNull(sysUid, "请登录");
        param.getScoreSettingPo().setSysUid(sysUid);
        param.setTopCommId(AntRestUtils.getTopCommId(request));
        return Mono.just(param)
            .flatMap(e -> userFeignClient.addScoreSetting(e))
            .map(e -> {
                if(e.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS) {
                    customerSysLogService.addAccountScoreSettingLog(
                        param.getScoreSettingPo().getName(),
                        request);
                }
                return e;
            });
    }

    /**
     * 获取积分体系列表
     * @param request
     * @param param
     * @return
     */
    @PostMapping(value = "/getScoreSettingList")
    public Mono<ListResponse<ScoreSettingDto>> getScoreSettingList(
        ServerHttpRequest request,
        @RequestBody SearchScoreSettingParam param) {
        log.info("getScoreSettingList, param=");
//        final Long sysUid = AntRestUtils.getSysUid(request);
        if(!AntRestUtils.isTopComm(request)) {
            final List<String> sysUserGids = AntRestUtils.getSysUserGids(request);
            if(CollectionUtils.isEmpty(sysUserGids)) {
                return Mono.just(new ListResponse<>(List.of(), 0L));
            }
            log.info("非顶级商户，设置场站组限制: {}", sysUserGids);
            param.setAccountSiteGidList(sysUserGids);
        }
        final Long topCommId = AntRestUtils.getTopCommId(request);
        param.setTopCommId(topCommId);

        return userFeignClient.getScoreSettingList(param);
    }

    /**
     * 删除积分体系
     * @param request
     * @param id
     * @return
     */
    @PostMapping(value = "/deleteScoreSetting")
    public Mono<BaseResponse> deleteScoreSetting(
        ServerHttpRequest request,
        @RequestParam("id") Long id,
        @RequestParam("settingName") String settingName) {
        return Mono.just(id)
            .flatMap(e -> userFeignClient.deleteScoreSetting(id))
            .map(e -> {
                if(e.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS) {
                    customerSysLogService.deleteAccountScoreSettingLog(
                        settingName,
                        request);
                }
                return e;
            });

    }

    /**
     * 启动积分体系
     * @param request
     * @param id
     * @return
     */
    @PostMapping(value = "/startScoreSetting")
    public Mono<BaseResponse> startScoreSetting(
        ServerHttpRequest request,
        @RequestParam("id") Long id,
        @RequestParam("settingName") String settingName) {
        return Mono.just(id)
            .flatMap(e -> userFeignClient.startScoreSetting(e))
            .map(e -> {
                if(e.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS) {
                    customerSysLogService.updateStatusAccountScoreSettingLog(
                        settingName,
                        request);
                }
                return e;
            });

    }

    /**
     * 停止积分体系
     * @param request
     * @param id
     * @return
     */
    @PostMapping(value = "/stopScoreSetting")
    public Mono<BaseResponse> stopScoreSetting(
        ServerHttpRequest request,
        @RequestParam("id") Long id,
        @RequestParam("settingName") String settingName) {
        return Mono.just(id)
            .flatMap(e -> userFeignClient.stopScoreSetting(e))
            .map(e -> {
                if(e.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS) {
                    customerSysLogService.updateStatusAccountScoreSettingLog(
                        settingName,
                        request);
                }
                return e;
            });

    }

    /**
     * 编辑积分体系
     * @param request
     * @param param
     * @return
     */
    @PostMapping(value = "/updateScoreSetting")
    public Mono<BaseResponse> updateScoreSetting(
        ServerHttpRequest request,
        @RequestBody ScoreSettingParam param) {
        param.setTopCommId(AntRestUtils.getTopCommId(request));
        return Mono.just(param)
            .flatMap(e -> userFeignClient.updateScoreSetting(e))
            .map(e -> {
                if(e.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS) {
                    customerSysLogService.updateAccountScoreSettingLog(
                        param.getScoreSettingPo().getName(),
                        request);
                }
                return e;
            });

    }

    /**
     * 修改积分（增/减）
     * @param request
     * @param param
     * @return
     */
    @PostMapping(value = "/updateScore")
    public Mono<BaseResponse> updateScore(
        ServerHttpRequest request,
        @RequestBody ScoreUpdateParam param) {
        final Long sysUid = AntRestUtils.getSysUid(request);
        IotAssert.isNotNull(sysUid, "请登录");
        param.setSysUid(sysUid);
        return userFeignClient.updateScore(param);
    }

    /**
     * 获取用户积分等级信息
     * @param request
     * @param userId
     * @return
     */
    @GetMapping(value = "/getScoreLevel")
    public Mono<ObjectResponse<ScoreLevelDto>> getScoreLevel(
        ServerHttpRequest request,
        @RequestParam("userId") Long userId,
        @RequestParam("scoreSettingId") Long scoreSettingId) {
        log.info("getScoreSettingList, userId: {}, scoreSettingId: {}", userId, scoreSettingId);
        return userFeignClient.getScoreLevel(userId, scoreSettingId);
    }

    /**
     * 获取用户积分列表
     * @param request
     * @param param
     * @return
     */
    @PostMapping(value = "/getScoreList")
    public Mono<ListResponse<ScoreLogDto>> getScoreList(
        ServerHttpRequest request,
        @RequestBody SearchScoreLogParam param) {
        log.info("getScoreSettingList, param={}", JsonUtils.toJsonString(param));
        return userFeignClient.getScoreList(param);
    }

    @PostMapping(value = "/getScoreUserList")
    public Mono<ListResponse<ScoreUserDto>> getScoreUserList(
        @RequestBody SearchScoreUserParam param) {
        log.info("getScoreUserList, param={}", JsonUtils.toJsonString(param));
        return userFeignClient.getScoreUserList(param);
    }

    @PostMapping(value = "/deleteScoreUser")
    public Mono<BaseResponse> deleteScoreUser(@RequestBody  ScoreUserParam param) {
        log.info("deleteScoreUser, param = {}", param);
        return userFeignClient.deleteScoreUser(param);
    }

    @PostMapping(value = "/addScoreUser")
    public Mono<BaseResponse> addScoreUser( ServerHttpRequest request,
        @RequestBody ScoreUserAddParam param) {
        log.info("addScoreUser, param = {} ", param);
        param.setTopCommId(AntRestUtils.getTopCommId(request));
        IotAssert.isNotNull(param.getTopCommId(),"未登录状态");
        return userFeignClient.addScoreUser(param);
    }
}