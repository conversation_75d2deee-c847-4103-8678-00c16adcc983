package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.auth.sys.vo.SysRoleVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * IAuthCenterServiceImpl
 *
 * @since 2019/7/29 15:19
 * <AUTHOR>
 */
@Slf4j
@Service
public class AuthCenterService //implements IAuthCenterService
{

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    /**
     * 根据userId查询用户权限信息
     *
     * @param userId
     */

    public List<SysRoleVo> findRolesByUserId(Long userId, String token) {
        log.info("根据userId查询用户权限信息的请求参数----------{}---------{}", userId, token);

        List<SysRoleVo> jsonObject = authCenterFeignClient.findByUserId(userId, token);
        log.info("根据userId查询用户权限信息的接口数据返回-------------------{}", jsonObject);
        return jsonObject;
    }

    /**
     * 判断是否拥有roleName角色
     *
     * @param sysRoleList
     * @param roleName
     * @return
     */

    public Boolean checkRole(List<SysRoleVo> sysRoleList, String roleName) {
        if (StringUtils.isBlank(roleName) || sysRoleList == null) {
            throw new DcServiceException("系统异常");
        }

        for (SysRoleVo sysRole : sysRoleList) {
            if (roleName.equals(sysRole.getName())) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断是否拥有充值角色
     *
     * @param sysRoleList
     * @return
     */

    public Boolean checkRechargeButtonRole(List<SysRoleVo> sysRoleList) {

        if (sysRoleList == null) {
            throw new DcServiceException("系统异常");
        }

        for (SysRoleVo sysRole : sysRoleList) {
            if ("充值管理员".equals(sysRole.getName())) {
                return false;
            }
        }
        return true;
    }
}
