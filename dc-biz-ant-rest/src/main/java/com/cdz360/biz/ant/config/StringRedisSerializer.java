package com.cdz360.biz.ant.config;

import com.cdz360.base.utils.JsonUtils;
import java.nio.charset.Charset;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.util.Assert;

/**
 * 用来序列化key
 *
 * <AUTHOR>
 * @since Create on 2018/6/16 18:07
 */
public class StringRedisSerializer implements RedisSerializer<Object> {

    private final Charset charset;

    public StringRedisSerializer() {
        this(Charset.forName("UTF-8"));
    }

    public StringRedisSerializer(Charset charset) {
        Assert.notNull(charset, "Charset must not be null!");
        this.charset = charset;
    }

    @Override
    public String deserialize(byte[] bytes) {
        return (bytes == null ? null : new String(bytes, charset));
    }

    @Override
    public byte[] serialize(Object object) {
        String string = JsonUtils.toJsonString(object);
//            String string = JsonUtils.toJsonString(object);
        String target = "\"";
        String replacement = "";
        string = string.replace(target, replacement);
        return string.getBytes(charset);
    }
}