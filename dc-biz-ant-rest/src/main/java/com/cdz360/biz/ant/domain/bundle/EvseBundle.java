package com.cdz360.biz.ant.domain.bundle;

import com.cdz360.biz.model.upgradepg.type.BundleType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.ZoneId;
import java.util.Date;
import java.util.TimeZone;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.time.DateFormatUtils;


/**
 * EvseBundle
 *  桩升级包信息
 * <EMAIL>
 * <AUTHOR>
 * @since  2019/9/18 13:13
 */
@Schema(description = "com.chargerlink.device.business.entity.result.EvseBundle")
@Data
@Accessors(chain = true)
public class EvseBundle implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    @Schema(description = "升级包类型")
    private BundleType type;

    @Schema(description = "状态（0已停用；1正常；）")
    private Integer status;

    @Schema(description = "升级包大小")
    private Long bundleSize;

    /**
     * 升级包唯一编号
     */
    @Schema(description = "升级包唯一编号")
    private String version;
    /**
     * 上传升级压缩包的文件名
     */
    @Schema(description = "上传升级压缩包的文件名")
    private String fileName;
    /**
     * 升级要点
     */
    @Schema(description = "升级要点")
    private String releaseNote;
    /**
     * 操作人ID
     */
    @Schema(description = "操作人ID")
    private Long opId;
    /**
     * 操作人姓名
     */
    @Schema(description = "操作人姓名")
    private String opName;
    /**
     * 自描述文件版本
     */
    @Schema(description = "自描述文件版本")
    private Integer protocol;
    /**
     * 自描述文件内容
     */
    @Schema(description = "自描述文件内容")
    private String context;
    /**
     * 新增时间
     */
    @Schema(description = "新增时间")
    private Date createTime;
    /**
     * 最后更新时间
     */
    @Schema(description = "最后更新时间")
    private Date updateTime;
    /**
     * 是否有效
     */
    @Schema(description = "是否有效")
    private Boolean enable;

    /**
     * 上传进度
     */
    @Schema(description = "上传进度 上传中:0-100 上传失败:-1")
    private Integer progress;


    public String getCreateTimeDisplay() {
        return DateFormatUtils.format(createTime, "yyyy-MM-dd HH:mm:ss",
            TimeZone.getTimeZone(ZoneId.of("GMT+8")));
    }

    public String getUpdateTimeDisplay() {
        return DateFormatUtils.format(updateTime, "yyyy-MM-dd HH:mm:ss",
            TimeZone.getTimeZone(ZoneId.of("GMT+8")));
    }
}