package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.feign.reactor.ReactorAuthCenterFeignClient;
import com.cdz360.biz.ant.service.DictService;
import com.cdz360.biz.ant.utils.OSSClientUtil;
import com.cdz360.biz.model.geo.po.CityPo;
import com.cdz360.biz.model.geo.po.ProvincePo;
import com.cdz360.biz.model.geo.vo.ProvinceTreeVo;
import com.cdz360.biz.model.sys.vo.SystemVo;
import com.chargerlinkcar.framework.common.domain.vo.PictureConfig;
import com.chargerlinkcar.framework.common.feign.GeoFeignClient;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import java.util.UUID;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Mono;

/**
 * 商户端系统配置
 *
 * <AUTHOR>
 * @since Created on 20:21 2019/2/25.
 */
@Slf4j
@RestController
@RequestMapping("/api/sys/")
public class SystemRest extends BaseController {

    @Autowired
    private OSSClientUtil ossClient;

    @Autowired
    private GeoFeignClient geoFeignClient;

    @Autowired
    private DictService iDictService;

    @Autowired
    private ReactorAuthCenterFeignClient reactorAuthCenterFeignClient;

    /**
     * 图片上传
     *
     * @return
     */
    @RequestMapping(value = "/upload", method = RequestMethod.POST)
    public ObjectResponse uploadFile(MultipartFile file) throws Exception {
        //获取文件名称
        String originalFilename = file.getOriginalFilename();
        log.info("图片名-------------{}", originalFilename);
        String substring = originalFilename.substring(originalFilename.lastIndexOf(".")).toLowerCase();
        log.info("图片截取后-----------{}", substring);
        String fileName = UUID.randomUUID().toString() + substring;
        log.info("图片名称{}", fileName);
        //图片上传
        Boolean aBoolean = ossClient.uploadFile(file.getInputStream(), fileName);
        if (!aBoolean) {
            return new ObjectResponse<>("图片上传失败");
        }
        String imgUrl = ossClient.getImgUrl(fileName);
        String url = imgUrl + fileName;
        return new ObjectResponse<>(url);
    }


    /**
     * 获取图片配置
     *
     * @return
     */
    @RequestMapping(value = "/getPictureConfig", method = RequestMethod.GET)
    public ObjectResponse<PictureConfig> getPictureConfig() {
        return iDictService.getPictureConfig();
    }


    @GetMapping("/getGeoTree")
    @Operation(summary = "获取树形结构的省/市/区数据", description = "注: character为拼音首字母, 生成文档的类型有误")
    public ListResponse<ProvinceTreeVo> getGeoTree(ServerHttpRequest request) {
        // TODO: 要做缓存
        return this.geoFeignClient.getGeoTree();
    }

    @GetMapping("/geo/getProvinceList")
    @Operation(summary = "获取省数据", description = "注: character为拼音首字母, 生成文档的类型有误")
    public ListResponse<ProvincePo> getProvinceList(ServerHttpRequest request) {
        // TODO: 要做缓存
        return this.geoFeignClient.listProvince();
    }

    @GetMapping("/geo/getCityList")
    @Operation(summary = "获取省数据", description = "注: character为拼音首字母, 生成文档的类型有误")
    public ListResponse<CityPo> getCityList(ServerHttpRequest request) {
        // TODO: 要做缓存
        return this.geoFeignClient.listCity();
    }

    @Operation(summary = "获取系统下的菜单列表")
    @GetMapping("/sysMenu")
    public Mono<ObjectResponse<SystemVo>> sysMenuBySysId(
        ServerHttpRequest request, @Valid @RequestParam(value = "sysId") Long sysId) {
        log.info("获取系统下的菜单列表: {}", sysId);
        String token =getToken2(request);
        return reactorAuthCenterFeignClient.sysMenuBySysId(token, sysId, null)
            .doOnNext(FeignResponseValidate::check)
            .map(x -> {
                List<SystemVo> data = x.getData();
                return RestUtils.buildObjectResponse(data.get(0));
            });
    }
}
