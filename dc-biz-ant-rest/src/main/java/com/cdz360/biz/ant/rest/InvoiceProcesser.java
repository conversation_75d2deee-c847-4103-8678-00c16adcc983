package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.base.model.base.type.UserType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.service.InvoiceService;
import com.cdz360.biz.model.invoice.param.InvoiceContentParam;
import com.cdz360.biz.model.invoice.param.InvoiceRecordParam;
import com.cdz360.biz.model.invoice.type.ProductType;
import com.cdz360.biz.model.invoice.vo.InvoiceFeesVo;
import com.cdz360.biz.model.oa.constant.OaConstants;
import com.cdz360.biz.model.oa.param.BillingParam;
import com.cdz360.biz.model.oa.param.PrepaidInvoicingParam;
import com.cdz360.biz.model.trading.invoice.dto.CorpInvoiceRecordDto;
import com.cdz360.biz.model.trading.invoice.vo.CorpInvoiceReturnPlanVo;
import com.cdz360.biz.model.trading.invoice.vo.CorpInvoicingContentVo;
import com.cdz360.biz.model.trading.peek.invoice.dto.PeekInvoiceDto;
import com.chargerlinkcar.framework.common.domain.CommercialSample;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordUpdateParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceRecordDetail;
import com.chargerlinkcar.framework.common.domain.peek.invoice.param.PeekInvoiceParam;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.InvoiceUtils;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
public class InvoiceProcesser extends BaseController {

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;


    public Mono<CorpInvoiceRecordDetail> corpAppendOrderByOa(ServerHttpRequest request,
        @NonNull String processKey,
        @Nullable String applyNo,
        @Nullable String procInstId,
        Long corpId,
        @Nullable List<String> appendBillNoList) {
        return this.corpAppendOrderByOa(request, processKey, applyNo, procInstId, corpId,
            null,
            appendBillNoList);
    }

    /**
     * OA流程模拟企业客户开票追加订单
     */
    public Mono<CorpInvoiceRecordDetail> corpAppendOrderByOa(ServerHttpRequest request,
        @NonNull String processKey,
        @Nullable String applyNo,
        @Nullable String procInstId,
        Long corpId,
        @Nullable String interimCode,
        @Nullable List<String> appendBillNoList) {

        CorpInvoiceRecordUpdateParam tempReq = new CorpInvoiceRecordUpdateParam();
        tempReq.setCorpId(corpId)
            .setApplyNo(applyNo)
            .setProcInstId(procInstId)
            .setProcDefKey(processKey)
            .setOpAll(Boolean.FALSE)
            .setInterimCode(interimCode)
            .setOrderNoList(appendBillNoList);

        return Mono.just(this.corpInvoiceAppendOrderProcess(request, tempReq))
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData);
    }

    /**
     * 1、用于管理平台页面调用 2、用于企客对账开票流程调用
     */
    public ObjectResponse<CorpInvoiceRecordDetail> corpInvoiceAppendOrderProcess(
        ServerHttpRequest request, CorpInvoiceRecordUpdateParam param) {
        log.info("企业客户开票追加订单(充值/充电/账单): {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        // 获取登录用户相关信息
        CommercialSample comm = this.getCommercialSample2(request);
        if (ObjectUtils.isEmpty(comm)) {
            throw new DcArgumentException("当前操作用户信息不存在，请重新登录");
        }

        Long opUid = comm.getId();
        if (opUid == null || opUid <= 0) {
            throw new DcArgumentException("当前操作用户Id不存在");
        }

        // 操作人信息
        param.setCommIdChain(comm.getCommIdChain());
        param.setOpId(opUid);
        param.setOpName(comm.getUsername());
        param.setOpType(UserType.SYS_USER);

        return invoiceService.corpInvoiceAppendOrder(param);
    }

    /**
     * 预计算，客户开票追加订单(充值/充电/账单)
     */
    public ObjectResponse<PeekInvoiceDto> peekInvoice(
        ServerHttpRequest request, PeekInvoiceParam param) {
        log.info("预计算，客户开票追加订单(充值/充电/账单): {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        // 获取登录用户相关信息
        CommercialSample comm = this.getCommercialSample2(request);
        if (ObjectUtils.isEmpty(comm)) {
            throw new DcArgumentException("当前操作用户信息不存在，请重新登录");
        }

        Long opUid = comm.getId();
        if (opUid == null || opUid <= 0) {
            throw new DcArgumentException("当前操作用户Id不存在");
        }

        // 操作人信息
        param.setCommIdChain(comm.getCommIdChain());
        param.setOpId(opUid);
        param.setOpName(comm.getUsername());
        param.setOpType(UserType.SYS_USER);

        return dataCoreFeignClient.peekInvoice(param);
    }

    public Mono<CorpInvoiceRecordDetail> corpRemoveOrder(ServerHttpRequest request,
        @NonNull String processKey,
        Long corpId,
        @NonNull String applyNo,
        @Nullable List<String> removeBillNoList) {
        return this.corpRemoveOrder(request, processKey, corpId, applyNo, false, removeBillNoList);
    }

    /**
     * 模拟企业客户开票移除订单
     */
    public Mono<CorpInvoiceRecordDetail> corpRemoveOrder(ServerHttpRequest request,
        @NonNull String processKey,
        Long corpId,
        @NonNull String applyNo,
        @Nullable Boolean opAll,
        @Nullable List<String> removeBillNoList) {

        CorpInvoiceRecordUpdateParam tempReq = new CorpInvoiceRecordUpdateParam();
        tempReq.setCorpId(corpId)
            .setApplyNo(applyNo)
            .setProcDefKey(processKey)
            .setOpAll(opAll != null ? opAll : Boolean.FALSE)
            .setOrderNoList(removeBillNoList);

        return Mono.just(this.corpInvoiceRemoveOrderProcess(request, tempReq))
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData);
    }

    /**
     * 1、用于管理平台页面调用 2、用于企客对账开票流程调用
     */
    public ObjectResponse<CorpInvoiceRecordDetail> corpInvoiceRemoveOrderProcess(
        ServerHttpRequest request, CorpInvoiceRecordUpdateParam param) {
        log.info("企业客户开票移除订单(充值/充电/账单): {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));

        // 获取登录用户相关信息
        CommercialSample comm = this.getCommercialSample2(request);
        if (ObjectUtils.isEmpty(comm)) {
            throw new DcArgumentException("当前操作用户信息不存在，请重新登录");
        }

        Long opUid = comm.getId();
        if (opUid == null || opUid <= 0) {
            throw new DcArgumentException("当前操作用户Id不存在");
        }

        // 操作人信息
        param.setCommIdChain(comm.getCommIdChain());
        param.setOpId(opUid);
        param.setOpName(comm.getUsername());
        param.setOpType(UserType.SYS_USER);

        return invoiceService.corpInvoiceRemoveOrder(param);
    }

    /**
     * 对账盖章开票流程模拟企业客户开票记录提交到审核
     */
    public Mono<ObjectResponse<Integer>> invoiceSubmit2Audit(ServerHttpRequest request,
        @NonNull CorpInvoiceRecordDetail recordDetail,
        @NonNull BillingParam param) {

        CorpInvoiceRecordDto dto = new CorpInvoiceRecordDto();
        dto.setCorpId(param.getCorpId())
            .setInvoiceWay(InvoicingMode.POST_SETTLEMENT)
            .setBillingProcessRequest(Boolean.TRUE)
            .setApplyNo(recordDetail.getApplyNo())
            .setTempSalId(recordDetail.getTempSalId())
            .setProductTempId(recordDetail.getProductTempId())
            .setActualElecFee(recordDetail.getActualElecFee())
            .setActualServFee(recordDetail.getActualServFee());

        AtomicReference<BigDecimal> fixElecFeeRef = new AtomicReference<>(BigDecimal.ZERO);
        AtomicReference<BigDecimal> fixServFeeRef = new AtomicReference<>(BigDecimal.ZERO);
        List<CorpInvoicingContentVo> invoicingContentVoList = new ArrayList<>();
        for (InvoiceRecordParam invoiceRecord : param.getInvoiceRecords()) {  // 这一层是发票 invoice_record
            for (InvoiceContentParam item : invoiceRecord.getContents()) {                  // 这一层是商品行明细 invoice_record_content
//        param.getInvoiceList().forEach(item -> {
                if (ProductType.ELEC_ACTUAL_FEE.equals(item.getProductType())) {
                    fixElecFeeRef.set(fixElecFeeRef.get().add(item.getFixAmount()));
                    if (dto.getElecNum() == null) {
                        dto.setElecNum(BigDecimal.ZERO);
                    }
                    dto.setElecNum(dto.getElecNum().add(item.getNum()));
                } else if (ProductType.SERV_ACTUAL_FEE.equals(item.getProductType())) {
                    fixServFeeRef.set(fixServFeeRef.get().add(item.getFixAmount()));
                    if (dto.getServNum() == null) {
                        dto.setServNum(BigDecimal.ZERO);
                    }
                    dto.setServNum(dto.getServNum().add(item.getNum()));
                }

                CorpInvoicingContentVo invoicingContent = new CorpInvoicingContentVo();
//                if (StringUtils.isNotBlank(item.getName())) {
//                    invoicingContent.setProductName(item.getName());
//                } else {
                invoicingContent.setProductName(item.getProductName());
//                }
                invoicingContent.setProductType(item.getProductType())
                    .setTaxRate(item.getTaxRate())
                    .setFixAmount(item.getFixAmount())
                    .setNum(item.getNum())
                    .setSpec(item.getSpec())
                    .setUnit(item.getUnit())
                    .setPrice(item.getPrice());
                invoicingContentVoList.add(invoicingContent);
            }
        }
        dto.setFixElecFee(fixElecFeeRef.get());
        dto.setFixServFee(fixServFeeRef.get());
        dto.setFixTotalFee(fixElecFeeRef.get().add(fixServFeeRef.get()));
        dto.setInvoicingContent(invoicingContentVoList);

        dto.setInvoicingRemark(param.getInvoiceRemark());

        if (Boolean.TRUE.equals(param.getReturnFlag())) {
            dto.setReturnFlag(1);
        } else if (Boolean.FALSE.equals(param.getReturnFlag())) {
            dto.setReturnFlag(0);
            dto.setReturnPlanVoList(param.getReturnPlanVoList().stream().map(t -> {
                CorpInvoiceReturnPlanVo vo = new CorpInvoiceReturnPlanVo();
                switch (t.getIndex()) {
                    case 1:
                        vo.setPlanTitle("第一次");
                        break;
                    case 2:
                        vo.setPlanTitle("第二次");
                        break;
                    case 3:
                        vo.setPlanTitle("第三次");
                        break;
                }
                vo.setPlanMoney(t.getAmount());
                vo.setPlanTime(t.getTime());
                return vo;
            }).collect(Collectors.toList()));
        }

        return Mono.just(this.corpInvoiceRecordSubmit2Audit(request, dto))
            .doOnNext(FeignResponseValidate::check);
    }

    /**
     * 预付订单开票流程模拟企业客户开票记录提交到审核
     */
    public Mono<ObjectResponse<Integer>> invoiceSubmit2Audit(ServerHttpRequest request,
        @NonNull CorpInvoiceRecordDetail recordDetail,
        @NonNull PrepaidInvoicingParam param) {

        CorpInvoiceRecordDto dto = new CorpInvoiceRecordDto();
        dto.setCorpId(param.getCorpId())
            .setInvoiceWay(InvoicingMode.POST_CHARGER);

        dto.setApplyNo(recordDetail.getApplyNo())
            .setProcDefKey(OaConstants.PD_KEY_PREPAID_ORDER_INVOICING);
        if (recordDetail.getTempSalId() != null) {
            dto.setTempSalId(recordDetail.getTempSalId());
        }
        if (recordDetail.getProductTempId() != null) {
            dto.setProductTempId(recordDetail.getProductTempId());
        }
        if (recordDetail.getActualElecFee() != null) {
            dto.setActualElecFee(recordDetail.getActualElecFee());
        }
        if (recordDetail.getActualServFee() != null) {
            dto.setActualServFee(recordDetail.getActualServFee());
        }

        InvoiceRecordParam invRec = param.getInvoiceRecords().get(0);
        InvoiceFeesVo fees = InvoiceUtils.calculateFees(invRec);
        dto.setElecNum(fees.getElecNum())
            .setFixElecFee(fees.getElecFee())
            .setServNum(fees.getServNum())
            .setFixServFee(fees.getServFee())
            .setFixTechServFee(fees.getParkFee())
            .setFixTotalFee(fees.getTotalFee());
//        AtomicReference<BigDecimal> fixElecFeeRef = new AtomicReference<>(BigDecimal.ZERO);
//        AtomicReference<BigDecimal> fixServFeeRef = new AtomicReference<>(BigDecimal.ZERO);
        List<CorpInvoicingContentVo> invoicingContentVoList = new ArrayList<>();
        invRec.getContents().forEach(item -> {
//            if (ProductType.ELEC_ACTUAL_FEE.equals(item.getProductType())) {
//                fixElecFeeRef.set(item.getFixAmount());
//                dto.setElecNum(item.getNum());
//            } else if (ProductType.SERV_ACTUAL_FEE.equals(item.getProductType())) {
//                fixServFeeRef.set(item.getFixAmount());
//                dto.setServNum(item.getNum());
//            } else if (ProductType.TECH_SERV_FEE.equals(item.getProductType())) {
//                dto.setFixTechServFee(item.getFixAmount());
//            }

            CorpInvoicingContentVo invoicingContent = new CorpInvoicingContentVo();
            invoicingContent.setProductName(item.getProductName())
                .setProductType(item.getProductType())
                .setCode(item.getCode())
                .setTaxRate(item.getTaxRate())
                .setFixAmount(item.getFixAmount())
                .setNum(item.getNum())
                .setPrice(item.getPrice())
                .setSpec(item.getSpec())
                .setUnit(item.getUnit());
            invoicingContentVoList.add(invoicingContent);
        });
//        dto.setFixElecFee(fixElecFeeRef.get());
//        dto.setFixServFee(fixServFeeRef.get());
//        dto.setFixTotalFee(fixElecFeeRef.get().add(fixServFeeRef.get()));
        dto.setInvoicingContent(invoicingContentVoList);

        dto.setInvoicingRemark(invRec.getRemark());

        dto.setActualData(param.getActualData());

        return Mono.just(this.corpInvoiceRecordSubmit2Audit(request, dto))
            .doOnNext(FeignResponseValidate::check);
    }

    /**
     * 1、用于管理平台页面调用 2、用于企客对账开票流程调用
     */
    public ObjectResponse<Integer> corpInvoiceRecordSubmit2Audit(
        ServerHttpRequest request, CorpInvoiceRecordDto dto) {
        log.info("企业客户开票记录提交到审核(非财务): {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(dto));
        return invoiceService.corpInvoiceRecordSubmit2Audit(dto);
    }

}
