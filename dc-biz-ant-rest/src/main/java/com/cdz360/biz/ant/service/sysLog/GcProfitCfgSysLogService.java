package com.cdz360.biz.ant.service.sysLog;

import com.cdz360.base.model.base.vo.KvAny;
import com.cdz360.biz.auth.user.type.LogOpType;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;

/**
 * 收益配置相关系统操作日志
 */
@Slf4j
@Service
public class GcProfitCfgSysLogService {

//    @Autowired
//    private CommercialFeignClient commercialFeignClient;

    @Autowired
    private BaseSysLogService sysUserLogService;

    /**
     * 收益配置停用或启用日志
     */
    public void disableOrEnableLog(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY_STATUS,
            List.of(KvAny.of("任务名称", name)),
            request);
    }

    /**
     * 新增收益配置日志
     */
    public void addGcProfitCfgLog(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
            KvAny.of("任务名称", name),
            request);
    }

    /**
     * 删除收益配置日志
     */
    public void delGcProfitCfgLog(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.DISABLE,
            List.of(KvAny.of("任务名称", name)),
            request);
    }

    /**
     * 编辑收益配置日志
     */
    public void editGcProfitCfgLog(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
            List.of(KvAny.of("任务名称", name)),
            request);
    }

}
