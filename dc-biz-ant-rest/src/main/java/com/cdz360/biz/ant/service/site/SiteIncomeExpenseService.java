package com.cdz360.biz.ant.service.site;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.model.site.vo.SiteIncomeExpenseVo;
import com.cdz360.biz.utils.feign.site.BiSiteOrderFeignClient;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class SiteIncomeExpenseService {

    @Autowired
    private BiSiteOrderFeignClient biSiteOrderFeignClient;

    public Mono<ListResponse<SiteIncomeExpenseVo>> siteSixMonthIncomeExpense(
        String siteId, LocalDate month) {
        IotAssert.isNotBlank(siteId, "场站ID不能为空");
        IotAssert.isNotNull(month, "请指定月份信息");
        return biSiteOrderFeignClient.siteSixMonthIncomeExpense(siteId, month.with(
            TemporalAdjusters.firstDayOfMonth()));
    }
}
