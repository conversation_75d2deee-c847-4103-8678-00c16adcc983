package com.cdz360.biz.ant.utils;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.auth.corpWx.JsCode2SessionRes;
import com.cdz360.biz.auth.user.param.SysUserLoginParam.Environment;
import java.util.List;
import java.util.concurrent.TimeUnit;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Repository
public class RedisUtil {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    public void put(String key, String value, long expire) {
        stringRedisTemplate.opsForValue().set(key, value, expire, TimeUnit.SECONDS);
    }

    public String get(String key) {
        return stringRedisTemplate.opsForValue().get(key);
    }

    public String get(String prefix, String key) {
        Assert.notNull(key, "key 不能为空");
        if (StringUtils.isEmpty(prefix)) {
            return get(key);
        }
        return get(String.join(":", prefix, key));
    }

    public void del(String key) {

        stringRedisTemplate.delete(key);
    }

    public void expire(String key, long timeout, TimeUnit timeUnit) {
        stringRedisTemplate.expire(key, timeout, timeUnit);
    }

    /**
     * 入队
     *
     * @param key
     * @param value
     * @return
     */
    public Long rightPushList(String key, String value) {
        return stringRedisTemplate.opsForList().rightPush(key, value);
    }

    /**
     * 出队
     *
     * @param key
     * @return
     */
    public String leftPopList(String key) {
        return stringRedisTemplate.opsForList().leftPop(key);
    }

    /**
     * 范围检索
     *
     * @param key
     * @param start
     * @param end
     * @return
     */
    public List<String> range(String key, int start, int end) {
        return stringRedisTemplate.opsForList().range(key, start, end);
    }

    /**
     * 栈/队列长
     *
     * @param key
     * @return
     */
    public Long length(String key) {
        return stringRedisTemplate.opsForList().size(key);
    }

    public JsCode2SessionRes getWxworkSession(String wxworkUid, String code) {
        String redisKey = wxworkSessionKey(wxworkUid, code);
        val session = this.get(redisKey);
        if (com.cdz360.base.utils.StringUtils.isNotBlank(session)) {
            return JsonUtils.fromJson(session, JsCode2SessionRes.class);
        }
        return null;
    }

    public void saveWxworkSession(JsCode2SessionRes x, String code) {
        String redisKey = wxworkSessionKey(x.getUserId(), code);
        this.put(redisKey, JsonUtils.toJsonString(x), 60 * 2);
    }

    private String wxworkSessionKey(String wxworkUid, String code) {
        return Environment.wxwork.name() + ":" + wxworkUid + ":" + code;
    }


}
