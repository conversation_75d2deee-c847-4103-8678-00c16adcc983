package com.cdz360.biz.ant.rest.iot;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.domain.CfgEvseAllV2;
import com.cdz360.biz.ant.feign.AntUserFeignClient;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.DeviceFeignClient;
import com.cdz360.biz.ant.feign.IotWorkerFeignClient;
import com.cdz360.biz.ant.service.iot.IotEvseCfgService;
import com.cdz360.biz.ant.service.sysLog.SiteSysLogService;
import com.cdz360.biz.model.iot.param.ModifyEvseCfgExParam;
import com.cdz360.biz.model.iot.param.ModifyEvseCfgParam;
import com.cdz360.biz.model.iot.vo.WhiteCardCfgVo;
import com.cdz360.biz.model.iot.vo.WhiteCardV2;
import com.cdz360.biz.model.trading.site.vo.EvsePriceSchemeInfoVo;
import com.chargerlinkcar.framework.common.domain.CommercialSample;
import com.chargerlinkcar.framework.common.domain.param.CardsParam;
import com.chargerlinkcar.framework.common.domain.vo.TemplateInfoVo;
import com.chargerlinkcar.framework.common.feign.IotBizClient;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * EvseCfgRest
 *
 * @since 2019/6/18 9:20
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "获取桩配置接口", description = "桩配置")
public class IotEvseCfgRest extends BaseController {
    @Autowired
    private IotWorkerFeignClient iotWorkerFeignClient;

    @Autowired
    private IotBizClient iotBizClient;
    @Autowired
    private DeviceFeignClient deviceFeignClient;
    @Autowired
    private AntUserFeignClient userFeignClient;

    @Autowired
    private IotEvseCfgService iotEvseCfgService;
    @Autowired
    private SiteSysLogService siteSysLogService;


    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Operation(summary = "桩计费详情")
    @GetMapping(value = "/cfg/evse/getPriceSchemeInfo")
    public ObjectResponse<EvsePriceSchemeInfoVo> getPriceSchemeInfo(
            ServerHttpRequest request,
            @RequestParam(value = "evseNo") String evseNo) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return RestUtils.buildObjectResponse(this.iotEvseCfgService.getPriceSchemeInfo(evseNo));
    }

    // 请使用上面接口: /cfg/evse/getPriceSchemeInfo
    @PostMapping(value = "/cfg/evse/getcfg")
    public BaseResponse cfgEvseGetcfg(@RequestParam(value = "evseIds") List<String> evseIds) {
        return this.iotEvseCfgService.cfgEvseGetcfg(evseIds);
//        return iotWorkerFeignClient.cfgEvseGetcfg(evseIds);
    }

    @Operation(summary = "获取桩上报的计费信息")
    @PostMapping(value = "/cfg/evse/getcfgInfo")
    public ObjectResponse<Map> cfgEvseGetcfgInfo(@RequestParam(value = "evseId") String evseId) {
        ObjectResponse<CfgEvseAllV2> ob = iotWorkerFeignClient.cfgEvseGetcfgInfo(evseId);
        FeignResponseValidate.check(ob);
        log.info("获取的配置信息: ob = {}", ob);

        CfgEvseAllV2 cfgEvseAll = ob.getData();
        Map<String, Object> result = new HashMap<>();
        result.put("CfgEvseAll", cfgEvseAll);
        if (cfgEvseAll == null) {
            return new ObjectResponse<>(result);
        }
        List<WhiteCardV2> whiteCards = cfgEvseAll.getWhiteCards();
        Map<String, WhiteCardCfgVo> whiteMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(whiteCards)) {
            // 有效的紧急卡列表
            whiteCards = whiteCards.stream().filter(card -> card.getCardNumber() != null).collect(Collectors.toList());

            log.info("紧急卡个数: size = {}", whiteCards.size());
            CardsParam cardsParam = new CardsParam();
            List<String> cards = whiteCards.stream().map(WhiteCardV2::getCardNumber).collect(Collectors.toList());
            cardsParam.setCardNos(cards);
            ListResponse<WhiteCardCfgVo> whiteCardCfgVoListResponse = userFeignClient.getWhiteCardCfgVoByCardNos(cardsParam);
            if (null == whiteCardCfgVoListResponse || null == whiteCardCfgVoListResponse.getData()) {
                log.warn("获取紧急卡信息失败");
            } else {
                whiteCardCfgVoListResponse.getData().forEach(e -> whiteMap.put(trimLeftZero(e.getCardNo()), e));

                whiteCards.forEach(e -> {
                    if (whiteMap.get(e.getCardNumber()) == null) {
                        WhiteCardCfgVo wc = new WhiteCardCfgVo();
                        wc.setPassWord(e.getPasscode());
                        whiteMap.put(e.getCardNumber(), wc);
                    } else {
                        whiteMap.get(e.getCardNumber()).setPassWord(e.getPasscode());
                    }
                });
            }
        }

        result.put("whiteCards", whiteMap);

        result.put("whiteVinList", cfgEvseAll.getWhiteVinList());

        IotAssert.isNotNull(cfgEvseAll.getPriceCode(), "计费模板编号不正确，请重新下发计费模板");
        ObjectResponse<TemplateInfoVo> template = deviceFeignClient.getTemplateDetailById(cfgEvseAll.getPriceCode().longValue());
        if (null == template) {
            log.error("获取计费模板信息异常");
            return RestUtils.buildObjectResponse(result);
        }

        TemplateInfoVo templateInfoVo = template.getData();
        if (templateInfoVo == null) {
            return new ObjectResponse<>(result);
        }
        result.put("name", templateInfoVo.getName());
        result.put("template", template.getData());
        return new ObjectResponse<>(result);
    }

    //移除左边的0
    private String trimLeftZero(String str) {
        return str.replaceAll("^(0+)", "");
    }

    @Operation(summary = "单个桩计费模板下发")
    @GetMapping(value = "/cfg/evse/sendPriceSchema")
    public BaseResponse sendPriceSchema(
            ServerHttpRequest request, @RequestParam(value = "evseNo") String evseNo) {
        log.info(LoggerHelper2.formatEnterLog(request));

        String priceSchemaName = this.iotEvseCfgService.sendPriceSchema(evseNo);
        siteSysLogService.sendPriceSchema(priceSchemaName, evseNo, request);
        return RestUtils.success();
    }

    @Operation( summary = "修改/下发桩配置")
    @PostMapping(value = "/api/evse/cfg/modifyEvseCfg")
    public BaseResponse modifyEvseCfg(ServerHttpRequest request,
                                      @RequestBody ModifyEvseCfgParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + ", param = {}", JsonUtils.toJsonString(param));
        Assert.isTrue(CollectionUtils.isNotEmpty(param.getEvseNoList()), "参数错误, 桩编号不能为空");

        // 获取操作人
        CommercialSample comm = this.getCommercialSample2(request);
        if (ObjectUtils.isEmpty(comm)) {
            log.warn("当前操作用户信息不存在，请重新登录");
            throw new DcArgumentException("当前操作用户信息不存在，请重新登录");
        }

        // 是否需要下发计费模板
        log.info("priceSchemeId = {}", param.getPriceSchemeId());
        if (null == param.getPriceSchemeId()) {
            // 单独下发桩配置，不带计费模板信息，不需要取消定时下发
            BaseResponse res = this.iotBizClient.modifyEvseCfgV2(param);
            FeignResponseValidate.check(res);

            // t_bs_box_setting 中预存数据
            this.dataCoreFeignClient.updateEvseSetting(param);

            return res;
        } else {
            this.iotEvseCfgService.downPriceTemplate(param, comm.getId());
            siteSysLogService.downPriceTemplate(param.getName(), param.getEvseNoList(), param.getSiteDefault(), param.getSiteName(), request);
            return RestUtils.success();
        }
    }

    @Operation( summary = "修改桩计费模板-用于不支持计费下发的桩")
    @PostMapping(value = "/api/evse/cfg/modifyEvsePrice")
    public BaseResponse modifyEvsePrice(ServerHttpRequest request,
                                        @RequestBody ModifyEvseCfgParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + ", modifyEvsePrice param = {}", JsonUtils.toJsonString(param));
        Assert.isTrue(CollectionUtils.isNotEmpty(param.getEvseNoList()), "参数错误, 桩编号不能为空");

        this.iotEvseCfgService.modifyEvsePrice(param);
        siteSysLogService.downPriceTemplate(param.getName(), param.getEvseNoList(), param.getSiteDefault(), param.getSiteName(), request);
        return RestUtils.success();
    }

    @Operation( summary = "下发桩单个桩计费模板")
    @PostMapping(value = "/api/evse/cfg/sendEvsePrice")
    public BaseResponse sendEvsePrice(ServerHttpRequest request,
                                      @RequestBody ModifyEvseCfgExParam param) {
        log.info(">> 下发桩单个桩计费模板: {}", JsonUtils.toJsonString(param));
        this.iotEvseCfgService.sendEvsePrice(param);
        return RestUtils.success();
    }
}
