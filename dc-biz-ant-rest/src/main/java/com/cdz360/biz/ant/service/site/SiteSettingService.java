package com.cdz360.biz.ant.service.site;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.ant.feign.reactor.ReactorSiteDataCoreFeignClient;
import com.cdz360.biz.model.trading.site.param.UpdateSiteOaDefaultValueParam;
import com.cdz360.biz.model.trading.site.po.SiteOaDefaultConfigPo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class SiteSettingService {

    @Autowired
    private ReactorSiteDataCoreFeignClient siteDataCoreFeignClient;

    public Mono<SitePo> updateOaDefaultValue(UpdateSiteOaDefaultValueParam param) {
        IotAssert.isNotBlank(param.getSiteId(), "场站ID不能为空");
        if (CollectionUtils.isEmpty(param.getOaDefaultValueList())) {
            throw new DcArgumentException("需要指定流程配置项");
        }

        param.getOaDefaultValueList().forEach(x -> {
            IotAssert.isNotBlank(x.getProcDefKey(), "配置项流程KEY不能为空");
            IotAssert.isNotBlank(x.getProcDefName(), "配置项流程名称不能为空");
        });

        return siteDataCoreFeignClient.updateOaDefaultValue(param)
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData);
    }

    public Mono<ListResponse<SiteOaDefaultConfigPo>> fetchOaDefaultValue(String siteId) {
        IotAssert.isNotBlank(siteId, "场站ID不能为空");
        return siteDataCoreFeignClient.fetchOaDefaultValue(siteId);
    }

    public Mono<ObjectResponse<SiteOaDefaultConfigPo>> getOaDefaultValue(
        String siteId, String procDefKey) {
        IotAssert.isNotBlank(siteId, "场站ID不能为空");
        IotAssert.isNotBlank(procDefKey, "流程定义KEY不能为空");
        return siteDataCoreFeignClient.getOaDefaultValue(siteId, procDefKey);
    }
}
