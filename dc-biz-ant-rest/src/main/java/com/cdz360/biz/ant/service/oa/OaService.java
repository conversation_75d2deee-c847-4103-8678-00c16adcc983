package com.cdz360.biz.ant.service.oa;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.reactor.OaFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorAuthCenterFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorBizBiFeignClient;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.oa.constant.OaConstants;
import com.cdz360.biz.model.oa.vo.OaElecPayIncomeExpenseVo;
import com.cdz360.biz.model.oa.vo.OaStatisticsInfo;
import com.cdz360.biz.model.site.vo.BiSiteExpenseSumByMonthVo;
import com.cdz360.biz.model.site.vo.BiSiteIncomeSumByMonthVo;
import com.cdz360.biz.model.site.vo.SiteIncomeExpenseVo;
import com.cdz360.biz.oa.dto.BatchTaskVo;
import com.cdz360.biz.oa.dto.OaTaskDto;
import com.cdz360.biz.oa.param.ListTaskParam;
import com.cdz360.biz.oa.param.OaProcessTagParam;
import com.cdz360.biz.oa.param.PayElecFeeTaskParam;
import com.cdz360.biz.oa.vo.OaElecPayTaskVo;
import com.cdz360.biz.oa.vo.OaElecPayTaskVo.PeriodVo;
import com.cdz360.biz.oa.vo.OaRechargeInfoVo;
import com.cdz360.biz.oa.vo.OaRechargeInfoVo.BtnType;
import com.cdz360.biz.oa.vo.OaTaskCommentVo;
import com.cdz360.biz.oa.vo.OaTaskVo;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.fasterxml.jackson.databind.JsonNode;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class OaService {

    @Autowired
    private OaFeignClient oaFeignClient;

    @Autowired
    private ReactorAuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private ReactorBizBiFeignClient bizBiFeignClient;

    private Mono<Map<Long, String>> userNameMap(Set<Long> uids) {
        return authCenterFeignClient
            .getSysUserByIdList(new ArrayList<>(uids))
            .doOnNext(FeignResponseValidate::check)
            .map(x -> x.getData().stream()
                .collect(Collectors.toMap(SysUserVo::getId, SysUserVo::getName)));
    }

    private <T extends OaTaskDto> Mono<ListResponse<T>> assigneeUserName(ListResponse<T> result) {
        if (CollectionUtils.isNotEmpty(result.getData())) {
            Set<Long> uids = new HashSet<>();
            for (OaTaskDto dto : result.getData()) {
                if (null != dto.getAssignee()) {
                    uids.add(dto.getAssignee());
                }

                if (null != dto.getOUid()) {
                    uids.add(dto.getOUid());
                }
            }

            return this.userNameMap(uids)
                .map(uNameMap -> {
                    for (OaTaskDto dto : result.getData()) {
                        if (null != dto.getAssignee()) {
                            dto.setAssigneeName(uNameMap.get(dto.getAssignee()));
                        }

                        if (null != dto.getOUid()) {
                            dto.setOName(uNameMap.get(dto.getOUid()));
                        }
                    }
                    return result;
                });
        }
        return Mono.just(result);
    }

    public Mono<ListResponse<OaTaskVo>> getAssigneeApplyTasks(ListTaskParam param) {
        return this.oaFeignClient.getAssigneeApplyTasks(param)
            .doOnNext(FeignResponseValidate::check)
            .flatMap(this::assigneeUserName);
    }

    public Mono<ListResponse<OaTaskVo>> getAssigneeAuditTasks(ListTaskParam param) {
        return authCenterFeignClient.searchUserIdList(
                param.getOUid(), param.getSubmitUserNameLike()) // 获取团队隔离组用户ID
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .map(list -> {
                if (CollectionUtils.isNotEmpty(list.getTeamCatalogUserIdList())) {
                    param.setCreatedByList(
                        list.getTeamCatalogUserIdList().stream()
                            .map(Objects::toString).collect(Collectors.toList()));
                }

                if (CollectionUtils.isNotEmpty(list.getSearchNameUserIdList())) {
                    param.setSearchCreatedByList(
                        list.getSearchNameUserIdList().stream()
                            .map(Objects::toString).collect(Collectors.toList()));
                }

                return param;
            })
            .filter(x -> !(StringUtils.isNotBlank(x.getSubmitUserNameLike()) &&
                CollectionUtils.isEmpty(x.getSearchCreatedByList())))
            .flatMap(this.oaFeignClient::getAssigneeAuditTasks)
            .doOnNext(FeignResponseValidate::check)
            .flatMap(this::assigneeUserName)
            .flatMap(res -> this.initBtnType(res, param.getOUid()))
            .switchIfEmpty(Mono.just(RestUtils.buildListResponse(List.of())));
    }

    public Mono<ListResponse<OaTaskVo>> getAssigneeTasks(ListTaskParam param) {
        return this.oaFeignClient.getAssigneeTasks(param)
            .doOnNext(FeignResponseValidate::check)
            .flatMap(this::assigneeUserName);
    }

    public Mono<ListResponse<OaElecPayTaskVo>> getPayElecFeeTasks(PayElecFeeTaskParam param) {

        ListTaskParam listParam = new ListTaskParam();
        listParam.setTotal(param.getTotal());
        listParam.setStart(param.getStart());
        listParam.setSize(param.getSize());
        listParam.setTopCommId(param.getTopCommId());
        listParam.setOUid(param.getOUid());
        listParam.setProcessInstIdList(param.getProcInstIdList());
        listParam.setAuditStatus(param.getAuditStatus());

        return this.getAssigneeAuditTasks(listParam).doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .filter(CollectionUtils::isNotEmpty)
            .map(e -> {
                return e.stream().map(x -> {
                    OaElecPayTaskVo taskVo = new OaElecPayTaskVo();
                    taskVo.setTaskId(x.getId())
                        .setProcInstId(x.getProcessInstanceId())
                        .setOaKey(x.getOaKey())
                        .setOaName(x.getOaName());

                    JsonNode summaryNode = JsonUtils.fromJson(x.getSummaryJson());
                    Optional.ofNullable(
                            summaryNode.get(OaConstants.OA_ELEC_PAY_SUMMARY_FIELD_SITE_ID))
                        .ifPresent(z -> taskVo.setSiteId(z.asText()));
                    Optional.ofNullable(
                            summaryNode.get(OaConstants.OA_ELEC_PAY_SUMMARY_FIELD_SITE_NAME))
                        .ifPresent(z -> taskVo.setSiteName(z.asText()));
                    Optional.ofNullable(
                            summaryNode.get(OaConstants.OA_ELEC_PAY_SUMMARY_FIELD_BILL_DATE)) // 提取账期日期
                        .ifPresent(z -> {
                            if (z.isArray() && !z.isEmpty()) {
                                String maxBillDateStr = null;
                                String billStartDateStr = null;
                                String billEndDateStr = null;

                                // 提取开始日期（第一个元素，索引0）
                                if (z.size() > 0 && z.get(0) != null && !z.get(0).isNull()) {
                                    billStartDateStr = z.get(0).asText();
                                }

                                // 提取结束日期（第二个元素，索引1）
                                if (z.size() > 1 && z.get(1) != null && !z.get(1).isNull()) {
                                    billEndDateStr = z.get(1).asText();
                                    maxBillDateStr = billEndDateStr;
                                } else if (z.get(0) != null && !z.get(0).isNull()) {
                                    maxBillDateStr = z.get(0).asText();
                                }

                                // 设置账期开始日期
                                if (StringUtils.isNotBlank(billStartDateStr)) {
                                    taskVo.setBillStartDate(billStartDateStr);
                                }

                                // 设置账期结束日期
                                if (StringUtils.isNotBlank(billEndDateStr)) {
                                    taskVo.setBillEndDate(billEndDateStr);
                                }

                                // 确保获取到有效的日期字符串用于设置maxBillDate
                                if (StringUtils.isNotBlank(maxBillDateStr)) {
                                    try {
                                        taskVo.setMaxBillDate(DateUtil.dateToLocalDate(
                                            DateUtil.timeStr2Date(maxBillDateStr)));
                                    } catch (Exception ex) {
                                        log.error("解析billDate失败, maxBillDateStr: {}, error: {}",
                                            maxBillDateStr, ex.getMessage());
                                    }
                                } else {
                                    log.error("解析billDate失败, z: {}", JsonUtils.toJsonString(z));
                                }
                            }
                        });
                    Optional.ofNullable(
                            summaryNode.get(OaConstants.OA_ELEC_PAY_SUMMARY_FIELD_STATISTICS))
                        .ifPresent(z -> taskVo.setStatisticsInfo(
                            JsonUtils.fromJson(z.asText(), OaStatisticsInfo.class)));

                    return taskVo;
                }).collect(Collectors.toList());
            }).flatMap(list -> {
                List<BatchTaskVo> voList = list.stream().map(e -> {
                    BatchTaskVo batchTaskVo = new BatchTaskVo();
                    batchTaskVo.setSiteId(e.getSiteId())
                        .setBillStartDate(e.getBillStartDate())
                        .setBillEndDate(e.getBillEndDate())
                        .setMaxBillDate(e.getMaxBillDate());
                    return batchTaskVo;
                }).collect(Collectors.toList());
                return bizBiFeignClient.getOaElecPayTaskListBySiteId(voList)
                    .doOnNext(FeignResponseValidate::check).map(ListResponse::getData)
                    .map(resData -> {
                        Map<String, List<OaElecPayIncomeExpenseVo>> siteIdMap = resData.stream()
                            .collect(Collectors.groupingBy(OaElecPayIncomeExpenseVo::getSiteId,
                                Collectors.toList()));
                        return list.stream().peek(e -> {
                            List<OaElecPayIncomeExpenseVo> payIncomeExpenseVoList = siteIdMap.get(
                                e.getSiteId());
                            OaElecPayIncomeExpenseVo matchedVo = null;
                            if (CollectionUtils.isNotEmpty(payIncomeExpenseVoList)) {
                                // 判断账期是否相等，找到与maxBillDate年月匹配的数据
                                LocalDate maxBillDate = e.getMaxBillDate();
                                if (maxBillDate != null) {
                                    // 按时间倒序排序，便于后续查找
                                    List<OaElecPayIncomeExpenseVo> sortedList = payIncomeExpenseVoList.stream()
                                        .sorted(Comparator.comparing(
                                            OaElecPayIncomeExpenseVo::getMaxBillDate).reversed())
                                        .toList();

                                    // 查找上期数据：找到第一个月份小于本期月份的数据
                                    for (OaElecPayIncomeExpenseVo item : sortedList) {
                                        boolean boo =
                                            item.getMaxBillDate() != null && item.getMaxBillDate()
                                                .isEqual(maxBillDate);
                                        boo = boo && item.getSiteIncomeExpenseVo() != null
                                            && item.getSiteIncomeExpenseVo().getMonth()
                                            .isBefore(maxBillDate);
                                        if (boo) {
                                            matchedVo = item;
                                            break;
                                        }
                                    }
                                    if (matchedVo != null) {
                                        e.setDefaultShowDiscount(
                                            matchedVo.getDefaultShowDiscount());
                                    } else {
                                        // 如果没有找到匹配的账期数据，记录警告日志
                                        log.warn(
                                            "未找到与账期匹配的收支数据, siteId: {}, maxBillDate: {}",
                                            e.getSiteId(), maxBillDate);
                                    }
                                } else {
                                    log.warn("账期为空, siteId: {}", e.getSiteId());
                                }
                            }

                            this.calcRealData(e, matchedVo);

                        }).collect(Collectors.toList());
                    });
            }).map(RestUtils::buildListResponse)
            .switchIfEmpty(Mono.just(RestUtils.buildListResponse(List.of())));
    }

    private void calcRealData(OaElecPayTaskVo vo, OaElecPayIncomeExpenseVo matchedVo) {

        // 处理本期数据
        if (vo.getStatisticsInfo() != null) {
            PeriodVo currPeriod = new PeriodVo();
            try {
                OaStatisticsInfo statisticsInfo = vo.getStatisticsInfo();

                // 获取platform数据和discount数据
                OaStatisticsInfo.PlatformData platform = statisticsInfo.getPlatform();
                OaStatisticsInfo.DiscountData discount = statisticsInfo.getDiscount();

                if (platform != null && discount != null) {
                    // 从discount获取数据
                    currPeriod.setDiscount(discount.getDiscount());

                    // 计算当期指标，从platform获取总电量
                    BigDecimal totalElec = platform.getTotalElec();
                    if (totalElec != null && !DecimalUtils.isZero(totalElec)) {
                        // 计算电费单价
                        BigDecimal billFee = discount.getBillFee();
                        if (billFee != null) {
                            currPeriod.setElecUnitPrice(
                                billFee.divide(totalElec, 4, RoundingMode.HALF_UP));
                        }

                        // 计算服务费单价
                        BigDecimal actualServFee = discount.getActualServFee();
                        if (actualServFee != null) {
                            currPeriod.setServUnitPrice(
                                actualServFee.divide(totalElec, 4, RoundingMode.HALF_UP));
                        }

                        if (matchedVo != null && matchedVo.getCurrPeriodPower() != null
                            && matchedVo.getCurrPeriodDays() != null) {
                            // 利用率 = 订单电量/功率/当月天数/24
                            BigDecimal temp = matchedVo.getCurrPeriodPower()
                                .multiply(new BigDecimal(matchedVo.getCurrPeriodDays() * 24));
                            currPeriod.setEfficiency(
                                totalElec.divide(temp, 6, RoundingMode.HALF_UP));
                        }
                    } else {
                        log.error(
                            "计算本期数据失败, totalElec值无效. procInstId: {}, totalElec: {}",
                            vo.getProcInstId(), totalElec);
                    }
                } else {
                    log.error(
                        "计算本期数据失败, statisticsInfo无效. procInstId: {}, statisticsInfo: {}",
                        vo.getProcInstId(), JsonUtils.toJsonString(statisticsInfo));
                }
            } catch (Exception e) {
                log.error("计算本期数据失败, procInstId: {}, error: {}", vo.getProcInstId(),
                    e.getMessage());
            }

            vo.setCurrPeriod(currPeriod);
        }

        // 处理上期数据
        if (matchedVo != null && matchedVo.getSiteIncomeExpenseVo() != null) {

            PeriodVo lastPeriod = new PeriodVo();
            try {
                SiteIncomeExpenseVo lastMonth = matchedVo.getSiteIncomeExpenseVo();
                // 设置上期效率
                lastPeriod.setEfficiency(lastMonth.getEfficiency());

                // 如果有收入数据，计算上期电费和服务费单价
                if (lastMonth.getIncome() != null && lastMonth.getExpense() != null) {
                    BiSiteIncomeSumByMonthVo income = lastMonth.getIncome();
                    BiSiteExpenseSumByMonthVo expense = lastMonth.getExpense();

                    BigDecimal number = expense.getNumber(); // 电费账单电量
                    BigDecimal elec = income.getElec();// 订单电量
                    lastPeriod.setDiscount(
                        DecimalUtils.gtZero(number) ? ((number.subtract(elec)).divide(number, 4,
                            RoundingMode.HALF_UP)) : BigDecimal.ZERO); // 电损计算

                    BigDecimal fee = expense.getElecFee(); // 实缴电费
                    BigDecimal incomeSum = DecimalUtils.add(income.getElecFee(),
                        income.getServFee());
                    BigDecimal elecFee = income.getElecFee(); // 平台电费(万元)
                    BigDecimal servFee = (incomeSum.subtract(
                        Optional.ofNullable(fee).orElse(elecFee))); // 服务费
                    lastPeriod.setElecUnitPrice(
                        fee.divide(elec, 4, RoundingMode.HALF_UP)); // 电费单价=实缴电费/订单电费
                    lastPeriod.setServUnitPrice(
                        servFee.divide(elec, 4, RoundingMode.HALF_UP)); // 服务费单价=服务费/订单电量
                } else {
                    log.error("计算上期数据失败, lastMonth无效. procInstId: {}, lastMonth: {}",
                        vo.getProcInstId(), JsonUtils.toJsonString(lastMonth));
                }
            } catch (Exception e) {
                log.error("计算上期数据失败, siteId: {}, error: {}", vo.getSiteId(), e.getMessage(),
                    e);
            }
            vo.setLastPeriod(lastPeriod);
        } else {
            log.error("计算上期数据失败, matchedVo无效 procInstId: {}, matchedVo: {}",
                vo.getProcInstId(), JsonUtils.toJsonString(matchedVo));
        }
    }

    public Mono<OaRechargeInfoVo> processDetail(final String procInstId, final Long sysUid) {
        return this.oaFeignClient.processDetail(procInstId, sysUid.toString())
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .flatMap(x -> {
                Set<Long> uids = new HashSet<>();
                if (StringUtils.isBlank(x.getAssigneeName()) && StringUtils.isNotBlank(
                    x.getAssignee())) {
                    uids.add(Long.parseLong(x.getAssignee()));
                }
                if (StringUtils.isBlank(x.getOName()) && StringUtils.isNotBlank(x.getOUid())) {
                    uids.add(Long.parseLong(x.getOUid()));
                }
                if (CollectionUtils.isNotEmpty(x.getCommentList())) {
                    uids.addAll(x.getCommentList().stream()
                        .filter(k -> StringUtils.isBlank(k.getOName()) && StringUtils.isNotBlank(
                            k.getOUid()))
                        .map(OaTaskCommentVo::getOUid)
                        .map(Long::parseLong)
                        .collect(Collectors.toSet()));
                }

                return this.userNameMap(uids)
                    .flatMap(uNameMap -> {
                        // 分配用户
                        if (StringUtils.isBlank(x.getAssigneeName()) &&
                            StringUtils.isNotBlank(x.getAssignee())) {
                            x.setAssigneeName(uNameMap.get(Long.parseLong(x.getAssignee())));
                        }
                        if (StringUtils.isBlank(x.getOName()) &&
                            StringUtils.isNotBlank(x.getOUid())) {
                            x.setOName(uNameMap.get(Long.parseLong(x.getOUid())));
                        }
                        if (CollectionUtils.isNotEmpty(x.getCommentList())) {
                            for (OaTaskCommentVo vo : x.getCommentList()) {
                                if (StringUtils.isBlank(vo.getOName()) &&
                                    StringUtils.isNotBlank(vo.getOUid())) {
                                    vo.setOName(uNameMap.get(Long.parseLong(vo.getOUid())));
                                }
                            }
                        }

                        return this.initBtnType(x, sysUid);
                    });
            });
    }

    private Mono<ListResponse<OaTaskVo>> initBtnType(ListResponse<OaTaskVo> result, Long sysUid) {
        if (CollectionUtils.isNotEmpty(result.getData())) {
            result.getData().forEach(x -> {
                x.setCanAudit(sysUid.equals(x.getAssignee()));
                if (null == x.getEndTime() &&
                    (null == x.getAssignee() || x.getAssignee() == 0) &&
                    StringUtils.isNotBlank(x.getGroupType()) && x.getInGroupMember()) {
                    if ("1".equals(x.getGroupType())) { // 普通组
                        x.setCanAudit(true);
                    } else if ("2".equals(x.getGroupType())) { // 团队隔离组
                        // 判断两个用户团队标签是否一致
                        authCenterFeignClient.sameTeamCatalog(x.getOUid(), sysUid)
                            .doOnNext(FeignResponseValidate::check)
                            .map(ObjectResponse::getData)
                            .doOnNext(x::setCanAudit)
                            .block(Duration.ofSeconds(50L));
                    }
                }
            });
        }
        return Mono.just(result);
    }

    private Mono<OaRechargeInfoVo> initBtnType(OaRechargeInfoVo result, Long sysUid) {
        return Mono.just(sysUid)
            .flatMap(x -> {
                // 1. 已经结束的流程
                if (null != result.getEndTime()) {
                    result.setBtnType(OaRechargeInfoVo.BtnType.COMMENT);
                } else {
                    if (StringUtils.isNotBlank(result.getAssignee())) { // 已分配处理人节点
                        result.setBtnType(sysUid.toString().equals(result.getAssignee()) ?
                            OaRechargeInfoVo.BtnType.AUDIT : OaRechargeInfoVo.BtnType.COMMENT);
                    } else {
                        // 组类类型不存在，当前用户不属于组内成员
                        if (StringUtils.isBlank(result.getGroupType())
                            || (null != result.getInHistoryLink() && !result.getInHistoryLink())) {
                            result.setBtnType(BtnType.OTHER);
                        } else {
                            if ("1".equals(result.getGroupType()) &&
                                Boolean.TRUE.equals(result.getInGroupMember())) { // 普通组
                                result.setBtnType(OaRechargeInfoVo.BtnType.AUDIT);
                            } else if ("10".equals(result.getGroupType()) &&
                                Boolean.TRUE.equals(result.getInGroupMember())) { // 团队隔离组
                                // 判断两个用户团队标签是否一致
                                return authCenterFeignClient.sameTeamCatalog(
                                        Long.parseLong(result.getOUid()), sysUid)
                                    .doOnNext(FeignResponseValidate::check)
                                    .map(ObjectResponse::getData)
                                    .map(res -> result.setBtnType(res ?
                                        OaRechargeInfoVo.BtnType.AUDIT
                                        : OaRechargeInfoVo.BtnType.COMMENT));
                            } else { // FIXME: 团队隔离组会存在问题
                                result.setBtnType(OaRechargeInfoVo.BtnType.COMMENT);
                            }
                        }
                    }
                }
                return Mono.just(result);
            });
    }

    public Mono<BaseResponse> saveTag(OaProcessTagParam param) {
        return this.oaFeignClient.saveTag(param)
            .doOnNext(FeignResponseValidate::check);
    }

    public Mono<BaseResponse> batchSaveTag(OaProcessTagParam param) {
        return this.oaFeignClient.batchSaveTag(param)
            .doOnNext(FeignResponseValidate::check);
    }

}
