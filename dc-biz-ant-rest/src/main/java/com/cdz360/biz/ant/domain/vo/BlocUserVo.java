package com.cdz360.biz.ant.domain.vo;

import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.model.corp.type.CorpType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 集团功能
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "集团账户")
@ToString(callSuper = true)
@EqualsAndHashCode
public class BlocUserVo implements Serializable {

    @Schema(description = "集团账户Id")
    protected Long id;

    @Schema(description = "状态")
    protected Boolean enable;

    /**
     * 集团客户名称
     */
    @Schema(description = "集团客户名称")
    protected String blocUserName;

    /**
     * 联系人名称
     */
    @Schema(description = "联系人名称")
    protected String contactName;

    /**
     * 手机号
     */
    @Schema(description = "手机号")
    protected String phone;

    @Schema(description = "企业类型")
    protected CorpType type;

    /**
     * 省
     */
    @Schema(description = "省")
    protected Integer province;

    /**
     * 市
     */
    @Schema(description = "市")
    protected Integer city;

    /**
     * 区
     */
    @Schema(description = "区")
    protected Integer district;

    /**
     * 地址
     */
    @Schema(description = "地址")
    protected String address;

    /**
     * 余额
     */
    @Schema(description = "余额")
    protected String blocBalance;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "基础账户可用余额")
    private BigDecimal availableAmount;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "冻结金额")
    private BigDecimal frozenAmount;
    /**
     * 账号
     */
    @Schema(description = "账号")
    protected String account;
    /**
     * 密码
     */
    @Schema(description = "密码")
    protected String password;
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    protected String updateTime;
    @Schema(description = "所属顶级商户id")
    protected Long topCommId;
    /**
     * 所属商户id
     */
    @Schema(description = "所属商户id")
    protected Long commId;
    @Schema(description = "所属商户")
    protected String commName;
    @Schema(description = "用户Id")
    private Long uid;
    @Schema(description = "商户及子商户id")
    private List<Long> comIds;

    @Schema(description = "结算类型: UNKNOWN(0)-未知,BALANCE(1)-账户余额扣减(先付费),GUARANTEE(2)-担保消费结算,"
        +
        "POSTPAID(3)-预消费结算(后付费),PARTNER(4)-外部平台结算", format = "java.lang.Integer")
    private SettlementType settlementType;

    @Schema(description = "企业客户开票方式: 充值预开票(PRE_PAY)、充电后开票(POST_CHARGER)、后付费账单开票(POST_SETTLEMENT)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InvoicingMode invoiceWay;

    @Schema(description = "企业客户是否使用协议价")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean useDiscount;

    @Schema(description = "引流人")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String referrer;

}
