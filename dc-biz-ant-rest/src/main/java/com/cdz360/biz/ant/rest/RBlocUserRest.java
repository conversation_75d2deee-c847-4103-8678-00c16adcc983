package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.aop.CheckToken;
import com.cdz360.biz.ant.service.RBlocUserService;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUserVo;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * 集团客户功能 Created by Administrator on 2018/12/12.
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/rblocUser")
@Tag(name = "授信账户")
public class RBlocUserRest extends BaseController {

    @Autowired
    private RBlocUserService rBlocUserService;

    /**
     * 集团客户id查询集团客户详情
     *
     * @param rBlocUserId
     * @return
     */
    @ResponseBody
    @GetMapping("/findRBlocUserById")
    public ObjectResponse<RBlocUser> findRBlocUserById(Long rBlocUserId) {
        return rBlocUserService.findRBlocUserById(rBlocUserId);
    }

    @CheckToken(check = "getToken4Aspect")
    @Operation(summary = "获取带账户信息授信客户")
    @GetMapping(value = "/getCorpUserWithAccountInfo")
    public Mono<ObjectResponse<RBlocUserVo>> getCorpUserWithAccountInfo(
        ServerHttpRequest request,
        @Parameter(name = "授信客户ID", required = true) @RequestParam("corpUserId") Long corpUserId) {
        log.info("获取带账户信息授信客户: {}", LoggerHelper2.formatEnterLog(request));
        return rBlocUserService.getCorpUserWithAccountInfo(corpUserId);
    }

    /**
     * 查询集团客户信息(分页查询)
     *
     * @param blocUserId 集团id
     * @param keyWord    查询条件（手机号/客户id）
     * @return
     */
    @ResponseBody
    @GetMapping("/queryRBlocUser")
    public ListResponse<com.chargerlinkcar.framework.common.domain.vo.RBlocUser> queryRBlocUser(
        ServerWebExchange exh,
        Long blocUserId, String keyWord, ServerHttpRequest request) {
        String token = getToken2(request);
        OldPageParam page = getPage2(request, exh, false);
        String commIdChian = super.getCommIdChain2(request);
        log.info(
            "查询集团客户信息(分页查询)commIdChian = {}, blocUserId:{},keyWord:{},token:{},page:{}",
            commIdChian, blocUserId, keyWord, token, page);
        return rBlocUserService.queryRBlocUser(blocUserId, keyWord, page, commIdChian);
    }

    /**
     * 根据集团id查询所属集团的客户列表
     *
     * @param blocUserId
     * @return
     */
    @ResponseBody
    @GetMapping("/findRBlocUserByBlocUserId")
    public ListResponse<Long> findRBlocUserByBlocUserId(Long blocUserId) {
        return rBlocUserService.findRBlocUserByBlocUserId(blocUserId);
    }
}
