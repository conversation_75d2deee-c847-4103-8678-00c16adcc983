package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.biz.ant.feign.TradingFeignClient;
import com.chargerlinkcar.framework.common.domain.request.ExpectLimitSocReq;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * ExpectLimitSocService
 *
 * @since 6/1/2021 1:57 PM
 * <AUTHOR>
 */
@Slf4j
@Service
public class ExpectLimitSocService {

    @Autowired
    private TradingFeignClient tradingFeignClient;

    public BaseResponse changeExpectLimitSoc(ExpectLimitSocReq req) {

        IotAssert.isNotBlank(req.getOrderNo(), "请传入orderNo");
        IotAssert.isNotNull(req.getExpectLimitSoc(), "请传入期望修改的soc");

        return tradingFeignClient.changeExpectLimitSoc(req);
    }


}