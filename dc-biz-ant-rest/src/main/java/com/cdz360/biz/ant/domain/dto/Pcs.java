package com.cdz360.biz.ant.domain.dto;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * 储能设备 -- PCS
 */
@Data
@Accessors(chain = true)
public class Pcs {
    // 机柜序列号,
    @JsonProperty("ss")
    private String systemSn;
    // 监控软件版本,0.01
    @JsonProperty("msv")
    private BigDecimal monitorSoftwareVersion;
    // 监控硬件版本,0.01
    @JsonProperty("mhv")
    private BigDecimal monitorHardwareVersion;
    // AC模块软件版本,0.01
    @JsonProperty("asv")
    private BigDecimal acSoftwareVersion;
    // DC模块软件版本,0.01
    @JsonProperty("dsv")
    private BigDecimal dcSoftwareVersion;
    // U2模块软件版本,0.01
    @JsonProperty("u2sv")
    private BigDecimal u2SoftwareVersion;
    // BMS通讯超时时间,s
    @JsonProperty("bctt")
    private Integer bmsCommunicationTimeoutTime;
    // RS485通讯超时时间,s
    @JsonProperty("rs485ctt")
    private Integer rs485CommunicationTimeoutTime;
    // tcp通讯超时时间,s
    @JsonProperty("tctt")
    private Integer tcpCommunicationTimeoutTime;
    // BUS正极电压,0.1V
    @JsonProperty("bpv")
    private BigDecimal busPositiveVoltage;
    // BUS负极电压,0.1V
    @JsonProperty("bnv")
    private BigDecimal busNegativeVoltage;
    // AB交流线电压,0.1V
    @JsonProperty("alvab")
    private BigDecimal acLineVoltageAb;
    // BC交流线电压,0.1V
    @JsonProperty("alvbc")
    private BigDecimal acLineVoltageBc;
    // CA交流线电压,0.1V
    @JsonProperty("alvca")
    private BigDecimal acLineVoltageCa;
    // A相交流电流,0.1A
    @JsonProperty("aca")
    private BigDecimal acCurrentA;
    // B相交流电流,0.1A
    @JsonProperty("acb")
    private BigDecimal acCurrentB;
    // C相交流电流,0.1A
    @JsonProperty("acc")
    private BigDecimal acCurrentC;
    // 交流频率,0.01Hz
    @JsonProperty("af")
    private BigDecimal acFrequency;
    // A相交流有功功率,0.1kW
    @JsonProperty("acActivePA")
    private BigDecimal acActivePowerA;
    // B相交流有功功率,0.1kW
    @JsonProperty("acActivePB")
    private BigDecimal acActivePowerB;
    // C相交流有功功率,0.1kW
    @JsonProperty("acActivePC")
    private BigDecimal acActivePowerC;
    // A相交流无功功率,0.1kVAR
    @JsonProperty("arpa")
    private BigDecimal acReactivePowerA;
    // B相交流无功功率,0.1kVAR
    @JsonProperty("arpb")
    private BigDecimal acReactivePowerB;
    // C相交流无功功率,0.1kVAR
    @JsonProperty("arpc")
    private BigDecimal acReactivePowerC;
    // A相交流视在功率,0.1kVA
    @JsonProperty("aapa")
    private BigDecimal acApparentPowerA;
    // B相交流视在功率,0.1kVA
    @JsonProperty("aapb")
    private BigDecimal acApparentPowerB;
    // C相交流视在功率,0.1kVA
    @JsonProperty("aapc")
    private BigDecimal acApparentPowerC;
    // A相交流功率因数,0.01PF
    @JsonProperty("apa")
    private BigDecimal acPfA;
    // B相交流功率因数,0.01PF
    @JsonProperty("apb")
    private BigDecimal acPfB;
    // C相交流功率因数,0.01PF
    @JsonProperty("apc")
    private BigDecimal acPfC;
    // 模块温度,0.1℃
    @JsonProperty("mt")
    private BigDecimal moduleTemperature;
    // 环境温度,0.1℃
    @JsonProperty("at")
    private BigDecimal ambientTemperature;
    // 总交流有功功率,0.1kW
    @JsonProperty("acActivept")
    private BigDecimal acActivePowerTotal;
    // 总交流无功功率,0.1kVAR
    @JsonProperty("arpt")
    private BigDecimal acReactivePowerTotal;
    // 总交流视在功率,0.1kVAR
    @JsonProperty("aapt")
    private BigDecimal acApparentPowerTotal;
    // 总交流功率因数,1PF
    @JsonProperty("apt")
    private Integer acPfTotal;
    // 累计交流充电电量,kWh
    @JsonProperty("ace")
    private Long accumulativeChargedEnergy;
    // 累计交流放电电量,kWh
    @JsonProperty("ade")
    private Long accumulativeDischargedEnergy;
    // 最大运行容量,0.1kW
    @JsonProperty("moc")
    private BigDecimal maxOperatingCapacity;
    // 当天交流充电电量,kWh
    @JsonProperty("acet")
    private Integer acChargingElecToday;
    // 当天交流放电电量,kWh
    @JsonProperty("adet")
    private Integer acDischargeElecToday;
    // 并离网模式,
    @JsonProperty("ogm")
    private Integer onGridMode;
    // 能量调度模式,
    @JsonProperty("edm")
    private Integer energyDispatchingMode;
    // 启动方式,
    @JsonProperty("sum")
    private Integer startUpMode;
    // 无功控制模式,
    @JsonProperty("rpcm")
    private Integer reactivePowerControlMode;
    // 功率因数设置,0.01
    @JsonProperty("psp")
    private BigDecimal pfSetPoint;
    // 有功功率设置,0.1kW
    @JsonProperty("apsp")
    private BigDecimal activePowerSetPoint;
    // 无功功率设置,0.1kVAR
    @JsonProperty("rpsp")
    private BigDecimal reactivePowerSetPoint;
    // 紧急功率启动,
    @JsonProperty("eps")
    private Integer emergencyPowerStart;
    // 功率变化模式,
    @JsonProperty("pcm")
    private Integer powerChangeMode;
    // 电网恢复延时,s
    @JsonProperty("grd")
    private Integer gridReconnectDelay;
    // 离网交流电压调节,0.01
    @JsonProperty("ogavr")
    private BigDecimal offGridAcVoltageRegulation;
    // 无功响应模式,
    @JsonProperty("rprm")
    private Integer reactivePowerResponseMode;
    // 电压频率穿越限流使能,
    @JsonProperty("flf")
    private Integer fvrtLimitFunction;
    // 电压频率穿越使能,
    @JsonProperty("ff")
    private Integer fvrtFunction;
    // 孤岛检测使能,
    @JsonProperty("aie")
    private Integer antiIslandingEnable;
    // 离网交流电压启动方式,
    @JsonProperty("ogavsm")
    private Integer offGridAcVoltageStartupMode;
    // 有功控制模式,
    @JsonProperty("apcm")
    private Integer activePowerControlMode;
    // 系统状态1,
    @JsonProperty("ss1")
    private Integer systermState1;
    // 系统状态2,
    @JsonProperty("ss2")
    private Integer systermState2;
    // AC模块在线状态,
    @JsonProperty("amos")
    private Integer acModuleOnlineStatus;
    // AC模块运行状态,
    @JsonProperty("amrs")
    private Integer acModuleRunningStatus;
    // AC模块告警状态,
    @JsonProperty("amas")
    private Integer acModuleAlarmStatus;
    // DCAC模块故障状态,
    @JsonProperty("dms")
    private Integer dcacModuleStatus;
    // DCAC其它故障,
    @JsonProperty("dof")
    private Integer dcacOtherFault;
    // DCAC故障信息,
    @JsonProperty("dfml")
    private List<Integer> dcacFaultMessageList;
    // DCAC故障信息1,
//    private List<Integer> dcacFaultMessage1;
    // DCAC故障信息2,
//    private List<Integer> dcacFaultMessage2;
    // DCAC故障信息3,
//    private List<Integer> dcacFaultMessage3;
    // 总直流支路: 直流功率,0.1kW
    @JsonProperty("dp")
    private BigDecimal dcPower;
    // 总直流支路: 直流电压,0.1V
    @JsonProperty("dv")
    private BigDecimal dcVoltage;
    // 总直流支路: 直流电流,0.1A
    @JsonProperty("dc")
    private BigDecimal dcCurrent;
    // 总直流支路: 直流累计充电电量,kWh
    @JsonProperty("dace")
    private Long dcAccumulativeChargedEnergy;
    // 总直流支路: 直流累计放电电量,kWh
    @JsonProperty("dade")
    private Long dcAccumulativeDischargedEnergy;
    // 直流控制模式,
    @JsonProperty("dcm")
    private Integer dcControlMode;
    // 直流电流设置,0.1A
    @JsonProperty("dcs")
    private BigDecimal dcCurrentSetting;
    // 直流功率设置,0.1kW
    @JsonProperty("dps")
    private BigDecimal dcPowerSetting;
    // 直流下限电压,0.1V
    @JsonProperty("dllv")
    private BigDecimal dcLowerLimitVoltage;
    // 放电终止电压,0.1V
    @JsonProperty("dtv")
    private BigDecimal dischargeTerminationVoltage;
    // 均充电压,0.1V
    @JsonProperty("acp")
    private BigDecimal allChargingPressure;
    // 充电截止电流,0.1V
    @JsonProperty("ccoc")
    private BigDecimal chargingCutOffCurrent;
    // 最大充电电流,0.1A
    @JsonProperty("mcc")
    private BigDecimal maxChargingCurrent;
    // 最大放电电流,0.1A
    @JsonProperty("mdc")
    private BigDecimal maxDischargeCurrent;
    // DCAC开关机状态,
    @JsonProperty("dss")
    private Integer dcacSwitchState;
    // DCAC并离网状态,
    @JsonProperty("dogs")
    private Integer dcacOnGridState;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
