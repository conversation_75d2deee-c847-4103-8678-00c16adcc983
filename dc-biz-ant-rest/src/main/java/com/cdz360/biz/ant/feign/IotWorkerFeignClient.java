package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.domain.CfgEvseAllV2;
import com.cdz360.biz.model.iot.vo.EvseCfgResultPo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 *
 * @since 2019/6/17 20:59
 * <AUTHOR>
 */
@FeignClient(name = DcConstants.KEY_FEIGN_IOT_WORKER)
@Component
public interface IotWorkerFeignClient {
    @PostMapping(value = "/iot/biz/cfg/evse/getCfg")
    BaseResponse cfgEvseGetcfg(@RequestParam(value = "evseIds") List<String> evseIds);

    @PostMapping(value = "/iot/biz/cfg/evse/getCfgInfo")
    ObjectResponse<CfgEvseAllV2> cfgEvseGetcfgInfo(@RequestParam(value = "evseId") String evseId);

//    @PostMapping(value = "/iot/biz/evseCfg/siteInfo")
//    ListResponse<PriceSchemeSiteVo> getPriceSchemeSiteInfo(@RequestBody List<Long> idList);

    @GetMapping(value = "/iot/biz/evseCfg/getEvseCfg")
    ObjectResponse<EvseCfgResultPo> getEvseCfgResult(@RequestParam(value = "evseNo") String evseNo);

    /**
     * 云端查询桩器件信息指令
     * @param evseNo
     * @return
     */
    @PostMapping(value = "/iot/biz/evse/moduleQuery")
    BaseResponse moduleQuery(@RequestParam(value = "evseNo") String evseNo);
}
