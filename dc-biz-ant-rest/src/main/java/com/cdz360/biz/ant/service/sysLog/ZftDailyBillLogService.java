package com.cdz360.biz.ant.service.sysLog;

import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.model.base.vo.KvAny;
import com.cdz360.biz.auth.user.type.LogOpType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ZftDailyBillLogService {

    @Autowired
    private BaseSysLogService sysUserLogService;

    public void retryCheckBill(ServerHttpRequest request, String zftName, PayChannel channel, String billDate) {
        this.sysUserLogService.buildOpLog(LogOpType.DELIVERY,
                KvAny.of("支付平台账单", zftName + channel.getDesc() + billDate),
                request);
    }
}
