package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.aop.CheckToken;
import com.cdz360.biz.ant.domain.vo.SysRole;
import com.cdz360.biz.ant.service.sys.SysUserService;
import com.cdz360.biz.ant.service.sysLog.CustomerSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.auth.sys.param.BatchAddRoleUserParam;
import com.cdz360.biz.auth.sys.param.RoleUserListParam;
import com.cdz360.biz.auth.sys.param.RoleUserUpdateParam;
import com.cdz360.biz.auth.user.dto.SysUserLoginResult;
import com.cdz360.biz.auth.user.param.ListSysUserParam;
import com.cdz360.biz.auth.user.param.SysUserLoginParam;
import com.cdz360.biz.model.common.request.TokenRequest;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.sys.vo.SiteGroupVo;
import com.cdz360.biz.oa.param.account.ListOaGroupUserParam;
import com.chargerlinkcar.framework.common.domain.SysUser;
import com.chargerlinkcar.framework.common.domain.TCommercialUser;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Tag(name = "系统用户相关操作接口", description = "系统用户相关操作接口")
@Slf4j
@RestController
public class SysUserRest {

    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private CustomerSysLogService customerSysLogService;

    @Operation(summary = "获取审核组下属用户列表")
    @PostMapping(value = "/api/sys/user/inGroupList")
    public Mono<ListResponse<SysUserVo>> inGroupSysUserList(
        ServerHttpRequest request,
        @RequestBody ListOaGroupUserParam param) {
        log.info("获取审核组下属用户列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return sysUserService.inGroupSysUserList(param);
    }

    @Operation(summary = "企业微信授权登录")
    @GetMapping(value = "/api/sys/user/wxwork/auth")
    public Mono<ObjectResponse<String>> wxworkAuth(ServerHttpRequest request,
        @ApiParam("授权登录CODE") @RequestParam("code") String code) {
        log.info("企业微信授权登录: {}", LoggerHelper2.formatEnterLog(request));
        return sysUserService.wxworkJsCode2Session(
            AntRestUtils.getTopCommId(request), code);
    }

    @PostMapping(value = "/api/sys/user/login")
    public ObjectResponse<SysUserLoginResult> sysUserLogin(ServerHttpRequest request,
        @RequestBody SysUserLoginParam param) {
        log.info("平台账户登录: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), param);

        if (null == param.getPlatform()) { // 临时兼容户用储能
            val clientType = AntRestUtils.getAppClientType(request);
            if (null != clientType) {
                param.setClientType(clientType)
                    .setPlatform(clientType.getCode());
            }
        }

        param.setTopCommId(AntRestUtils.getTopCommId(request));
        ObjectResponse<SysUserLoginResult> res = sysUserService.sysUserLogin(param);
        customerSysLogService.login(res.getData(), request);
        return res;
    }

    @Operation(summary = "退出登录")
    @PostMapping("/api/sys/user/logout")
    public BaseResponse logout(ServerHttpRequest request
    ) {
        String token = AntRestUtils.getToken2(request);
        IotAssert.isNotBlank(token, "token不能为空");
        TokenRequest tokenRequest = new TokenRequest().setToken(token);
        return sysUserService.sysUserLoginOut(tokenRequest);
    }

    @Operation(summary = "系统用户切换到另一相关的账号")
    @PostMapping("/api/sys/user/getAuthCodeList")
    public ObjectResponse<SysUser> getAuthCodeList(ServerHttpRequest request
    ) {
        String token = AntRestUtils.getToken2(request);
        IotAssert.isNotBlank(token, "token不能为空");
        TokenRequest tokenRequest = new TokenRequest().setToken(token);
        return sysUserService.getUserByToken(tokenRequest);
    }

    @CheckToken
    @Operation(summary = "系统用户切换到另一相关的账号")
    @GetMapping(value = "/api/sys/user/switch")
    public Mono<ObjectResponse<SysUserLoginResult>> switchUser(
        ServerHttpRequest request,
        @Parameter(name = "切换的目标用户ID", required = true) @RequestParam(value = "targetSysUid") Long targetSysUid) {
        log.info("系统用户切换到另一相关的账号: {}, targetSysUid = {}",
            LoggerHelper2.formatEnterLog(request), targetSysUid);
//        Long sysUid = AntRestUtils.getSysUid(request);
        return sysUserService.switchUser(AntRestUtils.getToken2(request), targetSysUid);
    }

    @GetMapping(value = "/api/sys/user/getOpenId")
    public BaseResponse getOpenId(ServerHttpRequest request, @RequestParam("code") String code) {
        return sysUserService.getOpenId(request, code);
    }

    @GetMapping(value = "/api/sys/user/getAuthLoginTmpKey")
    public ObjectResponse<String> getAuthLoginTmpKey(ServerHttpRequest request,
        @RequestParam(value = "corpId") Long corpId) {
        log.info("企业平台免密登陆: " + LoggerHelper2.formatEnterLog(request) + "corpId = {}",
            corpId);
        IotAssert.isNotNull(corpId, "请传入 corpId");
        String idChain = AntRestUtils.getCommIdChain(request);
        IotAssert.isNotBlank(idChain, "无法获取idChain");
        return sysUserService.getLoginTmpKey(request, corpId, idChain);
    }

    @GetMapping(value = "/api/sys/user/loginByTmpKey")
    public ObjectResponse<SysUserLoginResult> loginByTmpKey(ServerHttpRequest request,
        @RequestParam(value = "key") String key) {
        log.info(
            "企业平台免密登陆，获取登陆结果: " + LoggerHelper2.formatEnterLog(request) + "key = {}",
            key);
        IotAssert.isNotNull(key, "请传入密钥");
        return sysUserService.getLoginInfoByKey(request, key);
    }

    /**
     * 管理平台 -设置 - 修改
     *
     * @param request
     * @param tcu
     * @return
     */
    @PostMapping("/api/sys/user/edit")
    public BaseResponse edit(ServerHttpRequest request,
        @RequestBody TCommercialUser tcu) {
        log.info("账号修改: " + LoggerHelper2.formatEnterLog(request) + "tcu = {}", tcu);
        IotAssert.isTrue(StringUtils.isNotBlank(tcu.getUsername()), "username不能为空");
        BaseResponse res = sysUserService.edit(request, tcu);
        customerSysLogService.userEdit(tcu.getUsername(), request);
        return res;
    }

    /**
     * 企业 - 设置 - 新建账号
     *
     * @param bodyEntity
     * @return
     */
    @PostMapping("/api/sys/user/add")
    public BaseResponse add(ServerHttpRequest request,
        @RequestBody SysUser bodyEntity) {
        log.info(
            "企业 - 设置 - 新建账号: " + LoggerHelper2.formatEnterLog(request) + "bodyEntity = {}",
            bodyEntity);
        IotAssert.isTrue(StringUtils.isNotBlank(bodyEntity.getUsername()), "参数错误");
        BaseResponse res = sysUserService.add(request, bodyEntity);
        customerSysLogService.userAdd(bodyEntity.getUsername(), request);
        return res;
    }

    /**
     * 企业 - 设置 - 账号修改
     *
     * @param bodyEntity
     * @return
     */
    @PostMapping("/api/sys/user/modify")
    public BaseResponse modify(ServerHttpRequest request,
        @RequestBody SysUser bodyEntity) {
        log.info(
            "企业 - 设置 - 账号修改: " + LoggerHelper2.formatEnterLog(request) + "bodyEntity = {}",
            bodyEntity);
        IotAssert.isTrue(StringUtils.isNotBlank(bodyEntity.getUsername()), "参数错误");
        BaseResponse res = sysUserService.modify(request, bodyEntity);
        customerSysLogService.userModify(bodyEntity.getUsername(), request);
        return res;
    }

    /**
     * 企业 - 设置 - 账号修改状态
     *
     * @param bodyEntity
     * @return
     */
    @PostMapping("/api/sys/user/changeState")
    public BaseResponse changeState(ServerHttpRequest request,
        @RequestBody SysUser bodyEntity) {
        log.info("企业 - 设置 - 账号修改状态: " + LoggerHelper2.formatEnterLog(request)
                + "bodyEntity = {}",
            bodyEntity);
        IotAssert.isTrue(StringUtils.isNotBlank(bodyEntity.getUsername()), "参数错误");
        BaseResponse res = sysUserService.changeState(request, bodyEntity);
        customerSysLogService.changeState(bodyEntity.getUsername(), request);
        return res;
    }

    @Operation(summary = "获取账号绑定的角色")
    @PostMapping("/api/sys/user/getRoleListByUserId")
    public ListResponse<SysRole> getRoleListByUserId(ServerHttpRequest request,
        @RequestBody RoleUserListParam params) {
        log.info("获取账号绑定的角色,params={}", JsonUtils.toJsonString(params));
        return sysUserService.getRoleListByUserId(request, params);
    }

    @Operation(summary = "账号尚未绑定的角色列表")
    @GetMapping("/api/sys/user/getRoleByUserId")
    public ListResponse<SysRole> getRoleByUserId(ServerHttpRequest request,
        @RequestParam(value = "keyWord", required = false) String keyWord,
        @RequestParam("platform") Long platform,
        @RequestParam("userId") Long userId,
        @RequestParam(value = "size", required = false) Long size) {
        log.info("账号尚未绑定的角色列表:keyWord={},platform={},userId={},size={}", keyWord,
            platform, userId,
            size);
        return sysUserService.getRoleByUserId(request, keyWord, platform, userId, size);
    }

    @Operation(summary = "批量修改用户关联的角色")
    @PostMapping("/api/sys/user/batchUpdateRoleUserByUserId")
    public BaseResponse batchUpdateRoleUserByUserId(ServerHttpRequest request,
        @RequestBody RoleUserUpdateParam params) {
        log.info("批量修改用户关联的角色,params={}", JsonUtils.toJsonString(params));
        return sysUserService.batchUpdateRoleUserByUserId(request, params);
    }

    @Operation(summary = "用户批量新增角色")
    @PostMapping("/api/sys/user/batchAddRoleUserByUserId")
    public BaseResponse batchAddRoleUserByUserId(ServerHttpRequest request,
        @RequestBody BatchAddRoleUserParam params) {
        log.info("用户批量新增角色，params={}", JsonUtils.toJsonString(params));
        return sysUserService.batchAddRoleUserByUserId(request, params);
    }

    @Operation(summary = "获取当前账号关联的场站组信息")
    @GetMapping(value = "/api/sys/user/getSiteGroupsList")
    public ListResponse<SiteGroupVo> getSiteGroupsList(ServerHttpRequest request,
        @RequestParam(required = false) List<Integer> types) {
        String token = AntRestUtils.getToken2(request);
        IotAssert.isNotBlank(token, "未登录状态");
        return sysUserService.getSiteGroupsList(token, types);

    }

    @Operation(summary = "获取当前账号关联的场站组信息")
    @GetMapping(value = "/api/sys/user/getSiteGroupsByUid")
    public ListResponse<SiteGroupVo> getSiteGroupsByUid(ServerHttpRequest request,
        @RequestParam(value = "type", required = false) Integer type) {
        String token = AntRestUtils.getToken2(request);
        IotAssert.isNotBlank(token, "未登录状态");
        return sysUserService.getSiteGroupsByUid(token, type);
    }

    @Operation(summary = "获取当前场站运维、售后人员，在工单转派使用")
    @GetMapping(value = "/api/sys/user/getUserListBySiteId")
    public ListResponse<SysUserVo> getUserListBySiteId(ServerHttpRequest request,
        @RequestParam(value = "siteId", required = false) String siteId) {
        IotAssert.isNotBlank(siteId,"场站ID不能为空");
        String token = AntRestUtils.getToken2(request);
        IotAssert.isNotBlank(token, "未登录状态");
        return sysUserService.getUserListBySiteId(siteId);
    }

    @Operation(summary = "获取当前用户相同团队标签的用户列表")
    @PostMapping(value = "/api/sys/user/sameCorpWxAppNameSysUser")
    public Mono<ListResponse<SysUserVo>> sameTeamCatalogSysUser(
        ServerHttpRequest request, @RequestBody ListSysUserParam param) {
        log.info("获取用户列表: {}", JsonUtils.toJsonString(param));
        param.setSameCorpWxAppNameUid(AntRestUtils.getSysUid(request));
        return sysUserService.sameCorpWxAppNameSysUser(AntRestUtils.getToken2(request), param);
    }

    @Operation(summary = "获取用户列表")
    @PostMapping(value = "/api/sys/user/findSysUserList")
    public Mono<ListResponse<SysUserVo>> findSysUserList(
        ServerHttpRequest request, @RequestBody ListSysUserParam param) {
        log.info("获取用户列表: {}", JsonUtils.toJsonString(param));
        return sysUserService.findSysUserList(AntRestUtils.getToken2(request), param);
    }
}
