package com.cdz360.biz.ant.domain.vo;

import lombok.Data;

import java.util.List;

/**
 * 站点定时充电设置(设备侧接口)
 *
 * SiteTimeInsertVo
 * <AUTHOR>
 * 
 * @since 2019.2.27
 */
@Data
public class SiteTimeInsertVo {
    /**
     * 定时id
     */
    private Integer timerId;
    /**
     * 站点id
     */
    private String siteId;
    /**
     * 配置定时充电的桩
     */
    private List<String> boxCodeS;
    /**
     * 开启时间
     */
    private String openTime;
    /**
     * 充电方式
     */
    private String chargerWay;
    /**
     * 充电频率
     */
    private String chargerFrequency;
    /**
     * 开启标志 1开启 0关闭 默认1
     */
    private String online;
}
