package com.cdz360.biz.ant.domain;

import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.model.corp.type.CorpType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *
 * 集团
 *<AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BlocUser implements Serializable {

	protected Long id;

	/** 商户Id */
	protected Long commId;

	@Schema(description = "所属的场站组")
	private List<String> gids;

	/** 集团客户名称 */
	@NotBlank(message="集团名称必须填写且长度不能超过30个字符",groups={First.class})
	@Size(max = 30,message = "集团名称必须填写且长度不能超过30个字符",groups = {First.class})
	protected String blocUserName;

	/** 联系人名称 */
	protected String contactName;

	/** 手机号 */
	protected String phone;

	/**
	 * 企业类型
	 */
	private CorpType type;

	/** 电子邮箱 */
	protected String email;

	/** 省 */
	protected String province;

	/** 市 */
	protected String city;

	/** 区 */
	protected String district;

	/** 地址 */
	protected String address;

	/** 组织机构代码 */
	protected String organizationImage;

	/** 营业执照 */
	protected String businessImage;

	/** 创建时间 */
	protected Date createDate;

	/** 账号 */
	@NotBlank(message="{message.userName.length}",groups={First.class})
	@Size(min = 4,max = 30,message = "{message.userName.length}",groups = {First.class})
	protected String account;

	/** 密码 */
	/** 密码(以字母开头，长度在6~18之间，只能包含字符、数字和下划线) */
//	@NotBlank(message="{message.password.Not}",groups={First.class})
	protected String password;
	/** 更新时间 */
	protected Date updateTime;

	@Schema(description = "企业结算方式: UNKNOWN(0)-未知,BALANCE(1)-账户余额扣减(先付费),GUARANTEE(2)-担保消费结算," +
			"POSTPAID(3)-预消费结算(后付费),PARTNER(4)-外部平台结算",
			format = "java.lang.Integer")
	private SettlementType settlementType;

	/**
	 * 续费提醒金额 ( 为null时表示不开启续费提醒 )
	 */
	protected BigDecimal renewReminderAmount;
	/**
	 * 是否已发送提醒邮件
	 */
	protected Boolean isSendReminderEmail;

	@Schema(description = "是否包含互联企业 null or true 表示包含; false 表示不包含")
	@JsonInclude(JsonInclude.Include.NON_NULL)
	private Boolean includedHlhtCorp;

	/**
	 * 企客摘要
	 */
	private String digest;
}
