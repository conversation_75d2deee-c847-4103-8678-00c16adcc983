package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.trading.order.param.PrepaidOrderListParam;
import com.cdz360.biz.model.trading.order.param.RewriteInterimParam;
import com.cdz360.biz.model.trading.order.vo.PrepaidOperationVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDataVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class ReactorDataCoreOrderFeignHystrix
    implements FallbackFactory<ReactorDataCoreOrderFeignClient> {

    @Override
    public ReactorDataCoreOrderFeignClient apply(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE,
            throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE,
            throwable.getStackTrace());
        return new ReactorDataCoreOrderFeignClient() {
            @Override
            public Mono<ListResponse<ChargerOrderVo>> getPrepaidOrderList(
                PrepaidOrderListParam param) {
                log.error("【服务熔断】: Service = {}, api = getPrepaidOrderList (根据PageType获取预付订单),"
                    + " param = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, param);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<Long>> getPrepaidOrderListCount(PrepaidOrderListParam param) {
                log.error("【服务熔断】: Service = {}, api = getPrepaidOrderListCount (根据PageType获取预付订单总数),"
                    + " param = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, param);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<String> > prepaidOrderOperation(PrepaidOrderListParam param) {
                log.error("【服务熔断】: Service = {}, api = prepaidOrderOperation (预付订单操作),"
                    + " param = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, param);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> rewriteInterimCodeByOa(RewriteInterimParam param) {
                log.error("【服务熔断】: Service = {}, api = rewriteInterimCodeByOa,"
                    + " param = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, param);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<PrepaidOperationVo>> getAllPrepaidOrderNos(PrepaidOrderListParam param) {
                log.error("【服务熔断】: Service = {}, api = getAllPrepaidOrderNos (获取全部能开票的预付订单号),"
                    + " param = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, param);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<ChargerOrderVo>> queryPrePaidOrderDetailList(
                PrepaidOrderListParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = queryPrePaidOrderDetailList (获取预付订单流程详情页的订单详细数据),"
                        + " param = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, param);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<ChargerOrderDataVo>> getPrepaidOrderDataVo(
                PrepaidOrderListParam param) {
                log.error("【服务熔断】: Service = {}, api = getPrepaidOrderDataVo (获取预付订单的统计信息),"
                    + " param = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, param);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }
        };
    }

    @Override
    public <V> Function<V, ReactorDataCoreOrderFeignClient> compose(
        Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(
        Function<? super ReactorDataCoreOrderFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
        return null;
    }
}
