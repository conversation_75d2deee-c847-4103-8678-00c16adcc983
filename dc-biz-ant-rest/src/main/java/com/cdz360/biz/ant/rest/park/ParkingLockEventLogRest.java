package com.cdz360.biz.ant.rest.park;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.ant.domain.park.vo.ParkingLockEventLogVo;
import com.cdz360.biz.ant.feign.reactor.ReactorDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "地锁事件日志相关接口")
@RestController
@Slf4j
@RequestMapping("/api/parkingLock/eventLog")
public class ParkingLockEventLogRest {

    @Autowired
    private ReactorDeviceMgmFeignClient deviceMgmFeignClient;

    @Operation(summary = "获取地锁近20天事件日志")
    @GetMapping(value = "/recent20")
    public Mono<ListResponse<ParkingLockEventLogVo>> eventLogRecent20(ServerHttpRequest request,
        @Parameter(name = "地锁ID", required = true) @RequestParam Long parkingLockId) {
        log.info("获取地锁近20天事件日志: {}", LoggerHelper2.formatEnterLog(request));
        return deviceMgmFeignClient.parkingLockEventLogRecent20(parkingLockId);
    }
}
