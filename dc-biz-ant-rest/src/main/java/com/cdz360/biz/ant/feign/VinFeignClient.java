package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.cus.vin.param.VinSearchParam;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.chargerlinkcar.framework.common.domain.vo.VinDto;
import com.chargerlinkcar.framework.common.domain.vo.VinDto2;
import com.chargerlinkcar.framework.common.domain.vo.VinParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 *  vin码操作
 * @since 2019/5/15 11:10
 * <AUTHOR>
 */
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_USER, fallbackFactory = VinFeignClientHystrixFactory.class)
public interface VinFeignClient {

    @RequestMapping(value = "/api/vin/create", method = RequestMethod.POST)
    BaseResponse create(@RequestBody VinParam vinParam);

    @RequestMapping(value = "/api/vin/delete", method = RequestMethod.POST)
    BaseResponse delete(@RequestParam("id") Long id);

    @RequestMapping(value = "/api/vin/update", method = RequestMethod.POST)
    BaseResponse update(@RequestBody VinParam vinParam);

    @PostMapping(value = "/api/vin/getById")
    ObjectResponse<VinDto> getById(@RequestParam("id") Long id);

    @RequestMapping(value = "/api/vin/select", method = RequestMethod.POST)
    ListResponse<VinDto> select(@RequestBody VinSearchParam vinSearchParam);

    @RequestMapping(value = "/api/vin/select", method = RequestMethod.POST)
    ObjectResponse<ExcelPosition> exportVinList(@RequestBody VinSearchParam vinSearchParam);

    /**
     * 订单筛选-查询商户及子商户Vin码列表
     *
     * @param vin
     * @return
     */
    @RequestMapping(value = "/api/vin/selectByVin")
    ObjectResponse<VinDto> selectByVin(@RequestParam(value = "vin") String vin,
        @RequestParam(value = "commId", required = false) Long commId);

    @PostMapping(value = "/api/vin/selectVinOnCorp")
    ListResponse<VinDto> selectVinOnCorp(@RequestBody VinParam vinParam);

    @PostMapping(value = "/api/vin/exportVinListOnCorp")
    ObjectResponse<ExcelPosition> exportVinListOnCorp(@RequestBody VinParam vinParam);

    @PostMapping(value = "/api/vin/createOnCorp")
    ObjectResponse createOnCorp(@RequestBody VinParam vinParam);

    @PostMapping(value = "/api/vin/batchCreateOnCorp")
    ObjectResponse batchCreateOnCorp(@RequestBody List<VinParam> vinParamList);

    @PostMapping(value = "/api/vin/updateByVinAndCommAndCorpOnCorp")
    ObjectResponse updateByVinAndCommAndCorpOnCorp(@RequestBody List<VinParam> vinParamList);

    @PostMapping(value = "/api/vin/getByIdList")
    ListResponse<VinDto> getByIdList(@RequestBody List<Long> idList);

    @GetMapping(value = "/api/vin/getVinDto2ById")
    ObjectResponse<VinDto2> getVinDto2ById(@RequestParam(value = "vinId") Long vinId,
        @RequestParam(value = "commIdChain") String commIdChain);
}