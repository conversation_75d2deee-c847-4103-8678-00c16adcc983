//package com.cdz360.biz.ant.rest.pv;
//
//import com.cdz360.base.model.base.dto.BaseResponse;
//import com.cdz360.base.model.base.dto.ListResponse;
//import com.cdz360.base.model.base.dto.ObjectResponse;
//import com.cdz360.biz.ant.service.pv.PvCfgService;
//import com.cdz360.biz.ant.utils.AntRestUtils;
//import com.cdz360.biz.model.common.request.Update;
//import com.cdz360.biz.model.trading.iot.param.ListGtiCfgParam;
//import com.cdz360.biz.model.trading.iot.po.GtiCfgPo;
//import com.cdz360.biz.model.trading.iot.vo.GtiCfgVo;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.server.reactive.ServerHttpRequest;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.web.bind.annotation.*;
//import reactor.core.publisher.Mono;
//
///**
// * PvCfgRest
// *
// * @since 8/31/2021 4:30 PM
// * <AUTHOR>
// */
//@Slf4j
//@RestController
//@Tag(name = "逆变器模板相关接口", description = "逆变器模板")
//@RequestMapping("/api/biz/pvCfg")
//public class PvCfgRest {
//    @Autowired
//    private PvCfgService service;
//
//    @PostMapping(value = "/list")
//    public Mono<ListResponse<GtiCfgVo>> list(
//            ServerHttpRequest request, @RequestBody ListGtiCfgParam param) {
//        log.info("pvCfg list. param: {}", param);
//        return service.list(param);
//    }
//
//    @GetMapping(value = "/getById")
//    public Mono<ObjectResponse<GtiCfgPo>> getById(
//            ServerHttpRequest request, @RequestParam(value = "cfgId") Long cfgId) {
//        log.info("pvCfg getById. cfgId: {}", cfgId);
//        return service.getById(cfgId);
//    }
//
//    @PostMapping(value = "/add")
//    public Mono<BaseResponse> add(
//            ServerHttpRequest request, @RequestBody @Validated GtiCfgPo param) {
//        log.info("pvCfg add. param: {}", param);
//        Long sysUid = AntRestUtils.getSysUid(request);
//        String userLoginName = AntRestUtils.getSysUserName(request);
//        param.setOpUid(sysUid)
//                .setOpName(userLoginName);
//        return service.add(param);
//    }
//
//    @PostMapping(value = "/edit")
//    public Mono<BaseResponse> edit(
//            ServerHttpRequest request, @RequestBody @Validated(Update.class) GtiCfgPo param) {
//        log.info("pvCfg edit. param: {}", param);
//        Long sysUid = AntRestUtils.getSysUid(request);
//        String userLoginName = AntRestUtils.getSysUserName(request);
//        param.setOpUid(sysUid)
//                .setOpName(userLoginName);
//        return service.edit(param);
//    }
//
//    @GetMapping(value = "/del")
//    public Mono<BaseResponse> del(
//            ServerHttpRequest request, @RequestParam(value = "cfgId") Long cfgId) {
//        log.info("pvCfg del. cfgId: {}", cfgId);
//        return service.del(cfgId);
//    }
//}