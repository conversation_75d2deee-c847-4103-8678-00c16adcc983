package com.cdz360.biz.ant.service.tj;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.feign.reactor.BizTjFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorAuthCenterFeignClient;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaParam;
import com.cdz360.biz.model.tj.kc.type.TjAreaShape;
import com.cdz360.biz.model.tj.kc.vo.TjAreaVo;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TjAreaService {

    @Autowired
    private ReactorAuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private BizTjFeignClient bizTjFeignClient;

    public Mono<ListResponse<TjAreaVo>> getAllOwnArea(ListTjAreaParam param) {
        return bizTjFeignClient.findUserArea(param);
    }

    public Mono<ListResponse<TjAreaVo>> findArea(ListTjAreaParam param) {
        return bizTjFeignClient.findArea(param);
    }

    public Mono<ListResponse<TjAreaVo>> findUserArea(ListTjAreaParam param) {
        return bizTjFeignClient.findUserArea(param)
            .doOnNext(FeignResponseValidate::check)
            .flatMap(res -> {
                val uidList = res.getData().stream().map(TjAreaVo::getUid).distinct()
                    .collect(Collectors.toList());

                return authCenterFeignClient.getSysUserByIdList(uidList)
                    .doOnNext(FeignResponseValidate::check)
                    .map(ListResponse::getData)
                    .map(userList -> {
                        val um = userList.stream()
                            .collect(Collectors.toMap(SysUserVo::getId, SysUserVo::getName));
                        return RestUtils.buildListResponse(
                            res.getData().stream()
                                .peek(u -> u.setUserName(um.getOrDefault(u.getUid(), "")))
                                .collect(Collectors.toList()),
                            null != res.getTotal() ? res.getTotal() : 0L);
                    });
            });
    }

    public Mono<TjAreaVo> getTjAreaByAid(Long aid) {
        IotAssert.isNotNull(aid, "投建区域唯一ID不能为空");
        return bizTjFeignClient.getTjAreaByAid(aid)
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .flatMap(area -> authCenterFeignClient.getSysUserById(area.getUid())
                .doOnNext(FeignResponseValidate::check)
                .map(ObjectResponse::getData)
                .map(u -> area.setUserName(u.getName())));
    }

    private static void checkTjAreaInfo(TjAreaVo area) {
        IotAssert.isNotBlank(area.getName(), "请提供区域名称");
        IotAssert.isNotBlank(area.getColor(), "请提供区域颜色");
        IotAssert.isNotBlank(area.getGid(), "请选择负责用户");
        IotAssert.isNotNull(area.getUid(), "请选择负责用户");
        IotAssert.isNotNull(area.getShape(), "请选择区域类型");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(area.getPaths()),
            "请提供区域Geo信息");
        IotAssert.isTrue(
            !(TjAreaShape.CIRCLE.getCode() == area.getShape() && null == area.getRadius()),
            "圆形需要提供半径");

        if (TjAreaShape.CIRCLE.getCode() == area.getShape()) {
            IotAssert.isTrue(1 == area.getPaths().get(0).size(),
                "圆形仅需要提供中心点");
        } else if (TjAreaShape.RECTANGLE.getCode() == area.getShape()) {
            IotAssert.isTrue(2 == area.getPaths().get(0).size(),
                "矩形仅需要提供左下角和右上角两个点");
        } else if (TjAreaShape.POLYGON.getCode() == area.getShape()) {
            IotAssert.isTrue(2 < area.getPaths().get(0).size(),
                "多变形需要提供至少三个点");
        } else { // 行政区
            IotAssert.isTrue(area.getPaths().stream().noneMatch(x -> x.size() < 1),
                "行政区中每个区域中至少三个点");
        }
    }

    public Mono<ObjectResponse<TjAreaVo>> addTjArea(TjAreaVo area) {
        checkTjAreaInfo(area);
        return bizTjFeignClient.saveTjArea(area);
    }

    public Mono<ObjectResponse<TjAreaVo>> editTjArea(TjAreaVo area) {
        IotAssert.isNotNull(area.getAid(), "投建区域ID不能为空");
        checkTjAreaInfo(area);
        return bizTjFeignClient.saveTjArea(area);
    }

    public Mono<ObjectResponse<TjAreaVo>> disableTjArea(Long aid) {
        IotAssert.isNotNull(aid, "投建区域唯一ID不能为空");
        return bizTjFeignClient.disableTjArea(aid);
    }
}
