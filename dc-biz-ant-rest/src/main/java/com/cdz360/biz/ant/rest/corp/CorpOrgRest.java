package com.cdz360.biz.ant.rest.corp;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.ant.service.corp.CorpOrgService;
import com.cdz360.biz.model.cus.corp.vo.CorpOrgVO;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@RestController
@Slf4j
@Tag(name = "企业所属组织", description = "企业所属组织")
public class CorpOrgRest {

    @Autowired
    private CorpOrgService corpOrgService;

    @Operation(summary = "获取企业下组织")
    @PostMapping("/api/corp/corpOrgList")
    public Mono<ListResponse<CorpOrgVO>> listCorpOrg(
            ServerHttpRequest request, @Parameter(name = "企业ID") @RequestParam(value = "corpId") Long corpId) {
        log.info("获取企业下组织: {}", LoggerHelper2.formatEnterLog(request));
        return corpOrgService.listCorpOrg(corpId);
    }

}
