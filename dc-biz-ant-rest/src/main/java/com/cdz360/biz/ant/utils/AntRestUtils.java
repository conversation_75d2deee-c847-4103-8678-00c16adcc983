package com.cdz360.biz.ant.utils;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.biz.model.common.constant.Constant;
import com.cdz360.biz.model.common.constant.DcBizConstants;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;

@Slf4j
public class AntRestUtils {

    private static final String HEADER_TOP_COMM_ID = "topCommId";
    private static final String HEADER_COMM_ID = "commId";

    private static final List<String> IP_HEADER_LIST = Arrays.asList("X-Forwarded-For", "Proxy-Client-IP",
            "WL-Proxy-Client-IP", "HTTP_CLIENT_IP", "HTTP_X_FORWARDED_FOR");

    private static final String HTTP_HEADER_LANG_KEY = "Content-Language";

    public static String getToken2(ServerHttpRequest request) {
        String token = request.getHeaders().getFirst(Constant.CURRENT_USER_TOKEN);
        if (StringUtils.isBlank(token)) {
            token = request.getQueryParams().getFirst(Constant.CURRENT_USER_TOKEN);
        }
        return token;
    }

    public static Long getTopCommId(ServerHttpRequest request) {
        long topCommId = 0L;
        try {
            topCommId = NumberUtils.parseLong(request.getHeaders().getFirst(Constant.CURRENT_TOP_COMM_ID), 0L);
        } catch (Exception e) {
            // ignore
        }
        if (topCommId > 0L) {
            return topCommId;
        }
        try {
            return Long.parseLong(request.getHeaders().getFirst(HEADER_TOP_COMM_ID));
        } catch (Exception e) {
            return null;
        }

    }


    public static Long getCommId(ServerHttpRequest request) {
        long commId = 0L;
        try {
            commId = NumberUtils.parseLong(request.getHeaders().getFirst(Constant.CURRENT_COMM_ID), 0L);
        } catch (Exception e) {
            // ignore
        }
        if (commId > 0L) {
            return commId;
        }
        try {
            return Long.parseLong(request.getHeaders().getFirst(HEADER_COMM_ID));
        } catch (Exception e) {
            return null;
        }

    }


    public static String getCommIdChain(ServerHttpRequest request) {
        return request.getHeaders().getFirst(Constant.CURRENT_COMM_ID_CHAIN);
    }


    /**
     * 是否是顶级商户
     */
    public static boolean isTopComm(ServerHttpRequest request) {
        final String commIdChain = getCommIdChain(request);
        if(StringUtils.isBlank(commIdChain)) {
            return false;
        }
        return Arrays.stream(commIdChain.split(","))
            .filter(StringUtils::isNotBlank)
            .filter(StringUtils::isNumeric)
            .count() == 1;
    }

    public static String getCommIdChainAndFilter(ServerHttpRequest request) {
        Long topCommId = getTopCommId(request);
        Long commId = getCommId(request);
        if (topCommId != null && topCommId.longValue() == DcBizConstants.superTopCommId
                && commId != null && commId.longValue() == DcBizConstants.superTopCommId) {   // 后门, 查看全平台统计数据
            return "";
        } else {
            return getCommIdChain(request);
        }
    }

    public static Long getSysUid(ServerHttpRequest request) {
        return NumberUtils.parseLong(request.getHeaders().getFirst(Constant.CURRENT_SYS_USER_ID), 0L);
    }

    /**
     * 获取当前用户的登录账号
     */
    public static String getSysUserLoginName(ServerHttpRequest request) {
        String sysUserLoginName = request.getHeaders().getFirst(Constant.CURRENT_SYS_USER_LOGIN_NAME);
        if (sysUserLoginName == null) {
            return null;
        }
        try {
            sysUserLoginName = URLDecoder.decode(sysUserLoginName, StandardCharsets.UTF_8);//解码
        } catch (Exception e) {
            log.warn("error = {}", e.getMessage(), e);
            return null;
        }
        return sysUserLoginName;
    }

    /**
     * 获取当前登录用户的姓名
     *
     */
    public static String getSysUserName(ServerHttpRequest request) {
        String sysUserName = request.getHeaders().getFirst(Constant.CURRENT_SYS_USER_NAME);
        if (sysUserName == null) {
            return null;
        }
        try {
            sysUserName = URLDecoder.decode(sysUserName, StandardCharsets.UTF_8);//解码
        } catch (Exception e) {
            log.warn("error = {}", e.getMessage(), e);
            return null;
        }
        return sysUserName;
    }

    /**
     * 获取当前登录用户的手机号
     */
    public static String getSysUserPhone(ServerHttpRequest request) {
        String sysUserPhone = request.getHeaders().getFirst(Constant.CURRENT_SYS_USER_PHONE);
        if (sysUserPhone == null) {
            return null;
        }
        try {
            sysUserPhone = URLDecoder.decode(sysUserPhone, StandardCharsets.UTF_8);//解码
        } catch (Exception e) {
            log.warn("error = {}", e.getMessage(), e);
            return null;
        }
        return sysUserPhone;
    }

    /**
     * 获取当前登录用户的场站组列表
     */
    public static List<String> getSysUserGids(ServerHttpRequest request) {
        String strGids = request.getHeaders().getFirst(Constant.CURRENT_SYS_USER_GIDS);
        if (StringUtils.isNotBlank(strGids)) {
            try {
                return Arrays.stream(URLDecoder.decode(strGids, StandardCharsets.UTF_8).split(",")).collect(Collectors.toList());
            } catch (Exception e) {
                log.warn("error = {}", e.getMessage(), e);
            }
        }
        return List.of();
    }

    /**
     * 获取客户端类型
     *
     * @param request
     * @return
     */
    public static AppClientType getAppClientType(ServerHttpRequest request) {
        return AppClientType.valueOf(NumberUtils.parseInt(request.getHeaders().getFirst(Constant.HD_APP_CLIENT), 0));
    }


    /**
     * 获取请求主机IP地址,如果通过代理进来，则透过防火墙获取真实IP地址;
     *
     */
    public static String getIpAddress(ServerHttpRequest request) {
        // 获取请求主机IP地址,如果通过代理进来，则透过防火墙获取真实IP地址

        String ip = "";
        Iterator<String> iterHeader = IP_HEADER_LIST.iterator();
        while (iterHeader.hasNext()) {
            String headerName = iterHeader.next();

            ip = request.getHeaders().getFirst(headerName);
            if (StringUtils.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip)) {
                log.debug("{} = {}", headerName, ip);
                break;
            }
        }
        if (StringUtils.isBlank(ip)) {
            ip = request.getRemoteAddress().getAddress().getHostAddress();
            log.debug("remote addr = {}", ip);
        } else {
            String[] toks = ip.split(",");
            for (int index = 0; index < toks.length && toks.length > 1; index++) {
                String strIp = toks[index];
                if (!"unknown".equalsIgnoreCase(strIp)) {
                    ip = strIp;
                    break;
                }
            }
        }
        if (StringUtils.isBlank(ip)) {
            log.error("无法获取请求方的IP");
        }
        return ip;
    }

    /**
     * 获取语言信息
     *
     * @param request
     * @return
     */
    public static Locale getLocale(final ServerHttpRequest request) {
        return locale(request.getHeaders().getFirst(HTTP_HEADER_LANG_KEY));
    }

    /**
     * 获取语言信息
     *
     * @param exh
     * @return
     */

    public static Locale getLocale(final ServerWebExchange exh) {
        return locale(exh.getRequest().getHeaders().getFirst(HTTP_HEADER_LANG_KEY));
    }

    /**
     * 设置语言信息
     *
     * @param lang
     * @return
     */
    private static Locale locale(final String lang) {
        return StringUtils.isNotBlank(lang) ? new Locale(lang)
            : null;
    }


}
