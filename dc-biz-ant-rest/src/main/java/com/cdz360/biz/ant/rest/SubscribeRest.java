package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.service.SubscribeService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.auth.subscribe.param.AddSubscribeParam;
import com.cdz360.biz.auth.subscribe.param.CreatePayOrderParam;
import com.cdz360.biz.auth.subscribe.param.SubLogListParam;
import com.cdz360.biz.auth.subscribe.param.SubscribeListParam;
import com.cdz360.biz.auth.subscribe.vo.*;
import com.cdz360.biz.auth.sys.vo.SysRoleSimpleVo;
import com.cdz360.biz.model.sys.vo.SubscribeOrderVo;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/sub")
@Tag(name = "功能订阅相关接口", description = "功能订阅")
public class SubscribeRest {

    @Autowired
    private SubscribeService subscribeService;

    /**
     * 新增功能订阅
     * @param params
     * @return
     */
    @PostMapping("/add")
    public BaseResponse add(ServerHttpRequest request, @RequestBody AddSubscribeParam params) {
        log.info("新增功能订阅,params={}", JsonUtils.toJsonString(params));
        Long sysUid = AntRestUtils.getSysUid(request);
        IotAssert.isNotNull(sysUid,"未登录状态");
        params.setCreateBy(sysUid);
        return subscribeService.add(params);
    }

    /**
     * 功能订阅编辑
     * @param params
     * @return
     */
    @PostMapping("/update")
    public BaseResponse update(ServerHttpRequest request,@RequestBody AddSubscribeParam params) {
        log.info("编辑功能订阅,params={}", JsonUtils.toJsonString(params));
        Long sysUid = AntRestUtils.getSysUid(request);
        IotAssert.isNotNull(sysUid,"未登录状态");
        params.setUpdateBy(sysUid);
        return subscribeService.update(params);
    }

    /**
     * 编辑功能订阅
     * @param subId
     * @return
     */
    @GetMapping("/updateStatus")
    public BaseResponse updateStatus(ServerHttpRequest request,@RequestParam("subId") Long subId) {
        Long sysUid = AntRestUtils.getSysUid(request);
        IotAssert.isNotNull(sysUid,"未登录状态");
        return subscribeService.updateStatus(sysUid,subId);
    }

    /**
     * 列表
     *
     * @param params
     * @return
     */
    @PostMapping("/getList")
    public ListResponse<SubscribeVo> getList(@RequestBody SubscribeListParam params) {
        log.info("功能订阅列表,params={}", JsonUtils.toJsonString(params));
        return subscribeService.getList(params);
    }

    /**
     * 绑定的商户信息
     *
     * @param subId
     * @return
     */
    @GetMapping("/getCommList")
    public ListResponse<CommVo> getCommList(@RequestParam("subId") Long subId) {
        IotAssert.isNotNull(subId, "ID不能为空");
        return subscribeService.getCommList(subId);
    }

    /**
     * 绑定的角色信息
     *
     * @param subId
     * @return
     */
    @GetMapping("/getRoleList")
    public ListResponse<SysRoleSimpleVo> getRoleList(@RequestParam("subId") Long subId) {
        IotAssert.isNotNull(subId, "ID不能为空");
        return subscribeService.getRoleList(subId);
    }

    @GetMapping("/getDetail")
    public ObjectResponse<SubscribeDetailVo> getDetail(@RequestParam("subId") Long subId) {
        IotAssert.isNotNull(subId, "ID不能为空");
        return subscribeService.getDetail(subId);
    }

    @GetMapping("/getListByUser")
    public ListResponse<SubscribeDetailVo> getListByUser(ServerHttpRequest request,
                                                   @RequestParam(value = "status", required = false) Boolean status) {
        String token = AntRestUtils.getToken2(request);
        IotAssert.isNotNull(token,"未登录状态");
        return subscribeService.getListByUser(token,status);
    }


    @PostMapping("/createPayOrder")
    public ObjectResponse<String> createPayOrder(ServerHttpRequest request,
                                                 @RequestBody CreatePayOrderParam params) {
        log.info("功能订阅创建订单，params={}", JsonUtils.toJsonString(params));
        params.setSysUid(AntRestUtils.getSysUid(request))
                .setCommId(AntRestUtils.getCommId(request))
                .setCommIdChain(AntRestUtils.getCommIdChain(request));
        return subscribeService.createPayOrder(params);
    }

    @GetMapping("/getOrderById")
    public ObjectResponse<SubscribeOrderVo> getOrderById(@RequestParam(value = "payNo") String payNo) {
        log.info("功能订阅订单号,payNo={}", payNo);
        return subscribeService.getOrderById(payNo);
    }

    @PostMapping("/getSubLogList")
    public ListResponse<SubscribeLogVo> getSubLogList(ServerHttpRequest request,
                                                      @RequestBody SubLogListParam params) {
        log.info("功能订阅记录,params={}", JsonUtils.toJsonString(params));
        params.setCommIdChain(AntRestUtils.getCommIdChain(request));
        return subscribeService.getSubLogList(params);
    }

    @GetMapping("/getRoleListByPayNo")
    public ListResponse<SubscribeOrderDetailVo> getRoleListByPayNo(@RequestParam(value = "payNo") String payNo) {
        log.info("功能订阅订单号,payNo={}", payNo);
        return subscribeService.getRoleListByPayNo(payNo);
    }

    @GetMapping("/addNote")
    public BaseResponse addNote(@RequestParam(value = "payNo") String payNo,
                                @RequestParam(value = "note") String note) {
        log.info("备注说明,payNo={},note={}", payNo, note);
        return subscribeService.addNote(payNo,note);
    }

}
