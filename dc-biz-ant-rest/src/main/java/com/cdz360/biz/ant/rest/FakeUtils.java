package com.cdz360.biz.ant.rest;

import org.apache.commons.lang3.RandomStringUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * @since 6/22/2020 5:13 PM
 * <AUTHOR>
 */
public class FakeUtils<T> {
    public T reflaction(T obj) {
        try {
            for (Field field : obj.getClass().getDeclaredFields()) {
                field.setAccessible(true); // You might want to set modifier to public first.
                Object val = field.get(obj);
                if (val instanceof Integer) {
                    field.set(obj, new Random((long) (Math.random() * 1000)).nextInt(1000));
                } else if(val instanceof BigDecimal) {
                    Integer ran = new Random((long) (Math.random() * 1000)).nextInt(1000);

                    BigDecimal bigDecimal = new BigDecimal(ran * Math.random()).setScale(2, RoundingMode.UP);
                    field.set(obj, bigDecimal);
                } else if(val instanceof Long) {
                    field.set(obj, Long.valueOf(new Random((long) (Math.random() * 1000)).nextInt(1000)));
                } else if(val instanceof String) {
                    field.set(obj, RandomStringUtils.random(10));
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return obj;
    }

    public List<T> reflactionList(T obj) {
        final Integer listCount = new Random((long) (Math.random() * 1000)).nextInt(10);
        int i = 0;
        List<T> ret = new ArrayList<>();
        try {
            for (; i < listCount; i++) {
                Class c = Class.forName(obj.getClass().getName());
                T t = (T) c.newInstance();
                ret.add(reflaction(t));
            }
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        } catch (InstantiationException e) {
            e.printStackTrace();
        }
        return ret;
    }
}