package com.cdz360.biz.ant.feign;


import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.domain.CustomUi;
import com.cdz360.biz.ant.domain.Notice;
import com.cdz360.biz.ant.domain.vo.BlocWalletVO;
import com.cdz360.biz.ant.domain.vo.NoticeVo;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.domain.BlocUserDto;
import com.chargerlinkcar.framework.common.domain.vo.SiteDebitAccountVo;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class MerchantFeignClientFHystrixactory implements FallbackFactory<MerchantFeignClient> {

    @Override
    public MerchantFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER,
            throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER,
            throwable.getStackTrace());

        return new MerchantFeignClient() {
            private String error = "异常信息：";

//            @Override
//            public ListResponse<Long> getCommIdListByToken(String token) {
//                return RestUtils.serverBusy4ListResponse();
//            }

//            @Override
//            public ListResponse<Long> getCommIdListByCommId(Long commId) {
//                return RestUtils.serverBusy4ListResponse();
//            }

            @Override
            public ListResponse<Commercial> getSiteDebitCommIdList(SiteDebitAccountVo vo) {
                return RestUtils.serverBusy4ListResponse();
            }

//            @Override
//            public ListResponse<PrepayAmount> queryRechargeList(Long commId) {
//                log.error(error + throwable.getMessage());
//                return RestUtils.serverBusy4ListResponse();
//            }

//            @Override
//            public BaseResponse addRechargeList(Long commId, List<Long> amountList) {
//                log.error(error+throwable.getMessage());
//                return RestUtils.serverBusy();
//            }

            @Override
            public BaseResponse customUiSaveOrUpdate(String token, CustomUi customUi) {
                log.error(error + throwable.getMessage());
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<CustomUi> queryCustomUi(CustomUi customUi) {
                log.error(error + throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<JsonNode> findMerchantById(Long merchantId, String token) {
                log.error(error + throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

//            @Override
//            public ObjectResponse<MerchantCommVo> getCurrentMerchant(String token) {
//                log.error(error+throwable.getMessage());
//                return RestUtils.serverBusy4ObjectResponse();
//            }

//            @Override
//            public ListResponse<BlocUserVo> queryBlocUser(Integer _num, Integer _size, String keyWord, List<Long> commIdList) {
//                return RestUtils.serverBusy4ListResponse();
//            }

//
//            @Override
//            public BaseResponse insertBlocUser(BlocUser blocUser) {
//                return RestUtils.serverBusy();
//            }

//            @Override
//            public BaseResponse updateBlocUser(BlocUser blocUser) {
//                return RestUtils.serverBusy();
//            }

            @Override
            public ObjectResponse<BlocUserDto> deleteBlocUserById(Long blocUserId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<BlocUserDto> enableBlocUserById(Long blocUserId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Integer> queryBlocUserByCondition(String account,
                String blocUserName) {
                return RestUtils.serverBusy4ObjectResponse();
            }

//            @Override
//            public ObjectResponse<BlocUserVo> loginBlocUser(BlocUserLoginVo blocUserLoginVo) {
//                log.error(error+throwable.getMessage());
//                return RestUtils.serverBusy4ObjectResponse();
//            }

//            @Override
//            public ObjectResponse<BlocUserVo> getBlocUserByAccount(String username) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }


            @Override
            public ObjectResponse<Integer> getDutyCount(String siteId) {
                log.error(error + throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<JsonNode> getSiteDuty(String siteId) {
                log.error(error + throwable.getMessage());
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse saveSiteDuty(String siteId, String siteDuty) {
                log.error(error + throwable.getMessage());
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<Notice> queryNoticeBySite(NoticeVo notice, Integer _size,
                Integer _index) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse addNotice(Notice notice) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<BlocWalletVO> getBlocUserByBlocUserId(Long blocUserId) {
                return RestUtils.serverBusy4ObjectResponse();

            }
        };
    }
}
