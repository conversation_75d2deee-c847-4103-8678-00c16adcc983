package com.cdz360.biz.ant.service;

import static java.time.temporal.ChronoField.DAY_OF_MONTH;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.feign.reactor.ReactorBizBiFeignClient;
import com.cdz360.biz.model.bi.dashboard.CommStatisticBiVo;
import com.cdz360.biz.ess.model.data.param.DataBiParam;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class DashboardBiService {

    @Autowired
    private ReactorBizBiFeignClient bizBiFeignClient;

    public Mono<ListResponse<CommStatisticBiVo>> chargeDataSample(DataBiParam param) {
        return bizBiFeignClient.chargeDataSample(param)
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .map(data -> {
                List<LocalDateTime> times = new ArrayList<>();
                switch (param.getSampleType()) {
                    case HOUR:
                        if (null != param.getShortcut()) {
                            LocalDateTime now = LocalDateTime.now();
                            LocalDateTime start = now.plusHours(-param.getShortcut() + 1);
                            while (start.isBefore(now)) {
                                times.add(start);
                                start = start.plusHours(1);
                            }
                            times.add(now);
                        } else if (param.getFromDate() != null && null != param.getToDate()) {
                            LocalDateTime toDate = param.getToDate();
                            LocalDateTime start = param.getFromDate();
                            while (start.isBefore(toDate)) {
                                times.add(start);
                                start = start.plusHours(1);
                            }
                            times.add(toDate);
                        } else if (null != param.getDate()) {
                            LocalDateTime toDate = param.getDate();
                            LocalDateTime start = param.getDate().toLocalDate().atStartOfDay();
                            while (start.isBefore(toDate)) {
                                times.add(start);
                                start = start.plusHours(1);
                            }
                            times.add(toDate);
                        } else {
                            LocalDateTime toDate = LocalDateTime.now();
                            LocalDateTime start = toDate.toLocalDate().atStartOfDay();
                            while (start.isBefore(toDate)) {
                                times.add(start);
                                start = start.plusHours(1);
                            }
                            times.add(toDate);
                        }
                    case DAY:
                        if (null != param.getShortcut()) {
                            LocalDateTime now = LocalDate.now().atStartOfDay();
                            LocalDateTime start = now.plusDays(-param.getShortcut() + 1);
                            while (start.isBefore(now)) {
                                times.add(start);
                                start = start.plusDays(1);
                            }
                            times.add(now);
                        } else if (param.getFromDate() != null && null != param.getToDate()) {
                            LocalDateTime toDate = param.getToDate().toLocalDate().atStartOfDay();
                            LocalDateTime start = param.getFromDate().toLocalDate().atStartOfDay();
                            while (start.isBefore(toDate)) {
                                times.add(start);
                                start = start.plusDays(1);
                            }
                            times.add(toDate);
                        }
                        break;
                    case MONTH:
                        times = new ArrayList<>();
                        if (null != param.getShortcut()) {
                            LocalDateTime now = LocalDate.now()
                                .with(DAY_OF_MONTH, 1).atStartOfDay();
                            LocalDateTime start = now.minusMonths(param.getShortcut() - 1);
                            while (start.isBefore(now)) {
                                times.add(start);
                                start = start.plusMonths(1);
                            }
                            times.add(now);
                        } else if (param.getFromDate() != null && null != param.getToDate()) {
                            LocalDateTime toDate = param.getToDate().toLocalDate().atStartOfDay();
                            LocalDateTime start = param.getFromDate().toLocalDate().atStartOfDay();
                            while (start.isBefore(toDate)) {
                                times.add(start);
                                start = start.plusMonths(1);
                            }
                            times.add(toDate);
                        }
                        break;
                }

                List<LocalDateTime> hasTimes = data.stream()
                    .map(i -> i.getDate().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDate().atStartOfDay())
                    .collect(Collectors.toList());
                times.stream().filter(t -> !hasTimes.contains(t))
                    .forEach(t -> data.add(new CommStatisticBiVo()
                        .setDate(Date.from(t.atZone(ZoneId.systemDefault()).toInstant()))));

                return data.stream()
                    .sorted(Comparator.comparing(CommStatisticBiVo::getDate))
                    .collect(Collectors.toList());
            })
            .map(RestUtils::buildListResponse);
    }
}
