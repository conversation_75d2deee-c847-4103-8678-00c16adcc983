package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.domain.park.dto.ParkingLockDto;
import com.cdz360.biz.ant.domain.park.param.ListParkingLockParam;
import com.cdz360.biz.ant.domain.park.vo.ParkingLockEventLogVo;
import com.cdz360.biz.ant.domain.park.vo.ParkingLockVo;
import com.cdz360.biz.model.sim.param.ListSimParam;
import com.cdz360.biz.model.sim.param.ModifyRelationParam;
import com.cdz360.biz.model.sim.po.SimPo;
import com.cdz360.biz.model.sim.vo.SimImportItem;
import com.cdz360.biz.model.sim.vo.SimTinyVo;
import com.cdz360.biz.model.sim.vo.SimVo;
import java.util.List;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class ReactorDeviceMgmFeignHystrix implements FallbackFactory<ReactorDeviceMgmFeignClient> {

    @Override
    public ReactorDeviceMgmFeignClient apply(Throwable throwable) {
        log.error("err = {}", throwable.getMessage(), throwable);
        return new ReactorDeviceMgmFeignClient() {

            @Override
            public Mono<ListResponse<ParkingLockVo>> parkingLotList(ListParkingLockParam param) {
                log.error("【服务熔断】。Service = {}, api = parkingLotList (获取地锁列表), param = {}",
                    DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<ParkingLockVo>> addParkingLock(ParkingLockDto dto) {
                log.error("【服务熔断】。Service = {}, api = addParkingLock (添加地锁), dto = {}",
                    DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, JsonUtils.toJsonString(dto));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<ParkingLockVo>> removeParkingLock(Long lockId) {
                log.error("【服务熔断】。Service = {}, api = removeParkingLock (删除地锁), lockId = {}",
                    DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, lockId);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<ParkingLockEventLogVo>> parkingLockEventLogRecent20(
                Long parkingLockId) {
                log.error("服务[{}]接口熔断 - 获取地锁近20天事件日志, parkingLockId = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, parkingLockId);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<SimVo>> getSimList(ListSimParam param) {
                log.error("【服务熔断】。Service = {}, api = getSimList (获取SIM卡信息), param = {}",
                    DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, param);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<SimTinyVo>> getSimTinyList(ListSimParam param) {
                log.error("【服务熔断】。Service = {}, api = getSimTinyList (SIM卡下拉列表（极简）), param = {}",
                    DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, param);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<BaseResponse> syncAll() {
                log.error("【服务熔断】。Service = {}, api = syncAll (同步增量卡)",
                    DcConstants.KEY_FEIGN_IOT_DEVICE_MGM);
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<ObjectResponse<SimPo>> syncSim(Long simId) {
                log.error("【服务熔断】。Service = {}, api = syncSim (同步单个SIM信息), simId = {}",
                    DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, simId);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> modifyRelation(ModifyRelationParam param) {
                log.error("【服务熔断】。Service = {}, api = modifyRelation (修改关系), param = {}",
                    DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, param);
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<BaseResponse> batchModifyRelation(List<SimImportItem> list) {
                log.error(
                    "【服务熔断】。Service = {}, api = batchModifyRelation (批量修改SIM和桩的绑定关系), list.size = {}",
                    DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, list.size());
                return Mono.just(RestUtils.serverBusy());
            }
        };
    }

    @Override
    public <V> Function<V, ReactorDeviceMgmFeignClient> compose(
        Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_DEVICE_MGM);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(
        Function<? super ReactorDeviceMgmFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_IOT_DEVICE_MGM);
        return null;
    }
}
