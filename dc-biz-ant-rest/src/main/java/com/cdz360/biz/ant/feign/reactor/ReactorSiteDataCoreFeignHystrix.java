package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.charge.vo.SiteVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.geo.param.ListCitiesParam;
import com.cdz360.biz.model.geo.param.ListCountriesParam;
import com.cdz360.biz.model.geo.po.DistrictPo;
import com.cdz360.biz.model.geo.vo.GeoCitiesVo;
import com.cdz360.biz.model.geo.vo.GeoCountriesVo;
import com.cdz360.biz.model.merchant.dto.SiteTinyDto;
import com.cdz360.biz.model.trading.contract.vo.ContractVo;
import com.cdz360.biz.model.trading.cus.vo.UnliquidatedOrderVo;
import com.cdz360.biz.model.trading.hlht.dto.CecQueryQeuipBusinessPolicyResult;
import com.cdz360.biz.model.trading.order.dto.CusLastOrderSiteDto;
import com.cdz360.biz.model.trading.order.dto.CusOrderBiDto;
import com.cdz360.biz.model.trading.order.param.ListCusOrderBiParam;
import com.cdz360.biz.model.trading.site.param.ListCecSiteParam;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.param.UpdateSiteOaDefaultValueParam;
import com.cdz360.biz.model.site.po.PriceTemplatePo;
import com.cdz360.biz.model.trading.site.po.SiteOaDefaultConfigPo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.site.vo.PartnerSiteVo;
import com.cdz360.biz.model.trading.site.vo.SiteOrderStatsVo;
import com.cdz360.data.sync.model.Site;
import com.chargerlinkcar.framework.common.domain.PartnerSiteDetailInfoVo;
import com.chargerlinkcar.framework.common.domain.SiteSocLimitDto;
import java.util.List;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class ReactorSiteDataCoreFeignHystrix
    implements FallbackFactory<ReactorSiteDataCoreFeignClient> {

    @Override
    public <V> Function<V, ReactorSiteDataCoreFeignClient> compose(
        Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(
        Function<? super ReactorSiteDataCoreFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
        return null;
    }

    @Override
    public ReactorSiteDataCoreFeignClient apply(Throwable throwable) {
        return new ReactorSiteDataCoreFeignClient() {
            @Override
            public Mono<ListResponse<PartnerSiteVo>> cecSiteList(ListCecSiteParam param) {
                log.error("【服务熔断】: Service = {}, api = cecSiteList (互联站点列表)",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<PartnerSiteDetailInfoVo>> cecSiteDetail(String siteId) {
                log.error("【服务熔断】: Service = {}, api = cecSiteDetail (互联站点详情)",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

//            @Override
//            public Mono<ObjectResponse<PlugStatusCountDto>> cecSitePlugStatusStats(String siteId) {
//                log.error("【服务熔断】: Service = {}, api = cecSitePlugStatusStats (互联站点枪头状态统计)",
//                        DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
//                return Mono.just(RestUtils.serverBusy4ObjectResponse());
//            }

            @Override
            public Mono<ListResponse<SiteOrderStatsVo>> siteOrderStats(String siteId) {
                log.error("【服务熔断】: Service = {}, api = siteOrderStats (站点充电订单数据统计)",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<DistrictPo>> getDistrictByList(List<String> code) {
                log.error("【服务熔断】: Service = {}, api = getDistrictByList",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<Boolean>> moveCorp(Long corpId, Long commId) {
                log.error("【服务熔断】: Service = {}, api = moveCorp",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<CusOrderBiDto>> getCusOrderBiList(ListCusOrderBiParam param) {
                log.error("【服务熔断】: Service = {}, api = getCusOrderBiList",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<CusLastOrderSiteDto>> getCusOrderLastSiteInfoList(
                ListCusOrderBiParam param) {
                log.error("【服务熔断】: Service = {}, api = getCusOrderLastSiteInfoList",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<UnliquidatedOrderVo>> getUnliquidatedNum(
                ListCusOrderBiParam param) {
                log.error("【服务熔断】: Service = {}, api = getUnliquidatedNum",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<SiteSocLimitDto>> getSocLimitInfo(String siteId) {
                log.error("【服务熔断】: Service = {}, api = getSocLimitInfo",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<Boolean>> updateSocLimitInfo(SiteSocLimitDto param) {
                log.error("【服务熔断】: Service = {}, api = updateSocLimitInfo",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<Site>> getSiteList(Long commId, String cityCode,
                List<String> siteIdList, long start, int size) {
                log.error("【服务熔断】: Service = {}, api = getSiteList",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<ContractVo>> getContractBySiteId(String siteId, Long size) {
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<SiteVo>> getSiteListByContractId(
                Long contractId) {
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<SiteTinyDto>> getSiteTinyList(ListSiteParam param) {
                log.error("【服务熔断】: Service = {}, api = getSiteTinyList, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<SitePo>> updateOaDefaultValue(
                UpdateSiteOaDefaultValueParam param) {
                log.error("【服务熔断】: Service = {}, api = updateOaDefaultValue, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<SiteOaDefaultConfigPo>> fetchOaDefaultValue(String siteId) {
                log.error("【服务熔断】: Service = {}, api = fetchOaDefaultValue, siteId = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, siteId);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<SiteOaDefaultConfigPo>> getOaDefaultValue(String siteId,
                String procDefKey) {
                log.error(
                    "【服务熔断】: Service = {}, api = getOaDefaultValue, siteId = {}, procDefKey = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, siteId, procDefKey);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<PriceTemplatePo>> getHlhtSitePriceTemplateList(String siteId) {
                log.error(
                    "【服务熔断】: Service = {}, api = getHlhtSitePriceTemplateList, siteId = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, siteId);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<BaseResponse> syncHlhtTemplate(
                CecQueryQeuipBusinessPolicyResult cecQueryQeuipBusinessPolicyResult) {
                log.error(
                    "【服务熔断】: Service = {}, api = syncHlhtTemplate, fullPlugNo = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, cecQueryQeuipBusinessPolicyResult.getFullPlugNo());
                return Mono.just(RestUtils.serverBusy());
            }

            @Override
            public Mono<ListResponse<GeoCountriesVo>> findCountries(ListCountriesParam param) {
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<GeoCitiesVo>> findCities(ListCitiesParam param) {
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }
        };
    }
}
