package com.cdz360.biz.ant.service;

import com.aliyun.oss.OSSException;
import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.domain.request.CardRequest;
import com.cdz360.biz.ant.domain.vo.CardListdetailVO;
import com.cdz360.biz.ant.feign.AntUserFeignClient;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.feign.BizBiFeignClient;
import com.cdz360.biz.ant.feign.CorpSettlementFeignClient;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.SiteFeignClient;
import com.cdz360.biz.ant.feign.TradingFeignClient;
import com.cdz360.biz.ant.feign.VinFeignClient;
import com.cdz360.biz.ant.utils.ExcelUtils;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.cus.corp.dto.BatchAddCreditResultItem;
import com.cdz360.biz.model.cus.corp.dto.BatchOpResult;
import com.cdz360.biz.model.cus.corp.param.BatchAddCreditUserParam;
import com.cdz360.biz.model.cus.corp.po.CorpOrgLoginVo;
import com.cdz360.biz.model.cus.corp.type.LimitCycle;
import com.cdz360.biz.model.cus.corp.vo.CorpOrgVO;
import com.cdz360.biz.model.cus.corp.vo.CorpVo;
import com.cdz360.biz.model.cus.settlement.vo.CorpSettlementCfgVo;
import com.cdz360.biz.model.cus.wallet.param.CreateDepositOrderParam;
import com.cdz360.biz.model.merchant.dto.SiteTinyDto;
import com.cdz360.biz.model.site.type.SiteStatus;
import com.cdz360.biz.model.trading.order.vo.PayBillVo;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.vo.CorpOrderCountVo;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.OrderCountParam;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.domain.param.CorpCardRequest;
import com.chargerlinkcar.framework.common.domain.pay.PaySign;
import com.chargerlinkcar.framework.common.domain.type.CardStatus;
import com.chargerlinkcar.framework.common.domain.type.CardType;
import com.chargerlinkcar.framework.common.domain.vo.Card;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.domain.vo.SiteSelectInfoVo;
import com.chargerlinkcar.framework.common.domain.vo.VinDto;
import com.chargerlinkcar.framework.common.domain.vo.VinParam;
import com.chargerlinkcar.framework.common.feign.CardFeignClient;
import com.chargerlinkcar.framework.common.feign.CommercialFeignClient;
import com.chargerlinkcar.framework.common.feign.CorpFeignClient;
import com.chargerlinkcar.framework.common.feign.SiteDataCoreFeignClient;
import com.chargerlinkcar.framework.common.utils.AssertUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.PhoneUtils;
import com.chargerlinkcar.framework.common.utils.RegularExpressionUtil;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class CorpService //implements ICorpService
{

    @Autowired
    private CardFeignClient cardFeignClient;
    @Autowired
    private AntUserFeignClient userFeignClient;
    @Autowired
    private TradingFeignClient tradingFeignClient;
    @Autowired
    private VinFeignClient vinFeignClient;
    @Autowired
    private SiteFeignClient siteFeignClient;
    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private SiteDataCoreFeignClient siteDateCoreFeignClient;
    @Autowired
    private CorpFeignClient corpFeignClient;

    @Autowired
    private CorpSettlementFeignClient corpSettlementFeignClient;

    @Autowired
    private CommercialFeignClient commercialFeignClient;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private BizBiFeignClient bizBiFeignClient;

    private static final Integer VIN_STATUS_ACTIVE = 1;
    private static final Integer VIN_STATUS_INACTIVE = 0;

    private static final Integer VIN_ENABLE = 1;
    private static final Integer VIN_DISENABLE = 0;


    public ListResponse<CorpOrgVO> getOrgTree(Long blocUserId, OldPageParam pageVo) {
        return userFeignClient.getOrgTree(blocUserId, pageVo.getPageNum(), pageVo.getPageSize());
    }


    public ListResponse<CorpOrgVO> getOrgList(String token, Long blocUserId,
        OldPageParam pageVo) {
        return authCenterFeignClient.getOrgList(token, blocUserId, pageVo.getPageNum(),
            pageVo.getPageSize());
    }


    public ListResponse<CardListdetailVO> onlineCardsList(CardRequest cardRequest) {
        log.info("根据条件查询卡片列表查询条件是cardRequest: {}",
            JsonUtils.toJsonString(cardRequest));
        //调用服务
        cardRequest.setPage(cardRequest.get_index());
        cardRequest.setRows(cardRequest.get_size());
        ListResponse<CardListdetailVO> jsonResult = userFeignClient.queryOnlineCardsByPageOnCorp(
            cardRequest);
        log.info("根据条件查询卡片列表调用服务完成结果是{}", JsonUtils.toJsonString(jsonResult));
        FeignResponseValidate.check(jsonResult);
        return jsonResult;
    }

    public ObjectResponse<ExcelPosition> exportOnlineCardsByCorp(CardRequest cardRequest) {
        //调用服务
        cardRequest.setPage(cardRequest.get_index());
        cardRequest.setRows(cardRequest.get_size());
        CorpCardRequest corpCardRequest = new CorpCardRequest();
        BeanUtils.copyProperties(cardRequest, corpCardRequest);
        return bizBiFeignClient.exportCardForCorp(corpCardRequest);
    }


    public BaseResponse grantCard(CardRequest cardRequest, String commIdChain,
        List<String> gids) throws ParseException {
        log.info("cardRequest: {}, commIdList: {}", JsonUtils.toJsonString(cardRequest),
            commIdChain);

        this.cardRequestHandle(cardRequest, gids, commIdChain);

        ObjectResponse<Card> resp = cardFeignClient.queryCardByCardNo(cardRequest.getCardNo());
        FeignResponseValidate.check(resp);
        Card respCard = resp.getData();
        if (respCard.getCardType() != null && respCard.getCardStatus() != null) {
            if (respCard.getCardType().equals(CardType.ONLINE.getCode()) ||
                respCard.getCardType().equals(CardType.EMERGENCY.getCode()) ||
                respCard.getCardStatus().equals(CardStatus.ACTIVE.getCode())) {
                throw new DcServiceException("该卡已被激活");
            }
        }

        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String currDate = dateFormat.format(date);
        Card card = new Card();

        card.setCardNo(cardRequest.getCardNo());// 逻辑卡号
        if (StringUtils.isNotEmpty(cardRequest.getCarNo())) {
            card.setCarNo(cardRequest.getCarNo().toUpperCase());
        }
        if (StringUtils.isNotEmpty(cardRequest.getCarNum())) {
            card.setCarNum(cardRequest.getCarNum().toUpperCase());
        }
        card.setLineNum(cardRequest.getLineNum());
        card.setCarDepart(cardRequest.getCarDepart()); //添加车队信息
        card.setCardName(cardRequest.getCardName());
        card.setMobile(cardRequest.getMobile());

        card.setUserId(cardRequest.getUserId());
        card.setCommId(cardRequest.getCommId());
        card.setCorpId(cardRequest.getCorpId());

        card.setStations(cardRequest.getStations());
        card.setCardStatus(CardStatus.ACTIVE.getCode());//新增卡成功后, 卡的状态为'已激活
        card.setCardType(CardType.ONLINE.getCode());//新增卡成功后, 卡的类型为在线卡
        card.setCardActivationDate(dateFormat.parse(currDate));//新增卡成功后, 当前时间为卡片激活时间
        card.setCardUpdateDate(dateFormat.parse(currDate));//更新修改时间
        log.info("新增卡片（发卡给客户）Card：{}", JsonUtils.toJsonString(card));
        //调用服务
        BaseResponse jsonResult = userFeignClient.updateCard(card);

        log.info("新增卡片（发卡给客户）调用服务完成结果是{}", JsonUtils.toJsonString(jsonResult));
        return jsonResult;
    }

    /**
     * 企业在线卡新增与修改时，对入参进行校验
     *
     * @param cardRequest
     */
    private void cardRequestHandle(CardRequest cardRequest, //List<Long> commIdList
        List<String> gids,
        String commIdChain
    ) {
        IotAssert.isTrue(StringUtils.isNotEmpty(cardRequest.getCardNo()), "逻辑卡号不能为空");
        IotAssert.isTrue(StringUtils.isNotEmpty(cardRequest.getCardChipNo()), "物理卡号不能为空");
        /*	物理卡号：系统自动显示不可编辑；
        	卡名称：最大长度40个字符；只支持字母、数字、汉字；
        	车牌号：总位数7-8位，第一位汉字，第二位字母，其余为字母、数字、汉字；（字母大小写不限，输入小写，入库自动转换为大写）；
        	线路：最大20个字符，只支持字母、数字、汉字、常规字符；
        	车辆自编号：5~17位数字、字母组成；（字母大小写不限，输入小写，入库自动转换为大写）；
        	组织名称：下拉单选，枚举值为当前管理员所属组织及下属组织，组织名称级联显示，插件单选可搜索（见图1.1.2c）；
        	手机号：下拉单选，组织名称在授信管理中关联数据，插件采用单选可搜索（见图1.1.2c）*/
        if (StringUtils.isNotEmpty(cardRequest.getCardName())) {
            IotAssert.isTrue(cardRequest.getCardName().length() <= 40,
                "卡名称最大长度40个字符");
            IotAssert.isTrue(
                RegularExpressionUtil.chineseEnglishNumberAll(cardRequest.getCardName()),
                "卡名称只支持字母、数字、汉字");
        }
        if (StringUtils.isNotEmpty(cardRequest.getCarNo())) {
            cardRequest.setCarNo(cardRequest.getCarNo().toUpperCase());// 车牌号自动转大写
            IotAssert.isTrue(
                cardRequest.getCarNo().length() == 7 || cardRequest.getCarNo().length() == 8,
                "车牌号只支持7-8位");
            IotAssert.isTrue(RegularExpressionUtil.isProductDemandCarNo(cardRequest.getCarNo()),
                "请输入正确车牌号");
        }
//        if (StringUtils.isNotEmpty(cardRequest.getLineNum())) {
//            IotAssert.isTrue(cardRequest.getLineNum().length() <= 20, "线路最大20个字符");
//            IotAssert.isTrue(RegularExpressionUtil.chineseEnglishNumberCharacterAll(cardRequest.getLineNum()),
//                    "线路只支持字母、数字、汉字、常规字符");
//        }
        RegularExpressionUtil.lineNumCheck(cardRequest.getLineNum());
//        if (StringUtils.isNotEmpty(cardRequest.getCarNum())) {
//            cardRequest.setCarNum(cardRequest.getCarNum().toUpperCase());// 车辆自编号自动转大写
//            IotAssert.isTrue(cardRequest.getCarNum().length() >= 5 && cardRequest.getCarNum().length() <= 17,
//                    "车辆自编号只支持5~17位");
//            IotAssert.isTrue(RegularExpressionUtil.englishNumberAll(cardRequest.getCarNum()),
//                    "车辆自编号只能由数字、字母组成");
//        }
        RegularExpressionUtil.carNumCheck(cardRequest.getCarNum());

        // TODO: 2019/12/23 待后续做权限功能时，组织名称需限制为当前管理员所属组织及下属组织 @WZ
        IotAssert.isTrue(cardRequest.getCorpOrgId() != null && cardRequest.getCorpOrgId() > 0,
            "请选择组织名称");
        IotAssert.isTrue(StringUtils.isNotEmpty(cardRequest.getMobile()), "请选择手机号");
        IotAssert.isTrue(StringUtils.isNotEmpty(cardRequest.getStations()), "请勾选可用场站");

        this.checkAvailableStation(commIdChain, gids,
            Arrays.asList(cardRequest.getStations().split(",")));

        // 获取userId
        RBlocUser rBlocUser = new RBlocUser();
        rBlocUser.setPhone(cardRequest.getMobile());
        rBlocUser.setBlocUserId(cardRequest.getCorpId());
        ListResponse<RBlocUser> rBlocUserListResponse = userFeignClient.findByCondition(rBlocUser);
        FeignResponseValidate.check(rBlocUserListResponse);
        List<RBlocUser> rBlocUserList = rBlocUserListResponse.getData();
        if (rBlocUserList.size() == 1) {
            cardRequest.setUserId(rBlocUserList.get(0).getUserId());
        } else {
            throw new DcServiceException("无法通过手机找到对应的客户");
        }
        IotAssert.isTrue(rBlocUserList.get(0).getCorpOrgId().equals(cardRequest.getCorpOrgId()),
            "组织名称与手机号无法对应");
    }

    /**
     * 企业VIN码新增与修改时，对入参进行校验
     *
     * @param vinParam
     */
    private void vinParamHandle(VinParam vinParam, //List<Long> commIdList
        List<String> gids,
        String commIdChain
    ) {
        IotAssert.isTrue(StringUtils.isNotEmpty(vinParam.getVin()), "VIN码不能为空");
        /*	VIN码：5~17位数字、字母组成（字母大小写不限，输入小写，入库自动转换为大写）；
        	车牌号：总位数7-8位，第一位汉字，第二位字母，其余为字母、数字、汉字；（字母大小写不限，输入小写，入库自动转换为大写）；
        	线路：最大20个字符，只支持字母、数字、汉字、常规字符；
        	车辆自编号：5~17位数字、字母组成；（字母大小写不限，输入小写，入库自动转换为大写）；
        	组织名称：下拉单选，枚举值为当前管理员所属组织及下属组织；
        	手机号：组织名称在授信管理中关联的手机号；
        	可用站点：顶部显示检索框，帮助模糊匹配过滤站点。枚举值为管理平台配置的该企业所能访问的站点名称*/
//        IotAssert.isTrue(vinParam.getVin().length() >= 5 || vinParam.getVin().length() <= 17,
////                "VIN码只支持5~17位");
        IotAssert.isTrue(RegularExpressionUtil.englishNumberAll(vinParam.getVin(), 5, 17),
            "VIN码只支持5~17位字母、数字");
        IotAssert.isTrue(!vinParam.getVin().startsWith("0"), "VIN码第一位不能为0");
        vinParam.setVin(vinParam.getVin().toUpperCase());// VIN码自动转大写
        if (StringUtils.isNotEmpty(vinParam.getCarNo())) {
            vinParam.setCarNo(vinParam.getCarNo().toUpperCase());// 车牌号自动转大写
            IotAssert.isTrue(vinParam.getCarNo().length() == 7 || vinParam.getCarNo().length() == 8,
                "车牌号只支持7-8位");
            IotAssert.isTrue(RegularExpressionUtil.isProductDemandCarNo(vinParam.getCarNo()),
                "请输入正确车牌号");
        }

        vinParam.paramCheck();
//        if (StringUtils.isNotEmpty(vinParam.getLineNum())) {
//            IotAssert.isTrue(vinParam.getLineNum().length() <= 20, "线路最大20个字符");
//            IotAssert.isTrue(RegularExpressionUtil.chineseEnglishNumberCharacterAll(vinParam.getLineNum()),
//                    "线路只支持字母、数字、汉字、常规字符");
//        }
//        if (StringUtils.isNotEmpty(vinParam.getCarNum())) {
//            vinParam.setCarNum(vinParam.getCarNum().toUpperCase());// 车辆自编号自动转大写
//            IotAssert.isTrue(vinParam.getCarNum().length() >= 5 && vinParam.getCarNum().length() <= 17,
//                    "车辆自编号只支持5~17位");
//            IotAssert.isTrue(RegularExpressionUtil.englishNumberAll(vinParam.getCarNum()),
//                    "车辆自编号只能由数字、字母组成");
//        }
        // TODO: 2019/12/23 待后续做权限功能时，组织名称需限制为当前管理员所属组织及下属组织 @WZ
        IotAssert.isTrue(vinParam.getCorpOrgId() != null && vinParam.getCorpOrgId() > 0,
            "请选择组织名称");
        IotAssert.isTrue(StringUtils.isNotEmpty(vinParam.getMobile()), "请选择手机号");
        IotAssert.isTrue(StringUtils.isNotEmpty(vinParam.getStation()), "请勾选可用场站");

        this.checkAvailableStation(commIdChain, gids,
            Arrays.asList(vinParam.getStation().split(",")));

        // 获取userId
        RBlocUser rBlocUser = new RBlocUser();
        rBlocUser.setPhone(vinParam.getMobile());
        rBlocUser.setBlocUserId(vinParam.getCorpId());
        ListResponse<RBlocUser> rBlocUserListResponse = userFeignClient.findByCondition(rBlocUser);
        FeignResponseValidate.check(rBlocUserListResponse);
        List<RBlocUser> rBlocUserList = rBlocUserListResponse.getData();
        if (rBlocUserList.size() == 1) {
            vinParam.setUserId(rBlocUserList.get(0).getUserId());
        } else {
            throw new DcServiceException("无法通过手机找到对应的客户");
        }
        IotAssert.isTrue(rBlocUserList.get(0).getCorpOrgId().equals(vinParam.getCorpOrgId()),
            "组织名称与手机号无法对应");
    }


    public BaseResponse batchGrantCard(List<CardRequest> cardRequest, long corpId, long commId,
        String stations, //List<Long> commIdList
        List<String> gids,
        String commIdChain
    ) {

        this.checkAvailableStation(commIdChain, gids, Arrays.asList(stations.split(",")));

        List<Card> cardList = new ArrayList<>();
        cardRequest.forEach(e -> {
            Date date = new Date();
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String currDate = dateFormat.format(date);
            Card card = new Card();

            card.setCardChipNo(e.getCardChipNo()); // 在car-user中会通过物理卡号去获取userId和cardNo
            card.setCardName(e.getCardName());
            if (StringUtils.isNotEmpty(e.getCarNo())) {
                card.setCarNo(e.getCarNo().toUpperCase());// 车牌自动转大写
            }
            card.setLineNum(e.getLineNum());
            if (StringUtils.isNotEmpty(e.getCarNum())) {
                card.setCarNum(e.getCarNum().toUpperCase());// 车辆自编号自动转大写
            }
            card.setMobile(e.getMobile());

            card.setCommId(commId);
            card.setCorpId(corpId);

            card.setStations(stations);
            card.setCardStatus(CardStatus.ACTIVE.getCode());//新增卡成功后, 卡的状态为'已激活
            card.setCardType(CardType.ONLINE.getCode());//新增卡成功后, 卡的类型为在线卡
            try {
                card.setCardActivationDate(dateFormat.parse(currDate));//新增卡成功后, 当前时间为卡片激活时间
                card.setCardUpdateDate(dateFormat.parse(currDate));//更新修改时间
            } catch (ParseException ex) {
                ex.printStackTrace();
            }
            cardList.add(card);
        });
        ObjectResponse<Integer> result = userFeignClient.batchGrantCard(cardList);
        return result;
    }


    public BaseResponse delCard(List<String> cardNoList) {
        log.info("cardNoList: {}", JsonUtils.toJsonString(cardNoList));

        List<String> noSettlementCardList = getNoSettlementCard(cardNoList, null);

        cardNoList.removeAll(noSettlementCardList);
        log.info("筛选后 cardNoList: {}", JsonUtils.toJsonString(cardNoList));
        List<Card> cardList = new ArrayList<>();
        cardNoList.forEach(e -> {
            Card card = new Card();
            card.setCardNo(e);
            card.setCardStatus(CardStatus.DELETED.getCode());
            card.setCardUpdateDate(new Date());
            cardList.add(card);
        });
        BaseResponse baseResponse = cardFeignClient.updateBatchCard(cardList);
        // 若存在未结算的订单，将可删除的进行删除后，还需要提示返回
        if (CollectionUtils.isNotEmpty(noSettlementCardList)) {
            log.info("单张或多张充电卡存在订单尚未处理无法删除，请处理后再进行操作");
            throw new DcServiceException(
                "单张或多张充电卡存在订单尚未处理无法删除，请处理后再进行操作");
        }
        return baseResponse;
    }

    /**
     * 筛选出有未结算订单的卡(返回逻辑卡号)
     *
     * @param cardNoList     物理卡号列表
     * @param cardChipNoList 逻辑卡号列表
     * @return cardNo 逻辑卡号
     */
    public List<String> getNoSettlementCard(List<String> cardNoList, List<String> cardChipNoList) {
        ChargerOrderParam param = new ChargerOrderParam();
        param.setCardChipNoList(cardChipNoList);
        param.setCardNoList(cardNoList);
        ListResponse<String> res = tradingFeignClient.getNoSettlementCard(param);
        log.info("查询结果: res={}", JsonUtils.toJsonString(res));
        FeignResponseValidate.check(res);
        return res.getData();
    }

    /**
     * 筛选出有未结算订单的VIN
     *
     * @param vinList
     * @return VIN码
     */
    public List<String> getNoSettlementVin(List<String> vinList, long topCommId) {
        ChargerOrderParam param = new ChargerOrderParam();
        param.setVinList(vinList);
        param.setTopCommId(topCommId);
        ListResponse<String> res = tradingFeignClient.getNoSettlementVin(param);
        log.info("查询结果: res={}", JsonUtils.toJsonString(res));
        FeignResponseValidate.check(res);
        return res.getData();
    }


    public ObjectResponse<Card> queryCardByCardChipNo(String cardChipNo) {
        log.info("cardChipNo: {}", cardChipNo);
        return userFeignClient.queryCardByCardChipNo(cardChipNo);
    }


    public ObjectResponse<Card> queryCardByCardNo(String cardNo) {
        log.info("cardNo: {}", cardNo);
        return cardFeignClient.queryCardByCardNo(cardNo);
    }


    public BaseResponse modifyCard(CardRequest cardRequest, List<String> gids, String commIdChain)
        throws ParseException {
        log.info("cardRequest: {}, commIdChain: {}", JsonUtils.toJsonString(cardRequest),
            commIdChain);

        this.cardRequestHandle(cardRequest, gids, commIdChain);

        List<String> noSettlementCardList = this.getNoSettlementCard(
            List.of(cardRequest.getCardNo()),
            List.of(cardRequest.getCardChipNo()));
        if (CollectionUtils.isNotEmpty(noSettlementCardList)) {
            throw new DcServiceException("充电卡存在订单尚未处理，请处理后再进行操作");
        }

        Date date = new Date();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String currDate = dateFormat.format(date);
        Card card = new Card();
        card.setCardNo(cardRequest.getCardNo());
        card.setCarNo(cardRequest.getCarNo());// 用户可改为""
        if (StringUtils.isNotEmpty(card.getCarNo())) {
            card.setCarNo(card.getCarNo().toUpperCase());
        }
        card.setCarNum(cardRequest.getCarNum());// 用户可改为""
        card.setCarDepart(cardRequest.getCarDepart()); //添加  carDepart
        if (StringUtils.isNotEmpty(card.getCarNum())) {
            card.setCarNum(card.getCarNum().toUpperCase());
        }
        card.setLineNum(cardRequest.getLineNum());// 用户可改为""
        card.setCardName(cardRequest.getCardName());// 用户可改为""
        card.setMobile(cardRequest.getMobile());
        card.setUserId(cardRequest.getUserId());
        card.setStations(cardRequest.getStations());
        card.setCardUpdateDate(dateFormat.parse(currDate));//更新修改时间
        log.info("修改卡片 Card：{}", JsonUtils.toJsonString(card));
        //调用服务
        BaseResponse jsonResult = userFeignClient.updateCard(card);
        log.info("修改卡片 调用服务完成结果是{}", JsonUtils.toJsonString(jsonResult));
        return jsonResult;
    }


    public BaseResponse reportLossCard(String cardNo) {
        log.info("挂失卡片cardNo：{}", cardNo);
        //调用服务
        ObjectResponse<Card> jsonResult = cardFeignClient.queryCardByCardNo(cardNo);
        FeignResponseValidate.check(jsonResult);
        Card card = jsonResult.getData();
        if (card == null) {
            log.info("挂失卡片时，查询该卡片详情失败{}", JsonUtils.toJsonString(jsonResult));
            throw new DcServiceException("查询该卡片详情错误");
        }
        if (card.getCardStatus() != null && card.getCardStatus()
            .equals(CardStatus.ACTIVE.getCode())) {

            //BUG2020-482 （管理平台和企业平台）卡【挂失】【冻结】操作时，删除判断订单结算的逻辑
//            List<String> noSettlementCardList = this.getNoSettlementCard(null, Arrays.asList(card.getCardChipNo()));
//            if (CollectionUtils.isNotEmpty(noSettlementCardList)) {
//                log.info("充电卡存在订单尚未处理，请处理后再进行操作");
//                throw new DcServiceException("充电卡存在订单尚未处理，请处理后再进行操作");
//            }

            BaseResponse jsonResult2 = userFeignClient.updateCardStatus(cardNo,
                CardStatus.LOCK.getCode());
            FeignResponseValidate.check(jsonResult2);
            return jsonResult2;
        } else {
            throw new DcServiceException("挂失操作仅可用于'已激活'的卡");
        }
    }


    public BaseResponse reportActivateCard(String cardNo) {
        log.info("激活卡片cardNo：{}", cardNo);
        //调用服务
        ObjectResponse<Card> jsonResult = cardFeignClient.queryCardByCardNo(cardNo);
        FeignResponseValidate.check(jsonResult);
        Card card = jsonResult.getData();
        if (card == null) {
            log.info("激活卡片时，查询该卡片详情失败{}", jsonResult);
            throw new DcServiceException("查询该卡片详情错误");
        }
        if (card.getCardStatus() != null && card.getCardStatus()
            .equals(CardStatus.LOCK.getCode())) {
            if (card.getCardActivationDate() == null) {
                Date date = new Date();
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String currDate = dateFormat.format(date);
                Card c = new Card();
                c.setCardNo(card.getCardNo());
                try {
                    c.setCardActivationDate(dateFormat.parse(currDate));
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                BaseResponse result = userFeignClient.updateCard(c);
                log.info("激活卡片时，发现卡无激活时间，将以当前时间为激活时间，更新后结果是{}",
                    result);
            }
            BaseResponse jsonResult2 = userFeignClient.updateCardStatus(cardNo,
                CardStatus.ACTIVE.getCode());
            return jsonResult2;
        } else {
            throw new DcServiceException("激活操作仅用于'挂失'状态的卡");
        }
    }


    public ListResponse<VinDto> selectVinOnCorp(VinParam vinParam) {
        return vinFeignClient.selectVinOnCorp(vinParam);
    }

    public ObjectResponse<ExcelPosition> exportVinListOnCorp(VinParam vinParam) {
        return bizBiFeignClient.exportVinForCorp(vinParam);
    }

    /**
     * 可用场站校验
     *
     * @param commIdChain
     * @param stationList
     */
    private void checkAvailableStation(String commIdChain,
        List<String> gids,
        List<String> stationList) {
        ListResponse<SiteSelectInfoVo> siteSelectInfoVoListResponse =
            // 该函数用户在线卡和VIN，设置为不包含互联站点
            this.getAvailableSite(commIdChain, gids, null, false);
        FeignResponseValidate.check(siteSelectInfoVoListResponse);
        List<SiteSelectInfoVo> siteSelectInfoVoList = siteSelectInfoVoListResponse.getData();
        IotAssert.isTrue(CollectionUtils.isNotEmpty(siteSelectInfoVoList), "当前用户无可用场站");
        List<String> sourceStationId = siteSelectInfoVoList.stream()
            .map(SiteSelectInfoVo::getSiteId).collect(Collectors.toList());
        log.info("sourceStationId: {}", JsonUtils.toJsonString(sourceStationId));
        stationList.forEach(e -> {
            if (!sourceStationId.contains(e)) {
                throw new DcServiceException(
                    "只能选择当前企业的所属商户下的站点及其子集商户下的站点，排除站点：" + e);//
            }
        });
    }


    public BaseResponse batchGrantVin(List<VinParam> vinParamList, long corpId, long commId,
        long subCommId, String stations, //List<Long> commIdList
        List<String> gids,
        String commIdChain
    ) {

        this.checkAvailableStation(commIdChain, gids, Arrays.asList(stations.split(",")));

        vinParamList.forEach(e -> {
            e.setCommId(commId)
                .setSubCommId(subCommId)
                .setCorpId(corpId)
                .setStation(stations);
        });
        ObjectResponse result = vinFeignClient.batchCreateOnCorp(vinParamList);
        return result;
    }


    public BaseResponse grantVin(VinParam vinParam, List<String> gids, String commIdChain) {
        log.info("vinParam: {}, commIdChain: {}", JsonUtils.toJsonString(vinParam), commIdChain);
        ObjectResponse<VinDto> vinDtoObjectResponse = vinFeignClient.selectByVin(
            vinParam.getVin().toUpperCase(),
            vinParam.getCommId());
        IotAssert.isNull(vinDtoObjectResponse.getData(), "该VIN码已被使用");

        this.vinParamHandle(vinParam, gids, commIdChain);

        return vinFeignClient.createOnCorp(vinParam);
    }


    public BaseResponse modifyVin(VinParam vinParam, List<String> gids, String commIdChain) {
        log.info("vinParam: {}, commIdChain: {}", JsonUtils.toJsonString(vinParam), commIdChain);
        ObjectResponse<VinDto> oldVinRes = vinFeignClient.getById(vinParam.getId());
        FeignResponseValidate.check(oldVinRes);
        VinDto oldVin = oldVinRes.getData();
        if (!oldVin.getVin().equals(vinParam.getVin())) {
            ObjectResponse<VinDto> vinDtoObjectResponse = vinFeignClient.selectByVin(
                vinParam.getVin().toUpperCase(),
                vinParam.getCommId());
            IotAssert.isNull(vinDtoObjectResponse.getData(), "修改失败，VIN码已被使用");
        }

        this.vinParamHandle(vinParam, gids, commIdChain);

        List<String> noSettlementVinList = this.getNoSettlementVin(Arrays.asList(vinParam.getVin()),
            vinParam.getCommId());
        if (CollectionUtils.isNotEmpty(noSettlementVinList)) {
            log.info("VIN码存在订单尚未处理，请处理后再进行操作");
            throw new DcServiceException("VIN码存在订单尚未处理，请处理后再进行操作");
        }
        return vinFeignClient.updateByVinAndCommAndCorpOnCorp(Arrays.asList(vinParam));
    }


    public List<String> delVin(List<Long> idList, Long topCommId, Long corpId) {
        log.info("idList: {}, topCommId: {}, corpId: {}",
            JsonUtils.toJsonString(idList), topCommId, corpId);
        AssertUtil.isTrue(CollectionUtils.isNotEmpty(idList), "请求参数错误");
        ListResponse<VinDto> vinDtoListResponse = vinFeignClient.getByIdList(idList);
        FeignResponseValidate.check(vinDtoListResponse);
        List<String> vinList = vinDtoListResponse.getData().stream().map(VinDto::getVin)
            .collect(Collectors.toList());
        if (idList.size() != vinList.size()) {
            throw new DcServiceException("服务异常");
        }
        Map<String, Long> idMap = vinDtoListResponse.getData().stream()
            .collect(Collectors.toMap(VinDto::getVin, VinDto::getId));
        if (idList.size() != idMap.size()) {
            throw new DcServiceException("服务异常");
        }
        List<String> noSettlementVinList = getNoSettlementVin(vinList, topCommId);

        vinList.removeAll(noSettlementVinList);
        log.info("筛选后 vinList: {}", JsonUtils.toJsonString(vinList));
        List<VinParam> vinParamListReq = new ArrayList<>();
        vinList.forEach(e -> {
            VinParam vinparam = new VinParam();
            Long id = idMap.get(e);
            if (id != null && id > 0) {
                vinparam.setId(id)
                    .setVin(e)
                    .setEnable(VIN_DISENABLE)
                    .setCommId(topCommId)
                    .setCorpId(corpId)
                    .setUpdateTime(new Date());
                vinParamListReq.add(vinparam);
            }
        });
        BaseResponse baseResponse = vinFeignClient.updateByVinAndCommAndCorpOnCorp(vinParamListReq);
        // 若存在未结算的订单，将可删除的进行删除后，还需要提示返回
        if (CollectionUtils.isNotEmpty(noSettlementVinList)) {
            log.info("单个或多个VIN码存在订单尚未处理无法删除，请处理后再进行操作");
            throw new DcServiceException(
                "单个或多个VIN码存在订单尚未处理无法删除，请处理后再进行操作");
        }
        return vinList;
    }

    /**
     * VIN码停用和启用前置检查
     *
     * @param corpId
     * @param vin
     */
    private void preCheck(long corpId, String vin) {
        log.info("corpId: {}, vin: {}", corpId, vin);
        VinParam vinParam = new VinParam();
        vinParam.setCorpId(corpId).setVin(vin);
        ListResponse<VinDto> vinDtoListResponse = vinFeignClient.selectVinOnCorp(vinParam);
        FeignResponseValidate.check(vinDtoListResponse);
        VinDto vinDto = vinDtoListResponse.getData().get(0);
        if (vinDto.getEnable() != VIN_ENABLE) {
            throw new DcServiceException("VIN码已被删除");
        }
    }


    public BaseResponse disableVin(Long id, String vin, Long commId, Long corpId) {
        log.info(" vin: {}, commId:{}, corpId: {}", vin, commId, corpId);
        this.preCheck(corpId, vin);
        //BUG2020-482 VIN码停用时，删除判断订单是否处理的逻辑
//        List<String> noSettlementVinList = this.getNoSettlementVin(Arrays.asList(vin));
//        if (CollectionUtils.isNotEmpty(noSettlementVinList)) {
//            log.info("VIN码存在订单尚未处理，请处理后再进行操作");
//            throw new DcServiceException("VIN码存在订单尚未处理，请处理后再进行操作");
//        }
        VinParam vinParam = new VinParam();
        vinParam.setId(id)
            .setVin(vin)
            .setStatus(VIN_STATUS_INACTIVE)
            .setCorpId(corpId)
            .setCommId(commId)
            .setUpdateTime(new Date());
        return vinFeignClient.updateByVinAndCommAndCorpOnCorp(Arrays.asList(vinParam));
    }


    public BaseResponse enableVin(Long id, String vin, Long commId, Long corpId) {
        log.info(" vin: {}, commId:{}, corpId: {}", vin, commId, corpId);
        this.preCheck(corpId, vin);
        VinParam vinParam = new VinParam();
        vinParam.setId(id)
            .setVin(vin)
            .setStatus(VIN_STATUS_ACTIVE)
            .setCorpId(corpId)
            .setCommId(commId)
            .setUpdateTime(new Date());
        return vinFeignClient.updateByVinAndCommAndCorpOnCorp(Arrays.asList(vinParam));
    }


//    public ListResponse<CorpOrgVO> getOrganization(Long blocUserId, OldPageParam pageVo) {
//        log.info("blocUserId: {}, pageVo: {}", blocUserId, pageVo);
//        ListResponse<CorpOrgVO> corpOrgVOListResponse = this.getOrgTree(blocUserId, pageVo);
//        ObjectResponse<CorpOrgPo> corpOrgPoObjectResponse = userFeignClient.getCorpOrgByIdAndLevel(
//            blocUserId, 1);
//        FeignResponseValidate.check(corpOrgPoObjectResponse);
//        CorpOrgPo vo = corpOrgPoObjectResponse.getData();
//        corpOrgVOListResponse.getData().forEach(e -> {
//            e.setPId(vo.getId());
//        });
//        return corpOrgVOListResponse;
//    }


    public ListResponse<SiteSelectInfoVo> getAvailableSite(
        String commIdChain, List<String> gids,
        String siteName, Boolean includedHlhtSite) {
        ListSiteParam siteListRequest = new ListSiteParam();
        if (CollectionUtils.isNotEmpty(gids)) {
            siteListRequest.setGids(gids);
        } else {
            siteListRequest.setCommIdChain(commIdChain);
        }
        siteListRequest.setSiteName(siteName);
        siteListRequest.setStatusList(
            List.of(SiteStatus.ONLINE, SiteStatus.OPENING, SiteStatus.UNAVAILABLE));
        siteListRequest.setIncludedHlhtSite(includedHlhtSite);
        log.info("查询可用场站列表请求参数 {}", JsonUtils.toJsonString(siteListRequest));
        ListResponse<SiteTinyDto> res = this.siteDateCoreFeignClient.getSiteTinyList(
            siteListRequest);
        List<SiteSelectInfoVo> list = res.getData().stream()
            .map(o -> {
                SiteSelectInfoVo ret = new SiteSelectInfoVo();
                ret.setSiteId(o.getId());
                ret.setSiteName(o.getSiteName());
                return ret;
            }).collect(Collectors.toList());
        //return siteFeignClient.getSiteSelectInfoList(siteListRequest);
        return RestUtils.buildListResponse(list);
    }


    public ObjectResponse<CorpVo> getCorpVo(Long corpId) {
        ObjectResponse<CorpVo> corpVo = this.corpFeignClient.getCorpVo(corpId);
        FeignResponseValidate.check(corpVo);

        ObjectResponse<Commercial> commercial = commercialFeignClient.getCommercial(
            corpVo.getData().getCommId());
        FeignResponseValidate.check(commercial);

        corpVo.getData().setEnableCorpDeposit(commercial.getData().getEnableCorpDeposit());
        corpVo.getData().setEnableCorpRefund(commercial.getData().getEnableCorpRefund());
        corpVo.getData().setSupportPayChannel(this.supportPayChannel(commercial.getData()));
        return corpVo;
    }

    public ListResponse<CorpOrderCountVo> getOrderPayCount(OrderCountParam orderCountParam) {
        ListResponse<CorpOrderCountVo> corpOrderCountVoListResponse = bizBiFeignClient.corpOrderCount(
            orderCountParam);
        FeignResponseValidate.check(corpOrderCountVoListResponse);
        return corpOrderCountVoListResponse;
    }

    private List<PayChannel> supportPayChannel(Commercial comm) {
        List<PayChannel> result = new ArrayList<>();
        if (StringUtils.isNotBlank(comm.getAlipaySubMchId())) {
            result.add(PayChannel.ALIPAY);
        }

        if (StringUtils.isNotBlank(comm.getWxSubMchId())) {
            result.add(PayChannel.WXPAY);
        }

        return result;
    }


    public void batchAddCreditAccount(BatchAddCreditUserParam param) {
        BaseResponse res = this.corpFeignClient.batchAddCreditAccount(param);
        FeignResponseValidate.check(res);
    }

    /**
     * 解析批量上传授信账户的excel文件
     *
     * @return
     */
    public Mono<ObjectResponse<BatchOpResult<BatchAddCreditResultItem>>> parseCorpCreditExcel(
        Long topCommId, Long commId, Long corpId,
        FilePart file) {
        log.info(
            ">> 解析企业平台导入授信账户 excel文件. topCommId = {}, commId = {}, corpId = {}, file name = {}",
            topCommId, commId, corpId,
            file.filename());
        //try {
        //List<Vin4ManagerVo> vinList = new ArrayList<>(); //excel导入的VIN集合
        BatchOpResult<BatchAddCreditResultItem> result = new BatchOpResult<>();
        List<BatchAddCreditResultItem> valid = new ArrayList<>();// 有效
        List<BatchAddCreditResultItem> invalid = new ArrayList<>(); // 无效

        Mono<ObjectResponse<BatchOpResult<BatchAddCreditResultItem>>> m = null;

        try {
            File f = new File("/tmp/" + file.filename());
            m = file.transferTo(f)
                .then(Mono.just("文件上传成功"))
                .map(a -> {
                    try {
                        FileInputStream inputStream = new FileInputStream(f);
                        try {

                            log.info("inputStream: {}", inputStream);
                            List<BatchAddCreditUserParam.CreditUser> list = ExcelUtils.parseCorpCreditAccounts(
                                inputStream,
                                file.filename());
                            list.stream().forEach(cu -> {
                                BatchAddCreditResultItem item = new BatchAddCreditResultItem();
                                boolean isValid = true;
                                item.setPhone(cu.getPhone())
                                    .setName(cu.getName())
                                    .setLimitMoney(cu.getLimitMoney())
                                    .setDurType(cu.getDurType());
                                if (!PhoneUtils.isValidPhoneNumber(cu.getPhone())) {
                                    item.setError("手机号格式错误");
                                    isValid = false;
                                } else if (StringUtils.isBlank(cu.getName())
                                    || cu.getName().length() > 40) {
                                    item.setError("姓名信息错误");
                                    isValid = false;
                                } else if (LimitCycle.UNKNOWN.equals(cu.getDurType())) {
                                    item.setError("限额周期错误");
                                    isValid = false;
                                } else if (this.check(cu.getDurType(), cu.getLimitMoney())) {
                                    item.setError("限额金额错误");
                                    isValid = false;
                                }
                                if (isValid) {
                                    valid.add(item);
                                } else {
                                    invalid.add(item);
                                }
                            });
                            log.info("从 excel 中获取内容: valid.size = {}, invalid.sie = {}",
                                valid.size(), invalid.size());
                        } catch (DcServiceException e) {
                            throw new DcServiceException(e.getMessage());
                        } catch (Exception e) {
                            log.warn("<< 解析excel文件异常. message = {}", e.getMessage(), e);
                            throw new DcServiceException("解析excel文件异常，请求确认文件内容.");
                        }
                        result.setValidList(valid).setInvalidList(invalid);
                        return result;
                    } catch (IOException e) {
                        log.error("上传文件失败.....");
                        throw new DcServiceException("文件上传失败");
                    }
                }).map(a -> RestUtils.buildObjectResponse(result));
        } catch (Exception e) {
            log.warn("msg: {}", e.getMessage(), e);
            throw new OSSException("文件上传失败");
        }
        return m;
//        try (InputStream inputStream = file.getInputStream()) {
//
//            log.info("inputStream: {}", inputStream);
//            List<BatchAddCreditUserParam.CreditUser> list = ExcelUtils.parseCorpCreditAccounts(inputStream,
//                    file.getOriginalFilename());
//            list.stream().forEach(cu -> {
//                BatchAddCreditResultItem item = new BatchAddCreditResultItem();
//                boolean isValid = true;
//                item.setPhone(cu.getPhone())
//                        .setName(cu.getName())
//                        .setLimitMoney(cu.getLimitMoney())
//                        .setDurType(cu.getDurType());
//                if (!PhoneUtils.isValidPhoneNumber(cu.getPhone())) {
//                    item.setError("手机号格式错误");
//                    isValid = false;
//                } else if (StringUtils.isBlank(cu.getName()) || cu.getName().length() > 40) {
//                    item.setError("姓名信息错误");
//                    isValid = false;
//                } else if (LimitCycle.UNKNOWN.equals(cu.getDurType())) {
//                    item.setError("限额周期错误");
//                    isValid = false;
//                } else if (this.check(cu.getDurType(), cu.getLimitMoney())) {
//                    item.setError("限额金额错误");
//                    isValid = false;
//                }
//                if (isValid) {
//                    valid.add(item);
//                } else {
//                    invalid.add(item);
//                }
//            });
//            log.info("从 excel 中获取内容: valid.size = {}, invalid.sie = {}",
//                    valid.size(), invalid.size());
//        } catch (DcServiceException e) {
//            throw new DcServiceException(e.getMessage());
//        } catch (Exception e) {
//            log.warn("<< 解析excel文件异常. message = {}", e.getMessage(), e);
//            throw new DcServiceException("解析excel文件异常，请求确认文件内容.");
//        }
//        result.setValidList(valid).setInvalidList(invalid);
//
//        return result;
    }

    public boolean check(LimitCycle durType, BigDecimal limitMoney) {
        boolean invalid = false;

        if (LimitCycle.UNLIMITED.equals(durType) && limitMoney != null) {
            invalid = true;
        } else if ((!LimitCycle.UNLIMITED.equals(durType)) &&
            (limitMoney == null || DecimalUtils.lteZero(limitMoney))
        ) {
            invalid = true;
        }
        return invalid;
    }

    public ObjectResponse<CorpSettlementCfgVo> getCorpSettlementCfg(@NotNull Long corpId) {
        return corpSettlementFeignClient.getSettlementCfg(corpId);
    }

    public ObjectResponse<PaySign> createDepositOrder(
        String ip, CorpOrgLoginVo corpInfo, PayChannel payChannel, BigDecimal amount) {
        if (StringUtils.isBlank(ip)) {
            throw new DcArgumentException("IP地址无效");
        }

        if (null == payChannel) {
            throw new DcArgumentException("充值方式不能为空");
        }

        if (null == amount || DecimalUtils.isZero(amount)) {
            throw new DcArgumentException("请提供有效的充值金额");
        }

        // 校验收款方
        ObjectResponse<Commercial> comm = commercialFeignClient.getCommercial(
            Long.valueOf(corpInfo.getCommId()));
        FeignResponseValidate.check(comm);

        if (comm.getData().getEnableBalance() != null) {
            List<PayChannel> payChannelList = this.supportPayChannel(comm.getData());
            if (!payChannelList.contains(payChannel)) {
                throw new DcServiceException("操作失败，请在管理后台配置对应的直付商家");
            }
        }

        CreateDepositOrderParam param = new CreateDepositOrderParam();
        param.setCusId(corpInfo.getCorpPo().getUid())
            .setCommId(corpInfo.getCorpPo().getCommId())
            .setTopCommId(corpInfo.getCorpPo().getTopCommId())
            .setClientIp(ip)
            .setAppClientType(AppClientType.CORP_WEB)
            .setPayChannel(payChannel)
            .setAccountType(PayAccountType.CORP)
            .setAmount(amount);

        return tradingFeignClient.createDepositOrder(param);
    }

    public ObjectResponse<PaySign> createSubscribeOrder(Long commId, Long topCommId, String ip,
        PayChannel payChannel, String payNo) {
        if (StringUtils.isBlank(ip)) {
            throw new DcArgumentException("IP地址无效");
        }

        if (null == payChannel) {
            throw new DcArgumentException("充值方式不能为空");
        }

        CreateDepositOrderParam param = new CreateDepositOrderParam();
        param.setCusId(0L)
            .setCommId(commId)
            .setTopCommId(topCommId)
            .setClientIp(ip)
            .setAppClientType(AppClientType.MGM_WEB)
            .setPayChannel(payChannel)
            .setAccountType(PayAccountType.OTHER)
            .setPayNo(payNo);

        return tradingFeignClient.createDepositOrder(param);
    }

    public ObjectResponse<PayBillVo> payOrderQuery(String payTradeNo) {
        if (StringUtils.isBlank(payTradeNo)) {
            throw new DcArgumentException("充值单号无效");
        }

        return dataCoreFeignClient.payOrderQuery(payTradeNo);
    }
}
