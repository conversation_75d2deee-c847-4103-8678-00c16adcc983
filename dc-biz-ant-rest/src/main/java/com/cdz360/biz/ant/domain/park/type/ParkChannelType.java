package com.cdz360.biz.ant.domain.park.type;


import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "道闸通道类型")
@Getter
public enum ParkChannelType implements DcEnum {

    UNKNOWN(999, "未知"),

    ENTER_CHANNEL(0, "入口"),
    EXIT_CHANNEL(1, "出口"),
    GATE_CHANNEL(2, "出入口"),
    ;
    @JsonValue
    private final int code;
    private final String desc;

    ParkChannelType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @JsonCreator
    public static ParkChannelType valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (ParkChannelType flag : values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        return ParkChannelType.UNKNOWN;
    }

}
