package com.cdz360.biz.ant.service.partner;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.corp.type.CorpType;
import com.cdz360.biz.ant.domain.BlocUser;
import com.cdz360.biz.ant.domain.User;
import com.cdz360.biz.ant.domain.request.AddAccountRequest;
import com.cdz360.biz.ant.domain.request.EditAccountRequest;
import com.cdz360.biz.ant.feign.AntUserFeignClient;
import com.cdz360.biz.ant.feign.AuthCenterFeignClient;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorAuthCenterFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorSiteDataCoreFeignClient;
import com.cdz360.biz.ant.feign.reactor.ReactorUserFeignClient;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.corp.type.LimitCycle;
import com.cdz360.biz.model.cus.discount.param.UpdateCorpDiscountParam;
import com.cdz360.biz.model.order.type.OrderPayType;
import com.cdz360.biz.model.trading.hlht.param.AddAccountParam;
import com.cdz360.biz.model.trading.hlht.param.CheckAccountParam;
import com.cdz360.biz.model.trading.hlht.param.EditAccountParam;
import com.cdz360.biz.model.trading.hlht.po.PartnerSitePo;
import com.cdz360.biz.model.trading.hlht.vo.ClientOperaterVo;
import com.cdz360.biz.model.trading.hlht.vo.HlhtSiteCommVo;
import com.cdz360.biz.model.trading.hlht.vo.OperatorVo;
import com.cdz360.biz.utils.feign.auth.AuthCommFeignClient;
import com.cdz360.biz.utils.feign.hlht.OpenHlhtFeignClient;
import com.cdz360.biz.utils.feign.user.UserFeignClient;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.feign.SiteDataCoreFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.UUIDUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
@Service
public class PartnerClientService {

    @Autowired
    private OpenHlhtFeignClient openHlhtFeignClient;
    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private AuthCommFeignClient authCommFeignClient;
    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private ReactorAuthCenterFeignClient reactorAuthCenterFeignClient;

    @Autowired
    private ReactorUserFeignClient reactorUserFeignClient;

    @Autowired
    private AntUserFeignClient antUserFeignClient;
    @Autowired
    private SiteDataCoreFeignClient siteDataCoreFeignClient;
    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private ReactorSiteDataCoreFeignClient reactorSiteDataCoreFeignClient;

    public Mono<ListResponse<OperatorVo>> partnerList(Long start, Integer size,
                                                      String code, String name,
                                                      String corpName, String siteName) {
        return openHlhtFeignClient.partnerList(start, size, code, name, corpName, siteName);
    }

    public Mono<ObjectResponse<ClientOperaterVo>> getDetail(Long partnerId) {
        return openHlhtFeignClient.getDetail(partnerId);
    }

    public Mono<BaseResponse> editBillingBack(Long partnerId, Boolean billingBack) {
        return openHlhtFeignClient.editBillingBack(partnerId, billingBack);
    }

    public Mono<BaseResponse> switchAccountStatus(Long accountId) {
        return openHlhtFeignClient.switchAccountStatus(accountId);
    }

    public Mono<BaseResponse> addAccount(AddAccountRequest request) {
        AddAccountRequest.check(request);

        AddAccountParam param = new AddAccountParam();
        param.setPartnerId(request.getPartnerId());

        AtomicBoolean isUsePartnerUserPhone = new AtomicBoolean(false); // true(不新增t_user,使用t_partner.uid) false(新增t_user)
        AtomicReference<Long> partnerUserId = new AtomicReference<>(0L);
        AtomicReference<String> partnerUserPhone = new AtomicReference<>("");

        CheckAccountParam checkAccountParam = new CheckAccountParam();
        checkAccountParam.setPartnerId(request.getPartnerId())
                .setIsEdit(Boolean.FALSE)
                .setSiteIdList(request.getSiteIdList());
        return authCommFeignClient.getCommercial(request.getCommId())
                .doOnNext(FeignResponseValidate::check)
                .flatMap(e -> {
                    param.setCorpTopCommId(e.getData().getTopCommId());
                    return openHlhtFeignClient.checkSiteforDuplicates(checkAccountParam);
                })
                .doOnNext(e -> {
                    FeignResponseValidate.check(e);
                    if (e.getData().getTopCommId().equals(param.getCorpTopCommId())) {
                        isUsePartnerUserPhone.set(true);
                        partnerUserId.set(e.getData().getUid());
                    }
                })
                .flatMap(e -> userFeignClient.getNextPartnerUserPhone())
                .doOnNext(FeignResponseValidate::check)
                .doOnNext(e -> {
                    // STEP 0、生成下一个互联运营商虚拟手机号
                    request.setPhone(e.getData());
                })
                .flatMap(e -> {
                    // STEP 1、新增企业账号
                    CorpPo corp = new CorpPo();
                    corp.setTopCommId(param.getCorpTopCommId())
                            .setCommId(request.getCommId())
                            .setCorpName(request.getCorpName())
                            .setContactName(request.getCorpName())
                            .setPhone(request.getPhone())
                            .setType(CorpType.HLHT)
                            .setAccount(request.getCorpName())
                            .setPassword(UUIDUtils.getRandom(false, 6));
                    return reactorAuthCenterFeignClient.addCorp(corp)
                            .doOnNext(FeignResponseValidate::check)
                            .map(res -> {
                                request.setCorpId(res.getData());
                                return res.getData();
                            });
                })
                .flatMap(corpId -> checkCorpExist(corpId, 5))   // 确认企业信息已同步到user库
                .flatMap(e -> {
                    // 新增成功，设置企业付费模式
                    UpdateCorpDiscountParam discountParam = new UpdateCorpDiscountParam();
                    discountParam.setCorpId(e.getId())
                            .setDiscountSwitch(false)
                            .setSettlementType(request.getSettlementType());
                    return reactorUserFeignClient.updateDiscount(discountParam)
                            .doOnNext(FeignResponseValidate::check);
                })
                .flatMap(e -> {
                    return Boolean.TRUE.equals(isUsePartnerUserPhone.get()) ?
                            userFeignClient.findInfoByUid(partnerUserId.get(),
                                    param.getCorpTopCommId(),
                                    null)
                                    .doOnNext(FeignResponseValidate::check)
                                    .map(res -> res.getData().getPhone())
                            : Mono.just(request.getPhone());
                })
                .flatMap(phone -> {
                    partnerUserPhone.set(phone);
                    // STEP 2、新建授信账户。
                    return reactorAuthCenterFeignClient.getOrgInfoByLevel(request.getCorpId(), 1)
                            .doOnNext(FeignResponseValidate::check)
                            .map(orgPoResponse -> {
                                RBlocUser rBlocUser = new RBlocUser();
                                return rBlocUser.setName(request.getCorpName())
                                        .setCorpOrgId(orgPoResponse.getData().getId())
                                        .setLimitMoney(null)
                                        .setLimitCycle(LimitCycle.UNLIMITED)
                                        .setBlocUserId(request.getCorpId())
                                        .setPhone(phone)
                                        .setCommId(request.getCommId())
                                        .setStatus(1);
                            })
                            .flatMap(rBlocUser -> userFeignClient.insertRBlocUser(rBlocUser));
                })
                .doOnNext(FeignResponseValidate::check)
                .flatMap(e -> {
                    RBlocUser req = new RBlocUser();
                    req.setPhone(isUsePartnerUserPhone.get() ? partnerUserPhone.get() : request.getPhone())
                            .setBlocUserId(request.getCorpId());

                    return reactorUserFeignClient.selectRBlocUserByPhone(req)
                            .doOnNext(FeignResponseValidate::check)
                            .map(ObjectResponse::getData)
                            .flatMap(corpUser -> {
                                param.setCorpId(request.getCorpId())
                                        .setCorpName(request.getCorpName())
                                        .setCorpUserId(corpUser.getId());

                                return Mono.just(param)
                                        .filter(p -> !isUsePartnerUserPhone.get()) // 未新增t_user nothing to do
                                        .flatMap(p -> {
                                            //新增t_user成功  修改t_user中的 defaultPayType,balanceId, partnerCode
                                            User updateUser = new User();
                                            updateUser.setId(corpUser.getUserId())
                                                    .setDefaultPayType(OrderPayType.BLOC.getCode())
                                                    .setBalanceId(corpUser.getId())
                                                    .setPartnerCode(request.getPartnerCode());
                                            return reactorUserFeignClient.setBasicInfo(updateUser);
                                        })
                                        .doOnNext(FeignResponseValidate::check)
                                        .map(user -> param)
                                        .switchIfEmpty(Mono.just(param));
                            });

//                    ObjectResponse<RBlocUser> rBlocUserObjectResponse = antUserFeignClient.selectRBlocUserByPhone(req);
//                    FeignResponseValidate.check(rBlocUserObjectResponse);
//                    if (isUsePartnerUserPhone.get()) {
//                        // 未新增t_user nothing to do
//                    } else {
//                        //新增t_user成功  修改t_user中的 defaultPayType,balanceId, partnerCode
//                        User updateUser = new User();
//                        updateUser.setId(rBlocUserObjectResponse.getData().getUserId())
//                                .setDefaultPayType(OrderPayType.BLOC.getCode())
//                                .setBalanceId(rBlocUserObjectResponse.getData().getId())
//                                .setPartnerCode(request.getPartnerCode());
//                        ObjectResponse<User> res = antUserFeignClient.setBasicInfo(updateUser);
//                        FeignResponseValidate.check(res);
//                    }
//
//                    return param.setCorpId(request.getCorpId())
//                            .setCorpName(request.getCorpName())
//                            .setCorpUserId(rBlocUserObjectResponse.getData().getId());
                })
                .flatMap(dto -> {
                    // STEP 3、组装partnerSitePoList
                    return reactorSiteDataCoreFeignClient.getSiteList(
                            null, null, request.getSiteIdList(), 0, request.getSiteIdList().size())
                            .doOnNext(FeignResponseValidate::check)
                            .map(ListResponse::getData)
                            .flatMapMany(Flux::fromIterable)
                            .map(e -> new PartnerSitePo()
                                    .setSiteId(e.getSiteId())
                                    .setSiteName(e.getName())
                                    .setSiteCommId(e.getCommId())
                                    .setCityCode(e.getCity()))
                            .collectList()
                            .map(dto::setPartnerSitePoList);

//                    ListResponse<Site> siteList = siteDataCoreFeignClient.getSiteList(null, null, request.getSiteIdList(), 0, request.getSiteIdList().size());
//                    FeignResponseValidate.check(siteList);
//                    List<PartnerSitePo> partnerSitePoList = new ArrayList<>();
//                    siteList.getData().forEach(e -> {
//                        PartnerSitePo partnerSitePo = new PartnerSitePo();
//                        partnerSitePo.setSiteId(e.getSiteId())
//                                .setSiteName(e.getName())
//                                .setSiteCommId(e.getCommId())
//                                .setCityCode(e.getCity());
//                        partnerSitePoList.add(partnerSitePo);
//                    });
//                    dto.setPartnerSitePoList(partnerSitePoList);
//                    return dto;
                })
                .flatMap(dto -> {
                    // STEP 4、调用HLHT新增结算账户
                    return openHlhtFeignClient.addAccount(param);
                })
                .doOnNext(FeignResponseValidate::check);
    }

    public Mono<BaseResponse> editAccount(EditAccountRequest request) {
        EditAccountParam param = new EditAccountParam();
        param.setPartnerId(request.getPartnerId())
                .setCorpName(request.getCorpName())
                .setAccountId(request.getAccountId());

        CheckAccountParam checkAccountParam = new CheckAccountParam();
        checkAccountParam.setPartnerId(request.getPartnerId())
                .setIsEdit(Boolean.TRUE)
                .setAccountId(request.getAccountId())
                .setSiteIdList(request.getSiteIdList());
        return authCommFeignClient.getCommercial(request.getCommId())
                .doOnNext(FeignResponseValidate::check)
                .flatMap(e -> {
                    param.setCorpTopCommId(e.getData().getTopCommId());
                    return openHlhtFeignClient.checkSiteforDuplicates(checkAccountParam);
                })
                .doOnNext(FeignResponseValidate::check)
                .flatMap(e -> {
                    BlocUser blocUser = new BlocUser();
                    blocUser.setId(request.getCorpId())
                            .setCommId(request.getCommId())
                            .setBlocUserName(request.getCorpName())
                            .setSettlementType(request.getSettlementType());
                    return reactorAuthCenterFeignClient.updateBlocUser(blocUser);
                })
                .doOnNext(FeignResponseValidate::check)
                .flatMap(e -> {
                    User updateUser = new User();
                    CorpPo corpPo = e.getData();
                    updateUser.setId(corpPo.getUid())
                            .setCommId(corpPo.getTopCommId());
                    return reactorUserFeignClient.setBasicInfo(updateUser);
                })
                .doOnNext(FeignResponseValidate::check)
                .flatMap(e -> {
                    CheckAccountParam checkReq = new CheckAccountParam();
                    checkReq.setPartnerId(request.getPartnerId())
                            .setAccountId(request.getAccountId())
                            .setSiteIdList(request.getSiteIdList());
                    return openHlhtFeignClient.isModifiedSiteList(checkReq);
                })
                .doOnNext(FeignResponseValidate::check)
                .flatMap(booleanObjectResponse -> {
                    Boolean res = booleanObjectResponse.getData();
                    if (res) {
                        return reactorSiteDataCoreFeignClient.getSiteList(null, null, request.getSiteIdList(), 0, request.getSiteIdList().size())
                                .doOnNext(FeignResponseValidate::check)
                                .map(e -> {
                                    List<PartnerSitePo> partnerSitePoList = new ArrayList<>();
                                    e.getData().forEach(site -> {
                                        PartnerSitePo partnerSitePo = new PartnerSitePo();
                                        partnerSitePo.setSiteId(site.getSiteId())
                                                .setSiteName(site.getName())
                                                .setSiteCommId(site.getCommId())
                                                .setCityCode(site.getCity());
                                        partnerSitePoList.add(partnerSitePo);
                                    });
                                    param.setPartnerSitePoList(partnerSitePoList);
                                    return param;
                                })
                                .flatMap(e -> openHlhtFeignClient.editAccount(e));
                    } else {
                        return openHlhtFeignClient.editAccount(param);
                    }
                });
    }

    public ObjectResponse<HlhtSiteCommVo> getSiteByPartnerCode(String code) {
        return dataCoreFeignClient.getSiteByPartnerCode(code);
    }

    private Mono<CorpPo> checkCorpExist(Long corpId, int maxRetry) {
        if (maxRetry < 1) {
            log.error("超过最大重试次数!!!");
            return Mono.empty();
        }
        return Mono.delay(Duration.ofMillis(500L))  // 等待corp信息同步到user库
                .flatMap(v -> {
                    return userFeignClient.getCorp(corpId)
                            .flatMap(m -> {
                                if (m.getData() != null) {
                                    return Mono.just(m.getData());
                                } else {
                                    return checkCorpExist(corpId, maxRetry - 1);
                                }
                            });
                });
    }
}
