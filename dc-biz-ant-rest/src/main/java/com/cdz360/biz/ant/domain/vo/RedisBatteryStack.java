//package com.cdz360.biz.ant.domain.vo;
//
//import com.cdz360.biz.ant.domain.dto.BatteryStack;
//import com.fasterxml.jackson.annotation.JsonFormat;
//import com.fasterxml.jackson.annotation.JsonInclude;
//import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
//import com.fasterxml.jackson.databind.annotation.JsonSerialize;
//import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
//import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
//import io.swagger.v3.oas.annotations.media.Schema;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//
//import java.time.LocalDateTime;
//
//@Data
//@EqualsAndHashCode(callSuper = true)
//public class RedisBatteryStack extends BatteryStack {
//
//    @Schema(description = "日期")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
//    @JsonSerialize(using = LocalDateTimeSerializer.class)
//    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
//    private LocalDateTime time;
//}
