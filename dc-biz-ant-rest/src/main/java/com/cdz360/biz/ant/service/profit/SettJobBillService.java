package com.cdz360.biz.ant.service.profit;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.feign.reactor.DataCoreSettJobBillFeignClient;
import com.cdz360.biz.model.trading.profit.sett.param.ListSettJobBillParam;
import com.cdz360.biz.model.trading.profit.sett.vo.SettJobBillDetailVo;
import com.cdz360.biz.model.trading.profit.sett.vo.SettJobBillVo;
import com.cdz360.biz.oa.dto.OaSettJobBillDto;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class SettJobBillService {

    @Autowired
    private DataCoreSettJobBillFeignClient settJobBillFeignClient;

    public Mono<ListResponse<SettJobBillVo>> findSettJobBill(ListSettJobBillParam param) {
        return settJobBillFeignClient.findSettJobBill(param);
    }

    public Mono<ListResponse<OaSettJobBillDto>> checkSettJobBill(List<OaSettJobBillDto> param) {
        return settJobBillFeignClient.checkSettJobBill(param);
    }

    public Mono<ObjectResponse<SettJobBillVo>> recalculateSettJobBill(
        String billNo, Integer recalculateWay) {
        IotAssert.isTrue(StringUtils.isNotBlank(billNo), "结算单编号不能为空");
        return settJobBillFeignClient.recalculateSettJobBill(billNo, recalculateWay);
    }

    public Mono<ObjectResponse<SettJobBillVo>> deleteSettJobBill(String billNo) {
        IotAssert.isTrue(StringUtils.isNotBlank(billNo), "结算单编号不能为空");
        return settJobBillFeignClient.deleteSettJobBill(billNo);
    }

    public Mono<ObjectResponse<SettJobBillDetailVo>> getSettJobBill(@NotNull String billNo) {
        IotAssert.isTrue(StringUtils.isNotBlank(billNo), "结算单编号不能为空");
        return settJobBillFeignClient.getSettJobBill(billNo);
    }
}
