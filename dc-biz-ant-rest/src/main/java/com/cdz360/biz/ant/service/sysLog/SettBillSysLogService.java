package com.cdz360.biz.ant.service.sysLog;

import com.cdz360.base.model.base.vo.KvAny;
import com.cdz360.biz.auth.user.type.LogOpType;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;

/**
 * 结算单相关系统操作日志
 */
@Slf4j
@Service
public class SettBillSysLogService {

    @Autowired
    private BaseSysLogService sysUserLogService;

    /**
     * 删除结算单日志
     */
    public void delSettJobBillLog(String billNo, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.DISABLE,
            List.of(KvAny.of("结算单号", billNo)),
            request);
    }

    /**
     * 编辑结算单日志
     */
    public void editSettJobBillLog(String billNo, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
            List.of(KvAny.of("结算单号", billNo)),
            request);
    }

}
