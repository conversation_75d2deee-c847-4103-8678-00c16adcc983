package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.domain.ChargerOrder;
import com.cdz360.biz.ant.domain.request.PlugDetailRequest;
import com.cdz360.biz.ant.domain.vo.ChargerOrderFinishVo;
import com.cdz360.biz.ant.domain.vo.PlugDetailVo;
import com.cdz360.biz.model.cus.wallet.param.CreateDepositOrderParam;
import com.cdz360.biz.model.trading.order.po.ChargerOrderTimeDivision;
import com.cdz360.biz.model.trading.order.po.PayBillPo;
import com.chargerlinkcar.framework.common.domain.NoCardPayAccountInfo;
import com.chargerlinkcar.framework.common.domain.OrderTimeDivision;
import com.chargerlinkcar.framework.common.domain.OrderTimeDivisionParam;
import com.chargerlinkcar.framework.common.domain.invoice.DcInvoiceOrderVo;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.domain.param.OrderStopBiParam;
import com.chargerlinkcar.framework.common.domain.pay.PaySign;
import com.chargerlinkcar.framework.common.domain.request.ExpectLimitSocReq;
import com.chargerlinkcar.framework.common.domain.vo.OrderInfoElec;
import com.chargerlinkcar.framework.common.domain.vo.OrderStopBiVo;
import com.chargerlinkcar.framework.common.domain.vo.UpdateOrderVo;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 用户feign
 *
 * <AUTHOR> Created by 2018/11/19.
 */
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_TRADING, fallbackFactory = TradingFeignClientHystrixFactory.class)
public interface TradingFeignClient {

//    /**
//     * 获取该商户列表下的客户 订单详情
//     *
//     * @param commId     操作人商户ID
//     * @param orderNo    订单No
//     * @param commIdList 商户id列表
//     * @return
//     */
//    @RequestMapping(value = "/api/order/queryOrderDetail", method = RequestMethod.GET)
//    ObjectResponse<ChargerOrderWithBLOBs> queryOrderDetail(@RequestParam("orderNo") String orderNo,
//                                                           @RequestParam(value = "commIdList", required = false) List<Long> commIdList,
//                                                           @RequestParam(value = "commIdChain", required = false) String commIdChain,
//                                                           @RequestParam(value = "commId") @Nullable Long commId);

    @PostMapping("/api/order/getStopOrderList")
    ListResponse<OrderStopBiVo> getOrderStopSummaryList(@RequestBody OrderStopBiParam param);


    /**
     * 分页查询枪头订单列表
     *
     * @param commIdList        当前商户及子商户列表
     * @param boxOutFactoryCode 桩号
     * @param beginTime
     * @param endTime
     * @param status
     * @param channelId
     * @return
     */
    @RequestMapping(value = "/api/order/selectChargerOrderListByConnectId")
    ListResponse<ChargerOrder> selectChargerOrderListByConnectId(
        @RequestParam(value = "commIdList", required = false) List<Long> commIdList,
        @RequestParam("_index") Integer _index,
        @RequestParam("_size") Integer _size,
        @RequestParam("boxOutFactoryCode") String boxOutFactoryCode,
        @RequestParam("connectorId") String connectorId,
        @RequestParam("beginTime") String beginTime,
        @RequestParam("endTime") String endTime,
        @RequestParam("status") String status,
        @RequestParam("channelId") Integer channelId);


    /**
     * 根据站点统计昨天、近7天、近30天的充电次数、电量、金额
     *
     * @param siteId
     * @return
     */
    @RequestMapping(value = "/api/orderDataReport/getSiteSurvey", method = RequestMethod.POST)
    ListResponse<JsonNode> getSiteSurvey(@RequestParam("siteId") String siteId,
        @RequestParam("token") String token);


    /**
     * 查询分时订单列表
     *
     * @param orderNo
     */
    @RequestMapping(value = "/api/order/queryOrderTimeDivisionList")
    ListResponse<ChargerOrderTimeDivision> queryOrderTimeDivisionList(
        @RequestParam("orderNo") String orderNo);


    @PostMapping("/api/order/updateOrder")
    BaseResponse updateOrder(@RequestBody UpdateOrderVo orderVo);

    /**
     * 批量获取mongoDB订单电力度数相关数据
     *
     * @param orderNoList 订单号列表
     * @return
     */
    @GetMapping("/dc/order/orderInfoElec")
    ListResponse<OrderInfoElec> orderInfoElec(
        @RequestParam("orderNoList") List<String> orderNoList);

    /**
     * 根据订单编号获取订单相关信息
     *
     * @param orderNo
     * @return
     */
    @GetMapping(value = "/api/order/getFinishedOrderDetail")
    ObjectResponse<ChargerOrderFinishVo> getFinishedOrderDetail(
        @RequestParam("orderNo") String orderNo);

    /**
     * 枪头详情
     *
     * @return
     */
    @PostMapping("/dc/plug/detail")
    ListResponse<PlugDetailVo> getPlugDetail(@RequestBody PlugDetailRequest plugDetailRequest);

//    /**
//     * 根据订单编号获取订单相关信息
//     *
//     * @param orderNo
//     * @return
//     */
//    @GetMapping(value = "/dc/order/samplingInfo")
//    ListResponse<ChargerDetailVo> queryOrderSamplingInfo(@RequestParam("orderNo") String orderNo);

    /**
     * 筛选出有未结算订单的卡(返回逻辑卡号)
     *
     * @param param 物理卡号列表/逻辑卡号列表选传一个
     * @return cardNo 逻辑卡号
     */
    @PostMapping("/api/order/getNoSettlementCard")
    ListResponse<String> getNoSettlementCard(@RequestBody ChargerOrderParam param);

    /**
     * 筛选出有未结算订单的VIN
     *
     * @param param VIN码列表必传
     * @return VIN码
     */
    @PostMapping("/api/order/getNoSettlementVin")
    ListResponse<String> getNoSettlementVin(@RequestBody ChargerOrderParam param);

    @PostMapping("/api/pay/refundForAbnormal")
    ObjectResponse<String> refundForAbnormal(@RequestParam("orderNo") String orderNo);

    @GetMapping("/api/order/getUnfinishOrderBysiteId")
    ListResponse<ChargerOrder> getUnfinishOrderBysiteId(@RequestParam("siteId") String siteId);

    @GetMapping("/api/order/getUnfinishOrderByCorpId")
    ListResponse<ChargerOrder> getUnfinishOrderByCorpId(
        @RequestParam(value = "corpId") Long corpId);

    /**
     * 订单分时数据统计
     *
     * @param req
     * @return
     */
    @PostMapping("/api/order/orderTimeDivisionData")
    ListResponse<OrderTimeDivision> orderTimeDivisionData(@RequestBody OrderTimeDivisionParam req);

    // FIXME: 后续接口需要调整到 data-core

    @PostMapping(value = "/api/invoiced/chargerOrders")
    ListResponse<DcInvoiceOrderVo> queryChargerOrderList(
        @RequestHeader("token") String token,
        @RequestParam(value = "username", required = false) String username,
        @RequestParam(value = "status", required = false) Integer status,
        @RequestParam(value = "customerId", required = false) Long customerId,
        @RequestParam(value = "invoicedId", required = false) Long invoicedId,
        @RequestParam(value = "index", required = true) Integer index,
        @RequestParam(value = "size", required = true) Integer size,
        @RequestParam(value = "createTime", required = false) String createTime,
        @RequestParam(value = "endTime", required = false) String endTime);

    /**
     * 根据场站id结合场站配置的无卡充电订单结算账号，尝试结算该场站下所有未支付订单
     *
     * @param siteId
     * @return
     */
    @PostMapping(value = "/api/order/payNoCardOrderBySiteId")
    BaseResponse payNoCardOrderBySiteId(@RequestParam(value = "siteId") String siteId,
        @RequestBody NoCardPayAccountInfo accountInfo);

    /**
     * 充值后逻辑处理
     *
     * @param po
     * @return
     */
    @PostMapping(value = "/api/balance/postDeposit")
    BaseResponse postDeposit(@RequestBody PayBillPo po);

    @PostMapping(value = "/api/balance/createDepositOrder")
    ObjectResponse<PaySign> createDepositOrder(@RequestBody CreateDepositOrderParam param);

    @PostMapping(value = "/soc/limitSoc/changeExpectLimitSoc")
    BaseResponse changeExpectLimitSoc(@RequestBody ExpectLimitSocReq req);
}
