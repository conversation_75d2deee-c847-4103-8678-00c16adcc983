package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.cus.vin.param.VinSearchParam;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.chargerlinkcar.framework.common.domain.vo.VinDto;
import com.chargerlinkcar.framework.common.domain.vo.VinDto2;
import com.chargerlinkcar.framework.common.domain.vo.VinParam;
import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 *
 * @since 2019/5/15 11:17
 * <AUTHOR>
 */
@Slf4j
@Component
public class VinFeignClientHystrixFactory implements FallbackFactory<VinFeignClient> {

    @Override
    public VinFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER,
            throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER,
            throwable.getStackTrace());

        return new VinFeignClient() {
            @Override
            public BaseResponse create(VinParam vinParam) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse delete(Long id) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse update(VinParam vinParam) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<VinDto> getById(Long id) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<VinDto> select(VinSearchParam vinSearchParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<ExcelPosition> exportVinList(VinSearchParam vinSearchParam) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<VinDto> selectByVin(String vin, Long commId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<VinDto> selectVinOnCorp(VinParam vinParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<ExcelPosition> exportVinListOnCorp(VinParam vinParam) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse createOnCorp(VinParam vinParam) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse batchCreateOnCorp(List<VinParam> vinParamList) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse updateByVinAndCommAndCorpOnCorp(List<VinParam> vinParamList) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<VinDto> getByIdList(List<Long> idList) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<VinDto2> getVinDto2ById(Long vinId, String commIdChain) {
                return RestUtils.serverBusy4ObjectResponse();
            }
        };
    }
}