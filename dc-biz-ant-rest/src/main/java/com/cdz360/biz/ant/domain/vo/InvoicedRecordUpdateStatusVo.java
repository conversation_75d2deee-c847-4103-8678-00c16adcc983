package com.cdz360.biz.ant.domain.vo;

import com.cdz360.biz.model.invoice.type.InvoicedStatus;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * A DTO for the InvoicedRecord entity.
 */
@Data
public class InvoicedRecordUpdateStatusVo implements Serializable {
    @NotNull
    private Long id;

    @NotNull
    private InvoicedStatus status;

    // 更改备注
    private String desc;

}
