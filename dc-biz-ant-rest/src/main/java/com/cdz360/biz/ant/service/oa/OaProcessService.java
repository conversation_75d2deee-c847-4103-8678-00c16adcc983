package com.cdz360.biz.ant.service.oa;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.feign.reactor.DataCoreSmartPriceFeignClient;
import com.cdz360.biz.ant.service.oa.process.StartProcessStrategy;
import com.cdz360.biz.ant.service.oa.process.StartProcessStrategyFactory;
import com.cdz360.biz.ant.utils.RedisUtil;
import com.cdz360.biz.model.oa.vo.OaExcel.BatchStartResult;
import com.cdz360.biz.oa.param.OaStartProcessParam;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SerializationUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class OaProcessService {

    private static final String OA_BATCH_START_RESULT_PREFIX = "obsr:";
    private static final long OA_BATCH_START_RESULT_TIMEOUT = 10;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private StartProcessStrategyFactory startProcessStrategyFactory;

    @Autowired
    private DataCoreSmartPriceFeignClient dataCoreSmartPriceFeignClient;

    private static String formatKey(String opId, String processDefinitionKey) {
        return OA_BATCH_START_RESULT_PREFIX + opId + ":" + processDefinitionKey;
    }

    private void recordResult(String key, BatchStartResult result) {
        redisUtil.rightPushList(key, JsonUtils.toJsonString(result));
        redisUtil.expire(key, OA_BATCH_START_RESULT_TIMEOUT, TimeUnit.MINUTES);
    }

    public List<BatchStartResult> viewStartResult(@Nullable Integer acquiredNum,
        String processDefinitionKey,
        String opId) {
        String key = formatKey(opId, processDefinitionKey);
        int start = 0;
        if (acquiredNum != null) {
            Long length = redisUtil.length(key);
            if (acquiredNum >= length) {
                return List.of();
            }
            start = acquiredNum;
        }
        int end = start + 9;
        return redisUtil.range(key, start, end).stream()
            .map(e -> JsonUtils.fromJson(e, BatchStartResult.class))
            .collect(Collectors.toList());
    }

    @Async
    public void oaBatchStartProcess(OaStartProcessParam param) {
        IotAssert.isNotBlank(param.getProcessDefinitionKey(), "请提供流程定义KEY");
        // 重置批量启动结果记录
        String key = formatKey(param.getOpId(), param.getProcessDefinitionKey());
        redisUtil.del(key);

        final List<Map<String, Object>> dataList = param.getDataList();

        StartProcessStrategy strategy = startProcessStrategyFactory.getStrategy(
            param.getProcessDefinitionKey());

        for (int i = 0; i < dataList.size(); i++) {
            BatchStartResult startResult = new BatchStartResult();
            startResult.setRow(i);
            try {
                OaStartProcessParam tempReq = SerializationUtils.clone(param);
                tempReq.setDataList(null)
                    .setFromBatchCreateReq(Boolean.TRUE);
                strategy.paramConversion(dataList.get(i))
                    .map(e -> {
                        tempReq.setData(e);
                        return tempReq;
                    })
                    .flatMap(this::oaStartProcess)
                    .doOnNext(FeignResponseValidate::check)
                    .doOnNext(e -> this.recordResult(key, startResult))
                    .subscribe();
            } catch (DcException ex) {
                log.error("oaBatchStartProcess key: {}, idx: {}, DcException: {}", key, i,
                    ex.getMessage(), ex);
                startResult.setError(ex.getMessage());
                this.recordResult(key, startResult);
            } catch (Exception ex) {
                log.error("oaBatchStartProcess key: {}, idx: {}, Exception: {}", key, i,
                    ex.getMessage(), ex);
                startResult.setError("系统错误");
                this.recordResult(key, startResult);
            }
        }
    }

    public Mono<ObjectResponse<String>> oaStartProcess(OaStartProcessParam param) {
        IotAssert.isNotBlank(param.getProcessDefinitionKey(), "请提供流程定义KEY");
        IotAssert.isTrue(null != param.getData() &&
            param.getData().entrySet().size() > 0, "流程表单数据不能为空");
        return Mono.just(param)
            .flatMap(p -> {
                StartProcessStrategy strategy = startProcessStrategyFactory.getStrategy(
                    param.getProcessDefinitionKey());
                IotAssert.isNotNull(strategy, "该流程未配置");
                String procInstId = strategy.start(p).block();
                if (checkDefinitionKey(param.getProcessDefinitionKey())) {
                    // 通知任务去改变
                    dataCoreSmartPriceFeignClient.generateSmartPrice(param, procInstId).subscribe();
                }
                return Mono.just(procInstId);
            })
            .map(RestUtils::buildObjectResponse);
    }

    // 校验其中一个是不是智能模板
    private boolean checkDefinitionKey(String definitionKey) {
        if ("charge_fee".equals(definitionKey)) {
            return true;
        }
        return false;
    }

    public Mono<ObjectResponse<String>> oaProcessResubmit(OaStartProcessParam param) {
        IotAssert.isNotBlank(param.getProcessDefinitionKey(), "请提供流程定义KEY");
        IotAssert.isTrue(null != param.getData() &&
            param.getData().entrySet().size() > 0, "流程表单数据不能为空");
        return Mono.just(param)
            .flatMap(p -> {
                StartProcessStrategy strategy = startProcessStrategyFactory.getStrategy(
                    param.getProcessDefinitionKey());
                IotAssert.isNotNull(strategy, "该流程未配置");
                String procInstId = strategy.resubmit(p).block();
                if (checkDefinitionKey(param.getProcessDefinitionKey())) {
                    // 通知任务去改变
                    dataCoreSmartPriceFeignClient.generateSmartPrice(param, procInstId).subscribe();
                }
                return Mono.just(procInstId);
            })
            .map(RestUtils::buildObjectResponse);
    }
}
