package com.cdz360.biz.ant.service.srs;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.domain.vo.RadiationSampling;
import com.cdz360.biz.ant.domain.vo.RedisPvRtData;
import com.cdz360.biz.model.trading.iot.vo.RedisSrsRtData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import reactor.core.publisher.Flux;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RedisSrsRtDataService {
    private static final DateTimeFormatter TIME_POINT_FORMATTER = DateTimeFormatter.ofPattern("HH:mm");
    private final static DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    private static final String PRE_REDIS_KEY = "srs:";

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 获取辐射仪运行时数据数量
     *
     * @param dno 辐射仪编号
     * @param date 日期
     * @return
     */
    public Long countGtiRtData(String dno, LocalDate date) {
        String key = formatKey(dno, date);
        return redisTemplate.opsForList().size(key);
    }

    /**
     * 获取辐射仪运行时数据
     *
     * @param dno 辐射仪编号
     * @param date 日期
     * @param start
     * @param end
     * @return
     */
    public List<RedisSrsRtData> findSrsRtData(String dno, LocalDate date, long start, long end) {
        String key = formatKey(dno, date);
        return findSrsRtData(key, start, end);
    }

    /**
     * 获取辐射仪运行时数据
     *
     * @param start
     * @param end
     * @return
     */
    public List<RedisSrsRtData> findSrsRtData(String key, long start, long end) {
        Long size = redisTemplate.opsForList().size(key);
        if (null == size) {
            return List.of();
        }

        if (end > size) {
            end = -1;
        }

        return redisTemplate.opsForList()
                .range(key, start, end)
                .stream()
                .map(value -> JsonUtils.fromJson(value, RedisSrsRtData.class))
                .collect(Collectors.toList());
    }

    /**
     * 从redis中采样功率
     *
     * @param dnoList 辐射仪编号
     * @param date 日期
     * @param dis  采样间距
     * @return
     */
    public Flux<RadiationSampling> dayPowerSampling4Redis(
            List<String> dnoList, LocalDate date, int dis) {
        final List<LocalDateTime> times = splitDay(date, dis);
        log.debug("时段: time = {}", times.size());
        return this.dayRadiationSampling4Redis(dnoList, date, times);
    }

    /**
     * 从redis中采样功率
     *
     * @param dnoList 辐射仪编号
     * @param date    日期
     * @param times   采样时间点
     * @return
     */
    public Flux<RadiationSampling> dayRadiationSampling4Redis(
            List<String> dnoList, LocalDate date, final List<LocalDateTime> times) {
        if (CollectionUtils.isEmpty(dnoList)) {
            return Flux.fromIterable(times)
                    .map(time -> new RadiationSampling()
                            .setTime(time.format(TIME_POINT_FORMATTER))
                            .setRadiation(0));
        }

        return Flux.fromIterable(dnoList)
                .flatMap(dno -> this.dayRadiationSampling4Redis(formatKey(dno, date), times))
                .collectList()
                .map(list -> {
                    Map<String, Integer> collect = list.stream().collect(Collectors.groupingBy(
                            RadiationSampling::getTime,
                            Collectors.summingInt(RadiationSampling::getRadiation)));

                    List<RadiationSampling> result = new ArrayList<>();
                    for(String time: collect.keySet()) {
                        result.add(new RadiationSampling()
                                .setTime(time)
                                .setRadiation(collect.get(time)));
                    }
                    return result.stream()
                            .sorted(Comparator.comparing(RadiationSampling::getTime));
                })
                .flatMapMany(Flux::fromStream);
    }

    private Flux<RadiationSampling> dayRadiationSampling4Redis(String key, List<LocalDateTime> times) {
        List<RadiationSampling> samplingList = new ArrayList<>();
        try {
            boolean hasLine = true;
            RedisSrsRtData last = null;
            int start = 0;
            final int size = 100;

            List<RedisSrsRtData> dataList = this.findSrsRtData(key, start, size);
            if (CollectionUtils.isEmpty(dataList)) {
                hasLine = false;
            }

            int dataSize = dataList.size();
            int idx = 0;
            for(LocalDateTime time: times) {
                if (null != last) {
                    LocalDateTime temp = last.getTime()
                            .withSecond(0)
                            .withNano(0);
                    if (time.isBefore(temp)) {
                        addZeroSampling(samplingList, time);
                        continue;
                    }
                }

                if (hasLine) {
                    while (true) {
                        if (idx == dataSize) {
                            start = start + size;
                            dataList = this.findSrsRtData(key, start, start + 100);
                            if (CollectionUtils.isEmpty(dataList)) {
                                hasLine = false;
                                break;
                            }

                            dataSize = dataList.size();
                            idx = 0;
                        }

                        RedisSrsRtData rtData = dataList.get(idx);
                        if (null == rtData || rtData.getTime() == null) {
                            continue;
                        }

                        LocalDateTime temp = rtData.getTime()
                                .withSecond(0)
                                .withNano(0);

                        if (temp.isBefore(time)) {
                            idx = idx + 1;
                            continue;
                        } else if (temp.isEqual(time)) {
                            samplingList.add(new RadiationSampling()
                                    .setTime(time.format(TIME_POINT_FORMATTER))
                                    .setRadiation(this.radiationFormat(rtData.getValue())));
                            idx = idx + 1;
                            last = rtData;
                            break;
                        } else if (temp.isAfter(time)) {
                            if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(samplingList)
                                    && samplingList.get(samplingList.size() - 1).getRadiation() != null
                                    && samplingList.get(samplingList.size() - 1).getRadiation() > 0) {
                                Integer lastV = last != null && last.getValue() != null
                                        ? last.getValue() : 0;
                                Integer value = NumberUtils.sum(lastV, rtData.getValue()) / 2;
                                samplingList.add(new RadiationSampling()
                                        .setTime(time.format(TIME_POINT_FORMATTER))
                                        .setRadiation(this.radiationFormat(value)));
                            } else {
                                addZeroSampling(samplingList, time);
                            }
                            break;
                        } else {
                            idx = idx + 1;
                        }
                        rtData = null;
                    }
                }

                if (!hasLine) {
                    addZeroSampling(samplingList, time);
                }
            }
        } catch (Exception e) {
            // nothing
            log.error("err = {}", e.getMessage(), e);
        }
        return Flux.fromIterable(samplingList);
    }

    private Integer radiationFormat(Integer value) {
        return value != null ? value : 0;
    }

    private static List<LocalDateTime> splitDay(LocalDate date, int dis) {
        List<LocalDateTime> times = new ArrayList<>();
        LocalDateTime start = date.atStartOfDay();
        LocalDateTime end = date.plusDays(1).atStartOfDay();
        LocalDateTime now = LocalDateTime.now();
        while (start.isBefore(end) && start.isBefore(now)) {
            times.add(start);
            start = start.plusMinutes(dis);
        }
        return times;
    }

    private static void addZeroSampling(List<RadiationSampling> list, LocalDateTime time) {
        list.add(new RadiationSampling()
                .setTime(time.format(TIME_POINT_FORMATTER))
                .setRadiation(0));
    }

    /**
     * 获取最近一条运行时数据
     *
     * @param dno
     * @param date
     * @return
     */
    public RedisPvRtData latestRtData(String dno, LocalDate date) {
        String key = formatKey(dno, date);
        return this.latestRtData4RedisKey(key);
    }

    /**
     * 获取最近一条运行时数据
     *
     * @param key
     * @return
     */
    public RedisPvRtData latestRtData4RedisKey(String key) {
        Long size = redisTemplate.opsForList().size(key);
        if (null == size) {
            return null;
        }

        String value = redisTemplate.opsForList()
                .index(key, size - 1);
        if (StringUtils.isBlank(value)) {
            return null;
        }
        return JsonUtils.fromJson(value, RedisPvRtData.class);
    }

    private static String formatKey(String dno, LocalDate date) {
        return PRE_REDIS_KEY + dno + ":" + date.format(DATE_FORMATTER);
    }

    private static String formatKey(String dno, String date) {
        return PRE_REDIS_KEY + dno + ":" + date;
    }
}
