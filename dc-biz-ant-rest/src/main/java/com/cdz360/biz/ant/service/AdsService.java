package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.feign.DataCoreFeignClient;
import com.cdz360.biz.model.ads.param.CreateAdsParam;
import com.cdz360.biz.model.ads.param.ListAdsParam;
import com.cdz360.biz.model.ads.param.UpdateAdsParam;
import com.cdz360.biz.model.ads.vo.AdsVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * AdsService
 * <AUTHOR>
 *
 * @since 2025/2/12
 */
@Slf4j
@Service
public class AdsService {

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    public BaseResponse create(CreateAdsParam req) {
        return dataCoreFeignClient.createAds(req);
    }

    public BaseResponse update(UpdateAdsParam req) {
        return dataCoreFeignClient.updateAds(req);
    }

    public BaseResponse abortAds(Long id) {
        return dataCoreFeignClient.abortAds(id);
    }

    public BaseResponse activeAds(Long id) {
        return dataCoreFeignClient.activeAds(id);
    }

    public ObjectResponse<AdsVo> getAdsDetail(Long id) {
        ObjectResponse<AdsVo> ret = dataCoreFeignClient.getAdsDetail(id);
        return ret;
    }

    public ListResponse<AdsVo> list(ListAdsParam req) {
        ListResponse<AdsVo> ret = dataCoreFeignClient.listAds(req);
        return ret;
    }
}
