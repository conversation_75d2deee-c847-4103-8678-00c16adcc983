package com.cdz360.biz.ant.domain.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class ChargerOrderFinishVo implements Serializable {

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 订单金额
     */
    private long orderPrice;

    /**
     * 客户实付金额
     */
    private long actualPrice;

    /**
     * 服务费
     */
    private long servicePrice;

    /**
     * 电费价格
     */
    private long elecPrice;

    /**
     * 订单来源类型,0:微信渠道;1:APP在线发起充电;2:APP蓝牙发起充电:3:刷卡充电:4:桩上报离线数据5定时充电6手动开启7曹操专车开启
     */
    private Integer channelId;

    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 交直流类型**0-交流 1-直流**
     */
    private Integer currentType;

    /**
     * 订单电量
     */
    private long orderElectricity;

    //TODO 终端、终端编号、充电时长
    /**
     * 枪完整编号 如：010+12271003105+01
     */
    private String fullPlugNo;
    /**
     * 枪头名称
     */
    private String chargerName;

    /**
     * 状态,-500：硬件故障；-40：上报超时；-35：断连超时；-30关电超时；-29：结束充电失败；-20：链接超时；-10：打开电闸超时；-7：开启充电失败，PIN可能错误；-5:系统繁忙；0：订单未激活；100：电闸打开；200：充电中；250：关电闸，充电停止；300：充电完成；800：订单结束；1000：用户已支付；2000：钱已到账
     */
    private Integer status;

    /**
     * 充电开始时间
     */
    private Integer chargeStartTime;

    /**
     * 充电结束时间
     */
    private Integer chargeEndTime;

    /**
     * vin码
     */
    private String vin;

    /**
     * 车牌号
     */
    private String carNo;

    /**
     * 桩名称
     */
    private String evseName;

    // 物理卡号
    private String cardChipNo;


}
