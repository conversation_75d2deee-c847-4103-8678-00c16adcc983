package com.cdz360.biz.ant.rest.site;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.domain.request.SiteGeoBiParam;
import com.cdz360.biz.ant.service.site.SiteService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.bi.dashboard.SiteAndPlugBiVo;
import com.cdz360.biz.model.common.constant.DcBizConstants;
import com.cdz360.biz.model.site.param.SiteAndPlugBiParam;
import com.cdz360.biz.model.trading.comm.dto.CommSiteEvseCount;
import com.cdz360.biz.model.trading.site.dto.CitySiteNumDto;
import com.cdz360.biz.model.trading.site.dto.DistrictSiteNumDto;
import com.cdz360.biz.model.trading.site.dto.ProvinceSiteNumDto;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.HashSet;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RequestMapping("/api/site/gis")
@RestController
@Tag(name = "站点地图相关接口")
public class SiteGisRest {

    @Autowired
    private SiteService siteService;

    @Operation(summary = "场站地图-按省统计场站数量")
    @PostMapping("/getProvinceSiteNumList")
    public ListResponse<ProvinceSiteNumDto> getProvinceSiteNumList(ServerHttpRequest request,
        @RequestBody SiteGeoBiParam param) {
        log.debug(LoggerHelper2.formatEnterLog(request) + "param = {}", param);
        ListResponse<ProvinceSiteNumDto> res;
        List<String> sysUserGids = AntRestUtils.getSysUserGids(request);
        List<String> siteIdList = param.getSiteIdList();
        List<String> reqGids = param.getGids();

        if (CollectionUtils.isNotEmpty(sysUserGids)) {

            if (CollectionUtils.isNotEmpty(reqGids)) {
                if (!new HashSet<>(sysUserGids).containsAll(reqGids)) {
                    // 若无权限则返回空数据
                    return RestUtils.buildListResponse(null);
                }
                sysUserGids = reqGids;
            }
            res = this.siteService.getProvinceSiteNumList(null, siteIdList, sysUserGids);
        } else if (NumberUtils.equals(AntRestUtils.getCommId(request),
            DcBizConstants.superTopCommId)) {
            res = this.siteService.getProvinceSiteNumList(null, siteIdList, null);
        } else {
            res = this.siteService.getProvinceSiteNumList(AntRestUtils.getCommIdChain(request),
                siteIdList,
                null);
        }
        return res;
    }

    @Operation(summary = "场站地图-按城市统计场站数量")
    @PostMapping("/getCitySiteNumList")
    public ListResponse<CitySiteNumDto> getCitySiteNumList(ServerHttpRequest request,
        @RequestBody SiteGeoBiParam param) {
        log.debug(LoggerHelper2.formatEnterLog(request) + "param = {}", param);
        ListResponse<CitySiteNumDto> res;
        List<String> sysUserGids = AntRestUtils.getSysUserGids(request);
        List<String> siteIdList = param.getSiteIdList();
        List<String> reqGids = param.getGids();

        if (CollectionUtils.isNotEmpty(sysUserGids)) {

            if (CollectionUtils.isNotEmpty(reqGids)) {
                if (!new HashSet<>(sysUserGids).containsAll(reqGids)) {
                    // 若无权限则返回空数据
                    return RestUtils.buildListResponse(null);
                }
                sysUserGids = reqGids;
            }
            res = this.siteService.getCitySiteNumList(null, siteIdList, sysUserGids);
        } else if (NumberUtils.equals(AntRestUtils.getCommId(request),
            DcBizConstants.superTopCommId)) {
            res = this.siteService.getCitySiteNumList(null, siteIdList, null);
        } else {
            res = this.siteService.getCitySiteNumList(AntRestUtils.getCommIdChain(request),
                siteIdList,
                null);
        }
        return res;
    }

    @Operation(summary = "场站地图-按地区统计场站数量")
    @PostMapping("/getDistrictSiteNumList")
    public Mono<ListResponse<DistrictSiteNumDto>> getDistrictSiteNumList(ServerHttpRequest request,
        @RequestBody SiteGeoBiParam param) {
        log.debug(LoggerHelper2.formatEnterLog(request) + "param = {}", param);
        Mono<ListResponse<DistrictSiteNumDto>> res;
        List<String> sysUserGids = AntRestUtils.getSysUserGids(request);
        List<String> siteIdList = param.getSiteIdList();
        List<String> reqGids = param.getGids();

        if (CollectionUtils.isNotEmpty(sysUserGids)) {

            if (CollectionUtils.isNotEmpty(reqGids)) {
                if (!new HashSet<>(sysUserGids).containsAll(reqGids)) {
                    // 若无权限则返回空数据
                    return Mono.just(RestUtils.buildListResponse(null));
                }
                sysUserGids = reqGids;
            }
            res = this.siteService.getDistrictSiteNumList(null, siteIdList, sysUserGids);
        } else if (NumberUtils.equals(AntRestUtils.getCommId(request),
            DcBizConstants.superTopCommId)) {
            res = this.siteService.getDistrictSiteNumList(null, siteIdList, null);
        } else {
            res = this.siteService.getDistrictSiteNumList(AntRestUtils.getCommIdChain(request),
                siteIdList,
                null);
        }
        return res;
    }

    @Operation(summary = "场站地图-获取商户场站/桩/枪数量统计")
    @PostMapping("/getCommSiteEvseCount")
    public ObjectResponse<CommSiteEvseCount> getCommSiteEvseCount(ServerHttpRequest request,
        @RequestBody SiteGeoBiParam param) {
        log.debug(LoggerHelper2.formatEnterLog(request) + "param = {}", param);
        ObjectResponse<CommSiteEvseCount> res;
        List<String> sysUserGids = AntRestUtils.getSysUserGids(request);
        List<String> siteIdList = param.getSiteIdList();
        List<String> reqGids = param.getGids();

        if (CollectionUtils.isNotEmpty(sysUserGids)) {

            if (CollectionUtils.isNotEmpty(reqGids)) {
                if (!new HashSet<>(sysUserGids).containsAll(reqGids)) {
                    // 若无权限则返回空数据
                    return RestUtils.buildObjectResponse(null);
                }
                sysUserGids = reqGids;
            }
            res = this.siteService.getCommSiteEvseCount(null, siteIdList, sysUserGids);
        } else if (NumberUtils.equals(AntRestUtils.getCommId(request),
            DcBizConstants.superTopCommId)) {
            res = this.siteService.getCommSiteEvseCount(null, siteIdList, reqGids);
        } else {
            res = this.siteService.getCommSiteEvseCount(AntRestUtils.getCommIdChain(request),
                siteIdList,
                null);
        }
        return res;
    }

    @Operation(summary = "场站/桩/枪状态统计数据")
    @PostMapping(value = "/getSiteAndPlugBiVo")
    public ObjectResponse<SiteAndPlugBiVo> getSiteAndPlugBiVo(ServerHttpRequest request,
        @RequestBody SiteAndPlugBiParam param) {
        log.debug(LoggerHelper2.formatEnterLog(request) + "param = {}", param);
        List<String> sysUserGids = AntRestUtils.getSysUserGids(request);
//        List<String> siteIdList = param.getSiteIdList();
        List<String> reqGids = param.getGids();

        if (CollectionUtils.isNotEmpty(sysUserGids)) {

            if (CollectionUtils.isNotEmpty(reqGids)) {
                if (!new HashSet<>(sysUserGids).containsAll(reqGids)) {
                    // 若无权限则返回空数据
                    return RestUtils.buildObjectResponse(null);
                }
            } else {
                param.setGids(sysUserGids);
            }
        } else {
            param.setIdChain(AntRestUtils.getCommIdChain(request));
        }
        return siteService.getSiteAndPlugBiVo(param);
    }

}
