package com.cdz360.biz.ant.service;


import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.biz.ant.domain.vo.GroupUserListVo;
import com.cdz360.biz.ant.domain.vo.UserGroupVo;
import com.cdz360.biz.ant.feign.AntUserFeignClient;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class UserGroupService //implements IUserGroupService
{

    @Autowired
    private AntUserFeignClient userFeignClient;


    /**
     * 根据 客户分组名称 查询 客户分组列表
     *
     * @param commIdList 当前商户及子商户id列表
     * @param page
     * @param groupName  分组名称
     * @return
     */

    public ListResponse<UserGroupVo> queryUserGroupList(List<Long> commIdList,
        OldPageParam page, String groupName) {

        Integer _index = page.getPageNum();

        Integer _size = page.getPageSize();

        ListResponse<UserGroupVo> jsonObjectRes = userFeignClient.queryUserGroupList(_index, _size,
            groupName, commIdList);
        return jsonObjectRes;
//        if (jsonObjectRes == null || jsonObjectRes.get("status") == null) {
//            throw new DcServiceException("获取失败");
//        }
//
//        if (jsonObjectRes.getInteger("status") != ResultConstant.RES_SUCCESS_CODE) {
//
//            if (jsonObjectRes.get("error") == null) {
//                throw new DcServiceException("获取失败");
//            }else{
//                throw new DcServiceException(jsonObjectRes.get("error").toString());
//            }
//        }
//
//        PaginationEntity<UserGroupVo> pageUserGroups = JSON.parseObject(JsonUtils.toJsonString(jsonObjectRes.get("data")),
//                new TypeReference<PaginationEntity<UserGroupVo>>() {
//        });
//
//        ListResponse<UserGroupVo> res = new ListResponse<>(pageUserGroups.getRows(), pageUserGroups.getTotal());
//
//        return res;
    }

    /**
     * 根据 客户分组Id 查询 客户分组详情
     *
     * @param commIdList  当前商户及子商户id列表
     * @param userGroupId 客户分组Id
     * @return
     */

    public ObjectResponse<GroupUserListVo> queryUserGroupById(List<Long> commIdList,
        Long userGroupId) {

        if (userGroupId == null) {
            throw new DcServiceException("参数出错");
        }

        ObjectResponse<GroupUserListVo> jsonObjectRes = userFeignClient.queryUserGroupById(
            userGroupId, commIdList);

        if (jsonObjectRes.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
            throw new DcServiceException("获取失败");
        }
        return jsonObjectRes;

//        if (jsonObjectRes.getInteger("status") != ResultConstant.RES_SUCCESS_CODE) {
//
//            if (jsonObjectRes.get("error") == null) {
//                throw new DcServiceException("获取失败");
//            }else{
//                throw new DcServiceException(jsonObjectRes.get("error").toString());
//            }
//        }

//        GroupUserListVo groupUserListVo = jsonObjectRes.getData();
//
//
        //        return new ObjectResponse<>(groupUserListVo);

    }


    /**
     * 修改客户分组
     *
     * @param commId          当前商户id
     * @param groupId         分组id
     * @param groupName       分组名称
     * @param groupDescrition 分组描述
     * @param userIds         客户id列表
     * @return
     */

    public ObjectResponse updateUserGroup(Long commId, Long groupId, String groupName,
        String groupDescrition, List<Long> userIds) {

        /*if (StringUtils.isBlank(groupName)) {
            throw new DcServiceException("客户组名称不能空");
        }*/

        if (groupId == null) {
            throw new DcArgumentException("参数错误");
        }

        BaseResponse jsonObjectRes = userFeignClient.updateUserGroup(commId, groupId, groupName,
            groupDescrition, userIds);

        if (jsonObjectRes.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
            throw new DcServiceException("修改失败");
        }

//        if (jsonObjectRes.getInteger("status") != ResultConstant.RES_SUCCESS_CODE) {
//
//            if (jsonObjectRes.get("error") == null) {
//                throw new DcServiceException("修改失败");
//            }else{
//                throw new DcServiceException(jsonObjectRes.get("error").toString());
//            }
//        }

        //GroupUserListVo groupUserListVo = JSONObject.parseObject(JsonUtils.toJsonString(jsonObjectRes.get("data")), GroupUserListVo.class);

        return new ObjectResponse<>("修改成功");


    }


    /**
     * 添加客户分组
     *
     * @param commId          当前商户id
     * @param groupName       分组名称
     * @param groupDescrition 分组描述
     * @param userIds         客户id列表
     * @return
     */

    public BaseResponse addUserGroup(Long commId, String groupName, String groupDescrition,
        List<Long> userIds) {

        if (StringUtils.isBlank(groupName)) {
            throw new DcArgumentException("客户组名称不能空");
        }

        BaseResponse jsonObjectRes = userFeignClient.addUserGroup(commId, groupName,
            groupDescrition, userIds);
        return jsonObjectRes;

//        if (jsonObjectRes == null || jsonObjectRes.get("status") == null) {
//            throw new DcServiceException("添加失败");
//        }
//
//        if (jsonObjectRes.getInteger("status") != ResultConstant.RES_SUCCESS_CODE) {
//
//            if (jsonObjectRes.get("error") == null) {
//                throw new DcServiceException("添加失败");
//            }else{
//                throw new DcServiceException(jsonObjectRes.get("error").toString());
//            }
//        }
//
        //        return new ObjectResponse<>("添加成功");
    }


}
