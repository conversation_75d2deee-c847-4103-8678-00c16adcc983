package com.cdz360.biz.ant.service.tj;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.feign.reactor.BizTjFeignClient;
import com.cdz360.biz.model.tj.kc.dto.TjCompetitorSiteDto;
import com.cdz360.biz.model.tj.kc.param.ListTjCompetitorSiteParam;
import com.cdz360.biz.model.tj.kc.vo.TjCompetitorSiteVo;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TjCompetitorSiteService {

    @Autowired
    private BizTjFeignClient bizTjFeignClient;

    public Mono<ListResponse<TjCompetitorSiteVo>> findTjCompetitorSite(
        ListTjCompetitorSiteParam param) {
        return bizTjFeignClient.findTjCompetitorSite(param);
    }

    public Mono<ObjectResponse<TjCompetitorSiteVo>> saveTjCompetitorSiteAttachInfo(
        TjCompetitorSiteDto dto) {
        IotAssert.isNotNull(dto.getId(), "场站ID不能为空");
        return bizTjFeignClient.saveTjCompetitorSiteAttachInfo(dto);
    }
}
