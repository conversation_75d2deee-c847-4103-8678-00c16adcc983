package com.cdz360.biz.ant.service.site;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.feign.MerchantFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 站点值班表
 * <p>
 * SiteDutyServiceImpl
 *
 * <AUTHOR>
 * @since 2019.2.18
 */
@Service
@Slf4j
public class SiteDutyService //implements ISiteDutyService
{

    @Autowired
    private MerchantFeignClient merchantFeignClient;

    /**
     * 统计站点值班表人数
     *
     * @param siteId
     * @return
     */

    public ObjectResponse<Integer> getDutyCount(String siteId) {

        ObjectResponse<Integer> jsonResult = merchantFeignClient.getDutyCount(siteId);
        return jsonResult;
//        if (jsonResult != null || jsonResult.getInteger("status") == ResultConstant.RES_SUCCESS_CODE) {
//            return JSONObject.parseObject(jsonResult.toString(), ObjectResponse.class);
//        } else {
//            if ( jsonResult == null ){
//                throw new DcServiceException("数据获取错误");
//            }else{
//                throw new DcServiceException(jsonResult.getString("error"));
//            }
//        }
    }

    /**
     * 保存站点值班表
     *
     * @param siteId
     * @param siteDuty
     * @return
     */

    public BaseResponse saveSiteDuty(String siteId, String siteDuty) {
        BaseResponse jsonResult = merchantFeignClient.saveSiteDuty(siteId, siteDuty);
        FeignResponseValidate.check(jsonResult);
        return jsonResult;
//        if (jsonResult != null || jsonResult.getInteger("status") == ResultConstant.RES_SUCCESS_CODE) {
//            return JSONObject.parseObject(jsonResult.toString(), ObjectResponse.class);
//        } else {
//            if ( jsonResult == null ){
//                throw new DcServiceException("数据操作失败");
//            }else{
//                throw new DcServiceException(jsonResult.getString("error"));
//            }
//        }
    }

    /**
     * 站点值班表查询
     *
     * @param siteId
     * @return
     */

    public ObjectResponse<JsonNode> getSiteDuty(String siteId) {
        ObjectResponse<JsonNode> jsonResult = merchantFeignClient.getSiteDuty(siteId);
        return jsonResult;
    }
}
