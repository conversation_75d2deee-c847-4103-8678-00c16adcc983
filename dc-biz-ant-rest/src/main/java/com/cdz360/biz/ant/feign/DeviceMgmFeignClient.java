package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.domain.bundle.EvseBundleDto;
import com.cdz360.biz.ant.domain.bundle.EvseBundleParam;
import com.cdz360.biz.ant.domain.bundle.UpgradeStatusParam;
import com.cdz360.biz.ess.model.dto.EssEquipAlarmLangDto;
import com.cdz360.biz.ess.model.param.ListAlarmLangParam;
import com.cdz360.biz.model.evse.param.ListPackageParam;
import com.cdz360.biz.model.evse.param.UpdateEvsePackageParam;
import com.cdz360.biz.model.evse.vo.EvsePackageVo;
import com.cdz360.biz.model.iot.type.EvseVendor;
import com.cdz360.biz.model.iot.vo.TransformerPlugsVo;
import com.cdz360.biz.model.iot.vo.TransformerVo;
import com.cdz360.biz.model.parts.param.PartsCheckParam;
import com.cdz360.biz.model.upgradepg.type.BundleType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;
import reactor.core.publisher.Mono;


@FeignClient(name = DcConstants.KEY_FEIGN_IOT_DEVICE_MGM,
    fallbackFactory = DeviceMgmFeignClientHystrixFactory.class)
public interface DeviceMgmFeignClient {

    /**
     * @param type
     * @return
     */
    @GetMapping("/device/mgm/evse/getEvseModelOrFirm")
    ListResponse<String> getEvseModelOrFirm(@RequestParam(value = "type") String type);


    @GetMapping("/device/mgm/transformer/listTransformer")
    ListResponse<TransformerVo> listTransformer(
        @RequestParam(value = "keyword", required = false) String keyword,
        @RequestParam(value = "transformerId", required = false) Long transformerId,
        @RequestParam(value = "siteId") String siteId,
        @RequestParam(value = "start") long start,
        @RequestParam(value = "size") long size);


    @PostMapping("/device/mgm/transformer/addTransformer")
    BaseResponse addTransformer(@Validated @RequestBody TransformerVo req);

    @PostMapping("/device/mgm/transformer/editTransformer")
    BaseResponse editTransformer(@RequestBody TransformerVo req);

    @GetMapping("/device/mgm/transformer/disableTransformer")
    BaseResponse disableTransformer(@RequestParam(value = "id") long id);

    @GetMapping("/device/mgm/transformer/getPlugListByTransformerId")
    ListResponse<TransformerPlugsVo> getPlugListByTransformerId(
        @RequestParam(value = "siteId") String siteId,
        @RequestParam(value = "transformerId", required = false)
        Long transformerId);


    /**
     * 桩升级包分页查询
     *
     * <AUTHOR>
     * @since 10:11 2019/9/20
     */
    @PostMapping("/device/mgm/evsebundle/listEvseBundlePage")
    ListResponse<EvseBundleDto> evseBundlePage(@RequestBody EvseBundleParam evseBundleParam);

    /**
     * 桩升级包删除
     *
     * <AUTHOR>
     * @since 10:10 2019/9/20
     */
    @GetMapping("/device/mgm/evsebundle/deleteEvseBundle")
    BaseResponse deleteEvseBundle(@RequestParam("id") Long id);

    @PostMapping("/device/mgm/evsebundle/changeStatus")
    BaseResponse changeStatus(@RequestBody UpgradeStatusParam param);

    // 设备升级包上传
    @PostMapping(value = "/device/mgm/evsebundle/uploadEvseBundle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    ObjectResponse<Long> uploadEvseBundle(@RequestPart("file") MultipartFile file,
        @RequestParam("type") BundleType type,
        @RequestParam("vendor") EvseVendor vendor,
        @RequestParam("swVerCode") Long swVerCode,
        @RequestParam("opId") Long opId,
        @RequestParam("opName") String opName);

    // 物料导入前检查
    @PostMapping(value = "/device/mgm/parts/checkInDB")
    ListResponse<PartsCheckParam> checkInDB(@RequestBody List<PartsCheckParam> items);


    @PostMapping("/device/evsePackage/deleteById")
    BaseResponse deleteById(@RequestBody UpdateEvsePackageParam param);

    @Operation(summary = "修改升级包状态")
    @PostMapping("/device/evsePackage/updateStatus")
    BaseResponse updateStatus(@RequestBody UpdateEvsePackageParam param);

    @Operation(summary = "获取列表")
    @PostMapping("/device/evsePackage/getList")
    ListResponse<EvsePackageVo> getList(@RequestBody ListPackageParam param);

    @Operation(summary = "新增升级包")
    @PostMapping("/device/evsePackage/create")
    BaseResponse create(@RequestBody UpdateEvsePackageParam param);

    @Operation(summary = "获取品牌列表")
    @GetMapping("/device/evsePackage/getBrandList")
    ListResponse<String> getBrandList();

    @Operation(summary = "编辑升级包")
    @PostMapping("/device/evsePackage/editPackage")
    BaseResponse editPackage(UpdateEvsePackageParam param);

    @PostMapping(value = "/device/mgm/evse/getAlarmLangList")
     ListResponse<EssEquipAlarmLangDto> getAlarmLangList(
        @RequestBody ListAlarmLangParam param);

    @GetMapping(value = "/device/mgm/evse/updateBizStatus")
    BaseResponse updateBizStatus(
        @RequestParam("evseNo") String evseNo, @RequestParam("bizStatus") Long bizStatus);

}
