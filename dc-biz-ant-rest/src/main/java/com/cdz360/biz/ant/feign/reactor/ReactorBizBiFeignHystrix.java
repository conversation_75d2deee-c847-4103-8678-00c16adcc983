package com.cdz360.biz.ant.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ess.model.data.param.DataBiParam;
import com.cdz360.biz.model.bi.dashboard.CommStatisticBiVo;
import com.cdz360.biz.model.oa.vo.OaElecPayIncomeExpenseVo;
import com.cdz360.biz.model.trading.order.vo.ChargeOrderBiVo;
import com.cdz360.biz.model.trading.site.dto.DistrictSiteNumDto;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.oa.dto.BatchTaskVo;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDetailVo;
import java.util.List;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class ReactorBizBiFeignHystrix
    implements FallbackFactory<ReactorBizBiFeignClient> {

    @Override
    public ReactorBizBiFeignClient apply(Throwable throwable) {
        log.info("err = {}", throwable.getMessage(), throwable);
        return new ReactorBizBiFeignClient() {
            @Override
            public Mono<ListResponse<CommStatisticBiVo>> chargeDataSample(DataBiParam param) {
                log.error(
                    "【服务熔断】。Service = {}, api = chargeDataSample (充电数据采集), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_BI, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<ChargeOrderBiVo>> getOrderBi(String commIdChain,
                String siteId, List<Integer> bizTypeList) {
                log.error(
                    "服务[{}]接口熔断 - 获取历史(总)充电统计数据, commIdChain = {}, siteId = {}, bizTypeList = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_BI, commIdChain, siteId, bizTypeList);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<ChargerOrderDetailVo>> getChargerOrderDetail(
                @RequestBody ChargerOrderParam param) {
                log.error("服务[{}]接口熔断 - 获取订单峰平谷统计, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_BI, param);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<DistrictSiteNumDto>> getDistrictSiteNumList(
                ListSiteParam param) {
                log.error("服务[{}]接口熔断 - 获取历史(总)充电统计数据, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_BI, param);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<OaElecPayIncomeExpenseVo>> getOaElecPayTaskListBySiteId(
                List<BatchTaskVo> voList) {
                log.error("服务[{}]接口熔断 - getOaElecPayTaskListBySiteId, voList = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_BI, JsonUtils.toJsonTimeString(voList));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

        };
    }

    @Override
    public <V> Function<V, ReactorBizBiFeignClient> compose(
        Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_BI);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(
        Function<? super ReactorBizBiFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_BI);
        return null;
    }
}
