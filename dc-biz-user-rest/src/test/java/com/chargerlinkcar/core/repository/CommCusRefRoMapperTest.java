package com.chargerlinkcar.core.repository;

import com.cdz360.biz.ds.cus.ro.comm.mapper.CommCusRefRoMapper;
import com.cdz360.biz.ds.cus.rw.comm.mapper.CommCusRefRwMapper;
import com.cdz360.biz.model.common.constant.Constant;
import com.chargerlinkcar.core.BaseMapperTest;
import com.chargerlinkcar.framework.common.domain.vo.CommCusRef;
import java.util.Date;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 *
 * @since 2019/8/9
 **/
public class CommCusRefRoMapperTest extends BaseMapperTest {
    @Autowired
    private CommCusRefRoMapper commCusRefRoMapper;
    @Autowired
    private CommCusRefRwMapper commCusRefRwMapper;

    @Test
    public void insertOrUpdateCommCusRef() {
        Date now = new Date();
        CommCusRef commCusRef = new CommCusRef();
        commCusRef.setUserId(62287L);
        commCusRef.setCommId(34506L);
        commCusRef.setEnable(Constant.ENABLE);
        commCusRef.setCreateTime(now);
        commCusRef.setUpdateTime(now);
        commCusRefRwMapper.insertOrUpdateCommCusRef(commCusRef);
    }

    @Test
    public void listCommIdByCusId() {
        commCusRefRoMapper.listCommIdByCusId(62287L, false);
    }

    @Test
    public void checkCommCusRefExists() {
        boolean b = commCusRefRoMapper.checkCommCusRefExists(62281, 34506);
    }

    @Test
    public void checkCommCusRefDisabled() {
        boolean b = commCusRefRoMapper.checkCommCusRefDisabled(62281, 34506);
    }
}