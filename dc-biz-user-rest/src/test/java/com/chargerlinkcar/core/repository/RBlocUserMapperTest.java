package com.chargerlinkcar.core.repository;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.cus.domain.BlocUser;
import com.cdz360.biz.cus.repository.BlocUserMapper;
import com.cdz360.biz.cus.repository.RBlocUserMapper;
import com.cdz360.biz.cus.service.PublishCorpUserInfoService;
import com.cdz360.biz.cus.service.impl.RBlocUserServiceImpl;
import com.chargerlinkcar.core.BaseMapperTest;
import com.chargerlinkcar.framework.common.domain.BlocUserDto;
import com.chargerlinkcar.framework.common.domain.vo.CorpCreditAccountEx;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 集团客户单元测试
 *
 * <AUTHOR>
 */
public class RBlocUserMapperTest extends BaseMapperTest {
    @Autowired
    private RBlocUserMapper rBlocUserMapper;

    @Autowired
    private BlocUserMapper blocUserMapper;

    @Autowired
    private PublishCorpUserInfoService publishCorpUserInfoService;

    private RBlocUser rBlocUser;

    private RBlocUser rBlocUserCorp;

    @Autowired
    private RBlocUserServiceImpl rBlocUserService;

    @BeforeEach
    public void initData() throws Exception {
        rBlocUser = new RBlocUser() {{
            setBalance(BigDecimal.valueOf(9999L));
            setBlocUserId(251L);
            setUserId(33421L);
            setCommId(33421L);
            setPhone("13012345678");
            setStatus(1);
            setCreateDate(new Date());
            setFrozenAmount(BigDecimal.ZERO);
        }};

        rBlocUserCorp = new RBlocUser() {{
            setBalance(BigDecimal.valueOf(9999L));
            setBlocUserId(9990L);
            setUserId(33421L);
            setName("aaabbbcccddd");
            setCommId(33421L);
            setPhone("***********");
            setStatus(1);
            setCreateDate(new Date());
            setFrozenAmount(BigDecimal.ZERO);
            setCorpOrgId(9L);
        }};
    }

    @Test
    public void testCorpUpdate() {
        CorpCreditAccountEx corpCreditAccountEx = new CorpCreditAccountEx();
        corpCreditAccountEx.setCorpId(9999L);
        corpCreditAccountEx.setCorpOrgId(88888L);
        corpCreditAccountEx.setLimitMoney(BigDecimal.TEN);
        corpCreditAccountEx.setPhone("***********");
        corpCreditAccountEx.setUserName("testCorpUpdate");
        ObjectResponse<Long> res = rBlocUserService.upserdelRBlocUser(corpCreditAccountEx);
        Assertions.assertFalse(res.getData().equals(0));

        // update
        corpCreditAccountEx.setId(res.getData());
        corpCreditAccountEx.setPhone("***********");
        res = rBlocUserService.upserdelRBlocUser(corpCreditAccountEx);
        Assertions.assertFalse(res.getData().equals(0));
        List<RBlocUser> list = rBlocUserMapper.queryCorpUser(2,
                corpCreditAccountEx.getPhone(),
                corpCreditAccountEx.getCorpId(),
                corpCreditAccountEx.getCorpOrgId(),null,null);
        Assertions.assertTrue(CollectionUtils.isNotEmpty(list) && list.size() == 1);
        Assertions.assertTrue(list.get(0).getPhone().equalsIgnoreCase(corpCreditAccountEx.getPhone()));

        // delete
        corpCreditAccountEx.setStatus(false);
        res = rBlocUserService.upserdelRBlocUser(corpCreditAccountEx);
        Assertions.assertFalse(res.getData().equals(0));
        list = rBlocUserMapper.queryCorpUser(2,
                corpCreditAccountEx.getPhone(),
                corpCreditAccountEx.getCorpId(),
                corpCreditAccountEx.getCorpOrgId(), null,null);
        Assertions.assertTrue(CollectionUtils.isEmpty(list));
    }

    @Test
    public void testCorpQuery() {
        int res = rBlocUserMapper.insertRBlocUser(rBlocUserCorp);
        Assertions.assertTrue(res == 1);
        List<RBlocUser> list = rBlocUserMapper.queryCorpUser(null, null, 9990L, 9L, null,null);
        Assertions.assertTrue(list.size() > 0);
        list = rBlocUserMapper.queryCorpUser(0, "abb", 9990L, 9L, null,null);
        Assertions.assertTrue(list.size() > 0);
        RBlocUser rbu = list.get(0);
        list = rBlocUserMapper.queryCorpUser(1, rbu.getId().toString(), 9990L, 9L, null,null);
        Assertions.assertTrue(list.size() > 0);
        list = rBlocUserMapper.queryCorpUser(2, rbu.getPhone(), 9990L, 9L, null,null);
        Assertions.assertTrue(list.size() > 0);
        list = rBlocUserMapper.queryCorpUser(2, rbu.getPhone() + "x", 9990L, 9L, null,null);
        Assertions.assertTrue(list.size() == 0);

    }

    @Test
    public void insertRBlocUser() {
        int res = rBlocUserMapper.insertRBlocUser(rBlocUser);
        Assertions.assertTrue(res == 1);
        publishCorpUserInfoService.publishCorpUserInfo(rBlocUser);
    }

    @Test
    public void freezeAmount() {
        insertRBlocUser();
        int res = rBlocUserMapper.freezeAmount(rBlocUser.getId(), BigDecimal.valueOf(200L));
        Assertions.assertTrue(res == 1);
    }

    @Test
    public void updateFrozenAmountAndBalance() {
        insertRBlocUser();
        int res = rBlocUserMapper.updateFrozenAmountAndBalance(rBlocUser.setFrozenAmount(BigDecimal.valueOf(200L))
                .setBalance(BigDecimal.valueOf(100L)));
        Assertions.assertTrue(res == 1);
    }
    @Test
    public void modifyByCondition(){
        RBlocUser rBlocUser = new RBlocUser();
        rBlocUser.setId(1l);
        rBlocUser.setPhone("130");
        System.out.println(rBlocUserMapper.modifyByCondition(rBlocUser));
        publishCorpUserInfoService.publishCorpUserInfo(rBlocUser);
    }

    @Test
    public void insertSelective() {
        BlocUserDto user = new BlocUserDto();
        user.setCommId(33421l);
        user.setTopCommId(33421);
        user.setUid(62703);
        user.setBlocUserName("abc123");
        Integer i = blocUserMapper.insertSelective(user);
        IotAssert.isTrue(i > 0, "junit fail");
    }

    @Test
    public void findByCondition() {
        this.insertSelective();
        BlocUser user = new BlocUser();
        user.setCommId(33421l);
        user.setBlocUserName("ab");
        List<BlocUserDto> list = blocUserMapper.findByCondition(user);
        IotAssert.isTrue(CollectionUtils.isNotEmpty(list), "junit fail");
    }
}