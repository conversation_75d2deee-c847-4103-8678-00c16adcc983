package com.chargerlinkcar.core.repository;

import com.cdz360.biz.cus.repository.CardMapper;
import com.cdz360.biz.model.cus.user.dto.WhiteCardDto;
import com.chargerlinkcar.core.BaseMapperTest;
import com.chargerlinkcar.framework.common.domain.type.CardStatus;
import com.chargerlinkcar.framework.common.domain.vo.Card;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;

public class CardMapperTest extends BaseMapperTest {
    @Autowired
    private CardMapper cardMapper;

    private List<Card> listCard;

    private String cardKey = "key";

    @BeforeEach
    public void first() {
        Card record01 = new Card();
        record01.setCardChipNo("20190711111");
        record01.setCardNo("20190711111");
        record01.setCommId(33422L);
        record01.setCardType(1);
        record01.setStations("11111");
        record01.setCardKey(cardKey);

        Card record02 = new Card();
        record02.setCardChipNo("20190711222");
        record02.setCardNo("20190711222");
        record02.setCommId(33422L);
        record02.setCardType(1);
        record02.setStations("22222");
        record02.setCardKey(cardKey);


        Card record03 = new Card();
        record03.setCardChipNo("20190711333");
        record03.setCardNo("20190711333");
        record03.setCommId(33422L);
        record03.setCardType(1);
        record03.setStations("11111");
        record03.setCardKey(cardKey);


        Card record04 = new Card();
        record04.setCardChipNo("20190711444");
        record04.setCardNo("20190711444");
        record04.setCommId(33422L);
        record04.setCardType(1);
        record04.setStations("33333");
        record04.setCardKey(cardKey);

        listCard = new ArrayList<>();
        listCard.add(record01);
        listCard.add(record02);
        listCard.add(record03);
        listCard.add(record04);
    }

    @Test
    public void insertConBloc() {

        Long userId = 999L;
        Long corpId03 = 222L;
        Long corpId04 = 333L;

        List<Long> corpIds = List.of(corpId03, corpId04);

        Card record03 = new Card();
        record03.setCardChipNo("20190711444");
        record03.setCardNo("20191225444x");
        record03.setCommId(33422L);
        record03.setCardType(1);
        record03.setStations("33333");
        record03.setCardKey(cardKey);
        record03.setUserId(userId);
        record03.setCorpId(corpId03);
        cardMapper.insertSelective(record03);

        Card record04 = new Card();
        record04.setCardChipNo("20191225444");
        record04.setCardNo("20190711444y");
        record04.setCommId(33422L);
        record04.setCardType(1);
        record04.setStations("33333");
        record04.setCardKey(cardKey);
        record04.setUserId(userId);
        record04.setCorpId(corpId04);
        cardMapper.insertSelective(record04);

        List<Card> cards = cardMapper.getCardsByUserIdAndCorpIds(userId, corpIds);
        Assert.isTrue(cards.size() == 2, "not eq 2");

//        cardMapper.deleteCardByCardNo("20191225444x");
//        cardMapper.deleteCardByCardNo("20191225444y");
    }


    @Test
    public void insertForWhiteCardDtoMapList() {
        listCard.forEach(card -> {
            cardMapper.insertSelective(card);
        });
    }

    @Test
    public void queryWhiteCardDtoMapList() {
        insertForWhiteCardDtoMapList();
        List<Long> commIds = new ArrayList<>();
        commIds.add(33422L);
        //将下列状态的卡排除在外
        List<String> excludeCardStatusList = new ArrayList<>();
        excludeCardStatusList.add(CardStatus.INACTIVE.getCode());
        excludeCardStatusList.add(CardStatus.LOCK.getCode());
        excludeCardStatusList.add(CardStatus.FAILURE.getCode());
        excludeCardStatusList.add(CardStatus.EXPIRED.getCode());
        excludeCardStatusList.add(CardStatus.DELETED.getCode());
        List<WhiteCardDto> whiteCardDtos = cardMapper.queryWhiteCardDtoMapList(
                null, null,null, commIds,"",excludeCardStatusList);
        System.out.println(whiteCardDtos);
    }

    @Test
    public void queryCardByCardNoList() {
        List<String> cardNos = new ArrayList<>();
        cardNos.add("4290604410");
        cardMapper.queryCardByCardNoList(cardNos);
    }
}