package com.chargerlinkcar.core.repository;

import com.cdz360.biz.cus.domain.vo.VinSearchParamComm;
import com.cdz360.biz.cus.repository.VinMapper;
import com.cdz360.biz.model.cus.vin.param.VINCarNoParam;
import com.chargerlinkcar.core.BaseMapperTest;
import com.chargerlinkcar.framework.common.domain.vo.VinDto;
import com.chargerlinkcar.framework.common.domain.vo.VinParam;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class VinMapperTest extends BaseMapperTest {
    @Autowired
    private VinMapper vinMapper;

    private VinParam vinParam;
    private VinParam vinParam1;
    private VinParam vinParam2;


    private Long modifyBy = 1000L;

    private Long userId = 9999999L;

    @BeforeEach
    public void first() {
        vinParam = new VinParam();
        vinParam.setVin("setVin1");
        vinParam.setCarNo("setCarNo");
        vinParam.setName("setName");
        vinParam.setStation("setStation");
        vinParam.setStatus(1);
        vinParam.setUserId(userId);
        vinParam.setModifyBy(1000L);
        vinParam.setCommId(9999L);
        vinParam.setSubCommId(9999L);
        vinParam.setEnable(1);

        vinParam1 = new VinParam();
        vinParam1.setVin("setVin2");
        vinParam1.setCarNo("99999");
        vinParam1.setName("setName");
        vinParam1.setStation("setStation");
        vinParam1.setStatus(1);
        vinParam1.setUserId(userId);
        vinParam1.setModifyBy(1000L);
        vinParam1.setCommId(9999L);
        vinParam1.setSubCommId(9999L);

        vinParam2 = new VinParam();
        vinParam2.setVin("1008611");
        vinParam2.setCarNo("湘10086");
        vinParam2.setName("玛莎拉可");
        vinParam2.setStation("66666");
        vinParam2.setStatus(1);
        vinParam2.setUserId(61699L);
        vinParam2.setModifyBy(33425L);
        vinParam2.setCommId(33425L);
        vinParam2.setSubCommId(33425L);
    }

    @Test
    public void insert() {
        Integer c = vinMapper.insert(vinParam);
        Assertions.assertTrue(c > 0);
        Assertions.assertNotNull(vinParam.getId());
    }

    @Test
    public void insert_default() {
        Integer c1 = vinMapper.insert(vinParam1);
        Assertions.assertTrue(c1 > 0);
        Assertions.assertNotNull(vinParam1.getId());

        VinSearchParamComm vinSearchParam1 = new VinSearchParamComm();
        vinSearchParam1.setCarNo(vinParam1.getCarNo());
        vinSearchParam1.setEndTime("2030-12-31");
        vinSearchParam1.setStartTime("2000-1-1");
        vinSearchParam1.setStatus(vinParam1.getStatus());
        vinSearchParam1.setVin(vinParam1.getVin());
        vinSearchParam1.setUserId(vinParam1.getUserId());

        List<VinDto> list = vinMapper.select(vinSearchParam1);
        Assertions.assertTrue(list.size() > 0);
        Assertions.assertTrue(list.get(0).getSubCommId().equals(list.get(0).getCommId()));
        Assertions.assertTrue(list.get(0).getEnable() == 1);
    }

    @Test
    public void update() {
        insert();
        vinParam.setName("update");
        Integer c = vinMapper.update(vinParam);
        Assertions.assertTrue(c > 0);
        Assertions.assertNotNull(vinParam.getId());
    }

    @Test
    public void delete() {
        insert();
        VinSearchParamComm vinSearchParam = new VinSearchParamComm();
        vinSearchParam.setCarNo(vinParam.getCarNo());
        vinSearchParam.setEndTime("2030-12-31");
        vinSearchParam.setStartTime("2000-1-1");
        vinSearchParam.setStatus(vinParam.getStatus());
        vinSearchParam.setVin(vinParam.getVin());
        vinSearchParam.setUserId(vinParam.getUserId());

        List<VinDto> list = vinMapper.select(vinSearchParam);

        Integer c = vinMapper.delete(vinParam.getId(), modifyBy);
        Assertions.assertTrue(c > 0);
        List<VinDto> list1 = vinMapper.select(vinSearchParam);

        Assertions.assertFalse(list.size() == list1.size());

    }

    @Test
    public void select() {
        insert();
        VinSearchParamComm vinSearchParam = new VinSearchParamComm();
        vinSearchParam.setCarNo(vinParam.getCarNo());
        vinSearchParam.setEndTime("2030-12-31");
        vinSearchParam.setStartTime("2000-1-1");
        vinSearchParam.setStatus(vinParam.getStatus());
        vinSearchParam.setVin(vinParam.getVin());
        vinSearchParam.setUserId(vinParam.getUserId());

        List<VinDto> list = vinMapper.select(vinSearchParam);

        Assertions.assertTrue(list.size() == 1);
        Assertions.assertTrue(vinParam.getCarNo().equalsIgnoreCase(list.get(0).getCarNo()));
    }

//    @Test
//    public void selectCountByUserCommId() {
//
//        VinParam vinParamCount = new VinParam();
//        vinParamCount.setVin("setVin122");
//        vinParamCount.setCarNo("setCarNo");
//        vinParamCount.setName("setName");
//        vinParamCount.setStation("setStation");
//        vinParamCount.setStatus(1);
//        vinParamCount.setUserId(userId);
//        vinParamCount.setModifyBy(1000L);
//        vinParamCount.setCommId(9999L);
//        vinParamCount.setEnable(1);
//        Integer c = vinMapper.insert(vinParamCount);
//
//        Assertions.assertTrue(c > 0);
//        Assertions.assertTrue(vinMapper.selectCountByUserCommId(9999L, "setVin122") == 1);
//    }


    @Test
    public void insertForSelect() {
        Integer c = vinMapper.insert(vinParam2);
        Assertions.assertTrue(c > 0);
        Assertions.assertNotNull(vinParam2.getId());
    }

    @Test
    public void selectAllVinList() {
        insertForSelect();
        Map<String, Object> map = new HashMap<>();
        List<Long> commIds = new ArrayList<>();
        commIds.add(vinParam2.getCommId());
        map.put("commIds", commIds);
        map.put("userId", vinParam2.getUserId());
        List<VinDto> list = vinMapper.selectAllVinList(map);
        System.out.println(list.toString());
        Assertions.assertTrue(list.size() >= 1);
    }

    /**
     * 订单筛选-根据VIN查询卡号
     */
    @Test
    public void selectCarNoByVin() {
        insertForSelect();
//        Map<String, Object> map = new HashMap<>() {{
//            put("cardNos", new ArrayList<String>() {{
//                add("1008611");
//            }});
//        }};
        VINCarNoParam param = new VINCarNoParam();
        param.setVinList(List.of("1008611"));
        param.setCommIdChain("33425");
        List<VinDto> vinDtos = vinMapper.selectCarNoByVins(param);
        Assertions.assertTrue(vinDtos.size() >= 1);
    }
}