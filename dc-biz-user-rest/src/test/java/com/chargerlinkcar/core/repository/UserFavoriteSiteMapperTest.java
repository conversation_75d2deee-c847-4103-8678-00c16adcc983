package com.chargerlinkcar.core.repository;

import com.cdz360.biz.cus.repository.UserFavoriteSiteMapper;
import com.cdz360.biz.model.site.type.SiteStatus;
import com.chargerlinkcar.core.BaseMapperTest;
import com.chargerlinkcar.framework.common.domain.UserFavoriteSite;
import com.chargerlinkcar.framework.common.domain.request.UserFavorSiteReq;
import com.chargerlinkcar.framework.common.utils.DcAssertUtil;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;


public class UserFavoriteSiteMapperTest extends BaseMapperTest {

    @Autowired
    private UserFavoriteSiteMapper mapper;

    private long uid = 14;
    private String siteId = "131948248229892842";
    private boolean favorite = true;
    private long start = 0;
    private int size = 5;

    @Test
    public void test() {
        Integer add = mapper.addToFavorites(uid, siteId);
        DcAssertUtil.isTrue(add > 0, "junit fail");

        Integer ifFavorite = mapper.ifFavorite(uid, siteId);
        DcAssertUtil.isTrue(ifFavorite > 0, "junit fail");

        UserFavorSiteReq req = new UserFavorSiteReq();
        req.setUid(uid).setDisplayStatusList(Arrays.asList(SiteStatus.ONLINE.getCode(),SiteStatus.UNAVAILABLE.getCode())).setStart(start).setSize(size);
        List<UserFavoriteSite> getByUid = mapper.getByUid(req);
        DcAssertUtil.isNotNull(getByUid, "junit fail");

        Integer remove = mapper.removeTheCollection(uid, siteId);
        DcAssertUtil.isTrue(remove > 0, "junit fail");
    }

}