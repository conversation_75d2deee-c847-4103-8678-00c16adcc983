//package com.chargerlinkcar.core.repository;
//
//import com.chargerlinkcar.core.BaseMapperTest;
//import com.chargerlinkcar.core.domain.vo.KVAnalysisVo;
//import org.junit.jupiter.api.Assertions;
//import org.junit.jupiter.api.Test;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//
//public class AnalysisMapperTest extends BaseMapperTest {
//    @Autowired
//    AnalysisMapper analysisMapper;
//    @Test
//    public void getkhtjgl() {
//        List<Long> commIdList = new ArrayList<>();
//        commIdList.add(Long.valueOf(33421));
//        Map map = new HashMap();
//        map.put("commIdList", commIdList);
//        List<KVAnalysisVo> resultList=analysisMapper.getkhtjgl(map);
//        Assertions.assertNotNull(resultList);
//    }
//
//}