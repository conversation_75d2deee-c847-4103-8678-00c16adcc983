package com.chargerlinkcar.core.rest;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.cus.rest.CorpSettlementRest;
import com.cdz360.biz.ds.cus.ro.settlement.ds.SettlementRoDs;
import com.cdz360.biz.ds.cus.rw.settlement.ds.SettlementRwDs;
import com.cdz360.biz.model.cus.settlement.dto.SettlementDto;
import com.cdz360.biz.model.cus.settlement.param.ListSettlementParam;
import com.cdz360.biz.model.cus.settlement.param.UpdateCorpSettlementParam;
import com.cdz360.biz.model.cus.settlement.po.SettlementPo;
import com.cdz360.biz.model.cus.settlement.vo.SettlementVo;
import com.chargerlinkcar.core.BaseMockTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@Slf4j
class CorpSettlementRestTest extends BaseMockTest {

    private static final Long CORP_ID = 2L;

    private MockMvc mockMvc;
    @Autowired
    private WebApplicationContext wac;

    @Autowired
    private SettlementRwDs settlementRwDs;

    @Autowired
    private SettlementRoDs settlementRoDs;

    @BeforeEach
    public void setUp() throws Exception {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    void getSettlementCfgById() throws Exception {
        String url = "/api/corp/getSettlementCfgById";

        MockHttpServletRequestBuilder rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("cfgId", String.valueOf(78));

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(CorpSettlementRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("getSettlementCfgById")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").doesNotExist())
                .andDo(print())
                .andReturn();
    }

    @Test
    void getSettlementCfg() throws Exception {
        String url = "/api/corp/getSettlementCfg";

        MockHttpServletRequestBuilder rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("corpId", String.valueOf(289L));

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(CorpSettlementRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("getSettlementCfg")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();
    }

    @Test
    void updateSettlementCfg() throws Exception {
        String url = "/api/corp/updateSettlementCfg";

        String json = "{\"corpId\":277,\"activeDate\":null,\"guarantee\":true,\"guaranteeKwh\":11,\"guaranteeElecFee\":3.4321,\"guaranteeServFee\":1.5123,\"subSettlementType\":11,\"settlementDate\":28,\"siteIdList\":[\"1910132043005564461\",\"1910153049900637758\",\"1910172320999896327\",\"1911014879218856351\",\"1911143912069939133\"],\"settlementType\":3,\"settlementSwitch\":true,\"fixed\":{\"servFee\":0,\"elecFee\":0.0001},\"customFee\":{\"jian\":{\"servFee\":0,\"elecFee\":0},\"feng\":{\"servFee\":0,\"elecFee\":0},\"ping\":{\"servFee\":0,\"elecFee\":0},\"gu\":{\"servFee\":0,\"elecFee\":0}},\"sectionFeeList\":[{\"servFee\":0,\"elecFee\":0,\"fromKwh\":0,\"toKwh\":0}]}";

        UpdateCorpSettlementParam param = JsonUtils.fromJson(json, UpdateCorpSettlementParam.class);
//        param.setCorpId(CORP_ID);

        // 账户扣款: 即时生效
//        param.setSettlementType(SettlementType.BALANCE);
//        param.setGuarantee(false);
//        param.setSettlementSwitch(false);

        // 账户扣款: 未来发生
//        param.setSettlementType(SettlementType.BALANCE);
//        param.setActiveDate(tomorrow());
//        param.setGuarantee(false);
//        param.setSettlementSwitch(false);

        // 后付费: 即时生效
//        param.setSettlementType(SettlementType.POSTPAID);
//        param.setGuarantee(false);
//        param.setSettlementSwitch(false);

        // 后付费: 未来发生
//        param.setSettlementType(SettlementType.POSTPAID);
//        param.setActiveDate(tomorrow());
//        param.setGuarantee(false);
//        param.setSettlementSwitch(false);

        // 后付费: 带低保
//        param.setSettlementType(SettlementType.POSTPAID);
//        param.setGuarantee(true);
//        param.setGuaranteeElecFee(BigDecimal.TEN);
//        param.setGuaranteeServFee(BigDecimal.TEN);
//        param.setGuaranteeKwh(50);
//
//        param.setSettlementSwitch(true);
//        param.setSettlementDate(8);
//        param.setSiteIdList(List.of("1073094969257926657"));
////        param.setSubSettlementType(SubSettlementType.COST_PRICE);
//        param.setSubSettlementType(SubSettlementType.FIXED_PRICE);
//        param.setFixed(new Fee().setElecFee(BigDecimal.ONE).setServFee(BigDecimal.TEN));

        String req = JsonUtils.toJsonString(param); // 请求体
        log.info("req param = {}", JsonUtils.toJsonString(param));

        MockHttpServletRequestBuilder rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON).content(req);

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(CorpSettlementRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("updateSettlementCfg")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").doesNotExist())
                .andDo(print())
                .andReturn();
    }

    @Test
    void findSettlementList() throws Exception {
        String url = "/api/corp/findSettlementList";

        ListSettlementParam param = new ListSettlementParam();
        param.setStart(0L);
        param.setSize(100);
        param.setTotal(true);
//        param.setCorpId(2L);
//        param.setStatusList(List.of(SettlementStatusEnum.INIT));
        param.setCommIdChain("34474,34647,34661");

        String req = JsonUtils.toJsonString(param); // 请求体
        log.info("req param = {}", JsonUtils.toJsonString(param));

        MockHttpServletRequestBuilder rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON).content(req);

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(CorpSettlementRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("findSettlementList")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andDo(print())
                .andReturn();
    }

    @Test
    void removeOrder4Settlement() throws Exception {
        String url = "/api/corp/removeOrder4Settlement";

        String json = "{\"billNo\":\"0064140942550025\",\"billName\":\"新增账单2\",\"corpId\":1,\"billStatus\":null,\"settlementType\":null,\"cfgId\":54,\"guaranteeKwh\":null,\"guaranteeElecFee\":null,\"guaranteeServFee\":null,\"subSettlementType\":11,\"subSettlement\":null,\"settlementTotalFee\":null,\"settlementServFee\":null,\"settlementElecFee\":null,\"orderNum\":null,\"orderKwh\":null,\"kwhOther\":null,\"kwhJian\":null,\"kwhFeng\":null,\"kwhPing\":null,\"kwhGu\":null,\"orderElecFee\":null,\"orderServFee\":null,\"siteNameList\":null,\"siteNoList\":null,\"opType\":\"SYS_USER\",\"opId\":33801,\"opName\":\"18812345678\",\"createTime\":null,\"updateTime\":null,\"opPage\":null,\"selectAll\":true,\"orderParam\":{\"sk\":\"\",\"enable\":null,\"total\":null,\"start\":null,\"size\":null,\"sorts\":null,\"orderNo\":\"\",\"siteId\":\"\"},\"corpName\":\"\"}";

        SettlementDto param = JsonUtils.fromJson(json, SettlementDto.class);
        param.setSettlementType(SettlementType.POSTPAID);

        String req = JsonUtils.toJsonString(param); // 请求体
        log.info("req param = {}", JsonUtils.toJsonString(param));

        MockHttpServletRequestBuilder rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON).content(req);

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(CorpSettlementRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("removeOrder4Settlement")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andDo(print())
                .andReturn();
    }

    @Test
    void test() {
        String json = "{\"billNo\":\"005t071616280016\",\"billName\":null,\"corpId\":1,\"billStatus\":\"INIT\",\"settlementType\":3,\"cfgId\":null,\"guaranteeKwh\":null,\"guaranteeElecFee\":null,\"guaranteeServFee\":null,\"subSettlementType\":11,\"subSettlement\":null,\"settlementTotalFee\":26.8000,\"settlementServFee\":13.4000,\"settlementElecFee\":13.4000,\"orderNum\":1,\"orderKwh\":13.4000,\"kwhOther\":0.0000,\"kwhJian\":0.0000,\"kwhFeng\":0.0000,\"kwhPing\":0.0000,\"kwhGu\":0.0000,\"orderElecFee\":8.21,\"orderServFee\":6.70,\"siteNameList\":[\"商丘民权铁塔能源中心充电站\"],\"siteNoList\":[\"HEN-XS-2020-0082\"],\"createTime\":null,\"updateTime\":null}";
        SettlementPo po = JsonUtils.fromJson(json, SettlementPo.class);
        po.setCfgId(54L);
        int i = settlementRwDs.insertOrUpdate(po);
        log.info(">>>> i = {}", i);

        SettlementVo vo = settlementRoDs.findByBillNo(po.getBillNo());
        log.info("vo = {}", JsonUtils.toJsonString(vo));
    }

    @Test
    void appendOrder2Settlement() throws Exception {
        String url = "/api/corp/appendOrder2Settlement";

        String json = "{\"billNo\":null,\"billName\":\"账单名称\",\"corpId\":1,\"billStatus\":null,\"settlementType\":null,\"cfgId\":54,\"guaranteeKwh\":null,\"guaranteeElecFee\":null,\"guaranteeServFee\":null,\"subSettlementType\":11,\"subSettlement\":null,\"settlementTotalFee\":null,\"settlementServFee\":null,\"settlementElecFee\":null,\"orderNum\":null,\"orderKwh\":null,\"kwhOther\":null,\"kwhJian\":null,\"kwhFeng\":null,\"kwhPing\":null,\"kwhGu\":null,\"orderElecFee\":null,\"orderServFee\":null,\"siteNameList\":null,\"siteNoList\":null,\"createTime\":null,\"updateTime\":null,\"opPage\":\"ADD\",\"selectAll\":false,\"orderNoList\":[\"270615470391\"],\"corpName\":\"上海鼎充新能源技术有限公司\"}";
        SettlementDto param = JsonUtils.fromJson(json, SettlementDto.class);
        String req = JsonUtils.toJsonString(param); // 请求体
        log.info("req param = {}", JsonUtils.toJsonString(param));

        MockHttpServletRequestBuilder rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON).content(req);

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(CorpSettlementRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("appendOrder2Settlement")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andDo(print())
                .andReturn();
    }

    @Test
    void settlementByBillNo() throws Exception {
        String url = "/api/corp/settlementByBillNo";

        MockHttpServletRequestBuilder rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("billNo", "9999999");

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(CorpSettlementRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("settlementByBillNo")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();
    }

    @Test
    void getSettlementByBillNo() throws Exception {
        String url = "/api/corp/getSettlementByBillNo";

        MockHttpServletRequestBuilder rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("billNo", "9999999");

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(CorpSettlementRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("getSettlementByBillNo")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();
    }

    @Test
    void removeSettlementByBillNo() throws Exception {
        String url = "/api/corp/removeSettlementByBillNo";

        MockHttpServletRequestBuilder rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("billNo", "BILL_NO");

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(CorpSettlementRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("removeSettlementByBillNo")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andDo(print())
                .andReturn();
    }

    @Test
    void checkCorpSettlementByRBlocUserId() throws Exception {
        String url = "/api/corp/checkCorpSettlementByRBlocUserId";

        MockHttpServletRequestBuilder rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("rBlocUserId", String.valueOf(506));

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(CorpSettlementRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("checkCorpSettlementByRBlocUserId")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andDo(print())
                .andReturn();
    }

    public static Date tomorrow() {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
        calendar.add(Calendar.DATE, 1);
        return calendar.getTime();
    }

    @Test
    void settlementCfgActive() throws Exception {
        String url = "/api/corp/settlementCfgActive";

        MockHttpServletRequestBuilder rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON);

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(CorpSettlementRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("settlementCfgActive")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andDo(print())
                .andReturn();
    }

    @Test
    void generateSettlement() throws Exception {
        String url = "/api/corp/generateSettlement";

        MockHttpServletRequestBuilder rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON);

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(CorpSettlementRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("generateSettlement")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andDo(print())
                .andReturn();
    }
}