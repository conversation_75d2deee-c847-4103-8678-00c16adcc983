package com.chargerlinkcar.core.rest;

import com.cdz360.biz.cus.rest.CardMgmRest;
import com.chargerlinkcar.core.BaseMockTest;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.InjectMocks;

/**
 * <AUTHOR>
 *
 * @since 2019/5/20
 **/
public class CardMgmRestTest extends BaseMockTest {
    @InjectMocks
    private CardMgmRest cardMgmRest;

    @BeforeEach
    public void setUp() throws Exception {
    }

    @AfterEach
    public void tearDown() throws Exception {
    }

//    @Test
//    public void queryCards() {
//
//        BaseResultEntity res = cardMgmRest.queryCards(null,null, null, null, null, null);
//        Integer code = res.getCode();
//        assertEquals(0, (int) res.getCode());
//
//    }
}