package com.chargerlinkcar.core.rest;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.cus.rest.BlocWalletRest;
import com.chargerlinkcar.core.BaseMockTest;
import com.chargerlinkcar.framework.common.domain.vo.BalanceRollbackParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.handler;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@WebAppConfiguration
class BlocWalletRestTest extends BaseMockTest {

    private MockMvc mockMvc;
    @Autowired
    private WebApplicationContext wac;

    @BeforeEach
    public void setUp() throws Exception {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    void test_rollbackBlocFrozenMoney() throws Exception {
        String paramJson = "{\"topCommId\":33421,\"balanceId\":10964,\"frozenAmount\":20.00,\"cusId\":67120,\"orderNo\":\"251209557194\"}";

        BalanceRollbackParam param = JsonUtils.fromJson(paramJson, BalanceRollbackParam.class);

        log.info("param = {}", JsonUtils.toJsonString(param));

        String url = "/api/blocWallet/rollbackBlocFrozenMoney";

        MockHttpServletRequestBuilder rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(param));

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(BlocWalletRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("rollbackBlocFrozenMoney")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
    }
}