package com.chargerlinkcar.core.rest;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.cus.rest.SiteBlacklistRest;
import com.cdz360.biz.model.cus.site.param.ListSiteBlacklistParam;
import com.cdz360.biz.model.cus.site.param.SiteBlacklistEnableParam;
import com.chargerlinkcar.core.BaseMockTest;
import com.chargerlinkcar.framework.common.domain.OrderOvertimeParkingBi;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.handler;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@Slf4j
@WebAppConfiguration
class SiteBlacklistRestTest extends BaseMockTest {

    private static final String SITE_ID = "abc123456789";
    private static final Long UID = 2020520L;

    @Test
    void find() throws Exception {
        ListSiteBlacklistParam param = new ListSiteBlacklistParam();
        param.setTotal(Boolean.TRUE);
        param.setSize(10)
                .setStart(0L);
        param.setSiteId(SITE_ID);

        String url = "/api/siteBlacklist/find";

        MockHttpServletRequestBuilder rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(param));

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(SiteBlacklistRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("find")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
    }

    @Test
    void enable() throws Exception {
        SiteBlacklistEnableParam param = new SiteBlacklistEnableParam();
        param.setSiteId(SITE_ID);
        param.setUid(UID);
        param.setEnable(Boolean.TRUE);

        String url = "/api/siteBlacklist/enable";

        MockHttpServletRequestBuilder rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(param));

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(SiteBlacklistRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("enable")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
    }

    @Test
    void userInSiteBlacklist() throws Exception {
        SiteBlacklistEnableParam param = new SiteBlacklistEnableParam();
        param.setSiteId(SITE_ID);
        param.setUid(UID);

        String url = "/api/siteBlacklist/userInSiteBlacklist";

        MockHttpServletRequestBuilder rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(param));

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(SiteBlacklistRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("userInSiteBlacklist")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
    }

    @Test
    void overtimeParkingBi() throws Exception {
        String url = "/api/siteBlacklist/overtimeParkingBi";

        List<OrderOvertimeParkingBi> biList = new ArrayList<>();
        OrderOvertimeParkingBi bi = new OrderOvertimeParkingBi();
        bi.setSiteId(SITE_ID)
                .setUid(UID)
                .setNum(1);

        MockHttpServletRequestBuilder rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(biList));

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(SiteBlacklistRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("overtimeParkingBi")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
    }
}