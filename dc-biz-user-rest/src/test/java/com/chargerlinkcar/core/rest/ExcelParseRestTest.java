package com.chargerlinkcar.core.rest;

import com.cdz360.base.utils.JsonUtils;
import com.chargerlinkcar.core.BaseMockTest;
import com.chargerlinkcar.framework.common.domain.BaseResultEntity;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;


/**
 * <AUTHOR>
 */
public class ExcelParseRestTest extends BaseMockTest {

    private static final String ORIGINAL_FILE_NAME_PREFIX = "test";
    @Autowired
    protected WebApplicationContext wac;
    private MockMvc mockMvc;
    private File file;
    private File errFile;

    private String fileName;
    private String errFileName;

    // 静态数据

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @BeforeEach
    public void initTest() throws IOException {
        // 创建临时文件
        file = File.createTempFile(ORIGINAL_FILE_NAME_PREFIX, ".xls");
        file.deleteOnExit();
        errFile = File.createTempFile(ORIGINAL_FILE_NAME_PREFIX, ".xsx");
        errFile.deleteOnExit();

        System.out.println("file path: " + file.getAbsolutePath());
        System.out.println("file name: " + file.getName());
        System.out.println("error file path: " + errFile.getAbsolutePath());
        System.out.println("error file name: " + errFile.getName());

        // 文件名获取
        fileName = file.getName();
        errFileName = errFile.getName();

        // init excel context
        initExcelContext(file);
        initExcelContext(errFile);
    }

    @AfterEach
    public void clearTest() {
        if (null != file) {
            file.delete();
        }

        if (null != errFile) {
            errFile.delete();
        }
    }

    /**
     * 初始化文件内容
     *
     * @param file
     */
    private void initExcelContext(File file) {
        // 定义表头
        String[] title = {"物理卡号", "逻辑卡号", "密钥"};

        // 创建excel工作簿
        HSSFWorkbook workbook = new HSSFWorkbook();

        // 创建工作表sheet
        HSSFSheet sheet = workbook.createSheet();

        // 创建第一行
        HSSFRow row = sheet.createRow(0);
        HSSFCell cell = null;
        // 插入第一行数据的表头
        for (int i = 0; i < title.length; i++) {
            cell = row.createCell(i);
            cell.setCellValue(title[i]);
        }

        // 写入数据
        for (int i = 1; i <= 10; i++) {
            HSSFRow nrow = sheet.createRow(i);

            // 物理卡号
            HSSFCell ncell = nrow.createCell(0);
            ncell.setCellValue("" + i);

            // 逻辑卡号
            ncell = nrow.createCell(1);
            ncell.setCellValue("user" + i);

            // 密钥
            ncell = nrow.createCell(2);
            ncell.setCellValue("key" + i);
        }

        // 将内容写入到文件
        try {
            FileOutputStream stream = new FileOutputStream(file);
            workbook.write(stream);
            stream.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void parseExcel() throws Exception {
        // 正常处理流程
        ResultActions resultActions = mockMvc.perform(
            MockMvcRequestBuilders.multipart("/api/excel/parse")
                //.fileUpload("/api/excel/parse")
                .file(new MockMultipartFile(
                    "file",
                    fileName,
                    "multipart/form-data",
                    new FileInputStream(file))));

        MvcResult mvcResult = resultActions
            .andDo(MockMvcResultHandlers.print())
            .andExpect(MockMvcResultMatchers.status().isOk())
            .andReturn();

        // 转成对象
        String result = mvcResult.getResponse().getContentAsString();
        BaseResultEntity res = JsonUtils.fromJson(result, BaseResultEntity.class);
        Assertions.assertEquals(0, (int) res.getCode());

        // 异常梳理流程
        resultActions = mockMvc.perform(
            MockMvcRequestBuilders.multipart("/api/excel/parse")
                //.fileUpload("/api/excel/parse")
                .file(new MockMultipartFile(
                    "file",
                    errFileName,
                    "multipart/form-data",
                    new FileInputStream(errFile))));

        mvcResult = resultActions
            .andDo(MockMvcResultHandlers.print())
            .andExpect(MockMvcResultMatchers.status().isOk())
            .andReturn();

        result = mvcResult.getResponse().getContentAsString();
        res = JsonUtils.fromJson(result, BaseResultEntity.class);
        Assertions.assertEquals(1, (int) res.getCode());
    }
}