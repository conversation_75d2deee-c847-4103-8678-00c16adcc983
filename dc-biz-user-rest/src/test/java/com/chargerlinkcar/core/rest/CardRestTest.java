package com.chargerlinkcar.core.rest;

import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.cus.domain.Balance;
import com.cdz360.biz.cus.domain.User;
import com.cdz360.biz.cus.domain.vo.Card4ManagerVo;
import com.cdz360.biz.cus.repository.BlocUserMapper;
import com.cdz360.biz.cus.repository.CardMapper;
import com.cdz360.biz.cus.repository.RBlocUserMapper;
import com.cdz360.biz.cus.repository.UserMapper;
import com.cdz360.biz.cus.repository.VinMapper;
import com.cdz360.biz.cus.service.PublishCorpUserInfoService;
import com.chargerlinkcar.core.BaseMockTest;
import com.chargerlinkcar.framework.common.domain.BaseResultEntity;
import com.chargerlinkcar.framework.common.domain.BaseResultEntityTmpl;
import com.chargerlinkcar.framework.common.domain.BlocUserDto;
import com.chargerlinkcar.framework.common.domain.vo.Card;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.domain.vo.UserVo;
import com.chargerlinkcar.framework.common.domain.vo.VinDto;
import com.chargerlinkcar.framework.common.domain.vo.VinParam;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultHandlers;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.web.context.WebApplicationContext;


@Transactional
public class CardRestTest extends BaseMockTest {

    @Autowired
    protected WebApplicationContext wac;

    private MockMvc mockMvc;

    @Autowired
    private CardMapper cardMapper;
    @Autowired
    private VinMapper vinMapper;
    //@Autowired
    //private BalanceMapper balanceMapper;
    @Autowired
    private RBlocUserMapper rBlocUserMapper;
    @Autowired
    private PublishCorpUserInfoService publishCorpUserInfoService;
    @Autowired
    private UserMapper userMapper;
    //    @Autowired
//    private BlocWalletMapper blocWalletMapper;
    @Autowired
    private BlocUserMapper blocUserMapper;


    @Value("${iot.fee.max:5}")
    private BigDecimal IOT_FEE_MAX;//充电冻结金额

    private Integer PAY_TYPE_1 = 1;
    private Integer PAY_TYPE_2 = 2;

    private String card1 = "10000000000000000";
    private String card2 = "10000000000000001";
    private Long balance1 = 999L;
    private Long frozenAmount1 = 99L;
    private BigDecimal balance2 = BigDecimal.valueOf(99999L);
    private BigDecimal frozenAmount2 = BigDecimal.valueOf(9999L);
    private BigDecimal frozenAmount2_2 = BigDecimal.valueOf(9L);

    private BigDecimal limitMoney2 = BigDecimal.valueOf(1000000L);
    private BigDecimal limitMoney4 = BigDecimal.valueOf(2000000L);

    private String vin1 = "123456789AAAAAAAA";
    private String vin2 = "123456789BBBBBBBB";
    private Long balance3 = 777L;
    private Long frozenAmount3 = 77L;
    private BigDecimal balance4 = BigDecimal.valueOf(77777L);
    private BigDecimal frozenAmount4 = BigDecimal.valueOf(7777L);
    private BigDecimal frozenAmount4_2 = BigDecimal.valueOf(7L);

    private Long commId = 111111L;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
        {
            User user1 = new User();
            user1.setDefaultPayType(PAY_TYPE_1);
            userMapper.insertSelective(user1);

            Balance balance0 = new Balance();
            balance0.setBalance(balance1);
            balance0.setFrozenAmount(frozenAmount1);
            balance0.setUserId(user1.getId());
            //balanceMapper.insertSelective(balance0);

            user1.setBalanceId(balance0.getId());
            userMapper.updateByUidSelective(user1);

            Card card1 = new Card();
            card1.setCardNo(this.card1);
            card1.setUserId(user1.getId());
            cardMapper.insertSelective(card1);
        }
        //================================================
        {
            User user2 = new User();
            user2.setDefaultPayType(PAY_TYPE_2);
            userMapper.insertSelective(user2);

            BlocUserDto blocUserDto = new BlocUserDto();
            blocUserDto.setCommId(commId);
            blocUserDto.setTopCommId(commId);
            blocUserDto.setUid(commId);
            blocUserMapper.insertSelective(blocUserDto);

//            BlocWalletDto blocWalletDto = new BlocWalletDto();
//            blocWalletDto.setStatus(0);
//            blocWalletDto.setBalance(balance2);
//            blocWalletDto.setFrozenAmount(frozenAmount2);
//            blocWalletDto.setBloc_user_id(blocUserDto.getId());
//            blocWalletMapper.insertSelective(blocWalletDto);

            RBlocUser rBlocUser = new RBlocUser();
            rBlocUser.setBalance(balance2);
            rBlocUser.setUserId(user2.getId());
            rBlocUser.setFrozenAmount(frozenAmount2_2);
            rBlocUser.setBlocUserId(blocUserDto.getId());
            rBlocUser.setLimitMoney(limitMoney2);
            rBlocUserMapper.insertRBlocUser(rBlocUser);
            publishCorpUserInfoService.publishCorpUserInfo(rBlocUser);

            user2.setBalanceId(rBlocUser.getId());
            userMapper.updateByUidSelective(user2);

            UserVo userVo = userMapper.findUserInfoByUid(user2.getId(), commId);

            Card card2 = new Card();
            card2.setCardNo(this.card2);
            card2.setUserId(user2.getId());
            cardMapper.insertSelective(card2);
        }
        //================================================
        {
            User user3 = new User();
            user3.setDefaultPayType(PAY_TYPE_1);
            userMapper.insertSelective(user3);

            Balance balance2 = new Balance();
            balance2.setBalance(balance3);
            balance2.setFrozenAmount(frozenAmount3);
            balance2.setUserId(user3.getId());
            //balanceMapper.insertSelective(balance2);

            user3.setBalanceId(balance2.getId());
            userMapper.updateByUidSelective(user3);

            VinParam vinParam = new VinParam();
            vinParam.setVin(vin1);
            vinParam.setUserId(user3.getId());
            vinMapper.insert(vinParam);
        }
        //================================================
        {
            User user4 = new User();
            user4.setDefaultPayType(PAY_TYPE_2);
            userMapper.insertSelective(user4);

            BlocUserDto blocUserDto = new BlocUserDto();
            blocUserDto.setCommId(commId);
            blocUserDto.setTopCommId(commId);
            blocUserDto.setUid(commId);
            blocUserMapper.insertSelective(blocUserDto);

//            BlocWalletDto blocWalletDto = new BlocWalletDto();
//            blocWalletDto.setStatus(0);
//            blocWalletDto.setBalance(balance4);
//            blocWalletDto.setFrozenAmount(frozenAmount4);
//            blocWalletDto.setBloc_user_id(blocUserDto.getId());
//            blocWalletMapper.insertSelective(blocWalletDto);

            RBlocUser rBlocUser1 = new RBlocUser();
            rBlocUser1.setBalance(balance4);
            rBlocUser1.setUserId(user4.getId());
            rBlocUser1.setFrozenAmount(frozenAmount4_2);
            rBlocUser1.setBlocUserId(blocUserDto.getId());
            rBlocUser1.setLimitMoney(limitMoney4);
            rBlocUserMapper.insertRBlocUser(rBlocUser1);
            publishCorpUserInfoService.publishCorpUserInfo(rBlocUser1);

            user4.setBalanceId(rBlocUser1.getId());
            userMapper.updateByUidSelective(user4);

            UserVo userVo2 = userMapper.findUserInfoByUid(user4.getId(), commId);

            VinParam vinParam1 = new VinParam();
            vinParam1.setVin(vin2);
            vinParam1.setUserId(user4.getId());
            vinMapper.insert(vinParam1);
        }
    }

    @Test
    public void getCardsByUserIdAndCorpIds() {

        Long userId = 9090L;
        Long corpId1 = 9999L;
        Long corpId2 = 9998L;
        List<Long> corpIds = List.of(corpId1, corpId2);

        VinParam vinParam1 = new VinParam();
        vinParam1.setVin("qqqqqqqqqqqqqqq");
        vinParam1.setUserId(9090L);
        vinParam1.setCorpId(corpId1);
        vinMapper.insert(vinParam1);

        VinParam vinParam2 = new VinParam();
        vinParam2.setVin("xvxvxvxvxvxvxvxvxvxv");
        vinParam2.setUserId(9090L);
        vinParam2.setCorpId(corpId1);
        vinMapper.insert(vinParam2);

        List<VinDto> list = vinMapper.getCardsByUserIdAndCorpIds(userId, corpIds);
        Assert.isTrue(list.size() == 2, "not eq 2");
    }

    @Test
    public void batchAddCard() throws Exception {
        // static data
        List<Card4ManagerVo> voList = new ArrayList<>();

        // 数据为0情况
        ResultActions resultActions = mockMvc.perform(
            MockMvcRequestBuilders.post("/api/card/batchAddCard")
                .contentType(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(voList)));

        MvcResult mvcResult = resultActions
            .andDo(MockMvcResultHandlers.print())
            .andExpect(MockMvcResultMatchers.status().isOk())
            .andReturn();

        // 转成对象
        String result = mvcResult.getResponse().getContentAsString();
        BaseResultEntity res = JsonUtils.fromJson(result, BaseResultEntity.class);
        Assertions.assertEquals(1, (int) res.getCode());

        // 存在数据情况
    }

    @Test
    public void queryCardVinByCardNo_balance() throws Exception {
        ResultActions resultActions = mockMvc.perform(
            MockMvcRequestBuilders.post("/api/card/getCardAuth").param("cardNo", card1));

        MvcResult mvcResult = resultActions
            .andDo(MockMvcResultHandlers.print())
            .andExpect(MockMvcResultMatchers.status().isOk())
            .andReturn();

        // 转成对象
        String result = mvcResult.getResponse().getContentAsString();
        BaseResultEntityTmpl res = JsonUtils.fromJson(result, BaseResultEntityTmpl.class);
        Assertions.assertEquals(
            Math.min(balance1 - frozenAmount1, DecimalUtils.yuan2fen(IOT_FEE_MAX)),
            JsonUtils.fromJson(result).get("data").get("frozenAmount").asInt()
//            JsonUtils.fromJson(result).getJSONObject("data").getInteger("frozenAmount").intValue()
        );
    }

    @Test
    public void queryCardVinByCardNo_block_user() throws Exception {
        ResultActions resultActions = mockMvc.perform(
            MockMvcRequestBuilders.post("/api/card/getCardAuth").param("cardNo", card2));

        MvcResult mvcResult = resultActions
            .andDo(MockMvcResultHandlers.print())
            .andExpect(MockMvcResultMatchers.status().isOk())
            .andReturn();

        // 转成对象
        String result = mvcResult.getResponse().getContentAsString();
        BaseResultEntityTmpl res = JsonUtils.fromJson(result, BaseResultEntityTmpl.class);
        Assertions.assertEquals(0, (int) res.getCode());
        Assertions.assertEquals(Math.min(Math.min(
                DecimalUtils.yuan2fen(limitMoney2.subtract(balance2).subtract(frozenAmount2_2)),
                DecimalUtils.yuan2fen(balance2.subtract(frozenAmount2))), IOT_FEE_MAX.intValue()),
            JsonUtils.fromJson(result).get("data").get("frozenAmount").asInt()
//            JsonUtils.fromJson(result).getJSONObject("data").getInteger("frozenAmount").intValue()
        );
    }

    @Test
    public void vin_balance() throws Exception {
        ResultActions resultActions = mockMvc.perform(
            MockMvcRequestBuilders.post("/api/vin/getVinAuth").param("vin", vin1));

        MvcResult mvcResult = resultActions
            .andDo(MockMvcResultHandlers.print())
            .andExpect(MockMvcResultMatchers.status().isOk())
            .andReturn();

        // 转成对象
        String result = mvcResult.getResponse().getContentAsString();
        BaseResultEntity res = JsonUtils.fromJson(result, BaseResultEntity.class);
        Assertions.assertEquals(Math.min(balance3.intValue() - frozenAmount3.intValue(),
                DecimalUtils.yuan2fen(IOT_FEE_MAX)),
//            JsonUtils.fromJson(result).getJSONObject("data").getInteger("frozenAmount").intValue()
            JsonUtils.fromJson(result).get("data").get("frozenAmount").asInt()
        );
    }

    @Test
    public void vin_block_user() throws Exception {
        ResultActions resultActions = mockMvc.perform(
            MockMvcRequestBuilders.post("/api/vin/getVinAuth").param("vin", vin2));

        MvcResult mvcResult = resultActions
            .andDo(MockMvcResultHandlers.print())
            .andExpect(MockMvcResultMatchers.status().isOk())
            .andReturn();

        // 转成对象
        String result = mvcResult.getResponse().getContentAsString();
        BaseResultEntity res = JsonUtils.fromJson(result, BaseResultEntity.class);
        Assertions.assertEquals(0, (int) res.getCode());
        Assertions.assertEquals(Math.min(Math.min(
                DecimalUtils.yuan2fen(limitMoney4.subtract(balance4).subtract(frozenAmount4_2)),
                DecimalUtils.yuan2fen(balance4.subtract(frozenAmount4))), IOT_FEE_MAX.intValue()),
//            JsonUtils.fromJson(result).getJSONObject("data").getInteger("frozenAmount").intValue()
            JsonUtils.fromJson(result).get("data").get("frozenAmount").asInt()
        );
    }

    @Test
    public void queryCardVinByCardNo_failed() throws Exception {
        ResultActions resultActions = mockMvc.perform(
            MockMvcRequestBuilders.post("/api/card/getCardAuth").param("cardNo", "不会存在这个的"));

        MvcResult mvcResult = resultActions
            .andDo(MockMvcResultHandlers.print())
            .andExpect(MockMvcResultMatchers.status().isOk())
            .andReturn();

        // 转成对象
        String result = mvcResult.getResponse().getContentAsString();
        BaseResultEntityTmpl res = JsonUtils.fromJson(result, BaseResultEntityTmpl.class);
        Assertions.assertEquals(1, (int) res.getCode());
    }

    @Test
    public void getVinAuth_failed() throws Exception {
        ResultActions resultActions = mockMvc.perform(
            MockMvcRequestBuilders.post("/api/vin/getVinAuth").param("vin", "不会存在这个的"));

        MvcResult mvcResult = resultActions
            .andDo(MockMvcResultHandlers.print())
            .andExpect(MockMvcResultMatchers.status().isOk())
            .andReturn();

        // 转成对象
        String result = mvcResult.getResponse().getContentAsString();
        BaseResultEntityTmpl res = JsonUtils.fromJson(result, BaseResultEntityTmpl.class);
        Assertions.assertEquals(1, (int) res.getCode());
    }
}