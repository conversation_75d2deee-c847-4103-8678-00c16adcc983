package com.chargerlinkcar.core;


/*
@Slf4j
@ExtendWith(SpringExtension.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = UserApplication.class)
public class userTest {

    @Test
    public void userTest() {

        Map<String, Object> params = new HashMap<>();
        params.put("token","50c6b6e5e43d562d8b189ef5f2a55bbe");


        Map<String, String> headerParams = new HashMap<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("token","50c6b6e5e43d562d8b189ef5f2a55bbe");


        String merchantResult =  HttpPlusUtil.doPost("http://47.96.151.28:9003/api/info/token", JsonUtils.toJsonString(), headerParams);

    }
}*/