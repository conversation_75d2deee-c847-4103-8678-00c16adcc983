package com.chargerlinkcar.core.service.impl;

import com.cdz360.biz.cus.service.impl.BankCardServiceImpl;
import com.cdz360.biz.ds.cus.ro.wallet.ds.BankCardRoDs;
import com.cdz360.biz.ds.cus.rw.wallet.ds.BankCardRwDs;
import com.cdz360.biz.model.cus.wallet.param.AddBankCardParam;
import com.cdz360.biz.model.cus.wallet.vo.BankCardVo;
import com.chargerlinkcar.core.BaseMockTest;
import com.chargerlinkcar.framework.common.utils.UUIDUtils;
import com.github.pagehelper.Page;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * IBankCardServiceImplTest
 *
 * @since 2019/9/19 15:04
 * <AUTHOR>
 */
public class IBankCardServiceImplTest  extends BaseMockTest {

    @Autowired
    private BankCardRoDs bankCardRoDs;

    @Autowired
    private BankCardRwDs bankCardRwDs;

    @InjectMocks
    private BankCardServiceImpl bankCardServiceImpl;

    @BeforeEach
    public void init() {
        BankCardVo bankCard = new BankCardVo();
        bankCard.setCusId(123L).setCardNo(UUIDUtils.getRandom(true, 6)).setBankName("开户行").setOwnerName("持卡人姓名");
        BankCardVo result = this.bankCardRwDs.addBankCard(bankCard);
        Assertions.assertNotNull(result);
    }

    @Test
    public void updateBankCard(){
        List<BankCardVo> entity = bankCardRoDs.listBankCard(123L, null, null);

        AddBankCardParam bankCardVo = new AddBankCardParam();
        bankCardVo.setId(entity.get(0).getId());
        bankCardVo.setCusId(123L);
        bankCardVo.setCardNo(UUIDUtils.getRandom(true, 6));
        bankCardVo.setOwnerName("持卡人姓名1");
        bankCardVo.setBankName("发卡行名称1");
        bankCardVo.setUpdateTime(new Date());
        boolean result = bankCardRwDs.updateBankCard(bankCardVo);
        Assertions.assertTrue(result);
    }


    @Test
    public void deleteCardStatus(){

        List<BankCardVo> entity = bankCardRoDs.listBankCard(123L, null, null);
        bankCardRwDs.deleteBankCard(entity.get(0));
        BankCardVo bankCardVo = bankCardRwDs.getBankCard(entity.get(0).getId(),false);
        Assertions.assertTrue(bankCardVo.getEnable() == false);
    }

    @Test
    public void search() {

        Page<BankCardVo> page= new Page<>(0,10);
        List<BankCardVo> entity = bankCardRoDs.listBankCard(123L, null, null);
        Assertions.assertTrue(entity != null);
    }


}
