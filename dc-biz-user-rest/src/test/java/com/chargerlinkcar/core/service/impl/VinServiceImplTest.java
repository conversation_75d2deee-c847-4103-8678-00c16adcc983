package com.chargerlinkcar.core.service.impl;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.cus.client.TradingFeignClient;
import com.cdz360.biz.cus.domain.VinOrderCount;
import com.cdz360.biz.cus.repository.VinMapper;
import com.cdz360.biz.cus.service.MerchantService;
import com.cdz360.biz.cus.service.impl.VinServiceImpl;
import com.cdz360.biz.ds.cus.ro.mechant.ds.CommercialRoDs;
import com.cdz360.biz.model.cus.vin.param.VINCarNoParam;
import com.cdz360.biz.model.cus.vin.param.VinSearchParam;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import com.chargerlinkcar.core.BaseMockTest;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.domain.PaginationEntity;
import com.chargerlinkcar.framework.common.domain.vo.VinDto;
import com.chargerlinkcar.framework.common.domain.vo.VinParam;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.List;


public class VinServiceImplTest extends BaseMockTest {

    @Mock
    private VinMapper vinMapper;
    @Mock
    private CommercialRoDs commercialRoDs;
    @Mock
    private MerchantService merchantFeignClient;
    @Mock
    private TradingFeignClient tradingFeignClient;
    @InjectMocks
    private VinServiceImpl vinServiceImpl;

    @Test
    public void create() {
        Mockito.when(vinMapper.insert(Mockito.any())).thenReturn(1);
        Mockito.when(commercialRoDs.getCommerial(Mockito.anyLong())).thenReturn(new CommercialSimpleVo() {{
            setId(12345L);
        }});
        ObjectResponse res = vinServiceImpl.create(new VinParam() {
            {
                setSubCommId(99999L);
                setUserId(67676L);
                setVin("12345678901234567");
            }
        });
        Assertions.assertTrue(res.getStatus() == 0);
    }

    @Test
    public void delete() {
        Mockito.when(vinMapper.delete(Mockito.any(), Mockito.anyLong())).thenReturn(1);
        ObjectResponse res = vinServiceImpl.delete(0L);
        Assertions.assertTrue(res.getStatus() == 0);
    }

    @Test
    public void update() {
        Mockito.when(vinMapper.update(Mockito.any())).thenReturn(1);
        ObjectResponse res = vinServiceImpl.update(new VinParam());
        Assertions.assertTrue(res.getStatus() == 0);
    }

    @Test
    public void select() {
        List<Long> ret = new ArrayList<>();
        Mockito.when(merchantFeignClient.getCommIdListByToken(Mockito.anyString())).thenReturn(ret);

        VinDto vinDto = new VinDto();
        vinDto.setOrderCount(3);
        vinDto.setId(111L);
        vinDto.setVin("vinvinvin");
        List<VinDto> listX = new ArrayList<>();
        listX.add(vinDto);
        Mockito.when(vinMapper.select(Mockito.any())).thenReturn(listX);


        ArrayList<VinOrderCount> list = new ArrayList<>();
        VinOrderCount linkedHashMap = new VinOrderCount();
        linkedHashMap.setVin("vinvinvin");
        linkedHashMap.setOrderCount(Integer.valueOf(100));
        list.add(linkedHashMap);

        ListResponse<VinOrderCount> baseRes = new ListResponse<VinOrderCount>();
        baseRes.setData(list);
        Mockito.when(tradingFeignClient.queryOrderCountByVins(Mockito.any())).thenReturn(baseRes);

        ListResponse<VinDto> finalRes = vinServiceImpl.select("123", new VinSearchParam() {{
            setStart(10L);
            setSize(1);
            setUserId(0L);
        }});
        Assertions.assertTrue(finalRes.getStatus() == ResultConstant.RES_SUCCESS_CODE);
        Assertions.assertTrue(finalRes.getData() != null);

        Assertions.assertTrue(finalRes.getData() != null && finalRes.getData() instanceof PaginationEntity);
        Assertions.assertTrue(((PaginationEntity) finalRes.getData()).getRows().size() == 1);
    }

    @Test
    public void selectCarNoByVins() {
        java.util.List<VinDto> list = new ArrayList<>();
        list.add(new VinDto() {{
            setVin("11111");
        }});
        Mockito.when(vinMapper.selectCarNoByVins(Mockito.any())).thenReturn(list);
        VINCarNoParam param = new VINCarNoParam();
        param.setVinList(List.of("11111"));
        param.setCommIdChain("34474");
        ObjectResponse res = vinServiceImpl.selectCarNoByVins(param);
        Assertions.assertTrue(res.getStatus() == 0);
    }

    @Test
    public void selectAllVinList() {

        List<Long> ret = new ArrayList<Long>() {{
            add(33425L);
            add(33421L);
        }};
        Mockito.when(merchantFeignClient.getCommIdListByToken(Mockito.anyString())).thenReturn(ret);

        java.util.List<VinDto> list = new ArrayList<VinDto>() {{
            add(new VinDto() {{
                setVin("1008611");
                setCarNo("湘10086");
                setName("玛莎拉可");
                setStation("66666");
                setStatus(1);
                setUserId(61699L);
                setModifyBy(33425L);
                setSubCommId(33425L);
            }});
        }};

        Mockito.when(vinMapper.selectAllVinList(Mockito.any())).thenReturn(list);

        ListResponse res = vinServiceImpl.selectAllVinList("123", 61699L);
        Assertions.assertTrue(res.getData() != null);
        Assertions.assertTrue(res.getStatus() == 0);
    }
}