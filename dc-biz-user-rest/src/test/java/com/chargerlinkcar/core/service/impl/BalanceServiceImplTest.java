package com.chargerlinkcar.core.service.impl;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.UserType;
import com.cdz360.biz.cus.service.BalanceServiceImpl;
import com.cdz360.biz.model.finance.type.TaxStatus;
import com.cdz360.biz.model.finance.type.TaxType;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.cdz360.biz.model.trading.deposit.vo.DepositSourceType;
import com.cdz360.biz.model.trading.order.po.PayBillPo;
import com.chargerlinkcar.core.BaseMockTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

@Slf4j
class BalanceServiceImplTest extends BaseMockTest {

    @Autowired
    private BalanceServiceImpl balanceService;

    @Test
    void test_updateBalanceV2() {

        String str = "id=null, userId=66542, topCommId=null, commId=33421, commercialName=null, " +
                "orderId=null, channel=null, amount=15.59, freeAmount=9903.37, flowType=OUT_FLOW, " +
                "sourceType=MGM_WEB, payChannel=null, accountType=PERSONAL, accountCode=33421, " +
                "flowInAccountType=null, refBillNo=********************, taxType=UNKNOWN, taxStatus=NO," +
                " invoiceType=null, taxNo=null, expressStatus=null, expressCompany=null, expressNo=null," +
                " amountBefore=null, amountAfter=null, subject=null, clientIp=null, body=null, openId=null," +
                " payType=null, status=null, createTime=null, updateTime=null, tradeNo=null, payTime=null," +
                " notifyType=null, tradeType=null, bankType=null, outRefundNo=null, refundRecvAccout=null," +
                " refundRequestSource=null, remark=null, refundOldOrderId=null, chargeOrderNo=null," +
                " outAccountName=null, outBankName=null, outAccountNo=null, inAccountName=null," +
                " inBankName=null, inAccountNo=null, flowSeqNo=null, opUserType=SYS_USER, opUid=33801," +
                " opName=***********, cusName=null, cusPhone=null, userType=null";

        PayBillPo po = new PayBillPo();
        po.setUserId(66542L)
                .setCommId(33421L)
                .setAmount(new BigDecimal("15.59"))
                .setFreeAmount(new BigDecimal("9903.37"))
                .setFlowType(DepositFlowType.OUT_FLOW)
                .setSourceType(DepositSourceType.MGM_WEB)
                .setAccountType(PayAccountType.PERSONAL)
                .setAccountCode(33421L)
                .setRefBillNo("********************")
                .setTaxType(TaxType.UNKNOWN)
                .setTaxStatus(TaxStatus.NO)
                .setOpUserType(UserType.SYS_USER)
                .setOpUid(33801L)
                .setOpName("***********");

        balanceService.updateBalanceV2(po);
    }
}