package com.chargerlinkcar.core.service.impl;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.biz.cus.service.impl.WalletServiceImpl;
import com.cdz360.biz.ds.cus.rw.wallet.ds.RefundOrderRwDs;
import com.cdz360.biz.model.trading.cus.param.CusPayBillListParam;
import com.cdz360.biz.model.trading.cus.param.CusRefundAllParam;
import com.cdz360.biz.model.trading.cus.param.CusRefundOrderListParam;
import com.cdz360.biz.model.trading.cus.vo.CusPayBillVo;
import com.cdz360.biz.model.trading.cus.vo.CusRefundOrderVo;
import com.chargerlinkcar.core.BaseMockTest;
import com.chargerlinkcar.framework.common.domain.pay.RefundParams;
import com.chargerlinkcar.framework.common.service.PayBillIdGenerator;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
class WalletServiceImplTest extends BaseMockTest {

    @Autowired
    private WalletServiceImpl walletService;

    @Autowired
    private RefundOrderRwDs refundOrderRwDs;

    @Autowired
    private PayBillIdGenerator payBillIdGenerator;

    @Test
    void test_getCusPayBillList() {
        // 通过微信小程序对个人账户新增一条充值记录
        // 然后检查 t_pay_bill 和 资金块是否存在对应的记录

        CusPayBillListParam param = new CusPayBillListParam();
        param.setUid(66750L)
                .setTopCommId(33421L);

        // 微信小程序
        param.setClientType(AppClientType.WX_LITE);
        List<CusPayBillVo> cusPayBillList = walletService.getCusPayBillList(param, null);
        log.info("记录个数: client = {}, size = {}", param.getClientType().getDesc(), cusPayBillList.size());

        log.info("可退款数: client = {}, size = {}", param.getClientType().getDesc(), cusPayBillList.stream()
                .filter(vo -> DecimalUtils.gtZero(vo.getCanRefundAmount())).count());

        log.info("不可退款数: client = {}, size = {}", param.getClientType().getDesc(), cusPayBillList.stream()
                .filter(vo -> DecimalUtils.isZero(vo.getCanRefundAmount())).count());

        // 支付宝小程序
        param.setClientType(AppClientType.ALIPAY_LITE);
        cusPayBillList = walletService.getCusPayBillList(param, null);
        log.info("记录个数: client = {}, size = {}", param.getClientType().getDesc(), cusPayBillList.size());

        log.info("可退款数: client = {}, size = {}", param.getClientType().getDesc(), cusPayBillList.stream()
                .filter(vo -> DecimalUtils.gtZero(vo.getCanRefundAmount())).count());

        log.info("不可退款数: client = {}, size = {}", param.getClientType().getDesc(), cusPayBillList.stream()
                .filter(vo -> DecimalUtils.isZero(vo.getCanRefundAmount())).count());

        // IOS
        param.setClientType(AppClientType.IOS_APP);
        cusPayBillList = walletService.getCusPayBillList(param, null);
        log.info("记录个数: client = {}, size = {}", param.getClientType().getDesc(), cusPayBillList.size());

        log.info("可退款数: client = {}, size = {}", param.getClientType().getDesc(), cusPayBillList.stream()
                .filter(vo -> DecimalUtils.gtZero(vo.getCanRefundAmount())).count());

        log.info("不可退款数: client = {}, size = {}", param.getClientType().getDesc(), cusPayBillList.stream()
                .filter(vo -> DecimalUtils.isZero(vo.getCanRefundAmount())).count());

        // 安卓
        param.setClientType(AppClientType.ANDROID_APP);
        cusPayBillList = walletService.getCusPayBillList(param, null);
        log.info("记录个数: client = {}, size = {}", param.getClientType().getDesc(), cusPayBillList.size());

        log.info("可退款数: client = {}, size = {}", param.getClientType().getDesc(), cusPayBillList.stream()
                .filter(vo -> DecimalUtils.gtZero(vo.getCanRefundAmount())).count());

        log.info("不可退款数: client = {}, size = {}", param.getClientType().getDesc(), cusPayBillList.stream()
                .filter(vo -> DecimalUtils.isZero(vo.getCanRefundAmount())).count());
    }

    @Test
    void test_getRefundOrderRecord() {
//        RefundOrderPo po = new RefundOrderPo();
//        po.setSeqNo(CardConstants.TK + payBillIdGenerator.getNextPayBillId())
//                .setAmount(BigDecimal.TEN)
//                .setBalance(BigDecimal.TEN)
//                .setStatus(RefundStatus.FINISH)
//                .setCusId(66620L)
//                .setTopCommId(34490L)
//                .setAppType(AppClientType.WX_LITE)
//                .setFlowInAccountType(FlowInAccountType.TENPAY)
//                .setAmountBefore(BigDecimal.TEN)
//                .setCostBefore(BigDecimal.TEN)
//                .setAmountAfter(BigDecimal.TEN);
//        log.info("param = {}", po);
//        refundOrderRwDs.addRefundOrder(po);

        CusRefundOrderListParam param = new CusRefundOrderListParam();
        param.setUid(66750L)
                .setTopCommId(33421L)
                .setStart(0L)
                .setSize(100);

        // 微信小程序
        param.setClientType(AppClientType.WX_LITE);
        List<CusRefundOrderVo> refundOrderRecord = walletService.getRefundOrderRecord(param);
        log.info("记录个数: client = {}, size = {}", param.getClientType().getDesc(), refundOrderRecord.size());

        // 支付宝小程序
        param.setClientType(AppClientType.ALIPAY_LITE);
        refundOrderRecord = walletService.getRefundOrderRecord(param);
        log.info("记录个数: client = {}, size = {}", param.getClientType().getDesc(), refundOrderRecord.size());

        // IOS
        param.setClientType(AppClientType.IOS_APP);
        refundOrderRecord = walletService.getRefundOrderRecord(param);
        log.info("记录个数: client = {}, size = {}", param.getClientType().getDesc(), refundOrderRecord.size());

        // 安卓
        param.setClientType(AppClientType.ANDROID_APP);
        refundOrderRecord = walletService.getRefundOrderRecord(param);
        log.info("记录个数: client = {}, size = {}", param.getClientType().getDesc(), refundOrderRecord.size());
    }

    @Test
    void test_refundAll() {
        CusRefundAllParam param = new CusRefundAllParam();
        param.setUid(66764L)
                .setTopCommId(33421L)
                .setClientType(AppClientType.WX_LITE);
        int i = walletService.refundAll(param);
        log.info("result = {}", i);

        // 退款完成后，需要确认 t_pay_bill 和 t_refund_order 中的记录
        // 资金块中的数据是否修改
    }

    /**
     * 模拟退款失败测试
     */
    @Test
    void test_refundFail() {
        // 在"walletService.refundAll"中注释调用微信退款逻辑
        // 使用固定异常返回，看看结果返回情况

        /*
        // 退款结果处理
//            ObjectResponse<RefundRes> res = this.payBillRefund(refundParams);

        // 测试退款失败异常情况
        ObjectResponse<RefundRes> res = RestUtils.buildObjectResponse(new RefundRes());
        res.setStatus(ResultConstant.RES_FAIL_CODE);
         */

        CusRefundAllParam param = new CusRefundAllParam();
        param.setUid(66622L)
                .setTopCommId(34490L);
        int i = walletService.refundAll(param);
        log.info("result = {}", i);
    }

    @Test
    void test_cusRefundFail() {
        RefundParams params = new RefundParams();
        params.setUid(66549L);
        params.setTopCommId(33421L);
        params.setOutTradeNo("CZ202003081311040594");
        params.setTradeNo("4200000487202003082930877713");
        params.setRefundAmount(new BigDecimal("0.01"));
        params.setOutRefundNo("TK202003081311210595"); // t_refund_order 中的 seq

//        walletService.cusRefundFail(params);
    }
}