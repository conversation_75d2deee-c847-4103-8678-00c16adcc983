package com.chargerlinkcar.core.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.cus.client.AuthCenterFeignClient;
import com.cdz360.biz.cus.client.DataCoreFeignClient;
import com.cdz360.biz.cus.service.CorpBizService;
import com.cdz360.biz.cus.service.CorpSettlementService;
import com.cdz360.biz.cus.service.RBlocUserService;
import com.cdz360.biz.cus.service.settlement.SettlementDealWithService;
import com.cdz360.biz.ds.cus.ro.settlement.ds.SettlementCfgRoDs;
import com.cdz360.biz.ds.cus.ro.settlement.ds.SettlementRoDs;
import com.cdz360.biz.ds.cus.rw.settlement.ds.SettlementCfgRwDs;
import com.cdz360.biz.ds.cus.rw.settlement.ds.SettlementRwDs;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.settlement.dto.SettlementDto;
import com.cdz360.biz.model.cus.settlement.param.ListSettlementParam;
import com.cdz360.biz.model.cus.settlement.param.UpdateCorpSettlementParam;
import com.cdz360.biz.model.cus.settlement.po.SettlementCfgPo;
import com.cdz360.biz.model.cus.settlement.type.SettlementCfgStatusEnum;
import com.cdz360.biz.model.cus.settlement.vo.CorpSettlementCfgVo;
import com.cdz360.biz.model.cus.settlement.vo.SettlementVo;
import com.chargerlinkcar.core.BaseMockTest;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.service.SettlementNoGenerator;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
class CorpSettlementServiceTest extends BaseMockTest {

    @Mock
    private SettlementCfgRoDs settlementCfgRoDs;

    @Mock
    private SettlementCfgRwDs settlementCfgRwDs;

    @Mock
    private SettlementRwDs settlementRwDs;

    @Mock
    private SettlementRoDs settlementRoDs;

    @Mock
    private RBlocUserService rBlocUserService;

    @Mock
    private CorpBizService corpBizService;

    @Mock
    private AuthCenterFeignClient authCenterFeignClient;

    @Mock
    private DataCoreFeignClient dataCoreFeignClient;

    @Mock
    private SettlementNoGenerator settlementNoGenerator;

    @Mock
    private SettlementDealWithService settlementDealWithService;

    @InjectMocks
    private CorpSettlementService corpSettlementService;

    private final static Long TOP_COMM_ID = 34474L;
    private final static Long CORP_ID = 1L;
    private final static Long UID = 123L;

    private final static CorpPo CORP_PO = new CorpPo();

    @BeforeEach
    void init() {
        // 企业信息
        CORP_PO.setTopCommId(TOP_COMM_ID);
        CORP_PO.setId(CORP_ID);
        CORP_PO.setUid(UID);
        CORP_PO.setSettlementType(SettlementType.POSTPAID);

        Mockito.when(corpBizService.getCorp(Mockito.any())).thenReturn(CORP_PO);
    }

    @Test
    void getSettlementCfg() {
        // 找出该企业有效的记录(有且仅有一条)
        // status == ACTIVE
        SettlementCfgPo activeCfg = new SettlementCfgPo();
        activeCfg.setStatus(SettlementCfgStatusEnum.ACTIVE);
        activeCfg.setCorpId(CORP_ID);
        activeCfg.setActiveDate(new Date());
        Mockito.when(settlementCfgRoDs.findByCorpId(CORP_ID, SettlementCfgStatusEnum.ACTIVE))
                .thenReturn(activeCfg);

        // 找出企业还未生效的记录(有且仅有一条)
        // status == INACTIVE
        SettlementCfgPo inactiveCfg = new SettlementCfgPo();
        inactiveCfg.setStatus(SettlementCfgStatusEnum.ACTIVE);
        inactiveCfg.setCorpId(CORP_ID);
        activeCfg.setActiveDate(tomorrow());
        Mockito.when(settlementCfgRoDs.findByCorpId(CORP_ID, SettlementCfgStatusEnum.INACTIVE))
                .thenReturn(inactiveCfg);

        CorpSettlementCfgVo cfg = corpSettlementService.getSettlementCfg(CORP_ID);
        log.info("cfg = {}", JsonUtils.toJsonString(cfg));
    }

    @Test
    void updateSettlementCfg() {
        Mockito.when(settlementCfgRwDs.insert(Mockito.any())).thenReturn(1);
        Mockito.when(authCenterFeignClient.updateBlocUser(Mockito.any())).thenReturn(RestUtils.success());

        Mockito.when(settlementCfgRwDs.expiredCfg(Mockito.anyList())).thenReturn(1);

        // 将 INACTIVE 的配置逻辑删除
        Mockito.when(settlementCfgRwDs.removeByCorpId(Mockito.anyLong(), Mockito.any())).thenReturn(1);

        UpdateCorpSettlementParam param = new UpdateCorpSettlementParam();
        ObjectResponse<CorpPo> result = corpSettlementService.updateSettlementCfg(param);
        log.info("result = {}", result.getData());
    }

    @Test
    void findSettlementList() {
        SettlementVo vo = new SettlementVo();
        vo.setBillNo("TEST_12345678910");
        vo.setCorpId(CORP_ID);
        ListResponse<SettlementVo> result = new ListResponse<SettlementVo>();
        result.setData(List.of(vo));

        ListSettlementParam param = new ListSettlementParam();
        param.setCorpId(CORP_ID);
        Mockito.when(settlementRoDs.findAll(param)).thenReturn(result);

        ListResponse<SettlementVo> settlementList = corpSettlementService.findSettlementList(param);
        log.info(">>> {}", JsonUtils.toJsonString(settlementList));
    }

    @Test
    void getSettlementByBillNo() {
        String billNo = "1234567890";
        SettlementVo vo = new SettlementVo();
        vo.setBillNo(billNo);
        Mockito.when(settlementRoDs.findByBillNo(billNo)).thenReturn(vo);

        SettlementVo result = corpSettlementService.getSettlementByBillNo(billNo);
        assertNotNull(result);
        assertEquals(billNo, result.getBillNo(), "账单号");
    }

    @Test
    void removeSettlementByBillNo() {
        String billNo = "1234567890";

        Mockito.when(settlementRwDs.removeByBillNo(billNo)).thenReturn(1);

        Mockito.when(dataCoreFeignClient.clearBillNo(Mockito.any()))
                .thenReturn(RestUtils.buildObjectResponse(1));

        Integer i = corpSettlementService.removeSettlementByBillNo(billNo);
        log.info("i = {}", i);
    }

    @Test
    void checkCorpSettlementByRBlocUserId() {
        CORP_PO.setTopCommId(TOP_COMM_ID);
        CORP_PO.setId(CORP_ID);
        CORP_PO.setUid(UID);
        CORP_PO.setSettlementType(SettlementType.BALANCE);
        Mockito.when(corpBizService.getCorp(CORP_ID)).thenReturn(CORP_PO);

        RBlocUser blocUser = new RBlocUser();
        blocUser.setId(CORP_ID);
        blocUser.setBlocUserId(CORP_ID);
        Mockito.when(rBlocUserService.findRBlocUserById(CORP_ID, false))
                .thenReturn(blocUser);

        Boolean result = corpSettlementService.checkCorpSettlementByRBlocUserId(CORP_ID);
        assertFalse(result, "企业不是后付费");

        // ----------------------------------------
        CORP_PO.setSettlementType(SettlementType.POSTPAID);
        Mockito.when(corpBizService.getCorp(CORP_ID)).thenReturn(CORP_PO);

        // 找出该企业有效的记录(有且仅有一条)
        // status == ACTIVE
        SettlementCfgPo activeCfg = new SettlementCfgPo();
        activeCfg.setStatus(SettlementCfgStatusEnum.ACTIVE);
        activeCfg.setCorpId(CORP_ID);
        activeCfg.setActiveDate(new Date());
        Mockito.when(settlementCfgRoDs.findByCorpId(CORP_ID, SettlementCfgStatusEnum.ACTIVE))
                .thenReturn(activeCfg);

        // 找出企业还未生效的记录(有且仅有一条)
        // status == INACTIVE
        SettlementCfgPo inactiveCfg = new SettlementCfgPo();
        inactiveCfg.setStatus(SettlementCfgStatusEnum.ACTIVE);
        inactiveCfg.setCorpId(CORP_ID);
        activeCfg.setActiveDate(new Date());
        Mockito.when(settlementCfgRoDs.findByCorpId(CORP_ID, SettlementCfgStatusEnum.INACTIVE))
                .thenReturn(inactiveCfg);

        result = corpSettlementService.checkCorpSettlementByRBlocUserId(CORP_ID);
        assertTrue(result, "企业是后付费");
    }

    @Test
    void settlementCfgActive() {

        SettlementCfgPo po = new SettlementCfgPo();
        po.setCorpId(CORP_ID);
        List<SettlementCfgPo> result = List.of(po);
        Mockito.when(settlementCfgRoDs.needActiveCfgList(Mockito.any())).thenReturn(result);

        Mockito.when(authCenterFeignClient.updateBlocUser(Mockito.any()))
                .thenReturn(RestUtils.success());

        corpSettlementService.settlementCfgActive();
    }

    @Test
    void generateSettlement() {
        SettlementCfgPo po = new SettlementCfgPo();
        po.setCorpId(CORP_ID);
        List<SettlementCfgPo> result = List.of(po);
        Mockito.when(settlementCfgRoDs.getNeedAutoGenSettlement(Mockito.anyInt())).thenReturn(result);

        Mockito.when(settlementNoGenerator.next())
                .thenReturn(UUID.randomUUID().toString()
                        .replaceAll("-", "").substring(0, 16));

        // status == ACTIVE
        SettlementCfgPo activeCfg = new SettlementCfgPo();
        activeCfg.setStatus(SettlementCfgStatusEnum.ACTIVE);
        activeCfg.setCorpId(CORP_ID);
        activeCfg.setActiveDate(new Date());
        Mockito.when(settlementCfgRoDs.findByCorpId(CORP_ID, SettlementCfgStatusEnum.ACTIVE))
                .thenReturn(activeCfg);

        Mockito.when(dataCoreFeignClient.settlementOrderBi(Mockito.any()))
                .thenReturn(RestUtils.buildObjectResponse(new SettlementDto()));

        Mockito.when(settlementRwDs.insertOrUpdate(Mockito.any())).thenReturn(1);

        corpSettlementService.generateSettlement();
    }

    @Test
    void removeOrder4Settlement() {
        SettlementDto dto = new SettlementDto();
        dto.setOpPage(SettlementDto.OpPage.ADD);
        corpSettlementService.removeOrder4Settlement(dto);
    }

    @Test
    void appendOrder2Settlement() {
        SettlementDto dto = new SettlementDto();
        dto.setOpPage(SettlementDto.OpPage.ADD);

        Mockito.when(settlementNoGenerator.next())
                .thenReturn(UUID.randomUUID().toString()
                        .replaceAll("-", "").substring(0, 16));

        Mockito.when(dataCoreFeignClient.settlementOrderBi(Mockito.any()))
                .thenReturn(RestUtils.buildObjectResponse(new SettlementDto()));

        // 添加页面的操作
        corpSettlementService.appendOrder2Settlement(dto);

        // 编辑页面的操作
        dto.setOpPage(SettlementDto.OpPage.EDIT);
        corpSettlementService.appendOrder2Settlement(dto);
    }

    @Test
    void settlementByBillNo() {
        String billNo = "1234567890";
        SettlementVo vo = new SettlementVo();
        vo.setBillNo(billNo);
        Mockito.when(settlementRoDs.findByBillNo(billNo)).thenReturn(vo);

        Mockito.when(settlementRwDs.updateByBillNo(Mockito.any())).thenReturn(1);
        Mockito.when(dataCoreFeignClient.settlementByBillNo(Mockito.anyString()))
                .thenReturn(RestUtils.buildObjectResponse(1));

        corpSettlementService.settlementByBillNo(billNo);
    }

    public static Date tomorrow() {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
        calendar.add(Calendar.DATE, 1);
        return calendar.getTime();
    }
}