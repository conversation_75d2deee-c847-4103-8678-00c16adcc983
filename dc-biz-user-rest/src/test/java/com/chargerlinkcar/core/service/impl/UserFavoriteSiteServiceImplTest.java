package com.chargerlinkcar.core.service.impl;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.cus.repository.UserFavoriteSiteMapper;
import com.cdz360.biz.cus.service.impl.UserFavoriteSiteServiceImpl;
import com.chargerlinkcar.core.BaseMockTest;
import com.chargerlinkcar.framework.common.domain.UserFavoriteSite;
import com.chargerlinkcar.framework.common.domain.request.UserFavorSiteReq;
import com.chargerlinkcar.framework.common.utils.DcAssertUtil;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.Arrays;
import java.util.Date;

public class UserFavoriteSiteServiceImplTest extends BaseMockTest {

    @InjectMocks
    private UserFavoriteSiteServiceImpl userFavoriteSiteService;
    @Mock
    private UserFavoriteSiteMapper userFavoriteSiteMapper;

    private long uid = 14;
    private String siteId = "131948248229892842";
    private boolean favorite = true;
    private long start = 0;
    private int size = 5;

    @Test
    public void setFavorSite() {
        Mockito.when(userFavoriteSiteMapper.addToFavorites(uid,siteId)).thenReturn(1);
        Mockito.when(userFavoriteSiteMapper.removeTheCollection(uid,siteId)).thenReturn(1);
        BaseResponse response = userFavoriteSiteService.setFavorSite(uid, siteId, favorite);
        DcAssertUtil.isTrue(response.getStatus() == 0,"junit fail");
        favorite = false;
        BaseResponse response2 = userFavoriteSiteService.setFavorSite(uid, siteId, favorite);
        DcAssertUtil.isTrue(response2.getStatus() == 0,"junit fail");
    }

    @Test
    public void ifFavorite() {
        Mockito.when(userFavoriteSiteMapper.ifFavorite(uid,siteId)).thenReturn(1);
        ObjectResponse<Boolean> response = userFavoriteSiteService.ifFavorite(uid,siteId);
        DcAssertUtil.isTrue(response.getData(),"junit fail");
    }

    @Test
    public void getByUid() {
        UserFavoriteSite vo = new UserFavoriteSite();
        vo.setUid(uid).setSiteId(siteId).setCreateTime(new Date());
        UserFavorSiteReq req = new UserFavorSiteReq();
        req.setUid(uid).setStart(start).setSize(size);
        Mockito.when(userFavoriteSiteMapper.getByUid(req)).thenReturn(Arrays.asList(vo));
        ListResponse<UserFavoriteSite> response = userFavoriteSiteService.getByUid(req);
        DcAssertUtil.isNotNull(response,"junit fail");
    }
}