package com.chargerlinkcar.core.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.cus.service.SiteBlacklistService;
import com.cdz360.biz.model.cus.site.param.ListSiteBlacklistParam;
import com.cdz360.biz.model.cus.site.param.SiteBlacklistEnableParam;
import com.cdz360.biz.model.cus.site.vo.SiteBlacklistVo;
import com.chargerlinkcar.core.BaseMockTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.*;

@Slf4j
class SiteBlacklistServiceTest extends BaseMockTest {

    private static final String SITE_ID = "abc123456789";
    private static final Long UID = 2020520L;

    @Autowired
    private SiteBlacklistService siteBlacklistService;

    @Test
    void find() {
        ListSiteBlacklistParam param = new ListSiteBlacklistParam();
        param.setTotal(Boolean.TRUE);
        param.setSize(10)
                .setStart(0L);
        param.setSiteId(SITE_ID);

        ListResponse<SiteBlacklistVo> res = siteBlacklistService.find(param);
        assertEquals(1, res.getData().size(), "数据库已经内置的数据");
    }

    @Test
    void enable() {
        SiteBlacklistEnableParam param = new SiteBlacklistEnableParam();
        param.setSiteId(SITE_ID);
        param.setUid(UID);

        param.setEnable(Boolean.FALSE);
        siteBlacklistService.enable(param);

        ObjectResponse<Boolean> res = siteBlacklistService.userInSiteBlacklist(param);
        assertFalse(res.getData(), "内置数据");

        param.setEnable(Boolean.TRUE);
        siteBlacklistService.enable(param);

        res = siteBlacklistService.userInSiteBlacklist(param);
        assertTrue(res.getData(), "已经变更用户的状态");
    }

    @Test
    void userInSiteBlacklist() {
        SiteBlacklistEnableParam param = new SiteBlacklistEnableParam();
        param.setSiteId(SITE_ID);
        param.setUid(UID);

        siteBlacklistService.userInSiteBlacklist(param);
    }

    @Test
    void overtimeParkingBi() {
    }
}