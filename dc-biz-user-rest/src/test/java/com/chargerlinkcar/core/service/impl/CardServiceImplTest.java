package com.chargerlinkcar.core.service.impl;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.cus.client.TradingFeignClient;
import com.cdz360.biz.cus.domain.vo.CardListdetailVO;
import com.cdz360.biz.cus.repository.CardMapper;
import com.cdz360.biz.cus.service.CommercialService;
import com.cdz360.biz.cus.service.MerchantService;
import com.cdz360.biz.cus.service.impl.CardServiceImpl;
import com.cdz360.biz.model.cus.user.dto.WhiteCard;
import com.cdz360.biz.model.cus.user.dto.WhiteCardDto;
import com.cdz360.biz.model.cus.user.param.WhiteCardRequest;
import com.chargerlinkcar.core.BaseMockTest;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.param.CardsParam;
import com.chargerlinkcar.framework.common.domain.type.CardStatus;
import com.chargerlinkcar.framework.common.domain.vo.Card;
import com.chargerlinkcar.framework.common.domain.vo.CardVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import com.chargerlinkcar.framework.common.utils.AssertUtil;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.*;

@Slf4j
public class CardServiceImplTest extends BaseMockTest {

    @Mock
    private CardMapper cardMapper;
    @Mock
    private MerchantService merchantFeignClient;

    @Mock
    private CommercialService commercialService;
    @Mock
    private TradingFeignClient tradingFeignClient;

    @InjectMocks
    private CardServiceImpl cardService;

    @Test
    public void parseExcel() {
    }

    @Test
    public void queryOnlineCardsByPage() {
        String token = "qqqqqqqqqqqqqqqqqq";
        OldPageParam page = new OldPageParam(0, 10);
        long topCommId = 33421;
        String commIdChain = "33421";
        List<Long> object = new ArrayList<>();
        CardListdetailVO cardListdetailVO = new CardListdetailVO();
        cardListdetailVO.setCardNo("");
        cardListdetailVO.setCommId(1l);
        cardListdetailVO.setUserId(1l);
        cardListdetailVO.setBeginTime("");
        cardListdetailVO.setEndTime("");
        cardListdetailVO.setCardStatus("");
        List<CardListdetailVO> cardList = new ArrayList<CardListdetailVO>();
        cardList.add(cardListdetailVO);
        Map<String, Object> params = new HashMap<>(16);
        params.put("keyWord", cardListdetailVO.getCardNo());
        params.put("commId", cardListdetailVO.getCommId());
        params.put("userId", cardListdetailVO.getUserId());
        params.put("begintime", cardListdetailVO.getBeginTime());
        params.put("endtime", cardListdetailVO.getEndTime());
        params.put("cardStatus", cardListdetailVO.getCardStatus());
        params.put("topCommId", topCommId);
        params.put("commIdChain", commIdChain);
        Mockito.when(merchantFeignClient.getCommIdListByToken(Mockito.anyString())).thenReturn(object);
        Mockito.when(cardMapper.queryOnlineCardsByPage(params)).thenReturn(cardList);
        CardListdetailVO cardListdetailVO1 = new CardListdetailVO();
        cardListdetailVO1.setCommName("商户名");
        ObjectResponse<Commercial> object2 = new ObjectResponse<Commercial>();

//        object2.put("data",new ArrayList<CardListdetailVO>().add(cardListdetailVO1));
        object2.setData(new Commercial());
//        Mockito.when(merchantFeignClient.getCommercialByCommId(cardListdetailVO.getCommId())).thenReturn(object2);
        Mockito.when(commercialService.getCommercialByCommId(Mockito.anyLong())).thenReturn(object2);

        ChargerOrderVo vo = new ChargerOrderVo();
        vo.setPayAccountName("abc");
        ListResponse<ChargerOrderVo> ret = new ListResponse<ChargerOrderVo>(List.of(vo), 1l);
        CardsParam cardsParam = new CardsParam();
        cardsParam.setCardChipNolist(new ArrayList<>());
        cardsParam.setCommIdChain(commIdChain);
        Mockito.when(tradingFeignClient.selectByConditionAndOneYear(cardsParam)).thenReturn(ret);
        ListResponse<CardListdetailVO> result = cardService.queryOnlineCardsByPage(token, page, cardListdetailVO, topCommId, commIdChain);
        Assertions.assertTrue(result.getStatus() == ResultConstant.RES_SUCCESS_CODE);
        Assertions.assertTrue(result.getData() != null);
    }

    @Test
    public void updateCard() {
        Card card = new Card();
        card.setCardNo("11111");
        Mockito.when(cardMapper.queryCardByCardNo(Mockito.anyString())).thenReturn(card);
        Mockito.when(cardMapper.updateByCardNoSelective(Mockito.any())).thenReturn(1);
        BaseResponse result = cardService.updateCard(new Card());
        Assertions.assertTrue(result.getStatus() == 0);
    }

    @Test
    public void updateCardStatus() {
        String cardNo = "11111";
        String cardStatus = "10001";
        Mockito.when(cardMapper.updateCardStatus(cardNo, cardStatus)).thenReturn(1);
        BaseResponse result = cardService.updateCardStatus(cardNo, cardStatus);
        Assertions.assertTrue(result.getStatus() == 0);
    }


    @Test
    public void queryAllCardList() {
        List<Long> object = new ArrayList<Long>() {{
            add(33425L);
            add(33421L);
        }};
        Mockito.when(merchantFeignClient.getCommIdListByToken(Mockito.anyString())).thenReturn(object);

        java.util.List<CardVo> list = new ArrayList<CardVo>() {{
            add(new CardVo() {{
                setCardId(10086L);
                setCardNo("NO10086");
                setCardName("CARD007");
                setType(1);
                setStatus(1);
                setCardActivationDate(new Date());
            }});
        }};
        Mockito.when(cardMapper.queryAllCardList(Mockito.any())).thenReturn(list);
        ListResponse<CardVo> entity = cardService.queryAllCardList("123", "61699L");
        java.util.List listData = entity.getData();
        Assertions.assertTrue(listData.size() == 1);
        Assertions.assertTrue(entity.getStatus() == 0);
    }

    @Test
    public void queryWhiteCardDtoBySiteList() {
        String siteId = "";
        List<WhiteCardDto> list = new ArrayList<>();
        WhiteCardDto dto = new WhiteCardDto();
        List<WhiteCard> list1 = new ArrayList<>();
        {
            WhiteCard whiteCard = new WhiteCard();
            whiteCard.setCardNumber("123");
            whiteCard.setPasscode(null);
            list1.add(whiteCard);
        }
        dto.setSiteId(siteId);
        dto.setWhiteCardList(list1);
        list.add(dto);
        //将下列状态的卡排除在外
        List<String> excludeCardStatusList = new ArrayList<>();
        excludeCardStatusList.add(CardStatus.INACTIVE.getCode());
        excludeCardStatusList.add(CardStatus.LOCK.getCode());
        excludeCardStatusList.add(CardStatus.FAILURE.getCode());
        excludeCardStatusList.add(CardStatus.EXPIRED.getCode());
        excludeCardStatusList.add(CardStatus.DELETED.getCode());
        Card card = new Card();
        card.setCardNo("123");
        card.setStations("aaaaaaaaa");
        card.setCardStatus(CardStatus.ACTIVE.getCode());
        Mockito.when(cardMapper.queryCardByCardChipNo(Mockito.anyString())).thenReturn(card);
        Mockito.when(cardMapper.queryWhiteCardDtoMapList(null, siteId, null, null, null, excludeCardStatusList)).thenReturn(list);
//        Mockito.when(cardMapper.updateActivationCode(Mockito.any())).thenReturn()
        WhiteCardRequest request = new WhiteCardRequest();
        List<String> list2 = new ArrayList<>();
        String cardChipNo = "123";
        list2.add(cardChipNo);
        request.setCardChipNoList(list2);
        request.setIsAbandon(true);
        List<WhiteCardDto> res = cardService.queryWhiteCardDtoBySiteList(request);
        AssertUtil.notNull(res, "queryWhiteCardDtoBySiteId()单测失败");
    }

}