package com.chargerlinkcar.core.service.impl;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.cus.domain.BlocUser;
import com.cdz360.biz.cus.repository.BlocUserMapper;
import com.cdz360.biz.cus.service.impl.BlocUserServiceImpl;
import com.chargerlinkcar.core.BaseMockTest;
import com.chargerlinkcar.framework.common.domain.BlocUserDto;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.List;

class BlocUserServiceImplTest extends BaseMockTest {

    @Mock
    private BlocUserMapper blocUserMapper;
    @InjectMocks
    private BlocUserServiceImpl blocUserService;

    @Test
    void findByCondition() {
        List<BlocUserDto> list = new ArrayList<BlocUserDto>() {{
            add(new BlocUserDto() {{
                setId(10086L);
                setBlocUserName("abc");
            }});
        }};
        Mockito.when(blocUserMapper.findByCondition(Mockito.any())).thenReturn(list);
        ListResponse<BlocUserDto> res = blocUserService.findByCondition(new BlocUser());
        FeignResponseValidate.check(res);
        IotAssert.isTrue(CollectionUtils.isNotEmpty(res.getData()), "junit fail");
    }
}