package com.chargerlinkcar.core.service;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.cus.client.AuthCenterFeignClient;
import com.cdz360.biz.cus.service.CorpInvoiceInfoService;
import com.cdz360.biz.ds.cus.ro.corp.ds.CorpInvoiceInfoRoDs;
import com.cdz360.biz.ds.cus.rw.corp.ds.CorpInvoiceInfoRwDs;
import com.cdz360.biz.ds.cus.rw.corp.ds.CorpRwDs;
import com.cdz360.biz.model.cus.corp.dto.CorpInvoiceInfoDto;
import com.cdz360.biz.model.cus.corp.po.CorpInvoiceInfoPo;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.chargerlinkcar.core.BaseMockTest;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceInfoVo;
import java.util.Date;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.BeanUtils;

class CorpInvoiceInfoServiceTest extends BaseMockTest {

    @Mock
    private CorpInvoiceInfoRoDs corpInvoiceInfoRoDs;

    @Mock
    private CorpInvoiceInfoRwDs corpInvoiceInfoRwDs;

    @Mock
    private CorpRwDs corpRwDs;

    @Mock
    private AuthCenterFeignClient authCenterFeignClient;

    @InjectMocks
    private CorpInvoiceInfoService corpInvoiceInfoService;

    private static final CorpPo CORP_PO = new CorpPo();
    private static final CorpInvoiceInfoPo PO = new CorpInvoiceInfoPo();

    @BeforeEach
    void init() {
        PO.setCorpId(1L)
                .setUid(123L)
                .setInvoiceWay(InvoicingMode.POST_SETTLEMENT)
                .setTempSalId(1L)
                .setProductTempId(3L)
                .setCreateTime(new Date())
                .setUpdateTime(new Date());

        CORP_PO.setId(1L)
                .setSettlementType(SettlementType.POSTPAID);
    }

    @Test
    void insertOrUpdate() {
        Mockito.when(corpRwDs.getCorp(Mockito.anyLong(), Mockito.anyBoolean())).thenReturn(CORP_PO);
        Mockito.when(corpInvoiceInfoRwDs.insertOrUpdate(Mockito.any())).thenReturn(1);
        Mockito.when(authCenterFeignClient.updateBlocUser(Mockito.any())).thenReturn(RestUtils.success());
        CorpInvoiceInfoDto dto = new CorpInvoiceInfoDto();
        BeanUtils.copyProperties(PO, dto);
        int i = corpInvoiceInfoService.insertOrUpdate(dto);
    }

    @Test
    void disableByCorpId() {
        Mockito.when(corpRwDs.getCorp(Mockito.anyLong(), Mockito.anyBoolean())).thenReturn(CORP_PO);
        Mockito.when(authCenterFeignClient.updateBlocUser(Mockito.any())).thenReturn(RestUtils.success());
        int i = corpInvoiceInfoService.disableByCorpId(CORP_PO.getId());
    }

    @Test
    void getCorpInvoiceInfo() {
        Mockito.when(corpInvoiceInfoRoDs.getByCorpId(Mockito.any())).thenReturn(PO);
        Mockito.when(corpRwDs.getCorp(Mockito.anyLong(), Mockito.anyBoolean())).thenReturn(CORP_PO);
        CorpInvoiceInfoVo vo = corpInvoiceInfoService.getCorpInvoiceInfo(PO.getCorpId());
        assertEquals(vo.getCorpId(), PO.getCorpId());
        assertEquals(vo.getInvoiceWay(), PO.getInvoiceWay());
    }
}