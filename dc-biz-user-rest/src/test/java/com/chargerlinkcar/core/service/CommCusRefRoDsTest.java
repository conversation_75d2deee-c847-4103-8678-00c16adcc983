package com.chargerlinkcar.core.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.ds.cus.ro.comm.ds.CommCusRefRoDs;
import com.cdz360.biz.ds.cus.ro.comm.mapper.CommCusRefRoMapper;
import com.cdz360.biz.model.common.constant.Constant;
import com.chargerlinkcar.core.BaseMockTest;
import com.chargerlinkcar.framework.common.domain.vo.CommCusRef;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

class CommCusRefRoDsTest extends BaseMockTest {

    @Mock
    private CommCusRefRoMapper commCusRefRoMapper;
    @InjectMocks
    private CommCusRefRoDs commCusRefRoDs;

//    @BeforeEach
//    void addOrUpdateCusEable() {
//        CommCusRef commCusRef = new CommCusRef();
//        commCusRef.setUserId(63270l);
//        commCusRef.setCommId(33421l);
//        commCusRef.setEnable(Constant.ENABLE);
//        commCusRef.setCreateTime(new Date());
//        commCusRef.setUpdateTime(new Date());
//        commCusRefMapper.insertOrUpdateCommCusRef(commCusRef);
//    }

    @Test
    void findByCommIdAndPhone() {
        List<CommCusRef> list = new ArrayList<>() {{
            add(new CommCusRef() {{
                setUserId(63270l);
                setCommId(33421l);
                setEnable(Constant.ENABLE);
                setCreateTime(new Date());
                setUpdateTime(new Date());
            }});
        }};
        Mockito.when(commCusRefRoMapper.findByCondition(Mockito.any())).thenReturn(list);
        CommCusRef ref = commCusRefRoDs.findByCommIdAndPhone(33421l, "1231");
        IotAssert.isNotNull(ref, "junit fail");
    }

    @Test
    void findByCondition() {
        List<CommCusRef> list = new ArrayList<>() {{
            add(new CommCusRef() {{
                setUserId(63270l);
                setCommId(33421l);
                setEnable(Constant.ENABLE);
                setCreateTime(new Date());
                setUpdateTime(new Date());
            }});
        }};
        Mockito.when(commCusRefRoMapper.findByCondition(Mockito.any())).thenReturn(list);
        ListResponse<CommCusRef> ref = commCusRefRoDs.findByCondition(Mockito.any());
        FeignResponseValidate.check(ref);
    }

    @Test
    void findById() {
        CommCusRef ref = new CommCusRef();
        ref.setId(1l);
        ref.setCommId(33421l);
        Mockito.when(commCusRefRoMapper.findById(Mockito.anyLong())).thenReturn(ref);
        CommCusRef res = commCusRefRoDs.findById(Mockito.anyLong());
        IotAssert.isNotNull(res, "junit fail");
    }
}