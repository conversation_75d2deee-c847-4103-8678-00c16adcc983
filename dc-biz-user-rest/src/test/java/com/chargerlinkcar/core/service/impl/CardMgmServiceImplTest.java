package com.chargerlinkcar.core.service.impl;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.cus.repository.CardMapper;
import com.cdz360.biz.cus.service.CommercialService;
import com.cdz360.biz.cus.service.MerchantService;
import com.cdz360.biz.cus.service.impl.CardMgmServiceImpl;
import com.chargerlinkcar.core.BaseMockTest;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.vo.CardMgnVo;
import com.github.pagehelper.Page;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 *
 * @since 2019/5/20
 **/
@Transactional
@Data
@EqualsAndHashCode(callSuper = true)
public class CardMgmServiceImplTest extends BaseMockTest {
    @Autowired
    private CardMapper cardMapper;
    @Mock
    private MerchantService merchantFeignClient;

    @Mock
    private CommercialService commercialService;

    @InjectMocks
    @Autowired
    private CardMgmServiceImpl cardMgmService;

    @BeforeEach
    public void setUp() throws Exception {
    }

    @AfterEach
    public void tearDown() throws Exception {
    }

    @Test
    public void queryCardsByPage() {
        OldPageParam page = new OldPageParam(1, 10);

        Map<String, Object> params = new HashMap<>();
        params.put("cardStatus", null);
        params.put("cardType", null);
        params.put("begintime", null);
        params.put("endtime", null);
        params.put("keyWord", null);

        ObjectResponse<Commercial> jSONObject = new ObjectResponse<Commercial>();

        Commercial data = new Commercial();
        data.setCommName("上海鼎充");
        jSONObject.setData(data);
        Mockito.when(commercialService.getCommercialByCommId(Mockito.any())).thenReturn(jSONObject);

        ListResponse<CardMgnVo> res = cardMgmService.queryCardsByPage(null, page, null, null, null, null, null, null);
        Integer code = res.getStatus();
        Assertions.assertEquals(0, res.getStatus());
    }

    @Test
    public void deleteCardsById() {
    }

    @Test
    void test_resetCardsByIds() {
        cardMgmService.resetCardsByIds(List.of(432L));
    }
}