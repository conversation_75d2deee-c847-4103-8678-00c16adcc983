// package com.chargerlinkcar.core.balance.service;
//
// import com.chargerlinkcar.core.BaseMapperTest;
// import com.chargerlinkcar.core.balance.constant.BalanceType;
// import com.chargerlinkcar.core.balance.constant.FrozenType;
// import com.chargerlinkcar.framework.common.constant.UnFrozenType;
// import org.junit.jupiter.api.Test;
// import org.springframework.beans.factory.annotation.Autowired;
//
// /**
//  * <AUTHOR>
//  *  //TODO
//  * @since 2019/7/17
//  **/
// public class GroupCreditBalanceTest extends BaseMapperTest {
//     @Autowired
//     private BalanceFactroy balanceFactroy;
//
//     @Test
//     public void payWithBalance() {
//     }
//
//     @Test
//     public void initBalance() {
//     }
//
//     @Test
//     public void frozenAmount() {
//         IBalance balance = balanceFactroy.createBalance(BalanceType.GROUPCREDIT);
//         balance.frozenAmount(BalanceType.GROUPCREDIT, FrozenType.INIT, 1047, "100001", true);
//     }
//
//     @Test
//     public void unFrozenAmount(){
//         IBalance balance = balanceFactroy.createBalance(BalanceType.GROUPCREDIT);
//         balance.unFrozenAmount(BalanceType.GROUPCREDIT, UnFrozenType.FULL, 1047L, 90L, 200L, "100001");
//     }
// }