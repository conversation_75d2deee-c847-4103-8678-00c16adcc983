package com.cdz360.biz.cus.rest;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.cus.site.param.ListSiteBlacklistParam;
import com.cdz360.biz.model.cus.site.param.UpdateSiteParkFeeUserParam;
import com.chargerlinkcar.core.BaseMockTest;
import org.junit.jupiter.api.Test;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

class SiteParkFeeUserRestTest extends BaseMockTest {

    @Test
    void findBySiteId() throws Exception {
        String url = "/api/siteParkFeeUser/findBySiteId";

        MockHttpServletRequestBuilder rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("siteId", "123456789");
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(SiteParkFeeUserRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("findBySiteId")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andDo(print())
                .andReturn();
    }

    @Test
    void getParkFeeUser() throws Exception {
        String url = "/api/siteParkFeeUser/getParkFeeUser";

        MockHttpServletRequestBuilder rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("siteId", "123456789")
                .param("uid", "123");
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(SiteParkFeeUserRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("getParkFeeUser")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andDo(print())
                .andReturn();
    }

    @Test
    void updateSiteParkFeeUser() throws Exception {
        UpdateSiteParkFeeUserParam param = new UpdateSiteParkFeeUserParam();
        param.setSiteId("123456789")
                .setUidList(List.of(123L));

        String url = "/api/siteParkFeeUser/updateSiteParkFeeUser";

        MockHttpServletRequestBuilder rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(param));

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(SiteParkFeeUserRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("updateSiteParkFeeUser")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
    }
}