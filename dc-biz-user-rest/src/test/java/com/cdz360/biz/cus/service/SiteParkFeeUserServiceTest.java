package com.cdz360.biz.cus.service;

import com.cdz360.biz.ds.cus.ro.site.ds.SiteParkFeeUserRoDs;
import com.cdz360.biz.ds.cus.rw.site.ds.SiteParkFeeUserRwDs;
import com.cdz360.biz.model.cus.site.param.UpdateSiteParkFeeUserParam;
import com.cdz360.biz.model.cus.site.po.SiteParkFeeUserPo;
import com.chargerlinkcar.core.BaseMockTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class SiteParkFeeUserServiceTest extends BaseMockTest {

    @InjectMocks
    private SiteParkFeeUserService siteParkFeeUserService;

    @Mock
    private SiteParkFeeUserRoDs siteParkFeeUserRoDs;

    @Mock
    private SiteParkFeeUserRwDs siteParkFeeUserRwDs;

    private static final Long UID = 9527L;

    private static final String SITE_ID = "SITE_ID_123456789";

    private static final SiteParkFeeUserPo STATIC_PO = new SiteParkFeeUserPo();

    @BeforeEach
    void init() {
        STATIC_PO.setEnable(true)
                .setSiteId(SITE_ID)
                .setUid(UID);
    }

    @Test
    void findBySiteId() {
        Mockito.when(siteParkFeeUserRoDs.findBySiteId(SITE_ID)).thenReturn(List.of(STATIC_PO));
        List<SiteParkFeeUserPo> poList = siteParkFeeUserService.findBySiteId(SITE_ID);
        assertNotNull(poList, "已存在数据");
        assertTrue(poList.size() > 0, "已存在数据");
    }

    @Test
    void getByUidSiteId() {
        Mockito.when(siteParkFeeUserRoDs.getByUidSiteId(UID, SITE_ID)).thenReturn(STATIC_PO);
        SiteParkFeeUserPo po = siteParkFeeUserService.getByUidSiteId(UID, SITE_ID);
        assertNotNull(po, "已存在数据");
        assertEquals(SITE_ID, po.getSiteId(), "已存在数据");
        assertEquals(UID, po.getUid(), "已存在数据");
    }

    @Test
    void updateSiteParkFeeUser() {
        UpdateSiteParkFeeUserParam param = new UpdateSiteParkFeeUserParam();
        param.setSiteId(SITE_ID)
                .setUidList(List.of(UID));
        Mockito.when(siteParkFeeUserRwDs.excludeUpdate(param)).thenReturn(1);
        Mockito.when(siteParkFeeUserRwDs.insertOrUpdateBatch(Mockito.any())).thenReturn(1);
        Integer i = siteParkFeeUserService.updateSiteParkFeeUser(param);
        assertTrue(i > 0);

        param.setUidList(List.of());
        Mockito.when(siteParkFeeUserRwDs.excludeUpdate(param)).thenReturn(1);
        i = siteParkFeeUserService.updateSiteParkFeeUser(param);
        assertTrue(i > 0);
    }
}