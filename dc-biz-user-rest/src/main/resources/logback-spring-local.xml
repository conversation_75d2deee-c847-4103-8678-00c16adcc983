<?xml version="1.0" encoding="UTF-8"?>

<!--please pay attention that: file name should not be logback.xml，name it logback-spring.xml to use it in springboot framework-->
<configuration>

    <springProperty scope="context" name="logging.path" source="logging.path"/>
    <springProperty scope="context" name="logging.level" source="logging.level.com.charge.core"/>



    <include resource="org/springframework/boot/logging/logback/base.xml"/>


    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <Pattern>%d{HH:mm:ss.SSS}^|^%-5level^|^%logger{80} %M ^|^%line^|^^|^ %msg%n</Pattern>
        </encoder>
    </appender>

    <!-- to generate logfile daily -->
    <appender name="ERROR-APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <append>true</append>
        <!-- a filter that show green light for object that has a error log level-->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>error</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <!-- log name -->
        <file>${logging.path}/charge/chargerlink-car-user-error.log</file>
        <!-- to generate a log file everyday with a longest lasting of 30 days -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- logfile name with daily rolling-->
            <FileNamePattern>${logging.path}/charge/backup/chargerlink-car-user-error.log.%d{yyyy-MM-dd}
            </FileNamePattern>
            <!-- log perserve days-->
            <MaxHistory>30</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--output format：%d is for date，%thread is for thread name，%-5level：loglevel with 5 character  %msg：log user，%n line breaker-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}^|^[%thread]^|^%-5level^|^%logger{50}^|^%msg%n</pattern>
            <!-- encoding -->
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="ROOT-APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <append>true</append>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>${logging.level}</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <file>${logging.path}/charge/chargerlink-car-user-info.log</file>
        <!-- to generate a log file everyday with a longest lasting of 30 days -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- logfile name with daily rolling-->
            <FileNamePattern>
                ${logging.path}/charge/backup/chargerlink-car-user-info.log.%d{yyyy-MM-dd}
            </FileNamePattern>
            <!-- log perserve days-->
            <MaxHistory>30</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--output format：%d is for date，%thread is for thread name，%-5level：loglevel with 5 character  %msg：log user，%n line breaker-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}^|^[%thread]^|^%-5level^|^%logger{50}^|^%msg%n</pattern>
            <!-- encoding -->
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <appender name="ASPECT-APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <append>true</append>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>${logging.level}</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <file>${logging.path}/charge/common-aspect-info.log</file>
        <!-- to generate a log file everyday with a longest lasting of 30 days -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- logfile name with daily rolling-->
            <FileNamePattern>
                ${logging.path}/charge/common-aspect-info.log.%d{yyyy-MM-dd}
            </FileNamePattern>
            <!-- log perserve days-->
            <MaxHistory>30</MaxHistory>
        </rollingPolicy>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--output format：%d is for date，%thread is for thread name，%-5level：loglevel with 5 character  %msg：log message，%n line breaker-->
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}^|^[%thread]^|^%-5level^|^%logger{50}^|^%msg%n</pattern>
            <!-- encoding -->
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <logger name="com.chargerlink.core" level="${logging.level}" additivity="true">
        <appender-ref ref="ROOT-APPENDER"/>
        <appender-ref ref="ERROR-APPENDER"/>
    </logger>
    <logger name="com.chargerlinkcar.framework.common.LogAspect.LogAspect" level="${logging.level}" additivity="true">
        <appender-ref ref="ASPECT-APPENDER"/>
    </logger>
    <root level="${logging.level}">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="ROOT-APPENDER"/>
        <appender-ref ref="ERROR-APPENDER"/>
    </root>
</configuration>