package com.cdz360.biz.oa.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "流程关联单号信息")
public class OaContainSettJobBillDto {

    @Schema(description = "OA流程ID")
    private String procInstId;

    @Schema(description = "单号列表")
    private List<String> noList;
}
