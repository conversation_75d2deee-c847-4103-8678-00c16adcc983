package com.cdz360.biz.oa.dto.account;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OaGroupDto {

    private String id;

    @Schema(description = "组名称")
    private String name;

    @Schema(description = "组属性")
    private String type;

    @Schema(description = "用户数")
    private Long memberCount;
}
