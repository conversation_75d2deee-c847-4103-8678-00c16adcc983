package com.cdz360.biz.oa.param;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.biz.model.oa.vo.InvoicingContentVo;
import com.cdz360.biz.oa.dto.InvoicedSalTempRefDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * DepositInvoiceUserParam
 *
 * @since 3/28/2023 2:32 PM
 * <AUTHOR>
 */
@ApiModel(value = "个人&商户会员充值申请")
@Data
@EqualsAndHashCode(callSuper = true)
public class DepositInvoiceUserParam extends DepositInvoiceParam {

    @Schema(description = "商品行模板")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InvoicedSalTempRefDTO tempRefVo;

    @Deprecated
    @Schema(description = "发票记录invoiced_record.id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long invoiceRecordId;

    @Schema(description = "发票记录invoiced_record.id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> invRecIds;

    @Schema(description = "用户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long userId;

    @Schema(description = "用户(个人&商户)手机号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String phone;

    @Schema(description = "商户会员关系t_comm_cus_ref.id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long commCusId;

    @Override
    public void checkAndFilterField() {

        fixAmountFill();
        checkAmount();

        if (CollectionUtils.isEmpty(this.getOrderNoList())) {
            throw new DcArgumentException("请传入订单编号列表");
        }

//        if (this.getCorpId() == null) {
//            throw new DcArgumentException("请提供企业客户ID");
//        }

//        if (this.getInvoiceWay() == null) {
//            throw new DcArgumentException("需要提供企业客户的开票方式");
//        }

//        if (StringUtils.isBlank(this.getApplyNo())) {
//            throw new DcArgumentException("开票记录申请单号不能为空");
//        }

//        if (StringUtils.isBlank(dto.getSaleTin())) {
//            throw new DcArgumentException("开票主体纳税人识别号不能为空");
//        }
        if (null == this.getTempSalId()) {
            throw new DcArgumentException("开票主体记录ID不能为空");
        }

        if (null == this.getProductTempId()) {
            throw new DcArgumentException("商品行模板ID不能为空");
        }

//        if (null == this.getActualElecFee() && null == this.getActualServFee()) {
//            throw new DcArgumentException("应开电费和应开服务费不能同时为空");
//        }

        if ((null == this.getFixElecFee() ||
            DecimalUtils.isZero(this.getFixElecFee())) &&
            (null == this.getFixServFee() ||
                DecimalUtils.isZero(this.getFixServFee())) &&
            (null == this.getFixPrepaidCardFee() ||
                DecimalUtils.isZero(this.getFixPrepaidCardFee()))) {
            throw new DcArgumentException("修正电费和修正服务费和修正预付卡费不能同时为空");
        }

        if ((null != this.getFixElecFee() &&
            DecimalUtils.gt(this.getFixElecFee(), MAX_FIX)) ||
            (null != this.getFixServFee() &&
                DecimalUtils.gt(this.getFixServFee(), MAX_FIX)) ||
            (null != this.getFixPrepaidCardFee() &&
                DecimalUtils.gt(this.getFixPrepaidCardFee(), MAX_FIX))) {
            throw new DcArgumentException("修正电费或修正服务费或修正预付卡费金额过大");
        }
    }

}