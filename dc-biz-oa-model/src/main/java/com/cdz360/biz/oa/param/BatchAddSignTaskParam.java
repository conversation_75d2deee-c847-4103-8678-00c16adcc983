package com.cdz360.biz.oa.param;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonInclude(Include.NON_NULL)
@Schema(description = "批量加签请求参数")
@EqualsAndHashCode(callSuper = true)
public class BatchAddSignTaskParam extends AddSignTaskParam {

    @Schema(description = "加签任务ID列表")
    @JsonInclude(Include.NON_EMPTY)
    private List<String> taskIdList;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}
