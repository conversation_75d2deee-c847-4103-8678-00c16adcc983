package com.cdz360.biz.oa.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "电价调整OA表单信息")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ChangePriceSchemaFormDto extends OaStartFormBase {

    @Schema(description = "目标计费模板ID")
    @JsonInclude(Include.NON_NULL)
    private Long priceSchemeId;

    @Schema(description = "直流计费模板ID")
    @JsonInclude(Include.NON_NULL)
    private Long dcPriceSchemeId;

    @Schema(description = "交流计费模板ID")
    @JsonInclude(Include.NON_NULL)
    private Long acPriceSchemeId;
    @Schema(description = "操作对象类型: EVSE(部分桩);SITE(整个场站);SITE_PART(整站交直流分开)")
    @JsonInclude(Include.NON_NULL)
    private String target;

    @Schema(description = "场站ID列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> siteIdList;

    @Schema(description = "场站名称列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> siteNameList;

    @Schema(description = "桩编号列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> evseNoList;

    @Schema(description = "变更目标旧数据信息", hidden = true)
    @JsonInclude(Include.NON_NULL)
    private List<TargetPriceSchemeInfo> targetInfoList;

    @Schema(description = "执行方式: IN_TIME(立即);SPECIAL_DATE(指定日期);SPECIAL_TIME(指定时刻)")
    @JsonInclude(Include.NON_NULL)
    private String exeTime;

    @Schema(description = "执行时间: SPECIAL_DATE(指定日期);SPECIAL_TIME(指定时刻)")
    @JsonInclude(Include.NON_NULL)
    private String exeDate;

    @Schema(description = "交直流")
    @JsonInclude(Include.NON_NULL)
    private List<String> evseTypeList;

    @Schema(description = "申请人ID", hidden = true)
    private String opId;

    @Schema(description = "申请人名称", hidden = true)
    private String opName;


    /**
     * 次轮模板信息
     */
    @Schema(description = "轮次")
    @JsonInclude(Include.NON_NULL)
    private Integer round;

    @Schema(description = "目标计费模板ID")
    @JsonInclude(Include.NON_NULL)
    private Long priceSchemeId2;

    @Schema(description = "直流计费模板ID")
    @JsonInclude(Include.NON_NULL)
    private Long dcPriceSchemeId2;

    @Schema(description = "交流计费模板ID")
    @JsonInclude(Include.NON_NULL)
    private Long acPriceSchemeId2;

    @Schema(description = "桩编号列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> evseNoList2;

    @Schema(description = "变更目标旧数据信息", hidden = true)
    @JsonInclude(Include.NON_NULL)
    private List<TargetPriceSchemeInfo> targetInfoList2;

    @Schema(description = "执行方式: IN_TIME(立即);SPECIAL_DATE(指定日期);SPECIAL_TIME(指定时刻)")
    @JsonInclude(Include.NON_NULL)
    private String exeTime2;

    @Schema(description = "执行时间: SPECIAL_DATE(指定日期);SPECIAL_TIME(指定时刻)")
    @JsonInclude(Include.NON_NULL)
    private String exeDate2;

    @Schema(description = "是否是智能电价计价")
    @JsonInclude(Include.NON_NULL)
    private boolean smartPrice;

    @Schema(description = "智能新电价")
    @JsonInclude(Include.NON_NULL)
    private String elecPrice;

    @Schema(description = "智能新分时电价")
    @JsonInclude(Include.NON_NULL)
    private String timeBasedElecPrice;

    @Schema(description = "智能电价计价策略")
    @JsonInclude(Include.NON_NULL)
    private String smartStrategy;

    @Schema(description = "是否是智能电价计价")
    @JsonInclude(Include.NON_NULL)
    private boolean smartPrice2;

    @Schema(description = "智能新电价")
    @JsonInclude(Include.NON_NULL)
    private String elecPrice2;

    @Schema(description = "智能新分时电价")
    @JsonInclude(Include.NON_NULL)
    private String timeBasedElecPrice2;

    @Schema(description = "智能电价计价策略")
    @JsonInclude(Include.NON_NULL)
    private String smartStrategy2;
}
