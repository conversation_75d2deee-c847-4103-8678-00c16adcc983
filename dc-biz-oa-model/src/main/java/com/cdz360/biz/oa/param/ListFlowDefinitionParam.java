package com.cdz360.biz.oa.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(value = "获取流程定义列表参数")
@Data
@EqualsAndHashCode(callSuper = true)
public class ListFlowDefinitionParam extends BaseListParam {

    @ApiModelProperty(value = "最新版本")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean latest;

    @ApiModelProperty(value = "顶级商户ID", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String tenantId;
}
