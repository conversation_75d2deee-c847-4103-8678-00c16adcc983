package com.cdz360.biz.oa.dto;

import com.cdz360.biz.model.invoice.dto.OaFee;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "结算单汇总信息")
public class OaSettJobBillInfo {

    @Schema(description = "结算单是否存在差异")
    @JsonInclude(Include.NON_NULL)
    private Boolean difference;

    @Schema(description = "结算单编号列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> billNoList;

    @Schema(description = "结算单信息列表(接口不需要传递)", requiredMode = RequiredMode.NOT_REQUIRED)
    @JsonInclude(Include.NON_NULL)
    private List<OaSettJobBillDto> settJobBillList;

    @Schema(description = "实际结算,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private OaFee actualFee;
}
