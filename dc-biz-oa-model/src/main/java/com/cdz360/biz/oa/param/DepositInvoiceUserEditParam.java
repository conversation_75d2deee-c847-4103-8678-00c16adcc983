package com.cdz360.biz.oa.param;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.StringUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * DepositInvoiceUserEditParam
 *  不要在这个类中添加成员，可能会导致成员字段转换缺失
 * @since 4/3/2023 9:26 AM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class DepositInvoiceUserEditParam extends DepositInvoiceUserParam {

    public void checkAndFilterField() {

        if (StringUtils.isBlank(procInstId)) {
            throw new DcArgumentException("流程实例ID不能为空");
        }

        super.checkAndFilterField();
    }

}