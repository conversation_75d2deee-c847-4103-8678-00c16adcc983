package com.cdz360.biz.oa.param.account;

import com.cdz360.base.utils.JsonUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "修改用户所在审核组")
public class OaModifyUserGroupParam {

    Long uid;

    @Schema(description = "审批组名字 传空时,将用户移除所有的审批组,账号停用时需做此操作")
    List<String> groups;

    @Schema(description = "操作人uid", required = true)
    Long opUid;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
