package com.cdz360.biz.oa.dto;

import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.biz.model.oa.dto.OaContract;
import com.cdz360.biz.model.oa.type.OaHandleNode;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "支出-场站分成OA表单信息")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ExpenseSiteFormDto extends OaStartFormBase {

    @Schema(description = "场站ID")
    @JsonInclude(Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "场站ID列表", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_EMPTY)
    private List<String> siteIdList;

    @Schema(description = "场站名称")
    @JsonInclude(Include.NON_EMPTY)
    private String siteName;

    @Schema(description = "场站名称列表")
    @JsonInclude(Include.NON_EMPTY)
    private List<String> siteNameList;

//    @Schema(description = "场站编号")
//    @JsonInclude(Include.NON_EMPTY)
//    private String siteNo;

    @Schema(description = "场站备注（只取第一个场站）")
    @JsonInclude(Include.NON_EMPTY)
    private String siteRemark;

    @Schema(description = "分成客户名称", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_EMPTY)
    private String customerName;

    @Schema(description = "开户行", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_EMPTY)
    private String bankName;

    @Schema(description = "银行账号", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_EMPTY)
    private String bankAccount;

    @Schema(description = "付款公司", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_EMPTY)
    private String payCompanyName;

    // ========= 结算单信息 ==============
    @Schema(description = "结算单信息")
    @JsonInclude(Include.NON_NULL)
    private OaSettJobBillInfo settBillSummary;

    @Schema(description = "结算单差异说明")
    @JsonInclude(Include.NON_EMPTY)
    private String differenceRemark;
    // ========= 结算单信息 ==============

    @Schema(description = "结算账期")
    @JsonInclude(Include.NON_NULL)
    private TimeFilter billDateRange;

    @Schema(description = "付款时间说明")
    @JsonInclude(Include.NON_EMPTY)
    private String payTimeRequirement;

    @Schema(description = "经办节点")
    @JsonInclude(Include.NON_NULL)
    private List<OaHandleNode> handleNodes;

    // ========= 用印相关信息 =========
    @Schema(description = "印章公司名称")
    @JsonInclude(Include.NON_EMPTY)
    private String signetCompany;

    @Schema(description = "印章类型")
    @JsonInclude(Include.NON_EMPTY)
    private String signetType;

    @Schema(description = "其他印章说明")
    @JsonInclude(Include.NON_EMPTY)
    private String signetDesc;
    // ========= 用印相关信息 =========

    // ========= 合约 =========
    @Schema(description = "流程关联合约")
    @JsonInclude(Include.NON_NULL)
    private List<OaContract> contracts;
    // ========= 合约 =========
}
