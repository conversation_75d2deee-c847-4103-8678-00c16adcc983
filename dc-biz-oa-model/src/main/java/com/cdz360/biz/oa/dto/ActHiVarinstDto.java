package com.cdz360.biz.oa.dto;

import java.math.BigDecimal;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * ActHiVarinstDto
 *
 * @since 5/23/2023 11:29 AM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ToString(callSuper = true)
public class ActHiVarinstDto {
    private String id;
    private String procInstId;
    private String name;
    private String varType;
    private BigDecimal doubleVar;
    private Long longVar;
    private String text;
    private byte[] longString;
    private String byteArrayId;
    private String procDefinitionId;
    private String procInstKey;
}