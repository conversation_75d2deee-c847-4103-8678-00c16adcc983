package com.cdz360.biz.oa.param;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.cus.balance.po.BalanceApplicationPo;
import com.cdz360.biz.model.oa.param.PrepaidInvoicingEditParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@JsonInclude(Include.NON_NULL)
public class UserApproveParam {

    private Long uid;

    private String oName;

    private String oPhone;

    private String taskId;

    private Long topCommId;

    private String commIdChain;

    private String dataId;

    @Schema(description = "充值申请提交内容")
    private BalanceApplicationPo balanceApplicationPo;

    @Schema(description = "电价下发申请内容")
    private ChargeFeeParam chargeFeeParam;

    @Schema(description = "oa申请参数")
    private CreateProcessInstanceParam processInstanceParam;

    @Schema(description = "对账开票申请编辑入参")
    private BillingEditParam billingEditParam;

    @Schema(description = "充值开票企业申请编辑入参")
    private DepositInvoiceEditParam depositInvoiceEditParam;

    @Schema(description = "充值开票个人&商户会员申请编辑入参")
    private DepositInvoiceUserEditParam depositInvoiceUserEditParam;

    @Schema(description = "预付订单开票申请编辑入参")
    private PrepaidInvoicingEditParam prepaidInvoicingEditParam;


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
