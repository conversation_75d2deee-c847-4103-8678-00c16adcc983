package com.cdz360.biz.oa.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@ApiModel(value = "自定义任务对象")
@Data
@Accessors(chain = true)
public class OaCustomTaskDto {

    // =========== 👇 当前节点Task信息 ==================
    @ApiModelProperty(value = "任务ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String taskId;

    @ApiModelProperty(value = "任务名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String taskName;

    @ApiModelProperty(value = "当前指派的执行者的uid")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String taskAssignee;    // 当前指派的执行者的uid

    @ApiModelProperty(value = "当前指派的执行者的名称", notes = "")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String taskAssigneeName;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String taskDefinitionKey;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String taskDefinitionId;
    // =========== 👆 当前Task信息 ==================

    // =========== 👇 流程实例信息 ==================
    @ApiModelProperty(value = "流程实例ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String processInstanceId;   // 流程实例ID

    @ApiModelProperty(value = "流程结束时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "流程启动时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "启动人uid")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String startUserId;    // 启动人uid

    @ApiModelProperty(value = "启动人名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String startUserName;   // 启动人名称
    // =========== 👆 流程实例信息 ==================

    // =========== 👇 流程定义信息 ==================
    @ApiModelProperty(value = "流程定义KEY")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String processDefinitionKey;

    @ApiModelProperty(value = "流程定义名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String processDefinitionName;
    // =========== 👆 流程定义信息 ==================
}
