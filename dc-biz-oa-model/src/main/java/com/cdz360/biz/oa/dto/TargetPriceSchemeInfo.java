package com.cdz360.biz.oa.dto;

import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "电价调整目标对象信息")
@Data
@Accessors(chain = true)
public class TargetPriceSchemeInfo implements Serializable {

    @Schema(description = "目标对象的编号(场站ID/桩编号)")
    @JsonInclude(Include.NON_NULL)
    private String no;

    @Schema(description = "目标对象的名称(场站名称/桩名称)")
    @JsonInclude(Include.NON_NULL)
    private String name;

    @Schema(description = "目标对象旧电价模板ID")
    @JsonInclude(Include.NON_NULL)
    private Long priceSchemeId;

    @Schema(description = "模板类型")
    @JsonInclude(Include.NON_NULL)
    private SupplyType templateType;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
