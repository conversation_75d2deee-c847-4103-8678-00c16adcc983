package com.cdz360.biz.oa.param;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.model.oa.param.BillingParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class BillingEditParam extends BillingParam {

    @Schema(description = "流程实例ID")
    @JsonInclude(Include.NON_EMPTY)
    private String procInstId;


    public void checkAndFilterField() {

        if (StringUtils.isBlank(procInstId)) {
            throw new DcArgumentException("流程实例ID不能为空");
        }

        super.checkAndFilterField();
    }

}
