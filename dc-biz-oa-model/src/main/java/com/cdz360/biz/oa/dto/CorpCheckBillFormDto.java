package com.cdz360.biz.oa.dto;

import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.biz.model.invoice.dto.OaInvoiceBuyerInfo;
import com.cdz360.biz.model.invoice.dto.OaInvoiceSellerInfo;
import com.cdz360.biz.model.invoice.dto.OaInvoiceSphDetail;
import com.cdz360.biz.model.invoice.param.InvoiceRecordParam;
import com.cdz360.biz.model.oa.dto.OaContract;
import com.cdz360.biz.model.oa.type.OaHandleNode;
import com.cdz360.biz.model.oa.vo.InvoiceCompareVo;
import com.cdz360.biz.model.oa.vo.InvoicingContentVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "收入-企客对账OA表单信息")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CorpCheckBillFormDto extends OaStartFormBase {

    @Schema(description = "企业客户ID")
    @JsonInclude(Include.NON_EMPTY)
    private Long corpId;

    @Schema(description = "企业名称")
    @JsonInclude(Include.NON_EMPTY)
    private String corpName;

    @Schema(description = "企业摘要")
    @JsonInclude(Include.NON_EMPTY)
    private String corpDigest;

    // ========= 账单信息 ==============
    @Schema(description = "账单信息列表(接口只需传入单号即可，其他内容不需要传递)")
    @JsonInclude(Include.NON_NULL)
    private List<OaSettlementBillDto> settleBillList;

    @Schema(description = "结算单信息")
    @JsonInclude(Include.NON_NULL)
    private OaPostSettleBillInfo settBillSummary;

    @Schema(description = "结算单差异说明")
    @JsonInclude(Include.NON_EMPTY)
    private String differenceRemark;
    // ========= 结算单信息 ==============
    @Schema(description = "账期日期")
    @JsonInclude(Include.NON_NULL)
    private TimeFilter billDateRange;

    @Schema(description = "经办节点")
    @JsonInclude(Include.NON_NULL)
    private List<OaHandleNode> handleNodes;

    // ========= 用印相关信息 =========
    @Schema(description = "印章公司名称")
    @JsonInclude(Include.NON_EMPTY)
    private String signetCompany;

    @Schema(description = "印章类型")
    @JsonInclude(Include.NON_EMPTY)
    private String signetType;

    @Schema(description = "其他印章说明")
    @JsonInclude(Include.NON_EMPTY)
    private String signetDesc;
    // ========= 用印相关信息 =========

    // ========= 开票相关信息 =========
//    @Schema(description = "开票种类")
//    @JsonInclude(Include.NON_NULL)
//    private InvoiceType invoiceType;

    @Schema(description = "企业开票方式")
    @JsonInclude(Include.NON_NULL)
    private InvoicingMode invoiceWay;

    @Schema(description = "(购方)发票抬头")
    @JsonInclude(Include.NON_NULL)
    private OaInvoiceBuyerInfo invoiceBuyerInfo;

    @Schema(description = "开票主体(销方)")
    private OaInvoiceSellerInfo invoiceSellerInfo; // 记录

    @Schema(description = "发票列表")
    @JsonInclude(Include.NON_NULL)
    private List<InvoiceRecordParam> invoiceRecords;

    @Schema(description = "数据对比信息")
    @JsonInclude(Include.NON_NULL)
    private List<InvoiceCompareVo> actualData;

    @Schema(description = "发票备注")
    @JsonInclude(Include.NON_EMPTY)
    private String invoiceRemark;

    @Schema(description = "回款状态(true-已回款;false-未回款)")
    @JsonInclude(Include.NON_NULL)
    private Boolean paymentPlanStatus;

    @Schema(description = "回款计划")
    @JsonInclude(Include.NON_NULL)
    private List<PaymentPlan> paymentPlans;
    // ========= 开票相关信息 =========

    // ========= 合约 =========
    @Schema(description = "流程关联合约")
    @JsonInclude(Include.NON_NULL)
    private List<OaContract> contracts;
    // ========= 合约 =========
}
