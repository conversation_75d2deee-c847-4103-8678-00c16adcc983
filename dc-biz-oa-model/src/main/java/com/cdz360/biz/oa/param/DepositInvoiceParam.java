package com.cdz360.biz.oa.param;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.UserType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.biz.model.FileItem;
import com.cdz360.biz.model.invoice.param.InvoiceRecordParam;
import com.cdz360.biz.model.invoice.type.InvoiceChannel;
import com.cdz360.biz.model.invoice.type.InvoiceType;
import com.cdz360.biz.model.invoice.type.InvoicedStatus;
import com.cdz360.biz.model.oa.dto.OaContract;
import com.cdz360.biz.model.oa.type.OaHandleNode;
import com.cdz360.biz.model.oa.vo.InvoiceCompareVo;
import com.cdz360.biz.model.oa.vo.InvoicingContentVo;
import com.cdz360.biz.oa.vo.CorpInvoiceInfoVo;
import com.cdz360.biz.oa.vo.PayBillInvoiceBi;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * DepositInvoiceParam
 *
 * @since 3/8/2023 4:31 PM
 * <AUTHOR>
 */
@ApiModel(value = "企业充值申请")
@Data
public class DepositInvoiceParam {

    protected static final BigDecimal MAX_FIX = BigDecimal.valueOf(10000000000L);
    @Schema(description = "流程实例ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    protected String procInstId;
    @Schema(description = "账户类型")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private PayAccountType accountType;
    @Schema(description = "选中的抬头")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private CorpInvoiceInfoVo corpInvoiceInfoVo;
    @Schema(description = "订单编号列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> orderNoList;
    @Schema(description = "选中的订单列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<PayBillInvoiceBi> payBillChoiceList;
    @Schema(description = "申请单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String applyNo;
    @Schema(description = "是否为企客对账开票流程后台发起的请求 是(true) 否(false or null)")
    private Boolean billingProcessRequest;

    @Schema(description = "t_corp的id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long corpId;

    @Schema(description = "企业客户用户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long uid;

    @Schema(description = "企业名称 关联查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String corpName;

    @Schema(description = "流程状态: " +
        "草稿(NOT_SUBMITTED); 审核中(SUBMITTED); " +
        "开票中(REVIEWED); 审核未通过(AUDIT_FAILED); " +
        "开票未通过(INVOICING_FAIL);已开具(COMPLETED); " +
        "已作废(INVALID)[含红冲状态]")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InvoicedStatus status;

    @Schema(description = "开票主体ID 通过这个可以查找到开票主体")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long tempSalId;

    @Schema(description = "开具方式")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InvoiceChannel channel;

    @Schema(description = "开票主体名称 关联查询")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String saleName;

    @Schema(description = "开票主体纳税人识别号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String saleTin;

    @Schema(description = "商品行模板名称 关联查询")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String productTempName;

    @Schema(description = "商品行模板ID(t_invoiced_sal_temp.id)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long productTempId;

    @Schema(description = "企业客户开票抬头模板ID invoiced_model.id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long modelId;

    @Schema(description = "经办节点")
    @JsonInclude(Include.NON_NULL)
    private List<OaHandleNode> handleNodes;

    // ========= 用印相关信息 =========
    @Schema(description = "印章公司名称")
    @JsonInclude(Include.NON_EMPTY)
    private String signetCompany;

    @Schema(description = "印章类型")
    @JsonInclude(Include.NON_EMPTY)
    private String signetType;

    @Schema(description = "其他印章说明")
    @JsonInclude(Include.NON_EMPTY)
    private String signetDesc;
    // ========= 用印相关信息 =========

    @Schema(description = "开票抬头")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String invoiceName;

    @Schema(description = "企业、商户会员所属商户")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long commId;

    @Schema(description = "账户所属商户名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String commName;

    @Schema(description = "开票种类: PER_COMMON(个人普票); ENTER_COMMON(企业普票); ENTER_PROFESSION(企业专票)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InvoiceType invoiceType;

    @Schema(description = "企业客户开票方式: 充值预开票(PRE_PAY)、充电后开票(POST_CHARGER)、后付费账单开票(POST_SETTLEMENT)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InvoicingMode invoiceWay;

    @Schema(description = "明细订单数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer orderCnt;

    @Schema(description = "应开电费")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal actualElecFee;

    @Schema(description = "实开电费 调整后的开票金额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal fixElecFee;

    @Schema(description = "应开服务费")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal actualServFee;

    @Schema(description = "实开服务费 调整后的开票金额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal fixServFee;

    @Schema(description = "应开技术服务费")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal actualTechServFee;

    @Schema(description = "实开技术服务费 调整后的开票金额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal fixTechServFee;

    @Schema(description = "应开预付卡费")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal actualPrepaidCardFee;

    @Schema(description = "实开预付卡费")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal fixPrepaidCardFee;

    @Schema(description = "电费数量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal elecNum;

    @Schema(description = "服务费数量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal servNum;

    @Schema(description = "预付卡数量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal prepaidCardNum;

    @Schema(description = "应开总额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal totalFee;

    @Schema(description = "实开总额 调整后的开票金额")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal fixTotalFee;


//    @Deprecated
//    @Schema(description = "开票内容(实开)")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private List<InvoicingContentVo> invoicingContent;

    @Schema(description = "开票内容(实开)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<InvoiceRecordParam> invoiceRecords;

    @Schema(description = "开票数据对比")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<InvoiceCompareVo> actualData;

    @Deprecated
    @Schema(description = "开票备注")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String invoicingRemark;

    @Schema(description = "回款情况")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer returnFlag;

//    @Schema(description = "回款计划")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private List<CorpInvoiceReturnPlanVo> returnPlanVoList;

//    @Schema(description = "回款图片")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private List<String> returnImages;

    @Schema(description = "申请备注")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String note;

//    @Schema(description = "图片")
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private List<String> images;

    @Schema(description = "附件链接列表")
    @JsonInclude(Include.NON_NULL)
    private List<FileItem> attachmentLink;

    @Schema(description = "开具时间时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date issuedTime;

    @Schema(description = "开具附属信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String issuedRemark;

    @Schema(description = "申请失败原因")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String failRemark;

    @Schema(description = "审核时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date auditTime;

    @Schema(description = "审核建议")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String auditRemark;

    @Schema(description = "审核人")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String auditName;

    @Schema(description = "创建操作人类型. 0,未知; 1,商户/平台用户; 3,企业客户; 4,系统")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private UserType createOpType;

    @Schema(description = "创建操作人ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long createOpId;

    @Schema(description = "创建操作人名字")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String createOpName;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date createTime;

    @Schema(description = "操作人类型. 0,未知; 1,商户/平台用户; 3,企业客户; 4,系统")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private UserType updateOpType;

    @Schema(description = "操作人ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long updateOpId;

    @Schema(description = "操作人名字")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String updateOpName;

    @Schema(description = "发票代码")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String invoiceCode;

    @Schema(description = "发票号码")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String invoiceNumber;

    @Schema(description = "开具时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date reviewTime;

    // ========= 合约 =========
    @Schema(description = "流程关联合约")
    @JsonInclude(Include.NON_NULL)
    private List<OaContract> contracts;
    // ========= 合约 =========

    @Schema(description = "企客摘要")
    @JsonInclude(Include.NON_NULL)
    private String corpDigest;

    public void checkAndFilterField() {

        checkAmount();
        fixAmountFill();

        if (CollectionUtils.isEmpty(this.getOrderNoList())) {
            throw new DcArgumentException("请传入订单编号列表");
        }

        if (this.getCorpId() == null) {
            throw new DcArgumentException("请提供企业客户ID");
        }

        if (this.getInvoiceWay() == null) {
            throw new DcArgumentException("需要提供企业客户的开票方式");
        }

//        if (StringUtils.isBlank(this.getApplyNo())) {
//            throw new DcArgumentException("开票记录申请单号不能为空");
//        }

//        if (StringUtils.isBlank(dto.getSaleTin())) {
//            throw new DcArgumentException("开票主体纳税人识别号不能为空");
//        }
        if (null == this.getTempSalId()) {
            throw new DcArgumentException("开票主体记录ID不能为空");
        }

        if (null == this.getProductTempId()) {
            throw new DcArgumentException("商品行模板ID不能为空");
        }

//        if (null == this.getActualElecFee() && null == this.getActualServFee()) {
//            throw new DcArgumentException("应开电费和应开服务费不能同时为空");
//        }

        if ((null == this.getFixElecFee() ||
            DecimalUtils.isZero(this.getFixElecFee())) &&
            (null == this.getFixServFee() ||
                DecimalUtils.isZero(this.getFixServFee())) &&
            (null == this.getFixPrepaidCardFee() ||
                DecimalUtils.isZero(this.getFixPrepaidCardFee()))) {
            throw new DcArgumentException("修正电费和修正服务费和修正预付卡费不能同时为空");
        }

        if ((null != this.getFixElecFee() && DecimalUtils.gt(this.getFixElecFee(), MAX_FIX)) ||
            (null != this.getFixServFee() && DecimalUtils.gt(this.getFixServFee(), MAX_FIX)) ||
            (null != this.getFixPrepaidCardFee() && DecimalUtils.gt(this.getFixPrepaidCardFee(), MAX_FIX))) {
            throw new DcArgumentException("修正电费或修正服务费或修正预付卡费金额过大");
        }
    }

    protected void checkAmount() {
        if (CollectionUtils.isEmpty(this.getInvoiceRecords())) {
            throw new DcArgumentException("开票内容(实开)不能为空");
        } else {
            for (var invRec : this.getInvoiceRecords()) {
                for (var invCtx : invRec.getContents()) {
                    if (invCtx.getFixAmount() != null) {
                        if (DecimalUtils.gt(invCtx.getFixAmount(), MAX_FIX)) {
                            throw new DcArgumentException(invCtx.getProductName() + "金额过大");
                        }
                        if (DecimalUtils.isZero(invCtx.getFixAmount())) {
                            throw new DcArgumentException(invCtx.getProductName() + "金额不能为0");
                        }
                    }
                }
            }
        }

        if (this.getActualData() == null || this.getActualData().isEmpty()) {
            throw new DcArgumentException("应开数据不能为空");
        } else {
            for (InvoiceCompareVo one : this.getActualData()) {
                if (one.getFixAmount() != null && DecimalUtils.gt(one.getFixAmount(), MAX_FIX)) {
                    throw new DcArgumentException(one.getProductName() + "金额过大");
                }
            }
        }
    }

    /**
     * 修正数据自动填写
     * FIXME 由于企业牵扯的代码比较多，本应该去除的fix字段暂时维持，前端可以不传，这里自动填写
     */
    protected void fixAmountFill() {
        if (this.getActualData() != null && !this.getActualData().isEmpty()) {
            BigDecimal sum = BigDecimal.ZERO;
            for (InvoiceCompareVo one : this.getActualData()) {
                if (one.getProductType() != null) {
                    switch (one.getProductType()) {
                        case TECH_SERV_FEE:
                            this.setActualTechServFee(one.getFixAmount());
                            break;
                        case SERV_ACTUAL_FEE:
                            this.setActualServFee(one.getFixAmount());
                            break;
                        case ELEC_ACTUAL_FEE:
                            this.setActualElecFee(one.getFixAmount());
                            break;
                        case PREPAID_CARD_FEE:
                            this.setActualPrepaidCardFee(one.getFixAmount());
                            break;
                        case ACTUAL_FEE:
                        case PARK_OUT_FEE:
                        case CUSTOM:
                            break;
                    }
                }
                if (one.getFixAmount() != null) {
                    sum = sum.add(one.getFixAmount());
                }
            }
            this.setTotalFee(sum);
        }

        BigDecimal sum = BigDecimal.ZERO;
        BigDecimal elecNum = BigDecimal.ZERO;
        BigDecimal servNum = BigDecimal.ZERO;
        BigDecimal prepaidCardNum = BigDecimal.ZERO;
        if(CollectionUtils.isNotEmpty(this.getInvoiceRecords())) {
            for(var invRec : this.getInvoiceRecords()) {
                for(var invCtx : invRec.getContents()){
                    if (invCtx.getProductType() != null) {
                        switch (invCtx.getProductType()) {
                            case TECH_SERV_FEE:
                                this.setFixTechServFee(invCtx.getFixAmount());
                                break;
                            case SERV_ACTUAL_FEE:
                                this.setFixServFee(invCtx.getFixAmount());
                                servNum = servNum.add(invCtx.getNum());
                                break;
                            case ELEC_ACTUAL_FEE:
                                this.setFixElecFee(invCtx.getFixAmount());
                                elecNum = elecNum.add(invCtx.getNum());
                                break;
                            case PREPAID_CARD_FEE:
                                this.setFixPrepaidCardFee(invCtx.getFixAmount());
                                prepaidCardNum = prepaidCardNum.add(invCtx.getNum());
                                break;
                            case ACTUAL_FEE:
                            case PARK_OUT_FEE:
                            case CUSTOM:
                                break;
                        }
                    }
                    if (invCtx.getFixAmount() != null) {
                        sum = sum.add(invCtx.getFixAmount());
                    }
                }
            }

            this.setServNum(servNum);
            this.setElecNum(elecNum);
            this.setPrepaidCardNum(prepaidCardNum);
            this.setFixTotalFee(sum);
        }
    }
}