package com.cdz360.biz.oa.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.param.TimeFilter;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@ApiModel(value = "查询任务列表参数")
@Data
@EqualsAndHashCode(callSuper = true)
public class ListTaskParam extends BaseListParam {

    @ApiModelProperty(name = "商户链", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commIdChain;

    @ApiModelProperty(name = "所属顶级商户id", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected Long topCommId;

//    @ApiModelProperty(value = "用户组ID")
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String groupId;

    @ApiModelProperty(value = "当前操作用户ID", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long oUid;

    @ApiModelProperty(value = "申请人ID列表", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> createdByList;

    @ApiModelProperty(value = "申请人ID列表(搜索)", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> searchCreatedByList;

    @ApiModelProperty(value = "流程实例ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> processInstIdList;

    @ApiModelProperty(value = "流程实例ID模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String processInstIdLike;

    @ApiModelProperty(value = "提交时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter submitTime;

    @ApiModelProperty(value = "流程类型列表", notes = "存放字典中")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> oaDefinitionKeyList;

    @ApiModelProperty(value = "当前节点名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String taskName;

    @ApiModelProperty(value = "当前节点处理人")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String taskAssigneeName;

    @ApiModelProperty(value = "摘要")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String summaryLike;

    @ApiModelProperty(value = "标签")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String tagLike;

    @ApiModelProperty(value = "提交人")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String submitUserNameLike;

    @ApiModelProperty(value = "审核状态")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private AuditStatus auditStatus;

    @ApiModelProperty(value = "提交状态")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private SubmitStatus submitStatus;

    @ApiModelProperty(value = "是否完成: true(流程已完成); false(流程未完成)")
    @JsonInclude(Include.NON_NULL)
    protected Boolean finished;

    public enum AuditStatus {

        WAITING("待审核"),
        APPROVED("已审核");

        private final String desc;

        AuditStatus(String desc) {
            this.desc = desc;
        }
    }

    public enum SubmitStatus {

        UNSUBMIT("待提交");

        private final String desc;

        SubmitStatus(String desc) {
            this.desc = desc;
        }
    }
}
