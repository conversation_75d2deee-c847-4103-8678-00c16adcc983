package com.cdz360.biz.oa.param;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.DcEnum;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.model.invoice.type.ProductType;
import com.cdz360.biz.model.oa.vo.ReturnPlanVo;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.Getter;
import org.apache.commons.lang3.BooleanUtils;

@Deprecated(since = "20230310")
@Data
public class OldBillingParam {

    @Schema(description = "企业客户申请单号(新增修改时都有代码逻辑赋值，无需前端传入)", hidden = true)
    @JsonInclude(Include.NON_EMPTY)
    private String applyNo;

    @JsonInclude(Include.NON_NULL)
    private Long corpId;

    @Schema(description = "企业客户名称(新增修改时都有代码逻辑赋值，无需前端传入)", hidden = true)
    @JsonInclude(Include.NON_EMPTY)
    private String corpName;

    @Schema(description = "选中的结算单")
    @JsonInclude(Include.NON_NULL)
    private List<String> billNoList;

    @Schema(description = "结算单-总开始周期")
    @JsonInclude(Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date settlementStart;

    @Schema(description = "结算单-总结束周期")
    @JsonInclude(Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date settlementEnd;

    @Schema(description = "实际结算与平台数据是否存在差异")
    @JsonInclude(Include.NON_NULL)
    private Boolean discrepant;

    @Schema(description = "结算账单")
    @JsonInclude(Include.NON_NULL)
    private List<Summary> summaryData;

    @Schema(description = "差异说明")
    @JsonInclude(Include.NON_EMPTY)
    private String discrepantDesc;

    /**
     * {@link com.cdz360.biz.oa.OaConstants#VARIABLE_PARAM_PRINT}
     */
    @Schema(description = "是否用印")
    @JsonInclude(Include.NON_NULL)
    private Boolean useSignet;

    @Schema(description = "印章公司名称")
    @JsonInclude(Include.NON_EMPTY)
    private String signetCompany;

    @Schema(description = "印章类型")
    @JsonInclude(Include.NON_EMPTY)
    private String signetType;

    @Schema(description = "其它印章说明")
    @JsonInclude(Include.NON_EMPTY)
    private String signetDesc;

    /**
     * {@link com.cdz360.biz.oa.OaConstants#VARIABLE_PARAM_INVOICING}
     */
    @Schema(description = "是否开票")
    @JsonInclude(Include.NON_NULL)
    private Boolean invoice;

    @Schema(description = "发票抬头")
    @JsonInclude(Include.NON_EMPTY)
    private String invoiceTitle;

    @Schema(description = "开票主体")
    @JsonInclude(Include.NON_EMPTY)
    private String saleName;

    @Schema(description = "开票内容")
    @JsonInclude(Include.NON_NULL)
    private List<InvoiceVo> invoiceList;

    @Schema(description = "开票备注")
    @JsonInclude(Include.NON_EMPTY)
    private String invoiceRemark;

    @Schema(description = "回款情况")
    @JsonInclude(Include.NON_NULL)
    private Boolean returnFlag;

    @Schema(description = "回款计划")
    @JsonInclude(Include.NON_NULL)
    private List<ReturnPlanVo> returnPlanVoList;

    @Schema(description = "备注")
    @JsonInclude(Include.NON_EMPTY)
    private String note;

    @Schema(description = "附件链接列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> attachmentLink;

    @Data
    public static class Summary {

        private SummaryIndex index;

        @Schema(description = "结算单号")
        private String billNo;

        @Schema(description = "账期开始日期")
        @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
        private Date settStartDateDay;

        @Schema(description = "账期结束日期")
        @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
        private Date settEndDateDay;

        @Schema(description = "名称")
        private String name;

        @Schema(description = "总电量（kW·h）")
        private BigDecimal elec;

        @Schema(description = "总电费（元）")
        private BigDecimal elecFee;

        @Schema(description = "服务费（元）")
        private BigDecimal servFee;

        @Schema(description = "其它费用（元）")
        private BigDecimal otherFee;

        @Schema(description = "结算金额（元）")
        private BigDecimal settlementFee;
    }

    @Getter
    public enum SummaryIndex implements DcEnum {
        UNKNOWN(0, "未知"),
        BILL_ROW(1, "结算单行"),
        ACTUAL_ROW(2, "实际结算行"),
        SUMMARY_ROW(3, "结算单汇总行"),
        ACTUAL_SUMMARY_ROW(4, "实际-汇总");

        @JsonValue
        private final int code;
        private final String desc;

        SummaryIndex(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        @JsonCreator
        public static SummaryIndex valueOf(Object codeIn) {
            int code = 0;
            if (codeIn instanceof Integer) {
                code = (Integer) codeIn;
            } else if (codeIn instanceof Long) {
                code = ((Long) codeIn).intValue();
            } else if (codeIn instanceof String) {
                code = Integer.parseInt((String) codeIn);
            }

            for (SummaryIndex status : values()) {
                if (status.code == code) {
                    return status;
                }
            }
            return SummaryIndex.UNKNOWN;
        }

    }

    @Data
    public static class InvoiceVo {

        private String name;

        private ProductType type;

        private BigDecimal taxRate;

        private BigDecimal number;

        private BigDecimal amount;

        private BigDecimal unitPrice;
    }

    public void checkAndFilterField() {

        // 新增修改时都有代码逻辑赋值，无需前端传入
        applyNo = null;
        corpName = null;

        if (null == corpId) {
            throw new DcArgumentException("请选择企客名称");
        }

        if (CollectionUtils.isNotEmpty(billNoList)
            && (settlementStart == null || settlementEnd == null)) {
            throw new DcArgumentException("平台账单数据异常");
        }

        if (CollectionUtils.isEmpty(summaryData)) {
            throw new DcArgumentException("缺少结算账单入参");
        } else {
            summaryData.stream().filter(e -> e == null || e.getIndex() == null
                    || StringUtils.isBlank(e.getName()) || e.getElec() == null
                    || e.getElecFee() == null || e.getServFee() == null
                    || e.getOtherFee() == null || e.getSettlementFee() == null)
                .findFirst().ifPresent(e -> {
                    throw new DcArgumentException("结算账单数据异常");
                });
            summaryData.stream().filter(e -> SummaryIndex.BILL_ROW.equals(e.getIndex())
                    && (StringUtils.isBlank(e.getBillNo())
                    || e.getSettStartDateDay() == null
                    || e.getSettEndDateDay() == null))
                .findFirst().ifPresent(e -> {
                    throw new DcArgumentException("结算账单数据异常");
                });
            summaryData.stream().filter(e -> {
                    BigDecimal sum = e.getElecFee().add(e.getServFee()).add(e.getOtherFee());
                    return !DecimalUtils.eq(e.getSettlementFee(), sum);
                })
                .findFirst().ifPresent(e -> {
                    throw new DcArgumentException(e.getName() + "-结算金额不匹配");
                });

            long count = summaryData.stream()
                .filter(e -> SummaryIndex.BILL_ROW.equals(e.getIndex())).count();

            summaryData.stream().filter(e -> SummaryIndex.ACTUAL_ROW.equals(e.getIndex()))
                .findFirst()
                .ifPresentOrElse(e -> {
                    if (count > 0) {
                        AtomicReference<BigDecimal> actualElecRef = new AtomicReference<>(
                            BigDecimal.ZERO);
                        AtomicReference<BigDecimal> actualElecFeeRef = new AtomicReference<>(
                            BigDecimal.ZERO);
                        AtomicReference<BigDecimal> actualServFeeRef = new AtomicReference<>(
                            BigDecimal.ZERO);
                        AtomicReference<BigDecimal> actualOtherFeeRef = new AtomicReference<>(
                            BigDecimal.ZERO);
                        AtomicReference<BigDecimal> actualSettFeeRef = new AtomicReference<>(
                            BigDecimal.ZERO);
                        summaryData.stream().filter(t -> SummaryIndex.BILL_ROW.equals(t.getIndex()))
                            .forEach(t -> {
                                actualElecRef.updateAndGet(
                                    v -> DecimalUtils.add(v, t.getElec()));
                                actualElecFeeRef.updateAndGet(
                                    v -> DecimalUtils.add(v, t.getElecFee()));
                                actualServFeeRef.updateAndGet(
                                    v -> DecimalUtils.add(v, t.getServFee()));
                                actualOtherFeeRef.updateAndGet(
                                    v -> DecimalUtils.add(v, t.getOtherFee()));
                                actualSettFeeRef.updateAndGet(
                                    v -> DecimalUtils.add(v, t.getSettlementFee()));
                            });

                        boolean boo = DecimalUtils.eq(actualElecRef.get(), e.getElec())
                            && DecimalUtils.eq(actualElecFeeRef.get(), e.getElecFee())
                            && DecimalUtils.eq(actualServFeeRef.get(), e.getServFee())
                            && DecimalUtils.eq(actualOtherFeeRef.get(), e.getOtherFee())
                            && DecimalUtils.eq(actualSettFeeRef.get(), e.getSettlementFee());
                        if (!boo) {
                            throw new DcArgumentException("结算账单数据不匹配");
                        }
                    }
                }, () -> {
                    throw new DcArgumentException("实际结算数据不能为空");
                });

            // 实际 - 汇总
            summaryData.stream().filter(e -> SummaryIndex.ACTUAL_SUMMARY_ROW.equals(e.getIndex()))
                .findFirst()
                .ifPresentOrElse(e -> {
                    // 实际结算
                    Summary actualInfo = summaryData.stream()
                        .filter(i -> SummaryIndex.ACTUAL_ROW.equals(i.getIndex())).findFirst()
                        .get();
                    // 结算单汇总
                    Summary billInfo = summaryData.stream()
                        .filter(i -> SummaryIndex.SUMMARY_ROW.equals(i.getIndex())).findFirst()
                        .get();

                    boolean boo =
                        DecimalUtils.eq(e.getElec().add(billInfo.getElec()), actualInfo.getElec())
                            && DecimalUtils.eq(e.getElecFee().add(billInfo.getElecFee()),
                            actualInfo.getElecFee())
                            && DecimalUtils.eq(e.getServFee().add(billInfo.getServFee()),
                            actualInfo.getServFee())
                            && DecimalUtils.eq(e.getOtherFee().add(billInfo.getOtherFee()),
                            actualInfo.getOtherFee())
                            && DecimalUtils.eq(
                            e.getSettlementFee().add(billInfo.getSettlementFee()),
                            actualInfo.getSettlementFee());
                    if (!boo) {
                        throw new DcArgumentException("(实际-汇总)数据不匹配");
                    }
                }, () -> {
                    throw new DcArgumentException("(实际-汇总)数据不能为空");
                });

            summaryData = summaryData.stream()
                .sorted(Comparator.comparingInt(e -> e.getIndex().code))
                .collect(Collectors.toList());
        }

        if (BooleanUtils.isNotTrue(useSignet)) {
            signetCompany = null;
            signetType = null;
            signetDesc = null;
        }

        if (Boolean.TRUE.equals(invoice)) {
            if (CollectionUtils.isEmpty(invoiceList)) {
                throw new DcArgumentException("勾选开票节点时，开票内容不能为空");
            }
            if (StringUtils.isBlank(invoiceTitle) || StringUtils.isBlank(saleName)) {
                throw new DcArgumentException("勾选开票节点时，开票信息不能为空");
            }
            invoiceList.stream().filter(e -> e == null || StringUtils.isBlank(e.getName())
                    || e.getType() == null || e.getTaxRate() == null
                    || e.getNumber() == null || e.getAmount() == null || e.getUnitPrice() == null)
                .findFirst().ifPresent(e -> {
                    throw new DcArgumentException("开票内容数据异常");
                });
            long count = invoiceList.stream().filter(e -> DecimalUtils.isZero(e.getAmount()))
                .count();
            if (invoiceList.size() == count) {
                throw new DcArgumentException("开票金额不能全部为空");
            }

            summaryData.stream().filter(e -> SummaryIndex.ACTUAL_ROW.equals(e.getIndex()))
                .findFirst()
                .ifPresent(data -> {
                    invoiceList.stream().filter(e -> {
                            boolean invalid = DecimalUtils.eq(e.getAmount(),
                                e.getUnitPrice().multiply(e.getNumber())) == false;

                            if (ProductType.ELEC_ACTUAL_FEE.equals(e.getType())) {
                                invalid = invalid
                                    || DecimalUtils.eq(data.getElecFee(), e.getAmount()) == false;
                            } else if (ProductType.SERV_ACTUAL_FEE.equals(e.getType())) {
                                BigDecimal fee = DecimalUtils.add(data.getServFee(),
                                    data.getOtherFee());
                                invalid = invalid || DecimalUtils.eq(fee, e.getAmount()) == false;
                            }
                            return invalid;
                        })
                        .findFirst().ifPresent(e -> {
                            throw new DcArgumentException("开票内容与实际结算数据不一致");
                        });
                });
        } else {
            invoiceTitle = null;
            saleName = null;
            invoiceList = null;
            invoiceRemark = null;
            returnFlag = null;
            returnPlanVoList = null;
        }

        if (Boolean.FALSE.equals(returnFlag)) {
            if (CollectionUtils.isEmpty(returnPlanVoList)) {
                throw new DcArgumentException("勾选未回款时，回款计划不能为空");
            }
            returnPlanVoList.stream().filter(e -> e == null
                    || e.getIndex() == null
                    || (e.getAmount() == null && e.getTime() != null)
                    || (e.getAmount() != null && e.getTime() == null))
                .findFirst().ifPresent(e -> {
                    if (e.getIndex() != 4) { // 4-合计行
                        throw new DcArgumentException("回款计划数据异常");
                    }
                });
            // 回款计划合计
            BigDecimal sumAmount = returnPlanVoList.stream()
                .filter(e -> e.getAmount() != null && e.getIndex() != 4)
                .map(ReturnPlanVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            returnPlanVoList.stream().filter(e -> e.getIndex() == 4)
                .findFirst().ifPresent(e -> {
                    BigDecimal amount = e.getAmount();
                    if (!DecimalUtils.eq(sumAmount, amount)) {
                        throw new DcArgumentException("回款计划合计金额不正确");
                    }
                });

            returnPlanVoList = returnPlanVoList.stream()
                .filter(e -> e.getAmount() != null && e.getTime() != null)
                .sorted(Comparator.comparingInt(ReturnPlanVo::getIndex))
                .collect(Collectors.toList());
        } else {
            returnPlanVoList = null;
        }

    }

}
