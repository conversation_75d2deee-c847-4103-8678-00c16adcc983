package com.cdz360.biz.oa.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class OaTaskDto {

    // =========== 👇 当前Task信息 ==================
    @ApiModelProperty(value = "任务ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String id;

    @ApiModelProperty(value = "任务名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String name;

    @ApiModelProperty(value = "当前指派的执行者的uid")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long assignee;    // 当前指派的执行者的uid

    @ApiModelProperty(value = "当前指派的执行者的名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String assigneeName;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String taskDefinitionKey;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String taskDefinitionId;
    // =========== 👆 当前Task信息 ==================

    // ======= 👇 申请信息 ================
    @ApiModelProperty(value = "申请单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dataId;

    @ApiModelProperty(value = "申请人uid")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long oUid;    // 申请人uid

    @ApiModelProperty(value = "申请人名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String oName;   // 申请人名字
    // ======= 👆 申请信息 ================

    // ===== 👇 申请流程实例信息 ====================
    @ApiModelProperty(value = "流程实例ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String processInstanceId;   // 流程实例ID

    @ApiModelProperty(value = "结束时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "提交时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    // ======= 👆 申请流程实例信息 ================

    // ===== 👇 流程定义信息 ====================
    private String oaKey;   // OA流程编码
    private String oaName;  // OA流程名字
    // ===== 👆 流程定义信息 ====================
}
