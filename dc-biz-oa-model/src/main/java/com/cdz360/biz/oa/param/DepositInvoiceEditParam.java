package com.cdz360.biz.oa.param;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.StringUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * DepositInvoiceEditParam
 *  充值开票-编辑
 * @since 3/21/2023 11:08 AM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class DepositInvoiceEditParam extends DepositInvoiceParam {

//    @Schema(description = "流程实例ID")
//    @JsonInclude(Include.NON_EMPTY)
//    private String procInstId;


    public void checkAndFilterField() {

        if (StringUtils.isBlank(procInstId)) {
            throw new DcArgumentException("流程实例ID不能为空");
        }

        super.checkAndFilterField();
    }

}