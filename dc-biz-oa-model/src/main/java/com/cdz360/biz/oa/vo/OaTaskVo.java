package com.cdz360.biz.oa.vo;

import com.cdz360.biz.oa.dto.OaTaskDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "任务信息详情")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class OaTaskVo extends OaTaskDto {

    @ApiModelProperty(value = "当前节点所属审核组类型", notes = "被分配组可用")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String groupType;

    @ApiModelProperty(value = "当前用户是否属于审核组成员")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean inGroupMember;

    @ApiModelProperty(value = "是否可以审核")
    @JsonInclude(Include.NON_NULL)
    @Deprecated // TODO: 2025/7/30 该字段赋值逻辑有误，不要使用
    private Boolean canAudit;

    @ApiModelProperty(value = "摘要JSON")
    @JsonInclude(Include.NON_EMPTY)
    private String summaryJson;

    @ApiModelProperty(value = "标签")
    @JsonInclude(Include.NON_NULL)
    private List<String> tagList;

}
