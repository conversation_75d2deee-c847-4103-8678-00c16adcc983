package com.cdz360.biz.oa.param.account;

import com.cdz360.base.utils.JsonUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OaModifyAccountParam {

    @Schema(description = "用户UID", required = true)
    private Long uid;
    @Schema(description = "姓名")
    private String name;
    @Schema(description = "email地址")
    private String email;
    @Schema(description = "集团商户id")
    private Long topCommId;

    @Schema(description = "操作人uid", required = true)
    private Long opUid;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
