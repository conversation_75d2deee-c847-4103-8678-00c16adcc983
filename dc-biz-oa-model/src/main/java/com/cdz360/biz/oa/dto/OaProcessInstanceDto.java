package com.cdz360.biz.oa.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class OaProcessInstanceDto {

    private String id;
    private String oaKey;
    private String oaName;
    private Long oUid;


    private String curReviewerTaskName; // 当前审批节点名称, 如"商务审批"
    private Long curReviewerUid;  // 当前审批人UID
    private String curReviewerName; // 当前审批人名字

    private Date createTime;
}
