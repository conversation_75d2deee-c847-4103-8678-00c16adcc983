package com.cdz360.biz.oa.param.account;

import com.cdz360.base.model.base.param.BaseListParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListOaGroupUserParam extends BaseListParam {

    @Schema(description = "租户ID")
    @JsonInclude(Include.NON_EMPTY)
    private String tenantId;

    @Schema(description = "审核组ID")
    @JsonInclude(Include.NON_EMPTY)
    private String groupId;

    @Schema(description = "用户名称")
    @JsonInclude(Include.NON_EMPTY)
    private String uName;
}
