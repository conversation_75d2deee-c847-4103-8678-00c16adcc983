package com.cdz360.biz.oa.dto.account;

import com.cdz360.biz.model.invoice.type.InvoiceType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CorpInvoiceDto  {
    @Schema(description = "纳税人识别号(开票主体) 通过这个可以查找到开票主体")
    private String saleTin;

    @Schema(description = "开票主体名称", required = true)
    private String saleName;

    // 个人发票模板信息
    @Schema(description = "发票类型: " +
        "PER_COMMON -- 个人普票;" +
        "ENTER_COMMON -- 企业普票;" +
        "ENTER_PROFESSION -- 企业专票")
    private InvoiceType invoiceType;

    @Schema(description = "企业客户开票抬头模板ID invoiced_model.id")
    private Long modelId;

    @Schema(description = "发票抬头")
    private String name;

    @Schema(description = "电子邮件")
    private String email;

    @Schema(description = "纳税人识别号(企业专票需要填写)")
    private String tin;

    @Schema(description = "详细街道信息(企业地址)")
    private String address;

    @Schema(description = "联系方式(企业联系方式)")
    private String tel;

    @Schema(description = "开户银行")
    private String bank;

    @Schema(description = "银行账号")
    private String bankAccount;

    @Schema(description = "收件人")
    private String receiverName;//收件人

    @Schema(description = "收件人手机号")
    private String receiverMobilePhone;//收件人手机号

    @Schema(description = "收件人省")
    private String receiverProvince;    //收件人省

    @Schema(description = "收件人市")
    private String receiverCity; // 收件人市

    @Schema(description = "收件人区")
    private String receiverArea;// 收件人区

    @Schema(description = "收件人详细地址")
    private String receiverAddress;//收件人地址

}
