package com.cdz360.biz.oa.vo;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.oa.vo.OaStatisticsInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OaElecPayTaskVo {

    @ApiModelProperty(value = "任务ID")
    private String taskId;

    @ApiModelProperty(value = "流程实例ID")
    private String procInstId;

    @ApiModelProperty(value = "OA流程编码")
    private String oaKey;

    @ApiModelProperty(value = "OA流程名字")
    private String oaName;

    private String siteId;

    private String siteName;

    @Schema(description = "默认配置-电损是否显示")
    private Boolean defaultShowDiscount;

    @Schema(description = "本期")
    private PeriodVo currPeriod;

    @Schema(description = "有效上期")
    private PeriodVo lastPeriod;

    @Schema(description = "账期开始日期String")
    @JsonInclude(Include.NON_EMPTY)
    private String billStartDate;

    @Schema(description = "账期结束日期String")
    @JsonInclude(Include.NON_EMPTY)
    private String billEndDate;

    @JsonIgnore
    private LocalDate maxBillDate;

    @JsonIgnore
    @ApiModelProperty(value = "流程内表单数据")
    private OaStatisticsInfo statisticsInfo;

//    @ApiModelProperty(value = "本期数据")
//    private SiteIncomeExpenseVo currMonth;

    @Override
    public String toString() {
        return JsonUtils.toJsonTimeString(this);
    }

    @Data
    @Accessors(chain = true)
    public static class PeriodVo {
        @Schema(description = "效率")
        private BigDecimal efficiency;

        @Schema(description = "电损")
        private BigDecimal discount;

        @Schema(description = "电费单价")
        private BigDecimal elecUnitPrice;

        @Schema(description = "服务费单价")
        private BigDecimal servUnitPrice;
    }

}
