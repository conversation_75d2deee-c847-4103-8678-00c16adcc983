package com.cdz360.biz.oa.vo;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BatchApproveResult {

    /**
     * procInstId
     */
    private List<String> successProcList;

    /**
     * procInstId
     */
    private List<FailVo> failureProcList;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

    @Data
    public static class FailVo {

        private String procInstId;

        private String failMsg;
    }

}
