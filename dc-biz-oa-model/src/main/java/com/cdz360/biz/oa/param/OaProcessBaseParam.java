package com.cdz360.biz.oa.param;

import com.cdz360.base.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * OA流程基础参数
 */
@Data
@Accessors(chain = true)
public class OaProcessBaseParam {

    /**
     * 申请人uid
     */
    @JsonProperty("oUid")
    protected Long oUid;

    /**
     * 申请人名字
     */
    @JsonProperty("oName")
    protected String oName;

    /**
     * 申请人手机号
     */
    @JsonProperty("oPhone")
    protected String oPhone;

    @JsonProperty("topCommId")
    protected Long topCommId;

    @JsonProperty("tenantIdChain")
    protected String tenantIdChain;

    protected Map<String, Object> formVariables;

    public OaProcessBaseParam(Long oUid, String oName, String oPhone, Long topCommId,
        String tenantIdChain) {
        this.oUid = oUid;
        this.oName = oName;
        this.oPhone = oPhone;
        this.topCommId = topCommId;
        this.tenantIdChain = tenantIdChain;
    }

    public OaProcessBaseParam() {
    }

    /**
     * Date类型转为String再存入flowable库
     *
     * @param beanMap
     * @return
     */
    protected Map<String, Object> formatDate(Map<String, Object> beanMap) {
        return formatDate(beanMap, "yyyy-MM-dd");
    }

    /**
     * Date类型转为String再存入flowable库
     *
     * @param beanMap
     * @return
     */
    protected Map<String, Object> formatDate(Map<String, Object> beanMap, String format) {
        Map<String, Object> resMap = new HashMap<>();
        beanMap.forEach((key, obj) -> {
            if (obj instanceof Date) {
                obj = DateUtils.toStringFormat((Date) obj, format);
            }
            resMap.put(key, obj);
        });
        return resMap;
    }

}
