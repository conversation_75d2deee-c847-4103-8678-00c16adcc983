package com.cdz360.biz.oa.param;

import com.cdz360.biz.oa.dto.CreateProcessInstanceDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@ApiModel(value = "申请流程")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CreateProcessInstanceParam extends CreateProcessInstanceDto {

    @ApiModelProperty(value = "申请人ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String createByUid;

    @ApiModelProperty(value = "租户ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String tenantId;

    @ApiModelProperty(value = "租户ID链")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String tenantIdChain;
}
