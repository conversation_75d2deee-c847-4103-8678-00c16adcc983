package com.cdz360.biz.oa.param;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.oa.param.BillingParam;
import java.io.Serializable;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.cglib.beans.BeanMap;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class BillingProcessParam extends OaProcessBaseParam implements Serializable {

    public BillingProcessParam() {
    }

    public BillingProcessParam(Long oUid, String oName, String oPhone, Long topCommId,
        String tenantIdChain, BillingParam param) {
        this.oUid = oUid;
        this.oName = oName;
        this.oPhone = oPhone;
        this.topCommId = topCommId;
        this.tenantIdChain = tenantIdChain;
        Map<String, Object> beanMap = BeanMap.create(param);
        this.formVariables = super.formatDate(beanMap);
    }

    public BillingProcessParam(Long oUid, String oName, String oPhone, Long topCommId,
        String tenantIdChain, BillingEditParam param) {
        this.oUid = oUid;
        this.oName = oName;
        this.oPhone = oPhone;
        this.topCommId = topCommId;
        this.tenantIdChain = tenantIdChain;

        param.setProcInstId(null); // 忽略此参数
        Map<String, Object> beanMap = BeanMap.create(param);
        this.formVariables = super.formatDate(beanMap);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}
