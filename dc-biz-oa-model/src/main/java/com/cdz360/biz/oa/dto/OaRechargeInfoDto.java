package com.cdz360.biz.oa.dto;

import com.cdz360.biz.oa.vo.FormModelVo;
import com.cdz360.biz.oa.vo.OaTaskCommentVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

@ApiModel(value = "充值申请详情")
@Data
@Accessors(chain = true)
public class OaRechargeInfoDto {

    // =========== 👇 当前Task信息 ==================
    @ApiModelProperty(value = "任务ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String id;

    @ApiModelProperty(value = "任务名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String name;

    @ApiModelProperty(value = "当前指派的执行者的uid")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String assignee;    // 当前指派的执行者的uid

    @ApiModelProperty(value = "当前指派的执行者的名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String assigneeName;

    @ApiModelProperty(value = "当前节点所属审核组类型", notes = "被分配组可用")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String groupType;

    @ApiModelProperty(value = "当前用户是否属于当前节点审核组成员")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean inGroupMember;

    @ApiModelProperty(value = "当前用户是否属于历史审核成员")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean inHistoryLink;
    // =========== 👆 当前Task信息 ==================

    // ======= 👇 申请信息 ================
    @ApiModelProperty(value = "申请单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dataId;

    @ApiModelProperty(value = "申请人uid")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String oUid;    // 申请人uid

    @ApiModelProperty(value = "申请人名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String oName;   // 申请人名字
    // ======= 👆 申请信息 ================

    // ===== 👇 申请流程实例信息 ====================
    @ApiModelProperty(value = "流程实例ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String processInstanceId;   // 流程实例ID

    @ApiModelProperty(value = "流程实例名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String processInstanceName;   // 流程实例ID

    @ApiModelProperty(value = "结束时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "提交时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    // ======= 👆 申请流程实例信息 ================

    // ===== 👇 流程定义信息 ====================
    @ApiModelProperty(value = "流程实例ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String processDefinitionKey;

    @ApiModelProperty(value = "流程实例ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String processDefinitionName;
    // ===== 👇 流程定义信息 ====================

    // 流程实例附言信息
    @Schema(description = "流程实例附言信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<OaTaskCommentVo> commentList;

    // 提交表单信息
    @Schema(description = "提交表单信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Map formInfo;

    // 提交表单信息(自带表单数据)
    @Schema(description = "表单信息(自带表单数据)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private FormModelVo formModelData;

    @Schema(description = "分好组的原场站数据")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<List<TargetPriceSchemeInfo>> groupPriceSchema;
}
