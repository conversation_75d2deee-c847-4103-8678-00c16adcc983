package com.cdz360.biz.oa.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "OA结算单概要信息")
public class OaSettJobBillDto {

    @ApiModelProperty(value = "结算单号")
    @NotNull(message = "billNo 不能为 null")
    @Size(max = 32, message = "billNo 长度不能超过 32")
    private String billNo;

    @ApiModelProperty(value = "结算任务名称(t_gc_profit_cfg.name)")
    @Size(max = 64, message = "jobName 长度不能超过 64")
    private String jobName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "结算周期(开始时间)")
    private Date settPeriodFrom;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "结算周期(结束时间)")
    private Date settPeriodTo;

    @Schema(description = "电量(单位: kW·h)")
    private BigDecimal elec;

    @ApiModelProperty(value = "电费(单位: 元)")
    private BigDecimal elecFee;

    @ApiModelProperty(value = "服务费(单位: 元)")
    private BigDecimal servFee;

    @ApiModelProperty(value = "停充超时费(单位: 元)")
    private BigDecimal parkFee;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "更新时间")
    @NotNull(message = "updateTime 不能为 null")
    private Date updateTime;
}
