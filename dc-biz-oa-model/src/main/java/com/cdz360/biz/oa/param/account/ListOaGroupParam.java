package com.cdz360.biz.oa.param.account;

import com.cdz360.base.model.base.param.BaseListParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListOaGroupParam extends BaseListParam {
    @Schema(description = "组类型")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String groupType;

    @Schema(description = "组名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String groupName;

    @Schema(description = "审核组ID列表", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> groupIdList;

    @Schema(description = "组成员ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String memberId;

    @Schema(description = "组成员ID列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> memberIds;

}
