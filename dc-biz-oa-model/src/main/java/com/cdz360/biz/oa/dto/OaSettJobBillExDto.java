package com.cdz360.biz.oa.dto;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * OaSettJobBillExDto
 *
 * @since 5/30/2023 4:27 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class OaSettJobBillExDto extends OaSettJobBillDto {
    @ApiModelProperty(value = "结算总金额(单位: 元)")
    private BigDecimal sum;
}