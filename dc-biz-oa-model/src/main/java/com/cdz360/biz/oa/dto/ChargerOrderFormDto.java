package com.cdz360.biz.oa.dto;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.biz.model.invoice.dto.OaInvoiceBuyerInfo;
import com.cdz360.biz.model.invoice.dto.OaInvoiceSellerInfo;
import com.cdz360.biz.model.invoice.dto.OaInvoiceSphDetail;
import com.cdz360.biz.model.invoice.param.InvoiceRecordParam;
import com.cdz360.biz.model.invoice.type.InvoiceType;
import com.cdz360.biz.model.oa.dto.OaContract;
import com.cdz360.biz.model.oa.type.OaHandleNode;
import com.cdz360.biz.model.oa.vo.InvoiceCompareVo;
import com.cdz360.biz.model.oa.vo.OaOrderBiVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "收入-充电开票OA表单信息")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ChargerOrderFormDto extends OaStartFormBase {

    @Schema(description = "账户类型: 个人账户/商户会员/企业账户")
    @JsonInclude(Include.NON_NULL)
    private PayAccountType accountType;

    @Schema(description = "所属商户(企业客户/商户会员需要提供)")
    @JsonInclude(Include.NON_NULL)
    private Long commId;

    @Schema(description = "所属商户名称(企业客户/商户会员需要提供)")
    @JsonInclude(Include.NON_NULL)
    private String commName;

    @Schema(description = "用户ID(t_user.id)")
    @JsonInclude(Include.NON_NULL)
    private Long userId;

    @Schema(description = "用户手机号(个人账户/商户会员)", hidden = true)
    private String phone;

    @Schema(description = "企业客户ID-企业客户需要提供", hidden = true)
    @JsonInclude(Include.NON_EMPTY)
    private Long corpId;

    @Schema(description = "企业名称-企业客户需要提供", hidden = true)
    @JsonInclude(Include.NON_EMPTY)
    private String corpName;

    @Schema(description = "企业摘要-企业客户需要提供", hidden = true)
    @JsonInclude(Include.NON_EMPTY)
    private String corpDigest;

    // ========= 账单信息 ==============
    @Schema(description = "充值记录列表(接口只需传入单号即可，其他内容不需要传递)")
    @JsonInclude(Include.NON_NULL)
    private List<OaChargerOrderDto> chargerOrderList;

    // >>>拼凑数据对比数据-不需要接口提供，也不需要保存<<<
    // 平台数据(充值单列表中提取)；实际结算(商品行信息中提取)
    @Schema(description = "充电订单汇总信息")
    @JsonInclude(Include.NON_NULL)
    private OaOrderBiVo orderBiVo;
    // ========= 结算单信息 ==============

    @Schema(description = "经办节点")
    @JsonInclude(Include.NON_NULL)
    private List<OaHandleNode> handleNodes;

    // ========= 用印相关信息 =========
    @Schema(description = "印章公司名称")
    @JsonInclude(Include.NON_EMPTY)
    private String signetCompany;

    @Schema(description = "印章类型")
    @JsonInclude(Include.NON_EMPTY)
    private String signetType;

    @Schema(description = "其他印章说明")
    @JsonInclude(Include.NON_EMPTY)
    private String signetDesc;
    // ========= 用印相关信息 =========

    // ========= 开票相关信息 =========
    @Schema(description = "开票种类")
    @JsonInclude(Include.NON_NULL)
    private InvoiceType invoiceType;

    @Schema(description = "(购方)发票抬头")
    @JsonInclude(Include.NON_NULL)
    private OaInvoiceBuyerInfo invoiceBuyerInfo;

    @Schema(description = "开票主体(销方)")
    private OaInvoiceSellerInfo invoiceSellerInfo;


    @Schema(description = "发票信息,可能会有多张")
    @JsonInclude(Include.NON_NULL)
    private List<InvoiceRecordParam> invoiceRecords;

    @Schema(description = "数据对比信息")
    @JsonInclude(Include.NON_NULL)
    private List<InvoiceCompareVo> actualData;

    @Deprecated
    @Schema(description = "发票备注")
    @JsonInclude(Include.NON_EMPTY)
    private String invoiceRemark;
    // ========= 开票相关信息 =========

    // ========= 合约 =========
    @Schema(description = "流程关联合约")
    @JsonInclude(Include.NON_NULL)
    private List<OaContract> contracts;
    // ========= 合约 =========
}
