package com.cdz360.biz.oa.dto;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "电价调整-智能策略-峰平谷模式下的电价信息")
@Data
@Accessors(chain = true)
public class SmartStrategyElecPrice implements Serializable {

    @Schema(description = "尖时电价")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal sharpPeakElecPrice;

    @Schema(description = "峰时电价")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal peakElecPrice;

    @Schema(description = "平时电价")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal offPeakElecPrice;

    @Schema(description = "谷时电价")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal valleyElecPrice;


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
