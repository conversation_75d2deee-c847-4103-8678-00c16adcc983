package com.cdz360.biz.oa.param;

import com.cdz360.base.utils.JsonUtils;
import java.io.Serializable;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.cglib.beans.BeanMap;

/**
 * DepositInvoiceProcessParam
 *
 * @since 3/10/2023 10:35 AM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class DepositInvoiceProcessParam extends OaProcessBaseParam implements Serializable {

    public DepositInvoiceProcessParam() {
    }

    public DepositInvoiceProcessParam(Long oUid, String oName, String oPhone, Long topCommId,
        String tenantIdChain, DepositInvoiceParam param) {
        super(oUid, oName, oPhone, topCommId, tenantIdChain);
        Map<String, Object> beanMap = BeanMap.create(param);
        this.formVariables = super.formatDate(beanMap, "yyyy-MM-dd HH:mm:ss");
    }

    public DepositInvoiceProcessParam(Long oUid, String oName, String oPhone, Long topCommId,
        String tenantIdChain, DepositInvoiceEditParam param) {
        super(oUid, oName, oPhone, topCommId, tenantIdChain);

        param.setProcInstId(null); // 忽略此参数
        Map<String, Object> beanMap = BeanMap.create(param);
        this.formVariables = super.formatDate(beanMap, "yyyy-MM-dd HH:mm:ss");
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}