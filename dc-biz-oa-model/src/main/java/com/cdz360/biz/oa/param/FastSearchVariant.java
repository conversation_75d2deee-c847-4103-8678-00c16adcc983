package com.cdz360.biz.oa.param;

import com.cdz360.base.model.base.vo.BaseObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * FastSearchVariant
 *  快速搜索
 * @since 5/19/2023 4:33 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class FastSearchVariant extends BaseObject {

    // 关联场站
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> siteIdList;

    // 关联企业
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<Long> corpIdList;
}