package com.cdz360.biz.oa.param;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * FastSearchParam
 *
 * @since 5/22/2023 1:19 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class FastSearchParam {

    private String siteId;

    private List<String> siteIdList;
    @Schema(description = "前端无需传递")
    private String siteIdListStr;

    private Long corpId;
    private String excludeProcInstId;

    public void listSortAndSetString() {
        if (CollectionUtils.isNotEmpty(siteIdList)) {
            siteIdList = siteIdList.stream().sorted().collect(Collectors.toList());
            siteIdListStr = JsonUtils.toJsonString(siteIdList);
        }
    }

}