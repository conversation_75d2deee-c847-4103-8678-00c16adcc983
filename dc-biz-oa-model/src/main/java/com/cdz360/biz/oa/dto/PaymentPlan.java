package com.cdz360.biz.oa.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "回款计划")
@Data
@Accessors(chain = true)
public class PaymentPlan {

    @Schema(description = "回款顺序")
    private int order;

    @Schema(description = "回款金额,单位: 元")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal amount;

    @Schema(description = "回款时间")
    @JsonInclude(Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date time;
}
