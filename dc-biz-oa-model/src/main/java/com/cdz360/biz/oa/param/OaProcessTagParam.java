package com.cdz360.biz.oa.param;

import com.cdz360.base.utils.JsonUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class OaProcessTagParam {

    private String procInstId;

    private List<String> procInstIdList;

    @Schema(description = "标签列表")
    private List<String> tagList;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
