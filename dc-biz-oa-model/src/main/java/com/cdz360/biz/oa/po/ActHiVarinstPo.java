package com.cdz360.biz.oa.po;

import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ActHiVarinstPo {

    private String ID_;
    private Integer REV_;
    private String PROC_INST_ID_;
    private String EXECUTION_ID_;
    private String TASK_ID_;
    private String NAME_;
    private String VAR_TYPE_;
    private String SCOPE_ID_;
    private String SUB_SCOPE_ID_;
    private String SCOPE_TYPE_;
    private String BYTEARRAY_ID_;
    private Double DOUBLE_;
    private Long LONG_;
    private String TEXT_;
    private String TEXT2_;
    private Date CREATE_TIME_;
    private Date LAST_UPDATED_TIME_;

}
