package com.cdz360.biz.oa.param;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "OA流程启动参数")
@Data
@Accessors(chain = true)
public class OaStartProcessParam implements Serializable {

    @Schema(description = "租户ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String topCommId;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "商户ID链", example = "34474,34475 ant入参有值表示受商户链限制")
    private String commIdChain;

    @Schema(description = "申请人ID")
    private String opId;

    @Schema(description = "申请人名称")
    private String opName;

    // ===== 流程信息 =====
    @Schema(description = "流程定义ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String processDefinitionId;

    @Schema(description = "流程定义KEY")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String processDefinitionKey;

    @Schema(description = "重新提交流程需要提供(提交时可以不提供)")
    @JsonInclude(Include.NON_EMPTY)
    private String taskId;

    @Schema(description = "流程表单参数", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_NULL)
    private Map<String, Object> data;

    @Schema(description = "是否来自批量创建请求")
    private Boolean fromBatchCreateReq;

    @Schema(description = "流程表单参数")
    @JsonInclude(Include.NON_NULL)
    private List<Map<String, Object>> dataList;

    @Schema(description = "true-表示配置表单;false-表示自定义表单", hidden = true)
    protected boolean autoForm;

    @Schema(description = "商户id")
    private Long commId;

    @Schema(description = "用户手机号")
    private String sysUserPhone;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}
