package com.cdz360.biz.oa.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class FormModelVo {

    private String id;
    private String name;
    private String description;
    private String key;
    private int version;
    private List<FormField> fields;
    private List<FormOutcome> outcomes;
    private String outcomeVariableName;

}
