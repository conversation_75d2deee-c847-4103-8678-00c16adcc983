package com.cdz360.biz.oa.dto;

import com.cdz360.biz.model.FileItem;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.cglib.beans.BeanMap;

@Data
@Accessors(chain = true)
@Schema(description = "OA流程表单提交基类")
public class OaStartFormBase {

    @Schema(description = "提交备注")
    @JsonInclude(Include.NON_EMPTY)
    private String note;

    @Schema(description = "附件链接列表")
    @JsonInclude(Include.NON_NULL)
    private List<FileItem> attachmentLink;

    // 转成MAP
    @SuppressWarnings({"unchecked"})
    public Map<String, Object> toMap() {
        HashMap<String, Object> result = new HashMap<>();

        BeanMap data = BeanMap.create(this);
        data.keySet().forEach(key -> {
            if (null != data.get(key)) { // 去掉null
                result.put(key.toString(), data.get(key));
            }
        });

        return result;
    }
}
