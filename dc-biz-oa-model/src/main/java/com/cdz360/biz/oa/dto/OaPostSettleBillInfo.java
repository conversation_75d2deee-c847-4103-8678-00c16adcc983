package com.cdz360.biz.oa.dto;

import com.cdz360.biz.model.invoice.dto.OaActualData;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "后付费充电订单结算单汇总信息")
public class OaPostSettleBillInfo {

    @Schema(description = "结算单是否存在差异")
    @JsonInclude(Include.NON_NULL)
    private Boolean difference;

    @Schema(description = "账单信息列表")
    @JsonInclude(Include.NON_NULL)
    private List<OaSettlementBillDto> settleBillList;

    @Schema(description = "实际结算信息")
    @JsonInclude(Include.NON_NULL)
    private OaActualData actualData;
}
