package com.cdz360.biz.oa.param;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.FileItem;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.util.List;
import lombok.Data;

@Data
public class ApproveParam {

    private String procInstId;
    private Long uid;
    @Schema(description = "当前任务ID(流程实例结束时可以为空)", requiredMode = RequiredMode.NOT_REQUIRED)
    private String taskId;

    private String dataId;

    @Schema(description = "审批结果 true, 通过; false, 拒回", required = true)
    private Boolean approve;
    private String note;

    @Schema(description = "附件链接列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<FileItem> attachmentList;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
