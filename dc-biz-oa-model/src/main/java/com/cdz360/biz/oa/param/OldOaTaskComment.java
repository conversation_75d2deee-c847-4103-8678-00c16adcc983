package com.cdz360.biz.oa.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Deprecated(since = "20230310")
@ApiModel(value = "任务处理附言", description = "包路径谨慎调整，调整影响附言反射")
@Data
@Accessors(chain = true)
public class OldOaTaskComment {

    @ApiModelProperty(value = "操作", notes = "comment,submit,reject,pass")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String op;

    @Schema(description = "附言信息")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String note;

    @Schema(description = "附件链接列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> attachmentList;

    public OldOaTaskComment submitterOp(boolean approve) {
        this.op = approve ? "submit" : "end";
        return this;
    }

    public OldOaTaskComment rechargeOp(boolean approve) {
        this.op = approve ? "pass" : "reject";
        return this;
    }
}
