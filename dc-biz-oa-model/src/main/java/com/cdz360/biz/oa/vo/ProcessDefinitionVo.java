package com.cdz360.biz.oa.vo;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ProcessDefinitionVo {

    private String id;
    private String key;
    private int version;
    private String name;
    private String description;
    private String tenantId;
    private String deploymentId;
    private String category;
    private boolean graphicalNotationDefined;
    private boolean suspended;
    private boolean startFormDefined;

}
