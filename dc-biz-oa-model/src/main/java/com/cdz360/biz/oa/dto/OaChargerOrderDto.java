package com.cdz360.biz.oa.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "OA充电订单概要信息")
public class OaChargerOrderDto {

    @Schema(description = "充值单号")
    @JsonInclude(Include.NON_EMPTY)
    private String orderNo;

    @Schema(description = "充值实际金额, 单位'元', 2位小数")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal amount;

    @Schema(description = "充值赠送金额, 单位'元', 2位小数")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal freeAmount;

    @Schema(description = "可开票金额金额, 单位'元', 2位小数")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal invoiceAmount;

    @Schema(description = "支付时间", hidden = true)
    @JsonInclude(Include.NON_NULL)
    private Date payTime;

    @Schema(description = "充值申请流程ID")
    @JsonInclude(Include.NON_EMPTY)
    private String procInstId;
}
