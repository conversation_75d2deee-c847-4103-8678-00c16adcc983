package com.cdz360.biz.oa.param;

import com.cdz360.biz.model.FileItem;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "加签请求参数")
@Data
@Accessors(chain = true)
public class AddSignTaskParam {

    @Schema(description = "加签任务ID")
    @JsonInclude(Include.NON_EMPTY)
    private String taskId;

    @Schema(description = "加签用户ID列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> signUidList;

    @Schema(description = "操作人ID")
    @JsonInclude(Include.NON_NULL)
    private String opUid;

    @Schema(description = "备注")
    @JsonInclude(Include.NON_EMPTY)
    private String note;

    @Schema(description = "附件链接列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<FileItem> attachmentList;
}
