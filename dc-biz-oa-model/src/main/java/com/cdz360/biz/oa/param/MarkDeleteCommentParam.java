package com.cdz360.biz.oa.param;

import com.cdz360.biz.model.FileItem;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@ApiModel(value = "审批流留言标记删除")
@Data
@Accessors(chain = true)
public class MarkDeleteCommentParam {

    @Schema(description = "流程实例ID")
    @JsonInclude(Include.NON_EMPTY)
    private String procInstId;

    @Schema(description = "留言ID")
    @JsonInclude(Include.NON_EMPTY)
    private String commentId;

    @Schema(description = "标记删除文件列表")
    @JsonInclude(Include.NON_NULL)
    private FileItem file;

    @ApiModelProperty(value = "当前操作用户ID", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long oUid;

    @Schema(description = "标记: 无标记(null/0);删除(1)")
    @JsonInclude(Include.NON_NULL)
    private Integer flag;

}
