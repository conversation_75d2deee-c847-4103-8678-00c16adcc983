package com.cdz360.biz.oa.param;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.FileItem;
import com.cdz360.biz.oa.dto.BatchTaskVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.util.List;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BatchApproveParam {

    private List<BatchTaskVo> taskList;

    private Long uid;

    @Schema(description = "审批结果 true, 通过; false, 拒回", requiredMode = RequiredMode.REQUIRED)
    private Boolean approve;

    private String note;

    @Schema(description = "附件链接列表")
    private List<FileItem> attachmentList;

    @Override
    public String toString() {
        return JsonUtils.toJsonTimeString(this);
    }

}
