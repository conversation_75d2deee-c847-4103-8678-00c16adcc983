package com.cdz360.biz.oa.dto;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.invoice.type.ProductType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.ZonedDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.ZonedDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * InvoicedTemplateSalDetailDTO
 *
 * @since 3/14/2023 4:45 PM
 * <AUTHOR>
 */
@Schema(description = "商品行信息传输信息")
@Data
@Accessors(chain = true)
public class InvoicedTemplateSalDetailDTO implements Serializable {
    @Schema(description = "主键")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String code;

    @Schema(description = "模板Id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long refId;

    @Deprecated(since = "20200728")
    @Schema(description = "发票类型", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String invoiceType;

    @Schema(description = "分类编码 对应 Spbmbbh 的商品和服务税收分类编码")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String productCode;

    @Schema(description = "商品或服务名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String productName;

    @Schema(description = "商品类别: ACTUAL_FEE -- 实际金额消费;" +
        "SERV_ACTUAL_FEE -- 实际金额服务费; ELEC_ACTUAL_FEE -- 实际金额电费;PARK_OUT_FEE -- 超时停车费")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ProductType productType;

    @Schema(description = "商品版本号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String version;

    @Schema(description = "用户平台编码")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String venderOwnCode;

    @Schema(description = "商品规格型号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String spec;

    @Schema(description = "商品单位")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String unit;

    @Schema(description = "商品税率 16,6表示16%，6%")
    private Integer taxRate;

    @Schema(description = "优惠政策标识")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String policyMark;

    @Schema(description = "计费比例")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer shareReate;

    @Schema(description = "创建者")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String creatorName;

    @Schema(description = "创建时间")
    @JsonIgnore
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonSerialize(using = ZonedDateTimeSerializer.class)
    private ZonedDateTime createDate;

    @Deprecated(since = "20200728")
    @Schema(description = "商户ID", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long commercialId;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}