package com.cdz360.biz.oa.dto;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@JsonInclude(Include.NON_NULL)
public class BatchTaskVo {

    private String procInstId;

    @Schema(description = "当前任务ID(流程实例结束时可以为空)", requiredMode = RequiredMode.NOT_REQUIRED)
    private String taskId;

    private String siteId;

    @Schema(description = "账期开始日期String")
    @JsonInclude(Include.NON_EMPTY)
    private String billStartDate;

    @Schema(description = "账期结束日期String")
    @JsonInclude(Include.NON_EMPTY)
    private String billEndDate;

    @Schema(description = "账期最大值")
    private LocalDate maxBillDate;

    @Override
    public String toString() {
        return JsonUtils.toJsonTimeString(this);
    }

}
