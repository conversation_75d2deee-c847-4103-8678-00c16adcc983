package com.cdz360.biz.oa.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * OaSettJobBillPdfDto
 *
 * @since 5/30/2023 5:10 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class OaSettJobBillPdfDto {
    @Schema(description = "结算单是否存在差异")
    @JsonInclude(Include.NON_NULL)
    private Boolean difference;

    @Schema(description = "差异说明")
    @JsonInclude(Include.NON_NULL)
    private String differenceRemark;

    @Schema(description = "结算单信息列表")
    @JsonInclude(Include.NON_NULL)
    private List<OaSettJobBillExDto> settJobBillList;
}