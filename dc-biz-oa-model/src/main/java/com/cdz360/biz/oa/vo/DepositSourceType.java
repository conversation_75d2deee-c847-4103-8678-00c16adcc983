package com.cdz360.biz.oa.vo;

import com.cdz360.base.model.base.type.DcEnum;
import lombok.Getter;

/**
 * DepositSourceType
 *
 * @since 3/15/2023 3:03 PM
 * <AUTHOR>
 */
@Getter
public enum DepositSourceType implements DcEnum {
    UNKNOWN(0, "其他"),

    WX_LITE(2, "微信小程序"),

    ALIPAY_LITE(4, "支付宝小程序"),

    ANDROID_APP(6, "安卓APP"),

    IOS_APP(8, "iOS APP"),

    CHARGE_H5(10, "充电H5"),

    // 客户自己把钱打到公司账户后, 公司运营人员手动为客户账户进行充值
    MGM_WEB(20, "平台充值(后台操作)"),
    MGM_WEB_APPLICATION(100, "平台充值(申请审核)"),

    // 企业客户通过平台客户端进行充值
    MERCHANT(24, "企业充值"),

    HLHT(50, "三方平台"),

    GD_LITE(25, "高德小程序"),
    ;

    private final int code;
    private final String desc;

    DepositSourceType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static DepositSourceType valueOf(int code) {
        for (DepositSourceType status : values()) {
            if (status.code == code) {
                return status;
            }
        }
        return DepositSourceType.UNKNOWN;
    }


}
