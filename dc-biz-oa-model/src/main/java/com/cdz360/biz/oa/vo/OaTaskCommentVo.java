package com.cdz360.biz.oa.vo;

import com.cdz360.biz.model.FileItem;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@ApiModel(value = "任务附言信息")
@Data
@Accessors(chain = true)
public class OaTaskCommentVo {

    @Schema(description = "留言记录ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String id;

    @Schema(description = "操作人ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String oUid;

    @Schema(description = "操作人姓名")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String oName;

    @Schema(description = "操作 submit,reject,pass")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String op;

    @Schema(description = "附言信息")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String note;

    @Schema(description = "附件链接列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<FileItem> attachmentList;

    @Schema(description = "提交时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date time;

//    @Schema(description = "操作人姓名,导出pdf使用")
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String operateName;
}
