package com.cdz360.biz.oa.param;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.oa.param.PrepaidInvoicingEditParam;
import com.cdz360.biz.model.oa.param.PrepaidInvoicingParam;
import java.io.Serializable;
import java.util.Map;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.cglib.beans.BeanMap;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class PrepaidInvoiceProcessParam extends OaProcessBaseParam implements Serializable {

    public PrepaidInvoiceProcessParam() {
    }

    public PrepaidInvoiceProcessParam(Long oUid, String oName, String oPhone, Long topCommId,
        String tenantIdChain, PrepaidInvoicingParam param) {
        super(oUid, oName, oPhone, topCommId, tenantIdChain);

        param.setOpName(null); // 忽略此参数
        param.setInvoiceWay(null); // 忽略此参数
        param.setOrderNoList(null); // 忽略此参数
        Map<String, Object> beanMap = BeanMap.create(param);
        this.formVariables = super.formatDate(beanMap);
    }

    public PrepaidInvoiceProcessParam(Long oUid, String oName, String oPhone, Long topCommId,
        String tenantIdChain, PrepaidInvoicingEditParam param) {
        super(oUid, oName, oPhone, topCommId, tenantIdChain);

        param.setOpName(null); // 忽略此参数
        param.setInvoiceWay(null); // 忽略此参数
        param.setOrderNoList(null); // 忽略此参数
        param.setProcInstId(null); // 忽略此参数
        Map<String, Object> beanMap = BeanMap.create(param);
        this.formVariables = super.formatDate(beanMap);
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}