package com.cdz360.biz.oa.vo;

import com.cdz360.biz.model.cus.balance.vo.BalanceApplicationVo;
import com.cdz360.biz.oa.dto.OaRechargeInfoDto;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.Map;

@ApiModel(value = "充值申请详情")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class OaRechargeInfoVo extends OaRechargeInfoDto {

    @Schema(description = "页面按钮控制")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BtnType btnType;

    private BalanceApplicationVo balanceApplicationVo;

    private Map chargeInfo;

    public void initBtnType(Long sysUid) {
        if (null != getEndTime()) {
            this.btnType = BtnType.OTHER;
        } else {
            this.btnType = sysUid.toString().equals(getAssignee()) ? BtnType.AUDIT : BtnType.COMMENT;
        }
    }

    public static enum BtnType {
        OTHER("其他"),
        COMMENT("留言"),
        AUDIT("审核"),
        ;

        private String desc;

        BtnType(String desc) {
            this.desc = desc;
        }
    }
}
