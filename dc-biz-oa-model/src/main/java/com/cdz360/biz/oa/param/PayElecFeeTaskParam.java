package com.cdz360.biz.oa.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.biz.oa.param.ListTaskParam.AuditStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class PayElecFeeTaskParam extends BaseListParam {

    @ApiModelProperty(name = "所属顶级商户id", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    protected Long topCommId;

    @ApiModelProperty(value = "当前操作用户ID", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long oUid;

    private List<String> procInstIdList;

    @ApiModelProperty(value = "审核状态")
    private AuditStatus auditStatus;

}
