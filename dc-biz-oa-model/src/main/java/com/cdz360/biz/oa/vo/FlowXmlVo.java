package com.cdz360.biz.oa.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@ApiModel(value = "流程Xml")
@Data
@Accessors(chain = true)
public class FlowXmlVo {

    @ApiModelProperty(value = "流程名称", required = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String name;

    @ApiModelProperty(value = "流程分类", required = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String category;

    @ApiModelProperty(value = "xml 文件", required = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String xml;
}
