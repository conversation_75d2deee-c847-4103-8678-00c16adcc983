package com.cdz360.biz.oa.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@ApiModel(value = "启动流程参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CreateProcessInstanceDto extends CompleteFormDto {

    @ApiModelProperty(value = "流程定义ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String processDefinitionId;

    @ApiModelProperty(value = "")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String name;
}
