package com.cdz360.biz.oa.dto;

import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.biz.model.invoice.dto.OaInvoiceBuyerInfo;
import com.cdz360.biz.model.invoice.dto.OaInvoiceSellerInfo;
import com.cdz360.biz.model.invoice.param.InvoiceRecordParam;
import com.cdz360.biz.model.invoice.type.InvoiceType;
import com.cdz360.biz.model.oa.dto.OaContract;
import com.cdz360.biz.model.oa.type.OaHandleNode;
import com.cdz360.biz.model.oa.vo.InvoiceCompareVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "收入-场站分成OA表单信息")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class IncomeSiteFormDto extends OaStartFormBase {

    @Schema(description = "场站ID")
    @JsonInclude(Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "场站ID列表", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_EMPTY)
    private List<String> siteIdList;

    @Schema(description = "场站名称")
    @JsonInclude(Include.NON_EMPTY)
    private String siteName;

    @Schema(description = "场站名称列表")
    @JsonInclude(Include.NON_EMPTY)
    private List<String> siteNameList;

//    @Schema(description = "场站编号")
//    @JsonInclude(Include.NON_EMPTY)
//    private String siteNo;

    @Schema(description = "场站备注（只取第一个场站）")
    @JsonInclude(Include.NON_EMPTY)
    private String siteRemark;

    // ========= 结算单信息 ==============
    @Schema(description = "结算单信息")
    @JsonInclude(Include.NON_NULL)
    private OaSettJobBillInfo settBillSummary;

    @Schema(description = "结算单差异说明")
    @JsonInclude(Include.NON_EMPTY)
    private String differenceRemark;
    // ========= 结算单信息 ==============

    @Schema(description = "账期日期")
    @JsonInclude(Include.NON_NULL)
    private TimeFilter billDateRange;

    @Schema(description = "经办节点")
    @JsonInclude(Include.NON_NULL)
    private List<OaHandleNode> handleNodes;

    // ========= 用印相关信息 =========
    @Schema(description = "印章公司名称")
    @JsonInclude(Include.NON_EMPTY)
    private String signetCompany;

    @Schema(description = "印章类型")
    @JsonInclude(Include.NON_EMPTY)
    private String signetType;

    @Schema(description = "其他印章说明")
    @JsonInclude(Include.NON_EMPTY)
    private String signetDesc;
    // ========= 用印相关信息 =========

    // ========= 开票相关信息 =========
    @Schema(description = "开票种类")
    @JsonInclude(Include.NON_NULL)
    private InvoiceType invoiceType;

    @Schema(description = "(购方)发票抬头")
    @JsonInclude(Include.NON_NULL)
    private OaInvoiceBuyerInfo invoiceBuyerInfo;

    @Schema(description = "开票主体(销方)")
    private OaInvoiceSellerInfo invoiceSellerInfo; // 记录


    @Schema(description = "发票信息,发票可能会有多张")
    @JsonInclude(Include.NON_NULL)
    private List<InvoiceRecordParam> invoiceRecords;

    @Schema(description = "数据对比信息")
    @JsonInclude(Include.NON_NULL)
    private List<InvoiceCompareVo> actualData;

    @Deprecated
    @Schema(description = "发票备注")
    @JsonInclude(Include.NON_EMPTY)
    private String invoiceRemark;

    @Schema(description = "回款状态(true-已回款;false-未回款)")
    @JsonInclude(Include.NON_NULL)
    private Boolean paymentPlanStatus;

    @Schema(description = "回款计划")
    @JsonInclude(Include.NON_NULL)
    private List<PaymentPlan> paymentPlans;
    // ========= 开票相关信息 =========

    // ========= 合约 =========
    @Schema(description = "流程关联合约")
    @JsonInclude(Include.NON_NULL)
    private List<OaContract> contracts;
    // ========= 合约 =========
}
