plugins {
    id 'java'
    id "com.gorylenko.gradle-git-properties"
}

apply plugin: 'org.springframework.boot'

sourceSets {
    main {
        resources {
            srcDirs "src/main/resources", "src/main/java"
        }
    }
}
springBoot {
    buildInfo()
}

dependencies {

    //implementation project(':auth-config')
    //implementation project(':auth-core')
    implementation project(':dc-biz-common-model')
    implementation project(':dc-biz-auth-model')
    implementation project(':dc-biz-user-model')
    implementation project(':dc-biz-oa-model')
    implementation project(':dc-biz-trading-model')
    implementation("com.cdz360.cloud:dc-base-ds:${dcCloudVersion}")
    implementation("com.cdz360.cloud:dc-data-sync:${dcCloudVersion}")
    implementation("com.cdz360.cloud:dc-data-sync-publisher:${dcCloudVersion}")
    implementation("com.cdz360.cloud:dc-data-cache-reader:${dcCloudVersion}")
    implementation("com.cdz360.cloud:dc-data-cache-writer:${dcCloudVersion}")
    //implementation project(':chargerlink-dev-ops')

    implementation('org.springframework.boot:spring-boot-starter-actuator')
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation('org.springframework.boot:spring-boot-starter-aop')
    implementation('org.springframework.boot:spring-boot-starter-data-redis')
    implementation('org.springframework.cloud:spring-cloud-starter-config')
    implementation('org.springframework.cloud:spring-cloud-starter-netflix-eureka-client')

    implementation('org.springframework.cloud:spring-cloud-starter-openfeign')
    implementation('org.springframework.cloud:spring-cloud-bus')
    implementation('org.springframework.cloud:spring-cloud-starter-bus-amqp')
//    implementation('org.springframework.cloud:spring-cloud-starter-sleuth')
//    implementation('org.springframework.cloud:spring-cloud-sleuth-zipkin')
    implementation 'io.micrometer:micrometer-tracing-bridge-brave'
    implementation 'io.zipkin.reporter2:zipkin-reporter-brave'
    implementation 'io.github.openfeign:feign-micrometer'
//    implementation("org.springframework.cloud:spring-cloud-starter-netflix-zuul")
//    implementation("com.netflix.zuul:zuul-core")
//    implementation "io.springfox:springfox-boot-starter:${swaggerVersion}"
    implementation("org.springdoc:springdoc-openapi-ui:${springdocVersion}")
    implementation("org.springdoc:springdoc-openapi-webflux-ui:${springdocVersion}")


    implementation("com.playtika.reactivefeign:feign-reactor-spring-cloud-starter:${feignReactiveVersion}")
    implementation("com.playtika.reactivefeign:feign-reactor-webclient:${feignReactiveVersion}")
    implementation("com.playtika.reactivefeign:feign-reactor-cloud:${feignReactiveVersion}")
    implementation("com.playtika.reactivefeign:feign-reactor-spring-configuration:${feignReactiveVersion}")

    implementation("com.google.guava:guava:${guavaVersion}")
    implementation("org.apache.commons:commons-pool2:${commonsPoolVersion}")

    implementation("com.baomidou:mybatis-plus-boot-starter:${mybatisPlusVersion}")


    implementation("com.github.pagehelper:pagehelper:${pagehelperVersion}")
//    implementation("com.github.pagehelper:pagehelper-spring-boot-starter:${pageHelperStarterVersion}") // 注释此行，否则Page<T>.getTotal()无效

    implementation('org.springframework.boot:spring-boot-starter-data-redis')

    // logstash间接依赖于jaxb, 在java10+以上的环境, 缺少jaxb-api时, logstash无法正常启动
    implementation("org.glassfish.jaxb:jaxb-runtime")

    implementation("mysql:mysql-connector-java:${mysqlConnectorVersion}")


    implementation("javax.servlet:javax.servlet-api:4.0.1")     // shiro 需要 javax.servlet.xxx package


    implementation("org.apache.shiro:shiro-core:${shiroVersion}")
//    implementation("org.apache.shiro:shiro-crypto-hash:${shiroVersion}")
    implementation("org.apache.shiro:shiro-spring:${shiroVersion}")
    implementation("org.apache.shiro:shiro-jakarta-ee:${shiroVersion}")
    implementation("org.apache.shiro:shiro-ehcache:${shiroVersion}")

    implementation("org.jooq:jool-java-8:0.9.14")



}
