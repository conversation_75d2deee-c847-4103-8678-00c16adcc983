#spring:
#  profiles:
#    active: ${package.environment}
server:
  port: 9003
spring:
  application:
    name: dc-biz-auth-dev
  config:
    import: "configserver:http://oam-test.iot.renwochong.com"
  profiles:
    active: test01,common,jdbc-auth,redis,rabbitmq,zipkin
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    config:
      label: test01
  devtools:
    restart:
      enabled: false
  jackson:
    default-property-inclusion: non_null
    time-zone: 'GMT+8'
    date-format: yyyy-MM-dd HH:mm:ss

#mybatis-plus:
#  type-aliases-package: com.cdz360.biz.auth.dao


eureka:
  client:
    register-with-eureka: false
    service-url:
      defaultZone: http://aaa:<EMAIL>:7001/eureka/
      #http://aaa:<EMAIL>/eureka/
  instance:
    prefer-ip-address: true

logging:
  level:
    com.cdz360.biz: 'DEBUG'
    com.chargerlinkcar.core: 'DEBUG'
    org.springframework: 'INFO'
    org.springframework.cloud: 'DEBUG'
    org.springframework.cloud.config: 'DEBUG'
    org.springframework.cloud.netflix: 'DEBUG'
    feign: 'DEBUG'

  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} - %logger{36}.%M\\(%line\\) - %msg%n"
#logging:
#  file: info.log
#  config: classpath:log4j2-spring-local.xml

authc:
  smsUrl: XXXX
  smsApiKey: XXXX
  company:
  codeName: CODE
  templateNo: 2704640
  idcodeExpire: 1800
  idcodeRetryInterval: 60
  cookieDomain: 'mamcharge.com'
  cookieSecure: false



ribbon:
  ConnectTimeout: 200 # 连接超时时间(ms)
  ReadTimeout: 6000 # 通信超时时间(ms)
  OkToRetryOnAllOperations: true # 是否对所有操作重试
  MaxAutoRetriesNextServer: 2 # 同一服务不同实例的重试次数
  MaxAutoRetries: 1 # 同一实例的重试次数

hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 60000


springdoc:
  packagesToScan: com.cdz360.biz.auth.rest
  swagger-ui:
    path: /swagger-ui.html


lock:
  over-time: 1800000
  error-num: 500
authCenter:
  autoMap: true
area:
  province-service:
  regin-service: