package com.cdz360.biz.auth.ds.rw.user.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SiteGroupUserRwMapper {

    int batchInsert(@Param("uid") Long uid, @Param("gidList") List<String> gidList);

    int batchDelete(@Param("uid") Long uid,
        @Param("exGidList") List<String> exGidList);

    int deleteByUid(@Param("uid") long uid);

    int deleteByGid(@Param("gid") String gid);
}
