package com.cdz360.biz.auth.ds.rw.sys;

import com.cdz360.biz.auth.ds.rw.sys.mapper.SysRoleRwMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysRoleRwDs {
    @Autowired
    private SysRoleRwMapper sysRoleRwMapper;

    public int  batchUpdate ( String commIds, Long updateBy, List<Long> roleIdList){
        return sysRoleRwMapper.batchUpdate(commIds,updateBy,roleIdList);
    }
}
