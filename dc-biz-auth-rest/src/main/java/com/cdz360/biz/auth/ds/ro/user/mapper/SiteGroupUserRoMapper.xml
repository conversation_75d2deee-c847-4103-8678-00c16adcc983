<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.ds.ro.user.mapper.SiteGroupUserRoMapper">

  <resultMap id="RESULT_SITEGROUP_VO" type="com.cdz360.biz.model.sys.vo.SiteGroupVo">
    <result column="gid" jdbcType="VARCHAR" property="gid"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="adminUid" jdbcType="BIGINT" property="adminUid"/>
    <result column="type" typeHandler="com.cdz360.ds.type.EnumTypeHandler" property="type"/>
    <result column="ownType" typeHandler="com.cdz360.ds.type.EnumTypeHandler" property="ownType"/>
    <result column="enable" property="enable"/>
    <result column="adminName" jdbcType="VARCHAR" property="adminName"/>
    <result column="gNameStr" jdbcType="VARCHAR" property="gNameStr"/>
  </resultMap>

  <resultMap id="RESULT_CORP_GROUP_VO" type="com.cdz360.biz.model.sys.vo.CorpGroupTinyVo">
    <result column="id" jdbcType="BIGINT" property="corpId"/>
    <collection property="gids" ofType="java.lang.String" javaType="list">
      <result column="gid" property="value"/>
    </collection>
  </resultMap>

  <select id="getGidListByUid" resultType="java.lang.String">
    select distinct(ug.gid)
    from t_site_group_user_ref as ug
    left join t_site_group sg on sg.gid = ug.gid
    where ug.uid = #{uid}
    and sg.enable = true
  </select>

  <select id="getGidListByUidAndType" resultType="java.lang.String">
    select distinct(ug.gid)
    from t_site_group_user_ref as ug
    left join t_site_group sg on sg.gid = ug.gid
    where ug.uid = #{uid}
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( types )">
      and sg.type in
      <foreach collection="types" index="index" item="item" open="(" close=")"
               separator=",">
        #{item}
      </foreach>
    </if>
    and sg.enable = true
  </select>

  <select id="getSiteGroupsByUid" resultMap="RESULT_SITEGROUP_VO">
    select sg.*,
    adm.name as adminName
    from t_site_group_user_ref as ug
    left join t_site_group sg on sg.gid = ug.gid
    left join sys_user adm on adm.id = sg.adminUid
    where ug.uid = #{uid}
    and sg.enable = true
    <if test="type != null">
      and sg.type = #{type}
    </if>
    group by sg.gid
  </select>
  <select id="getUidsByGids" resultType="com.cdz360.biz.auth.user.vo.SiteGroupUserRefVo">
    select
    ref.*,
    user.corpWxUid as corpWxUid
    from t_site_group_user_ref ref
    left join sys_user user on user.id = ref.uid
    <where>
      ref.gid in
      <foreach collection="gids" item="item" index="index" separator="," open="(" close=")">
        #{item}
      </foreach>
    </where>
  </select>
  <select id="getSiteGroupsList" resultMap="RESULT_SITEGROUP_VO">
    SELECT
    any_value(tsg.uid) as adminUid,
    any_value(su.name) as adminName,
    group_concat(ts.`name`) as gNameStr
    FROM
    t_site_group_user_ref tsg
    LEFT JOIN t_site_group ts ON tsg.gid = ts.gid
    LEFT JOIN sys_user su ON tsg.uid = su.id
    LEFT JOIN t_commercial tc ON tc.id = su.commId
    WHERE
    su.`status` = 1
    and ts.enable = 1
    <!--        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( idChain )">-->
    <!--            and tc.idChain like CONCAT(#{idChain}, '%')-->
    <!--        </if>-->
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( gidList )">
      and tsg.`gid` in
      <foreach collection="gidList" index="index" item="item" open="(" close=")"
               separator=",">
        #{item}
      </foreach>
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( types )">
      and ts.type in
      <foreach collection="types" index="index" item="item" open="(" close=")"
               separator=",">
        #{item}
      </foreach>
    </if>
    GROUP BY
    tsg.uid
  </select>
  <select id="findYwUser"
    parameterType="com.cdz360.biz.auth.sys.param.YwUserParam"
    resultType="com.cdz360.biz.auth.model.vo.SysUser">
    select su.id, su.topCommId, su.commId, su.name
    from t_site_group_user_ref sgu
    left join t_site_group sg on sg.gid = sgu.gid
    left join sys_user su on su.id = sgu.uid
    where sg.type = 3
    <if test="null != topCommId">
      and su.topCommId = #{topCommId}
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( gidList )">
      <foreach collection="gidList" index="index" item="item" open="and sg.gid in (" close=")"
        separator=",">
        #{item}
      </foreach>
    </if>
    <choose>
      <when test="null == linkOpGroup">
      </when>
      <when test="linkOpGroup">
        and sg.gid in (select gid from t_site_group_user_ref where uid = #{uid})
        and sgu.uid != #{uid}
      </when>
      <otherwise>
        and sg.gid not in (select gid from t_site_group_user_ref where uid = #{uid})
      </otherwise>
    </choose>
    group by sgu.uid
  </select>
  <select id="getYwGroupOtherUser" resultType="com.cdz360.biz.auth.model.vo.SysUser">
    select su.id, su.topCommId, su.commId, su.name
    from t_site_group_user_ref sgu
    left join t_site_group sg on sg.gid = sgu.gid
    left join sys_user su on su.id = sgu.uid
    where sg.type = 3
    <choose>
      <when test="null != same and same">
        and sg.gid in (select gid from t_site_group_user_ref where uid = #{uid})
        and sgu.uid != #{uid}
      </when>
      <otherwise>
        and sg.gid not in (select gid from t_site_group_user_ref where uid = #{uid})
      </otherwise>
    </choose>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( gidList )">
      <foreach collection="gidList" index="index" item="item" open="and sg.gid in (" close=")"
        separator=",">
        #{item}
      </foreach>
    </if>
    group by sgu.uid
  </select>
  <select id="getUserBySiteGroupGid" resultType="com.cdz360.biz.model.cus.vo.SysUserVo">
    select su.id, su.topCommId, su.commId, su.name
    from t_site_group_user_ref sgu
    left join sys_user su on su.id = sgu.uid
    where sgu.gid = #{gid}
  </select>

  <select id="getCorpGroups" resultMap="RESULT_CORP_GROUP_VO">
    select
      c.id,
      r.gid
    from
      t_corp c
    left join sys_user u on
      c.sysUid = u.id
    left join t_site_group_user_ref r on
      u.id = r.uid
    where
      c.id in
    <foreach collection="corpIdList" index="index" item="corpId" open="("
        separator="," close=")">
      #{corpId}
    </foreach>
  </select>

  <select id="getByGidList" resultType="com.cdz360.biz.model.cus.vo.SysUserVo">
    select
      distinct su.id,
      su.topCommId,
      su.commId,
      su.name,
      su.username,
      sg.name as groupName
    from t_site_group_user_ref sgu
    left join t_site_group sg on sg.gid = sgu.gid
    left join sys_user su on su.id = sgu.uid
    where
    sg.enable = true
    and su.status = 1
    and sgu.gid in
    <foreach collection="gidList" index="index" item="item" open="("
      separator="," close=")">
      #{item}
    </foreach>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(typeList)">
      and sg.`type` in
      <foreach collection="typeList" item="item" separator="," open="(" close=")">
        #{item.code}
      </foreach>
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(exUidList)">
      and su.id not in
      <foreach collection="exUidList" item="item" separator="," open="(" close=")">
        #{item}
      </foreach>
    </if>
  </select>

</mapper>