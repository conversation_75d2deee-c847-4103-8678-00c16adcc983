package com.cdz360.biz.auth.ds.ro.user;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.auth.ds.ro.user.mapper.SiteGroupUserRoMapper;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.sys.param.YwUserParam;
import com.cdz360.biz.auth.user.vo.SiteGroupUserRefVo;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.sys.param.ListSiteGroupParam;
import com.cdz360.biz.model.sys.vo.CorpGroupTinyVo;
import com.cdz360.biz.model.sys.vo.SiteGroupVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SiteGroupUserRoDs {

    @Autowired
    private SiteGroupUserRoMapper siteGroupUserRoMapper;

    /**
     * 使用 sys_user.id获取该用户关联的组的gid
     */
    public List<String> getGidListByUid(Long uid) {
        if (uid == null || uid < 1L) {
            throw new DcArgumentException("参数错误,uid不能为空");
        }
        return siteGroupUserRoMapper.getGidListByUid(uid);
    }

    /**
     * 使用 sys_user.id 和 Type 获取该用户关联的组的gid
     */
    public List<String> getGidListByUidAndType(Long uid, List<Integer> types) {
        if (uid == null || uid < 1L) {
            throw new DcArgumentException("参数错误,uid不能为空");
        }
        return siteGroupUserRoMapper.getGidListByUidAndType(uid, types);
    }

    /**
     * 使用 sys_user.id获取该用户关联的场站组信息
     */
    public List<SiteGroupVo> getSiteGroupsByUid(Long uid, Integer type) {
        if (uid == null || uid < 1L) {
            throw new DcArgumentException("参数错误,uid不能为空");
        }
        return siteGroupUserRoMapper.getSiteGroupsByUid(uid, type);
    }

    public List<SiteGroupVo> getSiteGroupsList(String idChain, List<String> gidList, List<Integer> types) {
        return siteGroupUserRoMapper.getSiteGroupsList(idChain, gidList, types);
    }

    public List<SysUser> findYwUser(YwUserParam param) {
        return siteGroupUserRoMapper.findYwUser(param);
    }

    public List<SysUser> getYwGroupOtherUser(Long uid, Boolean same, List<String> gidList) {
        return siteGroupUserRoMapper.getYwGroupOtherUser(uid, same, gidList);
    }

    public List<SiteGroupUserRefVo> getUidsByGids(List<String> gids) {
        return siteGroupUserRoMapper.getUidsByGids(gids);
    }

    public List<SysUserVo> getUserBySiteGroupGid(String gid) {
        return siteGroupUserRoMapper.getUserBySiteGroupGid(gid);
    }

    public List<SysUserVo> getByGidList(ListSiteGroupParam param) {
        return siteGroupUserRoMapper.getByGidList(param);
    }

    public List<CorpGroupTinyVo> getCorpGroups(@NonNull List<Long> corpIdList) {
        if (CollectionUtils.isEmpty(corpIdList)) {
            return List.of();
        }
        return siteGroupUserRoMapper.getCorpGroups(corpIdList);
    }

}
