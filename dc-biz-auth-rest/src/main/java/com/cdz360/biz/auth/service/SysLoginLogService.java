package com.cdz360.biz.auth.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cdz360.biz.auth.dao.SysLoginLogDao;
import com.cdz360.biz.auth.dao.SysUserDao;
import com.cdz360.biz.auth.model.constant.GlobalConst;
import com.cdz360.biz.auth.model.vo.SysLoginLog;
import com.cdz360.biz.auth.model.vo.SysUser;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
@Slf4j
public class SysLoginLogService extends ServiceImpl<SysLoginLogDao, SysLoginLog> {
    @Autowired
    private SysUserDao sysUserDao;
    @Value("${lock.over-time}")
    private Long lockOverTime;
    @Value("${lock.error-num}")
    private Integer errorNum;

//    public boolean insertErrorLog(SysUser su) {
//        SysLoginLog log = new SysLoginLog();
//        log.setErrorNum(1);
//        log.setUserId(su.getId());
//        log.setLastLoginTime(new Date());
//        return insert(log);
//    }


    public boolean checkTimeOver(Date lastLoginDate, SysUser su) {
        boolean over = false;
        if (lockOverTime == null) {
            over = true;
        } else {
            over = (new Date().getTime() - lastLoginDate.getTime()) > lockOverTime;
        }
        if (over) {
            su.setLocked(GlobalConst.NORMAL);
            sysUserDao.updateById(su);
            super.getBaseMapper().deleteById(su.getId());
//            deleteById(su.getId());
        }
        return over;
    }
}
