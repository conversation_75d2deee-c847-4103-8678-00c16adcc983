package com.cdz360.biz.auth.rest;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.auth.model.constant.GlobalConst;
import com.cdz360.biz.auth.model.dto.OperateinfoDto;
import com.cdz360.biz.auth.model.vo.Rez;
import com.cdz360.biz.auth.model.vo.TOperateBaseInfo;
import com.cdz360.biz.auth.service.TOperateBaseInfoService;
import org.apache.commons.lang.StringUtils;
import org.bouncycastle.jcajce.provider.symmetric.SEED.Wrap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/data/operateinfo")
public class TOperateBaseInfoController //implements TOperateBaseInfoInterface 
{
    @Autowired
    TOperateBaseInfoService tOperateBaseInfoService;

    
    @PostMapping
    public Rez<TOperateBaseInfo> add(@RequestBody TOperateBaseInfo entity) {
        int insert = tOperateBaseInfoService.getBaseMapper().insert(entity);
        return Rez.hasModified(insert > 0, entity);
    }

    
    @PutMapping
    public Rez<TOperateBaseInfo> edit(@RequestBody TOperateBaseInfo entity) {
        boolean edit = tOperateBaseInfoService.updateById(entity);
        return Rez.hasModified(edit, entity);
    }

    
    @GetMapping("{id}")
    public Rez<TOperateBaseInfo> findById(@PathVariable Long id) {
        TOperateBaseInfo entity = tOperateBaseInfoService.getBaseMapper().selectById(id);
        return Rez.ofNullable(entity);
    }

    
    @DeleteMapping("{id}")
    public Rez delete(@PathVariable Long id) {
        TOperateBaseInfo entity = new TOperateBaseInfo();
        entity.setId(id);
        entity.setStatus(GlobalConst.FREEZE);
        boolean delete = tOperateBaseInfoService.updateById(entity);
        return Rez.hasModified(delete);
    }

    @RequestMapping("/page")
    public ListResponse<TOperateBaseInfo> page(@RequestBody OperateinfoDto pd) {
        QueryWrapper<TOperateBaseInfo> condition = Wrappers.query();
        condition.eq("status", GlobalConst.NORMAL);
        if (StringUtils.isNotEmpty(pd.getOperateName())) {
            condition.eq("operate_name", pd.getOperateName());
        }
        if (StringUtils.isNotEmpty(pd.getKeyword())) {
            condition.like("operate_name", pd.getKeyword());
        }
        Page<TOperateBaseInfo> page = tOperateBaseInfoService.getBaseMapper().selectPage(new Page<TOperateBaseInfo>(pd.getPage(), pd.getSize(), false), condition);
        ListResponse<TOperateBaseInfo> res = new ListResponse<>(page.getRecords(), page.getTotal());
        return res;
        //return Rez.data(page);
    }
}
