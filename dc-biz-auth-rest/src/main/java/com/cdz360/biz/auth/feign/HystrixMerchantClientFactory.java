package com.cdz360.biz.auth.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.user.vo.UserBalanceVo;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.message.po.MsgTemplatePO;
import com.cdz360.biz.model.cus.user.param.ModifyCusInfoParam;
import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class HystrixMerchantClientFactory implements FallbackFactory<CarUserFeignClient> {

    @Override
    public CarUserFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER, throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER, throwable.getStackTrace());

        return new CarUserFeignClient() {
            @Override
            public ObjectResponse<Long> createBlocUserByPhone(CorpPo corp) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<UserBalanceVo> findByUserId(Long userId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse updateCusInfo(ModifyCusInfoParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<MsgTemplatePO> getMsgTemplate(String key, Long topCommId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

//            @Override
//            public ObjectResponse<Long> getCorpCountByCorpName(String corpName) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

//            @Override
//            public ObjectResponse<Long> getCorpCountByAccount(String account) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }
        };
    }
}
