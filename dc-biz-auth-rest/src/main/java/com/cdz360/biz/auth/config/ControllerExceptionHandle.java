package com.cdz360.biz.auth.config;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.exception.DcException;
import com.cdz360.biz.auth.model.exception.NameConflictException;
import com.cdz360.biz.auth.model.exception.RestException;
import com.cdz360.biz.auth.model.type.ErrStatus;
import com.cdz360.biz.auth.model.vo.Rez;
import com.cdz360.biz.auth.utils.SqlExceptionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.validation.BindException;
import org.springframework.validation.Errors;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Optional;
import java.util.stream.Collectors;


@RestControllerAdvice("com.cdz360.biz.auth")
@Slf4j
public class ControllerExceptionHandle {
    @Autowired
    SqlExceptionUtils sqlExceptionUtils;

    @ExceptionHandler(BindException.class)
    public Rez<Object> bind(BindException e) {
        log.error("参数绑定异常", e);
        return Rez.error(ErrStatus.REQ_PARAM_NOT_VALID);
    }

    @ExceptionHandler(NameConflictException.class)
    public Rez nameConflict(NameConflictException e) {
        log.warn("名称重复!!", e);
        return Rez.error(ErrStatus.NAME_CONFLICT);
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
//    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Rez argEx(MethodArgumentNotValidException e) {
        Optional<String> err = Optional.ofNullable(e)
                .map(MethodArgumentNotValidException::getBindingResult)
                .map(Errors::getFieldErrors)
                .map(l -> l.stream().map(fieldError -> fieldError.getField() + ":" + fieldError.getDefaultMessage())
                        .collect(Collectors.joining(";")));
        log.warn("参数验证失败!!", e);
        return Rez.error(4003, err.orElse("参数验证失败" + e.getMessage()));
    }

    @ExceptionHandler(RestException.class)
    public Rez restEx(RestException ex) {
        log.error("内部http接口调用异常!!", ex);
        return Rez.error(5004, ex.getMessage());
    }

    @ExceptionHandler(DuplicateKeyException.class)
//    @ResponseStatus(HttpStatus.CONFLICT)
    public Rez ukEx(DuplicateKeyException ex) {
        log.warn("主键重复!!", ex);
        String exMsg = sqlExceptionUtils.getDuplcateKeyExMsg(ex);
        return Rez.error(4010, exMsg);
    }


    @ExceptionHandler(DataIntegrityViolationException.class)
//    @ResponseStatus(HttpStatus.CONFLICT)
    public Rez sqlErr(DataIntegrityViolationException ex) {
        log.error("数据库操作!!异常", ex);
        String message = ex.getMostSpecificCause().getMessage();
        return Rez.error(4010, message);
    }

    @ExceptionHandler({DcException.class})
    @ResponseBody
    public BaseResponse handleDcException(DcException ex) {
        log.warn(ex.getMessage(), ex);
        BaseResponse result = new BaseResponse();
        result.setStatus(ex.getStatus());
        result.setError(ex.getMessage());
        return result;
    }

    @ExceptionHandler(Exception.class)
    @ResponseStatus
    public Rez unknown(Exception ex) {
        log.error("未知异常. error = {}", ex.getMessage(), ex);
        return Rez.error(ErrStatus.UNKNOWN);
    }


}
