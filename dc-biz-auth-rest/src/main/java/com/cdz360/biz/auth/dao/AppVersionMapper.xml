<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cdz360.biz.auth.dao.AppVersionMapper">


    <resultMap id="appVersionPo" type="com.cdz360.biz.auth.sys.po.AppVersionPo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="topCommId" jdbcType="BIGINT" property="topCommId"/>
        <result column="appType" typeHandler="com.cdz360.ds.type.EnumTypeHandler" property="appType"/>
        <result column="minVer" jdbcType="BIGINT" property="minVer"/>
        <result column="curVer" jdbcType="BIGINT" property="curVer"/>
        <result column="pkgUrl" jdbcType="VARCHAR" property="pkgUrl"/>
        <result column="releaseNote" jdbcType="VARCHAR" property="releaseNote"/>
        <result column="enable" jdbcType="BOOLEAN" property="enable"/>
        <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>


    <select id="getAppVersion" resultMap="appVersionPo">
        select * from t_app_version
        where topCommId = #{topCommId} and appType = #{appType.code}
    </select>

</mapper>