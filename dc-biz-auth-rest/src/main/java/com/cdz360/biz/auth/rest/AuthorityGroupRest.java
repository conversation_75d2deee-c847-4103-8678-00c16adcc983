package com.cdz360.biz.auth.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.auth.model.param.AddAuthorityGroupRequest;
import com.cdz360.biz.auth.model.param.AddUserGroupRequest;
import com.cdz360.biz.auth.sys.vo.Authority;
import com.cdz360.biz.auth.model.vo.AuthorityGroup;
import com.cdz360.biz.auth.service.AuthorityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * AuthorityGroupRest
 *
 * @since 2020/2/11 16:16
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/api/authorityGroup")
public class AuthorityGroupRest {

    @Autowired
    AuthorityService authorityService;

    @PostMapping("/addGroup")
    public BaseResponse AddGroup(@RequestBody AddAuthorityGroupRequest addAuthorityGroupRequest) {
        return authorityService.addGroup(addAuthorityGroupRequest.getAuthorityGroup(),
            addAuthorityGroupRequest.getAuthorityList());
    }

    @GetMapping("/getGroupList")
    public ListResponse<AuthorityGroup> getGroups() {
        return new ListResponse<>(authorityService.getGroups());
    }

    @GetMapping("/getGroupById")
    public ObjectResponse<AuthorityGroup> getGroupById(@RequestParam("id") Long id) {
        return new ObjectResponse<>(authorityService.getGroupById(id));
    }

    @DeleteMapping("/deleteGroupById")
    public BaseResponse deleteGroupById(@RequestParam("id") Long id) {
        return authorityService.deleteGroupById(id);
    }

    @GetMapping("/getAuthorityList")
    public ListResponse<Authority> getAuthorityList(
        @RequestParam(value = "sysId", required = false) Long sysId) {
        return new ListResponse<>(authorityService.getAuthorityList(sysId));
    }

    @GetMapping("/getUserAuthoritiesByUid")
    public ListResponse<Authority> getUserAuthoritiesByUid(@RequestParam("id") Long uid) {
        return new ListResponse<>(authorityService.getAuthorityListByUid(uid));
    }

    @PostMapping("/modifyUserGroupRef")
    public BaseResponse modifyUserGroupRef(@RequestBody AddUserGroupRequest addUserGroupRequest) {
        log.info("新增用户组关系: {}", JsonUtils.toJsonString(addUserGroupRequest));
        int count = authorityService.modifyUserGroupRef(addUserGroupRequest);
        log.info(">> 新增 {}", count);
        return BaseResponse.newInstance();
    }
}
