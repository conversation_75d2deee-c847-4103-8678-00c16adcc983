package com.cdz360.biz.auth.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cdz360.biz.model.sys.constant.SmsOperatorType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

@Data
@TableName("t_commercial_manage")
public class TCommercialManage {
    private Long id;
    private Long comId;

    @TableField("appName")
    @Schema(description = "APP显示名称", example = "任我充")
    private String appName;

    @TableField("minChargeAmount")
    @Schema(description = "最小启动充电金额,单位'元'", example = "5.01")
    private BigDecimal minChargeAmount;

    @TableField("minPrepayAmount")
    @Schema(description = "即充即退的最小充值金额,单位'元'", example = "100.01")
    private BigDecimal minPrepayAmount;

    private String smsModelVerifyCodeNo;
    private String smsModelNationNo;
    private String smsModelAppNo;

    @TableField("smsOperatorType")
    @Schema(description = "短信运营商类型")
    private SmsOperatorType smsOperatorType;

    @Schema(description = "短信apikey(云片平台apikey或动动客平台UID)")
    private String smsApiKey;

    @TableField("smsApiPwd")
    @Schema(description = "短信平台用户密码(运营商为云片时不填，动动客时必填)")
    private String smsApiPwd;

    private String smsModelPersonCharge;
    private String smsModelMerchantCharge;
    private String smsModelCreditCharge;
    private String wxMsgApiKey;
    private String wxMsgTemplateRecharge;
    private String wxMsgTemplateRefund;
    private String wxMsgTemplateChargingEnd;//停止充电消息模板
    private String wxMsgTemplateChargingEndAbnormal;//异常订单消息模板
    private String wxMsgTemplateChargingStartAfter;
    private String wxMsgTemplateChargingStartPre;
    private String payApiKey;
    private String wxAppid;
    private String wxAppSecret;

    /**
     * 微信公众号appid
     */
    private String wechatAppid;
    /**
     * 微信公众号secret
     */
    private String wechatAppSecret;

    @TableField("alipayAppletAppId")
    @Schema(description = "支付宝小程序appid")
    private String alipayAppletAppId;

    @TableField("alipayPubKey")
    @Schema(description = "支付宝公钥")
    private String alipayPubKey;

    @TableField("alipayPrvKey")
    @Schema(description = "支付宝私钥")
    private String alipayPrvKey;

    // 支付宝验签升级: 证书相关参数
    @TableField("alipayAppletCertPath")
    @Schema(description = "支付宝小程序应用公钥证书路径")
    private String alipayAppletCertPath;

    @TableField("alipayPublicCertPath")
    @Schema(description = "支付宝公钥证书文件路径")
    private String alipayPublicCertPath;

    @TableField("alipayRootCertPath")
    @Schema(description = "支付宝CA根证书文件路径")
    private String alipayRootCertPath;
    // 支付宝验签升级: 证书相关参数

    /**
     * 友盟ios appId
     */
    @TableField("uPushIosAppId")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String uPushIosAppId;

    /**
     * 周边服务key
     */
    @TableField("wx_service_api_key")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String wxServiceApiKey;


    /**
     * 友盟ios appSecret
     */
    @TableField("uPushIosAppSecret")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String uPushIosAppSecret;

    /**
     * 友盟android appId
     */
    @TableField("uPushAndroidAppId")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String uPushAndroidAppId;


    /**
     * 友盟android appSecret
     */
    @TableField("uPushAndroidAppSecret")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String uPushAndroidAppSecret;

    private String wxGrantType;
    private String smsModelTimerChargeNo;

    @TableField("serviceTel")
    @Schema(description = "客服电话")
    private String serviceTel;
    @TableField("invoinceUrl")
    @Schema(description = "发票URL")
    private String invoinceUrl;

    @TableField("invoiceDesc")
    @Schema(description = "发票描述")
    private String invoiceDesc;

    @TableField(exist = false)
    @Schema(description = "发票功能启动开关")
    private Boolean invoinceEnabled;

    @TableField("wxLiteMgmAppId")
    @Schema(description = "桩管家appId")
    private String wxLiteMgmAppId;

    @TableField("wxLiteMgmSecret")
    @Schema(description = "桩管家secret")
    private String wxLiteMgmSecret;

}
