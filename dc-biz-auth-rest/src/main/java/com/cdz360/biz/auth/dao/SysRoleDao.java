package com.cdz360.biz.auth.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.auth.model.vo.SysRole;

import java.util.List;

public interface SysRoleDao extends BaseMapper<SysRole> {
    List<SysRole> findByUserId(Long userId);

    List<SysRole> getRoleByUserId(Long userId);

    CorpPo getCorpByUserId(Long userId);

    List<SysRole> findRoleHasSiteDetailAuth();

}
