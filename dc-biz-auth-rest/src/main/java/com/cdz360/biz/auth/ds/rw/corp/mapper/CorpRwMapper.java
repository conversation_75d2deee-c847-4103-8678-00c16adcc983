package com.cdz360.biz.auth.ds.rw.corp.mapper;

import com.cdz360.biz.auth.corp.po.BlocUser;
import com.cdz360.biz.auth.corp.po.CorpUserOrgPo;
import com.cdz360.biz.model.cus.corp.po.CorpOrgPo;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.Nullable;

import java.math.BigDecimal;

@Mapper
public interface CorpRwMapper {

    long insertCorp(CorpPo corp);

    long updateCorp(BlocUser corp);

    int addOrUpdateCorpOrg(CorpOrgPo corpOrgPo);

    int insertCorpUserOrg(CorpUserOrgPo corpOrgPo);

    int updateCorpOrg(CorpOrgPo corpOrgPo);

    CorpOrgPo countOrgByName(@Param("name") String name, @Param("corpId") Long corpId);

    int addCorpOrg(CorpOrgPo corpOrgPo);

     int addCorpUserRole(Long userId);

    CorpOrgPo getOrgById(Long orgId);

    /**
     * 启用、禁用企业
     * @param corpId
     * @return
     */
    int updateCorpEnable (Long corpId);

    int moveCorp(@Param("corpId") Long corpId, @Param("commId") Long commId, @Param("topCommId") Long topCommId);

    int setRenewReminderAmount(@Param("corpId") long corpId,
                               @Nullable @Param("amount") BigDecimal amount);

    int setCorpFullInvoicing(@Param("corpId") Long corpId,
        @Nullable @Param("fullInvoicing") Boolean fullInvoicing);

}
