<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.ds.ro.corp.mapper.CorpOrgRoMapper">

    <select id="getCorpOrgBySysUid" resultType="com.cdz360.biz.auth.corp.po.CorpOrgPo">
        select org.*
        from t_corp_org org
        left join t_corp_user_org cuo on org.id = cuo.orgId
        where cuo.sysUid  = #{sysUid}
        and org.corpId = #{corpId}
    </select>

</mapper>