package com.cdz360.biz.auth.service;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.corp.type.CorpType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.auth.corp.param.BalanceRemindParam;
import com.cdz360.biz.auth.corp.po.BlocUser;
import com.cdz360.biz.auth.corp.po.CorpUserOrgPo;
import com.cdz360.biz.auth.ds.ro.corp.CorpRoDs;
import com.cdz360.biz.auth.ds.ro.corp.mapper.CorpRoMapper;
import com.cdz360.biz.auth.ds.ro.remind.RemindAccountRoDs;
import com.cdz360.biz.auth.ds.ro.user.SiteGroupUserRoDs;
import com.cdz360.biz.auth.ds.ro.user.SysUserRoDs;
import com.cdz360.biz.auth.ds.rw.corp.CorpRwDs;
import com.cdz360.biz.auth.ds.rw.corp.mapper.CorpRwMapper;
import com.cdz360.biz.auth.ds.rw.remind.RemindAccountRwDs;
import com.cdz360.biz.auth.ds.rw.user.SiteGroupUserRwDs;
import com.cdz360.biz.auth.ds.rw.user.SysUserRwDs;
import com.cdz360.biz.auth.feign.CarUserFeignClient;
import com.cdz360.biz.auth.feign.reactor.UserFeignClient;
import com.cdz360.biz.auth.model.dto.R;
import com.cdz360.biz.auth.model.vo.RemindAccountVo;
import com.cdz360.biz.auth.model.vo.TCommercial;
import com.cdz360.biz.auth.sys.po.PointPo;
import com.cdz360.biz.auth.user.po.SysUserPo;
import com.cdz360.biz.auth.user.vo.UserBalanceVo;
import com.cdz360.biz.auth.utils.IotAssert;
import com.cdz360.biz.model.cus.corp.param.ChangeByGidsParam;
import com.cdz360.biz.model.cus.corp.param.ListCorpParam;
import com.cdz360.biz.model.cus.corp.po.CorpOrgPo;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.corp.vo.CorpOrgVO;
import com.cdz360.biz.model.cus.corp.vo.CorpSimpleVo;
import com.cdz360.biz.model.cus.corp.vo.CorpVo;
import com.cdz360.biz.model.cus.message.po.MessagePo;
import com.cdz360.biz.model.cus.message.type.BroadCastType;
import com.cdz360.biz.model.cus.message.type.MsgType;
import com.cdz360.biz.model.cus.message.type.PlatformType;
import com.cdz360.biz.model.cus.user.param.ModifyCusInfoParam;
import com.cdz360.biz.model.sys.vo.CorpGroupTinyVo;
import com.cdz360.data.sync.service.DcEventPublisher;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.hash.Hashing;
import jakarta.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestParam;

//import com.cdz360.biz.auth.ds.rw.blocuser.RBlocUserRwDs;

@Slf4j
@Service
public class CorpBizService {

    private final String CORP_PHONE_PREFIX = "B";
    @Autowired
    private CorpRoDs corpRoDs;
    @Autowired
    private CorpRwDs corpRwDs;
    @Autowired
    private RemindAccountRoDs remindAccountRoDs;
    @Autowired
    private RemindAccountRwDs remindAccountRwDs;
    @Autowired
    private SiteGroupUserRoDs siteGroupUserRoDs;
    @Autowired
    private SiteGroupUserRwDs siteGroupUserRwDs;
    @Autowired
    private SysUserRwDs sysUserRwDs;
    @Autowired
    private SysUserRoDs sysUserRoDs;
    @Autowired
    private CorpRoMapper corpRoMapper;
    @Autowired
    private CorpRwMapper corpRwMapper;
    @Autowired
    private CarUserFeignClient carUserFeignClient;
    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private PublishCorpOrgInfoService publishCorpOrgInfoService;
    @Autowired
    private PublishCorpInfoService publishCorpInfoService;
    @Autowired
    private DcCusBalanceService dcCusBalanceService;
    @Autowired
    private TCommercialService tCommercialService;
    @Autowired
    private MessageBizService messageBizService;

//    @Autowired
//    private RBlocUserRwDs rBlocUserRwDs;
    @Autowired
    private LoginService loginService;
    @Autowired
    private DcEventPublisher dcEventPublisher;

    @Transactional(rollbackFor = Exception.class)
    public synchronized Long addCorp(CorpPo corp) {
        log.info("corp = {}", JsonUtils.toJsonString(corp));
//        if (corp.getCreatorId() == null || StringUtils.isBlank(corp.getCreatorName())) {
//            throw new DcServiceException("获取登录账号信息失败");
//        }
        //企业名称是否存在
        CorpPo blocCount = corpRoDs.getCorpCountByCorpName(
            corp.getCorpName());
        if (blocCount != null) {
            throw new DcServiceException("企业名称已存在");
        }
        //登录账号是否存在
        SysUserPo userInfo = sysUserRoDs.getByUsername(corp.getAccount(),
            AppClientType.CORP_WEB.getCode());
        if (userInfo != null) {
            throw new DcServiceException("账号已存在");
        }

        //判断账户是否存在,如果存在
        ObjectResponse<Long> res = carUserFeignClient.createBlocUserByPhone(corp);
        if (res == null) {
            log.warn("Feign 调用失败");
            throw new DcServiceException(DcConstants.KEY_RES_CODE_SERVER_ERROR,
                "系统繁忙，请稍后重试");
        }

        if (res.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
            log.warn("res = {}", JsonUtils.toJsonString(res));
            throw new DcServiceException(res.getStatus(), res.getError());
        }

        //sys_user中插入数据
        SysUserPo sysUserPo = new SysUserPo();
        sysUserPo.setUsername(corp.getAccount());
        sysUserPo.setPassword(Base64.getEncoder().encodeToString(Hashing.sha256()
            .hashString(corp.getCorpName() + corp.getPassword(), Charset.defaultCharset())
            .asBytes()));
//        sysUserPo.setPassword(new Sha256Hash(corp.getPassword(), corp.getCorpName()).toBase64());
        sysUserPo.setSalt(corp.getCorpName());
        sysUserPo.setName(corp.getAccount());
        sysUserPo.setPhone(corp.getPhone());
        sysUserPo.setTopCommId(corp.getTopCommId());
        sysUserPo.setCommId(corp.getCommId());
        sysUserPo.setStatus(1);
        sysUserPo.setCreateBy(res.getData());
        sysUserPo.setPlatform((long) AppClientType.CORP_WEB.getCode());
        sysUserPo.setBusinessImage(corp.getBusinessImage());
        sysUserRwDs.insetUser(sysUserPo);

        //系统管理员给最高权限角色

        corpRwMapper.addCorpUserRole(sysUserPo.getId());

        //插入企业信息表  t_corp
        corp.setUid(res.getData());
        corp.setEnable(true);
        corp.setSysUid(sysUserPo.getId());
        this.corpRwDs.insertCorp(corp);

        //t_corp_org中插入数据
        CorpOrgPo corpOrgPo = new CorpOrgPo();
        corpOrgPo.setOrgName(corp.getCorpName()).setCorpId(corp.getId()).setOrgLevel(1)
            .setAccount(corp.getAccount()).setEnable(true);
        corpRwDs.addOrUpdateCorpOrg(corpOrgPo);
        corpOrgPo.setL1Id(corpOrgPo.getId());
        //设置l1Id
        corpRwDs.updateCorpOrg(corpOrgPo);

        //t_corp_user_org中插入数据
        CorpUserOrgPo corpUserOrgPo = new CorpUserOrgPo();
        corpUserOrgPo.setCorpId(corp.getId());
        corpUserOrgPo.setSysUid(sysUserPo.getId());
        corpUserOrgPo.setOrgId(corpOrgPo.getId());
        corpRwDs.insertCorpUserOrg(corpUserOrgPo);

        sysUserPo.setCorpId(corp.getId());
        sysUserRwDs.updateCorpId(sysUserPo);

        //同步t_corp到d_charger.t_r_corp
        this.publishCorpInfoService.publishCorpInfo(corp.getId(), IotEvent.CREATE);
        //同步t_corp_org到d_card_manage.t_corp_org
        this.publishCorpOrgInfoService.PublishCorpOrgInfo(corpOrgPo.getId());

        return corp.getId(); // 返回ID
    }


    public Page<CorpOrgVO> getCorpOrgList(Long userId, Long corpId, Integer index, Integer size) {
        Page<CorpOrgVO> pageInfo = PageHelper.startPage(index, size, true, false, null);
        Page<CorpOrgVO> corpOrgVOPage = corpRoMapper.getOrgByUserId(corpId, userId);
        log.info("测试" + JsonUtils.toJsonString(corpOrgVOPage));
        corpOrgVOPage.forEach(e -> {

            if (e.getOrgLevel() == null) {

            } else if (e.getOrgLevel().equals(1)) {
                e.setPName("--");
            } else if (e.getOrgLevel().equals(2)) {
                CorpOrgPo corpInfo = corpRoMapper.getCorpOrgByOrgId(e.getL1Id());
                if (corpInfo != null) {
                    e.setPName(corpInfo.getOrgName());
                    e.setParentId(corpInfo.getId());
                }
            } else {
                CorpOrgPo corpInfo = corpRoMapper.getCorpOrgByOrgId(e.getL2Id());
                if (corpInfo != null) {
                    e.setPName(corpInfo.getOrgName());
                    e.setParentId(corpInfo.getId());
                }
            }

        });
        corpOrgVOPage.setTotal(pageInfo.getTotal());
        return corpOrgVOPage;
    }


    public ListResponse<CorpOrgVO> getCorpOrgByLevel(Long userId, Long corpId, Integer level,
        Integer userLevel) {
        List<CorpOrgVO> corpOrgVOS = corpRoMapper.getCorpOrgByLevel(userId, corpId, level,
            userLevel);
        return new ListResponse<>(corpOrgVOS);
    }

    public ObjectResponse<CorpOrgPo> getOrgInfoByLevel(Long corpId, Integer level) {
        return RestUtils.buildObjectResponse(corpRoMapper.getOrgInfoByLevel(corpId, level));
    }

    public int addOrUpdateCorpOrg(CorpOrgPo corpOrgPo) {

        CorpOrgPo corpOrg = corpRwMapper.countOrgByName(corpOrgPo.getOrgName(),
            corpOrgPo.getCorpId());

        if (corpOrgPo.getId() != null) {
            if (corpOrg != null && !corpOrg.getId().equals(corpOrgPo.getId())) {
                throw new DcServiceException("存在同名(同账号)组织");
            }
            int ret = corpRwMapper.updateCorpOrg(corpOrgPo);
            if (ret > 0) {
                this.publishCorpOrgInfoService.PublishCorpOrgInfo(corpOrgPo.getId());
            }
            return ret;
        }

        if (corpOrg != null) {
            throw new DcServiceException("存在同名(同账号)组织");
        }
        //三级商户获取二级的上级作为l1Id
        if (corpOrgPo.getOrgLevel().equals(3)) {
            CorpOrgPo orgInfo = corpRwMapper.getOrgById(corpOrgPo.getL2Id());
            if (orgInfo != null) {
                corpOrgPo.setL1Id(orgInfo.getL1Id());
            }
        }
        //把自己的id设置回l1Id或者l2Id,3级商户不用再次update
        int i = corpRwMapper.addCorpOrg(corpOrgPo);
        if (i > 0) {
            this.publishCorpOrgInfoService.PublishCorpOrgInfo(corpOrgPo.getId());
        }
        if (corpOrgPo.getOrgLevel() == 1) {
            corpOrgPo.setL1Id(corpOrgPo.getId());
            if (corpRwMapper.updateCorpOrg(corpOrgPo) > 0) {
                this.publishCorpOrgInfoService.PublishCorpOrgInfo(corpOrgPo.getId());
            }
        } else if (corpOrgPo.getOrgLevel() == 2) {
            corpOrgPo.setL2Id(corpOrgPo.getId());
            if (corpRwMapper.updateCorpOrg(corpOrgPo) > 0) {
                this.publishCorpOrgInfoService.PublishCorpOrgInfo(corpOrgPo.getId());
            }
        }
        return i;
    }

    public ListResponse<CorpOrgVO> getOrgByUserId(Long corpId, Long cusId) {
        List<CorpOrgVO> corpOrgVOS = corpRoMapper.getOrgByUserId(corpId, cusId);
        return new ListResponse<>(corpOrgVOS);
    }

    public CorpPo getCorp(Long corpId) {
        if (corpId == null) {
            throw new DcArgumentException("参数错误, 企业ID不能为空");
        }
        var ret = this.corpRoMapper.getCorp(corpId, false);
        if (ret.getSysUid() != null) {
            ret.setGids(siteGroupUserRoDs.getGidListByUid(ret.getSysUid()));
        }
        return ret;
    }

//    public CorpPo getCorpByUid(Long uid) {
//        if (uid == null) {
//            throw new DcArgumentException("参数错误, 企业ID不能为空");
//        }
//        var ret = this.corpRoMapper.getCorpByUid(uid);
//        return ret;
//    }

    @Transactional(rollbackFor = Exception.class)
    public ObjectResponse<CorpPo> updateCorpUser(BlocUser blocUser) {

        blocUser.setUpdateTime(new Date());
        //企业名称是否存在
        CorpPo corp = corpRoMapper.getCorpCountByCorpName(
            blocUser.getBlocUserName());

        if (corp != null && !corp.getId().equals(blocUser.getId())) {
            throw new DcServiceException("企业名称已存在");
        }
        //获取企业信息
        CorpPo corpInfo = corpRoMapper.getCorpById(blocUser.getId());
        log.debug("corpInfo: {}", corpInfo);
        if (corpInfo == null) {
            throw new DcServiceException("企业信息不存在");
        }

        //获取登陆账户信息
        if (blocUser.getAccount() != null) { // 为了兼容企业结算方式的调整
            SysUserPo sysUser = sysUserRoDs.getByUsername(blocUser.getAccount(),
                AppClientType.CORP_WEB.getCode());
            if (sysUser == null || !sysUser.getCorpId().equals(blocUser.getId())) {
                throw new DcServiceException("登陆用户信息不存在");
            }

            //更新登陆账号信息
            if (blocUser.getPassword() != null) {
                String password = Base64.getEncoder().encodeToString(Hashing.sha256()
                    .hashString(sysUser.getSalt() + blocUser.getPassword(),
                        Charset.defaultCharset())
                    .asBytes());
//                String password = new Sha256Hash(blocUser.getPassword(),
//                    sysUser.getSalt()).toBase64();
                sysUserRwDs.updateUserPasswordAndUsername(sysUser.getId(), password,
                    blocUser.getAccount());
            }
        }
        // 若企业商户信息发生变更，则对应登录账户也需变更
        if (blocUser.getCommId() != null
            && !NumberUtils.equals(blocUser.getCommId(), corpInfo.getCommId())) {
            TCommercial tCommercial = tCommercialService.selectById(blocUser.getCommId());
            IotAssert.isNotNull(tCommercial, "找不到该商户");
            blocUser.setTopCommId(tCommercial.getTopCommId());
            sysUserRwDs.moveCorp(corpInfo.getId(), blocUser.getCommId(),
                tCommercial.getTopCommId());
        }

        ObjectResponse<UserBalanceVo> byUserId = carUserFeignClient.findByUserId(corpInfo.getUid());
        if (byUserId == null || byUserId.getData() == null) {
            throw new DcServiceException("企业对应用户不存在");
        }

        UserBalanceVo userBalanceVo = byUserId.getData();
        if (StringUtils.isNotBlank(blocUser.getPhone()) &&
            !(CORP_PHONE_PREFIX + blocUser.getPhone()).equals(userBalanceVo.getPhone())) {
            log.info("修改企业手机号，更新企业对应的用户手机号: B{} -> B{}",
                userBalanceVo.getPhone(), blocUser.getPhone());
            ModifyCusInfoParam modifyCusInfoParam = new ModifyCusInfoParam();
            modifyCusInfoParam.setCusId(corpInfo.getUid());
            modifyCusInfoParam.setPhone(CORP_PHONE_PREFIX + blocUser.getPhone());
            ObjectResponse objectResponse = carUserFeignClient.updateCusInfo(modifyCusInfoParam);
            if (objectResponse == null
                || objectResponse.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
                throw new DcServiceException("更新企业对应用户失败");
            }
        }

        //更新企业信息
        corpRwDs.updateCorp(blocUser);
        this.corpSiteGroupUserRef(corpInfo, blocUser.getGids());

        //同步d_card_manager.t_bloc_user
        this.publishCorpInfoService.publishCorpInfo(corpInfo.getId());

        return RestUtils.buildObjectResponse(this.getCorp(corpInfo.getId()));
    }

    /**
     * 调整企客场站组关系 远程调用刷新可用场站
     *
     * @param corpInfo 企业信息
     * @param gidList  企业的可用站点组
     */
    private void corpSiteGroupUserRef(CorpPo corpInfo,
        List<String> gidList) {
        if (gidList == null) {
            return;
        }
        Long uid = corpInfo.getSysUid();
        List<String> originGids = siteGroupUserRoDs.getGidListByUid(uid);
        if (com.cdz360.biz.auth.utils.CollectionUtils.isEqual(gidList, originGids)) {
            // 未变更可用站点组
            return;
        }

        try {
            if (CollectionUtils.isEmpty(gidList)) {
                siteGroupUserRwDs.deleteByUid(uid);
            } else {
                siteGroupUserRwDs.batchDelete(uid, gidList);
                siteGroupUserRwDs.batchInsert(uid, gidList);
            }
        } catch (Exception e) {
            log.error("调整企客场站组关系异常: err = {}", e.getMessage(), e);
        }

        SysUserPo sysUser = sysUserRoDs.getByUseId(uid);
        loginService.clearTokenByUsername(sysUser.getPlatform().intValue(), sysUser.getUsername());

        ChangeByGidsParam param = new ChangeByGidsParam();
        param.setCorpId(corpInfo.getId());
        param.setGids(gidList);
        param.setTopCommId(corpInfo.getTopCommId());
        userFeignClient.changeByGids(param)
            .doOnNext(res -> IotAssert.isTrue(res != null
                    && res.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS,
                "根据企业gids刷新鉴权介质的可用场站失败"))
            .subscribe();
    }

    @Transactional
    public ListResponse<CorpVo> getCorpList(ListCorpParam param) {
        log.info("param = {}", JsonUtils.toJsonString(param));
        var list = this.corpRoDs.getCorpList(param);
        var total = this.corpRoDs.getCorpCount(param);
        ListResponse<CorpVo> cvListRes = this.buildCorpVoListResponse(list, total);
        if (CollectionUtils.isEmpty(list)) {
            return cvListRes;
        }
        Long topCommId = param.getTopCommId();
        if (topCommId == null) {
            topCommId = list.get(0).getTopCommId();
        }
        List<Long> uidList = list.stream().map(CorpPo::getUid).collect(Collectors.toList());
        ListResponse<PointPo> pointRes = this.dcCusBalanceService.queryPointPo(
            PayAccountType.PERSONAL, uidList,
            topCommId, topCommId, true, null);
        Map<Long, PointPo> map = pointRes.getData().stream()
            .collect(Collectors.toMap(k -> Long.parseLong(k.getUid()), p -> p));

        cvListRes.getData().stream().forEach(c -> {
            PointPo p = map.get(c.getUid());
            if (p != null) {
                c.setAmount(p.getPoint()).setAvailableAmount(p.getAvailable())
                    .setFrozenAmount(p.getFrozen());
            }
        });
        return cvListRes;
    }

    public ListResponse<CorpVo> buildCorpVoListResponse(List<CorpPo> list, long total) {
        var cvList = list.stream().map(c -> {
            CorpVo cv = new CorpVo();
            BeanUtils.copyProperties(c, cv);
            return cv;
        }).collect(Collectors.toList());
        return new ListResponse<>(cvList, total);
    }

    public BaseResponse updateCorpEnable(Long corpId) {
        this.corpRwDs.updateCorpEnable(corpId);
        return BaseResponse.success();
    }

    public List<CorpSimpleVo> getCorpByCommId(String commIdChain, Long corpId) {
        if (commIdChain == null) {
            throw new DcServiceException("请求参数不正确");
        }
        return corpRoDs.getCorpByCommId(commIdChain, 1, corpId);
    }

    public BaseResponse moveCorp(Long corpId, Long commId) {
        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");

        TCommercial tCommercial = tCommercialService.selectById(commId);
        IotAssert.isNotNull(tCommercial, "找不到该商户");

        CorpPo corpById = corpRoDs.getCorpById(corpId);
        IotAssert.isNotNull(corpById, "找不到该企业");

        log.info("moveCorp t_corp: oldCommId-{}, topCommId-{}, newCommId-{}",
            corpById.getCommId(), tCommercial.getTopCommId(), commId);
        IotAssert.isTrue(corpRwDs.moveCorp(corpId, commId, tCommercial.getTopCommId()) > 0,
            "更新企业失败");

        log.info("moveCorp sys_user");
        IotAssert.isTrue(sysUserRwDs.moveCorp(corpId, commId, tCommercial.getTopCommId()) > 0,
            "更新企业失败登陆信息失败");

//        log.info("更新t_r_bloc_user: {}条记录", rBlocUserRwDs.moveCorp(corpId, commId));

        publishCorpInfoService.publishCorpInfo(corpId);
        return BaseResponse.success();
    }

    @Transactional
    public BaseResponse setRenewReminderAmount(BalanceRemindParam param) {
        IotAssert.isNotNull(param.getCorpId(), "企业ID无效");
        IotAssert.isNotNull(param.getRemindOpen(), "请指定续费提醒开关状态");

        long corpId = param.getCorpId();
        boolean remindOpen = param.getRemindOpen();
        BigDecimal amount = param.getAmount();

        remindAccountRwDs.updateCorpBalanceRemindAccount(
            corpId, param.getInRemindUidList(), param.getExRemindUidList());

        return corpRwDs.setRenewReminderAmount(corpId, remindOpen, amount) > 0
            ? RestUtils.success()
            : new BaseResponse(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "更新失败");
    }

    public BaseResponse resetEmailSendStatus(Long corpId, Long corpUid) {
        try {
            // STEP 1.获取该企业的账户余额
            CorpPo corpPo = null;
            if (corpId != null) {
                corpPo = corpRoDs.getCorpById(corpId);
            } else {
                corpPo = corpRoDs.getCorpByUid(corpUid);
            }
            IotAssert.isNotNull(corpPo, "找不到企业信息");
            if (corpPo.getRenewReminderAmount() == null) {
                log.info("企业不开启续费提醒");
                return RestUtils.success();
            }
            ListResponse<PointPo> pointRes = this.dcCusBalanceService.queryPointPo(
                PayAccountType.PERSONAL, List.of(corpPo.getUid()),
                corpPo.getTopCommId(), corpPo.getTopCommId(), true, null);

            // STEP 2.余额若大于续费提醒金额，则重置邮件的发送状态
            if (pointRes.getData() != null && pointRes.getData().size() > 0) {
                PointPo pointPo = pointRes.getData().get(0);
                if (DecimalUtils.gt(pointPo.getPoint(), corpPo.getRenewReminderAmount())) {
                    BlocUser blocUser = new BlocUser();
                    blocUser.setId(corpId);
                    blocUser.setIsSendReminderEmail(false);
                    corpRwDs.updateCorp(blocUser);
                }
            }
        } catch (Exception ex) {
            log.error("企业客户充值后，按条件重置邮件发送状态，发生异常 msg: {}", ex.getMessage(),
                ex);
        }
        return RestUtils.success();// 固定返回成功
    }

    public BaseResponse renewalReminderJob() {
        log.info("renewalReminderJob==>开始执行续费提醒任务!");
        final int size = 10;
        long start = 0;

        List<CorpPo> poList = null;
        do {
            // STEP 1.获取需要续费提醒的企业
            poList = corpRoDs.getNeedRemindCorp(start, size);

            if (CollectionUtils.isNotEmpty(poList)) {

                // STEP 2.根据topCommId分组
                Map<Long, List<Long>> topCommIdMap =
                    poList.stream().collect(
                        Collectors.groupingBy(CorpPo::getTopCommId,
                            Collectors.mapping(CorpPo::getUid,
                                Collectors.toList())));

                List<CorpPo> finalPoList = poList;
                topCommIdMap.forEach((topCommId, uidList) -> {
                    // STEP 3.查询账户余额
                    ListResponse<PointPo> pointRes = this.dcCusBalanceService.queryPointPo(
                        PayAccountType.PERSONAL, uidList,
                        topCommId, topCommId, true, null);
                    Map<Long, PointPo> map = pointRes.getData().stream()
                        .collect(Collectors.toMap(k -> Long.parseLong(k.getUid()), p -> p));

                    // STEP 4.检查并推送提醒消息
                    finalPoList.forEach(c -> {
                        PointPo p = map.get(c.getUid());
                        log.debug("PointPo: {}, renewReminderAmount: {}", p,
                            c.getRenewReminderAmount());
                        if (p != null && DecimalUtils.lte(p.getPoint(),
                            c.getRenewReminderAmount())) {
                            this.sendReminderMessage(c, p.getPoint());
                            log.info(
                                "推送提醒消息 id: {}, corpName: {}, renewReminderAmount: {}, point: {}",
                                c.getId(), c.getCorpName(), c.getRenewReminderAmount(),
                                p.getPoint());
                        }
                    });
                });
            }

            // STEP 5.递增查询
            start += 10;

        } while (poList.size() == size);

        log.info("end");
        return RestUtils.success();
    }

    private void sendReminderMessage(
        CorpPo corp, BigDecimal remainder) {
        Long corpId = corp.getId();
        BigDecimal renewReminderAmount = corp.getRenewReminderAmount();
        Long commId = corp.getCommId();
        MessagePo msg = new MessagePo();
        msg.setTitle("企业账户续费提醒")
            .setContent(
                "尊敬的客户，您的企业(" + corp.getCorpName() + ")账户余额不足" + renewReminderAmount
                    + "元，任我充提醒您及时充值")
            .setMsgType(MsgType.REMIND)
            .setCorpId(corpId)
            .setPlatformList(List.of(PlatformType.ALL))
            .setBroadcast(BroadCastType.PART)
            .setCommIdList(List.of(commId))
            .setOpUid(0L);
        messageBizService.addMessage(msg);

        BlocUser blocUser = new BlocUser();
        blocUser.setId(corpId);
        blocUser.setIsSendReminderEmail(true);
        corpRwDs.updateCorp(blocUser);

        // 企业余额提醒推送[重启导致丢失，不需要重推]
        this.publish2WXWork(corp.getTopCommId(), corp.getId(), corp.getCorpName(), remainder);
    }

    public void publish2WXWork(Long topCommId, Long corpId, String corpName, BigDecimal remainder) {
        if (null == corpId) {
            return;
        }
        List<RemindAccountVo> remindList = remindAccountRoDs.getRemindAccountByCorpId(corpId);
        if (CollectionUtils.isEmpty(remindList)) {
            return;
        }

        // com.chargerlinkcar.framework.common.domain.dto.msg.CorpWxPushMsg
        HashMap<String, Object> data = new HashMap<>();
        data.put("topCommId", topCommId);
        data.put("type", "CORP_REMAINDER");
//        data.put("title", "企客续费通知");
        data.put("toUserId", remindList.stream()
            .map(RemindAccountVo::getUid)
            .map(Object::toString)
            .collect(Collectors.joining("|")));
        data.put("data", JsonUtils.toJsonString(new HashMap<>() {{
            put("corpName", corpName);
            put("remainder", remainder);
        }}));
        this.dcEventPublisher.publishCorpWxMsg(JsonUtils.toJsonString(data));
    }

    public CorpPo getCorpByUid(Long corpUid) {
        CorpPo result = corpRoDs.getCorpByUid(corpUid);
        if (null != result) {
            result.setCreateTime(null);
            result.setUpdateTime(null);
        }
        return result;
    }

    public ListResponse<String> getGidsById(Long corpId) {
        var po = corpRoDs.getCorpById(corpId);
        if (po == null) {
            return RestUtils.buildListResponse(List.of());
        }

        var result = siteGroupUserRoDs.getGidListByUid(po.getSysUid());
        return RestUtils.buildListResponse(result);
    }

    public ListResponse<CorpGroupTinyVo> getCorpGroups(List<Long> corpIdList) {
        return RestUtils.buildListResponse(siteGroupUserRoDs.getCorpGroups(corpIdList));
    }

    public BaseResponse updateCorpFullInvoice(Long corpId, Boolean fullInvoicing) {
        IotAssert.isNotNull(corpId, "corpId不能为空");
        IotAssert.isNotNull(fullInvoicing, "fullInvoicing不能为空");
        CorpPo corp = corpRoDs.getCorpById(corpId);

        if (!CorpType.HLHT.equals(corp.getType())) {
            log.info("非互联互通企业无需修改全额开票,corpId={}", corpId);
            return RestUtils.success();
        }
        corpRwDs.setCorpFullInvoicing(corpId, fullInvoicing);
        return RestUtils.success();
    }


}
