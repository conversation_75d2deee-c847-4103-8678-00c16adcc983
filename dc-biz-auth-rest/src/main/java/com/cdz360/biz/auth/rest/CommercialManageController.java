package com.cdz360.biz.auth.rest;

/**
 * TCommercialManageController
 *
 * @since 2019/10/12
 * <AUTHOR>
 */

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.model.vo.TCommercialManage;
import com.cdz360.biz.auth.service.TCommercialManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class CommercialManageController {

    @Autowired
    private TCommercialManageService tCommercialManageService;

    @GetMapping("/api/commercialManage/appCfg2Redis")
    public BaseResponse appCfg2Redis() {
        tCommercialManageService.appCfg2Redis();
        return BaseResponse.success();
    }


    @GetMapping("/api/commercialManage/getCommercialManage")
    public ObjectResponse<TCommercialManage> getCommercialManage(@RequestParam("topCommId") Long topCommId) {
        var commMg = this.tCommercialManageService.getTCommercialManage(topCommId);
        return RestUtils.buildObjectResponse(commMg);
    }

}