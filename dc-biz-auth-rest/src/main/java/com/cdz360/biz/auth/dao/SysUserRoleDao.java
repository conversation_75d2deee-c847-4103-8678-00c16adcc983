package com.cdz360.biz.auth.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdz360.biz.auth.model.vo.SysUserRole;
import com.cdz360.biz.auth.subscribe.vo.SubscribeOrderDetailVo;
import com.cdz360.biz.auth.sys.param.BatchAddRoleUserParam;
import com.cdz360.biz.auth.sys.param.RoleUserUpdateParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SysUserRoleDao extends BaseMapper<SysUserRole> {
    Integer insertUserRoleIdList(@Param("userId") Long userId, @Param("roleIdList") List<Long> roleIdList);

    Integer batchDelUserRole(@Param("userIdList") List<Long> userIdList, @Param("roleIdList") List<Long> roleIdList);

    List<Long> findRoleIdListByUserId(Long userId);

    List<Long> findRoleIdListByUserIdAndStatus(@Param("userId") Long userId, @Param("status") Integer status);

    Long findRoleIdByUserId(Long userId);

    Long findOrgIdByUserId(Long userId);


    List<String> findRoleNameListByUserId(Long userId);

    List<String> findRoleNameByroleId(Long userId);

    List<Long> findUserIdByRoleId(Long roleId);

    Integer batchUpdateRoleUser(RoleUserUpdateParam params);

    Integer batchUpdateRoleUserByUserId(RoleUserUpdateParam params);

    Integer batchAddRoleUser(BatchAddRoleUserParam params);

    Integer batchAddRoleUserByUserId(BatchAddRoleUserParam params);

    void batchAddRoleUserByList(@Param("days") Long days, @Param("userRoleList") List<SubscribeOrderDetailVo> userRoleList);
}
