<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.ds.rw.user.mapper.SysUserLogRwMapper">

	<resultMap id="RESULT_SYS_USER_LOG_PO" type="com.cdz360.biz.auth.user.po.SysUserLogPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="sysUid" jdbcType="BIGINT" property="sysUid" />
		<result column="loginName" jdbcType="VARCHAR" property="loginName" />
		<result column="loginUser" jdbcType="VARCHAR" property="loginUser" />
		<result column="topCommId" jdbcType="BIGINT" property="topCommId" />
		<result column="commId" jdbcType="BIGINT" property="commId" />
		<result column="opType" jdbcType="VARCHAR" property="opType" />
		<result column="ip" jdbcType="VARCHAR" property="ip" />
		<result column="clientType" property="clientType"
				typeHandler="com.cdz360.ds.type.EnumTypeHandler" />
		<result column="opObject" property="opObject"
				typeHandler="com.cdz360.biz.auth.ds.MybatisJsonTypeHandler" />
		<result column="createTime" jdbcType="DATE" property="createTime" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_SYS_USER_LOG_PO">	
		select * from t_sys_user_log where id = #{id}
		<if test="lock == true">
			for update
		</if>
	</select>

	<insert id="insertSysUserLog" useGeneratedKeys="true" keyProperty="id"
		keyColumn="id" parameterType="com.cdz360.biz.auth.user.po.SysUserLogPo">
		insert into t_sys_user_log (`sysUid`,
			`loginName`,
			`loginUser`,
			`topCommId`,
			`commId`,
			`opType`,
			`ip`,
			`clientType`,
			`opObject`,
			`createTime`)
		values (#{sysUid},
			#{loginName},
			#{loginUser},
			#{topCommId},
			#{commId},
			#{opType},
			#{ip},
			#{clientType.code},
			#{opObject, typeHandler=com.cdz360.biz.auth.ds.MybatisJsonTypeHandler},
			now())
	</insert>

	<update id="updateSysUserLog" parameterType="com.cdz360.biz.auth.user.po.SysUserLogPo">
		update t_sys_user_log set
		<if test="sysUid != null">
			sysUid = #{sysUid},
		</if>
		<if test="loginName != null">
			loginName = #{loginName},
		</if>
		<if test="loginUser != null">
			loginUser = #{loginUser},
		</if>
		<if test="topCommId != null">
			topCommId = #{topCommId},
		</if>
		<if test="commId != null">
			commId = #{commId},
		</if>
		<if test="opType != null">
			opType = #{opType},
		</if>
		<if test="ip != null">
			ip = #{ip},
		</if>
		<if test="clientType != null">
			clientType = #{clientType.code},
		</if>
		<if test="opObject != null">
			opObject = #{opObject},
		</if>
		where id = #{id}
	</update>

</mapper>
