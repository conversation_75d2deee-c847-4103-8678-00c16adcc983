package com.cdz360.biz.auth.model.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "查询提醒账户列表参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListRemindAccountParam extends BaseListParam {

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "商户ID链", example = "34474,34475")
    private String commIdChain;

    @Schema(description = "企业客户ID")
    @JsonInclude(Include.NON_NULL)
    private Long corpId;

    @Schema(description = "账户ID列表(与corpId同时存在时，组成“或”查询)")
    @JsonInclude(Include.NON_NULL)
    private List<Long> uidList;

    @Schema(description = "不包含账户ID列表")
    @JsonInclude(Include.NON_NULL)
    private List<Long> excludeUidList;
}
