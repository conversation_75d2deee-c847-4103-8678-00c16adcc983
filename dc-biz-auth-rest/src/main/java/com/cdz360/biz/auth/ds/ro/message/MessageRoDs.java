package com.cdz360.biz.auth.ds.ro.message;

import com.cdz360.biz.auth.ds.ro.message.mapper.MessageRoMapper;
import com.cdz360.biz.auth.ds.rw.message.mapper.MessageRwMapper;
import com.cdz360.biz.model.cus.message.param.ListMessageParam;
import com.cdz360.biz.model.cus.message.po.MessagePo;
import com.cdz360.biz.model.cus.message.vo.MessageVo;
import com.cdz360.biz.model.cus.message.vo.UserMessageVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 */
@Slf4j

@Service

public class MessageRoDs {

	@Autowired
	private MessageRoMapper messageRoMapper;

	public UserMessageVo getMessageById(Long msgId,Long uid){
		return this.messageRoMapper.getMessageById(msgId,uid);
	}

	public int getUnReadCount(Long uid,Long platform){
		return this.messageRoMapper.getUnReadCount(uid,platform);
	}

	public List<MessageVo> getMsgList(ListMessageParam reqParam){
		return this.messageRoMapper.getMsgList(reqParam);
	}
	public List<UserMessageVo> getUserMsgList(ListMessageParam reqParam){
		return this.messageRoMapper.getUserMsgList(reqParam);
	}
	public int getMsgListCount(ListMessageParam reqParam){
		return this.messageRoMapper.getMsgListCount(reqParam);
	}

	public int getUserMsgListCount(ListMessageParam reqParam){
		return this.messageRoMapper.getUserMsgListCount(reqParam);
	}
}

