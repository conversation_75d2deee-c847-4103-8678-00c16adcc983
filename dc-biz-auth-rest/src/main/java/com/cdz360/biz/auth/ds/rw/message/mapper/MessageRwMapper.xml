<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.biz.auth.ds.rw.message.mapper.MessageRwMapper">

<!--	<resultMap id="RESULT_ZFT_PO" type="com.cdz360.biz.auth.zft.po.ZftPo">-->

<!--		<id column="id" jdbcType="BIGINT" property="id" />-->

<!--		<result column="name" jdbcType="VARCHAR" property="name" />-->

<!--		<result column="enableBalance" jdbcType="INTEGER" property="enableBalance" />-->

<!--		<result column="wxSubMchId" jdbcType="VARCHAR" property="wxSubMchId" />-->

<!--		<result column="wxSubMchName" jdbcType="VARCHAR" property="wxSubMchName" />-->

<!--	</resultMap>-->

	<insert id="insertMessage" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
		insert into t_message (
			`opUid`,
			`title`,
			`content`,
		<if test="corpId != null">
			`corpId`,
		</if>
			`platform`,
			`msgType`,
			`broadcast`,
			`commIds`,
			`createTime`,
			`updateTime`
			)

		values (
			#{opUid},
			#{title},
			#{content},
		<if test="corpId != null">
			#{corpId},
		</if>
			#{platform.code},
			#{msgType.code},
			#{broadcast.code},
			#{commIds},
			now(),
			now()
		)
	</insert>
	<insert id="updateMessageReadLog">
		update t_sys_user_msg set readStatus=2,updateTime=now() where msgId=#{msgId} and sysUid=#{uid}
	</insert>
    <insert id="batchInsert">
		insert into t_sys_user_msg
		(sysUid, msgId, platform, createTime)
		values
		<foreach collection ="userIdList" item="item" index= "index" separator =",">
		(
		#{item},
		#{msgId},
		#{platform.code},
		now()
		)
		</foreach >
	</insert>
    <update id="editMessage">
		update t_message set enable = false,updateTime=now() where id=#{msgId}
	</update>
	<update id="editMsgLog">
		update t_sys_user_msg set enable = false,updateTime=now() where msgId=#{msgId}
	</update>


</mapper>

