package com.cdz360.biz.auth.ds.rw.sys;

import com.cdz360.biz.auth.ds.rw.sys.mapper.AccRelativeRwMapper;
import com.cdz360.biz.auth.sys.po.AccRelativePo;
import com.cdz360.biz.auth.utils.IotAssert;
import com.cdz360.biz.model.sys.vo.AccRelativeOrderVo;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AccRelativeRwDs {

    @Autowired
    private AccRelativeRwMapper mapper;

    public boolean insertOrUpdate(AccRelativePo po) {
        IotAssert.isNotNull(po.getSysUid(), "sysUid不能为空");
        return mapper.insertOrUpdate(po) > 0;
    }

    public boolean updateByCondition(AccRelativePo po) {
        IotAssert.isNotNull(po.getSysUid(), "sysUid不能为空");
        return mapper.updateByCondition(po) > 0;
    }

    public boolean dismissBySysUid(Long sysUid, boolean work) {
        return mapper.dismissBySysUid(sysUid, work) > 0;
    }

    public boolean deleteBySysUid(Long sysUid) {
        return mapper.deleteBySysUid(sysUid) > 0;
    }

    public boolean updateBatch(List<AccRelativeOrderVo> list) {
        AtomicBoolean update = new AtomicBoolean(false);
        list.forEach(e -> {
            if (!update.get() &&
                    (e.getXjOrderNum() != null || e.getYwOrderNum() != null)) {
                update.set(true);
            }
        });

        if (update.get()) {
            return mapper.updateBatch(list) > 0;
        } else {
            return false;
        }
    }

}
