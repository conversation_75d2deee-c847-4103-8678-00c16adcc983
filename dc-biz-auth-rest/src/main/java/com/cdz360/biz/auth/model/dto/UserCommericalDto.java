package com.cdz360.biz.auth.model.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class UserCommericalDto extends PageDto {
    private Long userId;
    private String username;
    private String name;
    private String phone;
    private String email;
    private Long commercialId;
    private boolean isCurrent;
    private String keyWord;
    private int offset;
    private int limit;
}
