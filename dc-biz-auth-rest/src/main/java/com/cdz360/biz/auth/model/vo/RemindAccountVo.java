package com.cdz360.biz.auth.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "余额提醒账户")
@Data
@Accessors(chain = true)
public class RemindAccountVo {

    @Schema(description = "企业ID")
    @JsonInclude(Include.NON_NULL)
    private Long corpId;

    @Schema(description = "提醒用户ID(sys_user.id)")
    @JsonInclude(Include.NON_NULL)
    private Long uid;

    //  附加字段信息
    @Schema(description = "用户账号")
    @JsonInclude(Include.NON_EMPTY)
    private String account;

    @Schema(description = "用户手机号")
    @JsonInclude(Include.NON_EMPTY)
    private String phone;

    @Schema(description = "用户名称")
    @JsonInclude(Include.NON_EMPTY)
    private String name;
}
