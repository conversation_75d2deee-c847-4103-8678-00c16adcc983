package com.cdz360.biz.auth.utils;

import org.apache.commons.lang.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.util.WebUtils;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;

public class RequestUtils {
    private static final String TOKEN = "token";

    private RequestUtils() {
    }

    public static String getToken(HttpServletRequest httpRequest) {
        //从header中获取token
        String token = httpRequest.getHeader(TOKEN);

        //如果header中不存在token，则从参数中获取token
        if (StringUtils.isBlank(token)) {
            token = httpRequest.getParameter(TOKEN);
        }

        if (StringUtils.isBlank(token)) {
            Cookie cookie = WebUtils.getCookie(httpRequest, TOKEN);
            if (cookie != null) {
                token = cookie.getValue();
            }
        }
        return token;
    }

    public static String getToken() {
        HttpServletRequest httpRequest = currentRequest();
        //从header中获取token
        String token = httpRequest.getHeader(TOKEN);

        //如果header中不存在token，则从参数中获取token
        if (StringUtils.isBlank(token)) {
            token = httpRequest.getParameter(TOKEN);
        }

        if (StringUtils.isBlank(token)) {
            Cookie cookie = WebUtils.getCookie(httpRequest, TOKEN);
            if (cookie != null) {
                token = cookie.getValue();
            }
        }
        return token;
    }
    public static HttpServletRequest currentRequest() {
        return ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();
    }

//    public static Object getAttribute(String name) {
//        return currentRequest().getAttribute(name);
//    }

    public static Cookie mamchargeCookie(String value, int expire, String domain, boolean cookieSecure) {
        Cookie cookie = new Cookie("token", value);
        cookie.setPath("/");
        cookie.setDomain(domain);
        cookie.setMaxAge(expire);
        cookie.setSecure(cookieSecure);
        return cookie;
    }
}
