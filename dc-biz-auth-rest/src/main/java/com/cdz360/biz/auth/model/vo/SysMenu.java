package com.cdz360.biz.auth.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sys_menu")
@ToString(callSuper = true)
public class SysMenu extends AbstractSeqEntity implements LogicDeletable {

    public static final int MENU_TYPE = 1;
    public static final int PAGE_TYPE = 2;
    public static final int BUTTON_TYPE = 3;
    public static final int MODULE_TYPE = 0;
    @Schema(description = "父级Menu的ID(sys_menu.id)")
    private Long pid;
    private String pids;
    private String perm;
    private String name;
    private String icon;
    private String url;
    @Schema(description = "所属系统ID(sys_system.id)")
    @TableField("subsys_id")
    private Long subsysId;
    /**
     * 1 菜单  2. 页面 3 按钮
     */
    @Schema(description = "菜单类型: 0 目录 1 菜单  2 页面 3 按钮")
    private Integer type;
    private String tips;
    private Boolean hidden;
    private Integer status;
    @TableField("is_open")
    private Boolean opened;

    @Schema(description = "组件路径(户用储能平台使用)")
    @JsonInclude(Include.NON_EMPTY)
    private String component;

    @TableField(exist = false)
    private List<SysMenu> children;
    /**
     * 所属应用名称
     */
    @TableField(exist = false)
    private String subsysName;
    /**
     * 标记角色是否选中此菜单
     */
    @TableField(exist = false)
    private Boolean isChecked;
    private String extra;
    /**
     *
     */
    @TableField(exist = false)
    private String fullKey;

    public SysMenu() {

    }


    public SysMenu(Long id) {
        super.setId(id);
    }
}
