package com.cdz360.biz.auth.model.dto;

import com.cdz360.biz.auth.model.vo.SysRole;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class RoleCategoryVo {
    private String name;
    private String code;
    private Long id;
    @Schema(description = "归属平台. 0,未知; 20, 充电管理平台; 21, 运营支撑平台")
    private Integer platform;
    private List<SysRole> roleList;
}
