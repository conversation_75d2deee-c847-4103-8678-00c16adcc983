package com.cdz360.biz.auth.model.dto;

import com.cdz360.biz.auth.model.vo.SysUser;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import lombok.Data;

@Data
public class AuthRequest {
    @NotBlank
    private String username;
    @NotBlank
    private String password;
    private String credentials;
    private Long sysId;

    @Schema(description = "归属平台. 0,未知; 1, 运营支撑平台; 2, 充电管理平台")
    private Integer platform;

    public SysUser toSysUser() {
        SysUser sysUser = new SysUser();
        sysUser.setPassword(password);
        sysUser.setUsername(username);
        sysUser.setPlatform(platform);
        return sysUser;
    }
}
