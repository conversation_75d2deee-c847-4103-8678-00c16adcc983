package com.cdz360.biz.auth.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sys_dict")
@ToString(callSuper = true)
public class SysDict extends AbstractSeqEntity {
    @NotNull
    private String name;
//    private String tips;

    private Long pid;
    private String code;

    @TableField(exist = false)
    private String pcode;

    @TableField(exist = false)
    private Integer platform;

    public SysDict() {

    }
}
