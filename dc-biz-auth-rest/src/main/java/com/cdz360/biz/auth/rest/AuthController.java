package com.cdz360.biz.auth.rest;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.config.AuthcProperties;
import com.cdz360.biz.auth.config.CurrentUser;
import com.cdz360.biz.auth.ess.vo.AdminIndexVo;
import com.cdz360.biz.auth.model.constant.Constants;
import com.cdz360.biz.auth.model.dto.AuthRequest;
import com.cdz360.biz.auth.model.dto.UserCommericalDto;
import com.cdz360.biz.auth.model.dto.ValidPhoneSysDto;
import com.cdz360.biz.auth.model.type.ErrStatus;
import com.cdz360.biz.auth.model.vo.Rez;
import com.cdz360.biz.auth.model.vo.SysMenu;
import com.cdz360.biz.auth.model.vo.SysSystem;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.model.vo.SysUserComercial;
import com.cdz360.biz.auth.model.vo.TCommercial;
import com.cdz360.biz.auth.model.vo.TCommercialUser;
import com.cdz360.biz.auth.service.AuthorityService;
import com.cdz360.biz.auth.service.LoginService;
import com.cdz360.biz.auth.service.SysMenuService;
import com.cdz360.biz.auth.service.TCommercialService;
import com.cdz360.biz.auth.sys.vo.SysMenuVo;
import com.cdz360.biz.auth.utils.RedisUtils;
import com.cdz360.biz.auth.utils.RequestUtils;
import com.cdz360.biz.model.common.request.TokenRequest;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.sys.vo.AuthorityVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api")
@Slf4j
@EnableConfigurationProperties(AuthcProperties.class)
@Tag(name = "系统登陆", description = "login")
public class AuthController {

    @Autowired
    private SysUserInterface userService;

    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private AuthorityService authorityService;
    @Autowired
    private LoginService loginService;

    @Autowired
    private TCommercialInterface httpCommercialService;
    @Autowired
    private AuthcProperties authcProperties;
    @Autowired
    private TCommercialService tCommercialService;

    @Autowired
    private SysMenuService sysMenuService;


    @PostMapping("/admin/index")
    public ObjectResponse<AdminIndexVo> adminIndex(@RequestBody @Valid TokenRequest tokenRequest) {
        log.info("token获取用户信息 ~> token:{}", tokenRequest);
        String json = loginService.getUserJson(tokenRequest.getToken());
        if (json == null) {
            log.warn("token获取用户信息 ~> token验证失败:{}", tokenRequest);
            return new ObjectResponse<>(DcConstants.KEY_RES_CODE_AUTH_ERROR, "请重新登录");
//            return Rez.error(ErrStatus.AUT_INVALID_TOKEN);
        }
        SysUser sysUser = JsonUtils.fromJson(json, SysUser.class);
        sysUser.setPassword(null);
        log.info("token获取用户信息 ~> user:{}", JsonUtils.toJsonString(sysUser));

        val user = new SysUserVo();
        BeanUtils.copyProperties(sysUser, user);

        List<AuthorityVo> authList = CollectionUtils.isEmpty(sysUser.getAuthorityList()) ?
            List.of() : sysUser.getAuthorityList()
            .stream().map(x -> {
                val auth = new AuthorityVo();
                BeanUtils.copyProperties(x, auth);
                return auth;
            }).collect(Collectors.toList());

        List<SysMenuVo> menuList = sysMenuService.getAdminIndexMenu(
                sysUser.getId(), sysUser.getPlatform())
            .stream().map(x -> {
                val menu = new SysMenuVo();
                BeanUtils.copyProperties(x, menu);
                return menu;
            }).collect(Collectors.toList());

        return RestUtils.buildObjectResponse(new AdminIndexVo().setAccount(user)
            .setAuthority(authList)
            .setMenu(menuList));
    }

    @PostMapping("/info/token")
    public ObjectResponse<SysUser> getUserByToken(@RequestBody @Valid TokenRequest tokenRequest) {
        log.info("token获取用户信息 ~> token:{}", tokenRequest);
        String json = loginService.getUserJson(tokenRequest.getToken());
        if (json == null) {
            log.warn("token获取用户信息 ~> token验证失败:{}", tokenRequest);
            return new ObjectResponse<>(DcConstants.KEY_RES_CODE_AUTH_ERROR, "请重新登录");
//            return Rez.error(ErrStatus.AUT_INVALID_TOKEN);
        }
        SysUser sysUser = JsonUtils.fromJson(json, SysUser.class);
        sysUser.setPassword(null);
        log.info("token获取用户信息 ~> user:{}", JsonUtils.toJsonString(sysUser));
        return RestUtils.buildObjectResponse(sysUser);
//        return Rez.data(sysUser);
    }


    @PostMapping("/detailInfo/token")
    public ObjectResponse<SysUser> getUserDetailByToken(HttpServletRequest request) {
        String token = request.getHeader(Constants.TOKENKEY);
        log.info("token获取用户信息 ~> token:{}", token);
        String json = loginService.getUserJson(token);
        if (json == null) {
            log.warn("token获取用户信息 ~> token验证失败:{}", token);
            return new ObjectResponse<>(DcConstants.KEY_RES_CODE_AUTH_ERROR, "请重新登录");
//            return Rez.error(ErrStatus.AUT_INVALID_TOKEN);
        }
        SysUser sysUser = JsonUtils.fromJson(json, SysUser.class);
        ObjectResponse<SysUser> rez = userService.findById(sysUser, sysUser.getId());
        if (rez.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS) {
            sysUser = rez.getData();
        }
        sysUser.setPassword(null);

        log.debug("token获取用户信息 ~> user:{}", JsonUtils.toJsonString(sysUser));
        return RestUtils.buildObjectResponse(sysUser);
//        return Rez.data(sysUser);
    }

    @PostMapping("/free/token")
    public Rez<String> detailInfo(@RequestBody SysUser su) {
        if (StringUtils.isEmpty(su.getEmail()) && StringUtils.isEmpty(su.getUsername())
            && StringUtils.isEmpty(su.getPhone())) {
            return Rez.error(ErrStatus.USER_NOT_EXIST);
        }
        Rez<SysUser> rezUser = userService.entity(su);
        if (!rezUser.isSuccess()) {
            return Rez.error(ErrStatus.USER_NOT_EXIST);
        }
        if (rezUser.getData() == null) {
            return Rez.error(ErrStatus.USER_NOT_EXIST);
        }
        SysUser sysUser = rezUser.getData();

        String token = loginService.getTokenAndSave(sysUser);

        return Rez.data(token);
    }

    @PostMapping("/free/credentials/token")
    public Rez<String> credentialsToken(@RequestBody SysUser su) {
        if (StringUtils.isEmpty(su.getCredentials())) {
            return Rez.error(ErrStatus.USER_NOT_EXIST);
        }
        Rez<SysUser> rezUser = userService.entity(su);
        if (!rezUser.isSuccess()) {
            return Rez.error(ErrStatus.USER_NOT_EXIST);
        }
        if (rezUser.getData() == null) {
            return Rez.error(ErrStatus.USER_NOT_EXIST);
        }
        SysUser sysUser = rezUser.getData();

        String token = loginService.getTokenAndSave(sysUser);

        return Rez.data(token);
    }

    @PostMapping("/free/sys_credentials/token")
    public Rez<String> sysCredentialsToken(@RequestBody AuthRequest authRequest) {
        log.warn("sysCredentialsToken authRequest = {}", JsonUtils.toJsonString(authRequest));
        if (StringUtils.isEmpty(authRequest.getCredentials())) {
            return Rez.error(ErrStatus.USER_NOT_EXIST);
        }
        if (StringUtils.isEmpty(authRequest.getSysId())) {
            return Rez.error(4014, "应用id不能为空");
        }
        SysUser su = new SysUser();
        su.setCredentials(authRequest.getCredentials());
        Rez<SysUser> rezUser = userService.entity(su);
        if (!rezUser.isSuccess()) {
            return Rez.error(ErrStatus.USER_NOT_EXIST);
        }
        if (rezUser.getData() == null) {
            return Rez.error(ErrStatus.USER_NOT_EXIST);
        }
        SysUser sysUser = rezUser.getData();
        ValidPhoneSysDto dto = new ValidPhoneSysDto(sysUser.getUsername(), authRequest.getSysId(),
            authRequest.getPlatform());
        Rez valid = userService.validPhoneUsernameEmail(dto);
        if (!valid.isSuccess()) {
            // 如果手机号 没有该应用的权限,返回错误码 4030
            if (ErrStatus.ACCESS_FORBIDDEN.getErrcode() == valid.getStatus()) {
                log.warn("获得验证码 ->!! {} 无法登录系统(id:{})", authRequest.getUsername(),
                    authRequest.getSysId());
                return valid;
            }
            // 账号锁定
            if (ErrStatus.USER_FREZZE.getErrcode() == valid.getStatus()) {
                log.warn("获得验证码 ->!! {} ,账号已锁定", authRequest.getUsername());
                return valid;
            }
            log.error("获得验证码 ->!!异常,response:{}", valid);
            return valid;
        }
        String token = loginService.getTokenAndSave(sysUser);

        return Rez.data(token);
    }

    @PostMapping("/roleInfo/token")
    public ObjectResponse<SysUser> getRoleInfoByToken(HttpServletRequest request, @RequestBody SysSystem sys) {
        String token = request.getHeader(Constants.TOKENKEY);
        String json = loginService.getUserJson(token);
        if (json == null) {
            ObjectResponse<SysUser> res = new ObjectResponse<>();
            res.setStatus(ErrStatus.AUT_INVALID_TOKEN.getErrcode())
                .setError(ErrStatus.AUT_INVALID_TOKEN.getErrmsg());
            return res;
//            return  Rez.error(ErrStatus.AUT_INVALID_TOKEN);
        }

        SysUser sysUser = JsonUtils.fromJson(json, SysUser.class);
        ObjectResponse<SysUser> rez = userService.findById(sysUser, sysUser.getId());
        if (rez.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS) {
            sysUser = rez.getData();
        }
        sysUser.setPassword(null);
        if (sys != null && sys.getId() != null) {
            Rez<List<SysSystem>> resp = userService.sysList(sysUser.getId(), sys.getId());
            if (resp.isSuccess()) {
                sysUser.setSysList(resp.getData());
            }
        }
        log.info("token获取用户信息 ~> user:{}", JsonUtils.toJsonString(sysUser));
        return RestUtils.buildObjectResponse(sysUser);
    }


    @GetMapping("/menuTree")
    public Rez<List<SysSystem>> menuTree(@CurrentUser SysUser user,
        @RequestParam(required = false) Long sysId, HttpServletRequest request) {
        log.info("用户获取子系统菜单 <~ user:{},sysId:{}", user, sysId);
        if (user == null) {
            return Rez.error(ErrStatus.AUT_INVALID_TOKEN);
        }
        Long id = user.getId();
        Rez<List<SysSystem>> resp = userService.sysList(id, sysId);
        List<SysSystem> list = resp.getData();
        return Rez.data(list);
    }

    @GetMapping("/menus/sys/{sysId}")
    public Rez sysMenus(@CurrentUser SysUser user, @PathVariable("sysId") Long sysId,
        HttpServletResponse response) {
        log.info("用户获取子系统菜单 ~> user:{},sysId:{}", user, sysId);
        if (user == null) {
            return Rez.error(ErrStatus.AUT_INVALID_TOKEN);
        }
        Assert.notNull(user.getId(), "user ");
        Rez<List<SysMenu>> menus = userService.menus(user.getId(), sysId);
        if (!menus.isSuccess()) {
            if (Objects.equals(4030, menus.getStatus())) {
                log.warn("用户无权访问!!user:{},sysId:{}", user.getId(), sysId);
                response.setStatus(200);

                //暂时桩管家前端处理方便
                if (sysId == 23) {
                    return Rez.error(4031, "用户无权访问!");
                } else {
                    return Rez.error(4030, "用户无权访问!");
                }

            }

            log.warn("用户获取子系统菜单 ~> user:{},sysId:{},response:{}", user, sysId,
                JsonUtils.toJsonString(menus));
            return Rez.error(ErrStatus.REST_ERROR);
        }

        return Rez.data(menus.getData());
    }


    @Deprecated
    @GetMapping("/menus/app/{appkey}")
    public Rez menusByAppId(@CurrentUser SysUser user, @PathVariable("appkey") String appkey) {
        log.error("deprecated !!! menusByAppId");
        if (user == null) {
            return Rez.error(ErrStatus.AUT_INVALID_TOKEN);
        }

        Assert.notNull(user.getId(), "user");

        Rez<List<SysMenu>> menus = userService.menusByAppId(user.getId(), appkey);
        if (!menus.isSuccess()) {
            return Rez.error(ErrStatus.REST_ERROR);
        }
        return Rez.data(menus.getData());
    }

    @PostMapping("/logout")
    public BaseResponse logout(@RequestBody @Valid TokenRequest tokenRequest,
        HttpServletResponse response) {
        String token = tokenRequest.getToken();
        log.info("退出登录 <~ token:{}", token);

        if (loginService.getUserJson(token) == null) {
            log.warn("token已失效,token:{}", token);
//            return Rez.error(ErrStatus.AUT_INVALID_TOKEN);

            return RestUtils.success();
//            return Rez.ok("成功");
        }

        loginService.clearToken(token);

        if (loginService.getUserJson(token) == null) {
            log.info("删除token成功");
            Cookie cookie = RequestUtils.mamchargeCookie("", -1, authcProperties.getCookieDomain(),
                authcProperties.getCookieSecure());
            response.addCookie(cookie);
            return RestUtils.success();
//            return Rez.ok("删除token成功");
        }
        return RestUtils.fail(5001, "删除token失败,请联系管理员");
//        return Rez.error(5001, "删除token失败,请联系管理员");
    }

    //##商户端

    @GetMapping("/commercials/user/current")
    public Rez commercialUsersCurrent(@CurrentUser SysUser user, UserCommericalDto ucd) {
        if (user == null) {
            return Rez.error(ErrStatus.AUT_INVALID_TOKEN);
        }
        Assert.notNull(user.getId(), "user");
        ucd.setUserId(user.getId());
        ucd.setCurrent(true);
        return getUserCom(ucd);
    }

    @GetMapping("/commercials/ids")
    public Rez commercialsIds(@CurrentUser SysUser user) {
        if (user == null) {
            return Rez.error(ErrStatus.AUT_INVALID_TOKEN);
        }
        Assert.notNull(user.getId(), "user");

        TCommercial tc = tCommercialService.selectByUserId(user.getId());
        if (tc == null) {
            return Rez.error(ErrStatus.COM_NOT_EXIST);
        }
        List<Long> ids = tCommercialService.getSubCommIdList(tc.getIdChain());
        return Rez.ofNullable(ids);
    }


    @GetMapping("/commercials/user")
    public Rez commercialUsers(@CurrentUser SysUser user, UserCommericalDto ucd) {
        if (user == null) {
            return Rez.error(ErrStatus.AUT_INVALID_TOKEN);
        }
        Assert.notNull(user.getId(), "user");
        ucd.setUserId(user.getId());
        ucd.setCurrent(false);
        return getUserCom(ucd);
    }

    @PostMapping("/commercials/user/edit")
    public BaseResponse editCommercialsUser(@CurrentUser SysUser user,
        @RequestBody TCommercialUser tcu) {
        log.info("user/edit user = {}, tcu = {}", JsonUtils.toJsonString(user),
            JsonUtils.toJsonString(tcu));
        ObjectResponse<SysUser> sysUserRes = this.userService.findById(user, user.getId());

        if (sysUserRes == null || sysUserRes.getData() == null) {
            log.error("修改账号信息失败, user = {}, tcu = {}", JsonUtils.toJsonString(user),
                JsonUtils.toJsonString(tcu));
            throw new DcServiceException(ErrStatus.ACCESS_FORBIDDEN.getErrcode(),
                ErrStatus.ACCESS_FORBIDDEN.getErrmsg());
        }
        SysUser sysUser = sysUserRes.getData();
        if ((AppClientType.SASS_MGM_WEB.getCode() == sysUser.getPlatform()) // 运营支撑平台
            || NumberUtils.equals(sysUser.getId(), tcu.getId())  // 修改自己账号信息
            || (AppClientType.MGM_WEB.getCode() == sysUser.getPlatform() &&
            this.checkAuth(sysUser.getCommIdChain(), tcu.getCommId())) // 充电管理平台,只能修改本身及下属的账号
        ) {
            Rez rez = httpCommercialService.editUser(tcu);
            return rez.isSuccess() ? RestUtils.success()
                : new BaseResponse(rez.getStatus(), rez.getError());
        } else {
            log.error("修改账号信息失败, user = {}, tcu = {}", JsonUtils.toJsonString(user),
                JsonUtils.toJsonString(tcu));
            throw new DcServiceException(ErrStatus.NOT_AUTH.getErrcode(),
                ErrStatus.NOT_AUTH.getErrmsg());
        }
    }

    private boolean checkAuth(String currUserCommIdChain, Long targetUserCommId) {
        List<String> currUserCommIdList = Arrays.asList(currUserCommIdChain.split(","));

        TCommercial comm = tCommercialService.selectById(targetUserCommId);
        List<String> targetUserCommIdList = Arrays.asList(comm.getIdChain().split(","));

        return targetUserCommIdList.containsAll(currUserCommIdList);
    }

    @PostMapping("/commercials/user/add")
    public BaseResponse addCommercialsUser(@CurrentUser SysUser user,
        @RequestBody TCommercialUser tcu) {
        Rez<SysUserComercial> userComRez = httpCommercialService.userCommercials(user.getId());
        if (userComRez.isSuccess()) {

            if (userComRez.getData().getCommercialRole() != null
                && userComRez.getData().getCommercialRole() == 1) {
                List<Long> groupIds = authorityService.getAuthGroupIdListByUid(user.getId());
                tcu.setComId(userComRez.getData().getCommercialId());
                tcu.setAuthGroupIds(groupIds);
                Rez rez = httpCommercialService.addUser(tcu);
                return rez.isSuccess() ? RestUtils.success()
                    : new BaseResponse(rez.getStatus(), rez.getError());
            } else {
                throw new DcServiceException(ErrStatus.NOT_AUTH.getErrcode(),
                    ErrStatus.NOT_AUTH.getErrmsg());
            }
        }
        throw new DcServiceException(ErrStatus.COM_NOT_EXIST.getErrcode(),
            ErrStatus.COM_NOT_EXIST.getErrmsg());
    }

    @GetMapping("/commercials/user/current/page")
    public ListResponse<TCommercialUser> commercialUsersPage(@CurrentUser SysUser user,
        UserCommericalDto ucd) {
        ListResponse<TCommercialUser> res = new ListResponse<>();
        if (user == null) {
            res.setStatus(ErrStatus.AUT_INVALID_TOKEN.getErrcode());
            res.setError(ErrStatus.AUT_INVALID_TOKEN.getErrmsg());
            return res;
            //return Rez.error(ErrStatus.AUT_INVALID_TOKEN);
        }
        Assert.notNull(user.getId(), "user");
        ucd.setUserId(user.getId());
        ucd.setCurrent(true);
        return httpCommercialService.findByUserPageByCommercialUser(ucd);
    }

    public Rez getUserCom(UserCommericalDto ucd) {

        if (ucd.isCurrent()) {
            return httpCommercialService.commercialsfromUsersCurrent(ucd);
        } else {
            return httpCommercialService.commercialsfromUsers(ucd);
        }

    }

    @GetMapping("/commercials/user/info")
    public Rez userCommercialInfo(@CurrentUser SysUser user) {
        if (user == null) {
            return Rez.error(ErrStatus.AUT_INVALID_TOKEN);
        }

        Assert.notNull(user.getId(), "user");

        return httpCommercialService.findByUserId(user.getId());
    }

    @GetMapping("/commercials/user/sub")
    public Rez userCommercialInfoSub(@CurrentUser SysUser user) {
        if (user == null) {
            return Rez.error(ErrStatus.AUT_INVALID_TOKEN);
        }

        Assert.notNull(user.getId(), "user");
        TCommercial entity = tCommercialService.selectYoungerByUserId(user.getId());
        return Rez.ofNullable(entity);
//        return httpCommercialService.userSub(user.getId());
    }


    @GetMapping("/commercials/user/sup")
    public Rez userCommercialInfoSup(@CurrentUser SysUser user) {
        if (user == null) {
            return Rez.error(ErrStatus.AUT_INVALID_TOKEN);
        }
        Assert.notNull(user.getId(), "user");
        return httpCommercialService.userSup(user.getId());
    }

    @PostMapping("/commercials/info/token")
    public ObjectResponse<TCommercialUser> getCommercialsUserByToken(@CurrentUser SysUser sysUser) {
        log.info("token获取用户信息 ~> sysUser:{}", JsonUtils.toJsonString(sysUser));
        if (sysUser == null) {
            return new ObjectResponse<>(
                ErrStatus.AUT_INVALID_TOKEN.getErrcode(), ErrStatus.AUT_INVALID_TOKEN.getErrmsg());
        }
        TCommercialUser user = new TCommercialUser();
        BeanUtils.copyProperties(sysUser, user);
        if (sysUser.getPlatform() != null
            && sysUser.getPlatform().intValue() == AppClientType.MGM_WEB.getCode()) {
            //log.info("token获取用户信息 ~> user:{}", JsonUtils.toJsonString(user));
            Rez<TCommercial> rez = httpCommercialService.findByUserId(user.getId());
            if (!rez.isSuccess()) {
                log.warn("获取用户信息失败. rez = {}", rez);
                return new ObjectResponse<>(
                    ErrStatus.COM_NOT_EXIST.getErrcode(), ErrStatus.COM_NOT_EXIST.getErrmsg());
            }
            Rez<SysUserComercial> userComRez = httpCommercialService.userCommercials(user.getId());
            if (userComRez.isSuccess()) {
                user.setCommercialRole(userComRez.getData().getCommercialRole());
            }

            TCommercial tc = rez.getData();
            user.setComId(tc.getId());
            user.setPassword(null);
            user.setTopCommId(tc.getTopCommId());
            user.setCommIdChain(tc.getIdChain());
            List<Long> ids = Arrays.asList(tc.getIdChain().split(","))
                .stream().map(s -> Long.parseLong(s)).collect(Collectors.toList());
//        Rez<List<Long>> rezids = httpCommercialService.commercialsIds(tc.getId());
//        if (rezids.isSuccess()) {
//            user.setComIds(rezids.getData());
//        }
            user.setComIds(ids);
        } else {
            user.setComId(sysUser.getCommId());
            user.setPassword(null);
            user.setTopCommId(sysUser.getTopCommId());
            user.setCommIdChain(sysUser.getCommIdChain());
        }
        log.info("token获取用户信息 ~> user:{}", JsonUtils.toJsonString(user));
        return new ObjectResponse<>(user);
    }

    @GetMapping("/commercials/manage")
    public Rez getCommercialsmanage(Long comId) {
        return httpCommercialService.commercialsManage(comId);
    }

    @GetMapping(value = "/api/commercials/manageByAppId")
    ObjectResponse<Long> apiCommercialsManageByAppId(@RequestParam("appId") String appId) {
        return httpCommercialService.commercialsManage(appId);
    }


}
