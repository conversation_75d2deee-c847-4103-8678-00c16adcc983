package com.cdz360.biz.auth.ds.rw.subscribe;

import com.cdz360.biz.auth.ds.rw.subscribe.mapper.SubscribeRwMapper;
import com.cdz360.biz.auth.subscribe.param.AddSubscribeParam;
import com.cdz360.biz.auth.subscribe.param.CreatePayOrderParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class SubscribeRwDs {


    @Autowired
    private SubscribeRwMapper subscribeRwMapper;

    public Long add(AddSubscribeParam params) {
        return subscribeRwMapper.add(params);
    }

    public Long update(AddSubscribeParam params) {
        return subscribeRwMapper.update(params);
    }

    public void updateSubOrder(String payNo,Long payChannel,Boolean status) {
         subscribeRwMapper.updateSubOrder(payNo, payChannel, status);
    }

    public void updateNoteByNo(String payNo,String note) {
        subscribeRwMapper.updateNoteByNo(payNo, note);
    }

    public void updateExpireTimeByPayNo(String payNo) {
        subscribeRwMapper.updateExpireTimeByPayNo(payNo);
    }

    public void updateStatus(Long updateBy, Long subId) {
        subscribeRwMapper.updateStatus(updateBy, subId);
    }

    public void addSubComm(Long subId, Long topCommId, List<Long> commIdList) {
        subscribeRwMapper.addSubComm(subId, topCommId, commIdList);
    }

    public void updateSubComm(Long subId) {
        subscribeRwMapper.updateSubComm(subId);
    }

    public void addSubRole(Long subId, List<Long> roleIdList) {
        subscribeRwMapper.addSubRole(subId, roleIdList);
    }


    public void updateSubRole(Long subId) {
        subscribeRwMapper.updateSubRole(subId);
    }

    public void createOrder(CreatePayOrderParam params) {
         subscribeRwMapper.createOrder(params);
    }

    public void createOrderUser(String payNo, List<Map> mapList) {
        subscribeRwMapper.createOrderUser(payNo,mapList);
    }

    public void createOrderRole(String payNo,List<Long> roleIdList) {
        subscribeRwMapper.createOrderRole(payNo,roleIdList);
    }
}
