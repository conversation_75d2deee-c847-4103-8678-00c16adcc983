package com.cdz360.biz.auth.dao;

import com.cdz360.biz.auth.model.vo.AuthorityGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AuthGroup
 *
 * @since 2020/2/11 16:54
 * <AUTHOR>
 */
@Mapper
public interface AuthGroupDao {
    int addGroup(AuthorityGroup authorityGroup);

    int updateGroup(AuthorityGroup authorityGroup);

    int deleteGroupById(@Param("id") Long id);

    List<AuthorityGroup> getGroupList();

    AuthorityGroup getGroupById(@Param("id") Long id);

    AuthorityGroup getGroupByNameNonId(@Param("groupName") String groupName, @Param("notId") Long notId);

    AuthorityGroup getGroupByRoleId(@Param("roleId") Long roleId);

    List<AuthorityGroup> getGroupListByRoleIdList(@Param("roleIdList") List<Long> roleIdList);

    List<String> getRoleCommIdByUserId(@Param("userId") Long userId);
}
