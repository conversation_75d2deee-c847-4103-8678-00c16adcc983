package com.cdz360.biz.auth.utils;

import com.cdz360.biz.model.common.constant.RegularExpression;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RegularExpressionUtil {
//    /**
//     * 字符串是否全为数字
//     *
//     * @param str 目标字符串
//     * @return
//     */
//    public static boolean digitAll(String str) {
//        Pattern pattern = Pattern.compile(RegularExpression.digit);
//        Matcher matcher = pattern.matcher(str);
//        return matcher.matches();
//    }

//    /**
//     * 字符串是否全为数字、字母
//     *
//     * @param str 目标字符串,必传
//     * @param minLength 最小位数,可选,默认为 1
//     * @param maxLength 最大位数,可选
//     * @return
//     */
//    public static boolean englishNumberAll(String str, Integer minLength, Integer maxLength) {
////        StringBuffer sb = new StringBuffer(RegularExpression.englishNumber);
//        StringBuffer sb = new StringBuffer("^[A-Za-z0-9]");
//        if (minLength != null && maxLength != null) {
//            sb.append("{").append(minLength).append(",").append(maxLength).append("}");
//        } else if (minLength != null) {
//            sb.append("{").append(minLength).append(",").append("}");
//        } else if (maxLength != null) {
//            sb.append("{").append(1).append(",").append(maxLength).append("}");
//        } else {
//            sb.append("+");
//        }
//        sb.append("$");
//        Pattern pattern = Pattern.compile(sb.toString());
//        Matcher matcher = pattern.matcher(str);
//        return matcher.matches();
//    }

    /**
     * 字符串是否为中文、英文、数字
     *
     * @param str 目标字符串
     * @return
     */
    public static boolean chineseEnglishNumberAll(String str) {
        Pattern pattern = Pattern.compile(RegularExpression.chineseEnglishNumber);
        Matcher matcher = pattern.matcher(str);
        return matcher.matches();
    }

    /**
     * <P>
     * 字符串是否为中文、英文、数字、常规字符
     * 产品需求 限定常规字符为{@code `~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘’，。、}
     * </P>
     * @param str 目标字符串,必传
     * @param minLength 最小位数,可选,默认为 1
     * @param maxLength 最大位数,可选
     * @return
     */
    public static boolean chineseEnglishNumberCharacterAll(String str, Integer minLength, Integer maxLength) {
//        StringBuffer sb = new StringBuffer(RegularExpression.chineseEnglishNumber);
        StringBuffer sb = new StringBuffer("^[\\u4E00-\\u9FA5A-Za-z0-9");//中文、英文、数字
        sb.append(RegularExpression.character);//产品需求常规字符
        sb.append("]");
        if (minLength != null && maxLength != null) {
            sb.append("{").append(minLength).append(",").append(maxLength).append("}");
        } else if (minLength != null) {
            sb.append("{").append(minLength).append(",").append("}");
        } else if (maxLength != null) {
            sb.append("{").append(1).append(",").append(maxLength).append("}");
        } else {
            sb.append("+");
        }
        sb.append("$");
        Pattern pattern = Pattern.compile(sb.toString());
        Matcher matcher = pattern.matcher(str);
        return matcher.matches();
    }


    /**
     * 邮箱校验
     * by 李楠楠 充电管理平台和运营支撑平台统一邮箱校验格式：名称@域名
     * 名称和域名的字符限制：允许英文字母、数字、下划线、英文句号、以及中划线组成
     * @param email
     * @return
     */
    public static boolean isEmail(String email) {
        Pattern pattern = Pattern.compile(RegularExpression.email);
        Matcher matcher = pattern.matcher(email);
        return matcher.matches();
    }
}
