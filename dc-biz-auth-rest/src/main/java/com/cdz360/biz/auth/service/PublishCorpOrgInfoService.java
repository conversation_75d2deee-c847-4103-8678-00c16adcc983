package com.cdz360.biz.auth.service;

import com.cdz360.base.model.corp.dto.CorpOrgSyncDto;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.auth.ds.ro.corp.mapper.CorpRoMapper;
import com.cdz360.biz.model.cus.corp.po.CorpOrgPo;
import com.cdz360.data.sync.service.DcEventPublisher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * PublishCorpOrgInfoService
 *
 * @since 1/10/2020 4:53 PM
 * <AUTHOR>
 */
@Slf4j
@Service
public class PublishCorpOrgInfoService {

    @Autowired
    private DcEventPublisher dcEventPublisher;

    @Autowired
    private CorpRoMapper corpRoMapper;

    public void PublishCorpOrgInfo(CorpOrgSyncDto corpOrgIn) {
//        CorpOrgSyncDto corpUserSyncDto = corpOrgIn;
        if(corpOrgIn == null) {
            log.error("组织不存在. corpOrgIn = {}", corpOrgIn);
            return;
        }
        this.dcEventPublisher.publishCorpOrgInfo(corpOrgIn);
    }

    public void PublishCorpOrgInfo(CorpOrgPo corpOrgIn) {
        this.dcEventPublisher.publishCorpOrgInfo(buildCusSyncDto(corpOrgIn));
    }

    public void PublishCorpOrgInfo(List<Long> corpOrgIds) {
        if(!CollectionUtils.isEmpty(corpOrgIds)) {
            corpOrgIds.stream().forEach(e -> this.PublishCorpOrgInfo(e));
        }
    }

    public void PublishCorpOrgInfo(Long corpOrgId) {
        if(corpOrgId != null) {
            this.PublishCorpOrgInfo(buildCusSyncDto(corpRoMapper.getCorpOrgByOrgId(corpOrgId)));
        }
    }

    private CorpOrgSyncDto buildCusSyncDto(CorpOrgPo in) {
        if(in == null) {
            return null;
        } else {
            CorpOrgSyncDto out = new CorpOrgSyncDto();
            BeanUtils.copyProperties(in, out);
            return out;
        }
    }
}