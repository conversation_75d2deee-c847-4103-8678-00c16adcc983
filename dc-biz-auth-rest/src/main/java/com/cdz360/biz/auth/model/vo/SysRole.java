package com.cdz360.biz.auth.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import java.util.Set;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sys_role")
@ToString(callSuper = true)
public class SysRole extends AbstractSeqEntity implements LogicDeletable {
//    private Long id;
    @TableField
    private Integer pid;
    @NotNull
    private String name;
    private Integer category;

    /**
     * AppClientType
     */
    @Schema(description = "归属平台. 0,未知; 20, 充电管理平台; 21, 运营支撑平台")
    private Integer platform;
    @TableField(exist = false)
    private String categoryName;
    private String tips;
    private Integer status;
    private Integer version;
    @TableField(value = "corpId")
    private Long corpId;

    @TableField(value = "commId")
    private Long commId;
    @TableField(value = "topCommId")
    private Long topCommId;

    @TableField(value = "create_by")
    private Long createBy;

    @TableField(value = "commIds")
    private String commIds;

    //全部权限
    private Boolean svaha;

    private String extra;
    @TableField(exist = false)
    private Set<Long> menuIds;
    @TableField(exist = false)
    private List<SysSystem> systemList;

    @TableField(exist = false)
    private List<Long> authorityIdList;

    /**
     * 是否为新添加的类别
     * {@link categoryName}
     */
    @TableField(exist = false)
    private Boolean isAddNewCategory;

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date expireTime;

}
