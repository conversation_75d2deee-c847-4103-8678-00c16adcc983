package com.cdz360.biz.auth.ds.rw.corp;

import com.cdz360.biz.auth.corp.po.BlocUser;
import com.cdz360.biz.auth.corp.po.CorpUserOrgPo;
import com.cdz360.biz.auth.ds.rw.corp.mapper.CorpRwMapper;
import com.cdz360.biz.model.cus.corp.po.CorpOrgPo;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

@Slf4j
@Service
public class CorpRwDs {
    @Autowired
    private CorpRwMapper corpRwMapper;


    public long insertCorp (CorpPo corp) {
        return corpRwMapper.insertCorp(corp);
    }

    public int addOrUpdateCorpOrg(CorpOrgPo corpOrgPo){
        return corpRwMapper.addOrUpdateCorpOrg(corpOrgPo);
    };
    public int insertCorpUserOrg(CorpUserOrgPo corpUserOrgPo){
        return corpRwMapper.insertCorpUserOrg(corpUserOrgPo);
    };
    public Long updateCorp(BlocUser corpOrgPo){
        return  corpRwMapper.updateCorp(corpOrgPo);
    }

    public int updateCorpOrg(CorpOrgPo corpOrgPo) {
        return  corpRwMapper.updateCorpOrg(corpOrgPo);
    };

    public int updateCorpEnable (Long corpId) {return corpRwMapper.updateCorpEnable(corpId);}


    public int moveCorp(Long corpId, Long commId, Long topCommId) {
        return corpRwMapper.moveCorp(corpId, commId, topCommId);
    }

    public int setRenewReminderAmount(long corpId, boolean remindOpen, BigDecimal amount) {
        return corpRwMapper.setRenewReminderAmount(corpId, remindOpen ? amount : null);
    }

    public void setCorpFullInvoicing(Long corpId, Boolean fullInvoicing) {
        corpRwMapper.setCorpFullInvoicing(corpId, fullInvoicing);
    }

}
