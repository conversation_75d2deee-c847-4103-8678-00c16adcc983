<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.ds.ro.remind.mapper.RemindAccountRoMapper">

  <sql id="SORT_LIMIT">
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( sorts )">
      <foreach item="sort" collection="sorts"
        open="order by" separator="," close=" ">
        ${sort.columnsString} ${sort.order}
      </foreach>
    </if>
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <otherwise>
        limit #{size}
      </otherwise>
    </choose>
  </sql>

  <select id="getRemindAccountByCorpId"
    resultType="com.cdz360.biz.auth.model.vo.RemindAccountVo">
    select ra.*
    from t_remind_account ra
    left join sys_user su on su.id = ra.uid
    where ra.corpId = #{corpId}
  </select>

  <select id="findRemindAccount"
    parameterType="com.cdz360.biz.auth.model.param.ListRemindAccountParam"
    resultType="com.cdz360.biz.auth.model.vo.RemindAccountVo">
    select ra.*, su.username account, su.phone, su.name
    from t_remind_account ra
    left join sys_user su on su.id = ra.uid
    <where>
      <if test="null != corpId">
        ra.corpId = #{corpId}
      </if>
    </where>

    <include refid="SORT_LIMIT"/>
  </select>

  <select id="count" resultType="java.lang.Long">
    select count(1)
    from t_remind_account ra
    left join sys_user su on su.id = ra.uid
    <where>
      <if test="null != corpId">
        ra.corpId = #{corpId}
      </if>
    </where>
  </select>

  <select id="unbindCorpBalance"
    parameterType="com.cdz360.biz.auth.model.param.ListRemindAccountParam"
    resultType="com.cdz360.biz.auth.model.vo.RemindAccountVo">
    select su.id uid, su.username account, su.phone, su.name
    from sys_user su
    left join t_commercial comm on comm.id = su.commId
    left join t_remind_account ra on ra.uid = su.id and ra.corpId = #{corpId}
    <where>
      su.platform = 20
      <choose>
        <when test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(uidList)">
          <foreach collection="uidList" separator=","
            open="and (ra.uid is null or ra.uid in (" close="))" item="item" index="index">
            #{item}
          </foreach>
        </when>
        <otherwise>
          and ra.uid is null
        </otherwise>
      </choose>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(excludeUidList)">
        <foreach collection="excludeUidList" separator="," open="and su.id not in (" close=")"
          item="item" index="index">
          #{item}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
        and comm.idChain like CONCAT(#{commIdChain}, '%')
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( sk )">
        and (
        su.username like CONCAT('%', #{sk}, '%') or
        su.phone like CONCAT('%', #{sk}, '%') or
        su.name like CONCAT('%', #{sk}, '%')
        )
      </if>
    </where>
    <include refid="SORT_LIMIT"/>
  </select>

  <select id="unbindCorpBalanceCount" resultType="java.lang.Long">
    select count(su.id)
    from sys_user su
    left join t_commercial comm on comm.id = su.commId
    left join t_remind_account ra on ra.uid = su.id and ra.corpId = #{corpId}
    <where>
      su.platform = 20
      <choose>
        <when test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(uidList)">
          <foreach collection="uidList" separator=","
            open="and (ra.uid is null or ra.uid in (" close="))" item="item" index="index">
            #{item}
          </foreach>
        </when>
        <otherwise>
          and ra.uid is null
        </otherwise>
      </choose>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(excludeUidList)">
        <foreach collection="excludeUidList" separator="," open="and su.id not in (" close=")"
          item="item" index="index">
          #{item}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
        and comm.idChain like CONCAT(#{commIdChain}, '%')
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( sk )">
        and (
        su.username like CONCAT('%', #{sk}, '%') or
        su.phone like CONCAT('%', #{sk}, '%') or
        su.name like CONCAT('%', #{sk}, '%')
        )
      </if>
    </where>
  </select>
  <select id="corpBalanceTemporary"
    parameterType="com.cdz360.biz.auth.model.param.ListRemindAccountParam"
    resultType="com.cdz360.biz.auth.model.vo.RemindAccountVo">
    select su.id uid, su.username account, su.phone, su.name
    from sys_user su
    left join t_commercial comm on comm.id = su.commId
    <where>
      <choose>
        <when test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(uidList)">
          <foreach collection="uidList" separator="," open="(su.id in (" close=") or" item="item"
            index="index">
            #{item}
          </foreach>
          (su.id in (select ra.uid from t_remind_account ra where ra.corpId = #{corpId})))
        </when>
        <otherwise>
          su.id in (select ra.uid from t_remind_account ra where ra.corpId = #{corpId})
        </otherwise>
      </choose>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(excludeUidList)">
        <foreach collection="excludeUidList" separator="," open="and su.id not in (" close=")"
          item="item" index="index">
          #{item}
        </foreach>
      </if>

      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
        and comm.idChain like CONCAT(#{commIdChain}, '%')
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( sk )">
        and (
        su.username like CONCAT('%', #{sk}, '%') or
        su.phone like CONCAT('%', #{sk}, '%') or
        su.name like CONCAT('%', #{sk}, '%')
        )
      </if>
    </where>

    <include refid="SORT_LIMIT"/>
  </select>
  <select id="corpBalanceTemporaryCount"
    parameterType="com.cdz360.biz.auth.model.param.ListRemindAccountParam"
    resultType="java.lang.Long">
    select count(su.id)
    from sys_user su
    left join t_commercial comm on comm.id = su.commId
    <where>
      <choose>
        <when test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(uidList)">
          <foreach collection="uidList" separator="," open="(su.id in (" close=") or" item="item"
            index="index">
            #{item}
          </foreach>
          (su.id in (select ra.uid from t_remind_account ra where ra.corpId = #{corpId})))
        </when>
        <otherwise>
          su.id in (select ra.uid from t_remind_account ra where ra.corpId = #{corpId})
        </otherwise>
      </choose>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(excludeUidList)">
        <foreach collection="excludeUidList" separator="," open="and su.id not in (" close=")"
          item="item" index="index">
          #{item}
        </foreach>
      </if>

      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
        and comm.idChain like CONCAT(#{commIdChain}, '%')
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( sk )">
        and (
        su.username like CONCAT('%', #{sk}, '%') or
        su.phone like CONCAT('%', #{sk}, '%') or
        su.name like CONCAT('%', #{sk}, '%')
        )
      </if>
    </where>
  </select>
</mapper>

