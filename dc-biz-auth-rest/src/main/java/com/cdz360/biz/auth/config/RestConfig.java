package com.cdz360.biz.auth.config;

import com.cdz360.biz.auth.service.oauth.ShiroHttpService;
import com.cdz360.biz.auth.service.oauth.ShiroHttpServiceImpl;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;
import org.springframework.web.client.RestTemplate;

@Configuration
public class RestConfig {

    @Bean
    @ConfigurationProperties(prefix = "authc")
    @Order(Ordered.LOWEST_PRECEDENCE - 1)
    public AuthUrlConfig urlConfig() {
        return new AuthUrlConfig();
    }

    @Bean("authRestTpl")
    @ConditionalOnMissingBean(name = "authRestTpl")
    @ConditionalOnProperty(name = "authc.url")
    public RestTemplate restTemplate() {
        System.err.println("init url RestTemplate");
        return new RestTemplate();
    }

    @ConditionalOnMissingBean(name = "authRestTpl")
    @Bean("authRestTpl")
    @LoadBalanced
    public RestTemplate restTemplateCloud() {
        System.err.println("init cloud RestTemplate");
        return new RestTemplate();
    }

    @Bean
    @ConditionalOnMissingBean(ShiroHttpService.class)
    public ShiroHttpService shiroHttpService(AuthUrlConfig authUrlConfig, @Qualifier("authRestTpl") RestTemplate restTemplate) {
        ShiroHttpServiceImpl shiroHttpService = new ShiroHttpServiceImpl();
        shiroHttpService.setAuthUrl(authUrlConfig.getUrl());
        shiroHttpService.setRestTemplate(restTemplate);
        return shiroHttpService;
    }

}
