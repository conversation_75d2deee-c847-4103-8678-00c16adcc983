package com.cdz360.biz.auth.ds.ro.user.mapper;

import com.cdz360.biz.model.sys.constant.SiteGroupType;
import com.cdz360.biz.model.sys.dto.UserOwnerSiteGroupDto;
import com.cdz360.biz.model.sys.param.ListSiteGroupParam;
import com.cdz360.biz.model.sys.po.SiteGroupPo;
import com.cdz360.biz.model.sys.vo.SiteGroupVo;
import com.cdz360.biz.model.sys.vo.UserGroupVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SiteGroupRoMapper {


    SiteGroupVo getSiteGroupVoByGid(@Param("gid") String gid);

    SiteGroupPo getOneByName(@Param("name") String name,
        @Param("exGid") String exGid);

    SiteGroupPo getByGid(@Param("gid") String gid);

    List<SiteGroupVo> findAll(ListSiteGroupParam param);

    List<SiteGroupVo> findSiteGroupAndUser(ListSiteGroupParam param);

    List<UserGroupVo> findList(ListSiteGroupParam param);

    Long count(ListSiteGroupParam param);

    List<UserOwnerSiteGroupDto> userOwnerSiteGroup(ListSiteGroupParam param);

    UserOwnerSiteGroupDto getUserOwnerSiteGroup(
        @Param("uid") Long uid, @Param("groupType") SiteGroupType groupType);

    List<SiteGroupPo> getSiteGroupList(ListSiteGroupParam param);

    Long getHighProrityAdminUid(@Param("typeList") List<SiteGroupType> typeList,
        @Param("gidList") List<String> gidList);

}
