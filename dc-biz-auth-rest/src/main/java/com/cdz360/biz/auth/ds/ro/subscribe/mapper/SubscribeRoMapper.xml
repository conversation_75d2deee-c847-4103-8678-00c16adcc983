<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.ds.ro.subscribe.mapper.SubscribeRoMapper">

    <resultMap id="FUNCTION_DETAIL" type="com.cdz360.biz.auth.subscribe.vo.SubscribeDetailVo">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="topCommId" jdbcType="BIGINT" property="topCommId"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="days" jdbcType="BIGINT" property="days"/>
        <result column="chargingMode" jdbcType="VARCHAR" property="chargingMode"/>
        <result column="fee" jdbcType="DECIMAL" property="fee"/>
        <result column="desc" jdbcType="VARCHAR" property="desc"/>
        <result column="fileInfo" property="fileInfo" typeHandler="com.cdz360.biz.auth.ds.MybatisJsonTypeHandler"/>
        <result column="status" jdbcType="BOOLEAN" property="status"/>
        <result column="expireTime" jdbcType="TIMESTAMP" property="expireTime"/>
        <collection property="commInfoList" ofType="com.cdz360.biz.auth.subscribe.vo.CommVo">
            <result column="commId" jdbcType="BIGINT" property="id"/>
            <result column="commName" jdbcType="VARCHAR" property="commName"/>
        </collection>
        <collection property="roleIdList" ofType="java.lang.Long" javaType="list">
            <result column="roleId" property="value"/>
        </collection>
    </resultMap>
    <resultMap id="FUNCTION_LIST" type="com.cdz360.biz.auth.subscribe.vo.SubscribeDetailVo">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="topCommId" jdbcType="BIGINT" property="topCommId"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="days" jdbcType="BIGINT" property="days"/>
        <result column="chargingMode" jdbcType="VARCHAR" property="chargingMode"/>
        <result column="fee" jdbcType="DECIMAL" property="fee"/>
        <result column="desc" jdbcType="VARCHAR" property="desc"/>
        <result column="fileInfo" property="fileInfo" typeHandler="com.cdz360.biz.auth.ds.MybatisJsonTypeHandler"/>
        <result column="status" jdbcType="BOOLEAN" property="status"/>
        <result column="expireTime" jdbcType="TIMESTAMP" property="expireTime"/>
    </resultMap>
    <select id="getById" resultType="com.cdz360.biz.auth.subscribe.po.SubscribePo">
        select *
        from t_subscribe
        where id = #{id}
    </select>
    <sql id="queryList">
        1=1
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( title )">
            AND title LIKE concat('%', #{title}, '%')
        </if>
        <if test="status!=null">
            AND ts.status = #{status}
        </if>
        <if test="roleId!=null">
            AND tsr.roleId = #{roleId}
        </if>
        <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( commIdList )">
            and tsc.commId in
            <foreach collection="commIdList" index="index" item="item"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </sql>
    <select id="getCount" resultType="java.lang.Long">
        SELECT COUNT(*) FROM (SELECT
        DISTINCT ts.id
        FROM
        t_subscribe ts
        LEFT JOIN t_subscribe_role tsr ON ts.id = tsr.subId AND tsr.enable=true
        LEFT JOIN t_subscribe_comm tsc on ts.id = tsc.subId and tsc.enable=true
        WHERE
        <include refid="queryList"></include>
        ) tmp
    </select>
    <select id="getList" resultType="com.cdz360.biz.auth.subscribe.vo.SubscribeVo">
        SELECT
        ts.id,
        ts.title,
        count(distinct commId) as commAmount,
        (select count(*) from t_subscribe_role where subId=ts.id and enable=true) as roleAmount,
        ts.days,
        ts.fee,
        ts.status
        FROM
        t_subscribe ts
        LEFT JOIN t_subscribe_comm tsc ON ts.id = tsc.subId AND tsc.ENABLE = true
        LEFT JOIN t_subscribe_role tsr ON ts.id = tsr.subId AND tsr.ENABLE = true
        WHERE
        <include refid="queryList"></include>
        GROUP BY
        ts.id
        ORDER BY
        ts.id DESC
        limit #{start},#{size}
    </select>
    <select id="getCommList" resultType="com.cdz360.biz.auth.subscribe.vo.CommVo">
        SELECT tc.id,
               comm_name AS commName
        FROM t_subscribe_comm tsc
                 LEFT JOIN t_commercial tc ON tsc.commId = tc.id
        WHERE tsc.`enable` = TRUE
          AND tsc.subId = #{subId}
    </select>
    <select id="getRoleList" resultType="com.cdz360.biz.auth.sys.vo.SysRoleSimpleVo">
        SELECT sr.id,
               sr.name
        FROM t_subscribe_role tsr
                 LEFT JOIN sys_role sr ON tsr.roleId = sr.id
        WHERE tsr.`enable` = TRUE
          AND tsr.subId = #{subId}
    </select>
    <select id="getDetail" resultMap="FUNCTION_DETAIL">
        SELECT ts.*,
               tsc.topCommId,
               tsc.commId   AS commId,
               tsr.roleId   AS roleId,
               tc.comm_name as commName
        FROM t_subscribe ts
                 LEFT JOIN t_subscribe_comm tsc ON tsc.subId = ts.id AND tsc.ENABLE = TRUE
                 LEFT JOIN t_commercial tc on tc.id = tsc.commId
                 LEFT JOIN t_subscribe_role tsr ON tsr.subId = ts.id
            AND tsr.ENABLE = TRUE
                 JOIN sys_role sr ON sr.id = tsr.roleId
            AND sr.STATUS = TRUE
        WHERE ts.id = #{subId}
    </select>
    <select id="getListByUser" resultMap="FUNCTION_LIST">
        SELECT
        ts.*,
        max(ifnull( sur.id,0 )) AS surId,
        min( sur.expireTime ) AS expireTime
        FROM
        t_subscribe ts
        LEFT JOIN t_subscribe_role tsr ON tsr.subId = ts.id AND tsr.ENABLE =TRUE
        LEFT JOIN sys_user_role sur ON sur.role_id = tsr.roleId AND sur.ENABLE =TRUE and sur.user_id=#{sysId}
        LEFT JOIN t_subscribe_comm tsc ON tsc.subId = ts.id AND tsc.ENABLE =TRUE
        WHERE
        1=1
        <if test="commId!=null">
            AND tsc.commId = #{commId}
        </if>
        <if test="status!=null">
            AND ts.status=#{status}
        </if>
        GROUP BY
        ts.id
        <if test="status == null">
            having surId >0 and (expireTime is null or expireTime>=DATE_FORMAT(now(),'%Y-%m-%d 00:00:00'))
        </if>
        ORDER BY
        ts.id DESC;
    </select>
    <select id="getOrderById" resultType="com.cdz360.biz.model.sys.vo.SubscribeOrderVo">
        SELECT
            ts.title,
            tso.*
        FROM
            t_subscribe_order tso
                LEFT JOIN t_subscribe ts ON tso.subId = ts.id
        WHERE
            tso.payNo = #{payNo}
            LIMIT 1
    </select>
    <sql id="QUERY_LOG_LIST">
        and tso.status=1
        <if test="commIdChain!=null">
            AND tc.idChain like concat(#{commIdChain},"%")
        </if>
        <if test="userType == @com.cdz360.biz.auth.subscribe.type.UserType@APPLY_USER and @com.cdz360.base.utils.StringUtils@isNotBlank(username)">
            AND su.username like concat('%',#{username},'%')
        </if>
        <if test="userType == @com.cdz360.biz.auth.subscribe.type.UserType@OPEN_USER and @com.cdz360.base.utils.StringUtils@isNotBlank(username)">
            AND user.username like concat('%',#{username},'%')
        </if>
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(title)">
            AND ts.title like concat('%',#{title},'%')
        </if>
        <if test="payChannel!=null">
            AND tso.payChannel=#{payChannel.code}
        </if>
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(payNo)">
            AND tso.payNo like concat('%',#{payNo},'%')
        </if>
    </sql>
    <select id="getLogCount" resultType="java.lang.Long">
        SELECT count(*)
        FROM (
        SELECT tso.id
        FROM t_subscribe_order tso
        LEFT JOIN t_subscribe ts ON tso.subId = ts.id
        LEFT JOIN sys_user su ON su.id = tso.sysUid
        LEFT JOIN t_subscribe_order_user_role tsou ON tsou.payNo = tso.payNo
        LEFT JOIN sys_user USER
        ON USER.id = tsou.userId
        LEFT JOIN t_commercial tc ON tso.commId = tc.id
        WHERE
        1=1
        <include refid="QUERY_LOG_LIST"></include>
        GROUP BY tso.id
        ) tmp
    </select>
    <select id="getLogList" resultType="com.cdz360.biz.auth.subscribe.vo.SubscribeLogVo">
        SELECT tso.id,
        tso.amount,
        tso.payNo,
        tso.payChannel,
        tso.days,
        ts.title,
        su.username
        FROM t_subscribe_order tso
        LEFT JOIN t_subscribe ts ON tso.subId = ts.id
        LEFT JOIN sys_user su ON su.id = tso.sysUid
        LEFT JOIN t_subscribe_order_user_role tsou ON tsou.payNo = tso.payNo
        LEFT JOIN sys_user USER
        ON USER.id = tsou.userId
        LEFT JOIN t_commercial tc ON tso.commId = tc.id
        WHERE
        1=1
        <include refid="QUERY_LOG_LIST"></include>
        GROUP BY
        tso.id
        ORDER BY
        tso.id DESC
        LIMIT #{start}, #{size}
    </select>
    <select id="getRoleListByPayNo" resultType="com.cdz360.biz.auth.subscribe.vo.SubscribeOrderDetailVo">
        SELECT
            tsou.*,
            sr.NAME AS roleName,
            su.username
        FROM
            t_subscribe_order_user_role tsou
                LEFT JOIN sys_role sr ON sr.id = tsou.roleId
                LEFT JOIN sys_user su ON su.id = tsou.userId
        WHERE
            payNo = #{payNo}
    </select>
    <select id="getSubByTitle" resultType="com.cdz360.biz.auth.subscribe.po.SubscribePo">
        select *
        from t_subscribe
        where title=#{title}
    </select>
</mapper>
