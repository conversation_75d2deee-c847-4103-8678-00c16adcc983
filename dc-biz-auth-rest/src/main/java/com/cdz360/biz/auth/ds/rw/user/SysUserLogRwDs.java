package com.cdz360.biz.auth.ds.rw.user;

import com.cdz360.biz.auth.user.po.SysUserLogPo;
import com.cdz360.biz.auth.ds.rw.user.mapper.SysUserLogRwMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.math.BigDecimal;
import java.util.Date;

@Slf4j
@Service
public class SysUserLogRwDs {

	@Autowired
	private SysUserLogRwMapper sysUserLogRwMapper;

	public SysUserLogPo getById(Long id, boolean lock) {
		return this.sysUserLogRwMapper.getById(id, lock);
	}

	public boolean insertSysUserLog(SysUserLogPo sysUserLogPo) {
		return this.sysUserLogRwMapper.insertSysUserLog(sysUserLogPo) > 0;
	}

	public boolean updateSysUserLog(SysUserLogPo sysUserLogPo) {
		return this.sysUserLogRwMapper.updateSysUserLog(sysUserLogPo) > 0;
	}


}
