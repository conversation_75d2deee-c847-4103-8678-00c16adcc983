package com.cdz360.biz.auth.ds.rw.message.mapper;
import com.cdz360.biz.model.cus.message.po.MessagePo;
import com.cdz360.biz.model.cus.message.type.PlatformType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
@Mapper

public interface MessageRwMapper {

	/**
	 * 添加站内信
	 * @param messagePo
	 * @return
	 */
	int insertMessage(MessagePo messagePo);

	/**
	 * 阅读批量插入
	 * @param userIdList
	 * @param platform
	 * @param msgId
	 * @return
	 */
	int batchInsert(@Param("userIdList") List<Long> userIdList, @Param("platform") PlatformType platform, @Param("msgId") Long msgId);
	/**
	 * 写入阅读记录
	 * @param msgId
	 * @param uid
	 * @return
	 */
	int updateMessageReadLog(@Param("msgId") Long msgId,@Param("uid") Long uid);

	/**
	 * 站内信撤回
	 * @param msgId
	 * @return
	 */
	int editMessage(@Param("msgId") Long msgId);

	/**
	 * 消息记录表
	 * @param msgId
	 * @return
	 */
	int editMsgLog(@Param("msgId") Long msgId);

}

