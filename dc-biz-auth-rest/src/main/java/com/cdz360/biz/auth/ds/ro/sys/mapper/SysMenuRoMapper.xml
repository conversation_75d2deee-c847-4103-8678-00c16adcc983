<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.ds.ro.sys.mapper.SysMenuRoMapper">
    <select id="getMenuListByUrl" resultType="com.cdz360.biz.auth.model.vo.SysMenu">
        select * from sys_menu where status =1
        and url in
        <foreach collection="urlList" item="url"
                 open="(" separator="," close=")">
            #{url}
        </foreach>
    </select>
</mapper>