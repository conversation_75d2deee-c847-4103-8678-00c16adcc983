<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.biz.auth.ds.rw.user.mapper.CorpWxAppRwMapper">



	<resultMap id="RESULT_CORPWXAPP_PO" type="com.cdz360.biz.auth.user.po.CorpWxAppPo">

		<id column="id" jdbcType="BIGINT" property="id" />

		<result column="topCommId" jdbcType="BIGINT" property="topCommId" />

		<result column="commId" jdbcType="BIGINT" property="commId" />

		<result column="name" jdbcType="VARCHAR" property="name" />

		<result column="flagType" property="flagType" />

		<result column="appSecret" jdbcType="VARCHAR" property="appSecret" />

		<result column="appCorpId" jdbcType="VARCHAR" property="appCorpId" />

	</resultMap>



	<select id="getById"

			resultMap="RESULT_CORPWXAPP_PO">	
		select * from t_corp_wx_app where id = #{id}

		<if test="lock == true">

			for update

		</if>

	</select>



	<insert id="insertCorpWxApp" useGeneratedKeys="true" keyProperty="id"

		keyColumn="id" parameterType="com.cdz360.biz.auth.user.po.CorpWxAppPo">

		insert into t_corp_wx_app (`topCommId`,

			`commId`,

			`name`,

			`flagType`,

			`appSecret`,

			`appCorpId`)

		values (#{topCommId},

			#{commId},

			#{name},

			#{flagType},

			#{appSecret},

			#{appCorpId})

	</insert>



	<update id="updateCorpWxApp" parameterType="com.cdz360.biz.auth.user.po.CorpWxAppPo">

		update t_corp_wx_app set

		<if test="topCommId != null">

			topCommId = #{topCommId},

		</if>

		<if test="commId != null">

			commId = #{commId},

		</if>

		<if test="name != null">

			name = #{name},

		</if>

		<if test="flagType != null">

			flagType = #{flagType},

		</if>

		<if test="appSecret != null">

			appSecret = #{appSecret},

		</if>

		<if test="appCorpId != null">

			appCorpId = #{appCorpId},

		</if>

		where id = #{id}

	</update>



</mapper>

