package com.cdz360.biz.auth.rest;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.auth.config.CurrentUser;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.service.LoginService;
import com.cdz360.biz.auth.service.SysUserBizService;
import com.cdz360.biz.auth.user.dto.SysUserLoginResult;
import com.cdz360.biz.auth.user.param.BatchUserRoleParam;
import com.cdz360.biz.auth.user.param.SysUserLoginParam;
import com.cdz360.biz.auth.user.param.SysUserOpenIdParam;
import com.cdz360.biz.auth.user.po.SysUserPo;
import com.cdz360.biz.auth.user.vo.SiteGroupUserRefVo;
import com.cdz360.biz.auth.utils.IotAssert;
import com.cdz360.biz.model.common.request.TokenRequest;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.sys.param.GetYwUserParam;
import com.cdz360.biz.model.sys.vo.SiteGroupVo;
import com.cdz360.biz.model.trading.yw.type.CreateYwOrderSourceType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Tag(name = "系统帐号相关接口", description = "帐号")
public class SysUserRest {

    @Autowired
    private SysUserBizService sysUserBizService;
    @Autowired
    private LoginService loginService;


    @PostMapping("/api/sys/user/login")
    public ObjectResponse<SysUserLoginResult> login(@RequestBody @Valid SysUserLoginParam param) {
        log.info("密码-登录 -> username = {}, platform = {}", param.getUsername(), param.getPlatform());
        var result = this.sysUserBizService.doLogin(param, Boolean.TRUE);
        log.info("result = {}", result);
        return RestUtils.buildObjectResponse(result);
    }

    @PostMapping("/api/sys/user/switch")
    public ObjectResponse<SysUserLoginResult> switchUser(
        @CurrentUser SysUser user,
//            @RequestParam(value = "sysUid") Long sysUid,
        @RequestParam(value = "targetSysUid") Long targetSysUid) {
        log.info("免密切换用户 -> sysUid = {}, targetSysUid = {}", user.getId(), targetSysUid);
        var result = this.sysUserBizService.switchUser(user, targetSysUid);
        log.info("result = {}", result);
        return RestUtils.buildObjectResponse(result);
    }

    @PostMapping("/api/sys/user/getLoginTmpKey")
    public ObjectResponse<String> getLoginTmpKey(@RequestParam(value = "corpId") @Valid Long corpId,
        @RequestParam(value = "idChain") @Valid String idChain) {
        log.info("免密授权-登录 -> corpId = {}, idChain = {}", corpId, idChain);
        var result = this.sysUserBizService.authLogin(corpId, idChain);
        log.info("result = {}", result);
        return RestUtils.buildObjectResponse(result);
    }

    @PostMapping("/api/sys/user/getLoginInFoByKey")
    public ObjectResponse<SysUserLoginResult> getLoginInFoByKey(
        @RequestParam(value = "key") @Valid String key) {
        log.info("免密授权-登录 -> key = {}", key);
        var result = this.sysUserBizService.getLoginInfoByKey(key);
        log.info("result = {}", result);
        return RestUtils.buildObjectResponse(result);
    }

    @GetMapping("/api/sys/user/refreshCorpTokenValue")
    public BaseResponse refreshCorpTokenValue(
        @Valid @RequestParam(value = "username") String username) {
        log.info("刷新企业用户token, username = {}", username);
        this.sysUserBizService.refreshCorpTokenValue(username);
        return RestUtils.success();
    }

    /**
     * 通过用户ID 获取用户openID
     *
     * @return
     */
    @PostMapping("/api/sys/user/getUserByIdList")
    public ListResponse<String> getUserByIdList(@RequestBody SysUserOpenIdParam param) {
        log.info("用户idList = {}", param.getUserIdList());
        return sysUserBizService.getUserByIdList(param.getUserIdList(), param.getOpUid());
    }

    /**
     * 获取用户openId
     *
     * @param token
     * @param code
     * @return
     */
    @GetMapping("/api/sys/user/getOpenId")
    public BaseResponse getOpenId(@RequestParam("token") String token,
        @RequestParam(value = "code") String code) {
        log.info("用户信息:token={}, code = {}", token, code);
        return this.sysUserBizService.getOpenId(token, code);
    }

    /**
     * 公众号订阅绑定使用，解绑以后及时显示
     *
     * @param tokenRequest
     * @return
     */
    @PostMapping("/api/sys/user/getUserInfoById")
    public ObjectResponse<SysUserPo> getUserInfoById(
        @RequestBody @Valid TokenRequest tokenRequest) {
        log.info("token获取用户信息 ~> token:{}", tokenRequest);
        String json = loginService.getUserJson(tokenRequest.getToken());
        if (json == null) {
            log.warn("token获取用户信息 ~> token验证失败:{}", tokenRequest);
            return new ObjectResponse<>(DcConstants.KEY_RES_CODE_AUTH_ERROR, "请重新登录");
        }
        SysUser sysUser = JsonUtils.fromJson(json, SysUser.class);
        return this.sysUserBizService.getUserInfoById(sysUser.getId());
    }


    /**
     * 获取系统用户
     *
     * @param username
     * @param platform
     * @return
     */
    @PostMapping("/api/sys/user/getByUserNameAndPlatform")
    public ObjectResponse<SysUserPo> getByUserNameAndPlatform(
        @Parameter(name = "账号") @RequestParam(value = "username") String username,
        @Parameter(name = "平台类型") @RequestParam(value = "platform") AppClientType platform) {
        log.info("获取用户信息: username = {}, platform {}", username, platform);
        var res = this.sysUserBizService.getByUserNameAndPlatform(username, platform);
        return RestUtils.buildObjectResponse(res);
    }

    @PostMapping("/api/sys/user/getByUserNameLike")
    public ListResponse<SysUserPo> getByUserNameLike(
        @Parameter(name = "账号") @RequestParam(value = "username") String username) {
        log.info("获取用户信息列表: username = {}", username);
        var res = this.sysUserBizService.getByNameLike(username);
        return RestUtils.buildListResponse(res);
    }

    @Operation(summary = "获取系统用户信息")
    @PostMapping("/api/sys/user/getSysUserById")
    public ObjectResponse<SysUserPo> getSysUserById(@RequestParam(value = "id") Long id) {
        log.info("获取系统用户信息: id = {}", id);
        return this.sysUserBizService.getUserInfoById(id);
    }

    @Operation(summary = "获取系统用户信息列表")
    @PostMapping("/api/sys/user/getSysUserByIdList")
    public ListResponse<SysUserVo> getSysUserByIdList(@RequestBody List<Long> ids) {
        log.info("获取系统用户信息: id = {}", ids);
        return this.sysUserBizService.getUserInfoByIdList(ids);
    }


    @Operation(summary = "获取运维人员列表")
    @PostMapping(value = "/api/sys/user/ywUserList")
    public ListResponse<SysUserVo> ywUserList() {
        log.info("获取运维人员列表");
//        String json = loginService.getUserJson(tokenRequest.getToken());
//        if (json == null) {
//            log.warn("token获取用户信息 ~> token验证失败:{}", tokenRequest);
//            ListResponse<SysUserPo> result = RestUtils.buildListResponse(List.of());
//            result.setStatus(DcConstants.KEY_RES_CODE_AUTH_ERROR)
//                    .setError("请重新登录");
//            return result;
//        }
        return RestUtils.buildListResponse(this.sysUserBizService.ywUserList());
    }

    @Operation(summary = "获取运维人员信息")
    @PostMapping(value = "/api/sys/user/getYwUser")
    public ObjectResponse<Optional<SysUserVo>> getYwUser(@RequestBody GetYwUserParam param) {
        log.info("获取运维人员: param = {}", JsonUtils.toJsonString(param));
        return RestUtils.buildObjectResponse(
            this.sysUserBizService.getYwUser(param));
    }

    @Operation(summary = "转换运维工单来源")
    @GetMapping(value = "/api/sys/user/getYwOrderSrc")
    public ObjectResponse<CreateYwOrderSourceType> getYwOrderSrc(
        @RequestParam(value = "uid") Long uid) {
        log.info("转换运维工单来源: uid = {}", uid);
        return RestUtils.buildObjectResponse(this.sysUserBizService.getYwOrderSrc(uid));
    }

    @Operation(summary = "账号角色批量修改")
    @PostMapping(value = "/api/sys/user/batchUpdate")
    public BaseResponse batchUpdate(@CurrentUser SysUser user,
        @RequestBody BatchUserRoleParam params) {
        log.info("账号批量修改: params = {}", JsonUtils.toJsonString(params));
        IotAssert.isNotNull(user, "未登录状态");

        return this.sysUserBizService.batchUpdate(params);
    }

    @Operation(summary = "获取当前账号所属商户及子商户的账号列表")
    @GetMapping(value = "/api/sys/user/getUserListById")
    public ListResponse<SysUserVo> getUserListById(@CurrentUser SysUser user) {
        IotAssert.isNotNull(user, "未登录状态");
        IotAssert.isTrue(user.getCommIdChain() != null && user.getPlatform() != null, "账号信息不正确");
        return RestUtils.buildListResponse(
            sysUserBizService.getUserListById(user.getCommIdChain(), user.getPlatform()));
    }


    /**
     * 使用 sys_user.id获取该用户关联的场站组信息
     */
    @Operation(summary = "获取当前账号关联的场站组信息")
    @GetMapping(value = "/api/sys/user/getSiteGroupsByUid")
    public ListResponse<SiteGroupVo> getSiteGroupsByUid(@CurrentUser SysUser user,
        @RequestParam(value = "type", required = false) Integer type) {
        IotAssert.isNotNull(user, "未登录状态");
        var list = sysUserBizService.getSiteGroupsByUid(user.getId(), type);
        return RestUtils.buildListResponse(list);
    }

    @Operation(summary = "获取当前账号关联的场站组信息")
    @PostMapping(value = "/api/sys/user/getUidsByGids")
    public ListResponse<SiteGroupUserRefVo> getUidsByGids(@RequestBody List<String> gids) {
        IotAssert.isTrue(CollectionUtils.isNotEmpty(gids), "请传入参");
        List<SiteGroupUserRefVo> list = sysUserBizService.getUidsByGids(gids);
        return RestUtils.buildListResponse(list);
    }


    @Operation(summary = "获取系统用户信息")
    @PostMapping(value = "/api/sys/user/getByUid")
    public ObjectResponse<SysUserVo> getSysUserVoByUid(
        @Parameter(name = "系统用户ID") @RequestParam(value = "uid") Long uid) {
        log.info("获取系统用户信息: uid = {}", uid);
        return RestUtils.buildObjectResponse(this.sysUserBizService.getSysUserVoByUid(uid));
    }

    @Operation(summary = "获取当前账号关联的场站组信息")
    @GetMapping(value = "/api/sys/user/getSiteGroupsList")
    public ListResponse<SiteGroupVo> getSiteGroupsList(@CurrentUser SysUser user,
        @RequestParam(required = false, value = "types") List<Integer> types) {
        IotAssert.isNotNull(user, "未登录状态");
        var list = sysUserBizService.getSiteGroupsList(user.getCommIdChain(), user.getGids(), types);
        return RestUtils.buildListResponse(list);
    }
}
