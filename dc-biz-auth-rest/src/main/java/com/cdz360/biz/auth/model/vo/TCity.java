package com.cdz360.biz.auth.model.vo;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TCity
 *
 * @since 2019/5/27
 * <AUTHOR>
 */
@TableName
@Data
@EqualsAndHashCode(callSuper = true)
public class TCity extends BaseEntity implements LogicDeletable {
    private Long id;
    private String cname;
    private Long pid;
    @TableLogic(delval = "0")
    private Integer status;
}