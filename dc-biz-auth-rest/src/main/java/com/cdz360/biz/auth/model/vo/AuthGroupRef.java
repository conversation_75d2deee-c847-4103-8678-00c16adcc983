package com.cdz360.biz.auth.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * AuthGroupRef
 * 
 * @since 2020/2/10 2:53
 * <AUTHOR>
 */
@Data
@Schema(description = "权限和组关系表")
@Accessors(chain = true)
public class AuthGroupRef {
    private Long id;
    @Schema(description = "组id")
    private Long groupId;
    @Schema(description = "权限id")
    private Long authorityId;
    @Schema(description = "权限code")
    private String authorityCode;
    @Schema(description = "所属模块")
    private String module;
    @Schema(description = "操作人")
    private String opName;
    private Long opId;
    private Date createTime;
}
