<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.ds.ro.sys.mapper.SysRoleRoMapper">


    <select id="getUserRoleById" resultType="com.cdz360.biz.auth.model.vo.SysRole">
        SELECT
            role.NAME,
            role.extra,
            role.id,
            role.extra as createUser,
            role.status,
            role.create_by as createBy

        FROM
            sys_user
                USER LEFT JOIN sys_user_role user_role ON USER.id = user_role.user_id
                     LEFT JOIN sys_role role ON user_role.role_id = role.id
        WHERE
            USER.id = #{userId}
          AND role.platform = 22
          AND role.NAME = '超级管理员'
    </select>
    <select id="getRoleByIdList" resultType="com.cdz360.biz.auth.model.vo.SysRole">
        select * from sys_role where status = 1
        and id in
        <foreach collection="roleIdList" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
        <if test="platform != null">
            and platform = #{platform}
        </if>
    </select>
    <sql id="queryRoleList">
        sur.user_id = #{userId}
        AND sr.STATUS = 1
        AND sur.ENABLE = TRUE
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( keyWord )">
            AND sr.name like concat('%', #{keyWord}, '%')
        </if>
        <choose>
            <when test="searchType!= null and searchType == 'ALL'">
                AND (expireTime IS NULL OR expireTime >= DATE_FORMAT(now(),'%Y-%m-%d 00:00:00'))
            </when>
            <otherwise>
                <choose>
                    <when test="searchType!=null and searchType == 'IN_EXPIRE'">
                        <if test="startTime != null">
                            <![CDATA[ and expireTime >= #{startTime} ]]>
                        </if>
                        <if test="endTime != null">
                            <![CDATA[ and expireTime <= #{endTime} ]]>
                        </if>
                        <if test="startTime == null">
                            <![CDATA[ and expireTime >= DATE_FORMAT(now(),'%Y-%m-%d 00:00:00')]]>
                        </if>
                    </when>
                    <otherwise>
                        and expireTime is null
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
    </sql>
    <select id="getRoleAmountByUserId" resultType="java.lang.Long">
        SELECT
           count(*)
        FROM
            sys_role sr
                LEFT JOIN sys_user_role sur ON sr.id = sur.role_id
        WHERE
        <include refid="queryRoleList"></include>
    </select>
    <select id="getRoleListByUserId" resultType="com.cdz360.biz.auth.model.vo.SysRole">
        SELECT
            sr.id,
            sr.name,
            sur.expireTime
        FROM
            sys_role sr
                LEFT JOIN sys_user_role sur ON sr.id = sur.role_id
        WHERE
      <include refid="queryRoleList"></include>
        ORDER BY
            sr.id DESC
            LIMIT #{start},#{size}
    </select>
    <select id="getRoleByUserId" resultType="com.cdz360.biz.auth.model.vo.SysRole">
        SELECT
            id,
            name
        FROM
            sys_role sr
                LEFT JOIN (
                SELECT DISTINCT role_id FROM sys_user_role WHERE user_id = #{userId} AND ENABLE = TRUE AND (expireTime IS NULL OR expireTime >=DATE_FORMAT(now(),'%Y-%m-%d 00:00:00'))) tmp ON tmp.role_id = sr.id
        WHERE
            STATUS = 1
          AND platform in
          <foreach collection="platformList" item="item" open="(" separator="," close=")">
                #{item}
          </foreach>
          AND tmp.role_id IS NULL
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( keyWord )">
            and sr.name like CONCAT(#{keyWord}, '%')
        </if>
        <if test="size!=null">
            limit #{size}
        </if>
    </select>
</mapper>