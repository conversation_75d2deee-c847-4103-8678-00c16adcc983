package com.cdz360.biz.auth.config;

import com.cdz360.biz.model.common.constant.DcBizConstants;
import com.cdz360.data.sync.constant.DcMqConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.FanoutExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class MqListenerConfig {


    @Bean
    public Queue sysUserLogQueue() {
        return new Queue(DcBizConstants.MQ_QUEUE_AUTH_SYS_USER_LOG, true, false, false);
    }

    @Bean
    public FanoutExchange exchangeSysUserLog() {
        return new FanoutExchange(DcMqConstants.MQ_EXCHANGE_NAME_SYS_USER_LOG, true, false);
    }

    @Bean
    public Binding bindingExchangeSysUserLog(Queue sysUserLogQueue, FanoutExchange exchangeSysUserLog) {

        return BindingBuilder.bind(sysUserLogQueue).to(exchangeSysUserLog);
    }
}
