<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.biz.auth.ds.ro.user.mapper.SiteGroupRoMapper">


  <resultMap id="RESULT_SITEGROUP_VO" extends="RESULT_SITEGROUP_PO"
    type="com.cdz360.biz.model.sys.vo.SiteGroupVo">
    <result column="adminName" property="adminName"/>
    <result column="linkAccountCount" property="linkAccountCount"/>
  </resultMap>

  <resultMap id="RESULT_SITEGROUP_PO" type="com.cdz360.biz.model.sys.po.SiteGroupPo">
    <result column="gid" jdbcType="VARCHAR" property="gid"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="adminUid" jdbcType="BIGINT" property="adminUid"/>
    <result column="type" typeHandler="com.cdz360.ds.type.EnumTypeHandler" property="type"/>
    <result column="ownType" typeHandler="com.cdz360.ds.type.EnumTypeHandler" property="ownType"/>
    <result column="priority" jdbcType="BIGINT" property="priority"/>
    <result column="enable" property="enable"/>
  </resultMap>

  <resultMap id="USER_SITE_GROUP" type="com.cdz360.biz.model.sys.dto.UserOwnerSiteGroupDto">
    <result column="uid" property="uid"/>
    <result column="userName" jdbcType="VARCHAR" property="userName"/>

    <collection property="groupVoList" ofType="com.cdz360.biz.model.sys.vo.SiteGroupVo">
      <result column="gid" jdbcType="VARCHAR" property="gid"/>
      <result column="name" jdbcType="VARCHAR" property="name"/>
      <result column="adminUid" jdbcType="BIGINT" property="adminUid"/>
      <result column="type" typeHandler="com.cdz360.ds.type.EnumTypeHandler" property="type"/>
      <result column="enable" property="enable"/>
    </collection>
  </resultMap>

  <resultMap id="RESULT_GROUP_USER" type="com.cdz360.biz.model.sys.vo.SiteGroupVo">
    <id column="gid" property="gid"/>
    <result column="gid" jdbcType="VARCHAR" property="gid"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <collection property="userNameList" ofType="String">
      <result column="username"/>
    </collection>
    <collection property="corpWxUidList" ofType="String">
      <result column="corpWxUid"/>
    </collection>
  </resultMap>


  <select id="getOneByName"
    resultMap="RESULT_SITEGROUP_PO">
    select * from t_site_group where name = #{name}
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(exGid)">
      and gid != #{exGid}
    </if>
    limit 1
  </select>

  <select id="getByGid"
    resultMap="RESULT_SITEGROUP_PO">
    select * from t_site_group where gid = #{gid}
  </select>

  <sql id="SEARCH_SQL">
    <where>
      <if test="platform != null">
        and g.platform = #{platform}
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(nameLike)">
        and g.name like concat('%', #{nameLike}, '%')
      </if>
      <if test="enable != null">
        and g.enable = #{enable}
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(typeList)">
        and g.`type` in
        <foreach collection="typeList" item="item" separator="," open="(" close=")">
          #{item.code}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(ownTypeList)">
        and g.`ownType` in
        <foreach collection="ownTypeList" item="item" separator="," open="(" close=")">
          #{item.code}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(gidList)">
        and g.`gid` in
        <foreach collection="gidList" item="gid" separator="," open="(" close=")">
          #{gid}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(exGidList)">
        and g.`gid` not in
        <foreach collection="exGidList" item="gid" separator="," open="(" close=")">
          #{gid}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(uidList)">
        and ref.uid in
        <foreach collection="uidList" item="uid" separator="," open="(" close=")">
          #{uid}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(userPhoneOrAccountLike)">
        and ((su.phone like concat('%', #{userPhoneOrAccountLike}, '%')) or
        (su.username like concat('%', #{userPhoneOrAccountLike}, '%')))
      </if>
    </where>
  </sql>

  <select id="findAll"
    parameterType="com.cdz360.biz.model.sys.param.ListSiteGroupParam"
    resultMap="RESULT_SITEGROUP_VO">
    select g.*
    , ad.name adminName
    , (select count(*) from t_site_group_user_ref r where r.gid = g.gid) linkAccountCount
    from t_site_group g
    left join t_site_group_user_ref ref on ref.gid = g.gid
    left join sys_user ad on ad.id = g.adminUid and ad.platform = #{platform}
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(userPhoneOrAccountLike)">
      left join sys_user su on su.id = ref.uid
    </if>
    <include refid="SEARCH_SQL"/>
    group by g.gid
    <choose>
      <when test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( sorts )">
        <foreach item="sort" collection="sorts"
          open="order by" separator="," close=" ">
          ${sort.columnsString} ${sort.order}
        </foreach>
      </when>
      <otherwise>
        order by g.createTime desc
      </otherwise>
    </choose>
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <otherwise>
        limit #{size}
      </otherwise>
    </choose>
  </select>
  <select id="count"
    parameterType="com.cdz360.biz.model.sys.param.ListSiteGroupParam"
    resultType="java.lang.Long">
    select count(distinct g.gid) from t_site_group g
    left join t_site_group_user_ref ref on ref.gid = g.gid
    left join sys_user ad on ad.id = g.adminUid and ad.platform = 21
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(userPhoneOrAccountLike)">
      left join sys_user su on su.id = ref.uid
    </if>
    <include refid="SEARCH_SQL"/>
  </select>

  <select id="userOwnerSiteGroup"
    parameterType="com.cdz360.biz.model.sys.param.ListSiteGroupParam"
    resultMap="USER_SITE_GROUP">
    select sgu.uid, su.name userName, sg.*
    from t_site_group sg
    left join t_site_group_user_ref sgu on sgu.gid = sg.gid
    left join sys_user su on su.id = sgu.uid
    where sgu.uid in
    <foreach collection="uidList" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(typeList)">
      and sg.`type` in
      <foreach collection="typeList" item="item" separator="," open="(" close=")">
        #{item.code}
      </foreach>
    </if>
  </select>

  <select id="getUserOwnerSiteGroup"
    parameterType="com.cdz360.biz.model.sys.param.ListSiteGroupParam"
    resultMap="USER_SITE_GROUP">
    select su.id, su.name userName, sg.*
    from sys_user su
    left join t_site_group_user_ref sgu on su.id = sgu.uid
    left join t_site_group sg on sgu.gid = sg.gid
    <if test="null != groupType">
      and sg.`type` = #{groupType.code}
    </if>
    where su.id = #{uid}
  </select>
  <select id="findList" resultType="com.cdz360.biz.model.sys.vo.UserGroupVo">
    SELECT
    su.id,
    su.`name`,
    GROUP_CONCAT( g.NAME ) as groupStr
    FROM
    sys_user su
    JOIN t_site_group_user_ref ref ON su.id = ref.uid
    JOIN t_site_group g ON g.gid = ref.gid
    <where>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( userNameLike )">
        su.`name` = #{userNameLike}
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(nameLike)">
        and g.name like concat('%', #{nameLike}, '%')
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(ownTypeList)">
        and g.`ownType` in
        <foreach collection="ownTypeList" item="item" separator="," open="(" close=")">
          #{item.code}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(gidList)">
        and g.`gid` in
        <foreach collection="gidList" item="gid" separator="," open="(" close=")">
          #{gid}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(uidList)">
        and su.`id` in
        <foreach collection="uidList" item="uid" separator="," open="(" close=")">
          #{uid}
        </foreach>
      </if>
    </where>
    GROUP BY
    su.id
  </select>
  <select id="findSiteGroupAndUser" resultType="com.cdz360.biz.model.sys.vo.SiteGroupVo"
    resultMap="RESULT_GROUP_USER">
    SELECT
    tmp.gid, tmp.`name`, su.`name` AS username, su.corpWxUid
    FROM
    (
    SELECT
    ts.gid, ts.`name`
    FROM
    t_site_group ts
    WHERE
    ts.`enable` = TRUE
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(nameLike)">
      and ts.name like concat('%', #{nameLike}, '%')
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(gidList)">
      and ts.`gid` in
      <foreach collection="gidList" item="gid" separator="," open="(" close=")">
        #{gid}
      </foreach>
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(typeList)">
      and ts.`type` in
      <foreach collection="typeList" item="item" separator="," open="(" close=")">
        #{item.code}
      </foreach>
    </if>
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <otherwise>
        limit #{size}
      </otherwise>
    </choose>
    ) tmp
    LEFT JOIN t_site_group_user_ref gu ON tmp.gid = gu.gid
    LEFT JOIN sys_user su ON su.id = gu.uid
    AND su.STATUS = TRUE
  </select>
  <select id="getSiteGroupVoByGid" resultMap="RESULT_SITEGROUP_VO">
    select g.*
    , ad.name adminName
    , (select count(*) from t_site_group_user_ref r where r.gid = g.gid) linkAccountCount
    from t_site_group g
    left join sys_user ad on ad.id = g.adminUid and ad.platform = 21
    where g.gid = #{gid}
  </select>
  <select id="getSiteGroupList" resultMap="RESULT_SITEGROUP_PO">
    select * from t_site_group
    where enable = true
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(gidList)">
      and gid in
      <foreach collection="gidList" item="gid" separator="," open="(" close=")">
        #{gid}
      </foreach>
    </if>
  </select>

  <select id="getHighProrityAdminUid" resultType="java.lang.Long">
    select
      adminUid
    from
      t_site_group
    where
      `enable` = TRUE
      and `type` in
    <foreach collection="typeList" item="item" separator="," open="(" close=")">
      #{item.code}
    </foreach>
      and gid in
    <foreach collection="gidList" item="gid" separator="," open="(" close=")">
      #{gid}
    </foreach>
    order by priority desc
    limit 1
  </select>

</mapper>

