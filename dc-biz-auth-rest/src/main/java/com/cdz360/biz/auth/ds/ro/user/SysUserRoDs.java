package com.cdz360.biz.auth.ds.ro.user;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.auth.ds.ro.user.mapper.SysUserRoMapper;
import com.cdz360.biz.auth.sys.param.QuerySysUserRequest;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.sys.param.RoleUserListParam;
import com.cdz360.biz.auth.sys.vo.RoleUserVo;
import com.cdz360.biz.auth.user.po.SysUserPo;
import com.cdz360.biz.model.cus.message.type.PlatformType;
import com.cdz360.biz.model.cus.param.SysUserCheckParam;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SysUserRoDs {

    @Autowired
    private SysUserRoMapper sysUserRoMapper;

    public SysUserPo getByUsername(String username, Integer platform) {
        return this.sysUserRoMapper.getByUsername(username, platform);
    }

    public SysUserPo getByUseId(Long userId) {
        return this.sysUserRoMapper.getByUseId(userId);
    }

    public SysUserVo getByUseId2(Long userId, boolean valid) {
        List<SysUserVo> poList = this.sysUserRoMapper.findByUserId(List.of(userId), valid);
        if (CollectionUtils.isEmpty(poList)) {
            throw new DcArgumentException("用户ID无效");
        }

        return poList.get(0);
    }

    public SysUserPo getByOpenId(String openId) {
        return this.sysUserRoMapper.getByOpenId(openId);
    }

    public List<SysUserPo> getUserIdList(List<Long> commIdList, PlatformType platform, int start,
        int size) {
        return this.sysUserRoMapper.getUserIdList(commIdList, platform, start, size);
    }

    public List<String> getUserByIdList(List<Long> userIdList, Long opUid) {
        return this.sysUserRoMapper.getUserByIdList(userIdList, opUid);
    }

    public List<SysUserPo> getUserByIdAndPlatform(List<Long> userIdList, Long platform) {
        return this.sysUserRoMapper.getUserByIdAndPlatform(userIdList, platform);
    }

    /**
     * 系统续费提醒，发送给企业
     *
     * @param commIdList
     * @param platform
     * @param start
     * @param size
     * @return
     */
    public List<SysUserPo> getUserIdListForCorp(List<Long> commIdList, PlatformType platform,
        Long corpId, int start, int size) {
        return this.sysUserRoMapper.getUserIdListForCorp(commIdList, platform, corpId, start, size);
    }

    public List<SysUserVo> findByUserId(List<Long> userIdList, boolean valid) {
        if (CollectionUtils.isEmpty(userIdList)) {
            return List.of();
        }

        return this.sysUserRoMapper.findByUserId(userIdList, valid);
    }

    public Long countByCondition(Long sysUid, Integer status) {
        return this.sysUserRoMapper.countByCondition(sysUid, status);
    }

    public SysUserPo getByUserNameAndPlatform(String username, AppClientType platform) {
        return this.sysUserRoMapper.getByUserNameAndPlatform(username, platform);
    }

    public List<SysUserPo> getByNameLike(String username) {
        return this.sysUserRoMapper.getByNameLike(username);
    }

    public Long getRoleUserAmount(RoleUserListParam params) {
        return this.sysUserRoMapper.getRoleUserAmount(params);
    }

    public List<RoleUserVo> getRoleUserList(RoleUserListParam params) {
        return this.sysUserRoMapper.getRoleUserList(params);
    }

    public List<RoleUserVo> getUserByRoleId(String keyWord, Long platform, Long roleId, Long size) {
        return this.sysUserRoMapper.getUserByRoleId(keyWord, platform, roleId, size);
    }

    public List<SysUserVo> getUserListById(String commIdChain, Integer platform) {
        return this.sysUserRoMapper.getUserListById(commIdChain, platform);
    }

    public List<Long> groupIdByKeyWord(String keyWord) {
        return this.sysUserRoMapper.groupIdByKeyWord(keyWord);
    }

    public List<Long> uidByUname(String uname) {
        return this.sysUserRoMapper.uidByUname(uname);
    }

    public List<Long> uidByTeamCatalog(String teamCatalog, String uname) {
        return this.sysUserRoMapper.uidByTeamCatalog(teamCatalog, uname);
    }

    public List<Long> uidByTeamCatalogList(List<String> teamCatalogList,
        String uname) {
        return this.sysUserRoMapper.uidByTeamCatalogList(teamCatalogList, uname);
    }

    public List<Long> sameCorpWxAppNameUids(Long uid) {
        return this.sysUserRoMapper.sameCorpWxAppNameUids(uid);
    }

    public List<SysUserPo> queryByCheckParam(List<SysUserCheckParam> list) {
        return this.sysUserRoMapper.queryByCheckParam(list);
    }

    // 用于桩管家一键登录
    public SysUserPo getOneByPhoneAndPlatform(Long topCommId, String phone, Integer platform) {
        return this.sysUserRoMapper.getOneByPhoneAndPlatform(topCommId, phone, platform);
    }

    public List<SysUser> getUserSimpleList(QuerySysUserRequest param) {
        return this.sysUserRoMapper.getUserSimpleList(param);
    }

    public SysUser selectById(Long id) {
        return this.sysUserRoMapper.selectById(id);
    }
}
