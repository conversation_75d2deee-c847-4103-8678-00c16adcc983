package com.cdz360.biz.auth.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cdz360.biz.auth.dao.SysUserRoleDao;
import com.cdz360.biz.auth.model.vo.SysUserRole;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SysUserRoleService extends ServiceImpl<SysUserRoleDao, SysUserRole> {
    @Transactional(rollbackFor = Exception.class)
    public boolean insertRoleIdList(Long userId, List<Long> roleIdList) {
        Integer result = baseMapper.insertUserRoleIdList(userId, roleIdList);
        return null != result && result >= 1;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean updateRoleIdList(Long userId, List<Long> roleIdList) {
        log.info("userId = {}, roleIds = {}", userId, roleIdList);
        boolean res = false;
        if (roleIdList != null) {
            List<Long> old = findRoleIdListByUserId(userId);
            List<Long> toBeAdd = roleIdList.stream().filter(rid -> !old.contains(rid)).collect(Collectors.toList());
            List<Long> toBeDelete = old.stream().filter(rid -> !roleIdList.contains(rid)).collect(Collectors.toList());
            if (!toBeAdd.isEmpty()) {
                log.info("增加用户权限. userId = {}, roleIds = {}", userId, toBeAdd);
                Integer integer = baseMapper.insertUserRoleIdList(userId, toBeAdd);
                res = integer != null && integer == roleIdList.size();
            }
            if (!toBeDelete.isEmpty()) {
                QueryWrapper<SysUserRole> wrapper = Wrappers.query();
                wrapper.eq("user_id", userId).in("role_Id", toBeDelete);
                int del = super.getBaseMapper().delete(wrapper);
//                boolean del = delete(Condition.create().eq("user_id", userId).in("role_Id", toBeDelete));
                res = res && del > 0;
            }
        }
        return res;
    }

    public List<Long> findRoleIdListByUserId(Long userId) {
        return baseMapper.findRoleIdListByUserId(userId);
    }

    /**
     * 查询可用的角色，以便获取用户权限
     * @param userId
     * @return
     */
    public List<Long> findEnabledRoleIdListByUserId(Long userId) {
        return baseMapper.findRoleIdListByUserIdAndStatus(userId, 1);
    }

    public Long findRoleIdByUserId(Long userId) {
        return baseMapper.findRoleIdByUserId(userId);
    }

    public Long findOrgIdByUserId(Long userId) {
        return baseMapper.findOrgIdByUserId(userId);
    }

    public List<String> findRoleNameListByUserId(Long userId) {
        return baseMapper.findRoleNameListByUserId(userId);
    }

    public List<Long> findUserIdByRoleId(Long roleId) {
        return baseMapper.findUserIdByRoleId(roleId);
    }
}
