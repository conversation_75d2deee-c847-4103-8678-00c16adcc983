package com.cdz360.biz.auth.interceptor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import jakarta.servlet.http.HttpServletRequest;

/**
 * ExceptionInterceptor
 *
 * @since 3/7/2020 10:29 AM
 * <AUTHOR>
 */
@Slf4j
@ControllerAdvice
public class AuthExceptionInterceptor {

    @ExceptionHandler({DcException.class})
    @ResponseBody
    public BaseResponse handleDcException(DcException ex) {
        log.warn(ex.getMessage(), ex);
        BaseResponse result = new BaseResponse();
        result.setStatus(ex.getStatus());
        result.setError(ex.getMessage());
        return result;
    }

    @ExceptionHandler({IllegalArgumentException.class})
    @ResponseBody
    public BaseResponse handleAssertException(IllegalArgumentException ex) {
        log.warn(ex.getMessage(), ex);
        BaseResponse result = new BaseResponse();
        result.setStatus(DcConstants.KEY_RES_CODE_ARGUMENT_ERROR);
        result.setError(ex.getMessage());
        return result;
    }

    @ExceptionHandler({MaxUploadSizeExceededException.class})
    @ResponseBody
    public BaseResponse handleMaxUploadSizeException(MaxUploadSizeExceededException ex) {
        log.warn(ex.getMessage(), ex);
        BaseResponse result = new BaseResponse();
        result.setStatus(DcConstants.KEY_RES_CODE_SERVICE_ERROR);
        result.setError("文件大小超出最大限制！");
        return result;
    }

//    @ExceptionHandler({MethodArgumentNotValidException.class})
//    @ResponseBody
//    public BaseResponse handleAssertException(MethodArgumentNotValidException ex) {
//        log.warn(ex.getBindingResult().getFieldError().getDefaultMessage(), ex);
//        BaseResponse result = new BaseResponse();
//        result.setStatus(DcConstants.KEY_RES_CODE_ARGUMENT_ERROR);
//        result.setError(ex.getBindingResult().getFieldError().getDefaultMessage());
//        return result;
//    }

    /**
     * 处理validate相关的异常
     *
     * @param ex
     * @return
     */
    @ExceptionHandler({MethodArgumentNotValidException.class})
    @ResponseBody
    public BaseResponse handleValidateExcetion(MethodArgumentNotValidException ex) {
        log.warn(ex.getMessage(), ex);
        String errorMsg;
        FieldError fieldError = ex.getBindingResult().getFieldError();
        if (fieldError != null) {
            errorMsg = fieldError.getDefaultMessage();
        } else {
            errorMsg = "参数错误..";
        }

        BaseResponse result = new BaseResponse();
        result.setStatus(DcConstants.KEY_RES_CODE_ARGUMENT_ERROR);
        result.setError(errorMsg);
        return result;
    }

    @ExceptionHandler
    @ResponseBody
    public BaseResponse handle(Exception ex, HttpServletRequest req) {
        log.error("exception message = {}, url = {}",
                ex.getMessage(), req.getRequestURI(), ex);
        BaseResponse result = new BaseResponse();
        result.setStatus(DcConstants.KEY_RES_CODE_UNKNOWN_ERROR);
        result.setError("系统错误");
        if (ex instanceof IllegalArgumentException) {
            // Assert 失败的异常
            result.setStatus(2000);
            result.setError(ex.getMessage());
        } else {
            log.error("error = {}", ex.getMessage(), ex);
        }
        // TODO: 需要按异常类型做处理, 返回不同的status和error

        return result;
    }

    @ExceptionHandler({DcArgumentException.class})
    @ResponseBody
    public BaseResponse handle(DcArgumentException ex) {
        log.error(ex.getMessage(), ex);
        BaseResponse result = new BaseResponse();
        result.setStatus(DcConstants.KEY_RES_CODE_UNKNOWN_ERROR);
        result.setError("系统错误");
        if (ex instanceof DcArgumentException) {
            // Assert 失败的异常
            result.setStatus(ex.getStatus());
            result.setError(ex.getMessage());
        }
        // TODO: 需要按异常类型做处理, 返回不同的status和error

        return result;
    }


}