package com.cdz360.biz.auth.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;
import java.util.UUID;

/**
 * 充值记录Id生成器
 *
 * <AUTHOR>
 * @since 2019/11/11 19:48
 */
@Slf4j
@Service
public class SubOrderIdGenerator {
    private static final String KEY_REDIS_PAY_BILL_IDX = "sys:paybill:idx";
    private static final String KEY_REDIS_PAY_BILL_ORDER_IDX = "sys:paybill:order:idx";
    private static final String REDIS_PAY_BILL_HASH = "sys:paybill:idx";

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    public String getNextPayBillId() {
        String pattern = "yyyyMMddHHmmss";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        String ret;
        synchronized (this) {
            String date = simpleDateFormat.format(new Date());

            Long val = stringRedisTemplate.opsForValue().increment(KEY_REDIS_PAY_BILL_ORDER_IDX, 1L);
            ret = String.format("%s%04d", date, val % 999);
        }
        return ret;
    }

    public Long getNextId() {
        String pattern = "yyMMddHHmm";
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
        String date = simpleDateFormat.format(new Date());
        try {
            Long res = stringRedisTemplate.opsForHash().increment(KEY_REDIS_PAY_BILL_IDX, REDIS_PAY_BILL_HASH, 1L);
            // Long.MAX_VALUE: 9223372036854775807L;
            return Long.valueOf(String.format("%s%04d", date, res % 10000));
        } catch (Exception e) { // redis宕机时采用uuid的方式生成唯一id
            log.warn("err={}", e.getMessage(), e);
            int first = new Random(10).nextInt(8) + 1;
            int randNo = UUID.randomUUID().toString().hashCode();
            if (randNo < 0) {
                randNo = -randNo;
            }
            return Long.valueOf(first + String.format("%016d", randNo));
        }
    }
}
