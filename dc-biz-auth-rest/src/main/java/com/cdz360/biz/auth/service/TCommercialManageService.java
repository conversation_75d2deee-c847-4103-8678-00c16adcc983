package com.cdz360.biz.auth.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.app.vo.AppCfg;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.auth.dao.TCommercialDao;
import com.cdz360.biz.auth.dao.TCommercialManageDao;
import com.cdz360.biz.auth.model.param.TCommercialRequest;
import com.cdz360.biz.auth.model.vo.Commercial;
import com.cdz360.biz.auth.model.vo.TCommercialManage;
import com.cdz360.biz.auth.sys.po.AppVersionPo;
import com.cdz360.biz.auth.utils.IotAssert;
import com.cdz360.data.cache.RedisAppRwService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * TCommercialManageService
 *  TODO
 * @since 2019/10/12
 * <AUTHOR>
 */
@Slf4j
@Service
public class TCommercialManageService extends ServiceImpl<TCommercialManageDao, TCommercialManage> {

    @Autowired
    private RedisAppRwService redisAppRwService;
    @Autowired
    private TCommercialDao tCommercialDao;
    @Autowired
    private AppVersionService appVersionService;

    @Value("${commercial.manage.invoiceUrl:}")
    private String invoiceUrl;

    public void appCfg2Redis() {
        log.info("appCfg2Redis-start");
        int page = 1;
        final int size = 10;
        QueryWrapper<TCommercialManage> condition = Wrappers.query();
        condition.orderBy(true, true, "id");
        IPage<TCommercialManage> tCommercialManagePage = this.getBaseMapper().selectPage(new Page<>((page - 1) * size, size), condition);
        List<TCommercialManage> list = tCommercialManagePage.getRecords();
        log.info("list: {}", JsonUtils.toJsonString(list));
        if (CollectionUtils.isNotEmpty(list)) {
            do {
                list.stream().forEach(e -> {
                    setAppcfg(e);
                });
                page++;
                list = this.getBaseMapper().selectPage(new Page<>((page - 1) * size, size), condition).getRecords();
                log.info("list: {}", JsonUtils.toJsonString(list));
            } while (CollectionUtils.isNotEmpty(list));
        }
        log.info("appCfg2Redis-end");
    }

    /**
     * 配置信息写入redis
     *
     * @param tCommercialManage
     */
    private void setAppcfg(TCommercialManage tCommercialManage) {
        AppCfg appCfg = new AppCfg();
        appCfg.setAppName(tCommercialManage.getAppName());
        appCfg.setServiceTel(tCommercialManage.getServiceTel());
        appCfg.setAppCommId(tCommercialManage.getComId());
        appCfg.setMinChargeAmount(tCommercialManage.getMinChargeAmount());
        appCfg.setMinPrepayAmount(tCommercialManage.getMinPrepayAmount());
        appCfg.setInvoiceUrl(tCommercialManage.getInvoinceUrl());
        appCfg.setInvoiceDesc(tCommercialManage.getInvoiceDesc());

        if (StringUtils.isNotBlank(tCommercialManage.getWxAppid())) {
            appCfg.setAppId(tCommercialManage.getWxAppid());
            appCfg.setAppType(AppClientType.WX_LITE);
            AppVersionPo iosApp = this.appVersionService.getAppVersion(tCommercialManage.getComId(), AppClientType.WX_LITE);
            if (iosApp != null) {
                appCfg.setMinAppVer(iosApp.getMinVer());
                appCfg.setAppVer(iosApp.getCurVer());
                appCfg.setReleaseNote(iosApp.getReleaseNote());
                appCfg.setAppPkgUrl(iosApp.getPkgUrl());
            }
            redisAppRwService.updateAppCfg(appCfg);
        }
        if (StringUtils.isNotBlank(tCommercialManage.getAlipayAppletAppId())) {
            appCfg.setAppId(tCommercialManage.getAlipayAppletAppId());
            appCfg.setAppType(AppClientType.ALIPAY_LITE);
            AppVersionPo iosApp = this.appVersionService.getAppVersion(tCommercialManage.getComId(), AppClientType.ALIPAY_LITE);
            if (iosApp != null) {
                appCfg.setMinAppVer(iosApp.getMinVer());
                appCfg.setAppVer(iosApp.getCurVer());
                appCfg.setReleaseNote(iosApp.getReleaseNote());
                appCfg.setAppPkgUrl(iosApp.getPkgUrl());
            }
            redisAppRwService.updateAppCfg(appCfg);
        }

        AppVersionPo iosApp = this.appVersionService.getAppVersion(tCommercialManage.getComId(), AppClientType.IOS_APP);
        if (iosApp != null) {
            appCfg.setAppId(formatAppId(tCommercialManage.getComId(), AppClientType.IOS_APP));
            appCfg.setAppType(AppClientType.IOS_APP);
            appCfg.setAppVer(iosApp.getCurVer());
            appCfg.setMinAppVer(iosApp.getMinVer());
            appCfg.setReleaseNote(iosApp.getReleaseNote());
            appCfg.setAppPkgUrl(iosApp.getPkgUrl());
            redisAppRwService.updateAppCfg(appCfg);
        }

        AppVersionPo androidApp = this.appVersionService.getAppVersion(tCommercialManage.getComId(), AppClientType.ANDROID_APP);
        if (androidApp != null) {
            appCfg.setAppId(formatAppId(tCommercialManage.getComId(), AppClientType.ANDROID_APP));
            appCfg.setAppType(AppClientType.ANDROID_APP);
            appCfg.setAppVer(androidApp.getCurVer());
            appCfg.setMinAppVer(androidApp.getMinVer());
            appCfg.setReleaseNote(androidApp.getReleaseNote());
            appCfg.setAppPkgUrl(androidApp.getPkgUrl());
            redisAppRwService.updateAppCfg(appCfg);
        }
    }

    private String formatAppId(Long topCommId, AppClientType appType) {
        StringBuilder buf = new StringBuilder();
        buf.append(topCommId).append("-").append(appType.name());
        return buf.toString();
    }

    public TCommercialManage getTCommercialManage(Long commId) {

        log.info("查找商户: {}", commId);

        QueryWrapper<TCommercialManage> wrapper = Wrappers.query();
        wrapper.eq("com_id", commId);

        List<TCommercialManage> list = this.getBaseMapper().selectList(wrapper);
        log.info("查找商户list: {}", list.size());

        if (CollectionUtils.isNotEmpty(list)) {
            if (list.size() > 1) {
                log.warn("查找到多个商户信息，这里取第一个。");
            }

            TCommercialManage tCommercialManage = list.get(0);
            tCommercialManage.setInvoinceEnabled(!StringUtils.isBlank(tCommercialManage.getInvoinceUrl()));
            return tCommercialManage;
        } else {
            return null;
        }
    }

    public void setTCommercialManage(TCommercialManage tCommercialManage) {
        log.info("修改商户信息tCommercialManage: {}", tCommercialManage);

        IotAssert.isTrue(tCommercialManage.getId() != null, "修改商户信息，id不能为空。");

        if (Boolean.TRUE.equals(tCommercialManage.getInvoinceEnabled())) {
            tCommercialManage.setInvoinceUrl(invoiceUrl);
        } else if (Boolean.FALSE.equals(tCommercialManage.getInvoinceEnabled())) {
            tCommercialManage.setInvoinceUrl("");
        }

        boolean ret = this.updateById(tCommercialManage);
        IotAssert.isTrue(ret, "修改商户配置信息，该商户未配置客户端。id" + tCommercialManage.getId());

        setAppcfg(this.getBaseMapper().selectById(tCommercialManage.getId()));
    }

    public List<Commercial> findByCommIdListAndName(TCommercialRequest request) {
        return tCommercialDao.findByCommIdListAndName(request.getCommIdList(), request.getCommName());
    }
}