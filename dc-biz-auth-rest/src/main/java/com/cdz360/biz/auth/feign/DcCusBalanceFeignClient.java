package com.cdz360.biz.auth.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.auth.corp.po.ListPointParam;
import com.cdz360.biz.auth.sys.po.PointPo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 */
@FeignClient(value = DcConstants.KEY_FEIGN_DC_BIZ_CUS_BALANCE, fallbackFactory = DcCusBalanceFeignClientHystrix.class)
public interface DcCusBalanceFeignClient {

//    /**
//     * 获取积分信息
//     *
//     * @param type
//     * @param uid
//     * @return
//     */
//    @RequestMapping(value = "/hyena/point/getPoint", method = RequestMethod.GET)
//    ObjectResponse<PointPo> getPoint(@RequestParam(value = "type") String type,
//                                     @RequestParam(value = "uid") String uid,
//                                     @RequestParam(value = "subUid") String subUid);

    /**
     * 获取积分列表
     *
     * @param param
     * @return
     */
    @RequestMapping(value = "/hyena/point/listPoint", method = RequestMethod.POST)
    ListResponse<PointPo> listPoint(@RequestBody ListPointParam param);

}

