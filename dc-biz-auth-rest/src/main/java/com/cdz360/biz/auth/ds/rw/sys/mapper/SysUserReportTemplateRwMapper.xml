<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.ds.rw.sys.mapper.SysUserReportTemplateRwMapper">

    <insert id="insert">
        INSERT INTO sys_user_report_template (
            `templateName`,
            `sysUserId`,
            `page`,
            `queryConditions`,
            `columns`,
            `createTime`)
        VALUES (
            #{templateName},
            #{sysUserId},
            #{page.code},
            #{queryConditions, typeHandler=com.cdz360.biz.auth.ds.MybatisJsonTypeHandler},
            #{columns, typeHandler=com.cdz360.biz.auth.ds.MybatisJsonTypeHandler},
            now()
        )
    </insert>

    <delete id="delete">
        delete from
            sys_user_report_template
        where id = #{templateId}
    </delete>

</mapper>