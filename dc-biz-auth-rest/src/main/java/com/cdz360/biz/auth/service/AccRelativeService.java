package com.cdz360.biz.auth.service;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.ds.ro.sys.AccRelativeRoDs;
import com.cdz360.biz.auth.ds.ro.user.SiteGroupUserRoDs;
import com.cdz360.biz.auth.ds.ro.user.SysUserRoDs;
import com.cdz360.biz.auth.ds.rw.sys.AccRelativeRwDs;
import com.cdz360.biz.auth.ds.rw.user.SiteGroupUserRwDs;
import com.cdz360.biz.auth.feign.DataCoreFeignClient;
import com.cdz360.biz.auth.model.constant.GlobalConst;
import com.cdz360.biz.auth.model.vo.SysRole;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.sys.param.AccRelativeParam;
import com.cdz360.biz.auth.sys.param.AddAccRelativeParam;
import com.cdz360.biz.auth.sys.param.YwUserParam;
import com.cdz360.biz.auth.sys.po.AccRelativePo;
import com.cdz360.biz.auth.sys.vo.AccRelativeVo;
import com.cdz360.biz.auth.sys.vo.RelAccount;
import com.cdz360.biz.auth.sys.vo.SysRoleSimpleVo;
import com.cdz360.biz.auth.utils.IotAssert;
import com.cdz360.biz.auth.utils.TokenGenerator;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.sys.constant.SiteGroupType;
import com.cdz360.biz.model.sys.vo.AccRelativeOrderVo;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AccRelativeService {

    @Autowired
    private SiteGroupUserRoDs siteGroupUserRoDs;

    @Autowired
    private SiteGroupUserRwDs siteGroupUserRwDs;

    @Autowired
    private AccRelativeRoDs accRelativeRoDs;
    @Autowired
    private AccRelativeRwDs accRelativeRwDs;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysUserRoDs sysUserRoDs;
    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    public ListResponse<AccRelativeVo> getVoList(AccRelativeParam param) {
        List<AccRelativeVo> res = accRelativeRoDs.getVoList(param);

        List<AccRelativePo> needUpdateList = new ArrayList<>();

        // 所属运维场站组
        res.forEach(acc -> {
            List<String> oldSiteIdList = acc.getSiteIdList();

            acc.setSiteIdList(new ArrayList<>());
            List<String> gids = siteGroupUserRoDs.getGidListByUidAndType(acc.getSysUid(), Arrays.asList(new Integer[]{SiteGroupType.YW.getCode()}));
            acc.setGidList(gids);
            if (CollectionUtils.isNotEmpty(gids)) {
                // 根据场站组ID获取该场站组下所有的站点信息
                ListSiteParam gidParam = new ListSiteParam();
                gidParam.setGids(gids);
                ListResponse<String> siteListRes = dataCoreFeignClient.getSiteListByGids(gidParam);
                if (siteListRes != null && CollectionUtils.isNotEmpty(siteListRes.getData())) {
                    List<String> siteIdList = siteListRes.getData();
                    acc.setSiteIdList(siteIdList);

                    // 判断 siteIdList 和 oldSiteIdList 是否完全相等，不相等则更新
                    if (CollectionUtils.isEmpty(oldSiteIdList) || !new HashSet<>(
                        oldSiteIdList).equals(new HashSet<>(siteIdList))) {
                        AccRelativePo po = new AccRelativePo();
                        po.setSysUid(acc.getSysUid())
                            .setSiteIdList(siteIdList);
                        needUpdateList.add(po);
                    }
                }
            }
        });

        if (CollectionUtils.isNotEmpty(needUpdateList)) {
            needUpdateList.forEach(e -> accRelativeRwDs.updateByCondition(e));
        }

        return RestUtils.buildListResponse(res, null != param.getTotal() && param.getTotal() ?
            accRelativeRoDs.getVoListCount(param) : 0L);
    }

    public void syncOrderNum() {
        log.info("syncOrderNum start");
        List<Long> sysUidList = accRelativeRoDs.getValidSysUid();
        if (CollectionUtils.isEmpty(sysUidList)) {
            log.info("无有效运维人员");
            return;
        }
        ListResponse<AccRelativeOrderVo> response = dataCoreFeignClient.getOrderNum(sysUidList);
        if (response != null && CollectionUtils.isNotEmpty(response.getData())) {
            List<AccRelativeOrderVo> list = response.getData();
            accRelativeRwDs.updateBatch(list);
        }
        log.info("syncOrderNum end");
    }

    public ObjectResponse<AccRelativeVo> findByAccount(String account, String commIdChain) {
        AccRelativeVo res = accRelativeRoDs.getByUsername(account,
            commIdChain,
            AppClientType.MGM_WEB.getCode());
        if (res != null) {
            List<SysRole> roles = sysUserService.findUserRoleListByUid(res.getSysUid());
            List<SysRoleSimpleVo> roleList = roles.stream().map(e -> {
                SysRoleSimpleVo vo = new SysRoleSimpleVo();
                BeanUtils.copyProperties(e, vo);
                return vo;
            }).collect(Collectors.toList());

            res.setRoleList(roleList);

            // 运维组ID列表
            if (null != res.getSysUid()) {
        // 查询type为3的列表
        res.setGidList(
            siteGroupUserRoDs.getGidListByUidAndType(
                res.getSysUid(), Arrays.asList(new Integer[] {SiteGroupType.YW.getCode()})));
            }
        }
        return RestUtils.buildObjectResponse(res);
    }

    public ListResponse<SysUserVo> addAccRelative(AddAccRelativeParam param) {
        // 检查本人及关联账号是否为启用状态
        IotAssert.isTrue(sysUserRoDs.countByCondition(param.getSysUid(), GlobalConst.NORMAL) > 0,
            "账号已被停用，请联系管理员！");
        if (CollectionUtils.isNotEmpty(param.getRelUserNameList())) {
            param.getRelUserNameList().forEach(e -> {
                Long count = sysUserRoDs.countByCondition(e.getSysUid(), GlobalConst.NORMAL);
                IotAssert.isTrue(count > 0, e.getUserName() + "账号已被停用，请联系管理员！");
            });
        }

        // 更新前的数据
        AccRelativeVo oldAcc = accRelativeRoDs.getAccRelative(param.getSysUid(), false);

        Optional.ofNullable(param.getGidList())
            .filter(CollectionUtils::isNotEmpty)
            .map(e -> {
                ListSiteParam gidParam = new ListSiteParam();
                gidParam.setGids(e);
                return dataCoreFeignClient.getSiteListByGids(gidParam);
            }).filter(e -> e.getData() != null && CollectionUtils.isNotEmpty(e.getData()))
            .map(ListResponse::getData)
            .ifPresent(param::setSiteIdList);
        String relativeCode = TokenGenerator.generateValue();
        param.setRelativeCode(relativeCode)
            .setCreateTime(new Date())
            .setEnable(Boolean.TRUE);
        boolean insert = accRelativeRwDs.insertOrUpdate(param);

        if (!insert) {
            throw new DcServiceException("新增操作失败");
        }

        // 配置关联账号
        if (CollectionUtils.isNotEmpty(param.getRelUserNameList())) {
            param.getRelUserNameList().forEach(e -> {
                AccRelativePo temp = new AccRelativePo();
                temp.setSysUid(e.getSysUid())
                    .setRelativeCode(relativeCode)
                    .setWork(param.getWork())
                    .setEnable(Boolean.TRUE);
                accRelativeRwDs.insertOrUpdate(temp);
            });
        }

        List<Long> result = new ArrayList<>();
        if (Boolean.FALSE.equals(param.getWork())) {
            result = this.updateAccWork(param.getSysUid(),
                param.getRelUserNameList().stream()
                    .map(RelAccount::getSysUid)
                    .collect(Collectors.toList()),
                false);
//            // 若某人离职，则关联账号也改为离职
//            accRelativeRwDs.dismissBySysUid(param.getSysUid(), false);
//
//            // 本人及关联账号失去登录权限
//            List<Long> sysUidList = new ArrayList<>(List.of(param.getSysUid()));
//            if (CollectionUtils.isNotEmpty(param.getRelUserNameList())) {
//                param.getRelUserNameList().forEach(e -> {
//                    sysUidList.add(e.getSysUid());
//                });
//            }
//            sysUserService.changeStatusBatch(sysUidList, GlobalConst.LOCKED);
        } else if (null != oldAcc) {
            // 离职转在职需要调整关联账号调整
            if (Boolean.FALSE.equals(oldAcc.getWork()) &&
                Boolean.TRUE.equals(param.getWork())) {
                this.updateAccWork(param.getSysUid(),
                    param.getRelUserNameList().stream()
                        .map(RelAccount::getSysUid)
                        .collect(Collectors.toList()),
                    true);
            }
        }

        // 所属运维组逻辑处理
        this.ywSiteGroupUserRef(param.getSysUid(), param.getGidList());

        return RestUtils.buildListResponse(sysUserRoDs.findByUserId(result, false));
    }

    private void ywSiteGroupUserRef(Long uid, List<String> gidList) {
        try {
            siteGroupUserRwDs.batchDelete(uid, gidList);
            siteGroupUserRwDs.batchInsert(uid, gidList);
        } catch (Exception e) {
            log.error("调整运维用户场站组关系异常: err = {}", e.getMessage());
        }
    }

    public BaseResponse checkForOutstandingOrder(Long sysUid) {
        AccRelativeVo oldAcc = accRelativeRoDs.getAccRelative(sysUid, false);

        List<Long> collect = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(oldAcc.getRelUserNameList())) {
            collect = oldAcc.getRelUserNameList().stream()
                .map(RelAccount::getSysUid)
                .collect(Collectors.toList());
        }
        collect.add(sysUid);

        ObjectResponse<Boolean> response = dataCoreFeignClient.existUnfinishedOrder(
            collect);
        if (response == null || response.getData() == null) {
            throw new DcServiceException("检查工单数据异常");
        }
        if (BooleanUtils.isTrue(response.getData())) {
            throw new DcServiceException("请先处理完账户名下运维单，再操作离职");
        }

        response = dataCoreFeignClient.existUnfinishedRecord(
            collect);
        if (response == null || response.getData() == null) {
            throw new DcServiceException("检查工单数据异常");
        }
        if (BooleanUtils.isTrue(response.getData())) {
            throw new DcServiceException("请先处理完账户名下巡检单，再操作离职");
        }
        return RestUtils.success();
    }

    public ListResponse<SysUserVo> editAccRelative(AddAccRelativeParam param) {
        // 检查关联账号是否为启用状态
//        if (CollectionUtils.isNotEmpty(param.getRelUserNameList())) {
//            param.getRelUserNameList().forEach(e -> {
//                Long count = sysUserRoDs.countByCondition(e.getSysUid(), GlobalConst.NORMAL);
//                IotAssert.isTrue(count > 0, e.getUserName() + "账号已被停用，请联系管理员！");
//            });
//        }

        // 更新前的数据
        AccRelativeVo oldAcc = accRelativeRoDs.getAccRelative(param.getSysUid(), false);

        String relativeCode = TokenGenerator.generateValue();
        param.setRelativeCode(relativeCode);

        Optional.ofNullable(param.getGidList())
            .filter(CollectionUtils::isNotEmpty)
            .map(e -> {
                ListSiteParam gidParam = new ListSiteParam();
                gidParam.setGids(e);
                return dataCoreFeignClient.getSiteListByGids(gidParam);
            }).filter(e -> e.getData() != null && CollectionUtils.isNotEmpty(e.getData()))
            .map(ListResponse::getData)
            .ifPresent(param::setSiteIdList);
        boolean update = accRelativeRwDs.insertOrUpdate(param);

        if (!update) {
            throw new DcServiceException("编辑操作失败");
        }

        // 配置关联账号
        if (CollectionUtils.isNotEmpty(param.getRelUserNameList())) {
            param.getRelUserNameList().forEach(e -> {
                AccRelativePo temp = new AccRelativePo();
                temp.setSysUid(e.getSysUid())
                    .setRelativeCode(relativeCode)
                    .setWork(param.getWork())
                    .setEnable(Boolean.TRUE);
                accRelativeRwDs.insertOrUpdate(temp);
            });
        }

        List<Long> result = new ArrayList<>();
        if (Boolean.FALSE.equals(param.getWork())) {
            result = this.updateAccWork(param.getSysUid(),
                param.getRelUserNameList().stream()
                    .map(RelAccount::getSysUid)
                    .collect(Collectors.toList()),
                false);
//            // 若某人离职，则关联账号也改为离职
//            accRelativeRwDs.dismissBySysUid(param.getSysUid(), false);
//
//            // 本人及关联账号失去登录权限
//            List<Long> sysUidList = new ArrayList<>(List.of(param.getSysUid()));
//            if (CollectionUtils.isNotEmpty(param.getRelUserNameList())) {
//                param.getRelUserNameList().forEach(e -> sysUidList.add(e.getSysUid()));
//            }
//            sysUserService.changeStatusBatch(sysUidList, GlobalConst.LOCKED);
        } else if (null != oldAcc) {
            // 离职转在职需要调整关联账号调整
            if (Boolean.FALSE.equals(oldAcc.getWork()) &&
                Boolean.TRUE.equals(param.getWork())) {
                this.updateAccWork(param.getSysUid(),
                    param.getRelUserNameList().stream()
                        .map(RelAccount::getSysUid)
                        .collect(Collectors.toList()),
                    true);
            }
        }

        // 所属运维组逻辑处理
        this.ywSiteGroupUserRef(param.getSysUid(), param.getGidList());

        return RestUtils.buildListResponse(sysUserRoDs.findByUserId(result, false));
    }

    private List<Long> updateAccWork(Long sysUid, List<Long> relAccSysUidList, boolean work) {
        // 若某人离职，则关联账号也改为离职
        accRelativeRwDs.dismissBySysUid(sysUid, work);

        // 本人及关联账号失去登录权限
        if (CollectionUtils.isEmpty(relAccSysUidList)) {
            relAccSysUidList = new ArrayList<>();
        }
        relAccSysUidList.add(sysUid);
        sysUserService.changeStatusBatch(relAccSysUidList,
            work ? GlobalConst.NORMAL : GlobalConst.LOCKED);
        log.debug("relUid = {}, work = {}", relAccSysUidList, work);
        return relAccSysUidList;
    }

    public ObjectResponse<SysUserVo> deleteBySysUid(Long sysUid) {
        if (accRelativeRwDs.deleteBySysUid(sysUid)) {
            return RestUtils.buildObjectResponse(sysUserRoDs.getByUseId2(sysUid, false));
        }

        throw new DcServiceException("删除操作失败");
    }

    public AccRelativeVo getYwAccount(Long sysUid) {
        return accRelativeRoDs.getAccRelative(sysUid, true);
    }

    public List<SysUser> findYwUser(YwUserParam param) {
        // 暂时不考虑顶级商户
        param.setTopCommId(null);
        return siteGroupUserRoDs.findYwUser(param);
    }

    public List<SysUser> getYwGroupOtherUser(Long uid, Boolean same, List<String> gidList) {
        IotAssert.isNotNull(uid, "用户ID无效");
        IotAssert.isNotNull(same, "是否查询同组标识需要提供");
        return siteGroupUserRoDs.getYwGroupOtherUser(uid, same, gidList);
    }
}
