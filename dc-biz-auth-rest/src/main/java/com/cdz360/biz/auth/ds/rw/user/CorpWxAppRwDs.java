package com.cdz360.biz.auth.ds.rw.user;


import com.cdz360.biz.auth.ds.rw.user.mapper.CorpWxAppRwMapper;
import com.cdz360.biz.auth.user.po.CorpWxAppPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class CorpWxAppRwDs {



	@Autowired

	private CorpWxAppRwMapper corpWxAppRwMapper;



	public CorpWxAppPo getById(Long id, boolean lock) {

		return this.corpWxAppRwMapper.getById(id, lock);

	}



	public boolean insertCorpWxApp(CorpWxAppPo corpWxAppPo) {

		return this.corpWxAppRwMapper.insertCorpWxApp(corpWxAppPo) > 0;

	}



	public boolean updateCorpWxApp(CorpWxAppPo corpWxAppPo) {

		return this.corpWxAppRwMapper.updateCorpWxApp(corpWxAppPo) > 0;

	}





}

