package com.cdz360.biz.auth.config;


import com.cdz360.biz.auth.model.vo.LoggerEntity;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.model.vo.SysUserComercial;
import com.cdz360.biz.auth.service.SysUserService;
import com.cdz360.biz.auth.service.TCommercialService;
import com.cdz360.biz.auth.utils.SysUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service("customParameterApi")
@Slf4j
public class AuditService implements CustomParameterApi {
    @Autowired
    private SysUserService userService;
    @Autowired
    private TCommercialService tCommercialService;
    @Value("${authCenter.autoMap}")
    private Boolean autoMap;

    @Override
    public Map<String, String> join() {
        return null;
    }

    @Override
    public Map<String, String> join(LoggerEntity loggerEntity) {
        if (autoMap == null || !autoMap) {
            return null;
        }
        Long userId = SysUserUtil.curUserId();
        if (userId == null) return null;

        SysUser su = userService.getBaseMapper().selectById(userId);
        SysUserComercial suc = tCommercialService.getUserCommercials(userId);
        Map<String, String> map = new HashMap<>();
        map.put("username", su.getUsername());
        map.put("userPhone", su.getPhone());
        map.put("userId", "" + su.getId());
        map.put("platform", "auth");
        if (suc != null) {
            map.put("commId", "" + suc.getCommercialId());
        }

        map.put("cate", null);
        map.put("subCate", null);
        return map;
    }
}
