package com.cdz360.biz.auth.dao;

import com.cdz360.biz.auth.model.vo.AuthGroupRef;
import com.cdz360.biz.auth.model.vo.AuthorityGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * AuthGroupRefDao
 *
 * @since 2020/2/11 16:53
 * <AUTHOR>
 */
@Mapper
public interface AuthGroupRefDao {
    int batchAddGroupRef(@Param("refList") List<AuthGroupRef> authGroupRefList);

    int deleteGroupRefByGroupId(@Param("groupId") Long groupId);

    List<AuthGroupRef> selectByGroupId(@Param("groupId") Long groupId);

    List<AuthorityGroup> getAuthorityGroupListByUid(@Param("uid") Long uid);
}
