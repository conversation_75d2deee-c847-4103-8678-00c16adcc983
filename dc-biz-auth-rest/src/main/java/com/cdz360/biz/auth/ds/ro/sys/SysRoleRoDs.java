package com.cdz360.biz.auth.ds.ro.sys;

import com.cdz360.biz.auth.ds.ro.sys.mapper.SysRoleRoMapper;
import com.cdz360.biz.auth.model.vo.SysRole;
import com.cdz360.biz.auth.sys.param.RoleUserListParam;
import com.cdz360.biz.auth.sys.vo.RoleUserVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysRoleRoDs {
    @Autowired
    private SysRoleRoMapper sysRoleRoMapper;

     public SysRole getUserRoleById(Long userId) {
        return this.sysRoleRoMapper.getUserRoleById(userId);
    }

    public List<SysRole> getRoleByIdList (List<Long> roleIdList,Long platform) {
        return this.sysRoleRoMapper.getRoleByIdList(roleIdList,platform);
    }

    public Long getRoleAmountByUserId (RoleUserListParam params) {
        return this.sysRoleRoMapper.getRoleAmountByUserId(params);
    }

    public List<SysRole> getRoleListByUserId (RoleUserListParam params) {
        return this.sysRoleRoMapper.getRoleListByUserId(params);
    }

    public List<SysRole> getRoleByUserId(String keyWord, List<Long> platformList, Long userId, Long size) {
        return this.sysRoleRoMapper.getRoleByUserId(keyWord, platformList, userId, size);
    }
}
