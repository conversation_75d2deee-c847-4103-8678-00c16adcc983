package com.cdz360.biz.auth.ds.ro.sys.mapper;

import com.cdz360.biz.model.sys.constant.ReportPage;
import com.cdz360.biz.model.sys.po.SysUserReportTemplatePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.List;

@Mapper
public interface SysUserReportTemplateRoMapper {

    List<SysUserReportTemplatePo> getInfo(@NonNull @Param("sysUserId") long sysUserId,
                                          @Nullable @Param("page") Integer page);

    long countBySysUserId(@Param("sysUserId") Long sysUserId,
                          @Param("page") ReportPage page);
}
