package com.cdz360.biz.auth.ds.ro.sys;

import com.cdz360.biz.auth.ds.ro.sys.mapper.AccRelativeRoMapper;
import com.cdz360.biz.auth.sys.param.AccRelativeParam;
import com.cdz360.biz.auth.sys.vo.AccRelativeVo;
import com.cdz360.biz.auth.sys.vo.RelAccount;
import java.util.List;
import javax.validation.constraints.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

@Service
public class AccRelativeRoDs {

    @Autowired
    private AccRelativeRoMapper mapper;

    public List<AccRelativeVo> getVoList(AccRelativeParam param) {
        return mapper.getVoList(param);
    }

    public AccRelativeVo getAccRelative(Long sysUid, boolean valid) {
        return mapper.getAccRelative(sysUid, valid);
    }

    public Long getVoListCount(AccRelativeParam param) {
        return mapper.getVoListCount(param);
    }

    public List<Long> getValidSysUid() {
        return mapper.getValidSysUid();
    }

    public AccRelativeVo getByUsername(@NotNull String username,
                                       @Nullable String commIdChain,
                                       @NotNull Integer platform) {
        return mapper.getByUsername(username,
                commIdChain,
                platform);
    }

    public List<RelAccount> getRelUserList(Long sysUid) {
        return mapper.getRelUserList(sysUid);
    }

    /**
     * 获取用户关联的账户信息
     *
     * @param sysUid 主账号ID
     * @param refSysUid 关联账号ID
     * @return
     */
    public RelAccount getRelUser(Long sysUid, Long refSysUid) {
        return mapper.getRelUser(sysUid, refSysUid);
    }

    public AccRelativeVo getAccRelativeBySiteId(String siteId) {
        return mapper.getAccRelativeBySiteId(siteId);
    }

    public AccRelativeVo getAccRelativeVoBySysUid(Long sysUid) {
        return mapper.getAccRelativeVoBySysUid(sysUid);
    }

    public AccRelativeVo getAccRelativeByCommId(Long commId) {
        return mapper.getAccRelativeByCommId(commId);
    }

    public AccRelativeVo getAccRelativeByCommIdChain(Long commId) {
        return mapper.getAccRelativeByCommIdChain(commId);
    }
}
