package com.cdz360.biz.auth.service;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.auth.model.dto.UserCommericalDto;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.model.vo.TCommercial;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * DSFilterService
 *  运营支撑平台数据权限过滤
 * @since 2019/8/8 16:51
 * <AUTHOR>
 *    <EMAIL>
 */
@Service
public class DataScopeService {
    @Autowired
    SysRoleService sysRoleService;
    @Autowired
    TCommercialService tCommercialService;

    public List<Long> dsFilterAccount(Long userId) {
        if (userId == null) {
            throw new DcServiceException("无效登录用户");
        }
        TCommercial tCommercial = tCommercialService.selectByUserId(userId);
        UserCommericalDto userCommericalDto = new UserCommericalDto()
                .setCurrent(false)
                .setCommercialId(tCommercial.getId());

        List<SysUser> userList = tCommercialService.selectUserByCommercialsId(userCommericalDto);

        List<Long> userIdList = Arrays.asList(userId); // 登录账号 至少可查看自己
        if (CollectionUtils.isNotEmpty(userList)) {
            userIdList = userList.stream().filter(u -> u != null).map(u -> u.getId()).collect(Collectors.toList());
        }
        return userIdList;
    }

}
