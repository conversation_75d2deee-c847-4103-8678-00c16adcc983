package com.cdz360.biz.auth.ds.ro.remind.mapper;

import com.cdz360.biz.auth.model.param.ListRemindAccountParam;
import com.cdz360.biz.auth.model.vo.RemindAccountVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface RemindAccountRoMapper {

    List<RemindAccountVo> getRemindAccountByCorpId(@Param("corpId") Long corpId);

    List<RemindAccountVo> findRemindAccount(ListRemindAccountParam listRemindAccountParam);

    Long count(ListRemindAccountParam param);

    List<RemindAccountVo> unbindCorpBalance(ListRemindAccountParam param);

    Long unbindCorpBalanceCount(ListRemindAccountParam param);

    List<RemindAccountVo> corpBalanceTemporary(ListRemindAccountParam param);

    Long corpBalanceTemporaryCount(ListRemindAccountParam param);
}
