package com.cdz360.biz.auth.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.param.SortParam;
import com.cdz360.base.model.base.type.OrderType;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.biz.auth.corp.po.ListPointParam;
import com.cdz360.biz.auth.feign.DcCusBalanceFeignClient;
import com.cdz360.biz.auth.sys.po.PointPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 *
 * @since 2019/8/7
 **/
@Slf4j
@Service
public class DcCusBalanceService {

    @Autowired
    private DcCusBalanceFeignClient dcCusBalanceFeignClient;

    /**
     * 根据条件查询账户
     *
     * @param payAccountType 账户类型
     * @param userIds        用户ids
     * @param commId         所属商户
     * @param enable         状态（1启用，0禁用）
     * @param sorts          排序
     * @return
     */
    public ListResponse<PointPo> queryPointPo(PayAccountType payAccountType, List<Long> userIds,
                                              Long topCommId,
                                              Long commId, Boolean enable, List<SortParam> sorts) {
        log.info("根据条件查询账户。payAccountType: {}, userIds: {}, commId: {}, enable: {}, sorts: {}",
                payAccountType, userIds, commId, enable, sorts);

        if (topCommId == null || topCommId <= 0) {
            log.info("商户数据错误。topCommId: {}", topCommId);
            throw new DcArgumentException("参数错误!");
        }

        ListPointParam listPointParam = new ListPointParam();

        //账户类型:现金和权益
        listPointParam.setType(formatPointType(payAccountType, topCommId));
        //用户ID
        listPointParam.setUidList(longList2StringList(userIds));

        listPointParam.setSorts(sorts == null ? defaultSort() : sorts);

        ListResponse<PointPo> listPointPo = dcCusBalanceFeignClient.listPoint(listPointParam);


        log.info("根据条件查询账户。listPointParam: {}, result.size = {}", listPointParam, listPointPo.getData().size());

        return listPointPo;
    }

    public static String formatPointType(PayAccountType payAccountType, long topCommId) {
        StringBuilder buf = new StringBuilder();
        if (payAccountType == PayAccountType.CORP) {
            buf.append(PayAccountType.PERSONAL.name()
                    .toLowerCase())
                    .append("_")
                    .append(topCommId);
        } else {
            buf.append(payAccountType.name()
                    .toLowerCase())
                    .append("_")
                    .append(topCommId);
        }
        return buf.toString();
    }





    private List<String> longList2StringList(List<Long> longList) {
        if (longList == null) {
            return null;
        }
        List<String> stringList = new ArrayList<>();
        for (Long l : longList) {
            if (l != null) {
                stringList.add(l.toString());
            }
        }
        return stringList;
    }

    private List<SortParam> defaultSort() {
        List<SortParam> sortParams = new ArrayList<>();
        SortParam sortParam = new SortParam();

        List<String> columns = new ArrayList<>();
        columns.add("createTime");

        sortParam.setColumns(columns);
        sortParam.setOrder(OrderType.desc);
        sortParams.add(sortParam);
        return sortParams;
    }
}
