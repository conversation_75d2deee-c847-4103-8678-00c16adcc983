package com.cdz360.biz.auth.service;

import com.cdz360.biz.auth.ds.ro.user.SiteGroupUserRoDs;
import com.cdz360.biz.auth.utils.IotAssert;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.sys.param.ListSiteGroupParam;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SiteGroupUserService {

    @Autowired
    private SiteGroupUserRoDs siteGroupUserRoDs;

    public List<SysUserVo> getUserBySiteGroupGid(String gid) {
        IotAssert.isNotBlank(gid, "场站组ID无效");
        List<SysUserVo> result = siteGroupUserRoDs.getUserBySiteGroupGid(gid);
        return result;
    }

    public List<SysUserVo> getByGidList( ListSiteGroupParam param) {
       return siteGroupUserRoDs.getByGidList(param);
    }
}
