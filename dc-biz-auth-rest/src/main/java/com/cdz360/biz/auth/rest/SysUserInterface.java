package com.cdz360.biz.auth.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.auth.config.CurrentUser;
import com.cdz360.biz.auth.model.dto.AuthRequest;
import com.cdz360.biz.auth.model.dto.CheckPasswordRespDto;
import com.cdz360.biz.auth.model.dto.ValidPhoneSysDto;
import com.cdz360.biz.auth.model.vo.Rez;
import com.cdz360.biz.auth.model.vo.SysMenu;
import com.cdz360.biz.auth.model.vo.SysSystem;
import com.cdz360.biz.auth.model.vo.SysUser;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

public interface SysUserInterface {


    ObjectResponse<SysUser> findById(@CurrentUser SysUser user,@PathVariable("id") Long id);


    Rez<SysUser> entity(@RequestBody SysUser su);


    /**
     * 运营支撑-账号管理-新增账号
     * @param user
     * @return
     */
    BaseResponse add(@CurrentUser SysUser sysUser, @RequestBody SysUser user);


    Rez delete(@PathVariable("id") Long id);

    CheckPasswordRespDto checkPassword(@RequestBody AuthRequest user);

    Rez<List<SysSystem>> sysList(@PathVariable("userId") Long userId, @RequestParam(value = "sysId", required = false) Long sysId);

    Rez<List<SysMenu>> menus(@PathVariable("userId") Long userId, @PathVariable("sysId") Long sysId);


    Rez<List<SysMenu>> menusByAppId(@PathVariable("userId") Long userId, @PathVariable("appId") String appId);


    Rez validPhoneUsernameEmail(@RequestBody ValidPhoneSysDto dto);

}
