package com.cdz360.biz.auth.ds.ro.corp;

import com.cdz360.biz.auth.corp.po.CorpOrgPo;
import com.cdz360.biz.auth.ds.ro.corp.mapper.CorpOrgRoMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CorpOrgRoDs {

    @Autowired
    private CorpOrgRoMapper corpOrgRoMapper;


    public CorpOrgPo getCorpOrgBySysUid(Long sysUid, Long corpId) {
        return this.corpOrgRoMapper.getCorpOrgBySysUid(sysUid, corpId);
    }
}
