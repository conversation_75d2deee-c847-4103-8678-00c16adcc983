package com.cdz360.biz.auth.ds.ro.user.mapper;

import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.sys.param.YwUserParam;
import com.cdz360.biz.auth.user.vo.SiteGroupUserRefVo;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.sys.param.ListSiteGroupParam;
import com.cdz360.biz.model.sys.vo.CorpGroupTinyVo;
import com.cdz360.biz.model.sys.vo.SiteGroupVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.NonNull;

@Mapper
public interface SiteGroupUserRoMapper {

    /**
     * 使用 sys_user.id获取该用户关联的组的gid
     */
    List<String> getGidListByUid(@Param("uid") Long uid);

    List<String> getGidListByUidAndType(@Param("uid") Long uid, @Param("types") List<Integer> types);

    List<SiteGroupVo> getSiteGroupsByUid(@Param("uid") Long uid, @Param("type") Integer type);

    List<SiteGroupVo> getSiteGroupsList(@Param("idChain") String idChain, @Param("gidList") List<String> gidList,
        @Param("types") List<Integer> types);

    List<SysUser> findYwUser(YwUserParam param);

    List<SysUser> getYwGroupOtherUser(
        @Param("uid") Long uid,
        @Param("same") Boolean same,
        @Param("gidList") List<String> gidList);

    List<SiteGroupUserRefVo> getUidsByGids(@Param("gids") List<String> gids);

    List<SysUserVo> getUserBySiteGroupGid(@Param("gid") String gid);
    List<SysUserVo> getByGidList(ListSiteGroupParam param);

    List<CorpGroupTinyVo> getCorpGroups(@NonNull @Param("corpIdList") List<Long> corpIdList);

}
