package com.cdz360.biz.auth.ds.rw.subscribe.mapper;

import com.cdz360.biz.auth.subscribe.param.AddSubscribeParam;
import com.cdz360.biz.auth.subscribe.param.CreatePayOrderParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface SubscribeRwMapper {
    Long add(AddSubscribeParam params);

    Long update(AddSubscribeParam params);

    void updateSubOrder(@Param("payNo") String payNo, @Param("payChannel") Long payChannel, @Param("status") Boolean status);

    void updateNoteByNo(@Param("payNo") String payNo, @Param("note") String note);

    void updateExpireTimeByPayNo(@Param("payNo") String payNo);

    void updateStatus(@Param("updateBy") Long updateBy, @Param("subId") Long subId);

    void addSubComm(@Param("subId") Long subId, @Param("topCommId") Long topCommId, @Param("commIdList") List<Long> commIdList);

    void addSubRole(@Param("subId") Long subId, @Param("roleIdList") List<Long> roleIdList);

    void updateSubComm(@Param("subId") Long subId);

    void updateSubRole(@Param("subId") Long subId);

    void createOrder(CreatePayOrderParam params);

    void createOrderUser(@Param("payNo") String payNo, @Param("mapList") List<Map> MapList);

    void createOrderRole(@Param("payNo") String payNo, @Param("roleIdList") List<Long> roleIdList);

}