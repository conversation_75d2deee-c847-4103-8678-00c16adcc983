package com.cdz360.biz.auth.config;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

@Slf4j
@Configuration
public class FeignConfig {
    @Bean
    public FeignTokenInterceptor feignTokenInterceptor() {
        return new FeignTokenInterceptor();
    }

    public class FeignTokenInterceptor implements RequestInterceptor {
        @Override
        public void apply(RequestTemplate template) {
            String token = "";
            try {
                token = (String) RequestContextHolder.currentRequestAttributes().getAttribute("token", RequestAttributes.SCOPE_REQUEST);

            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            template.header("token", token);
        }
    }
}
