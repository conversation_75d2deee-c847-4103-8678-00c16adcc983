package com.cdz360.biz.auth.ds.ro.sys.mapper;

import com.cdz360.biz.auth.sys.param.AccRelativeParam;
import com.cdz360.biz.auth.sys.vo.AccRelativeVo;
import com.cdz360.biz.auth.sys.vo.RelAccount;
import java.util.List;
import javax.validation.constraints.NotNull;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.Nullable;

@Mapper
public interface AccRelativeRoMapper {

    List<AccRelativeVo> getVoList(AccRelativeParam param);

    Long getVoListCount(AccRelativeParam param);

    List<Long> getValidSysUid();

    AccRelativeVo getByUsername(@NotNull @Param("username") String username,
                                @Nullable @Param("commIdChain") String commIdChain,
                                @NotNull @Param("platform") Integer platform);

    List<RelAccount> getRelUserList(@Param("sysUid") Long sysUid);

    RelAccount getRelUser(@Param("sysUid") Long sysUid, @Param("refSysUid") Long refSysUid);

    AccRelativeVo getAccRelative(@Param("sysUid") Long sysUid, @Param("valid") boolean valid);

    AccRelativeVo getAccRelativeBySiteId(@Param("siteId") String siteId);

    AccRelativeVo getAccRelativeVoBySysUid(@Param("sysUid") Long sysUid);

    AccRelativeVo getAccRelativeByCommId(@Param("commId") Long commId);
    AccRelativeVo getAccRelativeByCommIdChain(@Param("commId") Long commId);
}
