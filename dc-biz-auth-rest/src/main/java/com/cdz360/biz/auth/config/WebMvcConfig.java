package com.cdz360.biz.auth.config;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.auth.utils.RequestUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;


@Configuration
@Slf4j
public class WebMvcConfig implements WebMvcConfigurer {

    @Autowired
    UserArgResolver userArgResolver;

    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**");
    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(userArgResolver);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        HandlerInterceptor interceptor = new HandlerInterceptor() {
            //            JSONObject jsonObject = new JSONObject(5);
            Map<String, Object> jsonObject = new HashMap<>();

            @Override
            public boolean preHandle(HttpServletRequest request, HttpServletResponse response,
                Object handler) throws Exception {

                jsonObject.put("method", request.getMethod());
                jsonObject.put("url", request.getRequestURI());
                jsonObject.put("param", request.getParameterMap());
                jsonObject.put("user", request.getRemoteUser());
                jsonObject.put("token", request.getHeader("token"));
                log.info("request:: {}", JsonUtils.toJsonString(jsonObject));
                String token = RequestUtils.getToken(request);
                if (token != null) {
                    request.setAttribute("token", token);
                }
                return true;
            }
        };
        registry.addInterceptor(interceptor).addPathPatterns("/**").excludePathPatterns("/data/**");
    }

}
