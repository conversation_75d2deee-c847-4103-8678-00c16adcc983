package com.cdz360.biz.auth.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@TableName("sys_user")
@Data
@ToString
public class TCommercialUser extends SysUser {

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long topCommId;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long comId;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long commId;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> comIds;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer commercialRole;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String changePass;// 修改密码时，表示原密码，入参了就做原密码检查

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String newPass;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastLoginTime;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commIdChain;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String roleNameList;
}
