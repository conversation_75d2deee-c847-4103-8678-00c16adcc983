package com.cdz360.biz.auth.service.corpWx;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.auth.corpWx.UserCorpWx;
import com.cdz360.biz.auth.ds.ro.user.CorpWxAppRoDs;
import com.cdz360.biz.auth.ds.ro.user.SysUserRoDs;
import com.cdz360.biz.auth.ds.rw.user.SysUserRwDs;
import com.cdz360.biz.auth.feign.reactor.OpenHlhtFeignClient;
import com.cdz360.biz.auth.user.param.ListCorpWxAppParam;
import com.cdz360.biz.auth.user.vo.CorpWxAppVo;
import com.cdz360.biz.auth.utils.IotAssert;
import java.time.Duration;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class CorpWxService {

    @Autowired
    private SysUserRoDs sysUserRoDs;

    @Autowired
    private SysUserRwDs sysUserRwDs;

    @Autowired
    private CorpWxAppRoDs corpWxAppRoDs;

    @Autowired
    private OpenHlhtFeignClient openHlhtFeignClient;

    public void testApi() {
        String corpId = "ww61b19a0295bcd17a";
        String corpSecret = "P3j2-yHD0bFjmJjnYNsPU16NhY0hxqmMrZMLzdf4C-A";
        openHlhtFeignClient.corpWxDepartmentList(34474L, null)
            .subscribe(x -> log.info("department: {}", x));
        openHlhtFeignClient.corpWxUserList(34474L, 1L, false)
            .subscribe(x -> log.info("user list: {}", x));
    }

//    @Scheduled(initialDelay = 4000, fixedRate = 1000 * 60)
//    public void syncSysUserFlag() {
//        this.testApi();
//        log.info(">>>>> syncSysUserFlag");
//    }

    public List<CorpWxAppVo> listAllCorpWxApp(ListCorpWxAppParam param) {
        return corpWxAppRoDs.findAll(param)
            .stream()
            .map(x -> new CorpWxAppVo().setId(x.getId()).setName(x.getName()))
            .collect(Collectors.toList());
    }

    public String getUserMobile(Long topCommId, String code) {
        IotAssert.isNotNull(topCommId, "顶级商户ID不能为空");
        IotAssert.isNotBlank(code, "授权码不能为空");
        return openHlhtFeignClient.getUserDetail(topCommId, code)
            .map(ObjectResponse::getData)
            .map(UserCorpWx::getMobile)
            .block(Duration.ofSeconds(50L));
    }

    public BaseResponse refreshTeamCatalog() {
        return Flux.fromIterable(corpWxAppRoDs.findAll(new ListCorpWxAppParam()))
            .flatMap(app -> openHlhtFeignClient.corpWxDepartmentList(
                    app.getTopCommId(), null)
                .flatMap(x -> Mono.justOrEmpty(x.getData()))
                .flatMapMany(Flux::fromIterable)
                .flatMap(d -> openHlhtFeignClient.corpWxUserList(
                        app.getTopCommId(), d.getId(), false)
                    .flatMap(x -> Mono.justOrEmpty(x.getData()))
                    .flatMapMany(Flux::fromIterable)
                    .doOnNext(u -> {
                        if (StringUtils.isNotBlank(u.getMobile())) {
                            final int i = sysUserRwDs.updateTeamCatalogByPhone(
                                app.getTopCommId(), u.getMobile(), d.getName(), u.getUserId(),
                                app.getId());
                            if (i > 0) {
                                log.info("[{}]更新成功: mobile = {}, i = {}", u.getUserId(),
                                    u.getMobile(), i);
                            } else {
                                log.warn("[{}]更新失败: mobile = {}", u.getUserId(), u.getMobile());
                            }
                        } else {
                            log.warn("[{}]企业微信没有手机号", u.getUserId());
                        }
                    })))
            .collectList()
            .map(x -> RestUtils.success())
            .block(Duration.ofSeconds(50L));

    }
}
