package com.cdz360.biz.auth.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.sys.param.SiteGroupSiteParam;
import com.cdz360.biz.model.sys.param.UpdateSiteGroupSiteParam;
import com.cdz360.biz.model.sys.vo.SiteGroupSiteInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class SiteGroupSiteFeignHystrix implements FallbackFactory<SiteGroupSiteFeignClient> {

    @SuppressWarnings("ALL")
    @Override
    public SiteGroupSiteFeignClient apply(Throwable throwable) {
        log.error("服务[{}]熔断, err = {}",
            DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, throwable.getMessage(), throwable);

        return new SiteGroupSiteFeignClient() {

            @Override
            public Mono<ListResponse<String>> findSiteGroupSiteBySiteId(SiteGroupSiteParam param) {
                log.error("[{}] 获取场站关联的场站组ID列表, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, param);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<SiteGroupSiteInfoVo>> findSiteGroupSiteInfo(
                SiteGroupSiteParam param) {
                log.error("[{}] 获取场站组关联场站信息, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, param);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<SiteGroupSiteInfoVo>> updateSiteGroupSiteRef(
                UpdateSiteGroupSiteParam param) {
                log.error("[{}] 更新场站组关联场站信息, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, param);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<Long>> getSiteAmountByGidList(
                SiteGroupSiteParam param) {
                log.error("[{}] 场站组关联站点数量, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, param);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }
        };
    }
}
