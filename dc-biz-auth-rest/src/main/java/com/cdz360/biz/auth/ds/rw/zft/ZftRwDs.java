package com.cdz360.biz.auth.ds.rw.zft;



import com.cdz360.biz.auth.zft.po.ZftPo;

import com.cdz360.biz.auth.ds.rw.zft.mapper.ZftRwMapper;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;

import org.springframework.stereotype.Service;


@Slf4j

@Service

public class ZftRwDs {



	@Autowired
	private ZftRwMapper zftRwMapper;



	public ZftPo getById(Long id, boolean lock) {
		return this.zftRwMapper.getById(id, lock);
	}



	public boolean insertZft(ZftPo zftPo) {
		return this.zftRwMapper.insertZft(zftPo) > 0;
	}



	public boolean updateZft(ZftPo zftPo) {
		return this.zftRwMapper.updateZft(zftPo) > 0;
	}





}

