package com.cdz360.biz.auth.ds.rw.user;

import com.cdz360.biz.auth.ds.rw.user.mapper.SysUserRwMapper;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.user.po.SysUserPo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysUserRwDs {

    @Autowired
    private SysUserRwMapper sysUserRwMapper;

   public  void updateLastLoginTime(Long sysUserId) {
        this.sysUserRwMapper.updateLastLoginTime(sysUserId);
    }

    public int updateUserPasswordAndUsername(Long sysUserId, String password,String username) {
        return this.sysUserRwMapper.updateUserPasswordAndUsername(sysUserId,password, username);
    }
    public void insetUser (SysUserPo sysUser){
       this.sysUserRwMapper.insetUser(sysUser);
    }

    public void updateCorpId(SysUserPo sysUser) {
       this.sysUserRwMapper.updateCorpId(sysUser);
    }

    public void updateUserId(SysUserPo sysUser) {
        this.sysUserRwMapper.updateUserId(sysUser);
    }

    public boolean updateUserStatusById(SysUser sysUser) {
        return this.sysUserRwMapper.updateUserStatusById(sysUser) > 0;
    }

    public void updateOpenId(Long userId, String wxOpenId) {
        this.sysUserRwMapper.updateOpenId(userId,wxOpenId);
    }

    public void insertOrg (Long corpId,Long userId,Long orgId) {
        this.sysUserRwMapper.insertOrg(corpId,userId,orgId);
    }
    public void insertRole (Long userId,Long roleId,Long createBy) {
        this.sysUserRwMapper.insertRole(userId,roleId,createBy);
    }
    public void updateUserOrgById (Long userId,Long orgId) { this.sysUserRwMapper.updateUserOrgById(userId,orgId);}

    public void updateUserRole (Long userId,Long roleId,Long updateBy) {
        this.sysUserRwMapper.updateUserRole(userId,roleId,updateBy);
    }

    public List<Long> getUserByOrg (Long userId) {
        return this.sysUserRwMapper.getUserByOrg(userId);
    }

    public int moveCorp(Long corpId, Long commId, Long topCommId) {
        return this.sysUserRwMapper.moveCorp(corpId, commId, topCommId);
    }

    public int updateTeamCatalogByPhone(
            Long topCommId, String mobile, String departmentName,
            String corpWxUid, Long corpWxAppId) {
        return this.sysUserRwMapper.updateTeamCatalogByPhone(
                topCommId, mobile, departmentName, corpWxUid, corpWxAppId);
    }

    public int clearSysUserGroupId(String groupId) {
       return this.sysUserRwMapper.clearSysUserGroupId(groupId);
    }

    public int updateSysUserGroupId(String groupId, List<Long> uidList) {
       return this.sysUserRwMapper.updateSysUserGroupId(groupId, uidList);
    }
}
