package com.cdz360.biz.auth.ds.ro.sys;

import com.cdz360.biz.auth.ds.ro.sys.mapper.SysUserReportTemplateRoMapper;
import com.cdz360.biz.model.sys.constant.ReportPage;
import com.cdz360.biz.model.sys.po.SysUserReportTemplatePo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysUserReportTemplateRoDs {

    @Autowired
    private SysUserReportTemplateRoMapper mapper;

    public List<SysUserReportTemplatePo> getInfo(long sysUserId, @Nullable Integer page) {
        return mapper.getInfo(sysUserId, page);
    }

    public long countBySysUserId(long sysUserId, ReportPage page) {
        return mapper.countBySysUserId(sysUserId, page);
    }

}
