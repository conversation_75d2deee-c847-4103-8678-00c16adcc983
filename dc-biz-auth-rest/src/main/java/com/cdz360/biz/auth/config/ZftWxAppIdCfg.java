package com.cdz360.biz.auth.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
public class ZftWxAppIdCfg {

    @Value("${zft.wx.wxMchId:}")
    private String wxMchId;

    @Value("${zft.wx.liteAppId:}")
    private String wxLiteAppId;

    @Value("${zft.wx.androidAppId:}")
    private String wxAndroidAppId;

    @Value("${zft.wx.iosAppId:}")
    private String wxIosAppId;
}
