package com.cdz360.biz.auth.ds.ro.corp.mapper;

import com.cdz360.biz.model.cus.corp.param.ListCorpParam;
import com.cdz360.biz.model.cus.corp.po.CorpOrgPo;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.corp.vo.CorpOrgVO;
import com.cdz360.biz.model.cus.corp.vo.CorpSimpleVo;
import com.cdz360.biz.model.cus.corp.vo.CorpVo;
import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.List;

@Mapper
public interface CorpRoMapper {
      CorpPo getCorpCountByCorpName(String blocUser);
      CorpPo getCorpCountByAccount(String acount);
      CorpPo getCorpById(long id);
      CorpPo getCorpByUid(long uid);
      List<CorpSimpleVo> getCorpByCommId(@NonNull @Param("commIdChain") String commIdChain,
                                         @Nullable @Param("enable") Integer enable,
                                         @Nullable @Param("corpId") Long corpId);
      Page<CorpOrgVO> getCorpOrgList(@Param("corpId") Long corpId);
      List<CorpOrgVO> getCorpOrgByLevel(@Param("userId") Long userId,@Param("corpId") Long corpId, @Param("orgLevel") Integer orgLevel,@Param("userLevel") Integer userLevel);
      CorpOrgPo getOrgInfoByLevel(@Param("corpId") Long corpId, @Param("orgLevel") Integer orgLevel);
      CorpOrgPo getCorpOrgByOrgId(@Param("id") Long id);
      Page<CorpOrgVO> getOrgByUserId(@Param("corpId") Long corpId, @Param("cusId") Long cusId);
      CorpPo getCorp(@Param("corpId") long corpId, @Param("lock") boolean lock);

      /**
       * orgName 选传
       */
      List<CorpOrgPo> getOrgByL1Id(@Param("corpId") Long corpId, @Param("l1Id") Long l1Id,
                                   @Param("orgName") String orgName);

      /**
       * orgName 选传
       * @param corpId
       * @param l2Id
       * @param orgName
       * @return
       */
      List<CorpOrgPo> getOrgByL2Id(@Param("corpId") Long corpId, @Param("l2Id") Long l2Id,
                                   @Param("orgName") String orgName);

      List<CorpPo> getCorpList(ListCorpParam param);

      Long getCorpCount(ListCorpParam param);

      List<CorpPo> getNeedRemindCorp(@Param("start") long start,
                                     @Param("size") int size);

}
