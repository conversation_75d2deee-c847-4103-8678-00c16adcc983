package com.cdz360.biz.auth.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.auth.corpWx.DepartmentCorpWx;
import com.cdz360.biz.auth.corpWx.UserCorpWx;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_OPEN_HLHT,
    fallbackFactory = OpenHlhtFeignHystrix.class)
public interface OpenHlhtFeignClient {

    // 获取企业微信部门列表
    @GetMapping("/open/corpWx/{topCommId}/departmentList")
    Mono<ListResponse<DepartmentCorpWx>> corpWxDepartmentList(
        @PathVariable(value = "topCommId") Long topCommId,
        @RequestParam(value = "id", required = false) Long id);

    // 获取企业微信用户敏感信息
    @GetMapping("/open/corpWx/{topCommId}/userDetail")
    Mono<ObjectResponse<UserCorpWx>> getUserDetail(
        @PathVariable(value = "topCommId") Long topCommId,
        @RequestParam("code") String code);

    // 获取企业微信部门成员详情
    @GetMapping("/open/corpWx/{topCommId}/userList")
    Mono<ListResponse<UserCorpWx>> corpWxUserList(
        @PathVariable(value = "topCommId") Long topCommId,
        @RequestParam("departmentId") Long departmentId,
        @RequestParam(value = "fetchChild", required = false) Boolean fetchChild);
}