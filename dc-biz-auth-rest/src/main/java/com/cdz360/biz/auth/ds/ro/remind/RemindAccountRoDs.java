package com.cdz360.biz.auth.ds.ro.remind;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.ds.ro.remind.mapper.RemindAccountRoMapper;
import com.cdz360.biz.auth.model.param.ListRemindAccountParam;
import com.cdz360.biz.auth.model.vo.RemindAccountVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class RemindAccountRoDs {

    @Autowired
    private RemindAccountRoMapper remindAccountRoMapper;

    public List<RemindAccountVo> getRemindAccountByCorpId(Long corpId) {
        return remindAccountRoMapper.getRemindAccountByCorpId(corpId);
    }

    public Mono<ListResponse<RemindAccountVo>> findRemindAccount(ListRemindAccountParam param) {
        return Mono.just(param)
            .doOnNext(p -> {
                if (null == param.getSize() || param.getSize() > 10000) {
                    param.setSize(9999);
                }
            })
            .map(this.remindAccountRoMapper::findRemindAccount)
            .map(RestUtils::buildListResponse)
            .doOnNext(res -> {
                if (null != param.getTotal() && param.getTotal()) {
                    res.setTotal(this.remindAccountRoMapper.count(param));
                } else {
                    res.setTotal(0L);
                }
            });
    }

    public Mono<ListResponse<RemindAccountVo>> unbindCorpBalance(ListRemindAccountParam param) {
        return Mono.just(param)
            .doOnNext(p -> {
                if (null == param.getSize() || param.getSize() > 10000) {
                    param.setSize(9999);
                }
            })
            .map(this.remindAccountRoMapper::unbindCorpBalance)
            .map(RestUtils::buildListResponse)
            .doOnNext(res -> {
                if (null != param.getTotal() && param.getTotal()) {
                    res.setTotal(this.remindAccountRoMapper.unbindCorpBalanceCount(param));
                } else {
                    res.setTotal(0L);
                }
            });
    }

    public Mono<ListResponse<RemindAccountVo>> corpBalanceTemporary(ListRemindAccountParam param) {
        return Mono.just(param)
            .doOnNext(p -> {
                if (null == param.getSize() || param.getSize() > 10000) {
                    param.setSize(9999);
                }
            })
            .map(this.remindAccountRoMapper::corpBalanceTemporary)
            .map(RestUtils::buildListResponse)
            .doOnNext(res -> {
                if (null != param.getTotal() && param.getTotal()) {
                    res.setTotal(this.remindAccountRoMapper.corpBalanceTemporaryCount(param));
                } else {
                    res.setTotal(0L);
                }
            });
    }
}
