package com.cdz360.biz.auth.model.vo;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 商户表
 *
 * <AUTHOR>
 */
@Data
public class Commercial implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 商户号
     */
    private String merchants;

    /**
     * 商户类型(1:个人、2:企业)
     */
    private Integer commType;

    /**
     * 商户名称
     */
    private String commName;

    /**
     * 简称
     */
    private String shortName;

    /**
     * 商户logo
     */
    private String commLogo;

    /**
     * 商户图标
     */
    private String commIcon;


    /**
     * 联系人
     */
    private String contacts;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 联系邮箱
     */
    private String email;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 类别(1.实体,2.虚拟)
     */
    private Integer commCategory;

    /**
     * 商户行业(1.商业体,2.住宅,3.公共设施,4.政府机构,5.其他)
     */
    private Integer commIndustry;

    /**
     * 营业执照注册号
     */
    private String license;

    /**
     * 组织机构代码
     */
    private String code;

    /**
     * 法人代表
     */
    private String legalPerson;

    /**
     * 省
     */
    private String provinceId;

    /**
     * 市
     */
    private String cityId;

    /**
     * 区
     */
    private String areaId;

    /**
     * 详细地址
     */
    private String detail;

    /**
     * 父级
     */
    private Long pid;

    /**
     * 商户级别
     */
    private Integer commLevel;

    /**
     * 是否保证金(0.否,1.是)
     */
    protected Integer isBond;

    /**
     * 保证金金额
     */
    protected Long bondAmount;

    /**
     * 充电方式 0:扫码充电  1：即查即充
     */
    private Integer chargeMode;

    /**
     * 自动充满1，按金额2
     */
    private Integer billType;

    /**
     * 业务类型，0充电，1租车
     */
    private Integer businessType;

    /**
     * 运营方式，0客户运营商，1设备运营商，2即是客户运营商也是设备运营商
     */
    private Integer operateType;

    /**
     * 运营商，关联运营商字典表
     */
    private Long operateBaseInfoId;

    /**
     * 公司类型，0自营，1加盟，2第三方
     */
    private Integer companyType;

    /**
     * 注册资金
     */
    private Long registrationAmount;

    /**
     * 成立日期
     */
    private String registrationTime;

    /**
     * 营业执照
     */
    private String businessLicence;

    /**
     * 服务电话
     */
    private String servicePhone;

    /**
     * 状态（1、审核中，2、审核成功，3、审核失败）
     */
    private Integer status;

    /**
     * 第三方序号
     */
    private String thridNo;

    /**
     * 判断开启充电是否使用timeout_status状态:0不需要timeout_status
     */
    private Integer setChargerStartTimeout;

    /**
     * 预授权金额（支付宝）（分）
     */
    private Long preAuthorizationAmount;

    /**
     * 当前商户授权码
     */
    private String myLicenseCode;

    /**
     * 代理商级别
     * 当前商户是代理商时存储值
     */
    private Integer agentLevel;

    /**
     * 代理商层级id
     * 当前商户是代理商存储值：将商户上级id按照顺序以逗号分割，
     * 例子：
     * 当前商户为二级代理商 存储格式：商户Id,一级代理商Id
     */
    private String agentLevelPath;
//
//    @Schema(description = "下级商户列表")
//    private List<Commercial> children;
}
