package com.cdz360.biz.auth.ds.ro.user.mapper;


import com.cdz360.biz.auth.user.param.ListCorpWxAppParam;
import com.cdz360.biz.auth.user.po.CorpWxAppPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper

public interface CorpWxAppRoMapper {



	CorpWxAppPo getById(@Param("id") Long id);

	List<CorpWxAppPo> findAll(ListCorpWxAppParam param);
}

