package com.cdz360.biz.auth.ds.rw.message;

import com.cdz360.biz.auth.ds.rw.message.mapper.MessageRwMapper;
import com.cdz360.biz.auth.zft.po.ZftPo;
import com.cdz360.biz.model.cus.message.po.MessagePo;
import com.cdz360.biz.model.cus.message.type.PlatformType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 */
@Slf4j

@Service

public class MessageRwDs {

	@Autowired
	private MessageRwMapper messageRwMapper;

	/**
	 * 添加站内信
	 * @param messagePo
	 * @return
	 */
	public int insertMessage(MessagePo messagePo) {

		return this.messageRwMapper.insertMessage(messagePo) ;

	}

	/**
	 * 批量插入
	 * @param userIdList
	 * @param platform
	 * @param msgId
	 * @return
	 */
	public int batchInsert(List<Long> userIdList, PlatformType platform,Long msgId) {

		return this.messageRwMapper.batchInsert(userIdList,platform,msgId) ;

	}

	/**
	 * 修改阅读记录
	 * @param msgId
	 * @param uid
	 */
	public void updateMessageReadLog(Long msgId,Long uid){
		this.messageRwMapper.updateMessageReadLog(msgId,uid);
	}

	/**
	 * 站内信撤回
	 * @param msgId
	 * @return
	 */
	public int editMessage (Long msgId) {
		return this.messageRwMapper.editMessage(msgId);
	}

	/**
	 * 消息记录表
	 * @param msgId
	 * @return
	 */
	public int editMsgLog (Long msgId) {
		return this.messageRwMapper.editMsgLog(msgId);
	}
}

