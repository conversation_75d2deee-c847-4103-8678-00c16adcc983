package com.cdz360.biz.auth.model.vo;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cdz360.biz.auth.sys.vo.Authority;
import com.cdz360.biz.auth.sys.vo.RelAccount;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.hash.Hashing;
import io.swagger.v3.oas.annotations.media.Schema;
import java.nio.charset.Charset;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.codec.digest.Md5Crypt;
import org.springframework.util.Assert;

@EqualsAndHashCode(callSuper = true)
@TableName("sys_user")
@Data
public class SysUser extends BaseEntity implements LogicDeletable {

    /**
     * 权限组名
     */
    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String groupName;

    /**
     * 组织机构
     */
    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orgName;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String gNameStr;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String gidStr;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Long siteAmount;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date birthday;

    /**
     * 邮箱
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String email;

    @TableId(value = "id", type = IdType.AUTO)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long id;

    @TableField(value = "topCommId")
    private Long topCommId;
    @TableField(value = "commId")
    private Long commId;

    @TableField(exist = false)
    private String commIdChain;

    /**
     * 姓名
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String name;
    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
//    @JsonIgnore
    private String password;
    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "\\d{11}", message = "这可能不是一个正确的手机号,不需要国际地区号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String phone;

    @Schema(description = "账号是否为受限账号: true-受限，false-非受限")
    @TableField(value = "limitAcc")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Boolean limitAcc;

    /**
     * 角色ID列表
     */
    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> roleIdList;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<SysRole> roleList;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long roleId;


    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String salt;
    /**
     * 性别  2女 1男
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer sex;
    /**
     * 状态  1：启用  2：冻结  3：删除
     */
    @TableLogic(value = "1")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer status;

    /**
     * {@Link com.cdz360.base.model.app.type.AppClientType}
     */
    @NotNull(message = "请选择所属平台")
    @Schema(description = "所属平台. 0,未知; 20, 充电管理平台; 30，海外版充电管理平台，21, 运营支撑平台")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer platform;

    @Schema(description = "所属商户ID")
    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long commercialId;


    @TableField(exist = false)
    @Schema(description = "用户所属的场站组")
    private List<String> gids;

    private Long createBy;


    @TableField(value = "corpId")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long corpId;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long orgId;

    @TableField(exist = false)
    @Schema(description = "本身及下属的组织ID")
    private List<Long> orgIds;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer orgLevel;
    /**
     * 用户名
     */
    @Schema(description = "账号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String username;

    @Schema(description = "微信昵称")
    @TableField(value = "nickname")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String nickname;


    @Schema(description = "微信openId")
    @TableField(value = "wxOpenId")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String wxOpenId;
    /**
     * 租户ID
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String tenantId;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date lastLoginTime;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer locked;

    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String credentials;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<SysSystem> sysList;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String platformName;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer commPlatform;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String commLogo;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Authority> authorityList;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<RelAccount> relAccountList;

    @TableField(exist = false)
    //权限组列表，用于新建用户时传参
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> authGroupIds;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<AuthorityGroup> authorityGroupList;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String corpAuthCommIds;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String token;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commercialName;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private CorpPo corpPo;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String corpWxAppName;

    @TableField(value = "corpWxAppId")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long corpWxAppId;

    @TableField(value = "teamCatalog")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String teamCatalog;

    @TableField(value = "corpWxUid")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String corpWxUid;

    public String getSalt() {
        return salt;
    }

    public SysUser setSalt(String salt) {
        this.salt = salt;
        return this;
    }

    public void useGenerateSalt() {
        this.setSalt(Md5Crypt.md5Crypt(username.getBytes()));
    }

    public String hashedPassword() {
        if (this.salt == null) {
            this.useGenerateSalt();
        }
        return Base64.getEncoder()
            .encodeToString(Hashing.sha256()
                .hashString(salt + password, Charset.defaultCharset()).asBytes());
//        return new Sha256Hash(password, salt).toBase64();
    }

    public String hashedPassword(String pwd) {
        Assert.notNull(salt, "用户 salt 不应为空");
        return Base64.getEncoder()
            .encodeToString(Hashing.sha256()
                .hashString(salt + pwd, Charset.defaultCharset()).asBytes());
//        return new Sha256Hash(pwd, salt).toBase64();
    }

    @Override
    public String toString() {
        return "SysUser{" +
            "password='" + password + '\'' +
            ", salt='" + salt + '\'' +
            '}';
    }
}
