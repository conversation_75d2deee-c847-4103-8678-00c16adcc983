package com.cdz360.biz.auth.service.oauth;
//
//import com.alibaba.fastjson.JSONObject;
//import com.cdz360.biz.auth.model.dto.R;
//import com.cdz360.biz.auth.model.exception.ErrcodeException;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.shiro.session.SessionException;
//import org.apache.shiro.subject.Subject;
//import org.apache.shiro.web.filter.authc.LogoutFilter;
//
//import javax.servlet.ServletRequest;
//import javax.servlet.ServletResponse;
//import jakarta.servlet.http.HttpServletRequest;
//
//@Slf4j
//public class AuthLogoutFilter extends LogoutFilter {
//    ShiroHttpService shiroHttpService;
//
//    public void setShiroHttpService(ShiroHttpService shiroHttpService) {
//        this.shiroHttpService = shiroHttpService;
//    }
//
//    @Override
//    protected boolean preHandle(ServletRequest request, ServletResponse response) throws Exception {
//        Subject subject = getSubject(request, response);
//        String redirectUrl = getRedirectUrl(request, response, subject);
//        //try/catch added for SHIRO-298:
//        log.info("url = {}", redirectUrl);
//        try {
//            subject.logout();
//            shiroHttpService.logout(OAuth2Filter.getRequestToken((HttpServletRequest) request));
//
//        } catch (SessionException ise) {
//            log.debug("Encountered session exception during logout.  This can generally safely be ignored.", ise);
//        } catch (ErrcodeException e) {
//            log.error("status 不为0 ::{}", e.getMessage());
//        }
//        //如果 token 来自 cookie
//        if (OAuth2Filter.COOKIE.equals(request.getAttribute(OAuth2Filter.TOKEN_FROM))) {
//            issueRedirect(request, response, redirectUrl);
//        } else {
//            R ok = R.ok("you have logout now!, for more actions pls login again!");
//            JSONObject.writeJSONString(response.getOutputStream(), ok);
//            response.setContentType("application/json");
//            response.flushBuffer();
//        }
//        return false;
//    }
//}
