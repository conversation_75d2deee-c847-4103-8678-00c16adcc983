package com.cdz360.biz.auth.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.auth.dao.SysDictDao;
import com.cdz360.biz.auth.model.exception.NameConflictException;
import com.cdz360.biz.auth.model.type.ErrStatus;
import com.cdz360.biz.auth.model.vo.SysDict;
import com.cdz360.biz.auth.utils.BeanValidator;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service
public class SysDictService extends ServiceImpl<SysDictDao, SysDict> {

    public List<SysDict> queryByPcode(String pcode) {
        List<SysDict> sysDicts = baseMapper.findByPcode(pcode);
        return sysDicts;
    }

    public SysDict queryByPcodeAndCode(String pcode, String code) {
        return baseMapper.findByPcodeAndCode(pcode, code);
    }


    public boolean upsertDictType(SysDict sysDict) {
        Assert.notNull(sysDict, "参数错误");
        Assert.notNull(sysDict.getCode(), "参数错误");
        Assert.notNull(sysDict.getName(), "参数错误");
        sysDict.setPid(0L);
        return this.getBaseMapper().insertOrUpdate(sysDict);
    }

    public boolean deleteDictType(Long id) {
        SysDict sysDict = this.getBaseMapper().selectById(id);
        if (sysDict == null) {
            return false;
        }

        getBaseMapper().delete(Wrappers.query(SysDict.class).eq("pid", sysDict.getId()));
        return this.getBaseMapper().deleteById(id) > 0L;
    }


    @Deprecated
    public Optional<ErrStatus> upsertDictByPcode(SysDict dict) {
        String pcode = dict.getPcode();
        boolean isvalid = BeanValidator.isvalid(dict);
        if (isvalid && !StringUtils.isEmpty(pcode)) {
            List<SysDict> list = baseMapper.selectList(
                Wrappers.query(SysDict.class).eq("code", pcode));
            //pcode 应该是唯一的
            if (list.size() != 1) {
                return Optional.of(ErrStatus.UNKNOWN);
            }
            SysDict parent = list.get(0);
            dict.setPid(parent.getId());

            list = baseMapper.selectList(Wrappers.query(SysDict.class).eq("pid", parent.getId())
                .eq("name", dict.getName()));
            if (CollectionUtils.isNotEmpty(list)) {
                return Optional.of(ErrStatus.NAME_CONFLICT);
            }

            SysDict vo = new SysDict();
            vo.setCode(dict.getCode());
            vo.setPid(parent.getId());
            SysDict resVo = baseMapper.selectOne(Wrappers.query(vo));

            if (resVo == null) {
                return getBaseMapper().insert(dict) > 0 ? Optional.empty()
                    : Optional.of(ErrStatus.UNKNOWN);
            } else {
                dict.setId(resVo.getId());
                return updateById(dict) ? Optional.empty() : Optional.of(ErrStatus.UNKNOWN);
            }
        }
        return Optional.empty();
    }


    public void checkName(String name, String pcode) {
        // 检查名称可用
        List<SysDict> all = queryByPcode(pcode);

        boolean nameNotAvailable = all.stream().anyMatch(d -> d.getName().equals(name));
        if (nameNotAvailable) {
            throw new NameConflictException();
        }
    }

    // 获取国充场站的gcType值
    public List<Integer> getGcSiteTypes() {
        String strV = baseMapper.getGcSiteTypes();
        if (StringUtils.isNotBlank(strV)) {
            return Arrays.stream(strV.split(",")).map(str -> NumberUtils.parseInt(str, -1))
                .collect(Collectors.toList());
        } else {
            return List.of();
        }
    }
}
