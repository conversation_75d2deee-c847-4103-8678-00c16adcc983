<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.dao.UserGroupMapper">
    <insert id="batchInsertUserGroupRef" parameterType="java.util.List">
        insert into t_user_group_ref (userId, authGroupId, opName, opId, createTime) values
        <foreach collection="authGroupIds" item="authGroupId" index="index" separator=",">
            (
            #{userId, jdbcType=BIGINT},
            #{authGroupId, jdbcType=BIGINT},
            #{opName, jdbcType=VARCHAR},
            #{opId, jdbcType=BIGINT},
            now())
        </foreach>
    </insert>

    <delete id="deleteUserGroupRefByUserId">
        delete from t_user_group_ref where userId=#{userId}
    </delete>

    <select id="getAuthGroupIdListByUid" resultType="java.lang.Long">
        select authGroupId from t_user_group_ref where userId = #{userId}
    </select>

    <select id="getAccountListByGroupId" resultType="java.lang.String">
        select u.name from t_user_group_ref ur
        left join sys_user u on u.id = ur.userId
        left join t_auth_group_ref gr on gr.`groupId` = ur.`authGroupId`
        where gr.`groupId` = #{groupId}
        limit #{start},#{size}
    </select>
</mapper>