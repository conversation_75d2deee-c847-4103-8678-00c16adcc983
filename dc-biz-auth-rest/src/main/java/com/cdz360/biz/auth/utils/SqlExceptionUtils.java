package com.cdz360.biz.auth.utils;

import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class SqlExceptionUtils {

//    @Autowired
//    IndexMetaDao indexMetaDao;

    public String getDuplcateKeyExMsg(DuplicateKeyException ex) {
        String message = ex.getMostSpecificCause().getMessage();
        Pattern pattern = Pattern.compile("'(.*?)'");
        Matcher matcher =
                pattern.matcher(message);
        String msg = null;
        String key = null;
        if (matcher.find()) {
            msg = matcher.group(1);
        }
        if (matcher.find()) {
            key = matcher.group(1);
        }

        StringBuilder sb = new StringBuilder("输入值与现有数据重复");
        return sb.toString();
    }

}
