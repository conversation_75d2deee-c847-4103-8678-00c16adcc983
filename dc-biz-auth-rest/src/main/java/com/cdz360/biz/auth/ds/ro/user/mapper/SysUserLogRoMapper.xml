<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.ds.ro.user.mapper.SysUserLogRoMapper">

	<resultMap id="RESULT_SYS_USER_LOG_PO" type="com.cdz360.biz.auth.user.po.SysUserLogPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="sysUid" jdbcType="BIGINT" property="sysUid" />
		<result column="loginName" jdbcType="VARCHAR" property="loginName" />
		<result column="loginUser" jdbcType="VARCHAR" property="loginUser" />
		<result column="topCommId" jdbcType="BIGINT" property="topCommId" />
		<result column="commId" jdbcType="BIGINT" property="commId" />
		<result column="opType" jdbcType="VARCHAR" property="opType" />
		<result column="ip" jdbcType="VARCHAR" property="ip" />
		<result column="clientType" property="clientType"
				typeHandler="com.cdz360.ds.type.EnumTypeHandler" />
		<result column="opObject" property="opObject"
				typeHandler="com.cdz360.biz.auth.ds.MybatisJsonTypeHandler" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
	</resultMap>

	<resultMap id="RESULT_SYS_USER_LOG_VO" extends="RESULT_SYS_USER_LOG_PO" type="com.cdz360.biz.auth.user.vo.SysUserLogVo">
		<result column="comm_name" jdbcType="VARCHAR" property="commName" />
		<result column="sysUserName" jdbcType="VARCHAR" property="sysUserName" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_SYS_USER_LOG_PO">	
		select * from t_sys_user_log where id = #{id}
	</select>

	<select id="getLoginLog" parameterType="com.cdz360.biz.auth.user.param.SysUserLogParam"
			resultMap="RESULT_SYS_USER_LOG_VO">
		select
			log.*,
			comm.comm_name
		from
			t_sys_user_log log
		left join t_commercial comm on
			log.`commId` = comm.id
		where
			log.`opType` = "LOGIN"
			and comm.`idChain` like concat(#{commIdChain}, '%')
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( loginName )">
			and log.`loginName` like concat('%', #{loginName}, '%')
		</if>
		<if test="clientType != null">
			and log.`clientType` = #{clientType.code}
		</if>
		<if test="beginTime != null and endTime != null">
			<![CDATA[ and log.`createTime` >= #{beginTime} and `createTime` <= #{endTime} ]]>
		</if>
		<if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( commIdList )">
			and log.`commId` in
			<foreach collection="commIdList" item="commId" open="(" separator="," close=")">
				#{commId}
			</foreach>
		</if>
		order by
			log.`createTime` desc
		limit #{start}, #{size}
	</select>

	<select id="getLoginLogCount" parameterType="com.cdz360.biz.auth.user.param.SysUserLogParam"
			resultType="java.lang.Long">
		select
			count(log.id)
		from
			t_sys_user_log log
		left join t_commercial comm on
			log.`commId` = comm.id
		where
			log.`opType` = "LOGIN"
			and comm.`idChain` like concat(#{commIdChain}, '%')
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( loginName )">
			and log.`loginName` like concat('%', #{loginName}, '%')
		</if>
		<if test="clientType != null">
			and log.`clientType` = #{clientType.code}
		</if>
		<if test="beginTime != null and endTime != null">
			<![CDATA[ and log.`createTime` >= #{beginTime} and `createTime` <= #{endTime} ]]>
		</if>
		<if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( commIdList )">
			and log.`commId` in
			<foreach collection="commIdList" item="commId" open="(" separator="," close=")">
				#{commId}
			</foreach>
		</if>
	</select>

	<select id="getOpLog" parameterType="com.cdz360.biz.auth.user.param.SysUserLogParam"
			resultMap="RESULT_SYS_USER_LOG_VO">
		select
			log.*,
			comm.comm_name,
			su.name as sysUserName
		from
			t_sys_user_log log
		left join t_commercial comm on
			log.`commId` = comm.id
		left join sys_user su on
			log.`sysUid` = su.id
		where
			`opType` != "LOGIN"
		and comm.`idChain` like concat(#{commIdChain}, '%')
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( sysName )">
			and ( su.name like concat('%', #{sysName}, '%') or log.loginName like concat('%', #{sysName}, '%') )
		</if>
		<if test="clientType != null">
			and log.`clientType` = #{clientType.code}
		</if>
		<if test="beginTime != null and endTime != null">
			<![CDATA[ and log.`createTime` >= #{beginTime} and `createTime` <= #{endTime} ]]>
		</if>
		<if test="opType != null">
			and `opType` = #{opType}
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( opObject )">
			and log.`opObject` like concat('%', #{opObject}, '%')
		</if>
		<if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( commIdList )">
			and log.`commId` in
			<foreach collection="commIdList" item="commId" open="(" separator="," close=")">
				#{commId}
			</foreach>
		</if>
		order by
			log.`createTime` desc
		limit #{start}, #{size}
	</select>

	<select id="getOpLogCount" parameterType="com.cdz360.biz.auth.user.param.SysUserLogParam"
			resultType="java.lang.Long">
		select
			count(log.id)
		from
			t_sys_user_log log
		left join t_commercial comm on
			log.`commId` = comm.id
		left join sys_user su on
			log.`sysUid` = su.id
		where
			`opType` != "LOGIN"
		and comm.`idChain` like concat(#{commIdChain}, '%')
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( sysName )">
			and ( su.name like concat('%', #{sysName}, '%') or log.loginName like concat('%', #{sysName}, '%') )
		</if>
		<if test="clientType != null">
			and log.`clientType` = #{clientType.code}
		</if>
		<if test="beginTime != null and endTime != null">
			<![CDATA[ and log.`createTime` >= #{beginTime} and `createTime` <= #{endTime} ]]>
		</if>
		<if test="opType != null">
			and `opType` = #{opType}
		</if>
		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( opObject )">
			and log.`opObject` like concat('%', #{opObject}, '%')
		</if>
		<if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( commIdList )">
			and log.`commId` in
			<foreach collection="commIdList" item="commId" open="(" separator="," close=")">
				#{commId}
			</foreach>
		</if>
	</select>
</mapper>
