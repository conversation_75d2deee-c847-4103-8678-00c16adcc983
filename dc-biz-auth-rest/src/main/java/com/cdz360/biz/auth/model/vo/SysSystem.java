package com.cdz360.biz.auth.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 子系统表
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("sys_system")
@ToString(callSuper = true)
public class SysSystem extends BaseEntity {

    @TableField
    @NotNull
    private String name;
    private Integer platform;
    private String appkey;
    private String url;
    private String icon;
    private Boolean hidden;
    @TableField(value = "`desc`")
    private String desc;
    @TableField(exist = false)
    private List<SysMenu> menus;

    public SysSystem() {
    }

    public SysSystem(Long id) {
        this.setId(id);
    }

}
