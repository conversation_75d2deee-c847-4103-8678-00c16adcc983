<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.ds.rw.user.mapper.SiteGroupUserRwMapper">

  <delete id="batchDelete">
    DELETE FROM t_site_group_user_ref where uid = #{uid}
    and gid not in
    <foreach collection="exGidList" item="item" index="index" separator="," open="(" close=")">
      #{item}
    </foreach>
  </delete>

  <delete id="deleteByUid">
    DELETE FROM t_site_group_user_ref where uid = #{uid}
  </delete>
  <delete id="deleteByGid">
    DELETE FROM t_site_group_user_ref where gid = #{gid}
  </delete>

  <insert id="batchInsert">
    INSERT ignore INTO t_site_group_user_ref
    (`gid`, `uid`, `createTime`)
    VALUES
    <foreach collection="gidList" item="item" index="index" separator=",">
      (#{item}, #{uid}, now())
    </foreach>
  </insert>
</mapper>