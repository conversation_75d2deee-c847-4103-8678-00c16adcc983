<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.biz.auth.ds.ro.message.mapper.MessageRoMapper">

	<select id="getMessageById" resultType="com.cdz360.biz.model.cus.message.vo.UserMessageVo">
		select msg.id,msg.title,msg.corpId,msg.content,msg.msgType,msg.platform,msg.createTime,log.readStatus
		from t_message msg left join t_sys_user_msg log on msg.id =log.msgId
		where msg.id=#{msgId} and sysUid = #{uid} and msg.enable=true
	</select>
    <select id="getUnReadCount" resultType="java.lang.Integer">
		SELECT
			count( id )
		FROM
			t_sys_user_msg
		WHERE
			sysUid = #{uid}
			AND platform in(#{platform},99)
			and enable=true and readStatus=1 limit 1
	</select>
	<sql id="common_where_condition">
		1=1
		<if test="title != null">
			and title LIKE concat("%",#{title},"%")
		</if>
		<if test="enable != null">
			and msg.enable=#{enable}
		</if>
		<if test="startTime != null">
			<![CDATA[ and msg.createTime >= #{startTime} ]]>
		</if>
		<if test="endTime != null">
			<![CDATA[ and msg.createTime <= #{endTime} ]]>
		</if>
		<if test="msgType != null">
			and msg.msgType = #{msgType.code}
		</if>
		<choose>
			<when test="username == '系统'">
				and msg.opUid=0
			</when>
			<otherwise>
				<if test="username != null">
					and user.username like concat("%",#{username},"%")
				</if>
			</otherwise>
		</choose>
	</sql>
	<select id="getMsgList" resultType="com.cdz360.biz.model.cus.message.vo.MessageVo">
		SELECT
		msg.*,
		corp.corpName,
		user.username,
		(SELECT group_concat( comm_name ) FROM t_commercial tc WHERE FIND_IN_SET( tc.id, msg.commIds )) AS commNameList
		FROM
		t_message msg
		LEFT JOIN sys_user user ON user.id = msg.opUid
		left join t_corp corp on msg.corpId = corp.id
		<where>
			<include refid="common_where_condition"></include>
		</where>
		ORDER BY msg.id DESC LIMIT	#{start},#{size}
	</select>
	<select id="getMsgListCount" resultType="java.lang.Integer">
		SELECT
			count(distinct msg.id)
		FROM
		t_message msg
		LEFT JOIN sys_user user ON user.id = msg.opUid
		<where>
			<include refid="common_where_condition"></include>
		</where>
	</select>
	<sql id="userMsgList_condition">
		1=1
		AND log.sysUid=#{uid}
		AND log.enable = true
		<if test="msgType!= null">
			AND msg.msgType = #{msgType.code}
		</if>
		<if test="unread!= null and unread == true">
			AND log.readStatus=1
		</if>
		AND log.platform in(99,#{platform})
	</sql>
	<select id="getUserMsgList" resultType="com.cdz360.biz.model.cus.message.vo.UserMessageVo">
		SELECT
			msg.id,
			msg.msgType,
			msg.title,
			msg.createTime,
			readStatus
		FROM
			t_sys_user_msg log
			left join t_message msg on log.msgId=msg.id
		<where>
			<include refid="userMsgList_condition"></include>
		</where>
		ORDER BY msg.id DESC
		LIMIT #{start},#{size}
	</select>
	<select id="getUserMsgListCount" resultType="java.lang.Integer">
		SELECT
			count( log.id )
		FROM
		t_sys_user_msg log
		<if test="msgType!= null">
		left join t_message msg on log.msgId=msg.id
		</if>
		<where>
			<include refid="userMsgList_condition"></include>
		</where>
	</select>
</mapper>

