package com.cdz360.biz.auth.model.dto;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class R extends HashMap<String, Object> {
    private static final long serialVersionUID = 1L;

    public R() {
        put("status", 0);
    }

    public static R error() {
        return error(500, "未知异常，请联系管理员");
    }

    public static R error(String msg) {
        return error(500, msg);
    }

    public static R error(int errcode, String errmsg) {
        R r = new R();
        r.put("status", errcode);
        r.put("error", errmsg);
        return r;
    }

    public static R ok(String errmsg) {
        R r = new R();
        r.put("error", errmsg);
        return r;
    }

    public static R ok(Map<String, Object> map) {
        R r = new R();
        r.putAll(map);
        return r;
    }

    public static R ok() {
        return new R();
    }

    public R put(String key, Object value) {
        super.put(key, value);
        return this;
    }

    public boolean isSuccess() {
        return Objects.equals(this.get("status"), 0);
    }

    public String errmsg() {
        return (String) this.get("error");
    }
}