package com.cdz360.biz.auth.model.vo;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.ToString;

@Data
@TableName("sys_user_comercial")
@ToString(callSuper = true)
public class SysUserComercial {
    @TableId(type = IdType.INPUT)
    @NotBlank
    private Long userId;
    @NotBlank
    private Long commercialId;

    private Integer commercialRole;

    @Schema(description = "仅在创建商户时才为true 具体用意不明，暂时处理")
    private boolean main;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(value = "update_time", fill = FieldFill.UPDATE, update = "now()")
    private Date updateTime;

    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private Long createBy;
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;
    @TableField(exist = false)
    private String createUser;
    @TableField(exist = false)
    private String updateUser;
}
