package com.cdz360.biz.auth.ds.rw.user.mapper;


import com.cdz360.biz.auth.user.po.CorpWxAppPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper

public interface CorpWxAppRwMapper {

	CorpWxAppPo getById(@Param("id") Long id, @Param("lock") boolean lock);



	int insertCorpWxApp(CorpWxAppPo corpWxAppPo);



	int updateCorpWxApp(CorpWxAppPo corpWxAppPo);





}

