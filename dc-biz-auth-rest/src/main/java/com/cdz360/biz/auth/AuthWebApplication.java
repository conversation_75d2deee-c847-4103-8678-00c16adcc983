package com.cdz360.biz.auth;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import reactivefeign.spring.config.EnableReactiveFeignClients;
import reactor.core.publisher.Hooks;

@SpringBootApplication
@EnableDiscoveryClient
@EnableReactiveFeignClients(basePackages = {"com.cdz360.biz.auth.feign.reactor"})
@EnableFeignClients(basePackages = {"com.cdz360.biz.auth.feign",
        "com.chargerlinkcar.framework.common.feign"})
@Slf4j
@ComponentScan(basePackages = {
        "com.cdz360.biz.auth",
        "com.cdz360.data",
        "com.chargerlinkcar.framework.common"})
@MapperScan(basePackages = {"com.cdz360.biz.auth.**.mapper"})
@EnableScheduling
public class AuthWebApplication {

    /**
     * 主函数
     */
    public static void main(String[] args) {
        Hooks.enableAutomaticContextPropagation();  // 输出调用链traceId
        SpringApplication.run(AuthWebApplication.class, args);
    }
}


