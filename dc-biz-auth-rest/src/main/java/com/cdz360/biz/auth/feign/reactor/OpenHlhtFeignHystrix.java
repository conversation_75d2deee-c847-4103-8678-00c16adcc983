package com.cdz360.biz.auth.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.corpWx.DepartmentCorpWx;
import com.cdz360.biz.auth.corpWx.UserCorpWx;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class OpenHlhtFeignHystrix implements FallbackFactory<OpenHlhtFeignClient> {

    @Override
    public OpenHlhtFeignClient apply(Throwable throwable) {
        log.error("服务[{}]熔断, err = {}",
            DcConstants.KEY_FEIGN_OPEN_HLHT, throwable.getMessage(), throwable);

        return new OpenHlhtFeignClient() {
            @Override
            public Mono<ListResponse<DepartmentCorpWx>> corpWxDepartmentList(
                Long topCommId, Long id) {
                log.error("[{}]接口熔断-获取企业微信部门列表, topCommId = {}, id = {}",
                    DcConstants.KEY_FEIGN_OPEN_HLHT, topCommId, id);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<UserCorpWx>> getUserDetail(Long topCommId, String code) {
                log.error("[{}]接口熔断-获取企业微信用户敏感信息, topCommId = {}, code = {}",
                    DcConstants.KEY_FEIGN_OPEN_HLHT, topCommId, code);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ListResponse<UserCorpWx>> corpWxUserList(
                Long topCommId, Long departmentId, Boolean fetchChild) {
                log.error("[{}]接口熔断-获取企业微信部门成员详情, topCommId = {}," +
                        " departmentId = {}, fetchChild = {}",
                    DcConstants.KEY_FEIGN_OPEN_HLHT, topCommId, departmentId, fetchChild);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }
        };
    }

    @Override
    public <V> Function<V, OpenHlhtFeignClient> compose(
        Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_OPEN_HLHT);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(
        Function<? super OpenHlhtFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_OPEN_HLHT);
        return null;
    }
}
