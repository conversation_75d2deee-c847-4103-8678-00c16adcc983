package com.cdz360.biz.auth.ds.ro.user.mapper;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.sys.param.QuerySysUserRequest;
import com.cdz360.biz.auth.sys.param.RoleUserListParam;
import com.cdz360.biz.auth.sys.vo.RoleUserVo;
import com.cdz360.biz.auth.user.po.SysUserPo;
import com.cdz360.biz.model.cus.message.type.PlatformType;
import com.cdz360.biz.model.cus.param.SysUserCheckParam;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SysUserRoMapper {

    SysUserPo getByUsername(@Param("username") String username,
        @Param("platform") Integer platform);

    SysUserPo getByUseId(@Param("userId") Long userId);

    SysUserPo getByOpenId(@Param("openId") String openId);

    List<SysUserPo> getUserIdList(@Param("commIdList") List<Long> commIdList,
        @Param("platform") PlatformType platform,
        @Param("start") int start,
        @Param("size") int size);

    /**
     * 通过用户Id获取
     *
     * @param userIdList
     * @return
     */
    List<String> getUserByIdList(@Param("userIdList") List<Long> userIdList,
        @Param("opUid") Long opUid);

    /**
     * 系统续费提醒
     *
     * @param commIdList
     * @param platform
     * @param corpId
     * @param start
     * @param size
     * @return
     */
    List<SysUserPo> getUserIdListForCorp(@Param("commIdList") List<Long> commIdList,
        @Param("platform") PlatformType platform,
        @Param("corpId") Long corpId,
        @Param("start") int start,
        @Param("size") int size);

    List<SysUserVo> findByUserId(@Param("userIdList") List<Long> userIdList,
        @Param("valid") boolean valid);

    Long countByCondition(@Param("sysUid") Long sysUid,
        @Param("status") Integer status);


    List<SysUserPo> getUserByIdAndPlatform(@Param("userIdList") List<Long> userIdList,
        @Param("platform") Long platform);

    SysUserPo getByUserNameAndPlatform(@Param("username") String username,
        @Param("platform") AppClientType platform);

    List<SysUserPo> getByNameLike(@Param("username") String username);

    Long getRoleUserAmount(RoleUserListParam params);

    List<RoleUserVo> getRoleUserList(RoleUserListParam params);

    List<RoleUserVo> getUserByRoleId(@Param("keyWord") String keyWord,
        @Param("platform") Long platform,
        @Param("roleId") Long roleId,
        @Param("size") Long size);

    List<SysUserVo> getUserListById(@Param("commIdChain") String commIdChain,
        @Param("platform") Integer platform);

    List<Long> groupIdByKeyWord(@Param("keyWord") String keyWord);

    List<Long> uidByTeamCatalog(@Param("teamCatalog") String teamCatalog,
        @Param("uname") String uname);

    List<Long> uidByTeamCatalogList(@Param("teamCatalogList") List<String> teamCatalogList,
        @Param("uname") String uname);

    List<Long> uidByUname(@Param("uname") String uname);

    List<SysUserPo> queryByCheckParam(@Param("list") List<SysUserCheckParam> list);

    List<Long> sameCorpWxAppNameUids(@Param("uid") Long uid);

    SysUserPo getOneByPhoneAndPlatform(
        @Param("topCommId") Long topCommId,
        @Param("phone") String phone,
        @Param("platform") Integer platform);

    List<SysUser> getUserSimpleList(QuerySysUserRequest param);

    SysUser selectById(@Param("id") Long id);
}
