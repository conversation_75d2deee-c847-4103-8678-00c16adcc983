<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.biz.auth.ds.rw.zft.mapper.ZftRwMapper">



	<resultMap id="RESULT_ZFT_PO" type="com.cdz360.biz.auth.zft.po.ZftPo">

		<id column="id" jdbcType="BIGINT" property="id" />
		<id column="topCommId" jdbcType="BIGINT" property="topCommId" />
		<id column="commId" jdbcType="BIGINT" property="commId" />

		<result column="name" jdbcType="VARCHAR" property="name" />

		<result column="enableBalance" jdbcType="INTEGER" property="enableBalance" />

		<result column="wxMchId" jdbcType="VARCHAR" property="wxMchId" />
		<result column="wxSubMchId" jdbcType="VARCHAR" property="wxSubMchId" />
		<result column="wxSubMchName" jdbcType="VARCHAR" property="wxSubMchName" />
		<result column="wxCreditServiceId" jdbcType="VARCHAR" property="wxCreditServiceId" />

		<result column="alipayMchId" jdbcType="VARCHAR" property="alipayMchId" />
		<result column="alipaySubMchId" jdbcType="VARCHAR" property="alipaySubMchId" />
		<result column="alipaySubMchName" jdbcType="VARCHAR" property="alipaySubMchName" />
		<result column="alipayCreditServiceId" jdbcType="VARCHAR" property="alipayCreditServiceId" />

		<result column="wxLiteAppId" jdbcType="VARCHAR" property="wxLiteAppId" />
		<result column="wxAndroidAppId" jdbcType="VARCHAR" property="wxAndroidAppId" />
		<result column="wxIosAppId" jdbcType="VARCHAR" property="wxIosAppId" />

		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />

		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />

		<result column="updateOpId" jdbcType="BIGINT" property="updateOpId" />

		<result column="updateOpName" jdbcType="VARCHAR" property="updateOpName" />

		<result column="createOpId" jdbcType="BIGINT" property="createOpId" />

		<result column="createOpName" jdbcType="VARCHAR" property="createOpName" />

	</resultMap>



	<select id="getById"

			resultMap="RESULT_ZFT_PO">
		select * from t_zft where id = #{id}

		<if test="lock == true">

			for update

		</if>

	</select>



	<insert id="insertZft" useGeneratedKeys="true" keyProperty="id"

			keyColumn="id" parameterType="com.cdz360.biz.auth.zft.po.ZftPo">

		insert into t_zft (
			`topCommId`,
			`commId`,
			`name`,

			`enableBalance`,

<!--			`enableRefund`,-->
			`wxMchId`,
			`wxSubMchId`,
			`wxSubMchName`,
			`wxCreditServiceId`,
			`alipayMchId`,
			`alipaySubMchId`,
			`alipaySubMchName`,
			`alipayCreditServiceId`,
			`wxLiteAppId`,
			`wxAndroidAppId`,
			`wxIosAppId`,

			`createTime`,

			`updateTime`,

			`updateOpId`,

			`updateOpName`,

			`createOpId`,

			`createOpName`)

		values (
			#{topCommId},
			#{commId},
			#{name},

			#{enableBalance},
<!--			#{enableRefund},-->
			#{wxMchId},
			#{wxSubMchId},

			#{wxSubMchName},
			#{wxCreditServiceId},
			#{alipayMchId},
			#{alipaySubMchId},
			#{alipaySubMchName},
			#{alipayCreditServiceId},

			#{wxLiteAppId},
			#{wxAndroidAppId},
			#{wxIosAppId},

			now(),

			now(),

			#{updateOpId},

			#{updateOpName},

			#{createOpId},

			#{createOpName})

	</insert>



	<update id="updateZft" parameterType="com.cdz360.biz.auth.zft.po.ZftPo">

		update t_zft set

		<if test="commId != null">
			commId = #{commId},
		</if>

		<if test="name != null">
			name = #{name},
		</if>

		<if test="enableBalance != null">
			enableBalance = #{enableBalance},
		</if>

<!--		<if test="enableRefund != null">-->

<!--			enableRefund = #{enableRefund},-->

<!--		</if>-->
		<if test="wxMchId != null">
			wxMchId = #{wxMchId},
		</if>
		<if test="wxSubMchId != null">
			wxSubMchId = #{wxSubMchId},
		</if>

		<if test="wxSubMchName != null">
			wxSubMchName = #{wxSubMchName},
		</if>
		<if test="wxCreditServiceId != null">
			wxCreditServiceId = #{wxCreditServiceId},
		</if>

		<if test="alipayMchId != null">
			alipayMchId = #{alipayMchId},
		</if>
		<if test="alipaySubMchId != null">
			alipaySubMchId = #{alipaySubMchId},
		</if>

		<if test="alipaySubMchName != null">
			alipaySubMchName = #{alipaySubMchName},
		</if>
		<if test="alipayCreditServiceId != null">
			alipayCreditServiceId = #{alipayCreditServiceId},
		</if>

		<if test="wxLiteAppId != null">
			wxLiteAppId = #{wxLiteAppId},
		</if>
		<if test="wxAndroidAppId != null">
			wxAndroidAppId = #{wxAndroidAppId},
		</if>
		<if test="wxIosAppId != null">
			wxIosAppId = #{wxIosAppId},
		</if>

		<if test="updateOpId != null">

			updateOpId = #{updateOpId},

		</if>

		<if test="updateOpName != null">

			updateOpName = #{updateOpName},

		</if>

		<if test="createOpId != null">

			createOpId = #{createOpId},

		</if>

		<if test="createOpName != null">

			createOpName = #{createOpName},

		</if>

		updateTime = now()

		where id = #{id}

	</update>



</mapper>

