package com.cdz360.biz.auth.ds.ro.message.mapper;
import com.cdz360.biz.model.cus.message.param.ListMessageParam;
import com.cdz360.biz.model.cus.message.vo.MessageVo;
import com.cdz360.biz.model.cus.message.vo.UserMessageVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
@Mapper

public interface MessageRoMapper {
	/**
	 * 根据ID获取站内信信息
	 * @param msgId
	 * @return
	 */
	UserMessageVo getMessageById(@Param("msgId") Long msgId,@Param("uid") Long uid);

	/**
	 * 获取用户未读消息条数
	 * @param uid
	 * @param platform
	 * @return
	 */
	int getUnReadCount(@Param("uid") Long uid,@Param("platform") Long platform);

	/**
	 * 获取消息列表
	 * @param reqParam
	 * @return
	 */
	List<MessageVo> getMsgList(ListMessageParam reqParam);

	/**
	 * 个人站内信列表
	 * @param reqParam
	 * @return
	 */
	List<UserMessageVo> getUserMsgList(ListMessageParam reqParam);

	/**
	 * 消息总数
	 * @param reqParam
	 * @return
	 */
	int getMsgListCount(ListMessageParam reqParam);

	/**
	 * 个人消息总数
	 * @param reqParam
	 * @return
	 */
	int getUserMsgListCount(ListMessageParam reqParam);
}

