package com.cdz360.biz.auth.service.oauth;

import com.cdz360.base.utils.JsonUtils;
import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.apache.shiro.authc.AuthenticationException;
import org.apache.shiro.authc.AuthenticationToken;
import org.apache.shiro.web.filter.authc.AuthenticatingFilter;
import org.apache.shiro.web.util.WebUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMethod;

/**
 * oauth2过滤器
 */
public class OAuth2Filter extends AuthenticatingFilter {

    public static final String TOKEN = "token";
    public static final String SYS_ID = "sysId";
    public static final String TOKEN_FROM = "__token-from";
    public static final String COOKIE = "cookie";

    /**
     * 获取请求的token
     */
    public static String getRequestToken(HttpServletRequest httpRequest) {
        //从header中获取token
        String token = httpRequest.getHeader(TOKEN);

        //如果header中不存在token，则从参数中获取token
        if (StringUtils.isEmpty(token)) {
            token = httpRequest.getParameter(TOKEN);
        }

        if (StringUtils.isEmpty(token)) {
            Optional<Cookie> cookieOpt = Arrays.stream(httpRequest.getCookies())
                .filter(c -> com.cdz360.base.utils.StringUtils.equalsIgnoreCase(c.getName(), TOKEN))
                .findFirst();
            if (cookieOpt.isPresent()) {
                httpRequest.setAttribute(TOKEN_FROM, COOKIE);
                token = cookieOpt.get().getValue();
            }
        }
        if (token != null) {
            httpRequest.setAttribute(TOKEN, token);
        }
        return token;
    }

    /**
     * 获取请求的系统ID
     */
    public static String getRequestSysId(HttpServletRequest httpRequest) {
        //从header中获取sysId
        String sysId = httpRequest.getHeader(SYS_ID);
        //如果header中不存在sysId，则从参数中获取sysId
        if (StringUtils.isEmpty(sysId)) {
            sysId = httpRequest.getParameter(SYS_ID);
            return sysId;
        }
        return sysId;
    }

    @Override
    protected boolean preHandle(ServletRequest request, ServletResponse response) throws Exception {
        HttpServletRequest httpRequest = WebUtils.toHttp(request);

        HttpServletResponse httpResponse = WebUtils.toHttp(response);
        if (httpRequest.getMethod().equals(RequestMethod.OPTIONS.name())) {
            httpResponse.setHeader("Access-control-Allow-Origin", "*");
            httpResponse.setHeader("Access-Control-Allow-Methods", httpRequest.getMethod());
            httpResponse.setHeader("Access-Control-Allow-Headers", "*");
            httpResponse.setStatus(HttpStatus.OK.value());
            return false;
        }
        return super.preHandle(request, response);
    }

    @Override
    protected AuthenticationToken createToken(ServletRequest request, ServletResponse response)
        throws Exception {
        //获取请求token
        String token = getRequestToken((HttpServletRequest) request);

        if (StringUtils.isEmpty(token)) {
            return null;
        }

        return new OAuth2Token(token);
    }

    @Override
    protected boolean isAccessAllowed(ServletRequest request, ServletResponse response,
        Object mappedValue) {
        return false;
    }

    @Override
    protected boolean onAccessDenied(ServletRequest request, ServletResponse response)
        throws Exception {
        //获取请求token，如果token不存在，直接返回401
        String token = getRequestToken((HttpServletRequest) request);
        if (StringUtils.isEmpty(token)) {
            HttpServletResponse httpResponse = (HttpServletResponse) response;
            initHttpResponse((HttpServletRequest) request, httpResponse);
            httpResponse.setStatus(200);
            httpResponse.setContentType(MediaType.APPLICATION_JSON_VALUE);
            httpResponse.setCharacterEncoding("UTF-8");
            Map<String, Object> json = new HashMap<>();
            json.put("status", 4012);
            json.put("error", "请重新登录");
//            String json = new JSONObject()
//                .fluentPut("status", 4012)
//                .fluentPut("error", "请重新登录")
//                .toString();
            Cookie cookie = new Cookie("token", "");
            cookie.setMaxAge(0);
            httpResponse.addCookie(cookie);
            httpResponse.getWriter().print(JsonUtils.toJsonString(json));
            httpResponse.flushBuffer();
            return false;
        }

        return executeLogin(request, response);
    }

    protected void initHttpResponse(HttpServletRequest httpRequest,
        HttpServletResponse httpResponse) {
        // 跨域
        String origin = httpRequest.getHeader("Origin");
        if (origin == null) {
            httpResponse.setHeader("Access-Control-Allow-Origin", "*");
        } else {
            httpResponse.setHeader("Access-Control-Allow-Origin", origin);
        }
        httpResponse.setHeader("Access-Control-Allow-Headers",
            "Origin, x-requested-with, Content-Type, Accept,X-Cookie");
        httpResponse.setHeader("Access-Control-Allow-Credentials", "true");
        httpResponse.setHeader("Access-Control-Allow-Methods", "GET,POST,PUT,OPTIONS,DELETE");
    }

    @Override
    protected boolean onLoginFailure(AuthenticationToken token, AuthenticationException e,
        ServletRequest request, ServletResponse response) {
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        httpResponse.setContentType("application/json;charset=utf-8");

        try {
            initHttpResponse((HttpServletRequest) request, httpResponse);
            ((HttpServletResponse) response).setStatus(200);
            //处理登录失败的异常
            Throwable throwable = e.getCause() == null ? e : e.getCause();
            Map<String, Object> json = new HashMap<>();
            json.put("status", 4012);
            json.put("error", "token 认证失败");
//            String json = new JSONObject().fluentPut("status", 4012)
//                .fluentPut("error", "token 认证失败").toString();
            httpResponse.getWriter().print(JsonUtils.toJsonString(json));
        } catch (IOException e1) {

        }

        return false;
    }


}
