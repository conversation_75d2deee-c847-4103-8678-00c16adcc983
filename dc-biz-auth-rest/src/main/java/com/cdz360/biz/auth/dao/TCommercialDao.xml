<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.dao.TCommercialDao">
    <resultMap id="BaseResultMap" type="com.cdz360.biz.auth.model.vo.TCommercial">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="topCommId" jdbcType="BIGINT" property="topCommId"/>
        <result column="pid" jdbcType="BIGINT" property="pid"/>
        <result column="comm_level" jdbcType="INTEGER" property="commLevel"/>
        <result column="merchants" jdbcType="VARCHAR" property="merchants"/>
        <result column="comm_type" jdbcType="INTEGER" property="commType"/>
        <result column="comm_name" jdbcType="VARCHAR" property="commName"/>
        <result column="short_name" jdbcType="VARCHAR" property="shortName"/>
        <result column="comm_logo" jdbcType="VARCHAR" property="commLogo"/>
        <result column="comm_icon" jdbcType="VARCHAR" property="commIcon"/>
        <result column="contacts" jdbcType="VARCHAR" property="contacts"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="comm_category" jdbcType="INTEGER" property="commCategory"/>
        <result column="comm_industry" jdbcType="INTEGER" property="commIndustry"/>
        <result column="license" jdbcType="VARCHAR" property="license"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="legal_person" jdbcType="VARCHAR" property="legalPerson"/>
        <result column="province_id" jdbcType="INTEGER" property="provinceId"/>
        <result column="city_id" jdbcType="INTEGER" property="cityId"/>
        <result column="area_id" jdbcType="INTEGER" property="areaId"/>
        <result column="detail" jdbcType="VARCHAR" property="detail"/>
        <result column="is_bond" jdbcType="INTEGER" property="isBond"/>
        <result column="bond_amount" jdbcType="INTEGER" property="bondAmount"/>
        <result column="charge_mode" jdbcType="INTEGER" property="chargeMode"/>
        <result column="bill_type" jdbcType="INTEGER" property="billType"/>
        <result column="business_type" jdbcType="TINYINT" property="businessType"/>
        <result column="company_type" jdbcType="TINYINT" property="companyType"/>
        <result column="registration_amount" jdbcType="BIGINT" property="registrationAmount"/>
        <result column="registration_time" jdbcType="VARCHAR" property="registrationTime"/>
        <result column="business_licence" jdbcType="VARCHAR" property="businessLicence"/>
        <result column="operate_type" jdbcType="TINYINT" property="operateType"/>
        <result column="operate_base_info_id" jdbcType="BIGINT" property="operateBaseInfoId"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="business_status" jdbcType="INTEGER" property="businessStatus"/>
        <result column="service_phone" jdbcType="VARCHAR" property="servicePhone"/>
        <result column="thrid_no" jdbcType="VARCHAR" property="thridNo"/>
        <result column="set_charger_start_timeout" jdbcType="INTEGER" property="setChargerStartTimeout"/>
        <result column="pre_authorization_amount" jdbcType="INTEGER" property="preAuthorizationAmount"/>
        <result column="my_license_code" jdbcType="VARCHAR" property="myLicenseCode"/>
        <result column="agent_level" jdbcType="INTEGER" property="agentLevel"/>
        <result column="agent_level_path" jdbcType="VARCHAR" property="agentLevelPath"/>
        <result column="province_name" jdbcType="VARCHAR" property="provinceName"/>
        <result column="city_name" jdbcType="VARCHAR" property="cityName"/>
        <result column="area_name" jdbcType="VARCHAR" property="areaName"/>
        <result column="out_key" jdbcType="VARCHAR" property="outKey"/>
        <result column="wx_appid" jdbcType="VARCHAR" property="wxAppid"/>
        <result column="wx_app_secret" jdbcType="VARCHAR" property="wxAppSecret"/>
        <result column="sms_app_key" jdbcType="VARCHAR" property="smsAppKey"/>
        <result column="org_code" jdbcType="VARCHAR" property="orgCode"/>
        <result column="token" jdbcType="VARCHAR" property="token"/>
        <result column="callback_url" jdbcType="VARCHAR" property="callbackUrl"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="operateBaseInfoName" jdbcType="VARCHAR" property="operateBaseInfoName"/>
        <result column="parentName" jdbcType="VARCHAR" property="parentName"/>
        <result column="idChain" jdbcType="VARCHAR" property="idChain"/>

        <result column="zftId" jdbcType="BIGINT" property="zftId"/>
        <result column="enableZft" jdbcType="TINYINT" property="enableZft"/>
        <result column="enableDigiccy" jdbcType="BOOLEAN" property="enableDigiccy"/>
        <result column="enableCorpDeposit" jdbcType="BOOLEAN" property="enableCorpDeposit"/>
        <result column="enableCorpRefund" jdbcType="BOOLEAN" property="enableCorpRefund"/>
        <result column="enableCommRefund" jdbcType="BOOLEAN" property="enableCommRefund"/>
        <result column="enableOnlinePay" jdbcType="BOOLEAN" property="enableOnlinePay"/>
        <result column="enableUseScore" jdbcType="BOOLEAN" property="enableUseScore"/>
        <result column="hlhtSitePayType" jdbcType="VARCHAR" property="hlhtSitePayType"/>

        <result column="platform" jdbcType="TINYINT" property="platform"/>
        <result column="platformName" jdbcType="VARCHAR" property="platformName"/>

        <association property="tOperateBaseInfo" javaType="com.cdz360.biz.auth.model.vo.TOperateBaseInfo">
            <id property="id" column="id"/>
            <result column="operate_name" jdbcType="VARCHAR" property="operateName"/>
            <result column="short_name" jdbcType="VARCHAR" property="shortName"/>
            <result column="service_time" jdbcType="VARCHAR" property="serviceTime"/>
            <result column="service_phone" jdbcType="VARCHAR" property="servicePhone"/>
            <result column="remark" jdbcType="VARCHAR" property="remark"/>
            <result column="icon" jdbcType="VARCHAR" property="icon"/>
            <result column="app_name" jdbcType="VARCHAR" property="appName"/>
            <result column="ios_url" jdbcType="VARCHAR" property="iosUrl"/>
            <result column="android_url" jdbcType="VARCHAR" property="androidUrl"/>
            <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
            <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
            <result column="business_status" jdbcType="INTEGER" property="businessStatus"/>
            <result column="create_by" jdbcType="BIGINT" property="createBy"/>
            <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
            <result column="status" jdbcType="TINYINT" property="status"/>
        </association>
    </resultMap>
    <sql id="columns">
        id, pid, comm_level,
        merchants, comm_type, comm_name,
        short_name, comm_logo, comm_icon,
        contacts, phone, email,
        create_time, comm_category, comm_industry,
        license, code, legal_person,
        province_id, city_id, area_id,
        detail, is_bond, bond_amount,
        charge_mode, bill_type, business_type,
        company_type, registration_amount, registration_time,
        business_licence, operate_type, operate_base_info_id,
        status, service_phone, thrid_no,
        set_charger_start_timeout, pre_authorization_amount,
        my_license_code, agent_level, agent_level_path,wx_appid,wx_app_secret,sms_app_key,url
        -- ,
        -- follow_up_people, customer_name
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        select
        tc.*,tp.pname as province_name,ci.cname as city_name,td.dname as area_name,t_zft.name as zftName
        from t_commercial tc left join t_province tp on tc.province_id=tp.id left join t_city ci on tc.city_id = ci.id
        left join t_district td on tc.area_id=td.id
        left join t_zft on t_zft.id = tc.zftId
        where tc.id =#{id}
    </select>

    <select id="selectByUserId" resultMap="BaseResultMap">
        select
        tc.*,
        tp.pname as province_name,ci.cname as city_name,td.dname as area_name
        from t_commercial tc
        inner join sys_user_comercial suc on tc.id=suc.commercial_id
        left join t_operate_base_info tob on tc.operate_base_info_id = tob.id
        left join t_province tp on tc.province_id=tp.id left join t_city ci on tc.city_id = ci.id
        left join t_district td on tc.area_id=td.id
        where suc.user_id=#{userId}
        and tc.status=1
        limit 0,1
    </select>

    <select id="findSimpleVoById" resultType="com.cdz360.biz.model.merchant.vo.CommercialSimpleVo">
        select
            id,
            topCommId ,
            comm_name as commName,
            idChain
        from
            t_commercial
        where
            id = #{id}
        limit 1
    </select>


    <select id="countByUserPageByCommercialUser" resultType="java.lang.Long">
        select count(1) from (
        <include refid="selectUserByCommercialsTemplate"/>
        ) t
    </select>
    <select id="selectByUserPageByCommercialUser"
      resultType="com.cdz360.biz.auth.model.vo.TCommercialUser">

        <include refid="selectUserByCommercialsTemplate"/>
        order by su.update_time desc
        limit #{param.start},#{param.size}
    </select>
    <select id="countByTCommercial" resultType="java.lang.Long">
        select count(1) from t_commercial tc where 1=1
        <if test="param.merchants!=null">
            and merchants=#{param.merchants}
        </if>
        <if test="param.commCategory!=null">
            and comm_category=#{param.commCategory}
        </if>

        <if test="param.commIndustry!=null">
            and comm_industry=#{param.commIndustry}
        </if>

        <if test="param.commType!=null">
            and comm_type=#{param.commType}
        </if>

        <if test="param.commLevel!=null">
            and comm_level=#{param.commLevel}
        </if>

        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( param.sk )">
            and ( merchants like CONCAT(#{param.sk},'%') or short_name like CONCAT(#{param.sk},'%'))
        </if>

    </select>

    <select id="selectPageByCom" resultMap="BaseResultMap">
        select tc.*,tobi.operate_name as operateBaseInfoName,p.comm_name as parentName
        from t_commercial tc
        left join t_operate_base_info tobi on tc.operate_base_info_id=tobi.id
        left join t_commercial p on tc.pid=p.id
        where 1=1
        <if test="param.merchants!=null">
            and tc.merchants=#{param.merchants}
        </if>
        <if test="param.commCategory!=null">
            and tc.comm_category=#{param.commCategory}
        </if>

        <if test="param.commIndustry!=null">
            and tc.comm_industry=#{param.commIndustry}
        </if>

        <if test="param.commType!=null">
            and tc.comm_type=#{param.commType}
        </if>

        <if test="param.commLevel!=null">
            and tc.comm_level=#{param.commLevel}
        </if>

        <if test="param.status!=null">
            and tc.status = #{param.status}
        </if>
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( param.sk )">
            and ( tc.merchants like CONCAT(#{param.sk},'%') or tc.short_name like CONCAT(#{param.sk},'%'))
        </if>

        order by tc.update_time desc
        limit #{param.start},#{param.size}
    </select>

    <sql id="selectUserByCommercialsTemplate">
        select su.* ,suc.commercial_role as commercialRole,su.last_login_time as lastLoginTime from sys_user su ,sys_user_comercial suc
        where su.id = suc.user_id
        and suc.commercial_id = #{param.commercialId}
        <if test="param.status!=null and param.status>0">
            and su.status=#{param.status}
        </if>
        <if test="param.username!=null">
            and su.username like CONCAT(#{param.username},'%')
        </if>
        <if test="param.name!=null">
            and su.name like CONCAT(#{param.name},'%')
        </if>
        <if test="param.phone!=null">
            and su.phone like CONCAT(#{param.phone},'%')
        </if>
        <if test="param.email!=null">
            and su.email=#{param.email}
        </if>
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( param.sk )">
            and ( su.username like CONCAT(#{param.sk},'%') or su.name like CONCAT(#{param.sk},'%') or su.phone
            like CONCAT(#{param.sk},'%') or su.email like CONCAT(#{param.sk},'%'))
        </if>
    </sql>
    <select id="selectUserByCommercials" resultType="com.cdz360.biz.auth.model.vo.SysUser">
        select su.* from sys_user su where su.id in(
        select suc.user_id from sys_user_comercial suc where commercial_id
        <choose>
            <when test="isCurrent">
                = #{commercialId}

            </when>
            <otherwise>
                in (
                select suc.user_id from sys_user_comercial suc
                where commercial_id in ( select tc.id from t_commercial tc where find_in_set(#{commercialId},
                tc.`idChain`)
                )
            </otherwise>
        </choose>
        )
        <if test="status!=null">
            and su.status=#{status}
        </if>
        <if test="username!=null">
            and su.username=#{username}
        </if>
        <if test="name!=null">
            and su.name like CONCAT(#{name},'%')
        </if>
        <if test="phone!=null">
            and su.phone=#{phone}
        </if>
        <if test="email!=null">
            and su.email=#{email}
        </if>
        order by su.update_time
    </select>


    <select id="checkByShortName" resultType="com.cdz360.biz.auth.model.vo.TCommercial">
        SELECT * FROM t_commercial WHERE short_name = #{shortName}
    </select>

    <select id="findByCommIdListAndName" resultType="com.cdz360.biz.auth.model.vo.Commercial">
        select
        id,
        pid,
        comm_level as commLevel,
            merchants,
            comm_name as commName,
            short_name as shortName
        from t_commercial
        <where>
            <if test="commIdList != null and commIdList.size() > 0">
                id in
                <foreach collection="commIdList" item="item" index="index"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="commName != null">
                and comm_name like concat('%', #{commName}, '%')
            </if>
        </where>
    </select>


    <select id="getCommercialList" parameterType="com.cdz360.biz.model.merchant.param.ListCommercialParam"
            resultType="com.cdz360.biz.model.merchant.dto.CommercialDto">
        select
        c.id,
        c.pid,
        c.topCommId,
        c.comm_type as commType,
        c.comm_level as commLevel,
        c.merchants,
        c.comm_name as commName,
        c.short_name as shortName,
        c.contacts,
        c.phone,
        c.comm_category as commCategory,
        comm_industry as commIndustry,
        c.idChain,
        c.zftId,
        c.enableZft,
        c.enableOnlinePay,
        c.enableCommRefund,
        zft.wxMchId,
        zft.wxSubMchId,
        zft.alipayMchId,
        zft.alipaySubMchId
        from t_commercial c
        left join t_zft zft
        on c.zftId = zft.id
        where status = 1
        <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( commIdList )">
            and c.id in
            <foreach collection="commIdList" item="commId" open="("
                     separator="," close=")">
                #{commId}
            </foreach>
        </if>
        <if test="level != null">
            and c.comm_level = #{level}
        </if>
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commNameExact )">
            and c.comm_name = #{commNameExact}
        </if>
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
            and c.idChain like CONCAT(#{commIdChain}, '%')
        </if>
        order by c.idChain
        limit #{start},#{size}
    </select>


    <update id="updateIdChain">
        update t_commercial set comm_level = #{commLevel}, idChain = #{idChain} where id = #{id}
    </update>
    <update id="updateRefundStatus">
        UPDATE t_commercial trc
        LEFT JOIN t_commercial tc ON tc.idChain LIKE concat( trc.idChain, '%' )
        SET tc.refundStatus = #{refundStatus}
        WHERE
            trc.zftId = tc.zftId
            and
            trc.id = #{commId}
            AND tc.zftId > 0;
    </update>
    <update id="updateRefundStatusByzftId">
        update t_commercial set refundStatus = #{refundStatus} where zftId=#{zftId}
    </update>

    <select id="getLeastPrivilegesCommLevel" resultType="java.lang.Integer">
        select
            comm_level
        from
            t_commercial
        where
            `idChain` like CONCAT(#{idChain},'%')
            order by length(`idChain`) desc
            limit 1;
    </select>

    <select id="hasZftId" resultType="java.lang.Long">
        select c.id
        from t_commercial c
        where c.zftId > 0 and c.zftId != #{excludeZftId}
        and c.id in
        <foreach collection="commIdList" item="commId" open="("
                 separator="," close=")">
            #{commId}
        </foreach>
        limit 1
    </select>
    <select id="getCommListByIdChain" resultType="com.cdz360.biz.auth.model.vo.TCommercial">
        SELECT
        trc.id AS id,
        trc.topCommId,
        pid,
        comm_level AS commLevel,
        merchants,
        comm_type AS commType,
        comm_name AS commName,
        short_name AS shortName,
        comm_logo AS commLogo,
        comm_icon AS commIcon,
        contacts,
        phone,
        email,
        comm_category AS commCategory,
        comm_industry AS commIndustry,
        license,
        CODE,
        legal_person AS legalPerson,
        province_id AS provinceId,
        city_id AS cityId,
        area_id AS areaId,
        detail,
        is_bond AS isBond,
        bond_amount AS bondAmount,
        charge_mode AS chargeMode,
        bill_type AS billType,
        business_type AS businessType,
        company_type AS companyType,
        registration_amount AS registrationAmount,
        registration_time AS registrationTime,
        business_licence AS businessLicence,
        operate_type AS operateType,
        operate_base_info_id AS operateBaseInfoId,
        `status`,
        business_status AS businessStatus,
        service_phone AS servicePhone,
        thrid_no AS thridNo,
        set_charger_start_timeout AS setChargerStartTimeout,
        pre_authorization_amount AS preAuthorizationAmount,
        my_license_code AS myLicenseCode,
        agent_level AS agentLevel,
        agent_level_path AS agentLevelPath,
        out_key AS outKey,
        org_code AS orgCode,
        token,
        callback_url AS callbackUrl,
        wx_appid AS wxAppid,
        wx_app_secret AS wxAppSecret,
        sms_app_key AS smsAppKey,
        zftId,
        enableZft,
        trc.enableCorpDeposit,
        trc.enableCorpRefund,
        trc.enableCommRefund,
        trc.enableOnlinePay,
        trc.enableUseScore,
        url,
        idChain,
        create_time AS createTime,
        update_time AS updateTime,
        create_by AS createBy,
        update_by AS updateBy,
        t_zft.name as zftName
<!--        t_zft.enableRefund-->
        FROM
        t_commercial trc left join t_zft on trc.zftId = t_zft.id
        WHERE
        ( STATUS = 1 )

        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
            and idChain like CONCAT(#{commIdChain}, '%')
        </if>
        order by idChain
    </select>
    <select id="getZftNameById" resultType="com.cdz360.biz.auth.zft.po.ZftPo">
        select *  from t_zft where id = #{id}
    </select>
    <select id="getCommIdList" resultType="java.lang.Long">
        SELECT
            tc.id
        FROM
            t_commercial trc
            LEFT JOIN t_commercial tc ON tc.idChain LIKE concat( trc.idChain, '%' )
        WHERE
            trc.id = #{commId};
    </select>
    <select id="getCommercialIdList" resultType="java.lang.Long">
        select id from t_commercial where status=1
    </select>
    <select id="getCommercialInfoByMerchant" resultType="com.cdz360.biz.auth.model.vo.TCommercial">
        select * from t_commercial where merchants = #{merchants}
    </select>
    <select id="getCommercialInfoByIdList" resultType="com.cdz360.biz.auth.model.vo.TCommercial">
        select id,comm_logo as commLogo,platform,platformName from t_commercial
        where
         id IN
        <foreach collection="idList" index="index" item="item" open="("
                 separator="," close=")">
            #{item}
        </foreach>
        and comm_logo is not null and comm_logo != ''
        order by id desc limit 1
    </select>


    <update id="editHlhtSitePayType">
        update
            t_commercial
        set
            `hlhtSitePayType` = #{hlhtSitePayType}
        where
            id = #{commId}
    </update>

</mapper>