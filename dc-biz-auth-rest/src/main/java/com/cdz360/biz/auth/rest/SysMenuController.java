package com.cdz360.biz.auth.rest;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.auth.config.SysLog;
import com.cdz360.biz.auth.dao.SysRoleMenuDao;
import com.cdz360.biz.auth.model.constant.GlobalConst;
import com.cdz360.biz.auth.model.dto.PageDto;
import com.cdz360.biz.auth.model.type.ErrStatus;
import com.cdz360.biz.auth.model.vo.*;
import com.cdz360.biz.auth.service.SysMenuService;
import com.cdz360.biz.auth.service.SysRoleService;
import com.cdz360.biz.auth.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/data/menus")
public class SysMenuController //implements SysMenuInterface
{


    @Autowired
    private SysMenuService menuService;
    @Autowired
    private SysUserService userService;
    @Autowired
    private SysRoleService sysRoleService;
    @Autowired
    private SysRoleMenuDao sysRoleMenuDao;


    @SysLog("菜单分页")
    @GetMapping("/page")
    public ListResponse<SysMenu> page(PageDto pageDto) {
        Page<SysMenu> sysMenuPage = menuService.getBaseMapper().selectPage(new Page<>(0L, pageDto.getPage(), pageDto.getSize()),
            Wrappers.query(SysMenu.class));
        ListResponse<SysMenu> res = new ListResponse<SysMenu>(sysMenuPage.getRecords(), sysMenuPage.getTotal());
        return res;
        //return Rez.data(sysMenuPage);
    }

    /**
     * 查询 用户的权限
     *
     * @param userId 用户id
     * @return 权限列表
     */
    //
    @GetMapping("/{userId}/perms")
    public Set<String> findPermByUserId(@PathVariable Long userId) {
        return menuService.findPermByUserId(userId);
    }


    @SysLog("菜单查询-id")
    @GetMapping("{id}")
    public Rez<SysMenu> findById(@PathVariable Long id) {
        SysMenu sysMenu = menuService.getBaseMapper().selectById(id);
        return Rez.ofNullable(sysMenu);
    }

    /**
     * 全部菜单 ,按系统分组
     *
     * @param sysId 搜索条件 子系统id
     * @param name  菜单名称
     * @return
     */
    @SysLog("菜单-按系统分组")
    @GetMapping("/group-by-sys")
    public Rez groupBySys(Long sysId, String name) {
        List<SysSystem> sysSystems = userService.querySystemMenuTree(null, sysId, name);
        return Rez.data(sysSystems);
    }


    @SysLog("菜单添加")
    @PostMapping()
    public Rez<SysMenu> add(@RequestBody SysMenu menu) {
        if (menu.getPid() != null && menu.getPid() != 0) {
            menuService.fillPids(menu);
        } else {
            menu.setPids("0");
        }
        //判断重复
        Long count = menuService.countMenuByUniqueKeyAndOtherId(menu.getSubsysId(), menu.getName(), menu.getType(), null);
        if (count > 0) {
            return Rez.error(4101, "菜单不能重复");
        }

        /**
         * 新增页面时，充电管理平台，站点->站点管理,新增页面，默认隐藏
         * 勾选站点详情权限的角色，默认添加新增页面的权限
         */
        SysMenu menuInfo = new SysMenu();
        String url = "site-mgm";
        Long subSysId = 56L;
        if (menu.getPid() != null) {
            menuInfo = menuService.getMenuById(menu.getPid());
            if (menuInfo != null && url.equals(menuInfo.getUrl()) && subSysId.equals(menuInfo.getSubsysId())) {
                menu.setOpened(Boolean.FALSE);
            }
        }
        int insert = menuService.getBaseMapper().insert(menu);

        //站点管理下的页面具备站点详情权限的，角色添加新增权限
        if (menuInfo != null && url.equals(menuInfo.getUrl()) && subSysId.equals(menuInfo.getSubsysId())) {
            List<SysRole> roleList = sysRoleService.findRoleHasSiteDetailAuth();
            if (CollectionUtils.isNotEmpty(roleList)) {
                List<Long> roleIds = roleList.stream().map(SysRole::getId).collect(Collectors.toList());
                sysRoleMenuDao.saveBatchByMenuId(menu.getId(),roleIds);
            }
        }

        return Rez.hasModified(insert > 0, menu);
    }


    @SysLog("菜单修改")
    @PutMapping("{id}")
    public Rez<SysMenu> modify(@PathVariable Long id, @RequestBody SysMenu menu) {
        log.info("编辑菜单: menu = {}", JsonUtils.toJsonString(menu));
        //数据不一致
        if (menu.getId() != null && !id.equals(menu.getId())) {
            return Rez.error(ErrStatus.REQ_CONFLICT);
        }
        menu.setId(id);

        if (menu.getPid() != null && menu.getPid() != 0) {
            menuService.fillPids(menu);
        } else {
            menu.setPids("0");
        }

        //判断重复
        Long count = menuService.countMenuByUniqueKeyAndOtherId(menu.getSubsysId(), menu.getName(), menu.getType(), id);
        if (count > 0) {
            return Rez.error(4101, "菜单不能重复");
        }
        boolean update = menuService.updateById(menu);
        if (update) {
            if (menu.getStatus() == GlobalConst.FREEZE) { // 禁用后，下级页面也禁用

                List<SysMenu> parents = menuService.getBaseMapper().selectList(Wrappers.query(SysMenu.class).eq("pid", id));
                parents.forEach(sysMenu -> {
                    sysMenu.setStatus(GlobalConst.FREEZE);
                    menuService.updateById(sysMenu);
                });
            }
        }

        // 角色菜单调整
        if (null != menu.getType() && SysMenu.MENU_TYPE == menu.getType()) {
            menuService.postModifyMenu(menu);
        }

        return Rez.hasModified(update, menuService.getBaseMapper().selectById(id));
    }


    @SysLog("菜单删除")
    @DeleteMapping("{id}")
    public Rez<SysMenu> delete(@PathVariable Long id) {
        log.info("删除菜单: menuId = {}", id);
        int b = menuService.getBaseMapper().deleteById(id);
        if (b > 0) {
            // 角色菜单关系需要删除
            try {
                sysRoleMenuDao.deleteByMenuId(id);
            } catch (Exception e) {
                log.error("删除角色菜单关系(需要手动处理，不处理关系不大): menuId = {}", id);
            }

            List<SysMenu> pid = menuService.getBaseMapper().selectList(Wrappers.query(SysMenu.class).eq("pid", id));
            pid.forEach(sysMenu -> delete(sysMenu.getId()));
        }
        return Rez.hasModified(b > 0);
    }
}
