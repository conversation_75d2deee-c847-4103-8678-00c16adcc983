<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.ds.rw.user.mapper.SiteGroupRwMapper">

  <insert id="insertSiteGroup">
    insert into t_site_group (gid,name,
    <if test="null != type">
      type,
    </if>
    <if test="null != ownType">
      ownType,
    </if>
    platform,selectAll,adminUid,createTime,updateTime)
    values (#{gid},#{name},
    <if test="null != type">
      #{type.code},
    </if>
    <if test="null != ownType">
      #{ownType.code},
    </if>
    #{clientType.code},#{selectAll},#{adminUid},now(),now())
  </insert>

  <update id="updateSiteGroup">
    update t_site_group
    <set>
      <if test="null != name">
        name = #{name},
      </if>
      <if test="null != type">
        type = #{type.code},
      </if>
      <if test="null != ownType">
        ownType = #{ownType.code},
      </if>
      <if test="null != adminUid">
        adminUid = #{adminUid},
      </if>
      <if test="null != selectAll">
        selectAll = #{selectAll},
      </if>
      updateTime=NOW()
    </set>
    where gid = #{gid}
  </update>
  <update id="disableByGid">
    update t_site_group
    set enable = false
    where gid = #{gid}
  </update>

  <delete id="deleteByGid">
    delete from t_site_group where gid = #{gid}
  </delete>


</mapper>

