<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.ds.ro.corp.mapper.CorpRoMapper">

  <resultMap id="CorpPoMap" type="com.cdz360.biz.model.cus.corp.po.CorpPo">
    <id column="id" property="id"/>
    <result column="topCommId" property="topCommId"/>
    <result column="commId" property="commId"/>
    <result column="sysUid" property="sysUid"/>
    <result column="uid" property="uid"/>
    <result column="corpName" property="corpName"/>
    <result column="contactName" property="contactName"/>
    <result column="phone" property="phone"/>
    <result column="type" property="type"/>
    <result column="email" property="email"/>
    <result column="province" property="province"/>
    <result column="city" property="city"/>
    <result column="district" property="district"/>
    <result column="address" property="address"/>
    <result column="businessImage" property="businessImage"/>
    <result column="enable" property="enable"/>
    <result column="settlementType" property="settlementType"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="invoiceWay" property="invoiceWay"/>
    <result column="renewReminderAmount" property="renewReminderAmount"/>
    <result column="isSendReminderEmail" property="isSendReminderEmail"/>
    <result column="digest" property="digest"/>
    <result column="creatorId" property="creatorId"/>
    <result column="creatorName" property="creatorName"/>
    <result column="createTime" property="createTime"/>
    <result column="updateTime" property="updateTime"/>
  </resultMap>

  <resultMap id="CusCorpPoMap" type="com.cdz360.biz.model.cus.corp.po.CorpPo">
    <id column="id" property="id"/>
    <result column="topCommId" property="topCommId"/>
    <result column="commId" property="commId"/>
    <result column="sysUid" property="sysUid"/>
    <result column="uid" property="uid"/>
    <result column="corpName" property="corpName"/>
    <result column="contactName" property="contactName"/>
    <result column="phone" property="phone"/>
    <result column="type" property="type"/>
    <result column="email" property="email"/>
    <result column="province" property="province"/>
    <result column="city" property="city"/>
    <result column="district" property="district"/>
    <result column="address" property="address"/>
    <result column="businessImage" property="businessImage"/>
    <result column="enable" property="enable"/>
    <result column="settlementType" property="settlementType"
      typeHandler="com.cdz360.ds.type.EnumTypeHandler"/>
    <result column="invoiceWay" property="invoiceWay"/>
    <result column="createTime" property="createTime"/>
    <result column="updateTime" property="updateTime"/>
  </resultMap>

  <resultMap id="SampleBaseResultMap" type="com.cdz360.biz.model.cus.corp.po.CorpOrgPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="corpId" jdbcType="BIGINT" property="corpId"/>
    <result column="orgName" jdbcType="VARCHAR" property="orgName"/>
    <result column="orgLevel" jdbcType="INTEGER" property="orgLevel"/>
    <result column="l1Id" jdbcType="BIGINT" property="l1Id"/>
    <result column="l2Id" jdbcType="BIGINT" property="l2Id"/>
    <result column="enable" jdbcType="TINYINT" property="enable"/>
    <result column="account" jdbcType="VARCHAR" property="account"/>
    <result column="password" jdbcType="VARCHAR" property="password"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>
  <sql id="selectCorpColumns">
    c.id,
    c.topCommId, c.uid, c.commId,
    c.corpName as corpName, c.contactName,
    c.phone, c.email,
    c.type,
    c.province, c.city, c.district, c.address,
    c.businessImage,
    c.creatorId,
    c.creatorName,
    <!--c.createTime,-->
    user.username as account,
    c.`enable`,
    c.settlementType,
    c.invoiceWay,
    c.digest
    <!--c.updateTime-->
  </sql>

  <sql id="where_query_queryBlocUserByName">
    <if test="commId != null">
      AND c.commId = #{commId}
    </if>
    <if test="settlementType != null">
      AND c.settlementType = #{settlementType}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( invoiceWay )">
      AND c.invoiceWay = #{invoiceWay}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( corpName )">
      and c.corpName like CONCAT('%', #{corpName}, '%')
    </if>
    <if test="corpType != null">
      and c.type = #{corpType}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and d.idChain like CONCAT(#{commIdChain}, '%')
    </if>
    <if test="commIdList!=null and commIdList.size()>0">
      AND c.commId in
      <foreach collection="commIdList" index="index" item="item" open="("
        separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="idList!=null and idList.size()>0">
      AND c.id in
      <foreach collection="idList" index="index" item="item" open="("
        separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="uidList !=null and uidList.size()>0">
      AND c.uid in
      <foreach collection="uidList" index="index" item="item" open="("
        separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="sk!=null and ''!=sk">
      AND (
      c.corpName LIKE concat('%',#{sk},'%') OR
      user.username LIKE concat('%',#{sk},'%') OR
      c.contactName LIKE concat('%',#{sk},'%') OR
      c.phone LIKE concat('%',#{sk},'%')
      )
    </if>
    <if test="enable != null">
      and c.`enable` = #{enable}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( referrer )">
      and c.creatorName like concat('%', #{referrer}, '%')
    </if>

  </sql>

  <sql id="Base_Select_Sql">
    id,corpId,orgName,orgLevel,l1Id,l2Id,enable,createTime,updateTime
  </sql>
  <select id="getCorpCountByCorpName" resultMap="CorpPoMap">
    select * from t_corp where corpName=#{blocUser}
  </select>
  <select id="getCorpCountByAccount" resultMap="CorpPoMap">
    select * from t_corp where corpName=#{blocUser}
  </select>
  <select id="getCorpById" resultMap="CorpPoMap">
    select * from t_corp where id=#{id}
  </select>
  <select id="getCorpByUid" resultMap="CorpPoMap">
    select * from t_corp where uid = #{uid}
  </select>
  <select id="getCorpOrgList" resultType="com.cdz360.biz.model.cus.corp.vo.CorpOrgVO">
    SELECT
    a.id,
    a.orgName,
    a.orgLevel,
    b.orgName AS pName,
    b.id as parentId
    FROM
    `t_corp_org` a
    LEFT JOIN `t_corp_org` b ON a.l2Id = b.id
    WHERE
    a.orgLevel = 3 and a.corpId=#{corpId}
    UNION
    SELECT
    c.id,
    c.orgName,
    c.orgLevel,
    d.orgName AS pName,
    d.id as parentId
    FROM
    `t_corp_org` c
    LEFT JOIN `t_corp_org` d ON c.l1Id = d.id
    WHERE
    c.orgLevel = 2 and c.corpId=#{corpId}
    UNION
    SELECT
    e.id,
    e.orgName,
    e.orgLevel,
    '--' AS pName,
    null as parentId
    FROM
    `t_corp_org` e
    WHERE
    e.orgLevel = 1 and e.corpId=#{corpId}
  </select>
  <select id="getCorpOrgByLevel" resultType="com.cdz360.biz.model.cus.corp.vo.CorpOrgVO">
    SELECT
    org.id,
    orgName,
    orgLevel
    FROM `t_corp_org` org
    <if test="userLevel !=null and userLevel == 1">
      where corpId =#{corpId}
      and orgLevel =#{orgLevel}
    </if>
    <if test="userLevel !=null and userLevel != 1">
      left join t_corp_user_org user_org on org.id = user_org.orgId where user_org.corpId =#{corpId}
      and orgLevel =#{orgLevel} and user_org.sysUid= #{userId}
    </if>
  </select>

  <select id="getOrgInfoByLevel" resultType="com.cdz360.biz.model.cus.corp.po.CorpOrgPo">
    SELECT
    *
    FROM `t_corp_org`
    where corpId =#{corpId}
    and orgLevel =#{orgLevel}
    limit 1
  </select>

  <select id="getCorpOrgByOrgId" resultType="com.cdz360.biz.model.cus.corp.po.CorpOrgPo">
    SELECT
    <include refid="Base_Select_Sql"/>
    FROM `t_corp_org` where id =#{id}
  </select>
  <select id="getOrgByUserId" resultType="com.cdz360.biz.model.cus.corp.vo.CorpOrgVO">
    SELECT
    org.id,org.corpId,org.orgName,org.orgLevel,org.l1Id,org.l2Id
    FROM
    t_corp_org org
    LEFT JOIN t_corp_user_org USER ON org.id = USER.orgId
    WHERE
    sysUid = #{cusId}
    UNION
    SELECT
    org.id,org.corpId,org.orgName,org.orgLevel,org.l1Id ,org.l2Id
    FROM
    t_corp_org org
    LEFT JOIN t_corp_user_org USER ON org.l2Id = USER.orgId
    WHERE
    sysUid = #{cusId}
    union
    SELECT
    org.id,org.corpId,org.orgName,org.orgLevel,org.l1Id,org.l2Id
    FROM
    t_corp_org org
    LEFT JOIN t_corp_user_org USER ON org.l1Id = USER.orgId
    WHERE
    sysUid = #{cusId}
  </select>
  <select id="getCorp" resultType="com.cdz360.biz.model.cus.corp.po.CorpPo">
    select
    user.username as account,
    address as address,
    -- balance,
    corpName as blocUserName,
    businessImage,
    city,
    c.commId,
    contactName,
    -- createTime as createDate,
    district,
    c.email,
    c.id,
    c.phone,
    c.type,
    c.sysUid,
    c.uid,
    c.topCommId,
    c.renewReminderAmount,
    c.isSendReminderEmail,
    c.fullInvoicing,
    province,
    c.digest
    -- updateTime
    from t_corp c left join sys_user user on c.sysUid = user.id where c.id = #{corpId}
    <if test="lock == true">
      for update
    </if>
  </select>

  <select id="getOrgByL1Id" parameterType="map" resultMap="SampleBaseResultMap">
    SELECT
    <include refid="Base_Select_Sql"/>
    FROM `t_corp_org` where corpId =#{corpId} and l1Id =#{l1Id}
    <if test="orgName != null and orgName != ''">
      and orgName like concat('%',#{orgName},"%")
    </if>
  </select>
  <select id="getOrgByL2Id" parameterType="map" resultMap="SampleBaseResultMap">
    SELECT
    <include refid="Base_Select_Sql"/>
    FROM `t_corp_org` where corpId =#{corpId} and l2Id =#{l2Id}
    <if test="orgName != null and orgName != ''">
      and orgName like concat('%',#{orgName},"%")
    </if>
  </select>
  <select id="getCorpList" resultMap="CusCorpPoMap">
    select
    <include refid="selectCorpColumns"/>
    ,d.comm_name as commName
    FROM t_corp as c
    left join t_commercial d on c.commId = d.id
    left join sys_user user on user.id = c.sysUid
    <where>
      <include refid="where_query_queryBlocUserByName"/>
    </where>
    AND d.status = 1
    ORDER BY c.id desc
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <when test="size != null">
        limit #{size}
      </when>
    </choose>
  </select>
  <select id="getCorpCount" resultType="java.lang.Long">
    select count(distinct c.id)
    FROM t_corp as c
    left join t_commercial d on d.id = c.commId
    left join sys_user user on user.id = c.sysUid
    <where>
      <include refid="where_query_queryBlocUserByName"/>
    </where>
    and d.status = 1
  </select>
  <select id="getCorpByCommId" resultType="com.cdz360.biz.model.cus.corp.vo.CorpSimpleVo">
    select
    corp.id as corpId,
    corp.corpName,
    corp.commId,
    corp.enable,
    tc.comm_name commName
    from t_corp corp
    left join t_commercial tc on tc.id = corp.commId
    where tc.status = 1
    and tc.idChain like concat(#{commIdChain},"%")
    <if test="enable != null">
      and corp.enable = #{enable}
    </if>
    <if test="corpId != null">
      and corp.id = #{corpId}
    </if>
  </select>

  <select id="getNeedRemindCorp" resultMap="CorpPoMap">
    select
    *
    from
    t_corp
    where
    enable = 1
    and `settlementType` = 1
    and `isSendReminderEmail` = 0
    and `renewReminderAmount` >= 0
    limit #{start}, #{size}
  </select>

</mapper>