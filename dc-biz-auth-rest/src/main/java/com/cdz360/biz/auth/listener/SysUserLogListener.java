package com.cdz360.biz.auth.listener;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.auth.ds.rw.user.SysUserLogRwDs;
import com.cdz360.biz.auth.user.po.SysUserLogPo;
import com.cdz360.biz.model.common.constant.DcBizConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Slf4j
@Component
@RabbitListener(queues = DcBizConstants.MQ_QUEUE_AUTH_SYS_USER_LOG)
public class SysUserLogListener {

    @Autowired
    private SysUserLogRwDs sysUserLogRwDs;

    @RabbitHandler
    public void sysUserLogListener(String msg) {
        log.info(">> sysUserLog msg = {}", msg);
        try {
            SysUserLogPo uLog = JsonUtils.fromJson(msg, SysUserLogPo.class);
            sysUserLogRwDs.insertSysUserLog(uLog);
        } catch (Exception e) {
            log.error("处理系统操作日志失败 {}", e.getMessage(), e);
        }
    }
}
