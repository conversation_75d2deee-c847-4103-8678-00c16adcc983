package com.cdz360.biz.auth.ds.ro.user;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.ds.ro.user.mapper.SiteGroupRoMapper;
import com.cdz360.biz.auth.utils.IotAssert;
import com.cdz360.biz.model.sys.constant.SiteGroupType;
import com.cdz360.biz.model.sys.dto.UserOwnerSiteGroupDto;
import com.cdz360.biz.model.sys.param.ListSiteGroupParam;
import com.cdz360.biz.model.sys.po.SiteGroupPo;
import com.cdz360.biz.model.sys.vo.SiteGroupVo;
import com.cdz360.biz.model.sys.vo.UserGroupVo;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import jakarta.annotation.Nonnull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class SiteGroupRoDs {

    @Autowired
    private SiteGroupRoMapper siteGroupRoMapper;

    public SiteGroupPo getByGid(String gid) {
        return this.siteGroupRoMapper.getByGid(gid);
    }

    public SiteGroupPo getOneByName(String name, String exGid) {
        return this.siteGroupRoMapper.getOneByName(name, exGid);
    }

    public Mono<SiteGroupVo> getSiteGroupInfo(String gid) {
        IotAssert.isNotBlank(gid, "场站组ID无效");
        return Mono.just(gid)
            .map(siteGroupRoMapper::getSiteGroupVoByGid);
    }

    public Mono<ListResponse<SiteGroupVo>> findSiteGroup(ListSiteGroupParam param) {
        if (null == param.getSize()) {
            param.setSize(100);
        }
        // 场站组为空时就返回空，否则缺少这个过滤条件了
        if (param.getNeedGidList() && CollectionUtils.isEmpty(param.getGidList())) {
            return Mono.just(new ArrayList<SiteGroupVo>())
                .map(RestUtils::buildListResponse);
        }
        return Mono.just(param)
            .map(siteGroupRoMapper::findAll)
//            .map(this::map2Vo)
            .map(RestUtils::buildListResponse)
            .doOnNext(res -> {
                if (null != param.getTotal() && param.getTotal()) {
                    res.setTotal(siteGroupRoMapper.count(param));
                }
            });
    }

    public Mono<ListResponse<SiteGroupVo>> findSiteGroupAndUser(ListSiteGroupParam param) {
        if (null == param.getSize()) {
            param.setSize(100);
        }
        return Mono.just(param)
            .map(siteGroupRoMapper::findSiteGroupAndUser)
            .map(RestUtils::buildListResponse)
            .doOnNext(res -> {
                if (null != param.getTotal() && param.getTotal()) {
                    res.setTotal(siteGroupRoMapper.count(param));
                }
            });
    }

    public List<UserGroupVo> findUserGroup(ListSiteGroupParam param) {
        return siteGroupRoMapper.findList(param);
    }

    private List<SiteGroupVo> map2Vo(List<SiteGroupPo> poList) {
        return poList.stream().map(this::map2Vo).collect(Collectors.toList());
    }

    private SiteGroupVo map2Vo(SiteGroupPo po) {
        final SiteGroupVo result = new SiteGroupVo();
        result.setGid(po.getGid())
            .setName(po.getName())
            .setType(po.getType());
        return result;
    }

    public List<UserOwnerSiteGroupDto> userOwnerSiteGroup(ListSiteGroupParam param) {
        if (CollectionUtils.isEmpty(param.getUidList())) {
            return List.of();
        }
        return siteGroupRoMapper.userOwnerSiteGroup(param);
    }

    public UserOwnerSiteGroupDto getUserOwnerSiteGroup(Long uid, SiteGroupType groupType) {
        return siteGroupRoMapper.getUserOwnerSiteGroup(uid, groupType);
    }

    public List<SiteGroupPo> getSiteGroupList(ListSiteGroupParam param) {
        return siteGroupRoMapper.getSiteGroupList(param);
    }

    public Long getHighProrityAdminUid(@Nonnull List<SiteGroupType> typeList,
        @Nonnull List<String> gidList) {
        return siteGroupRoMapper.getHighProrityAdminUid(typeList, gidList);
    }

}
