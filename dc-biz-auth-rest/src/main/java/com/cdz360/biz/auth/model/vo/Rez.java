package com.cdz360.biz.auth.model.vo;

import com.cdz360.biz.auth.model.type.ErrStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class Rez<T> {
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private int errcode;
    private int status;
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String errmsg;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String error;
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private T data;

    public Rez() {
    }

    public Rez(int errcode, String errmsg) {
        this.status = errcode;
        this.error = errmsg;
    }

    public Rez(ErrStatus status) {
        this.status = status.getErrcode();
        this.error = status.getErrmsg();
    }

    public static <T> Rez<T> error(int errcode, String errmsg) {
        Rez<T> rez = new Rez<>(errcode, errmsg);
        return rez;
    }


    public static <T> Rez<T> error(ErrStatus errStatus) {
        Rez<T> rez = new Rez<>(errStatus);
        return rez;
    }

    public static <T> Rez<T> data(@NotNull T data) {
        Rez<T> rez = new Rez<>(ErrStatus.OK);
        rez.data = data;
        return rez;
    }


    public static <T> Rez<T> ok() {
        Rez<T> rez = new Rez<>(ErrStatus.OK);
        return rez;
    }

    public static <T> Rez<T> ok(String errmsg) {
        Rez<T> objectRez = new Rez<>();
        objectRez.status = 0;
        objectRez.error = errmsg;
        return objectRez;
    }

    public static <T> Rez<T> ofNullable(T data) {
        return data == null ? error(ErrStatus.RES_NOT_FOUND) : data(data);
    }

    public static <T> Rez<T> hasModified(boolean modified) {
        return modified ? ok() : error(ErrStatus.RES_NOT_MODIFY);
    }

    public static <T> Rez<T> hasModified(boolean modified, T entity) {
        return modified ? data(entity) : error(ErrStatus.RES_NOT_MODIFY);
    }

    public boolean isSuccess() {
        return this.status == 0;
    }


    public int getStatus() {
        return status;
    }

    public String getError() {
        if (status != 0) {
            return error;
        } else {
            return "";
        }
    }
}
