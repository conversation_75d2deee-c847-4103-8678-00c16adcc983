package com.cdz360.biz.auth.ds.rw.sys;

import com.cdz360.biz.auth.ds.rw.sys.mapper.SysUserReportTemplateRwMapper;
import com.cdz360.biz.model.sys.po.SysUserReportTemplatePo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
public class SysUserReportTemplateRwDs {

    @Autowired
    private SysUserReportTemplateRwMapper mapper;

    public int add(SysUserReportTemplatePo template) {
        return mapper.insert(template);
    }

    public int delete(Long templateId) {
        return mapper.delete(templateId);
    }
}
