package com.cdz360.biz.auth.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class SysUserVo extends SysUser {
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date expireTime;

    @Schema(description = "所属审核组名称','拼接")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String auditGroupNameJoin;

    @Schema(description = "所属审核组ID列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> auditGroupIdList;

    @Schema(description = "站点数量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long siteAmount;
}
