package com.cdz360.biz.auth.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdz360.biz.auth.sys.param.QuerySysUserRequest;
import com.cdz360.biz.auth.model.vo.SysRole;
import com.cdz360.biz.auth.model.vo.SysUser;
import java.util.HashMap;
import java.util.List;
import org.apache.ibatis.annotations.Param;

//@Repository
public interface SysUserDao extends BaseMapper<SysUser> {

    SysUser queryByUserNameOrPhoneOrEmail(String username);

    SysUser queryByUserName(@Param("username") String username,
        @Param("platform") Integer platform);

    List<SysUser> checkByUserNameOrEmail(SysUser su);

    // 注意: t_commercial 中的 phone 是唯一，所以要带上手机号作为查询条件
    List<SysUser> checkByUserNameOrPhoneOrEmail(SysUser su);

    int checkByPhoneAndPlantform(@Param("phone") String phone, @Param("plantform") int plantform,
        @Param("sysUserId") Long sysUserId);

    int checkUserNameUniqueAndPlantform(@Param("username") String username,
        @Param("plantform") int plantform, @Param("sysUserId") Long sysUserId);

//    List<SysPosition> findUserPositionList(Long userId);

//    Integer insertUserPositionList(@Param("userId") Long userId, @Param("posts") List<SysPosition> positions, @Param("curuid") Long curUserId);

    Integer purgeByUserId(Long userId);

    /**
     * 岗位是否正在被 *未冻结*的用户引用
     *
     * @param postId 岗位id
     * @return 被引用的数量(未去重)
     */
    int countPostsRefByUser(Long postId);

    List<SysRole> findUserRoleList(Long userId);

    List<SysUser> selectByPositionId(Long id);

    /**
     * 根据id集合查询用户信息
     *
     * @param map
     * @return
     */
    List<SysUser> querySysUserByIds(HashMap map);

    List<SysUser> querySysUser(QuerySysUserRequest querySysUserRequest);

    long querySysUserCount(QuerySysUserRequest querySysUserRequest);

    List<SysUser> sameCorpWxAppNameSysUser(QuerySysUserRequest querySysUserRequest);

    long countSameCorpWxAppNameSysUser(QuerySysUserRequest querySysUserRequest);
}
