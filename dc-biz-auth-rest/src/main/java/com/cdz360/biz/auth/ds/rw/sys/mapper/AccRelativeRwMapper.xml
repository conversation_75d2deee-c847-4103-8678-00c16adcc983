<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.ds.rw.sys.mapper.AccRelativeRwMapper">

    <insert id="insertOrUpdate" parameterType="com.cdz360.biz.auth.sys.po.AccRelativePo">
        INSERT INTO t_acc_relative
            (sysUid, relativeCode, `work`,
            siteIdList,
            priority,
            <if test="updateOpType != null">
                updateOpType,
            </if>
            <if test="updateOpUid != null">
                updateOpUid,
            </if>
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( updateOpName )">
                updateOpName,
            </if>
            createTime
        )
        VALUES
            (#{sysUid}, #{relativeCode}, #{work},
            #{siteIdList, typeHandler=com.cdz360.biz.auth.ds.MybatisJsonTypeHandler},
            #{priority},
            <if test="updateOpType != null">
                #{updateOpType},
            </if>
            <if test="updateOpUid != null">
                #{updateOpUid},
            </if>
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( updateOpName )">
                #{updateOpName},
            </if>
            now()
        )
        ON DUPLICATE KEY UPDATE
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( relativeCode )">
            relativeCode = #{relativeCode},
        </if>
        <if test="work != null">
            `work` = #{work},
        </if>
        <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
            siteIdList = #{siteIdList, typeHandler=com.cdz360.biz.auth.ds.MybatisJsonTypeHandler},
        </if>
        <if test="xjOrderNum != null">
            xjOrderNum = #{xjOrderNum},
        </if>
        <if test="priority != null">
            priority = #{priority},
        </if>
        <if test="ywOrderNum != null">
            ywOrderNum = #{ywOrderNum},
        </if>
        <if test="enable != null">
            enable = #{enable},
        </if>
        <if test="updateOpType != null">
            updateOpType = #{updateOpType},
        </if>
        <if test="updateOpUid != null">
            updateOpUid = #{updateOpUid},
        </if>
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( updateOpName )">
            updateOpName = #{updateOpName},
        </if>
        <if test="createTime != null">
            createTime = #{createTime},
        </if>
            updateTime = now()
    </insert>

    <update id="updateByCondition" parameterType="com.cdz360.biz.auth.sys.po.AccRelativePo">
        update
            t_acc_relative
        <set>
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( relativeCode )">
                relativeCode = #{relativeCode},
            </if>
            <if test="work != null">
                `work` = #{work},
            </if>
            <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
                siteIdList = #{siteIdList, typeHandler=com.cdz360.biz.auth.ds.MybatisJsonTypeHandler},
            </if>
            <if test="xjOrderNum != null">
                xjOrderNum = #{xjOrderNum},
            </if>
            <if test="ywOrderNum != null">
                ywOrderNum = #{ywOrderNum},
            </if>
            <if test="enable != null">
                enable = #{enable},
            </if>
            <if test="priority != null">
                priority = #{priority},
            </if>
            <if test="updateOpType != null">
                updateOpType = #{updateOpType},
            </if>
            <if test="updateOpUid != null">
                updateOpUid = #{updateOpUid},
            </if>
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( updateOpName )">
                updateOpName = #{updateOpName},
            </if>
            <if test="createTime != null">
                createTime = #{createTime},
            </if>
            updateTime = now()
        </set>
        where
            sysUid = #{sysUid}
    </update>

    <update id="dismissBySysUid">
        update
            t_acc_relative a
        inner join t_acc_relative b on
            a.relativeCode = b.relativeCode
            and a.sysUid != b.sysUid
        set
            b.`work` = #{work},
            b.updateTime = now()
        where
            a.sysUid = #{sysUid}
    </update>

    <update id="deleteBySysUid">
        update
            t_acc_relative
        set
            relativeCode = "",
            enable = 0,
            siteIdList = null,
            updateTime = now()
        where
            sysUid = #{sysUid}
            and enable = 1
    </update>

    <update id="updateBatch" parameterType="com.cdz360.biz.model.sys.vo.AccRelativeOrderVo">
        update
            t_acc_relative
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="xjOrderNum =case" suffix="else 0 end,">
                <foreach collection="list" item="i">
                    <if test="i.xjOrderNum!=null">
                        when sysUid = #{i.sysUid} then #{i.xjOrderNum}
                    </if>
                </foreach>
            </trim>
            <trim prefix="ywOrderNum =case" suffix="else 0 end,">
                <foreach collection="list" item="i">
                    <if test="i.ywOrderNum!=null">
                        when sysUid = #{i.sysUid} then #{i.ywOrderNum}
                    </if>
                </foreach>
            </trim>
        </trim>
        where sysUid in
        <foreach collection="list" item="i" open="(" separator="," close=")">
            #{i.sysUid}
        </foreach>
    </update>

</mapper>