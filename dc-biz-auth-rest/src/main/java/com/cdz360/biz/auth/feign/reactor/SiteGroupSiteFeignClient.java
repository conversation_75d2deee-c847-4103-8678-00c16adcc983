package com.cdz360.biz.auth.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.sys.param.SiteGroupSiteParam;
import com.cdz360.biz.model.sys.param.UpdateSiteGroupSiteParam;
import com.cdz360.biz.model.sys.vo.SiteGroupSiteInfoVo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE,
    fallbackFactory = SiteGroupSiteFeignHystrix.class)
public interface SiteGroupSiteFeignClient {

    // 获取场站关联的场站组ID列表
    @PostMapping(value = "/dataCore/siteGroupSite/findGidsBySiteId")
    Mono<ListResponse<String>> findSiteGroupSiteBySiteId(
        @RequestBody SiteGroupSiteParam param);

    // 获取场站组关联场站信息
    @PostMapping(value = "/dataCore/siteGroupSite/siteInfo")
    Mono<ListResponse<SiteGroupSiteInfoVo>> findSiteGroupSiteInfo(
        @RequestBody SiteGroupSiteParam param);

    // 更新场站组关联场站信息
    @PostMapping(value = "/dataCore/siteGroupSite/updateRef")
    Mono<ObjectResponse<SiteGroupSiteInfoVo>> updateSiteGroupSiteRef(
        @RequestBody UpdateSiteGroupSiteParam param);

    @PostMapping(value = "/dataCore/siteGroupSite/getSiteAmountByGidList")
    Mono<ObjectResponse<Long>> getSiteAmountByGidList(
        @RequestBody SiteGroupSiteParam param);
}