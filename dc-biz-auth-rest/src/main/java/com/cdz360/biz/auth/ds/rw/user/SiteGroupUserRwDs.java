package com.cdz360.biz.auth.ds.rw.user;

import com.cdz360.biz.auth.ds.rw.user.mapper.SiteGroupUserRwMapper;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SiteGroupUserRwDs {

    @Autowired
    private SiteGroupUserRwMapper siteGroupUserRwMapper;

    public int batchInsert(Long uid, List<String> gidList) {
        return siteGroupUserRwMapper.batchInsert(uid, gidList);
    }

    public int batchDelete(Long uid, List<String> exGidList) {
        return siteGroupUserRwMapper.batchDelete(uid, exGidList);
    }

    public int deleteByUid(long uid) {
        return siteGroupUserRwMapper.deleteByUid(uid);
    }

    public int deleteByGid(String gid) {
        return siteGroupUserRwMapper.deleteByGid(gid);
    }

}
