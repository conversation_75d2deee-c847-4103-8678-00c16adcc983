package com.cdz360.biz.auth.config;

import com.cdz360.biz.auth.service.LoginService;
import com.cdz360.biz.auth.service.oauth.*;
import javax.servlet.Filter;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.cache.ehcache.EhCacheManager;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.session.mgt.SessionManager;
import org.apache.shiro.session.mgt.eis.MemorySessionDAO;
import org.apache.shiro.spring.LifecycleBeanPostProcessor;
import org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.util.ThreadContext;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.springframework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Shiro配置
 */
@Slf4j
@Configuration
//@ConditionalOnClass(ZuulFilter.class)
public class ShiroConfig {

//    public AuthLogoutFilter authLogoutFilter(ShiroHttpService shiroHttpService) {
//        AuthLogoutFilter authLogoutFilter = new AuthLogoutFilter();
//        authLogoutFilter.setShiroHttpService(shiroHttpService);
//        return authLogoutFilter;
//    }

    @Bean("sessionManager")
    public SessionManager sessionManager() {
//        DefaultWebSessionManager sessionManager = new DefaultWebSessionManager();
        RestSessionManager sessionManager = new RestSessionManager();
        sessionManager.setSessionValidationSchedulerEnabled(true);
        sessionManager.setSessionIdUrlRewritingEnabled(false);
        MemorySessionDAO memorySessionDAO = new MemorySessionDAO();
        memorySessionDAO.setSessionIdGenerator(new CustomSessionIdGen());
        sessionManager.setSessionDAO(memorySessionDAO);

        return sessionManager;
    }

    @Bean
    public OAuth2Realm oAuth2Realm(ShiroHttpService httpService, LoginService loginService) {
        return new OAuth2Realm(httpService, loginService);
    }


    @Bean("securityManager")
    public SecurityManager securityManager(OAuth2Realm oAuth2Realm, SessionManager sessionManager) {
        DefaultWebSecurityManager securityManager = new DefaultWebSecurityManager();
        securityManager.setRealm(oAuth2Realm);
        ThreadContext.bind(securityManager);
        securityManager.setSessionManager(sessionManager);
        securityManager.setCacheManager(new EhCacheManager());
        return securityManager;
    }

    @Bean("shiroFilter")
    public ShiroFilterFactoryBean shirFilter(SecurityManager securityManager, AuthFilterMap authFilterMap, ShiroHttpService shiroHttpService) {
        log.info("\r\ninit shiro filter! \r\nstart!");
        ShiroFilterFactoryBean shiroFilter = new ShiroFilterFactoryBean();
        shiroFilter.setSecurityManager(securityManager);
        //oauth过滤
        Map<String, Filter> filters = new HashMap<>();
        filters.put("oauth2", new OAuth2Filter());
//        filters.put("logout", authLogoutFilter(shiroHttpService));
        shiroFilter.setFilters(filters);
        shiroFilter.setUnauthorizedUrl("/");

        Map<String, String> filterMap = new LinkedHashMap<>();
        filterMap.put("/logout", "logout");
        filterMap.put("/city/**", "anon");
        filterMap.put("/api/**", "anon");
        filterMap.put("/actuator/**", "anon");

        // 放行Swagger2页面，需要放行这些
        filterMap.put("/swagger-ui.html", "anon");
        filterMap.put("/swagger-ui/**", "anon");
        filterMap.put("/swagger/**", "anon");
        filterMap.put("/webjars/**", "anon");
        filterMap.put("/swagger-resources/**", "anon");
        filterMap.put("/v2/**", "anon");
        filterMap.put("/v3/api-docs/**", "anon");
        filterMap.put("/static/**", "anon");

        filterMap.putAll(authFilterMap);
        filterMap.put("/**", "oauth2");

        log.debug("shiro filter map :: {}", filterMap);
        shiroFilter.setFilterChainDefinitionMap(filterMap);

        return shiroFilter;
    }

    @Bean("lifecycleBeanPostProcessor")
    public LifecycleBeanPostProcessor lifecycleBeanPostProcessor() {
        return new LifecycleBeanPostProcessor();
    }

    @Bean
    public DefaultAdvisorAutoProxyCreator defaultAdvisorAutoProxyCreator() {
        DefaultAdvisorAutoProxyCreator proxyCreator = new DefaultAdvisorAutoProxyCreator();
        proxyCreator.setProxyTargetClass(true);
        return proxyCreator;
    }

    @Bean
    public AuthorizationAttributeSourceAdvisor authorizationAttributeSourceAdvisor(SecurityManager securityManager) {
        AuthorizationAttributeSourceAdvisor advisor = new AuthorizationAttributeSourceAdvisor();
        advisor.setSecurityManager(securityManager);
        return advisor;
    }

}
