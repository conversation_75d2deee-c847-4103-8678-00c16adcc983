package com.cdz360.biz.auth.rest;


import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.service.corpWx.CorpWxService;
import com.cdz360.biz.auth.user.param.ListCorpWxAppParam;
import com.cdz360.biz.auth.user.vo.CorpWxAppVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/api/corpWxApp")
@Tag(name = "企业微信标签相关接口", description = "企业微信标签")
public class CorpWxAppRest {

    @Autowired
    private CorpWxService corpWxService;


    @GetMapping("/refreshTeamCatalog")
    @Operation(summary = "刷新用户的团队标签")
    public BaseResponse refreshTeamCatalog(HttpServletRequest request) {
        log.info("刷新用户的团队标签");
        return this.corpWxService.refreshTeamCatalog();
    }

    /**
     * 获取所有的企业微信标签
     *
     * @param request HttpServletRequest
     * @param param 请求参数
     * @return 企业微信标签列表
     */
    @Operation(summary = "获取所有的企业微信标签")
    @PostMapping("/listAll")
    public ListResponse<CorpWxAppVo> listAllCorpWxApp(
            HttpServletRequest request, @RequestBody ListCorpWxAppParam param) {
        log.info("获取所有的企业微信标签: {}", param);
        return RestUtils.buildListResponse(this.corpWxService.listAllCorpWxApp(param));
    }
}
