package com.cdz360.biz.auth.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.oa.dto.account.OaAccountDto;
import com.cdz360.biz.oa.dto.account.OaGroupDto;
import com.cdz360.biz.oa.dto.account.OaMemberGroupListDto;
import com.cdz360.biz.oa.param.account.ListOaGroupParam;
import com.cdz360.biz.oa.param.account.ListOaGroupUserParam;
import com.cdz360.biz.oa.param.account.OaModifyGroupParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

import java.util.function.Function;

@Slf4j
@Component
public class OaAccountFeignHystrix
        implements FallbackFactory<OaAccountFeignClient> {
    @Override
    public OaAccountFeignClient apply(Throwable throwable) {
        return new OaAccountFeignClient() {
            @Override
            public Mono<ListResponse<OaAccountDto>> groupUserList(ListOaGroupUserParam param) {
                log.error("【服务熔断】: Service = {}, api = groupUserList (获取审核组用户列表), param = {}",
                        DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<OaGroupDto>> groupList(ListOaGroupParam param) {
                log.error("【服务熔断】: Service = {}, api = groupList (获取审核组列表), param = {}",
                        DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<OaMemberGroupListDto>> memberGroupList(
                ListOaGroupParam param) {
                log.error("【服务熔断】: Service = {}, api = memberGroupList (批量获取审核组列表), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<String>> modifyGroup(OaModifyGroupParam param) {
                log.error("【服务熔断】: Service = {}, api = modifyGroup (创建/修改审核组信息), param = {}",
                        DcConstants.KEY_FEIGN_DC_BIZ_OA, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<OaGroupDto>> removeGroup(String gid) {
                log.error("【服务熔断】: Service = {}, api = removeGroup (删除审核组信息), gid = {}",
                        DcConstants.KEY_FEIGN_DC_BIZ_OA, gid);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }
        };
    }

    @Override
    public <V> Function<V, OaAccountFeignClient> compose(
            Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_OA);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(
            Function<? super OaAccountFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_OA);
        return null;
    }
}
