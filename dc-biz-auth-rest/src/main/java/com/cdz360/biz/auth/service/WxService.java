package com.cdz360.biz.auth.service;


import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.auth.ds.ro.user.SysUserRoDs;
import com.cdz360.biz.auth.ds.rw.user.SysUserRwDs;
import com.cdz360.biz.auth.model.constant.GlobalConst;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.model.vo.TCommercialManage;
import com.cdz360.biz.auth.user.po.SysUserPo;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.event.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 微信公众号相关接口
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class WxService {

    private String token = "123456";
    @Autowired
    private SysUserRoDs sysUserRoDs;
    @Autowired
    private SysUserRwDs sysUserRwDs;

    @Autowired
    private TCommercialManageService tCommercialManageService;

    public static String sendGet(String url, String param) {
        String result = "";
        BufferedReader in = null;
        try {
            String urlNameString = url + "?" + param;
            URL realUrl = new URL(urlNameString);
            // 打开和URL之间的连接
            URLConnection connection = realUrl.openConnection();
            // 设置通用的请求属性
            connection.setRequestProperty("accept", "*/*");
            connection.setRequestProperty("connection", "Keep-Alive");
            connection.setRequestProperty("user-agent",
                "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            // 建立实际的连接
            connection.connect();
            // 获取所有响应头字段
            Map<String, List<String>> map = connection.getHeaderFields();
            // 遍历所有的响应头字段
            /*for (String key : map.keySet()) {
                System.out.println(key + "--->" + map.get(key));
            }*/
            // 定义 BufferedReader输入流来读取URL的响应
            in = new BufferedReader(new InputStreamReader(
                connection.getInputStream()));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }
        } catch (Exception e) {
            System.out.println("发送GET请求出现异常！" + e);
            e.printStackTrace();
        }
        // 使用finally块来关闭输入流
        finally {
            try {
                if (in != null) {
                    in.close();
                }
            } catch (Exception e2) {
                e2.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 微信公众号接入
     *
     * @param signature
     * @param timestamp
     * @param nonce
     * @param echostr
     * @return
     */
    public String checkSignature(String signature, String timestamp, String nonce, String echostr) {
        String[] arr = new String[]{timestamp, nonce, token};
        //排序
        Arrays.sort(arr);
        //生成字符串
        StringBuffer sb = new StringBuffer();
        for (String a : arr) {
            sb.append(a);
        }
        //shal加密
        String formattedText = sha1(sb.toString());
        if (formattedText.equals(signature)) {
            return echostr;
        }

        return null;
    }

    /**
     * 字符串进行sha1加密
     *
     * @param str
     * @return
     */
    public String sha1(String str) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-1");
            digest.update(str.getBytes());
            byte messageDigest[] = digest.digest();
            StringBuffer hexString = new StringBuffer();
            for (int i = 0; i < messageDigest.length; i++) {
                String shaHex = Integer.toHexString(messageDigest[i] & 0xFF);
                if (shaHex.length() < 2) {
                    hexString.append(0);
                }
                hexString.append(shaHex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 公众号绑定、解绑
     */
    public BaseResponse bindOrUnbindUser(String username, String password, Long type,
        String wxOpenId, String nickname) {
        //通过用户名获取用户信息
        SysUserPo user = this.sysUserRoDs.getByUsername(username, AppClientType.MGM_WEB.getCode());
        if (user == null) {
            return BaseResponse.newInstance().setStatus(1).setError("账号或密码不正确");
//            throw new DcServiceException("账号或密码不正确");
        }
        if (user.getStatus() != null && user.getStatus() == GlobalConst.LOCKED) {
            log.warn("帐号被锁定. storedUser = {}", user);
            return BaseResponse.newInstance().setStatus(1).setError("帐号被锁定,请稍后再重试");
//            throw new DcServiceException("帐号被锁定,请稍后再重试", Level.WARN);
        }
        SysUser su = new SysUser();
        su.setPassword(password);
        su.setUsername(username);

        //暂定 0绑定  1解绑
        if (type.equals(0L)) {
            //检测密码是否正确
            boolean equals = this.checkPassword(su, user);
            if (!equals) {
                throw new DcServiceException("帐号/密码错误", Level.WARN);
            }

            user.setWxOpenId(wxOpenId);
            user.setNickname(nickname);
        } else {
            user.setNickname("");
            user.setWxOpenId("");
        }
        sysUserRwDs.updateUserId(user);
        return BaseResponse.success();
    }

    public boolean checkPassword(SysUser naked, SysUserPo stored) {
        naked.setSalt(stored.getSalt());
        return naked.hashedPassword().equals(stored.getPassword());
    }

    /**
     * 获取用户信息
     *
     * @param code
     * @return
     */
    public ObjectResponse<SysUserPo> getUserInfo(String code, Long topCommId) {
        //获取当前账户配置的appid等信息
        TCommercialManage manageInfo = tCommercialManageService.getTCommercialManage(topCommId);
        if (manageInfo == null || manageInfo.getWechatAppid() == null) {
            SysUserPo sysUserPo = new SysUserPo();
            return new ObjectResponse<>(sysUserPo);
        }
        String appid = manageInfo.getWechatAppid();
        String secret = manageInfo.getWechatAppSecret();
        //通过code换取access_token,openId
        String access_token_url = "https://api.weixin.qq.com/sns/oauth2/access_token";
        String access_token_param = "appid=" + appid + "&secret=" + secret + "&code=" + code
            + "&grant_type=authorization_code";
        String result = sendGet(access_token_url, access_token_param);
        JsonNode jsonObject = JsonUtils.fromJson(result);
        log.info("jsonObject={}", jsonObject);

        SysUserPo sysUserPo = new SysUserPo();
        String access_token = jsonObject.get("access_token").asText();

        if (StringUtils.isNotBlank(access_token)) {
            String wxOpenId = jsonObject.get("openid").asText();
            log.info("openId={}", wxOpenId);
            SysUserPo user = sysUserRoDs.getByOpenId(wxOpenId);
            sysUserPo.setWxOpenId(wxOpenId);
            if (user != null) {
                sysUserPo.setUsername(user.getUsername());
                sysUserPo.setNickname(user.getNickname());
            }
            //通过access_token,openId换取nickname
            String userInfo_url = "https://api.weixin.qq.com/sns/userinfo";
            String userInfo_param =
                "access_token=" + access_token + "&openid=" + wxOpenId + "&lang=zh_CN";
            String ret = sendGet(userInfo_url, userInfo_param);
            JsonNode object = JsonUtils.fromJson(ret);
            log.info("userInfo={}", object);
            if (object.has("nickname")) {
                sysUserPo.setNickname(object.get("nickname").asText());
            }

        } else {
            log.error("errorMsg={}", jsonObject.get("errmsg"));
            return new ObjectResponse<>(sysUserPo);
        }
        log.info("returnInfo={}", sysUserPo);
        return new ObjectResponse<>(sysUserPo);
    }
}
