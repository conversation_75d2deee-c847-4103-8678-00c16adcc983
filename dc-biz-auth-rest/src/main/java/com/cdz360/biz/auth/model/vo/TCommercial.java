package com.cdz360.biz.auth.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cdz360.biz.model.order.type.OrderPayType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@TableName("t_commercial")
@Data
@EqualsAndHashCode(callSuper = true)
public class TCommercial extends BaseEntity implements LogicDeletable {

    @Schema(description = "集团商户ID", example = "12345")
    @TableField(value = "topCommId")
    private Long topCommId;

    private Long pid;

    @TableField(value = "comm_level")
    private Integer commLevel;

    private String merchants;
    @TableField(value = "comm_type")
    private Integer commType;
    @TableField(value = "comm_name")
    private String commName;
    @TableField(value = "short_name")
    private String shortName;
    @TableField(value = "comm_logo")
    private String commLogo;
    @TableField(value = "comm_icon")
    private String commIcon;

    private String contacts;

    private String phone;

    private String email;
    @TableField(value = "comm_category")
    private Integer commCategory;
    @TableField(value = "comm_industry")
    private Integer commIndustry;

    private String license;

    private String code;
    @TableField(value = "legal_person")
    private String legalPerson;
    @TableField(value = "province_id")
    private Integer provinceId;
    @TableField(exist = false)
    private String provinceName;
    @TableField(value = "city_id")
    private Integer cityId;
    @TableField(exist = false)
    private String cityName;
    @TableField(value = "area_id")
    private Integer areaId;
    @TableField(exist = false)
    private String areaName;

    private String detail;
    @TableField(value = "is_bond")
    private Integer isBond;
    @TableField(value = "bond_amount")
    private Integer bondAmount;
    @TableField(value = "charge_mode")
    private Integer chargeMode;
    @TableField(value = "bill_type")
    private Integer billType;
    @TableField(value = "business_type")
    private Byte businessType;
    @TableField(value = "company_type")
    private Byte companyType;
    @TableField(value = "registration_amount")
    private Long registrationAmount;
    @TableField(value = "registration_time")
    private String registrationTime;
    @TableField(value = "business_licence")
    private String businessLicence;
    @TableField(value = "operate_type")
    private Byte operateType;
    @TableField(value = "operate_base_info_id")
    private Long operateBaseInfoId;
    @TableLogic(value = "1")    // 1 有效
    private Integer status;
    @TableField(value = "business_status")
    private Integer businessStatus;
    @TableField(value = "service_phone")
    private String servicePhone;
    @TableField(value = "thrid_no")
    private String thridNo;
    @TableField(value = "set_charger_start_timeout")
    private Integer setChargerStartTimeout;
    @TableField(value = "pre_authorization_amount")
    private Integer preAuthorizationAmount;
    @TableField(value = "my_license_code")
    private String myLicenseCode;
    @TableField(value = "agent_level")
    private Integer agentLevel;
    @TableField(value = "agent_level_path")
    private String agentLevelPath;
    @TableField("out_key")
    private String outKey;
    @TableField(value = "org_code")
    private String orgCode;

    /***
     *  商户所属平台
     */
    @TableField(value = "platform")
    private Integer platform;

    @TableField(value = "platformName")
    private String platformName;

    private String token;
    @TableField(value = "callback_url")
    private String callbackUrl;
    @TableField(value = "wx_appid")
    private String wxAppid;
    @TableField(value = "wx_app_secret")
    private String wxAppSecret;

    // 个人账户收款方
    // null == 顶级商户没有开启，true -- 个人收款方可用，false -- 个人收款方不可用
    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean enableBalance;

    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean enableUseZft;

    // 直付商名称
    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String zftName;

//    @TableField(value = "wxSubMchId")
    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String ecnyPlatformKey;

    @TableField(exist = false)
    @Schema(description = "微信(服务商)商户号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String wxMchId;

    @TableField(exist = false)
    @Schema(description = "微信(二级)商户号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String wxSubMchId;

    @TableField(exist = false)
    @Schema(description = "微信信用分服务商ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String wxCreditServiceId;

    @Schema(description = "支付宝(服务商)商户号")
    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String alipayMchId;

    @TableField(exist = false)
    @Schema(description = "支付宝(二级)商户号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String alipaySubMchId;

    @TableField(exist = false)
    @Schema(description = "支付宝芝麻信用服务商ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String alipayCreditServiceId;
    /**
     * 二级商户的微信小程序 ID
     */
    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String wxLiteAppId;

    /**
     * 二级商户的微信Android ID
     */
    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String wxAndroidAppId;

    /**
     * 二级商户的微信IOS ID
     */
    @TableField(exist = false)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String wxIosAppId;



    @TableField(value = "sms_app_key")
    private String smsAppKey;

    @TableField(value = "zftId")
    private Long zftId;

//    @TableField(exist = false)
//    private Boolean enableRefund;

    @TableField(value = "enableZft")
    private Boolean enableZft;

    @Schema(description = "(一级商户使用)数币 true -- 显示; false -- 隐藏")
    @TableField(value = "enableDigiccy")
    private Boolean enableDigiccy;

    @Schema(description = "企业在线充值 true -- 开启")
    @TableField(value = "enableCorpDeposit")
    private Boolean enableCorpDeposit;

    @Schema(description = "企业在线退款 true -- 开启")
    @TableField(value = "enableCorpRefund")
    private Boolean enableCorpRefund;

    @Schema(description = "会员在线退款 true -- 开启")
    @TableField(value = "enableCommRefund")
    private Boolean enableCommRefund;

    @Schema(description = "商户会员在线充值 true -- 开启")
    @TableField(value = "enableOnlinePay")
    private Boolean enableOnlinePay;

    @Schema(description = "商户会员等级 true -- 开启")
    @TableField(value = "enableUseScore")
    private Boolean enableUseScore;

    @TableField(exist = false)
    private SysUser mainUser;

    @TableField(exist = false)
    private String operateBaseInfoName;
    @TableField(exist = false)
    private String parentName;
    @TableField(exist = false)
    private List<TCommercial> youngers;
    @TableField(exist = false)
    private TCommercial parent;
    @TableField(exist = false)
    private TOperateBaseInfo tOperateBaseInfo;
    @TableField(exist = false)
    private List<Long> ids;
    @TableField(exist = false)
    private TCommercialManage tCommercialManage;
    @TableField(exist = false)
    private List<TCommercial> children;

    private String url;//'商户二维码url'

    @Schema(description = "是否配置了小程序,在t_commercial_manage存在对应记录")
    @TableField(exist = false)
    private Boolean hasManage;

    @TableField(exist = false)
    @Schema(description = "发票功能启动开关")
    private Boolean invoinceEnabled;

    @TableField(exist = false)
    @Schema(description = "客服电话")
    private String serviceTel;

    @TableField(value = "idChain")
    private String idChain;

    @TableField(exist = false)
    @Schema(description = "商户下属中最小权限的商户的级别")
    private Integer leastPrivilegesCommLevel;

    @JsonIgnore
    @TableField(value = "hlhtSitePayType")
    private String hlhtSitePayType;

    @TableField(exist = false)
    private List<OrderPayType> hlhtSitePayTypeList;

    @TableField(exist = false)
    @Schema(description = "直付通在平台创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", locale = "zh", timezone = "GMT+8")
    private Date zftCreateTime;

    @TableField(exist = false)
    @Schema(description = "直付通所属商户")
    private Long zftCommId;
}
