package com.cdz360.biz.auth.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@ConfigurationProperties(prefix = "authc")
@Data
@Component
public class AuthcProperties {
    private String smsUrl;
    private int idcodeExpire = 180;
    private int idcodeRetryInterval = 60;
    private String cookieDomain;
    private String smsApiKey;
    private Boolean cookieSecure;
    private String company;
    private String templateNo;
    private String codeName;

    public String getSmsUrl() {
        if (smsUrl != null && smsUrl.matches("^https?//.*"))
            return "http://" + smsUrl;
        return smsUrl;
    }
}
