package com.cdz360.biz.auth.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@Configuration
@ComponentScan("com.cdz360.biz.auth.*")
public class AuthAutoConfigure {
    @Bean
    @ConditionalOnMissingBean(AuthFilterMap.class)
    public AuthFilterMap filterMapConfigure() {
        return new AuthFilterMap();
    }

}
