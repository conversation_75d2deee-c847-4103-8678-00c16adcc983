package com.cdz360.biz.auth.service.oauth;

import org.apache.shiro.session.Session;
import org.apache.shiro.session.mgt.eis.SessionIdGenerator;
import org.apache.shiro.web.servlet.ShiroHttpServletRequest;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;
import java.io.Serializable;
import java.util.Objects;
import java.util.UUID;

public class CustomSessionIdGen implements SessionIdGenerator {
    @Override
    public Serializable generateId(Session session) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();

        String source = (String) request.getAttribute(ShiroHttpServletRequest.REFERENCED_SESSION_ID_SOURCE);
        if (Objects.equals(source, "token")) {
            return (String) request.getAttribute("token");
        }
        return UUID.randomUUID().toString();
    }

}
