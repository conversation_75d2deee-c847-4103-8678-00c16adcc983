package com.cdz360.biz.auth.ds.ro.sys;

import com.cdz360.biz.auth.ds.ro.sys.mapper.SysMenuRoMapper;
import com.cdz360.biz.auth.model.vo.SysMenu;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysMenuRoDs {
    @Autowired
    private SysMenuRoMapper sysMenuRoMapper;
    public List<SysMenu> getMenuListByUrl (List<String> urlList) {
        return sysMenuRoMapper.getMenuListByUrl(urlList);
    }
}
