<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cdz360.biz.auth.dao.SysMenuDao">
  <select id="findMenuByUserIdAndSysId" resultType="com.cdz360.biz.auth.model.vo.SysMenu">
    SELECT DISTINCT m.id AS id,m.pid,m.perm,m.`name`,m.icon,m.url,m.subsys_id AS
    subsysId,m.`type`,m.tips,m.hidden,m.`status`,m.is_open AS opened,m.seq,m.create_time AS
    createTime,m.update_time
    AS updateTime,m.create_by AS createBy,m.update_by AS updateBy, m.component,
    m.extra,ua.name AS createUser, ub.name AS updateUser
    FROM sys_menu m
    <if test="userId!=null">
      JOIN sys_role_menu rm ON m.id=rm.menu_id
      JOIN sys_user_role ur ON ur.role_id=rm.role_id and ur.enable=true and (ur.expireTime is null
      or ur.expireTime>=DATE_FORMAT(now(),'%Y-%m-%d 00:00:00'))
      JOIN sys_role sr on rm.role_id = sr.id
    </if>
    LEFT JOIN sys_user ua on m.create_by=ua.id
    LEFT JOIN sys_user ub on m.update_by=ub.id

    <where>
      1=1
      <if test="showMenu==true">
        and m.status = 1
      </if>

      <if test="userId!=null">
        and ur.user_id=#{userId} AND (sr.status =1 OR sr.status is null)
      </if>

      <if test="sysId!=null">
        AND m.subsys_id=#{sysId,javaType=Long}
      </if>
    </where>
    ORDER BY m.seq
  </select>

  <select id="findMenuByUserIdAndSysIdAndUrl" resultType="com.cdz360.biz.auth.model.vo.SysMenu">
    SELECT DISTINCT m.id AS id,m.pid,m.perm,m.`name`,m.icon,m.url,m.subsys_id AS
    subsysId,m.`type`,m.tips,m.hidden,m.`status`,m.is_open AS opened,m.seq,m.create_time AS
    createTime,m.update_time
    AS updateTime,m.create_by AS createBy,m.update_by AS updateBy,
    m.extra,ua.name AS createUser, ub.name AS updateUser
    FROM sys_menu m
    <if test="userId!=null">
      JOIN sys_role_menu rm ON m.id=rm.menu_id
      JOIN sys_user_role ur ON ur.role_id=rm.role_id
      JOIN sys_role sr on rm.role_id = sr.id
    </if>
    LEFT JOIN sys_user ua on m.create_by=ua.id
    LEFT JOIN sys_user ub on m.update_by=ub.id
    <where>
      <if test="userId!=null">
        ur.user_id=#{userId} AND (sr.status =1 OR sr.status is null)
      </if>
      <if test="sysId!=null">
        AND m.subsys_id=#{sysId,javaType=Long}
      </if>
      <if test="url!=null">
        AND m.url=#{url,javaType=String}
      </if>
    </where>
    ORDER BY m.seq
  </select>

  <select id="findPermByUserId" resultType="java.lang.String">
    SELECT DISTINCT m.perm
    FROM sys_menu m
    <if test="_parameter!=null">
      JOIN sys_role_menu rm ON m.id=rm.menu_id
      JOIN sys_user_role ur ON ur.role_id=rm.role_id
      JOIN sys_role sr on rm.role_id = sr.id
    </if>
    <where>
      m.perm IS NOT NULL AND m.perm!=''
      <if test="_parameter!=null">
        AND ur.user_id=#{userId}
        <![CDATA[
                AND !sr.status<=>2
                ]]>

      </if>
    </where>
  </select>
  <select id="findAllMenuTaggedByRoleId" resultType="com.cdz360.biz.auth.model.vo.SysMenu">
    SELECT DISTINCT m.id AS id,
    m.pid,
    m.perm,
    m.`name`,
    m.icon,
    m.url,
    m.subsys_id AS subsysId,
    m.`type`,
    m.tips,
    m.hidden,
    m.`status`,
    m.is_open AS isOpen,
    m.seq,
    m.create_time AS createTime,
    m.update_time AS updateTime,
    m.create_by AS createBy,
    m.update_by AS updateBy,
    if(rm.role_id = #{roleId}, 1, 0) AS isChecked
    FROM sys_role_menu rm
    RIGHT JOIN sys_menu m ON m.id = rm.menu_id
  </select>
  <select id="countMenuByUserIdAndSysId" resultType="java.lang.Long">
    SELECT count(1)
    FROM sys_menu m
    <if test="userId!=null">
      JOIN sys_role_menu rm ON m.id=rm.menu_id
      JOIN sys_user_role ur ON ur.role_id=rm.role_id
    </if>
    <where>
      <if test="userId!=null">
        ur.user_id=#{userId}
      </if>
      <if test="sysId!=null">
        AND m.subsys_id=#{sysId,javaType=Long}
      </if>
    </where>
  </select>

  <select id="countMenuByUniqueKeyAndOtherId" resultType="java.lang.Long">
    SELECT count(1)
    FROM sys_menu
    <where>
      <if test="subsysId!=null">
        subsys_id=#{subsysId}
      </if>
      <if test="name!=null">
        AND name=#{name}
      </if>
      <if test="type!=null">
        AND type=#{type}
      </if>
      <if test="id!=null">
        AND id!=#{id}
      </if>
    </where>
  </select>
  <select id="getMenuById" resultType="com.cdz360.biz.auth.model.vo.SysMenu">
    select id,url,subsys_id as subsysId from sys_menu where id = #{id}
  </select>
</mapper>