package com.cdz360.biz.auth.model.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 资金周转-商户集团信息
 */
@Data
@Accessors(chain = true)
public class CommercialVo {
    /**
     * 编号
     */
    private Long code;
    /**
     * 所属集团商户代码
     */
    private Long groupCode;
    /**
     * 商户级别(1.集团商户,>1表示下级商户)
     */
    private Integer orgLevel;
    /**
     * 商户名称
     */
    private String name;
    /**
     * 简称
     */
    private String shortName;
    /**
     * 联系人
     */
    private String contacts;
    /**
     * 联系电话
     */
    private String contactPhone;
    /**
     * 联系邮箱
     */
    private String email;
    /**
     * 营业执照注册号/社会信用代码
     */
    private String orgCode;
    /**
     * 父商户代码
     */
    private Long parentCode;
    /**
     * 状态（1、审核中，2、审核成功，3、审核失败）
     */
    private Integer businessStatus;
}
