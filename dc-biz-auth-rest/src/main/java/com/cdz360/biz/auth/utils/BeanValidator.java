package com.cdz360.biz.auth.utils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import java.util.Set;

public abstract class BeanValidator {

    private static Validator validation = Validation.buildDefaultValidatorFactory().getValidator();


    public static boolean isvalid(Object o) {
        boolean r = false;
        Set<ConstraintViolation<Object>> vr = validation.validate(o);
        if (vr.isEmpty()) {
            r = true;
        }
        return r;
    }
}
