package com.cdz360.biz.auth.service.oauth;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.auth.model.exception.ErrcodeException;
import com.cdz360.biz.auth.model.exception.RestException;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.service.LoginService;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;

@Slf4j
public abstract class ShiroHttpService {

    protected String authUrl = "dc-biz-auth";

    public ShiroHttpService setAuthUrl(String authUrl) {
        this.authUrl = authUrl;
        return this;
    }

//    public abstract RestTemplate getRestTemplate();


    public abstract Set<String> roleSet(Long userId, String token);

    public abstract Set<String> permSet(Long userId, String token);


    public final SysUser userByToken(LoginService loginService, String token) {
        log.info("token = {}", token);
        String json = loginService.getUserJson(token);
        log.info("json = {}", json);
        SysUser simpleUser = JsonUtils.fromJson(json, SysUser.class);
        return simpleUser;
    }


    protected void checkResponse(String url, ResponseEntity<String> forEntity) {
        if (!forEntity.getStatusCode().is2xxSuccessful()) {
            String msg = String.format("调用接口-%s,响应码: %d", url,
                forEntity.getStatusCodeValue());
            log.error(msg);
            throw new RestException(msg);
        }
    }

    protected void checkErrcode(ResponseEntity<String> forEntity) {
        String body = forEntity.getBody();
        JsonNode parse = JsonUtils.fromJson(body);
        if (parse.get("status").asInt() != 0) {
//        if (!JSONPath.containsValue(parse, "$.status", 0)) {
            log.warn("业务 status 不为0. body:: {}", body);
            throw new ErrcodeException(body);
        }
    }


}
