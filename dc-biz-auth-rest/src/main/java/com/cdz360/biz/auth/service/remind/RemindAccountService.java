package com.cdz360.biz.auth.service.remind;


import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.auth.ds.ro.remind.RemindAccountRoDs;
import com.cdz360.biz.auth.ds.rw.remind.RemindAccountRwDs;
import com.cdz360.biz.auth.model.param.ListRemindAccountParam;
import com.cdz360.biz.auth.model.param.RemoveRemindAccountParam;
import com.cdz360.biz.auth.model.vo.RemindAccountVo;
import com.cdz360.biz.auth.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class RemindAccountService {


    @Autowired
    private RemindAccountRoDs remindAccountRoDs;

    @Autowired
    private RemindAccountRwDs remindAccountRwDs;

    public Mono<ListResponse<RemindAccountVo>> corpBalanceRemindAccount(
        ListRemindAccountParam param) {
        IotAssert.isNotNull(param.getCorpId(), "请提供企业客户ID");
        return remindAccountRoDs.findRemindAccount(param);
    }

    public Mono<ListResponse<RemindAccountVo>> unbindCorpBalance(ListRemindAccountParam param) {
        IotAssert.isNotNull(param.getCorpId(), "请提供企业客户ID");
        return remindAccountRoDs.unbindCorpBalance(param);
    }

    public Mono<Integer> removeCorpBalanceRemindAccount(RemoveRemindAccountParam param) {
        IotAssert.isNotNull(param.getCorpId(), "请提供企业客户ID");
        return Mono.just(param)
            .map(remindAccountRwDs::removeCorpBalanceRemindAccount);
    }

    public Mono<ListResponse<RemindAccountVo>> corpBalanceTemporary(ListRemindAccountParam param) {
        IotAssert.isNotNull(param.getCorpId(), "请提供企业客户ID");
        return remindAccountRoDs.corpBalanceTemporary(param);
    }
}
