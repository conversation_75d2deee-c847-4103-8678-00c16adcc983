package com.cdz360.biz.auth.model.param;

import com.cdz360.biz.auth.model.type.BatchUpdateRoleEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

/**
 * 批量修改角色
 */
@Data
public class BatchUpdateRoleRequest   {


    @Schema(description = "修改权限内容")
    private BatchUpdateRoleEnum editType;

    @Schema(description = "平台")
    private Long platform;

    @Schema(description = "角色列表")
    private List<Long> roleIdList;

    @Schema(description = "充值申请，企业开票时所选择的商户")
    private String commIds;

    @Schema(description = "操作人")
    private Long updateBy;


}