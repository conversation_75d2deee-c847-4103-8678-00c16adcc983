package com.cdz360.biz.auth.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.service.SiteGroupService;
import com.cdz360.biz.auth.utils.IotAssert;
import com.cdz360.biz.model.sys.constant.SiteGroupType;
import com.cdz360.biz.model.sys.dto.SiteGroupDto;
import com.cdz360.biz.model.sys.dto.UserOwnerSiteGroupDto;
import com.cdz360.biz.model.sys.param.ListSiteGroupParam;
import com.cdz360.biz.model.sys.po.SiteGroupPo;
import com.cdz360.biz.model.sys.vo.SiteGroupVo;
import com.cdz360.biz.model.sys.vo.UserGroupVo;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import java.time.Duration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Tag(name = "场站组相关接口", description = "场站组相关接口")
public class SiteGroupRest {


    @Autowired
    private SiteGroupService siteGroupService;

    @Operation(summary = "获取场站组列表")
    @PostMapping(value = "/api/siteGroup/findSiteGroup")
    public ListResponse<SiteGroupVo> findSiteGroup(
        HttpServletRequest request,
        @RequestBody ListSiteGroupParam param) {
        log.info("获取场站组列表: param = {}", JsonUtils.toJsonString(param));
        return siteGroupService.findSiteGroup(param);
    }

    @Operation(summary = "获取场站组信息")
    @GetMapping(value = "/api/siteGroup/getSiteGroupInfo")
    public ObjectResponse<SiteGroupVo> getSiteGroupInfo(
        HttpServletRequest request,
        @RequestParam("gid") String gid) {
        log.info("获取场站组信息: gid = {}", gid);
        return siteGroupService.getSiteGroupInfo(gid)
            .map(RestUtils::buildObjectResponse)
            .block(Duration.ofSeconds(50L));
    }

    @Operation(summary = "删除场站组信息")
    @DeleteMapping(value = "/api/siteGroup/removeSiteGroup")
    public ObjectResponse<SiteGroupVo> removeSiteGroup(
        HttpServletRequest request,
        @RequestParam("gid") String gid) {
        log.info("获取场站组信息: gid = {}", gid);
        return siteGroupService.removeSiteGroup(gid)
            .map(RestUtils::buildObjectResponse)
            .block(Duration.ofSeconds(50L));
    }

    @Operation(summary = "新增场站组")
    @PostMapping(value = "/api/siteGroup/addSiteGroup")
    public ObjectResponse<SiteGroupVo> addSiteGroup(
        HttpServletRequest request,
        @RequestBody SiteGroupDto dto) {
        log.info("新增场站组: dto = {}", JsonUtils.toJsonString(dto));
        return siteGroupService.addSiteGroup(dto)
            .map(RestUtils::buildObjectResponse)
            .block(Duration.ofSeconds(50L));
    }

    @Operation(summary = "账号默认场站组追加场站")
    @PostMapping(value = "/api/siteGroup/accountDefaultSiteGroupAppendSite")
    public ObjectResponse<SiteGroupVo> accountDefaultSiteGroupAppendSite(
        @RequestParam("id") Long id, @RequestParam("siteId") String siteId) {
        log.info("账号默认场站组追加场站: id = {}, siteId = {}", id, siteId);
        return siteGroupService.accountDefaultSiteGroupAppendSite(id, siteId)
            .map(RestUtils::buildObjectResponse)
            .block(Duration.ofSeconds(50L));
    }

    @Operation(summary = "编辑场站组")
    @PostMapping(value = "/api/siteGroup/editSiteGroup")
    public ObjectResponse<SiteGroupVo> editSiteGroup(
        HttpServletRequest request,
        @RequestBody SiteGroupDto dto) {
        log.info("编辑场站组: dto = {}", JsonUtils.toJsonString(dto));
        return siteGroupService.editSiteGroup(dto)
            .map(RestUtils::buildObjectResponse)
            .block(Duration.ofSeconds(50L));
    }

    @Operation(summary = "用户拥有场站组信息")
    @PostMapping(value = "/api/siteGroup/userOwnerSiteGroup")
    public ListResponse<UserOwnerSiteGroupDto> userOwnerSiteGroup(
        HttpServletRequest request,
        @RequestBody ListSiteGroupParam param) {
        log.debug("用户拥有场站组信息: param = {}", JsonUtils.toJsonString(param));
        return RestUtils.buildListResponse(siteGroupService.userOwnerSiteGroup(param));
    }

    @Operation(summary = "用户拥有场站和归属场站组信息")
    @PostMapping(value = "/api/siteGroup/userOwnerSite")
    public ObjectResponse<UserOwnerSiteGroupDto> userOwnerSite(
        HttpServletRequest request,
        @ApiParam("用户ID") @RequestParam("uid") Long uid,
        @ApiParam("场站组类型") @RequestParam(value = "groupType", required = false) SiteGroupType groupType) {
        log.debug("用户拥有场站和归属场站组信息: uid = {}, groupType = {}", uid, groupType);
        return RestUtils.buildObjectResponse(siteGroupService.userOwnerSite(uid, groupType));
    }

    @Operation(summary = "根据组Id  获取用户列表")
    @PostMapping(value = "/api/siteGroup/findUserGroup")
    public ListResponse<UserGroupVo> findUserGroup(
        HttpServletRequest request,
        @RequestBody ListSiteGroupParam param) {
        log.debug("获取场站组列表: param = {}", JsonUtils.toJsonString(param));
        return RestUtils.buildListResponse(siteGroupService.findUserGroup(param));
    }

    @Operation(summary = "获取场站组列表,以及成员信息")
    @PostMapping(value = "/api/siteGroup/findSiteGroupAndUser")
    public ListResponse<SiteGroupVo> findSiteGroupAndUser(
        HttpServletRequest request,
        @RequestBody ListSiteGroupParam param) {
        log.debug("获取场站组列表,以及成员信息: param = {}", JsonUtils.toJsonString(param));
        return siteGroupService.findSiteGroupAndUser(param);
    }

    @Operation(summary = "获取场站组列表")
    @PostMapping(value = "/api/siteGroup/getSiteGroupList")
    public ListResponse<SiteGroupPo> getSiteGroupList(@RequestBody ListSiteGroupParam param) {
        log.debug("获取场站组列表: param = {}", JsonUtils.toJsonString(param));
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getGidList()), "组信息不能为空");
        return RestUtils.buildListResponse(siteGroupService.getSiteGroupList(param));
    }
}
