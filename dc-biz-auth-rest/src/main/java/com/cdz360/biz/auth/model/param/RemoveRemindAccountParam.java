package com.cdz360.biz.auth.model.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "删除提醒账户参数")
@Data
@Accessors(chain = true)
public class RemoveRemindAccountParam {

    @Schema(description = "企业客户ID")
    @JsonInclude(Include.NON_NULL)
    private Long corpId;

    @Schema(description = "用户ID列表")
    @JsonInclude(Include.NON_NULL)
    private List<Long> uidList;
}
