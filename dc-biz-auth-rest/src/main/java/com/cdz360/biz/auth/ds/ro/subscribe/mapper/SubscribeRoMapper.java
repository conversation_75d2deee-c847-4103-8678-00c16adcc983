package com.cdz360.biz.auth.ds.ro.subscribe.mapper;

import com.cdz360.biz.auth.subscribe.param.SubLogListParam;
import com.cdz360.biz.auth.subscribe.param.SubscribeListParam;
import com.cdz360.biz.auth.subscribe.po.SubscribePo;
import com.cdz360.biz.auth.subscribe.vo.*;
import com.cdz360.biz.auth.sys.vo.SysRoleSimpleVo;
import com.cdz360.biz.model.sys.vo.SubscribeOrderVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SubscribeRoMapper {

    SubscribePo getById(@Param("id") Long id);

    SubscribePo getSubByTitle(@Param("title") String title);

    SubscribeOrderVo getOrderById(@Param("payNo") String payNo);

    SubscribeDetailVo getDetail(@Param("subId") Long subId);

    Long getCount(SubscribeListParam params);

    List<SubscribeVo> getList(SubscribeListParam params);

    List<CommVo> getCommList(@Param("subId") Long subId);

    List<SysRoleSimpleVo> getRoleList(@Param("subId") Long subId);

    List<SubscribeDetailVo> getListByUser(@Param("sysId") Long sysId, @Param("commId") Long commId, @Param("status") Boolean status);

    Long getLogCount(SubLogListParam params);

    List<SubscribeLogVo> getLogList(SubLogListParam params);

    List<SubscribeOrderDetailVo> getRoleListByPayNo(@Param("payNo") String payNo);

}
