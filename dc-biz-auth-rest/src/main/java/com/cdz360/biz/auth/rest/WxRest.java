package com.cdz360.biz.auth.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.auth.service.WxService;
import com.cdz360.biz.auth.user.po.SysUserPo;
import com.cdz360.biz.auth.utils.IotAssert;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "微信公众号相关", description = "微信公众号相关接口")
public class WxRest {

    @Autowired
    private WxService wxService;

    /**
     * 微信公众号接入
     * @param signature
     * @param timestamp
     * @param nonce
     * @param echostr
     * @return
     */
    @RequestMapping(value = "/api/wx/checkSignature",method = RequestMethod.GET)
    public String checkSignature(@RequestParam("signature") String signature,
                                 @RequestParam("timestamp") String timestamp,
                                 @RequestParam("nonce") String nonce,
                                    @RequestParam("echostr") String echostr){
        log.info("signature={},timestamp={},nonce={},echostr={}",signature,timestamp,nonce,echostr);
        return wxService.checkSignature(signature,timestamp,nonce,echostr);
    }

    /**
     * 公众号获取用户信息
     */
    @RequestMapping(value = "/api/wx/getUserInfo",method = RequestMethod.GET)
    public ObjectResponse<SysUserPo> getUserInfo (@RequestParam("code") String code,
                                                  @RequestParam("topCommId") Long topCommId){
        log.info("code={}",code);
        if (StringUtils.isBlank(code)){
            throw new DcServiceException("code值不能为空");
        }
        return wxService.getUserInfo(code,topCommId);
    }


    @RequestMapping(value = "/api/wx/bindOrUnbindUser",method = RequestMethod.GET)
    public BaseResponse bindOrUnbindUser (@RequestParam("username") String username,
                                          @RequestParam("password") String password,
                                          @RequestParam("type") Long type,
                                          @RequestParam("wxOpenId") String wxOpenId,
                                          @RequestParam("nickname") String nickname){
        log.info("usernme={},password={},wxOpenId={},nickname={}",username,password,wxOpenId,nickname);
        IotAssert.isNotNull(type,"操作类型不能为空");
        IotAssert.isNotNull(username,"用户名不能为空");

        // 解绑不需要密码
        if (type.equals(0L)) {
            IotAssert.isNotNull(password, "密码不能为空");
        }
        IotAssert.isNotNull(wxOpenId,"openId获取失败");
        return wxService.bindOrUnbindUser(username,password,type,wxOpenId,nickname);
    }




}
