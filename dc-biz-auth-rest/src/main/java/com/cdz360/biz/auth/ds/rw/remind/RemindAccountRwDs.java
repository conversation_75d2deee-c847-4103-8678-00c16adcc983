package com.cdz360.biz.auth.ds.rw.remind;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.auth.ds.rw.remind.mapper.RemindAccountRwMapper;
import com.cdz360.biz.auth.model.param.RemoveRemindAccountParam;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RemindAccountRwDs {

    @Autowired
    private RemindAccountRwMapper remindAccountRwMapper;

    public int updateCorpBalanceRemindAccount(
        Long corpId, List<Long> includeUidList, List<Long> excludeUidList) {
        if (CollectionUtils.isNotEmpty(excludeUidList)) {
            remindAccountRwMapper.removeExclude(corpId, excludeUidList);
        }

        if (CollectionUtils.isNotEmpty(includeUidList)) {
            remindAccountRwMapper.insertOrIgnore(corpId, includeUidList);
        }
        return includeUidList.size();
    }

    public int removeCorpBalanceRemindAccount(RemoveRemindAccountParam param) {
        return remindAccountRwMapper.removeExclude(param.getCorpId(), param.getUidList());
    }
}
