package com.cdz360.biz.auth.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CustomShiroConfig {
    @Bean
    AuthFilterMap filterMapConfigure() {
        AuthFilterMap authFilterMap = new AuthFilterMap();
        authFilterMap.put("/data/users/validate", "anon");
        authFilterMap.put("/data/users/entity", "anon");
        authFilterMap.put("/data/users/entityInfo/*", "anon");
        authFilterMap.put("/data/users/sys/valid/**", "anon");
        authFilterMap.put("/data/users/query/phone", "anon");
        authFilterMap.put("/data/users/mobile/pass", "anon");
        authFilterMap.put("/data/commercials/user/*/current", "anon");
        authFilterMap.put("/data/commercials/user/*", "anon");
        authFilterMap.put("/data/commercials/*", "anon");
        authFilterMap.put("/data/users/*", "anon");
        authFilterMap.put("/data/commercials/ids/*", "anon");
        authFilterMap.put("/data/commercials/user/getMaxCommercial/*", "anon");
        authFilterMap.put("/data/commercials/user/getByCommlevel/*", "anon");
        authFilterMap.put("/data/commercials/user/getChildByCommId/*", "anon");
        authFilterMap.put("/data/commercials/manage/*", "anon");
        authFilterMap.put("/data/commercialManage/*", "anon");
        authFilterMap.put("/data/users/authValid", "anon");
        authFilterMap.put("/data/login/**", "anon");
        return authFilterMap;
    }
}
