package com.cdz360.biz.auth.rest;


import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.auth.model.dto.TCommercialDto;
import com.cdz360.biz.auth.model.dto.UserCommericalDto;
import com.cdz360.biz.auth.model.vo.*;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
public interface TCommercialInterface {

    Rez<SysUserComercial> userCommercials(@PathVariable("userId") Long userId);

    Rez<List<SysUser>> commercialsUsers(UserCommericalDto ucd);


    Rez<List<SysUser>> commercialsUsersCurrent(UserCommericalDto ucd);

    Rez<List<SysUser>> commercialsfromUsersCurrent(@RequestBody UserCommericalDto ucd);

    Rez<List<SysUser>> commercialsfromUsers(@RequestBody UserCommericalDto ucd);


    @Deprecated
    Rez<TCommercial> add(@RequestBody TCommercial entity);


    Rez<TCommercial> findById(@PathVariable("id") Long id);


    Rez<TCommercial> findByUserId(@PathVariable("userId") Long userId);

    Rez<TCommercial> userSub(@PathVariable("userId") Long userId);


    /**
     * 查询商户子节点（树形）
     *
     * @param userId
     * @return
     */
//    @GetMapping("/user/treesubAndSelf/{userId}")
//    Rez<TCommercial> userTreeSubAndSelf(@PathVariable("userId") Long userId);
    Rez<TCommercial> userSup(@PathVariable("userId") Long userId);

//    @Deprecated
//    Rez delete(@PathVariable("id") Long id);


    ListResponse<TCommercial> page(@RequestBody TCommercialDto pd);


    ListResponse<TCommercialUser> findByUserPageByCommercialUser(@RequestBody UserCommericalDto ucd);

    Rez editUser(@RequestBody TCommercialUser tcu);

    Rez addUser(@RequestBody TCommercialUser tcu);

    Rez commercialsManage(@PathVariable("comId") Long comId);

    ObjectResponse<Long> commercialsManage(String appId);


}
