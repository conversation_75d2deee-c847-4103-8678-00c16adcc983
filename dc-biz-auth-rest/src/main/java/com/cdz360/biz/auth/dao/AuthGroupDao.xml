<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.dao.AuthGroupDao">
    <insert id="addGroup" useGeneratedKeys="true" keyProperty="id"
            keyColumn="id" parameterType="com.cdz360.biz.auth.model.vo.AuthorityGroup">
        INSERT INTO t_authority_group(roleId,groupName, groupDesc, opName,opId,createTime,updateTime)
        VALUES
        (#{roleId},#{groupName},#{groupDesc},#{opName},#{opId},now(),now())
    </insert>

    <delete id="deleteGroupById">
        DELETE
        FROM t_authority_group
        WHERE id = #{id};
    </delete>

    <update id="updateGroup" parameterType="com.cdz360.biz.auth.model.vo.AuthorityGroup">
        update t_authority_group
        <set>
            roleId=#{roleId},groupName=#{groupName},groupDesc=#{groupDesc},opName=#{opName},opId=#{opId},updateTime=now()
        </set>
        where id=#{id}
    </update>

    <select id="getGroupList" resultType="com.cdz360.biz.auth.model.vo.AuthorityGroup">
        SELECT
        id,roleId,groupName,groupDesc,opName,opId,createTime,updateTime
        FROM t_authority_group
    </select>

    <select id="getGroupById" resultType="com.cdz360.biz.auth.model.vo.AuthorityGroup">
        SELECT
        id,roleId,groupName,groupDesc,opName,opId,createTime,updateTime
        FROM t_authority_group
        WHERE
        id=#{id}
    </select>

    <select id="getGroupByNameNonId" resultType="com.cdz360.biz.auth.model.vo.AuthorityGroup">
        SELECT
        id,roleId,groupName,groupDesc,opName,opId,createTime,updateTime
        FROM t_authority_group
        WHERE
        groupName=#{groupName}
        <if test="notId != null">
            AND id <![CDATA[ <> ]]> #{notId}
        </if>
    </select>

    <select id="getGroupByRoleId" resultType="com.cdz360.biz.auth.model.vo.AuthorityGroup">
        SELECT
        id,roleId,groupName,groupDesc,opName,opId,createTime,updateTime
        FROM t_authority_group
        WHERE
        roleId=#{roleId}
    </select>

    <select id="getGroupListByRoleIdList" resultType="com.cdz360.biz.auth.model.vo.AuthorityGroup">
        SELECT
        id,roleId,groupName,groupDesc,opName,opId,createTime,updateTime
        FROM t_authority_group
        <where>
            roleId in
            <foreach item="item" index="index" collection="roleIdList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </select>
    <select id="getRoleCommIdByUserId" resultType="java.lang.String">
        SELECT
<!--            group_concat(commIds) as commIds-->
            commIds
        FROM
            sys_role role
            left JOIN sys_user_role sur ON role.id = sur.role_id
        WHERE
            sur.user_id = #{userId}
        and commIds is not null
        and commIds <![CDATA[ <> ]]> ''
    </select>

</mapper>