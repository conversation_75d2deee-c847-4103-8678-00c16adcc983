package com.cdz360.biz.auth.ds.ro.corp;

//import com.cdz360.biz.auth.corp.po.CorpPo;
import com.cdz360.biz.auth.ds.ro.corp.mapper.CorpRoMapper;
import com.cdz360.biz.model.cus.corp.param.ListCorpParam;
import com.cdz360.biz.model.cus.corp.po.CorpOrgPo;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.corp.vo.CorpSimpleVo;
import com.cdz360.biz.model.cus.corp.vo.CorpVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CorpRoDs {

    @Autowired
    private CorpRoMapper corpRoMapper;

    public CorpPo getCorpCountByCorpName(String blocUser) {
        return corpRoMapper.getCorpCountByCorpName(blocUser);
    }

    public CorpPo getCorpCountByAccount(String acount) {
        return corpRoMapper.getCorpCountByAccount(acount);
    }

    public CorpPo getCorpById(long id) {
        return corpRoMapper.getCorpById(id);
    }

    public CorpPo getCorpByUid(long uid) {
        return corpRoMapper.getCorpByUid(uid);
    }

    public List<CorpSimpleVo> getCorpByCommId(@NonNull String commIdChain,
                                              @Nullable Integer enable,
                                              @Nullable Long corpId) {
        return corpRoMapper.getCorpByCommId(commIdChain, enable, corpId);
    }

    /**
     * orgName 选传
     * @param corpId
     * @param l1Id
     * @param orgName
     * @return
     */
    public List<CorpOrgPo> getOrgByL1Id(Long corpId, Long l1Id, String orgName){
        return corpRoMapper.getOrgByL1Id(corpId, l1Id, orgName);
    }

    /**
     * orgName 选传
     * @param corpId
     * @param l2Id
     * @param orgName
     * @return
     */
    public List<CorpOrgPo> getOrgByL2Id(Long corpId, Long l2Id, String orgName){
        return corpRoMapper.getOrgByL2Id(corpId, l2Id, orgName);
    }

    public List<CorpPo> getCorpList (ListCorpParam param) {
        return corpRoMapper.getCorpList(param);
    }

    public Long getCorpCount(ListCorpParam param) {
        return this.corpRoMapper.getCorpCount(param);
    }

    /**
     * 获取需要续费提醒的企业
     * @return
     */
    public List<CorpPo> getNeedRemindCorp(long start, int size) {
        return corpRoMapper.getNeedRemindCorp(start, size);
    }

}
