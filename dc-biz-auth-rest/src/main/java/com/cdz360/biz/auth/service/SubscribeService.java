package com.cdz360.biz.auth.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.utils.*;
import com.cdz360.biz.auth.dao.SysUserRoleDao;
import com.cdz360.biz.auth.ds.ro.subscribe.SubscribeRoDs;
import com.cdz360.biz.auth.ds.rw.subscribe.SubscribeRwDs;
import com.cdz360.biz.auth.feign.DataCoreFeignClient;
import com.cdz360.biz.auth.feign.IotDeviceMgmFeignClient;
import com.cdz360.biz.auth.subscribe.param.*;
import com.cdz360.biz.auth.subscribe.po.SubscribePo;
import com.cdz360.biz.auth.subscribe.type.ModeType;
import com.cdz360.biz.auth.subscribe.type.PayChannelEnum;
import com.cdz360.biz.auth.subscribe.vo.*;
import com.cdz360.biz.auth.sys.vo.SysRoleSimpleVo;
import com.cdz360.biz.auth.utils.IotAssert;
import com.cdz360.biz.auth.utils.SubOrderIdGenerator;
import com.cdz360.biz.model.bi.dashboard.SiteAndPlugBiVo;
import com.cdz360.biz.model.sys.vo.SubscribeOrderVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SubscribeService {

    @Autowired
    private SubscribeRoDs subscribeRoDs;
    @Autowired
    private SubscribeRwDs subscribeRwDs;
    @Autowired
    private SubOrderIdGenerator subOrderIdGenerator;
    @Autowired
    private SysUserRoleDao sysUserRoleDao;
    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;
    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;


    @Transactional

    public BaseResponse add(AddSubscribeParam params) {
        IotAssert.isTrue(CollectionUtils.isNotEmpty(params.getCommIdList()), "商户信息不能为空");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(params.getRoleIdList()), "角色信息不能为空");
        IotAssert.isNull(subscribeRoDs.getSubByTitle(params.getTitle()), "标题已经存在");

        subscribeRwDs.add(params);
        subscribeRwDs.addSubComm(params.getId(), params.getTopCommId(), params.getCommIdList());
        subscribeRwDs.addSubRole(params.getId(), params.getRoleIdList());
        return RestUtils.success();
    }

    @Transactional
    public BaseResponse update(AddSubscribeParam params) {
        IotAssert.isNotNull(params.getId(), "ID不能为空");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(params.getCommIdList()), "商户信息不能为空");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(params.getRoleIdList()), "角色信息不能为空");
        IotAssert.isNotNull(subscribeRoDs.getById(params.getId()), "订阅信息不存在");
        SubscribePo subInfo = subscribeRoDs.getSubByTitle(params.getTitle());
        IotAssert.isTrue(subInfo != null && subInfo.getId().equals(params.getId()), "标题已经存在");

        subscribeRwDs.update(params);
        subscribeRwDs.updateSubComm(params.getId());
        subscribeRwDs.updateSubRole(params.getId());
        subscribeRwDs.addSubComm(params.getId(), params.getTopCommId(), params.getCommIdList());
        subscribeRwDs.addSubRole(params.getId(), params.getRoleIdList());
        return RestUtils.success();
    }

    public BaseResponse updateStatus(Long updateBy, Long subId) {
        subscribeRwDs.updateStatus(updateBy, subId);
        return RestUtils.success();
    }

    public ListResponse<SubscribeVo> getList(SubscribeListParam params) {
        if (params.getStart() == null) {
            params.setStart(0L);
        }
        if (params.getSize() == null) {
            params.setSize(10);
        }
        Long total = subscribeRoDs.getCount(params);
        if (total == null || total.equals(0L)) {
            return RestUtils.buildListResponse(null, total);
        }
        return new ListResponse<>(subscribeRoDs.getList(params), total);
    }

    public ListResponse<CommVo> getCommList(Long subId) {
        return new ListResponse<>(subscribeRoDs.getCommList(subId));
    }

    public ListResponse<SysRoleSimpleVo> getRoleList(Long subId) {
        return new ListResponse<>(subscribeRoDs.getRoleList(subId));
    }

    public ObjectResponse<SubscribeDetailVo> getDetail(Long subId) {
        SubscribePo subDetail = subscribeRoDs.getById(subId);
        IotAssert.isNotNull(subDetail, "内容不存在");
        return RestUtils.buildObjectResponse(subscribeRoDs.getDetail(subId));
    }

    public List<SubscribeDetailVo> getListByUser(Long sysId, Long commId,Boolean status) {
        return subscribeRoDs.getListByUser(sysId, commId,status);
    }

    @Transactional
    public ObjectResponse<String> createPayOrder(CreatePayOrderParam params) {
        IotAssert.isNotNull(params.getSubId(), "功能ID不能为空");
        SubscribePo subInfo = subscribeRoDs.getById(params.getSubId());
        IotAssert.isTrue(subInfo != null && Boolean.TRUE.equals(subInfo.getStatus()), "功能不存在");

        BigDecimal amount = BigDecimal.ZERO;
        if (ModeType.GUN_FEE.equals(subInfo.getChargingMode())) {
            ObjectResponse<SiteAndPlugBiVo> response = iotDeviceMgmFeignClient.getSiteAndPlugStatus(params.getCommId(), params.getCommIdChain());
//            ObjectResponse<Long> response = dataCoreFeignClient.getChargerNumByChain(params.getCommIdChain());
            if (response == null) {
                throw new DcServiceException("系统错误");
            }
            if (response.getData() == null || response.getData().getPlugCount()==0) {
                throw new DcServiceException("不存在枪头信息");
            }
            amount = subInfo.getFee().multiply(BigDecimal.valueOf(response.getData().getPlugCount()));
        } else {
            amount = subInfo.getFee();
        }

        params.setDays(subInfo.getDays())
                .setUnitPrice(subInfo.getFee())
                .setAmount(amount)
                .setPayNo("CZ".concat(subOrderIdGenerator.getNextPayBillId()));

        subscribeRwDs.createOrder(params);

        List<Long> roleIdList = subscribeRoDs.getRoleList(params.getSubId()).stream().map(SysRoleSimpleVo::getId).collect(Collectors.toList());
        // 批量插入  t_subscribe_order_user_role
        List<Map> mapList = new ArrayList<>();
        params.getUserIdList().stream().forEach(e -> roleIdList.stream().forEach(i -> {
            Map map = new HashMap<>();
            map.put("userId", e);
            map.put("roleId", i);
            mapList.add(map);
        }));
        subscribeRwDs.createOrderUser(params.getPayNo(), mapList);
        // 免费功能
        if (subInfo.getFee().equals(BigDecimal.ZERO)) {
            // t_subscribe_order 订单信息修改
            subscribeRwDs.updateSubOrder(params.getPayNo(), 0L, Boolean.TRUE);
            // 批量插入  sys_user_role
            List<SubscribeOrderDetailVo> userRoleList = subscribeRoDs.getRoleListByPayNo(params.getPayNo());
            sysUserRoleDao.batchAddRoleUserByList(subInfo.getDays(), userRoleList);
            // 更新t_subscribe_order_user_role中的expireTime
            subscribeRwDs.updateExpireTimeByPayNo(params.getPayNo());
        }
        return RestUtils.buildObjectResponse(params.getPayNo());
    }

    public ObjectResponse<SubscribeOrderVo> getOrderById(String payNo) {
        IotAssert.isNotNull(payNo, "订单号不能为空");
        return RestUtils.buildObjectResponse(subscribeRoDs.getOrderById(payNo));
    }

    public ListResponse<SubscribeLogVo> getSubLogList(SubLogListParam params) {
        Long total = subscribeRoDs.getLogCount(params);
        if (null == total || total == 0) {
            return RestUtils.buildListResponse(null, total);
        }
        if (params.getStart() == null) {
            params.setStart(0L);
        }
        if (params.getSize() == null) {
            params.setSize(10);
        }
        return RestUtils.buildListResponse(subscribeRoDs.getLogList(params), total);
    }

    public ListResponse<SubscribeOrderDetailVo> getRoleListByPayNo(String payNo) {
        return RestUtils.buildListResponse(subscribeRoDs.getRoleListByPayNo(payNo));
    }

    @Transactional
    public BaseResponse chargeNotify(NotifyPayParams params) {
        if (StringUtils.isEmpty(params.getOut_trade_no())) {
            log.error("功能订阅回调单号为空");
            throw new DcServiceException("功能订阅单号为空");
        }

        SubscribeOrderVo orderInfo = subscribeRoDs.getOrderById(params.getOut_trade_no());
        if (orderInfo == null || Boolean.TRUE.equals(orderInfo.getStatus())) {
            log.info("订单已完成");
            return RestUtils.success();
        }

        List<SubscribeOrderDetailVo> userRoleList = subscribeRoDs.getRoleListByPayNo(params.getOut_trade_no());

        if (CollectionUtils.isEmpty(userRoleList)) {
            log.error("信息不完整，payNo={}", params.getOut_trade_no());
            throw new DcServiceException("信息不完整");
        }
        Long payChannel = 0L;
        if (PayChannelEnum.WX_NATIVE.getName().equals(params.getPay_channel())) {
            payChannel = Long.valueOf(PayChannel.WXPAY.getCode());
        } else if (PayChannelEnum.ALIPAY_PAGE.getName().equals(params.getPay_channel())) {
            payChannel = Long.valueOf(PayChannel.ALIPAY.getCode());
        }
        // t_subscribe_order 订单信息修改
        subscribeRwDs.updateSubOrder(params.getOut_trade_no(), payChannel, Boolean.TRUE);
        // 批量插入  sys_user_role
        sysUserRoleDao.batchAddRoleUserByList(orderInfo.getDays(), userRoleList);
        // 更新t_subscribe_order_user_role中的expireTime
        subscribeRwDs.updateExpireTimeByPayNo(params.getOut_trade_no());

        return RestUtils.success();
    }

    public BaseResponse addNote(String payNo, String note) {
        IotAssert.isTrue(StringUtils.isNotEmpty(payNo), "订单编号不能为空");
        IotAssert.isTrue(StringUtils.isNotEmpty(note), "说明信息不能为空");
        subscribeRwDs.updateNoteByNo(payNo, note);
        return RestUtils.success();
    }

}
