<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.ds.rw.user.mapper.SysUserRwMapper">
    <insert id="insetUser" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
            INSERT INTO sys_user (
        username,
        password,
        salt,
        name,
        phone,
        topCommId,
        commId,
        status,
        platform,
        create_time,
        update_time
        )
        VALUES
        (
        #{username},
        #{password},
        #{salt},
        #{name},
        #{phone},
        #{topCommId},
        #{commId},
        #{status},
        #{platform},
        now(),
        now()
        )
    </insert>
    <insert id="insertOrg">
        INSERT INTO t_corp_user_org (
            corpId,
            sysUid,
            orgId,
            createTime
        )
        VALUES
        (
            #{corpId},
            #{userId},
            #{orgId},
            now()
        )
    </insert>

    <update id="updateCorpId" parameterType="com.cdz360.biz.auth.user.po.SysUserPo">
        update sys_user set corpId = #{corpId}
        where id = #{id}
    </update>
    <insert id="insertRole">
        INSERT INTO sys_user_role (
            user_id,
            role_id,
            create_by,
            create_time
        )
        VALUES
        (
            #{userId},
            #{roleId},
            #{createBy},
            now()
        )
    </insert>


    <update id="updateLastLoginTime">
        update sys_user set last_login_time = now()
        where id = #{id}
    </update>
    <update id="updateUserPasswordAndUsername">
        update sys_user set
        username = #{username}
        <if test = "password != null">
        ,password = #{password}
        </if>
         where id = #{sysUserId}
    </update>
    <update id="updateUserOrgById">
        update t_corp_user_org set orgId = #{orgId} where sysUid = #{userId}
    </update>
    <update id="updateUserRole">
        update sys_user_role set role_id = #{roleId},update_time = now(),update_by = #{updateBy} where user_id = #{userId}
    </update>
    <select id="getUserByOrg" resultType="java.lang.Long">
        SELECT
            a.sysUid
        FROM
            t_corp_user_org AS a
        LEFT JOIN t_corp_user_org b ON a.orgId = b.orgId
        WHERE
            b.sysUid = #{userId} UNION
        SELECT
            `sysUid`
        FROM
            t_corp_user_org a
        LEFT JOIN t_corp_org b ON a.orgId = b.id
        WHERE
            `l1Id` IN ( SELECT orgId FROM t_corp_user_org WHERE `sysUid` = #{userId} ) UNION
        SELECT
            `sysUid`
        FROM
            t_corp_user_org a
        LEFT JOIN t_corp_org b ON a.orgId = b.id
        WHERE
            `l2Id` IN ( SELECT orgId FROM t_corp_user_org WHERE `sysUid` = #{userId} )
    </select>

    <update id="moveCorp">
        update sys_user
        set
            topCommId = #{topCommId},
            commId = #{commId}
        where
            corpId = #{corpId}
    </update>
    <update id="updateOpenId">
          update sys_user
        set
            wxOpenId = #{wxOpenId}
        where
            id = #{userId}
    </update>
    <update id="updateUserId">
        update sys_user
        set wxOpenId = #{wxOpenId},
            nickname = #{nickname}
        where
            id = #{id}
    </update>
    <update id="updateTeamCatalogByPhone">
        update sys_user
        set teamCatalog = #{departmentName},
        corpWxAppId = #{corpWxAppId},
        corpWxUid = #{corpWxUid}
        where topCommId = #{topCommId}
        and phone = #{mobile}
<!--        and status = 1-->
    </update>
    <update id="clearSysUserGroupId">
        update sys_user
        set auditGroupId = null
        where auditGroupId = #{groupId}
    </update>
    <update id="updateSysUserGroupId">
        update sys_user
        set auditGroupId = #{groupId}
        where id in
        <foreach collection="uidList" item="item" open="("
                 separator="," close=")">
            #{item}
        </foreach>
    </update>

    <update id="updateUserStatusById">
        update sys_user
        set status=#{status},
        update_by = #{updateBy},
        update_time = now()
        where
        id = #{id}
    </update>

</mapper>