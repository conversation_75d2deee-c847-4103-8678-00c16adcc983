package com.cdz360.biz.auth.ds.ro.user;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.ds.ro.user.mapper.SysUserLogRoMapper;
import com.cdz360.biz.auth.user.param.SysUserLogParam;
import com.cdz360.biz.auth.user.po.SysUserLogPo;
import com.cdz360.biz.auth.user.vo.SysUserLogVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SysUserLogRoDs {

	@Autowired
	private SysUserLogRoMapper sysUserLogRoMapper;

	public SysUserLogPo getById(Long id) {
		return this.sysUserLogRoMapper.getById(id);
	}

    public ListResponse<SysUserLogVo> getLoginLog(SysUserLogParam param) {
        List<SysUserLogVo> data = this.sysUserLogRoMapper.getLoginLog(param);
        Long total = this.sysUserLogRoMapper.getLoginLogCount(param);
        return RestUtils.buildListResponse(data, total);
    }

    public ListResponse<SysUserLogVo> getOpLog(SysUserLogParam param) {
        List<SysUserLogVo> data = this.sysUserLogRoMapper.getOpLog(param);
        Long total = this.sysUserLogRoMapper.getOpLogCount(param);
        return RestUtils.buildListResponse(data, total);
    }
}
