package com.cdz360.biz.auth.config;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdz360.biz.auth.model.vo.BaseEntity;
import com.cdz360.biz.auth.utils.SysUserUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

import java.util.List;

@Aspect
@Component
@Slf4j
public class UserFillingAspect {
    public static void fillUserInfo(BaseEntity rs) {
        if (rs.getCreateUser() == null) {
            Long createBy = rs.getCreateBy();
            rs.setCreateUser(SysUserUtil.nameById(createBy));
        }
        if (rs.getUpdateUser() == null) {
            Long updateBy = rs.getUpdateBy();
            rs.setUpdateUser(SysUserUtil.nameById(updateBy));
        }
    }

    @Pointcut("execution(* com.baomidou.mybatisplus.service.impl.ServiceImpl.select*(..))")
    public void selectAop() {

    }

    @Around("selectAop()")
    public Object findByIdRet(ProceedingJoinPoint point) throws Throwable {

        Object rs = point.proceed();

        try {
            if (rs instanceof BaseEntity) {
                fillUserInfo((BaseEntity) rs);
            } else if (rs instanceof List) {
                for (Object r : ((List) rs)) {
                    if (r instanceof BaseEntity) {
                        fillUserInfo((BaseEntity) r);
                    }
                }
            } else if (rs instanceof Page) {
                List records = ((Page) rs).getRecords();
                for (Object record : records) {
                    if (record instanceof BaseEntity) {
                        fillUserInfo((BaseEntity) record);
                    }
                }
            }
        } catch (Exception e) {
            // 不影响现有逻辑
        }
        return rs;
    }
}
