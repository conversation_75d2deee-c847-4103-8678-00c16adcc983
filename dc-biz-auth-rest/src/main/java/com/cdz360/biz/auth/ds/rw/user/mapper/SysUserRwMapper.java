package com.cdz360.biz.auth.ds.rw.user.mapper;

import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.user.po.SysUserPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysUserRwMapper {

    int updateLastLoginTime(@Param("id") Long id);
    int updateUserPasswordAndUsername(Long sysUserId, String password, String username);
    int insetUser(SysUserPo sysUser);
    int updateCorpId(SysUserPo sysUser);
    int updateUserId(SysUserPo sysUser);
    int updateUserStatusById(SysUser sysUser);
    int updateOpenId(@Param("userId") Long userId,@Param("wxOpenId") String wxOpenId);
    int insertOrg (Long corpId,Long userId,Long orgId);
    int insertRole (Long userId,Long roleId,Long createBy);
    List<Long> getUserByOrg (Long userId);
    int updateUserOrgById (Long userId,Long orgId);
    int updateUserRole(Long userId,Long roleId,Long updateBy);

    int moveCorp(@Param("corpId") Long corpId,
                 @Param("commId") Long commId,
                 @Param("topCommId") Long topCommId);

    int updateTeamCatalogByPhone(
            @Param("topCommId") Long topCommId,
            @Param("mobile") String mobile,
            @Param("departmentName") String departmentName,
            @Param("corpWxUid") String corpWxUid,
            @Param("corpWxAppId") Long corpWxAppId);

    int clearSysUserGroupId(@Param("groupId") String groupId);

    int updateSysUserGroupId(@Param("groupId") String groupId,
                             @Param("uidList") List<Long> uidList);
}
