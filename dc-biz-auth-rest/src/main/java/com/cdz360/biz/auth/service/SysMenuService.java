package com.cdz360.biz.auth.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.auth.dao.SysMenuDao;
import com.cdz360.biz.auth.dao.SysRoleMenuDao;
import com.cdz360.biz.auth.dao.SysSystemDao;
import com.cdz360.biz.auth.model.vo.SysMenu;
import com.cdz360.biz.auth.model.vo.SysRole;
import com.cdz360.biz.auth.model.vo.SysUser;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Slf4j
@Service
public class SysMenuService extends ServiceImpl<SysMenuDao, SysMenu> {

    @Autowired
    private SysUserService userService;

    @Autowired
    private SysRoleService roleService;

    @Autowired
    private SysRoleMenuDao sysRoleMenuDao;

    @Autowired
    private SysMenuDao menuDao;

    @Autowired
    private SysSystemDao systemDao;

    public Set<String> findPermByUserId(Long userId) {
        SysUser sysUser =
            userService.getBaseMapper().selectById(userId);
        if (sysUser == null || sysUser.isFreeze()) {
            return Collections.emptySet();
        }
        List<SysRole> roleList = roleService.findByUserId(userId);
        if (roleList.stream().anyMatch(sysRole -> !sysRole.isFreeze() && sysRole.getSvaha())) {
            return baseMapper.findPermByUserId(null);
        }
        return baseMapper.findPermByUserId(userId);
    }

    public void fillPids(SysMenu menu) {
        Long pid = menu.getPid();
        LinkedList<Long> pidList = new LinkedList<>();
        appendPids(pid, pidList);
        menu.setPids(StringUtils.collectionToDelimitedString(pidList, ","));
    }

    private void appendPids(Long pid, LinkedList<Long> pidList) {
        if (pid == null || pid == 0) {
            return;
        }
        pidList.addFirst(pid);
        SysMenu sysMenu = getBaseMapper().selectById(pid);
        if (sysMenu == null) {
            return;
        }
        appendPids(sysMenu.getPid(), pidList);
    }

    public Long countMenuByUniqueKeyAndOtherId(Long subsysId, String name, Integer type, Long id) {
        return baseMapper.countMenuByUniqueKeyAndOtherId(subsysId, name, type, id);
    }

    public SysMenu getMenuById(Long id) {
        return baseMapper.getMenuById(id);
    }

    public void postModifyMenu(SysMenu menu) {
        List<Long> roleIdList = sysRoleMenuDao.notYeyRoleMenu(menu.getId(), menu.getPid());
        if (CollectionUtils.isNotEmpty(roleIdList)) {
            Integer i = sysRoleMenuDao.saveBatchByMenuId(menu.getPid(), roleIdList);
            log.info("调整菜单后，角色菜单关系新增: roleIdList = {}, menuId = {}, i = {}",
                roleIdList, menu.getId(), i);
        }
    }

    public List<SysMenu> getAdminIndexMenu(Long uid, Integer platform) {
        val id = systemDao.selectByPlatform(platform);
        if (null == id) {
            throw new DcArgumentException("用户所属平台标识无效");
        }

        return menuDao.findMenuByUserIdAndSysId(uid, id, true);
    }
}
