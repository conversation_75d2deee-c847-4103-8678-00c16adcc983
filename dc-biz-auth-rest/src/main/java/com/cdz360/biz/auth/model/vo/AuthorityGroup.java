package com.cdz360.biz.auth.model.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * authorityGroup
 *
 * @since 2020/2/10 2:46
 * <AUTHOR>
 */
@Data
@Schema(description = "权限组")
@Accessors(chain = true)
public class AuthorityGroup {
    private Long id;
    @Schema(description = "关联的角色id")
    private Long roleId;
    @Schema(description = "组名称")
    private String groupName;
    @Schema(description = "组描述")
    private String groupDesc;
    @Schema(description = "操作者")
    private String opName;
    private Long opId;
    private Date createTime;
    private Date updateTime;

    private List<AuthGroupRef> authGroupRef;
}
