package com.cdz360.biz.auth.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.ds.ro.sys.SysUserReportTemplateRoDs;
import com.cdz360.biz.auth.ds.rw.sys.SysUserReportTemplateRwDs;
import com.cdz360.biz.model.sys.constant.ReportPage;
import com.cdz360.biz.model.sys.po.SysUserReportTemplatePo;
import com.cdz360.biz.auth.utils.IotAssert;
import com.cdz360.biz.auth.utils.RegularExpressionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ReportTemplateService {

    @Autowired
    private SysUserReportTemplateRoDs reportTemplateRoDs;
    @Autowired
    private SysUserReportTemplateRwDs reportTemplateRwDs;

    public List<SysUserReportTemplatePo> getInfo(long susUserId, Integer page) {
        return this.reportTemplateRoDs.getInfo(susUserId, page);
    }

    public BaseResponse add(SysUserReportTemplatePo template) {

        IotAssert.isTrue(RegularExpressionUtil.chineseEnglishNumberCharacterAll(template.getTemplateName(), 1, 10),
                "不能超过10个字符，只能输入数字、汉字、字母、常规字符");
        long total = this.reportTemplateRoDs.countBySysUserId(template.getSysUserId(), template.getPage());
        IotAssert.isTrue(total < 5, "最多创建5个模板");
        return this.reportTemplateRwDs.add(template) > 0 ? RestUtils.success() : RestUtils.serverBusy();
    }

    public BaseResponse delete(Long templateId) {

        return this.reportTemplateRwDs.delete(templateId) > 0 ? RestUtils.success() : RestUtils.serverBusy();
    }
}
