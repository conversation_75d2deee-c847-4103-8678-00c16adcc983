package com.cdz360.biz.auth.rest;

import com.cdz360.biz.auth.model.vo.Rez;
import com.cdz360.biz.auth.model.vo.SysLoginLog;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.service.SysLoginLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@Slf4j
@RequestMapping("/data/login")
@Deprecated
public class LoginLogController //implements LoginLogInterface
{
    @Autowired
    private SysLoginLogService loginLogService;

    @PostMapping("/check")
    public Boolean checkTimeOver(@RequestBody SysUser su) {
        log.error("deprecated !!! LoginLogController.checkTimeOver");
//        SysLoginLog log = loginLogService.selectById(su.getId());
//        if (log != null) {
//            return loginLogService.checkTimeOver(log.getLastLoginTime(), su);
//        }
        return false;
    }

    @DeleteMapping("/{id}")
    public Rez delete(@PathVariable("id") Long id) {
        log.error("deprecated !!! LoginLogController.delete");
        return Rez.hasModified(loginLogService.getBaseMapper().deleteById(id) > 0);
    }

    @GetMapping("/{id}")
    public SysLoginLog getLog(@PathVariable("id") Long id) {
        log.error("deprecated !!! LoginLogController.getLog");
        return loginLogService.getBaseMapper().selectById(id);
    }

}
