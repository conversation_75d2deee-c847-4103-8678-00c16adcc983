package com.cdz360.biz.auth.rest;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.config.CurrentUser;
import com.cdz360.biz.auth.config.SysLog;
import com.cdz360.biz.auth.dao.SysUserDao;
import com.cdz360.biz.auth.dao.SysUserRoleDao;
import com.cdz360.biz.auth.ds.ro.corp.CorpRoDs;
import com.cdz360.biz.auth.ds.ro.user.SiteGroupRoDs;
import com.cdz360.biz.auth.ds.ro.user.SiteGroupUserRoDs;
import com.cdz360.biz.auth.ds.ro.user.SysUserRoDs;
import com.cdz360.biz.auth.ds.rw.user.SiteGroupUserRwDs;
import com.cdz360.biz.auth.ds.rw.user.SysUserRwDs;
import com.cdz360.biz.auth.feign.reactor.OaAccountFeignClient;
import com.cdz360.biz.auth.feign.reactor.SiteGroupSiteFeignClient;
import com.cdz360.biz.auth.model.constant.GlobalConst;
import com.cdz360.biz.auth.model.dto.AuthRequest;
import com.cdz360.biz.auth.model.dto.CheckPasswordRespDto;
import com.cdz360.biz.auth.model.dto.ModifyPassDto;
import com.cdz360.biz.auth.model.dto.ValidPhoneSysDto;
import com.cdz360.biz.auth.model.param.AddUserGroupRequest;
import com.cdz360.biz.auth.model.type.ErrStatus;
import com.cdz360.biz.auth.model.vo.Rez;
import com.cdz360.biz.auth.model.vo.SysLoginLog;
import com.cdz360.biz.auth.model.vo.SysMenu;
import com.cdz360.biz.auth.model.vo.SysRole;
import com.cdz360.biz.auth.model.vo.SysSystem;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.model.vo.SysUserComercial;
import com.cdz360.biz.auth.model.vo.SysUserRole;
import com.cdz360.biz.auth.model.vo.SysUserVo;
import com.cdz360.biz.auth.model.vo.TCommercial;
import com.cdz360.biz.auth.model.vo.UserVo;
import com.cdz360.biz.auth.service.AuthorityService;
import com.cdz360.biz.auth.service.SysLoginLogService;
import com.cdz360.biz.auth.service.SysSystemService;
import com.cdz360.biz.auth.service.SysUserRoleService;
import com.cdz360.biz.auth.service.SysUserService;
import com.cdz360.biz.auth.service.TCommercialService;
import com.cdz360.biz.auth.sys.param.BatchAddRoleUserParam;
import com.cdz360.biz.auth.sys.param.QuerySysUserRequest;
import com.cdz360.biz.auth.sys.param.RoleUserListParam;
import com.cdz360.biz.auth.sys.param.RoleUserUpdateParam;
import com.cdz360.biz.auth.sys.vo.RoleUserVo;
import com.cdz360.biz.auth.user.param.ListSysUserParam;
import com.cdz360.biz.auth.user.po.SysUserPo;
import com.cdz360.biz.auth.utils.IotAssert;
import com.cdz360.biz.auth.utils.RegularExpressionUtil;
import com.cdz360.biz.model.cus.corp.dto.UserIdDto;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.param.SysUserCheckParam;
import com.cdz360.biz.model.sys.dto.UserOwnerSiteGroupDto;
import com.cdz360.biz.model.sys.param.SiteGroupSiteParam;
import com.cdz360.biz.model.sys.vo.SiteGroupVo;
import com.cdz360.biz.oa.dto.account.OaAccountDto;
import com.cdz360.biz.oa.dto.account.OaGroupDto;
import com.cdz360.biz.oa.dto.account.OaMemberGroupListDto;
import com.cdz360.biz.oa.param.account.ListOaGroupParam;
import com.cdz360.biz.oa.param.account.ListOaGroupUserParam;
import com.cdz360.biz.oa.param.account.OaModifyGroupParam;
import com.google.common.hash.Hashing;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
@RequestMapping("/data/users")
public class SysUserController implements SysUserInterface {

    //    public static final int NO_MENU_FORBIDDEN = 4031;
    @Autowired
    private OaAccountFeignClient oaAccountFeignClient;

    @Autowired
    private SysUserDao sysUserDao;
    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SysUserRoleService userRoleService;

    @Autowired
    private SysLoginLogService loginLogService;
    @Autowired
    private SysSystemService systemService;

    @Autowired
    private TCommercialService tCommercialService;

    @Autowired
    private AuthorityService authorityService;

//    @Autowired
//    private com.cdz360.biz.auth.ds.ro.user.SysUserRoDs sysUserRoDs;

    @Autowired
    private CorpRoDs corpRoDs;

    @Autowired
    private SysUserRwDs sysUserRwDs;

    @Autowired
    private SysUserRoDs sysUserRoDs;

    @Autowired
    private SiteGroupRoDs siteGroupRoDs;

    @Autowired
    private SysUserRoleDao sysUserRoleDao;

    @Autowired
    private SiteGroupUserRwDs siteGroupUserRwDs;

    @Autowired
    private SiteGroupUserRoDs siteGroupUserRoDs;

    @Autowired
    private SiteGroupSiteFeignClient siteGroupSiteFeignClient;


    @Override
    @GetMapping("/{id}")
    public ObjectResponse<SysUser> findById(@CurrentUser SysUser user,
        @PathVariable Long id) {
        log.info("查询账号信息. id= {}, user= {}", id, JsonUtils.toJsonString(user));
        SysUser sysUser = sysUserService.selectById(id);
        // 处理 null
        if (sysUser == null) {
            ObjectResponse<SysUser> res = new ObjectResponse<>();
            res.setStatus(ErrStatus.RES_NOT_FOUND.getErrcode())
                .setError(ErrStatus.RES_NOT_FOUND.getErrmsg());
            return res;
//            return Rez.error(ErrStatus.RES_NOT_FOUND);
        }
        List<SysRole> roles = sysUserService.findUserRoleListByUid(id);
        sysUser.setRoleList(roles);
        List<Long> roleIdListByUserId = userRoleService.findRoleIdListByUserId(id);
        sysUser.setRoleIdList(roleIdListByUserId);

        sysUserService.fillUserCommercialName(sysUser);

        if (user.getPlatform() == AppClientType.CORP_WEB.getCode()) {
            Long roleIdByUserId = userRoleService.findRoleIdByUserId(id);
            sysUser.setRoleId(roleIdByUserId);
            //获取组织ID
            Long orgIdByUserId = userRoleService.findOrgIdByUserId(id);
            sysUser.setOrgId(orgIdByUserId);
        }

        // 管理平台账号、工商储账号获取场站组信息
        if (List.of(AppClientType.MGM_WEB.getCode(), AppClientType.COMM_ESS_MGM.getCode())
            .contains(sysUser.getPlatform())) {
            Optional<UserOwnerSiteGroupDto> optionalUserOwnerSiteGroup = Optional.ofNullable(
                siteGroupRoDs.getUserOwnerSiteGroup(sysUser.getId(), null));

            optionalUserOwnerSiteGroup.ifPresent(userOwnerSiteGroup -> {
                if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(
                    userOwnerSiteGroup.getGroupVoList())) {
                    sysUser.setGids(userOwnerSiteGroup.getGroupVoList().stream()
                        .map(SiteGroupVo::getGid)
                        .collect(Collectors.toList()));
                }
            });
        }

        // 附加权限组
        sysUser.setAuthorityGroupList(authorityService.getAuthorityGroupListByUid(sysUser.getId()));

        return RestUtils.buildObjectResponse(sysUser);
    }

    @GetMapping("/entityInfo/{id}")
    public ObjectResponse<SysUser> entityInfo(@CurrentUser SysUser sysUser,
        @PathVariable("id") Long id) {
        return findById(sysUser, id);
    }


    @PostMapping("/entity")
    public Rez<SysUser> entity(@RequestBody SysUser su) {
        return Rez.data(sysUserDao.selectOne(new QueryWrapper<>(su)));
//        return Rez.data(sysUserDao.selectOne(su));
    }


    @SysLog("添加用户")
    @Transactional
    @PostMapping("")
    public ObjectResponse<Long> add(
        @CurrentUser SysUser sysUser,
        @RequestBody SysUser user) {
        log.info("增加用户,{}", JsonUtils.toJsonString(user));
        log.info("当前登录用户,{}", JsonUtils.toJsonString(sysUser));
        if (user.getPlatform() != null && user.getPlatform() == AppClientType.SASS_MGM_WEB.getCode()
            && com.cdz360.base.utils.CollectionUtils.isNotEmpty(user.getAuthGroupIds())) {
            // 【所属平台】为运营支撑平台时不能配置权限
            throw new DcServiceException(ErrStatus.ACCOUNT_ACCESS_ERROR.getErrcode(),
                ErrStatus.ACCOUNT_ACCESS_ERROR.getErrmsg());
        }

        if (user.getPlatform() == AppClientType.MGM_WEB.getCode()
            && user.getCommercialId() == null) {
            // 【所属平台】为充电管理平台时需配置所属商户
            throw new DcServiceException(ErrStatus.COMMERCIALID_NOT_EXIST.getErrcode(),
                ErrStatus.COMMERCIALID_NOT_EXIST.getErrmsg());
        }
        if (com.cdz360.base.utils.StringUtils.isNotBlank(user.getEmail())
            && !RegularExpressionUtil.isEmail(user.getEmail())) {
            throw new DcServiceException(ErrStatus.EMAIL_ERROR.getErrcode(),
                ErrStatus.EMAIL_ERROR.getErrmsg());
        }

        if (user.getPlatform() != AppClientType.CORP_WEB.getCode()) {
//            IotAssert.isTrue(sysUserDao.checkByPhoneAndPlantform(user.getPhone(), user.getPlatform(), null) == 0,
//                    "保存失败：手机号对应的账号已存在");
            IotAssert.isTrue(
                sysUserDao.checkUserNameUniqueAndPlantform(user.getUsername(), user.getPlatform(),
                    null) == 0,
                "保存失败：对应的账号已存在");
        }

//        if (com.cdz360.base.utils.StringUtils.isBlank(user.getUsername())) {
//            user.setUsername(user.getPhone());
//        }
        if (com.cdz360.base.utils.StringUtils.isBlank(user.getName())) {
            user.setName(user.getUsername());
        }
        List<SysUser> sysUsers = sysUserDao.checkByUserNameOrEmail(user);

        if (!CollectionUtils.isEmpty(sysUsers)) {
            for (SysUser su : sysUsers) {
                if (user.getUsername().equals(su.getUsername()) || user.getUsername()
                    .equals(su.getPhone()) || user.getUsername().equals(su.getEmail())) {
                    throw new DcServiceException(ErrStatus.USERNAME_CONFLICT.getErrcode(),
                        ErrStatus.USERNAME_CONFLICT.getErrmsg());
                }
                if (com.cdz360.base.utils.StringUtils.isNotBlank(user.getEmail())
                    && (user.getEmail().equals(su.getUsername()) || user.getEmail()
                    .equals(su.getPhone()) || user.getEmail().equals(su.getEmail()))) {
                    throw new DcServiceException(ErrStatus.EMAIL_CONFLICT.getErrcode(),
                        ErrStatus.EMAIL_CONFLICT.getErrmsg());
                }
                if (user.getPhone().equals(su.getUsername()) || user.getPhone()
                    .equals(su.getPhone()) || user.getPhone().equals(su.getEmail())) {
                    throw new DcServiceException(ErrStatus.PHONE_CONFLICT.getErrcode(),
                        ErrStatus.PHONE_CONFLICT.getErrmsg());
                }
            }
            throw new DcServiceException(ErrStatus.NAME_CONFLICT.getErrcode(),
                ErrStatus.NAME_CONFLICT.getErrmsg());
        }
        if (!StringUtils.isEmpty(user.getCredentials())) {
            QueryWrapper<SysUser> wrapper = Wrappers.query();
            wrapper.eq("credentials", user.getCredentials());
            Long count = sysUserService.getBaseMapper().selectCount(wrapper);
//            Integer count = sysUserService.getBaseMapper().selectCount(
//                Condition.create().eq("credentials", user.getCredentials()));
            if (count > 0L) {
                throw new DcServiceException(ErrStatus.CREDENTIALS_ERROR.getErrcode(),
                    ErrStatus.CREDENTIALS_ERROR.getErrmsg());
            }
        }

        // 企业管理平台处理
        if (user.getPlatform() == AppClientType.CORP_WEB.getCode()) {
            //判断账号的唯一性
            SysUserPo userInfo = sysUserRoDs.getByUsername(user.getUsername(),
                AppClientType.CORP_WEB.getCode());
            if (userInfo != null) {
                throw new DcServiceException(ErrStatus.USERNAME_CONFLICT.getErrcode(),
                    ErrStatus.USERNAME_CONFLICT.getErrmsg());
            }
            //获取企业信息
            CorpPo corpInfo = corpRoDs.getCorpById(sysUser.getCorpId());
            //企业平台加密方式跟其他不一样
            String password = Base64.getEncoder().encodeToString(Hashing.sha256()
                .hashString(corpInfo.getCorpName() + user.getPassword(), Charset.defaultCharset())
                .asBytes());
//            String password = new Sha256Hash(user.getPassword(), corpInfo.getCorpName()).toBase64();
            user.setPassword(password);
            user.setCommId(sysUser.getCommId());
            user.setTopCommId(sysUser.getTopCommId());
            user.setCreateBy(sysUser.getId());
            user.setSalt(corpInfo.getCorpName());
            user.setCorpId(sysUser.getCorpId());
            user.setPhone("0");
        } else if (user.getPlatform() == AppClientType.COMM_ESS_MGM.getCode()) { // 工商储能账号
            user.setName(user.getUsername());
            user.setCreateBy(sysUser.getId());

            String hashedPassword = user.hashedPassword();
            user.setPassword(hashedPassword);
        } else {
            if (user.getCommercialId() != null) {
                TCommercial comm = this.tCommercialService.selectById(user.getCommercialId());
                if (comm != null) {
                    user.setCommId(comm.getId());
                    user.setTopCommId(comm.getTopCommId());
                }

                //管理平台，账号所属平台跟所属商户保持一致
//                if (user.getPlatform() == AppClientType.MGM_WEB.getCode() && comm != null) {
//                    user.setPlatform(comm.getPlatform());
//                }
            }
            String hashedPassword = user.hashedPassword();
            user.setPassword(hashedPassword);
        }

        boolean insertOK = sysUserService.addUser(user);

        //企业平台徐插入的表
        if (insertOK && sysUser.getPlatform() == AppClientType.CORP_WEB.getCode()) {
            sysUserRwDs.insertOrg(sysUser.getCorpId(), user.getId(), user.getOrgId());
            sysUserRwDs.insertRole(user.getId(), user.getRoleId(), sysUser.getId());
        }

        // 工商储平台需要插入角色
        if (insertOK && com.cdz360.base.utils.CollectionUtils.isNotEmpty(user.getRoleIdList()) && (
            sysUser.getPlatform() == AppClientType.COMM_ESS_MGM.getCode()
                || sysUser.getPlatform() == AppClientType.MGM_WEB.getCode())) {
            BatchAddRoleUserParam batchAddRoleUserParam = new BatchAddRoleUserParam();
            batchAddRoleUserParam.setUserId(user.getId());
            batchAddRoleUserParam.setRoleIdList(user.getRoleIdList());
            batchAddRoleUserParam.setExpireTime(null);
            sysUserRoleDao.batchAddRoleUserByUserId(batchAddRoleUserParam);
        }

        if (insertOK && user.getId() != null) {
            if (user.getCommercialId() != null) {
                SysUserComercial suc = new SysUserComercial();
                suc.setUserId(user.getId());
                suc.setCommercialId(user.getCommercialId());
                suc.setCommercialRole(1);
                suc.setMain(false); // 仅在商户创建时才 true 值
                tCommercialService.insertUserCommercials(suc);
            }

            if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(user.getAuthGroupIds())) {
                AddUserGroupRequest addUserGroupRequest = new AddUserGroupRequest();
                addUserGroupRequest.setUserId(user.getId());
                addUserGroupRequest.setAuthGroupIds(user.getAuthGroupIds());
                int count = authorityService.modifyUserGroupRef(addUserGroupRequest);
                log.info("新增用户组数量: {}", count);//TODO 需要检查是否存在group
            } else {
                log.info("未新增用户组, insertOK: {}, userId: {}, groupList: {}",
                    insertOK, user.getId(), user.getAuthGroupIds());
            }
        }
//        return Rez.hasModified(insertOK, user);
        return insertOK ? RestUtils.buildObjectResponse(user.getId())
            : RestUtils.serverBusy4ObjectResponse();
    }

    @SysLog("修改用户信息")
    @Transactional
    @PutMapping("/modify")
    public BaseResponse modify(@CurrentUser SysUser currentUser,
        @RequestBody SysUser bodyEntity) {
        log.info("修改用户信息,{}", JsonUtils.toJsonString(bodyEntity));

        //企业管理平台处理
        if (bodyEntity.getPlatform() == AppClientType.CORP_WEB.getCode()) {

            IotAssert.isTrue(bodyEntity.getRoleId() != null && bodyEntity.getRoleId() > 0,
                "请选择所属角色");
            IotAssert.isTrue(bodyEntity.getOrgId() != null && bodyEntity.getOrgId() > 0,
                "请选择所属组织");
            //通过ID 获取用户信息
            SysUserPo user = sysUserRoDs.getByUseId(bodyEntity.getId());
            if (user == null) {
                throw new DcServiceException(500, "账号信息不存在");
            }
            //修改账号密码
            String password = null;
            if (bodyEntity.getPassword() != null) {
                password = Base64.getEncoder().encodeToString(Hashing.sha256()
                    .hashString(user.getSalt() + bodyEntity.getPassword(), Charset.defaultCharset())
                    .asBytes());
//                password = new Sha256Hash(bodyEntity.getPassword(), user.getSalt()).toBase64();
            }
            sysUserRwDs.updateUserPasswordAndUsername(user.getId(), password,
                bodyEntity.getUsername());
            sysUserRwDs.updateUserOrgById(user.getId(), bodyEntity.getOrgId());
            sysUserRwDs.updateUserRole(user.getId(), bodyEntity.getRoleId(), currentUser.getId());

//            SysUser sysUser = sysUserService.selectById(bodyEntity.getId());

            return RestUtils.success();

        }

        IotAssert.isTrue(com.cdz360.base.utils.StringUtils.isNotBlank(bodyEntity.getUsername()),
            "请输入账号");
        IotAssert.isTrue(bodyEntity.getUsername().length() <= 40, "账号最大长度40个字符");
        IotAssert.isTrue(RegularExpressionUtil.chineseEnglishNumberAll(bodyEntity.getUsername()),
            "账号只支持字母、数字、汉字");
//        IotAssert.isTrue(com.cdz360.base.utils.StringUtils.isNotBlank(bodyEntity.getPassword()), "请输入密码");
        IotAssert.isTrue(bodyEntity.getPlatform() != null && bodyEntity.getPlatform() > 0,
            "请选择所属平台");

//        IotAssert.isTrue(sysUserDao.checkByPhoneAndPlantform(bodyEntity.getPhone(), bodyEntity.getPlatform(), id) == 0,
//                "保存失败：手机号对应的账号已存在");

        if (com.cdz360.base.utils.StringUtils.isNotBlank(bodyEntity.getEmail())
            && !RegularExpressionUtil.isEmail(bodyEntity.getEmail())) {
            throw new DcServiceException(ErrStatus.EMAIL_ERROR.getErrcode(),
                ErrStatus.EMAIL_ERROR.getErrmsg());
        }

        if (bodyEntity.getId() == null) {
            throw new DcServiceException(500, "userid null");
        }
        List<SysUser> suList = sysUserService.checkByUserNameOrPhoneOrEmail(bodyEntity);

        if (!CollectionUtils.isEmpty(suList)) {
            for (SysUser su : suList) {
                if (su.getId().equals(bodyEntity.getId())) {
                    continue;
                }
                if (su.getUsername().equals(bodyEntity.getUsername())) {
                    throw new DcServiceException(ErrStatus.USERNAME_CONFLICT.getErrcode(),
                        ErrStatus.USERNAME_CONFLICT.getErrmsg());
                }
                if (com.cdz360.base.utils.StringUtils.isNotBlank(bodyEntity.getEmail()) &&
                    su.getEmail().equals(bodyEntity.getEmail())
                ) {
                    throw new DcServiceException(ErrStatus.EMAIL_CONFLICT.getErrcode(),
                        ErrStatus.EMAIL_CONFLICT.getErrmsg());
                }
//                if (su.getPhone().equals(bodyEntity.getPhone()) ) {
//                    return Rez.error(ErrStatus.PHONE_CONFLICT);
//                }
            }
        }
        if (!StringUtils.isEmpty(bodyEntity.getCredentials())) {
            QueryWrapper<SysUser> wrapper = Wrappers.query();
            wrapper.eq("credentials", bodyEntity.getCredentials())
                .ne("id", bodyEntity.getId());
            Long count = sysUserService.getBaseMapper().selectCount(wrapper);
//            Integer count = sysUserService.getBaseMapper().selectCount(
//                Condition.create().eq("credentials", bodyEntity.getCredentials())
//                    .ne("id", bodyEntity.getId()));
            if (count > 0L) {
                throw new DcServiceException(ErrStatus.CREDENTIALS_ERROR.getErrcode(),
                    ErrStatus.CREDENTIALS_ERROR.getErrmsg());
            }
        }

        if (com.cdz360.base.utils.StringUtils.isNotBlank(bodyEntity.getPassword())) {
            bodyEntity.setPassword(bodyEntity.hashedPassword());
        }

        boolean updateOK = sysUserService.updateUser(bodyEntity);
        if (updateOK
            && bodyEntity.getId() != null
            /*&& com.cdz360.base.utils.CollectionUtils.isNotEmpty(bodyEntity.getAuthGroupIds())*/) {
            AddUserGroupRequest addUserGroupRequest = new AddUserGroupRequest();
            addUserGroupRequest.setUserId(bodyEntity.getId());
            addUserGroupRequest.setAuthGroupIds(bodyEntity.getAuthGroupIds());
            int count = authorityService.modifyUserGroupRef(addUserGroupRequest);
            log.info("新增用户组数量: {}", count);//TODO 需要检查是否存在group

            // 工商储平台   需要修改角色 场站组
            if (currentUser.getPlatform() != null
                && AppClientType.COMM_ESS_MGM.getCode() == currentUser.getPlatform().intValue()) {
                if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(bodyEntity.getRoleIdList())) {
                    QueryWrapper<SysUserRole> wrapper = Wrappers.query();
                    wrapper.eq("user_id", bodyEntity.getId());
                    userRoleService.getBaseMapper().delete(wrapper);
                    BatchAddRoleUserParam batchAddRoleUserParam = new BatchAddRoleUserParam();
                    batchAddRoleUserParam.setUserId(bodyEntity.getId());
                    batchAddRoleUserParam.setRoleIdList(bodyEntity.getRoleIdList());
                    sysUserRoleDao.batchAddRoleUserByUserId(batchAddRoleUserParam);
                }

                if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(bodyEntity.getGids())) {
                    siteGroupUserRwDs.deleteByUid(bodyEntity.getId());
                    siteGroupUserRwDs.batchInsert(bodyEntity.getId(), bodyEntity.getGids());
                }
            }
        } else {
            log.info("未新增用户组, insertOK: {}, userId: {}, groupList: {}",
                updateOK, bodyEntity.getId(), bodyEntity.getAuthGroupIds());
        }

        //回显数据用
//        SysUser sysUser = sysUserService.selectById(bodyEntity.getId());

        return RestUtils.success();
    }

    @SysLog("账号-停用启用")
    @Transactional
    @PutMapping("/changeState")
    public BaseResponse changeState(@CurrentUser SysUser sysUser, @RequestBody SysUser bodyEntity) {
        IotAssert.isNotNull(bodyEntity.getStatus(), "未传入状态");
        if (sysUser != null) {
            bodyEntity.setUpdateBy(sysUser.getId());
        }
        boolean res = sysUserService.updateUserStatusById(bodyEntity);
        return res ? RestUtils.success() : RestUtils.serverBusy();
    }


    @SysLog("删除用户")
    @DeleteMapping("{id}")
    public Rez delete(@PathVariable Long id) {
        log.info("删除用户信息,{}", id);
        int b = sysUserService.getBaseMapper().deleteById(id);
//        QueryWrapper<SysUserRole> wrapper = ;
        userRoleService.getBaseMapper().delete(Wrappers.query(SysUserRole.class)
            .eq("user_id", id));
        return Rez.hasModified(b > 0);
    }


    @SysLog("密码校验")
    @PostMapping("/validate")
    public CheckPasswordRespDto checkPassword(@RequestBody @Valid AuthRequest user) {
        CheckPasswordRespDto resp = new CheckPasswordRespDto();

        SysUser storedUser = sysUserService.storedUser(user.toSysUser());
        //用户不存在
        if (storedUser == null) {
            resp.setCode(CheckPasswordRespDto.NOT_MATCH);
            return resp;
        }
        //判断是否有输错密码的记录
        SysLoginLog log = loginLogService.getBaseMapper().selectById(storedUser.getId());
        if (log != null) {
            loginLogService.checkTimeOver(log.getLastLoginTime(), storedUser);
        }

        if (storedUser.getLocked() != null && storedUser.getLocked() == GlobalConst.LOCKED) {
            resp.setCode(CheckPasswordRespDto.MORE_ERROR_NUM);
            return resp;
        }

        if (storedUser.isFreeze()) {
            resp.setCode(CheckPasswordRespDto.ACCOUNT_FREEZE);
            return resp;
        }

        boolean equals = sysUserService.checkPasswordEquals(user.toSysUser(), storedUser);

        if (!equals) {
            resp.setCode(CheckPasswordRespDto.NOT_MATCH);
            //loginLogService.checkErrorNum(storedUser);//增加密码错误记录

            return resp;
        }

        loginLogService.getBaseMapper().deleteById(storedUser.getId());
        SysUser su = new SysUser();
        su.setId(storedUser.getId());
        su.setLastLoginTime(new Date());
        sysUserService.updateById(su);

        resp.setCode(CheckPasswordRespDto.OK);
        //密码不进行传输
        storedUser.setPassword(null);
        resp.setData(storedUser);
        return resp;
    }


    @SysLog("手机号查询用户")
    @PostMapping("/query/phone")
    public Rez<SysUser> queryByPhone(@RequestParam("phone") String phone) {
        SysUser sysUser = sysUserService.getBaseMapper().selectOne(
            Wrappers.query(SysUser.class).eq("phone", phone).eq("status", 1));
        return Rez.ofNullable(sysUser);
    }

    /**
     * 根据id集合查询用户信息
     *
     * @param idList
     * @return
     */

    @SysLog("根据id集合查询用户信息")
    @PostMapping("/querySysUserByIds")
    public ListResponse<SysUser> querySysUserByIds(@RequestBody List<Long> idList) {
        log.info("根据id集合查询用户信息,{}", idList);
        return sysUserService.querySysUserByIds(idList);
    }

    // @Override
    @ApiOperation(value = "查询系统用户列表", notes = "用于充电管理平台")
    @PostMapping("/findSysUserList")
    public ListResponse<SysUserVo> findSysUserList(
        @CurrentUser SysUser sysUser, @RequestBody ListSysUserParam param) {
        final QuerySysUserRequest request = new QuerySysUserRequest();
        BeanUtils.copyProperties(param, request);
        request.setPlatform(sysUser.getPlatform() != null ? sysUser.getPlatform()
            : AppClientType.MGM_WEB.getCode());

        if (null != param.getSameCorpWxAppNameUid()) {
            final List<Long> uids = sysUserService.sameCorpWxAppNameUserIds(
                param.getSameCorpWxAppNameUid());
            if (CollectionUtils.isEmpty(uids)) {
                return RestUtils.buildListResponse(List.of());
            }
            request.setUserIdlist(uids);
        }

        if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(param.getUidList())) {
            if (com.cdz360.base.utils.CollectionUtils.isEmpty(request.getUserIdlist())) {
                request.setUserIdlist(param.getUidList());
            } else {
                request.getUserIdlist().addAll(param.getUidList());
            }
        }
        return this.page(sysUser, request);
    }

    // @Override
    @ApiOperation(value = "获取当前用户相同团队标签的用户列表")
    @PostMapping("/sameCorpWxAppNameSysUser")
    public ListResponse<SysUserVo> sameCorpWxAppNameSysUser(
        @CurrentUser SysUser sysUser, @RequestBody ListSysUserParam param) {
        final QuerySysUserRequest request = new QuerySysUserRequest();
        BeanUtils.copyProperties(param, request);
        request.setStatus(1);
        request.setPlatform(AppClientType.MGM_WEB.getCode());

        if (null != param.getSameCorpWxAppNameUid()) {
            final List<Long> uids = sysUserService.sameCorpWxAppNameUserIds(
                param.getSameCorpWxAppNameUid());
            if (CollectionUtils.isEmpty(uids)) {
                return RestUtils.buildListResponse(List.of());
            }
            request.setUserIdlist(uids);
        }
        return sysUserService.sameCorpWxAppNameSysUser(sysUser, request);
    }

    /**
     * 用户列表
     *
     * @param querySysUserRequest
     * @return
     */
    // @Override
    @PostMapping("/page")
    public ListResponse<SysUserVo> page(
        @CurrentUser SysUser sysUser,
        @RequestBody QuerySysUserRequest querySysUserRequest) {

        if (sysUser == null) {
            return new ListResponse<>(
                ErrStatus.AUT_INVALID_TOKEN.getErrcode(), ErrStatus.AUT_INVALID_TOKEN.getErrmsg());
        }

        //企业管理平台处理
        log.info("sysUser = {}, req = {}", sysUser, JsonUtils.toJsonString(querySysUserRequest));
        if (sysUser.getPlatform() == AppClientType.CORP_WEB.getCode()) {
            querySysUserRequest.setUserId(sysUser.getId());
            querySysUserRequest.setPlatform(sysUser.getPlatform());
        }

        if (sysUser.getPlatform() == AppClientType.MGM_WEB.getCode()) {
            querySysUserRequest.setPlatform(sysUser.getPlatform());
        }

        /**
         * 存在审核组查询条件，要先查flowable中的人员信息
         */
        if (com.cdz360.base.utils.StringUtils.isNotEmpty(querySysUserRequest.getAuditGroupId())) {
            ListOaGroupUserParam groupParam = new ListOaGroupUserParam();
            groupParam.setGroupId(querySysUserRequest.getAuditGroupId());
            ListResponse<OaAccountDto> block = oaAccountFeignClient.groupUserList(groupParam)
                .block(Duration.ofSeconds(50L));
            if (block == null || com.cdz360.base.utils.CollectionUtils.isEmpty(block.getData())) {
                return RestUtils.buildListResponse(new ArrayList<>(), 0);
            }
            List<OaAccountDto> userList = block.getData();
            querySysUserRequest.setUserIdlist(userList.stream().map(OaAccountDto::getUid).collect(
                Collectors.toList()));
        }

        if (Boolean.TRUE.equals(querySysUserRequest.getQueryLimit())) {
            if (Boolean.TRUE.equals(sysUser.getLimitAcc())) { // 受限账号需要追加限制条件
                querySysUserRequest.setLimitCreateBy(sysUser.getId());
            }
        }

        ListResponse<SysUser> res = sysUserService.selectSysUser(querySysUserRequest);

        // 审核组信息  支撑平台也需要获取组信息    一个人同属多个组
        if (null == querySysUserRequest.getPlatform() ||
            querySysUserRequest.getPlatform() == AppClientType.MGM_WEB.getCode()) {
            ListOaGroupParam groupParam = new ListOaGroupParam();

            final List<String> collect = res.getData()
                .stream()
                .filter(x -> x.getPlatform() != null
                    && x.getPlatform() == AppClientType.MGM_WEB.getCode())
                .map(SysUser::getId)
                .map(Objects::toString)
                .collect(Collectors.toList());
            groupParam.setMemberIds(collect);
            final ListResponse<OaMemberGroupListDto> block = oaAccountFeignClient.memberGroupList(groupParam)
                .block(Duration.ofSeconds(50L));

            final Map<String, List<OaGroupDto>> memberGroupMap =
                com.cdz360.base.utils.CollectionUtils.isNotEmpty(block.getData()) ?
                    block.getData()
                        .stream()
                        .collect(Collectors.toMap(OaMemberGroupListDto::getGroupMemberUserId,
                            OaMemberGroupListDto::getGroupList, (o, n) -> n))
                    : new HashMap<>();

            final List<SysUserVo> result = res.getData().stream().map(x -> {
                SysUserVo vo = new SysUserVo();
                BeanUtils.copyProperties(x, vo);

                List<OaGroupDto> resGroupList = memberGroupMap.get(x.getId().toString());
                if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(resGroupList)) {
                    final List<String> groupIdList = resGroupList.stream().map(OaGroupDto::getId)
                        .collect(Collectors.toList());
                    vo.setAuditGroupIdList(groupIdList);
                    vo.setAuditGroupNameJoin(resGroupList.stream().map(OaGroupDto::getName)
                        .collect(Collectors.joining(",")));
                }

                return vo;
            }).collect(Collectors.toList());
            return RestUtils.buildListResponse(result, res.getTotal() != null ? res.getTotal() : 0);
        }

        if (CollectionUtils.isEmpty(res.getData())) {
            return RestUtils.buildListResponse(List.of(), 0);
        }

        //   工商储账号  获取关联站点
        if (sysUser.getPlatform() == AppClientType.COMM_ESS_MGM.getCode()) {
            res.getData().stream()
                .filter(x -> com.cdz360.base.utils.StringUtils.isNotEmpty(x.getGidStr()))
                .forEach(x -> {
                    List<String> gidList = Arrays.asList(x.getGidStr().split(","));
                    SiteGroupSiteParam siteGroupSiteParam = new SiteGroupSiteParam();
                    siteGroupSiteParam.setGidList(gidList);
                    ObjectResponse<Long> block = siteGroupSiteFeignClient.getSiteAmountByGidList(
                        siteGroupSiteParam).block(Duration.ofSeconds(50L));
                    if (block != null && block.getData() != null) {
                        x.setSiteAmount(block.getData());
                    }
                });
        }
        final List<SysUserVo> result = res.getData().stream().map(x -> {
            SysUserVo vo = new SysUserVo();
            BeanUtils.copyProperties(x, vo);
            return vo;
        }).collect(Collectors.toList());
        return RestUtils.buildListResponse(result, res.getTotal() != null ? res.getTotal() : 0);
    }


    @Operation(summary = "分配审核组")
    @PostMapping("/allocateGroup")
    public ObjectResponse<Integer> allocateSysUserGroup(
        @RequestBody OaModifyGroupParam param) {
        log.info("分配用户组: param = {}",
            JsonUtils.toJsonString(param));
        return sysUserService.allocateSysUserGroup(param);
    }

    @Operation(summary = "通过关键词获取审核组ID")
    @PostMapping("/groupIdByKeyWord")
    public ListResponse<Long> groupIdByKeyWord(
        @RequestParam("sk") String sk) {
        log.info("通过关键词获取审核组ID: sk = {}", sk);
        return RestUtils.buildListResponse(sysUserService.groupIdByKeyWord(sk));
    }

    @Operation(summary = "团队标签用户ID列表")
    @GetMapping("/teamCatalogUserIdList")
    public ListResponse<Long> teamCatalogUserIdList(
        @RequestParam("uid") Long uid,
        @RequestParam(value = "uname", required = false) String uname) {
        log.info("团队标签用户ID列表: uid = {}, uname = {}", uid, uname);
        return RestUtils.buildListResponse(sysUserService.teamCatalogUserIdList(uid, uname));
    }

    @Operation(summary = "多条件获取用户ID列表")
    @GetMapping("/searchUserIdList")
    public ObjectResponse<UserIdDto> searchUserIdList(
        @RequestParam("teamCatalogUid") Long teamCatalogUid,
        @RequestParam(value = "uname", required = false) String uname) {
        log.info("团队标签用户ID列表: teamCatalogUid = {}, uname = {}", teamCatalogUid, uname);
        return RestUtils.buildObjectResponse(
            sysUserService.searchUserIdList(teamCatalogUid, uname));
    }

    @Operation(summary = "两个用户的团队标签是否一致")
    @GetMapping("/sameTeamCatalog")
    public ObjectResponse<Boolean> sameTeamCatalog(
        @RequestParam("uid1") Long uid1,
        @RequestParam("uid2") Long uid2) {
        log.info("两个用户的团队标签是否一致: uid1 = {}, uid2 = {}", uid1, uid2);
        return RestUtils.buildObjectResponse(sysUserService.sameTeamCatalog(uid1, uid2));
    }

    @Operation(summary = "获取与指定用户的团队标签一致的用户ID列表")
    @GetMapping("/sameTeamCatalogUserIds")
    public ListResponse<Long> sameTeamCatalogUserIds(@RequestParam(value = "uid") Long uid) {
        log.info("获取与指定用户的团队标签一致的用户ID列表: uid = {}", uid);
        return RestUtils.buildListResponse(sysUserService.sameTeamCatalogUserIds(uid));
    }

    @SysLog("用户名查询用户")
    @Deprecated
    @GetMapping("/name/{username}")
    public SysUser findByUsername(@PathVariable String username) {
        log.info(username);
        SysUser entity = new SysUser();
        entity.setUsername(username);
        SysUser sysUser = sysUserDao.selectOne(Wrappers.query(entity));
        log.info("{}", sysUser);
        return sysUser;
    }

    /**
     * 按名称查询
     *
     * @param name          姓名
     * @param accurateQuery 是否精确查询
     * @return
     */
    @GetMapping("/findByName")
    public ListResponse<SysUser> findByName(@RequestParam(name = "name") String name,
        @RequestParam(name = "accurateQuery", defaultValue = "true")
        Boolean accurateQuery) {
        log.info("findByName name: {}, accurateQuery: {}", name, accurateQuery);
        return sysUserService.findByName(name, accurateQuery);
    }


    @PutMapping("{id}/pass/")
    public Rez resetPassword(@PathVariable Long id, @RequestBody ModifyPassDto dto) {
        SysUser sysUser = sysUserService.getBaseMapper().selectById(id);
        //验证旧密码::失败
        if (!sysUser.hashedPassword(dto.getOldPassword()).equals(sysUser.getPassword())) {
            return Rez.error(ErrStatus.AUT_WRONG_PASS);
        }
        //设置新密码
        sysUser.setPassword(sysUser.hashedPassword(dto.getNewPassword()));
        boolean update = sysUserService.updateById(sysUser);
        return Rez.hasModified(update);
    }

    @RequestMapping(value = "/mobile/pass", method = RequestMethod.POST)
    @ResponseBody

    public Rez resetPasswordThroughMobile(@RequestBody ModifyPassDto dto) {
        SysUser sysUser = sysUserService.getBaseMapper().selectOne(
            Wrappers.query(SysUser.class).eq("phone", dto.getPhone()).eq("status", 1));
        if (sysUser == null) {
            return Rez.error(ErrStatus.USER_NOT_EXIST);
        }
        //设置新密码
        sysUser.setPassword(sysUser.hashedPassword(dto.getNewPassword()));
        boolean update = sysUserService.updateById(sysUser);
        return Rez.hasModified(update);
    }


    @GetMapping("/{userId}/menuTree")
    public Rez<List<SysSystem>> sysList(@PathVariable Long userId,
        @RequestParam(required = false) Long sysId) {
        if (sysId == null) {
            List<SysSystem> systemList = sysUserService.querySystemMenuTree(userId, null, null);
            return Rez.data(systemList);
        } else {
            SysSystem system = sysUserService.querySystemMenuTree(userId, sysId);
            return Rez.data(Collections.singletonList(system));
        }
    }


    @GetMapping("/{userId}/menus/sys/{sysId}")
    public Rez<List<SysMenu>> menus(
        @PathVariable("userId") Long userId
        , @PathVariable("sysId") Long sysId) {
        List<SysMenu> menuList = sysUserService.queryMenuTree(userId, sysId, null);
        if (CollectionUtils.isEmpty(menuList)) {
            return Rez.error(ErrStatus.ACCESS_FORBIDDEN);
        }
        return Rez.data(menuList);
    }


    @GetMapping("/{userId}/menus/app/{appId}")
    public Rez<List<SysMenu>> menusByAppId(@PathVariable("userId") Long userId,
        @PathVariable("appId") String appId) {
        SysSystem app = systemService.getBaseMapper()
            .selectOne(Wrappers.query(SysSystem.class).eq("appkey", appId));
        if (app == null) {
            return Rez.error(ErrStatus.RES_NOT_FOUND);
        }
        Long sysId = app.getId();

        List<SysMenu> list = sysUserService.queryMenuTree(userId, sysId, null);

        return Rez.data(list);
    }

    /**
     * 手机号是否可以登录应用
     */

    @PostMapping("/sys/valid")
    public Rez<Void> validPhoneUsernameEmail(@RequestBody ValidPhoneSysDto dto) {
        log.debug(" dto = {}", dto);
        String phone = dto.getPhone();
        SysUser sysUser = sysUserService.getBaseMapper().selectOne(Wrappers.query(SysUser.class)
            .eq("platform", dto.getPlatform())

//            .andNew() // TODO, 这里的逻辑要确认下。 xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
            //.andNew("(phone = ? or username = ?)", phone, phone));
            .eq("phone", phone)
            .or().eq("username", phone));
        if (sysUser == null) {
            return Rez.error(ErrStatus.BAD_MOBILE);
        }
        if (sysUser.isFreeze()) {
            return Rez.error(ErrStatus.USER_FREZZE);
        }
        Long count = sysUserService.countMenuByUserIdAndSysId(sysUser.getId(), dto.getSysId());
        if (count > 0) {
            return Rez.ok();
        }
        return Rez.error(ErrStatus.ACCESS_FORBIDDEN);
    }

    /**
     * 手机号是否可以登录应用
     */

    @PostMapping("/sys/valid/phone")
    public Rez<Void> valid(@RequestBody @Valid ValidPhoneSysDto dto) {
        String phone = dto.getPhone();

        SysUser sysUser = sysUserService.getBaseMapper()
            .selectOne(Wrappers.query(SysUser.class).eq("phone", phone));
        if (sysUser == null) {
            return Rez.error(ErrStatus.BAD_MOBILE);
        }
        if (sysUser.isFreeze()) {
            return Rez.error(ErrStatus.USER_FREZZE);
        }
        Long count = sysUserService.countMenuByUserIdAndSysId(sysUser.getId(), dto.getSysId());
        if (count > 0) {
            return Rez.ok();
        }
        return Rez.error(ErrStatus.ACCESS_FORBIDDEN);
    }

    /**
     * 根据请求url和用户id查询菜单的用户权限
     *
     * @param url
     * @param userId
     * @param sysId
     * @return
     */

    @GetMapping("/authValid")
    public boolean authValid(@RequestParam("url") String url,
        @RequestParam(value = "userId", required = false) Long userId,
        @RequestParam(value = "sysId", required = false) Long sysId) {

        return sysUserService.authValid(url, userId, sysId);
    }

    /**
     * 资金周转-用户信息
     *
     * @param sysUserId
     * @return
     */

    @GetMapping("/operatorlistByCode")
    public ListResponse<UserVo> operatorlistByCode(Long sysUserId) {
        List<UserVo> list = null;
        if (sysUserId != null && sysUserId > 0) {
            SysUser sysUser = sysUserService.getBaseMapper().selectById(sysUserId);
            if (sysUser != null) {
                list = Arrays.asList(this.convertToUserVo(sysUser));
            }
        } else {
            List<SysUser> sysUserList = sysUserService.getBaseMapper().selectList(null);
            if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(sysUserList)) {
                list = sysUserList.stream().map(tc -> this.convertToUserVo(tc))
                    .collect(Collectors.toList());
            }
        }
        ListResponse<UserVo> res = new ListResponse<>(list,
            list != null ? Long.valueOf(list.size()) : 0);
        return res;
    }

    public UserVo convertToUserVo(SysUser sysUser) {
        SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 顶级商户
        SysUserComercial sysUserComercial = this.tCommercialService.getUserCommercials(
            sysUser.getId());
        return new UserVo()
            .setCode(sysUser.getId())
            .setLoginname(sysUser.getUsername())
            .setDisplayname(sysUser.getName())
            .setBirthday(sysUser.getBirthday() != null ? sdf1.format(sysUser.getBirthday()) : null)
            .setGender(sysUser.getSex() != null ? sysUser.getSex() : 3)
            .setEmail(sysUser.getEmail())
            .setContactPhoneNum(sysUser.getPhone())
            .setStatus(sysUser.getStatus())
            .setIdcard(sysUser.getCredentials() != null ? sysUser.getCredentials() : null)
            .setLastLoginTime(
                sysUser.getLastLoginTime() != null ? sdf2.format(sysUser.getLastLoginTime()) : null)
            .setCreateTime(
                sysUser.getCreateTime() != null ? sdf2.format(sysUser.getCreateTime()) : null)
            .setUpdateTime(
                sysUser.getUpdateTime() != null ? sdf2.format(sysUser.getUpdateTime()) : null)
            .setProviderId(sysUserComercial != null && sysUserComercial.getCommercialId() != null
                ? sysUserComercial.getCommercialId() : null);
    }

//    @GetMapping("/archs/{userId}")
//
//    public Rez getOrgByUser(@PathVariable("userId") Long userId) {
//        SysArchInfo sa = infoService.selectByUserId(userId);
//        return Rez.data(sa);
//    }

    /**
     * 获取用户所在组织以及下级组织的账户成员
     *
     * @param userId
     * @return
     */
    @GetMapping("/getUserByOrg")
    public ListResponse<Long> getUserByOrg(@RequestParam("userId") Long userId) {
        return new ListResponse<>(sysUserRwDs.getUserByOrg(userId));
    }

    /**
     * 角色绑定的账号信息
     *
     * @param params
     * @return
     */
    @PostMapping("/getUserListByRoleId")
    public ListResponse<RoleUserVo> getUserListByRoleId(@RequestBody RoleUserListParam params) {
        log.info("角色绑定的管理平台账号,params={}", JsonUtils.toJsonString(params));
        return sysUserService.getUserListByRoleId(params);
    }

    /**
     * 获取尚未绑定该角色账号信息
     *
     * @param keyWord
     * @param platform
     * @param roleId
     * @return
     */
    @GetMapping("/getUserByRoleId")
    public ListResponse<RoleUserVo> getUserByRoleId(
        @RequestParam(value = "keyWord", required = false) String keyWord,
        @RequestParam("platform") Long platform,
        @RequestParam("roleId") Long roleId,
        @RequestParam(value = "size", required = false) Long size) {
        log.info("获取尚未绑定该角色账号信息:keyWord={},platform={},roleId={},size={}", keyWord,
            platform,
            roleId, size);
        return sysUserService.getUserByRoleId(keyWord, platform, roleId, size);
    }

    /**
     * 批量新增账号
     *
     * @param params
     * @return
     */
    @PostMapping("/batchAddRoleUser")
    public BaseResponse batchAddRoleUser(@RequestBody BatchAddRoleUserParam params) {
        log.info("批量新增账号，params={}", JsonUtils.toJsonString(params));
        return sysUserService.batchAddRoleUser(params);
    }

    @Operation(summary = "批量修改角色关联的账户")
    @PostMapping("/batchUpdateRoleUser")
    public BaseResponse batchUpdateRoleUser(@RequestBody RoleUserUpdateParam params) {
        log.info("批量修改角色关联的账户,params={}", JsonUtils.toJsonString(params));
        return sysUserService.batchUpdateRoleUser(params);
    }

    @Operation(summary = "sys_user相关检查")
    @PostMapping("/checkInDB")
    public ListResponse<SysUserCheckParam> checkInDB(@RequestBody List<SysUserCheckParam> list) {
        log.info("checkInDB list.size = {}", list.size());
        return RestUtils.buildListResponse(sysUserService.checkInDB(list));
    }

    @Operation(summary = "简易获取用户列表信息")
    @PostMapping("/getUserSimpleList")
    public ListResponse<SysUser> getUserSimpleList(@RequestBody QuerySysUserRequest param) {
        log.info("简易获取用户列表信息  param = {}", JsonUtils.toJsonString(param));
        return RestUtils.buildListResponse(sysUserService.getUserSimpleList(param));
    }


}
