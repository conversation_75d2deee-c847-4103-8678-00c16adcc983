package com.cdz360.biz.auth.rest;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.auth.config.SysLog;
import com.cdz360.biz.auth.model.dto.PageDto;
import com.cdz360.biz.auth.model.vo.Rez;
import com.cdz360.biz.auth.model.vo.SysSystem;
import com.cdz360.biz.auth.service.SysSystemService;
import com.cdz360.biz.auth.utils.LikeSqlEscaper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/data/systems")
public class SysSystemController {

    @Autowired
    SysSystemService systemService;

    @SysLog("子系统查询-id")
    @GetMapping("/{id}")
    public Rez<SysSystem> findById(@PathVariable("id") Long id) {
        SysSystem sysDept = systemService.getBaseMapper().selectById(id);
        return Rez.ofNullable(sysDept);
    }

    @GetMapping({"/search", "/page"})
    @SysLog("子系统分页查询搜索")
    public ListResponse<SysSystem> searchByName(//@RequestParam(required = false, defaultValue = "") String name,
        PageDto pageDto) {
        String name = pageDto.getKeyword(); // RequestParam 写法有未通过，调整参数名称
        log.debug("name = {}", name);
//        String name = null;
        QueryWrapper<SysSystem> wrapper = Wrappers.query();
        wrapper.orderBy(true, false, "create_time");
        if (!StringUtils.isEmpty(name)) {
            wrapper.like("name", LikeSqlEscaper.escapeLike(name));
        }
        Page<SysSystem> page = systemService.getBaseMapper()
            .selectPage(new Page<>(pageDto.getPage(), pageDto.getSize()),
                wrapper);
        ListResponse<SysSystem> res = new ListResponse<SysSystem>(page.getRecords(),
            page.getTotal());
        return res;
        //return Rez.data(page);
    }

    @SysLog("子系统添加")
    @PostMapping
    public Rez<SysSystem> add(@RequestBody SysSystem dept) {
        QueryWrapper<SysSystem> wrapper = Wrappers.query();
        wrapper.eq("name", dept.getName());
        Long count = systemService.getBaseMapper().selectCount(wrapper);
        if (count != null && count > 0L) {
            return Rez.error(1, "名称重复");
        }
        wrapper = Wrappers.query();
        wrapper.eq("url", dept.getUrl());
        count = systemService.getBaseMapper().selectCount(wrapper);

        if (count != null && count > 0) {
            return Rez.error(2, "url重复");
        }
        int insert = systemService.getBaseMapper().insert(dept);
        return Rez.hasModified(insert > 0, dept);
    }

    @SysLog("子系统修改")
    @PutMapping("/{id}")
    public Rez modify(@PathVariable("id") Long id, @RequestBody SysSystem bodyEntity) {
        bodyEntity.setId(id);
        QueryWrapper<SysSystem> wrapper = Wrappers.query();
        wrapper.eq("name", bodyEntity.getName()).ne("id", id);
        Long count = systemService.getBaseMapper().selectCount(wrapper);
        if (count != null && count > 0) {
            return Rez.error(1, "名称重复");
        }
        wrapper = Wrappers.query();
        wrapper.eq("url", bodyEntity.getUrl()).ne("id", id);
        count = systemService.getBaseMapper().selectCount(wrapper);

        if (count != null && count > 0) {
            return Rez.error(2, "url重复");
        }
        boolean update = systemService.updateById(bodyEntity);
        SysSystem sysSystem = systemService.getBaseMapper().selectById(id);
        return Rez.hasModified(update, sysSystem);
    }

    @SysLog("子系统删除")
    @DeleteMapping("/{id}")
    public Rez delete(@PathVariable("id") Long id) {
        int delDept = systemService.getBaseMapper().deleteById(id);
        return Rez.hasModified(delDept > 0);
    }
}
