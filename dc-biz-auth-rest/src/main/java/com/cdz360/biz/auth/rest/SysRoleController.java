package com.cdz360.biz.auth.rest;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.config.CurrentUser;
import com.cdz360.biz.auth.config.SysLog;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.auth.ds.ro.sys.SysRoleRoDs;
import com.cdz360.biz.auth.ds.ro.user.SysUserRoDs;
import com.cdz360.biz.auth.model.dto.PageDto;
import com.cdz360.biz.auth.model.dto.RoleCategoryVo;
import com.cdz360.biz.auth.model.param.BatchUpdateRoleRequest;
import com.cdz360.biz.auth.model.type.ErrStatus;
import com.cdz360.biz.auth.model.vo.Rez;
import com.cdz360.biz.auth.model.vo.SysDict;
import com.cdz360.biz.auth.model.vo.SysRole;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.service.SysDictService;
import com.cdz360.biz.auth.service.SysRoleService;
import com.cdz360.biz.auth.sys.param.BatchAddRoleUserParam;
import com.cdz360.biz.auth.sys.param.ListSysRoleParam;
import com.cdz360.biz.auth.sys.param.RoleUserListParam;
import com.cdz360.biz.auth.sys.param.RoleUserUpdateParam;
import com.cdz360.biz.auth.user.po.SysUserPo;
import com.cdz360.biz.auth.utils.IotAssert;
import com.cdz360.biz.auth.utils.LikeSqlEscaper;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.event.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/data/roles")
public class SysRoleController //implements SysRoleInterface
{

    public static final String ROLE_CATEGORY = "role_category";


    @Autowired
    SysRoleService roleService;
    @Autowired
    SysDictService dictService;
    @Autowired
    SysUserRoDs sysUserRoDs;
    @Autowired
    SysRoleRoDs sysRoleRoDs;

    @Operation(summary = "获取系统角色列表")
    @PostMapping("/findSysRole")
    public ListResponse<SysRole> findSysRole(
        @CurrentUser SysUser sysUser,
        @RequestBody ListSysRoleParam param) {
        log.info("获取系统角色列表: {}", param);
        PageDto pageDto = new PageDto();

        if (null == param.getStart()) {
            param.setStart(0L);
        }

        if (null == param.getSize() || param.getSize() == 0) {
            param.setSize(999);
        }

        pageDto.setPage((int) (param.getStart() / param.getSize()) + 1);
        pageDto.setSize(param.getSize());

        if (null != param.getPlatform()) {
            pageDto.setPlatform(param.getPlatform());
        }

        if (com.cdz360.base.utils.StringUtils.isNotBlank(param.getRoleNameLike())) {
            pageDto.setKeyword(param.getRoleNameLike());
        }

        pageDto.setQueryLimit(param.getQueryLimit());

        return this.page(sysUser, pageDto, null);
    }

    //@Override
    @SysLog("角色分页")
    @GetMapping("/page")
    public ListResponse<SysRole> page(
        @CurrentUser SysUser sysUser, PageDto pageDto, String category) {
        QueryWrapper<SysRole> wrapper = Wrappers.query();
//        Condition condition = Condition.create();
        if (!StringUtils.isEmpty(category)) {
            wrapper.eq("category", category);
        }
        if (!StringUtils.isEmpty(pageDto.getKeyword())) {
            wrapper.like("name", LikeSqlEscaper.escapeLike(pageDto.getKeyword()));
        }

        if (pageDto.getStatus() > 0) {
            wrapper.eq("status", pageDto.getStatus());
        }
        if (pageDto.getPlatform() != null) {
            wrapper.eq("platform", pageDto.getPlatform());
        }

        if (sysUser.getPlatform() == null) {
            log.warn("sysUser = {}", sysUser);
            throw new DcArgumentException("所属平台不能位空", Level.ERROR);
        } else if (sysUser.getPlatform() == AppClientType.SASS_MGM_WEB.getCode()) {
            wrapper.in("platform",
                List.of(AppClientType.SASS_MGM_WEB.getCode(),
                    AppClientType.MGM_WEB.getCode(),
                    AppClientType.ESS_MGM.getCode(),
                    AppClientType.MGM_FOREIGN_WEB.getCode(),
                    AppClientType.COMM_ESS_MGM.getCode()));
        } else if (sysUser.getPlatform() == AppClientType.COMM_ESS_MGM.getCode()) { // 工商业储能平台
            if (pageDto.getPlatform() == null) {
                wrapper.in("platform",
                    List.of(Long.valueOf(AppClientType.COMM_ESS_MGM.getCode()),
                        Long.valueOf(AppClientType.COMM_ESS_APP.getCode())));
            } else {
                wrapper.eq("platform",
                    pageDto.getPlatform() != null ? pageDto.getPlatform() : sysUser.getPlatform());
            }
        } else if (sysUser.getPlatform() == AppClientType.MGM_WEB.getCode()) { // 管理平台
            wrapper.in("create_by", sysUser.getId());
        } else {         //企业管理平台
            wrapper.eq("platform", sysUser.getPlatform());
            wrapper.in("create_by", sysUser.getId());
        }

        if (Boolean.TRUE.equals(pageDto.getQueryLimit())) {
            if (Boolean.TRUE.equals(sysUser.getLimitAcc())) {
                wrapper.in("create_by", sysUser.getId());
            }
        }

        wrapper.orderBy(true, false, "create_time");
        Page<SysRole> sysRolePage = roleService.getBaseMapper().selectPage(
            new Page<>(pageDto.getPage(), pageDto.getSize()), wrapper);

        List<SysDict> sysDicts = dictService.queryByPcode(ROLE_CATEGORY);
        sysRolePage.getRecords().forEach(role -> {
            if (role.getCategory() != null) {
                Optional<@NotNull String> categoryName = sysDicts.stream().filter(
                        sysDict -> Objects.equals(Integer.parseInt(sysDict.getCode()),
                            role.getCategory()))
                    .findFirst().map(SysDict::getName);

                role.setCategoryName(categoryName.orElse(null));
            } else {
                role.setCategoryName("未分类");
            }

            //获取创建人信息
            if (NumberUtils.equals(role.getCreateBy(), 0L)) {
                role.setCreateUser("系统");
            } else {
                SysUserPo userInfo = sysUserRoDs.getByUseId(role.getCreateBy());
                if (userInfo != null) {
                    role.setCreateUser(userInfo.getUsername());
                }
            }
        });
        ListResponse<SysRole> res = new ListResponse<>(sysRolePage.getRecords(),
            sysRolePage.getTotal());
        return res;
        //return Rez.data(sysRolePage);
    }

    //@Override
    @SysLog("角色查询-id")
    @GetMapping("{id}")
    public Rez<SysRole> findById(@PathVariable Long id) {
        SysRole sysRole = roleService.findRoleByIdWithMenus(id);
        if (sysRole.getCategory() != null) {
            dictService.queryByPcodeAndCode(sysRole.getCategory().toString(), ROLE_CATEGORY);
        }
        return Rez.ofNullable(sysRole);
    }

    @GetMapping("/grouped")
    @SysLog("角色分组查询")
    public Rez grouped(@CurrentUser SysUser sysUser) {
        List<RoleCategoryVo> roleCategoryVos = roleService.groupByKind(sysUser);
        return Rez.data(roleCategoryVos);

    }

    @GetMapping("/kinds")
    @SysLog("所有角色分类")
    public Rez roleCategors() {
        List<SysDict> sysDicts = dictService.queryByPcode(ROLE_CATEGORY);
        QueryWrapper<SysRole> wrapper = Wrappers.query();
//        Condition condition = new Condition();

        wrapper.select("category", "platform")
            .in("category", sysDicts.stream().map(SysDict::getCode).collect(Collectors.toList()))
            .groupBy("category, platform");
        List<SysRole> roles = roleService.getBaseMapper().selectList(wrapper);
        Map<Integer, Integer> integerMap = roles.stream()
            .collect(Collectors.toMap(SysRole::getCategory, SysRole::getPlatform));
        sysDicts.forEach(e -> {
            try {
                Integer platform = integerMap.get(Integer.valueOf(e.getCode()));
                if (platform != null) {
                    e.setPlatform(platform);
                }
            } catch (Exception ex) {
                log.error(
                    "数据字典错误 auth_center.sys_dict表 id={} 该条记录code字段应为数字,需人工修正",
                    e.getId(), ex);
            }
        });
        return Rez.data(sysDicts);
    }

    @PutMapping("/kinds/{code}")
    @SysLog("角色分类-修改")
    @Deprecated
    public Rez updateRoleCategory(@PathVariable String code,
        @Valid @RequestBody SysDict sysDict) {
//        log.error("deprecated");
//        return Rez.error(ErrStatus.REST_ERROR);
        if (sysDict.getCode() != null && !Objects.equals(code, sysDict.getCode())) {
            return Rez.error(ErrStatus.REQ_CONFLICT);
        }
        sysDict.setPcode(ROLE_CATEGORY);
        sysDict.setCode(code);

        Optional<ErrStatus> dictByPcode = dictService.upsertDictByPcode(sysDict);
        if (dictByPcode.isPresent()) {
            return Rez.error(dictByPcode.get());
        }
        return Rez.data(dictService.queryByPcode(ROLE_CATEGORY));
    }

    @PostMapping("/kinds")
    public Rez addRoleCategory(@Valid @RequestBody SysDict sysDict) {
        sysDict.setPcode(ROLE_CATEGORY);
        List<SysDict> all = dictService.queryByPcode(ROLE_CATEGORY);

        boolean nameNotAvailable = all.stream()
            .anyMatch(d -> d.getName().equals(sysDict.getName()));
        if (nameNotAvailable) {
            return Rez.error(ErrStatus.CATEGORY_NAME_CONFLICT);
        }

        Optional<Integer> max = all.stream().map(SysDict::getCode).map(Integer::parseInt)
            .max(Integer::compareTo);
        sysDict.setCode(String.valueOf(max.orElse(0) + 1));
        SysDict parent = dictService.getBaseMapper().selectOne(Wrappers.query(SysDict.class).eq("code", ROLE_CATEGORY));
        sysDict.setPid(parent.getId());

        int insert = dictService.getBaseMapper().insert(sysDict);
        return Rez.hasModified(insert > 0, sysDict);
    }

    @DeleteMapping("/kinds/{id}")
    @SysLog("角色分类-删除")
    public Rez deleteCategory(@PathVariable Long id) {
        int b = 0;
        SysDict dict = dictService.getBaseMapper().selectById(id);
        if (dict != null) {
            long count = roleService.getBaseMapper().selectCount(Wrappers.query(SysRole.class).eq("category", dict.getCode()));
            if (count > 0L) {
                return Rez.error(ErrStatus.FREEZE_ERROR);
            }
            b = dictService.getBaseMapper().deleteById(id);
        }
        return Rez.hasModified(b > 0);
    }

    //@Override
    @SysLog("角色添加")
    @PostMapping()
    public ObjectResponse add(@CurrentUser SysUser sysUser, @RequestBody SysRole role) {
        if (sysUser != null) {
            role.setCreateBy(sysUser.getId());
        }
        if (role.getIsAddNewCategory() != null && role.getIsAddNewCategory()) {
            IotAssert.isNotBlank(role.getCategoryName(), "新类别名称不能为空");
            SysDict dict = new SysDict();
            dict.setName(role.getCategoryName());
            Rez rez = this.addRoleCategory(dict);
            if (rez.getStatus() != 0) {
                throw new DcServiceException(rez.getStatus(), rez.getError());
            }
            role.setCategory(Integer.valueOf(dict.getCode()));
        }

        if (sysUser.getPlatform() == AppClientType.CORP_WEB.getCode()) {
            role.setCreateBy(sysUser.getId());
            CorpPo corpInfo = roleService.getCorpByUserId(sysUser.getId());
            role.setCorpId(corpInfo.getId());
            role.setCommId(corpInfo.getCommId());
            role.setTopCommId(corpInfo.getTopCommId());
        }
        boolean insert = roleService.insertWithMenus(role);
//        SysRole byIdWithMenus = roleService.findRoleByIdWithMenus(role.getId());
        return insert ? RestUtils.buildObjectResponse(role.getId())
            : RestUtils.serverBusy4ObjectResponse();
    }

    //@Override
    @SysLog("角色修改")
    @PutMapping("{id}")
    public BaseResponse modify(@CurrentUser SysUser sysUser, @PathVariable Long id,
        @RequestBody SysRole bodyEntity) {

        if (bodyEntity.getIsAddNewCategory() != null && bodyEntity.getIsAddNewCategory()) {
            IotAssert.isNotBlank(bodyEntity.getCategoryName(), "新类别名称不能为空");
            SysDict dict = new SysDict();
            dict.setName(bodyEntity.getCategoryName());
            Rez rez = this.addRoleCategory(dict);
            if (rez.getStatus() != 0) {
                throw new DcServiceException(rez.getStatus(), rez.getError());
            }
            bodyEntity.setCategory(Integer.valueOf(dict.getCode()));
        }

        bodyEntity.setId(id);
        //企业管理平台处理
        if (sysUser.getPlatform() == AppClientType.CORP_WEB.getCode()) {
            bodyEntity.setPlatform(sysUser.getPlatform());
            bodyEntity.setCorpId(sysUser.getCorpId());
        }
        boolean update = roleService.updateWithMenu(bodyEntity);
//        return Rez.hasModified(update, roleService.findRoleByIdWithMenus(id));
        return update ? RestUtils.success() : RestUtils.serverBusy();
    }

    //@Override
    @SysLog("角色删除")
    @Deprecated
    @DeleteMapping("{id}")
    public Rez delete(@PathVariable Long id) {
//        log.error("deprecated !!!");
//        return Rez.error(ErrStatus.REST_ERROR);
        boolean delete = roleService.deleteByIdWithMenus(id);
        return Rez.hasModified(delete);
    }

    //@Override
    @GetMapping("/user/{userId}")
    public List<SysRole> findByUserId(@PathVariable Long userId) {
        return roleService.findByUserId(userId);
    }

    /**
     * 获取当前登录用户创建的角色
     *
     * @param userId
     * @return
     */
    @GetMapping("/getRoleByUserId/{userId}")
    public List<SysRole> getRoleByUserId(@PathVariable Long userId) {
        return roleService.getRoleByUserId(userId);
    }

    //@Override
    @GetMapping("/names/user/{userId}")
    public Set<String> roleNameSetByUserId(@PathVariable Long userId) {
        List<SysRole> roles = roleService.findByUserId(userId);
        Set<String> collect = roles.stream().map(SysRole::getName).collect(Collectors.toSet());
        return collect;
    }

    @Operation(summary = "角色批量修改权限")
    @PostMapping("/batchUpdate")
    public BaseResponse batchUpdate(@CurrentUser SysUser sysUser,
        @RequestBody BatchUpdateRoleRequest params) {
        if (null == sysUser) {
            throw new DcArgumentException("未登录状态");
        }
        params.setUpdateBy(sysUser.getId());
        return roleService.batchUpdate(params);
    }

    @Operation(summary = "获取角色关联的menu")
    @GetMapping("/getMenuList")
    public ListResponse<Long> getMenuList(@RequestParam("roleId") Long roleId) {
        if (null == roleId) {
            throw new DcArgumentException("角色ID不能为空");
        }
        return new ListResponse<>(roleService.getMenuList(roleId));
    }

    @Operation(summary = "获取角色列表")
    @GetMapping("/getRoleList")
    public ListResponse<SysRole> getRoleList(@CurrentUser SysUser sysUser,
        @RequestParam("platform") Long platform) {
        IotAssert.isNotNull(sysUser, "未登录状态");
        IotAssert.isNotNull(platform, "角色ID不能为空");
        return new ListResponse<>(roleService.getRoleList(platform));
    }

    @Operation(summary = "获取账号绑定的角色")
    @PostMapping("/getRoleListByUserId")
    public ListResponse<SysRole> getRoleListByUserId(@RequestBody RoleUserListParam params) {
        log.info("获取账号绑定的角色,params={}", JsonUtils.toJsonString(params));
        return roleService.getRoleListByUserId(params);
    }

    @Operation(summary = "账号尚未绑定的角色列表")
    @GetMapping("/getRoleByUserId")
    public ListResponse<SysRole> getRoleByUserId(
        @RequestParam(value = "keyWord", required = false) String keyWord,
        @RequestParam("platform") Long platform,
        @RequestParam("userId") Long userId,
        @RequestParam(value = "size", required = false) Long size) {
        log.info("账号尚未绑定的角色列表:keyWord={},platform={},userId={},size={}", keyWord,
            platform, userId, size);
        return roleService.getRoleByUserId(keyWord, platform, userId, size);
    }

    @Operation(summary = "批量修改用户关联的角色")
    @PostMapping("/batchUpdateRoleUserByUserId")
    public BaseResponse batchUpdateRoleUserByUserId(@RequestBody RoleUserUpdateParam params) {
        log.info("批量修改用户关联的角色,params={}", JsonUtils.toJsonString(params));
        return roleService.batchUpdateRoleUserByUserId(params);
    }

    @Operation(summary = "用户批量新增角色")
    @PostMapping("/batchAddRoleUserByUserId")
    public BaseResponse batchAddRoleUserByUserId(@RequestBody BatchAddRoleUserParam params) {
        log.info("用户批量新增角色，params={}", JsonUtils.toJsonString(params));
        return roleService.batchAddRoleUserByUserId(params);
    }


    @Operation(summary = "获取国充场站的gcType值")
    @GetMapping("/getGcTypes")
    public ListResponse<Integer> getGcTypes() {
        log.info("获取国充场站的gcType值");
        var types = dictService.getGcSiteTypes();
        return RestUtils.buildListResponse(types);
    }
}
