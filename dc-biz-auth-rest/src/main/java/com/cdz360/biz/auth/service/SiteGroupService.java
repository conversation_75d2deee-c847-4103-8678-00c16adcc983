package com.cdz360.biz.auth.service;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServerException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.auth.ds.ro.user.SiteGroupRoDs;
import com.cdz360.biz.auth.ds.ro.user.SiteGroupUserRoDs;
import com.cdz360.biz.auth.ds.ro.user.SysUserRoDs;
import com.cdz360.biz.auth.ds.rw.user.SiteGroupRwDs;
import com.cdz360.biz.auth.ds.rw.user.SiteGroupUserRwDs;
import com.cdz360.biz.auth.feign.DataCoreFeignClient;
import com.cdz360.biz.auth.feign.reactor.SiteGroupSiteFeignClient;
import com.cdz360.biz.auth.sys.vo.AccRelativeVo;
import com.cdz360.biz.auth.utils.IotAssert;
import com.cdz360.biz.model.sys.constant.SiteGroupOwnType;
import com.cdz360.biz.model.sys.constant.SiteGroupType;
import com.cdz360.biz.model.sys.dto.SiteGroupDto;
import com.cdz360.biz.model.sys.dto.UserOwnerSiteGroupDto;
import com.cdz360.biz.model.sys.param.ListSiteGroupParam;
import com.cdz360.biz.model.sys.param.SiteGroupSiteParam;
import com.cdz360.biz.model.sys.param.UpdateSiteGroupSiteParam;
import com.cdz360.biz.model.sys.po.SiteGroupPo;
import com.cdz360.biz.model.sys.vo.SiteGroupSiteInfoVo;
import com.cdz360.biz.model.sys.vo.SiteGroupVo;
import com.cdz360.biz.model.sys.vo.UserGroupVo;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class SiteGroupService {

    @Autowired
    private SiteGroupRoDs siteGroupRoDs;

    @Autowired
    private SiteGroupRwDs siteGroupRwDs;

    @Autowired
    private SiteGroupUserRwDs siteGroupUserRwDs;

    @Autowired
    private SiteGroupUserRoDs siteGroupUserRoDs;

    @Autowired
    private SysUserRoDs sysUserRoDs;

    @Autowired
    private AccRelativeService accRelativeService;

    @Autowired
    private SiteGroupSiteFeignClient siteGroupSiteFeignClient;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    public ListResponse<SiteGroupVo> findSiteGroup(ListSiteGroupParam param) {
        Mono<ListSiteGroupParam> mono = Mono.just(param);
        if (CollectionUtils.isNotEmpty(param.getSiteIdList())) {
            mono = siteGroupSiteFeignClient.findSiteGroupSiteBySiteId(
                    new SiteGroupSiteParam()
                        .setGidList(param.getGidList())
                        .setSiteIdList(param.getSiteIdList())
                ).doOnNext(x -> IotAssert.isTrue(
                    x.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS, "获取场站组ID失败"))
                .map(res -> {
                    if (CollectionUtils.isNotEmpty(param.getGidList())) {
                        return param.setGidList(
                            Stream.concat(param.getGidList().stream(), res.getData().stream())
                                .distinct()
                                .collect(Collectors.toList()));
                    } else {
                        return param.setGidList(res.getData());
                    }
                });
        }
        return mono.flatMap(x -> {
                // 以上已通过场站条件去获取组信息，如果没获取到则返回空
                if (CollectionUtils.isNotEmpty(x.getSiteIdList()) && CollectionUtils.isEmpty(
                    x.getGidList())) {
                    return Mono.just(RestUtils.buildListResponse(new ArrayList<SiteGroupVo>()));
                } else {
                    return siteGroupRoDs.findSiteGroup(x);
                }
            })
            .flatMap(res -> {
                // 获取场站组和场站关系
                List<String> gids = res.getData().stream().map(SiteGroupPo::getGid)
                    .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(gids)) {
                    return Mono.just(res);
                }
                return siteGroupSiteFeignClient.findSiteGroupSiteInfo(
                        new SiteGroupSiteParam().setGidList(gids))
                    .doOnNext(x -> {
                        if (x.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
                            log.warn("res = {}", JsonUtils.toJsonString(x));
                            if (x.getStatus() == DcConstants.KEY_RES_CODE_BALANCE_ERROR) {
                                throw new DcServiceException(x.getStatus(), x.getError());
                            }
                            throw new DcServiceException(x.getStatus(), x.getError());
                        } else if (x.getData() == null) {
                            log.error("no data.... {}", JsonUtils.toJsonString(x));
                            throw new DcServerException("数据为空");
                        }
                    })
                    .map(ListResponse::getData)
                    .map(list -> {
                        Map<String, SiteGroupSiteInfoVo> gidMap = list.stream()
                            .collect(Collectors.toMap(SiteGroupSiteInfoVo::getGid, o -> o));
                        res.getData().forEach(data -> {
                            if (gidMap.containsKey(data.getGid())) {
                                SiteGroupSiteInfoVo target = gidMap.get(data.getGid());
                                if (null != target) {
                                    data.setSelfSiteIdList(target.getSelfSiteIdList())
                                        .setOfflineSiteIdList(target.getOfflineSiteIdList());
                                }
                            }
                        });
                        return res;
                    });
            }).block(Duration.ofSeconds(50L));
    }

    public Mono<SiteGroupVo> getSiteGroupInfo(String gid) {
        IotAssert.isNotBlank(gid, "场站组ID无效");
        return siteGroupRoDs.getSiteGroupInfo(gid)
            .flatMap(res -> siteGroupSiteFeignClient.findSiteGroupSiteInfo(
                    new SiteGroupSiteParam().setGidList(List.of(gid)))
                .doOnNext(x -> {
                    if (x.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
                        log.warn("res = {}", JsonUtils.toJsonString(x));
                        if (x.getStatus() == DcConstants.KEY_RES_CODE_BALANCE_ERROR) {
                            throw new DcServiceException(x.getStatus(), x.getError());
                        }
                        throw new DcServiceException(x.getStatus(), x.getError());
                    } else if (x.getData() == null) {
                        log.error("no data.... {}", JsonUtils.toJsonString(x));
                        throw new DcServerException("数据为空");
                    }
                })
                .map(ListResponse::getData)
                .map(list -> {
                    Map<String, SiteGroupSiteInfoVo> gidMap = list.stream()
                        .collect(Collectors.toMap(SiteGroupSiteInfoVo::getGid, o -> o));
                    if (gidMap.containsKey(res.getGid())) {
                        SiteGroupSiteInfoVo target = gidMap.get(res.getGid());
                        if (null != target) {
                            res.setSelfSiteIdList(target.getSelfSiteIdList())
                                .setOfflineSiteIdList(target.getOfflineSiteIdList());
                        }
                    }
                    return res;
                }));
    }

    private SiteGroupVo map2Vo(SiteGroupPo po) {
        final SiteGroupVo result = new SiteGroupVo();
        result.setGid(po.getGid())
            .setName(po.getName())
            .setType(po.getType());
        return result;
    }

    public Mono<SiteGroupVo> removeSiteGroup(String gid) {
        IotAssert.isNotBlank(gid, "场站组ID无效");
        SiteGroupPo oldPo = siteGroupRoDs.getByGid(gid);
        if (null != oldPo) {
            boolean b = siteGroupRwDs.disableByGid(gid);
            if (b) {
                // 删除人员与场站组的关系
                int i = siteGroupUserRwDs.deleteByGid(gid);
                // 删除人员关联关系
                siteGroupSiteFeignClient.updateSiteGroupSiteRef(new UpdateSiteGroupSiteParam()
                        .setGid(gid)
                        .setOfflineSiteIdList(new ArrayList<>())
                        .setDirectSiteIdList(new ArrayList<>()))
                    .subscribe(x -> log.info("场站组与场站关系同步完成"));

            } else {
                log.warn("删除场站失败: {}", gid);
            }
            return Mono.just(oldPo)
                .map(this::map2Vo);
        } else {
            return Mono.just(new SiteGroupVo()); // 不存在记录则返回成功
        }
    }

    public Mono<SiteGroupVo> addSiteGroup(SiteGroupDto dto) {
        SiteGroupPo old = siteGroupRoDs.getOneByName(dto.getName(), null);
        IotAssert.isNull(old, "场站组名称已经存在，请更换场站组名称后再操作。");
        return siteGroupRwDs.insertSiteGroup(dto)
            .doOnNext(x -> this.syncSiteGroupSiteRefEvent(dto)) // 场站关系建立
            .map(this::map2Vo);
    }

    public Mono<SiteGroupVo> accountDefaultSiteGroupAppendSite(
        Long id, String siteId) {
        if (null == id) {
            throw new DcArgumentException("用户ID不能为空");
        }

        val user = sysUserRoDs.getByUseId(id);
        if (null == user) {
            throw new DcArgumentException("用户ID无效");
        }

        if (StringUtils.isBlank(siteId)) {
            throw new DcArgumentException("场站ID不能为空");
        }

        String groupName = user.getName() + "场站组"; // 账户名称默认场站组
        SiteGroupPo group = siteGroupRoDs.getOneByName(groupName, null);
        if (null == group) {
            SiteGroupDto newGroup = new SiteGroupDto();
            newGroup.setName(groupName)
                .setAdminUid(id)
                .setClientType(AppClientType.COMM_ESS_MGM)
                .setType(SiteGroupType.UNKNOWN)
                .setOwnType(SiteGroupOwnType.UNKNOWN)
                .setDirectSiteIdList(List.of(siteId))
                .setOfflineSiteIdList(List.of())
                .setSelectAll(false);
            return this.addSiteGroup(newGroup)
                .doOnNext(gp -> siteGroupUserRwDs.batchInsert(id, List.of(gp.getGid())));
        } else { // 场站组已经存在
            SiteGroupDto newGroup = new SiteGroupDto();
            newGroup.setGid(group.getGid())
                .setName(groupName)
                .setAdminUid(id)
                .setClientType(AppClientType.COMM_ESS_MGM)
                .setType(SiteGroupType.UNKNOWN)
                .setOwnType(SiteGroupOwnType.UNKNOWN)
                .setOnlyAppendSiteIdList(List.of(siteId))
                .setSelectAll(false);
            return this.editSiteGroup(newGroup);
        }
    }

    public Mono<SiteGroupVo> editSiteGroup(SiteGroupDto dto) {
        IotAssert.isNotBlank(dto.getGid(), "场站组ID不能为空");
        SiteGroupPo old = siteGroupRoDs.getByGid(dto.getGid());
        IotAssert.isNotNull(old, "场站组ID无效");
        old = siteGroupRoDs.getOneByName(dto.getName(), dto.getGid());
        IotAssert.isNull(old, "场站组名称已经存在，请更换场站组名称后再操作。");
        SiteGroupPo newPo = new SiteGroupPo();
        BeanUtils.copyProperties(dto, newPo);
        return siteGroupRwDs.updateSiteGroup(newPo)
            .doOnNext(x -> this.syncSiteGroupSiteRefEvent(dto)) // 场站关系建立
            .map(this::map2Vo);
    }

    private void syncSiteGroupSiteRefEvent(SiteGroupDto dto) {
        IotAssert.isNotBlank(dto.getGid(), "场站ID不能为空");
        siteGroupSiteFeignClient.updateSiteGroupSiteRef(new UpdateSiteGroupSiteParam()
                .setGid(dto.getGid())
                .setOnlyAppendSiteIdList(dto.getOnlyAppendSiteIdList())
                .setOfflineSiteIdList(dto.getOfflineSiteIdList())
                .setDirectSiteIdList(dto.getDirectSiteIdList()))
            .subscribe(x -> log.info("场站组与场站关系同步完成"));
    }

    public ListResponse<SiteGroupVo> findSiteGroupAndUser(ListSiteGroupParam param) {
        return siteGroupRoDs.findSiteGroupAndUser(param)
            .block(Duration.ofSeconds(50L));
    }

    public List<SiteGroupPo> getSiteGroupList(ListSiteGroupParam param) {
        return siteGroupRoDs.getSiteGroupList(param);
    }

    public List<UserGroupVo> findUserGroup(ListSiteGroupParam param) {
        return siteGroupRoDs.findUserGroup(param);
    }

    public List<UserOwnerSiteGroupDto> userOwnerSiteGroup(ListSiteGroupParam param) {
        return siteGroupRoDs.userOwnerSiteGroup(param);
    }

    public UserOwnerSiteGroupDto userOwnerSite(Long uid, SiteGroupType groupType) {
        // 获取用户所属场站组信息
        final UserOwnerSiteGroupDto result =
            siteGroupRoDs.getUserOwnerSiteGroup(uid, groupType);

        if (SiteGroupType.YW.equals(groupType) && CollectionUtils.isNotEmpty(result.getGroupVoList())) {
            // 获取运维负责场站信息
            // 根据场站组ID获取该场站组下所有的站点信息
            ListSiteParam param = new ListSiteParam();
            param.setGids(result.getGroupVoList().stream().map(SiteGroupVo::getGid).collect(Collectors.toList()));
            result.setSiteIdList(dataCoreFeignClient.getSiteListByGids(param).getData());
        }

        return result;
    }
}
