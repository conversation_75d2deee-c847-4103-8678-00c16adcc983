package com.cdz360.biz.auth.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户数据权限, 地区
 */
@TableName("sys_user_region")
@Data
@EqualsAndHashCode(callSuper = true)
public class SysUserRegion extends BaseEntity {
    @TableField("user_id")
    private Long userId;
    private String regions;
}
