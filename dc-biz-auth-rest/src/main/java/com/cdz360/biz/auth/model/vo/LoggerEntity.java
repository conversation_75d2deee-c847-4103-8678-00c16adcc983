package com.cdz360.biz.auth.model.vo;

import lombok.Data;

@Data
public class LoggerEntity {
    private String ip;
    private String userAgent;
    private String requestParam;
    private String url;
    private String postBody;
    private String requestMethod;
    private String method;
    private Object responseParam;
    private String user;
    private String userName;
    private long startTime;
    private long endTime;
    private int consume;
    private String token;
    private String described;
    private String afterPlatformTraceId;
    private String exception;
    private String type;
    private String httpCode;
    private String rawData;
    private String modifiedData;



}
