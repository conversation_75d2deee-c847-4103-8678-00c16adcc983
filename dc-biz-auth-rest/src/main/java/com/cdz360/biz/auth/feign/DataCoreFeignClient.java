package com.cdz360.biz.auth.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.sys.param.SiteGroupSiteParam;
import com.cdz360.biz.model.sys.vo.AccRelativeOrderVo;
import java.util.List;

import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, fallbackFactory = HystrixDataCoreClientFactory.class)
public interface DataCoreFeignClient {

    // 通过运维人员获取已处理工单数
    @PostMapping(value = "/dataCore/ywOrder/getOrderNum")
    ListResponse<AccRelativeOrderVo> getOrderNum(@RequestBody List<Long> sysUidList);

    // 检查是否有未结束的巡检单
    @PostMapping(value = "/dataCore/inspection/existUnfinishedRecord")
    ObjectResponse<Boolean> existUnfinishedRecord(@RequestBody List<Long> sysUidList);

    // 检查是否有未结束的运维单
    @PostMapping(value = "/dataCore/ywOrder/existUnfinishedOrder")
    ObjectResponse<Boolean> existUnfinishedOrder(@RequestBody List<Long> sysUidList);

    // 通过商户链获取商户及子商户枪头总数
    @GetMapping("/dataCore/site/getChargerNumByChain")
     ObjectResponse<Long> getChargerNumByChain(@RequestParam(value = "commIdChain") String commIdChain);

    // 获取场站关联的场站组ID列表
    @PostMapping(value = "/dataCore/siteGroupSite/findGidsBySiteId")
    ListResponse<String> findSiteGroupSiteBySiteId(@RequestBody SiteGroupSiteParam param);

    @PostMapping("/dataCore/site/getSiteListByGids")
    ListResponse<String> getSiteListByGids(@RequestBody ListSiteParam params);
}
