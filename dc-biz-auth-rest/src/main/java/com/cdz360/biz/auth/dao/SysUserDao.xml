<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.dao.SysUserDao">
  <!--    <insert id="insertUserPositionList">-->
  <!--        INSERT INTO sys_user_postition(user_id, post_id, prime,create_by,create_time)-->
  <!--        VALUES-->
  <!--        <foreach collection="posts" item="post" separator=",">-->
  <!--            <![CDATA[-->
  <!--                (#{userId},#{post.id},#{post.prime}<=>1,#{curuid},now())-->
  <!--        ]]>-->
  <!--        </foreach>-->
  <!--    </insert>-->
  <delete id="purgeByUserId">
    DELETE
    FROM sys_user_postition
    WHERE user_id = #{userId};
  </delete>


  <select id="queryByUserNameOrPhoneOrEmail" resultType="com.cdz360.biz.auth.model.vo.SysUser">
    SELECT
    id AS id,email,`name`,`password`,phone,salt,sex,`status`,platform,username,tenant_id,locked
    FROM sys_user WHERE username=#{username} OR phone=#{username} OR email=#{username}
  </select>

  <select id="queryByUserName" resultType="com.cdz360.biz.auth.model.vo.SysUser">
    SELECT
    id AS id,email,`name`,`password`,phone, topCommId, commId,
    salt,sex,`status`,platform,username,tenant_id,locked
    FROM sys_user WHERE username=#{username} and platform = #{platform}
  </select>

  <select id="checkByUserNameOrEmail" resultType="com.cdz360.biz.auth.model.vo.SysUser">
    SELECT
    id AS id,email,`name`,`password`,phone,salt,sex,`status`,platform,username,tenant_id,locked
    FROM sys_user
    WHERE
    (
    username=#{username}
    <!--        OR phone=#{phone}-->
    <if test="email != null and email != '' ">
      OR email=#{email}
    </if>
    )
    <if test="platform != null">
      and platform = #{platform}
    </if>
  </select>

  <select id="checkByUserNameOrPhoneOrEmail" resultType="com.cdz360.biz.auth.model.vo.SysUser">
    SELECT
    id AS id,email,`name`,`password`,phone,salt,sex,`status`,platform,username,tenant_id,locked
    FROM sys_user
    WHERE
    (
    username=#{username}
    OR phone=#{phone}
    <if test="email != null and email != '' ">
      OR email=#{email}
    </if>
    )
    <if test="platform != null">
      and platform = #{platform}
    </if>
  </select>

  <select id="checkByPhoneAndPlantform" resultType="java.lang.Integer">
    select
    count(*)
    from
    sys_user
    where
    phone = #{phone}
    and platform = #{plantform}
    <if test="sysUserId != null and sysUserId > 0">
      and id != #{sysUserId}
    </if>
  </select>

  <!--    <select id="findUserPositionList" resultType="com.cdz360.biz.auth.model.vo.SysPosition">-->
  <!--        SELECT *-->
  <!--        FROM sys_user_postition up-->
  <!--        JOIN sys_position p ON up.post_id = p.id-->
  <!--        WHERE-->
  <!--        <![CDATA[-->
  <!--                (NOT  p.status <=> 2)-->
  <!--        ]]>-->

  <!--        AND up.user_id=#{userId}-->
  <!--    </select>-->
  <select id="countPostsRefByUser" resultType="java.lang.Integer">
    SELECT count(1)
    FROM sys_user_postition up JOIN sys_user u on up.user_id = u.id
    WHERE post_id=#{postId}
    AND
    <![CDATA[
              (not u.status<=>2)
        ]]>
  </select>
  <select id="findUserRoleList" resultType="com.cdz360.biz.auth.model.vo.SysRole">
    SELECT r.id,r.name,r.category,r.status,r.svaha
    FROM sys_role r
    JOIN sys_user_role s on r.id = s.role_id
    WHERE (NOT  <![CDATA[
          status <=> 2
        ]]>)
    AND s.user_id=#{userId}
    AND s.enable=true and (s.expireTime is null or s.expireTime>=DATE_FORMAT(now(),'%Y-%m-%d
    00:00:00'))
  </select>

  <select id="selectByPositionId" resultType="com.cdz360.biz.auth.model.vo.SysUser">
    SELECT
    su.id AS
    id,su.email,su.`name`,su.`password`,su.phone,su.salt,su.sex,su.`status`,su.platform,su.username,tenant_id
    FROM sys_user su,sys_user_postition sup,sys_position sp
    WHERE su.id=sup.user_id and sup.post_id=sp.id and sp.id=#{id}
  </select>

  <select id="querySysUserByIds" parameterType="java.util.Map"
    resultType="com.cdz360.biz.auth.model.vo.SysUser">
    SELECT
    id AS id,email,`name`,`password`,phone,salt,sex,`status`,platform,username,tenant_id,locked,corpWxUid,commId,topCommId
    FROM sys_user
    WHERE 1=1
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( idList )">
      and id IN
      <foreach collection="idList" index="index" item="item" open="("
        separator="," close=")">
        #{item}
      </foreach>
    </if>
  </select>

  <select id="querySysUser"
    parameterType="com.cdz360.biz.auth.sys.param.QuerySysUserRequest"
    resultType="com.cdz360.biz.auth.model.vo.SysUser">
    SELECT
    <!--        <if test="platform == null">-->
    group_concat(DISTINCT(ag.groupName) SEPARATOR ',') AS groupName,
    <!--        </if>-->
    group_concat(DISTINCT(sr. NAME) SEPARATOR ',') AS roleName,
    <!--sr.status AS roleStatus,-->
    su.id AS id,
    su.birthday,
    su.email,
    su.`name`,
    su.`password`,
    su.phone,
    su.salt,
    su.sex,
    su.`status`,
    su.platform,
    su.username,
    su.tenant_id AS tenantId,
    su.last_login_time AS lastLoginTime,
    su.locked,
    su.credentials,
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( commIdList )">
      suc.commercial_id,
    </if>
    <!--        <if test="platform == 22">-->
    group_concat(DISTINCT(corp_org. orgName) SEPARATOR ',') AS orgName,
    group_concat(DISTINCT(sg. name) SEPARATOR ',') AS gNameStr,
    group_concat(DISTINCT(sg. gid) SEPARATOR ',') AS gidStr,
    <!--        </if>-->
    su.create_time AS createTime,
    su.update_time AS updateTime,
    su.create_by AS createBy,
    su.update_by AS updateBy,
    su.teamCatalog,
    su.corpWxAppId,
    corpWxApp.name corpWxAppName
    FROM sys_user su
    <!--        <if test=" platform == null">-->
    LEFT JOIN t_user_group_ref ugr ON ugr.userId = su.id
    LEFT JOIN t_authority_group ag ON ugr.authGroupId = ag.id
    <!--        </if>-->
    LEFT JOIN sys_user_role sur ON sur.user_id = su.id and sur.enable=true and (sur.expireTime is
    null or sur.expireTime>=DATE_FORMAT(now(),'%Y-%m-%d 00:00:00'))
    LEFT JOIN sys_role sr ON sr.id = sur.role_id and sr.status <![CDATA[ <> ]]> 2
    left join t_corp_wx_app corpWxApp on corpWxApp.id = su.corpWxAppId
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( commIdList )">
      left join sys_user_comercial suc on suc.user_id = su.id
    </if>
    left join t_site_group_user_ref ref on ref.uid = su.id
    left join t_site_group sg on sg.gid = ref.gid and sg.enable = true

    <!--        <if test=" platform != null and platform == 22">-->
    left join t_corp_user_org user_org on user_org.sysUid = su.id
    left join t_corp_org corp_org on corp_org.id = user_org.orgId
    <!--        </if>-->
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(role)">
      and sr.name like concat("%",#{role},"%")
    </if>
    <if test="id != null and id > 0">
      and corp_org.id = #{id}
    </if>

    where
    1 = 1
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( gidList )">
      <foreach collection="gidList" item="item"
        open="and su.id in (select uid from t_site_group_user_ref gur where gur.gid in ("
        separator="," close="))">
        #{item}
      </foreach>
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(roleName)">
      and sr.name like concat('%', #{roleName}, '%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(accountOrNameLike)">
      and (
      su.username like concat('%', #{accountOrNameLike}, '%')
      or su.name like concat('%', #{accountOrNameLike}, '%')
      )
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(keyword)">
      and (
      su.phone like concat('%', #{keyword}, '%')
      or su.username like concat('%', #{keyword}, '%')
      or su.name like concat('%', #{keyword}, '%')
      <!--<if test=" platform != null and  platform.intValue()!= 22">
        or groupName like concat('%', #{keyword}, '%')
      </if>
      or roleName like concat('%', #{keyword}, '%')-->
      )
      <!--and roleStatus <![CDATA[ <> ]]> 2-->
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(key4word)">
      and (
      su.phone like concat('%', #{key4word}, '%')
      or su.username like concat('%', #{key4word}, '%')
      or su.name like concat('%', #{key4word}, '%')
      or su.teamCatalog like concat("%",#{key4word},"%")
      )
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(userIdlist)">
      and su.id in
      <foreach collection="userIdlist" index="index" item="item" open="("
        separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( commIdList )">
      and suc.commercial_id in
      <foreach collection="commIdList" item="commId" open="("
        separator="," close=")">
        #{commId}
      </foreach>
    </if>
    <if test="platform != null and platform > 0">
      and su.platform = #{platform}
    </if>
    <if test="platform == null">
      <!--            为空表示来自支撑平台的调用，且不加筛选条件-->
      and su.platform in (20,21,30,90,92)
    </if>
    <if test="userId != null and userId > 0">
      and (su.create_by = #{userId} or su.id = #{userId})
    </if>
    <if test="limitCreateBy != null">
      and su.create_by = #{limitCreateBy}
    </if>
    <if test="status != null and status > 0">
      and su.status = #{status}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(account)">
      and su.username like concat("%",#{account},"%")
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(teamCatalog)">
      and su.teamCatalog like concat("%",#{teamCatalog},"%")
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( teamCatalogList )">
      and
      <foreach collection="teamCatalogList" item="item" index="index" separator=" or " open="("
        close=")">
        su.teamCatalog like concat("%",#{item},"%")
      </foreach>
    </if>
    <!--where su.status=1-->
    GROUP by
    su.id
    <!--    HAVING-->
    <!--    1=1-->
    <!--    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( gidList )">-->
    <!--      <foreach collection="gidList" item="item"-->
    <!--        open="and su.id in (select uid from t_site_group_user_ref gur where gur.gid in ("-->
    <!--        separator="," close="))">-->
    <!--        #{item}-->
    <!--      </foreach>-->
    <!--    </if>-->
    <!--    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(roleName)">-->
    <!--      and roleName like concat('%', #{roleName}, '%')-->
    <!--    </if>-->
    <!--    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(accountOrNameLike)">-->
    <!--      and (-->
    <!--      su.username like concat('%', #{accountOrNameLike}, '%')-->
    <!--      or su.name like concat('%', #{accountOrNameLike}, '%')-->
    <!--      )-->
    <!--    </if>-->
    <!--    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(keyword)">-->
    <!--      and (-->
    <!--      su.phone like concat('%', #{keyword}, '%')-->
    <!--      or su.username like concat('%', #{keyword}, '%')-->
    <!--      or su.name like concat('%', #{keyword}, '%')-->
    <!--      &lt;!&ndash;<if test=" platform != null and  platform.intValue()!= 22">-->
    <!--        or groupName like concat('%', #{keyword}, '%')-->
    <!--      </if>-->
    <!--      or roleName like concat('%', #{keyword}, '%')&ndash;&gt;-->
    <!--      )-->
    <!--      &lt;!&ndash;and roleStatus <![CDATA[ <> ]]> 2&ndash;&gt;-->
    <!--    </if>-->
    <!--    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(userIdlist)">-->
    <!--      and su.id in-->
    <!--      <foreach collection="userIdlist" index="index" item="item" open="("-->
    <!--        separator="," close=")">-->
    <!--        #{item}-->
    <!--      </foreach>-->
    <!--    </if>-->
    <!--    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( commIdList )">-->
    <!--      and suc.commercial_id in-->
    <!--      <foreach collection="commIdList" item="commId" open="("-->
    <!--        separator="," close=")">-->
    <!--        #{commId}-->
    <!--      </foreach>-->
    <!--    </if>-->
    <!--    <if test="platform != null and platform > 0">-->
    <!--      and su.platform = #{platform}-->
    <!--    </if>-->
    <!--    <if test="platform == null">-->
    <!--      &lt;!&ndash;            为空表示来自支撑平台的调用，且不加筛选条件&ndash;&gt;-->
    <!--      and su.platform in (20,21,90,92)-->
    <!--    </if>-->
    <!--    <if test="userId != null and userId > 0">-->
    <!--      and (su.create_by = #{userId} or su.id = #{userId})-->
    <!--    </if>-->
    <!--    <if test="status != null and status > 0">-->
    <!--      and su.status = #{status}-->
    <!--    </if>-->
    <!--    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(account)">-->
    <!--      and su.username like concat("%",#{account},"%")-->
    <!--    </if>-->
    <!--    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(teamCatalog)">-->
    <!--      and su.teamCatalog like concat("%",#{teamCatalog},"%")-->
    <!--    </if>-->
    <!--    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( teamCatalogList )">-->
    <!--      and-->
    <!--      <foreach collection="teamCatalogList" item="item" index="index" separator=" or " open="("-->
    <!--        close=")">-->
    <!--        su.teamCatalog like concat("%",#{item},"%")-->
    <!--      </foreach>-->
    <!--    </if>-->
    ORDER BY
    su.create_time DESC
    limit #{start}, #{size}
  </select>

  <select id="querySysUserCount"
    parameterType="com.cdz360.biz.auth.sys.param.QuerySysUserRequest"
    resultType="java.lang.Long">
    select count(*) from (
    SELECT
    <!--        <if test="platform == null">-->
    group_concat(DISTINCT(ag.groupName) SEPARATOR ',') AS groupName,
    <!--        </if>-->
    group_concat(DISTINCT(sr. NAME) SEPARATOR ',') AS roleName,
    <!--sr.status AS roleStatus,-->
    su.id AS id,
    su.birthday,
    su.email,
    su.`name`,
    su.`password`,
    su.phone,
    su.salt,
    su.sex,
    su.`status`,
    su.platform,
    su.username,
    su.tenant_id AS tenantId,
    su.last_login_time AS lastLoginTime,
    su.locked,
    su.credentials,
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( commIdList )">
      suc.commercial_id,
    </if>
    <!--        <if test="platform == 22">-->
    group_concat(DISTINCT(corp_org. orgName) SEPARATOR ',') AS orgName,
    <!--        </if>-->
    su.create_time AS createTime,
    su.update_time AS updateTime,
    su.create_by AS createBy,
    su.update_by AS updateBy,
    su.teamCatalog,
    su.corpWxAppId,
    corpWxApp.name corpWxAppName
    FROM
    sys_user su
    <!--        <if test=" platform == null">-->
    LEFT JOIN t_user_group_ref ugr ON ugr.userId = su.id
    LEFT JOIN t_authority_group ag ON ugr.authGroupId = ag.id
    <!--        </if>-->
    LEFT JOIN sys_user_role sur ON sur.user_id = su.id and sur.enable=true and (sur.expireTime is
    null or sur.expireTime>=DATE_FORMAT(now(),'%Y-%m-%d 00:00:00'))
    LEFT JOIN sys_role sr ON sr.id = sur.role_id and sr.status <![CDATA[ <> ]]> 2
    left join t_corp_wx_app corpWxApp on corpWxApp.id = su.corpWxAppId
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( commIdList )">
      left join sys_user_comercial suc on suc.user_id = su.id
    </if>

    <!--        <if test=" platform != null and platform == 22">-->
    left join t_corp_user_org user_org on user_org.sysUid = su.id
    left join t_corp_org corp_org on corp_org.id = user_org.orgId
    <!--        </if>-->
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(role)">
      and sr.name like concat("%",#{role},"%")
    </if>
    <if test="id != null and id > 0">
      and corp_org.id = #{id}
    </if>
    <!--where su.status=1-->
    <!--    GROUP by-->
    <!--    su.id-->
    <!--    HAVING-->
    where
    1=1
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( gidList )">
      <foreach collection="gidList" item="item"
        open="and su.id in (select uid from t_site_group_user_ref gur where gur.gid in ("
        separator="," close="))">
        #{item}
      </foreach>
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(roleName)">
      and sr.name like concat('%', #{roleName}, '%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(accountOrNameLike)">
      and (
      su.username like concat('%', #{accountOrNameLike}, '%')
      or su.name like concat('%', #{accountOrNameLike}, '%')
      )
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(keyword)">
      and (
      su.phone like concat('%', #{keyword}, '%')
      or su.username like concat('%', #{keyword}, '%')
      or su.name like concat('%', #{keyword}, '%')
      <!--<if test=" platform != null and  platform.intValue()!= 22">
        or groupName like concat('%', #{keyword}, '%')
      </if>
      or roleName like concat('%', #{keyword}, '%')-->
      )
      <!--and roleStatus <![CDATA[ <> ]]> 2-->
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(userIdlist)">
      and su.id in
      <foreach collection="userIdlist" index="index" item="item" open="("
        separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( commIdList )">
      and suc.commercial_id in
      <foreach collection="commIdList" item="commId" open="("
        separator="," close=")">
        #{commId}
      </foreach>
    </if>
    <if test="platform != null and platform > 0">
      and su.platform = #{platform}
    </if>
    <if test="platform == null">
      <!--            为空表示来自支撑平台的调用，且不加筛选条件-->
      and su.platform in (20,21,90,92)
    </if>
    <if test="userId != null and userId > 0">
      and (su.create_by = #{userId} or su.id = #{userId})
    </if>
    <if test="limitCreateBy != null">
      and su.create_by = #{limitCreateBy}
    </if>
    <if test="status != null and status > 0">
      and su.status = #{status}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(account)">
      and su.username like concat("%",#{account},"%")
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(teamCatalog)">
      and su.teamCatalog like concat("%",#{teamCatalog},"%")
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( teamCatalogList )">
      and
      <foreach collection="teamCatalogList" item="item" index="index" separator=" or " open="("
        close=")">
        su.teamCatalog like concat("%",#{item},"%")
      </foreach>
    </if>
    GROUP by
    su.id
    ORDER BY
    su.create_time DESC
    ) a
  </select>
  <select id="checkUserNameUniqueAndPlantform" resultType="java.lang.Integer">
    select
    count(*)
    from
    sys_user
    where
    username = #{username}
    and platform = #{plantform}
    <if test="sysUserId != null and sysUserId > 0">
      and id != #{sysUserId}
    </if>
  </select>

  <sql id="SAME_CORP_WX_APP_NAME_SQL">
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(accountOrNameLike)">
      and (
      su.username like concat('%', #{accountOrNameLike}, '%')
      or su.name like concat('%', #{accountOrNameLike}, '%')
      )
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(keyword)">
      and (
      su.phone like concat('%', #{keyword}, '%')
      or su.username like concat('%', #{keyword}, '%')
      or su.name like concat('%', #{keyword}, '%')
      )
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(userIdlist)">
      and su.id in
      <foreach collection="userIdlist" index="index" item="item" open="("
        separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="platform != null and platform > 0">
      and su.platform = #{platform}
    </if>
    <if test="platform == null">
      <!--            为空表示来自支撑平台的调用，且不加筛选条件-->
      and su.platform in (20,21,90,92)
    </if>
    <if test="userId != null and userId > 0">
      and (su.create_by = #{userId} or su.id = #{userId})
    </if>
    <if test="status != null and status > 0">
      and su.status = #{status}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(account)">
      and su.username like concat("%",#{account},"%")
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(teamCatalog)">
      and su.teamCatalog like concat("%",#{teamCatalog},"%")
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( teamCatalogList )">
      and
      <foreach collection="teamCatalogList" item="item" index="index" separator=" or " open="("
        close=")">
        su.teamCatalog like concat("%",#{item},"%")
      </foreach>
    </if>
  </sql>

  <select id="sameCorpWxAppNameSysUser"
    parameterType="com.cdz360.biz.auth.sys.param.QuerySysUserRequest"
    resultType="com.cdz360.biz.auth.model.vo.SysUser">
    SELECT
    su.id,
    su.`name`,
    su.phone,
    su.username
    FROM sys_user su
    where 1=1
    <include refid="SAME_CORP_WX_APP_NAME_SQL"/>
    limit #{start}, #{size}
  </select>

  <select id="countSameCorpWxAppNameSysUser"
    parameterType="com.cdz360.biz.auth.sys.param.QuerySysUserRequest"
    resultType="java.lang.Long">
    SELECT
    count(su.id)
    FROM sys_user su
    where 1=1
    <include refid="SAME_CORP_WX_APP_NAME_SQL"/>
  </select>
</mapper>