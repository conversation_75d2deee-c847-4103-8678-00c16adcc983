package com.cdz360.biz.auth.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;

/**
 * <p>
 * 用户数据权限
 * </p>
 *
 * <AUTHOR>
  * @since 2018-12-26
 */
@TableName("sys_subject_field")
public class SysSubjectField extends Model<SysSubjectField> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 业务资源：比如订单、设备表等，目前默认只有一个def
     */
    @TableField("resource_type")
    private String resourceType;
    /**
     * 1.用户 2.岗位
     */
    @TableField("subject_type")
    private Integer subjectType;
    /**
     * 主题id，账号or岗位
     */
    @TableField("subject_id")
    private Long subjectId;
    /**
     * 业务数据field 编码
     */
    @TableField("field_type")
    private String fieldType;
    /**
     * 业务属性值，比如城市的编码、设备的类型具体值
     */
    @TableField("field_value")
    private String fieldValue;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getResourceType() {
        return resourceType;
    }

    public void setResourceType(String resourceType) {
        this.resourceType = resourceType;
    }

    public Integer getSubjectType() {
        return subjectType;
    }

    public void setSubjectType(Integer subjectType) {
        this.subjectType = subjectType;
    }

    public Long getSubjectId() {
        return subjectId;
    }

    public void setSubjectId(Long subjectId) {
        this.subjectId = subjectId;
    }

    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    public String getFieldValue() {
        return fieldValue;
    }

    public void setFieldValue(String fieldValue) {
        this.fieldValue = fieldValue;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "SysSubjectField{" +
                ", id=" + id +
                ", resourceType=" + resourceType +
                ", subjectType=" + subjectType +
                ", subjectId=" + subjectId +
                ", fieldType=" + fieldType +
                ", fieldValue=" + fieldValue +
                "}";
    }
}
