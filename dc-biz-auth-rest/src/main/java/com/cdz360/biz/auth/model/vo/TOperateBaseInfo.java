package com.cdz360.biz.auth.model.vo;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@TableName("t_operate_base_info")
@Data
@EqualsAndHashCode(callSuper = true)
public class TOperateBaseInfo extends BaseEntity implements LogicDeletable {
    private String operateName;

    private String shortName;

    private String serviceTime;

    private String servicePhone;

    private String remark;

    private String icon;

    private String appName;

    private String iosUrl;

    private String androidUrl;

    private Integer businessStatus;
    @TableLogic(value = "1")
    private Integer status;

}