package com.cdz360.biz.auth.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.auth.ds.ro.user.SysUserLogRoDs;
import com.cdz360.biz.auth.user.param.SysUserLogParam;
import com.cdz360.biz.auth.user.vo.SysUserLogVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SysUserLogService {
    @Autowired
    private SysUserLogRoDs sysUserLogRoDs;

    public ListResponse<SysUserLogVo> getLoginLog(SysUserLogParam param) {
        return sysUserLogRoDs.getLoginLog(param);
    }

    public ListResponse<SysUserLogVo> getOpLog(SysUserLogParam param) {
        return sysUserLogRoDs.getOpLog(param);
    }
}
