package com.cdz360.biz.auth.service.msg;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.auth.ds.ro.user.SysUserRoDs;
import com.cdz360.biz.auth.ds.rw.message.MessageRwDs;
import com.cdz360.biz.auth.feign.CarUserFeignClient;
import com.cdz360.biz.auth.model.vo.TCommercialManage;
import com.cdz360.biz.auth.service.TCommercialManageService;
import com.cdz360.biz.auth.user.po.SysUserPo;
import com.cdz360.biz.auth.utils.RedisUtils;
import com.cdz360.biz.model.cus.message.po.MessagePo;
import com.cdz360.biz.model.cus.message.po.MsgTemplatePO;
import com.cdz360.biz.model.cus.message.type.MsgTemplate;
import com.cdz360.biz.model.cus.message.type.MsgType;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedDeque;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MsgSendService implements MsgSendObserve, Runnable {

    @Autowired
    private SysUserRoDs sysUserRoDs;
    @Autowired
    private MessageRwDs messageRwDs;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private TCommercialManageService tCommercialManageService;
    @Autowired
    private CarUserFeignClient carUserFeignClient;

    private Queue<MessagePo> msgQ = new ConcurrentLinkedDeque<>();
    private String wxLiteMgmToken = "wxLiteMgmAccessToken";


    @PostConstruct
    public void init() {
        new Thread(this).start();
    }

    @Override
    public void notifyEvent() {
        synchronized (this) {
            log.info("size = {}", msgQ.size());
            this.notifyAll();
        }
    }

    @Override
    public void addEventMsg(MessagePo po) {
        this.msgQ.add(po);
    }

    @Override
    public void run() {
        while (true) {
            try {
                synchronized (this) {
                    this.process();
                    this.wait();
                }
            } catch (Exception e) {
                log.error("处理异常: {}", e.getMessage(), e);
            }
        }
    }

    private void process() throws UnsupportedEncodingException {
        while (!msgQ.isEmpty()) {
            MessagePo msg = msgQ.remove();
            this.process(msg);
        }
    }

    /**
     * 业务处理
     *
     * @param msg
     */
    public void process(MessagePo msg) {
        // 查询发送
        log.info("发送消息:msg={}", msg);
        int start = 0, size = 500;
        while (true) {
            // 运维工单处理逻辑
            if (MsgType.YW_REMINDER.equals(msg.getMsgType())
                || MsgType.INSPECTION_REMINDER.equals(msg.getMsgType())) {
                messageRwDs.batchInsert(msg.getTargetUid(), msg.getPlatform(), msg.getId());
                break;
            }

            List<SysUserPo> userInfoList = null;
            if (msg.getCorpId() != null) {
                //系统续费提醒
                userInfoList = sysUserRoDs.getUserIdListForCorp(msg.getCommIdList(),
                    msg.getPlatform(), msg.getCorpId(), start, size);
            } else {
                userInfoList = sysUserRoDs.getUserIdList(msg.getCommIdList(), msg.getPlatform(),
                    start, size);
            }

            log.info("返回数据：re={}", userInfoList.size());
            List<Long> userIdList = userInfoList.stream().map(e -> e.getId())
                .collect(Collectors.toList());
            messageRwDs.batchInsert(userIdList, msg.getPlatform(), msg.getId());

            //升级通知
//                if (msg.getMsgType().equals(MsgType.NOTICE) &&
//                        (msg.getPlatform().equals(PlatformType.MANAGE)||msg.getPlatform().equals(PlatformType.ALL))) {
//                    //按照topCommId分组
//                    Map<Long, List<SysUserPo>> groupByTopCommId = userInfoList.stream().filter(e->e.getWxOpenId()!=null && e.getWxOpenId().length()>0).collect(Collectors.groupingBy(SysUserPo::getTopCommId));
//                    if (groupByTopCommId.size()>0) {
//                        this.sendNoticeMsg(msg,groupByTopCommId);
//                    }
//                }
            if (userInfoList.size() < size) {
                break;
            }
            start += size;
        }
    }

    /**
     * 发送升级通知
     */
    public void sendNoticeMsg(MessagePo msg, Map<Long, List<SysUserPo>> list)
        throws UnsupportedEncodingException {

        Map<String, Object> commData = new HashMap<>();
        Map<String, String> things2 = new HashMap<>();
        Map<String, String> things1 = new HashMap<>();
        things2.put("value", msg.getTitle());
        things1.put("value", "升级通知");
        commData.put("thing2", things2);
        commData.put("thing1", things1);

        list.forEach((key, userList) -> {
            String accessToken = this.getOpenId(key);
            if (StringUtils.isNotBlank(accessToken)) {
                String url =
                    "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token="
                        + accessToken;
                userList.forEach(e -> {
                    Map<String, Object> postData = new HashMap<>();
                    postData.put("touser", e.getWxOpenId());
                    String templateId = this.getTemplateId(MsgTemplate.WX_LITE_MGM_NOTICE, key);
                    if (StringUtils.isBlank(templateId)) {
                        return;
                    }
                    postData.put("template_id", templateId);
                    postData.put("page", "pages/messageNotice/messageNotice");
                    postData.put("data", commData);
                    try {
                        this.sendPost(url, JsonUtils.toJsonString(postData));
                    } catch (IOException ex) {
                        ex.printStackTrace();
                    }
                });
            }
        });
    }

    public String getTemplateId(MsgTemplate key, Long topCommId) {
        String templateId = "";
        ObjectResponse<MsgTemplatePO> msgTemplatePOObjectResponse = carUserFeignClient.getMsgTemplate(
            key.name(), topCommId);
        if (msgTemplatePOObjectResponse.getData() != null) {
            templateId = msgTemplatePOObjectResponse.getData().getValue();
        }
        return templateId;
    }

    /**
     * 获取openId
     *
     * @param topCommId
     * @return
     */
    public String getOpenId(Long topCommId) {

        if (StringUtils.isNotBlank(redisUtils.get(wxLiteMgmToken + ":" + topCommId))) {
            return redisUtils.get(wxLiteMgmToken + ":" + topCommId);
        }
        TCommercialManage manageInfo = tCommercialManageService.getTCommercialManage(topCommId);
        if (manageInfo == null || manageInfo.getWxLiteMgmAppId() == null) {
            throw new DcServiceException("微信账户信息未配置");
        }
        String getTokenUrl =
            "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid="
                + manageInfo.getWxLiteMgmAppId() + "&secret=" + manageInfo.getWxLiteMgmSecret();
        JsonNode result = sendGet(getTokenUrl);
        if (result != null) {
            String accessToken = result.get("access_token").asText();
            redisUtils.set(wxLiteMgmToken + ":" + topCommId, accessToken, 7200);
            return accessToken;
        }
        return "";
    }

    /**
     * 发送get请求
     *
     * @param url
     * @return
     */
    public JsonNode sendGet(String url) {
        try {
            URL requestUrl = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) requestUrl.openConnection();
            connection.setRequestMethod("GET");
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.connect();
            InputStream inputStream = connection.getInputStream();
            int size = inputStream.available();
            byte[] bs = new byte[size];
            inputStream.read(bs);
            String message = new String(bs, "UTF-8");
            JsonNode jsonObject = JsonUtils.fromJson(message);
            return jsonObject;
        } catch (Exception e) {
            log.info("请求失败", e.getMessage(), e);
            return null;
        }
    }

    public void sendPost(String url, String JSONBody) throws IOException {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        httpPost.addHeader("Content-Type", "application/json");
        httpPost.setEntity(new StringEntity(JSONBody));
        CloseableHttpResponse response = httpClient.execute(httpPost);
        HttpEntity entity = response.getEntity();
        String responseContent = EntityUtils.toString(entity, "UTF-8");
        response.close();
        httpClient.close();
    }
}
