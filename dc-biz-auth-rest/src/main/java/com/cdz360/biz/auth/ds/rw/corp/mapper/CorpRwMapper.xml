<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.ds.rw.corp.mapper.CorpRwMapper">
    <insert id="insertCorp" useGeneratedKeys="true" keyProperty="id" keyColumn="id" >
        INSERT INTO t_corp (
        topCommId,
        commId,
        uid,
        corpName,
        contactName,
        phone,
        type,
        email,
        province,
        city,
        district,
        address,
        businessImage,
        creatorId,
        creatorName,
        createTime,
        updateTime,
        `enable`,
         sysUid,
         digest
        )
        VALUES
        (
        #{topCommId},
        #{commId},
        #{uid},
        #{corpName},
        #{contactName},
        #{phone},
        #{type},
        #{email},
        #{province},
        #{city},
        #{district},
        #{address},
        #{businessImage},
        #{creatorId},
        #{creatorName},
        now(),
        now(),
        #{enable},
        #{sysUid},
        #{digest}
        )
    </insert>
    <insert id="addOrUpdateCorpOrg" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
       INSERT INTO t_corp_org (
        corpId,
        orgName,
        orgLevel,
        l1Id,
        l2Id,
        createTime,
        updateTime,
        `enable`
        )
        VALUES
        (
        #{corpId},
        #{orgName},
        #{orgLevel},
        #{l1Id},
        #{l2Id},
        now(),
        now(),
        #{enable}
        )
    </insert>
    <insert id="insertCorpUserOrg">
        INSERT INTO t_corp_user_org (
        corpId,
        sysUid,
        orgId,
        createTime
        )
        VALUES
        (
        #{corpId},
        #{sysUid},
        #{orgId},
        now()
        )
    </insert>
    <insert id="addCorpOrg" useGeneratedKeys="true" keyProperty="id">
        insert into t_corp_org
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="corpId != null">
                corpId,
            </if>
            <if test="orgName != null">
                orgName,
            </if>
            <if test="orgLevel != null">
                orgLevel,
            </if>
            <if test="l1Id != null">
                l1Id,
            </if>
            <if test="l2Id != null">
                l2Id,
            </if>
            <if test="account != null">
                account,
            </if>
            <if test="password != null">
                password,
            </if>
            createTime,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="corpId != null">
                #{corpId},
            </if>
            <if test="orgName != null">
                #{orgName},
            </if>
            <if test="orgLevel != null">
                #{orgLevel},
            </if>
            <if test="l1Id != null">
                #{l1Id},
            </if>
            <if test="l2Id != null">
                #{l2Id},
            </if>
            <if test="account != null">
                #{account},
            </if>
            <if test="password != null">
                #{password},
            </if>
            now(),
        </trim>
    </insert>
    <insert id="addCorpUserRole">
        insert into sys_user_role
        (user_id,
         role_id,
         create_time,
         update_time,
         create_by,
         update_by)
        VALUES (
        #{userId},
        (SELECT ID FROM SYS_ROLE WHERE PLATFORM = 22 AND NAME = "超级管理员"),
        now(),
        now(),
        1,
        1
                );
    </insert>
    <update id="updateCorpOrg">
        update t_corp_org
        set
        <if test="corpId != null">
            corpId=#{corpId},
        </if>
        <if test="orgName != null">
            orgName=#{orgName},
        </if>
        <if test="orgLevel != null">
            orgLevel=#{orgLevel},
        </if>
        <if test="l1Id != null">
            l1Id=#{l1Id},
        </if>
        <if test="orgLevel != null">
            l2Id=#{l2Id},
        </if>
        <if test="password != null">
            password=#{password},
        </if>
        <if test="enable != null">
            `enable`=#{enable},
        </if>
        updateTime=now()
        <where>
            id=#{id}
        </where>
    </update>
    <update id="updateCorp">
        UPDATE t_corp SET
        <if test="topCommId != null">
            topCommId = #{topCommId},
        </if>
        <if test="commId != null">
            commId = #{commId},
        </if>
        <if test="blocUserName != null">
            corpName = #{blocUserName},
        </if>
        <if test="contactName != null">
           contactName = #{contactName},
        </if>
        <if test="email != null">
           email = #{email},
        </if>
        <if test="province != null">
           province = #{province},
        </if>
        <if test="city != null">
           city = #{city},
        </if>
        <if test="district != null">
           district = #{district},
        </if>
        <if test="address != null">
           address = #{address},
        </if>
        <if test="businessImage != null">
           businessImage = #{businessImage},
        </if>
        <if test="phone != null">
           phone = #{phone},
        </if>
        <if test="settlementType != null">
            settlementType = #{settlementType.code},
        </if>
        <if test="invoiceWay != null">
            invoiceWay = #{invoiceWay},
        </if>
        <if test="isSendReminderEmail != null">
            isSendReminderEmail = #{isSendReminderEmail},
        </if>
        <if test="digest != null">
            digest = #{digest},
        </if>
        <if test="fullInvoicing != null">
            fullInvoicing = #{fullInvoicing},
        </if>
        updateTime = now()
        WHERE id = #{id}
    </update>
    <update id="updateCorpEnable">
        update t_corp set enable=ABS(enable-1) where id = #{corpId};
    </update>
    <select id="countOrgByName" resultType="com.cdz360.biz.model.cus.corp.po.CorpOrgPo">
        SELECT
        *
        FROM `t_corp_org` where orgName =#{name}
        <if test="corpId!=null and corpId>0">
            and corpId=#{corpId}
        </if>
    </select>
    <select id="getOrgById" resultType="com.cdz360.biz.model.cus.corp.po.CorpOrgPo">
        select * from t_corp_org where id = #{orgId}
    </select>

    <update id="moveCorp">
        update t_corp set
            commId = #{commId},
            topCommId = #{topCommId}
        where
        id = #{corpId}
    </update>

    <update id="setRenewReminderAmount">
        update
            t_corp
        set
            `renewReminderAmount` = #{amount},
            isSendReminderEmail = 0
        where
            id = #{corpId}
    </update>

    <update id="setCorpFullInvoicing">
        update
        t_corp
        set
        fullInvoicing = #{fullInvoicing}
        where
        id = #{corpId}
    </update>

</mapper>