package com.cdz360.biz.auth.utils;

import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.service.SysUserService;
import org.apache.shiro.SecurityUtils;
import org.springframework.stereotype.Component;

@Component
public class SysUserUtil {
    private static SysUserService userService;

    public SysUserUtil(SysUserService sysUserService) {
        userService = sysUserService;
    }

    public static SysUser cur() {
        if (SecurityUtils.getSubject() == null) return null;
        if (SecurityUtils.getSubject().getPrincipals() == null) return null;
        Object primaryPrincipal = SecurityUtils.getSubject().getPrincipals().getPrimaryPrincipal();
        return (SysUser) primaryPrincipal;
    }

    public static Long curUserId() {
        SysUser cur = cur();
        if (cur != null) {
            return cur.getId();
        }
        return null;
    }

    public static String nameById(Long id) {
        if (id == null) return null;
        String username = userService.queryUsernameById(id);
        return username;
    }
}
