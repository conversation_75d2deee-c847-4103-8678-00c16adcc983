package com.cdz360.biz.auth.rest;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.ds.ro.zft.ZftRoDs;
import com.cdz360.biz.auth.model.constant.GlobalConst;
import com.cdz360.biz.auth.model.dto.TCommercialDto;
import com.cdz360.biz.auth.model.dto.UserCommericalDto;
import com.cdz360.biz.auth.model.exception.ComEditException;
import com.cdz360.biz.auth.model.param.AddUserGroupRequest;
import com.cdz360.biz.auth.model.param.TCommercialRequest;
import com.cdz360.biz.auth.model.type.ErrStatus;
import com.cdz360.biz.auth.model.vo.*;
import com.cdz360.biz.auth.service.*;
import com.cdz360.biz.auth.utils.IotAssert;
import com.cdz360.biz.model.order.type.OrderPayType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/data/commercials")
public class TCommercialController implements TCommercialInterface {
    @Autowired
    TCommercialService tCommercialService;
    @Autowired
    TOperateBaseInfoService tOperateBaseInfoService;
    @Autowired
    SysUserService sysUserService;
    @Autowired
    SysUserRoleService userRoleService;

    @Autowired
    AuthorityService authorityService;

    @Autowired
    private TCommercialManageService tCommercialManageService;

    @Autowired
    private ZftRoDs zftRoDs;

    @PostMapping("/user")
    public Rez<SysUserComercial> addUserCommercials(@RequestBody SysUserComercial uc) {

        boolean insert = tCommercialService.insertUserCommercials(uc);
        return Rez.hasModified(insert, uc);
    }

    @GetMapping("/userComercial/{userId}")
    public Rez<SysUserComercial> userCommercials(@PathVariable("userId") Long userId) {

        return Rez.ofNullable(tCommercialService.getUserCommercials(userId));
    }


    @GetMapping("/user/{commercialId}")
    public Rez<List<SysUser>> commercialsUsers(UserCommericalDto ucd) {
        List<SysUser> users = tCommercialService.selectUserByCommercialsId(ucd);
        return Rez.ofNullable(users);
    }


    @GetMapping("/user/{commercialId}/current")
    public Rez<List<SysUser>> commercialsUsersCurrent(UserCommericalDto ucd) {
        List<SysUser> users = tCommercialService.selectUserByCommercialsId(ucd);
        return Rez.ofNullable(users);
    }


    @PostMapping(value = "/user_list/current")
    public Rez<List<SysUser>> commercialsfromUsersCurrent(@RequestBody UserCommericalDto ucd) {
        TCommercial tc = tCommercialService.selectByUserId(ucd.getUserId());
        if (tc == null) {
            return Rez.error(ErrStatus.COM_NOT_EXIST);
        }
        ucd.setCommercialId(tc.getId());
        return commercialsUsersCurrent(ucd);
    }


    @PostMapping(value = "/user_list")
    public Rez<List<SysUser>> commercialsfromUsers(@RequestBody UserCommericalDto ucd) {
        TCommercial tc = tCommercialService.selectByUserId(ucd.getUserId());
        if (tc == null) {
            return Rez.error(ErrStatus.COM_NOT_EXIST);
        }
        ucd.setCommercialId(tc.getId());
        return commercialsUsers(ucd);
    }

    @Deprecated
    @PostMapping
    public Rez<TCommercial> add(@RequestBody TCommercial entity) {
        log.info("新增商户 entity = {}", entity);
        Rez insert = null;
        try {
            return tCommercialService.insertNewCom(entity);
        } catch (ComEditException.UserException e) {
            e.printStackTrace();
            return Rez.error(1, "user error");
        } catch (ComEditException.ComException e) {
            e.printStackTrace();
            return Rez.error(2, "com error");
        } catch (ComEditException.UserComException e) {
            e.printStackTrace();
            return Rez.error(1, "user com error");
        }
    }



    @GetMapping("{id}")
    public Rez<TCommercial> findById(@PathVariable Long id) {
        TCommercial entity = tCommercialService.selectById(id);
        if (entity != null) {
            if (entity.getOperateBaseInfoId() != null) {
                entity.setTOperateBaseInfo(tOperateBaseInfoService.getBaseMapper().selectById(entity.getOperateBaseInfoId()));
            } else {
                entity.setTOperateBaseInfo(null);
            }
            if (entity.getPid() != null) {
                entity.setParent(tCommercialService.selectById(entity.getPid()));
            }

            // 此处来自于另一张表的数据，用于实现发票开关和服务电话的功能
            TCommercialManage tCommercialManage = tCommercialManageService.getTCommercialManage(id);
            if (tCommercialManage != null) {
                entity.setInvoinceEnabled(!com.cdz360.base.utils.StringUtils.isBlank(tCommercialManage.getInvoinceUrl()));
                entity.setServiceTel(tCommercialManage.getServiceTel());
                entity.setHasManage(Boolean.TRUE);
            } else {
                entity.setHasManage(Boolean.FALSE);
            }

            entity.setLeastPrivilegesCommLevel(tCommercialService.getLeastPrivilegesCommLevel(entity.getIdChain()));

            // 直付商户
            tCommercialService.initZftInfo(entity);

            if (com.cdz360.base.utils.StringUtils.isNotBlank(entity.getHlhtSitePayType())) {
                entity.setHlhtSitePayTypeList(Arrays.stream(entity.getHlhtSitePayType().split(","))
                        .map(OrderPayType::getValueByName).collect(Collectors.toList()));
            }
        }

        return Rez.ofNullable(entity);
    }


    @PostMapping("/multi")
    public Rez getMultiTCommer(@RequestBody TCommercial tCommercial) {
        if (CollectionUtils.isEmpty(tCommercial.getIds()) && com.cdz360.base.utils.StringUtils.isBlank(tCommercial.getIdChain())) {
            return Rez.data(null);
        }
        if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(tCommercial.getIds())) {
            return Rez.data(tCommercialService.getBaseMapper().selectBatchIds(tCommercial.getIds()));
        } else {
            return Rez.data(this.tCommercialService.getCommListByIdChain(tCommercial.getIdChain()));
        }
    }

    @GetMapping("/user/info/{userId}")
    public Rez<TCommercial> findByUserId(@PathVariable Long sysUid) {
        if (sysUid == null) {
            return Rez.error(ErrStatus.USER_NOT_EXIST);
        }
        TCommercial entity = tCommercialService.selectByUserId(sysUid);
        log.info("~> entity:{}", JsonUtils.toJsonString(entity));
        if (entity == null) {
            log.warn("商户信息不存在. sysUid = {}", sysUid);
        } else if (entity != null && entity.getOperateBaseInfoId() != null) {
            QueryWrapper<TOperateBaseInfo> wrapper = Wrappers.query();
            wrapper.eq("id", entity.getOperateBaseInfoId()).eq("status", GlobalConst.NORMAL);
            TOperateBaseInfo tOperateBaseInfo = tOperateBaseInfoService.getBaseMapper().selectOne(wrapper);
            entity.setTOperateBaseInfo(tOperateBaseInfo);
        }
        return Rez.ofNullable(entity);
    }

    @GetMapping("/user/sub/{userId}")
    public Rez<TCommercial> userSub(@PathVariable Long userId) {
        log.info("userId = {}", userId);
        if (userId == null) {
            return Rez.error(ErrStatus.USER_NOT_EXIST);
        }
        TCommercial entity = tCommercialService.selectYoungerByUserId(userId);
        return Rez.ofNullable(entity);
    }


    @GetMapping("/user/sup/{userId}")
    public Rez<TCommercial> userSup(@PathVariable Long userId) {
        if (userId == null) {
            return Rez.error(ErrStatus.USER_NOT_EXIST);
        }
        TCommercial entity = tCommercialService.selectParentByUserId(userId);
        return Rez.ofNullable(entity);
    }


    @PostMapping("/findByCommIdListAndName")
    public ListResponse<Commercial> findByCommIdListAndName(@RequestBody TCommercialRequest request) {
        log.info("request: {}", JsonUtils.toJsonString(request));
        return new ListResponse<>(tCommercialManageService.findByCommIdListAndName(request));
    }

    @RequestMapping("/page")
    public ListResponse<TCommercial> page(@RequestBody TCommercialDto param) {
        log.info("param = {}", param);
        param.setSizeIfNull(10, 10000);
        ListResponse<TCommercial> page = tCommercialService.selectPage(param);
//        ListResponse<TCommercial> res = new ListResponse<TCommercial>(page.getRecords(), page.getTotal());
        return page;
    }

    /**
     * 资金周转 接口
     *
     * @param commId
     * @return
     */

    @RequestMapping("/listByCode")
    public ListResponse<CommercialVo> listByCode(Long commId) {
        List<CommercialVo> list = null;
        if (commId != null && commId != 0) {
            TCommercial tc = tCommercialService.selectById(commId);
            if (tc != null) {
                list = Arrays.asList(this.getCommericalVo(tc));
            }
        } else {
            List<TCommercial> tCommercials = tCommercialService.getBaseMapper().selectList(null);
            if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(tCommercials)) {
                list = tCommercials.stream().map(tc -> this.getCommericalVo(tc)).collect(Collectors.toList());
            }
        }
        ListResponse<CommercialVo> res = new ListResponse<>(list);
        return res;
    }

    public CommercialVo getCommericalVo(TCommercial tc) {
        // 顶级商户
//        TCommercial topTc = this.tCommercialService.getMaxCommercialByCommId(tc.getId());
        return new CommercialVo()
                .setCode(tc.getId())
                .setGroupCode(tc.getTopCommId())
                .setOrgLevel(tc.getCommLevel())
                .setName(tc.getCommName())
                .setShortName(tc.getShortName())
                .setContacts(tc.getContacts())
                .setContactPhone(tc.getPhone())
                .setEmail(tc.getEmail())
                .setOrgCode(tc.getLicense())
                .setParentCode(tc.getPid())
                .setBusinessStatus(tc.getBusinessStatus());
    }


    @PostMapping("/user/page")
    public ListResponse<TCommercialUser> findByUserPageByCommercialUser(@RequestBody UserCommericalDto ucd) {
        TCommercial tc = tCommercialService.selectByUserId(ucd.getUserId());
        ListResponse<TCommercialUser> res = new ListResponse<TCommercialUser>();
        if (tc == null) {
            res.setStatus(ErrStatus.COM_NOT_EXIST.getErrcode());
            res.setError(ErrStatus.COM_NOT_EXIST.getErrmsg());
            return res;
        }
        ucd.setCommercialId(tc.getId());
        ListResponse<TCommercialUser> result = tCommercialService.selectByUserPageByCommercialUser(ucd);
        return result;
    }


    @PostMapping("/user/edit")
    public Rez editUser(@RequestBody TCommercialUser tcu) {

        List<SysUser> suList = sysUserService.checkByUserNameOrPhoneOrEmail(tcu);

        if (!CollectionUtils.isEmpty(suList)) {
            for (SysUser su : suList) {
                if (NumberUtils.equals(su.getId(), tcu.getId())) {
                    continue;
                }
                if (tcu.getUsername().equals(su.getUsername())) {
                    log.warn("username 冲突. tcu.id = {}, su.id = {}, su.username = {}",
                            tcu.getId(), su.getId(), su.getUsername());
                    return Rez.error(ErrStatus.USERNAME_CONFLICT);
                }
                if (StringUtils.isNotBlank(tcu.getEmail()) &&
                        tcu.getEmail().equals(su.getEmail())) {
                    return Rez.error(ErrStatus.EMAIL_CONFLICT);
                }
            }
        }


        if (StringUtils.isNotEmpty(tcu.getNewPass())) {

            if(StringUtils.isNotBlank(tcu.getChangePass())) {
                // 检查旧密码是否正确
                tcu.setPassword(tcu.getChangePass());
                SysUser sysUser = sysUserService.storedUser(tcu);
                IotAssert.isNotNull(sysUser, "用户信息有误，请确认后再尝试。");
                IotAssert.isTrue(sysUser.hashedPassword(tcu.getChangePass()).equalsIgnoreCase(sysUser.getPassword()),
                        "原密码不正确");
            }

            tcu.setPassword(tcu.getNewPass());
            tcu.setPassword(tcu.hashedPassword());
        }
        tcu.setCommercialRole(null);
        boolean updateUser = sysUserService.updateById(tcu);
        if (tcu.getCommercialRole() != null) {
            SysUserComercial suc = new SysUserComercial();
            suc.setUserId(tcu.getId());
            suc.setCommercialRole(tcu.getCommercialRole());
            tCommercialService.updateUserCommercials(suc);
        }
        return Rez.ok();
    }


    @PostMapping("/user/add")
    public Rez addUser(@RequestBody TCommercialUser tcu) {
        log.info("新增用户. tcu = {}", JsonUtils.toJsonString(tcu));
        List<SysUser> sysUsers = sysUserService.checkByUserNameOrPhoneOrEmail(tcu);

        if (!CollectionUtils.isEmpty(sysUsers)) {
            for (SysUser su : sysUsers) {
                if (tcu.getUsername().equals(su.getUsername())) {
                    return Rez.error(ErrStatus.USERNAME_CONFLICT);
                }
                if (com.cdz360.base.utils.StringUtils.isNotBlank(tcu.getEmail()) &&
                        tcu.getEmail().equals(su.getEmail())) {
                    return Rez.error(ErrStatus.EMAIL_CONFLICT);
                }
//                if (tcu.getPhone().equals(su.getPhone())) {
//                    return Rez.error(ErrStatus.PHONE_CONFLICT);
//                }
            }
            return Rez.error(ErrStatus.NAME_CONFLICT);
        }
        if (!StringUtils.isEmpty(tcu.getNewPass())) {
            tcu.setPassword(tcu.getNewPass());
            tcu.setPassword(tcu.hashedPassword());
        }
        Long commId = tcu.getCommId();
        if (commId == null) {
            commId = tcu.getComId();
        }
        tcu.setCommId(commId);
        if (commId != null) {
            TCommercial comm = this.tCommercialService.selectById(commId);
            if (comm != null) {
                tcu.setTopCommId(comm.getTopCommId());
            }
        }
        boolean addUser = sysUserService.addUser(tcu);
        if (addUser) {
            SysUserComercial uc = new SysUserComercial();
            uc.setUserId(tcu.getId());
            uc.setCommercialId(tcu.getComId());
            uc.setCommercialRole(tcu.getCommercialRole());
            boolean insert = tCommercialService.insertUserCommercials(uc);
            SysUserRole ur = new SysUserRole();
            ur.setRoleId(2020l);
            ur.setUserId(tcu.getId());
            insert = userRoleService.getBaseMapper().insert(ur) > 0;
            {
                AddUserGroupRequest param = new AddUserGroupRequest();
                param.setUserId(ur.getUserId());
                param.setAuthGroupIds(tcu.getAuthGroupIds());
                param.setOpId(0L);
                param.setOpName("");
                this.authorityService.modifyUserGroupRef(param);
            }
            return Rez.hasModified(insert, uc);
        }
        return Rez.error(ErrStatus.RES_NOT_MODIFY);
    }


    @GetMapping("/userinfo/{userId}")
    public Rez<TCommercialUser> commercialsUser(@PathVariable("userId") Long userId) {
        SysUserComercial suc = tCommercialService.getUserCommercials(userId);
        if (suc == null) return Rez.error(ErrStatus.RES_NOT_FOUND);
        SysUser sysUser = sysUserService.getBaseMapper().selectById(userId);
        //根据userId获取角色名称
        List<String> roleName = userRoleService.findRoleNameListByUserId(userId);
        //角色列表转字符串
        String roleNameList = StringUtils.join(roleName.toArray(), ",");
        // 处理 null
        if (sysUser == null) return Rez.error(ErrStatus.RES_NOT_FOUND);
        TCommercialUser tcu = new TCommercialUser();
        tcu.setRoleNameList(roleNameList);
        tcu.setCommercialRole(suc.getCommercialRole());
        BeanUtils.copyProperties(sysUser, tcu);
        return Rez.ofNullable(tcu);
    }

    @GetMapping("/manage/{comId}")
    public Rez commercialsManage(@PathVariable("comId") Long comId) {
        return Rez.data(tCommercialService.selectManageByComId(comId));
    }


    public ObjectResponse<Long> commercialsManage(String appId) {
        return new ObjectResponse<>(tCommercialService.selectManageByWXAppId(appId));
    }

    @GetMapping("/user/getMaxCommercial/{commId}")
    public ObjectResponse<TCommercial> getMaxCommercialByCommId(@PathVariable("commId") Long commId) {
        return RestUtils.buildObjectResponse(tCommercialService.getMaxCommercialByCommId(commId));
    }

    @GetMapping("/user/getChildByCommId/{commId}")
    public Rez getChildByCommId(@PathVariable("commId") Long commId) {
        return Rez.data(tCommercialService.getChildByCommId(commId));
    }

    @GetMapping("/user/getByCommlevel/{commLevel}")
    public Rez getByCommlevel(@PathVariable("commLevel") Integer commLevel) {
        return Rez.data(tCommercialService.getByCommlevel(commLevel));
    }

    @PostMapping("/publishAllCommercialInfo")
    public BaseResponse publishAllCommercialInfo() {
        log.warn("同步所有的商户信息开始");
        this.tCommercialService.publishAllCommercialInfo();
        log.warn("同步所有的商户信息w完成");
        return BaseResponse.newInstance();
    }

    /**
     * 获取系统用户所属商户的层级
     *
     * @param userId 系统用户Id
     * @return 1是集团商户，2是商户，3是子商户
     */
    @GetMapping("/user/commercialLevel/{userId}")
    ObjectResponse<Integer> userCommercialLevel(@PathVariable("userId") Long userId) {
        log.info("获取系统用户商户层级");
        SysUserComercial userComm = tCommercialService.getUserCommercials(userId);
        if (null == userComm || userComm.getCommercialId() == null) {
            log.info("用户非法: userId={}", userId);
            throw new DcArgumentException("用户非法");
        }

        TCommercial tCommercial = this.tCommercialService.selectById(userComm.getCommercialId());
        log.info("comm level={}", tCommercial.getCommLevel());
        return RestUtils.buildObjectResponse(tCommercial.getCommLevel());
    }
}
