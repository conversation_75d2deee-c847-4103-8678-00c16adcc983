package com.cdz360.biz.auth.service.oauth;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;


@Slf4j
public class ShiroHttpServiceImpl extends ShiroHttpService {

    private RestTemplate restTemplate;


    public void setRestTemplate(RestTemplate restTemplate) {
        log.info("setRestTemplate");
        this.restTemplate = restTemplate;
    }

    @Override
    public Set<String> roleSet(Long userId, String token) {
        log.info("roleSet");
        String url = String.format("http://%s/data/roles/names/user/%d", authUrl, userId);

        ResponseEntity<String> forEntity = getForEntity(url, token, String.class);
        checkResponse(url, forEntity);
        String json = forEntity.getBody();
        Set<String> stringSet = JsonUtils.fromJson(json, new TypeReference<Set<String>>() {
        });
        return stringSet;
    }

    @Override
    public Set<String> permSet(Long userId, String token) {
        log.info("permSet");
        String url = String.format("http://%s/data/menus/%d/perms", authUrl, userId);
        ResponseEntity<String> forEntity = getForEntity(url, token, String.class);
        checkResponse(url, forEntity);
        Set<String> stringSet = JsonUtils.fromJson(forEntity.getBody(),
            new TypeReference<Set<String>>() {
            });
        return stringSet;
    }


    public <T> ResponseEntity<T> getForEntity(String url, String token, Class<T> tClass) {
        log.info("getForEntity");
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add("token", token);
        HttpEntity<String> stringHttpEntity = new HttpEntity<>(null, httpHeaders);
        ResponseEntity<T> exchange = restTemplate.exchange(url, HttpMethod.GET, stringHttpEntity,
            tClass);
        return exchange;
    }

}
