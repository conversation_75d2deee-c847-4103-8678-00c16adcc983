package com.cdz360.biz.auth.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class PageDto extends DataScopeDto {

    private long page = 1L;
//    private long size = 10L;


    @Schema(description = "归属平台", example = "20-充电管理，21-运营支撑，22-企业客户")
    private Long platform;

    private String keyword;

    private int status;

    private int commId;

    private Long corpId;

    @Schema(description = "查询限制")
    @JsonInclude(Include.NON_NULL)
    private Boolean queryLimit;

    public void setPage(int page) {
        if (page > 1)
            this.page = page;
    }

//    public void setSize(int size) {
//        if (size > 0)
//            this.size = size;
//    }

//    public boolean getStatus() {
//        if (status>0) {
//            return  true;
//        } else {
//            return false;
//        }
//    }

    public void setRows(int row) {
        setSize(row);
    }
}
