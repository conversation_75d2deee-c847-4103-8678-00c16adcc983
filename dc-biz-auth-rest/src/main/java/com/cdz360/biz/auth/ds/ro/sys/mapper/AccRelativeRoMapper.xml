<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.ds.ro.sys.mapper.AccRelativeRoMapper">

  <resultMap id="VOMAP" type="com.cdz360.biz.auth.sys.vo.AccRelativeVo">
    <result column="sysUid" jdbcType="BIGINT" property="sysUid"/>
    <result column="username" jdbcType="VARCHAR" property="userName"/>

    <result column="priority" property="priority"/>

    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="phone" jdbcType="VARCHAR" property="phone"/>
    <result column="status" jdbcType="INTEGER" property="status"/>
    <result column="work" jdbcType="TINYINT" property="work"/>
    <result column="commId" jdbcType="BIGINT" property="commId"/>
    <result column="commName" jdbcType="VARCHAR" property="commName"/>
    <result column="siteIdList" property="siteIdList"
      typeHandler="com.cdz360.biz.auth.ds.MybatisJsonTypeHandler"/>
    <result column="xjOrderNum" jdbcType="BIGINT" property="xjOrderNum"/>
    <result column="ywOrderNum" jdbcType="BIGINT" property="ywOrderNum"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="opName" jdbcType="VARCHAR" property="opName"/>

    <collection property="relUserNameList" ofType="com.cdz360.biz.auth.sys.vo.RelAccount">
      <result column="relSysUid" jdbcType="BIGINT" property="sysUid"/>
      <result column="relUsername" jdbcType="VARCHAR" property="userName"/>
      <result column="relCommName" jdbcType="VARCHAR" property="commName"/>
      <result column="relPriority" jdbcType="INTEGER" property="priority"/>
      <result column="relSiteIdList" property="siteIdList"
        typeHandler="com.cdz360.biz.auth.ds.MybatisJsonTypeHandler"/>
    </collection>
  </resultMap>

  <select id="getVoList" parameterType="com.cdz360.biz.auth.sys.param.AccRelativeParam"
    resultMap="VOMAP">
    select
    a.*,
    relU.id as relSysUid,
    relU.username as relUsername
    from
    (
    select
    acc.sysUid ,
    acc.relativeCode ,
    accU.username ,
    accU.name ,
    accU.phone ,
    acc.`work` ,
    acc.`priority` ,
    accU.commId ,
    comm.comm_name as commName ,
    acc.siteIdList ,
    acc.xjOrderNum ,
    acc.ywOrderNum ,
    acc.createTime ,
    acc.updateOpName as opName
    from
    t_acc_relative acc
    left join sys_user accU on
    accU.id = acc.sysUid
    left join t_commercial comm on
    comm.id = accU.commId
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( gidList )">
      left join t_site_group_user_ref sgu on sgu.uid = acc.sysUid
      left join t_site_group sg on sg.gid = sgu.gid
    </if>
    where
    acc.enable is true
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and comm.`idChain` like concat(#{commIdChain}, '%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( name )">
      and accU.name like CONCAT('%', #{name}, '%')
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
      <!--                and json_contains(acc.siteIdList, json_array-->
      <!--                <foreach collection="siteIdList" open="(" close=")"-->
      <!--                         separator="," item="siteId">-->
      <!--                    #{siteId}-->
      <!--                </foreach>-->
      <!--                )-->
      <foreach collection="siteIdList" open="and (" close=")"
        separator="or" item="siteId">
        acc.siteIdList like CONCAT('%', #{siteId}, '%')
      </foreach>
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( gidList )">
      <foreach collection="gidList" open="and sg.gid in (" close=")"
        separator="," item="gid">
        #{gid}
      </foreach>
    </if>
    <if test="commId != null">
      and accU.commId = #{commId}
    </if>
    order by
    acc.createTime desc
    <if test="start != null and size != null">
      limit #{start}, #{size}
    </if>
    ) a
    left join t_acc_relative rel on
    a.relativeCode = rel.relativeCode
    and a.sysUid != rel.sysUid
    left join sys_user relU on
    rel.sysUid = relU.id
    order by
    a.createTime desc
  </select>

  <select id="getVoListCount" parameterType="com.cdz360.biz.auth.sys.param.AccRelativeParam"
    resultType="java.lang.Long">
    select
    count(acc.sysUid)
    from
    t_acc_relative acc
    left join sys_user accU on
    accU.id = acc.sysUid
    left join t_commercial comm on
    comm.id = accU.commId
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( gidList )">
      left join t_site_group_user_ref sgu on sgu.uid = acc.sysUid
      left join t_site_group sg on sg.gid = sgu.gid
    </if>
    where
    acc.enable is true
    and acc.siteIdList is not null
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and comm.`idChain` like concat(#{commIdChain}, '%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( name )">
      and accU.name like CONCAT('%', #{name}, '%')
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( siteIdList )">
      <!--                and json_contains(acc.siteIdList, json_array-->
      <!--                <foreach collection="siteIdList" open="(" close=")"-->
      <!--                         separator="," item="siteId">-->
      <!--                    #{siteId}-->
      <!--                </foreach>-->
      <!--                )-->
      <foreach collection="siteIdList" open="and (" close=")"
        separator="or" item="siteId">
        acc.siteIdList like CONCAT('%', #{siteId}, '%')
      </foreach>
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( gidList )">
      <foreach collection="gidList" open="and sg.gid in (" close=")"
        separator="," item="gid">
        #{gid}
      </foreach>
    </if>
    <if test="commId != null">
      and accU.commId = #{commId}
    </if>
  </select>

  <select id="getValidSysUid" resultType="java.lang.Long">
    select
    sysUid
    from
    t_acc_relative
    where
    enable = 1
    and `work` = 1
    and siteIdList is not null
  </select>

  <select id="getByUsername" resultMap="VOMAP">
    select
    a.*,
    relU.id as relSysUid,
    relU.username as relUsername,
    comm.comm_name as relCommName
    from
    (
    select
    accU.id as sysUid ,
    acc.relativeCode ,
    accU.username ,
    accU.name ,
    accU.phone ,
    accU.status ,
    acc.`work` ,
    acc.`priority` ,
    accU.commId ,
    comm.comm_name as commName ,
    acc.siteIdList ,
    acc.xjOrderNum ,
    acc.ywOrderNum ,
    acc.createTime ,
    acc.updateOpName as opName
    from
    sys_user accU
    left join t_acc_relative acc on
    accU.id = acc.sysUid
    left join t_commercial comm on
    accU.commId = comm.id
    where
    accU.platform = #{platform}
    and accU.username = #{username}
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and comm.`idChain` like concat(#{commIdChain}, '%')
    </if>
    limit 1 ) a
    left join t_acc_relative rel on
    a.relativeCode = rel.relativeCode
    and a.sysUid != rel.sysUid
    and a.relativeCode != ''
    left join sys_user relU on
    rel.sysUid = relU.id
    left join t_commercial comm on relU.commId = comm.id
  </select>

  <select id="getRelUserList" resultType="com.cdz360.biz.auth.sys.vo.RelAccount">
    select
    relU.id as sysUid,
    relU.username as userName
    from
    t_acc_relative rel
    left join t_acc_relative a on
    a.relativeCode = rel.relativeCode
    and a.sysUid != rel.sysUid
    left join sys_user relU on
    rel.sysUid = relU.id
    where
    a.sysUid = #{sysUid}
    and a.`work` = 1
    and a.enable = 1
  </select>

  <select id="getRelUser" resultType="com.cdz360.biz.auth.sys.vo.RelAccount">
    select
    relU.id as sysUid,
    relU.username as userName
    from
    t_acc_relative a
    left join t_acc_relative rel on
    a.relativeCode = rel.relativeCode
    and a.sysUid != rel.sysUid
    left join sys_user relU on
    rel.sysUid = relU.id
    where
    a.sysUid = #{sysUid}
    and a.`work` = 1
    and a.enable = 1
    and rel.sysUid = #{refSysUid}
  </select>
  <select id="getAccRelative"
    resultMap="VOMAP">
    select
    u.id as sysUid ,
    u.username ,
    u.name ,
    u.phone ,
    u.status ,
    u.commId ,
    comm.comm_name commName,
    rel.relativeCode ,
    rel.`work` ,
    rel.siteIdList ,
    rel.xjOrderNum ,
    rel.ywOrderNum ,
    rel.createTime ,
    rel.siteIdList,
    rel.updateOpName opName,
    uRel.id relSysUid,
    uRel.username relUsername,
    aRel.priority relPriority,
    aRel.siteIdList relSiteIdList
    from t_acc_relative rel
    left join sys_user u on u.id = rel.sysUid
    left join t_commercial comm on u.commId = comm.id
    left join t_acc_relative aRel on aRel.relativeCode = rel.relativeCode and aRel.sysUid !=
    rel.sysUid
    and aRel.`work` = 1
    and aRel.enable = 1
    left join sys_user uRel on uRel.id = aRel.sysUid
    where rel.sysUid = #{sysUid}
    <if test="valid">
      and u.status = 1
      and rel.`work` = 1
      and rel.enable = 1
    </if>
  </select>

  <select id="getAccRelativeBySiteId"
    resultMap="VOMAP">
    select
    u.id as sysUid ,
    u.username ,
    u.name ,
    u.phone ,
    u.status ,
    u.commId ,
    comm.comm_name commName,
    rel.relativeCode ,
    rel.`work` ,
    rel.siteIdList ,
    rel.xjOrderNum ,
    rel.ywOrderNum ,
    rel.createTime ,
    rel.updateOpName opName
    from t_acc_relative rel
    left join sys_user u on u.id = rel.sysUid
    left join t_commercial comm on u.commId = comm.id
    where rel.siteIdList like CONCAT('%', #{siteId}, '%')
    and u.status = 1
    and rel.`work` = 1
    and rel.enable = 1
    order by rel.priority desc
    limit 1
  </select>

  <select id="getAccRelativeVoBySysUid" resultMap="VOMAP">
    select
    u.id as sysUid ,
    u.username ,
    u.name ,
    u.phone ,
    u.status ,
    u.commId ,
    comm.comm_name commName,
    rel.relativeCode ,
    rel.`work` ,
    rel.siteIdList ,
    rel.xjOrderNum ,
    rel.ywOrderNum ,
    rel.createTime ,
    rel.updateOpName opName
    from sys_user u
    left join t_acc_relative rel on u.id = rel.sysUid
    left join t_commercial comm on u.commId = comm.id
    where u.id = #{sysUid}
    and u.status = 1
    and rel.`work` = 1
    and rel.enable = 1
    order by rel.priority desc
    limit 1
  </select>

  <select id="getAccRelativeByCommId"
    resultMap="VOMAP">
    select
    u.id as sysUid ,
    u.username ,
    u.name ,
    u.phone ,
    u.status ,
    u.commId ,
    comm.comm_name commName,
    rel.relativeCode ,
    rel.`work` ,
    rel.siteIdList ,
    rel.xjOrderNum ,
    rel.ywOrderNum ,
    rel.createTime ,
    rel.updateOpName opName
    from t_acc_relative rel
    left join sys_user u on u.id = rel.sysUid
    left join t_commercial comm on u.commId = comm.id
    where comm.id = #{commId}
    and u.status = 1
    and rel.`work` = 1
    and rel.enable = 1
    and rel.siteIdList is not null
    order by rel.priority desc
    limit 1
  </select>

  <select id="getAccRelativeByCommIdChain"
    resultMap="VOMAP">
    select
    u.id as sysUid ,
    u.username ,
    u.name ,
    u.phone ,
    u.status ,
    u.commId ,
    comm.comm_name commName,
    rel.relativeCode ,
    rel.`work` ,
    rel.siteIdList ,
    rel.xjOrderNum ,
    rel.ywOrderNum ,
    rel.createTime ,
    rel.updateOpName opName
    from t_acc_relative rel
    left join sys_user u on u.id = rel.sysUid
    left join t_commercial comm on u.commId = comm.id
    left join t_commercial chainComm on chainComm.id = #{commId}
    where chainComm.idChain like CONCAT(comm.idChain, '%')
    and u.status = 1
    and rel.`work` = 1
    and rel.enable = 1
    and rel.siteIdList is not null
    order by comm.idChain desc
    limit 1
  </select>


</mapper>