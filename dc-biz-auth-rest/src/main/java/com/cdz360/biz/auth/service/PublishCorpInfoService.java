package com.cdz360.biz.auth.service;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.corp.dto.CorpSyncDto;
import com.cdz360.biz.auth.ds.ro.corp.mapper.CorpRoMapper;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.data.sync.service.DcEventPublisher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * PublishCorpOrgInfoService
 *
 * @since 1/10/2020 4:53 PM
 * <AUTHOR>
 */
@Slf4j
@Service
public class PublishCorpInfoService {

    @Autowired
    private DcEventPublisher dcEventPublisher;

    @Autowired
    private CorpRoMapper corpRoMapper;

    /**
     * 根据corpId推送企业信息
     *
     * @param corpId
     */
    public void publishCorpInfo(Long corpId) {
        publishCorpInfo(corpId, null);
    }

    public void publishCorpInfo(Long corpId, IotEvent event) {
        if (corpId != null) {
            this.PublishCorpInfo(buildCorpSyncDto(corpRoMapper.getCorpById(corpId), event));
        }
    }

    /**
     * 组织企业信息
     *
     * @param in
     * @return
     */
    private CorpSyncDto buildCorpSyncDto(CorpPo in, IotEvent event) {
        if (in == null) {
            return null;
        } else {
            CorpSyncDto out = new CorpSyncDto();
            BeanUtils.copyProperties(in, out);

            if (IotEvent.CREATE.equals(event)) {
                out.setOpId(in.getCreatorId())
                    .setOpName(in.getCreatorName());
            }

            return out;
        }
    }

    /**
     * 发送企业信息
     *
     * @param corpOrgIn
     */
    public void PublishCorpInfo(CorpSyncDto corpOrgIn) {
        if (corpOrgIn == null) {
            log.error("企业信息不存在. corpOrgIn = {}", (Object) null);
            return;
        }
        this.dcEventPublisher.publishCorpInfo(corpOrgIn);
    }

}