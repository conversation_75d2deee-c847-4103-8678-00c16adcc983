<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cdz360.biz.auth.dao.SysDictDao">
    <select id="findByPcodeAndCode" resultType="com.cdz360.biz.auth.model.vo.SysDict">
        SELECT d.*
        FROM sys_dict d JOIN sys_dict p
        ON d.pid=p.id
        WHERE d.code=#{code} AND p.code=#{pcode}
    </select>
    <select id="findByPcode" resultType="com.cdz360.biz.auth.model.vo.SysDict">
        SELECT d.*
        FROM sys_dict d JOIN sys_dict p
        ON d.pid=p.id
        WHERE p.code=#{pcode}
    </select>

    <select id="getGcSiteTypes" resultType="java.lang.String">
        select siteIds from t_bi_site_ref where typeCode = "运营gcType类型"
    </select>
</mapper>