package com.cdz360.biz.auth.service;


import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.auth.dao.TCommercialDao;
import com.cdz360.biz.auth.ds.ro.corp.CorpRoDs;
import com.cdz360.biz.auth.ds.ro.message.MessageRoDs;
import com.cdz360.biz.auth.ds.rw.message.MessageRwDs;
import com.cdz360.biz.auth.service.msg.MsgSendService;
import com.cdz360.biz.auth.utils.IotAssert;
import com.cdz360.biz.model.cus.message.param.ListMessageParam;
import com.cdz360.biz.model.cus.message.po.MessagePo;
import com.cdz360.biz.model.cus.message.type.PlatformType;
import com.cdz360.biz.model.cus.message.type.BroadCastType;
import com.cdz360.biz.model.cus.message.vo.MessageVo;
import com.cdz360.biz.model.cus.message.vo.UserMessageVo;
import com.google.common.base.Joiner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

import java.util.List;


/**
 * 站内信
 * <AUTHOR>
 */
@Slf4j
@Service
public class MessageBizService {

    @Autowired
    private MessageRwDs messageRwDs;
    @Autowired
    private MessageRoDs messageRoDs;
    @Autowired
    private TCommercialDao tCommercialDao;

    @Autowired
    private MsgSendService msgSendService;

    @Autowired
    private CorpRoDs corpRoDs;

    private int platformCount = 2;

    /**
     * 添加消息
     * @param message
     * @return
     */
    public int addMessage(MessagePo message)  {
        log.info("消息内容,message={}", JsonUtils.toJsonString(message));
        try{
            IotAssert.isNotNull(message.getTitle(),"消息标题不能为空");
            IotAssert.isTrue(message.getTitle().length()<=40,"标题最大长度40个字符");
            IotAssert.isNotNull(message.getMsgType(),"消息类型不能为空");
            IotAssert.isNotNull(message.getContent(),"消息内容不能为空");
            IotAssert.isNotNull(message.getPlatformList(),"请选择接收平台");
            IotAssert.isNotNull(message.getBroadcast(),"请选择接收对象");

            if (message.getBroadcast().equals(BroadCastType.PART)) {
                IotAssert.isNotNull(message.getCommIdList(),"商户信息不能为空");
            } else {
                List<Long> commIdList = this.tCommercialDao.getCommercialIdList();
                if (CollectionUtils.isNotEmpty(commIdList)) {
                    message.setCommIdList(commIdList);
                }
            }
            message.setCommIds(Joiner.on(",").join(message.getCommIdList()));

            if(message.getPlatformList().size() ==platformCount) {
                message.setPlatform(PlatformType.ALL);
            } else {
                message.setPlatform(message.getPlatformList().get(0));
            }

            messageRwDs.insertMessage(message);


            msgSendService.addEventMsg(message);
            msgSendService.notifyEvent();
            return 1;
        }catch (Exception e) {
            log.info("消息写入错误,errorMsg={}",e.getMessage(), e);
            throw new DcArgumentException("站内信添加失败");
        }
    }

    /**
     * 站内信撤回
     * @param updateBy
     * @param msgId
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int editMessage(Long updateBy,Long msgId){
        try{
            IotAssert.isNotNull(updateBy,"未登录状态");
            IotAssert.isNotNull(msgId,"消息Id不能为空");
            this.messageRwDs.editMessage(msgId);
            this.messageRwDs.editMsgLog(msgId);
            return 1;
        } catch (Exception e) {
            log.info("撤回失败,errorMsg={}",e.getMessage());
            throw new DcArgumentException("消息撤回失败");
        }
    }

    /**
     * 获取站内信详情
     * @param msgId
     * @return
     */
    public UserMessageVo getMessageById(Long msgId,Long uid,Integer platform) {
        try{
            IotAssert.isNotNull(msgId,"消息ID不能为空");
            IotAssert.isNotNull(uid,"未登录状态");
            UserMessageVo messageVo = this.messageRoDs.getMessageById(msgId,uid);
            //企业续费提醒
            if (messageVo.getCorpId() != null && platform.equals(PlatformType.MANAGE.getCode())) {
                CorpPo corpPo =  corpRoDs.getCorpById(messageVo.getCorpId());
                if (corpPo != null) {
                    StringBuilder tmpStr = new StringBuilder();
                    tmpStr.append(messageVo.getContent()).append("(").append(corpPo.getCorpName()).append(")").append("。");
                    messageVo.setContent(tmpStr.toString());
                }
            }
            if (messageVo != null && messageVo.getReadStatus().equals(1L)) {
               this.messageRwDs.updateMessageReadLog(msgId, uid);
            }
            return messageVo;
        } catch (Exception e){
            log.info("错误信息，err={}",e.getMessage());
            throw new DcArgumentException("获取消息错误");
        }
    }

    /**
     * 个人未读条数
     * @param uid
     * @param platform
     * @return
     */
    public int getUnReadCount (Long uid,Long platform) {
        return this.messageRoDs.getUnReadCount(uid,platform);
    }

    /**
     * 分页获取消息，限用在运营支撑平台
     * @param reqParam
     * @return
     */
    public ListResponse<MessageVo> getMsgList (ListMessageParam reqParam) {
        if (reqParam.getSize() == null) {
            reqParam.setSize(10);
        }
        if (reqParam.getStart() == null) {
            reqParam.setStart(0L);
        }
        return RestUtils.buildListResponse(this.messageRoDs.getMsgList(reqParam),this.messageRoDs.getMsgListCount(reqParam));
    }

    /**
     * 个人站内信列表
     * @param reqParam
     * @return
     */
    public ListResponse<UserMessageVo> getUserMsgList (ListMessageParam reqParam) {

        if (reqParam == null) {
            throw new DcArgumentException("请求参数错误");
        }
        if(reqParam.getPlatform() == null) {
            throw new DcArgumentException("请选择所属平台");
        }
        if (reqParam.getSize() == null) {
            reqParam.setSize(10);
        }
        if (reqParam.getStart() == null) {
            reqParam.setStart(0L);
        }

        return RestUtils.buildListResponse(this.messageRoDs.getUserMsgList(reqParam),this.messageRoDs.getUserMsgListCount(reqParam));
    }





}
