package com.cdz360.biz.auth.ds.ro.zft.mapper;



import com.cdz360.biz.auth.zft.param.ListZftParam;
import com.cdz360.biz.auth.zft.po.ZftPo;

import org.apache.ibatis.annotations.Mapper;

import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper

public interface ZftRoMapper {



	ZftPo getById(@Param("id") Long id);

	ZftPo getByTopCommId(@Param("topCommId") Long topCommId);

	List<ZftPo> findAll(ListZftParam param);

	Long count(ListZftParam param);

    Long hasEnableBalance(@Param("topCommId") Long topCommId,
						  @Param("excludeId") Long excludeId);

    Long hasName(@Param("topCommId") Long topCommId,
				 @Param("excludeId") Long excludeId,
				 @Param("name") String name);
}

