package com.cdz360.biz.auth.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.service.ZftService;
import com.cdz360.biz.auth.zft.dto.ZftDto;
import com.cdz360.biz.auth.zft.param.ListZftParam;
import com.cdz360.biz.auth.zft.vo.ZftVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Tag(name = "直付商户相关接口", description = "直付商户相关接口")
public class ZftRest {

    @Autowired
    private ZftService zftService;

    @Operation(summary = "获取直付商家列表")
    @PostMapping(value = "/api/zft/zftList")
    public ListResponse<ZftVo> zftList(
            HttpServletRequest request,
            @RequestBody ListZftParam param) {
        log.debug("获取直付商家列表: param = {}", JsonUtils.toJsonString(param));
        return zftService.zftList(param);
    }

    @Operation(summary = "获取直付商家的信息")
    @GetMapping(value = "/api/zft/getZft")
    public ObjectResponse<ZftVo> getZft(
            HttpServletRequest request,
            @Parameter(name = "直付商家ID", required = true) @RequestParam(value = "id") Long id) {
        log.debug("获取直付商家的信息: id = {}", id);
        return RestUtils.buildObjectResponse(zftService.getZft(id));
    }

    @Operation(summary = "更新直付商家信息")
    @PostMapping(value = "/api/zft/updateZft")
    public ObjectResponse<Long> updateZft(
            HttpServletRequest request,
            @RequestBody ZftDto dto) {
        log.debug("更新直付商家信息: dto = {}", JsonUtils.toJsonString(dto));
        return RestUtils.buildObjectResponse(zftService.updateZft(dto));
    }

    @Operation(summary = "获取顶级账户的直付信息")
    @GetMapping(value = "/api/zft/getZftByTopCommId")
    public ObjectResponse<ZftVo> getZftByTopCommId(
        HttpServletRequest request,
        @Parameter(name = "顶级商户ID", required = true) @RequestParam(value = "topCommId") Long topCommId) {
        log.debug("顶级商户ID: topCommId = {}", topCommId);
        return RestUtils.buildObjectResponse(zftService.getZftByTopCommId(topCommId));
    }
}
