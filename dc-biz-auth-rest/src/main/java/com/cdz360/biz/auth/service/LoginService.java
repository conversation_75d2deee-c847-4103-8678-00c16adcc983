package com.cdz360.biz.auth.service;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.utils.RedisUtils;
import com.cdz360.biz.auth.utils.TokenGenerator;
import java.util.Optional;
import java.util.Set;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class LoginService {

    public static final String USER2TOKEN_PREFIX = "authc:user-name-token";
    public static final String TOKEN2USER_PREFIX = "authc:token-user";
    private final long authExpire = 24L * 3600 * 7;
    @Autowired
    RedisUtils redisUtils;

    @Value("${auth.account.demo: ht-demo-account,<EMAIL>}")
    private Set<String> accountMutilLoginList;

    public String updateToken(SysUser user) {
        //企业管理平台   phone非必填
        String key = concat(USER2TOKEN_PREFIX, user.getPlatform(), user.getUsername());
        String token = redisUtils.get(key);
        if (token != null) {
            // 支持demo账号多端登录
            if (accountMutilLoginList.contains(user.getUsername())) {
                redisUtils.set(TOKEN2USER_PREFIX, token, user, authExpire);
                redisUtils.set(key, token, authExpire);
                log.debug("演示账号访问: {}, {}", user.getUsername(), accountMutilLoginList);
                return token;
            }

            this.redisUtils.delete(concat(TOKEN2USER_PREFIX, null, token));
        }
        token = TokenGenerator.generateValue();
        redisUtils.set(TOKEN2USER_PREFIX, token, user, authExpire);
        redisUtils.set(key, token, authExpire);
        return token;
    }

    public String getTokenAndSave(SysUser user) {
        //企业管理平台   phone非必填
        String key = concat(USER2TOKEN_PREFIX, user.getPlatform(), user.getUsername());
        String token = redisUtils.get(key);
        if (token == null) {
            token = TokenGenerator.generateValue();
        }
        redisUtils.set(TOKEN2USER_PREFIX, token, user, authExpire);
        redisUtils.set(key, token, authExpire);
        return token;
    }

    public String getUserJson(String token) {
        return redisUtils.get(TOKEN2USER_PREFIX, token);
    }

    public SysUser getUser(String token) {
        String userJson = getUserJson(token);
        if (StringUtils.isEmpty(userJson)) {
            log.warn("can't find user info with token = {}", token);
            return null;
        }
        return JsonUtils.fromJson(userJson, SysUser.class);
    }

    public void clearToken(String token) {
        SysUser sysUser = getUser(token);
        Optional<String> key = Optional.ofNullable(sysUser)
            .map(sysUserX -> concat(USER2TOKEN_PREFIX, sysUserX.getPlatform(),
                sysUserX.getUsername()));
        key.ifPresent(s -> redisUtils.delete(s));

        redisUtils.delete(concat(TOKEN2USER_PREFIX, null, token));
    }

    public void clearTokenByUsername(Integer platform, String username) {
        String key = concat(USER2TOKEN_PREFIX, platform, username);
        String token = redisUtils.get(key);
        if (StringUtils.isNotBlank(token)) {
            this.redisUtils.delete(concat(TOKEN2USER_PREFIX, null, token));
        }
    }

    private String concat(String pre, Integer platform, String key) {
        if (platform == null) {
            return String.join(":", pre, key);
        } else {
            return String.join(":", pre, platform.toString(), key);
        }
    }
}
