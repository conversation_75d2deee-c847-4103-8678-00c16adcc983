package com.cdz360.biz.auth.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.auth.config.ZftAlipayAppIdCfg;
import com.cdz360.biz.auth.config.ZftWxAppIdCfg;
import com.cdz360.biz.auth.dao.TCommercialDao;
import com.cdz360.biz.auth.ds.ro.zft.ZftRoDs;
import com.cdz360.biz.auth.ds.rw.zft.ZftRwDs;
import com.cdz360.biz.auth.model.vo.TCommercial;
import com.cdz360.biz.auth.zft.dto.ZftDto;
import com.cdz360.biz.auth.zft.param.ListZftParam;
import com.cdz360.biz.auth.zft.po.ZftPo;
import com.cdz360.biz.auth.zft.vo.ZftCommVo;
import com.cdz360.biz.auth.zft.vo.ZftVo;
import java.time.Duration;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class ZftService {

    @Autowired
    private ZftWxAppIdCfg zftWxAppIdCfg;

    @Autowired
    private ZftAlipayAppIdCfg zftAlipayAppIdCfg;

    @Autowired
    private ZftRoDs zftRoDs;

    @Autowired
    private ZftRwDs zftRwDs;

    @Autowired
    private TCommercialDao tCommercialDao;

    @Autowired
    private TCommercialService commercialService;

    public ListResponse<ZftVo> zftList(ListZftParam param) {
        return Mono.just(param)
            .flatMap(zftRoDs::findAll)
            .map(poList -> {
                List<ZftVo> result = poList.getData().parallelStream().map(po -> {
                    ZftVo vo = new ZftVo();
                    BeanUtils.copyProperties(po, vo);

                    // 获取关联商户列表
                    QueryWrapper<TCommercial> ew = Wrappers.query();
                    ew.eq("zftId", vo.getId());
//                    EntityWrapper<TCommercial> ew = new EntityWrapper<>();
//                    ew.where("zftId={0}", vo.getId());
                    List<ZftCommVo> commList = commercialService.getBaseMapper().selectList(ew)
                        .stream().map(comm -> new ZftCommVo().setId(comm.getId())
                            .setCommName(comm.getCommName()))
                        .collect(Collectors.toList());
                    vo.setCommList(commList);

                    return vo;
                }).collect(Collectors.toList());
                return RestUtils.buildListResponse(result, poList.getTotal());
            })
            .block(Duration.ofSeconds(50L));

//        ListResponse<ZftPo> poList = zftRoDs.findAll(param);
//        List<ZftVo> result = poList.getData().parallelStream().map(po -> {
//            ZftVo vo = new ZftVo();
//            BeanUtils.copyProperties(po, vo);
//
//            // 获取关联商户列表
//            EntityWrapper<TCommercial> ew = new EntityWrapper<>();
//            ew.where("zftId={0}", vo.getId());
//            List<ZftCommVo> commList = commercialService.selectList(ew)
//                    .stream().map(comm -> new ZftCommVo().setId(comm.getId()).setCommName(comm.getCommName()))
//                    .collect(Collectors.toList());
//            vo.setCommList(commList);
//
//            return vo;
//        }).collect(Collectors.toList());
//
//        return RestUtils.buildListResponse(result, poList.getTotal());
    }

    public ZftVo getZft(Long id) {
        ZftPo po = this.zftRoDs.getById(id);
        if (null == po) {
            throw new DcArgumentException("直付商户ID无效");
        }

        ZftVo vo = new ZftVo();
        BeanUtils.copyProperties(po, vo);

        // 获取关联商户列表
        QueryWrapper<TCommercial> ew = Wrappers.query();
        ew.eq("zftId", vo.getId());
//        EntityWrapper<TCommercial> ew = new EntityWrapper<>();
//        ew.where("zftId={0}", vo.getId());
        List<ZftCommVo> commList = commercialService.getBaseMapper().selectList(ew)
            .stream()
            .map(comm -> new ZftCommVo().setId(comm.getId()).setCommName(comm.getCommName()))
            .collect(Collectors.toList());
        vo.setCommList(commList);

        return vo;
    }

    public ZftVo getZftByTopCommId(Long topCommId) {
        ZftPo po = this.zftRoDs.getByTopCommId(topCommId);
        if (null == po) {
            throw new DcArgumentException("直付商户ID无效");
        }
        ZftVo vo = new ZftVo();
        BeanUtils.copyProperties(po, vo);
        return vo;
    }

    @Transactional(rollbackFor = DcException.class)
    public Long updateZft(ZftDto dto) {
        // 需要限制: 一个集团商户下只能有一个使用个人账户收款方
        // 并且必须配置微信和支付宝信息
        if (dto.getEnableBalance()) {
            boolean has = this.zftRoDs.hasEnableBalance(dto.getTopCommId(), dto.getId());
            if (has) {
                log.warn("开启个人账户收款方同一集团商户只能开启一个");
                throw new DcArgumentException("开启个人账户收款方同一集团商户只能开启一个");
            }

            // 校验微信支付宝商户ID
            if (StringUtils.isBlank(dto.getWxSubMchId()) ||
                StringUtils.isBlank(dto.getWxSubMchName())) {
                throw new DcArgumentException("开启个人账户收款方需要提供微信商户信息");
            }

            if (StringUtils.isBlank(dto.getAlipaySubMchId()) ||
                StringUtils.isBlank(dto.getAlipaySubMchName())) {
                throw new DcArgumentException("开启个人账户收款方需要提供支付宝商户信息");
            }
        }

        // 商户名称全局唯一
        boolean has = this.zftRoDs.hasName(dto.getTopCommId(), dto.getId(), dto.getName());
        if (has) {
            log.info("直付商家名称已存在，请更换直付商家名称");
            throw new DcArgumentException("直付商家名称已存在，请更换直付商家名称");
        }

        ZftPo po = new ZftPo();
//        BeanUtils.copyProperties(dto, po);
        po.setId(dto.getId())
            .setTopCommId(dto.getTopCommId())
            .setCommId(dto.getCommId())
            .setName(dto.getName())
            .setEnableBalance(dto.getEnableBalance())
            .setAlipaySubMchId(dto.getAlipaySubMchId())
            .setAlipaySubMchName(dto.getAlipaySubMchName())
            .setAlipayCreditServiceId(dto.getAlipayCreditServiceId())
            .setWxSubMchId(dto.getWxSubMchId())
            .setWxSubMchName(dto.getWxSubMchName())
            .setWxCreditServiceId(dto.getWxCreditServiceId())
            .setUpdateOpId(dto.getUpdateOpId())
            .setUpdateOpName(dto.getUpdateOpName());
//                .setEnableRefund(dto.getEnableRefund());

        // 页面没有提供输入，这里从配置中获取 👇
        if (StringUtils.isNotBlank(dto.getWxSubMchId())) {
            if (!"NONE".equalsIgnoreCase(dto.getWxSubMchId())) {
                po.setWxMchId(zftWxAppIdCfg.getWxMchId());
            }
            po.setWxAndroidAppId(zftWxAppIdCfg.getWxAndroidAppId())
                .setWxLiteAppId(zftWxAppIdCfg.getWxLiteAppId())
                .setWxIosAppId(zftWxAppIdCfg.getWxIosAppId());
        }
        if (StringUtils.isNotBlank(dto.getAlipaySubMchId())) {
            if (!"NONE".equalsIgnoreCase(dto.getAlipaySubMchId())
                && StringUtils.isNotBlank(zftAlipayAppIdCfg.getAlipayMchId())) {
                po.setAlipayMchId(zftAlipayAppIdCfg.getAlipayMchId());
            }
        }
        // 页面没有提供输入，这里从配置中获取 👆

        List<Long> oldCommList;
        if (dto.getId() == null) {
            po.setId(this.addZft(po));
            oldCommList = List.of();
        } else {
            boolean b = this.zftRwDs.updateZft(po);
            if (!b) {
                throw new DcArgumentException("更新失败");
            }

            // 获取关联商户列表
            QueryWrapper<TCommercial> ew = Wrappers.query();
            ew.eq("zftId", po.getId());
//            EntityWrapper<TCommercial> ew = new EntityWrapper<>();
//            ew.where("zftId={0}", po.getId());
            oldCommList = commercialService.getBaseMapper().selectList(ew)
                .stream().map(TCommercial::getId).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(oldCommList) ||
            CollectionUtils.isNotEmpty(dto.getCommIdList())) {

            if (CollectionUtils.isNotEmpty(dto.getCommIdList())) {
                List<TCommercial> commList = dto.getCommIdList()
                    .stream().filter(commId -> !oldCommList.contains(commId))
                    .map(commId -> {
                        TCommercial comm = new TCommercial();
                        comm.setId(commId);
                        comm.setZftId(po.getId());
                        return comm;
                    }).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(commList)) {
                    // 是否存在已关联其他直付商家
                    has = commercialService.hasZftId(
                        dto.getCommIdList().stream()
                            .filter(commId -> !oldCommList.contains(commId))
                            .collect(Collectors.toList()), po.getId());
                    if (has) {
                        throw new DcArgumentException("不能选择已绑定的商户");
                    }

                    boolean b = commercialService.updateBatchById(commList);
                    if (!b) {
                        throw new DcArgumentException("更新失败");
                    }
                }
            } else {
                dto.setCommIdList(List.of());
            }

            // 清空差集部分
            List<TCommercial> commList = oldCommList.stream()
                .filter(commId -> !dto.getCommIdList().contains(commId))
                .map(commId -> {
                    TCommercial comm = new TCommercial();
                    comm.setId(commId);
                    comm.setZftId(0L);
                    return comm;
                }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(commList)) {
                boolean b = commercialService.updateBatchById(commList);
                if (!b) {
                    throw new DcArgumentException("更新失败");
                }
            }

            //在线退款开启与关闭
//            tCommercialDao.updateRefundStatusByzftId(po.getId(),po.getEnableRefund());
        }

        return po.getId();
    }

    private Long addZft(ZftPo po) {
        po.setCreateOpId(po.getUpdateOpId())
            .setCreateOpName(po.getUpdateOpName());
        this.zftRwDs.insertZft(po);
        return po.getId();
    }
}
