package com.cdz360.biz.auth.feign;

///**
// * DeviceBusinessFeignClient
// *
// * @since 2019/6/13 17:25
// * <AUTHOR>
// */
//@FeignClient(name = "device-business-rest"/*, fallbackFactory = HystrixMerchantClientFactory.class*/)
//public interface DeviceBusinessFeignClient {
//
//    @PostMapping("/api/boxsetting/updateQRCode")
//    ObjectResponse<Integer> updateQRCode(@RequestBody BoxSettingUpsertQRCodeRequest request);
//
//}