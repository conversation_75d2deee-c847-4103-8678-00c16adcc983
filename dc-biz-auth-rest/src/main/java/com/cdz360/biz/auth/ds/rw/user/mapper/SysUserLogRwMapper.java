package com.cdz360.biz.auth.ds.rw.user.mapper;

import com.cdz360.biz.auth.user.po.SysUserLogPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.math.BigDecimal;
import java.util.Date;

@Mapper
public interface SysUserLogRwMapper {
	SysUserLogPo getById(@Param("id") Long id, @Param("lock") boolean lock);

	int insertSysUserLog(SysUserLogPo sysUserLogPo);

	int updateSysUserLog(SysUserLogPo sysUserLogPo);


}
