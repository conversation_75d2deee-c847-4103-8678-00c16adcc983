<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.dao.AuthGroupRefDao">
    <insert id="batchAddGroupRef">
        INSERT INTO t_auth_group_ref(groupId, authorityId, authorityCode,opName,opId,createTime,updateTime)
        VALUES
        <foreach collection="refList" item="rList" separator=",">
            <![CDATA[
                (#{rList.groupId},#{rList.authorityId},]]>

            <choose>
                <when test="rList.authorityCode != null and rList.authorityCode !=''">
                    #{rList.authorityCode}
                </when>
                <otherwise>
                    (select code from t_authority where id=#{rList.authorityId})
                </otherwise>
            </choose>

            <![CDAT<PERSON>[,#{rList.opName},#{rList.opId},now(),now())
        ]]>
        </foreach>
    </insert>

    <delete id="deleteGroupRefByGroupId">
        DELETE
        FROM t_auth_group_ref
        WHERE groupId = #{groupId};
    </delete>

    <select id="selectByGroupId" resultType="com.cdz360.biz.auth.model.vo.AuthGroupRef">
        select a.*,b.module from t_auth_group_ref a
        left join t_authority b on a.authorityId = b.id
        where a.groupId = #{groupId};
    </select>

    <select id="getAuthorityGroupListByUid" resultType="com.cdz360.biz.auth.model.vo.AuthorityGroup">
        SELECT
            ag.*
        FROM
            sys_user su
        LEFT JOIN t_user_group_ref ugr ON ugr.userId = su.id
        LEFT JOIN t_authority_group ag ON ag.id = ugr.authGroupId
        WHERE
            su.id = #{uid}
            AND ugr.userId = #{uid}
        ORDER BY
            ag.createTime DESC
    </select>
</mapper>