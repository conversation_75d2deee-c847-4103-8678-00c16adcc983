<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.ds.rw.sys.mapper.SysRoleRwMapper">



    <update id="batchUpdate">
        update sys_role
            set commIds = #{commIds},update_by = #{updateBy}
        where
        id IN
        <foreach collection="roleIdList" index="index" item="item" open="("
                 separator="," close=")">
            #{item}
        </foreach>
    </update>
</mapper>