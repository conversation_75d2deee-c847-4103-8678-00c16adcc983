package com.cdz360.biz.auth.service.oauth;

import com.cdz360.biz.auth.model.exception.ErrcodeException;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.service.LoginService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.authc.*;
import org.apache.shiro.authz.AuthorizationInfo;
import org.apache.shiro.authz.SimpleAuthorizationInfo;
import org.apache.shiro.realm.AuthorizingRealm;
import org.apache.shiro.subject.PrincipalCollection;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.Set;

/**
 * 认证
 *
 * <AUTHOR>
 *    <EMAIL>
 * @since 2017-05-20 14:00
 */
@Slf4j
public class OAuth2Realm extends AuthorizingRealm {
    private ShiroHttpService shiroHttpService;

    private LoginService loginService;

    public OAuth2Realm(ShiroHttpService shiroHttpService, LoginService loginService) {
        this.shiroHttpService = shiroHttpService;
        this.loginService = loginService;
    }

    @Override
    public boolean supports(AuthenticationToken token) {
        return token instanceof OAuth2Token;
    }

    /**
     * 授权(验证权限时调用)
     */
    @Override
    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principals) {
        SysUser user = (SysUser) principals.getPrimaryPrincipal();
        Long userId = user.getId();

        ServletRequestAttributes servletRequestAttributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        String token = (String) servletRequestAttributes.getAttribute("token", RequestAttributes.SCOPE_REQUEST);
        Set<String> permSet = null;
        Set<String> roleSet = null;

        log.info("user = {}", user);
        try {
            //用户权限列表
            permSet = shiroHttpService.permSet(userId, token);

            roleSet = shiroHttpService.roleSet(userId, token);
        } catch (ErrcodeException e) {
            log.error("系统error 不为0::{}", e.getMessage(), e);
            return null;
        }


        SimpleAuthorizationInfo info = new SimpleAuthorizationInfo();
        info.setStringPermissions(permSet);
        info.setRoles(roleSet);
        return info;
    }

    /**
     * 认证(登录时调用)
     */
    @Override
    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) throws AuthenticationException {
        String accessToken = (String) token.getPrincipal();

        //根据accessToken，查询用户信息
        SysUser user = null;
        try {
            user = shiroHttpService.userByToken(loginService, accessToken);
        } catch (ErrcodeException e) {
            log.warn("系统errcode 不为0,{}", e.getMessage());
        }

        //token失效
        if (user == null) {
            throw new IncorrectCredentialsException("token失效，请重新登录");
        }
        //账号锁定
        if (user.getStatus() == 2) {
            throw new LockedAccountException("账号已被锁定,请联系管理员");
        }

        SimpleAuthenticationInfo info = new SimpleAuthenticationInfo(user, accessToken, getName());
        return info;
    }
}
