package com.cdz360.biz.auth.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.bi.dashboard.SiteAndPlugBiVo;
import com.cdz360.biz.model.bi.dashboard.SiteAndPlugBiVoEx;
import com.cdz360.biz.model.common.request.SiteCtrlRequest;
import com.cdz360.biz.model.iot.dto.EvseDto;
import com.cdz360.biz.model.iot.dto.PlugInfoDto;
import com.cdz360.biz.model.iot.dto.SiteCtrlDto;
import com.cdz360.biz.model.iot.param.*;
import com.cdz360.biz.model.iot.vo.*;
import com.cdz360.biz.model.trading.iot.dto.EvseModuleDto;
import com.cdz360.biz.model.trading.iot.dto.SiteDeviceBiDto;
import com.cdz360.biz.model.trading.iot.po.EvseModelPo;
import com.cdz360.biz.model.trading.iot.vo.EvseInfoVo;
import com.cdz360.biz.model.trading.iot.vo.EvseStatusPowerBiVo;
import com.cdz360.biz.model.trading.iot.vo.PlugStatusBiVo;
import com.cdz360.biz.model.trading.iot.vo.PlugSupplyBiVo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.yw.param.ReplaceDeviceParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Slf4j
@Component
public class IotDeviceMgmFeignClientHystrix implements FallbackFactory<IotDeviceMgmFeignClient> {
    @Override
    public IotDeviceMgmFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, throwable.getStackTrace());

        return new IotDeviceMgmFeignClient() {


            @Override
            public ObjectResponse<EvseInfoVo> getEvseInfo( String evseNo){
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<EvseInfoVo> getEvseInfoList(ListEvseParam param){
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<EvseDto> getEvseList( ListEvseParam param) {
                log.error("发生熔断. param = {}", param);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<EvseModelVo> getEvseModelVoList(ListEvseParam param) {
                log.error("发生熔断. param = {}", param);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<EvseModelPo> getModelList(Long start, Integer size) {
                log.error("发生熔断. getModelList start = {}, size = {}", start, size);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<EvseDto> getEvseListForTopology(ListEvseParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<OfflineEvseParam> checkOfflineEvseInDB(List<OfflineEvseParam> params) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<OfflineEvseParam> checkEvseInDB(List<OfflineEvseParam> params) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<PlugVo> getPlugInfo(String plugNo) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<PlugVo> getPlugList(ListPlugParam param) {

                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<DeviceBi> getDeviceBi(String siteId) {
                log.error("发生熔断. siteId = {}", siteId);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<DeviceBi> getSitesDevice(ListDeviceParam param) {
                log.error("发生熔断. param = {}", param);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<PlugSupplyBiVo> getPlugSupplyBi(String commIdChain) {
                return RestUtils.serverBusy4ListResponse();
            }


            @Override
            public ListResponse<PlugStatusBiVo> getPlugStatusBi(String provinceCode,
                                                                String cityCode,
                                                                String siteId,
                                                                String commIdChain) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SiteDeviceBiDto> getSiteDeviceBiList(List<String> siteIdList) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<EvseStatusPowerBiVo> getEvseStatusPowerBi(String provinceCode,
                                                                          String cityCode,
                                                                          String siteId,
                                                                          String commIdChain) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<PlugInfoDto> getPlugsOfSite(Integer index, Integer size, String siteId) {
                log.error("发生熔断. siteId = {}", siteId);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<EvseCfgResultVo> getEvseCfgResultList(ListEvseCfgResultParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<Long> getEvseListActiveTime(Date startTime, Date endTime, String evseNo, String siteId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse add(SiteCtrlRequest req) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse edit(SiteCtrlRequest req) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<SiteCtrlDto> list(String keyword, String siteId, long start, long size) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse disable(String num) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse addOrUpdate(SiteCtrlCfgPo po) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<SiteCtrlCfgVo> findByCtrlNo(String ctrlNum) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<SiteCtrlCfgVo> getBySiteCtrl(String ctrlNum) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse send2GetCfg(String ctrlNum) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<String> getIdleSiteIdList(Long topCommId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<SitePo> getRecordEvsePlugInfo(String siteId, Boolean isOfflineSite) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Date> getExpireDate(String siteId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SitePo> getUpgradeCleaningEvsePlugInfo() {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<SiteAndPlugBiVo> getSiteAndPlugStatus(Long commId, String commIdChain) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SiteAndPlugBiVoEx> getSiteAndPlugStatusSubComm(Long commId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<UpgradeRecordVo> getUpgradeRecordVo(UpgradeTaskListRequest request) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<EvseModuleDto> getEvseModuleList(String evseNo) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<DeviceVo> getByEvseNo(String evseNo, String deviceName, Long start, Long size) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<EvseDeviceVo> getByDeviceNo(FindDeviceParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse addDevice(DeviceParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse editDevice(DeviceParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse replaceDevice(ReplaceDeviceParam param) {
                return RestUtils.serverBusy4ListResponse();
            }
        };
    }
}
