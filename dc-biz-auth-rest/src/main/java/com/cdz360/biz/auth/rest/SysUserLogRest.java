package com.cdz360.biz.auth.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.auth.service.SysUserLogService;
import com.cdz360.biz.auth.user.param.SysUserLogParam;
import com.cdz360.biz.auth.user.vo.SysUserLogVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class SysUserLogRest {

    @Autowired
    private SysUserLogService sysUserLogService;

    @PostMapping("/api/sys/user/getLoginLog")
    public ListResponse<SysUserLogVo> getLoginLog(@RequestBody SysUserLogParam param) {
        log.info("param = {}", param);
        return sysUserLogService.getLoginLog(param);
    }

    @PostMapping("/api/sys/user/getOpLog")
    public ListResponse<SysUserLogVo> getOpLog(@RequestBody SysUserLogParam param) {
        log.info("param = {}", param);
        return sysUserLogService.getOpLog(param);
    }
}
