package com.cdz360.biz.auth.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <p>
 * 角色与菜单对应关系
 * </p>
 *
 * <AUTHOR>
  * @since 2017-10-28
 */
@EqualsAndHashCode(callSuper = true)
@TableName("sys_role_menu")
@Data
@ToString(callSuper = true)
public class SysRoleMenu extends BaseEntity {

    /**
     * 角色ID
     */
    @TableField("role_id")
    private Long roleId;
    /**
     * 菜单ID
     */
    @TableField("menu_id")
    private Long menuId;


}
