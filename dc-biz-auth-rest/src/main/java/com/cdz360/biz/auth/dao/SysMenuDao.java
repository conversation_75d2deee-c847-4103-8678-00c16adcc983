package com.cdz360.biz.auth.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdz360.biz.auth.model.vo.SysMenu;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface SysMenuDao extends BaseMapper<SysMenu> {

    List<SysMenu> findMenuByUserIdAndSysId(@Param("userId") Long userId, @Param("sysId") Long sysId, @Param("showMenu") boolean showMenu);

    List<SysMenu> findMenuByUserIdAndSysIdAndUrl(@Param("userId") Long userId, @Param("sysId") Long sysId, @Param("url") String url);

    Long countMenuByUserIdAndSysId(@Param("userId") Long userId, @Param("sysId") Long sysId);


    Set<String> findPermByUserId(Long userId);


    List<SysMenu> findAllMenuTaggedByRoleId(Long roleId);

    Long countMenuByUniqueKeyAndOtherId(@Param("subsysId") Long subsysId, @Param("name") String name, @Param("type") Integer type, @Param("id") Long id);

    SysMenu getMenuById (@Param("id") Long id);

}
