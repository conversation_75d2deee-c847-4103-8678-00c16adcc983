<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.biz.auth.ds.ro.user.mapper.CorpWxAppRoMapper">



	<resultMap id="RESULT_CORPWXAPP_PO" type="com.cdz360.biz.auth.user.po.CorpWxAppPo">

		<id column="id" jdbcType="BIGINT" property="id" />

		<result column="topCommId" jdbcType="BIGINT" property="topCommId" />

		<result column="commId" jdbcType="BIGINT" property="commId" />

		<result column="name" jdbcType="VARCHAR" property="name" />

		<result column="flagType" property="flagType" />

		<result column="appSecret" jdbcType="VARCHAR" property="appSecret" />

		<result column="appCorpId" jdbcType="VARCHAR" property="appCorpId" />

	</resultMap>



	<select id="getById"

			resultMap="RESULT_CORPWXAPP_PO">	
		select * from t_corp_wx_app where id = #{id}

	</select>
	<select id="findAll"
			parameterType="com.cdz360.biz.auth.user.param.ListCorpWxAppParam"
			resultType="com.cdz360.biz.auth.user.po.CorpWxAppPo">
		select * from t_corp_wx_app
		<where>
			<if test="@com.cdz360.base.utils.StringUtils@isNotBlank(nameLike)">
				and name like concat('%', #{nameLike}, '%')
			</if>
		</where>
		limit 100
	</select>


</mapper>

