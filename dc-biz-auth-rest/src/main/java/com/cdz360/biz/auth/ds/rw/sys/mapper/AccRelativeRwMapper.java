package com.cdz360.biz.auth.ds.rw.sys.mapper;

import com.cdz360.biz.auth.sys.po.AccRelativePo;
import com.cdz360.biz.model.sys.vo.AccRelativeOrderVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AccRelativeRwMapper {

    int insertOrUpdate(AccRelativePo po);

    int updateByCondition(AccRelativePo po);

    int dismissBySysUid(@Param("sysUid") Long sysUid, @Param("work") boolean work);

    int deleteBySysUid(@Param("sysUid") Long sysUid);

    int updateBatch(@Param("list") List<AccRelativeOrderVo> list);

}
