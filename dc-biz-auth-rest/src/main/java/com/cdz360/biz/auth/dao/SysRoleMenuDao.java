package com.cdz360.biz.auth.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdz360.biz.auth.model.vo.SysRole;
import com.cdz360.biz.auth.model.vo.SysRoleMenu;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface SysRoleMenuDao extends BaseMapper<SysRoleMenu> {

    Integer deleteByRoleId(Long roleId);
    int deleteByMenuId(@Param("menuId") Long menuId);

    Integer saveBatch(@Param("roleId") Long roleId, @Param("menuIds") Set<Long> menuIds);

    Integer saveBatchByMenuId(@Param("menuId") Long menuId, @Param("roleIds") List<Long> roleIds);

    List<Long> findMenuIdsByRoleId(Long roleId);

    Integer deleteBatch(@Param("roleId") Long roleId, @Param("menuIds") Set<Long> menuIds);

    List<Long> notYeyRoleMenu(@Param("menuId") Long menuId, @Param("pMenuId") Long pMenuId);

    List<Long> getMenuList(@Param("roleId") Long roleId);

    List<SysRole> getRoleList(@Param("platform") Long platform);
}
