package com.cdz360.biz.auth.service;

//import com.baomidou.mybatisplus.mapper.Condition;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.auth.config.UserFillingAspect;
import com.cdz360.biz.auth.dao.SysMenuDao;
import com.cdz360.biz.auth.dao.SysRoleMenuDao;
import com.cdz360.biz.auth.dao.SysSystemDao;
import com.cdz360.biz.auth.dao.SysUserDao;
import com.cdz360.biz.auth.dao.SysUserRoleDao;
import com.cdz360.biz.auth.ds.ro.sys.SysRoleRoDs;
import com.cdz360.biz.auth.ds.ro.user.SysUserRoDs;
import com.cdz360.biz.auth.ds.rw.user.SiteGroupUserRwDs;
import com.cdz360.biz.auth.ds.rw.user.SysUserRwDs;
import com.cdz360.biz.auth.sys.param.QuerySysUserRequest;
import com.cdz360.biz.auth.model.vo.SysMenu;
import com.cdz360.biz.auth.model.vo.SysRole;
import com.cdz360.biz.auth.model.vo.SysSystem;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.model.vo.SysUserVo;
import com.cdz360.biz.auth.model.vo.TCommercial;
import com.cdz360.biz.auth.sys.param.BatchAddRoleUserParam;
import com.cdz360.biz.auth.sys.param.RoleUserListParam;
import com.cdz360.biz.auth.sys.param.RoleUserUpdateParam;
import com.cdz360.biz.auth.sys.vo.RoleUserVo;
import com.cdz360.biz.auth.user.po.SysUserPo;
import com.cdz360.biz.auth.utils.IotAssert;
import com.cdz360.biz.model.cus.corp.dto.UserIdDto;
import com.cdz360.biz.model.cus.param.SysUserCheckParam;
import com.cdz360.biz.oa.param.account.OaModifyAccountParam;
import com.cdz360.biz.oa.param.account.OaModifyGroupParam;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.jooq.lambda.Seq;
import org.jooq.lambda.tuple.Tuple2;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.bind.annotation.RequestBody;

@Service
@Slf4j
public class SysUserService extends ServiceImpl<SysUserDao, SysUser> {

    @Autowired
    private SysRoleMenuDao roleMenuDao;

    @Autowired
    private SysMenuDao menuDao;

    @Autowired
    private SysSystemDao systemDao;


    @Autowired
    private SysUserRoleService userRoleService;
    @Autowired
    private SysRoleService roleService;
    //    @Autowired
//    SysPositionService positionService;
    @Autowired
    private TCommercialService tCommercialService;

    @Autowired
    private SysUserRoDs sysUserRoDs;

    @Autowired
    private SysUserRwDs sysUserRwDs;

    @Autowired
    private SysUserRoleDao sysUserRoleDao;

    @Autowired
    private SiteGroupUserRwDs siteGroupUserRwDs;

    @Autowired
    private SysRoleRoDs sysRoleRoDs;

    public ListResponse<SysUser> selectSysUser(QuerySysUserRequest querySysUserRequest) {
        IotAssert.isTrue(
            querySysUserRequest.getStart() != null && querySysUserRequest.getSize() != null,
            "请传入分页参数");
        List<SysUser> sysUserList = baseMapper.querySysUser(querySysUserRequest);
        long total = 0;
        if (Boolean.TRUE.equals(querySysUserRequest.getTotal())) {
            total = baseMapper.querySysUserCount(querySysUserRequest);
        }
        sysUserList.forEach(user -> {
            this.fillUserPostsAndRoles(user);
            this.fillUserCommercialName(user);
        });
        return RestUtils.buildListResponse(sysUserList, total);
    }

    public List<Long> teamCatalogUserIdList(Long uid, String uname) {
        final SysUserPo user = this.sysUserRoDs.getByUseId(uid);
        if (null == user) {
            throw new DcArgumentException("用户ID无效");
        }
        if (StringUtils.isBlank(user.getTeamCatalog())) {
            return StringUtils.isNotBlank(uname) ?
                this.sysUserRoDs.uidByUname(uname) : List.of();
        }
//        final List<Long> result = this.sysUserRoDs.uidByTeamCatalog(user.getTeamCatalog(), uname);
//        result.remove(uid);
        return this.sysUserRoDs.uidByTeamCatalog(user.getTeamCatalog(), uname);
    }

    public UserIdDto searchUserIdList(Long teamCatalogUid, String uname) {
        if ((null == teamCatalogUid || teamCatalogUid < 1) && StringUtils.isBlank(uname)) {
            throw new DcArgumentException("查询用户ID参数无效");
        }

        final SysUserPo user = this.sysUserRoDs.getByUseId(teamCatalogUid);
        if (null == user) {
            throw new DcArgumentException("用户ID无效");
        }

        UserIdDto result = new UserIdDto();

        if (StringUtils.isNotBlank(uname)) {
            result.setSearchNameUserIdList(this.sysUserRoDs.uidByUname(uname));
        }

        if (StringUtils.isBlank(user.getTeamCatalog())) {
            result.setTeamCatalogUserIdList(List.of());
        } else {
            // 对teamCatalog团队标签 进行拆分
            List<String> catalogList = List.of(user.getTeamCatalog().split(","));
            // 找出来团队标签里包含当前用户团队标签数组的所有用户
            result.setTeamCatalogUserIdList(
                this.sysUserRoDs.uidByTeamCatalogList(catalogList, null));
        }

        return result;
    }

    public Boolean sameTeamCatalog(Long uid1, Long uid2) {
        final SysUserPo user = this.sysUserRoDs.getByUseId(uid1);
        if (null == user) {
            throw new DcArgumentException("用户ID无效");
        }
        if (StringUtils.isBlank(user.getTeamCatalog())) {
            return false;
        }
        // 以1为主，将1作为提交人，对1的 teamCatalog团队标签 进行拆分
        List<String> catalogList = List.of(user.getTeamCatalog().split(","));
//        final List<Long> list = this.sysUserRoDs.uidByTeamCatalog(user.getTeamCatalog(), null);
        // 找出来团队标签里包含提交人团队标签数组的所有用户
        final List<Long> list = this.sysUserRoDs.uidByTeamCatalogList(catalogList, null);
        return list.contains(uid2);
    }

    public SysUser selectById(Long id) {
        return sysUserRoDs.selectById(id);
    }

    public List<Long> sameCorpWxAppNameUserIds(Long uid) {
        if (null == uid) {
            return List.of();
        }
        return this.sysUserRoDs.sameCorpWxAppNameUids(uid);
    }

    public List<Long> sameTeamCatalogUserIds(Long uid) {
        if (null == uid) {
            return List.of();
        }
        final SysUserPo user = this.sysUserRoDs.getByUseId(uid);
        if (null == user || StringUtils.isBlank(user.getTeamCatalog())) {
            return List.of();
        }
        return this.sysUserRoDs.uidByTeamCatalog(user.getTeamCatalog(), null);
    }

    public List<Long> groupIdByKeyWord(String keyWord) {
        return this.sysUserRoDs.groupIdByKeyWord(keyWord);
    }

    public ObjectResponse<Integer> allocateSysUserGroup(OaModifyGroupParam param) {
        if (StringUtils.isBlank(param.getGid())) {
            throw new DcArgumentException("审核组ID不能为空");
        }

        // 清空原审核组的所属用户
        int i = sysUserRwDs.clearSysUserGroupId(param.getGid());
        log.info("清空数量: gid = {}, i = {}", param.getGid(), i);

        // 重新分配
        if (CollectionUtils.isNotEmpty(param.getUserAccountList())) {
            final List<Long> collect = param.getUserAccountList().stream()
                .map(OaModifyAccountParam::getUid)
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect)) {
                i = sysUserRwDs.updateSysUserGroupId(param.getGid(), collect);
                log.info("新分配数量: gid = {}, i = {}", param.getGid(), i);
            }
        }
        return RestUtils.buildObjectResponse(i);
    }

    public static List<SysMenu> toMenuTree(List<SysMenu> menuList, String nameFilter) {

        if (!StringUtils.isEmpty(nameFilter)) {
            Seq<SysMenu> sorted = Seq.seq(menuList).sorted(SysMenu::getType, (t1, t2) -> t2 - t1);
            List<Long> pids = new ArrayList<>();
            menuList = sorted.filter(sysMenu -> {
                boolean nameChecked =
                    sysMenu.getName() != null && (sysMenu.getName().contains(nameFilter));
                nameChecked = nameChecked || pids.contains(sysMenu.getId());
                if (nameChecked) {
                    pids.add(sysMenu.getPid());
                }
                return nameChecked;
            }).toList();

        }

        List<SysMenu> topMenus = new ArrayList<>();
        Map<Long, SysMenu> sysMenuMap = menuList.stream()
            .collect(Collectors.toMap(SysMenu::getId, Function.identity()));
        for (SysMenu sysMenu : menuList) {
            if (sysMenu.getPid() != null) {
                SysMenu parent = sysMenuMap.get(sysMenu.getPid());
                if (parent != null) {
                    if (parent.getChildren() == null) {
                        parent.setChildren(new ArrayList<>());
                    }
                    parent.getChildren().add(sysMenu);
//                } else if (sysMenu.getType() == SysMenu.MODULE_TYPE) {
                } else if (List.of(SysMenu.MODULE_TYPE, SysMenu.MENU_TYPE)
                    .contains(sysMenu.getType())) { // SysMenu.MENU_TYPE 适配户用储能
                    topMenus.add(sysMenu);
                }
            }
        }
        return topMenus;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean addUser(@RequestBody @Valid SysUser user) {
        int result = getBaseMapper().insert(user);
        if (result > 0) {
            Long userId = user.getId();
            //处理用户角色
            if (user.getRoleIdList() != null && CollectionUtils.isNotEmpty(user.getRoleIdList())) {
                userRoleService.insertRoleIdList(userId, user.getRoleIdList());
            }
            //处理 岗位
//            if (!CollectionUtils.isEmpty(user.getPositionList())) {
//                resetUserPositionList(userId, user.getPositionList());
//            }

            // 仅管理平台添加场站组
            if (user.getPlatform() != null && (List.of(AppClientType.MGM_WEB.getCode(),
                    AppClientType.COMM_ESS_MGM.getCode())
                .contains(user.getPlatform().intValue())) && CollectionUtils.isNotEmpty(
                user.getGids())) {
                int i = siteGroupUserRwDs.batchInsert(userId, user.getGids());
                if (i <= 0) {
                    log.warn("新增用户场站组关系异常: {}, {}", userId, user.getGids());
                }
            }

        }
        return result > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean updateUser(SysUser bodyEntity) {
        boolean update = getBaseMapper().updateById(bodyEntity) > 0;
        List<Long> roleIdList = bodyEntity.getRoleIdList();
        //更新角色
        // 账号绑定角色  分开
//        userRoleService.updateRoleIdList(bodyEntity.getId(), roleIdList);
        //更新岗位
//        resetUserPositionList(bodyEntity.getId(), bodyEntity.getPositionList());

        // 场站组绑定关系调整
        List<String> newGidList =
            CollectionUtils.isNotEmpty(bodyEntity.getGids()) ? bodyEntity.getGids() : List.of();
        if (CollectionUtils.isNotEmpty(newGidList)) {
            int i = siteGroupUserRwDs.batchDelete(bodyEntity.getId(), newGidList);
            if (i <= 0) {
                log.warn("删除用户场站组关系异常: {}, {}",
                    bodyEntity.getId(), newGidList);
            }
            i = siteGroupUserRwDs.batchInsert(bodyEntity.getId(), newGidList);
            if (i <= 0) {
                log.warn("编辑用户场站组关系异常: {}, {}",
                    bodyEntity.getId(), newGidList);
            }
        } else {
            int i = siteGroupUserRwDs.deleteByUid(bodyEntity.getId());
            if (i <= 0) {
                log.warn("删除用户场站组关系异常: {}, {}",
                    bodyEntity.getId(), newGidList);
            }
        }
        return update;
    }

    /**
     * 更新账号状态
     * @param bodyEntity
     * @return
     */
    public boolean updateUserStatusById(SysUser bodyEntity) {
        return sysUserRwDs.updateUserStatusById(bodyEntity);
    }

    /**
     * 批量启用停用
     *
     * @param sysUidList
     * @param status
     * @return
     */
    public boolean changeStatusBatch(List<Long> sysUidList, Integer status) {
        List<SysUser> temp = sysUidList.stream().map(e -> {
            SysUser su = new SysUser();
            su.setId(e);
            su.setStatus(status);
            return su;
        }).collect(Collectors.toList());
        return updateBatchById(temp);
    }

    public List<SysUser> checkByUserNameOrPhoneOrEmail(SysUser su) {
        // 用户相关可以不需要手机号
        return baseMapper.checkByUserNameOrEmail(su);
    }

    public SysUser storedUser(SysUser user) {
        if (user == null || user.getUsername() == null || user.getPassword() == null) {
            return null;
        }
        //return baseMapper.queryByUserNameOrPhoneOrEmail(user.getUsername());
        return baseMapper.queryByUserName(user.getUsername(), user.getPlatform());
    }

    public boolean checkPasswordEquals(SysUser naked, SysUser stored) {
        naked.setSalt(stored.getSalt());
        return naked.hashedPassword().equals(stored.getPassword());
    }

    /**
     * 查看所有系统的所有菜单树, 权限中心管理时使用
     *
     * @param userId
     * @param sysId
     * @param nameFilter
     * @return
     */
    @Transactional
    public List<SysSystem> querySystemMenuTree(Long userId, Long sysId, String nameFilter) {
        List<SysMenu> sysMenuList = menuDao.findMenuByUserIdAndSysId(userId, sysId, false);
        // 根据子系统分组, 没有子系统的作一组
        SysSystem defaultSys = new SysSystem(-1L);
        defaultSys.setName("默认系统");
        defaultSys.setDesc("此系统并不存在!");

        List<SysSystem> systemList = Seq.seq(sysMenuList)
            .distinct(SysMenu::getId)
            .grouped(m -> Optional.ofNullable(m.getSubsysId()).orElse(-1L))
            .map(tup ->
                this.menuToSys(tup, defaultSys, nameFilter)
            )
            .filter(sys -> sys.getMenus() != null && !sys.getMenus().isEmpty()
                && sys.getId().longValue() != -1L)
            .toList();
//
        systemList.stream().forEach(sys -> {
            sys.getMenus().stream().forEach(m1 -> {
                this.genFullKey(m1, String.valueOf(sys.getId()));
            });
        });
        return systemList;
    }

    private void genFullKey(SysMenu menu, String parentKey) {
        menu.setFullKey(parentKey + "-" + menu.getId());
        if (menu.getChildren() != null && !menu.getChildren().isEmpty()) {
            menu.getChildren().stream().forEach(cm -> {
                this.genFullKey(cm, menu.getFullKey());
            });
        }
    }

    private SysSystem menuToSys(Tuple2<Long, Seq<SysMenu>> tup, SysSystem defaultSys,
        String nameFilter) {
        Long id = tup.v1;
        SysSystem system = Optional.ofNullable(systemDao.selectById(id)).orElse(defaultSys);
        //SysSystemVo vo = new SysSystemVo();
        //BeanUtils.copyProperties(system, vo);
        //vo.setKey(vo.get);
        UserFillingAspect.fillUserInfo(system);
        List<SysMenu> menuSet = tup.v2().toList();
        system.setMenus(toMenuTree(menuSet, nameFilter));
        return system;
    }

    /**
     * 查询指定 用户和系统的 菜单树
     *
     * @param userId 用户id
     * @param sysId  子系统id
     */
    @Transactional
    public SysSystem querySystemMenuTree(Long userId, Long sysId) {
        List<SysMenu> menuList = menuDao.findMenuByUserIdAndSysId(userId, sysId, false);
        SysSystem system = Optional.ofNullable(systemDao.selectById(sysId))
            .orElse(new SysSystem(sysId));
        system.setMenus(toMenuTree(menuList, null));
        return system;
    }

    @Transactional
    public List<SysMenu> queryMenuTree(Long userId, Long sysId, String nameFilter) {
        List<SysMenu> sysMenuList = queryMenuList(userId, sysId);
        List<SysMenu> collect = sysMenuList.stream().distinct().collect(Collectors.toList());
        List<SysMenu> menuTree = toMenuTree(collect, nameFilter);
        return menuTree;
    }

    /**
     * 查询 应用 中 某用户的菜单
     *
     * @param userId 用户id
     * @param sysId  应用id
     */
    public List<SysMenu> queryMenuList(Long userId, Long sysId) {
        boolean showMenu = (userId != null);
        if (userId != null) {
            //冻结的用户  不应该登录, 这里不再处理

            List<SysRole> roleList = roleService.findByUserId(userId);
            boolean isAdmin = roleList
                .stream()
                .anyMatch(
                    sysRole -> Objects.equals(true, sysRole.getSvaha()) && !sysRole.isFreeze());
            if (isAdmin) {
                userId = null; //如果拥有 管理员角色(全部菜单) 显示全部菜单
            }
        }

        return menuDao.findMenuByUserIdAndSysId(userId, sysId, showMenu);
    }

    @Cacheable(value = "userById", key = "#id")
    public String queryUsernameById(Long id) {
        SysUser sysUser = baseMapper.selectById(id);
        if (sysUser != null) {
            return sysUser.getName();
        }
        return null;
    }

    //------------------------------------用户-职位相关--------------------------------------

    public Long countMenuByUserIdAndSysId(Long userId, Long sysId) {
        List<SysRole> byUserId = roleService.findByUserId(userId);
        boolean isAdmin = false;
        for (SysRole role : byUserId) {
            if (role.getSvaha() != null && role.getSvaha()) {
                isAdmin = true;
                break;
            }
        }
        if (isAdmin) {
            userId = null;
        }
        return menuDao.countMenuByUserIdAndSysId(userId, sysId);
    }

//    public List<SysPosition> findUserPositionListByUid(Long userId) {
//        return baseMapper.findUserPositionList(userId);
//    }

    // 是否可以停用职位

//    @Transactional(rollbackFor = Exception.class)
//    public Boolean resetUserPositionList(Long userId, List<SysPosition> positionIdList) {
//        if (positionIdList == null) {
//            return false;
//        }
//        baseMapper.purgeByUserId(userId);
//        Integer integer = baseMapper.insertUserPositionList(userId, positionIdList, SysUserUtil.curUserId());
//        return SqlHelper.retBool(integer);
//    }

    //------------------------------------用户-角色相关--------------------------------------

//    public boolean canFreezePost(Long postId) {
//        int i = baseMapper.countPostsRefByUser(postId);
//        return i == 0;
//    }

    public List<SysRole> findUserRoleListByUid(Long userId) {
        return baseMapper.findUserRoleList(userId);
    }

    //设置用户的角色和职位
    public void fillUserPostsAndRoles(SysUser user) {
        user.setRoleList(findUserRoleListByUid(user.getId()));
        //List<SysPosition> positionList = findUserPositionListByUid(user.getId());
        //positionList.forEach(position -> positionService.fillPositionInfo(position));
        //user.setPositionList(positionList);
    }

//    public List<SysUser> selectByPositionId(Long id) {
//        return baseMapper.selectByPositionId(id);
//    }

//    public List<SysUser> selectByExample(SysUser example) {
//        Wrapper wrapper = Condition.create();
//        if (!StringUtils.isEmpty(example.getName())) {
//            wrapper.like("name", example.getName());
//        }
//        if (!StringUtils.isEmpty(example.getId())) {
//            wrapper.eq("id", example.getId());
//        }
//
//        wrapper.eq("status", GlobalConst.NORMAL);
//
//        List<SysUser> list = super.selectList(wrapper);
//        return list;
//    }

    public boolean authValid(String url, Long userId, Long sysId) {
        log.info("authValid      url = " + url + "  userId = " + userId + "   sysId = " + sysId);
        //查询一个系统下的所有菜单
        List<SysMenu> menuList = menuDao.findMenuByUserIdAndSysId(null, sysId, false);
        if (menuList.size() == 0) {
            return true;
        }
        for (SysMenu sysMenu : menuList) {
            AntPathMatcher pathMatcher = new AntPathMatcher();
            if (pathMatcher.match(sysMenu.getUrl(), url)) {
                //匹配用户是否包含权限
                List<SysMenu> userSysMenu = menuDao.findMenuByUserIdAndSysIdAndUrl(userId, sysId,
                    sysMenu.getUrl());
                return userSysMenu.size() > 0;
            }
            return true;
        }
        return true;
    }

    /**
     * 根据id集合查询用户信息
     *
     * @param ids
     * @return
     */
    public ListResponse<SysUser> querySysUserByIds(List<Long> ids) {
        HashMap map = new HashMap();
        map.put("idList", ids);
        List<SysUser> res = baseMapper.querySysUserByIds(map);
        return new ListResponse<SysUser>(res);
    }

    public ListResponse<SysUser> findByName(String name, Boolean accurateQuery) {
        List<SysUser> res = new ArrayList<>();
        if (Boolean.TRUE == accurateQuery) {
            res = baseMapper.selectList(Wrappers.query(SysUser.class).eq("name", name));
        } else {
            res = baseMapper.selectList(Wrappers.query(SysUser.class).like("name", name));
        }
        return RestUtils.buildListResponse(res);
    }

    //设置用户的商户名称
    public void fillUserCommercialName(SysUser user) {
        user.setCommercialName("");//防止该用户没集团信息时，接口不返回commercialName字段
        TCommercial tCommercial = tCommercialService.selectByUserId(user.getId());//根据用户id查询所属集团信息
        if (tCommercial != null) {
            user.setCommercialId(tCommercial.getId());
            user.setCommercialName(tCommercial.getCommName());
            user.setCommIdChain(tCommercial.getIdChain());
        }
    }

    public ListResponse<RoleUserVo> getUserListByRoleId(RoleUserListParam params) {
        IotAssert.isNotNull(params.getRoleId(), "角色ID不能为空");
        IotAssert.isNotNull(params.getPlatform(), "所属平台不能为空");

        if (params.getStart() == null) {
            params.setStart(0L);
        }
        if (params.getSize() == null) {
            params.setSize(10);
        }

        Long total = sysUserRoDs.getRoleUserAmount(params);
        if (total == null || total.equals(0L)) {
            return new ListResponse<>(null, 0L);
        }
        return new ListResponse<>(sysUserRoDs.getRoleUserList(params), total);
    }

    public SysUserPo getUserById(Long userId) {
        return sysUserRoDs.getByUseId(userId);
    }

    public ListResponse<RoleUserVo> getUserByRoleId(String keyWord, Long platform, Long roleId,
        Long size) {
        IotAssert.isNotNull(platform, "请选择所属平台");
        IotAssert.isNotNull(roleId, "请选择所属角色");

        // 充电管理平台、海外版公用账号
        if (platform.equals(Long.valueOf(AppClientType.MGM_FOREIGN_WEB.getCode()))) {
            platform = Long.valueOf(AppClientType.MGM_WEB.getCode());
        }
        return new ListResponse<>(sysUserRoDs.getUserByRoleId(keyWord, platform, roleId, size));
    }

    public BaseResponse batchAddRoleUser(BatchAddRoleUserParam params) {
        IotAssert.isTrue(CollectionUtils.isNotEmpty(params.getUserIdList()), "账号信息不能为空");
        IotAssert.isTrue(params.getRoleId() != null, "角色ID不能为空");
        IotAssert.isTrue(params.getPlatform() != null, "所属平台不能为空");
        List<SysRole> roleList = sysRoleRoDs.getRoleByIdList(List.of(params.getRoleId()),
            params.getPlatform());
        IotAssert.isTrue(CollectionUtils.isNotEmpty(roleList), "角色信息不存在");
        sysUserRoleDao.batchAddRoleUser(params);
        return BaseResponse.success();
    }

    public BaseResponse batchUpdateRoleUser(RoleUserUpdateParam params) {
        sysUserRoleDao.batchUpdateRoleUser(params);
        return BaseResponse.success();
    }

    public List<SysUserCheckParam> checkInDB(List<SysUserCheckParam> list) {
        List<SysUserPo> poList = sysUserRoDs.queryByCheckParam(
            list.stream().distinct().collect(Collectors.toList()));
        Map<String, SysUserPo> map = poList.stream()
            .collect(Collectors.toMap(SysUserPo::getUsername, t -> t, (v1, v2) -> v1));
        return list.stream().peek(e -> {
            SysUserPo temp = map.get(e.getUsername());
            if (temp != null) {
                e.setIsSubsistent(true);
                e.setSysUserId(temp.getId());
            } else {
                e.setIsSubsistent(false);
            }
        }).collect(Collectors.toList());
    }

    public ListResponse<SysUserVo> sameCorpWxAppNameSysUser(
        SysUser sysUser, QuerySysUserRequest param) {
        IotAssert.isTrue(param.getStart() != null && param.getSize() != null,
            "请传入分页参数");
        List<SysUser> sysUserList = baseMapper.sameCorpWxAppNameSysUser(param);
        long total = 0;
        if (Boolean.TRUE.equals(param.getTotal())) {
            total = baseMapper.countSameCorpWxAppNameSysUser(param);
        }

        if (CollectionUtils.isEmpty(sysUserList)) {
            return RestUtils.buildListResponse(List.of(), 0);
        }

        final List<SysUserVo> result = sysUserList.stream().map(x -> {
            SysUserVo vo = new SysUserVo();
            BeanUtils.copyProperties(x, vo);
            return vo;
        }).collect(Collectors.toList());
        return RestUtils.buildListResponse(result, total);
    }

    public List<SysUser> getUserSimpleList(QuerySysUserRequest param) {
        param.setStart(Optional.ofNullable(param.getStart()).orElse(0L));
        param.setSize(Optional.ofNullable(param.getSize()).orElse(10));

        return sysUserRoDs.getUserSimpleList(param);
    }
}
