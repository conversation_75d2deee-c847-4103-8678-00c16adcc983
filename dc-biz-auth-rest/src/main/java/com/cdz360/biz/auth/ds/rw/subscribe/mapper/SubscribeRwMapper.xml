<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.ds.rw.subscribe.mapper.SubscribeRwMapper">

    <insert id="add" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO t_subscribe (title,
                                 days,
                                 chargingMode,
                                 fee,
                                 `desc`,
                                 fileInfo,
                                 createTime,
                                 updateTime,
                                 createBy)
        VALUES (#{title},
                #{days},
                #{chargingMode},
                #{fee},
                #{desc},
                #{fileInfo, typeHandler=com.cdz360.biz.auth.ds.MybatisJsonTypeHandler},
                now(),
                now(),
                #{createBy})
    </insert>
    <insert id="addSubComm">
        INSERT INTO t_subscribe_comm(subId,topCommId,commId)
        VALUES
        <foreach collection="commIdList" separator="," item="commId">
            (#{subId},#{topCommId},#{commId})
        </foreach>
        ON DUPLICATE KEY UPDATE
        enable=true
    </insert>
    <insert id="addSubRole">
        INSERT INTO t_subscribe_role(subId,roleId)
        VALUES
        <foreach collection="roleIdList" separator="," item="roleId">
            (#{subId},#{roleId})
        </foreach>
        ON DUPLICATE KEY UPDATE
        enable=true
    </insert>
    <insert id="createOrder">
        INSERT INTO
            t_subscribe_order
            (subId,payNo,sysUid,days,unitPrice,amount,commId)
        VALUES
            (#{subId},#{payNo},#{sysUid},#{days},#{unitPrice},#{amount},#{commId})
    </insert>
    <insert id="createOrderUser">
        INSERT INTO
            t_subscribe_order_user_role
            (payNo,roleId,userId)
        VALUES
        <foreach collection="mapList" separator="," item="map">
            (#{payNo},#{map.roleId},#{map.userId})
        </foreach>
    </insert>
    <insert id="createOrderRole">
        INSERT INTO
        t_subscribe_order_role
        (payNo,roleId)
        VALUES
        <foreach collection="roleIdList" separator="," item="roleId">
            (#{payNo},#{roleId})
        </foreach>
    </insert>
    <update id="update">
        UPDATE t_subscribe SET
        <if test="title != null">
            title = #{title},
        </if>
        <if test="days != null">
            days = #{days},
        </if>
        <if test="chargingMode != null">
            chargingMode = #{chargingMode},
        </if>
        <if test="fee != null">
            fee = #{fee},
        </if>
        <if test="desc != null">
            `desc` = #{desc},
        </if>
        <if test="fileInfo != null">
            fileInfo = #{fileInfo,typeHandler=com.cdz360.biz.auth.ds.MybatisJsonTypeHandler},
        </if>
        <if test="updateBy != null">
            updateBy = #{updateBy},
        </if>
        updateTime = now()
        WHERE id = #{id}
    </update>
    <update id="updateSubComm">
        update t_subscribe_comm set enable = false where subId=#{subId}
    </update>
    <update id="updateSubRole">
        update t_subscribe_role set enable = false where subId=#{subId}
    </update>
    <update id="updateStatus">
        update t_subscribe set status=ABS(status -1),updateBy=#{updateBy} where id=#{subId}
    </update>
    <update id="updateSubOrder">
        update t_subscribe_order set status=#{status},payChannel=#{payChannel} where payNo=#{payNo}
    </update>
    <update id="updateExpireTimeByPayNo">
        UPDATE t_subscribe_order_user_role tsou,
            sys_user_role sur
        SET tsou.expireTime = sur.expireTime
        WHERE
            tsou.userId = sur.user_id
          AND tsou.roleId = sur.role_id
          AND tsou.payNo = #{payNo}
    </update>
    <update id="updateNoteByNo">
        update t_subscribe_order set note=#{note} where payNo=#{payNo}
    </update>


</mapper>