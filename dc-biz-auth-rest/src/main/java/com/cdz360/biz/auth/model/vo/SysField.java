package com.cdz360.biz.auth.model.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import java.io.Serializable;

/**
 * <p>
 * 数据权限，数据类型
 * </p>
 *
 * <AUTHOR>
  * @since 2018-12-26
 */
@TableName("sys_field")
public class SysField extends Model<SysField> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 1用户2岗位
     */
    @TableField("subject_type")
    private Integer subjectType;
    /**
     * 维度名
     */
    private String name;
    /**
     * 维度code
     */
    private String code;
    private String api;
    private Integer status;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getSubjectType() {
        return subjectType;
    }

    public void setSubjectType(Integer subjectType) {
        this.subjectType = subjectType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getApi() {
        return api;
    }

    public void setApi(String api) {
        this.api = api;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "SysField{" +
                ", id=" + id +
                ", subjectType=" + subjectType +
                ", name=" + name +
                ", code=" + code +
                ", api=" + api +
                ", status=" + status +
                "}";
    }
}
