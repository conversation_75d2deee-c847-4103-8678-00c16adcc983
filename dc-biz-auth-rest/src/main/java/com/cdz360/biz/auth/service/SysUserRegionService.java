package com.cdz360.biz.auth.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cdz360.biz.auth.dao.SysUserRegionDao;
import com.cdz360.biz.auth.model.vo.SysUserRegion;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class SysUserRegionService extends ServiceImpl<SysUserRegionDao, SysUserRegion> {

    @Transactional
    public boolean insertOrUpdateByUid(SysUserRegion sysUserRegion) {
        boolean result = false;
        QueryWrapper<SysUserRegion> eq = Wrappers.query();
        eq.eq("user_id", sysUserRegion.getUserId());
//        Wrapper eq = Condition.create().eq("user_id", sysUserRegion.getUserId());
        SysUserRegion region = this.getBaseMapper().selectOne(
            eq);
        if (region != null) {
            result = this.update(sysUserRegion, eq);
        } else {
            result = this.getBaseMapper().insert(sysUserRegion) > 0;
        }
        return result;
    }

}
