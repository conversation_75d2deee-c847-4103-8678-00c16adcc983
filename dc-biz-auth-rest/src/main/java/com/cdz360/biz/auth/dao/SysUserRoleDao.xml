<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cdz360.biz.auth.dao.SysUserRoleDao">
    <insert id="insertUserRoleIdList">
        INSERT INTO sys_user_role(user_id,role_id,create_time)
        VALUES
        <foreach collection="roleIdList" separator="," item="roleId">
            (#{userId},#{roleId},now())
        </foreach>
        ON DUPLICATE KEY UPDATE
        update_Time = now()
    </insert>
    <insert id="batchAddRoleUser">
        INSERT INTO sys_user_role(user_id,role_id,expireTime,create_time,update_time)
        VALUES
        <foreach collection="userIdList" separator="," item="userId">
            (#{userId},#{roleId},#{expireTime},now(),now())
        </foreach>
        ON DUPLICATE KEY UPDATE
        update_Time = now(),
        enable=true,
        expireTime=#{expireTime}
    </insert>
    <insert id="batchAddRoleUserByUserId">
        INSERT INTO sys_user_role(user_id,role_id,expireTime,create_time,update_time)
        VALUES
        <foreach collection="roleIdList" separator="," item="roleId">
            (#{userId},#{roleId},#{expireTime},now(),now())
        </foreach>
        ON DUPLICATE KEY UPDATE
        update_Time = now(),
        enable=true,
        expireTime=#{expireTime}
    </insert>
    <insert id="batchAddRoleUserByList">
        INSERT INTO sys_user_role(user_id,role_id,expireTime,create_time,update_time)
        VALUES
        <choose>
            <when test="days==0">
                <foreach collection="userRoleList" separator="," item="item">
                    (#{item.userId},#{item.roleId},null,now(),now())
                </foreach>
            </when>
            <otherwise>
                <foreach collection="userRoleList" separator="," item="item">
                    (#{item.userId},#{item.roleId},DATE_ADD(DATE_FORMAT(now(),'%Y-%m-%d'),INTERVAL #{days}
                    DAY),now(),now())
                </foreach>
            </otherwise>
        </choose>

        ON DUPLICATE KEY UPDATE
        update_Time = now(),
        enable=true,
        <choose>
            <when test="days == 0">
                expireTime=null
            </when>
            <otherwise>
                expireTime=DATE_ADD(expireTime,INTERVAL #{days} DAY)
            </otherwise>
        </choose>
    </insert>
    <update id="batchUpdateRoleUser">
        update sys_user_role
        set
        <if test="enable!=null">
            enable = #{enable},
        </if>
        expireTime=#{expireTime},
        update_time = now()
        where user_id in
        <foreach collection="userIdList" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        and role_id=#{roleId}
    </update>
    <update id="batchUpdateRoleUserByUserId">
        update sys_user_role
        set
        <if test="enable!=null">
            enable = #{enable},
        </if>
        expireTime=#{expireTime},
        update_time = now()
        where role_id in
        <foreach collection="roleIdList" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
        and user_id=#{userId}
    </update>
    <delete id="batchDelUserRole">
        delete from sys_user_role
        where
        user_id in
        <foreach collection="userIdList" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        and role_id in
        <foreach collection="roleIdList" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
    </delete>
    <select id="findRoleIdListByUserId" resultType="java.lang.Long">
        SELECT role_id
        FROM sys_user_role
        WHERE user_id = #{userId}
    </select>

    <select id="findRoleIdListByUserIdAndStatus" resultType="java.lang.Long">
        SELECT role_id
        FROM sys_user_role sur
                 LEFT JOIN sys_role sr ON sur.role_id = sr.id
        WHERE sur.user_id = #{userId}
          AND sr.STATUS = #{status}
          AND sur.enable=true  AND (expireTime IS NULL OR expireTime >= DATE_FORMAT(now(),'%Y-%m-%d 00:00:00'))
    </select>


    <select id="findRoleNameListByUserId" resultType="java.lang.String">
        SELECT name
        FROM sys_user_role
                 left join sys_role on sys_user_role.role_id = sys_role.id
        WHERE user_id = #{userId}
          and sys_user_role.enable = true
          and (sys_user_role.expireTime is null or sys_user_role.expireTime >= DATE_FORMAT(now(),'%Y-%m-%d 00:00:00'))
          and sys_role.status = 1
    </select>

    <select id="findRoleNameByroleId" resultType="java.lang.String">
        SELECT name
        FROM sys_user_role
                 left join sys_role on sys_user_role.role_id = sys_role.id
        WHERE user_id = #{userId}
    </select>
    <select id="findRoleIdByUserId" resultType="java.lang.Long">
        SELECT role_id as roleId
        FROM sys_user_role
        WHERE user_id = #{userId} limit 1
    </select>
    <select id="findUserIdByRoleId" resultType="java.lang.Long">
        SELECT user_id as userId
        FROM sys_user_role
        WHERE role_id = #{roleId}
    </select>
    <select id="findOrgIdByUserId" resultType="java.lang.Long">
        SELECT orgId
        FROM t_corp_user_org
        WHERE sysUid = #{userId} limit 1
    </select>

</mapper>