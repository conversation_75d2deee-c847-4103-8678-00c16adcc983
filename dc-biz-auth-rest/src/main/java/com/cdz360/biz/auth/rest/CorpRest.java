package com.cdz360.biz.auth.rest;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.config.CurrentUser;
import com.cdz360.biz.auth.corp.param.BalanceRemindParam;
import com.cdz360.biz.auth.corp.po.BlocUser;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.service.CorpBizService;
import com.cdz360.biz.model.cus.corp.param.ListCorpParam;
import com.cdz360.biz.model.cus.corp.po.CorpOrgPo;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.corp.vo.CorpOrgVO;
import com.cdz360.biz.model.cus.corp.vo.CorpSimpleVo;
import com.cdz360.biz.model.cus.corp.vo.CorpVo;
import com.cdz360.biz.model.sys.vo.CorpGroupTinyVo;
import com.github.pagehelper.Page;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.stream.Collectors;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/corp")
@Tag(name = "企业相关接口", description = "企业")
public class CorpRest {


    @Autowired
    private CorpBizService corpBizService;

    /**
     * 添加企业
     *
     * @param request
     * @param corp
     * @return Long t_corp.id
     */
    @PostMapping("/addCorp")
    public ObjectResponse<Long> addCorp(HttpServletRequest request, @RequestBody CorpPo corp) {
        log.info("获取信息corp={}", corp);
        Long i = this.corpBizService.addCorp(corp);
        log.info("i = {}", i);
        return RestUtils.buildObjectResponse(i);
    }

    @GetMapping("/getOrgList")
    @Operation(summary = "组织列表")
    public ListResponse<CorpOrgVO> getOrgList(
        @CurrentUser SysUser sysUser,
        @RequestParam("corpId") Long corpId, @RequestParam("_index") Integer _index,
        @RequestParam("_size") Integer _size) {
        log.info("sysUser.id = {}", sysUser == null ? null : sysUser.getId());
        Page<CorpOrgVO> corpOrgVOS = corpBizService.getCorpOrgList(sysUser.getId(),
            sysUser.getCorpId(), _index, _size);
        return new ListResponse<>(corpOrgVOS.stream().collect(Collectors.toList()),
            corpOrgVOS.getTotal());
    }

    @GetMapping("/getOrgByLevel")
    @ResponseBody
    public ListResponse<CorpOrgVO> getOrgByLevel(@CurrentUser SysUser sysUser,
        @RequestParam("corpId") Long corpId, @RequestParam("orgLevel") Integer orgLevel) {
        return corpBizService.getCorpOrgByLevel(sysUser.getId(), corpId, orgLevel,
            sysUser.getOrgLevel());
    }

    @GetMapping("/getOrgInfoByLevel")
    public ObjectResponse<CorpOrgPo> getOrgInfoByLevel(@RequestParam("corpId") Long corpId,
        @RequestParam("orgLevel") Integer orgLevel) {
        return corpBizService.getOrgInfoByLevel(corpId, orgLevel);
    }

    /**
     * 新增企业组织
     *
     * @param corpOrgPo
     * @return
     */
    @PostMapping("/addOrUpdateCorpOrg")
    public BaseResponse addOrUpdateCorpOrg(@CurrentUser SysUser sysUser,
        @RequestBody CorpOrgPo corpOrgPo) {
        //企业管理平台 组织名称在本公司唯一
        if (sysUser.getPlatform() == AppClientType.CORP_WEB.getCode()) {
            corpOrgPo.setCorpId(sysUser.getCorpId());
        }
        corpBizService.addOrUpdateCorpOrg(corpOrgPo);
        return new BaseResponse();
    }

    /**
     * 获取当前账户所在组织以及下级组织
     *
     * @return
     */
    @GetMapping("/getOrgByUserId")
    public ListResponse<CorpOrgVO> getOrgByUserId(@CurrentUser SysUser sysUser) {
//        @RequestParam("corpId") Long corpId, @RequestParam("cusId") Integer cusId
        return corpBizService.getOrgByUserId(sysUser.getCorpId(), sysUser.getId());
    }

    @GetMapping("/getCorp")
    public ObjectResponse<CorpPo> getCorp(HttpServletRequest request,
        @RequestParam(value = "corpId") Long corpId) {
        var corp = this.corpBizService.getCorp(corpId);
        return RestUtils.buildObjectResponse(corp);
    }

//    @GetMapping("/getCorpByUid")
//    public ObjectResponse<CorpPo> getCorpByUid(HttpServletRequest request,
//                                          @RequestParam(value = "uid") Long uid) {
//        var corp = this.corpBizService.getCorpByUid(uid);
//        return RestUtils.buildObjectResponse(corp);
//    }

    /**
     * 更新集团用户信息
     *
     * @param blocUser
     * @return
     */
    @ResponseBody
    @PostMapping("/updateBlocUser")
    public ObjectResponse<CorpPo> updateBlocUser(
        @RequestBody BlocUser blocUser) {
        log.info("blocUser = {}", blocUser);
        return corpBizService.updateCorpUser(blocUser);
    }

    @PostMapping("/moveCorp")
    public BaseResponse moveCorp(@RequestParam("corpId") Long corpId,
        @RequestParam("commId") Long commId) {
        log.info("moveCorp: corpId = {}, commId = {}", corpId, commId);
        return corpBizService.moveCorp(corpId, commId);
    }

    /**
     * 获取当前登录账号下企业信息
     *
     * @param request
     * @param param
     * @return
     */
    @PostMapping("/getCorpList")
    public ListResponse<CorpVo> getCorpList(HttpServletRequest request,
        @RequestBody ListCorpParam param) {
        return this.corpBizService.getCorpList(param);
    }

    @GetMapping("/updateCorpEnable")
    public BaseResponse updateCorpEnable(Long corpId) {
        return this.corpBizService.updateCorpEnable(corpId);
    }

    /**
     * 获取当前商户及子商户的企业信息
     *
     * @param commIdChain
     * @return
     */
    @GetMapping("/getCorpByCommId")
    public ListResponse<CorpSimpleVo> getCorpByCommId(
        @RequestParam(value = "commIdChain") String commIdChain,
        @RequestParam(value = "corpId", required = false) Long corpId) {
        return new ListResponse<>(this.corpBizService.getCorpByCommId(commIdChain, corpId));
    }

    /**
     * 根据企业id设置续费提醒金额
     *
     * @param param
     * @return
     */
    @PostMapping("/setRenewReminderAmount")
    public BaseResponse setRenewReminderAmount(@RequestBody BalanceRemindParam param) {
        return corpBizService.setRenewReminderAmount(param);
    }

    /**
     * 企业客户充值后，按条件重置邮件发送状态
     *
     * @param corpId
     * @return
     */
    @GetMapping(value = "/resetEmailSendStatus")
    public BaseResponse resetEmailSendStatus(
        @RequestParam(value = "corpId", required = false) Long corpId,
        @RequestParam(value = "corpUid", required = false) Long corpUid) {
        return corpBizService.resetEmailSendStatus(corpId, corpUid);
    }

    @Operation(summary = "通过UID获取企业信息")
    @GetMapping(value = "/getCorpByUid")
    public ObjectResponse<CorpPo> getCorpByUid(
        @RequestParam(value = "corpUid") Long corpUid) {
        log.info("通过UID获取企业信息: uid = {}", corpUid);
        return RestUtils.buildObjectResponse(corpBizService.getCorpByUid(corpUid));
    }

    @Operation(summary = "通过企业ID获取所属场站组")
    @GetMapping(value = "/getGidsById")
    public ListResponse<String> getGidsById(@RequestParam(value = "corpId") Long corpId) {
        log.info("通过企业ID获取所属场站组: corpId = {}", corpId);
        return corpBizService.getGidsById(corpId);
    }

    @Operation(summary = "通过企业ID列表获取所属场站组")
    @PostMapping(value = "/getCorpGroups")
    public ListResponse<CorpGroupTinyVo> getCorpGroups(@RequestBody List<Long> corpIdList) {
        log.info("通过企业ID列表获取所属场站组: corpIdList.size = {}", corpIdList.size());
        return corpBizService.getCorpGroups(corpIdList);
    }

    /**
     * 定时检查企业客户余额，若低于配置项则推送站内消息
     *
     * @return
     */
    @GetMapping("/renewalReminderJob")
    public BaseResponse renewalReminderJob() {
        return corpBizService.renewalReminderJob();
    }


    @Operation(summary = "更新企业客户是否全额开票")
    @GetMapping("/updateCorpFullInvoice")
    public BaseResponse updateCorpFullInvoice(
        @RequestParam(value = "corpId") Long corpId,
        @RequestParam(value = "fullInvoicing") Boolean fullInvoicing) {
        return this.corpBizService.updateCorpFullInvoice(corpId, fullInvoicing);
    }

}
