<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.ds.ro.sys.mapper.SysUserReportTemplateRoMapper">

    <resultMap id="BASE" type="com.cdz360.biz.model.sys.po.SysUserReportTemplatePo">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="templateName" jdbcType="VARCHAR" property="templateName" />
        <result column="sysUserId" jdbcType="BIGINT" property="sysUserId" />
        <result column="page" jdbcType="INTEGER" property="page"
                typeHandler="com.cdz360.ds.type.EnumTypeHandler" />
        <result column="queryConditions" property="queryConditions"
                typeHandler="com.cdz360.biz.auth.ds.MybatisJsonTypeHandler" />
        <result column="columns" property="columns"
                typeHandler="com.cdz360.biz.auth.ds.MybatisJsonTypeHandler" />
        <result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
        <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>

    <select id="getInfo" resultMap="BASE">
        select
            id,
            templateName,
            sysUserId,
            page,
            queryConditions,
            columns,
            createTime,
            updateTime
        from
            sys_user_report_template
        where
            `sysUserId` = #{sysUserId}
        <if test="page != null">
            and `page` = #{page}
        </if>
    </select>
    
    <select id="countBySysUserId" resultType="java.lang.Long">
        select
            count(*)
        from
            sys_user_report_template
        where
            `sysUserId` = #{sysUserId}
            and `page` = #{page.code}
    </select>

</mapper>