package com.cdz360.biz.auth.service;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.corp.po.CorpOrgPo;
import com.cdz360.biz.auth.dao.AuthGroupDao;
import com.cdz360.biz.auth.dao.SysUserRoleDao;
import com.cdz360.biz.auth.ds.ro.corp.CorpOrgRoDs;
import com.cdz360.biz.auth.ds.ro.corp.CorpRoDs;
import com.cdz360.biz.auth.ds.ro.sys.AccRelativeRoDs;
import com.cdz360.biz.auth.ds.ro.sys.SysRoleRoDs;
import com.cdz360.biz.auth.ds.ro.user.SiteGroupRoDs;
import com.cdz360.biz.auth.ds.ro.user.SiteGroupUserRoDs;
import com.cdz360.biz.auth.ds.ro.user.SysUserRoDs;
import com.cdz360.biz.auth.ds.rw.user.SysUserRwDs;
import com.cdz360.biz.auth.feign.DataCoreFeignClient;
import com.cdz360.biz.auth.model.constant.GlobalConst;
import com.cdz360.biz.auth.model.type.ErrStatus;
import com.cdz360.biz.auth.model.vo.AuthorityGroup;
import com.cdz360.biz.auth.model.vo.SysLoginLog;
import com.cdz360.biz.auth.model.vo.SysRole;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.model.vo.SysUserComercial;
import com.cdz360.biz.auth.model.vo.TCommercial;
import com.cdz360.biz.auth.model.vo.TCommercialManage;
import com.cdz360.biz.auth.model.vo.TCommercialUser;
import com.cdz360.biz.auth.service.corpWx.CorpWxService;
import com.cdz360.biz.auth.sys.vo.AccRelativeVo;
import com.cdz360.biz.auth.sys.vo.Authority;
import com.cdz360.biz.auth.sys.vo.RelAccount;
import com.cdz360.biz.auth.user.dto.SysUserLoginResult;
import com.cdz360.biz.auth.user.param.BatchUserRoleParam;
import com.cdz360.biz.auth.user.param.SysUserLoginParam;
import com.cdz360.biz.auth.user.po.SysUserPo;
import com.cdz360.biz.auth.user.type.OperationType;
import com.cdz360.biz.auth.user.vo.SiteGroupUserRefVo;
import com.cdz360.biz.auth.utils.IotAssert;
import com.cdz360.biz.auth.utils.RedisUtils;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.corp.vo.CorpOrgVO;
import com.cdz360.biz.model.cus.corp.vo.CorpSimpleVo;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.sys.constant.SiteGroupType;
import com.cdz360.biz.model.sys.param.GetYwUserParam;
import com.cdz360.biz.model.sys.param.SiteGroupSiteParam;
import com.cdz360.biz.model.sys.vo.SiteGroupVo;
import com.cdz360.biz.model.trading.yw.type.CreateYwOrderSourceType;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.slf4j.event.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class SysUserBizService {

    private static final String CARD_MAKE_ROLE_NAME = "制卡角色";
    private final long loginKeyExpire = 3600L;// 10秒过期
    @Autowired
    private SysUserRoDs sysUserRoDs;
    @Autowired
    private CorpOrgRoDs corpOrgRoDs;
    @Autowired
    private SiteGroupRoDs siteGroupRoDs;
    @Autowired
    private SiteGroupUserRoDs siteGroupUserRoDs;
    @Autowired
    private CorpRoDs corpRoDs;
    @Autowired
    private SysUserRwDs sysUserRwDs;
    @Autowired
    private LoginService loginService;
    @Autowired
    private SysLoginLogService loginLogService;
    @Autowired
    private TCommercialService commercialService;
    @Autowired
    private CorpBizService corpBizService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private AuthorityService authorityService;
    @Autowired
    private TCommercialService tCommercialService;
    @Autowired
    private RedisUtils redisUtils;
    @Autowired
    private TCommercialManageService tCommercialManageService;
    @Autowired
    private AuthGroupDao authGroupDao;
    @Autowired
    private SysUserRoleService sysUserRoleService;

    @Autowired
    private CorpWxService corpWxService;

    @Autowired
    private SysRoleRoDs sysRoleRoDs;
    @Autowired
    private SysUserRoleDao sysUserRoleDao;
    @Autowired
    private SysRoleService sysRoleService;
    @Autowired
    private AccRelativeRoDs accRelativeRoDs;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    private static Optional<SysUserVo> ywSysUser(AccRelativeVo accRelative) {
        SysUserVo sysUserVo = new SysUserVo()
            .setId(accRelative.getSysUid())
            .setUsername(accRelative.getUserName())
            .setName(accRelative.getName())
            .setPhone(accRelative.getPhone())
            .setCommId(accRelative.getCommId())
            .setCommName(accRelative.getCommName());
        return Optional.ofNullable(sysUserVo);
    }

    @Transactional
    public SysUserLoginResult doLogin(SysUserLoginParam param, boolean isCheckPassword) {
        SysUserPo storedUser = null;
        // 三方登录
        if (null != param.getEnv()) {
            if (StringUtils.isBlank(param.getMobile())) {
                throw new DcArgumentException("登录失败，当前用户手机号有误");
            }
            param.setTrust(true);
            String mobile = param.getMobile();

            storedUser = this.sysUserRoDs.getOneByPhoneAndPlatform(
                param.getTopCommId(), mobile, param.getPlatform());
            IotAssert.isNotNull(storedUser, "登录失败，平台账号不存在，请联系管理员创建");
        }

        SysUserLoginResult resp = new SysUserLoginResult();

        if (null == storedUser) { // 普通账号登录
            storedUser = this.sysUserRoDs.getByUsername(param.getUsername(), param.getPlatform());
        }

        //用户不存在
        if (storedUser == null) {
            log.warn("帐号不存在. username = {}, platform = {}",
                param.getUsername(), param.getPlatform());
            throw new DcServiceException("帐号不存在", Level.WARN);
        }
        SysUser su = new SysUser();
        su.setId(storedUser.getId());
        su.setLimitAcc(storedUser.getLimitAcc());
        //判断是否有输错密码的记录
        SysLoginLog logx = loginLogService.getBaseMapper().selectById(storedUser.getId());
        if (logx != null) {
            loginLogService.checkTimeOver(logx.getLastLoginTime(), su);
        }

        if (storedUser.getStatus() != null && storedUser.getStatus() == GlobalConst.LOCKED) {
            log.warn("帐号被锁定. storedUser = {}", storedUser);
            throw new DcServiceException("帐号被锁定,请稍后再重试", Level.WARN);
        }
        if (param.getPlatform() == AppClientType.CORP_WEB.getCode()) {
            CorpPo corp = this.corpRoDs.getCorpById(storedUser.getCorpId());
            if (corp == null) {
                log.error("数据错误,企业不存在 sysUser = {}", storedUser);
                throw new DcServiceException("数据错误,企业不存在");
            }

            CorpOrgPo corpOrg = this.corpOrgRoDs.getCorpOrgBySysUid(storedUser.getId(),
                storedUser.getCorpId());
            if (corpOrg == null) {
                log.error("数据错误,企业组织不存在 sysUser = {}", storedUser);
                throw new DcServiceException("数据错误,企业组织不存在");
            }

            ListResponse<CorpOrgVO> orgInfo = corpBizService.getOrgByUserId(corp.getId(),
                storedUser.getId());
            if (orgInfo.getData() != null) {
                List<Long> orgIds = orgInfo.getData().stream().map(CorpOrgVO::getId)
                    .collect(Collectors.toList());
                su.setOrgIds(orgIds);
            } else {
                log.error("数据错误,企业组织不存在 corpOrg = {}", corpOrg);
                throw new DcServiceException("数据错误,企业组织不存在");
            }

            su.setCorpPo(corp);
            su.setCorpId(corp.getId());
            su.setOrgId(corpOrg.getId());
            su.setOrgLevel(corpOrg.getOrgLevel());
            su.setGids(siteGroupUserRoDs.getGidListByUid(su.getId()));   // 用户帐号对应的场站组

            resp.setOrgId(corpOrg.getId())
                .setOrgName(corpOrg.getOrgName())
                .setBlocUserName(corp.getCorpName())
                .setOrgLevel(corpOrg.getOrgLevel());
        } else if (
            List.of(AppClientType.MGM_WEB.getCode(),
                    AppClientType.ESS_MGM.getCode(),
                    AppClientType.COMM_ESS_MGM.getCode())
                .contains(param.getPlatform())
//            param.getPlatform() == AppClientType.MGM_WEB.getCode()
        ) { //充电  光伏 储能  光储充共用
            //获得用户权限列表
            List<Long> roleIdListByUserId = sysUserRoleService.findEnabledRoleIdListByUserId(
                storedUser.getId());
            if (CollectionUtils.isNotEmpty(roleIdListByUserId)) {

                List<AuthorityGroup> groupListByRoleIdList = authGroupDao.getGroupListByRoleIdList(
                    roleIdListByUserId);

                if (CollectionUtils.isNotEmpty(groupListByRoleIdList)) {

                    List<Authority> authorities =
                        authorityService.getAuthorityListByGroupIdList(
                            groupListByRoleIdList.stream()
                                .map(AuthorityGroup::getId).collect(Collectors.toList()));
                    su.setAuthorityList(authorities);
                } else {
                    //FIXME 之后会去除
                    List<Authority> authorities = authorityService.getAuthorityListByUid(
                        storedUser.getId());
                    su.setAuthorityList(authorities);
                }
            }

            //获得可切换的用户
            List<RelAccount> relAccountList = accRelativeRoDs.getRelUserList(storedUser.getId());
            su.setRelAccountList(relAccountList);

            //获得企业开票审核权限的商户ID
            List<String> roleCommIdByUserId = authGroupDao.getRoleCommIdByUserId(
                storedUser.getId());

            Set<String> commIdsSet = new HashSet<>();
            for (String str : roleCommIdByUserId) {
                String[] split = str.split(",");
                Set<String> collect = new HashSet<>(Arrays.asList(split));
                commIdsSet.addAll(collect);
            }
            final String CommIds = CollectionUtils.join(commIdsSet, ",");

            su.setCorpAuthCommIds(CommIds);

            su.setGids(siteGroupUserRoDs.getGidListByUid(su.getId()));   // 用户帐号对应的场站组
        }

        su.setStatus(storedUser.getStatus());
        su.setPlatform(param.getPlatform());
        su.setTopCommId(storedUser.getTopCommId());
        su.setCommId(storedUser.getCommId());
        su.setName(storedUser.getName());
        su.setUsername(param.getUsername());
        su.setPassword(param.getPassword());
        su.setPhone(storedUser.getPhone());
        su.setNickname(storedUser.getNickname());
        su.setWxOpenId(storedUser.getWxOpenId());
        if (storedUser.getCommId() != null) {
            TCommercial comm = commercialService.selectById(storedUser.getCommId());
            su.setCommIdChain(comm.getIdChain());
            if (param.getPlatform() == AppClientType.MGM_WEB.getCode()) {
                //账号登录 获取左上角的 logo,商户名称，如果本商户未设置，则获取上一级商户设置的
                if (StringUtils.isEmpty(comm.getCommLogo())) {
                    TCommercial commInfo = tCommercialService.getCommPlatByIdChain(
                        comm.getIdChain());
                    if (null != commInfo) {
                        su.setCommPlatform(commInfo.getPlatform());
                        su.setPlatformName(commInfo.getPlatformName());
                        su.setCommLogo(commInfo.getCommLogo());
                    }
                } else {
                    su.setCommPlatform(comm.getPlatform());
                    su.setPlatformName(comm.getPlatformName());
                    su.setCommLogo(comm.getCommLogo());
                }
            }
        }
        if (isCheckPassword) {
            if (Boolean.TRUE.equals(param.getTrust())) {
                su.setUsername(storedUser.getUsername());
                su.setSalt(storedUser.getSalt());
            } else {
                boolean equals = this.checkPassword(su, storedUser);

                if (!equals) {
                    log.warn("帐号/密码错误. param = {}", param);
                    throw new DcServiceException("帐号/密码错误", Level.WARN);
                }
            }
        }

        if (AppClientType.CARD_MAKER_PC.equals(param.getClientType())) {
            log.info("制卡软件登陆,检查权限");
            SysRole byRoleName = sysRoleService.findByRoleName(CARD_MAKE_ROLE_NAME);
            IotAssert.isNotNull(byRoleName, "未配置【制卡角色】，登陆失败。");
            List<Long> roleIdListByUserId = sysUserRoleService.findEnabledRoleIdListByUserId(
                storedUser.getId());
            IotAssert.isTrue(roleIdListByUserId.contains(byRoleName.getId()),
                "该用户未赋予【制卡角色】，登陆失败。");
        }

        loginLogService.getBaseMapper().deleteById(storedUser.getId());
        sysUserRwDs.updateLastLoginTime(storedUser.getId());
        if (null != param.getClientType() &&
            List.of(AppClientType.MGM_WX_LITE, AppClientType.COMM_ESS_APP)
                .contains(param.getClientType())) {
            // 桩管家小程序兼容
            su.setPlatform(param.getClientType().getCode());
            su.setGids(siteGroupUserRoDs.getGidListByUid(su.getId()));   // 用户帐号对应的场站组
        }
        String token = loginService.updateToken(su);
        resp.setUsername(storedUser.getUsername())
            .setName(storedUser.getName())
            .setPhone(storedUser.getPhone())
            .setTopCommId(storedUser.getTopCommId())
            .setCommId(storedUser.getCommId())
            .setToken(token)
            .setId(storedUser.getId())
            .setWxOpenId(storedUser.getWxOpenId())
            .setPlatform(su.getPlatform())
            .setPlatformName(su.getPlatformName())
            .setCommLogo(su.getCommLogo())
            .setCommPlatform(su.getCommPlatform());
        return resp;
    }

    public SysUserLoginResult switchUser(SysUser user, Long targetSysUid) {
        SysUserPo sysUserPo = sysUserRoDs.getByUseId(targetSysUid);
        if (null == sysUserPo) {
            throw new DcArgumentException("目标用户已失效, 请联系管理员");
        }

        RelAccount relAccount = accRelativeRoDs.getRelUser(user.getId(), targetSysUid);
        IotAssert.isNotNull(relAccount, "切换目标账户不存在");

        // 秘密登录
        SysUserLoginParam param = new SysUserLoginParam();
        param.setUsername(sysUserPo.getUsername())
            .setPassword(null)
            .setClientType(AppClientType.MGM_WEB)
            .setPlatform(AppClientType.MGM_WEB.getCode());
        return this.doLogin(param, Boolean.FALSE);
    }

    /**
     * 企业平台免密登陆登录 参考 doLogin 逻辑
     *
     * @param corpId
     * @return
     */
    public String authLogin(Long corpId, String idChain) {

        log.info("在idChain: {} 寻找corp: {}", idChain, corpId);

        List<CorpSimpleVo> corpByCommId = corpRoDs.getCorpByCommId(idChain, null, null);
        IotAssert.isTrue(CollectionUtils.isNotEmpty(corpByCommId), "该商户无关联的企业");
        IotAssert.isTrue(corpByCommId.stream()
                .map(CorpSimpleVo::getCorpId)
                .collect(Collectors.toList())
                .contains(corpId),
            "该企业当前不属于此商户");

        if (corpId == null) {
            log.error("请传入 corpId");
            throw new DcServiceException("请传入 corpId");
        }
        CorpPo corp = this.corpRoDs.getCorpById(corpId);
        if (corp == null) {
            log.error("数据错误,企业不存在 corpId = {}", corpId);
            throw new DcServiceException("数据错误,企业不存在");
        }

        CorpOrgPo corpOrg = this.corpOrgRoDs.getCorpOrgBySysUid(corp.getSysUid(), corpId);
        if (corpOrg == null) {
            log.error("数据错误,企业组织不存在 sysUserId = {}, corpId = {}", corp.getSysUid(),
                corpId);
            throw new DcServiceException("数据错误,企业组织不存在");
        }

        SysUserPo storedUser = this.sysUserRoDs.getByUseId(corp.getSysUid());
        if (storedUser == null) {
            log.warn("帐号不存在. sysUserId = {}", corp.getSysUid());
            throw new DcServiceException("帐号不存在", Level.WARN);
        }

        SysUser su = new SysUser();

        ListResponse<CorpOrgVO> orgInfo = corpBizService.getOrgByUserId(corp.getId(),
            storedUser.getId());
        if (orgInfo.getData() != null) {
            List<Long> orgIds = orgInfo.getData().stream().map(CorpOrgVO::getId)
                .collect(Collectors.toList());
            su.setOrgIds(orgIds);
        } else {
            log.error("数据错误,企业组织不存在 corpOrg = {}", corpOrg);
            throw new DcServiceException("数据错误,企业组织不存在");
        }

        su.setId(storedUser.getId());
        su.setCorpPo(corp);
        su.setCorpId(corp.getId());
        su.setOrgId(corpOrg.getId());
        su.setOrgLevel(corpOrg.getOrgLevel());
        su.setStatus(storedUser.getStatus());
        su.setPlatform(AppClientType.CORP_WEB.getCode());
        su.setTopCommId(storedUser.getTopCommId());
        su.setCommId(storedUser.getCommId());
        su.setName(storedUser.getName());
        su.setUsername(storedUser.getUsername());
//        su.setPassword(param.getPassword());
        su.setPhone(storedUser.getPhone());
        if (storedUser.getCommId() != null) {
            TCommercial comm = commercialService.selectById(storedUser.getCommId());
            su.setCommIdChain(comm.getIdChain());
        }
//        boolean equals = this.checkPassword(su, storedUser);
        su.setSalt(storedUser.getSalt());
        su.setGids(siteGroupUserRoDs.getGidListByUid(su.getId())); // 用户帐号对应的场站组

//        loginLogService.deleteById(storedUser.getId());
        sysUserRwDs.updateLastLoginTime(storedUser.getId());
        String token = loginService.getTokenAndSave(su);

        SysUserLoginResult ret = new SysUserLoginResult();
        ret.setOrgId(corpOrg.getId())
            .setOrgName(corpOrg.getOrgName())
            .setBlocUserName(corp.getCorpName())
            .setUsername(storedUser.getUsername())
            .setOrgLevel(corpOrg.getOrgLevel())
            .setPhone(storedUser.getPhone())
            .setTopCommId(storedUser.getTopCommId())
            .setCommId(storedUser.getCommId())
            .setToken(token).setId(storedUser.getId());

        String loginKey = UUID.randomUUID().toString().replaceAll("-", "");
        if (redisUtils.exist(loginKey)) {
            throw new DcServiceException("存在重复的key，请稍后重试。");
        }

        redisUtils.set(loginKey, ret, loginKeyExpire);

        return loginKey;
    }

    /**
     * 根据key从redis获取登陆信息并删除该key
     *
     * @param key
     * @return
     */
    public SysUserLoginResult getLoginInfoByKey(String key) {
        if (!redisUtils.exist(key)) {
            throw new DcServiceException("不存在key，请确认是否有登陆权限。");
        }
        SysUserLoginResult ret = redisUtils.get(key, SysUserLoginResult.class);
        redisUtils.delete(key);
        return ret;
    }

    public boolean checkPassword(SysUser naked, SysUserPo stored) {
        naked.setSalt(stored.getSalt());
        return naked.hashedPassword().equals(stored.getPassword());
    }

    public void refreshCorpTokenValue(String username) {
        SysUserLoginResult resp = new SysUserLoginResult();

        SysUserPo storedUser = this.sysUserRoDs.getByUsername(username,
            AppClientType.CORP_WEB.getCode());
        //用户不存在
        if (storedUser == null) {
            log.warn("帐号不存在. username = {}, platform = {}",
                username, AppClientType.CORP_WEB.getCode());
            return;
        }
        SysUser su = new SysUser();
        su.setId(storedUser.getId());

        CorpPo corp = this.corpRoDs.getCorpById(storedUser.getCorpId());
        if (corp == null) {
            log.error("数据错误,企业不存在 sysUser = {}", storedUser);
            return;
        }
        CorpOrgPo corpOrg = this.corpOrgRoDs.getCorpOrgBySysUid(storedUser.getId(),
            storedUser.getCorpId());
        if (corpOrg == null) {
            log.error("数据错误,企业组织不存在 sysUser = {}", storedUser);
            return;
        }

        ListResponse<CorpOrgVO> orgInfo = corpBizService.getOrgByUserId(corp.getId(),
            storedUser.getId());
        if (orgInfo.getData() != null) {
            List<Long> orgIds = orgInfo.getData().stream().map(CorpOrgVO::getId)
                .collect(Collectors.toList());
            su.setOrgIds(orgIds);
        } else {
            log.error("数据错误,企业组织不存在 corpOrg = {}", corpOrg);
            return;
        }

        su.setCorpPo(corp);
        su.setCorpId(corp.getId());
        su.setOrgId(corpOrg.getId());
        su.setOrgLevel(corpOrg.getOrgLevel());

        resp.setOrgId(corpOrg.getId())
            .setOrgName(corpOrg.getOrgName())
            .setBlocUserName(corp.getCorpName())
            .setUsername(storedUser.getUsername())
            .setOrgLevel(corpOrg.getOrgLevel());
        su.setStatus(storedUser.getStatus());
        su.setPlatform(AppClientType.CORP_WEB.getCode());
        su.setTopCommId(storedUser.getTopCommId());
        su.setCommId(storedUser.getCommId());
        su.setName(storedUser.getName());
        su.setUsername(username);
//        su.setPassword(param.getPassword());
        su.setPhone(storedUser.getPhone());
        if (storedUser.getCommId() != null) {
            TCommercial comm = commercialService.selectById(storedUser.getCommId());
            su.setCommIdChain(comm.getIdChain());
        }

        loginService.getTokenAndSave(su);
    }

    public BaseResponse edit(TCommercialUser tcu) {

        List<SysUser> suList = sysUserService.checkByUserNameOrPhoneOrEmail(tcu);

        if (!org.springframework.util.CollectionUtils.isEmpty(suList)) {
            for (SysUser su : suList) {
                if (NumberUtils.equals(su.getId(), tcu.getId())) {
                    continue;
                }
                if (tcu.getUsername().equals(su.getUsername())) {
                    log.warn("username 冲突. tcu.id = {}, su.id = {}, su.username = {}",
                        tcu.getId(), su.getId(), su.getUsername());
                    throw new DcServiceException(ErrStatus.USERNAME_CONFLICT.getErrcode(),
                        ErrStatus.USERNAME_CONFLICT.getErrmsg());
//                    return Rez.error(ErrStatus.USERNAME_CONFLICT);
                }
                if (StringUtils.isNotBlank(tcu.getEmail()) &&
                    tcu.getEmail().equals(su.getEmail())) {
                    throw new DcServiceException(ErrStatus.EMAIL_CONFLICT.getErrcode(),
                        ErrStatus.EMAIL_CONFLICT.getErrmsg());
//                    return Rez.error(ErrStatus.EMAIL_CONFLICT);
                }
//                if (tcu.getPhone().equals(su.getPhone())) {
//                    return Rez.error(ErrStatus.PHONE_CONFLICT);
//                }
            }
        }

        if (!StringUtils.isEmpty(tcu.getNewPass())) {
            tcu.setPassword(tcu.getNewPass());
            tcu.setPassword(tcu.hashedPassword());
        }
        tcu.setCommercialRole(null);
        boolean updateUser = sysUserService.updateById(tcu);
        if (tcu.getCommercialRole() != null) {
            SysUserComercial suc = new SysUserComercial();
            suc.setUserId(tcu.getId());
            suc.setCommercialRole(tcu.getCommercialRole());
            tCommercialService.updateUserCommercials(suc);
        }
        return RestUtils.success();
    }

    /**
     * 获取用户openId
     *
     * @return
     */
    public BaseResponse getOpenId(String token, String code) {
        SysUserPo sysUserPo = this.getUserIdByToken(token);
        IotAssert.isNotNull(sysUserPo, "未登录状态");
        IotAssert.isNotNull(code, "code不能为空");
        if (sysUserPo.getWxOpenId() != null) {
            throw new DcServiceException("当前登录账户已绑定微信");
        }

        //获取当前账户配置的appid等信息
        TCommercialManage manageInfo = tCommercialManageService.getTCommercialManage(
            sysUserPo.getTopCommId());
        if (manageInfo == null || manageInfo.getWxLiteMgmAppId() == null) {
            return RestUtils.success();
//            throw new DcServiceException("微信账户信息未配置");
        }
        //通过code获取openId
        String getOpenIdUrl =
            "https://api.weixin.qq.com/sns/jscode2session?appid=" + manageInfo.getWxLiteMgmAppId()
                + "&secret=" + manageInfo.getWxLiteMgmSecret() + "&js_code=" + code
                + "&grant_type=authorization_code";
        log.info("getOpenId={}", getOpenIdUrl);
        JsonNode result = this.sendGet(getOpenIdUrl);
        if (result != null) {
            log.info("获取openId返回数据:res = {}", result);
            String openId = result.get("openid").asText();
            if (com.cdz360.base.utils.StringUtils.isNotBlank(openId)) {
                SysUserPo user = sysUserRoDs.getByOpenId(openId);
                if (user != null) {
                    throw new DcServiceException("该微信已绑定其他账户");
                }
                sysUserRwDs.updateOpenId(sysUserPo.getId(), openId);
            } else {
                throw new DcServiceException(result.get("errmsg").asText());
            }

        } else {
            throw new DcServiceException("获取信息失败");
        }
        return RestUtils.success();
    }

    public ListResponse<String> getUserByIdList(List<Long> userIdList, Long opUid) {
        if (CollectionUtils.isEmpty(userIdList)) {
            throw new DcServiceException("参数错误");
        }
        // 将所有关联账号包含在内
        List<Long> relUidList = userIdList.stream()
            .map(uid -> accRelativeRoDs.getRelUserList(uid).stream()
                .filter(Objects::nonNull)
                .filter(i -> i.getSysUid() != null)
                .map(RelAccount::getSysUid))
            .flatMap(Function.identity())
            .distinct()
            .collect(Collectors.toList());

        List<String> list = sysUserRoDs.getUserByIdList(
            Stream.concat(userIdList.stream(), relUidList.stream())
                .distinct()
                .collect(Collectors.toList()), opUid);
        return new ListResponse<>(list);
    }

    /**
     * 通过token获取用户id
     *
     * @param token
     * @return
     */
    public SysUserPo getUserIdByToken(String token) {
        log.info("token获取用户信息 ~> token:{}", token);
        String json = loginService.getUserJson(token);
        if (json == null) {
            log.warn("token获取用户信息 ~> token验证失败:{}", token);

        }
        return JsonUtils.fromJson(json, SysUserPo.class);
    }

    public JsonNode sendGet(String url) {
        try {
            URL requestUrl = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) requestUrl.openConnection();
            connection.setRequestMethod("GET");
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.connect();
            InputStream inputStream = connection.getInputStream();
            int size = inputStream.available();
            byte[] bs = new byte[size];
            inputStream.read(bs);
            String message = new String(bs, "UTF-8");
            JsonNode jsonObject = JsonUtils.fromJson(message);
            return jsonObject;
        } catch (Exception e) {
            log.info("请求失败", e.getMessage(), e);
            return null;
        }
    }

    public ObjectResponse<SysUserPo> getUserInfoById(Long sysUid) {
        log.info("用户信息:sysUid={}", sysUid);
        SysUserPo sysUser = sysUserRoDs.getByUseId(sysUid);
        return new ObjectResponse<>(sysUser);
    }

    public ListResponse<SysUserVo> getUserInfoByIdList(List<Long> list) {
        log.info("用户信息列表:id list={}", list);
        return new ListResponse<>(sysUserRoDs.findByUserId(list, false));
    }

    public List<SysUserVo> ywUserList() {
//        // 获取运维角色
//        SysRole ywRole = sysRoleService.findByRoleName("现场运维角色");
//        if (null == ywRole) {
//            log.warn("现场运维人员角色不存在: 默认[现场运维角色], 请确认角色设置是否正确");
//            throw new DcServiceException("现场运维角色不存在");
//        }

        // 通过角色ID获取用户列表
        List<Long> userIdList = accRelativeRoDs.getValidSysUid();
        if (CollectionUtils.isEmpty(userIdList)) {
            throw new DcServiceException("现场运维人员不存在");
        }

        return sysUserRoDs.findByUserId(userIdList, true);
    }

    public Optional<SysUserVo> getYwUser(GetYwUserParam param) {
        if (param == null || param.getUid() == null || StringUtils.isEmpty(param.getSiteId())
            || param.getSiteCommId() == null) {
            throw new DcServiceException("参数错误");
        }
        String siteId = param.getSiteId();
        Long siteCommId = param.getSiteCommId();
        // 创建人是否是运维
        AccRelativeVo accRelative = accRelativeRoDs.getAccRelative(param.getUid(), true);

        // 若不是设备故障报修，则优先派单给uid
        if (BooleanUtils.isFalse(param.getEquipRepairEntry())) {
            if (null != accRelative) {
                if (CollectionUtils.isNotEmpty(accRelative.getSiteIdList()) &&
                    accRelative.getSiteIdList().contains(siteId)) {
                    return ywSysUser(accRelative);
                }

                Optional<RelAccount> first = accRelative.getRelUserNameList().stream()
                    .filter(rel -> CollectionUtils.isNotEmpty(rel.getSiteIdList()))
                    .filter(rel -> rel.getSiteIdList().contains(siteId))
                    .max(Comparator.comparing(RelAccount::getPriority));
                if (first.isPresent()) {
                    SysUserVo byUseId2 = sysUserRoDs.getByUseId2(first.get().getSysUid(), true);
                    return Optional.of(byUseId2);
                }
            }
        }

        AccRelativeVo accRelativeVo;
        // 获取场站所属场站组
        ListResponse<String> response = dataCoreFeignClient.findSiteGroupSiteBySiteId(
            new SiteGroupSiteParam().setSiteIdList(List.of(siteId)));
        if (response != null && CollectionUtils.isNotEmpty(response.getData())) {
            List<String> gids = response.getData();
            // 筛选出最高优先级的运维场站组的adminUid
            Long adminUid = siteGroupRoDs.getHighProrityAdminUid(List.of(SiteGroupType.YW),
                gids);
            if (adminUid == null || adminUid < 1) {
                log.error("场站未配置运维负责人 siteId = {}", siteId);
                return Optional.empty();
            }
            // adminUid作为派单默认接收人
            accRelativeVo = accRelativeRoDs.getAccRelativeVoBySysUid(adminUid);
            if (accRelativeVo == null) {
                log.error("场站未配置运维负责人 siteId = {}", siteId);
                return Optional.empty();
            }
            return ywSysUser(accRelativeVo);
        }

        // 当前创建人是运维
        if (null != accRelative) {
            return ywSysUser(accRelative);
        }

        /*
        // 获取所属商户一样的运维人员
        accRelativeVo = accRelativeRoDs.getAccRelativeByCommId(siteCommId);
        if (null != accRelativeVo) {
            return ywSysUser(accRelativeVo);
        }

        // 商户链上的运维
        accRelativeVo = accRelativeRoDs.getAccRelativeByCommIdChain(siteCommId);
        if (null != accRelativeVo) {
            return ywSysUser(accRelativeVo);
        }
        */

        return Optional.empty();
//        throw new DcServiceException("不存在可用的运维人员");
    }

    /**
     * 0) 运维 1) C段客户故障报修 -- C端客户 2) 账户所属商户与顶级商户不一样 -- 商户 3) 账户所属商户与顶级商户一样: ① 有客户角色 -- 客服 ② 没有客服角色 --
     * 其他
     *
     * @param uid 系统用户ID
     * @return
     */
    public CreateYwOrderSourceType getYwOrderSrc(Long uid) {
        // 判断是否是商户
        SysUserPo user = sysUserRoDs.getByUseId(uid);
        if (null == user) {
            throw new DcArgumentException("系统用户ID无效");
        }

        // 判断是否为运维(t_acc_relative)
        AccRelativeVo accRelative = accRelativeRoDs.getAccRelative(uid, true);
        if (null != accRelative) {
            return CreateYwOrderSourceType.YW_SRC;
        }

        if (!user.getTopCommId().equals(user.getCommId())) {
            return CreateYwOrderSourceType.COMM_SRC;
        }

        List<SysRole> roles = sysRoleService.getRoleByUserId(uid);
        if (CollectionUtils.isNotEmpty(roles) &&
            roles.stream().anyMatch(r -> "客服".equals(r.getName()))) {
            return CreateYwOrderSourceType.KF_SRC;
        }

        return CreateYwOrderSourceType.OTHER_SRC;
    }

    @Transactional
    public BaseResponse batchUpdate(BatchUserRoleParam params) {
        IotAssert.isNotNull(params.getEditType(), "修改项不存在");
        IotAssert.isNotNull(params.getOperationType(), "操作类型不存在");
        IotAssert.isNotNull(params.getPlatform(), "平台不存在");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(params.getUserIdList()), "账号不能为空");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(params.getRoleIdList()), "角色不能为空");

        List<SysUserPo> userList = sysUserRoDs.getUserByIdAndPlatform(params.getUserIdList(),
            params.getPlatform());
        List<SysRole> roleList = sysRoleRoDs.getRoleByIdList(params.getRoleIdList(),
            params.getPlatform());
        IotAssert.isTrue(CollectionUtils.isNotEmpty(userList), "账号信息为空");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(roleList), "角色信息为空");

        List<Long> roleIdList = roleList.stream().map(SysRole::getId).collect(Collectors.toList());
        List<Long> userIdList = userList.stream().map(SysUserPo::getId)
            .collect(Collectors.toList());

        switch (params.getEditType()) {
            case ROLE_MANAGE:
                if (OperationType.INCREASE.equals(params.getOperationType())) {
                    userIdList.stream()
                        .forEach(e -> sysUserRoleDao.insertUserRoleIdList(e, roleIdList));
                } else if (OperationType.REDUCE.equals(params.getOperationType())) {
                    sysUserRoleDao.batchDelUserRole(userIdList, roleIdList);
                }
                break;
            default:
                throw new DcArgumentException("操作项不存在");

        }
        return RestUtils.success();
    }

    public SysUserPo getByUserNameAndPlatform(String username, AppClientType platform) {
        return sysUserRoDs.getByUserNameAndPlatform(username, platform);
    }

    public List<SysUserPo> getByNameLike(String username) {
        return sysUserRoDs.getByNameLike(username);
    }

    public List<SysUserVo> getUserListById(String commIdChain, Integer platform) {
        return sysUserRoDs.getUserListById(commIdChain, platform);
    }


    /**
     * 使用 sys_user.id获取该用户关联的场站组信息
     */
    public List<SiteGroupVo> getSiteGroupsByUid(Long uid, Integer type) {
        return siteGroupUserRoDs.getSiteGroupsByUid(uid, type);
    }

    public SysUserVo getSysUserVoByUid(Long uid) {
        if (null == uid) {
            return null;
        }
        return sysUserRoDs.getByUseId2(uid, false);
    }

    public List<SiteGroupVo> getSiteGroupsList(String idChain, List<String> gids,
        List<Integer> types) {
        List<SiteGroupVo> siteGroupsList = siteGroupUserRoDs.getSiteGroupsList(idChain, gids,
            types);
        return siteGroupsList;
    }

    public List<SiteGroupUserRefVo> getUidsByGids(List<String> gids) {
        return siteGroupUserRoDs.getUidsByGids(gids);
    }
}
