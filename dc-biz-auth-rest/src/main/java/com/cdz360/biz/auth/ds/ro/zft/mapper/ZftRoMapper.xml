<?xml version="1.0" encoding="UTF-8" ?>

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.cdz360.biz.auth.ds.ro.zft.mapper.ZftRoMapper">



	<resultMap id="RESULT_ZFT_PO" type="com.cdz360.biz.auth.zft.po.ZftPo">

		<id column="id" jdbcType="BIGINT" property="id" />
		<id column="topCommId" jdbcType="BIGINT" property="topCommId" />
		<id column="commId" jdbcType="BIGINT" property="commId" />

		<result column="name" jdbcType="VARCHAR" property="name" />

		<result column="enableBalance" jdbcType="INTEGER" property="enableBalance" />

<!--		<result column="enableRefund" jdbcType="INTEGER" property="enableRefund" />-->

		<result column="ecnyPlatformKey" jdbcType="VARCHAR" property="ecnyPlatformKey" />
		<result column="wxMchId" jdbcType="VARCHAR" property="wxMchId" />
		<result column="wxSubMchId" jdbcType="VARCHAR" property="wxSubMchId" />
		<result column="wxSubMchName" jdbcType="VARCHAR" property="wxSubMchName" />
		<result column="wxCreditServiceId" jdbcType="VARCHAR" property="wxCreditServiceId" />

		<result column="alipayMchId" jdbcType="VARCHAR" property="alipayMchId" />
		<result column="alipaySubMchId" jdbcType="VARCHAR" property="alipaySubMchId" />
		<result column="alipaySubMchName" jdbcType="VARCHAR" property="alipaySubMchName" />
		<result column="alipayCreditServiceId" jdbcType="VARCHAR" property="alipayCreditServiceId" />

		<result column="wxLiteAppId" jdbcType="VARCHAR" property="wxLiteAppId" />
		<result column="wxAndroidAppId" jdbcType="VARCHAR" property="wxAndroidAppId" />
		<result column="wxIosAppId" jdbcType="VARCHAR" property="wxIosAppId" />

		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />

		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />

		<result column="updateOpId" jdbcType="BIGINT" property="updateOpId" />

		<result column="updateOpName" jdbcType="VARCHAR" property="updateOpName" />

		<result column="createOpId" jdbcType="BIGINT" property="createOpId" />

		<result column="createOpName" jdbcType="VARCHAR" property="createOpName" />

	</resultMap>



	<select id="getById"

			resultMap="RESULT_ZFT_PO">
		select * from t_zft where id = #{id}

	</select>
	<select id="findAll"
			parameterType="com.cdz360.biz.auth.zft.param.ListZftParam"
			resultMap="RESULT_ZFT_PO">

		select distinct zft.*
		from t_zft zft
		left join t_commercial comm on comm.zftId = zft.id
		left join t_commercial zftComm on zftComm.id = zft.commId
		where 1=1

		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
			and zftComm.`idChain` like concat(#{commIdChain}, '%')
		</if>

		<if test="null != topCommId">
			and zft.topCommId = #{topCommId}
		</if>

		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank(mchName)">
			and zft.name like concat("%", #{mchName}, "%")
		</if>

		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank(commName)">
			and comm.comm_name like concat("%", #{commName}, "%")
		</if>

		ORDER BY zft.id desc
		<choose>
			<when test="start != null and size != null">
				limit #{start},#{size}
			</when>
			<otherwise>
				limit #{size}
			</otherwise>
		</choose>
	</select>

    <select id="count" resultType="java.lang.Long">
		select count(distinct zft.id)
		from t_zft zft
		left join t_commercial comm on comm.zftId = zft.id
		left join t_commercial zftComm on zftComm.id = zft.commId
		where 1=1

		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
			and zftComm.`idChain` like concat(#{commIdChain}, '%')
		</if>

		<if test="null != topCommId">
			and zft.topCommId = #{topCommId}
		</if>

		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank(mchName)">
			and zft.name like concat("%", #{mchName}, "%")
		</if>

		<if test="@com.cdz360.base.utils.StringUtils@isNotBlank(commName)">
			and comm.comm_name like concat("%", #{commName}, "%")
		</if>
	</select>
    <select id="hasEnableBalance" resultType="java.lang.Long">
		select id from t_zft zft
		where
		topCommId = #{topCommId} and enableBalance = true
		<if test="excludeId != null">
			and id != #{excludeId}
		</if>
		limit 1
	</select>
    <select id="hasName" resultType="java.lang.Long">
		select id from t_zft zft
		where
		topCommId = #{topCommId}
		and name = #{name}
		<if test="excludeId != null">
			and id != #{excludeId}
		</if>
		limit 1
	</select>
  <select id="getByTopCommId" resultType="com.cdz360.biz.auth.zft.po.ZftPo">
		SELECT
		*
		FROM
		auth_center.t_zft
		WHERE
		topCommId = #{topCommId}
		AND commId = #{topCommId}
		limit 1
	</select>

</mapper>

