package com.cdz360.biz.auth.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.cus.corp.param.ChangeByGidsParam;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class UserFeignHystrix implements FallbackFactory<UserFeignClient> {

    @Override
    public UserFeignClient apply(Throwable throwable) {
        log.error("服务[{}]熔断, err = {}",
            DcConstants.KEY_FEIGN_DC_BIZ_USER, throwable.getMessage(), throwable);

        return new UserFeignClient() {
            @Override
            public Mono<BaseResponse> changeByGids(ChangeByGidsParam param) {
                log.error("[{}] 根据企业gids刷新鉴权介质的可用场站, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_USER, param);
                return Mono.just(RestUtils.serverBusy());
            }
        };
    }

    @Override
    public <V> Function<V, UserFeignClient> compose(
        Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(
        Function<? super UserFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER);
        return null;
    }
}
