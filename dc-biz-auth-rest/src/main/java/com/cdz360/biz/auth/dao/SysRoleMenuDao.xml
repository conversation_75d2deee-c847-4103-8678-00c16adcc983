<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.cdz360.biz.auth.dao.SysRoleMenuDao">
    <insert id="saveBatch">
        INSERT INTO sys_role_menu(role_id, menu_id,create_time)
        VALUES
        <foreach collection="menuIds" item="menuId" separator=",">
            (#{roleId},#{menuId},now())
        </foreach>
        ON DUPLICATE KEY UPDATE
        update_time = now()
    </insert>
    <insert id="saveBatchByMenuId">
        INSERT INTO sys_role_menu(role_id, menu_id,create_time)
        VALUES
        <foreach collection="roleIds" item="roleId" separator=",">
            (#{roleId},#{menuId},now())
        </foreach>
    </insert>
    <delete id="deleteByRoleId">
        DELETE FROM sys_role_menu
        WHERE role_id=#{roleId}
    </delete>
    <delete id="deleteByMenuId">
        DELETE FROM sys_role_menu
        WHERE menu_id=#{menuId}
    </delete>
    <delete id="deleteBatch">
        DELETE FROM sys_role_menu
        WHERE role_id=#{roleId} AND
        menu_id IN
        <foreach collection="menuIds" item="mid" separator="," open="(" close=")">
            mid
        </foreach>
    </delete>
    <select id="findMenuIdsByRoleId" resultType="java.lang.Long">
        SELECT menu_id
        FROM sys_role_menu
        WHERE role_id=#{roleId}
    </select>
    <select id="notYeyRoleMenu" resultType="java.lang.Long">
        select distinct rm.role_id
        from sys_role_menu rm
        where rm.menu_id = #{menuId}
        and rm.role_id not in (
            select distinct rm.role_id
            from sys_role_menu rm
            where rm.menu_id = #{pMenuId}
        )
    </select>
    <select id="getMenuList" resultType="java.lang.Long">
        select menu_id from sys_role_menu where role_id= #{roleId}
    </select>
    <select id="getRoleList" resultType="com.cdz360.biz.auth.model.vo.SysRole">
        SELECT
            id,
            name,
            commId
        FROM
            sys_role
        WHERE
            platform = #{platform}
          AND STATUS =1
    </select>
</mapper>