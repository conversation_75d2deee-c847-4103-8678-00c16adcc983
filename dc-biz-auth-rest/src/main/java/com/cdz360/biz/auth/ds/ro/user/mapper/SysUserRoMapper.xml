<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.ds.ro.user.mapper.SysUserRoMapper">

  <sql id="SYS_USER_PO_COLUMNS">
    u.id,
    u.topCommId,
    u.commId,
    u.corpId,
    u.username,
    u.password,
    u.salt,
    u.name,
    u.phone,
    u.email,
    u.platform,
    u.status,
    u.wxOpenId,
    u.nickname,
    u.limitAcc,
    u.last_login_time as lastLoginTime,
    u.create_time as createTime,
    u.update_time as updateTime
  </sql>


  <select id="getByUsername" resultType="com.cdz360.biz.auth.user.po.SysUserPo">
    select
    <include refid="SYS_USER_PO_COLUMNS"/>
    from sys_user as u
    where u.username = #{username}
    and u.platform = #{platform}
  </select>

  <select id="getByUseId" resultType="com.cdz360.biz.auth.user.po.SysUserPo">
    select id, corpId, username, name, phone, topCommId, commId, wxOpenId,
    nickname, salt, email, platform, status, auditGroupId, teamCatalog,corpWxUid
    from sys_user as u
    where u.id = #{userId}
  </select>
  <select id="getByOpenId" resultType="com.cdz360.biz.auth.user.po.SysUserPo">
    select wxOpenId,nickname,username from sys_user where wxOpenId=#{openId}
  </select>
  <select id="getUserIdList" resultType="com.cdz360.biz.auth.user.po.SysUserPo">
    select id,wxOpenId,topCommId from sys_user
    where status=1
    and commId in
    <foreach item="item" collection="commIdList" separator="," open="(" close=")" index="">
      #{item}
    </foreach>
    <choose>
      <when test="platform.code == 99">
        and platform in (20,22)
      </when>
      <otherwise>
        and platform=#{platform.code}
      </otherwise>
    </choose>
    limit #{start},#{size}
  </select>
  <select id="getUserIdListForCorp" resultType="com.cdz360.biz.auth.user.po.SysUserPo">
    select * from (
    select id,wxOpenId,topCommId from sys_user
    where
    platform=20
    and status=1
    and commId in
    <foreach item="item" collection="commIdList" separator="," open="(" close=")" index="">
      #{item}
    </foreach>
    union
    select id,wxOpenId,topCommId from sys_user
    where
    status=1
    and platform=22
    and corpId=#{corpId}
    ) tmp limit #{start},#{size}
  </select>
  <select id="getUserByIdList" resultType="java.lang.String">
    select wxOpenId from sys_user where id in
    <foreach item="item" collection="userIdList" separator="," open="(" close=")" index="">
      #{item}
    </foreach>
    and status = 1
    and wxOpenid is not null and wxOpenid != ''
    <if test="opUid != null">
      and id != #{opUid}
    </if>
  </select>

  <select id="findByUserId" resultType="com.cdz360.biz.model.cus.vo.SysUserVo">
    select u.id, u.username, u.name, u.phone, u.topCommId, u.commId, u.corpWxUid, u.teamCatalog,
    comm.comm_name commName
    from sys_user u
    left join t_commercial comm on comm.id = u.commId
    where u.id in
    <foreach item="item" collection="userIdList" separator="," open="(" close=")">
      #{item}
    </foreach>
    <if test="valid">
      and u.status = 1
    </if>
  </select>

  <select id="countByCondition" resultType="java.lang.Long">
    select
    count(*)
    from
    sys_user
    where
    1=1
    <if test="sysUid != null">
      and id = #{sysUid}
    </if>
    <if test="status != null">
      and status = #{status}
    </if>
  </select>
  <select id="getUserByIdAndPlatform" resultType="com.cdz360.biz.auth.user.po.SysUserPo">
    select * from sys_user where
    platform = #{platform}
    and id in
    <foreach item="item" collection="userIdList" separator="," open="(" close=")" index="">
      #{item}
    </foreach>
  </select>
  <select id="getByUserNameAndPlatform" resultType="com.cdz360.biz.auth.user.po.SysUserPo">
    select * from sys_user where
    platform = #{platform.code}
    and username = #{username}
  </select>

  <select id="getByNameLike" resultType="com.cdz360.biz.auth.user.po.SysUserPo">
    select * from sys_user
    <where>
      name like concat('%', #{username} , '%')
    </where>
    limit 999
  </select>
  <sql id="queryRoleUserList">
    sr.id = #{roleId}
    AND su.status = 1
    AND su.platform = #{platform}
    AND sur.enable=true
    <if test="commId!=null">
      AND su.commId = #{commId}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(keyWord)">
      AND ( su.username LIKE concat('%', #{keyWord} , '%') OR su.NAME LIKE concat('%', #{keyWord} ,
      '%') OR su.phone LIKE concat('%', #{keyWord} , '%'))
    </if>
    <choose>
      <when test="searchType!= null and searchType == 'ALL'">
        AND (expireTime IS NULL OR expireTime >= DATE_FORMAT(now(),'%Y-%m-%d 00:00:00'))
      </when>
      <otherwise>
        <choose>
          <when test="searchType!=null and searchType == 'IN_EXPIRE'">
            <if test="startTime != null">
              <![CDATA[ and expireTime >= #{startTime} ]]>
            </if>
            <if test="endTime != null">
              <![CDATA[ and expireTime <= #{endTime} ]]>
            </if>
            <if test="startTime == null">
              <![CDATA[ and expireTime >= DATE_FORMAT(now(),'%Y-%m-%d 00:00:00') ]]>
            </if>
          </when>
          <otherwise>
            and expireTime is null
          </otherwise>
        </choose>
      </otherwise>
    </choose>

  </sql>

  <select id="getRoleUserAmount" resultType="java.lang.Long">
    SELECT
    count(*)
    FROM
    sys_role sr
    LEFT JOIN sys_user_role sur ON sr.id = sur.role_id
    LEFT JOIN sys_user su ON su.id = sur.user_id
    WHERE
    <include refid="queryRoleUserList"></include>
  </select>
  <select id="getRoleUserList" resultType="com.cdz360.biz.auth.sys.vo.RoleUserVo">
    SELECT
    su.id,
    su.username,
    su.NAME,
    su.commId,
    su.phone,
    tc.comm_name as commName,
    sur.expireTime
    FROM
    sys_role sr
    LEFT JOIN sys_user_role sur ON sr.id = sur.role_id
    LEFT JOIN sys_user su ON su.id = sur.user_id
    LEFT JOIN t_commercial tc on tc.id=su.commId
    WHERE
    <include refid="queryRoleUserList"></include>
    ORDER BY
    id DESC
    <if test="start!=null and size!=null">
      LIMIT #{start},#{size}
    </if>
  </select>
  <select id="getUserByRoleId" resultType="com.cdz360.biz.auth.sys.vo.RoleUserVo">
    SELECT
    su.id,
    su.NAME,
    su.username,
    su.phone,
    su.commId,
    tc.comm_name AS commName
    FROM
    sys_user su
    LEFT JOIN ( SELECT DISTINCT user_id FROM sys_user_role WHERE role_id = #{roleId} AND enable =
    TRUE AND ( expireTime >=DATE_FORMAT(now(),'%Y-%m-%d 00:00:00') OR expireTime IS NULL ) ) AS tmp
    ON su.id = tmp.user_id
    LEFT JOIN t_commercial tc ON tc.id = su.commId
    WHERE
    su.STATUS = 1
    AND su.platform = #{platform}

    AND tmp.user_id IS NULL
    <if test="keyWord !=null">
      AND ( username LIKE concat('%',#{keyWord},'%') OR NAME LIKE concat('%',#{keyWord},'%') OR
      su.phone LIKE concat('%',#{keyWord},'%') )
    </if>
    <if test="size!=null">
      LIMIT #{size}
    </if>
  </select>
  <select id="getUserListById" resultType="com.cdz360.biz.model.cus.vo.SysUserVo">
    SELECT
    su.id,su.username
    FROM
    sys_user su
    LEFT JOIN t_commercial tc ON su.commId = tc.id
    WHERE
    tc.idChain LIKE concat(#{commIdChain},'%')
    AND su.platform = #{platform}
    AND su.STATUS =1
  </select>
  <select id="groupIdByKeyWord" resultType="java.lang.Long">
    select distinct(id)
    from sys_user
    where username LIKE concat('%',#{keyWord},'%') OR `name` LIKE concat('%',#{keyWord},'%')
  </select>
  <select id="uidByTeamCatalog" resultType="java.lang.Long">
    select distinct(id)
    from sys_user
    where ( find_in_set(#{teamCatalog}, teamCatalog)  <!-- 两个方向的数组都要支持. 如果审批人和提交人都有多个团队标签,暂时还不支持 -->
      or find_in_set(teamCatalog, #{teamCatalog}) )
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( uname )">
      and name like CONCAT('%', #{uname}, '%')
    </if>
  </select>

  <select id="uidByTeamCatalogList" resultType="java.lang.Long">
    select distinct(id)
    from sys_user
    where 1=1
    <foreach collection="teamCatalogList" index="index" item="item" open="and ("
      separator="or" close=")">
      find_in_set(#{item}, teamCatalog)
    </foreach>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( uname )">
      and name like CONCAT('%', #{uname}, '%')
    </if>
  </select>

  <select id="uidByUname" resultType="java.lang.Long">
    select distinct(id)
    from sys_user
    where platform = 20 and name like CONCAT('%', #{uname}, '%')
  </select>

  <select id="queryByCheckParam" parameterType="com.cdz360.biz.model.cus.param.SysUserCheckParam"
    resultType="com.cdz360.biz.auth.user.po.SysUserPo">
    select
    *
    from
    sys_user
    <where>
      status = 1
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(list)">
        and
        <foreach collection="list" index="index" item="item" open="("
          separator="or" close=")">
          (name = #{item.name} and username = #{item.username})
        </foreach>
      </if>
    </where>
  </select>
  <select id="sameCorpWxAppNameUids" resultType="java.lang.Long">
    select su.id
    from sys_user su
    where su.corpWxAppId in (
    select
    app.id
    from t_corp_wx_app app
    left join sys_user src
    on app.id = src.corpWxAppId
    where src.id = #{uid}
    )
  </select>
  <select id="getOneByPhoneAndPlatform" resultType="com.cdz360.biz.auth.user.po.SysUserPo">
    select * from sys_user
    where status = 1
    and topCommId = #{topCommId}
    and phone = #{phone}
    and platform = #{platform}
    limit 1
  </select>
  <select id="getUserSimpleList" resultType="com.cdz360.biz.auth.model.vo.SysUser">
    SELECT * FROM sys_user WHERE
    <if test="platform != null">
      platform = #{platform}
    </if>
    ORDER BY
    id DESC
    LIMIT #{start}, #{size}
  </select>

  <select id="selectById" resultType="com.cdz360.biz.auth.model.vo.SysUser">
    select * from sys_user where id= #{id}
  </select>

</mapper>