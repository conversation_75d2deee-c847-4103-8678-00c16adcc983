package com.cdz360.biz.auth.config;

import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.service.LoginService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;
import org.springframework.web.util.WebUtils;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;

@Component
public class UserArgResolver implements HandlerMethodArgumentResolver {
    private static final String TOKEN = "token";

    @Autowired
    LoginService loginService;


    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        boolean b = parameter.hasParameterAnnotation(CurrentUser.class);
        boolean assignableFrom = SysUser.class.isAssignableFrom(parameter.getParameterType());
        return b && assignableFrom;
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {

        String token = webRequest.getHeader(TOKEN);

        //如果header中不存在token，则从参数中获取token
        if (StringUtils.isBlank(token)) {
            token = webRequest.getParameter(TOKEN);
        }

        if (StringUtils.isBlank(token)) {
            HttpServletRequest nativeRequest = webRequest.getNativeRequest(HttpServletRequest.class);
            Cookie cookie = WebUtils.getCookie(nativeRequest, TOKEN);
            if (cookie != null)
                token = cookie.getValue();
        }

        SysUser sysUser = null;
        if (token != null) {
            sysUser = loginService.getUser(token);
        }

        return sysUser;
    }
}
