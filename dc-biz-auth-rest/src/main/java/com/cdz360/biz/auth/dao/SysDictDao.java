package com.cdz360.biz.auth.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdz360.biz.auth.model.vo.SysDict;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SysDictDao extends BaseMapper<SysDict> {

    SysDict findByPcodeAndCode(@Param("pcode") String pcode,
                               @Param("code") String code);

    List<SysDict> findByPcode(String pcode);

    // 获取国充场站的gcType值，使用逗号分割
    String getGcSiteTypes();
}
