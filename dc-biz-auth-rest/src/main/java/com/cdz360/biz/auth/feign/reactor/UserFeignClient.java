package com.cdz360.biz.auth.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.biz.model.cus.corp.param.ChangeByGidsParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_USER,
    fallbackFactory = UserFeignHystrix.class)
public interface UserFeignClient {

    // 根据企业gids刷新鉴权介质的可用场站
    @PostMapping("/api/siteAuth/changeByGids")
    Mono<BaseResponse> changeByGids(@RequestBody ChangeByGidsParam param);

}