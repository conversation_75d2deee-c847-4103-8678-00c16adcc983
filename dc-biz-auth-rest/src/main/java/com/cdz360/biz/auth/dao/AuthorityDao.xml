<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.dao.AuthorityDao">
  <select id="getAllAuthority" resultType="com.cdz360.biz.auth.sys.vo.Authority">
    select au.*
    from t_authority au
    <if test="null != sysId">
      where au.menuId in (select id from sys_menu sm where sm.subsys_id = #{sysId})
    </if>
  </select>
  <select id="getAuthorityListByUid" resultType="com.cdz360.biz.auth.sys.vo.Authority">
    SELECT
    c.authorityId as id,c.authorityCode as `code` FROM sys_user a
    LEFT JOIN t_user_group_ref b ON a.id = b.userId
    LEFT JOIN t_auth_group_ref c on b.authGroupId=c.groupId
    where c.id is not null and a.id =#{id} group by c.authorityId,c.authorityCode
  </select>
  <select id="getAuthorityListByGroupIdList" resultType="com.cdz360.biz.auth.sys.vo.Authority">
    select id, code from t_authority where id in (
    select distinct(authorityId) from t_auth_group_ref
    <where>
      groupId in
      <foreach item="item" index="index" collection="idList"
        open="(" separator="," close=")">
        #{item}
      </foreach>
    </where>
    )
  </select>
  <select id="getAuthorityByModule" resultType="com.cdz360.biz.auth.sys.vo.Authority">
    select * from t_authority where
    module in
    <foreach item="item" index="index" collection="moduleList"
      open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>
</mapper>