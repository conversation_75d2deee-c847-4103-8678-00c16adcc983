package com.cdz360.biz.auth.config;
//
//import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
//import com.cdz360.biz.auth.model.vo.SysUser;
//import org.apache.ibatis.reflection.MetaObject;
//import org.apache.shiro.SecurityUtils;
//import org.apache.shiro.subject.PrincipalCollection;
//import org.springframework.stereotype.Component;
//
//import java.util.Date;
//
//@Component
//public class MetaObjectHandlerConfig implements MetaObjectHandler {
//
//    public static final String CREATE_TIME = "createTime";
//    public static final String UPDATE_TIME = "updateTime";
//
//    public static final String CREATE_BY = "createBy";
//    public static final String UPDATE_BY = "updateBy";
//
//    @Override
//    public void insertFill(MetaObject metaObject) {
//        Object createTime = getFieldValByName(CREATE_TIME, metaObject);
//        if (createTime == null) {
//            setFieldValByName(CREATE_TIME, new Date(), metaObject);
//        }
//
//        Object createBy = getFieldValByName(CREATE_BY, metaObject);
//        if (createBy == null) {
//            PrincipalCollection pc = SecurityUtils.getSubject().getPrincipals();
//            if (pc != null) {
//                SysUser principal = (SysUser) pc.getPrimaryPrincipal();
//                if (principal != null)
//                    setFieldValByName(CREATE_BY, principal.getId(), metaObject);
//            }
//
//        }
//
//    }
//
//    @Override
//    public void updateFill(MetaObject metaObject) {
//        Object updateTime = getFieldValByName(UPDATE_TIME, metaObject);
//        if (updateTime == null) {
//            setFieldValByName(UPDATE_TIME, new Date(), metaObject);
//        }
//
//        Object updateBy = getFieldValByName(UPDATE_BY, metaObject);
//        if (updateBy == null) {
//            PrincipalCollection pc = SecurityUtils.getSubject().getPrincipals();
//            if (pc != null) {
//                SysUser principal = (SysUser) pc.getPrimaryPrincipal();
//                setFieldValByName(UPDATE_BY, principal.getId(), metaObject);
//            }
//        }
//    }
//}
