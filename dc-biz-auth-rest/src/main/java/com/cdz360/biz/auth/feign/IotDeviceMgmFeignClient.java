package com.cdz360.biz.auth.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.biz.model.bi.dashboard.SiteAndPlugBiVo;
import com.cdz360.biz.model.bi.dashboard.SiteAndPlugBiVoEx;
import com.cdz360.biz.model.common.request.SiteCtrlRequest;
import com.cdz360.biz.model.iot.dto.EvseDto;
import com.cdz360.biz.model.iot.dto.PlugInfoDto;
import com.cdz360.biz.model.iot.dto.SiteCtrlDto;
import com.cdz360.biz.model.iot.param.*;
import com.cdz360.biz.model.iot.vo.*;
import com.cdz360.biz.model.trading.iot.dto.EvseModuleDto;
import com.cdz360.biz.model.trading.iot.dto.SiteDeviceBiDto;
import com.cdz360.biz.model.trading.iot.po.EvseModelPo;
import com.cdz360.biz.model.trading.iot.vo.EvseInfoVo;
import com.cdz360.biz.model.trading.iot.vo.EvseStatusPowerBiVo;
import com.cdz360.biz.model.trading.iot.vo.PlugStatusBiVo;
import com.cdz360.biz.model.trading.iot.vo.PlugSupplyBiVo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.yw.param.ReplaceDeviceParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;
import java.util.List;

@FeignClient(name = DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, fallbackFactory = IotDeviceMgmFeignClientHystrix.class)
public interface IotDeviceMgmFeignClient {


    /**
     * 获取桩信息
     *
     * @param evseNo
     * @return
     */
    @GetMapping(value = "/device/mgm/evse/getEvseInfo")
    ObjectResponse<EvseInfoVo> getEvseInfo(@RequestParam(value = "evseNo") String evseNo);

    /**
     * 获取桩列表
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/device/mgm/evse/getEvseInfoList")
    ListResponse<EvseInfoVo> getEvseInfoList(@RequestBody ListEvseParam param);

    /**
     * 获取桩列表信息
     *
     * @param param 查询参数
     * @return 桩列表
     */
    @PostMapping(value = "/device/mgm/evse/getEvseList")
    ListResponse<EvseDto> getEvseList(@RequestBody ListEvseParam param);

    /**
     * 获取桩列表(包含品牌型号模块等信息)
     *
     * @param param 查询参数
     * @return 桩列表
     */
    @PostMapping(value = "/device/mgm/evse/getEvseModelVoList")
    ListResponse<EvseModelVo> getEvseModelVoList(@RequestBody ListEvseParam param);

    /**
     * 获取设备型号
     * @param start
     * @param size
     * @return
     */
    @GetMapping(value = "/device/mgm/evse/getModelList")
    ListResponse<EvseModelPo> getModelList(@RequestParam(value = "start") Long start,
                                           @RequestParam(value = "size") Integer size);

    @PostMapping(value = "/device/mgm/evse/getEvseListForTopology")
    ListResponse<EvseDto> getEvseListForTopology(@RequestBody ListEvseParam param);

    /**
     * 检查脱机桩是否已存在库中
     * @param params
     * @return
     */
    @PostMapping(value = "/device/mgm/evse/checkOfflineEvseInDB")
    ListResponse<OfflineEvseParam> checkOfflineEvseInDB(@RequestBody List<OfflineEvseParam> params);

    /**
     * 检查桩是否已存在库中
     * @param params
     * @return
     */
    @PostMapping(value = "/device/mgm/evse/checkEvseInDB")
    ListResponse<OfflineEvseParam> checkEvseInDB(@RequestBody List<OfflineEvseParam> params);

    /**
     * 获取枪头信息
     *
     * @param plugNo
     * @return
     */
    @PostMapping(value = "/device/mgm/plug/getPlugInfo")
    ObjectResponse<PlugVo> getPlugInfo(@RequestParam(value = "plugNo") String plugNo);

    /**
     * 获取枪头列表
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/device/mgm/plug/getPlugList")
    ListResponse<PlugVo> getPlugList(@RequestBody ListPlugParam param);

    @GetMapping(value = "/iot/bi/device")
    ObjectResponse<DeviceBi> getDeviceBi(@RequestParam(value = "siteId", required = false) String siteId);

    @PostMapping(value = "/iot/bi/getSitesDevice")
    ListResponse<DeviceBi> getSitesDevice(@RequestBody ListDeviceParam param);


    /**
     * 根据电流类型统计桩/枪数量
     *
     * @return
     */
    @PostMapping(value = "/iot/bi/getPlugSupplyBi")
    ListResponse<PlugSupplyBiVo> getPlugSupplyBi(@RequestParam(value = "commIdChain", required = false) String commIdChain);

    /**
     * 根据状态统计桩/枪数量
     *
     * @param commIdChain
     * @return
     */
    @PostMapping(value = "/iot/bi/getPlugStatusBi")
    ListResponse<PlugStatusBiVo> getPlugStatusBi(
            @RequestParam(value = "provinceCode", required = false) String provinceCode,
            @RequestParam(value = "cityCode", required = false) String cityCode,
            @RequestParam(value = "siteId", required = false) String siteId,
            @RequestParam(value = "commIdChain", required = false) String commIdChain);

    /**
     * 统计场站桩/枪数量
     *
     * @param siteIdList
     * @return
     */
    @PostMapping(value = "/iot/bi/getSiteDeviceBiList")
    ListResponse<SiteDeviceBiDto> getSiteDeviceBiList(@RequestParam(value = "siteIdList", required = false) List<String> siteIdList);


    /**
     * 根据状态统计桩功率
     *
     * @param commIdChain
     * @return
     */
    @PostMapping(value = "/iot/bi/getEvseStatusPowerBi")
    ListResponse<EvseStatusPowerBiVo> getEvseStatusPowerBi(
            @RequestParam(value = "provinceCode", required = false) String provinceCode,
            @RequestParam(value = "cityCode", required = false) String cityCode,
            @RequestParam(value = "siteId", required = false) String siteId,
            @RequestParam(value = "commIdChain", required = false) String commIdChain);


    @GetMapping(value = "/device/mgm/site/plugs")
    ListResponse<PlugInfoDto> getPlugsOfSite(
            @RequestParam(value = "index") Integer index,
            @RequestParam(value = "size") Integer size,
            @RequestParam(value = "siteId") String siteId);


    /**
     * 获取桩最新下发时间
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/device/mgm/evse/getEvseCfgResultList")
    ListResponse<EvseCfgResultVo> getEvseCfgResultList(@RequestBody ListEvseCfgResultParam param);

    /**
     * 获取桩时段内在平台的时间（秒）
     *
     * @param startTime
     * @param endTime
     * @param evseNo
     * @param siteId
     * @return
     */
    @Deprecated
    @GetMapping(value = "/device/mgm/evse/getEvseListActiveTime")
    ObjectResponse<Long> getEvseListActiveTime(@RequestParam("startTime")
                                               @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                               @RequestParam("endTime")
                                               @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                               @RequestParam("evseNo") String evseNo,
                                               @RequestParam("siteId") String siteId);

    /**
     * 控制器新增
     *
     * @param req
     * @return
     */
    @PostMapping("/device/mgm/siteCtrl/add")
    BaseResponse add(@RequestBody SiteCtrlRequest req);

    /**
     * 控制器修改
     *
     * @param req
     * @return
     */
    @PostMapping("/device/mgm/siteCtrl/edit")
    BaseResponse edit(@RequestBody SiteCtrlRequest req);

    /**
     * 查询控制器列表
     *
     * @param keyword
     * @param start
     * @param size
     * @return
     */
    @GetMapping("/device/mgm/siteCtrl/list")
    ListResponse<SiteCtrlDto> list(@RequestParam(value = "keyword", required = false) String keyword,
                                   @RequestParam(value = "siteId") String siteId,
                                   @RequestParam(value = "start") long start,
                                   @RequestParam(value = "size") long size);

    /**
     * 控制器解绑
     *
     * @param num
     * @return
     */
    @GetMapping("/device/mgm/siteCtrl/disable")
    BaseResponse disable(@RequestParam(value = "num") String num);


    /**
     * 新增场站配置模板或更新
     *
     * @param po
     * @return
     */
    @PostMapping(value = "/device/mgm/siteCtrlCfg/addOrUpdate")
    BaseResponse addOrUpdate(@RequestBody SiteCtrlCfgPo po);

    /**
     * 获取场站最新的配置模板信息
     *
     * @param ctrlNum
     * @return
     */
    @GetMapping(value = "/device/mgm/siteCtrlCfg/findByCtrlNo")
    ObjectResponse<SiteCtrlCfgVo> findByCtrlNo(
            @Parameter(name = "场站控制器编号", required = true) @RequestParam(value = "ctrlNum") String ctrlNum);

    /**
     * 查看场站控制器配置
     *
     * @param ctrlNum
     * @return
     */
    @GetMapping(value = "/device/mgm/siteCtrlCfg/getBySiteCtrl")
    ObjectResponse<SiteCtrlCfgVo> getBySiteCtrl(@RequestParam(value = "ctrlNum") String ctrlNum);

    /**
     * 下发获取控制器配置指令
     *
     * @param ctrlNum
     * @return
     */
    @GetMapping(value = "/device/mgm/siteCtrlCfg/send2GetCfg")
    BaseResponse send2GetCfg(@RequestParam(value = "ctrlNum") String ctrlNum);


    /**
     * 获取有空闲枪头的场站ID列表
     *
     * @param topCommId
     * @return
     */
    @GetMapping(value = "/device/mgm/site/getIdleSiteIdList")
    ListResponse<String> getIdleSiteIdList(@RequestParam("topCommId") Long topCommId);

    @GetMapping(value = "/device/mgm/site/getRecordEvsePlugInfo")
    ObjectResponse<SitePo> getRecordEvsePlugInfo(@RequestParam(value = "siteId") String siteId,
                                                 @RequestParam(value = "isOfflineSite") Boolean isOfflineSite);

    @GetMapping(value = "/device/mgm/site/getUpgradeCleaningEvsePlugInfo")
    ListResponse<SitePo> getUpgradeCleaningEvsePlugInfo();

    @Operation(summary = "获取场站质保到期日")
    @GetMapping(value = "/device/mgm/site/getExpireDate")
    ObjectResponse<Date> getExpireDate(@RequestParam(value = "siteId") String siteId);

    // 获取商户下桩/枪状态统计数据
    @PostMapping(value = "/device/mgm/plug/getSiteAndPlugStatus")
    ObjectResponse<SiteAndPlugBiVo> getSiteAndPlugStatus(@RequestParam(value = "commId", required = false) Long commId,
                                                         @RequestParam(value = "commIdChain", required = false) String commIdChain);

    @PostMapping(value = "/device/mgm/plug/getSiteAndPlugStatusSubComm")
    ListResponse<SiteAndPlugBiVoEx> getSiteAndPlugStatusSubComm(@RequestParam("commId") Long commId);

    @Operation(summary = "获取桩升级记录")
    @PostMapping("/device/upgrade/getUpgradeRecordVo")
    ListResponse<UpgradeRecordVo> getUpgradeRecordVo(@RequestBody UpgradeTaskListRequest request);

    @GetMapping("/device/mgm/evseModule/list")
    ObjectResponse<EvseModuleDto> getEvseModuleList(@RequestParam("evseNo") String evseNo);

    @GetMapping("/iot/evseDevice/getByEvseNo")
    ListResponse<DeviceVo> getByEvseNo(@RequestParam(value = "evseNo") String evseNo,
                                       @RequestParam(value = "deviceName", required = false) String deviceName,
                                       @RequestParam(value = "start") Long start,
                                       @RequestParam(value = "size") Long size);

    @PostMapping("/iot/evseDevice/getByDeviceNo")
    ObjectResponse<EvseDeviceVo> getByDeviceNo(@RequestBody FindDeviceParam param);

    @PostMapping("/iot/evseDevice/addDevice")
    BaseResponse addDevice(@RequestBody DeviceParam param);

    @PostMapping("/iot/evseDevice/editDevice")
    BaseResponse editDevice(@RequestBody DeviceParam param);

    @PostMapping("/iot/evseDevice/replaceDevice")
    BaseResponse replaceDevice(@RequestBody ReplaceDeviceParam param);

}
