package com.cdz360.biz.auth.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.auth.dao.AuthGroupDao;
import com.cdz360.biz.auth.dao.AuthGroupRefDao;
import com.cdz360.biz.auth.dao.AuthorityDao;
import com.cdz360.biz.auth.dao.SysMenuDao;
import com.cdz360.biz.auth.dao.SysRoleDao;
import com.cdz360.biz.auth.dao.SysRoleMenuDao;
import com.cdz360.biz.auth.dao.SysSystemDao;
import com.cdz360.biz.auth.dao.SysUserRoleDao;
import com.cdz360.biz.auth.ds.ro.sys.SysMenuRoDs;
import com.cdz360.biz.auth.ds.ro.sys.SysRoleRoDs;
import com.cdz360.biz.auth.ds.ro.user.SysUserRoDs;
import com.cdz360.biz.auth.ds.rw.sys.SysRoleRwDs;
import com.cdz360.biz.auth.model.constant.GlobalConst;
import com.cdz360.biz.auth.model.dto.RoleCategoryVo;
import com.cdz360.biz.auth.model.param.BatchUpdateRoleRequest;
import com.cdz360.biz.auth.model.vo.AuthGroupRef;
import com.cdz360.biz.auth.sys.vo.Authority;
import com.cdz360.biz.auth.model.vo.AuthorityGroup;
import com.cdz360.biz.auth.model.vo.SysDict;
import com.cdz360.biz.auth.model.vo.SysMenu;
import com.cdz360.biz.auth.model.vo.SysRole;
import com.cdz360.biz.auth.model.vo.SysRoleMenu;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.sys.param.BatchAddRoleUserParam;
import com.cdz360.biz.auth.sys.param.RoleUserListParam;
import com.cdz360.biz.auth.sys.param.RoleUserUpdateParam;
import com.cdz360.biz.auth.user.po.SysUserPo;
import com.cdz360.biz.auth.utils.IotAssert;
import com.cdz360.biz.model.cus.message.type.PlatformType;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.jooq.lambda.Seq;
import org.slf4j.event.Level;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@Transactional
public class SysRoleService extends ServiceImpl<SysRoleDao, SysRole> {

    @Autowired
    private SysRoleMenuDao roleMenuDao;


    @Autowired
    private SysMenuDao menuDao;
    @Autowired
    private SysSystemDao systemDao;
    @Autowired
    private SysDictService dictService;
    @Autowired
    private SysRoleRoDs sysRoleRoDs;

    @Autowired
    private AuthGroupDao authGroupDao;

    @Autowired
    private AuthGroupRefDao authGroupRefDao;
    @Autowired
    private SysRoleRwDs sysRoleRwDs;

    @Autowired
    private SysRoleMenuDao sysRoleMenuDao;

    @Autowired
    private SysMenuRoDs sysMenuRoDs;

    @Autowired
    private AuthorityDao authorityDao;

    @Autowired
    private SysUserRoleDao sysUserRoleDao;

    @Autowired
    private SysUserRoDs sysUserRoDs;

    @Transactional
    public List<SysRole> findByUserId(Long userId) {
        List<SysRole> list = baseMapper.findByUserId(userId);
        return list;
    }

    public SysRole findByRoleName(String roleName) {
        SysRole sysRole = new SysRole();
        sysRole.setName(roleName);
        return baseMapper.selectOne(Wrappers.query(sysRole));
    }

    public List<SysRole> findRoleHasSiteDetailAuth() {
        List<SysRole> list = baseMapper.findRoleHasSiteDetailAuth();
        return list;
    }


    /**
     * 获取当前登录用户创建的角色
     *
     * @param userId
     * @return
     */
    public List<SysRole> getRoleByUserId(Long userId) {
        List<SysRole> list = baseMapper.getRoleByUserId(userId);
        return list;
    }

    /**
     * 获取当前用户所属企业
     *
     * @param userId
     * @return
     */
    public CorpPo getCorpByUserId(Long userId) {
        CorpPo list = baseMapper.getCorpByUserId(userId);
        return list;
    }

    /**
     * 获取角色 及 菜单列表
     *
     * @param roleId
     * @return
     */
    @Transactional
    public SysRole findRoleByIdWithMenus(Long roleId) {
        SysRole sysRole = baseMapper.selectById(roleId);
        if (sysRole != null) {
            List<SysRoleMenu> roleMenuList = roleMenuDao.selectList(
                Wrappers.query(SysRoleMenu.class).eq("role_id", roleId));

            Set<Long> idList = Seq.seq(roleMenuList).map(SysRoleMenu::getMenuId).toSet();
            sysRole.setMenuIds(idList);

            AuthorityGroup groupByRoleId = authGroupDao.getGroupByRoleId(roleId);
            if (groupByRoleId == null) {
                sysRole.setAuthorityIdList(List.of());
            } else {
                List<AuthGroupRef> authGroupRefs = authGroupRefDao.selectByGroupId(
                    groupByRoleId.getId());
                sysRole.setAuthorityIdList(authGroupRefs.stream()
                    .map(AuthGroupRef::getAuthorityId)
                    .collect(Collectors.toList()));
            }

        }
        return sysRole;
    }

    /**
     * 删除 角色 及 角色-菜单 关系
     *
     * @param roleId
     */
    @Transactional
    @Deprecated
    public boolean deleteByIdWithMenus(Long roleId) {
        Integer menuDel = roleMenuDao.deleteByRoleId(roleId);
        Integer roleDel = baseMapper.deleteById(roleId);
        return menuDel > 0 && roleDel > 0;
//        return SqlHelper.delBool(menuDel) && SqlHelper.delBool(roleDel);
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean updateWithMenu(SysRole sysRole) {
//        Condition condition = Condition.create();
        QueryWrapper<SysRole> wrapper =  Wrappers.query(SysRole.class);
        if (sysRole.getPlatform() != null
            && sysRole.getPlatform() == AppClientType.CORP_WEB.getCode()) {
            wrapper.eq("platform", AppClientType.CORP_WEB.getCode());
            wrapper.eq("corpId", sysRole.getCorpId());
        }
        wrapper.eq("name", sysRole.getName());
        SysRole old = this.getBaseMapper().selectOne(wrapper);
        if (old != null && old.getId().longValue() != sysRole.getId()) {
            log.info("操作失败,角色名称已存在. sysRole = {}", sysRole);
            throw new DcServiceException("操作失败,角色名称已存在", Level.INFO);
        }

        baseMapper.updateById(sysRole);
        Boolean svaha = sysRole.getSvaha();

        if ((svaha == null || !svaha) && sysRole.getMenuIds() != null) {
            // 删除角色对应菜单 & 重建对应菜单
//            roleMenuDao.findMenuIdsByRoleId(sysRole.getId());
            roleMenuDao.deleteByRoleId(sysRole.getId());
            if (CollectionUtils.isNotEmpty(sysRole.getMenuIds())) {
                roleMenuDao.saveBatch(sysRole.getId(), sysRole.getMenuIds());
            }
        }

        if (sysRole.getPlatform() != null
            && List.of(AppClientType.MGM_WEB.getCode(), AppClientType.COMM_ESS_MGM.getCode())
            .contains(sysRole.getPlatform())) {

            if (sysRole.getAuthorityIdList() == null) {
                sysRole.setAuthorityIdList(List.of());
            }

            AuthorityGroup groupByRoleId = authGroupDao.getGroupByRoleId(sysRole.getId());
            if (groupByRoleId == null) {
                log.info("角色无权限组，新建权限组");
                AuthorityGroup authorityGroup = new AuthorityGroup();
                authorityGroup.setRoleId(sysRole.getId())
                    .setGroupName(sysRole.getName() + "-权限组")
                    .setCreateTime(new Date());
                log.info("新增权限组： {}, id: {}", authGroupDao.addGroup(authorityGroup),
                    authorityGroup.getId());
                groupByRoleId = authorityGroup;
            } else {
                log.info("角色有权限组,删除权限组对应关系： {}",
                    authGroupRefDao.deleteGroupRefByGroupId(groupByRoleId.getId()));
            }

            List<Authority> authorityList =
                sysRole.getAuthorityIdList().stream().map(e -> {
                    Authority authority = new Authority();
                    authority.setId(e);
                    return authority;
                }).collect(Collectors.toList());

            final Long groupId = groupByRoleId.getId();
            List<AuthGroupRef> authGroupRefList = new ArrayList<>();
            authorityList.stream().forEach(e -> {
                AuthGroupRef authGroupRef = new AuthGroupRef();
                authGroupRef.setGroupId(groupId)
//                            .setAuthorityCode(e.getCode())
                    .setAuthorityId(e.getId())
                    .setCreateTime(new Date())
//                            .setOpId(authorityGroup.getOpId())
                //.setOpName(sysRole.getName() + "-权限组")
                ;
                authGroupRefList.add(authGroupRef);
            });
            if (CollectionUtils.isNotEmpty(authGroupRefList)) {
                log.info("新增权限组对应关系： {}",
                    authGroupRefDao.batchAddGroupRef(authGroupRefList));
            }
        }

        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean insertWithMenus(SysRole sysRole) {
        if (sysRole.getPlatform() == null) {
            log.warn("参数错误. platform 不能为空. sysRole = {}", sysRole);
            throw new DcArgumentException("参数错误. platform 不能为空");
        }

        QueryWrapper<SysRole> wrapper =  Wrappers.query(SysRole.class);
//        Condition condition = Condition.create();
        wrapper.eq("name", sysRole.getName());

        //企业管理平台，角色名称在本企业不能重复
        if (sysRole.getPlatform().equals(AppClientType.CORP_WEB.getCode())) {
            wrapper.eq("corpId", sysRole.getCorpId());
        }
        SysRole old = this.getBaseMapper().selectOne(wrapper);
        if (old != null) {
            log.info("操作失败,角色名称已存在. sysRole = {}", sysRole);
            throw new DcServiceException("操作失败,角色名称已存在", Level.INFO);
        }
        final Integer id = baseMapper.insert(sysRole);
        boolean menuSave = true;
        //是否全选
        Boolean svaha = sysRole.getSvaha();
        if ((svaha == null || !svaha) && CollectionUtils.isNotEmpty(sysRole.getMenuIds())) {
            Integer integer = roleMenuDao.saveBatch(sysRole.getId(), sysRole.getMenuIds());
            menuSave = integer > 0; // SqlHelper.retBool(integer);
        }

        if (List.of(AppClientType.MGM_WEB.getCode(), AppClientType.COMM_ESS_MGM.getCode())
            .contains(sysRole.getPlatform())) {

            if (sysRole.getAuthorityIdList() == null) {
                sysRole.setAuthorityIdList(List.of());
            }

            AuthorityGroup authorityGroup = new AuthorityGroup();
            authorityGroup.setRoleId(sysRole.getId())
                .setGroupName(sysRole.getName() + "-权限组")
                .setCreateTime(new Date());
            log.info("新增权限组： {}, id: {}", authGroupDao.addGroup(authorityGroup),
                authorityGroup.getId());

            List<Authority> authorityList =
                sysRole.getAuthorityIdList().stream().map(e -> {
                    Authority authority = new Authority();
                    authority.setId(e);
                    return authority;
                }).collect(Collectors.toList());

            List<AuthGroupRef> authGroupRefList = new ArrayList<>();
            authorityList.forEach(e -> {
                AuthGroupRef authGroupRef = new AuthGroupRef();
                authGroupRef.setGroupId(authorityGroup.getId())
//                            .setAuthorityCode(e.getCode())
                    .setAuthorityId(e.getId())
                    .setCreateTime(new Date())
//                            .setOpId(authorityGroup.getOpId())
//                        .setOpName(sysRole.getName() + "-权限组")
                ;
                authGroupRefList.add(authGroupRef);
            });
            if (CollectionUtils.isNotEmpty(authGroupRefList)) {
                log.info("新增权限组对应关系： {}",
                    authGroupRefDao.batchAddGroupRef(authGroupRefList));
            }
        }
        return id > 0 && menuSave;
//        return SqlHelper.retBool(id) && menuSave;
    }

    public List<RoleCategoryVo> groupByKind(SysUser sysUser) {
        QueryWrapper<SysRole> wrapper =  Wrappers.query(SysRole.class);
//        Condition condition = Condition.create();
        wrapper.eq("status", 1);

        //企业管理平台
        if (sysUser.getPlatform() == AppClientType.CORP_WEB.getCode()) {
            wrapper.eq("platform", sysUser.getPlatform());

            SysRole userRoleInfo = sysRoleRoDs.getUserRoleById(sysUser.getId());
            wrapper.eq("create_by", sysUser.getId());
//            List createBy =  new ArrayList<>();
//            createBy.add(sysUser.getId());
//
//            if (userRoleInfo != null) {
//                createBy.add(userRoleInfo.getCreateBy());
//            }
//            condition.in("create_by",createBy);
        }
        List<SysRole> all = this.getBaseMapper().selectList(wrapper);

        Map<Integer, List<SysRole>> integerListMap = Seq.seq(all)
            .groupBy(sysRole -> sysRole.getCategory() == null ? -1 : sysRole.getCategory());
        List<RoleCategoryVo> voList = new ArrayList<>(integerListMap.keySet().size());
        for (Map.Entry<Integer, List<SysRole>> entry : integerListMap.entrySet()) {
            Integer key = entry.getKey();
            List<SysRole> value = entry.getValue();
            SysDict dict = dictService.queryByPcodeAndCode(GlobalConst.ROLE_CATEGORY,
                key.toString());
            RoleCategoryVo vo = new RoleCategoryVo();
            if (dict == null) {
                vo.setName("未分类");
            } else {
                BeanUtils.copyProperties(dict, vo);
            }
            java.util.Optional<SysRole> tmp = value.stream()
                .filter(e -> e.getPlatform() != null && e.getPlatform() > 0).findFirst();
            tmp.ifPresent(sysRole -> vo.setPlatform(sysRole.getPlatform()));

            vo.setRoleList(value);
            voList.add(vo);
        }
        return voList;
    }

    @Transactional
    public BaseResponse batchUpdate(BatchUpdateRoleRequest params) {
        IotAssert.isNotNull(params.getEditType(), "修改项不能为空");

        if (CollectionUtils.isEmpty(params.getRoleIdList())) {
            throw new DcArgumentException("角色信息不能为空");
        }
        List<SysRole> roleList = sysRoleRoDs.getRoleByIdList(params.getRoleIdList(),
            params.getPlatform());
        if (CollectionUtils.isEmpty(roleList)) {
            throw new DcArgumentException("角色信息不能为空");
        }
        switch (params.getEditType()) {
            case CHARGE_AND_INVOICE:
                //修改sys_role表
                if (StringUtils.isBlank(params.getCommIds())) {
                    throw new DcArgumentException("商户信息不能为空");
                }
                List<Long> roleIdList = roleList.stream().map(SysRole::getId)
                    .collect(Collectors.toList());
                sysRoleRwDs.batchUpdate(params.getCommIds(), params.getUpdateBy(), roleIdList);

                //修改sys_role_menu表
                //获取menuId
                List<String> urlList = List.of("finance-center", "invoice-center", "corpInvoice",
                    "corpInvoiceList", "deposit-apply", "balanceApplicationList");
                List<SysMenu> menuList = sysMenuRoDs.getMenuListByUrl(urlList);
                roleList.stream().forEach(e -> sysRoleMenuDao.saveBatch(e.getId(),
                    menuList.stream().map(SysMenu::getId).collect(Collectors.toSet())));

                //获取t_authority_group信息
                List<Long> groupIdList = authGroupDao.getGroupListByRoleIdList(roleIdList)
                    .stream().map(AuthorityGroup::getId).collect(Collectors.toList());
                //获取t_authority信息
                List<String> moduleList = List.of("CORP_INVOICE_MODULE", "BALANCE_APPLICATION");
                List<Long> authorityIdList = authorityDao.getAuthorityByModule(moduleList).stream()
                    .map(Authority::getId).collect(Collectors.toList());
                List<AuthGroupRef> refList = new ArrayList<>();
                groupIdList.stream().forEach(e -> authorityIdList.stream().forEach(v -> {
                    AuthGroupRef authGroupRef = new AuthGroupRef()
                        .setGroupId(e)
                        .setAuthorityId(v)
                        .setOpId(params.getUpdateBy());
                    refList.add(authGroupRef);
                }));
                //写入 t_authority_group_ref表
                authGroupRefDao.batchAddGroupRef(refList);
                break;
            default:
                throw new DcArgumentException("修改项不存在");
        }
        return RestUtils.success();
    }

    public List<Long> getMenuList(Long roleId) {
        return sysRoleMenuDao.getMenuList(roleId);
    }

    public List<SysRole> getRoleList(Long platform) {
        return sysRoleMenuDao.getRoleList(platform);
    }

    public ListResponse<SysRole> getRoleListByUserId(RoleUserListParam params) {
        IotAssert.isNotNull(params.getUserId(), "用户ID不能为空");
        if (params.getStart() == null) {
            params.setStart(0L);
        }
        if (params.getSize() == null) {
            params.setSize(10);
        }
        Long total = sysRoleRoDs.getRoleAmountByUserId(params);
        if (total == null || total.equals(0L)) {
            return new ListResponse<>(null, 0L);
        }
        return new ListResponse<>(sysRoleRoDs.getRoleListByUserId(params), total);
    }

    public ListResponse<SysRole> getRoleByUserId(String keyWord, Long platform, Long userId,
        Long size) {
        IotAssert.isNotNull(platform, "请选择所属平台");
        IotAssert.isNotNull(userId, "账号ID不能为空");

        // 管理平台包含 充电平台 + 海外平台
        List<Long> platformList = new ArrayList<>();
        if (Long.valueOf(PlatformType.MANAGE.getCode()).equals(platform)) {
            platformList = List.of(platform, Long.valueOf(PlatformType.HW.getCode()));
        } else {
             platformList = List.of(platform);
        }
//        if (List.of(31L, 32L, 33L).contains(platform)) {
//            platform = (long) PlatformType.MANAGE.getCode();
//        }
        return new ListResponse<>(sysRoleRoDs.getRoleByUserId(keyWord, platformList, userId, size));
    }

    public BaseResponse batchUpdateRoleUserByUserId(RoleUserUpdateParam params) {
        IotAssert.isNotNull(params.getUserId(), "账号ID不能为空");
        IotAssert.isNotNull(CollectionUtils.isNotEmpty(params.getRoleIdList()), "角色信息不能为空");
        sysUserRoleDao.batchUpdateRoleUserByUserId(params);
        return BaseResponse.success();
    }

    public BaseResponse batchAddRoleUserByUserId(BatchAddRoleUserParam params) {
        IotAssert.isNotNull(params.getUserId(), "账号信息不能为空");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(params.getRoleIdList()), "角色ID不能为空");
        IotAssert.isNotNull(params.getPlatform(), "所属平台不能为空");
        SysUserPo userInfo = sysUserRoDs.getByUseId(params.getUserId());
        IotAssert.isTrue(userInfo != null && userInfo.getPlatform().equals(params.getPlatform()),
            "账号信息不存在");

        sysUserRoleDao.batchAddRoleUserByUserId(params);
        return BaseResponse.success();
    }

}
