package com.cdz360.biz.auth.ds.rw.user;

import com.cdz360.biz.auth.ds.ro.user.mapper.SiteGroupRoMapper;
import com.cdz360.biz.auth.ds.rw.user.mapper.SiteGroupRwMapper;
import com.cdz360.biz.auth.utils.IotAssert;
import com.cdz360.biz.model.sys.dto.SiteGroupDto;
import com.cdz360.biz.model.sys.param.ListSiteGroupParam;
import com.cdz360.biz.model.sys.po.SiteGroupPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class SiteGroupRwDs {

    @Autowired
    private SiteGroupRoMapper siteGroupRoMapper;

    @Autowired
    private SiteGroupRwMapper siteGroupRwMapper;

    public Mono<SiteGroupPo> insertSiteGroup(SiteGroupDto dto) {
        Long count = siteGroupRoMapper.count(new ListSiteGroupParam());
        String gid = String.format("G%04d", count);
        if (null != siteGroupRoMapper.getByGid(gid)) {
            gid = String.format("GX%03d", count);
            if (null != siteGroupRoMapper.getByGid(gid)) {
                gid = String.format("GL%03d", count);
            }
        }
        dto.setGid(gid);
        SiteGroupPo newPo = new SiteGroupPo();
        BeanUtils.copyProperties(dto, newPo);
        int i = siteGroupRwMapper.insertSiteGroup(newPo);
        IotAssert.isTrue(i > 0, "新增场站组失败");
        return Mono.just(newPo);
    }

    public Mono<SiteGroupPo> updateSiteGroup(SiteGroupPo siteGroupPo) {
        int i = siteGroupRwMapper.updateSiteGroup(siteGroupPo);
        IotAssert.isTrue(i > 0, "更新场站组失败");
        return Mono.just(siteGroupPo);
    }

    public boolean disableByGid(String gid) {
        return siteGroupRwMapper.disableByGid(gid) > 0;
    }

    public boolean deleteByGid(String gid) {
        return siteGroupRwMapper.deleteByGid(gid) > 0;
    }
}
