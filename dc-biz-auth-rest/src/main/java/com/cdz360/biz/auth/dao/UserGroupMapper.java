package com.cdz360.biz.auth.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * UserGroupMapper
 *
 * @since 2/24/2020 4:48 PM
 * <AUTHOR>
 */

@Mapper
public interface UserGroupMapper {
    int batchInsertUserGroupRef(@Param("userId") Long userId,
                                @Param("authGroupIds") List<Long> authGroupIds,
                                @Param("opName") String opName,
                                @Param("opId") Long opId);

    int deleteUserGroupRefByUserId(@Param("userId") Long userId);

    List<Long> getAuthGroupIdListByUid(@Param("userId") Long userId);


    List<String> getAccountListByGroupId(@Param("groupId") Long groupId,
                                        @Param("start") Long start,
                                        @Param("size") Integer size);
}