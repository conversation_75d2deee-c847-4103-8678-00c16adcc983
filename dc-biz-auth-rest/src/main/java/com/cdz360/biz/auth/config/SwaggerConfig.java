package com.cdz360.biz.auth.config;

import org.springframework.context.annotation.Configuration;

@Configuration
public class SwaggerConfig {
//
//    final String desc = "xxxxxxxxxxxxxxxxxxx";
//    final ApiInfo apiInfo = new ApiInfo("dc-biz-auth", desc,
//            "0.0.1", "", null,
//            "", "", Collections.emptyList());
//
//    @Bean
//    public Docket petApi() {
//        return new Docket(DocumentationType.SWAGGER_2).select()
//                // 仅显示 com.cdz360.biz.ant.rest 目录下的接口
//                .apis(RequestHandlerSelectors.basePackage("com.cdz360.biz.auth.rest"))
//                .build().apiInfo(apiInfo);
//    }


}
