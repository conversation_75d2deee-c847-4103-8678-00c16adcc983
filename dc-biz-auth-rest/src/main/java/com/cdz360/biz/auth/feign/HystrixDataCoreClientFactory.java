package com.cdz360.biz.auth.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.sys.param.SiteGroupSiteParam;
import com.cdz360.biz.model.sys.vo.AccRelativeOrderVo;
import java.util.List;

import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class HystrixDataCoreClientFactory implements FallbackFactory<DataCoreFeignClient> {

    @Override
    public DataCoreFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, throwable.getStackTrace());

        return new DataCoreFeignClient() {
            @Override
            public ListResponse<AccRelativeOrderVo> getOrderNum(List<Long> sysUidList) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<Boolean> existUnfinishedRecord(List<Long> sysUidList) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Boolean> existUnfinishedOrder(List<Long> sysUidList) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Long> getChargerNumByChain(String commIdChain) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<String> findSiteGroupSiteBySiteId(SiteGroupSiteParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<String> getSiteListByGids(ListSiteParam params) {
                return RestUtils.serverBusy4ListResponse();
            }
        };
    }
}
