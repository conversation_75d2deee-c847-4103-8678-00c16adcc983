<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.auth.ds.rw.remind.mapper.RemindAccountRwMapper">
  <delete id="removeExclude">
    delete from t_remind_account
    where corpId=#{corpId}
    <foreach collection="uidList" item="uid" open="and uid in (" close=")" separator=",">
      #{uid}
    </foreach>
  </delete>

  <select id="insertOrIgnore" resultType="java.lang.Integer">
    insert ignore
    into t_remind_account(corpId, uid, createTime)
    values
    <foreach collection="uidList" item="uid" open="" close="" separator=",">
      (#{corpId}, #{uid}, now())
    </foreach>
  </select>
</mapper>

