package com.cdz360.biz.auth.service;

import com.cdz360.biz.auth.model.vo.SysRole;
import com.cdz360.biz.auth.service.oauth.ShiroHttpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class ShiroServiceImpl extends ShiroHttpService {
    @Autowired
    SysRoleService roleService;
    @Autowired
    SysMenuService menuController;
    @Autowired
    @Qualifier("authRestTpl")
    RestTemplate restTemplate;

//    @Override
//    public RestTemplate getRestTemplate() {
//        return this.restTemplate;
//    }

    @Override
    public Set<String> roleSet(Long userId, String token) {
        List<SysRole> roles = roleService.findByUserId(userId);
        Set<String> collect = roles.stream().map(SysRole::getName).collect(Collectors.toSet());
        return collect;
    }

    @Override
    public Set<String> permSet(Long userId, String token) {
        return menuController.findPermByUserId(userId);
    }

}
