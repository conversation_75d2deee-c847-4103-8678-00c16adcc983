package com.cdz360.biz.auth.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.auth.service.TCityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * TCityController
 *  TODO
 * @since 2019/5/27
 * <AUTHOR>
 */
@RestController
@RequestMapping("/data/city")
public class TCityController //implements TCityInterface
{

    @Autowired
    TCityService tCityService;


    @GetMapping("/getCityCode")
    public ObjectResponse<String> getCityCode(String cityName) {
        return new ObjectResponse<>(tCityService.getCityCode(cityName));
    }
}