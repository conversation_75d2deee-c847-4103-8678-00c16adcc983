package com.cdz360.biz.auth.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.auth.user.vo.UserBalanceVo;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.message.po.MsgTemplatePO;
import com.cdz360.biz.model.cus.user.param.ModifyCusInfoParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * CarUserFeignClient
 *
 * @since 2019/6/13 17:25
 * <AUTHOR>
 */
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_USER, fallbackFactory = HystrixMerchantClientFactory.class)
public interface CarUserFeignClient {

    @RequestMapping(value = "/api/blocUser/createBlocUserByPhone", method = RequestMethod.POST)
    ObjectResponse<Long> createBlocUserByPhone(@RequestBody CorpPo corp);

    @RequestMapping(value = "/api/user/findByUserId", method = RequestMethod.POST)
    ObjectResponse<UserBalanceVo> findByUserId(@RequestParam(value = "userId") Long userId);

    @RequestMapping(value = "/api/user/updateCusInfo", method = RequestMethod.POST)
    ObjectResponse updateCusInfo(@RequestBody ModifyCusInfoParam param);

    @GetMapping(value = "/api/msg/template/getMsgTemplate")
    ObjectResponse<MsgTemplatePO> getMsgTemplate(@RequestParam("key") String key,
                                                 @RequestParam("topCommId") Long topCommId);

//    /**
//     * 判断企业名称是否存在
//     * @param corpName
//     * @return
//     */
//    @GetMapping("/api/blocUser/getCorpCountByCorpName")
//    ObjectResponse<Long> getCorpCountByCorpName(@RequestParam(value = "corpName") String corpName);

//    /**
//     * 判断登录账号是否存在
//     * @param account
//     * @return
//     */
//    @GetMapping("/api/blocUser/getCorpCountByAccount")
//    public ObjectResponse<Long> getCorpCountByAccount(@RequestParam(value = "account") String account);
}