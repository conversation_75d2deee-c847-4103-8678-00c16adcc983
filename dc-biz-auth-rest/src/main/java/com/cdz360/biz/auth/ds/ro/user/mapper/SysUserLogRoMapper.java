package com.cdz360.biz.auth.ds.ro.user.mapper;

import com.cdz360.biz.auth.user.param.SysUserLogParam;
import com.cdz360.biz.auth.user.po.SysUserLogPo;
import com.cdz360.biz.auth.user.vo.SysUserLogVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysUserLogRoMapper {

	SysUserLogPo getById(@Param("id") Long id);

	List<SysUserLogVo> getLoginLog(SysUserLogParam param);
	Long getLoginLogCount(SysUserLogParam param);

	List<SysUserLogVo> getOpLog(SysUserLogParam param);
	Long getOpLogCount(SysUserLogParam param);
}
