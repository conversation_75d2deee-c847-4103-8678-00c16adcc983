package com.cdz360.biz.auth;

import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@Slf4j
@SpringBootApplication
@EnableTransactionManagement
@ComponentScan(basePackages = {"com.cdz360.biz.auth"})
@MapperScan(basePackages = {
        "com.cdz360.biz.auth.ds.ro.*.mapper",
        "com.cdz360.biz.auth.ds.rw.*.mapper"
})
public class AuthRestTestMain {

    /**
     * 主函数
     */
    public static void main(String[] args) {
        log.info("starting....");
        SpringApplication.run(AuthRestTestMain.class, args);
        log.info("started");
    }
}
