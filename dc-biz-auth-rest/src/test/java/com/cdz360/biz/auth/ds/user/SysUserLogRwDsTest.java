package com.cdz360.biz.auth.ds.user;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.vo.KvObject;
import com.cdz360.biz.auth.AuthRestTestBase;
import com.cdz360.biz.auth.ds.rw.user.SysUserLogRwDs;
import com.cdz360.biz.auth.user.po.SysUserLogPo;
import com.cdz360.biz.auth.user.type.LogOpType;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
public class SysUserLogRwDsTest extends AuthRestTestBase {

    @Autowired
    private SysUserLogRwDs sysUserLogRwDs;

    @Test
    public void test_insertSysUserLog() {
        log.info(">>");
        SysUserLogPo sysUserLog = new SysUserLogPo();
        sysUserLog.setTopCommId(1L).setCommId(2L).setSysUid(3L)
                .setOpType(LogOpType.LOGIN).setClientType(AppClientType.MGM_WEB);
        KvObject kv = new KvObject();
        kv.setKey("aaaa");
        kv.setValue(List.of("eeeeee","rrrrrrrrr"));
        KvObject kv2 = new KvObject();
        kv2.setKey("aaaa");
        kv2.setValue("按时到场");
        sysUserLog.setOpObject(List.of(kv, kv2));
        sysUserLogRwDs.insertSysUserLog(sysUserLog);

        SysUserLogPo readback = this.sysUserLogRwDs.getById(sysUserLog.getId(), false);
        log.info("sysUserLog = {}", readback);
    }
}
