package com.cdz360.biz.auth.rest;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.auth.BaseMockTest;
import com.cdz360.biz.auth.zft.dto.ZftDto;
import com.cdz360.biz.auth.zft.param.ListZftParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;

@Slf4j
class ZftRestTest extends BaseMockTest {

    private MockMvc mockMvc;
    @Autowired
    private WebApplicationContext wac;

    @BeforeEach
    public void setUp() throws Exception {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    void zftList() throws Exception {
        String url = "/api/zft/zftList";

        ListZftParam param = new ListZftParam();
        param.setTotal(true)
                .setStart(0L)
                .setSize(10);

        String req = JsonUtils.toJsonString(param); // 请求体
        log.info("req param = {}", req);

        MockHttpServletRequestBuilder rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON).content(req);

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(ZftRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("zftList")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").doesNotExist())
                .andDo(print())
                .andReturn();
    }

    @Test
    void getZft() throws Exception {
        MockHttpServletRequestBuilder rb = null;
        String url = null;

        // 查询是否存在
        url = "/api/zft/getZft";
        rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("id", "0");
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(ZftRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("getZft")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_ARGUMENT_ERROR))
//                .andExpect(jsonPath("$.data").doesNotExist())
                .andDo(print())
                .andReturn();
    }

    @Test
    void updateZft() throws Exception {
        String url = "/api/zft/updateZft";

        ZftDto dto = new ZftDto();
        dto.setName("扬州鼎充");
        dto.setEnableBalance(true);
        dto.setWxMchSwitch(true)
                .setWxSubMchId("123456789")
                .setWxSubMchName("微信支付商")
                .setAlipaySwitch(true)
                .setAlipaySubMchId("987654321")
                .setAlipaySubMchName("支付宝直付商")
                .setCommIdList(List.of())
                .setUpdateOpId(0L)
                .setUpdateOpName("sys");

        String req = JsonUtils.toJsonString(dto); // 请求体
        log.info("req param = {}", req);

        MockHttpServletRequestBuilder rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON).content(req);

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(ZftRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("updateZft")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").doesNotExist())
                .andDo(print())
                .andReturn();

        dto.setId(1L)
                .setName("new扬州鼎充");
        rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON).content(JsonUtils.toJsonString(dto));

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(ZftRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("updateZft")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").doesNotExist())
                .andDo(print())
                .andReturn();
    }
}