package com.cdz360.biz.auth.service;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.auth.AuthWebApplication;
import com.cdz360.biz.model.sys.constant.ReportPage;
import com.cdz360.biz.model.sys.po.SysUserReportTemplatePo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.List;

@Slf4j
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = AuthWebApplication.class)
class ReportTemplateServiceTest {

    @Autowired
    private ReportTemplateService service;

    @Test
    void getInfo() {
        List<SysUserReportTemplatePo> res = service.getInfo(123, null);
        log.info("{}", JsonUtils.toJsonString(res));
    }

    @Test
    void add() {
        SysUserReportTemplatePo template = new SysUserReportTemplatePo();
        template.setTemplateName("asdasdasdasd")
                .setSysUserId(63270L)
                .setPage(ReportPage.CORP_ORDER_LIST)
                .setQueryConditions(List.of("orderNo"))
                .setColumns(List.of("orderNo"));

        log.info("res: {}", service.add(template));
    }
}