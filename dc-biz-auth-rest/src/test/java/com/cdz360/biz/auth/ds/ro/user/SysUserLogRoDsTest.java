package com.cdz360.biz.auth.ds.ro.user;

import com.cdz360.biz.auth.AuthRestTestBase;
import com.cdz360.biz.auth.user.po.SysUserLogPo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
public class SysUserLogRoDsTest extends AuthRestTestBase {
    @Autowired
    private SysUserLogRoDs sysUserLogRoDs;

    @Test
    public void test_insertSysUserLog() {
        log.info(">>");

        SysUserLogPo readback = sysUserLogRoDs.getById(1L);
        log.info("sysUserLog = {}", readback);
    }
}