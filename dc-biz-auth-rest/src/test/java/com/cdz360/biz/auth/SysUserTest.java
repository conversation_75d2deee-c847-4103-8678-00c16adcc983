package com.cdz360.biz.auth;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.junit.jupiter.api.Test;

public class SysUserTest {

    @Test
    public void setUp() throws Exception {
    }

    @Test
    public void json() throws IOException {
        SysUser sysUser = new SysUser();
        sysUser.setPassword("123");
        sysUser.setUsername("pat");
        sysUser.setSalt("xafsfa");
        String s = JsonUtils.toJsonString(sysUser);
        System.out.println("s = " + s);
        Object parse = JsonUtils.fromJson(s, SysUser.class);
        System.out.println("parse = " + parse);

        try {
            SysUser sysUser1 = new ObjectMapper().readValue(s, SysUser.class);
            System.out.println("sysUser1 = " + sysUser1);
            String s1 = new ObjectMapper().writeValueAsString(sysUser1);
            System.out.println("s1 = " + s1);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void t3() {
        String s = "Duplicate entry '7-业务管理' for key 'uk_sysid_name'";
        Pattern pattern = Pattern.compile(".*'(.*?)'for key '(.*?)'.*");
        Matcher matcher = pattern.matcher(s);

        while (matcher.find()) {
            System.out.println("find");
            System.out.println(matcher.group());
            System.out.println(matcher.groupCount());
        }

    }
}