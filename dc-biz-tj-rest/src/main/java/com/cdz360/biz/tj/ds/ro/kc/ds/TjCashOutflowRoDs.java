package com.cdz360.biz.tj.ds.ro.kc.ds;


import com.cdz360.biz.model.tj.kc.param.ListTjMaterialCostParam;
import com.cdz360.biz.model.tj.kc.po.TjCashOutflowPo;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjCashOutflowRoMapper;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjDailyChargingDurationRoMapper;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjCashOutflowRoDs {


    @Autowired

    private TjCashOutflowRoMapper tjCashOutflowRoMapper;


    public TjCashOutflowPo getById(Long id) {

        return this.tjCashOutflowRoMapper.getById(id);

    }

    public List<TjCashOutflowPo> findTjCashOutflow(Integer type) {
        return this.tjCashOutflowRoMapper.findTjCashOutflow(type);
    }

    public List<TjCashOutflowPo> findAllTjCashOutflow() {
        return this.tjCashOutflowRoMapper.findAllTjCashOutflow();
    }
}

