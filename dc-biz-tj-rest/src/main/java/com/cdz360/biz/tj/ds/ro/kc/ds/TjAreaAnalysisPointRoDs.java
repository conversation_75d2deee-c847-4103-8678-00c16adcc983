package com.cdz360.biz.tj.ds.ro.kc.ds;

import com.cdz360.biz.tj.ds.ro.kc.mapper.TjAreaAnalysisPointRoMapper;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaAnalysisPointParam;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisPointVo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisPointWithSiteVo;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TjAreaAnalysisPointRoDs {

    @Autowired
    private TjAreaAnalysisPointRoMapper tjAreaAnalysisPointRoMapper;

    public List<TjAreaAnalysisPointVo> findTjAnalysisPoint(ListTjAreaAnalysisPointParam param) {
        return tjAreaAnalysisPointRoMapper.findTjAnalysisPoint(param)
            .stream()
            .map(x -> {
                TjAreaAnalysisPointVo tmp = new TjAreaAnalysisPointVo();
                BeanUtils.copyProperties(x, tmp);
                return tmp;
            }).collect(Collectors.toList());
    }

    public List<TjAreaAnalysisPointWithSiteVo> findTjAnalysisPointWithSite(
        ListTjAreaAnalysisPointParam param) {
        return tjAreaAnalysisPointRoMapper.findTjAnalysisPointWithSite(param)
            .stream()
            .map(x -> {
                TjAreaAnalysisPointWithSiteVo tmp = new TjAreaAnalysisPointWithSiteVo();
                BeanUtils.copyProperties(x, tmp);
                return tmp;
            }).collect(Collectors.toList());
    }

    public long countTjAnalysisPoint(ListTjAreaAnalysisPointParam param) {
        return tjAreaAnalysisPointRoMapper.countTjAnalysisPoint(param);
    }
}

