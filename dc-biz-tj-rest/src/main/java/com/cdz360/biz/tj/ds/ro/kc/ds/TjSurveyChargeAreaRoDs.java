package com.cdz360.biz.tj.ds.ro.kc.ds;


import com.cdz360.biz.model.tj.kc.po.TjSurveyChargeAreaPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveyChargeAreaRoMapper;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveyHighVoltageRoMapper;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjSurveyChargeAreaRoDs {


    @Autowired

    private TjSurveyChargeAreaRoMapper tjSurveyChargeAreaRoMapper;


    public TjSurveyChargeAreaPo getById(Long id) {

        return this.tjSurveyChargeAreaRoMapper.getById(id);

    }

    public List<TjSurveyChargeAreaPo> findTjSurveyChargeAreaBySurveyNo(String surveyNo) {
        return this.tjSurveyChargeAreaRoMapper.findTjSurveyChargeAreaBySurveyNo(surveyNo);
    }

}

