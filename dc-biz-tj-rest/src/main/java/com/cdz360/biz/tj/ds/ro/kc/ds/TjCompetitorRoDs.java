package com.cdz360.biz.tj.ds.ro.kc.ds;

import com.cdz360.biz.tj.ds.ro.kc.mapper.TjCompetitorRoMapper;
import com.cdz360.biz.model.tj.kc.param.ListTjCompetitorParam;
import com.cdz360.biz.model.tj.kc.po.TjCompetitorPo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j
@Service
public class TjCompetitorRoDs {

    @Autowired
    private TjCompetitorRoMapper tjCompetitorRoMapper;

    public List<TjCompetitorPo> findTjCompetitor(ListTjCompetitorParam param) {
        return this.tjCompetitorRoMapper.findTjCompetitor(param);
    }

    public long countTjCompetitor(ListTjCompetitorParam param) {
        return this.tjCompetitorRoMapper.countTjCompetitor(param);
    }

    public TjCompetitorPo getById(Long id) {
        return this.tjCompetitorRoMapper.getById(id);
    }

    public TjCompetitorPo getOneByName(String name) {
        return tjCompetitorRoMapper.getOneByName(name);
    }
}

