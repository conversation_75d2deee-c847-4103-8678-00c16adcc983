package com.cdz360.biz.tj.ds.ro.kc.ds;

import com.cdz360.biz.model.tj.kc.param.ListSiteWithinTjAreaParam;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaParam;
import com.cdz360.biz.model.tj.kc.po.TjAreaPo;
import com.cdz360.biz.model.tj.kc.vo.SiteWithinTjVo;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjAreaRoMapper;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TjAreaRoDs {

    @Autowired
    private TjAreaRoMapper tjAreaRoMapper;

    public TjAreaPo getByAid(Long aid) {
        return this.tjAreaRoMapper.getByAid(aid);
    }

    public List<TjAreaPo> findArea(ListTjAreaParam param) {
        return this.tjAreaRoMapper.findArea(param);
    }

    public long count(ListTjAreaParam param) {
        return this.tjAreaRoMapper.count(param);
    }

    public List<TjAreaPo> findAllUserArea(ListTjAreaParam param) {
        return this.tjAreaRoMapper.findAllUserArea(param);
    }

    public List<SiteWithinTjVo> findSiteWithinTjArea(ListSiteWithinTjAreaParam param) {
        return this.tjAreaRoMapper.findSiteWithinTjArea(param);
    }

    public long countSiteWithinTjArea(ListSiteWithinTjAreaParam param) {
        return this.tjAreaRoMapper.countSiteWithinTjArea(param);
    }
}

