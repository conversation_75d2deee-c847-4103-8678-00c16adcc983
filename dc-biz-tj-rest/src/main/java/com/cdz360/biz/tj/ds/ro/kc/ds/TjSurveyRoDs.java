package com.cdz360.biz.tj.ds.ro.kc.ds;

import com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveyRoMapper;
import com.cdz360.biz.model.tj.kc.param.ListTjSurveyParam;
import com.cdz360.biz.model.tj.kc.param.RepeatSurveyParam;
import com.cdz360.biz.model.tj.kc.param.TjSurveyBiParam;
import com.cdz360.biz.model.tj.kc.po.TjSurveyPo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyBiVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyVo;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TjSurveyRoDs {

    @Autowired
    private TjSurveyRoMapper tjSurveyRoMapper;

    public TjSurveyPo getByNo(String no) {
        return this.tjSurveyRoMapper.getByNo(no);
    }

    public List<TjSurveyPo> findTjSurvey(ListTjSurveyParam param) {
        return this.tjSurveyRoMapper.findTjSurvey(param);
    }

    public long countTjSurvey(ListTjSurveyParam param) {
        return this.tjSurveyRoMapper.countTjSurvey(param);
    }

    public TjSurveyBiVo tjSurveyBi(TjSurveyBiParam param) {
        return this.tjSurveyRoMapper.tjSurveyBi(param);
    }

    public List<TjSurveyVo> repeatSurvey(RepeatSurveyParam param) {
        return this.tjSurveyRoMapper.repeatSurvey(param)
            .stream().map(l -> {
                val r = new TjSurveyVo();
                BeanUtils.copyProperties(l, r);
                return r;
            }).collect(Collectors.toList());
    }
}

