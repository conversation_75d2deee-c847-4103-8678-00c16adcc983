package com.cdz360.biz.tj.ds.ro.kc.ds;


import com.cdz360.biz.model.tj.kc.po.TjDepreciationPo;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjDepreciationRoMapper;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjDepreciationRwMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjDepreciationRoDs {


    @Autowired

    private TjDepreciationRoMapper tjDepreciationRoMapper;


    public TjDepreciationPo getById(Long id) {
        return this.tjDepreciationRoMapper.getById(id);
    }

    public TjDepreciationPo findTjDepreciation() {
        return this.tjDepreciationRoMapper.findTjDepreciation();
    }

}

