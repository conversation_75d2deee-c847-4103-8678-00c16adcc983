package com.cdz360.biz.tj.ds.ro.kc.ds;


import com.cdz360.biz.model.tj.kc.po.TjSurveyChargeAreaPilePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveyChargeAreaPileRoMapper;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveyChargeAreaRoMapper;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveyHighVoltageRoMapper;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjSurveyChargeAreaPileRoDs {


    @Autowired

    private TjSurveyChargeAreaPileRoMapper tjSurveyChargeAreaPileRoMapper;


    public TjSurveyChargeAreaPilePo getById(Long id) {

        return this.tjSurveyChargeAreaPileRoMapper.getById(id);

    }

    public List<TjSurveyChargeAreaPilePo> findTjSurveyChargeAreaPileByAreaId(Long areaId) {
        return this.tjSurveyChargeAreaPileRoMapper.findTjSurveyChargeAreaPileByAreaId(areaId);
    }

    public List<TjSurveyChargeAreaPilePo> findTjSurveyChargeAreaPileBySurveyNo(String surveyNo) {
        return this.tjSurveyChargeAreaPileRoMapper.findTjSurveyChargeAreaPileBySurveyNo(surveyNo);
    }

}

