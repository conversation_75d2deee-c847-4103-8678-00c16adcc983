package com.cdz360.biz.tj.ds.ro.kc.ds;


import com.cdz360.biz.model.tj.kc.param.ListTjMaterialCostParam;
import com.cdz360.biz.model.tj.kc.po.TjMaterialCostPo;
import com.cdz360.biz.model.tj.kc.vo.TjMaterialCostVo;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjMaterialCostRoMapper;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjMaterialCostRwMapper;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjMaterialCostRoDs {


    @Autowired

    private TjMaterialCostRoMapper tjMaterialCostRoMapper;


    public TjMaterialCostPo getById(Long id) {

        return this.tjMaterialCostRoMapper.getById(id);

    }

    public List<TjMaterialCostVo> findTjMaterialCost(ListTjMaterialCostParam param) {
        return this.tjMaterialCostRoMapper.findTjMaterialCost(param);
    }

    public long countTjMaterialCost(ListTjMaterialCostParam param) {
        return this.tjMaterialCostRoMapper.countTjMaterialCost(param);
    }

}

