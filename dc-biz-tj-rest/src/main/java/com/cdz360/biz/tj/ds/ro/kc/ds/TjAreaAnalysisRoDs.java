package com.cdz360.biz.tj.ds.ro.kc.ds;

import com.cdz360.biz.tj.ds.ro.kc.mapper.TjAreaAnalysisRoMapper;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaAnalysisParam;
import com.cdz360.biz.model.tj.kc.po.TjAreaAnalysisPo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisWithPointVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TjAreaAnalysisRoDs {

    @Autowired
    private TjAreaAnalysisRoMapper tjAreaAnalysisRoMapper;

    public TjAreaAnalysisPo getById(Long id) {
        return this.tjAreaAnalysisRoMapper.getById(id);
    }

    public List<TjAreaAnalysisPo> findTjAnalysis(ListTjAreaAnalysisParam param) {
        return tjAreaAnalysisRoMapper.findTjAnalysis(param);
    }

    public long countTjAnalysis(ListTjAreaAnalysisParam param) {
        return tjAreaAnalysisRoMapper.countTjAnalysis(param);
    }

    public TjAreaAnalysisWithPointVo getWithPointById(Long analysisId) {
        TjAreaAnalysisWithPointVo result = tjAreaAnalysisRoMapper.getWithPointById(analysisId);
        if (null == result) {
            return null;
        }
        TjAreaAnalysisWithPointVo tmp = new TjAreaAnalysisWithPointVo(); // mybatis 结果类名不一样
        BeanUtils.copyProperties(result, tmp);
        return tmp;
    }
}

