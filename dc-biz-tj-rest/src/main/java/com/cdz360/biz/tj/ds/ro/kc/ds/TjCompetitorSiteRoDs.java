package com.cdz360.biz.tj.ds.ro.kc.ds;

import com.cdz360.biz.tj.ds.ro.kc.mapper.TjCompetitorSiteRoMapper;
import com.cdz360.biz.model.tj.kc.param.ListTjCompetitorSiteParam;
import com.cdz360.biz.model.tj.kc.po.TjCompetitorSitePo;
import com.cdz360.biz.model.tj.kc.vo.TjCompetitorSiteVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TjCompetitorSiteRoDs {

    @Autowired
    private TjCompetitorSiteRoMapper tjCompetitorSiteRoMapper;

    public TjCompetitorSitePo getById(Long id) {
        return this.tjCompetitorSiteRoMapper.getById(id);
    }

    public List<TjCompetitorSiteVo> findTjCompetitorSite(ListTjCompetitorSiteParam param) {
        return this.tjCompetitorSiteRoMapper.findTjCompetitorSite(param);
    }

    public long countTjCompetitorSite(ListTjCompetitorSiteParam param) {
        return this.tjCompetitorSiteRoMapper.countTjCompetitorSite(param);
    }

}

