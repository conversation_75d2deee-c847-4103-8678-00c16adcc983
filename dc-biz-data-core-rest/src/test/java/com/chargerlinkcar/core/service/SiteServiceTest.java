package com.chargerlinkcar.core.service;

import com.cdz360.biz.dc.domain.SendSiteBiParamParse;
import com.cdz360.biz.dc.service.site.SiteBizService;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ds.trading.rw.site.ds.BiErpSiteRwDs;
import com.cdz360.biz.model.trading.bi.param.ListBiErpSiteParam;
import com.cdz360.biz.model.trading.bi.po.BiErpSitePo;
import com.cdz360.biz.model.trading.bi.type.ErpSiteStatus;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.chargerlinkcar.core.BaseMockTest;
import com.chargerlinkcar.framework.common.service.BiErpSiteSeqNoGenerator;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

@Slf4j
class SiteServiceTest extends BaseMockTest {

    @Autowired
    private SiteBizService siteService;

    @Autowired
    private SiteRoDs siteDs;

    @Autowired
    private BiErpSiteRwDs biErpSiteRwDs;

    @Autowired
    private BiErpSiteSeqNoGenerator biErpSiteSeqNoGenerator;

    @Test
    public void test_sendErpSiteBi() {
//        String param = "";
//        String param = "auto";
//        String param = "resend";
//        String param = "resend.siteId=20190620758035403755788743";
//        String param = "resend.siteId=20190620758035403755788743;date=2020-04-10";
//        String param = "resend.siteId=20190620758035403755788743,2004096325574525944";
//        String param = "resend.siteId=20190620758035403755788743,2004096325574525944;date=2020-04-10";
        String param = "resend.fail";
//        String param = "resend.fail.siteId=201906200758035403755788743";
//        String param = "resend.fail.siteId=20190620758035403755788743;date=2020-04-10";
//        String param = "resend.fail.siteId=20190620758035403755788743,2004096325574525944";
//        String param = "resend.fail.siteId=20190620758035403755788743,2004096325574525944;date=2020-04-10";

        // 下面方式不要随便使用
//        String param = "send.siteId=20190620758035403755788743,2004096325574525944";
        siteService.sendErpSiteBi(param);

        // 已经关账的测试，需要erp中处理，让记录返回判断值
        // YYSL20040165	121001
        // YYSL20040169	123001
    }

    @Test
    public void test_biErpSiteOp() {
        String seqNo = biErpSiteSeqNoGenerator.nextSeqNo();
        BiErpSitePo po = new BiErpSitePo();
        po.setSiteId("11111111")
                .setStatus(ErpSiteStatus.SENDING)
                .setIncomeTime(LocalDateTime.now())
                .setTotalOrderPrice(BigDecimal.TEN)
                .setTotalServicePrice(BigDecimal.ONE)
                .setTotalElectricPrice(BigDecimal.TEN.subtract(BigDecimal.ONE))
//                .setTotalPower(BigDecimal.TEN)
                .setSeqNo(seqNo);
        int i = biErpSiteRwDs.add(po);
        log.info("添加 i = {}", i);

        po.setStatus(ErpSiteStatus.SENT);
        po.setRemark("推送成功");
        i = biErpSiteRwDs.updateById(po);
        log.info("update i = {}", i);

        po.setStatus(ErpSiteStatus.FAIL);
        i = biErpSiteRwDs.updateById(po);
        log.info("update i = {}", i);

        ListBiErpSiteParam param = new ListBiErpSiteParam();
        param.setStatusList(List.of(ErpSiteStatus.FAIL));
        param.setSiteIdList(List.of("6570716288362535235"));
        param.setIncomeTime(LocalDateTime.of(2020, 2, 1, 0, 0, 0));
        List<BiErpSitePo> biErpSitePos = biErpSiteRwDs.listErpSite(param);
        log.info("list size = {}", biErpSitePos.size());
    }

    @Test
    public void test_paramParse() {
        // 数据库中默认需要推送的场站列表
        List<SitePo> sendSiteIdList = siteService.getSendSiteList(null);

        // 下一月份值
        int lastYear = LocalDate.now().minusYears(1).getYear();
        int lastMonth = LocalDate.now().minusMonths(1).getMonthValue();
        int nextMonth = LocalDate.now().getMonthValue();

        // 正常推送使用的参数
        String param = null;
        SendSiteBiParamParse result = null;

        result = siteService.paramParse(param);
//        Assertions.assertEquals(lastMonth, result.getMonth(), "不传入月份，默认上一个月份值");
//        Assertions.assertEquals(LocalDate.now().getYear(), result.getYear(), "不传入月份，默认上一个月份值");
        Assertions.assertEquals(sendSiteIdList.size(), result.getSiteIdList().size(), "当月所有需要推送场站数据");
        param = "";
        result = siteService.paramParse(param);
//        Assertions.assertEquals(lastMonth, result.getMonth(), "不传入月份，默认上一个月份值");
//        Assertions.assertEquals(LocalDate.now().getYear(), result.getYear(), "不传入月份，默认上一个月份值");
        Assertions.assertEquals(sendSiteIdList.size(), result.getSiteIdList().size(), "当月所有需要推送场站数据");
        param = "auto";
        result = siteService.paramParse(param);
//        Assertions.assertEquals(lastMonth, result.getMonth(), "不传入月份，默认上一个月份值");
//        Assertions.assertEquals(LocalDate.now().getYear(), result.getYear(), "不传入月份，默认上一个月份值");
        Assertions.assertEquals(sendSiteIdList.size(), result.getSiteIdList().size(), "当月所有需要推送场站数据");

        // 重发的参数
        param = "resend";
        result = siteService.paramParse(param);
//        Assertions.assertEquals(LocalDate.now().minusMonths(1).getMonthValue(),
//                result.getMonth(), "不传入月份，默认上一个月份值");
        Assertions.assertEquals(sendSiteIdList.size(), result.getSiteIdList().size(), "当月所有需要推送场站数据");

        param = "resend.siteId=1111,2222";
        result = siteService.paramParse(param);
//        Assertions.assertEquals(LocalDate.now().minusMonths(1).getMonthValue(),
//                result.getMonth(), "不传入月份，默认当前月份值");
        Assertions.assertEquals(0, result.getSiteIdList().size(), "当月所有需要推送场站数据");

        param = "resend.siteId=1111,6570716288362535235";
        result = siteService.paramParse(param);
//        Assertions.assertEquals(LocalDate.now().minusMonths(1).getMonthValue(),
//                result.getMonth(), "不传入月份，默认当前月份值");
        Assertions.assertEquals(1, result.getSiteIdList().size(), "当月所有需要推送场站数据");

        param = "resend.siteId=1111,6570716288362535235;month=" + lastMonth;
        result = siteService.paramParse(param);
//        Assertions.assertEquals(LocalDate.now().getYear(), result.getYear(), "传入月份值需要相等");
//        Assertions.assertEquals(lastMonth, result.getMonth(), "传入月份值需要相等");
        Assertions.assertEquals(1, result.getSiteIdList().size(), "当月所有需要推送场站数据");

        param = "resend.siteId=1111,6570716288362535235;year=" + lastYear + ";month=" + lastMonth;
        result = siteService.paramParse(param);
//        Assertions.assertEquals(lastYear, result.getYear(), "传入月份值需要相等");
//        Assertions.assertEquals(lastMonth, result.getMonth(), "传入月份值需要相等");
        Assertions.assertEquals(1, result.getSiteIdList().size(), "当月所有需要推送场站数据");

        param = "resend.siteId=1111,6570716288362535235;year=" + lastYear + ";month=" + nextMonth;
        result = siteService.paramParse(param);
//        Assertions.assertEquals(lastYear, result.getYear(), "传入月份值需要相等");
//        Assertions.assertEquals(nextMonth, result.getMonth(), "传入月份值需要相等");
        Assertions.assertEquals(1, result.getSiteIdList().size(), "当月所有需要推送场站数据");

        try {
            param = "resend.month=" + 13;
            result = siteService.paramParse(param);

            param = "resend.siteId=1111,6570716288362535235;month=" + nextMonth;
            result = siteService.paramParse(param);
        } catch (Exception e) {
            Assert.hasText(e.getMessage(), "抛出异常: 当前月份值无效");
        }

        ListBiErpSiteParam biErpSiteParam = new ListBiErpSiteParam();
        biErpSiteParam.setStatusList(List.of(ErpSiteStatus.FAIL));

        LocalDateTime incomeTime = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0)
                .with(TemporalAdjusters.firstDayOfMonth()).minusMonths(1);

        param = "resend.fail";
        result = siteService.paramParse(param);
//        Assertions.assertEquals(lastMonth, result.getMonth(), "不传入月份，默认上一个月份值");
//        Assertions.assertEquals(LocalDate.now().getYear(), result.getYear(), "不传入月份，默认上一个月份值");
        // 这个Case 不需要
//        Assertions.assertEquals(biErpSiteRwDs.listErpSite(
//                biErpSiteParam.setIncomeTime(incomeTime)).size(), result.getSiteIdList().size(), "当月所有需要推送场站数据");

        param = "resend.fail.siteId=1111,2222";
        result = siteService.paramParse(param);
//        Assertions.assertEquals(lastMonth, result.getMonth(), "不传入月份，默认当前月份值");
        Assertions.assertEquals(0, result.getSiteIdList().size(), "当月所有需要推送场站数据");

        param = "resend.fail.siteId=1111,6570716288362535235";
        result = siteService.paramParse(param);
//        Assertions.assertEquals(lastMonth, result.getMonth(), "不传入月份，默认当前月份值");
//        Assertions.assertEquals(1, result.getSiteIdList().size(), "当月所有需要推送场站数据");

        param = "resend.fail.siteId=1111,6570716288362535235;month=" + lastMonth;
        result = siteService.paramParse(param);
//        Assertions.assertEquals(LocalDate.now().getYear(), result.getYear(), "传入月份值需要相等");
//        Assertions.assertEquals(lastMonth, result.getMonth(), "传入月份值需要相等");
//        Assertions.assertEquals(1, result.getSiteIdList().size(), "当月所有需要推送场站数据");

        param = "resend.fail.year=" + lastYear + ";month=" + lastMonth;
        result = siteService.paramParse(param);
//        Assertions.assertEquals(lastYear, result.getYear(), "传入月份值需要相等");
//        Assertions.assertEquals(lastMonth, result.getMonth(), "传入月份值需要相等");
//        Assertions.assertEquals(biErpSiteRwDs.listErpSite(
//                biErpSiteParam.setIncomeTime(result.getIncomeTime())).size(),
//                result.getSiteIdList().size(), "当月所有需要推送场站数据");

        param = "resend.fail.year=" + lastYear + ";month=" + nextMonth;
        result = siteService.paramParse(param);
//        Assertions.assertEquals(lastYear, result.getYear(), "传入月份值需要相等");
//        Assertions.assertEquals(nextMonth, result.getMonth(), "传入月份值需要相等");
//        Assertions.assertEquals(biErpSiteRwDs.listErpSite(
//                biErpSiteParam.setIncomeTime(result.getIncomeTime())).size(),
//                result.getSiteIdList().size(), "当月所有需要推送场站数据");

        try {
            param = "resend.fail.month=" + 13;
            result = siteService.paramParse(param);

            param = "resend.fail.siteId=1111,6570716288362535235;month=" + nextMonth;
            result = siteService.paramParse(param);
        } catch (Exception e) {
            Assert.hasText(e.getMessage(), "抛出异常: 当前月份值无效");
        }

        // 异常参数
        param = "-help";
        try {
            result = siteService.paramParse(param);
        } catch (Exception e) {
            Assert.hasText(e.getMessage(), "抛出异常: 请输入有效的参数");
            Assert.isTrue(e.getMessage().contains("请输入有效的参数"), "抛出异常: 请输入有效的参数");
        }
    }
}