package com.chargerlinkcar.core.service;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.dc.domain.vo.SiteInMongoVo;
import com.cdz360.biz.dc.service.site.SiteBizService;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.chargerlinkcar.core.BaseMockTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

@Slf4j
class SiteBizServiceTest extends BaseMockTest {

    @Autowired
    private SiteBizService siteBizService;

    @Test
    void getSiteListFromMongo() {

        String param = "{\"sk\":\"\",\"enable\":null,\"total\":null,\"start\":90,\"size\":10,\"sorts\":null,\"topCommId\":34474,\"geoNear\":{\"lng\":121.3216781616211,\"lat\":31.0958309173584,\"distance\":null},\"totalPower\":null}";

        ListSiteParam req = JsonUtils.fromJson(param, ListSiteParam.class);

        List<SiteInMongoVo> result = new ArrayList<>();

        long start = req.getStart();
        long size = req.getSize();
        do {
            log.info("start: {}, size: {}", start, size);
            result = siteBizService.getSiteListFromMongo(req);
            start += result.size();
            req.setStart(start);
        } while (!CollectionUtils.isEmpty(result));
    }
}