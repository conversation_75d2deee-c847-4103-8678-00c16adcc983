package com.chargerlinkcar.core.service;

import com.cdz360.biz.dc.service.OrderDataService;
import com.cdz360.biz.ds.trading.ro.order.mapper.ChargerOrderRoMapper;
import com.chargerlinkcar.core.BaseMockTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

@Slf4j
class OrderDataServiceTest extends BaseMockTest {

    @Autowired
    private OrderDataService orderDataService;

    @Mock
    private ChargerOrderRoMapper chargerOrderRoMapper;

    @Test
    void test_syncUserCommRef() throws InterruptedException {
//        orderDataService.syncUserCommRef();

        // 模拟多线程操作
        List<Thread> ts = new ArrayList<Thread>();
        for (int i = 0; i < 100; i++) {
            ts.add(new Thread(new Runnable() {
                public void run() {
                    orderDataService.syncUserCommRef();
                }
            }));
        }

        for (Thread t : ts) {
            t.start();
        }

        for (Thread t : ts) {
            while (t.isAlive()) {
                Thread.sleep(1);
            }
        }

    }

    @Test
    public void orderOvertimeParkingBi() {
//        chargerOrderRoMapper.orderOvertimeParkingBi();
        this.orderDataService.orderOvertimeParkingBi();
    }
}