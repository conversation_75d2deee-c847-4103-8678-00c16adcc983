package com.chargerlinkcar.core.service;

import com.cdz360.biz.dc.service.EvseCfgScheduleService;
import com.cdz360.biz.model.trading.site.po.EvseCfgSchedulePo;
import com.cdz360.biz.model.trading.site.vo.PriceSchemeSiteVo;
import com.chargerlinkcar.core.BaseMockTest;
import com.chargerlinkcar.framework.common.domain.param.ListPriceSchemeSiteUseParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

//@Transactional
@Slf4j
class EvseCfgScheduleServiceTest extends BaseMockTest {

    @Autowired
    private EvseCfgScheduleService evseCfgScheduleService;

    private static Long EVSE_NO_START = 11900000000L;
    private static Long EVSE_NO_RANGE = 10L;
    private static Date SCHEDULE_TIME = new Date();

    // 去掉秒/毫秒
    private Date getScheduleTime(Date date) {
        String pattern = "yyMMddHHmm";
        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
        try {
            return dateFormat.parse(dateFormat.format(date));
        } catch (ParseException e) {
            e.printStackTrace();
            return getScheduleTime(SCHEDULE_TIME);
        }
    }

    @BeforeEach
    public void init() {
        Long evseNo = EVSE_NO_START;

        Date scheduleTime = getScheduleTime(SCHEDULE_TIME);
        log.info("schedule time = {}", scheduleTime);

        EvseCfgSchedulePo po = new EvseCfgSchedulePo();
        po.setScheduleTime(scheduleTime)
                .setPriceSchemeId(1L)
                .setOpUid(1L);

        // 数据初始化
        List<EvseCfgSchedulePo> list = new ArrayList<>();
        for (int i = 0; i < EVSE_NO_RANGE; i++) {
            evseNo += i;
            list.add(po.setEvseNo(String.format("%012d", evseNo)));
        }

        int insert = evseCfgScheduleService.batchInsert(list);
        log.info("insert = {}", insert);
    }

    @Test
    void test_schedule() {
        evseCfgScheduleService.schedule();
    }

    @Test
    void test_batchInsert() {
    }

    @Test
    void  test_getByPriceSchemeId() {
        List<Long> priceSchemeIdList = List.of(627L, 624L, 618L, 614L, 602L, 596L, 595L, 584L, 581L, 576L);
        List<Long> commIdList = List.of(33421L, 34528L, 34529L, 34533L, 34534L, 34535L);
        ListPriceSchemeSiteUseParam param = new ListPriceSchemeSiteUseParam();
        param.setPriceSchemeIdList(priceSchemeIdList)
                .setCommIdChain(null);
        List<PriceSchemeSiteVo> res = evseCfgScheduleService.getByPriceSchemeId(param);
        log.info("size = {}", res.size());
    }

    @Test
    void test_updatePriceSchedule() {
        List<Long> temIdList = List.of(760L, 766L, 767L, 789L, 797L, 798L, 844L, 845L, 846L);
        evseCfgScheduleService.updatePriceSchedule(temIdList);
    }
}