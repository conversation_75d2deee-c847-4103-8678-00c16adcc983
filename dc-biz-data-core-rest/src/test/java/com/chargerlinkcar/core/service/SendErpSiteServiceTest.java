package com.chargerlinkcar.core.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.biz.dc.domain.SendSiteBi;
import com.cdz360.biz.dc.service.SendErpSiteService;
import com.chargerlinkcar.core.BaseMockTest;
import com.chargerlinkcar.framework.common.service.BiErpSiteSeqNoGenerator;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;

@Slf4j
class SendErpSiteServiceTest extends BaseMockTest {

    @Autowired
    private SendErpSiteService sendErpSiteService;

    @Autowired
    private BiErpSiteSeqNoGenerator biErpSiteSeqNoGenerator;

    @Test
    void test_save() {
        SendSiteBi bi = new SendSiteBi();
        bi.setSiteNo("CS-SITE-NO1")
                .setSeqNo(this.biErpSiteSeqNoGenerator.nextSeqNo())
                .setSiteId("222222")
                .setTotalOrderPrice(BigDecimal.TEN)
//                .setTotalPower(BigDecimal.TEN)
                .setTotalServicePrice(BigDecimal.ONE)
                .setTotalElectricPrice(BigDecimal.TEN.subtract(BigDecimal.ONE));

        BaseResponse res = sendErpSiteService.save(bi);
        log.info("res = {}", res);
    }

    @Test
    void test_view() {

        SendSiteBi bi = new SendSiteBi();
        bi.setSiteNo("CS-SITE-NO1")
                .setSeqNo(this.biErpSiteSeqNoGenerator.nextSeqNo())
                .setSiteId("222222")
                .setTotalOrderPrice(BigDecimal.TEN)
//                .setTotalPower(BigDecimal.TEN)
                .setTotalServicePrice(BigDecimal.ONE)
                .setTotalElectricPrice(BigDecimal.TEN.subtract(BigDecimal.ONE));

        BaseResponse res = sendErpSiteService.view(bi);
        log.info("res = {}", res);
    }
}