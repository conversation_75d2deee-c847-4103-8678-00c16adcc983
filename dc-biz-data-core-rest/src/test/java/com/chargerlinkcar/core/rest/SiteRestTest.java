package com.chargerlinkcar.core.rest;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.biz.dc.rest.site.SiteRest;
import com.chargerlinkcar.core.BaseMockTest;
import com.chargerlinkcar.framework.common.domain.SitePersonaliseDTO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.math.BigDecimal;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@Slf4j
@WebAppConfiguration
class SiteRestTest extends BaseMockTest {
    private static final String SITE_ID = "1073094969257926657";
    private static final SitePersonaliseDTO SITE_PERSONALISE_DTO = new SitePersonaliseDTO();

    private MockMvc mockMvc;

    @Autowired
    private WebApplicationContext wac;

    @BeforeEach
    public void setUp() throws Exception {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();

        SITE_PERSONALISE_DTO
                .setFrozenAmount(BigDecimal.TEN)
                .setParkTimeoutFee(Boolean.TRUE)
                .setOvertimeParkingTime(10)
                .setOvertimeParkingNum(10)
                .setStartCharingEnable(Boolean.FALSE)
                .setSiteId(SITE_ID);
    }

    @Test
    void getPersonalise() throws Exception {
        String url = "/dataCore/site/getPersonalise";

        MockHttpServletRequestBuilder rb = null;

        // 查询是否存在
        rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("siteId", SITE_ID);
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(SiteRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("getPersonalise")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();
    }

    @Test
    void updatePersonalise() throws Exception {

        String param = "{\"siteId\":\"2004228945251615529\",\"topCommId\":null,\"startCharingEnable\":true,\"settlementMethod\":1,\"payType\":null,\"commId\":null,\"corpUserId\":null,\"blocUserId\":null,\"phone\":null,\"commercialName\":null,\"siteCommId\":null,\"commIdChain\":null,\"userName\":null,\"blocUserName\":null,\"corpUserName\":null,\"commName\":null,\"commUserName\":null,\"frozenAmount\":20,\"parkTimeoutFee\":true,\"overtimeParkingNum\":1,\"overtimeParkingTime\":10,\"limitSoc\":false,\"socLimitTimes\":null,\"socLimit\":null}";

        String url = "/dataCore/site/updatePersonalise";

        MockHttpServletRequestBuilder rb = null;

        // 查询是否存在
        rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
//                .content(JsonUtils.toJsonString(SITE_PERSONALISE_DTO));
                .content(param);
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(SiteRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("updatePersonalise")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andDo(print())
                .andReturn();
    }

    @Test
    void getSiteListFromMongo() throws Exception {

        String param = "{\"sk\":\"\",\"enable\":null,\"total\":null,\"start\":90,\"size\":10,\"sorts\":null,\"topCommId\":34474,\"geoNear\":{\"lng\":121.3216781616211,\"lat\":31.0958309173584,\"distance\":null},\"totalPower\":null}";

        String url = "/dataCore/site/getSiteListFromMongo";

        MockHttpServletRequestBuilder rb = null;

        // 查询是否存在
        rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
//                .content(JsonUtils.toJsonString(SITE_PERSONALISE_DTO));
                .content(param);
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(SiteRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("getSiteListFromMongo")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andDo(print())
                .andReturn();
    }
}