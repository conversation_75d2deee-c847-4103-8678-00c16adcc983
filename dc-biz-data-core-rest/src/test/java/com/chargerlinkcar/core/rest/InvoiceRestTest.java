package com.chargerlinkcar.core.rest;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.dc.rest.InvoiceRest;
import com.cdz360.biz.model.trading.invoice.param.ListCorpInvoiceRecordParam;
import com.chargerlinkcar.core.BaseMockTest;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordAuditParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordUpdateParam;
import com.fasterxml.jackson.core.type.TypeReference;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

class InvoiceRestTest extends BaseMockTest {

    private MockMvc mockMvc;

    @Autowired
    private WebApplicationContext wac;

    @BeforeEach
    public void setUp() throws Exception {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    void findCorpInvoiceRecordList() throws Exception {
        String json = "{\"sk\":\"\",\"enable\":null,\"total\":null,\"start\":null,\"size\":10,\"sorts\":null,\"commIdChain\":\"34474\"}";
        ListCorpInvoiceRecordParam param = JsonUtils.fromJson(json, new TypeReference<ListCorpInvoiceRecordParam>(){});

        String url = "/dataCore/invoice/findCorpInvoiceRecordList";

        MockHttpServletRequestBuilder rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(param));

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(InvoiceRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("findCorpInvoiceRecordList")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
    }

    @Test
    void corpInvoiceAppendOrder() throws Exception {
//        String json = "{\"commIdChain\":\"34474\",\"corpId\":1,\"orderNoList\":[\"CZ202008071735210183\"],\"corpInvoiceInfoVo\":{\"corpId\":1,\"uid\":67949,\"invoiceWay\":\"PRE_PAY\",\"saleTin\":\"400000000000000001\",\"productTempId\":15,\"enable\":true,\"commId\":34474,\"invoiceOpen\":true,\"saleName\":\"增值税开票测试\",\"tempRefVo\":{\"id\":15,\"name\":\"新建商品模板\",\"saleTin\":\"400000000000000001\",\"target\":\"PLATFORM\",\"enable\":false,\"detailVoList\":[{\"code\":\"500b7af7ed794793b69da91d2d6d6637\",\"refId\":15,\"productCode\":\"FL12345678811\",\"productName\":\"商品行名称2\",\"productType\":\"ELEC_ACTUAL_FEE\",\"version\":\"0\",\"spec\":\"规格型号\",\"unit\":\"单位\",\"taxRate\":16,\"commercialId\":34474},{\"code\":\"f195a5b40fb646778d989d2f3f888653\",\"refId\":15,\"productCode\":\"FL12345678911\",\"productName\":\"商品行名称1\",\"productType\":\"SERV_ACTUAL_FEE\",\"version\":\"0\",\"spec\":\"规格型号\",\"unit\":\"单位\",\"taxRate\":16,\"commercialId\":34474}]},\"invoiceType\":\"PER_COMMON\",\"name\":\"别说了了了\",\"email\":\"<EMAIL>\",\"tin\":\"\",\"address\":\"\",\"tel\":\"\",\"bank\":\"\",\"bankAccount\":\"\",\"receiverName\":\"\",\"receiverMobilePhone\":\"\",\"receiverProvince\":\"\",\"receiverCity\":\"\",\"receiverArea\":\"\",\"receiverAddress\":\"\"},\"opType\":\"SYS_USER\",\"opId\":33801,\"opName\":\"***********\"}";
        String json = "{\"commIdChain\":\"34474\",\"corpId\":1,\"orderNoList\":[\"********************\"],\"corpInvoiceInfoVo\":{\"corpId\":1,\"uid\":67949,\"invoiceWay\":\"PRE_PAY\",\"saleTin\":\"***************\",\"productTempId\":112,\"enable\":true,\"commId\":34474,\"invoiceOpen\":true,\"saleName\":\"上海百旺测试0814\",\"tempRefVo\":{\"id\":112,\"name\":\"移动端模板\",\"saleTin\":\"***************\",\"target\":\"MOBILE\",\"enable\":true,\"detailVoList\":[{\"code\":\"3cbc175ade0a4ded8f6ca9565e495b77\",\"refId\":112,\"productCode\":\"1\",\"productName\":\"服务费\",\"productType\":\"SERV_ACTUAL_FEE\",\"version\":\"0\",\"spec\":\"121\",\"unit\":\"1\",\"taxRate\":10},{\"code\":\"bd863d96b5074e998c3640fefabb8fde\",\"refId\":112,\"productCode\":\"121\",\"productName\":\"电费\",\"productType\":\"ELEC_ACTUAL_FEE\",\"version\":\"0\",\"spec\":\"1\",\"unit\":\"1\",\"taxRate\":10}]},\"invoiceType\":\"PER_COMMON\",\"name\":\"发票抬头信息\",\"email\":\"<EMAIL>\",\"tin\":\"\",\"address\":\"\",\"tel\":\"\",\"bank\":\"\",\"bankAccount\":\"\",\"receiverName\":\"\",\"receiverMobilePhone\":\"\",\"receiverProvince\":\"\",\"receiverCity\":\"\",\"receiverArea\":\"\",\"receiverAddress\":\"\"},\"opType\":\"SYS_USER\",\"opId\":33857,\"opName\":\"***********\"}";
        CorpInvoiceRecordUpdateParam param = JsonUtils.fromJson(json, new TypeReference<CorpInvoiceRecordUpdateParam>(){});

        String url = "/dataCore/invoice/corpInvoiceAppendOrder";

        MockHttpServletRequestBuilder rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(param));

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(InvoiceRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("corpInvoiceAppendOrder")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
    }

    @Test
    void corpInvoiceRemoveOrder() throws Exception {
        String json = "{\"commIdChain\":\"34474\",\"corpId\":1,\"applyNo\":\"106s071936570045\",\"orderNoList\":[\"0064140929440024\"],\"corpInvoiceInfoVo\":{\"corpId\":1,\"uid\":67949,\"invoiceWay\":\"POST_SETTLEMENT\",\"saleTin\":\"400000000000000001\",\"productTempId\":15,\"enable\":true,\"commId\":34474,\"invoiceOpen\":true,\"saleName\":\"增值税开票测试\",\"tempRefVo\":{\"id\":15,\"name\":\"新建商品模板\",\"saleTin\":\"400000000000000001\",\"target\":\"PLATFORM\",\"enable\":false,\"detailVoList\":[{\"code\":\"500b7af7ed794793b69da91d2d6d6637\",\"refId\":15,\"productCode\":\"FL12345678811\",\"productName\":\"商品行名称2\",\"productType\":\"ELEC_ACTUAL_FEE\",\"version\":\"0\",\"spec\":\"规格型号\",\"unit\":\"单位\",\"taxRate\":16,\"commercialId\":34474},{\"code\":\"f195a5b40fb646778d989d2f3f888653\",\"refId\":15,\"productCode\":\"FL12345678911\",\"productName\":\"商品行名称1\",\"productType\":\"SERV_ACTUAL_FEE\",\"version\":\"0\",\"spec\":\"规格型号\",\"unit\":\"单位\",\"taxRate\":16,\"commercialId\":34474}]},\"invoiceType\":\"PER_COMMON\",\"name\":\"别说了了了\",\"email\":\"<EMAIL>\",\"tin\":\"\",\"address\":\"\",\"tel\":\"\",\"bank\":\"\",\"bankAccount\":\"\",\"receiverName\":\"\",\"receiverMobilePhone\":\"\",\"receiverProvince\":\"\",\"receiverCity\":\"\",\"receiverArea\":\"\",\"receiverAddress\":\"\"},\"opType\":\"SYS_USER\",\"opId\":33801,\"opName\":\"***********\"}";
        CorpInvoiceRecordUpdateParam param = JsonUtils.fromJson(json, new TypeReference<CorpInvoiceRecordUpdateParam>(){});

        String url = "/dataCore/invoice/corpInvoiceRemoveOrder";

        MockHttpServletRequestBuilder rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(param));

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(InvoiceRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("corpInvoiceRemoveOrder")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
    }

    @Test
    void corpInvoiceRecordDetail() throws Exception {
        String url = "/dataCore/invoice/corpInvoiceRecordDetail";
        MockHttpServletRequestBuilder rb = null;

        // 查询是否存在
        rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("applyNo", "106s071420140044");
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(InvoiceRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("corpInvoiceRecordDetail")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();
    }

    @Test
    void deleteCorpInvoiceRecordByApplyNo() throws Exception {
        String url = "/dataCore/invoice/deleteCorpInvoiceRecordByApplyNo";
        MockHttpServletRequestBuilder rb = null;

        // 查询是否存在
        rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("applyNo", "106t081404000048");
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(InvoiceRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("deleteCorpInvoiceRecordByApplyNo")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();
    }

    @Test
    void corpInvoiceRecordAudit() throws Exception {
        String json = "{\"applyNo\":\"106t081525180049\",\"auditResult\":true,\"auditRemark\":\"asdfasdfsd\",\"opType\":\"SYS_USER\",\"opId\":33801,\"opName\":\"***********\"}";
        CorpInvoiceRecordAuditParam param = JsonUtils.fromJson(json, new TypeReference<CorpInvoiceRecordAuditParam>(){});

        String url = "/dataCore/invoice/corpInvoiceRecordAudit";

        MockHttpServletRequestBuilder rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(param));

        mockMvc.perform(rb)
                .andExpect(handler().handlerType(InvoiceRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("corpInvoiceRecordAudit")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andDo(print())
                .andReturn();
    }
}