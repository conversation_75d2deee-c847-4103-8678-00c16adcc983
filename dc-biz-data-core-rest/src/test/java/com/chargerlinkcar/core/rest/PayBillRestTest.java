package com.chargerlinkcar.core.rest;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.rest.PayBillRest;
import com.cdz360.biz.model.finance.type.TaxStatus;
import com.cdz360.biz.model.finance.type.TaxType;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.model.trading.order.po.PayBillPo;
import com.chargerlinkcar.core.BaseMockTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@Slf4j
class PayBillRestTest extends BaseMockTest {

    private MockMvc mockMvc;

    @Autowired
    private WebApplicationContext wac;

    @BeforeEach
    public void setUp() throws Exception {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    void pointRecLog() throws Exception {
        String url = "/dataCore/paybill/pointRecLog";
        MockHttpServletRequestBuilder rb = null;

        // 查询是否存在
        rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("orderId", "CZ202008171855340568");
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(PayBillRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("pointRecLog")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();
    }

    @Test
    void invoiceOrderList() throws Exception {
        String url = "/dataCore/paybill/invoiceOrderList";

        MockHttpServletRequestBuilder rb = null;

        PayBillParam param = new PayBillParam();
        param.setUserId(92558L);
        param.setIndex(0);
        param.setSize(10);
        param.setInCorpInvoice(true);
        param.setApplyNo("****************");
        param.setCommIdChain("34474");
        param.setInCorpInvoice(true);
        param.setFlowTypeList(List.of(DepositFlowType.IN_FLOW));
        param.setAccountTypeList(List.of(PayAccountType.CORP)); // 查询企业客户的充值
        param.setTaxStatus(StringUtils.isBlank(param.getApplyNo()) ?
                List.of(TaxStatus.NO) : List.of(TaxStatus.NO, TaxStatus.YES)); // 未开票

        // 查询是否存在
        rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(param));
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(PayBillRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("invoiceOrderList")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();
    }

    @Test
    void updateByOrderId() throws Exception {
        String url = "/dataCore/paybill/updateByOrderId";

        MockHttpServletRequestBuilder rb = null;

        PayBillPo param = new PayBillPo();
        param.setOrderId("CZ202008181858100655")
                .setUserId(92558L)
                .setTaxType(TaxType.PREPAY_TAX)
                .setTaxStatus(TaxStatus.YES)
                .setTaxNo("23214325");

        // 查询是否存在
        rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(param));
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(PayBillRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("updateByOrderId")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();
    }

    @Test
    void checkTaxStatus() throws Exception {
        String url = "/dataCore/paybill/checkTaxStatus";

        MockHttpServletRequestBuilder rb = null;

        // 正常充值
        rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("orderId", "CZ202008201923410741");
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(PayBillRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("checkTaxStatus")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();

        // 存在开票中的充值记录
        rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("orderId", "CZ202003160900040177");
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(PayBillRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("checkTaxStatus")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();

        // 存在成功开票的充值记录
        rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("orderId", "CZ202008111420300284");
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(PayBillRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("checkTaxStatus")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();

        // 企业客户开票完成的记录
        rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("orderId", "CZ202008171312210548");
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(PayBillRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("checkTaxStatus")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();

        // 企业客户开票占用
        rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("orderId", "CZ202008181304180605");
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(PayBillRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("checkTaxStatus")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();

        // 存在企业客户开票记录
        rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("orderId", "CZ202008111409170283");
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(PayBillRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("checkTaxStatus")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();
    }
}