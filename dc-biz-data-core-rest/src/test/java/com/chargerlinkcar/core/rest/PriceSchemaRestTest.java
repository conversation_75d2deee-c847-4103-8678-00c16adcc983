package com.chargerlinkcar.core.rest;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.biz.dc.rest.PriceSchemaRest;
import com.chargerlinkcar.core.BaseMockTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@Slf4j
@WebAppConfiguration
class PriceSchemaRestTest extends BaseMockTest {

    private static final String EVSE_NO = "12345678901";

    private MockMvc mockMvc;

    @Autowired
    private WebApplicationContext wac;

    @BeforeEach
    public void setUp() throws Exception {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    void sendPriceSchema() throws Exception {
        String url = "/dataCore/priceTemp/sendPriceSchema";

        MockHttpServletRequestBuilder rb = null;

        // 查询是否存在
        rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("evseNo", EVSE_NO);
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(PriceSchemaRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("sendPriceSchema")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andDo(print())
                .andReturn();
    }
}