package com.chargerlinkcar.core.rest;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.dc.rest.CorpSettlementRest;
import com.cdz360.biz.model.order.param.ListSettlementOrderParam;
import com.chargerlinkcar.core.BaseMockTest;
import com.chargerlinkcar.framework.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@Slf4j
class CorpSettlementRestTest  extends BaseMockTest {

    private MockMvc mockMvc;

    @Autowired
    private WebApplicationContext wac;

    @BeforeEach
    public void setUp() throws Exception {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    void getNotSettlementListByCorpId() throws Exception {
        String url = "/dataCore/order/getNotSettlementOrderList";

        MockHttpServletRequestBuilder rb = null;

        ListSettlementOrderParam param = new ListSettlementOrderParam();
        param.setCorpId(1L);
        param.setTotal(true);
        param.setStart(0L);
        param.setSize(10);
//        param.setBillNoList(List.of("1234567890"));
//        param.setNotContainOrderNoList(List.of("270116581137"));

        // 查询是否存在
        rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(param));
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(CorpSettlementRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("getNotSettlementOrderList")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();
    }

    @Test
    void clearBillNo() {
    }

    @Test
    void getSettlementOrderList() throws Exception {
        String url = "/dataCore/order/getSettlementOrderList";
        MockHttpServletRequestBuilder rb = null;

        ListSettlementOrderParam param = new ListSettlementOrderParam();
        param.setBillNoList(List.of("123456"));

        // 查询是否存在
        rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(param));
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(CorpSettlementRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("getSettlementOrderList")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();
    }

    @Test
    void getNotSettlementOrderBi() throws Exception {
        String url = "/dataCore/order/settlementOrderBi";
        MockHttpServletRequestBuilder rb = null;

        ListSettlementOrderParam param = new ListSettlementOrderParam();
        param.setCorpId(1L);
        param.setStopTimeFilter(new TimeFilter()
                .setStartTime(DateUtils.toDate(LocalDateTime.of(LocalDate.now().minusMonths(1), LocalTime.MIN)))
                .setEndTime(DateUtils.toDate(LocalDateTime.of(LocalDate.now(), LocalTime.MIN))));
//        param.setBillNoList(List.of("123456"));

        // 查询是否存在
        rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(param));
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(CorpSettlementRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("settlementOrderBi")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();
    }

    @Test
    void settlementByBillNo() throws Exception {
        String url = "/dataCore/order/settlementByBillNo";
        MockHttpServletRequestBuilder rb = null;

        // 查询是否存在
        rb = get(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .param("billNo", "123456789");
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(CorpSettlementRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("settlementByBillNo")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();
    }
}