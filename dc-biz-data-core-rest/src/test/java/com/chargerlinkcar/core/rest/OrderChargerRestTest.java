package com.chargerlinkcar.core.rest;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.dc.rest.OrderChargerRest;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.chargerlinkcar.core.BaseMockTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@Slf4j
class OrderChargerRestTest extends BaseMockTest {

    private MockMvc mockMvc;

    @Autowired
    private WebApplicationContext wac;

    @BeforeEach
    public void setUp() throws Exception {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    void listChargerOrder() throws Exception {
        String url = "/dataCore/order/listChargerOrder";

        MockHttpServletRequestBuilder rb = null;

        ListChargeOrderParam param = new ListChargeOrderParam();
        param.setInCorpInvoice(true);
        param.setCommIdChain("34474");
        param.setStatusList(List.of(2000));
        param.setCorpId(1L);
        param.setStart(0L);
        param.setSize(10);

        // 查询是否存在
        rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(param));
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(OrderChargerRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("listChargerOrder")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();
    }
}