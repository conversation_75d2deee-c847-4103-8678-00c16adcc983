package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.trading.site.param.ListOvertimeParkFeeOrderParam;
import com.chargerlinkcar.core.BaseMockTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.test.context.web.WebAppConfiguration;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.web.context.WebApplicationContext;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultHandlers.print;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebAppConfiguration
class OvertimeParkFeeOrderRestTest extends BaseMockTest {

    private MockMvc mockMvc;

    @Autowired
    private WebApplicationContext wac;

    @BeforeEach
    public void setUp() throws Exception {
        this.mockMvc = MockMvcBuilders.webAppContextSetup(this.wac).build();
    }

    @Test
    void findAll() throws Exception {
        String url = "/dataCore/overtimeParkFeeOrder/findAll";

        MockHttpServletRequestBuilder rb = null;

        ListOvertimeParkFeeOrderParam param = new ListOvertimeParkFeeOrderParam();
        param.setSiteId("SITE_ID")
                .setUid(0L)
                .setTotal(true)
                .setSize(10)
                .setStart(0L);

        // 查询是否存在
        rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(param));
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(OvertimeParkFeeOrderRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("findAll")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();
    }

    @Test
    void orderBi() throws Exception {
        String url = "/dataCore/overtimeParkFeeOrder/orderBi";

        MockHttpServletRequestBuilder rb = null;

        ListOvertimeParkFeeOrderParam param = new ListOvertimeParkFeeOrderParam();
        param.setSiteId("SITE_ID")
                .setUid(0L)
                .setTotal(true)
                .setSize(10)
                .setStart(0L);

        // 查询是否存在
        rb = post(url).contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON)
                .content(JsonUtils.toJsonString(param));
        mockMvc.perform(rb)
                .andExpect(handler().handlerType(OvertimeParkFeeOrderRest.class)) //验证执行的控制器类型
                .andExpect(handler().methodName("orderBi")) //验证执行的控制器方法名
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value(DcConstants.KEY_RES_CODE_SUCCESS))
//                .andExpect(jsonPath("$.data").exists())
                .andDo(print())
                .andReturn();
    }
}