package com.cdz360.biz.dc.service.yw;

import com.cdz360.biz.ds.trading.ro.yw.ds.YwOrderRoDs;
import com.chargerlinkcar.core.BaseMockTest;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class YwOrderServiceTest extends BaseMockTest {

    @Autowired
    private YwOrderRoDs ywOrderRoDs;
    @Autowired
    private YwOrderService ywOrderService;

    @Test
    void calcYwDuration() {
        String ywOrderNo = "Y110216104317100";
        ywOrderRoDs.getByYwOrderNoList(List.of(ywOrderNo))
            .forEach(po -> {
                ywOrderService.calcYwDuration(po.getYwOrderNo(), po.getCreateTime(), po.getMaintTime());
            });
    }
}