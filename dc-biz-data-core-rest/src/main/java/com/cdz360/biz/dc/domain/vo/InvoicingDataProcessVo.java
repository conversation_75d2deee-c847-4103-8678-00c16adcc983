package com.cdz360.biz.dc.domain.vo;

import com.cdz360.biz.model.trading.order.po.ChargerOrderPayPo;
import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class InvoicingDataProcessVo {

    /**
     * append为true时使用 <充电单，原始订单开票数据>
     */
    private Optional<Map<String, ChargerOrderPayPo>> orderPayMapOpt;

    /**
     * append为false时使用 <充电单，可开票金额>
     */
    private Map<String, BigDecimal> corpOrderInvoiceMap;

    /**
     * append为false时使用 <充值单，可开票金额>
     */
    private Map<String, BigDecimal> czInvoiceMapMapRef;

    /**
     * append为false时使用 <充电单号，<关联充值单号，充值单开票金额>>
     */
    private Map<String, Map<String, BigDecimal>> orderInvoiceMapRef2;

    /**
     * 是否为企业开票申请 企业的开票关联表：t_invoice_record_order_ref 非企业的开票关联表：t_invoiced_record_bill_ref
     */
    private Boolean isCorpInvoicingApplyRef;

}
