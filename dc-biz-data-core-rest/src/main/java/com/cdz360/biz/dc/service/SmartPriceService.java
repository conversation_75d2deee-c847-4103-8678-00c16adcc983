package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.dc.client.AntFeignClient;
import com.cdz360.biz.dc.client.UserFeignClient;
import com.cdz360.biz.dc.client.reactor.OaFeignClient;
import com.cdz360.biz.dc.service.site.SiteBizService;
import com.cdz360.biz.ds.trading.ro.site.ds.PriceItemRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.PriceSchemaRoDs;
import com.cdz360.biz.ds.trading.rw.site.ds.PriceSchemaRwDs;
import com.cdz360.biz.model.iot.vo.ChargeV2;
import com.cdz360.biz.model.oa.constant.OaConstants;
import com.cdz360.biz.model.site.po.PriceItemPo;
import com.cdz360.biz.model.site.po.PriceTemplatePo;
import com.cdz360.biz.model.site.type.PriceTemplateCalcType;
import com.cdz360.biz.model.site.type.SitePriceSmartStrategyType;
import com.cdz360.biz.model.trading.coupon.vo.UserInfoVo;
import com.cdz360.biz.model.trading.site.param.AddPriceSchemaParam;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import com.cdz360.biz.oa.dto.SmartStrategyElecPrice;
import com.cdz360.biz.oa.dto.TargetPriceSchemeInfo;
import com.chargerlinkcar.framework.common.utils.AssertUtil;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.SerializationUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SmartPriceService {

    @Autowired
    private AntFeignClient antFeignClient;

    @Autowired
    private PriceSchemaBizService priceSchemaBizService;

    @Autowired
    private OaFeignClient oaFeignClient;

    @Autowired
    private PriceSchemaRoDs priceSchemaRoDs;

    @Autowired
    private PriceSchemaRwDs priceSchemaRwDs;

    @Autowired
    private PriceItemRoDs priceItemRoDs;

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private SiteBizService siteBizService;

    // 仅防止扩容出错
    private ConcurrentHashMap<String, Integer> selfAddMap = new ConcurrentHashMap<>();

    /**
     * @param params data数据
     */
    public void generateSmartPrice(Map<String, Object> params, String procInstId,
        UserInfoVo userInfoVo) throws Exception {
        String target = (String) params.get("target");
        // 0.根据以前的计费模板进行分组
        // 分组失败不应影响智能调价(首轮普通，次轮智能的情况)
        this.groupByTemplate(params, procInstId);

        try {
            // 1.根据智能计费动态修改 params
            if ("SITE".equals(target)) {
                siteSmartPrice(params, procInstId, userInfoVo);
            } else if ("SITE_PART".equals(target)) {
                sitePartSmartPrice(params, procInstId, userInfoVo);
            } else if ("EVSE".equals(target)) {
                evseSmartPrice(params, procInstId, userInfoVo);
            } else {
                throw new DcServiceException("不支持的操作对象: " + target);
            }
            // 2.回写新的计算结果到数据库
            oaFeignClient.updateNameByParams(params, procInstId, OaConstants.PI_VARIABLE_PARAM)
                .subscribe();
        } catch (Exception e) {
            // 更新错误信息
            params.put("smartPriceFail", e.getMessage());
            oaFeignClient.updateNameByParams(params, procInstId, OaConstants.PI_VARIABLE_PARAM)
                .block();
            throw new Exception(e);
        } finally {
            // 3.清理数据
            cleanSelfAddMap(procInstId);
        }
    }


    private void groupByTemplate(Map<String, Object> params, String procInstId) {
        Map<String, List<TargetPriceSchemeInfo>> group = new HashMap<>();
        String target = (String) params.get("target");
        List<String> siteIdList = ((ArrayList<Object>) params.get("siteIdList"))
            .stream().map(String::valueOf).toList();
        if ("EVSE".equals(target)) {
            siteIdList = ((ArrayList<Object>) params.get("evseNoList"))
                .stream().map(String::valueOf).toList();
        }
        List<List<TargetPriceSchemeInfo>> data = null;
        ArrayList<Object> targetInfoList = (ArrayList<Object>) params.get("targetInfoList");
        ListSiteParam getSiteVoListParam = new ListSiteParam();
        getSiteVoListParam.setTotal(true);
        // 没有target，全部都没有原模板，分到一组
        if (CollectionUtils.isEmpty(targetInfoList)) {
            params.put("originTarget", "empty");
            getSiteVoListParam.setSiteIdList(siteIdList);
            List<SiteVo> siteVos = siteBizService.getSiteVoList(getSiteVoListParam).getData();
            List<TargetPriceSchemeInfo> emptyTarget = new ArrayList<>();
            siteVos.forEach(item -> {
                TargetPriceSchemeInfo targetPriceSchemeInfo = new TargetPriceSchemeInfo();
                targetPriceSchemeInfo.setName(item.getSiteName());
                targetPriceSchemeInfo.setNo(item.getId());
                emptyTarget.add(targetPriceSchemeInfo);
            });
            params.put("emptyTarget", emptyTarget);
            return;
        }
        List<TargetPriceSchemeInfo> priceScheme = targetInfoList
            .stream().map(item -> JsonUtils.fromJson(JsonUtils.toJsonString(item),
                TargetPriceSchemeInfo.class)).toList();
        Set<String> targetSet = priceScheme.stream().map(TargetPriceSchemeInfo::getNo)
            .collect(Collectors.toSet());
        // 剔除没有原模板的类型，单独分类
        List<String> emptyTargetId = siteIdList.stream().filter(item -> !targetSet.contains(item))
            .toList();
        // 非空就添加
        if (!emptyTargetId.isEmpty()) {
            List<TargetPriceSchemeInfo> emptyTarget = new ArrayList<>();
            getSiteVoListParam.setSiteIdList(emptyTargetId);
            List<SiteVo> siteVos = siteBizService.getSiteVoList(getSiteVoListParam).getData();
            siteVos.forEach(item -> {
                TargetPriceSchemeInfo targetPriceSchemeInfo = new TargetPriceSchemeInfo();
                targetPriceSchemeInfo.setName(item.getSiteName());
                targetPriceSchemeInfo.setNo(item.getId());
                emptyTarget.add(targetPriceSchemeInfo);
            });
            params.put("emptyTarget", emptyTarget);
        }
        siteIdList = siteIdList.stream().filter(item -> targetSet.contains(item)).toList();
        int equal = 0;
        if (siteIdList.size() == priceScheme.size() || "EVSE".equals(target)) {
            // 如果操作对象是 个别充电桩，要么是直流桩，要么是交流桩，也只有一个模板
            equal = 1;
            params.put("originTarget", "same");
        } else if (siteIdList.size() * 2 == priceScheme.size()) {
            equal = 2;
            params.put("originTarget", "part");
        } else {
            String errLog = "同时存在交直流模板和普通模板，无法进行智能分组";
            params.put("groupByTemplateFail", errLog);
            log.warn(errLog);
            return;
        }

        // 不知道原场站计费是哪种，在次进行判断分组
        if (equal == 1) {
            for (TargetPriceSchemeInfo info : priceScheme) {
                PriceTemplatePo priceTemplatePo = priceSchemaBizService.getPriceSchema(
                    info.getPriceSchemeId(), true).get();
                String key = "";
                for (ChargeV2 chargeV2 : priceTemplatePo.getPriceItemList()) {
                    key += chargeV2.getCategory().getCode() + chargeV2.getStartTime()
                        + chargeV2.getStopTime()
                        + chargeV2.getElecPrice().toPlainString() + chargeV2.getServPrice()
                        .toPlainString();
                }
                List<TargetPriceSchemeInfo> got = group.getOrDefault(key, new ArrayList<>());
                got.add(info);
                group.put(key, got);
            }
            data = new ArrayList<>(group.values());
        } else if (equal == 2) {
            List<TargetPriceSchemeInfo> acScheme = priceScheme.stream()
                .filter(item -> item.getTemplateType().equals(SupplyType.AC))
                .collect(Collectors.toList());
            Map<String, TargetPriceSchemeInfo> dcSchema = priceScheme.stream()
                .filter(item -> item.getTemplateType().equals(SupplyType.DC))
                .collect(
                    Collectors.toMap(TargetPriceSchemeInfo::getNo, self -> self, (v1, v2) -> v1));
            for (TargetPriceSchemeInfo info : acScheme) {
                // 先加AC
                PriceTemplatePo priceTemplatePo = priceSchemaBizService.getPriceSchema(
                    info.getPriceSchemeId(), true).get();
                String key = "";
                for (ChargeV2 chargeV2 : priceTemplatePo.getPriceItemList()) {
                    key += chargeV2.getCategory().getCode() + chargeV2.getStartTime()
                        + chargeV2.getStopTime()
                        + chargeV2.getElecPrice().toPlainString() + chargeV2.getServPrice()
                        .toPlainString();
                }
                // 再加DC
                priceTemplatePo = priceSchemaBizService.getPriceSchema(
                    dcSchema.get(info.getNo()).getPriceSchemeId(), true).get();
                for (ChargeV2 chargeV2 : priceTemplatePo.getPriceItemList()) {
                    key += chargeV2.getCategory().getCode() + chargeV2.getStartTime()
                        + chargeV2.getStopTime()
                        + chargeV2.getElecPrice().toPlainString() + chargeV2.getServPrice()
                        .toPlainString();
                }
                List<TargetPriceSchemeInfo> got = group.getOrDefault(key, new ArrayList<>());
                // 添加AC的样例足以
                got.add(info);
                group.put(key, got);
            }
            data = new ArrayList<>(group.values());
        }

        // block 掉防止更新事务冲突
        oaFeignClient.updateNameByParams(data, procInstId,
            OaConstants.PI_VARIABLE_GROUP_PRICE_SCHEMA).block();
    }


    /**
     * 个别充电桩
     * 除了校验，其他代码基本一样
     *
     * @param params
     */
    private void evseSmartPrice(Map<String, Object> params, String procInstId,
        UserInfoVo userInfoVo) {
        PriceTemplatePo normalSchema = null;
        boolean smartPrice = (boolean) params.get("smartPrice");
        final Map<String, PriceTemplatePo> firstPriceSchema = new HashMap<>();
        List<TargetPriceSchemeInfo> priceScheme = ((ArrayList<Object>) params.get("targetInfoList"))
            .stream().map(item -> JsonUtils.fromJson(JsonUtils.toJsonString(item),
                TargetPriceSchemeInfo.class)).toList();

        // 1.计算第一个智能调价
        if (smartPrice) {
            firstSmartPriceCal(priceScheme, params, firstPriceSchema, userInfoVo, procInstId);
        } else {
            // 获取计费模板, 只有一个
            long priceSchemeId = Long.valueOf(String.valueOf(params.get("priceSchemeId")));
            normalSchema = priceSchemaBizService.getPriceSchema(
                priceSchemeId, true).get();
        }

        // 2.计算第二个智能调价
        boolean smartPrice2 = (boolean) params.get("smartPrice2");
        if (smartPrice2) {
            List<TargetPriceSchemeInfo> secondPriceScheme = priceScheme.stream()
                .map(item -> SerializationUtils.clone(item)).toList();
            // 2.1原来是智能调价，即有多个模板的情况
            if (smartPrice) {
                secondSmartPriceCal(secondPriceScheme, params, firstPriceSchema, userInfoVo,
                    procInstId);
                // 2.2 回写数据
                params.put("smartInfoList2", secondPriceScheme);
            } else {
                // 原来是普通计费，只有一个模板
                TargetPriceSchemeInfo normal = new TargetPriceSchemeInfo();
                secondNormalPriceCal(normalSchema, normal, params, null, userInfoVo, procInstId);
                // 2.2 回写数据
                params.put("smartInfo2", normal);
            }
        }
    }

    /**
     * 整站交直流分开收费
     * 除了校验和AC DC2个模板，其他代码基本一样
     *
     * @param params
     */
    private void sitePartSmartPrice(Map<String, Object> params, String procInstId,
        UserInfoVo userInfoVo) {
        // 1.计算第一个智能调价
        boolean smartPrice = (boolean) params.get("smartPrice");
        final Map<String, PriceTemplatePo> firstPriceSchema = new HashMap<>();
        PriceTemplatePo normalACSchema = null;
        PriceTemplatePo normalDCSchema = null;

        // 1.1根据场站获取老的计费模板ID列表，先从前端提交信息中获取计费模板ID
        List<String> siteIdList = (ArrayList<String>) params.get("siteIdList");

        List<TargetPriceSchemeInfo> priceScheme = ((ArrayList<Object>) params.get("targetInfoList"))
            .stream().map(item -> JsonUtils.fromJson(JsonUtils.toJsonString(item),
                TargetPriceSchemeInfo.class)).toList();

        if (smartPrice) {
            firstSmartPriceCal(priceScheme, params, firstPriceSchema, userInfoVo, procInstId);
        } else {
            // 场站交直流计费获取计费模板, 获取2个
            long acPriceSchemeId = Long.valueOf(String.valueOf(params.get("acPriceSchemeId")));
            long dcPriceSchemeId = Long.valueOf(String.valueOf(params.get("dcPriceSchemeId")));
            normalACSchema = priceSchemaBizService.getPriceSchema(
                acPriceSchemeId, true).get();
            normalDCSchema = priceSchemaBizService.getPriceSchema(
                dcPriceSchemeId, true).get();
        }

        // 2.计算第二个智能调价
        boolean smartPrice2 = (boolean) params.get("smartPrice2");
        if (smartPrice2) {
            List<TargetPriceSchemeInfo> secondPriceScheme = priceScheme.stream()
                .map(item -> SerializationUtils.clone(item)).toList();
            // 2.1原来是智能调价，即有多个模板的情况
            if (smartPrice) {
                secondSmartPriceCal(secondPriceScheme, params, firstPriceSchema, userInfoVo,
                    procInstId);
                // 2.2 回写数据
                params.put("smartInfoList2", secondPriceScheme);
            } else {
                // 原来是场站交直流计费，有2个模板
                // AC计算
                TargetPriceSchemeInfo ac = new TargetPriceSchemeInfo();
                secondNormalPriceCal(normalACSchema, ac, params, SupplyType.AC, userInfoVo,
                    procInstId);
                params.put("smartInfo2AC", ac);

                // DC计算
                TargetPriceSchemeInfo dc = new TargetPriceSchemeInfo();
                secondNormalPriceCal(normalDCSchema, dc, params, SupplyType.DC, userInfoVo,
                    procInstId);
                // 2.2 回写数据
                params.put("smartInfo2DC", dc);
            }
        }
    }

    /**
     * 整站普通收费
     *
     * @param params
     */
    private void siteSmartPrice(Map<String, Object> params, String procInstId,
        UserInfoVo userInfoVo) {
        // 1.计算第一个智能调价
        boolean smartPrice = (boolean) params.get("smartPrice");
        Map<String, PriceTemplatePo> firstPriceSchema = new HashMap<>();
        PriceTemplatePo normalSchema = null;

        List<TargetPriceSchemeInfo> priceScheme = ((ArrayList<Object>) params.get("targetInfoList"))
            .stream().map(item -> JsonUtils.fromJson(JsonUtils.toJsonString(item),
                TargetPriceSchemeInfo.class)).toList();

        if (smartPrice) {
            firstSmartPriceCal(priceScheme, params, firstPriceSchema, userInfoVo, procInstId);
        } else {
            // 普通计费获取计费模板, 只有一个
            long priceSchemeId = Long.valueOf(String.valueOf(params.get("priceSchemeId")));
            normalSchema = priceSchemaBizService.getPriceSchema(
                priceSchemeId, true).get();
        }

        // 2.计算第二个智能调价
        boolean smartPrice2 = (boolean) params.get("smartPrice2");
        if (smartPrice2) {
            List<TargetPriceSchemeInfo> secondPriceScheme = priceScheme.stream()
                .map(item -> SerializationUtils.clone(item)).toList();
            // 2.1原来是智能调价，即有多个模板的情况
            if (smartPrice) {
                secondSmartPriceCal(secondPriceScheme, params, firstPriceSchema, userInfoVo,
                    procInstId);
                // 2.2 回写数据
                params.put("smartInfoList2", secondPriceScheme);
            } else {
                // 原来是普通计费，只有一个模板
                TargetPriceSchemeInfo normal = new TargetPriceSchemeInfo();
                secondNormalPriceCal(normalSchema, normal, params, null, userInfoVo, procInstId);
                // 2.2 回写数据
                params.put("smartInfo2", normal);
            }
        }
    }

    /**
     * 计算第一个智能收费，整站和交直流可复用
     *
     * @param firstPriceScheme 原始计费模板
     * @param params 原始参数
     * @param firstPriceSchema 计算结果
     */
    private void firstSmartPriceCal(List<TargetPriceSchemeInfo> firstPriceScheme,
        Map<String, Object> params,
        Map<String, PriceTemplatePo> firstPriceSchema, UserInfoVo userInfoVo, String procInstId) {
        // 1.2根据原有的计费模板id，一一创建新的计费模板对应
        firstPriceScheme.forEach((item) -> {
            String siteId = item.getNo();

            // 获取老的计费模板
            Optional<PriceTemplatePo> priceSchemaOption = priceSchemaBizService.getPriceSchema(
                item.getPriceSchemeId(), true);
            // 上面已经校验了，此处可以不校验，但上面为前端提交数据，非亲自去数据库查看
            AssertUtil.isFalse(priceSchemaOption.isEmpty(), "该场站没有原计费模板: " + siteId);

            // 要修改这个对象，防止mybatis缓存影响
            PriceTemplatePo priceSchema = SerializationUtils.clone(priceSchemaOption.get());

            PriceTemplateCalcType calculateType = priceSchema.getCalculateType();
            // 智能策略类型，1.singlePrice 单一电价，2.fixServFee 固定服务费
            SitePriceSmartStrategyType smartStrategy = SitePriceSmartStrategyType.getSmartStrategyByCode(
                params.get("smartStrategy"));
            IotAssert.isNotNull(smartStrategy, "智能策略模式下，智能策略类型不能为空");

            BigDecimal elecPrice = BigDecimal.ZERO;
            if (params.get("elecPrice") != null && StringUtils.isNotBlank(
                String.valueOf(params.get("elecPrice")))) {
                elecPrice = new BigDecimal(String.valueOf(params.get("elecPrice")));
            }
            SmartStrategyElecPrice timeBasedElecPrice = JsonUtils.fromJson(
                String.valueOf(params.get("timeBasedElecPrice")), SmartStrategyElecPrice.class);

            if (calculateType != null && calculateType.equals(PriceTemplateCalcType.UNIFY_PRICE)) {
                // 统一计费，不能填分时电价，只能填电价
                IotAssert.isNotNull(elecPrice, "电价不能为空");
            }

            // 分时电价相关的智能策略，必须填分时电价
            if (smartStrategy.equals(SitePriceSmartStrategyType.FIX_SERV_FEE_TIME_BASED_PRICE)
                || smartStrategy.equals(
                SitePriceSmartStrategyType.FIX_TOTAL_FEE_TIME_BASED_PRICE)
                || smartStrategy.equals(
                SitePriceSmartStrategyType.FIX_ELEC_FEE_TIME_BASED_PRICE)) {
                IotAssert.isTrue(timeBasedElecPrice != null &&
                    timeBasedElecPrice.getSharpPeakElecPrice() != null &&
                    timeBasedElecPrice.getPeakElecPrice() != null &&
                    timeBasedElecPrice.getOffPeakElecPrice() != null &&
                    timeBasedElecPrice.getValleyElecPrice() != null, "新的分时电价不能为空");
            }

            // 给计费模版起名字用
            List<BigDecimal> elecPriceList = new ArrayList<>();

            // 计算新的价格
            switch (calculateType) {
                case UNIFY_PRICE ->
                    calUnifyPrice(priceSchema, smartStrategy, elecPrice, elecPriceList);
                case TIME_BASE_PRICE ->
                    calTimeBasePrice(priceSchema, smartStrategy, elecPrice, timeBasedElecPrice,
                        elecPriceList);
                case TIME_BASE_PRICE_2 ->
                    calTimeBasePriceWithServiceFee(priceSchema, smartStrategy, elecPrice,
                        timeBasedElecPrice, elecPriceList);
                default -> throw new DcServiceException("模板计费类型不存在");
            }
            // 创建新的计费模板或者复用原来的
            long newPriceSchemeId = createNewPriceSchema(priceSchema, userInfoVo, procInstId,
                smartStrategy, elecPriceList);
            SupplyType templateType = item.getTemplateType();
            String templateTypeString = "";
            if (templateType != null) {
                templateTypeString = templateType.toString();
            }
            firstPriceSchema.put(siteId + templateTypeString, priceSchema);

            // 记录数据
            item.setPriceSchemeId(newPriceSchemeId);
        });
        // 1.4回写数据
        params.put("smartInfoList", firstPriceScheme);
    }


    /**
     * 第一次和第二次都是智能调价
     * 计算第二个智能收费，整站和交直流可复用
     *
     * @param secondPriceScheme 原始计费模板
     * @param params 原始参数
     * @param firstPriceSchema 第一个计费结果
     */
    private void secondSmartPriceCal(List<TargetPriceSchemeInfo> secondPriceScheme,
        Map<String, Object> params,
        Map<String, PriceTemplatePo> firstPriceSchema, UserInfoVo userInfoVo, String procInstId) {
        secondPriceScheme.forEach(item -> {
            SupplyType templateType = item.getTemplateType();
            String templateTypeString = "";
            if (templateType != null) {
                templateTypeString = templateType.toString();
            }
            PriceTemplatePo priceSchema = firstPriceSchema.get(item.getNo() + templateTypeString);
            PriceTemplateCalcType calculateType = priceSchema.getCalculateType();
            // 智能策略类型，1.singlePrice 单一电价，2.fixServFee 固定服务费
            SitePriceSmartStrategyType smartStrategy = SitePriceSmartStrategyType.getSmartStrategyByCode(
                params.get("smartStrategy2"));
            IotAssert.isNotNull(smartStrategy, "智能策略模式下，智能策略类型不能为空");

            BigDecimal elecPrice = BigDecimal.ZERO;
            if (params.get("elecPrice2") != null && StringUtils.isNotBlank(
                String.valueOf(params.get("elecPrice2")))) {
                elecPrice = new BigDecimal(String.valueOf(params.get("elecPrice2")));
            }
            SmartStrategyElecPrice timeBasedElecPrice = JsonUtils.fromJson(
                String.valueOf(params.get("timeBasedElecPrice2")), SmartStrategyElecPrice.class);

            if (calculateType != null && calculateType.equals(PriceTemplateCalcType.UNIFY_PRICE)) {
                // 统一计费，不能填分时电价，只能填电价
                IotAssert.isNotNull(elecPrice, "电价不能为空");
            }

            // 分时电价相关的智能策略，必须填分时电价
            if (smartStrategy.equals(SitePriceSmartStrategyType.FIX_SERV_FEE_TIME_BASED_PRICE)
                || smartStrategy.equals(
                SitePriceSmartStrategyType.FIX_TOTAL_FEE_TIME_BASED_PRICE)
                || smartStrategy.equals(
                SitePriceSmartStrategyType.FIX_ELEC_FEE_TIME_BASED_PRICE)) {
                IotAssert.isTrue(timeBasedElecPrice != null &&
                    timeBasedElecPrice.getSharpPeakElecPrice() != null &&
                    timeBasedElecPrice.getPeakElecPrice() != null &&
                    timeBasedElecPrice.getOffPeakElecPrice() != null &&
                    timeBasedElecPrice.getValleyElecPrice() != null, "新的分时电价不能为空");
            }

            // 给计费模版起名字用
            List<BigDecimal> elecPriceList = new ArrayList<>();

            // 计算新的价格
            switch (calculateType) {
                case UNIFY_PRICE ->
                    calUnifyPrice(priceSchema, smartStrategy, elecPrice, elecPriceList);
                case TIME_BASE_PRICE ->
                    calTimeBasePrice(priceSchema, smartStrategy, elecPrice, timeBasedElecPrice,
                        elecPriceList);
                case TIME_BASE_PRICE_2 ->
                    calTimeBasePriceWithServiceFee(priceSchema, smartStrategy, elecPrice,
                        timeBasedElecPrice, elecPriceList);
                default -> throw new DcServiceException("模板计费类型不存在");
            }
            // 创建新的计费模板或者复用原来的
            long newPriceSchemeId = createNewPriceSchema(priceSchema, userInfoVo, procInstId,
                smartStrategy, elecPriceList);
            // 记录数据
            item.setPriceSchemeId(newPriceSchemeId);
        });
    }


    /**
     * 第一次普通操作, 第二次是智能调价
     * 计算第二次价格模板, 普通和场站交直流可以复用
     *
     * @param normalSchema 原计费模板
     * @param priceScheme 总共需要动态计算的
     * @param params 原始参数
     * @param supplyType 计费类型
     */
    private void secondNormalPriceCal(PriceTemplatePo normalSchema,
        TargetPriceSchemeInfo priceScheme,
        Map<String, Object> params, SupplyType supplyType, UserInfoVo userInfoVo,
        String procInstId) {
        PriceTemplateCalcType calculateType = normalSchema.getCalculateType();

        // 智能策略类型，1.singlePrice 单一电价，2.fixServFee 固定服务费
        SitePriceSmartStrategyType smartStrategy = SitePriceSmartStrategyType.getSmartStrategyByCode(
            params.get("smartStrategy2"));
        IotAssert.isNotNull(smartStrategy, "智能策略模式下，智能策略类型不能为空");

        BigDecimal elecPrice2 = BigDecimal.ZERO;
        if (params.get("elecPrice2") != null && StringUtils.isNotBlank(
            String.valueOf(params.get("elecPrice2")))) {
            elecPrice2 = new BigDecimal(String.valueOf(params.get("elecPrice2")));
        }
        SmartStrategyElecPrice timeBasedElecPrice2 = JsonUtils.fromJson(
            String.valueOf(params.get("timeBasedElecPrice2")), SmartStrategyElecPrice.class);

        if (calculateType != null && calculateType.equals(PriceTemplateCalcType.UNIFY_PRICE)) {
            // 统一计费，不能填分时电价
            IotAssert.isTrue(timeBasedElecPrice2 != null &&
                timeBasedElecPrice2.getSharpPeakElecPrice() != null &&
                timeBasedElecPrice2.getPeakElecPrice() != null &&
                timeBasedElecPrice2.getOffPeakElecPrice() != null &&
                timeBasedElecPrice2.getValleyElecPrice() != null, "新的分时电价不能为空");
        }

        // 分时电价相关的智能策略，必须填分时电价
        if (smartStrategy.equals(SitePriceSmartStrategyType.FIX_SERV_FEE_TIME_BASED_PRICE)
            || smartStrategy.equals(
            SitePriceSmartStrategyType.FIX_TOTAL_FEE_TIME_BASED_PRICE)
            || smartStrategy.equals(
            SitePriceSmartStrategyType.FIX_ELEC_FEE_TIME_BASED_PRICE)) {
            IotAssert.isTrue(timeBasedElecPrice2 != null &&
                timeBasedElecPrice2.getSharpPeakElecPrice() != null &&
                timeBasedElecPrice2.getPeakElecPrice() != null &&
                timeBasedElecPrice2.getOffPeakElecPrice() != null &&
                timeBasedElecPrice2.getValleyElecPrice() != null, "新的分时电价不能为空");
        }

        // 给计费模版起名字用
        List<BigDecimal> elecPriceList = new ArrayList<>();

        // 计算新的价格
        switch (calculateType) {
            case UNIFY_PRICE ->
                calUnifyPrice(normalSchema, smartStrategy, elecPrice2, elecPriceList);
            case TIME_BASE_PRICE ->
                calTimeBasePrice(normalSchema, smartStrategy, elecPrice2, timeBasedElecPrice2,
                    elecPriceList);
            case TIME_BASE_PRICE_2 ->
                calTimeBasePriceWithServiceFee(normalSchema, smartStrategy, elecPrice2,
                    timeBasedElecPrice2, elecPriceList);
            default -> throw new DcServiceException("模板计费类型不存在");
        }
        // 创建新的计费模板
        long newPriceSchemeId = createNewPriceSchema(normalSchema, userInfoVo, procInstId,
            smartStrategy, elecPriceList);

        // 记录数据
        priceScheme.setPriceSchemeId(newPriceSchemeId);
        priceScheme.setTemplateType(supplyType);
    }

    /**
     * 创建计费模板
     *
     * @param firstPriceSchema
     * @return 模板id
     */
    private long createNewPriceSchema(PriceTemplatePo firstPriceSchema, UserInfoVo userInfoVo,
        String procInstId,
        SitePriceSmartStrategyType smartStrategy,
        List<BigDecimal> elecPriceList) {
        // 1.查询数据库的计费模板
        List<PriceTemplatePo> allByEnableAndNotDelete = priceSchemaRoDs.findAllByEnableAndNotDelete();
        List<Long> idList = allByEnableAndNotDelete.stream().map(PriceTemplatePo::getId)
            .collect(Collectors.toList());

        List<PriceItemPo> priceItemList = priceItemRoDs.getPriceItemList(idList);
        Map<Long, List<PriceItemPo>> id2TempItem = priceItemList.stream()
            .collect(Collectors.groupingBy(PriceItemPo::getTemplateId, Collectors.toList()));

        // 2.比较模板是否相同
        List<ChargeV2> firstItemList = firstPriceSchema.getPriceItemList();
        List<PriceItemPo> newItems = firstItemList.stream()
            .map(o -> priceSchemaRwDs.toPriceSchemaItem(firstPriceSchema, o))
            .sorted(Comparator.comparingInt(PriceItemPo::getStartTime))
            .collect(Collectors.toList());
        int size = newItems.size();

        for (List<PriceItemPo> value : id2TempItem.values()) {
            if (value.size() == size) {
                if (comparePriceItem(newItems, value)) {
                    log.info("复用原有计费模板: {}", value.get(0).getTemplateId());
                    return value.get(0).getTemplateId();
                }
            }
        }
        // 3.没有模板，进行新增操作
        log.info("没有模板可以复用，进行新增操作");
        // 设置计费模板信息
        AddPriceSchemaParam param = new AddPriceSchemaParam();
        param.setCalculateType(firstPriceSchema.getCalculateType());
        param.setFreeChargeFlag(firstPriceSchema.getFreeChargeFlag());
        param.setPriceItemList(firstPriceSchema.getPriceItemList());
        param.setUsage(firstPriceSchema.getUsage());

        // 填充用户user信息
        param.setCommId(userInfoVo.getCommId())
            .setTopCommId(userInfoVo.getTopCommId())
            .setCommIdChain(userInfoVo.getCommIdChain())
            .setCreatorUserId(userInfoVo.getUserId())
            .setCreatorName(userInfoVo.getUserName())
            .setCreatorPhone(userInfoVo.getUserPhone());

        // 设置模板名称
        param.setName(generateNewPriceTemplateName(procInstId, smartStrategy, elecPriceList));

        PriceTemplatePo priceTemplatePo = priceSchemaBizService.addPriceSchema(param);
        return priceTemplatePo.getId();
    }

    private String generateNewPriceTemplateName(String procInstId,
        SitePriceSmartStrategyType smartStrategy,
        List<BigDecimal> elecPriceList) {
        String key = getSelfAddMapKey(procInstId, smartStrategy, elecPriceList);
        Integer value = selfAddMap.getOrDefault(key, 1);
        // unsafe, 如要并发请换成 AtomicInteger
        selfAddMap.put(key, value + 1);
        return key + "_" + String.format("%04d", value);
    }

    private String getSelfAddMapKey(String procInstId, SitePriceSmartStrategyType smartStrategy,
        List<BigDecimal> elecPriceList) {
        int type = 0;
        switch (smartStrategy) {
            case FIX_TOTAL_FEE -> type = 1;
            case FIX_SERV_FEE -> type = 2;
            case FIX_ELEC_FEE -> type = 3;
            case FIX_TOTAL_FEE_TIME_BASED_PRICE -> type = 4;
            case FIX_SERV_FEE_TIME_BASED_PRICE -> type = 5;
            case FIX_ELEC_FEE_TIME_BASED_PRICE -> type = 6;
        }
        String elecPriceStr = "";
        if (CollectionUtils.isNotEmpty(elecPriceList)) {
            elecPriceStr = elecPriceList.stream().map(Objects::toString)
                .collect(Collectors.joining("_"));
        }
        return procInstId.substring(0, 8).toUpperCase(Locale.ROOT) + "_" + type + "_" + elecPriceStr;
    }

    // unsafe
    private void cleanSelfAddMap(String procInstId) {
        log.info("清理key: " + procInstId);
        selfAddMap.keySet().removeIf(key -> key.startsWith(procInstId));
    }

    /**
     * 比较2个PriceItemPo是否相等
     *
     * @param var1 PriceItemPo
     * @param var2 PriceItemPo
     * @return 相等返回true，不相等返回false
     */
    private Boolean comparePriceItem(List<PriceItemPo> var1, List<PriceItemPo> var2) {
        int size = var1.size();
        for (int i = 0; i < size; i++) {
            PriceItemPo item1 = var1.get(i);
            PriceItemPo item2 = var2.get(i);
            // 时间段和2个价格都相等，就继续
            if (item1.getStartTime().equals(item2.getStartTime())
                && item1.getStopTime().equals(item2.getStopTime())
                && item1.getPrice().equals(item2.getPrice())
                && item1.getServicePrice().equals(item2.getServicePrice())
                && item1.getCategory().equals(item2.getCategory())) {
                continue;
            }
            // 不相等返回false
            return false;
        }
        return true;
    }

    // 计算普通收费
    private void calUnifyPrice(PriceTemplatePo priceSchema,
        SitePriceSmartStrategyType smartStrategy, BigDecimal elecPrice,
        List<BigDecimal> elecPriceList) {
        // elecPrice即可能是电价总价，也可能是电价单价，具体看智能策略的值
        elecPrice = elecPrice.setScale(4, RoundingMode.HALF_UP);
        ChargeV2 item1 = priceSchema.getPriceItemList().get(0);
        BigDecimal priceSum = item1.getElecPrice().add(item1.getServPrice())
            .setScale(4, RoundingMode.HALF_UP);

        switch (smartStrategy) {
            // 普通收费不涉及到另外两种带峰平谷的分时计费的模版
            case FIX_TOTAL_FEE -> {
                // 单一电价
                // 新价格比总价大，设置新电价为电价，服务费为0，总价为新电价
                if (elecPrice.compareTo(priceSum) > 0) {
                    item1.setElecPrice(elecPrice);
                    item1.setServPrice(BigDecimal.ZERO);
                } else {
                    // 电费单价是新电价，服务费单价是旧电价减去新电价
                    item1.setElecPrice(elecPrice);
                    BigDecimal newServicePrice = priceSum.subtract(elecPrice)
                        .setScale(4, RoundingMode.HALF_UP);
                    item1.setServPrice(newServicePrice);
                }
            }
            case FIX_SERV_FEE -> {
                // 固定服务费
                item1.setElecPrice(elecPrice);
                item1.setServPrice(item1.getServPrice());
            }
            case FIX_ELEC_FEE -> {
                // 固定电费
                item1.setElecPrice(item1.getElecPrice());
                item1.setServPrice(elecPrice);
            }

        }

        elecPriceList.add(elecPrice);
    }

    // 计算分时收费
    private void calTimeBasePrice(PriceTemplatePo priceSchema,
        SitePriceSmartStrategyType smartStrategy, BigDecimal elecPrice,
        SmartStrategyElecPrice timeBasedElecPrice,
        List<BigDecimal> elecPriceList) {
        // elecPrice即可能是电价总价，也可能是电价单价，具体看智能策略的值
        elecPrice = elecPrice.setScale(4, RoundingMode.HALF_UP);

        // 取尖峰平谷的值写入计费模版名称中，用1-4来标识尖峰平谷
        Map<Integer, BigDecimal> priceMap = new HashMap<>();
        List<SitePriceSmartStrategyType> timeBaseTypeList = List.of(
            SitePriceSmartStrategyType.FIX_ELEC_FEE_TIME_BASED_PRICE,
            SitePriceSmartStrategyType.FIX_SERV_FEE_TIME_BASED_PRICE,
            SitePriceSmartStrategyType.FIX_TOTAL_FEE_TIME_BASED_PRICE);

        for (ChargeV2 chargeV2 : priceSchema.getPriceItemList()) {
            BigDecimal priceSum = chargeV2.getElecPrice().add(chargeV2.getServPrice())
                .setScale(4, RoundingMode.HALF_UP);

            if (timeBaseTypeList.contains(smartStrategy)) {
                // 这几个峰平谷的，预先处理，拿出来要设置的电价值，然后就能用不是峰平谷的逻辑去处理了
                switch (chargeV2.getCategory()) {
                    case PRICE_TAG_JIAN -> {
                        elecPrice = timeBasedElecPrice.getSharpPeakElecPrice();
                        priceMap.put(1, elecPrice);
                    }
                    case PRICE_TAG_FENG -> {
                        elecPrice = timeBasedElecPrice.getPeakElecPrice();
                        priceMap.put(2, elecPrice);
                    }
                    case PRICE_TAG_PING -> {
                        elecPrice = timeBasedElecPrice.getOffPeakElecPrice();
                        priceMap.put(3, elecPrice);
                    }
                    case PRICE_TAG_GU -> {
                        elecPrice = timeBasedElecPrice.getValleyElecPrice();
                        priceMap.put(4, elecPrice);
                    }
                }
                IotAssert.isNotNull(elecPrice, "分时电价有误");
            }
            switch (smartStrategy) {
                case FIX_TOTAL_FEE_TIME_BASED_PRICE:
                    // 总价不变-峰平谷
                case FIX_TOTAL_FEE: {
                    // 单一电价

                    // 新价格比总价大，设置新电价为电价，服务费为0，总价为新电价
                    if (elecPrice.compareTo(priceSum) > 0) {
                        chargeV2.setElecPrice(elecPrice);
                        chargeV2.setServPrice(BigDecimal.ZERO);
                    } else {
                        // 电费单价是新电价，服务费单价是旧电价减去新电价
                        chargeV2.setElecPrice(elecPrice);
                        BigDecimal newServicePrice = priceSum.subtract(elecPrice)
                            .setScale(4, RoundingMode.HALF_UP);
                        chargeV2.setServPrice(newServicePrice);
                    }
                    break;
                }
                case FIX_SERV_FEE_TIME_BASED_PRICE:
                    // 服务费不变-峰平谷
                case FIX_SERV_FEE: {
                    // 固定服务费
                    chargeV2.setElecPrice(elecPrice);
                    chargeV2.setServPrice(chargeV2.getServPrice());
                    break;
                }
                case FIX_ELEC_FEE_TIME_BASED_PRICE:
                    // 电费不变-峰平谷
                case FIX_ELEC_FEE: {
                    // 固定电费
                    chargeV2.setElecPrice(chargeV2.getElecPrice());
                    chargeV2.setServPrice(elecPrice);
                    break;
                }
            }

        }
        // 按顺序存入
        if (priceMap.get(1) != null) {
            elecPriceList.add(priceMap.get(1));
        }
        if (priceMap.get(2) != null) {
            elecPriceList.add(priceMap.get(2));
        }
        if (priceMap.get(3) != null) {
            elecPriceList.add(priceMap.get(3));
        }
        if (priceMap.get(4) != null) {
            elecPriceList.add(priceMap.get(4));
        }
    }

    // 计算分时收费带服务费
    private void calTimeBasePriceWithServiceFee(PriceTemplatePo priceSchema,
        SitePriceSmartStrategyType smartStrategy, BigDecimal elecPrice,
        SmartStrategyElecPrice timeBasedElecPrice, List<BigDecimal> elecPriceList) {
        this.calTimeBasePrice(priceSchema, smartStrategy, elecPrice, timeBasedElecPrice,
            elecPriceList);
    }

    /**
     * 校验
     *
     * @param params 参数
     */
    public void validateGenerateSmartPrice(Map<String, Object> params) {
        // 1.计算第一个智能调价
        boolean smartPrice = (boolean) params.get("smartPrice");

        // 不是智能调价，不校验，直接返回
        if (!smartPrice) {
            return;
        }
        // 1.1根据场站获取老的计费模板ID列表
        // 这几个stream可能为空，待处理
        ArrayList<Object> siteIdListObjects = (ArrayList<Object>) params.get("siteIdList");
        List<String> siteIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(siteIdListObjects)) {
            siteIdList = siteIdListObjects.stream().map(
                String::valueOf).toList();
        }
        ArrayList<Object> targetInfoListObject = (ArrayList<Object>) params.get("targetInfoList");
        List<TargetPriceSchemeInfo> priceScheme = new ArrayList<>();
        if (!CollectionUtils.isEmpty(targetInfoListObject)) {
            priceScheme = targetInfoListObject
                .stream().map(item -> JsonUtils.fromJson(JsonUtils.toJsonString(item),
                    TargetPriceSchemeInfo.class)).toList();
        }
        String target = (String) params.get("target");
        if ("SITE".equals(target)) {
            // 普通整站下发, 校验必须是一个模板
            Map<String, List<TargetPriceSchemeInfo>> validateMap = priceScheme.stream()
                .collect(Collectors.groupingBy(TargetPriceSchemeInfo::getNo,
                    Collectors.toList()));

            siteIdList.forEach(siteId -> {
                List<TargetPriceSchemeInfo> infos = validateMap.get(siteId);
                try {
                    AssertUtil.notNull(infos, "该场站没有原计费模板信息, 场站ID: " + siteId);
                } catch (Exception e) {
                    SitePo sitepo = siteBizService.getSiteById(siteId);
                    throw new DcServiceException(
                        "该场站没有原计费模板信息, 场站名称: " + sitepo.getSiteName());
                }
                try {
                    AssertUtil.isEqual(infos.size(), 1,
                        "该场站原计费为交直流模板, 场站ID: " + siteId);
                } catch (Exception e) {
                    SitePo sitepo = siteBizService.getSiteById(siteId);
                    throw new DcServiceException(
                        "该场站原计费为交直流模板, 场站名称: " + sitepo.getSiteName());
                }
            });
        } else if ("SITE_PART".equals(target)) {
            // 普通整站下发, 校验必须是一个模板
            Map<String, List<TargetPriceSchemeInfo>> validateMap = priceScheme.stream()
                .collect(Collectors.groupingBy(TargetPriceSchemeInfo::getNo,
                    Collectors.toList()));
            siteIdList.forEach(siteId -> {
                List<TargetPriceSchemeInfo> infos = validateMap.get(siteId);
                try {
                    AssertUtil.notNull(infos, "该场站没有原计费模板信息, 场站ID: " + siteId);
                } catch (Exception e) {
                    SitePo sitepo = siteBizService.getSiteById(siteId);
                    throw new DcServiceException(
                        "该场站没有原计费模板信息, 场站名称: " + sitepo.getSiteName());
                }
                try {
                    AssertUtil.isEqual(infos.size(), 2,
                        "该场站原计费为普通模板, 场站ID: " + siteId);
                } catch (Exception e) {
                    SitePo sitepo = siteBizService.getSiteById(siteId);
                    throw new DcServiceException(
                        "该场站原计费为普通模板, 场站名称: " + sitepo.getSiteName());
                }
            });
        } else if ("EVSE".equals(target)) {
            // 获取所有充电桩的第一个计费模板
            Optional<TargetPriceSchemeInfo> first = priceScheme.stream()
                .filter(item -> item.getPriceSchemeId() != null).findFirst();
            if (first.isEmpty()) {
                throw new DcServiceException("原所有充电桩无任何计费模板");
            }
        } else {
            throw new DcServiceException("不支持的操作对象: " + target);
        }
    }
}
