package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.ChargePriceCategory;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.model.charge.vo.ChargeOrderFeeItem;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.queue.CloudChargeReqQueue;
import com.cdz360.biz.dc.queue.CloudStopReqQueue;
import com.cdz360.biz.dc.repository.ChargerOrderRepository;
import com.cdz360.biz.ds.trading.ro.corp.ds.CorpRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderRoDs;
import com.cdz360.biz.ds.trading.ro.order.mapper.ChargerOrderTimeDivisionRoMapper;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteChargeJobRoDS;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargeOrderReserveDs;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderRwDs;
import com.cdz360.biz.model.order.type.OrderPayType;
import com.cdz360.biz.model.trading.order.dto.LowKwOrderDto;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.po.ChargerOrderTimeDivision;
import com.cdz360.biz.model.trading.site.po.CommPo;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobPlugVo;
import com.chargerlinkcar.framework.common.domain.BlocUserDto;
import com.chargerlinkcar.framework.common.domain.ChargeOrderReserve;
import com.chargerlinkcar.framework.common.domain.OrderReserveVo;
import com.chargerlinkcar.framework.common.domain.request.StartChargerRequest;
import com.chargerlinkcar.framework.common.domain.request.StopChargerRequest;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrder;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderSite;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.CloudChargeVo;
import com.chargerlinkcar.framework.common.domain.vo.CommCusRef;
import com.chargerlinkcar.framework.common.domain.vo.OrderBiVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderTimeShareBiVo;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.domain.vo.SiteDebitAccountBase;
import com.chargerlinkcar.framework.common.domain.vo.UserPropVO;
import com.chargerlinkcar.framework.common.feign.CardFeignClient;
import com.chargerlinkcar.framework.common.feign.UserFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import jakarta.annotation.PostConstruct;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR> 充电、订单相关
 * @since 2018/12/2 15:34
 */
@Slf4j
@Service
public class ChargerOrderBizService {

    private Map<OrderStartType, PlatformStartCharging> platformStartChargingMap = new HashMap<>();
    @Autowired
    private ChargerOrderRwDs chargerOrderRwDs;

    @Autowired
    private ChargerOrderRoDs chargerOrderRoDs;

    @Autowired
    private CorpRoDs corpRoDs;

    @Autowired
    private ChargeOrderReserveDs chargeOrderReserveDs;
    @Autowired
    private SiteChargeJobRoDS siteChargeJobDS;

//    @Autowired
//    private OrderFeignClient orderFeignClient;

    @Autowired
    private CardFeignClient cardFeignClient;


    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private TRCommercialService trCommercialService;


    @Autowired
    private OrderDataService orderDataService;


    //云端充电请求队列
    private CloudChargeReqQueue<CloudChargeVo> cloudChargeReqQueue;
    //云端停充请求队列
    private CloudStopReqQueue<StopChargerRequest> cloudStopReqQueue;

    @Autowired
    private ChargerOrderRepository chargerOrderRepository;

    @Autowired
    private ChargerOrderTimeDivisionRoMapper chargerOrderTimeDivisionMapper;

    @PostConstruct
    public void init() {
        cloudChargeReqQueue = new CloudChargeReqQueue();
        cloudStopReqQueue = new CloudStopReqQueue();
        log.info("chargeReqQueue init end");
    }

    public void pushInStartingQueue(CloudChargeVo cloudChargeVo) {
        log.info("cloudChargeVo: {}", JsonUtils.toJsonString(cloudChargeVo));
        cloudChargeVo.getBcCodeList().forEach(e -> {
            CloudChargeVo chargeReq = new CloudChargeVo();

            if (cloudChargeVo.getStartType() == OrderStartType.MGM_WEB_MANUAL) {
                // STEP 1. 插入充电记录
                ChargeOrderReserve reserve = new ChargeOrderReserve();
                reserve.setEvseNo(e.getEvseNo())
                    .setPlugId(e.getPlugId())
                    .setOpId(cloudChargeVo.getOpId())
                    .setStatus(1)
                    .setSiteId(cloudChargeVo.getSiteId())
//                .setPlanTime(null)
                    .setCommId(cloudChargeVo.getSiteOperateId())
                    .setTopCommId(cloudChargeVo.getSiteTopCommId());
                chargeOrderReserveDs.insertOrUpdate(reserve);
                log.info("reserve.id: {}", reserve.getId());
                chargeReq.setChargeOrderReserveId(reserve.getId());
            }

            // STEP 2.插入到队列
            chargeReq.setPlugNo(e.getPlugNo())
                .setSiteTopCommId(cloudChargeVo.getSiteTopCommId())
                .setOrderCustomerId(cloudChargeVo.getOrderCustomerId())
                .setOrderCustomerName(cloudChargeVo.getOrderCustomerName())
                .setOrderPayAccountId(cloudChargeVo.getOrderPayAccountId())
                .setDefaultPayType(cloudChargeVo.getDefaultPayType())
                .setPayType(cloudChargeVo.getPayType())
                .setFrozenAmount(cloudChargeVo.getFrozenAmount())
                .setAccountTotalAmount(cloudChargeVo.getAccountTotalAmount())
                .setAccountNo(cloudChargeVo.getAccountNo())
                .setOrderMobilePhone(cloudChargeVo.getOrderMobilePhone())
                .setSiteContactsPhone(cloudChargeVo.getSiteContactsPhone())
                .setStartType(cloudChargeVo.getStartType())
                .setStopSoc(cloudChargeVo.getStopSoc())
//                    .setDiscountRefId(cloudChargeVo.getDiscountRefId())
            ;
            log.info("chargeReq: {}", JsonUtils.toJsonString(chargeReq));
            cloudChargeReqQueue.offer(chargeReq);
        });
        log.info("pushInStartingQueue end");
    }

    public CloudChargeVo startPoll(long timeout, TimeUnit unit) throws InterruptedException {
        return this.cloudChargeReqQueue.poll(timeout, unit);
    }

    public void pushInStopingQueue(List<StopChargerRequest> stopRequest) {
        log.info("stopRequest: {}", JsonUtils.toJsonString(stopRequest));
        if (stopRequest != null) {
            stopRequest.forEach(e -> {
                // 插入到队列
                IotAssert.isNotNull(e.getOrderNo(), "orderNo为空");
                cloudStopReqQueue.offer(e);
            });
        }
        log.info("end");
    }

    public StopChargerRequest stopPoll(long timeout, TimeUnit unit) throws InterruptedException {
        return this.cloudStopReqQueue.poll(timeout, unit);
    }


    public List<com.chargerlinkcar.framework.common.domain.vo.ChargerOrder> findChargerOrder(
        List<String> orderNoList) {
        log.info("查询充电订单列表: orderNoList={}", orderNoList);
        if (null == orderNoList || orderNoList.isEmpty()) {
            return new ArrayList<>();
        }

        // 订单列表
        List<com.chargerlinkcar.framework.common.domain.vo.ChargerOrder> orderList =
            chargerOrderRwDs.findChargerOrder(orderNoList);

        // 集团客户Id
        Set<Long> blocUserIds = orderList.stream()
            .map(com.chargerlinkcar.framework.common.domain.vo.ChargerOrder::getPayAccountId)
            .collect(Collectors.toSet());

        // 默认扣款账户名称
        Map<String, String> blocNameMap = new HashMap<>();
        if (blocUserIds.size() > 0) {
            ListResponse<RBlocUser> rBlocUserList = userFeignClient.selectRBlocUserIds(
                new ArrayList<>(blocUserIds));
            if (rBlocUserList.getData() != null && rBlocUserList.getData().size() > 0) {
                blocNameMap = rBlocUserList.getData().stream().collect(
                    Collectors.toMap(v -> String.valueOf(v.getId()),
                        RBlocUser::getBlocUserName, (v1, v2) -> v1));
            }
        }

        // 扣款账户名称
        final Map<String, String> fBlocNameMap = blocNameMap;
        orderList.forEach(o -> {
            if (o.getDefaultPayType() != null && o.getPayAccountId() != null
                && o.getCustomerId() != null) {
                if (OrderPayType.PERSON.getCode() == o.getDefaultPayType()) {
                    o.setPayAccountName("个人账户");
                } else if (OrderPayType.BLOC.getCode() == o.getDefaultPayType()) {
                    StringBuilder sb = new StringBuilder();
                    sb.append("企业账户-")
                        .append(fBlocNameMap.get(String.valueOf(o.getPayAccountId())));
                    o.setPayAccountName(sb.toString());
                } else if (OrderPayType.MERCHANT.getCode() == o.getDefaultPayType()) {
                    CommPo commPo = trCommercialService.getCommercialById(
                        o.getPayAccountId());
                    if (commPo != null) {
                        StringBuilder sb = new StringBuilder();
                        sb.append("商户会员-")
                            .append(commPo.getName());
                        o.setPayAccountName(sb.toString());
                    }
                }
            }
        });

        return orderList;
    }

    public com.chargerlinkcar.framework.common.domain.vo.ChargerOrder findByOrderNo(
        String orderNo) {
        log.info("获取充电订单: orderNo={}", orderNo);
        return chargerOrderRwDs.findByOrderNo(orderNo);
    }

    public synchronized void addStartChargingMap(OrderStartType type,
        PlatformStartCharging charging) {
        platformStartChargingMap.put(type, charging);
    }

    public CloudChargeVo webStartCharger(StartChargerRequest chargerRequest) {
        log.info("platformStartChargingMap: {}", platformStartChargingMap);
        PlatformStartCharging startCharging = platformStartChargingMap.get(
            chargerRequest.getStartType());
        log.info("startCharging: {}", startCharging);
        CloudChargeVo res = startCharging.checkChargingQueue(chargerRequest);
        log.info("res: {}", res);
        if (CollectionUtils.isNotEmpty(res.getBcCodeList())) {
            this.pushInStartingQueue(res);
        }
        return res;
    }


    /**
     * 定时任务停止充电
     *
     * @param startType   传OrderStartType.SCHEDULE_JOB 或 OrderStartType.MGM_WEB_MANUAL
     * @param stopRequest
     * @return {@code <plugno, failReasonString>}
     */
    public Optional<Map<String, String>> webStopCharger(OrderStartType startType,
        List<StopChargerRequest> stopRequest) {
        PlatformStartCharging startCharging = platformStartChargingMap.get(startType);
        Optional<Map<String, String>> failPlugNoMap = startCharging.checkStopingQueue(stopRequest);
        this.pushInStopingQueue(stopRequest);
        return failPlugNoMap;
    }


    public OrderReserveVo getReserveInfoByPlugNoList(List<String> plugNoList) {
        return chargeOrderReserveDs.getReserveInfoByPlugNoList(plugNoList);
    }

    public List<SiteChargeJobPlugVo> getSiteJobByPlugNoList(List<String> plugNoList) {
        return siteChargeJobDS.getJobInFoByPlugNos(plugNoList);
    }


    /**
     * 批量修改订单表里的场站名字
     *
     * @param siteId
     * @param siteName
     */
    @Async
    public void updateSiteNameTask(String siteId, String siteName, String traceId) {
        log.info("修改订单上的场站名字 siteId = {}, siteName = {}, traceId = {}", siteId, siteName,
            traceId);
        this.chargerOrderRwDs.updateSiteName(siteId, siteName);
    }

    /**
     * 场站后台开启充电-扣款账户校验 用于 开启充电 和 修改场站扣款账户 时
     *
     * @param request
     * @return
     */
    public CloudChargeVo siteDebitAccountCheck(SiteDebitAccountBase request) {
        // 开启充电时,startCharingEnable和settlementMethod都为null,payType不为null
        if (request.getStartCharingEnable() == null && request.getSettlementMethod() == null) {
            // do nothing
        } else {
            if (request.getStartCharingEnable() != null
                && !request.getStartCharingEnable()
                && request.getSettlementMethod() != null) {
                // 配置为禁用，但指定结算账户方式不为null
                throw new DcServiceException("请确认您的勾选结果后，重新提交");
            }
            if (request.getStartCharingEnable() != null
                && request.getStartCharingEnable()
                && request.getSettlementMethod() == null) {
                // 配置为启用，但指定结算账户方式为null
                throw new DcServiceException("未选择结算账户方式");
            }
            if (request.getSettlementMethod() != null
                && request.getSettlementMethod() == 1 &&
                (request.getPayType() != null || request.getCommId() != null
                    || request.getPhone() != null)) {
                // 配置为启动时指定，但结算账户等字段不为null
                throw new DcServiceException("请确认您的勾选结果后，重新提交");
            }

            if (request.getStartCharingEnable() != null && !request.getStartCharingEnable()) {
                //不允许后台开启充电
                request.setPayType(PayAccountType.UNKNOWN);
            } else if (request.getStartCharingEnable() != null
                && request.getStartCharingEnable()
                && request.getSettlementMethod() != null
                && request.getSettlementMethod() == 1) {
                //后台启动时指定
                request.setPayType(PayAccountType.OTHER);
            }
        }
        IotAssert.isNotNull(request.getPayType(), "未选择结算账户类型");

        Long orderCustomerId = null;
        Long orderCustomerCommId = null;
        String orderCustomerName = null;
        Long sitePayAccountId = null;
        Long orderPayAccountId = null;
//        Integer defaultPayType = null;
        PayAccountType payType = request.getPayType();
        String accountNo = null;
        String orderMobilePhone = null;
        switch (payType) {
            case PERSONAL:
                IotAssert.isNotBlank(request.getPhone(), "手机号不能为空");
                ObjectResponse<UserPropVO> userPropVOObjectResponse = userFeignClient.findByPhone(
                    request.getPhone(), request.getTopCommId());
                FeignResponseValidate.check(userPropVOObjectResponse);
                UserPropVO vo = userPropVOObjectResponse.getData();
                IotAssert.isTrue(NumberUtils.equals(vo.getStatus(), 10001),
                    "当前手机号对应账户不可用");
                orderCustomerId = vo.getUserId();
                orderCustomerCommId = request.getTopCommId();
                orderCustomerName = vo.getUsername();
                sitePayAccountId = vo.getUserId();
                orderPayAccountId = request.getTopCommId();
//                defaultPayType = OrderPayType.PERSON.getCode();
                accountNo = request.getPhone();
                orderMobilePhone = request.getPhone();
                break;
            case CREDIT:
                // TODO: 2020/3/16 校验 corpUserId
                IotAssert.isNotNull(request.getCommId(), "请先选择所属商户");
                IotAssert.isNotNull(request.getCorpUserId(), "请先选择企业授信客户");
                ObjectResponse<BlocUserDto> userDtoObjectResponse = userFeignClient.getByRBlocUserId(
                    request.getCorpUserId());
                FeignResponseValidate.check(userDtoObjectResponse);
                BlocUserDto dto = userDtoObjectResponse.getData();
                IotAssert.isTrue(null != dto && null != dto.getEnable() && dto.getEnable(),
                    "当前账户不可用");
                RBlocUser temp = new RBlocUser();
                temp.setId(request.getCorpUserId());
                ListResponse<RBlocUser> rBlocUserListResponse = userFeignClient.findByCondition(
                    temp);
                FeignResponseValidate.check(rBlocUserListResponse);
                IotAssert.isTrue(CollectionUtils.isNotEmpty(rBlocUserListResponse.getData()),
                    "找不到对应企业授信客户");
                RBlocUser rBlocUser = rBlocUserListResponse.getData().get(0);
                IotAssert.isTrue(null != rBlocUser.getStatus() &&
                    NumberUtils.equals(rBlocUser.getStatus(), 1), "当前账户不可用");
                IotAssert.isTrue(null != rBlocUser.getCommId() &&
                        NumberUtils.equals(rBlocUser.getCommId(), request.getCommId()),
                    "授信客户与所属商户不对应，请重新配置结算账户");
                orderCustomerId = rBlocUser.getUserId();
                orderCustomerCommId = rBlocUser.getCommId();
                orderCustomerName = rBlocUser.getUserName();
                sitePayAccountId = rBlocUser.getId();
                orderPayAccountId = rBlocUser.getId();
//                defaultPayType = OrderPayType.BLOC.getCode();
                accountNo = rBlocUser.getPhone();
                orderMobilePhone = rBlocUser.getPhone();
                break;
            case COMMERCIAL:
                // TODO: 2020/3/16 校验commId
                IotAssert.isNotNull(request.getCommId(), "请先选择所属商户");
                IotAssert.isNotNull(request.getPhone(), "手机号不能为空");
                ObjectResponse<CommCusRef> commCusRefObjectResponse = userFeignClient.findByCommIdAndPhone(
                    request.getCommId(), request.getPhone());
                FeignResponseValidate.check(commCusRefObjectResponse);
                CommCusRef ref = commCusRefObjectResponse.getData();
                IotAssert.isTrue(NumberUtils.equals(ref.getEnable(), 1),
                    "当前手机号对应账户不可用");
                orderCustomerId = ref.getUserId();
                orderCustomerCommId = ref.getUserCommId();
                orderCustomerName = ref.getUserName();
                sitePayAccountId = ref.getId();
                orderPayAccountId = request.getCommId();
//                defaultPayType = OrderPayType.MERCHANT.getCode();
                accountNo = request.getPhone();
                orderMobilePhone = request.getPhone();
                break;
            case UNKNOWN:
                orderCustomerId = 0l;
                orderCustomerCommId = 0l;
                orderCustomerName = null;
                sitePayAccountId = 0l;
                orderPayAccountId = 0l;
//                defaultPayType = 10;
                break;
            case OTHER:
                orderCustomerId = 0l;
                orderCustomerCommId = 0l;
                orderCustomerName = null;
                sitePayAccountId = 0l;
                orderPayAccountId = 0l;
//                defaultPayType = 11;
                break;
            default:
                throw new DcServiceException("请传入正确的结算账户类型");
        }
        CloudChargeVo res = new CloudChargeVo();
        res.setOrderCustomerId(orderCustomerId)
            .setOrderCustomerCommId(orderCustomerCommId)
            .setOrderCustomerName(orderCustomerName)
            .setSitePayAccountId(sitePayAccountId)
            .setOrderPayAccountId(orderPayAccountId)
            .setDefaultPayType(payType.getCode())
            .setPayType(payType)
            .setAccountNo(accountNo)
            .setOrderMobilePhone(orderMobilePhone);
        return res;
    }

    public Mono<ListResponse<ChargerOrderVo>> listChargerOrder(ListChargeOrderParam param) {
        ListResponse<ChargerOrder> res = chargerOrderRoDs.listChargeOrder(param);
        return Mono.just(res)
            .doOnNext(result -> result.setTotal(null != result.getTotal() ? result.getTotal() : 0L))
            .map(result -> RestUtils.buildListResponse(map2Vo(res.getData()), result.getTotal()));
    }

    public ObjectResponse<OrderBiVo> chargeOrderBiForCorp(ListChargeOrderParam param) {
//        if (param.getCorpId() > 0L) {
//            CorpPo corp = corpRoDs.getCorpById(param.getCorpId());
//            IotAssert.isNotNull(corp, "企业ID无效");
//            if (CorpType.HLHT == corp.getType()
//                    && param.getSettlementType() == null) {
//                param.setSettlementType(SettlementType.PARTNER);
//            }
//        }

        return chargerOrderRoDs.chargeOrderBiForCorp(param);
    }

    public ListResponse<ChargerOrderSite> chargerOrderGroupBySite(ListChargeOrderParam param) {
        List<ChargerOrderSite> res = chargerOrderRoDs.chargerOrderGroupBySite(param);
        return RestUtils.buildListResponse(res);
    }

    public ListResponse<OrderTimeShareBiVo> chargerOrderGroupByTimeShareFee(
        ListChargeOrderParam param) {
        List<OrderTimeShareBiVo> res = chargerOrderRoDs.chargerOrderGroupByTimeShareFee(param);
        return RestUtils.buildListResponse(res);
    }

    private List<ChargerOrderVo> map2Vo(List<ChargerOrder> orderList) {
        if (CollectionUtils.isEmpty(orderList)) {
            return new ArrayList<>();
        }

        return orderList.stream().map(this::map2Vo).collect(Collectors.toList());
    }

    private ChargerOrderVo map2Vo(ChargerOrder order) {
        ChargerOrderVo vo = new ChargerOrderVo();
        BeanUtils.copyProperties(order, vo);

        vo.setChargeStartTime(
            order.getChargeStartTime() == null ? 0L : order.getChargeStartTime().longValue());
        vo.setChargeEndTime(
            order.getChargeEndTime() == null ? 0L : order.getChargeEndTime().longValue());

        return vo;
    }

    /**
     * mongodb 设定拔枪时间
     *
     * @param orderNo
     * @return
     */
    public BaseResponse setPlugOutTime(String orderNo) {
        final Instant now = Instant.now();  //当前的时间
        long plugOutTime = now.toEpochMilli();
        chargerOrderRepository.update(orderNo, plugOutTime);
        return BaseResponse.success();
    }

    public Mono<ListResponse<ChargerOrderVo>> listChargerOrder4Hlht(ListChargeOrderParam param) {
        ListResponse<ChargerOrder> res = chargerOrderRoDs.listChargerOrder4Hlht(param);

        Set<String> orderNoSet = res.getData().stream().filter(e -> e.getOrderNo() != null)
            .map(e -> String.valueOf(e.getOrderNo())).collect(Collectors.toSet());
        Map<String, List<ChargerOrderTimeDivision>> chargerOrderTimeDivisionMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(orderNoSet)) {
            List<ChargerOrderTimeDivision> chargerOrderTimeDivisionList = chargerOrderTimeDivisionMapper.selectListByOrderNoList(
                new ArrayList<String>(orderNoSet));
            if (CollectionUtils.isNotEmpty(chargerOrderTimeDivisionList)) {
                for (String orderNo : orderNoSet) {
                    List<ChargerOrderTimeDivision> chargerOrderTimeDivisionListTemp = new ArrayList<>();
                    chargerOrderTimeDivisionList.forEach(o -> {
                        if (o.getOrderNo().equals(orderNo)) {
                            chargerOrderTimeDivisionListTemp.add(o);
                        }
                    });
                    chargerOrderTimeDivisionMap.put(orderNo, chargerOrderTimeDivisionListTemp);
                }
            }
        }

        return Mono.just(res)
            .map(result -> {
                List<ChargerOrderVo> chargerOrderVoList = map2Vo(res.getData());
                if (CollectionUtils.isNotEmpty(chargerOrderVoList)) {
                    chargerOrderVoList.forEach(e -> {
                        List<ChargeOrderFeeItem> feeItemList = new ArrayList<>();
                        if (chargerOrderTimeDivisionMap != null
                            && chargerOrderTimeDivisionMap.size() > 0) {
                            List<ChargerOrderTimeDivision> chargerOrderTimeDivisionList =
                                chargerOrderTimeDivisionMap.get(String.valueOf(e.getOrderNo()));
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            if (CollectionUtils.isNotEmpty(chargerOrderTimeDivisionList)) {
                                for (ChargerOrderTimeDivision division : chargerOrderTimeDivisionList) {
                                    ChargeOrderFeeItem item = new ChargeOrderFeeItem();
                                    try {
                                        item.setCategory(division.getTag() == null ? null
                                                : ChargePriceCategory.valueOf(division.getTag()))
                                            .setStartTime(division.getStartTime() == null ? null
                                                : sdf.parse(division.getStartTime()))
                                            .setStopTime(division.getStopTime() == null ? null
                                                : sdf.parse(division.getStopTime()))
                                            .setKwh(division.getElectric())
                                            .setElecPrice(division.getElectricUnit())
                                            .setElecFee(division.getElectricPrice())
                                            .setServPrice(division.getServiceUnit())
                                            .setServFee(division.getServicePrice());
                                        feeItemList.add(item);
                                    } catch (ParseException exception) {
                                        log.warn("分时解析时间出错 startTime = {} , stopTime = {}",
                                            division.getStartTime(), division.getStopTime());
                                    }
                                }
                            }
                        }
                        e.setFeeDetail(feeItemList);
                    });
                }
                return RestUtils.buildListResponse(chargerOrderVoList);
            });
    }
}
