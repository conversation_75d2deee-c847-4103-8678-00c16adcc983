package com.cdz360.biz.dc.constant;

/**
 * 订单消息推送状态枚举
 */
//public class ChargerOrderConstants {
//
////    //*********************充电订单来源**********************
////    //充电订单来源,刷卡
////    public static final int CHARGER_SOURCE_CARD = 11;
////    //充电订单来源,微信公众号
////    public static final int CHARGER_SOURCE_WECHATPUB = 12;
////    //充电订单来源,微信端
////    public static final int CHARGER_SOURCE_WECHAT = 13;
////    //充电订单来源,app(包括,ios,android)
////    public static final int CHARGER_SOURCE_APP = 14;
////    //充电订单来源,支付宝小程序
////    public static final int CHARGER_SOURCE_ALIPAY_MINI = 15;
////    //充电订单来源,支付宝生活号
////    public static final int CHARGER_SOURCE_ALIPAY = 16;
////    //*********************充电订单来源**********************
//
//
////    //*********************充电订单收费模式**********************
////    //充电单固定收费模式
////    public static final int CHARGER_CLEARING_MODE_IB = 3;
////    //充电单实时收费模式
////    public static final int CHARGER_CLEARING_MODE_RT = 4;
////    //*********************充电订单收费模式**********************
//
//
////    //*********************充电订单推送消息状态**********************
////    //卡充电开始消息推送
////    public static final String ORDER_CARD_START = "ORDER_CARD_START";
////
////    //订单结束消息推送
////    public static final String ORDER_COMPLETE = "ORDER_COMPLETE";
////
////    //订单支付成功消息推送
////    public static final String ORDER_PAY_MENT_SUCCEED = "ORDER_PAY_MENT_SUCCEED";
////    //*********************充电订单推送消息状态**********************
//
//
//    //*********************充电订单支付状态**********************
//    //充电订单支付状态,已支付
//    public static final int CHARGER_PAY_STATUS_ISPAY = 2;
//    //*********************充电订单支付状态**********************
//}
