package com.cdz360.biz.dc.client.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.trading.hlht.po.PartnerPo;
import com.cdz360.biz.model.trading.hlht.po.PartnerSitePo;
import com.chargerlinkcar.framework.common.domain.param.CorpWxSendMsgCommonParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

import java.util.function.Function;

/**
 * ReactorUserFeignClientHystrixFactory
 *
 * @since 11/3/2020 10:32 AM
 * <AUTHOR>
 */
@Slf4j
@Component
public class ReactorOpenHlhtFeignClientHystrixFactory implements FallbackFactory<ReactorOpenHlhtFeignClient> {
    @Override
    public ReactorOpenHlhtFeignClient apply(Throwable throwable) {
        log.error("err= {}", throwable.getMessage(), throwable);
        return new ReactorOpenHlhtFeignClient() {

            @Override
            public Mono<ListResponse<PartnerSitePo>> getSiteListByCode(String code) {
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ObjectResponse<PartnerPo>> getDetailByCode(String code) {
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<String>> sendMsgCommon(Long topCommId,
                CorpWxSendMsgCommonParam param) {
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }
        };
    }

    @Override
    public <V> Function<V, ReactorOpenHlhtFeignClient> compose(Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_OPEN_HLHT);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(Function<? super ReactorOpenHlhtFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_OPEN_HLHT);
        return null;
    }
}