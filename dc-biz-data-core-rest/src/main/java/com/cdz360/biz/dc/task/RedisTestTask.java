package com.cdz360.biz.dc.task;

import com.cdz360.data.cache.RedisAppReadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 仅用于测试环境,确保不出现redis reset by peer问题
 */
@Slf4j
@Component
@Profile(value = {"test01", "test02", "test03", "test04", "test05", "test06", "test07"})
public class RedisTestTask {
    @Autowired
    private RedisAppReadService redisAppReadService;

    @Scheduled(initialDelay = 4000, fixedRate = 60000)
    public void testRedisRead() {

        try {
            redisAppReadService.getAppCfg("wx9f655776219990e6");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

}
