package com.cdz360.biz.dc.rest.site;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.dc.domain.vo.SiteInMongoVo;
import com.cdz360.biz.dc.service.site.SiteBizService;
import com.cdz360.biz.dc.utils.RedisUtil;
import com.cdz360.biz.model.cus.commercial.vo.CommercialManage;
import com.chargerlinkcar.framework.common.domain.request.SiteAroundRequest;
import com.chargerlinkcar.framework.common.domain.vo.SiteAroundVo;
import com.chargerlinkcar.framework.common.feign.AuthCenterFeignClient;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.StringJoiner;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "场站周边服务相关接口", description = "场站周边服务")
public class SiteAroundRest {

    final String url = "https://apis.map.qq.com/ws/place/v1/search";

    @Autowired
    private SiteBizService siteService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @PostMapping("/dataCore/site/getSiteAroundInfo")
    @Operation(summary = "获取站点周边服务信息")
    public ListResponse<SiteAroundVo> getSiteAroundInfo(@RequestBody SiteAroundRequest param)
        throws IOException {

        log.info("siteParam={}", param);
        IotAssert.isNotBlank(param.getSiteId(), "站点ID不能为空");
        IotAssert.isNotBlank(param.getAroundType().getCode(), "查询类型不能为空");
        IotAssert.isNotNull(param.getTopCommId(), "商户ID不能为空");

        if (param.getPage() == null) {
            param.setPage(1L);
        }
        if (param.getSize() == null) {
            param.setSize(3L);
        }
        /**
         * 腾讯api  限制每天额度 2000，不够使用
         * 修改减少请求次数
         * 1、一次请求拉取满足客户端的全部数据 ，40条
         * 2、缓存数据失效时间拉长
         */
        long total = 20L;
        String key =
            "siteService:" + param.getSiteId() + ":aroundType:" + param.getAroundType() + ":page:"
                + param.getPage() + ":size:" + total;

        List<SiteAroundVo> siteAroundVo = null;

        if (redisUtil.get(key) != null) {
            siteAroundVo = JsonUtils.fromJson(redisUtil.get(key), new TypeReference<>() {
            });
            if (CollectionUtils.isNotEmpty(siteAroundVo)) {
                int size = siteAroundVo.size();
                // 按照分页截取
                return new ListResponse<>(siteAroundVo.subList(0,
                    param.getSize() > size ? size : Math.toIntExact(param.getSize())));
            }
        }

        SiteInMongoVo site = siteService.getSiteFromMongo(param.getSiteId(), null);

        if (site == null || site.getPosition() == null || site.getPosition().length < 2) {
            throw new DcServiceException("站点信息不正确");
        }
        param.setPosition(site.getPosition());
        String url = getUrl(param);
        if (url == null) {
            return new ListResponse<>(null);
        }
        String jsonString = sendGet(url);

        if (jsonString != null) {
            siteAroundVo = JsonUtils.fromJson(jsonString, new TypeReference<>() {
            });
            redisUtil.set(key, JsonUtils.toJsonString(siteAroundVo), 24 * 120);
        }

        // 按照分页截取
        if (CollectionUtils.isNotEmpty(siteAroundVo)) {
            int size = siteAroundVo.size();
            siteAroundVo = siteAroundVo.subList(0,
                param.getSize() > size ? size : Math.toIntExact(param.getSize()));
        }

        return new ListResponse<>(siteAroundVo);
    }


    private String getUrl(SiteAroundRequest param) {
        StringJoiner str = new StringJoiner("");
        str.add(url).add("?keyword=");
        str.add(URLEncoder.encode(param.getAroundType().getName()));
        double[] position = param.getPosition();
        str.add("&boundary=nearby(").add(new String(position[1] + "," + position[0])).add(",2000)");
        str.add("&page_size=").add("20");
        str.add("&page_index=").add(param.getPage().toString());
        str.add("&orderby=_distance");
        ObjectResponse<CommercialManage> commercialManageObjectResponse = authCenterFeignClient.getCommercialManage(
            param.getTopCommId());
        if (commercialManageObjectResponse != null
            && commercialManageObjectResponse.getData() != null
            && commercialManageObjectResponse.getData().getWxServiceApiKey() != null) {
            str.add("&key=").add(commercialManageObjectResponse.getData().getWxServiceApiKey());
        } else {
            return null;
        }

        return str.toString();
    }

    public String sendGet(String url) throws IOException {
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        HttpGet httpGet = new HttpGet(url);
        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(httpGet);
            HttpEntity responseEntity = response.getEntity();
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK
                && responseEntity != null) {
                String ret = EntityUtils.toString(responseEntity);
                JsonNode jsonObject = JsonUtils.fromJson(ret);
                if (jsonObject.get("status").intValue() == 0) {
                    return jsonObject.get("data").toString();
                } else {
                    log.error("周边服务调用错误:message={}", jsonObject.get("message"));
                    return null;
                }

            }

        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }
}
