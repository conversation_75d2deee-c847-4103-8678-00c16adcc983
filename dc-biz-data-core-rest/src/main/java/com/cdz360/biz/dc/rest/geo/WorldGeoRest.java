package com.cdz360.biz.dc.rest.geo;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.dc.service.geo.WorldGeoService;
import com.cdz360.biz.model.geo.param.ListCitiesParam;
import com.cdz360.biz.model.geo.param.ListCountriesParam;
import com.cdz360.biz.model.geo.vo.GeoCitiesVo;
import com.cdz360.biz.model.geo.vo.GeoCountriesVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "国家/州", description = "国家/州相关接口合集")
@Slf4j
@RestController
@RequestMapping("/dataCore/geo")
public class WorldGeoRest {

    @Autowired
    private WorldGeoService worldGeoService;

    @Operation(summary = "获取国家列表列表")
    @PostMapping(value = "/findCountries")
    public Mono<ListResponse<GeoCountriesVo>> findCountries(
        @RequestBody ListCountriesParam param) {
        log.info("获取国家列表列表: param = {}", JsonUtils.toJsonString(param));
        return worldGeoService.findCountries(param);
    }

    @Operation(summary = "获取城市列表")
    @PostMapping(value = "/findCities")
    public Mono<ListResponse<GeoCitiesVo>> findCities(
        @RequestBody ListCitiesParam param) {
        log.info("获取城市列表: param = {}", JsonUtils.toJsonString(param));
        return worldGeoService.findCities(param);
    }

}
