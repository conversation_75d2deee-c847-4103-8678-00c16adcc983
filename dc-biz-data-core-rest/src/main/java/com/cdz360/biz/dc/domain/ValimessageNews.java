package com.cdz360.biz.dc.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * 发送消息所用参数
 */
@Data
public class ValimessageNews implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 访问来源
     */
    private Integer sourceId;

    /**
     * 商户id
     **/
    private Long commId;

    /**
     * 用户id
     */
    private Long userId;

    /**调用信息推送服务的唯一标识 */
    private String appId;

    private String appSecret;
    /**调用信息推送服务的唯一标识 */
    private String apiKey;
    /** 消息类型 SMS:短信APP:APPWECHAT:微信WECHAT:钉钉 EMAIL:邮件*/
    private  String messageType;
    /**模板编号 */
    private String templateNo;
    /** */
    private String method;
    /** userId*/
    private String toUser;
    /** formId*/
    private String formId;
    private String emphasisKeyword;
    /** 消息类型 1:充值成功2:退款3:充电结束4:充电开始*/
    private String type;
    /**消息内容 */
    //private AppNotification data; // 通知内容体
    private String orderId; //开启充电订单号
    private String startChargeTime; //开启充电的开始时间
    private String preMoney; //开启充电预付款金额
    private String duration; //充电时长
    private String siteName; //开启充电站点名称
    private String serialNumber; //开启充电桩编号
    private String chargeStopReaso; //充电结束原因
    private String chargeStopTime; //结束时间
    private String orderPrice; //结束充电费用
    private String refundNumber; //退款订单编号
    private String refundFees; //退款预付款金额
    private String refundTime; //退款时间
    private String rechargeFees; //充值金额
    private String rechargeAccount; //充值账号
    private String rechargeTime; //充值时间
    private String rechargeBalance; //充值账户余额
}
