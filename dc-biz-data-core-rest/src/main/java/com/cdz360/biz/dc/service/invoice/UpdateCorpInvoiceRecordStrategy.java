package com.cdz360.biz.dc.service.invoice;

import com.cdz360.biz.model.trading.invoice.dto.CorpInvoiceRecordDto;
import com.cdz360.biz.model.trading.invoice.vo.CorpInvoiceRecordVo;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordUpdateParam;

public interface UpdateCorpInvoiceRecordStrategy {

    CorpInvoiceRecordVo updateRecord(CorpInvoiceRecordUpdateParam param, boolean append);

    CorpInvoiceRecordVo deleteRecord(CorpInvoiceRecordUpdateParam param, CorpInvoiceRecordDto dto);

    CorpInvoiceRecordDto preUpdate(CorpInvoiceRecordUpdateParam param, boolean append);
}
