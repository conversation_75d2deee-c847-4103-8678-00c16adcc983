package com.cdz360.biz.dc.service;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.ds.trading.ro.site.ds.BsBoxSettingRoDs;
import com.cdz360.biz.model.iot.param.ModifyEvseCfgParam;
import com.cdz360.biz.model.trading.site.po.BsBoxSettingPo;
import com.cdz360.biz.model.trading.site.vo.PriceSchemeSiteVo;
import com.chargerlinkcar.framework.common.domain.param.ListPriceSchemeSiteUseParam;
import com.chargerlinkcar.framework.common.domain.type.EvseCfgEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/1/6 20:00
 */
@Slf4j
@Service
public class BsBoxSettingService {
    @Autowired
    private BsBoxSettingRoDs bsBoxSettingRoDs;

    /**
     * 获取下发中的桩配置
     *
     * @param evseNoList
     * @param lock
     * @return
     */
    public List<BsBoxSettingPo> getByEvseNo(List<String> evseNoList, List<Integer> statusList, boolean lock) {
        log.info("获取桩配置: evseNoList size = {}, statusList = {}, lock = {}", evseNoList.size(), statusList, lock);
        return bsBoxSettingRoDs.getByEvseNo(evseNoList, statusList, lock);
    }

    public List<PriceSchemeSiteVo> getSchemeSiteByPriceSchemeId(ListPriceSchemeSiteUseParam param) {
        log.info("获取使用该计费模板: priceSchemeIdList = {}", param.getPriceSchemeIdList());
        return bsBoxSettingRoDs.getSchemeSiteUsed(param);
//        List<BsBoxSettingWithSiteInfoVo> infoVoList = bsBoxSettingRoDs.getSchemeSiteByPriceSchemeId(param, lock);
//
//        // 过滤去重后返回
//        return infoVoList.stream().map(i -> {
//            PriceSchemeSiteVo vo = new PriceSchemeSiteVo();
//            vo.setPriceSchemeId(i.getChargeId())
//                    .setSiteId(i.getSiteId())
//                    .setSiteName(i.getSiteName())
//                    .setStatus(EvseCfgResult.SUCCESS);
//            return vo;
//        }).distinct().collect(Collectors.toList());
    }

    public int insertOrUpdate(BsBoxSettingPo po) {
        log.info(">> 新增/更新: poList = {}", po);
        int i = bsBoxSettingRoDs.insertOrUpdate(po);
        log.info("<< i = {}", i);
        return i;
    }

    /**
     * 批量更新
     *
     * @param byEvseNo
     */
    public long batchUpdate(List<BsBoxSettingPo> byEvseNo) {
        if (CollectionUtils.isEmpty(byEvseNo)) {
            return 0;
        }

        // 考虑数量巨大，分批更新
        int start = 0;
        int size = 200;

        int cnt = byEvseNo.size() / size + 1;
        do {
            int i = bsBoxSettingRoDs.batchUpdate(
                    byEvseNo.stream().skip(start).limit(size).collect(Collectors.toList()));

            start += i;
            --cnt;
        } while (cnt > 0);

        return start;
    }

    /**
     * 批量插入数据
     *
     * @param byEvseNo
     */
    public long batchInsert(List<BsBoxSettingPo> byEvseNo) {
        if (CollectionUtils.isEmpty(byEvseNo)) {
            return 0;
        }

        // 考虑数量巨大，分批更新
        int start = 0;
        int size = 200;

        int cnt = byEvseNo.size() / size + 1;
        do {
            int i = bsBoxSettingRoDs.batchInsert(
                    byEvseNo.stream().skip(start).limit(size).collect(Collectors.toList()));

            start += i;
            --cnt;
        } while (cnt > 0);

        return start;
    }

    public long batchUpdateBoxSettingStatus(List<BsBoxSettingPo> poList) {
        log.info("批量更新桩配置下发状态: size = {}", poList.size());

        if (CollectionUtils.isEmpty(poList)) {
            log.info("入参数据大小为空");
            return 0;
        }

        List<String> evseNoList = poList.stream()
                .map(BsBoxSettingPo::getBoxOutFactoryCode).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(evseNoList)) {
            log.info("桩编号不存在，数据不完整");
            return 0;
        }

        List<BsBoxSettingPo> byEvseNo = this.getByEvseNo(evseNoList, null, false);

        poList.parallelStream().forEach(po -> {
            Optional<BsBoxSettingPo> first = byEvseNo.stream()
                    .filter(s -> s.getBoxOutFactoryCode().equals(po.getBoxOutFactoryCode())).findFirst();
            first.ifPresent(bsBoxSettingPo -> po.setId(bsBoxSettingPo.getId()));
        });

        return this.batchUpdate(poList);
    }

    /**
     * 下发配置时的装配置更新,该方法仅仅针对配置下发,紧急卡和计费模板需要做对应调整
     *
     * @param param
     */
    public void sendInsertOrUpdate(ModifyEvseCfgParam param) {
        param.getEvseNoList().stream().map(evseNo -> {
            BsBoxSettingPo setting = new BsBoxSettingPo();
            BeanUtils.copyProperties(param, setting);

            // 字段不一样
            setting.setUrl(param.getQrUrl());
            setting.setTemplateCode(null);    // 计费模板信息没有
            setting.setChargeId(null);        // 计费模板为空
            setting.setWhiteCardList(null);   // 紧急充电卡为空

            setting.setBoxCode(evseNo)
                    .setBoxOutFactoryCode(evseNo);
            setting.setStatus(EvseCfgEnum.EVSE_CFG_SEND_PROCESS.value);
            return setting;
        }).forEach(this::insertOrUpdate);
    }

    /**
     * 批量更新桩配置状态
     *
     * @param evseNoList
     * @param status
     */
    public void updateStatus(List<String> evseNoList, EvseCfgEnum status) {
        if (null == status) {
            log.warn("没有指定状态值,不做作更新操作");
            return;
        }

        if (CollectionUtils.isEmpty(evseNoList)) {
            log.warn("桩编号列表为空,不做处理");
            return;
        }

        this.batchUpdate(evseNoList.stream().map(evseNo -> {
            BsBoxSettingPo po = new BsBoxSettingPo();
            po.setBoxCode(evseNo)
                    .setBoxOutFactoryCode(evseNo)
                    .setStatus(status.value);
            return po;
        }).collect(Collectors.toList()));
    }
}
