package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.dc.client.reactor.ReactorUserFeignClient;
import com.cdz360.biz.dc.service.site.SiteBizService;
import com.cdz360.biz.dc.service.site.SiteChargeJobService;
import com.cdz360.biz.ds.trading.ro.corp.ds.CorpRoDs;
import com.cdz360.biz.ds.trading.rw.sync.ds.CorpRwDs;
import com.cdz360.biz.model.cus.discount.param.ChangeCorpCommIdRmParam;
import com.cdz360.biz.utils.feign.auth.AuthCorpFeignClient;
import com.cdz360.biz.utils.feign.user.UserCorpFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

/**
 * MoveCorpService
 *
 * @since 11/3/2020 9:52 AM
 * <AUTHOR>
 */
@Slf4j
@Service
public class MoveCorpService {




    @Autowired
    private ReactorUserFeignClient reactorUserFeignClient;

    @Autowired
    private SiteBizService siteService;

    @Autowired
    private SiteChargeJobService siteChargeJobService;

//    @Autowired
//    private PublishCorpInfoService publishCorpInfoService;

    @Autowired
    private CorpRwDs corpRwDs;

    @Autowired
    private CorpRoDs corpRoDs;

    @Autowired
    private TRCommercialService trCommercialService;

//    @Autowired
//    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private AuthCorpFeignClient authCorpFeignClient;

//    @Autowired
//    private UserFeignClient userFeignClient;

    @Autowired
    private UserCorpFeignClient userCorpFeignClient;

    @Autowired
    private com.cdz360.biz.utils.feign.user.UserFeignClient monoUserFeignClient;

    public Mono<ObjectResponse<Boolean>> move(Long corpId, Long commId) {

        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");

//        return Mono.just(Boolean.TRUE)
//                .doOnNext(e -> IotAssert.isNull(e, "error"))
//                .doOnNext(e -> log.info("next"))
//                .map(e -> new ObjectResponse(e));


        return reactorUserFeignClient.userMoveCorp(corpId, commId).doOnNext(e -> {
            this.moveCorp(corpId, commId);
        }).flatMap(e -> {
            return authCorpFeignClient.moveCorp(corpId, commId)
                    .doOnNext(FeignResponseValidate::check);

            // 同步各库
//            TRCommercialPo commercialById = trCommercialService.getCommercialById(commId);
//            Long topCommId = Long.valueOf(commercialById.getIdChain().split(",")[0]);
//
//            CorpPo corpById = corpRoDs.getCorpById(corpId);
//            corpById.setCommId(commId).setTopCommId(topCommId);
//
//            corpRwDs.insertOrUpdate(corpById);

//            publishCorpInfoService.publishCorpInfo(corpId);
//            BaseResponse baseResponse = authCenterFeignClient.moveCorp(corpId, commId);
//            FeignResponseValidate.check(baseResponse);
        }).flatMap(e -> {

            return userCorpFeignClient.moveCorp(corpId, commId)
                    .doOnNext(FeignResponseValidate::check)
                    .flatMap(x -> monoUserFeignClient.changeCorpCommIdDiscountList(corpId, commId))
                    .doOnNext(FeignResponseValidate::check)
                    .map(ObjectResponse::getData)

//            Mono<ObjectResponse<Boolean>> retMono = monoUserFeignClient.changeCorpCommIdDiscountList(corpId, commId)
                    .flatMap(site -> {
//                        FeignResponseValidate.check(site);
                        if (CollectionUtils.isNotEmpty(site.getRemoveList())) {

                            ChangeCorpCommIdRmParam param = new ChangeCorpCommIdRmParam();
                            param.setCorpId(corpId).setRemoveList(site.getRemoveList());

                            log.info("待移除的协议价场站数: {}", site.getRemoveList().size());
                            return monoUserFeignClient.changeCorpCommIdRm(param);
                        } else {
                            log.info("无待移除的协议价场站");
                            return Mono.just(new ObjectResponse<>(Boolean.TRUE));
                        }
                    });

//            ObjectResponse<Integer> integerObjectResponse = userFeignClient.moveCorp(corpId, commId);
//            FeignResponseValidate.check(integerObjectResponse);
//            return retMono;
        });
    }

    // 事务可能无效
    @Transactional
    private void moveCorp(Long corpId, Long commId) {
        siteService.moveCorpSiteConfStart(corpId, commId);
        siteChargeJobService.moveCorpDetail(corpId, commId);
        siteService.moveCorpSoc(corpId, commId);
        siteService.moveCorpNoCard(corpId,commId);
    }
}