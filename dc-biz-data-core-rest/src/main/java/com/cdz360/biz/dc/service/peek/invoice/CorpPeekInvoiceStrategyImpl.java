package com.cdz360.biz.dc.service.peek.invoice;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.biz.dc.service.invoice.UpdateCorpInvoiceRecordService;
import com.cdz360.biz.dc.service.invoice.UpdateCorpInvoiceRecordStrategy;
import com.cdz360.biz.model.trading.invoice.dto.CorpInvoiceRecordDto;
import com.cdz360.biz.model.trading.peek.invoice.dto.PeekInvoiceDto;
import com.chargerlinkcar.framework.common.domain.peek.invoice.param.PeekInvoiceParam;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * CorpPeekInvoiceStrategyImpl
 *  企业-预计算发票数值
 * @since 3/22/2023 4:48 PM
 * <AUTHOR>
 */
@Slf4j
@Service
public class CorpPeekInvoiceStrategyImpl extends AbstractPeekInvoiceStrategy {

    @Autowired
    private UpdateCorpInvoiceRecordService updateCorpInvoiceRecordService;

    @Autowired
    private PeekInvoiceService peekInvoiceService;

    @PostConstruct
    public void init() {
        peekInvoiceService.addStrategy(PayAccountType.CORP, this);
    }

    @Override
    public PeekInvoiceDto peekInvoice(PeekInvoiceParam params) {
        final UpdateCorpInvoiceRecordStrategy strategy = updateCorpInvoiceRecordService.getStrategyByKey(
            params.getCorpInvoiceInfoVo()
                .getInvoiceWay());
        if(strategy != null) {
            final CorpInvoiceRecordDto corpInvoiceRecordDto = strategy.preUpdate(params, true);
            PeekInvoiceDto ret = new PeekInvoiceDto();
            ret.setActualElecFee(corpInvoiceRecordDto.getActualElecFee())
                .setActualServFee(corpInvoiceRecordDto.getActualServFee())
                .setTotalFee(corpInvoiceRecordDto.getTotalFee());
            return ret;
        }
        return null;
    }
}