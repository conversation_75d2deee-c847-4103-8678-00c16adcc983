package com.cdz360.biz.dc.domain;

import com.cdz360.base.model.geo.vo.GeoInfo;
import com.chargerlinkcar.framework.common.domain.vo.SiteInMongo;
import org.springframework.data.mongodb.core.mapping.Document;

import java.math.BigDecimal;

@Document(collection = "site_info")
public class SiteInMongoPo extends SiteInMongo {


    public SiteInMongoPo() {

    }

    public SiteInMongoPo(BigDecimal lng, BigDecimal lat) {
        if (lng != null && lat != null) {
            this.setGeoInfo(new GeoInfo().setLng(lng).setLat(lat));

            super.setPosition(new double[]{lng.doubleValue(), lat.doubleValue()});
        }
    }
}
