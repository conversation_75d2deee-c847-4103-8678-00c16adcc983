package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.CorpSettlementService;
import com.cdz360.biz.model.cus.settlement.dto.SettlementDto;
import com.cdz360.biz.model.order.param.ListSettlementOrderParam;
import com.cdz360.biz.model.order.param.NotInSettlementOrderParam;
import com.cdz360.biz.model.trading.order.param.ClearBillNoParam;
import com.cdz360.biz.model.trading.order.vo.SettlementOrderVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "企业结算相关接口")
@Slf4j
@RestController
public class CorpSettlementRest {

    @Autowired
    private CorpSettlementService corpSettlementService;

    @Operation(summary = "确认是否存在异常订单未加入到指定账单")
    @PostMapping(value = "/dataCore/order/notInSettlementOrders")
    public Mono<ObjectResponse<Integer>> notInSettlementOrders(
        ServerHttpRequest request, @RequestBody NotInSettlementOrderParam param) {
        log.info("确认是否存在异常订单未加入到指定账单: {}", JsonUtils.toJsonString(param));
        return corpSettlementService.notInSettlementOrders(param)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "获取企业客户的充电订单列表")
    @PostMapping(value = "/dataCore/order/getNotSettlementOrderList")
    public ListResponse<SettlementOrderVo> getNotSettlementOrderList(
        ServerHttpRequest request, @RequestBody ListSettlementOrderParam param) {
        log.info("获取企业客户的充电订单列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return corpSettlementService.getNotSettlementOrderList(param);
    }

    @Operation(summary = "账单删除清空充电订单中的账单编号")
    @PostMapping(value = "/dataCore/order/clearBillNo")
    public ObjectResponse<Integer> clearBillNo(
        ServerHttpRequest request, @RequestBody ClearBillNoParam param) {
        log.info("账单删除清空充电订单中的账单编号: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return RestUtils.buildObjectResponse(corpSettlementService.clearBillNo(param));
    }

    @Operation(summary = "获取企业客户的充电订单列表")
    @PostMapping(value = "/dataCore/order/getSettlementOrderList")
    public ListResponse<SettlementOrderVo> getSettlementOrderList(
        ServerHttpRequest request, @RequestBody ListSettlementOrderParam param) {
        log.info("获取企业客户的充电订单列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return corpSettlementService.getSettlementOrderList(param);
    }

    @Operation(summary = "统计账单相关订单信息")
    @PostMapping(value = "/dataCore/order/settlementOrderBi")
    public ObjectResponse<SettlementDto> settlementOrderBi(
        ServerHttpRequest request, @RequestBody ListSettlementOrderParam param) {
        log.info("统计账单相关订单信息: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return RestUtils.buildObjectResponse(corpSettlementService.settlementOrderBi(param));
    }

    /**
     * 账单结算调整
     *
     * @param billNo
     * @return
     */
    @GetMapping(value = "/dataCore/order/settlementByBillNo")
    public ObjectResponse<Integer> settlementByBillNo(
        ServerHttpRequest request,
        @RequestParam(value = "billNo") String billNo) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return RestUtils.buildObjectResponse(corpSettlementService.settlementByBillNo(billNo));
    }

//    @Operation(summary = "更新充电订单的账单编号")
//    @PostMapping(value = "/dataCore/order/updateSettlementOrderBillNo")
//    public ObjectResponse<Integer> updateSettlementOrderBillNo(
//            ServerHttpRequest request, @RequestBody UpdateSettlementOrderParam param) {
//        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = {}", JsonUtils.toJsonString(param));
//        return RestUtils.buildObjectResponse(corpSettlementService.updateSettlementOrderBillNo(param));
//    }
}
