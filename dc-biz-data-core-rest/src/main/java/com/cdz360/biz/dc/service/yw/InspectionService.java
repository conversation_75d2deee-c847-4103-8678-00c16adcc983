package com.cdz360.biz.dc.service.yw;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DateUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.auth.sys.param.AccRelativeParam;
import com.cdz360.biz.auth.sys.vo.AccRelativeVo;
import com.cdz360.biz.dc.client.AuthCenterFeignClient;
import com.cdz360.biz.dc.service.siteGroup.SiteGroupService;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteInspectionCfgRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ds.trading.ro.yw.ds.SiteInspectionRecordRoDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteInspectionCfgRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteInspectionRecordRwDs;
import com.cdz360.biz.model.FileItem;
import com.cdz360.biz.model.OldOrderImagesStruct;
import com.cdz360.biz.model.cus.message.po.MessagePo;
import com.cdz360.biz.model.cus.message.type.BroadCastType;
import com.cdz360.biz.model.cus.message.type.MsgType;
import com.cdz360.biz.model.cus.message.type.PlatformType;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.trading.iot.vo.EvseInfoVo;
import com.cdz360.biz.model.trading.site.dto.InspectionRecordDto;
import com.cdz360.biz.model.trading.site.dto.RecentInspectionRecordDto;
import com.cdz360.biz.model.trading.site.param.ChangeRecordParam;
import com.cdz360.biz.model.trading.site.param.InspectionParam;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.param.RecordParam;
import com.cdz360.biz.model.trading.site.param.SiteInspectionRecordParam;
import com.cdz360.biz.model.trading.site.po.SiteInspectionCfgPo;
import com.cdz360.biz.model.trading.site.po.SiteInspectionRecordPo;
import com.cdz360.biz.model.trading.site.type.InspectionRemindType;
import com.cdz360.biz.model.trading.site.type.SiteGcType;
import com.cdz360.biz.model.trading.site.type.SiteInspectionStatus;
import com.cdz360.biz.model.trading.site.vo.InspectionRecordBi;
import com.cdz360.biz.model.trading.site.vo.InspectionRecordVo;
import com.cdz360.biz.model.trading.site.vo.SiteInspectionRecordVo;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import com.cdz360.biz.utils.config.HlhtCfg;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.PlugNoParser;
import java.time.Duration;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class InspectionService {

    @Autowired
    private HlhtCfg hlhtCfg;
    @Autowired
    private RedisIotReadService redisIotReadService;
    @Autowired
    private SiteInspectionCfgRoDs cfgRoDs;
    @Autowired
    private SiteInspectionCfgRwDs cfgRwDs;
    @Autowired
    private SiteInspectionRecordRoDs recordRoDs;
    @Autowired
    private SiteInspectionRecordRwDs recordRwDs;
    @Autowired
    private SiteRoDs siteRoDs;
    @Autowired
    private SiteGroupService siteGroupService;
    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;
    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;

    private final Integer MIN_INSPECTION_CYCLE = 3; // 巡检周期即将到的前第三天，推送运维提醒

    /**
     * 获取最近一次巡检信息
     *
     * @param siteId
     * @return
     */
    public ObjectResponse<RecentInspectionRecordDto> getRecentRecord(String siteId) {

        RecentInspectionRecordDto dto = new RecentInspectionRecordDto();

        SiteInspectionRecordPo latestRecord = recordRoDs.getLatestRecord(siteId);
        if (latestRecord != null) {
            ListResponse<SysUserVo> sysUserVoListResponse = authCenterFeignClient
                .querySysUserByIds(List.of(latestRecord.getOpUid()));
            FeignResponseValidate.check(sysUserVoListResponse);
            Map<Long, SysUserVo> sysUserVoMap = sysUserVoListResponse.getData()
                .stream().collect(Collectors.toMap(SysUserVo::getId, o -> o));

            dto.setStatus(latestRecord.getStatus())
                .setTime(latestRecord.getReportTime());

            SysUserVo sysUserVo = sysUserVoMap.get(latestRecord.getOpUid());
            if (sysUserVo != null) {
                dto.setRummager(sysUserVo.getName());
            }

            SiteInspectionCfgPo cfgPo = cfgRoDs.getCfg(siteId);
            if (cfgPo != null && InspectionRemindType.FIXED == cfgPo.getType()) {

                LocalDate latest = latestRecord.getReportTime().toInstant()
                    .atZone(ZoneOffset.systemDefault()).toLocalDate();
//                Duration duration = Duration.between(latest, LocalDate.now());
//                if (duration.toDays() >= cfgPo.getCycle()) {
//                    dto.setDays(duration.toDays());
//                }
                dto.setDays(ChronoUnit.DAYS.between(latest, LocalDate.now()));
            }
        }
        return RestUtils.buildObjectResponse(dto);
    }

    /**
     * 获取场站巡检的配置
     *
     * @param siteId
     * @return
     */
    public ObjectResponse<SiteInspectionCfgPo> getConfig(@RequestParam("siteId") String siteId) {
        SiteInspectionCfgPo res = cfgRoDs.getCfg(siteId);
        return RestUtils.buildObjectResponse(res);
    }

    /**
     * 修改巡检配置
     *
     * @param req
     * @return
     */
    public BaseResponse editConfig(SiteInspectionCfgPo req) {
        if (req.getRemark() != null && req.getRemark().length() > 100) {
            throw new DcArgumentException("备注最多可输入100位");
        }
        if (InspectionRemindType.FIXED != req.getType()) {
            req.setCycle(null);
        }
        int i = cfgRwDs.editConfig(req);
        return i > 0 ? RestUtils.success() : RestUtils.fail(2000, "操作失败");
    }

    /**
     * 巡检记录-饼图数据
     *
     * @param param
     * @return
     */
    public ListResponse<InspectionRecordBi> getRecordBi(SiteInspectionRecordParam param) {

        List<InspectionRecordBi> res = recordRoDs.getRecordBi(param);
        if (CollectionUtils.isNotEmpty(res)) {

            ListResponse<SysUserVo> response = authCenterFeignClient.querySysUserByIds(res.stream()
                .map(InspectionRecordBi::getOpUid)
                .collect(Collectors.toList()));
            FeignResponseValidate.check(response);
            Map<Long, SysUserVo> sysUserVoMap = response.getData().stream()
                .collect(Collectors.toMap(SysUserVo::getId, o -> o));
            res.forEach(e -> {
                SysUserVo sysUserVo = sysUserVoMap.get(e.getOpUid());
                if (sysUserVo != null) {
                    e.setRummager(sysUserVo.getName());
                }
            });

        }
        return RestUtils.buildListResponse(res);
    }

    /**
     * 巡检记录-表格数据
     *
     * @param param
     * @return
     */
    public ListResponse<InspectionRecordVo> getRecordVoList(SiteInspectionRecordParam param) {

        List<InspectionRecordVo> res = recordRoDs.getRecordVoList(param);
        long total = recordRoDs.getRecordVoListCount(param);
        if (CollectionUtils.isNotEmpty(res)) {

            ListResponse<SysUserVo> response = authCenterFeignClient.querySysUserByIds(res.stream()
                .map(e -> e.getOpUid())
                .collect(Collectors.toList()));
            FeignResponseValidate.check(response);
            Map<Long, SysUserVo> sysUserVoMap = response.getData().stream()
                .collect(Collectors.toMap(SysUserVo::getId, o -> o));
            res.forEach(e -> {
                SysUserVo sysUserVo = sysUserVoMap.get(e.getOpUid());
                if (sysUserVo != null) {
                    e.setOpName(sysUserVo.getName());
                }
            });

        }
        return RestUtils.buildListResponse(res, total);
    }

    /**
     * 巡检工单
     *
     * @param param
     * @return
     */
    public ListResponse<InspectionRecordDto> getRecords(RecordParam param) {

        if (StringUtils.isNotBlank(param.getRummager())) {
            ListResponse<SysUserVo> temp = authCenterFeignClient
                .findByName(param.getRummager(), Boolean.FALSE);
            FeignResponseValidate.check(temp);
            if (CollectionUtils.isEmpty(temp.getData())) {
                return RestUtils.buildListResponse(List.of());
            }
            param.setOpUidList(temp.getData().stream()
                .map(SysUserVo::getId).collect(Collectors.toList()));
        }

        List<InspectionRecordDto> res = recordRoDs.getRecords(param);
        long total = recordRoDs.getRecordsCount(param);

        AtomicReference<Map<String, List<String>>> siteGidMapRef = new AtomicReference<>(
            new HashMap<>());
        AtomicReference<Map<String, String>> groupMapRef = new AtomicReference<>(new HashMap<>());
        List<String> siteIdList = res.stream()
            .map(InspectionRecordDto::getSiteId)
            .collect(Collectors.toList());
        siteGroupService.getSiteGroupMapVo(siteIdList)
            .block(Duration.ofSeconds(50L))
            .ifPresent(mapVo -> {
                siteGidMapRef.set(mapVo.getSiteGidMap());
                groupMapRef.set(mapVo.getGroupMap());
            });

        if (CollectionUtils.isNotEmpty(res)) {

            Set<Long> idList = new HashSet<>();
            res.forEach(e -> {
                idList.add(e.getOpUid());
                idList.add(e.getQcUid());
            });
            ListResponse<SysUserVo> response = authCenterFeignClient
                .querySysUserByIds(new ArrayList<>(idList));
            FeignResponseValidate.check(response);
            Map<Long, SysUserVo> sysUserVoMap = response.getData().stream()
                .collect(Collectors.toMap(SysUserVo::getId, o -> o));
            res.forEach(e -> {
                SysUserVo sysUserVo = sysUserVoMap.get(e.getOpUid());
                if (sysUserVo != null) {
                    e.setRummager(sysUserVo.getName());
                }
                SysUserVo sysUserVo2 = sysUserVoMap.get(e.getQcUid());
                if (sysUserVo2 != null) {
                    e.setQcUserName(sysUserVo2.getName());
                }

                // 返回场站运维组信息
                siteGidMapRef.get().getOrDefault(e.getSiteId(), List.of()).stream()
                    .filter(Objects::nonNull)
                    .filter(t -> StringUtils.isNotBlank(groupMapRef.get().get(t)))
                    .findFirst().ifPresent(s -> {
                        e.setSiteGid(s);
                        e.setSiteGidName(groupMapRef.get().get(s));
                    });
            });

        }
        return RestUtils.buildListResponse(res, total);
    }

    /**
     * 修改工单状态
     *
     * @param recordId
     * @param status
     * @return
     */
    public BaseResponse changeStatus(Long sysUserId, Long recordId, SiteInspectionStatus status) {
//        SiteInspectionRecordPo recordPo = recordRoDs.getById(recordId);
        Long qcUid = null;
        Date qcTime = null;

        if (SiteInspectionStatus.PASS == status
            || SiteInspectionStatus.FAIL == status) {
            qcUid = sysUserId;
            qcTime = new Date();
        }

        int i = recordRwDs.changeStatus(List.of(recordId), status,
            null,
            qcUid, qcTime, null);
        return i > 0 ? RestUtils.success() : RestUtils.fail(2000, "操作失败");
    }

    /**
     * 批量质检
     *
     * @param param
     * @return
     */
    public BaseResponse changeStatusBatch(ChangeRecordParam param) {
        IotAssert.isTrue(param != null
                && CollectionUtils.isNotEmpty(param.getRecordIdList()),
            "缺少入参");
        Long qcUid = null;
        Date qcTime = null;
        String qcRemark = null;
        Long score = null;
        if (SiteInspectionStatus.PASS == param.getStatus()
            || SiteInspectionStatus.FAIL == param.getStatus()) {
            qcUid = param.getSysUserId();
            qcTime = new Date();
            qcRemark = StringUtils.isNotBlank(param.getQcRemark()) ? param.getQcRemark()
                : ""; // 为保证擦除上次质检的备注
            score = SiteInspectionStatus.PASS.equals(param.getStatus()) ? param.getScore() : null;
        }

        int i = recordRwDs.changeStatus(param.getRecordIdList(), param.getStatus(),
            qcRemark,
            qcUid, qcTime, score);
        return i > 0 ? RestUtils.success() : RestUtils.fail(2000, "操作失败");
    }

    public BaseResponse recordDel(Long sysUserId, Long recordId) {
        IotAssert.isTrue(sysUserId != null && recordId != null, "缺少入参");

        SiteInspectionRecordPo recordPo = recordRoDs.getById(recordId);
        IotAssert.isNotNull(recordPo, "工单不存在");

        IotAssert.isTrue(Objects.equals(recordPo.getOpUid(), sysUserId), "您无权删除此工单");
        List<SiteInspectionStatus> statusList = List.of(SiteInspectionStatus.TO_BE_INSPECTED,
            SiteInspectionStatus.CANCEL);
        IotAssert.isTrue(statusList.contains(recordPo.getStatus()), "当前工单状态不支持删除");

        SiteInspectionRecordPo update = new SiteInspectionRecordPo();
        update.setId(recordId)
            .setStatus(SiteInspectionStatus.DELETED);
        int i = recordRwDs.editById(update);
        return i > 0 ? RestUtils.success()
            : RestUtils.fail(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "删除失败");
    }

    /**
     * 巡检详情
     *
     * @param id
     * @return
     */
    public ObjectResponse<SiteInspectionRecordVo> getDetail(Long id) {
        SiteInspectionRecordVo res = new SiteInspectionRecordVo();

        SiteInspectionRecordPo recordPo = recordRoDs.getById(id);

        ListSiteParam siteReq = new ListSiteParam();
        siteReq.setSiteIdList(List.of(recordPo.getSiteId()));
        ListResponse<SiteVo> response = siteRoDs.getSiteVoList(siteReq);
        FeignResponseValidate.check(response);
        SiteVo siteVo = response.getData().get(0);

        if (recordPo.getEvseReport() != null) {
            List<String> allEvseNo = recordPo.getEvseReport().mergeAllEvseNo();
            if (CollectionUtils.isNotEmpty(allEvseNo)) {
                ListEvseParam param = new ListEvseParam();
                param.setEvseNoList(allEvseNo);

                ListResponse<EvseInfoVo> listResponse = iotDeviceMgmFeignClient
                    .getEvseInfoList(param);
                FeignResponseValidate.check(listResponse);
                Map<String, String> evseMap = listResponse.getData().stream()
                    .collect(Collectors.toMap(EvseVo::getEvseNo, EvseVo::getName));
                recordPo.getEvseReport().fillEvseName(evseMap);
            }
        }
        BeanUtils.copyProperties(recordPo, res);

        List<Long> sysUserIds = new ArrayList<>();
        if (res.getOpUid() != null) {
            sysUserIds.add(res.getOpUid());
        }
        if (res.getQcUid() != null) {
            sysUserIds.add(res.getQcUid());
        }
        if (CollectionUtils.isNotEmpty(sysUserIds)) {
            ListResponse<SysUserVo> voListResponse = authCenterFeignClient
                .querySysUserByIds(sysUserIds);
            FeignResponseValidate.check(voListResponse);
            Map<Long, String> sysUserVoMap = voListResponse.getData().stream()
                .collect(Collectors.toMap(SysUserVo::getId,
                    SysUserVo::getName));
            if (sysUserVoMap.containsKey(res.getOpUid())) {
                res.setOpName(sysUserVoMap.get(res.getOpUid()));
            }
            if (sysUserVoMap.containsKey(res.getQcUid())) {
                res.setQcName(sysUserVoMap.get(res.getQcUid()));
            }
        }

        ObjectResponse<Date> dateRes = iotDeviceMgmFeignClient.getExpireDate(recordPo.getSiteId());
        FeignResponseValidate.checkIgnoreData(dateRes);

        res.setSiteName(siteVo.getSiteName())
            .setProvinceName(siteVo.getProvinceName())
            .setCityName(siteVo.getCityName())
            .setAreaName(siteVo.getAreaName())
            .setAddress(siteVo.getAddress())
            .setLongitude(siteVo.getLongitude())
            .setLatitude(siteVo.getLatitude())
            .setExpireDate(dateRes.getData());

        return RestUtils.buildObjectResponse(res);
    }

//    /**
//     * 导出巡检详情
//     * @param id
//     * @return
//     */
//    public ObjectResponse<ExcelPosition> recordExport(Long id) throws IOException, DocumentException {
//        log.info("recordExport id: {}", id);
//        ExcelPosition position = new ExcelPosition();
//        position.setSubFileName(UUIDUtils.getUuid32());
//        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
//
//        this.recordExport(id, position);
//        return RestUtils.buildObjectResponse(position);
//    }
//
//    private void recordExport(Long id, ExcelPosition position) throws IOException, DocumentException {
//        log.info("res: {}", System.getProperty("user.dir"));
//        log.info("res: {}", System.getProperty("user.home"));
//        final String HTML = "siteInspectionDetail.html"; // 此文件存于OSS，且OSS文件名称需为siteInspectionDetail_en.html（freemarker自动追加“_en”）
//
//        SiteInspectionRecordPo recordPo = recordRoDs.getById(id);
//        SitePo sitePo = siteRoDs.getSite(recordPo.getSiteId());
//
//        Map<Long, SysUserVo> sysUserVoMap = new HashMap<>();
//        List<Long> ids = new ArrayList<>();
//        if (recordPo.getOpUid() != null) {
//            ids.add(recordPo.getOpUid());
//        }
//        if (recordPo.getQcUid() != null) {
//            ids.add(recordPo.getQcUid());
//        }
//        if (CollectionUtils.isNotEmpty(ids)) {
//            ListResponse<SysUserVo> response = authCenterFeignClient.querySysUserByIds(ids);
//            FeignResponseValidate.check(response);
//            sysUserVoMap = response.getData().stream().collect(Collectors.toMap(SysUserVo::getId, o -> o));
//        }
//
//        ListEvseParam param = new ListEvseParam();
//        param.setSiteIdList(List.of(sitePo.getId()));
//        ListResponse<EvseInfoVo> listResponse = iotDeviceMgmFeignClient.getEvseInfoList(param);
//        FeignResponseValidate.checkIgnoreData(listResponse);
//        List<EvseInfoVo> evseInfoVoList = listResponse.getData();
//
//        Map<String, Object> data = new HashMap();
//
//        data.put("siteName", sitePo.getSiteName());
//        data.put("siteAddress", sitePo.getAddress());
//        data.put("siteTypeDesc", SiteGcType.valueOf(sitePo.getGcType()).getDesc());
//        data.put("No", recordPo.getNo());
//        SysUserVo opUser = sysUserVoMap.get(recordPo.getOpUid());
//        data.put("opName", opUser != null ? opUser.getName() : "");
//        data.put("reportTime", new Date());
//        SysUserVo qcUser = sysUserVoMap.get(recordPo.getQcUid());
//        data.put("qcName", qcUser != null ? qcUser.getName() : "");
//        data.put("evseReport", recordPo.getEvseReport());
//        data.put("envReport", recordPo.getEnvReport());
//        data.put("photos", recordPo.getPhotos());
//        data.put("dcEvseNum", sitePo.getDcEvseNum());
//        data.put("acEvseNum", sitePo.getAcEvseNum());
//
//        data.put("evseList", evseInfoVoList);
//
//        String content = HtmlToPdfFreeMarkerUtil.freeMarkerRender(data, HTML);
//        HtmlToPdfFreeMarkerUtil.createPdf(content, position);
//    }

    /**
     * 获取待巡检场站
     *
     * @param param
     * @return
     */
    public ListResponse<RecentInspectionRecordDto> getNeedInspectionSite(InspectionParam param) {
        if (param.getStart() == null) {
            param.setStart(0L);
        }
        if (param.getSize() == null) {
            param.setSize(10);
        }

//        List<SiteInspectionCfgPo> cfgPoList = cfgRoDs.getNeedInspectionSite(commIdChain, param.getStart(), param.getSize());
//        Map<String, SiteInspectionCfgPo> cfgPoMap = cfgPoList.stream()
//                .collect(Collectors.toMap(SiteInspectionCfgPo::getSiteId, o -> o));
//        Long total = cfgRoDs.getNeedInspectionSiteCount(commIdChain);

        List<RecentInspectionRecordDto> res = recordRoDs
            .getNeedInspectionSite(MIN_INSPECTION_CYCLE, param);
        Long total = recordRoDs.getNeedInspectionSiteCount(MIN_INSPECTION_CYCLE, param);

        return RestUtils.buildListResponse(res, total);
    }

    /**
     * 巡检提醒
     */
    public void siteInspectionReminder() {
        log.info("siteInspectionReminder start");
        List<SiteInspectionCfgPo> cfgPoList = cfgRoDs.getNeedRemindSite();
        List<String> siteIdList = cfgPoList.stream().map(SiteInspectionCfgPo::getSiteId)
            .collect(Collectors.toList());
        Map<String, SiteInspectionCfgPo> map = cfgPoList.stream()
            .collect(Collectors.toMap(SiteInspectionCfgPo::getSiteId, o -> o));
        AccRelativeParam param = new AccRelativeParam();
        param.setSiteIdList(siteIdList);
        ListResponse<AccRelativeVo> relativeResponse = authCenterFeignClient.getVoList(param);
        FeignResponseValidate.check(relativeResponse);

        List<AccRelativeVo> res = relativeResponse.getData();
        Map<String, List<Long>> siteMap = new HashMap<>();
        res.forEach(e -> {
            e.getSiteIdList().forEach(siteId -> {
                List<Long> sysUidList = new ArrayList<>();
                if (siteMap.containsKey(siteId)) {
                    sysUidList = siteMap.get(siteId);
                    sysUidList.add(e.getSysUid());
                } else {
                    sysUidList.add(e.getSysUid());
                }
                siteMap.put(siteId, sysUidList);
            });
        });

        List<RecentInspectionRecordDto> recordDtos = recordRoDs.getNeedReminderSite(siteIdList);
        recordDtos.forEach(e -> {
            SiteInspectionCfgPo cfgPo = map.get(e.getSiteId());
            if (cfgPo != null) {
                LocalDate recently = null;
                if (e.getTime() == null) {
                    Date time = cfgPo.getUpdateTime() != null ? cfgPo.getUpdateTime()
                        : cfgPo.getCreateTime();
                    recently = time.toInstant().atZone(ZoneOffset.systemDefault()).toLocalDate();
                } else {
                    recently = e.getTime().toInstant().atZone(ZoneOffset.systemDefault())
                        .toLocalDate();
                }
                if (recently.plusDays(cfgPo.getCycle() - MIN_INSPECTION_CYCLE)
                    .isEqual(LocalDate.now())) {
                    // 推送消息
                    MessagePo message = new MessagePo();
                    message.setTitle(e.getSiteName())
                        .setContent("该场站即将到巡检周期，请及时去场站巡检")
                        .setMsgType(MsgType.INSPECTION_REMINDER)
                        .setPlatformList(List.of(PlatformType.MANAGE))
                        .setBroadcast(BroadCastType.PART)
                        .setTargetUid(siteMap.get(e.getSiteId()))
                        .setCommIdList(List.of(e.getSiteCommId()))
                        .setOpUid(0L);
                    log.info("push msg: {}", message);
                    try {
                        ObjectResponse response = authCenterFeignClient.addMessage(null, message);
                        FeignResponseValidate.check(response);
                    } catch (Exception ex) {
                        log.error("推送巡检提醒失败 siteId: {}, msg: {}", e.getSiteId(),
                            ex.getMessage(),
                            ex);
                    }
                }
            }
        });
    }

    /**
     * 获取待处理巡检工单
     *
     * @param sysUserId
     * @return
     */
    public ListResponse<SiteInspectionRecordVo> getToBeInspectRecord(Long sysUserId,
        BaseListParam param) {
        if (param.getStart() == null) {
            param.setStart(0L);
        }
        if (param.getSize() == null) {
            param.setSize(10);
        }
//        List<SiteInspectionStatus> statusList = List.of(SiteInspectionStatus.INIT, SiteInspectionStatus.FAIL);
//        List<SiteInspectionRecordVo> res = recordRoDs.queryByOpIdAndStatus(sysUserId, statusList, param.getStart(), param.getSize());
//        Long total = recordRoDs.queryByOpIdAndStatusCount(sysUserId, statusList);

        // 若场站质检通过后，不再显示之前不合格(FAIL)的巡检单
        List<SiteInspectionRecordVo> res = recordRoDs
            .getToBeInspectRecord(sysUserId, param.getStart(), param.getSize());
        Long total = recordRoDs.getToBeInspectRecordCount(sysUserId);
        return RestUtils.buildListResponse(res, total);
    }

    /**
     * 获取历史巡检工单
     *
     * @param sysUserId
     * @return
     */
    public ListResponse<SiteInspectionRecordVo> getHistoryRecord(Long sysUserId,
        BaseListParam param) {
        if (param.getStart() == null) {
            param.setStart(0L);
        }
        if (param.getSize() == null) {
            param.setSize(10);
        }
        List<SiteInspectionStatus> statusList = List.of(SiteInspectionStatus.TO_BE_INSPECTED,
            SiteInspectionStatus.PASS, SiteInspectionStatus.CANCEL);
        List<SiteInspectionRecordVo> res = recordRoDs
            .queryByOpIdAndStatus(sysUserId, statusList, param.getStart(), param.getSize(),
                param.getSk());
        Long total = recordRoDs.queryByOpIdAndStatusCount(sysUserId, statusList, param.getSk());
        return RestUtils.buildListResponse(res, total);
    }

    /**
     * 通过枪号获取场站巡检信息
     *
     * @param topCommId
     * @param qrCode
     * @param siteId
     * @return
     */
    public ObjectResponse<SiteVo> getSiteByPlugNo(Long topCommId, String plugNo,
        String qrCode, String siteId) {
        if (StringUtils.isNotBlank(plugNo) && StringUtils.isNotBlank(qrCode)) {
            PlugVo plug = null;
            Boolean thirdQrCode = false;
            if (CollectionUtils.isNotEmpty(this.hlhtCfg.getThirdQrCodeList()) && StringUtils.isNotEmpty(qrCode)) {
                for (int i = 0; i < this.hlhtCfg.getThirdQrCodeList().size(); i++) {
                    PlugNoParser plugNoParser = new PlugNoParser(this.hlhtCfg);
                    String thirdPlugNo = plugNoParser.parseThirdQrCode(topCommId, this.hlhtCfg.getThirdQrCodeList().get(i).getThirdQrCodeRegex(), this.hlhtCfg.getThirdQrCodeList().get(i).getThirdQrCodeOperatorId(), qrCode);
                    if (thirdPlugNo != null) {
                        thirdQrCode = true;
                        plug = redisIotReadService.getPlugRedisCache(thirdPlugNo);
                        break;
                    }
                }
            }
            if (!thirdQrCode) {
                if (StringUtils.startsWithIgnoreCase(qrCode, "hlht://")) {
                    PlugNoParser plugNoParser = new PlugNoParser(this.hlhtCfg);
                    plugNo = plugNoParser.parseHlhtQrCode(topCommId, qrCode);
                    plug = redisIotReadService.getPlugRedisCache(plugNo);
                } else {

                    PlugNoParser plugNoParser = new PlugNoParser(plugNo);
                    plug = redisIotReadService.getPlugRedisCache(
                        plugNoParser.getEvseNo(), plugNoParser.getPlugNo());
                }
            }
            if (plug != null) {
                siteId = plug.getSiteId();
            }
        }

        ListSiteParam param = new ListSiteParam();
        param.setSiteIdList(List.of(siteId));
        ListResponse<SiteVo> siteResponse = siteRoDs.getSiteVoList(param);
        FeignResponseValidate.checkIgnoreData(siteResponse);
        if (CollectionUtils.isEmpty(siteResponse.getData())) {
            throw new DcServiceException("无法获取场站信息");
        }
        SiteVo res = siteResponse.getData().get(0);
        res.setGcTypeDesc(SiteGcType.valueOf(res.getGcType()).getDesc());

        ObjectResponse<Date> response = iotDeviceMgmFeignClient.getExpireDate(siteId);
        FeignResponseValidate.checkIgnoreData(response);
        res.setExpireDate(response.getData());

        SiteInspectionRecordPo latestRecord = recordRoDs.getLatestRecord(siteId);
        if (latestRecord != null) {
            res.setRecentInspection(latestRecord.getReportTime());
        }

        return RestUtils.buildObjectResponse(res);
    }

    /**
     * 创建巡检单
     *
     * @param sysUserId
     * @param siteId
     * @param inspectionType
     * @return
     */
    public ObjectResponse<SiteInspectionRecordPo> create(Long sysUserId, String siteId,
        Integer inspectionType) {
        Long idx = recordRoDs.getNewInspectionNoIdx();
        String newNo = String
            .format("%s%04d", DateUtils.toStringFormat(new Date(), "yyyyMMdd"), idx);
        SiteInspectionRecordPo req = new SiteInspectionRecordPo();
        req.setNo(newNo)
            .setSiteId(siteId)
            .setOpUid(sysUserId)
            .setInspectionType(inspectionType);
        int i = recordRwDs.create(req);
        if (req.getId() == null) {
            log.info("巡检工单创建失败, req={}", JsonUtils.toJsonString(req));
            return RestUtils.serverBusy4ObjectResponse();
        }
//        SiteInspectionRecordPo res = recordRoDs.getById(req.getId());
//        return RestUtils.buildObjectResponse(res);
        return RestUtils.buildObjectResponse(req);
    }

    /**
     * 是否存在未结束的巡检单
     */
    public ObjectResponse<Boolean> existUnfinishedRecord(List<Long> sysUidList) {
        if (CollectionUtils.isEmpty(sysUidList)) {
            throw new DcServiceException("sysUidList不能为空");
        }
        RecordParam param = new RecordParam();
        param.setOpUidList(sysUidList)
            .setStatusList(List.of(SiteInspectionStatus.INIT, SiteInspectionStatus.FAIL));
        long recordsCount = recordRoDs.getRecordsCount(param);
        return RestUtils.buildObjectResponse(recordsCount > 0);
    }

    /**
     * 保存巡检单
     *
     * @param req
     * @return
     */
    public BaseResponse save(SiteInspectionRecordPo req) {
        if (null != req.getRemark() && req.getRemark().length() > 100) {
            throw new DcServiceException("巡检单备注信息不能超过100个字");
        }
        int i = recordRwDs.editById(req);
        return i > 0 ? RestUtils.success() : RestUtils.fail(2000, "保存失败");
    }

    /**
     * 提交巡检单
     *
     * @param req
     * @return
     */
    public BaseResponse report(SiteInspectionRecordPo req) {
        if (null != req.getRemark() && req.getRemark().length() > 100) {
            throw new DcServiceException("巡检单备注信息不能超过100个字");
        }
        SiteInspectionRecordPo recordInfo = recordRoDs.getById(req.getId());
        IotAssert.isNotNull(recordInfo, "工单信息不存在");

        int i = 0;
        // 已经完成的工单只能修改 备注信息  签名 图片 建议
        if (SiteInspectionStatus.PASS.equals(recordInfo.getStatus())) {
            recordInfo.setRemark(req.getRemark())
                .setSignImage(req.getSignImage())
                .setAdvice(req.getAdvice())
                .setIsPerfect(req.getIsPerfect())
                .setPhotos(req.getPhotos())
                .setReportTime(new Date());
            i = recordRwDs.editById(recordInfo);
        } else {
            req.setStatus(SiteInspectionStatus.TO_BE_INSPECTED)
                .setReportTime(new Date());
            i = recordRwDs.editById(req);
        }
        return i > 0 ? RestUtils.success() : RestUtils.fail(2000, "提交失败");
    }


    public ObjectResponse<SiteInspectionRecordPo> transInspection(Long recId, Long opUid) {
        SiteInspectionRecordPo recordPo = recordRoDs.getById(recId);
        if (null == recordPo) {
            throw new DcServiceException("巡检记录无效");
        }

        recordPo.setOpUid(opUid)
            .setStatus(SiteInspectionStatus.INIT);
        recordRwDs.editById(recordPo);

        return RestUtils.buildObjectResponse(recordPo);
    }

    @Deprecated(since = "20230309")
    public Mono<BaseResponse> inspectionOrderFixImages() {
        int size = 100;
        int count = 0;
        while (count++ < 100) {
            List<OldOrderImagesStruct> result = recordRoDs.oldInspectionOrder(size);
            if (CollectionUtils.isEmpty(result)) {
                break;
            }
            log.info("需要处理个数: {}", result.size());
            result.stream()
                .map(x -> new SiteInspectionRecordPo()
                    .setId(Long.parseLong(x.getOrderNo()))
                    .setPhotos(x.getImages()
                        .stream().map(k -> new FileItem().setUrl(k))
                        .collect(Collectors.toList())))
                .forEach(order -> {
                    int b = recordRwDs.editById(order);
                    log.info("更新订单: {}, {}", order, b);
                });
        }
        return Mono.just(RestUtils.success());
    }
}
