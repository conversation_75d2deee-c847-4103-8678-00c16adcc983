package com.cdz360.biz.dc.service.invoice;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.client.UserFeignClient;
import com.cdz360.biz.dc.domain.vo.InvoicingDataProcessVo;
import com.cdz360.biz.dc.service.PayBillBizService;
import com.cdz360.biz.ds.trading.ro.invoice.ds.InvoiceRecordOrderRefRoDs;
import com.cdz360.biz.ds.trading.ro.invoice.ds.InvoicedRecordBillRefRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderPayRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.PayBillRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.InvoiceRecordRoDs;
import com.cdz360.biz.ds.trading.rw.invoice.ds.InvoicedRecordBillRefRwDs;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderPayRwDs;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderRwDs;
import com.cdz360.biz.ds.trading.rw.order.ds.PayBillRwDs;
import com.cdz360.biz.model.cus.settlement.param.UpdateSettlementInvoicedAmountParam;
import com.cdz360.biz.model.trading.invoice.dto.InvoicedRecordDto;
import com.cdz360.biz.model.trading.invoice.param.ListInvoiceRecordOrderRefParam;
import com.cdz360.biz.model.trading.invoice.po.InvoiceRecordOrderRefPo;
import com.cdz360.biz.model.trading.invoice.po.InvoicedRecordBillRefPo;
import com.cdz360.biz.model.trading.order.dto.PayBillInvoiceBi;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.model.trading.order.param.UpdateOrderInvoicedAmountParam;
import com.cdz360.biz.model.trading.order.param.UpdateOrderInvoicedAmountParam.OpType;
import com.cdz360.biz.model.trading.order.po.ChargerOrderPayPo;
import com.cdz360.biz.model.trading.order.vo.PayBillLinkChargeOrderVo;
import com.cdz360.biz.model.trading.order.vo.PayBillUsedDetail;
import com.chargerlinkcar.framework.common.utils.OptionalUtils;
import com.google.common.collect.Lists;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class InvoiceProcess {

    @Autowired
    private ChargerOrderRwDs chargerOrderRwDs;

    @Autowired
    private ChargerOrderPayRoDs chargerOrderPayRoDs;

    @Autowired
    private ChargerOrderPayRwDs chargerOrderPayRwDs;

    @Autowired
    private InvoiceRecordRoDs invoiceRecordRoDs;

    @Autowired
    private InvoiceRecordOrderRefRoDs invoiceRecordOrderRefRoDs;

    @Autowired
    private InvoicedRecordBillRefRoDs invoicedRecordBillRefRoDs;

    @Autowired
    private InvoicedRecordBillRefRwDs invoicedRecordBillRefRwDs;

    @Autowired
    private PayBillRoDs payBillRoDs;

    @Autowired
    private PayBillRwDs payBillRwDs;

    @Autowired
    private PayBillBizService payBillService;

    @Autowired
    private UserFeignClient userFeignClient;

    public void settlementInvoicedAmount(boolean append, @NonNull String applyNo,
        List<String> billNoList) {
        // 账单相关的充电订单，不考虑开票金额
//        // 充电订单处理
//        int i = chargerOrderRwDs.updateOrderInvoicedAmountByBillNo(append, billNoList);

        if (CollectionUtils.isNotEmpty(billNoList)) {
            // 账单开票金额: t_settlement.已开票金额
            UpdateSettlementInvoicedAmountParam updateParam = new UpdateSettlementInvoicedAmountParam();
            updateParam
                .setOpType(append ?
                    UpdateSettlementInvoicedAmountParam.OpType.FIXED :
                    UpdateSettlementInvoicedAmountParam.OpType.ROLL_BACK)
                .setApplyNo(applyNo)
                .setBillNoList(billNoList);
            BaseResponse res = userFeignClient.updateSettlementInvoicedAmount(updateParam);
        }
    }

    /**
     * 根据充电订单号，对t_pay_bill表和order_pay表开票相关金额进行处理
     *
     * @param append      增加或减少
     * @param orderNoList 充电订单号列表
     * @param invoiceId   invoiced_record.id
     */
    public void chargerOrderInvoicedAmount(@NonNull boolean append,
        @NonNull List<String> orderNoList,
        @Nullable Long invoiceId) {
        this.chargerOrderInvoicedAmount(append, orderNoList, invoiceId, null);
    }

    /**
     * 根据充电订单号，对t_pay_bill表和t_charger_order_pay表开票相关金额进行处理（根据申请类型，可能还会操作t_invoiced_record_bill_ref表）
     *
     * @param append                 增加或减少
     * @param orderNoList            充电订单号列表
     * @param invoiceId              invoiced_record.id
     * @param oaProcessDefinitionKey OA流程定义KEY
     */
    @Transactional
    public void chargerOrderInvoicedAmount(@NonNull boolean append,
        @NonNull List<String> orderNoList,
        @Nullable Long invoiceId,
        @Nullable String oaProcessDefinitionKey) {
        log.info("chargerOrderInvoicedAmount start. "
                + "append = {}, orderNoList = {}, invoiceId = {}, oaProcessDefinitionKey = {}",
            append, orderNoList, invoiceId, oaProcessDefinitionKey);

        // STEP1.组装需要用到的Map
        InvoicingDataProcessVo vo = this.assembleMaps(append, orderNoList, invoiceId);

        // STEP2.调整t_pay_bill表和t_invoiced_record_bill_ref表
        final Map<String, BigDecimal> orderNo2ActualInvoiceAmountMap = this.payBillAndRecordBillRefProcess(
            append, orderNoList, invoiceId, vo);

        // STEP3.充电记录相关开票金额处理，并记录开票记录ID值（即t_charger_order表和t_charger_order_pay表）
        this.orderPayProcess(append, orderNoList, invoiceId, vo, orderNo2ActualInvoiceAmountMap);

    }

    /**
     * 组装需要用到的Map
     *
     * @param append
     * @param orderNoList
     * @param invoiceId
     * @return
     */
    public InvoicingDataProcessVo assembleMaps(boolean append,
        @NonNull List<String> orderNoList,
        @Nullable Long invoiceId) {

        AtomicReference<Optional<Map<String, ChargerOrderPayPo>>> orderPayMapRef = new AtomicReference<>(
            Optional.empty()); // <充电单，原始订单开票数据>

        // append为false时使用
        AtomicReference<Map<String, BigDecimal>> corpOrderInvoiceMapRef = new AtomicReference<>(
            new HashMap<>()); // <充电单，可开票金额>
//        AtomicReference<Map<String, BigDecimal>> czInvoiceMapMapRef = new AtomicReference<>(
//            new HashMap<>()); // <充值单，可开票金额>
        AtomicReference<Map<String, Map<String, BigDecimal>>> orderInvoiceMapRef2 = new AtomicReference<>(
            new HashMap<>()); // <充电单号，<充电单开票金额，对应充值单的开票金额>>

        /**
         * 是否为企业开票申请
         * 企业的开票关联表：t_invoice_record_order_ref
         * 非企业的开票关联表：t_invoiced_record_bill_ref
         */
        AtomicReference<Boolean> isCorpInvoicingApplyRef = new AtomicReference<>(
            null); // TODO: 2023/4/27  invoiceId为空怎么办？

        if (invoiceId != null) {
            InvoicedRecordDto recordDto = invoiceRecordRoDs.getById(invoiceId);
            if (recordDto == null) {
                log.info("recordDto不存在，invoiceId：{}", invoiceId);
                return new InvoicingDataProcessVo();
            }
            isCorpInvoicingApplyRef.set(StringUtils.isNotBlank(recordDto.getApplyNo()));

            if (append) {
                if (isCorpInvoicingApplyRef.get()) {
                    // 企业开票申请，之前已经建立了关联关系
                } else {
                    // 个人或商户开票申请，要依赖order_pay表信息来创建t_invoiced_record_bill_ref表记录
                    orderPayMapRef.set(OptionalUtils.ofEmptyListAble(orderNoList)
                        .flatMap(e -> {
                            return OptionalUtils.ofEmptyListAble(
                                    chargerOrderPayRoDs.getOrderInvoiceInfos(orderNoList))
                                .map(poList -> poList.stream()
                                    .collect(Collectors.toMap(ChargerOrderPayPo::getOrderNo,
                                        o -> o)));
                        }));
                }
            } else {

                if (isCorpInvoicingApplyRef.get()) {
                    // 企业开票申请
                    List<InvoiceRecordOrderRefPo> refPos = invoiceRecordOrderRefRoDs.findList(
                        new ListInvoiceRecordOrderRefParam().setApplyNo(recordDto.getApplyNo()));

                    OptionalUtils.ofEmptyListAble(refPos)
                        .ifPresent(e -> {
                            corpOrderInvoiceMapRef.set(e.stream()
                                .collect(Collectors.toMap(InvoiceRecordOrderRefPo::getOrderNo,
                                    InvoiceRecordOrderRefPo::getInvoiceAmount,
                                    (v1, v2) -> v2)));
                            log.info("corpOrderInvoiceMapRef.keySet: {} ",
                                corpOrderInvoiceMapRef.get().keySet());
                        });
                } else {
                    // 个人或商户开票申请
                    List<InvoicedRecordBillRefPo> refPos = invoicedRecordBillRefRoDs.getByRecordId(
                        invoiceId);
                    OptionalUtils.ofEmptyListAble(refPos)
                        .ifPresent(e -> {
                            InvoicingMode invoicingMode = e.stream()
                                .map(InvoicedRecordBillRefPo::getType).filter(Objects::nonNull)
                                .findFirst().orElse(InvoicingMode.POST_CHARGER);

//                            corpOrderInvoiceMapRef.set(e.stream()
//                                .collect(Collectors.toMap(InvoicedRecordBillRefPo::getOrderNo,
//                                    InvoicedRecordBillRefPo::getOrderInvoiceAmount, (v1, v2) -> {
//                                        if (InvoicingMode.PRE_PAY.equals(invoicingMode)) {
//                                            return v1.add(v2);
//                                        } else if (InvoicingMode.POST_CHARGER.equals(
//                                            invoicingMode)) {
//                                            return v2;
//                                        }
//                                        return null;
//                                    })));

                            e.stream().filter(s -> StringUtils.isNotBlank(s.getOrderNo()))
                                .collect(Collectors.groupingBy(InvoicedRecordBillRefPo::getOrderNo))
                                .forEach((k, v) -> orderInvoiceMapRef2.get().put(k, v.stream()
                                    .collect(Collectors.toMap(InvoicedRecordBillRefPo::getCzOrderId,
                                        InvoicedRecordBillRefPo::getCzInvoiceAmount))));

//                            czInvoiceMapMapRef.set(e.stream()
//                                .collect(Collectors.toMap(InvoicedRecordBillRefPo::getCzOrderId,
//                                    InvoicedRecordBillRefPo::getCzInvoiceAmount, (v1, v2) -> {
//                                        if (InvoicingMode.PRE_PAY.equals(invoicingMode)) {
//                                            return v2;
//                                        } else if (InvoicingMode.POST_CHARGER.equals(
//                                            invoicingMode)) {
//                                            return v1.add(v2);
//                                        }
//                                        return null;
//                                    })));
                            log.info("orderInvoiceMapRef2.keySet: {} ",
                                orderInvoiceMapRef2.get().keySet());
                        });
                }
            }
        }

        InvoicingDataProcessVo vo = new InvoicingDataProcessVo();
        vo.setOrderPayMapOpt(orderPayMapRef.get())
            .setCorpOrderInvoiceMap(corpOrderInvoiceMapRef.get())
//            .setCzInvoiceMapMapRef(czInvoiceMapMapRef.get())
            .setOrderInvoiceMapRef2(orderInvoiceMapRef2.get())
            .setIsCorpInvoicingApplyRef(isCorpInvoicingApplyRef.get());
        return vo;
    }

    /**
     * 1、调整相关充值记录的已开票金额（即t_pay_bill表） 2、操作非企业的开票关联表（即t_invoiced_record_bill_ref表）
     *
     * @param append
     * @param orderNoList
     * @param invoiceId
     * @param vo
     * @return {@code <充电单，可开票实际消费金额>}
     */
    public Map<String, BigDecimal> payBillAndRecordBillRefProcess(boolean append,
        @NonNull List<String> orderNoList,
        @Nullable Long invoiceId,
        InvoicingDataProcessVo vo) {

        if (CollectionUtils.isEmpty(orderNoList)) {
            return new HashMap<>();
        }

        final Boolean isCorpInvoicingApply = vo.getIsCorpInvoicingApplyRef(); // 是否为企业开票申请

        // append为true时使用
        final Map<String, BigDecimal> orderNo2ActualInvoiceAmountMap = new HashMap<>(); // <充电单，可开票实际消费金额>
        final Optional<Map<String, ChargerOrderPayPo>> orderPayMapOpt = vo.getOrderPayMapOpt(); // <充电单，原始订单开票数据>

        // append为false时使用
        final Map<String, BigDecimal> corpOrderNo2InvoiceAmountMap = Optional.ofNullable(
            vo.getCorpOrderInvoiceMap()).orElse(new HashMap<>()); // <充电单，可开票金额>
//        final Map<String, BigDecimal> czOrderId2InvoiceAmountMap = Optional.ofNullable(
//            vo.getCzInvoiceMapMapRef()).orElse(new HashMap<>()); // <充值单，可开票金额>
        final Map<String, Map<String, BigDecimal>> orderInvoiceMap2 = Optional.ofNullable(
            vo.getOrderInvoiceMapRef2()).orElse(new HashMap<>()); // <充电单号，<关联充值单号，充值单开票金额>>

        orderNoList.forEach(orderNo -> {
            List<PayBillLinkChargeOrderVo> orderList = payBillService.orderPointRecLog(orderNo,
                false // 忽略开票状态
            );
            if (CollectionUtils.isNotEmpty(orderList)) {

                AtomicBoolean isCorpOrderFullyInvoicedRef = new AtomicBoolean(); // 企业订单是否为金额全部开票
                if (!append && BooleanUtils.isTrue(isCorpInvoicingApply)) {
                    BigDecimal totalCostFee = orderList.stream()
                        .map(PayBillLinkChargeOrderVo::getAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal invoiceAmount = corpOrderNo2InvoiceAmountMap.get(orderNo);
                    isCorpOrderFullyInvoicedRef.set(DecimalUtils.eq(invoiceAmount, totalCostFee));
                }

                List<UpdateOrderInvoicedAmountParam> updateList = orderList.stream().map(order -> {
                    AtomicReference<BigDecimal> amountRef = new AtomicReference<>(null);
                    Optional<BigDecimal> opt = Optional.ofNullable(
                        order.getPayBillOrderInvoiceAmount()).filter(DecimalUtils::gtZero);

                    if (append && opt.isPresent()) {
                        amountRef.set(opt.get());
                    } else if (!append) {

                        if (BooleanUtils.isTrue(isCorpInvoicingApply)) {

                            if (BooleanUtils.isTrue(isCorpOrderFullyInvoicedRef.get())) {
                                amountRef.set(order.getAmount());
                            } else if (BooleanUtils.isFalse(isCorpOrderFullyInvoicedRef.get())) {
                                // 订单非全部金额开票时，企业开票申请要用关联表记录的“订单可开票金额”和对应资金块的“实际金额”比对，才能找到要减少的充值单号
                                BigDecimal mapInvoiceAmount = corpOrderNo2InvoiceAmountMap.get(
                                    orderNo);
                                if (DecimalUtils.eq(mapInvoiceAmount, order.getAmount())) {
                                    amountRef.set(mapInvoiceAmount);
                                }
                            } else {
                                // 不匹配时，认为没有用这笔充值单来开票
                                return null;
                            }

                        } else {
                            var tempMap = orderInvoiceMap2.get(orderNo);
                            if (tempMap != null && tempMap.get(order.getOrderId()) != null) {
                                amountRef.set(tempMap.get(order.getOrderId()));
                            } else {
                                // 认为没有用这笔充值单来开票
                                log.info("查询订单对应的充值单失败, orderNo= {}, order= {}",
                                    orderNo, JsonUtils.toJsonString(order));
                                return null;
                            }
                        }
                    } else {
                        log.info("充值单 payBillOrderInvoiceAmount 可开票金额小于等于0, orderNo= {}, order= {}",
                            orderNo, JsonUtils.toJsonString(order));
                        return null;
                    }
                    log.info("orderId: {}, amountRef: {}", order.getOrderId(), amountRef.get());
                    if (amountRef.get() == null) {
                        return null;
                    }
                    UpdateOrderInvoicedAmountParam amountParam = new UpdateOrderInvoicedAmountParam();
                    amountParam.setOpType(append ?
                            UpdateOrderInvoicedAmountParam.OpType.ADD
                            : UpdateOrderInvoicedAmountParam.OpType.SUB)
                        .setBeforeInvoicedAmount(order.getPayBillInvoicedAmount())
                        .setAmount(amountRef.get())
                        .setOrderNo(order.getOrderId());
                    return amountParam;
                }).filter(Objects::nonNull).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(updateList)) {
                    // 充值记录处理，分批操作
                    Lists.partition(updateList, 100)
                        .forEach(payBillRwDs::updateOrderInvoicedAmount);

                    orderNo2ActualInvoiceAmountMap.put(orderNo, updateList.stream()
                        .map(UpdateOrderInvoicedAmountParam::getAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                } else {
                    log.info("updateList is empty");
                }
                // 操作t_invoiced_record_bill_ref表
                if (BooleanUtils.isNotTrue(isCorpInvoicingApply)) {
                    if (append) {
                        final List<PayBillLinkChargeOrderVo> finalOrderList = orderList;
                        orderPayMapOpt.ifPresent(map -> {
                            final var payPo = map.getOrDefault(orderNo,
                                new ChargerOrderPayPo()
                                    .setInvoiceAmount(BigDecimal.ZERO)
                                    .setInvoicedAmount(BigDecimal.ZERO));
                            final var orderInvoiceAmount = orderNo2ActualInvoiceAmountMap.get(
                                orderNo);

                            List<InvoicedRecordBillRefPo> recordBillRefPos = finalOrderList.stream()
                                .map(order -> {
                                    Optional<BigDecimal> opt = Optional.ofNullable(
                                            order.getPayBillOrderInvoiceAmount())
                                        .filter(DecimalUtils::gtZero);
                                    if (orderInvoiceAmount == null || opt.isEmpty()) {
                                        return null;
                                    }
                                    InvoicedRecordBillRefPo refPo = new InvoicedRecordBillRefPo();
                                    refPo.setRecordId(invoiceId)
                                        .setType(InvoicingMode.POST_CHARGER)
                                        .setCzOrderId(order.getOrderId())
                                        .setCzBeforeAmount(order.getPayBillInvoicedAmount())
                                        .setCzInvoiceAmount(opt.get())
                                        .setOrderNo(orderNo)
                                        .setOrderBeforeInvoiceAmount(payPo.getInvoiceAmount())
                                        .setOrderBeforeInvoicedAmount(payPo.getInvoicedAmount())
                                        .setOrderInvoiceAmount(orderInvoiceAmount);
                                    return refPo;
                                }).filter(Objects::nonNull).collect(Collectors.toList());
                            if (CollectionUtils.isNotEmpty(recordBillRefPos)) {
                                // 开票记录和账单关联表处理，分批操作
                                Lists.partition(recordBillRefPos, 100)
                                    .forEach(invoicedRecordBillRefRwDs::batchInsert);
                            } else {
                                log.info("recordBillRefPos is empty");
                            }

                        });
                    } else {
                        invoicedRecordBillRefRwDs.disableByTypeAndOrderNo(
                            InvoicingMode.POST_CHARGER,
                            orderNo, invoiceId);
                    }
                }

            } else {
                log.info("orderList is empty");
            }
        });
        return orderNo2ActualInvoiceAmountMap;
    }

    /**
     * 充电记录相关开票金额处理，并记录开票记录ID值（即t_charger_order_pay表）
     *
     * @param append
     * @param orderNoList
     * @param invoiceId
     * @param vo
     * @param orderNo2ActualInvoiceAmountMap {@code <充电单，实际开票金额>}
     */
    public void orderPayProcess(boolean append,
        @NonNull List<String> orderNoList,
        @Nullable Long invoiceId,
        InvoicingDataProcessVo vo,
        final Map<String, BigDecimal> orderNo2ActualInvoiceAmountMap) {

        if (orderNo2ActualInvoiceAmountMap.size() == 0) {
            return;
        }

//        final Map<String, BigDecimal> orderNo2InvoiceAmountMap = Optional.ofNullable(
//            vo.getOrderInvoiceMapRef()).orElse(new HashMap<>()); // <充电单，可开票金额>
//        final Map<String, Map<String, BigDecimal>> orderInvoiceMap2 = Optional.ofNullable(
//            vo.getOrderInvoiceMapRef2()).orElse(new HashMap<>()); // <充电单号，<关联充值单号，充值单开票金额>>

        List<UpdateOrderInvoicedAmountParam> updateList = orderNoList.stream().map(orderNo -> {
            AtomicReference<BigDecimal> amountRef = new AtomicReference<>(
                orderNo2ActualInvoiceAmountMap.get(orderNo));
            if (amountRef.get() == null) {
                return null;
            }
            UpdateOrderInvoicedAmountParam amountParam = new UpdateOrderInvoicedAmountParam();
            amountParam.setOpType(append ?
                    UpdateOrderInvoicedAmountParam.OpType.ADD
                    : UpdateOrderInvoicedAmountParam.OpType.SUB)
                .setOrderNo(orderNo)
                .setAmount(amountRef.get())
                .setInvoiceId(invoiceId);
            return amountParam;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        this.orderInvoiceDataProcess(updateList);

    }

    /**
     * order表和order_pay表发票相关数据字段处理
     *
     * @param updateList
     */
    public void orderInvoiceDataProcess(List<UpdateOrderInvoicedAmountParam> updateList) {
        if (CollectionUtils.isNotEmpty(updateList)) {
            // t_charger_order_pay表分批操作
            Lists.partition(updateList, 100)
                .forEach(chargerOrderPayRwDs::updateOrderInvoicedAmount);

            // t_charger_order表分批操作
            Lists.partition(updateList, 100)
                .forEach(list -> {
                    Map<OpType, List<UpdateOrderInvoicedAmountParam>> collect = list.stream()
                        .collect(Collectors.groupingBy(UpdateOrderInvoicedAmountParam::getOpType));

                    // 一笔订单的资金块可能既含有充值开票，又含有充电开票部分，一个invoiced_id字段不够用
                    Optional.ofNullable(collect.get(OpType.ADD))
                        .ifPresent(addList -> {
                            // 所以若order表上invoiced_id已有值，则不填入
                            Long updateInvoiceId = addList.get(0).getInvoiceId();
                            List<String> updateOrderNoList = addList.stream()
                                .map(UpdateOrderInvoicedAmountParam::getOrderNo).collect(
                                    Collectors.toList());
                            if (CollectionUtils.isNotEmpty(updateOrderNoList)
                                && updateInvoiceId != null) {
                                chargerOrderRwDs.fillInvoicedIdWhenEmpty(updateInvoiceId,
                                    updateOrderNoList);
                            }
                        });

                    Optional.ofNullable(collect.get(OpType.SUB))
                        .filter(CollectionUtils::isNotEmpty)
                        .ifPresent(subList -> {
                            // 所以若order表上invoiced_id与updateInvoiceId不一致，则不置空
                            Long updateInvoiceId = subList.get(0).getInvoiceId();
                            List<String> updateOrderNoList = subList.stream()
                                .map(UpdateOrderInvoicedAmountParam::getOrderNo).collect(
                                    Collectors.toList());
                            if (CollectionUtils.isNotEmpty(updateOrderNoList)
                                && updateInvoiceId != null) {
                                chargerOrderRwDs.emptyInvoicedIdWhenEqual(updateInvoiceId,
                                    updateOrderNoList);
                            }
                        });
                });

        }
    }

    @Transactional
    public void payBillInvoicedAmount(boolean append, List<String> orderNoList) {

        // 充电记录处理
        orderNoList.forEach(orderId -> {
            PayBillUsedDetail payBillUsedDetail = payBillService.pointRecLog(orderId, null);
            if (CollectionUtils.isNotEmpty(payBillUsedDetail.getChargeOrderVoList())) {
                List<UpdateOrderInvoicedAmountParam> updateList = payBillUsedDetail.getChargeOrderVoList()
                    .stream().map(order -> {
                        UpdateOrderInvoicedAmountParam amountParam = new UpdateOrderInvoicedAmountParam();
                        amountParam.setOpType(append ?
                                UpdateOrderInvoicedAmountParam.OpType.ADD
                                : UpdateOrderInvoicedAmountParam.OpType.SUB)
                            .setAmount(order.getAmount())
                            .setOrderNo(order.getOrderNo());
                        return amountParam;
                    }).collect(Collectors.toList());

                this.orderInvoiceDataProcess(updateList);

            }
        });

        // 考虑减少操作
        PayBillParam param = new PayBillParam();
        param.setOrderIdList(orderNoList);
        ListResponse<PayBillInvoiceBi> biList = payBillRoDs.invoiceBi(param);
        if (biList == null || CollectionUtils.isEmpty(biList.getData())) {
            log.warn("发票信息不存在. orderNoList = {}", orderNoList);
            return;
        }

        // 充值记录处理
        List<UpdateOrderInvoicedAmountParam> updateList = biList.getData().stream().map(bi -> {
            UpdateOrderInvoicedAmountParam amountParam = new UpdateOrderInvoicedAmountParam();
            amountParam.setOpType(append ?
                    UpdateOrderInvoicedAmountParam.OpType.ADD
                    : UpdateOrderInvoicedAmountParam.OpType.SUB)
                .setAmount(bi.getCanInvoiceAmount())
                .setOrderNo(bi.getOrderId());
            return amountParam;
        }).collect(Collectors.toList());

        // 分批操作
        Lists.partition(updateList, 100)
            .forEach(payBillRwDs::updateOrderInvoicedAmount);
//        int i = payBillRwDs.updateOrderInvoicedAmount(updateList);
    }

    public void emptyInvoicedIds(@NonNull Long invoicedId) {
        try {
            List<Long> invoicedIdList = List.of(invoicedId);
            Integer i = chargerOrderRwDs.emptyInvoicedIds(invoicedIdList);
            Integer ii = chargerOrderPayRwDs.emptyInvoicedIds(invoicedIdList);
            log.info("更新充电订单的开票Id: invoicedId = {}, result = {} : {}",
                invoicedId, i, ii);
        } catch (Exception e) {
            log.error("清空开票ID err = {}", e.getMessage(), e);
        }
    }

    public void postChargerHandler(@NonNull boolean append, @NonNull List<String> orderNoList,
        @Nullable Long invoiceId) {
        this.postChargerHandler(append, orderNoList, invoiceId, null);
    }

    public void postChargerHandler(@NonNull boolean append, @NonNull List<String> orderNoList,
        @Nullable Long invoiceId, @Nullable String oaProcessDefinitionKey) {
        this.chargerOrderInvoicedAmount(append, orderNoList, invoiceId, oaProcessDefinitionKey);

        if (!append && invoiceId != null) {
            this.emptyInvoicedIds(invoiceId);
        }
    }

}
