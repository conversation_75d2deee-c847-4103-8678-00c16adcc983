package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ds.trading.ro.site.ds.EssSiteRoDs;
import com.cdz360.biz.ess.model.site.param.ListEssSiteParam;
import com.cdz360.biz.ess.model.site.vo.EssSiteVo;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssSiteService {

    @Autowired
    private EssSiteRoDs essSiteRoDs;

    public Mono<ListResponse<EssSiteVo>> findEssSite(ListEssSiteParam param) {
        return Mono.just(param)
            .doOnNext(p -> {
                if (null == param.getSize() || param.getSize() > 100) {
                    param.setSize(100);
                }
            })
            .map(this.essSiteRoDs::findEssSite)
            .map(RestUtils::buildListResponse)
            .doOnNext(res -> {
                if (null != param.getTotal() && param.getTotal()) {
                    res.setTotal(this.essSiteRoDs.count(param));
                } else {
                    res.setTotal(0L);
                }
            });
    }

    public Mono<ObjectResponse<EssSiteVo>> getSiteDetail(String siteId) {
        IotAssert.isNotBlank(siteId, "场站ID不能为空");
        EssSiteVo result = essSiteRoDs.getEssSiteDetail(siteId);
        return Mono.just(RestUtils.buildObjectResponse(result));
    }
}
