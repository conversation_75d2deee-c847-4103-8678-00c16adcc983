package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.BiPlugService;
import com.cdz360.biz.dc.service.ess.EssWarnBiService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.text.MessageFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * BiPlugRest
 *
 * <AUTHOR>
 * @since 3/28/2020 11:09 AM
 */
@Slf4j
@RestController
@RequestMapping("/dataCore/biPlug")
@Tag(name = "枪头数据统计")
public class BiPlugRest {


    @Autowired
    private BiPlugService biPlugService;

    @Autowired
    private EssWarnBiService essWarnBiService;

    //    @Async
    @PostMapping("/asyncBiPlugDaily")
    public BaseResponse asyncBiPlugDaily(
        @RequestParam(name = "dateTime", required = false)
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date date,
        @RequestParam(name = "siteId", required = false) String siteId,
        @RequestParam(name = "siteIds", required = false) List<String> siteIds) {//TODO 超超哥的催促
        this.asyncBiPlugDailyX(date, siteId);
        return BaseResponse.success();
    }

    //    @Async
    @PostMapping("/asyncBiPlugMonthly")
    public BaseResponse asyncBiPlugMonthly(
        @RequestParam(name = "dateTime", required = false)
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date date,
        @RequestParam(name = "siteId", required = false) String siteId,
        @RequestParam(name = "siteIds", required = false) List<String> siteIds) {//TODO 超超哥的催促
        asyncBiPlugDMonthlyX(date, siteId);
        return BaseResponse.success();
    }

//    @Async
    @PostMapping("/asyncBiPlugRemedyDaily")
    public BaseResponse asyncBiPlugRemedyDaily(
        @RequestParam(name = "dateTime", required = false)
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date date,
        @RequestParam(name = "siteId", required = false) String siteId,
        @RequestParam(name = "siteIds", required = false) List<String> siteIds) {//TODO 超超哥的催促
        this.asyncBiPlugRemedyDailyX(date, siteId);
        return BaseResponse.success();
    }

    @Async
    public void asyncBiPlugRemedyDailyX(Date date, String siteId) {
        this.biPlugRemedyDaily(date, siteId);
    }

    @Operation(summary = "按订单更新时间补救，统计桩枪每日充电数据")
    @PostMapping("/biPlugRemedyDaily")
    public BaseResponse biPlugRemedyDaily(
        @RequestParam(name = "dateTime", required = false)
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date date,
        @RequestParam(name = "siteId", required = false) String siteId) {
        log.info("按订单更新时间补救，统计桩枪每日充电数据: {}", date);

        if (date == null) {
            date = new Date();
            log.info("按订单更新时间补救，统计桩枪每日充电数据, 使用当前时间: {}", date);
        }

        long start = System.nanoTime();
        biPlugService.refreshPlugBiByOrderDaily(date, siteId);

        String msg = MessageFormat.format("plug remedy daily finish: {0}", date);
        this.debugPerformance(msg, start);
        return BaseResponse.success();
    }

    @Async  // 临时异步处理
    public void asyncBiPlugDailyX(Date date, String siteId) {
        this.biPlugDaily(date, siteId);
    }

    //    @Operation(summary = "统计枪头每【天】活动数据")
//    @GetMapping("/biPlugDaily")       // 这里实际不是通过rest调用的....
    public BaseResponse biPlugDaily(
        @RequestParam(name = "dateTime", required = false)
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date date,
        @RequestParam(name = "siteId", required = false) String siteId) {

        log.info("统计枪头每【天】活动数据: {}", date);

        long start = System.nanoTime();
        if (date == null) {
            date = new Date();
            log.info("统计枪头每【天】活动数据, 使用当前时间: {}", date);
        }

        // 工商储告警统计逻辑
        essWarnBiService.commEssDailyBi(date, siteId);

        biPlugService.dailyBi(date, siteId);

        String msg = MessageFormat.format("plug daily finish: {0}", date);
        this.debugPerformance(msg, start);
        return BaseResponse.success();
    }

    @GetMapping("/test/test")
    public Mono<ObjectResponse<String>> testCommEssAlarmBi() {

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());

        calendar.clear(Calendar.MILLISECOND);
        calendar.clear(Calendar.SECOND);
        calendar.clear(Calendar.MINUTE);

        calendar.add(Calendar.DATE, -1);
        final Date date = calendar.getTime();

        // 工商储告警统计逻辑
        essWarnBiService.commEssDailyBi(date, null);

        return Mono.just(RestUtils.buildObjectResponse("string"));
    }

    @Async  // 临时异步处理
    public void asyncBiPlugDMonthlyX(Date date,
        String siteId) {
        this.biPlugDMonthly(date, siteId);
    }

    @Operation(summary = "统计枪头每【月】活动数据")
    @GetMapping("/biPlugMonthly")
    public BaseResponse biPlugDMonthly(
        @RequestParam(name = "dateTime", required = false)
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date date,
        @RequestParam(name = "siteId", required = false) String siteId) {

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.clear(Calendar.MILLISECOND);
        calendar.clear(Calendar.SECOND);
        calendar.clear(Calendar.MINUTE);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);

        long startTime = calendar.getTime().getTime();

        calendar.add(Calendar.MONTH, 1);
        long endTime = calendar.getTime().getTime();

        // reset month
        calendar.add(Calendar.MONTH, -1);

        long currentTime = startTime;

        log.info("plug monthly start: {}", date);
        long start = System.nanoTime();
        while (currentTime < endTime) {
            this.biPlugDaily(calendar.getTime(), siteId);
            calendar.add(Calendar.DATE, 1);
            currentTime = calendar.getTime().getTime();
        }
        String msg = MessageFormat.format("plug monthly finish: {0}", date);
        this.debugPerformance(msg, start);
        return BaseResponse.success();
    }

    /**
     * debug接口时延
     */
    private void debugPerformance(String name, long startTime) {
        long curTime = System.nanoTime();
        if (curTime - startTime > 1000L * 1000000) {
            log.warn("延迟过大 name = {}...{}. ", name, (curTime - startTime) / 1000000);
        }
    }
}