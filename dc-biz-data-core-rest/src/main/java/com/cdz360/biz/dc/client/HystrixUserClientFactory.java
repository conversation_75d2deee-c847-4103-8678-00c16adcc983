package com.cdz360.biz.dc.client;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.corp.dto.CorpOrgSyncDto;
import com.cdz360.base.model.corp.dto.CorpSyncDto;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.cus.balance.po.BalanceApplicationPo;
import com.cdz360.biz.model.cus.corp.param.ListCorpParam;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.corp.vo.CorpVo;
import com.cdz360.biz.model.cus.score.dto.UserScoreSettingLevelSiteGidDto;
import com.cdz360.biz.model.cus.score.param.SearchScoreLogParam;
import com.cdz360.biz.model.cus.settlement.param.ListSettlementParam;
import com.cdz360.biz.model.cus.settlement.param.UpdateSettlementInvoicedAmountParam;
import com.cdz360.biz.model.cus.settlement.vo.SettlementBi;
import com.cdz360.biz.model.cus.site.param.UpdateSiteParkFeeUserParam;
import com.cdz360.biz.model.cus.site.po.SiteParkFeeUserPo;
import com.cdz360.biz.model.iot.vo.WhiteCardCfgVo;
import com.cdz360.biz.model.trading.coupon.vo.UserInfoVo;
import com.chargerlinkcar.framework.common.domain.Dict;
import com.chargerlinkcar.framework.common.domain.SitePersonaliseDTO;
import com.chargerlinkcar.framework.common.domain.UserCommRef;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceInfoVo;
import com.chargerlinkcar.framework.common.domain.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @since 2018/11/25.
 */
@Slf4j
@Component
public class HystrixUserClientFactory implements FallbackFactory<UserFeignClient> {


    @Override
    public UserFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER, throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER, throwable.getStackTrace());

        return new UserFeignClient(){
            @Override
            public ObjectResponse<com.chargerlinkcar.framework.common.domain.vo.RBlocUser> findRBlocUserById(Long rBlocUserId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<RBlocUser> selectRBlocUserIds(List<Long> ids) {
                return null;
            }

            @Override
            public ObjectResponse<CorpPo> getCorp(Long corpId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<CorpVo> getCorpList(ListCorpParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse addUserCommRef(UserCommRef userCommRef) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<UserPropVO> findByPhone(String phone, Long commId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<CommCusRef> findByCommIdAndPhone(Long commId, String phone) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<RBlocUserVo> findRBlocUserVoById(Long rBlocUserId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<CommCusRef> merFindById(Long id) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<CommCusRef> findByCondition(CommCusRef ref) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse syncCorpInfo(CorpSyncDto ref) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse syncCorpOrgInfo(CorpOrgSyncDto ref) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<Dict> queryPage(Dict dict) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<UserVo> findInfoByUid(Long userId, Long topCommId, String commIdChain) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SettlementBi> settlementBi(ListSettlementParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<OrderBiVo> settlementBiForCorp(ListSettlementParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse updateSettlementInvoicedAmount(UpdateSettlementInvoicedAmountParam param) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<CommCusRef> queryCommCusRefs(Integer _index, Integer _size, String userPhone, Boolean enable, Long commId, Long userId, String cusName, String commIdChain) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<WhiteCardCfgVo> getWhiteCardCfgVo(String siteId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<CorpInvoiceInfoVo> getCorpInvoiceInfo(Long corpId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SiteParkFeeUserPo> findSiteParkFeeUserBySiteId(String siteId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<Integer> updateSiteParkFeeUser(UpdateSiteParkFeeUserParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Integer> moveCorp(Long corpId, Long commId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<VinDto> getByIdList(List<Long> idList) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<Integer> updateSiteParkFeeUserList(SitePersonaliseDTO param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<SitePersonaliseDTO> getSiteParkFeeUserList(SitePersonaliseDTO param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<BalanceApplicationPo> getByOrderId(String orderId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<BalanceApplicationPo> getByOrderIds(List<String> orderIds) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<BalanceApplicationPo> findAllByOrderIds(List<String> orderIds) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<UserScoreSettingLevelSiteGidDto> getSiteGroupUserScoreSetting(
                SearchScoreLogParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<UserInfoVo> findUserInfoById( Long id){
                return RestUtils.serverBusy4ObjectResponse();
            }
        };
    }
}
