package com.cdz360.biz.dc.service.site;

import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.service.PriceSchemaBizService;
import com.cdz360.biz.model.trading.hlht.param.DataSyncSitePrices;
import com.cdz360.biz.model.trading.site.po.EvseCfgSchedulePo;
import com.cdz360.biz.model.trading.site.vo.SiteChargePriceVo;
import com.cdz360.biz.utils.feign.hlht.OpenHlhtFeignClient;
import com.cdz360.data.cache.RedisIotReadService;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2020/2/13 21:31
 */
@Service
@Slf4j
public class SiteDefaultSettingService {
//
//    @Autowired
//    private SiteFeignClient siteFeignClient;

    @Autowired
    private RedisIotReadService redisIotReadService;

    @Autowired
    private PriceSchemaBizService priceSchemaBizService;

    @Autowired
    private OpenHlhtFeignClient openHlhtFeignClient;

    /**
     * 异步处理定时变更场站默认计费信息
     * 并同步互联互通场站最新价格策略到孙建飞库
     * @param evseCfgList
     */
    @Async
    public void setSiteDefaultSetting(List<EvseCfgSchedulePo> evseCfgList) {
        if (CollectionUtils.isEmpty(evseCfgList)) {
            return;
        }
        // 获取需要设置成场站默认计费的桩
        evseCfgList = evseCfgList.stream().filter(x->Boolean.TRUE.equals(x.getSiteDefault())).collect(
            Collectors.toList());

        if (CollectionUtils.isEmpty(evseCfgList)) {
            log.info("不存在要更改场站配置计费信息");
            return;
        }
        log.info("异步处理定时变更场站默认计费信息");

        // 转map
        Map<String, EvseCfgSchedulePo> evseCfgMap = evseCfgList.stream()
            .collect(Collectors.toMap(EvseCfgSchedulePo::getEvseNo, p -> p));

        // 找出所有站点
        List<String> evseNoList = evseCfgList.stream()
            .map(EvseCfgSchedulePo::getEvseNo).collect(Collectors.toList());

        // iot.t_evse 中获取场站
        List<EvseVo> evseList = redisIotReadService.getEvseList(evseNoList);
        if (CollectionUtils.isEmpty(evseList)) {
            log.error("redis中无法获取桩的信息: evseNoList = {}", evseNoList);
            return;
        }

        // 拼接上priceCode
        evseList.forEach(x-> x.setPriceCode(evseCfgMap.get(x.getEvseNo()).getPriceSchemeId()));

        // 场站分组
        Map<String, List<EvseVo>> collect = evseList.parallelStream()
                .filter( evseVo -> StringUtils.isNotBlank(evseVo.getSiteId()))
                .collect(Collectors.groupingBy(EvseVo::getSiteId));

        log.info("这次需要变更的场站个数: size = {}", collect.keySet().size());

        // 按照场站  计费模板 分组下发
        collect.forEach((siteId, deviceList) -> {
            Map<Long, List<EvseVo>> priceMap = deviceList.stream()
                .filter(x -> x.getPriceCode() != null && x.getEvseNo() != null)
                .collect(Collectors.groupingBy(EvseVo::getPriceCode));

            // 按照场站  计费模板设置场站默认计费信息
            priceMap.forEach((priceId, evseVoList) -> priceSchemaBizService.setDefaultPriceScheme(siteId,
                priceId, evseVoList));

            // 同步孙老师
            List<SiteChargePriceVo> sitePriceList = priceSchemaBizService.getSitePriceList(siteId);

            List<DataSyncSitePrices> syncDataList = sitePriceList.stream().map(x -> {
                DataSyncSitePrices item = new DataSyncSitePrices();
                item.setSiteId(siteId)
                    .setPriceCode(x.getId())
                    .setSupplyType(x.getTemplateType());
                return item;
            }).collect(Collectors.toList());
            if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(syncDataList)) {
                this.syncSitePrice(syncDataList);
                log.info("场站[{}]的计费信息已变更: syncDataList = {}",
                    JsonUtils.toJsonString(syncDataList));
            }
        });







//        collect.forEach((k, v) -> {
//            // 最后一个定时的默认产站计费
//            List<String> tmpList = v.stream().map(EvseVo::getEvseNo).collect(Collectors.toList());
//            Optional<EvseCfgSchedulePo> schedulePo = evseCfgList.stream()
//                    .filter(i -> tmpList.contains(i.getEvseNo()))
//                    .max(Comparator.comparing(EvseCfgSchedulePo::getScheduleTime));
//            log.info("optional = {}", schedulePo);
//            if (schedulePo.isPresent()) {
//                // 变更场站的计费信息
//                PriceTemplatePo priceSchema = priceSchemaBizService.getSitePriceScheme(k);
////                if (null == priceSchema) {
////                    log.error("device 服务异常");
////                } else {
//                    boolean needUpdate = schedulePo.get().getSiteDefault() == null ?
//                            false : schedulePo.get().getSiteDefault();
//                    if (priceSchema == null || needUpdate) {
//                        priceSchemaBizService.setDefaultPriceScheme(k,
//                                schedulePo.get().getPriceSchemeId(),v);
//                        List<SiteChargePriceVo> sitePriceList = priceSchemaBizService.getSitePriceList(k);
//
//                        List<DataSyncSitePrices> syncDataList = sitePriceList.stream().map(x -> {
//                            DataSyncSitePrices item = new DataSyncSitePrices();
//                            item.setSiteId(k)
//                                .setPriceCode(x.getId())
//                                .setSupplyType(x.getTemplateType());
//                            return item;
//                        }).collect(Collectors.toList());
//                        if (com.cdz360.base.utils.CollectionUtils.isNotEmpty(syncDataList)) {
//                            this.syncSitePrice(syncDataList);
//                        }
////                        this.syncSitePrice(k, schedulePo.get().getPriceSchemeId());
////                        this.syncSitePrice(null);
//                        log.info("场站[{}]的计费信息已变更: syncDataList = {}", JsonUtils.toJsonString(syncDataList));
//                    }
//   //             }
//            }
//        });
    }

    /**
     * 同步互联互通场站最新价格策略到孙建飞库
     */
    public void syncSitePrice(List<DataSyncSitePrices> dataSyncSitePricesList) {
        log.info("syncSitePrice siteId = {}, dataSyncSitePricesList = {}", JsonUtils.toJsonString(dataSyncSitePricesList));
        openHlhtFeignClient.syncHlhtStationPrice(dataSyncSitePricesList)
                .subscribe();
    }

}
