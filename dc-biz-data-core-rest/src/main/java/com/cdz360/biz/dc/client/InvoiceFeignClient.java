package com.cdz360.biz.dc.client;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.oa.param.PrepaidInvoicingEditParam;
import com.chargerlinkcar.framework.common.domain.invoice.InvoicedRecordVo;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoiceApplyResultDTO;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoiceApplyResultOaDTO;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoicedRecordDTO;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceInfoParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordManualParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.SaveInvoiceRecordsParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceInfoVo;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * InvoiceFeignClient
 *  TODO
 * @since 2019/11/12 11:14
 * <AUTHOR>
 */
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_INVOICE)
@Component
public interface InvoiceFeignClient {

    /**
     * 根据发票id列表查询发票信息
     * @return
     */
    @RequestMapping(value = "/api/invoiced-records/findByInvoiceIds",method = RequestMethod.POST)
    ListResponse<InvoicedRecordVo> findByInvoiceIds(@RequestBody List<Long> invoiceIds);

    @GetMapping("/api/invoiced-records/getRecordByOa")
    ObjectResponse<InvoicedRecordDTO> getRecordByOa(
        @RequestParam(value = "procInstId") String procInstId);

    /**
     * 企业客户申请开票
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/invoice/saveInvoiceRecords")
    ObjectResponse<InvoiceApplyResultDTO> saveCorpInvoiceRecords(@RequestBody SaveInvoiceRecordsParam param);

    /**
     * 企业手动开票
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/invoice/invoiceRecordManual")
    ObjectResponse<Boolean> invoiceRecordManual(@RequestBody CorpInvoiceRecordManualParam param);

    /**
     * 通过企业客户平台开票申请单号获取开票记录
     *
     * @param applyNo
     * @return
     */
    @GetMapping(value = "/api/invoice/getInvoicedRecordByApplyNo")
    ListResponse<InvoicedRecordVo> getInvoicedRecordByApplyNo(
            @RequestParam(value = "applyNo") String applyNo);

    @PostMapping(value = "/api/invoice/getCorpInvoiceDetail")
    ObjectResponse<CorpInvoiceInfoVo> getCorpInvoiceDetail(@RequestBody CorpInvoiceInfoParam param);

    @PostMapping("/api/invoiced-records/editRecordById")
    BaseResponse editRecordById(@RequestBody InvoicedRecordDTO param);

    // 收入-充电开票， 创建发票记录和发票对应的商品行信息
    @PostMapping("/api/invoiced-records/disposeByPrepaidProcess")
    ObjectResponse<Long> disposeByPrepaidProcess(@RequestBody PrepaidInvoicingEditParam param);

    /**
     * 模拟个人开票审核，导入开票系统
     * @param procInstId
     * @return
     */
    @PostMapping("/api/invoiced-recordsV4/exportToInvoiceByOa")
    ObjectResponse<InvoiceApplyResultOaDTO> exportToInvoiceByOa(
        @RequestParam("procInstId") String procInstId);

}
