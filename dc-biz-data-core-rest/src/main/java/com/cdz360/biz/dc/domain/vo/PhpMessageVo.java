package com.cdz360.biz.dc.domain.vo;

/**
 * <AUTHOR>
 *  php消息通知实体
 * @since 2018/12/3 10:57
 */
//@Data
//public class PhpMessageVo implements Serializable {
//
//    private static final long serialVersionUID = 7107621187745645364L;
//
//    /**
//     * 用户id
//     */
//    private String userId;
//
//    /**
//     * 卡号
//     */
//    private String cardNo;
//
//    /**
//     * 指开始充电时间、充值时间、卡的状态改变时间
//     * 值为毫秒单位的时间戳
//     */
//    private String time;
//
//    /**
//     * 新的卡号
//     */
//    private String newCardNo;
//
//    /**
//     * 订单来源
//     */
//    private String channelId;
//
//    /**
//     * 订单id
//     */
//    private String orderId;
//
//    /**
//     * 站点名称
//     */
//    private String stationName;
//
//    /**
//     * 预付金额
//     */
//    private String preAmount;
//
//    /**
//     * 充电时长
//     */
//    private String duration;
//
//    /**
//     * 充电费用
//     */
//    private String orderPrice;
//
//    /**
//     * 充值渠道
//     */
//    private String channel;
//
//    /**
//     * 充值金额
//     */
//    private String amount;
//
//    /**
//     * 赠送金额
//     */
//    private String giveMoney;
//
//    /**
//     * 卡余额
//     */
//    private String balance;
//
//    /**
//     * 消息类型
//     */
//    private Integer type;
//
//    /**
//     * 结束充电的类型，type为103必传
//     */
//    private String endType;
//
//    /**
//     * 充电桩编号
//     */
//    private String eqNum;
//
//    /**
//     * 充电插座编号，枪头号
//     */
//    private String postNum;
//}
