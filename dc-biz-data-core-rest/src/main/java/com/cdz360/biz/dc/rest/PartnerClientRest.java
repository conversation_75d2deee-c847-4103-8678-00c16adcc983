package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.dc.service.PartnerService;
import com.cdz360.biz.model.trading.hlht.vo.HlhtSiteCommVo;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/dataCore/partner")
public class PartnerClientRest {

    @Autowired
    private PartnerService partnerService;

    /**
     * 根据partnerCode获取场站共同商户
     *
     * @param code
     * @return
     */
    @GetMapping(value = "/getSiteByPartnerCode")
    public ObjectResponse<HlhtSiteCommVo> getSiteByPartnerCode(@RequestParam(value = "partnerCode") String code) {
        IotAssert.isNotBlank(code, "code不能为空");
        return partnerService.getSiteByPartnerCode(code);
    }

}
