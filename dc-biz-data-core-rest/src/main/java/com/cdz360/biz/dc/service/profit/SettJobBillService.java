package com.cdz360.biz.dc.service.profit;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.client.reactor.OaFeignClient;
import com.cdz360.biz.dc.service.profit.sett.SettJobBillStrategy;
import com.cdz360.biz.dc.service.profit.sett.SettJobBillStrategyFactory;
import com.cdz360.biz.ds.trading.ro.profit.sett.ds.GcProfitCfgRoDs;
import com.cdz360.biz.ds.trading.ro.profit.sett.ds.SettJobBillRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.BiSiteMonthIncomeRoDs;
import com.cdz360.biz.ds.trading.rw.profit.sett.ds.SettJobBillRwDs;
import com.cdz360.biz.model.trading.profit.sett.param.ListProfitCfgParam;
import com.cdz360.biz.model.trading.profit.sett.param.ListSettJobBillParam;
import com.cdz360.biz.model.trading.profit.sett.po.GcProfitCfgPo;
import com.cdz360.biz.model.trading.profit.sett.po.SettJobBillPo;
import com.cdz360.biz.model.trading.profit.sett.vo.ProfitCfgVo;
import com.cdz360.biz.model.trading.profit.sett.vo.SettJobBillDetailVo;
import com.cdz360.biz.model.trading.profit.sett.vo.SettJobBillVo;
import com.cdz360.biz.model.trading.site.vo.SuperviseIncomeExpenseVo;
import com.chargerlinkcar.framework.common.utils.DateUtils;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class SettJobBillService {

    private static final String SUPERVISE_TIME_KEY = "supervise:time:key";

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern(
        "yyyy-MM-dd HH:mm:ss");

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private OaFeignClient oaFeignClient;

    @Autowired
    private BiSiteMonthIncomeRoDs siteMonthIncomeRoDs;

    @Autowired
    private GcProfitCfgRoDs profitCfgRoDs;

    @Autowired
    private SettJobBillRoDs settJobBillRoDs;

    @Autowired
    private SettJobBillRwDs settJobBillRwDs;

//    @Autowired
//    private ChargeOrderSettJobBillStrategy chargeOrderSettJobBillStrategy;
//
//    @Autowired
//    private BiSysSettJobBillStrategy biSysSettJobBillStrategy;

    @Autowired
    private SettJobBillStrategyFactory settJobBillStrategyFactory;

    public Mono<SettJobBillDetailVo> getSettJobBill(String billNo) {
        SettJobBillPo settJobBillPo = settJobBillRoDs.getByBillNo(billNo);
        IotAssert.isNotNull(settJobBillPo, "账单编号无效");

        SettJobBillDetailVo result = new SettJobBillDetailVo();
        BeanUtils.copyProperties(settJobBillPo, result);
        return Mono.just(result);
    }

    public Mono<ListResponse<SettJobBillVo>> findSettJobBill(ListSettJobBillParam param) {
        return Mono.just(param)
            .doOnNext(p -> {
                if (null == param.getSize() || param.getSize() > 10000) {
                    param.setSize(9999);
                }
            })
            .map(this.settJobBillRoDs::findSettJobBill)
            .map(RestUtils::buildListResponse)
            .doOnNext(res -> {
                if (null != param.getTotal() && param.getTotal()) {
                    res.setTotal(this.settJobBillRoDs.count(param));
                } else {
                    res.setTotal(0L);
                }
            });
    }

    public Mono<SettJobBillVo> deleteSettJobBill(String billNo) {
        SettJobBillPo settJobBillPo = settJobBillRoDs.getByBillNo(billNo);
        IotAssert.isNotNull(settJobBillPo, "账单编号无效");

        if (Boolean.TRUE.equals(settJobBillPo.getEnable())) {
            boolean b = settJobBillRwDs.updateSettJobBill(
                new SettJobBillPo().setBillNo(billNo).setEnable(false));
            if (!b) {
                throw new DcArgumentException("删除操作失败");
            }
        }

        SettJobBillVo result = new SettJobBillVo();
        BeanUtils.copyProperties(settJobBillPo, result);
        return Mono.just(result);
    }

    public Mono<SettJobBillVo> recalculateSettJobBill(String billNo, Integer recalculateWay) {
        IotAssert.isTrue(List.of(1, 2).contains(recalculateWay), "重新计算方式无效");
        SettJobBillPo settJobBillPo = settJobBillRoDs.getByBillNo(billNo);
        IotAssert.isNotNull(settJobBillPo, "账单编号无效");

        ProfitCfgVo cfg = null;
        if (1 == recalculateWay) {
            IotAssert.isNotNull(settJobBillPo.getJobId(), "旧结算单不支持该方式");
            GcProfitCfgPo cfgPo = profitCfgRoDs.getById(settJobBillPo.getJobId());
            IotAssert.isNotNull(cfgPo, "结算单中的结算任务无效");
            IotAssert.isTrue(cfgPo.getEnable(), "结算单中的结算任务已被删除");
            cfg = new ProfitCfgVo();
            BeanUtils.copyProperties(cfgPo, cfg);
        }

        SettJobBillStrategy strategy = settJobBillStrategyFactory.getStrategy(
            null == cfg ? settJobBillPo.getJobCalSource() : cfg.getCalSource());
        if (null != strategy) {
            strategy.recalculateSettJobBill(settJobBillPo, cfg);
        } else {
            log.error("结算单[数据计算来源]类型无效，需要人工接入排查: {}", billNo);
        }
        SettJobBillVo result = new SettJobBillVo();
        BeanUtils.copyProperties(settJobBillPo, result);
        return Mono.just(result);
    }

    @Async
    public void generateSettJobBill() {
        LocalDate date = LocalDate.now();
        int monthDay = date.getDayOfMonth();
        log.info("结算任务结算单生成: date = {}, {}", date, monthDay);

        ListProfitCfgParam cfgParam = new ListProfitCfgParam().setEnable(true)
            .setGenerateDay(monthDay);
        cfgParam.setStart(0L).setSize(500);

        int cnt = 1;
        do {
            // 获取有时效的结算任务
            List<ProfitCfgVo> cfgList = profitCfgRoDs.findGcProfitCfg(cfgParam);

            // 生成结算单记录
            cfgList.forEach(cfg -> cfg.getSiteIdList()
                .forEach(siteId -> {
                    SettJobBillStrategy strategy =
                        settJobBillStrategyFactory.getStrategy(cfg.getCalSource());
                    if (null != strategy) {
                        log.debug("cfg = {}", JsonUtils.toJsonString(cfg));
                        strategy.generateSettJobBill(siteId, cfg);
                    } else {
                        log.error("结算任务[数据计算来源]类型无效，需要人工接入排查: {}", cfg);
                    }
                }));

            cfgParam.setStart(cfgParam.getStart() + cfgParam.getSize());
            ++cnt;

            log.info("[{}]结算任务结算单生成: size = {}", cnt, cfgList.size());
            if (cfgList.size() < cfgParam.getSize()) {
                break;
            }
        } while (cnt < 1000); // 配置最大值
    }

    /**
     * 检查结算单差异
     *
     * @param param
     * @return 返回列表[不存在的单或与当前表记录存在差异]
     */
    public Mono<ListResponse<SettJobBillVo>> checkSettJobBill(List<SettJobBillVo> param) {

        IotAssert.isNotNull(param, "请传入参");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param), "请传列表");

        return Mono.just(param)
            .map(e -> {

                final List<String> jobNos = e.stream()
                    .map(SettJobBillVo::getBillNo)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());

                IotAssert.isTrue(CollectionUtils.isNotEmpty(jobNos), "结算单号列表为空");

                ListSettJobBillParam queryParam = new ListSettJobBillParam();
                queryParam.setBillNoList(jobNos)
                    .setStart(0L)
                    .setSize(999);
                final List<SettJobBillVo> settJobBill = settJobBillRoDs.findSettJobBill(queryParam);
                final Map<String, SettJobBillVo> settJobBillVosMap = settJobBill.stream()
                    .collect(Collectors.toMap(SettJobBillVo::getBillNo, o -> o));

                final List<SettJobBillVo> settJobBillVos = new ArrayList<>();
                e.forEach(wantCheck -> {
                    final SettJobBillVo realRecord = settJobBillVosMap.get(wantCheck.getBillNo());
                    if (!equalSettJob(wantCheck, realRecord)) {
                        settJobBillVos.add(wantCheck);
                    }
                });
                return settJobBillVos;
            })
            .map(RestUtils::buildListResponse);
    }

    private static boolean equalSettJob(SettJobBillVo r, SettJobBillVo l) {
        if (r == null || l == null) {
            return false;
        }
        return r.getBillNo().equals(l.getBillNo()) &&
            DecimalUtils.eq(r.getElecFee(), l.getElecFee()) &&
            DecimalUtils.eq(r.getServFee(), l.getServFee()) &&
            DecimalUtils.eq(r.getParkFee(), l.getParkFee()) &&
            r.getJobName().equals(l.getJobName()) &&
            DateUtils.equal(r.getSettPeriodFrom(), l.getSettPeriodFrom()) &&
            DateUtils.equal(r.getSettPeriodTo(), l.getSettPeriodTo()) &&
            DateUtils.equal(r.getUpdateTime(), l.getUpdateTime());
    }

    @Async
    public void superviseIncomeExpense() {
        // t_bi_site_gc_month_income
        // t_bi_site_gc_month_expense

        // 上一次记录的时间 <=（redis）
        LocalDateTime lastDateTime = this.getSuperviseTime();
        if (null == lastDateTime) { // 第一次查询
            lastDateTime = LocalDateTime.now().minusHours(2);
        }
        log.info("观察场站收入/支出: {}", lastDateTime);

        // 检查到有变化数据，提取场站ID
        List<SuperviseIncomeExpenseVo> superviseList =
            siteMonthIncomeRoDs.superviseIncomeExpense(lastDateTime);
        if (CollectionUtils.isEmpty(superviseList)) {
            return;
        }

        log.info("处理计算单自动结算: {}", superviseList.size());
        superviseList.forEach(rec -> {
            // 通过场站ID获取指定结算单列表
            List<SettJobBillPo> settJobBillPos = settJobBillRoDs.findBiSysSettJobBill(
                rec.getSiteId(), rec.getMonth().atStartOfDay());

            settJobBillPos.forEach(bill -> {
                // 判断结算单列表是否被流程占用
                oaFeignClient.containSettJobBillNo(bill.getBillNo())
                    .map(FeignResponseValidate::checkReturn)
                    .doOnNext(inOa -> {
                        if (!inOa) { // 自动更新没有被占用的结算单
                            SettJobBillStrategy strategy = settJobBillStrategyFactory.getStrategy(
                                bill.getJobCalSource());
                            if (null != strategy) {
                                strategy.recalculateSettJobBill(bill, null);
                            }
                        }
                    })
                    .block(Duration.ofSeconds(50L));
            });
        });

        // 更新最大时间 =>（redis）
        superviseList.stream()
            .map(SuperviseIncomeExpenseVo::getUpdateTime)
            .max(LocalDateTime::compareTo)
            .ifPresent(localDateTime -> {
                this.setSuperviseTime(localDateTime);
                log.info("下次查询时间: {}", localDateTime);
            });
    }

    private void setSuperviseTime(LocalDateTime time) {
        redisTemplate.opsForValue().set(SUPERVISE_TIME_KEY, DATE_TIME_FORMATTER.format(time));
    }

    private LocalDateTime getSuperviseTime() {
        String val = redisTemplate.opsForValue().get(SUPERVISE_TIME_KEY);
        LocalDateTime result = null;
        if (null != val) {
            try {
                result = LocalDateTime.parse(val, DATE_TIME_FORMATTER);
            } catch (Exception ex) {
                log.warn("时间解释失败: {}", val);
            }
        }
        return result;
    }
}
