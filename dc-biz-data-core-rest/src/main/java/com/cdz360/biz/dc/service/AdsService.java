package com.cdz360.biz.dc.service;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ds.trading.ro.ads.ds.AdsCityRoDs;
import com.cdz360.biz.ds.trading.ro.ads.ds.AdsProvinceRoDs;
import com.cdz360.biz.ds.trading.ro.ads.ds.AdsRoDs;
import com.cdz360.biz.ds.trading.ro.ads.ds.AdsSiteGroupRoDs;
import com.cdz360.biz.ds.trading.ro.ads.ds.AdsUserRoDs;
import com.cdz360.biz.ds.trading.rw.ads.ds.AdsCityRwDs;
import com.cdz360.biz.ds.trading.rw.ads.ds.AdsProvinceRwDs;
import com.cdz360.biz.ds.trading.rw.ads.ds.AdsRwDs;
import com.cdz360.biz.ds.trading.rw.ads.ds.AdsSiteGroupRwDs;
import com.cdz360.biz.ds.trading.rw.ads.ds.AdsUserRwDs;
import com.cdz360.biz.model.ads.param.CreateAdsParam;
import com.cdz360.biz.model.ads.param.ListAdsParam;
import com.cdz360.biz.model.ads.param.UpdateAdsParam;
import com.cdz360.biz.model.ads.po.AdsCityPo;
import com.cdz360.biz.model.ads.po.AdsPo;
import com.cdz360.biz.model.ads.po.AdsProvincePo;
import com.cdz360.biz.model.ads.po.AdsSiteGroupPo;
import com.cdz360.biz.model.ads.po.AdsUserPo;
import com.cdz360.biz.model.ads.type.AdsArea;
import com.cdz360.biz.model.ads.type.AdsPlacement;
import com.cdz360.biz.model.ads.vo.AdsVo;
import com.cdz360.biz.model.cus.user.param.AddUserParam;
import com.chargerlinkcar.framework.common.domain.vo.UserPropVO;
import com.chargerlinkcar.framework.common.domain.vo.UserVo;
import com.chargerlinkcar.framework.common.feign.UserFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class AdsService {

    @Autowired
    private AdsRoDs adsRoDs;

    @Autowired
    private AdsRwDs adsRwDs;

    @Autowired
    private AdsProvinceRwDs adsProvinceRwDs;

    @Autowired
    private AdsCityRwDs adsCityRwDs;

    @Autowired
    private AdsSiteGroupRwDs adsSiteGroupRwDs;

    @Autowired
    private AdsUserRwDs adsUserRwDs;

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private AdsProvinceRoDs adsProvinceRoDs;

    @Autowired
    private AdsCityRoDs adsCityRoDs;

    @Autowired
    private AdsSiteGroupRoDs adsSiteGroupRoDs;

    @Autowired
    private AdsUserRoDs adsUserRoDs;

    @Transactional
    public void createAds(CreateAdsParam req) {
        this.checkAdsInfo(req);
        AdsPo adsPo = new AdsPo();
        adsPo.setName(req.getName())
            .setStatus(Boolean.TRUE.equals(req.getStatus()) ? 1 : 0)
            .setClient(req.getClientTypeList().stream()
                .map(AppClientType::name)
                .reduce((a, b) -> a + "," + b)
                .orElse(""))
            .setAdPlacement(req.getAdPlacement())
            .setAdImage(req.getAdImageUrl())
            .setAdSort(req.getAdSort())
            .setAdArea(req.getAdArea())
            .setAdDesc(req.getAdDesc())
            .setTopCommId(req.getTopCommId())
            .setAdUrl(req.getAdUrl());
        // 弹出频次单独处理
        if (AdsPlacement.POPUP.equals(req.getAdPlacement())) {
            adsPo.setAdPopupCount(
                Math.round(1 / req.getAdPopupCount()));
        } else {
            adsPo.setAdPopupCount(null);
        }

        // 存入广告表
        adsRwDs.insertAds(adsPo);

        if (AdsArea.PROVINCE.equals(adsPo.getAdArea()) && CollectionUtils.isNotEmpty(
            req.getProvinceCodeList())) {
            IotAssert.isTrue(adsProvinceRwDs.batchInsertAdsProvince(
                req.getProvinceCodeList()
                    .stream()
                    .map(e -> new AdsProvincePo()
                        .setAdsId(adsPo.getId())
                        .setProvinceCode(e)
                        .setEnable(true)
                    )
                    .collect(Collectors.toList())
            ) == req.getProvinceCodeList().size(), "创建广告的投放区域和省份的关系失败");
        }

        if (AdsArea.CITY.equals(adsPo.getAdArea()) && CollectionUtils.isNotEmpty(
            req.getCityCodeList())) {
            IotAssert.isTrue(adsCityRwDs.batchInsertAdsCity(
                req.getCityCodeList()
                    .stream()
                    .map(e -> new AdsCityPo()
                        .setAdsId(adsPo.getId())
                        .setCityCode(e)
                        .setEnable(true)
                    )
                    .collect(Collectors.toList())
            ) == req.getCityCodeList().size(), "创建广告的投放区域和城市的关系失败");
        }

        if (AdsArea.SITE_GROUP.equals(adsPo.getAdArea()) && CollectionUtils.isNotEmpty(
            req.getGidList())) {
            IotAssert.isTrue(adsSiteGroupRwDs.batchInsertAdsSiteGroup(
                req.getGidList()
                    .stream()
                    .map(e -> new AdsSiteGroupPo()
                        .setAdsId(adsPo.getId())
                        .setGid(e)
                        .setEnable(true)
                    )
                    .collect(Collectors.toList())
            ) == req.getGidList().size(), "创建广告的投放区域和场站组的关系失败");
        }

        if (AdsArea.USER.equals(adsPo.getAdArea()) && CollectionUtils.isNotEmpty(
            req.getPhoneList())) {

            Map<String, Long> phoneAndUserIdMap = this.getUserIdMap(req.getCommId(),
                req.getTopCommId(), req.getPhoneList());

            IotAssert.isTrue(adsUserRwDs.batchInsertAdsUser(
                req.getPhoneList()
                    .stream()
                    .map(e -> new AdsUserPo()
                        .setAdsId(adsPo.getId())
                        .setUserId(phoneAndUserIdMap.get(e))
                        .setPhone(e)
                        .setEnable(true)
                    )
                    .collect(Collectors.toList())
            ) == req.getPhoneList().size(), "创建广告的投放区域和用户的关系失败");
        }
    }

    /**
     * 获取手机号和userId的对应关系，手机号不存在则新增
     *
     * @param commId    商户id
     * @param topCommId 顶级商户id
     * @param phoneList 手机号数组
     * @return 手机号key，userId value
     */
    private Map<String, Long> getUserIdMap(Long commId, Long topCommId, List<String> phoneList) {
        Map<String, Long> phoneAndUserIdMap = new HashMap<>();
        phoneList.forEach(phone -> {
            // 判断手机号是否存在于系统，若不存在则新增
            ObjectResponse<UserPropVO> jsonObjectRes = userFeignClient.findByPhone(phone,
                topCommId);
            if (jsonObjectRes == null || jsonObjectRes.getData() == null) {
                AddUserParam param = new AddUserParam(commId,
                    topCommId,
                    "86",
                    phone,
                    null,
                    phone,
                    null, null);
                ObjectResponse<Long> ret = userFeignClient.addUser(param);
                FeignResponseValidate.check(ret);
                phoneAndUserIdMap.put(phone, ret.getData());
            } else {
                UserPropVO userPropVO = jsonObjectRes.getData();
                phoneAndUserIdMap.put(phone, userPropVO.getUserId());
            }
        });
        return phoneAndUserIdMap;
    }

    private void checkAdsInfo(CreateAdsParam req) {
        // 校验
        IotAssert.isNotBlank(req.getName(), "请填入广告标题");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(req.getClientTypeList()),
            "最少选择一个投放客户端");
        IotAssert.isNotNull(req.getAdPlacement(), "请选择投放位置");
        IotAssert.isNotBlank(req.getAdImageUrl(), "请上传广告图片");
        if (AdsPlacement.POPUP.equals(req.getAdPlacement())) {
            IotAssert.isTrue(req.getAdPopupCount() != null &&
                    req.getAdPopupCount() >= 0.01f &&
                    req.getAdPopupCount() <= 1.00f &&
                    isTwoOrLessDecimals(req.getAdPopupCount()),
                "弹窗频次只能在0.01-1之间");
        }

        // 校验文章链接
        String adUrl = req.getAdUrl();
        Pattern pattern = Pattern.compile("^(rwcApp://|http://|https://).+");
        boolean isValidUrl = Optional.ofNullable(adUrl)
            .map(url -> pattern.matcher(url).matches())
            .orElse(false);
        IotAssert.isTrue(StringUtils.isNotBlank(req.getAdUrl()) && isValidUrl, "请填入文章链接");
        IotAssert.isTrue(req.getAdSort() != null && req.getAdSort() <= 100
            && req.getAdSort() >= 0, "优先级只能在0-100之间");
        IotAssert.isNotNull(req.getAdArea(), "请选择投放区域");

        switch (req.getAdArea()) {
            case ALL:
                // do nothing
                break;
            case PROVINCE:
                IotAssert.isTrue(CollectionUtils.isNotEmpty(req.getProvinceCodeList()),
                    "请选择省份列表");
                break;
            case CITY:
                IotAssert.isTrue(CollectionUtils.isNotEmpty(req.getCityCodeList()),
                    "请选择城市列表");
                break;
            case SITE_GROUP:
                IotAssert.isTrue(CollectionUtils.isNotEmpty(req.getGidList()), "请选择场站组");
                break;
            case USER:
                IotAssert.isTrue(CollectionUtils.isNotEmpty(req.getPhoneList()),
                    "请输入指定用户的手机号");
                // 校验手机号
                Pattern phonePattern = Pattern.compile("^1[3456789]\\d{9}$");
                // 验证每个手机号码是否符合规则
                for (String phone : req.getPhoneList()) {
                    phone = phone.trim();
                    if (!phonePattern.matcher(phone).matches()) {
                        throw new IllegalArgumentException("手机号码不符合规则: " + phone);
                    }
                }
                Set<String> phoneSet = new HashSet<>(req.getPhoneList());
                IotAssert.isTrue(phoneSet.size() == req.getPhoneList().size(),
                    "手机号不能重复");
                break;
        }
        IotAssert.isNotBlank(req.getAdDesc(), "请填入备注");

    }

    /**
     * 校验是否最多两位小数
     *
     * @param adPopupCount
     * @return
     */
    private boolean isTwoOrLessDecimals(Float adPopupCount) {
        BigDecimal bigDecimalNumber = new BigDecimal(String.valueOf(adPopupCount));

        // 乘100
        BigDecimal multiplied = bigDecimalNumber.multiply(new BigDecimal(100));
        // 获得整数部分
        BigDecimal integerPart = new BigDecimal(multiplied.toBigInteger());
        // 获得小数部分
        BigDecimal fractionalPart = multiplied.subtract(integerPart);

        // 如果小数部分不为零，则表示有多余的小数位
        return fractionalPart.compareTo(BigDecimal.ZERO) == 0;
    }

    public void activeAds(Long id) {
        IotAssert.isTrue(NumberUtils.gtZero(id), "启用广告需要传入id");
        AdsVo adsVo = adsRoDs.getByAdsId(id);
        IotAssert.isNotNull(adsVo, "广告信息不存在");
        AdsPo adsPo = new AdsPo();
        adsPo.setId(id);
        adsPo.setStatus(1);
        adsRwDs.updateAds(adsPo);
    }

    public void abortAds(Long id) {
        IotAssert.isTrue(NumberUtils.gtZero(id), "停用广告需要传入id");
        AdsVo adsVo = adsRoDs.getByAdsId(id);
        IotAssert.isNotNull(adsVo, "广告信息不存在");
        AdsPo adsPo = new AdsPo();
        adsPo.setId(id);
        adsPo.setStatus(0);
        adsRwDs.updateAds(adsPo);
    }

    @Transactional
    public void updateAds(UpdateAdsParam req) {
        this.checkAdsInfo(req);

        IotAssert.isTrue(NumberUtils.gtZero(req.getId()), "编辑广告信息需要传入id");
        AdsVo adsVo = adsRoDs.getByAdsId(req.getId());
        IotAssert.isNotNull(adsVo, "广告信息不存在");
        AdsPo adsPo = new AdsPo();
        adsPo.setId(req.getId());
        adsPo.setName(req.getName())
            .setStatus(Boolean.TRUE.equals(req.getStatus()) ? 1 : 0)
            .setClient(req.getClientTypeList().stream()
                .map(AppClientType::name)
                .reduce((a, b) -> a + "," + b)
                .orElse(""))
            .setAdPlacement(req.getAdPlacement())
            .setAdImage(req.getAdImageUrl())
            .setAdSort(req.getAdSort())
            .setAdArea(req.getAdArea())
            .setAdDesc(req.getAdDesc())
            .setTopCommId(req.getTopCommId())
            .setAdUrl(req.getAdUrl());
        // 弹出频次单独处理
        if (AdsPlacement.POPUP.equals(req.getAdPlacement())) {
            adsPo.setAdPopupCount(
                Math.round(1 / req.getAdPopupCount()));
        } else {
            adsPo.setAdPopupCount(null);
        }

        // 存入广告表
        adsRwDs.updateAds(adsPo);

        // 处理投放区域
        if (adsVo.getAdArea().equals(req.getAdArea())) {
            // 投放区域没变化，校验里边的值一样不
            switch (req.getAdArea()) {
                case ALL:
                    // do nothing
                    break;
                case PROVINCE:
                    if (!compareListsIgnoringOrder(adsVo.getProvinceCodeList(),
                        req.getProvinceCodeList())) {
                        // 不一样，把旧的都置为不可用，然后更新新的
                        adsProvinceRwDs.updateAdsProvinceEnableByAdsId(adsVo.getId(), false);
                        IotAssert.isTrue(adsProvinceRwDs.batchInsertAdsProvince(
                                req.getProvinceCodeList()
                                    .stream()
                                    .map(e -> new AdsProvincePo()
                                        .setAdsId(adsPo.getId())
                                        .setProvinceCode(e)
                                        .setEnable(true)
                                    )
                                    .collect(Collectors.toList())
                            ) == req.getProvinceCodeList().size(),
                            "更新广告的投放区域和省份的关系失败");
                    } else {
                        log.info("广告的投放区域和省份的关系没变，不进行更新, 之前的是:{}",
                            adsVo.getProvinceCodeList());
                    }
                    break;
                case CITY:
                    if (!compareListsIgnoringOrder(adsVo.getCityCodeList(),
                        req.getCityCodeList())) {
                        // 不一样，把旧的都置为不可用，然后更新新的
                        adsCityRwDs.updateAdsCityEnableByAdsId(adsVo.getId(), false);
                        IotAssert.isTrue(adsCityRwDs.batchInsertAdsCity(
                            req.getCityCodeList()
                                .stream()
                                .map(e -> new AdsCityPo()
                                    .setAdsId(adsPo.getId())
                                    .setCityCode(e)
                                    .setEnable(true)
                                )
                                .collect(Collectors.toList())
                        ) == req.getCityCodeList().size(), "更新广告的投放区域和城市的关系失败");
                    } else {
                        log.info("广告的投放区域和城市的关系没变，不进行更新, 之前的是:{}",
                            adsVo.getCityCodeList());
                    }
                    break;
                case SITE_GROUP:
                    if (!compareListsIgnoringOrder(adsVo.getGidList(), req.getGidList())) {
                        // 不一样，把旧的都置为不可用，然后更新新的
                        adsSiteGroupRwDs.updateAdsSiteGroupEnableByAdsId(adsVo.getId(), false);
                        IotAssert.isTrue(adsSiteGroupRwDs.batchInsertAdsSiteGroup(
                            req.getGidList()
                                .stream()
                                .map(e -> new AdsSiteGroupPo()
                                    .setAdsId(adsPo.getId())
                                    .setGid(e)
                                    .setEnable(true)
                                )
                                .collect(Collectors.toList())
                        ) == req.getGidList().size(), "更新广告的投放区域和场站组的关系失败");
                    } else {
                        log.info("广告的投放区域和场站组的关系没变，不进行更新, 之前的是:{}",
                            adsVo.getProvinceCodeList());
                    }
                    break;
                case USER:
                    if (!compareListsIgnoringOrder(adsVo.getPhoneList(), req.getPhoneList())) {
                        // 不一样，把旧的都置为不可用，然后更新新的
                        adsUserRwDs.updateAdsUserEnableByAdsId(adsVo.getId(), false);
                        Map<String, Long> phoneAndUserIdMap = this.getUserIdMap(req.getCommId(),
                            req.getTopCommId(), req.getPhoneList());
                        IotAssert.isTrue(adsUserRwDs.batchInsertAdsUser(
                            req.getPhoneList()
                                .stream()
                                .map(e -> new AdsUserPo()
                                    .setAdsId(adsPo.getId())
                                    .setUserId(phoneAndUserIdMap.get(e))
                                    .setPhone(e)
                                    .setEnable(true)
                                )
                                .collect(Collectors.toList())
                        ) == req.getPhoneList().size(), "更新广告的投放区域和用户的关系失败");
                    } else {
                        log.info("广告的投放区域和用户的关系没变，不进行更新, 之前的是：{}",
                            adsVo.getPhoneList());
                    }
                    break;
            }
        } else {
            // 投放区域有变化，进行更新，先把之前的置为不可用，再更新新的
            switch (adsVo.getAdArea()) {
                case ALL:
                    // do nothing
                    break;
                case PROVINCE:
                    // 把旧的都置为不可用
                    adsProvinceRwDs.updateAdsProvinceEnableByAdsId(adsVo.getId(), false);
                    break;
                case CITY:
                    // 把旧的都置为不可用
                    adsCityRwDs.updateAdsCityEnableByAdsId(adsVo.getId(), false);
                    break;
                case SITE_GROUP:
                    //把旧的都置为不可用
                    adsSiteGroupRwDs.updateAdsSiteGroupEnableByAdsId(adsVo.getId(), false);
                    break;
                case USER:
                    // 把旧的都置为不可用
                    adsUserRwDs.updateAdsUserEnableByAdsId(adsVo.getId(), false);
                    break;
            }
            switch (req.getAdArea()) {
                case ALL:
                    // do nothing
                    break;
                case PROVINCE:
                    // 更新新的
                    IotAssert.isTrue(adsProvinceRwDs.batchInsertAdsProvince(
                            req.getProvinceCodeList()
                                .stream()
                                .map(e -> new AdsProvincePo()
                                    .setAdsId(adsPo.getId())
                                    .setProvinceCode(e)
                                    .setEnable(true)
                                )
                                .collect(Collectors.toList())
                        ) == req.getProvinceCodeList().size(),
                        "更新广告的投放区域和省份的关系失败");
                    break;
                case CITY:
                    // 更新新的
                    IotAssert.isTrue(adsCityRwDs.batchInsertAdsCity(
                        req.getCityCodeList()
                            .stream()
                            .map(e -> new AdsCityPo()
                                .setAdsId(adsPo.getId())
                                .setCityCode(e)
                                .setEnable(true)
                            )
                            .collect(Collectors.toList())
                    ) == req.getCityCodeList().size(), "更新广告的投放区域和城市的关系失败");
                    break;
                case SITE_GROUP:
                    // 更新新的
                    IotAssert.isTrue(adsSiteGroupRwDs.batchInsertAdsSiteGroup(
                        req.getGidList()
                            .stream()
                            .map(e -> new AdsSiteGroupPo()
                                .setAdsId(adsPo.getId())
                                .setGid(e)
                                .setEnable(true)
                            )
                            .collect(Collectors.toList())
                    ) == req.getGidList().size(), "更新广告的投放区域和场站组的关系失败");
                    break;
                case USER:
                    // 更新新的
                    Map<String, Long> phoneAndUserIdMap = this.getUserIdMap(req.getCommId(),
                        req.getTopCommId(), req.getPhoneList());
                    IotAssert.isTrue(adsUserRwDs.batchInsertAdsUser(
                        req.getPhoneList()
                            .stream()
                            .map(e -> new AdsUserPo()
                                .setAdsId(adsPo.getId())
                                .setUserId(phoneAndUserIdMap.get(e))
                                .setPhone(e)
                                .setEnable(true)
                            )
                            .collect(Collectors.toList())
                    ) == req.getPhoneList().size(), "更新广告的投放区域和用户的关系失败");
                    break;
            }
        }
    }


    public AdsVo getAdsDetail(Long id) {
        IotAssert.isNotNull(id, "请传入活动id");
        AdsVo ret = adsRoDs.getByAdsId(id);
        IotAssert.isNotNull(ret, "活动不存在: " + id);
        return ret;
    }


    public ListResponse<AdsVo> listAds(ListAdsParam req) {

        if (req.getStart() == null || req.getStart() < 0) {
            req.setStart(0L);
        }
        if (req.getSize() == null || req.getSize() < 0) {
            req.setSize(10);
        }
        List<AdsPo> list = adsRoDs.findSortedList(req);

        List<Long> adsIdList = Optional.ofNullable(list)
            .orElse(Collections.emptyList())
            .stream()
            .map(AdsPo::getId)
            .collect(Collectors.toList());

        if (adsIdList.isEmpty()) {
            return new ListResponse<>(new ArrayList<>(), 0L);
        }

        // 处理省份、城市、场站组、用户手机号信息
        List<AdsProvincePo> provincePoList = adsProvinceRoDs.getByAdsIdList(adsIdList);
        List<AdsCityPo> cityPoList = adsCityRoDs.getByAdsIdList(adsIdList);
        List<AdsSiteGroupPo> siteGroupPoList = adsSiteGroupRoDs.getByAdsIdList(adsIdList);
        List<AdsUserPo> userPoList = adsUserRoDs.getByAdsIdList(adsIdList);

        List<AdsVo> adsVoList = Optional.ofNullable(list)
            .orElse(Collections.emptyList())
            .stream()
            .map(po -> {
                AdsVo vo = new AdsVo();
                vo.setAdDesc(po.getAdDesc())
                    .setStatus(po.getStatus())
                    .setName(po.getName())
                    .setAdArea(po.getAdArea())
                    .setAdUrl(po.getAdUrl())
                    .setClient(po.getClient())
                    .setAdSort(po.getAdSort())
                    .setAdPopupCount(po.getAdPopupCount())
                    .setAdImage(po.getAdImage())
                    .setAdPlacement(po.getAdPlacement())
                    .setTopCommId(po.getTopCommId())
                    .setCreateTime(po.getCreateTime())
                    .setId(po.getId())
                    .setUpdateTime(po.getUpdateTime());
                switch (po.getAdArea()) {
                    case ALL -> {
                        // do nothing
                    }
                    case PROVINCE -> {
                        vo.setProvinceCodeList(
                            Optional.ofNullable(provincePoList)
                                .orElse(Collections.emptyList())
                                .stream()
                                .filter(item -> item.getAdsId().equals(po.getId()))
                                .map(AdsProvincePo::getProvinceCode)
                                .collect(Collectors.toList()));
                    }
                    case CITY -> {
                        vo.setCityCodeList(
                            Optional.ofNullable(cityPoList)
                                .orElse(Collections.emptyList())
                                .stream()
                                .filter(item -> item.getAdsId().equals(po.getId()))
                                .map(AdsCityPo::getCityCode)
                                .collect(Collectors.toList()));
                    }
                    case SITE_GROUP -> {
                        vo.setGidList(
                            Optional.ofNullable(siteGroupPoList)
                                .orElse(Collections.emptyList())
                                .stream()
                                .filter(item -> item.getAdsId().equals(po.getId()))
                                .map(AdsSiteGroupPo::getGid)
                                .collect(Collectors.toList()));
                    }
                    case USER -> {
                        vo.setPhoneList(
                            Optional.ofNullable(userPoList)
                                .orElse(Collections.emptyList())
                                .stream()
                                .filter(item -> item.getAdsId().equals(po.getId()))
                                .map(AdsUserPo::getPhone)
                                .collect(Collectors.toList()));
                    }
                }
                return vo;
            })
            .collect(Collectors.toList());
        return new ListResponse<>(adsVoList, adsRoDs.findListCount(req));
    }

    /**
     * c端获取广告列表
     *
     * @param param
     * @return
     */
    public ListResponse<AdsVo> getUserAdsList(ListAdsParam param) {
        if (NumberUtils.gtZero(param.getUserId())) {
            // 先找出来用户手机号
            ObjectResponse<UserVo> user = userFeignClient.queryUserByUidAndCommId(param.getUserId(),
                null);
            if (user != null && user.getData() != null && StringUtils.isNotBlank(
                user.getData().getPhone())) {
                param.setPhone(user.getData().getPhone());
            }
        }
        // c端获取的，如果投放区域相关的查询条件都是空，补充投放区域是ALL的查询条件
        if (StringUtils.isBlank(param.getProvinceCode()) && StringUtils.isBlank(param.getCityCode())
            && StringUtils.isBlank(param.getSiteId()) && StringUtils.isBlank(
            param.getProvinceCode())) {
            param.setAdAreaList(Collections.singletonList(AdsArea.ALL));
        }
        List<AdsPo> list = adsRoDs.findSortedList(param);
        List<AdsVo> adsVoList = Optional.ofNullable(list)
            .orElse(Collections.emptyList())
            .stream()
            .map(po -> {
                AdsVo vo = new AdsVo();
                vo.setAdDesc(po.getAdDesc())
                    .setStatus(po.getStatus())
                    .setName(po.getName())
                    .setAdArea(po.getAdArea())
                    .setAdUrl(po.getAdUrl())
                    .setClient(po.getClient())
                    .setAdSort(po.getAdSort())
                    .setAdPopupCount(po.getAdPopupCount())
                    .setAdImage(po.getAdImage())
                    .setAdPlacement(po.getAdPlacement())
                    .setTopCommId(po.getTopCommId())
                    .setCreateTime(po.getCreateTime())
                    .setId(po.getId())
                    .setUpdateTime(po.getUpdateTime());
                return vo;
            })
            .collect(Collectors.toList());
        // 用户查看的广告，无需统计个数
        return new ListResponse<>(adsVoList, Long.valueOf(adsVoList.size()));
    }

    /**
     * 比较两个数组的值是否相等，忽略顺序
     *
     * @param list1
     * @param list2
     * @return
     */
    public boolean compareListsIgnoringOrder(List<String> list1, List<String> list2) {
        if (list1 == null && list2 == null) {
            // 两个列表都为null，视为相同
            return true;
        } else if (list1 == null || list2 == null) {
            // 一个列表为null，另一个不为null，视为不相同
            return false;
        }

        Set<String> set1 = new HashSet<>(list1);
        Set<String> set2 = new HashSet<>(list2);

        return set1.equals(set2);
    }

}
