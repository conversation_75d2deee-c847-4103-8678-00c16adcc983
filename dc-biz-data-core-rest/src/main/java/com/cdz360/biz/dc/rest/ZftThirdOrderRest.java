package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.dc.service.ZftThirdOrderService;
import com.cdz360.biz.model.trading.bill.dto.ZftThirdOrderDto;
import com.cdz360.biz.model.trading.bill.param.ListZftThirdOrderParam;
import com.cdz360.biz.model.trading.bill.vo.TradeOrderBi;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@Tag(name = "支付平台账单订单相关接口", description = "支付平台账单订单相关接口")
@RestController
public class ZftThirdOrderRest {

    @Autowired
    private ZftThirdOrderService zftThirdOrderService;

    @Operation(summary = "获取支付平台账单订单列表")
    @PostMapping(value = "/api/dataCore/findAllZftThirdOrder")
    public Mono<ListResponse<ZftThirdOrderDto>> findAllZftThirdOrder(
            ServerHttpRequest request,
            @RequestBody ListZftThirdOrderParam param) {
        log.debug("获取支付平台账单订单列表: {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return zftThirdOrderService.findAllZftThirdOrder(param);
    }

    @Operation(summary = "企业直付交易记录统计")
    @PostMapping(value = "/api/dataCore/tradeOrderBi")
    public Mono<ListResponse<TradeOrderBi>> tradeOrderBi(
            ServerHttpRequest request,
            @RequestBody ListZftThirdOrderParam param) {
        log.debug("企业直付交易记录统计: {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return zftThirdOrderService.tradeOrderBi(param);
    }
}
