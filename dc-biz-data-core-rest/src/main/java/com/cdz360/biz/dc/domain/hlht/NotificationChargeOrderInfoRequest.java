package com.cdz360.biz.dc.domain.hlht;
//
//import com.alibaba.fastjson.annotation.JSONField;
//import com.fasterxml.jackson.annotation.JsonProperty;
//import lombok.Data;
//
//import java.io.Serializable;
//import java.math.BigDecimal;
//import java.util.List;
//
///**
// * <AUTHOR>
// *  推送充电订单信息
// * @since 2019-04-28 9:56
// */
//@Data
//public class NotificationChargeOrderInfoRequest implements Serializable {
//
//    /**
//     * 运营商ID
//     */
//    @JSONField(name = "OperatorID")
//    @JsonProperty(value = "OperatorID")
//    private String operatorId;
//
//    /**
//     * 充电订单号
//     */
//    @JSONField(name = "StartChargeSeq")
//    @JsonProperty(value = "StartChargeSeq")
//    private String startChargeSeq;
//
//    /**
//     * 充电设备接口编码
//     */
//    @JSONField(name = "ConnectorID")
//    @JsonProperty(value = "ConnectorID")
//    private String connectorID;
//
//    /**
//     * 累计充电量
//     */
//    @JSONField(name = "TotalPower")
//    @JsonProperty(value = "TotalPower")
//    private BigDecimal totalPower;
//    /**
//     * 累计总金额
//     */
//    @JSONField(name = "TotalMoney")
//    @JsonProperty(value = "TotalMoney")
//    private BigDecimal totalMoney;
//
//    /**
//     * 开始时间
//     */
//    @JSONField(name = "StartTime")
//    @JsonProperty(value = "StartTime")
//    private String startTime;
//
//    /**
//     * 结束时间
//     */
//    @JSONField(name = "EndTime")
//    @JsonProperty(value = "EndTime")
//    private String endTime;
//
//    /**
//     * 总电费
//     * */
//    @JSONField(name = "TotalElecMoney")
//    @JsonProperty(value = "TotalElecMoney")
//    private BigDecimal totalElecMoney;
//
//    /**
//     * 总服务费
//     * */
//    @JSONField(name = "TotalServiceMoney")
//    @JsonProperty(value = "TotalServiceMoney")
//    private BigDecimal totalServiceMoney;
//
//    /**
//     * 充电结束原因
//     * */
//    @JSONField(name = "StopReason")
//    @JsonProperty(value = "StopReason")
//    private Integer stopReason;
//
//    /**
//     * 时段数
//     * */
//    @JSONField(name = "SumPeriod")
//    @JsonProperty(value = "SumPeriod")
//    private Integer sumPeriod;
//
//    /**
//     * 充电明细
//     * */
//    @JSONField(name = "ChargeDetails")
//    @JsonProperty(value = "ChargeDetails")
//    private List<ChargeDetail> chargeDetails;
//}
