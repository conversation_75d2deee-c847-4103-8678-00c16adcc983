package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.OrderDataService;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

@Slf4j
@RestController
@Tag(name = "定时任务", description = "定时任务")
@RequestMapping("/dataCore/job")
public class JobRest {

    @Autowired
    private OrderDataService orderDataService;

    @GetMapping("/archiveOrderDetail")
    public BaseResponse archiveOrderDetail(@RequestParam(value = "date", required = false)
                                           @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {

        String tid = RandomStringUtils.randomAlphanumeric(8);
        log.info("订单详情归档 [{}]", tid);
        orderDataService.archiveOrderDetail(date, tid);
        return RestUtils.success();
    }

    /**
     * 临时洗数据使用，完成后删除
     * @param start
     * @param end
     * @return
     */
    @GetMapping("/archiveOrderDetailTemp")
    public BaseResponse archiveOrderDetail(@RequestParam(value = "start")
                                           @DateTimeFormat(pattern = "yyyy-MM-dd") Date start,
                                           @RequestParam(value = "end")
                                           @DateTimeFormat(pattern = "yyyy-MM-dd") Date end) {

        String tid = RandomStringUtils.randomAlphanumeric(8);
        log.info("订单详情归档 [{}] start = {}, end = {}", tid, start, end);
        orderDataService.archiveOrderDetailTemp(start, end, tid);
        return RestUtils.success();
    }
}
