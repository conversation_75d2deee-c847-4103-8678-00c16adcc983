package com.cdz360.biz.dc.domain;

/**
 * <AUTHOR>
 *  账单类型枚举
 * @since 2018/12/1 16:26
 */
public enum BillTypeEnum {

    RECHARGE(1,"充值"),
    BALANCE_DEDUCTION(2,"充电（余额抵扣）"),
    WITHDRAWALS(3,"提现"),
    DEPOSIT_WITHDRAWALS(4,"保证金提取"),
    DEPOSIT_PAY(5,"保证金缴纳"),
    ORDER_ENTER(6,"充电"),
    REFUND(7,"充电退款"),
    REFUND_TO_BALANCE(8,"充电退款（退至余额）"),
    CARD_DEDUCTION(12,"平台鉴权卡充电支付");

    private Integer code;
    private String value;

    private BillTypeEnum(Integer code,String value){
        this.code = code;
        this.value = value;
    }

    public static String getValue(Integer code){
        for (BillTypeEnum e : BillTypeEnum.values()) {
            if( e.getCode().equals(code) ){
                return e.value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
