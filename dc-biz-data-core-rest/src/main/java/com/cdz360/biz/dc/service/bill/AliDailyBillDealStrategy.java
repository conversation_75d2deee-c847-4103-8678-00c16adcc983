package com.cdz360.biz.dc.service.bill;

import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.config.MchBillRegexConfig;
import com.cdz360.biz.dc.service.parse.AliPayFileParse;
import com.cdz360.biz.ds.trading.ro.corp.ds.CorpRoDs;
import com.cdz360.biz.model.trading.bill.param.NotifyDailyBillParam;
import com.cdz360.biz.model.trading.bill.po.ZftDailyBillPo;
import com.cdz360.biz.model.trading.bill.type.DailyBillDownloadResult;
import com.cdz360.biz.model.trading.bill.type.DailyBillStatus;
import com.cdz360.biz.model.trading.corp.po.CorpPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.PostConstruct;
import java.util.Collection;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Component
public class AliDailyBillDealStrategy extends AbstractDailyBillDealStrategy {

    @Autowired
    private MchBillRegexConfig mchBillRegexConfig;

    @Autowired
    private CorpRoDs corpRoDs;

    @Autowired
    private DailyBillDealService dailyBillDealService;

    @Autowired
    private AliPayFileParse aliPayFileParse;

    @PostConstruct
    public void init() {
        this.dailyBillDealService.addStrategy(PayChannel.ALIPAY, this);
    }

    @Transactional
    @Override
    public void execute(ZftDailyBillPo po, NotifyDailyBillParam param) {
        if (DailyBillDownloadResult.FAIL.equals(param.getResult())) {
            po.setStatus(DailyBillStatus.LOAD_FAIL);
            zftDailyBillRwDs.updateZftDailyBill(po);
            log.error("账单下载失败: msg = {}", param.getFailMsg());
            return;
        }

        // 处理文件流
        po.setStatus(DailyBillStatus.CHECKING)
                .setDownloadUrl(param.getDownloadUrl());
        zftDailyBillRwDs.updateZftDailyBill(po);

        try {
            if (StringUtils.isNotBlank(param.getDownloadUrl())) {
                // 下载对账
                String localPath = "/tmp/" + po.getName() + ".csv";
                super.downloadFile(po.getDownloadUrl(), localPath);

                // 读取文件内容进行对账
                aliPayFileParse.parseFile(localPath, po.getId());
            }

            // 没有对上的充值记录(t_pay_bill)
            super.checkPayBill(po);

            po.setStatus(DailyBillStatus.CHECK_SUCCESS);
        } catch (Exception e) {
            po.setStatus(DailyBillStatus.CHECK_FAIL);
        }

        zftDailyBillRwDs.updateZftDailyBill(po);
    }

    @Transactional
    @Override
    public void regexExecute(NotifyDailyBillParam param) {
        if (DailyBillDownloadResult.FAIL.equals(param.getResult())) {
            log.error("账单下载失败: msg = {}", param.getFailMsg());
            return;
        }

        if (CollectionUtils.isEmpty(mchBillRegexConfig.getRegList())) {
            log.error("没有配置: msg = {}", param);
            return;
        }

        // 企业是否存在
        CorpPo corp = corpRoDs.getCorpById(param.getCorpId());
        if (null == corp) {
            log.error("对账企业不存在: {}", param);
            return;
        }

        Optional<MchBillRegexConfig.CorpMchInfo> corpMchOp = mchBillRegexConfig.getRegList()
                .stream()
                .filter(i -> i.getTopCommId().equals(corp.getTopCommId()) &&
                        CollectionUtils.isNotEmpty(i.getCorpMchInfoList()))
                .map(i -> i.getCorpMchInfoList().stream()
                        .filter(mch -> mch.getCorpId().equals(param.getCorpId()))
                        .collect(Collectors.toList()))
                .flatMap(Collection::stream)
                .findFirst();

        if (corpMchOp.isEmpty()) {
            log.error("配置中没有找到对应的企业: {}", param);
            return;
        }

        MchBillRegexConfig.CorpMchInfo corpMchInfo = corpMchOp.get();
        if (StringUtils.isNotBlank(param.getDownloadUrl())) {
            // 下载对账
            String localPath = "/tmp/" + corpMchInfo.getCorpId() + '-' + corpMchInfo.getCorpName() + ".csv";
            super.downloadFile(param.getDownloadUrl(), localPath);

            // 读取文件内容进行对账
            aliPayFileParse.regexParseFile(localPath, corpMchInfo);

            //变更状态
            super.checkZftThirdOrderResult(corpMchInfo);
        }
    }
}
