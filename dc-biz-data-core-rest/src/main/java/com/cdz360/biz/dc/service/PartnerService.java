package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.client.reactor.ReactorOpenHlhtFeignClient;
import com.cdz360.biz.ds.trading.ro.comm.ds.CommercialRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import com.cdz360.biz.model.trading.hlht.po.PartnerSitePo;
import com.cdz360.biz.model.trading.hlht.vo.HlhtSiteCommVo;
import java.time.Duration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PartnerService {

    @Autowired
    private ReactorOpenHlhtFeignClient reactorOpenHlhtFeignClient;
    @Autowired
    private SiteRoDs siteRoDs;
    @Autowired
    private CommercialRoDs commercialRoDs;

    public ObjectResponse<HlhtSiteCommVo> getSiteByPartnerCode(@RequestParam(value = "partnerCode") String code) {
        ListResponse<PartnerSitePo> siteVoListResponse = reactorOpenHlhtFeignClient.getSiteListByCode(code)
            .block(Duration.ofSeconds(50L));
        HlhtSiteCommVo result = new HlhtSiteCommVo();
        if (siteVoListResponse != null && CollectionUtils.isNotEmpty(siteVoListResponse.getData())) {
            List<PartnerSitePo> siteList = siteVoListResponse.getData();
            result.setSiteList(siteList);
            // 场站所属的共同商户
            List<String> idList = siteList.stream().map(PartnerSitePo::getSiteId).collect(Collectors.toList());

            List<List<Long>> commIdList = new ArrayList<>();
            List<Long> commList = new ArrayList<>();

            // idChain转list存放
            siteRoDs.getIdChainBySiteIdList(idList).stream().forEach(e -> {
                String[] split = e.split(",");
                List<Long> collect = Arrays.stream(split).map(v -> Long.parseLong(v)).collect(Collectors.toList());
                commIdList.add(collect);
            });

            // 比较获取几个list交集商户
            int size = commIdList.size();
            List<Long> tmpList = new ArrayList<>();
            if (size == 1) {
                tmpList = commIdList.get(0);
            } else {
                List<Long> tmpList1 = commIdList.get(0);
                List<Long> tmpList2 = commIdList.get(1);
                tmpList = tmpList1.stream().filter(e -> tmpList2.contains(e)).collect(Collectors.toList());
                for (var i = 0; i < size; i++) {
                    if (i == 0 || i == 1) {
                        continue;
                    }
                    if (CollectionUtils.isEmpty(tmpList)) {
                        break;
                    }
                    List<Long> finalTmpList = tmpList;
                    tmpList = commIdList.get(i).stream().filter(e -> finalTmpList.contains(e)).collect(Collectors.toList());
                }
            }
            // 如果存在交集 取最小的
            if (CollectionUtils.isNotEmpty(tmpList)) {
                Long commId = Collections.max(tmpList);
                CommercialSimpleVo commercial = commercialRoDs.getCommercial(commId);
                if (commercial != null) {
                    result.setCommId(commId)
                            .setCommName(commercial.getCommName());
                }
            }
            return RestUtils.buildObjectResponse(result);


        } else {
            return RestUtils.buildObjectResponse(result);
        }

    }
}
