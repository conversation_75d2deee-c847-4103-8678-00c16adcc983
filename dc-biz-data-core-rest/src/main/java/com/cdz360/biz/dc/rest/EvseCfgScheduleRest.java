package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.EvseCfgScheduleService;
import com.cdz360.biz.model.iot.param.ModifyEvseCfgParam;
import com.cdz360.biz.model.trading.site.po.EvseCfgSchedulePo;
import com.cdz360.biz.model.trading.site.vo.EvseCfgScheduleVo;
import com.cdz360.biz.model.trading.site.vo.PriceSchemeSiteVo;
import com.chargerlinkcar.framework.common.domain.param.ListPriceSchemeSiteUseParam;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@Tag(name = "桩配置定时下发信息")
public class EvseCfgScheduleRest {

    @Autowired
    private EvseCfgScheduleService evseCfgScheduleService;

    // xxl-job 中启动对应的定时任务(1分钟一次)
    @Operation(summary = "定时任务触发下发桩计费信息")
    @GetMapping("/dataCore/evseCfgSchedule/schedule")
    public BaseResponse evseCfgSchedule(
            ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false));
        this.evseCfgScheduleService.schedule();
        return RestUtils.success();
    }

    @Operation(summary = "桩配置定时下发批量插入")
    @PostMapping("/dataCore/evseCfgSchedule/batchInsert")
    public ObjectResponse<Integer> batchInsert(
            ServerHttpRequest request, @RequestBody List<EvseCfgSchedulePo> poList) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + "; param size = {}", poList.size());
        this.evseCfgScheduleService.batchInsert(poList);
        return RestUtils.buildObjectResponse(1);
    }

    @Operation(summary = "获取计费模板待下发列表")
    @PostMapping("/dataCore/evseCfgSchedule/getByPriceSchemeId")
    public ListResponse<PriceSchemeSiteVo> getByPriceSchemeId(
            ServerHttpRequest request,
            @RequestBody ListPriceSchemeSiteUseParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false));
        return RestUtils.buildListResponse(this.evseCfgScheduleService.getByPriceSchemeId(param));
    }

    /**
     * 过滤可以下发的桩编号
     *
     * @param evseNoList
     * @return
     */
    @Operation(summary = "过滤可以下发的桩编号")
    @PostMapping("/dataCore/evseCfgSchedule/downFilter")
    public ListResponse<String> downFilter(
            ServerHttpRequest request,
            @RequestBody List<String> evseNoList) {
        log.info(LoggerHelper2.formatEnterLog(request, false));
        return RestUtils.buildListResponse(this.evseCfgScheduleService.downFilter(evseNoList));
    }

    @Operation(summary = "查看对应桩是否存在定时任务，优则返回定时信息")
    @PostMapping("/dataCore/evseCfgSchedule/getByEvseNo")
    public ListResponse<EvseCfgScheduleVo> getByEvseNo(
            ServerHttpRequest request,
            @RequestBody List<String> evseNoList) {
        log.info(LoggerHelper2.formatEnterLog(request, false));
        return RestUtils.buildListResponse(this.evseCfgScheduleService.getByEvseNo(evseNoList));
    }

    @Operation(summary = "disable桩配置下发定时记录")
    @PostMapping(value = "/dataCore/priceTemp/disableSchedule")
    public BaseResponse disableSchedule(@RequestBody List<String> evseNoList,
                                        ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false));
        this.evseCfgScheduleService.disableScheduleByEvseNo(null , evseNoList);
        return RestUtils.success();
    }

    @Operation(summary = "更新桩配置下发定时的计费模板Id")
    @PostMapping(value = "/dataCore/priceTemp/updatePriceSchedule")
    public BaseResponse updatePriceSchedule(@RequestBody List<Long> tempIdList,
                                            ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = " + tempIdList);
        int i = this.evseCfgScheduleService.updatePriceSchedule(tempIdList);
        log.info("result = {}", i);
        return RestUtils.success();
    }

    @Operation(summary = "创建下发电价")
    @PostMapping(value = "/dataCore/createPriceSchedule")
    public BaseResponse createPriceSchedule(@RequestBody ModifyEvseCfgParam param,
        ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param = " + param);
        this.evseCfgScheduleService.createPriceSchedule(param);
        return RestUtils.success();
    }
}
