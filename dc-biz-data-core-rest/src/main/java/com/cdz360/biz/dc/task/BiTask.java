package com.cdz360.biz.dc.task;

import com.cdz360.biz.dc.service.CommBiService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class BiTask {

    @Autowired
    private CommBiService commBiService;

    @Scheduled(cron = "1 * * * * ?" ) // 1分钟执行一次
    public void topCommBiTask() {
        this.commBiService.addTopCommBi();
    }
}
