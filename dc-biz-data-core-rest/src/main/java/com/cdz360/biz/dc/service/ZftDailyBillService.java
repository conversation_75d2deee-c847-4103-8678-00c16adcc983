package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.auth.zft.param.ListZftParam;
import com.cdz360.biz.auth.zft.vo.ZftVo;
import com.cdz360.biz.dc.config.BillCheckCfg;
import com.cdz360.biz.dc.config.BillCheckCfg.CommInfo;
import com.cdz360.biz.dc.config.MchBillRegexConfig;
import com.cdz360.biz.dc.service.bill.DailyBillDealService;
import com.cdz360.biz.ds.trading.ro.bill.ds.ZftDailyBillRoDs;
import com.cdz360.biz.ds.trading.rw.bill.ds.ZftDailyBillRwDs;
import com.cdz360.biz.model.trading.bill.param.ListZftDailyBillParam;
import com.cdz360.biz.model.trading.bill.param.NotifyDailyBillParam;
import com.cdz360.biz.model.trading.bill.param.QueryBillParam;
import com.cdz360.biz.model.trading.bill.po.ZftDailyBillPo;
import com.cdz360.biz.model.trading.bill.type.DailyBillStatus;
import com.cdz360.biz.model.trading.bill.vo.ZftDailyBillVo;
import com.cdz360.biz.utils.feign.auth.AuthZftFeignClient;
import com.cdz360.biz.utils.feign.pcp.PcpAsyncFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class ZftDailyBillService {

    private static final String WX_SUB_MCH_ID_NONE = "NONE";

    @Autowired
    private MchBillRegexConfig mchBillRegexConfig;

    private static final DateTimeFormatter FORMAT_yyyy_MM_dd = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final SimpleDateFormat SIMPLE_FORMAT_yyyy_MM_dd = new SimpleDateFormat("yyyy-MM-dd");
    private static final DateTimeFormatter FORMAT_yyyy_MM_dd_HH_mm_ss = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private BillCheckCfg billCheckCfg;
    @Autowired
    private ZftDailyBillRoDs zftDailyBillRoDs;

    @Autowired
    private ZftDailyBillRwDs zftDailyBillRwDs;

    @Autowired
    private AuthZftFeignClient authZftFeignClient;

    @Autowired
    private PcpAsyncFeignClient pcpAsyncFeignClient;

    @Autowired
    private DailyBillDealService dailyBillDealService;

    public Mono<ListResponse<ZftDailyBillVo>> findAllZftDailyBill(ListZftDailyBillParam param) {
        return Mono.just(param)
                .map(zftDailyBillRoDs::findAllZftDailyBill)
                .map(this::map2Vo)
                .map(RestUtils::buildListResponse)
                .doOnNext(res -> {
                    if (null != param.getTotal() && param.getTotal()) {
                        res.setTotal(zftDailyBillRoDs.count(param));
                    }
                });
    }

    private List<ZftDailyBillVo> map2Vo(List<ZftDailyBillPo> poList) {
        return poList.stream().map(this::map2Vo).collect(Collectors.toList());
    }

    private ZftDailyBillVo map2Vo(ZftDailyBillPo po) {
        ZftDailyBillVo vo = new ZftDailyBillVo();
        vo.setId(po.getId())
                .setName(po.getName())
                .setStatus(po.getStatus())
                .setBillDate(po.getBillDate())
                .setChannel(po.getChannel())
                .setZftId(po.getZftId())
                .setZftName(po.getZftName())
                .setZftCommId(po.getZftCommId())
                .setDownloadUrl(po.getDownloadUrl());
        return vo;
    }

    @Async
    public void downloadZftDailyBill(String uuid) {
        log.info("触发账单下载: uuid = {}", uuid);
        ListZftParam param = new ListZftParam();
        param.setStart(0L)
                .setSize(100);

        // 集团商户，直付商户
        Map<Long, List<ZftVo>> aliMchMap = new HashMap<>();
        LocalDateTime billDate = LocalDate.now().minusDays(1).atStartOfDay();
        while (true) {
            List<ZftVo> zftList = authZftFeignClient.zftList("", param)
                    .doOnNext(FeignResponseValidate::check)
                    .map(ListResponse::getData)
                    .doOnNext(list ->
                            log.info("当前页: start = {}, size = {}, size = {}",
                                    param.getStart(), param.getSize(), list.size())
                    )
                    .block(Duration.ofSeconds(50L));

            boolean again = false;
            if (CollectionUtils.isNotEmpty(zftList)) {
                // 微信各直付商分开查询
                zftList.stream()
                        .filter(zft -> StringUtils.isNotBlank(zft.getWxSubMchId())
                            && !WX_SUB_MCH_ID_NONE.equals(zft.getWxSubMchId()))
                        .forEach(zft -> this.wxDailyBillDownload(zft, billDate));

                // 支付宝合并查询，下载后会分开存储
                zftList.stream()
                        .filter(zft -> StringUtils.isNotBlank(zft.getAlipaySubMchId()))
                        .forEach(zftVo -> {
                            Long topCommId = zftVo.getTopCommId();
                            if (aliMchMap.containsKey(topCommId)) {
                                aliMchMap.get(topCommId).add(zftVo);
                            } else {
                                aliMchMap.put(topCommId, new ArrayList<>() {{
                                    add(zftVo);
                                }});
                            }
                        });

                again = zftList.size() == param.getSize() &&
                        (param.getStart() + param.getSize()) < 1000;
            }

            if (again) {
                param.setStart(param.getStart() + param.getSize());
            } else {
                break;
            }
        }

        // 支付宝账单
        this.aliDailyBillDownload(aliMchMap, billDate);

        // 微信普通商户对账
        this.wxOrdinaryBillDownload(billDate);
    }

    /**
     * 微信普通商户对账
     * @param billDate
     */
    public void wxOrdinaryBillDownload(LocalDateTime billDate) {
        if (CollectionUtils.isEmpty(billCheckCfg.getOrdinaryCommList())) {
            log.info("未配置需要对账的商户");
            return;
        }
        String billDateStr = billDate.format(FORMAT_yyyy_MM_dd);
        billCheckCfg.getOrdinaryCommList().forEach(e -> {
            QueryBillParam param = new QueryBillParam();
            param.setTopCommId(e.getTopCommId())
                .setZftName(e.getName())
                .setProvider(Boolean.FALSE)
                .setBillDate(billDateStr);

            // 账单名称: 直付商家名 + 交易渠道 + 账期
            String billName = e.getName() + "微信" + param.getBillDate().replaceAll("-", "");
            ZftDailyBillPo po = new ZftDailyBillPo();
            po.setName(billName)
                .setMchId(null)
                .setZftCommId(e.getTopCommId())
                .setZftName(e.getName())
                .setZftId(null)
                .setStatus(DailyBillStatus.LOADING)
                .setChannel(PayChannel.WXPAY)
                .setBillDate(Date.from(
                    billDate.atZone(ZoneId.systemDefault()).toInstant()));
            boolean b = zftDailyBillRwDs.insertZftDailyBill(po);
            if (b) {
                param.setDailyBillId(po.getId());
            } else {
                log.error("创建对账单异常: po = {}", JsonUtils.toJsonString(po));
                throw new DcServiceException("创建对账单异常");
            }

            pcpAsyncFeignClient.queryBill(param)
                .doOnNext(FeignResponseValidate::check)
                .doOnNext(res -> log.debug("dailyBillId = {}", param.getDailyBillId()))
                .block(Duration.ofSeconds(50L));
        });
    }

    /**
     * 支付宝账单
     * @param aliMchMap
     * @param billDate
     */
    public void aliDailyBillDownload(Map<Long, List<ZftVo>> aliMchMap, LocalDateTime billDate) {
        String billDateStr = billDate.format(FORMAT_yyyy_MM_dd);

        aliMchMap.forEach((key, value) -> {
            try {
                // 新增账单
                List<QueryBillParam.ZftMchInfo> mchInfoList = value.stream().map(zftVo -> {
                    QueryBillParam.ZftMchInfo mchInfo = new QueryBillParam.ZftMchInfo();
                    mchInfo.setMchId(zftVo.getAlipaySubMchId())
                            .setZftName(zftVo.getName());

                    // 账单名称: 直付商家名 + 交易渠道 + 账期
                    String billName = zftVo.getName() + "支付宝" + billDateStr.replaceAll("-", "");
                    ZftDailyBillPo po = new ZftDailyBillPo();
                    po.setName(billName)
                            .setMchId(zftVo.getAlipaySubMchId())
                            .setZftCommId(zftVo.getCommId())
                            .setZftName(zftVo.getName())
                            .setZftId(zftVo.getId())
                            .setStatus(DailyBillStatus.LOADING)
                            .setChannel(PayChannel.ALIPAY)
                            .setBillDate(Date.from(
                                    billDate.atZone(ZoneId.systemDefault()).toInstant()));
                    boolean b = zftDailyBillRwDs.insertZftDailyBill(po);
                    if (b) {
                        mchInfo.setDailyBillId(po.getId());
                    } else {
                        log.error("创建对账单异常: po = {}", JsonUtils.toJsonString(po));
                        throw new DcServiceException("创建对账单异常");
                    }
                    return mchInfo;
                }).collect(Collectors.toList());

                QueryBillParam queryBillParam = new QueryBillParam();
                queryBillParam.setBillDate(billDate.format(FORMAT_yyyy_MM_dd))
                    .setTopCommId(key)
                    .setProvider(Boolean.TRUE)
                    .setAliMchInfoList(mchInfoList)
                    .setCorpMchInfoList(this.getCorpMchList(key)); // 集团商户下特殊对账

                pcpAsyncFeignClient.queryBill(queryBillParam)
                        .doOnNext(FeignResponseValidate::check)
                        .doOnNext(res -> log.debug("aliMchId = {}", queryBillParam.getAliMchInfoList()))
                        .block(Duration.ofSeconds(50L));
            } catch (Exception e) {
                log.error("支付宝下载对账单异常: err = {}", e.getMessage());
            }
        });
    }

    private List<QueryBillParam.CorpMchInfo> getCorpMchList(Long topCommId) {
        if (CollectionUtils.isEmpty(mchBillRegexConfig.getRegList())) return null;

        return mchBillRegexConfig.getRegList().stream()
                .filter(i -> topCommId.equals(i.getTopCommId()) && CollectionUtils.isNotEmpty(i.getCorpMchInfoList()))
                .map(i -> i.getCorpMchInfoList().stream()
                        .map(mchReg -> {
                            QueryBillParam.CorpMchInfo res = new QueryBillParam.CorpMchInfo();
                            BeanUtils.copyProperties(mchReg, res);
                            return res;
                        }).collect(Collectors.toList()))
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    public void wxDailyBillDownload(ZftVo zftVo, LocalDateTime billDate) {
        try {
            QueryBillParam queryBillParam = new QueryBillParam();

            QueryBillParam.ZftMchInfo mchInfo = new QueryBillParam.ZftMchInfo();
            mchInfo.setMchId(zftVo.getWxSubMchId())
                    .setZftName(zftVo.getName());
            queryBillParam.setBillDate(billDate.format(FORMAT_yyyy_MM_dd))
                .setTopCommId(zftVo.getTopCommId())
                .setProvider(Boolean.TRUE)
                .setWxMchInfo(mchInfo);

            Mono.just(queryBillParam)
                    .doOnNext(param -> {
                        // 账单名称: 直付商家名 + 交易渠道 + 账期
                        String billName = zftVo.getName() + "微信" + param.getBillDate().replaceAll("-", "");
                        ZftDailyBillPo po = new ZftDailyBillPo();
                        po.setName(billName)
                                .setMchId(zftVo.getWxSubMchId())
                                .setZftCommId(zftVo.getCommId())
                                .setZftName(zftVo.getName())
                                .setZftId(zftVo.getId())
                                .setStatus(DailyBillStatus.LOADING)
                                .setChannel(PayChannel.WXPAY)
                                .setBillDate(Date.from(
                                        billDate.atZone(ZoneId.systemDefault()).toInstant()));
                        boolean b = zftDailyBillRwDs.insertZftDailyBill(po);
                        if (b) {
                            param.getWxMchInfo().setDailyBillId(po.getId());
                        } else {
                            log.error("创建对账单异常: po = {}", JsonUtils.toJsonString(po));
                            throw new DcServiceException("创建对账单异常");
                        }
                    })
                    .flatMap(pcpAsyncFeignClient::queryBill)
                    .doOnNext(FeignResponseValidate::check)
                    .doOnNext(res -> log.debug("wxSubMchId = {}", queryBillParam.getWxMchInfo().getMchId()))
                    .block(Duration.ofSeconds(50L));
        } catch (Exception e) {
            log.error("微信下载对账单异常: err = {}", e.getMessage());
        }
    }

    private void notifyRegexDailyBill(String uuid, NotifyDailyBillParam param) {
        log.info("指定对账规则对账: uuid = {}, param = {}", uuid, JsonUtils.toJsonString(param));
        // FIEME: 重复调用没有限制，有问题
        this.dailyBillDealService.dealWithRegexDailyBill(param);
    }

    @Async
    public void notifyDailyBill(String uuid, NotifyDailyBillParam param) {
        if(null != param.getCorpId()) {
            this.notifyRegexDailyBill(uuid, param);
            return;
        }

        log.info("对账: uuid = {}, param = {}", uuid, JsonUtils.toJsonString(param));
        ZftDailyBillPo dailyBillPo = zftDailyBillRoDs.getById(param.getDailyBillId());
        if (null == dailyBillPo) {
            log.error("对账单不存在: dailyBill = {}", param.getDailyBillId());
            return;
        }

        if (DailyBillStatus.CHECKING.equals(dailyBillPo.getStatus())) {
            log.warn("当前对账单正在对账中: {}", JsonUtils.toJsonString(dailyBillPo));
            return;
        }

        if (DailyBillStatus.CHECK_SUCCESS.equals(dailyBillPo.getStatus())) {
            log.warn("当前对账单不需要再对账: {}", JsonUtils.toJsonString(dailyBillPo));
            return;
        }

        this.dailyBillDealService.dealWithDailyBill(dailyBillPo, param);
    }

    @Transactional
    public Mono<BaseResponse> retryCheckBill(Long topCommId, Long dailyBillId) {
        ZftDailyBillPo dailyBillPo = zftDailyBillRwDs.getById(dailyBillId, true);
        if (null == dailyBillPo) {
            log.warn("对账单不存在: dailyBill = {}", dailyBillId);
            throw new DcArgumentException("该账单无效，请刷新后重试");
        }

        List<DailyBillStatus> list = List.of(DailyBillStatus.CHECK_FAIL, DailyBillStatus.LOAD_FAIL);
        if (!list.contains(dailyBillPo.getStatus())) {
            log.warn("该账单状态不正确，不允许重试: status = {}", dailyBillPo.getStatus());
            throw new DcArgumentException("该账单状态不正确，不允许重试");
        }

        dailyBillPo.setStatus(DailyBillStatus.LOADING);
        boolean b = zftDailyBillRwDs.updateZftDailyBill(dailyBillPo);

        QueryBillParam queryBillParam = this.generateBillParam(topCommId, dailyBillPo);

        return pcpAsyncFeignClient.queryBill(queryBillParam)
                .doOnNext(FeignResponseValidate::check)
                .doOnNext(res -> log.debug("重试 = {}", JsonUtils.toJsonString(queryBillParam)));
    }

    private QueryBillParam generateBillParam(@NonNull Long topCommId, @NonNull ZftDailyBillPo dailyBillPo) {
        QueryBillParam queryBillParam = new QueryBillParam();
        queryBillParam.setBillDate(SIMPLE_FORMAT_yyyy_MM_dd.format(dailyBillPo.getBillDate()))
            .setTopCommId(topCommId);

        if (PayChannel.WXPAY.equals(dailyBillPo.getChannel())) {
            Optional<CommInfo> optional = Optional.empty();
            if (billCheckCfg != null
                && CollectionUtils.isNotEmpty(billCheckCfg.getOrdinaryCommList())) {
                optional = billCheckCfg.getOrdinaryCommList().stream()
                    .filter(e -> e.getTopCommId().equals(dailyBillPo.getZftCommId())
                        && e.getName().equals(dailyBillPo.getZftName()))
                    .findFirst();
            }

            optional.ifPresentOrElse(e -> {
                queryBillParam.setProvider(Boolean.FALSE)
                    .setZftName(dailyBillPo.getZftName())
                    .setDailyBillId(dailyBillPo.getId());
            }, () -> {
                queryBillParam.setProvider(Boolean.TRUE);
                queryBillParam.setWxMchInfo(this.generateSubMchInfo(dailyBillPo));
            });
        } else {
            queryBillParam.setProvider(Boolean.TRUE);
            queryBillParam.setSplitOne(true);
            queryBillParam.setAliMchInfoList(List.of(this.generateSubMchInfo(dailyBillPo)));
        }
        return queryBillParam;
    }

    private QueryBillParam.ZftMchInfo generateSubMchInfo(@NonNull ZftDailyBillPo dailyBillPo) {
        QueryBillParam.ZftMchInfo mchInfo = new QueryBillParam.ZftMchInfo();
        mchInfo.setMchId(dailyBillPo.getMchId())
            .setZftName(dailyBillPo.getZftName())
            .setDailyBillId(dailyBillPo.getId());
        return mchInfo;
    }

}
