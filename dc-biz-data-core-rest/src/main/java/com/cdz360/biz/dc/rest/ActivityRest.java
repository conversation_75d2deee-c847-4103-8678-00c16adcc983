package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.dc.service.ActivityService;
import com.cdz360.biz.model.trading.coupon.param.ActivityUserCouponParam;
import com.cdz360.biz.model.trading.coupon.param.CouponSearchParam;
import com.cdz360.biz.model.trading.coupon.param.CreateActivityParam;
import com.cdz360.biz.model.trading.coupon.param.ListActivityParam;
import com.cdz360.biz.model.trading.coupon.param.NewGuestAcquireParam;
import com.cdz360.biz.model.trading.coupon.param.UpdateActivityParam;
import com.cdz360.biz.model.trading.coupon.po.CouponPo;
import com.cdz360.biz.model.trading.coupon.type.AcquireCouponResult;
import com.cdz360.biz.model.trading.coupon.vo.ActivityVo;
import com.cdz360.biz.model.trading.coupon.vo.CouponBi;
import com.cdz360.biz.model.trading.coupon.vo.CouponVo;
import com.cdz360.biz.model.trading.coupon.vo.DiscountVo;
import java.math.BigDecimal;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class ActivityRest {

    @Autowired
    private ActivityService activityService;


    @PostMapping(value = "/dataCore/activity/createActivity")
    public BaseResponse createActivity(@RequestBody CreateActivityParam req) {
        activityService.createActivity(req);
        return BaseResponse.success();
    }

    @PostMapping(value = "/dataCore/activity/updateActivity")
    public BaseResponse updateActivity(@RequestBody UpdateActivityParam req) {
        activityService.updateActivity(req);
        return BaseResponse.success();
    }

    @GetMapping(value = "/dataCore/activity/activeActivity")
    public BaseResponse activeActivity(@RequestParam("id") Long id) {
        activityService.activeActivity(id);
        return BaseResponse.success();
    }

    @GetMapping(value = "/dataCore/activity/abortActivity")
    public BaseResponse abortActivity(@RequestParam("id") Long id) {
        activityService.abortActivity(id);
        return BaseResponse.success();
    }

    @GetMapping(value = "/dataCore/activity/showInMobile")
    public BaseResponse showInMobile(@RequestParam("id") Long id) {
        activityService.showInMobile(id);
        return BaseResponse.success();
    }

    @GetMapping(value = "/dataCore/activity/hideInMobile")
    public BaseResponse hideInMobile(@RequestParam("id") Long id) {
        activityService.hideInMobile(id);
        return BaseResponse.success();
    }

    @GetMapping(value = "/dataCore/activity/getActivityDetail")
    public ObjectResponse<ActivityVo> getActivityDetail(@RequestParam("id") Long id) {
        return new ObjectResponse<>(activityService.getActivityDetail(id));
    }

    @GetMapping(value = "/dataCore/activity/couponBi")
    public ObjectResponse<CouponBi> couponBi(@RequestParam("id") Long id) {
        return activityService.couponBi(id);
    }

    @PostMapping(value = "/dataCore/activity/userActivityCouponList")
    public ListResponse<CouponVo> userActivityCouponList(@RequestBody CouponSearchParam req) {
        return activityService.userActivityCouponList(req);
    }

    /**
     * 领券
     *
     * @param phone
     * @param activityId
     * @return
     */
    @RequestMapping(value = "/dataCore/activity/acquireCoupon", method = RequestMethod.GET)
    public ObjectResponse<AcquireCouponResult> acquireCoupon(@RequestParam("activityId") Long activityId,
        @RequestParam("phone") String phone) {
        return activityService.acquireCoupon(activityId, phone, null);
    }

    @RequestMapping(value = "/dataCore/activity/acquireCouponByUserId", method = RequestMethod.GET)
    public ObjectResponse<AcquireCouponResult> acquireCoupon(@RequestParam("activityId") Long activityId,
        @RequestParam("userId") Long userId) {
        return activityService.acquireCoupon(activityId, null, userId);
    }

    /**
     * 新客领券
     *
     * @return
     */
    @PostMapping(value = "/dataCore/activity/newGuestAcquireCoupon")
    public ObjectResponse<AcquireCouponResult> newGuestAcquireCoupon(
        @RequestBody NewGuestAcquireParam param) {
        return activityService.newGuestAcquireCoupon(param);
    }

    @PostMapping(value = "/dataCore/activity/countByCondition")
    public ObjectResponse<Long> countByCondition(@RequestBody CouponPo po) {
        return activityService.countByCondition(po);
    }

    @PostMapping(value = "/dataCore/activity/listActivity")
    public ListResponse<ActivityVo> listActivity(@RequestBody ListActivityParam req) {
        return activityService.listActivity(req);
    }

    /**
     * 商户会员充值满赠信息
     * @param commId
     * @param amount
     * @return
     */
    @GetMapping(value = "/dataCore/activity/discountInfo")
    public ObjectResponse<DiscountVo> discountInfo(@RequestParam("commId") Long commId,
        @RequestParam("amount") BigDecimal amount) {
        log.info("满送信息,commId={},amount={}", commId, amount);
        return activityService.discountInfo(commId,amount);
    }

    @GetMapping(value = "/dataCore/activity/hasActivity")
    public ObjectResponse<ActivityVo> hasActivity(@RequestParam("commId") Long commId,
        @RequestParam("accountType") Long accountType) {
        log.info("是否存在同类的活动信息,commId={},amount={}", commId, accountType);
        return activityService.hasActivity(commId,accountType);
    }

    /**
     * 用户手机号是否领取过优惠券统计
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/activity/hasUserAcquiredCoupon")
    public ObjectResponse<Map<String, Boolean>> hasUserAcquiredCoupon(
        @RequestBody ActivityUserCouponParam param){
        return activityService.hasUserAcquiredCoupon(param);
    }
}
