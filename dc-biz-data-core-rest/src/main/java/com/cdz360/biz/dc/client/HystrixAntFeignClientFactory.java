package com.cdz360.biz.dc.client;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.sys.param.AccRelativeParam;
import com.cdz360.biz.auth.sys.vo.AccRelativeVo;
import com.cdz360.biz.model.cus.message.po.MessagePo;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.trading.site.param.FetchTargetPriceSchemeParam;
import com.cdz360.biz.oa.dto.TargetPriceSchemeInfo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @since 2018/11/25.
 */
@Slf4j
@Component
public class HystrixAntFeignClientFactory implements FallbackFactory<AntFeignClient> {
    @Override
    public AntFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_AUTH, throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_AUTH, throwable.getStackTrace());

        return new AntFeignClient() {

            @Override
            public Mono<ListResponse<TargetPriceSchemeInfo>> fetchTargetPriceScheme(
                FetchTargetPriceSchemeParam param) {
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }
        };
    }
}
