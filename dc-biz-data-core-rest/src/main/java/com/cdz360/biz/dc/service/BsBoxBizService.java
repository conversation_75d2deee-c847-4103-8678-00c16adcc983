package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.biz.dc.domain.hlht.HlhtSyncSiteDto;
import com.cdz360.biz.dc.domain.hlht.HlhtSyncSiteDto.Plug;
import com.cdz360.biz.ds.trading.ro.iot.ds.BsBoxRoDs;
import com.cdz360.biz.ds.trading.rw.iot.ds.BsBoxRwDs;
import com.cdz360.biz.ds.trading.rw.iot.ds.BsChargerRwDs;
import com.cdz360.biz.model.trading.hlht.dto.CecQueryQeuipBusinessPolicyResult;
import com.cdz360.biz.model.trading.iot.param.DelBsBoxParam;
import com.cdz360.biz.model.trading.iot.param.DelBsChargerParam;
import com.cdz360.biz.model.trading.iot.po.BsBoxPo;
import com.cdz360.biz.model.trading.iot.po.BsChargerPo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class BsBoxBizService {

    @Autowired
    private BsBoxRoDs bsBoxRoDs;

    @Autowired
    private BsBoxRwDs bsBoxRwDs;

    @Autowired
    private BsChargerRwDs bsChargerRwDs;


    @Transactional
    public void syncBsBoxList(SitePo site, List<HlhtSyncSiteDto.Evse> evseList, CecQueryQeuipBusinessPolicyResult policyInfoResult) {
        // BsBoxPo param = new BsBoxPo();
        // param.setSiteId(site.getSiteId());

        // List<BsBoxPo> existsBox = bsBoxRoDs.findByCondition(param);
        List<HlhtSyncSiteDto.Evse> collect = evseList.stream().map(inEvse -> {
            // 区分多个集团商户互联场站桩
            HlhtSyncSiteDto.Evse evse = new HlhtSyncSiteDto.Evse();
            BeanUtils.copyProperties(inEvse, evse);
            evse.setEvseNo(site.getTopCommId() + ":" + inEvse.getEvseNo());
            if (policyInfoResult != null) {
                evse.setEvsePolicyFlag(evse.getPlugList().stream().map(Plug::getPlugNo).collect(Collectors.toList()).contains(policyInfoResult.getConnectorID()));
            } else {
                evse.setEvsePolicyFlag(false);
            }
            evse.getPlugList().forEach(plug -> plug.setPlugNo(site.getTopCommId() + ":" + plug.getPlugNo()));
            return evse;
        }).collect(Collectors.toList());
        collect.forEach(inEvse -> syncBsBox(site.getOperateId(), site.getId(), site.getTemplateId() ,inEvse));

        // 删除不存在的桩
        List<String> evseNoList = collect.stream()
                .map(HlhtSyncSiteDto.Evse::getEvseNo)
                .collect(Collectors.toList());
        DelBsBoxParam param = new DelBsBoxParam();
        param.setSiteId(site.getId()).setEvseNoList(evseNoList);
        int i = bsBoxRwDs.delBsBoxNotExist(param);

        DelBsChargerParam delBsChargerParam = new DelBsChargerParam();
        delBsChargerParam.setSiteId(site.getId()).setEvseNoList(evseNoList);
        bsChargerRwDs.delBsChargerNotExist(delBsChargerParam);
    }

    private void syncBsBox(Long siteCommId, String siteId, Long priceCode, HlhtSyncSiteDto.Evse in) {
        BsBoxPo box = this.bsBoxRoDs.getBsBox(in.getEvseNo());
        if (box == null) {
            box = new BsBoxPo();
            box.setSiteId(siteId)
                    .setBusinessId(String.valueOf(siteCommId))
                    .setPlugNum(in.getPlugList().size())
                    .setEvseNo(in.getEvseNo())
                    .setEvseName(in.getEvseName())
                    .setPower(in.getPower())
                    .setCurrentType(in.getSupplyType() == SupplyType.DC ? 1 : 0);
            if (priceCode != null) {
                box.setPriceCode(priceCode);
            }
            this.bsBoxRwDs.insertBsBox(box);
        } else {
            box.setSiteId(siteId)
                    .setBusinessId(String.valueOf(siteCommId))
                    .setPlugNum(in.getPlugList().size())
                    .setEvseName(in.getEvseName())
                    .setPower(in.getPower())
                    .setCurrentType(in.getSupplyType() == SupplyType.DC ? 1 : 0);
            if (priceCode != null && (in.getEvsePolicyFlag() || box.getPriceCode() == null)) {
                box.setPriceCode(priceCode);
            }
            this.bsBoxRwDs.updateBsBox(box);
        }

        bsChargerRwDs.delBsCharger(box.getEvseNo());
        int idx = 1;
        for (HlhtSyncSiteDto.Plug p : in.getPlugList()) {
            syncBsCharger(box, idx, p);
            idx += 1;
        }
    }


    private void syncBsCharger(BsBoxPo box, int idx, HlhtSyncSiteDto.Plug in) {
        BsChargerPo ch = new BsChargerPo();
        ch.setChargerName(in.getPlugName())
                .setBusinessId(box.getBusinessId())
                .setEvseNo(box.getEvseNo())
                .setPlugNo(in.getPlugNo())
                .setConnectorId(idx) // TODO: 2020/10/13 暂且这样
                .setStationCode(box.getSiteId())
                .setCurrentType(box.getCurrentType())
                .setLastModifyTime(new Date());
        bsChargerRwDs.insertBsCharger(ch);
    }
}
