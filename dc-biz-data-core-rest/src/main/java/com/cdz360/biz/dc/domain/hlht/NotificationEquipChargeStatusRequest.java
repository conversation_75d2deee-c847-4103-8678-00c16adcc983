package com.cdz360.biz.dc.domain.hlht;
//
//import com.alibaba.fastjson.annotation.JSONField;
//import com.fasterxml.jackson.annotation.JsonProperty;
//import lombok.Data;
//
//import java.math.BigDecimal;
//
///**
// * <AUTHOR>
// * :
// * @since 2019-05-01 13:44
// */
//@Data
//public class NotificationEquipChargeStatusRequest {
//
//    private static final long serialVersionUID = 1L;
//
//    /**
//     * 运营商ID
//     */
//    @JSONField(name = "OperatorID")
//    @JsonProperty(value = "OperatorID")
//    private String operatorId;
//
//    /**
//     * 充电订单号
//     */
//    @JSONField(name = "StartChargeSeq")
//    @JsonProperty(value = "StartChargeSeq")
//    private String startChargeSeq;
//    /**
//     * 充电订单状态
//     */
//    @JSONField(name = "StartChargeSeqStat")
//    @JsonProperty(value = "StartChargeSeqStat")
//    private Integer startChargeSeqStat;
//    /**
//     * 充电设备接口编码
//     */
//    @JSONField(name = "ConnectorID")
//    @JsonProperty(value = "ConnectorID")
//    private String connectorID;
//    /**
//     * 充电设备接口状态 1.空闲，2占用（未充电），3.占用（充电中），4.占用（预约锁定），255.故障
//     */
//    @JSONField(name = "ConnectorStatus")
//    @JsonProperty(value = "ConnectorStatus")
//    private Integer connectorStatus;
//    /**
//     * A相电流
//     */
//    @JSONField(name = "CurrentA")
//    @JsonProperty(value = "CurrentA")
//    private BigDecimal currentA;
//    /**
//     * B相电流
//     */
//    @JSONField(name = "CurrentB")
//    @JsonProperty(value = "CurrentB")
//    private BigDecimal currentB;
//    /**
//     * C相电流
//     */
//    @JSONField(name = "CurrentC")
//    @JsonProperty(value = "CurrentC")
//    private BigDecimal currentC;
//    /**
//     * A相电压
//     */
//    @JSONField(name = "VoltageA")
//    @JsonProperty(value = "VoltageA")
//    private BigDecimal voltageA;
//    /**
//     * B相电压
//     */
//    @JSONField(name = "VoltageB")
//    @JsonProperty(value = "VoltageB")
//    private BigDecimal voltageB;
//    /**
//     * C相电压
//     */
//    @JSONField(name = "VoltageC")
//    @JsonProperty(value = "VoltageC")
//    private BigDecimal voltageC;
//    /**
//     * 电池剩余电量
//     */
//    @JSONField(name = "Soc")
//    @JsonProperty(value = "Soc")
//    private Double soc;
//    /**
//     * 开启充电时间
//     */
//    @JSONField(name = "StartTime")
//    @JsonProperty(value = "StartTime")
//    private String startTime;
//    /**
//     * 本次采样时间
//     */
//    @JSONField(name = "EndTime")
//    @JsonProperty(value = "EndTime")
//    private String endTime;
//    /**
//     * 累计充电量
//     */
//    @JSONField(name = "TotalPower")
//    @JsonProperty(value = "TotalPower")
//    private BigDecimal totalPower;
//    /**
//     * 累计电费
//     */
//    @JSONField(name = "ElecMoney")
//    @JsonProperty(value = "ElecMoney")
//    private BigDecimal elecMoney;
//    /**
//     * 累计服务费
//     */
//    @JSONField(name = "SeviceMoney")
//    @JsonProperty(value = "SeviceMoney")
//    private BigDecimal seviceMoney;
//    /**
//     * 累计总金额
//     */
//    @JSONField(name = "TotalMoney")
//    @JsonProperty(value = "TotalMoney")
//    private BigDecimal totalMoney;
//    /**
//     * 时段数N
//     */
//    @JSONField(name = "SumPeriod")
//    @JsonProperty(value = "SumPeriod")
//    private Integer sumPeriod;
//
//}
