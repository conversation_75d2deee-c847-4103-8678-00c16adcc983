package com.cdz360.biz.dc.client.reactor;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.user.po.SysUserPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

/**
 * ReactorUserFeignClientHystrixFactory
 *
 * @since 11/3/2020 10:32 AM
 * <AUTHOR>
 */
@Slf4j
@Component
public class ReactorAuthCenterFeignClientHystrixFactory implements
    FallbackFactory<ReactorAuthCenterFeignClient> {

    @Override
    public ReactorAuthCenterFeignClient apply(Throwable throwable) {
        log.error("err= {}", throwable.getMessage(), throwable);
        return new ReactorAuthCenterFeignClient() {


            @Override
            public Mono<ObjectResponse<SysUserPo>> getSysUserById(Long id) {
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }
        };

    }
}