package com.cdz360.biz.dc.service;


import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.ds.trading.ro.warn.ds.WarningRoDs;
import com.cdz360.biz.ds.trading.rw.warn.ds.WarningRwDs;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import com.cdz360.biz.model.trading.warn.param.AddUserWarnParam;
import com.cdz360.biz.model.trading.warn.param.UserWarnListParam;
import com.cdz360.biz.model.trading.warn.param.WarnListParam;
import com.cdz360.biz.model.trading.warn.param.WarnSubParam;
import com.cdz360.biz.model.trading.warn.po.WarningPo;
import com.cdz360.biz.model.trading.warn.vo.AddUserWarnVo;
import com.cdz360.biz.model.trading.warn.vo.UserWarningVo;
import com.chargerlinkcar.framework.common.feign.AuthCenterFeignClient;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


@Slf4j
@Service
public class WarningService {

    @Autowired
    private WarningRoDs warningRoDs;
    @Autowired
    private WarningRwDs warningRwDs;
    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    /**
     * 支撑平台获取告警列表
     *
     * @param param
     * @return
     */
    public ListResponse<WarningPo> getWarnList(WarnListParam param) {
        Long total = warningRoDs.getWarnTotal(param);
        if (param.getStart() == null) {
            param.setStart(0L);
        }
        if (param.getSize() == null) {
            param.setSize(10);
        }
        List<WarningPo> list = new ArrayList<>();
        if (total != null) {
            list = warningRoDs.getWarnList(param);
        }
        return new ListResponse<>(list, total);
    }

    /**
     * 批量修改订阅（支撑平台）
     */
    public BaseResponse queryWarningSub(WarnSubParam param) {
        if (param.getEnable() == null) {
            throw new DcServiceException("订阅状态不能为空");
        }
        if (CollectionUtils.isEmpty(param.getCodeIdList())) {
            throw new DcServiceException("请选择告警信息");
        }
        warningRwDs.queryWarningSub(param);
        //不支持订阅时将管理员订阅取消
        if (param.getEnable().equals(false)) {
            warningRwDs.updateAllToUnEnableById(param.getCodeIdList());
        }
        return BaseResponse.success();
    }

    /**
     * 获取用户订阅列表
     *
     * @param param
     * @return
     */
    public ListResponse<UserWarningVo> getUserWarnList(UserWarnListParam param) {
        param.setEnable(true);
        if (param.getStart() == null) {
            param.setStart(0L);
        }
        if (param.getSize() == null) {
            param.setSize(10);
        }
        Long total = warningRoDs.getUserWarnTotal(param);
        List<UserWarningVo> list = new ArrayList<>();
        if (total > 0) {
            list = warningRoDs.getUserWarnList(param);
        }
        return new ListResponse<>(list, total);
    }

    /**
     * 用户订阅
     *
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse addOrUpdateUserWarn(AddUserWarnParam param) {
        log.info("param={}", param);
        if (param.getSysUid() == null) {
            throw new DcServiceException("参数错误");
        }
        if (CollectionUtils.isEmpty(param.getCodeList())) {
            throw new DcServiceException("订阅内容不能为空");
        }
        if (CollectionUtils.isEmpty(param.getSiteIdList())) {
            throw new DcServiceException("订阅站点不能为空");
        }
//        ObjectResponse<SysUserPo> sysUserPoObjectResponse =  authCenterFeignClient.getUserInfoById(param.getSysUid());
//        if (sysUserPoObjectResponse.getData()==null || StringUtils.isBlank(sysUserPoObjectResponse.getData().getWxOpenId())){
//            throw  new DcServiceException("尚未绑定公众号");
//        }
        try {
            //更新至不可订阅状态
            warningRwDs.updateAllToUnEnable(param.getSysUid());
            //组织数据
            List<AddUserWarnVo> list = new ArrayList<>();

            for (int i = 0; i < param.getSiteIdList().size(); i++) {

                for (int j = 0; j < param.getCodeList().size(); j++) {
                    AddUserWarnVo addUserWarnVo = new AddUserWarnVo();
                    addUserWarnVo.setSiteId(param.getSiteIdList().get(i));
                    addUserWarnVo.setSysUid(param.getSysUid());
                    addUserWarnVo.setWarnCode(param.getCodeList().get(j));
                    list.add(addUserWarnVo);
                }

            }
            //更新
            warningRwDs.addOrUpdateUserWarn(list);
            return BaseResponse.success();
        } catch (Exception e) {
            log.error("e.message={}", e.getMessage());
            throw new DcServiceException("操作失败");
        }
    }

    public ListResponse<String> getUserSubSiteList(Long sysUid) {
        List<String> list = warningRoDs.getUserSubSiteList(sysUid);
        return new ListResponse<>(list);
    }

    public ListResponse<SiteVo> getUserSubSiteInfoList(Long sysUid, Long start, Long size) {

        Long total = warningRoDs.getUserSubSiteCount(sysUid);
        if (total == null) {
            return new ListResponse<>(null, 0L);
        } else {
            return new ListResponse<>(warningRoDs.getUserSubSiteInfoList(sysUid, start, size),
                total);
        }
    }

    public ListResponse<String> getUserSubCodeList(Long sysUid) {
        List<String> list = warningRoDs.getUserSubCodeList(sysUid);
        return new ListResponse<>(list);
    }


}
