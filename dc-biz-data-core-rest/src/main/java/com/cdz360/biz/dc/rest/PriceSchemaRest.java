package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.service.PriceSchemaBizService;
import com.cdz360.biz.model.iot.param.ModifyEvseCfgParam;
import com.cdz360.biz.model.iot.vo.ChargeV2;
import com.cdz360.biz.model.site.po.PriceItemPo;
import com.cdz360.biz.model.site.po.PriceTemplatePo;
import com.cdz360.biz.model.site.vo.PriceTemplateModVo;
import com.cdz360.biz.model.trading.site.param.AddPriceSchemaParam;
import com.cdz360.biz.model.trading.site.param.ListPriceTemplateParam;
import com.cdz360.biz.model.trading.site.param.UpdatePriceSchemaParam;
import com.cdz360.biz.model.trading.site.vo.SiteChargePriceVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Tag(name = "价格模板相关接口", description = "价格模板")
public class PriceSchemaRest {

    @Autowired
    private PriceSchemaBizService priceSchemaBizService;

    @Operation(summary = "获取价格模板列表")
    @PostMapping("/dataCore/priceTemp/getPriceSchemaList")
    public ListResponse<PriceTemplatePo> getPriceSchemaList(
            ServerHttpRequest request,
            @RequestBody ListPriceTemplateParam param) {
        log.info("获取价格模板列表: {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), param);

        // 分页信息
        Page<PriceTemplatePo> page = PageHelper.offsetPage(0, 10);
        if (null != param.getStart()
                && null != param.getSize() && param.getSize() > 0) {
            page = PageHelper.offsetPage(param.getStart().intValue(), param.getSize());
        }
        log.info("分页: page = {}, size = {}", page.getPageNum(), page.getPageSize());

        var list = this.priceSchemaBizService.getPriceTemplateList(param);
        return RestUtils.buildListResponse(list, page == null ? 0 : (int) page.getTotal());
    }


    @PostMapping("/dataCore/priceTemp/addPriceSchema")
    @Operation(summary = "新增计费模板")
    public ObjectResponse<PriceTemplatePo> addPriceSchema(@RequestBody AddPriceSchemaParam param) {
        Assert.notNull(param.getName(), "模板名称不能为空");
        Assert.notNull(param.getCommId(), "代理商ID不能为空");
        Assert.notNull(param.getFreeChargeFlag(), "免费充电标识不能为空");
        PriceTemplatePo vo = priceSchemaBizService.addPriceSchema(param);
        log.info("新建计费模板: id = {}, code = {}", vo.getId(), vo.getCode());
        return RestUtils.buildObjectResponse(vo);
    }

    @PostMapping("/dataCore/priceTemp/updatePriceSchema")
    @Operation(summary = "更新计费模板")
    public ObjectResponse<Long> updatePriceSchema(@RequestBody UpdatePriceSchemaParam param) {
        log.info("更新计费模板: param = {}", param);
        Assert.notNull(param.getId(), "计费模板ID不能为空");
        Assert.notNull(param.getCommId(), "代理商ID不能为空");
        Assert.notNull(param.getFreeChargeFlag(), "免费充电标识不能为空");


        Long templateId = priceSchemaBizService.updatePriceSchema(param);

        // 同步mongodb中site_info表关联此计费模板的场站数据
//        siteMongoDataService.syncSite2Mongo(templateId);//只有下发记费模板，且模板作为场站计费信息时，才修改场站关联的价格模板
        return new ObjectResponse<>(templateId);
    }


    @Operation(summary = "计费模板启用/禁用")
    @GetMapping("/dataCore/priceTemp/enablePriceSchema")
    public BaseResponse enablePriceSchema(
            @Parameter(name = "用户token", required = true) @RequestParam(value = "token") String token,
            @Parameter(name = "计费模板Id", required = true) @RequestParam(value = "id") Long id,
            @Parameter(name = "计费模板使能状态: true--启用; false--禁用", required = true) @RequestParam(value = "enable") Boolean enable,
            ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request));
        this.priceSchemaBizService.enablePriceSchema(token, id, enable);
        return RestUtils.success();
    }


    @Operation(summary = "光伏收益计费模板批量删除")
    @PostMapping("/dataCore/priceTemp/deletePvPriceSchema")
    public BaseResponse deletePvPriceSchema(ServerHttpRequest request,
                                             @RequestBody List<Long> priceIdList) {
        log.info("光伏收益计费模板批量删除 priceIdList.size: {}", priceIdList.size());
        return this.priceSchemaBizService.deletePvPriceSchema(priceIdList);
    }

    @Operation(summary = "即时下发计费模板")
    @PostMapping("/dataCore/priceTemp/down")
    public BaseResponse priceTempDown(@RequestBody ModifyEvseCfgParam param,
                                      ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false));
        this.priceSchemaBizService.priceTempDown(param);
        return RestUtils.success();
    }

    @Operation(summary = "即时下发场站默认计费模板")
    @GetMapping("/dataCore/priceTemp/downDefault")
    public BaseResponse priceTempDownDefault(@RequestParam("evseNo") String evseNo) {
        this.priceSchemaBizService.priceTempDownDefault(evseNo);
        return RestUtils.success();
    }

    @Operation(summary = "获取场站计费模板信息")
    @GetMapping("/dataCore/priceTemp/getSitePriceList")
    public ListResponse<SiteChargePriceVo> getSitePriceList(@RequestParam("siteId") String siteId) {
        log.info("获取场站计费信息, siteId={}", siteId);
        return RestUtils.buildListResponse(priceSchemaBizService.getSitePriceList(siteId));
    }

    @Operation(summary = "修改桩计费模板-用于不支持计费下发的桩")
    @PostMapping("/dataCore/priceTemp/modifyEvsePrice")
    public BaseResponse modifyEvsePrice(@RequestBody ModifyEvseCfgParam param,
                                        ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false));
        this.priceSchemaBizService.modifyEvsePrice(param);
        return RestUtils.success();
    }

    @Operation(summary = "获取桩的计费模板信息")
    @GetMapping("/dataCore/priceTemp/getByEvesNo")
    public ObjectResponse<PriceTemplatePo> getPriceSchemeByEvesNo(
            @Parameter(name = "桩编号", required = true) @RequestParam(value = "evseNo") String evseNo,
            ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return RestUtils.buildObjectResponse(this.priceSchemaBizService.getPriceSchemeByEvesNo(evseNo));
    }

    @Operation(summary = "获取计费模板信息")
    @GetMapping("/dataCore/priceTemp/getByEvesNo2")
    public ObjectResponse<ChargePriceVo> getPriceSchemeByEvesNo2(
            @RequestParam("evseNo") String evseNo) {
        Assert.notNull(evseNo, "桩编号不能为空");
        ChargePriceVo templateInfo = priceSchemaBizService.getPriceSchemeByEvesNo2(evseNo);
        return new ObjectResponse<>(templateInfo);
    }

//    @Operation(summary = "获取桩的计费模板信息")
//    @GetMapping("/dataCore/priceTemp/getChargePriceVoByEvesNo")
//    public ObjectResponse<ChargePriceVo> getChargePriceVoByEvesNo(
//            @Parameter(name = "桩编号", required = true) @RequestParam(value = "evseNo") String evseNo,
//            ServerHttpRequest request) {
//        log.info(LoggerHelper2.formatEnterLog(request));
//        return RestUtils.buildObjectResponse(this.priceSchemaBizService.getChargePriceVoByEvesNo(evseNo));
//    }

    @Operation(summary = "单个桩计费模板下发")
    @GetMapping(value = "/dataCore/priceTemp/sendPriceSchema")
    public ObjectResponse<String> sendPriceSchema(
            ServerHttpRequest request, @RequestParam(value = "evseNo") String evseNo) {
        log.info(LoggerHelper2.formatEnterLog(request) + "sendPriceSchema evseNo = {}", evseNo);
        String priceSchemaName = this.priceSchemaBizService.sendPriceSchema(evseNo);
        return RestUtils.buildObjectResponse(priceSchemaName);
    }

    @Operation(summary = "更新桩配置")
    @PostMapping(value = "/dataCore/priceTemp/evseSetting")
    public BaseResponse updateEvseSetting(@RequestBody ModifyEvseCfgParam param,
                                          ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false));
        this.priceSchemaBizService.updateEvseSetting(param);
        return RestUtils.success();
    }

    @Operation(summary = "设置场站的默认计费模板")
    @PostMapping("/dataCore/priceTemp/setDefaultPriceScheme")
    public BaseResponse setDefaultPriceScheme(
            @Parameter(name = "设置场站默认计费模板", required = true) @RequestParam(value = "priceSchemeId") Long priceSchemeId,
            @Parameter(name = "设置场站Id", required = true) @RequestParam(value = "siteId") String siteId,
            ServerHttpRequest request
    ) {
        log.info(LoggerHelper2.formatEnterLog(request));
        priceSchemaBizService.setDefaultPriceScheme(siteId, priceSchemeId, null);
        return RestUtils.success();
    }

    @Operation(summary = "获取场站计费模板")
    @GetMapping("/dataCore/priceTemp/getSitePriceScheme")
    public ObjectResponse<PriceTemplatePo> getSitePriceScheme(
            @Parameter(name = "设置场站Id", required = true) @RequestParam(value = "siteId") String siteId,
            ServerHttpRequest request
    ) {
        log.info(LoggerHelper2.formatEnterLog(request));
        PriceTemplatePo template = priceSchemaBizService.getSitePriceScheme(siteId);
        return RestUtils.buildObjectResponse(template);
    }

    @Operation(summary = "获取计费模板信息")
    @PostMapping("/dataCore/priceTemp/getPriceSchema")
    public ObjectResponse<PriceTemplatePo> getPriceSchema(@RequestParam("priceId") Long priceId) {
        Assert.notNull(priceId, "计费模板ID不能为空");
        Optional<PriceTemplatePo> templateInfo = priceSchemaBizService.getPriceSchema(priceId, null);

        return new ObjectResponse<>(templateInfo.orElse(null));
    }

    @Operation(summary = "获取计费模板信息列表，带分时信息的")
    @PostMapping("/dataCore/priceTemp/getTimeBasedPriceSchemaList")
    public ListResponse<PriceTemplatePo> getTimeBasedPriceSchemaList(
        @RequestBody List<Long> priceIdList) {
        Assert.notNull(priceIdList, "计费模板ID列表不能为空");
        List<PriceTemplatePo> templateInfo = priceSchemaBizService.getTimeBasedPriceSchemaList(
            priceIdList, null);

        return new ListResponse<>(templateInfo);
    }

    @Operation(summary = "获取计费模板分时信息")
    @PostMapping("/dataCore/priceTemp/getChargeV2List")
    public ListResponse<ChargeV2> getChargeV2List(@RequestBody List<Long> priceIdList) {
        log.info("getChargeV2List priceIdList.size = {}", priceIdList.size());
        return RestUtils.buildListResponse(priceSchemaBizService.getChargeV2List(priceIdList));
    }

    @Operation(summary = "获取计费模板分时信息")
    @PostMapping("/dataCore/priceTemp/getPriceSchemaItem")
    public ListResponse<PriceItemPo> getPriceSchemaItem(@RequestBody List<Long> priceIdList) {
        log.info("getPriceSchemaItem priceIdList.size = {}", priceIdList.size());
        return priceSchemaBizService.getPriceSchemaItem(priceIdList);
    }


    @Operation(summary = "获取计费模板信息")
    @PostMapping("/dataCore/priceTemp/getChargePrice")
    public ObjectResponse<ChargePriceVo> getChargePrice(@RequestParam("priceId") Long priceId) {
        Assert.notNull(priceId, "计费模板ID不能为空");
        ChargePriceVo templateInfo = priceSchemaBizService.getChargePrice(priceId);
        return new ObjectResponse<>(templateInfo);
    }

    @Operation(summary = "根据场站ID获取互联互通同步过来的价格模板列表")
    @PostMapping("/dataCore/priceTemp/getHlhtSitePriceTemplateList")
    public ListResponse<PriceTemplatePo> getHlhtSitePriceTemplateList(
        ServerHttpRequest request,
        @RequestParam("siteId") String siteId) {
        Assert.isTrue(StringUtils.isNotBlank(siteId), "站点id不能为空");
        log.info("获取价格模板列表: {}, siteId = {}",
            LoggerHelper2.formatEnterLog(request, false), siteId);
        return RestUtils.buildListResponse(this.priceSchemaBizService.getHlhtSitePriceTemplateList(siteId));
    }

    @Operation(summary = "获取价格模板的模版列表")
    @PostMapping("/dataCore/priceTemp/getPriceSchemaModList")
    public ListResponse<PriceTemplateModVo> getPriceSchemaModList(
        ServerHttpRequest request,
        @RequestBody ListPriceTemplateParam param) {
        log.info("获取价格模板的模版列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), param);

        return this.priceSchemaBizService.getPriceTemplateModList(param);
    }
}
