package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.dc.service.CustomerAttractSyncService;
import com.cdz360.biz.model.cus.param.CustomerAttractListParam;
import com.cdz360.biz.model.trading.sync.vo.CustomerAttractBiVo;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class CustomerAttractSyncRest {

    @Autowired
    private CustomerAttractSyncService service;

    @Operation(summary = "获取引流汇总数据")
    @PostMapping(value = "/dataCore/cusAttractSync/getBiVo")
    public ObjectResponse<CustomerAttractBiVo> getCustomerAttractBi(@RequestBody CustomerAttractListParam param) {
        return service.getCustomerAttractBi(param);
    }

    @Operation(summary = "根据客户类型获取引流客户详情数据")
    @PostMapping(value = "/dataCore/cusAttractSync/getBiList")
    public ListResponse<CustomerAttractBiVo> getAttractBiList(@RequestBody CustomerAttractListParam param) {
        return service.getAttractBiList(param);
    }

}
