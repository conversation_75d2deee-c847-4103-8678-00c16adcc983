package com.cdz360.biz.dc.service.invoice;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ds.trading.ro.invoice.ds.CorpInvoiceRecordRoDs;
import com.cdz360.biz.ds.trading.rw.invoice.ds.CorpInvoiceRecordRwDs;
import com.cdz360.biz.ds.trading.rw.invoice.ds.InvoiceRecordOrderRefRwDs;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderInterimRwDs;
import com.cdz360.biz.model.cus.settlement.vo.SettlementBi;
import com.cdz360.biz.model.invoice.type.InvoicedStatus;
import com.cdz360.biz.model.trading.invoice.dto.CorpInvoiceRecordDto;
import com.cdz360.biz.model.trading.invoice.po.CorpInvoiceRecordPo;
import com.cdz360.biz.model.trading.invoice.po.InvoiceRecordOrderRefPo;
import com.cdz360.biz.model.trading.invoice.vo.CorpInvoiceRecordVo;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordUpdateParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceInfoVo;
import com.chargerlinkcar.framework.common.service.SettlementNoGenerator;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
public abstract class AbstractCorpInvoiceRecordStrategy
        implements UpdateCorpInvoiceRecordStrategy {

    @Autowired
    private ChargerOrderInterimRwDs chargerOrderInterimRwDs;

    @Autowired
    private CorpInvoiceRecordRwDs corpInvoiceRecordRwDs;

    @Autowired
    private CorpInvoiceRecordRoDs corpInvoiceRecordRoDs;

    @Autowired
    private SettlementNoGenerator settlementNoGenerator;

    @Autowired
    private InvoiceRecordOrderRefRwDs invoiceRecordOrderRefRwDs;

    public CorpInvoiceRecordDto preUpdate(CorpInvoiceRecordUpdateParam param, boolean append) {
        CorpInvoiceInfoVo corpInvoiceInfoVo = param.getCorpInvoiceInfoVo();
        CorpInvoiceRecordDto updateDto;
        if (StringUtils.isNotBlank(param.getApplyNo())) {
            var originalDto = corpInvoiceRecordRoDs.getRecordByApplyNo(param.getApplyNo(), true);
            if (null == originalDto) {
                throw new DcArgumentException("申请单号无效");
            }
            updateDto = new CorpInvoiceRecordDto();
            updateDto.setApplyNo(originalDto.getApplyNo())
                .setActualElecFee(originalDto.getActualElecFee())
                .setActualServFee(originalDto.getActualServFee())
                .setActualTechServFee(originalDto.getActualTechServFee());

            if (!corpInvoiceInfoVo.getInvoiceWay().equals(originalDto.getInvoiceWay())) {
                log.info("企业开票方式已经变更不支持不支持该操作: corp = {}, record = {}",
                    param.getCorpInvoiceInfoVo().getInvoiceWay(), originalDto.getInvoiceWay());
                throw new DcArgumentException("企业开票信息已变更，建议删除订单后重新创建开票申请");
            }
        } else {
            updateDto = this.recordInitialize(param);
        }

        updateDto.setCorpId(param.getCorpId())
            .setTempSalId(corpInvoiceInfoVo.getTempSalId())
            .setProductTempId(corpInvoiceInfoVo.getTempRefVo().getId())
            .setModelId(corpInvoiceInfoVo.getModelId())
            .setInvoiceName(corpInvoiceInfoVo.getName())
            .setInvoiceType(corpInvoiceInfoVo.getInvoiceType())
            .setInvoiceWay(corpInvoiceInfoVo.getInvoiceWay())
            .setUpdateOpId(param.getOpId())
            .setUpdateOpType(param.getOpType())
            .setUpdateOpName(param.getOpName());

        return updateDto;
    }

    public CorpInvoiceRecordDto recordInitialize(CorpInvoiceRecordUpdateParam param) {
        CorpInvoiceRecordDto dto = new CorpInvoiceRecordDto();
        dto.setApplyNo(StringUtils.isBlank(param.getApplyNo())
                ? settlementNoGenerator.next()  // 这里使用账单单号生成器生成单号
                : param.getApplyNo())
            .setProcInstId(param.getProcInstId())
            .setCreateOpId(param.getOpId())
            .setCreateOpType(param.getOpType())
            .setCreateOpName(param.getOpName())
            .setActualServFee(BigDecimal.ZERO)
            .setFixServFee(BigDecimal.ZERO)
            .setActualElecFee(BigDecimal.ZERO)
            .setFixElecFee(BigDecimal.ZERO)
            .setTotalFee(BigDecimal.ZERO)
            .setFixTotalFee(BigDecimal.ZERO)
            .setStatus(InvoicedStatus.NOT_SUBMITTED);
        return dto;
    }

    public abstract void updateException(CorpInvoiceRecordUpdateParam param, String applyNo, boolean append);

    /**
     * 过滤无效的orderNo，并返回对应的可开票金额
     *
     * @return String：orderNo； BigDecimal：可开票金额
     */
    public abstract Optional<Map<String, BigDecimal>> orderNoList(
        CorpInvoiceRecordUpdateParam param, long start, int size);

    /**
     * 过滤无效的orderNo，并返回对应的可开票金额
     *
     * @return String：orderNo； BigDecimal：可开票金额
     */
    public abstract Optional<Map<String, BigDecimal>> orderNoList(List<String> orderNoList);

    @Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_UNCOMMITTED)
    @Override
    public CorpInvoiceRecordVo updateRecord(CorpInvoiceRecordUpdateParam param, boolean append) {
        String applyNo = null;
        try {
            CorpInvoiceRecordDto dto = this.preUpdate(param, append);
            applyNo = dto.getApplyNo();
            int i = corpInvoiceRecordRwDs.insertOrUpdate(this.toPo(dto));
            CorpInvoiceRecordVo vo = this.postUpdate(param, dto, append);
            log.info("企业客户开票记录: {}", JsonUtils.toJsonString(vo));
            return vo;
        } catch (Exception e) {
            log.error("企业客户开票记录操作异常: err = {}", e.getMessage(), e);
            this.updateException(param, applyNo, append);
            throw e; // 继续将异常抛出
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW, isolation = Isolation.READ_UNCOMMITTED)
    @Override
    public CorpInvoiceRecordVo deleteRecord(CorpInvoiceRecordUpdateParam param, CorpInvoiceRecordDto dto) {
        String applyNo = null;
        try {
            CorpInvoiceRecordVo vo = this.postUpdate(param, dto, false);
            applyNo = dto.getApplyNo();
            log.info("调整开票记录数据: {}", JsonUtils.toJsonString(vo));
            return vo;
        } catch (Exception e) {
            log.error("删除企业客户开票记录操作异常: err = {}", e.getMessage(), e);
            this.updateException(param, applyNo, false);
            throw e; // 继续将异常抛出
        }
    }

    private CorpInvoiceRecordPo toPo(CorpInvoiceRecordDto dto) {
        CorpInvoiceRecordPo po = new CorpInvoiceRecordPo();
        BeanUtils.copyProperties(dto, po);
        return po;
    }

    public CorpInvoiceRecordVo postUpdate(
            CorpInvoiceRecordUpdateParam param, CorpInvoiceRecordDto dto, boolean append) {
        // 填充充值记录和开票记录关系表
        if (append) {
            if (StringUtils.isNotBlank(param.getInterimCode())
                || Boolean.TRUE.equals(param.getOpAll())) {
                invoiceRecordOrderRefRwDs.delete(dto.getInvoiceWay(), dto.getApplyNo(), null);

                // 循环插入数据
                long start = 0L;
                int size = 500;
                List<String> orderNoList;
                do {
                    Optional<Map<String, BigDecimal>> mapOpt = this.orderNoList(param, start, size);
                    if (mapOpt.isEmpty()) break;
                    orderNoList = new ArrayList<>(mapOpt.get().keySet());
//                    start = start + orderNoList.size(); // 不需要
                    param.setOrderNoList(orderNoList);
                    if (CollectionUtils.isNotEmpty(orderNoList)) {
                        invoiceRecordOrderRefRwDs.batchInsert(this.toRefPo(param, dto, mapOpt.get()));

                        if (orderNoList.size() < size) break;
                    }
                } while (CollectionUtils.isNotEmpty(orderNoList));
            } else {
                if (CollectionUtils.isNotEmpty(param.getOrderNoList())) {

                    Optional<Map<String, BigDecimal>> mapOpt = this.orderNoList(
                        param.getOrderNoList());

                    invoiceRecordOrderRefRwDs.batchInsert(
                        this.toRefPo(param, dto, mapOpt.orElse(new HashMap<>())));
                }
            }
        } else {
            invoiceRecordOrderRefRwDs.delete(dto.getInvoiceWay(), dto.getApplyNo(),
                Boolean.TRUE.equals(param.getOpAll()) ? null : param.getOrderNoList());
        }

        this.updateInterimCode(param.getApplyNo(), param.getInterimCode());

        return this.toVo(dto);
    }

    public void updateInterimCode(String applyNo, String interimCode) {
        if (StringUtils.isNotBlank(interimCode)) {
            // OA流程会延迟更新，此处需要重新读库
            var originalDto = corpInvoiceRecordRoDs.getRecordByApplyNo(applyNo, false);
            if (StringUtils.isNotBlank(originalDto.getProcInstId())
                && !StringUtils.equals(originalDto.getProcInstId(), interimCode)) {
                // 存在流程ID且值不等于interimCode
                chargerOrderInterimRwDs.rewriteInterimCode(interimCode,
                    originalDto.getProcInstId());
            }
        }
    }

    private List<InvoiceRecordOrderRefPo> toRefPo(@NonNull CorpInvoiceRecordUpdateParam param,
        @NonNull CorpInvoiceRecordDto dto, @NonNull Map<String, BigDecimal> decimalMap) {
        if (dto.getInvoiceWay() != InvoicingMode.POST_SETTLEMENT || CollectionUtils.isEmpty(param.getSettlementBiList())) {
            return param.getOrderNoList().stream().map(orderNo -> {
                InvoiceRecordOrderRefPo po = new InvoiceRecordOrderRefPo();
                po.setApplyNo(dto.getApplyNo())
                    .setInvoiceWay(dto.getInvoiceWay())
                    .setOrderNo(orderNo)
                    .setInvoiceAmount(decimalMap.get(orderNo))
                    .setRemark("");
                return po;
            }).collect(Collectors.toList());
        } else {
            Map<String, SettlementBi> biMap = param.getSettlementBiList().stream()
                .collect(Collectors.toMap(SettlementBi::getBillNo, o -> o));
            return param.getOrderNoList().stream().map(orderNo -> {
                InvoiceRecordOrderRefPo po = new InvoiceRecordOrderRefPo();
                po.setApplyNo(dto.getApplyNo())
                    .setInvoiceWay(dto.getInvoiceWay())
                    .setOrderNo(orderNo)
                    .setInvoiceAmount(decimalMap.get(orderNo))
                    .setRemark(biMap.get(orderNo).getBillName());
                return po;
            }).collect(Collectors.toList());
        }
    }

    /**
     * 调整金额
     *
     * @param dto
     * @param serv
     * @param elec
     */
    protected void updateFee(boolean append, CorpInvoiceRecordDto dto, BigDecimal serv, BigDecimal elec) {
//        // 注意: 这里是操作订单的总额 ↓
//        dto.setActualElecFee(elec)
//                .setActualServFee(serv)
//                .setTotalFee(serv.add(elec));
//        // 注意: 这里是操作订单的总额 ↑;
        if (append) {
            dto.setActualServFee(dto.getActualServFee().add(serv))
                    .setActualElecFee(dto.getActualElecFee().add(elec));
        } else {
            dto.setActualElecFee(dto.getActualElecFee().subtract(elec))
                    .setActualServFee(dto.getActualServFee().subtract(serv));
        }
        dto.setTotalFee(dto.getActualElecFee().add(dto.getActualServFee()));
        log.info("applyNo = {}, actServFee = {}, actElecFee = {}",
                dto.getApplyNo(), dto.getActualServFee(), dto.getActualElecFee());
    }

    /**
     * 对象转换
     *
     * @param dto
     * @return
     */
    protected CorpInvoiceRecordVo toVo(CorpInvoiceRecordDto dto) {
        CorpInvoiceRecordVo vo = new CorpInvoiceRecordVo();
        BeanUtils.copyProperties(dto, vo);
        return vo;
    }
}
