package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.dc.service.EssSiteService;
import com.cdz360.biz.ess.model.site.param.ListEssSiteParam;
import com.cdz360.biz.ess.model.site.vo.EssSiteVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "ESS场站相关接口", description = "ESS场站")
@RequestMapping("/dataCore/site/ess")
public class EssSiteRest {

    @Autowired
    private EssSiteService essSiteService;

    @Operation(summary = "获取ESS站点列表")
    @PostMapping("/findSite")
    public Mono<ListResponse<EssSiteVo>> findEssSite(
        @RequestBody ListEssSiteParam param) {
        log.info("获取ESS站点列表: {}", param);
        return essSiteService.findEssSite(param);
    }

    @Operation(summary = "获取ESS场站详情")
    @GetMapping(value = "/{siteId}/detail")
    public Mono<ObjectResponse<EssSiteVo>> getSiteDetail(
        @PathVariable(value = "siteId") String siteId) {
        log.info("获取ESS场站详情: {}", siteId);
        return essSiteService.getSiteDetail(siteId);
    }

}
