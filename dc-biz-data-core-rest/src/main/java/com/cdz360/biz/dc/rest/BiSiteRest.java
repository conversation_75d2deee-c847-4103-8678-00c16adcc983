package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.BiSiteService;
import com.cdz360.biz.model.trading.order.type.BiDependOnType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * BiSiteRest
 *
 * <AUTHOR>
 * @since 3/24/2020 10:13 AM
 */
@Slf4j
@RestController
@RequestMapping("/dataCore/biSite")
@Tag(name = "站点数据统计")
public class BiSiteRest {

    @Autowired
    private BiSiteService biSiteService;

    //    @Async
    @PostMapping("/asyncBiSiteHourly")
    public BaseResponse asyncBiSiteHourly(
        @RequestParam(name = "type", required = false) BiDependOnType type,
        @RequestParam(name = "dateTime", required = false)
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date date,
        @RequestParam(name = "siteId", required = false) String siteId,
        @RequestParam(name = "siteIds", required = false) List<String> siteIds) {//TODO 超超哥的催促
        this.asyncBiSiteHourlyX(type, date, siteId);
        return BaseResponse.success();
    }

//    @Async
    @PostMapping("/asyncBiSiteDaily")
    public BaseResponse asyncBiSiteDaily(
        @RequestParam(name = "type", required = false) BiDependOnType type,
        @RequestParam(name = "dateTime", required = false)
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date date,
        @RequestParam(name = "siteId", required = false) String siteId,
        @RequestParam(name = "siteIds", required = false) List<String> siteIds) {//TODO 超超哥的催促
        this.asyncBiSiteDailyX(type, date, siteId);
        return BaseResponse.success();
    }

    @Async
    public void asyncBiSiteMonthlyX(BiDependOnType type, Date date, String siteId,
        List<String> siteIds) {
        this.biSiteMonthly(type, date, siteId);
    }

    //    @Async
    @PostMapping("/asyncBiSiteMonthly")
    public BaseResponse asyncBiSiteMonthly(
        @RequestParam(name = "type", required = false) BiDependOnType type,
        @RequestParam(name = "dateTime", required = false)
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date date,
        @RequestParam(name = "siteId", required = false) String siteId,
        @RequestParam(name = "siteIds", required = false) List<String> siteIds) {//TODO 超超哥的催促
        this.asyncBiSiteMonthlyX(type, date, siteId, siteIds);
        return BaseResponse.success();
    }

    @Async
    public void asyncBiSiteHourlyX(BiDependOnType type, Date date, String siteId) {
        this.biSiteHourly(type, date, siteId);
    }


    @Operation(summary = "统计场站小时充电数据")
    @PostMapping("/biSiteHourly")
    public BaseResponse biSiteHourly(
        @RequestParam(name = "type", required = false) BiDependOnType type,
        @RequestParam(name = "dateTime", required = false)
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date date,
        @RequestParam(name = "siteId", required = false) String siteId) {
        log.info("统计场站小时充电数据: {}", date);

        if (date == null) {
            date = new Date();
            log.info("统计场站小时充电数据, 使用当前时间: {}", date);
        }

        long start = System.nanoTime();
        biSiteService.hourlyBi(date, type, siteId);

        String msg = MessageFormat.format("site hourly finish: {0}", date);
        this.debugPerformance(msg, start);
        return BaseResponse.success();
    }

    //    @Async
    @PostMapping("/asyncBiSiteRemedyHourly")
    public BaseResponse asyncBiSiteRemedyHourly(
        @RequestParam(name = "dateTime", required = false)
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date date,
        @RequestParam(name = "siteId", required = false) String siteId,
        @RequestParam(name = "siteIds", required = false) List<String> siteIds) {//TODO 超超哥的催促
        this.asyncBiSiteRemedyHourlyX(date, siteId);
        return BaseResponse.success();
    }

    @Async
    public void asyncBiSiteRemedyHourlyX(Date date, String siteId) {
        this.biSiteRemedyHourly(date, siteId);
    }

    @Operation(summary = "按订单更新时间补救，统计场站小时充电数据")
    @PostMapping("/biSiteRemedyHourly")
    public BaseResponse biSiteRemedyHourly(
        @RequestParam(name = "dateTime", required = false)
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date date,
        @RequestParam(name = "siteId", required = false) String siteId) {
        log.info("按订单更新时间补救，统计场站小时充电数据: {}", date);

        if (date == null) {
            date = new Date();
            log.info("按订单更新时间补救，统计场站小时充电数据, 使用当前时间: {}", date);
        }

        long start = System.nanoTime();
        biSiteService.refreshSiteBiByOrderHourly(date, siteId);

        String msg = MessageFormat.format("site remedy hourly finish: {0}", date);
        this.debugPerformance(msg, start);
        return BaseResponse.success();
    }

    @Async
    public void asyncBiSiteDailyX( BiDependOnType type, Date date, String siteId){
        this.biSiteDaily(type, date, siteId);
    }

    @GetMapping("/biSiteDaily")
    public BaseResponse biSiteDaily(
        @RequestParam(name = "type", required = false) BiDependOnType type,
        @RequestParam(name = "dateTime", required = false)
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date date,
        @RequestParam(name = "siteId", required = false) String siteId) {
        LocalDateTime time = date.toInstant().atOffset(ZoneOffset.of("+8")).toLocalDateTime();
        time = time.withHour(0).withMinute(0).withNano(0);
        LocalDateTime endTime = time.plusDays(1);

        long start = System.nanoTime();
        while (time.isBefore(endTime)) {
            this.biSiteHourly(type,
                new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()),
                siteId);
            time = time.plusHours(1);
        }
        //log.info("site daily finish: {}, {}", date, System.nanoTime() - start);

        String msg = MessageFormat.format("site daily finish: {0}", date);
        this.debugPerformance(msg, start);
        return BaseResponse.success();
    }

    @GetMapping("/biSiteMonthly")
    public BaseResponse biSiteMonthly(
        @RequestParam(name = "type", required = false) BiDependOnType type,
        @RequestParam(name = "dateTime", required = false)
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date date,
        @RequestParam(name = "siteId", required = false) String siteId) {

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.clear(Calendar.MILLISECOND);
        calendar.clear(Calendar.SECOND);
        calendar.clear(Calendar.MINUTE);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);

        long startTime = calendar.getTime().getTime();

        calendar.add(Calendar.MONTH, 1);
        long endTime = calendar.getTime().getTime();

        // reset month
        calendar.add(Calendar.MONTH, -1);

        long currentTime = startTime;

        long start = System.nanoTime();
        while (currentTime < endTime) {
            this.biSiteDaily(type, calendar.getTime(), siteId);
            calendar.add(Calendar.DATE, 1);
            currentTime = calendar.getTime().getTime();
        }
        //log.info("site monthly finish: {}, {}", date, System.nanoTime() - start);
        String msg = MessageFormat.format("site monthly finish: {0}", date);
        this.debugPerformance(msg, start);
        return BaseResponse.success();
    }


    @Operation(summary = "计算场站收入")
    @GetMapping("/accountSiteAccountIncome")
    public ObjectResponse<String> accountSiteAccountIncome(
        @RequestParam(name = "siteIds", required = false) List<String> siteIds,
        @RequestParam(name = "year", required = false) Integer year,
        @RequestParam(name = "month", required = false) Integer month) {
        if (Boolean.TRUE.equals(this.biSiteService.getWorkingStatus())) {
            return RestUtils.buildObjectResponse(
                "计算任务开始失败，有正在进行中的计算任务,请稍后再执行");
        }
        this.biSiteService.accountSiteAccountIncome(siteIds, year, month);
        return RestUtils.buildObjectResponse("启动计算任务成功");
    }

    @Operation(summary = "计算场站每日运营收入收入(国充)")
    @GetMapping("/accountSiteDailyFee")
    public ObjectResponse<String> accountSiteDailyFee(
        @RequestParam(name = "siteIds", required = false) List<String> siteIds,
        @RequestParam(name = "startDate", required = false) String startDate,
        @RequestParam(name = "endDate", required = false) String endDate) {
        if (Boolean.TRUE.equals(this.biSiteService.getWorkingDailyAccountingStatus())) {
            return RestUtils.buildObjectResponse(
                "计算任务开始失败，有正在进行中的计算任务,请稍后再执行");
        }
        this.biSiteService.accountSiteDailyFee(siteIds, startDate, endDate);
        return RestUtils.buildObjectResponse("启动计算任务成功");
    }

//    public static void main(String[] args) {
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTime(new Date());
//        calendar.clear(Calendar.MILLISECOND);
//        calendar.clear(Calendar.SECOND);
//        calendar.clear(Calendar.MINUTE);
//        calendar.set(Calendar.HOUR_OF_DAY, 0);
//        calendar.set(Calendar.DAY_OF_MONTH, 1);
//
//        long startTime = calendar.getTime().getTime();
//
//        calendar.add(Calendar.MONTH, 1);
//        long endTime = calendar.getTime().getTime();
//
//        // reset month
//        calendar.add(Calendar.MONTH, -1);
//
//        long currentTime = startTime;
//
//        while(currentTime < endTime) {
////            this.biSiteDaily(request, calendar.getTime());
////            test(calendar.getTime());
////            log.info("time = {}", calendar);
//            log.info("s = {}", calendar.getTime());
//            calendar.add(Calendar.DATE, 1);
//            currentTime = calendar.getTime().getTime();
//        }
//    }
//
//    private static void test(Date date) {
//        LocalDateTime time = date.toInstant().atOffset(ZoneOffset.of("+8")).toLocalDateTime();
//        time = time.withHour(0).withMinute(0).withNano(0);
//        LocalDateTime endTime = time.plusDays(1);
//
//        log.info("========================================================");
//        while(time.isBefore(endTime)) {
////            this.biSiteHourly(request, new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
//            log.info(">> time = {}", time);
//            time = time.plusHours(1);
//        }
//        log.info("========================================================");
//    }


    /**
     * debug接口时延
     */
    private void debugPerformance(String name, long startTime) {
        long curTime = System.nanoTime();
        if (curTime - startTime > 1000L * 1000000) {
            log.warn("延迟过大 name = {}...{}. ", name, (curTime - startTime) / 1000000);
        }
    }
}