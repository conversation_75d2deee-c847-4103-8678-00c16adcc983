package com.cdz360.biz.dc.rest.site;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.domain.hlht.HlhtSyncSiteDto;
import com.cdz360.biz.dc.domain.vo.HlhtSiteInfoVo;
import com.cdz360.biz.dc.domain.vo.SiteInMongoVo;
import com.cdz360.biz.dc.service.site.SiteBizService;
import com.cdz360.biz.model.cus.score.dto.UserScoreSettingLevelSiteGidDto;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.discount.param.DiscountServiceParam;
import com.cdz360.biz.model.discount.vo.SiteDiscountVo;
import com.cdz360.biz.model.merchant.dto.CommercialSiteDto;
import com.cdz360.biz.model.merchant.dto.SiteTinyDto;
import com.cdz360.biz.model.merchant.param.CommcialSiteParam;
import com.cdz360.biz.model.site.dto.OaSiteMonthDataDto;
import com.cdz360.biz.model.site.dto.SiteGeoDto;
import com.cdz360.biz.model.site.param.AddSiteParam;
import com.cdz360.biz.model.site.param.ListSiteBaseParam;
import com.cdz360.biz.model.site.param.UpdateSiteParam;
import com.cdz360.biz.model.site.type.SiteCategory;
import com.cdz360.biz.model.site.type.SiteStatus;
import com.cdz360.biz.model.trading.hlht.dto.CecQueryQeuipBusinessPolicyResult;
import com.cdz360.biz.model.trading.hlht.dto.DataSyncOvertimeParkFeeFlag;
import com.cdz360.biz.model.trading.order.po.CardPo;
import com.cdz360.biz.model.trading.site.dto.SiteProfitInfo;
import com.cdz360.biz.model.trading.site.dto.SiteSimpleDto;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.param.ListSiteProfitParam;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.site.vo.MoveCorpNoCardList;
import com.cdz360.biz.model.trading.site.vo.MoveCorpNoCardVo;
import com.cdz360.biz.model.trading.site.vo.SiteConfStartList;
import com.cdz360.biz.model.trading.site.vo.SiteNoVo;
import com.cdz360.biz.model.trading.site.vo.SiteOrderStatsVo;
import com.cdz360.biz.model.trading.site.vo.SiteQrCodeVo;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import com.cdz360.biz.model.trading.soc.param.SocStrategyDict;
import com.cdz360.biz.model.trading.soc.vo.MoveCorpUserSocStrategyList;
import com.cdz360.data.sync.model.Site;
import com.chargerlinkcar.framework.common.domain.PlugStatusCountDto;
import com.chargerlinkcar.framework.common.domain.SiteDetailInfoVo;
import com.chargerlinkcar.framework.common.domain.SiteOverTimeParkDTO;
import com.chargerlinkcar.framework.common.domain.SitePersonaliseDTO;
import com.chargerlinkcar.framework.common.domain.SiteSocLimitDto;
import com.chargerlinkcar.framework.common.domain.request.SiteGeoListRequest;
import com.chargerlinkcar.framework.common.domain.vo.SiteDebitAccountVo;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;


@Slf4j
@RestController
@Tag(name = "场站相关接口", description = "场站")
public class SiteRest {

    @Autowired
    private SiteBizService siteService;


    /**
     * 获取场站ID列表， gids 或 commIdChain 必须传一个
     */
    @PostMapping("/dataCore/site/getSiteIds")
    public ListResponse<String> getSiteIds(@RequestBody ListSiteBaseParam paramIn) {
        return RestUtils.buildListResponse(
            this.siteService.getSiteIds(paramIn.getGids(), paramIn.getCommIdChain()));
    }


    @PostMapping("/dataCore/site/getSiteById")
    public ObjectResponse<SitePo> getSiteById(@RequestParam("siteId") String siteId) {
        log.info("siteId = {}", siteId);
        Assert.isTrue(com.cdz360.base.utils.StringUtils.isNotBlank(siteId), "站点id不能为空");

        SitePo vo = siteService.getSiteById(siteId);
        return new ObjectResponse<>(vo);
    }


    @GetMapping("/dataCore/site/getSiteInfo")
    public ObjectResponse<Site> getSiteInfo(@RequestParam("siteId") String siteId) {
        log.info("siteId = {}", siteId);
        Assert.isTrue(com.cdz360.base.utils.StringUtils.isNotBlank(siteId), "站点id不能为空");

        Site vo = siteService.getSiteInfo(siteId);
        return new ObjectResponse<>(vo);
    }

    @PostMapping("/dataCore/site/getCardAmountBySiteId")
    public ListResponse<CardPo> getCardAmountBySiteId(@RequestBody ListSiteBaseParam param) {
        log.info("param = {}", param);
        Assert.isTrue(com.cdz360.base.utils.StringUtils.isNotBlank(param.getSiteId()),
            "站点id不能为空");

        return siteService.getCardAmountBySiteId(param);
    }

    @Operation(summary = "创建场站")
    @PostMapping("/dataCore/site/addSite")
    public ObjectResponse<SitePo> addSite(@RequestBody AddSiteParam param) {
        log.info("param = {}", param);
        SitePo vo = siteService.addSite(param);
        log.info("site = {}", JsonUtils.toJsonString(vo));
        return new ObjectResponse<>(vo);
    }


    @Operation(summary = "修改场站信息")
    @PostMapping("/dataCore/site/updateSiteInfo")
    public ObjectResponse<SitePo> updateSiteInfo(@RequestBody UpdateSiteParam param) {
        log.info("param = {}", param);
        SitePo vo = siteService.updateSiteInfo(param);
        log.info("site = {}", JsonUtils.toJsonString(vo));
        return new ObjectResponse<>(vo);
    }


    @Operation(summary = "修改场站状态")
    @PostMapping("/dataCore/site/updateSiteStatus")
    public ObjectResponse<SitePo> updateSiteStatus(@RequestParam String siteId,
        @RequestParam SiteStatus status) {
        log.info("siteId = {}, status = {}", siteId, status);
        SitePo site = this.siteService.updateSiteStatus(siteId, status);
        //log.info("site = {}", JsonUtils.toJsonString(vo));
        return RestUtils.buildObjectResponse(site);
    }

    @Operation(summary = "场站运营数据推送到亨通ERP")
    @PostMapping("/dataCore/site/sendErpSiteBi")
    public BaseResponse sendErpSiteBi(
        ServerHttpRequest request, @RequestBody String param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param={}" + param);
        int i = siteService.sendErpSiteBi(param);
        log.info("i = {}", i);
        return RestUtils.success();
    }


    /**
     * 修改场站上线时间
     *
     * @param siteId
     * @param onlineDate
     * @return
     */
    @PostMapping(value = "/dataCore/site/setOnlineDate")
    public BaseResponse setOnlineDate(@RequestParam("siteId") String siteId,
        @RequestParam("onlineDate") String onlineDate) {
        siteService.setOnlineDate(siteId, onlineDate);
        return BaseResponse.success();
    }


    @PostMapping(value = "/dataCore/site/getSiteTinyList")
    public ListResponse<SiteTinyDto> getSiteTinyList(@RequestBody ListSiteParam param) {
        log.debug("param = {}", param);
        List<SiteTinyDto> siteList = this.siteService.getSiteTinyList(param);
        return RestUtils.buildListResponse(siteList);
    }


    @PostMapping(value = "/dataCore/site/getSiteDetail")
    @Operation(summary = "获取场站详情信息")
    public ObjectResponse<SiteDetailInfoVo> getSiteDetail(@RequestBody SiteGeoListRequest param) {
        if (StringUtils.isBlank(param.getSiteId())) {
            log.warn("参数错误,场站编号不能为空. param = {}", param);
            throw new DcArgumentException("参数错误,场站编号不能为空");
        }
        var site = this.siteService.getSiteDetail(param);
        return RestUtils.buildObjectResponse(site);
    }

    /**
     * 用于查询场站编号是否存在
     *
     * @param siteNo
     * @return
     */
    @GetMapping(value = "/dataCore/site/getSiteInfoBySiteNo")
    @Operation(summary = "用于查询场站编号是否存在")
    public ObjectResponse<SiteNoVo> getSiteInfoBySiteNo(@RequestParam Long topCommId,
        @RequestParam String siteNo) {
        var site = this.siteService.getSiteInfoBySiteNo(topCommId, siteNo);
        return RestUtils.buildObjectResponse(site);
    }

    @GetMapping(value = "/dataCore/site/getSiteNoById")
    public ObjectResponse<SiteNoVo> getSiteNoById(@RequestParam(value = "siteId") String siteId) {
        return RestUtils.buildObjectResponse(siteService.getSiteNoById(siteId));
    }

    @PostMapping("/dataCore/site/getSiteFromMongo")
    @Operation(summary = "获取mongo里存的场站")
    public ObjectResponse<SiteInMongoVo> getSiteFromMongo(
        @RequestParam(value = "siteId") String siteId,
        @RequestParam(required = false, value = "userId") Long userId) {
        log.debug("siteId = {}, userId = {}", siteId, userId);
        SiteInMongoVo site = siteService.getSiteFromMongo(siteId, userId);
        //log.info("list.size = {}", list.size());
        return RestUtils.buildObjectResponse(site);
    }

    @PostMapping("/dataCore/site/getSiteListFromMongo")
    @Operation(summary = "获取mongo里存的场站列表")
    public ListResponse<SiteInMongoVo> getSiteListFromMongo(@RequestBody ListSiteParam param) {
        log.debug("param = {}", param);
        List<SiteInMongoVo> list = siteService.getSiteListFromMongo(param);
        log.info("list.size = {}", list.size());
        return RestUtils.buildListResponse(list);
    }


    @Operation(summary = "同步数据库中的场站数据到mongo(仅用作数据同步)")
    @GetMapping("/dataCore/site/syncSite2Mongo")
    public BaseResponse syncSite2Mongo() {
        log.info("同步场站数据到mongo");
        siteService.syncSite2Mongo();
        return RestUtils.success();
    }


    @PostMapping("/dataCore/site/getSiteVoList")
    public ListResponse<SiteVo> getSiteVoList(@RequestBody ListSiteParam param) {
        log.info("param = {}", param);
        ListResponse<SiteVo> page = siteService.getSiteVoList(param);
        return page;
    }

    @Operation(summary = "获取场站光伏/储能收益计算规则", description = "光伏站收益计算存在才在结果中体现")
    @PostMapping("/dataCore/site/getSiteProfitList")
    public Mono<ListResponse<SiteProfitInfo>> getSiteProfitList(
        @RequestBody ListSiteProfitParam param) {
        log.info("获取场站光伏收益计算规则. param = {}", param);
        return siteService.getSiteProfitList(param)
            .map(RestUtils::buildListResponse);
    }

    /**
     * 修改场站后台充电-扣款账户
     *
     * @return
     */
    @PostMapping(value = "/dataCore/site/updateSiteDebitAccent")
    public BaseResponse updateSiteDebitAccent(@RequestBody SiteDebitAccountVo request) {
        siteService.updateSiteDebitAccent(request);
        return BaseResponse.success();
    }

    @Operation(summary = "同步电价到t_site_template表中")
    @GetMapping("/dataCore/site/syncSitePrice2SiteTemplate")
    public BaseResponse syncSitePrice2SiteTemplate() {
        log.info("同步电价到t_site_template表中");
        siteService.syncSitePrice2SiteTemplate();
        return RestUtils.success();
    }

    /**
     * 鼎充专用---更新站点开票标识
     *
     * @param list
     * @param invoicedValid
     * @return
     */
    @Deprecated
    @PostMapping("/dataCore/site/updateInvoicedValid")
    public BaseResponse updateInvoicedValid(
        @RequestParam(value = "list", required = true) List<String> list,
        @RequestParam(value = "invoicedValid", required = true) Integer invoicedValid) {
        siteService.updateInvoicedValid(list, invoicedValid);
        return BaseResponse.success();
    }

    /**
     * 查询场站列表信息通用接口(可拓展)
     *
     * @param start
     * @param size
     * @return
     */
    @PostMapping("/dataCore/site/getSiteList")
    public ListResponse<Site> getSiteList(
        @RequestParam(value = "commId", required = false) Long commId,
        @RequestParam(value = "cityCode", required = false) String cityCode,
        @RequestParam(value = "siteIdList", required = false) List<String> siteIdList,
        @RequestParam(value = "start", defaultValue = "0") long start,
        @RequestParam(value = "size", defaultValue = "5") int size) {
        return siteService.getSiteList(commId, cityCode, siteIdList, start, size);
    }

    /**
     * 查询互联互通场站列表信息接口
     *
     * @param start
     * @param size
     * @return
     */
    @PostMapping("/dataCore/site/getHlhtSiteInfoVoList")
    public ListResponse<HlhtSiteInfoVo> getHlhtSiteVoList(
        @RequestParam(value = "commId", required = false) Long commId,
        @RequestParam(value = "cityCode", required = false) String cityCode,
        @RequestParam(value = "siteIdList") List<String> siteIdList,
        @RequestParam(value = "start", defaultValue = "0") long start,
        @RequestParam(value = "size", defaultValue = "5") int size) {
        return siteService.getHlhtSiteInfoVoList(commId, cityCode, siteIdList, start, size);
    }

    /**
     * 更新t_site中桩枪数量等信息
     *
     * @param siteId
     * @param unBindEvseNo 解绑桩时，清除相关定时充电任务
     * @return
     */
    @GetMapping(value = "/dataCore/site/recordEvsePlugInfo")
    public BaseResponse recordEvsePlugInfo(@RequestParam(value = "siteId") String siteId,
        @RequestParam(value = "unBindEvseNo", required = false) String unBindEvseNo) {
        log.info("recordEvsePlugInfo siteId: {}, unBindEvseNo: {}", siteId, unBindEvseNo);
        boolean ret = this.siteService.recordEvsePlugInfo(siteId, unBindEvseNo);
        log.info("更新结果 ret = {}", ret);
        return RestUtils.success();
    }

    /**
     * 2020-06升级清洗t_site中桩枪数量等信息
     *
     * @return
     */
    @GetMapping(value = "/dataCore/site/upgradeCleaningEvsePlugInfo")
    public BaseResponse upgradeCleaningEvsePlugInfo() {
        log.info("2020-06升级清洗开始");
        boolean ret = this.siteService.upgradeCleaningEvsePlugInfo();
        log.info("更新结果 ret = {}", ret);
        return RestUtils.success();
    }

    @Operation(summary = "获取场站个性化设置")
    @GetMapping(value = "/dataCore/site/getPersonalise")
    public ObjectResponse<SitePersonaliseDTO> getPersonalise(
        ServerHttpRequest request,
        @Parameter(name = "场站Id", required = true) @RequestParam(value = "siteId") String siteId) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return RestUtils.buildObjectResponse(siteService.getPersonalise(siteId));
    }

    @Operation(summary = "变更场站个性化设置")
    @PostMapping(value = "/dataCore/site/updatePersonalise")
    public BaseResponse updatePersonalise(
        ServerHttpRequest request, @RequestBody SitePersonaliseDTO dto) {
        log.info("变更场站个性化设置: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(dto));
        int i = siteService.updatePersonalise(dto);
        log.info("i = {}", i);
        return RestUtils.success();
    }

    @Operation(summary = "创建企业soc策略")
    @PostMapping(value = "/dataCore/site/createCorpSocStrategy")
    public BaseResponse createCorpSocStrategy(
        ServerHttpRequest request, @RequestBody SocStrategyDict param) {

        log.info(LoggerHelper2.formatEnterLog(request, false) + "param = " + JsonUtils.toJsonString(
            param));
        int i = siteService.createCorpSocStrategy(param);
        log.info("i = {}", i);
        return RestUtils.success();
    }

    @Operation(summary = "更新企业soc策略")
    @PostMapping(value = "/dataCore/site/updateCorpStrategy")
    public BaseResponse updateCorpStrategy(
        ServerHttpRequest request, @RequestBody SocStrategyDict param) {

        log.info(LoggerHelper2.formatEnterLog(request, false) + "param = " + JsonUtils.toJsonString(
            param));
        int i = siteService.updateCorpStrategy(param);
        log.info("i = {}", i);
        return RestUtils.success();
    }

    @Operation(summary = "删除企业soc策略")
    @GetMapping(value = "/dataCore/site/deleteCorpStrategy")
    public BaseResponse deleteCorpStrategy(
        ServerHttpRequest request, @RequestParam(value = "id") Long id) {

        log.info(
            LoggerHelper2.formatEnterLog(request, false) + "param = " + JsonUtils.toJsonString(id));
        int i = siteService.deleteCorpStrategy(id);
        log.info("i = {}", i);
        return RestUtils.success();
    }

    @Operation(summary = "为同步停充超时配置拼装参数")
    @PostMapping(value = "/dataCore/site/getSyncParamData")
    public ListResponse<DataSyncOvertimeParkFeeFlag> getSyncParamData(ServerHttpRequest request,
        @RequestBody List<String> siteIdList) {

        log.info(LoggerHelper2.formatEnterLog(request, false) + "siteIdList.size = "
            + siteIdList.size());
        return siteService.getSyncParamData(siteIdList);
    }

    @Operation(summary = "企业切换商户时，场站后台启动配置项保留/移除列表")
    @GetMapping(value = "/dataCore/site/getMoveCorpSiteConfStart")
    public ObjectResponse<SiteConfStartList> getMoveCorpSiteConfStart(
        @RequestParam("corpId") Long corpId,
        @RequestParam("commId") Long commId) {
        log.info("corpId = {}, commId = {}", corpId, commId);

        return new ObjectResponse<>(siteService.getMoveCorpSiteConfStart(corpId, commId));
    }

    @Operation(summary = "企业切换商户时，场站soc限制保留/移除列表")
    @GetMapping(value = "/dataCore/site/getMoveCorpSoc")
    public ObjectResponse<MoveCorpUserSocStrategyList> getMoveCorpSoc(
        @RequestParam("corpId") Long corpId,
        @RequestParam("commId") Long commId) {
        log.info("corpId = {}, commId = {}", corpId, commId);

        return new ObjectResponse<>(siteService.getMoveCorpSoc(corpId, commId));
    }

    @Operation(summary = "企业切换商户时，场站无卡启动结算账户保留/移除列表")
    @GetMapping(value = "/dataCore/site/getMoveCorpNoCard")
    public ObjectResponse<MoveCorpNoCardList> getMoveCorpNoCard(@RequestParam("corpId") Long corpId,
        @RequestParam("commId") Long commId) {
        log.info("corpId = {}, commId = {}", corpId, commId);

        return new ObjectResponse<>(siteService.getMoveCorpNoCard(corpId, commId));
    }

    @Operation(summary = "根据站点id获取站点下各状态插座数量统计")
    @GetMapping("/dataCore/site/getChargerStatusStatisticsBySiteId")
    public ObjectResponse<PlugStatusCountDto> getChargerStatusStatisticsBySiteId(
        @RequestParam("siteId") String siteId) {
        return RestUtils.buildObjectResponse(
            siteService.getChargerStatusStatisticsBySiteId(siteId));
    }


    @Operation(summary = "返回场站列表,含城市,省份信息")
    @PostMapping("/dataCore/site/getSiteGeoList")
    public ListResponse<SiteGeoDto> getSiteGeoList(ServerHttpRequest request,
        @RequestBody ListSiteParam param
    ) {
        log.debug(LoggerHelper2.formatEnterLog(request));
        var list = this.siteService.getSiteGeoList(param);
        return RestUtils.buildListResponse(list);
    }

    @Operation(summary = "桩管家-监控中心-选择充电站")
    @PostMapping("/dataCore/site/getCommercialSiteList")
    public ListResponse<CommercialSiteDto> getCommercialSiteList(ServerHttpRequest request,
        @RequestBody CommcialSiteParam param) {
        return RestUtils.buildListResponse(this.siteService.getCommercialSiteList(param));
    }

    @Operation(summary = "同步场站信息 open-hlht -> data-core")
    @PostMapping("/dataCore/site/syncHlhtSite")
    public ListResponse<Site> syncHlhtSite(ServerHttpRequest request,
        @RequestBody HlhtSyncSiteDto site) {
        log.info(">> sites.id = {}", site.getOpenSiteId());
//        Site retSite = this.siteService.syncHlhtSite(site);
        return RestUtils.buildListResponse(this.siteService.syncHlhtSite(site));
    }

    @Operation(summary = "获取场站已存在的发票提供方信息")
    @GetMapping("/dataCore/site/getInvoiceDescList")
    public ListResponse<String> getInvoiceDescList(ServerHttpRequest request,
        @RequestParam(value = "commIdChain") String commIdchain,
        @RequestParam(value = "desc", required = false) String desc) {
        log.info(">> commIdchain = {} desc = {}", commIdchain, desc);
        return this.siteService.getInvoiceDescList(commIdchain, desc);
    }

    @Operation(summary = "场站充电订单数据统计", description = "昨天、近7天、近30天的充电次数、电量、金额")
    @GetMapping(value = "/dataCore/site/orderStats")
    public Mono<ListResponse<SiteOrderStatsVo>> siteOrderStats(
        ServerHttpRequest request, @RequestParam("siteId") String siteId) {
        log.debug("场站充电订单数据统计: {}", LoggerHelper2.formatEnterLog(request));
        return this.siteService.siteOrderStats(siteId);
    }

    @Operation(summary = "场站协议价服务费")
    @PostMapping(value = "/dataCore/site/discountServiceFee")
    public ListResponse<SiteDiscountVo> discountServiceFee(
        ServerHttpRequest request, @RequestBody DiscountServiceParam param) {
        log.debug("场站协议价服务费: {}", JsonUtils.toJsonString(param));
        return RestUtils.buildListResponse(this.siteService.discountServiceFee(param));
    }

    @Operation(summary = "获取协议价服务费")
    @PostMapping(value = "/dataCore/site/discountServiceFeeByCode")
    public ObjectResponse<SiteDiscountVo> discountServiceFeeByCode(
        ServerHttpRequest request, @RequestBody DiscountServiceParam param) {
        log.debug("获取协议价服务费: {}", JsonUtils.toJsonString(param));
        return RestUtils.buildObjectResponse(this.siteService.discountServiceFeeByCode(param));
    }

    @Operation(summary = "场站电费支付数据记录")
    @PostMapping(value = "/dataCore/site/oaMonthData")
    public Mono<BaseResponse> oaSiteMonthData(
        ServerHttpRequest request, @RequestBody OaSiteMonthDataDto dto) {
        log.info("场站电费支付数据记录: {}", JsonUtils.toJsonString(dto));
        this.siteService.oaSiteMonthData(dto);
        return Mono.just(RestUtils.success());
    }

    @Operation(summary = "获取场站充电限制状态详情")
    @GetMapping(value = "/dataCore/site/getSocLimitInfo")
    public Mono<ObjectResponse<SiteSocLimitDto>> getSocLimitInfo(
        ServerHttpRequest request, @RequestParam(value = "siteId") String siteId) {
        log.debug("获取场站充电限制详情: {}", siteId);
        return this.siteService.getSocLimitInfo(siteId);
    }

    @Operation(summary = "修改场站充电限制状态详情")
    @PostMapping(value = "/dataCore/site/updateSocLimitInfo")
    public Mono<ObjectResponse<Boolean>> updateSocLimitInfo(
        ServerHttpRequest request, @RequestBody SiteSocLimitDto param) {
        log.debug("修改场站充电限制状态详情: {}", JsonUtils.toJsonString(param));
        if (param.isSocLimit() && CollectionUtils.isEmpty(param.getStrategyMainList())) {
            IotAssert.isTrue(false, "请传入策略列表");
        }
        return this.siteService.updateSocLimitInfo(param);
    }

    @Operation(summary = "更新场站评级数据")
    @GetMapping(value = "/dataCore/site/updateSiteCommentInfo")
    public Mono<BaseResponse> updateSiteCommentInfo(ServerHttpRequest request) {
        log.info("更新场站评级数据: {}", LoggerHelper2.formatEnterLog(request));
        return this.siteService.updateSiteCommentInfo();
    }

    @Operation(summary = "按照商户链返回场站信息")
    @GetMapping("/dataCore/site/getSiteListByIdChain")
    public ListResponse<SiteSimpleDto> getSiteListByIdChain(ServerHttpRequest request,
        @RequestParam(value = "idChain", required = false) String idChain) {
        log.debug(LoggerHelper2.formatEnterLog(request));
        return this.siteService.getSiteListByIdChain(idChain);

    }

    @Operation(summary = "获取运维人员管理场站的类型(光储充)")
    @PostMapping("/dataCore/site/ywUserSiteCategoryList")
    public ListResponse<SiteCategory> ywUserSiteCategoryList(@RequestBody List<String> siteIdList) {
        return siteService.ywUserSiteCategoryList(siteIdList);
    }

    @Operation(summary = "企业授信账户作为无卡启动结算的场站信息")
    @GetMapping("/dataCore/site/getCorpNoCardList")
    public ListResponse<MoveCorpNoCardVo> getCorpNoCardList(
        @RequestParam(value = "corpId") Long corpId) {
        return RestUtils.buildListResponse(siteService.getCorpNoCardList(corpId));
    }

    @Operation(summary = "企业禁用，无卡充电结算停止")
    @GetMapping("/dataCore/site/updateNoCardSetting")
    public BaseResponse updateNoCardSetting(@RequestParam(value = "corpId") Long corpId) {
        return siteService.updateNoCardSetting(corpId);
    }

    @Operation(summary = "商户会员禁用，无卡充电结算使用的场站")
    @GetMapping("/dataCore/site/getCommNoCardList")
    public ListResponse<SiteVo> getCommNoCardList(@RequestParam(value = "commId") Long commId,
        @RequestParam(value = "userId") Long userId) {
        return RestUtils.buildListResponse(siteService.getCommNoCardList(commId, userId));
    }

    @Operation(summary = "商户会员禁用，无卡充电账户同时停用")
    @GetMapping("/dataCore/site/updateCommNoCardList")
    public BaseResponse updateCommNoCardList(@RequestParam(value = "commId") Long commId,
        @RequestParam(value = "userId") Long userId) {
        return siteService.updateCommNoCardList(commId, userId);
    }

    @Operation(summary = "通过商户链获取枪头总数")
    @GetMapping("/dataCore/site/getChargerNumByChain")
    public ObjectResponse<Long> getChargerNumByChain(
        @RequestParam(value = "commIdChain") String commIdChain) {
        IotAssert.isTrue(StringUtils.isNotEmpty(commIdChain), "商户信息不能为空");
        return siteService.getChargerNumByChain(commIdChain);
    }

    @Operation(summary = "通过场站组列表获取场站id列表")
    @PostMapping("/dataCore/site/getSiteListByGids")
    public ListResponse<String> getSiteListByGids(
        @RequestBody ListSiteParam params) {
//        IotAssert.isTrue(CollectionUtils.isNotEmpty(params.getGids()), "请传入场站组列表");
        return siteService.getSiteListByGids(params);
    }

    @Operation(summary = "查询场站存在于哪些场站组")
    @GetMapping("/dataCore/site/getGidsBySiteId")
    public ListResponse<String> getGidsBySiteId(@RequestParam(value = "siteId") String siteId) {
        return siteService.getGidsBySiteId(siteId);
    }

    @GetMapping("/dataCore/site/getSiteQrCodeVo")
    public ObjectResponse<SiteQrCodeVo> getSiteQrCodeVo(
        @RequestParam(value = "siteId") String siteId) {
        return siteService.getSiteQrCodeVo(siteId);
    }

    /**
     * 获取关联场站
     *
     * @param billNoList 结算单号列表
     * @return
     */
    @PostMapping("/dataCore/site/getSiteByBillNoList")
    public ListResponse<String> getSiteByBillNoList(@RequestBody List<String> billNoList) {
        log.info("billNoList = {}", billNoList);
        Assert.isTrue(CollectionUtils.isNotEmpty(billNoList), "请传入结算单号列表");
        return RestUtils.buildListResponse(siteService.getSiteByBillNoList(billNoList));
    }

    @GetMapping("/dataCore/site/getExistingOperateCorpCodes")
    public ListResponse<String> getExistingOperateCorpCodes() {
        return siteService.getExistingOperateCorpCodes();
    }

    @Operation(summary = "同步互联互通计费信息")
    @PostMapping("/dataCore/site/syncHlhtTemplate")
    public BaseResponse syncHlhtTemplate(ServerHttpRequest request,
        @RequestBody CecQueryQeuipBusinessPolicyResult cecQueryQeuipBusinessPolicyResult) {
        Assert.isTrue(StringUtils.isNotEmpty(cecQueryQeuipBusinessPolicyResult.getFullPlugNo()),
            "完整的枪头编号为空");
        this.siteService.syncHlhtTemplate(cecQueryQeuipBusinessPolicyResult);
        return new BaseResponse();
    }

    @Operation(summary = "根据场站ID，获取场站运维、售后人员，在工单转派使用")
    @GetMapping(value = "/dataCore/site/getUserListBySiteId")
    public ListResponse<SysUserVo> getUserListBySiteId(
        @RequestParam(value = "siteId") String siteId) {
        IotAssert.isNotBlank(siteId, "场站ID不能为空");
        return siteService.getUserListBySiteId(siteId);
    }

    @Operation(summary = "根据场站id集合返回占位费信息集合")
    @PostMapping(value = "/dataCore/site/getOverTimeParkList")
    public ListResponse<SiteOverTimeParkDTO> getOverTimeParkList(
        @RequestBody List<String> siteIdList) {
        log.info("获取场站占位费信息集合: siteIdList.size = {}",
            JsonUtils.toJsonString(siteIdList.size()));
        return siteService.getOverTimeParkList(siteIdList);
    }

    /**
     * <p> 通过MQ消息将场站信息同步给所有微服务, 用于定时任务触发</p>
     * <p> 默认仅同步‘上线’的场站</p>
     */
    @Operation(summary = "同步场站信息", description = "通过MQ消息将场站信息同步给所有微服务, 用于定时任务触发. 默认仅同步‘上线’的场站")
    @PostMapping(value = "/dataCore/site/syncSitesInfo")
    public ListResponse<String> syncSitesInfo(
        @RequestParam(value = "siteIdList", required = false) List<String> siteIdList,
        @RequestParam(value = "statusList", required = false) List<SiteStatus> statusList) {
        log.info("同步场站信息. siteIdList = {}, statusList = {}", siteIdList, statusList);
        ListResponse<String> result = siteService.syncSitesInfo(siteIdList, statusList);
        log.info("同步场站信息完成");
        return result;
    }

    @GetMapping(value = "/dataCore/site/getOptimalScoreSettingList")
    @Operation(summary = "待支付页面-根据订单号获取按照优惠排序后的积分体系列表")
    ListResponse<UserScoreSettingLevelSiteGidDto> getOptimalScoreSettingList(
        @RequestParam(value = "orderNo") String orderNo ) {
        IotAssert.isNotBlank(orderNo, "订单编号不能为空");
        return siteService.getOptimalScoreSettingList(orderNo);
    }
}
