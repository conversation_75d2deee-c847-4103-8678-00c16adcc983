package com.cdz360.biz.dc.domain.vo;

import com.cdz360.biz.dc.domain.ChargerOrderTimeDivision;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * :
 * @since 2019-03-11 16:47
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChargerOrderTimeDivisionVo extends ChargerOrderTimeDivision implements Serializable{

    protected static final long serialVersionUID = 1L;

    /**
     * 站点Id
     */
    private String siteId;

    /**
     * 省份编码
     * */
    private Integer province;


    /**
     * 城市编码
     * */
    private Integer city;


    /**
     * 平台商户id
     */
    private String commId;


    /**
     * 设备运营商id
     */
    private String deviceCommId;




}
