package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.biz.ds.trading.ro.comm.ds.CommercialRoDs;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import com.cdz360.biz.model.order.type.OrderPayType;
import com.cdz360.biz.utils.feign.user.UserFeignClient;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUserVo;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import java.time.Duration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AccountBizService {


    @Autowired
    private UserFeignClient tdUserFeignClient;

    @Autowired
    private CommercialRoDs commercialRoDs;

    public String getAccountName(Integer defaultPayType, Long payAccountId) {
        String accountName = "";
        if (defaultPayType == null || payAccountId == null) {
            return accountName;
        }
        if (OrderPayType.PERSON.getCode() == defaultPayType) {
            accountName = "个人账户";
        } else if (OrderPayType.PREPAY.getCode() == defaultPayType) {
            accountName = "第三方支付";  // jira BUG0919-299
        } else if (OrderPayType.BLOC.getCode() == defaultPayType) {
            ObjectResponse<RBlocUserVo> rBlocUser = tdUserFeignClient.findRBlocUserVoById(payAccountId)
                    .block(Duration.ofSeconds(50L));
            if (rBlocUser != null && rBlocUser.getStatus() == ResultConstant.RES_SUCCESS_CODE && rBlocUser.getData() != null) {
                ObjectResponse<CorpPo> corpRes = tdUserFeignClient.getCorp(rBlocUser.getData().getBlocUserId())
                        .block(Duration.ofSeconds(50L));
                FeignResponseValidate.check(corpRes);
                StringBuilder sb = new StringBuilder();
                sb.append("企业账户");
                sb.append("-");
                sb.append(corpRes.getData().getBlocUserName());
                accountName = sb.toString();

            }
        } else if (OrderPayType.MERCHANT.getCode() == defaultPayType) {
            CommercialSimpleVo commercial = commercialRoDs.getCommercial(payAccountId);
            if (commercial != null) {
                StringBuilder sb = new StringBuilder();
                sb.append("商户会员");
                sb.append("-");
                sb.append(commercial.getCommName());
                accountName = sb.toString();
            }
        } else if(PayAccountType.WX_CREDIT.getCode() == defaultPayType) {
            accountName = "微信信用充";
        }else if(PayAccountType.ALIPAY_CREDIT.getCode() == defaultPayType) {
            accountName = "支付宝信用充";
        }
        return accountName;
    }
}
