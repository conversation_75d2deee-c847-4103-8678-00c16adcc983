package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.dc.domain.OrderInMongo;
import com.cdz360.biz.dc.service.ChargerQueryService;
import com.cdz360.biz.dc.service.OrderDataService;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ChargerQueryController
 *
 * @since 2/11/2020 12:29 PM
 * <AUTHOR>
 */
@RestController
@RequestMapping("/dataCore/chargerQuery")
@Slf4j
public class ChargerQueryController {

    @Autowired
    private ChargerQueryService chargerQueryService;

    @Autowired
    private OrderDataService orderDataService;

    /**
     * 获取枪头订单信息
     *
     * @param orderNo
     * @return
     * @throws DcServiceException
     */
    @GetMapping("/getChargerOrderInfo")
    public ObjectResponse<OrderInMongo> getChargerOrderInfo(@RequestParam("orderNo") String orderNo) {

        IotAssert.isNotBlank(orderNo, "请传入订单号");
        log.info("枪头订单信息。 orderNo: {}", orderNo);

        OrderInMongo orderInMongo = chargerQueryService.getOrderInMongo(orderNo);
//        OrderInMongo orderInMongo = null;
        if (orderInMongo == null) {
            orderInMongo = orderDataService.getOssOrderDetail(orderNo);
        }
        if (orderInMongo == null) {
            log.warn("订单信息不存在. orderNo = {}", orderNo);
            return new ObjectResponse<>(null);
        }
        //FIXME 没必要都传这坨东西，只取最后一个
        if (CollectionUtils.isNotEmpty(orderInMongo.getDetails())) {
            List lastest = List.of(orderInMongo.getDetails().get(orderInMongo.getDetails().size() - 1));
            orderInMongo.setDetails(lastest);
        }

        log.info("枪头订单信息。 orderInMongo: {}", JsonUtils.toJsonString(orderInMongo));

        return new ObjectResponse<>(orderInMongo);
    }
}