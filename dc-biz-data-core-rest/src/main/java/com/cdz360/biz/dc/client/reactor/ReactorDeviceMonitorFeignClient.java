package com.cdz360.biz.dc.client.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.es.po.EssAlarmPo;
import com.cdz360.biz.dc.domain.WWarningRecordInMongo;
import com.cdz360.biz.ess.model.param.ListEssAlarmParam;
import com.cdz360.biz.model.trading.bi.param.UpdateWarningYwParam;
import java.util.Date;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

/**
 * DeviceMonitorFeignClient
 *
 * @since 3/29/2020 9:42 PM
 * <AUTHOR>
 */
@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_IOT_MONITOR,
    fallbackFactory = ReactorDeviceMonitorFeignClientHystrixFactory.class)
public interface ReactorDeviceMonitorFeignClient {

    @PostMapping(value = "/api/alarm/evseErrorAlarm")
    Mono<ListResponse<WWarningRecordInMongo>> evseErrorAlarm(@RequestParam("startTime")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
        @RequestParam("endTime")
        @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
        @RequestParam(name = "plugId", required = false) Integer plugId,
        @RequestParam(name = "evseNo", required = false) String evseNo,
        @RequestParam("siteId") String siteId);

    // 获取储能设备告警参数
//    @PostMapping(value = "/ess/alarm/getEssAlarmRecordList")
//    Mono<ListResponse<EssAlarmVo>> getEssAlarmRecordList(
//        @RequestBody ListEssAlarmParam param);

    @PostMapping(value = "/ess/alarm/getEssAlarmList")
    Mono<ListResponse<EssAlarmPo>> getEssAlarmList(
        @RequestBody ListEssAlarmParam param);

    @PostMapping(value = "/api/alarm/updateYwOrderByEvseList")
    Mono<BaseResponse> updateYwOrderByEvseList(@RequestBody UpdateWarningYwParam param);
}