package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.dc.service.SimService;
import com.cdz360.biz.model.sim.vo.SimImportParam;
import com.cdz360.biz.model.sim.vo.SimImportVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class SimRest {

    @Autowired
    private SimService service;

    /**
     * 解析批量导入excel文件
     * @param param
     * @return
     */
    @PostMapping("/dataCore/sim/parseSimExcel")
    public ObjectResponse<SimImportVo> parseSimExcel(@RequestBody SimImportParam param) {
        if (CollectionUtils.isEmpty(param.getList())) {
            log.info("无法获取有效信息");
            throw new DcServiceException("无法获取有效信息，请检查excel文件内容");
        }
        return service.parseSimExcel(param);
    }

}
