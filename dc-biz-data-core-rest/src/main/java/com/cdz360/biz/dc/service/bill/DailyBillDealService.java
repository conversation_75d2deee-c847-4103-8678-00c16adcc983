package com.cdz360.biz.dc.service.bill;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.trading.bill.param.NotifyDailyBillParam;
import com.cdz360.biz.model.trading.bill.po.ZftDailyBillPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class DailyBillDealService {

    private Map<PayChannel, DailyBillDealStrategy> strategyMap = new ConcurrentHashMap<>();

    protected void addStrategy(PayChannel channel, DailyBillDealStrategy strategy) {
        this.strategyMap.put(channel, strategy);
    }

    public void dealWithDailyBill(ZftDailyBillPo po, NotifyDailyBillParam param) {
        DailyBillDealStrategy strategy = this.strategyMap.get(po.getChannel());
        if (null == strategy) {
            log.error("支付平台处理方式无效: po = {}", JsonUtils.toJsonString(po));
            throw new DcArgumentException("支付平台不处理");
        }
        strategy.execute(po, param);
    }

    public void dealWithRegexDailyBill(NotifyDailyBillParam param) {
        DailyBillDealStrategy strategy = this.strategyMap.get(PayChannel.ALIPAY);
        if (null == strategy) {
            log.error("支付平台处理方式无效: po = {}", JsonUtils.toJsonString(param));
            throw new DcArgumentException("支付平台不处理");
        }
        strategy.regexExecute(param);
    }
}
