package com.cdz360.biz.dc.constant;

/**
 * 订单结束原因适配类
 *
 * <AUTHOR>
 *         类型适配转换
 */
//public class OrderStopType {
//    private static Logger logger = LoggerFactory.getLogger(OrderStopType.class);
//
//    public static String orderSource(String source) {
//
//        try {
//            if (source != null && !"".equals(source)) {
//                return orderSourceEnum.valueOf(source).getContext() + "";
//            }
//        } catch (Exception e) {
//            logger.error("转换失败", e);
//            return null;
//        }
//        return null;
//    }
//
//    public enum orderSourceEnum {
//        /** 按照服务类型（按电量充电，按时长充电，按金额充电等）正常停止充电 */
//        CHG_STOP_NORMAL("0", "正常停止"),
//        /**用户本地刷卡停止充电*/
//        CHG_STOP_LOCAL_CARD("1", "用户本地刷卡停止充电"),
//        /**用户本地输入校验码停止充电*/
//        CHG_STOP_LOCAL_PIN("2", "用户本地输入校验码停止充电"),
//        /**用户远程结束*/
//        CHG_STOP_REMOTE_USER("3", "用户远程结束充电"),
//        /**管理员远程结束*/
//        CHG_STOP_REMOTE_ADMIN("4", "管理员远程结束充电"),
//        /**急停按下，停止充电*/
//        CHG_STOP_EMERGENCY_STOP("5", "用户本地按下急停停止充电"),
//        CHG_STOP_EV_DISCONNECT("6", "检测到枪头断开而停止充电"),
//        CHG_STOP_REBOOT("7", "检测到系统重启而停止充电"),
//        CHG_STOP_OFFLINE("8", "检测到充电桩断线而停止充电"),
//        CHG_STOP_POWER_LOSS("9", "检测到充电桩电桩掉电而停止充电"),
//        CHG_STOP_SYSTEM_FAULT("10", "检测到充电桩故障而停止充电"),
//        CHG_STOP_BMS_FAULT("11", "检测到电动车故障而停止充电"),
//        CHG_STOP_OTHER("12", "其他原因停止充电"),
//        CHG_STOP_METER_FAULT("13", "电表故障停止充电"),
//        CHG_STOP_CARD_FAULT("14", "刷卡器故障停止充电"),
//        CHG_STOP_LCD_FAULT("15", "LCD故障停止充电"),
//        CHG_STOP_FULL("16", "检测到充满停止充电"),
//        CHG_STOP_LOW_BALANCE("17", "检测到余额不足而停止充电"),
//        CHG_STOP_OVERLOAD("18", "检测到过载停止充电"),
//        CHG_STOP_OVER_VOLTAGE("19", "检测到电压过高停止充电"),
//        CHG_STOP_UNDER_FAULT("20", "检测到电压过低停止充电");
////        CHG_STOP_NORMAL("0", "按照充电服务类型正常停止充电"),
////        CHG_STOP_LOCAL_CARD("1", "用户本地刷卡停止充电"),
////        CHG_STOP_LOCAL_PIN("2", "用户本地输入校验码停止充电"),
////        CHG_STOP_REMOTE_USER("3", "用户远程结束充电"),
////        CHG_STOP_REMOTE_ADMIN("4", "管理员远程结束充电"),
////        CHG_STOP_EMERGENCY_STOP("5", "用户本地按下急停停止充电"),
////        CHG_STOP_EV_DISCONNECT("6", "检测到枪头断开而停止充电"),
////        CHG_STOP_REBOOT("7", "检测到系统重启而停止充电"),
////        CHG_STOP_OFFLINE("8", "检测到充电桩断线而停止充电"),
////        CHG_STOP_POWER_LOSS("9", "检测到充电桩电桩掉电而停止充电"),
////        CHG_STOP_SYSTEM_FAULT("10", "检测到充电桩故障而停止充电"),
////        CHG_STOP_BMS_FAULT("11", "检测到电动陈故障而停止充电"),
////        /** 对应接入平台：其它原因，停止充电 */
////        CHG_STOP_LOW_BALANCE("12", "检测到到余额不足而停止充电"),
////        /** 对应接入平台：检测到过载停止充电 */
////        CHG_STOP_OVERLOAD("13", "电表故障"),
////        /** 对应接入平台：检测到未结负载停止充电 */
////        CHG_STOP_NOT_LOAD("14", "刷卡器故障"),
////        /** 对应接入平台：检测到未结负载停止充电 */
////        CHG_STOP_FULL("15", "LCD故障"),
////        /** 对应接入平台：检测到金额充够停止充电 */
////        CHG_STOP_MONEY_ENOUGTH("16", "soc充满停止"),
////        /** 对应接入平台：检测到时间充够停止充电 */
////        CHG_STOP_TIME_ENOUGTH("17", "soc充满停止"),
////        /** 对应接入平台：其他原因停止充电 */
////        CHG_STOP_OTHER("255", "soc充满停止");
//
//
//        private String newSorce;
//        private String context;
//
//        private orderSourceEnum(String newSorce, String context) {
//            this.newSorce = newSorce;
//            this.context = context;
//        }
//
//
//        public String getErrorCode() {
//            return newSorce;
//        }
//
//
//        public String getErrorMessage() {
//            return context;
//        }
//
//
//        /**
//         * 普通方法
//         * @param newSorce
//         * @return
//         */
//        public static String getContext(String newSorce) {
//            for (orderSourceEnum orderSourceEnum : OrderStopType.orderSourceEnum.values()) {
//                if (newSorce.equals(orderSourceEnum.getNewSorce())) {
//                    return orderSourceEnum.context;
//                }
//            }
//            return null;
//        }
//
//        /**
//         * @return the newSorce
//         */
//        public String getNewSorce() {
//            return newSorce;
//        }
//
//        /**
//         * @param newSorce the newSorce to set
//         */
//        public void setNewSorce(String newSorce) {
//            this.newSorce = newSorce;
//        }
//
//        /**
//         * @return the context
//         */
//        public String getContext() {
//            return context;
//        }
//
//        /**
//         * @param context the context to set
//         */
//        public void setContext(String context) {
//            this.context = context;
//        }
//
//    }
//
//
//    public static void main(String[] args){
//        String sss = OrderStopType.orderSourceEnum.getContext("3");
//        System.out.println(sss);
//    }
//}
