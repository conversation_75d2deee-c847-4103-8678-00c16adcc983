package com.cdz360.biz.dc.domain.erp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "运营数据查看实体")
public class ErpViewModel {
    @Schema(description = "ERP系统表单内码（使用内码时必录）")
    private String id;

    @Schema(description = "ERP系统单据编码，字符串类型（使用编码时必录）")
    private String number;

    @Schema(description = "ERP系统创建者组织内码，字符串类型（非必录）")
    private String createOrgId;
}
