package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.client.AuthCenterFeignClient;
import com.cdz360.biz.dc.service.site.SiteDefaultSettingService;
import com.cdz360.biz.ds.trading.ro.site.ds.EvseCfgScheduleRoDs;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.iot.param.ListEvseCfgResultParam;
import com.cdz360.biz.model.iot.param.ModifyEvseCfgParam;
import com.cdz360.biz.model.iot.vo.EvseCfgResultVo;
import com.cdz360.biz.model.site.po.PriceItemPo;
import com.cdz360.biz.model.site.po.PriceTemplatePo;
import com.cdz360.biz.model.trading.site.param.ListPriceTemplateParam;
import com.cdz360.biz.model.trading.site.po.BsBoxSettingPo;
import com.cdz360.biz.model.trading.site.po.EvseCfgSchedulePo;
import com.cdz360.biz.model.trading.site.vo.EvseCfgScheduleVo;
import com.cdz360.biz.model.trading.site.vo.PriceSchemeSiteVo;
import com.cdz360.biz.utils.feign.hlht.OpenHlhtFeignClient;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.domain.param.CorpWxSendMsgCommonParam;
import com.chargerlinkcar.framework.common.domain.param.CorpWxSendMsgKV;
import com.chargerlinkcar.framework.common.domain.param.ListPriceSchemeSiteUseParam;
import com.chargerlinkcar.framework.common.domain.type.EvseCfgEnum;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class EvseCfgScheduleService {

    @Autowired
    private EvseCfgScheduleRoDs evseCfgScheduleRoDs;

    @Autowired
    private PriceSchemaBizService priceSchemaBizService;

    @Autowired
    private BsBoxSettingService bsBoxSettingService;

    @Autowired
    private SiteDefaultSettingService siteDefaultSettingService;

    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;

    @Autowired
    private OpenHlhtFeignClient openHlhtFeignClient;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    // 下发桩配置
    // 单位是: 分钟
    @Value("${data.schedule.permitTime:10}")
    private Integer permitTime;

    /**
     * 禁用定时下发信息
     *
     * @param idList
     * @return
     */
    public int disableSchedule(List<Long> idList) {
        log.info("禁用定时信息: idList = {}", idList);
        if (null == idList || idList.isEmpty()) {
            return 0;
        }

        return this.evseCfgScheduleRoDs.disableSchedule(idList);
    }

    /**
     * 通过计费模板获取定时下发信息
     *
     * @param param
     * @return
     */
    public List<PriceSchemeSiteVo> getByPriceSchemeId(ListPriceSchemeSiteUseParam param) {
        log.info("通过计费模板获取定时下发信息: priceSchemeId = {}", param.getPriceSchemeIdList());

        // 待下发的计费模板
        List<PriceSchemeSiteVo> result = this.evseCfgScheduleRoDs.getByPriceSchemeId(param);

        // 已使用的计费模板
        List<PriceSchemeSiteVo> usedList = bsBoxSettingService.getSchemeSiteByPriceSchemeId(param);

        result.addAll(usedList);
        log.info("size = {}", result.size());
        return result;
    }

    /**
     * 注: 这里使用当前时间作为需要下发的的查询条件，存在时间差
     */
//    @Transactional
    public void schedule() {
        // 查询当前是否存在下发的计费信息
        // t_evse_cfg_shedule
        String pattern = "yyMMddHHmm";
        SimpleDateFormat dateFormat = new SimpleDateFormat(pattern);
        try {
            Date scheduleTime = dateFormat.parse(dateFormat.format(new Date()));
            log.info("schedule time = {}", scheduleTime);

            // FIXME: 这里可能涉及返回数量巨大，导致内存爆炸问题，暂时不考虑
            List<EvseCfgSchedulePo> evseCfgList = evseCfgScheduleRoDs.getEvseCfgList(null,
                scheduleTime);
            log.info("size = {}", evseCfgList.size());
            if (evseCfgList.isEmpty()) {
                return;
            }

            // 找出有效的定时信息
            ListEvseCfgResultParam cfgParam = new ListEvseCfgResultParam();
            cfgParam.setEvseNoList(evseCfgList.stream().map(EvseCfgSchedulePo::getEvseNo)
                .collect(Collectors.toList()));
            ListResponse<EvseCfgResultVo> voList = iotDeviceMgmFeignClient.getEvseCfgResultList(
                cfgParam);
            FeignResponseValidate.check(voList);

            Map<String, EvseCfgResultVo> cfgMap = voList.getData().stream()
                .collect(Collectors.toMap(EvseCfgResultVo::getEvseNo, o -> o));

            // 需要下发的列表
//            List<EvseCfgSchedulePo> notDown = evseCfgList.stream().filter(s -> {
//                EvseCfgVo vo = cfgMap.get(s.getEvseNo());
//                return vo.getPriceCodeEffectiveTime().getTime() >= s.getScheduleTime().getTime();
//            }).collect(Collectors.toList());

            // 不需要下发的列表
            List<EvseCfgSchedulePo> needDown = evseCfgList.stream().filter(s -> {
                EvseCfgResultVo vo = cfgMap.get(s.getEvseNo());
                if (null == vo || vo.getPriceCodeEffectiveTime() == null) {
                    return true;
                }

                return vo.getPriceCodeEffectiveTime().getTime() < s.getScheduleTime().getTime();
            }).collect(Collectors.toList());

            // 下发前变更下发状态
            // enable=false
            this.disableSchedule(
                evseCfgList.stream().map(EvseCfgSchedulePo::getId).collect(Collectors.toList()));

            // 根据计费模板进行分组
            Map<Long, List<EvseCfgSchedulePo>> tempIdMap = needDown.stream()
                .collect(Collectors.groupingBy(EvseCfgSchedulePo::getPriceSchemeId));

            // 查出所有计费模板的信息
            Set<Long> tempIdSet = tempIdMap.keySet();
            log.info("temp id size = {}", tempIdSet.size());
            if (tempIdSet.isEmpty()) {
                return;
            }

            List<PriceTemplatePo> priceTemplateList = priceSchemaBizService.getPriceTemplateList(
                new ListPriceTemplateParam().setIdList(new ArrayList<>(tempIdSet))
                    .setDeleteFlag(false).setEnable(true));
            if (priceTemplateList.isEmpty()) {
                log.warn("没有找到对应的计费模板");
                return;
            }

            // 场站默认设置处理，并同步互联互通场站最新价格策略到孙建飞库
            siteDefaultSettingService.setSiteDefaultSetting(needDown);

            // 组装需要变更的计费变更参数
            tempIdSet.forEach(tempId -> {
                // 获取子模版
                List<PriceItemPo> itemList = priceSchemaBizService.getPriceItemListByTempId(tempId);

                // v2接口
                // 所有桩编号
                Set<String> evseNoList = tempIdMap.get(tempId).stream()
                    .map(EvseCfgSchedulePo::getEvseNo).collect(Collectors.toSet());

                ModifyEvseCfgParam param = new ModifyEvseCfgParam();
                param.setEvseNoList(new ArrayList<>(evseNoList));
                param.setPriceSchemeId(tempId);
                param.setPriceSchemeList(priceSchemaBizService.priceItem2Charge(itemList));

                // 调用 iotWorker 下发
                try {
                    this.priceSchemaBizService.scheduleTempDown(param);
                } catch (Exception e) {
                    log.error("下发失败: msg = {}", e.getMessage(), e);
                }
            });

            Map<String, List<EvseCfgSchedulePo>> needPushWx = needDown.stream().collect(Collectors.groupingBy(by ->
                    by.getProcessInstanceId()+ "///" + by.getPriceSchemeId() + "///" + by.getSiteId() + "///" + by.getOpUid()));
            needPushWx.entrySet().stream().forEach(e -> {
                String[] split = e.getKey().split("///");
                String processInstanceId = split[0];
                Long priceSchemeId = Long.valueOf(split[1]);
                String siteId = split[2];
                Long opUid = Long.valueOf(split[3]);
                Optional<PriceTemplatePo> templateInfo = priceSchemaBizService.getPriceSchema(
                        priceSchemeId, null);
                ObjectResponse<PriceTemplatePo> priceRes = new ObjectResponse<>(templateInfo.orElse(null));
                FeignResponseValidate.check(priceRes);
                this.pushWx(processInstanceId, siteId, opUid, priceRes.getData().getName());
            });
        } catch (Exception e) {
            log.error("时间转换异常: msg = {}", e.getMessage(), e);
        }
    }

    /**
     * 按条件批量插入
     *
     * @param poList
     * @return
     */
    public Integer batchInsert(List<EvseCfgSchedulePo> poList, Boolean isTwice, Boolean isFirstImmediately) {
        if (null == poList || poList.isEmpty()) {
            return 0;
        }
        // 非二轮或者首轮为立即下发的二轮
        if (!isTwice || isFirstImmediately) {
            this.evseCfgScheduleRoDs.disableScheduleByEvseNo(
                    null, poList.stream().map(EvseCfgSchedulePo::getEvseNo).collect(Collectors.toList()));
        }
        return this.evseCfgScheduleRoDs.batchInsert(poList);
    }

    /**
     * 批量插入
     *
     * @param poList
     * @return
     */
    public Integer batchInsert(List<EvseCfgSchedulePo> poList) {
        if (null == poList || poList.isEmpty()) {
            return 0;
        }
        // disable 桩之前的定时
        this.evseCfgScheduleRoDs.disableScheduleByEvseNo(
                null, poList.stream().map(EvseCfgSchedulePo::getEvseNo).collect(Collectors.toList()));

        return this.evseCfgScheduleRoDs.batchInsert(poList);
    }

    /**
     * 过滤掉不可下发的桩
     *
     * @param evseNoList
     * @return
     */
    public List<String> downFilter(List<String> evseNoList) {
        if (null == evseNoList || evseNoList.isEmpty()) {
            log.info("入参为空, 返回空");
            return new ArrayList<>();
        }

        // t_evse_cfg_schedule
        Calendar c = new GregorianCalendar();
        c.setTime(new Date());
        c.add(Calendar.MINUTE, permitTime); // 下发前十分钟的不能再下发
        List<String> disableEvseNoList = evseCfgScheduleRoDs.getEvseCfgList(evseNoList, c.getTime())
            .stream().map(EvseCfgSchedulePo::getEvseNo).collect(Collectors.toList());
        log.info("[t_evse_cfg_schedule]不可下发的桩编号: disableEvseNoList = {}",
            disableEvseNoList);

        // 删除不可下发的桩
        evseNoList.removeAll(disableEvseNoList);

        // t_bs_box_setting 中的充电中
        disableEvseNoList = bsBoxSettingService.getByEvseNo(evseNoList,
                Collections.singletonList(EvseCfgEnum.EVSE_CFG_SEND_PROCESS.value), true)
            .stream().map(BsBoxSettingPo::getBoxOutFactoryCode).collect(Collectors.toList());
        log.info("[t_bs_box_setting]不可下发的桩编号: disableEvseNoList = {}", disableEvseNoList);

        // 删除不可下发的桩
        evseNoList.removeAll(disableEvseNoList);

        log.info("有效的桩编号个数: size = {}", evseNoList.size());
        return evseNoList;
    }

    public List<EvseCfgScheduleVo> getByEvseNo(List<String> evseNoList) {
        log.info("获取桩定时任务: evseNoList = {}", evseNoList);
        return this.evseCfgScheduleRoDs.getByEvseNo(evseNoList, false);
    }

    public void disableScheduleByEvseNo(Long priceSchemeId, List<String> evseNoList) {
        log.info("禁用定时信息: priceSchemeId = {}", priceSchemeId);
        if (null == evseNoList || evseNoList.isEmpty()) {
            log.warn("桩编号不能为空");
            throw new DcArgumentException("桩编号不能为空");
        }

        int i = this.evseCfgScheduleRoDs.disableScheduleByEvseNo(priceSchemeId, evseNoList);
        log.info("更新: i = {}", i);
    }

    /**
     * 更新定时中的计费模板Id
     *
     * @param tempIdList
     * @return
     */
    public int updatePriceSchedule(List<Long> tempIdList) {
        log.info("更新定时中的计费模板Id: tempIdList = {}", tempIdList);

        if (CollectionUtils.isEmpty(tempIdList)) {
            throw new DcArgumentException("请指定计费模板Id列表");
        }

        // 10分钟约束
        // schduleTime >= time 说明存在
        LocalDateTime time = LocalDateTime.now().plusMinutes(10);
        Date scheduleTime = Date.from(time.atZone(ZoneId.systemDefault()).toInstant());
        EvseCfgSchedulePo priceSchedule = this.evseCfgScheduleRoDs.findPriceSchedule(tempIdList,
            scheduleTime);
        if (null != priceSchedule) {
            throw new DcArgumentException("指定为定时下发的计费模板，下发10分钟内不能不做变更操作");
        }

        // 更新成最新的计费模板
        Optional<Long> max = tempIdList.stream().max(Comparator.comparing(Long::longValue));
        return max.map(aLong -> this.evseCfgScheduleRoDs.updatePriceSchedule(tempIdList, aLong))
            .orElse(0);
    }

    public void createPriceSchedule(ModifyEvseCfgParam param) {
        log.info(">> 下发计费模板: opUid = {}", param.getOpUid());
        Assert.notNull(param.getPriceSchemeId(), "参数错误，计费模板Id不能为空");

        Optional<PriceTemplatePo> templateInfo = priceSchemaBizService.getPriceSchema(
            param.getPriceSchemeId(), null);
        ObjectResponse<PriceTemplatePo> priceRes = new ObjectResponse<>(templateInfo.orElse(null));

        FeignResponseValidate.check(priceRes);

        if (Boolean.FALSE.equals(priceRes.getData().getEnable())) {
            throw new DcArgumentException("该计费模板已被禁用，请选择其他计费模板");
        }

        if (null == param.getSchedule()) {
            // 是否默认场站计费
            // 接口包含逻辑: 判断场站是否第一次下发，如果是第一次下发会默认设置上的
            this.setDefaultPriceScheme(
                param.getPriceSchemeId(), param.getSiteId(),
                param.getSiteDefault() == null ? false : param.getSiteDefault());

            // 即时下发
            priceSchemaBizService.priceTempDown(param);

            //企业微信推送
            this.pushWx(param.getProcessInstanceId(), param.getSiteId(), param.getOpUid(), priceRes.getData().getName());

            log.info("<< 即时下发完成完成");
        } else {
            //定时下发判断当前时间是否大于下发时间
//            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");//设置日期格式
//            if (format.format(new Date()).compareTo(format.format(param.getSchedule()))>0) {
//                throw new DcArgumentException("下发时间不能早于当前时间");
//            }
            // 将定时时间的毫秒去掉在比较
            if (System.currentTimeMillis() > param.getSchedule().getTime() / 1000 * 1000) {
                throw new DcArgumentException("下发时间不能早于当前时间");
            }

            // 定时下发
            List<EvseCfgSchedulePo> poList = param.getEvseNoList().stream().map(evseNo -> {
                EvseCfgSchedulePo po = new EvseCfgSchedulePo();
                po.setEvseNo(evseNo)
                    .setOpUid(param.getOpUid())
                    .setPriceSchemeId(param.getPriceSchemeId())
                    .setSiteDefault(param.getSiteDefault() == null ? false : param.getSiteDefault())
                    .setScheduleTime(param.getSchedule())
                    .setSiteId(param.getSiteId())
                    .setProcessInstanceId(param.getProcessInstanceId());
                return po;
            }).collect(Collectors.toList());

            Integer count = batchInsert(poList, param.getIsTwice(), param.getIsFirstImmediately());
            log.info("<< 定时下发: count = {}", count);
        }
    }

    private void setDefaultPriceScheme(Long priceSchemeId, String siteId, Boolean isDefault) {
        log.info(">> 设置场站默认的计费模板: priceSchemeId = {}, siteId = {}", priceSchemeId,
            siteId);

        ObjectResponse<PriceTemplatePo> res = new ObjectResponse();
        res.setStatus(ResultConstant.RES_FAIL_CODE);

        if (Boolean.FALSE.equals(isDefault)) {
            PriceTemplatePo template = priceSchemaBizService.getSitePriceScheme(siteId);
            res = RestUtils.buildObjectResponse(template);
        }

        if (Boolean.TRUE.equals(isDefault) ||
            (res.getStatus() == ResultConstant.RES_SUCCESS_CODE && res.getData() == null)) {
            // 设置默认模板，或，场站原先不存在默认模板
            priceSchemaBizService.setDefaultPriceScheme(siteId, priceSchemeId, null);
        } else {
            log.info("不设定场站默认计费模板");
        }
        log.info("<<");
    }

    // 企业微信推送
    public void pushWx(String processInstanceId, String siteId, Long opUid, String PriceSchemeName) {
        CorpWxSendMsgCommonParam msgParam = new CorpWxSendMsgCommonParam();
        msgParam.setTitle("电价下发");
        CorpWxSendMsgKV kv1 = new CorpWxSendMsgKV("申请单号", processInstanceId);
        CorpWxSendMsgKV kv2 = new CorpWxSendMsgKV("模板名称", PriceSchemeName);
        CorpWxSendMsgKV kv3 = new CorpWxSendMsgKV("提醒内容", "电价下发已执行，请确认");
        msgParam.setContentItems(List.of(kv1, kv2, kv3));
        msgParam.setPage("packageOa/pages/flow/applicationDetail/applicationDetail?procInstId=" + processInstanceId);
        ListResponse<SysUserVo> response = authCenterFeignClient.querySysUserByIds(List.of(opUid));
        FeignResponseValidate.check(response);
        SysUserVo sysUserVo = response.getData().get(0);
        if (StringUtils.isBlank(sysUserVo.getCorpWxUid())) {
            log.warn("缺少企业微信用户ID: {}", opUid);
            return;
        }
        msgParam.setToUser(sysUserVo.getCorpWxUid());
        log.info("企业推送，TopCommId: {},参数: {}", sysUserVo.getTopCommId(), msgParam);
        openHlhtFeignClient.sendMsgCommon(sysUserVo.getTopCommId(), msgParam)
                .subscribe(x -> log.info("企业推送结束: {}", x.getData()));
    }
}
