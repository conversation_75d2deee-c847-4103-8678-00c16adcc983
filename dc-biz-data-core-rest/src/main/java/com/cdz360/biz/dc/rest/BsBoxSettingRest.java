package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.BsBoxSettingService;
import com.cdz360.biz.model.trading.site.po.BsBoxSettingPo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 桩配置相关接口
 *
 * <AUTHOR>
 * @since 2020/2/21 15:19
 */
@Slf4j
@RestController
@Tag(name = "桩配置相关接口", description = "bs-box-setting")
public class BsBoxSettingRest {

    @Autowired
    private BsBoxSettingService bsBoxSettingService;

    @Operation(summary = "批量跟新桩配置状态")
    @PostMapping("/dataCore/boxsetting/batchUpdateBoxSettingStatus")
    public BaseResponse batchUpdateBoxSettingStatus(
            ServerHttpRequest request,
            @RequestBody List<BsBoxSettingPo> poList) {
        log.info(LoggerHelper2.formatEnterLog(request, false));
        long i = bsBoxSettingService.batchUpdateBoxSettingStatus(poList);
        log.info("更新 i = {}", i);
        return RestUtils.success();
    }
}
