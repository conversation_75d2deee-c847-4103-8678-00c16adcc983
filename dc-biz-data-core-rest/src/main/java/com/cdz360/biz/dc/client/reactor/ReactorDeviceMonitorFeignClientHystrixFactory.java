package com.cdz360.biz.dc.client.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.es.po.EssAlarmPo;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.domain.WWarningRecordInMongo;
import com.cdz360.biz.ess.model.param.ListEssAlarmParam;
import com.cdz360.biz.model.trading.bi.param.UpdateWarningYwParam;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

/**
 * ReactorUserFeignClientHystrixFactory
 *
 * @since 11/3/2020 10:32 AM
 * <AUTHOR>
 */
@Slf4j
@Component
public class ReactorDeviceMonitorFeignClientHystrixFactory implements
    FallbackFactory<ReactorDeviceMonitorFeignClient> {

    @Override
    public ReactorDeviceMonitorFeignClient apply(Throwable throwable) {
        log.error("err= {}", throwable.getMessage(), throwable);
        return new ReactorDeviceMonitorFeignClient() {

            @Override
            public Mono<ListResponse<WWarningRecordInMongo>> evseErrorAlarm(Date startTime,
                Date endTime, Integer plugId, String evseNo, String siteId) {
                log.error("获取告警列表异常: {}", siteId);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<EssAlarmPo>> getEssAlarmList(ListEssAlarmParam param) {
                log.error("服务[{}]接口熔断 - 获取储能设备告警参数, param = {}",
                    DcConstants.KEY_FEIGN_IOT_MONITOR, param);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<BaseResponse> updateYwOrderByEvseList(UpdateWarningYwParam param) {
                return Mono.just(RestUtils.success());
            }
        };

    }
}