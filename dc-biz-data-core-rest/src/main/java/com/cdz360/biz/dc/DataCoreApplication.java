package com.cdz360.biz.dc;

import com.cdz360.biz.dc.service.invoice.callback.InvoiceCallbackService;
import com.netflix.discovery.EurekaClient;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import reactivefeign.spring.config.EnableReactiveFeignClients;
import reactor.core.publisher.Hooks;

/**
 *
 * <AUTHOR>
 *
 *
 */
@EnableDiscoveryClient
@SpringBootApplication
@EnableReactiveFeignClients(basePackages = {"com.cdz360.biz.utils.feign.*", "com.cdz360.biz.dc.client.reactor"})
@EnableFeignClients(basePackages = {
        "com.cdz360.biz.dc.client",
        "com.chargerlinkcar.framework.common.feign",
        "com.cdz360.biz.utils.feign.iot"
})
@EnableAsync
@Slf4j
@ComponentScan(basePackages = {"com.chargerlinkcar.core", "com.chargerlinkcar.framework.common", "com.cdz360"})
@MapperScan(basePackages = {"com.cdz360.biz.ds.trading.**.mapper",
    "com.cdz360.biz.ds.ess.**.mapper"})
@EnableScheduling
public class DataCoreApplication {

    @Autowired
    private EurekaClient discoveryClient;

    @Autowired
    private InvoiceCallbackService invoiceCallbackService;

    /**
     * 主函数
     */
    public static void main(String[] args) {
        log.info("starting dc-data-core!!!");
        Hooks.enableAutomaticContextPropagation();  // 输出调用链traceId
//        SpringApplication.run(DataCoreApplication.class, args);
        new SpringApplicationBuilder(DataCoreApplication.class).web(WebApplicationType.REACTIVE).run(args);
    }


    @PreDestroy
    public void destroy() {
        log.info("going to shutdown");

        // 发票反馈队列遗留数据
        invoiceCallbackService.destroy();

        //DiscoveryManager.getInstance().shutdownComponent();
        discoveryClient.shutdown();
        log.info("eureka de-registered.....");
    }
}
