package com.cdz360.biz.dc.rest.site;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.siteGroup.SiteGroupService;
import com.cdz360.biz.model.sys.param.SiteGroupSiteParam;
import com.cdz360.biz.model.sys.param.UpdateSiteGroupSiteParam;
import com.cdz360.biz.model.sys.vo.SiteGroupSiteInfoVo;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.ArrayList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/dataCore/siteGroupSite")
@Tag(name = "站点开票相关接口")
public class SiteGroupSiteRefRest {

    @Autowired
    private SiteGroupService siteGroupService;

    @Operation(summary = "获取场站关联的场站组ID列表")
    @PostMapping(value = "/findGidsBySiteId")
    public Mono<ListResponse<String>> findSiteGroupSiteBySiteId(
        ServerHttpRequest request, @RequestBody SiteGroupSiteParam param) {
        log.debug("获取场站关联的场站组ID列表: param = {}", JsonUtils.toJsonString(param));
        return Mono.just(siteGroupService.findSiteGroupSiteBySiteId(param))
            .map(ArrayList::new)
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "获取场站组关联场站信息")
    @PostMapping(value = "/siteInfo")
    public Mono<ListResponse<SiteGroupSiteInfoVo>> findSiteGroupSiteInfo(
        ServerHttpRequest request, @RequestBody SiteGroupSiteParam param) {
        log.debug("获取场站组关联场站信息: param = {}", JsonUtils.toJsonString(param));
        return Mono.just(siteGroupService.findSiteGroupSiteInfo(param))
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "获取场站组关联场站信息")
    @PostMapping(value = "/getSiteListByGidList")
    public Mono<ListResponse<SiteVo>> getSiteListByGidList(
        @RequestBody SiteGroupSiteParam param) {
        return Mono.just(siteGroupService.getSiteListByGidList(param))
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "更新场站组关联场站信息")
    @PostMapping(value = "/updateRef")
    public Mono<ObjectResponse<SiteGroupSiteInfoVo>> updateSiteGroupSiteRef(
        ServerHttpRequest request, @RequestBody UpdateSiteGroupSiteParam param) {
        log.debug("更新场站组关联场站信息: param = {}", JsonUtils.toJsonString(param));
        return Mono.just(siteGroupService.updateSiteGroupSiteRef(param))
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "获取场站组 关联的站点数量")
    @PostMapping(value = "/getSiteAmountByGidList")
    public Mono<ObjectResponse<Long>> getSiteAmountByGidList(
        ServerHttpRequest request, @RequestBody SiteGroupSiteParam param) {
        log.debug("获取关联场站总数: param = {}", JsonUtils.toJsonString(param));
        return Mono.just(siteGroupService.getSiteAmountByGidList(param))
            .map(RestUtils::buildObjectResponse);
    }
}
