package com.cdz360.biz.dc.config;

import com.cdz360.base.model.base.vo.BaseObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties("bill")
public class MchBillRegexConfig {

    private List<Reg> regList;

    @Data
    @Accessors(chain = true)
    public static class Reg {
        private Long topCommId; // 顶级商户ID

        private List<CorpMchInfo> corpMchInfoList;
    }

    @Data
    @Accessors(chain = true)
    @EqualsAndHashCode(callSuper = true)
    public static class CorpMchInfo extends BaseObject {
        @Schema(description = "企业客户ID")
        @JsonInclude(JsonInclude.Include.NON_NULL)
        private Long corpId;

        @Schema(description = "企业客户名称")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String corpName;

        @Schema(description = "指定对账字段索引值 对账字段索引根据对账csv中确定(从0开始)", example = "3")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private int regexFieldIdx;

        @Schema(description = "匹配规则")
        @JsonInclude(JsonInclude.Include.NON_EMPTY)
        private String regex;
    }
}
