package com.cdz360.biz.dc.service.site;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteRwDs;
import com.cdz360.biz.model.site.dto.UpdateSiteInvoicedValidDto;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

import java.util.List;

@Slf4j
@Service
public class SiteInvoiceService {

    @Autowired
    private SiteRwDs siteRwDs;

    @Transactional
    public Mono<BaseResponse> updateInvoicedValid(UpdateSiteInvoicedValidDto dto) {
        IotAssert.isTrue(CollectionUtils.isNotEmpty(dto.getSiteIdList()), "请提供场站ID");
        IotAssert.isNotNull(dto.getInvoicedValid(), "请选择移动端开票状态");
//        IotAssert.isNotNull(dto.getPlatformInvoicedValid(), "请选择平台开票状态");

        dto.getSiteIdList().forEach(siteId -> {
            SitePo site = new SitePo();
            site.setId(siteId);
            site.setInvoicedValid(dto.getInvoicedValid() ? 1 : 0);
            site.setMobileTempSalId(dto.getInvoicedValid() ? dto.getMobileTempSalId() : 0);
            site.setPlatformInvoicedValid(dto.getPlatformInvoicedValid());
            this.siteRwDs.updateSite(site);
        });

        return Mono.just(RestUtils.success());
    }

    @Transactional
    public Mono<BaseResponse> updateTempSalId(Long oldTempSalId, Long newTempSalId) {
        // 先把旧的场站id列表查出来
        List<String> siteIdList = siteRwDs.getSiteIdsByMobileTempSalId(oldTempSalId);
        // 然后进行修改
        if(siteIdList != null && !siteIdList.isEmpty()){
            for(String siteId : siteIdList){
                SitePo site = new SitePo();
                site.setId(siteId);
                site.setMobileTempSalId(newTempSalId != null ? newTempSalId : 0);
                siteRwDs.updateSite(site);
            }
        }
        return Mono.just(RestUtils.success());
    }
}
