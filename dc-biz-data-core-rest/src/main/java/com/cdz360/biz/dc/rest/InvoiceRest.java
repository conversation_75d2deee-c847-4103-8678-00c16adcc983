package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.domain.InvoiceCallbackMsg;
import com.cdz360.biz.dc.service.InvoiceService;
import com.cdz360.biz.model.oa.param.PrepaidInvoicingEditParam;
import com.cdz360.biz.model.oa.vo.InvoicingContentVo;
import com.cdz360.biz.model.oa.vo.OaInvoicedVo;
import com.cdz360.biz.model.trading.invoice.dto.CorpInvoiceRecordDto;
import com.cdz360.biz.model.trading.invoice.dto.InvoiceRecordOrderRefDto;
import com.cdz360.biz.model.trading.invoice.dto.InvoicedRecordDto;
import com.cdz360.biz.model.trading.invoice.param.ListCorpInvoiceRecordParam;
import com.cdz360.biz.model.trading.invoice.param.ListInvoiceRecordOrderRefParam;
import com.cdz360.biz.model.trading.invoice.param.UserInvoiceRecordParam;
import com.cdz360.biz.model.trading.invoice.po.CorpInvoiceRecordPo;
import com.cdz360.biz.model.trading.invoice.po.InvoiceRecordOrderRefPo;
import com.cdz360.biz.model.trading.invoice.vo.CorpInvoiceRecordVo;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.peek.invoice.dto.PeekInvoiceDto;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoiceApplyResultOaDTO;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoiceCallBackDto;
import com.chargerlinkcar.framework.common.domain.invoice.dto.UpdateIdDTO;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordAuditParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordManualParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordUpdateParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.UserInvoiceRecordAuditParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceRecordDetail;
import com.chargerlinkcar.framework.common.domain.peek.invoice.param.PeekInvoiceParam;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderSite;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderBiVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderTimeShareBiVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * 为发票系统系统的接口
 */
@Slf4j
@RestController
@Tag(name = "为发票系统系统的接口", description = "invoice")
public class InvoiceRest {

    @Autowired
    private InvoiceService invoiceService;

    @Operation(summary = "手动触发修正开票数据", description = "仅用在异常处理情况")
    @GetMapping(value = "/dataCore/invoice/manualCorrectInvoiceData")
    public Mono<BaseResponse> manualCorrectInvoiceData(ServerHttpRequest request,
        @RequestParam(value = "accountType") PayAccountType accountType,
        @RequestParam(value = "recordId", required = false) Long recordId,
        @RequestParam(value = "applyNo", required = false) String applyNo) {
        log.info("manualCorrect: {}, accountType = {}, recordId = {}, applyNo = {}",
            LoggerHelper2.formatEnterLog(request, false), accountType, recordId, applyNo);
        invoiceService.manualCorrectInvoiceData(accountType, recordId, applyNo);
        return Mono.just(RestUtils.success());
    }

    @Deprecated(since = "********")
    @Operation(summary = "手动触发处理发票反馈数据", description = "仅用在异常处理情况")
    @GetMapping(value = "/dataCore/invoice/manualNotifyEvent")
    public Mono<BaseResponse> manualNotifyEvent(ServerHttpRequest request) {
        invoiceService.manualNotifyEvent();
        return Mono.just(RestUtils.success());
    }

    @Deprecated(since = "********")
    @Operation(summary = "手动补推数据", description = "仅用在异常处理情况")
    @PostMapping("/dataCore/invoice/manualCallback")
    public Mono<BaseResponse> manualCallback(ServerHttpRequest request,
                                             @RequestBody InvoiceCallbackMsg msg) {
        invoiceService.manualCallback(msg);
        return Mono.just(RestUtils.success());
    }

    @Operation(summary = "发票回调(发票服务调用)")
    @PostMapping("/dataCore/invoice/invoiceCallback")
    public ObjectResponse<Integer> invoiceCallback(
            ServerHttpRequest request, @RequestBody InvoiceCallBackDto dto) {
        log.info("发票回调(发票服务调用): {}, dto = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(dto));

        if (StringUtils.isNotBlank(dto.getApplyNo())) {
            return RestUtils.buildObjectResponse(invoiceService.corpInvoiceCallback(dto));
        }

        return RestUtils.buildObjectResponse(invoiceService.invoiceCallback(dto));
    }

    @Operation(summary = "发票申请结果返回成功时处理")
    @PostMapping("/dataCore/invoice/updateInvoiceAmount")
    public BaseResponse updateInvoiceAmount(
            ServerHttpRequest request, @RequestBody InvoiceCallBackDto dto) {
        log.info("发票申请结果返回: {}, dto = {}",
                LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(dto));
        invoiceService.updateInvoiceAmount(dto);
        return RestUtils.success();
    }

    @Operation(summary = "获取企业开票列表")
    @PostMapping(value = "/dataCore/invoice/findCorpInvoiceRecordList")
    public ListResponse<CorpInvoiceRecordDto> findCorpInvoiceRecordList(
            ServerHttpRequest request, @RequestBody ListCorpInvoiceRecordParam param) {
        log.debug("获取企业开票列表: {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return invoiceService.findCorpInvoiceRecordList(param);
    }

    @Operation(summary = "获取企业开票记录")
    @GetMapping(value = "/dataCore/invoice/getRecordByProcInstId")
    public ObjectResponse<CorpInvoiceRecordDto> getRecordByProcInstId(
        ServerHttpRequest request, @RequestParam(value = "procInstId") String procInstId) {
        log.debug("获取企业开票记录: {}", LoggerHelper2.formatEnterLog(request));
        return RestUtils.buildObjectResponse(invoiceService.getRecordByProcInstId(procInstId));
    }

    @Operation(summary = "企业客户开票追加订单(充值/充电/账单)")
    @PostMapping(value = "/dataCore/invoice/corpInvoiceAppendOrder")
    public ObjectResponse<CorpInvoiceRecordVo> corpInvoiceAppendOrder(
            ServerHttpRequest request, @RequestBody CorpInvoiceRecordUpdateParam param) {
        log.debug("企业客户开票追加订单(充值/充电/账单): {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return RestUtils.buildObjectResponse(invoiceService.corpInvoiceAppendOrder(param));
    }

    @Operation(summary = "预付订单开票流程提交审核(非企业扣款账户订单)")
    @PostMapping(value = "/dataCore/invoice/prepaidInvoiceSubmit2Audit")
    public ObjectResponse<Long> prepaidInvoiceSubmit2Audit(
        ServerHttpRequest request, @RequestBody PrepaidInvoicingEditParam param) {
        log.debug("预付订单开票流程提交审核. prepaidInvoiceSubmit2Audit: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), param);
        return invoiceService.prepaidInvoiceSubmit2Audit(param);
    }

    @Operation(summary = "预付订单开票流程放弃申请(非企业扣款账户订单)")
    @PostMapping(value = "/dataCore/invoice/abandonPrepaidInvoice")
    public BaseResponse abandonPrepaidInvoice(
        ServerHttpRequest request, @RequestParam("procInstId") String procInstId) {
        log.debug("abandonPrepaidInvoice: {}, procInstId = {}",
            LoggerHelper2.formatEnterLog(request, false), procInstId);
        return invoiceService.abandonPrepaidInvoice(procInstId);
    }

    @Operation(summary = "预计算，客户开票追加订单(充值/充电/账单)")
    @PostMapping(value = "/dataCore/invoice/peekInvoice")
    public ObjectResponse<PeekInvoiceDto> peekInvoice(
            ServerHttpRequest request, @RequestBody PeekInvoiceParam param) {
        log.debug("预计算，客户开票追加订单(充值/充电/账单): {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return RestUtils.buildObjectResponse(invoiceService.peekInvoice(param));
    }

    @Operation(summary = "企业客户开票移除订单(充值/充电/账单)")
    @PostMapping(value = "/dataCore/invoice/corpInvoiceRemoveOrder")
    public ObjectResponse<CorpInvoiceRecordVo> corpInvoiceRemoveOrder(
            ServerHttpRequest request, @RequestBody CorpInvoiceRecordUpdateParam param) {
        log.debug("企业客户开票移除订单(充值/充电/账单): {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return RestUtils.buildObjectResponse(invoiceService.corpInvoiceRemoveOrder(param));
    }

    @Operation(summary = "企业开票记录删除")
    @GetMapping(value = "/dataCore/invoice/deleteCorpInvoiceRecordByApplyNo")
    public ObjectResponse<Integer> deleteCorpInvoiceRecordByApplyNo(
            ServerHttpRequest request, @Parameter(name = "申请单号") @RequestParam(value = "applyNo") String applyNo) {
        log.debug("企业开票记录删除: {}", LoggerHelper2.formatEnterLog(request));
        return RestUtils.buildObjectResponse(invoiceService.deleteCorpInvoiceRecordByApplyNo(applyNo));
    }

    @Operation(summary = "企业开票记录删除(用于企客开票流程调用)")
    @GetMapping(value = "/dataCore/invoice/deleteCorpInvoiceRecordByOa")
    public ObjectResponse<Integer> deleteCorpInvoiceRecordByOa(ServerHttpRequest request,
        @Parameter(name = "流程实例ID") @RequestParam(value = "procInstId") String procInstId,
        @Parameter(name = "是否物理删除") @RequestParam(value = "physicalDeletion") Boolean physicalDeletion) {
        log.debug("企业开票记录删除: {}", LoggerHelper2.formatEnterLog(request));
        return RestUtils.buildObjectResponse(
            invoiceService.deleteCorpInvoiceRecordByOa(procInstId, physicalDeletion));
    }

    @Operation(summary = "企业开票记录修改回款状态")
    @PostMapping(value = "/dataCore/invoice/updateCorpInvoiceRecordReturnFlag")
    public ObjectResponse<Integer> updateCorpInvoiceRecordReturnFlag(
            ServerHttpRequest request, @RequestBody CorpInvoiceRecordPo po) {
        log.debug("企业开票记录修改回款状态: {}", LoggerHelper2.formatEnterLog(request));
        return RestUtils.buildObjectResponse(invoiceService.updateCorpInvoiceRecordReturnFlag(po));
    }

    @Operation(summary = "企业开票记录详情")
    @GetMapping(value = "/dataCore/invoice/corpInvoiceRecordDetail")
    public ObjectResponse<CorpInvoiceRecordDetail> corpInvoiceRecordDetail(
            ServerHttpRequest request, @RequestParam(value = "applyNo") String applyNo) {
        log.debug("企业开票记录详情: {}", LoggerHelper2.formatEnterLog(request));
        return RestUtils.buildObjectResponse(invoiceService.corpInvoiceRecordDetail(applyNo));
    }

    @Operation(summary = "企业客户开票记录提交到审核(非财务)")
    @PostMapping(value = "/dataCore/invoice/corpInvoiceRecordSubmit2Audit")
    public ObjectResponse<Integer> corpInvoiceRecordSubmit2Audit(
            ServerHttpRequest request, @RequestBody CorpInvoiceRecordDto dto) {
        log.debug("企业客户开票记录提交到审核(非财务): {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(dto));
        return RestUtils.buildObjectResponse(invoiceService.corpInvoiceRecordSubmit2Audit(dto));
    }

    /**
     * 个人商户会员开票记录提交到审核
     * @param request
     * @param dto
     * @return
     */
    @Operation(summary = "个人&商户会员开票记录提交到审核")
    @PostMapping(value = "/dataCore/invoice/userInvoiceRecordSubmit2Audit")
    public ListResponse<InvoicedRecordDto> userInvoiceRecordSubmit2Audit(
            ServerHttpRequest request, @RequestBody UserInvoiceRecordParam dto) {
        log.debug("个人&商户会员开票记录提交到审核: {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(dto));
        return RestUtils.buildListResponse(invoiceService.userInvoiceRecordSubmit2Audit(dto));
    }

    /**
     * 入参不能同时为null
     * @param request
     * @param recordId
     * @param procInstId 优先级高
     * @return
     */
    @Operation(summary = "个人&商户会员&企业客户开票记录提交到审核-审核驳回")
    @PostMapping(value = "/dataCore/invoice/userInvoiceRecordDeny")
    public ObjectResponse<Integer> userInvoiceRecordDeny(ServerHttpRequest request,
        @RequestParam(value = "recordId", required = false) Long recordId,
        @RequestParam(value = "procInstId", required = false) String procInstId) {
        log.debug("个人&商户会员&企业客户开票记录提交到审核-审核驳回: {}, recordId = {}, procInstId = {}",
                LoggerHelper2.formatEnterLog(request, false), recordId, procInstId);
        return RestUtils.buildObjectResponse(
            invoiceService.userInvoiceRecordDeny(recordId, procInstId));
    }

    @Operation(summary = "充值开票，个人&商户会员，结束申请")
    @PostMapping(value = "/dataCore/invoice/userInvoiceRecordAbandon")
    public ObjectResponse<Integer> userInvoiceRecordAbandon(ServerHttpRequest request,
        @RequestParam(value = "recordId", required = false) Long recordId,
        @RequestParam(value = "procInstId", required = false) String procInstId) {
        log.debug("充值开票，个人&商户会员，结束申请: {}, recordId = {}, procInstId = {}",
                LoggerHelper2.formatEnterLog(request, false), recordId, procInstId);
        return RestUtils.buildObjectResponse(
            invoiceService.userInvoiceRecordAbandon(recordId, procInstId));
    }

    @Operation(summary = "企业手动开票")
    @PostMapping(value = "/dataCore/invoice/corpInvoiceRecordManual")
    public Mono<ObjectResponse<Boolean>> corpInvoiceRecordManual(
            ServerHttpRequest request, @RequestBody CorpInvoiceRecordManualParam param) {
        log.debug("企业手动开票: {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return invoiceService.corpInvoiceRecordManual(param)
                .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "OA流程结束后，将开票记录状态变为‘已开具’")
    @GetMapping(value = "/dataCore/invoice/endByOa")
    public BaseResponse endCorpInvoiceRecordByOa(ServerHttpRequest request,
        @RequestParam(value = "procInstId") String procInstId) {
        log.info("endCorpInvoiceRecordByOa: {}, procInstId = {}",
            LoggerHelper2.formatEnterLog(request, false), procInstId);
        return invoiceService.endCorpInvoiceRecordByOa(procInstId);
    }


    @Operation(summary = "企业开票审核(财务)")
    @PostMapping(value = "/dataCore/invoice/corpInvoiceRecordAudit")
    public Mono<ObjectResponse<Integer>> corpInvoiceRecordAudit(
            ServerHttpRequest request, @RequestBody CorpInvoiceRecordAuditParam param) {
        log.debug("企业开票审核: {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return Mono.just(invoiceService.corpInvoiceRecordAudit(param))
                .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "企业开票审核(财务, 用于企客对账开票流程调用)")
    @PostMapping(value = "/dataCore/invoice/corpInvoiceRecordAuditByOa")
    public Mono<ObjectResponse<InvoiceApplyResultOaDTO>> corpInvoiceRecordAuditByOa(
            ServerHttpRequest request, @RequestBody CorpInvoiceRecordAuditParam param) {
        log.debug("企业开票审核: {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return invoiceService.corpInvoiceRecordAuditByOa(param);
    }

//    @Operation(summary = "设置企业开票内容")
//    @PostMapping(value = "/dataCore/invoice/setCorpInvoicingContent")
//    public Mono<ListResponse<InvoicingContentVo>> setCorpInvoicingContent(
//            ServerHttpRequest request,
//        @Parameter(name = "需要查询的订单号", required = true)
//        @RequestParam(value = "invoiceId") Long invoiceId,
//        @Parameter(name = "需要查询的订单号", required = true)
//        @RequestParam(value = "procInstId") String procInstId) {
//        log.debug("设置企业开票内容invoiced_record.id: {}, {}", invoiceId, procInstId);
//        return invoiceService.setCorpInvoicingContent(invoiceId, procInstId);
//    }

    @Operation(summary = "个人&商户会员开票审核(财务, 用于个人&商户会员开票流程调用)")
    @PostMapping(value = "/dataCore/invoice/userInvoiceRecordAuditByOa")
    public Mono<ObjectResponse<InvoiceApplyResultOaDTO>> userInvoiceRecordAuditByOa(
            ServerHttpRequest request, @RequestBody UserInvoiceRecordAuditParam param) {
        log.debug("个人&商户会员开票审核: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return invoiceService.userInvoiceRecordAuditByOa(param);
    }

    @Operation(summary = "获取企业开票记录关联的订单列表")
    @PostMapping(value = "/dataCore/invoice/getInvoiceRecordOrderList")
    public ListResponse<InvoiceRecordOrderRefDto> getInvoiceRecordOrderList(
            ServerHttpRequest request, @RequestBody ListInvoiceRecordOrderRefParam param) {
        log.info("获取企业开票记录关联的订单列表: {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return invoiceService.getInvoiceRecordOrderList(param);
    }

    @Operation(summary = "通过OA获取企业开票记录关联的订单列表")
    @GetMapping(value = "/dataCore/invoice/getInvoiceRecordOrderListByOa")
    public ListResponse<InvoiceRecordOrderRefPo> getInvoiceRecordOrderListByOa(
            ServerHttpRequest request, @RequestParam(value = "procInstId") String procInstId) {
        log.info("获取企业开票记录关联的订单列表: {}, procInstId = {}",
                LoggerHelper2.formatEnterLog(request, false), procInstId);
        return invoiceService.findListByOa(procInstId);
    }

    @Operation(summary = "获取企业开票记录关联的订单按照场站汇总")
    @PostMapping(value = "/dataCore/invoice/recordOrderGroupBySite")
    public ListResponse<ChargerOrderSite> recordOrderGroupBySite(
            ServerHttpRequest request, @RequestBody ListInvoiceRecordOrderRefParam param) {
        log.info("获取企业开票记录关联的订单按照场站汇总: {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return invoiceService.recordOrderGroupBySite(param);
    }

    @Operation(summary = "获取企业开票记录关联的订单 金额数据")
    @PostMapping(value = "/dataCore/invoice/recordOrderGroupByTimeShareFee")
    public ListResponse<OrderTimeShareBiVo> recordOrderGroupByTimeShareFee(
            ServerHttpRequest request, @RequestBody ListInvoiceRecordOrderRefParam param) {
        log.info("获取企业开票记录关联的订单按照场站汇总: {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return invoiceService.recordOrderGroupByTimeShareFee(param);
    }

    @Operation(summary = "更新企业开票的开票抬头信息")
    @PostMapping(value = "/dataCore/invoice/updateCorpInvoiceRecordModelId")
    public ObjectResponse<Integer> updateCorpInvoiceRecordModelId(
            ServerHttpRequest request, @RequestBody UpdateIdDTO data) {
        log.info("更新企业开票的开票抬头信息: {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(data));
        return RestUtils.buildObjectResponse(invoiceService.updateCorpInvoiceRecordModelId(data));
    }

    @Operation(summary = "更新企业开票的开票模板信息")
    @PostMapping(value = "/dataCore/invoice/updateCorpInvoiceRecordProTempId")
    public Mono<ObjectResponse<Integer>> updateCorpInvoiceRecordProTempId(
            ServerHttpRequest request, @RequestBody List<UpdateIdDTO> updateIdDTOList) {
        log.info("更新企业开票的开票模板信息: {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(updateIdDTOList));
        return invoiceService.updateCorpInvoiceRecordProTempId(updateIdDTOList)
                .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "修改企业开票记录")
    @PostMapping(value = "/dataCore/invoice/updateCorpInvoiceRecord")
    public Mono<ObjectResponse<Integer>> updateCorpInvoiceRecord(
            ServerHttpRequest request, @RequestBody CorpInvoiceRecordPo invoiceRecordPo) {
        log.info("修改企业开票记录: {}, invoiceRecordPo = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(invoiceRecordPo));
        return invoiceService.updateCorpInvoiceRecord(invoiceRecordPo)
                .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "修改个人&商户会员开票记录")
    @PostMapping(value = "/dataCore/invoice/updateUserInvoiceRecord")
    public Mono<ObjectResponse<Integer>> updateUserInvoiceRecord(
            ServerHttpRequest request, @RequestBody InvoicedRecordDto invoicedRecordDto) {
        log.info("修改个人&商户会员开票记录: {}, invoicedRecordDto = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(invoicedRecordDto));
        return invoiceService.updateUserInvoiceRecord(invoicedRecordDto)
                .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "企业客户相关订单是否被占用")
    @PostMapping(value = "/dataCore/invoice/invoiceUsed")
    public Mono<ObjectResponse<Boolean>> invoiceUsed(
            ServerHttpRequest request,
            @Parameter(name = "需要查询的订单开票方式", required = true) @RequestParam(value = "invoiceWay") InvoicingMode invoiceWay,
            @Parameter(name = "需要查询的订单号", required = true) @RequestParam(value = "orderNo") String orderNo) {
        log.info("企业客户相关订单是否被占用: {}", LoggerHelper2.formatEnterLog(request));
        return invoiceService.invoiceUsed(invoiceWay, orderNo)
                .doOnNext(res -> log.debug("orderNo = {}, res = {}", orderNo, res))
                .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "获取已申请企业开票的充电订单列表")
    @PostMapping(value = "/dataCore/invoice/includeChargerOrderList")
    public Mono<ListResponse<ChargerOrderVo>> includeChargerOrderList(
            ServerHttpRequest request, @RequestBody ListChargeOrderParam param) {
        log.debug("获取已申请企业开票的充电订单列表: {}, param = {}",
                LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        return invoiceService.includeChargerOrderList(param);
    }

    @Operation(summary = "获取已申请企业开票的充电订单汇总信息")
    @PostMapping(value = "/dataCore/invoice/includeChargerOrderBi")
    public Mono<ObjectResponse<OrderBiVo>> includeChargerOrderBi(
            ServerHttpRequest request, @RequestBody ListChargeOrderParam param) {
        log.debug("获取已申请企业开票的充电订单汇总信息: {}, param = {}",
                LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        return invoiceService.includeChargerOrderBi(param)
                .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "获取预付订单流程页面需展示的开票信息")
    @GetMapping(value = "/dataCore/invoice/getInvoiceVo4PrepaidProcess")
    public Mono<ObjectResponse<OaInvoicedVo>> getInvoiceVo4PrepaidProcess(ServerHttpRequest request,
        @RequestParam(value = "procInstId") String procInstId) {
        log.debug("getInvoiceVo4PrepaidProcess: {}, procInstId = {}",
            LoggerHelper2.formatEnterLog(request), procInstId);
        return invoiceService.getInvoiceVo4PrepaidProcess(procInstId);
    }

}
