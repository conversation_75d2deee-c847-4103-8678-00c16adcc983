package com.cdz360.biz.dc.client.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.download.dto.DownloadJobDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

import java.util.function.Function;

@Slf4j
@Component
public class BiDownloadJobFeignHystrix
    implements FallbackFactory<BiDownloadJobFeignClient> {

    @Override
    public BiDownloadJobFeignClient apply(Throwable throwable) {
        return new BiDownloadJobFeignClient() {
            @Override
            public Mono<BaseResponse> generateFile(DownloadJobDto downloadJobDto) {
                log.error("【服务熔断】: Service = {}, api = generateFile (文件生成), dto = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_BI, JsonUtils.toJsonString(downloadJobDto));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> generatePrintFile(DownloadJobDto downloadJobDto) {
                log.error("【服务熔断】: Service = {}, api = generatePrintFile (打印文件生成), dto = {}",
                        DcConstants.KEY_FEIGN_DC_BIZ_BI, JsonUtils.toJsonString(downloadJobDto));
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> clearDownloadFile(Integer days) {
                log.error(
                    "【服务熔断】。Service = {}, api = clearDownloadFile(生成文件清理), days = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_BI, days);
                return Mono.just(RestUtils.serverBusy());
            }
        };
    }

    @Override
    public <V> Function<V, BiDownloadJobFeignClient> compose(
        Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_BI);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(
        Function<? super BiDownloadJobFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_BI);
        return null;
    }
}
