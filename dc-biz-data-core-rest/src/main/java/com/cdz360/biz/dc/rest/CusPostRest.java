package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.CusPostService;
import com.cdz360.biz.model.cus.type.CusPostStatus;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@Tag(name = "用户评论涉及接口", description = "用户评论涉及接口")
@RestController
public class CusPostRest {
    @Autowired
    private CusPostService cusPostService;

    @Operation(summary = "变更充电订单中评论状态")
    @GetMapping(value = "/dataCore/post/updateOrderCusPostStatus")
    public Mono<BaseResponse> updateOrderCusPostStatus(
            ServerHttpRequest request,
            @Parameter(name = "充电订单号", required = true) @RequestParam("orderNo") String orderNo,
            @Parameter(name = "调整后的状态", required = true) @RequestParam("cusPostStatus")CusPostStatus status) {
        log.info("变更充电订单中评论状态: {}", LoggerHelper2.formatEnterLog(request));
        return cusPostService.updateOrderCusPostStatus(orderNo, status)
                .map(i -> RestUtils.success());
    }
}
