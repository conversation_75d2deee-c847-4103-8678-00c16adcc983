package com.cdz360.biz.dc.rest.site;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.rover.RoverService;
import com.cdz360.biz.model.trading.rover.dto.RoverMixDto;
import com.cdz360.biz.model.trading.rover.param.RoverSearchParam;
import com.cdz360.biz.model.trading.rover.param.SiteListRoverCycleParam;
import com.cdz360.biz.model.trading.rover.po.SiteRoverCfgPo;
import com.cdz360.biz.model.trading.rover.po.SiteRoverPo;
import com.cdz360.biz.model.trading.rover.vo.SiteRoverVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * SiteRoverRest
 *
 * @since 7/27/2022 1:54 PM
 * <AUTHOR>
 */
@Tag(name = "运营巡查相关操作接口", description = "运营巡查相关操作接口")
@Slf4j
@RestController
public class SiteRoverRest {

    @Autowired
    private RoverService roverService;

    @Operation(summary = "创建运营巡查")
    @PostMapping(value = "/dataCore/siteRover/createRover")
    public Mono<ObjectResponse<RoverMixDto>> createRover(@RequestBody RoverMixDto param) {
        log.info("创建运营巡查: {}", JsonUtils.toJsonString(param));
        return roverService.createRover(param)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "修改运营巡查")
    @PostMapping(value = "/dataCore/siteRover/updateRover")
    public Mono<ObjectResponse<RoverMixDto>> updateRover(@RequestBody RoverMixDto param) {
        log.info("修改运营巡查: {}", JsonUtils.toJsonString(param));
        return roverService.updateRover(param)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "查询运营巡查")
    @PostMapping(value = "/dataCore/siteRover/searchRoverList")
    public Mono<ListResponse<RoverMixDto>> searchRoverList(@RequestBody RoverSearchParam param) {
        log.info("查询运营巡查: {}", JsonUtils.toJsonString(param));
        return roverService.searchPrerunList(param);
    }


    @PostMapping(value = "/dataCore/siteRover/getSiteRoverCfg")
    public Mono<ObjectResponse<SiteRoverCfgPo>> getSiteRoverCfg(
        @RequestParam(value = "siteId") String siteId) {
        log.info("获取运营巡查配置: {}", siteId);
        return Mono.just(new ObjectResponse<>(roverService.getSiteRoverCfg(siteId)));
    }

    @PostMapping(value = "/dataCore/siteRover/setSiteRoverCfg")
    public Mono<ObjectResponse<SiteRoverCfgPo>> setSiteRoverCfg(@RequestBody SiteRoverCfgPo param) {
        log.info("设置运营巡查配置: {}", JsonUtils.toJsonString(param));
        return Mono.just(new ObjectResponse<>(roverService.setSiteRoverCfg(param)));
    }

    @PostMapping(value = "/dataCore/siteRover/cancelRover")
    public Mono<ObjectResponse<Boolean>> cancelRover(
        @RequestParam(value = "roverId") Long roverId,
        @RequestParam(value = "cancellerUid") Long cancellerUid,
        @RequestParam(value = "cancellerName") String cancellerName) {
        log.info("取消运营巡查: {}, {}, {}", roverId, cancellerUid, cancellerName);
        return Mono.just(new ObjectResponse<>(roverService.cancelRover(
            roverId,
            cancellerUid,
            cancellerName)));
    }

    @PostMapping(value = "/dataCore/siteRover/deleteRover")
    public Mono<ObjectResponse<Boolean>> deleteRover(
        @RequestParam(value = "roverId") Long roverId) {
        log.info("删除运营巡查: {}", roverId);
        return Mono.just(new ObjectResponse<>(roverService.deleteRover(roverId)));
    }

    @PostMapping(value = "/dataCore/siteRover/rankRover")
    public Mono<ObjectResponse<SiteRoverPo>> rankRover(@RequestBody SiteRoverPo param) {
        log.info("打分运营巡查: {}", JsonUtils.toJsonString(param));
        return roverService.rankRover(param)
            .map(RestUtils::buildObjectResponse);
    }

    @PostMapping(value = "/dataCore/siteRover/getSiteRoverCycleList")
    public Mono<ListResponse<SiteRoverVo>> getSiteRoverCycleList(
        @RequestBody SiteListRoverCycleParam param) {
        log.info("待巡检场站列表: {}", JsonUtils.toJsonString(param));
        return roverService.getSiteRoverCycleList(param)
            .map(RestUtils::buildListResponse);
    }

    @GetMapping(value = "/dataCore/siteRover/getSiteRoverLatest")
    public Mono<ObjectResponse<SiteRoverVo>> getSiteRoverLatest(
        @RequestParam("siteId") String siteId) {
        log.info("最近一次场站巡查信息: {}", siteId);
        return roverService.getSiteRoverLatest(siteId)
            .map(RestUtils::buildObjectResponse);
    }

    @GetMapping(value = "/dataCore/siteRover/clearInitRover")
    public Mono<ObjectResponse<Integer>> clearInitRover(
        @RequestParam(value = "expireHour", required = false, defaultValue = "24") Integer expireHour) {
        log.info("清空超出{}小时的待处理订单", expireHour);
        return roverService.clearInitRover(expireHour)
            .map(RestUtils::buildObjectResponse);
    }

    /**
     * 桩管家场站巡查提醒
     *
     * @param beforeDay 提前几天通知
     * @return
     */
    @Operation(summary = "桩管家场站巡检提醒")
    @GetMapping(value = "/dataCore/siteRover/siteRoverNotify")
    public Mono<ObjectResponse<Integer>> siteRoverNotify(
        @RequestParam(value = "beforeDay", defaultValue = "3") Integer beforeDay) {
        log.info("桩管家场站巡检提醒: beforeDay = {}", beforeDay);
        return roverService.siteRoverNotify(beforeDay);
    }

    @Operation(summary = "运营巡查图片数据修正(旧数据数据结构调整)")
    @GetMapping(value = "/dataCore/siteRover/fixImages")
    public Mono<BaseResponse> roverOrderFixImages() {
        log.info("运营巡查图片数据修正");
        return roverService.roverOrderFixImages();
    }
}