package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.dc.service.FileService;
import com.cdz360.biz.model.trading.file.po.OssFilePo;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@Tag(name = "文件管理接口", description = "文件管理")
@RequestMapping("/dataCore/file")
public class FileRest {
    @Autowired
    private FileService fileService;

    /**
     * 添加文件信息
     * @param param
     * @return
     */
    @PostMapping("/addFile")
    public ObjectResponse<OssFilePo> addFile(@RequestBody OssFilePo param) {
        log.debug("param = {}", JsonUtils.toJsonString(param));
        return  new ObjectResponse<>(fileService.addFile(param));
    }

}
