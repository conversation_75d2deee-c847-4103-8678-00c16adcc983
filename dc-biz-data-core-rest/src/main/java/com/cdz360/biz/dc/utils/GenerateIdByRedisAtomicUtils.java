package com.cdz360.biz.dc.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.support.atomic.RedisAtomicLong;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * RedisAtomicLong产生流水号
 *
 * <AUTHOR>
 * Create on 2018/09/10 16:37
 */
@Component
public class GenerateIdByRedisAtomicUtils {
    private static final String DEFAULT_SEQUENCE_KEY = "WARNING_RECORD";
    private static StringRedisTemplate stringRedisTemplate;

    @Autowired
    private StringRedisTemplate autowiredStringRedisTemplate;

    public static Long generateLongKey(String key, int increment) {
        RedisAtomicLong counter = new RedisAtomicLong(key, stringRedisTemplate.getConnectionFactory());
        return counter.addAndGet(increment);
    }

    public static Long generateLongKey(String key) {
        return generateLongKey(key, 1);
    }

    public static Long generateLongKey() {
        return generateLongKey(DEFAULT_SEQUENCE_KEY);
    }

    @PostConstruct
    public void initRedisTemplate() {
        stringRedisTemplate = autowiredStringRedisTemplate;
    }
}