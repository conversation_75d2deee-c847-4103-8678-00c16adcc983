package com.cdz360.biz.dc.client.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.chargerlinkcar.framework.common.domain.vo.CommCusRef;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

import java.util.function.Function;

/**
 * ReactorUserFeignClientHystrixFactory
 *
 * @since 11/3/2020 10:32 AM
 * <AUTHOR>
 */
@Slf4j
@Component
public class ReactorUserFeignClientHystrixFactory implements FallbackFactory<ReactorUserFeignClient> {
    @Override
    public ReactorUserFeignClient apply(Throwable throwable) {
        log.error("{}", throwable);
        return new ReactorUserFeignClient() {
            @Override
            public Mono<ObjectResponse<Boolean>> userMoveCorp(Long corpId, Long commId) {
                log.error("【服务熔断】: Service = {}, corpId = {}, commId = {})",
                        DcConstants.KEY_FEIGN_DC_BIZ_USER, corpId, commId);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<ObjectResponse<CommCusRef>> findById(Long id) {
                log.error("【服务熔断】: Service = {}, id = {})",
                        DcConstants.KEY_FEIGN_DC_BIZ_USER, id);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }
        };
    }

    @Override
    public <V> Function<V, ReactorUserFeignClient> compose(Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(Function<? super ReactorUserFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER);
        return null;
    }
}