package com.cdz360.biz.dc.rest.abc;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.abc.AbcCouponService;
import com.cdz360.biz.model.trading.coupon.dto.AbcCouponDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "农行优惠券相关操作接口", description = "农行优惠券相关操作接口")
@Slf4j
@RestController
@RequestMapping("/dataCore/abc/coupon")
public class AbcCouponRest {

    @Autowired
    private AbcCouponService abcCouponService;

    @Operation(summary = "用户当前是否已经使用优惠券")
    @PostMapping(value = "/todayUsed")
    public Mono<ObjectResponse<AbcCouponDto>> userTodayUsedAbcCoupon(
        @RequestBody AbcCouponDto dto) {
        log.info("用户当前是否已经使用优惠券: param = {}", JsonUtils.toJsonString(dto));
        return abcCouponService.userTodayUsedAbcCoupon(dto);
    }

    @Operation(summary = "新建或编辑农行优惠券")
    @PostMapping(value = "/save")
    public Mono<ObjectResponse<AbcCouponDto>> saveAbcCoupon(@RequestBody AbcCouponDto dto) {
        log.info("新建或编辑农行优惠券: param = {}", JsonUtils.toJsonString(dto));
        return abcCouponService.saveAbcCoupon(dto)
            .map(RestUtils::buildObjectResponse);
    }

}
