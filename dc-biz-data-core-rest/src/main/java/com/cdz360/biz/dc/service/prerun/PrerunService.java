package com.cdz360.biz.dc.service.prerun;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.service.DeviceBizService;
import com.cdz360.biz.dc.service.siteGroup.SiteGroupService;
import com.cdz360.biz.ds.trading.ro.prerun.ds.PrerunAssetImgRoDs;
import com.cdz360.biz.ds.trading.ro.prerun.ds.PrerunCheckRoDs;
import com.cdz360.biz.ds.trading.ro.prerun.ds.PrerunEvseRoDs;
import com.cdz360.biz.ds.trading.ro.prerun.ds.PrerunRoDs;
import com.cdz360.biz.ds.trading.rw.prerun.ds.PrerunAssetImgRwDs;
import com.cdz360.biz.ds.trading.rw.prerun.ds.PrerunCheckRwDs;
import com.cdz360.biz.ds.trading.rw.prerun.ds.PrerunEvseRwDs;
import com.cdz360.biz.ds.trading.rw.prerun.ds.PrerunRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteRwDs;
import com.cdz360.biz.model.iot.param.ModifyEvseInfoParam;
import com.cdz360.biz.model.trading.iot.po.BsBoxPo;
import com.cdz360.biz.model.trading.prerun.dto.PrerunMixDto;
import com.cdz360.biz.model.trading.prerun.param.PrerunCheckParam;
import com.cdz360.biz.model.trading.prerun.param.PrerunSearchParam;
import com.cdz360.biz.model.trading.prerun.po.PrerunCheckPo;
import com.cdz360.biz.model.trading.prerun.po.PrerunEvsePo;
import com.cdz360.biz.model.trading.prerun.po.PrerunPo;
import com.cdz360.biz.model.trading.prerun.type.PrerunStatusType;
import com.cdz360.biz.model.trading.prerun.vo.PrerunVo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.chargerlinkcar.framework.common.feign.IotBizClient;
import com.chargerlinkcar.framework.common.service.RedisNoGen;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

/**
 * PrerunService
 *  开通调试
 * @since 6/22/2022 8:35 AM
 * <AUTHOR>
 */
@Slf4j
@Service
public class PrerunService {
    @Autowired
    private RedisNoGen redisNoGen;
    @Autowired
    private PrerunRoDs prerunRoDs;
    @Autowired
    private PrerunRwDs prerunRwDs;

    @Autowired
    private PrerunAssetImgRoDs prerunAssetImgRoDs;
    @Autowired
    private PrerunAssetImgRwDs prerunAssetImgRwDs;

    @Autowired
    private PrerunCheckRoDs prerunCheckRoDs;
    @Autowired
    private PrerunCheckRwDs prerunCheckRwDs;

    @Autowired
    private PrerunEvseRoDs prerunEvseRoDs;
    @Autowired
    private PrerunEvseRwDs prerunEvseRwDs;

    @Autowired
    private SiteRwDs siteRwDs;
    @Autowired
    private SiteGroupService siteGroupService;

//    @Transactional
//    public Mono<PrerunMixDto> openPrerun(PrerunMixDto param) {
//        return Mono.empty();
//    }

    @Autowired
    private IotBizClient iotBizClient;

    @Autowired
    private DeviceBizService deviceBizService;

    @Transactional
    public Mono<PrerunMixDto> createPrerun(PrerunMixDto param) {
        return Mono.just(param)
            .doOnNext(e -> {
                IotAssert.isNotNull(param.getPrerun(), "请传入开通调试工单信息");
                final SitePo siteById = siteRwDs.getSiteById(param.getPrerun().getSiteId(), true);
                IotAssert.isNotNull(siteById, "找不到对应场站信息");

                PrerunSearchParam searchParam = new PrerunSearchParam();
                searchParam.setSiteId(param.getPrerun().getSiteId());
                searchParam.setEnable(true);
                searchParam.setSize(10);
                IotAssert.isTrue(CollectionUtils.isEmpty(prerunRoDs.searchPrerunList(searchParam)),
                "创建工单失败，场站当前已存在调试工单，请返回上一页后重试");

                String prerunNo = redisNoGen.prerunNo();
                log.info("new prerunNo: {}", prerunNo);
                param.getPrerun().setPrerunNo(prerunNo);
                prerunRwDs.insertPrerun(param.getPrerun());

                Optional.ofNullable(e.getPrerunEvsePoList()).ifPresentOrElse(opt -> {
                    opt.forEach(po -> {
                        po.setPrerunId(e.getPrerun().getId());
                        prerunEvseRwDs.insertPrerunEvse(po);
                    });

                    this.updateEvseListInfo(opt, param.getPrerun().getSiteId());
                }, () -> log.info("未传入场站设备信息"));
            })
            .doOnNext(e -> {
                Optional.ofNullable(e.getPrerunAssetImgPoList()).ifPresentOrElse(opt -> {
                    opt.forEach(po -> {
                        po.setPrerunId(e.getPrerun().getId());
                        prerunAssetImgRwDs.insertPrerunAssetImg(po);
                    });
                }, () -> log.info("未传入配套设施信息"));
            })
            .map(e -> e);
    }

    // 更新到桩信息到桩表
    private void updateEvseListInfo(List<PrerunEvsePo> opt, String siteId) {
        List<ModifyEvseInfoParam> modifyEvseInfoParams = opt.stream()
            .map(om -> {
                ModifyEvseInfoParam modifyEvseInfoParam = new ModifyEvseInfoParam();
                return modifyEvseInfoParam.setEvseNo(om.getEvseNo())
                    .setName(om.getEvseName())
                    .setExpireDate(om.getExpireDate())
                    .setProduceDate(om.getProduceDate())
                    .setFirmwareVer(om.getFirmwareVer())
                    .setModel(om.getModel())
                    .setPower(om.getPower())
                    .setSupplyType(om.getSupply())
                    .setSiteId(siteId)
                    .setPhysicalNo(om.getPhysicalNo())
                    .setModelId(om.getModelId())
                    .setModuleType(om.getModuleType())
                    .setPlugVoList(List.of());
            })
            .collect(Collectors.toList());
        final BaseResponse baseResponse = iotBizClient.updateEvseInfoList(modifyEvseInfoParams);
        FeignResponseValidate.check(baseResponse);

        List<BsBoxPo> bsBoxPos = opt.stream().map(om -> {
            BsBoxPo box = new BsBoxPo();
            return box.setEvseNo(om.getEvseNo())
                .setEvseName(om.getEvseName())//Y2020-2315
                .setSiteId(siteId)
                .setPower(om.getPower());
        }).collect(Collectors.toList());
        final boolean b = deviceBizService.updateBsBoxList(bsBoxPos);
        log.info("更新到桩信息到桩表: {}", b);
    }

    @Transactional
    public Mono<PrerunMixDto> updatePrerun(PrerunMixDto param) {
        return Mono.just(param)
            .doOnNext(e -> {
                IotAssert.isNotNull(param.getPrerun(), "请传入开通调试工单信息");
                IotAssert.isNotNull(param.getPrerun().getId(), "请传入开通调试工单信息id");
//                IotAssert.isNotBlank(param.getPrerun().getPrerunNo(), "请传入开通调试工单号");

                final PrerunPo byPrerunNo = prerunRwDs.getById(
                    param.getPrerun().getId(), true);

                prerunRwDs.updatePrerun(param.getPrerun());

                final PrerunVo ret = prerunRoDs.getById(param.getPrerun().getId());
                param.setPrerun(ret);

                Optional.ofNullable(e.getPrerunEvsePoList()).ifPresentOrElse(opt -> {
                    log.info("disable count evse: {}",
                        prerunEvseRwDs.disableAllByPrerunId(e.getPrerun().getId()));
                    opt.forEach(po -> {
                        po.setPrerunId(e.getPrerun().getId());
                        prerunEvseRwDs.insertPrerunEvse(po);
                    });

                    this.updateEvseListInfo(opt, param.getPrerun().getSiteId());
                    e.setPrerunEvsePoList(prerunEvseRoDs.getByPrerunId(e.getPrerun().getId()));
                }, () -> log.info("未传入场站设备信息"));
            })
            .doOnNext(e -> {
                Optional.ofNullable(e.getPrerunAssetImgPoList()).ifPresentOrElse(opt -> {
                    log.info("disable count evse: {}",
                        prerunAssetImgRwDs.disableAllByPrerunId(e.getPrerun().getId()));
                    opt.forEach(po -> {
                        po.setPrerunId(e.getPrerun().getId());
                        prerunAssetImgRwDs.insertPrerunAssetImg(po);
                    });
                    e.setPrerunAssetImgPoList(prerunAssetImgRoDs.getByPrerunId(e.getPrerun().getId()));
                }, () -> log.info("未传入配套设施信息"));
            }).map(e -> e);
    }

    @Transactional
    public Mono<PrerunVo> submitPrerun(PrerunCheckParam param) {
        return Mono.just(param)
            .map(e -> {
                IotAssert.isNotNull(param, "请传入开通调试工单信息");
                IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getPrerunIds()),
                    "请传入开通调试工单信息id");
                IotAssert.isNotNull(param.getStatus(), "请传入提交状态");

                Long id = param.getPrerunIds().get(0);
                IotAssert.isNotNull(id, "请传入开通调试工单信息id");

                final PrerunPo byPrerunNo = prerunRwDs.getById(id, true);
                IotAssert.isNotNull(byPrerunNo, "找不到调试工单信息");

                if(PrerunStatusType.INIT.equals(byPrerunNo.getStatus()) &&
                    PrerunStatusType.WAIT_CHECK.equals(param.getStatus())) {

                    byPrerunNo.setStatus(param.getStatus());
                    prerunRwDs.updatePrerun(byPrerunNo);

                    return prerunRoDs.getById(byPrerunNo.getId());
                } else {
                    log.error("from: {}, to: {}", byPrerunNo.getStatus(), param.getStatus());
                    IotAssert.isTrue(false, "工单状态不不正确，请刷新后重试");
                }
                return null;
            });
    }

    public Mono<ListResponse<PrerunMixDto>> searchPrerunList(PrerunSearchParam param) {
        AtomicReference<Map<String, List<String>>> siteGidMapRef = new AtomicReference<>(new HashMap<>());
        AtomicReference<Map<String, String>> groupMapRef = new AtomicReference<>(new HashMap<>());

        return Mono.just(param)
            .map(e -> prerunRoDs.searchPrerunList(param))
            .flatMap(prerunVoList -> {
                List<String> siteIdList = prerunVoList.stream()
                    .map(PrerunVo::getSiteId)
                    .collect(Collectors.toList());
                return Mono.just(siteIdList)
                    .flatMap(e -> siteGroupService.getSiteGroupMapVo(e))
                    .map(mapVoOptional -> {
                        mapVoOptional.ifPresent(t -> {
                            siteGidMapRef.set(t.getSiteGidMap());
                            groupMapRef.set(t.getGroupMap());
                        });
                        return prerunVoList;
                    });
            })
            .map(e -> e.stream().map(prerun -> {
                PrerunMixDto ret = new PrerunMixDto();
                ret.setPrerun(prerun);
                ret.setPrerunAssetImgPoList(prerunAssetImgRoDs.getByPrerunId(prerun.getId()));
                ret.setPrerunEvsePoList(prerunEvseRoDs.getByPrerunId(prerun.getId()));
                if(PrerunStatusType.NO_PASS.equals(prerun.getStatus()) ||
                    PrerunStatusType.SOLVED.equals(prerun.getStatus())) {
                    // 仅完成和不合格，才会获取质检记录
                    ret.setPrerunCheckPo(prerunCheckRoDs.getLatestByPrerunId(prerun.getId()));
                }

                // 返回场站运维组信息
                siteGidMapRef.get().getOrDefault(prerun.getSiteId(), List.of()).stream()
                    .filter(Objects::nonNull)
                    .filter(t -> StringUtils.isNotBlank(groupMapRef.get().get(t)))
                    .findFirst().ifPresent(s -> {
                        prerun.setSiteGid(s);
                        prerun.setSiteGidName(groupMapRef.get().get(s));
                    });

                return ret;
            }).collect(Collectors.toList()))
            .map(RestUtils::buildListResponse)
            .doOnNext(e -> {
                if(Boolean.TRUE.equals(param.getTotal())) {
                    long total = prerunRoDs.searchPrerunListCount(param);
                    e.setTotal(total);
                } else {
                    e.setTotal(0L);
                }
            });
    }

    public Mono<ObjectResponse<Boolean>> checkPrerun(PrerunCheckParam param) {
        IotAssert.isNotNull(param, "请传入参数");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getPrerunIds()), "请传入工单id");
        IotAssert.isNotNull(param.getStatus(), "请传入质检结果");

        if(!List.of(PrerunStatusType.NO_PASS, PrerunStatusType.SOLVED)
            .contains(param.getStatus())) {
            log.error("不支持的质检类型: {}", param.getStatus());
            IotAssert.isTrue(false, "不支持的质检类型");
        }

        return Mono.just(param)
            .doOnNext(e -> {
                for(Long id : param.getPrerunIds()) {
                    final PrerunPo prerunPo = prerunRwDs.getById(id, true);
                    if(prerunPo == null) {
                        log.warn("当前调试工单不存在: {}", id);
                        continue;
                    }
                    if(Boolean.FALSE.equals(prerunPo.getEnable())) {
                        log.warn("当前调试工单已删除: {}", id);
                        continue;
                    }
                    if(!PrerunStatusType.WAIT_CHECK.equals(prerunPo.getStatus())) {
                        log.warn("当前调试工单{}, 状态为{}, 无法质检", id, prerunPo.getStatus());
                        continue;
                    }
                    PrerunCheckPo prerunCheckPo = new PrerunCheckPo();
                    prerunCheckPo.setPrerunId(id)
                            .setFromStatus(prerunPo.getStatus())
                                .setToStatus(param.getStatus())
                                    .setComment(param.getComment())
                                        .setOpUid(param.getOpUid())
                                            .setOpName(param.getOpName());
                    prerunCheckRwDs.insertPrerunCheck(prerunCheckPo);
                    prerunPo.setStatus(param.getStatus());
                    prerunRwDs.updatePrerun(prerunPo);
                }
            })
            .map(e -> Boolean.TRUE)
            .map(RestUtils::buildObjectResponse);
    }

    public Mono<ObjectResponse<Boolean>> cancelPrerun(PrerunCheckParam param) {
        IotAssert.isNotNull(param, "请传入参数");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getPrerunIds()), "请传入工单id");

        return Mono.just(param)
            .doOnNext(e -> {
                for(Long id : param.getPrerunIds()) {
                    final PrerunPo prerunPo = prerunRwDs.getById(id, true);
                    IotAssert.isNotNull(prerunPo, "当前调试工单不存在: " + id);
                    IotAssert.isTrue(prerunPo.getEnable(), "当前调试工单已删除: " + id);
                    IotAssert.isTrue(PrerunStatusType.INIT.equals(prerunPo.getStatus()),
                        "当前调试工单状态不支持取消: " + id + "-" + prerunPo.getStatus());

                    prerunPo.setStatus(PrerunStatusType.CANCEL);
                    prerunRwDs.updatePrerun(prerunPo);
                }
            })
            .map(e -> Boolean.TRUE)
            .map(RestUtils::buildObjectResponse);
    }

    public Mono<ObjectResponse<Boolean>> deletePrerun(PrerunCheckParam param) {
        IotAssert.isNotNull(param, "请传入参数");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getPrerunIds()), "请传入工单id");

        List CanDeleteStatusList = List.of(PrerunStatusType.CANCEL, PrerunStatusType.WAIT_CHECK);

        return Mono.just(param)
            .doOnNext(e -> {
                for(Long id : param.getPrerunIds()) {
                    final PrerunPo prerunPo = prerunRwDs.getById(id, true);
                    IotAssert.isNotNull(prerunPo, "当前调试工单不存在: " + id);
                    IotAssert.isTrue(prerunPo.getEnable(), "当前调试工单已删除: " + id);
                    IotAssert.isTrue(CanDeleteStatusList.contains(prerunPo.getStatus()),
                        "当前调试工单状态不支持删除: " + id + "-" + prerunPo.getStatus());

                    prerunPo.setEnable(false);
                    prerunRwDs.updatePrerun(prerunPo);
                }
            })
            .map(e -> Boolean.TRUE)
            .map(RestUtils::buildObjectResponse);
    }

    public Mono<ObjectResponse<PrerunMixDto>> getLatestPrerun(String siteId, Long prerunId) {
        IotAssert.isTrue(StringUtils.isNotBlank(siteId) || prerunId != null, "请传入场站id或工单id");

        return Mono.just("")
            .map(e -> {
                PrerunSearchParam param = new PrerunSearchParam();
                param.setSiteId(siteId);
                if(prerunId != null) {
                    param.setPrerunIds(List.of(prerunId));
                }
                param.setEnable(true);
                param.setSize(1);
                param.setStart(0L);
                return prerunRoDs.searchPrerunList(param);
            })
            .filter(CollectionUtils::isNotEmpty)
            .map(e -> e.stream().findFirst())
            .map(Optional::get)
            .map(prerun -> {
                PrerunMixDto ret = new PrerunMixDto();
                ret.setPrerun(prerun);
                ret.setPrerunAssetImgPoList(prerunAssetImgRoDs.getByPrerunId(prerun.getId()));
                ret.setPrerunEvsePoList(prerunEvseRoDs.getByPrerunId(prerun.getId()));
                if(PrerunStatusType.NO_PASS.equals(prerun.getStatus()) ||
                    PrerunStatusType.SOLVED.equals(prerun.getStatus())) {
                    // 仅完成和不合格，才会获取质检记录
                    ret.setPrerunCheckPo(prerunCheckRoDs.getLatestByPrerunId(prerun.getId()));
                }
                return ret;
            })
            .map(RestUtils::buildObjectResponse)
            .switchIfEmpty(Mono.just(RestUtils.buildObjectResponse(null)));
    }

    /**
     * 设定场站当前工单是否接入平台
     * @param siteId
     * @param b
     */
    public void setSitePlatformConn(String siteId, boolean b) {
        log.info("设定场站当前工单是否接入平台{}, {}", siteId, b);
        PrerunSearchParam param = new PrerunSearchParam();
        param.setSiteId(siteId);
        param.setEnable(true);
        param.setSize(1);
        param.setStart(0L);
        final List<PrerunVo> prerunVos = prerunRoDs.searchPrerunList(param);
        if(CollectionUtils.isNotEmpty(prerunVos)) {
            log.info("场站存在有效工单: {}", prerunVos);
            final PrerunVo prerunVo = prerunVos.get(0);
            PrerunPo prerunPo = new PrerunPo();
            prerunPo.setId(prerunVo.getId());
            prerunPo.setPlatformConn(b);
            log.info("修改是否接入平台, prerunId: {}, {}",
                prerunVo.getId(), prerunRwDs.updatePrerun(prerunPo));
        } else {
            log.info("场站不存在有效工单。");
        }
    }
}