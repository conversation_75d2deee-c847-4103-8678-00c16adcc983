package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.client.UserFeignClient;
import com.cdz360.biz.dc.service.site.SiteBizService;
import com.cdz360.biz.ds.trading.ro.corp.ds.CorpUserRoDs;
import com.cdz360.biz.ds.trading.ro.coupon.ds.ActivityRoDs;
import com.cdz360.biz.ds.trading.ro.coupon.ds.CouponDictRoDs;
import com.cdz360.biz.ds.trading.ro.iot.ds.BsBoxRoDs;
import com.cdz360.biz.ds.trading.ro.iot.ds.BsChargerRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteChargeJobLogRoDs;
import com.cdz360.biz.ds.trading.ro.site.mapper.SiteRoMapper;
import com.cdz360.biz.ds.trading.ro.soc.mapper.SiteSocCfgRoMapper;
import com.cdz360.biz.ds.trading.ro.user.ds.TRUserRoDs;
import com.cdz360.biz.ds.trading.rw.coupon.ds.ActivityRwDs;
import com.cdz360.biz.ds.trading.rw.coupon.ds.CouponDictRwDs;
import com.cdz360.biz.ds.trading.rw.coupon.ds.CouponScoreSettingRwDs;
import com.cdz360.biz.ds.trading.rw.coupon.ds.CouponSiteRwDs;
import com.cdz360.biz.ds.trading.rw.iot.ds.BsBoxRwDs;
import com.cdz360.biz.ds.trading.rw.iot.ds.BsChargerRwDs;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteChargeJobRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteDefaultSettingRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteRwDs;
import com.cdz360.biz.ds.trading.rw.soc.ds.SiteSocStrategyRwDs;
import com.cdz360.biz.model.trading.coupon.param.CouponDictParam;
import com.cdz360.biz.model.trading.coupon.param.CouponDictSearchParam;
import com.cdz360.biz.model.trading.coupon.po.*;
import com.cdz360.biz.model.trading.coupon.type.CouponDictStatusType;
import com.cdz360.biz.model.trading.coupon.type.CouponDictType;
import com.cdz360.biz.model.trading.coupon.type.CouponValidType;
import com.cdz360.biz.model.trading.coupon.type.ExtraScoreSettingType;
import com.cdz360.biz.model.trading.coupon.vo.ChangeVo;
import com.cdz360.biz.model.trading.coupon.vo.CouponDictVo;
import com.cdz360.biz.model.trading.coupon.vo.UserInfoVo;
import com.cdz360.biz.model.trading.site.po.SiteChargeJobPo;
import com.cdz360.biz.model.trading.site.po.SiteDefaultSettingPo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobVo;
import com.cdz360.biz.model.trading.soc.vo.SocCorpUserVo;
import com.cdz360.data.cache.RedisIotRwService;
import com.chargerlinkcar.framework.common.domain.ChangeInfo;
import com.chargerlinkcar.framework.common.service.DcEventPublisherService;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.IotAssert;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * CouponDictService
 *
 * @since 7/27/2020 4:52 PM
 * <AUTHOR>
 */
@Slf4j
@Service
public class CouponDictService {

    @Autowired
    private CouponDictRwDs couponDictRwDs;

    @Autowired
    private CouponDictRoDs couponDictRoDs;

    @Autowired
    private CouponSiteRwDs couponSiteRwDs;

    @Autowired
    private CouponScoreSettingRwDs couponScoreSettingRwDs;

    @Autowired
    private ActivityRoDs activityRoDs;

    @Autowired
    private ActivityRwDs activityRwDs;

    @Autowired
    private SiteChargeJobLogRoDs siteChargeJobLogRoDs;

    @Autowired
    private SiteChargeJobRwDs siteChargeJobRwDs;

    @Autowired
    private SiteSocCfgRoMapper siteSocCfgRoMapper;

    @Autowired
    private SiteSocStrategyRwDs siteSocStrategyRwDs;

    @Autowired
    private SiteRoMapper siteRoMapper;

    @Autowired
    private SiteRwDs siteRwDs;

    @Autowired
    private ChargerOrderRoDs chargerOrderRoDs;

    @Autowired
    private ChargerOrderRwDs chargerOrderRwDs;

    @Autowired
    private SiteBizService siteBizService;

    @Autowired
    private DcEventPublisherService dcEventPublisherService;

    @Autowired
    private BsBoxRoDs bsBoxRoDs;

    @Autowired
    private BsBoxRwDs bsBoxRwDs;

    @Autowired
    private BsChargerRoDs bsChargerRoDs;

    @Autowired
    private BsChargerRwDs bsChargerRwDs;

    @Autowired
    private RedisIotRwService redisIotRwService;

    @Autowired
    private SiteDefaultSettingRwDs siteDefaultSettingRwDs;

    @Autowired
    private CorpUserRoDs corpUserRoDs;

    @Autowired
    private TRUserRoDs trUserRoDs;

    @Autowired
    private UserFeignClient userFeignClient;

    public BaseResponse dictCreate(CouponDictParam req) {

        IotAssert.isTrue(couponDictRoDs.getDictCountByName(req.getName()).intValue() == 0,
                "存在同名模板");

        IotAssert.isNotNull(req.getValidType(), "请输入有效期类型");

        if (req.getType() == null) {
            req.setType(CouponDictType.SERV_FEE_FIX);
        }

        if (CouponValidType.FIX.equals(req.getValidType())) {
            IotAssert.isNotNull(req.getValidTimeFrom(), CouponValidType.FIX.getDesc() + "请输入有效期-开始");
            IotAssert.isNotNull(req.getValidTimeTo(), CouponValidType.FIX.getDesc() + "请输入有效期-截至");

            // 修复时间为00:00:00
            req.setValidTimeFrom(DateUtil.getThisDate(req.getValidTimeFrom()));
            req.setValidTimeTo(DateUtil.getNextDate(DateUtil.getThisDate(req.getValidTimeTo())));
            IotAssert.isTrue(req.getValidTimeFrom().getTime() <= req.getValidTimeTo().getTime(),
                    "开始时间须在结束时间之前");

            req.setValidRelateDay(null);

        } else if (CouponValidType.RELATE.equals(req.getValidType())) {
            IotAssert.isNotNull(req.getValidRelateDay(), CouponValidType.RELATE.getDesc() + "，请填写过期日, 自领取时间");
            IotAssert.isTrue(req.getValidRelateDay().intValue() > 0, "过期日, 自领取时间须大于0");

            req.setValidTimeFrom(null);
            req.setValidTimeTo(null);

        }

        // 校验叠加规则
        if (ExtraScoreSettingType.PART.equals(req.getExtraScoreSettingType())) {
            // 叠加部分积分体系时，必须选择积分体系列表
            if (CollectionUtils.isEmpty(req.getExtraScoreSettingList())) {
                throw new DcArgumentException("叠加部分积分体系时，积分体系列表不能为空");
            }
        } else {
            req.setExtraScoreSettingList(new ArrayList<>());
        }
        IotAssert.isTrue(DecimalUtils.gtZero(req.getConditionAmount()), "实际满内容要大于0");
        IotAssert.isTrue(DecimalUtils.gtZero(req.getAmount()), "满减金额要大于0");
        IotAssert.isTrue(req.getConditionAmount().compareTo(req.getAmount()) > 0, "实际满的金额要大于减的金额");
        // 处理显示满减金额，显示的金额为null时用配置的实际满减金额来填充
        if (req.getShowConditionAmount() == null) {
            req.setShowConditionAmount(req.getConditionAmount());
        } else {
            IotAssert.isTrue(DecimalUtils.gtZero(req.getAmount()), "显示金额要大于0");
        }

        CouponDictPo couponDictPo = new CouponDictPo();

        BeanUtils.copyProperties(req, couponDictPo);

        couponDictPo.setStatus(CouponDictStatusType.ENABLE)
            .setScoreSettingType(req.getExtraScoreSettingType());

        IotAssert.isTrue(couponDictRwDs.insertCouponDict(couponDictPo), "创建失败");

        // 关联场站
        if (CollectionUtils.isNotEmpty(req.getUsableSiteIdList())) {
            List<CouponSitePo> siteList = req.getUsableSiteIdList().stream().map(e -> {
                CouponSitePo couponSitePo = new CouponSitePo();
                couponSitePo.setCouponDictId(couponDictPo.getId()).setSiteId(e);
                return couponSitePo;
            }).collect(Collectors.toList());

            IotAssert.isTrue(couponSiteRwDs.batchInsert(siteList) == siteList.size(), "关联场站失败");
        }

        // 关联场站组

        if (CollectionUtils.isNotEmpty(req.getGidList())) {
            List<CouponGidPo> gidList = req.getGidList().stream().map(e -> {
                CouponGidPo couponGidPo = new CouponGidPo();
                couponGidPo.setCouponDictId(couponDictPo.getId()).setGid(e);
                return couponGidPo;
            }).collect(Collectors.toList());
            IotAssert.isTrue(couponSiteRwDs.batchInsertGid(gidList) == gidList.size(), "关联场站组失败");
        }

        // 关联积分体系
        if (CollectionUtils.isNotEmpty(req.getExtraScoreSettingList())) {
            // 仅存储叠加部分的
            List<CouponScoreSettingPo> couponScoreSettingList = req.getExtraScoreSettingList().stream().map(e -> {
                CouponScoreSettingPo couponScoreSettingPo = new CouponScoreSettingPo();
                couponScoreSettingPo.setCouponDictId(couponDictPo.getId())
                        .setScoreSettingId(e.getScoreSettingId())
                        .setScoreSettingName(e.getScoreSettingName());
                return couponScoreSettingPo;
            }).collect(Collectors.toList());
            IotAssert.isTrue(couponScoreSettingRwDs.batchInsert(couponScoreSettingList) == couponScoreSettingList.size(), "关联积分体系失败");
        }


        return BaseResponse.success();
    }

    public ListResponse<CouponDictVo> getDictList(CouponDictSearchParam req) {
//        Page<CouponDictVo> pageInfo = PageHelper.offsetPage(req.getStart(), req.getSize());
        List<CouponDictVo> dictList = couponDictRoDs.getDictList(req);

        // 存在配置场站组的
        if (CollectionUtils.isNotEmpty(dictList)) {
            List<Long> dictIdList = dictList.stream()
                .filter(x -> CollectionUtils.isEmpty(x.getSiteList())).map(CouponDictVo::getId)
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(dictIdList)) {
                List<CouponDictVo> dictGidList = couponDictRoDs.getDictGidList(dictIdList);
                if (CollectionUtils.isNotEmpty(dictGidList)) {

                    Map<Long, List<SitePo>> dictMap = dictGidList.stream()
                        .collect(Collectors.toMap(CouponDictVo::getId, CouponDictVo::getSiteList));
                    dictList.forEach(x -> {
                        if (x.getId() != null && dictMap.containsKey(x.getId())) {
                            x.setSiteList(dictMap.get(x.getId()));
                        }
                    });
                }
            }
        }

        // 处理显示满减金额
        dictList.stream().forEach(dictVo -> {
            if (dictVo.getShowConditionAmount() == null) {
                dictVo.setShowConditionAmount(dictVo.getConditionAmount());
            }
        });
        return new ListResponse<>(dictList, couponDictRoDs.getDictListCount(req));
//        return new ListResponse<>(couponDictRoDs.getDictList(req), 100L);
    }

    public BaseResponse disableDict(Long id) {
        IotAssert.isNotNull(id, "请输入模板id");

        List<ActivityPo> processingByDictId = activityRoDs.getProcessingByDictId(id);
        IotAssert.isTrue(CollectionUtils.isEmpty(processingByDictId), "作废失败：请将关联活动停止后再操作");

        IotAssert.isTrue(couponDictRwDs.disableDict(id), "作废券模板失败: " + id);
        return BaseResponse.success();
    }

    /**
     * 站点修改商户，需要调整券模板，券，活动等信息
     *
     * @param idChain
     * @param siteId
     * @return
     */
    public ChangeVo changeBySiteId(String idChain, String siteId) {

        ChangeVo changeVo = new ChangeVo();
        //获取券、活动相关信息
        List<CouponDictVo> couponDictPoList = couponDictRoDs.getDictListBySiteId(siteId);

        if (CollectionUtils.isNotEmpty(couponDictPoList)) {
            //保留券模板
            changeVo.setEnableCouponTemplate(couponDictPoList.stream().filter(e -> idChain.contains(e.getIdChain())).collect(Collectors.toList()));
            //删除券模板--不包含在idChain上，且siteAmount>1
            changeVo.setUnEnableCouponTemplate(couponDictPoList.stream().filter(e -> !idChain.contains(e.getIdChain()) && e.getSiteAmount() > 1L).collect(Collectors.toList()));
            //作废的券模板 --不包含在idChain上，且siteAmount = 1
            changeVo.setInvalidCouponTemplate(couponDictPoList.stream().filter(e -> !idChain.contains(e.getIdChain()) && e.getSiteAmount().equals(1L)).collect(Collectors.toList()));
            //券模板删除或作废影响的活动
            List<Long> couponDictIdList = couponDictPoList.stream().filter(e -> !idChain.contains(e.getIdChain())).map(CouponDictVo::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(couponDictIdList)) {
                changeVo.setUnEnableActivity(activityRoDs.getActivityListByDictId(couponDictIdList));
            }

        }

        //获取定时任务相关信息
        List<SiteChargeJobVo> siteChargeJob = siteChargeJobLogRoDs.getSiteJobBySiteId(siteId);
        changeVo.setSiteChargeJob(siteChargeJob);

        //企业充电限制查询
        List<SocCorpUserVo> socCorpUserVoList = this.siteSocCfgRoMapper.queryChangeUserInfoBySiteId(siteId);
        if (CollectionUtils.isNotEmpty(socCorpUserVoList)) {
            changeVo.setEnableChargeLimitCorp(socCorpUserVoList.stream().filter(e -> idChain.contains(e.getIdChain())).collect(Collectors.toList()));
            changeVo.setUnEnableChargeLimitCorp(socCorpUserVoList.stream().filter(e -> !idChain.contains(e.getIdChain())).collect(Collectors.toList()));
        }

        //后台启动功能配置
        SitePo sitePo = siteRoMapper.getSite(siteId);
        if (sitePo != null) {
            changeVo.setDefaultPayType(PayAccountType.valueOf(sitePo.getDefaultPayType()))
                .setPayAccountId(sitePo.getPayAccountId());
        } else {
            throw new DcServiceException("站点信息不存在");
        }
        // 无卡启动充电结算账户设置
        SiteDefaultSettingPo siteDefaultSetting = siteDefaultSettingRwDs.getBySiteId(siteId);
        if (null != siteDefaultSetting &&
                null != siteDefaultSetting.getNoCardPayAccountType() &&
                !siteDefaultSetting.getNoCardPayAccountType().equals(0) &&
                !siteDefaultSetting.getNoCardPayAccountType().equals(1)) {
            UserInfoVo userInfo = new UserInfoVo();
            switch (siteDefaultSetting.getNoCardPayAccountType()) {
                case 2: // 企业授信账户
                    userInfo = corpUserRoDs.getUserInfoById(siteDefaultSetting.getNoCardPayAccountId());
                    break;
                case 3: // 商户会员
                     userInfo = trUserRoDs.selectSiteDefaultInfo(siteDefaultSetting.getSiteId());
                    break;
            }

            if (userInfo != null && StringUtils.isNotBlank(userInfo.getCommIdChain())) {
                userInfo.setPayAccountType(siteDefaultSetting.getNoCardPayAccountType());
                if (idChain.contains(userInfo.getCommIdChain())) {
                    changeVo.setEnableNoCardUserInfo(userInfo);
                } else {
                    changeVo.setUnEnableNoCardUserInfo(userInfo);
                }
            }
        }

        return changeVo;
    }

    /**
     * 执行站点修改商户需要删除的券模板、活动、充电限制、定时任务
     *
     * @param changeInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public int queryChangeCommIdBySiteId(ChangeInfo changeInfo) {
        try {
            //需要删除的券模板
            if (CollectionUtils.isNotEmpty(changeInfo.getUnEnableCouponTemplate())) {
                List<Long> couponDictIdList = changeInfo.getUnEnableCouponTemplate().stream().map(CouponDictVo::getId).collect(Collectors.toList());
                couponSiteRwDs.updateCouponSiteBySiteId(couponDictIdList, changeInfo.getSiteId());
            }
            //需要废除的券模板,券模板可用场站enable=0,券状态DISABLE
            if (CollectionUtils.isNotEmpty(changeInfo.getInvalidCouponTemplate())) {
                List<Long> couponDictIdList = changeInfo.getInvalidCouponTemplate().stream().map(CouponDictVo::getId).collect(Collectors.toList());
                couponSiteRwDs.updateCouponSiteBySiteId(couponDictIdList, changeInfo.getSiteId());
                couponDictRwDs.disableDictById(couponDictIdList);
            }
            //需要停止的活动
            if (CollectionUtils.isNotEmpty(changeInfo.getUnEnableActivity())) {
                List<Long> activityIdList = changeInfo.getUnEnableActivity().stream().map(ActivityPo::getId).collect(Collectors.toList());
                activityRwDs.updateActivityById(activityIdList);
            }

            //需要删除的充电充电限制用户
            if (CollectionUtils.isNotEmpty(changeInfo.getUnEnableChargeLimitCorp())) {
                List<Long> socUserIdList = changeInfo.getUnEnableChargeLimitCorp().stream().map(SocCorpUserVo::getId).collect(Collectors.toList());
                siteSocStrategyRwDs.deleteById(socUserIdList);
            }

            //需要删除的定时任务
            if (CollectionUtils.isNotEmpty(changeInfo.getUnEnableTask())) {
                List<Long> taskIdList = changeInfo.getUnEnableTask().stream().map(SiteChargeJobPo::getId).collect(Collectors.toList());
                siteChargeJobRwDs.updatePayTypeByIds(taskIdList);
            }
            //需要删除的无卡启动结算账户
            if (changeInfo.getUnEnableNoCardUserInfo() != null) {
                SiteDefaultSettingPo siteDefaultPo = new SiteDefaultSettingPo();
                siteDefaultPo.setSiteId(changeInfo.getSiteId())
                        .setNoCardPayAccountId(0L)
                        .setNoCardPayAccountType(0);
                siteDefaultSettingRwDs.updateBySiteId(siteDefaultPo);
            }

            //后台启动功能、站点归属商户调整
            SitePo sitePo = new SitePo();
            sitePo.setId(changeInfo.getSiteId());
            sitePo.setOperateId(changeInfo.getCommId());
            sitePo.setOperateName(changeInfo.getCommName());

            if (changeInfo.getChargeSetting().equals(false) && changeInfo.getSiteId() != null) {
                sitePo.setDefaultPayType(PayAccountType.UNKNOWN.getCode())
                    .setPayAccountId(0L);
            }
            siteRwDs.updateSite(sitePo);

            //修改bs_box中business_id
            bsBoxRwDs.updateBsBoxBySiteId(changeInfo.getSiteId(), changeInfo.getCommId());
            //修改bs_charger中business_id
            bsChargerRwDs.updateChargerBySiteId(changeInfo.getSiteId(), changeInfo.getCommId());
            //站点下的订单调整到新商户下
//            ChargerOrder chargerOrder = chargerOrderRoDs.findByStationId(changeInfo.getSiteId());
//            if (chargerOrder != null) {
            chargerOrderRwDs.updateCommIdBySiteId(changeInfo.getCommId(), changeInfo.getSiteId(), changeInfo.getCommName());
//            }

            log.info("站点修改商户操作成功");
            return 1;
        } catch (Exception e) {
            log.info("站点修改商户操作失败：message={}", e.getMessage());
            return 2;
        }

    }

    /**
     * 站点修改商户，同步修改commId
     *
     * @param changeInfo
     */
    public void sysSiteCommId(ChangeInfo changeInfo) {
        SitePo siteInfo = siteRoMapper.getSite(changeInfo.getSiteId());
        siteInfo.setOperateId(changeInfo.getCommId()).setOperateName(changeInfo.getCommName());
        //站点信息同步到mongo
        siteBizService.updateMongoSite(siteInfo);
        //站点信息同步到MQ
        log.info("站点更新商户推送MQ");
        dcEventPublisherService.publishSiteInfo(siteInfo);
    }

    /**
     * 同步桩信息
     *
     * @param changeInfo
     */
    public void updateEvseRedisCache(ChangeInfo changeInfo) {
        if (StringUtils.isBlank(changeInfo.getSiteId())) {
            throw new DcServiceException("场站ID不能为空");
        }
        List<EvseVo> evseVoList = bsBoxRoDs.getEvseList(changeInfo.getSiteId());
        if (CollectionUtils.isNotEmpty(evseVoList)) {
            evseVoList.stream().forEach(e -> {
                e.setSiteCommId(changeInfo.getCommId());
                redisIotRwService.updateEvseRedisCache(e);
            });
        }
    }

    /**
     * 同步枪信息
     *
     * @param changeInfo
     */
    public void updatePlugRedisCache(ChangeInfo changeInfo) {
        if (StringUtils.isBlank(changeInfo.getSiteId())) {
            throw new DcServiceException("场站ID不能为空");
        }
        List<PlugVo> evseVoList = bsChargerRoDs.getPlugList(changeInfo.getSiteId());
        if (CollectionUtils.isNotEmpty(evseVoList)) {
            evseVoList.stream().forEach(e -> {
                e.setSiteCommId(changeInfo.getCommId()).setSiteCommName(changeInfo.getCommName());
                redisIotRwService.updatePlugRedisCache(e);
            });
        }
    }

    public int refreshStatus2Expire() {
        return couponDictRwDs.refreshStatus2Expire();
    }

    public List<CouponDictPo> getDictListById(List<Long> dictIdList) {
        return couponDictRoDs.getDictListById(dictIdList);
    }
}