package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.param.TimeFilter2;
import com.cdz360.base.model.es.type.ChargeFlowType;
import com.cdz360.base.model.es.vo.MeterRtData;
import com.cdz360.base.model.meter.vo.MeterKhwData;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ds.ess.ro.data.ds.EssEquipTimelyRoDs;
import com.cdz360.biz.ds.ess.rw.data.ds.EssEquipTimelyRwDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ess.model.data.po.EssEquipTimelyPo;
import com.cdz360.biz.model.ess.param.ListEssDailyParam;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.trading.iot.po.EvsePo;
import com.cdz360.biz.model.trading.meter.param.MeterDataListParam;
import com.cdz360.biz.model.trading.meter.param.MeterListParam;
import com.cdz360.biz.model.trading.meter.po.DeviceMeterPo;
import com.cdz360.biz.model.trading.meter.po.MeterPo;
import com.cdz360.biz.model.trading.meter.vo.MeterDataVo;
import com.cdz360.biz.model.trading.meter.vo.MeterEvseVo;
import com.cdz360.biz.model.trading.meter.vo.SiteMeterVo;
import com.cdz360.biz.model.trading.site.vo.EvseElecVo;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import com.cdz360.biz.utils.feign.iot.DeviceFeignClient;
import com.cdz360.biz.utils.feign.iot.MeterFeignClient;
import com.chargerlinkcar.framework.common.utils.DateUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class MeterDataService {

    @Autowired
    private EssEquipTimelyRoDs essEquipTimelyRoDs;

    @Autowired
    private EssEquipTimelyRwDs essEquipTimelyRwDs;

    @Autowired
    private MeterFeignClient meterFeignClient;

    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private DeviceFeignClient deviceFeignClient;

    @Autowired
    private ChargerOrderRoDs chargerOrderRoDs;


    /**
     * 保存上月和这月数据，上传上来的不是电量，都是电表读数.
     * <p>
     * 因此上月的数据用上月读数减去上上月的，这月的需要用当前读数减一下上个月的读数
     * </p>
     *
     * @param siteId
     * @param gwno
     * @param dno
     * @param ts
     * @param meterRtData
     */
    @Transactional
    public void saveMeterRtAndHisData(String siteId, String gwno, String dno, String ts,
        MeterRtData meterRtData) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
        try {
            LocalDateTime dateTime = LocalDateTime.parse(ts, formatter);
            // 保存上月数据
            this.saveMonthMeterData(siteId, dno, dateTime, meterRtData, true);
            // 保存这个月的数据
            this.saveMonthMeterData(siteId, dno, dateTime, meterRtData, false);

        } catch (DateTimeParseException e) {
            throw new DcServiceException("日期时间格式不正确");
        }
    }

    /**
     * 保存电表上传的上月数据
     *
     * @param siteId
     * @param dno
     * @param ts
     * @param meterRtData
     * @param lastMonthFlag 是否是上个月的历史数据标识, true,上个月，null或者false，这个月
     */
    private void saveMonthMeterData(String siteId, String dno, LocalDateTime ts,
        MeterRtData meterRtData, Boolean lastMonthFlag) {
        // 存在且数据不一致则修改，不存在则新增
        ListEssDailyParam param = new ListEssDailyParam();
        param.setDnos(Collections.singletonList(dno));
        param.setSiteIdList(Collections.singletonList(siteId));
        TimeFilter2 timeFilter = new TimeFilter2();
        timeFilter.setStartTime(
            DateUtils.getMonthStartTime(ts, Boolean.TRUE.equals(lastMonthFlag)));
        timeFilter.setEndTime(
            DateUtils.getMonthEndTime(ts, Boolean.TRUE.equals(lastMonthFlag)));
        param.setStartTimeFilter(timeFilter);
        param.setSize(100);
        List<EssEquipTimelyPo> essEquipTimelyPoList = essEquipTimelyRoDs.getTimelyElecList(param);

        // 总
        EssEquipTimelyPo essEquipTimelyAllPo = essEquipTimelyPoList.stream()
            .filter(timelyPo -> timelyPo.getPiName().equals("总"))
            .findFirst().orElse(new EssEquipTimelyPo());

        this.saveElecData(essEquipTimelyAllPo, dno, siteId, ts, "总",
            Boolean.TRUE.equals(lastMonthFlag) ? meterRtData.getKwhL2() : meterRtData.getKwhL1(),
            Boolean.TRUE.equals(lastMonthFlag) ? meterRtData.getKwhL1() : meterRtData.getKwh(),
            lastMonthFlag);

        // 尖时
        EssEquipTimelyPo essEquipTimelySharpPeakPo = essEquipTimelyPoList.stream()
            .filter(timelyPo -> timelyPo.getPiName().equals("尖时"))
            .findFirst().orElse(new EssEquipTimelyPo());

        this.saveElecData(essEquipTimelySharpPeakPo, dno, siteId, ts, "尖时",
            Boolean.TRUE.equals(lastMonthFlag) ? meterRtData.getKwhL2() : meterRtData.getKwhL1(),
            Boolean.TRUE.equals(lastMonthFlag) ? meterRtData.getKwhL1() : meterRtData.getKwh(),
            lastMonthFlag);

        // 峰时
        EssEquipTimelyPo essEquipTimelyPeakPo = essEquipTimelyPoList.stream()
            .filter(timelyPo -> timelyPo.getPiName().equals("峰时"))
            .findFirst().orElse(new EssEquipTimelyPo());

        this.saveElecData(essEquipTimelyPeakPo, dno, siteId, ts, "峰时",
            Boolean.TRUE.equals(lastMonthFlag) ? meterRtData.getKwhL2() : meterRtData.getKwhL1(),
            Boolean.TRUE.equals(lastMonthFlag) ? meterRtData.getKwhL1() : meterRtData.getKwh(),
            lastMonthFlag);

        // 平时
        EssEquipTimelyPo essEquipTimelyOffPeakPo = essEquipTimelyPoList.stream()
            .filter(timelyPo -> timelyPo.getPiName().equals("平时"))
            .findFirst().orElse(new EssEquipTimelyPo());

        this.saveElecData(essEquipTimelyOffPeakPo, dno, siteId, ts, "平时",
            Boolean.TRUE.equals(lastMonthFlag) ? meterRtData.getKwhL2() : meterRtData.getKwhL1(),
            Boolean.TRUE.equals(lastMonthFlag) ? meterRtData.getKwhL1() : meterRtData.getKwh(),
            lastMonthFlag);

        // 谷时
        EssEquipTimelyPo essEquipTimelyValleyPo = essEquipTimelyPoList.stream()
            .filter(timelyPo -> timelyPo.getPiName().equals("谷时"))
            .findFirst().orElse(new EssEquipTimelyPo());

        this.saveElecData(essEquipTimelyValleyPo, dno, siteId, ts, "谷时",
            Boolean.TRUE.equals(lastMonthFlag) ? meterRtData.getKwhL2() : meterRtData.getKwhL1(),
            Boolean.TRUE.equals(lastMonthFlag) ? meterRtData.getKwhL1() : meterRtData.getKwh(),
            lastMonthFlag);

        // 深谷
        EssEquipTimelyPo essEquipTimelyDeepValleyPo = essEquipTimelyPoList.stream()
            .filter(timelyPo -> timelyPo.getPiName().equals("深谷"))
            .findFirst().orElse(new EssEquipTimelyPo());

        this.saveElecData(essEquipTimelyDeepValleyPo, dno, siteId, ts, "深谷",
            Boolean.TRUE.equals(lastMonthFlag) ? meterRtData.getKwhL2() : meterRtData.getKwhL1(),
            Boolean.TRUE.equals(lastMonthFlag) ? meterRtData.getKwhL1() : meterRtData.getKwh(),
            lastMonthFlag);

    }

    /**
     * 保存电量相关数据。
     * <p>
     * 1.如果保存的是上个月的数据，用上月的电表读数减去上上月的电表读数就是电量
     * </p>
     * <p>
     * 2.如果保存的是这个月的数据，用这个月的电表读数减去上月的电表读数就是电量
     * </p>
     *
     * @param essEquipTimelyPo
     * @param dno
     * @param siteId
     * @param ts
     * @param tag
     * @param kwhL1            lastMonthFlag true时表示上上月数据，false or null时表示上月数据
     * @param kwh              lastMonthFlag true时表示上月数据，false or null时表示当月数据
     * @param lastMonthFlag    要计算的是否是上个月的历史数据标识, true,上个月，null或者false，这个月
     */
    private void saveElecData(EssEquipTimelyPo essEquipTimelyPo, String dno, String siteId,
        LocalDateTime ts, String tag, MeterKhwData kwhL1, MeterKhwData kwh, Boolean lastMonthFlag) {
        EssEquipTimelyPo elecData = new EssEquipTimelyPo();
        elecData.setDno(dno)
            .setSiteId(siteId)
            .setStartTime(DateUtils.getMonthStartTime(ts, Boolean.TRUE.equals(lastMonthFlag)))
            .setEndTime(Boolean.TRUE.equals(lastMonthFlag) ? DateUtils.getMonthEndTime(ts, true) :
                LocalDateTime.now())
            .setEssDno(dno)
            .setPiName(tag)
            .setFlowType(ChargeFlowType.CHARGE)
            .setEnable(true);

        // 新读数，减去旧读数后对应数据库里的inKwh字段
        BigDecimal readingValue = BigDecimal.ZERO;
        // 旧读数，对应的数据库里的totalInKwh字段
        BigDecimal oldReadingValue = BigDecimal.ZERO;

        if (kwh != null && kwh.getPositive() != null) {
            switch (tag) {
                case "总" -> readingValue = kwh.getPositive().getTotal();
                case "尖时" -> readingValue = kwh.getPositive().getV1();
                case "峰时" -> readingValue = kwh.getPositive().getV2();
                case "平时" -> readingValue = kwh.getPositive().getV3();
                case "谷时" -> readingValue = kwh.getPositive().getV4();
                case "深谷" -> readingValue = kwh.getPositive().getV5();
            }
            if (readingValue == null) {
                readingValue = BigDecimal.ZERO;
            }
        }
        if (kwhL1 != null && kwhL1.getPositive() != null) {
            switch (tag) {
                case "总" -> oldReadingValue = kwhL1.getPositive().getTotal();
                case "尖时" -> oldReadingValue = kwhL1.getPositive().getV1();
                case "峰时" -> oldReadingValue = kwhL1.getPositive().getV2();
                case "平时" -> oldReadingValue = kwhL1.getPositive().getV3();
                case "谷时" -> oldReadingValue = kwhL1.getPositive().getV4();
                case "深谷" -> oldReadingValue = kwhL1.getPositive().getV5();
            }
            if (oldReadingValue == null) {
                oldReadingValue = BigDecimal.ZERO;
            }
        }

        // 处理正向电量
        elecData.setInKwh(readingValue.subtract(oldReadingValue));
        // 处理开始电表读数，即上个月或者是上上个月的读数
        elecData.setTotalInKwh(oldReadingValue);

        if (Boolean.TRUE.equals(lastMonthFlag)) {
            // 上个月的
            if (essEquipTimelyPo.getInKwh() == null
                || essEquipTimelyPo.getInKwh().compareTo(elecData.getInKwh()) != 0
                || essEquipTimelyPo.getTotalInKwh() == null
                || essEquipTimelyPo.getTotalInKwh().compareTo(elecData.getTotalInKwh()) != 0) {
                // 正向电量不同或者是总正向电量不同，需要去新增或者更新数据库
                essEquipTimelyRwDs.addEssEquipTimely(elecData);
            }
        } else {
            // 这个月的，无需判断是否更新，直接去更新数据库
            essEquipTimelyRwDs.addEssEquipTimely(elecData);
        }
    }

    /**
     * 获取电表抄表数据
     *
     * @param param
     * @return
     */
    public ListResponse<MeterDataVo> getMeterDataList(MeterDataListParam param) {
        // 首先，按照分页数据在抄表数据里拿到所有的dno列表
        ListEssDailyParam listEssDailyParam = new ListEssDailyParam();

        TimeFilter2 timeFilter = new TimeFilter2();
        // 处理查询的账单时间
        if (param.getStartTimeFilter() != null) {
            timeFilter.setStartTime(
                param.getStartTimeFilter().getStartTime() != null ? DateUtils.getMonthStartTime(
                    param.getStartTimeFilter().getStartTime(), false) : null);
            timeFilter.setEndTime(
                param.getStartTimeFilter().getEndTime() != null ? DateUtils.getMonthEndTime(
                    param.getStartTimeFilter().getEndTime(), false) : null);
            listEssDailyParam.setStartTimeFilter(timeFilter);
        } else {
            timeFilter = null;
        }
        listEssDailyParam.setSiteIdList(param.getSiteIdList());
        listEssDailyParam.setGidList(param.getGidList());
        listEssDailyParam.setStart(param.getStart());
        listEssDailyParam.setSize(param.getSize());
        listEssDailyParam.setIdChain(param.getIdChain());
        // 获取到场站id，dno记录与site绑定才查询，因此之后再去meter表里拿场站绑定的dno
        // size和start无效
        List<String> elecDataSavedSiteIdList = essEquipTimelyRoDs.getDistinctSiteList(
            listEssDailyParam);

        if (CollectionUtils.isEmpty(elecDataSavedSiteIdList)) {
            return RestUtils.buildListResponse(new ArrayList<>(), 0L);
        }

        // 再去meter表看查出来的这些场站里有没有电表,meter表里有的才展示，没有的不展示
        MeterListParam meterListParam = new MeterListParam();
        meterListParam.setSiteIdList(elecDataSavedSiteIdList);
        meterListParam.setStart(0L);
        meterListParam.setIdChain(param.getIdChain());
        meterListParam.setSize(1000);
        ListResponse<SiteMeterVo> siteMeterVoListResponse = meterFeignClient.getSiteMeterList(
            meterListParam);

        if (siteMeterVoListResponse == null || CollectionUtils.isEmpty(
            siteMeterVoListResponse.getData())) {
            return RestUtils.buildListResponse(new ArrayList<>(), 0L);
        }

        List<String> siteIdList = siteMeterVoListResponse.getData().stream()
            .map(SiteMeterVo::getSiteId).collect(Collectors.toList());
        if (siteIdList.isEmpty()) {
            return RestUtils.buildListResponse(new ArrayList<>(), 0L);
        }
        listEssDailyParam.setSiteIdList(siteIdList);

        List<MeterDataVo> meterDataVoList = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

        // 处理dno和桩列表
        Map<String, List<MeterEvseVo>> siteIdAndMeterVoMap = siteMeterVoListResponse.getData()
            .stream()
            .collect(Collectors.toMap(
                SiteMeterVo::getSiteId,
                SiteMeterVo::getMeterEvseList,
                (existing, replacement) -> existing
            ));
        // 电表对应的桩列表map
        Map<String, List<String>> dnoAndEvseListMap = new HashMap<>();
        // 电表对应的场站idmap，防止桩中间换绑过场站，订单数据统计出错
        Map<String, String> dnoAndSiteIdMap = new HashMap<>();
        // 涉及到的所有桩编号数组
        List<String> evseNoList = new ArrayList<>();
        siteIdAndMeterVoMap.forEach((siteId, meterEvseVoList) -> {
            meterEvseVoList.forEach(meterEvseVo -> {
                List<String> savedDeviceIdList = new ArrayList<>();
                if (dnoAndEvseListMap.containsKey(meterEvseVo.getNo())) {
                    savedDeviceIdList = dnoAndEvseListMap.get(meterEvseVo.getNo());
                }
                List<String> deviceIdList = meterEvseVo.getDeviceMeterPoList().stream().map(
                    DeviceMeterPo::getDeviceId).toList();
                savedDeviceIdList.addAll(deviceIdList);
                evseNoList.addAll(savedDeviceIdList);
                dnoAndEvseListMap.put(meterEvseVo.getNo(), savedDeviceIdList);
                dnoAndSiteIdMap.put(meterEvseVo.getNo(), meterEvseVo.getSiteId());
            });
        });

        // 获取场站相关信息
        List<SiteVo> siteVoList = siteRoDs.getSiteVoBySiteIdList(siteIdList);
        // 场站id和场站信息map
        Map<String, SiteVo> siteIdAndSiteInfoMap = siteVoList.stream()
            .collect(Collectors.toMap(
                SiteVo::getId,
                siteVo -> siteVo,
                (existing, replacement) -> existing
            ));

        // 电表列表
        List<String> dnoList = siteMeterVoListResponse.getData().stream()
            .flatMap(siteMeterVo -> Optional.ofNullable(siteMeterVo.getMeterEvseList()).stream()
                .flatMap(Collection::stream))
            .map(MeterPo::getNo)
            .toList();

        // 查询电表抄表数据
        listEssDailyParam.setSiteIdList(siteIdList);
        listEssDailyParam.setGidList(param.getGidList());
        listEssDailyParam.setDnos(dnoList);
        listEssDailyParam.setStart(param.getStart());
        listEssDailyParam.setSize(param.getSize());

        List<EssEquipTimelyPo> essEquipTimelyPoList = essEquipTimelyRoDs.getSiteMonthElecDataList(
            listEssDailyParam);

        // 需要拿到具体的siteIdList和dnoList，这时候再去统计比较准确，统计的都是场站有绑定电表的数据条数，统计distinct(siteId, startTime)
        Long totalCount = essEquipTimelyRoDs.getDistinctSiteAndMonthListCount(listEssDailyParam);

        // 查出来的电表数据按表号分组
        Map<String, List<EssEquipTimelyPo>> dnoAndElecDataMap = essEquipTimelyPoList.stream()
            .collect(Collectors.groupingBy(EssEquipTimelyPo::getDno));

        log.info("桩充电量开始查询");
        // 把涉及到的所有的桩对应的充电量先查出来，后边再按照电表包含的桩来统计电表上每个月的充电量
        List<EvseElecVo> evseElecVoList =
            CollectionUtils.isNotEmpty(evseNoList)
                ? chargerOrderRoDs.getElecBySiteAndEvseList(siteIdList, evseNoList, timeFilter)
                : new ArrayList<>();
        log.info("桩充电量完成查询");

        // 根据桩列表查询每个桩的装机功率，然后在使用的时候按照dno分组求和
        ListEvseParam evseParam = new ListEvseParam();
        evseParam.setEvseNoList(evseNoList);
        ListResponse<EvsePo> evsePoListResponse = deviceFeignClient.getEvseList(evseParam).block();
        // 桩编号和功率map
        Map<String, Integer> evseNoAndPowerMap = new HashMap<>();
        if (evsePoListResponse != null && evsePoListResponse.getData() != null) {
            Map<String, Integer> evseNoAndPowerMapTemp = evsePoListResponse.getData().stream()
                .collect(Collectors.toMap(
                    EvsePo::getEvseId,
                    evsePo -> Optional.ofNullable(evsePo.getPower()).orElse(0),
                    (existingValue, newValue) -> existingValue
                ));
            if (!evseNoAndPowerMapTemp.isEmpty()) {
                evseNoAndPowerMap.putAll(evseNoAndPowerMapTemp);
            }
        }

        // 预先处理省、市、区信息
        // 处理每个电表的尖峰平谷数据
        dnoAndElecDataMap.forEach((dno, elecDataList) -> {
            // 每个电表里（elecDataList）又有多个时间段的，先按时间段分组，然后一个时间段是一个MeterDataVo
            // 按 startTime 分组
            Map<LocalDateTime, List<EssEquipTimelyPo>> monthElecDataMap = elecDataList.stream()
                .collect(Collectors.groupingBy(EssEquipTimelyPo::getStartTime));

            // 统计这个电表对应的桩上的订单量
            Map<String, BigDecimal> tsAndElecDataMap = new HashMap<>();
            // 先拿到指定dno对应的桩列表
            List<String> filterdEvseNoList = dnoAndEvseListMap.getOrDefault(dno,
                Collections.emptyList());
            Set<String> filterdEvseNoSet = new HashSet<>(filterdEvseNoList);
            // 再筛选出指定dno对应的桩列表的充电量统计
            List<EvseElecVo> filteredEvseElecList = evseElecVoList.stream()
                .filter(vo -> filterdEvseNoSet.contains(vo.getEvseNo())
                    // 桩换绑过场站，会导致订单统计出错，因此加上场站id筛选条件
                    && dnoAndSiteIdMap.get(dno) != null && dnoAndSiteIdMap.get(dno)
                    .equals(vo.getSiteId()))
                .toList();
            // 计算dno对应的桩列表的功率之和
            BigDecimal totalPower = Optional.ofNullable(filterdEvseNoSet)
                .orElseGet(Collections::emptySet)
                .stream()
                .filter(evseNoAndPowerMap::containsKey)
                .map(evseNo -> BigDecimal.valueOf(evseNoAndPowerMap.getOrDefault(evseNo, 0)))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 然后按照monthDate分组并汇总electricity
            Map<String, List<EvseElecVo>> groupedElecByMonthDate = filteredEvseElecList.stream()
                .collect(Collectors.groupingBy(EvseElecVo::getMonthDate));
            groupedElecByMonthDate.forEach((monthDate, voList) -> {
                BigDecimal totalElectricity = voList.stream()
                    .map(EvseElecVo::getElectricity)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                tsAndElecDataMap.put(monthDate, totalElectricity);
            });

            // 填充要返回给前端的数据
            monthElecDataMap.forEach((ts, monthElecDataList) -> {
                String monthDate = ts.format(formatter);
                MeterDataVo meterDataVo = new MeterDataVo();
                // 总
                EssEquipTimelyPo essEquipTimelyAllPo = monthElecDataList.stream()
                    .filter(timelyPo -> timelyPo.getPiName().equals("总"))
                    .findFirst().orElse(new EssEquipTimelyPo());

                // 尖时
                EssEquipTimelyPo essEquipTimelySharpPeakPo = monthElecDataList.stream()
                    .filter(timelyPo -> timelyPo.getPiName().equals("尖时"))
                    .findFirst().orElse(new EssEquipTimelyPo());

                // 峰时
                EssEquipTimelyPo essEquipTimelyPeakPo = monthElecDataList.stream()
                    .filter(timelyPo -> timelyPo.getPiName().equals("峰时"))
                    .findFirst().orElse(new EssEquipTimelyPo());

                // 平时
                EssEquipTimelyPo essEquipTimelyOffPeakPo = monthElecDataList.stream()
                    .filter(timelyPo -> timelyPo.getPiName().equals("平时"))
                    .findFirst().orElse(new EssEquipTimelyPo());

                // 谷时
                EssEquipTimelyPo essEquipTimelyValleyPo = monthElecDataList.stream()
                    .filter(timelyPo -> timelyPo.getPiName().equals("谷时"))
                    .findFirst().orElse(new EssEquipTimelyPo());

                // 深谷
                EssEquipTimelyPo essEquipTimelyDeepValleyPo = monthElecDataList.stream()
                    .filter(timelyPo -> timelyPo.getPiName().equals("深谷"))
                    .findFirst().orElse(new EssEquipTimelyPo());

                SiteVo siteVo =
                    siteIdAndSiteInfoMap.get(essEquipTimelyAllPo.getSiteId()) != null
                        ? siteIdAndSiteInfoMap.get(essEquipTimelyAllPo.getSiteId())
                        : new SiteVo();

                BigDecimal orderElec = tsAndElecDataMap.getOrDefault(monthDate,
                    BigDecimal.valueOf(0.0));

                meterDataVo.setSiteId(essEquipTimelyAllPo.getSiteId())
                    .setSiteName(siteVo.getSiteName())
                    .setSiteNo(siteVo.getSiteNo())
                    .setProvince(siteVo.getProvince())
                    .setProvinceName(siteVo.getProvinceName())
                    .setCity(siteVo.getCity())
                    .setCityName(siteVo.getCityName())
                    .setDistrict(siteVo.getArea())
                    .setDistrictName(siteVo.getAreaName())
                    // 电表对应的桩上的装机功率之和
                    .setTotalPower(totalPower)
                    .setMonthDate(monthDate)
                    .setDno(dno)
                    // 电表和桩有绑定关系，去统计桩上的
                    .setOrderElec(orderElec)
                    .setMeterElec(essEquipTimelyAllPo.getInKwh())
                    .setSharpPeakElec(essEquipTimelySharpPeakPo.getInKwh())
                    .setPeakElec(essEquipTimelyPeakPo.getInKwh())
                    .setOffPeakElec(essEquipTimelyOffPeakPo.getInKwh())
                    .setValleyElec(essEquipTimelyValleyPo.getInKwh())
                    .setDeepValleyElec(essEquipTimelyDeepValleyPo.getInKwh())
                    .setStartTotalReadingElec(essEquipTimelyAllPo.getTotalInKwh())
                    .setEndTotalReadingElec(
                        essEquipTimelyAllPo.getTotalInKwh() != null
                            ? essEquipTimelyAllPo.getTotalInKwh()
                            .add(essEquipTimelyAllPo.getInKwh()) : essEquipTimelyAllPo.getInKwh())
                    // 电损计算，(抄表电量-订单电量)/抄表电量
                    .setDiscount(essEquipTimelyAllPo.getInKwh().compareTo(BigDecimal.ZERO) != 0
                        ? (essEquipTimelyAllPo.getInKwh().subtract(orderElec)).divide(
                        essEquipTimelyAllPo.getInKwh(), 3, RoundingMode.HALF_UP)
                        : BigDecimal.ZERO)
                ;
                meterDataVoList.add(meterDataVo);
            });

        });

        return RestUtils.buildListResponse(meterDataVoList, totalCount);
    }
}
