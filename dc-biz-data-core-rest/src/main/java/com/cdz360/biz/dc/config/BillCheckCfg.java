package com.cdz360.biz.dc.config;

import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 需要对账的商户
 * 微信：特指普通商户
 * 支付宝：TODO
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "bill")
public class BillCheckCfg {

    private List<CommInfo> ordinaryCommList;

    @Data
    public static class CommInfo {
        private Long topCommId;
        private String name;//直付商家名
    }

}
