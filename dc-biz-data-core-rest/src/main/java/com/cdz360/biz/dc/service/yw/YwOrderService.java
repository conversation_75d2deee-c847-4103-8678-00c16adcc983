package com.cdz360.biz.dc.service.yw;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.param.SortParam;
import com.cdz360.base.model.base.type.EvseBizStatus;
import com.cdz360.base.model.base.type.OrderType;
import com.cdz360.base.model.base.type.UserType;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.auth.user.po.SysUserPo;
import com.cdz360.biz.dc.client.reactor.ReactorAuthCenterFeignClient;
import com.cdz360.biz.dc.client.reactor.ReactorDeviceMonitorFeignClient;
import com.cdz360.biz.dc.client.reactor.ReactorOpenHlhtFeignClient;
import com.cdz360.biz.dc.service.MsgSendService;
import com.cdz360.biz.dc.service.site.SiteBizService;
import com.cdz360.biz.dc.service.siteGroup.SiteGroupService;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteGroupRoDs;
import com.cdz360.biz.ds.trading.ro.yw.ds.SiteInspectionRecordRoDs;
import com.cdz360.biz.ds.trading.ro.yw.ds.YwOrderLogRoDs;
import com.cdz360.biz.ds.trading.ro.yw.ds.YwOrderRoDs;
import com.cdz360.biz.ds.trading.rw.yw.ds.YwOrderLogRwDs;
import com.cdz360.biz.ds.trading.rw.yw.ds.YwOrderRwDs;
import com.cdz360.biz.ess.model.param.ListEssParam;
import com.cdz360.biz.model.FileItem;
import com.cdz360.biz.model.OldOrderImagesStruct;
import com.cdz360.biz.model.iot.param.EvseTinyParam;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.iot.param.ListGtiParam;
import com.cdz360.biz.model.iot.vo.GtiVo;
import com.cdz360.biz.model.parts.param.PartsYwOrderRefParam;
import com.cdz360.biz.model.parts.param.PartsYwOrderRefParam.AssociationOp;
import com.cdz360.biz.model.parts.param.PartsYwOrderRefParam.AssociationOpType;
import com.cdz360.biz.model.site.type.AlarmStatusEnum;
import com.cdz360.biz.model.sys.param.GetYwUserParam;
import com.cdz360.biz.model.sys.param.SiteGroupSiteParam;
import com.cdz360.biz.model.sys.vo.AccRelativeOrderVo;
import com.cdz360.biz.model.trading.bi.param.UpdateWarningYwParam;
import com.cdz360.biz.model.trading.bi.param.WarningBiParam;
import com.cdz360.biz.model.trading.ess.vo.EssVo;
import com.cdz360.biz.model.trading.iot.dto.EvseTinyDto;
import com.cdz360.biz.model.trading.iot.po.EvsePo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import com.cdz360.biz.model.trading.yw.dto.ChargingModule;
import com.cdz360.biz.model.trading.yw.dto.Goods;
import com.cdz360.biz.model.trading.yw.dto.GoodsInfo;
import com.cdz360.biz.model.trading.yw.dto.GoodsInfo.OtherDevice;
import com.cdz360.biz.model.trading.yw.param.CreateYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.CusRecNoticeParam;
import com.cdz360.biz.model.trading.yw.param.ListYwOrderLogParam;
import com.cdz360.biz.model.trading.yw.param.ListYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.ReplaceDeviceParam;
import com.cdz360.biz.model.trading.yw.param.SolvedYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.TransYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.UpdateYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.UpdateYwOrderStatusParam;
import com.cdz360.biz.model.trading.yw.po.YwOrderLogPo;
import com.cdz360.biz.model.trading.yw.po.YwOrderPo;
import com.cdz360.biz.model.trading.yw.type.CreateYwOrderSourceType;
import com.cdz360.biz.model.trading.yw.type.YwOrderLevel;
import com.cdz360.biz.model.trading.yw.type.YwOrderOpType;
import com.cdz360.biz.model.trading.yw.type.YwOrderStatus;
import com.cdz360.biz.model.trading.yw.type.YwOrderTag;
import com.cdz360.biz.model.trading.yw.type.YwType;
import com.cdz360.biz.model.trading.yw.vo.YwEvseVo;
import com.cdz360.biz.model.trading.yw.vo.YwOrderBi;
import com.cdz360.biz.model.trading.yw.vo.YwOrderLogVo;
import com.cdz360.biz.model.trading.yw.vo.YwOrderVo;
import com.cdz360.biz.model.yw.param.BatchCreateYwParams;
import com.cdz360.biz.utils.feign.auth.AuthCommFeignClient;
import com.cdz360.biz.utils.feign.iot.DeviceFeignClient;
import com.cdz360.biz.utils.feign.iot.DevicePartsFeignClient;
import com.cdz360.biz.utils.feign.iot.GtiFeignClient;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.framework.common.domain.WWarningRecord;
import com.chargerlinkcar.framework.common.domain.param.CorpWxSendMsgCommonParam;
import com.chargerlinkcar.framework.common.domain.param.CorpWxSendMsgKV;
import com.chargerlinkcar.framework.common.feign.AuthCenterFeignClient;
import com.chargerlinkcar.framework.common.feign.DeviceMonitorFeignClient;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.feign.OpenHlhtFeignClient;
import com.chargerlinkcar.framework.common.service.RedisNoGen;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.ExcelFieldFormatter;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeSet;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class YwOrderService {

    private static final List<YwOrderStatus> CAN_SOLVED = List.of(YwOrderStatus.PROCESSING,
        YwOrderStatus.WAIT_CHECK, YwOrderStatus.SOLVED);
    private static final List<YwOrderStatus> CHECK_OP = List.of(YwOrderStatus.NO_PASS,
        YwOrderStatus.SOLVED);
    private static final List<YwOrderStatus> CAN_DELETED = List.of(YwOrderStatus.INIT,
        YwOrderStatus.RECEIVED, YwOrderStatus.PROCESSING, YwOrderStatus.TRANSFERRING,
        YwOrderStatus.WAIT_CHECK, YwOrderStatus.SUSPEND);

    @Autowired
    private RedisNoGen redisNoGen;

    @Autowired
    private RedisIotReadService redisIotReadService;

    @Autowired
    private SiteInspectionRecordRoDs siteInspectionRecordRoDs;
    @Autowired
    private YwOrderRoDs ywOrderRoDs;
    @Autowired
    private SiteGroupService siteGroupService;

    @Autowired
    private YwOrderRwDs ywOrderRwDs;

    @Autowired
    private YwOrderLogRwDs ywOrderLogRwDs;

    @Autowired
    private YwOrderLogRoDs ywOrderLogRoDs;

    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;

    @Autowired
    private MsgSendService msgSendService;

    @Autowired
    private DeviceFeignClient deviceFeignClient;

    @Autowired
    private GtiFeignClient gtiFeignClient;

    @Autowired
    private DevicePartsFeignClient devicePartsFeignClient;

    @Autowired
    private SiteBizService siteBizService;

    @Autowired
    private SiteGroupRoDs siteGroupRoDs;

    @Autowired
    private DeviceMonitorFeignClient deviceMonitorFeignClient;

    @Autowired
    private AuthCommFeignClient authCommFeignClient;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private OpenHlhtFeignClient openHlhtFeignClient;

    @Autowired
    private ReactorDeviceMonitorFeignClient reactorDeviceMonitorFeignClient;

    @Autowired
    private ReactorAuthCenterFeignClient reactorAuthCenterFeignClient;

    @Autowired
    private ReactorOpenHlhtFeignClient reactorOpenHlhtFeignClient;


    public Mono<YwOrderVo> updateYwOrderTag(UpdateYwOrderParam param) {
        IotAssert.isNotBlank(param.getYwOrderNo(), "运维工单编号无效");

        YwOrderVo ywOrder = ywOrderRoDs.getByYwOrderNo(param.getYwOrderNo());
        IotAssert.isNotNull(ywOrder, "运维工单编号无效");

        ywOrder.setTag(param.getTag()).setRemark(param.getRemark());
        return Mono.just(new YwOrderPo().setYwOrderNo(param.getYwOrderNo()).setTag(param.getTag())
            .setRemark(param.getRemark())).map(ywOrderRwDs::updateYwOrderTag).map(x -> ywOrder);
    }

    public Mono<ListResponse<YwOrderVo>> findYwOrder(ListYwOrderParam param) {
        AtomicReference<Map<String, List<String>>> siteGidMapRef = new AtomicReference<>(
            new HashMap<>());
        AtomicReference<Map<String, String>> groupMapRef = new AtomicReference<>(new HashMap<>());

        return Mono.just(param).doOnNext(p -> {
            if (null == param.getSize() || param.getSize() > 10000) {
                param.setSize(9999);
            }
        }).map(this.ywOrderRoDs::findAll).flatMap(ywOrderVoList -> {
            List<String> siteIdList = ywOrderVoList.stream().map(YwOrderVo::getSiteId)
                .collect(Collectors.toList());
            return Mono.just(siteIdList).flatMap(e -> siteGroupService.getSiteGroupMapVo(e))
                .map(mapVoOptional -> {
                    mapVoOptional.ifPresent(t -> {
                        siteGidMapRef.set(t.getSiteGidMap());
                        groupMapRef.set(t.getGroupMap());
                    });
                    return ywOrderVoList;
                });
        }).flatMap(list -> {
            return Mono.just(list).map(
                    t -> list.stream().filter(e -> CollectionUtils.isNotEmpty(e.getEvseNoList()))
                        .flatMap(e -> e.getEvseNoList().stream()).distinct()
                        .collect(Collectors.toList())).filter(CollectionUtils::isNotEmpty)
                .flatMap(e -> deviceFeignClient.getEvseList(new ListEvseParam().setEvseNoList(e)))
                .doOnNext(FeignResponseValidate::check).map(e -> Optional.of(
                    e.getData().stream().collect(Collectors.toMap(EvsePo::getEvseId, o -> o))))
                .switchIfEmpty(Mono.just(Optional.empty())).map(evseMapOpt -> {
                    return list.stream().peek(vo -> {

                        // 返回场站运维组信息
                        siteGidMapRef.get().getOrDefault(vo.getSiteId(), List.of()).stream()
                            .filter(Objects::nonNull)
                            .filter(t -> StringUtils.isNotBlank(groupMapRef.get().get(t)))
                            .findFirst().ifPresent(s -> {
                                vo.setSiteGid(s);
                                vo.setSiteGidName(groupMapRef.get().get(s));
                            });

                        vo.setYwDurationStr(
                            ExcelFieldFormatter.ywDurationConvert(vo.getYwDuration()));

                        if (CollectionUtils.isNotEmpty(vo.getEvseNoList())
                            && evseMapOpt.isPresent()) {
                            vo.setEvseVoList(vo.getEvseNoList().stream()
                                .map(t -> evseMapOpt.get().getOrDefault(t, null))
                                .filter(Objects::nonNull).map(t -> {
                                    YwEvseVo yev = new YwEvseVo();
                                    yev.setEvseId(t.getEvseId()).setName(t.getName())
                                        .setPhysicalNo(t.getPhysicalNo()).setSupply(t.getSupply())
                                        .setModel(t.getModel()).setPower(t.getPower())
                                        .setProduceDate(t.getProduceDate())
                                        .setExpireDate(t.getExpireDate());
                                    return yev;
                                }).collect(Collectors.toList()));
                        }
                    }).collect(Collectors.toList());
                });
        }).map(RestUtils::buildListResponse).doOnNext(res -> {
            if (null != param.getTotal() && param.getTotal()) {
                res.setTotal(this.ywOrderRoDs.count(param));
            } else {
                res.setTotal(0L);
            }
        });
    }

    public Mono<List<YwOrderBi>> ywOrderBi(ListYwOrderParam param) {
        return Mono.just(param).map(this.ywOrderRoDs::ywOrderBi);
    }

    private List<YwOrderVo> map2Vo(List<YwOrderPo> poList) {
        return poList.stream().map(this::map2Vo).collect(Collectors.toList());
    }

    private YwOrderVo map2Vo(YwOrderPo po) {
        YwOrderVo vo = new YwOrderVo();
        BeanUtils.copyProperties(po, vo);

        // 桩名称列表
        List<EvseVo> evseList = redisIotReadService.getEvseList(po.getEvseNoList());
        vo.setEvseNameList(evseList.stream().map(
            evse -> StringUtils.isBlank(evse.getName()) ? evse.getEvseNo()
                : evse.getName() + " " + evse.getEvseNo()).collect(Collectors.toList()));
        return vo;
    }

    public Mono<YwOrderVo> getYwOrderDetail(String ywOrderNo) {
        if (StringUtils.isBlank(ywOrderNo)) {
            throw new DcArgumentException("运维工单编号无效");
        }

        return Mono.just(ywOrderNo).map(ywOrderRoDs::getByYwOrderNo).doOnNext(vo -> {
            if (null == vo) {
                throw new DcArgumentException("运维工单编号无效");
            }

            // 桩名称列表
            if (CollectionUtils.isNotEmpty(vo.getEvseNoList())) {
                List<EvseVo> evseList = redisIotReadService.getEvseList(vo.getEvseNoList());
                vo.setEvseNameList(evseList.stream().map(
                    evse -> StringUtils.isBlank(evse.getName()) ? evse.getEvseNo()
                        : evse.getName() + " " + evse.getEvseNo()).collect(Collectors.toList()));
            }
        }).doOnNext(vo -> {
            if (vo.getYwType() == YwType.PV.getCode()) {
                ListGtiParam param = new ListGtiParam();
                param.setSiteId(vo.getSiteId());
                param.setTotal(true);
                ListResponse<GtiVo> listResponse = gtiFeignClient.findGtiList(param)
                    .block(Duration.ofSeconds(50L));
                FeignResponseValidate.checkIgnoreData(listResponse);
                List<GtiVo> gtiVoList = listResponse.getData();
                if (CollectionUtils.isNotEmpty(gtiVoList)) {
                    List<String> vendorList = new ArrayList<>();
                    gtiVoList.forEach(e -> {
                        if (!vendorList.contains(e.getVendor().getDesc())) {
                            e.getVendor().getDesc();
                        }
                    });
                    if (CollectionUtils.isNotEmpty(vendorList)) {
                        AtomicReference<String> pvGwVendorName = new AtomicReference<>("");
                        vendorList.forEach(e -> {
                            pvGwVendorName.set(pvGwVendorName + e + ",");
                        });
                        vo.setPvGwVendorName(
                            pvGwVendorName.get().substring(0, pvGwVendorName.get().length() - 1));
                    } else {
                        vo.setPvGwVendorName("--");
                    }
                } else {
                    vo.setPvGwVendorName("--");
                }
            }
            if (vo.getYwType() == YwType.ESS.getCode()) {
                ListEssParam param = new ListEssParam();
                param.setSiteIdList(List.of(vo.getSiteId()));
                param.setTotal(true);
                ListResponse<EssVo> listResponse = deviceFeignClient.findEssList(param)
                    .block(Duration.ofSeconds(50L));
                FeignResponseValidate.checkIgnoreData(listResponse);
                List<EssVo> essVoList = listResponse.getData();
                if (CollectionUtils.isNotEmpty(essVoList)) {
                    vo.setEssNum(essVoList.size());
                } else {
                    vo.setEssNum(0);
                }
            }
        });
//                .map(this::map2Vo);
    }

    @Transactional
    public Mono<YwOrderPo> createYwOrder(CreateYwOrderParam param) {
        if (param.getYwType() == null) {
            param.setYwType(YwType.CHARGE.getCode());
        }
        return Mono.just(param)
//                .doOnNext(CreateYwOrderParam::checkParam)
            .map(p -> new YwOrderPo().setYwOrderNo(redisNoGen.ywOrderNo()).setSiteId(p.getSiteId())
                .setOrderStatus(YwOrderStatus.INIT).setOrderNo(p.getOrderNo())
                .setEvseNoList(p.getEvseNoList()).setOverExpireDate(p.getOverExpireDate())
                .setFaultImages(p.getFaultImages()).setFaultDesc(p.getFaultDesc())
                .setFaultLevel(p.getFaultLevel()).setSourceType(p.getSourceType())
                .setTag(p.getTag() != null ? p.getTag() : YwOrderTag.ASSESS)

                .setMaintName(p.getMaintName()).setMaintType(p.getMaintType())
                .setMaintUid(p.getMaintUid())

                .setCreateOpName(p.getOpName()).setCreateOpUid(p.getOpUid())
                .setCreateOpType(p.getOpType()).setUpdateOpName(p.getOpName())
                .setUpdateOpType(p.getOpType()).setUpdateOpUid(p.getOpUid())

                .setYwType(p.getYwType()).setCarInfo(p.carInfo()))
            .doOnNext(ywOrderRwDs::upsetYwOrder).doOnNext(po -> {
                if (po.getYwType() == YwType.CHARGE.getCode()) {//只有充推送 光储不进行推送
                    msgSendService.sendWxOperationMsg(po, param.getEvseStrList());
                }
            }).flatMap(po -> {
                // 告警码转运维工单
                if (CollectionUtils.isNotEmpty(param.getIgnoreWarningCodeList())) {
                    UpdateWarningYwParam updateWarningYwParam = new UpdateWarningYwParam();
                    updateWarningYwParam.setEvseNoList(po.getEvseNoList())
                        .setIgnoreWarningCodeList(param.getIgnoreWarningCodeList())
                        .setYwOrderNo(po.getYwOrderNo());
                    return reactorDeviceMonitorFeignClient.updateYwOrderByEvseList(updateWarningYwParam).map(x-> po);
                }
                return Mono.just(po);
            }).doOnNext(po -> {
                YwOrderLogPo logPo = new YwOrderLogPo();
                logPo.setType(YwOrderOpType.YW_ORDER_CREATE).setOpUserName(po.getCreateOpName())
                    .setOpUserType(po.getCreateOpType()).setOpUid(po.getCreateOpUid())
                    .setOrderStatus(po.getOrderStatus()).setYwOrderNo(po.getYwOrderNo());
                ywOrderLogRwDs.insertYwOrderLog(logPo);
            }).flatMap(this::sendCorpWxMsg);
    }

    /**
     * 运维工单发送企业微信消息
     */
    public Mono<YwOrderPo> sendCorpWxMsg(YwOrderPo po) {
        if (po == null || po.getCreateOpUid().equals(po.getMaintUid())) { // 创建人、运维人相同不推送
            return Mono.just(po);
        }
        log.info("运维工单，企业微信消息发送,ywOrderNo:{};maintUid:{}", po.getYwOrderNo(),
            po.getMaintUid());
        return reactorAuthCenterFeignClient.getSysUserById(po.getMaintUid())
            .doOnNext(FeignResponseValidate::checkIgnoreData)
            .filter(x -> x.getData() != null)
            .map(ObjectResponse::getData)
            .map(SysUserPo::getCorpWxUid)
            .flatMap(corpWxUid -> {
                if (StringUtils.isEmpty(corpWxUid)) {
                    return Mono.just(po);
                }
                log.info("corpWxUid=>{}", corpWxUid);
                SitePo sitePo = siteBizService.getSiteById(po.getSiteId());

                CorpWxSendMsgCommonParam params = new CorpWxSendMsgCommonParam();
                params.setTitle("维修工单").setToUser(corpWxUid)
                    .setPage("packageYw/pages/operations/operationsList/operationsList");

                List<CorpWxSendMsgKV> contentItems = new ArrayList<>();
                contentItems.add(
                    new CorpWxSendMsgKV().setKey("工单号").setValue(po.getYwOrderNo()));
                contentItems.add(
                    new CorpWxSendMsgKV().setKey("场站名称").setValue(sitePo.getSiteName()));
                contentItems.add(
                    new CorpWxSendMsgKV().setKey("故障描述").setValue(po.getFaultDesc()));
                contentItems.add(new CorpWxSendMsgKV().setKey("工单来源")
                    .setValue(po.getSourceType().getDesc()));
                params.setContentItems(contentItems);

                log.info("运维工单发送企业微信消息，content：{}", JsonUtils.toJsonString(params));
                return reactorOpenHlhtFeignClient.sendMsgCommon(sitePo.getTopCommId(), params)
                    .onErrorResume(e -> {
                        log.error("发送企业微信消息失败", e);
                        return Mono.empty();
                    }).map(xx -> po);
            });
    }

    public ObjectResponse<Boolean> existUnfinishedOrder(List<Long> sysUidList) {
        ListYwOrderParam param = new ListYwOrderParam();
        param.setMaintUidList(sysUidList).setOrderStatusList(
            List.of(YwOrderStatus.NO_PASS, YwOrderStatus.INIT, YwOrderStatus.RECEIVED));
        Long count = ywOrderRoDs.count(param);
        return new ObjectResponse<>(count > 0);
    }

    public Mono<YwOrderVo> transYwOrder(TransYwOrderParam param) {
        return Mono.just(param).doOnNext(p -> {
            if (StringUtils.isBlank(p.getYwOrderNo())) {
                throw new DcArgumentException("运维工单编号不能为空");
            }

            if (null == p.getUid()) {
                throw new DcArgumentException("请提供有效的用户Id");
            }

            if (StringUtils.isBlank(p.getUserName())) {
                throw new DcArgumentException("用户名称不能为空");
            }

            YwOrderPo po = ywOrderRwDs.getByYwOrderNo(p.getYwOrderNo(), true);
            if (null == po) {
                throw new DcArgumentException("运维工单编号无效");
            }

//                    if (po.getMaintUid() != null && !po.getMaintUid().equals(p.getOpUid())) {
//                        throw new DcArgumentException("不能操作不属于自己的运维工单");
//                    }

            po.setOrderStatus(YwOrderStatus.TRANSFERRING).setMaintUid(p.getUid())
                .setMaintType(p.getUserType()).setMaintName(p.getUserName())
                .setUpdateOpUid(p.getOpUid()).setUpdateOpName(p.getOpName())
                .setUpdateOpType(p.getOpType());
            ywOrderRwDs.upsetYwOrder(po);

            YwOrderLogPo logPo = new YwOrderLogPo();
            logPo.setType(YwOrderOpType.YW_ORDER_TRANSFER).setTargetUid(p.getUid())
                .setTargetUserName(p.getUserName()).setTargetUserType(p.getUserType())
                .setOpUserName(p.getOpName()).setOpUserType(p.getOpType()).setOpUid(p.getOpUid())
                .setOrderStatus(po.getOrderStatus()).setYwOrderNo(p.getYwOrderNo());
            ywOrderLogRwDs.insertYwOrderLog(logPo);
        }).map(p -> ywOrderRoDs.getByYwOrderNo(p.getYwOrderNo())).doOnNext(p -> {
            // 推送企业消息
            YwOrderPo ywOrderPo = new YwOrderPo();
            BeanUtils.copyProperties(p, ywOrderPo);
            this.sendCorpWxMsg(ywOrderPo);
        });
    }

    @Transactional
    public Mono<BaseResponse> updateOrderStatus(UpdateYwOrderStatusParam param) {
        return Mono.just(param).doOnNext(p -> {
            if (CollectionUtils.isEmpty(p.getYwOrderNoList())) {
                throw new DcArgumentException("运维工单编号不能为空");
            }

            if (null == p.getOrderStatus()) {
                throw new DcArgumentException("状态没有指定");
            }

            List<YwOrderPo> poList = ywOrderRoDs.getByYwOrderNoList(p.getYwOrderNoList());
            if (CollectionUtils.isEmpty(poList) || poList.size() != p.getYwOrderNoList().size()) {
                throw new DcArgumentException("运维工单编号无效");
            }

            poList.forEach(po -> {

                if (CHECK_OP.contains(p.getOrderStatus())) {
                    log.info("质检操作: ywOrderNo = {}", po.getYwOrderNo());
                    po.setQcName(p.getOpName()).setQcUid(p.getOpUid()).setQcType(p.getOpType())
                        .setQcTime(new Date());

                    // 为保证擦除上次质检的备注
                    if (StringUtils.isNotBlank(p.getQcRemark())) {
                        po.setQcRemark(p.getQcRemark());
                    } else if (StringUtils.isBlank(p.getQcRemark()) && StringUtils.isNotBlank(
                        po.getQcRemark())) {
                        po.setQcRemark("");
                    }

                    po.setYwDuration(this.calcYwDuration(po.getYwOrderNo(), po.getCreateTime(),
                        po.getMaintTime()));
                    po.setScore(
                        YwOrderStatus.SOLVED.equals(p.getOrderStatus()) ? p.getScore() : null);

                } else if (YwOrderStatus.DELETED.equals(p.getOrderStatus())) {
                    log.info("删除操作: ywOrderNo = {}", po.getYwOrderNo());
                    IotAssert.isTrue(po.getCreateOpUid().equals(p.getOpUid()), "您无权删除此工单");
                    IotAssert.isTrue(CAN_DELETED.contains(po.getOrderStatus()),
                        "当前工单状态不支持删除");
                }
                po.setOrderStatus(p.getOrderStatus()).setUpdateOpUid(p.getOpUid())
                    .setUpdateOpName(p.getOpName()).setUpdateOpType(p.getOpType());
                ywOrderRwDs.upsetYwOrder(po);

                YwOrderLogPo logPo = new YwOrderLogPo();
                logPo.setType(p.getType()).setOpUserName(p.getOpName()).setOpUserType(p.getOpType())
                    .setOpUid(p.getOpUid()).setOrderStatus(po.getOrderStatus())
                    .setYwOrderNo(po.getYwOrderNo());
                ywOrderLogRwDs.insertYwOrderLog(logPo);
            });
        }).map(p -> RestUtils.success());
    }

    /**
     * 计算运维时长（秒）
     *
     * @param ywOrderNo
     * @param ywCreateTime 工单创建时间
     * @param ywMaintTime  运维人处理时间
     * @return
     */
    public Long calcYwDuration(String ywOrderNo, Date ywCreateTime, Date ywMaintTime) {
        ListYwOrderLogParam param = new ListYwOrderLogParam();
        param.setYwOrderNoList(List.of(ywOrderNo));
        param.setSorts(List.of(SortParam.as("opTime", OrderType.desc))); // 按操作时间倒序
        List<YwOrderLogPo> list = ywOrderLogRoDs.findYwOrderLog(param);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }

        AtomicLong ywDuration = new AtomicLong(0); // 运维时长
        final long suspendFrequency = list.stream()
            .filter(e -> YwOrderStatus.SUSPEND == e.getOrderStatus()).count(); // 工单挂起总次数
        if (suspendFrequency == 0) {
            DateUtil.calcDuration(ywCreateTime, ywMaintTime).ifPresent(d -> {
                ywDuration.addAndGet(d.toSeconds());
            });
            return ywDuration.get();
        }

        AtomicReference<YwOrderLogPo> last = new AtomicReference<>(
            new YwOrderLogPo().setOrderStatus(YwOrderStatus.WAIT_CHECK));
        AtomicLong suspendStep = new AtomicLong(suspendFrequency); // 第几次挂起

        for (YwOrderLogPo currLogPo : list) {
            if (YwOrderStatus.PROCESSING == currLogPo.getOrderStatus()
                && YwOrderStatus.WAIT_CHECK == last.get().getOrderStatus()) {

                DateUtil.calcDuration(currLogPo.getOpTime(), ywMaintTime).ifPresent(d -> {
                    ywDuration.addAndGet(d.toSeconds());
                });
                last.set(currLogPo);

            } else if (YwOrderStatus.SUSPEND == currLogPo.getOrderStatus()
                && YwOrderStatus.PROCESSING == last.get().getOrderStatus()) {

                if (suspendStep.getAndDecrement() == 1) {
                    DateUtil.calcDuration(ywCreateTime, currLogPo.getOpTime()).ifPresent(d -> {
                        ywDuration.addAndGet(d.toSeconds());
                    });
                    break;
                }
                last.set(currLogPo);

            } else if (YwOrderStatus.PROCESSING == currLogPo.getOrderStatus()
                && YwOrderStatus.SUSPEND == last.get().getOrderStatus()) {

                DateUtil.calcDuration(currLogPo.getOpTime(), last.get().getOpTime())
                    .ifPresent(d -> {
                        ywDuration.addAndGet(d.toSeconds());
                    });
                last.set(currLogPo);

            } else if (YwOrderStatus.PROCESSING == currLogPo.getOrderStatus()
                && YwOrderStatus.PROCESSING == last.get().getOrderStatus()) {

                DateUtil.calcDuration(currLogPo.getOpTime(), last.get().getOpTime())
                    .ifPresent(d -> {
                        ywDuration.addAndGet(d.toSeconds());
                    });
                last.set(currLogPo);

            } else {
                // nothing to do
            }
        }

        return ywDuration.get();
    }

//    public Mono<BaseResponse> receivedYwOrder(String ywOrderNo) {
//        return null;
//    }
//
//    public Mono<BaseResponse> startYwOrder(String ywOrderNo) {
//        return null;
//    }

    public Mono<YwOrderPo> saveYwOrder(SolvedYwOrderParam param) {
        return Mono.just(param).doOnNext(SolvedYwOrderParam::checkParam).map(p -> {
            YwOrderPo po = ywOrderRwDs.getByYwOrderNo(p.getYwOrderNo(), true);
            if (null == po) {
                throw new DcArgumentException("运维工单编号无效");
            }

            if (!CAN_SOLVED.contains(po.getOrderStatus())) {
                log.warn("当前工单状态不支持保存: po = {}", JsonUtils.toJsonString(po));
                throw new DcServiceException(
                    "当前工单状态为[" + po.getOrderStatus().getDesc() + "]，不支持保存");
            }

            po.setGoods(p.getGoods()).setRemote(p.getRemote()).setFaultReason(p.getFaultReason())
                .setCheckStep(p.getCheckStep()).setDealProcess(p.getDealProcess())
                .setImages(p.getImages()).setUpdateOpType(p.getOpType())
                .setUpdateOpName(p.getOpName()).setUpdateOpUid(p.getOpUid())
                .setIsPerfect(p.getIsPerfect()).setAdvice(p.getAdvice())
                .setSignImage(p.getSignImage());
            return po;
        }).doOnNext(ywOrderRwDs::upsetYwOrder).doOnNext(po -> {
            YwOrderLogPo logPo = new YwOrderLogPo();
            logPo.setType(YwOrderOpType.YW_ORDER_UPDATE_PROCESS).setOpUserName(param.getOpName())
                .setOpUserType(param.getOpType()).setOpUid(param.getOpUid())
                .setOrderStatus(po.getOrderStatus()).setYwOrderNo(po.getYwOrderNo());
            ywOrderLogRwDs.insertYwOrderLog(logPo);
        });
    }

    public Mono<YwOrderPo> solvedYwOrder(SolvedYwOrderParam param) {
        return Mono.just(param).doOnNext(SolvedYwOrderParam::checkParam).map(p -> {
            YwOrderPo po = ywOrderRwDs.getByYwOrderNo(p.getYwOrderNo(), true);
            if (null == po) {
                throw new DcArgumentException("运维工单编号无效");
            }

            if (!CAN_SOLVED.contains(po.getOrderStatus())) {
                log.warn("当前工单状态不支持更新: po = {}", JsonUtils.toJsonString(po));
                throw new DcServiceException(
                    "当前工单状态为[" + po.getOrderStatus().getDesc() + "]，不支持更新");
            }

            // 已完成的运维工单只修改 客户意见，签名
            if (YwOrderStatus.SOLVED.equals(po.getOrderStatus())) {
                po.setAdvice(p.getAdvice()).setIsPerfect(p.getIsPerfect()).setImages(p.getImages())
                    .setSignImage(p.getSignImage());
                return po;
            }
            po.setGoods(p.getGoods())
                .setOrderStatus(YwOrderStatus.WAIT_CHECK) // 提交后则认为待质检状态，不管内容是否完整性
                .setMaintTime(new Date()).setRemote(p.getRemote())
                .setFaultReason(p.getFaultReason()).setCheckStep(p.getCheckStep())
                .setDealProcess(p.getDealProcess()).setImages(p.getImages())
                .setUpdateOpType(p.getOpType()).setUpdateOpName(p.getOpName())
                .setUpdateOpUid(p.getOpUid()).setIsPerfect(p.getIsPerfect())
                .setAdvice(p.getAdvice()).setSignImage(p.getSignImage());
            return po;
        }).flatMap(e -> {
            // 已完成运维工单只修改客户意见以及签名
            if (YwOrderStatus.SOLVED.equals(e.getOrderStatus())) {
                return Mono.just(e);
            }
//                    if (e.getGoods() == null || CollectionUtils.isEmpty(e.getGoods().getChargingModuleList())) {
//                        return Mono.just(e);
//                    }

            if (CollectionUtils.isEmpty(e.getGoods())) {
                return Mono.just(e);
            }

            // 桩器件信息同步

            // 电源模块信息
            List<ChargingModule> chargingModuleList = e.getGoods().stream()
                .filter(i -> i.getTypeName().contains("电源模块")).map(i -> {
                    ChargingModule module = new ChargingModule();
                    module.setIdx(i.getIdx()).setEvseNo(i.getEvseNo()).setRollback(i.getRollback())
                        .setNewDeviceNo(i.getNewDeviceNo()).setOldDeviceNo(i.getOldDeviceNo());
                    return module;
                }).collect(Collectors.toList());
            // 其他模块信息
            List<OtherDevice> otherDeviceList = e.getGoods().stream()
                .filter(i -> !i.getTypeName().contains("电源模块")).map(i -> {
                    OtherDevice otherDevice = new OtherDevice();
                    otherDevice.setName(i.getTypeName());
                    return otherDevice;
                }).collect(Collectors.toList());

            ReplaceDeviceParam req = new ReplaceDeviceParam();
            req.setYwOrderNo(e.getYwOrderNo());
            GoodsInfo goodsInfo = new GoodsInfo();
            goodsInfo.setChargingModuleList(chargingModuleList);
            goodsInfo.setOtherDeviceList(otherDeviceList);
            req.setGoodsInfo(goodsInfo);
            BaseResponse response = iotDeviceMgmFeignClient.replaceDevice(req);
            FeignResponseValidate.check(response);

            // 判断是否需要同步物料管理信息
            // rollback   true  需要回滚   false 编辑后新增
            /**
             * 物料状态同步分三种情况，通过rollback区分
             * rollback == null   上一步保存信息，无需同步物料
             * rollback == false  新增内容  需要同步记录goods信息，同时同步物料
             * rollback == true   回滚操作   删除goods中内容，同时同步物料 反向操作
             */

            // 目前仅做新操作的物料同步

            PartsYwOrderRefParam refParam = new PartsYwOrderRefParam();
            refParam.setUid(e.getUpdateOpUid()).setUserName(e.getUpdateOpName())
                .setYwOrderNo(e.getYwOrderNo());
            List<AssociationOp> associationList = e.getGoods().stream()
                .filter(i -> Boolean.FALSE.equals(i.getRollback())).map(i -> {
                    AssociationOp vo = new AssociationOp();
                    vo.setOpType(i.getOpType()).setNewPartsCode(i.getNewPartsCode())
                        .setTypeId(i.getTypeId()).setRollback(i.getRollback())
                        .setTypeName(i.getTypeName());
                    return vo;
                }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(associationList)) {
                refParam.setAssociationOps(associationList);
                ObjectResponse<PartsYwOrderRefParam> ret = devicePartsFeignClient.partsYwOrderRef(
                    refParam).block(Duration.ofSeconds(50L));
                log.info("物料状态同步后返回,ret={}", JsonUtils.toJsonString(ret));
                FeignResponseValidate.check(ret);
            }

            // 去除回滚操作保存,不保存rollback
            List<Goods> tmpGoods = e.getGoods().stream()
                .filter(i -> !Boolean.TRUE.equals(i.getRollback())).peek(i -> i.setRollback(null))
                .collect(Collectors.toList());

            e.setGoods(tmpGoods);

//                    ReplaceDeviceParam req = new ReplaceDeviceParam();
//                    req.setYwOrderNo(e.getYwOrderNo());
//                    req.setGoodsInfo(e.getGoods());
//                    BaseResponse response = iotDeviceMgmFeignClient.replaceDevice(req);
//                    FeignResponseValidate.check(response);

//                List<Goods> temp = e.getGoods().stream()
//                    .filter(t -> t.getRollback() == null) // 若rollback为true，则此条数据t_yw_order不记录
//                    .peek(t -> t.setRollback(null)) // rollback字段无需入库
//                    .collect(Collectors.toList());
//                log.info("tmpGoods={},tmp={}",JsonUtils.toJsonString(tmpGoods),JsonUtils.toJsonString(temp));
//                if (CollectionUtils.isNotEmpty(temp)) {
//                    List<Goods> finalTmpGoods = tmpGoods;
//                    temp.stream().forEach(i -> finalTmpGoods.add(i));
//                    e.setGoods(finalTmpGoods.stream().peek(i->i.setRollback(null)).collect(
//                        Collectors.toList()));
//                } else {
//                    e.setGoods(tmpGoods.stream().peek(i->i.setRollback(null)).collect(Collectors.toList()));
//                }
            return Mono.just(e);
        }).doOnNext(ywOrderRwDs::upsetYwOrder).doOnNext(po -> {
            YwOrderLogPo logPo = new YwOrderLogPo();
            logPo.setType(YwOrderOpType.YW_ORDER_UPDATE_PROCESS).setOpUserName(param.getOpName())
                .setOpUserType(param.getOpType()).setOpUid(param.getOpUid())
                .setOrderStatus(po.getOrderStatus()).setYwOrderNo(po.getYwOrderNo());
            ywOrderLogRwDs.insertYwOrderLog(logPo);
        });
    }

    /**
     * 同步物料库后 拆除或更换  返回最新的物料ID
     *
     * @param rollbackGoods
     * @param associationOpList
     * @return
     */
    public List<Goods> getGoodsList(List<Goods> rollbackGoods,
        List<AssociationOp> associationOpList) {
        log.info("同步物料信息，rollbackGoods={},回传信息,assOpList={}",
            JsonUtils.toJsonString(rollbackGoods), JsonUtils.toJsonString(associationOpList));
        if (rollbackGoods.size() != associationOpList.size()) {
            return new ArrayList<>();
        }
        final int length = associationOpList.size();
        for (var i = 0; i < length; i++) {
            final AssociationOp op = associationOpList.get(i);
            if (Boolean.FALSE.equals(op.getRollback())) {
                rollbackGoods.get(i).setInNewCode(op.getInNewCode());
            }
        }
        return rollbackGoods.stream().filter(e -> Boolean.FALSE.equals(e.getRollback()))
            .collect(Collectors.toList());
    }

    public List<Goods> formatGoods(List<Goods> goodsList) {
        log.info("格式化同步物料信息,goods={}", JsonUtils.toJsonString(goodsList));
        List<Goods> goods = new ArrayList<>();
        if (CollectionUtils.isEmpty(goodsList)) {
            return goods;
        }
        goodsList.stream().forEach(e -> {
            if (Boolean.FALSE.equals(e.getRollback())) { // 新增的需要同步库存
                goods.add(e);
            } else if (AssociationOpType.INSTALL.equals(e.getOpType())) { // 添新回退，
                e.setOpType(AssociationOpType.UNINSTALL).setNewPartsCode(e.getNewPartsCode())
                    .setTypeName(e.getTypeName()).setRollback(e.getRollback())
                    .setTypeId(e.getTypeId());
                goods.add(e);
            } else if (AssociationOpType.UNINSTALL.equals(e.getOpType())) { // 移除回退
                e.setOpType(AssociationOpType.INSTALL).setNewFullModel(e.getOldFullModel())
                    .setTypeId(e.getTypeId()).setTypeName(e.getTypeName())
                    .setRollback(e.getRollback()).setNewPartsCode(
                        StringUtils.isNotEmpty(e.getNewPartsCode()) ? e.getNewPartsCode()
                            : e.getInNewCode()).setNewTypeCode(e.getOldTypeCode());
                goods.add(e);
            } else if (AssociationOpType.REPLACE.equals(e.getOpType())) { // 更换回退
                // 更换上去的器件，实现移除操作  让库存回归
                goods.add(new Goods().setOpType(AssociationOpType.UNINSTALL)
                    .setOldFullModel(e.getNewFullModel()).setOldTypeCode(e.getNewTypeCode())
                    .setNewPartsCode(e.getNewPartsCode()).setRollback(e.getRollback())
                    .setTypeId(e.getTypeId()).setTypeName(e.getTypeName()));
            }
        });
        log.info("回退信息,goods={}", JsonUtils.toJsonString(goods));
        return goods;
    }

    public Mono<List<YwOrderLogVo>> ywOrderTransList(String ywOrderNo) {
        // 获取运维工单转派记录
        return Mono.just(ywOrderNo).map(List::of).map(
            list -> new ListYwOrderLogParam().setTypeList(List.of(YwOrderOpType.YW_ORDER_TRANSFER))
                .setYwOrderNoList(list)).map(ywOrderLogRoDs::findYwOrderLog).map(this::map2LogVo);
    }

    private List<YwOrderLogVo> map2LogVo(List<YwOrderLogPo> poList) {
        return poList.stream().map(this::map2LogVo).collect(Collectors.toList());
    }

    private YwOrderLogVo map2LogVo(YwOrderLogPo po) {
        return new YwOrderLogVo().setYwOrderNo(po.getYwOrderNo()).setType(po.getType())
            .setOrderStatus(po.getOrderStatus()).setTargetUid(po.getTargetUid())
            .setTargetUserName(po.getTargetUserName()).setOpUid(po.getOpUid())
            .setOpUserName(po.getOpUserName()).setOpTime(po.getOpTime());

    }

    public Mono<ObjectResponse<YwOrderVo>> getSiteLatestRec(String siteId) {
        if (StringUtils.isBlank(siteId)) {
            throw new DcArgumentException("场站ID无效");
        }

        return Mono.just(siteId)
            .map(id -> RestUtils.buildObjectResponse(ywOrderRoDs.getSiteLatestRec(id)));
    }

    public Mono<List<AccRelativeOrderVo>> getOrderNum(List<Long> sysUidList) {
        List<AccRelativeOrderVo> res = sysUidList.stream().map(e -> {
            AccRelativeOrderVo temp = new AccRelativeOrderVo();
            temp.setSysUid(e);
            return temp;
        }).collect(Collectors.toList());

        Map<Long, Long> ywMap = new HashMap<>();
        List<AccRelativeOrderVo> ywList = ywOrderRoDs.getOrderNum(sysUidList);
        if (CollectionUtils.isNotEmpty(ywList)) {
            ywMap = ywList.stream().collect(
                Collectors.toMap(AccRelativeOrderVo::getSysUid, AccRelativeOrderVo::getYwOrderNum));
        }
        List<AccRelativeOrderVo> xjList = siteInspectionRecordRoDs.getOrderNum(sysUidList);
        Map<Long, Long> xjMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(xjList)) {
            xjMap = xjList.stream().collect(
                Collectors.toMap(AccRelativeOrderVo::getSysUid, AccRelativeOrderVo::getXjOrderNum));
        }
        Map<Long, Long> finalYwMap = ywMap;
        Map<Long, Long> finalXjMap = xjMap;
        res.forEach(e -> {
            e.setYwOrderNum(finalYwMap.get(e.getSysUid()));
            e.setXjOrderNum(finalXjMap.get(e.getSysUid()));
        });
        return Mono.just(res);
    }

    public Mono<Integer> recNoticeFeedback(CusRecNoticeParam param) {
        return Mono.just(param).map(ywOrderRwDs::recNoticeFeedback);
    }

    @Deprecated(since = "20230309")
    public Mono<BaseResponse> ywOrderFixImages() {
        int size = 100;
        int count = 0;
        while (count++ < 100) {
            List<OldOrderImagesStruct> result = ywOrderRoDs.oldYwOrder(size);
            if (CollectionUtils.isEmpty(result)) {
                break;
            }
            log.info("需要处理个数: {}", result.size());
            result.stream().map(x -> new YwOrderPo().setYwOrderNo(x.getOrderNo()).setImages(
                x.getImages().stream().map(k -> new FileItem().setUrl(k))
                    .collect(Collectors.toList()))).forEach(order -> {
                boolean b = ywOrderRwDs.upsetYwOrderImage(order);
                log.info("更新订单: {}, {}", order, b);
            });
        }
        return Mono.just(RestUtils.success());
    }

    /**
     * 批量创建运维工单
     */
    public void batchCreateYwOrder(BatchCreateYwParams params) {

        // 获取场站列表
        List<SiteVo> siteList = siteGroupRoDs.getSiteListByGidList(
            new SiteGroupSiteParam().setGidList(params.getGidList()));

        Mono.just(siteList).filter(CollectionUtils::isNotEmpty).flatMapMany(Flux::fromIterable)
            .flatMap(siteVo -> {
                CreateYwOrderParam p = new CreateYwOrderParam();
                p.setSiteId(siteVo.getId())
                    .setSiteCommId(siteVo.getSiteCommId())
                    .setFaultLevel(YwOrderLevel.UNKNOWN)
                    .setIgnoreWarningCodeList(params.getIgnoreWarningCodeList())
                    .setSourceType(CreateYwOrderSourceType.DEVICE_WARNING)
                    .setEquipRepairEntry(Boolean.FALSE).setOpUid(0L).setOpType(UserType.SYSTEM)
                    .setOpName(UserType.SYSTEM.getDesc());

                // 获取场站可用的桩，剔除已停用、拆除
                EvseTinyParam evseTinyParam = new EvseTinyParam().setSiteId(siteVo.getId())
                    .setBizStatusList(List.of(EvseBizStatus.NORMAL));
                return Mono.just(iotDeviceMgmFeignClient.getEvseTinyList(
                        evseTinyParam)).doOnNext(FeignResponseValidate::checkIgnoreData).map(
                        ListResponse::getData)
                    .filter(CollectionUtils::isNotEmpty)
                    .map(x -> {
                        p.setEvseNoList(x.stream().map(EvseTinyDto::getEvseNo).toList());
                        return p;
                    });
            })
            .filter(p -> CollectionUtils.isNotEmpty(p.getEvseNoList())).flatMap(p -> {
                // 桩最近的告警
                WarningBiParam warningBiParam = new WarningBiParam();
                warningBiParam.setEvseNoList(p.getEvseNoList())
                    .setIgnoreWarningCodeList(params.getIgnoreWarningCodeList())
                    .setStatus(List.of(AlarmStatusEnum.ALARM_STATUS_NOT_END))
                    .setWarningDuration(params.getWarningDuration());
                return Mono.just(deviceMonitorFeignClient.getEvseWarningList(
                        warningBiParam)).doOnNext(FeignResponseValidate::checkIgnoreData)
                    .map(ListResponse::getData)
                    .filter(CollectionUtils::isNotEmpty).map(x -> {
                        Map<String, List<WWarningRecord>> evseMap = x.stream()
                            .collect(Collectors.groupingBy(WWarningRecord::getDeviceId));

                        // 组织告警描述
                        StringBuilder sb = new StringBuilder();
                        evseMap.forEach((deviceId, warningList) -> {
                            sb.append(StringUtils.isEmpty(warningList.get(0).getEvseName()) ? ""
                                    : warningList.get(0).getEvseName())
                                .append(" ").append(warningList.get(0).getDeviceId()).append(":");

                            // 根据故障码去重,存在一个桩多个枪同时告警
                            List<WWarningRecord> list = warningList.stream()
                                .collect(Collectors.collectingAndThen(Collectors.toCollection(() ->
                                        new TreeSet<>
                                            (Comparator.comparing(WWarningRecord::getWarningCode))),
                                    ArrayList::new));

                            list.forEach(warningRecord -> sb.append("故障码:")
                                .append(warningRecord.getWarningCode()).append(",")
                                .append(warningRecord.getWarningName()).append(","));
                            sb.append(";");
                        });
                        p.setEvseNoList(
                                x.stream().map(WWarningRecord::getDeviceId).distinct()
                                    .toList())
                            .setFaultDesc(sb.toString());

                        return p;
                    });

            }).flatMap(p -> {
                // 运维人员自动选择
                GetYwUserParam ywUserParam = new GetYwUserParam();
                ywUserParam.setEquipRepairEntry(p.getEquipRepairEntry())
                    .setUid(p.getOpUid()).setSiteId(p.getSiteId())
                    .setSiteCommId(p.getSiteCommId());
                return authCommFeignClient.getYwUser(ywUserParam).doOnNext(FeignResponseValidate::check)
                    .map(ObjectResponse::getData).filter(Optional::isPresent).map(Optional::get)
                    .map(ywUser -> {
                        p.setMaintName(ywUser.getName())
                            .setMaintUid(ywUser.getId())
                            .setMaintType(UserType.SYS_USER)
                            .setMaintCommId(ywUser.getCommId());
                        return p;
                    });
            }).flatMap(this::createYwOrder).collectList().subscribe();
    }

//    @Deprecated(since = "20230309")
//    public Mono<BaseResponse> ywOrderFixFaultImages() {
//        int size = 100;
//        int count = 0;
//        while (count++ < 100) {
//            List<OldOrderImagesStruct> result = ywOrderRoDs.oldYwOrderForFault(size);
//            if (CollectionUtils.isEmpty(result)) {
//                break;
//            }
//            log.info("需要处理个数: {}", result.size());
//            result.stream()
//                .map(x -> new YwOrderPo()
//                    .setYwOrderNo(x.getOrderNo())
//                    .setImages(x.getImages()
//                        .stream().map(k -> new FileItem().setUrl(k))
//                        .collect(Collectors.toList())))
//                .forEach(order -> {
//                    boolean b = ywOrderRwDs.upsetYwOrderFaultImage(order);
//                    log.info("更新订单: {}, {}", order, b);
//                });
//        }
//        return Mono.just(RestUtils.success());
//    }
}
