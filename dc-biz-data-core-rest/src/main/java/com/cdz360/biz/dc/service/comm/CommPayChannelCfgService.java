package com.cdz360.biz.dc.service.comm;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.ds.trading.rw.comm.ds.CommPayChannelCfgRwDs;
import com.cdz360.biz.model.trading.comm.po.CommPayChannelCfgPo;
import com.cdz360.biz.model.site.type.SitePayChannelType;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CommPayChannelCfgService {

    @Autowired
    private CommPayChannelCfgRwDs commPayChannelCfgRwDs;

    /**
     * 记录已知问题:
     * <p>同一商户层级的场站，删掉其中一个场站会导致其他场站无法使用</p>
     *
     * @param commId
     * @param payChannelTypes
     */
    public void updateSiteCommPayChannelCfg(
        Long commId, List<SitePayChannelType> payChannelTypes) {
        boolean b = false;
        if (CollectionUtils.isEmpty(payChannelTypes)) {
            b = commPayChannelCfgRwDs.deleteCfg(commId, AppClientType.UNKNOWN, null);
        } else {
            if (payChannelTypes.contains(SitePayChannelType.ABC_BANK)) {
                b = commPayChannelCfgRwDs.addOrIgnore(new CommPayChannelCfgPo()
                    .setCommId(commId)
                    .setClientType(AppClientType.UNKNOWN)
                    .setPayChannel(PayChannel.ABC_BANK)
                    .setPayCfgUniqueNo("ABC_34474_DEFAULT"));
            } else {
                b = commPayChannelCfgRwDs.deleteCfg(
                    commId, AppClientType.UNKNOWN, PayChannel.ABC_BANK);
            }

            if (payChannelTypes.contains(SitePayChannelType.CCB_ECNY_BANK)) {
                b = commPayChannelCfgRwDs.addOrIgnore(new CommPayChannelCfgPo()
                    .setCommId(commId)
                    .setClientType(AppClientType.UNKNOWN)
                    .setPayChannel(PayChannel.CCB_ECNY_BANK)
                    .setPayCfgUniqueNo("CCB_34474_DEFAULT"));
            } else {
                b = commPayChannelCfgRwDs.deleteCfg(
                    commId, AppClientType.UNKNOWN, PayChannel.CCB_ECNY_BANK);
            }
        }
        if (b) {
            log.info("操作支付渠道成功!");
        }
    }
}
