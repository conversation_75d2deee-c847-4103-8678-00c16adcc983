package com.cdz360.biz.dc.domain;
//
//import lombok.Data;
//
//import java.io.Serializable;
//import java.util.Date;
//
///**
// * <AUTHOR>
// *  支付记录
// * @since 2018/12/3 15:06
// */
//@Data
//public class Payment implements Serializable {
//
//    protected static final long serialVersionUID = 1L;
//
//    /**
//     * 支付payId
//     **/
//    protected String payId;
//
//    /**
//     * 订单编号
//     **/
//    protected Long orderId;
//
//    /**
//     * 支付时间
//     **/
//    protected Date payTime;
//
//    /**
//     * 支付流水号
//     **/
//    protected String payCertificateId;
//
//    /**
//     * 支付状态,1:待支付; 2:已支付;3: 支付失败
//     **/
//    protected Integer payStatus;
//
//    /**
//     * 支付方式 (1~19微信 20~39支付宝 40银联 ，50线下支付,60余额支付,70NFC储值卡,80无需支付、100其它)
//     **/
//    protected Integer payModes;
//
//    /**
//     * 应付金额
//     **/
//    protected Integer payablePrice;
//
//    /**
//     * 实付金额
//     **/
//    protected Integer actualPrice;
//
//    /**
//     * 应用来源,0:微信渠道;1:APP在线发起充电;2:APP蓝牙发起充电:3:刷卡充电:4:桩上报离线数据
//     **/
//    protected Integer channelId;
//
//    /**
//     * 客户编号
//     **/
//    protected Long customerId;
//
//    /**
//     * 商户编号
//     **/
//    protected Long commercialId;
//
//    /**
//     * 支付说明
//     **/
//    protected String payRemark;
//
//    /**
//     * 创建时间
//     **/
//    protected Date createTime;
//
//    /**
//     * 业务类型(1为充值，2为余额抵扣（支出），3.提现，4.保证金提取（保证金提现），5.保证金缴纳，6.充电入账，7充电退款，8充电退款（退至余额）)
//     **/
//    protected Integer businessType;
//
//    /**
//     * 流水号
//     **/
//    protected String billId;
//
//    /**
//     * 平台类型(1公有云，2互联互通，3第三方)
//     **/
//    protected Integer platformType;
//
//    /**
//     * 卡id
//     **/
//    protected Long cardId;
//
//    /**
//     * 卡号
//     **/
//    protected String cardNo;
//
//    /**
//     *  数据类型 1 用户数据 2 卡数据
//     **/
//    protected Integer dataType;
//
//    /**
//     * 设备运营商ID
//     */
//    private Long deviceCommercialId;
//}
