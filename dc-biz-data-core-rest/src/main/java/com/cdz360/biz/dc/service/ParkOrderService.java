package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.utils.*;
import com.cdz360.biz.dc.domain.OrderInMongo;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderCarRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderRoDs;
import com.cdz360.biz.ds.trading.ro.park.ds.ParkOrderRoDs;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderCarRwDs;
import com.cdz360.biz.ds.trading.rw.park.ds.ParkOrderRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteDefaultSettingRwDs;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.po.ChargerOrderCarPo;
import com.cdz360.biz.model.trading.order.po.ChargerOrderPo;
import com.cdz360.biz.model.trading.order.vo.ChargeOrderCache;
import com.cdz360.biz.model.trading.park.api.dto.ParkFeeReq;
import com.cdz360.biz.model.trading.park.api.dto.ParkOrderCouponReq;
import com.cdz360.biz.model.trading.park.api.dto.ParkOrderCouponRes;
import com.cdz360.biz.model.trading.park.po.ParkOrderPo;
import com.cdz360.biz.model.trading.park.type.ParkOrderStatusType;
import com.cdz360.biz.model.trading.park.vo.BsParkOrder;
import com.cdz360.biz.model.trading.park.vo.ParkCouponVo;
import com.cdz360.biz.model.trading.site.po.SiteDefaultSettingPo;
import com.cdz360.data.cache.RedisChargeOrderRwService;
import com.chargerlinkcar.framework.common.constant.OrderStatus;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrder;
import com.chargerlinkcar.framework.common.domain.vo.VinCarNoParam;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * ParkOrderService
 *
 * @since 4/20/2021 4:16 PM
 * <AUTHOR>
 */
@Slf4j
@Service
public class ParkOrderService {

    @Autowired
    private ParkOrderRoDs parkOrderRoDs;

    @Autowired
    private ParkOrderRwDs parkOrderRwDs;

    @Autowired
    private ChargerOrderCarRwDs chargerOrderCarRwDs;

    @Autowired
    private ChargerOrderCarRoDs chargerOrderCarRoDs;

    @Autowired
    private SiteDefaultSettingRwDs siteDefaultSettingRwDs;

    @Autowired
    private ChargerOrderRoDs chargerOrderRoDs;

    @Autowired
    private RedisChargeOrderRwService redisChargeOrderRwService;

    @Autowired
    private MongoTemplate template;

    // 第三方停车对接类型:
    private static final String PARK_TYPE_IST = "IST";// IST艾视特
    private static final String PARK_TYPE_ZK = "ZK";// ZK中控
    private static final String PARK_TYPE_YB = "YB";// YB宜泊
    private static final String PARK_TYPE_CX = "CX";// CX 深圳创享智能开发有限公司(扬中停车场)
    public static final String PARK_TYPE_NTC = "NTC";// NTC 宁停车(燕子矶停车场)
    public static final String PARK_TYPE_STC = "STC";// STC 苏停车(摩尔广场)
    public static final String PARK_TYPE_JS = "JS";// JS 捷顺https://jsopen.jslife.com.cn/cloudopen/index.html#/documentCenter?id=467
    public static final String PARK_TYPE_PP = "PP";// PP停车

    private static final long ParkingGapMSec = 5 * 3600 * 1000;// 停车有效时间

    public Mono<ObjectResponse<Boolean>> in(BsParkOrder bsParkOrder) {

        ParkOrderPo byParkOrderId = parkOrderRoDs.getByParkOrderId(bsParkOrder.getOrderId());
        if(byParkOrderId == null) {
            ParkOrderPo parkOrderPo = this.convert2Po(bsParkOrder, ParkOrderStatusType.IN);

            SiteDefaultSettingPo siteDefaultSettingPo = siteDefaultSettingRwDs.selectByParkId(bsParkOrder.getParkId());
            if(siteDefaultSettingPo == null) {
                log.error("找不到对应的场站配置: parkId: {}", bsParkOrder.getParkId());
            } else {
                // 当前发现此车牌号在该场站下充电中时，将充电订单关联到停车订单
                // 此段逻辑发生的概率应该不大
                // 仅发生在充电订单开始后，收到道闸系统的推送信息
                ChargerOrderPo chargingOrder = chargerOrderRoDs.getChargingOrderAtSiteByCarNo(
                        bsParkOrder.getCarNumber(),
                        siteDefaultSettingPo.getSiteId());

                Optional.ofNullable(chargingOrder).ifPresent(e -> parkOrderPo.setChargeOrderNo(e.getOrderNo()));

            }

            parkOrderRwDs.insertParkOrder(parkOrderPo);
        } else {
            log.info("收到重复入场记录: {}", bsParkOrder.getOrderId());
        }


        return Mono.just(new ObjectResponse<>(true));
    }

    public Mono<ObjectResponse<Boolean>> out(BsParkOrder bsParkOrder) {
        ParkOrderPo byParkOrderId = parkOrderRoDs.getByParkOrderId(bsParkOrder.getOrderId());
        if(byParkOrderId == null) {
            log.warn("没有对应入场记录: {}", bsParkOrder.getOrderId());
            ParkOrderPo parkOrderPo = this.convert2Po(bsParkOrder, ParkOrderStatusType.OUT);
            parkOrderRwDs.insertParkOrder(parkOrderPo);
        } else if(ParkOrderStatusType.IN.equals(byParkOrderId.getStatus())) {
            log.info("修改停车订单状态为OUT: ", bsParkOrder.getOrderId());
            ParkOrderPo parkOrderPo = this.convert2Po(bsParkOrder, ParkOrderStatusType.OUT);

            parkOrderPo.setId(byParkOrderId.getId());

            parkOrderRwDs.updateParkOrder(parkOrderPo);
        } else {
            log.info("收到重复出场记录: {}", bsParkOrder.getOrderId());
        }

        return Mono.just(new ObjectResponse<>(true));
    }

    private ParkOrderPo convert2Po(BsParkOrder bsParkOrder, ParkOrderStatusType status) {
        ParkOrderPo parkOrderPo = new ParkOrderPo();
        parkOrderPo.setStatus(status)
                .setCarNo(bsParkOrder.getCarNumber())
                .setParkId(bsParkOrder.getParkId())
                .setInTime(bsParkOrder.getInTime() != null ? new Date(bsParkOrder.getInTime() * 1000) : null)
                .setOutTime(bsParkOrder.getOutTime() != null ? new Date(bsParkOrder.getOutTime() * 1000) : null)
                .setParkOrderId(bsParkOrder.getOrderId())
//                    .setChargeOrderNo(bsParkOrder)
                .setDuration(bsParkOrder.getDuration())
                .setCarType(bsParkOrder.getCarType())
                .setPayType(bsParkOrder.getPayType())
                .setTotalAmount(bsParkOrder.getTotal())
                .setReduceAmount(bsParkOrder.getReduceAmount())
                .setPidAddr(bsParkOrder.getPicAddr());
        return parkOrderPo;
    }

    public Mono<ObjectResponse<String>> getParkSignKey(Long parkId) {
        SiteDefaultSettingPo siteDefaultSettingPo = siteDefaultSettingRwDs.selectByParkId(parkId);
        if(siteDefaultSettingPo == null || StringUtils.isBlank(siteDefaultSettingPo.getParkSignKey())) {
            return Mono.just(new ObjectResponse<>(""));
        } else {
            return Mono.just(new ObjectResponse<>(siteDefaultSettingPo.getParkSignKey()));
        }
    }

    public Mono<ObjectResponse<String>> getParkSignKey(String siteId) {
        SiteDefaultSettingPo siteDefaultSettingPo = siteDefaultSettingRwDs.getBySiteId(siteId);
        if(siteDefaultSettingPo == null || StringUtils.isBlank(siteDefaultSettingPo.getParkSignKey())) {
            return Mono.just(new ObjectResponse<>(""));
        } else {
            return Mono.just(new ObjectResponse<>(siteDefaultSettingPo.getParkSignKey()));
        }
    }

    /**
     * 更新充电订单车辆信息 和 订车订单关联的充电订单
     * @param vinCarNoParam
     * @return
     */
    public Mono<ObjectResponse<Boolean>> updateChargeOrderCarAndPark(VinCarNoParam vinCarNoParam) {
        IotAssert.isNotBlank(vinCarNoParam.getOrderNo(), "请输入订单号");
        IotAssert.isNotBlank(vinCarNoParam.getCarNo(), "请输入车牌号");
        ChargerOrderCarPo chargerOrderCar = chargerOrderCarRwDs.getChargerOrderCar(vinCarNoParam.getOrderNo());

        IotAssert.isNotNull(chargerOrderCar, "找不到订单停车信息");

        final ChargeOrderCache order = redisChargeOrderRwService.getOrder(vinCarNoParam.getOrderNo(),
                ChargeOrderCache.class);
        IotAssert.isNotNull(order, "更新车牌号失败，充电订单未生成");

        IotAssert.isNotNull(order.getStatus(), "订单状态异常");

        IotAssert.isTrue(OrderStatus.ORDER_STATUS_UNACTIVATED <= order.getStatus() &&
                order.getStatus() < OrderStatus.ORDER_STATUS_COMPLETE,
                "更新车牌号失败，当前不允许修改车牌号，请稍后再试");

        ChargerOrderCarPo car = new ChargerOrderCarPo();
        car.setOrderNo(vinCarNoParam.getOrderNo());
        car.setCarNo(vinCarNoParam.getCarNo());

        return Mono.just(RestUtils.buildObjectResponse(chargerOrderCarRwDs.addUpdateChargerOrderCar(car)))
                .doOnNext(e -> {
                    if(e.getData()) {
                        log.info("更新redis订单车牌号");
                        Optional.ofNullable(order).ifPresentOrElse(redisOrder -> {
                            order.setCarNo(vinCarNoParam.getCarNo());
                            redisChargeOrderRwService.updateChargeOrder(vinCarNoParam.getOrderNo(), order);
                        }, () -> {
                            log.warn("redis 不存在此订单: {}", vinCarNoParam.getOrderNo());
                        });
                    }
                })
                .doOnNext(e -> {
                    if(e.getData()) {
                        log.info("更新mongoDB订单车牌号");
                        Update update = new Update();
                        update.set("carNo", vinCarNoParam.getCarNo());
                        // 更新查询条件
                        Criteria criteria = Criteria.where("orderNo").is(vinCarNoParam.getOrderNo());
                        log.info("从mongodb中更新订单数据的查询条件: {}", JsonUtils.toJsonString(criteria));
                        // 存在则更新，不存在则新增
                        log.info("更新订单数据字段数据, update={}", update);
                        UpdateResult result = template.upsert(Query.query(criteria), update, OrderInMongo.class);
                        log.info("<< 更新结果: result={}", result);
                    }
                })
                .doOnNext(e -> {

                    // 已匹配充电订单的停车订单，作为可疑订单
//                    ParkOrderPo parkingByChargeOrderNo =
//                            parkOrderRoDs.getParkingByChargeOrderNo(vinCarNoParam.getOrderNo());

                    // 匹配到的停车订单，此停车订单将修改其chargeOrderNo字段
                    List<ParkOrderPo> parkingByCarNoList = parkOrderRoDs.getParkingByCarNo(vinCarNoParam.getCarNo(),
                            vinCarNoParam.getOrderNo());

                    ParkOrderPo parkOrderByOrderNo = parkOrderRoDs.getParkingByChargeOrderNo(vinCarNoParam.getOrderNo());

                    log.info("正在车场中的车: {}", parkingByCarNoList);

                    final ParkOrderPo latestParkingOrder = CollectionUtils.isNotEmpty(parkingByCarNoList) ?
                        parkingByCarNoList.get(0) :
                        null;

                    Date now = new Date();
                    if(latestParkingOrder != null &&
                        latestParkingOrder.getCreateTime().getTime() + ParkingGapMSec > now.getTime()) {
                        // 近期的入场信息
                        ParkOrderPo parkOrderPo = new ParkOrderPo();
                        parkOrderPo.setId(latestParkingOrder.getId())
                            .setChargeOrderNo(vinCarNoParam.getOrderNo());

//                        log.info("绑定停车-充电订单({}): {}, {}",
//                            parkOrderRwDs.updateParkOrder(parkOrderPo),
//                            latestParkingOrder.getParkOrderId(),
//                            vinCarNoParam.getOrderNo());

                        log.info("修改停车订单对应的充电订单: {} -> {}",
                            latestParkingOrder.getChargeOrderNo(), vinCarNoParam.getOrderNo());
                        latestParkingOrder.setChargeOrderNo(vinCarNoParam.getOrderNo());
                        if (parkOrderByOrderNo != null) {
                            parkOrderByOrderNo.setChargeOrderNo(parkOrderByOrderNo.getCarNo() + "_" + parkOrderByOrderNo.getChargeOrderNo());
                            parkOrderRwDs.updateParkOrder(parkOrderByOrderNo);
                        }
                        parkOrderRwDs.updateParkOrder(latestParkingOrder);
                    } else {
                        log.info("没有匹配的停车订单");
                        SiteDefaultSettingPo setting = siteDefaultSettingRwDs.getByOrderNo(vinCarNoParam.getOrderNo());
                        if(setting != null &&
                                (PARK_TYPE_ZK.equals(setting.getParkType()) ||
                                        PARK_TYPE_YB.equals(setting.getParkType()) ||
                                        PARK_TYPE_CX.equals(setting.getParkType()) ||
                                        PARK_TYPE_JS.equals(setting.getParkType()))) {
                            ParkOrderPo parkOrder = parkOrderRoDs.getParkingByChargeOrderNo(vinCarNoParam.getOrderNo());
                            if(parkOrder == null) {
                                log.info("{}-尝试创建订车订单", setting.getParkType());
                                ParkOrderPo parkOrderPo = new ParkOrderPo();
                                parkOrderPo.setChargeOrderNo(vinCarNoParam.getOrderNo())
                                        .setCarNo(vinCarNoParam.getCarNo())
                                        .setStatus(ParkOrderStatusType.IN)
                                        .setParkId(setting.getParkId())
                                        .setParkOrderId("");
                                parkOrderRwDs.insertParkOrder(parkOrderPo);
                            } else {
                                log.info("{}-尝试修改订车订单: {}", setting.getParkType(), parkOrder);
                                parkOrder.setCarNo(vinCarNoParam.getCarNo());
                                parkOrderRwDs.updateParkOrder(parkOrder);
                            }
                        } else {
                            log.info("场站未配置停车减免");
                        }
                    }
                });
    }

    public Mono<ObjectResponse<Integer>> bindAllChargingOrderCar(VinCarNoParam vinCarNoParam) {

        if(vinCarNoParam == null ||
            vinCarNoParam.getUserId() == null ||
            StringUtils.isEmpty(vinCarNoParam.getCarNo())) {
            log.warn("参数不正确");
            return Mono.just(new ObjectResponse<>(0));
        }

        return Mono.just(vinCarNoParam)
            .map(e -> {
                // 用户3天内最近至多100条充电未完成的记录
                final ListChargeOrderParam param = new ListChargeOrderParam();

                final TimeFilter createTimeFilter = new TimeFilter();
                final Date endTime = new Date();
                final Date startTime = DateUtils.addDays(endTime, -3);
                createTimeFilter.setStartTime(startTime).setEndTime(endTime);

                param.setStatusList(List.of(
                    OrderStatus.ORDER_STATUS_UNACTIVATED,
                        OrderStatus.ORDER_STATUS_SWITCH_ON,
                        OrderStatus.ORDER_STATUS_CHARGING
//                        OrderStatus.ORDER_STATUS_COMPLETE,
//                        OrderStatus.ORDER_STATUS_USER_PAY
                    ))
                    .setCusId(vinCarNoParam.getUserId())
                    .setChargeCreateTimeFilter(createTimeFilter)
                    .setSize(100);
                final List<ChargerOrder> chargerOrderList =
                    chargerOrderRoDs.listChargingOrderNonCarNo(param);

                if(CollectionUtils.isNotEmpty(chargerOrderList)) {
                    emptyCarNoOrderBind(chargerOrderList, vinCarNoParam.getCarNo()).subscribe();
                    return chargerOrderList.size();
                } else {
                    return 0;
                }

            })
            .map(RestUtils::buildObjectResponse);
    }

    private Mono<Integer> emptyCarNoOrderBind(List<ChargerOrder> orderList, String carNo) {
        return Mono.just(orderList)
            .doOnNext(e -> {
                for(ChargerOrder one : e) {
                    VinCarNoParam vinCarNoParam = new VinCarNoParam();
                    vinCarNoParam.setCarNo(carNo).setOrderNo(one.getOrderNo());
                    updateChargeOrderCarAndPark(vinCarNoParam).subscribe();
                }
            })
            .map(List::size);
    }

    /**
     * 检查并获取停车优惠券
     * @param chargeOrderNo
     * @param kwh
     * @return
     */
    public Mono<ObjectResponse<ParkCouponVo>> checkParkCoupon(String chargeOrderNo, BigDecimal kwh) {

        IotAssert.isNotNull(chargeOrderNo, "请输入订单");
        IotAssert.isNotNull(kwh, "请输入充电量");

        ChargerOrder byOrderNo = chargerOrderRoDs.findIncludeCarByOrderNo(chargeOrderNo);
        IotAssert.isNotNull(byOrderNo, "找不到订单");

        IotAssert.isNotBlank(byOrderNo.getStationId(), "订单无关联场站");

        SiteDefaultSettingPo bySiteId = siteDefaultSettingRwDs.getBySiteId(byOrderNo.getStationId());

        if(bySiteId != null && bySiteId.getParkCouponKwh() != null && bySiteId.getParkCouponTime() != null) {

            log.info("第三方停车费减免对接类型: {}", bySiteId.getParkType());

            if(PARK_TYPE_IST.equals(bySiteId.getParkType())) {

                if (BigDecimal.valueOf(bySiteId.getParkCouponKwh().longValue()).compareTo(kwh) <= 0) {
                    // 达到减免条件
                    ParkOrderPo parkingByChargeOrderNo = parkOrderRoDs.getParkingByChargeOrderNo(chargeOrderNo);

                    return Optional.ofNullable(parkingByChargeOrderNo).map(e -> {
                        ParkCouponVo parkCouponVo = new ParkCouponVo();
                        parkCouponVo.setCarNo(e.getCarNo())
                                .setParkType(PARK_TYPE_IST)
                                .setDuration(bySiteId.getParkCouponTime())
                                .setParkId(e.getParkId())
                                .setParkOrderNo(e.getParkOrderId())
                                .setParkSignKey(bySiteId.getParkSignKey())
                                .setSiteId(byOrderNo.getStationId());
                        return Mono.just(new ObjectResponse<>(parkCouponVo));
                    }).orElseGet(() -> {
                        log.info("没有停车订单");
                        return Mono.just(new ObjectResponse<>());
                    });

//                if(parkingByChargeOrderNo != null) {
//
//                    ParkCouponVo parkCouponVo = new ParkCouponVo();
//                    parkCouponVo.setCarNo(parkingByChargeOrderNo.getCarNo())
//                            .setDuration(bySiteId.getParkCouponKwh())
//                            .setParkId(parkingByChargeOrderNo.getParkId())
//                            .setParkOrderNo(parkingByChargeOrderNo.getChargeOrderNo())
//                            .setParkSignKey(bySiteId.getParkSignKey())
//                            .setSiteId(byOrderNo.getStationId());
//                    return Mono.just(new ObjectResponse<>(parkCouponVo));
//                } else {
//                    log.info("没有停车订单");
//                }
                } else {
                    log.info("PARK_TYPE_IST, 电量为: {},未达到停车减免条件: {}",
                            kwh, bySiteId.getParkCouponKwh());
                    return Mono.just(new ObjectResponse<>());
                }
            } else if(PARK_TYPE_ZK.equals(bySiteId.getParkType()) ||
                    PARK_TYPE_YB.equals(bySiteId.getParkType()) ||
                    PARK_TYPE_CX.equals(bySiteId.getParkType())) {
                final ParkOrderPo parkingByChargeOrderNo = parkOrderRoDs.getParkingByChargeOrderNo(chargeOrderNo);
                if (BigDecimal.valueOf(bySiteId.getParkCouponKwh().longValue()).compareTo(kwh) <= 0) {
                    // 达到减免条件

                    return Optional.ofNullable(parkingByChargeOrderNo).map(e -> {
                        ParkCouponVo parkCouponVo = new ParkCouponVo();
                        parkCouponVo.setCarNo(e.getCarNo())
                                .setParkOrderId(e.getId())
                                .setParkType(bySiteId.getParkType())
                                .setDuration(bySiteId.getParkCouponTime())
                                .setParkId(e.getParkId())
                                .setParkOrderNo(e.getParkOrderId())
                                .setParkAppId(bySiteId.getParkAppId())
                                .setParkAppSecret(bySiteId.getParkAppSecret())
                                .setSiteId(byOrderNo.getStationId());
                        return Mono.just(new ObjectResponse<>(parkCouponVo));
                    }).orElseGet(() -> {
                        log.info("没有停车订单");
                        if (PARK_TYPE_CX.equals(bySiteId.getParkType())) {
                            ParkCouponVo parkCouponVo = new ParkCouponVo();
                            parkCouponVo.setCarNo(byOrderNo.getCarNo())
                                .setParkType(bySiteId.getParkType())
                                .setDuration(bySiteId.getParkCouponTime())
                                .setParkId(bySiteId.getParkId())
                                .setParkAppId(bySiteId.getParkAppId())
                                .setParkAppSecret(bySiteId.getParkAppSecret())
                                .setSiteId(byOrderNo.getStationId());
                            return Mono.just(new ObjectResponse<>(parkCouponVo));
                        }
                        return Mono.just(new ObjectResponse<>());
                    }).doOnNext(e -> {
                        if(e != null && e.getData() != null) {
                            ParkCouponVo parkCouponVo = e.getData();
                            ParkOrderPo parkOrderPo = new ParkOrderPo();
                            parkOrderPo.setId(parkCouponVo.getParkOrderId())
                                    .setStatus(ParkOrderStatusType.OUT);
                            log.info("ZK, 设置订车订单为OUT: {}", parkOrderPo);
                            if (!PARK_TYPE_CX.equals(bySiteId.getParkType())) {
                                parkOrderRwDs.updateParkOrder(parkOrderPo);
                            }
                        }
                    });
                } else {
                    log.info("PARK_TYPE_ZK, 电量为: {},未达到停车减免条件: {}",
                            kwh, bySiteId.getParkCouponKwh());
                    Optional.ofNullable(parkingByChargeOrderNo)
                            .ifPresent(e -> {
                                Integer id = e.getId();
                                ParkOrderPo parkOrderPo = new ParkOrderPo();
                                parkOrderPo.setId(id)
                                        .setStatus(ParkOrderStatusType.OUT);
                                log.info("ZK, 设置订车订单为OUT: {}", parkOrderPo);
                                if (!PARK_TYPE_CX.equals(bySiteId.getParkType())) {
                                    parkOrderRwDs.updateParkOrder(parkOrderPo);
                                }
                            });
                    return Mono.just(new ObjectResponse<>());
                }
            } else if(PARK_TYPE_JS.equals(bySiteId.getParkType())) {
//                final ParkOrderPo parkingByChargeOrderNo = parkOrderRoDs.getParkingByChargeOrderNo(chargeOrderNo);
                if (BigDecimal.valueOf(bySiteId.getParkCouponKwh().longValue()).compareTo(kwh) <= 0) {
                    final ChargerOrderCarPo chargerOrderCar = chargerOrderCarRoDs.getChargerOrderCar(
                        chargeOrderNo);
                    if(chargerOrderCar == null || StringUtils.isEmpty(chargerOrderCar.getCarNo())) {
                        log.info("没有找到订单和车牌号记录: {}", chargeOrderNo);
                        return Mono.just(new ObjectResponse<>());
                    }
                    ParkCouponVo parkCouponVo = new ParkCouponVo();
                    parkCouponVo.setCarNo(chargerOrderCar.getCarNo())
                        .setParkOrderId(0)
                        .setParkType(bySiteId.getParkType())
                        .setDuration(bySiteId.getParkCouponTime())
                        .setParkId(bySiteId.getParkId())
                        .setParkOrderNo(chargeOrderNo)
                        .setParkAppId(bySiteId.getParkAppId())
                        .setParkAppSecret(bySiteId.getParkAppSecret())
                        .setParkConfig(bySiteId.getParkConfig())
                        .setSiteId(byOrderNo.getStationId());
                    return Mono.just(new ObjectResponse<>(parkCouponVo));
                } else {
                    log.info("{}, 电量为: {},未达到停车减免条件: {}", PARK_TYPE_JS,
                        kwh, bySiteId.getParkCouponKwh());
                    return Mono.just(new ObjectResponse<>());
                }
            } else {
                // 未配置第三方停车费减免对接类型
                if(StringUtils.isBlank(bySiteId.getParkSignKey())) {
                    log.error("缺失配置，需要排查");
                } else {
                    // 鼎充协议
                }
            }
        }

        if (bySiteId != null && PARK_TYPE_NTC.equals(bySiteId.getParkType())) {
            final ChargerOrderCarPo chargerOrderCar = chargerOrderCarRoDs.getChargerOrderCar(
                    chargeOrderNo);
            ParkCouponVo parkCouponVo = new ParkCouponVo();
            parkCouponVo.setCarNo(chargerOrderCar.getCarNo())
                    .setParkType(PARK_TYPE_NTC)
                    .setParkId(bySiteId.getParkId())
                    .setParkOrderNo(chargeOrderNo)
                    .setParkAppSecret(bySiteId.getParkAppSecret())
                    .setParkAppId(bySiteId.getParkAppId())
                    .setISP(byOrderNo.getCommercialName())
                    .setSiteId(byOrderNo.getStationId());
            return Mono.just(new ObjectResponse<>(parkCouponVo));
        } else if (bySiteId != null && PARK_TYPE_STC.equals(bySiteId.getParkType())) {
            final ChargerOrderCarPo chargerOrderCar = chargerOrderCarRoDs.getChargerOrderCar(
                    chargeOrderNo);
            ParkCouponVo parkCouponVo = new ParkCouponVo();
            parkCouponVo.setCarNo(chargerOrderCar.getCarNo())
                    .setParkType(PARK_TYPE_STC)
                    .setParkConfig(bySiteId.getParkConfig())
                    .setParkId(bySiteId.getParkId())
                    .setParkAppId(bySiteId.getParkAppId());
            return Mono.just(new ObjectResponse<>(parkCouponVo));
        } else if (bySiteId != null && PARK_TYPE_PP.equals(bySiteId.getParkType())) {
            final ChargerOrderCarPo chargerOrderCar = chargerOrderCarRoDs.getChargerOrderCar(chargeOrderNo);
            ParkCouponVo parkCouponVo = new ParkCouponVo();
            parkCouponVo.setCarNo(chargerOrderCar.getCarNo())
                    .setParkType(PARK_TYPE_PP)
                    .setParkConfig(bySiteId.getParkConfig())
                    .setParkAppId(bySiteId.getParkAppId())
                    .setParkAppSecret(bySiteId.getParkAppSecret());
            return Mono.just(new ObjectResponse<>(parkCouponVo));
        }

        log.info("不返回停车减免");

        return Mono.just(new ObjectResponse<>());
    }

    public Mono<ObjectResponse<Integer>> setParkCouponDuration(String chargeOrderNo, Integer duration) {
        IotAssert.isNotNull(chargeOrderNo, "请输入订单");
        IotAssert.isNotNull(duration, "请输入充电量");

        ParkOrderPo parkingByChargeOrderNo = parkOrderRoDs.getParkingByChargeOrderNo(chargeOrderNo);
        if(parkingByChargeOrderNo != null) {
            ParkOrderPo parkOrderPo = new ParkOrderPo();
            parkOrderPo.setId(parkingByChargeOrderNo.getId());
            parkOrderPo.setCouponDuration(duration);
            boolean b = parkOrderRwDs.updateParkOrder(parkOrderPo);
            return Mono.just(new ObjectResponse<>(b ? 1 : 0));
        }

        return Mono.just(new ObjectResponse<>(0));
    }

    public Mono<BaseResponse> uploadOrderParkFee(ParkFeeReq param) {
        return Mono.just(param)
            .doOnNext(e -> {
//                IotAssert.isNotBlank(param.getChargeOrderNo(), "请传入充电订单号");
                IotAssert.isNotBlank(param.getSiteId(), "请传入场站id");

                ParkOrderPo parkingByChargeOrderNo = null;

                if(StringUtils.isNotBlank(param.getChargeOrderNo())) {
                    // 查询是否已经存在停车订单
                    parkingByChargeOrderNo =
                        parkOrderRoDs.getParkingByChargeOrderNo(param.getChargeOrderNo());
                } else {
                    // 设定为空，以便通过表唯一键验证
                    param.setChargeOrderNo(null);
                }

                if(parkingByChargeOrderNo == null) {
                    ParkOrderPo parkOrderPo = this.convertParkFee2Po(param);
//                    SiteDefaultSettingPo siteDefaultSettingPo = siteDefaultSettingRwDs.getBySiteId(param.getSiteId());
//                    if(siteDefaultSettingPo == null) {
//                        log.error("找不到对应的场站配置: siteId: {}", param.getSiteId());
//                    } else {
                        // 当前发现此车牌号在该场站下充电中时，将充电订单关联到停车订单
                        // 此段逻辑发生的概率应该不大
                        // 仅发生在充电订单开始后，收到道闸系统的推送信息
//                        ChargerOrderPo chargingOrder = chargerOrderRoDs.getChargingOrderAtSiteByCarNo(
//                            bsParkOrder.getCarNumber(),
//                            siteDefaultSettingPo.getSiteId());
//
//                        Optional.ofNullable(chargingOrder).ifPresent(e -> parkOrderPo.setChargeOrderNo(e.getOrderNo()));

//                    }

                    parkOrderRwDs.insertParkOrder(parkOrderPo);
                } else {
                    log.info("更新当前已存在记录: {}", JsonUtils.toJsonString(parkingByChargeOrderNo));
                    ParkOrderPo parkOrderPo = this.convertParkFee2Po(param);

                    parkOrderPo.setId(parkingByChargeOrderNo.getId());

                    parkOrderRwDs.updateParkOrder(parkOrderPo);
                }
            })
            .map(e -> RestUtils.success());
    }

    private ParkOrderPo convertParkFee2Po(ParkFeeReq param) {
        ParkOrderPo parkOrderPo = new ParkOrderPo();
        parkOrderPo.setStatus(param.getOutTime() != null ? ParkOrderStatusType.OUT : ParkOrderStatusType.IN)
            .setCarNo(param.getCarNo())
            .setParkId(0L)
            .setInTime(param.getInTime())
            .setOutTime(param.getOutTime())
            .setParkOrderId(param.getParkOrderNo())
            .setChargeOrderNo(param.getChargeOrderNo())
            .setDuration(null)
            .setCarType(null)
            .setPayType(null)
            .setTotalAmount(param.getParkFee())
            .setReduceAmount(null)
            .setPidAddr(null)
            .setCouponDuration(param.getFreeMinutes());
        return parkOrderPo;
    }

    public Mono<ObjectResponse<ParkOrderCouponRes>> getDiscount(ParkOrderCouponReq param) {

        return Mono.just(param)
            .map(e -> {
                IotAssert.isNotBlank(param.getSiteId(), "请传入场站id");
                IotAssert.isNotBlank(param.getCarNo(), "请传入车牌号");

                SiteDefaultSettingPo bySiteId = siteDefaultSettingRwDs.getBySiteId(
                    param.getSiteId());
                if(bySiteId != null && bySiteId.getParkCouponKwh() != null && bySiteId.getParkCouponTime() != null) {
                    final List<ChargerOrderPo> orderList = chargerOrderRoDs.getPaidOrderAtSiteByCarNoRecently(
                        param.getCarNo(),
                        param.getSiteId(), 24, 99, param.getInTime());
                    if(CollectionUtils.isEmpty(orderList)) {
                        log.info("没有找到最近订单信息");
                        return RestUtils.buildObjectResponse(null);
                    }
                    final ChargerOrderPo chargerOrderPo = orderList.get(0);
                    final BigDecimal kwh = chargerOrderPo.getOrderElectricity();

                    if (BigDecimal.valueOf(bySiteId.getParkCouponKwh().longValue()).compareTo(kwh) <= 0) {
                        // 达到减免条件
                        log.info("订单信息： {}", chargerOrderPo);
                        ParkOrderCouponRes parkOrderCouponRes = new ParkOrderCouponRes();
                        parkOrderCouponRes.setChargeOrderNo(chargerOrderPo.getOrderNo())
                            .setChargeStartTime(new Date(chargerOrderPo.getChargeStartTime() * 1000))
                            .setChargeStopTime(new Date(chargerOrderPo.getChargeEndTime() * 1000))
                            .setFreeMinutes(bySiteId.getParkCouponTime());
                        log.info("返回优惠信息： {}", JsonUtils.toJsonString(parkOrderCouponRes));
                        return RestUtils.buildObjectResponse(parkOrderCouponRes);
                    } else {
                        log.info("电量为: {},未达到停车减免条件: {}",
                            kwh, bySiteId.getParkCouponKwh());
                        return RestUtils.buildObjectResponse(null);
                    }
                }

                log.info("未配置停车减免");
                return RestUtils.buildObjectResponse(null);
            });
    }
}