package com.cdz360.biz.dc.client.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.auth.user.po.SysUserPo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_AUTH,
    fallbackFactory = OaFeignClientHystrix.class
)
public interface ReactorAuthCenterFeignClient {

    @PostMapping("/api/sys/user/getSysUserById")
    Mono<ObjectResponse<SysUserPo>> getSysUserById(@RequestParam(value = "id") Long id);


}
