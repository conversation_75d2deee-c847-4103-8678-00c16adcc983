package com.cdz360.biz.dc.service.peek.invoice;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.ds.trading.ro.order.ds.PayBillRoDs;
import com.cdz360.biz.model.finance.type.TaxStatus;
import com.cdz360.biz.model.invoice.type.ProductType;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.model.trading.peek.invoice.dto.PeekInvoiceDto;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedTemplateSalDetailVo;
import com.chargerlinkcar.framework.common.domain.peek.invoice.param.PeekInvoiceParam;
import com.chargerlinkcar.framework.common.domain.vo.OrderBiVo;
import java.math.BigDecimal;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * ComputeInvoiceService
 *
 * @since 3/25/2023 5:34 PM
 * <AUTHOR>
 */
@Slf4j
@Service
public class ComputeInvoiceService {
    @Autowired
    private PayBillRoDs payBillRoDs;

    public PeekInvoiceDto peekInvoice(PeekInvoiceParam params) {
        // TODO 这个方法可能没写好，目前先这么做
        // 统计订单的充值金额(实际金额)
        PayBillParam billParam = new PayBillParam();
        if (!Boolean.TRUE.equals(params.getOpAll())) {
            billParam = new PayBillParam();
            billParam.setCommIdChain(params.getCommIdChain());
            billParam.setFlowTypeList(List.of(DepositFlowType.IN_FLOW));
            billParam.setUserId(params.getUserId());
            billParam.setTaxStatus(List.of(TaxStatus.NO, TaxStatus.PART));
            billParam.setAccountTypeList(List.of(params.getMode()));
            billParam.setOrderIdList(params.getOrderNoList());
        } else {

            billParam.setCommIdChain(params.getCommIdChain());
            // TODO 下面3个参数需要调整
            billParam.setUserId(params.getPayBillParam().getUserId());
            billParam.setOrderId(params.getPayBillParam().getOrderId());
            billParam.setPayTimeFilter(params.getPayBillParam().getPayTimeFilter());
//            billParam.setInCorpInvoice(true);
            billParam.setFlowTypeList(List.of(DepositFlowType.IN_FLOW));
            billParam.setAccountTypeList(List.of(params.getMode()));
            billParam.setTaxStatus(List.of(TaxStatus.NO, TaxStatus.PART));
        }

        // 充值实际金额
        BigDecimal cost = BigDecimal.ZERO;
//        if (!Boolean.TRUE.equals(params.getOpAll())) {
        ObjectResponse<OrderBiVo> orderBi = payBillRoDs.invoiceOrderBiForCorp(billParam);

        cost = orderBi.getData().getInvoiceAmount();

        // 客户商品行信息
        List<InvoicedTemplateSalDetailVo> detailVoList = params.getCorpInvoiceInfoVo().getTempRefVo().getDetailVoList();
        if (CollectionUtils.isEmpty(detailVoList)) {
            throw new DcArgumentException("商品行还没有配置");
        }

        PeekInvoiceDto ret = new PeekInvoiceDto();

        long count = detailVoList.stream()
            .filter(item -> item.getProductType() == ProductType.SERV_ACTUAL_FEE).count();
        if (count == 0) {
            // 全部算成电费费
            ret.setActualElecFee(cost)
                .setActualServFee(BigDecimal.ZERO)
                .setTotalFee(cost);
            return ret;
        }

        ret.setActualServFee(cost)
            .setActualElecFee(BigDecimal.ZERO)
            .setTotalFee(cost);
        return ret;

//        }
//        payBillRoDs.invoiceOrderBiForCorp()
    }
}