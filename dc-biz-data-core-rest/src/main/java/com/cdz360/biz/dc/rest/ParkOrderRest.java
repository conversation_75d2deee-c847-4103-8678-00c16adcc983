package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.ParkOrderService;
import com.cdz360.biz.model.trading.park.api.dto.ParkFeeReq;
import com.cdz360.biz.model.trading.park.api.dto.ParkOrderCouponReq;
import com.cdz360.biz.model.trading.park.api.dto.ParkOrderCouponRes;
import com.cdz360.biz.model.trading.park.vo.BsParkOrder;
import com.cdz360.biz.model.trading.park.vo.ParkCouponVo;
import com.chargerlinkcar.framework.common.domain.vo.VinCarNoParam;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;

/**
 * ParkOrderRest
 *
 * @since 4/20/2021 4:09 PM
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "停车订单", description = "park-order")
public class ParkOrderRest {

    @Autowired
    private ParkOrderService parkOrderService;


    @GetMapping("/dataCore/parkOrder/getParkSignKey")
    public Mono<ObjectResponse<String>> getParkSignKey(
            ServerHttpRequest request,
            @RequestParam("parkId") Long parkId) {
        log.info("查询车场signKey {}: parkId = {}",
                LoggerHelper2.formatEnterLog(request, false), parkId);

        return parkOrderService.getParkSignKey(parkId);
    }
    @GetMapping("/dataCore/parkOrder/getParkSignKeyBySiteId")
    public Mono<ObjectResponse<String>> getParkSignKeyBySiteId(
            ServerHttpRequest request,
            @RequestParam("siteId") String siteId) {
        log.info("查询车场signKey {}: siteId = {}",
                LoggerHelper2.formatEnterLog(request, false), siteId);

        return parkOrderService.getParkSignKey(siteId);
    }

    @PostMapping("/dataCore/parkOrder/getDiscount")
    public Mono<ObjectResponse<ParkOrderCouponRes>> getDiscount(
        ServerHttpRequest request,
        @RequestBody ParkOrderCouponReq param) {
        log.info("查询订单停车优惠信息 {}: getDiscount = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return parkOrderService.getDiscount(param);
    }

    @PostMapping("/dataCore/parkOrder/uploadOrderParkFee")
    public Mono<BaseResponse> uploadOrderParkFee(
        ServerHttpRequest request,
        @RequestBody ParkFeeReq param) {
        log.info("上报停车收费信息 {}: uploadOrderParkFee = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return parkOrderService.uploadOrderParkFee(param);
    }

    @PostMapping("/dataCore/parkOrder/in")
    public Mono<ObjectResponse<Boolean>> in(
            ServerHttpRequest request,
            @RequestBody BsParkOrder bsParkOrder) {
        log.info("IN车辆入场，订单记录 {}: bsParkOrder = {}",
                LoggerHelper2.formatEnterLog(request, false), bsParkOrder);

        return parkOrderService.in(bsParkOrder);
    }

    @PostMapping("/dataCore/parkOrder/out")
    public Mono<ObjectResponse<Boolean>> out(
            ServerHttpRequest request,
            @RequestBody BsParkOrder bsParkOrder) {
        log.info("OUT车辆出场，订单记录 {}: bsParkOrder = {}",
                LoggerHelper2.formatEnterLog(request, false), bsParkOrder);

        return parkOrderService.out(bsParkOrder);
    }

    /**
     * 更新充电订单车辆信息 和 订车订单关联的充电订单
     * @param request
     * @param vinCarNoParam
     * @return
     */
    @PostMapping("/dataCore/parkOrder/updateChargeOrderCar")
    public Mono<ObjectResponse<Boolean>> updateChargeOrderCar(
            ServerHttpRequest request,
            @RequestBody VinCarNoParam vinCarNoParam) {
        log.info("修改充电订单对应车牌号 {}: vinCarNoParam = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(vinCarNoParam));

        return parkOrderService.updateChargeOrderCarAndPark(vinCarNoParam);
    }

    @PostMapping("/dataCore/parkOrder/bindAllChargingOrderCar")
    public Mono<ObjectResponse<Integer>> bindAllChargingOrderCar(
            ServerHttpRequest request,
            @RequestBody VinCarNoParam vinCarNoParam) {
        log.info(">>用户所有未支付订单绑定车牌号{}: vinCarNoParam = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(vinCarNoParam));

        return parkOrderService.bindAllChargingOrderCar(vinCarNoParam);
    }

    @PostMapping("/dataCore/parkOrder/checkParkCoupon")
    public Mono<ObjectResponse<ParkCouponVo>> checkParkCoupon(ServerHttpRequest request,
                                                              @RequestParam("orderNo") String orderNo,
                                                              @RequestParam("kwh") BigDecimal kwh) {
        log.info("检查并返回停车优惠券 {}: orderNo = {}, kwh = {}",
                LoggerHelper2.formatEnterLog(request, false), orderNo, kwh);
        return parkOrderService.checkParkCoupon(orderNo, kwh);
    }

    @PostMapping("/dataCore/parkOrder/setParkCouponDuration")
    public Mono<ObjectResponse<Integer>> setParkCouponDuration(ServerHttpRequest request,
                                                              @RequestParam("orderNo") String orderNo,
                                                              @RequestParam("duration") Integer duration) {
        log.info("设置订单的免停车费时间 {}: orderNo = {}, duration = {}",
                LoggerHelper2.formatEnterLog(request, false), orderNo, duration);
        return parkOrderService.setParkCouponDuration(orderNo, duration);
    }

}