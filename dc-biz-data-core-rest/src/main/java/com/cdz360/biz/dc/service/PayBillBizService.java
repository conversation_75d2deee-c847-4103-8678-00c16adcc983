package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcException;
import com.cdz360.base.model.base.exception.DcServerException;
import com.cdz360.base.model.base.param.SortParam;
import com.cdz360.base.model.base.type.OrderType;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.client.InvoiceFeignClient;
import com.cdz360.biz.dc.client.UserFeignClient;
import com.cdz360.biz.dc.domain.TaxInfo;
import com.cdz360.biz.dc.service.invoice.InvoiceProcess;
import com.cdz360.biz.ds.trading.ro.corp.ds.CorpRoDs;
import com.cdz360.biz.ds.trading.ro.corp.ds.UserSyncRoDs;
import com.cdz360.biz.ds.trading.ro.invoice.ds.InvoiceRecordOrderRefRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.PayBillRoDs;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderPayRwDs;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderRwDs;
import com.cdz360.biz.ds.trading.rw.order.ds.PayBillRwDs;
import com.cdz360.biz.model.cus.balance.po.BalanceApplicationPo;
import com.cdz360.biz.model.cus.balance.type.BalanceApplicationStatusType;
import com.cdz360.biz.model.cus.corp.param.ListCorpParam;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.corp.vo.CorpVo;
import com.cdz360.biz.model.cus.wallet.vo.RefundAnalyzeVo;
import com.cdz360.biz.model.finance.type.TaxStatus;
import com.cdz360.biz.model.finance.type.TaxType;
import com.cdz360.biz.model.invoice.type.InvoiceType;
import com.cdz360.biz.model.invoice.type.InvoicedStatus;
import com.cdz360.biz.model.trading.cus.vo.CusRepVo;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.cdz360.biz.model.trading.order.dto.PayBillInvoiceBi;
import com.cdz360.biz.model.trading.order.dto.TaxOrderDto;
import com.cdz360.biz.model.trading.order.param.CzOrderPointParam;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.model.trading.order.param.UpdateOrderInvoicedAmountParam;
import com.cdz360.biz.model.trading.order.param.ZftBillParam;
import com.cdz360.biz.model.trading.order.po.ChargerOrderPayPo;
import com.cdz360.biz.model.trading.order.po.PayBillPo;
import com.cdz360.biz.model.trading.order.type.OldPayType;
import com.cdz360.biz.model.trading.order.type.PayBillStatus;
import com.cdz360.biz.model.trading.order.vo.PayBillAccountDetailVo;
import com.cdz360.biz.model.trading.order.vo.PayBillBi;
import com.cdz360.biz.model.trading.order.vo.PayBillLinkChargeOrderVo;
import com.cdz360.biz.model.trading.order.vo.PayBillUsedDetail;
import com.cdz360.biz.model.trading.order.vo.PayBillUsedInfo;
import com.cdz360.biz.model.trading.order.vo.PayBillVo;
import com.cdz360.biz.model.trading.order.vo.RefundReasonCountVo;
import com.cdz360.biz.model.trading.order.vo.UserBillAccountNameVo;
import com.cdz360.biz.model.trading.order.vo.ZftBillBi;
import com.cdz360.biz.model.trading.order.vo.ZftBillVo;
import com.cdz360.biz.model.trading.site.po.CommPo;
import com.cdz360.biz.model.wallet.vo.RefundReasonVo;
import com.cdz360.biz.utils.feign.pcp.PcpAsyncFeignClient;
import com.chargerlinkcar.framework.common.constant.CheckTaxStatus;
import com.chargerlinkcar.framework.common.constant.OrderStatus;
import com.chargerlinkcar.framework.common.domain.ListPointLogParam;
import com.chargerlinkcar.framework.common.domain.ListPointRecLogParam;
import com.chargerlinkcar.framework.common.domain.ListPointRecParam;
import com.chargerlinkcar.framework.common.domain.PointLog;
import com.chargerlinkcar.framework.common.domain.PointRecDto;
import com.chargerlinkcar.framework.common.domain.PointRecLogDto;
import com.chargerlinkcar.framework.common.domain.PointRecPo;
import com.chargerlinkcar.framework.common.domain.invoice.InvoicedRecordVo;
import com.chargerlinkcar.framework.common.domain.type.PayChannelEnum;
import com.chargerlinkcar.framework.common.domain.type.PointOpType;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrder;
import com.chargerlinkcar.framework.common.domain.vo.OrderBiVo;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.service.DcCusBalanceService;
import com.chargerlinkcar.framework.common.service.PayBillIdGenerator;
import com.chargerlinkcar.framework.common.utils.AssertUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.ResponseConvertUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 * @since 2019/11/5 17:50
 */
@Slf4j
@Service
public class PayBillBizService //implements IPayBillService
{

    private final String CORP_PHONE_PREFIX = "B";

    @Autowired
    private UserSyncRoDs userSyncRoDs;

    @Autowired
    private CorpRoDs corpRoDs;

    @Autowired
    private PayBillRwDs payBillRwDs;

    @Autowired
    private PayBillRoDs payBillRoDs;

    @Autowired
    private ChargerOrderRwDs chargerOrderDs;

    @Autowired
    private ChargerOrderBizService chargerOrderService;

    @Autowired
    private DcCusBalanceService dcCusBalanceService;

    @Autowired
    private PayBillIdGenerator payBillIdGenerator;

    @Autowired
    private InvoiceFeignClient invoiceFeignClient;

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private TRCommercialService trCommercialService;

    @Autowired
    private InvoiceProcess invoiceProcess;

    @Autowired
    private InvoiceRecordOrderRefRoDs invoiceRecordOrderRefRoDs;

    @Autowired
    private PcpAsyncFeignClient pcpAsyncFeignClient;

    @Autowired
    private ChargerOrderPayRwDs chargerOrderPayRwDs;

    public ListResponse<PayBillVo> payBillList(PayBillParam param) {
//        log.info("查询充值记录: param={}", JsonUtils.toJsonString(param));

        // 分页信息
//        Page<PayBillPo> page = null;
//        if (null != param.getIndex() && null != param.getSize()) {
//            page = PageHelper.startPage(param.getIndex(), param.getSize(), Boolean.TRUE.equals(param.getTotal()), false, null);
//            log.info("分页: index={}, size={}", page.getPageNum(), page.getPageSize());
//        }

        if (null != param.getIndex() && null == param.getStart()) {
            // index不是null，start是null，表示用的还是pagehelper的写法，单独处理start
            param.setSize(param.getSize() != null ? param.getSize() : 99);
            param.setIndex(param.getIndex() > 1 ? param.getIndex(): 1);
            // limit offset,rows 和pagehelper分页差异，因此乘之前要减一
            long index = param.getIndex() - 1;
            param.setStart(index * param.getSize());
        }
        ListResponse<PayBillPo> result = payBillRwDs.payBillList(param);
        log.info("查询充值记录：size={}", result.getTotal());

//        if (param.getInCorpInvoice() != null && param.getInCorpInvoice()) {
//            List<PayBillVo> res = this.poMap2Vo(result);
//            return new ListResponse<>(res, null == page ? 0 : page.getTotal());
//        }

//        // 企业客户Id
//        Set<Long> uidList = result.stream().map(PayBillPo::getUserId).collect(Collectors.toSet());
//
//        // 默认扣款账户名称
//        Map<Long, String> corpName;
//        if (CollectionUtils.isNotEmpty(uidList)) {
//            ListCorpParam corpParam = new ListCorpParam();
//            corpParam.setUidList(new ArrayList<>(uidList));
//            ListResponse<CorpVo> corpList = userFeignClient.getCorpList(corpParam);
//            if (CollectionUtils.isNotEmpty(corpList.getData())) {
//                corpName = corpList.getData().stream().collect(
//                        Collectors.toMap(CorpVo::getUid, CorpVo::getCorpName));
//            } else {
//                corpName = new HashMap<>();
//            }
//        } else {
//            corpName = new HashMap<>();
//        }

        if (result.getTotal() == 0) {
            return new ListResponse<>(new ArrayList<>(), 0L);
        }
        List<PayBillVo> res = this.poMap2Vo(result.getData());
//        res.forEach(o -> {
//            // 总金额
//            o.setTotalAmount(o.getAmount().add(o.getFreeAmount()));
//
//            // 账户名称
//            if (PayAccountType.PERSONAL == o.getAccountType()) {
//                o.setPayAccountName("个人账户");
//            } else if (PayAccountType.COMMERCIAL == o.getAccountType()) {
//                TRCommercialPo commPo = trCommercialService.getCommercialById(o.getAccountCode());
//                o.setPayAccountName("商户会员-" + commPo.getCommName());
//            } else if (PayAccountType.CORP == o.getAccountType()) {
//                o.setPayAccountName("企业账户-" + corpName.get(o.getUserId()));
//                o.setCusName(corpName.get(o.getUserId()));
//            } // end if
//        });

        // 获取充值单对应的已完结的流程单号，已被下方代码替换
//        final Map<String, String> procInstIdMap = this.getOrderProcInstIdMap(res
//            .stream()
//            .map(PayBillVo::getOrderId)
//            .filter(StringUtils::isNotBlank)
//            .collect(Collectors.toList()));
//
//        if(procInstIdMap != null) {
//            res.forEach(e -> {
//                if (procInstIdMap.get(e.getOrderId()) != null) {
////                    e.setProcInstId(procInstIdMap.get(e.getOrderId()));
//                    String s = procInstIdMap.get(e.getOrderId());
//                    String[] split = s.split("///");
//                    if (split != null && split.length > 0) {
//                        e.setProcInstId(split[0]);
//                        if (split.length > 1) {
//                            e.setOutAccountName(split[1]);
//                        }
//                    }
//                }
//            });
//        }

        // 获取充值单对应的流程单号，仅在填入数据时才区分已完结和未完结
        List<String> orderIdList = res
            .stream()
            .map(PayBillVo::getOrderId)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIdList)) {
            return new ListResponse<>(res, result.getTotal());
        }
        ListResponse<BalanceApplicationPo> byOrderIds = userFeignClient.findAllByOrderIds(
            orderIdList);
        FeignResponseValidate.check(byOrderIds);
        List<BalanceApplicationPo> orderList = byOrderIds.getData();
        res.forEach(e -> {
            // 增加的按理来说只有一条，保险起见也用max筛选最新一条
            BalanceApplicationPo inFlowByOrderId = orderList.stream()
                .filter(po -> po.getOrderId().equals(e.getOrderId()) && po.getFlowType() != null
                    && po.getFlowType().getCode() == DepositFlowType.IN_FLOW.getCode())
                .max(Comparator.comparing(BalanceApplicationPo::getCreateTime))
                .orElse(null);
            // 减少的取最新一条就好了
            BalanceApplicationPo outFlowByOrderId = orderList.stream()
                .filter(po -> po.getOrderId().equals(e.getOrderId()) && po.getFlowType() != null
                    && po.getFlowType().getCode() == DepositFlowType.OUT_FLOW.getCode())
                .max(Comparator.comparing(BalanceApplicationPo::getCreateTime))
                .orElse(null);
            if (inFlowByOrderId != null && inFlowByOrderId.getStatus() != null) {
                // 原有逻辑，充值且已完成的审批流设置审批流单号
                if (BalanceApplicationStatusType.FINISH.equals(inFlowByOrderId.getStatus())
                    || BalanceApplicationStatusType.REJECT.equals(inFlowByOrderId.getStatus())
                    || BalanceApplicationStatusType.PASSED.equals(inFlowByOrderId.getStatus())) {
                    e.setOutAccountName(inFlowByOrderId.getOutAccountName());
                    e.setProcInstId(inFlowByOrderId.getProcInstId());
                }
            }
            // 新增逻辑，减少且在流程中的审批流设置运行中的审批流单号
            // NEW表示占用中，如果是那几个已完成的状态，会直接变更可开票金额
            if (outFlowByOrderId != null && BalanceApplicationStatusType.NEW.equals(
                outFlowByOrderId.getStatus())
                && outFlowByOrderId.getFlowType().getCode()
                == DepositFlowType.OUT_FLOW.getCode()) {
                e.setRunProcInstId(outFlowByOrderId.getProcInstId());
            }
        });


        return new ListResponse<>(res, result.getTotal());
    }

    /**
     * 直付通对账订单列表
     *
     * @param param
     * @return
     */
    public Mono<ListResponse<ZftBillVo>> zftBillList(ZftBillParam param) {
        log.info("直付通对账订单记录: param={}", JsonUtils.toJsonString(param));

        return Mono.just(param)
            .doOnNext(p -> {
                // 参数限制
                if (param.getStart() == null) {
                    param.setStart(0L);
                }

                if (param.getSize() == null || param.getSize() > 1000) {
                    param.setSize(10);
                }
            })
            .map(payBillRwDs::zftBillList)
            .map(RestUtils::buildListResponse)
            .doOnNext(res -> {
                if (param.getTotal() != null && param.getTotal()) {
                    res.setTotal(payBillRwDs.zftBillCount(param));
                }
            });

//        Long total = 0L;
//
//        //判断是否需要计算总数
//        if (param.getTotal() != null && param.getTotal()) {
//            total = payBillRwDs.zftBillCount(param);
//        }
//
//        if(param.getStart() == null) {
//            param.setStart(0L);
//        }
//        if (param.getSize() == null ){
//            param.setSize(10);
//        }
//        List<ZftBillVo> result = payBillRwDs.zftBillList(param);
//        log.info("查询直付通对账记录：size={}", result.size());
//
//        return new ListResponse<>(result, total);
    }

    public ObjectResponse<UserBillAccountNameVo> userBillAccountName(PayBillParam param) {
        UserBillAccountNameVo result = payBillRwDs.userBillAccountName(param);
        log.info("result = {}", JsonUtils.toJsonString(result));
        return new ObjectResponse<>(result);
    }

    public ListResponse<PayBillInvoiceBi> invoiceOrderList(PayBillParam param) {
        log.info("查询充值记录: param={}", JsonUtils.toJsonString(param));

        final ListResponse<PayBillInvoiceBi> response = payBillRoDs.invoiceBi(param);

        if(CollectionUtils.isEmpty(response.getData())) {
            return response;
        }

//        final List<String> orderIdList = response.getData()
//            .stream()
//            .map(PayBillInvoiceBi::getOrderId)
//            .filter(StringUtils::isNotBlank)
//            .collect(Collectors.toList());
//
//        if(CollectionUtils.isEmpty(orderIdList)) {
//            return response;
//        }
//
//        // 获取申请单号
//        final ListResponse<BalanceApplicationPo> byOrderIds = userFeignClient.getByOrderIds(
//            orderIdList);
//        FeignResponseValidate.check(byOrderIds);
//        final List<BalanceApplicationPo> orderList = byOrderIds.getData();
//
//        if(CollectionUtils.isEmpty(orderList)) {
//            return response;
//        }
//
//        final List<BalanceApplicationPo> orderProcInstId = orderList.stream()
//            .filter(e -> StringUtils.isNotBlank(e.getProcInstId()))
//            .collect(Collectors.toList());
//        final Map<String, String> procInstIdMap = orderProcInstId.stream()
//            .collect(Collectors.toMap(BalanceApplicationPo::getOrderId,
//                BalanceApplicationPo::getProcInstId));

        // 获取充值单对应的已完结的流程单号，已被下方代码替换
//        final Map<String, String> procInstIdMap = this.getOrderProcInstIdMap(response.getData()
//            .stream()
//            .map(PayBillInvoiceBi::getOrderId)
//            .filter(StringUtils::isNotBlank)
//            .collect(Collectors.toList()));
//        if(procInstIdMap == null) {
//            return response;
//        }
//
//        response.getData()
//            .forEach(e -> {
//                if(procInstIdMap.get(e.getOrderId()) != null) {
//                    String s = procInstIdMap.get(e.getOrderId());
//                    String[] split = s.split("///");
//                    if (split != null && split.length > 0) {
//                        e.setProcInstId(split[0]);
//                        if (split.length > 1) {
//                            e.setOutAccountName(split[1]);
//                        }
//                    }
//                }
//        });

        // 获取充值单对应的流程单号，仅在填入数据时才区分已完结和未完结
        List<String> orderIdList = response.getData()
            .stream()
            .map(PayBillInvoiceBi::getOrderId)
            .filter(StringUtils::isNotBlank)
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIdList)) {
            return response;
        }
        ListResponse<BalanceApplicationPo> byOrderIds = userFeignClient.findAllByOrderIds(
            orderIdList);
        FeignResponseValidate.check(byOrderIds);
        List<BalanceApplicationPo> orderList = byOrderIds.getData();
        response.getData().forEach(e -> {
            // 增加的按理来说只有一条，保险起见也用max筛选最新一条
            BalanceApplicationPo inFlowByOrderId = orderList.stream()
                .filter(po -> po.getOrderId().equals(e.getOrderId()) && po.getFlowType() != null
                    && po.getFlowType().getCode() == DepositFlowType.IN_FLOW.getCode())
                .max(Comparator.comparing(BalanceApplicationPo::getCreateTime))
                .orElse(null);
            // 减少的取最新一条就好了
            BalanceApplicationPo outFlowByOrderId = orderList.stream()
                .filter(po -> po.getOrderId().equals(e.getOrderId()) && po.getFlowType() != null
                    && po.getFlowType().getCode() == DepositFlowType.OUT_FLOW.getCode())
                .max(Comparator.comparing(BalanceApplicationPo::getCreateTime))
                .orElse(null);
            if (inFlowByOrderId != null && inFlowByOrderId.getStatus() != null) {
                // 原有逻辑，充值且已完成的审批流设置审批流单号
                if (BalanceApplicationStatusType.FINISH.equals(inFlowByOrderId.getStatus())
                    || BalanceApplicationStatusType.REJECT.equals(inFlowByOrderId.getStatus())
                    || BalanceApplicationStatusType.PASSED.equals(inFlowByOrderId.getStatus())) {
                    e.setOutAccountName(inFlowByOrderId.getOutAccountName());
                    e.setProcInstId(inFlowByOrderId.getProcInstId());
                }
            }
            // 新增逻辑，减少且在流程中的审批流设置运行中的审批流单号
            // NEW表示占用中，如果是那几个已完成的状态，会直接变更可开票金额
            if (outFlowByOrderId != null && BalanceApplicationStatusType.NEW.equals(
                outFlowByOrderId.getStatus())
                && outFlowByOrderId.getFlowType().getCode()
                == DepositFlowType.OUT_FLOW.getCode()) {
                e.setRunProcInstId(outFlowByOrderId.getProcInstId());
            }
        });

        return response;
    }

    /**
     * 获取充值单对应表
     * @param orderIdList
     * @return
     */
    private Map<String, String> getOrderProcInstIdMap(List<String> orderIdList) {
//        final List<String> orderIdList = list
//            .stream()
//            .map(PayBillInvoiceBi::getOrderId)
//            .filter(StringUtils::isNotBlank)
//            .collect(Collectors.toList());

        if(CollectionUtils.isEmpty(orderIdList)) {
            return null;
        }

        // 获取申请单号
        final ListResponse<BalanceApplicationPo> byOrderIds = userFeignClient.getByOrderIds(
            orderIdList);
        FeignResponseValidate.check(byOrderIds);
        final List<BalanceApplicationPo> orderList = byOrderIds.getData();

        if(CollectionUtils.isEmpty(orderList)) {
            return null;
        }

        final List<BalanceApplicationPo> orderProcInstId = orderList.stream()
            .filter(e -> StringUtils.isNotBlank(e.getProcInstId()))
            .collect(Collectors.toList());
        final Map<String, String> procInstIdMap = orderProcInstId.stream()
//            .filter(e -> com.cdz360.biz.model.cus.balance.type.DepositFlowType.IN_FLOW.equals(e.getFlowType()))
            .collect(Collectors.toMap(BalanceApplicationPo::getOrderId, e -> e.getProcInstId() + "///" + e.getOutAccountName()));
        return procInstIdMap;
    }

    public ObjectResponse<OrderBiVo> invoiceOrderBiForCorp(PayBillParam param) {
        log.info("查询充值记录汇总: param={}", JsonUtils.toJsonString(param));
        return payBillRoDs.invoiceOrderBiForCorp(param);
    }

    public List<PayBillBi> payBillBi(PayBillParam param) {
        log.info("充值记录数据统计: param={}", param);
        return payBillRwDs.payBillBi(param);
    }

    /**
     * 直付通数据统计
     *
     * @param param
     * @return
     */
    public List<ZftBillBi> zftBillBi(ZftBillParam param) {
        log.info("直付通数据统计: param={}", param);
        List<ZftBillBi> result = payBillRwDs.zftBillBi(param);
        List<Long> typeList = result.stream().map(e -> e.getFlowInAccountType())
            .collect(Collectors.toList());

        if (!typeList.contains(1L)) {
            ZftBillBi zftBillBi = new ZftBillBi();
            zftBillBi.setFlowInAccountType(1L);
            result.add(zftBillBi);
        }

        if (!typeList.contains(2L)) {
            ZftBillBi zftBillBi = new ZftBillBi();
            zftBillBi.setFlowInAccountType(2L);
            result.add(zftBillBi);
        }

        return result;
    }

    public Integer updateById(PayBillPo po) {
        log.info("更新充值记录: po={}", po);
        po.setOrderId(null); // 查询条件控制

        Long id = po.getId();
        if (null == id) {
            log.info("充值记录id值没有提供");
            throw new DcArgumentException("请提供修改的充值记录的Id值");
        }

        // 记录是否存在
        PayBillPo sqlPo = payBillRwDs.findById(id);
        if (null == sqlPo) {
            log.info("充值记录id在数据库中没有对应记录: id={}", id);
            throw new DcArgumentException("数据中不存在该充值记录,请确认充值记录的Id值是否正确");
        }

        //修改开票状态为已开票时的判断逻辑
        if (TaxStatus.YES.equals(po.getTaxStatus()) && !TaxType.UNKNOWN.equals(po.getTaxType()) &&
            !sqlPo.getTaxType().equals(po.getTaxType())) {
            ObjectResponse<CheckTaxStatus> res = checkTaxStatus(sqlPo.getOrderId());
            if (CheckTaxStatus.HAS_WAITING.equals(res.getData())) {
                throw new DcServerException("资金块存在审核中的开票订单");
            }
            if (CheckTaxStatus.NONE_WAITING_HAS_COMPLETED.equals(res.getData())) {
                throw new DcServerException(
                    "资金块存在已开票的充电订单，不可以再次调整充值开票状态");
            }
        }

        // 更新后需要调整充电订单的可开票金额
        this.resetChargerOrderInvoiceAmount(po, sqlPo);

        int i = payBillRwDs.update(po);
        log.info("result={}", i);

        return i;
    }

    public Integer updateByOrderId(PayBillPo po) {
        log.info("更新充值记录: po = {}", po);
        po.setId(null); // 查询条件控制

        String orderId = po.getOrderId();
        if (null == orderId) {
            log.info("充值记录id值没有提供");
            throw new DcArgumentException("请提供修改的充值记录的Id值");
        }

        // 记录是否存在
        PayBillPo sqlPo = payBillRwDs.findByOrderId(orderId, false);
        if (null == sqlPo) {
            log.info("充值记录id在数据库中没有对应记录: orderId={}", orderId);
            throw new DcArgumentException("数据中不存在该充值记录,请确认充值订单号是否正确");
        }

        // 计算开票状态
        TaxStatus calTaxStatus = this.taxStatus(
            null, sqlPo.getAmount(), sqlPo.getFreeAmount(), sqlPo.getAmount(),
            sqlPo.getInvoicedAmount());

        // 不可开票金额和未开票金额调整
        List<PayBillPo> poList = payBillRwDs.getPayBillOfRefBillNo(orderId);
        BigDecimal outFlowAmount = poList.stream()
            .map(PayBillPo::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal outFlowFreeAmount = poList.stream()
            .map(PayBillPo::getFreeAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 发票状态不可逆: 产品
        // 已开票种类可以调整
        log.info("发票状态变更: taxType={}, calTaxStatus = {}, sqlPoTaxType={}",
            po.getTaxType(), calTaxStatus, sqlPo.getTaxType());
        if (null != po.getTaxType()) {
            log.debug("未开票可以修改成任何状态，已开票仅可以调整发票种类(税种)");
            if ((TaxStatus.YES.equals(sqlPo.getTaxStatus()) || TaxStatus.YES.equals(calTaxStatus))
                && !TaxStatus.YES.equals(po.getTaxStatus())) {
                log.info("该充值记录已开票，不可再次修改发票状态: taxType={}", po.getTaxType());
                throw new DcArgumentException("该充值记录已开票，不可再次修改");
            }

            if (TaxStatus.PART.equals(calTaxStatus) && !TaxStatus.PART.equals(po.getTaxStatus())) {
                throw new DcArgumentException("该充值记录已经是部分开票, 不支持开票状态变更");
            }
        }

        // 是否可以变更开票状态校验
        // 产品: 存在开票中的充电订单不能进行调整
        if (TaxStatus.NO.equals(calTaxStatus) && TaxStatus.YES.equals(po.getTaxStatus())) {
            ObjectResponse<CheckTaxStatus> res = checkTaxStatus(sqlPo.getOrderId());
            if (CheckTaxStatus.HAS_WAITING.equals(res.getData())) {
                throw new DcServerException(
                    "资金块存在审核中的开票订单，不可以再次调整充值开票状态");
            }
            if (CheckTaxStatus.NONE_WAITING_HAS_COMPLETED.equals(res.getData())) {
                throw new DcServerException(
                    "资金块存在已开票的充电订单，不可以再次调整充值开票状态");
            }

            if (DecimalUtils.eq(outFlowAmount, po.getAmount())) {
                throw new DcArgumentException("资金块无可用实际金额，不允许调整为已开票");
            }

            if (TaxStatus.YES.equals(po.getTaxStatus())) {
                po.setInvoicedAmount(sqlPo.getAmount().subtract(outFlowAmount));
            }
        }

        // 更新后需要调整充电订单的可开票金额
        this.resetChargerOrderInvoiceAmount(po, sqlPo);

        // 发票状态调整
        if (!TaxStatus.NO.equals(calTaxStatus)) {
            po.setTaxStatus(null);
        }

        int i = payBillRwDs.update(po);
        log.info("result={}", i);

        return i;
    }

    private void resetChargerOrderInvoiceAmount(PayBillPo po, PayBillPo sqlPo) {
        log.info(">> 充值充电订单开票金额: po.taxStatus = {}, sqlPo.taxStatus = {}",
            po.getTaxStatus(), sqlPo.getTaxStatus());
        if (TaxStatus.YES.equals(po.getTaxStatus()) && TaxStatus.NO.equals(sqlPo.getTaxStatus())) {
            PayBillUsedDetail detail = this.pointRecLog(po.getOrderId(), null);
            log.info("detail = {}", detail);
            if (CollectionUtils.isNotEmpty(detail.getChargeOrderVoList())) {
//                List<String> orderNoList = detail.getChargeOrderVoList().stream()
//                        .map(PayBillLinkChargeOrderVo::getOrderNo)
//                        .collect(Collectors.toList());
//                if (CollectionUtils.isEmpty(orderNoList)) {
//                    log.info("<< 订单列表为空");
//                    return;
//                }
//
//                int result = chargerOrderService.resetChargerOrderInvoiceAmount(orderNoList);
//                log.info("重置充电订单记录: result = {}", result);

                // 充电记录处理
                List<UpdateOrderInvoicedAmountParam> updateList = detail.getChargeOrderVoList()
                    .stream().map(order -> {
                        UpdateOrderInvoicedAmountParam amountParam = new UpdateOrderInvoicedAmountParam();
                        amountParam.setOpType(UpdateOrderInvoicedAmountParam.OpType.ADD)
                            .setAmount(order.getAmount())
                            .setOrderNo(order.getOrderNo());
                        return amountParam;
                    }).collect(Collectors.toList());

                invoiceProcess.orderInvoiceDataProcess(updateList);
            }
        }
        log.info("<< ");
    }

    public PayBillPo findById(Long id) {
        log.info("根据充值记录的ID获取记录信息: id={}", id);
        AssertUtil.notNull(id, "充值记录的ID不能为空");
        return payBillRwDs.findById(id);
    }

    public int insert(PayBillPo po) {
        log.info("新增充值记录: po={}", po);
        if (null == po.getId()) {
            po.setId(payBillIdGenerator.getNextId());
        }
        log.info("充值记录: id={}", po.getId());

        // 数据库必填字段
        if (po.getPayChannel() == PayChannel.WXPAY) {
            po.setChannel(PayChannelEnum.WX_LITE.getName());
        } else if (po.getPayChannel() == PayChannel.ALIPAY) {
            po.setChannel(PayChannelEnum.ALIPAY_APPLET.getName());
        } else {
            po.setChannel(PayChannelEnum.UNDEFINED.getName());
        }

        // pay_type
        if (po.getAccountType() == PayAccountType.PERSONAL ||
            po.getAccountType() == PayAccountType.CORP) {
            po.setPayType(OldPayType.BALANCE_RECHARGE.getCode());
        } else if (po.getAccountType() == PayAccountType.COMMERCIAL) {
            po.setPayType(OldPayType.COMMERCIAL_RECHARGE.getCode());
        } else if (po.getAccountType() == PayAccountType.PREPAY) {
            po.setPayType(OldPayType.PREPAY_RECHARGE.getCode());
        } else {
            po.setPayType(OldPayType.UNKNOWN.getCode());
        }

        // subject / body
        po.setSubject("充值: " + po.getOrderId());
        po.setBody("充值: " + po.getOrderId());
        if (null == po.getCreateTime()) {
            po.setCreateTime(new Date());
        }

        // 如果是减少操作，则需要调整引用充值记录的状态
        if (StringUtils.isNotBlank(po.getRefBillNo())) {
            List<PayBillPo> payBillList = payBillRwDs.getPayBillList(List.of(po.getRefBillNo()));
            if (CollectionUtils.isNotEmpty(payBillList)) {
                PayBillPo refPo = payBillList.get(0);
                if (null == po.getOpName() ||
                    "--".equals(po.getOpName())) { // "--" 一键退款写入的，需要调整
                    po.setOpName(refPo.getOpName());
                }

                if (StringUtils.isNotBlank(refPo.getZftName())) {
                    po.setWxSubMchId(refPo.getWxSubMchId())
                        .setAlipaySubMchId(refPo.getAlipaySubMchId())
                        .setZftName(refPo.getZftName());
                }

                // 充值资金块剩余信息
                Map<String, BigDecimal> billRemain = this.getBillRemain(
                    String.valueOf(po.getUserId()), po.getTopCommId(), po.getAccountType(),
                    po.getAccountCode(), refPo.getFlowSeqNo());
                if (DecimalUtils.isZero(billRemain.get("remainCost")) &&
                    DecimalUtils.isZero(billRemain.get("remainFree"))) {
                    int u = payBillRwDs.updateStatus(
                        refPo.getOrderId(), refPo.getStatus(), PayBillStatus.EXHAUST.getCode());
                    log.info("当前资金块已经消耗完: u = {}", u);
                }
            }

            // 更新充值记录减少金额
            if (null != po.getStatus() &&
                po.getStatus() == PayBillStatus.PAID.getCode()) {
                payBillRwDs.appendOutFlow(po.getRefBillNo(), po.getAmount(), po.getFreeAmount());
            }
        }

        // 已开票金额调整
        if (po.getTaxStatus() == TaxStatus.YES) {
            po.setInvoicedAmount(po.getAmount());
        }

//        if (DecimalUtils.isZero(po.getAmount())) {
//            po.setTaxStatus(TaxStatus.CANT);
//        }

        int i = payBillRwDs.insert(po);
        log.info("result={}", i);
        return i;
    }

    private List<PointRecDto> getPayBillRecLog(PayBillPo po) {
        // 获取充值订单关联的资金块明细日志
        String flowSeqNo = po.getFlowSeqNo();
        if (null == flowSeqNo) {
            return List.of();
        }

        List<String> seqNoList = Arrays.asList(flowSeqNo.split(","));
        ListPointRecParam param = new ListPointRecParam();
        param.setUid(po.getUserId().toString());

        // @Nathan 商户会员的充值需要填入商户Id
        if (PayAccountType.COMMERCIAL.equals(po.getAccountType())) {
            param.setSubUid(po.getAccountCode().toString());
        } else {
            param.setSubUid(po.getTopCommId().toString());
        }

        param.setSeqNumList(seqNoList.stream().map(Long::valueOf).collect(Collectors.toList()));
        param.setType(ResponseConvertUtils.formatPointType(po.getAccountType(), po.getTopCommId()));
        param.setFetchRecLogs(true);

        // type: 1: increase; 2: decrease; 3: freeze; 4: unfreeze; 5: expire; 6: cancel
        List<PointRecDto> recDtoList = dcCusBalanceService.listPointRecord(param);
        log.info("资金块详情: size={}", recDtoList.size());
        return recDtoList;
    }

    public PayBillUsedDetail pointRecLog(String orderId, String orderNo) {
        log.info("获取充值订单关联充电订单资金块详情: orderId={}, orderNo={}", orderId, orderNo);
        if (StringUtils.isBlank(orderId)) {
            throw new DcArgumentException("请提供充值订单号");
        }

        PayBillUsedDetail result = new PayBillUsedDetail();

        PayBillPo po = payBillRwDs.findByOrderId(orderId, false);
        if (null == po) {
            log.info("查无该充值记录: orderId={}", orderId);
            return result;
        }

        if (StringUtils.isBlank(po.getFlowSeqNo())) {
            log.info("该充值记录没有关联的资金块信息: orderId={}", orderId);
            return result;
        }

        List<PointRecDto> recDtoList = this.getPayBillRecLog(po);

        // 冻结，减少，解冻
        List<Integer> logTypes = List.of(PointOpType.FREEZE.code(),
            PointOpType.DECREASE.code(),
            PointOpType.UNFREEZE.code());
        if (CollectionUtils.isNotEmpty(recDtoList)) {
            List<PointRecLogDto> recLogDtoList = recDtoList.stream()
                .filter(i -> i.getRecLogs() != null)
                .flatMap(i -> i.getRecLogs().stream())
                .filter(lg -> logTypes.contains(lg.getType()))
                .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(recLogDtoList)) {
                // 从明细日志获取订单号列表(可能存在退款充值订单号)
                Set<String> orderNoList = recLogDtoList.stream()
                    .map(PointRecLogDto::getOrderNo)
                    .collect(Collectors.toSet());

                // 数据库中的订单信息
                // 过滤订单为零的充电订单
                List<ChargerOrder> orderList = chargerOrderService.findChargerOrder(
                    new ArrayList<>(orderNoList));
                log.info("order list size={}", orderList.size());

                // 根据订单号列表查询发票信息
                List<Long> iList = orderList.stream().map(ChargerOrder::getInvoicedId)
                    .collect(Collectors.toList());
                List<TaxInfo> taxInfoList = new ArrayList<>();
                ListResponse<InvoicedRecordVo> response = invoiceFeignClient.findByInvoiceIds(
                    iList);
                List<InvoicedRecordVo> voList = response.getData();
                if (!voList.isEmpty()) {
                    taxInfoList = voList.stream().map(vo -> {
                        TaxInfo info = new TaxInfo();
                        info.setId(vo.getId())
                            .setTaxNo(vo.getInvoiceNumber())
                            .setStatus(vo.getInvoicedStatus());

                        // 发票类别
                        InvoiceType invoiceType = vo.getInvoiceType();
                        switch (invoiceType) {
                            case ENTER_PROFESSION:
                                info.setTaxType(TaxType.SPECIAL_VAT);
                                break;
                            case PER_COMMON:
                                info.setTaxType(TaxType.NORMAL_TAX);
                                break;
                            case ENTER_COMMON:
                                info.setTaxType(TaxType.PREPAY_TAX);
                                break;
                            default:
                                info.setTaxType(TaxType.UNKNOWN);
                        }
                        return info;
                    }).collect(Collectors.toList());
                }

                // 充电订单列表
                result.setChargeOrderVoList(
                    this.recLogMap2Vo(po, recLogDtoList, orderList, taxInfoList));
            }
        }

        // 资金块使用(充值记录)
        result.setUsedInfo(this.map2BillUsedInfo(po));

//        if (result.getChargeOrderVoList().isEmpty()) {
//            log.info("统计结束");
//            return result;
//        }

        // 资金块账户减少/退款
        List<PayBillPo> poList = payBillRwDs.getPayBillOfRefBillNo(orderId);
        List<PayBillVo> voList = this.poMap2Vo(poList);
        voList.add(0, this.poMap2Vo(po));
        result.setPayBillVoList(voList);

        // 不可开票金额和未开票金额调整
        BigDecimal outFlowAmount = poList.stream()
            .map(PayBillPo::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal outFlowFreeAmount = poList.stream()
            .map(PayBillPo::getFreeAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        PayBillUsedInfo usedInfo = result.getUsedInfo();
        usedInfo.setNotTaxTotal(usedInfo.getNotTaxTotal().subtract(outFlowFreeAmount))
            .setUnTaxTotal(usedInfo.getUnTaxTotal().subtract(outFlowAmount));
//        if (po.getTaxStatus() != TaxStatus.NO) {
//            usedInfo.setTaxTotal(po.getAmount().subtract(outFlowAmount));
//            usedInfo.setUnTaxTotal(BigDecimal.ZERO.add(outFlowAmount));
//        } else {
//            usedInfo.setUnTaxTotal(po.getAmount());
//        }
//        usedInfo.setTaxTotal(po.getInvoicedAmount())
//                .setUnTaxTotal(po.getAmount().subtract(po.getInvoicedAmount()));

        List<PayBillLinkChargeOrderVo> chargeOrderVoList = result.getChargeOrderVoList();
        chargeOrderVoList.forEach(vo -> {
            log.info("order status={}", vo.getOrderStatus());
            if (vo.getOrderStatus() == OrderStatus.ORDER_STATUS_RECEIVE_MONEY) {
                // 已结算
                usedInfo.setSettlementTotal(
                    usedInfo.getSettlementTotal()
                        .add(vo.getAmount())
                        .add(vo.getFreeAmount()));

//                // 是否开票
//                log.info("tax type={}", vo.getTaxType());
//                if (po.getTaxStatus() == TaxStatus.NO) {
//                    if (vo.getTaxStatus() == TaxStatus.YES) {
//                        // 未开票
//                        usedInfo.setUnTaxTotal(usedInfo.getUnTaxTotal().subtract(vo.getAmount()));
//                        // 已开票
//                        usedInfo.setTaxTotal(usedInfo.getTaxTotal().add(vo.getAmount()));
//                    }
//                }
            } else {
                // 冻结金额
                usedInfo.setFrozenTotal(
                    usedInfo.getFrozenTotal()
                        .add(vo.getAmount())
                        .add(vo.getFreeAmount()));

                // 赠送金额: 恒等于充值记录的赠送金额，与充电订单使用充值记录的赠送没有关系
//                usedInfo.setFreeAmount(usedInfo.getFreeAmount().add(vo.getFreeAmount()));
            }
        });
//        result.getUsedInfo()
//                .setSettlementTotal() // 已结算
//                .setTaxTotal()    // 已结算 已开票
//                .setFrozenTotal() // 未结算 冻结金额(实际和赠送)
//                .setNotTaxTotal() // 未结算 赠送金额
//                .setUnTaxTotal(); // 已结算 未开票

//        result.setUsedInfo(usedInfo);

        log.info("已结算: {}, 已开票: {}, 冻结金额: {}, 不可开票: {}, 未开票: {}",
            usedInfo.getSettlementTotal(), usedInfo.getTaxTotal(),
            usedInfo.getFrozenTotal(), usedInfo.getNotTaxTotal(), usedInfo.getUnTaxTotal());

        return result;
    }

    private PayBillUsedInfo map2BillUsedInfo(PayBillPo po) {
        PayBillUsedInfo info = new PayBillUsedInfo();
        info.setOrderId(po.getOrderId())
            .setTaxType(po.getTaxType()) // 需要根据状态进行调整
            .setTaxNo(po.getTaxNo())
            .setTaxStatus(this.taxStatus(
                po.getOrderId(), po.getAmount(), po.getFreeAmount(), po.getAmount(),
                po.getInvoicedAmount()))
            .setAmount(po.getAmount())
            .setFreeAmount(po.getFreeAmount());

        // 充值资金块剩余信息
        Map<String, BigDecimal> billRemain = this.getBillRemain(
            String.valueOf(po.getUserId()), po.getTopCommId(),
            po.getAccountType(), po.getAccountCode(), po.getFlowSeqNo());

        // 实际余额
        info.setAvailable(billRemain.get("remainCost").add(billRemain.get("remainFree")))
            .setCostAvailable(billRemain.get("remainCost"));

        // 初始化
        info.setSettlementTotal(BigDecimal.ZERO)
            .setFrozenTotal(BigDecimal.ZERO);

        // 充值记录使用情况
        info.setTaxTotal(po.getInvoicedAmount())
            .setNotTaxTotal(po.getFreeAmount())
            .setUnTaxTotal(po.getAmount().subtract(po.getInvoicedAmount()));

        return info;
    }

    /**
     * 根据充值单号获取对应充电单实际消费金额
     * @param czOrderId 充值单号
     * @return {@code <充电单号，实际消费金额>}
     */
    public Optional<Map<String, BigDecimal>> getOrderCostAmountMapByCzOrderId(String czOrderId) {
        log.info("getOrderCostAmountMapByCzOrderId. czOrderId={}", czOrderId);
        if (StringUtils.isBlank(czOrderId)) {
            return Optional.empty();
        }

        PayBillPo po = payBillRwDs.findByOrderId(czOrderId, false);
        if (null == po) {
            log.info("查无该充值记录: czOrderId={}", czOrderId);
            return Optional.empty();
        }

        if (StringUtils.isBlank(po.getFlowSeqNo())) {
            log.info("该充值记录没有关联的资金块信息: czOrderId={}", czOrderId);
            return Optional.empty();
        }

        Map<String, BigDecimal> orderCostAmountMap = new HashMap<>();

        List<PointRecDto> recDtoList = this.getPayBillRecLog(po);
        List<Integer> logTypes = List.of(PointOpType.DECREASE.code());
        if (CollectionUtils.isNotEmpty(recDtoList)) {
            recDtoList.stream()
                .filter(i -> i.getRecLogs() != null)
                .flatMap(i -> i.getRecLogs().stream())
                .filter(lg -> logTypes.contains(lg.getType()))
                .collect(Collectors.groupingBy(PointRecLogDto::getOrderNo))
                .forEach((k, v) -> {
                    // 消耗实际成本
                    BigDecimal deltaCost = v.stream()
                        .filter(recLog -> recLog.getType() == PointOpType.DECREASE.code())
                        .map(PointRecLogDto::getDeltaCost)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    orderCostAmountMap.put(k, deltaCost);
                });
        }
        return Optional.of(orderCostAmountMap);
    }

    public PayBillAccountDetailVo accountDetail(String orderId) {
        log.info("获取充值记录充值前的账户信息: orderId={}", orderId);
        if (StringUtils.isBlank(orderId)) {
            throw new DcArgumentException("请提供充值订单号");
        }

        PayBillAccountDetailVo vo = new PayBillAccountDetailVo();
        vo.setFrozenCost(BigDecimal.ZERO)
            .setFrozen(BigDecimal.ZERO)
            .setCost(BigDecimal.ZERO)
            .setAvailable(BigDecimal.ZERO);

        PayBillPo po = payBillRwDs.findByOrderId(orderId, false);
        if (null == po) {
            log.info("查无该充值记录: orderId={}", orderId);
            return vo;
        }

        String flowSeqNo = po.getFlowSeqNo();
        if (null == flowSeqNo) {
            log.info("该充值记录没有关联的资金块信息: orderId={}", orderId);
            return vo;
        }

        // 最小序列号的账户信息
        Optional<Long> target = Arrays.stream(flowSeqNo.split(","))
            .map(Long::valueOf).min(Long::compareTo);
        if (target.isEmpty()) {
            log.info("充值对应的资金块明细seq不存在: orderId={}", orderId);
            return vo;
        }

        // seqNo == 1 表示第一条充值
        if (target.get() < 2) {
            log.info("该充值是第一条充值");
            return vo;
        }

        ListPointLogParam param = new ListPointLogParam();
        param.setUid(po.getUserId().toString());

        // @Nathan 商户会员的充值需要填入商户Id
        if (PayAccountType.COMMERCIAL.equals(po.getAccountType())) {
            param.setSubUid(po.getAccountCode().toString());
        } else {
            param.setSubUid(po.getTopCommId().toString());
        }

        param.setSeqNum(target.get() - 1); // 最小序列号的上一个序列号
        param.setType(ResponseConvertUtils.formatPointType(po.getAccountType(), po.getTopCommId()));
        List<PointLog> logList = dcCusBalanceService.listPointLog(param);
        if (logList.isEmpty()) {
            log.info("资金块明细为空");
            return vo;
        }

        // 组装返回数据(只有一条数据)
        return this.pointLogMap2Vo(logList.get(0));
    }

    private List<PayBillVo> poMap2Vo(List<PayBillPo> poList) {
        return poList.stream().map(this::poMap2Vo).collect(Collectors.toList());
    }

    private PayBillVo poMap2Vo(PayBillPo po) {
        PayBillVo vo = new PayBillVo();
        BeanUtils.copyProperties(po, vo);

        vo.setTotalAmount(po.getAmount().add(po.getFreeAmount()));

        // 开票状态
        if (DepositFlowType.IN_FLOW.equals(po.getFlowType())) {
            vo.setTaxStatus(this.taxStatus(
                po.getOrderId(), po.getAmount(), po.getFreeAmount(), po.getAmount(),
                po.getInvoicedAmount()));
        } else {
            vo.setTaxStatus(TaxStatus.CANT);
        }

        if(TaxStatus.NO.equals(vo.getTaxStatus()) ||
            TaxStatus.PART.equals(vo.getTaxStatus())) {
            // 计算可开票金额
            vo.setCanInvoiceAmount(vo.getAmount()
                .subtract(vo.getOutFlowAmount())
                .subtract(vo.getInvoicedAmount()));
        }

        // 手机号调整
        if (null != vo.getCusPhone()) {
            vo.setCusPhone(vo.getCusPhone().replace(CORP_PHONE_PREFIX, ""));
        }
        return vo;
    }

    private List<PayBillLinkChargeOrderVo> recLogMap2Vo(
        PayBillPo po, List<PointRecLogDto> recLogList,
        List<ChargerOrder> orderList, List<TaxInfo> taxInfoList) {
        // 订单状态
        Map<String, Integer> orderStatusMap = orderList.stream()
            .collect(Collectors.toMap(ChargerOrder::getOrderNo, ChargerOrder::getStatus));

        // 分情况统计数据
        List<PayBillLinkChargeOrderVo> result = new ArrayList<>();
        recLogList.stream()
            .filter(i -> orderStatusMap.get(i.getOrderNo()) != null)
            .collect(Collectors.groupingBy(PointRecLogDto::getOrderNo))
            .forEach((k, v) -> {
                // k --> 充电订单号
                // v --> 操作流水
                PayBillLinkChargeOrderVo vo = this.recLogMap2Vo(k, v, po, orderStatusMap.get(k),
                    orderList, taxInfoList);
                // 没有使用资金块金额则过滤
                if (DecimalUtils.gtZero(vo.getAmount()) || DecimalUtils.gtZero(
                    vo.getFreeAmount())) {
                    result.add(vo);
                }
            });
        return result;
    }

    /**
     * 数据转换
     *
     * @param k           充电订单号
     * @param v           record log
     * @param po
     * @param orderStatus
     * @param orderList
     * @param taxInfoList
     * @return
     */
    private PayBillLinkChargeOrderVo recLogMap2Vo(
        String k, List<PointRecLogDto> v,
        PayBillPo po, Integer orderStatus,
        List<ChargerOrder> orderList, List<TaxInfo> taxInfoList) {

        // 充电订单
        PayBillLinkChargeOrderVo vo = new PayBillLinkChargeOrderVo();
        vo.setOrderNo(k)
            .setAmount(po.getAmount())
            .setFreeAmount(po.getFreeAmount());

        // 手机号
        if (StringUtils.isNotEmpty(po.getCusPhone())) {
            vo.setPhone(po.getCusPhone().replace(CORP_PHONE_PREFIX, ""));
        }

        // 发票信息
        // (1)充值已经开发票
        // (2)订单开票信息
        // 充电订单的开票状态与充值无关: 20200820
//        TaxStatus taxStatus = this.taxStatus(
//                po.getOrderId(), po.getAmount(), po.getFreeAmount(), po.getAmount(), po.getInvoicedAmount());
//        log.info("充值记录开票状态: {}", taxStatus);
//        vo.setTaxNo(po.getTaxNo())
//                .setTaxStatus(taxStatus)
//                .setTaxType(po.getTaxType());
//        if (TaxStatus.NO != taxStatus) {
//            log.info("资金块已开票: orderId={}", po.getOrderId());
//            vo.setTaxNo(po.getTaxNo())
//                    .setTaxStatus(TaxStatus.YES)
//                    .setTaxType(po.getTaxType());
//        } else {
//            vo.setTaxType(TaxType.UNKNOWN)
//                    .setTaxStatus(TaxStatus.NO);
//        }

        // 已结算订单
        log.info("order no={}, status={}", k, orderStatus);
        if (OrderStatus.ORDER_STATUS_RECEIVE_MONEY == orderStatus) {
            // 消耗实际成本
            BigDecimal deltaCost = v.stream()
                .filter(recLog -> recLog.getType() == PointOpType.DECREASE.code())
                .map(PointRecLogDto::getDeltaCost)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 消耗实际成本
            BigDecimal delta = v.stream()
                .filter(recLog -> recLog.getType() == PointOpType.DECREASE.code())
                .map(PointRecLogDto::getDelta)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            log.info("delta={}, deltaCost={}", delta, deltaCost);

            vo.setAmount(deltaCost);
            vo.setFreeAmount(delta.subtract(deltaCost));
        } else {
            // 是否有解冻
            boolean hasUNFREEZE = v.stream()
                .anyMatch(recLog -> recLog.getType() == PointOpType.UNFREEZE.code());

            // 是否有减少
            boolean hasDECREASE = v.stream()
                .anyMatch(recLog -> recLog.getType() == PointOpType.UNFREEZE.code());

            // 只有解冻情况
            log.info("hasUNFREEZE={}, hasDECREASE={}", hasUNFREEZE, hasDECREASE);
            if (hasUNFREEZE && !hasDECREASE) {
                vo.setAmount(BigDecimal.ZERO);
                vo.setFreeAmount(BigDecimal.ZERO);
            } else {
                // 消耗实际成本
                BigDecimal deltaCost = v.stream()
                    .filter(recLog -> recLog.getType() == PointOpType.FREEZE.code())
                    .map(PointRecLogDto::getDeltaCost)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                // 变动量
                BigDecimal delta = v.stream()
                    .filter(recLog -> recLog.getType() == PointOpType.FREEZE.code())
                    .map(PointRecLogDto::getDelta)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                log.info("delta={}, deltaCost={}", delta, deltaCost);

                vo.setAmount(deltaCost);
                vo.setFreeAmount(delta.subtract(deltaCost));
            }
        }

        List<ChargerOrder> targetList = orderList.stream()
            .filter(order -> order.getOrderNo().equals(k))
            .collect(Collectors.toList());
        if (targetList.isEmpty()) {
            log.warn("错误订单号: orderNo={}", k);
        } else {
            ChargerOrder chargerOrder = targetList.get(0);
            vo.setCreateTime(chargerOrder.getCreateTime())
                .setOrderStatus(chargerOrder.getStatus())
                .setPayAccountId(chargerOrder.getPayAccountId())
                .setPayAccountName(chargerOrder.getPayAccountName())
                .setSiteName(chargerOrder.getStationName())
                .setTotalAmount(chargerOrder.getOrderPrice())
                .setOrderFreeAmount(chargerOrder.getFreeGoldAmount())
                .setServicePrice(chargerOrder.getServicePrice())
                .setElecPrice(chargerOrder.getElecPrice())
                .setInvoicedAmount(chargerOrder.getInvoicedAmount())
                .setInvoiceAmount(chargerOrder.getInvoiceAmount());

            // 充电订单的开票状态
            vo.setTaxStatus(this.taxStatus(null,
                chargerOrder.getPrincipalAmount(), chargerOrder.getFreeGoldAmount(),
                chargerOrder.getInvoiceAmount(), chargerOrder.getInvoicedAmount()));

            // 充电订单开票
            Long invoicedId = chargerOrder.getInvoicedId();
            if (null != invoicedId) {
                List<TaxInfo> targetInfoList = taxInfoList.stream()
                    .filter(info -> info.getId().equals(invoicedId))
                    .collect(Collectors.toList());
                if (!targetInfoList.isEmpty()) {
                    log.info("充电订单开票: orderNo={}, invoicedId={}", chargerOrder.getOrderNo(),
                        invoicedId);
                    vo.setTaxNo(targetInfoList.get(0).getTaxNo())
                        .setTaxStatus(TaxStatus.YES)
                        .setTaxType(targetInfoList.get(0).getTaxType());
//                            .setInvoicedStatus(targetInfoList.get(0).getStatus().name());
                }
            }
        }

        return vo;
    }

    /**
     * 计算开票状态
     *
     * @param actual         实际金额
     * @param free           赠送金额
     * @param invoiceAmount  可开票金额
     * @param invoicedAmount 已开票金额
     * @return 开票状态
     */
    private TaxStatus taxStatus(String orderId, @NotNull BigDecimal actual, BigDecimal free,
        @NotNull BigDecimal invoiceAmount, @NotNull BigDecimal invoicedAmount) {

        // 充值记录附加判断
        if (StringUtils.isNotBlank(orderId)) {
            List<PayBillPo> poList = payBillRwDs.getPayBillOfRefBillNo(orderId);
            // 不可开票金额和未开票金额调整
            BigDecimal outFlowAmount = poList.stream()
                .map(PayBillPo::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            if (DecimalUtils.eq(outFlowAmount, actual)) {
                return TaxStatus.CANT;
            }

            if (DecimalUtils.gtZero(outFlowAmount)) {
                actual = actual.subtract(outFlowAmount);
                invoiceAmount = invoiceAmount.subtract(outFlowAmount);
            }
        }

        // actual = 0, free > 0
        // actual > 0, free = 0
        // actual > 0, free > 0

        if (DecimalUtils.gtZero(actual)) {
            if (DecimalUtils.isZero(invoicedAmount)) {
                return TaxStatus.NO;
            }

            if (DecimalUtils.eq(actual, invoicedAmount)) {
                return TaxStatus.YES;
            }

            // invoicedAmount < actual
            return TaxStatus.PART;
        }

        // actual = 0, free > 0
        return TaxStatus.CANT;
    }

    private List<PayBillAccountDetailVo> pointLogMap2Vo(List<PointLog> logList) {
        return logList.stream().map(this::pointLogMap2Vo).collect(Collectors.toList());
    }

    private PayBillAccountDetailVo pointLogMap2Vo(PointLog pLog) {
        PayBillAccountDetailVo vo = new PayBillAccountDetailVo();
        vo.setAvailable(pLog.getAvailable())
            .setFrozen(pLog.getFrozen())
            .setCost(pLog.getCost().subtract(pLog.getFrozenCost()))
            .setFrozenCost(pLog.getFrozenCost());
        log.info("vo={}", vo);
        return vo;
    }

    public PayBillVo payBillView(String orderId) {
        log.info("查看充值记录信息: orderId={}", orderId);
        if (StringUtils.isBlank(orderId)) {
            throw new DcArgumentException("请提供充值订单号");
        }

        PayBillPo po = payBillRwDs.findByOrderId(orderId, false);
        if (null == po) {
            throw new DcArgumentException("查无改充值记录信息");
        }

        // 支付失败的充值记录不允许查看
        if (po.getStatus() == PayBillStatus.PAY_FAIL.getCode()) {
            throw new DcArgumentException("支付失败的充值，不允许查看");
        }

        // 对象重组
        PayBillVo vo = this.poMap2Vo(po);

        // init ref data
        vo.setRefBillAmount(BigDecimal.ZERO)
            .setRefBillFreeAmount(BigDecimal.ZERO)
            .setRefBillRemainAmount(BigDecimal.ZERO)
            .setRefBillRemainFreeAmount(BigDecimal.ZERO);

        // 减少操作, 获取引用的资金块信息
        log.info("flow type={}", po.getFlowType());
        if (po.getFlowType() == DepositFlowType.OUT_FLOW) {
            String refNo = po.getRefBillNo();
            if (StringUtils.isNotBlank(refNo)) {
                PayBillPo refPo = payBillRwDs.findByOrderId(refNo, false);
                if (null != refPo) {
                    log.info("获取资金快信息: refNo={}, flowSeqNo={}", refNo, po.getFlowSeqNo());
                    vo.setRefBillAmount(refPo.getAmount());
                    vo.setRefBillFreeAmount(refPo.getFreeAmount());
                    vo.setRefBillCreateTime(refPo.getCreateTime());
                    vo.setRefBillTaxType(refPo.getTaxType());
                    vo.setRefBillTaxStatus(refPo.getTaxStatus());

                    // 获取资金块剩余金额信息
                    String flowSeqNo = refPo.getFlowSeqNo();
                    log.debug("获取资金块剩余金额信息: flowSeqNo={}", flowSeqNo);
                    if (null != flowSeqNo) {
                        // 充值资金块剩余信息
                        Map<String, BigDecimal> billRemain = this.getBillRemain(
                            String.valueOf(vo.getUserId()), po.getTopCommId(), po.getAccountType(),
                            po.getAccountCode(), flowSeqNo);

                        vo.setRefBillRemainAmount(billRemain.get("remainCost"))
                            .setRefBillRemainFreeAmount(billRemain.get("remainFree"));
                    }
                }
            }
        } else if (po.getFlowType() == DepositFlowType.IN_FLOW) {
            // 获取资金块剩余金额信息
            String flowSeqNo = po.getFlowSeqNo();
            log.debug("获取资金块剩余金额信息: flowSeqNo={}", flowSeqNo);
            if (null != flowSeqNo) {
                // 充值资金块剩余信息
                Map<String, BigDecimal> billRemain = this.getBillRemain(
                    String.valueOf(vo.getUserId()), po.getTopCommId(),
                    po.getAccountType(), po.getAccountCode(), flowSeqNo);

                vo.setRemainAmount(billRemain.get("remainCost"))
                    .setRemainFreeAmount(billRemain.get("remainFree"));
            }
        }

        vo.setTotalAmount(vo.getAmount().add(vo.getFreeAmount()));

        log.info("ref bill: amount={}, freeAmount={} remainCost={}, remainFree={}",
            vo.getRefBillAmount(), vo.getRefBillFreeAmount(),
            vo.getRefBillRemainAmount(), vo.getRefBillRemainFreeAmount());

        // 充值账户信息
        vo.setRefBillAccountInfo(this.accountDetail(orderId));
        log.info("account info={}", vo.getRefBillAccountInfo());

        // 账户名称
        if (PayAccountType.PERSONAL == po.getAccountType()) {
            vo.setPayAccountName("个人账户");
        } else if (PayAccountType.COMMERCIAL == po.getAccountType()) {
            CommPo commPo = trCommercialService.getCommercialById(po.getAccountCode());
            vo.setPayAccountName("商户会员-" + commPo.getName());
        } else if (PayAccountType.CORP == po.getAccountType()) {
            ListCorpParam corpParam = new ListCorpParam();
            corpParam.setUidList(List.of(po.getUserId()));
            ListResponse<CorpVo> corpList = userFeignClient.getCorpList(corpParam);
            vo.setPayAccountName("企业账户-" + corpList.getData().get(0).getCorpName());
        } // end if

        // 申请单号
        ObjectResponse<BalanceApplicationPo> response = userFeignClient.getByOrderId(
            po.getOrderId());
        if (response != null && response.getData() != null) {
            vo.setApplyId(response.getData().getId());
            vo.setAttachment(response.getData().getAttachment());
        }
        return vo;
    }

    public Mono<ObjectResponse<PayBillVo>> payBillTkView(String outRefundNo) {
        log.info("查看退款记录信息: orderId={}", outRefundNo);
        if (StringUtils.isBlank(outRefundNo)) {
            throw new DcArgumentException("请提供退款单号");
        }

        PayBillPo po = payBillRoDs.findByOutRefundNo(outRefundNo);
        if (null == po) {
            return Mono.just(new PayBillVo()).map(RestUtils::buildObjectResponse);
        }

        // 对象重组
        PayBillVo vo = this.poMap2Vo(po);

        // init ref data
        vo.setRefBillAmount(BigDecimal.ZERO)
            .setRefBillFreeAmount(BigDecimal.ZERO)
            .setRefBillRemainAmount(BigDecimal.ZERO)
            .setRefBillRemainFreeAmount(BigDecimal.ZERO);

        // 减少操作, 获取引用的资金块信息
        log.info("flow type={}", po.getFlowType());
        if (po.getFlowType() == DepositFlowType.OUT_FLOW) {
            String refNo = po.getRefBillNo();
            if (StringUtils.isNotBlank(refNo)) {
                PayBillPo refPo = payBillRwDs.findByOrderId(refNo, false);
                if (null != refPo) {
                    log.info("获取资金快信息: refNo={}, flowSeqNo={}", refNo, po.getFlowSeqNo());
                    vo.setRefBillAmount(refPo.getAmount());
                    vo.setRefBillFreeAmount(refPo.getFreeAmount());
                    vo.setRefBillCreateTime(refPo.getCreateTime());
                    vo.setRefBillTaxType(refPo.getTaxType());
                    vo.setRefBillTaxStatus(refPo.getTaxStatus());

                    // 获取资金块剩余金额信息
                    String flowSeqNo = refPo.getFlowSeqNo();
                    log.debug("获取资金块剩余金额信息: flowSeqNo={}", flowSeqNo);
                    if (null != flowSeqNo) {
                        // 充值资金块剩余信息
                        Map<String, BigDecimal> billRemain = this.getBillRemain(
                            String.valueOf(vo.getUserId()), po.getTopCommId(), po.getAccountType(),
                            po.getAccountCode(), flowSeqNo);

                        vo.setRefBillRemainAmount(billRemain.get("remainCost"))
                            .setRefBillRemainFreeAmount(billRemain.get("remainFree"));
                    }
                }
            }
        } else if (po.getFlowType() == DepositFlowType.IN_FLOW) {
            // 获取资金块剩余金额信息
            String flowSeqNo = po.getFlowSeqNo();
            log.debug("获取资金块剩余金额信息: flowSeqNo={}", flowSeqNo);
            if (null != flowSeqNo) {
                // 充值资金块剩余信息
                Map<String, BigDecimal> billRemain = this.getBillRemain(
                    String.valueOf(vo.getUserId()), po.getTopCommId(),
                    po.getAccountType(), po.getAccountCode(), flowSeqNo);

                vo.setRemainAmount(billRemain.get("remainCost"))
                    .setRemainFreeAmount(billRemain.get("remainFree"));
            }
        }

        vo.setTotalAmount(vo.getAmount().add(vo.getFreeAmount()));

        log.info("ref bill: amount={}, freeAmount={} remainCost={}, remainFree={}",
            vo.getRefBillAmount(), vo.getRefBillFreeAmount(),
            vo.getRefBillRemainAmount(), vo.getRefBillRemainFreeAmount());

        // 充值账户信息
        vo.setRefBillAccountInfo(this.accountDetail(po.getOrderId()));
        log.info("account info={}", vo.getRefBillAccountInfo());

        // 账户名称
        if (PayAccountType.PERSONAL == po.getAccountType()) {
            vo.setPayAccountName("个人账户");
        } else if (PayAccountType.COMMERCIAL == po.getAccountType()) {
            CommPo commPo = trCommercialService.getCommercialById(po.getAccountCode());
            vo.setPayAccountName("商户会员-" + commPo.getName());
        } else if (PayAccountType.CORP == po.getAccountType()) {
            ListCorpParam corpParam = new ListCorpParam();
            corpParam.setUidList(List.of(po.getUserId()));
            ListResponse<CorpVo> corpList = userFeignClient.getCorpList(corpParam);
            vo.setPayAccountName("企业账户-" + corpList.getData().get(0).getCorpName());
        } // end if

        // 申请单号
        ObjectResponse<BalanceApplicationPo> response = userFeignClient.getByOrderId(
            po.getOrderId());
        if (response != null && response.getData() != null) {
            vo.setApplyId(response.getData().getId());
            vo.setAttachment(response.getData().getAttachment());
        }
        return Mono.just(vo).map(RestUtils::buildObjectResponse);
    }

    private Map<String, BigDecimal> getBillRemain(String uid, Long topCommId,
        PayAccountType accountType, long commId, String flowSeqNo) {
        Map<String, BigDecimal> result = new HashMap<>();

        // 充值资金块剩余信息
        ListPointRecParam param = new ListPointRecParam();
        param.setUid(uid)
//                .setSubUid(String.valueOf(commId))
            .setSeqNumList(Arrays.stream(flowSeqNo.split(","))
                .map(Long::valueOf).collect(Collectors.toList()))
            .setType(ResponseConvertUtils.formatPointType(accountType, topCommId));

        // @Nathan 商户会员的充值需要填入商户Id
        if (PayAccountType.COMMERCIAL == accountType) {
            param.setSubUid(String.valueOf(commId));
        } else {
            param.setSubUid(topCommId.toString());
        }

        List<PointRecDto> recs = dcCusBalanceService.listPointRecord(param);
        if (!recs.isEmpty()) {
            // 实际成本剩余: totalCost != 0 为实际
            // frozenCost + usedCost
            BigDecimal remainCost = recs.stream()
                .filter(rec -> !DecimalUtils.isZero(rec.getTotalCost()))
                .map(PointRecPo::getAvailable)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
//                                    .mapToLong(rec -> rec.getFrozenCost() + rec.getUsedCost()).sum();

            // 赠送成本剩余: totalCost == 0 为赠送
            BigDecimal remainFree = recs.stream()
                .filter(rec -> DecimalUtils.isZero(rec.getTotalCost()))
                .map(PointRecPo::getAvailable)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

            result.put("remainCost", remainCost);
            result.put("remainFree", remainFree);
        } else {
            result.put("remainCost", DecimalUtils.ZERO);
            result.put("remainFree", DecimalUtils.ZERO);
        }
        log.info("remainCost={}, remainFree={}", result.get("remainCost"),
            result.get("remainFree"));

        return result;
    }

    //    判断逻辑：
//    当子页面 2 中点击确认后，系统判断该笔充值是否存在资金块在发票中心已经完成（已开
//    具）或审核中开票订单请求
//        1.如果无
//    则直接进子页面 4 完成数据修改，后续产生新订单开票相关信息继承录入的信息。
//        2.如果存在待审核的开票订单
//    则显示 3.1，提示语：“平台存在待审核的开票请求包含该笔充值的金额，请完成开票流程后，
//    再进行本次开票状态修改。 ”
//        3.如果不存在待审核开票订单，存在已开具的开票订单
//    则进子页面 3.2 进行确认，提示语：
//        ************************************************************************************************
//    该笔充值已存在部分金额完成开票，本次修改只更改未开票部分金额的开票信息，请确认是
//        否继续
//    备注：
//    可通过后台将已开票的部分进行红冲后，再次尝试修改此订单的开票状态。
    public ObjectResponse<CheckTaxStatus> checkTaxStatus(String orderId) {
        log.info("充值记录是否关联开票充电订单: orderId={}", orderId);
        if (StringUtils.isBlank(orderId)) {
            throw new DcArgumentException("请提供充值订单号");
        }

        PayBillPo po = payBillRwDs.findByOrderId(orderId, false);
        if (null == po) {
            log.info("查无该充值记录: orderId={}", orderId);
            throw new DcArgumentException("充值订单号无效");
        }

        if (StringUtils.isBlank(po.getFlowSeqNo())) {
            log.info("该充值记录没有关联的资金块信息: orderId={}", orderId);
            return new ObjectResponse<>(CheckTaxStatus.NONE);
        }

        if (PayAccountType.CORP.equals(po.getAccountType())) {
            // 获取是否关联企业开票记录
            List<TaxOrderDto> list = payBillRoDs.findInvoicePayBill(List.of(orderId));
            if (CollectionUtils.isNotEmpty(list)) {
                CheckTaxStatus res = this.invoiceStatus(list);
                if (!CheckTaxStatus.NONE.equals(res)) {
                    return RestUtils.buildObjectResponse(res);
                }

                // 企业客户开票占用
                return RestUtils.buildObjectResponse(CheckTaxStatus.HAS_WAITING);
            }
        }

        List<PointRecDto> recDtoList = this.getPayBillRecLog(po);

        // 冻结，减少，解冻
        List<Integer> logTypes = List.of(PointOpType.DECREASE.code());
        if (CollectionUtils.isNotEmpty(recDtoList)) {
            List<PointRecLogDto> recLogDtoList = recDtoList.stream()
                .filter(i -> i.getRecLogs() != null)
                .flatMap(i -> i.getRecLogs().stream())
                .filter(lg -> logTypes.contains(lg.getType()))
                .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(recLogDtoList)) {
                // 从明细日志获取订单号列表(可能存在退款充值订单号)
                List<String> orderNoList = recLogDtoList.stream()
                    .map(PointRecLogDto::getOrderNo)
                    .distinct()
                    .collect(Collectors.toList());

                CheckTaxStatus res = this.invoiceStatus(
                    chargerOrderDs.findInvoicedOrder(false, orderNoList));
                if (!CheckTaxStatus.NONE.equals(res)) {
                    return RestUtils.buildObjectResponse(res);
                }

                if (PayAccountType.CORP.equals(po.getAccountType())) {
                    // 充电订单
                    List<TaxOrderDto> orderList = chargerOrderDs.findInvoicedOrder(true,
                        new ArrayList<>(orderNoList));
                    res = this.invoiceStatus(orderList);
                    if (!CheckTaxStatus.NONE.equals(res)) {
                        return RestUtils.buildObjectResponse(res);
                    }
                }
            }
        }

        return new ObjectResponse<>(CheckTaxStatus.NONE);
    }

    private CheckTaxStatus invoiceStatus(List<TaxOrderDto> taxOrderList) {
        if (CollectionUtils.isNotEmpty(taxOrderList)) {
            // 待审核的开票订单
            List<InvoicedStatus> submitted = List.of(
                InvoicedStatus.SUBMITTED, InvoicedStatus.REVIEWED, InvoicedStatus.NOT_SUBMITTED);
            Optional<TaxOrderDto> first = taxOrderList.stream()
                .filter(dto -> dto.getInvoicedStatus() != null)
                .filter(dto -> submitted.contains(dto.getInvoicedStatus())).findFirst();
            if (first.isPresent()) {
//                    "请完成开票流程后，再进行本次开票状态修改");
                return CheckTaxStatus.HAS_WAITING;
            }

            first = taxOrderList.stream()
                .filter(dto -> dto.getInvoicedStatus() != null)
                .filter(vo -> InvoicedStatus.COMPLETED.equals(vo.getInvoicedStatus())).findFirst();
            if (first.isPresent()) {
//                    "备注：可通过后台将已开票的部分进行红冲后，再次尝试修改此订单的开票状态。");
                return CheckTaxStatus.NONE_WAITING_HAS_COMPLETED;
            }
        }

        return CheckTaxStatus.NONE;
    }

    /**
     * 更新充值记录的状态
     *
     * @param orderId
     * @param status
     * @return
     */
    public Integer updateStatus(String orderId, PayBillStatus status) {
        log.info("更新充值记录的状态: orderIs={}, status={}", orderId, status);
        if (StringUtils.isBlank(orderId)) {
            throw new DcArgumentException("请提供充值订单号");
        }

        if (null == status) {
            throw new DcArgumentException("请提供目标状态值");
        }

        PayBillPo po = payBillRwDs.findByOrderId(orderId, false);
        if (null == po) {
            throw new DcArgumentException("查无改充值记录信息");
        }

        return payBillRwDs.updateStatus(orderId, po.getStatus(), status.getCode());
    }

    /**
     * 充电订单涉及充值记录详细信息
     *
     * @param orderNo   充电订单号
     * @param calculate 充值记录是否是计算状态
     * @return
     */
    public List<PayBillLinkChargeOrderVo> orderPointRecLog(String orderNo, boolean calculate) {
        log.info("获取充电订单的资金块详情: orderNo={}", orderNo);
        if (StringUtils.isBlank(orderNo)) {
            throw new DcArgumentException("请提供充电订单号");
        }

        final List<PayBillLinkChargeOrderVo> voList = new ArrayList<>();

        // 充电订单
        ChargerOrder order = chargerOrderService.findByOrderNo(orderNo);
        if (null == order) {
            log.info("查无改充电订单信息: orderNo={}", orderNo);
            return voList;
        }
        ChargerOrderPayPo payPo = chargerOrderPayRwDs.getByOrderNo(orderNo, false);
        if (SettlementType.POSTPAID.equals(payPo.getSettlementType())) {
            log.info("后付费订单无需查询资金块");
            return List.of();
        }

        // 订单启动方式决定是否存在资金块信息
        // 无需查询的启动方式
        List<OrderStartType> ignoreType = Arrays.asList(
            OrderStartType.UNKNOWN,
            //OrderStartType.EVSE_OFFLINE_CARD,
            OrderStartType.EVSE_AUTO,
//                OrderStartType.MGM_WEB_MANUAL,   // 0x21
//                OrderStartType.MGM_WEB_BATCH, // 0x22
            OrderStartType.HLHT   // 0x23
//            OrderStartType.SCHEDULE_JOB
        );
        if (ignoreType.contains(OrderStartType.valueOf(order.getOrderType()))) {
            log.info("忽略: type={}", OrderStartType.valueOf(order.getOrderType()));
            return voList;
        }

        if (order.getOrderType() == OrderStartType.EVSE_OFFLINE_CARD.getCode() &&
            order.getStatus() != OrderStatus.ORDER_STATUS_RECEIVE_MONEY) {
            log.info("紧急充电卡在结束时才有默认扣款账户信息： orderNo = {}", order.getOrderNo());
            return voList;
        }

        if (null == order.getDefaultPayType()) {
            log.warn("充电订单的支付账号类型为空，不支持: orderNo = {}", orderNo);
            return voList;
        }

        this.payBillRec(voList, order, calculate);
//        final List<String> orderTemplate = recLogDtoList
//                .stream()
//                .map(PointRecLogDto::getRecOrigOrderNo)
//                .sorted(Collections.reverseOrder())
//                .collect(Collectors.toList());

        return voList.stream()
            .sorted(Comparator.comparing(PayBillLinkChargeOrderVo::getOrderId))
            .collect(Collectors.toList());
    }

    /**
     * 充值单号获取用户对应的积分块信息
     * @param param
     * @return
     */
    public List<PointRecDto> getCzOrderPointsInfo(CzOrderPointParam param) {

        // TODO 充值单号获取用户对应的积分块信息 等待老蒋接口修改
        log.info("getCzOrderPointsInfo: {}", param);

        IotAssert.isNotNull(param, "请传入参");

        PayAccountType accountType = param.getAccountType();
        List<String> orderIdList = param.getOrderIdList();
        Long userId = param.getUserId();
        Long corpId = param.getCorpId();
        Long commId = param.getCommId();

        if(CollectionUtils.isEmpty(orderIdList)) {
            return List.of();
        }

        final List<PayBillPo> payBillList = payBillRoDs.getPayBillList(orderIdList);
        if(CollectionUtils.isEmpty(payBillList)) {
            return List.of();
        }

        final List<Long> collect = payBillList.stream()
            .map(PayBillPo::getFlowSeqNo)
            .map(e -> List.of(e.split(",")))
            .flatMap(List::stream)
            .map(Long::valueOf)
            .collect(Collectors.toList());

        ListPointRecParam recParam = new ListPointRecParam();
//        recLogParam.setSeqNumList(collect);
        recParam.setOrderNos(param.getOrderIdList());

        if (PayAccountType.CREDIT.equals(accountType)) {

            IotAssert.isNotNull(corpId, "缺少必要参数。");
            com.cdz360.biz.model.trading.corp.po.CorpPo corp = corpRoDs.getCorpById(corpId);
            recParam.setUid(corp.getUid().toString())
                .setSubUid(corp.getTopCommId().toString())
                .setType(ResponseConvertUtils.formatPointType(
                    PayAccountType.PERSONAL, corp.getTopCommId()));

        } else {
            IotAssert.isNotNull(userId, "缺少必要参数。");
            CusRepVo user = userSyncRoDs.getUserByUid(userId);
            recParam.setUid(userId.toString())
                .setSubUid(user.getTopCommId().toString())
                .setType(ResponseConvertUtils.formatPointType(
                    accountType, user.getTopCommId()));
            if (PayAccountType.COMMERCIAL.equals(accountType)) {
                IotAssert.isNotNull(commId, "缺少必要参数。");
                recParam.setSubUid(String.valueOf(commId));
            }
        }

//        recLogParam.setSeqNumList(List.of(999L));
//        recLogParam.setSorts(List.of(SortParam.as("log.seqNum", OrderType.desc)));

        List<PointRecDto> recLogDtoList = dcCusBalanceService.listPointRecord(recParam);

        if(!param.isContainFree()) {
            // 不包含赠送金额
            return recLogDtoList.stream()
                .filter(e -> DecimalUtils.gt(e.getTotalCost(), BigDecimal.ZERO))
                .collect(Collectors.toList());
        }

        return recLogDtoList;
    }

    public List<String> getCzOrderIdListByOrderNoList(@NonNull PayAccountType accountType,
        @NonNull List<String> orderNoList,
        @Nullable Long userId,
        @Nullable Long corpId,
        @Nullable Long commId
    ) {
        IotAssert.isTrue(CollectionUtils.isNotEmpty(orderNoList), "入参缺失");
        // 适配逻辑
        final List<PayAccountType> credit = List.of(PayAccountType.WX_CREDIT,
            PayAccountType.ALIPAY_CREDIT,
            PayAccountType.E_CNY);
        if (credit.contains(accountType)) {
            return payBillRoDs.selectByChargeOrderNoList(orderNoList,
                DepositFlowType.IN_FLOW);
        }

        log.debug("通过订单查找资金块信息");
        ListPointRecLogParam param = new ListPointRecLogParam();

        if (PayAccountType.CREDIT.equals(accountType)) {

            IotAssert.isNotNull(corpId, "缺少必要参数");
            com.cdz360.biz.model.trading.corp.po.CorpPo corp = corpRoDs.getCorpById(corpId);
            param.setUid(corp.getUid().toString())
                .setSubUid(corp.getTopCommId().toString())
                .setType(ResponseConvertUtils.formatPointType(
                    PayAccountType.PERSONAL, corp.getTopCommId()));

        } else {
            IotAssert.isNotNull(userId, "缺少必要参数");
            CusRepVo user = userSyncRoDs.getUserByUid(userId);
            param.setUid(userId.toString())
                .setSubUid(user.getTopCommId().toString())
                .setType(ResponseConvertUtils.formatPointType(
                    accountType, user.getTopCommId()));
            if (PayAccountType.COMMERCIAL.equals(accountType)) {
                IotAssert.isNotNull(commId, "缺少必要参数");
                param.setSubUid(String.valueOf(commId));
            }
        }

        // 充电订单号
        param.setOrderNoList(orderNoList);
        param.setSorts(List.of(SortParam.as("log.seqNum", OrderType.desc)));

        List<PointRecLogDto> recLogDtoList = dcCusBalanceService.listPointRecordLog(param);//

        return recLogDtoList.stream().map(PointRecLogDto::getRecOrigOrderNo)
            .filter(Objects::nonNull).distinct().collect(Collectors.toList());
    }

    private void payBillRec(final List<PayBillLinkChargeOrderVo> voList, final ChargerOrder order,
        boolean calculate) {
        // 适配逻辑
        final List<Integer> credit = List.of(PayAccountType.WX_CREDIT.getCode(),
            PayAccountType.ALIPAY_CREDIT.getCode(),
            PayAccountType.E_CNY.getCode());
        if (null == order.getDefaultPayType() ||
            credit.contains(order.getDefaultPayType())) {
            // 充值记录列表
            PayBillPo po = payBillRoDs.selectByChargeOrderNo(order.getOrderNo(), null);
            if (null == po) {
                return;
            }

            PayBillLinkChargeOrderVo vo = new PayBillLinkChargeOrderVo();
            vo.setOrderId(po.getOrderId())
                .setOrderNo(order.getOrderNo())
                .setOrderStatus(order.getStatus())
                .setAmount(po.getAmount())
                .setFreeAmount(BigDecimal.ZERO)
                .setTaxType(po.getTaxType())
                .setTaxNo(po.getTaxNo());

            this.setPayBillOrderData(vo, po, po.getAmount());

            if (calculate) {
                vo.setTaxStatus(
                    this.taxStatus(
                        po.getOrderId(),
                        po.getAmount(), po.getFreeAmount(),
                        po.getAmount(), po.getInvoicedAmount()));
            } else {
                vo.setTaxStatus(po.getTaxStatus()); // 保留记录上的开票状态
            }

            voList.add(vo);
            return;
        }

        log.debug("通过订单查找资金块信息");
        ListPointRecLogParam param = new ListPointRecLogParam();
        param.setUid(order.getCustomerId().toString());
        param.setSubUid(order.getTopCommId().toString());
//        param.setSeqNumList(seqNoList.stream().map(Long::valueOf).collect(Collectors.toList()));
        param.setType(ResponseConvertUtils.formatPointType(
            PayAccountType.valueOf(order.getDefaultPayType()),
            order.getTopCommId()));

        // 授信账户需要调整参数
        if (order.getDefaultPayType() == PayAccountType.CREDIT.getCode()) {
            ObjectResponse<RBlocUser> rBlocUser = userFeignClient.findRBlocUserById(
                order.getPayAccountId());
            if (null == rBlocUser || rBlocUser.getData() == null) {
                log.warn("查无该集团客户: payAccountId = {}", order.getDefaultPayType());
                return;
            }

            Long blocUserId = rBlocUser.getData().getBlocUserId();
            ObjectResponse<CorpPo> corp = userFeignClient.getCorp(blocUserId);
            if (corp == null || null == corp.getData()) {
                log.warn("查无该集团客户: blocUserId = {}", blocUserId);
                return;
            }

            param.setUid(corp.getData().getUid().toString())
                .setSubUid(corp.getData().getTopCommId().toString())
                .setType(ResponseConvertUtils.formatPointType(
                    PayAccountType.PERSONAL, corp.getData().getTopCommId()));
        } else if (order.getDefaultPayType() == PayAccountType.COMMERCIAL.getCode()) {
            param.setSubUid(String.valueOf(order.getPayAccountId()));
        }

        // 充电订单号
        param.setOrderNoList(Collections.singletonList(order.getOrderNo()));
        param.setSorts(List.of(SortParam.as("log.seqNum", OrderType.desc)));

        // type: 1: increase; 2: decrease; 3: freeze; 4: unfreeze; 5: expire; 6: cancel
        List<PointRecLogDto> recLogDtoList = dcCusBalanceService.listPointRecordLog(param);
        // 充电订单消费资金块信息
        if (order.getStatus() == OrderStatus.ORDER_STATUS_RECEIVE_MONEY) { // 已计算订单
            // 获取减少的记录
            recLogDtoList = recLogDtoList.stream()
                .filter(lg -> PointOpType.DECREASE.code() == lg.getType())
                .collect(Collectors.toList());
        } else {
            // 获取冻结的记录
            recLogDtoList = recLogDtoList.stream()
                .filter(lg -> PointOpType.FREEZE.code() == lg.getType())
                .collect(Collectors.toList());
        }
        log.info("资金块详情: size={}", recLogDtoList.size());

        // 充值订单号列表
        List<String> billOrderIdList = recLogDtoList.stream()
            .filter(rl -> rl.getRecOrigOrderNo() != null)
            .map(PointRecLogDto::getRecOrigOrderNo)
            .distinct()
            .collect(Collectors.toList());
//                .collect(Collectors.toSet());
        log.info("充值记录订单号: size={}", billOrderIdList.size());

        if (billOrderIdList.isEmpty()) {
            log.info("没有找到对应的充值记录");
            return;
        }

        // 充值记录列表
        List<PayBillPo> poList = payBillRwDs.getPayBillList(billOrderIdList);
        log.info("相关充值记录: size={}", poList.size());

        // 统计实际消耗和赠送消耗
        recLogDtoList.stream()
            .filter(rl -> null != rl.getRecOrigOrderNo())
            .collect(Collectors.groupingBy(PointRecLogDto::getRecOrigOrderNo))
            .forEach((k, v) -> {
                Optional<PayBillPo> first = poList.stream().filter(i -> i.getOrderId().equals(k))
                    .findFirst();
                if (first.isPresent()) {
                    // 实际成本
                    BigDecimal cost = v.stream().map(PointRecLogDto::getDeltaCost)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                    // 赠送成本
                    BigDecimal free = v.stream()
                        .map(lg -> lg.getDelta().subtract(lg.getDeltaCost()))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                    PayBillLinkChargeOrderVo vo = new PayBillLinkChargeOrderVo();
                    vo.setOrderId(k)
                        .setOrderNo(order.getOrderNo())
                        .setOrderStatus(order.getStatus())
                        .setAmount(cost)
                        .setFreeAmount(free)
                        .setTaxType(first.get().getTaxType())
                        .setTaxNo(first.get().getTaxNo());

                    this.setPayBillOrderData(vo, first.get(), cost);

                    if (calculate) {
                        vo.setTaxStatus(
                            this.taxStatus(
                                first.get().getOrderId(),
                                first.get().getAmount(), first.get().getFreeAmount(),
                                first.get().getAmount(), first.get().getInvoicedAmount()));
                    } else {
                        vo.setTaxStatus(first.get().getTaxStatus()); // 保留记录上的开票状态
                    }

                    voList.add(vo);
                    log.info("orderId={}, cost={}, free={}", k, cost, free);
                } else {
                    log.warn("充值记录不存在 或 已全部开票. orderId: {}", k);
                }
            });
    }

    private void setPayBillOrderData(PayBillLinkChargeOrderVo vo, PayBillPo po, BigDecimal cost) {
        BigDecimal amount = Optional.ofNullable(po.getAmount())
            .orElse(BigDecimal.ZERO);
        BigDecimal outFlowAmount = Optional.ofNullable(po.getOutFlowAmount())
            .orElse(BigDecimal.ZERO);
        // 判断充值记录是否还有未开票金额
        if (DecimalUtils.gt(amount.subtract(outFlowAmount), po.getInvoicedAmount())) {
            vo.setPayBillOrderInvoiceAmount(cost); // 则认为cost就是可开票金额
        }
        vo.setPayBillInvoicedAmount(po.getInvoicedAmount());
    }

    public Integer updateStatusByOrderId(String orderId, int srcStatus, int targetStatus) {
        log.info("变更充值记录: orderId={}, srcStatus={}, targetStatus={}", orderId, srcStatus,
            targetStatus);
        return payBillRwDs.updateStatus(orderId, srcStatus, targetStatus);
    }

    @Async
    public void updatePayBillStatus(ChargerOrder order) {
//        log.info("确认是否需要更新充值记录的状态: orderNo={}", orderNo);

//        if (StringUtils.isBlank(orderNo)) {
//            throw new DcArgumentException("请提供充电订单号");
//        }

//        // 充电订单
//        ChargerOrder order = chargerOrderDs.findByOrderNo(orderNo);
//        if (null == order) {
//            log.info("查无改充电订单信息: orderNo={}", orderNo);
//            return;
//        }

        // 订单启动方式决定是否存在资金块信息
        // 无需查询的启动方式
        List<OrderStartType> ignoreType = Arrays.asList(
            OrderStartType.UNKNOWN,
//                OrderStartType.EVSE_OFFLINE_CARD,
            OrderStartType.EVSE_AUTO,
//                OrderStartType.MGM_WEB_MANUAL,   // 0x21
//                OrderStartType.MGM_WEB_BATCH, // 0x22
            OrderStartType.HLHT,   // 0x23
            OrderStartType.SCHEDULE_JOB
        );
        if (ignoreType.contains(OrderStartType.valueOf(order.getOrderType()))) {
            log.info("忽略: type={}", OrderStartType.valueOf(order.getOrderType()));
            return;
        }

        if (order.getOrderType() == OrderStartType.EVSE_OFFLINE_CARD.getCode() &&
            order.getStatus() != OrderStatus.ORDER_STATUS_RECEIVE_MONEY) {
            log.info("紧急充电卡在结束时才有默认扣款账户信息： orderNo = {}", order.getOrderNo());
            return;
        }

        log.debug("通过订单查找资金块信息");
        ListPointRecLogParam param = new ListPointRecLogParam();
        param.setUid(order.getCustomerId().toString());
        param.setSubUid(order.getTopCommId().toString());
//        param.setSeqNumList(seqNoList.stream().map(Long::valueOf).collect(Collectors.toList()));
        param.setType(ResponseConvertUtils.formatPointType(
            PayAccountType.valueOf(order.getDefaultPayType()),
            order.getTopCommId()));

        // 授信账户需要调整参数
        if (order.getDefaultPayType() == PayAccountType.CREDIT.getCode()) {
            ObjectResponse<RBlocUser> rBlocUser = userFeignClient.findRBlocUserById(
                order.getPayAccountId());
            if (null == rBlocUser || rBlocUser.getData() == null) {
                log.warn("查无该集团客户: payAccountId = {}", order.getDefaultPayType());
                return;
            }

            Long blocUserId = rBlocUser.getData().getBlocUserId();
            ObjectResponse<CorpPo> corp = userFeignClient.getCorp(blocUserId);
            if (corp == null || null == corp.getData()) {
                log.warn("查无该集团客户: blocUserId = {}", blocUserId);
                return;
            }

            param.setUid(corp.getData().getUid().toString())
                .setSubUid(corp.getData().getTopCommId().toString())
                .setType(ResponseConvertUtils.formatPointType(
                    PayAccountType.PERSONAL, corp.getData().getTopCommId()));
        } else if (order.getDefaultPayType() == PayAccountType.COMMERCIAL.getCode()) {
            param.setSubUid(String.valueOf(order.getPayAccountId()));
        }

        // 充电订单号
        param.setOrderNoList(Collections.singletonList(order.getOrderNo()));

        // type: 1: increase; 2: decrease; 3: freeze; 4: unfreeze; 5: expire; 6: cancel
        List<PointRecLogDto> recLogDtoList = dcCusBalanceService.listPointRecordLog(param).stream()
            .filter(lg -> PointOpType.DECREASE.code() == lg.getType() ||
                PointOpType.UNFREEZE.code() == lg.getType())
            .collect(Collectors.toList());
        log.info("减少操作: size={}", recLogDtoList.size());

        // 已结算的资金块
        this.updatePayBillStatus(recLogDtoList);

//        // 是否已经全部开票
//        this.updatePayBillTax(recLogDtoList);

        // 充值记录调整开票金额
        this.payBillAppendInvoicedAmount(order.getOrderNo(), recLogDtoList.stream()
            .filter(lg -> PointOpType.DECREASE.code() == lg.getType())
            .collect(Collectors.toList()));
    }

    @Transactional(rollbackFor = DcException.class)
    public void payBillAppendInvoicedAmount(String orderNo, List<PointRecLogDto> recLogDtoList) {
        // 充值订单号列表
        List<String> billOrderIdList = recLogDtoList.stream()
            .filter(rl -> rl.getRecOrigOrderNo() != null)
            .map(PointRecLogDto::getRecOrigOrderNo)
            .distinct()
            .collect(Collectors.toList());
        log.info("充值记录订单号: list={}", billOrderIdList);
        if (CollectionUtils.isEmpty(billOrderIdList)) {
            return;
        }

        BigDecimal amount = BigDecimal.ZERO;
        for (PointRecLogDto dto : recLogDtoList) {
            String orderId = dto.getRecOrigOrderNo();
            PayBillPo po = payBillRwDs.findByOrderId(orderId, false);
            if (po.getTaxStatus() != TaxStatus.YES) { // 还未开票则需要处理
                PayBillPo newPo = new PayBillPo();
                newPo.setOrderId(orderId)
                    .setInvoicedAmount(po.getInvoicedAmount().add(dto.getDeltaCost()));

                // 充值记录状态
                newPo.setTaxStatus(this.taxStatus(
                    po.getOrderId(), po.getAmount(), po.getFreeAmount(), po.getAmount(),
                    newPo.getInvoicedAmount()));
                payBillRwDs.update(newPo);
            } else {
                amount = amount.add(dto.getDeltaCost());
            }
        }

        // 更新充电订单的已开票金额
        UpdateOrderInvoicedAmountParam amountParam = new UpdateOrderInvoicedAmountParam();
        amountParam.setOpType(UpdateOrderInvoicedAmountParam.OpType.ADD)
            .setAmount(amount)
            .setOrderNo(orderNo);
        invoiceProcess.orderInvoiceDataProcess(List.of(amountParam));
    }

//    private void updatePayBillTax(List<PointRecLogDto> recLogDtoList) {
//        // 充值订单号列表
//        List<String> billOrderIdList = recLogDtoList.stream()
//                .filter(rl -> rl.getRecOrigOrderNo() != null)
//                .map(PointRecLogDto::getRecOrigOrderNo)
//                .distinct()
//                .collect(Collectors.toList());
//        log.info("充值记录订单号: list={}", billOrderIdList);
//        if (CollectionUtils.isEmpty(billOrderIdList)) {
//            return;
//        }
//
//        // 充值记录列表
//        List<PayBillPo> payBillList = payBillRwDs.getPayBillList(billOrderIdList).stream()
//                .filter(po -> po.getTaxType() == TaxType.UNKNOWN).collect(Collectors.toList());
//        log.info("未开票充值记录: size={}", payBillList.size());
//
//        payBillList.forEach(po -> {
//            // 充值记录已经耗尽
//            if (po.getStatus() == PayBillStatus.EXHAUST.getCode()) {
//                // 查看充值记录的资金块详情
//                PayBillUsedDetail detail = this.pointRecLog(po.getOrderId(), null);
//
//                // 减少操作总金额
//                List<PayBillPo> poList = payBillRwDs.getPayBillOfRefBillNo(po.getOrderId());
//                BigDecimal reduce = poList.stream().map(PayBillPo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//
//                // 已经开票
//                if (DecimalUtils.gtZero(po.getAmount()) &&
//                        DecimalUtils.eq(po.getAmount(), detail.getUsedInfo().getTaxTotal().add(reduce))) {
//                    po.setTaxStatus(TaxStatus.YES);
//                    int update = payBillRwDs.update(po);
//                    log.info("充值记录已经变更开票状态: update = {}", update);
//                }
//            }
//        });
//    }

    private void updatePayBillStatus(List<PointRecLogDto> recLogDtoList) {
        // 已结算的资金块
        // type: 1: increase; 2: decrease; 3: freeze; 4: unfreeze; 5: expire; 6: cancel
        List<PointRecLogDto> rList = recLogDtoList.stream()
            .filter(lg -> PointOpType.DECREASE.code() == lg.getType() ||
                PointOpType.UNFREEZE.code() == lg.getType()) // 只是占用的情况也需要回滚状态
            .collect(Collectors.toList());
        log.info("减少操作: size={}", recLogDtoList.size());

        // 充值订单号列表
        List<String> billOrderIdList = recLogDtoList.stream()
            .filter(rl -> rl.getRecOrigOrderNo() != null)
            .map(PointRecLogDto::getRecOrigOrderNo)
            .distinct()
            .collect(Collectors.toList());
//                .collect(Collectors.toSet());
        log.info("充值记录订单号: list={}", billOrderIdList);
        if (CollectionUtils.isEmpty(billOrderIdList)) {
            return;
        }

        // 充值记录列表
        List<PayBillPo> payBillList = payBillRwDs.getPayBillList(billOrderIdList).stream()
            .filter(po -> po.getTaxType() == TaxType.UNKNOWN).collect(Collectors.toList());
        log.info("未开票充值记录: size={}", payBillList.size());

        // 查询资金消耗信息
        payBillList.forEach(po -> {
            if (StringUtils.isNotBlank(po.getFlowSeqNo())) {
                // 当前的充值记录可能存在实际金额和赠送金额两个积分块，需要分开对待
                String[] seqNoList = po.getFlowSeqNo().split(",");
                if (seqNoList.length > 0) {
                    // 充值资金块剩余信息
                    ListPointRecParam param = new ListPointRecParam();
                    param.setUid(po.getUserId().toString())
//                            .setSubUid(String.valueOf(po.getTopCommId()))
                        .setSeqNumList(Arrays.stream(seqNoList)
                            .map(Long::valueOf).collect(Collectors.toList()))
                        .setType(ResponseConvertUtils.formatPointType(po.getAccountType(),
                            po.getTopCommId()));

                    // @Nathan 商户会员的充值需要填入商户Id
                    if (PayAccountType.COMMERCIAL == po.getAccountType()) {
                        param.setSubUid(po.getAccountCode().toString());
                    } else {
                        param.setSubUid(po.getTopCommId().toString());
                    }

                    List<PointRecDto> recs = dcCusBalanceService.listPointRecord(param);

                    if (!recs.isEmpty()) {
                        // 实际成本剩余: totalCost != 0 为实际
                        // frozenCost + usedCost
                        BigDecimal remainCost = recs.stream()
                            .filter(rec -> !DecimalUtils.isZero(rec.getTotalCost()))
                            .map(PointRecPo::getAvailable).reduce(BigDecimal.ZERO, BigDecimal::add);

                        // 赠送成本剩余: totalCost == 0 为赠送
                        BigDecimal remainFree = recs.stream()
                            .filter(rec -> DecimalUtils.isZero(rec.getTotalCost()))
                            .map(PointRecPo::getAvailable).reduce(BigDecimal.ZERO, BigDecimal::add);

                        if (DecimalUtils.lteZero(remainCost.add(remainFree))) {
                            Integer i = payBillRwDs.updateStatus(
                                po.getOrderId(), po.getStatus(), PayBillStatus.EXHAUST.getCode());
                            log.info("充值记录状态已更新: orderId={}, i={}", po.getOrderId(), i);
                        } else if (po.getStatus() == PayBillStatus.EXHAUST.getCode()) {
                            // 存在余额情况, 调整回原来状态
                            Integer i = payBillRwDs.updateStatus(
                                po.getOrderId(), po.getStatus(), PayBillStatus.PAID.getCode());
                            log.info("充值记录状态回滚: orderId={}, i={}", po.getOrderId(), i);
                        }
                    }
                    return;
                }
            }

            // 获取资金块剩余金额信息
            Optional<PointRecLogDto> rlDto = rList.stream()
                .filter(rl -> po.getOrderId().equals(rl.getRecOrigOrderNo())).findFirst();

            if (rlDto.isPresent()) {
                if (DecimalUtils.lteZero(rlDto.get().getAvailable().add(rlDto.get().getFrozen()))) {
                    Integer i = payBillRwDs.updateStatus(
                        po.getOrderId(), po.getStatus(), PayBillStatus.EXHAUST.getCode());
                    log.info("充值记录状态已更新: orderId={}, i={}", po.getOrderId(), i);
                }
            }
        });
    }

//    public void rollbackPayBillTaxStatus(String orderNo) {
//        log.info("确认是否需要回滚充值记录的状态: orderNo={}", orderNo);
//
//        if (com.cdz360.base.utils.StringUtils.isBlank(orderNo)) {
//            throw new DcArgumentException("请提供充电订单号");
//        }
//
//        // 充电订单的资金块详情
//        List<PayBillLinkChargeOrderVo> voList = this.orderPointRecLog(orderNo);
////        if (null == response || null == response.getData()) {
////            log.error("data-core 服务异常: res = {}", response);
////        } else {
////            List<PayBillLinkChargeOrderVo> voList = response.getData();
//            voList.forEach(vo -> {
//                // 查看充值记录信息
//                PayBillPo po = payBillRwDs.findByOrderId(vo.getOrderId(), false);
//                if (null != po &&
//                        po.getTaxStatus() == TaxStatus.YES) {
//                    po.setTaxStatus(TaxStatus.NO);
//                    int update = payBillRwDs.update(po);
//                    log.info("充值记录发票状态回滚: update = {}", update);
//                }
//            });
////        }
//    }

    public RefundAnalyzeVo refundAnalyze(Date startDate,
        Date stopDate) {
        List<RefundReasonCountVo> voList = payBillRwDs.refundAnalyze(startDate, stopDate);
        RefundAnalyzeVo res = new RefundAnalyzeVo();
        for (RefundReasonCountVo e : voList) {
            if ("电桩故障".equals(e.getCusNote())) {
                res.setEvseErrorCount(e.getCusNoteCount());
            } else if ("油车占位".equals(e.getCusNote())) {
                res.setOccupiedByCarCount(e.getCusNoteCount());
            } else if ("收费太贵".equals(e.getCusNote())) {
                res.setTooExpensiveCount(e.getCusNoteCount());
            } else if ("客服服务".equals(e.getCusNote())) {
                res.setBadCustomerServiceCount(e.getCusNoteCount());
            } else if ("不愿充值".equals(e.getCusNote())) {
                res.setDistrustCount(e.getCusNoteCount());
            } else {
                res.setOthersCount(res.getOthersCount() + e.getCusNoteCount());
            }
        }
        log.info(JsonUtils.toJsonString(res));
        return res;
    }

    public ListResponse<RefundReasonVo> refundList(String cusName, String cusPhone, String cusNote,
        int start, int size) {
        List<RefundReasonVo> res = payBillRwDs.refundList(cusName, cusPhone, cusNote, start, size);
        long total = payBillRwDs.refundListCount(cusName, cusPhone, cusNote);
        return RestUtils.buildListResponse(res, total);
    }

    public Boolean checkRefPayBill(String refBillNo) {
        PayBillPo po = payBillRwDs.findByOrderId(refBillNo, false);
        if (null == po) {
            return false;
        }

        if (PayAccountType.CORP.equals(po.getAccountType())) {
            // 获取是否关联企业开票记录
            List<TaxOrderDto> list = payBillRoDs.findInvoicePayBill(List.of(refBillNo));
            if (CollectionUtils.isNotEmpty(list)) {
                CheckTaxStatus res = this.invoiceStatus(list);
                if (!CheckTaxStatus.NONE.equals(res)) {
                    return true;
                }

                // 企业客户开票占用
                return true;
            }
        }

        // 目前逻辑: 如果已经存在开票金额则认为已经关联开票
        return DecimalUtils.gtZero(po.getInvoicedAmount());
    }

    public Mono<ObjectResponse<PayBillVo>> payOrderQuery(String orderId) {
        if (StringUtils.isBlank(orderId)) {
            throw new DcArgumentException("充值单号无效");
        }

        PayBillPo po = payBillRoDs.findByOrderId(orderId, true);
        if (null == po) {
            throw new DcArgumentException("充值记录不存在");
        }

        return Mono.just(po)
            .map(this::poMap2Vo)
            .doOnNext(vo -> {
                if (PayBillStatus.UNPAID.getCode() == vo.getStatus()) {
                    // 查询第三方充值记录状态来调整充值状态
                    try {
                        pcpAsyncFeignClient.payOrderQuery(orderId)
                            .doOnNext(FeignResponseValidate::check)
                            .doOnNext(res -> {
                                if (StringUtils.equals("SUCCESS", res.getData().getResult_code())) {
                                    vo.setStatus(PayBillStatus.PAID.getCode());
                                }
                            }).subscribe(res -> log.info("充值单结果: order = {}", res.getData()));
                    } catch (Exception e) {
                        log.error("查询充值单异常: err = {}", e.getMessage(), e);
                    }
                }
            })
            .map(RestUtils::buildObjectResponse);
    }
}
