package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.SmartPriceService;
import com.cdz360.biz.model.trading.coupon.vo.UserInfoVo;
import com.cdz360.biz.oa.param.OaStartProcessParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/dataCore/smartPrice")
public class SmartPriceRest {

    @Autowired
    private SmartPriceService smartPriceService;


    @PostMapping("/generateSmartPrice")
    public Mono<ObjectResponse<String>> generateSmartPrice(@RequestBody OaStartProcessParam params,
        @RequestParam(value = "procInstId") String procInstId) {
        log.info("开始计算智能调价, procInstId = {}, params = {}", procInstId, params);
        UserInfoVo userInfoVo = new UserInfoVo();
        userInfoVo.setCommId(params.getCommId());
        userInfoVo.setCommIdChain(params.getCommIdChain());
        userInfoVo.setTopCommId(Long.parseLong(params.getTopCommId()));
        userInfoVo.setUserId(Long.parseLong(params.getOpId()));
        userInfoVo.setUserName(params.getOpName());
        userInfoVo.setUserPhone(params.getSysUserPhone());
        return Mono.just("success")
            .doOnNext(item -> {
                try {
                    smartPriceService.generateSmartPrice(params.getData(), procInstId, userInfoVo);
                } catch (Exception e) {
                    log.error("{}", e);
                }
            }).map(RestUtils::buildObjectResponse);
    }

    @PostMapping("/validateGenerateSmartPrice")
    public Mono<ObjectResponse<String>> validateGenerateSmartPrice(@RequestBody OaStartProcessParam params) {
        return Mono.just("success").flatMap(raw -> {
            try {
                smartPriceService.validateGenerateSmartPrice(params.getData());
            } catch (Exception e) {
                log.error("智能调价预校验失败, {}", e);
                return Mono.just(e.getMessage());
            }
            return Mono.just(raw);
        }).map(RestUtils::buildObjectResponse);
    }
}
