package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.dc.service.MsgSendService;
import com.cdz360.biz.model.trading.msg.param.MsgSendParam;
import com.cdz360.biz.model.trading.msg.param.ParkingLockErrorParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "设备告警微信模板消息发送", description = "设备告警发送微信模板消息")
public class MsgSendRest {

    @Autowired
    private MsgSendService msgSendService;

    @Operation(summary = "充电管理平台，订阅列表")
    @PostMapping("/dataCore/msgSend/sendMsg")
    public BaseResponse sendMsg(@RequestBody MsgSendParam param) {
        log.info("桩告警模板消息发送 = {}", JsonUtils.toJsonString(param));
        return msgSendService.sendMsg(param);
    }

    @GetMapping("/dataCore/msgSend/sendEvseCfgAlarm")
    public BaseResponse sendEvseCfgAlarm(@RequestParam(value = "evseNo") String evseNo,
                                         @RequestParam(value = "customWarningDesc", required = false) String customWarningDesc) {
        log.info("桩配置异常告警推送 evseNo: {}, customWarningDesc: {}", evseNo, customWarningDesc);
        return msgSendService.sendEvseCfgAlarm(evseNo, customWarningDesc);
    }

    @Operation(summary = "地锁告警推送", description = "桩管家公众号模板推送")
    @PostMapping("/dataCore/msgSend/parkingLockError")
    public BaseResponse sendParkingLockError(@RequestBody ParkingLockErrorParam errorParam) {
        log.info("地锁告警推送: param = {}", errorParam);
        return msgSendService.sendParkingLockError(errorParam);
    }
}
