package com.cdz360.biz.dc.domain.hlht;

import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.trading.hlht.dto.CecPolicyInfo;
import com.cdz360.biz.model.trading.hlht.dto.CecQueryQeuipBusinessPolicyResult;
import com.cdz360.data.sync.model.Site;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class HlhtSyncSiteDto extends Site {

    @Schema(description = "互联站点ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String openSiteId;

    private String operatorId;

    private String feeDesc;
    private List<ImageVo> images;
    private List<Evse> evseList;

    // 站点计费信息(站点的计费模板其实是取得第一个枪头的计费模板)
    @Schema(description = "计费信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<CecPolicyInfo> policyInfos;

    @Schema(description = "计费信息")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private CecQueryQeuipBusinessPolicyResult policyInfoResult;

    @Data
    public static class Evse {
        private String evseNo;
        private String evseName;
        private SupplyType supplyType;
        private Integer power;
        private List<Plug> plugList;
        private Boolean evsePolicyFlag;
    }


    @Data
    public static class Plug {

        private String plugNo;
        private String plugName;
    }

    @Data
    public static class ImageVo {
        private String url;
    }

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
