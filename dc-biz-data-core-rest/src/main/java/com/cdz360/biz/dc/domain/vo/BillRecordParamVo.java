package com.cdz360.biz.dc.domain.vo;

/**
 * <AUTHOR>
 *  流水记录
 * @since 2018/12/1 16:18
 */
//@Data
//public class BillRecordParamVo implements Serializable{
//
//
//    private static final long serialVersionUID = 1L;
//
//    /**
//     * 账单类型(1充值，2余额抵扣（支出），3.提现，4.保证金提取（保证金提现），
//     * 5.保证金缴纳，6.充电入账，7充电退款，8充电退款（退至余额）)
//     **/
//    private Integer type;
//
//    /**
//     * 流水金额(以分为单位)
//     **/
//    private Integer billAmount;
//
//    /**
//     * 商户编号
//     **/
//    private Long commercialId;
//
//    /**
//     * 备注
//     **/
//    private String remark;
//
//    /**
//     * 用户id
//     **/
//    private String userId;
//
//    /**
//     * 交易ip地址
//     **/
//    private String payIdAddr;
//
//    /**
//     *账号类型（0-个人，1-集团）
//     **/
//    private Integer accountType;
//
//    /**
//     * 用户名
//     **/
//    private String username;
//
//    /**
//     * 设备运营商
//     **/
//    private String deviceCarrieroperator;
//
//    /**
//     * 订单号
//     **/
//    private String orderId;
//
//    /**
//     * 支付方式（1~19微信 20~39支付宝 40银联 ，50线下支付,
//     * 60余额支付,70NFC储值卡,80无需支付、100其它）
//     **/
//    private Integer payModes;
//
//    /**
//     * 支付交易号
//     **/
//    private String payCertificateId;
//
//    /**
//     *  渠道类型（0:微信发起充电;1:APP在线发起充电;2:APP蓝牙发起充电;
//     *  3:刷卡充电;4:桩上报离线数据）
//     **/
//    private Integer channelId;
//
//    /**
//     * 平台类型（1公有云，2互联互通，3第三方）
//     * */
//    private Integer platformType;
//
//    /**
//     * 关联openId
//     **/
//    private String connectionOpenId;
//}
