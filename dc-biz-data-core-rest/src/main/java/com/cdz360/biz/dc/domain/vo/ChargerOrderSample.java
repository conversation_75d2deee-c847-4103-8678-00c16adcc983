package com.cdz360.biz.dc.domain.vo;

//
//@Data
//public class ChargerOrderSample implements Serializable {
//    /**
//     * 公有云订单编号
//     */
//    private Long orderId;
//
//    /**
//     * 站点ID
//     */
//    private String stationId;
//
//    /**
//     * 站点名称
//     */
//    private String stationName;
//
//
//    /**
//     * 插座二维码
//     */
//    private String qrCode;
//
//
//    /**
//     * 枪头编号
//     */
//    private String bcCode;
//
//
//    /**
//     * 充电时长
//     */
//    private String duration;
//
//    /**
//     * 订单电量
//     */
//    private Integer orderElectricity;
//
//    /**
//     * 订单金额
//     */
//    private Integer orderPrice;
//
//    /**
//     * 充电卡号/身份唯一识别号
//     */
//    private String cardNo;
//
//    /**
//     * 本金
//     */
//    private Integer principalAmount;
//
//    /**
//     * 充电开始时间
//     */
//    private Integer chargeStartTime;
//
//    /**
//     * 充电结束时间
//     */
//    private Integer chargeEndTime;
//
//    /**
//     * 车辆Vin码
//     */
//    private String vin;
//
//    /**
//     * 开始充点前电量百分比
//     */
//    private String startSoc;
//
//    /**
//     * 结束充电后电量百分比
//     */
//    private String stopSoc;
//
//    /**
//     * 结束原因
//     */
//    private String stopReason;
//
//    /**
//     * 显示字段 内容：出厂编号+evseId+connectorId
//     */
//    private String showId;
//
//
//    private String connectorId;
//
//    /**
//     * 充电接口(枪)编号
//     */
//    private String connectId;
//
//    /**
//     * 状态,-500：硬件故障；-40：上报超时；-35：断连超时；-30关电超时；-29：结束充电失败；-20：链接超时；-10：打开电闸超时；
//     * -7：开启充电失败，PIN可能错误；-5:系统繁忙；0：订单未激活；100：电闸打开；200：充电中；250：关电闸，充电停止；
//     * 300：充电完成；800：订单结束；1000：用户已支付；2000：钱已到账
//     */
//    private Integer status;
//
//
//    /**
//     * 订单创建时间
//     */
//    private Date createTime;
//
//
//    /**
//     * 订单来源类型,0:微信渠道;1:APP在线发起充电;2:APP蓝牙发起充电:3:刷卡充电:4:桩上报离线数据5定时充电6手动开启7曹操专车开启
//     */
//    private Integer channelId;
//
//    /**
//     * 当前电量百分比
//     */
//    private String currentSoc;
//
//
//    /**
//     * 手机号码
//     */
//    private String mobilePhone;
//
//
//    /**
//     * 盒子出厂编码
//     */
//    private String boxOutFactoryCode;
//
//    /**
//     * 客户实付金额
//     */
//    private Integer actualPrice;
//
//
//    /**
//     * 单次充电优惠金额--等级优惠金额(单位分)
//     */
//    private Long discountMoney;
//
//    /**
//     * 优惠券金额（单位分）
//     */
//    private Long couponMoney;
//
//    /**
//     * 支付方式
//     */
//    private Integer payModes;
//
//    /**
//     * 支付状态
//     */
//    private Integer payStatus;
//
//    /**
//     * 更新时间
//     */
//    private Date updateTime;
//
//    /**
//     * 枪头名称
//     */
//    private String chargerName;
//
//
//}
