package com.cdz360.biz.dc.domain.hlht;
//
//import com.alibaba.fastjson.annotation.JSONField;
//import com.fasterxml.jackson.annotation.JsonProperty;
//import lombok.Data;
//
//import java.io.Serializable;
//
///**
// * <AUTHOR>
// *  推送启动充电结果的输入参数
// * @since 2019-02-25 15:45
// */
//@Data
//public class NotificationStartChargeResultRequest implements Serializable{
//
//    /**
//     * 运营商ID
//     */
//    @JSONField(name = "OperatorID")
//    @JsonProperty(value = "OperatorID")
//    private String operatorId;
//
//    /**
//     * 运营商ID+唯一编号
//     */
//    @JSONField(name = "StartChargeSeq")
//    @JsonProperty(value = "StartChargeSeq")
//    private String startChargeSeq;
//
//    /**
//     * 充电设备接口编码
//     */
//    @JSONField(name = "ConnectorID")
//    @JsonProperty(value = "ConnectorID")
//    private String connectorID;
//
//    /**
//     * 充电订单状态
//     */
//    @JSONField(name = "StartChargeSeqStat")
//    @JsonProperty(value = "StartChargeSeqStat")
//    private Integer startChargeSeqStat;
//
//    /**
//     * 充电启动时间 格式"yyyy-MM-dd HH:mm:ss"
//     */
//    @JSONField(name = "StartTime")
//    @JsonProperty(value = "StartTime")
//    private String startTime;
//
//    /**
//     * 验证码
//     */
//    @JSONField(name = "IdentCode")
//    @JsonProperty(value = "IdentCode")
//    private String identCode;
//}
