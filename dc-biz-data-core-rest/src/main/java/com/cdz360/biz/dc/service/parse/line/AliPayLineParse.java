package com.cdz360.biz.dc.service.parse.line;

import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.model.trading.bill.po.ZftThirdOrderPo;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class AliPayLineParse implements LineParse {

    private static final int LINE_COL_NUM = 26;
    private static final List<Integer> LANG_XIN_LINE_COL_NUM = List.of(24, 25);

    private static final String MONEY_SUFFIX = "-";

    private static final String EXCHANGE = "交易";

    private static final String REFUND = "退款";

    private static final DateTimeFormatter FORMAT_yyyy_MM_dd_HH_mm_ss = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public String getSplit() {
        return ",";
    }

    @Override
    public BaseObject resolveLine(String rowData) {
        //判断参数
        if (StringUtils.isBlank(rowData)) {
            log.error("【支付宝文件解析】行数据为空!");
            return null;
        }

        //分解行数据
        String[] rowDataArray = rowData.split(this.getSplit());
        if (rowDataArray.length != LINE_COL_NUM) {
            log.error("【支付宝文件解析】无效记录：" + rowData);
            return null;
        }

        try {
            ZftThirdOrderPo thirdOrderPo = new ZftThirdOrderPo();

            // 服务费
            String fee = trimAll(rowDataArray[22]);
            thirdOrderPo.setPayFee(new BigDecimal(fee));

            LocalDateTime parse = LocalDateTime.parse(rowDataArray[5].trim(), FORMAT_yyyy_MM_dd_HH_mm_ss);
            thirdOrderPo.setTradeTime(Date.from(parse.atZone(ZoneId.systemDefault()).toInstant()));

            String tradeType = trimAll(rowDataArray[2]);
            if (EXCHANGE.equals(tradeType)) { // 充值
                thirdOrderPo.setChannelNo(trimAll(rowDataArray[0]));
                thirdOrderPo.setPlatformNo(trimAll(rowDataArray[1]));

                String amount = trimAll(rowDataArray[11]);
                thirdOrderPo.setTradeAmount(new BigDecimal(amount));
                thirdOrderPo.setTradeType(DepositFlowType.IN_FLOW);
            } else if (REFUND.equals(tradeType)) {
                thirdOrderPo.setChannelNo(trimAll(rowDataArray[0]));
                thirdOrderPo.setPlatformNo(trimAll(rowDataArray[21]));

                String refund = trimAll(rowDataArray[11]).replace(MONEY_SUFFIX, "");
                thirdOrderPo.setTradeAmount(new BigDecimal(refund));
                thirdOrderPo.setTradeType(DepositFlowType.OUT_FLOW);
            } else {
                thirdOrderPo.setTradeType(DepositFlowType.UNKNOWN);
            }

            return thirdOrderPo;
        } catch (Exception e) {
            log.error("数据异常，原始数据信息: {}", rowData, e);
        }
        return null;
    }

    @Override
    public BaseObject regexResolveLine(String rowData, String regex) {
        //判断参数
        if (StringUtils.isBlank(rowData)) {
            log.error("【支付宝文件解析】行数据为空!");
            return null;
        }

        //分解行数据
        String[] rowDataArray = rowData.split(this.getSplit());
        if (!LANG_XIN_LINE_COL_NUM.contains(rowDataArray.length)) {
            log.error("【支付宝文件解析】无效记录：" + rowData);
            return null;
        }

        try {
            ZftThirdOrderPo thirdOrderPo = new ZftThirdOrderPo();
            String reg = "^新电途\\(上海鼎充:(.*)\\)$"; // FIXME: 后续调整成配置形式
            Pattern compile = Pattern.compile(reg);
            Matcher matcher = compile.matcher(rowDataArray[3]);
            while (matcher.find()) {
                String group = matcher.group(1);
                thirdOrderPo.setOpenOrderId(group);
            }

            // 服务费
            String fee = trimAll(rowDataArray[22]);
            thirdOrderPo.setPayFee(new BigDecimal(fee));

            LocalDateTime parse = LocalDateTime.parse(rowDataArray[5].trim(), FORMAT_yyyy_MM_dd_HH_mm_ss);
            thirdOrderPo.setTradeTime(Date.from(parse.atZone(ZoneId.systemDefault()).toInstant()));

            String tradeType = trimAll(rowDataArray[2]);
            if (EXCHANGE.equals(tradeType)) { // 充值
                thirdOrderPo.setChannelNo(trimAll(rowDataArray[0]));
                thirdOrderPo.setPlatformNo(trimAll(rowDataArray[1]));

                String amount = trimAll(rowDataArray[11]);
                thirdOrderPo.setTradeAmount(new BigDecimal(amount));
                thirdOrderPo.setTradeType(DepositFlowType.IN_FLOW);
            } else if (REFUND.equals(tradeType)) {
                thirdOrderPo.setChannelNo(trimAll(rowDataArray[0]));
                thirdOrderPo.setPlatformNo(trimAll(rowDataArray[21]));

                String refund = trimAll(rowDataArray[11]).replace(MONEY_SUFFIX, "");
                thirdOrderPo.setTradeAmount(new BigDecimal(refund));
                thirdOrderPo.setTradeType(DepositFlowType.OUT_FLOW);
            } else {
                thirdOrderPo.setTradeType(DepositFlowType.UNKNOWN);
            }

            return thirdOrderPo;
        } catch (Exception e) {
            log.error("数据异常，原始数据信息: {}", rowData, e);
        }
        return null;
    }

    private static String trimAll(String target) {
        return target.trim()
                .replaceAll("\t", "");
    }
}
