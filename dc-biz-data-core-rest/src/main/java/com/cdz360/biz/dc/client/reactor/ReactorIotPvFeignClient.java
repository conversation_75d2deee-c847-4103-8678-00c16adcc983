package com.cdz360.biz.dc.client.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.model.iot.param.ListCtrlParam;
import com.cdz360.biz.model.iot.vo.GwInfoVo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

/**
 * ReactorUserFeignClient
 *
 * @since 11/3/2020 10:31 AM
 * <AUTHOR>
 */
@Deprecated
@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_IOT_PV)
public interface ReactorIotPvFeignClient {

    @PostMapping(value = "/iot/biz/pv/findCtrlList")
    Mono<ListResponse<GwInfoVo>> findCtrlList(@RequestBody ListCtrlParam param);
}