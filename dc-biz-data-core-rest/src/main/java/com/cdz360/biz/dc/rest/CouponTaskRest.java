package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.biz.dc.service.CouponTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * CouponTaskRest
 *
 * @since 8/4/2020 10:16 AM
 * <AUTHOR>
 */

@Slf4j
@RestController
@RequestMapping("/dataCore/couponTask")
public class CouponTaskRest {

    @Autowired
    private CouponTaskService couponTaskService;

    @GetMapping(value = "/refreshStatus")
    public BaseResponse refresh(@RequestParam(value = "trace", required = false) String trace) {
        log.debug("刷新券、模板、活动状态: {}", trace);
        couponTaskService.refresh(trace);
        return BaseResponse.success();
    }
}