package com.cdz360.biz.dc.service;

import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderRwDs;
import com.cdz360.biz.model.cus.type.CusPostStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class CusPostService {

    @Autowired
    private ChargerOrderRwDs chargerOrderRwDs;

    public Mono<Boolean> updateOrderCusPostStatus(
            String orderNo, CusPostStatus status) {
        return Mono.just(chargerOrderRwDs.updateCusPostStatus(orderNo, status));
    }
}
