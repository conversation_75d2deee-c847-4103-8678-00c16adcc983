package com.cdz360.biz.dc.service.invoice.callback;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.dc.domain.InvoiceCallbackMsg;
import com.cdz360.biz.dc.service.invoice.InvoiceProcess;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedDeque;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class InvoiceCallbackService implements InvoiceCallbackObserve, Runnable {

    @Autowired
    private InvoiceProcess invoiceProcess;

    private Queue<InvoiceCallbackMsg> msgQ = new ConcurrentLinkedDeque<>();

    @PostConstruct
    public void init() {
        new Thread(this, this.getClass().getName()).start();
    }

    /**
     * 用于问题排查
     */
    public void manualNotifyEvent() {
        log.info("thread name: {}", Thread.currentThread().getName());
        this.notifyEvent();
    }

    @Override
    public void notifyEvent() {
        synchronized (this) {
            log.info("size = {}", msgQ.size());
            this.notifyAll();
        }
    }

    @Override
    public void addEventMsg(InvoiceCallbackMsg msg) {
        this.msgQ.add(msg);
    }

    @Override
    public void run() {
        while (true) {
            try {
                synchronized (this) {
                    this.process();
                    this.wait();
                }
            } catch (Exception e) {
                log.error("处理异常: {}", e.getMessage(), e);
            }
        }
    }

    private void process() {
        while (!msgQ.isEmpty()) {
            InvoiceCallbackMsg msg = msgQ.remove();
            this.process(msg);
        }
    }

    /**
     * 业务处理
     *
     * @param msg
     */
    public void process(InvoiceCallbackMsg msg) {
        log.info("处理发票回调: msg = {}", JsonUtils.toJsonString(msg));
        if (null != msg.getInvoiceWay()) { // 企业开票
            switch (msg.getInvoiceWay()) {
                case POST_SETTLEMENT:
                    invoiceProcess.settlementInvoicedAmount(msg.isAppend(), msg.getApplyNo(), msg.getOrderNoList());
                    break;
                case PRE_PAY:
                    // 获取充电
                    invoiceProcess.payBillInvoicedAmount(msg.isAppend(), msg.getOrderNoList());
                    break;
                case POST_CHARGER:
                    invoiceProcess.postChargerHandler(msg.isAppend(), msg.getOrderNoList(), msg.getInvoiceId());
                    break;
                default:
                    break;
            }
        } else {
            // 非企业账户的充电订单开票
            invoiceProcess.postChargerHandler(msg.isAppend(), msg.getOrderNoList(),
                msg.getInvoiceId(), msg.getOaProcessDefinitionKey());
        }
        log.info("处理发票回调完成: invoiceId = {}", msg.getInvoiceId());
    }

    /**
     * 销毁时需要处理
     */
    public void destroy() {
        while (!msgQ.isEmpty()) {
            log.error("发票反馈需要手动处理: {}", JsonUtils.toJsonString(msgQ.remove()));
        }
    }


}
