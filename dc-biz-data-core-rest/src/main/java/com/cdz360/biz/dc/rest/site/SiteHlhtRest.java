package com.cdz360.biz.dc.rest.site;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.biz.dc.service.site.SiteHlhtService;
import com.cdz360.biz.model.trading.hlht.param.BindHlhtParam;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping(value = "/dataCore/siteHlht")
public class SiteHlhtRest {

    @Autowired
    private SiteHlhtService service;

    @Operation(summary = "商户绑定互联站点")
    @PostMapping(value = "/bindHlhtSite")
    public BaseResponse bindHlhtSite(@RequestBody BindHlhtParam param) {
        log.info("bindHlhtSite param: {}", param);

        return service.bindHlhtSite(param);
    }

    @Operation(summary = "商户解绑互联站点")
    @PostMapping(value = "/unbindHlhtSite")
    public BaseResponse unbindHlhtSite(@RequestBody BindHlhtParam param) {
        log.info("unbindHlhtSite param: {}", param);

        return service.unbindHlhtSite(param);
    }
}
