package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.dc.service.CorpProfitConfService;
import com.cdz360.biz.model.trading.profit.conf.po.CorpProfitConfPo;
import com.cdz360.biz.model.trading.profit.conf.vo.CorpProfitBaseVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * CorpProfitConfRest
 *
 * @since 2/23/2021 4:08 PM
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/dataCore/corpProfitConf")
@Tag(name = "企业收益计算相关接口")
public class CorpProfitConfRest {

    @Autowired
    private CorpProfitConfService corpProfitConfService;

//    @GetMapping("/getCorpProfitConf")
//    public ListResponse<CorpProfitConfPo> getCorpProfitConf(@RequestParam("corpId") Long corpId) {
//        return new ListResponse(corpProfitConfService.getCorpProfitConf(corpId));
//    }
    @GetMapping("/getCorpProfitConf")
    public ObjectResponse<CorpProfitBaseVo> getCorpProfitConf(@RequestParam("corpId") Long corpId) {
        log.info("getCorpProfitConf: {}", corpId);
        return new ObjectResponse<>(corpProfitConfService.getCorpProfitConf(corpId));
    }

    @PostMapping("/addCorpProfitConf")
    public BaseResponse addCorpProfitConf(@RequestBody CorpProfitBaseVo param) {
        log.info("addCorpProfitConf: {}", JsonUtils.toJsonString(param));
        return corpProfitConfService.addCorpProfitConf(param);
    }

    @PostMapping("/disableCorpProfitConf")
    public BaseResponse disableCorpProfitConf(@RequestParam("corpId") Long corpId) {
        log.info("disableCorpProfitConf: {}", corpId);
        return corpProfitConfService.disableCorpProfitConf(corpId);
    }

    // 计算上月企业收益
    @PostMapping("/computePrevMonthlyProfit")
    public BaseResponse computeMonthlyProfit() {
        log.info("计算上个月企业收益");
        BaseResponse baseResponse = corpProfitConfService.computePrevMonthlyProfit();
        log.info("计算上个月企业收益,完成");
        return baseResponse;
    }
}