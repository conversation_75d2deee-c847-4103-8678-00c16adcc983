package com.cdz360.biz.dc.client.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.chargerlinkcar.framework.common.domain.oa.NotifyResultParam;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class OaFeignClientHystrix implements FallbackFactory<OaFeignClient> {

    @Override
    public OaFeignClient apply(Throwable throwable) {
        log.debug("err = {}", throwable.getMessage(), throwable);

        return new OaFeignClient() {

            @Override
            public Mono<BaseResponse> notifyResult(NotifyResultParam params) {
                log.error(
                    "【服务熔断】。Service = {}, api = notifyResult(通知OA流程是否自动开票成功), params = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, params);
                return Mono.just(RestUtils.serverBusy());
            }

//            @Override
//            public Mono<ListResponse<InvoicingContentVo>> getInvoicingContent(String procInstId) {
//                log.error(
//                    "【服务熔断】。Service = {}, api = getInvoicingContent(获取开票内容), procInstId = {}",
//                    DcConstants.KEY_FEIGN_DC_BIZ_OA, procInstId);
//                return Mono.just(RestUtils.serverBusy4ListResponse());
//            }

            @Override
            public Mono<ObjectResponse<Boolean>> containSettJobBillNo(String billNo) {
                log.error("服务[{}]接口熔断 - 查询结算单是否已经被流程占用, param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, billNo);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }

            @Override
            public Mono<BaseResponse> updateNameByParams(Object params,
                String procInstId, String name){
                log.error("服务[{}]接口熔断 - 更新params数据失败, procInstId = {}, params = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_OA, procInstId, params);
                return Mono.just(RestUtils.serverBusy4ObjectResponse());
            }
        };
    }

    @Override
    public <V> Function<V, OaFeignClient> compose(Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_OA);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(Function<? super OaFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_OA);
        return null;
    }
}
