package com.cdz360.biz.dc.repository;

import com.cdz360.biz.dc.domain.SiteInMongoPo;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.geo.Box;
import org.springframework.data.geo.Point;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2019/9/16 18:09
 */
@Repository
public interface SiteMongoDataRepository extends CrudRepository<SiteInMongoPo, String> {

    List<SiteInMongoPo> findByPositionNear(Point point, Example example);

    List<SiteInMongoPo> findByPositionWithin(Point point, Example example);

    Page<SiteInMongoPo> findByPositionNear(Point point, double max, Pageable pageable);

    Page<SiteInMongoPo> findByPositionWithin(Box box, Pageable pageable);
}
