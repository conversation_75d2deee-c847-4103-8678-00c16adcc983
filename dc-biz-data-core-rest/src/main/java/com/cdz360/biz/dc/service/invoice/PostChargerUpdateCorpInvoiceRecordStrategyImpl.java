package com.cdz360.biz.dc.service.invoice;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.model.corp.type.CorpType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ds.trading.ro.invoice.ds.InvoiceRecordOrderRefRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderPayRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.InvoiceRecordRoDs;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.invoice.type.ProductType;
import com.cdz360.biz.model.oa.vo.BillInvoiceVo;
import com.cdz360.biz.model.trading.invoice.dto.CorpInvoiceRecordDto;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.po.ChargerOrderPayPo;
import com.chargerlinkcar.framework.common.constant.OrderStatus;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordUpdateParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedTemplateSalDetailVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrder;
import com.chargerlinkcar.framework.common.domain.vo.OrderBiVo;
import com.chargerlinkcar.framework.common.feign.AuthCenterFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PostChargerUpdateCorpInvoiceRecordStrategyImpl
    extends AbstractCorpInvoiceRecordStrategy {

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private UpdateCorpInvoiceRecordService updateCorpInvoiceRecordService;

    @Autowired
    private ChargerOrderRoDs chargerOrderRoDs;

    @Autowired
    private ChargerOrderPayRoDs chargerOrderPayRoDs;

    @Autowired
    private InvoiceRecordOrderRefRoDs invoiceRecordOrderRefRoDs;

    @Autowired
    private InvoiceRecordRoDs invoiceRecordRoDs;

    @Autowired
    private InvoiceProcess invoiceProcess;

    @PostConstruct
    public void init() {
        updateCorpInvoiceRecordService.addStrategy(InvoicingMode.POST_CHARGER, this);
    }

    @Override
    public CorpInvoiceRecordDto preUpdate(CorpInvoiceRecordUpdateParam param, boolean append) {
        // 初始化对象值
        CorpInvoiceRecordDto updateDto = super.preUpdate(param, append);

        // 统计充电订单金额
        ListChargeOrderParam orderParam = new ListChargeOrderParam();
        if (!Boolean.TRUE.equals(param.getOpAll())) {
            orderParam.setCommIdChain(param.getCommIdChain());
            orderParam.setOrderNoList(param.getOrderNoList());
            orderParam.setTotal(false);
        } else {
            updateDto.setFixElecFee(BigDecimal.ZERO)
                .setFixServFee(BigDecimal.ZERO)
                .setFixTotalFee(BigDecimal.ZERO)
                .setTotalFee(BigDecimal.ZERO)
                .setActualElecFee(BigDecimal.ZERO)
                .setActualServFee(BigDecimal.ZERO);
            if (param.getChargerOrderParam() != null) {
                orderParam = param.getChargerOrderParam();
            }
        }
        orderParam.setCorpId(param.getCorpId());

        BigDecimal elecFee = BigDecimal.ZERO;
        BigDecimal servFee = BigDecimal.ZERO;
        if (append || !Boolean.TRUE.equals(param.getOpAll())) {
            final ObjectResponse<CorpPo> corpRes = this.authCenterFeignClient.getCorp(
                param.getCorpId());
            FeignResponseValidate.check(corpRes);
            if (CorpType.HLHT == corpRes.getData().getType()
                && orderParam.getSettlementType() == null) {
                orderParam.setSettlementType(SettlementType.PARTNER);
            }

            if (StringUtils.isNotBlank(param.getInterimCode())) {
                orderParam.setInterimCode(param.getInterimCode());
            } else if (Boolean.TRUE.equals(param.getOpAll())) {
                param.getChargerOrderParam().setSettlementType(orderParam.getSettlementType());
            }

            OrderBiVo orderBi;
            if (append) {
                orderBi = chargerOrderRoDs.corpInvoiceOrderBi(orderParam);
            } else {
                orderParam.setApplyNo(param.getApplyNo());
                orderBi = invoiceRecordOrderRefRoDs.countChargerOrder(orderParam);
            }

            // 优先填电费
            BigDecimal totalFee = orderBi.getInvoiceAmount();
            BigDecimal totalElecFee = orderBi.getTotalElecAmount();

            if (DecimalUtils.gt(totalFee, totalElecFee)) {
                elecFee = totalElecFee;
                servFee = totalFee.subtract(totalElecFee);
            } else {
                elecFee = totalFee;
            }

            // 产品: 根据商品行信息调整
            List<InvoicedTemplateSalDetailVo> detailVoList = param.getCorpInvoiceInfoVo()
                .getTempRefVo().getDetailVoList();
            if (CollectionUtils.isEmpty(detailVoList)) {
                throw new DcArgumentException("商品行还没有配置");
            }

            Optional<InvoicedTemplateSalDetailVo> elec = detailVoList.stream()
                .filter(item -> item.getProductType() == ProductType.ELEC_ACTUAL_FEE)
                .findFirst();
            Optional<InvoicedTemplateSalDetailVo> serv = detailVoList.stream()
                .filter(item -> item.getProductType() == ProductType.SERV_ACTUAL_FEE)
                .findFirst();
            if (elec.isPresent() && serv.isPresent()) {
                // nothing to do
            } else if (serv.isPresent()) { // 设置服务费，没有电费
                servFee = elecFee.add(servFee);
                elecFee = BigDecimal.ZERO;
            } else {
                elecFee = elecFee.add(servFee);
                servFee = BigDecimal.ZERO;
            }
        }

        this.updateFee(append, updateDto, servFee, elecFee);
        return updateDto;
    }

    /**
     * 过滤无效的orderNo，并返回对应的可开票金额
     *
     * @return String：orderNo； BigDecimal：可开票金额
     */
    @Override
    public Optional<Map<String, BigDecimal>> orderNoList(CorpInvoiceRecordUpdateParam param,
        long start, int size) {
        if (StringUtils.isNotBlank(param.getInterimCode())) {
            List<BillInvoiceVo> list = chargerOrderPayRoDs.getBillInvoiceVoListByInterimCode(
                param.getInterimCode(), true, start, size, true);
            if (CollectionUtils.isEmpty(list)) {
                return Optional.empty();
            }
            return Optional.of(list.stream().filter(Objects::nonNull).collect(
                Collectors.toMap(BillInvoiceVo::getOrderNo, BillInvoiceVo::getInvoiceAmount)));
        }

        param.getChargerOrderParam()
            .setInCorpInvoice(true)
            .setStatusList(List.of(OrderStatus.ORDER_STATUS_RECEIVE_MONEY)) // 已结算的充电订单
            .setStart(start)
            .setSize(size)
            .setTotal(false);
        ListResponse<ChargerOrder> res = chargerOrderRoDs.listChargeOrder4Master(
            param.getChargerOrderParam());
        if (CollectionUtils.isEmpty(res.getData())) {
            return Optional.empty();
        }
        return Optional.of(res.getData().stream().filter(Objects::nonNull)
            .collect(Collectors.toMap(ChargerOrder::getOrderNo, ChargerOrder::getInvoiceAmount)));
    }

    /**
     * 过滤无效的orderNo，并返回对应的可开票金额
     *
     * @return String：orderNo； BigDecimal：可开票金额
     */
    @Override
    public Optional<Map<String, BigDecimal>> orderNoList(@NonNull List<String> orderNoList) {
        List<ChargerOrderPayPo> res = chargerOrderPayRoDs.getOrderInvoiceInfos(orderNoList);
        if (CollectionUtils.isEmpty(res)) {
            return Optional.empty();
        }
        return Optional.of(res.stream().filter(Objects::nonNull).collect(
            Collectors.toMap(ChargerOrderPayPo::getOrderNo, ChargerOrderPayPo::getInvoiceAmount)));
    }

    @Override
    public void updateException(CorpInvoiceRecordUpdateParam param, String applyNo,
        boolean append) {
        // nothing to do
    }
}
