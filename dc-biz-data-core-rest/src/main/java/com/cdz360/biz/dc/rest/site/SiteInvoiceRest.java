package com.cdz360.biz.dc.rest.site;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.dc.service.site.SiteInvoiceService;
import com.cdz360.biz.model.site.dto.UpdateSiteInvoicedValidDto;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/dataCore/site/invoice")
@Tag(name = "站点开票相关接口")
public class SiteInvoiceRest {

    @Autowired
    private SiteInvoiceService siteInvoiceService;

    @Operation(summary = "调整场站的开票状态")
    @PostMapping(value = "/updateInvoicedValid")
    public Mono<BaseResponse> updateInvoicedValid(
        ServerHttpRequest request,
        @RequestBody UpdateSiteInvoicedValidDto dto) {
        log.info("调整场站的开票状态: {}, dto = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(dto));
        return siteInvoiceService.updateInvoicedValid(dto);
    }

    @Operation(summary = "调整场站的开票状态")
    @PostMapping(value = "/notifyMobileTempSal")
    public Mono<BaseResponse> notifyMobileTempSal(
        ServerHttpRequest request,
        @RequestParam(name = "oldTempSalId") Long oldTempSalId,
        @RequestParam(name = "newTempSalId") Long newTempSalId) {
        log.info("通知场站更新绑定的移动端开票主体ID: {}, oldTempSalId = {}, newTempSalId = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(oldTempSalId),
            JsonUtils.toJsonString(newTempSalId));
        return siteInvoiceService.updateTempSalId(oldTempSalId, newTempSalId);
    }
}
