package com.cdz360.biz.dc.domain;

import com.cdz360.base.model.base.type.InvoicingMode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "发票回调")
public class InvoiceCallbackMsg {

    @Schema(description = "开票与否 true -- 表示申请开票; false -- 表示取消开票")
    private boolean append;

    @Schema(description = "开票申请单号")
    private String applyNo;

    @Schema(description = "充电订单列表")
    private List<String> orderNoList;

    @Schema(description = "发票记录ID")
    private Long invoiceId;

    @Schema(description = "企业客户开票方式: 充值预开票(PRE_PAY)、充电后开票(POST_CHARGER)、后付费账单开票(POST_SETTLEMENT)企业开票时使用")
    private InvoicingMode invoiceWay;

    @Schema(description = "OA流程定义KEY")
    private String oaProcessDefinitionKey;

}
