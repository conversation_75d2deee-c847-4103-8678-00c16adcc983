package com.cdz360.biz.dc.utils;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.model.common.constant.ExcelCheckResult;
import com.cdz360.biz.model.iot.param.OfflineEvseParam;
import com.cdz360.biz.model.iot.vo.EvseImportVo;
import com.cdz360.biz.model.iot.vo.EvseModelVo;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.service.excel.BufferedExcelConvertor;
import com.chargerlinkcar.framework.common.service.excel.ExcelConvertor;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.RegularExpressionUtil;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EvseExcelConvertor extends ExcelConvertor<EvseImportVo> {

    private static final Integer CELL_LENGTH = 10;

    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;
    @Autowired
    private BufferedExcelConvertor excelConvertor;

    @PostConstruct
    public void init() {
        this.excelConvertor.addExcelConvertorMap(ExcelCheckResult.EVSE_VALID, this);
    }

    @Override
    public Integer getCellLength() {
        return CELL_LENGTH;
    }

    @Override
    public EvseImportVo parse(List<List<String>> lineList) {
        List<EvseModelVo> valid = new ArrayList<>(); // 有效
        List<EvseModelVo> invalid = new ArrayList<>(); // 无效
        lineList.forEach(evse -> {
            EvseModelVo vo = new EvseModelVo();
            vo.setEvseId(
                null == evse.get(0) || evse.get(0) == "" ? null : evse.get(0).trim()); // 电桩编号
            vo.setName(
                null == evse.get(1) || evse.get(1) == "" ? null : evse.get(1).trim());   // 电桩名称
            vo.setModel(
                null == evse.get(2) || evse.get(2) == "" ? null : evse.get(2).trim());  // 电桩型号
            vo.setBrand(
                null == evse.get(3) || evse.get(3) == "" ? null : evse.get(3).trim());  // 品牌
            vo.setPowerStr(
                null == evse.get(4) || evse.get(4) == "" ? null : evse.get(4).trim());  // 额定功率
            vo.setPlugNumStr(
                null == evse.get(5) || evse.get(5) == "" ? null : evse.get(5).trim());  // 枪头数
            vo.setUseSiteSettingStr(
                null == evse.get(6) || evse.get(6) == "" ? null : evse.get(6).trim()); // 是否使用场站通用配置
            vo.setPhysicalNo(
                null == evse.get(7) || evse.get(7) == "" ? null : evse.get(7).trim()); // 出厂编号
            vo.setSimIccid(
                null == evse.get(8) || evse.get(8) == "" ? null : evse.get(8).trim()); // ICCID
            vo.setSimMsisdn(
                null == evse.get(9) || evse.get(9) == "" ? null : evse.get(9).trim());  // MSISDN

            ExcelCheckResult checkResult = checkEvseFormat(vo);
            if (checkResult.getCode() != ExcelCheckResult.EVSE_VALID.getCode()) {
                vo.setDetail(checkResult.getDesc());
                invalid.add(vo);
            } else {
                valid.add(vo);
                vo.strFieldsConvert();
            }
        });
        return new EvseImportVo(valid, invalid);
    }

    private ExcelCheckResult checkEvseFormat(EvseModelVo evse) {

        // 有效性检测
        if (StringUtils.isEmpty(evse.getEvseId())
            || StringUtils.isEmpty(evse.getName())
            || StringUtils.isEmpty(evse.getModel())
            || StringUtils.isEmpty(evse.getBrand())
            || StringUtils.isEmpty(evse.getPowerStr())
            || StringUtils.isEmpty(evse.getPlugNumStr())
            || StringUtils.isEmpty(evse.getUseSiteSettingStr())) {
            return ExcelCheckResult.EVSE_INVALID;
        }

        // evseNO规则1~20位数字
        if (!RegularExpressionUtil.digitAll(evse.getEvseId()) ||
            !(1 <= evse.getEvseId().length() && evse.getEvseId().length() <= 20)) {
            return ExcelCheckResult.EVSE_NO_INVALID;
        }

        // evseName: 最大20位
        if (StringUtils.isNotEmpty(evse.getName())) {
            if (!(evse.getName().length() <= 20)) {
                return ExcelCheckResult.EVSE_NAME_INVALID;
            }
        }

        // useSiteSetting 必须为 是或否
        if (!evse.getUseSiteSettingStr().equals("是")
            && !evse.getUseSiteSettingStr().equals("否")) {
            return ExcelCheckResult.EVSE_USE_SITE_DEFAULT_INVALID;
        }

        if (StringUtils.isNotBlank(evse.getPhysicalNo())) {
            if (!(evse.getPhysicalNo().length() <= 20)) {
                return ExcelCheckResult.EVSE_PHYSICAL_NO_INVALID;
            }
        }

        // iccid 20位以内数字字母组成
        if (StringUtils.isNotEmpty(evse.getSimIccid())) {
            if (!RegularExpressionUtil.englishNumberAll(evse.getSimIccid(), 1, 20)) {
                return ExcelCheckResult.EVSE_ICCID_INVALID;
            }
        }

        // msisdn 15位以内数字字母组成
        if (StringUtils.isNotEmpty(evse.getSimMsisdn())) {
            if (!RegularExpressionUtil.englishNumberAll(evse.getSimMsisdn(), 1, 15)) {
                return ExcelCheckResult.EVSE_MSISDN_INVALID;
            }
        }
        return ExcelCheckResult.EVSE_VALID;
    }

    @Override
    public EvseImportVo verify(EvseImportVo t) {
        HashSet<String> vin = new HashSet<>();
        HashSet<String> vinLst = new HashSet<>();
        EvseImportVo importVo = checkEvseInExcel(vin, t.getValid(), t.getInvalid());
        log.info("valid.size = {}, invalid.size = {}", importVo.getValid().size(),
            importVo.getInvalid().size());
        //相同数据第一次add的时候是能插入HashSet的，需将这部分数据也标记为重复数据
        importVo.getInvalid().forEach(evseVo -> {
            vinLst.add(evseVo.getEvseId());
        });
        importVo = checkEvseInExcel(vinLst, importVo.getValid(), t.getInvalid());
        log.info("valid.size = {}, invalid.size = {}", importVo.getValid().size(),
            importVo.getInvalid().size());
        importVo = checkSimInExcel(importVo.getValid(), t.getInvalid());
        log.info("valid.size = {}, invalid.size = {}", importVo.getValid().size(),
            importVo.getInvalid().size());
        importVo = checkEvseInDatebase(importVo.getValid(), t.getInvalid());
        log.info("valid.size = {}, invalid.size = {}", importVo.getValid().size(),
            importVo.getInvalid().size());
        return importVo;
    }

    public EvseImportVo checkEvseInExcel(HashSet<String> evseSet, List<EvseModelVo> evseList,
        List<EvseModelVo> invalid) {
        EvseImportVo importVo = new EvseImportVo();
        List<EvseModelVo> valid = new ArrayList<>(); // 新的有效数据集合

        evseList.forEach(evse -> {
            ExcelCheckResult checkResult = null;
            if (evseSet.add(evse.getEvseId())) {
                checkResult = ExcelCheckResult.EVSE_VALID;
            } else {
                checkResult = ExcelCheckResult.EXCEL_EXIST;
            }
            evse.setDetail(checkResult.getDesc());
            if (checkResult.getCode() != ExcelCheckResult.EVSE_VALID.getCode()) {
                invalid.add(evse);
            } else {
                valid.add(evse);
            }
        });
        importVo.setValid(valid);
        importVo.setInvalid(invalid);
        return importVo;
    }


    public EvseImportVo checkSimInExcel(List<EvseModelVo> evseList, List<EvseModelVo> invalid) {
        EvseImportVo importVo = new EvseImportVo();
        List<EvseModelVo> valid = new ArrayList<>(); // 新的有效数据集合

        List<String> repeatedIccid = Stream.concat(evseList.stream(), invalid.stream())
            .filter(e -> org.apache.commons.lang.StringUtils.isNotBlank(e.getSimIccid()))
            .collect(Collectors.groupingBy(EvseModelVo::getSimIccid, Collectors.counting()))
            .entrySet().stream().filter(e -> e.getValue() > 1)
            .map(Map.Entry::getKey)
            .collect(Collectors.toList());
        List<String> repeatedMsisdn = Stream.concat(evseList.stream(), invalid.stream())
            .filter(e -> org.apache.commons.lang.StringUtils.isNotBlank(e.getSimMsisdn()))
            .collect(Collectors.groupingBy(EvseModelVo::getSimMsisdn, Collectors.counting()))
            .entrySet().stream().filter(e -> e.getValue() > 1)
            .map(Map.Entry::getKey)
            .collect(Collectors.toList());

        evseList.forEach(evse -> {
            ExcelCheckResult checkResult = null;
            if (org.apache.commons.lang.StringUtils.isNotBlank(evse.getSimIccid())
                && repeatedIccid.contains(evse.getSimIccid())) {
                checkResult = ExcelCheckResult.ICCID_REPEATED;
            } else if (org.apache.commons.lang.StringUtils.isNotBlank(evse.getSimMsisdn())
                && repeatedMsisdn.contains(evse.getSimMsisdn())) {
                checkResult = ExcelCheckResult.MSISDN_REPEATED;
            } else {
                checkResult = ExcelCheckResult.EVSE_VALID;
            }
            evse.setDetail(checkResult.getDesc());
            if (checkResult.getCode() != ExcelCheckResult.EVSE_VALID.getCode()) {
                invalid.add(evse);
            } else {
                valid.add(evse);
            }
        });
        importVo.setValid(valid);
        importVo.setInvalid(invalid);
        return importVo;
    }

    /**
     * 校验数据
     *
     * @param evseList 需要判断的数据
     * @param invalid  无效的数据
     * @return
     */
    public EvseImportVo checkEvseInDatebase(List<EvseModelVo> evseList, List<EvseModelVo> invalid) {
        EvseImportVo vinMgnListVo = new EvseImportVo();
        List<EvseModelVo> valid = new ArrayList<>(); // 新的有效数据集合

        if (CollectionUtils.isEmpty(evseList)) {
            vinMgnListVo.setValid(valid);
            vinMgnListVo.setInvalid(invalid);
            return vinMgnListVo;
        }

        List<OfflineEvseParam> params = evseList.stream().map(e -> {
            OfflineEvseParam temp = new OfflineEvseParam();
            temp.setEvseNo(e.getEvseId());
            temp.setModel(e.getModel());
            temp.setBrand(e.getBrand());
            temp.setPower(e.getPower());
            temp.setPlugNum(e.getPlugNum());
            temp.setIccid(e.getSimIccid());
            temp.setMsisdn(e.getSimMsisdn());
            return temp;
        }).collect(Collectors.toList());
        ListResponse<OfflineEvseParam> response = iotDeviceMgmFeignClient.checkEvseInDB(params);
        FeignResponseValidate.check(response);
        Map<String, OfflineEvseParam> evseNoMap = response.getData().stream()
            .collect(Collectors.toMap(OfflineEvseParam::getEvseNo, o -> o));

        evseList.forEach(evse -> {
            ExcelCheckResult checkResult = null;
            OfflineEvseParam checkRes = evseNoMap.get(evse.getEvseId());

            if (Boolean.TRUE.equals(checkRes.getIsExist())) {
                checkResult = ExcelCheckResult.EXCEL_ALREADY_EXIST;
            } else if (Boolean.FALSE.equals(checkRes.getIsSimExist())) {
                checkResult = ExcelCheckResult.EVSE_SIM_INEXISTENCE;
            } else if (Boolean.TRUE.equals(checkRes.getIsSimBind())) {
                checkResult = ExcelCheckResult.EVSE_SIM_IS_BINDING;
            } else if (Boolean.FALSE.equals(checkRes.getIsModelBrandUniqueKey())) {
                checkResult = ExcelCheckResult.EVSE_MODEL_BRAND_INVALID;
            } else {
                evse.setSimId(checkRes.getSimId());
                evse.setModelId(checkRes.getModelId());
                checkResult = ExcelCheckResult.EVSE_VALID;
            }
            evse.setDetail(checkResult.getDesc());
            if (checkResult.getCode() != ExcelCheckResult.EVSE_VALID.getCode()) {
                invalid.add(evse);
            } else {
                valid.add(evse);
            }
        });
        vinMgnListVo.setValid(valid);
        vinMgnListVo.setInvalid(invalid);
        return vinMgnListVo;
    }
}
