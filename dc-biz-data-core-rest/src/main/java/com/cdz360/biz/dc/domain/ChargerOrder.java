package com.cdz360.biz.dc.domain;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 *  订单基础信息表
 * @since 2018/11/22 11:29
 */
@Data
public class ChargerOrder implements Serializable{

    protected static final long serialVersionUID = 1L;

    /**
     * 公有云订单编号
     */
    protected Long orderId;

    private Long topCommId;
    /**
     * 商户ID
     */
    // protected Long commercialId;
    /**
     * 站点ID
     */
    protected String stationId;
    /**
     * 站点ID
     */
    protected String stationIdStr;

    /**
     * 互联互通订单号
     */
    protected String openOrderId;

    /**
     * 用户ID
     */
    protected Long customerId;
    /**
     * 用户名称
     */
    protected String customerName;

    /**
     * 充电卡号/身份唯一识别号
     */
    protected String cardNo;

    /**
     * 订单来源类型,0:微信渠道;1:APP在线发起充电;2:APP蓝牙发起充电:3:刷卡充电:
     * 4:桩上报离线数据5定时充电6手动开启7曹操专车开启
     */
    protected Integer channelId;

    /**
     * 订单金额 -电费+服务费
     */
    protected Integer orderPrice;
    /**
     * 订单金额 -服务费
     */
    protected Integer servicePrice;
    /**
     * 订单金额 -电费
     */
    protected Integer elecPrice;

    /**
     * 订单电量
     */
    protected Integer orderElectricity;

    /**
     * 状态,-500：硬件故障；-40：上报超时；-35：断连超时；-30关电超时；-29：结束充电失败；
     * -20：链接超时；-10：打开电闸超时；-7：开启充电失败，PIN可能错误；-5:系统繁忙；
     * 0：订单未激活；100：电闸打开；200：充电中；250：关电闸，充电停止；300：充电完成；
     * 800：订单结束；1000：用户已支付；2000：钱已到账
     */
    protected Integer status;

    /**
     * 星标标志，默认0，0：非星标；1：星标；
     */
//    protected Integer starFlag;

    /**
     * 计费模板ID
     */
    protected Long priceSchemeId;

//    /**
//     * 充电车位ID
//     */
//    protected Long carportId;
//
//    /**
//     * 车位名称
//     */
//    protected String carportName;

    /**
     * 备注
     */
    protected String remark;

    /**
     * 订单创建时间
     */
    protected Date createTime;
    /**
     * 当前数据的记录创建时间,数据库自动创建
     */
//    protected Date createDateTime;

    /**
     * 订单停止时间
     */
    protected Date stopTime;

    /**
     * 订单修改时间
     */
    protected Date updateTime;

    /**
     * CORE订单ID
     */
//    protected Long coreOrderId;

    /**
     * 硬件流水号
     */
    protected String tradeNo;

    /**
     * 厂商盒子类型
     */
    protected Integer boxType;

    /**
     * 充电接口(枪)编号
     */
    protected String connectId;
    /**
     * 显示字段 内容：出厂编号+evseId+connectorId
     */
    private String showId;
    /**
     * 充电开始时间
     */
    protected Integer chargeStartTime;

    /**
     * 充电结束时间
     */
    protected Integer chargeEndTime;

    /**
     * 充电开始电量
     */
    protected Integer startElectricity;

    /**
     * 充电结束电量
     */
    protected Integer endElectricity;

    /**
     * 订单来源类型,0:微信渠道;1:APP在线发起充电;2:APP蓝牙发起充电:3:刷卡充电:4:桩上报离线数据
     */
    protected Integer type;

    /**
     * 充电二维码后四位更换1位蓝牙位，1位网络位，2位pin码
     */
    protected String pin;

    /**
     * platformId区分app或者微信或者其他平台发起的充电的回传标示
     */
    protected String platformId;

//    /**
//     * 充电记录
//     */
//    protected String statusChangeLog;
//    /**
//     * 人工修改充电记录
//     */
//    protected String statusChangeLogManual;

    /**
     * 站点名称
     */
    private String stationName;
    /**
     * 手机号码
     */
    private String mobilePhone;
    /**
     * 人工调整后金额 默认值为-1
     */
    private Integer manualPrice;
    /**
     * 客户实付金额
     */
    private Integer actualPrice;
    /**
     * 支付方式
     */
    private Integer payModes;
    /**
     * 支付状态 1：待支付；2：已支付；3：支付失败
     */
    private Integer payStatus;
    /**
     * 订单类型，0:未知，1：平台，2：互联互通，3：第三方，4：离线订单
     */
    private Integer orderType;
//    /**
//     * 结算方式，0:未知，1：后付费，2：预付费
//     */
//    private Integer clearingMode;
    /**
     * 客户运营商名称
     */
    private String customerCommercialName;
    /**
     * 客户运营商ID
     */
    //private Long customerCommercialId;
    /**
     * 设备运营商名称
     */
    private String commercialName;
    /**
     * 互联互通运营商ID
     */
    private String openOperatorId;
    /**
     * 预付金额
     */
//    private Integer prepayAmount;
    /**
     * 是否桩端计费
     */
//    private Integer calcPriceByCharger;

    /**
     * 预充电时长 单位秒
     */
//    private Integer preDuration;

    /**
     * core推送发起充电超时，通知第三方结束订单标志 0:正常 其他异常
     */
    private Integer timeoutStatus;

    // 实体附加字段

    /**
     * 订单时长
     */
    private String duration;

    /**
     * 本金
     */
    private Integer principalAmount;

    /**
     * 赠送金
     */
    private Integer freeGoldAmount;

    /**
     * 盒子编码
     */
    private String boxCode;

    /**
     * 盒子出厂编码
     */
//    private String boxOutFactoryCode;

    /**
     * 客户订单金额，向客户提供商(客户)收取的钱
     */
//    protected Integer customerPrice;

    /**
     * 渠道商ID
     */
    protected Long deviceCommercialId;

    /**
     * 计费模版快照记录,存json
     */
//    protected String setOrderPriceRemark;

    /**
     * 子账户id
     */
    protected Integer merchantId;

    /**
     * 客户端版本
     */
    protected String version;

    /**
     * 实时功率
     */
    protected String rtPower;

    /**
     * 计费模板是否改变过
     */
    protected String templatePowerIsChanged;

    /**
     * 插座二维码
     */
    protected String qrCode;

    /**
     * 插座序列号
     */
    protected String connectorId;

    /**
     * 订单异常结束原因
     */
    protected String exceptionReason;


    /**
     * 上报订单的设备类型
     */
//    protected String orderDiviceType;

    /**
     * 充电是否提前结束
     */
    protected int endUnexpected;

    /**
     * 实际补贴金额(单位：分)
     */
//    protected Integer actualSubsidyAmount;

    /**
     * 补贴是否分润(0:不参与分润  1:参与分润 )
     */
//    protected Integer subsidyShareProfitStatus;

    /**
     * 补贴类型(1:固定补贴 )
     */
//    protected Integer subsidyType;

    /**
     * 补贴时长(单位：分)
     */
//    protected String subsidyDuration;

    /**
     * vin码
     */
    protected String vin;

    /**
     * 开始SOC
     */
    protected String startSoc;

    /**
     * 结束SOC
     */
    protected String stopSoc;

    /**
     * 停止原因
     */
    protected String stopReason;

    /**
     * 当前SOC
     */
    protected String currentSoc;

    /**
     * 补贴金
     */
    protected Integer discountMoney;

    /**
     * 优惠券
     */
    protected Integer couponMoney;

    /**
     * 活动Id
     */
    protected Long activityId;

    /**
     * 支付时间
     */
    private String payTime;

    /**
     * 枪头名称
     */
    private String chargerName;


    public ChargerOrder() {
    }

    public ChargerOrder(Long orderId, String cardNo, Integer channelId, String pin, String platformId, String openOrderId) {
        this.orderId = orderId;
        this.cardNo = cardNo;
        this.channelId = channelId;
        this.pin = pin;
        this.platformId = platformId;
        this.openOrderId = openOrderId;
    }

    /**
     * @param statusDateLong
     * @param remark
     *  设置订单状态日志
     * @showdocUrl:
     */
//    public void setChargerStatusChangeLog(JSONObject remark, Long statusDateLong) {
//        List<Object> listObj = new ArrayList<>();
//        String statusChargerLog = this.getStatusChangeLog();
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("time", statusDateLong);
//
//        if (remark != null) {
//            jsonObject.put("remark", remark);
//            //停止充电不添加状态
//            if (remark.get("orderStatus") != "stop") {
//                jsonObject.put("status", this.getStatus());
//            }
//        } else {
//            jsonObject.put("status", this.getStatus());
//        }
//
//
//        if (statusChargerLog == null) {
//            listObj.add(jsonObject);
//        } else {
//            listObj = (List<Object>) JSONObject.parse(statusChargerLog);// 将日志转化成 list 集合
//            listObj.add(jsonObject);
//        }
//        this.setStatusChangeLog(JsonUtils.toJsonString(listObj));
//    }
}
