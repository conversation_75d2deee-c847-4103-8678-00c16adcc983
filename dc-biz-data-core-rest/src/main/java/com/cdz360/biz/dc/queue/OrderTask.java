package com.cdz360.biz.dc.queue;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.service.ChargerOrderBizService;
import com.cdz360.biz.dc.utils.RedisUtil;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargeOrderReserveDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteChargeJobLogRwDs;
import com.cdz360.biz.model.trading.order.type.OrderChannelType;
import com.cdz360.biz.model.trading.site.po.SiteChargeJobLogPo;
import com.cdz360.biz.utils.feign.order.TdChargeFeignClient;
import com.chargerlinkcar.framework.common.domain.ChargeOrderReserve;
import com.chargerlinkcar.framework.common.domain.request.StopChargerRequest;
import com.chargerlinkcar.framework.common.domain.vo.CloudChargeVo;
import com.chargerlinkcar.framework.common.domain.vo.CreateChargeOrderParam;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.PlugNoUtils;
import java.time.Duration;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class OrderTask {

    @Value("${iot.cloudCharge.pollNum:10}")
    private Integer pollNum;//每次从队列中取多少个进行消费操作
    //    @Autowired
//    private OrderFeignClient orderFeignClient;
    //    @Autowired
//    private ChargerOrderFeignClient chargerOrderFeignClient;
    @Autowired
    private TdChargeFeignClient tdChargeFeignClient;
    @Autowired
    private ChargeOrderReserveDs chargeOrderReserveDs;
    @Autowired
    private SiteChargeJobLogRwDs siteChargeJobLogRwDs;
    @Autowired
    private ChargerOrderBizService chargerOrderBizService;
    @Autowired
    private RedisUtil redisUtil;

    private AtomicLong startCount = new AtomicLong(1L);
    private AtomicLong stopCount = new AtomicLong(1L);


    @Scheduled(fixedRate = 1000)
    public void start() {
        long count = startCount.getAndAdd(1L);
        if (count % (60 * 5) == 0) {
            log.info("stop charge. count = {}", count);
        }
        if (count > (Long.MAX_VALUE - 10)) {
            startCount.set(1L);
        }
        try {
            this.threadLoop();
        } catch (Exception e) {
            log.warn("error = {}", e.getMessage(), e);
        }
    }

    @Scheduled(fixedRate = 1000)
    public void stop() {
        long count = stopCount.getAndAdd(1L);
        if (count % (60 * 5) == 0) {
            log.info("stop charge. count = {}", count);
        }
        if (count > (Long.MAX_VALUE - 10)) {
            stopCount.set(1L);
        }
        try {
            this.stopLoop();
        } catch (Exception e) {
            log.warn("error = {}", e.getMessage(), e);
        }
    }

    private void threadLoop() throws Exception {
        int max = pollNum;
        while (max > 0) {
            CloudChargeVo pl = chargerOrderBizService.startPoll(50, TimeUnit.MILLISECONDS);
            if (pl == null) {
                return;
            }
            this.startCharge(pl);
            max--;
        }
    }

    private void startCharge(CloudChargeVo vo) {
        Boolean boo = false;
        String failReason = "";
        try {
            CreateChargeOrderParam param = this.buildCreateOrderParam(vo);
            //STEP 1. 调用trading，发起充电
            BaseResponse res = tdChargeFeignClient.createCharge(param)
                .block(Duration.ofSeconds(50L));
//            BaseResponse res = chargerOrderFeignClient.createChargeOrder(param);
//            ObjectResponse res = orderFeignClient.startCharger(vo);
            FeignResponseValidate.check(res);
            boo = true;
        } catch (Exception e) {
            boo = false;
            failReason = e.getMessage();
            log.warn("平台开启充电失败 plugNo: {} ", vo.getPlugNo(), e);
        } finally {
            //STEP 2.下发状态或失败原因回写
            if (vo.getStartType() == OrderStartType.MGM_WEB_MANUAL) {
                ChargeOrderReserve reserve = new ChargeOrderReserve();
                reserve.setId(vo.getChargeOrderReserveId());
                reserve.setStatus(boo ? 2 : 3);
                chargeOrderReserveDs.updateStatus(reserve);
            } else if (!boo && vo.getStartType() == OrderStartType.SCHEDULE_JOB) {
                // 通过plugNo,从redis中读取logId,进行回写 failReason
                String jobLogId = redisUtil.get(
                    PlugNoUtils.PLATFORM_SCHEDULE_JOB_LOG_ID + vo.getPlugNo());
                if (StringUtils.isNotBlank(jobLogId)) {
                    SiteChargeJobLogPo po = new SiteChargeJobLogPo();
                    po.setId(Long.valueOf(jobLogId))
                        .setResult(1)
                        .setFailReason(failReason);
                    siteChargeJobLogRwDs.writeBack(po);
                    redisUtil.del(PlugNoUtils.PLATFORM_SCHEDULE_JOB_LOG_ID + vo.getPlugNo());
                }
            }
        }
    }

    private CreateChargeOrderParam buildCreateOrderParam(CloudChargeVo vo) {
        CreateChargeOrderParam param = new CreateChargeOrderParam();
        param.setPlugNo(vo.getPlugNo())
            .setCusId(vo.getOrderCustomerId())
            .setCusName(vo.getOrderCustomerName())
            .setCusPhone(vo.getOrderMobilePhone())
            .setCommercialId(vo.getSiteTopCommId())
            .setPayAccountType(vo.getPayType())
            .setBalanceId(vo.getOrderPayAccountId())
            .setOrderType(vo.getStartType())
            .setAppClientType(AppClientType.MGM_WEB) // TODO 这里要细化
            .setChannelId(OrderChannelType.MGM_WEB)
            .setStopSoc(vo.getStopSoc());

        return param;
    }

    private void stopLoop() throws Exception {
        int max = pollNum;
        while (max > 0) {
            StopChargerRequest pl = chargerOrderBizService.stopPoll(50, TimeUnit.MILLISECONDS);
//            log.info("pl: {}", JsonUtils.toJsonString(pl));
            if (pl == null) {
                return;
            }
            this.stopCharge(pl);
            max--;
        }
    }

    private void stopCharge(StopChargerRequest vo) {
        //调用trading，发起充电
        log.info("停止充电. seq = {}, plugNo = {}, orderNo = {}", vo.getSeqNo(), vo.getPlugNo(),
            vo.getOrderNo());
        tdChargeFeignClient.stopCharge(vo.getOrderNo(), AppClientType.MGM_WEB)
            .subscribe();
//        orderFeignClient.stopCharger(vo.getPlugNo(), vo.getOrderNo(), AppClientType.MGM_WEB);
    }
}
