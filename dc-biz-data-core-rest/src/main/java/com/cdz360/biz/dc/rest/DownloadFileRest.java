package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.DownloadFileService;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.param.GenerateResult;
import com.cdz360.biz.model.download.param.ListDownloadJobParam;
import com.cdz360.biz.model.download.vo.DownloadJobVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

@Tag(name = "下载文件相关接口", description = "下载文件相关接口")
@Slf4j
@RestController
public class DownloadFileRest {

    @Autowired
    private DownloadFileService downloadFileService;

    @Operation(summary = "下载任务列表")
    @PostMapping(value = "/dataCore/download/applyList")
    public Mono<ListResponse<DownloadJobVo>> downloadApplyList(
        @RequestBody ListDownloadJobParam param) {
        log.info("下载任务列表: param = {}", param);
        return downloadFileService.downloadApplyList(param);
    }

    @Operation(summary = "申请下载")
    @PostMapping(value = "/dataCore/download/apply")
    public Mono<ObjectResponse<ExcelPosition>> downloadFileApply(
        @RequestBody DownloadApplyParam param) {
        log.info("申请下载: param = {}", param);
        return downloadFileService.downloadFileApply(param)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "申请打印")
    @PostMapping(value = "/dataCore/download/printApply")
    public Mono<ObjectResponse<ExcelPosition>> downloadFilePrintApply(
            @RequestBody DownloadApplyParam param) {
        log.info("申请下载: param = {}", param);
        return downloadFileService.downloadFilePrintApply(param)
                .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "触发下载")
    @GetMapping(value = "/dataCore/download/executeJob")
    public Mono<BaseResponse> executeDownloadJob() {
        return downloadFileService.executeDownloadJob();
    }

    @Operation(summary = "生成文件清理")
    @GetMapping(value = "/dataCore/download/clearFile")
    public Mono<BaseResponse> clearDownloadFile() {
        return downloadFileService.clearDownloadFile();
    }

    @Operation(summary = "取消指定下载任务")
    @GetMapping(value = "/dataCore/download/cancelJob")
    public Mono<ObjectResponse<DownloadJobVo>> cancelDownloadJob(
        @Parameter(name = "下载任务记录ID") @RequestParam Long id) {
        log.info("取消指定下载任务: id = {}", id);
        return downloadFileService.cancelDownloadJob(id)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "下载文件生成反馈")
    @PostMapping(value = "/dataCore/download/notifyResult")
    public Mono<BaseResponse> notifyGenerateResult(@RequestBody GenerateResult result) {
        log.info("下载文件生成反馈: {}", result);
        return downloadFileService.notifyGenerateResult(result);
    }
}
