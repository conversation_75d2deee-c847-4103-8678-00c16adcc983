package com.cdz360.biz.dc.service;

import com.cdz360.biz.ds.trading.ro.site.ds.CommRoDs;
import com.cdz360.biz.model.trading.site.po.CommPo;
import com.chargerlinkcar.framework.common.domain.MemCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 商户引射表
 *
 * <AUTHOR>
 * @since 2019/11/14 13:29
 */
@Slf4j
@Service
public class TRCommercialService {

    // 缓存
    private static final Integer TTL = 10 * 60; // 缓存有效期10分钟
    Map<Long, MemCache<CommPo>> caches = new ConcurrentHashMap<>();
    @Autowired
//    private TRCommercialRoDs trCommercialRoDs;
    private CommRoDs commRoDs;

    /**
     * 通过商户Id获取商户信息
     *
     * @param commId
     * @return
     */
    public CommPo getCommercialById(Long commId) {
        MemCache<CommPo> cache = caches.get(commId);
        if (cache == null) {
            // 缓存未命中, 查询数据库
            cache = buildCache(commId);
        } else if (cache.getExpire().before(new Date())) {
            // 缓存数据已过期, 重新查询数据库
            cache = buildCache(commId);
        }
        return cache != null ? cache.getData() : null;
    }

    private MemCache buildCache(long commId) {
        CommPo commercial = this.commRoDs.getCommById(commId);
        if (commercial == null) {
            return null;
        }

        MemCache<CommPo> cache = new MemCache<>(commercial, TTL);
        caches.put(commercial.getId(), cache);
        return cache;
    }

//    public ListResponse<TRCommercialPo> listCommercial(ListCommercialParam param) {
//        return trCommercialRoDs.listCommercial(param);
//    }

//    public Long countCommercial(ListCommercialParam param) {
//        return trCommercialRoDs.countCommercial(param);
//    }
}
