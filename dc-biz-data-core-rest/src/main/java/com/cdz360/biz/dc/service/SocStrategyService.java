package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.dc.client.UserFeignClient;
import com.cdz360.biz.ds.trading.ro.soc.ds.SocStrategyRoDs;
import com.cdz360.biz.ds.trading.ro.soc.ds.UserSocStrategyRoDs;
import com.cdz360.biz.ds.trading.ro.soc.ds.UserSocTimeRoDs;
import com.cdz360.biz.ds.trading.rw.soc.ds.UserSocStrategyRwDs;
import com.cdz360.biz.model.trading.soc.param.QueryStrategyParam;
import com.cdz360.biz.model.trading.soc.param.SocStrategyDict;
import com.cdz360.biz.model.trading.soc.po.SocStrategyPo;
import com.cdz360.biz.model.trading.soc.po.UserSocStrategyPo;
import com.cdz360.biz.model.trading.soc.po.UserSocTimePo;
import com.cdz360.biz.model.trading.soc.vo.UserSocStrategyCreditCusVo;
import com.cdz360.biz.model.trading.soc.vo.UserSocStrategyVinVo;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.domain.vo.VinDto;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * SocStrategyService
 *
 * @since 12/30/2020 2:34 PM
 * <AUTHOR>
 */
@Service
public class SocStrategyService {

    @Autowired
    private SocStrategyRoDs socStrategyRoDs;

    @Autowired
    private UserSocTimeRoDs userSocTimeRoDs;

    @Autowired
    private UserSocStrategyRoDs userSocStrategyRoDs;

    @Autowired
    private UserSocStrategyRwDs userSocStrategyRwDs;

    @Autowired
    private UserFeignClient userFeignClient;

    public List<SocStrategyDict> queryStrategy(QueryStrategyParam param) {
        List<SocStrategyPo> socStrategyPos = socStrategyRoDs.queryStrategy(param);

        if(CollectionUtils.isEmpty(socStrategyPos)) {
            return List.of();
        }


        Map<Long, Pair<List<Long>, List<Long>>> keyIds = new HashMap<>();// 策略id和对应vin、授信id数组

        List<SocStrategyDict> allStrategy = socStrategyPos.stream().map(e -> {
            List<UserSocTimePo> byStrategyId = userSocTimeRoDs.getByStrategyId(e.getId());
            SocStrategyDict ret = new SocStrategyDict();
            ret.setSocStrategy(e).setTimeList(byStrategyId);

            // 去除限制对象，授信账户、VIN码
            List<UserSocStrategyPo> limitObjects = userSocStrategyRoDs.getByStrategyId(e.getId());
            if(CollectionUtils.isNotEmpty(limitObjects)) {
                // vin 为空串，则是授信账户
                ret.setCreditCusCount(limitObjects.stream().filter(obj -> obj.getVinId() == 0L).count());
                ret.setVinCount(limitObjects.size() - ret.getCreditCusCount());

                var p = Pair.of(limitObjects.stream()
                                .filter(obj -> obj.getUserId() == 0L && obj.getVinId() != 0L)
                                .map(UserSocStrategyPo::getVinId)
                                .collect(Collectors.toList()),
                        limitObjects.stream()
                                .filter(obj -> obj.getUserId() != 0L && obj.getVinId() == 0L)
                                .map(UserSocStrategyPo::getVinId)
                                .collect(Collectors.toList()));

                keyIds.put(e.getId(), p);
            }

            return ret;
        }).collect(Collectors.toList());












        return allStrategy;
    }

    public List<UserSocStrategyCreditCusVo> queryCorpStrategyCreditCus(QueryStrategyParam param) {
        IotAssert.isNotNull(param.getStrategyId(), "请传入策略id");

        List<UserSocStrategyPo> byStrategyId = userSocStrategyRoDs.getByStrategyId(param.getStrategyId());
        if(CollectionUtils.isEmpty(byStrategyId)) {
            return List.of();
        }

        // 过滤，以便获得授信记录
        List<UserSocStrategyPo> collect = byStrategyId.stream()
                .filter(e -> e.getVinId() == 0L && e.getUserId() != 0L)
                .collect(Collectors.toList());

        List<Long> ids = collect.stream().map(UserSocStrategyPo::getUserId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(ids)) {
            return List.of();
        }

        ListResponse<RBlocUser> rBlocUserListResponse = userFeignClient.selectRBlocUserIds(ids);
        FeignResponseValidate.check(rBlocUserListResponse);

        Map<Long, Long> idMap = new HashMap<>();
        collect.forEach(e -> {
            if(idMap.get(e.getUserId()) == null) {
                idMap.put(e.getUserId(), e.getId());
            }
        });

        var userList = rBlocUserListResponse.getData().stream().map(e -> {
            UserSocStrategyCreditCusVo ret = new UserSocStrategyCreditCusVo();
            ret.setName(e.getName())
                    .setPhone(e.getPhone())
                    .setOrgName(e.getOrgName())

                    .setUserId(e.getId())
                    .setId(idMap.get(e.getId()))
                    .setStrategyId(param.getStrategyId());
            return ret;
        }).collect(Collectors.toList());

        return userList;
    }

    public List<UserSocStrategyVinVo> queryCorpStrategyVin(QueryStrategyParam param) {
        IotAssert.isNotNull(param.getStrategyId(), "请传入策略id");

        List<UserSocStrategyPo> byStrategyId = userSocStrategyRoDs.getByStrategyId(param.getStrategyId());
        if(CollectionUtils.isEmpty(byStrategyId)) {
            return List.of();
        }

        // 过滤，以便获得授信记录
        List<UserSocStrategyPo> collect = byStrategyId.stream()
                .filter(e -> e.getVinId() != 0L && e.getUserId() == 0L)
                .collect(Collectors.toList());


        if(CollectionUtils.isEmpty(collect)) {
            return List.of();
        }

        ListResponse<VinDto> byIdList = userFeignClient.getByIdList(collect.stream()
                .map(UserSocStrategyPo::getVinId)
                .collect(Collectors.toList()));
        FeignResponseValidate.check(byIdList);

        Map<Long, Long> idMap = new HashMap<>();
        collect.forEach(e -> {
            if(idMap.get(e.getVinId()) == null) {
                idMap.put(e.getVinId(), e.getId());
            }
        });
        var vinList = byIdList.getData().stream().map(e -> {
            UserSocStrategyVinVo ret = new UserSocStrategyVinVo();
            ret.setCarDepart(e.getCarDepart())
                    .setPhone(e.getMobile())
                    .setVin(e.getVin())
                    .setCarNo(e.getCarNo())
                    .setCarNum(e.getCarNum())
                    .setLineNum(e.getLineNum())
                    .setVinId(e.getId())
                    .setId(idMap.get(e.getId()))
                    .setStrategyId(param.getStrategyId());
            return ret;
        }).collect(Collectors.toList());

        return vinList;
    }

    public Integer addCorpStrategyCreditCus(List<QueryStrategyParam> params) {

        if(CollectionUtils.isEmpty(params) ||
                CollectionUtils.isEmpty(params.stream().filter(Objects::nonNull).collect(Collectors.toList()))) {
            return 0;
        }

        //TODO 禁止已经存在的用户再次添加

        List<UserSocStrategyPo> collect = params.stream().map(e -> {
            UserSocStrategyPo ret = new UserSocStrategyPo();
            if (e.getVinId() == null) {
                ret.setVinId(0L);
            } else {
                ret.setVinId(e.getVinId());
            }

            if (e.getUserId() == null) {
                ret.setUserId(0L);
            } else {
                ret.setUserId(e.getUserId());
            }

            ret.setStrategyId(e.getStrategyId());

            return ret;
        }).collect(Collectors.toList());

        return userSocStrategyRwDs.batchInsert(collect);
    }

    public Integer removeCorpStrategyCreditCus(List<QueryStrategyParam> params) {
        if(CollectionUtils.isEmpty(params) ||
                CollectionUtils.isEmpty(params.stream().filter(Objects::nonNull).collect(Collectors.toList()))) {
            return 0;
        }

        List<Long> ids = params.stream().map(QueryStrategyParam::getId).collect(Collectors.toList());

        return userSocStrategyRwDs.deleteByIdList(ids);
    }

    public Integer addCorpStrategyVin(List<QueryStrategyParam> params) {

        return this.addCorpStrategyCreditCus(params);

    }
}