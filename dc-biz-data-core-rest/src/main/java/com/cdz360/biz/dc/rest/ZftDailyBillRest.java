package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.ZftDailyBillService;
import com.cdz360.biz.model.trading.bill.param.ListZftDailyBillParam;
import com.cdz360.biz.model.trading.bill.param.NotifyDailyBillParam;
import com.cdz360.biz.model.trading.bill.vo.ZftDailyBillVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@Tag(name = "支付平台账单相关接口", description = "支付平台账单相关接口")
@RestController
public class ZftDailyBillRest {

    @Autowired
    private ZftDailyBillService zftDailyBillService;

    @Operation(summary = "获取支付平台账单记录")
    @PostMapping(value = "/api/dataCore/findAllZftDailyBill")
    public Mono<ListResponse<ZftDailyBillVo>> findAllZftDailyBill(
        ServerHttpRequest request,
        @RequestBody ListZftDailyBillParam param) {
        log.debug("获取支付平台账单记录: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return zftDailyBillService.findAllZftDailyBill(param);
    }

    @Operation(summary = "对账重试", description = "失败后可以重试")
    @GetMapping(value = "/api/dataCore/retryCheckBill")
    public Mono<BaseResponse> retryCheckBill(
        ServerHttpRequest request,
        @Parameter(name = "顶级商户ID") @RequestParam(value = "topCommId") Long topCommId,
        @Parameter(name = "对账单ID") @RequestParam(value = "dailyBillId") Long dailyBillId) {
        log.debug("对账重试: {}", LoggerHelper2.formatEnterLog(request));
        return zftDailyBillService.retryCheckBill(topCommId, dailyBillId);
    }

    @Operation(summary = "定时下载账单", description = "异步处理")
    @GetMapping(value = "/api/dataCore/downloadZftDailyBill")
    public Mono<BaseResponse> downloadZftDailyBill(ServerHttpRequest request) {
        String uuid = UUID.randomUUID().toString();
        log.info("定时下载账单: {}", uuid);
        this.zftDailyBillService.downloadZftDailyBill(uuid);
        return Mono.just(RestUtils.success());
    }

    @Operation(summary = "触发对账流程", description = "PCP服务通知, 异步处理")
    @PostMapping(value = "/api/dataCore/notifyDailyBill")
    public Mono<BaseResponse> notifyDailyBill(@RequestBody NotifyDailyBillParam param) {
        String uuid = UUID.randomUUID().toString();
        log.info("触发对账流程: {}", uuid);
        this.zftDailyBillService.notifyDailyBill(uuid, param);
        return Mono.just(RestUtils.success());
    }
}
