package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.client.UserFeignClient;
import com.cdz360.biz.dc.domain.EvseCfgResendDto;
import com.cdz360.biz.ds.trading.ro.iot.ds.BsBoxRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.BsBoxSettingRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.CommRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.PriceItemRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.PriceSchemaRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteDefaultSettingRwDs;
import com.cdz360.biz.model.common.constant.ExcelCheckResult;
import com.cdz360.biz.model.finance.type.BizType;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.iot.param.ModifyEvseCfgParam;
import com.cdz360.biz.model.iot.param.OfflineEvseParam;
import com.cdz360.biz.model.iot.vo.ChargeV2;
import com.cdz360.biz.model.iot.vo.EvseImportVo;
import com.cdz360.biz.model.iot.vo.EvseModelVo;
import com.cdz360.biz.model.iot.vo.OfflineEvseImportVo;
import com.cdz360.biz.model.iot.vo.OfflineEvseVo;
import com.cdz360.biz.model.iot.vo.WhiteCardCfgVo;
import com.cdz360.biz.model.merchant.dto.SiteTinyDto;
import com.cdz360.biz.model.merchant.param.ListCommercialParam;
import com.cdz360.biz.model.site.po.PriceItemPo;
import com.cdz360.biz.model.site.po.PriceTemplatePo;
import com.cdz360.biz.model.trading.evse.EvseInfo;
import com.cdz360.biz.model.trading.iot.po.BsBoxPo;
import com.cdz360.biz.model.trading.iot.vo.EvseInfoVo;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.po.BsBoxSettingPo;
import com.cdz360.biz.model.trading.site.po.CommPo;
import com.cdz360.biz.model.trading.site.po.SiteDefaultSettingPo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.service.excel.BufferedExcelConvertor;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.NumberUtil;
import com.chargerlinkcar.framework.common.utils.RegularExpressionUtil;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EvseManagerService {

    @Autowired
    private BsBoxRoDs bsBoxRoDs;
    @Autowired
    private SiteRoDs siteRoDs;
    @Autowired
    private PriceSchemaRoDs priceSchemaRoDs;
    @Autowired
    private PriceItemRoDs priceItemRoDs;
    @Autowired
    private BsBoxSettingRoDs bsBoxSettingRoDs;
    @Autowired
    private SiteDefaultSettingRwDs siteDefaultSettingRwDs;
    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private DeviceBizService deviceBizService;
    @Autowired
    private PriceSchemaBizService priceSchemaBizService;
    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;
    @Autowired
//    private TRCommercialRoDs trCommercialRoDs;
    private CommRoDs commRoDs;
    @Autowired
    private RedisIotReadService redisIotReadService;
    @Autowired
    private BufferedExcelConvertor excelConvertor;


    public ObjectResponse<EvseInfo> getEvseInfo(String evseNo) {
        List<BsBoxSettingPo> settingPos = bsBoxSettingRoDs.getByEvseNo(List.of(evseNo), null, false);
        IotAssert.isTrue(CollectionUtils.isNotEmpty(settingPos), "桩不存在");
        BsBoxSettingPo settingPo = settingPos.get(0);
        BsBoxPo boxPo = bsBoxRoDs.getBsBox(evseNo);
        SitePo sitePo = siteRoDs.getSite(boxPo.getSiteId());
        EvseInfo res = new EvseInfo();
        if (settingPo.getChargeId() != null) {
            PriceTemplatePo priceTemplatePo = priceSchemaRoDs.findById(settingPo.getChargeId(), false);
            List<PriceItemPo> itemPos = priceItemRoDs.getPriceItemListByTempId(settingPo.getChargeId());
            res.setTemplateCode(priceTemplatePo.getCode())
                    .setTemplateName(priceTemplatePo.getName())
                    .setTemplateList(itemPos.stream().map(e -> {
                        ChargeV2 v2 = new ChargeV2();
                        v2.setCode(e.getCategory().getCode())
                                .setCategory(e.getCategory())
                                .setStartTime(DateUtil.intToTime(e.getStartTime()))
                                .setStopTime(DateUtil.intToTime(e.getStopTime()))
                                .setElecPrice(e.getPrice())
                                .setServPrice(e.getServicePrice());
                        return v2;
                    }).collect(Collectors.toList()));
        }
        res.setSiteId(sitePo.getId())
                .setSiteName(sitePo.getSiteName())
                .setEvseName(boxPo.getEvseName());

        EvseVo evseVo = redisIotReadService.getEvseRedisCache(evseNo);
        if (evseVo != null) {
            res.setProtocolVer(evseVo.getProtocolVer());
        }

        res.setDayVolume(settingPo.getDayVolume())
                .setNightVolume(settingPo.getNightVolume())
                .setQrUrl(settingPo.getUrl())
                .setTimedCharge(settingPo.getIsTimedCharge())
                .setQueryChargeRecord(settingPo.getIsQueryChargeRecord())
                .setNoCardCharge(settingPo.getIsNoCardCharge())
                .setQrCharge(settingPo.getIsScanCharge())
                .setVinCharge(settingPo.getIsVinCharge())
                .setCardCharge(settingPo.getIsCardCharge())
                .setAmount(settingPo.getIsQuotaMoneyCharge())
                .setKwh(settingPo.getIsQuotaEleCharge())
                .setTime(settingPo.getIsQuotaTimeCharge())
                .setInternationalAgreement(settingPo.getInternationalAgreement())
                .setIsAutoStopCharge(settingPo.getIsAutoStopCharge())
                .setAvgOrTurnCharge(settingPo.getAvgOrTurnCharge())
                .setIsCombineCharge(settingPo.getIsCombineCharge())
                .setHeating(settingPo.getHeating())
                .setHeatingVoltage(settingPo.getHeatingVoltage())
                .setBatteryCheck(settingPo.getBatteryCheck())
                .setSecurityCheck(settingPo.getSecurityCheck())
                .setConstantCharge(settingPo.getConstantCharge())
                .setVinDiscover(settingPo.getVinDiscover())
                .setOrderPrivacySetting(settingPo.getOrderPrivacySetting())
                .setAccountDisplayType(settingPo.getAccountDisplayType())
        ;

        ListResponse<WhiteCardCfgVo> whiteCardV2ListResponse = userFeignClient.getWhiteCardCfgVo(boxPo.getSiteId());
        FeignResponseValidate.check(whiteCardV2ListResponse);
        res.setWhiteCards(whiteCardV2ListResponse.getData());

        return RestUtils.buildObjectResponse(res);
    }


    public BaseResponse downDefultSetting2AllEvse(String siteId) {
        BsBoxPo req = new BsBoxPo();
        req.setSiteId(siteId);
        List<BsBoxPo> list = bsBoxRoDs.findByCondition(req, null);

        SiteDefaultSettingPo po = siteDefaultSettingRwDs.getBySiteId(siteId);
        if (null == po) {
            log.info("该场站没有默认配置信息");
            throw new DcArgumentException("场站Id无效, 没有默认配置信息");
        }

        ModifyEvseCfgParam param = new ModifyEvseCfgParam();
        BeanUtils.copyProperties(po, param);
        param.setQrUrl(po.getUrl());
        param.setEvseNoList(list.stream().map(BsBoxPo::getEvseNo).collect(Collectors.toList()));

        deviceBizService.downSetting2Evse(param);
        return RestUtils.success();
    }

    public BaseResponse downSettingByEvse(String evseNo) {
        BsBoxPo po = bsBoxRoDs.getBsBox(evseNo);
        if (null == po) {
            log.info("未找到桩信息 evseNo: {}", evseNo);
            throw new DcArgumentException("未找到桩信息");
        }

        deviceBizService.downSetting2Evse(evseNo, po.getSiteId());
        return RestUtils.success();
    }

    public BaseResponse downLastSettingByEvse(String evseNo) {
        BsBoxPo po = bsBoxRoDs.getBsBox(evseNo);
        if (null == po) {
            log.info("未找到桩信息 evseNo: {}", evseNo);
            throw new DcArgumentException("未找到桩信息");
        }

        List<BsBoxSettingPo> settingPos = bsBoxSettingRoDs.getByEvseNo(List.of(evseNo), null,
            false);
        IotAssert.isTrue(CollectionUtils.isNotEmpty(settingPos), "桩无配置信息");
        BsBoxSettingPo settingPo = settingPos.get(0);

        // 配置下发
        ModifyEvseCfgParam param = new ModifyEvseCfgParam();
        BeanUtils.copyProperties(settingPo, param);
        param.setQrUrl(settingPo.getUrl());
        param.setEvseNoList(Collections.singletonList(evseNo));

        deviceBizService.downSetting2Evse(param);
        return RestUtils.success();
    }

    public ObjectResponse<ModifyEvseCfgParam> getModifyEvseCfgParam(String siteId) {

        BsBoxPo req = new BsBoxPo();
        req.setSiteId(siteId);
        List<BsBoxPo> list = bsBoxRoDs.findByCondition(req, null);

        SiteDefaultSettingPo settingPo = siteDefaultSettingRwDs.getBySiteId(siteId);
        if (null == settingPo) {
            log.info("该场站没有默认配置信息");
            throw new DcArgumentException("场站Id无效, 没有默认配置信息");
        }

        ModifyEvseCfgParam param = new ModifyEvseCfgParam();
        param.setEvseNoList(list.stream().map(BsBoxPo::getEvseNo).collect(Collectors.toList()));
        param.setPriceSchemeId(settingPo.getChargeId());

        SitePo sitePo = siteRoDs.getSite(siteId);
        param.setSiteName(sitePo.getSiteName());

        PriceTemplatePo priceTemplatePo = priceSchemaRoDs.findById(settingPo.getChargeId(), false);
        param.setName(priceTemplatePo.getName());

        return RestUtils.buildObjectResponse(param);
    }

    public ObjectResponse<ModifyEvseCfgParam> getResendEvseCfgParam(EvseCfgResendDto dto) {
        IotAssert.isNotNull(dto.getEvseNo(), "入参缺失");
        String evseNo = dto.getEvseNo();

        ModifyEvseCfgParam param = new ModifyEvseCfgParam();
        param.setEvseNoList(List.of(evseNo));
        if (!dto.isFetchPirce() && !dto.isFetchConfig()) {
            return RestUtils.buildObjectResponse(param);
        }

        SiteDefaultSettingPo settingPo = siteDefaultSettingRwDs.selectByEvseNo(evseNo);

        if (dto.isFetchConfig()) {
            if (!NumberUtils.gtZero(dto.getIotConfigId())) {
                // 使用场站默认配置
                IotAssert.isNotNull(settingPo, "桩对应场站没有默认配置信息");
                BeanUtils.copyProperties(settingPo, param);
            }
        }

        if (dto.isFetchPirce()) {
            AtomicReference<Long> priceId = new AtomicReference<>(null);
            if (NumberUtils.gtZero(dto.getPriceId())) {
                priceId.set(dto.getPriceId());
            } else {
                // 使用场站默认计费
                Long priceCode = priceSchemaBizService.getDefaultPriceCodeByEvseNo(evseNo,
                    settingPo.getSiteId());
                Optional.ofNullable(priceCode)
                    .filter(NumberUtils::gtZero)
                    .ifPresentOrElse(priceId::set, () -> {
                        throw new DcServiceException("桩对应场站没有默认配置信息");
                    });
            }
            param.setPriceSchemeId(priceId.get());
            param.setPriceSchemeList(priceSchemaBizService.getChargeV2List(List.of(priceId.get())));
        }

        return RestUtils.buildObjectResponse(param);
    }

    public ObjectResponse<OfflineEvseImportVo> parseOfflineEvseExcel(List<List<String>> list) {
        log.info(">> 解析支撑平台脱机桩 excel文件 ");
        try {
            List<OfflineEvseVo> vinList = new ArrayList<>(); //excel导入的VIN集合
            List<OfflineEvseVo> valid = new ArrayList<>(); // 有效
            List<OfflineEvseVo> invalid = new ArrayList<>(); // 无效
            log.info("从 excel 中获取内容: {}", list);

            list.forEach(card -> {
                OfflineEvseVo vo = new OfflineEvseVo();
                vo.setEvseNo(null == card.get(0) || card.get(0) == "" ? null : card.get(0).trim().toUpperCase()); // 电桩编号
                vo.setName(null == card.get(1) || card.get(1) == "" ? null : card.get(1).trim());    // 电桩名称
                vo.setSiteName(null == card.get(2) || card.get(2) == "" ? null : card.get(2).trim());    // 所属站点名称
                vo.setModel(null == card.get(3) || card.get(3) == "" ? null : card.get(3).trim());    // 电桩型号
                vo.setSupplyType(null == card.get(4) || card.get(4) == "" ? null : card.get(4).trim()); // 电流类型
                vo.setPower(NumberUtil.parseIntegerIgnoreDecimal(card.get(5))); // 额定功率
                vo.setFirmwareVer(null == card.get(6) || card.get(6) == "" ? null : card.get(6).trim()); // 软件版本
                vo.setProtocolVer(NumberUtil.parseIntegerIgnoreDecimal(card.get(7)));// 桩协议版本
                vo.setIccid(null == card.get(8) || card.get(8) == "" ? null : card.get(8).trim());    // SIM卡号


                ExcelCheckResult checkResult = checkOfflineEvseFormat(vo);
                if (checkResult.getCode() != ExcelCheckResult.EVSE_OFFLINE_VALID.getCode()) {
                    vo.setDetail(checkResult.getDesc());
                    invalid.add(vo);
                } else {
                    valid.add(vo);
                }
                vinList.add(vo);
            });
            HashSet<String> vin = new HashSet<>();
            HashSet<String> vinLst = new HashSet<>();
            OfflineEvseImportVo importVo = checkOfflineEvseInExcel(vin, valid, invalid);
            log.info("valid.size = {}, invalid.size = {}", importVo.getValid().size(), importVo.getInvalid().size());
            //相同数据第一次add的时候是能插入HashSet的，需将这部分数据也标记为重复数据
            importVo.getInvalid().forEach(evseVo -> {
                vinLst.add(evseVo.getEvseNo());
            });
            importVo = checkOfflineEvseInExcel(vinLst, importVo.getValid(), invalid);
            log.info("valid.size = {}, invalid.size = {}", importVo.getValid().size(), importVo.getInvalid().size());
            importVo = checkOfflineEvseInDatebase(importVo.getValid(), invalid);
            log.info("valid.size = {}, invalid.size = {}", importVo.getValid().size(), importVo.getInvalid().size());
            return RestUtils.buildObjectResponse(importVo);
        } catch (DcServiceException e) {
            throw new DcServiceException(e.getMessage());
        } catch (Exception e) {
            log.warn("<< 解析excel文件异常. message = {}", e.getMessage(), e);
            throw new DcServiceException("解析excel文件异常，请求确认文件内容.");
        }

    }

    public Mono<ObjectResponse<EvseImportVo>> parseEvseExcel(FilePart file) {
        log.info(">> 解析桩批量导入excel文件 ");
        try {
            File f = new File("/tmp/" + file.filename());
            return file.transferTo(f)
                    .then(Mono.just("文件上传成功"))
                    .map(a -> {
                        try (InputStream inputStream = new FileInputStream(f)) {
                            EvseImportVo importVo = excelConvertor.convert(
                                    ExcelCheckResult.EVSE_VALID, inputStream,
                                    file.filename());
                            return RestUtils.buildObjectResponse(importVo);
                        } catch (IOException e) {
                            log.warn("<< 解析excel文件异常. message = {}", e.getMessage(), e);
                            throw new DcServiceException("解析excel文件异常，请求确认文件内容.");
                        }
//            List<EvseModelVo> evseList = new ArrayList<>(); //excel导入的集合
//            List<EvseModelVo> valid = new ArrayList<>(); // 有效
//            List<EvseModelVo> invalid = new ArrayList<>(); // 无效
//            log.info("从 excel 中获取内容: {}", list);
//
//            list.forEach(evse -> {
//                EvseModelVo vo = new EvseModelVo();
//                vo.setEvseId(null == evse.get(0) || evse.get(0) == "" ? null : evse.get(0).trim()); // 电桩编号
//                vo.setName(null == evse.get(1) || evse.get(1) == "" ? null : evse.get(1).trim());   // 电桩名称
//                vo.setModel(null == evse.get(2) || evse.get(2) == "" ? null : evse.get(2).trim());  // 电桩型号
//                vo.setBrand(null == evse.get(3) || evse.get(3) == "" ? null : evse.get(3).trim());  // 品牌
//                vo.setUseSiteSettingStr(null == evse.get(4) || evse.get(4) == "" ? null : evse.get(4).trim()); // 是否使用场站通用配置
//                vo.setPhysicalNo(null == evse.get(5) || evse.get(5) == "" ? null : evse.get(5).trim()); // 出厂编号
//                vo.setSimIccid(null == evse.get(6) || evse.get(6) == "" ? null : evse.get(6).trim()); // ICCID
//                vo.setSimMsisdn(null == evse.get(7) || evse.get(7) == "" ? null : evse.get(7).trim());  // MSISDN
//
//
//                ExcelCheckResult checkResult = checkEvseFormat(vo);
//                if (checkResult.getCode() != ExcelCheckResult.EVSE_VALID.getCode()) {
//                    vo.setDetail(checkResult.getDesc());
//                    invalid.add(vo);
//                } else {
//                    valid.add(vo);
//                }
//                vo.fillUseSiteSetting(vo.getUseSiteSettingStr());
//                evseList.add(vo);
//            });
//            HashSet<String> vin = new HashSet<>();
//            HashSet<String> vinLst = new HashSet<>();
//            EvseImportVo importVo = checkEvseInExcel(vin, valid, invalid);
//            log.info("valid.size = {}, invalid.size = {}", importVo.getValid().size(), importVo.getInvalid().size());
//            //相同数据第一次add的时候是能插入HashSet的，需将这部分数据也标记为重复数据
//            importVo.getInvalid().forEach(evseVo -> {
//                vinLst.add(evseVo.getEvseId());
//            });
//            importVo = checkEvseInExcel(vinLst, importVo.getValid(), invalid);
//            log.info("valid.size = {}, invalid.size = {}", importVo.getValid().size(), importVo.getInvalid().size());
//            importVo = checkSimInExcel(importVo.getValid(), invalid);
//            log.info("valid.size = {}, invalid.size = {}", importVo.getValid().size(), importVo.getInvalid().size());
//            importVo = checkEvseInDatebase(importVo.getValid(), invalid);
//            log.info("valid.size = {}, invalid.size = {}", importVo.getValid().size(), importVo.getInvalid().size());
                    });
        } catch (DcServiceException e) {
            throw new DcServiceException(e.getMessage());
        } catch (Exception e) {
            log.warn("<< 解析excel文件异常. message = {}", e.getMessage(), e);
            throw new DcServiceException("解析excel文件异常，请求确认文件内容.");
        }

    }

    private ExcelCheckResult checkOfflineEvseFormat(OfflineEvseVo evse) {

        // 有效性检测
        if (StringUtils.isEmpty(evse.getEvseNo())
                || StringUtils.isEmpty(evse.getSiteName())
                || StringUtils.isEmpty(evse.getSupplyType())) {
            return ExcelCheckResult.EVSE_OFFLINE_INVALID;
        }

        // evseNO规则1~20位数字
        if (!RegularExpressionUtil.digitAll(evse.getEvseNo()) ||
                !(1 <= evse.getEvseNo().length() && evse.getEvseNo().length() <= 20)) {
            return ExcelCheckResult.EVSE_NO_INVALID;
        }

        // evseName: 最大20位
        if (StringUtils.isNotEmpty(evse.getName())) {
            if (!(evse.getName().length() <= 20)) {
                return ExcelCheckResult.EVSE_NAME_INVALID;
            }
        }

        // model: 最大20位
        if (StringUtils.isNotEmpty(evse.getModel())) {
            if (!(evse.getModel().length() <= 20)) {
                return ExcelCheckResult.EVSE_MODLE_INVALID;
            }
        }

        // supplyType: AC or DC
        if (StringUtils.isNotEmpty(evse.getSupplyType())) {
            SupplyType res = null;
            for (SupplyType type : SupplyType.values()) {
                if (type.name().equals(evse.getSupplyType())) {
                    res = type;
                }
            }
            if (res == null) {
                return ExcelCheckResult.EVSE_SUPPLY_INVALID;
            }
        }

        // power: (0~10000]
        if (evse.getPower() != null) {
            if (!(0 < evse.getPower() && evse.getPower() <= 10000)) {
                return ExcelCheckResult.EVSE_POWER_INVALID;
            }
        }

        // firmwareVer 最大20位
        if (StringUtils.isNotEmpty(evse.getFirmwareVer())) {
            if (!(evse.getFirmwareVer().length() <= 20)) {
                return ExcelCheckResult.EVSE_FIRMWAREVER_INVALID;
            }
        }

        // protocolVer 最大20位
//        if (StringUtils.isNotEmpty(evse.getProtocolVer())) {
//            if (!(evse.getProtocolVer().length() <= 20)) {
//                return ExcelCheckResult.EVSE_PROTOCOLVER_INVALID;
//            }
//        }

        // iccid 30位以内数字字母组成
        if (StringUtils.isNotEmpty(evse.getIccid())) {
            if (!RegularExpressionUtil.englishNumberAll(evse.getIccid(), 1, 30)) {
                return ExcelCheckResult.EVSE_ICCID_INVALID;
            }
        }
        return ExcelCheckResult.EVSE_OFFLINE_VALID;
    }

    private ExcelCheckResult checkEvseFormat(EvseModelVo evse) {

        // 有效性检测
        if (StringUtils.isEmpty(evse.getEvseId())
                || StringUtils.isEmpty(evse.getName())
                || StringUtils.isEmpty(evse.getModel())
                || StringUtils.isEmpty(evse.getBrand())
                || StringUtils.isEmpty(evse.getUseSiteSettingStr())) {
            return ExcelCheckResult.EVSE_INVALID;
        }

        // evseNO规则1~20位数字
        if (!RegularExpressionUtil.digitAll(evse.getEvseId()) ||
                !(1 <= evse.getEvseId().length() && evse.getEvseId().length() <= 20)) {
            return ExcelCheckResult.EVSE_NO_INVALID;
        }

        // evseName: 最大20位
        if (StringUtils.isNotEmpty(evse.getName())) {
            if (!(evse.getName().length() <= 20)) {
                return ExcelCheckResult.EVSE_NAME_INVALID;
            }
        }

        // useSiteSetting 必须为 是或否
        if (!evse.getUseSiteSettingStr().equals("是")
                && !evse.getUseSiteSettingStr().equals("否")) {
            return ExcelCheckResult.EVSE_USE_SITE_DEFAULT_INVALID;
        }

        if (StringUtils.isNotBlank(evse.getPhysicalNo())) {
            if (!(evse.getPhysicalNo().length() <= 20)) {
                return ExcelCheckResult.EVSE_PHYSICAL_NO_INVALID;
            }
        }

        // iccid 20位以内数字字母组成
        if (StringUtils.isNotEmpty(evse.getSimIccid())) {
            if (!RegularExpressionUtil.englishNumberAll(evse.getSimIccid(), 1, 20)) {
                return ExcelCheckResult.EVSE_ICCID_INVALID;
            }
        }

        // msisdn 15位以内数字字母组成
        if (StringUtils.isNotEmpty(evse.getSimMsisdn())) {
            if (!RegularExpressionUtil.englishNumberAll(evse.getSimMsisdn(), 1, 15)) {
                return ExcelCheckResult.EVSE_MSISDN_INVALID;
            }
        }
        return ExcelCheckResult.EVSE_VALID;
    }


    /**
     * 校验数据是否重复
     *
     * @param evseList vin的样本数据
     * @param evseList 需要判断是否重复的数据
     * @param invalid  无效的数据
     * @return
     */
    public OfflineEvseImportVo checkOfflineEvseInExcel(HashSet<String> evseSet, List<OfflineEvseVo> evseList, List<OfflineEvseVo> invalid) {
        OfflineEvseImportVo importVo = new OfflineEvseImportVo();
        List<OfflineEvseVo> valid = new ArrayList<>(); // 新的有效数据集合

        evseList.forEach(evse -> {
            ExcelCheckResult checkResult = null;
            if (evseSet.add(evse.getEvseNo())) {
                checkResult = ExcelCheckResult.EVSE_OFFLINE_VALID;
            } else {
                checkResult = ExcelCheckResult.EXCEL_OFFLINE_EXIST;
            }
            evse.setDetail(checkResult.getDesc());
            if (checkResult.getCode() != ExcelCheckResult.EVSE_OFFLINE_VALID.getCode()) {
                invalid.add(evse);
            } else {
                valid.add(evse);
            }
        });
        importVo.setValid(valid);
        importVo.setInvalid(invalid);
        return importVo;
    }

    public EvseImportVo checkEvseInExcel(HashSet<String> evseSet, List<EvseModelVo> evseList, List<EvseModelVo> invalid) {
        EvseImportVo importVo = new EvseImportVo();
        List<EvseModelVo> valid = new ArrayList<>(); // 新的有效数据集合

        evseList.forEach(evse -> {
            ExcelCheckResult checkResult = null;
            if (evseSet.add(evse.getEvseId())) {
                checkResult = ExcelCheckResult.EVSE_VALID;
            } else {
                checkResult = ExcelCheckResult.EXCEL_EXIST;
            }
            evse.setDetail(checkResult.getDesc());
            if (checkResult.getCode() != ExcelCheckResult.EVSE_VALID.getCode()) {
                invalid.add(evse);
            } else {
                valid.add(evse);
            }
        });
        importVo.setValid(valid);
        importVo.setInvalid(invalid);
        return importVo;
    }

    public EvseImportVo checkSimInExcel(List<EvseModelVo> evseList, List<EvseModelVo> invalid) {
        EvseImportVo importVo = new EvseImportVo();
        List<EvseModelVo> valid = new ArrayList<>(); // 新的有效数据集合

        List<String> repeatedIccid = Stream.concat(evseList.stream(), invalid.stream())
                .filter(e -> StringUtils.isNotBlank(e.getSimIccid()))
                .collect(Collectors.groupingBy(EvseModelVo::getSimIccid, Collectors.counting()))
                .entrySet().stream().filter(e -> e.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        List<String> repeatedMsisdn = Stream.concat(evseList.stream(), invalid.stream())
                .filter(e -> StringUtils.isNotBlank(e.getSimMsisdn()))
                .collect(Collectors.groupingBy(EvseModelVo::getSimMsisdn, Collectors.counting()))
                .entrySet().stream().filter(e -> e.getValue() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        evseList.forEach(evse -> {
            ExcelCheckResult checkResult = null;
            if (StringUtils.isNotBlank(evse.getSimIccid())
                    && repeatedIccid.contains(evse.getSimIccid())) {
                checkResult = ExcelCheckResult.ICCID_REPEATED;
            } else if (StringUtils.isNotBlank(evse.getSimMsisdn())
                    && repeatedMsisdn.contains(evse.getSimMsisdn())) {
                checkResult = ExcelCheckResult.MSISDN_REPEATED;
            } else {
                checkResult = ExcelCheckResult.EVSE_VALID;
            }
            evse.setDetail(checkResult.getDesc());
            if (checkResult.getCode() != ExcelCheckResult.EVSE_VALID.getCode()) {
                invalid.add(evse);
            } else {
                valid.add(evse);
            }
        });
        importVo.setValid(valid);
        importVo.setInvalid(invalid);
        return importVo;
    }


    /**
     * 校验数据
     *
     * @param evseList 需要判断的数据
     * @param invalid  无效的数据
     * @return
     */
    public OfflineEvseImportVo checkOfflineEvseInDatebase(List<OfflineEvseVo> evseList, List<OfflineEvseVo> invalid) {
        OfflineEvseImportVo vinMgnListVo = new OfflineEvseImportVo();
        List<OfflineEvseVo> valid = new ArrayList<>(); // 新的有效数据集合

        if (CollectionUtils.isEmpty(evseList)) {
            vinMgnListVo.setValid(valid);
            vinMgnListVo.setInvalid(invalid);
            return vinMgnListVo;
        }

        List<SitePo> sitePoList = siteRoDs.getSiteByNameList(evseList.stream().map(OfflineEvseVo::getSiteName).collect(Collectors.toList()));
        Map<String, SitePo> siteNameMap = sitePoList.stream()
                .collect(Collectors.toMap(SitePo::getSiteName, o -> o));

        List<OfflineEvseParam> params = evseList.stream().map(e -> {
            OfflineEvseParam temp = new OfflineEvseParam();
            temp.setEvseNo(e.getEvseNo());

            SitePo sitePo = siteNameMap.get(e.getSiteName());
            temp.setSiteId(sitePo == null ? "" : sitePo.getId());
            return temp;
        }).collect(Collectors.toList());
        ListResponse<OfflineEvseParam> response = iotDeviceMgmFeignClient.checkOfflineEvseInDB(params);
        FeignResponseValidate.check(response);
        Map<String, Boolean> evseNoSiteIdMap = response.getData().stream()
                .collect(Collectors.toMap(e -> e.getEvseNo().concat(e.getSiteId()), OfflineEvseParam::getIsExist));

        List<Integer> bizTypeList = List.of(BizType.OFFLINE_SELF.getCode(), BizType.OFFLINE_NON_SELF.getCode());

        // 必须是脱机站点，且存在于库中
        // 脱机桩 同一站点下不能重复，全局下可重复
        evseList.forEach(evse -> {
            ExcelCheckResult checkResult = null;
            SitePo sitePo = siteNameMap.get(evse.getSiteName());
            Boolean isExist = evseNoSiteIdMap.get(evse.getEvseNo()
                    .concat(sitePo == null ? "" : sitePo.getId()));

            if (sitePo == null
                    || sitePo.getBizType() == null
                    || !bizTypeList.contains(sitePo.getBizType())) {
                checkResult = ExcelCheckResult.OFFLINE_SITE_NOT_EXIST;
            } else if (Boolean.TRUE.equals(isExist)) {
                checkResult = ExcelCheckResult.OFFLINE_EVSENO_EXIST;
            } else {
                checkResult = ExcelCheckResult.EVSE_OFFLINE_VALID;
                evse.setSiteId(sitePo.getId());
            }
            evse.setDetail(checkResult.getDesc());
            if (checkResult.getCode() != ExcelCheckResult.EVSE_OFFLINE_VALID.getCode()) {
                invalid.add(evse);
            } else {
                valid.add(evse);
            }
        });
        vinMgnListVo.setValid(valid);
        vinMgnListVo.setInvalid(invalid);
        return vinMgnListVo;
    }

    /**
     * 校验数据
     *
     * @param evseList 需要判断的数据
     * @param invalid  无效的数据
     * @return
     */
    public EvseImportVo checkEvseInDatebase(List<EvseModelVo> evseList, List<EvseModelVo> invalid) {
        EvseImportVo vinMgnListVo = new EvseImportVo();
        List<EvseModelVo> valid = new ArrayList<>(); // 新的有效数据集合

        if (CollectionUtils.isEmpty(evseList)) {
            vinMgnListVo.setValid(valid);
            vinMgnListVo.setInvalid(invalid);
            return vinMgnListVo;
        }

        List<OfflineEvseParam> params = evseList.stream().map(e -> {
            OfflineEvseParam temp = new OfflineEvseParam();
            temp.setEvseNo(e.getEvseId());
            temp.setModel(e.getModel());
            temp.setBrand(e.getBrand());
            temp.setIccid(e.getSimIccid());
            temp.setMsisdn(e.getSimMsisdn());
            return temp;
        }).collect(Collectors.toList());
        ListResponse<OfflineEvseParam> response = iotDeviceMgmFeignClient.checkEvseInDB(params);
        FeignResponseValidate.check(response);
        Map<String, OfflineEvseParam> evseNoMap = response.getData().stream()
                .collect(Collectors.toMap(OfflineEvseParam::getEvseNo, o -> o));


        evseList.forEach(evse -> {
            ExcelCheckResult checkResult = null;
            OfflineEvseParam checkRes = evseNoMap.get(evse.getEvseId());

            if (Boolean.TRUE.equals(checkRes.getIsExist())) {
                checkResult = ExcelCheckResult.EXCEL_ALREADY_EXIST;
            } else if (Boolean.FALSE.equals(checkRes.getIsSimExist())) {
                checkResult = ExcelCheckResult.EVSE_SIM_INEXISTENCE;
            } else if (Boolean.TRUE.equals(checkRes.getIsSimBind())) {
                checkResult = ExcelCheckResult.EVSE_SIM_IS_BINDING;
            } else if (Boolean.FALSE.equals(checkRes.getIsModelBrandUniqueKey())) {
                checkResult = ExcelCheckResult.EVSE_MODEL_BRAND_INVALID;
            } else {
                evse.setSimId(checkRes.getSimId());
                evse.setModelId(checkRes.getModelId());
                checkResult = ExcelCheckResult.EVSE_VALID;
            }
            evse.setDetail(checkResult.getDesc());
            if (checkResult.getCode() != ExcelCheckResult.EVSE_VALID.getCode()) {
                invalid.add(evse);
            } else {
                valid.add(evse);
            }
        });
        vinMgnListVo.setValid(valid);
        vinMgnListVo.setInvalid(invalid);
        return vinMgnListVo;
    }


    /**
     * 获取脱机桩列表
     *
     * @param param
     * @return
     */
    public ListResponse<EvseInfoVo> getOfflineEvseList(ListEvseParam param) {
        ListSiteParam listSiteParam = new ListSiteParam();
        listSiteParam.setTopCommId(param.getTopCommId())
                .setSiteCommId(param.getOperateId())
                .setBizTypeList(List.of(BizType.OFFLINE_SELF.getCode(), BizType.OFFLINE_NON_SELF.getCode()))
                .setSiteName(param.getSiteName());
        List<SiteTinyDto> siteListRes = siteRoDs.getSiteTinyList(listSiteParam);
        if (CollectionUtils.isEmpty(siteListRes)) {
            return RestUtils.buildListResponse(null, 0L);
        } else {
            param.setSiteIdList(siteListRes.stream().map(SiteTinyDto::getId)
                    .collect(Collectors.toList()));
        }
        ListResponse<EvseInfoVo> evseRes = iotDeviceMgmFeignClient.getEvseInfoList(param);
        FeignResponseValidate.check(evseRes);

        if (CollectionUtils.isNotEmpty(evseRes.getData())) {
            ListCommercialParam temp = new ListCommercialParam();
            temp.setCommIdList(evseRes.getData().stream().map(EvseVo::getSiteCommId).collect(Collectors.toList()));
            ListResponse<CommPo> response = commRoDs.getCommList2(temp);
            FeignResponseValidate.checkIgnoreData(response);
            Map<Long, CommPo> commMap = response.getData().stream()
                    .collect(Collectors.toMap(CommPo::getId, o -> o));

            evseRes.getData().forEach(e -> {
                CommPo comm = commMap.get(e.getSiteCommId());
                if (comm != null) {
                    e.setSiteCommName(comm.getName());
                    e.setTopCommId(comm.getTopCommId());
                }
            });
        }
        return evseRes;
    }


}
