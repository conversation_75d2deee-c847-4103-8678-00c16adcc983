package com.cdz360.biz.dc.service.site;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.client.UserFeignClient;
import com.cdz360.biz.dc.service.ChargerOrderBizService;
import com.cdz360.biz.dc.service.TRCommercialService;
import com.cdz360.biz.dc.utils.RedisUtil;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteChargeJobLogRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteChargeJobPlugRoDS;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteChargeJobRoDS;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteChargeJobTimeRoDS;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteChargeJobLogRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteChargeJobPlugRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteChargeJobRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteChargeJobTimeRwDs;
import com.cdz360.biz.model.trading.site.param.ChargeJobLogParam;
import com.cdz360.biz.model.trading.site.param.ChargeJobParam;
import com.cdz360.biz.model.trading.site.po.CommPo;
import com.cdz360.biz.model.trading.site.po.JobTacticTime;
import com.cdz360.biz.model.trading.site.po.SiteChargeJobLogPo;
import com.cdz360.biz.model.trading.site.po.SiteChargeJobPlugPo;
import com.cdz360.biz.model.trading.site.po.SiteChargeJobPo;
import com.cdz360.biz.model.trading.site.po.SiteChargeJobTimePo;
import com.cdz360.biz.model.trading.site.type.SiteChargeJobStatus;
import com.cdz360.biz.model.trading.site.type.TimerOperation;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobLogVo;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobMoveCorpList;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobVo;
import com.cdz360.biz.model.trading.site.vo.TimeChargeVO;
import com.chargerlinkcar.framework.common.domain.param.JobTactic;
import com.chargerlinkcar.framework.common.domain.param.SiteChargeJobParam;
import com.chargerlinkcar.framework.common.domain.request.StartChargerRequest;
import com.chargerlinkcar.framework.common.domain.request.StopChargerRequest;
import com.chargerlinkcar.framework.common.domain.vo.CloudChargeVo;
import com.chargerlinkcar.framework.common.domain.vo.CommCusRef;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.PlugNoUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import java.lang.reflect.Type;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class SiteChargeJobService {

    @Autowired
    private SiteChargeJobPlugRoDS siteChargeJobPlugRoDS;
    @Autowired
    private SiteChargeJobPlugRwDs siteChargeJobPlugRwDs;
    @Autowired
    private SiteChargeJobRwDs siteChargeJobRwDs;
    @Autowired
    private SiteChargeJobRoDS siteChargeJobRoDS;
    @Autowired
    private SiteChargeJobTimeRoDS siteChargeJobTimeRoDS;
    @Autowired
    private SiteChargeJobTimeRwDs siteChargeJobTimeRwDs;
    @Autowired
    private SiteChargeJobLogRoDs siteChargeJobLogRoDs;
    @Autowired
    private SiteChargeJobLogRwDs siteChargeJobLogRwDs;
    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private ChargerOrderBizService chargerOrderBizService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private TRCommercialService commercialService;

    @Transactional
    public BaseResponse bindingJob(SiteChargeJobParam param) {
        IotAssert.isTrue(
            CollectionUtils.isEmpty(siteChargeJobPlugRoDS.getByPlugNos(param.getPlugNoList())),
            "存在枪头已绑定定时任务");

        SiteChargeJobVo vo = new SiteChargeJobVo();
        if (null != param.getStopSoc()) {
            IotAssert.isTrue(param.getStopSoc() > 0 &&
                param.getStopSoc() <= 100, "停充SOC无效");
            vo.setStopSoc(param.getStopSoc());
        } else {
            vo.setStopSoc(0);
        }

        if (param.getBindExistingJob()) {
            IotAssert.isNotNull(param.getExistingJobId(), "绑定已有任务时，已有任务ID不能为空");
            vo.setPlugNoList(param.getPlugNoList())
                .setOpId(param.getSysUserId())
                .setId(param.getExistingJobId());

        } else {

            Long sitePayAccountId = this.preCheck(param);

            vo.setPlugNoList(param.getPlugNoList())
                .setJobTimePoList(param.getJobTacticList().stream()
                    .map(JobTactic::parse).collect(Collectors.toList()))
                .setSiteId(param.getSiteId())
                .setName(param.getJobName())
                .setCommId(param.getCurrCommId())
                .setPayAccountType(param.getPayType().getCode())
                .setPayAccountId(sitePayAccountId)
                .setOpId(param.getSysUserId());

        }

        if (vo.getId() == null) {
            SiteChargeJobVo tmpSearch = new SiteChargeJobVo();
            tmpSearch.setName(vo.getName());
            List<SiteChargeJobVo> dbList = siteChargeJobRoDS.findByCondition(tmpSearch);
            IotAssert.isTrue(CollectionUtils.isEmpty(dbList), "任务名称重复");
            siteChargeJobRwDs.insert(vo); // 插入新任务

            // 插入时返回了vo.id
            vo.getJobTimePoList().forEach(e -> {
                e.setJobId(vo.getId());
            });
            siteChargeJobTimeRwDs.insert(vo.getJobTimePoList()); // 插入多条场站任务时间配置
        }

        SiteChargeJobVo tempStempearch = new SiteChargeJobVo();
        tempStempearch.setId(vo.getId());
        List<SiteChargeJobVo> dbList = siteChargeJobRoDS.findByCondition(tempStempearch);
        IotAssert.isTrue(CollectionUtils.isNotEmpty(dbList), "任务不存在");

        vo.getPlugNoList().forEach(e -> {
            Pair<String, Integer> tmp = PlugNoUtils.splitPlugNo(e);
            siteChargeJobPlugRwDs.bindByJobIdAndPlugNo(vo.getId(), tmp.getFirst(),
                tmp.getSecond()); // 任务和枪绑定
        });

        Long plugNum = siteChargeJobPlugRoDS.getPlugCountByJobId(vo.getId());
        vo.setPlugNum(plugNum.intValue());
        siteChargeJobRwDs.updateById(vo);

        return BaseResponse.success();
    }

    private Long preCheck(SiteChargeJobParam param) {
        IotAssert.isTrue(
            StringUtils.isNotBlank(param.getJobName()) && param.getJobName().length() <= 40
                && CollectionUtils.isNotEmpty(param.getJobTacticList()), "请检查入参");
        IotAssert.isTrue(param.getJobTacticList().size() <= 10, "任务策略最多配置10行内容");

        LocalDateTime today = LocalDateTime.now().withHour(0).withMinute(0).withSecond(0)
            .withNano(0);

        if (param.getExistingJobId() != null && param.getExistingJobId() > 0) {
            // 修改时，只校验最后一个结束日期是否早于今天，不校验开始日期
            LocalDateTime firstEndDate = DateUtil.dateTimeToDate(
                param.getJobTacticList().get(param.getJobTacticList().size() - 1).getEndDate());
            IotAssert.isTrue(today.isEqual(firstEndDate) || firstEndDate.isAfter(today),
                "保存失败，任务过期");
        } else {
            // 新增时，校验第一个开始日期是否早于今天
            LocalDateTime firstStartDate = DateUtil.dateTimeToDate(
                param.getJobTacticList().get(0).getStartDate());
            IotAssert.isTrue(today.isEqual(firstStartDate) || firstStartDate.isAfter(today),
                "保存失败，任务过期");
        }

        LocalDateTime lastEndTime = null;// 上一行的结束日期
        for (JobTactic e : param.getJobTacticList()) {

            LocalDateTime startDate = DateUtil.dateTimeToDate(e.getStartDate());
            if (lastEndTime != null) {
                IotAssert.isTrue(lastEndTime.isBefore(startDate),
                    "某一行的开始日期需大于上一行的结束日期");
            }
            LocalDateTime endDate = DateUtil.dateTimeToDate(e.getEndDate());
            IotAssert.isTrue(startDate.isBefore(endDate) || startDate.isEqual(endDate),
                "结束日期需大于等于开始日期");

            //校验入参-启动节点
            e.parameterCalibration();

            lastEndTime = endDate;
        }

        IotAssert.isTrue(param.getPayType() != null
                && param.getPayType() != PayAccountType.UNKNOWN
                && param.getPayType() != PayAccountType.OTHER,
            "请选择正确的结算账户类型");

        if (null != param.getStopSoc()) {
            IotAssert.isTrue(param.getStopSoc() > 0 &&
                param.getStopSoc() <= 100, "停充SOC无效");
        } else {
            param.setStopSoc(0);
        }

        param.setStartCharingEnable(null);//设置为null
        param.setSettlementMethod(null);//设置为null
        CloudChargeVo result = chargerOrderBizService.siteDebitAccountCheck(param);
        return result.getSitePayAccountId();
    }

    @Transactional
    public BaseResponse modifyChargeJob(SiteChargeJobParam param) {

        SiteChargeJobVo vo = new SiteChargeJobVo();

        Long sitePayAccountId = this.preCheck(param);

        vo.setPlugNoList(param.getPlugNoList())
            .setJobTimePoList(param.getJobTacticList().stream()
                .map(JobTactic::parse).collect(Collectors.toList()))
            .setSiteId(param.getSiteId())
            .setName(param.getJobName())
            .setCommId(param.getCurrCommId())
            .setPayAccountType(param.getPayType().getCode())
            .setPayAccountId(sitePayAccountId)
            .setOpId(param.getSysUserId())
            .setId(param.getExistingJobId());
        vo.setStopSoc(param.getStopSoc());

//        IotAssert.isTrue(CollectionUtils.isEmpty(siteChargeJobPlugRoDS.getByPlugNos(vo.getPlugNoList())),
//                "存在枪头已绑定定时任务");
        {
            SiteChargeJobVo tempStempearch = new SiteChargeJobVo();
            tempStempearch.setId(vo.getId());
            List<SiteChargeJobVo> dbList = siteChargeJobRoDS.findByCondition(tempStempearch);
            IotAssert.isTrue(CollectionUtils.isNotEmpty(dbList), "任务不存在");
        }
        {
            SiteChargeJobVo tmpSearch = new SiteChargeJobVo();
            tmpSearch.setName(vo.getName());
            List<SiteChargeJobVo> dbList = siteChargeJobRoDS.findByCondition(tmpSearch);
            if (CollectionUtils.isNotEmpty(dbList)) {
                dbList.forEach(e -> {
                    IotAssert.isTrue(NumberUtils.equals(vo.getId(), e.getId()), "任务名称重复");
                });
            }
        }

        vo.setStatus(SiteChargeJobStatus.VALID.getCode());
        siteChargeJobRwDs.updateById(vo);

        siteChargeJobTimeRwDs.deleteByJobId(vo.getId()); // 清除原有的时间配置
        vo.getJobTimePoList().forEach(e -> {
            e.setJobId(vo.getId());
        });
        siteChargeJobTimeRwDs.insert(vo.getJobTimePoList()); // 插入多条场站任务时间配置

//        vo.getPlugNoList().forEach(e -> {
//            Pair<String, Integer> tmp = PlugNoUtils.splitPlugNo(e);
//            siteChargeJobPlugRwDs.bindByJobIdAndPlugNo(vo.getId(), tmp.getFirst(), tmp.getSecond()); // 任务和枪绑定
//        });

//        Long plugNum = siteChargeJobPlugRoDS.getPlugCountByJobId(vo.getId());
//        vo.setPlugNum(plugNum.intValue());
//        siteChargeJobRwDs.updateById(vo);

        return BaseResponse.success();
    }


    public void unbindingJob(List<String> plugNos) {
        List<SiteChargeJobPlugPo> dbPlugPo = siteChargeJobPlugRoDS.getByPlugNos(plugNos);
        IotAssert.isTrue(dbPlugPo.size() == plugNos.size(),
            "存在枪头未绑定任务");
        dbPlugPo.forEach(e -> {
            // 根据枪进行解绑
            siteChargeJobPlugRwDs.deleteByJobIdAndPlugNo(e.getJobId(), e.getEvseNo(),
                e.getPlugIdx());
        });

        // 根据去重后的jobId,刷新枪头数
        dbPlugPo.stream().map(SiteChargeJobPlugPo::getJobId).distinct().forEach(e -> {
            Long plugNum = siteChargeJobPlugRoDS.getPlugCountByJobId(e);
            SiteChargeJobPo po = new SiteChargeJobPo();
            po.setId(e).
                setPlugNum(plugNum.intValue());
            siteChargeJobRwDs.updateById(po);
        });
    }

    public ListResponse<SiteChargeJobPo> getSiteChargeJobBySiteId(String siteId, String jobName) {
        SiteChargeJobVo tempStempearch = new SiteChargeJobVo();
        tempStempearch.setFuzzyJobName(jobName)
            .setStatusList(List.of(SiteChargeJobStatus.VALID.getCode(),
                SiteChargeJobStatus.BLOCK_UP.getCode()))
            .setSiteId(siteId);
        List<SiteChargeJobVo> voList = siteChargeJobRoDS.findByCondition(tempStempearch);
        // 剔除已过期的任务
        List<SiteChargeJobPo> res = voList.stream()
            .peek(e -> e.setJobTimePoList(null)).map(e -> (SiteChargeJobPo) e)
            .collect(Collectors.toList());
        return RestUtils.buildListResponse(res);
    }

    public ListResponse<SiteChargeJobVo> jobList(ChargeJobParam param) {
        IotAssert.isTrue(param.getStart() != null && param.getSize() != null
            && StringUtils.isNotBlank(param.getCommIdChain()), "请检查入参");
        List<SiteChargeJobVo> res = siteChargeJobRoDS.jobList(param);
        Long total = siteChargeJobRoDS.jobListCount(param);

        ListResponse<RBlocUser> tempRes1 = userFeignClient.selectRBlocUserIds(res.stream().filter(
                e -> NumberUtils.equals(e.getPayAccountType(), PayAccountType.CREDIT.getCode()))
            .map(SiteChargeJobPo::getPayAccountId).collect(Collectors.toList()));
        FeignResponseValidate.check(tempRes1);
        Map<Long, RBlocUser> blocMap = tempRes1.getData().stream()
            .collect(Collectors.toMap(RBlocUser::getId, o -> o));

        CommCusRef tempSearch = new CommCusRef();
        tempSearch.setIdList(res.stream().filter(
                e -> NumberUtils.equals(e.getPayAccountType(), PayAccountType.COMMERCIAL.getCode()))
            .map(SiteChargeJobPo::getPayAccountId).collect(Collectors.toList()));
        ListResponse<CommCusRef> tempRes2 = userFeignClient.findByCondition(tempSearch);
        FeignResponseValidate.check(tempRes2);
        Map<Long, CommCusRef> merchantMap = tempRes2.getData().stream()
            .collect(Collectors.toMap(CommCusRef::getId, o -> o));

        res.forEach(e -> {

            if (NumberUtils.equals(e.getPayAccountType(), PayAccountType.PERSONAL.getCode())) {
                e.setDebitAccountName("个人账户");

            } else if (NumberUtils.equals(e.getPayAccountType(),
                PayAccountType.CREDIT.getCode())) {
                RBlocUser info = blocMap.get(e.getPayAccountId());
                if (info != null) {
                    e.setDebitAccountPhone(info.getPhone())
                        .setDebitAccountName("企业账户-" + info.getBlocUserName())
                        .setDebitAccountCommId(info.getCommId())
                        .setDebitAccountCorpId(info.getBlocUserId())
                        .setDebitAccountRCorpUserId(info.getId());
                }
            } else if (NumberUtils.equals(e.getPayAccountType(),
                PayAccountType.COMMERCIAL.getCode())) {
                CommCusRef info = merchantMap.get(e.getPayAccountId());
                if (info != null) {
                    e.setDebitAccountPhone(info.getUserPhone())
                        .setDebitAccountName("商户会员-" + info.getCommName())
                        .setDebitAccountCommId(info.getCommId());
                }
            }

        });
        return RestUtils.buildListResponse(res, total);
    }

    /**
     * 检查任务是否过期 若配置的结束时间小于系统时间，则认为过期
     *
     * @param vo
     */
    private void checkForExpiration(SiteChargeJobVo vo) {

        IotAssert.isNotNull(vo.getJobTimePoList(), "任务时间配置为空");

        vo.getJobTimePoList().sort(Comparator.comparing(SiteChargeJobTimePo::getEndDate));

        SiteChargeJobTimePo lastTimePo = vo.getJobTimePoList()
            .get(vo.getJobTimePoList().size() - 1);
        List<JobTacticTime> time = lastTimePo.getTime();
        String[] timeList = time.get(time.size() - 1).getTime().split(":"); // 12:30
        LocalDateTime maxEndTime = DateUtil.dateTimeToDate(lastTimePo.getEndDate())
            .withHour(Integer.parseInt(timeList[0]))
            .withMinute(Integer.parseInt(timeList[1]));

        LocalDateTime currTime = LocalDateTime.now().withSecond(0).withNano(0);
        if (currTime.isAfter(maxEndTime)) {
            this.changeJobStatus(vo.getId(), SiteChargeJobStatus.EXPIRES.getCode());
        }
    }

    public BaseResponse changeJobStatus(Long jobId, Integer jobStatus) {
        SiteChargeJobPo po = new SiteChargeJobPo();
        po.setId(jobId).setStatus(jobStatus);
        siteChargeJobRwDs.updateById(po);
        return BaseResponse.success();
    }

    public ListResponse<SiteChargeJobLogVo> getChargeJobLogList(ChargeJobLogParam param) {
        return siteChargeJobLogRoDs.jobLogList(param);
    }

    public ListResponse<TimeChargeVO> loadChargeJob() {

        List<TimeChargeVO> res = new ArrayList<>();

        LocalDateTime currDateTime = LocalDateTime.now();
        LocalDateTime today = currDateTime.withHour(0).withMinute(0).withSecond(0).withNano(0);

        this.filterJob(currDateTime, today, res);

        // 若40分钟之后时间会跨到第二天，则第二天零点左右的任务也要返回
        LocalDateTime later = currDateTime.plusMinutes(40);
        LocalDateTime tomorrow = today.plusDays(1);
        if (later.isEqual(tomorrow) || later.isAfter(tomorrow)) {

            this.filterJob(currDateTime, tomorrow, res);
        }

        // 按配置的充电时间排序
        res.sort(Comparator.comparing(TimeChargeVO::getStartTime));
        log.info("load res.size = {}", res.size());
        return RestUtils.buildListResponse(res);
    }

    private void filterJob(LocalDateTime currDateTime, LocalDateTime targetDay,
        List<TimeChargeVO> res) {

        List<SiteChargeJobTimePo> poList = siteChargeJobTimeRoDS.loadChargeJob(
            DateUtil.timeToDate(targetDay));
        for (SiteChargeJobTimePo e : poList) {
            List<JobTacticTime> timeList = JsonUtils.fromJson(JsonUtils.toJsonString(e.getTime()),
                new TypeReference<List<JobTacticTime>>() {
                    @Override
                    public Type getType() {
                        return super.getType();
                    }
                });
//                List<JobTacticTime> timeList = JSONObject.parseArray(
//                JsonUtils.toJsonString(e.getTime()), JobTacticTime.class);
            for (int i = 0; i < timeList.size(); i++) {
                String[] temp = timeList.get(i).getTime().split(":"); // 12:00 或 00:00
                LocalDateTime tempEndDate = targetDay.withHour(Integer.parseInt(temp[0]))
                    .withMinute(Integer.parseInt(temp[1]));

                // 若配置的充电时间在30分钟以内，则返回
                Duration duration = Duration.between(currDateTime, tempEndDate);
                if (duration.toMinutes() >= 0 && duration.toMinutes() <= 30) {
                    TimeChargeVO timeChargeVO = new TimeChargeVO();
                    timeChargeVO.setJobId(e.getJobId())
                        .setTimeIdx(i + 1)
                        .setOperation(timeList.get(i).getOperation())
                        .setStartTime(tempEndDate);
                    res.add(timeChargeVO);
                }
            }

        }
    }


    public BaseResponse executeChargingJob(TimeChargeVO vo) {

        // 1.读取siteId,payAccountType,payAccountId,opId,绑定的枪头
        ChargeJobParam tempSearch = new ChargeJobParam();
        tempSearch.setJobId(vo.getJobId());
        List<SiteChargeJobVo> res = siteChargeJobRoDS.jobList(tempSearch);
        IotAssert.isTrue(CollectionUtils.isNotEmpty(res), "找不到该任务");
        SiteChargeJobVo info = res.get(0);
        log.debug("job detail: {}", JsonUtils.toJsonString(info));

        //创建执行日志
        info.getPlugNoList().forEach(e -> {
            SiteChargeJobLogPo temp = new SiteChargeJobLogPo();
            Pair<String, Integer> pair = PlugNoUtils.splitPlugNo(e);
            temp.setJobId(info.getId())
                .setTimeIdx(vo.getTimeIdx())
                .setOperation(vo.getOperation())
                .setSiteId(info.getSiteId())
                .setEvseNo(pair.getFirst())
                .setPlugIdx(pair.getSecond());
            siteChargeJobLogRwDs.insert(temp);
            // 将plugNo-temp.id 写入redis
            redisUtil.setMinutes(PlugNoUtils.PLATFORM_SCHEDULE_JOB_LOG_ID + e,
                String.valueOf(temp.getId()), 20);
        });

        if (TimerOperation.START.equals(vo.getOperation())) {
            // 检查并推入，充电下发队列
            StartChargerRequest chargerRequest = new StartChargerRequest();
            chargerRequest.setSysUserId(info.getOpId())
                .setStartType(OrderStartType.SCHEDULE_JOB)
                .setScheduleJobAccountId(info.getPayAccountId())
                .setScheduleJobAccountType(info.getPayAccountType())
                .setPlugNoList(info.getPlugNoList())
                .setSiteId(info.getSiteId())
                .setStopSoc(info.getStopSoc());
            CloudChargeVo cloudChargeVo = chargerOrderBizService.webStartCharger(chargerRequest);
            log.info("cloudChargeVo:{}", cloudChargeVo);
            if (cloudChargeVo.getFailPlugNoList() != null
                && cloudChargeVo.getFailPlugNoList().size() > 0) {

                cloudChargeVo.getFailPlugNoList().forEach((k, v) -> {
                    // 通过plugNo,从redis中读取logId,进行回写
                    Long jobLogId = Long.valueOf(
                        redisUtil.get(PlugNoUtils.PLATFORM_SCHEDULE_JOB_LOG_ID + k));
                    SiteChargeJobLogPo writeBack = new SiteChargeJobLogPo();
                    writeBack.setId(jobLogId)
                        .setResult(1)
                        .setFailReason(v);
                    siteChargeJobLogRwDs.writeBack(writeBack);
                    redisUtil.del(PlugNoUtils.PLATFORM_SCHEDULE_JOB_LOG_ID + k);
                });
            }
        } else if (TimerOperation.STOP.equals(vo.getOperation())) {

            List<StopChargerRequest> stopRequest = info.getPlugNoList().stream().map(e -> {
                StopChargerRequest request = new StopChargerRequest();
                request.setPlugNo(e);
                return request;
            }).collect(Collectors.toList());
            Optional<Map<String, String>> failPlugNoMap = chargerOrderBizService.webStopCharger(
                OrderStartType.SCHEDULE_JOB, stopRequest);
            if (failPlugNoMap.isPresent() && failPlugNoMap.get().size() > 0) {

                failPlugNoMap.get().forEach((k, v) -> {
                    // 通过plugNo,从redis中读取logId,进行回写
                    Long jobLogId = Long.valueOf(
                        redisUtil.get(PlugNoUtils.PLATFORM_SCHEDULE_JOB_LOG_ID + k));
                    SiteChargeJobLogPo writeBack = new SiteChargeJobLogPo();
                    writeBack.setId(jobLogId)
                        .setResult(1)
                        .setFailReason(v);
                    siteChargeJobLogRwDs.writeBack(writeBack);
                    redisUtil.del(PlugNoUtils.PLATFORM_SCHEDULE_JOB_LOG_ID + k);
                });
            }
        }

        return RestUtils.success();
    }

    /**
     * 任务执行日志创建时间超过10分钟仍无结果，认为超时 任务配置的最后启动时间小于系统时间，认为任务状态过期
     */
    public void checkForTimeout() {

        //任务执行日志创建时间超过10分钟仍无结果，认为超时
        List<SiteChargeJobLogPo> poList = siteChargeJobLogRoDs.loadTimeoutJob();
        poList.forEach(e -> {
            e.setResult(2);
            siteChargeJobLogRwDs.writeBack(e);
        });

        //任务配置的最后启动时间小于系统时间，认为任务状态过期
        SiteChargeJobVo tempStempearch = new SiteChargeJobVo();
        tempStempearch.setStatus(SiteChargeJobStatus.VALID.getCode());
        List<SiteChargeJobVo> voList = siteChargeJobRoDS.findByCondition(tempStempearch);
        voList.forEach(this::checkForExpiration);
    }

    public void writeBack(SiteChargeJobLogPo po) {
        IotAssert.isTrue(po.getId() != null && po.getResult() != null,
            "缺少入参");
        siteChargeJobLogRwDs.writeBack(po);
    }

    public SiteChargeJobMoveCorpList getMoveCorpDetail(Long corpId, Long commId) {
        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");

        List<SiteChargeJobVo> jobList = siteChargeJobLogRoDs.getSiteJobByCorpId(corpId);

        SiteChargeJobMoveCorpList ret = new SiteChargeJobMoveCorpList();

        if (CollectionUtils.isNotEmpty(jobList)) {
            CommPo commercialById = commercialService.getCommercialById(commId);
            IotAssert.isNotNull(commercialById, "找不到对应商户: " + commId);

            // 后台启动设置删除列表
            List<SiteChargeJobVo> jobRemoveList = jobList.stream()
                .filter(e -> e.getIdChainSite().indexOf(commercialById.getIdChain()) != 0)
                .collect(Collectors.toList());
            ret.setRemoveList(jobRemoveList);

            List<SiteChargeJobVo> jobRemainList = jobList.stream()
                .filter(e -> e.getIdChainSite().indexOf(commercialById.getIdChain()) == 0)
                .collect(Collectors.toList());
            ret.setRemainList(jobRemainList);

            // 位于 remove 列表的相同元素需要从 remain 中剔除
            Set<String> removeSet = ret.getRemoveList().stream().map(SiteChargeJobVo::getSiteId)
                .collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(removeSet) && CollectionUtils.isNotEmpty(
                ret.getRemainList())) {
                List<SiteChargeJobVo> collect = ret.getRemainList()
                    .stream()
                    .filter(e -> !removeSet.contains(e.getSiteId()))
                    .collect(Collectors.toList());
                ret.setRemainList(collect);
            }
        }

        return ret;
    }

    public Long moveCorpDetail(Long corpId, Long commId) {
        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");

        CommPo commercialById = commercialService.getCommercialById(commId);
        IotAssert.isNotNull(commercialById, "找不到对应商户: " + commId);
        IotAssert.isNotBlank(commercialById.getIdChain(), "找不到该企业idChain");

        Long ret = siteChargeJobLogRwDs.moveCorpDetail(corpId, commercialById.getIdChain());

        log.info("移除场站定时任务配置项：{}个", ret);

        return ret;
    }

}
