package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServerException;
import com.cdz360.base.model.charge.vo.SiteVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.client.AuthCenterFeignClient;
import com.cdz360.biz.dc.client.reactor.ReactorOpenHlhtFeignClient;
import com.cdz360.biz.ds.trading.ro.contract.ds.ContractRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ds.trading.rw.contract.ds.ContractRwDs;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.merchant.dto.SiteTinyDto;
import com.cdz360.biz.model.trading.contract.param.AddContractParam;
import com.cdz360.biz.model.trading.contract.param.ContractListParam;
import com.cdz360.biz.model.trading.contract.po.ContractSite;
import com.cdz360.biz.model.trading.contract.type.ContractType;
import com.cdz360.biz.model.trading.contract.type.PayCycle;
import com.cdz360.biz.model.trading.contract.vo.ContractVo;
import com.cdz360.biz.model.trading.hlht.po.PartnerPo;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.OptionalUtils;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class ContractService {

    @Autowired
    private ContractRoDs contractRoDs;

    @Autowired
    private ContractRwDs contractRwDs;

    @Autowired
    private FileService fileService;

    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private ReactorOpenHlhtFeignClient reactorOpenHlhtFeignClient;


    @Transactional
    public BaseResponse addContract(AddContractParam param) {

        this.checkData(param);
        if (StringUtils.isBlank(param.getSettleInfo())) {
            param.setSettleInfo("");
        }
        // 非互联互通合约，设备侧标志为空
        if (!param.getContractType().equals(ContractType.CON_C12)) {
            param.setOperatorCode("");
        } else {
            param.setCustomerName(StringUtils.isNotBlank(param.getCustomerName()) ? param.getCustomerName() : "");
        }
        contractRwDs.addContract(param);

        // 销售合约 施工  设备租赁其他合约场站非必填项
        if (CollectionUtils.isNotEmpty(param.getSiteIdList())) {
            List<ContractSite> contractSiteList = param.getSiteIdList().stream().map(e -> {
                ContractSite contractSite = new ContractSite();
                return contractSite.setSiteId(e).setContractId(param.getContractId());
            }).collect(Collectors.toList());
            contractRwDs.addOrUpdateContractSite(contractSiteList);
        }

        fileService.updateContractFile(param.getContractId(), param.getContractLinks());
        return RestUtils.success();
    }

    @Transactional
    public BaseResponse delContract(Long id, String idChain, Long sysUid) {
        IotAssert.isNotNull(id, "合同ID不能为空");
        IotAssert.isNotNull(contractRoDs.getContractById(id, idChain), "合同不存在");
        contractRwDs.disableContract(id, sysUid);
        contractRwDs.updateContractSiteEnable(id);
        fileService.updateContractFileEnable(id);
        return RestUtils.success();
    }

    @Transactional
    public BaseResponse updateContract(AddContractParam param) {
        IotAssert.isNotNull(param.getContractId(), "合同ID不存在");
        //判断是否存在
        ContractVo contractVo = contractRoDs.getContractById(param.getContractId(), param.getIdChain());
        if (contractVo == null) {
            throw new DcServerException("合同不存在");
        }
        //非平台合约,场地租赁合约付款周期清0
//        if (!param.getContractType().equals(ContractType.CON_C6)
//                && !param.getContractType().equals(ContractType.CON_C1)) {
//            param.setPayCycle(PayCycle.PAY_C0);
//        }

        // 平台合约  金额清0
        if (param.getContractType().equals(ContractType.CON_C6)) {
            param.setAmount(BigDecimal.ZERO);
        }
        if (param.getPayCycle() == null) {
            param.setPayCycle(PayCycle.PAY_C0);
        }
        if (StringUtils.isBlank(param.getSettleInfo())) {
            param.setSettleInfo("");
        }
        if (!param.getContractType().equals(ContractType.CON_C12)) {
            param.setOperatorCode("");
        } else {
            param.setCustomerName(StringUtils.isNotBlank(param.getCustomerName()) ? param.getCustomerName() : "");
        }
        contractRwDs.updateContract(param);
        contractRwDs.updateContractSiteEnable(param.getContractId());
        fileService.updateContractFileEnable(param.getContractId());

        // 销售 ，施工，设备租赁，其他合约场站为空
        if (CollectionUtils.isNotEmpty(param.getSiteIdList())) {
            List<ContractSite> contractSiteList = param.getSiteIdList().stream().map(e -> {
                ContractSite contractSite = new ContractSite();
                return contractSite.setSiteId(e).setContractId(param.getContractId());
            }).collect(Collectors.toList());
            contractRwDs.addOrUpdateContractSite(contractSiteList);
        }

        fileService.updateContractFile(param.getContractId(), param.getContractLinks());
        return RestUtils.success();
    }

    public void checkData(AddContractParam param) {

        IotAssert.isNotNull(param.getContractType(), "合约类型不能为空");
        IotAssert.isNotNull(param.getContractName(), "合同编号不能为空");
        IotAssert.isNotNull(param.getSignTime(), "签订日期不能为空");
        IotAssert.isNotNull(param.getCommId(), "所属商户不能为空");
        IotAssert.isNotNull(param.getContractLinks(), "纸质合约内容不能为空");

        //平台合约必填收费方式，服务费用，开始日期，截止日期
        if (param.getContractType().equals(ContractType.CON_C6)) {
            IotAssert.isNotNull(param.getStartTime(), "开始日期不能为空");
            IotAssert.isNotNull(param.getEndTime(), "截止日期不能为空");
        }
        if (param.getContractNo() != null && param.getContractNo().length() > 30) {
            throw new DcServerException("合同编号最多可输入30个字符");
        }

        if (param.getContractName().length() > 30) {
            throw new DcServerException("合约名称最多可输入30个字符");
        }
    }

    public ContractVo getContractById(Long contractId, String idChain) {
        IotAssert.isNotNull(contractId, "合约ID不能为空");
        ContractVo contractVo = contractRoDs.getContractById(contractId, idChain);
        IotAssert.isNotNull(contractVo, "合约信息不存在");
        contractVo.setOssFileList(fileService.getFileList(contractVo.getId()));
        // 录入人信息
        if (contractVo.getSysUid() != null) {
            List<Long> userIdList = List.of(contractVo.getSysUid());
            ListResponse<SysUserVo> response = authCenterFeignClient.querySysUserByIds(userIdList);
            FeignResponseValidate.check(response);
            contractVo.setUsername(response.getData().get(0).getUsername());
        }
        return contractVo;
    }

    public ListResponse<ContractVo> getContractList(ContractListParam param) {

        if (param.getStart() == null) {
            param.setStart(0L);
        }
        if (param.getSize() == null) {
            param.setSize(10);
        }

        if (param.getContractStatus() != null) {
            param.setStatus(param.getContractStatus().toString());
        }
        Long total = contractRoDs.getContractCount(param);
        List<ContractVo> list = new ArrayList<>();

        if (total != null && total > 0) {
            list = contractRoDs.getContractList(param);
            List<Long> userIdList = list.stream().filter(e -> e.getSysUid() != null).map(ContractVo::getSysUid).collect(Collectors.toList());
            Map<Long, String> userMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(userIdList)) {
                ListResponse<SysUserVo> response = authCenterFeignClient.querySysUserByIds(userIdList);
                FeignResponseValidate.check(response);
                userMap = response.getData().stream().collect(Collectors.toMap(e -> e.getId(), e -> e.getUsername()));
            }
            Map<Long, String> finalUserMap = userMap;
            Map<Long, String> finalUserMap1 = userMap;
            list.stream().map(e -> {
                e.setOssFileList(fileService.getFileList(e.getId()));
                if (finalUserMap.containsKey(e.getSysUid())) {
                    e.setUsername(finalUserMap1.get(e.getSysUid()));
                }

                // 互联互通合约的客户名称
                if (e.getContractType().equals(String.valueOf(ContractType.CON_C12.getCode())) && StringUtils.isNotBlank(e.getOperatorCode())) {
                    ObjectResponse<PartnerPo> response = reactorOpenHlhtFeignClient.getDetailByCode(e.getOperatorCode())
                        .block(Duration.ofSeconds(50L));
                    if (response != null && response.getData() != null) {
                        e.setCustomerName(response.getData().getName());
                    }
                }
                // 文件信息
                e.setOssFileList(fileService.getFileList(e.getId()));
                return e;
            }).collect(Collectors.toList());
        }
        return new ListResponse<>(list, total);
    }

    public Mono<ListResponse<ContractVo>> getContractBySiteId(String siteId, Long size) {
        IotAssert.isNotBlank(siteId, "场站Id不能为空");
        return Mono.just(siteId)
                .map(p -> {
//                    String idChain = siteBizService.getIdChainBySiteId(p);
//                    IotAssert.isNotNull(idChain, "场站所属商户信息不存在");
//                    List<Long> commIdList = Arrays.asList(idChain.split(",")).stream().map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
                    List<ContractVo> list = contractRoDs.getContractBySiteId(p, size != null ? size : 2L);
                    return RestUtils.buildListResponse(list);
                });
    }

    public Mono<ListResponse<SiteVo>> getSiteListByContractId(Long contractId) {
        List<SiteVo> siteVos = contractRoDs.getSiteListByContractId(contractId);

        OptionalUtils.ofEmptyListAble(siteVos)
            .map(t -> siteVos.stream()
                .filter(e -> e != null && StringUtils.isBlank(e.getSiteName()))
                .map(SiteVo::getId).collect(Collectors.toList()))
            .filter(CollectionUtils::isNotEmpty)
            .ifPresent(namelessSiteIds -> {
                ListSiteParam param = new ListSiteParam();
                param.setSiteIdList(namelessSiteIds);
                List<SiteTinyDto> siteTinyList = siteRoDs.getSiteTinyList(param);

                OptionalUtils.ofEmptyListAble(siteTinyList)
                    .map(e -> e.stream().collect(Collectors.toMap(
                        SiteTinyDto::getId, SiteTinyDto::getSiteName)))
                    .ifPresent(nameMap -> {
                        siteVos.stream().filter(x -> StringUtils.isBlank(x.getSiteName()))
                            .forEach(vo -> {
                                vo.setSiteName(nameMap.get(vo.getId()));
                            });
                    });
            });

        return Mono.just(siteVos)
            .map(RestUtils::buildListResponse);
    }

}
