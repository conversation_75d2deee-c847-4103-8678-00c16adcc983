package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.DeviceBizService;
import com.cdz360.biz.model.trading.iot.po.BsBoxPo;
import com.cdz360.biz.model.trading.iot.po.BsChargerPo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@Tag(name = "桩/枪相关接口", description = "桩/枪")
public class DeviceRest {

    @Autowired
    private DeviceBizService evseBizService;


    @Operation(summary = "获取设备信息")
    @GetMapping("/dataCore/device/evse/getBsBox")
    public ObjectResponse<BsBoxPo> getBsBox(
            ServerHttpRequest request, @RequestParam String evseNo) {
        log.info(LoggerHelper2.formatEnterLog(request));
        var box = this.evseBizService.getBsBox(evseNo);
        //log.info("i = {}", i);
        return RestUtils.buildObjectResponse(box);
    }


    @Operation(summary = "修改设备信息")
    @PostMapping("/dataCore/device/evse/updateBsBox")
    public BaseResponse updateBsBox(
            ServerHttpRequest request, @RequestBody BsBoxPo bsBox) {
        log.info(LoggerHelper2.formatEnterLog(request) + " bsBox = {}", JsonUtils.toJsonString(bsBox));
        boolean ret = this.evseBizService.updateBsBox(bsBox);
        log.info("更新结果 ret = {}", ret);
        return RestUtils.success();
    }


    @Operation(summary = "获取枪头设备信息")
    @GetMapping("/dataCore/device/plug/getPlugInfo")
    public ObjectResponse<BsChargerPo> getPlugInfo(
            ServerHttpRequest request, @RequestParam String plugNo) {
        log.info(LoggerHelper2.formatEnterLog(request));
        var box = this.evseBizService.getBsCharge(plugNo);
        //log.info("i = {}", i);
        return RestUtils.buildObjectResponse(box);
    }

    @Operation(summary = "获取设备信息")
    @GetMapping("/dataCore/device/evse/getEvseList")
    public ListResponse<EvseVo> getEvseList(
        ServerHttpRequest request, @RequestParam String siteId) {
        log.info("获取设备信息 request = {}, siteId = {}", LoggerHelper2.formatEnterLog(request), siteId);
        List<EvseVo> evseVoList = this.evseBizService.getEvseList(siteId);
        return RestUtils.buildListResponse(evseVoList);
    }

    @Operation(summary = "获取设备信息")
    @GetMapping("/dataCore/device/plug/getPlugList")
    public ListResponse<PlugVo> getPlugList(
        ServerHttpRequest request, @RequestParam String siteId) {
        log.info("获取设备信息 request = {}, siteId = {}", LoggerHelper2.formatEnterLog(request), siteId);
        List<PlugVo> plugVoList = this.evseBizService.getPlugList(siteId);
        return RestUtils.buildListResponse(plugVoList);
    }
}
