package com.cdz360.biz.dc.service.bill;

import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.config.MchBillRegexConfig;
import com.cdz360.biz.ds.trading.ro.bill.ds.ZftThirdOrderRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.PayBillRoDs;
import com.cdz360.biz.ds.trading.rw.bill.ds.ZftDailyBillRwDs;
import com.cdz360.biz.ds.trading.rw.bill.ds.ZftThirdOrderRwDs;
import com.cdz360.biz.ds.trading.rw.order.ds.PayBillRwDs;
import com.cdz360.biz.model.trading.bill.po.ZftDailyBillPo;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URL;
import java.nio.channels.Channels;
import java.nio.channels.ReadableByteChannel;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public abstract class AbstractDailyBillDealStrategy implements DailyBillDealStrategy {
    @Autowired
    protected ZftDailyBillRwDs zftDailyBillRwDs;

    @Autowired
    private PayBillRwDs payBillRwDs;

    @Autowired
    private PayBillRoDs payBillRoDs;

    @Autowired
    private ZftThirdOrderRoDs zftThirdOrderRoDs;

    @Autowired
    private ZftThirdOrderRwDs zftThirdOrderRwDs;

    /**
     * 下载远程文件并保存到本地
     *
     * @param remoteUrl
     * @param localPath
     */
    protected void downloadFile(String remoteUrl, String localPath) {
        URL website = null;
        ReadableByteChannel rbc = null;
        FileOutputStream fos = null;
        try {
            website = new URL(remoteUrl);
            rbc = Channels.newChannel(website.openStream());
            fos = new FileOutputStream(localPath);//本地要存储的文件地址 例如：test.txt
            fos.getChannel().transferFrom(rbc, 0, Long.MAX_VALUE);
        } catch (Exception e) {
            log.error("下载对账单失败{}", e.getMessage(), e);
        } finally {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    log.warn("关闭流异常: err = {}", e.getMessage());
                }
            }
            if (rbc != null) {
                try {
                    rbc.close();
                } catch (IOException e) {
                    log.warn("关闭流异常: err = {}", e.getMessage());
                }
            }
        }
    }

    protected void checkPayBill(ZftDailyBillPo dailyBillPo) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(dailyBillPo.getBillDate());
        calendar.add(Calendar.DATE, 1);
        Date endDate = calendar.getTime();
        boolean siftMchId = StringUtils.isNotBlank(dailyBillPo.getMchId())
            && dailyBillPo.getZftId() > 0;
        List<PayChannel> channelList = null;
        if (PayChannel.WXPAY.equals(dailyBillPo.getChannel())) {
            channelList = List.of(PayChannel.WXPAY, PayChannel.WX_CREDIT);
        } else if (PayChannel.ALIPAY.equals(dailyBillPo.getChannel())) {
            channelList = List.of(PayChannel.ALIPAY, PayChannel.ALIPAY_CREDIT);
        }
        payBillRwDs.updateCheckResult(
            dailyBillPo.getId(),
            dailyBillPo.getMchId(), channelList,
            dailyBillPo.getBillDate(), endDate,
            dailyBillPo.getZftCommId(), siftMchId);
    }

    protected void checkZftThirdOrderResult(MchBillRegexConfig.CorpMchInfo corpMchInfo) {
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date beginDate = calendar.getTime();
        zftThirdOrderRwDs.updateZftThirdOrderResult(corpMchInfo.getCorpId(),beginDate);
    }
}
