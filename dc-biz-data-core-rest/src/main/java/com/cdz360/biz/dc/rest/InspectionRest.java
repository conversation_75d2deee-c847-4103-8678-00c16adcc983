package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.yw.InspectionService;
import com.cdz360.biz.model.trading.site.dto.InspectionRecordDto;
import com.cdz360.biz.model.trading.site.dto.RecentInspectionRecordDto;
import com.cdz360.biz.model.trading.site.param.ChangeRecordParam;
import com.cdz360.biz.model.trading.site.param.InspectionParam;
import com.cdz360.biz.model.trading.site.param.RecordParam;
import com.cdz360.biz.model.trading.site.param.SiteInspectionRecordParam;
import com.cdz360.biz.model.trading.site.po.SiteInspectionCfgPo;
import com.cdz360.biz.model.trading.site.po.SiteInspectionRecordPo;
import com.cdz360.biz.model.trading.site.type.SiteInspectionStatus;
import com.cdz360.biz.model.trading.site.vo.InspectionRecordBi;
import com.cdz360.biz.model.trading.site.vo.InspectionRecordVo;
import com.cdz360.biz.model.trading.site.vo.SiteInspectionRecordVo;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * 巡检工单相关接口
 */
@Slf4j
@RestController
@RequestMapping(value = "/dataCore/inspection")
public class InspectionRest {

    @Autowired
    private InspectionService service;

    /**
     * 获取最近一次巡检信息
     */
    @GetMapping(value = "/getRecentRecord")
    public ObjectResponse<RecentInspectionRecordDto> getRecentRecord(
        @RequestParam("siteId") String siteId) {
        return service.getRecentRecord(siteId);
    }

    /**
     * 获取场站巡检的配置
     *
     * @param siteId
     * @return
     */
    @GetMapping(value = "/getConfig")
    public ObjectResponse<SiteInspectionCfgPo> getConfig(@RequestParam("siteId") String siteId) {
        log.info("getConfig siteId: {}", siteId);
        return service.getConfig(siteId);
    }

    /**
     * 修改巡检配置
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/editConfig")
    public BaseResponse editConfig(@RequestBody SiteInspectionCfgPo req) {
        log.info("editConfig req: {}", req);
        return service.editConfig(req);
    }

    /**
     * 巡检记录-饼图数据
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/getRecordBi")
    public ListResponse<InspectionRecordBi> getRecordBi(
        @RequestBody SiteInspectionRecordParam param) {
        log.info("getRecordBi param: {}", param);
        return service.getRecordBi(param);
    }

    /**
     * 巡检记录-表格数据
     *
     */
    @PostMapping(value = "/getRecordVoList")
    public ListResponse<InspectionRecordVo> getRecordVoList(
        @RequestBody SiteInspectionRecordParam param) {
        log.info("getRecordVoList param: {}", param);
        return service.getRecordVoList(param);
    }

    /**
     * 巡检工单
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/getRecords")
    public ListResponse<InspectionRecordDto> getRecords(@RequestBody RecordParam param) {
        log.info("getRecords param: {}", param);
        return service.getRecords(param);
    }


    /**
     * 修改工单状态
     *
     * @param recordId
     * @param status
     * @return
     */
    @GetMapping(value = "/changeStatus")
    public BaseResponse changeStatus(@RequestParam("sysUserId") Long sysUserId,
        @RequestParam("recordId") Long recordId,
        @RequestParam("status") SiteInspectionStatus status) {
        log.info("changeStatus recordId: {}, status: {}, sysUserId: {}", recordId, status,
            sysUserId);
        return service.changeStatus(sysUserId, recordId, status);
    }

    /**
     * 批量质检
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/changeStatusBatch")
    public BaseResponse changeStatusBatch(@RequestBody ChangeRecordParam param) {
        log.info("changeStatusBatch param: {}", param);
        return service.changeStatusBatch(param);
    }

    @GetMapping(value = "/del")
    public BaseResponse recordDel(@RequestParam("sysUserId") Long sysUserId,
        @RequestParam("recordId") Long recordId) {
        log.info("recordDel sysUserId: {}, recordId: {}", sysUserId, recordId);
        return service.recordDel(sysUserId, recordId);
    }


    /**
     * 巡检详情
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/getDetail")
    public ObjectResponse<SiteInspectionRecordVo> getDetail(@RequestParam("id") Long id) {
        log.info("getDetail id: {}", id);
        return service.getDetail(id);
    }
//
//    /**
//     * 导出巡检详情
//     * @param id
//     * @return
//     */
//    @GetMapping(value = "/recordExport")
//    public ObjectResponse<ExcelPosition> recordExport(@RequestParam("id") Long id) throws IOException, DocumentException {
//        log.info("recordExport id: {}", id);
//        return service.recordExport(id);
//    }

    /**
     * 获取待巡检场站
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/getNeedInspectionSite")
    public ListResponse<RecentInspectionRecordDto> getNeedInspectionSite(
        @RequestBody InspectionParam param) {
        log.debug("getNeedInspectionSite param: {}", param);
        return service.getNeedInspectionSite(param);
    }

    @PostMapping(value = "/siteInspectionReminder")
    public BaseResponse siteInspectionReminder() {
        service.siteInspectionReminder();
        return RestUtils.success();
    }

    /**
     * 获取待处理巡检工单
     *
     * @param sysUserId
     * @return
     */
    @PostMapping(value = "/getToBeInspectRecord")
    public ListResponse<SiteInspectionRecordVo> getToBeInspectRecord(
        @RequestParam("sysUserId") Long sysUserId,
        @RequestBody BaseListParam param) {
        log.info("getToBeInspectRecord sysUserId: {}, param: {}", sysUserId, param);
        return service.getToBeInspectRecord(sysUserId, param);
    }

    /**
     * 获取历史巡检工单
     *
     * @param sysUserId
     * @return
     */
    @PostMapping(value = "/getHistoryRecord")
    public ListResponse<SiteInspectionRecordVo> getHistoryRecord(
        @RequestParam("sysUserId") Long sysUserId,
        @RequestBody BaseListParam param) {
        log.info("getHistoryRecord sysUserId: {}, param: {}", sysUserId, param);
        return service.getHistoryRecord(sysUserId, param);
    }

    /**
     * 通过枪号获取场站巡检信息
     *
     * @param qrCode
     * @param siteId
     * @return
     */
    @GetMapping(value = "/getSiteByPlugNo")
    public ObjectResponse<SiteVo> getSiteByPlugNo(@RequestParam(value = "topCommId") Long topCommId,
        @RequestParam(value = "plugNo", required = false) String plugNo,
        @RequestParam(value = "qrCode", required = false) String qrCode,
        @RequestParam(value = "siteId", required = false) String siteId) {
        log.info("getSiteByPlugNo topCommId: {}, plugNo: {}, qrCode: {}, siteId: {}", topCommId,
            plugNo, qrCode, siteId);
        return service.getSiteByPlugNo(topCommId, plugNo, qrCode, siteId);
    }

    /**
     * 创建巡检单
     *
     * @param siteId
     * @return
     */
    @GetMapping(value = "/create")
    public ObjectResponse<SiteInspectionRecordPo> create(
        @RequestParam(value = "sysUserId") Long sysUserId,
        @RequestParam(value = "siteId") String siteId,
        @RequestParam(value = "inspectionType") Integer inspectionType) {
        log.info("create sysUserId: {}, siteId: {}, inspectionType: {}", sysUserId, siteId,
            inspectionType);
        return service.create(sysUserId, siteId, inspectionType);
    }

    @Operation(summary = "检查是否有未结束的巡检单")
    @PostMapping(value = "/existUnfinishedRecord")
    public ObjectResponse<Boolean> existUnfinishedRecord(@RequestBody List<Long> sysUidList) {
        log.info("existUnfinishedRecord size: {}", sysUidList == null ? null : sysUidList.size());
        return service.existUnfinishedRecord(sysUidList);
    }

    @Operation(summary = "变更巡检人")
    @PostMapping(value = "/trans")
    public ObjectResponse<SiteInspectionRecordPo> transInspection(
        @RequestParam("recId") Long recId, @RequestParam("opUid") Long opUid) {
        log.debug("变更巡检人: recId = {}, opUid = {}", recId, opUid);
        return service.transInspection(recId, opUid);
    }

    /**
     * 保存巡检单
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/save")
    public BaseResponse save(@RequestBody SiteInspectionRecordPo req) {
        log.info("save req: {}", req);
        return service.save(req);
    }

    /**
     * 提交巡检单
     *
     * @param req
     * @return
     */
    @PostMapping(value = "/report")
    public BaseResponse report(@RequestBody SiteInspectionRecordPo req) {
        log.info("report req: {}", req);
        return service.report(req);
    }


    @Operation(summary = "运维巡检工单图片数据修正(旧数据数据结构调整)")
    @GetMapping(value = "/fixImages")
    public Mono<BaseResponse> inspectionOrderFixImages() {
        log.info("运维巡检工单图片数据修正");
        return service.inspectionOrderFixImages();
    }
}
