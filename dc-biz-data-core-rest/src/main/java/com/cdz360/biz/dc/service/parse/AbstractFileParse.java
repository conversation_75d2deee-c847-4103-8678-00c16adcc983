package com.cdz360.biz.dc.service.parse;

import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ds.trading.ro.order.ds.PayBillRoDs;
import com.cdz360.biz.ds.trading.ro.order.mapper.ChargerOrderRoMapper;
import com.cdz360.biz.ds.trading.rw.bill.ds.ZftThirdOrderHlhtBiRwDs;
import com.cdz360.biz.ds.trading.rw.bill.ds.ZftThirdOrderRwDs;
import com.cdz360.biz.ds.trading.rw.order.ds.PayBillRwDs;
import com.cdz360.biz.model.trading.bill.po.ZftThirdOrderHlhtBiPo;
import com.cdz360.biz.model.trading.bill.po.ZftThirdOrderPo;
import com.cdz360.biz.model.trading.bill.type.DailyBillCheckResult;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.cdz360.biz.model.trading.order.po.PayBillPo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderSimpleFeeVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.List;

@Slf4j
@Component
public abstract class AbstractFileParse implements FileParse {
    @Autowired
    protected ZftThirdOrderRwDs zftThirdOrderRwDs;

    @Autowired
    protected ZftThirdOrderHlhtBiRwDs zftThirdOrderHlhtBiRwDs;

    @Autowired
    protected ChargerOrderRoMapper chargerOrderRoMapper;

    @Autowired
    private PayBillRoDs payBillRoDs;

    @Autowired
    private PayBillRwDs payBillRwDs;

    // 对账
    protected void billCheck(ZftThirdOrderPo thirdOrderPo, Long dailyBillId) {
        PayBillPo billPo = null;
        String platformNo = thirdOrderPo.getPlatformNo();
        if (DepositFlowType.IN_FLOW.equals(thirdOrderPo.getTradeType())) {
            billPo = payBillRoDs.findByOrderId(platformNo, false);
        } else {
            billPo = payBillRoDs.findByOutRefundNo(platformNo);
        }

        if (null == billPo) {
            thirdOrderPo.setCheckResult(DailyBillCheckResult.NO_NOT_MATCH);
        } else {
            if (DecimalUtils.eq(billPo.getAmount(), thirdOrderPo.getTradeAmount())) {
                thirdOrderPo.setCheckResult(DailyBillCheckResult.FULL_MATCH);
                billPo.setCheckResult(DailyBillCheckResult.FULL_MATCH);
            } else {
                thirdOrderPo.setCheckResult(DailyBillCheckResult.AMOUNT_NOT_MATCH);
                billPo.setCheckResult(DailyBillCheckResult.AMOUNT_NOT_MATCH);
            }
            billPo.setDailyBillId(dailyBillId);
            payBillRwDs.update(billPo);
        }
    }

    // 对账
    protected void billRegexCheck(List<ZftThirdOrderPo> thirdOrderPoList){
        thirdOrderPoList.forEach(zftThirdOrderPo -> {
            String openOrderId = zftThirdOrderPo.getOpenOrderId();
            if (StringUtils.isNotBlank(openOrderId)) {
                ZftThirdOrderHlhtBiPo zftThirdOrderHlhtBiPo = zftThirdOrderHlhtBiRwDs.getByOpenOrderId(openOrderId, true);
                if (null == zftThirdOrderHlhtBiPo) {
                    zftThirdOrderHlhtBiPo = new ZftThirdOrderHlhtBiPo();
                    if (zftThirdOrderPo.getTradeType() == DepositFlowType.IN_FLOW) {
                        zftThirdOrderHlhtBiPo.setZftTotalMoney(zftThirdOrderPo.getTradeAmount());
                    }
                    if (zftThirdOrderPo.getTradeType() == DepositFlowType.OUT_FLOW) {
                        zftThirdOrderHlhtBiPo.setZftTotalMoney(BigDecimal.ZERO.subtract(zftThirdOrderPo.getTradeAmount()));
                    }
                    zftThirdOrderHlhtBiPo.setOpenOrderId(openOrderId);
                    ChargerOrderSimpleFeeVo chargerOrderSimpleFeeVo = chargerOrderRoMapper.getOrderFeeByOpenOrderId(openOrderId);
                    if (null == chargerOrderSimpleFeeVo) {
                        zftThirdOrderPo.setCheckResult(DailyBillCheckResult.NO_NOT_MATCH);
                    } else {
                        if (null == chargerOrderSimpleFeeVo.getParkingFee()) {
                            chargerOrderSimpleFeeVo.setParkingFee(BigDecimal.ZERO);
                        }

                        if (zftThirdOrderHlhtBiPo.getZftTotalMoney().compareTo(chargerOrderSimpleFeeVo.getChargerOrderFee().add(chargerOrderSimpleFeeVo.getParkingFee())) == 0) {
                            zftThirdOrderHlhtBiPo.setCheckResult(DailyBillCheckResult.FULL_MATCH);
                            zftThirdOrderPo.setCheckResult(DailyBillCheckResult.FULL_MATCH);
                        } else {
                            zftThirdOrderHlhtBiPo.setCheckResult(DailyBillCheckResult.AMOUNT_NOT_MATCH);
                            zftThirdOrderPo.setCheckResult(DailyBillCheckResult.AMOUNT_NOT_MATCH);
                        }
                        zftThirdOrderHlhtBiPo.setOrderNo(chargerOrderSimpleFeeVo.getOrderNo());
                        zftThirdOrderHlhtBiPo.setOrderFee(chargerOrderSimpleFeeVo.getChargerOrderFee());
                        zftThirdOrderHlhtBiPo.setParkingFee(chargerOrderSimpleFeeVo.getParkingFee());
                        zftThirdOrderHlhtBiRwDs.insertZftThirdOrderHlhtBiBill(zftThirdOrderHlhtBiPo);
                    }
                } else {
                    // FIXME: 后续需要处理重复对账问题
                    if (zftThirdOrderPo.getTradeType() == DepositFlowType.IN_FLOW) {
                        zftThirdOrderHlhtBiPo.setZftTotalMoney(zftThirdOrderHlhtBiPo.getZftTotalMoney().add(zftThirdOrderPo.getTradeAmount()));
                    }
                    if (zftThirdOrderPo.getTradeType() == DepositFlowType.OUT_FLOW) {
                        zftThirdOrderHlhtBiPo.setZftTotalMoney(zftThirdOrderHlhtBiPo.getZftTotalMoney().subtract(zftThirdOrderPo.getTradeAmount()));
                    }
                    ChargerOrderSimpleFeeVo chargerOrderSimpleFeeVo = chargerOrderRoMapper.getOrderFeeByOpenOrderId(openOrderId);

                    if (null == chargerOrderSimpleFeeVo.getParkingFee()) {
                        chargerOrderSimpleFeeVo.setParkingFee(BigDecimal.ZERO);
                    }

                    if (zftThirdOrderHlhtBiPo.getZftTotalMoney().compareTo(chargerOrderSimpleFeeVo.getChargerOrderFee().add(chargerOrderSimpleFeeVo.getParkingFee())) == 0) {
                        zftThirdOrderHlhtBiPo.setCheckResult(DailyBillCheckResult.FULL_MATCH);
                        zftThirdOrderPo.setCheckResult(DailyBillCheckResult.FULL_MATCH);
                    } else {
                        zftThirdOrderHlhtBiPo.setCheckResult(DailyBillCheckResult.AMOUNT_NOT_MATCH);
                        zftThirdOrderPo.setCheckResult(DailyBillCheckResult.AMOUNT_NOT_MATCH);
                    }
                    zftThirdOrderHlhtBiPo.setOrderNo(chargerOrderSimpleFeeVo.getOrderNo());
                    zftThirdOrderHlhtBiPo.setOrderFee(chargerOrderSimpleFeeVo.getChargerOrderFee());
                    zftThirdOrderHlhtBiPo.setParkingFee(chargerOrderSimpleFeeVo.getParkingFee());
                    zftThirdOrderHlhtBiRwDs.updateZftThirdOrderHlhtBiBill(zftThirdOrderHlhtBiPo);
                }
            }
        });
    }
}
