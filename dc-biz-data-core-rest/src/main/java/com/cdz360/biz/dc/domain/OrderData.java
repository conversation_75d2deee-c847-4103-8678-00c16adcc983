package com.cdz360.biz.dc.domain;
//
//import lombok.Data;
//import org.springframework.data.mongodb.core.mapping.Document;
//
//import java.math.BigDecimal;
//
///**
// * 订单相关数据统计
// *
// * OrderDataVo
// * <AUTHOR>
// *
// * @since 2019.3.6
// * 世事洞明皆学问，人情练达即文章
// */
//@Document(collection = "chargerlink_car_site_order_data")
//@Data
//public class OrderData {
//    /** id */
//    private String id;
//    /** 设备运营商id */
//    private String commId;
//    /** 设备运营商名 */
//    private String commName;
//    /** 站点id */
//    private String siteId;
//    /** 站点名 */
//    private String siteName;
//    /** 站点经度 */
//    private String longitude;
//    /** 站点维度 */
//    private String latitude;
//    /** 省份编码 */
//    private Integer province;
//    /** 城市编码 */
//    private Integer city;
//    /** 区域编码 */
//    private Integer area;
//    /** 桩数量 */
//    private Integer boxCount;
//    /** 枪头数量 */
//    private Integer chargerCount;
//    /** 订单时长(秒) */
//    private Long orderDuration;
//    /** 订单电量(单位kwh) */
//    private BigDecimal orderElectricity;
//    /** 订单数量 */
//    private Long orderCount;
//    /** 订单充电金额(元) */
//    private BigDecimal orderPrice;
//    /** 订单数据日期时间戳（精确到年月日） */
//    private Long orderDataDate;
//    /** 更新日期时间戳 */
//    private Long updateTime;
//
//}
