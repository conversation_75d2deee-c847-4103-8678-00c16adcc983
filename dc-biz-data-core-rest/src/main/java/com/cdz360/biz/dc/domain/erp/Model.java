package com.cdz360.biz.dc.domain.erp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class Model<T> {
    @Schema(description = "设置自动审核: true -- 自动审核; false -- 不自动审核")
    private String IsAutoSubmitAndAudit = "true";

    @Schema(description = "具体实体")
    private T Model;

    public Model<T> setIsAutoSubmitAndAudit(Boolean b) {
        this.IsAutoSubmitAndAudit = b.toString();
        return this;
    }
}
