package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.CardBizService;
import com.cdz360.biz.model.trading.iot.param.ListWhiteCardEvseParam;
import com.cdz360.biz.model.trading.iot.po.WhiteCardEvsePo;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Tag(name = "卡相关接口", description = "卡")
public class CardRest {
    @Autowired
    private CardBizService cardBizService;

    /**
     * 查询紧急充电卡下发结果信息
     *
     * @param param
     * @return
     */
    @PostMapping("/dataCore/card/getWhiteCardEvseList")
    public ListResponse<WhiteCardEvsePo> getWhiteCardEvseList(@RequestBody ListWhiteCardEvseParam param) {
        log.debug("param = {}", JsonUtils.toJsonString(param));
        var list =  this.cardBizService.getWhiteCardEvseList(param);
        return RestUtils.buildListResponse(list);
    }

    @PostMapping("/dataCore/card/disable")
    public BaseResponse disable(@RequestBody List<String> whiteCardNoList) {
        log.debug("disable whiteCardNoList.size = {}", whiteCardNoList.size());
        return cardBizService.disable(whiteCardNoList);
    }

}
