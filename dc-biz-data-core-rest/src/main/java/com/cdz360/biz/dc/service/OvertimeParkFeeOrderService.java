package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.parse.BalanceServiceImpl;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.OrderOvertimeParkDivisionRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.OvertimeParkFeeOrderRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteOvertimeParkDivisionRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteOvertimeParkSettingRoDs;
import com.cdz360.biz.ds.trading.rw.site.ds.OvertimeParkFeeOrderRwDs;
import com.cdz360.biz.model.common.constant.Constant;
import com.cdz360.biz.model.trading.order.po.ChargerOrderPo;
import com.cdz360.biz.model.trading.site.param.ListOvertimeParkFeeOrderParam;
import com.cdz360.biz.model.trading.site.po.OrderOvertimeParkDivisionPo;
import com.cdz360.biz.model.trading.site.po.OvertimeParkFeeOrderPo;
import com.cdz360.biz.model.trading.site.po.SiteOvertimeParkDivisionPo;
import com.cdz360.biz.model.trading.site.po.SiteOvertimeParkSettingPo;
import com.cdz360.biz.model.trading.site.vo.OvertimeParkFeeOrderBi;
import com.cdz360.biz.model.trading.site.vo.OvertimeParkFeeOrderVo;
import com.cdz360.biz.model.trading.site.vo.SiteOvertimeParkDivisionVo;
import com.chargerlinkcar.framework.common.domain.OrderOvertimeParkingBi;
import com.chargerlinkcar.framework.common.domain.vo.OrderOvertimeParkInfoVo;
import com.chargerlinkcar.framework.common.feign.UserFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class OvertimeParkFeeOrderService {
    @Autowired
    private OvertimeParkFeeOrderRoDs overtimeParkFeeOrderRoDs;

    @Autowired
    private OvertimeParkFeeOrderRwDs overtimeParkFeeOrderRwDs;

    @Autowired
    private BalanceServiceImpl balanceService;

    @Autowired
    private ChargerOrderRoDs chargerOrderRoDs;

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private com.chargerlinkcar.framework.common.feign.UserFeignClient userSyncFeignClient;

    @Autowired
    private SiteOvertimeParkDivisionRoDs siteOvertimeParkDivisionRoDs;
    @Autowired
    private SiteOvertimeParkSettingRoDs siteOvertimeParkSettingRoDs;
    @Autowired
    private OrderOvertimeParkDivisionRoDs orderOvertimeParkDivisionRoDs;

    public ListResponse<OvertimeParkFeeOrderVo> findAll(ListOvertimeParkFeeOrderParam param) {
        if (param.getSize() == null || param.getSize() > 9999) {
            param.setSize(9999);
        }

        final ListResponse<OvertimeParkFeeOrderVo> all = this.overtimeParkFeeOrderRoDs.findAll(
            param);

        final List<OvertimeParkFeeOrderVo> data = all.getData();
        if(CollectionUtils.isNotEmpty(data)) {
            final List<String> hasDivisionList = orderOvertimeParkDivisionRoDs.filterByOrderNos(
                data.stream()
                    .map(OvertimeParkFeeOrderVo::getOrderNo)
                    .collect(Collectors.toList()));

            if(CollectionUtils.isEmpty(hasDivisionList)) {
                return all;
            }

            for(OvertimeParkFeeOrderVo one : data) {
                if(hasDivisionList.contains(one.getOrderNo())) {
                    one.setHasOvertimeDivision(true);
                }
            }
        }

        return all;
    }

    public OvertimeParkFeeOrderBi orderBi(ListOvertimeParkFeeOrderParam param) {
        return this.overtimeParkFeeOrderRoDs.orderBi(param);
    }

    public OvertimeParkFeeOrderVo getOverTimeParkFeeByOrderNo(String orderNo) {
        //结算信息
        OvertimeParkFeeOrderVo res = this.overtimeParkFeeOrderRoDs.getOverTimeParkFeeByOrderNo(orderNo);
        if(res != null) {
            ChargerOrderPo order = chargerOrderRoDs.getChargeOrderPo(orderNo, false);
            if(order != null) {
                res.setPayAccountName(balanceService.getAccountName(order.getDefaultPayType(), order.getPayAccountId()));
                res.setDefaultPayType(order.getDefaultPayType());
            }

            // 是否有占位分时订单
            final List<OrderOvertimeParkDivisionPo> parkDivisionRoDsByOrderNo =
                orderOvertimeParkDivisionRoDs.getByOrderNo(orderNo);
            if(CollectionUtils.isNotEmpty(parkDivisionRoDsByOrderNo)) {
                res.setHasOvertimeDivision(true);
            }
        }
        log.debug("res: {}", res);
        return res;
    }

    /**
     * 获取占位订单分时信息
     * @param orderNo
     * @return
     */
    public OrderOvertimeParkInfoVo getOverTimeParkDivisionByOrderNo(String orderNo) {

        final List<OrderOvertimeParkDivisionPo> parkDivisionRoDsByOrderNo =
            orderOvertimeParkDivisionRoDs.getByOrderNo(orderNo);

        if(CollectionUtils.isEmpty(parkDivisionRoDsByOrderNo)) {
            log.debug("无占位分时信息");
            return null;
        }

        final OrderOvertimeParkDivisionPo orderOvertimeParkDivisionPo = parkDivisionRoDsByOrderNo.get(
            0);

        final SiteOvertimeParkDivisionPo siteDivisionPo = siteOvertimeParkDivisionRoDs.getById(
            orderOvertimeParkDivisionPo.getDivisionId());

//        final List<Long> divisionIds = parkDivisionRoDsByOrderNo.stream()
//            .map(OrderOvertimeParkDivisionPo::getDivisionId)
//            .collect(Collectors.toList());

        final List<SiteOvertimeParkDivisionPo> byIdList = siteOvertimeParkDivisionRoDs
            .getBySettingId(siteDivisionPo.getSettingId(), null);

        final Map<Long, SiteOvertimeParkDivisionPo> divisionMap = byIdList.stream()
            .collect(Collectors.toMap(SiteOvertimeParkDivisionPo::getId, o -> o, (n, o) -> o));

        // 数据库中的条目
        final List<SiteOvertimeParkDivisionVo> res = parkDivisionRoDsByOrderNo.stream()
            .map(e -> {
                final SiteOvertimeParkDivisionPo siteOvertimeParkDivisionPo =
                    divisionMap.get(e.getDivisionId());
                SiteOvertimeParkDivisionVo ret = new SiteOvertimeParkDivisionVo();
                if (siteOvertimeParkDivisionPo != null) {

                    BeanUtils.copyProperties(siteOvertimeParkDivisionPo, ret);
                }
                return ret.setDivisionFee(e.getFee())
                    .setDuration(e.getDuration());
            })
            .collect(Collectors.toList());
        log.debug("res: {}", res);
//        return res;

        OrderOvertimeParkInfoVo retObj = new OrderOvertimeParkInfoVo();

        // 分组后累加
        final Map<Long, List<SiteOvertimeParkDivisionVo>> overtimeParkGrouping = res.stream()
            .collect(Collectors.groupingBy(SiteOvertimeParkDivisionVo::getId));
        final List<SiteOvertimeParkDivisionVo> resSum = byIdList.stream()
            .map(e -> {
                SiteOvertimeParkDivisionVo ret = new SiteOvertimeParkDivisionVo();
                BeanUtils.copyProperties(e, ret);
                final List<SiteOvertimeParkDivisionVo> divisionVos = overtimeParkGrouping.get(
                    e.getId());

                if(CollectionUtils.isEmpty(divisionVos)) {
                    return ret.setDivisionFee(BigDecimal.ZERO)
                        .setDuration(0L);
                }

                final BigDecimal feeSum = divisionVos.stream()
                    .map(SiteOvertimeParkDivisionVo::getDivisionFee)
                    .reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);

                final long duration = divisionVos.stream()
                    .mapToLong(SiteOvertimeParkDivisionVo::getDuration)
                    .sum();

                return ret.setDivisionFee(feeSum)
                    .setDuration(duration);
            })
            .collect(Collectors.toList());

        retObj.setDivision(resSum);

        final SiteOvertimeParkSettingPo byId = siteOvertimeParkSettingRoDs.getById(
            siteDivisionPo.getSettingId());
        if(byId != null) {
            retObj.setMaxFee(byId.getMaxFee());
        }

        log.debug("resSum: {}", retObj);
        return retObj;
    }

    @Transactional
    public ObjectResponse<Integer> cancel(ListOvertimeParkFeeOrderParam param) {
        final OvertimeParkFeeOrderPo byOrderNo = overtimeParkFeeOrderRwDs.getByOrderNo(
            param.getOrderNo(), true);
        IotAssert.isNotNull(byOrderNo, "找不到停充订单");

        final ChargerOrderPo chargerOrder =
            chargerOrderRoDs.getChargeOrderPo2(param.getOrderNo(), false);
        IotAssert.isNotNull(chargerOrder, "找不到充电订单");

        IotAssert.isTrue(!Constant.OVERTIME_PARK_ORDER_CANCEL_STATUS_100
            .equals(byOrderNo.getCancelStatus()), "订单当前已取消");

        byOrderNo.setCancelStatus(Constant.OVERTIME_PARK_ORDER_CANCEL_STATUS_100);
        overtimeParkFeeOrderRwDs.updateOvertimeParkFeeOrder(byOrderNo);

        // 取消订单时需要更新黑名单表的停充状态
        OrderOvertimeParkingBi paramBi = new OrderOvertimeParkingBi();
        paramBi.setLatestOrderNo(param.getOrderNo())
            .setOvertimeOrderStatus(Constant.OVERTIME_PARK_ORDER_STATUS_202);
        ObjectResponse<Boolean> booleanObjectResponse = userSyncFeignClient.updateLatestOrder(
            paramBi);
        FeignResponseValidate.check(booleanObjectResponse);

        // 取消后减少超时次数：1次
        OrderOvertimeParkingBi decParam = new OrderOvertimeParkingBi();
        decParam.setSiteId(byOrderNo.getSiteId())
            .setCorpId(chargerOrder.getCorpId() == null ? 0 : chargerOrder.getCorpId())
            .setUid(byOrderNo.getUid())
            .setCommId(0L);
        final ObjectResponse<Boolean> res = userFeignClient.decreaseNum(decParam);
        FeignResponseValidate.check(res);

        return RestUtils.buildObjectResponse(0);
    }
}
