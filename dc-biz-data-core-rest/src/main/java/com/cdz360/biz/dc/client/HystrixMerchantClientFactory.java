package com.cdz360.biz.dc.client;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.chargerlinkcar.core.domain.Commercial;
import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class HystrixMerchantClientFactory implements FallbackFactory<MerchantFeignClient> {

    @Override
    public MerchantFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER, throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER, throwable.getStackTrace());

        return new MerchantFeignClient() {
//            @Override
//            public ListResponse<Long> getCommIdListByCommId(Long commId) {
//                return RestUtils.serverBusy4ListResponse();
//            }

            @Override
            public ObjectResponse<Commercial> getCommercialByToken(String token) {
                return RestUtils.serverBusy4ObjectResponse();
            }
        };
    }
}
