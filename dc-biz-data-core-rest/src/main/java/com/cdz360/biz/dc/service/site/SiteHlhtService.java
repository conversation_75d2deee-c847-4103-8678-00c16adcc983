package com.cdz360.biz.dc.service.site;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.charge.type.HlhtType;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ds.trading.ro.geo.ds.GeoRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ds.trading.rw.partner.ds.SiteHlhtRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteRwDs;
import com.cdz360.biz.model.geo.po.DistrictPo;
import com.cdz360.biz.model.site.type.SiteStatus;
import com.cdz360.biz.model.trading.hlht.param.BindHlhtParam;
import com.cdz360.biz.model.trading.hlht.param.HlhtSiteParam;
import com.cdz360.biz.model.trading.hlht.po.PartnerSitePo;
import com.cdz360.biz.model.trading.hlht.po.SiteHlhtPo;
import com.cdz360.biz.model.trading.hlht.vo.HlhtSiteVo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.site.type.SiteCategory;
import com.cdz360.biz.utils.feign.hlht.OpenHlhtFeignClient;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.feign.CommercialFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IDUtil;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.time.Duration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SiteHlhtService {

    @Autowired
    private OpenHlhtFeignClient openHlhtFeignClient;
    @Autowired
    private CommercialFeignClient commercialFeignClient;
    @Autowired
    private SiteHlhtRwDs siteHlhtRwDs;
    @Autowired
    private SiteRwDs siteRwDs;
    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private GeoRoDs geoRoDs;

    @Autowired
    private SiteBizService siteBizService;

    @Transactional
    public BaseResponse bindHlhtSite(BindHlhtParam param) {
        //STEP1 校验入参
        HlhtSiteParam req = new HlhtSiteParam();
        req.setHlhtSiteId(param.getHlhtSiteId());
        ListResponse<HlhtSiteVo> response = openHlhtFeignClient.hlhtSiteList(req)
            .block(Duration.ofSeconds(50L));
        FeignResponseValidate.check(response);
        IotAssert.isTrue(response.getData().size() == param.getHlhtSiteId().size(), "未找到场站信息");
        ObjectResponse<Commercial> commercialObjectResponse = commercialFeignClient.getCommercial(param.getCommId());
        FeignResponseValidate.check(commercialObjectResponse);
        Commercial comm = commercialObjectResponse.getData();

        SitePo reqPo = new SitePo();
        reqPo.setTopCommId(comm.getTopCommId());
        long count = siteRoDs.countByCondition(reqPo,
                response.getData().stream().map(HlhtSiteVo::getStationId).collect(Collectors.toList()));
        IotAssert.isTrue(count == 0, "存在站点已经新增到当前一级商户下");


        List<SitePo> sitePoList = new ArrayList<>();
        List<SiteHlhtPo> siteHlhtPoList = new ArrayList<>();
        List<PartnerSitePo> sitePos = new ArrayList<>();

        response.getData().forEach(e -> {

            //STEP2 新增d_charger.t_site记录
            String siteId = IDUtil.generateSiteId();// 站点ID
            SitePo entity = new SitePo();
            entity.setId(siteId)
                    .setIdNo(entity.getId())
                    .setSiteName(e.getName())
                    .setBizType(3)
                    .setType(1) // 默认是公共类型(约定的)
                    .setCategory(List.of(SiteCategory.CE))
                    .setAddress(e.getAddress())
                    .setStatus(this.toDcSiteStatus(e.getStatus()))
                    .setTopCommId(comm.getTopCommId())
                    .setOperateId(param.getCommId())
                    .setOperateName(comm.getCommName())
                    .setOpenSiteId(e.getStationId())
                    .setPartnerCode(e.getPartnerCode());
            if (StringUtils.isNotBlank(e.getAreaCode())) {
                entity.setArea(Integer.valueOf(e.getAreaCode().trim()));
            }

            // 地址信息
            DistrictPo district = geoRoDs.getDistrict(e.getAreaCode());
            entity.setCity(Integer.valueOf(district.getCityCode()))
                    .setProvince(Integer.valueOf(district.getProvinceCode()));

            sitePoList.add(entity);


            //STEP3 建立商户与互联场站关系
            SiteHlhtPo po = new SiteHlhtPo();
            po.setTopCommId(comm.getTopCommId())
                    .setCommId(param.getCommId())
                    .setSiteId(siteId)
                    .setPartnerCode(e.getPartnerCode())
                    .setPartnerName(e.getPartnerName())
                    .setEvseOwnerCode(e.getEvseOwnerCode())
                    .setEvseOwnerName(e.getEvseOwnerName());
            siteHlhtPoList.add(po);


            //STEP4 d_open.t_partner_site建立绑定关系
            PartnerSitePo sitePo = new PartnerSitePo();
            sitePo.setPid(e.getPartnerId())
                    .setPartnerCode(e.getPartnerCode())
                    .setTopCommId(comm.getTopCommId())
                    .setCommId(param.getCommId())
                    .setHlhtType(HlhtType.REVERSE_HLHT)
                    .setSiteCommId(param.getCommId())
                    .setSiteId(siteId)
                    .setPSiteId(e.getStationId())
                    .setCityCode(e.getAreaCode());
            sitePos.add(sitePo);

        });

        siteRwDs.addSiteList(sitePoList);

        siteHlhtRwDs.add(siteHlhtPoList);

        Mono<BaseResponse> res = openHlhtFeignClient.addPartnerSite(sitePos);
        FeignResponseValidate.check(res.block(Duration.ofSeconds(50L)));

        return RestUtils.success();
    }

    public int toDcSiteStatus(Integer status) {
        if (status == null) {
            return 3;   // 维护中
        } else if (50 == status) {
            return 2;   // 上线中
        } else {
            return 3;   // 维护中
        }
    }

    @Transactional
    public BaseResponse unbindHlhtSite(BindHlhtParam param) {
        //STEP1 校验入参
        HlhtSiteParam req = new HlhtSiteParam();
        req.setCommId(param.getCommId())
                .setHlhtSiteId(param.getHlhtSiteId());
        ListResponse<HlhtSiteVo> response = openHlhtFeignClient.hlhtSiteList(req)
            .block(Duration.ofSeconds(50L));
        FeignResponseValidate.check(response);
        IotAssert.isTrue(response.getData().size() == param.getHlhtSiteId().size(), "未找到场站信息");
        ObjectResponse<Commercial> commercialObjectResponse = commercialFeignClient.getCommercial(param.getCommId());
        FeignResponseValidate.check(commercialObjectResponse);
        Commercial comm = commercialObjectResponse.getData();

        List<String> openSiteIdList = response.getData().stream().map(HlhtSiteVo::getStationId).collect(Collectors.toList());
        List<SitePo> sitePoList = siteRoDs.findByCondition(comm.getTopCommId(), param.getCommId(),
                List.of(SiteStatus.OPENING.getCode(), SiteStatus.ONLINE.getCode(), SiteStatus.UNAVAILABLE.getCode()), null, openSiteIdList);
        IotAssert.isTrue(param.getHlhtSiteId().size() == sitePoList.size(), "未找到场站信息");


        List<String> siteIdList = sitePoList.stream().map(SitePo::getId).collect(Collectors.toList());
        //STEP2 修改t_site状态
        siteRwDs.updateStatus(SiteStatus.UNKNOWN.getCode(), siteIdList);
        // 更新 mongo 上场站的状态
        CompletableFuture<Integer> future = CompletableFuture.supplyAsync(() -> this.updateMongoSiteInfo(sitePoList));

        //STEP3 修改t_site_hlht状态
        siteHlhtRwDs.disable(comm.getTopCommId(), param.getCommId(), siteIdList);

        //STEP4 t_partner_site批量disable
        Mono<BaseResponse> res = openHlhtFeignClient.disablePartnerSite(comm.getTopCommId(), siteIdList);
        FeignResponseValidate.check(res.block(Duration.ofSeconds(50L)));

        try {
            future.get(10, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("更新mongo中的场站状态异常，需要手动调整: siteIdList = {}, err = {}",
                    siteIdList, e.getMessage(), e);
        }

        return RestUtils.success();
    }

    private Integer updateMongoSiteInfo(List<SitePo> sitePoList) {
        sitePoList.stream().map(po -> po.setStatus(SiteStatus.UNKNOWN.getCode()))
                .forEach(siteBizService::updateMongoSite);
        return sitePoList.size();
    }
}
