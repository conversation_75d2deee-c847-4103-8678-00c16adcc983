package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.yw.YwOrderService;
import com.cdz360.biz.model.sys.vo.AccRelativeOrderVo;
import com.cdz360.biz.model.trading.yw.param.CreateYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.CusRecNoticeParam;
import com.cdz360.biz.model.trading.yw.param.ListYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.SolvedYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.TransYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.UpdateYwOrderParam;
import com.cdz360.biz.model.trading.yw.param.UpdateYwOrderStatusParam;
import com.cdz360.biz.model.trading.yw.po.YwOrderPo;
import com.cdz360.biz.model.trading.yw.vo.YwOrderBi;
import com.cdz360.biz.model.trading.yw.vo.YwOrderLogVo;
import com.cdz360.biz.model.trading.yw.vo.YwOrderVo;
import com.cdz360.biz.model.yw.param.BatchCreateYwParams;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "运维工单相关操作接口", description = "运维工单相关操作接口")
@Slf4j
@RestController
public class YwOrderRest {

    @Autowired
    private YwOrderService ywOrderService;

    @Operation(summary = "获取场站最近一条运维工单记录")
    @GetMapping(value = "/dataCore/ywOrder/getSiteLatestRec")
    public Mono<ObjectResponse<YwOrderVo>> getSiteLatestRec(
        @Parameter(name = "场站ID", required = true)
        @RequestParam(value = "siteId") String siteId) {
        log.debug("获取场站最近一条运维工单记录: siteId = {}", siteId);
        return ywOrderService.getSiteLatestRec(siteId);
    }

    @Operation(summary = "更新运维工单标签")
    @PostMapping(value = "/dataCore/ywOrder/updateTag")
    public Mono<ObjectResponse<YwOrderVo>> updateYwOrderTag(@RequestBody UpdateYwOrderParam param) {
        log.debug("更新运维工单标签: param = {}", JsonUtils.toJsonString(param));
        return ywOrderService.updateYwOrderTag(param)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "获取运维工单列表")
    @PostMapping(value = "/dataCore/ywOrder/findAll")
    public Mono<ListResponse<YwOrderVo>> findYwOrder(@RequestBody ListYwOrderParam param) {
        log.debug("获取运维工单列表: param = {}", JsonUtils.toJsonString(param));
        return ywOrderService.findYwOrder(param);
    }

    @Operation(summary = "统计运维人员运维数据")
    @PostMapping(value = "/dataCore/ywOrder/ywOrderBi")
    public Mono<ListResponse<YwOrderBi>> ywOrderBi(@RequestBody ListYwOrderParam param) {
        log.debug("统计运维人员运维数据: param = {}", JsonUtils.toJsonString(param));
        return ywOrderService.ywOrderBi(param)
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "获取运维工单详情")
    @GetMapping(value = "/dataCore/ywOrder/getYwOrderDetail")
    public Mono<ObjectResponse<YwOrderVo>> getYwOrderDetail(
        @Parameter(name = "运维工单编号", required = true) @RequestParam(value = "ywOrderNo") String ywOrderNo) {
        log.debug("获取运维工单详情: ywOrderNo = {}", ywOrderNo);
        return ywOrderService.getYwOrderDetail(ywOrderNo)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "创建运维工单")
    @PostMapping(value = "/dataCore/ywOrder/createOrder")
    public Mono<ObjectResponse<YwOrderPo>> createYwOrder(@RequestBody CreateYwOrderParam param) {
        log.debug("创建运维工单: {}", JsonUtils.toJsonString(param));
        return ywOrderService.createYwOrder(param)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "检查是否有未结束的运维单")
    @PostMapping(value = "/dataCore/ywOrder/existUnfinishedOrder")
    public ObjectResponse<Boolean> existUnfinishedOrder(@RequestBody List<Long> sysUidList) {
        log.info("existUnfinishedOrder size: {}", sysUidList == null ? null : sysUidList.size());
        return ywOrderService.existUnfinishedOrder(sysUidList);
    }

    @Operation(summary = "运维工单转派")
    @PostMapping(value = "/dataCore/ywOrder/trans")
    public Mono<ObjectResponse<YwOrderVo>> transYwOrder(
        ServerHttpRequest request, @RequestBody TransYwOrderParam param) {
        log.debug("运维工单转派: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        return ywOrderService.transYwOrder(param)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "运维工单状态更新")
    @PostMapping(value = "/dataCore/ywOrder/updateStatus")
    public Mono<BaseResponse> updateOrderStatus(@RequestBody UpdateYwOrderStatusParam param) {
        log.info("运维工单状态更新: param = {}", JsonUtils.toJsonString(param));
        return ywOrderService.updateOrderStatus(param);
    }

//    @Operation(summary = "运维工单接单")
//    @GetMapping(value = "/dataCore/ywOrder/received")
//    public Mono<BaseResponse> receivedYwOrder(
//            ServerHttpRequest request,
//            @Parameter(name = "运维工单编号") @RequestParam(value = "ywOrderNo") String ywOrderNo) {
//        log.debug("运维工单接单: {}", LoggerHelper2.formatEnterLog(request));
//        return ywOrderService.receivedYwOrder(ywOrderNo);
//    }
//
//    @Operation(summary = "运维工单开始")
//    @GetMapping(value = "/dataCore/ywOrder/start")
//    public Mono<BaseResponse> startYwOrder(
//            ServerHttpRequest request,
//            @Parameter(name = "运维工单编号") @RequestParam(value = "ywOrderNo") String ywOrderNo) {
//        log.debug("运维工单开始: {}", LoggerHelper2.formatEnterLog(request));
//        return ywOrderService.startYwOrder(ywOrderNo);
//    }

    @Operation(summary = "运维工单保存操作")
    @PostMapping(value = "/dataCore/ywOrder/save")
    public Mono<ObjectResponse<YwOrderPo>> saveYwOrder(
        ServerHttpRequest request, @RequestBody SolvedYwOrderParam param) {
        log.debug("运维工单保存操作: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        return ywOrderService.saveYwOrder(param)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "运维工单解决提交操作")
    @PostMapping(value = "/dataCore/ywOrder/solved")
    public Mono<ObjectResponse<YwOrderPo>> solvedYwOrder(
        ServerHttpRequest request, @RequestBody SolvedYwOrderParam param) {
        log.debug("运维工单解决提交操作: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        return ywOrderService.solvedYwOrder(param)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "获取运维工单转派记录")
    @PostMapping(value = "/dataCore/ywOrder/transList")
    public Mono<ListResponse<YwOrderLogVo>> ywOrderTransList(
        ServerHttpRequest request,
        @Parameter(name = "运维工单编号", required = true) @RequestParam("ywOrderNo") String ywOrderNo) {
        log.debug("获取运维工单转派记录: ywOrderNo = {}", ywOrderNo);
        return ywOrderService.ywOrderTransList(ywOrderNo)
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "通过运维人员获取已处理工单数")
    @PostMapping(value = "/dataCore/ywOrder/getOrderNum")
    public Mono<ListResponse<AccRelativeOrderVo>> getOrderNum(ServerHttpRequest request,
        @RequestBody List<Long> sysUidList) {
        log.info("通过运维人员获取已处理工单数: sysUidList = {}", sysUidList);
        return ywOrderService.getOrderNum(sysUidList)
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "recNoticeFeedback")
    @GetMapping(value = "/dataCore/ywOrder/recNoticeFeedback")
    public Mono<ObjectResponse<Integer>> recNoticeFeedback(ServerHttpRequest request,
        @RequestBody CusRecNoticeParam param) {
        log.debug("通过运维人员获取已处理工单数: param = {}", JsonUtils.toJsonString(param));
        return ywOrderService.recNoticeFeedback(param)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "运维工单图片数据修正(旧数据数据结构调整)")
    @GetMapping(value = "/dataCore/ywOrder/fixImages")
    public Mono<BaseResponse> ywOrderFixImages() {
        log.info("运维工单图片数据修正");
        return ywOrderService.ywOrderFixImages();
    }


    @Operation(summary = "任务批量创建运维工单")
    @PostMapping(value = "/dataCore/ywOrder/batchCreateYwOrder")
    public void batchCreateYwOrder(@RequestBody BatchCreateYwParams params) {
        log.info("定时任务，批量创建运维工单,params={}",JsonUtils.toJsonString(params));
        if (CollectionUtils.isNotEmpty(params.getGidList()) && CollectionUtils.isNotEmpty(
            params.getIgnoreWarningCodeList()) && params.getWarningDuration() != null) {
             ywOrderService.batchCreateYwOrder(params);
        }
    }




//    @Operation(summary = "运维工单图片数据修正(旧数据数据结构调整)")
//    @GetMapping(value = "/dataCore/ywOrder/fixFaultImages")
//    public Mono<BaseResponse> ywOrderFixFaultImages() {
//        log.info("运维工单图片数据修正,faultImages");
//        return ywOrderService.ywOrderFixFaultImages();
//    }
}
