package com.cdz360.biz.dc.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.apache.commons.pool2.BaseObject;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "ERP场站数据推送方式信息")
public class SendSiteBiParamParse extends BaseObject {
    // 是否是失败重传
    @Schema(description = "" +
            "推送失败重传: true -- 是; false -- 否;" +
            " null 表示可以指定场站指定时间推送，与 t_bi_erp_site 中数据无关")
    private Boolean failResend = Boolean.FALSE;

//    @Schema(description = "指定重传数据的场站ID列表")
//    private List<SitePo> siteIdList = new ArrayList<>();

//    @Schema(description = "指定重传的数据的年份")
//    private Integer year;
//
//    @Schema(description = "指定重传的数据的月份")
//    private Integer month;

    @Schema(description = "指定重传数据的场站ID列表")
    private List<String> siteIdList = new ArrayList<>();

    @Schema(description = "收入时间")
    private LocalDate date;

    /**
     * 转换成收入时间
     *
     * @return
     */
    public LocalDateTime getIncomeTime() {
        LocalTime time = LocalTime.parse("00:00:00");
        return date.atTime(time);
    }
}
