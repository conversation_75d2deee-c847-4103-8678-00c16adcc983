package com.cdz360.biz.dc.client;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.dc.domain.WWarningRecordInMongo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Date;

/**
 * DeviceMonitorFeignClient
 *
 * @since 3/29/2020 9:42 PM
 * <AUTHOR>
 */
@FeignClient(DcConstants.KEY_FEIGN_IOT_MONITOR)
@Component
public interface DeviceMonitorFeignClient {
    @PostMapping(value = "/api/alarm/evseErrorAlarm")
    ListResponse<WWarningRecordInMongo> evseErrorAlarm(@RequestParam("startTime")
                                                       @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                       @RequestParam("endTime")
                                                       @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                       @RequestParam(name = "plugId", required = false) Integer plugId,
                                                       @RequestParam(name = "evseNo", required = false) String evseNo,
                                                       @RequestParam("siteId") String siteId);
}