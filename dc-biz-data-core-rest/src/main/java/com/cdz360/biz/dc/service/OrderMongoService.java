package com.cdz360.biz.dc.service;

import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.domain.OrderInMongo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

/**
 * DeviceStatusMongoService
 *
 * @since 2/11/2020 2:06 PM
 * <AUTHOR>
 */
@Slf4j
@Service
public class OrderMongoService {
    @Autowired
    private MongoTemplate template;

    public OrderInMongo findOne(String orderNo) {
        log.debug(">> 从mongodb中获取订单数据: orderNo={}", orderNo);

        // 查询条件
        Criteria criteria = Criteria.where("orderNo").is(orderNo);
//        log.info("从mongodb中获取订单数据的查询条件: {}", JsonUtils.toJsonString(criteria));

        OrderInMongo order = template.findOne(new Query(criteria), OrderInMongo.class);
        if (order == null) {
            log.info("数据不存在. orderNo = {}", orderNo);
        } else {
            log.debug("<< 查询结果: order orderNo = {}, orderStatus = {}", order.getOrderNo(), order.getOrderStatus());
        }
        return order;
    }


    public void deleteOrder(String orderNo) {
        log.info("删除Mongo订单 orderNo = {}", orderNo);
        if (StringUtils.isBlank(orderNo)) {
            return;
        }
        Criteria criteria = Criteria.where("orderNo").is(orderNo);
        template.remove(new Query(criteria), OrderInMongo.class);
    }
}