package com.cdz360.biz.dc.service.bill;

import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.service.parse.WxFileParse;
import com.cdz360.biz.model.trading.bill.param.NotifyDailyBillParam;
import com.cdz360.biz.model.trading.bill.po.ZftDailyBillPo;
import com.cdz360.biz.model.trading.bill.type.DailyBillDownloadResult;
import com.cdz360.biz.model.trading.bill.type.DailyBillStatus;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.PostConstruct;

@Slf4j
@Component
public class WxDailyBillDealStrategy extends AbstractDailyBillDealStrategy {
    @Autowired
    private DailyBillDealService dailyBillDealService;

    @Autowired
    private WxFileParse wxFileParse;

    @PostConstruct
    public void init() {
        this.dailyBillDealService.addStrategy(PayChannel.WXPAY, this);
    }

    @Transactional
    @Override
    public void execute(ZftDailyBillPo po, NotifyDailyBillParam param) {
        if (DailyBillDownloadResult.FAIL.equals(param.getResult())) {
            po.setStatus(DailyBillStatus.LOAD_FAIL);
            zftDailyBillRwDs.updateZftDailyBill(po);
            log.error("账单下载失败: msg = {}", param.getFailMsg());
            return;
        }

        po.setStatus(DailyBillStatus.CHECKING)
                .setDownloadUrl(param.getDownloadUrl());
        zftDailyBillRwDs.updateZftDailyBill(po);

        try {
            if (StringUtils.isNotBlank(param.getDownloadUrl())) {
                // 下载对账
                String localPath = "/tmp/" + po.getName() + ".csv";
                super.downloadFile(po.getDownloadUrl(), localPath);

                // 读取文件内容进行对账
                wxFileParse.parseFile(localPath, po.getId());
            }

            // 没有对上的充值记录(t_pay_bill)
            super.checkPayBill(po);

            po.setStatus(DailyBillStatus.CHECK_SUCCESS);
        } catch (Exception e) {
            po.setStatus(DailyBillStatus.CHECK_FAIL);
        }

        zftDailyBillRwDs.updateZftDailyBill(po);
    }
}
