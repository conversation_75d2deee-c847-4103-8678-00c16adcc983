package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.prerun.PrerunService;
import com.cdz360.biz.model.trading.prerun.param.PrerunCheckParam;
import com.cdz360.biz.model.trading.prerun.dto.PrerunMixDto;
import com.cdz360.biz.model.trading.prerun.param.PrerunSearchParam;
import com.cdz360.biz.model.trading.prerun.vo.PrerunVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

/**
 * PrerunRest
 * 
 * @since 6/22/2022 8:34 AM
 * <AUTHOR>
 */
@Tag(name = "开通调试工单相关操作接口", description = "开通调试工单相关操作接口")
@Slf4j
@RestController
public class PrerunRest {
    @Autowired
    private PrerunService prerunService;

//    @Operation(summary = "创建(修改)开通调试工单")
//    @PostMapping(value = "/dataCore/prerun/openPrerun")
//    public Mono<ObjectResponse<PrerunMixDto>> openPrerun(@RequestBody PrerunMixDto param) {
//        log.info("创建开通调试工单: {}", JsonUtils.toJsonString(param));
//        return prerunService.openPrerun(param)
//            .map(RestUtils::buildObjectResponse);
//    }

    @Operation(summary = "创建开通调试工单")
    @PostMapping(value = "/dataCore/prerun/createPrerun")
    public Mono<ObjectResponse<PrerunMixDto>> createPrerun(@RequestBody PrerunMixDto param) {
        log.info("创建开通调试工单: {}", JsonUtils.toJsonString(param));
        return prerunService.createPrerun(param)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "修改开通调试工单")
    @PostMapping(value = "/dataCore/prerun/updatePrerun")
    public Mono<ObjectResponse<PrerunMixDto>> updatePrerun(@RequestBody PrerunMixDto param) {
        log.info("修改开通调试工单: {}", JsonUtils.toJsonString(param));
        return prerunService.updatePrerun(param)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "提交开通调试工单")
    @PostMapping(value = "/dataCore/prerun/submitPrerun")
    public Mono<ObjectResponse<PrerunVo>> submitPrerun(@RequestBody PrerunCheckParam param) {
        log.info("提交开通调试工单: {}", JsonUtils.toJsonString(param));
        return prerunService.submitPrerun(param)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "查询开通调试工单")
    @PostMapping(value = "/dataCore/prerun/searchPrerunList")
    public Mono<ListResponse<PrerunMixDto>> searchPrerunList(@RequestBody PrerunSearchParam param) {
        log.info("查询开通调试工单: {}", JsonUtils.toJsonString(param));
        return prerunService.searchPrerunList(param);
    }

    @Operation(summary = "质检调试工单")
    @PostMapping(value = "/dataCore/prerun/checkPrerun")
    public Mono<ObjectResponse<Boolean>> checkPrerun(@RequestBody PrerunCheckParam param) {
        log.info("质检调试工单: {}", JsonUtils.toJsonString(param));
        return prerunService.checkPrerun(param);
    }

    @Operation(summary = "取消调试工单")
    @PostMapping(value = "/dataCore/prerun/cancelPrerun")
    public Mono<ObjectResponse<Boolean>> cancelPrerun(@RequestBody PrerunCheckParam param) {
        log.info("取消调试工单: {}", JsonUtils.toJsonString(param));
        return prerunService.cancelPrerun(param);
    }

    @Operation(summary = "删除调试工单")
    @PostMapping(value = "/dataCore/prerun/deletePrerun")
    public Mono<ObjectResponse<Boolean>> deletePrerun(@RequestBody PrerunCheckParam param) {
        log.info("删除调试工单: {}", JsonUtils.toJsonString(param));
        return prerunService.deletePrerun(param);
    }

    @Operation(summary = "查找场站最近一个有效工单")
    @GetMapping(value = "/dataCore/prerun/getLatestPrerun")
    public Mono<ObjectResponse<PrerunMixDto>> getLatestPrerun(
        @RequestParam(value = "siteId", required = false) String siteId,
        @RequestParam(value = "prerunId", required = false) Long prerunId) {
        log.info("查找场站最近一个有效工单: {}, {}", siteId, prerunId);
        return prerunService.getLatestPrerun(siteId, prerunId);
    }
}