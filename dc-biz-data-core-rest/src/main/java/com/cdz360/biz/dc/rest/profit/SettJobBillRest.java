package com.cdz360.biz.dc.rest.profit;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.profit.SettJobBillService;
import com.cdz360.biz.model.trading.profit.sett.param.ListSettJobBillParam;
import com.cdz360.biz.model.trading.profit.sett.vo.SettJobBillDetailVo;
import com.cdz360.biz.model.trading.profit.sett.vo.SettJobBillVo;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/dataCore/settJobBill")
@Tag(name = "结算单相关操作接口", description = "结算单相关操作接口")
public class SettJobBillRest {

    @Autowired
    private SettJobBillService settJobBillService;

    @Operation(summary = "获取结算单")
    @DeleteMapping(value = "/getBill")
    public Mono<ObjectResponse<SettJobBillDetailVo>> getSettJobBill(
        @RequestParam("billNo") String billNo) {
        log.debug("获取结算单: billNo = {}", billNo);
        return settJobBillService.getSettJobBill(billNo)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "获取结算单列表")
    @PostMapping(value = "/findAll")
    public Mono<ListResponse<SettJobBillVo>> findSettJobBill(
        @RequestBody ListSettJobBillParam param) {
        log.debug("获取结算单列表: param = {}", JsonUtils.toJsonString(param));
        return settJobBillService.findSettJobBill(param);
    }

    @Operation(summary = "检查结算单列表变更")
    @PostMapping(value = "/checkSettJobBill")
    public Mono<ListResponse<SettJobBillVo>> checkSettJobBill(
        @RequestBody List<SettJobBillVo> param) {
        log.debug("检查结算单列表变更: param = {}", JsonUtils.toJsonString(param));
        return settJobBillService.checkSettJobBill(param);
    }

    @Operation(summary = "删除结算单(逻辑删除)")
    @DeleteMapping(value = "/deleteBill")
    public Mono<ObjectResponse<SettJobBillVo>> deleteSettJobBill(
        @RequestParam("billNo") String billNo) {
        log.debug("删除结算单(逻辑删除): billNo = {}", billNo);
        return settJobBillService.deleteSettJobBill(billNo)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "结算单数据更新")
    @GetMapping(value = "/recalculateBill")
    public Mono<ObjectResponse<SettJobBillVo>> recalculateSettJobBill(
        @RequestParam("billNo") String billNo,
        @ApiParam("重新计算方式: 1(使用关联任务最新规则计算); 2(使用原来的计算规则进行计算)")
        @RequestParam("recalculateWay") Integer recalculateWay) {
        log.debug("结算单数据更新: {}, {}", billNo, recalculateWay);
        return settJobBillService.recalculateSettJobBill(billNo, recalculateWay)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "结算单生成")
    @GetMapping(value = "/generateBill")
    public Mono<BaseResponse> generateSettJobBill() {
        log.debug("结算单生成");
        settJobBillService.generateSettJobBill();
        return Mono.just(RestUtils.success());
    }

    @Operation(summary = "定时监控运营场站月收入/支出项")
    @GetMapping(value = "/superviseIncomeExpense")
    public Mono<BaseResponse> superviseIncomeExpense() {
        log.debug("定时监控运营场站月收入/支出项");
        settJobBillService.superviseIncomeExpense();
        return Mono.just(RestUtils.success());
    }
}
