package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.domain.alarm.MonitorAlarmRequest;
import com.cdz360.biz.dc.domain.alarm.MonitorEvseRequest;
import com.cdz360.biz.dc.domain.alarm.MonitorSiteCtrlRequest;
import com.cdz360.biz.dc.repository.WarningRecordRepository;
import com.cdz360.biz.dc.utils.GenerateIdByRedisAtomicUtils;
import com.cdz360.biz.ds.trading.ro.alarm.ds.WarningDetailRoDs;
import com.cdz360.biz.model.monitor.dto.WarningRecordPo;
import com.cdz360.biz.model.site.type.AlarmEventTypeEnum;
import com.cdz360.biz.model.site.type.AlarmStatusEnum;
import com.cdz360.biz.model.trading.alarm.vo.WarningDetailVo;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * 桩端上报告警数据实现类
 *
 * <AUTHOR> Create on 2018/09/13 19:41
 */
@Slf4j
@Service
public class AlarmService //implements WWarningRecordService
{

    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    private WarningRecordRepository wWarningRecordRepository;

    @Autowired
    private WarningDetailRoDs warningDetailService;


    /**
     * 临时通过redis存取值避免告警并发问题
     *
     * @param redisKey
     * @return
     */
    @Synchronized
    private Object getRedisObject(String redisKey) {
        long nowTime = System.currentTimeMillis();
        Object redisObject = redisTemplate.opsForValue().get(redisKey);
        if (redisObject != null) {
            log.info("桩端上报执行告警业务进行中{}", redisObject.toString());
            return redisObject;
        }
        log.info("加入告警处理中数据{}时间{}", redisKey, nowTime);
        //加入告警处理中数据，暂定10秒处理时间
        redisTemplate.opsForValue().set(redisKey, nowTime, 10, TimeUnit.SECONDS);
        return null;
    }

    /**
     * 组织告警消息，如果重复告警更新告警时间返回null
     *
     * @param monitorAlarmRequest 桩端上报消息体
     * @return 告警消息
     */
    private WarningRecordPo getWWarningSiteCtrlRecordData(MonitorAlarmRequest monitorAlarmRequest
//, BoxSimpleInfoVo bsBox
    ) throws DcServiceException {
        List<Integer> typeList = new ArrayList<>();
        typeList.add(AlarmEventTypeEnum.ALARM_WARNING.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_MALFUNCTION.getValue());

        // 根据告警码查询桩所有未结束告警
        WarningRecordPo warningRecord = wWarningRecordRepository.getWarningRecordX(
            AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue(),
            monitorAlarmRequest.getAlarmCode().toString(), monitorAlarmRequest.getEvseId(),
            monitorAlarmRequest.getConnectorId(), typeList);

        log.info("桩：{},未结束告警记录列表：{}", monitorAlarmRequest.getEvseId(),
            JsonUtils.toJsonString(warningRecord));
        if (warningRecord != null // && wWarningRecordList.size() > 0
        ) {
            //存储重复告警只更新告警时间，正常只有一条数据
            List warningIdUpdateList = new ArrayList();
            //for (WWarningRecord warningRecord : wWarningRecordList) {
            if (StringUtils.isNotBlank(monitorAlarmRequest.getError())) {
                warningRecord.setError(monitorAlarmRequest.getError());
            }
            if (monitorAlarmRequest.getTemp() != null && monitorAlarmRequest.getTemp() != 0) {
                warningRecord.setTemp(monitorAlarmRequest.getTemp());
            }
            // TODO 需要考虑非桩端上报的离线告警处理
            if (!warningRecord.getWarningType()
                .equals(AlarmEventTypeEnum.ALARM_PLATFORM_WARNING.getValue())) {
                // 如果存在桩端上报告警、故障，存入list更新时间
                log.info("已上报该桩的报警信息{}桩号={},插座号={},告警编码={}",
                    warningRecord.getWarningId(),
                    monitorAlarmRequest.getEvseId(),
                    monitorAlarmRequest.getConnectorId(),
                    monitorAlarmRequest.getAlarmCode());
                warningIdUpdateList.add(warningRecord.getWarningId());
            }
            //}
            wWarningRecordRepository.updateBatchStatusById(warningIdUpdateList, warningRecord);
            log.info("重复告警更新时间不返回数据{}", monitorAlarmRequest.getEvseId());
            // 重复告警只更新时间不返回数据
            return null;
        } else {
            // 根据告警码和设备查询告警对应描述配置
            WarningDetailVo warningDetailQuery = new WarningDetailVo() {{
                setWarningCode(monitorAlarmRequest.getAlarmCode().toString());
            }};

            List<WarningDetailVo> warningDetailVos = warningDetailService.queryWarningDetail(
                warningDetailQuery);
            if (warningDetailVos == null || warningDetailVos.size() == 0) {
                log.warn("获取告警对应描述配置失败。AlarmCode = {}",
                    monitorAlarmRequest.getAlarmCode());
                return null;
            }
            WarningDetailVo warningDetail = warningDetailVos.get(0);
            WarningRecordPo wWarningRecord = new WarningRecordPo();
            wWarningRecord.setWarningId(GenerateIdByRedisAtomicUtils.generateLongKey());
            wWarningRecord.setWarningCode(monitorAlarmRequest.getAlarmCode().toString());
            wWarningRecord.setWarningName(warningDetail.getWarningName());
            // 如果为枪头报警，需要插入枪头序号
            if (monitorAlarmRequest.getConnectorId() != null
                && monitorAlarmRequest.getConnectorId() != 0) {
                wWarningRecord.setConnectorId(monitorAlarmRequest.getConnectorId());
            }
            wWarningRecord.setStartTime(monitorAlarmRequest.getStartTime());
            wWarningRecord.setWarningUpdateTime(monitorAlarmRequest.getStartTime());
            wWarningRecord.setStatus(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue());
            wWarningRecord.setWarningType(monitorAlarmRequest.getInfoType());
            // 告警等级
            wWarningRecord.setLevel(warningDetail.getWarningLevel());
            wWarningRecord.setWarningInstructions(warningDetail.getWarningInstructions());
            // 设备ID
            wWarningRecord.setDeviceId(monitorAlarmRequest.getEvseId());
            // 上报设备ID
            wWarningRecord.setSourceNo(monitorAlarmRequest.getSourceNo());
            wWarningRecord.setBoxOutFactoryCode(monitorAlarmRequest.getEvseId());
            // 商户id
            wWarningRecord.setBusinessId(String.valueOf(monitorAlarmRequest.getSiteCommId()));
            // 站点id
            wWarningRecord.setSiteId(monitorAlarmRequest.getSiteId());
            // 站点名称
            wWarningRecord.setSiteName(monitorAlarmRequest.getSiteName());
            if (StringUtils.isNotBlank(monitorAlarmRequest.getError())) {
                wWarningRecord.setError(monitorAlarmRequest.getError());
            }
            if (monitorAlarmRequest.getTemp() != null && monitorAlarmRequest.getTemp() != 0) {
                wWarningRecord.setTemp(monitorAlarmRequest.getTemp());
            }
            if (StringUtils.isNotBlank(monitorAlarmRequest.getLinkId())) {
                wWarningRecord.setLinkId(monitorAlarmRequest.getLinkId());
            }
            return wWarningRecord;
        }
    }

    private WarningRecordPo getWWarningEvseRecordData(MonitorEvseRequest request)
        throws DcServiceException {
        List<Integer> typeList = new ArrayList<>();
        typeList.add(AlarmEventTypeEnum.ALARM_SITE_CTRL_WARNING.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_SITE_CTRL_MALFUNCTION.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_MALFUNCTION.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_PLATFORM_WARNING.getValue());

        // 根据告警码查询控制器所有未结束告警
        WarningRecordPo warningRecord = wWarningRecordRepository.getWarningEvseRecordX(
            AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue(),
            request.getAlarmCode().toString(),
            request.getEvseNo(),
            typeList);

        log.info("桩：{},未结束告警记录列表：{}", request.getEvseNo(),
            JsonUtils.toJsonString(warningRecord));
        if (warningRecord != null) {
            //存储重复告警只更新告警时间，正常只有一条数据
            List warningIdUpdateList = new ArrayList();
            if (StringUtils.isNotBlank(request.getError())) {
                warningRecord.setError(request.getError());
            }
            if (!warningRecord.getWarningType()
                .equals(AlarmEventTypeEnum.ALARM_PLATFORM_WARNING.getValue())) {
                // 如果存在桩端上报告警、故障，存入list更新时间
                log.info("已上报该桩的报警信息{}, evseNo={}, 告警编码={}",
                    warningRecord.getWarningId(),
                    request.getEvseNo(),
                    request.getAlarmCode());
                warningIdUpdateList.add(warningRecord.getWarningId());
            }
            wWarningRecordRepository.updateBatchStatusById(warningIdUpdateList, warningRecord);
            log.info("重复告警更新时间不返回数据{}", request.getEvseNo());
            // 重复告警只更新时间不返回数据
            return null;
        } else {
            // 根据告警码和设备查询告警对应描述配置
            WarningDetailVo warningDetailQuery = new WarningDetailVo() {{
                setWarningCode(request.getAlarmCode().toString());
            }};

            //TODO 每次都读数据库，需要优化
            List<WarningDetailVo> warningDetailVos = warningDetailService.queryWarningDetail(
                warningDetailQuery);
            if (CollectionUtils.isEmpty(warningDetailVos)) {
                log.warn("获取告警对应描述配置失败。AlarmCode = {}", request.getAlarmCode());
                return null;
            }
            WarningDetailVo warningDetail = warningDetailVos.get(0);
            WarningRecordPo wWarningRecord = new WarningRecordPo();
            wWarningRecord.setWarningId(GenerateIdByRedisAtomicUtils.generateLongKey());
            wWarningRecord.setWarningCode(request.getAlarmCode().toString());
            wWarningRecord.setWarningName(warningDetail.getWarningName());
            // 如果为枪头报警，需要插入枪头序号
//            if (monitorSiteCtrlRequest.getConnectorId() != null && monitorSiteCtrlRequest.getConnectorId() != 0) {
            wWarningRecord.setConnectorId(0);
//            }
            wWarningRecord.setStartTime(request.getStartTime());
            wWarningRecord.setWarningUpdateTime(request.getStartTime());
            wWarningRecord.setStatus(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue());
            if (NumberUtils.equals(request.getInfoType(),
                AlarmEventTypeEnum.ALARM_SITE_CTRL_WARNING.getValue())) {
                request.setInfoType(AlarmEventTypeEnum.ALARM_SITE_CTRL_WARNING.getValue());
            } else if (NumberUtils.equals(request.getInfoType(),
                AlarmEventTypeEnum.ALARM_SITE_CTRL_MALFUNCTION.getValue())) {
                request.setInfoType(AlarmEventTypeEnum.ALARM_SITE_CTRL_MALFUNCTION.getValue());
            } else {
                log.error("未知的infoType: {}", request.getInfoType());
            }
            wWarningRecord.setWarningType(request.getInfoType());
            // 告警等级
            wWarningRecord.setLevel(warningDetail.getWarningLevel());
            wWarningRecord.setWarningInstructions(warningDetail.getWarningInstructions());
            // 设备ID
            wWarningRecord.setBoxOutFactoryCode(request.getEvseNo());
            // 上报设备ID
            wWarningRecord.setSourceNo(request.getCtrlNo());
            // 商户id
            wWarningRecord.setBusinessId(String.valueOf(request.getSiteCommId()));
            // 站点id
            wWarningRecord.setSiteId(request.getSiteId());
            // 站点名称
            wWarningRecord.setSiteName(request.getSiteName());
            if (StringUtils.isNotBlank(request.getError())) {
                wWarningRecord.setError(request.getError());
            }

            if (StringUtils.isNotBlank(request.getLinkId())) {
                wWarningRecord.setLinkId(request.getLinkId());
            }
            return wWarningRecord;
        }
    }

    private WarningRecordPo getWWarningSiteCtrlRecordData(
        MonitorSiteCtrlRequest monitorSiteCtrlRequest) throws DcServiceException {

        List<Integer> typeList = new ArrayList<>();
        typeList.add(AlarmEventTypeEnum.ALARM_SITE_CTRL_WARNING.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_SITE_CTRL_MALFUNCTION.getValue());

        // 根据告警码查询控制器所有未结束告警
        WarningRecordPo warningRecord = wWarningRecordRepository.getWarningSiteCtrlRecordX(
            AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue(),
            monitorSiteCtrlRequest.getAlarmCode().toString(),
            monitorSiteCtrlRequest.getCtrlNo(),
            typeList);

        log.info("场站控制器：{},未结束告警记录列表：{}", monitorSiteCtrlRequest.getCtrlNo(),
            JsonUtils.toJsonString(warningRecord));
        if (warningRecord != null) {
            //存储重复告警只更新告警时间，正常只有一条数据
            List warningIdUpdateList = new ArrayList();
            //for (WWarningRecord warningRecord : wWarningRecordList) {
            if (StringUtils.isNotBlank(monitorSiteCtrlRequest.getError())) {
                warningRecord.setError(monitorSiteCtrlRequest.getError());
            }
            if (monitorSiteCtrlRequest.getPwrTemp() != null
                && monitorSiteCtrlRequest.getPwrTemp() != 0) {
                warningRecord.setPwrTemp(monitorSiteCtrlRequest.getPwrTemp());
            }
            if (monitorSiteCtrlRequest.getLoadRatio() != null
                && monitorSiteCtrlRequest.getLoadRatio() != 0) {
                warningRecord.setLoadRatio(monitorSiteCtrlRequest.getLoadRatio());
            }
            // TODO 需要考虑非桩端上报的离线告警处理
            if (!warningRecord.getWarningType()
                .equals(AlarmEventTypeEnum.ALARM_PLATFORM_WARNING.getValue())) {
                // 如果存在桩端上报告警、故障，存入list更新时间
                log.info("已上报该桩的报警信息{}, 场站控制器={}, 告警编码={}",
                    warningRecord.getWarningId(),
                    monitorSiteCtrlRequest.getCtrlNo(),
                    monitorSiteCtrlRequest.getAlarmCode());
                warningIdUpdateList.add(warningRecord.getWarningId());
            }
            wWarningRecordRepository.updateBatchStatusById(warningIdUpdateList, warningRecord);
            log.info("重复告警更新时间不返回数据{}", monitorSiteCtrlRequest.getCtrlNo());
            // 重复告警只更新时间不返回数据
            return null;
        } else {
            // 根据告警码和设备查询告警对应描述配置
            WarningDetailVo warningDetailQuery = new WarningDetailVo() {{
                setWarningCode(monitorSiteCtrlRequest.getAlarmCode().toString());
            }};

            //TODO 每次都读数据库，需要优化
            List<WarningDetailVo> warningDetailVos = warningDetailService.queryWarningDetail(
                warningDetailQuery);
            if (warningDetailVos == null || warningDetailVos.size() == 0) {
                log.warn("获取告警对应描述配置失败。AlarmCode = {}",
                    monitorSiteCtrlRequest.getAlarmCode());
                return null;
            }
            WarningDetailVo warningDetail = warningDetailVos.get(0);
            WarningRecordPo wWarningRecord = new WarningRecordPo();
            wWarningRecord.setWarningId(GenerateIdByRedisAtomicUtils.generateLongKey());
            wWarningRecord.setWarningCode(monitorSiteCtrlRequest.getAlarmCode().toString());
            wWarningRecord.setWarningName(warningDetail.getWarningName());
            // 如果为枪头报警，需要插入枪头序号
//            if (monitorSiteCtrlRequest.getConnectorId() != null && monitorSiteCtrlRequest.getConnectorId() != 0) {
//                wWarningRecord.setConnectorId(monitorSiteCtrlRequest.getConnectorId());
//            }
            wWarningRecord.setStartTime(monitorSiteCtrlRequest.getStartTime());
            wWarningRecord.setWarningUpdateTime(monitorSiteCtrlRequest.getStartTime());
            wWarningRecord.setStatus(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue());
            if (StringUtils.isNotBlank(monitorSiteCtrlRequest.getLinkId())) {
                // 有LinkId时，认为是控制器的告警
                Assert.notNull(monitorSiteCtrlRequest.getInfoType(), "infoType为null");
                if (NumberUtils.equals(monitorSiteCtrlRequest.getInfoType(),
                    AlarmEventTypeEnum.ALARM_SITE_CTRL_WARNING.getValue())) {
                    monitorSiteCtrlRequest.setInfoType(
                        AlarmEventTypeEnum.ALARM_SITE_CTRL_WARNING.getValue());
                } else if (NumberUtils.equals(monitorSiteCtrlRequest.getInfoType(),
                    AlarmEventTypeEnum.ALARM_SITE_CTRL_MALFUNCTION.getValue())) {
                    monitorSiteCtrlRequest.setInfoType(
                        AlarmEventTypeEnum.ALARM_SITE_CTRL_MALFUNCTION.getValue());
                } else {
                    log.error("未知的infoType: {}", monitorSiteCtrlRequest.getInfoType());
                }
            }
            wWarningRecord.setWarningType(monitorSiteCtrlRequest.getInfoType());
            // 告警等级
            wWarningRecord.setLevel(warningDetail.getWarningLevel());
            wWarningRecord.setWarningInstructions(warningDetail.getWarningInstructions());
            // 设备ID
            wWarningRecord.setBoxOutFactoryCode(monitorSiteCtrlRequest.getCtrlNo());
            // 上报设备ID
            wWarningRecord.setSourceNo(monitorSiteCtrlRequest.getCtrlNo());
            // 商户id
            wWarningRecord.setBusinessId(String.valueOf(monitorSiteCtrlRequest.getSiteCommId()));
            // 站点id
            wWarningRecord.setSiteId(monitorSiteCtrlRequest.getSiteId());
            // 站点名称
            wWarningRecord.setSiteName(monitorSiteCtrlRequest.getSiteName());
            if (StringUtils.isNotBlank(monitorSiteCtrlRequest.getError())) {
                wWarningRecord.setError(monitorSiteCtrlRequest.getError());
            }
            if (monitorSiteCtrlRequest.getPwrTemp() != null
                && monitorSiteCtrlRequest.getPwrTemp() != 0) {
                wWarningRecord.setPwrTemp(monitorSiteCtrlRequest.getPwrTemp());
            }
            if (monitorSiteCtrlRequest.getLoadRatio() != null
                && monitorSiteCtrlRequest.getLoadRatio() != 0) {
                wWarningRecord.setLoadRatio(monitorSiteCtrlRequest.getLoadRatio());
            }
            if (StringUtils.isNotBlank(monitorSiteCtrlRequest.getLinkId())) {
                wWarningRecord.setLinkId(monitorSiteCtrlRequest.getLinkId());
            }
            return wWarningRecord;
        }
    }


    //得到ID列表
    private List<Long> getIdList(List<WarningRecordPo> wWarningRecordList) {
        List<Long> recordIdList = new ArrayList<>();
        for (WarningRecordPo wWarningRecord : wWarningRecordList) {
            if (null != wWarningRecord.getWarningId()) {
                recordIdList.add(wWarningRecord.getWarningId());
            }
        }
        return recordIdList;
    }


    public long notEndWarningRecordNum(MonitorAlarmRequest monitorAlarmRequest) {
        // 查询桩是否已经上报告警，告警数据以桩端上报为准。
        List<String> deviceNoList = new ArrayList<>();
        deviceNoList.add(monitorAlarmRequest.getEvseId());

        List<Integer> typeList = new ArrayList<>();
        typeList.add(AlarmEventTypeEnum.ALARM_WARNING.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_MALFUNCTION.getValue());

        // 根据告警码查询桩所有未结束告警
        long count = wWarningRecordRepository.getWarningRecordCount(
            AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue(),
            monitorAlarmRequest.getAlarmCode() != null ? monitorAlarmRequest.getAlarmCode()
                .toString() : null, deviceNoList,
            monitorAlarmRequest.getConnectorId(), typeList, null);
        return count;
    }

    public long notEndWarningEvseRecordNum(MonitorEvseRequest monitorEvseRequest) {
        // 查询桩是否已经上报告警，告警数据以桩端上报为准。
        List<String> deviceNoList = new ArrayList<>();
        deviceNoList.add(monitorEvseRequest.getEvseNo());

        List<Integer> typeList = new ArrayList<>();
        typeList.add(AlarmEventTypeEnum.ALARM_SITE_CTRL_WARNING.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_SITE_CTRL_MALFUNCTION.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_MALFUNCTION.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_PLATFORM_WARNING.getValue());

        // 根据告警码查询桩所有未结束告警
        long count = wWarningRecordRepository.getWarningRecordCount(
            AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue(),
            monitorEvseRequest.getAlarmCode() != null ? monitorEvseRequest.getAlarmCode().toString()
                : null, deviceNoList,
            0, typeList, null);
        return count;
    }

    public long notEndWarningSiteCtrlRecordNum(MonitorSiteCtrlRequest monitorSiteCtrlRequest) {
        // 查询桩是否已经上报告警，告警数据以桩端上报为准。
        List<String> deviceNoList = new ArrayList<>();
        deviceNoList.add(monitorSiteCtrlRequest.getCtrlNo());

        List<Integer> typeList = new ArrayList<>();
        typeList.add(AlarmEventTypeEnum.ALARM_SITE_CTRL_WARNING.getValue());
        typeList.add(AlarmEventTypeEnum.ALARM_SITE_CTRL_MALFUNCTION.getValue());

        // 根据告警码查询控制器生成的所有未结束告警
        long count = wWarningRecordRepository.getWarningSiteCtrlRecordCount(
            AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue(),
            monitorSiteCtrlRequest.getAlarmCode() != null ? monitorSiteCtrlRequest.getAlarmCode()
                .toString() : null,
            deviceNoList,
            typeList);
        return count;
    }


    public List<WarningRecordPo> evseErrorAlarms(Date startTime,
        Date endTime,
        Integer plugId,
        String evseNo,
        String siteId) {
        return wWarningRecordRepository.evseErrorAlarms(startTime, endTime, plugId, evseNo, siteId);
    }

}
