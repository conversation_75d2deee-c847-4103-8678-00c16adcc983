package com.cdz360.biz.dc.service.peek.invoice;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.biz.model.trading.peek.invoice.dto.PeekInvoiceDto;
import com.chargerlinkcar.framework.common.domain.peek.invoice.param.PeekInvoiceParam;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * PersonalPeekInvoiceStrategyImpl
 *
 * @since 3/25/2023 5:26 PM
 * <AUTHOR>
 */
@Slf4j
@Service
public class PersonalPeekInvoiceStrategyImpl extends AbstractPeekInvoiceStrategy {

    @Autowired
    private PeekInvoiceService peekInvoiceService;

    @Autowired
    private ComputeInvoiceService computeInvoiceService;

    @PostConstruct
    public void init() {
        peekInvoiceService.addStrategy(PayAccountType.PERSONAL, this);
        peekInvoiceService.addStrategy(PayAccountType.COMMERCIAL, this);
    }

    @Override
    public PeekInvoiceDto peekInvoice(PeekInvoiceParam params) {
        return computeInvoiceService.peekInvoice(params);
    }
}