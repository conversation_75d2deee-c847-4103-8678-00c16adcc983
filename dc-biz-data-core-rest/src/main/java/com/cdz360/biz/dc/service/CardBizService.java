package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ds.trading.ro.iot.ds.WhiteCardEvseRoDs;
import com.cdz360.biz.ds.trading.rw.iot.ds.WhiteCardEvseRwDs;
import com.cdz360.biz.model.trading.iot.param.ListWhiteCardEvseParam;
import com.cdz360.biz.model.trading.iot.po.WhiteCardEvsePo;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CardBizService {

    @Autowired
    private WhiteCardEvseRoDs whiteCardEvseRoDs;
    @Autowired
    private WhiteCardEvseRwDs whiteCardEvseRwDs;

    public List<WhiteCardEvsePo> getWhiteCardEvseList(ListWhiteCardEvseParam param) {
        return this.whiteCardEvseRoDs.getWhiteCardEvseList(param);
    }

    public BaseResponse disable(List<String> whiteCardNoList) {
        IotAssert.isTrue(CollectionUtils.isNotEmpty(whiteCardNoList), "缺少入参");
        return whiteCardEvseRwDs.disable(whiteCardNoList)
            ? RestUtils.success() : RestUtils.fail(2000, "操作失败");
    }

}
