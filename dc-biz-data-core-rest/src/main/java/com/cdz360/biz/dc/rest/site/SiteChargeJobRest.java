package com.cdz360.biz.dc.rest.site;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.site.SiteChargeJobService;
import com.cdz360.biz.model.trading.site.param.ChargeJobLogParam;
import com.cdz360.biz.model.trading.site.param.ChargeJobParam;
import com.cdz360.biz.model.trading.site.po.SiteChargeJobLogPo;
import com.cdz360.biz.model.trading.site.po.SiteChargeJobPo;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobLogVo;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobMoveCorpList;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobVo;
import com.cdz360.biz.model.trading.site.vo.TimeChargeVO;
import com.chargerlinkcar.framework.common.domain.param.SiteChargeJobParam;
import com.chargerlinkcar.framework.common.rest.BaseController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
public class SiteChargeJobRest extends BaseController {

    @Autowired
    private SiteChargeJobService siteChargeJobService;

    @Operation(summary = "绑定枪的定时充电任务")
    @PostMapping("/dataCore/siteChargeJobs/bindingJob")
    public BaseResponse bindingJob(@RequestBody SiteChargeJobParam param) {
        log.info("vo: {}", JsonUtils.toJsonString(param));
        return siteChargeJobService.bindingJob(param);
    }

    @Operation(summary = "解绑枪的定时充电任务")
    @PostMapping("/dataCore/siteChargeJobs/unbindingJob")
    public BaseResponse unbindingJob(@RequestBody List<String> plugNoList) {
        log.info("plugNoList: {}", JsonUtils.toJsonString(plugNoList));
        siteChargeJobService.unbindingJob(plugNoList);
        return RestUtils.success();
    }

    @Operation(summary = "获取场站下的定时充电任务")
    @GetMapping("/dataCore/siteChargeJobs/getSiteChargeJobBySiteId")
    public ListResponse<SiteChargeJobPo> getSiteChargeJobBySiteId(@RequestParam(value = "siteId", required = true)
                                                                          String siteId,
                                                                  @RequestParam(value = "jobName", required = false)
                                                                          String jobName) {
        return siteChargeJobService.getSiteChargeJobBySiteId(siteId, jobName);
    }

    @Operation(summary = "充电任务列表")
    @PostMapping("/dataCore/siteChargeJobs/jobList")
    public ListResponse<SiteChargeJobVo> jobList(@RequestBody ChargeJobParam param) {
        log.info("param: {}", JsonUtils.toJsonString(param));
        return siteChargeJobService.jobList(param);
    }

    @Operation(summary = "定时充电任务-启用/停用")
    @GetMapping("/dataCore/siteChargeJobs/changeJobStatus")
    public BaseResponse changeJobStatus(@RequestParam(value = "jobId") Long jobId,
                                        @Parameter(name = "0:有效, 1:停用", example = "0")
                                        @RequestParam(value = "jobStatus") Integer jobStatus) {

        return siteChargeJobService.changeJobStatus(jobId, jobStatus);
    }

    @Operation(summary = "修改定时充电任务")
    @PostMapping("/dataCore/siteChargeJobs/modifyChargeJob")
    public BaseResponse modifyChargeJob(@RequestBody SiteChargeJobParam param) {
        log.info("param: {}", JsonUtils.toJsonString(param));
        return siteChargeJobService.modifyChargeJob(param);
    }

    @Operation(summary = "定时充电任务日志列表")
    @PostMapping("/dataCore/siteChargeJobs/getChargeJobLogList")
    public ListResponse<SiteChargeJobLogVo> getChargeJobLogList(@RequestBody ChargeJobLogParam param) {
        log.info("param: {}", JsonUtils.toJsonString(param));
        return siteChargeJobService.getChargeJobLogList(param);
    }

    @Operation(summary = "加载最近30分钟要执行的任务")
    @GetMapping("/dataCore/siteChargeJobs/loadChargeJob")
    public ListResponse<TimeChargeVO> loadChargeJob() {
        log.info("load start");
        return siteChargeJobService.loadChargeJob();
    }

    @Operation(summary = "开始执行充电任务")
    @PostMapping("/dataCore/siteChargeJobs/executeChargingJob")
    public BaseResponse executeChargingJob(@RequestBody TimeChargeVO vo) {
        log.info("vo: {}", vo);
        return siteChargeJobService.executeChargingJob(vo);
    }

    @Operation(summary = "检查充电任务")
    @GetMapping("/dataCore/siteChargeJobs/checkForTimeout")
    public BaseResponse checkForTimeout() {
        siteChargeJobService.checkForTimeout();
        return RestUtils.success();
    }

    @Operation(summary = "回写充电任务日志状态")
    @PostMapping("/dataCore/siteChargeJobs/writeBack")
    public BaseResponse writeBack(@RequestBody SiteChargeJobLogPo po) {
        siteChargeJobService.writeBack(po);
        return RestUtils.success();
    }
    @Operation(summary = "企业修改商户，获取相关保留/删除的定时任务列表")
    @GetMapping("/dataCore/siteChargeJobs/getMoveCorpDetail")
    public ObjectResponse<SiteChargeJobMoveCorpList> getMoveCorpDetail(@RequestParam("corpId") Long corpId,
                                                                       @RequestParam("commId") Long commId) {

        return new ObjectResponse<>(siteChargeJobService.getMoveCorpDetail(corpId, commId));
    }
}
