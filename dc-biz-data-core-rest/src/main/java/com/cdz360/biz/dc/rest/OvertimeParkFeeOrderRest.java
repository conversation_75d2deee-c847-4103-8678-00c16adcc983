package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.OvertimeParkFeeOrderService;
import com.cdz360.biz.model.trading.site.param.ListOvertimeParkFeeOrderParam;
import com.cdz360.biz.model.trading.site.vo.OvertimeParkFeeOrderBi;
import com.cdz360.biz.model.trading.site.vo.OvertimeParkFeeOrderVo;
import com.cdz360.biz.model.trading.site.vo.SiteOvertimeParkDivisionVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderOvertimeParkInfoVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Tag(name = "超停收费订单相关接口", description = "超停收费订单相关接口")
@Slf4j
@RestController
public class OvertimeParkFeeOrderRest {

    @Autowired
    private OvertimeParkFeeOrderService overtimeParkFeeOrderService;

    @Operation(summary = "获取超停收费订单列表")
    @PostMapping(value = "/dataCore/overtimeParkFeeOrder/findAll")
    public ListResponse<OvertimeParkFeeOrderVo> findAll(
            @RequestBody ListOvertimeParkFeeOrderParam param) {
        log.debug("获取超停收费订单列表: {}", JsonUtils.toJsonString(param));
        return overtimeParkFeeOrderService.findAll(param);
    }

    @Operation(summary = "超停收费订单统计")
    @PostMapping(value = "/dataCore/overtimeParkFeeOrder/orderBi")
    public ObjectResponse<OvertimeParkFeeOrderBi> orderBi(
            @RequestBody ListOvertimeParkFeeOrderParam param) {
        log.debug("超停收费订单统计: {}", JsonUtils.toJsonString(param));
        return RestUtils.buildObjectResponse(overtimeParkFeeOrderService.orderBi(param));
    }

    @Operation(summary = "根据订单号获取占位费信息")
    @GetMapping(value = "/dataCore/overtimeParkFeeOrder/getOverTimeParkFeeByOrderNo")
    public ObjectResponse<OvertimeParkFeeOrderVo> getOverTimeParkFeeByOrderNo(@RequestParam(value = "orderNo") String orderNo) {
        log.info("根据订单号获取占位费信息: orderNo={}",orderNo);
        return  RestUtils.buildObjectResponse(overtimeParkFeeOrderService.getOverTimeParkFeeByOrderNo(orderNo));
    }

    @Operation(summary = "根据订单号获取占位费分时信息")
    @GetMapping(value = "/dataCore/overtimeParkFeeOrder/getOverTimeParkDivisionByOrderNo")
    public ObjectResponse<OrderOvertimeParkInfoVo> getOverTimeParkDivisionByOrderNo(
        @RequestParam(value = "orderNo") String orderNo) {
        log.info("根据订单号获取占位费分时信息: orderNo={}",orderNo);
        return  RestUtils.buildObjectResponse(
            overtimeParkFeeOrderService.getOverTimeParkDivisionByOrderNo(orderNo));
    }

    @Operation(summary = "取消超停收费订单")
    @PostMapping(value = "/dataCore/overtimeParkFeeOrder/cancel")
    public ObjectResponse<Integer> cancel(
        @RequestBody ListOvertimeParkFeeOrderParam param) {
        log.debug("取消超停收费订单: {}", JsonUtils.toJsonString(param));
        return overtimeParkFeeOrderService.cancel(param);
    }
}
