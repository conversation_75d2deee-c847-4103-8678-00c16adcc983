package com.cdz360.biz.dc.service.parse.line;

import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.model.trading.bill.po.ZftThirdOrderPo;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class WxLineParse implements LineParse {

    private static final int LINE_COL_NUM = 27;

    private static final String EXCHANGE = "SUCCESS";

    private static final String REFUND = "REFUND";

    private static final DateTimeFormatter FORMAT_yyyy_MM_dd_HH_mm_ss =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final String MCH_DATA_PACKAGE_SPLIT = "\\|";
    private static final int MCH_DATA_PACKAGE_LENGTH = 3;
    private static final String WX_ZFF = "wxzff";

    @Override
    public String getSplit() {
        return ",";
    }

    @Override
    public BaseObject resolveLine(String rowData) {
        //判断参数
        if (StringUtils.isBlank(rowData)) {
            log.error("【微信文件解析】行数据为空!");
            return null;
        }

        // 分解行数据
        String[] rowDataArray = rowData.split(this.getSplit());
        if (rowDataArray.length != LINE_COL_NUM) {
            log.error("【微信文件解析】无效记录：" + rowData);
            return null;
        }

        try {
            ZftThirdOrderPo thirdOrderPo = new ZftThirdOrderPo();
            LocalDateTime parse = LocalDateTime.parse(trimAll(rowDataArray[0]), FORMAT_yyyy_MM_dd_HH_mm_ss);
            thirdOrderPo.setTradeTime(Date.from(parse.atZone(ZoneId.systemDefault()).toInstant()));

            // 订单金额,费率 单位是分
            String fee = trimAll(rowDataArray[22]);
            thirdOrderPo.setPayFee(new BigDecimal(fee));

            // 交易状态
            String tradeStatus = trimAll(rowDataArray[9]);
            if (EXCHANGE.equals(tradeStatus)) { // 充值
                thirdOrderPo.setChannelNo(trimAll(rowDataArray[5]));

                this.getPlatformNoByWxCredit(rowDataArray)
                    .ifPresentOrElse(thirdOrderPo::setPlatformNo,
                        () -> thirdOrderPo.setPlatformNo(trimAll(rowDataArray[6])));

                String amount = trimAll(rowDataArray[12]);
                thirdOrderPo.setTradeAmount(new BigDecimal(amount));
                thirdOrderPo.setTradeType(DepositFlowType.IN_FLOW);
            } else if (REFUND.equals(tradeStatus)) { // 退款
                thirdOrderPo.setChannelNo(trimAll(rowDataArray[14]));
                thirdOrderPo.setPlatformNo(trimAll(rowDataArray[15]));

                String refund = trimAll(rowDataArray[16]);
                thirdOrderPo.setTradeAmount(new BigDecimal(refund));
                thirdOrderPo.setTradeType(DepositFlowType.OUT_FLOW);
            } else {
                thirdOrderPo.setTradeType(DepositFlowType.UNKNOWN);
            }

            return thirdOrderPo;
        } catch (Exception e) {
            log.error("数据异常，原始数据信息: {}", rowData, e);
        }
        return null;
    }

    private Optional<String> getPlatformNoByWxCredit(String[] rowDataArray) {
        String mchDataPackage = trimAll(rowDataArray[21]);
        if (StringUtils.isEmpty(mchDataPackage)) return Optional.empty();

        String[] data = mchDataPackage.split(MCH_DATA_PACKAGE_SPLIT);
        if (data.length == MCH_DATA_PACKAGE_LENGTH && WX_ZFF.equalsIgnoreCase(data[0])) {
            return Optional.ofNullable(data[2]);
        }
        return Optional.empty();
    }

    private static String trimAll(String target) {
        return target.trim()
                .replaceAll("`", "")
                .replaceAll("\t", "");
    }
}
