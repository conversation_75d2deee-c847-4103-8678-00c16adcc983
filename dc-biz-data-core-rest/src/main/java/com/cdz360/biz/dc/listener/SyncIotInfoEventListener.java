package com.cdz360.biz.dc.listener;

import com.cdz360.base.model.base.type.IotEvent;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.service.DeviceBizService;
import com.cdz360.biz.dc.service.PriceSchemaBizService;
import com.cdz360.biz.model.common.constant.Constant;
import com.cdz360.data.sync.event.DcBaseQEvent;
import com.cdz360.data.sync.event.EvseInfoEvent;
import com.cdz360.data.sync.event.MqEventSubType;
import com.cdz360.data.sync.event.PlugInfoEvent;
import com.chargerlinkcar.framework.common.domain.param.BoxActivateRequest;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RabbitListener(queues = Constant.MQ_QUEUE_DATA_CORE_IOT)
public class SyncIotInfoEventListener {

    public static final String eventType = "eventType";

    @Autowired
    private DeviceBizService deviceBizService;
    @Autowired
    private PriceSchemaBizService priceSchemaBizService;

    @RabbitHandler
    public void syncInfoListener(String msg) {
        log.info(">> sync iot msg = {}", msg);
        try {
            JsonNode json = JsonUtils.fromJson(msg);
            String event = json.get(eventType).asText();

            if (event.equals(IotEvent.BIND.name())) {

                String subMqType = json.get(DcBaseQEvent.FIELD_MQ_SUB_TYPE).asText();
                if (MqEventSubType.MQ_EVSE.name().equals(subMqType)) {

                    EvseInfoEvent evseInfoEvent = JsonUtils.fromJson(msg, EvseInfoEvent.class);
                    EvseVo evseVo = evseInfoEvent.getData();
                    BoxActivateRequest paramx = new BoxActivateRequest();
                    paramx.setBoxName(evseVo.getName());
                    if (null != evseVo.getUseSiteCfg()) {
                        paramx.setIsUseSiteDefaultSetting(Boolean.TRUE.equals(evseVo.getUseSiteCfg()) ? 1 : 0);
                    }
                    paramx.setSerialNumber(evseVo.getEvseNo());
                    paramx.setSiteId(evseVo.getSiteId());
                    paramx.setRatedPower(evseVo.getPower());
                    paramx.setModelName(StringUtils.isBlank(evseVo.getModelName()) ? "" : evseVo.getModelName());
                    paramx.setFirmwareVer(StringUtils.isBlank(evseVo.getFirmwareVer()) ? "" : evseVo.getFirmwareVer());
                    deviceBizService.activateBoxInfo(paramx);

                } else if (MqEventSubType.MQ_PLUG.name().equals(subMqType)) {

                    PlugInfoEvent plugInfoEvent = JsonUtils.fromJson(msg, PlugInfoEvent.class);
                    PlugVo plugVo = plugInfoEvent.getData();
                    deviceBizService.initBsChargerInfo(plugVo);

                }


            } else if (event.equals(IotEvent.UNBIND.name())) {

                String subMqType = json.get(DcBaseQEvent.FIELD_MQ_SUB_TYPE).asText();
                if (MqEventSubType.MQ_EVSE.name().equals(subMqType)) {
                    EvseInfoEvent evseInfoEvent = JsonUtils.fromJson(msg, EvseInfoEvent.class);
                    EvseVo evseVo = evseInfoEvent.getData();
                    deviceBizService.unActivateBoxInfo(evseVo.getEvseNo());

                } else if (MqEventSubType.MQ_PLUG.name().equals(subMqType)) {

                    // nothing to do
                }

            } else if (event.equals(IotEvent.CFG_CHANGE.name())) {
                // 配置/价格变更
                String subMqType = json.get(DcBaseQEvent.FIELD_MQ_SUB_TYPE).asText();
                if (MqEventSubType.MQ_EVSE.name().equals(subMqType)) {
                    EvseInfoEvent evseInfoEvent = JsonUtils.fromJson(msg, EvseInfoEvent.class);
                    EvseVo evseVo = evseInfoEvent.getData();
                    deviceBizService.postPriceActive(evseVo.getEvseNo(), evseVo.getPriceCode());
                } else if (MqEventSubType.MQ_PLUG.name().equals(subMqType)) {

                    // nothing to do
                }
            } else if (event.equals(IotEvent.CREATE.name())) {
                // 此处逻辑转移至iot-worker桩注册方法
//                String subMqType = json.get(DcBaseQEvent.FIELD_MQ_SUB_TYPE).asText();
//                if (MqEventSubType.MQ_EVSE.name().equals(subMqType)) {
//                    try {
//                        EvseInfoEvent evseInfoEvent = JsonUtils.fromJson(msg, EvseInfoEvent.class);
//                        EvseVo evseVo = evseInfoEvent.getData();
//                        new Timer().schedule(new TimerTask() {
//                            @Override
//                            public void run() {
//                                priceSchemaBizService.sendPriceSchema(evseVo.getEvseNo());
//                            }
//                        }, 3000L);  // 延迟3秒后再下发桩价格
//                    } catch (Exception e) {
//                        log.warn("桩注册重新下发计费异常: {}", e.getMessage(), e);
//                    }
//                }
            } else {
                log.debug("无需处理的事件");
            }

        } catch (Exception e) {
            log.error("处理消息失败 {}", e.getMessage(), e);
        }
    }
}
