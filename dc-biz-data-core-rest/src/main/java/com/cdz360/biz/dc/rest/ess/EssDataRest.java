package com.cdz360.biz.dc.rest.ess;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.es.vo.EmuDailyFeeFull;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.ess.EssDailyDataService;
import com.cdz360.biz.ess.model.data.param.DayKwhParam;
import com.cdz360.biz.ess.model.data.vo.DayEssDataBi;
import com.cdz360.biz.ess.model.data.vo.DaySiteEssRtDataBi;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "储能数据", description = "储能数据")
@Slf4j
@RestController
@RequestMapping("/dataCore/ess/data")
public class EssDataRest {


    @Autowired
    private EssDailyDataService essDailyDataService;

    /**
     * 保存ESS的每日充放电数据
     *
     * @param seq 用于调用链日志追踪
     */
    @PostMapping("/saveEssDailyData")
    public BaseResponse saveEssDailyData(
        @RequestParam String siteId,
        @RequestParam(required = false) Long seq,
        @RequestBody EmuDailyFeeFull data) {
        log.info(">> 保存ESS的每日充放电数据 seq= {}, siteId= {}", seq, siteId);
        essDailyDataService.saveDailyEmuData(siteId, data);
        return RestUtils.success();
    }

    @Operation(summary = "获取场站近七天储能数据", description = "从昨天开始近七天(仅返回有数据的天数)")
    @PostMapping(value = "/siteRtData7Day")
    public Mono<ListResponse<DayEssDataBi>> siteRtData7Day(
        @Parameter(name = "场站ID", required = true) @RequestParam("siteId") String siteId) {
        log.info("获取场站近七天发电数据: siteId = {}", siteId);
        return this.essDailyDataService.siteRtData7Day(siteId)
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "获取场站下指定时间范围储能数据量",
        description = "仅返回带数据的日期,注意空数据日期")
    @PostMapping(value = "/siteDayOfRangeKwh")
    public Mono<ListResponse<DaySiteEssRtDataBi>> siteDayOfRangeKwh(
        @RequestBody DayKwhParam param) {
        log.info("获取场站下指定时间范围储能数据量: param = {}", JsonUtils.toJsonString(param));
        return this.essDailyDataService.siteDayOfRangeKwh(param)
            .collectList()
            .map(RestUtils::buildListResponse);
    }

}
