package com.cdz360.biz.dc.domain.alarm;

import lombok.Getter;

/**
 * 告警类型(参考w_warnings表，要做到桩端和插座端告警类型同步)
 *
 * <AUTHOR>
 * @since Create on 2018/7/24 19:53
 */
@Getter
public enum AlarmTypeEnum {

    ALARM_NORMAL_DC("0", "正常", "正常", 0),
    ALARM_NORMAL_AC("1000", "正常", "正常", 0),

    //暂时使用此告警码，后期可以更新
    ALARM_CONNECT_LOST("1101", "桩离线", "充电桩", 0),
    //急停告警
    ALARM_EMERGENCY_STOP("5", "急停告警", "充电桩", 0),

    ALARM_GW_LOGIN_TIME_OUT("5000", "网关登录超时", "网关", 3),

    ALARM_MICRO_APPS_UPDOWN("6000", "微服务掉线", "微服务", 3),

    ALARM_TRANS_FINISH("7000", "资金周转交易失败告警", "微服务", 3),

    //以下未使用
    ALARM_SMOKE("100", "烟感报警", "感应器", 1),
    ALARM_SIM("401", "流量不足", "SIM卡", 1),
    ALARM_OFFLINE_8H("00018", "长时间离线", "充电桩", 1),
    CHG_ALARM_SYS_POWER_FAULT("20106", "断电", "充电桩", 1),
    ALARM_CARD_READ_COMMUNICATE("20108", "读卡器通信故障", "充电桩", 1),
    ALARM_SURROUND_HIGH_TEMPERATURE("20327", "环境温度过高", "插座", 1),
    CHG_ALARM_CHIP_HIGH_TEMPERATURE("20336", "芯片高温", "充电桩", 1),
    CHG_ALARM_CCM_FUSE_BLOW("20333", "插座异常告警", "插座", 1),
    CHG_ALARM_LOAD_OVER("20334", "负载超限", "插座", 1),
    CHG_ALARM_NO_CURRENT_UP("20335", "连续3次开启充电后没有电流上报", "插座", 1);

    private String value;

    private String label;

    private String entity;

    private int level;

    /**
     * 构造方法
     *
     * @param value  告警代码
     * @param label  告警
     * @param entity 告警实体
     * @param level  告警等级
     */
    AlarmTypeEnum(String value, String label, String entity, int level) {
        this.value = value;
        this.label = label;
        this.entity = entity;
        this.level = level;
    }

}
