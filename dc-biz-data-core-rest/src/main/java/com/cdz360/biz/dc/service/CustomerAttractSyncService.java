package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ds.trading.ro.sync.ds.CustomerAttractSyncRoDs;
import com.cdz360.biz.ds.trading.rw.sync.ds.CustomerAttractSyncRwDs;
import com.cdz360.biz.model.common.constant.CustomerAttractType;
import com.cdz360.biz.model.common.po.CustomerAttractPo;
import com.cdz360.biz.model.cus.param.CustomerAttractListParam;
import com.cdz360.biz.model.trading.sync.vo.CustomerAttractBiDto;
import com.cdz360.biz.model.trading.sync.vo.CustomerAttractBiItem;
import com.cdz360.biz.model.trading.sync.vo.CustomerAttractBiVo;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.OptionalUtils;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.temporal.TemporalAdjusters;
import java.util.Date;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CustomerAttractSyncService {

    @Autowired
    private CustomerAttractSyncRoDs customerAttractSyncRoDs;
    @Autowired
    private CustomerAttractSyncRwDs customerAttractSyncRwDs;

    /**
     * 新增客户引流记录
     *
     * @param sysUid
     * @param sysUserName
     * @param type
     * @param customerId
     */
    public void addCustomerAttractRecord(Long sysUid, String sysUserName,
        CustomerAttractType type, Long customerId) {
        if (sysUid == null || StringUtils.isBlank(sysUserName)
            || type == null || customerId == null) {
            return;
        }
        if (customerAttractSyncRwDs.alreadyExist(customerId)) {
            return;
        }
        try {
            CustomerAttractPo customerAttractPo = new CustomerAttractPo()
                .setType(type)
                .setCustomerId(customerId)
                .setSysUid(sysUid)
                .setSysUserName(sysUserName);
            boolean addSucceed = customerAttractSyncRwDs.insertIgnore(customerAttractPo);
            if (!addSucceed) {
                log.error("新增客户引流记录失败。customerAttractPo: {}", customerAttractPo);
            }
        } catch (Exception ex) {
            log.error("新增客户引流记录失败。customerId: {}, error: {}", customerId, ex.getMessage(),
                ex);
        }
    }

    /**
     * 获取引流汇总数据
     *
     * @param param
     * @return
     */
    public ObjectResponse<CustomerAttractBiVo> getCustomerAttractBi(
        CustomerAttractListParam param) {
        log.info("getCustomerAttractBi. param: {}", param);
        IotAssert.isNotNull(param.getSysUid(), "入参缺失");
        CustomerAttractBiVo res = new CustomerAttractBiVo();

        LocalDate today = LocalDate.now();
        if (Boolean.TRUE.equals(param.getObtainCurrMonthData())) {
            // 是否获取本月引流数据
            CustomerAttractBiItem currMonth = new CustomerAttractBiItem();

            TimeFilter createTimeFilter = new TimeFilter();
            createTimeFilter.setStartTime(
                    DateUtil.localDateToDate(today.with(TemporalAdjusters.firstDayOfMonth()))) // 本月的第一天
                .setEndTime(new Date()); // 当前时间

            this.getCustomerAttractBiItemBySysUid(currMonth, param.getSysUid(),
                createTimeFilter);

            res.setCurrMonth(currMonth);
        }

        if (Boolean.TRUE.equals(param.getObtainLastMonthData())) {
            // 是否获取上月引流数据
            CustomerAttractBiItem lastMonth = new CustomerAttractBiItem();

            LocalDate thisDayLastMonth = today.minusMonths(1L); // 上月的今天
            TimeFilter createTimeFilter = new TimeFilter();
            createTimeFilter.setStartTime(
                    DateUtil.localDateToDate(
                        thisDayLastMonth.with(TemporalAdjusters.firstDayOfMonth()))) // 上月的第一天
                .setEndTime(
                    DateUtil.localDateToDate(
                        thisDayLastMonth.with(TemporalAdjusters.lastDayOfMonth()))); // 上月的最后一天

            this.getCustomerAttractBiItemBySysUid(lastMonth, param.getSysUid(),
                createTimeFilter);

            res.setLastMonth(lastMonth);
        }

        if (Boolean.TRUE.equals(param.getObtainTotalData())) {
            // 是否获取累积引流数据
            CustomerAttractBiItem totalMonth = new CustomerAttractBiItem();

            this.getCustomerAttractBiItemBySysUid(totalMonth, param.getSysUid(),
                null);

            res.setTotal(totalMonth);
        }
        return RestUtils.buildObjectResponse(res);
    }

    /**
     * 根据引流人和时间获取引流数据
     *
     * @param biItem
     * @param sysUid
     * @param createTimeFilter
     */
    private void getCustomerAttractBiItemBySysUid(@NonNull CustomerAttractBiItem biItem,
        @NonNull long sysUid, @Nullable TimeFilter createTimeFilter) {

        OptionalUtils.ofEmptyListAble(customerAttractSyncRoDs.getNumDto(sysUid, createTimeFilter))
            .map(e -> e.stream().filter(x -> x != null && x.getNum() != null).collect(
                Collectors.toMap(CustomerAttractBiDto::getType, CustomerAttractBiDto::getNum)))
            .ifPresent(numMap -> {
                biItem.setUserNum(numMap.getOrDefault(CustomerAttractType.PERSONAL, 0L))
                    .setCorpNum(numMap.getOrDefault(CustomerAttractType.CORP, 0L));
            });

        OptionalUtils.ofEmptyListAble(customerAttractSyncRoDs.getKwhDto(sysUid, createTimeFilter))
            .map(e -> e.stream().filter(x -> x != null && x.getKwh() != null).collect(
                Collectors.toMap(CustomerAttractBiDto::getType, CustomerAttractBiDto::getKwh)))
            .ifPresent(kwhMap -> {
                biItem.setUserKwh(
                        kwhMap.getOrDefault(CustomerAttractType.PERSONAL, BigDecimal.ZERO))
                    .setCorpKwh(kwhMap.getOrDefault(CustomerAttractType.CORP, BigDecimal.ZERO));
            });
    }

    /**
     * 根据客户类型获取引流客户详情数据
     *
     * @param param
     * @return
     */
    public ListResponse<CustomerAttractBiVo> getAttractBiList(
        CustomerAttractListParam param) {
        log.info("getAttractBiList. param: {}", param);
        IotAssert.isTrue(param.getType() != null
//            && StringUtils.isNotBlank(param.getCommIdChain())
            && param.getSysUid() != null, "入参缺失");

        param.setCommIdChain(null); // 充电统计数据忽略商户层级限制

        return RestUtils.buildListResponse(
            customerAttractSyncRoDs.getAttractBiList(param.getType(),
                param.getCommIdChain(),
                param.getSysUid(),
                param.getStrKeyword(),
                param.getStart(), param.getSize()));
    }

}
