package com.cdz360.biz.dc.service.invoice;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.ds.trading.ro.order.ds.PayBillRoDs;
import com.cdz360.biz.model.finance.type.TaxStatus;
import com.cdz360.biz.model.invoice.type.ProductType;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.cdz360.biz.model.trading.invoice.dto.CorpInvoiceRecordDto;
import com.cdz360.biz.model.trading.order.dto.PayBillInvoiceBi;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordUpdateParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedTemplateSalDetailVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderBiVo;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PrePayUpdateCorpInvoiceRecordStrategyImpl
        extends AbstractCorpInvoiceRecordStrategy {

    @Autowired
    private UpdateCorpInvoiceRecordService updateCorpInvoiceRecordService;

    @Autowired
    private PayBillRoDs payBillRoDs;

    @Autowired
    private InvoiceProcess invoiceProcess;

    @PostConstruct
    public void init() {
        updateCorpInvoiceRecordService.addStrategy(InvoicingMode.PRE_PAY, this);
    }

    @Override
    public CorpInvoiceRecordDto preUpdate(CorpInvoiceRecordUpdateParam param, boolean append) {
        // 初始化对象值
        CorpInvoiceRecordDto updateDto = super.preUpdate(param, append);

        // 统计订单的充值金额(实际金额)
        PayBillParam billParam = new PayBillParam();
        if (!Boolean.TRUE.equals(param.getOpAll())) {
            billParam = new PayBillParam();
            billParam.setCommIdChain(param.getCommIdChain());
            billParam.setFlowTypeList(List.of(DepositFlowType.IN_FLOW));
            billParam.setUserId(param.getCorpInvoiceInfoVo().getUid());
            billParam.setTaxStatus(List.of(TaxStatus.NO));
            billParam.setOrderIdList(param.getOrderNoList());
        } else {
            updateDto.setFixElecFee(BigDecimal.ZERO)
                    .setFixServFee(BigDecimal.ZERO)
                    .setFixTotalFee(BigDecimal.ZERO)
                    .setTotalFee(BigDecimal.ZERO)
                    .setActualElecFee(BigDecimal.ZERO)
                    .setActualServFee(BigDecimal.ZERO);

            billParam.setCommIdChain(param.getCommIdChain());
            billParam.setUserId(param.getPayBillParam().getUserId());
            billParam.setOrderId(param.getPayBillParam().getOrderId());
            billParam.setPayTimeFilter(param.getPayBillParam().getPayTimeFilter());
            billParam.setInCorpInvoice(true);
            billParam.setFlowTypeList(List.of(DepositFlowType.IN_FLOW));
            billParam.setAccountTypeList(List.of(PayAccountType.CORP)); // 查询企业客户的充值
            billParam.setTaxStatus(List.of(TaxStatus.NO)); // 未开票
        }

        // 充值实际金额
        BigDecimal cost = BigDecimal.ZERO;
        if (append || !Boolean.TRUE.equals(param.getOpAll())) {
            ObjectResponse<OrderBiVo> orderBi = payBillRoDs.invoiceOrderBiForCorp(billParam);

            cost = orderBi.getData().getInvoiceAmount();

            // 企业客户商品行信息
            List<InvoicedTemplateSalDetailVo> detailVoList = param.getCorpInvoiceInfoVo().getTempRefVo().getDetailVoList();
            if (CollectionUtils.isEmpty(detailVoList)) {
                throw new DcArgumentException("商品行还没有配置");
            }

            long count = detailVoList.stream()
                    .filter(item -> item.getProductType() == ProductType.SERV_ACTUAL_FEE).count();
            if (count == 0) {
                // 全部算成电费费
                this.updateFee(append, updateDto, BigDecimal.ZERO, cost);
                return updateDto;
            }
        }

        this.updateFee(append, updateDto, cost, BigDecimal.ZERO);
        return updateDto;
    }

    /**
     * 过滤无效的orderNo，并返回对应的可开票金额
     *
     * @return String：orderNo； BigDecimal：可开票金额
     */
    @Override
    public Optional<Map<String, BigDecimal>> orderNoList(CorpInvoiceRecordUpdateParam param, long start, int size) {
        PayBillParam billParam = new PayBillParam();
        billParam.setCommIdChain(param.getCommIdChain());
        billParam.setUserId(param.getPayBillParam().getUserId());
        billParam.setOrderId(param.getPayBillParam().getOrderId());
        billParam.setPayTimeFilter(param.getPayBillParam().getPayTimeFilter());
        billParam.setInCorpInvoice(true);
        billParam.setFlowTypeList(List.of(DepositFlowType.IN_FLOW));
        billParam.setAccountTypeList(List.of(PayAccountType.CORP)); // 查询企业客户的充值
        billParam.setTaxStatus(List.of(TaxStatus.NO)); // 未开票
        billParam.setStart(start)
            .setSize(size);
        List<PayBillInvoiceBi> biList = payBillRoDs.invoiceBi4Master(billParam).getData();
        if (CollectionUtils.isEmpty(biList)) {
            return Optional.empty();
        }
        return Optional.of(biList.stream().filter(Objects::nonNull).collect(
            Collectors.toMap(PayBillInvoiceBi::getOrderId, PayBillInvoiceBi::getCanInvoiceAmount)));
    }

    @Override
    public Optional<Map<String, BigDecimal>> orderNoList(List<String> orderNoList) {
        PayBillParam billParam = new PayBillParam();
        billParam.setOrderIdList(orderNoList);
        billParam.setInCorpInvoice(true);
        billParam.setFlowTypeList(List.of(DepositFlowType.IN_FLOW));
        billParam.setAccountTypeList(List.of(PayAccountType.CORP)); // 查询企业客户的充值
        billParam.setTaxStatus(List.of(TaxStatus.NO)); // 未开票
        billParam.setSize(orderNoList.size());
        List<PayBillInvoiceBi> biList = payBillRoDs.invoiceBi4Master(billParam).getData();
        if (CollectionUtils.isEmpty(biList)) {
            return Optional.empty();
        }
        return Optional.of(biList.stream().filter(Objects::nonNull).collect(
            Collectors.toMap(PayBillInvoiceBi::getOrderId, PayBillInvoiceBi::getCanInvoiceAmount)));
    }

    @Override
    public void updateException(CorpInvoiceRecordUpdateParam param, String applyNo, boolean append) {
        // nothing to do
    }
}
