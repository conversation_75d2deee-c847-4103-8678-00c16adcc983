package com.cdz360.biz.dc.domain.vo;

import com.cdz360.biz.dc.domain.SiteInMongoPo;
import com.cdz360.biz.model.cus.score.dto.UserScoreSettingLevelSiteGidDto;
import com.cdz360.biz.model.trading.coupon.vo.ActivityRunningVo;
import com.cdz360.biz.model.trading.coupon.vo.CouponVoEx;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * SiteInMongoVo
 *
 * @since 7/25/2023 3:15 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "场站信息扩展")
public class SiteInMongoVo extends SiteInMongoPo {
//    @Schema(description = "当前进行中的活动")
//    private List<ActivityRunningVo> activityRunningVoList;
//
//    @Schema(description = "当前可被领券活动")
//    private List<ActivityRunningVo> activityCanObtainList;
//
//    @Schema(description = "当前未使用券对应的活动")
//    private List<ActivityRunningVo> activityUnconsumedList;

    @Schema(description = "所有活动，包括已经参与和未参与")
    private List<ActivityRunningVo> activityAllList;

//    private List<CouponVoEx> couponVoExList;
    private List<String> gids;
    private List<UserScoreSettingLevelSiteGidDto> userScoreSettingList;
}