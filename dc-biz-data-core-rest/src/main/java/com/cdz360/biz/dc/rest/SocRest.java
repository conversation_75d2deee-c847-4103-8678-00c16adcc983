package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.SocStrategyService;
import com.cdz360.biz.ds.trading.ro.soc.ds.SiteSocCfgRoDs;
import com.cdz360.biz.model.trading.soc.param.QueryStrategyParam;
import com.cdz360.biz.model.trading.soc.param.SocStrategyDict;
import com.cdz360.biz.model.trading.soc.vo.SocCorpVo;
import com.cdz360.biz.model.trading.soc.vo.UserSocStrategyCreditCusVo;
import com.cdz360.biz.model.trading.soc.vo.UserSocStrategyVinVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * SocRest
 *
 * @since 8/11/2020 3:27 PM
 * <AUTHOR>
 */

@Slf4j
@RestController
@Tag(name = "场站SOC限制相关接口", description = "场站SOC")
public class SocRest {

    @Autowired
    private SiteSocCfgRoDs siteSocCfgRoDs;

    @Autowired
    private SocStrategyService socStrategyService;

    @Deprecated
    @GetMapping(value = "/dataCore/soc/queryBlocUserForSiteSoc")
    @Operation(summary = "根据siteid获取已配置soc优先策略的企业列表")
    public ListResponse<SocCorpVo> queryBlocUserForSiteSoc(@RequestParam(value = "siteId") String siteId) {
        List<SocCorpVo> ret = siteSocCfgRoDs.queryBlocUserForSiteSoc(siteId);
        return RestUtils.buildListResponse(ret);
    }

    @PostMapping(value = "/dataCore/soc/queryStrategy")
    @Operation(summary = "获取企业已配置soc优先策略的列表")
    public ListResponse<SocStrategyDict> queryStrategy(@RequestBody QueryStrategyParam param) {
        List<SocStrategyDict> ret = socStrategyService.queryStrategy(param);
        return RestUtils.buildListResponse(ret);
    }

    @PostMapping(value = "/dataCore/soc/queryCorpStrategyCreditCus")
    @Operation(summary = "获取已配置soc优先策略的企业列表")
    ListResponse<UserSocStrategyCreditCusVo> queryCorpStrategyCreditCus(@RequestBody QueryStrategyParam param) {
        List<UserSocStrategyCreditCusVo> ret = socStrategyService.queryCorpStrategyCreditCus(param);
        return RestUtils.buildListResponse(ret);
    }

    @PostMapping(value = "/dataCore/soc/queryCorpStrategyVin")
    @Operation(summary = "获取已配置soc优先策略的企业列表")
    ListResponse<UserSocStrategyVinVo> queryCorpStrategyVin(@RequestBody QueryStrategyParam param) {
        List<UserSocStrategyVinVo> ret = socStrategyService.queryCorpStrategyVin(param);
        return RestUtils.buildListResponse(ret);
    }

    @PostMapping(value = "/dataCore/soc/addCorpStrategyCreditCus")
    @Operation(summary = "新增已配置soc优先策略的企业列表")
    ObjectResponse<Integer> addCorpStrategyCreditCus(@RequestBody List<QueryStrategyParam> params) {
        Integer ret = socStrategyService.addCorpStrategyCreditCus(params);
        return RestUtils.buildObjectResponse(ret);
    }

    @PostMapping(value = "/dataCore/soc/removeCorpStrategyCreditCus")
    @Operation(summary = "删除已配置soc优先策略的企业列表")
    ObjectResponse<Integer> removeCorpStrategyCreditCus(@RequestBody List<QueryStrategyParam> params) {
        Integer ret = socStrategyService.removeCorpStrategyCreditCus(params);
        return RestUtils.buildObjectResponse(ret);
    }

    @PostMapping(value = "/dataCore/soc/addCorpStrategyVin")
    @Operation(summary = "新增已配置soc优先策略的企业列表Vin")
    ObjectResponse<Integer> addCorpStrategyVin(@RequestBody List<QueryStrategyParam> params) {
        Integer ret = socStrategyService.addCorpStrategyVin(params);
        return RestUtils.buildObjectResponse(ret);
    }
}