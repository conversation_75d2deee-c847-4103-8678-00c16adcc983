package com.cdz360.biz.dc.service.site;

import com.cdz360.biz.ds.trading.ro.site.ds.SiteOaDefaultConfigRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteOaDefaultConfigRwDs;
import com.cdz360.biz.model.trading.site.param.UpdateSiteOaDefaultValueParam;
import com.cdz360.biz.model.trading.site.po.SiteOaDefaultConfigPo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class SiteSettingService {

    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private SiteOaDefaultConfigRwDs siteOaDefaultConfigRwDs;

    @Autowired
    private SiteOaDefaultConfigRoDs siteOaDefaultConfigRoDs;

    public Mono<SitePo> updateOaDefaultValue(UpdateSiteOaDefaultValueParam param) {
        val site = siteRoDs.getSite(param.getSiteId());
        IotAssert.isNotNull(site, "场站ID无效");

        // 对象转化
        val updateList = param.getOaDefaultValueList().stream().map(x -> {
            val r = new SiteOaDefaultConfigPo();
            BeanUtils.copyProperties(x, r);
            return r.setSiteId(param.getSiteId());
        }).collect(Collectors.toList());

        int i = siteOaDefaultConfigRwDs.batchUpsetSiteOaDefaultConfig(updateList);
        return Mono.just(site);
    }

    public Mono<List<SiteOaDefaultConfigPo>> fetchOaDefaultValue(String siteId) {
        return Mono.just(siteId).map(siteOaDefaultConfigRoDs::fetchOaDefaultValue);
    }

    public Mono<SiteOaDefaultConfigPo> getOaDefaultValue(String siteId, String procDefKey) {
        return Mono.justOrEmpty(siteOaDefaultConfigRoDs.getOaDefaultValue(siteId, procDefKey));
    }
}
