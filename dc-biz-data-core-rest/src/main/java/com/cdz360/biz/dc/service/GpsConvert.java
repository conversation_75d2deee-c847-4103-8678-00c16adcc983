package com.cdz360.biz.dc.service;

/**
 * Description：坐标实体类
 * Created by：fucc on 2018/1/31.
 * Version：1.0
 */
public class GpsConvert {
    private double wgLat;
    private double wgLon;

    public GpsConvert(double wgLat, double wgLon) {
        setWgLat(wgLat);
        setWgLon(wgLon);
    }

    public double getWgLat() {
        return wgLat;
    }

    public void setWgLat(double wgLat) {
        this.wgLat = wgLat;
    }

    public double getWgLon() {
        return wgLon;
    }

    public void setWgLon(double wgLon) {
        this.wgLon = wgLon;
    }

    @Override
    public String toString() {
        return wgLat + "," + wgLon;
    }
}
