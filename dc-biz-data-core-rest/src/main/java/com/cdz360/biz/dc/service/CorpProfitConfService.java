package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.corp.type.CorpType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.dc.client.AuthCenterFeignClient;
import com.cdz360.biz.ds.trading.ro.corp.ds.CorpRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderRoDs;
import com.cdz360.biz.ds.trading.ro.profit.conf.ds.CorpProfitBaseRoDs;
import com.cdz360.biz.ds.trading.ro.profit.conf.ds.CorpProfitConfRoDs;
import com.cdz360.biz.ds.trading.ro.profit.conf.ds.CorpProfitSubRoDs;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderPayRwDs;
import com.cdz360.biz.ds.trading.rw.profit.conf.ds.CorpProfitBaseRwDs;
import com.cdz360.biz.ds.trading.rw.profit.conf.ds.CorpProfitConfRwDs;
import com.cdz360.biz.ds.trading.rw.profit.conf.ds.CorpProfitSubRwDs;
import com.cdz360.biz.model.trading.corp.po.CorpPo;
import com.cdz360.biz.model.trading.profit.conf.po.CorpProfitConfPo;
import com.cdz360.biz.model.trading.profit.conf.vo.CorpProfitBaseVo;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * CorpProfitConfService
 *
 * @since 2/23/2021 4:18 PM
 * <AUTHOR>
 */
@Service
@Slf4j
public class CorpProfitConfService {
    @Autowired
    private CorpProfitConfRoDs corpProfitConfRoDs;

    @Autowired
    private CorpProfitConfRwDs corpProfitConfRwDs;

    @Autowired
    private ChargerOrderRoDs chargerOrderRoDs;

    @Autowired
    private ChargerOrderPayRwDs chargerOrderPayRwDs;

    @Autowired
    private CorpProfitBaseRoDs corpProfitBaseRoDs;

    @Autowired
    private CorpProfitBaseRwDs corpProfitBaseRwDs;

    @Autowired
    private CorpProfitSubRoDs corpProfitSubRoDs;

    @Autowired
    private CorpProfitSubRwDs corpProfitSubRwDs;

    @Autowired
    private CorpRoDs corpRoDs;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;


    //    public List<CorpProfitConfPo> getCorpProfitConf(Long corpId) {
//        List<CorpProfitConfPo> ret = corpProfitConfRoDs.getListById(corpId);
//        if(CollectionUtils.isEmpty(ret)) {
//            return List.of();
//        }
//        return ret;
//    }
    public CorpProfitBaseVo getCorpProfitConf(Long corpId) {
        IotAssert.isNotNull(corpId, "请传入企业id");
        com.cdz360.biz.model.cus.corp.po.CorpPo corpPo = FeignResponseValidate.checkReturn(
            authCenterFeignClient.getCorp(
                corpId));
        CorpProfitBaseVo corpProfitBaseVo = new CorpProfitBaseVo();
        CorpProfitBaseVo corp = corpProfitBaseRoDs.getByCorpId(corpId);
        if (corp != null) {
            BeanUtils.copyProperties(corp, corpProfitBaseVo);
        }
        corpProfitBaseVo.setFullInvoicing(corpPo.getFullInvoicing())
            .setType(corpPo.getType());
        return corpProfitBaseVo;
    }

//    public BaseResponse disableCorpProfitConf(Long corpId) {
//        int ret = corpProfitConfRwDs.disableCorpProfitConf(corpId);
//        log.info("corpId: {}, 禁用{}个收益配置", corpId, ret);
//        return BaseResponse.success();
//    }
    @Transactional
    public BaseResponse disableCorpProfitConf(Long corpId) {
        IotAssert.isNotNull(corpId, "请传入企业id");
        CorpProfitBaseVo byCorpId = corpProfitBaseRoDs.getByCorpId(corpId);
        if(byCorpId == null) {
            log.warn("不存在有效的收益配置");
            return BaseResponse.success();
        }
//        IotAssert.isNotNull(byCorpId, "不存在有效的收益配置，请刷新后重试");
        boolean ret = corpProfitBaseRwDs.disableById(byCorpId.getId());
        log.info("corpId: {}, baseId: {}, 禁用基本公式： {}", corpId, byCorpId.getId(), ret);
        if(CollectionUtils.isNotEmpty(byCorpId.getCorpProfitSubList())) {
            int cnt = corpProfitSubRwDs.disableByBaseId(byCorpId.getId());
            log.info("corpId: {}, baseId: {}, 禁用区间： {}", corpId, byCorpId.getId(), cnt);
        }
        // 互联互通企业  停止全额开票开关
        authCenterFeignClient.updateCorpFullInvoice(corpId, Boolean.FALSE);
        return BaseResponse.success();
    }

//    @Transactional
//    public BaseResponse addCorpProfitConf(List<CorpProfitConfPo> confList) {
//        if(CollectionUtils.isNotEmpty(confList)) {
//            Long corpId = confList.get(0).getCorpId();
//            IotAssert.isNotNull(corpId, "请传入企业id");
//
//            this.disableCorpProfitConf(corpId);
//
//            int ret = corpProfitConfRwDs.batchInsert(confList);
//
//            log.info("corpId: {}, 新增{}个收益配置", corpId, ret);
//        }
//        return BaseResponse.success();
//    }

    @Transactional
    public BaseResponse addCorpProfitConf(CorpProfitBaseVo param) {

        final CorpPo corpById = corpRoDs.getCorpById(param.getCorpId(), true);
        IotAssert.isNotNull(corpById, "企业不存在");

        this.disableCorpProfitConf(param.getCorpId());

        boolean b = corpProfitBaseRwDs.insertCorpProfitBase(param);
        if(b) {
            log.info("新增基本公式成功: {}", JsonUtils.toJsonString(param));
            if(CollectionUtils.isNotEmpty(param.getCorpProfitSubList())) {
                param.getCorpProfitSubList().forEach(e -> e.setBaseId(param.getId()));
                int i = corpProfitSubRwDs.batchInsertCorpProfitSub(param.getCorpProfitSubList());
                log.info("新增区间count: {}", i);
            }

            if (CorpType.HLHT.equals(param.getType())) {
                authCenterFeignClient.updateCorpFullInvoice(param.getCorpId(),
                    param.getFullInvoicing());
            }
        } else {
            IotAssert.isTrue(false, "新增基本公式失败");
        }
        return BaseResponse.success();
    }

    public BaseResponse computePrevMonthlyProfit() {
//        this.computeMonthlyProfit();
        return BaseResponse.success();
    }

    @Async
    public void computeMonthlyProfit() {

        final Date MonthEnd = DateUtil.getThisMonth(new Date());
        final Date MonthStart = DateUtil.getPrevMonth(MonthEnd);

        List<CorpProfitConfPo> allList = corpProfitConfRoDs.getAllGradientList();
        if(CollectionUtils.isNotEmpty(allList)) {
            log.info("计算[{}, {}), 内订单的收益", MonthStart, MonthEnd);
            Map<Long, List<CorpProfitConfPo>> collect = allList.stream()
                    .collect(Collectors.groupingBy(CorpProfitConfPo::getCorpId));
            collect.keySet().stream().forEach(corpId -> {
                BigDecimal aim = chargerOrderRoDs.getCorpElecSum(corpId, MonthStart, MonthEnd);
                CorpProfitConfPo corpProfitConfPo = this.pickProfitConf(collect.get(corpId), aim);
                if(corpProfitConfPo != null) {
                    try {
                        log.info("使用{}计算收益", JsonUtils.toJsonString(corpProfitConfPo));
                        Integer count = chargerOrderPayRwDs.updateCorpGradientProfit(corpId,
                                MonthStart,
                                MonthEnd,
                                corpProfitConfPo.getElecFeeRate().movePointLeft(2),
                                corpProfitConfPo.getServFeeRate().movePointLeft(2));
                        log.info("更新了{}个订单计算收益", count);
                    } catch (Exception ex) {
                        log.error("企业计算收益异常: corpId = {}, err = {}", corpId, ex.getMessage(), ex);
                    }
                }
            });

        }
    }

    private CorpProfitConfPo pickProfitConf(List<CorpProfitConfPo> list, BigDecimal aim) {
        if(CollectionUtils.isEmpty(list)) {
            return null;
        }

        CorpProfitConfPo tail = list.get(list.size() - 1);
        if(tail.getElecRangeFrom().compareTo(aim) <= 0) {
            return tail;
        }

        int i = 0;
        while(i < list.size()) {
            if(list.get(i).getElecRangeFrom().compareTo(aim) <= 0 && list.get(i).getElecRangeTo().compareTo(aim) > 0) {
                return list.get(i);
            }
            i++;
        }

        return null;
   }
}