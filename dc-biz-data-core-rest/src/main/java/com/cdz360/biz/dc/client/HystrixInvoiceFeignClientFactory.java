package com.cdz360.biz.dc.client;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.oa.param.PrepaidInvoicingEditParam;
import com.chargerlinkcar.framework.common.domain.invoice.InvoicedRecordVo;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoiceApplyResultDTO;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoiceApplyResultOaDTO;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoicedRecordDTO;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceInfoParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordManualParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.SaveInvoiceRecordsParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceInfoVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2019/11/14 14:33
 */
@Slf4j
@Component
public class HystrixInvoiceFeignClientFactory implements FallbackFactory<InvoiceFeignClient> {
    @Override
    public InvoiceFeignClient create(Throwable cause) {

        log.error("【服务熔断】。Service = {},Message = {}", "DC-INVOICE", cause.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", "DC-INVOICE", cause.getStackTrace());

        return new InvoiceFeignClient() {
            @Override
            public ListResponse<InvoicedRecordVo> findByInvoiceIds(List<Long> invoiceIds) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<InvoicedRecordDTO> getRecordByOa(String procInstId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<InvoiceApplyResultDTO> saveCorpInvoiceRecords(
                SaveInvoiceRecordsParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Boolean> invoiceRecordManual(CorpInvoiceRecordManualParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<InvoicedRecordVo> getInvoicedRecordByApplyNo(String applyNo) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<CorpInvoiceInfoVo> getCorpInvoiceDetail(CorpInvoiceInfoParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse editRecordById(InvoicedRecordDTO param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Long> disposeByPrepaidProcess(PrepaidInvoicingEditParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<InvoiceApplyResultOaDTO> exportToInvoiceByOa(String procInstId) {
                return RestUtils.serverBusy4ObjectResponse();
            }
        };
    }
}
