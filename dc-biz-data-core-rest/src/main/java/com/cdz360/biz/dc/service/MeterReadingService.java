package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.ds.trading.ro.meter.ds.BiMeterRoDs;
import com.cdz360.biz.ds.trading.rw.meter.ds.BiMeterRwDs;
import com.cdz360.biz.model.trading.meter.po.BiMeterPo;
import com.cdz360.biz.utils.feign.iot.MeterFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * MeterReadingService
 *
 * @since 9/25/2020 11:16 AM
 * <AUTHOR>
 */
@Slf4j
@Service
public class MeterReadingService {

    @Autowired
    private BiMeterRoDs biMeterRoDs;

    @Autowired
    private BiMeterRwDs biMeterRwDs;

    @Autowired
    private MeterFeignClient meterFeignClient;

    public void getPrevDayReading(Date date) {
        log.info("抄表数据日差值");

        ListResponse<BiMeterPo> prevDayReading = meterFeignClient.getPrevDayReading(date);

        FeignResponseValidate.check(prevDayReading);

        if(CollectionUtils.isNotEmpty(prevDayReading.getData())) {
            log.info("插入抄表记录: {} 条", biMeterRwDs.batchInsert(prevDayReading.getData()));
        } else {
            log.info("没有需要插入的抄表记录。");
        }
    }




}