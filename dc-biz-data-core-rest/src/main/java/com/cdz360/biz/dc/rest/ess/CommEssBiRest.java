package com.cdz360.biz.dc.rest.ess;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.dc.service.ess.CommEssBiService;
import com.cdz360.biz.ess.model.param.EssMapDataParam;
import com.cdz360.biz.ess.model.vo.CommEssMapDataVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "工商储能设备统计操作接口", description = "工商储能设备统计操作接口合集")
@Slf4j
@RestController
@RequestMapping("/dataCore/comm/ess/bi")
public class CommEssBiRest {

    @Autowired
    private CommEssBiService commEssBiService;

    @Operation(summary = "工商储能地图数据统计")
    @PostMapping("/map/data")
    public Mono<ObjectResponse<CommEssMapDataVo>> commEssMapData(
        @RequestBody @Valid EssMapDataParam param) {
        log.info("工商储能地图数据统计: {}", param);
        return commEssBiService.commEssMapData(param);
    }

}
