package com.cdz360.biz.dc.service;

import com.cdz360.base.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.UUID;

/**
 * CouponTaskService
 *
 * @since 8/4/2020 10:25 AM
 * <AUTHOR>
 */
@Slf4j
@Service
public class CouponTaskService {


    @Autowired
    private CouponDictService couponDictService;

    @Autowired
    private CouponService couponService;

    @Autowired
    private ActivityService activityService;


    public void refresh(String trace) {
        if(StringUtils.isBlank(trace)) {
            trace = UUID.randomUUID().toString();
        }
        log.debug("刷新券模板: {}, modified {}", trace, couponDictService.refreshStatus2Expire());
        log.debug("刷新券: {}, modified {}", trace, couponService.refreshStatus());
        log.debug("刷新活动: {}, modified {}", trace, activityService.refreshStatus());
    }

}