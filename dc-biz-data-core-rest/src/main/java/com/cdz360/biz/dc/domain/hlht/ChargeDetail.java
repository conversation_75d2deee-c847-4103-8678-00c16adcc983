package com.cdz360.biz.dc.domain.hlht;
//
//import com.alibaba.fastjson.annotation.JSONField;
//import com.fasterxml.jackson.annotation.JsonProperty;
//import lombok.Data;
//
//import java.io.Serializable;
//
///**
// * <AUTHOR>
// *  充电明细信息
// * @since 2019-04-08 14:55
// */
//@Data
//public class ChargeDetail implements Serializable {
//
//    /**
//     * 开始时间
//     */
//    @JSONField(name = "DetailStartTime")
//    @JsonProperty(value = "DetailStartTime")
//    private String detailStartTime;
//    /**
//     * 结束时间
//     */
//    @JSONField(name = "DetailEndTime")
//    @JsonProperty(value = "DetailEndTime")
//    private String detailEndTime;
//    /**
//     * 时段电价
//     */
//    @JSONField(name="ElecPrice")
//    @JsonProperty(value="ElecPrice")
//    private double elecPrice;
//    /**
//     * 时段服务费价格
//     */
//    @JSONField(name = "SevicePrice")
//    @JsonProperty(value = "SevicePrice")
//    private double sevicePrice;
//    /**
//     * 时段充电量
//     */
//    @JSONField(name = "DetailPower")
//    @JsonProperty(value = "DetailPower")
//    private double detailPower;
//    /**
//     * 时段电费
//     */
//    @JSONField(name = "DetailElecMoney")
//    @JsonProperty(value = "DetailElecMoney")
//    private double detailElecMoney;
//    /**
//     * 时段服务费
//     */
//    @JSONField(name = "DetailSeviceMoney")
//    @JsonProperty(value = "DetailSeviceMoney")
//    private double detailSeviceMoney;
//
//}
