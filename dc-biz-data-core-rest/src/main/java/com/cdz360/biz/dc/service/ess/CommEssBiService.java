package com.cdz360.biz.dc.service.ess;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ds.ess.ro.data.ds.EssDailyRoDs;
import com.cdz360.biz.ess.model.param.EssMapDataParam;
import com.cdz360.biz.ess.model.vo.CommEssMapDataVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class CommEssBiService {

    @Autowired
    private EssDailyRoDs essDailyRoDs;

    public Mono<ObjectResponse<CommEssMapDataVo>> commEssMapData(EssMapDataParam param) {
        return Mono.just(param)
            .map(essDailyRoDs::commEssMapData)
            .map(RestUtils::buildObjectResponse);
    }

}
