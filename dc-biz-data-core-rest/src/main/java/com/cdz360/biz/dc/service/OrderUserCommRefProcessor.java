package com.cdz360.biz.dc.service;

import com.chargerlinkcar.framework.common.domain.vo.ChargerOrder;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

@Slf4j
public class OrderUserCommRefProcessor implements Runnable {

    // 这里不需要线程安全
    private List<ChargerOrder> list = new ArrayList<>();
    private LinkedBlockingDeque<ChargerOrder> queue = new LinkedBlockingDeque<>();
    private AtomicLong queueSize = new AtomicLong(0L);

    private OrderUserCommRefProcessorCallback<ChargerOrder> callback;

    public OrderUserCommRefProcessorCallback<ChargerOrder> getCallback() {
        return callback;
    }

    public void setCallback(OrderUserCommRefProcessorCallback<ChargerOrder> callback) {
        this.callback = callback;
    }

    public void push(ChargerOrder msg) {
        if (this.list.contains(msg)) {
            return;
        }

        try {
            this.list.add(msg);
            this.queue.put(msg);
            queueSize.addAndGet(1L);
        } catch (InterruptedException e) {
            log.error("queue.size: {}, error: {}", queue.size(), e.getMessage(), e);
        }
    }

    @Override
    public void run() {
        do {
            ChargerOrder request = null;
            try {
                request = queue.poll(1L, TimeUnit.SECONDS);
                if (request != null) {
                    long size = queueSize.addAndGet(-1L);
                    if (size > 100 && size % 100 == 0) {
                        log.warn("堆积过大. size: {}", size);
                    }

                    if (callback != null) {
                        callback.onArrived(request);
                    }

                    this.list.remove(request);
                }
            } catch (Exception e) {
                log.error("queue.size: {}, error: {}", queue.size(), e.getMessage(), e);
                if (request != null) {

                    if (callback != null) {
                        callback.onFailed(request, e);
                    }
                }
            }
        } while (true);
    }
}
