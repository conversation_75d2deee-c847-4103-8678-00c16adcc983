package com.cdz360.biz.dc.service.site;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteWeatherRoDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteWeatherRwDs;
import com.cdz360.biz.model.trading.site.po.SiteWeatherPo;
import com.cdz360.biz.model.trading.site.po.WeatherDaily;
import com.cdz360.biz.model.trading.site.po.WeatherHourly;
import com.cdz360.biz.model.trading.site.vo.SiteWeatherVo;
import com.chargerlinkcar.framework.common.utils.DateUtils;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.fasterxml.jackson.core.type.TypeReference;
import java.lang.reflect.Type;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SiteWeatherService {

    private static final DateTimeFormatter TIME_POINT_FORMATTER = DateTimeFormatter.ofPattern(
        "HH:mm");
    @Autowired
    private SiteWeatherRwDs siteWeatherRwDs;
    @Autowired
    private SiteWeatherRoDs siteWeatherRoDs;

    public BaseResponse refreshSiteWeather(List<SiteWeatherPo> poList) {
        IotAssert.isTrue(CollectionUtils.isNotEmpty(poList), "入参无效");

        return siteWeatherRwDs.insertOrUpdate(poList) ? RestUtils.success() :
            RestUtils.fail(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "刷新场站天气失败");
    }

    public ObjectResponse<SiteWeatherVo> findBySiteId(String siteId) {
        IotAssert.isTrue(StringUtils.isNotBlank(siteId), "入参无效");

        SiteWeatherPo po = siteWeatherRoDs.findBySiteId(siteId);

        SiteWeatherVo res = new SiteWeatherVo();

        if (po == null) {
            res.setDate(DateUtils.toDate(LocalDate.now()));
            return RestUtils.buildObjectResponse(res);
        }

        res.setDate(po.getDate())
            .setHumidity(po.getHumidity())
            .setWindSpeed(po.getWindSpeed())
            .setPressure(po.getPressure());

        LocalDateTime now = LocalDateTime.now();
        if (CollectionUtils.isNotEmpty(po.getHourly())) {
            Optional<WeatherHourly> hour = JsonUtils.fromJson(
                    JsonUtils.toJsonString(po.getHourly()),
                    new TypeReference<List<WeatherHourly>>() {
                        @Override
                        public Type getType() {
                            return super.getType();
                        }
                    })
//                Optional<WeatherHourly> hour = JSON.parseArray(JsonUtils.toJsonString(po.getHourly()),
//                    WeatherHourly.class)
                .stream().filter(e -> StringUtils.isNotBlank(e.getTime()))
                .peek(e -> e.setTime(e.getTime().replace(":", "")))
                .sorted(Comparator.comparing(WeatherHourly::getTime).reversed())
                .filter(e -> Integer.parseInt(now.format(TIME_POINT_FORMATTER).replace(":", ""))
                    > Integer.parseInt(e.getTime()))
                .findFirst();
            hour.ifPresent(weatherHourly -> res.setWeather(weatherHourly.getWeather())
                .setTemp(weatherHourly.getTemp())
                .setImg(weatherHourly.getImg()));
        }

        if (CollectionUtils.isNotEmpty(po.getDaily())) {
            //当天日出日落
//            JSON.parseArray(JsonUtils.toJsonString(po.getDaily()), WeatherDaily.class).stream().sorted(Comparator.comparing(e -> e.getDate()))
//                    .findFirst().ifPresent(e -> {
//                        if (e.getDate() != null && e.getDate().equals(DateUtils.toDate(now))) {
//                            res.setSunSet(e.getSunSet())
//                                    .setSunRise(e.getSunRise());
//                        }
//                    });
            JsonUtils.fromJson(JsonUtils.toJsonString(po.getDaily()),
                    new TypeReference<List<WeatherDaily>>() {
                    }).stream().min(Comparator.comparing(WeatherDaily::getDate))
                .ifPresent(weather -> res.setSunSet(weather.getSunSet())
                    .setSunRise(weather.getSunRise()));

            res.setNextThreeDays(
                JsonUtils.fromJson(JsonUtils.toJsonString(po.getDaily()),
                        new TypeReference<List<WeatherDaily>>() {
                            @Override
                            public Type getType() {
                                return super.getType();
                            }
                        })
//                JSON.parseArray(JsonUtils.toJsonString(po.getDaily()), WeatherDaily.class)
                    .stream()
                    .filter(e -> e.getDate() != null && e.getDate().after(DateUtils.toDate(now)))
                    .sorted((a, b) -> {
                        if (a.getDate().getTime() > b.getDate().getTime()) {
                            return 1;
                        } else {
                            return -1;
                        }
                    })
                    .limit(3)
                    .map(e -> {
                        SiteWeatherVo.SimpleWeather temp = new SiteWeatherVo.SimpleWeather();
                        temp.setDate(e.getDate());
                        if (e.getDay() != null) {
                            temp.setTempHigh(e.getDay().getTempHigh())
                                .setWeather(e.getDay().getWeather());
                        }

                        if (e.getNight() != null) {
                            temp.setTempLow(e.getNight().getTempLow());
                        }
                        return temp;
                    })
                    .collect(Collectors.toList()));
        }

        return RestUtils.buildObjectResponse(res);
    }

}
