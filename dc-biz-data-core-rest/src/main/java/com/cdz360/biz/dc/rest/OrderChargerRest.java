package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.ChargerOrderBizService;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.site.vo.SiteChargeJobPlugVo;
import com.chargerlinkcar.framework.common.domain.OrderReserveVo;
import com.chargerlinkcar.framework.common.domain.request.StartChargerRequest;
import com.chargerlinkcar.framework.common.domain.request.StopChargerRequest;
import com.chargerlinkcar.framework.common.domain.vo.*;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import java.text.ParseException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.client.loadbalancer.Response;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.util.List;

/**
 * 管理平台开启充电
 */
@Slf4j
@RestController
public class OrderChargerRest {

    @Autowired
    private ChargerOrderBizService chargerOrderBizService;


    /**
     * 后台开启充电
     * @param chargerRequest
     * @return
     */
    @PostMapping("/dataCore/orderCharger/webStartCharger")
    public BaseResponse webStartCharger(@RequestBody StartChargerRequest chargerRequest) {
        chargerOrderBizService.webStartCharger(chargerRequest);
        return BaseResponse.success();
    }

    /**
     * 后台关闭充电
     * @param stopRequest
     * @return
     */
    @PostMapping("/dataCore/orderCharger/webStopCharger")
    public BaseResponse webStopCharger(@RequestBody List<StopChargerRequest> stopRequest) {
        chargerOrderBizService.webStopCharger(OrderStartType.MGM_WEB_MANUAL, stopRequest);
        return BaseResponse.success();
    }

//    @PostMapping("/dataCore/orderCharger/catQueue")
//    public BaseResponse webStartCharger() throws InterruptedException {
//        chargerOrderBizService.show();
//        return BaseResponse.success();
//    }

    /**
     * 获取云端充电请求队列下发情况
     * @param plugNoList
     * @return
     */
    @PostMapping("/dataCore/orderCharger/getReserveInfoByPlugNoList")
    public ObjectResponse<OrderReserveVo> getReserveInfoByPlugNoList(@RequestBody List<String> plugNoList) {
        return new ObjectResponse<>(chargerOrderBizService.getReserveInfoByPlugNoList(plugNoList));
    }

    /**
     *  根据plugNo查询定时充电任务信息
     * @param plugNoList
     * @return
     */
    @PostMapping("/dataCore/siteChargeJob/getSiteJobByPlugNoList")
    public ListResponse<SiteChargeJobPlugVo> getSiteJobByPlugNoList(@RequestBody List<String> plugNoList) {
        log.info("plugNoList: {}", JsonUtils.toJsonString(plugNoList));
        return RestUtils.buildListResponse(chargerOrderBizService.getSiteJobByPlugNoList(plugNoList));
    }

    @Operation(summary = "获取充电订单列表")
    @PostMapping(value = "/dataCore/order/listChargerOrder")
    public Mono<ListResponse<ChargerOrderVo>> listChargerOrder(
            ServerHttpRequest request, @RequestBody ListChargeOrderParam param) {
        log.debug("获取充电订单列表: {}, param = {}",
                LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        return chargerOrderBizService.listChargerOrder(param);
    }

    @Operation(summary = "充电订单按照场站汇总")
    @PostMapping(value = "/dataCore/order/chargerOrderGroupBySite")
    public ListResponse<ChargerOrderSite> chargerOrderGroupBySite(
            ServerHttpRequest request, @RequestBody ListChargeOrderParam param) {
        log.debug("获取充电订单列表: {}, param = {}",
                LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        return chargerOrderBizService.chargerOrderGroupBySite(param);
    }

    @Operation(summary = "获取企业客户开票充电订单尖峰平谷金额汇总")
    @PostMapping(value = "/dataCore/order/chargerOrderGroupByTimeShareFee")
    public ListResponse<OrderTimeShareBiVo> chargerOrderGroupByTimeShareFee(
            ServerHttpRequest request, @RequestBody ListChargeOrderParam param) {
        log.debug("获取充电订单列表: {}, param = {}",
                LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        return chargerOrderBizService.chargerOrderGroupByTimeShareFee(param);
    }

    @Operation(summary = "获取企业充电订单汇总")
    @PostMapping(value = "/dataCore/order/chargeOrderBiForCorp")
    public ObjectResponse<OrderBiVo> chargeOrderBiForCorp(
            ServerHttpRequest request, @RequestBody ListChargeOrderParam param) {
        log.debug("获取企业充电订单汇总: {}, param = {}",
                LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        return chargerOrderBizService.chargeOrderBiForCorp(param);
    }

    @Operation(summary = "记录拔枪时间")
    @PostMapping(value = "/dataCore/order/setPlugOutTime")
    public BaseResponse setPlugOutTime(
            ServerHttpRequest request, @RequestParam("orderNo") String orderNo) {
        log.debug("记录拔枪时间: {}, orderNo = {}",
                LoggerHelper2.formatEnterLog(request), orderNo);
        return chargerOrderBizService.setPlugOutTime(orderNo);
    }

    @Operation(summary = "获取充电订单列表(含尖峰品谷分时信息 互联互通使用)")
    @PostMapping(value = "/dataCore/order/listChargerOrder4Hlht")
    public Mono<ListResponse<ChargerOrderVo>> listChargerOrder4Hlht(
        ServerHttpRequest request, @RequestBody ListChargeOrderParam param) {
        log.debug("获取充电订单列表: {}, param = {}",
            LoggerHelper2.formatEnterLog(request), JsonUtils.toJsonString(param));
        return chargerOrderBizService.listChargerOrder4Hlht(param);
    }
}
