package com.cdz360.biz.dc.service.abc;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ds.trading.ro.coupon.ds.AbcCouponRoDs;
import com.cdz360.biz.ds.trading.rw.coupon.ds.AbcCouponRwDs;
import com.cdz360.biz.model.trading.coupon.dto.AbcCouponDto;
import com.cdz360.biz.model.trading.coupon.po.AbcCouponPo;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class AbcCouponService {

    @Autowired
    private AbcCouponRoDs abcCouponRoDs;

    @Autowired
    private AbcCouponRwDs abcCouponRwDs;

    @Transactional
    public Mono<AbcCouponDto> saveAbcCoupon(AbcCouponDto dto) {
        IotAssert.isNotBlank(dto.getPhone(), "手机号不能为空");
        IotAssert.isNotBlank(dto.getCouponId(), "优惠券ID不能为空");
        IotAssert.isNotNull(dto.getTotalAmount(), "总金额金额不能为空");
        IotAssert.isNotNull(dto.getPayAmount(), "实付金额金额不能为空");
        IotAssert.isNotNull(dto.getCouponAmount(), "优惠券金额金额不能为空");

        // 通过手机号和优惠券号查询
        AbcCouponPo couponPo = abcCouponRwDs.getNotUsedByPhoneAndCouponId(
            dto.getPhone(), dto.getCouponId(), true);
        if (null != couponPo) {
            couponPo.setCouponAmount(dto.getCouponAmount())
                .setPayAmount(dto.getPayAmount())
                .setTotalAmount(dto.getTotalAmount());
            boolean b = abcCouponRwDs.updateAbcCoupon(couponPo);
            if (!b) {
                log.warn("更新优惠券不成功");
            }
        } else {
            AbcCouponPo newPo = new AbcCouponPo();
            BeanUtils.copyProperties(dto, newPo);
            boolean b = abcCouponRwDs.insertAbcCoupon(newPo);
            if (!b) {
                log.warn("新增优惠券不成功");
            }
        }

        return Mono.just(dto);
    }

    public Mono<ObjectResponse<AbcCouponDto>> userTodayUsedAbcCoupon(AbcCouponDto dto) {
        IotAssert.isNotBlank(dto.getPhone(), "手机号不能为空");
        AbcCouponPo couponPo = abcCouponRoDs.getTodayUsedByPhone(dto.getPhone());
        if (null != couponPo) {
            BeanUtils.copyProperties(couponPo, dto);
        }
        return Mono.just(RestUtils.buildObjectResponse(null != couponPo ? dto : null));
    }
}
