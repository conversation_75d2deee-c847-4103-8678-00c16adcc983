package com.cdz360.biz.dc.service.site;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.service.GpsConvert;
import com.cdz360.biz.dc.service.PositionUtil;
import com.cdz360.biz.model.finance.type.BizType;
import com.cdz360.biz.model.site.param.AddSiteParam;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.chargerlinkcar.framework.common.domain.SiteDetailInfoVo;
import com.chargerlinkcar.framework.common.domain.vo.SiteSimpleInfoVo;
import com.chargerlinkcar.framework.common.utils.IDUtil;
import java.util.stream.Collectors;

public class SiteConvert {

    /**
     * 转换为Site实体类
     *
     * @param request
     * @return
     */
    public static SitePo generateSiteEntity(AddSiteParam request) {
        if (request == null) {
            return null;
        }
        SitePo entity = new SitePo();
        // 站点ID
        entity.setId(IDUtil.generateSiteId());
        //try {
        String idNo = entity.getId();   // 后续版本可移除
        entity.setIdNo(idNo);
//        } catch (Exception e) {
//            throw new DcServiceException("新增站点失败，生成站点编号出错");
//        }
        // 场站支持电流形式
        entity.setSupplyType(request.getSupplyType());
        // 站点名称
        entity.setSiteName(request.getSiteName());
        // 站点简称
        entity.setSiteShortName(request.getSiteShortName());
        // 经度
        entity.setLongitude(request.getLongitude());
        // 纬度
        entity.setLatitude(request.getLatitude());
        if (StringUtils.isNotBlank(request.getTimeZone())) {
            entity.setTimeZone(request.getTimeZone());
        }
        // 站点地址
        entity.setAddress(request.getAddress());
        // 省
        entity.setProvince(request.getProvince());
        // 市
        entity.setCity(request.getCity());
        // 区
        entity.setArea(request.getArea());
        // 服务号码
        entity.setPhone(request.getPhone());
        // 运营商ID
        entity.setOperateId(request.getOperateId());
        // 运营商名称
        entity.setOperateName(request.getOperateName());
        entity.setOperateCorpCode(request.getOperateCorpCode());
        // 物业ID或代理商账号ID
        entity.setMerchantId(request.getMerchantId());
        // 联系人
        entity.setContacts(request.getContacts());
        // 联系人号码
        entity.setContactsPhone(request.getContactsPhone());
        // 备注
        entity.setRemark(request.getRemark());
        // 站点类型**0-未知 1-公共 2-公交 3-物流 4-混合**
        entity.setType(request.getType());
        // 工作日服务时间
        entity.setServiceWorkdayTime(request.getServiceWorkdayTime());
        // 节假日服务时间
        entity.setServiceHolidayTime(request.getServiceHolidayTime());
        // 停车是否收费 **0-未知 1-收费 2-免费**
        entity.setPark(request.getPark());
        // 停车费
        entity.setParkFee(request.getParkFee());
        // 否需要预约 **0-未知 1-需要预约 2-不需要预约**
        entity.setAppoint(request.getAppoint());
        // 使用范围**0-未知 1-对外开放 2-内部使用 3-特定人群**
        entity.setScope(request.getScope());
        // 上线时间
        entity.setOnlineDate(request.getOnlineDate());
        // 站点图片
        entity.setImages(JsonUtils.toJsonString(request.getImages()));
        // 支持品牌
        //entity.setBrandIds(request.getBrandIds());
        // 支付方式
        //entity.setAppPay(request.getPayMod());
        // 收费说明
        entity.setFeeDescription(request.getFeeDescription());
        // 发票描述
        entity.setInvoiceDesc(request.getInvoiceDesc());
        if (null != request.getBizType()) {
            entity.setBizType(request.getBizType().getCode());
        }
        entity.setBizName(request.getBizName());
        if (CollectionUtils.isNotEmpty(request.getPayChannels())) {
            entity.setPayChannels(request.getPayChannels()
                .stream().map(Enum::name)
                .collect(Collectors.joining(",")));
        }

        return entity;
    }

    /**
     * @param site
     * @return
     */
    public static SiteDetailInfoVo convertToSiteVo(SitePo site) {
        if (site == null) {
            return null;
        }
        SiteDetailInfoVo vo = new SiteDetailInfoVo();
        // 站点ID
        vo.setId(site.getId());
        // 站点编号
        vo.setIdNo(site.getIdNo());
        // 站点名称
        vo.setSiteName(site.getSiteName());
        // 站点简称
        vo.setSiteShortName(site.getSiteShortName());
        // 场站支持充电桩电流形式
        vo.setSupplyType(site.getSupplyType());
        // 经度
        vo.setLongitude(site.getLongitude());
        // 纬度
        vo.setLatitude(site.getLatitude());
        //处理微信小程序需要的经纬度
        GpsConvert gpsConvert = PositionUtil.gps84_To_Gcj02(site.getLatitude().doubleValue(),
            site.getLongitude().doubleValue());
        if (null != gpsConvert) {
            vo.setLatitudeWX(gpsConvert.getWgLat());
            vo.setLongitudeWX(gpsConvert.getWgLon());
        }
        // 站点地址
        vo.setAddress(site.getAddress());
        // 省份编码
        vo.setProvince(site.getProvince());
        // 城市编码
        vo.setCity(site.getCity());
        // 区域编码
        vo.setArea(site.getArea());
        // 服务号码
        vo.setPhone(site.getPhone());
        // 站点状态{@link com.chargerlink.device.business.constant.SiteStatusEnum}
        vo.setStatus(site.getStatus());
        vo.setTopCommId(site.getTopCommId());
        // 运营商ID
        vo.setOperateId(site.getOperateId());
        // 运营商名称
        vo.setOperateName(site.getOperateName());
        vo.setOperateCorpCode(site.getOperateCorpCode());
        // 物业ID或代理商账号ID
        vo.setMerchantId(site.getMerchantId());
        // 联系人
        vo.setContacts(site.getContacts());
        // 联系人号码
        vo.setContactsPhone(site.getContactsPhone());
        // 备注
        vo.setRemark(site.getRemark());
        // 站点类型**0-未知 1-公共 2-公交 3-物流 4-混合**
        vo.setType(site.getType());
        // 工作日服务时间
        vo.setServiceWorkdayTime(site.getServiceWorkdayTime());
        // 节假日服务时间
        vo.setServiceHolidayTime(site.getServiceHolidayTime());
        // 停车是否收费 **0-未知 1-收费 2-免费**
        vo.setPark(site.getPark());
        // 停车费: 单位元
        vo.setParkFee(site.getParkFee());
        //其他服务
        vo.setServiceInfo(site.getServiceInfo());
//        if (StringUtils.isNotBlank(site.getParkFee())) {
//            // 考虑小数
//            if (site.getParkFee().contains(".")) {
//                vo.setParkFee((int) (Double.valueOf(site.getParkFee()) * 100));
//            } else {
//                vo.setParkFee(Integer.valueOf(site.getParkFee()) * 100);
//            }
//        } else {
//            vo.setParkFee(0);
//        }
        // 是否需要预约 **0-未知 1-需要预约 2-不需要预约**
        vo.setAppoint(site.getAppoint() == null ? 0 : site.getAppoint());
        // 使用范围**0-未知 1-对外开放 2-内部使用 3-特定人群**
        vo.setScope(site.getScope());
        // 0元电价是否显示
        vo.setIsHidden(site.getIsHidden());
        // 上线时间
        vo.setOnlineDate(site.getOnlineDate());
        // 最后更新时间
        vo.setUpdateTime(site.getUpdateTime());
        // 创建时间
        vo.setCreateTime(site.getCreateTime());
        // 站点图片
        //vo.setImages(site.getImages());
        // 支持品牌
        //vo.setBrandIds(site.getBrandIds());
        // 支付方式
        vo.setPayMod(site.getAppPay());
        // 收费方式
        vo.setFeeDescription(site.getFeeDescription());
        // 发票提供方
        vo.setInvoiceDesc(site.getInvoiceDesc());
        // 获取国家、城市ID
        vo.setCityId(site.getCityId());
        vo.setCountryId(site.getCountryId());
        //收费范围 最小
        vo.setFeeMin(site.getFeeMin() == null ? 0L : site.getFeeMin());
        //收费范围 最大
        vo.setFeeMax(site.getFeeMax() == null ? 0L : site.getFeeMax());
        //站点默认绑定计费模板Id
        vo.setTemplateId(site.getTemplateId());
        //站点默认绑定计费模板名称
        vo.setTemplateName(site.getTemplateName());
        vo.setBizType(
            null == site.getBizType() ? BizType.UNKNOWN : BizType.valueOf(site.getBizType()));
        vo.setBizName(site.getBizName());
        vo.setAcEvseNum(site.getAcEvseNum());
        vo.setDcEvseNum(site.getDcEvseNum());
        vo.setAcPlugNum(site.getAcPlugNum());
        vo.setDcPlugNum(site.getDcPlugNum());
        vo.setAcPower(site.getAcPower());
        vo.setDcPower(site.getDcPower());
        vo.setOpenSiteId(site.getOpenSiteId());
        vo.setForbiddenClient(site.getForbiddenClient());
        vo.setGcType(site.getGcType());

        // 储能相关字段
        vo.setEssCapacity(site.getEssCapacity());
        vo.setEssInPriceId(site.getEssInPriceId());
        vo.setEssOutPriceId(site.getEssOutPriceId());
        vo.setEssPower(site.getEssPower());

        return vo;
    }

    /**
     * @param site
     * @return
     */
    public static SiteSimpleInfoVo convertToSiteSimpleInfoVo(SitePo site) {
        if (site == null) {
            return null;
        }
        SiteSimpleInfoVo vo = new SiteSimpleInfoVo();
        // 站点id
        vo.setSiteId(site.getId());
        // 站点名称
        vo.setSiteName(site.getSiteName());
        //站点自编号
        vo.setSiteNo(site.getSiteNo());
        //冻结金额
        vo.setFrozenAmount(site.getFrozenAmount());
        // 站点地址
        vo.setAddress(site.getAddress());
        // 省份编码
        vo.setProvince(Integer.valueOf(site.getProvince()));
        // 城市编码
        vo.setCity(Integer.valueOf(site.getCity()));
        // 站点状态 {@link com.chargerlink.device.business.constant.SiteStatusEnum}
        vo.setStatus(site.getStatus());
        //经纬度
        vo.setLatitude(site.getLatitude());
        vo.setLongitude(site.getLongitude());
        vo.setTopCommId(site.getTopCommId());
        // 运营商ID
        vo.setOperateId(site.getOperateId());
        // 运营商名称
        vo.setOperateName(site.getOperateName());
        // 站点类型**0-未知 1-公共 2-公交 3-物流 4-混合**
        vo.setType(site.getType());
        // 工作日服务时间
        vo.setServiceWorkdayTime(site.getServiceWorkdayTime());
        // 节假日服务时间
        vo.setServiceHolidayTime(site.getServiceHolidayTime());
        // 使用范围**0-未知 1-对外开放 2-内部使用 3-特定人群**
        vo.setScope(site.getScope());
        // 上线时间
        vo.setOnlineDate(site.getOnlineDate());
        //收费说明
        vo.setFeeDescription(site.getFeeDescription());
        //收费范围 最小
        vo.setFeeMin(site.getFeeMin().intValue());
        //收费范围 最大
        vo.setFeeMax(site.getFeeMax().intValue());
        //站点手机号
        vo.setContactsPhone(site.getContactsPhone());
        //计费模板
        vo.setTemplateId(site.getTemplateId());
        vo.setTemplateName(site.getTemplateName());
        vo.setBizType(site.getBizType());
        vo.setBizName(site.getBizName());
        vo.setDefaultPayType(site.getDefaultPayType());
        vo.setPayAccountId(site.getPayAccountId());
        return vo;
    }

//    /**
//     * @param site
//     * @return
//     */
//    public static SiteElasticsearch convertToSiteElasticsearch(Site site) {
//        SiteElasticsearch entity = new SiteElasticsearch();
//        // 站点ID
//        entity.setSiteId(site.getId());
//        // 站点名称
//        entity.setSiteName(site.getName());
//        // 站点地址
//        entity.setAddress(site.getAddress());
//        // 省份编码
//        entity.setProvince(site.getProvince());
//        // 城市编码
//        entity.setCity(site.getCity());
//        // 运营商ID
//        entity.setOperateId(site.getOperateId());
//        // 运营商名称
//        entity.setOperateName(site.getOperateName());
//        // 站点类型**0-未知 1-公共 2-公交 3-物流 4-混合**
//        entity.setType(site.getType());
//        // 工作日服务时间
//        entity.setServiceWorkdayTime(site.getServiceWorkdayTime());
//        // 节假日服务时间
//        entity.setServiceHolidayTime(site.getServiceHolidayTime());
//        // 使用范围**0-未知 1-对外开放 2-内部使用 3-特定人群**
//        entity.setScope(site.getScope());
//        // 停车是否收费 **0-未知 1-收费 2-免费**
//        entity.setPark(site.getPark());
//        // 停车费
//        entity.setParkFee(site.getParkFee());
//        // 站点图片
//        entity.setImages(site.getImages());
//        // 支持品牌
//        entity.setBrandIds(site.getBrandIds());
//        // 站点坐标
//        entity.setLocation(site.getLatitude(), site.getLongitude());
//        // 站点状态
//        entity.setStatus(site.getStatus());
//        //最小值
//        entity.setFeeMin(site.getFeeMin());
//        //最大值
//        entity.setFeeMax(site.getFeeMax());
//        entity.setArea(site.getArea());
//        return entity;
//    }
}
