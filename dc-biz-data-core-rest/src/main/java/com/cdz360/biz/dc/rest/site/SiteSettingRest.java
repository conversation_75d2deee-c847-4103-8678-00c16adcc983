package com.cdz360.biz.dc.rest.site;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.site.SiteSettingService;
import com.cdz360.biz.model.trading.site.param.UpdateSiteOaDefaultValueParam;
import com.cdz360.biz.model.trading.site.po.SiteOaDefaultConfigPo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "场站配置相关接口", description = "场站配置相关接口")
@RequestMapping("/dataCore/site/setting")
public class SiteSettingRest {

    @Autowired
    private SiteSettingService siteSettingService;

    @Operation(summary = "更新场站流程默认值配置项", description = "关联接口: /updatePersonalise")
    @PostMapping(value = "/updateOaDefaultValue")
    public Mono<ObjectResponse<SitePo>> updateOaDefaultValue(
        ServerHttpRequest request, @RequestBody UpdateSiteOaDefaultValueParam param) {
        log.info("更新场站流程默认值配置项: {}, param = {}",
            LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return siteSettingService.updateOaDefaultValue(param)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "获取场站流程默认值配置项")
    @GetMapping(value = "/fetchOaDefaultValue")
    public Mono<ListResponse<SiteOaDefaultConfigPo>> fetchOaDefaultValue(
        ServerHttpRequest request, @ApiParam("场站ID") @RequestParam("siteId") String siteId) {
        log.info("获取场站流程默认值配置项: {}", LoggerHelper2.formatEnterLog(request));
        return siteSettingService.fetchOaDefaultValue(siteId)
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "获取场站指定流程默认值配置项")
    @GetMapping(value = "/getOaDefaultValue")
    public Mono<ObjectResponse<SiteOaDefaultConfigPo>> getOaDefaultValue(
        ServerHttpRequest request, @ApiParam("场站ID") @RequestParam("siteId") String siteId,
        @ApiParam("oa流程定义KEY") @RequestParam("procDefKey") String procDefKey) {
        log.info("获取场站流程默认值配置项: {}", LoggerHelper2.formatEnterLog(request));
        return siteSettingService.getOaDefaultValue(siteId, procDefKey)
            .map(RestUtils::buildObjectResponse)
            .switchIfEmpty(Mono.just(RestUtils.buildObjectResponse(null)));
    }
}
