package com.cdz360.biz.dc.domain;

import com.cdz360.biz.model.finance.type.TaxType;
import com.cdz360.biz.model.invoice.type.InvoicedStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 对接发票系统返回开票信息
 *
 * <AUTHOR>
 * @since 2019/11/13 10:21
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode
@ToString
public class TaxInfo {
    @Schema(description = "发票记录Id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long id;

    @Schema(description = "发票类型(税种): UNKNOWN(0)-未知," +
            "NORMAL_TAX(2)-个人普通发票,PREPAY_TAX(3)-企业普通发票," +
            "SPECIAL_VAT(5)-企业专业发票", example = "UNKNOWN")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TaxType taxType;

    @Schema(description = "税票号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String taxNo;

    @Schema(description = "发票状态: SUBMITTED-审核中(未导出),REVIEWED-审核中(已导出)," +
            "AUDIT_FAILED-审核未通过(未导出),INVOICING_FAIL-开票失败(已导出)," +
            "COMPLETED-已开具,NOT_SUBMITTED-待提交,RED_DASHED-已红冲,INVALID-已作废",
            example = "SUBMITTED")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InvoicedStatus status;
}
