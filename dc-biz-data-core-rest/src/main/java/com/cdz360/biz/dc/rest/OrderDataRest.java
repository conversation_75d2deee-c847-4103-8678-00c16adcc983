package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.LogHelper;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.service.ChargerOrderAnalyseService;
import com.cdz360.biz.dc.service.ChargerQueryService;
import com.cdz360.biz.dc.service.OrderDataService;
import com.cdz360.biz.model.bi.dashboard.SiteAndPlugBiVo;
import com.cdz360.biz.model.common.constant.DcBizConstants;
import com.cdz360.biz.model.oa.vo.BillInvoiceVo;
import com.cdz360.biz.model.trading.cus.vo.UnliquidatedOrderVo;
import com.cdz360.biz.model.trading.order.dto.CusCorpOrderBiDto;
import com.cdz360.biz.model.trading.order.dto.CusLastOrderSiteDto;
import com.cdz360.biz.model.trading.order.dto.CusOrderBiDto;
import com.cdz360.biz.model.trading.order.dto.SyncOrderDtoDetail;
import com.cdz360.biz.model.trading.order.param.BillInvoiceVoParam;
import com.cdz360.biz.model.trading.order.param.GetOrderDetailParam;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.param.ListCusOrderBiParam;
import com.cdz360.biz.model.trading.order.param.PrepaidOrderListParam;
import com.cdz360.biz.model.trading.order.param.RewriteInterimParam;
import com.cdz360.biz.model.trading.order.param.SiteOrderBiParam;
import com.cdz360.biz.model.trading.order.po.ChargerOrderTimeDivision;
import com.cdz360.biz.model.trading.order.vo.PrepaidOperationVo;
import com.cdz360.biz.model.trading.order.vo.SiteOrderBi;
import com.cdz360.biz.model.trading.score.po.ScoreDiscountPo;
import com.cdz360.biz.model.trading.site.po.OvertimeParkFeeOrderPo;
import com.chargerlinkcar.framework.common.domain.OrderOvertimeParkingBi;
import com.chargerlinkcar.framework.common.domain.order.ChargerOrderWithBLOBs;
import com.chargerlinkcar.framework.common.domain.vo.BatteryVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerDetailVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDataVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.LatestOrderInfoResult;
import com.chargerlinkcar.framework.common.domain.vo.OrderInfoVo;
import com.chargerlinkcar.framework.common.domain.vo.UpdateOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.VinDto2;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 订单相关数据统计
 *
 * <AUTHOR>
 *  订单相关数据操作
 * @since 2019/3/6
 */
@Slf4j
@RestController
@RequestMapping("/dataCore/orderData")
public class OrderDataRest {

    @Autowired
    private OrderDataService orderDataService;

    @Autowired
    private ChargerOrderAnalyseService chargerOrderAnalyseService;

    @Autowired
    private ChargerQueryService chargerQueryService;


    @Operation(summary = "由充电订单同步用户与商户的关系")
    @GetMapping(value = "/syncUserCommRef")
    public BaseResponse syncUserCommRef(ServerHttpRequest request) {
        orderDataService.syncUserCommRef();
        return RestUtils.success();
    }


    @PostMapping("/getCusOrderBiList")
    @Operation(summary = "获取客户充电统计信息")
    public ListResponse<CusOrderBiDto> getCusOrderBiList(@RequestBody ListCusOrderBiParam param) {
        var list = this.orderDataService.getCusOrderBiList(param);
        return RestUtils.buildListResponse(list);
    }

    @PostMapping("/getCusAndCorpOrderBiList")
    @Operation(summary = "获取客户充电统计信息")
    public ObjectResponse<CusCorpOrderBiDto> getCusAndCorpOrderBiList(
        @RequestBody ListCusOrderBiParam param) {
        log.info("获取客户充电统计信息: {}", JsonUtils.toJsonString(param));
        CusCorpOrderBiDto ret = this.orderDataService.getCusAndCorpOrderBiList(param);
        return RestUtils.buildObjectResponse(ret);
    }

    @PostMapping("/getCusOvertimeParkOrderBiList")
    @Operation(summary = "获取占位订单列表")
    public ListResponse<OvertimeParkFeeOrderPo> getCusOvertimeParkOrderBiList(
        @RequestBody ListCusOrderBiParam param) {
        var list = this.orderDataService.getCusOvertimeParkOrderLastList(param);
        return RestUtils.buildListResponse(list);
    }

    @PostMapping("/getCusOrderLastSiteInfoList")
    @Operation(summary = "获取客户上次充电场站列表信息")
    public ListResponse<CusLastOrderSiteDto> getCusOrderLastSiteInfoList(
        @RequestBody ListCusOrderBiParam param) {
        var list = this.orderDataService.getCusOrderLastSiteInfoList(param);
        return RestUtils.buildListResponse(list);
    }

    @Operation(summary = "统计最近10分钟的超停数据")
    @GetMapping(value = "/orderOvertimeParkingBi")
    public ListResponse<OrderOvertimeParkingBi> orderOvertimeParkingBi(ServerHttpRequest request) {
        var result = this.orderDataService.orderOvertimeParkingBi();
        return RestUtils.buildListResponse(result);
    }

    @Operation(summary = "获取曹操专车对账订单详情")
    @PostMapping(value = "/getSyncOrderList")
    public ListResponse<SyncOrderDtoDetail> getSyncOrderList(
        @RequestBody List<String> orderNoList) {
        return RestUtils.buildListResponse(orderDataService.getCcSyncOrders(orderNoList));
    }

    @GetMapping(value = "/siteAndPlugStatusFillHlhtData")
    public ObjectResponse<SiteAndPlugBiVo> siteAndPlugStatusFillHlhtData(
        @RequestParam(value = "commId", required = false) Long commId,
        @RequestParam(value = "commIdChain", required = false) String commIdChain) {
        return RestUtils.buildObjectResponse(
            orderDataService.siteAndPlugStatusFillHlhtData(commId, commIdChain));
    }


    @GetMapping(value = "/analyseChargerOrder")
    public BaseResponse analyseChargerOrder(ServerHttpRequest request,
        @RequestParam String orderNo) {
        chargerOrderAnalyseService.analyseOrder(orderNo);
        return RestUtils.success();
    }

    @PostMapping(value = "/estimateOrderTimeDivisionList")
    public ListResponse<ChargerOrderTimeDivision> estimateOrderTimeDivisionList(
        ServerHttpRequest request,
        @RequestBody UpdateOrderVo updateOrderVo) {

        log.info("获取估算的分时数据, updateOrderVo: {}", updateOrderVo);
        List<ChargerOrderTimeDivision> timeDivisions =
            chargerQueryService.estimateOrderTimeDivisionListWrapper(updateOrderVo);
        return new ListResponse<>(timeDivisions, (long) timeDivisions.size());
    }

    @GetMapping(value = "/getVinDto2RankInfo")
    public ObjectResponse<VinDto2> getVinDto2RankInfo(@RequestParam("vin") String vin,
        @RequestParam("commIdChain") String commIdChain) {
        log.info("getVinDto2RankInfo vin: {}, commIdChain: {}", vin, commIdChain);
        return orderDataService.getVinDto2RankInfo(vin, commIdChain);
    }

    @GetMapping(value = "/getBatteryVo")
    public ObjectResponse<BatteryVo> getBatteryVo(@RequestParam("vin") String vin,
        @RequestParam("commIdChain") String commIdChain) {
        log.info("getBatteryVo vin: {}, commIdChain: {}", vin, commIdChain);
        return orderDataService.getBatteryVo(vin, commIdChain);
    }

    @PostMapping(value = "/getUnliquidatedNum")
    public ListResponse<UnliquidatedOrderVo> getUnliquidatedNum(
        @RequestBody ListCusOrderBiParam param) {
        log.info("getUnliquidatedNum param = {}", param);
        return orderDataService.getUnliquidatedNum(param.getUidList(), param.getCommIdChain());
    }

    @Operation(summary = "获取场站充电统计", description = "使用充电结束时间作为统计时间")
    @PostMapping("/getSiteOrderBi")
    public ObjectResponse<SiteOrderBi> getSiteOrderBi(@RequestBody SiteOrderBiParam param) {
        if (param.getSiteId() == null) {
            throw new DcArgumentException("场站ID不能为空");
        }
        return orderDataService.getSiteOrderBi(param);
    }


    /**
     * 获取该商户列表下的客户 订单详情
     */
    @PostMapping("/getOrderDetail")
    public ObjectResponse<ChargerOrderWithBLOBs> getOrderDetail(ServerHttpRequest request,
        @RequestBody GetOrderDetailParam param) {
        log.debug(LoggerHelper2.formatEnterLog(request));
        if (CollectionUtils.isNotEmpty(param.getGids())) {
            // pass
        } else if (param.getCommId() != null
            && param.getCommId().longValue() == DcBizConstants.superTopCommId.longValue()
            && (param.getCommId() != null || StringUtils.isBlank(param.getCommIdChain()))
        ) {
            // pass 这个是个后门代码，让‘任我充’商户可以查看所有的订单
        } else if (CollectionUtils.isEmpty(param.getCommIdList()) && StringUtils.isEmpty(
            param.getCommIdChain())) {
            throw new DcArgumentException("参数错误");
        }

        return chargerQueryService.queryOrderDetail(param);
    }

    /**
     * 获取mongodb中的订单数据
     *
     * @param orderNo
     * @return
     * <AUTHOR>
     */
    @Operation(summary = "充电流程--获取mongodb中的订单数据")
    @GetMapping("/orderInfo")
    public ObjectResponse<OrderInfoVo> orderInfo(
        @Parameter(name = "订单编号") @RequestParam(name = "orderNo") String orderNo) {
        long startTime = System.nanoTime();    // debug 性能问题
        log.info(">> 获取订单详情信息: orderNo = {}", orderNo);

        OrderInfoVo vo = chargerQueryService.getOrderDetailInfo(orderNo);

        ObjectResponse<OrderInfoVo> result = new ObjectResponse<>(vo);
        log.info("<< 获取订单数据结果: orderNo = {}, status = {}, elec = {}, payStatus = {}",
            vo.getOrderNo(), vo.getStatus(), vo.getElectricity(), vo.getPayStatus());
        LogHelper.logLatency(log, OrderDataRest.class.getSimpleName(),
            "orderInfo", "获取mongodb中的订单数据", startTime);
        return result;
    }

    @Operation(summary = "根据第三方订单号获取订单信息")
    @GetMapping("/getOrderInfoByThirdOrderNo")
    public ObjectResponse<OrderInfoVo> getOrderInfoByThirdOrderNo(
        @Parameter(name = "第三方订单编号") @RequestParam String thirdOrderNo,
        @Parameter(name = "用户ID") @RequestParam Long customId) {
        OrderInfoVo vo = chargerQueryService.getOrderInfoByThirdOrderNo(thirdOrderNo, customId);
        return new ObjectResponse<>(vo);
    }


    /**
     * 该订单实时采样信息
     *
     * @param orderNo
     * @return
     */
    @Operation(summary = "充电流程--获取mongodb中的订单的实时采样信息")
    @GetMapping("/samplingInfo")
    public ListResponse<ChargerDetailVo> getOrderSamplingInfo(
        @Parameter(name = "订单编号") @RequestParam String orderNo) {
        long startTime = System.nanoTime();    // debug 性能问题
        log.info(">> 从mongodb中获取订单数据: orderNo={}", orderNo);

        // 订单编号
        if (StringUtils.isBlank(orderNo)) {
            log.info("<< 订单编号不能为空.");
            throw new DcArgumentException("订单编号不能为空");
        }
        // mongodb中数据
        ListResponse<ChargerDetailVo> sampling = orderDataService.getOrderSamplingInfo(orderNo);

        LogHelper.logLatency(log, OrderDataRest.class.getSimpleName(),
            "orderSamplingInfo", "获取mongodb中的订单的实时采样信息", startTime);
        return sampling;
    }

    @Operation(summary = "获取用户最近订单信息")
    @GetMapping(value = "/getLatestOrderInfo")
    ObjectResponse<LatestOrderInfoResult> getLatestOrderInfo(
        @RequestParam(value = "accountId") Long accountId,
        @RequestParam(value = "accountType") PayAccountType accountType,
        @RequestParam(value = "siteId") String siteId) {
        log.info(">> 获取用户最近订单信息: accountId = {}, accountType = {}, siteId = {}",
            accountId, accountType, siteId);
        ObjectResponse<LatestOrderInfoResult> res = orderDataService.getLatestOrderInfo(accountId,
            accountType, siteId);
        log.debug("res: {}", res);
        return res;
    }

    @Operation(summary = "订单mongo数据清洗")
    @GetMapping(value = "/orderFlushSiteInfo")
    BaseResponse orderFlushSiteInfo(
        @RequestParam(value = "size", defaultValue = "100") Integer size,
        @RequestParam(value = "skip", defaultValue = "-1") Integer skip) {
        log.info(">> 订单mongo数据清洗，写入场站信息: size = {}, skip = {}", size, skip);
        orderDataService.orderFlushSiteInfo(size, skip);
        return BaseResponse.success();
    }

    @Operation(summary = "订单mongo数据清洗-设置执行状态")
    @GetMapping(value = "/setRunFlag")
    BaseResponse setRunFlag(@RequestParam(value = "b") Boolean b) {
        orderDataService.setRunFlag(b);
        return BaseResponse.success();
    }

    @Operation(summary = "订单mongo数据清洗-获取执行信息")
    @GetMapping(value = "/getSkipInfo")
    ObjectResponse<String> getSkipInfo() {
        return new ObjectResponse<String>(orderDataService.getSkipInfo());
    }

    @Operation(summary = "根据PageType获取预付订单")
    @PostMapping(value = "/getPrepaidOrderList")
    public ListResponse<ChargerOrderVo> getPrepaidOrderList(
        @RequestBody PrepaidOrderListParam param) {
        log.debug(">> getPrepaidOrderList. param = {}", param);
        return orderDataService.getPrepaidOrderList(param);
    }

    @Operation(summary = "根据PageType获取预付订单总数")
    @PostMapping(value = "/getPrepaidOrderListCount")
    public ObjectResponse<Long> getPrepaidOrderListCount(@RequestBody PrepaidOrderListParam param) {
        log.debug(">> getPrepaidOrderListCount. param = {}", param);
        return orderDataService.getPrepaidOrderListCount(param);
    }

    @Operation(summary = "预付订单操作")
    @PostMapping(value = "/prepaidOrderOperation")
    public ObjectResponse<String> prepaidOrderOperation(@RequestBody PrepaidOrderListParam param) {
        log.debug(">> prepaidOrderOperation. param = {}", param);
        return orderDataService.prepaidOrderOperation(param);
    }

    @Operation(summary = "重写InterimCode")
    @PostMapping(value = "/rewriteInterimCodeByOa")
    public BaseResponse rewriteInterimCodeByOa(@RequestBody RewriteInterimParam param) {
        log.debug(">> rewriteInterimCodeByOa. param = {}", param);
        return orderDataService.rewriteInterimCodeByOa(param);
    }

    @Operation(summary = "Interim数据定时清理")
    @PostMapping(value = "/orderInterimCleaning")
    public BaseResponse orderInterimPeriodicCleaning() {
        return orderDataService.orderInterimPeriodicCleaning();
    }

    @Operation(summary = "获取全部能开票的预付订单号")
    @PostMapping(value = "/getAllPrepaidOrderNos")
    public ObjectResponse<PrepaidOperationVo> getAllPrepaidOrderNos(
        @RequestBody PrepaidOrderListParam param) {
        log.debug(">> getAllPrepaidOrderNos. param = {}", param);
        return orderDataService.getAllPrepaidOrderNos(param);
    }

    @PostMapping(value = "/getBillInvoiceVoList")
    public ListResponse<BillInvoiceVo> getBillInvoiceVoList(@RequestBody BillInvoiceVoParam param) {
        log.debug(">> getBillInvoiceVoList. param: {}", param.toString());
        return orderDataService.getBillInvoiceVoList(param);
    }

    @Operation(summary = "获取预付订单流程详情页的订单详细数据")
    @PostMapping(value = "/queryPrePaidOrderDetailList")
    public ListResponse<ChargerOrderVo> queryPrePaidOrderDetailList(
        @RequestBody PrepaidOrderListParam param) {
        log.debug(">> queryPrePaidOrderDetailList. param = {}", param);
        return orderDataService.queryPrePaidOrderDetailList(param);
    }

    @Operation(summary = "获取预付订单的统计信息")
    @PostMapping(value = "/getPrepaidOrderDataVo")
    public ObjectResponse<ChargerOrderDataVo> getPrepaidOrderDataVo(
        @RequestBody PrepaidOrderListParam param) {
        log.debug(">> getPrepaidOrderDataVo. param = {}", param);
        return orderDataService.getPrepaidOrderDataVo(param);
    }

    @Operation(summary = "检查所选预付订单是否可开票")
    @PostMapping(value = "/checkPrepaidOrderList")
    public BaseResponse checkWhetherThePrepaidOrderCanBeInvoiced(
        @RequestBody PrepaidOrderListParam param) {
        log.debug(">> checkWhetherThePrepaidOrderCanBeInvoiced. param = {}", param);
        return orderDataService.checkWhetherThePrepaidOrderCanBeInvoiced(param);
    }

    @Operation(summary = "获取场站指定日期的所有非0元订单号（用于监管平台核对订单补推使用）")
    @PostMapping(value = "/queryOrderNoListBySiteIdAndDay")
    public ListResponse<String> queryOrderNoListBySiteIdAndDay(
        @RequestBody ListChargeOrderParam param) {
        log.debug(">> queryOrderNoListBySiteIdAndDay. param = {}", param);
        return orderDataService.queryOrderNoListBySiteIdAndDay(param);
    }

    @Operation(summary = "获取订单积分折扣绑定关系")
    @GetMapping(value = "/getScoreDiscount")
    public ObjectResponse<ScoreDiscountPo> getScoreDiscount(
        @RequestParam(value = "orderNo") String orderNo) {
        log.debug(">> getScoreDiscount. param = {}", orderNo);
        return orderDataService.getScoreDiscount(orderNo);
    }

    @Operation(summary = "根据平台订单号集合获取互联互通订单号集合")
    @PostMapping(value = "/queryOpenOrderNoListByOrderNoList")
    public ListResponse<String> queryOpenOrderNoListByOrderNoList(
        @RequestBody ListChargeOrderParam param) {
        log.debug(">> queryOpenOrderNoListByOrderNoList. param = {}", JsonUtils.toJsonString(param));
        return orderDataService.queryOpenOrderNoListByOrderNoList(param);
    }
}
