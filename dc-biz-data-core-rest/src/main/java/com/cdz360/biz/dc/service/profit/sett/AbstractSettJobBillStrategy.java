package com.cdz360.biz.dc.service.profit.sett;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ds.trading.ro.profit.sett.ds.SettJobBillRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ds.trading.rw.profit.sett.ds.SettJobBillRwDs;
import com.cdz360.biz.model.trading.profit.sett.param.ListSettJobBillParam;
import com.cdz360.biz.model.trading.profit.sett.po.SettJobBillPo;
import com.cdz360.biz.model.trading.profit.sett.vo.ProfitCfgVo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public abstract class AbstractSettJobBillStrategy<R, P> implements SettJobBillStrategy {

    private static final DateTimeFormatter formatter =
        DateTimeFormatter.ofPattern("yyMMddHHmmssSSS");
    @Autowired
    protected SettJobBillStrategyFactory settJobBillStrategyFactory;
    @Autowired
    private SettJobBillRwDs settJobBillRwDs;

    @Autowired
    private SettJobBillRoDs settJobBillRoDs;

    @Autowired
    private SiteRoDs siteRoDs;

    protected abstract P statisticsParam(LocalDate refer, String siteId, ProfitCfgVo cfg); // 数据统计参数

    protected abstract R statistics(P param); // 数据统计

    protected abstract SettJobBillPo compute(ProfitCfgVo cfg, P param); // 规则计算


    protected void insertSettJobBill(SettJobBillPo settJobBill) {
        ListSettJobBillParam findOldParam = new ListSettJobBillParam();
        findOldParam.setJobId(settJobBill.getJobId())
            .setJobCategory(settJobBill.getJobCategory())
            .setSiteId(settJobBill.getSiteId())
            .setSettPeriodFrom(settJobBill.getSettPeriodFrom())
            .setSettPeriodTo(settJobBill.getSettPeriodTo())
            .setEnable(true);
        SettJobBillPo oldBill = settJobBillRoDs.getJobBill(findOldParam);
        if (oldBill != null) {   // 已经有存在相同结算配置的数据,做更新操作
            oldBill.setJobName(settJobBill.getJobName())
                .setJobCategory(settJobBill.getJobCategory())
                .setJobCalSource(settJobBill.getJobCalSource())
                .setSiteId(settJobBill.getSiteId())
                .setSettPeriodFrom(settJobBill.getSettPeriodFrom())
                .setSettPeriodTo(settJobBill.getSettPeriodTo())
                .setElec(settJobBill.getElec())
                .setElecFee(settJobBill.getElecFee())
                .setServFee(settJobBill.getServFee())
                .setParkFee(settJobBill.getParkFee())
                .setDataSource(settJobBill.getDataSource())
                .setTimeTarget(settJobBill.getTimeTarget())
                .setChargeOrderRules(settJobBill.getChargeOrderRules())
                .setBiRule(settJobBill.getBiRule())
                .setJobId(settJobBill.getJobId())
                .setEnable(settJobBill.getEnable())
                .setRemark(settJobBill.getRemark());
            this.updateSettJobBill(oldBill);
        } else {
            settJobBill.setBillNo("B" + formatter.format(LocalDateTime.now())); // 账单编号

            // 场站名称
            SitePo site = siteRoDs.getSite(settJobBill.getSiteId());
            if (null != site) {
                settJobBill.setSiteName(site.getSiteName());
            }

            boolean b = settJobBillRwDs.insertSettJobBill(settJobBill);
            if (!b) {
                log.error("新增结算单记录失败: {}", JsonUtils.toJsonString(settJobBill));
            }
        }
    }

    protected void updateSettJobBill(SettJobBillPo settJobBill) {
        log.info("更新结算单数据记录 settJobBill= {}", settJobBill);
        boolean b = settJobBillRwDs.updateSettJobBill(settJobBill);
        if (!b) {
            log.error("新更新结算单记录失败: {}", JsonUtils.toJsonString(settJobBill));
        }
    }

}
