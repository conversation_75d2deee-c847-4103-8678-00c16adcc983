package com.cdz360.biz.dc.service.geo;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ds.trading.ro.geo.ds.CitiesRoDs;
import com.cdz360.biz.ds.trading.ro.geo.ds.CountriesRoDs;
import com.cdz360.biz.model.geo.param.ListCitiesParam;
import com.cdz360.biz.model.geo.param.ListCountriesParam;
import com.cdz360.biz.model.geo.vo.GeoCitiesVo;
import com.cdz360.biz.model.geo.vo.GeoCountriesVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class WorldGeoService {

    @Autowired
    private CountriesRoDs countriesRoDs;

    @Autowired
    private CitiesRoDs citiesRoDs;

    public Mono<ListResponse<GeoCountriesVo>> findCountries(ListCountriesParam param) {
        return Mono.just(param)
            .doOnNext(p -> {
                if (null == param.getSize() || param.getSize() > 100) {
                    param.setSize(100);
                }
            })
            .map(this.countriesRoDs::findCountries)
            .map(RestUtils::buildListResponse)
            .doOnNext(res -> {
                if (null != param.getTotal() && param.getTotal()) {
                    res.setTotal(this.countriesRoDs.count(param));
                } else {
                    res.setTotal(0L);
                }
            });
    }

    public Mono<ListResponse<GeoCitiesVo>> findCities(ListCitiesParam param) {
        return Mono.just(param)
            .doOnNext(p -> {
                if (null == param.getSize() || param.getSize() > 100) {
                    param.setSize(100);
                }
            })
            .map(this.citiesRoDs::findCities)
            .map(RestUtils::buildListResponse)
            .doOnNext(res -> {
                if (null != param.getTotal() && param.getTotal()) {
                    res.setTotal(this.citiesRoDs.count(param));
                } else {
                    res.setTotal(0L);
                }
            });
    }
}
