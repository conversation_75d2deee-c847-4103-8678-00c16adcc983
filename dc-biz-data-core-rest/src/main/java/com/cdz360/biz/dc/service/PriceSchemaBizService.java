package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.exception.DcTokenException;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.charge.vo.ChargePriceItem;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.client.MerchantFeignClient;
import com.cdz360.biz.dc.domain.SiteInMongoPo;
import com.cdz360.biz.dc.repository.SiteMongoDataRepository;
import com.cdz360.biz.ds.trading.ro.iot.ds.BsBoxRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.PriceItemRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.PriceSchemaModRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.PriceSchemaRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteTemplateRoDs;
import com.cdz360.biz.ds.trading.rw.site.ds.PriceSchemaRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteDefaultSettingRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteTemplateRwDs;
import com.cdz360.biz.model.iot.param.ModifyEvseCfgParam;
import com.cdz360.biz.model.iot.vo.ChargeV2;
import com.cdz360.biz.model.site.po.PriceItemPo;
import com.cdz360.biz.model.site.po.PriceTemplatePo;
import com.cdz360.biz.model.site.type.PriceTemplateCalcType;
import com.cdz360.biz.model.site.type.TemplateUsage;
import com.cdz360.biz.model.site.vo.PriceTemplateModVo;
import com.cdz360.biz.model.trading.hlht.param.DataSyncSitePrices;
import com.cdz360.biz.model.trading.iot.po.BsBoxPo;
import com.cdz360.biz.model.trading.site.param.AddPriceSchemaParam;
import com.cdz360.biz.model.trading.site.param.ListPriceTemplateParam;
import com.cdz360.biz.model.trading.site.param.UpdatePriceSchemaParam;
import com.cdz360.biz.model.trading.site.po.BsBoxSettingPo;
import com.cdz360.biz.model.trading.site.po.CommPo;
import com.cdz360.biz.model.trading.site.po.SiteDefaultSettingPo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.site.po.SiteTemplatePo;
import com.cdz360.biz.model.trading.site.type.PriceSchemaConstant;
import com.cdz360.biz.model.trading.site.vo.SiteChargePriceVo;
import com.cdz360.biz.model.trading.site.vo.SiteTemplateVo;
import com.cdz360.biz.utils.feign.hlht.OpenHlhtFeignClient;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.domain.param.ListPriceSchemeSiteUseParam;
import com.chargerlinkcar.framework.common.feign.IotBizClient;
import com.chargerlinkcar.framework.common.service.DcEventPublisherService;
import com.chargerlinkcar.framework.common.utils.DateUtils;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.event.Level;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

/**
 * 价格模板业务服务
 */
@Slf4j
@Service
public class PriceSchemaBizService {

    private static final Long FLAG_TEMPLATE_INITIAL_VERSION = 1000L;
    private static AtomicLong CODE_IDX = new AtomicLong(0L);

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private SiteRwDs siteRwDs;

    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private PriceSchemaRoDs priceTemplateRoDs;

    @Autowired
    private PriceSchemaModRoDs priceTemplateModRoDs;

    @Autowired
    private PriceSchemaRwDs priceSchemaRwDs;

    @Autowired
    private PriceItemRoDs priceItemRoDs;

    @Autowired
    private IotBizClient iotBizClient;

    @Autowired
    private EvseCfgScheduleService evseCfgScheduleService;

    @Autowired
    private BsBoxRoDs boxRoDs;


    @Autowired
    private BsBoxSettingService bsBoxSettingService;

    @Autowired
    private MerchantFeignClient merchantFeignClient;

    @Autowired
    private TRCommercialService commercialService;

    @Autowired
    private SiteDefaultSettingRwDs siteDefaultSettingRwDs;

    @Autowired
    private SiteMongoDataRepository siteMongoDataRepository;

    @Autowired
    private DcEventPublisherService dcEventPublisherService;

    @Autowired
    private PriceSchemaBizService priceSchemaBizService;

    @Autowired
    private OpenHlhtFeignClient openHlhtFeignClient;

    @Autowired
    private RedisIotReadService redisIotReadService;

    @Autowired
    private SiteTemplateRoDs siteTemplateRoDs;

    @Autowired
    private SiteTemplateRwDs siteTemplateRwDs;

    @Transactional
    public PriceTemplatePo addPriceSchema(AddPriceSchemaParam param) {
        Assert.notNull(param.getName(), "模板名称不能为空");
        Assert.notNull(param.getCommId(), "代理商ID不能为空");
        Assert.notNull(param.getFreeChargeFlag(), "免费充电标识不能为空");

        PriceTemplatePo template = new PriceTemplatePo();
        List<ChargeV2> subTemplateList = param.getPriceItemList();
        this.fillRemark(param, subTemplateList, template);  // 填充价格备注信息

        // 计费模板编号
        String timestamp = DateUtils.timeStampToDate(new Date().getTime(), "yyyyMMddHHmmss");
        String code = String.format("%s%02d", timestamp, CODE_IDX.addAndGet(1L) % 99);
        template.setCode(code);
        // 初始化版本号
        template.setVersion(FLAG_TEMPLATE_INITIAL_VERSION);
        // 创建时间
        template.setCreateTime(Calendar.getInstance().getTime());
        template.setDeleteFlag(0);
        template.setEnable(true);
        // 是否免费
        template.setFreeChargeFlag(param.getFreeChargeFlag());
        // 模板名称
        template.setName(param.getName());
        // 代理商ID
        template.setCommercialId(param.getCommId());
        // 充电计费单位
        //template.setCalculateUnit(priceSchema.getCalculateUnit());
        // 充电计费方式
        template.setCalculateType(param.getCalculateType());

        template.setPriceItemList(param.getPriceItemList());

        template.setCreatorUserId(param.getCreatorUserId())
            .setCreatorName(param.getCreatorName())
            .setCreatorPhone(param.getCreatorPhone());

        template.setUsage(param.getUsage());

        boolean nameExist = checkTemplateNameExist(template.getName(), param.getCommIdChain(),
            param.getUsage());
        if (nameExist) {
            throw new DcServiceException("您所属的集团商户及下属商户中已存在该计费模板名称");
        }

        PriceTemplatePo vo = priceSchemaRwDs.addPriceSchema(template, param.getTopCommId(),
            param.getCommIdChain());
        log.info("新建计费模板: id = {}, code = {}", vo.getId(), vo.getCode());
        return vo;
    }


    @Transactional
    public Long updatePriceSchema(UpdatePriceSchemaParam param) {
        log.info("收到更新计费模板请求 param={}", param);
        // 1-参数校验

        PriceTemplatePo oldTemplate = priceTemplateRoDs.findById(param.getId(), true);
        Assert.notNull(oldTemplate, "未找到计费模板信息");

        // 增加重复提交校验
        priceSchemaRwDs.deletePriceSchema(param.getId());

        PriceTemplatePo template = new PriceTemplatePo();
        //2.2-再新增新的主模板
        // 以下字段保持不变============================
        BeanUtils.copyProperties(oldTemplate, template);

        // 版本号+1
        template.setVersion(oldTemplate.getVersion() + 1);
        template.setDeleteFlag(0);
        // 是否免费
        template.setFreeChargeFlag(param.getFreeChargeFlag());
        // 以下字段“更新”============================

        // 充电计费方式
        template.setCalculateType(param.getCalculateType());

        template.setPriceItemList(param.getPriceItemList());

        this.fillRemark(param, param.getPriceItemList(), template);  // 填充价格备注信息

        priceSchemaRwDs.addPriceSchema(template, param.getTopCommId(), param.getCommIdChain());

        // 定时的计费模板更新
        List<Long> templateIdList = List.of(oldTemplate.getId(), template.getId());
        evseCfgScheduleService.updatePriceSchedule(templateIdList);
        //FeignResponseValidate.check(response);
        //log.info("res = {}", response);

        return template.getId();
    }

    private boolean checkTemplateNameExist(String priceSchemaName, String commIdChain,
        TemplateUsage usage) {
        IotAssert.isNotBlank(priceSchemaName, "计费模板名称不能为空");
        ListPriceTemplateParam param = new ListPriceTemplateParam()
            .setUsage(usage);
        param.setCommIdChain(commIdChain).setName(priceSchemaName).setEnable(true)
            .setDeleteFlag(false);
        List<PriceTemplatePo> list = priceTemplateRoDs.getPriceTemplateList(param);
        return CollectionUtils.isNotEmpty(list);
    }

    /**
     * 填充价格备注信息
     */
    private void fillRemark(AddPriceSchemaParam param, List<ChargeV2> subTemplateList,
        PriceTemplatePo template) {
        if (param.getFreeChargeFlag() == PriceSchemaConstant.UN_FREE) {
            if (subTemplateList == null && subTemplateList.size() == 0) {
                throw new DcServiceException("分时计费子计费模板不能为空", Level.WARN);
            }
            if (param.getCalculateType() == PriceTemplateCalcType.UNIFY_PRICE) {

                template.setRemarkCharge(subTemplateList.get(0).getElecPrice() + "元/kW·h");
                template.setRemarkService(subTemplateList.get(0).getServPrice() + "元/kW·h");

            } else if (param.getCalculateType() == PriceTemplateCalcType.TIME_BASE_PRICE) {

                template.setRemarkCharge("分时电费");
                template.setRemarkService(subTemplateList.get(0).getServPrice() + "元/kW·h");

            } else if (param.getCalculateType() == PriceTemplateCalcType.TIME_BASE_PRICE_2) {

                template.setRemarkCharge("分时电费");
                template.setRemarkService("分时服务费");

            }
            ChargeV2 subTemplateInsertRequest = subTemplateList.get(subTemplateList.size() - 1);
            if (!subTemplateInsertRequest.getStopTime().equals("24:00")) {
                throw new DcServiceException("分时计费结束时间不正确", Level.WARN);
            }
        } else if (param.getFreeChargeFlag().intValue() == PriceSchemaConstant.FREE) {
            template.setRemarkCharge("免费");
            template.setRemarkService("免费");
        }
    }

    public List<PriceTemplatePo> getPriceTemplateList(ListPriceTemplateParam param) {
        return this.priceTemplateRoDs.getPriceTemplateList(param);
    }

    public List<PriceItemPo> getPriceItemListByTempId(Long tempId) {
        if (null == tempId) {
            log.warn("请提供计费模板Id值");
            return new ArrayList<>();
        }

        return priceItemRoDs.getPriceItemListByTempId(tempId);
    }

    public Optional<PriceTemplatePo> getPriceSchema(Long priceId, Boolean enable) {
        log.info("获取计费模板信息(带分时段计费信息): priceId = {}", priceId);

//        ListPriceTemplateParam param = new ListPriceTemplateParam();
//        param.setIdList(Collections.singletonList(priceId));
//
//        if (null != enable) {
//            param.setEnable(enable);
//        }
//
//        List<PriceTemplatePo> resultList = this.getPriceTemplateList(param);
//        if (resultList.isEmpty()) {
//            return Optional.empty();
//        }

        // 根据计费模板ID查询有且仅有一个
//        PriceTemplatePo result = resultList.get(0);

        PriceTemplatePo result = priceTemplateRoDs.findByIdAndEnable(priceId, enable);
        if (null == result) {
            return Optional.empty();
        }
        result.setPriceItemList(this.priceItem2Charge(this.getPriceItemListByTempId(priceId)));
        log.info("获取计费模板信息: result = {}", result);
        return Optional.of(result);
    }

    public List<ChargeV2> getChargeV2List(List<Long> priceIdList) {
        List<ChargeV2> chargeV2s = this.priceItem2Charge(
            priceItemRoDs.getPriceItemList(priceIdList));
        return chargeV2s;
    }

    public ListResponse<PriceItemPo> getPriceSchemaItem(List<Long> priceIdList) {
        List<PriceItemPo> result = priceItemRoDs.getPriceItemList(priceIdList);
        return RestUtils.buildListResponse(result);
    }

    /**
     * 计费模板使能状态调整
     *
     * @param token
     * @param id
     * @param enable
     */
    public void enablePriceSchema(String token, Long id, @NotNull Boolean enable) {
        log.info("计费模板使能操作: id = {}, enable = {}", id, enable);

        if (StringUtils.isBlank(token)) {
            throw new DcTokenException("没有token值");
        }

        PriceTemplatePo template = priceTemplateRoDs.findById(id, false);
        if (null == template) {
            log.info("该Id对应的计费模板不存在");
            throw new DcArgumentException("该Id对应的计费模板不存在, 请确认计费模板Id有效性");
        }

        if (null == enable) {
            log.info("使能参数缺失");
            throw new DcArgumentException("使能参数缺失，请提供使能入参值");
        }

        // 用户是否具有修改计费模板的权限
        ObjectResponse<Commercial> res = merchantFeignClient.getCommercialByToken(token);
        FeignResponseValidate.check(res);
        Commercial commercial = res.getData();
        if (!commercial.getId().equals(template.getCommercialId())) {
            throw new DcServiceException("用户所属商户没有权限变更该计费模板信息");
        }

        if (!enable) {
            // 未被使用的计费模板以及定时下发的计费模板可以禁用
            if (this.templateUsed(template)) {
                log.info("该计费模板已被使用，不支持禁用操作(可能同一集团的其他商户)");
                throw new DcServiceException("该计费模板已被使用，不支持禁用操作");
            }
        }

        log.info("数据库中 enable = {}", template.getEnable());
        if ((template.getEnable() && !enable) || (!template.getEnable() && enable)) {
            priceSchemaRwDs.updateEnable(id, enable);
        }
    }

    public BaseResponse deletePvPriceSchema(List<Long> priceIdList) {

        priceIdList.forEach(priceId -> {
            boolean isUsed = siteRoDs.isPriceTemplateUsed(priceId);
            if (isUsed) {
                PriceTemplatePo po = priceTemplateRoDs.findById(priceId, false);
                throw new DcServiceException(po.getName() + "已被使用，不支持删除操作");
            }
        });
        return priceSchemaRwDs.batchDelete(priceIdList)
            ? RestUtils.success()
            : RestUtils.fail(2000, "操作失败");
    }

    private boolean templateUsed(PriceTemplatePo template) {
        // 数组 size() > 0 说明已经被占用: 定时/被桩使用
        // 子商户列表
        //ListResponse<Long> entity = merchantFeignClient.getCommIdListByCommId(template.getCommercialId());
        CommPo comm = commercialService.getCommercialById(template.getCommercialId());
        //List<Long> commIdList = entity.getData();

        ListPriceSchemeSiteUseParam param = new ListPriceSchemeSiteUseParam();
        param.setPriceSchemeIdList(Collections.singletonList(template.getId()));
        //param.setCommIdList(commIdList);
        param.setCommIdChain(comm.getIdChain());

        return !evseCfgScheduleService.getByPriceSchemeId(param).isEmpty();
    }

    public void scheduleTempDown(ModifyEvseCfgParam param) {
        log.info("定时下发下发计费模板: evseNoList size = {}", param.getEvseNoList().size());

        // 计费模板Id
        PriceTemplatePo template = priceTemplateRoDs.findById(param.getPriceSchemeId(), false);
        if (null == template) {
            log.warn("无法获取对应的计费模板: tempId = {}", param.getPriceSchemeId());
            return;
        }

        // 新增桩配置记录: t_bs_box_setting
        this.insertOrUpdate(param, template);

        // 调用 iotWorker 下发
        this.iotBizClient.modifyEvseCfgV2(param);

        // 变更桩的配置信息
//        this.updateEvseSetting(param);
    }

    public void priceTempDown(ModifyEvseCfgParam param) {
        log.info("即时下发计费模板: evseNoList size = {}", param.getEvseNoList().size());

        // 计费模板Id
        PriceTemplatePo template = priceTemplateRoDs.findById(param.getPriceSchemeId(), false);
        if (null == template) {
            log.warn("无法获取对应的计费模板: tempId = {}", param.getPriceSchemeId());
            throw new DcArgumentException("无法获取对应的计费模板");
        }

        // 判断场站是否第一次下发，如果第一次下发设置成默认计费模板
        SiteDefaultSettingPo siteInfo = siteDefaultSettingRwDs.getBySiteId(param.getSiteId());
        IotAssert.isNotNull(siteInfo, "场站信息不存在");
        if (siteInfo.getChargeId() == null) {
            param.setSiteDefault(Boolean.TRUE);
        }

        // 设置默认计费模板
        if (Boolean.TRUE.equals(param.getSiteDefault())) {
            List<EvseVo> evseList = redisIotReadService.getEvseList(param.getEvseNoList());
            this.setDefaultPriceScheme(param.getSiteId(), param.getPriceSchemeId(), evseList);
        }

        // 获取子模版
        List<PriceItemPo> itemList = this.getPriceItemListByTempId(template.getId());
        param.setPriceSchemeList(this.priceItem2Charge(itemList));

        // 是否需要带上默认场站配置信息
        // t_bs_box_setting
        // isUseSiteDefaultSetting
        // FIXME: 单独下发计费模板需要带上默认配置信息？原来逻辑是下发全部内容(场站默认配置，紧急卡，计费模板等)

        // 新增桩配置记录: t_bs_box_setting
        this.insertOrUpdate(param, template);

        // 调用 iotWorker 下发
        this.iotBizClient.modifyEvseCfgV2(param);

        // 变更桩的配置信息
//        this.updateEvseSetting(param);

        // TODO: 清空定时下发任务
        // dataCore
        // 计费模板 + 桩编号List
        // 产品需求: 下发后将定时中的数据清理掉
        this.evseCfgScheduleService.disableScheduleByEvseNo(param.getPriceSchemeId(),
            param.getEvseNoList());

        // 作为场站默认计费时，通知HLHT对端
        if (Boolean.TRUE.equals(param.getSiteDefault())) {
            List<SiteChargePriceVo> sitePriceList = this.getSitePriceList(param.getSiteId());

            List<DataSyncSitePrices> syncDataList = sitePriceList.stream().map(x -> {
                DataSyncSitePrices item = new DataSyncSitePrices();
                item.setSiteId(param.getSiteId())
                    .setPriceCode(x.getId())
                    .setSupplyType(x.getTemplateType());
                return item;
            }).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(syncDataList)) {
                openHlhtFeignClient.syncHlhtStationPrice(syncDataList)
                    .subscribe();
            }
//            openHlhtFeignClient.syncHlhtStationPrice(null)
//                .subscribe();
        }
    }

    public void priceTempDownDefault(String evseNo) {
        IotAssert.isTrue(StringUtils.isNotBlank(evseNo), "入参缺失");
        log.info("即时下发场站默认计费模板: evseNo = {}", evseNo);

        BsBoxPo bsBox = boxRoDs.getBsBox(evseNo);
        IotAssert.isTrue(StringUtils.isNotBlank(bsBox.getSiteId()), "桩还未绑定场站");

        Long priceCode = this.getDefaultPriceCodeByEvseNo(evseNo, bsBox.getSiteId());

        ModifyEvseCfgParam param = new ModifyEvseCfgParam();
//        param.setPriceSchemeId(siteDefaultSetting.getChargeId());
        param.setSiteId(bsBox.getSiteId());
        param.setEvseNoList(List.of(evseNo));
        param.setPriceSchemeId(priceCode);

        this.priceTempDown(param);
    }

    /**
     * 获取桩所绑场站的计费ID（涉及场站多计费模板情况）
     *
     * @param evseNo
     * @param siteId
     * @return
     */
    public Long getDefaultPriceCodeByEvseNo(@NonNull String evseNo, @Nullable String siteId) {
        IotAssert.isTrue(StringUtils.isNotBlank(evseNo), "入参缺失");

        if (StringUtils.isBlank(siteId)) {
            BsBoxPo bsBox = boxRoDs.getBsBox(evseNo);
            IotAssert.isTrue(StringUtils.isNotBlank(bsBox.getSiteId()), "桩还未绑定场站");
            siteId = bsBox.getSiteId();
        }

//        SiteDefaultSettingPo siteDefaultSetting = siteDefaultSettingRwDs.getBySiteId(
//            bsBox.getSiteId());
//        IotAssert.isTrue(siteDefaultSetting.getChargeId() != null, "场站未配置计费信息");

        // 场站多计费模板
        List<SiteChargePriceVo> sitePriceList = this.getSitePriceList(siteId);
        IotAssert.isTrue(CollectionUtils.isNotEmpty(sitePriceList), "场站未配置计费信息");

        long priceCode;
        if (sitePriceList.size() == 1) {
            priceCode = sitePriceList.get(0).getId();
        } else {
            EvseVo evseVo = redisIotReadService.getEvseRedisCache(evseNo);
            Optional<SiteChargePriceVo> first = sitePriceList.stream().filter(
                x -> x.getTemplateType() != null && x.getTemplateType()
                    .equals(evseVo.getSupplyType())).findFirst();
            IotAssert.isTrue(first.isPresent(), "桩类型不存在");
            priceCode = first.get().getId();
        }
        return priceCode;
    }

    public void modifyEvsePrice(ModifyEvseCfgParam param) {
        log.info("修改桩计费模板-用于不支持计费下发的桩: evseNoList size = {}",
            param.getEvseNoList().size());

        // 计费模板Id
        PriceTemplatePo template = priceTemplateRoDs.findById(param.getPriceSchemeId(), false);
        if (null == template) {
            log.warn("无法获取对应的计费模板: tempId = {}", param.getPriceSchemeId());
            throw new DcArgumentException("无法获取对应的计费模板");
        }

        // 获取子模版
        List<PriceItemPo> itemList = this.getPriceItemListByTempId(template.getId());
        param.setPriceSchemeList(this.priceItem2Charge(itemList));

        // 新增桩配置记录: t_bs_box_setting
        this.insertOrUpdate(param, template);

        // 调用 iotWorker 下发
        this.iotBizClient.modifyEvsePrice(param);

        // 作为场站默认计费时，通知HLHT对端
        if (Boolean.TRUE.equals(param.getSiteDefault())) {
            List<SiteChargePriceVo> sitePriceList = this.getSitePriceList(param.getSiteId());

            List<DataSyncSitePrices> syncDataList = sitePriceList.stream().map(x -> {
                DataSyncSitePrices item = new DataSyncSitePrices();
                item.setSiteId(param.getSiteId())
                    .setPriceCode(x.getId())
                    .setSupplyType(x.getTemplateType());
                return item;
            }).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(syncDataList)) {
                openHlhtFeignClient.syncHlhtStationPrice(syncDataList)
                    .subscribe();
            }
//            openHlhtFeignClient.syncHlhtStationPrice(null)
//                .subscribe();
        }
    }

    /**
     * 获取场站的计费模板信息
     *
     * @param siteId
     */
    public List<SiteChargePriceVo> getSitePriceList(String siteId) {
        SitePo site = siteRoDs.getSite(siteId);
        IotAssert.isNotNull(site, "场站信息不存在");

        // 获取场站多计费模板列表
        List<SiteTemplateVo> siteTemplateList = siteTemplateRoDs.getSiteTemplateInfoBySiteId(siteId);

        if(CollectionUtils.isEmpty(siteTemplateList)) {
             return new ArrayList<>();
        }

        var ac = siteTemplateList.stream()
            .filter(x -> SupplyType.AC.equals(x.getTemplateType())).findFirst();
        var dc = siteTemplateList.stream()
            .filter(x -> SupplyType.DC.equals(x.getTemplateType())).findFirst();
        var both = siteTemplateList.stream()
            .filter(x -> SupplyType.BOTH.equals(x.getTemplateType())).findFirst();
        if (ac.isPresent() && dc.isPresent()) {  //  交直流模板 相同的情况下   只保留一个
            if (ac.get().getTemplateId() != null && ac.get().getTemplateId()
                .equals(dc.get().getTemplateId())) {
                siteTemplateList = siteTemplateList.stream()
                    .filter(x -> SupplyType.AC.equals(x.getTemplateType())).map(x -> {
                        x.setTemplateType(SupplyType.BOTH);
                        return x;
                    }).collect(Collectors.toList());
            }
        } else if (ac.isPresent() && both.isPresent()) {  //  交流 +  全局
            if (ac.get().getTemplateId() != null && ac.get().getTemplateId()
                .equals(both.get().getTemplateId())) { // 两个模板一样
                siteTemplateList = siteTemplateList.stream()
                    .filter(x -> SupplyType.AC.equals(x.getTemplateType())).map(x -> {
                        x.setTemplateType(SupplyType.BOTH);
                        return x;
                    }).collect(Collectors.toList());
            } else {
                siteTemplateList = siteTemplateList.stream()
                    .map(x -> {
                        if (SupplyType.BOTH.equals(x.getTemplateType())) {
                            x.setTemplateType(SupplyType.DC);
                        }
                        return x;
                    }).collect(Collectors.toList());
            }
        } else if (dc.isPresent() && both.isPresent()) {  //  直流 +  全局
            if (dc.get().getTemplateId() != null && dc.get().getTemplateId()
                .equals(both.get().getTemplateId())) { // 两个模板一样
                siteTemplateList = siteTemplateList.stream()
                    .filter(x -> SupplyType.DC.equals(x.getTemplateType())).map(x -> {
                        x.setTemplateType(SupplyType.BOTH);
                        return x;
                    }).collect(Collectors.toList());
            } else {
                siteTemplateList = siteTemplateList.stream()
                    .map(x -> {
                        if (SupplyType.BOTH.equals(x.getTemplateType())) {
                            x.setTemplateType(SupplyType.AC);
                        }
                        return x;
                    }).collect(Collectors.toList());
            }
        } else {
            siteTemplateList = siteTemplateList.stream().map(x->{
                x.setTemplateType(SupplyType.BOTH);
                return x;
            }).collect(Collectors.toList());
        }

        List<SiteChargePriceVo> collect = siteTemplateList.stream()
            .map(e -> {
                SiteChargePriceVo siteChargePriceVo = new SiteChargePriceVo();
                siteChargePriceVo.setCode(e.getCode())
                    .setId(e.getTemplateId())
                    .setName(e.getTemplateName())
                    .setVersion(e.getVersion() != null ? e.getVersion().intValue() : 0);

                siteChargePriceVo.setFreeChargeFlag(e.getFreeChargeFlag());
                siteChargePriceVo.setTemplateType(e.getTemplateType());
                if (CollectionUtils.isNotEmpty(e.getItems())) {
                    siteChargePriceVo.setItemList(e.getItems().stream()
                            .filter(sub -> sub.getStartTime()!=null && sub.getStopTime() != null).map(sub -> new ChargePriceItem()
                            .setCode(sub.getNum())
                            .setCategory(sub.getCategory())
                            .setElecPrice(sub.getPrice())
                            .setServPrice(sub.getServicePrice())
                            .setStartTime(this.convert2View(sub.getStartTime()))
                            .setEndTime(this.convert2View(sub.getStopTime())))
                        .collect(Collectors.toList()));
                }
                return siteChargePriceVo;
            })
            .collect(Collectors.toList());

        return collect;
    }

    public List<ChargeV2> priceItem2Charge(List<PriceItemPo> poList) {
        return poList.stream().map(this::priceItem2Charge).collect(Collectors.toList());
    }

    private ChargeV2 priceItem2Charge(PriceItemPo po) {
        ChargeV2 result = new ChargeV2();
//        result.setCode(po.getNum());
        if (po.getCategory() == null) {
            log.error("err");
        }
        result.setTemplateId(po.getTemplateId());

        if (null != po.getCategory()) {
            result.setCode(po.getCategory().getCode()); // FIXME: 原来逻辑是填充"尖/峰/平/古"值
        }
        result.setCategory(po.getCategory()); // 存尖/峰/平/古
        result.setElecPrice(po.getPrice());
        result.setServPrice(po.getServicePrice());

        if (po.getStartTime() != null) {
            result.setStartTime(getHourAndMinute(po.getStartTime()));
        }

        if (po.getStopTime() != null) {
            result.setStopTime(getHourAndMinute(po.getStopTime()));
        }

        return result;
    }

    private String getHourAndMinute(int time) {
        Integer hour = time / 60;
        Integer minute = time % 60;
        return String.format("%02d:%02d", hour, minute);
    }

    public PriceTemplatePo getPriceSchemeByEvesNo(String evseNo) {
        log.info("获取桩的计费模板: evseNo = {}", evseNo);
        if (StringUtils.isBlank(evseNo)) {
            throw new DcArgumentException("桩编号不能为空，请提供桩编号");
        }

        // 获取桩配置信息
        // t_bs_box_setting
        List<BsBoxSettingPo> settingPoList = bsBoxSettingService.getByEvseNo(
            Collections.singletonList(evseNo), null, false);
        if (settingPoList.isEmpty()) {
            log.info("该桩没有配置相关信息");
            throw new DcArgumentException("该桩没有配置相关信息，请给桩下发配置信息");
        }

        // 计费模板信息
        return this.getPriceSchema(settingPoList.get(0).getChargeId(), true).orElse(null);
    }

    public ChargePriceVo getPriceSchemeByEvesNo2(String evseNo) {
        log.info("获取桩的计费模板: evseNo = {}", evseNo);
        if (StringUtils.isBlank(evseNo)) {
            throw new DcArgumentException("桩编号不能为空，请提供桩编号");
        }

        // 获取桩配置信息
        // t_bs_box_setting
        List<BsBoxSettingPo> settingPoList = bsBoxSettingService.getByEvseNo(
            Collections.singletonList(evseNo), null, false);
        if (settingPoList.isEmpty()) {
            log.warn("该桩没有配置相关信息。 evseNo = {}", evseNo);
            return null;
        }

        // 计费模板信息
        return this.getChargePrice(settingPoList.get(0).getChargeId());
    }

//    public ChargePriceVo getChargePriceVoByEvesNo(String evseNo) {
//        log.info("evseNo={}", evseNo);
//        EvseVo evseCache = redisIotReadService.getEvseRedisCache(evseNo);
//        if (evseCache == null) {
//            throw new DcServiceException("当前充电桩不存在");
//        }
//        log.info("计费模板: templateId={}", evseCache.getPriceCode());
//
//        // 计费模板
//        TemplateInfoVo template = templateService.getTemplateDetailById(evseCache.getPriceCode());
//        if (null == template) {
//            throw new DcServiceException("当前桩还未绑定计费模板，请稍后重试");
//        }
//
//        // 转换对象
//        ChargePriceVo chargePriceVo = new ChargePriceVo();
//        chargePriceVo.setCode(template.getCode())
//                .setId(template.getId())
//                .setName(template.getName())
//                .setVersion(template.getVersion())
//                .setItemList(new ArrayList<>());
//
//        // 时段
//        template.getSubTemplateList().forEach(sub -> {
//            ChargePriceItem item = new ChargePriceItem();
//            item.setCode(sub.getNum())
//                    .setCategory(
//                            ChargePriceCategory.valueOf(
//                                    Integer.valueOf(sub.getTariffTag())))
//                    .setElecPrice(sub.getPrice())
//                    .setServPrice(sub.getServicePrice())
//                    .setStartTime(this.convert2View(sub.getStartTime()))
//                    .setEndTime(this.convert2View(sub.getStopTime()));
//            chargePriceVo.getItemList().add(item);
//        });
//
//        return chargePriceVo;
//    }

    public void updateEvseSetting(ModifyEvseCfgParam param) {
        log.info("更新桩配置信息: param = {}", JsonUtils.toJsonString(param));
        this.insertOrUpdate(param, null);
        log.info("<<");
    }

    private void insertOrUpdate(ModifyEvseCfgParam param, PriceTemplatePo template) {
        if(CollectionUtils.isEmpty(param.getEvseNoList())) {
            log.warn("桩信息不存在: siteId = {}", param.getSiteId());
            return;
        }
        // 获取存在的桩配置
        List<String> evseNoList = param.getEvseNoList();
        List<BsBoxSettingPo> byEvseNo = bsBoxSettingService.getByEvseNo(evseNoList, null, false);

        // 区分是否存在
        // 已经存在的
        List<String> existSettingList = byEvseNo.parallelStream()
            .map(BsBoxSettingPo::getBoxOutFactoryCode).collect(Collectors.toList());

        Predicate<String> settingFilter = o -> !existSettingList.contains(o);
        List<String> noExistSettingList = evseNoList.parallelStream().filter(settingFilter)
            .collect(Collectors.toList());

        // 批量更新
        byEvseNo.parallelStream().forEach(po -> {
            // 配置和计费模板分开下发: 默认带计费模板信息时表示仅下发计费模板
            if (null != template) {
                po.setTemplateCode(template.getCode()); // 计费模板信息没有
                po.setChargeId(template.getId()); // 计费模板为空
            } else {
                BeanUtils.copyProperties(param, po);
            }
        });
        long l = bsBoxSettingService.batchUpdate(byEvseNo);
        log.info("更新的数量: i = {}", l);

        // 批量插入
        long i = bsBoxSettingService.batchInsert(noExistSettingList.parallelStream().map(evseNo -> {
            // 新增调整
            BsBoxSettingPo po = new BsBoxSettingPo();
            po.setUrl(param.getQrUrl());
            po.setBoxOutFactoryCode(evseNo);
            po.setBoxCode(evseNo);
            po.setWhiteCardList(null); // 紧急充电卡为空

            // 配置和计费模板分开下发: 默认带计费模板信息时表示仅下发计费模板
            if (null != template) {
                po.setTemplateCode(template.getCode()); // 计费模板信息没有
                po.setChargeId(template.getId()); // 计费模板为空
            } else {
                BeanUtils.copyProperties(param, po);
            }

            return po;
        }).collect(Collectors.toList()));
        log.info("插入数量: i = {}", i);


    }

    @Transactional
    public PriceTemplatePo getSitePriceScheme(String siteId) {
        log.info("查看场站是否使用计费模板: siteId = {}", siteId);

        if (StringUtils.isBlank(siteId)) {
            throw new DcArgumentException("场站Id不能为空，请提供有效的场站Id");
        }

        SitePo site = this.siteRoDs.getSite(siteId);
        // t_site_default_setting
        //SiteDefultSettingVO site = siteDefultSettingService.selectBySiteId(siteId);
        //log.info("priceSchemeId = {}, code = {}", site.getChargeId());

        if (null == site) {
            log.warn("该场不存在. siteId = {}", siteId);
            throw new DcArgumentException("参数错误,场站不存在. ");
//            throw new DcArgumentException("该场站无效，请选择有效的场站");
        }
        if (site.getTemplateId() == null || site.getTemplateId().longValue() < 1L) {
            log.info("场站 {} ( {} ) 未设置计费模板", site.getSiteName(), site.getId());
            return null;
        }

        // 获取计费模板
        PriceTemplatePo priceSchema = this.priceTemplateRoDs.findById(site.getTemplateId(), false);
//        if (null == templateDetail) {
//            log.warn("场站默认使用的计费模板不存在");
//            return null;
//        }

        log.info("场站当前计费模板: priceScheme = {}", priceSchema);
        return priceSchema;
    }

    /**
     * 设置场站的默认计费模板
     *
     * @param priceSchemeId
     * @param siteId
     */
    @Transactional
    public void setDefaultPriceScheme(String siteId, Long priceSchemeId, List<EvseVo> evseVoList) {
        log.info(">> 更新场站默认计费模板: priceSchemeId = {}", priceSchemeId);
        if (null == priceSchemeId || priceSchemeId < 1L) {
            throw new DcArgumentException("计费模板Id不能为空，请提供有效计费模板Id");
        }

        if (StringUtils.isBlank(siteId)) {
            throw new DcArgumentException("场站Id不能为空，请提供有效场站Id");
        }

        PriceTemplatePo template = this.priceTemplateRoDs.findById(priceSchemeId, false);
        //template = templateService.getTemplateDetailById(priceSchemeId);
//        } catch (DcArgumentException dc) {
//            log.warn(dc.getMessage());
//        }

        if (null == template) {
            log.warn("无法通过计费模板Id获取该计费模板");
            throw new DcArgumentException("无法通过计费模板Id获取该计费模板，请提供有效计费模板Id");
        }

        // t_site_default_setting
        SiteDefaultSettingPo siteSetting = siteDefaultSettingRwDs.getBySiteId(siteId);
        if (null == siteSetting) {
            throw new DcArgumentException("无法通过场站Id获取场站信息，请提供有效的场站Id");
        }

        List<SiteTemplatePo> siteTemplateList = siteTemplateRoDs.getSiteTemplateBySiteId(siteId);
        // 根据桩类型确定第二计费模板
        /**
         * 每次下发必更新 场站t_site中的计费信息
         *  最多保持两个计费模板
         *  交流 + 直流
         */
        SiteTemplatePo siteTemplatePo = new SiteTemplatePo()
            .setSiteId(siteId)
            .setTemplateId(priceSchemeId);

        if (CollectionUtils.isNotEmpty(evseVoList)) {
            boolean hasAc = evseVoList.stream().anyMatch(x -> SupplyType.AC.equals(x.getSupplyType()));
            boolean hasDc = evseVoList.stream().anyMatch(x -> SupplyType.DC.equals(x.getSupplyType()));

            siteTemplatePo.setTemplateType(hasDc && hasAc ? SupplyType.BOTH : (hasDc ? SupplyType.DC : SupplyType.AC));
            /**
             * 最多保留两条计费模板
             * 存在 交流+ 直流  ，再次下发混合计费模板，则保留混合计费模板
             */
            int maxSize = 2;
            if (CollectionUtils.isNotEmpty(siteTemplateList) && siteTemplateList.size() == maxSize) {
                boolean hasBoth = siteTemplateList.stream()
                    .anyMatch(x -> SupplyType.BOTH.equals(x.getTemplateType()));
                // 下发混合  则删除全部
                if (SupplyType.BOTH.equals(siteTemplatePo.getTemplateType())) {
                    siteTemplateRwDs.deleteBySiteId(siteId);
                } else if (hasBoth) {  // 下发  交流 或 直流 且存在混合类型
                    siteTemplateRwDs.deleteBySiteIdAndType(siteId, SupplyType.BOTH);
                }
            }
            siteTemplateRwDs.insertTemplate(siteTemplatePo);
        }

        siteDefaultSettingRwDs.updateSiteDefaultPriceScheme(siteId, priceSchemeId);
            //log.info("i = {}", i);

            // t_site 计费模板信息更新
        siteRwDs.updateSitePriceScheme(siteId, priceSchemeId, template.getName());

//        siteDefaultSettingRwDs.updateSiteDefaultPriceScheme(siteId, priceSchemeId);
        //log.info("i = {}", i);

        // t_site 计费模板信息更新
//        siteRwDs.updateSitePriceScheme(siteId, priceSchemeId, template.getName());

        // mongo 数据修改
        // 场站计费模板信息
        Optional<SiteInMongoPo> byId = siteMongoDataRepository.findById(siteId);
        if (byId.isPresent()) {
            SiteInMongoPo siteInMongo = byId.get();
            siteInMongo.setChargePrice(this.getTemplate(template));
            log.info("mongo 更新 siteInMongo = {}", siteInMongo);
            // 多计费模板
            List<SiteChargePriceVo> sitePriceList = this.getSitePriceList(siteId);
            List<ChargePriceVo> chargePriceList = JsonUtils.fromJson(JsonUtils.toJsonString(sitePriceList), new TypeReference<List<ChargePriceVo>>() {});
            siteInMongo.setChargePriceList(chargePriceList);
            siteMongoDataRepository.save(siteInMongo);
        }
        // 推送MQ
        SitePo site = this.siteRoDs.getSite(siteId);
        dcEventPublisherService.publishSiteInfo(site);
        log.info("<< 更新完成");
    }

    @Transactional
    public ChargePriceVo getChargePrice(long priceSchemaId) {
        Optional<PriceTemplatePo> priceSchema = this.getPriceSchema(priceSchemaId, true);
        if (priceSchema.isEmpty()) {
            return null;
        } else {
            return this.getTemplate(priceSchema.get());
        }
    }

    private ChargePriceVo getTemplate(PriceTemplatePo priceSchema) {
        // 场站计费模板信息
//        Long templateId = site.getTemplateId();
        if (priceSchema == null || priceSchema.getId() == null
            || priceSchema.getId().longValue() < 1L) {
            log.error("参数错误. priceSchema = {}", priceSchema);
            return null;
        }

        List<PriceItemPo> items = this.priceItemRoDs.getPriceItemListByTempId(priceSchema.getId());

        ChargePriceVo chargePriceVo = new ChargePriceVo();
        chargePriceVo.setCode(priceSchema.getCode())
            .setId(priceSchema.getId())
            .setName(priceSchema.getName())
            .setVersion(null != priceSchema.getVersion() ? priceSchema.getVersion().intValue() : 0)
            .setItemList(new ArrayList<>());

        // 时段
        items.forEach(sub -> {
            ChargePriceItem item = new ChargePriceItem();
            item.setCode(sub.getNum())
                .setCategory(sub.getCategory())
                .setElecPrice(sub.getPrice())
                .setServPrice(sub.getServicePrice())
                .setStartTime(this.convert2View(sub.getStartTime()))
                .setEndTime(this.convert2View(sub.getStopTime()));
            chargePriceVo.getItemList().add(item);
        });

        return chargePriceVo;


    }

    /**
     * 将分钟转成可视时间(一天总共分钟: 24*60=1440)
     *
     * @param minute
     * @return example = "1635", description = "格式为HHMM. 每日的开始时间为 0000, 当日结束时间为 2400"
     */
    private String convert2View(Integer minute) {
        String h = String.format("%02d", minute / 60);
        String m = String.format("%02d", minute % 60);
        return h + m;
    }

    /**
     * 单桩配置下发（下发上次计费信息）
     *
     * @param evseNo
     */
    public String sendPriceSchema(String evseNo) {
        // 单桩配置下发
        SiteDefaultSettingPo setting = siteDefaultSettingRwDs.selectByEvseNo(evseNo);
        if (null == setting || setting.getChargeId() == null) {
            throw new DcArgumentException("请先选择并下发计费模板给到场站中的桩", Level.WARN);
        }

        List<String> evseNoList = List.of(evseNo);
        List<BsBoxSettingPo> evse = bsBoxSettingService.getByEvseNo(evseNoList, null, false);
        if (CollectionUtils.isEmpty(evse) || evse.get(0).getChargeId() == null) {
            throw new DcArgumentException("请先选择并下发计费模板给到场站中的桩", Level.WARN);
        }

        // 对桩操作电价下发仅是对已经下发过计费信息的桩，桩掉电价，
        // 重新将之前最后一次下发的计费信息重新下发一遍；
        ModifyEvseCfgParam param = new ModifyEvseCfgParam();
        param.setEvseNoList(evseNoList);
        param.setPriceSchemeId(evse.get(0).getChargeId());

        List<PriceItemPo> itemList = priceSchemaBizService.getPriceItemListByTempId(
            evse.get(0).getChargeId());
        log.info("桩: {}, 取到计费模板: {}", evseNo, itemList);
        param.setPriceSchemeList(priceSchemaBizService.priceItem2Charge(itemList));

        BaseResponse res = this.iotBizClient.modifyEvseCfgV2(param);
        FeignResponseValidate.check(res);

        // t_bs_box_setting 中预存数据
        this.updateEvseSetting(param);

        PriceTemplatePo priceTemplatePo = priceTemplateRoDs.findById(evse.get(0).getChargeId(),
            false);
        return priceTemplatePo.getName(); // 避免编译器优化
    }

    public List<PriceTemplatePo> getHlhtSitePriceTemplateList(String siteId) {
        return this.priceTemplateRoDs.getHlhtSitePriceTemplateList(siteId);
    }

    public List<PriceTemplatePo> getTimeBasedPriceSchemaList(List<Long> priceIdList,
        Boolean enable) {
        log.info("获取计费模板信息列表(带分时段计费信息): priceIdList = {}", priceIdList);

        List<PriceTemplatePo> result = priceTemplateRoDs.findByIdListAndEnable(priceIdList, enable);
        if (CollectionUtils.isEmpty(result)) {
            return result;
        }
        result.forEach(priceTemplatePo -> {
            priceTemplatePo.setPriceItemList(
                this.priceItem2Charge(this.getPriceItemListByTempId(priceTemplatePo.getId())));
        });
        log.info("获取计费模板信息列表: result = {}", result);
        return result;
    }

    /**
     * 获取计费模版的模版列表
     *
     * @param param
     * @return
     */
    public ListResponse<PriceTemplateModVo> getPriceTemplateModList(ListPriceTemplateParam param) {
        // 分页信息
        if (null == param.getStart()) {
            param.setStart(0L);
        }
        if (null == param.getSize() || param.getSize() <= 0) {
            param.setSize(10);
        }

        List<PriceTemplateModVo> priceTemplateModVoList = this.priceTemplateModRoDs.getPriceTemplateModList(param);
        if (CollectionUtils.isEmpty(priceTemplateModVoList)) {
            return new ListResponse<>();
        }
        Long total = this.priceTemplateModRoDs.getPriceTemplateModListCount(param);
        return new ListResponse<>(priceTemplateModVoList, total != null ? total : 0L);
    }
}
