package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.PayBillBizService;
import com.cdz360.biz.model.cus.wallet.vo.RefundAnalyzeVo;
import com.cdz360.biz.model.trading.order.dto.PayBillInvoiceBi;
import com.cdz360.biz.model.trading.order.param.CzOrderPointParam;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.model.trading.order.param.ZftBillParam;
import com.cdz360.biz.model.trading.order.po.PayBillPo;
import com.cdz360.biz.model.trading.order.type.PayBillStatus;
import com.cdz360.biz.model.trading.order.vo.*;
import com.cdz360.biz.model.wallet.vo.RefundReasonVo;
import com.chargerlinkcar.framework.common.constant.CheckTaxStatus;
import com.chargerlinkcar.framework.common.domain.PointRecDto;
import com.chargerlinkcar.framework.common.domain.PointRecLogDto;
import com.chargerlinkcar.framework.common.domain.vo.OrderBiVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import java.util.Date;

/**
 * 充值提现审核相关接口
 *
 * <AUTHOR>
 * @since 2019/11/4 16:24
 */
@Slf4j
@RestController
@Tag(name = "充值提现审核相关接口", description = "pay-bill")
public class PayBillRest {

    @Autowired
    private PayBillBizService payBillService;

    @Operation(summary = "校验充值记录是否可以较少操作", description = "true -- 不可以操作, false -- 可以操作")
    @GetMapping("/dataCore/paybill/checkRefPayBill")
    public ObjectResponse<Boolean> checkRefPayBill(
            ServerHttpRequest request,
            @Parameter(name = "充值记录号", required = true) @RequestParam(value = "refBillNo") String refBillNo) {
        log.info("校验充值记录是否可以较少操作: {}", refBillNo);
        return RestUtils.buildObjectResponse(payBillService.checkRefPayBill(refBillNo));
    }

    @Operation(summary = "充值记录查看")
    @GetMapping("/dataCore/paybill/view")
    public ObjectResponse<PayBillVo> payBillView(
            @Parameter(name = "充值记录订单号", example = "********************", required = true) @RequestParam(value = "orderId") String orderId,
            ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return new ObjectResponse<>(payBillService.payBillView(orderId));
    }

    @Operation(summary = "退款记录查看")
    @GetMapping("/dataCore/paybill/tkView")
    public Mono<ObjectResponse<PayBillVo>> payBillTkView(
        @Parameter(name = "退款记录号", example = "TK201911141856240018", required = true) @RequestParam(value = "outRefundNo") String outRefundNo,
        ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return payBillService.payBillTkView(outRefundNo);
    }

    @Operation(summary = "获取充值记录列表")
    @PostMapping("/dataCore/paybill/orderList")
    public ListResponse<PayBillVo> getPayBillList(
            @RequestBody PayBillParam param,
            ServerHttpRequest request) {
        log.debug("获取充值记录列表: {}, param = {}",
                LoggerHelper2.formatEnterLog(request, false), JsonUtils.toJsonString(param));
        return payBillService.payBillList(param);
    }

    @Operation(summary = "获取直付通对账订单列表")
    @PostMapping("/dataCore/paybill/getZftBillList")
    public Mono<ListResponse<ZftBillVo>> getZftBillList(
            @RequestBody ZftBillParam param,
            ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param={}" + param);
        return payBillService.zftBillList(param);
    }

    @Operation(summary = "获取用户充值记录到账账户，支付账户列表")
    @PostMapping("/dataCore/paybill/userBillAccountName")
    public ObjectResponse<UserBillAccountNameVo> userBillAccountName(
            @RequestBody PayBillParam param,
            ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param={}" + param);
        return payBillService.userBillAccountName(param);
    }

    @Operation(summary = "获取企业客户充值记录列表")
    @PostMapping("/dataCore/paybill/invoiceOrderList")
    public ListResponse<PayBillInvoiceBi> invoiceOrderList(
            @RequestBody PayBillParam param,
            ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param={}" + param);
        return payBillService.invoiceOrderList(param);
    }

    @Operation(summary = "获取企业客户充值记录汇总")
    @PostMapping("/dataCore/paybill/invoiceOrderBiForCorp")
    public ObjectResponse<OrderBiVo> invoiceOrderBiForCorp(
            @RequestBody PayBillParam param,
            ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param={}" + param);
        return payBillService.invoiceOrderBiForCorp(param);
    }

    @Operation(summary = "统计充值记录数据")
    @PostMapping("/dataCore/paybill/bi")
    public ListResponse<PayBillBi> payBillBi(
            @RequestBody PayBillParam param,
            ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param={}" + param);
        return new ListResponse<>(payBillService.payBillBi(param));
    }

    @Operation(summary = "统计直付通记录数据")
    @PostMapping("/dataCore/zftbill/bi")
    public ListResponse<ZftBillBi> zftBillBi(
            @RequestBody ZftBillParam param,
            ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param={}" + param);
        return new ListResponse<>(payBillService.zftBillBi(param));
    }

    @Operation(summary = "通过充值记录Id更新充值信息")
    @PostMapping("/dataCore/paybill/updateById")
    public ObjectResponse<Integer> updateById(
            @RequestBody PayBillPo po,
            ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param={}" + po);

        return new ObjectResponse<>(payBillService.updateById(po));
    }

    @Operation(summary = "通过充值记录Id更新充值信息")
    @PostMapping("/dataCore/paybill/updateStatusByOrderId")
    public ObjectResponse<Integer> updateStatusByOrderId(
            @Parameter(name = "充值记录订单号", example = "********************", required = true) @RequestParam(value = "orderId") String orderId,
            @Parameter(name = "充值记录原来状态", required = true) @RequestParam(value = "srcStatus") int srcStatus,
            @Parameter(name = "充值记录目标的状态", required = true) @RequestParam(value = "targetStatus") int targetStatus,
            ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request));

        return new ObjectResponse<>(payBillService.updateStatusByOrderId(orderId, srcStatus, targetStatus));
    }

    @Operation(summary = "通过充值订单号更新充值信息")
    @PostMapping("/dataCore/paybill/updateByOrderId")
    public ObjectResponse<Integer> updateByOrderId(
            @RequestBody PayBillPo po,
            ServerHttpRequest request) {
        log.debug(LoggerHelper2.formatEnterLog(request, false) + " param={}" + po);

        return new ObjectResponse<>(payBillService.updateByOrderId(po));
    }

    @Operation(summary = "新增充值记录")
    @PostMapping("/dataCore/paybill/insert")
    public ObjectResponse<Integer> insert(
            @RequestBody PayBillPo po,
            ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + " param={}" + po);
        return new ObjectResponse<>(payBillService.insert(po));
    }

    @Operation(summary = "获取充值记录资金块详情")
    @GetMapping("/dataCore/paybill/pointRecLog")
    public ObjectResponse<PayBillUsedDetail> pointRecLog(
            @Parameter(name = "充值记录订单号", example = "********************", required = true) @RequestParam(value = "orderId") String orderId,
            @Parameter(name = "充电订单号", example = "1194797842524704768") @RequestParam(value = "orderNo", required = false) String orderNo,
            ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request));
        //return RestUtils.buildObjectResponse(null);
        return RestUtils.buildObjectResponse(payBillService.pointRecLog(orderId, orderNo));
    }

    @Operation(summary = "充电订单的资金块信息")
    @GetMapping("/dataCore/paybill/orderPointRecLog")
    public ListResponse<PayBillLinkChargeOrderVo> orderPointRecLog(
            @Parameter(name = "充电订单号", example = "1194797842524704768") @RequestParam(value = "orderNo") String orderNo,
            ServerHttpRequest request
    ) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return RestUtils.buildListResponse(payBillService.orderPointRecLog(orderNo, true));
    }

    /**
     * {@code
     * <ul>
     * <li>PERSONAL:
     * {
     *   "accountType": "PERSONAL",
     *   "orderIdList": ["********************"],
     *   "userId": 84528,
     *   "commId": 34474
     * }
     *
     * <li>COMMERCIAL:
     * {
     *   "userId": "84543",
     *   "accountType": "COMMERCIAL",
     * 	 "commId": 34632,
     * 	 "orderIdList":["********************"]
     * }
     *
     * <li>CREDIT:
     * {
     *   "CR_accountType": "CREDIT",
     * 	 "CR_orderIdList":["********************"],
     *   "CR_corpId": 294
     * }
     * }
     * @param param
     * @param request
     * @return
     */
    @Operation(summary = "充值订单的资金块信息")
    @PostMapping("/dataCore/paybill/getCzOrderPointsInfo")
    public ListResponse<PointRecDto> getCzOrderPointsInfo(
            @RequestBody CzOrderPointParam param,
            ServerHttpRequest request
    ) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return RestUtils.buildListResponse(payBillService.getCzOrderPointsInfo(param));
    }

    @Operation(summary = "获取充值记录充值时的账户信息")
    @GetMapping("/dataCore/paybill/accountDetail")
    public ObjectResponse<PayBillAccountDetailVo> accountDetail(
            @Parameter(name = "充值记录订单号", example = "********************", required = true) @RequestParam(value = "orderId") String orderId,
            ServerHttpRequest request
    ) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return new ObjectResponse<>(payBillService.accountDetail(orderId));
    }

    @Operation(summary = "判断充值订单是否存在资金块在发票中心已开票或审核中的开票订单")
    @PostMapping("/dataCore/paybill/checkTaxStatus")
    public ObjectResponse<CheckTaxStatus> checkTaxStatus(
            ServerHttpRequest request,
            @Parameter(name = "充值记录订单号", example = "********************", required = true)
            @RequestParam(value = "orderId") String orderId) {
        log.info(LoggerHelper2.formatEnterLog(request));
        return payBillService.checkTaxStatus(orderId);
    }

    @Operation(summary = "变更充值订单的状态")
    @PostMapping("/dataCorder/paybill/updateStatus")
    public ObjectResponse<Integer> updateStatus(
            @Parameter(name = "充值记录订单号", example = "********************", required = true) @RequestParam(value = "orderId") String orderId,
            @Parameter(name = "目标状态", example = "3", required = true) @RequestParam(value = "status") PayBillStatus status,
            ServerHttpRequest request) {
        log.info(LoggerHelper2.formatEnterLog(request));

        return new ObjectResponse<>(payBillService.updateStatus(orderId, status));
    }

    @Operation(summary = "退款数据分析")
    @GetMapping("/dataCorder/refund/analyze")
    public ObjectResponse<RefundAnalyzeVo> refundAnalyze(ServerHttpRequest request,
                                                         @RequestParam(value = "startDate", required = false) @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date startDate,
                                                         @RequestParam(value = "stopDate", required = false) @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss") Date stopDate) {
        log.info("request: {}, startDate: {}, stopDate: {}", LoggerHelper2.formatEnterLog(request), startDate, stopDate);

        return RestUtils.buildObjectResponse(payBillService.refundAnalyze(startDate, stopDate));
    }

    @Operation(summary = "退款原因列表")
    @GetMapping("/dataCorder/refund/list")
    public ListResponse<RefundReasonVo> refundList(ServerHttpRequest request,
                                                   @RequestParam(value = "cusName", required = false) String cusName,
                                                   @RequestParam(value = "cusPhone", required = false) String cusPhone,
                                                   @RequestParam(value = "cusNote", required = false) String cusNote,
                                                   @RequestParam(value = "start") int start,
                                                   @RequestParam(value = "size") int size) {
        log.info("request: {}, cusName: {}, cusPhone: {}, cusNote: {}, start: {}, size: {}",
                LoggerHelper2.formatEnterLog(request), cusName, cusPhone, cusNote, start, size);

        return payBillService.refundList(cusName, cusPhone, cusNote, start, size);
    }

    /**
     * 在线充值单查询
     *
     * @param orderId
     * @return
     */
    @GetMapping("/dataCore/paybill/payOrderQuery")
    public Mono<ObjectResponse<PayBillVo>> payOrderQuery(
            ServerHttpRequest request,
            @Parameter(name = "充值单号", required = true) @RequestParam(value = "orderId") String orderId) {
        log.debug("在线充值单查询: {}", LoggerHelper2.formatEnterLog(request));
        return payBillService.payOrderQuery(orderId);
    }
}
