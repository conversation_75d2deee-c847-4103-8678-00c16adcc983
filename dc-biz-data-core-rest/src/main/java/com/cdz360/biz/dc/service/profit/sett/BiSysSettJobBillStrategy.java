package com.cdz360.biz.dc.service.profit.sett;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ds.trading.ro.order.ds.BiSiteFinanceMonthRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.BiSiteMonthExpenseRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.BiSiteMonthIncomeRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.BiSiteOrderRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.model.site.vo.BiSiteExpenseSumByMonthVo;
import com.cdz360.biz.model.site.vo.BiSiteIncomeSumByMonthVo;
import com.cdz360.biz.model.trading.profit.sett.dto.BiSysSettJobBillDto;
import com.cdz360.biz.model.trading.profit.sett.po.CalculateRuleBase;
import com.cdz360.biz.model.trading.profit.sett.po.SettJobBillPo;
import com.cdz360.biz.model.trading.profit.sett.type.ProfitCfgCalSource;
import com.cdz360.biz.model.trading.profit.sett.vo.ProfitCfgVo;
import com.cdz360.biz.model.trading.site.param.SettJobProfitBiParam;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.site.vo.SettJobPowerUseRateVo;
import com.chargerlinkcar.framework.common.domain.vo.BiSiteFinanceMonthData;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import jakarta.annotation.PostConstruct;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BiSysSettJobBillStrategy extends
    AbstractSettJobBillStrategy<BiSysSettJobBillDto, SettJobProfitBiParam> {

    // 规则计算字段映射
    private static final Map<String, String> RULE_KEY_MAP = new HashMap<>() {{
        put("income", "income"); // 运营收入
        put("syselec", "orderElec"); // 订单电量
        put("elecPaid", "elecFee"); // 实缴电费
        put("rent", "rentFee"); // 租金
        put("lab", "laborFee"); // 劳务
        put("sale", "attractFee"); // 引流
        put("ops", "opsFee"); // 运维
        put("loss", "depreciationFee"); // 折旧
        put("other", "otherFee"); // 其他
        put("useRate", "powerUseRate"); // 平均利用率（功率）
    }};

    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private BiSiteOrderRoDs biSiteOrderRoDs;

    @Autowired
    private BiSiteFinanceMonthRoDs biSiteFinanceMonthRoDs;

    @Autowired
    private BiSiteMonthIncomeRoDs siteMonthIncomeRoDs;

    @Autowired
    private BiSiteMonthExpenseRoDs siteMonthExpenseRoDs;

    @PostConstruct
    public void init() {
        this.settJobBillStrategyFactory.addStrategy(ProfitCfgCalSource.FROM_BI_SYS, this);
    }

    @Override
    protected SettJobProfitBiParam statisticsParam(LocalDate refer, String siteId,
        ProfitCfgVo cfg) {
        SettJobProfitBiParam param = new SettJobProfitBiParam()
            .setSiteId(siteId)
            .setTimeTarget(cfg.getTimeTarget());
        return param.setMonth(refer.minusMonths(1)
            .withDayOfMonth(cfg.getMonthDay()));
    }

    @Override
    protected BiSysSettJobBillDto statistics(SettJobProfitBiParam param) {
        BiSysSettJobBillDto result = new BiSysSettJobBillDto()
            .setSiteId(param.getSiteId());

        // 收入
        BiSiteIncomeSumByMonthVo income = siteMonthIncomeRoDs.settJobProfitBi(param);
        if (null != income) {
            result.setIncome(income.getElecFee().add(income.getServFee()));
        } else {
            result.setIncome(BigDecimal.ZERO);
        }

        // 订单电量-平台电量(上传时间)
        BiSiteFinanceMonthData monthData = biSiteFinanceMonthRoDs.getSiteMonthData(
            param.getSiteId(), param.getMonth().getYear(), param.getMonth().getMonthValue());
        result.setOrderElec(null != monthData && null != monthData.getElec() ?
            monthData.getElec() : BigDecimal.ZERO);

        // 支出
        BiSiteExpenseSumByMonthVo expense = siteMonthExpenseRoDs.settJobProfitBi(param);
        if (null != expense) {
            BeanUtils.copyProperties(expense, result);
        } else {
            result.setFee(BigDecimal.ZERO)
                .setElecFee(BigDecimal.ZERO)
                .setRentFee(BigDecimal.ZERO)
                .setDivideFee(BigDecimal.ZERO)
                .setLaborFee(BigDecimal.ZERO)
                .setAttractFee(BigDecimal.ZERO)
                .setOpsFee(BigDecimal.ZERO)
                .setDepreciationFee(BigDecimal.ZERO)
                .setOtherFee(BigDecimal.ZERO);
        }

        // 功率利用率
        SettJobPowerUseRateVo power = biSiteOrderRoDs.settJobPowerUseRate(param);
        if (null != power && null != power.getElec() && null != power.getPower()) {
            SitePo site = siteRoDs.getSite(param.getSiteId()); // 获取场站上线时间
            Date online = site.getOnlineDate();
            if (null == online) {
                online = new Date();
            }

            LocalDateTime from = param.getMonth().atStartOfDay();
            LocalDateTime middle = online.toInstant().atOffset(ZoneOffset.of("+8"))
                .toLocalDateTime();
            long len = Duration.between(from.isBefore(middle) ? middle : from,
                param.getMonth().plusMonths(1).atStartOfDay()).toDays();

            // 月充电量 / （ 有效功率 * 天数 * 24 ）
            if (len > 0 && DecimalUtils.gtZero(power.getPower())) {
                result.setPowerUseRate(power.getElec()
                    .divide(power.getPower()
                        .multiply(new BigDecimal(len * 24)), 6, RoundingMode.HALF_DOWN));
            } else {
                result.setPowerUseRate(BigDecimal.ZERO);
            }
        } else {
            result.setPowerUseRate(BigDecimal.ZERO);
        }
        return result;
    }

    @Override
    protected SettJobBillPo compute(ProfitCfgVo cfg, SettJobProfitBiParam param) {
        ExpressionComputeRuleEntity<BigDecimal> calRule =
            new ExpressionComputeRuleEntity<>(BigDecimal.class);

        SettJobBillPo result = new SettJobBillPo();
        result.setElecFee(BigDecimal.ZERO)
            .setServFee(BigDecimal.ZERO)
            .setParkFee(BigDecimal.ZERO);

        BiSysSettJobBillDto statistics = this.statistics(param);
        log.info("[normal]计算前: cfgId = {}, siteId = {}, stat = {}",
            cfg.getId(), param.getSiteId(), statistics);
        HashMap<String, Object> var = new HashMap<>();
        BeanMap beanMap = BeanMap.create(statistics);
        RULE_KEY_MAP.forEach((k, v) -> var.put(k, beanMap.get(v)));
        calRule.setVariables(var);

        CalculateRuleBase rule;
        if (CollectionUtils.isNotEmpty(cfg.getBiRule().getRange())) {
            // 找出符合条件计算规则
            final ExpressionComputeRuleEntity<Boolean> expression =
                new ExpressionComputeRuleEntity<>(Boolean.class);
            expression.setVariables(var);
            rule = cfg.getBiRule().getRange().stream()
                .filter(x -> StringUtils.isNotBlank(x.getCondition()) &&
                    expression.setExpression(x.getCondition()).compute())
                .map(x -> (CalculateRuleBase) x)
                .findFirst().orElse(cfg.getBiRule().getBase());
        } else {
            rule = cfg.getBiRule().getBase();
        }

        result.setDataSource(JsonUtils.toJsonString(statistics))
            .setElec(statistics.getOrderElec())
            .setElecFee(result.getElecFee().add(
                calRule.setExpression(rule.getElecFee()).compute()))
            .setServFee(result.getServFee().add(
                calRule.setExpression(rule.getServFee()).compute()))
            .setParkFee(result.getParkFee().add(
                calRule.setExpression(rule.getParkFee()).compute()));
        log.info("计算后: cfgId = {}, siteId = {}, result = {}",
            cfg.getId(), param.getSiteId(), result);
        return result;
    }

    @Override
    public void generateSettJobBill(String siteId, ProfitCfgVo cfg) {
        IotAssert.isNotNull(cfg.getBiRule(), "未配置计算规则");

        SettJobProfitBiParam biParam = this.statisticsParam(
            cfg.getGenerateDay() >= cfg.getMonthDay() ?
                LocalDate.now() : LocalDate.now().minusMonths(1), siteId, cfg);
        SettJobBillPo settJobBill = this.compute(cfg, biParam); // 规则计算
        assignSettJobBill(settJobBill.setSiteId(siteId), cfg);
        super.insertSettJobBill(settJobBill);
    }

    @Override
    public void recalculateSettJobBill(SettJobBillPo settJobBillPo, ProfitCfgVo cfg) {
        if (null == cfg) {
            cfg = new ProfitCfgVo()
                .setBiRule(settJobBillPo.getBiRule())
                .setCategory(settJobBillPo.getJobCategory());
        } else {
            settJobBillPo.setChargeOrderRules(cfg.getChargeOrderRules())
                .setJobName(cfg.getName())
                .setJobCategory(cfg.getCategory())
                .setJobCalSource(cfg.getCalSource())
                .setBiRule(cfg.getBiRule())
                .setRemark(cfg.getRemark());
        }

        String siteId = settJobBillPo.getSiteId();
        LocalDate date = DateUtil.dateToLocalDate(settJobBillPo.getSettPeriodTo());
        cfg.setMonthDay(date.getDayOfMonth())
            .setGenerateDay(
                DateUtil.dateToLocalDate(settJobBillPo.getCreateTime()).getDayOfMonth());

        SettJobProfitBiParam biParam = this.statisticsParam(DateUtil.dateToLocalDate(
            settJobBillPo.getSettPeriodTo()), siteId, cfg);
        SettJobBillPo data = this.compute(cfg, biParam); // 规则计算
        settJobBillPo.setDataSource(data.getDataSource())
            .setElec(data.getElec())
            .setElecFee(data.getElecFee())
            .setServFee(data.getServFee())
            .setParkFee(data.getParkFee());

        super.updateSettJobBill(settJobBillPo);
    }

    private static void assignSettJobBill(
        SettJobBillPo settJobBill, ProfitCfgVo cfg) { // 赋值
        settJobBill.setJobId(cfg.getId())
            .setChargeOrderRules(cfg.getChargeOrderRules())
            .setJobName(cfg.getName())
            .setJobCategory(cfg.getCategory())
            .setJobCalSource(cfg.getCalSource())
            .setBiRule(cfg.getBiRule())
            .setRemark(cfg.getRemark());

        Integer monthDay = cfg.getMonthDay(); // 结算周期
        Integer generateDay = cfg.getGenerateDay(); // 生成日期
        LocalDate now = LocalDate.now();

        if (generateDay >= monthDay) {
            settJobBill.setSettPeriodFrom(DateUtil.localDateToDate(
                    now.minusMonths(1).withDayOfMonth(monthDay)))
                .setSettPeriodTo(DateUtil.localDateToDate(now.withDayOfMonth(monthDay)));
        } else {
            settJobBill.setSettPeriodFrom(DateUtil.localDateToDate(
                    now.minusMonths(2).withDayOfMonth(monthDay)))
                .setSettPeriodTo(DateUtil.localDateToDate(
                    now.minusMonths(1).withDayOfMonth(monthDay)));
        }
    }
}
