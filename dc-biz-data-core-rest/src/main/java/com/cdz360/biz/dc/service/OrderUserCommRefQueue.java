package com.cdz360.biz.dc.service;

import com.cdz360.base.utils.JsonUtils;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class OrderUserCommRefQueue {

    @Autowired
    private SyncUserCommRefService syncUserCommRefService;

    private OrderUserCommRefProcessor processor;

    @PostConstruct
    public void init() {
        processor = new OrderUserCommRefProcessor();
        processor.setCallback(new OrderUserCommRefProcessorCallback<ChargerOrder>() {
            @Override
            public void onArrived(ChargerOrder order) {
                syncUserCommRefService.insertUserCommRef(order);
            }

            @Override
            public void onFailed(ChargerOrder order, Exception ex) {
                log.warn("处理充电订单同步商户关系失败: order = {}", JsonUtils.toJsonString(order));
            }
        });

        startProcessor(processor);
    }

    private void startProcessor(OrderUserCommRefProcessor processor) {
        ExecutorService executorService = new ThreadPoolExecutor(1,
                1,
                0L,
                TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>());

        new FinalizableDelegatedExecutorService(executorService).execute(processor);
    }

    public void pushAll(List<ChargerOrder> orderList) {
        orderList.forEach(this::push);
    }

    public void push(ChargerOrder order) {
        processor.push(order);
    }
}
