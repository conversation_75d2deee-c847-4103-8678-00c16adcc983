package com.cdz360.biz.dc.client.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.chargerlinkcar.framework.common.domain.vo.CommCusRef;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

/**
 * ReactorUserFeignClient
 *
 * @since 11/3/2020 10:31 AM
 * <AUTHOR>
 */
@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_USER,
        fallbackFactory = ReactorUserFeignClientHystrixFactory.class)
public interface ReactorUserFeignClient {

    @GetMapping("/api/user/moveCorp/move")
    Mono<ObjectResponse<Boolean>> userMoveCorp(@RequestParam(value = "corpId") Long corpId,
                                               @RequestParam(value = "commId") Long commId);

    @GetMapping(value = "/api/merchantBalance/findById")
    Mono<ObjectResponse<CommCusRef>> findById(@RequestParam("id") Long id);

}