package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.client.reactor.BiDownloadJobFeignClient;
import com.cdz360.biz.ds.trading.ro.download.ds.DownloadJobRoDs;
import com.cdz360.biz.ds.trading.rw.download.ds.DownloadJobRwDs;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.dto.DownloadJobDto;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.param.GenerateResult;
import com.cdz360.biz.model.download.param.ListDownloadJobParam;
import com.cdz360.biz.model.download.po.DownloadJobPo;
import com.cdz360.biz.model.download.type.DownloadJobStatus;
import com.cdz360.biz.model.download.vo.DownloadJobVo;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.utils.UUIDUtils;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.io.File;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

@Slf4j
@Service
public class DownloadFileService {

    @Value("${download.max:3}")
    private Integer DOWNLOAD_MAX;

    @Value("${download.timeout:10}")
    private Integer DOWNLOAD_TIMEOUT; // 单位: 分钟

    @Value("${download.file.keep:5}")
    private Integer DOWNLOAD_FILE_KEEP; // 下载文件保留时间，单位: 天

    @Autowired
    private DownloadJobRoDs downloadJobRoDs;

    @Autowired
    private DownloadJobRwDs downloadJobRwDs;

    @Autowired
    private BiDownloadJobFeignClient biDownloadJobFeignClient;

    @Autowired
    private MessageSource messageSource;

    public Mono<ExcelPosition> downloadFileApply(DownloadApplyParam param) {
        if (null == param.getUid()) {
            throw new DcArgumentException("申请用户ID无效");
        }

        // 当前用户正在下载任务数不能超过指定数量
        Long cnt = downloadJobRoDs.userDownloadingCount(param.getUid());
        if (cnt >= DOWNLOAD_MAX) { // 读写分离，从库统计可能不太准确，所以使用大于等于
            throw new DcArgumentException("当前已存在" + DOWNLOAD_MAX + "个下载任务，请稍后再试");
        }

        // 构建下载任务
        ExcelPosition filePos = new ExcelPosition()
            .setSubFileName(UUIDUtils.getUuid32())
            .setSubDir("download_job" + File.separator +
                LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));

        DownloadJobPo applyPo = new DownloadJobPo();
        applyPo.setUid(param.getUid())
            .setReqParam(param.getReqParam())
            .setFileType(param.getFileType())
            .setExFileName(param.getExFileName())
            .setFunctionMap(param.getFunctionMap())
            .setFilePosition(JsonUtils.toJsonString(filePos))
            .setStatus(DownloadJobStatus.WAIT);
        boolean b = downloadJobRwDs.insertDownloadJob(applyPo);
        if (!b) {
            throw new DcServiceException("新增下载任务失败");
        }
        return Mono.just(filePos);
    }

    public Mono<ExcelPosition> downloadFilePrintApply(DownloadApplyParam param) {
        if (null == param.getUid()) {
            throw new DcArgumentException("申请用户ID无效");
        }
        // 当前用户正在下载任务数不能超过指定数量
        Long cnt = downloadJobRoDs.userDownloadingCount(param.getUid());
        if (cnt >= DOWNLOAD_MAX) { // 读写分离，从库统计可能不太准确，所以使用大于等于
            throw new DcArgumentException("当前已存在" + DOWNLOAD_MAX + "个下载任务，请稍后再试");
        }
        ExcelPosition filePos = new ExcelPosition()
                .setSubFileName(UUIDUtils.getUuid32())
                .setSubDir("download_job" + File.separator +
                        LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        DownloadJobDto jobDto = new DownloadJobDto()
                .setFilePosition(JsonUtils.toJsonString(filePos))
                .setFileType(param.getFileType())
                .setFunctionMap(param.getFunctionMap())
                .setReqParam(param.getReqParam());

        return biDownloadJobFeignClient.generatePrintFile(jobDto)
                .flatMap(res -> {
                    if (res.getStatus() == ResultConstant.RES_SUCCESS_CODE) {
                        log.info("打印文件生成成功，并返回" + JsonUtils.toJsonString(filePos));
                        return Mono.just(filePos);
                    } else {
                        return Mono.error(new RuntimeException("Generate file failed: " + res.getError()));
                    }
                });
    }

    public Mono<ListResponse<DownloadJobVo>> downloadApplyList(ListDownloadJobParam param) {
        return Mono.just(param)
            .doOnNext(p -> {
                if (null == param.getSize() || param.getSize() > 10000) {
                    param.setSize(9999);
                }
            })
            .map(this.downloadJobRoDs::downloadJobList)
            .map(RestUtils::buildListResponse)
            .doOnNext(res -> {
                if (null != param.getTotal() && param.getTotal()) {
                    res.setTotal(this.downloadJobRoDs.count(param));
                } else {
                    res.setTotal(0L);
                }
                // 海外版，单独处理文件名称
                if (null != param.getLocale() && null != res.getData()) {
                    Map<String, String> fileNameI18nMap = new HashMap<>();
                    res.getData().forEach(d -> {
                        if (!fileNameI18nMap.containsKey(d.getExFileName())) {
                            if (d.getExFileName().startsWith("充值订单")
                                && d.getExFileName().length() == 12) {
                                // 海外版，带占位符的需要特殊处理
                                // 充值订单20250307
                                fileNameI18nMap.put(d.getExFileName(),
                                    messageSource.getMessage("充值订单",
                                        new Object[]{d.getExFileName().substring(4, 12)},
                                        param.getLocale()));
                            } else {
                                fileNameI18nMap.put(d.getExFileName(),
                                    messageSource.getMessage(d.getExFileName(), null,
                                        param.getLocale()));
                            }
                        }
                        d.setExFileName(fileNameI18nMap.get(d.getExFileName()));
                    });
                }
            });
    }

    public Mono<BaseResponse> executeDownloadJob() {
        // 算上超时时间
        LocalDateTime from = LocalDateTime.now().minusMinutes(DOWNLOAD_TIMEOUT);

        boolean has = downloadJobRoDs.hasDownloadingJob(from);
        if (!has) {
            // 获取等待下载的任务
            DownloadJobPo applyPo = downloadJobRoDs.getNextDownloadJob();
            if (null != applyPo) {
                log.info("执行下载任务: {}", applyPo);
                DownloadJobDto jobDto = new DownloadJobDto()
                    .setFilePosition(applyPo.getFilePosition())
                    .setFileType(applyPo.getFileType())
                    .setFunctionMap(applyPo.getFunctionMap())
                    .setId(applyPo.getId())
                    .setReqParam(applyPo.getReqParam());
                biDownloadJobFeignClient.generateFile(jobDto)
                    .subscribe(res -> {
                        if (res.getStatus() == ResultConstant.RES_SUCCESS_CODE) {
                            DownloadJobPo updatePo = new DownloadJobPo()
                                .setId(applyPo.getId())
                                .setStatus(DownloadJobStatus.PROCESSING)
                                .setStartTime(new Date());
                            downloadJobRwDs.updateDownloadApply(updatePo);
                        }
                    });
            }
        }

        return Mono.just(RestUtils.success());
    }

    public Mono<BaseResponse> clearDownloadFile() {
        LocalDateTime beforeDate = LocalDate.now().minusDays(DOWNLOAD_FILE_KEEP).atStartOfDay();
        long sum = downloadJobRwDs.cancelOverDateJob(beforeDate);
        log.info("清理文件数: date = {}, sum = {}", beforeDate, sum);
        return Mono.just(sum)
            .filter(s -> s > 0)
            .flatMap(s -> biDownloadJobFeignClient.clearDownloadFile(DOWNLOAD_FILE_KEEP))
            .switchIfEmpty(Mono.just(RestUtils.success()));
    }

    public Mono<DownloadJobVo> cancelDownloadJob(Long id) {
        return Mono.justOrEmpty(downloadJobRoDs.getById(id))
            .switchIfEmpty(Mono.error(new DcArgumentException("指定ID无效")))
            .filter(jobPo -> DownloadJobStatus.WAIT.equals(jobPo.getStatus()))
            .switchIfEmpty(Mono.error(new DcArgumentException("指定下载任务不允许取消")))
            .doOnNext(jobPo -> {
                jobPo.setStatus(DownloadJobStatus.CANCEL);
                boolean b = downloadJobRwDs.updateDownloadApply(jobPo);
                if (!b) {
                    throw new DcServiceException("取消失败");
                }
            })
            .map(jobPo -> new DownloadJobVo()
                .setId(jobPo.getId())
                .setStatus(jobPo.getStatus())
                .setFileType(jobPo.getFileType())
                .setFilePosition(jobPo.getFilePosition()));
    }

    public Mono<BaseResponse> notifyGenerateResult(GenerateResult result) {
        return Mono.justOrEmpty(downloadJobRwDs.getById(result.getId(), true))
            .switchIfEmpty(Mono.error(new DcArgumentException("指定ID无效")))
            .filter(jobPo -> DownloadJobStatus.PROCESSING.equals(jobPo.getStatus()))
            .switchIfEmpty(Mono.error(new DcArgumentException("下载任务状态不允许当前操作")))
            .doOnNext(jobPo -> {
                jobPo.setStatus(result.getStatus());
                if (DownloadJobStatus.COMPLETED.equals(result.getStatus())) {
                    jobPo.setCompleteTime(new Date());
                }
                boolean b = downloadJobRwDs.updateDownloadApply(jobPo);
                if (!b) {
                    throw new DcServiceException("取消失败");
                }
            })
            .map(x -> RestUtils.success());
    }
}
