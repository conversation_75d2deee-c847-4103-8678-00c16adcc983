package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.param.SortParam;
import com.cdz360.base.model.base.type.ChargeOrderStatus;
import com.cdz360.base.model.base.type.OrderType;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ds.trading.ro.coupon.ds.AbcCouponRoDs;
import com.cdz360.biz.ds.trading.ro.coupon.ds.ActivityRoDs;
import com.cdz360.biz.ds.trading.ro.coupon.ds.CouponRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderPayRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderTimeDivisionRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteGroupRoDs;
import com.cdz360.biz.ds.trading.rw.coupon.ds.CouponRwDs;
import com.cdz360.biz.model.cus.score.dto.ScoreSettingDiscountDto;
import com.cdz360.biz.model.cus.score.po.ScoreSettingLevelPo;
import com.cdz360.biz.model.cus.user.param.BatchAddUserParam;
import com.cdz360.biz.model.trading.coupon.dto.CouponDto;
import com.cdz360.biz.model.trading.coupon.param.ActivityUserCouponParam;
import com.cdz360.biz.model.trading.coupon.param.AppletCouponParam;
import com.cdz360.biz.model.trading.coupon.param.BatchSendCouponParam;
import com.cdz360.biz.model.trading.coupon.param.DictNumParam;
import com.cdz360.biz.model.trading.coupon.param.OptimalCouponSearchParam;
import com.cdz360.biz.model.trading.coupon.po.AbcCouponPo;
import com.cdz360.biz.model.trading.coupon.po.CouponDictPo;
import com.cdz360.biz.model.trading.coupon.po.CouponPo;
import com.cdz360.biz.model.trading.coupon.type.AbcCouponUsed;
import com.cdz360.biz.model.trading.coupon.type.CouponStatusType;
import com.cdz360.biz.model.trading.coupon.type.CouponValidType;
import com.cdz360.biz.model.trading.coupon.type.OrderCouponType;
import com.cdz360.biz.model.trading.coupon.vo.ActivityVo;
import com.cdz360.biz.model.trading.coupon.vo.CouponBi;
import com.cdz360.biz.model.trading.coupon.vo.OrderCouponVo;
import com.cdz360.biz.model.trading.coupon.vo.PriorityCouponVo;
import com.cdz360.biz.model.trading.order.po.ChargerOrderPayPo;
import com.cdz360.biz.model.trading.order.po.ChargerOrderPo;
import com.cdz360.biz.model.trading.order.po.ChargerOrderTimeDivision;
import com.cdz360.biz.model.trading.score.po.ScoreDiscountPo;
import com.cdz360.biz.model.trading.score.type.DiscountType;
import com.cdz360.biz.model.trading.site.po.SiteGroupRefPo;
import com.chargerlinkcar.framework.common.constant.OrderStatus;
import com.chargerlinkcar.framework.common.domain.vo.UserVo;
import com.chargerlinkcar.framework.common.feign.UserFeignClient;
import com.chargerlinkcar.framework.common.utils.DateUtils;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.UUIDUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class CouponService {

    @Autowired
    private AbcCouponRoDs abcCouponRoDs;

    @Autowired
    private CouponRoDs couponRoDs;
    @Autowired
    private CouponRwDs couponRwDs;
    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private ChargerOrderRoDs chargerOrderRoDs;
    @Autowired
    private ChargerOrderPayRoDs chargerOrderPayRoDs;

    @Autowired
    private ChargerOrderTimeDivisionRoDs chargerOrderTimeDivisionRoDs;

    @Autowired
    private CouponDictService couponDictService;
    @Autowired
    private
    ActivityService activityService;

    @Autowired
    private ActivityRoDs activityRoDs;

    @Autowired
    private SiteGroupRoDs siteGroupRoDs;

    public ListResponse<CouponDto> getList(AppletCouponParam param) {

        List<SortParam> list = new ArrayList<>();

        if (param.getStatusList().contains(CouponStatusType.ENABLE)) {
            SortParam sortParam = new SortParam();
            sortParam.setColumns(List.of("a.id")).setOrder(OrderType.desc);
            list.add(sortParam);
        } else if (param.getStatusList().contains(CouponStatusType.USED)) {
            SortParam sortParam = new SortParam();
            sortParam.setColumns(List.of("o.create_time")).setOrder(OrderType.desc);
            list.add(sortParam);
        } else if (param.getStatusList().contains(CouponStatusType.EXPIRE)) {
            SortParam sortParam = new SortParam();
            sortParam.setColumns(List.of("a.validTimeTo")).setOrder(OrderType.desc);
            list.add(sortParam);

            SortParam sortParam2 = new SortParam();
            sortParam2.setColumns(List.of("a.id")).setOrder(OrderType.desc);
            list.add(sortParam2);
        }
        param.setSorts(list);
        return couponRoDs.getList(param.getStart(), param.getSize(), param.getCusId(),
            param.getStatusList(), null, param.getSorts(), null);
    }

    public ObjectResponse<PriorityCouponVo> getPriorityCoupon(long start, int size,
        Long userId, String orderNo, Long scoreSettingId) {
        ObjectResponse<UserVo> jsonObjectRes = userFeignClient.findInfoByUid(userId, null, null);
        FeignResponseValidate.check(jsonObjectRes);
        UserVo vo = jsonObjectRes.getData();

        PriorityCouponVo res = new PriorityCouponVo();
        res.setAutoDeduct(vo.getCouponAutoDeduct());

        ChargerOrderPo order = chargerOrderRoDs.getChargeOrderPo(orderNo, false);
        IotAssert.isNotNull(order, "订单不存在");
        IotAssert.isTrue(NumberUtils.equals(order.getStatus(), OrderStatus.ORDER_STATUS_COMPLETE)
            && ChargeOrderStatus.STOP == order.getOrderStatus(), "订单需处于结束但未支付状态");
        ChargerOrderPayPo pay = chargerOrderPayRoDs.getByOrderno(orderNo);
        IotAssert.isNotNull(pay, "订单支付信息不存在");

        if (NumberUtils.gtZero(pay.getDiscountRefId())) {
            // 商户会员已使用了协议价，不可再选择优惠券列表
            res.setList(new ArrayList<>())
                .setTotal(0L);
            return RestUtils.buildObjectResponse(res);
        }
        if (DecimalUtils.lteZero(pay.getOrderFee())) {
            log.info("订单原金额为0，不可再选择优惠券列表： {}",
                orderNo);
            res.setList(new ArrayList<>())
                .setTotal(0L);
            return RestUtils.buildObjectResponse(res);
        }
        // 支付类型校验
        List<PayAccountType> payAccountTypeList = List.of(
            PayAccountType.PERSONAL,
            PayAccountType.COMMERCIAL);
        if (!payAccountTypeList.contains(pay.getAccountType())) {
            // 除了个人账户和商户会员，其他支付类型在待支付界面都不能选择优惠券
            res.setList(new ArrayList<>())
                .setTotal(0L);
            return RestUtils.buildObjectResponse(res);
        }

        if (scoreSettingId != null && scoreSettingId > 0L && BigDecimal.ZERO.compareTo(
            pay.getScoreAmount()) == 0) {
            // 积分体系id大于0并且订单里的积分优惠金额是0，需要单独计算一次积分体系优惠
            List<SiteGroupRefPo> siteGroupRefList = siteGroupRoDs.getSiteGroupRefList(
                List.of(order.getStationId()));
            if (CollectionUtils.isNotEmpty(siteGroupRefList)) {
                List<String> siteGids = siteGroupRefList.stream()
                    .map(SiteGroupRefPo::getGid)
                    .distinct().collect(Collectors.toList());
                ObjectResponse<ScoreSettingDiscountDto> scoreDiscountRes = userFeignClient.getScoreSettingStrategy(
                        order.getCustomerId(), siteGids, scoreSettingId);
                FeignResponseValidate.check(scoreDiscountRes);
                ScoreSettingDiscountDto scoreSettingDiscountDto = scoreDiscountRes.getData();
                log.info("支付界面用户自选积分体系折扣计算: {}",
                    JsonUtils.toJsonString(scoreSettingDiscountDto));
                ScoreDiscountPo discountPo = new ScoreDiscountPo();
                if (scoreSettingDiscountDto != null
                    && scoreSettingDiscountDto.getScoreSettingDto() != null
                    && scoreSettingDiscountDto.getScoreSettingDto().getCurrentLevel()
                    != null) {
                    ScoreSettingLevelPo currentLevel = scoreSettingDiscountDto.getScoreSettingDto()
                        .getCurrentLevel();
                    discountPo.setOrderNo(orderNo)
                        .setScoreSettingId(
                            scoreSettingDiscountDto.getScoreSettingDto().getId())
                        .setServFeeDiscount(currentLevel.getDiscount())
                        .setDiscountType(DiscountType.valueOf(
                            scoreSettingDiscountDto.getScoreSettingDto().getDiscountType()
                                .name()))
                        .setFixedTotalDiscount(currentLevel.getTotalPrice());
                }
                if (discountPo.getOrderNo() != null) {
                    pay = this.computeOrderDiscount(order, pay, discountPo);
                }
            }
        }

        ListResponse<CouponDto> temp = couponRoDs.getPriorityCoupon(order, pay, start, size,
            userId, scoreSettingId);
        res.setList(temp.getData())
            .setTotal(temp.getTotal());
        return RestUtils.buildObjectResponse(res);
    }

    public int refreshStatus() {
        return couponRwDs.refreshStatus2Enable() +
            couponRwDs.refreshStatus2Expire();
    }

    public Mono<ObjectResponse<OrderCouponVo>> getOrderCoupon(String orderNo) {
        IotAssert.isNotBlank(orderNo, "充电订单编号不能为空");
        AbcCouponPo coupon = abcCouponRoDs.getByOrderNo(orderNo);
        if (null != coupon && (AbcCouponUsed.ORDER_USED_REACHABLE == coupon.getUsed())) {
            return Mono.just(new OrderCouponVo().setCouponId(coupon.getCouponId())
                    .setCouponAmount(coupon.getCouponAmount())
                    .setOrderCouponType(OrderCouponType.ABC)
                    .setUsed(coupon.getUsed()))
                .map(RestUtils::buildObjectResponse);
        }
        return Mono.just(RestUtils.buildObjectResponse(null));
    }

    public BaseResponse batchSendCoupon(BatchSendCouponParam param) {
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getPhoneList()), "手机号不能为空");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getDictNum()), "券信息不能为空");

        // 判断是否超出领券限制
        CouponBi couponBi = activityService.couponBi(
            param.getActivityId()).getData();
        Integer quantityAccount = couponBi.getQuantityAccount();  // 总领券人数
        Integer alreadyAcquireNum = couponBi.getAlreadyAcquireNum();  // 已领券人数

        Integer remainAmount = quantityAccount - alreadyAcquireNum;
//        Integer alreadyAcquireCouponNum = couponBi.getAlreadyAcquireCouponNum(); // 已领券数
//        Integer remainAmount = quantityAccount - alreadyAcquireCouponNum; // 剩余次数
//        Integer amount = param.getDictNum().stream()
//            .map(DictNumParam::getNum)
//            .filter(Objects::nonNull).filter(num -> num > 0)
//            .mapToInt(Integer::intValue).sum(); // 单人领券数
//        int i1 = param.getPhoneList().size() * amount; // 总计领券数
        IotAssert.isTrue(param.getPhoneList().size() <= remainAmount, "超出剩余领券人次");

        // 判断是否可重复领券
        ActivityVo activityVo = activityService.getActivityDetail(param.getActivityId());
        if (CollectionUtils.isEmpty(activityVo.getRuleParamList()) || !activityVo.getRuleParamList()
            .get(0).getRepeatActive()) {
            // 没有规则参数列表表示旧版本的活动，默认不可重复领券。或者配置的是不可重复领券
            // 根据手机号和活动id判断是否领过券（通过活动名称来兼容旧模版的）
            ActivityUserCouponParam activityUserCouponParam = new ActivityUserCouponParam();
            activityUserCouponParam.setActivityId(param.getActivityId());
            activityUserCouponParam.setPhoneList(param.getPhoneList());
            ObjectResponse<Map<String, Boolean>> acquiredCouponMap = activityService.hasUserAcquiredCoupon(
                activityUserCouponParam);
            IotAssert.isTrue(
                acquiredCouponMap.getData().values().stream().noneMatch(value -> value),
                "不可重复发券");

        }
        // 券信息
        List<Long> dictIdList = param.getDictNum().stream()
            .filter(i -> i.getNum() != null && i.getNum() > 0).map(DictNumParam::getDictId)
            .collect(Collectors.toList());
        Map<Long, Integer> dictNumMap = param.getDictNum().stream()
            .filter(i -> i.getNum() != null && i.getNum() > 0)
            .collect(Collectors.toMap(DictNumParam::getDictId, DictNumParam::getNum));

        List<CouponDictPo> dictList = couponDictService.getDictListById(dictIdList);
        Map<Long, CouponDictPo> dictMap = dictList.stream()
            .collect(Collectors.toMap(CouponDictPo::getId, dictPo -> dictPo));

        BatchAddUserParam batchAddUserParam = new BatchAddUserParam();
        batchAddUserParam.setTopCommId(param.getTopCommId())
            .setCommId(param.getCommId())
            .setPhoneList(param.getPhoneList())
            .setSysUid(param.getSysUid())
            .setSysUserName(param.getSysUserName());
        ListResponse<Long> response = userFeignClient.batchAddUser(batchAddUserParam);
        FeignResponseValidate.check(response);
        List<Long> userIdList = response.getData();

        // 组织批量写入
        final String batch = UUIDUtils.getUuid32();
        List<CouponPo> couponList = dictIdList.stream()
            .flatMap(i -> dictMap.containsKey(i) ? Stream.of(dictMap.get(i)) : Stream.empty())
            .map(value -> {
                CouponPo couponPo = new CouponPo();
                BeanUtils.copyProperties(value, couponPo);
                couponPo.setCouponDictId(value.getId())
                    .setActivityId(param.getActivityId())
                    .setTopCommId(param.getTopCommId())
                    .setStatus(CouponStatusType.ENABLE)
                    .setOpUid(param.getSysUid())
                    .setOpName(param.getSysUserName())
                    .setBatch(batch);

                // 开始结束日期
                LocalDate now = LocalDate.now();
                if (couponPo.getValidType() == CouponValidType.FIX) {
                    couponPo.setValidTimeFrom(value.getValidTimeFrom())
                        .setValidTimeTo(value.getValidTimeTo());

                    LocalDate timeFrom = value.getValidTimeFrom().toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDate();
                    if (now.isEqual(timeFrom) || now.isAfter(timeFrom)) {
                        couponPo.setStatus(CouponStatusType.ENABLE);
                    } else {
                        couponPo.setStatus(CouponStatusType.DISABLE);
                    }

                } else if (couponPo.getValidType() == CouponValidType.RELATE) {
                    couponPo.setStatus(CouponStatusType.ENABLE)
                        .setValidTimeFrom(DateUtils.toDate(now))
                        .setValidTimeTo(
                            DateUtils.toDate(now.plusDays(couponPo.getValidRelateDay())));
                }

                return couponPo;
            })
            .flatMap(couponPo -> userIdList.stream()
                .map(userId -> {
                    CouponPo newCouponPo = new CouponPo();
                    BeanUtils.copyProperties(couponPo, newCouponPo);
                    newCouponPo.setUserId(userId);
                    return newCouponPo;
                }))
            .flatMap(couponPo -> {
                Integer count = dictNumMap.get(couponPo.getCouponDictId());
                return Stream.generate(() -> couponPo).limit(count);
            })
            .collect(Collectors.toList());
        couponRwDs.insertCoupon(couponList);
        return RestUtils.success();
    }

    public ListResponse<CouponDto> getOptimalCouponList(OptimalCouponSearchParam param) {
        IotAssert.isNotNull(param.getSiteId(), "场站id不能为空");
        IotAssert.isNotNull(param.getSiteId(), "场站id不能为空");
        if (param.getStart() == null) {
            param.setStart(0L);
        }
        if (param.getSize() == null) {
            param.setSize(10);
        }
        return couponRoDs.getOptimalCouponListWithoutOrder(param.getUserId(),
            param.getScoreSettingId(), param.getSiteId(), param.getPrepayEnable(),
            param.getWxCreditEnable(), param.getStart().intValue(),
            param.getSize());
    }


    /**
     * 待支付界面使用，计算积分体系折扣
     *
     * @param order
     * @param pay
     * @param scoreDiscountPo
     * @return
     */
    private ChargerOrderPayPo computeOrderDiscount(ChargerOrderPo order, ChargerOrderPayPo pay,
        ScoreDiscountPo scoreDiscountPo) {
        if (DiscountType.SERV_FEE.equals(scoreDiscountPo.getDiscountType())
            && DecimalUtils.gte(scoreDiscountPo.getServFeeDiscount(), DecimalUtils.ZERO)) {
            BigDecimal newServFee = pay.getServFee()
                .multiply(scoreDiscountPo.getServFeeDiscount().movePointLeft(2))
                .setScale(2, RoundingMode.HALF_UP);

            BigDecimal servFeeDiscount = pay.getServFee().subtract(newServFee);
            log.info("待支付界面服务费折扣后，计算出来的费用为: {} -> {}", pay.getServFee(),
                newServFee);

            BigDecimal newOrderFee = pay.getOrderOriginFee().subtract(servFeeDiscount);

            log.info("待支付界面服务费折扣后，订单金额变更: {} -> {}",
                pay.getOrderFee(), newOrderFee);

            pay.setScoreAmount(servFeeDiscount);

            pay.setServFee(newServFee);

            pay.setOrderFee(newOrderFee);
        } else if (DiscountType.FIXED_TOTAL_FEE.equals(scoreDiscountPo.getDiscountType())) {
            List<String> orderNoList = new ArrayList<>();
            orderNoList.add(order.getOrderNo());
            List<ChargerOrderTimeDivision> timeDivisions = chargerOrderTimeDivisionRoDs.selectListByOrderNoList(
                orderNoList);
            timeDivisions.forEach(division -> {
                if (division.getTemplateId() == null) {
                    log.error(
                        "该充电订单没有计费模板, 不做调整: orderNo = {}, division = {}",
                        timeDivisions.get(0).getOrderNo(),
                        JsonUtils.toJsonString(division));
                } else {
                    if (division.getServiceUnit() == null) {
                        division.setServiceUnit(
                            division.getServicePrice()
                                .divide(division.getElectric(), 4, RoundingMode.HALF_UP));
                    }

                    if (division.getElectricUnit() == null) {
                        division.setElectricUnit(
                            division.getElectricPrice()
                                .divide(division.getElectric(), 4, RoundingMode.HALF_UP));
                    }

                    if (DecimalUtils.gt(division.getElectricUnit(),
                        scoreDiscountPo.getFixedTotalDiscount())) {
                        // 电费单价大于折扣价
                        // 电费单价 = 折扣价，服务费单价 = 0
                        division.setElectricUnit(scoreDiscountPo.getFixedTotalDiscount())
                            .setServiceUnit(BigDecimal.ZERO);

                        // 重新计算电费
                        division.setElectricPrice(
                            division.getElectric()
                                .multiply(division.getElectricUnit())
                                .setScale(2, RoundingMode.HALF_UP));
                    } else {
                        // 服务费单价 = 折扣价-电费单价， 电费单价不变
                        division.setServiceUnit(scoreDiscountPo.getFixedTotalDiscount()
                            .subtract(division.getElectricUnit()));
                    }

                    // 服务费计算
                    division.setServicePrice(
                        division.getElectric()
                            .multiply(division.getServiceUnit())
                            .setScale(2, RoundingMode.HALF_UP));

                    // 电费计算
                    division.setElectricPrice(
                        division.getElectric()
                            .multiply(division.getElectricUnit())
                            .setScale(2, RoundingMode.HALF_UP));

                    // 分段费用
                    division.setOrderPrice(
                        division.getElectricPrice().add(division.getServicePrice()));
                }
            });
            BigDecimal newServFee = timeDivisions.stream()
                .map(ChargerOrderTimeDivision::getServicePrice)
                .reduce(DecimalUtils::add)
                .orElse(BigDecimal.ZERO);
            log.info("待支付界面阶梯单价服务费折扣后，计算出来的费用为: {} -> {}", pay.getServFee(),
                newServFee);

            BigDecimal newElecFee = timeDivisions.stream()
                .map(ChargerOrderTimeDivision::getElectricPrice)
                .reduce(DecimalUtils::add)
                .orElse(BigDecimal.ZERO);
            log.info("待支付界面阶梯单价电费折扣后，计算出来的费用为: {} -> {}", pay.getElecFee(),
                newElecFee);

            BigDecimal scoreAmount = pay.getServFee().subtract(newServFee).add(
                pay.getElecFee().subtract(newElecFee)
            );
            log.info("待支付界面阶梯单价积分折扣金额: {}", scoreAmount);
            pay.setScoreAmount(scoreAmount);

            pay.setServFee(newServFee);

            pay.setElecFee(newElecFee);

            final BigDecimal newOrderFee = pay.getServFee().add(pay.getElecFee());

            log.info("待支付界面阶梯单价折扣后，计算出来的费用为: {} -> {}", pay.getOrderFee(),
                newOrderFee);

            pay.setOrderFee(newOrderFee);
        }
        return pay;
    }
}
