package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.client.DeviceMonitorFeignClient;
import com.cdz360.biz.dc.client.reactor.ReactorDeviceMonitorFeignClient;
import com.cdz360.biz.dc.domain.OrderInMongo;
import com.cdz360.biz.dc.domain.WWarningRecordInMongo;
import com.cdz360.biz.dc.repository.ChargerOrderRepository;
import com.cdz360.biz.ds.trading.ro.iot.ds.BsChargerRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderTimeDivisionRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ds.trading.rw.order.ds.BiPlugRwDs;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderRwDs;
import com.cdz360.biz.model.iot.param.ListCtrlParam;
import com.cdz360.biz.model.iot.vo.GwInfoVo;
import com.cdz360.biz.model.trading.iot.po.BsChargerPo;
import com.cdz360.biz.model.trading.order.po.ChargerOrderTimeDivisionEx;
import com.cdz360.biz.model.trading.site.po.BiPlugPo;
import com.cdz360.biz.model.trading.site.type.DeviceStatusCodeType;
import com.cdz360.biz.utils.feign.iot.DeviceFeignClient;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.domain.vo.BiSiteDateRangeVo;
import com.chargerlinkcar.framework.common.domain.vo.BiSiteDateVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerDetail;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrder;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.PlugNoUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * BiPlugService
 *
 * @since 3/28/2020 11:12 AM
 * <AUTHOR>
 */
@Slf4j
@Service
public class BiPlugService {


    private static final int PAGE_SIZE = 100;

    @Autowired
    private BiPlugRwDs biPlugRwDs;
    @Autowired
    private BsChargerRoDs bsChargerRoDs;

    @Autowired
    private ChargerOrderRwDs chargerOrderDs;

    @Autowired
    private ChargerOrderTimeDivisionRoDs chargerOrderTimeDivisionRoDs;

    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;

//    @Autowired
//    private ReactorIotPvFeignClient iotFeignClient;

    @Autowired
    private DeviceFeignClient deviceFeignClient;

    @Autowired
    private ReactorDeviceMonitorFeignClient reactorDeviceMonitorFeignClient;

    @Autowired
    private SiteRoDs siteDs;

    @Autowired
    private RedisIotReadService redisIotReadService;

    private static final Integer TAG_1 = 1;// 尖
    private static final Integer TAG_2 = 2;// 峰
    private static final Integer TAG_3 = 3;// 平
    private static final Integer TAG_4 = 4;// 谷

    private static final int ONE_DAY_SECOND = 24 * 3600;

    @Autowired
    private DeviceMonitorFeignClient deviceMonitorFeignClient;

    @Autowired
    private ChargerOrderRepository chargerOrderRepository;
//    private WWarningRecordRepositoryService wWarningRecordRepositoryService;

    private static final long OFFLINE_WARN_GAP = 60 * 1000;//离线间隙（毫秒），离线时间小于此时间，离线计数将+1

    // 处理线程池
    // 暂时使用 100 个线程
    private static final Executor executor = Executors.newFixedThreadPool(2, r -> {
        Thread t = new Thread(r);
        t.setDaemon(Boolean.TRUE);
        return t;
    });

    public void dailyBi(Date date, String siteId) {

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        calendar.clear(Calendar.MILLISECOND);
        calendar.clear(Calendar.SECOND);
        calendar.clear(Calendar.MINUTE);

        final Date startTime = calendar.getTime();
        calendar.add(Calendar.DATE, 1);
//        final Date endTime = calendar.getTime();

//        final Long startTimestamp = startTime.getTime() / 1000;
//        final Long endTimestamp = endTime.getTime() / 1000;

        final Date KeyTime = startTime;

        // 所有场站
        List<String> allSiteIdList;
        if (StringUtils.isBlank(siteId)) {
            allSiteIdList = siteDs.getSiteIdList(0L, 9999, null);
        } else {
            log.info("仅统计该场站: {}", siteId);
            allSiteIdList = List.of(siteId);
        }
        log.info("site size = {}", allSiteIdList.size());

        // 单个场站处理
        List<CompletableFuture<Integer>> collect = allSiteIdList.stream().map(
            site -> CompletableFuture.supplyAsync(
                () -> this.plugDailyBi(site, KeyTime),
                executor
            )).collect(Collectors.toList());

        List<Integer> result = collect.stream().map(CompletableFuture::join)
            .collect(Collectors.toList());
        log.info("枪头统计完成: date = {}, result = {}", date,
            result.stream().mapToInt(Integer::intValue).sum());

    }

    /**
     * FIXME 充电开始结束时间如果从过去某一个不重合的日期修改成当前日期，
     * 过去的日期不会被重新计算，这样会导致过去日期不准确，
     * 目前无法知道过去日期是什么，所以无法统计
     *
     * @param date
     */
    public void refreshPlugBiByOrderDaily(Date date, String siteId) {
        log.info("开始查找当前天更新的订单，并刷新桩枪统计: {}", date);
        Date startTime = DateUtil.getThisDate(date);
        Date endTime = DateUtil.getNextDate(date);

        ChargerOrderParam chargerOrderParam = new ChargerOrderParam();
        chargerOrderParam.setUpdateTimeStart(
            startTime.toInstant().atOffset(ZoneOffset.of("+8")).toLocalDateTime());
        chargerOrderParam.setUpdateTimeEnd(
            endTime.toInstant().atOffset(ZoneOffset.of("+8")).toLocalDateTime());

        if (StringUtils.isNotBlank(siteId)) {
            log.info("仅统计场站: {}", siteId);
            chargerOrderParam.setStationIds(List.of(siteId));
        } else {
            final List<String> biSiteIdGroupVoList = chargerOrderDs.getBiSiteIdGroupVoList(
                chargerOrderParam);
            if (CollectionUtils.isEmpty(biSiteIdGroupVoList)) {
                log.info("计算场站所有枪头统计完成,没有需要计算的场站");
                return;
            }
            chargerOrderParam.setStationIds(biSiteIdGroupVoList);
        }

        int currentSite = 1;
        for (String subSiteId : chargerOrderParam.getStationIds()) {

            chargerOrderParam.setStationId(subSiteId);

            // 获取充电时间不超过1天的开始和结束时间集合
            final List<BiSiteDateVo> biSiteDateGroupVoList = chargerOrderDs.getBiSiteDateGroupVoList(
                chargerOrderParam);
            final Set<Date> value = biSiteDateGroupVoList
                .stream()
                .map(BiSiteDateVo::getGroupDate)
                .collect(Collectors.toSet());

            final Set<Date> siteRefreshDate = getSiteRefreshDate(chargerOrderParam);
            value.addAll(siteRefreshDate);

            log.info("siteId: {}, Date count: {}", subSiteId, value.size());

            if (CollectionUtils.isNotEmpty(value)) {
                this.plugDailyBiRemedy(Map.of(subSiteId, value));
            } else {
                log.info("场站无可刷新的日期: {}", subSiteId);
            }
            log.info("计算场站所有枪头统计: {}/{}",
                currentSite++, chargerOrderParam.getStationIds().size());
        }

        log.info("计算场站所有枪头统计完成");

        // 使用最小-最大活动时间
//        final List<BiSiteDateRangeVo> biSiteDateRangeVoList = chargerOrderDs.getBiSiteDateRangeVoList(
//            chargerOrderParam);
//        if (CollectionUtils.isNotEmpty(biSiteDateRangeVoList)) {
//            for(BiSiteDateRangeVo one : biSiteDateRangeVoList) {
//                if(one == null) {
//                    log.warn("获取场站订单时段失败");
//                    continue;
//                }
//
//                final String subSiteId = one.getSiteId();
//
//                if(StringUtils.isBlank(subSiteId)) {
//                    log.warn("场站id为空: {}", one);
//                    continue;
//                }
//
//                if(one.getEndDate() == null || one.getStartDate() == null) {
//                    log.warn("场站时段信息为空: {}， {}", one.getStartDate(), one.getEndDate());
//                    continue;
//                }
//
//                if(one.getStartDate().getTime() > one.getEndDate().getTime()) {
//                    log.warn("场站时段信息不正确: {}， {}", one.getStartDate(), one.getEndDate());
//                    continue;
//                }
//
//                Date startDate = DateUtil.getThisDate(one.getStartDate());
//                final Date endDate = one.getEndDate();
//                while(startDate.getTime() <= endDate.getTime()) {
//
//                    this.plugDailyBiRemedy(Map.of(subSiteId, Set.of(startDate)));
//
//                    startDate = DateUtil.addDate(startDate, 1);
//                }
//                log.info("计算场站枪头统计完成siteId: {} , {}, {}",
//                    subSiteId, DateUtil.getThisDate(one.getStartDate()), endDate);
//            }
//            log.info("计算场站所有枪头统计完成");
//        } else {
//            log.info("计算场站所有枪头统计完成，无任何统计执行");
//        }

//        List<ChargerOrder> orderList = chargerOrderDs.getBiChargerOrderDetailNonDivision(chargerOrderParam);
//
//        if (CollectionUtils.isNotEmpty(orderList)) {
//            Map<String, List<ChargerOrder>> siteOrderMap = orderList.stream()
//                    .collect(Collectors.groupingBy(ChargerOrder::getStationId, Collectors.toList()));
//
//            //<siteId, <refresh date>>
//            Map<String, Set<Date>> remedyDateMap = new HashMap<>();
//
//            siteOrderMap.keySet().stream().forEach(key -> {
//                Set<Date> setDate = new HashSet<>();
//                siteOrderMap.get(key).stream().forEach(order -> {
//                    if (order.getChargeStartTime() != null) {
//                        setDate.add(DateUtil.getThisDate(new Date(Long.valueOf(order.getChargeStartTime()) * 1000)));
//                    }
//                    if (order.getChargeEndTime() != null) {
//                        setDate.add(DateUtil.getThisDate(new Date(Long.valueOf(order.getChargeEndTime()) * 1000)));
//                    }
//
//                    if (order.getChargeStartTime() != null &&
//                            order.getChargeEndTime() != null) {
//                        List<Date> betweenDates = DateUtil.getDatesBetween(new Date(order.getChargeStartTime()),
//                                new Date(order.getChargeEndTime()));
//
//                        if (CollectionUtils.isNotEmpty(betweenDates)) {
//                            log.info("这个订单跨越了1整天，需要将日期: {} 涵盖入内", betweenDates);
//                            betweenDates.stream().forEach(dia -> setDate.add(dia));
//                        }
//                    }
//
//                });
//                remedyDateMap.put(key, setDate);
//            });
//
//            this.plugDailyBiRemedy(remedyDateMap);
//        }

    }

    /**
     * 分页查询获取跨2天以上订单，展开成为影响日期的列表，添加到集合中
     *
     * @param params
     * @return
     */
    private Set<Date> getSiteRefreshDate(ChargerOrderParam params) {

        final int maxSize = 100;

        params.setStart(0L);
        params.setSize(maxSize);

        List<BiSiteDateRangeVo> orderListOnce;

        Set<Date> ret = new HashSet<>();

        int count = 0;

        do {
            orderListOnce = chargerOrderDs.getSiteRefreshLongDate(params);
            ret.addAll(extractDateRange(orderListOnce));
            count += orderListOnce.size();
        } while (orderListOnce.size() == maxSize);

        log.info("getSiteRefreshDate site count: {}, ", count);

        return ret;
    }

    private Set<Date> extractDateRange(List<BiSiteDateRangeVo> list) {
        Set<Date> ret = new HashSet<>();
        for (BiSiteDateRangeVo one : list) {
            final Set<Date> setByDateRange = getSetByDateRange(one.getStartDate(),
                one.getEndDate());
            ret.addAll(setByDateRange);
        }
        return ret;
    }

    private Set<Date> getSetByDateRange(Date s, Date e) {
        if (s == null || e == null || s.getTime() > e.getTime()) {
            return Set.of();
        }
        Set<Date> ret = new HashSet<>();

        Date curDate;
        do {
            curDate = DateUtil.getThisDate(s);
            ret.add(curDate);
            curDate = DateUtil.addDate(curDate, 1);
        } while (curDate.getTime() < e.getTime());

        return ret;
    }

    /**
     * 按场站，更新日期集合统计
     *
     * @param remedyMap <场站id, 需要更新的日期集合>
     */
    private void plugDailyBiRemedy(Map<String, Set<Date>> remedyMap) {
        log.debug("桩枪订单修正d: {}", JsonUtils.toJsonString(remedyMap));
        log.info("桩枪订单修正i: {}", remedyMap.size());
        if (!remedyMap.isEmpty()) {
            List<String> siteIds = remedyMap.keySet()
                .stream()
                .filter(key -> CollectionUtils.isNotEmpty(remedyMap.get(key)))
                .collect(Collectors.toList());

            // 多场站one by one
            int siteDateSum = siteIds.stream().map(siteId -> {
                List<CompletableFuture<Integer>> collect =
                    remedyMap.get(siteId)
                        .stream()
                        .map(date -> CompletableFuture.supplyAsync(
                            () -> this.plugDailyBi(siteId,
                                date), executor))
                        .collect(Collectors.toList());

                // 单场站中并发计算
                List<Integer> result = collect.stream().map(CompletableFuture::join)
                    .collect(Collectors.toList());

                return result.stream().mapToInt(Integer::intValue).sum();
            }).mapToInt(Integer::intValue).sum();

            log.info("桩枪订单修正完成: result = {}", siteDateSum);
        }
    }

    /**
     * 每天枪头统计
     */
    public Integer plugDailyBi(String siteId, Date date) {
        if (true) {
            return this.plugDailyBiByDivision(siteId, date);
        }
        /***以下内容不再执行**/

//        log.info("枪数据统计开始: {}， {}", siteId, date);
//        Calendar calendar = Calendar.getInstance();
//        calendar.setTime(date);
//
//        calendar.clear(Calendar.MILLISECOND);
//        calendar.clear(Calendar.SECOND);
//        calendar.clear(Calendar.MINUTE);
//
//        final Date startTime = calendar.getTime();
//        calendar.add(Calendar.DATE, 1);
//        final Date endTime = calendar.getTime();
//
//        final Long startTimestamp = startTime.getTime() / 1000;
//        final Long endTimestamp = endTime.getTime() / 1000;
//
//        final Date KeyTime = startTime;
//
//        List<BsChargerPo> hlhtPlugs = bsChargerRoDs.getHlhtChargerInfo(siteId);
//
////        List<String> allSiteIdList = siteDs.getAllSiteIdList();
//
////        allSiteIdList.forEach(siteId -> {
//        Set<String> allPlugs = redisIotReadService.getSitePlugs(siteId);
////            Set<String> allEvses = redisIotReadService.getSiteEvses(siteId);
//        if (CollectionUtils.isNotEmpty(hlhtPlugs)) {
//            // 若存在互联枪头信息，则操作如下
//            hlhtPlugs.forEach(plug -> {
//                BiPlugPo biPlugPo = new BiPlugPo();
//                biPlugPo.setSiteId(siteId);
//                biPlugPo.setEvseNo(plug.getEvseNo());
//                biPlugPo.setPlugId(plug.getConnectorId());
//                biPlugPo.setDate(KeyTime);
//                biPlugPo.setElectricity(BigDecimal.ZERO);
//
//                biPlugPo.setErrorCount(0);
//                biPlugPo.setOfflineCount(0);
//                biPlugRwDs.insertOrUpdate(biPlugPo);
//            });
//
//        } else if (CollectionUtils.isNotEmpty(allPlugs)) {
//            // 一次获取时间范围内该场站所有告警信息，根据redis-plug key做分组列表
//            ListResponse<WWarningRecordInMongo> warnRecordResponse =
//                deviceMonitorFeignClient.evseErrorAlarm(startTime, endTime, null, "", siteId);
//
//            Map<String, List<WWarningRecordInMongo>> warnRecordMap = new HashMap<>();
//            if (warnRecordResponse.getStatus() == ResultConstant.RES_SUCCESS_CODE &&
//                CollectionUtils.isNotEmpty(warnRecordResponse.getData())) {
//                warnRecordMap = warnRecordResponse.getData()
//                    .stream()
//                    .collect(Collectors.groupingBy(e ->
//                            PlugNoUtils.formatPlugNo(e.getBoxOutFactoryCode(), e.getConnectorId()),
//                        Collectors.toList()));
//            }
//
//            final Map<String, List<WWarningRecordInMongo>> warnMap = warnRecordMap;
//
//            allPlugs.forEach(plugNo -> {
//                String evseNo = plugNo.substring(0, plugNo.length() - 2);
//                Integer plugId = Integer.valueOf(plugNo.substring(plugNo.length() - 2));
//
//                Long offlineCount = 0L;
//                Long errorCount = 0L;
//                if (plugId == 1) {
//                    // 1号枪获取本时段内桩枪告警
////                        ListResponse<WWarningRecordInMongo> warnRecordRes =
////                                deviceMonitorFeignClient.evseErrorAlarm(startTime, endTime, null, evseNo, siteId);
//
//                    if (CollectionUtils.isNotEmpty(warnMap.get(plugNo))) {
//                        List<WWarningRecordInMongo> warnRecords = warnMap.get(plugNo);
//                        errorCount =
//                            warnRecords.stream().filter(record ->
//                                record.getWarningCode() != null &&
//                                    !record.getWarningCode().equalsIgnoreCase(
//                                        String.valueOf(DeviceStatusCodeType.C1101.getCode()))
//                            ).count();
//
//                        List<Long> offlineTimestamps =
//                            warnRecords.stream()
//                                .filter(record -> record.getWarningCode() != null &&
//                                    record.getWarningCode().equalsIgnoreCase(
//                                        String.valueOf(DeviceStatusCodeType.C1101.getCode())))
//                                .map(record -> record.getStartTime().getTime())
//                                .sorted()
//                                .collect(Collectors.toList());
//
//                        Long baseTime = 0L;
//                        if (CollectionUtils.isNotEmpty(offlineTimestamps)) {
//                            for (Long triggerTimestamp : offlineTimestamps) {
//                                if (baseTime == 0L) {
//                                    baseTime = triggerTimestamp;
//                                    offlineCount++;
//                                    continue;
//                                }
//                                if (triggerTimestamp - baseTime > OFFLINE_WARN_GAP) {
//                                    offlineCount++;
//                                }
//                                baseTime = triggerTimestamp;
//                            }
//                        }
//
//                    }
//                }
//
//                BiPlugPo biPlugPo = new BiPlugPo();
//                biPlugPo.setSiteId(siteId);
//                biPlugPo.setEvseNo(evseNo);
//                biPlugPo.setPlugId(plugId);
//                biPlugPo.setDate(KeyTime);
//                biPlugPo.setElectricity(BigDecimal.ZERO);
//
//                biPlugPo.setErrorCount(errorCount.intValue());
//                biPlugPo.setOfflineCount(offlineCount.intValue());
//                biPlugRwDs.insertOrUpdate(biPlugPo);
//            });
//        } else {
//            log.warn("场站{}, redis无枪头", siteId);
//        }
//
////        });
//
//        List<ChargerOrder> siteOrderList = chargerOrderDs.listOrderInDateRange(siteId,
//            startTimestamp, endTimestamp);
//        log.info("{} 场站: {} 订单数据1: {}, ", date, siteId,
//            siteOrderList == null ? null : siteOrderList.size());
//
//        List<ChargerOrder> siteOrderStopList = chargerOrderDs.listOrderInStopTime(siteId,
//            startTimestamp, endTimestamp);
//        log.info("{} 场站: {} 订单数据2: {}, ", date, siteId,
//            siteOrderStopList == null ? null : siteOrderStopList.size());
//
//        Map<String, List<ChargerOrder>> plugOrderMap =
//            siteOrderList.stream()
//                .collect(Collectors.groupingBy(
//                    e -> e.getStationId() + "#" + e.getBoxCode() + "#" + e.getConnectorId()));
//
//        Map<String, List<ChargerOrder>> plugOrderStopMap =
//            siteOrderStopList.stream()
//                .collect(Collectors.groupingBy(
//                    e -> e.getStationId() + "#" + e.getBoxCode() + "#" + e.getConnectorId()));
//
//        // 将没有订单的枪头置入枪头订单map，并为其创建一个空的订单列表作为value
//        if (CollectionUtils.isNotEmpty(allPlugs)) {
//            allPlugs.stream().map(plugNo -> {
//                String evseNo = plugNo.substring(0, plugNo.length() - 2);
//                Integer plugId = Integer.valueOf(plugNo.substring(plugNo.length() - 2));
//                String plugKey = siteId + "#" + evseNo + "#" + plugId;
//                return plugKey;
//            }).forEach(plugKey -> {
//                if (!plugOrderMap.keySet().contains(plugKey)) {
//                    plugOrderMap.put(plugKey, new ArrayList<>());
//                }
//            });
//        }
//
        int ret = 0;
//        for (String siteEvsePlug : plugOrderMap.keySet()) {
//            String[] keys = siteEvsePlug.split("#");
////            log.info("siteEvsePlug keys: {}", keys);
//            if (keys == null || keys.length != 3) {
//                log.warn("siteEvsePlug skip");
//                continue;
//            }
//            String plugSiteId = keys[0];
//            String evseId = keys[1];
//            Integer plugId = Integer.valueOf(keys[2]);
//
//            BiPlugPo biPlugPo = new BiPlugPo();
//            biPlugPo.setSiteId(plugSiteId);
//            biPlugPo.setEvseNo(evseId);
//            biPlugPo.setPlugId(plugId);
//            biPlugPo.setDate(KeyTime);
//
//            List<ChargerOrder> subOrderList = plugOrderMap.get(siteEvsePlug);
//
//            // 计算充电时长
//            if (CollectionUtils.isNotEmpty(subOrderList)) {
//                biPlugPo.setOrderCount(subOrderList.size());
//
//                Long duration =
//                    subOrderList.stream().mapToLong(e -> {
//                        Long chargeStartTime = e.getChargeStartTime();
//                        Long chargeEndTime = e.getChargeEndTime();
//
//                        long startMin = Math.max(startTimestamp, chargeStartTime);
//                        long endMin = Math.min(endTimestamp, chargeEndTime);
//                        IotAssert.isTrue(startMin <= endMin,
//                            "时段内开始结束时间不正确, orderNo: " + e.getOrderNo());
//                        return endMin - startMin;
//                    }).sum();
//
//                biPlugPo.setDuration(duration.intValue());
//            } else {
//                biPlugPo.setDuration(0);
//            }
//
//            // 计算结束时间在范围内的订单的充电电量
//            List<ChargerOrder> subOrderStopList = plugOrderStopMap.get(siteEvsePlug);
//            if (CollectionUtils.isNotEmpty(subOrderStopList)) {
//                biPlugPo.setElectricity(this.sumList(subOrderStopList.stream()
//                    .map(ChargerOrder::getOrderElectricity)
//                    .collect(Collectors.toList())));
//            } else {
//                biPlugPo.setElectricity(BigDecimal.ZERO);
//            }
//
////            ObjectResponse<Long> availableTime =
////                    iotDeviceMgmFeignClient.getEvseListActiveTime(startTime, endTime, evseId, plugSiteId);
//
////            if (availableTime != null &&
////                    availableTime.getStatus() == ResultConstant.RES_SUCCESS_CODE &&
////                    availableTime.getData() != null) {
////                biPlugPo.setAvailable(availableTime.getData().intValue());
////            } else {
////                log.warn("获取桩有效时间失败: {}, 使用最大时间填充", JsonUtils.toJsonString(availableTime));
////                biPlugPo.setAvailable(endTimestamp.intValue() - startTimestamp.intValue());
////            }
//            biPlugPo.setAvailable(DateUtil.DAY_SECOND);
//
//            biPlugRwDs.insertOrUpdate(biPlugPo);
//            ret++;
//        }
        return ret;
    }

    public Integer plugDailyBiByDivision(String siteId, Date date) {
        log.info("枪数据统计开始: {}， {}", siteId, date);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        calendar.clear(Calendar.MILLISECOND);
        calendar.clear(Calendar.SECOND);
        calendar.clear(Calendar.MINUTE);

        final Date startTime = calendar.getTime();
        calendar.add(Calendar.DATE, 1);
        final Date endTime = calendar.getTime();

        final Date KeyTime = startTime;

        List<BsChargerPo> hlhtPlugs = bsChargerRoDs.getHlhtChargerInfo(siteId);

//        List<String> allSiteIdList = siteDs.getAllSiteIdList();

//        allSiteIdList.forEach(siteId -> {
        Set<String> allPlugs = redisIotReadService.getSitePlugs(siteId);
//            Set<String> allEvses = redisIotReadService.getSiteEvses(siteId);
        if (CollectionUtils.isNotEmpty(hlhtPlugs)) {
            // 若存在互联枪头信息，则操作如下
            hlhtPlugs.stream().filter(Objects::nonNull).forEach(plug -> {
                BiPlugPo biPlugPo = new BiPlugPo();
                biPlugPo.setSiteId(siteId);
                biPlugPo.setEvseNo(plug.getEvseNo());
                biPlugPo.setPlugId(plug.getConnectorId() == null ? 0 : plug.getConnectorId());  // 枪头序号没有时，默认填0
                biPlugPo.setDate(KeyTime);
                biPlugPo.setElectricity(BigDecimal.ZERO);

                biPlugPo.setAvailable(DateUtil.DAY_SECOND);

                biPlugPo.setErrorCount(0);
                biPlugPo.setOfflineCount(0);
                biPlugRwDs.insertOrUpdate(biPlugPo);
            });

        } else if (CollectionUtils.isNotEmpty(allPlugs)) {
            // 一次获取时间范围内该场站所有告警信息，根据redis-plug key做分组列表
            ListResponse<WWarningRecordInMongo> warnRecordResponse =
                deviceMonitorFeignClient.evseErrorAlarm(startTime, endTime, null, "", siteId);

            Map<String, List<WWarningRecordInMongo>> warnRecordMap = new HashMap<>();
            if (warnRecordResponse.getStatus() == ResultConstant.RES_SUCCESS_CODE &&
                CollectionUtils.isNotEmpty(warnRecordResponse.getData())) {
                warnRecordMap = warnRecordResponse.getData()
                    .stream()
                    .collect(Collectors.groupingBy(e ->
                            PlugNoUtils.formatPlugNo(e.getBoxOutFactoryCode(), e.getConnectorId()),
                        Collectors.toList()));
            }

            final Map<String, List<WWarningRecordInMongo>> warnMap = warnRecordMap;

            allPlugs.forEach(plugNo -> {
                String evseNo = plugNo.substring(0, plugNo.length() - 2);
                Integer plugId = Integer.valueOf(plugNo.substring(plugNo.length() - 2));

                Long offlineCount = 0L;
                Long errorCount = 0L;
//                if (plugId == 1) {
                // 1号枪获取本时段内桩枪告警
//                        ListResponse<WWarningRecordInMongo> warnRecordRes =
//                                deviceMonitorFeignClient.evseErrorAlarm(startTime, endTime, null, evseNo, siteId);

                if (CollectionUtils.isNotEmpty(warnMap.get(plugNo))) {
                    List<WWarningRecordInMongo> warnRecords = warnMap.get(plugNo);
                    errorCount =
                        warnRecords.stream().filter(record ->
                            record.getWarningCode() != null &&
                                !record.getWarningCode().equalsIgnoreCase(
                                    String.valueOf(DeviceStatusCodeType.C1101.getCode()))
                        ).count();

                    if (plugId == 1) {
                        List<Long> offlineTimestamps =
                            warnRecords.stream()
                                .filter(record -> record.getWarningCode() != null &&
                                    record.getWarningCode().equalsIgnoreCase(
                                        String.valueOf(DeviceStatusCodeType.C1101.getCode())))
                                .map(record -> record.getStartTime().getTime())
                                .sorted()
                                .collect(Collectors.toList());
                        Long baseTime = 0L;
                        if (CollectionUtils.isNotEmpty(offlineTimestamps)) {
                            for (Long triggerTimestamp : offlineTimestamps) {
                                if (baseTime == 0L) {
                                    baseTime = triggerTimestamp;
                                    offlineCount++;
                                    continue;
                                }
                                if (triggerTimestamp - baseTime > OFFLINE_WARN_GAP) {
                                    offlineCount++;
                                }
                                baseTime = triggerTimestamp;
                            }
                        }
                    }

                }
//                }

                BiPlugPo biPlugPo = new BiPlugPo();
                biPlugPo.setSiteId(siteId);
                biPlugPo.setEvseNo(evseNo);
                biPlugPo.setPlugId(plugId);
                biPlugPo.setDate(KeyTime);
                biPlugPo.setElectricity(BigDecimal.ZERO);

                biPlugPo.setErrorCount(errorCount.intValue());
                biPlugPo.setOfflineCount(offlineCount.intValue());
                biPlugRwDs.insertOrUpdate(biPlugPo);
            });
        } else {
            log.warn("场站{}, redis无枪头", siteId);
        }

        /**
         * 逆变器，储能ESS挂在微网控制器下面
         * 逆变器，光储ESS告警
         */
        Mono.just(siteId)
            .flatMap(e -> {
                ListCtrlParam param = new ListCtrlParam();
                param.setSiteId(e)
                    .setSize(999);
                return deviceFeignClient.findCtrlList(param);
            })
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .map(res -> res.stream().map(GwInfoVo::getGwno).collect(Collectors.toList()))
            .filter(CollectionUtils::isNotEmpty)
            .zipWith(Mono.just(
                    deviceMonitorFeignClient.evseErrorAlarm(startTime, endTime, null, "", siteId))
                .doOnNext(FeignResponseValidate::check))
            .doOnNext(tuple -> {
                List<String> list = tuple.getT1();
                ListResponse<WWarningRecordInMongo> recordResponse = tuple.getT2();

                //逆变器
                Map<String, Map<Integer, List<WWarningRecordInMongo>>> warnRecordMap = new HashMap<>();

                //光储ESS
                Map<String, Map<String, List<WWarningRecordInMongo>>> essRecordMap = new HashMap<>();

                if (recordResponse.getStatus() == ResultConstant.RES_SUCCESS_CODE &&
                    CollectionUtils.isNotEmpty(recordResponse.getData())) {
                    warnRecordMap = recordResponse.getData().stream()
                        .filter(e -> e.getWarningType().equals(6))
                        .collect(Collectors.groupingBy(WWarningRecordInMongo::getBoxOutFactoryCode,
                            Collectors.groupingBy(WWarningRecordInMongo::getConnectorId,
                                Collectors.toList())));

                    essRecordMap = recordResponse.getData().stream()
                        .filter(e -> e.getWarningType().equals(7))
                        .collect(Collectors.groupingBy(WWarningRecordInMongo::getBoxOutFactoryCode,
                            Collectors.groupingBy(WWarningRecordInMongo::getDno,
                                Collectors.toList())));
                }

                final Map<String, Map<Integer, List<WWarningRecordInMongo>>> warnMap = warnRecordMap;
                final Map<String, Map<String, List<WWarningRecordInMongo>>> essMap = essRecordMap;

                //处理逆变器告警
                if (warnMap != null) {
                    list.forEach(gwno -> {
                        Map<Integer, List<WWarningRecordInMongo>> map = warnMap.get(gwno);
                        if (map != null) {
                            map.forEach((plugId, list1) -> {
                                Long errorCount = Long.valueOf(
                                    CollectionUtils.isNotEmpty(list1) ? list1.size() : 0);
                                BiPlugPo biPlugPo = new BiPlugPo()
                                    .setSiteId(siteId)
                                    .setEvseNo(gwno)
                                    .setPlugId(plugId)
                                    .setType(1L)
                                    .setDate(KeyTime)
                                    .setElectricity(BigDecimal.ZERO)
                                    .setErrorCount(errorCount.intValue())
                                    .setOfflineCount(0);
                                biPlugRwDs.insertOrUpdate(biPlugPo);
                            });
                        }
                    });
                }

                //处理ESS告警
                if (essMap != null) {
                    list.forEach(gwno -> {
                        Map<String, List<WWarningRecordInMongo>> map = essMap.get(gwno);
                        if (map != null) {
                            map.forEach((dno, list1) -> {
                                Long errorCount = Long.valueOf(
                                    CollectionUtils.isNotEmpty(list1) ? list1.size() : 0);
                                BiPlugPo biPlugPo = new BiPlugPo()
                                    .setSiteId(siteId)
                                    .setEvseNo(gwno)
                                    .setDno(dno)
                                    .setType(2L)
                                    .setPlugId(0)
                                    .setDate(KeyTime)
                                    .setElectricity(BigDecimal.ZERO)
                                    .setErrorCount(errorCount.intValue())
                                    .setOfflineCount(0);
                                biPlugRwDs.insertOrUpdate(biPlugPo);
                            });
                        }
                    });
                }
            })
            .block(Duration.ofSeconds(50L));   // 此处不要异步并发，会导致资源消耗过大

        /***逆变器,储能ESS告警统计结束***/

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        List<ChargerOrderTimeDivisionEx> divisionList =
            chargerOrderTimeDivisionRoDs.getDivisionBySiteAndTimeRange(siteId,
                sdf.format(startTime.getTime()),
                sdf.format(endTime.getTime()));

        divisionList = this.filterDurationError(divisionList);

        // 分时订单记录出现跨天时，需要专门处理
        List<ChargerOrderTimeDivisionEx> divisionSpanList =
            chargerOrderTimeDivisionRoDs.getDivisionBySiteAndTimeRangeSpanDay(siteId,
                sdf.format(startTime.getTime()),
                sdf.format(endTime.getTime()));

        divisionSpanList = this.filterDurationError(divisionSpanList);

        if (CollectionUtils.isNotEmpty(divisionSpanList)) {
            log.info("存在跨越0点的分时记录，共[{}]条，根据心跳拆分: {}",
                divisionSpanList.size(),
                divisionSpanList.stream()
                    .map(ChargerOrderTimeDivisionEx::getOrderNo)
                    .collect(Collectors.toList()));

            for (ChargerOrderTimeDivisionEx e : divisionSpanList) {

                final OrderInMongo byOrderNo = chargerOrderRepository.getByOrderNo(e.getOrderNo());
                ChargerOrderTimeDivisionEx separateDivision = this.separateHBList(e, byOrderNo,
                    startTime, endTime);
                if (separateDivision != null) {
                    divisionList.add(separateDivision);
                }

            }
        }

        log.info("{} 场站: {} 分时订单数据: {}, ", date, siteId,
            divisionList == null ? null : divisionList.size());
        Map<String, List<ChargerOrderTimeDivisionEx>> plugDivisionOrderMap =
            divisionList.stream()
                .collect(
                    Collectors.groupingBy(e -> siteId + "#" + e.getEvseNo() + "#" + e.getPlugId()));

        // 将没有订单的枪头置入枪头订单map，并为其创建一个空的订单列表作为value
        if (CollectionUtils.isNotEmpty(allPlugs)) {
            allPlugs.stream().map(plugNo -> {
                String evseNo = plugNo.substring(0, plugNo.length() - 2);
                Integer plugId = Integer.valueOf(plugNo.substring(plugNo.length() - 2));
                String plugKey = siteId + "#" + evseNo + "#" + plugId;
                return plugKey;
            }).forEach(plugKey -> {
                if (!plugDivisionOrderMap.keySet().contains(plugKey)) {
                    plugDivisionOrderMap.put(plugKey, new ArrayList<>());
                }
            });
        }

        int ret = 0;
        for (String siteEvsePlug : plugDivisionOrderMap.keySet()) {
            String[] keys = siteEvsePlug.split("#");
//            log.info("siteEvsePlug keys: {}", keys);
            if (keys == null || keys.length != 3) {
                log.warn("siteEvsePlug skip");
                continue;
            }
            String plugSiteId = keys[0];
            String evseId = keys[1];
            if (!org.apache.commons.lang3.StringUtils.isNumeric(
                keys[2])) {
                log.warn("无法识别的枪头号: plugId: {}, key: {}, val: {}, ",
                    keys[2], siteEvsePlug, plugDivisionOrderMap.get(siteEvsePlug));
                continue;
            }
            Integer plugId = Integer.valueOf(keys[2]);

            BiPlugPo biPlugPo = new BiPlugPo();
            biPlugPo.setSiteId(plugSiteId);
            biPlugPo.setEvseNo(evseId);
            biPlugPo.setPlugId(plugId);
            biPlugPo.setDate(KeyTime);

            List<ChargerOrderTimeDivisionEx> subOrderList = plugDivisionOrderMap.get(siteEvsePlug);

            // 计算充电时长
            if (CollectionUtils.isNotEmpty(subOrderList)) {

                // 订单个数统计
                Long orderCount = subOrderList.stream().map(ChargerOrderTimeDivisionEx::getOrderNo)
                    .distinct().count();
                biPlugPo.setOrderCount(orderCount.intValue());

                Long duration = subOrderList.stream()
                    .mapToLong(ChargerOrderTimeDivisionEx::getDuration).sum();

                // 枪头使用时长
                biPlugPo.setDuration(duration.intValue());

                // 计算结束时间在范围内的订单的充电电量
                biPlugPo.setElectricity(this.sumList(subOrderList.stream()
                    .map(ChargerOrderTimeDivisionEx::getElectric)
                    .collect(Collectors.toList())));

                // 枪头服务费汇总
                biPlugPo.setServFee(
                    this.sumList(
                        subOrderList.stream()
                            .map(ChargerOrderTimeDivisionEx::getServicePrice)
                            .collect(Collectors.toList())));

                // 枪头电费汇总
                biPlugPo.setElecFee(
                    this.sumList(
                        subOrderList.stream()
                            .map(ChargerOrderTimeDivisionEx::getElectricPrice)
                            .collect(Collectors.toList())));

                // 枪头费用汇总
                biPlugPo.setFee(biPlugPo.getServFee().add(biPlugPo.getServFee()));
//                subOrderList.stream()
//                        .map(ChargerOrderTimeDivisionEx::getOrderPrice)
//                        .collect(Collectors.toList());

                List<ChargerOrderTimeDivisionEx> tag1 = new ArrayList<>();// 尖
                List<ChargerOrderTimeDivisionEx> tag2 = new ArrayList<>();// 峰
                List<ChargerOrderTimeDivisionEx> tag3 = new ArrayList<>();// 平
                List<ChargerOrderTimeDivisionEx> tag4 = new ArrayList<>();// 谷

                subOrderList.stream().forEach(e -> {
                    if (TAG_1.equals(e.getTag())) {
                        tag1.add(e);
                    } else if (TAG_2.equals(e.getTag())) {
                        tag2.add(e);
                    } else if (TAG_3.equals(e.getTag())) {
                        tag3.add(e);
                    } else if (TAG_4.equals(e.getTag())) {
                        tag4.add(e);
                    } else {
                        log.error("没有找到分时订单对应的分时类型: {}", e.getOrderNo());
                    }
                });

                // 尖 充电量
                biPlugPo.setElecTag1(
                    this.sumList(
                        tag1.stream()
                            .map(ChargerOrderTimeDivisionEx::getElectric)
                            .collect(Collectors.toList())));

                // 峰 充电量
                biPlugPo.setElecTag2(
                    this.sumList(
                        tag2.stream()
                            .map(ChargerOrderTimeDivisionEx::getElectric)
                            .collect(Collectors.toList())));

                // 平 充电量
                biPlugPo.setElecTag3(
                    this.sumList(
                        tag3.stream()
                            .map(ChargerOrderTimeDivisionEx::getElectric)
                            .collect(Collectors.toList())));

                // 谷 充电量
                biPlugPo.setElecTag4(
                    this.sumList(
                        tag4.stream()
                            .map(ChargerOrderTimeDivisionEx::getElectric)
                            .collect(Collectors.toList())));

            } else {
                biPlugPo.setOrderCount(0);
                biPlugPo.setDuration(0);
                biPlugPo.setElectricity(BigDecimal.ZERO);
                biPlugPo.setServFee(BigDecimal.ZERO);
                biPlugPo.setElecFee(BigDecimal.ZERO);
                biPlugPo.setFee(BigDecimal.ZERO);
                biPlugPo.setElecTag1(BigDecimal.ZERO);
                biPlugPo.setElecTag2(BigDecimal.ZERO);
                biPlugPo.setElecTag3(BigDecimal.ZERO);
                biPlugPo.setElecTag4(BigDecimal.ZERO);
            }

            // 当日最长时间（秒）填充枪头可用时间
            biPlugPo.setAvailable(DateUtil.DAY_SECOND);

            biPlugRwDs.insertOrUpdate(biPlugPo);
            ret++;
        }
        return ret;
    }

    /**
     * 过滤时长不在合法范围内的分时订单
     *
     * @param divisionList
     * @return
     */
    private List<ChargerOrderTimeDivisionEx> filterDurationError(
        List<ChargerOrderTimeDivisionEx> divisionList) {
        if (CollectionUtils.isNotEmpty(divisionList)) {
            // 检查异常的分时订单
            Set<String> errorDivisionOrderIds = divisionList.stream()
                .filter(e -> e.getDuration() != null && (e.getDuration() < 0
                    || e.getDuration() > ONE_DAY_SECOND))
                .map(ChargerOrderTimeDivisionEx::getTimeDivisionOrderId)
                .collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(errorDivisionOrderIds)) {
                log.error("分时订单时长异常, orderId: {}", errorDivisionOrderIds);

                log.info("过滤分时订单时长异常订单: {}个", errorDivisionOrderIds.size());
                return divisionList.stream()
                    .filter(e -> !errorDivisionOrderIds.contains(e.getTimeDivisionOrderId()))
                    .collect(Collectors.toList());
            }
        }
        return divisionList;
    }

    /**
     * 拆分分时表，补救
     *
     * @param chargerOrderTimeDivisionEx
     * @param byOrderNo
     * @param startTime
     * @param endTime
     * @return
     */
    private ChargerOrderTimeDivisionEx separateHBList(
        ChargerOrderTimeDivisionEx chargerOrderTimeDivisionEx,
        OrderInMongo byOrderNo,
        Date startTime,
        Date endTime) {

        if (byOrderNo == null || CollectionUtils.isEmpty(byOrderNo.getDetails())) {

            log.info("无法根据心跳拆分订单信息,开启分时订单记录等比划分");

            return this.getDivisionByFactor(chargerOrderTimeDivisionEx, startTime, endTime);

//            try {
//                final SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                final Date orderStart = SDF.parse(chargerOrderTimeDivisionEx.getStartTime());
//                final Date orderEnd = SDF.parse(chargerOrderTimeDivisionEx.getStartTime());
//
//                final long orderDayStart = Math.max(orderStart.getTime(), startTime.getTime());
//                final long orderDayEnd = Math.min(orderEnd.getTime(), endTime.getTime());
//
//                final long orderDayDuration = orderDayEnd - orderDayStart;
//                final long dayDuration = endTime.getTime() - startTime.getTime();
//
//                if(orderDayDuration < 0 || dayDuration <= 0 || orderDayDuration > dayDuration) {
//                    log.error("等比划分失败，比例系数不正确: orderDayDuration: {}, dayDuration: {}",
//                            orderDayDuration, dayDuration);
//                    return null;
//                }
//
//                final double factor = orderDayDuration / dayDuration;
//                final BigDecimal BDFactor = BigDecimal.valueOf(factor);
//
//                ChargerOrderTimeDivisionEx ret = new ChargerOrderTimeDivisionEx();
//                BeanUtils.copyProperties(chargerOrderTimeDivisionEx, ret);
//
//                ret.setOrderNo(byOrderNo.getOrderNo()).
//                        setDuration((int) (ret.getDuration().doubleValue() * factor))
//                        .setElectric(ret.getElectric()
//                                .multiply(BDFactor)
//                                .setScale(4, RoundingMode.HALF_UP))
//                        .setServicePrice(ret.getServicePrice()
//                                .multiply(BDFactor)
//                                .setScale(2, RoundingMode.HALF_UP))
//                        .setElectricPrice(ret.getElectricPrice()
//                                .multiply(BDFactor)
//                                .setScale(2, RoundingMode.HALF_UP))
//                        .setTag(chargerOrderTimeDivisionEx.getTag());
//                return ret;
//            } catch(Exception e) {
//                log.error("分解心跳失败,时间解析出错", e , e);
//                return null;
//            }
        }

        List<ChargerDetail> details = byOrderNo.getDetails();

        final long startTimestamp = startTime.getTime() / 1000;
        final long endTimestamp = endTime.getTime() / 1000;

        final long TimeInRange = 600;

        // 获取10分钟内的心跳
//        List<ChargerDetail> spanListLeft10 = details.stream()
//                .filter(e -> Math.abs(e.getTimestamp() - startTimestamp) <= Minute_10)
//                .collect(Collectors.toList());
//
//        if(CollectionUtils.isNotEmpty(spanListLeft10)) {
//            final ChargerDetail spanLeft = spanListLeft10.stream().reduce(null, (s, e) -> {
//                if (s == null) {
//                    return e;
//                }
//                if (Math.abs(s.getTimestamp() - startTimestamp) < Math.abs(e.getTimestamp() - startTimestamp)) {
//                    return s;
//                } else {
//                    return e;
//                }
//            });
//            ChargerOrderTimeDivisionEx ret = new ChargerOrderTimeDivisionEx();
//            BeanUtils.copyProperties(chargerOrderTimeDivisionEx, ret);
//
//            ret.setOrderNo(byOrderNo.getOrderNo()).
//                    setDuration(ret.getDuration().intValue() - spanLeft.getDuration().intValue())
//                    .setElectric(ret.getElectric().subtract(spanLeft.getKwh()))
//                    .setServicePrice(ret.getServicePrice().subtract(spanLeft.getServFee()))
//                    .setElectricPrice(ret.getElectricPrice().subtract(spanLeft.getElecFee()))
//                    .setTag(chargerOrderTimeDivisionEx.getTag());
//
//            return ret;
//        }
        ChargerDetail fitDetailLeft = this.getFitDetail(details, chargerOrderTimeDivisionEx,
            startTimestamp, TimeInRange);

        if (fitDetailLeft != null) {
            log.info("开始时间端:dayStart: {}, orderDivEnd: {}", startTime,
                chargerOrderTimeDivisionEx.getStopTime());
            ChargerOrderTimeDivisionEx ret = new ChargerOrderTimeDivisionEx();
            BeanUtils.copyProperties(chargerOrderTimeDivisionEx, ret);

            final SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            final Date orderEnd;
            try {
                orderEnd = SDF.parse(chargerOrderTimeDivisionEx.getStopTime());
            } catch (ParseException e) {
                log.error("分解心跳失败,时间解析出错", e, e);
                return null;
            }

            final long orderDayEnd =
                orderEnd.getTime() / 1000;//Math.min(orderEnd.getTime(), endTime.getTime()) / 1000;

//            ret.setDuration(ret.getDuration() - fitDetailLeft.getDuration().intValue())
            ret.setDuration((int) orderDayEnd - (int) startTimestamp)
                .setElectric(ret.getElectric().subtract(fitDetailLeft.getKwh()))
                .setServicePrice(ret.getServicePrice().subtract(fitDetailLeft.getServFee()))
                .setElectricPrice(ret.getElectricPrice().subtract(fitDetailLeft.getElecFee()))
                .setTag(chargerOrderTimeDivisionEx.getTag());
            return ret;
        }

        ChargerDetail fitDetailRight = this.getFitDetail(details, chargerOrderTimeDivisionEx,
            endTimestamp, TimeInRange);
        if (fitDetailRight != null) {
            log.info("结束时间端:dayEnd: {}, orderDivStart: {}", endTime,
                chargerOrderTimeDivisionEx.getStartTime());
            ChargerOrderTimeDivisionEx ret = new ChargerOrderTimeDivisionEx();
            BeanUtils.copyProperties(chargerOrderTimeDivisionEx, ret);

            final SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            final Date orderStart;
            try {
                orderStart = SDF.parse(chargerOrderTimeDivisionEx.getStartTime());
            } catch (ParseException e) {
                log.error("分解心跳失败,时间解析出错", e, e);
                return null;
            }

            final long orderDayStart = orderStart.getTime()
                / 1000;//Math.max(orderStart.getTime(), startTime.getTime()) / 1000;

//            ret.setDuration(fitDetailRight.getDuration().intValue())
            ret.setDuration((int) endTimestamp - (int) orderDayStart)
                .setElectric(fitDetailRight.getKwh())
                .setServicePrice(fitDetailRight.getServFee())
                .setElectricPrice(fitDetailRight.getElecFee())
                .setTag(chargerOrderTimeDivisionEx.getTag());
            return ret;
        }

//        List<ChargerDetail> spanListLeft = details.stream()
//                .filter(e -> e.getTimestamp() < startTimestamp)
//                .collect(Collectors.toList());
//        ChargerDetail head;
//        if(CollectionUtils.isNotEmpty(spanListLeft)) {
//            head = spanListLeft.get(spanListLeft.size() - 1);
//        } else {
//            head = details.get(0);
//        }
//
//        List<ChargerDetail> spanListRight = details.stream()
//                .filter(e -> e.getTimestamp() <= endTimestamp)
//                .collect(Collectors.toList());
//        ChargerDetail tail = null;
////        if(CollectionUtils.isNotEmpty(spanListRight)) {
//            tail = spanListRight.get(spanListRight.size() - 1);
////        } else {
////            tail = spanListRight.get(spanListLeft.size() - 1);
////        }
//        if(head == null || tail == null) {
//            log.warn("分解心跳失败: head: {}, tail: {}", JsonUtils.toJsonString(head), JsonUtils.toJsonString(tail));
//            return null;
//        }
//
//        ChargerOrderTimeDivisionEx ret = new ChargerOrderTimeDivisionEx();
//        BeanUtils.copyProperties(chargerOrderTimeDivisionEx, ret);
//
//        ret.setOrderNo(byOrderNo.getOrderNo()).
//                setDuration(tail.getDuration().intValue() - head.getDuration().intValue())
//                .setElectric(tail.getKwh().subtract(head.getKwh()))
//                .setServicePrice(tail.getServFee().subtract(head.getServFee()))
//                .setElectricPrice(tail.getElecFee().subtract(head.getElecFee()))
//                .setTag(chargerOrderTimeDivisionEx.getTag());
//
//        return ret;

        log.info("没有找到{}秒范围内的有效心跳, 开启分时订单记录等比划分", TimeInRange);
        return this.getDivisionByFactor(chargerOrderTimeDivisionEx, startTime, endTime);
    }

    /**
     * 获取时间点范围内最接近时间点的心跳
     *
     * @param details
     * @param chargerOrderTimeDivisionEx
     * @param timestamp
     * @param second
     * @return
     */
    private ChargerDetail getFitDetail(List<ChargerDetail> details,
        ChargerOrderTimeDivisionEx chargerOrderTimeDivisionEx,
        long timestamp,
        long second) {

        List<ChargerDetail> spanList = details.stream()
            .filter(e -> Math.abs(e.getTimestamp() - timestamp) <= second)
            .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(spanList)) {
            final ChargerDetail spanDetail = spanList.stream().reduce(null, (s, e) -> {
                if (s == null) {
                    return e;
                }
                if (Math.abs(s.getTimestamp() - timestamp) < Math.abs(
                    e.getTimestamp() - timestamp)) {
                    return s;
                } else {
                    return e;
                }
            });

            if (spanDetail == null ||
                spanDetail.getKwh() == null ||
                spanDetail.getServFee() == null ||
                spanDetail.getElecFee() == null) {
                log.warn("订单心跳信息不全: {}", chargerOrderTimeDivisionEx.getOrderNo());
                return null;
            }

            return spanDetail;
        } else {
            return null;
        }
    }

    /**
     * 根据时间比例划分出分时订单
     *
     * @param chargerOrderTimeDivisionEx
     * @param startTime
     * @param endTime
     * @return
     */
    private ChargerOrderTimeDivisionEx getDivisionByFactor(
        ChargerOrderTimeDivisionEx chargerOrderTimeDivisionEx,
        Date startTime,
        Date endTime) {
        try {
            final SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            final Date orderStart = SDF.parse(chargerOrderTimeDivisionEx.getStartTime());
            final Date orderEnd = SDF.parse(chargerOrderTimeDivisionEx.getStopTime());

            final long orderDayStart = Math.max(orderStart.getTime(), startTime.getTime());
            final long orderDayEnd = Math.min(orderEnd.getTime(), endTime.getTime());

            final long orderDayDuration = orderDayEnd - orderDayStart;
            final long orderDuration = orderEnd.getTime() - orderStart.getTime();

            if (orderDayDuration < 0 || orderDuration <= 0 || orderDayDuration > orderDuration) {
                log.error("等比划分失败，比例系数不正确: orderDayDuration: {}, dayDuration: {}",
                    orderDayDuration, orderDuration);
                return null;
            }

            final double factor = orderDayDuration / (double) orderDuration;
            final BigDecimal BDFactor = BigDecimal.valueOf(factor);

            ChargerOrderTimeDivisionEx ret = new ChargerOrderTimeDivisionEx();
            BeanUtils.copyProperties(chargerOrderTimeDivisionEx, ret);

            ret.setDuration((int) (ret.getDuration().doubleValue() * factor))
                .setElectric(ret.getElectric()
                    .multiply(BDFactor)
                    .setScale(4, RoundingMode.HALF_UP))
                .setServicePrice(ret.getServicePrice()
                    .multiply(BDFactor)
                    .setScale(2, RoundingMode.HALF_UP))
                .setElectricPrice(ret.getElectricPrice()
                    .multiply(BDFactor)
                    .setScale(2, RoundingMode.HALF_UP))
                .setTag(chargerOrderTimeDivisionEx.getTag());
            return ret;
        } catch (Exception e) {
            log.error("分解心跳失败,时间解析出错", e, e);
            return null;
        }
    }

    private BigDecimal sumList(List<BigDecimal> list) {
        BigDecimal ret = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(list)) {

            for (BigDecimal one : list) {
                if (one != null) {
                    ret = DecimalUtils.add(one, ret);
                }
            }
        }
        return ret;
    }
}