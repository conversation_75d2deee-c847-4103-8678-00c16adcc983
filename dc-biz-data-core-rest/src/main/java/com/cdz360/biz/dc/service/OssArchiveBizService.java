package com.cdz360.biz.dc.service;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.domain.OrderInMongo;
import com.cdz360.biz.utils.service.OssArchiveService;
import com.cdz360.biz.model.oss.OssStsDto;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Slf4j
@Service
public class OssArchiveBizService {

    @Autowired
    private OssArchiveService ossArchiveService;

    /**
     * 从阿里云OSS获取订单详情信息
     */
    public OrderInMongo getOrderDetail(Date stopTime, String siteId, String orderNo) throws Exception {
        if (StringUtils.isBlank(orderNo) || StringUtils.isBlank(siteId) || stopTime == null) {
            log.warn("参数错误,无法获取oss的订单详情. orderNo = {}, siteId = {}, stopTime = {}", orderNo, siteId, stopTime);
            return null;
        }
        String json = ossArchiveService.getOrderDetailData(stopTime, siteId, orderNo);
//        log.info("json = {}", json);
//        System.out.println(json);
        if (StringUtils.isBlank(json)) {
            return null;
        }
        return JsonUtils.fromJson(json, OrderInMongo.class);
    }

    public boolean uploadOrderDetail(ChargerOrder o, byte[] buf) {
        return ossArchiveService.uploadOrderDetail(o, buf);
    }

    public OssStsDto getSts() {
        return ossArchiveService.getSts();
    }

}
