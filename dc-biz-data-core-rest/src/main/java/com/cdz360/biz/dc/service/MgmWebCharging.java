package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.model.order.type.OrderPayType;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.framework.common.domain.BlocUserDto;
import com.chargerlinkcar.framework.common.domain.param.AuthMediaParam;
import com.chargerlinkcar.framework.common.domain.request.StartChargerRequest;
import com.chargerlinkcar.framework.common.domain.request.StopChargerRequest;
import com.chargerlinkcar.framework.common.domain.vo.AuthMediaResult;
import com.chargerlinkcar.framework.common.domain.vo.CloudChargeTempVo;
import com.chargerlinkcar.framework.common.domain.vo.CloudChargeVo;
import com.chargerlinkcar.framework.common.domain.vo.CommCusRef;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUserVo;
import com.chargerlinkcar.framework.common.domain.vo.SiteDebitAccountBase;
import com.chargerlinkcar.framework.common.domain.vo.UserPropVO;
import com.chargerlinkcar.framework.common.domain.vo.UserVo;
import com.chargerlinkcar.framework.common.exception.DcBalanceException;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.feign.SiteDataCoreFeignClient;
import com.chargerlinkcar.framework.common.feign.UserFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class MgmWebCharging extends PlatformStartCharging {

    @Value("${iot.fee.min:5}")
    private BigDecimal cloudChargeStartAmount;//默认启动金额作为平台单枪开启充电最小金额

    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;
    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private SiteDataCoreFeignClient siteDataCoreFeignClient;
    @Autowired
    private RedisIotReadService redisIotReadService;
    @Autowired
    private ChargerOrderBizService chargerOrderBizService;

    @Autowired
    private SiteRoDs siteRoDs;

    @PostConstruct
    public void init() {
        chargerOrderBizService.addStartChargingMap(OrderStartType.MGM_WEB_MANUAL, this);
        chargerOrderBizService.addStartChargingMap(OrderStartType.MGM_WEB_BATCH, this);
    }


    @Override
    public CloudChargeVo checkChargingQueue(StartChargerRequest chargerRequest) {
        log.info("平台开启充电参数 chargerRequest：{}", JsonUtils.toJsonString(chargerRequest));
        IotAssert.isNotNull(chargerRequest.getStartType(), "订单启动方式不能为空");

        List<String> plugNoList = new ArrayList<>();
        if (chargerRequest.getPlugNo() != null) {
            plugNoList.add(chargerRequest.getPlugNo());
        }
        if (CollectionUtils.isNotEmpty(chargerRequest.getPlugNoList())) {
            plugNoList.addAll(chargerRequest.getPlugNoList());
        }

        //STEP 0.开启充电前置检查
        IotAssert.isTrue(chargerRequest != null && chargerRequest.getSiteId() != null,
            "请传入要开启充电的场站ID");

        List<PlugVo> plugVoList = redisIotReadService.getPlugList(plugNoList);

        IotAssert.isTrue(
            CollectionUtils.isNotEmpty(plugVoList) && plugNoList.size() == plugVoList.size(),
            "无法找到枪头信息，请刷新后重试");

        if (null != chargerRequest.getStopSoc()) {
            IotAssert.isTrue(chargerRequest.getStopSoc() > 0 &&
                chargerRequest.getStopSoc() <= 100, "停充SOC无效");
        }

        Map<String, EvseVo> evseVoMap =
            redisIotReadService.getEvseList(
                    plugVoList.stream().map(PlugVo::getEvseNo).distinct().collect(Collectors.toList()))
                .stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(EvseVo::getEvseNo, o -> o));
        plugVoList.forEach(e -> {
            try {
                IotAssert.isNotBlank(e.getEvseNo(), "枪头" + e.getPlugNo() + "不在线");
                IotAssert.isTrue(e.getStatus() != null,
                    "启动失败，" + e.getEvseNo() + "桩未上线");
                if (e.getStatus() == PlugStatus.RECHARGE_END && Boolean.TRUE.equals(
                    e.getConstantCharge())) {
                    // 二次充电不校验枪状态、枪头订单
                } else {
                    IotAssert.isTrue(e.getStatus().equals(PlugStatus.CONNECT),
                        "启动失败，" + e.getEvseNo() + "桩" + e.getIdx()
                            + "枪状态不是空占，请插拔枪后重试");
                    IotAssert.isTrue(StringUtils.isBlank(e.getOrderNo()),
                        "启动失败，" + e.getEvseNo() + "桩" + e.getIdx()
                            + "枪有进行中的充电订单，请插拔枪后重试");
                }
                IotAssert.isTrue(e.getPriceCode() != null && e.getPriceCode() > 0,
                    "启动失败，充电桩" + e.getEvseNo() + "未下发计费模板");
                EvseVo evseVo = evseVoMap.get(e.getEvseNo());
                IotAssert.isTrue(
                    evseVo != null && evseVo.getPriceCode() != null && evseVo.getPriceCode() > 0,
                    "启动失败，充电桩" + e.getEvseNo() + "未下发计费模板");
            } catch (Exception ex) {
                log.warn("异常枪头 plug = {}", e);
                throw ex;
            }
        });

//        TokenRequest tokenRequest = new TokenRequest();
//        tokenRequest.setToken(token);
//        JSONObject json = authCenterFeignClient.getCurrentUser(tokenRequest);
//        if (json == null || json.get("status") == null || !StringUtils.equals(json.get("status").toString(), "0")) {
//            throw new DcServiceException("当前商户信息获取失败");
//        }
//        SysUser sysUser = JSONObject.parseObject(JsonUtils.toJsonString(json.get("data")), SysUser.class);

        //STEP 1.校验是否允许后台充电，并校验账户正确性
//        ObjectResponse<SitePo> siteSimpleInfoVoObjectResponse
//                = siteDataCoreFeignClient.getSiteById(chargerRequest.getSiteId());
//        FeignResponseValidate.check(siteSimpleInfoVoObjectResponse);
//        SitePo site = siteSimpleInfoVoObjectResponse.getData();
        SitePo site = siteRoDs.getSite(chargerRequest.getSiteId());
        Long sitePayAccountId = site.getPayAccountId();
        PayAccountType payType = PayAccountType.valueOf(site.getDefaultPayType());
//        Integer defaultPayType = site.getDefaultPayType();
        BigDecimal siteFrozenAmount = site.getFrozenAmount();
        List<String> siteGids = null;
        Long orderCustomerId = null;
        Long orderCustomerCommId = null;
        String orderCustomerName = null;
        Long orderPayAccountId = null;
        String accountNo = null;
        String orderMobilePhone = null;
        if (site.getDefaultPayType().equals(PayAccountType.UNKNOWN.getCode())) {

            throw new DcServiceException("后台启动禁用模式下无法开启充电");
        } else if (site.getDefaultPayType().equals(PayAccountType.OTHER.getCode())) {

            //平台页面开启充电（枪头监控和站点中枪头管理）, 场站配置为启动时选择，此时需校验传入的账户信息
            IotAssert.isTrue(chargerRequest.getPayType() != null
                    && chargerRequest.getPayType() != PayAccountType.UNKNOWN
                    && chargerRequest.getPayType() != PayAccountType.OTHER,
                "请选择正确的结算账户类型");
            chargerRequest.setStartCharingEnable(null);//设置为null
            chargerRequest.setSettlementMethod(null);//设置为null
            CloudChargeVo result = this.siteDebitAccountCheck(chargerRequest);
            sitePayAccountId = result.getSitePayAccountId();
//            defaultPayType = result.getDefaultPayType();
            payType = result.getPayType();
            siteGids = result.getSiteGids();
            orderCustomerId = result.getOrderCustomerId();
            orderCustomerCommId = result.getOrderCustomerCommId();
            orderCustomerName = result.getOrderCustomerName();
            orderPayAccountId = result.getOrderPayAccountId();
            accountNo = result.getAccountNo();
            orderMobilePhone = result.getOrderMobilePhone();
        } else {
            // 平台页面开启充电（枪头监控和站点中枪头管理），且该场站明确配置了扣款账户
            // 重新获取相关信息
            CloudChargeVo cloudChargeVo = getCloudChargeInfo(sitePayAccountId,
                chargerRequest.getSiteId(),
                payType);
            siteGids = cloudChargeVo.getSiteGids();
            orderCustomerId = cloudChargeVo.getOrderCustomerId();
            orderCustomerCommId = cloudChargeVo.getOrderCustomerCommId();
            orderCustomerName = cloudChargeVo.getOrderCustomerName();
            orderPayAccountId = cloudChargeVo.getOrderPayAccountId();
            accountNo = cloudChargeVo.getAccountNo();
            orderMobilePhone = cloudChargeVo.getOrderMobilePhone();
        }

        //STEP 2.根据枪头数量计算最小开启金额，若账户余额不足则失败
        BigDecimal initAmount = null;
        if (plugNoList.size() == 1) {
            initAmount = cloudChargeStartAmount;
        } else {
            //initAmount = Math.toIntExact(plugNoList.size() * DecimalUtils.yuan2fen(siteFrozenAmount));
            initAmount = siteFrozenAmount.multiply(BigDecimal.valueOf(plugNoList.size()));
        }
        AuthMediaResult authMediaResult = null;
        try {
            AuthMediaParam authParam = new AuthMediaParam();
            authParam.setTopCommId(orderCustomerCommId)
                .setSiteId(chargerRequest.getSiteId())
                .setSiteCommId(site.getOperateId())
                .setSiteGids(siteGids)
                .setEvseNo(null)
                .setCusId(orderCustomerId)
                .setBalanceId(orderPayAccountId)
                .setPayType(payType)
                .setRealTimeFlag(true)
                .setFrozenAmount(siteFrozenAmount)
                .setInitAmount(initAmount);
            ObjectResponse<AuthMediaResult> authMediaResultObjectResponse =
                userFeignClient.authByBalanceIdAndPayType(authParam);
            FeignResponseValidate.check(authMediaResultObjectResponse);
            authMediaResult = authMediaResultObjectResponse.getData();
        } catch (DcBalanceException e) {
            log.info("msg: {}", e.getMessage(), e);
            throw new DcBalanceException(
                "余额或授信额度不足无法开启充电，可尝试逐个开启或进行账户充值");
        } catch (Exception ex) {
            log.info("msg: {}", ex.getMessage(), ex);
            throw ex;
        }
        IotAssert.isTrue(
            authMediaResult.getFrozenAmount() != null && authMediaResult.getBalance() != null,
            "余额不足无法开启充电，可尝试逐个开启或进行账户充值");

        //STEP 3.组装队列元素并返回
        List<CloudChargeTempVo> tempVos = plugVoList.stream().map(e -> {
            CloudChargeTempVo vo = new CloudChargeTempVo();
            vo.setEvseNo(e.getEvseNo())
                .setPlugId(e.getIdx())
                .setPlugNo(e.getPlugNo());
            return vo;
        }).collect(Collectors.toList());
        CloudChargeVo cloudChargeVo = new CloudChargeVo();
        cloudChargeVo.setSiteId(chargerRequest.getSiteId())
            .setSiteOperateId(site.getOperateId())
            .setSiteTopCommId(site.getTopCommId())
            .setBcCodeList(tempVos)
            .setOpId(chargerRequest.getSysUserId())
            .setOrderCustomerId(orderCustomerId)
            .setOrderCustomerName(orderCustomerName)
            .setOrderPayAccountId(orderPayAccountId)
            .setDefaultPayType(payType.getCode())
            .setPayType(payType)
            .setFrozenAmount(authMediaResult.getFrozenAmount())
            .setAccountTotalAmount(authMediaResult.getBalance())
//                .setDiscountRefId(authMediaResult.getDiscountRefId())
            .setAccountNo(accountNo)
            .setOrderMobilePhone(orderMobilePhone)
            .setSiteContactsPhone(site.getContactsPhone())
            .setStartType(chargerRequest.getStartType());

        // 停充SOC
        if (null != chargerRequest.getStopSoc()) {
            cloudChargeVo.setStopSoc(chargerRequest.getStopSoc());
        } else if (null != site.getStopSoc() && site.getStopSoc() > 0) {
            cloudChargeVo.setStopSoc(site.getStopSoc());
        }

        return cloudChargeVo;
    }

    /**
     * 场站后台开启充电-扣款账户校验 用于 开启充电 和 修改场站扣款账户 时
     *
     * @param request
     * @return
     */
    public CloudChargeVo siteDebitAccountCheck(SiteDebitAccountBase request) {
        // 开启充电时,startCharingEnable和settlementMethod都为null,payType不为null
        if (request.getStartCharingEnable() == null && request.getSettlementMethod() == null) {
            // do nothing
        } else {
            if (request.getStartCharingEnable() != null
                && !request.getStartCharingEnable()
                && request.getSettlementMethod() != null) {
                // 配置为禁用，但指定结算账户方式不为null
                throw new DcServiceException("请确认您的勾选结果后，重新提交");
            }
            if (request.getStartCharingEnable() != null
                && request.getStartCharingEnable()
                && request.getSettlementMethod() == null) {
                // 配置为启用，但指定结算账户方式为null
                throw new DcServiceException("未选择结算账户方式");
            }
            if (request.getSettlementMethod() != null
                && request.getSettlementMethod() == 1 &&
                (request.getPayType() != null || request.getCommId() != null
                    || request.getPhone() != null)) {
                // 配置为启动时指定，但结算账户等字段不为null
                throw new DcServiceException("请确认您的勾选结果后，重新提交");
            }

            if (request.getStartCharingEnable() != null && !request.getStartCharingEnable()) {
                //不允许后台开启充电
                request.setPayType(PayAccountType.UNKNOWN);
            } else if (request.getStartCharingEnable() != null
                && request.getStartCharingEnable()
                && request.getSettlementMethod() != null
                && request.getSettlementMethod() == 1) {
                //后台启动时指定
                request.setPayType(PayAccountType.OTHER);
            }
        }
        IotAssert.isNotNull(request.getPayType(), "未选择结算账户类型");

//        ObjectResponse<com.chargerlinkcar.framework.common.domain.vo.SiteSimpleInfoVo> siteSimpleInfoVoObjectResponse
//                = site2FeignClient.getSiteSimpleInfoById(request.getSiteId());
//        //ObjectResponse<SiteSimpleInfoVo> siteSimpleInfoVoObjectResponse = siteFeignClient.getSiteSimpleInfoById(request.getSiteId());
//        FeignResponseValidate.check(siteSimpleInfoVoObjectResponse);
        Long orderCustomerId = null;
        Long orderCustomerCommId = null;
        String orderCustomerName = null;
        List<String> siteGids = null;
        Long sitePayAccountId = null;
        Long orderPayAccountId = null;
//        Integer defaultPayType = null;
        PayAccountType payType = request.getPayType();
        String accountNo = null;
        String orderMobilePhone = null;
        switch (payType) {
            case PERSONAL:
                IotAssert.isNotBlank(request.getPhone(), "手机号不能为空");
                ObjectResponse<UserPropVO> userPropVOObjectResponse = userFeignClient.findByPhone(
                    request.getPhone(), request.getTopCommId());
                FeignResponseValidate.check(userPropVOObjectResponse);
                UserPropVO vo = userPropVOObjectResponse.getData();
                IotAssert.isTrue(NumberUtils.equals(vo.getStatus(), 10001),
                    "当前手机号对应账户不可用");
                IotAssert.isTrue(Boolean.FALSE == vo.getDebt(),
                    "您存在欠费订单，请将欠费的订单处理再充电");
                orderCustomerId = vo.getUserId();
                orderCustomerCommId = request.getTopCommId();
                orderCustomerName = vo.getUsername();
                sitePayAccountId = vo.getUserId();
                orderPayAccountId = request.getTopCommId();
//                defaultPayType = OrderPayType.PERSON.getCode();
                accountNo = request.getPhone();
                orderMobilePhone = request.getPhone();
                break;
            case CREDIT:
                // TODO: 2020/3/16 校验 corpUserId
                IotAssert.isNotNull(request.getCommId(), "请先选择所属商户");
                IotAssert.isNotNull(request.getCorpUserId(), "请先选择企业授信客户");
                ObjectResponse<BlocUserDto> userDtoObjectResponse = userFeignClient.getByRBlocUserId(
                    request.getCorpUserId());
                FeignResponseValidate.check(userDtoObjectResponse);
                BlocUserDto dto = userDtoObjectResponse.getData();
                IotAssert.isTrue(null != dto && null != dto.getEnable() && dto.getEnable(),
                    "当前账户不可用");
                IotAssert.isTrue(Boolean.FALSE == dto.getRblocUserDebt(),
                    "您存在欠费订单，请将欠费的订单处理再充电");
                RBlocUser temp = new RBlocUser();
                temp.setId(request.getCorpUserId());
                ListResponse<RBlocUser> rBlocUserListResponse = userFeignClient.findByCondition(
                    temp);
                FeignResponseValidate.check(rBlocUserListResponse);
                IotAssert.isTrue(CollectionUtils.isNotEmpty(rBlocUserListResponse.getData()),
                    "找不到对应企业授信客户");
                RBlocUser rBlocUser = rBlocUserListResponse.getData().get(0);
                IotAssert.isTrue(null != rBlocUser.getStatus() &&
                    NumberUtils.equals(rBlocUser.getStatus(), 1), "当前账户不可用");
                IotAssert.isTrue(null != rBlocUser.getCommId() &&
                        NumberUtils.equals(rBlocUser.getCommId(), request.getCommId()),
                    "授信客户与所属商户不对应，请重新配置结算账户");
                siteGids = super.checkCorpGidsAuthority(dto.getId(), request.getSiteId());
                orderCustomerId = rBlocUser.getUserId();
                orderCustomerCommId = rBlocUser.getCommId();
                orderCustomerName = rBlocUser.getUserName();
                sitePayAccountId = rBlocUser.getId();
                orderPayAccountId = rBlocUser.getId();
//                defaultPayType = OrderPayType.BLOC.getCode();
                accountNo = rBlocUser.getPhone();
                orderMobilePhone = rBlocUser.getPhone();
                break;
            case COMMERCIAL:
                // TODO: 2020/3/16 校验commId
                IotAssert.isNotNull(request.getCommId(), "请先选择所属商户");
                IotAssert.isNotNull(request.getPhone(), "手机号不能为空");
                ObjectResponse<CommCusRef> commCusRefObjectResponse = userFeignClient.findByCommIdAndPhone(
                    request.getCommId(), request.getPhone());
                FeignResponseValidate.check(commCusRefObjectResponse);
                CommCusRef ref = commCusRefObjectResponse.getData();
                IotAssert.isTrue(NumberUtils.equals(ref.getEnable(), 1),
                    "当前手机号对应账户不可用");
                IotAssert.isTrue(Boolean.FALSE == ref.getUserDebt(),
                    "您存在欠费订单，请将欠费的订单处理再充电");
                orderCustomerId = ref.getUserId();
                orderCustomerCommId = ref.getUserCommId();
                orderCustomerName = ref.getUserName();
                sitePayAccountId = ref.getId();
                orderPayAccountId = request.getCommId();
//                defaultPayType = OrderPayType.MERCHANT.getCode();
                accountNo = request.getPhone();
                orderMobilePhone = request.getPhone();
                break;
            case UNKNOWN:
                orderCustomerId = 0l;
                orderCustomerCommId = 0l;
                orderCustomerName = null;
                sitePayAccountId = 0l;
                orderPayAccountId = 0l;
//                defaultPayType = 10;
                break;
            case OTHER:
                orderCustomerId = 0l;
                orderCustomerCommId = 0l;
                orderCustomerName = null;
                sitePayAccountId = 0l;
                orderPayAccountId = 0l;
//                defaultPayType = 11;
                break;
            default:
                throw new DcServiceException("请传入正确的结算账户类型");
        }
        CloudChargeVo res = new CloudChargeVo();
        res.setOrderCustomerId(orderCustomerId)
            .setOrderCustomerCommId(orderCustomerCommId)
            .setOrderCustomerName(orderCustomerName)
            .setSiteGids(siteGids)
            .setSitePayAccountId(sitePayAccountId)
            .setOrderPayAccountId(orderPayAccountId)
            .setDefaultPayType(payType.getCode())
            .setPayType(payType)
            .setAccountNo(accountNo)
            .setOrderMobilePhone(orderMobilePhone);
        return res;
    }

    private CloudChargeVo getCloudChargeInfo(Long chargePayAccountId,
        String siteId,
        PayAccountType payType) {
        Long orderCustomerId = null;
        Long orderCustomerCommId = null;
        String orderCustomerName = null;
        Long orderPayAccountId = null;
        List<String> siteGids = null;
        String accountNo = null;
        String orderMobilePhone = null;
        switch (payType.getCode()) {
            case 1:
                ObjectResponse<UserVo> userVoObjectResponse = userFeignClient.findInfoByUid(
                    chargePayAccountId, null, null);
                FeignResponseValidate.check(userVoObjectResponse);
                UserVo vo = userVoObjectResponse.getData();
                IotAssert.isTrue(NumberUtils.equals(vo.getStatus(), 10001),
                    "找不到有效的个人客户，请重新配置结算账户");
                orderCustomerId = chargePayAccountId;
                orderCustomerCommId = vo.getCommId();
                orderCustomerName = vo.getUsername();
                orderPayAccountId = vo.getCommId();
                accountNo = vo.getPhone();
                orderMobilePhone = vo.getPhone();
                break;
            case 2:
                // TODO: 2020/3/16 校验 corpUserId
                ObjectResponse<RBlocUserVo> rBlocUserListResponse = userFeignClient.findRBlocUserVoById(
                    chargePayAccountId);
                FeignResponseValidate.check(rBlocUserListResponse);
                RBlocUserVo rBlocUserVo = rBlocUserListResponse.getData();
                IotAssert.isTrue(NumberUtils.equals(rBlocUserVo.getStatus(), 1),
                    "该企业客户已被禁用，请重新配置结算账户");
                siteGids = super.checkCorpGidsAuthority(rBlocUserVo.getBlocUserId(), siteId);
                orderCustomerId = rBlocUserVo.getUserId();
                orderCustomerCommId = rBlocUserVo.getCommId();
                orderCustomerName = rBlocUserVo.getUserName();
                orderPayAccountId = chargePayAccountId;
                accountNo = rBlocUserVo.getPhone();
                orderMobilePhone = rBlocUserVo.getPhone();
                break;
            case 3:
                // TODO: 2020/3/16 校验commId
                ObjectResponse<CommCusRef> commCusRefObjectResponse = userFeignClient.merFindById(
                    chargePayAccountId);
                FeignResponseValidate.check(commCusRefObjectResponse);
                CommCusRef ref = commCusRefObjectResponse.getData();
                IotAssert.isTrue(NumberUtils.equals(ref.getEnable(), 1),
                    "商户会员已停用，请重新配置结算账户");
                orderCustomerId = ref.getUserId();
                orderCustomerCommId = ref.getUserCommId();
                orderCustomerName = ref.getUserName();
                orderPayAccountId = ref.getCommId();
                accountNo = ref.getUserPhone();
                orderMobilePhone = ref.getUserPhone();
                break;
            default:
                log.info("场站后台充电-扣款账户默认扣款类型异常, defaultPayType= {}", payType);
                return null;
        }
        CloudChargeVo res = new CloudChargeVo();
        res.setOrderCustomerId(orderCustomerId)
            .setOrderCustomerCommId(orderCustomerCommId)
            .setOrderCustomerName(orderCustomerName)
            .setOrderPayAccountId(orderPayAccountId)
            .setSiteGids(siteGids)
            .setAccountNo(accountNo)
            .setOrderMobilePhone(orderMobilePhone);
        return res;
    }

    @Override
    public Optional<Map<String, String>> checkStopingQueue(List<StopChargerRequest> stopRequest) {
        // do nothing
        return Optional.empty();
    }
}
