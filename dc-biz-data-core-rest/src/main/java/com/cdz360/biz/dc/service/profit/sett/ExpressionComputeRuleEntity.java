package com.cdz360.biz.dc.service.profit.sett;

import java.util.Map;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

@Data
@Accessors(chain = true)
@NoArgsConstructor
public class ExpressionComputeRuleEntity<T> {

    private Map<String, Object> variables;
    private String expression;
    private Class<T> clazz;

    public ExpressionComputeRuleEntity(Class<T> clazz) {
        this.clazz = clazz;
    }

    public ExpressionComputeRuleEntity(String expression, Class<T> clazz) {
        this.expression = expression;
        this.clazz = clazz;
    }

    public ExpressionComputeRuleEntity(
        String expression, Map<String, Object> variables, Class<T> clazz) {
        this.expression = expression;
        this.variables = variables;
        this.clazz = clazz;
    }

    public T compute() {
        ExpressionParser parser = new SpelExpressionParser();
        Expression expression = parser.parseExpression(this.expression);
        StandardEvaluationContext context = new StandardEvaluationContext();
        this.variables.forEach(context::setVariable);
        return expression.getValue(context, this.clazz);
    }
}
