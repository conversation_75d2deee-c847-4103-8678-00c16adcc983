package com.cdz360.biz.dc.domain.vo;

import com.cdz360.base.model.charge.type.SiteDynamicPowerType;
import com.cdz360.biz.model.trading.site.vo.SiteChargePriceVo;
import com.cdz360.biz.model.trading.site.vo.SiteTemplateVo;
import com.chargerlinkcar.framework.common.domain.SiteOverTimeParkDTO;
import com.chargerlinkcar.framework.common.domain.SitePersonaliseDTO;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


@Data
@Accessors(chain = true)
public class HlhtSiteInfoVo {
    private String siteId;
    private String siteNo;
    private String name;
    private Integer siteType;
    private String siteTypeDesc;
    private String province;
    private String city;
    private String area;
    private String areaName;
    private String address;
    private Integer acPlugNum;
    private Integer dcPlugNum;
    private Integer parkFeeType;
    private String parkFeeDesc;
    private String openHourDesc;
    private String phone;
    private String contactsPhone;
    private int status;
    private BigDecimal lon;
    private BigDecimal lat;

    // 场站存在多个计费模式 用siteChargePriceVoList替换
    private Long priceCode;
    private SiteDynamicPowerType dyPow;
    private Date onlineDate;
    private List<String> picList;

    private List<SiteTemplateVo> siteTemplateVoList;

    private Boolean parkTimeoutFee;

    private SitePersonaliseDTO sitePersonaliseDTO;

    private SiteOverTimeParkDTO siteOverTimeParkDTO;


}
