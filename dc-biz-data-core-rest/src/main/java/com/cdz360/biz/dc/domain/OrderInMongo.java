package com.cdz360.biz.dc.domain;

import com.cdz360.base.model.base.type.SupplyType;
import com.chargerlinkcar.framework.common.domain.OrderInMongoBase;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.MongoId;

import java.math.BigDecimal;

/**
 * OrderInMongo
 *
 * @since 2/11/2020 2:07 PM
 * <AUTHOR>
 */
@Document(collection = "order_info_in_time")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class OrderInMongo extends OrderInMongoBase {

    private static final long serialVersionUID = 2305730201856288762L;
}