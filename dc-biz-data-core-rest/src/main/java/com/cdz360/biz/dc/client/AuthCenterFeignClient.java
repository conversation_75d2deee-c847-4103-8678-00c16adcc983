package com.cdz360.biz.dc.client;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.auth.sys.param.AccRelativeParam;
import com.cdz360.biz.auth.sys.vo.AccRelativeVo;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.message.po.MessagePo;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletRequest;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Created by zengkq on 2018/11/25.
 */
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_AUTH, fallbackFactory = HystrixAuthCenterClientFactory.class)
public interface AuthCenterFeignClient {
    /**
     * 根据id集合查询用户信息
     *
     * @param idList
     */
    @PostMapping(value = "/data/users/querySysUserByIds")
    ListResponse<SysUserVo> querySysUserByIds(@RequestBody List<Long> idList);

    /**
     * 按名称查询
     * @param name 姓名
     * @param accurateQuery 是否精确查询
     * @return
     */
    @GetMapping(value = "/data/users/findByName")
    ListResponse<SysUserVo> findByName(@RequestParam(name = "name") String name,
                                       @RequestParam(name = "accurateQuery", defaultValue = "true")
                                               Boolean accurateQuery);

    @PostMapping(value = "/api/msg/addMessage")
    ObjectResponse addMessage(@RequestParam(value = "token", required = false) String token,
                              @RequestBody MessagePo message);

    @PostMapping("/api/accRelative/list")
    ListResponse<AccRelativeVo> getVoList(@RequestBody AccRelativeParam param);

    // 通过企业ID获取所属场站组
    @GetMapping(value = "/api/corp/getGidsById")
    ListResponse<String> getGidsById(@RequestParam(value = "corpId") Long corpId);


    //    @Operation(summary = "获取国充场站的gcType值")
    @GetMapping("/data/roles/getGcTypes")
    ListResponse<Integer> getGcTypes();

    @GetMapping("/api/corp/getCorp")
    ObjectResponse<CorpPo> getCorp(@RequestParam(value = "corpId") Long corpId);

    @Operation(summary = "更新企业客户是否全额开票")
    @GetMapping("/api/corp/updateCorpFullInvoice")
    BaseResponse updateCorpFullInvoice(
        @RequestParam(value = "corpId") Long corpId,
        @RequestParam(value = "fullInvoicing") Boolean fullInvoicing);
}
