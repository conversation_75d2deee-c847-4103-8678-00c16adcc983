package com.cdz360.biz.dc;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.exception.DcException;
import com.chargerlinkcar.framework.common.utils.LoggerHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@ControllerAdvice(annotations = RestController.class)
public class DataCoreExceptionHandler {
    //private final Logger logger = LoggerFactory.getLogger(DataCoreExceptionHandler.class);

    @ExceptionHandler({DcException.class})
    @ResponseBody
    public BaseResponse handleDcException(DcException ex) {
        LoggerHelper.logException(log, ex);
        BaseResponse result = new BaseResponse();
        result.setStatus(ex.getStatus());
        result.setError(ex.getMessage());
        return result;
    }

    @ExceptionHandler({IllegalArgumentException.class})
    @ResponseBody
    public BaseResponse handleAssertException(IllegalArgumentException ex) {
        log.warn(ex.getMessage(), ex);
        BaseResponse result = new BaseResponse();
        result.setStatus(DcConstants.KEY_RES_CODE_ARGUMENT_ERROR);
        result.setError(ex.getMessage());
        return result;
    }

    @ExceptionHandler
    @ResponseBody
    public BaseResponse handle(Exception ex) {
        log.error(ex.getMessage(), ex);
        BaseResponse result = new BaseResponse();
        result.setStatus(DcConstants.KEY_RES_CODE_UNKNOWN_ERROR);
        result.setError("系统错误");
        if (ex instanceof IllegalArgumentException) {
            // Assert 失败的异常
            result.setStatus(2000);
            result.setError(ex.getMessage());
        } else {
            log.error("error = {}", ex.getMessage(), ex);
        }
        // TODO: 需要按异常类型做处理, 返回不同的status和error

        return result;
    }
}
