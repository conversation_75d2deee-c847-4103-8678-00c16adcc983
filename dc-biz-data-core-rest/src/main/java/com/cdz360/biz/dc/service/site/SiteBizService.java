package com.cdz360.biz.dc.service.site;

import com.cdz360.biz.utils.feign.iot.MeterFeignClient;
import org.springframework.data.util.Pair;
import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.param.SortParam;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.model.base.type.ChargeOrderStatus;
import com.cdz360.base.model.base.type.ChargePriceCategory;
import com.cdz360.base.model.base.type.EvseBizStatus;
import com.cdz360.base.model.base.type.OrderType;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PlugStatus;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.charge.vo.ChargePriceItem;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.client.UserFeignClient;
import com.cdz360.biz.dc.domain.SendSiteBi;
import com.cdz360.biz.dc.domain.SendSiteBiParamParse;
import com.cdz360.biz.dc.domain.SiteInMongoPo;
import com.cdz360.biz.dc.domain.hlht.HlhtSyncSiteDto;
import com.cdz360.biz.dc.domain.vo.HlhtSiteInfoVo;
import com.cdz360.biz.dc.domain.vo.SiteInMongoVo;
import com.cdz360.biz.dc.repository.SiteMongoDataRepository;
import com.cdz360.biz.dc.service.BsBoxBizService;
import com.cdz360.biz.dc.service.ChargerOrderBizService;
import com.cdz360.biz.dc.service.MgmWebCharging;
import com.cdz360.biz.dc.service.PriceSchemaBizService;
import com.cdz360.biz.dc.service.SendErpSiteService;
import com.cdz360.biz.dc.service.comm.CommPayChannelCfgService;
import com.cdz360.biz.dc.service.prerun.PrerunService;
import com.cdz360.biz.ds.trading.ro.corp.ds.CorpRoDs;
import com.cdz360.biz.ds.trading.ro.corp.ds.CorpUserRoDs;
import com.cdz360.biz.ds.trading.ro.corp.ds.UserSyncRoDs;
import com.cdz360.biz.ds.trading.ro.coupon.ds.ActivityCouponRuleRoDs;
import com.cdz360.biz.ds.trading.ro.coupon.ds.ActivityRoDs;
import com.cdz360.biz.ds.trading.ro.coupon.ds.CouponRoDs;
import com.cdz360.biz.ds.trading.ro.geo.ds.GeoRoDs;
import com.cdz360.biz.ds.trading.ro.iot.ds.BsChargerRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderPayRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.CommRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.PriceItemRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.PriceSchemaRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteAssetsRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteChargeJobPlugRoDS;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteGroupRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteLimitSocRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteOvertimeParkDivisionRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteOvertimeParkSettingRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteTemplateRoDs;
import com.cdz360.biz.ds.trading.ro.soc.ds.SiteSocCfgRoDs;
import com.cdz360.biz.ds.trading.ro.soc.ds.SiteSocMainStrategyRoDs;
import com.cdz360.biz.ds.trading.ro.soc.ds.SiteSocStrategyRoDs;
import com.cdz360.biz.ds.trading.ro.soc.ds.SocStrategyRoDs;
import com.cdz360.biz.ds.trading.ro.soc.ds.UserSocStrategyRoDs;
import com.cdz360.biz.ds.trading.rw.iot.ds.BsBoxRwDs;
import com.cdz360.biz.ds.trading.rw.iot.ds.BsChargerRwDs;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.BiErpOrderRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.BiErpSiteRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.PriceSchemaRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteAssetsRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteChargeJobPlugRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteChargeJobRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteDailyDataRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteDefaultSettingRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteGroupSiteRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteLimitSocRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteOvertimeParkDivisionRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteOvertimeParkSettingRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteTemplateRwDs;
import com.cdz360.biz.ds.trading.rw.soc.ds.SiteSocCfgRwDs;
import com.cdz360.biz.ds.trading.rw.soc.ds.SiteSocMainStrategyRwDs;
import com.cdz360.biz.ds.trading.rw.soc.ds.SiteSocStrategyRwDs;
import com.cdz360.biz.ds.trading.rw.soc.ds.SocStrategyRwDs;
import com.cdz360.biz.ds.trading.rw.soc.ds.UserSocStrategyRwDs;
import com.cdz360.biz.ds.trading.rw.soc.ds.UserSocTimeRwDs;
import com.cdz360.biz.ess.model.param.ListEssParam;
import com.cdz360.biz.model.common.param.BaseListParam;
import com.cdz360.biz.model.cus.score.dto.UserScoreSettingLevelSiteGidDto;
import com.cdz360.biz.model.cus.score.param.SearchScoreLogParam;
import com.cdz360.biz.model.cus.score.type.DiscountType;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.discount.param.DiscountServiceParam;
import com.cdz360.biz.model.discount.vo.DiscountChargeInfo;
import com.cdz360.biz.model.discount.vo.SiteDiscountVo;
import com.cdz360.biz.model.finance.type.BizType;
import com.cdz360.biz.model.geo.po.DistrictPo;
import com.cdz360.biz.model.iot.param.ListGtiParam;
import com.cdz360.biz.model.iot.vo.ChargeV2;
import com.cdz360.biz.model.iot.vo.GtiVo;
import com.cdz360.biz.model.merchant.dto.CommercialSiteDto;
import com.cdz360.biz.model.merchant.dto.SiteTinyDto;
import com.cdz360.biz.model.merchant.param.CommcialSiteParam;
import com.cdz360.biz.model.merchant.param.ListCommercialParam;
import com.cdz360.biz.model.site.dto.OaSiteMonthDataDto;
import com.cdz360.biz.model.site.dto.SiteGeoDto;
import com.cdz360.biz.model.site.param.AddSiteParam;
import com.cdz360.biz.model.site.param.ListSiteBaseParam;
import com.cdz360.biz.model.site.param.UpdateSiteParam;
import com.cdz360.biz.model.site.po.PriceItemPo;
import com.cdz360.biz.model.site.po.PriceTemplatePo;
import com.cdz360.biz.model.site.type.SiteCategory;
import com.cdz360.biz.model.site.type.SiteStatus;
import com.cdz360.biz.model.sys.constant.SiteGroupType;
import com.cdz360.biz.model.sys.param.ListSiteGroupParam;
import com.cdz360.biz.model.sys.po.SiteGroupPo;
import com.cdz360.biz.model.trading.bi.param.ListBiErpSiteParam;
import com.cdz360.biz.model.trading.bi.po.BiErpOrderPo;
import com.cdz360.biz.model.trading.bi.po.BiErpSitePo;
import com.cdz360.biz.model.trading.bi.type.ErpSiteStatus;
import com.cdz360.biz.model.trading.corp.po.CorpPo;
import com.cdz360.biz.model.trading.coupon.po.ActivityCouponRulePo;
import com.cdz360.biz.model.trading.coupon.type.ActivityStatusType;
import com.cdz360.biz.model.trading.coupon.type.ActivityType;
import com.cdz360.biz.model.trading.coupon.type.CouponSendType;
import com.cdz360.biz.model.trading.coupon.vo.ActivityRunningVo;
import com.cdz360.biz.model.trading.coupon.vo.CouponVoEx;
import com.cdz360.biz.model.trading.cus.vo.CusRepVo;
import com.cdz360.biz.model.trading.ess.vo.EssVo;
import com.cdz360.biz.model.trading.hlht.dto.CecPolicyInfo;
import com.cdz360.biz.model.trading.hlht.dto.CecQueryQeuipBusinessPolicyResult;
import com.cdz360.biz.model.trading.hlht.dto.DataSyncOvertimeParkFeeFlag;
import com.cdz360.biz.model.trading.hlht.po.SiteOutPo;
import com.cdz360.biz.model.trading.iot.po.BsBoxPo;
import com.cdz360.biz.model.trading.iot.po.BsChargerPo;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.po.CardPo;
import com.cdz360.biz.model.trading.order.po.ChargerOrderPayPo;
import com.cdz360.biz.model.trading.site.dto.SiteProfitInfo;
import com.cdz360.biz.model.trading.site.dto.SiteSimpleDto;
import com.cdz360.biz.model.trading.site.param.AddPriceSchemaParam;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.param.ListSiteProfitParam;
import com.cdz360.biz.model.trading.site.param.UpdatePriceSchemaParam;
import com.cdz360.biz.model.trading.site.po.BiSiteMonthPo;
import com.cdz360.biz.model.trading.site.po.CommPo;
import com.cdz360.biz.model.trading.site.po.SiteChargeJobPlugPo;
import com.cdz360.biz.model.trading.site.po.SiteChargeJobPo;
import com.cdz360.biz.model.trading.site.po.SiteDefaultSettingPo;
import com.cdz360.biz.model.trading.site.po.SiteGroupRefPo;
import com.cdz360.biz.model.trading.site.po.SiteOvertimeParkDivisionPo;
import com.cdz360.biz.model.trading.site.po.SiteOvertimeParkSettingPo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.site.po.SiteTemplatePo;
import com.cdz360.biz.model.trading.site.type.OvertimeParkingChargePartType;
import com.cdz360.biz.model.trading.site.type.OvertimeParkingLimitType;
import com.cdz360.biz.model.trading.site.type.PriceSchemaConstant;
import com.cdz360.biz.model.trading.site.vo.ImageVo;
import com.cdz360.biz.model.trading.site.vo.MoveCorpNoCardList;
import com.cdz360.biz.model.trading.site.vo.MoveCorpNoCardVo;
import com.cdz360.biz.model.trading.site.vo.SiteChargePriceVo;
import com.cdz360.biz.model.trading.site.vo.SiteConfStartList;
import com.cdz360.biz.model.trading.site.vo.SiteNoVo;
import com.cdz360.biz.model.trading.site.vo.SiteOrderStatsVo;
import com.cdz360.biz.model.trading.site.vo.SitePriceVo;
import com.cdz360.biz.model.trading.site.vo.SiteQrCodeVo;
import com.cdz360.biz.model.trading.site.vo.SiteTemplateVo;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import com.cdz360.biz.model.trading.site.vo.SiteWithConfVo;
import com.cdz360.biz.model.trading.soc.param.SocStrategyDict;
import com.cdz360.biz.model.trading.soc.po.SiteSocMainStrategyPo;
import com.cdz360.biz.model.trading.soc.po.UserSocTimePo;
import com.cdz360.biz.model.trading.soc.vo.MoveCorpUserSocStrategyList;
import com.cdz360.biz.model.trading.soc.vo.MoveCorpUserSocStrategyVo;
import com.cdz360.biz.model.trading.soc.vo.SiteSocMainStrategyVo;
import com.cdz360.biz.utils.feign.iot.DeviceFeignClient;
import com.cdz360.biz.utils.feign.iot.GtiFeignClient;
import com.cdz360.data.cache.RedisIotReadService;
import com.cdz360.data.cache.RedisIotRwService;
import com.cdz360.data.sync.model.Site;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.constant.OrderStatus;
import com.chargerlinkcar.framework.common.domain.Dict;
import com.chargerlinkcar.framework.common.domain.NoCardPayAccountInfo;
import com.chargerlinkcar.framework.common.domain.PlugStatusCountDto;
import com.chargerlinkcar.framework.common.domain.PointPo;
import com.chargerlinkcar.framework.common.domain.SiteDetailInfoVo;
import com.chargerlinkcar.framework.common.domain.SiteOverTimeParkDTO;
import com.chargerlinkcar.framework.common.domain.SitePersonaliseDTO;
import com.chargerlinkcar.framework.common.domain.SiteSocLimitDto;
import com.chargerlinkcar.framework.common.domain.request.ListPlugRequest;
import com.chargerlinkcar.framework.common.domain.request.SiteGeoListRequest;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrder;
import com.chargerlinkcar.framework.common.domain.vo.CloudChargeVo;
import com.chargerlinkcar.framework.common.domain.vo.CommCusRef;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUserVo;
import com.chargerlinkcar.framework.common.domain.vo.SiteDebitAccountVo;
import com.chargerlinkcar.framework.common.domain.vo.UserPropVO;
import com.chargerlinkcar.framework.common.domain.vo.UserVo;
import com.chargerlinkcar.framework.common.feign.AuthCenterFeignClient;
import com.chargerlinkcar.framework.common.feign.CommercialFeignClient;
import com.chargerlinkcar.framework.common.feign.DictFeignClient;
import com.chargerlinkcar.framework.common.feign.IotBizClient;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.feign.OpenHlhtFeignClient;
import com.chargerlinkcar.framework.common.service.AsyncCusBalanceService;
import com.chargerlinkcar.framework.common.service.BiErpSiteSeqNoGenerator;
import com.chargerlinkcar.framework.common.service.DcCusBalanceService;
import com.chargerlinkcar.framework.common.service.DcEventPublisherService;
import com.chargerlinkcar.framework.common.service.discount.DiscountDealWithService;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.MapUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.UUID;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.event.Level;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.geo.Box;
import org.springframework.data.geo.Metrics;
import org.springframework.data.geo.Point;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.NearQuery;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;


@Slf4j
@Service
public class SiteBizService {

    private static final Integer LIMIT_MAX_SIZE = 100;
    // 以下常量命名和前端保持一致
    private static final Integer SITE_SETTING_1 = 1; // 充电时冻结金额设置
    private static final Integer SITE_SETTING_2 = 2; // 停充超时充电控制
    private static final Integer SITE_SETTING_3 = 3; // 后台启动设置
    private static final Integer SITE_SETTING_4 = 4; // 用户充电限制
    private static final Integer SITE_SETTING_5 = 5; // 无卡启动订单结算账户管理
    private static final Integer SITE_SETTING_6 = 6; // 场站当前设置为免停车费，禁用停车减免
    private static final List<Integer> FREE_PARK_LIST = List.of(0, 2);
    //默认设置冻结金额
    private static BigDecimal FROZEN_MONEY = BigDecimal.valueOf(20);

    @Autowired
    private SiteDailyDataRwDs siteDailyDataRwDs;

    @Autowired
    private SiteOvertimeParkSettingRwDs siteOvertimeParkSettingRwDs;

    @Autowired
    private SiteOvertimeParkDivisionRwDs siteOvertimeParkDivisionRwDs;

    @Autowired
    private SiteOvertimeParkSettingRoDs siteOvertimeParkSettingRoDs;

    @Autowired
    private SiteOvertimeParkDivisionRoDs siteOvertimeParkDivisionRoDs;

    @Autowired
    private SiteRoDs siteRoDs;
    @Autowired
    private SiteRwDs siteRwDs;
    @Autowired
    private SiteChargeJobRwDs siteChargeJobRwDs;
    @Autowired
    private SiteChargeJobPlugRoDS siteChargeJobPlugRoDS;
    @Autowired
    private SiteChargeJobPlugRwDs siteChargeJobPlugRwDs;
    @Autowired
    private SiteDefaultSettingRwDs siteDefaultSettingRwDs;
    @Autowired
    private SiteSocStrategyRoDs siteSocStrategyRoDs;
    @Autowired
    private BiErpOrderRwDs biErpOrderRwDs;
    @Autowired
    private ChargerOrderBizService chargerOrderBizService;
    @Autowired
    private MgmWebCharging mgmWebCharging;
    @Autowired
    private ChargerOrderRwDs chargerOrderRwDs;
    @Autowired
    private BiErpSiteRwDs biErpSiteRwDs;
    //    @Autowired
//    private OrderFeignClient orderFeignClient;
    @Autowired
    private SendErpSiteService sendErpSiteService;
    @Autowired
    private BiErpSiteSeqNoGenerator biErpSiteSeqNoGenerator;
    @Autowired
    private PriceSchemaBizService priceSchemaBizService;
    @Autowired
//    private TRCommercialService commercialService;
    private CommRoDs commRoDs;

    @Autowired
    private PriceSchemaRoDs priceTemplateRoDs;

    @Autowired
    private CommercialFeignClient commercialFeignClient;
    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private com.cdz360.biz.utils.feign.user.UserFeignClient reactorUserFeignClient;

    @Autowired
    private DictFeignClient dictFeignClient;
    @Autowired
    private GeoRoDs geoRoDs;
    @Autowired
    private SiteMongoDataRepository siteMongoDataRepository;
    @Autowired
    private RedisIotReadService redisIotReadService;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMmgClient;
    @Autowired
    private IotBizClient iotBizClient;
    @Autowired
    private DcEventPublisherService dcEventPublisherService;
    @Deprecated
    @Autowired
    private SiteLimitSocRoDs siteLimitSocRoDs;
    @Deprecated
    @Autowired
    private SiteLimitSocRwDs siteLimitSocRwDs;

    @Autowired
    private SiteSocCfgRwDs siteSocCfgRwDs;
    @Autowired
    private SiteSocStrategyRwDs siteSocStrategyRwDs;
    @Autowired
    private SiteSocMainStrategyRwDs siteSocMainStrategyRwDs;
    @Autowired
    private SiteSocMainStrategyRoDs siteSocMainStrategyRoDs;
    @Autowired
    private UserSocStrategyRwDs userSocStrategyRwDs;
    @Autowired
    private SocStrategyRwDs socStrategyRwDs;
    @Autowired
    private UserSocTimeRwDs userSocTimeRwDs;

    @Autowired
    private SiteSocCfgRoDs siteSocCfgRoDs;
    //    @Autowired
//    private SiteSocStrategyRoDs siteSocStrategyRoDs;
    @Autowired
    private SocStrategyRoDs socStrategyRoDs;

    @Autowired
    private UserSyncRoDs userSyncRoDs;


    @Autowired
    private BsBoxBizService bsBoxBizService;
//    @Autowired
//    private UserSocTimeRoDs userSocTimeRoDs;

    @Autowired
    private ChargerOrderRoDs chargerOrderRoDs;

    @Autowired
    private ChargerOrderPayRoDs chargerOrderPayRoDs;

    @Autowired
    private DcCusBalanceService dcCusBalanceService;

    @Autowired
    private AsyncCusBalanceService asyncCusBalanceService;

    @Autowired
    private OpenHlhtFeignClient openHlhtFeignClient;

    @Autowired
    private DeviceFeignClient deviceFeignClient;

    @Autowired
    private GtiFeignClient gtiFeignClient;

//    @Autowired
//    private CorpRoDs corpRoDs;

    @Autowired
    private CommPayChannelCfgService commPayChannelCfgService;

    @Autowired
    private UserSocStrategyRoDs userSocStrategyRoDs;

    @Autowired
    private DiscountDealWithService discountDealWithService;

    @Autowired
    private CorpUserRoDs corpUserRoDs;

    @Autowired
    private CorpRoDs corpRoDs;

    @Autowired
    private PriceSchemaRwDs priceSchemaRwDs;

    @Autowired
    private SiteAssetsRwDs siteAssetsRwDs;

    @Autowired
    private SiteAssetsRoDs siteAssetsRoDs;

    @Autowired
    private PriceItemRoDs priceItemRoDs;

    @Autowired
    private PrerunService prerunService;

    @Autowired
    private BsChargerRoDs bsChargerRoDs;

    @Autowired
    private SiteGroupRoDs siteGroupRoDs;

    @Autowired
    private CouponRoDs couponRoDs;

    @Autowired
    private ActivityRoDs activityRoDs;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private SiteTemplateRwDs siteTemplateRwDs;

    @Autowired
    private SiteTemplateRoDs siteTemplateRoDs;

    @Autowired
    private SiteGroupSiteRwDs siteGroupSiteRwDs;

    @Autowired
    private BsChargerRwDs bsChargerRwDs;

    @Autowired
    private BsBoxRwDs bsBoxRwDs;

    @Autowired
    private RedisIotRwService redisIotRwService;

    @Autowired
    private ActivityCouponRuleRoDs activityCouponRuleRoDs;

    @Autowired
    private MeterFeignClient meterFeignClient;

    private static String getSaveId(String json) {
        return JsonUtils.fromJson(json)
            .get("Result")
            .get("Id")
            .asText();
    }

    private static String getSeqNo(String json) {
        return JsonUtils.fromJson(json)
            .get("Result")
            .get("Number")
            .asText();
    }

    public SitePo getSiteById(String siteId) {
        return this.siteRoDs.getSite(siteId);
    }

    public List<String> getSiteByBillNoList(List<String> billNoList) {
        return this.siteRoDs.getSiteByBillNoList(billNoList);
    }

    public ListResponse<String> getExistingOperateCorpCodes() {
        List<String> data = siteRoDs.getExistingOperateCorpCodes();
        return RestUtils.buildListResponse(data);
    }


    public Site getSiteInfo(String siteId) {
        log.info("siteId: {}", siteId);
        SitePo po = siteRoDs.getSite(siteId);
        IotAssert.isTrue(po != null, "未查询到此站点");
        //ObjectResponse<SiteDetailInfoVo> response = this.getSi deviceBussnessFeignClient.getSiteDetailBySiteId(siteId, null, null);

        //FeignResponseValidate.check(response);
        //SiteDetailInfoVo detailInfoVo = response.getData();
        //log.info("detailInfoVo: {}", JsonUtils.toJsonString(detailInfoVo));
        Dict dict = new Dict();
        dict.setType("siteType");
        dict.setValue(String.valueOf(po.getType()));
        ListResponse<Dict> dictListResponse = dictFeignClient.queryPage(dict);

        FeignResponseValidate.check(dictListResponse);
        List<Dict> result = dictListResponse.getData();
        log.info("result: {}", JsonUtils.toJsonString(result));

        Site res = new Site();
        res.setSiteId(po.getId())
            .setName(po.getSiteName())
            .setCommId(po.getOperateId())
            .setProvince(String.valueOf(po.getProvince()))
            .setCity(String.valueOf(po.getCity()))
            .setArea(String.valueOf(po.getArea()))
            .setAddress(po.getAddress())
            .setStatus(po.getStatus())
            .setPriceCode(po.getTemplateId())
            .setLon(po.getLongitude())
            .setLat(po.getLatitude())
            .setSiteType(po.getType())
            .setCommPhone(po.getContactsPhone())
            .setPhone(po.getPhone());
        if (CollectionUtils.isNotEmpty(result)) {
            res.setSiteTypeDesc(result.get(0).getLabel());
        }
        log.info("res: {}", JsonUtils.toJsonString(res));
        return res;
        // return new ObjectResponse<Site>(res);
    }

    /**
     * 根据场站ID获取在线卡、离线卡、VIN码数量
     */
    public ListResponse<CardPo> getCardAmountBySiteId(ListSiteBaseParam param) {
        return this.siteRoDs.getCardAmountBySiteId(param);
    }

    @Transactional
    public SitePo addSite(AddSiteParam param) {
        log.info("收到新增站点请求 param = {}", param);
        if (param.getTopCommId() == null) {
            throw new DcArgumentException("参数错误,集团商户ID不能为空");
        }

        //站点自定义编号判断
        if (StringUtils.isNotBlank(param.getSiteNo())) {
            if (param.getSiteNo().length() > 30) {
                throw new DcArgumentException("场站编号长度不能超过30个字符");
            }

            String patten = "^[A-Za-z0-9-_\\/]{1,30}$";
            boolean isMatch = Pattern.matches(patten, param.getSiteNo());
            if (isMatch == false) {
                throw new DcArgumentException("不可输入/,-,_以外的其他特殊字符");
            }

            SiteNoVo siteNoInfo = this.siteRoDs.getSiteInfoBySiteNo(param.getTopCommId(),
                param.getSiteNo());
            if (null != siteNoInfo) {
                throw new DcServiceException("站点编号已存在");
            }
        }

        SiteTinyDto nameExist = this.siteRoDs.getSiteByName(param.getTopCommId(),
            param.getSiteName());
        if (nameExist != null) {
            throw new DcServiceException("站点名称重复");
        }

        // 更新场站商户配置渠道信息
        if (null != param.getOperateId()) {
            commPayChannelCfgService.updateSiteCommPayChannelCfg(
                param.getOperateId(),
                param.getPayChannels());
        }

        // 组织站点新增数据
        SitePo entity = SiteConvert.generateSiteEntity(param);
        // 站点状态
        entity.setStatus(SiteStatus.OPENING.getCode());
        //充电冻结金额
        entity.setFrozenAmount(FROZEN_MONEY);
        // 站点类型
        if (param.getType() == null) {
            //类型未知
            entity.setType(0);
        }
        // 是否停车收费
        if (param.getPark() == null) {
            entity.setPark(0);
        }
        // 是否需要预约
        if (param.getAppoint() == null) {
            entity.setAppoint(0);
        }
        // 是否需要预约
        if (param.getScope() == null) {
            entity.setScope(0);
        }
        // 上线时间
        if (param.getOnlineDate() != null) {
            entity.setOnlineDate(param.getOnlineDate());
        }
        // 站点收费说明
        if (param.getFeeDescription() != null) {
            entity.setFeeDescription(param.getFeeDescription());
        }
        // 站点收费范围 最小
        if (param.getFeeMin() != null) {
            entity.setFeeMin(param.getFeeMin() != null ? param.getFeeMin().longValue() : 0L);
        }
        // 站点收费范围 最大
        if (param.getFeeMax() != null) {
            entity.setFeeMax(param.getFeeMax() != null ? param.getFeeMax().longValue() : 0L);
        }
        // 禁用的客户端编号
        if (param.getForbiddenClient() != null) {
            entity.setForbiddenClient(param.getForbiddenClient());
        }
        if (param.getIsHidden() != null) {
            entity.setIsHidden(param.getIsHidden());
        }
        //站点自定义编号
        entity.setSiteNo(param.getSiteNo());

        // 创建时间
        entity.setCreateTime(Calendar.getInstance().getTime());
        entity.setUpdateTime(Calendar.getInstance().getTime());
        // 保存经纬度对应的geohash,用于计算附近的站点
        //entity.setGeohash(GeohashUtils.encodeLatLon(request.getLatitude(), request.getLongitude(), 10));

        ObjectResponse<Commercial> topCommRes = this.commercialFeignClient.getCommercial(
            param.getTopCommId());
        FeignResponseValidate.check(topCommRes);
        Commercial topComm = topCommRes.getData();
        //if (topComm != null) {
        entity.setTopCommId(param.getTopCommId());
        //}

        log.info("站点信息写入数据库[entity={}]", JsonUtils.toJsonString(entity));

        if (param.getServiceInfo() != null) {
            entity.setServiceInfo(param.getServiceInfo());
        }

        entity.setCategory(param.getSiteCategoryList());

        if (CollectionUtils.isNotEmpty(param.getSiteCategoryList()) &&
            param.getSiteCategoryList().contains(SiteCategory.PV)) {
            entity.setPvIncomeTemplateId(param.getPvIncomeTemplateId());

            entity.setPvInstalledCapacity(param.getPvInstalledCapacity());
            entity.setOnGridDate(param.getOnGridDate());
            entity.setOnGridVoltageLevel(param.getOnGridVoltageLevel());
        }

        if (CollectionUtils.isNotEmpty(param.getSiteCategoryList()) &&
            param.getSiteCategoryList().contains(SiteCategory.ESS)) {
            entity.setEssInPriceId(param.getEssInPriceId());
            entity.setEssOutPriceId(param.getEssOutPriceId());
            entity.setEssCapacity(param.getEssCapacity());
            entity.setEssPower(param.getEssPower());
        }

        if (param.getGcType() != null) {
            entity.setGcType(param.getGcType());
        }

        if (param.getCityId() != null) {
            entity.setCityId(param.getCityId());
        }

        siteRwDs.addSite(entity);

        // 创建关联数据
        if (CollectionUtils.isNotEmpty(param.getSiteAssets())) {
            param.getSiteAssets().forEach(e -> {
                e.setSiteId(entity.getId());
            });
            siteAssetsRwDs.batchInsert(param.getSiteAssets());
        }

        // 创建场站关联场站组信息
        if (CollectionUtils.isNotEmpty(param.getGidList())) {
            siteGroupSiteRwDs.batchInsertBySiteId(entity.getId(), param.getGidList());
        }

        //新增一条场站通用配置记录
        SiteDefaultSettingPo siteDefultSettingRequest = new SiteDefaultSettingPo();
        siteDefultSettingRequest.setSiteId(entity.getId());
        siteDefultSettingRequest.setChargeId(entity.getTemplateId());
        siteDefultSettingRequest.setCreateTime(new Date());
        //if (topComm != null) {
        siteDefultSettingRequest.setUrl(topComm.getUrl());
        //}
        this.siteDefaultSettingRwDs.addSiteDefaultSetting(siteDefultSettingRequest);
        //把站点模板放到缓存中
        //cacheService.setValue(DBSCacheKeyEnum.MERCHANT_TEMPLATE_INFO,entity.getOperateId().toString(),entity.getTemplateId().toString());
        //调鼎充的微服务，完成iot库场站的记录新增
        //Assert.isTrue(this.insertDcSite(entity) > 0, "远程创建场站失败");
        dcEventPublisherService.publishSiteInfo(entity);
        return entity;
    }


    @Transactional
    public SitePo updateSiteInfo(UpdateSiteParam param) {
        log.info("收到更新站点信息请求 param = {}", param);
        if (StringUtils.isBlank(param.getSiteId())) {
            throw new DcArgumentException("参数错误,场站编号不能为空");
        }
        boolean nameChanged = false;
        SitePo site;
        {
            site = this.siteRoDs.getSite(param.getSiteId());
            if (site == null) {
                throw new DcArgumentException("未找到站点信息");
            }
            if (AppClientType.SASS_MGM_WEB != param.getClientType()
                && !NumberUtils.equals(site.getTopCommId(), param.getTopCommId())) {
                log.warn("site = {}, param = {}", JsonUtils.toJsonString(site), param);
                throw new DcArgumentException("您不能修改这个场站的信息");
            }
            //如果要修改站点名称，先判断名称是否重复
            if (!StringUtils.isBlank(param.getSiteName())) {
                if (site.getSiteName().equals(param.getSiteName())) {
                    //如果站点名称相同，则不需要修改
                    // request.setSiteName(null);
                } else {
                    SiteTinyDto nameExist = this.siteRoDs.getSiteByName(param.getTopCommId(),
                        param.getSiteName());
                    if (nameExist != null) {
                        throw new DcServiceException("站点名称重复");
                    }
                    nameChanged = true;
                }
            }
        }

        //站点自定义编号判断
        if (StringUtils.isNotBlank(param.getSiteNo()) && !StringUtils.equalsIgnoreCase(
            param.getSiteNo(), site.getSiteNo())) {
            String patten = "^[A-Za-z0-9-_\\/]{1,30}$";
            boolean isMatch = Pattern.matches(patten, param.getSiteNo());
            if (isMatch == false) {
                throw new DcServiceException("站点编号格式不正确");
            }
            SiteNoVo siteNoInfo = this.siteRoDs.getSiteInfoBySiteNo(param.getTopCommId(),
                param.getSiteNo());
            if (null != siteNoInfo) {
                throw new DcServiceException("站点编号已存在");
            }
        }

        //  2-更新站点信息
        SitePo update = new SitePo();
        // 站点ID
        update.setId(site.getId());

//
//            //充电冻结金额
//        update.setFrozenAmount(FROZEN_MONEY);

        // 状态追加
        update.setStatus(site.getStatus());
        // 修改时间
        update.setUpdateTime(Calendar.getInstance().getTime());
        site.setUpdateTime(Calendar.getInstance().getTime());
        if (param.getTopCommId() != null) {
            update.setTopCommId(param.getTopCommId());
            site.setTopCommId(param.getTopCommId());
        }
        if (param.getOperateId() != null) {
            update.setOperateId(param.getOperateId());
            site.setOperateId(param.getOperateId());
        }
        if (StringUtils.isNotBlank(param.getOperateName())) {
            update.setOperateName(param.getOperateName());
            site.setOperateName(param.getOperateName());
        }
        if (StringUtils.isNotBlank(param.getOperateCorpCode())) {
            update.setOperateCorpCode(param.getOperateCorpCode());
            site.setOperateCorpCode(param.getOperateCorpCode());
        }
        // 站点名称
        if (!StringUtils.isBlank(param.getSiteName())) {
            update.setSiteName(param.getSiteName());
            site.setSiteName(param.getSiteName());
        }
        // 站点简称
        if (StringUtils.isNotBlank(param.getSiteShortName())) {
            update.setSiteShortName(param.getSiteShortName());
            site.setSiteShortName(param.getSiteShortName());
        }
        // 经度
        if (param.getLongitude() != null) {
            Assert.isTrue(-180 <= param.getLongitude().doubleValue()
                && param.getLongitude().doubleValue() <= 180, "请输入有效经度");
            update.setLongitude(param.getLongitude());
            site.setLongitude(param.getLongitude());
        }
        // 纬度
        if (param.getLatitude() != null) {
            Assert.isTrue(-90 <= param.getLatitude().doubleValue()
                && param.getLatitude().doubleValue() <= 90, "请输入有效纬度");
            update.setLatitude(param.getLatitude());
            site.setLatitude(param.getLatitude());
        }
        if (StringUtils.isNotBlank(param.getTimeZone())) {
            update.setTimeZone(param.getTimeZone());
            site.setTimeZone(param.getTimeZone());
        }
        // 站点地址
        if (!StringUtils.isBlank(param.getAddress())) {
            update.setAddress(param.getAddress());
            site.setAddress(param.getAddress());
        }
        // 省份编码
        if (param.getProvince() != null) {
            update.setProvince(param.getProvince());
            site.setProvince(param.getProvince());
        }
        // 城市编码
        if (param.getCity() != null) {
            update.setCity(param.getCity());
            site.setCity(param.getCity());
        }
        // 区域编码
        if (param.getArea() != null) {
            update.setArea(param.getArea());
            site.setArea(param.getArea());
        }
        // 服务号码
        if (!StringUtils.isBlank(param.getPhone())) {
            update.setPhone(param.getPhone());
            site.setPhone(param.getPhone());
        }
        // 联系人
        if (!StringUtils.isBlank(param.getContacts())) {
            update.setContacts(param.getContacts());
            site.setContacts(param.getContacts());
        }
        // 联系人号码
        if (!StringUtils.isBlank(param.getContactsPhone())) {
            update.setContactsPhone(param.getContactsPhone());
            site.setContactsPhone(param.getContactsPhone());
        }
        // 备注
        if (!StringUtils.isBlank(param.getRemark())) {
            update.setRemark(param.getRemark());
            site.setRemark(param.getRemark());
        }
        // 站点类型**0-未知 1-公共 2-公交 3-物流 4-混合**
        if (param.getType() != null) {
            update.setType(param.getType());
            site.setType(param.getType());
        }
        // 工作日服务时间
        if (!StringUtils.isBlank(param.getServiceWorkdayTime())) {
            update.setServiceWorkdayTime(param.getServiceWorkdayTime());
            site.setServiceWorkdayTime(param.getServiceWorkdayTime());
        }
        // 节假日服务时间
        if (!StringUtils.isBlank(param.getServiceHolidayTime())) {
            update.setServiceHolidayTime(param.getServiceHolidayTime());
            site.setServiceHolidayTime(param.getServiceHolidayTime());
        }
        // 停车是否收费 **0-未知 1-收费 2-免费**
        if (param.getPark() != null) {
            update.setPark(param.getPark());
            site.setPark(param.getPark());
        }
        // 停车费
        if (param.getParkFee() != null) {
            update.setParkFee(param.getParkFee());
            site.setParkFee(param.getParkFee());
        }
        // 是否需要预约 **0-未知 1-需要预约 2-不需要预约**
        if (param.getAppoint() != null) {
            update.setAppoint(param.getAppoint());
            site.setAppoint(param.getAppoint());
        }
        // 使用范围**0-未知 1-对外开放 2-内部使用 3-特定人群**
        if (param.getScope() != null) {
            update.setScope(param.getScope());
            site.setScope(param.getScope());
        }
        // 上线时间
        if (param.getOnlineDate() != null) {
            update.setOnlineDate(param.getOnlineDate());
            site.setOnlineDate(param.getOnlineDate());
        }
        // 站点图片
        if (CollectionUtils.isNotEmpty(param.getImages())) {
            update.setImages(JsonUtils.toJsonString(param.getImages()));
            site.setImages(JsonUtils.toJsonString(param.getImages()));
        }
        // 支付方式
        if (!StringUtils.isBlank(param.getPayMod())) {
            update.setAppPay(param.getPayMod());
            site.setAppPay(param.getPayMod());
        } else {
            update.setAppPay("");
            site.setAppPay("");
        }
        // 收费说明
        if (!StringUtils.isBlank(param.getFeeDescription())) {
            update.setFeeDescription(param.getFeeDescription());
            site.setFeeDescription(param.getFeeDescription());
        }
        //收费范围 最小
        if (param.getFeeMin() != null) {
            update.setFeeMin(param.getFeeMin().longValue());
            site.setFeeMin(param.getFeeMin().longValue());
        }
        //收费范围 最大
        if (param.getFeeMax() != null) {
            update.setFeeMax(param.getFeeMax().longValue());
            site.setFeeMax(param.getFeeMax().longValue());
        }
        //电流形式
        if (param.getSupplyType() != null) {
            update.setSupplyType(param.getSupplyType());
            site.setSupplyType(param.getSupplyType());
        }
        //场站默认关联的计费模板Id
        if (param.getTemplateId() != null) {
            update.setTemplateId(param.getTemplateId());
            site.setTemplateId(param.getTemplateId());
        }
        //场站默认关联的计费模板名称
        if (param.getTemplateName() != null) {
            update.setTemplateName(param.getTemplateName());
            site.setTemplateName(param.getTemplateName());
        }

        if (param.getIsHidden() != null) {
            update.setIsHidden(param.getIsHidden());
            site.setIsHidden(param.getIsHidden());
        }

        if (param.getBizType() != null) {
            update.setBizType(param.getBizType().getCode());
            site.setBizType(param.getBizType().getCode());
            List<BizType> offlineSite = List.of(BizType.OFFLINE_SELF, BizType.OFFLINE_NON_SELF);
            prerunService.setSitePlatformConn(param.getSiteId(),
                !offlineSite.contains(param.getBizType()));
        }

        if (param.getBizName() != null) {
            update.setBizName(param.getBizName());
            site.setBizName(param.getBizName());
        }

        if (param.getInvoiceDesc() != null) {
            update.setInvoiceDesc(param.getInvoiceDesc());
            site.setInvoiceDesc(param.getInvoiceDesc());
        }

        if (param.getServiceInfo() != null) {
            update.setServiceInfo(param.getServiceInfo());
            site.setServiceInfo(param.getServiceInfo());
        }

        if (param.getForbiddenClient() != null) {
            update.setForbiddenClient(param.getForbiddenClient());
            site.setForbiddenClient(param.getForbiddenClient());
        }

        if (param.getGcType() != null) {
            update.setGcType(param.getGcType());
            site.setGcType(param.getGcType());
        }

        if (param.getPayChannelTypes() != null) {
            update.setPayChannels(
                param.getPayChannelTypes().stream().map(Enum::name)
                    .collect(Collectors.joining(",")));
            site.setPayChannels(update.getPayChannels());
        }

        // 更新场站商户配置渠道信息
        commPayChannelCfgService.updateSiteCommPayChannelCfg(
            param.getOperateId() != null ?
                param.getOperateId() : site.getOperateId(),
            param.getPayChannelTypes());

        if (null != param.getPark() && FREE_PARK_LIST.contains(param.getPark())) {
            log.info("场站当前设置为免停车费，禁用停车减免");
            SitePersonaliseDTO dto = new SitePersonaliseDTO();
            dto.setParkCouponTime(0)
                .setParkCouponKwh(0)
                .setTag(SITE_SETTING_6)
                .setSiteId(param.getSiteId());
            this.updatePersonalise(dto);
        }

        update.setSiteNo(param.getSiteNo());
        site.setSiteNo(param.getSiteNo());

        update.setCategory(param.getSiteCategoryList());
        site.setCategory(param.getSiteCategoryList());

        this.pvSiteUpdateField(update, site, param);
        this.essSiteUpdateField(update, site, param);

        // 创建关联数据
        siteAssetsRwDs.deleteBySiteId(update.getId());
        if (CollectionUtils.isNotEmpty(param.getSiteAssets())) {
            param.getSiteAssets().forEach(e -> {
                e.setSiteId(update.getId());
            });
            siteAssetsRwDs.batchInsert(param.getSiteAssets());
        }

        // 站点组信息
        if (CollectionUtils.isNotEmpty(param.getGidList())) {
            siteGroupSiteRwDs.deleteBySiteId(update.getId());
            siteGroupSiteRwDs.batchInsertBySiteId(update.getId(), param.getGidList());
        }

        log.info("修改站点信息[site={}] . nameChanged = {}",
            JsonUtils.toJsonString(update), nameChanged);
        //boolean count =
        siteRwDs.updateSite(update);
        this.updateMongoSite(site);
        if (nameChanged) {
            // 修改订单表上的场站名字
            String traceId = UUID.randomUUID().toString();
            log.info("修改场站名字 traceId = {}", traceId);
            this.chargerOrderBizService.updateSiteNameTask(site.getId(), site.getSiteName(),
                traceId);
        }
        log.info("推送MQ");
        dcEventPublisherService.publishSiteInfo(site);
        return site;
    }

    private void pvSiteUpdateField(SitePo update, SitePo site, UpdateSiteParam param) {
        if (CollectionUtils.isNotEmpty(param.getSiteCategoryList()) &&
            param.getSiteCategoryList().contains(SiteCategory.PV)) {
            update.setPvIncomeTemplateId(param.getPvIncomeTemplateId());

            update.setPvInstalledCapacity(param.getPvInstalledCapacity());
            update.setOnGridDate(param.getOnGridDate());
            update.setOnGridVoltageLevel(param.getOnGridVoltageLevel());

            site.setPvIncomeTemplateId(param.getPvIncomeTemplateId());

            site.setPvInstalledCapacity(param.getPvInstalledCapacity());
            site.setOnGridDate(param.getOnGridDate());
            site.setOnGridVoltageLevel(param.getOnGridVoltageLevel());
        } else {
            site.setPvIncomeTemplateId(null);
            site.setPvInstalledCapacity(null);
            site.setOnGridDate(null);
            site.setOnGridVoltageLevel(null);
        }
    }

    private void essSiteUpdateField(SitePo update, SitePo site, UpdateSiteParam param) {
        if (CollectionUtils.isNotEmpty(param.getSiteCategoryList()) &&
            param.getSiteCategoryList().contains(SiteCategory.ESS)) {
            update.setEssInPriceId(param.getEssInPriceId());
            update.setEssOutPriceId(param.getEssOutPriceId());
            update.setEssCapacity(param.getEssCapacity());
            update.setEssPower(param.getEssPower());

            site.setEssInPriceId(param.getEssInPriceId());
            site.setEssOutPriceId(param.getEssOutPriceId());
            site.setEssCapacity(param.getEssCapacity());
            site.setEssPower(param.getEssPower());
            // 储能场站  图片非必填
            update.setImages(JsonUtils.toJsonString(param.getImages()));
            site.setImages(JsonUtils.toJsonString(param.getImages()));
        } else {
            site.setEssInPriceId(null);
            site.setEssOutPriceId(null);
            site.setEssCapacity(null);
            site.setEssPower(null);
        }
    }


    @Transactional
    public SitePo updateSiteStatus(String siteId, SiteStatus status) {
        log.info("收到修改站点状态请求[siteId={},status={}]", siteId, status);

        SitePo dbSite = this.siteRoDs.getSite(siteId);
        if (dbSite.getStatus() == status.getCode()) {
            log.info("<< 忽略重复请求");
            return dbSite;
        }
        Assert.notNull(dbSite, "参数错误,场站不存在");
        SitePo update = new SitePo();
        update.setId(siteId);
        update.setStatus(status.getCode());
        update.setUpdateTime(Calendar.getInstance().getTime());
        if (status == SiteStatus.ONLINE && dbSite.getOnlineDate() == null) {
            //当场站的上线时间不为null时，才重新赋值
            dbSite.setOnlineDate(Calendar.getInstance().getTime());
            update.setOnlineDate(dbSite.getOnlineDate());
        }
        this.siteRwDs.updateSite(update);

        dbSite.setStatus(status.getCode());
        dbSite.setUpdateTime(update.getUpdateTime());

        if (status == SiteStatus.UNKNOWN) {
            // 删除mongo里'已删除'的场站
            this.siteMongoDataRepository.deleteById(siteId);
            // 删除产站组里的信息
            siteGroupSiteRwDs.deleteBySiteId(siteId);
        } else {
            this.updateMongoSite(dbSite);
        }
        dcEventPublisherService.publishSiteInfo(dbSite);
        return dbSite;
    }

    public String updateMongoSite(SitePo site) {
        if (StringUtils.isEmpty(site.getId())) {
            log.info("场站Id为空, 不能执行更新操作");
            throw new DcArgumentException("请提供场站Id");
        }

        SiteWithConfVo vo = new SiteWithConfVo();
        BeanUtils.copyProperties(site, vo);
        if (!FREE_PARK_LIST.contains(site.getPark())) {
            SiteDefaultSettingPo setting = siteDefaultSettingRwDs.getBySiteId(site.getId());
            if (null != setting) {
                vo.setParkCouponKwh(setting.getParkCouponKwh());
                vo.setParkCouponTime(setting.getParkCouponTime());
            }
        }

        mongoTemplate.save(this.mapSiteInMongo(vo));
        return site.getId();
    }

    /**
     * 对入参进行解析
     *
     * @param param
     * @return
     */
    public SendSiteBiParamParse paramParse(String param) {
        log.info("解析参数: param = [ {} ]", param);
        SendSiteBiParamParse parse = new SendSiteBiParamParse();
        if (StringUtils.isBlank(param) || "auto".equals(param)) {
            // 正常推送逻辑(调用方获取场站数据)
            return null;
        }

        // 参数去掉空格
        param = param.replace(" ", "");

        // 重发逻辑
        if (param.contains("send")) {
            // 推送规则调整
            if (param.startsWith("send")) {
                parse.setFailResend(null);
            } else if (param.startsWith("resend")) {
                parse.setFailResend(param.contains("fail"));
            }

            // 指定重送日期
            if (param.contains("date")) {
                try {
                    String[] split = param.split(";");
                    Optional<String> m = Stream.of(split).filter(str -> str.contains("date"))
                        .findFirst();
                    LocalDate date = LocalDate.parse(
                        m.get().substring(m.get().indexOf("date") + 5));
                    parse.setDate(date);
                } catch (Exception e) {
                    log.warn("指定收入时间的参数无效: param = {}, err = {}", param, e.getMessage(),
                        e);
                    throw new DcArgumentException("请传入有效的收入时间");
                }
            } else {
                parse.setDate(LocalDate.now().minusDays(1)); // 当前日期的前一天
            }

            // 是否指定场站
            if (param.contains("siteId")) {
                String[] split = param.split(";");
                Optional<String> siteId = Stream.of(split).filter(str -> str.contains("siteId"))
                    .findFirst();
                List<String> siteIdList = List.of(siteId.get()
                    .substring(siteId.get().indexOf("siteId") + 7).split(","));

                parse.setSiteIdList(siteIdList);
            }

            log.info("推送参数结果: parse = {}", parse);
            return parse;
        }

        // 其他参数不支持
        throw new DcArgumentException("请输入有效的参数");
    }

    /**
     * 传参规则
     * <p>
     * auto 或 不传参 -- 所有场站当前月份的运营数据 resend[.siteId=场站1Id,场站2Id...][;month=] --
     * 重传那些场站的某个月份数据，不带月份则默认当前系统月份 resend.fail[;siteId=场站1Id,场站2Id...][;month=] --
     * 重传推送失败的场站数据，不带场站则默认所有失败的数据
     *
     * @param param
     * @return
     */
    public synchronized int sendErpSiteBi(String param) {
        // 解析参数
        SendSiteBiParamParse parse = paramParse(param);

        // 区分新推送，还是重送
        if (null == parse ||
            null == parse.getFailResend()) { // 指定场站指定日期推送
            // 新数据推送
            // 1. 获取所有的场站
            List<SitePo> siteList = this.getSendSiteList(
                null != parse ? parse.getSiteIdList() : null);

            // 需要将后面的过滤条件去掉
            LocalDateTime incomeTime = parse != null ?
                parse.getIncomeTime()
                : LocalDate.now().minusDays(1).atTime(LocalTime.parse("00:00:00"));

            // 是否推送过,推送过怎不执行
            ListBiErpSiteParam biErpSiteParam = new ListBiErpSiteParam();
            biErpSiteParam.setIncomeTime(incomeTime);
            biErpSiteParam.setSiteIdList(
                siteList.stream().map(SitePo::getId).collect(Collectors.toList()));
            List<BiErpSitePo> poList = biErpSiteRwDs.listErpSite(biErpSiteParam);
            if (CollectionUtils.isNotEmpty(poList)) {
                log.info("数据已经推送过，不需要再次执行...");
                return 0;
            }

            siteList.forEach(site -> this.send2Erp(site, incomeTime));
        } else {
            // 数据重送
            ListBiErpSiteParam biErpSiteParam = new ListBiErpSiteParam();
            biErpSiteParam.setIncomeTime(parse.getIncomeTime());
            biErpSiteParam.setSiteIdList(parse.getSiteIdList());
            List<BiErpSitePo> poList;

            // 数据记录中仅有一条FAIL或SEND，携带其他CLOSE记录
            if (parse.getFailResend()) {
                // 查询推送失败的场站记录
                biErpSiteParam.setStatusList(List.of(ErpSiteStatus.FAIL, ErpSiteStatus.CLOSE));
                poList = biErpSiteRwDs.listErpSite(biErpSiteParam);
            } else {
                // 重送，不管是否推送失败，存在推送记录就会被重送
                biErpSiteParam.setStatusList(
                    List.of(ErpSiteStatus.FAIL, ErpSiteStatus.SENT, ErpSiteStatus.CLOSE));
                poList = biErpSiteRwDs.listErpSite(biErpSiteParam);
            }

            // 按收入时间分组
            // 这里考虑的情况是: 一个场站分多次推送都成功情况
            poList.stream()
                .collect(Collectors.groupingBy(p -> p.getSiteId() + p.getIncomeTime()))
                .forEach(this::resend2Erp);
        }

        // 推一次登一次原则
        this.sendErpSiteService.clearLogin();

        return 1; // 仅作返回标识，返回值无其他意义，无需处理
    }

    /**
     * 单个场站运营数据推送到ERP系统
     *
     * @param site
     * @return
     */
    private Boolean send2Erp(SitePo site, LocalDateTime incomeTime) {
        log.info(">> 场站运营数据推送 key = {}", site.getId() + incomeTime);
        SendSiteBi bi = null;
        List<BiErpOrderPo> poList = null;
        try {
            // 转换数据对象
            bi = this.siteMap2SendSiteBi(site);
            bi.setIncomeTime(incomeTime);

            // 获取场站的收入数据
            List<ChargerOrder> orderList = this.initSiteData(bi);

            // 空数据不推送
            if (DecimalUtils.isZero(bi.getTotalElectricity()) &&
                DecimalUtils.isZero(bi.getTotalOrderPrice()) &&
                DecimalUtils.isZero(bi.getTotalServicePrice()) &&
                DecimalUtils.isZero(bi.getTotalElectricPrice())) {
                log.info("场站数据为空: bi = {}", JsonUtils.toJsonString(bi));
                return Boolean.FALSE;
            }

            // 新增推送记录
            this.insertErpSite(bi);

            // 插入推送的订单数据
            poList = this.map2Po(orderList, bi.getId(), bi.getIncomeTime());
            biErpOrderRwDs.batchInsert(poList);

            // 推送
            ObjectResponse<String> response = sendErpSiteService.save(bi);
            if (response.getStatus() == 0) {  // 推送成功
                this.sendSuccess(bi, response, null, null);
            } else {  // 推送失败
                this.sendFail(bi, response, ErpSiteStatus.FAIL);
            }

            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("推送异常: err = {}", e.getMessage(), e);
            if (null != bi && null != bi.getId()) {
                // 推送失败
                this.exceptionFail(bi, poList, ErpSiteStatus.FAIL);
            }
        }

        log.info("<< 该场站运营数据推送完成");
        return Boolean.FALSE;
    }

    /**
     * 数据重送
     *
     * @param key        场站Id + 收入时间
     * @param recordList 之前推送记录列表
     * @return
     */
    private Boolean resend2Erp(String key, List<BiErpSitePo> recordList) {
        log.info(">> 场站运营数据重送 key = {}", key);
        SendSiteBi bi = null;
        BiErpSitePo po = null;
        List<BiErpOrderPo> poList = null;
        try {
            // 重送的记录中 FAIL 或 SEND 有且仅有一条记录
            List<ErpSiteStatus> targetStatus = List.of(ErpSiteStatus.FAIL, ErpSiteStatus.SENT);
            po = recordList.stream().filter(p -> targetStatus.contains(p.getStatus())).findFirst()
                .get();
            bi = new SendSiteBi();
            BeanUtils.copyProperties(po, bi);

            // 获取场站的收入数据
            List<ChargerOrder> orderList = this.initSiteData(bi);

            // 查看之前推送记录信息
            if (po.getStatus() == ErpSiteStatus.SENT) { // 已经推送成功，需要判断是否关账
                ObjectResponse<String> response = sendErpSiteService.view(bi);
                if (response.getStatus() == 0) {  // 查看返回正常
                    // 是否关账
                    String result = response.getData();
                    String erpCostNumber = this.getErpCostNumber(result);
                    if (null != erpCostNumber) { // 已经关账
                        log.info("该推送已关账：po = {}, erpCostNumber = {}", po, erpCostNumber);
                        // 计算差值
                        if (!this.differ(bi, recordList,
                            List.of(ErpSiteStatus.CLOSE, ErpSiteStatus.SENT))) {
                            return Boolean.FALSE;
                        }

                        // 新增推送记录
                        bi.setLastId(po.getId()).setErpCostNumber(erpCostNumber);
                        this.insertErpSite(bi);

                        // 插入推送的订单数据
                        poList = this.map2Po(orderList, bi.getId(), bi.getIncomeTime());
                        biErpOrderRwDs.batchInsert(poList);

                        // 推送
                        ObjectResponse<String> res = sendErpSiteService.save(bi);
                        if (res.getStatus() == 0) {  // 推送成功
                            this.sendSuccess(bi, res, po, ErpSiteStatus.CLOSE);
                        } else {  // 推送失败
                            this.sendFail(bi, res, ErpSiteStatus.CANCEL);
                        }

                        return Boolean.TRUE;
                    } // else null == erpCostNumber 没有关账的情况
                } else { // 查看返回失败
                    log.error("view result = {}", response.getError());
                    this.biErpSiteRwDs.updateById(
                        new BiErpSitePo().setId(po.getId())
                            .setRemark("接口查看异常: " +
                                response.getError()
                                    .substring(0, Math.min(response.getError().length(), 100))));
                    return Boolean.FALSE;
                }
            } // else po.getStatus() != ErpSiteStatus.SENT

            // 推送失败和没有关账的使用下面的处理逻辑
            if (!this.differ(bi, recordList, List.of(ErpSiteStatus.CLOSE))) {
                log.info("场站数据减去已关账的数据，没新的数据，不重推，status = {}", po.getStatus());
                return Boolean.FALSE;
            } else if (po.getStatus() == ErpSiteStatus.SENT) {
                // 避免没必要的推送
                BigDecimal elect = bi.getTotalElectricity().subtract(po.getTotalElectricity());
                BigDecimal orderPrice = bi.getTotalOrderPrice().subtract(po.getTotalOrderPrice());
                BigDecimal servicePrice = bi.getTotalServicePrice()
                    .subtract(po.getTotalServicePrice());
                BigDecimal electPrice = bi.getTotalElectricPrice()
                    .subtract(po.getTotalElectricPrice());

                if (DecimalUtils.isZero(elect) &&
                    DecimalUtils.isZero(orderPrice) &&
                    DecimalUtils.isZero(servicePrice) &&
                    DecimalUtils.isZero(electPrice)) {
                    log.info("已发送成功的，没有数据差异，不重推，status = {}", po.getStatus());
                    return Boolean.FALSE;
                }
            }

            // 新增推送记录
            this.insertErpSite(bi);

            // 插入推送的订单数据
            poList = this.map2Po(orderList, bi.getId(), bi.getIncomeTime());
            biErpOrderRwDs.batchInsert(poList);

            // 推送
            ObjectResponse<String> res = sendErpSiteService.save(bi);
            if (res.getStatus() == 0) {  // 推送成功
                this.sendSuccess(bi, res, po, ErpSiteStatus.CANCEL);
            } else {  // 推送失败
                this.sendFail(bi, res, ErpSiteStatus.CANCEL);
            }

            return Boolean.TRUE;
        } catch (Exception e) {
            log.error("推送异常: err = {}", e.getMessage(), e);
            if (null != bi && null != bi.getId() && !po.getId().equals(bi.getId())) {
                this.exceptionFail(bi, poList, ErpSiteStatus.CANCEL);
            }
        }

        log.info("<< 该场站运营数据重送完成");
        return Boolean.FALSE;
    }

    /**
     * 处理重送成功
     *
     * @param bi
     * @param po
     * @param status
     */
    private void sendSuccess(@NotNull SendSiteBi bi, ObjectResponse<String> res, BiErpSitePo po,
        ErpSiteStatus status) {
        // 获取返回的记录ID值
        bi.setErpId(getSaveId(res.getData()));
        bi.setSeqNo(getSeqNo(res.getData()));
        this.biErpSiteRwDs.updateById(bi.map2ErpSitePo().setStatus(ErpSiteStatus.SENT));
        // 将该厂站的相关订单调整成已推送状态
        this.biErpOrderRwDs.updateStatus(bi.getId(), ErpSiteStatus.SENT);

        // 原来推送成功的记录失效
        if (null != po) {
            this.biErpSiteRwDs.updateById(
                new BiErpSitePo().setId(po.getId()).setStatus(status));
            this.biErpOrderRwDs.updateStatus(po.getId(), status);
        }
    }

    /**
     * 处理推送失败
     *
     * @param bi
     * @param poList
     * @param status
     */
    private void exceptionFail(@NotNull SendSiteBi bi, @NotEmpty List<BiErpOrderPo> poList,
        ErpSiteStatus status) {
        this.biErpSiteRwDs.updateById(bi.map2ErpSitePo()
            .setStatus(status)
            .setRemark("逻辑处理异常(需要查看日志: " +
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                + ")"));

        // 推送的充电订单记录状态
        if (CollectionUtils.isNotEmpty(poList)) {
            this.biErpOrderRwDs.updateStatus(bi.getId(), status);
        }
    }

    /**
     * 处理推送失败
     *
     * @param bi
     * @param res
     * @param status
     */
    private void sendFail(@NotNull SendSiteBi bi, ObjectResponse<String> res,
        ErpSiteStatus status) {
        this.biErpSiteRwDs.updateById(bi.map2ErpSitePo()
            .setStatus(status)
            .setRemark("重推失败: " +
                res.getError().substring(0, Math.min(res.getError().length(), 100))));

        this.biErpOrderRwDs.updateStatus(bi.getId(), status);
    }

    /**
     * 获取场站充电订单数据
     *
     * @param bi
     * @return
     */
    private List<ChargerOrder> initSiteData(@NotNull SendSiteBi bi) {
        List<ChargerOrder> orderList = this.getChargerOrderListOfSite(bi);
        bi.setTotalElectricity(orderList.stream().map(ChargerOrder::getOrderElectricity)
            .reduce(BigDecimal.ZERO, BigDecimal::add));
        bi.setTotalOrderPrice(orderList.stream().map(ChargerOrder::getOrderPrice)
            .reduce(BigDecimal.ZERO, BigDecimal::add));
        bi.setTotalServicePrice(orderList.stream().map(ChargerOrder::getServicePrice)
            .reduce(BigDecimal.ZERO, BigDecimal::add));
        bi.setTotalElectricPrice(orderList.stream().map(ChargerOrder::getElecPrice)
            .reduce(BigDecimal.ZERO, BigDecimal::add));
        return orderList;
    }

    /**
     * 重送计算差值
     *
     * @param bi
     * @param statusList
     */
    private Boolean differ(@NotNull SendSiteBi bi,
        @NotEmpty List<BiErpSitePo> recordList,
        @NotEmpty List<ErpSiteStatus> statusList) {
        List<BiErpSitePo> collect = recordList.stream()
            .filter(r -> statusList.contains(r.getStatus()))
            .collect(Collectors.toList());
        BigDecimal elect = collect.stream()
            .map(BiErpSitePo::getTotalElectricity)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal orderPrice = collect.stream()
            .map(BiErpSitePo::getTotalOrderPrice)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal servicePrice = collect.stream()
            .map(BiErpSitePo::getTotalServicePrice)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal electPrice = collect.stream()
            .map(BiErpSitePo::getTotalElectricPrice)
            .reduce(BigDecimal.ZERO, BigDecimal::add);

        bi.setTotalElectricity(bi.getTotalElectricity().subtract(elect));
        bi.setTotalOrderPrice(bi.getTotalOrderPrice().subtract(orderPrice));
        bi.setTotalServicePrice(bi.getTotalServicePrice().subtract(servicePrice));
        bi.setTotalElectricPrice(bi.getTotalElectricPrice().subtract(electPrice));

        // 空数据不推送
        if (DecimalUtils.isZero(bi.getTotalElectricity()) &&
            DecimalUtils.isZero(bi.getTotalOrderPrice()) &&
            DecimalUtils.isZero(bi.getTotalServicePrice()) &&
            DecimalUtils.isZero(bi.getTotalElectricPrice())) {
            log.info("场站数据为空: bi = {}", JsonUtils.toJsonString(bi));
            return Boolean.FALSE;
        }

        return Boolean.TRUE;
    }

    /**
     * 获取Erp中的数据，存在数据说明已经关账
     *
     * @param json
     * @return
     */
    private String getErpCostNumber(String json) {
        String s = JsonUtils.fromJson(json)
            .get("Result")
            .get("Result")
            .get("F_Costnumber")
            .asText();

        if (StringUtils.isBlank(s)) {
            return null;
        }

        return s;
    }

    private List<BiErpOrderPo> map2Po(List<ChargerOrder> orderList, Long erpSiteId,
        LocalDateTime incomeTime) {
        return orderList.stream().map(order -> this.map2Po(order, erpSiteId, incomeTime))
            .collect(Collectors.toList());
    }

    private BiErpOrderPo map2Po(ChargerOrder order, Long erpSiteId, LocalDateTime incomeTime) {
        BiErpOrderPo po = new BiErpOrderPo();
        po.setIncomeTime(incomeTime);
        po.setErpSiteId(erpSiteId);
        po.setStatus(ErpSiteStatus.SENDING);
        po.setOrderNo(order.getOrderNo());
        po.setOrderPrice(order.getOrderPrice());
        po.setOrderElectricity(order.getOrderElectricity());
        po.setServicePrice(order.getServicePrice());
        po.setElectricPrice(order.getElecPrice());
        return po;
    }

    /**
     * 插入推送记录
     *
     * @param bi
     * @return
     */
    private void insertErpSite(SendSiteBi bi) {
        bi.setErpId(null).setSeqNo(null);
        BiErpSitePo po = bi.map2ErpSitePo();
        int i = this.biErpSiteRwDs.add(po);
        log.info("推送记录添加结果: i = {}, id = {}", i, po.getId());
        bi.setId(po.getId());
    }

    /**
     * 提取数据
     *
     * @param po
     * @return
     */
    private SendSiteBi siteMap2SendSiteBi(SitePo po) {
        SendSiteBi bi = new SendSiteBi();
        bi.setSiteNo(po.getSiteNo())
//                .setSeqNo(this.biErpSiteSeqNoGenerator.nextSeqNo()) // 注: 不赋值，等ERP系统返回
            .setSiteId(po.getId())
            .setTotalOrderPrice(BigDecimal.ZERO)
            .setTotalElectricPrice(BigDecimal.ZERO)
            .setTotalServicePrice(BigDecimal.ZERO);
        return bi;
    }

    /**
     * 查询该场站一天充电订单
     *
     * @param bi
     * @return
     */
    private List<com.chargerlinkcar.framework.common.domain.vo.ChargerOrder> getChargerOrderListOfSite(
        SendSiteBi bi) {
        long start = 0;
        int size = 1000;

        // 查询参数
        ListChargeOrderParam orderParam = new ListChargeOrderParam();
        orderParam.setStatusList(List.of(800, 2000));
        orderParam.setSiteIdList(List.of(bi.getSiteId()));

        TimeFilter filter = new TimeFilter();
        filter.setStartTime(new Date(bi.getIncomeTime()
            .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
        filter.setEndTime(new Date(bi.getIncomeTime().plusDays(1)
            .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
        orderParam.setChargeStopTimeFilter(filter);

        // 分页获取所有订单
        List<com.chargerlinkcar.framework.common.domain.vo.ChargerOrder> result = new ArrayList<>();
        while (true) {
            orderParam.setStart(start);
            orderParam.setSize(size);
            List<com.chargerlinkcar.framework.common.domain.vo.ChargerOrder> dataList =
                chargerOrderRoDs.listChargeOrder4Applet(orderParam).getData();

            if (CollectionUtils.isEmpty(dataList)) {
                break;
            }

            start += dataList.size();
            result.addAll(dataList);
        }

        return result;
    }

    /**
     * 获取当前所有符合的场站
     *
     * @return
     */
    public List<SitePo> getSendSiteList(List<String> siteIdList) {
        // 考虑数量巨大，分批更新
        long start = 0;
        int size = 200;

        // 查询所有的场站
        ListSiteParam param = new ListSiteParam();
//        param.setSiteNoExist(Boolean.TRUE);
        // 暂时需要查询的类型: 1，投建运营
//        param.setGcTypeList(List.of(1));
        // 上线的场站
        param.setStatusList(List.of(SiteStatus.ONLINE)); // 已上线的场站

        if (CollectionUtils.isNotEmpty(siteIdList)) {
            param.setSiteIdList(siteIdList);
        }

        List<SitePo> result = new ArrayList<>();
        List<SitePo> siteList;
        do {
            param.setStart(start).setSize(size);
            ListResponse<SitePo> siteListRes = this.siteRoDs.getSiteList(param);
            siteList = siteListRes.getData();
            result.addAll(siteList);
            start += siteList.size();
        } while (CollectionUtils.isNotEmpty(siteList));

        return result;
    }

    public SiteDetailInfoVo getSiteDetail(SiteGeoListRequest request) {
        //TODO 1-从缓存中获取
//        SiteDetailInfoVo detailInfo = (SiteDetailInfoVo) cacheService.getValue(DBSCacheKeyEnum.STATION_DETAIL, request.getSiteId());
//        if (detailInfo == null ) {
        //TODO 1.1-缓存中不存在，从数据库中获取
        SitePo site = this.siteRoDs.getSite(request.getSiteId());
        if (site == null) {
            log.warn("未查询到站点信息[siteId={}]", request.getSiteId());
            throw new DcArgumentException("未查询到站点信息", Level.WARN);
        }
        //log.info("先获取到站点信息[site={}]", JsonUtils.toJsonString(site));
        SiteDetailInfoVo detailInfo = SiteConvert.convertToSiteVo(site);
        detailInfo.setSiteNo(site.getSiteNo());
        detailInfo.setFrozenAmount(site.getFrozenAmount());
        detailInfo.setProvinceName(
            geoRoDs.getProvinceName(String.valueOf(detailInfo.getProvince())));
        detailInfo.setCityName(geoRoDs.getCityName(String.valueOf(detailInfo.getCity())));
        detailInfo.setAreaName(geoRoDs.getDistrictName(String.valueOf(detailInfo.getArea())));
        detailInfo.setPayMod(site.getAppPay());
        detailInfo.setPayChannelTypes(site.getPayChannelTypes());

        CommPo commercialPo = commRoDs.getCommById(site.getTopCommId());
        detailInfo.setTopCommName(commercialPo != null ? commercialPo.getName() : null);

        // 光伏相关字段赋值
        detailInfo.setCategory(site.getCategory());
        detailInfo.setPvInstalledCapacity(site.getPvInstalledCapacity());
        detailInfo.setPvIncomeTemplateId(site.getPvIncomeTemplateId());
        detailInfo.setOnGridDate(site.getOnGridDate());
        detailInfo.setOnGridVoltageLevel(site.getOnGridVoltageLevel());
        detailInfo.setSiteAssets(siteAssetsRoDs.getBySiteId(site.getId()));

        ListGtiParam param = new ListGtiParam();
        param.setSiteId(request.getSiteId());
        param.setTotal(true);
        ListResponse<GtiVo> listResponse = gtiFeignClient.findGtiList(param)
            .block(Duration.ofSeconds(50L));
        FeignResponseValidate.checkIgnoreData(listResponse);
        List<GtiVo> gtiVoList = listResponse.getData();
        if (CollectionUtils.isNotEmpty(gtiVoList)) {
            detailInfo.setPvNum(gtiVoList.size());
        } else {
            detailInfo.setPvNum(0);
        }

        ListEssParam essParam = new ListEssParam();
        essParam.setSiteIdList(List.of(request.getSiteId()));
        essParam.setTotal(true);
        ListResponse<EssVo> essListResponse = deviceFeignClient.findEssList(essParam)
            .block(Duration.ofSeconds(50L));
        FeignResponseValidate.checkIgnoreData(essListResponse);
        List<EssVo> essVoList = essListResponse.getData();
        if (CollectionUtils.isNotEmpty(essVoList)) {
            detailInfo.setEssNum(essVoList.size());
        } else {
            detailInfo.setEssNum(0);
        }

        if (site.getPvIncomeTemplateId() != null) {
            List<PriceItemPo> list = priceItemRoDs.getPriceItemListByTempId(
                site.getPvIncomeTemplateId());
            if (CollectionUtils.isNotEmpty(list)) {
                list.forEach(e -> {
                    e.setStart(DateUtil.intToTime(e.getStartTime()));
                    e.setStop(DateUtil.intToTime(e.getStopTime()));
                });
            }
            detailInfo.setPvProfit(list);
        }

        try {
            if (StringUtils.isNotBlank(site.getImages())) {
                List<SiteDetailInfoVo.ImageVo> images = JsonUtils.fromJson(site.getImages(),
                    new com.fasterxml.jackson.core.type.TypeReference<ArrayList<SiteDetailInfoVo.ImageVo>>() {
                    });
                detailInfo.setImageList(images);
            }
        } catch (Exception e) {
            log.warn("解析场站图片列表失败. {}", e.getMessage(), e);
        }
        //detailInfo.setProvinceName(areaInfoService.getProvinceNameByCode(detailInfo.getProvince()));
        //detailInfo.setCityName(areaInfoService.getCityNameByCode(detailInfo.getCity()));
        // detailInfo.setAreaName(areaInfoService.getAreaNameByCode(detailInfo.getArea()));
        //统计站点插座详情
        Map<String, Integer> chargerStatusMap = new HashMap<>(PlugStatus.values().length);
        for (PlugStatus statusEnum : PlugStatus.values()) {
            chargerStatusMap.put(statusEnum.name(), 0);
        }

        List<PlugVo> plugCacheList = redisIotReadService.listPlugBySiteId(request.getSiteId());

        //TODO 从缓存中获取
        //List connectorStatusList = cacheService.getAllHash(DeviceCacheKeyEnum.STATION_ID_REL_CONNECTOR_STATUS_4, request.getSiteId());
        if (CollectionUtils.isNotEmpty(plugCacheList)) {
            for (PlugVo plugCache : plugCacheList) {
                if (plugCache != null && plugCache.getStatus() != null) {
                    int count = chargerStatusMap.get(plugCache.getStatus().name());
                    chargerStatusMap.put(plugCache.getStatus().name(), count + 1);
                } else {
                    log.warn("异常的缓存数据. plugCache = {}", plugCache);
                }
            }
        }
        detailInfo.setChargerStatusMap(chargerStatusMap);

        if (detailInfo.getLatitude() == null || detailInfo.getLongitude() == null
            || request.getLatitude() == null || request.getLongitude() == null) {
            return detailInfo;
        }
        // 计算与传入经纬度的距离
        double distance = MapUtils.getDistance(detailInfo.getLatitude().doubleValue(),
            detailInfo.getLongitude().doubleValue(),
            request.getLatitude(), request.getLongitude());
        detailInfo.setDistance(distance);

        return detailInfo;
    }


    public void setOnlineDate(String siteId, String onlineDate) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        try {
            date = sdf.parse(onlineDate);
        } catch (ParseException e) {
            log.info("msg: {}", e.getMessage(), e);
            throw new DcServiceException("请传入正确的上线时间 例：" + sdf.format(new Date()));
        }
        SitePo site = new SitePo();
        site.setId(siteId);
        site.setOnlineDate(date);
        boolean ret = siteRwDs.updateSite(site);
        log.info("更新结果: ret = {}", ret);
    }

    public List<SiteTinyDto> getSiteTinyList(ListSiteParam param) {
        List<SiteTinyDto> siteList = this.siteRoDs.getSiteTinyList(param);
        if (Boolean.TRUE.equals(param.getOnlySiteWithMeter())) {
            // 仅查询有电表的场站
            if (CollectionUtils.isNotEmpty(siteList)) {
                List<String> siteIdList = siteList.stream().map(SiteTinyDto::getId).toList();
                ListResponse<String> response = meterFeignClient.getSiteIdWithMeterList(siteIdList);
                if (response != null && response.getData() != null) {
                    // 查到有电表的场站的结果了
                    Set<String> siteIdWithMeterSet = new HashSet<>(response.getData());
                    List<SiteTinyDto> result = siteList.stream()
                        .filter(site -> siteIdWithMeterSet.contains(site.getId()))
                        .collect(Collectors.toList());
                    return result;
                } else {
                    // 没查到，返回空数组
                    return new ArrayList<>();
                }
            }
        }
        return siteList;
    }


    /**
     * 用于查询场站编号是否存在
     *
     * @param siteNo
     * @return
     */
    public SiteNoVo getSiteInfoBySiteNo(Long topCommId, String siteNo) {
        return this.siteRoDs.getSiteInfoBySiteNo(topCommId, siteNo);
    }

    public SiteNoVo getSiteNoById(String siteId) {
        return this.siteRoDs.getSiteNoById(siteId);
    }


    /**
     * 从mongo里获取场站信息
     *
     * @param siteId
     * @return
     */
    public SiteInMongoVo getSiteFromMongo(String siteId, Long userId) {
        Optional<SiteInMongoPo> result = siteMongoDataRepository.findById(siteId);
        if (result.isEmpty()) {
            log.debug("场站[{}]mongo中不存在", siteId);
            throw new DcArgumentException("充电站不在了，如有疑问请联系在线客服");
        }

        result.ifPresent(e -> {
            SiteDefaultSettingPo setting = siteDefaultSettingRwDs.getBySiteId(siteId);
            if (setting != null &&
                setting.getParkId() != null &&
                setting.getParkSignKey() != null &&
                setting.getParkCouponTime() != null && setting.getParkCouponKwh() != null) {
                e.setParkCouponEnable(true);
            }
        });

        return result.map(e -> {
                SiteInMongoVo ret = new SiteInMongoVo();
                BeanUtils.copyProperties(e, ret);
                return ret;
            })
            .map(e -> {
                if (userId != null) {
                    final List<SiteInMongoVo> siteInMongoVos = extendsSiteInfoList(
                        List.of(e), userId);
                    return siteInMongoVos.get(0);
                } else {
                    return e;
                }
            })
            .get();
    }

    /**
     * 使用场站ID从mongo里获取场站信息
     *
     * @param param
     * @return
     */
    public List<SiteInMongoVo> getSiteListFromMongo(ListSiteParam param) {
        Query query = new Query();

        if (null != param.getTopCommId()) {
            query.addCriteria(Criteria.where("topCommId").is(param.getTopCommId()));
        }

        if (CollectionUtils.isNotEmpty(param.getSiteIdList())) {
            query.addCriteria(Criteria.where("id").in(param.getSiteIdList()));
        } else if (Boolean.TRUE.equals(param.getFilterIdlePlug())
            && param.getTopCommId() != null && param.getTopCommId() > 0L) {
            ListResponse<String> siteIdsRes = this.iotDeviceMmgClient.getIdleSiteIdList(
                param.getTopCommId());
            FeignResponseValidate.check(siteIdsRes);
            query.addCriteria(Criteria.where("id").in(siteIdsRes.getData()));
        }

        // 商户Id
        if (null != param.getCommIdList()) {
            query.addCriteria(Criteria.where("commId").in(param.getCommIdList()));
        }

        // 场站状态 status
        if (CollectionUtils.isNotEmpty(param.getStatusList())) {
            query.addCriteria(Criteria.where("status").in(param.getStatusList()));
        } else {
            List<SiteStatus> defaultStatusList = Arrays.asList(SiteStatus.ONLINE,
                SiteStatus.UNAVAILABLE);
            query.addCriteria(Criteria.where("status").in(defaultStatusList));
        }

        // 场站状态 scope
        if (null != param.getScopeList()) {
            query.addCriteria(Criteria.where("scope").in(param.getScopeList()));
        }

        // 内部使用场站  允许客户端显示
        query.addCriteria(Criteria.where("isHidden").ne(Boolean.TRUE));

        // 场站充电类型 supply type
        if (CollectionUtils.isNotEmpty(param.getSupplyTypeList())) {
            // 数据中存储交直流: BOTH

            // 只要传了，那就给它一个BOTH
            Set<SupplyType> supplyTypeSet = new HashSet<>();
            supplyTypeSet.add(SupplyType.BOTH);
            supplyTypeSet.addAll(param.getSupplyTypeList());

            query.addCriteria(Criteria.where("supplyType").in(new ArrayList<>(supplyTypeSet)));
        }

        // 场站收费筛选
        if (null != param.getParkingFeeTypeList()) {
            query.addCriteria(Criteria.where("park").in(param.getParkingFeeTypeList()));
        }

        // 支持支付方式
        if (CollectionUtils.isNotEmpty(param.getPayChannelList())) {
            List<Criteria> criteriaList = param.getPayChannelList().stream()
                .map(channel -> Criteria.where("payChannels").regex(channel))
                .collect(Collectors.toList());

            if (!criteriaList.isEmpty()) {
                query.addCriteria(new Criteria().orOperator(criteriaList.toArray(new Criteria[0])));
            }
        }

        // 场站运营类型筛选
        if (CollectionUtils.isNotEmpty(param.getBizTypeList())) {
            query.addCriteria(Criteria.where("bizType").in(param.getBizTypeList()));
        }

        // 关键词
        if (StringUtils.isNotBlank(param.getSk())) {
            Pattern pattern = Pattern.compile(param.getSk(), Pattern.CASE_INSENSITIVE);
            query.addCriteria(new Criteria().orOperator(
                Criteria.where("siteName").regex(pattern),
                Criteria.where("geoInfo.provinceName").regex(pattern),
                Criteria.where("geoInfo.cityName").regex(pattern),
                Criteria.where("geoInfo.districtName").regex(pattern),
                Criteria.where("geoInfo.address").regex(pattern)));
        }

        if (param.getAppClientType() != null) {
            Pattern pattern = Pattern.compile(param.getAppClientType().name(),
                Pattern.CASE_INSENSITIVE);
            query.addCriteria(Criteria.where("forbiddenClient").not().regex(pattern));
        }

        // 排序
        //Sort sort = Sort.by(Sort.Direction.ASC, "id"); // 默认排序
        Sort sort = Sort.unsorted();
        if (CollectionUtils.isNotEmpty(param.getSorts())) {
            if (null != param.getSorts() && param.getSorts().size() > 0) {
                for (SortParam s : param.getSorts()) {
                    Sort.Direction direction = (s.getOrder() == OrderType.asc ? Sort.Direction.ASC
                        : Sort.Direction.DESC);
                    for (String c : s.getColumns()) {
                        sort = sort.and(Sort.by(direction, c));
                    }
                }
            }
            sort = sort.and(Sort.by(Sort.Direction.ASC, "id")); // 默认排序
        }

        // 筛选充电类型场站
        if (param.getSiteCategory() != null) {
            query.addCriteria(Criteria.where("category").in(param.getSiteCategory()));
        }

        // 查询形式
        if (null != param.getGeoNear() &&
            null != param.getGeoNear().getLng() &&
            null != param.getGeoNear().getLat()) {
            Point point = new Point(
                param.getGeoNear().getLng().doubleValue(),
                param.getGeoNear().getLat().doubleValue());

            // 距离: 入参单位 m
            Long distance = param.getGeoNear().getDistance();
            NearQuery nearQuery = NearQuery.near(point, Metrics.KILOMETERS)
                .query(query).maxDistance(null == distance ? Double.MAX_VALUE : distance / 1000.0);
            log.info("查询条件: sorted = {},  nearQuery={}",
                sort.isSorted(), JsonUtils.toJsonString(nearQuery));

            Aggregation a;
            if (null != param.getStart() && null != param.getSize() && sort.isSorted()) {
                log.info("page: start={}, size={}", param.getStart(), param.getSize());
                // 带分页信息
                a = Aggregation.newAggregation(
                    Aggregation.geoNear(nearQuery, "distance"),
                    Aggregation.sort(sort),
                    Aggregation.skip(param.getStart()),
                    Aggregation.limit(param.getSize()));
            } else if (null != param.getStart() && null != param.getSize()) {
                log.info("page: start={}, size={}", param.getStart(), param.getSize());
                // 带分页信息
                a = Aggregation.newAggregation(
                    Aggregation.geoNear(nearQuery, "distance"),
                    Aggregation.skip(param.getStart()),
                    Aggregation.limit(param.getSize()));
            } else if (sort.isSorted()) {
                a = Aggregation.newAggregation(
                    Aggregation.sort(sort),
                    Aggregation.geoNear(nearQuery, "distance"));
            } else {
                a = Aggregation.newAggregation(Aggregation.geoNear(nearQuery, "distance"));
            }
            log.info("查询条件: querySiteWhitelist={}", a.toString());

            AggregationResults<SiteInMongoPo> results =
                mongoTemplate.aggregate(a, SiteInMongoPo.class, SiteInMongoPo.class);
            log.info("result.size = {}", results.getMappedResults().size());
            final List<SiteInMongoVo> siteInMongoVos = extendsSiteInfoList(
                results.getMappedResults(), param.getUserId());
            return new ArrayList<>(siteInMongoVos);
        } else if (null != param.getGeoWith() && param.getGeoWith().size() > 3) {
            // TODO: 暂时使用对角两点实现矩形查询
            // 矩形搜索
            double[] lowerLeft = new double[]{
                param.getGeoWith().get(0).getLng().doubleValue(),
                param.getGeoWith().get(0).getLat().doubleValue()
            };
            double[] upperRight = new double[]{
                param.getGeoWith().get(2).getLng().doubleValue(),
                param.getGeoWith().get(2).getLat().doubleValue()
            };

            query.addCriteria(Criteria.where("position").within(
                new Box(lowerLeft, upperRight)));

            // 排序
            query.with(sort);
        } // else 支持不传地理位置信息

        // 分页
        if (null != param.getStart() && null != param.getSize()) {
            query.skip(param.getStart())
                .limit(param.getSize());
        }
        log.info("查询条件: querySiteWhitelist={}", query.toString());

        List<SiteInMongoPo> list = mongoTemplate.find(query, SiteInMongoPo.class);
        final List<SiteInMongoVo> siteInMongoVos = extendsSiteInfoList(list, param.getUserId());
        log.info("list.size = {}", siteInMongoVos.size());
        return new ArrayList<>(siteInMongoVos);

    }

    /**
     * 扩展场站信息，增加活动、用户优惠券
     *
     * @param siteVoList
     * @param userId
     * @return
     */
    private List<SiteInMongoVo> extendsSiteInfoActivity(List<SiteInMongoVo> siteVoList,
        Long userId) {
        if (CollectionUtils.isEmpty(siteVoList) || userId == null || userId <= 0) {
            return siteVoList;
        }

        final List<String> siteIdList = siteVoList.stream()
            .map(SiteInMongoPo::getId)
            .distinct()
            .collect(Collectors.toList());

        // 获取场站生效中的活动(免费领券)
        List<ActivityRunningVo> runningListBySiteList = activityRoDs.getRunningListBySiteList(
            siteIdList, true);
        log.info("runningListBySiteList: {}", runningListBySiteList.size());

        // 场站绑定场站组   生效的活动
        List<ActivityRunningVo> runningListBySiteGidList = activityRoDs.getRunningListBySiteGidList(
            siteIdList, true);

        // 合并活动
        List<ActivityRunningVo> activityList = Stream.of(runningListBySiteList,
                runningListBySiteGidList)
            .filter(CollectionUtils::isNotEmpty)
            .flatMap(List::stream)
            .collect(Collectors.toList());

        // 按照发券规则拆分生效中的活动activityList
        // 仅类型是免费领券有多条发券规则，需要拆分，同时，仅发券规则是不同时段可再领需要拆分
        List<Long> freeAquireIdList = activityList.stream()
            .filter(
                activityRunningVo -> ActivityType.FREE_ACQUIRE.equals(activityRunningVo.getType())
                    && CouponSendType.ONCE_PER_PERIOD.equals(activityRunningVo.getSendCouponRule()))
            .map(ActivityRunningVo::getId)
            .distinct()
            .collect(Collectors.toList());

        // 其他类型的id列表
        List<Long> notJoinedIdList = activityList.stream()
            .filter(
                activityRunningVo -> !ActivityType.FREE_ACQUIRE.equals(activityRunningVo.getType())
                    || !CouponSendType.ONCE_PER_PERIOD.equals(
                    activityRunningVo.getSendCouponRule()))
            .map(ActivityRunningVo::getId)
            .distinct()
            .collect(Collectors.toList());

        // <场站> 活动列表map
        final Map<String, List<ActivityRunningVo>> siteGroupingActivityMap =
            activityList.stream()
                .collect(Collectors.groupingBy(ActivityRunningVo::getSiteId));

//        if(CollectionUtils.isEmpty(runningListBySiteList)) {
//            return siteVoList;
//        }

        // 排序，满金额最低的优惠券活动，放置在最前面
//        final Map<String, List<ActivityRunningVo>> siteGroupingActivityMap =
//            runningListBySiteList.stream()
//                .collect(Collectors.groupingBy(ActivityRunningVo::getSiteId));
//        siteGroupingActivityMap.keySet()
//            .forEach(key -> {
//                final List<ActivityRunningVo> activityRunningVos = siteGroupingActivityMap.get(key);
//                final List<ActivityRunningVo> activityRunningVosSorted = activityRunningVos.stream()
//                    .sorted(Comparator.comparing(ActivityRunningVo::getConditionAmount))
//                    .collect(Collectors.toList());
//                siteGroupingActivityMap.put(key, activityRunningVosSorted);
//            });

        // 用户在<场站>下所有未使用的券+进行中活动的券MAP
//        Map<String, List<CouponVoEx>> userSiteCouponMap = new HashMap<>();

//        if(CollectionUtils.isNotEmpty(runningListBySiteList)) {

        // 用户已获取但还未使用的券+活动中的券
        List<CouponVoEx> byUserAndSite = couponRoDs.getByUserAndSite(siteIdList,
            userId,
            activityList.stream()
                .map(ActivityRunningVo::getId)
                .distinct()
                .collect(Collectors.toList())
        );
        log.info("byUserAndSite: {}", byUserAndSite.size());

        // 场站组活动绑定的
        List<CouponVoEx> byUserAndSiteGroup = couponRoDs.getByUserAndSiteGroup(siteIdList,
            userId,
            activityList.stream()
                .map(ActivityRunningVo::getId)
                .distinct()
                .collect(Collectors.toList())
        );

        List<CouponVoEx> byUserList = Stream.of(byUserAndSite, byUserAndSiteGroup)
            .filter(CollectionUtils::isNotEmpty)
            .flatMap(List::stream)
            .collect(Collectors.toList());

        // <场站>用户已领取的可用券+活动中的券
        final Map<String, List<CouponVoEx>> userSiteCouponMap = byUserList.stream()
            .collect(Collectors.groupingBy(CouponVoEx::getSiteId));

        // 用户参与的所有活动id
        final List<Long> activityIdObtained = byUserList.stream()
            .map(CouponVoEx::getActivityId)
            .distinct()
            .collect(Collectors.toList());

        // 获取用户已参与的活动id对应的ruleId列表
        Map<Long, List<Long>> actIdAndRuleIdListObtained = byUserList.stream()
            .collect(Collectors.groupingBy(
                CouponVoEx::getActivityId,
                Collectors.collectingAndThen( // 先执行去重，再排序并转换为 List
                    Collectors.mapping(CouponVoEx::getRuleId, Collectors.toSet()), // 去重
                    ruleIds -> {
                        List<Long> sortedRuleIds = new ArrayList<>(ruleIds);
                        sortedRuleIds.sort(Long::compareTo);
                        return sortedRuleIds;
                    }
                )
            ));

        // 用户已参与的所有活动
        final List<ActivityRunningVo> allJoinedActivityList = activityRoDs.getListByIdList(
            activityIdObtained);
        allJoinedActivityList.forEach(e -> e.setObtained(true));
        // 按照发券规则拆分用户已参与的活动allJoinedActivityList
        // 仅类型是免费领券有多条发券规则，需要拆分，同时，仅发券规则是不同时段可再领需要拆分
        List<Long> joinedFreeAquireIdList = allJoinedActivityList.stream()
            .filter(
                activityRunningVo -> ActivityType.FREE_ACQUIRE.equals(activityRunningVo.getType())
                    && CouponSendType.ONCE_PER_PERIOD.equals(activityRunningVo.getSendCouponRule()))
            .map(ActivityRunningVo::getId)
            .distinct()
            .collect(Collectors.toList());

        // 其他类型的id列表
        List<Long> joinedIdList = allJoinedActivityList.stream()
            .filter(
                activityRunningVo -> !ActivityType.FREE_ACQUIRE.equals(activityRunningVo.getType())
                    || !CouponSendType.ONCE_PER_PERIOD.equals(
                    activityRunningVo.getSendCouponRule()))
            .map(ActivityRunningVo::getId)
            .distinct()
            .collect(Collectors.toList());

        // 把生效中和已参与的放在一起去获取规则参数列表
        freeAquireIdList = Stream.concat(freeAquireIdList.stream(), joinedFreeAquireIdList.stream())
            .distinct()
            .collect(Collectors.toList());
        notJoinedIdList = Stream.concat(notJoinedIdList.stream(), joinedIdList.stream())
            .distinct()
            .collect(Collectors.toList());
        // 获取规则参数列表
        List<ActivityCouponRulePo> couponRulePoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(freeAquireIdList)) {
            couponRulePoList = activityCouponRuleRoDs.getListByActivityIdList(
                freeAquireIdList);
        }
        if (CollectionUtils.isNotEmpty(notJoinedIdList)) {
            couponRulePoList.addAll(activityCouponRuleRoDs.getListByActivityIdList(
                notJoinedIdList));
        }
        // 按 activityId 分组
        Map<Long, List<ActivityCouponRulePo>> groupedRulesByActivityId = couponRulePoList.stream()
            .collect(Collectors.groupingBy(ActivityCouponRulePo::getActivityId));

        // 按照activityId和ruleId来统计当前领取人次是否超过规则限制
        Map<String, Boolean> obtainedCountMap = new HashMap<>();
        couponRulePoList.forEach(couponRulePo -> {
            // 已领取人次
            Long alreadyAcquireNum = couponRoDs.getAlreadyAcquireNumByRuleId(
                couponRulePo.getActivityId(),
                couponRulePo.getId());

            if (alreadyAcquireNum.intValue() < couponRulePo.getMaxAmountPerTime()) {
                obtainedCountMap.put(couponRulePo.getActivityId() + "-" + couponRulePo.getId(),
                    false);
            } else {
                obtainedCountMap.put(couponRulePo.getActivityId() + "-" + couponRulePo.getId(),
                    true);
            }
        });

//        }

        // 未使用的所有券
//        final List<CouponVoEx> unusedCouponInSite = couponRoDs.getUnusedCouponInSite(siteIdList,
//            userId);

        // 未使用使用的所有券 对应的活动
//        final List<Long> unusedCouponActivity = unusedCouponInSite.stream()
//            .map(CouponVoEx::getActivityId)
//            .distinct()
//            .collect(Collectors.toList());
        // 已获取券的活动
//        final List<ActivityRunningVo> listByIdList = activityRoDs.getListByIdList(
//            unusedCouponActivity);
//        final Map<String, List<ActivityRunningVo>> siteActivityObtainedMap = listByIdList.stream()
//            .collect(Collectors.groupingBy(ActivityRunningVo::getSiteId));

        for (SiteInMongoVo one : siteVoList) {
            final String siteId = one.getId();
            final List<CouponVoEx> couponVoExes = userSiteCouponMap.get(siteId);

            // 当前场站 生效中的活动
            final List<ActivityRunningVo> activityRunningVos = Optional
                .ofNullable(siteGroupingActivityMap.get(siteId))
                .orElse(List.of());

            List<ActivityRunningVo> allActivityList;

            if (CollectionUtils.isEmpty(couponVoExes)) {
                // 无任何券，赋值当前活动列表
//                allActivityList = activitySort(activityRunningVos);
                // 对有多个规则参数列表的免费领券活动，需要单独判断是否已经领取满
                List<ActivityRunningVo> allActivityListTemp = new ArrayList<>();

                activityRunningVos.forEach(activityRunningVo -> {
                    if (this.dealUnjoinedActivityByRuleId(activityRunningVo,
                        groupedRulesByActivityId, actIdAndRuleIdListObtained,
                        obtainedCountMap) != null) {
                        allActivityListTemp.add(
                            this.dealUnjoinedActivityByRuleId(activityRunningVo,
                                groupedRulesByActivityId, actIdAndRuleIdListObtained,
                                obtainedCountMap));
                    }
                });
                allActivityList = activitySort(allActivityListTemp);

            } else {
                // 当前场站 已参与的活动id
                final Set<Long> joinedActivityInSite = couponVoExes.stream()
                    .map(CouponVoEx::getActivityId)
                    .collect(Collectors.toSet());

                // 当前场站 生效中 且 未参与的活动
                final List<ActivityRunningVo> unjoinedActivityList = activityRunningVos.stream()
                    .filter(act -> !joinedActivityInSite.contains(act.getId()))
                    .collect(Collectors.toList());

                // 当前场站 已参与的活动
                final List<ActivityRunningVo> joinedActivityList = allJoinedActivityList.stream()
                    .filter(act -> joinedActivityInSite.contains(act.getId()))
                    .collect(Collectors.toList());

                // 如果类型是免费领券有多条发券规则，以及发券规则是不同时段可再领，拆分为多条

                // 已参与的活动直接按照已领取的ruleId进行拆分
                List<ActivityRunningVo> needAddJoinedList = new ArrayList<>();
                joinedActivityList.forEach(activityRunningVo -> {
                    if (actIdAndRuleIdListObtained.containsKey(activityRunningVo.getId())
                        && CollectionUtils.isNotEmpty(
                        actIdAndRuleIdListObtained.get(activityRunningVo.getId()))
                        && actIdAndRuleIdListObtained.get(activityRunningVo.getId()).size() > 1) {
                        int size =
                            actIdAndRuleIdListObtained.get(activityRunningVo.getId()).size() - 1;
                        for (int i = 0; i < size; i++) {
                            needAddJoinedList.add(activityRunningVo);
                        }
                    }
                });
                joinedActivityList.addAll(needAddJoinedList);
                joinedActivityList.sort(Comparator.comparingLong(ActivityRunningVo::getId));

                // 可能存在该逻辑，一个已参与的活动有多个规则参数列表，当前时间段可能未领券，也可能已领券。
                // 如果当前时间段已领券，则已在上边已参与的活动按照已领取的ruleId处处理好了
                // 如果当前时间段未领券，则需要添加一条未参与的活动
                List<ActivityRunningVo> unjoinedListTemp = new ArrayList<>();
                joinedActivityList.forEach(activityRunningVo -> {
                    if (!ActivityType.FREE_ACQUIRE.equals(activityRunningVo.getType())) {
                        // 非免费领券，不进行处理，进入下一次循环
                        return;
                    }
                    if (!CouponSendType.ONCE_PER_PERIOD.equals(
                        activityRunningVo.getSendCouponRule())) {
                        // 非单一时段限领1次，不同时段可再领，不进行处理，进入下一次循环
                        return;
                    }
                    if (!ActivityStatusType.PROCESSING.equals(
                        activityRunningVo.getStatus())) {
                        // 非进行中的活动，不进行处理，进入下一次循环
                        return;
                    }
                    if (!groupedRulesByActivityId.containsKey(activityRunningVo.getId())) {
                        return;
                    }
                    List<ActivityCouponRulePo> activityCouponRulePoList = groupedRulesByActivityId.get(
                        activityRunningVo.getId());
                    if (CollectionUtils.isEmpty(activityCouponRulePoList)) {
                        return;
                    }
                    // 找出来当前时间段的
                    Date now = new Date();
                    Optional<ActivityCouponRulePo> validCouponRule = activityCouponRulePoList
                        .stream()
                        .filter(rulePo -> !rulePo.getTimeFrom().after(now) && !rulePo.getTimeTo()
                            .before(now))
                        .min(Comparator.comparingLong(ActivityCouponRulePo::getId));

                    // 检查是否有符合条件的规则参数
                    if (validCouponRule.isPresent()) {
                        ActivityCouponRulePo rulePo = validCouponRule.get();
                        // 查看当前ruleId有没有领过券
                        boolean obtainedResult = actIdAndRuleIdListObtained.get(
                            activityRunningVo.getId()) != null
                            && actIdAndRuleIdListObtained.get(
                            activityRunningVo.getId()).contains(rulePo.getId());
                        if (!obtainedResult && !obtainedCountMap.get(
                            activityRunningVo.getId() + "-" + rulePo.getId())) {
                            ActivityRunningVo needAddActivity = new ActivityRunningVo();
                            BeanUtils.copyProperties(activityRunningVo, needAddActivity);
                            needAddActivity.setObtained(false);
                            unjoinedListTemp.add(needAddActivity);
                        }
                    }
                });

                // 未参与的活动

                // 未参与的活动无需处理按照ruleId拆分
                // 如果未参与的活动跟已参与的活动重名，则表示是编辑版本，需要单独处理

                unjoinedActivityList.forEach(activityRunningVo -> {
                    // 在已参与的活动里找到名字相同的
                    List<ActivityRunningVo> sameNameActivityList = joinedActivityList.stream()
                        .filter(
                            joinedActivity -> joinedActivity.getName()
                                .equals(activityRunningVo.getName())).collect(Collectors.toList());
                    // 未参与的活动是全部时间内仅可领取一次
                    if (CouponSendType.ONCE.equals(activityRunningVo.getSendCouponRule())) {
                        // 判断有没有领过的
                        if (CollectionUtils.isEmpty(sameNameActivityList)) {
                            // 没领过，还是未参与的活动
                            if (this.dealUnjoinedActivityByRuleId(activityRunningVo,
                                groupedRulesByActivityId, actIdAndRuleIdListObtained,
                                obtainedCountMap) != null) {
                                unjoinedListTemp.add(
                                    this.dealUnjoinedActivityByRuleId(activityRunningVo,
                                        groupedRulesByActivityId, actIdAndRuleIdListObtained,
                                        obtainedCountMap));
                            }
                        }
                    }

                    if (CouponSendType.ONCE_PER_PERIOD.equals(
                        activityRunningVo.getSendCouponRule())) {
                        // 未参与的活动是一个时间段内可以领一次
                        // 判断有没有领过的
                        if (CollectionUtils.isEmpty(sameNameActivityList)) {
                            // 没领过，还是未参与的活动
                            if (this.dealUnjoinedActivityByRuleId(activityRunningVo,
                                groupedRulesByActivityId, actIdAndRuleIdListObtained,
                                obtainedCountMap) != null) {
                                unjoinedListTemp.add(
                                    this.dealUnjoinedActivityByRuleId(activityRunningVo,
                                        groupedRulesByActivityId, actIdAndRuleIdListObtained,
                                        obtainedCountMap));
                            }
                        } else {
                            // 领过，以编辑后的规则参数的时间段来看，在当前时间所属规则的时间范围内是不是参与过活动
                            List<ActivityCouponRulePo> activityCouponRulePoList = groupedRulesByActivityId.get(
                                activityRunningVo.getId());
                            if (CollectionUtils.isEmpty(activityCouponRulePoList)) {
                                // 没有规则参数的活动，直接添加
                                unjoinedListTemp.add(activityRunningVo);
                                return;
                            }
                            // 找出来当前时间段的发券规则
                            Date now = new Date();
                            Optional<ActivityCouponRulePo> validCouponRule = activityCouponRulePoList
                                .stream()
                                .filter(rulePo -> !rulePo.getTimeFrom().after(now)
                                    && !rulePo.getTimeTo()
                                    .before(now))
                                .min(Comparator.comparingLong(ActivityCouponRulePo::getId));

                            // 检查是否有符合条件的规则参数
                            if (validCouponRule.isPresent()) {
                                // 拿到起止时间
                                // 看之前领的券有没有在这个起止时间内的

                                // 找到编辑前的活动id列表
                                Set<Long> sameActivityIdSet = sameNameActivityList.stream()
                                    .map(ActivityRunningVo::getId)
                                    .filter(Objects::nonNull)
                                    .collect(Collectors.toSet());

                                Optional<CouponVoEx> validTimeCoupon = couponVoExes
                                    .stream()
                                    .filter(couponVoEx -> sameActivityIdSet.contains(
                                        couponVoEx.getActivityId()))
                                    .filter(couponVoEx -> !couponVoEx.getCreateTime()
                                        .before(validCouponRule.get().getTimeFrom()))
                                    .filter(couponVoEx -> !couponVoEx.getCreateTime()
                                        .after(validCouponRule.get().getTimeTo()))
                                    .min(Comparator.comparingLong(CouponVoEx::getId));
                                if (validTimeCoupon.isEmpty() && !obtainedCountMap.get(
                                    activityRunningVo.getId() + "-" + validCouponRule.get()
                                        .getId())) {
                                    unjoinedListTemp.add(activityRunningVo);
                                }
                            } else {
                                // 没有表示当前不在任一活动时间内，不添加
                                // 不做任何处理
                            }

                        }
                    }
                });

                // 未参与的活动排序后放置在列表前面
                final List<ActivityRunningVo> activityRunningVosSorted = activitySort(
                    unjoinedListTemp);

                activityRunningVosSorted.addAll(joinedActivityList);

                allActivityList = activityRunningVosSorted;
            }

            one.setActivityAllList(allActivityList);
//            final List<ActivityRunningVo> activityRunningVos = siteGroupingActivityMap.get(
//                one.getId());
//            if(CollectionUtils.isNotEmpty(activityRunningVos)) {
//                // 进行中的活动
//                one.setActivityRunningVoList(activityRunningVos);
//            }
//
//            final List<ActivityRunningVo> activityObtainedVos = siteActivityObtainedMap.get(
//                one.getId());
//
//            if(CollectionUtils.isNotEmpty(activityObtainedVos)) {
//                //
//                final Set<Long> obtainedActivityIdSet = activityObtainedVos.stream()
//                    .map(ActivityRunningVo::getId)
//                    .collect(Collectors.toSet());
//                // 未获取的活动
//                final List<ActivityRunningVo> unobtainedActivity = activityRunningVos.stream()
//                    .filter(arv -> !obtainedActivityIdSet.contains(arv.getId()))
//                    .collect(Collectors.toList());
//
//                unobtainedActivity.addAll()
//
//            } else {
//                one.setActivityAllList(activityRunningVos);
//            }
//
//            final List<CouponVoEx> couponVoExes = userSiteCouponMap.get(one.getId());
//            if(CollectionUtils.isNotEmpty(couponVoExes)) {
//                one.setCouponVoExList(couponVoExes);
//            }
//
//            if(CollectionUtils.isNotEmpty(activityRunningVos)) {
//
//                final List<CouponVoEx> userObtainedCoupon = CollectionUtils.isNotEmpty(couponVoExes) ?
//                    couponVoExes : List.of();
//
//                final List<Long> couponActivityIds = userObtainedCoupon.stream()
//                    .map(CouponVoEx::getActivityId)
//                    .distinct()
//                    .collect(Collectors.toList());
//                // 可领券的活动
//                one.setActivityCanObtainList(activityRunningVos.stream()
//                    .filter(arVos -> arVos != null && !couponActivityIds.contains(arVos.getId()))
//                    .collect(Collectors.toList()));
//            }
//
//            // 还未消费的活动
//            final List<ActivityRunningVo> obtainedActivities = siteActivityObtainedMap.get(
//                one.getId());
//            one.setActivityUnconsumedList(obtainedActivities);
        }

        return siteVoList;
    }

    /**
     * 根据ruleId来判断当前活动是否是未参加的活动
     *
     * @return
     */
    private ActivityRunningVo dealUnjoinedActivityByRuleId(ActivityRunningVo activityRunningVo,
        Map<Long, List<ActivityCouponRulePo>> groupedRulesByActivityId,
        Map<Long, List<Long>> actIdAndRuleIdListObtained,
        Map<String, Boolean> obtainedCountMap) {
        ActivityRunningVo unjoinedActivity = null;

        // 根据ruleId判断是不是领取满了
        if (groupedRulesByActivityId.containsKey(activityRunningVo.getId())
            && CollectionUtils.isNotEmpty(
            groupedRulesByActivityId.get(activityRunningVo.getId()))) {
            // 看有没有符合当前时间的规则参数，有的话去判断这个规则参数有没有领完
            // 找出来当前时间段的
            Date now = new Date();
            Optional<ActivityCouponRulePo> validCouponRule = groupedRulesByActivityId.get(
                    activityRunningVo.getId())
                .stream()
                .filter(
                    rulePo -> !rulePo.getTimeFrom().after(now)
                        && !rulePo.getTimeTo()
                        .before(now))
                .min(Comparator.comparingLong(ActivityCouponRulePo::getId));

            // 检查是否有符合条件的规则参数
            if (validCouponRule.isPresent()) {
                ActivityCouponRulePo rulePo = validCouponRule.get();
                // 查看当前ruleId有没有领过券
                boolean obtainedResult = actIdAndRuleIdListObtained.get(
                    activityRunningVo.getId()) != null
                    && actIdAndRuleIdListObtained.get(
                    activityRunningVo.getId()).contains(rulePo.getId());
                if (!obtainedResult && !obtainedCountMap.get(
                    activityRunningVo.getId() + "-" + rulePo.getId())) {
                    unjoinedActivity = activityRunningVo;
                }
            }
        } else {
            unjoinedActivity = activityRunningVo;
        }
        return unjoinedActivity;
    }

    /**
     * 活动排序
     *
     * @param in
     * @return
     */
    private List<ActivityRunningVo> activitySort(List<ActivityRunningVo> in) {
        return in.stream()
            .sorted(Comparator.comparing(ActivityRunningVo::getAmount).reversed())
            .collect(Collectors.toList());
//        return in.stream()
//            .sorted(
//                new Comparator<ActivityRunningVo>() {
//                    @Override
//                    public int compare(ActivityRunningVo o1,
//                        ActivityRunningVo o2) {
//                        BigDecimal discountO1 = o1.getAmount();
//                        BigDecimal discountO2 = o2.getAmount();
//                        return discountO1.compareTo(discountO2);
//                    }
//                })
//            .collect(Collectors.toList());
    }

    private List<SiteInMongoVo> extendsSiteInfoScoreSetting(List<SiteInMongoVo> siteVoList,
        Long userId) {

        if (CollectionUtils.isEmpty(siteVoList) || userId == null || userId <= 0) {
            return siteVoList;
        }

        final List<String> siteIdList = siteVoList.stream().map(SiteInMongoVo::getId)
            .collect(Collectors.toList());

        final List<SiteGroupRefPo> siteGroupRefList = siteGroupRoDs.getSiteGroupRefList(siteIdList);

        if (CollectionUtils.isEmpty(siteGroupRefList)) {
            return siteVoList;
        }

        final List<String> gids = siteGroupRefList.stream()
            .map(SiteGroupRefPo::getGid)
            .distinct()
            .collect(Collectors.toList());

        SearchScoreLogParam param = new SearchScoreLogParam();
        param.setUserId(userId)
            .setGids(gids);
        final ListResponse<UserScoreSettingLevelSiteGidDto> siteGroupUserScoreSettingRes =
            userFeignClient.getSiteGroupUserScoreSetting(param);

        log.debug("siteGroupUserScoreSetting: {}", siteGroupUserScoreSettingRes);
        FeignResponseValidate.check(siteGroupUserScoreSettingRes);
        final List<UserScoreSettingLevelSiteGidDto> levelData =
            siteGroupUserScoreSettingRes.getData();

        if (CollectionUtils.isEmpty(levelData)) {
            return siteVoList;
        }

        final Map<String, List<UserScoreSettingLevelSiteGidDto>> scoreSettingMap =
            levelData.stream()
                .collect(Collectors.groupingBy(UserScoreSettingLevelSiteGidDto::getGid));

        final Map<String, List<SiteGroupRefPo>> gidMap = siteGroupRefList.stream()
            .collect(Collectors.groupingBy(SiteGroupRefPo::getSiteId));

        for (SiteInMongoVo one : siteVoList) {
            if (CollectionUtils.isNotEmpty(gidMap.get(one.getId()))) {
                final List<String> gidList = gidMap.get(one.getId()).stream()
                    .map(SiteGroupRefPo::getGid)
                    .collect(Collectors.toList());
                one.setGids(gidList);

                Set<String> gidSet = new HashSet(gidList);
                gidSet.retainAll(scoreSettingMap.keySet());

                List<UserScoreSettingLevelSiteGidDto> userScoreSettingList = new ArrayList<>();
                for (String gid : gidSet) {
                    userScoreSettingList.addAll(scoreSettingMap.get(gid));
                }
//                final List<UserScoreSettingLevelSiteGidDto> userScoreListUniq = userScoreSettingList.stream()
//                    .sorted(
//                        new Comparator<UserScoreSettingLevelSiteGidDto>() {
//                            @Override
//                            public int compare(UserScoreSettingLevelSiteGidDto o1,
//                                UserScoreSettingLevelSiteGidDto o2) {
//
//                                // TODO SCORE 类型为阶梯单价的积分体系优先级最高
//
//                                BigDecimal discountO1;
//                                BigDecimal discountO2;
//                                if (o1.getCurrentLevel() != null &&
//                                    o1.getCurrentLevel().getDiscount() != null) {
//                                    discountO1 = o1.getCurrentLevel().getDiscount();
//                                } else {
//                                    discountO1 = BigDecimal.valueOf(101);
//                                }
//                                if (o2.getCurrentLevel() != null &&
//                                    o2.getCurrentLevel().getDiscount() != null) {
//                                    discountO2 = o2.getCurrentLevel().getDiscount();
//                                } else {
//                                    discountO2 = BigDecimal.valueOf(101);
//                                }
//                                return discountO1.compareTo(discountO2);
//                            }
//                        })
//                    .distinct()
////                    .filter(e -> e.getCurrentLevel() != null &&
////                        NumberUtils.gtZero(e.getCurrentLevel().getLevel()))
//                    .filter(x -> {
//                        if (DiscountType.SERV_FEE.equals(x.getDiscountType())) {
//                            return x.getCurrentLevel() != null &&
//                                null != x.getCurrentLevel().getDiscount() &&
//                                DecimalUtils.lt(x.getCurrentLevel().getDiscount(),
//                                    new BigDecimal("100")); // 过滤100%折扣
//                        }
//                        return true;
//                    })
//                    .limit(1)
//                    .collect(Collectors.toList());
                one.setUserScoreSettingList(this.calScoreSettingLevel(userScoreSettingList));
            }
        }

        return siteVoList;
    }

    /**
     * 计算最优积分等级
     *
     * @return
     */
    private List<UserScoreSettingLevelSiteGidDto> calOptimalLevel(
        List<UserScoreSettingLevelSiteGidDto> userScoreSettingList) {
        // 类型为阶梯单价的积分体系优先级最高
        Optional<UserScoreSettingLevelSiteGidDto> first = userScoreSettingList.stream()
            .filter(x -> DiscountType.FIXED_TOTAL_FEE.equals(x.getDiscountType())
                && null != x.getCurrentLevel() &&
                null != x.getCurrentLevel().getTotalPrice())
            .findFirst();
        if (first.isPresent()) {
            return userScoreSettingList.stream()
                .filter(x -> DiscountType.FIXED_TOTAL_FEE.equals(x.getDiscountType())
                    && null != x.getCurrentLevel() &&
                    null != x.getCurrentLevel().getTotalPrice())
                .sorted(Comparator.comparing(o -> o.getCurrentLevel().getTotalPrice()))
                .limit(1)
                .collect(Collectors.toList());
        }

        return userScoreSettingList.stream()
            .sorted(
                new Comparator<UserScoreSettingLevelSiteGidDto>() {
                    @Override
                    public int compare(UserScoreSettingLevelSiteGidDto o1,
                        UserScoreSettingLevelSiteGidDto o2) {

                        // TODO SCORE 类型为阶梯单价的积分体系优先级最高

                        BigDecimal discountO1;
                        BigDecimal discountO2;
                        if (o1.getCurrentLevel() != null &&
                            o1.getCurrentLevel().getDiscount() != null) {
                            discountO1 = o1.getCurrentLevel().getDiscount();
                        } else {
                            discountO1 = BigDecimal.valueOf(101);
                        }
                        if (o2.getCurrentLevel() != null &&
                            o2.getCurrentLevel().getDiscount() != null) {
                            discountO2 = o2.getCurrentLevel().getDiscount();
                        } else {
                            discountO2 = BigDecimal.valueOf(101);
                        }
                        return discountO1.compareTo(discountO2);
                    }
                })
            .distinct()
//                    .filter(e -> e.getCurrentLevel() != null &&
//                        NumberUtils.gtZero(e.getCurrentLevel().getLevel()))
            .filter(x -> {
                if (DiscountType.SERV_FEE.equals(x.getDiscountType())) {
                    return x.getCurrentLevel() != null &&
                        null != x.getCurrentLevel().getDiscount() &&
                        DecimalUtils.lt(x.getCurrentLevel().getDiscount(),
                            new BigDecimal("100")); // 过滤100%折扣
                }
                return true;
            })
            .limit(1)
            .collect(Collectors.toList());
    }

    /**
     * 获取排序后的积分体系列表（优先级从高到低）
     *
     * @param userScoreSettingList
     * @return
     */
    private List<UserScoreSettingLevelSiteGidDto> calScoreSettingLevel(
        List<UserScoreSettingLevelSiteGidDto> userScoreSettingList) {
        return userScoreSettingList.stream()
            .filter(dto -> {
                DiscountType discountType = dto.getDiscountType();
                if (DiscountType.FIXED_TOTAL_FEE.equals(discountType) ||
                    DiscountType.SERV_FEE.equals(discountType)) {
                    BigDecimal discount =
                        dto.getCurrentLevel() != null ? dto.getCurrentLevel().getDiscount() : null;
                    return dto.getCurrentLevel() != null &&
                        discount != null &&
                        discount.compareTo(BigDecimal.valueOf(100)) < 0; // 过滤100%折扣
                }
                return false;
            })
            .sorted(Comparator.comparing((UserScoreSettingLevelSiteGidDto dto) -> {
                    // FIXED_TOTAL_FEE类型优先级最高
                    if (DiscountType.FIXED_TOTAL_FEE.equals(dto.getDiscountType())) {
                        return 0;
                    }
                    // SERV_FEE类型其次
                    if (DiscountType.SERV_FEE.equals(dto.getDiscountType())) {
                        return 1;
                    }
                    return 2;
                }).thenComparing(dto -> {
                    // FIXED_TOTAL_FEE类型，按总价格排序
                    if (DiscountType.FIXED_TOTAL_FEE.equals(dto.getDiscountType()) &&
                        dto.getCurrentLevel() != null &&
                        dto.getCurrentLevel().getTotalPrice() != null) {
                        return dto.getCurrentLevel().getTotalPrice();
                    }
                    // 其他类型,返回一个默认的最大值
                    return BigDecimal.valueOf(101);
                }, Comparator.nullsLast(BigDecimal::compareTo))
                .thenComparing(dto -> {
                    // SERV_FEE类型，按折扣值排序
                    BigDecimal discount =
                        dto.getCurrentLevel() != null ? dto.getCurrentLevel().getDiscount() : null;
                    return discount != null ? discount
                        : BigDecimal.valueOf(101);
                }, Comparator.nullsLast(BigDecimal::compareTo)))
            .collect(Collectors.toList());
    }

    /**
     * 拓展场站列表信息
     *
     * @param sitePoList
     * @param userId
     * @return
     */
    private List<SiteInMongoVo> extendsSiteInfoList(List<SiteInMongoPo> sitePoList, Long userId) {

        log.info("extendsSiteInfoList: {}, {}", sitePoList.size(), userId);

        final List<SiteInMongoVo> siteVosRet = sitePoList.stream()
            .map(in -> {
                SiteInMongoVo out = new SiteInMongoVo();
                BeanUtils.copyProperties(in, out);
                return out;
            })
            .collect(Collectors.toList());

        extendsSiteInfoScoreSetting(siteVosRet, userId);

        return extendsSiteInfoActivity(siteVosRet, userId);
    }


    /**
     * 将场站信息同步到mongo
     */
    public void syncSite2Mongo() {
        long start = 0L;
        int size = 100;

        ListSiteParam param = new ListSiteParam();
        param.setSize(size);
        List<SiteWithConfVo> siteList;
        do {
            param.setStart(start);
            ListResponse<SiteWithConfVo> siteListRes = this.siteRoDs.getSiteWithConfList(param);
            siteList = siteListRes.getData();
            //siteList = this.siteQueryService.listSite(start, size, templateId, false);
            start = start + siteList.size();
            this.insertCollection(this.mapSiteInMongo(siteList));
        } while (CollectionUtils.isNotEmpty(siteList));
    }

    private List<SiteInMongoPo> mapSiteInMongo(List<SiteWithConfVo> siteList) {
        return siteList.stream().map(this::mapSiteInMongo).collect(Collectors.toList());
    }

    /**
     * 根据价格模板获取当前价格
     * @return
     */
    private Pair<BigDecimal, ChargePriceItem> queryCurPrice(SiteChargePriceVo siteChargePriceVo) {
        // 当前电价
        BigDecimal curPrice = BigDecimal.ZERO;
        // 计费信息
        ChargePriceItem chargePriceItem = new ChargePriceItem();
        // 当前时间
        LocalDateTime curTime = LocalDateTime.now();
        int curMinutes = curTime.getHour() * 60 + curTime.getMinute();

        if (CollectionUtils.isNotEmpty(siteChargePriceVo.getItemList())) {
            for (ChargePriceItem item : siteChargePriceVo.getItemList()) {
                // 找到第一个结束时间大于当前时间的
                int endTime =
                    Integer.parseInt(item.getEndTime().substring(0, 2)) * 60
                        + Integer.parseInt(item.getEndTime().substring(2));
                if (endTime >= curMinutes) {
                    // 当前时段价格信息
                    BigDecimal curTmpPrice =
                        item.getElecPrice() == null ? BigDecimal.ZERO
                            : item.getElecPrice();
                    curTmpPrice = curTmpPrice.add(
                        item.getServPrice() == null ? BigDecimal.ZERO
                            : item.getServPrice());

                    curPrice = curTmpPrice;
                    chargePriceItem = item;
                    break;
                }
            }
        }
        return Pair.of(curPrice, chargePriceItem);
    }

    private SiteInMongoPo mapSiteInMongo(SiteWithConfVo site) {
        SiteInMongoPo siteInMongo = new SiteInMongoPo(site.getLongitude(), site.getLatitude());

        try {
            // 场站基础信息
            siteInMongo.setId(site.getId())
                .setDcTotal(null != site.getDcPlugNum() ? site.getDcPlugNum().longValue() : null)
                .setAcTotal(null != site.getAcPlugNum() ? site.getAcPlugNum().longValue() : null)
                .setAppoint(site.getAppoint())
                .setAppPay(site.getAppPay())
                //.setBrandIds(site.getBrandIds())
                .setContacts(site.getContacts())
                .setContactsPhone(site.getContactsPhone())
                .setCreateTime(site.getCreateTime())
                .setPayChannels(site.getPayChannels())
                .setFeeDescription(site.getFeeDescription())
                .setInvoiceDesc(site.getInvoiceDesc())
                .setFeeMax(site.getFeeMax())
                .setFeeMin(site.getFeeMin())
                .setMerchantId(site.getMerchantId())
                .setOnlineDate(site.getOnlineDate())
                .setTopCommId(site.getTopCommId())
                .setCommId(site.getOperateId())
                .setCommName(site.getOperateName())
                .setPark(site.getPark())
                .setServiceInfo(site.getServiceInfo())
//                    .setParkFee(
//                            StringUtils.isBlank(site.getParkFee()) ?
//                                    0 : Long.valueOf(this.validParkFee(site.getParkFee())))
                .setParkFee(site.getParkFee())
                .setPhone(site.getPhone())
                .setRemark(site.getRemark())
                .setType(site.getType())
                .setScope(site.getScope())
                .setIsHidden(site.getIsHidden())
                .setWorkTimeHoliday(site.getServiceHolidayTime())
                .setWorkTimeWorkDay(site.getServiceWorkdayTime())
                .setStatus(site.getStatus() == null ? SiteStatus.UNAVAILABLE
                    : SiteStatus.valueOf(site.getStatus()))
                .setUpdateTime(site.getUpdateTime())
                .setSiteName(site.getSiteName())
                .setForbiddenClient(site.getForbiddenClient())
                .setBizType(site.getBizType())
                .setBizName(site.getBizName())
                .setPartnerCode(site.getPartnerCode())
                .setSiteNo(site.getSiteNo()) //添加站点自定义编号
                .setCategory(site.getCategory());

            // 场站图片
            siteInMongo.setImages(new ArrayList<>());
            String images = site.getImages();
            if (StringUtils.isNotBlank(images)) {
                JsonNode node = new ObjectMapper().readTree(images);
                if (node.isArray()) {
                    for (JsonNode n : node) {
                        if (n.get("url") != null) {
                            siteInMongo.getImages().add(n.get("url").asText());
                        } // end if
                    }
                } // end if
            } // end if

            // 场站地理位置：经纬度上面已经初始化
            if (siteInMongo.getGeoInfo() != null) {
                siteInMongo.getGeoInfo()
                    .setProvinceCode(
                        site.getProvince() == null ? null : site.getProvince().toString())
                    .setProvinceName(site.getProvince() == null ? null
                        : geoRoDs.getProvinceName(String.valueOf(site.getProvince())))
                    .setCityCode(site.getCity() == null ? null : site.getCity().toString())
                    .setCityName(site.getCity() == null ? null
                        : geoRoDs.getCityName(String.valueOf(site.getCity())))
                    .setDistrictCode(site.getArea() == null ? null : site.getArea().toString())
                    .setDistrictName(site.getArea() == null ? null
                        : geoRoDs.getDistrictName(String.valueOf(site.getArea())))
                    .setAddress(site.getAddress());
            }

            // 场站计费模板信息
            if (site.getTemplateId() != null && site.getTemplateId() > 0L) {
                // 场站计费模板列表
                List<SiteChargePriceVo> sitePriceList = this.priceSchemaBizService.getSitePriceList(
                    site.getId());
                if (CollectionUtils.isNotEmpty(sitePriceList)) {
                    BigDecimal curPrice = null; // 当前价格
                    ChargePriceItem chargePriceItem = new ChargePriceItem(); // 当前计费详情
                    SiteChargePriceVo chargePrice = new SiteChargePriceVo(); // 当前计费模板
                    for (SiteChargePriceVo priceVo : sitePriceList) {
                        // 查询当前模板的价格和计费项
                        Pair<BigDecimal, ChargePriceItem> pricePair = this.queryCurPrice(priceVo);
                        // 获取价格并进行空值判断
                        BigDecimal price = pricePair.getFirst();
                        // 如果是第一个有效价格或发现更小价格
                        if (curPrice == null || price.compareTo(curPrice) < 0) {
                            curPrice = price;
                            chargePriceItem = pricePair.getSecond();
                            chargePrice = priceVo;
                        }
                    }

                    // 计费模板
                    ChargePriceVo price = new ChargePriceVo();
                    BeanUtils.copyProperties(chargePrice, price);
                    siteInMongo.setChargePrice(price);
                    // 设置费用
                    siteInMongo.setElecPrice(chargePriceItem.getElecPrice())
                        .setServPrice(chargePriceItem.getServPrice());
                    siteInMongo.setCurPrice(curPrice);
                    // 单位
                    siteInMongo.setUnit(sitePriceList.size() > 1 ? "元/kW·h起" : "元/kW·h");
                    // 多计费模板

                    List<ChargePriceVo> chargePriceList = JsonUtils.fromJson(
                        JsonUtils.toJsonString(sitePriceList),
                        new TypeReference<List<ChargePriceVo>>() {
                        });

                    siteInMongo.setChargePriceList(chargePriceList);
                }
            }

            if (null != site.getPark() && FREE_PARK_LIST.contains(site.getPark())) {
                siteInMongo.setParkCouponKwh(null);
                siteInMongo.setParkCouponTime(null);
            } else {
                siteInMongo.setParkCouponKwh(site.getParkCouponKwh());
                siteInMongo.setParkCouponTime(site.getParkCouponTime());
            }

            // 场站充电桩支持电流形式
            siteInMongo.setSupplyType(site.getSupplyType());

            return siteInMongo;
        } catch (Exception e) {
            log.warn("siteId = {}, msg = {}", site.getId(), e.getMessage(), e);
            throw new DcServiceException("数据转换异常了，请检查后重试");
        }
    }

    public long insertCollection(List<SiteInMongoPo> site) {
        siteMongoDataRepository.saveAll(site);
        return site.size();
    }


    public ListResponse<SiteVo> getSiteVoList(ListSiteParam param) throws DcServiceException {
        log.info("分页获取站点信息列表 param= {}", param);
        // 获取站点基础信息列表
        List<Integer> statusList = new ArrayList<>();
        if (param.getStatusList() == null) {
            param.setStatusList(
                List.of(SiteStatus.ONLINE, SiteStatus.OPENING, SiteStatus.UNAVAILABLE));
        }

        //如果查询指定站点，则不需要分页
        //Page<SitePo> pageInfo = PageHelper.offsetPage(request.getStart().intValue(), request.getSize());
        ListResponse<SiteVo> list = siteRoDs.getSiteVoList(param);

        if (Boolean.TRUE.equals(param.getFetchImageList()) &&
            list != null && CollectionUtils.isNotEmpty(list.getData())) {
            list.getData().forEach(e -> {
                try {
                    if (StringUtils.isNotBlank(e.getImages())) {
                        List<ImageVo> images = JsonUtils.fromJson(e.getImages(),
                            new com.fasterxml.jackson.core.type.TypeReference<ArrayList<ImageVo>>() {
                            });
                        e.setImageList(images);
                    }
                } catch (Exception ex) {
                    log.warn("解析场站图片列表失败. {}", ex.getMessage(), ex);
                }
            });
        }

        // 包含组信息
        if (Boolean.TRUE.equals(param.getIncludeGroup()) && CollectionUtils.isNotEmpty(
            list.getData())) {
            List<String> siteIdList = list.getData().stream().map(SiteVo::getId)
                .collect(Collectors.toList());
            // 场站组信息
            List<SiteGroupRefPo> siteGroupList = siteGroupRoDs.getSiteGroupRefList(siteIdList);

            if (CollectionUtils.isNotEmpty(siteGroupList)) {
                // 转map
                Map<String, List<SiteGroupRefPo>> siteGroupMap = siteGroupList.stream()
                    .collect(Collectors.groupingBy(SiteGroupRefPo::getSiteId));

                // 场站组名称
                List<String> gIdList = siteGroupList.stream().map(SiteGroupRefPo::getGid).distinct()
                    .collect(Collectors.toList());
                ListSiteGroupParam listSiteGroupParam = new ListSiteGroupParam();
                listSiteGroupParam.setGidList(gIdList);
                ListResponse<SiteGroupPo> response = authCenterFeignClient.getSiteGroupList(
                    listSiteGroupParam);
                FeignResponseValidate.check(response);
                List<SiteGroupPo> groupList = response.getData();

                Map<String, SiteGroupPo> groupMap = groupList.stream()
                    .collect(Collectors.toMap(SiteGroupPo::getGid, group -> group));

                // 场站添加组信息
                list.getData().forEach(e -> {
                    if (siteGroupMap.containsKey(e.getId())) {
                        ArrayList<SiteGroupPo> siteGroupVos = new ArrayList<>();
                        siteGroupMap.get(e.getId()).forEach(x -> {
                            if (groupMap.containsKey(x.getGid())) {
                                siteGroupVos.add(groupMap.get(x.getGid()));
                            }
                        });
                        e.setSiteGroupList(siteGroupVos);
                    }
                });
            }
        }

        log.info("站点列表信息: size = {}", list.getData().size());

        return list;
    }


    public Mono<List<SiteProfitInfo>> getSiteProfitList(ListSiteProfitParam param) {
        return Mono.just(param)
            .filter(p -> CollectionUtils.isNotEmpty(p.getSiteIdList()))
            .map(siteRoDs::getSiteProfitList)
            .switchIfEmpty(Mono.just(List.of()));
    }

    public ListResponse<PriceItemPo> getPvProfitList(String siteId) {

        SitePo site = siteRoDs.getSite(siteId);
        if (site == null || site.getPvIncomeTemplateId() == null) {
            return RestUtils.buildListResponse(List.of());
        }

        List<PriceItemPo> res = priceItemRoDs.getPriceItemListByTempId(
            site.getPvIncomeTemplateId());
        return RestUtils.buildListResponse(res);
    }


    public void updateSiteDebitAccent(SiteDebitAccountVo request) {
        CloudChargeVo result = mgmWebCharging.siteDebitAccountCheck(request);
        log.info("设置场站的默认扣款账户. siteId= {}, payType= {}, payAccountId= {}",
            request.getSiteId(), result.getPayType(), result.getSitePayAccountId());
        SitePo site = new SitePo();
        site.setId(request.getSiteId());
        site.setPayAccountId(result.getSitePayAccountId());
        site.setDefaultPayType(result.getPayType().getCode());
        //int i = siteManageMapper.updateById(site);
        this.siteRwDs.updateSite(site);
        //log.info("更新结果: i = {}", i);
    }

    @Deprecated
    @Transactional
    public void updateInvoicedValid(List<String> list, Integer invoicedValid) {
        log.info("更新站点开票请求站点ID = {}, invoiceValid = {}", JsonUtils.toJsonString(list),
            invoicedValid);
        list.stream().forEach(siteId -> {
            SitePo site = new SitePo();
            site.setId(siteId);
            site.setInvoicedValid(invoicedValid);
            this.siteRwDs.updateSite(site);
        });
    }

    public ListResponse<Site> getSiteList(@Nullable Long commId,
        @Nullable String cityCode,
        @Nullable List<String> siteIdList,
        long start, int size) {
        Dict dict = new Dict();
        dict.setType("siteType");
        ListResponse<Dict> dictListResponse = userFeignClient.queryPage(dict);
        FeignResponseValidate.check(dictListResponse);

        String commIdChain = null;
        if (commId != null) {
            ObjectResponse<Commercial> objectResponse = commercialFeignClient.getCommercial(commId);
            if (objectResponse == null
                || objectResponse.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS
                || objectResponse.getData() == null) {
                throw new DcArgumentException("无此商户信息");
            }
            commIdChain = objectResponse.getData().getIdChain();
        }

        List<SitePo> list = siteRoDs.listSiteUnify(commIdChain, cityCode, siteIdList, start, size);
        long total = siteRoDs.listSiteUnifyCount(commIdChain, cityCode, siteIdList);

        if (CollectionUtils.isEmpty(list)) {
            log.info("list is empty");
            return RestUtils.buildListResponse(new ArrayList<>());
        }

        List<Site> res = new ArrayList<>();
        for (SitePo po : list) {
            Site site = new Site();
            site.setSiteId(po.getId())
                .setName(po.getSiteName())
                .setTopCommId(po.getTopCommId())
                .setCommId(po.getOperateId())
                .setProvince(po.getProvince() == null ? null : po.getProvince().toString())
                .setCity(po.getCity() == null ? null : po.getCity().toString())
                .setArea(po.getArea() == null ? null : po.getArea().toString())
                .setAreaName(po.getAreaName())
                .setAddress(po.getAddress())
                .setAcPlugNum(po.getAcPlugNum())
                .setDcPlugNum(po.getDcPlugNum())
                .setParkFeeDesc(po.getParkFee())
                .setStatus(po.getStatus())
                .setPriceCode(po.getTemplateId())
                .setLon(po.getLongitude())
                .setLat(po.getLatitude())
                .setSiteType(po.getType())
                .setCommPhone(po.getPhone())
                .setPhone(po.getContactsPhone());
            dictListResponse.getData().stream().forEach(e -> {
                if (e.getValue().equals(po.getType().toString())) {
                    site.setSiteTypeDesc(e.getLabel());
                }
            });
            res.add(site);
        }
        return new ListResponse<Site>(res, total);
    }

    public ListResponse<HlhtSiteInfoVo> getHlhtSiteInfoVoList(@Nullable Long commId,
        @Nullable String cityCode,
        @Nullable List<String> siteIdList,
        long start, int size) {
        Dict dict = new Dict();
        dict.setType("siteType");
        ListResponse<Dict> dictListResponse = userFeignClient.queryPage(dict);
        FeignResponseValidate.check(dictListResponse);

        String commIdChain = null;
        if (commId != null) {
            ObjectResponse<Commercial> objectResponse = commercialFeignClient.getCommercial(commId);
            if (objectResponse == null
                || objectResponse.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS
                || objectResponse.getData() == null) {
                throw new DcArgumentException("无此商户信息");
            }
            commIdChain = objectResponse.getData().getIdChain();
        }

        List<SitePo> list = siteRoDs.listSiteUnify(commIdChain, cityCode, siteIdList, start, size);
        long total = siteRoDs.listSiteUnifyCount(commIdChain, cityCode, siteIdList);

        if (CollectionUtils.isEmpty(list)) {
            log.info("list is empty");
            return RestUtils.buildListResponse(new ArrayList<>());
        }

        List<HlhtSiteInfoVo> res = new ArrayList<>();
        for (SitePo po : list) {
            HlhtSiteInfoVo site = new HlhtSiteInfoVo();
            site.setSiteId(po.getId())
                .setName(po.getSiteName())
                .setProvince(po.getProvince() == null ? null : po.getProvince().toString())
                .setCity(po.getCity() == null ? null : po.getCity().toString())
                .setArea(po.getArea() == null ? null : po.getArea().toString())
                .setAreaName(po.getAreaName())
                .setAddress(po.getAddress())
                .setAcPlugNum(po.getAcPlugNum())
                .setDcPlugNum(po.getDcPlugNum())
                .setParkFeeDesc(po.getParkFee())
                .setParkFeeType(po.getPark())
                .setStatus(po.getStatus())
                .setPriceCode(po.getTemplateId())
                .setLon(po.getLongitude())
                .setLat(po.getLatitude())
                .setSiteType(po.getType())
                .setPhone(po.getPhone())
                .setContactsPhone(po.getContactsPhone())
                .setOpenHourDesc(po.getServiceWorkdayTime() + "," + po.getServiceHolidayTime())
                .setOnlineDate(po.getOnlineDate())
                .setParkTimeoutFee(po.getParkTimeoutFee());
            try {
                if (StringUtils.isNotBlank(po.getImages())) {
                    List<SiteDetailInfoVo.ImageVo> images = JsonUtils.fromJson(po.getImages(),
                        new com.fasterxml.jackson.core.type.TypeReference<ArrayList<SiteDetailInfoVo.ImageVo>>() {
                        });
                    List<String> picList = images.stream().map(SiteDetailInfoVo.ImageVo::getUrl)
                        .collect(Collectors.toList());
                    site.setPicList(picList);
                }
            } catch (Exception e) {
                log.warn("解析场站图片列表失败. {}", e.getMessage(), e);
            }
            dictListResponse.getData().stream().forEach(e -> {
                if (e.getValue().equals(po.getType().toString())) {
                    site.setSiteTypeDesc(e.getLabel());
                }
            });
            res.add(site);
        }
        List<SiteTemplateVo> siteTemplateList = siteTemplateRoDs.getSiteTemplateInfoBySiteIdList(
            siteIdList);
        Map<String, List<SiteTemplateVo>> templateMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(siteTemplateList)) {
            templateMap = siteTemplateList.stream()
                .collect(Collectors.groupingBy(SiteTemplateVo::getSiteId));
        }
        Map<String, List<SiteTemplateVo>> finalTemplateMap = templateMap;

//        List<SiteDefaultSettingPo> siteDefaultSettingPoList = siteDefaultSettingRwDs.getBySiteIds(siteIdList);
        Map<String, SiteOverTimeParkDTO> siteOverTimeParkDTOMap = new HashMap<>();
        List<SiteOverTimeParkDTO> siteOverTimeParkDTOList = getOverTimeParkList(
            siteIdList).getData();
        if (CollectionUtils.isNotEmpty(siteOverTimeParkDTOList)) {
            siteOverTimeParkDTOMap = siteOverTimeParkDTOList.stream()
                .collect(Collectors.toMap(SiteOverTimeParkDTO::getSiteId,
                    Function.identity(), (key1, key2) -> key2));
        }

        Map<String, SiteOverTimeParkDTO> finalSiteOverTimeParkDTOMap = siteOverTimeParkDTOMap;
        res.forEach(x -> {
            if (finalTemplateMap.containsKey(x.getSiteId())) {
                // 可能下发同一个计费模板 需要去重
                List<SiteTemplateVo> templateList = finalTemplateMap.get(x.getSiteId());
                if (CollectionUtils.isNotEmpty(templateList)) {
                    var ac = templateList.stream()
                        .filter(y -> SupplyType.AC.equals(y.getTemplateType())).findFirst();
                    var dc = templateList.stream()
                        .filter(y -> SupplyType.DC.equals(y.getTemplateType())).findFirst();
                    var both = templateList.stream()
                        .filter(y -> SupplyType.BOTH.equals(y.getTemplateType())).findFirst();
                    if (ac.isPresent() && dc.isPresent()) {  //  交直流模板 相同的情况下   只保留一个
                        if (ac.get().getTemplateId() != null && ac.get().getTemplateId()
                            .equals(dc.get().getTemplateId())) {
                            templateList = templateList.stream()
                                .filter(y -> SupplyType.AC.equals(y.getTemplateType())).map(y -> {
                                    y.setTemplateType(SupplyType.BOTH);
                                    return y;
                                }).collect(Collectors.toList());
                        }
                    } else if (ac.isPresent() && both.isPresent()) {  //  交流 +  全局
                        if (ac.get().getTemplateId() != null && ac.get().getTemplateId()
                            .equals(both.get().getTemplateId())) { // 两个模板一样
                            templateList = templateList.stream()
                                .filter(y -> SupplyType.AC.equals(y.getTemplateType())).map(y -> {
                                    y.setTemplateType(SupplyType.BOTH);
                                    return y;
                                }).collect(Collectors.toList());
                        } else {
                            templateList = templateList.stream()
                                .map(y -> {
                                    if (SupplyType.BOTH.equals(y.getTemplateType())) {
                                        y.setTemplateType(SupplyType.DC);
                                    }
                                    return y;
                                }).collect(Collectors.toList());
                        }
                    } else if (dc.isPresent() && both.isPresent()) {  //  直流 +  全局
                        if (dc.get().getTemplateId() != null && dc.get().getTemplateId()
                            .equals(both.get().getTemplateId())) { // 两个模板一样
                            templateList = templateList.stream()
                                .filter(y -> SupplyType.DC.equals(y.getTemplateType())).map(y -> {
                                    y.setTemplateType(SupplyType.BOTH);
                                    return y;
                                }).collect(Collectors.toList());
                        } else {
                            templateList = templateList.stream()
                                .map(y -> {
                                    if (SupplyType.BOTH.equals(y.getTemplateType())) {
                                        y.setTemplateType(SupplyType.AC);
                                    }
                                    return y;
                                }).collect(Collectors.toList());
                        }
                    } else {
                        templateList = templateList.stream().map(y -> {
                            y.setTemplateType(SupplyType.BOTH);
                            return y;
                        }).collect(Collectors.toList());
                    }
                    templateList = templateList.stream().collect(
                        Collectors.collectingAndThen(
                            Collectors.toCollection(() -> new TreeSet<>(
                                Comparator.comparing(o -> o.getTemplateId()))),
                            ArrayList::new));
                }
                x.setSiteTemplateVoList(templateList);
            } else if (x.getPriceCode() != null) { // 之前的 默认取t_site中配置的
                SiteTemplateVo siteTemplateVo = new SiteTemplateVo().setTemplateId(
                    x.getPriceCode()).setSiteId(x.getSiteId());
                x.setSiteTemplateVoList(List.of(siteTemplateVo));
            }
            if (finalSiteOverTimeParkDTOMap.containsKey(x.getSiteId()) && x.getParkTimeoutFee()) {
                SiteOverTimeParkDTO siteOverTimeParkDTO = finalSiteOverTimeParkDTOMap.get(
                    x.getSiteId());
                x.setSiteOverTimeParkDTO(siteOverTimeParkDTO);
            }
        });
        return new ListResponse<HlhtSiteInfoVo>(res, total);
    }

    public boolean recordEvsePlugInfo(String siteId, String unBindEvseNo) {
        //解绑相关定时充电任务
        if (StringUtils.isNotBlank(unBindEvseNo)) {
            List<SiteChargeJobPlugPo> poList = siteChargeJobPlugRoDS.getByEvseNo(
                unBindEvseNo);// 查询此枪与定时任务的绑定信息

            if (CollectionUtils.isNotEmpty(poList)) {
                siteChargeJobPlugRwDs.deleteByEvseNo(unBindEvseNo);// 从定时任务中删去此桩

                // 根据去重后的jobId,刷新枪头数
                poList.stream().map(SiteChargeJobPlugPo::getJobId).distinct().forEach(e -> {
                    Long plugNum = siteChargeJobPlugRoDS.getPlugCountByJobId(e);
                    SiteChargeJobPo po = new SiteChargeJobPo();
                    po.setId(e)
                        .setPlugNum(plugNum.intValue());
                    siteChargeJobRwDs.updateById(po);
                });
            }
        }

        //更新t_site中桩枪数量等信息
        ObjectResponse<SitePo> sitePoObjectResponse = iotDeviceMmgClient.getRecordEvsePlugInfo(
            siteId);
        FeignResponseValidate.check(sitePoObjectResponse);
        SitePo site = sitePoObjectResponse.getData();
        site.setId(siteId);
        return siteRwDs.updateSite(site);
    }

    /**
     * 2020-06升级清洗t_site中桩枪数量等信息
     *
     * @return
     */
    public boolean upgradeCleaningEvsePlugInfo() {
        ListResponse<SitePo> sitePoObjectResponse = iotDeviceMmgClient.getUpgradeCleaningEvsePlugInfo();
        FeignResponseValidate.check(sitePoObjectResponse);
        List<SitePo> sitePos = sitePoObjectResponse.getData();
        sitePos.forEach(e -> {
            e.setId(e.getSiteId());
            siteRwDs.updateSite(e);
        });
        return true;
    }

    public SitePersonaliseDTO getPersonalise(String siteId) {
        // t_site_defult_setting 中获取场站配置信息
        // t_site
        if (StringUtils.isBlank(siteId)) {
            throw new DcArgumentException("请提供场站ID值");
        }

        SitePo site = siteRoDs.getSite(siteId);
        if (site == null) {
            log.warn("场站不存在. siteId = {}", siteId);
            throw new DcArgumentException("参数错误,未找到该场站", Level.WARN);
        }

        SitePersonaliseDTO result = new SitePersonaliseDTO();
        result.setSiteId(siteId);

        // 场站相关信息初始化
        this.initSiteSetting(site, result);

        SiteDefaultSettingPo setting = siteDefaultSettingRwDs.getBySiteId(siteId);

//        result.setOvertimeParkingTimeoutEnable(setting.getOvertimeParkingTimeoutEnable());
//        if(Boolean.TRUE.equals(setting.getOvertimeParkingTimeoutEnable())) {
//            result.setOvertimeParkingLimitType(setting.getOvertimeParkingLimitType());
//            result.setOvertimeParkingChargePartType(setting.getOvertimeParkingChargePartType());
//            result.setOvertimeParkingChargeUserType(setting.getOvertimeParkingChargeUserType());
//
//            // 收/免费用户、企业
//            ObjectResponse<SitePersonaliseDTO> res = userFeignClient.getSiteParkFeeUserList(result);
//            FeignResponseValidate.check(res);
//            result.setOvertimeParkingUserIdList(res.getData().getOvertimeParkingUserIdList())
//                    .setOvertimeParkingCorpIdList(res.getData().getOvertimeParkingCorpIdList());
//        }

        if (Boolean.TRUE.equals(result.getParkTimeoutFee())) {
            if (null != setting) {
                result.setOvertimeParkingNum(setting.getOvertimeParkingNum())
                    .setOvertimeParkingTime(setting.getOvertimeParkingTime())
                    .setNoCardPayAccountId(setting.getNoCardPayAccountId());

                // 停充超时收费
                if (null != setting.getOvertimeParkingPrice() &&
                    DecimalUtils.gtZero(setting.getOvertimeParkingPrice())) {
                    result.setNeedOvertimeFee(true);
                    result.setOvertimeParkingPrice(setting.getOvertimeParkingPrice());

//                    ListResponse<SiteParkFeeUserPo> res = userFeignClient.findSiteParkFeeUserBySiteId(siteId);
//                    FeignResponseValidate.check(res);
//                    List<SiteParkFeeUserPo> userList = res.getData();
//                    result.setParkFeeUserList(
//                            userList.stream().map(SiteParkFeeUserPo::getUid)
//                                    .collect(Collectors.toList()));
                } else {
                    result.setNeedOvertimeFee(false);
                }

                final SiteOvertimeParkSettingPo overtimeSettingPo = siteOvertimeParkSettingRoDs
                    .getEnabledOneBySiteId(siteId);

                if (overtimeSettingPo != null) {
                    final List<SiteOvertimeParkDivisionPo> bySettingId =
                        siteOvertimeParkDivisionRoDs.getBySettingId(overtimeSettingPo.getId(),
                            true);

                    result.setOvertimeParkMaxFee(overtimeSettingPo.getMaxFee())
                        .setSiteOvertimeParkDivisionPoList(bySettingId);

                    result.setNeedOvertimeFee(true);
                } else {
                    result.setNeedOvertimeFee(false);
                }

                result.setOvertimeParkingLimitType(setting.getOvertimeParkingLimitType());
                result.setOvertimeParkingChargePartType(setting.getOvertimeParkingChargePartType());
                result.setOvertimeParkingChargeUserType(setting.getOvertimeParkingChargeUserType());

                // 收/免费用户、企业
                ObjectResponse<SitePersonaliseDTO> res = userFeignClient.getSiteParkFeeUserList(
                    result);
                FeignResponseValidate.check(res);
                result.setOvertimeParkingUserIdList(res.getData().getOvertimeParkingUserIdList())
                    .setOvertimeParkingCorpIdList(res.getData().getOvertimeParkingCorpIdList());
            }
        }

        if (setting != null) {
            result.setNoCardPayAccountId(setting.getNoCardPayAccountId());
            result.setNoCardPayAccountType(setting.getNoCardPayAccountType());

            //无卡自动结账开启
            if (null != setting.getNoCardPayAccountType() && !setting.getNoCardPayAccountType()
                .equals(0)) {
                PayAccountType accountType = PayAccountType.valueOf(
                    setting.getNoCardPayAccountType());
                switch (accountType) {
                    case COMMERCIAL:
                        reactorUserFeignClient.queryCommCusRefs(
                                null, null, null,
                                null, site.getOperateId(), setting.getNoCardPayAccountId(),
                                null, null)
                            .doOnNext(FeignResponseValidate::check)
                            .map(ListResponse::getData)
                            .filter(data -> data.size() == 1)
                            .map(data -> data.get(0))
                            .flatMap(commUser -> asyncCusBalanceService.getPoint2(
                                    PayAccountType.COMMERCIAL,
                                    site.getTopCommId(), commUser.getCommId(), commUser.getUserId())
                                .switchIfEmpty(
                                    Mono.just(new PointPo().setAvailable(BigDecimal.ZERO)))
                                .map(point -> NoCardPayAccountInfo.builder()
                                    .accountId(result.getNoCardPayAccountId())
                                    .accountType(result.getNoCardPayAccountType())
                                    .cusName(commUser.getUserName())
                                    .cusPhone(commUser.getUserPhone())
                                    .uid(commUser.getUserId())
                                    .available(point.getAvailable())
                                    .enable(commUser.getEnable())
                                    .cusStatus(commUser.getUserStatus())
                                    .commId(commUser.getCommId())
                                    .commName(commUser.getCommName())
                                    .build()))
                            .doOnNext(result::setNoCardPayAccountInfo)
                            .block(Duration.ofSeconds(50L));
                        break;
                    case CREDIT:
                        // 获取授信账户的信息
                        reactorUserFeignClient.findRBlocUserVoById(setting.getNoCardPayAccountId())
                            .doOnNext(FeignResponseValidate::check)
                            .map(ObjectResponse::getData)
                            .map(corpUser -> {
                                // 获取用户信息
                                CusRepVo user = userSyncRoDs.getUserByUid(corpUser.getUserId());

                                // 企业信息
                                CorpPo corp = corpRoDs.getCorpById(corpUser.getBlocUserId());

                                return NoCardPayAccountInfo.builder()
                                    .accountId(result.getNoCardPayAccountId())
                                    .accountType(result.getNoCardPayAccountType())
                                    .cusName(corpUser.getUserName())
                                    .cusPhone(corpUser.getPhone())
                                    .uid(corpUser.getUserId())
                                    .cusStatus(user.getStatus())
                                    .enable(corpUser.getStatus())
                                    .available(corpUser.calCorpUserAvailableAmount())
                                    .corpId(corpUser.getBlocUserId())
                                    .settlementType(null != corp ? corp.getSettlementType() : null)
                                    .commId(corpUser.getCommId())
                                    .commName(corpUser.getCommName())
                                    .build();
                            })
                            .doOnNext(result::setNoCardPayAccountInfo)
                            .block(Duration.ofSeconds(50L));
                        break;
                    default:
                        throw new DcServiceException("无卡扣款账号类型不正确");
                }
            }
//            if (setting.getNoCardPayAccountId() != null &&
//                    setting.getNoCardPayAccountId().intValue() > 0) {
////                SitePo site = siteRoDs.getSite(siteId);
////                IotAssert.isNotNull(site, "场站信息不存在");
//                ListResponse<CommCusRef> commCusRefListResponse = userFeignClient.queryCommCusRefs(
//                        null, null, null,
//                        null, site.getOperateId(), setting.getNoCardPayAccountId(),
//                        null, null);
//
//                FeignResponseValidate.check(commCusRefListResponse);
//
//                if (commCusRefListResponse.getData().size() != 1) {
//                    log.warn("(无卡支付)商户会员个数不正确，不做处理: {}", commCusRefListResponse);
//                } else {
//                    CommCusRef commCusRef = commCusRefListResponse.getData().get(0);
//
//                    ListResponse<PointPo> res = dcCusBalanceService.queryPointPo(PayAccountType.COMMERCIAL,
//                            List.of(setting.getNoCardPayAccountId()),
//                            site.getTopCommId(), commCusRef.getCommId(), true, null);
//                    FeignResponseValidate.check(res);
//
//                    CommCusRefVo commCusRefVo = new CommCusRefVo();
//
//                    BeanUtils.copyProperties(commCusRef, commCusRefVo);
//                    commCusRefVo.setAvailable(BigDecimal.ZERO);
//                    if (!res.getData().isEmpty()) {
//                        List<PointPo> pointList = res.getData().stream()
//                                .filter(e -> e.getSubUid().equalsIgnoreCase(commCusRef.getCommId().toString()))
//                                .collect(Collectors.toList());
//                        if (pointList.isEmpty()) {
//                            log.error("获取商户会员支付信息失败.");
//                        } else if (pointList.size() > 1) {
//                            log.error("存在多个商户会员支付信息");
//                        } else {
//                            PointPo pointPo = pointList.get(0);
//                            commCusRefVo.setAvailable(pointPo.getAvailable());
//                        }
//                    }
//
//                    result.setNoCardChargeMerchant(commCusRefVo);
//
////                    IotAssert.isTrue(!res.getData().isEmpty(), "获取商户会员支付信息失败");
////                    List<PointPo> pointList = res.getData().stream()
////                            .filter(e -> e.getSubUid().equalsIgnoreCase(commCusRef.getCommId().toString()))
////                            .collect(Collectors.toList());
////                    IotAssert.isTrue(!pointList.isEmpty(), "获取商户会员支付信息失败.");
////                    IotAssert.isTrue(pointList.size() == 1, "存在多个商户会员支付信息");
////
////                    PointPo point Po = pointList.get(0);
////                    CommCusRefVo commCusRefVo = new CommCusRefVo();
////
////                    BeanUtils.copyProperties(commCusRef, commCusRefVo);
////
////                    commCusRefVo.setAvailable(pointPo.getAvailable());
////
////                    result.setNoCardChargeMerchant(commCusRefVo);
//
//                }
//            }
            result.setLimitSoc(setting.getLimitSoc())
                .setParkCouponKwh(setting.getParkCouponKwh())
                .setParkCouponTime(setting.getParkCouponTime());
        }

//        result.setSocLimit(siteLimitSocRoDs.selectLimitList(siteId));
//        result.setSocLimitTimes(siteLimitSocRoDs.selectLimitTimeList(siteId));
//        if (setting != null) {
//            result.setLimitSoc(setting.getLimitSoc() == null ? Boolean.FALSE : setting.getLimitSoc());
//        } else {
//            result.setLimitSoc(Boolean.FALSE);
//        }

        // soc默认策略
        result.setSiteDefaultSocCfg(siteSocCfgRoDs.getSiteDefaultSocCfg(siteId));

        // soc优先策略
        result.setSocPriorityStrategyList(socStrategyRoDs.getSocPriorityStrategyList(siteId, null));

        log.info("获取场站个性化设置: {}", result);
        return result;
    }

    private void initSiteSetting(SitePo site, SitePersonaliseDTO result) {
//        SitePo site = siteRoDs.getSite(result.getSiteId());
//        if(site == null) {
//            log.warn("场站不存在. siteId = {}", result.getSiteId());
//            throw new DcArgumentException("参数错误,未找到该场站", Level.WARN);
//        }
        result.setFrozenAmount(site.getFrozenAmount());
        result.setParkTimeoutFee(site.getParkTimeoutFee());
        result.setNoCardPayAccountId(0L);
        result.setStopSoc(site.getStopSoc());

        // 初始化场站启动信息
        switch (PayAccountType.valueOf(site.getDefaultPayType())) {
            case PERSONAL:
                ObjectResponse<UserVo> user = userFeignClient.findInfoByUid(
                    site.getPayAccountId(), null, null);
                FeignResponseValidate.check(user);
                result.setStartCharingEnable(true);
                result.setSettlementMethod(2);
                result.setPayType(PayAccountType.PERSONAL);
                result.setPhone(user.getData().getPhone());
                break;
            case CREDIT:
                ObjectResponse<RBlocUserVo> rBlocUser = userFeignClient.findRBlocUserVoById(
                    site.getPayAccountId());
                FeignResponseValidate.check(rBlocUser);
                RBlocUserVo rBlocUserVo = rBlocUser.getData();
                result.setStartCharingEnable(true);
                result.setSettlementMethod(2);
                result.setPayType(PayAccountType.CREDIT);
                result.setBlocUserId(rBlocUserVo.getBlocUserId());
                result.setBlocUserName(rBlocUserVo.getBlocUserName());
                result.setCorpUserId(rBlocUserVo.getId());
                result.setCorpUserName(rBlocUserVo.getName());
                result.setCommId(rBlocUserVo.getCommId());
                result.setCommName(rBlocUserVo.getCommName());
                result.setPhone(rBlocUserVo.getPhone());
                break;
            case COMMERCIAL:
                ObjectResponse<CommCusRef> commCusRefObjectResponse = userFeignClient.merFindById(
                    site.getPayAccountId());
                FeignResponseValidate.check(commCusRefObjectResponse);
                CommCusRef commCusRef = commCusRefObjectResponse.getData();
                result.setStartCharingEnable(true);
                result.setSettlementMethod(2);
                result.setPayType(PayAccountType.COMMERCIAL);
                result.setCommUserName(commCusRef.getUserName());
                result.setCommId(commCusRef.getCommId());
                result.setCommName(commCusRef.getCommName());
                result.setPhone(commCusRef.getUserPhone());
                break;
            case UNKNOWN:
                result.setStartCharingEnable(false);
                result.setStopSoc(null);
                break;
            case OTHER:
                result.setStartCharingEnable(true);
                result.setSettlementMethod(1);
                break;
            default:
                log.warn("场站后台充电-扣款账户默认扣款类型异常, defaultPayType:{}",
                    site.getDefaultPayType());
        }
    }

    public ListResponse<SiteSimpleDto> getSiteListByIdChain(String idChain) {
        List<SiteSimpleDto> list = siteRoDs.getSiteListByIdChain(idChain);
        return new ListResponse<>(list);
    }

    @Transactional
    public int updatePersonalise(SitePersonaliseDTO dto) {
        SitePersonaliseDTO.checkValue(dto);

        SitePo site = new SitePo();
        site.setId(dto.getSiteId());
        if (SITE_SETTING_1.equals(dto.getTag())) {
            // t_site
            site.setFrozenAmount(dto.getFrozenAmount());
            siteRwDs.updateSite(site);
        }

        boolean siteDefaultSettingModify = false;
        SiteDefaultSettingPo setting = new SiteDefaultSettingPo();
        setting.setSiteId(dto.getSiteId());
        if (SITE_SETTING_2.equals(dto.getTag())) {
            // TODO 停充收费设定
            // t_site_defult_setting 中获取场站配置信息
            if (dto.getParkTimeoutFee()) {
                setting.setOvertimeParkingNum(dto.getOvertimeParkingNum());
                setting.setOvertimeParkingTime(dto.getOvertimeParkingTime());
//                siteDefaultSettingModify = true;
            }

            if (dto.getParkTimeoutFee() != null) {
                setting//.setOvertimeParkingTimeoutEnable(dto.getOvertimeParkingTimeoutEnable())
                    .setOvertimeParkingLimitType(dto.getOvertimeParkingLimitType())
                    .setOvertimeParkingChargePartType(dto.getOvertimeParkingChargePartType())
                    .setOvertimeParkingChargeUserType(dto.getOvertimeParkingChargeUserType());

                IotAssert.isTrue(
                    CollectionUtils.isNotEmpty(dto.getSiteOvertimeParkDivisionPoList()) &&
                        dto.getOvertimeParkMaxFee() != null, "请设置占位费时段和单次封顶金额");

                IotAssert.isTrue(dto.getSiteOvertimeParkDivisionPoList()
                    .stream()
                    .map(SiteOvertimeParkDivisionPo::getFee)
                    .noneMatch(Objects::isNull), "请填写完整超时收费单价");

                final String errorMsg = siteOvertimeParkDivisionRoDs
                    .isValidate(dto.getSiteOvertimeParkDivisionPoList());
                IotAssert.isNull(errorMsg, errorMsg);

                final SiteOvertimeParkSettingPo settingPo =
                    siteOvertimeParkSettingRoDs.getEnabledOneBySiteId(dto.getSiteId());

                if (settingPo != null) {
                    log.info("存在占位配置: {}", settingPo);
                    SiteOvertimeParkSettingPo updateSiteOvertimeParkSettingPo = new SiteOvertimeParkSettingPo();
                    updateSiteOvertimeParkSettingPo.setId(settingPo.getId()).setEnable(false);
                    log.info("禁用停充配置: {}", siteOvertimeParkSettingRwDs
                        .updateSiteOvertimeParkSetting(updateSiteOvertimeParkSettingPo));

                    log.info("禁用分时个数: {}",
                        siteOvertimeParkDivisionRwDs.disableBySettingId(settingPo.getId()));
                }
                SiteOvertimeParkSettingPo parkSettingPo = new SiteOvertimeParkSettingPo();
                parkSettingPo.setEnable(true)
                    .setSiteId(dto.getSiteId())
                    .setMaxFee(dto.getOvertimeParkMaxFee());
                siteOvertimeParkSettingRwDs.insertSiteOvertimeParkSetting(parkSettingPo);
                log.info("新增占位配置id: {}", parkSettingPo.getId());

                int parkDivisionInsertCount = siteOvertimeParkDivisionRwDs.batchInsert(
                    dto.getSiteOvertimeParkDivisionPoList()
                        .stream()
                        .map(e -> e.setSettingId(parkSettingPo.getId()).setEnable(true))
                        .collect(Collectors.toList()));
                log.info("新增占位分时个数：{}", parkDivisionInsertCount);

                if (dto.getParkTimeoutFee() && OvertimeParkingChargePartType.PART.equals(
                    dto.getOvertimeParkingChargePartType())) {

                    IotAssert.isTrue(
                        CollectionUtils.isNotEmpty(dto.getOvertimeParkingUserIdList()) ||
                            CollectionUtils.isNotEmpty(dto.getOvertimeParkingCorpIdList()),
                        "请选择" + dto.getOvertimeParkingChargeUserType().getDesc() + "用户列表");
                }

                // 收/免费用户、企业
                if (OvertimeParkingLimitType.BLACKLIST.equals(dto.getOvertimeParkingLimitType()) ||
                    OvertimeParkingChargePartType.ALL.equals(
                        dto.getOvertimeParkingChargePartType())) {
                    log.info("设置为黑名单/收费用户全部时，清空普通用户/企业客户列表");
                    dto.setOvertimeParkingUserIdList(List.of());
                    dto.setOvertimeParkingCorpIdList(List.of());
                }
                ObjectResponse<Integer> integerObjectResponse = userFeignClient.updateSiteParkFeeUserList(
                    dto);
                FeignResponseValidate.check(integerObjectResponse);
            }

            // t_site
            site.setId(dto.getSiteId())
                .setParkTimeoutFee(dto.getParkTimeoutFee());
            siteRwDs.updateSite(site);

            if (dto.getNeedOvertimeFee() != null && dto.getNeedOvertimeFee()) {
                setting.setOvertimeParkingPrice(dto.getOvertimeParkingPrice());
            } else {
                setting.setOvertimeParkingPrice(BigDecimal.ZERO);
            }

            siteDefaultSettingModify = true;

            // 超停收费用户列表
//            UpdateSiteParkFeeUserParam param = new UpdateSiteParkFeeUserParam();
//            param.setSiteId(dto.getSiteId())
//                    .setUidList(dto.getParkFeeUserList());
//            ObjectResponse<Integer> response = userFeignClient.updateSiteParkFeeUser(param);
//            FeignResponseValidate.check(response);
        }

        if (SITE_SETTING_3.equals(dto.getTag())) {

            // 后台启动设置相关
            if (!dto.getStartCharingEnable()) {
                dto.setPayType(PayAccountType.UNKNOWN);
            } else if (dto.getSettlementMethod() == 1) {
                dto.setPayType(PayAccountType.OTHER);
            } // else: payType must not be null

            switch (dto.getPayType()) {
                case PERSONAL:
                    ObjectResponse<UserPropVO> user = userFeignClient.findByPhone(dto.getPhone(),
                        dto.getTopCommId());
                    if (null == user || user.getData() == null) {
                        throw new DcArgumentException("保存失败，当前手机号对应账户不存在");
                    }
//                FeignResponseValidate.check(user);
                    site.setPayAccountId(user.getData().getUserId());
                    site.setDefaultPayType(PayAccountType.PERSONAL.getCode());
                    break;
                case CREDIT:
                    site.setPayAccountId(dto.getCorpUserId());
                    site.setDefaultPayType(PayAccountType.CREDIT.getCode());
                    break;
                case COMMERCIAL:
                    ObjectResponse<CommCusRef> commCusRef = userFeignClient.findByCommIdAndPhone(
                        dto.getCommId(), dto.getPhone());
                    if (null == commCusRef || commCusRef.getData() == null) {
                        throw new DcArgumentException("保存失败，当前手机号对应账户不存在");
                    }
//                FeignResponseValidate.check(commCusRef);
                    CommCusRef ref = commCusRef.getData();
                    site.setPayAccountId(ref.getId());
                    site.setDefaultPayType(PayAccountType.COMMERCIAL.getCode());
                    break;
                case OTHER:
                    site.setPayAccountId(0L);
                    site.setDefaultPayType(PayAccountType.OTHER.getCode());
                    break;
                case UNKNOWN:
                    site.setPayAccountId(0L);
                    site.setDefaultPayType(PayAccountType.UNKNOWN.getCode());
                    break;
            }

            // 停充SOC
            if (null != dto.getStopSoc() &&
                !PayAccountType.UNKNOWN.equals(dto.getPayType())) {
                IotAssert.isTrue(dto.getStopSoc() > 0 &&
                    dto.getStopSoc() <= 100, "停充SOC无效");
                site.setStopSoc(dto.getStopSoc());
            } else {
                site.setStopSoc(0);
            }

            siteRwDs.updateSite(site);
        }

//        if (dto.getLimitSoc() != null) {
//            setting.setSiteId(dto.getSiteId());
//            setting.setLimitSoc(dto.getLimitSoc());
//            siteDefaultSettingModify = true;
//        }

        if (SITE_SETTING_5.equals(dto.getTag()) && dto.getNoCardPayAccountId() != null) {
            this.updateNoCardPayAccountInfo(setting, dto);
            siteDefaultSettingModify = true;
        }

        if (SITE_SETTING_6.equals(dto.getTag())) {
            SitePo station = siteRoDs.getSite(setting.getSiteId());
            if (station == null || FREE_PARK_LIST.contains(station.getPark())) {
                log.info("场站当前设置为免停车费，禁用停车减免。");
                setting.setParkCouponKwh(0);
                setting.setParkCouponTime(0);
                siteDefaultSettingModify = true;
            } else {
                setting.setParkCouponKwh(dto.getParkCouponKwh());
                setting.setParkCouponTime(dto.getParkCouponTime());
                siteDefaultSettingModify = true;
            }
        }

        if (siteDefaultSettingModify) {
            siteDefaultSettingRwDs.updateBySiteId(setting);
        }

//        if (dto.getSocLimit() != null) {
//            siteLimitSocRwDs.deleteAllSocLimitBySiteId(dto.getSiteId());
//            dto.getSocLimit().stream().forEach(e -> {
//                e.setSiteId(dto.getSiteId());
//                siteLimitSocRwDs.insertSocLimit(e);
//            });
//        }

//        if (dto.getSocLimitTimes() != null) {
//            siteLimitSocRwDs.deleteAllSocLimitTime(dto.getSiteId());
//            dto.getSocLimitTimes().stream().forEach(e -> {
//                e.setSiteId(dto.getSiteId());
//                siteLimitSocRwDs.insertSocLimitTime(e);
//            });
//        }

//        if (false && SITE_SETTING_4.equals(dto.getTag())) {
//
//            if (dto.getSiteDefaultSocCfg() != null) {
//                if (dto.getSiteDefaultSocCfg().getSiteSocCfg() != null) {
//                    log.info("修改场站默认soc限制: {}", JsonUtils.toJsonString(dto.getSiteDefaultSocCfg().getSiteSocCfg()));
//                    dto.getSiteDefaultSocCfg().getSiteSocCfg().setSiteId(dto.getSiteId());
//                    siteSocCfgRwDs.insertOrUpdate(dto.getSiteDefaultSocCfg().getSiteSocCfg());
//                }
//                if (dto.getSiteDefaultSocCfg().getTimeList() != null) {
//                    log.info("修改场站默认soc限制, 时间列表: {}",
//                            JsonUtils.toJsonString(dto.getSiteDefaultSocCfg().getTimeList()));
//                    log.info("删除旧时间列表: {}", siteSocStrategyRwDs.deleteBySiteId(dto.getSiteId()));
//
//                    final List<SiteSocStrategyPo> batchList = dto.getSiteDefaultSocCfg()
//                            .getTimeList()
//                            .stream()
//                            .map(e -> {
//                                IotAssert.isNotBlank(e.getStartTimeStr(), "开始时间串不能为空");
//                                IotAssert.isNotBlank(e.getEndTimeStr(), "结束时间串不能为空");
//
//                                e.setStartTime(DateUtil.timeToInt(e.getStartTimeStr()));
//                                e.setEndTime(DateUtil.timeToInt(e.getEndTimeStr()));
//
//                                e.setSiteId(dto.getSiteId());
//                                IotAssert.isNotNull(e.getStartTime(), "开始时间不能为空");
//                                IotAssert.isNotNull(e.getEndTime(), "结束时间不能为空");
//                                IotAssert.isNotNull(e.getAllow(), "允许充电标记不能为空");
//                                return e;
//                            })
//                            .sorted(Comparator.comparing(SiteSocStrategyPo::getStartTime))
//                            .collect(Collectors.toList());
//
//                    if (CollectionUtils.isNotEmpty(batchList)) {
//                        log.info("插入新时间列表: {}", siteSocStrategyRwDs.batchInsert(batchList));
//                    } else {
//                        log.info("未插入新时间列表");
//                    }
//                }
//            }
//
//            if (dto.getSocPriorityStrategyList() != null) {
//
//                log.info("清空场站: {} 所有用户的模板关系, 删除了{}项",
//                        dto.getSiteId(), userSocStrategyRwDs.deleteBySiteId(dto.getSiteId()));
//
//                dto.getSocPriorityStrategyList().stream().forEach(e -> {
//
//                    log.info("删除模板用户关系: {}", e.getUserIdList());
//                    userSocStrategyRwDs.deleteByUserIdList(e.getUserIdList());
//
//                    if (e.getSocStrategyDict() != null) {
//
//                        IotAssert.isNotNull(e.getSocStrategyDict().getSocStrategy(), "请传入优先策略");
//                        IotAssert.isTrue(CollectionUtils.isNotEmpty(e.getUserIdList()), "请传入优先策略时间列表");
//
//                        e.getSocStrategyDict().getSocStrategy().setSiteId(dto.getSiteId());
//                        log.info("创建新策略: {}", JsonUtils.toJsonString(e.getSocStrategyDict().getSocStrategy()));
//                        IotAssert.isTrue(socStrategyRwDs.insertSocStrategy(e.getSocStrategyDict().getSocStrategy()),
//                                "创建策略失败");
//                        log.info("创建新策略成功");
//
//                        IotAssert.isTrue(CollectionUtils.isNotEmpty(e.getSocStrategyDict().getTimeList()),
//                                "请传入策略对应时间列表");
//                        e.getSocStrategyDict()
//                                .getTimeList()
//                                .stream()
//                                .forEach(s -> {
//                                    s.setStrategyId(e.getSocStrategyDict().getSocStrategy().getId());
//
//                                    IotAssert.isNotBlank(s.getStartTimeStr(), "开始时间串不能为空");
//                                    IotAssert.isNotBlank(s.getEndTimeStr(), "结束时间串不能为空");
//
//                                    s.setStartTime(DateUtil.timeToInt(s.getStartTimeStr()));
//                                    s.setEndTime(DateUtil.timeToInt(s.getEndTimeStr()));
//
//                                    IotAssert.isNotNull(s.getStartTime(), "开始时间不能为空");
//                                    IotAssert.isNotNull(s.getEndTime(), "结束时间不能为空");
//                                });
//
//                        log.info("创建策略对应时间: {}", JsonUtils.toJsonString(e.getSocStrategyDict().getTimeList()));
//
//                        IotAssert.isTrue(userSocTimeRwDs.batchInsert(e.getSocStrategyDict().getTimeList()) ==
//                                        e.getSocStrategyDict().getTimeList().size(),
//                                "创建优先策略时间列表失败");
//
//                        IotAssert.isTrue(userSocStrategyRwDs.batchInsert(e.getUserIdList().stream().map(u -> {
//                            UserSocStrategyPo ret = new UserSocStrategyPo();
//                            return ret.setUserId(u).setStrategyId(e.getSocStrategyDict().getSocStrategy().getId());
//                        }).collect(Collectors.toList())) == e.getUserIdList().size(), "新增模板用户关系失败");
//
//                    } else {
//                        log.info("删除用户数据且不创建新的模板信息");
//                    }
//
//                });
//
//                //TODO 回收未使用的模板
//
//            }
//        }

        return 1; // 避免编译器优化
    }

    /**
     * 场站无卡启动充电订单结算账户更新
     *
     * @param setting
     * @param dto
     */
    private void updateNoCardPayAccountInfo(SiteDefaultSettingPo setting, SitePersonaliseDTO dto) {
        log.info("场站无卡启动订单结算账户调整: siteId = {}", setting.getSiteId());
        if (null == dto.getNoCardPayAccountType() ||
            null == dto.getNoCardPayAccountId() ||
            dto.getNoCardPayAccountId().intValue() < 0) {
            log.warn("无卡启动订单提交数据缺少结算账户信息");
            throw new DcArgumentException("无卡启动订单结算账户信息无效");
        }

        PayAccountType accountType = PayAccountType.valueOf(dto.getNoCardPayAccountType());
        switch (accountType) {
            case COMMERCIAL: // 商户会员
                SitePo modiSite = siteRoDs.getSite(dto.getSiteId());
                IotAssert.isNotNull(modiSite, "找不到目标场站");
                IotAssert.isNotNull(modiSite.getOperateId(), "场站所属商户不能为空");

                ListResponse<CommCusRef> commCusRefListResponse = userFeignClient.queryCommCusRefs(
                    null,
                    null,
                    null,
                    true,
                    modiSite.getOperateId(),
                    dto.getNoCardPayAccountId(), null, null);

                FeignResponseValidate.check(commCusRefListResponse);

                IotAssert.isTrue(!commCusRefListResponse.getData().isEmpty(),
                    "未找到对应的商户" + modiSite.getOperateId() + "的会员"
                        + dto.getNoCardPayAccountId());
                setting.setNoCardPayAccountId(dto.getNoCardPayAccountId());
                setting.setNoCardPayAccountType(accountType.getCode());
                break;
            case CREDIT: // 授信账户
                setting.setNoCardPayAccountId(dto.getNoCardPayAccountId());
                setting.setNoCardPayAccountType(accountType.getCode());
                break;
            case UNKNOWN: //关闭
                setting.setNoCardPayAccountId(0L);
                setting.setNoCardPayAccountType(accountType.getCode());
                break;
            default:
                throw new DcArgumentException("无卡启动订单结算账户类型无效");
        }
    }

    @Transactional
    public int createCorpSocStrategy(SocStrategyDict param) {

        param.getSocStrategy().setSiteId("");
        param.getSocStrategy().setSoc(0);
        param.getSocStrategy().setAllow(true);

        IotAssert.isTrue(socStrategyRwDs.insertSocStrategy(param.getSocStrategy()),
            "创建策略失败");

        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getTimeList()),
            "请传入策略对应时间列表");
        this.batchInsertCorpStrategyTime(param.getTimeList(), param.getSocStrategy().getId());

        return 1;
    }

    @Transactional
    public int updateCorpStrategy(SocStrategyDict param) {
        IotAssert.isNotNull(param.getSocStrategy().getId(), "请传入策略id");

        IotAssert.isTrue(socStrategyRwDs.updateSocStrategy(param.getSocStrategy()),
            "修改策略失败");

        log.info("删除了策略时间段: {} 个",
            userSocTimeRwDs.deleteByStrategyId(param.getSocStrategy().getId()));
        this.batchInsertCorpStrategyTime(param.getTimeList(), param.getSocStrategy().getId());

        return 1;
    }

    @Transactional
    public int deleteCorpStrategy(Long id) {
        IotAssert.isNotNull(id, "请传入策略id");

        IotAssert.isTrue(socStrategyRwDs.deleteSocStrategy(id),
            "删除策略失败");

        log.info("删除了策略时间段: {} 个", userSocTimeRwDs.deleteByStrategyId(id));

        log.info("删除了用户充电限制策略: {} 个", userSocStrategyRwDs.deleteByStrategyId(id));

        return 1;
    }

    private void batchInsertCorpStrategyTime(List<UserSocTimePo> list, Long strategyId) {
        list.stream()
            .forEach(s -> {
                s.setStrategyId(strategyId);

                IotAssert.isNotBlank(s.getStartTimeStr(), "开始时间串不能为空");
                IotAssert.isNotBlank(s.getEndTimeStr(), "结束时间串不能为空");

                s.setStartTime(DateUtil.timeToInt(s.getStartTimeStr()));
                s.setEndTime(DateUtil.timeToInt(s.getEndTimeStr()));

                IotAssert.isNotNull(s.getStartTime(), "开始时间不能为空");
                IotAssert.isNotNull(s.getEndTime(), "结束时间不能为空");
            });

        log.info("创建策略对应时间: {}", JsonUtils.toJsonString(list));

        IotAssert.isTrue(userSocTimeRwDs.batchInsert(list) == list.size(),
            "创建优先策略时间列表失败");
    }

    public ListResponse<DataSyncOvertimeParkFeeFlag> getSyncParamData(List<String> siteIdList) {

        return RestUtils.buildListResponse(siteRoDs.getSyncParamData(siteIdList));
    }

    public SiteConfStartList getMoveCorpSiteConfStart(Long corpId, Long commId) {
        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");

        SiteConfStartList ret = new SiteConfStartList();

        List<SiteVo> siteList = siteRoDs.getSiteListByCorpId(corpId);
        if (CollectionUtils.isNotEmpty(siteList)) {
            CommPo commercialById = commRoDs.getCommById(commId);
            IotAssert.isNotNull(commercialById, "找不到对应商户: " + commId);

            // 后台启动设置删除列表
            List<SiteVo> siteConfRemoveList = siteList.stream()
                .filter(e -> e.getIdChain().indexOf(commercialById.getIdChain()) != 0)
                .collect(Collectors.toList());
            ret.setRemoveList(siteConfRemoveList);

            // 后台启动设置保留列表
            List<SiteVo> siteConfRemainList = siteList.stream()
                .filter(e -> e.getIdChain().indexOf(commercialById.getIdChain()) == 0)
                .collect(Collectors.toList());
            ret.setRemainList(siteConfRemainList);

            // 位于 remove 列表的相同元素需要从 remain 中剔除
            Set<String> removeSet = ret.getRemoveList().stream().map(SiteVo::getId)
                .collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(removeSet) && CollectionUtils.isNotEmpty(
                ret.getRemainList())) {
                List<SiteVo> collect = ret.getRemainList()
                    .stream()
                    .filter(e -> !removeSet.contains(e.getId()))
                    .collect(Collectors.toList());
                ret.setRemainList(collect);
            }

        }

        return ret;
    }

    public Long moveCorpSiteConfStart(Long corpId, Long commId) {
        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");

        CommPo commercialById = commRoDs.getCommById(commId);
        IotAssert.isNotNull(commercialById, "找不到对应商户: " + commId);
        IotAssert.isNotBlank(commercialById.getIdChain(), "找不到该企业idChain");

        Long ret = siteRwDs.moveCorpSiteConfStart(corpId, commercialById.getIdChain());
        log.info("移除场站后台启动配置项: {}个", ret);
        return ret;

    }

    public MoveCorpUserSocStrategyList getMoveCorpSoc(Long corpId, Long commId) {
        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");

        MoveCorpUserSocStrategyList ret = new MoveCorpUserSocStrategyList();

        List<MoveCorpUserSocStrategyVo> strategyList = userSocStrategyRoDs.getByCorpId(corpId);
        if (CollectionUtils.isNotEmpty(strategyList)) {
            CommPo commercialById = commRoDs.getCommById(commId);
            IotAssert.isNotNull(commercialById, "找不到对应商户: " + commId);

            List<MoveCorpUserSocStrategyVo> removeList = strategyList.stream()
                .filter(e -> e.getIdChain().indexOf(commercialById.getIdChain()) != 0)
                .collect(Collectors.toList());
            ret.setRemoveList(removeList);

            List<MoveCorpUserSocStrategyVo> remainList = strategyList.stream()
                .filter(e -> e.getIdChain().indexOf(commercialById.getIdChain()) == 0)
                .collect(Collectors.toList());
            ret.setRemainList(remainList);

            // 位于 remove 列表的相同元素需要从 remain 中剔除
            Set<String> removeSet = ret.getRemoveList().stream()
                .map(MoveCorpUserSocStrategyVo::getSiteId).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(removeSet) && CollectionUtils.isNotEmpty(
                ret.getRemainList())) {
                List<MoveCorpUserSocStrategyVo> collect = ret.getRemainList()
                    .stream()
                    .filter(e -> !removeSet.contains(e.getSiteId()))
                    .collect(Collectors.toList());
                ret.setRemainList(collect);
            }

        }

        return ret;
    }

    public MoveCorpNoCardList getMoveCorpNoCard(Long corpId, Long commId) {
        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");

        MoveCorpNoCardList ret = new MoveCorpNoCardList();
        List<MoveCorpNoCardVo> corpNoCardList = siteRoDs.getCorpNoCardList(corpId);
        if (CollectionUtils.isNotEmpty(corpNoCardList)) {
            CommPo commercialById = commRoDs.getCommById(commId);
            IotAssert.isNotNull(commercialById, "找不到对应商户: " + commId);

            List<MoveCorpNoCardVo> removeList = corpNoCardList.stream()
                .filter(e -> e.getCommIdChain().indexOf(commercialById.getIdChain()) != 0)
                .collect(Collectors.toList());
            ret.setRemoveList(removeList);

            List<MoveCorpNoCardVo> remainList = corpNoCardList.stream()
                .filter(e -> e.getCommIdChain().indexOf(commercialById.getIdChain()) == 0)
                .collect(Collectors.toList());
            ret.setRemainList(remainList);
        }

        return ret;
    }

    public Long moveCorpNoCard(Long corpId, Long commId) {
        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");
        CommPo commercialById = commRoDs.getCommById(commId);
        IotAssert.isNotNull(commercialById, "找不到对应商户: " + commId);
        IotAssert.isNotBlank(commercialById.getIdChain(), "找不到该商户idChain");
        List<MoveCorpNoCardVo> corpNoCardList = siteRoDs.getCorpNoCardList(corpId);
        if (CollectionUtils.isNotEmpty(corpNoCardList)) {
            List<String> siteIdList = corpNoCardList.stream()
                .filter(e -> e.getCommIdChain().indexOf(commercialById.getIdChain()) != 0)
                .map(MoveCorpNoCardVo::getSiteId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(siteIdList)) {
                siteDefaultSettingRwDs.updateBySiteIdList(siteIdList);
            }
        }
        return 1L;
    }

    public Long moveCorpSoc(Long corpId, Long commId) {
        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");

        CommPo commercialById = commRoDs.getCommById(commId);
        IotAssert.isNotNull(commercialById, "找不到对应商户: " + commId);
        IotAssert.isNotBlank(commercialById.getIdChain(), "找不到该企业idChain");

        Long ret = userSocStrategyRwDs.moveCorpSoc(corpId, commercialById.getIdChain());

        log.info("移除场站soc限制配置项：{}个", ret);

        return ret;

    }

    /**
     * 根据站点id获取站点下各状态插座数量统计
     *
     * @param siteId
     * @return
     */
    public PlugStatusCountDto getChargerStatusStatisticsBySiteId(String siteId) {
        log.info("根据站点ID获取充电接口状态统计信息[siteId={}]", siteId);
        PlugStatusCountDto dto = new PlugStatusCountDto();
        List<PlugVo> plugCacheList = new ArrayList<>();
        SitePo po = siteRoDs.getSite(siteId);
        if (po.getBizType() == BizType.HLHT.getCode()) {
            List<PlugVo> plugVoList = bsChargerRoDs.getPlugList(siteId);
            if (CollectionUtils.isEmpty(plugVoList)) {
                return dto;
            }
            plugVoList.forEach(e -> {
                PlugVo redisPlugVo = redisIotReadService.getPlugRedisCache(e.getPlugNo());
                if (redisPlugVo != null) {
                    e.setStatus(redisPlugVo.getStatus());
                } else {
                    e.setStatus(PlugStatus.UNKNOWN);
                }
                this.dealWith(dto, e.getStatus());
            });
            dto.setPlugTotal(NumberUtils.sum(po.getAcPlugNum(), po.getDcPlugNum()));
            dto.setChargeJobCount(siteChargeJobPlugRoDS.getSiteJobCount(siteId));
            dto.setChargeJobPlugCount(siteChargeJobPlugRoDS.getSiteJobPlugCount(siteId));
            return dto;
        }

        Set<String> evseNos = redisIotReadService.getSiteEvses(siteId);
        Set<String> plugNos = redisIotReadService.getSitePlugs(siteId);
        if (CollectionUtils.isEmpty(evseNos) || CollectionUtils.isEmpty(plugNos)) {
            return dto;
        }

        ListPlugRequest requestParams = new ListPlugRequest();
        requestParams.setEvseNoList(new ArrayList<>(evseNos));
        requestParams.setBizStatusList(List.of(EvseBizStatus.NORMAL));
        ListResponse<PlugVo> res = iotBizClient.getPlugList(requestParams);
        FeignResponseValidate.check(res);
        plugCacheList = res.getData();

        if (CollectionUtils.isNotEmpty(plugCacheList)) {
            plugCacheList = plugCacheList.stream()
                .filter(plugVo -> EvseBizStatus.NORMAL.equals(plugVo.getBizStatus()))
                .filter(plugVo -> {
                    if (plugVo == null || plugVo.getIdx() == null
                        || StringUtils.isBlank(plugVo.getEvseNo())) {
                        return false;
                    }

                    // 已经停用不做考虑
                    if (EvseBizStatus.STOP.equals(plugVo.getBizStatus())) {
                        return false;
                    }

                    String idx = String.format("%02d", Integer.parseInt(plugVo.getIdx() + ""));
                    return plugNos.contains(plugVo.getEvseNo().concat(idx));
                }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(plugCacheList)) {
                for (PlugVo plugCache : plugCacheList) {
                    if (plugCache != null && plugCache.getStatus() != null) {
                        this.dealWith(dto, plugCache.getStatus());
                    } else {
                        log.warn("异常的缓存数据. plugCache = {}", plugCache);
                    }
                }
            }
        }
//        SitePo po = siteRoDs.getSite(siteId);
//        dto.setPlugTotal(NumberUtils.sum(po.getAcPlugNum(), po.getDcPlugNum()));
        dto.setPlugTotal(plugCacheList.size());
        dto.setChargeJobCount(siteChargeJobPlugRoDS.getSiteJobCount(siteId));
        dto.setChargeJobPlugCount(siteChargeJobPlugRoDS.getSiteJobPlugCount(siteId));
        return dto;
    }

    private void dealWith(PlugStatusCountDto dto, PlugStatus status) {
        if (status == PlugStatus.UNKNOWN) {
            dto.setUnknownCount(dto.getUnknownCount() + 1);
        } else if (status == PlugStatus.IDLE) {
            dto.setIdleCount(dto.getIdleCount() + 1);
        } else if (status == PlugStatus.CONNECT) {
            dto.setConnectCount(dto.getConnectCount() + 1);
        } else if (status == PlugStatus.BUSY) {
            dto.setBusyCount(dto.getBusyCount() + 1);
        } else if (status == PlugStatus.JOIN) {
            dto.setJoinCount(dto.getJoinCount() + 1);
        } else if (status == PlugStatus.ERROR) {
            dto.setErrorCount(dto.getErrorCount() + 1);
        } else if (status == PlugStatus.RECHARGE_END) {
            dto.setRechargeEndCount(dto.getRechargeEndCount() + 1);
        } else if (status == PlugStatus.OFFLINE) {
            dto.setOfflineCount(dto.getOfflineCount() + 1);
        } else if (status == PlugStatus.OFF) {
            dto.setOffCount(dto.getOffCount() + 1);
        }
    }


    /**
     * 返回场站列表,含城市,省份信息
     *
     * @return
     */
    public List<SiteGeoDto> getSiteGeoList(ListSiteParam param) {
        List<SiteGeoDto> siteGeoList = this.siteRoDs.getSiteGeoList(param.getIncludedHlhtSite(),
            param.getBizTypeList(),
            param.getCommIdChain(),
            param.getGids(),
            param.getStart(), param.getSize());

        // 包含场站组信息
        if (Boolean.TRUE.equals(param.getIncludeGroup()) && CollectionUtils.isNotEmpty(
            siteGeoList)) {
            List<String> siteIdList = siteGeoList.stream().map(SiteGeoDto::getSiteId)
                .collect(Collectors.toList());
            // 场站组信息
            List<SiteGroupRefPo> siteGroupList = siteGroupRoDs.getSiteGroupRefList(siteIdList);

            if (CollectionUtils.isNotEmpty(siteGroupList)) {
                // 转map
                Map<String, List<SiteGroupRefPo>> siteGroupMap = siteGroupList.stream()
                    .collect(Collectors.groupingBy(SiteGroupRefPo::getSiteId));

                // 场站组名称
                List<String> gIdList = siteGroupList.stream().map(SiteGroupRefPo::getGid).distinct()
                    .collect(Collectors.toList());
                ListSiteGroupParam listSiteGroupParam = new ListSiteGroupParam();
                listSiteGroupParam.setGidList(gIdList);
                ListResponse<SiteGroupPo> response = authCenterFeignClient.getSiteGroupList(
                    listSiteGroupParam);
                FeignResponseValidate.check(response);
                List<SiteGroupPo> groupList = response.getData();

                Map<String, SiteGroupPo> groupMap = groupList.stream()
                    .collect(Collectors.toMap(SiteGroupPo::getGid, group -> group));

                // 场站添加组信息
                siteGeoList.forEach(e -> {
                    if (siteGroupMap.containsKey(e.getSiteId())) {
                        ArrayList<SiteGroupPo> siteGroupVos = new ArrayList<>();
                        siteGroupMap.get(e.getSiteId()).forEach(x -> {
                            if (groupMap.containsKey(x.getGid())) {
                                siteGroupVos.add(groupMap.get(x.getGid()));
                            }
                        });
                        e.setSiteGroupList(siteGroupVos);
                    }
                });
            }
        }
        return siteGeoList;
    }

    public List<CommercialSiteDto> getCommercialSiteList(CommcialSiteParam req) {

        List<CommercialSiteDto> res = new ArrayList<>();

        CommPo po = commRoDs.getCommById(req.getCommId());

        // STEP 1.subCommName为空时，拼装直属商户信息
        if (StringUtils.isBlank(req.getSubCommName())) {
            CommercialSiteDto directlyComm = new CommercialSiteDto();
            directlyComm.setCommId(req.getCommId())
//                    .setCommName(po.getCommName() + " 直属")
                .setCommName(po.getName())
                .setNextLevelCommCount(0L);

            ListSiteParam param = new ListSiteParam();
            param.setCommIdChain(po.getIdChain())
                .setIncludedHlhtSite(req.getIncludedHlhtSite())
                .setSiteCommId(req.getCommId());
            if (req.getCategory() != null) {
                param.setSiteCategory(req.getCategory());
            }
            if (CollectionUtils.isNotEmpty(req.getCategoryList())) {
                param.setCategoryList(req.getCategoryList());
            }
            if (CollectionUtils.isNotEmpty(req.getStatusList())) {
                final List<Integer> SiteStatusIntegerList =
                    Arrays.asList(SiteStatus.values()).stream().map(SiteStatus::getCode)
                        .collect(Collectors.toList());
                param.setStatusList(req.getStatusList()
                    .stream()
                    .filter(e -> SiteStatusIntegerList.contains(e))
                    .map(e -> SiteStatus.valueOf(e))
                    .collect(Collectors.toList()));
            } else {
                param.setStatusList(
                    List.of(SiteStatus.ONLINE, SiteStatus.OPENING, SiteStatus.UNAVAILABLE));
            }

            directlyComm.setSiteCount(siteRoDs.countSite(param));
            directlyComm.setIdChain(po.getIdChain());
            res.add(directlyComm);
        }

        // STEP 2.拼装下一级商户信息
        ListCommercialParam param = new ListCommercialParam();
        if (StringUtils.isNotBlank(req.getSubCommName())) {
            param.setCommIdChain(po.getIdChain())
                .setCommName(req.getSubCommName());
        } else {
            param.setPid(req.getCommId());
        }
        param.setEnable(true);
        ListResponse<CommPo> subClass = commRoDs.getCommList2(param);
//        ListResponse<TRCommercialPo> subClass = commercialService.listCommercial(param);

        ListSiteParam siteParam = new ListSiteParam();
        siteParam.setIncludedHlhtSite(req.getIncludedHlhtSite());
        if (req.getCategory() != null) {
            siteParam.setSiteCategory(req.getCategory());
        }
        if (CollectionUtils.isNotEmpty(req.getCategoryList())) {
            siteParam.setCategoryList(req.getCategoryList());
        }
        if (CollectionUtils.isNotEmpty(req.getStatusList())) {
            final List<Integer> SiteStatusIntegerList =
                Arrays.asList(SiteStatus.values()).stream().map(SiteStatus::getCode)
                    .collect(Collectors.toList());
            siteParam.setStatusList(req.getStatusList()
                .stream()
                .filter(e -> SiteStatusIntegerList.contains(e))
                .map(e -> SiteStatus.valueOf(e))
                .collect(Collectors.toList()));
        } else {
            siteParam.setStatusList(
                List.of(SiteStatus.ONLINE, SiteStatus.OPENING, SiteStatus.UNAVAILABLE));
        }

        ListCommercialParam commParam = new ListCommercialParam();
        subClass.getData().forEach(e -> {
            CommercialSiteDto subComm = new CommercialSiteDto();
            subComm.setCommId(e.getId())
                .setCommName(e.getName());

            siteParam.setCommIdChain(e.getIdChain());
            subComm.setSiteCount(siteRoDs.countSite(siteParam));

            commParam.setPid(e.getId());
            subComm.setNextLevelCommCount(commRoDs.getCount(commParam));
            subComm.setIdChain(e.getIdChain());
            res.add(subComm);
        });
        return res;
    }

    public List<Site> syncHlhtSite(HlhtSyncSiteDto in) {
        log.info("同步互联互通场站信息 in = {}", in);
        List<SitePo> siteList = siteRoDs.getSiteByOpenSiteId(in.getOpenSiteId());
        if (CollectionUtils.isEmpty(siteList)) {
            log.warn("场站不存在. inSite = {}", in);
            throw new DcArgumentException("场站不存在");
        }

        return siteList.stream()
            .map(s -> this.syncHlhtSite(s, in))
            .collect(Collectors.toList());
    }

    private Site syncHlhtSite(SitePo site, HlhtSyncSiteDto in) {
        if (!NumberUtils.equals(BizType.HLHT.getCode(), site.getBizType())) {
            log.warn("非互联互通场站. inSite = {}", in);
            throw new DcArgumentException("非互联互通场站");
        } else if (!StringUtils.equalsIgnoreCase(in.getOperatorId(), site.getPartnerCode())) {
            log.warn("互联互通合作方编号错误. inSite = {}", in);
            throw new DcArgumentException("互联互通合作方编号错误", Level.INFO);
        }
        List<SiteCategory> siteCategoryList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(site.getCategoryIntList())) {
            site.getCategoryIntList().forEach(e -> {
                siteCategoryList.add(SiteCategory.valueOf(e));
            });
        }
        SitePo updateSite = new SitePo();
        updateSite.setId(site.getId())
            .setSiteName(in.getName())
            .setStatus(in.getStatus())
            .setScope(1) // 默认对外开放
            .setCategory(
                site.getCategoryIntList() == null ? List.of(SiteCategory.CE) : siteCategoryList)
            .setPark(in.getParkFeeType())
            .setType(in.getSiteType())  // 默认都是公共类型，仅为了匹配平台的字段信息
            .setServiceWorkdayTime(in.getOpenHourDesc())
            .setServiceHolidayTime(in.getOpenHourDesc());
        site.setSiteName(in.getName())
            .setStatus(in.getStatus())
            .setScope(1) // 默认对外开放
            .setCategory(
                site.getCategoryIntList() == null ? List.of(SiteCategory.CE) : siteCategoryList)
            .setPark(in.getParkFeeType())
            .setType(in.getSiteType())  // 默认都是公共类型，仅为了匹配平台的字段信息
            .setServiceWorkdayTime(in.getOpenHourDesc())
            .setServiceHolidayTime(in.getOpenHourDesc());
        if (StringUtils.isNotBlank(in.getFeeDesc())) {
            updateSite.setFeeDescription(in.getFeeDesc());
            site.setFeeDescription(in.getFeeDesc());
        }
        if (StringUtils.isNotBlank(in.getArea())) {
            int areaCode = Integer.parseInt(in.getArea().trim());
            if (!NumberUtils.equals(areaCode, site.getArea())) {
                DistrictPo dist = this.geoRoDs.getDistrict(in.getArea().trim());
                updateSite.setArea(areaCode);
                site.setArea(areaCode);

                if (dist != null) {
                    updateSite.setAreaName(dist.getName())
                        .setCity(Integer.parseInt(dist.getCityCode()))
                        .setProvince(Integer.parseInt(dist.getProvinceCode()));
                    site.setAreaName(dist.getName())
                        .setCity(Integer.parseInt(dist.getCityCode()))
                        .setProvince(Integer.parseInt(dist.getProvinceCode()));
                }
            }
        }
        if (StringUtils.isNotBlank(in.getAddress())) {
            updateSite.setAddress(in.getAddress());
            site.setAddress(in.getAddress());
        }
        if (StringUtils.isNotBlank(in.getPhone())) {
            updateSite.setContactsPhone(in.getPhone());
            site.setContactsPhone(in.getPhone());
        }
        if (StringUtils.isNotBlank(in.getCommPhone())) {
            updateSite.setPhone(in.getCommPhone());
            site.setPhone(in.getCommPhone());
        }
        if (CollectionUtils.isNotEmpty(in.getImages())) {
            updateSite.setImages(JsonUtils.toJsonString(in.getImages()));
            site.setImages(JsonUtils.toJsonString(in.getImages()));
        }
        if (in.getLat() != null) {
            updateSite.setLatitude(in.getLat());
            site.setLatitude(in.getLat());
        }
        if (in.getLon() != null) {
            updateSite.setLongitude(in.getLon());
            site.setLongitude(in.getLon());
        }
        if (StringUtils.isNotBlank(in.getParkFeeDesc())) {
            updateSite.setParkFee(in.getParkFeeDesc());
            site.setParkFee(in.getParkFeeDesc());
        }
        int acEvseNum = 0;
        int dcEvseNum = 0;
        int acPlugNum = 0;
        int dcPlugNum = 0;
        int acPower = 0;
        int dcPower = 0;
        if (in.getEvseList() != null) {
            for (var evse : in.getEvseList()) {
                if (SupplyType.AC == evse.getSupplyType()) {
                    acEvseNum += 1;
                    acPlugNum += evse.getPlugList().size();
                    acPower += (evse.getPower() == null ? 0 : evse.getPower());
                } else {
                    dcEvseNum += 1;
                    dcPlugNum += evse.getPlugList().size();
                    dcPower += (evse.getPower() == null ? 0 : evse.getPower());
                }
            }
        }
        updateSite.setAcEvseNum(acEvseNum)
            .setDcEvseNum(dcEvseNum)
            .setAcPlugNum(acPlugNum)
            .setDcPlugNum(dcPlugNum)
            .setAcPower(acPower)
            .setDcPower(dcPower);
        site.setAcEvseNum(acEvseNum)
            .setDcEvseNum(dcEvseNum)
            .setAcPlugNum(acPlugNum)
            .setDcPlugNum(dcPlugNum)
            .setAcPower(acPower)
            .setDcPower(dcPower);

        // 更新场站的计费信息
        if (CollectionUtils.isNotEmpty(in.getPolicyInfos())) {
            // /api/template/addPriceSchema 增加计费模板
            // /api/evse/cfg/modifyEvseCfg 变更计费模板
            // /dataCore/priceTemp/setDefaultPriceScheme 设置场站默认计费模板
            Long priceId = this.hlhtSitePrice(site, in.getPolicyInfos());

            updateSite.setTemplateId(priceId);
            site.setTemplateId(priceId);
        }

        log.info("更新场站信息. site = {}", updateSite);
        siteRwDs.updateSite(updateSite);
        this.updateMongoSite(site);

        bsBoxBizService.syncBsBoxList(site, in.getEvseList(), in.getPolicyInfoResult());

        Site retSite = new Site();
        retSite.setTopCommId(site.getTopCommId())
            .setCommId(site.getOperateId())
            .setSiteId(site.getId())
            .setPriceCode(site.getTemplateId());
        return retSite;
    }

    private Long hlhtSitePrice(SitePo site, List<CecPolicyInfo> policyInfos) {
        Long priceId = null;
        if (site.getTemplateId() == null) {
            // 创建计费模板
            AddPriceSchemaParam priceSchemaParam = new AddPriceSchemaParam();
            priceSchemaParam.setName("互联互通" + site.getId())
                .setCommId(site.getOperateId())
                .setFreeChargeFlag(PriceSchemaConstant.UN_FREE)
                .setPriceItemList(this.toPriceItemList(policyInfos));
            PriceTemplatePo priceTemplatePo = priceSchemaBizService.addPriceSchema(
                priceSchemaParam);
            priceId = priceTemplatePo.getId();

            // 创建场站默认配置信息
            SiteDefaultSettingPo siteDefultSettingRequest = new SiteDefaultSettingPo();
            siteDefultSettingRequest.setSiteId(site.getId());
            siteDefultSettingRequest.setChargeId(priceId);
            siteDefultSettingRequest.setCreateTime(new Date());
            siteDefultSettingRequest.setUrl("");
            this.siteDefaultSettingRwDs.addSiteDefaultSetting(siteDefultSettingRequest);
        } else {
            // 创建计费模板
            ChargePriceVo chargePriceVo = priceSchemaBizService.getChargePrice(
                site.getTemplateId());
            priceId = site.getTemplateId();
            List<ChargeV2> chargeV2List = this.toPriceItemList(policyInfos);
            Boolean updateFlag = false;
            if (chargePriceVo != null && CollectionUtils.isNotEmpty(chargePriceVo.getItemList())
                && chargePriceVo.getItemList().size() == chargeV2List.size()) {
                List<ChargePriceItem> chargePriceItemList = chargePriceVo.getItemList().stream()
                    .sorted(Comparator.comparing(ChargePriceItem::getStartTime)).collect(
                        Collectors.toList());
                for (int i = 0; i < chargePriceItemList.size(); i++) {
                    ChargePriceItem chargePriceItem = chargePriceItemList.get(i);
                    ChargeV2 chargeV2 = chargeV2List.get(i);
                    if (!(chargePriceItem.getStartTime()
                        .equals(chargeV2.getStartTime().replace(":", ""))
                        && chargePriceItem.getServPrice().compareTo(chargeV2.getServPrice()) == 0
                        && chargePriceItem.getElecPrice().compareTo(chargeV2.getElecPrice())
                        == 0)) {
                        updateFlag = true;
                        break;
                    }
                }
            } else {
                updateFlag = true;
            }

            if (updateFlag) {
                UpdatePriceSchemaParam updatePriceSchemaParam = new UpdatePriceSchemaParam();
                updatePriceSchemaParam
                    .setId(site.getTemplateId())
                    .setCommId(site.getOperateId())
                    .setFreeChargeFlag(PriceSchemaConstant.UN_FREE)
                    .setPriceItemList(this.toPriceItemList(policyInfos));
                priceId = priceSchemaBizService.updatePriceSchema(updatePriceSchemaParam);
            }
        }

        this.priceSchemaBizService.setDefaultPriceScheme(site.getId(), priceId, null);
        SiteTemplatePo siteTemplatePo = new SiteTemplatePo()
            .setSiteId(site.getId())
            .setTemplateId(priceId)
            .setTemplateType(SupplyType.BOTH);
        siteTemplateRwDs.insertTemplate(siteTemplatePo);

        log.debug("互联站点计费更新: siteId = {}, priceId = {}", site.getId(), priceId);
        return priceId;
    }

    private List<ChargeV2> toPriceItemList(List<CecPolicyInfo> policyInfos) {
        List<ChargeV2> collect = policyInfos.stream().map(this::toPriceItemList)
            .sorted(Comparator.comparing(ChargeV2::getStartTime))
            .collect(Collectors.toList());

        String last = "24:00";
        for (int i = collect.size(); i > 0; i--) {
            collect.get(i - 1).setStopTime(last);
            last = collect.get(i - 1).getStartTime();
        }

        return collect;
    }

    private ChargeV2 toPriceItemList(CecPolicyInfo policy) {
        ChargeV2 v2 = new ChargeV2();
        v2.setElecPrice(policy.getElecPrice())
            .setServPrice(policy.getSevicePrice());
        v2.setCategory(ChargePriceCategory.PRICE_TAG_PING);
        v2.setStartTime(HHmmss2HHmm(policy.getStartTime()));
        return v2;
    }

    private String HHmmss2HHmm(String time) {
        StringBuilder builder = new StringBuilder(time.substring(0, time.length() - 2));
        builder.insert(2, ':');
        return builder.toString();
    }

    /**
     * 获取场站已存在的发票提供方信息
     *
     * @param commIdchain
     * @param desc
     * @return
     */
    public ListResponse<String> getInvoiceDescList(String commIdchain, String desc) {
        return RestUtils.buildListResponse(this.siteRoDs.getInvoiceDescList(commIdchain, desc));
    }

    public Mono<ListResponse<SiteOrderStatsVo>> siteOrderStats(String siteId) {
        List<SiteOrderStatsVo> result = this.chargerOrderRoDs.siteOrderStats(siteId);
        return Mono.just(RestUtils.buildListResponse(result));
    }

    public List<SiteDiscountVo> discountServiceFee(DiscountServiceParam param) {
        return param.getSiteIdList().stream().map(siteId -> {
            SitePo site = this.siteRoDs.getSite(siteId);

            if (null == site) {
                log.warn("该场不存在. siteId = {}", siteId);
                throw new DcArgumentException("参数错误,场站不存在. ");
            }

            SiteDiscountVo vo = new SiteDiscountVo();
            vo.setSiteId(siteId)
                .setSiteName(site.getSiteName());
            if (site.getTemplateId() == null || site.getTemplateId() < 1L) {
                log.info("场站 {} ( {} ) 未设置计费模板", site.getSiteName(), site.getId());
                return vo;
            }

            return this.convertPrice(param, vo, site.getTemplateId());
        }).collect(Collectors.toList());
    }

    private SiteDiscountVo convertPrice(DiscountServiceParam param, SiteDiscountVo vo,
        Long priceId) {
        // 免费/收费(普通收费/分时计费(电价)/分时计费(电价/服务单价))
        // 获取计费模板
        Optional<PriceTemplatePo> priceSchema = priceSchemaBizService.getPriceSchema(priceId, true);
        if (priceSchema.isEmpty()) {
            log.warn("场站计费模板ID无效: tempId = {}", priceId);
            return vo;
        }

        Integer freeChargeFlag = priceSchema.get().getFreeChargeFlag();
        if (null == freeChargeFlag) {
            log.error("计费模板是否免费标识不明确: temp = {}", JsonUtils.toJsonString(priceSchema));
            return vo;
        }

        if (PriceSchemaConstant.FREE == freeChargeFlag) {
            // 00:00 - 00:24, 0, 0
            ChargeV2 chargeV2 = new ChargeV2();
            chargeV2.setStartTime("00:00")
                .setStopTime("24:00")
                .setServPrice(BigDecimal.ZERO)
                .setElecPrice(BigDecimal.ZERO);
            priceSchema.get().setPriceItemList(List.of(chargeV2));
        }

        // 计费模板详情
        vo.setPriceItemList(priceSchema.get().getPriceItemList().stream()
            .map(item -> {
                DiscountChargeInfo info = new DiscountChargeInfo();
                BeanUtils.copyProperties(item, info);
                return info;
            }).collect(Collectors.toList()));

        // 协议价计算
        if (null != param.getType()) {
            this.discountDealWithService.priceDiscount(param, vo.getPriceItemList());
        }
        return vo;
    }

    public SiteDiscountVo discountServiceFeeByCode(DiscountServiceParam param) {

        SiteDiscountVo vo = new SiteDiscountVo();
        return this.convertPrice(param, vo, param.getPriceCode());
    }

    public Mono<ObjectResponse<SiteSocLimitDto>> getSocLimitInfo(String siteId) {
        Mono<Boolean> m1 = Mono.create(sink -> {
//            log.info("s1");
            SiteDefaultSettingPo setting = siteDefaultSettingRwDs.getBySiteId(siteId);
//            log.info("e1");
            sink.success(setting != null && setting.getLimitSoc() != null && setting.getLimitSoc());
        });

//        Mono<List<SiteSocStrategyPo>> m2 = Mono.create(sink -> {
////            log.info("s2");
//            List<SiteSocStrategyPo> bySiteId = siteSocStrategyRoDs.getBySiteId(siteId);
////            log.info("e2");
//            sink.success(bySiteId);
//        });

        Mono<List<SiteSocMainStrategyVo>> m3 = Mono.create(sink -> {
            List<SiteSocMainStrategyPo> bySiteId = siteSocMainStrategyRoDs.getBySiteId(siteId);
            sink.success(bySiteId.stream()
                .map(e -> {
                    SiteSocMainStrategyVo ret = new SiteSocMainStrategyVo();
                    BeanUtils.copyProperties(e, ret);
                    return ret;
                })
                .collect(Collectors.toList()));
        });

        return Mono.zip(
            m1.subscribeOn(Schedulers.parallel()),
//                m2.subscribeOn(Schedulers.parallel()),
            m3.subscribeOn(Schedulers.parallel())).map(t -> {
            SiteSocLimitDto ret = new SiteSocLimitDto();
            return ret.setSocLimit(t.getT1()).setStrategyMainList((t.getT2()));
        }).map(RestUtils::buildObjectResponse);

    }

    public Mono<ObjectResponse<Boolean>> updateSocLimitInfo(SiteSocLimitDto param) {//

        Mono<Boolean> m1 = Mono.create(sink -> {
            SiteDefaultSettingPo siteDefaultSettingPo = new SiteDefaultSettingPo();
            siteDefaultSettingPo.setSiteId(param.getSiteId());
            siteDefaultSettingPo.setLimitSoc(param.isSocLimit());
            sink.success(siteDefaultSettingRwDs.updateBySiteId(siteDefaultSettingPo) > 0);
        });

        final String siteId = param.getSiteId();

        Mono<Boolean> m2 = Mono.create(sink -> {
            if (param.isSocLimit()) {
                log.info("移出了{}个主策略", siteSocMainStrategyRwDs.deleteBySiteId(siteId));
                log.info("删除场站soc策略: {}",
                    siteSocStrategyRwDs.deleteBySiteId(param.getSiteId()));

                param.getStrategyMainList().forEach(main -> {
                    boolean b = siteSocMainStrategyRwDs.insertSiteSocMainStrategy(main);
                    main.getStrategyList()
                        .forEach(ie -> ie.setSiteId(siteId).setMainId(main.getId()));
                    log.info("新增场站soc策略: {}",
                        siteSocStrategyRwDs.batchInsert(main.getStrategyList()));

                });
//                if(CollectionUtils.isNotEmpty(param.getStrategyList())) {
//                    log.info("新增场站soc策略: {}", siteSocStrategyRwDs.batchInsert(param.getStrategyList()));
//                }
            } else {
                log.info("仅关闭soc限制状态，不删除原先设定的策略");
            }
            sink.success(Boolean.TRUE);
        });

        return Mono.zip(m1.subscribeOn(Schedulers.parallel()),
                m2.subscribeOn(Schedulers.parallel()))
            .map(t -> t.getT2() & t.getT1()).map(RestUtils::buildObjectResponse);
    }

    public Mono<BaseResponse> updateSiteCommentInfo() {
        BaseListParam param = new BaseListParam();
        param.setStart(0L)
            .setSize(300);
        return this.loopUpdateSiteCommentInfo(param)
            .map(i -> RestUtils.success());
    }

    private Mono<Object> loopUpdateSiteCommentInfo(BaseListParam param) {
        return reactorUserFeignClient.selectSiteCommentTotal(param)
            .switchIfEmpty(Mono.empty())
            .map(ListResponse::getData)
            .doOnNext(siteRwDs::updateSiteCommentLevelAvg)
            .map(list -> {
                if (list.size() < param.getSize() ||
                    param.getStart() > LIMIT_MAX_SIZE) {
                    return Mono.empty();
                } else {
                    param.setStart(list.size() + param.getStart());
                    return this.loopUpdateSiteCommentInfo(param);
                }
            });
    }

    public ListResponse<SiteCategory> ywUserSiteCategoryList(List<String> siteIdList) {
        List<SitePo> sitePoList = siteRoDs.getSiteBySiteIdList(siteIdList);
        List<SiteCategory> list = new ArrayList<>();
        List<Integer> integerList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(sitePoList)) {
            sitePoList.forEach(po -> {
                List<Integer> siteCategoryIntList = po.getCategoryIntList();
                if (CollectionUtils.isNotEmpty(siteCategoryIntList)) {
                    siteCategoryIntList.forEach(siteCategoryInt -> {
                        if (!integerList.contains(siteCategoryInt)) {
                            integerList.add(siteCategoryInt);
                        }
                    });
                }
            });
        }
        if (CollectionUtils.isNotEmpty(integerList)) {
            integerList.forEach(siteCategoryInt -> {
                list.add(SiteCategory.valueOf(siteCategoryInt));
            });
        }
        return new ListResponse<>(list);
    }

    public String getIdChainBySiteId(String siteId) {
        return siteRoDs.getIdChainBySiteId(siteId);
    }

    public List<MoveCorpNoCardVo> getCorpNoCardList(Long corpId) {
        return siteRoDs.getCorpNoCardList(corpId);
    }

    public BaseResponse updateNoCardSetting(Long corpId) {
        IotAssert.isNotNull(corpId, "企业ID不能为空");
        siteRwDs.updateNoCardSetting(corpId);
        return BaseResponse.success();
    }

    public List<SiteVo> getCommNoCardList(Long commId, Long userId) {
        return siteRoDs.getCommNoCardList(commId, userId);
    }

    public BaseResponse updateCommNoCardList(Long commId, Long userId) {
        siteRwDs.updateCommNoCardList(commId, userId);
        return BaseResponse.success();
    }

    public ObjectResponse<Long> getChargerNumByChain(String commIdChain) {
        return RestUtils.buildObjectResponse(siteRoDs.getChargerNumByChain(commIdChain));
    }

    public void oaSiteMonthData(OaSiteMonthDataDto dto) {
        IotAssert.isNotBlank(dto.getSiteId(), "场站ID无效");
//        IotAssert.isNotNull(dto.getDate(), "月份不能为空");
        IotAssert.isNotNull(dto.getFromDate(), "账期日期开始时间不能为空");
        IotAssert.isNotNull(dto.getToDate(), "账期日期结束时间不能为空");

        BiSiteMonthPo newData = new BiSiteMonthPo()
            .setSiteId(dto.getSiteId())
            .setFromDate(dto.getFromDate())
            .setToDate(dto.getToDate())
            .setElec(dto.getElec())
            .setElecFee(dto.getElecFee())
            .setAutoDebit(dto.getAutoDebit())
            .setProcInstId(dto.getProcInstId())
            .setOrderData(dto.getOrderData());

        boolean b = this.siteDailyDataRwDs.insertSiteDailyData(newData);
        if (!b) {
            log.error("场站月数据写入失败: {}", newData);
        }
    }

    public ListResponse<String> getSiteListByGids(ListSiteParam params) {
        return new ListResponse(siteGroupRoDs.getSiteListByGids(params));
    }

    public ListResponse<String> getGidsBySiteId(String siteId) {
        return RestUtils.buildListResponse(siteGroupRoDs.getGidsBySiteId(siteId));
    }

    public ObjectResponse<SiteQrCodeVo> getSiteQrCodeVo(String siteId) {

        String operateCorpCode = siteRoDs.getOperateCorpCodeById(siteId);
        if (StringUtils.isBlank(operateCorpCode)) {
            return RestUtils.buildObjectResponse(null);
        }

        ObjectResponse<SiteOutPo> response = openHlhtFeignClient.getSiteOutPo(siteId);
        FeignResponseValidate.checkIgnoreData(response);
        Integer outPlugSize = Optional.ofNullable(response.getData())
            .map(SiteOutPo::getOutPlugSize).orElse(14); // 无记录则默认枪头号位数为14

        SiteQrCodeVo data = new SiteQrCodeVo();
        data.setOperateCorpCode(operateCorpCode);
        data.setOutPlugSize(outPlugSize);
        return RestUtils.buildObjectResponse(data);
    }

    /**
     * 获取场站ID列表， gids 或 commIdChain 必须传一个
     */
    public List<String> getSiteIds(List<String> gids, String commIdChain) {
        List<String> siteIds = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(gids)) {
            ListSiteParam listSiteParam = new ListSiteParam();
            listSiteParam.setGids(gids);
            siteIds = siteGroupRoDs.getSiteListByGids(listSiteParam);
        } else if (StringUtils.isNotBlank(commIdChain)) {
            siteIds = siteRoDs.getSiteIdListByCommIdChain(commIdChain);
        } else {
            log.warn("参数错误,gids或commIdChain不能都为空");
            throw new DcArgumentException("参数错误,gids或commIdChain不能都为空");
        }
        return siteIds;
    }

    public void syncSitePrice2SiteTemplate() {
        int page = 1;
        int size = 300;

        ListSiteParam param = new ListSiteParam();
        param.setSize(size);
        List<SitePriceVo> siteList;
        do {
            param.setStart((long) ((page - 1) * size));
            siteList = siteDefaultSettingRwDs.getSitePriceList(param);
            if (CollectionUtils.isNotEmpty(siteList)) {
                siteList.forEach(e -> {
                    if (StringUtils.isNotBlank(e.getSiteId()) && NumberUtils.gtZero(e.getChargeId())
                        && e.getId() == null) { // 还未同步到t_site_template中的数据
                        List<EvseVo> evseVos = redisIotReadService.listEvseBySiteId(e.getSiteId());
                        if (CollectionUtils.isNotEmpty(evseVos)) {
                            // 场站是否包含交直流桩
                            boolean hasAc = evseVos.stream()
                                .anyMatch(x -> SupplyType.AC.equals(x.getSupplyType()));
                            boolean hasDc = evseVos.stream()
                                .anyMatch(x -> SupplyType.DC.equals(x.getSupplyType()));

                            SiteTemplatePo siteTemplatePo = new SiteTemplatePo()
                                .setSiteId(e.getSiteId())
                                .setTemplateId(e.getChargeId())
                                .setTemplateType(hasDc && hasAc ? SupplyType.BOTH
                                    : (hasDc ? SupplyType.DC : SupplyType.AC));

                            siteTemplateRwDs.insertTemplate(siteTemplatePo);
                        }
                    }
                });
            }

            page += 1;

        } while (CollectionUtils.isNotEmpty(siteList) && siteList.size() >= 300);
    }

    public List<SiteTemplateVo> getSiteTemplateInfoBySiteIdList(List<String> siteIdList) {
        return this.siteTemplateRoDs.getSiteTemplateInfoBySiteIdList(siteIdList);
    }

    public void syncHlhtTemplate(
        CecQueryQeuipBusinessPolicyResult cecQueryQeuipBusinessPolicyResult) {
        BsChargerPo bsChargerPo = bsChargerRwDs.getBsChargerByPlugNo(
            cecQueryQeuipBusinessPolicyResult.getFullPlugNo());
        SitePo sitePo = siteRoDs.getSite(bsChargerPo.getStationCode());
        if (sitePo == null) {
            log.warn("场站不存在. inSite = {}", bsChargerPo.getStationCode());
            throw new DcArgumentException("场站不存在");
        }

        this.syncHlhtTemplate(sitePo, cecQueryQeuipBusinessPolicyResult, bsChargerPo);
    }

    private void syncHlhtTemplate(SitePo site,
        CecQueryQeuipBusinessPolicyResult cecQueryQeuipBusinessPolicyResult,
        BsChargerPo bsChargerPo) {
        if (!NumberUtils.equals(BizType.HLHT.getCode(), site.getBizType())) {
            log.warn("非互联互通场站. inSite = {}", site.getId());
            throw new DcArgumentException("非互联互通场站");
        }
        List<CecPolicyInfo> policyInfos = cecQueryQeuipBusinessPolicyResult.getPolicyInfos();
        List<ChargeV2> chargeV2List = this.toPriceItemList(policyInfos);
        List<PriceTemplatePo> priceTemplatePoList = priceSchemaBizService.getHlhtSitePriceTemplateList(
            site.getId());

        Boolean createFlag = true;
        Long priceId = null;
        for (int i = 0; i < priceTemplatePoList.size(); i++) {
            List<PriceItemPo> chargePriceListExit = priceSchemaBizService.getPriceItemListByTempId(
                priceTemplatePoList.get(i).getId());
            Boolean thisPriceExit = true;
            if (chargePriceListExit.size() == chargeV2List.size()) {
                List<PriceItemPo> chargePriceItemList = chargePriceListExit.stream()
                    .sorted(Comparator.comparing(PriceItemPo::getStartTime)).collect(
                        Collectors.toList());
                for (int j = 0; j < chargePriceItemList.size(); j++) {
                    PriceItemPo chargePriceExit = chargePriceItemList.get(j);
                    ChargeV2 chargeV2 = chargeV2List.get(j);
                    if (!(getHourAndMinute(chargePriceExit.getStartTime()).replace(":", "")
                        .equals(chargeV2.getStartTime().replace(":", ""))
                        && chargePriceExit.getServicePrice().compareTo(chargeV2.getServPrice()) == 0
                        && chargePriceExit.getPrice().compareTo(chargeV2.getElecPrice())
                        == 0)) {
                        thisPriceExit = false;
                        break;
                    }
                }
            } else {
                thisPriceExit = false;
            }
            if (thisPriceExit) {
                createFlag = false;
                priceId = priceTemplatePoList.get(i).getId();
                break;
            }
        }

        // 创建计费模板
        if (createFlag) {
            AddPriceSchemaParam priceSchemaParam = new AddPriceSchemaParam();
            priceSchemaParam.setName(
                    "互联互通" + site.getId() + UUID.randomUUID().toString().replace("-", ""))
                .setCommId(site.getOperateId())
                .setFreeChargeFlag(PriceSchemaConstant.UN_FREE)
                .setPriceItemList(
                    this.toPriceItemList(cecQueryQeuipBusinessPolicyResult.getPolicyInfos()));
            PriceTemplatePo priceTemplatePo = priceSchemaBizService.addPriceSchema(
                priceSchemaParam);
            priceId = priceTemplatePo.getId();
        }
        if (priceId != null) {
            BsBoxPo bsBoxPo = new BsBoxPo();
            bsBoxPo.setEvseNo(bsChargerPo.getEvseNo())
                .setPriceCode(priceId);
            bsBoxRwDs.updateBsBox(bsBoxPo);

            List<String> plugNoList = bsChargerRoDs.getPlugListByEvseNo(bsChargerPo.getEvseNo())
                .stream().map(BsChargerPo::getPlugNo).collect(
                    Collectors.toList());
            redisIotRwService.updatePriceCode(bsChargerPo.getEvseNo(), plugNoList, priceId);
        }
        ;
    }

    private String getHourAndMinute(int time) {
        Integer hour = time / 60;
        Integer minute = time % 60;
        return String.format("%02d:%02d", hour, minute);
    }

    public ListResponse<SysUserVo> getUserListBySiteId(String siteId) {
        List<String> gidList = siteGroupRoDs.getGidsBySiteId(siteId);
        if (CollectionUtils.isEmpty(gidList)) {
            return RestUtils.buildListResponse(new ArrayList<>());
        }
        ListSiteGroupParam params = new ListSiteGroupParam();
        params.setGidList(gidList)
            .setTypeList(List.of(SiteGroupType.YW, SiteGroupType.SH));
        return authCenterFeignClient.getByGidList(params);

    }

    public ListResponse<SiteOverTimeParkDTO> getOverTimeParkList(List<String> siteIdList) {

        List<SiteDefaultSettingPo> siteDefaultSettingPoList = siteDefaultSettingRwDs.getBySiteIds(
            siteIdList);
        Map<String, Integer> overtimeParkMap = siteDefaultSettingPoList.stream().collect(
            Collectors.toMap(SiteDefaultSettingPo::getSiteId,
                o -> Optional.ofNullable(o.getOvertimeParkingTime()).orElse(-1), (v1, v2) -> v1));
        List<SiteOvertimeParkSettingPo> overtimeSettingPoList = siteOvertimeParkSettingRoDs.getEnabledBySiteIdList(
            siteIdList);
        List<Long> overtimeSettingIdList = overtimeSettingPoList.stream()
            .map(SiteOvertimeParkSettingPo::getId).collect(Collectors.toList());
        List<SiteOverTimeParkDTO> siteOverTimeParkDTOList = new ArrayList<>();

        if (CollectionUtils.isEmpty(overtimeSettingIdList)) {
            return RestUtils.buildListResponse(new ArrayList<>());
        }

        List<SiteOvertimeParkDivisionPo> siteOvertimeParkDivisionPoList = siteOvertimeParkDivisionRoDs.getBySettingIds(
            overtimeSettingIdList, true);
        Map<Long, List<SiteOvertimeParkDivisionPo>> setttingMap = siteOvertimeParkDivisionPoList.stream()
            .collect(Collectors.groupingBy(SiteOvertimeParkDivisionPo::getSettingId));
        overtimeSettingPoList.forEach(e -> {
            SiteOverTimeParkDTO siteOverTimeParkDTO = new SiteOverTimeParkDTO();
            siteOverTimeParkDTO.setSiteId(e.getSiteId())
                .setOvertimeParkMaxFee(e.getMaxFee())
                .setOvertimeParkFeeTime(overtimeParkMap.get(e.getSiteId()))
                .setSiteOvertimeParkDivisionPoList(setttingMap.get(e.getId()));
            siteOverTimeParkDTOList.add(siteOverTimeParkDTO);
        });
        return RestUtils.buildListResponse(siteOverTimeParkDTOList);

    }


    /**
     * <p> 通过MQ消息将场站信息同步给所有微服务, 用于定时任务触发</p>
     * <p> 默认仅同步‘上线’的场站</p>
     */
    public ListResponse<String> syncSitesInfo(List<String> siteIdList,
        List<SiteStatus> statusList) {
        ListSiteParam param = new ListSiteParam();
        param.setStart(0L)
            .setSize(100)
            .setTotal(false);
        if (CollectionUtils.isNotEmpty(siteIdList)) {
            param.setSiteIdList(siteIdList);
        }
        if (CollectionUtils.isNotEmpty(statusList)) {
            param.setStatusList(statusList);
        } else {
            param.setStatusList(List.of(SiteStatus.ONLINE));
        }
        ListResponse<SitePo> res;
        List<String> siteIdResult = new ArrayList<>();
        do {
            res = this.siteRoDs.getSiteList(param);
            if (res == null || CollectionUtils.isEmpty(res.getData())) {
                log.info("查询场站列表返回空. res = {}", res);
                break;
            }
            log.info("同步场站信息. param = {}, 返回 sites.size = {}",
                param, res.getData().size());
            res.getData().stream().forEach(s -> {
                dcEventPublisherService.publishSiteInfo(s);
                siteIdResult.add(s.getId());
            });
            param.setStart(param.getStart() + param.getSize());
        } while (res != null && CollectionUtils.isNotEmpty(res.getData()));
        return new ListResponse<>(siteIdResult);
    }

    public ListResponse<UserScoreSettingLevelSiteGidDto> getOptimalScoreSettingList(
        String orderNo) {

        ChargerOrder chargeOrderInfo = chargerOrderRoDs.findByOrderNo(orderNo);
        IotAssert.isNotNull(chargeOrderInfo, "订单信息不存在");
//        IotAssert.isNotNull(param.getSiteId(), "场站id不能为空");
//        IotAssert.isNotNull(param.getUserId(), "用户id不能为空");

        IotAssert.isTrue(
            NumberUtils.equals(chargeOrderInfo.getStatus(), OrderStatus.ORDER_STATUS_COMPLETE)
                && ChargeOrderStatus.STOP == chargeOrderInfo.getOrderStatus(),
            "订单需处于结束但未支付状态");
        ChargerOrderPayPo pay = chargerOrderPayRoDs.getByOrderno(orderNo);
        IotAssert.isNotNull(pay, "订单支付信息不存在");

        if (NumberUtils.gtZero(pay.getDiscountRefId())) {
            // 商户会员已使用了协议价，不可再选择积分体系列表
            return RestUtils.buildListResponse(new ArrayList<>());
        }

        if (DecimalUtils.lteZero(pay.getOrderFee())) {
            log.info("订单原金额为0，不使用积分体系： {}",
                orderNo);
            return RestUtils.buildListResponse(new ArrayList<>());
        }

        // 支付类型校验
        List<PayAccountType> payAccountTypeList = List.of(
            PayAccountType.PERSONAL,
            PayAccountType.COMMERCIAL);

        if (!payAccountTypeList.contains(
            pay.getAccountType())) {
            // 除了个人账户和商户会员，其他支付类型在待支付界面都不能使用积分体系
            return RestUtils.buildListResponse(new ArrayList<>());
        }

        List<String> gidList = siteGroupRoDs.getGidsBySiteId(chargeOrderInfo.getStationId());
        // 先查出场站组id
        final List<String> distinctedGidList = gidList.stream()
            .distinct().toList();

        if (CollectionUtils.isEmpty(distinctedGidList)) {
            return RestUtils.buildListResponse(new ArrayList<>());
        }
        SearchScoreLogParam searchScoreLogParam = new SearchScoreLogParam();
        searchScoreLogParam.setUserId(chargeOrderInfo.getCustomerId())
            .setGids(distinctedGidList);
        // 再获取场站组用户的积分信息
        final ListResponse<UserScoreSettingLevelSiteGidDto> siteGroupUserScoreSettingRes =
            userFeignClient.getSiteGroupUserScoreSetting(searchScoreLogParam);

        log.debug("siteGroupUserScoreSetting: {}", siteGroupUserScoreSettingRes);
        FeignResponseValidate.check(siteGroupUserScoreSettingRes);
        final List<UserScoreSettingLevelSiteGidDto> levelData =
            siteGroupUserScoreSettingRes.getData();

        if (CollectionUtils.isEmpty(levelData)) {
            return RestUtils.buildListResponse(new ArrayList<>());
        }
        return RestUtils.buildListResponse(this.calScoreSettingLevel(levelData));

    }
}
