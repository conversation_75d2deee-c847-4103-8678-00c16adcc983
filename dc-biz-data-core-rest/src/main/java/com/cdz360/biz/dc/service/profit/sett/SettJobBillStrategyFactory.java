package com.cdz360.biz.dc.service.profit.sett;

import com.cdz360.biz.model.trading.profit.sett.type.ProfitCfgCalSource;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SettJobBillStrategyFactory {

    private Map<ProfitCfgCalSource, SettJobBillStrategy> strategyMap = new ConcurrentHashMap<>();

    protected void addStrategy(ProfitCfgCalSource calSource, SettJobBillStrategy strategy) {
        this.strategyMap.put(calSource, strategy);
    }

    public SettJobBillStrategy getStrategy(ProfitCfgCalSource calSource) {
        return this.strategyMap.get(calSource);
    }
}
