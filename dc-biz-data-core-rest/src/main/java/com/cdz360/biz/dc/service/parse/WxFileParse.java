package com.cdz360.biz.dc.service.parse;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.biz.dc.service.parse.line.LineParse;
import com.cdz360.biz.dc.service.parse.line.WxLineParse;
import com.cdz360.biz.model.trading.bill.po.ZftThirdOrderPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class WxFileParse extends AbstractFileParse {

    private LineParse lineParse;

    public WxFileParse(WxLineParse lineParse) {
        this.lineParse = lineParse;
    }

    @Override
    public void parseFile(String absolutePath, Long dailyBillId) {
        BufferedReader br = null;

        File file = new File(absolutePath);
        try {
            List<ZftThirdOrderPo> list = new ArrayList<>(1000);
            log.info("文件名称:" + absolutePath);
            br = new BufferedReader(new InputStreamReader(new FileInputStream(file)));

            String strLine = null;
            while ((strLine = br.readLine()) != null) {
                if (strLine.startsWith("\uFEFF交易时间")) {
                    log.warn("【微信对账】标题信息, line : {}", strLine);
                    continue;
                }

                ZftThirdOrderPo thirdOrderPo = (ZftThirdOrderPo) lineParse.resolveLine(strLine);
                if (thirdOrderPo == null) {
                    continue;
                }

                thirdOrderPo.setDailyBillId(dailyBillId);
                super.billCheck(thirdOrderPo, dailyBillId);

                list.add(thirdOrderPo);
                if (list.size() >= 1000) {
                    log.warn("【微信对账】保存1000条信息到数据库");
                    zftThirdOrderRwDs.saveBatchList(list);
                    list.clear();
                }
            }

            // 继续保存这些数据
            if (list.size() > 0) {
                log.warn("【微信对账】保存" + list.size() + "条信息到数据库");
                zftThirdOrderRwDs.saveBatchList(list);
                list.clear();
            }
            br.close();
        } catch (Exception e) {
            if (e instanceof FileNotFoundException) {
                log.error("本地文件是空文件，需要确认下载的正确性");
                throw new DcServiceException(e.getMessage());
            }
            log.info("【微信对账文件解析】 异常信息：{}", e.getMessage());
            throw new DcServiceException("微信对账文件解析及入DB库出错");
        } finally {
            if (br != null) {
                try {
                    br.close();
                    boolean delete = file.delete();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }
}
