package com.cdz360.biz.dc.domain;

import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.domain.erp.ErpSaveModel;
import com.cdz360.biz.dc.domain.erp.ErpSiteInfo;
import com.cdz360.biz.model.trading.bi.po.BiErpSitePo;
import com.cdz360.biz.model.trading.bi.type.ErpSiteStatus;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SendSiteBi extends BiErpSitePo {

    /**
     * 转成推送记录对象
     *
     * @return
     */
    public BiErpSitePo map2ErpSitePo() {
        BiErpSitePo po = new BiErpSitePo();
        po.setSeqNo(this.getSeqNo())
                .setStatus(ErpSiteStatus.SENDING)
                .setTotalElectricity(this.getTotalElectricity())
                .setTotalOrderPrice(this.getTotalOrderPrice())
                .setTotalElectricPrice(this.getTotalElectricPrice())
                .setTotalServicePrice(this.getTotalServicePrice())
                .setIncomeTime(this.getIncomeTime())
                .setSiteId(this.getSiteId())
                .setErpId(this.getErpId())
                .setLastId(this.getLastId())
                .setErpCostNumber(this.getErpCostNumber())
                .setId(this.getId())
                .setSiteNo(this.getSiteNo());
        return po;
    }

    /**
     * 转成推送到ERP系统对象
     *
     * @return
     */
    public ErpSaveModel map2ErpSaveModel() {
        ErpSaveModel model = new ErpSaveModel();
        model.setF_FDATE(this.getIncomeTime().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:dd")))
                .setFID(this.getErpId())
                .setF_ITEMID(this.getSiteId()) // 场站ID
                .setF_Electricity(this.getTotalElectricity() == null ?
                        BigDecimal.ZERO : this.getTotalElectricity())
                .setF_Grossincome(this.getTotalOrderPrice() == null ?
                        BigDecimal.ZERO : this.getTotalOrderPrice())
                .setF_Servicerevenue(this.getTotalServicePrice() == null ?
                        BigDecimal.ZERO : this.getTotalServicePrice())
                .setF_Electricityincome(this.getTotalElectricPrice() == null ?
                        BigDecimal.ZERO : this.getTotalElectricPrice());

        // 场站信息
        if (StringUtils.isNotBlank(this.getSiteNo())) {
            ErpSiteInfo siteInfo = new ErpSiteInfo();
            siteInfo.setFNumber(this.getSiteNo());
            model.setF_ITEMNUMBER(siteInfo);
        }

        return model;
    }
}
