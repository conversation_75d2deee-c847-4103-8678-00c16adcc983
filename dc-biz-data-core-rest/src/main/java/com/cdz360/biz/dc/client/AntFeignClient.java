package com.cdz360.biz.dc.client;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.model.trading.site.param.FetchTargetPriceSchemeParam;
import com.cdz360.biz.oa.dto.TargetPriceSchemeInfo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import reactor.core.publisher.Mono;

@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_ANT, fallbackFactory = HystrixAntFeignClientFactory.class)
public interface AntFeignClient {

    @PostMapping(value = "/api/template/fetchTargetPriceScheme")
    Mono<ListResponse<TargetPriceSchemeInfo>> fetchTargetPriceScheme(
        @RequestBody FetchTargetPriceSchemeParam param);
}