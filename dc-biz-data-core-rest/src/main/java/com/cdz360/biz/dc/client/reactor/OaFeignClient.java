package com.cdz360.biz.dc.client.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.chargerlinkcar.framework.common.domain.oa.NotifyResultParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_OA,
    fallbackFactory = OaFeignClientHystrix.class
)
public interface OaFeignClient {

    // 通知OA流程是否自动开票成功
    @PostMapping(value = "/oa/billing/notifyResult")
    Mono<BaseResponse> notifyResult(@RequestBody NotifyResultParam params);

//    @GetMapping(value = "/oa/formInfo/getInvoicingContent")
//    Mono<ListResponse<InvoicingContentVo>> getInvoicingContent(
//        @RequestParam("procInstId") String procInstId);

    // 查询结算单是否已经被流程占用
    @GetMapping(value = "/oa/process-instance/containSettJobBillNo")
    Mono<ObjectResponse<Boolean>> containSettJobBillNo(
        @RequestParam("billNo") String billNo);

    // 更新params数据
    @PostMapping(value = "/oa/process-instance/updateNameByParams")
    Mono<BaseResponse> updateNameByParams(@RequestBody Object params,
        @RequestParam(value = "procInstId") String procInstId, @RequestParam(value = "name") String name);

}
