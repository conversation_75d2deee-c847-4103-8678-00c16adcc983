package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.dc.service.CouponDictService;
import com.cdz360.biz.model.trading.coupon.param.CouponDictParam;
import com.cdz360.biz.model.trading.coupon.param.CouponDictSearchParam;
import com.cdz360.biz.model.trading.coupon.vo.ChangeVo;
import com.cdz360.biz.model.trading.coupon.vo.CouponDictVo;
import com.chargerlinkcar.framework.common.domain.ChangeInfo;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * CouponDictRest
 *
 * @since 7/27/2020 3:45 PM
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/dataCore/couponDict")
@Tag(name = "券模板相关接口", description = "券模板")
public class CouponDictRest {

    @Autowired
    private CouponDictService couponDictService;

    @Operation( summary = "新增券模板")
    @RequestMapping(value = "/dictCreate", method = RequestMethod.POST)
    public BaseResponse dictCreate(@RequestBody CouponDictParam req) {
        log.info("新增券模板: {}", JsonUtils.toJsonString(req));
        return couponDictService.dictCreate(req);
    }

    @Operation( summary = "查询券模板列表")
    @RequestMapping(value = "/getDictList", method = RequestMethod.POST)
    public ListResponse<CouponDictVo> getDictList(@RequestBody CouponDictSearchParam req) {
        log.info("查询券模板列表: {}", JsonUtils.toJsonString(req));
        return couponDictService.getDictList(req);
    }


    @Operation( summary = "作废券模板")
    @RequestMapping(value = "/disableDict", method = RequestMethod.POST)
    public BaseResponse disableDict(@RequestParam(value = "id") Long id) {
        log.info("作废券模板: {}", id);
        return couponDictService.disableDict(id);
    }

    @Operation( summary = "站点更换商户需要调整的券模板、活动、定时任务、充电限制、无卡启动订单结算账户设置")
    @RequestMapping(value = "/changeBySiteId", method = RequestMethod.POST)
    public ObjectResponse<ChangeVo> changeBySiteId(@RequestParam(value = "idChain") String idChain,
                                                   @RequestParam(value = "siteId") String siteId) {

        log.info("请求参数：idChain={},siteId={}",idChain,siteId);

        if (idChain == null || siteId == null) {
            throw new DcServiceException("请求参数不正确");
        }

        return new ObjectResponse<>(couponDictService.changeBySiteId(idChain,siteId));
    }

    @Operation( summary = "站点更换商户执行券模板、活动、定时任务充电限制调整")
    @RequestMapping(value = "/queryChangeCommIdBySiteId", method = RequestMethod.POST)
    public ObjectResponse<Integer> queryChangeCommIdBySiteId(@RequestBody ChangeInfo changeInfo) {

        log.info("请求参数：changeInfo={}",JsonUtils.toJsonString(changeInfo));

        if (changeInfo.getSiteId() == null || changeInfo.getCommId() == null ) {
            throw new DcServiceException("请求参数不正确");
        }

        Integer ret = couponDictService.queryChangeCommIdBySiteId(changeInfo);
        //执行成功，发起站点信息同步
        if (ret.equals(1)) {
            log.info("发起同步站点commId请求");
            couponDictService.sysSiteCommId(changeInfo);
            couponDictService.updateEvseRedisCache(changeInfo);
            couponDictService.updatePlugRedisCache(changeInfo);

        }

        return new ObjectResponse<>(ret);
    }
}