package com.cdz360.biz.dc.service.profit.sett;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderPayRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderRoDs;
import com.cdz360.biz.model.trading.order.dto.OrderThinBiDto;
import com.cdz360.biz.model.trading.order.param.SettJobOrderBiParam;
import com.cdz360.biz.model.trading.profit.sett.dto.ChargeOrderSettJobBillDto;
import com.cdz360.biz.model.trading.profit.sett.po.ChargeOrderRule;
import com.cdz360.biz.model.trading.profit.sett.po.SettJobBillPo;
import com.cdz360.biz.model.trading.profit.sett.type.ProfitCfgCalSource;
import com.cdz360.biz.model.trading.profit.sett.type.ProfitRuleAccountType;
import com.cdz360.biz.model.trading.profit.sett.vo.ProfitCfgVo;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.fasterxml.jackson.core.type.TypeReference;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import jakarta.annotation.PostConstruct;
import javax.validation.constraints.NotNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ChargeOrderSettJobBillStrategy
    extends AbstractSettJobBillStrategy<OrderThinBiDto, SettJobOrderBiParam> {

    // 规则计算字段映射
    private static final Map<String, String> RULE_KEY_MAP = new HashMap<>() {{
        put("elec", "elec"); // 总电量
        put("elecFee", "elecFee"); // 总电费
        put("servFee", "servFee"); // 总服务费
        put("overFee", "overParkFee"); // 总超时费
    }};

    @Autowired
    private ChargerOrderPayRoDs chargerOrderPayRoDs;

    @Autowired
    private ChargerOrderRoDs chargerOrderRoDs;

    @PostConstruct
    public void init() {
        this.settJobBillStrategyFactory.addStrategy(ProfitCfgCalSource.FROM_CHARGE_ORDER, this);
    }

    @Override
    protected SettJobOrderBiParam statisticsParam(LocalDate refer, String siteId, ProfitCfgVo cfg) {
        SettJobOrderBiParam orderParam = new SettJobOrderBiParam();
        orderParam.setSiteId(siteId);

        Integer monthDay = cfg.getMonthDay(); // 结算周期
        TimeFilter timeFilter = new TimeFilter()
            .setStartTime(DateUtil.localDateToDate(
                refer.minusMonths(1).withDayOfMonth(monthDay)))
            .setEndTime(DateUtil.localDateToDate(refer.withDayOfMonth(monthDay)));

        switch (cfg.getTimeTarget()) {
            case PAY_TIME:
                orderParam.setPaymentTimeFilter(timeFilter);
                break;
            case STOP_TIME:
                orderParam.setStopTimeFilter(timeFilter);
                break;
            case CREATE_TIME:
                orderParam.setChargeCreateTimeFilter(timeFilter);
                break;
            case CHARGE_END_TIME:
                orderParam.setChargeStartTimeFilter(timeFilter);
                break;
            case CHARGE_START_TIME:
                orderParam.setChargeStopTimeFilter(timeFilter);
                break;
            default:
                throw new DcArgumentException("时间维度无效");
        }
        return orderParam;
    }

    @Override
    protected OrderThinBiDto statistics(SettJobOrderBiParam orderParam) {
        return chargerOrderRoDs.settJobOrderBi(orderParam);
    }

    @Override
    protected SettJobBillPo compute(ProfitCfgVo cfg, SettJobOrderBiParam orderParam) {
        ExpressionComputeRuleEntity<BigDecimal> calRule =
            new ExpressionComputeRuleEntity<>(BigDecimal.class);

        SettJobBillPo result = new SettJobBillPo();
        result.setElec(BigDecimal.ZERO)
            .setElecFee(BigDecimal.ZERO)
            .setServFee(BigDecimal.ZERO)
            .setParkFee(BigDecimal.ZERO);

        List<ChargeOrderRule> rules =
            JsonUtils.fromJson(JsonUtils.toJsonString(cfg.getChargeOrderRules()),
                new TypeReference<List<ChargeOrderRule>>() {
                });

        List<ChargeOrderSettJobBillDto> statList = new ArrayList<>();
        for (ChargeOrderRule rule : rules) {
            switch (rule.getAccountType()) {
                case ALL_ACCOUNT:
                    orderParam.setAccountType(null);
                    break;
                case PREPAY:
                case PERSONAL:
                case COMMERCIAL:
                case WX_CREDIT:
                case ALIPAY_CREDIT:
                    orderParam.setCorpId(null);
                    orderParam.setAccountType(rule.getAccountType().getCode());
                    break;
                case CORP:
                    orderParam.setAccountType(ProfitRuleAccountType.CREDIT.getCode());
                    if (null != rule.getAccountId()) {
                        orderParam.setCorpId(rule.getAccountId());
                    } else { // 其他企业客户单独计算
                        continue;
                    }
                    break;
            }

            OrderThinBiDto statistics = this.statistics(orderParam);
            log.info("[normal]计算前: cfgId = {}, siteId = {}, stat = {}",
                cfg.getId(), orderParam.getSiteId(), statistics);
            this.ruleVariablesMap(result, calRule, rule, statistics);

            // 计算数据源
            statList.add(new ChargeOrderSettJobBillDto()
                .setAccountType(rule.getAccountType())
                .setAccountId(rule.getAccountId())
                .setStatistics(statistics));
        }

        // 其他企业客户单独计算
        OrderThinBiDto stat = this.otherCorpCalculate(result, rules, orderParam, calRule);
        if (null != stat) { // 计算数据源
            statList.add(new ChargeOrderSettJobBillDto()
                .setAccountType(ProfitRuleAccountType.CORP)
                .setStatistics(stat));
        }

        if (statList.size() > 0) {
            result.setDataSource(JsonUtils.toJsonString(statList));
        }
        log.info("计算后: cfgId = {}, siteId = {}, result = {}",
            cfg.getId(), orderParam.getSiteId(), result);
        return result;
    }

    /**
     * 其他企业客户计算逻辑
     *
     * @param result     结算单
     * @param rules      规则集合
     * @param orderParam 查询数据请求参数
     * @param calRule    规则计算对象
     */
    private OrderThinBiDto otherCorpCalculate(
        @NotNull SettJobBillPo result, @NotNull List<ChargeOrderRule> rules,
        @NotNull SettJobOrderBiParam orderParam,
        @NotNull ExpressionComputeRuleEntity<BigDecimal> calRule) {
        Optional<ChargeOrderRule> other = rules.stream().filter(
                x -> ProfitRuleAccountType.CORP.equals(x.getAccountType()) && null == x.getAccountId())
            .findFirst();
        OrderThinBiDto statistics = null;

        if (other.isPresent()) {
            ChargeOrderRule rule = other.get();
            List<Long> excludeCorpIdList = rules.stream().filter(
                    k -> ProfitRuleAccountType.CORP.equals(k.getAccountType())
                        && null != k.getAccountId())
                .map(ChargeOrderRule::getAccountId)
                .distinct()
                .collect(Collectors.toList());

            orderParam.setAccountType(ProfitRuleAccountType.CREDIT.getCode())
                .setCorpId(null)
                .setExcludeCorpIdList(excludeCorpIdList);

            statistics = this.statistics(orderParam);
            log.info("[other]计算前: siteId = {}, stat = {}", result.getSiteId(), statistics);
            this.ruleVariablesMap(result, calRule, rule, statistics);
        } // end else
        return statistics;
    }

    /**
     * 规则计算结果
     *
     * @param result     结算单
     * @param calRule    规则计算对象
     * @param rule       规则
     * @param statistics 查询数据
     */
    private void ruleVariablesMap(
        @NotNull SettJobBillPo result, @NotNull ExpressionComputeRuleEntity<BigDecimal> calRule,
        @NotNull ChargeOrderRule rule, @NotNull OrderThinBiDto statistics) { // 规则计算字段映射
        HashMap<String, Object> var = new HashMap<>();
        BeanMap beanMap = BeanMap.create(statistics);
        RULE_KEY_MAP.forEach((k, v) -> var.put(k, beanMap.get(v)));
        calRule.setVariables(var);

        result.setElec(result.getElec().add(statistics.getElec()))
            .setElecFee(result.getElecFee().add(
                calRule.setExpression(rule.getElecFee()).compute()))
            .setServFee(result.getServFee().add(
                calRule.setExpression(rule.getServFee()).compute()))
            .setParkFee(result.getParkFee().add(
                calRule.setExpression(rule.getParkFee()).compute()));
    }

    @Override
    public void generateSettJobBill(String siteId, ProfitCfgVo cfg) {
        IotAssert.isTrue(CollectionUtils.isNotEmpty(cfg.getChargeOrderRules()), "未配置计算规则");
        SettJobOrderBiParam orderParam = this.statisticsParam(
            cfg.getGenerateDay() >= cfg.getMonthDay() ?
                LocalDate.now() : LocalDate.now().minusMonths(1), siteId, cfg);
        SettJobBillPo settJobBill = this.compute(cfg, orderParam); // 规则计算
        assignSettJobBill(settJobBill.setSiteId(siteId), cfg, orderParam);
        super.insertSettJobBill(settJobBill);
    }

    @Override
    public void recalculateSettJobBill(SettJobBillPo settJobBillPo, ProfitCfgVo cfg) {
        if (null == cfg) {
            cfg = new ProfitCfgVo()
                .setChargeOrderRules(settJobBillPo.getChargeOrderRules())
                .setCategory(settJobBillPo.getJobCategory())
                .setTimeTarget(settJobBillPo.getTimeTarget());
        } else {
            settJobBillPo.setJobId(cfg.getId())
                .setChargeOrderRules(cfg.getChargeOrderRules())
                .setJobName(cfg.getName())
                .setJobCategory(cfg.getCategory())
                .setJobCalSource(cfg.getCalSource())
                .setTimeTarget(cfg.getTimeTarget())
                .setChargeOrderRules(cfg.getChargeOrderRules())
                .setRemark(cfg.getRemark());
        }
        String siteId = settJobBillPo.getSiteId();
        LocalDate date = DateUtil.dateToLocalDate(settJobBillPo.getSettPeriodTo());
        cfg.setMonthDay(date.getDayOfMonth())
            .setGenerateDay(
                DateUtil.dateToLocalDate(settJobBillPo.getCreateTime()).getDayOfMonth());

        SettJobOrderBiParam orderParam = this.statisticsParam(DateUtil.dateToLocalDate(
                settJobBillPo.getSettPeriodTo()),
            siteId, cfg);
        SettJobBillPo data = this.compute(cfg, orderParam); // 规则计算
        settJobBillPo.setDataSource(data.getDataSource())
            .setElec(data.getElec())
            .setElecFee(data.getElecFee())
            .setServFee(data.getServFee())
            .setParkFee(data.getParkFee());
        super.updateSettJobBill(settJobBillPo);
    }

    private static void assignSettJobBill(
        SettJobBillPo settJobBill, ProfitCfgVo cfg, SettJobOrderBiParam orderParam) { // 赋值
        settJobBill.setJobId(cfg.getId())
            .setChargeOrderRules(cfg.getChargeOrderRules())
            .setJobName(cfg.getName())
            .setJobCategory(cfg.getCategory())
            .setJobCalSource(cfg.getCalSource())
            .setTimeTarget(cfg.getTimeTarget())
            .setChargeOrderRules(cfg.getChargeOrderRules())
            .setRemark(cfg.getRemark());

        TimeFilter timeFilter = null;
        switch (cfg.getTimeTarget()) {
            case PAY_TIME:
                timeFilter = orderParam.getPaymentTimeFilter();
                break;
            case STOP_TIME:
                timeFilter = orderParam.getStopTimeFilter();
                break;
            case CREATE_TIME:
                timeFilter = orderParam.getChargeCreateTimeFilter();
                break;
            case CHARGE_END_TIME:
                timeFilter = orderParam.getChargeStartTimeFilter();
                break;
            case CHARGE_START_TIME:
                timeFilter = orderParam.getChargeStopTimeFilter();
                break;
        }

        if (null != timeFilter) {
            settJobBill.setSettPeriodFrom(timeFilter.getStartTime())
                .setSettPeriodTo(timeFilter.getEndTime());
        }
    }
}
