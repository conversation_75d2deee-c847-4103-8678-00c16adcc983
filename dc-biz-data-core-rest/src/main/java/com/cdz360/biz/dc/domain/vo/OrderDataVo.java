package com.cdz360.biz.dc.domain.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单相关数据统计
 *
 * OrderDataVo
 * <AUTHOR>
 *
 * @since 2019.3.6
 */
@Data
public class OrderDataVo {
    /** id */
    private String id;
    /** 设备运营商id */
    private String commId;
    /** 设备运营商名 */
    private String commName;
    /** 站点id */
    private String siteId;
    /** 站点名 */
    private String siteName;
    /** 站点经度 */
    private String longitude;
    /** 站点维度 */
    private String latitude;
    /** 桩数量 */
    private Integer boxCount;
    /** 枪头数量 */
    private Integer chargerCount;
    /** 订单id */
    private String orderId;
    /** 订单结束时间，毫秒时间戳 */
    private Long orderEndTime;
    /** 订单时长(秒) */
    private String orderDuration;
    /** 订单电量(单位kwh) */
    private BigDecimal orderElectricity;
    /** 订单数量 */
    private String orderCount;
    /** 订单充电金额(元) */
    private BigDecimal orderPrice;

}
