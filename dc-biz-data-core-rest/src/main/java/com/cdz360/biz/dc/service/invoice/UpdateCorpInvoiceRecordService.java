package com.cdz360.biz.dc.service.invoice;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.biz.model.trading.invoice.dto.CorpInvoiceRecordDto;
import com.cdz360.biz.model.trading.invoice.vo.CorpInvoiceRecordVo;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordUpdateParam;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class UpdateCorpInvoiceRecordService {

    private Map<String, UpdateCorpInvoiceRecordStrategy> strategyMap = new HashMap<>();

    @Async
    public void updateAsync(CorpInvoiceRecordUpdateParam param, boolean append) {
        this.update(param, append);
    }

    /**
     * 企业客户开票记录更新
     *
     * @param param
     * @param append
     * @return
     */
    public CorpInvoiceRecordVo update(CorpInvoiceRecordUpdateParam param, boolean append) {
        InvoicingMode mode = param.getCorpInvoiceInfoVo().getInvoiceWay();
        if (null == mode) {
            throw new DcArgumentException("企业客户的平台开票方式无效");
        }

        UpdateCorpInvoiceRecordStrategy strategy = strategyMap.get(strategyKey(mode));
        if (null == strategy) {
            throw new DcArgumentException("企业客户的平台开票方式无效");
        }
        return strategy.updateRecord(param, append);
    }

    /**
     * 删除开票记录调用(模拟移除订单操作，回复订单数据)
     *
     * @param param
     * @return
     */
    public CorpInvoiceRecordVo deleteRecord(
            CorpInvoiceRecordUpdateParam param, CorpInvoiceRecordDto dto) {
        InvoicingMode mode = dto.getInvoiceWay();
        if (null == mode) {
            throw new DcArgumentException("企业客户的平台开票方式无效");
        }

        UpdateCorpInvoiceRecordStrategy strategy = strategyMap.get(strategyKey(mode));
        if (null == strategy) {
            throw new DcArgumentException("企业客户的平台开票方式无效");
        }
        return strategy.deleteRecord(param, dto);
    }

    public synchronized void addStrategy(
            InvoicingMode mode, UpdateCorpInvoiceRecordStrategy strategy) {
        String key = this.strategyKey(mode);
        strategyMap.put(key, strategy);
    }

    public String strategyKey(InvoicingMode mode) {
        return "corp:invoice:" + mode.name();
    }

    public UpdateCorpInvoiceRecordStrategy getStrategyByKey(InvoicingMode mode) {
        return strategyMap.get(strategyKey(mode));
    }

}
