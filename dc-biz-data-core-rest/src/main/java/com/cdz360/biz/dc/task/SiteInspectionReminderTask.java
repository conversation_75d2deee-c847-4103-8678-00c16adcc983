package com.cdz360.biz.dc.task;

import com.cdz360.biz.dc.service.yw.InspectionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SiteInspectionReminderTask {
    @Autowired
    private InspectionService service;

    @Scheduled(cron = "0 0 4 * * ?")
    public void inspectionReminder() {
        service.siteInspectionReminder();
    }
}
