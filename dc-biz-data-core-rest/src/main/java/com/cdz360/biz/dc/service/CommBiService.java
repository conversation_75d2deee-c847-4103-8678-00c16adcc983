package com.cdz360.biz.dc.service;

import com.cdz360.biz.ds.trading.ro.site.ds.CommRoDs;
import com.cdz360.biz.ds.trading.rw.comm.ds.BiTopCommRwDs;
import com.cdz360.biz.model.trading.comm.po.TopCommBiPo;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CommBiService {

    @Autowired
    private BiTopCommRwDs biTopCommRwDs;

    @Autowired
    private CommRoDs commRoDs;

    public void addTopCommBi() {
        List<Long> topCommIds = this.commRoDs.getTopCommIdList();
        topCommIds.stream().forEach(topCommId -> addTopCommBiX(topCommId));
    }

    /**
     * 统计1分钟内, 创建订单数,充电中订单数,停止订单数
     *
     * @param topCommId 集团商户ID
     */
    private void addTopCommBiX(Long topCommId) {
        Date time = this.biTopCommRwDs.getLastTime(topCommId);
        if (time == null) {
            // log.warn("过往记录不存在");
            return;
        }
        Date stopTime = new Date(time.getTime() + 60 * 1000L);    // 加1分钟
        Date curTime = new Date();
        LocalDateTime lastWeek = LocalDateTime.now().minusWeeks(1);
        while (curTime.after(stopTime)) {
            TopCommBiPo topCommBi = this.biTopCommRwDs.countTopCommBi(
                topCommId, lastWeek, time, stopTime);
            this.biTopCommRwDs.addTopCommBi(topCommBi);
            time.setTime(time.getTime() + 60 * 1000L);    // 加1分钟
            stopTime.setTime(stopTime.getTime() + 60 * 1000L);    // 加1分钟
        }
    }
}
