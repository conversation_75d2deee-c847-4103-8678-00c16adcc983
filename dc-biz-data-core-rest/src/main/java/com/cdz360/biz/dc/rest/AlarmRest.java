package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.AlarmBizService;
import com.cdz360.biz.dc.service.AlarmService;
import com.cdz360.biz.dc.service.WarningService;
import com.cdz360.biz.ess.model.param.GetAlarmListParam;
import com.cdz360.biz.ess.model.vo.DeviceAlarmDto;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import com.cdz360.biz.model.trading.warn.param.AddUserWarnParam;
import com.cdz360.biz.model.trading.warn.param.UserWarnListParam;
import com.cdz360.biz.model.trading.warn.param.WarnListParam;
import com.cdz360.biz.model.trading.warn.param.WarnSubParam;
import com.cdz360.biz.model.trading.warn.po.WarningPo;
import com.cdz360.biz.model.trading.warn.vo.UserWarningVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "告警订阅相关", description = "告警订阅")
public class AlarmRest {

    @Autowired
    private WarningService warningService;



    @Operation(summary = "根据条件获取告警列表")
    @PostMapping("/dataCore/warn/getWarnList")
    public ListResponse<WarningPo> getWarnList(@RequestBody WarnListParam param) {
        log.info("param = {}", param);
        return warningService.getWarnList(param);
    }

    @Operation(summary = "支撑平台消息订阅")
    @PostMapping("/dataCore/warn/queryWarningSub")
    public BaseResponse queryWarningSub(@RequestBody WarnSubParam param) {
        log.info("param = {}", param);
        return warningService.queryWarningSub(param);
    }

    @Operation(summary = "充电管理平台，订阅列表")
    @PostMapping("/dataCore/warn/getUserWarnList")
    public ListResponse<UserWarningVo> getUserWarnList(@RequestBody UserWarnListParam param) {
        log.info("param = {}", param);
        return warningService.getUserWarnList(param);
    }

    @Operation(summary = "充电管理平台，用户订阅")
    @PostMapping("/dataCore/warn/addOrUpdateUserWarn")
    public BaseResponse addOrUpdateUserWarn(@RequestBody AddUserWarnParam param) {
        log.info("param = {}", param);
        return warningService.addOrUpdateUserWarn(param);
    }

    @Operation(summary = "获取管理员订阅的产站ID")
    @GetMapping("/dataCore/warn/getUserSubSiteList")
    public ListResponse<String> getUserSubSiteList(@RequestParam("sysUid") Long sysUid) {
        return warningService.getUserSubSiteList(sysUid);
    }

    @Operation(summary = "获取管理员订阅的产站信息")
    @GetMapping("/dataCore/warn/getUserSubSiteInfoList")
    public ListResponse<SiteVo> getUserSubSiteInfoList(@RequestParam("sysUid") Long sysUid,
        @RequestParam("start") Long start,
        @RequestParam("size") Long size) {
        if (sysUid == null) {
            throw new DcServiceException("sysUid不能为空");
        }
        return warningService.getUserSubSiteInfoList(sysUid, start, size);
    }

    @Operation(summary = "获取管理员订阅的告警码")
    @GetMapping("/dataCore/warn/getUserSubCodeList")
    public ListResponse<String> getUserSubCodeList(@RequestParam("sysUid") Long sysUid) {
        return warningService.getUserSubCodeList(sysUid);
    }
}
