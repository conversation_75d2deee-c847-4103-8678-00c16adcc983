package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.service.site.SiteBizService;
import com.cdz360.biz.dc.utils.RedisUtil;
import com.cdz360.biz.ds.trading.ro.iot.ds.BsBoxRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.BsBoxSettingRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ds.trading.rw.iot.ds.BsBoxRwDs;
import com.cdz360.biz.ds.trading.rw.iot.ds.BsChargerRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteDefaultSettingRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteRwDs;
import com.cdz360.biz.model.iot.param.ModifyEvseCfgParam;
import com.cdz360.biz.model.trading.iot.po.BsBoxPo;
import com.cdz360.biz.model.trading.iot.po.BsChargerPo;
import com.cdz360.biz.model.trading.iot.vo.BsChargerMoreVo;
import com.cdz360.biz.model.trading.site.po.BsBoxSettingPo;
import com.cdz360.biz.model.trading.site.po.SiteDefaultSettingPo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.framework.common.domain.param.BoxActivateRequest;
import com.chargerlinkcar.framework.common.domain.type.EvseCfgEnum;
import com.chargerlinkcar.framework.common.feign.DzDeviceFeignClient;
import com.chargerlinkcar.framework.common.feign.IotBizClient;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

@Slf4j
@Service
public class DeviceBizService {

    @Autowired
    private BsBoxRoDs bsBoxRoDs;
    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private SiteRwDs siteRwDs;
    @Autowired
    private BsBoxSettingRoDs bsBoxSettingRoDs;


    @Autowired
    private BsBoxRwDs bsBoxRwDs;
    @Autowired
    private BsChargerRwDs bsChargerRwDs;

    @Autowired
    private SiteDefaultSettingRwDs siteDefaultSettingRwDs;

    @Autowired
    private BsBoxSettingService bsBoxSettingService;
    @Autowired
    private EvseCfgScheduleService evseCfgScheduleService;

    @Autowired
    private RedisIotReadService redisIotReadService;

    @Autowired
    private IotBizClient iotBizClient;
    @Autowired
    private DzDeviceFeignClient dzDeviceFeignClient;
    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private SiteBizService siteBizService;

    @Autowired
    private DeviceBizService deviceBizService;

    public BsBoxPo getBsBox(String evseNo) {
        return this.bsBoxRoDs.getBsBox(evseNo);
    }

    public BsChargerPo getBsCharge(String plugNo) {
        return this.bsChargerRwDs.getBsChargerByPlugNo(plugNo);
    }

    public BsChargerMoreVo getBsChargeMoreVo(String plugNo) {
        return this.bsChargerRwDs.getBsChargerMoreVoByPlugNo(plugNo);
    }

    @Transactional
    public boolean updateBsBox(BsBoxPo bsBox) {
        boolean result = this.bsBoxRwDs.updateBsBox(bsBox);

        // 更新场站表的功率数据: MQ推送会做更新处理
//        siteRwDs.updateSitePower(bsBox.getSiteId());

        if (bsBox.getPriceCode() != null && StringUtils.isNotBlank(bsBox.getEvseNo())) {
            // 更新 t_bs_box_setting
            log.info("更新桩计费模板: evse: {}, priceCode{}", bsBox.getEvseNo(),
                bsBox.getPriceCode());
            BsBoxSettingPo bsBoxSettingPo = new BsBoxSettingPo();
            bsBoxSettingPo.setBoxCode(bsBox.getEvseNo())
                .setBoxOutFactoryCode(bsBox.getEvseNo());
//                    .setChargeId(bsBox.getPriceCode());
            bsBoxSettingService.insertOrUpdate(bsBoxSettingPo);
        }

        // 是否需要更新桩配置
        if (bsBox.getUseSiteSetting() != null && bsBox.getUseSiteSetting()) {
            BsBoxPo oldPo = this.getBsBox(bsBox.getEvseNo());

            if (null != oldPo && oldPo.getUseSiteSetting()) {
                log.info("需要更新桩的配置信息: evseNo = {}, siteId = {}", bsBox.getEvseNo(),
                    oldPo.getSiteId());
                // 使用场站配置，但是不关联场站计费
                this.downSetting2Evse(bsBox.getEvseNo(), oldPo.getSiteId());
            }
        }

        return result;
    }

    @Transactional
    public boolean updateBsBoxList(List<BsBoxPo> bsBox) {
        return bsBox.stream()
            .map(this::updateBsBox)
            .reduce(Boolean.TRUE, Boolean::logicalAnd);
    }

    /**
     * 给桩下发场站默认配置信息
     *
     * @param evseNo 桩编号
     * @param siteId 场站Id
     */
    public void downSetting2Evse(String evseNo, String siteId) {
        log.info("给桩下发场站默认配置: evseNo = {}, siteId = {}", evseNo, siteId);
        if (StringUtils.isBlank(evseNo) || StringUtils.isBlank(siteId)) {
            log.info("桩编号或场站Id没有提供");
            throw new DcArgumentException("桩编号或场站Id没有提供");
        }

        // t_site_defult_setting
        SiteDefaultSettingPo siteDefaultSetting = siteDefaultSettingRwDs.getBySiteId(siteId);
        if (null == siteDefaultSetting) {
            log.info("该场站没有默认配置信息");
            throw new DcArgumentException("场站Id无效, 没有默认配置信息");
        }

        // 配置下发
        ModifyEvseCfgParam param = new ModifyEvseCfgParam();
        BeanUtils.copyProperties(siteDefaultSetting, param);
        param.setQrUrl(siteDefaultSetting.getUrl());
        param.setEvseNoList(Collections.singletonList(evseNo));
        this.downSetting2Evse(param);
    }

    /**
     * 给桩下发配置
     *
     * @param param
     */
    public void downSetting2Evse(ModifyEvseCfgParam param) {
        log.info("给桩下发配置: param = {}", JsonUtils.toJsonString(param));
        if (CollectionUtils.isEmpty(param.getEvseNoList())) {
            log.info("桩编号或场站Id没有提供");
            throw new DcArgumentException("桩编号或场站Id没有提供");
        }

        // t_bs_box_setting 中记录g更新
        bsBoxSettingService.sendInsertOrUpdate(param);

        // 配置下发
        BaseResponse baseResponse = this.iotBizClient.modifyEvseCfgV2(param);
        log.info("下发桩关联的场站配置信息完成");

        // 变更桩配置状态
        if (null == baseResponse || baseResponse.getStatus() != 0) {
            log.warn("下发配置iot返回异常");
            bsBoxSettingService.updateStatus(param.getEvseNoList(), EvseCfgEnum.EVSE_CFG_SEND_FAIL);
        }
    }

    @Transactional
    public void activateBoxInfo(BoxActivateRequest request) throws DcServiceException {
        log.info("初始化bs_box, request={}", JsonUtils.toJsonString(request));
        //STEP 1-参数校验
        //STEP 1.1-判断设备是否重复
//        BsBoxPo oldBox = bsBoxRoDs.getBsBox(request.getSerialNumber());
//        if (oldBox != null) {
//            throw new DcServiceException("新增充电桩失败，该设备已经添加");
//        }

        // 获取站点信息
        SitePo siteInfo;
        {
            //STEP 1.2-判断站点是否存在
            siteInfo = siteRoDs.getSite(request.getSiteId());
            Assert.notNull(siteInfo, "新增充电桩失败，未找到站点信息");
        }

        // 获取设备信息
        EvseVo evseCache;
        {
            evseCache = this.redisIotReadService.getEvseRedisCache(request.getSerialNumber());
            if (evseCache == null) {
                log.warn("无法找到设备. evseNo = {}", request.getSerialNumber());
                throw new DcServiceException("请确认设备已连接到云端");
            }
        }

        //STEP 2.1-保存设备信息
        BsBoxPo entity = new BsBoxPo();
        // 设备ID
        entity.setEvseNo(evseCache.getEvseNo());
        //是否关联站点默认计费模板
        entity.setIsAssociateSiteTemplate(request.getIsAssociateSiteTemplate());
        //设备名称
        entity.setEvseName(request.getBoxName());
        // 代理商ID
        entity.setBusinessId(String.valueOf(siteInfo.getOperateId()));
        // 站点编号
        entity.setSiteId(siteInfo.getId());
        // 有效枪头数量
        entity.setPlugNum(evseCache.getPlugNum());
        // 交直流类型**0-交流 1-直流 2-交直流**
        if (evseCache.getSupplyType() == SupplyType.AC) {
            entity.setCurrentType(0);
        } else if (evseCache.getSupplyType() == SupplyType.DC) {
            entity.setCurrentType(1);
        }
        // 是否使用场站默认桩配置 1是0否
        if (null != request.getIsUseSiteDefaultSetting()) {
            entity.setUseSiteSetting(
                request.getIsUseSiteDefaultSetting().equals(1) ? Boolean.TRUE : Boolean.FALSE);
        }
        //额定功率
        entity.setRatedPower(request.getRatedPower());
        entity.setModelName(request.getModelName());
        entity.setFirmwareVer(request.getFirmwareVer());
        bsBoxRwDs.insertBsBox(entity);

        //STEP 2.0 下发桩默认配置信息
        if ((request.getIsUseSiteDefaultSetting() != null
            && request.getIsUseSiteDefaultSetting() == 1)) {
            if (evseCache.getStatus() == EvseStatus.OFFLINE) {
                log.warn("设备离线. evseNo = {}", request.getSerialNumber());
                // 生成一条空的t_bs_box_setting
                BsBoxSettingPo po = new BsBoxSettingPo();
                po.setBoxOutFactoryCode(evseCache.getEvseNo());
                po.setBoxCode(evseCache.getEvseNo());
                po.setStatus(2);
                bsBoxSettingRoDs.insertOrUpdate(po);
            } else {
                log.info("下发场站默认配置到桩: evseNo = {}", evseCache.getEvseNo());
                // 使用场站配置，但是不关联场站计费
                dzDeviceFeignClient.downSetting2Evse(evseCache.getEvseNo(), request.getSiteId());
            }
        } else {
            // 生成一条空的t_bs_box_setting
            BsBoxSettingPo po = new BsBoxSettingPo();
            po.setBoxOutFactoryCode(evseCache.getEvseNo());
            po.setBoxCode(evseCache.getEvseNo());
            bsBoxSettingRoDs.insertOrUpdate(po);
        }

//        这里统计已经不符合要求
        siteBizService.recordEvsePlugInfo(request.getSiteId(), null);
        // 更新t_site表的功率统计
//        siteRwDs.updateSitePower(request.getSiteId());
    }


    public void initBsChargerInfo(PlugVo plugVo) {
        log.info("初始化bs_charger");
        EvseVo evseCache = this.redisIotReadService.getEvseRedisCache(plugVo.getEvseNo());
        BsChargerPo bsChargerPo = new BsChargerPo();
        bsChargerPo.setEvseNo(evseCache.getEvseNo());
        bsChargerPo.setPlugNo(plugVo.getPlugNo());
        bsChargerPo.setBusinessId(String.valueOf(evseCache.getSiteCommId()));
        bsChargerPo.setStationCode(evseCache.getSiteId());
        // 枪头connectorId
        bsChargerPo.setConnectorId(plugVo.getIdx());
        if (StringUtils.isNotBlank(plugVo.getName())) {
            bsChargerPo.setChargerName(plugVo.getName());
        }
        // 枪头类型
        if (evseCache.getSupplyType() == SupplyType.AC) {
            bsChargerPo.setCurrentType(0);
        } else if (evseCache.getSupplyType() == SupplyType.DC) {
            bsChargerPo.setCurrentType(1);
        }
        //新增充电接口
        log.info("插入bsCharge = {}", JsonUtils.toJsonString(bsChargerPo));
        bsChargerRwDs.insertBsCharger(bsChargerPo);
    }

    @Transactional
    public void unActivateBoxInfo(String evseNo) throws DcServiceException {
        log.info("收到桩已解绑MQ evseNo={}", evseNo);
        // SETP 1.校验代理商是否匹配
        BsBoxPo bsBoxPo = bsBoxRoDs.getBsBox(evseNo);
        Assert.notNull(bsBoxPo, "未找到设备信息");

        EvseVo evse = this.redisIotReadService.getEvseRedisCache(evseNo);
        if (evse != null && evse.getStatus() == EvseStatus.BUSY) {
            throw new DcServiceException("当前有插座正在充电中，不可解绑");
        }

        // SETP 2.删除枪头
        bsChargerRwDs.delBsCharger(evseNo);
        // SETP 3.删除盒子
        bsBoxRwDs.delBsBox(evseNo);

        // SETP 4.删除桩配置
        bsBoxSettingRoDs.delBoxSetting(evseNo);

        // SETP 5.disable桩配置下发定时记录,t_evse_cfg_schedule
        evseCfgScheduleService.disableScheduleByEvseNo(null, Collections.singletonList(evseNo));

        // 更新t_site表的功率统计
        if (evse.getSiteId() != null) {
            siteRwDs.updateSitePower(evse.getSiteId());
        }
    }

    /**
     * 桩端价格配置下发成功后更新 bs_box 和 bs_charger 表
     *
     * @param evseNo
     * @param priceCode
     */
    public void postPriceActive(String evseNo, Long priceCode) {
        log.info("更新价格模板 evseNo = {}, priceCode = {}", evseNo, priceCode);
        BsBoxPo update = new BsBoxPo();
        update.setEvseNo(evseNo).setPriceCode(priceCode);
        bsBoxRwDs.updateBsBox(update);

    }

    public List<EvseVo> getEvseList(String siteId) {
        return bsBoxRoDs.getEvseList(siteId);
    }

    public List<PlugVo> getPlugList(String siteId) {
        return bsChargerRwDs.getPlugList(siteId);
    }
}
