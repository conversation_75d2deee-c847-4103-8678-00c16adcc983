package com.cdz360.biz.dc.service.ess;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.model.es.po.EssAlarmPo;
import com.cdz360.base.model.es.type.WarnDeviceType;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DateUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.client.reactor.ReactorDeviceMonitorFeignClient;
import com.cdz360.biz.ds.trading.ro.site.ds.BiPlugRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.EssSiteRoDs;
import com.cdz360.biz.ds.trading.rw.order.ds.BiPlugRwDs;
import com.cdz360.biz.ess.model.param.ListEssAlarmParam;
import com.cdz360.biz.ess.model.site.param.ListEssSiteParam;
import com.cdz360.biz.ess.model.site.vo.EssSiteVo;
import com.cdz360.biz.model.trading.site.po.BiPlugPo;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class EssWarnBiService {

    @Autowired
    private ReactorDeviceMonitorFeignClient deviceMonitorFeignClient;

    @Autowired
    private BiPlugRwDs biPlugRwDs;

    @Autowired
    private BiPlugRoDs biPlugRoDs;

    @Autowired
    private EssSiteRoDs essSiteRoDs;

    @Async
    public void commEssDailyBi(Date date, String siteId) {
        log.info("工商储场站告警统计: date = {}, siteId = {}", date, siteId);
        long start = 0;
        int size = 100;

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.clear(Calendar.MILLISECOND);
        calendar.clear(Calendar.SECOND);
        calendar.clear(Calendar.MINUTE);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        final Date startTime = calendar.getTime();

        ListEssSiteParam siteParam = new ListEssSiteParam();
        siteParam.setSize(size).setStart(start);

        if (StringUtils.isNotBlank(siteId)) {
            siteParam.setSiteIdList(List.of(siteId));
        }

        while (true) {
            List<String> siteIdList = essSiteRoDs.findEssSite(siteParam).stream()
                .map(EssSiteVo::getId)
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(siteIdList)) {
                final ListEssAlarmParam alarmParam = new ListEssAlarmParam();
                alarmParam.setSize(500).setStart(0L);
                alarmParam.setDeviceType(WarnDeviceType.COMM_ESS)
                    .setHappenTimeFilter(new TimeFilter()
                        .setStartTime(startTime).setEndTime(
                            DateUtils.addDays(startTime, 1)));
                siteIdList.forEach(si -> this.siteEssWarnData(alarmParam, startTime, si));
            }

            if (siteIdList.size() < size) {
                break;
            }

            start += size;
            siteParam.setStart(start);
        }
        log.info("工商储场站告警统计完成: date = {}, siteId = {}", date, siteId);
    }


    public void siteEssWarnData(ListEssAlarmParam alarmParam, Date date, String siteId) {
        deviceMonitorFeignClient.getEssAlarmList(alarmParam.setSiteIdList(List.of(siteId)))
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .doOnNext(dataList -> {
                Map<String, List<EssAlarmPo>> essMap = dataList.stream()
                    .filter(x -> StringUtils.isNotEmpty(x.getEssDno()))
                    .collect(Collectors.groupingBy(EssAlarmPo::getEssDno));

                essMap.forEach((dno, v) -> {
//                    Map<EssEquipType, Long> dnoMap = v.stream()
//                        .filter(x -> null != x.getEquipType())
//                        .collect(Collectors.groupingBy(EssAlarmPo::getEquipType,
//                            Collectors.counting()));
//
//                    dnoMap.forEach((type, cnt) -> {
                        BiPlugPo bi = biPlugRoDs.getOneByUniqueKey(siteId, dno, 0, date);
                        if (null == bi) {
                            bi = new BiPlugPo();
                            bi.setSiteId(siteId)
                                .setEvseNo(dno)
                                .setDno(dno)
                                .setType(3L)    // 储能设备告警
                                .setPlugId(0)
                                .setDate(date)
                                .setElectricity(BigDecimal.ZERO)
                                .setErrorCount(v.size())
                                .setOfflineCount(0);
                        } else {
//                            if (null != bi.getErrorCount()) {
//                                bi.setErrorCount(bi.getErrorCount() + cnt.intValue());
//                            } else {
                                bi.setErrorCount(v.size());
//                            }
                        }
                        biPlugRwDs.insertOrUpdate(bi);
                    });
//                });
            }).block(Duration.ofSeconds(50L));
        log.info("统计完成: siteId = {}, date = {}", siteId, date);
    }
}
