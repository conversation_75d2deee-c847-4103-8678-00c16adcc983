package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.charge.vo.SiteVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.dc.service.ContractService;
import com.cdz360.biz.model.trading.contract.param.AddContractParam;
import com.cdz360.biz.model.trading.contract.param.ContractListParam;
import com.cdz360.biz.model.trading.contract.vo.ContractVo;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "合约管理相关接口", description = "合约管理")
@RequestMapping("/dataCore/contract")
public class ContractRest {
    @Autowired
    private ContractService contractService;

    /**
     * 添加合约
     *
     * @param param
     * @return
     */
    @PostMapping("/addContract")
    public BaseResponse addContract(@RequestBody AddContractParam param) {
        log.debug("param = {}", JsonUtils.toJsonString(param));
        return contractService.addContract(param);
    }

    /**
     * 删除合约
     *
     * @param id
     * @param idChain
     * @return
     */
    @GetMapping("/delContract")
    public BaseResponse delContract(@RequestParam(value = "id") Long id,
                                    @RequestParam(value = "idChain",required = false) String idChain,
                                    @RequestParam(value = "sysUid") Long sysUid) {
        log.debug("id = {},idChain={},sysUid={}", id, idChain, sysUid);
        return contractService.delContract(id, idChain, sysUid);
    }

    /**
     * 更新合约
     *
     * @param param
     * @return
     */
    @PostMapping("/updateContract")
    public BaseResponse updateContract(@RequestBody AddContractParam param) {
        log.debug("param = {}", JsonUtils.toJsonString(param));
        return contractService.updateContract(param);
    }

    /**
     * 合约详情
     *
     * @param contractId
     * @return
     */
    @GetMapping("/getContractById")
    public ObjectResponse<ContractVo> getContractById(@RequestParam("contractId") Long contractId,
                                                      @RequestParam(value = "idChain",required = false) String idChain) {
        log.debug("contractId = {},idChain={}", contractId, idChain);
        return new ObjectResponse<>(contractService.getContractById(contractId, idChain));
    }

    /**
     * 合约列表
     *
     * @param param
     * @return
     */
    @PostMapping("/getContractList")
    public ListResponse<ContractVo> getContractList(@RequestBody ContractListParam param) {
        log.debug("合约列表 params = {}", JsonUtils.toJsonString(param));
        return contractService.getContractList(param);
    }

    /**
     * 场站最近若干条合约
     * 如果产站没有合约，则取场站所属商户上级没有指定产站的合约
     *
     * @return
     */
    @GetMapping("/getContractBySiteId")
    public Mono<ListResponse<ContractVo>> getContractBySiteId(@RequestParam(value = "siteId") String siteId,
                                                              @RequestParam(value = "size", required = false) Long size) {
        log.info("siteId={},size={}", siteId, size);
        return contractService.getContractBySiteId(siteId, size);
    }

    @GetMapping("/getSiteListByContractId")
    public Mono<ListResponse<SiteVo>> getSiteListByContractId(@RequestParam(value = "contractId") Long contractId) {
        IotAssert.isNotNull(contractId, "合约ID不能为空");
        return contractService.getSiteListByContractId(contractId);
    }
}
