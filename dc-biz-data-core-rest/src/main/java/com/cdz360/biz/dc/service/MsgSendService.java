package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.auth.user.param.SysUserOpenIdParam;
import com.cdz360.biz.dc.utils.GenerateIdByRedisAtomicUtils;
import com.cdz360.biz.dc.utils.RedisUtil;
import com.cdz360.biz.ds.trading.ro.iot.ds.BsBoxRoDs;
import com.cdz360.biz.ds.trading.ro.msg.ds.MsgTemplateRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ds.trading.ro.warn.ds.WarningRoDs;
import com.cdz360.biz.ds.trading.ro.warn.mapper.WarningRoMapper;
import com.cdz360.biz.model.common.constant.DcBizConstants;
import com.cdz360.biz.model.cus.commercial.vo.CommercialManage;
import com.cdz360.biz.model.cus.message.type.MsgTemplate;
import com.cdz360.biz.model.trading.iot.po.BsBoxPo;
import com.cdz360.biz.model.trading.msg.param.MsgSendParam;
import com.cdz360.biz.model.trading.msg.param.ParkingLockErrorParam;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.warn.po.WarningPo;
import com.cdz360.biz.model.trading.yw.po.YwOrderPo;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.framework.common.feign.AuthCenterFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import reactor.core.publisher.Mono;


@Slf4j
@Service
public class MsgSendService {

    @Autowired
    private WarningRoMapper warningRoMapper;
    @Autowired
    private SiteRoDs siteRoDs;
    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;
    @Autowired
    private MsgTemplateRoDs msgTemplateRoDs;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private BsBoxRoDs bsBoxRoDs;
    @Autowired
    private WarningRoDs warningRoDs;

    @Autowired
    private RedisIotReadService redisIotReadService;

    private String ORDER_PAGE = "pages/viewOrderDetail/viewOrderDetail?orderNo=";
    private String WARN_PAGE = "pages/warning/warningList/warningList?warningId=";
    private String OPERATION_PAGE = "pages/operations/operationDetail/operationDetail?ywOrderNo=";
    private String SITE_CONFIG_DETAIL_PAGE = "pages/siteConfigDetail/siteConfigDetail?deviceId=";


    private String wxChatToken = "wxChatAccessToken";

    public BaseResponse sendMsg(@RequestBody MsgSendParam param) {
        IotAssert.isNotNull(param.getSiteId(), "站点ID不能为空");
        IotAssert.isNotNull(param.getWarningCode(), "告警码不能为空");

        //获取场站信息
        SitePo sitePo = siteRoDs.getSite(param.getSiteId());
        if (sitePo == null) {
            log.error("桩告警，场站不存在", param.getSiteId());
            throw new DcServiceException("场站信息不存在");
        }
        param.setSiteName(sitePo.getSiteName()).setTopCommId(sitePo.getTopCommId());
        //获取配置的wechat_appid
        ObjectResponse<CommercialManage> commercialManageObjectResponse = authCenterFeignClient.getCommercialManage(
            sitePo.getTopCommId());
        if (commercialManageObjectResponse.getData() != null
            && commercialManageObjectResponse.getData().getWechatAppid() != null
            && commercialManageObjectResponse.getData().getWechatAppSecret() != null) {
            param.setWechatAppid(commercialManageObjectResponse.getData().getWechatAppid());
            param.setWechatAppSecret(commercialManageObjectResponse.getData().getWechatAppSecret());
        } else {
            log.warn("桩管家微信公众号配置信息不存在,siteId={},topCommId={} ", param.getSiteId(),
                sitePo.getTopCommId());
            return new BaseResponse(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "未配置桩管家公众号");
        }

        //桩管家appid
        if (StringUtils.isEmpty(commercialManageObjectResponse.getData().getWxLiteMgmAppId())) {
            log.warn("未配置桩管家小程序appid,siteID={},topCommId={}", param.getSiteId(),
                sitePo.getTopCommId());
        } else {
            param.setWxLiteMgmAppId(commercialManageObjectResponse.getData().getWxLiteMgmAppId());
        }

        List<Long> userIdList = warningRoMapper.getUserIdByCondition(param.getSiteId(),
            param.getWarningCode());
        log.info("用户信息,userinfo.size={}", userIdList.size());
        if (userIdList.size() == 0) {
            //throw new DcServiceException("不存在订阅管理员");
            log.debug("无用户订阅 evseNo = {}", param.getBoxOutFactoryCode());
            return RestUtils.success();
        }
        SysUserOpenIdParam sysUserOpenIdParam = new SysUserOpenIdParam();
        sysUserOpenIdParam.setUserIdList(userIdList);

        ListResponse<String> listResponse = authCenterFeignClient.getUserByIdList(
            sysUserOpenIdParam);
        if (listResponse.getData() == null) {
            throw new DcServiceException("不存在绑定公众号的管理员");
        }
        param.setUserOpenIdList(listResponse.getData());

        //获取告警码详细信息
        WarningPo warningPo = warningRoMapper.getWarnInfoByCode(param.getWarningCode());
        if (warningPo == null) {
            log.warn("告警码不存在warningCode={}", param.getWarningCode());
            throw new DcServiceException("告警码相关信息不存在");
        }
        param.setWarningInstructions(
            StringUtils.isNotBlank(param.getCustomWarningDesc()) ? param.getCustomWarningDesc()
                : warningPo.getWarningDesc());

        this.sendWxTemplateMsg(param);

        return BaseResponse.success();
    }

    /**
     * 告警恢复提醒
     *
     * @param param
     */
    public void sendWxTemplateMsg(MsgSendParam param) {
        //消息模板
        String msgTemplateValue = null;

        if (param.getEndTime() != null) {
            msgTemplateValue = msgTemplateRoDs.getMsgTemplateByTopCommId(param.getTopCommId(),
                MsgTemplate.WECHAT_RESTORE_REMIND.toString());
        } else {
            msgTemplateValue = msgTemplateRoDs.getMsgTemplateByTopCommId(param.getTopCommId(),
                MsgTemplate.WECHAT_ALERT_REMIND.toString());
        }

        if (msgTemplateValue == null) {
            log.warn("消息模板不存在,topCommId={},msgType={}", param.getTopCommId());
            throw new DcServiceException("消息模板不存在");
        }
        String accessToken = this.getAccessToken(param);
        if (accessToken == null) {
            throw new DcServiceException("获取access_token失败");
        }

        String url =
            "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=" + accessToken;

        Map<String, Object> postData = new HashMap<>();
        Map<String, Object> commData = new HashMap<>();

        postData.put("template_id", msgTemplateValue);

        //first
        Map<String, Object> jsonObject1 = new HashMap<>();
        jsonObject1.put("value", "ID:" + param.getWarningId() + "   " + param.getWarningName());
        commData.put("first", jsonObject1);

        //keyword1
        Map<String, Object> jsonObject2 = new HashMap<>();
        jsonObject2.put("value", param.getSiteName());
        commData.put("keyword1", jsonObject2);

        //keyword2
        Map<String, Object> jsonObject3 = new HashMap<>();

        // 固定8000为地锁告警通知
        String parkErrorCode = "8000";
        if (param.getCtrlNo() != null) {
            jsonObject3.put("value", "控制器:" + param.getCtrlNo());
        } else if (param.getWarningCode() != null && param.getWarningCode().equals(parkErrorCode)) {
            StringBuilder str = new StringBuilder();
            str.append("地锁告警:").append(param.getDeviceId());

            if (param.getEvseName() != null) {
                str.append("(")
                    .append(param.getEvseName())
                    .append(param.getPlugName())
                    .append(")");
            }
            jsonObject3.put("value", str);
        } else {
            StringBuilder str = new StringBuilder();
            str.append("桩告警:").append(param.getDeviceId());

            if (param.getConnectorId() != null) {
                str.append(param.getConnectorId());
            }
            if (param.getEvseName() != null) {
                str.append("(").append(param.getEvseName()).append(")");
            }
            jsonObject3.put("value", str);
        }
        commData.put("keyword2", jsonObject3);

        //keyword3
        Map<String, Object> jsonObject4 = new HashMap<>();
        jsonObject4.put("value", param.getWarningCode());
        commData.put("keyword3", jsonObject4);

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //keyword4
        Map<String, Object> jsonObject5 = new HashMap<>();
        jsonObject5.put("value",
            param.getStartTime() != null ? simpleDateFormat.format(param.getStartTime()) : "未知");
        commData.put("keyword4", jsonObject5);

        //keyword5
        if (param.getEndTime() != null) {
            Map<String, Object> jsonObject6 = new HashMap<>();
            jsonObject6.put("value", simpleDateFormat.format(param.getEndTime()));
            commData.put("keyword5", jsonObject6);
        }
        //remark
        Map<String, Object> jsonObject7 = new HashMap<>();
        jsonObject7.put("value", param.getWarningInstructions());
        commData.put("remark", jsonObject7);

        postData.put("data", commData);

        //miniprogram
        if (StringUtils.isNotEmpty(param.getWxLiteMgmAppId())) {
            Map<String, Object> jsonObject8 = new HashMap<>();
            jsonObject8.put("appid", param.getWxLiteMgmAppId());
            if (Boolean.TRUE.equals(param.getIsEvseCfgAlarmMsg())) {
                jsonObject8.put("pagepath", SITE_CONFIG_DETAIL_PAGE.concat(param.getDeviceId()));
            } else if (StringUtils.isNotEmpty(param.getOrderNo())) {
                jsonObject8.put("pagepath", ORDER_PAGE.concat(param.getOrderNo()));
            } else {
                jsonObject8.put("pagepath", WARN_PAGE.concat(param.getWarningId().toString()));
            }
            postData.put("miniprogram", jsonObject8);
        }

        param.getUserOpenIdList().forEach(e -> {
            postData.put("touser", e);
            try {
                this.sendPost(url, JsonUtils.toJsonString(postData));
            } catch (IOException ex) {
                log.info("发送失败,message={}", ex.getMessage(), ex);
//                ex.printStackTrace();
            }
        });
    }

    /**
     * 运维工单提醒
     */
    public Mono<BaseResponse> sendWxOperationMsg(YwOrderPo param, List<String> evseStrList) {
        log.info("运维工单发送,param={}", JsonUtils.toJsonString(param));
        //获取场站信息
        SitePo sitePo = siteRoDs.getSite(param.getSiteId());
        return Mono.just(sitePo).filter(x -> x.getId() != null)
            .flatMap(
                x -> Mono.just(authCenterFeignClient.getCommercialManage(sitePo.getTopCommId())))
            .doOnNext(FeignResponseValidate::checkIgnoreData).map(ObjectResponse::getData)
            .filter(comm -> StringUtils.isNotEmpty(comm.getWechatAppid()) && StringUtils.isNotEmpty(
                comm.getWxLiteMgmAppId()) && StringUtils.isNotEmpty(comm.getWechatAppSecret()))
            .flatMap(comm -> {

                String msgTemplateValue = msgTemplateRoDs.getMsgTemplateByTopCommId(
                    sitePo.getTopCommId(), MsgTemplate.WECHAT_OPERATION_REMIND.toString());
                if (StringUtils.isEmpty(msgTemplateValue)) {
                    return Mono.just(RestUtils.success());
                }
                MsgSendParam msgSendParam = new MsgSendParam();
                msgSendParam.setTopCommId(sitePo.getTopCommId())
                    .setWechatAppid(comm.getWechatAppid())
                    .setWechatAppSecret(comm.getWechatAppSecret());

                // 获取accessToken
                String accessToken = this.getAccessToken(msgSendParam);

                // 提取一个桩信息
                String evseStr = "--";
                if (CollectionUtils.isNotEmpty(evseStrList)) {
                    evseStr = evseStrList.get(0);
                }

                String url =
                    "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token="
                        + accessToken;

                Map<String, Object> postData = new HashMap<>();
                Map<String, Object> commData = new HashMap<>();

                postData.put("template_id", msgTemplateValue);

                //first
                Map<String, Object> jsonObject1 = new HashMap<>();
                jsonObject1.put("value", "您好,您有新的请修信息");
                commData.put("first", jsonObject1);

                //keyword1
                Map<String, Object> jsonObject2 = new HashMap<>();
                jsonObject2.put("value", sitePo.getSiteName());
                commData.put("keyword1", jsonObject2);

                Map<String, Object> jsonObject3 = new HashMap<>();
                jsonObject3.put("value", evseStr);
                commData.put("keyword2", jsonObject3);

                //keyword3
                Map<String, Object> jsonObject4 = new HashMap<>();
                jsonObject4.put("value", param.getFaultDesc());
                commData.put("keyword3", jsonObject4);

                //keyword4
                Map<String, Object> jsonObject5 = new HashMap<>();
                jsonObject5.put("value", param.getCreateOpName());
                commData.put("keyword4", jsonObject5);

                //keyword5
                Map<String, Object> jsonObject6 = new HashMap<>();
                jsonObject6.put("value", param.getMaintName());
                commData.put("keyword5", jsonObject6);

                //remark
                Map<String, Object> jsonObject7 = new HashMap<>();
                jsonObject7.put("value", "请尽快维修并反馈");
                commData.put("remark", jsonObject7);

                postData.put("data", commData);

                //miniprogram
                if (StringUtils.isNotEmpty(comm.getWxLiteMgmAppId())
                    && StringUtils.isNotEmpty(
                    param.getYwOrderNo())) {
                    Map<String, Object> jsonObject8 = new HashMap<>();
                    jsonObject8.put("appid", comm.getWxLiteMgmAppId());
                    jsonObject8.put("pagepath",
                        OPERATION_PAGE.concat(param.getYwOrderNo()));
                    postData.put("miniprogram", jsonObject8);
                }

                SysUserOpenIdParam sysUserOpenIdParam = new SysUserOpenIdParam();
                sysUserOpenIdParam.setUserIdList(Arrays.asList(param.getMaintUid()));
                //剔除创建人
                sysUserOpenIdParam.setOpUid(param.getCreateOpUid());

                return Mono.just(authCenterFeignClient.getUserByIdList(sysUserOpenIdParam))
                    .doOnNext(FeignResponseValidate::checkIgnoreData)
                    .map(ListResponse::getData)
                    .filter(CollectionUtils::isNotEmpty).doOnNext(userList -> {
                        userList.forEach(e -> {
                            postData.put("touser", e);
                            try {
                                this.sendPost(url, JsonUtils.toJsonString(postData));
                            } catch (IOException ex) {
                                log.info("发送失败,message={}", ex.getMessage(), ex);
                            }
                        });
                    }).map(xx -> RestUtils.success());

            });

//        String msgTemplateValue = msgTemplateRoDs.getMsgTemplateByTopCommId(sitePo.getTopCommId(),
//            MsgTemplate.WECHAT_OPERATION_REMIND.toString());
//
//        if (msgTemplateValue == null) {
//            log.warn("消息模板不存在,topCommId={},msgType={}", sitePo.getTopCommId());
//            return RestUtils.success();
//        }

//        MsgSendParam msgSendParam = new MsgSendParam();
//        msgSendParam.setTopCommId(sitePo.getTopCommId());

        //获取配置的wechat_appid
//        ObjectResponse<CommercialManage> commercialManageObjectResponse = authCenterFeignClient.getCommercialManage(
//            sitePo.getTopCommId());
//        if (commercialManageObjectResponse.getData() != null
//            && commercialManageObjectResponse.getData().getWechatAppid() != null
//            && commercialManageObjectResponse.getData().getWechatAppSecret() != null) {
//            msgSendParam.setWechatAppid(commercialManageObjectResponse.getData().getWechatAppid());
//            msgSendParam.setWechatAppSecret(
//                commercialManageObjectResponse.getData().getWechatAppSecret());
//        } else {
//            log.warn("桩管家微信公众号配置信息不存在,siteId={},topCommId={} ", param.getSiteId(),
//                sitePo.getTopCommId());
//            return new BaseResponse(DcConstants.KEY_RES_CODE_SERVICE_ERROR, "未配置桩管家公众号");
//        }

        //桩管家appid
//        String wxLiteMgmAppId = null;
//        if (StringUtils.isEmpty(commercialManageObjectResponse.getData().getWxLiteMgmAppId())) {
//            log.warn("未配置桩管家小程序appid,siteID={},topCommId={}", param.getSiteId(),
//                sitePo.getTopCommId());
//        } else {
//            wxLiteMgmAppId = commercialManageObjectResponse.getData().getWxLiteMgmAppId();
//        }

//        String accessToken = this.getAccessToken(msgSendParam);
//        if (accessToken == null) {
//            throw new DcServiceException("获取access_token失败");
//        }

        //提取一个桩信息
//        String evseStr = "--";
//        if (CollectionUtils.isNotEmpty(evseStrList)) {
//            evseStr = evseStrList.get(0);
//        }

//        String url =
//            "https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=" + accessToken;
//
//        Map<String, Object> postData = new HashMap<>();
//        Map<String, Object> commData = new HashMap<>();
//
//        postData.put("template_id", msgTemplateValue);
//
//        //first
//        Map<String, Object> jsonObject1 = new HashMap<>();
//        jsonObject1.put("value", "您好,您有新的请修信息");
//        commData.put("first", jsonObject1);
//
//        //keyword1
//        Map<String, Object> jsonObject2 = new HashMap<>();
//        jsonObject2.put("value", sitePo.getSiteName());
//        commData.put("keyword1", jsonObject2);

        //keyword2
//        Map<String, Object> jsonObject3 = new HashMap<>();
//        jsonObject3.put("value", evseStr);
//        commData.put("keyword2", jsonObject3);
//
//        //keyword3
//        Map<String, Object> jsonObject4 = new HashMap<>();
//        jsonObject4.put("value", param.getFaultDesc());
//        commData.put("keyword3", jsonObject4);
//
//        //keyword4
//        Map<String, Object> jsonObject5 = new HashMap<>();
//        jsonObject5.put("value", param.getCreateOpName());
//        commData.put("keyword4", jsonObject5);
//
//        //keyword5
//        Map<String, Object> jsonObject6 = new HashMap<>();
//        jsonObject6.put("value", param.getMaintName());
//        commData.put("keyword5", jsonObject6);
//
//        //remark
//        Map<String, Object> jsonObject7 = new HashMap<>();
//        jsonObject7.put("value", "请尽快维修并反馈");
//        commData.put("remark", jsonObject7);
//
//        postData.put("data", commData);

//        //miniprogram
//        if (StringUtils.isNotEmpty(wxLiteMgmAppId) && StringUtils.isNotEmpty(
//            param.getYwOrderNo())) {
//            Map<String, Object> jsonObject8 = new HashMap<>();
//            jsonObject8.put("appid", wxLiteMgmAppId);
//            jsonObject8.put("pagepath", OPERATION_PAGE.concat(param.getYwOrderNo()));
//            postData.put("miniprogram", jsonObject8);
//        }

        //获取要发送的openId
//        SysUserOpenIdParam sysUserOpenIdParam = new SysUserOpenIdParam();
//        sysUserOpenIdParam.setUserIdList(Arrays.asList(param.getMaintUid()));
//        //剔除创建人
//        sysUserOpenIdParam.setOpUid(param.getCreateOpUid());
//        ListResponse<String> listResponse = authCenterFeignClient.getUserByIdList(
//            sysUserOpenIdParam);
//        if (listResponse != null && CollectionUtils.isNotEmpty(listResponse.getData())) {
//            listResponse.getData().forEach(e -> {
//                postData.put("touser", e);
//                try {
//                    this.sendPost(url, JsonUtils.toJsonString(postData));
//                } catch (IOException ex) {
//                    log.info("发送失败,message={}", ex.getMessage(), ex);
//                    ex.printStackTrace();
//                }
//            });
//        }
//        return BaseResponse.success();
    }

    /**
     * 获取access_token
     *
     * @return
     */
    public String getAccessToken(MsgSendParam param) {

        if (StringUtils.isNotBlank(redisUtil.get(wxChatToken + ":" + param.getTopCommId()))) {
            return redisUtil.get(wxChatToken + ":" + param.getTopCommId());
        }

        String getTokenUrl =
            "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid="
                + param.getWechatAppid() + "&secret=" + param.getWechatAppSecret();
        JsonNode result = sendGet(getTokenUrl);
        if (result != null) {
            String accessToken = result.get("access_token").asText();
            redisUtil.set(wxChatToken + ":" + param.getTopCommId(), accessToken, 2);
            return accessToken;
        }
        return "";
    }

    /**
     * 发送get请求
     *
     * @param url
     * @return
     */
    public JsonNode sendGet(String url) {
        try {
            URL requestUrl = new URL(url);
            HttpURLConnection connection = (HttpURLConnection) requestUrl.openConnection();
            connection.setRequestMethod("GET");
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.connect();
            InputStream inputStream = connection.getInputStream();
            int size = inputStream.available();
            byte[] bs = new byte[size];
            inputStream.read(bs);
            String message = new String(bs, "UTF-8");
            JsonNode jsonObject = JsonUtils.fromJson(message);
            return jsonObject;
        } catch (Exception e) {
            log.info("请求失败", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 发送post请求
     *
     * @param url
     * @param jsonBody
     * @throws IOException
     */
    public void sendPost(String url, String jsonBody) throws IOException {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        httpPost.addHeader("Content-Type", "application/json");
        //汉字乱码
        StringEntity s = new StringEntity(jsonBody, "utf-8");
        s.setContentEncoding("UTF-8");
        s.setContentType("application/json");
        httpPost.setEntity(s);

        CloseableHttpResponse response = httpClient.execute(httpPost);
        HttpEntity entity = response.getEntity();
        String responseContent = EntityUtils.toString(entity, "UTF-8");
        response.close();
        httpClient.close();

        JsonNode jsonObject = JsonUtils.fromJson(responseContent);
        if (jsonObject.get("errcode").asInt() != 0) {
            log.warn("微信模板消息发送失败,request={},return={}", jsonBody, jsonObject);
        }
    }

    public BaseResponse sendEvseCfgAlarm(String evseNo, String customWarningDesc) {

        BsBoxPo boxPo = bsBoxRoDs.getBsBox(evseNo);
        IotAssert.isNotNull(boxPo, "未找到桩信息");
        WarningPo warningPo = warningRoDs.getWarnInfoByName("桩配置异常");
        IotAssert.isNotNull(warningPo, "未找到告警配置信息");

        MsgSendParam param = new MsgSendParam();
        param.setIsEvseCfgAlarmMsg(Boolean.TRUE)
            .setSiteId(boxPo.getSiteId())
            .setTopCommId(DcBizConstants.superTopCommId)
            .setEndTime(null) // 固定为null
            .setBoxOutFactoryCode(evseNo)
            .setWarningId(GenerateIdByRedisAtomicUtils.generateLongKey())
            .setWarningCode(warningPo.getWarningCode())
            .setWarningName(warningPo.getWarningName())
            .setDeviceId(evseNo)
            .setEvseName(boxPo.getEvseName())
            .setStartTime(new Date())
            .setCustomWarningDesc(customWarningDesc)
            .setRemark(warningPo.getWarningDesc());
        return this.sendMsg(param);
    }

    public BaseResponse sendParkingLockError(ParkingLockErrorParam errorParam) {
        if (StringUtils.isBlank(errorParam.getEvseNo()) ||
            null == errorParam.getPlugId()) {
            throw new DcArgumentException("桩枪信息不正确");
        }

        PlugVo plug = redisIotReadService.getPlugRedisCache(errorParam.getEvseNo(),
            errorParam.getPlugId());
        IotAssert.isNotNull(plug, "未找到桩信息");
        WarningPo warningPo = warningRoDs.getWarnInfoByName("地锁故障");
        IotAssert.isNotNull(warningPo, "未找到告警配置信息");

        MsgSendParam param = new MsgSendParam();
        param.setIsEvseCfgAlarmMsg(Boolean.TRUE)
            .setSiteId(plug.getSiteId())
            .setTopCommId(DcBizConstants.superTopCommId)
            .setEndTime(null) // 固定为null
            .setBoxOutFactoryCode(errorParam.getEvseNo())
            .setWarningId(GenerateIdByRedisAtomicUtils.generateLongKey())
            .setWarningCode(warningPo.getWarningCode())
            .setWarningName(warningPo.getWarningName())
            .setDeviceId(errorParam.getSerialNumber())
            .setEvseName(plug.getEvseName())
            .setPlugName(plug.getName())
            .setStartTime(new Date())
            .setCustomWarningDesc(errorParam.getErrorMsg());
        return this.sendMsg(param);
    }
}
