package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ds.trading.ro.contract.ds.ContractRoDs;

import com.cdz360.biz.ds.trading.ro.file.ds.OssFileRoDs;
import com.cdz360.biz.ds.trading.rw.file.ds.OssFileRwDs;
import com.cdz360.biz.model.trading.file.po.OssFilePo;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class FileService {

    @Autowired
    private OssFileRwDs ossFileRwDs;

    @Autowired
    private OssFileRoDs ossFileRoDs;

    public OssFilePo addFile(OssFilePo ossFilePo){
        IotAssert.isNotNull(ossFilePo.getRealName(),"原文件名称不能为空");
        IotAssert.isNotNull(ossFilePo.getFileName(),"文件名称不能为空");
        IotAssert.isNotNull(ossFilePo.getType(),"文件类型不能为空");
        IotAssert.isNotNull(ossFilePo.getPath(),"文件存储路径不能为空");
        ossFileRwDs.addFile(ossFilePo);
        return ossFilePo;
    }

    public List<OssFilePo> getFileList(Long contractId) {
        return ossFileRoDs.getFileList(contractId);
    }

    public int updateContractFileEnable(Long contractId){
        return ossFileRwDs.updateContractFileEnable(contractId);
    }

    public int updateContractFile(Long contractId,List<Long> fileIdList){
        return ossFileRwDs.updateContractFile(contractId,fileIdList);
    }
}
