package com.cdz360.biz.dc.service.invoice;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.dc.client.UserFeignClient;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderRwDs;
import com.cdz360.biz.model.cus.settlement.param.ListSettlementParam;
import com.cdz360.biz.model.cus.settlement.param.UpdateSettlementInvoicedAmountParam;
import com.cdz360.biz.model.cus.settlement.type.SettlementStatusEnum;
import com.cdz360.biz.model.cus.settlement.vo.SettlementBi;
import com.cdz360.biz.model.invoice.type.ProductType;
import com.cdz360.biz.model.trading.invoice.dto.CorpInvoiceRecordDto;
import com.cdz360.biz.model.trading.invoice.vo.CorpInvoiceRecordVo;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceRecordUpdateParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedTemplateSalDetailVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderBiVo;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class PostSettlementUpdateCorpInvoiceRecordStrategyImpl
    extends AbstractCorpInvoiceRecordStrategy {

    @Autowired
    private UpdateCorpInvoiceRecordService updateCorpInvoiceRecordService;

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private ChargerOrderRwDs chargerOrderRwDs;

    @PostConstruct
    public void init() {
        updateCorpInvoiceRecordService.addStrategy(InvoicingMode.POST_SETTLEMENT, this);
    }

    @Override
    public CorpInvoiceRecordDto preUpdate(CorpInvoiceRecordUpdateParam param, boolean append) {
        // 初始化对象值
        CorpInvoiceRecordDto updateDto = super.preUpdate(param, append);

        // 统计账单金额
        ListSettlementParam settlementParam;
        if (!Boolean.TRUE.equals(param.getOpAll())) {
            settlementParam = new ListSettlementParam();
            settlementParam.setCommIdChain(param.getCommIdChain())
                .setCorpId(param.getCorpId())
                .setBillNoList(param.getOrderNoList());
        } else {
            updateDto.setFixElecFee(BigDecimal.ZERO)
                .setFixServFee(BigDecimal.ZERO)
                .setFixTotalFee(BigDecimal.ZERO)
                .setTotalFee(BigDecimal.ZERO)
                .setActualElecFee(BigDecimal.ZERO)
                .setActualServFee(BigDecimal.ZERO);
            settlementParam = param.getSettlementParam();
        }
        settlementParam.setInCorpInvoice(true);
        settlementParam.setStatusList(List.of(SettlementStatusEnum.PAID));

        // 电费服务费
        BigDecimal elecFee = BigDecimal.ZERO;
        BigDecimal servFee = BigDecimal.ZERO;
        if (append || !Boolean.TRUE.equals(param.getOpAll())) {
            if (param.fromBillingProcess()
                && CollectionUtils.isEmpty(param.getOrderNoList())) {
                // 请求来自OA流程且订单号列表为空时，不用获取电费服务费
            } else {
                if (!append) {
                    settlementParam.setApplyNo(param.getApplyNo());
                }
                ObjectResponse<OrderBiVo> orderBiVo = userFeignClient.settlementBiForCorp(
                    settlementParam);
                FeignResponseValidate.check(orderBiVo);

                elecFee = orderBiVo.getData().getTotalElecAmount();
                servFee = orderBiVo.getData().getTotalServAmount();
            }

            // 企业客户商品行信息
            List<InvoicedTemplateSalDetailVo> detailVoList = param.getCorpInvoiceInfoVo()
                .getTempRefVo().getDetailVoList();
            if (CollectionUtils.isEmpty(detailVoList)) {
                throw new DcArgumentException("商品行还没有配置");
            }

            Optional<InvoicedTemplateSalDetailVo> elec = detailVoList.stream()
                .filter(item -> item.getProductType() == ProductType.ELEC_ACTUAL_FEE)
                .findFirst();
            Optional<InvoicedTemplateSalDetailVo> serv = detailVoList.stream()
                .filter(item -> item.getProductType() == ProductType.SERV_ACTUAL_FEE)
                .findFirst();
            if (elec.isPresent() && serv.isPresent()) {
                // nothing to do
            } else if (serv.isPresent()) { // 设置服务费，没有电费
                servFee = elecFee.add(servFee);
                elecFee = BigDecimal.ZERO;
            } else {
                elecFee = elecFee.add(servFee);
                servFee = BigDecimal.ZERO;
            }
        }

        this.updateFee(append, updateDto, servFee, elecFee);
        return updateDto;
    }

    // 注: 账单存在两个库，关联信息只有invoicedAmount字段，所以特殊处理
    @Override
    public CorpInvoiceRecordVo postUpdate(
        CorpInvoiceRecordUpdateParam param, CorpInvoiceRecordDto dto, boolean append) {
        // 所有账单涉及的充电也需要变更开票金额???
//        @NotEmpty List<String> billNoList = param.getOrderNoList();

//        // 充电订单处理
//        int i = chargerOrderRwDs.updateOrderInvoicedAmountByBillNo(append, billNoList);

        if (CollectionUtils.isNotEmpty(param.getOrderNoList())) {
            // 账单开票金额: t_settlement.已开票金额
            UpdateSettlementInvoicedAmountParam updateParam = new UpdateSettlementInvoicedAmountParam();
            updateParam
                .setApplyNo(dto.getApplyNo())
                .setProcInstId(param.getProcInstId())
                .setCommIdChain(param.getCommIdChain())
                .setBillNoList(param.getOrderNoList());
            if (append) {
                updateParam.setOpType(UpdateSettlementInvoicedAmountParam.OpType.FIXED)
                    .setCorpId(param.getCorpId());
            } else {
                updateParam.setOpType(UpdateSettlementInvoicedAmountParam.OpType.ROLL_BACK);
            }
            BaseResponse res = userFeignClient.updateSettlementInvoicedAmount(updateParam);
            FeignResponseValidate.check(res);
        }

        return super.postUpdate(param, dto, append);
    }

    /**
     * 过滤无效的orderNo，并返回对应的可开票金额
     *
     * @return String：orderNo； BigDecimal：可开票金额
     */
    @Override
    public Optional<Map<String, BigDecimal>> orderNoList(CorpInvoiceRecordUpdateParam param,
        long start, int size) {
        param.getSettlementParam()
            .setStart(start)
            .setSize(size)
            .setTotal(false);
        param.getSettlementParam().setInCorpInvoice(true);
        param.getSettlementParam().setStatusList(List.of(SettlementStatusEnum.PAID));
        ListResponse<SettlementBi> res = userFeignClient.settlementBi(param.getSettlementParam());
        FeignResponseValidate.check(res);
        if (CollectionUtils.isEmpty(res.getData())) {
            return Optional.empty();
        }
        return Optional.of(res.getData().stream().filter(Objects::nonNull).collect(
            Collectors.toMap(SettlementBi::getBillNo, SettlementBi::getSettlementTotalFee)));
    }

    @Override
    public Optional<Map<String, BigDecimal>> orderNoList(List<String> orderNoList) {
        ListSettlementParam param = new ListSettlementParam();
        param.setBillNoList(orderNoList)
            .setStart(0L)
            .setSize(orderNoList.size());
        ListResponse<SettlementBi> res = userFeignClient.settlementBi(param);
        FeignResponseValidate.check(res);
        if (CollectionUtils.isEmpty(res.getData())) {
            return Optional.empty();
        }
        return Optional.of(res.getData().stream().filter(Objects::nonNull).collect(
            Collectors.toMap(SettlementBi::getBillNo, SettlementBi::getSettlementTotalFee)));
    }

    @Override
    public void updateException(CorpInvoiceRecordUpdateParam param, @NonNull String applyNo,
        boolean append) {
        if (param.fromBillingProcess()
            && CollectionUtils.isEmpty(param.getOrderNoList())) {
            // 请求来自OA流程且订单号列表为空时，无需回滚账单数据
            return;
        }
        // 需要将账单上的数据回滚
        // t_settlement.已开票金额
        UpdateSettlementInvoicedAmountParam updateParam = new UpdateSettlementInvoicedAmountParam();
        updateParam
            .setApplyNo(applyNo)
            .setCommIdChain(param.getCommIdChain())
            .setBillNoList(param.getOrderNoList());
        if (append) {
            updateParam.setOpType(UpdateSettlementInvoicedAmountParam.OpType.ROLL_BACK)
                .setCorpId(param.getCorpId());
        } else {
            updateParam.setOpType(UpdateSettlementInvoicedAmountParam.OpType.FIXED);
        }

        try {
            BaseResponse res = userFeignClient.updateSettlementInvoicedAmount(updateParam);
            FeignResponseValidate.check(res);
        } catch (Exception e) {
            log.error("这里需要人工介入处理数据: err = {}, param = {}",
                e.getMessage(), JsonUtils.toJsonString(updateParam), e);
        }
    }
}
