package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.ChargeOrderStatus;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderPayRwDs;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderRwDs;
import com.cdz360.biz.model.cus.settlement.dto.SettlementDto;
import com.cdz360.biz.model.order.param.ListSettlementOrderParam;
import com.cdz360.biz.model.order.param.NotInSettlementOrderParam;
import com.cdz360.biz.model.trading.order.dto.SettlementOrderBiDto;
import com.cdz360.biz.model.trading.order.param.ClearBillNoParam;
import com.cdz360.biz.model.trading.order.vo.SettlementOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrder;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class CorpSettlementService {

    @Autowired
    private ChargerOrderRwDs chargerOrderRwDs;
    @Autowired
    private ChargerOrderPayRwDs chargerOrderPayRwDs;

    public Mono<Integer> notInSettlementOrders(NotInSettlementOrderParam param) {
        IotAssert.isNotNull(param.getCorpId(), "企业客户ID不能为空");
        IotAssert.isNotNull(param.getSettStartDateDay(), "账期结束年月不能为空");
        IotAssert.isNotNull(param.getSettEndDateDay(), "账期结束年月不能为空");
        return Mono.just(param)
            .map(x -> chargerOrderRwDs.notInSettlementOrders(x))
            .map(List::size);
    }

    public ListResponse<SettlementOrderVo> getNotSettlementOrderList(
        ListSettlementOrderParam param) {
        if (null == param.getCorpId()) {
            throw new DcArgumentException("请提供企业ID值");
        }

        // 分页信息
        Page<ChargerOrder> page = null;
        if (param.getTotal() != null && param.getTotal()) {
            page = PageHelper.offsetPage(0, 10);
            if (null != param.getStart()
                && null != param.getSize() && param.getSize() > 0) {
                page = PageHelper.offsetPage(param.getStart().intValue(), param.getSize());
            }
            log.info("分页: page = {}, size = {}", page.getPageNum(), page.getPageSize());
        }

        List<ChargerOrder> orderList = chargerOrderRwDs.getNotSettlementOrderList(param);
        if (CollectionUtils.isEmpty(orderList)) {
            return RestUtils.buildListResponse(new ArrayList<>(), 0);
        }

        List<SettlementOrderVo> resultList = this.map2SettlementOrderVo(orderList);

        // 分时信息
        List<String> orderNoList = resultList.stream()
            .map(SettlementOrderVo::getOrderNo).collect(Collectors.toList());
        Map<String, SettlementOrderVo> timeBi = chargerOrderRwDs.timeDivisionBi(orderNoList)
            .stream().collect(Collectors.toMap(SettlementOrderVo::getOrderNo, o -> o));

        resultList.forEach(i -> {
            SettlementOrderVo vo = timeBi.get(i.getOrderNo());
            if (null != vo) {
                i.setKwhJian(vo.getKwhJian())
                    .setKwhFeng(vo.getKwhFeng())
                    .setKwhPing(vo.getKwhPing())
                    .setKwhGu(vo.getKwhGu())
                    .setKwhOther(vo.getKwhOther());
            }
        });

        return RestUtils.buildListResponse(resultList, page == null ? 0 : (int) page.getTotal());
    }

    private List<SettlementOrderVo> map2SettlementOrderVo(List<ChargerOrder> orderList) {
        return orderList.stream().map(this::map2SettlementOrderVo).collect(Collectors.toList());
    }

    private SettlementOrderVo map2SettlementOrderVo(ChargerOrder order) {
        SettlementOrderVo vo = new SettlementOrderVo();
        vo.setOrderNo(order.getOrderNo());
        vo.setStatus(order.getStatus());
        vo.setOrderStatus(ChargeOrderStatus.valueOf(order.getOrderStatus().name()));
        vo.setCusName(order.getCustomerName());
        vo.setCusPhone(order.getMobilePhone());
        vo.setOrderType(OrderStartType.valueOf(order.getOrderType()));
        vo.setSiteId(order.getStationId());
        vo.setSiteName(order.getStationName());
        vo.setOrderElec(order.getOrderElectricity());
        vo.setOrderPrice(order.getOrderPrice());
        vo.setServPrice(order.getServicePrice());
        vo.setElecPrice(order.getElecPrice());
        vo.setCreateTime(order.getCreateTime());
        vo.setStopTime(order.getStopTime());
        vo.setChargeStartTime(order.getChargeStartTime());
        vo.setChargeEndTime(order.getChargeEndTime());
        vo.setBillNo(order.getBillNo());
        // 分时段信息需额外处理
        return vo;
    }

    public Integer clearBillNo(ClearBillNoParam param) {
        if (StringUtils.isBlank(param.getBillNo())) {
            throw new DcArgumentException("账单编号不能为空");
        }

        //chargerOrderRwDs.clearBillNo(param);
        return chargerOrderPayRwDs.clearBillNo(param);
    }

    public ListResponse<SettlementOrderVo> getSettlementOrderList(ListSettlementOrderParam param) {
        if (CollectionUtils.isEmpty(param.getBillNoList())) {
            throw new DcArgumentException("账单编号不能为空");
        }

        // 分页信息
//        Page<ChargerOrder> page = null;
//        if (param.getTotal() != null && param.getTotal()) {
//            page = PageHelper.offsetPage(0, 10);
//            if (null != param.getStart()
//                && null != param.getSize() && param.getSize() > 0) {
//                page = PageHelper.offsetPage(param.getStart().intValue(), param.getSize());
//            }
//            log.info("分页: page = {}, size = {}", page.getPageNum(), page.getPageSize());
//        }

        List<ChargerOrder> orderList = chargerOrderRwDs.getSettlementOrderList(param);

        List<SettlementOrderVo> resultList = this.map2SettlementOrderVo(orderList);

        // 分时信息
        List<String> orderNoList = resultList.stream()
            .map(SettlementOrderVo::getOrderNo).collect(Collectors.toList());
        Map<String, SettlementOrderVo> timeBi = chargerOrderRwDs.timeDivisionBi(orderNoList)
            .stream().collect(Collectors.toMap(SettlementOrderVo::getOrderNo, o -> o));

        resultList.forEach(i -> {
            SettlementOrderVo vo = timeBi.get(i.getOrderNo());
            if (null != vo) {
                i.setKwhJian(vo.getKwhJian())
                    .setKwhFeng(vo.getKwhFeng())
                    .setKwhPing(vo.getKwhPing())
                    .setKwhGu(vo.getKwhGu())
                    .setKwhOther(vo.getKwhOther());
            }
        });

        return RestUtils.buildListResponse(
            resultList, Boolean.TRUE.equals(param.getTotal()) ?
                chargerOrderRwDs.countSettlementOrder(param) : 0);
    }

    public SettlementDto settlementOrderBi(ListSettlementOrderParam param) {
        if (param.getCorpId() == null) {
            throw new DcArgumentException("企业ID需要提供");
        }

        // FIXME: 判断是否存在非 2000 的充电订单

        // 标记账单
        if (StringUtils.isNotBlank(param.getMarkBillNo())) {
            // 全选条件: 时间范围; 订单号; 场站ID; 场站名称; 充电订单状态
            // 非全选条件: orderNoList 非空
            if (CollectionUtils.isEmpty(param.getBillNoList())) {
                param.setBillNoList(List.of(param.getMarkBillNo()));
            } else {
                param.getBillNoList().add(param.getMarkBillNo());
            }
            //int i = chargerOrderRwDs.markBillNo(param);
            //log.info("帐单号标记: i = {}", i);

            int i = chargerOrderPayRwDs.markBillNo(param);
            log.info("帐单号标记: i = {}", i);
        }

        SettlementOrderBiDto bi = chargerOrderRwDs.settlementOrderBi(param);
        SettlementDto dto = new SettlementDto();
        BeanUtils.copyProperties(bi, dto);

        dto.setSiteNameList(StringUtils.isNotBlank(bi.getSiteNameList()) ?
            List.of(bi.getSiteNameList().split(",")) : new ArrayList<>());
        dto.setSiteNoList(StringUtils.isNotBlank(bi.getSiteNoList()) ?
            List.of(bi.getSiteNoList().split(",")) : new ArrayList<>());

        return dto;
    }

    /**
     * 账单结算操作
     *
     * @param billNo
     * @return
     */
    public Integer settlementByBillNo(String billNo) {
        //Integer i = chargerOrderRwDs.updateCorpPayStatus(billNo, 1, 2);
        return chargerOrderPayRwDs.updateCorpPayStatus(billNo, 1, 2);
    }

//    public Integer updateSettlementOrderBillNo(UpdateSettlementOrderParam param) {
//        return chargerOrderRwDs.updateSettlementOrderBillNo(param);
//    }
}
