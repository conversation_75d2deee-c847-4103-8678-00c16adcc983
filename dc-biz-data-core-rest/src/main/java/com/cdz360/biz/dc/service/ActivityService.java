package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ds.trading.ro.coupon.ds.ActivityCouponRoDs;
import com.cdz360.biz.ds.trading.ro.coupon.ds.ActivityCouponRuleRoDs;
import com.cdz360.biz.ds.trading.ro.coupon.ds.ActivityDiscountRoDs;
import com.cdz360.biz.ds.trading.ro.coupon.ds.ActivityRoDs;
import com.cdz360.biz.ds.trading.ro.coupon.ds.CouponDictRoDs;
import com.cdz360.biz.ds.trading.ro.coupon.ds.CouponRoDs;
import com.cdz360.biz.ds.trading.ro.user.ds.TRUserRoDs;
import com.cdz360.biz.ds.trading.rw.coupon.ds.ActivityCouponRuleRwDs;
import com.cdz360.biz.ds.trading.rw.coupon.ds.ActivityCouponRwDs;
import com.cdz360.biz.ds.trading.rw.coupon.ds.ActivityDiscountRwDs;
import com.cdz360.biz.ds.trading.rw.coupon.ds.ActivityImageRwDs;
import com.cdz360.biz.ds.trading.rw.coupon.ds.ActivityRwDs;
import com.cdz360.biz.ds.trading.rw.coupon.ds.CouponRwDs;
import com.cdz360.biz.ds.trading.rw.coupon.ds.CouponScoreSettingRwDs;
import com.cdz360.biz.model.common.constant.Constant;
import com.cdz360.biz.model.cus.user.dto.CusSampleDto;
import com.cdz360.biz.model.cus.user.param.AddUserParam;
import com.cdz360.biz.model.trading.coupon.dto.ActivityTempDto;
import com.cdz360.biz.model.trading.coupon.param.ActivityCouponRuleParam;
import com.cdz360.biz.model.trading.coupon.param.ActivityUserCouponParam;
import com.cdz360.biz.model.trading.coupon.param.CouponSearchParam;
import com.cdz360.biz.model.trading.coupon.param.CreateActivityParam;
import com.cdz360.biz.model.trading.coupon.param.ListActivityParam;
import com.cdz360.biz.model.trading.coupon.param.NewGuestAcquireParam;
import com.cdz360.biz.model.trading.coupon.param.UpdateActivityParam;
import com.cdz360.biz.model.trading.coupon.po.ActivityCouponPo;
import com.cdz360.biz.model.trading.coupon.po.ActivityCouponRulePo;
import com.cdz360.biz.model.trading.coupon.po.ActivityImagePo;
import com.cdz360.biz.model.trading.coupon.po.ActivityPo;
import com.cdz360.biz.model.trading.coupon.po.ChargePo;
import com.cdz360.biz.model.trading.coupon.po.CouponDictPo;
import com.cdz360.biz.model.trading.coupon.po.CouponPo;
import com.cdz360.biz.model.trading.coupon.po.CouponScoreSettingPo;
import com.cdz360.biz.model.trading.coupon.type.AcquireCouponResult;
import com.cdz360.biz.model.trading.coupon.type.ActivityStatusType;
import com.cdz360.biz.model.trading.coupon.type.ActivityType;
import com.cdz360.biz.model.trading.coupon.type.CouponDictStatusType;
import com.cdz360.biz.model.trading.coupon.type.CouponSendType;
import com.cdz360.biz.model.trading.coupon.type.CouponStatusType;
import com.cdz360.biz.model.trading.coupon.type.CouponValidType;
import com.cdz360.biz.model.trading.coupon.vo.ActivityVo;
import com.cdz360.biz.model.trading.coupon.vo.CouponBi;
import com.cdz360.biz.model.trading.coupon.vo.CouponVo;
import com.cdz360.biz.model.trading.coupon.vo.DiscountVo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.chargerlinkcar.framework.common.domain.vo.UserPropVO;
import com.chargerlinkcar.framework.common.feign.UserFeignClient;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.DateUtils;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.RegularExpressionUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class ActivityService {

    @Autowired
    private ActivityRoDs activityRoDs;
    @Autowired
    private ActivityCouponRuleRoDs activityCouponRuleRoDs;
    @Autowired
    private ActivityRwDs activityRwDs;
    @Autowired
    private ActivityImageRwDs activityImageRwDs;
    @Autowired
    private ActivityCouponRuleRwDs activityCouponRuleRwDs;
    @Autowired
    private CouponRoDs couponRoDs;
    @Autowired
    private CouponRwDs couponRwDs;
    @Autowired
    private TRUserRoDs trUserRoDs;
    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private CouponDictRoDs couponDictRoDs;
    @Autowired
    private ActivityCouponRwDs activityCouponRwDs;
    @Autowired
    private ActivityCouponRoDs activityCouponRoDs;
    @Autowired
    private ActivityDiscountRwDs activityDiscountRwDs;
    @Autowired
    private ActivityDiscountRoDs activityDiscountRoDs;

    @Value("${activity.urlPattern:#:id}")
    private String activityUrlPattern;

    @Value("${activity.repalce::id}")
    private String activityUrlPatternReplace;

    /**
     * 新客注册时间限制条件，单位小时
     */
    @Value("${activity.newGuest.regTimeLimit:72}")
    private Long newGuestRegTimeLimit;
    @Autowired
    private CouponScoreSettingRwDs couponScoreSettingRwDs;

    public void activeActivity(Long id) {
        IotAssert.isNotNull(id, "请传入活动id");

        ActivityVo activityVo = activityRoDs.getActivityVo(id);
        IotAssert.isNotNull(activityVo, "活动不存在: " + id);

        // 仅在活动类型是免费领券的情况下判断
        if (ActivityType.getFreeActivities().contains(activityVo.getType())) {
            activityVo.getDictList().stream().forEach(e ->
                IotAssert
                    .isTrue(CouponDictStatusType.ENABLE.equals(e.getStatus()),
                        "启动失败：券模板不在启用状态 ")
            );
            IotAssert.isTrue(
                activityVo.getQuantityAccount() > couponRoDs
                    .getAlreadyAcquireNum(activityVo.getId()),
                "启动失败：领券人次已满");
        }

//        activityVo.getDictList().stream().forEach(e ->
//            IotAssert.isTrue(CouponDictStatusType.ENABLE.equals(e.getStatus()), "启动失败：券模板不在启用状态 ")
//        );

        IotAssert
            .isTrue(DateUtil.getNextDate(new Date()).getTime() <= activityVo.getTimeTo().getTime(),
                "启动失败：活动时间不正确");

//        IotAssert.isTrue(
//            activityVo.getQuantityAccount() > couponRoDs.getAlreadyAcquireNum(activityVo.getId()),
//            "启动失败：领券人次已满");

        Date now = new Date();
        if (activityVo.getTimeFrom().getTime() <= now.getTime() && now.getTime() < activityVo
            .getTimeTo().getTime()) {
            log.info("当前时间处于活动开始结束时间范围内，活动状态设置为进行中");
            activityVo.setStatus(ActivityStatusType.PROCESSING);
        } else {
            activityVo.setStatus(ActivityStatusType.PUBLISHED);
        }

        IotAssert.isTrue(activityRwDs.updateActivity(activityVo), "修改活动失败");
    }

    public void abortActivity(Long id) {
        IotAssert.isNotNull(id, "请传入活动id");

        ActivityPo activityPo = activityRoDs.getById(id);
        IotAssert.isNotNull(activityPo, "活动不存在: " + id);

        IotAssert.isTrue(!List.of(ActivityStatusType.ABORT, ActivityStatusType.FINISHED)
                .contains(activityPo.getStatus()),
            "活动当前状态为: " + activityPo.getStatus().getDesc() + ", 不允许修改");

        activityPo.setStatus(ActivityStatusType.ABORT);

        IotAssert.isTrue(activityRwDs.updateActivity(activityPo), "修改活动失败");
    }

    public void showInMobile(Long id) {
        setActivityShowInMobile(id, true);
    }

    public void hideInMobile(Long id) {
        setActivityShowInMobile(id, false);
    }

    private void setActivityShowInMobile(Long id, Boolean b) {
        IotAssert.isNotNull(id, "请传入活动id");

        ActivityPo activityPo = activityRoDs.getById(id);
        IotAssert.isNotNull(activityPo, "活动不存在: " + id);

        IotAssert.isTrue(!List.of(ActivityStatusType.ABORT, ActivityStatusType.FINISHED)
                .contains(activityPo.getStatus()),
            "活动当前状态为: " + activityPo.getStatus().getDesc() + ", 不允许修改");

        IotAssert.isTrue(ActivityType.FREE_ACQUIRE.equals(activityPo.getType()),
            "仅允许修改类型为免费领券的活动");

        activityPo.setShowInMobile(b);

        IotAssert.isTrue(activityRwDs.updateActivity(activityPo), "更新活动失败");
    }

    @Transactional
    public void updateActivity(UpdateActivityParam req) {
        IotAssert.isNotNull(req.getId(), "请填入活动id");

        ActivityVo activityVo = activityRoDs.getActivityVo(req.getId());
        IotAssert.isNotNull(activityVo, "活动不存在: " + req.getId());

        IotAssert.isTrue(List.of(ActivityStatusType.ABORT, ActivityStatusType.FINISHED)
                .contains(activityVo.getStatus()),
            "活动当前状态为: " + activityVo.getStatus().getDesc() + ", 不允许修改");

        IotAssert.isNotBlank(req.getName(), "请填入活动名称");

        // 通过活动名称和状态列表（非已结束）来判断是否存在同名活动
        List<ActivityStatusType> statusTypeList = new ArrayList<>();
        statusTypeList.add(ActivityStatusType.ABORT);
        statusTypeList.add(ActivityStatusType.PROCESSING);
        statusTypeList.add(ActivityStatusType.PUBLISHED);
        List<ActivityPo> byName = activityRoDs.getByNameAndStatusList(req.getName(),
            statusTypeList);
        if (CollectionUtils.isNotEmpty(byName)) {
            IotAssert.isTrue(byName.get(0).getId().equals(req.getId()), "已存在同名活动");
        }
//        IotAssert.isNotNull(req.getAcquireCount(), "请传入单次领券数");
//        IotAssert.isNotNull(req.getQuantityAccount(), "请传入领券人数");
//        IotAssert.isNotNull(req.getTimeFrom(), "请传入活动开始时间");
//        IotAssert.isNotNull(req.getTimeTo(), "请传入活动结束时间");
        // 校验发券规则
        if (ActivityType.FREE_ACQUIRE.equals(req.getType())) {
            // 免费领券
            IotAssert.isNotNull(req.getSendCouponRule(), "请传入发券规则");
        }
        if (ActivityType.NEW_GUEST_ACQUIRE.equals(req.getType())) {
            // 新人领券
            IotAssert.isTrue(CouponSendType.ONCE.equals(req.getSendCouponRule()),
                "新人领券只能传入\"整个时间范围内每用户限领1次\"");
        }
        if (ActivityType.PLATFORM_GIVING.equals(req.getType())) {
            // 后台发券
            IotAssert.isNull(req.getSendCouponRule(), "后台发券无需传入发券规则");
        }
        if (ActivityType.CHARGE_GIVING.equals(req.getType())) {
            // 充值赠送
            IotAssert.isNull(req.getSendCouponRule(), "充值赠送无需传入发券规则");
        }
        // 优惠券活动信息
        if (ActivityType.getFreeActivities().contains(activityVo.getType())) {
            // 除了充值赠送
//            IotAssert.isNotNull(req.getAcquireCount(), "请传入单次领券数");
//            IotAssert.isNotNull(req.getQuantityAccount(), "请传入领券人数");
            // 找到一个最小和最大的时间作为整个活动的开始结束时间
            Date minTime = null;
            Date maxTime = null;

            // 校验发券规则参数列表
            IotAssert.isTrue(CollectionUtils.isNotEmpty(req.getRuleParamList()),
                "请传入发券规则参数");
            Integer acquireCount = 0;
            Integer quantityAccount = 0;
            if (!ActivityType.PLATFORM_GIVING.equals(req.getType())) {
                // 不是后台发券才来校验预计发券数量
                // ruleParamsList里所有的预计发券数量加在一起，跟已领券数量对比
                int sumTotalAmount = req.getRuleParamList().stream()
                    .map(ActivityCouponRuleParam::getTotalAmount)
                    .reduce(0, Integer::sum);
                IotAssert.isTrue(sumTotalAmount >=
                        couponRoDs.getAlreadyAcquireCouponNum(req.getId()).intValue(),
                    "发券规则里的预计发券数量总和应大于当前已领券数量");

            }
            List<TimeFilter> timeList = new ArrayList<>();

            for (ActivityCouponRuleParam ruleParam : req.getRuleParamList()) {
                IotAssert.isNotNull(ruleParam.getTimeFrom(), "请传入活动开始时间");
                IotAssert.isNotNull(ruleParam.getTimeTo(), "请传入活动结束时间");

                // 修复开始时间为00s
                ruleParam.setTimeFrom(
                    DateUtil.adjustDateToSpecificSecond(ruleParam.getTimeFrom(), 0));
                // 修复结束时间为59s
                ruleParam.setTimeTo(DateUtil.adjustDateToSpecificSecond(ruleParam.getTimeTo(), 59));

                TimeFilter timeFilter = new TimeFilter();
                timeFilter.setStartTime(ruleParam.getTimeFrom());
                timeFilter.setEndTime(ruleParam.getTimeTo());
                timeList.add(timeFilter);

                IotAssert.isTrue(
                    ruleParam.getTimeFrom().getTime() <= ruleParam.getTimeTo().getTime(),
                    "活动时间结束时间必须在开始时间之后");
                if (ActivityType.PLATFORM_GIVING.equals(req.getType())) {
                    // 后台发券
                    IotAssert.isNotNull(ruleParam.getRepeatActive(),
                        "请选择同一手机号能否重复发券");
                    IotAssert.isNotNull(ruleParam.getCouponsPerTime(), "请选择每种券发送张数");
                    if (ruleParam.getMaxAmountPerTime() == null
                        || ruleParam.getMaxAmountPerTime() <= 0) {
                        throw new DcArgumentException("请传入最大发券次数");
                    }
                }
                if (ActivityType.FREE_ACQUIRE.equals(req.getType())
                    || ActivityType.NEW_GUEST_ACQUIRE.equals(req.getType())) {
                    // 免费领券或新人领券
                    if (ruleParam.getCouponsPerTime() == null
                        || ruleParam.getCouponsPerTime() <= 0) {
                        throw new DcArgumentException("请传入每次领取的券数");
                    }
                    if (ruleParam.getMaxAmountPerTime() == null
                        || ruleParam.getMaxAmountPerTime() <= 0) {
                        throw new DcArgumentException("请传入最大领取次数");
                    }
                }

                int alreadyAcquireNum = 0;
                if (CollectionUtils.isEmpty(activityVo.getRuleParamList())) {
                    // 旧版本,不包含规则参数列表，直接按userId统计
                    alreadyAcquireNum = couponRoDs.getAlreadyAcquireNum(req.getId()).intValue();
                } else {
                    // 新版本
                    if (ruleParam.getId() != null) {
                        // 有ruleId，这是之前的规则，没有ruleId是新加的规则
                        alreadyAcquireNum = couponRoDs.getAlreadyAcquireNumByRuleId(req.getId(),
                            ruleParam.getId()).intValue();
                    }
                }
                IotAssert.isTrue(ruleParam.getMaxAmountPerTime().intValue() >= alreadyAcquireNum,
                    "领券人数应大于当前领券人数");

                // 编辑后的新版本最大领取次数要减去直接已经领取的
                ruleParam.setMaxAmountPerTime(ruleParam.getMaxAmountPerTime() - alreadyAcquireNum);

                if (ruleParam.getCouponsPerTime() > acquireCount) {
                    acquireCount = ruleParam.getCouponsPerTime();
                }
                quantityAccount += ruleParam.getMaxAmountPerTime();

                if (minTime == null || ruleParam.getTimeFrom().getTime() <= minTime.getTime()) {
                    minTime = ruleParam.getTimeFrom();
                }
                if (maxTime == null || ruleParam.getTimeTo().getTime() >= maxTime.getTime()) {
                    maxTime = ruleParam.getTimeTo();
                }
            }

            if (DateUtil.overlap(timeList, true)) {
                throw new DcArgumentException("发券规则里的多段时间不能重合");
            }

            req.setTimeFrom(minTime);
            req.setTimeTo(maxTime);

            req.setAcquireCount(acquireCount);
            req.setQuantityAccount(quantityAccount);

            IotAssert.isTrue(CollectionUtils.isNotEmpty(req.getCouponDictIdList()), "请选择券模板");

            //券模版可编辑了，此段代码注释,去新增时校验
//            activityCouponRoDs.getListByActivityId(activityVo.getId()).stream().forEach(e -> {
//                CouponDictPo byId = couponDictRoDs.getById(e.getCouponDictId());
//                IotAssert.isNotNull(byId, "找不到券模板: " + e);
//                IotAssert.isTrue(CouponDictStatusType.ENABLE.equals(byId.getStatus()),
//                    "模板当前不可用: " + byId.getName());
//
//                if (CouponValidType.FIX.equals(byId.getValidType())) {
//                    log.info("判断活动时间与模板生效时间冲突");
//                    IotAssert
//                        .isTrue(/*byId.getValidTimeFrom().getTime() <= req.getTimeFrom().getTime() &&*/
//                            req.getTimeTo().getTime() <= byId.getValidTimeTo().getTime(),
//                            "活动时间与模板生效时间冲突");
//                }
//
//            });
        } else {
            // 充值赠送
            // 修复时间为00:00:00
            req.setTimeFrom(DateUtil.getThisDate(req.getTimeFrom()));
            req.setTimeTo(DateUtil.getNextDate(DateUtil.getThisDate(req.getTimeTo())));
            IotAssert.isTrue(req.getTimeFrom().getTime() <= req.getTimeTo().getTime(),
                "活动时间结束时间必须在开始时间之后");
            // 充赠活动设置领券字段为0
            req.setAcquireCount(0);
            req.setQuantityAccount(0);
        }

//        IotAssert.isTrue(req.getQuantityAccount().intValue() >=
//                couponRoDs.getAlreadyAcquireNum(req.getId()).intValue(),
//            "领券人数应大于当前领券人数");

//        // 修复时间为00:00:00
//        req.setTimeFrom(DateUtil.getThisDate(req.getTimeFrom()));
//        req.setTimeTo(DateUtil.getNextDate(DateUtil.getThisDate(req.getTimeTo())));
//        IotAssert.isTrue(req.getTimeFrom().getTime() <= req.getTimeTo().getTime(),
//            "活动时间结束时间必须在开始时间之后");

//        activityCouponRoDs.getListByActivityId(activity.getId()).stream().forEach(e -> {
//            CouponDictPo byId = couponDictRoDs.getById(e.getCouponDictId());
//            IotAssert.isNotNull(byId, "找不到券模板: " + e);
//            IotAssert.isTrue(CouponDictStatusType.ENABLE.equals(byId.getStatus()),
//                "模板当前不可用: " + byId.getName());
//
//            if (CouponValidType.FIX.equals(byId.getValidType())) {
//                log.info("判断活动时间与模板生效时间冲突");
//                IotAssert
//                    .isTrue(/*byId.getValidTimeFrom().getTime() <= req.getTimeFrom().getTime() &&*/
//                        req.getTimeTo().getTime() <= byId.getValidTimeTo().getTime(),
//                        "活动时间与模板生效时间冲突");
//            }
//
//        });

//        if (CollectionUtils.isNotEmpty(req.getImageUrlList())) {
////            IotAssert.isTrue(CollectionUtils.isNotEmpty(req.getImageUrlList()), "请传入活动图片");
//            log.info("删除原活动图: activityId: {}, {}", req.getId(),
//                activityImageRwDs.deleteActivityImage(req.getId()));
//            IotAssert.isTrue(activityImageRwDs.batchInsert(
//                req.getImageUrlList()
//                    .stream()
//                    .map(e -> new ActivityImagePo().setUrl(e).setActivityId(req.getId()))
//                    .collect(Collectors.toList())
//            ) == req.getImageUrlList().size(), "创建活动图片失败");
//        }

//        ActivityPo activityPo = new ActivityPo();
//        BeanUtils.copyProperties(req, activityPo);

        // 将旧活动设置为已结束
        activityVo.setId(activityVo.getId())
            .setStatus(ActivityStatusType.FINISHED);
        activityRwDs.updateActivity(activityVo);

        // 图片从之前的拿到
        req.setImageUrlList(activityVo.getImageList().stream()
            .map(ActivityImagePo::getUrl)
            .collect(Collectors.toList()));
        // 创建新活动
        this.createActivity(req);

//        if (req.getTimeTo() != null && req.getTimeFrom() != null) {
//            Date now = new Date();
//            IotAssert.isTrue(now.getTime() < req.getTimeTo().getTime(),
//                "活动时间结束时间必须在当前时间之后");
//            if (req.getTimeFrom().getTime() <= now.getTime() && now.getTime() < req.getTimeTo()
//                .getTime()) {
//                log.info("当前时间处于活动开始结束时间范围内，活动状态设置为进行中");
//                activityPo.setStatus(ActivityStatusType.PROCESSING);
//            } else {
//                activityPo.setStatus(ActivityStatusType.PUBLISHED);
//            }
//        }
//
//        IotAssert.isTrue(activityRwDs.updateActivity(activityPo), "修改活动失败");
//
//        // 充赠活动
//        if (activity.getType().equals(ActivityType.CHARGE_GIVING)) {
//            activityDiscountRwDs.batchEnable(activity.getId(), Boolean.FALSE);
//            List<ChargePo> discountList = activityDiscountRoDs.getById(activity.getId(), null);
//            // 存在的
//            List<Long> existsIdList = discountList.stream().filter(e -> {
//                List<ChargeVo> collect = req.getChargeList().stream().filter(
//                    i -> i.getChargeAmount().compareTo(e.getChargeAmount()) == 0
//                        && i.getDiscountAmount()
//                        .compareTo(e.getDiscountAmount()) == 0).collect(
//                    Collectors.toList());
//                return CollectionUtils.isNotEmpty(collect) ? Boolean.TRUE : Boolean.FALSE;
//            }).map(ChargePo::getId).collect(Collectors.toList());
//
//            // 不存在的
//            List<ChargeVo> unExistList = req.getChargeList().stream().filter(e -> {
//                List<ChargePo> collect = discountList.stream().filter(i ->
//                    i.getChargeAmount().compareTo(e.getChargeAmount()) == 0 && i.getDiscountAmount()
//                        .compareTo(e.getDiscountAmount()) == 0
//                ).collect(Collectors.toList());
//                return CollectionUtils.isNotEmpty(collect) ? Boolean.FALSE : Boolean.TRUE;
//            }).collect(Collectors.toList());
//
//            if (CollectionUtils.isNotEmpty(existsIdList)) {
//                IotAssert.isTrue(activityDiscountRwDs.batchEnableById(existsIdList), "修改活动内容失败");
//            }
//
//            if (CollectionUtils.isNotEmpty(unExistList)) {
//                IotAssert.isTrue(activityDiscountRwDs.batchInsert(activity.getId(), unExistList),
//                    "修改活动内容失败");
//            }
//        }
    }

    @Transactional
    public void createActivity(CreateActivityParam req) {
        IotAssert.isNotBlank(req.getName(), "请填入活动名称");
//        IotAssert.isTrue(CollectionUtils.isEmpty(activityRoDs.getByName(req.getName())), "已存在同名活动");
        // 通过活动名称和状态列表（非已结束）来判断是否存在同名活动
        List<ActivityStatusType> statusTypeList = new ArrayList<>();
        statusTypeList.add(ActivityStatusType.ABORT);
        statusTypeList.add(ActivityStatusType.PROCESSING);
        statusTypeList.add(ActivityStatusType.PUBLISHED);
        List<ActivityPo> byName = activityRoDs.getByNameAndStatusList(req.getName(),
            statusTypeList);
        IotAssert.isTrue(CollectionUtils.isEmpty(byName), "已存在同名活动");
        IotAssert.isNotNull(req.getCommId(), "请选择所属商户");
        IotAssert.isNotNull(req.getType(), "活动类型不能为空");
//        IotAssert.isTrue(CollectionUtils.isNotEmpty(req.getCouponDictIdList()), "请选择券模板");
//        IotAssert.isNotNull(req.getAcquireCount(), "请传入单次领券数");
//        IotAssert.isNotNull(req.getQuantityAccount(), "请传入领券人数");
//        IotAssert.isNotNull(req.getTimeFrom(), "请传入活动开始时间");
//        IotAssert.isNotNull(req.getTimeTo(), "请传入活动结束时间");
//        IotAssert.isTrue(CollectionUtils.isNotEmpty(req.getCouponDictIdList()), "请选择关联券模板");
//        IotAssert.isTrue(CollectionUtils.isNotEmpty(req.getImageUrlList()), "请传入活动图片");
        // 校验发券规则
        if (ActivityType.FREE_ACQUIRE.equals(req.getType())) {
            // 免费领券
            IotAssert.isNotNull(req.getSendCouponRule(), "请传入发券规则");
        }
        if (ActivityType.NEW_GUEST_ACQUIRE.equals(req.getType())) {
            // 新人领券
            IotAssert.isTrue(CouponSendType.ONCE.equals(req.getSendCouponRule()),
                "新人领券只能传入\"整个时间范围内每用户限领1次\"");
        }
        if (ActivityType.PLATFORM_GIVING.equals(req.getType())) {
            // 后台发券
            IotAssert.isNull(req.getSendCouponRule(), "后台发券无需传入发券规则");
        }
        if (ActivityType.CHARGE_GIVING.equals(req.getType())) {
            // 充值赠送
            IotAssert.isNull(req.getSendCouponRule(), "充值赠送无需传入发券规则");
        }

        if (ActivityType.getFreeActivities().contains(req.getType())) {
            // 除了充值赠送

            // 找到一个最小和最大的时间作为整个活动的开始结束时间
            Date minTime = null;
            Date maxTime = null;

            // 校验发券规则参数列表
            IotAssert.isTrue(CollectionUtils.isNotEmpty(req.getRuleParamList()),
                "请传入发券规则参数");
            Integer acquireCount = 0;
            Integer quantityAccount = 0;
            List<TimeFilter> timeList = new ArrayList<>();
            for (ActivityCouponRuleParam ruleParam : req.getRuleParamList()) {

                IotAssert.isNotNull(ruleParam.getTimeFrom(), "请传入活动开始时间");
                IotAssert.isNotNull(ruleParam.getTimeTo(), "请传入活动结束时间");
                // 修复时间为00s
                ruleParam.setTimeFrom(
                    DateUtil.adjustDateToSpecificSecond(ruleParam.getTimeFrom(), 0));
                // 修复时间为59s
                ruleParam.setTimeTo(DateUtil.adjustDateToSpecificSecond(ruleParam.getTimeTo(), 59));

                IotAssert.isTrue(
                    ruleParam.getTimeFrom().getTime() <= ruleParam.getTimeTo().getTime(),
                    "活动时间结束时间必须在开始时间之后");
                TimeFilter timeFilter = new TimeFilter();
                timeFilter.setStartTime(ruleParam.getTimeFrom());
                timeFilter.setEndTime(ruleParam.getTimeTo());
                timeList.add(timeFilter);

                if (ActivityType.PLATFORM_GIVING.equals(req.getType())) {
                    // 后台发券
                    IotAssert.isNotNull(ruleParam.getRepeatActive(),
                        "请选择同一手机号能否重复发券");
                    IotAssert.isNotNull(ruleParam.getCouponsPerTime(), "请选择每种券发送张数");
                    if (ruleParam.getMaxAmountPerTime() == null
                        || ruleParam.getMaxAmountPerTime() <= 0) {
                        throw new DcArgumentException("请传入最大发券次数");
                    }
                }
                if (ActivityType.FREE_ACQUIRE.equals(req.getType())
                    || ActivityType.NEW_GUEST_ACQUIRE.equals(req.getType())) {
                    // 免费领券或新人领券
                    if (ruleParam.getCouponsPerTime() == null
                        || ruleParam.getCouponsPerTime() <= 0) {
                        throw new DcArgumentException("请传入每次领取的券数");
                    }
                    if (ruleParam.getMaxAmountPerTime() == null
                        || ruleParam.getMaxAmountPerTime() <= 0) {
                        throw new DcArgumentException("请传入最大领取次数");
                    }
                }

                if (ruleParam.getCouponsPerTime() > acquireCount) {
                    acquireCount = ruleParam.getCouponsPerTime();
                }
                quantityAccount += ruleParam.getMaxAmountPerTime();


                if (minTime == null || ruleParam.getTimeFrom().getTime() <= minTime.getTime()) {
                    minTime = ruleParam.getTimeFrom();
                }
                if (maxTime == null || ruleParam.getTimeTo().getTime() >= maxTime.getTime()) {
                    maxTime = ruleParam.getTimeTo();
                }
            }

            if (DateUtil.overlap(timeList, true)) {
                throw new DcArgumentException("发券规则里的多段时间不能重合");
            }

            req.setTimeFrom(minTime);
            req.setTimeTo(maxTime);

            IotAssert.isTrue(CollectionUtils.isNotEmpty(req.getCouponDictIdList()), "请选择券模板");

//            if (!ActivityType.PLATFORM_GIVING.equals(req.getType())) {
//                IotAssert.isNotNull(req.getAcquireCount(), "请传入单次领券数");
//            } else {
//                req.setAcquireCount(0);
//            }

            req.setAcquireCount(acquireCount);
            req.setQuantityAccount(quantityAccount);

//            IotAssert.isNotNull(req.getQuantityAccount(), "请传入领券人数");
//            IotAssert.isTrue(CollectionUtils.isNotEmpty(req.getCouponDictIdList()), "请选择关联券模板");
            if (!List.of(ActivityType.NEW_GUEST_ACQUIRE, ActivityType.PLATFORM_GIVING)
                .contains(req.getType())) {
                IotAssert.isTrue(CollectionUtils.isNotEmpty(req.getImageUrlList()),
                    "请传入活动图片");
            }
//            if (!ActivityType.NEW_GUEST_ACQUIRE.equals(req.getType())) {
//                IotAssert.isTrue(CollectionUtils.isNotEmpty(req.getImageUrlList()),
//                    "请传入活动图片");
//            }

            req.getCouponDictIdList().stream().forEach(e -> {
                CouponDictPo byId = couponDictRoDs.getById(e);
                IotAssert.isNotNull(byId, "找不到券模板: " + e);
                IotAssert.isTrue(CouponDictStatusType.ENABLE.equals(byId.getStatus()),
                    "模板当前不可用: " + byId.getName());

//                if (CouponValidType.FIX.equals(byId.getValidType())) {
//                    log.info("判断活动时间与模板生效时间冲突");
//                    IotAssert
//                        .isTrue(/*byId.getValidTimeFrom().getTime() <= req.getTimeFrom().getTime() &&*/
//                            req.getTimeTo().getTime() <= byId.getValidTimeTo().getTime(),
//                            "活动时间与模板生效时间冲突");
//                }

                req.getRuleParamList().forEach(ruleParam -> {
                    if (CouponValidType.FIX.equals(byId.getValidType())) {
                        log.info("判断活动时间与模板生效时间冲突");
                        IotAssert
                            .isTrue(/*byId.getValidTimeFrom().getTime() <= req.getTimeFrom().getTime() &&*/
                                ruleParam.getTimeTo().getTime() <= byId.getValidTimeTo().getTime(),
                                "活动时间与模板生效时间冲突");
                    }
                });

            });
        } else {
            // 充值赠送
            // 修复时间为00:00:00
            req.setTimeFrom(DateUtil.getThisDate(req.getTimeFrom()));
            req.setTimeTo(DateUtil.getNextDate(DateUtil.getThisDate(req.getTimeTo())));
            IotAssert.isTrue(req.getTimeFrom().getTime() <= req.getTimeTo().getTime(),
                "活动时间结束时间必须在开始时间之后");
            // 充赠活动设置领券字段为0
            req.setAcquireCount(0);
            req.setQuantityAccount(0);
        }

//        // 修复时间为00:00:00
//        req.setTimeFrom(DateUtil.getThisDate(req.getTimeFrom()));
//        req.setTimeTo(DateUtil.getNextDate(DateUtil.getThisDate(req.getTimeTo())));
//        IotAssert.isTrue(req.getTimeFrom().getTime() <= req.getTimeTo().getTime(),
//            "活动时间结束时间必须在开始时间之后");

//        req.getCouponDictIdList().stream().forEach(e -> {
//            CouponDictPo byId = couponDictRoDs.getById(e);
//            IotAssert.isNotNull(byId, "找不到券模板: " + e);
//            IotAssert.isTrue(CouponDictStatusType.ENABLE.equals(byId.getStatus()),
//                    "模板当前不可用: " + byId.getName());
//
//            if (CouponValidType.FIX.equals(byId.getValidType())) {
//                log.info("判断活动时间与模板生效时间冲突");
//                IotAssert.isTrue(/*byId.getValidTimeFrom().getTime() <= req.getTimeFrom().getTime() &&*/
//                        req.getTimeTo().getTime() <= byId.getValidTimeTo().getTime(), "活动时间与模板生效时间冲突");
//            }
//
//        });

//        if (req.getType() == null) {
//            req.setType(ActivityType.FREE_ACQUIRE);
//        }

        ActivityPo activityPo = new ActivityPo();
        BeanUtils.copyProperties(req, activityPo);

        Date now = new Date();
        IotAssert.isTrue(now.getTime() < req.getTimeTo().getTime(),
            "活动时间结束时间必须在当前时间之后");
        if (req.getTimeFrom().getTime() <= now.getTime() && now.getTime() < req.getTimeTo()
            .getTime()) {
            log.info("当前时间处于活动开始结束时间范围内，活动状态设置为进行中");
            activityPo.setStatus(ActivityStatusType.PROCESSING);
        } else {
            activityPo.setStatus(ActivityStatusType.PUBLISHED);
        }

        IotAssert.isTrue(activityRwDs.insertActivity(activityPo), "新增活动失败");

        if (req.getType().equals(ActivityType.CHARGE_GIVING)) { // 充赠活动
            log.info("活动Id,id={}", activityPo.getId());
            if (CollectionUtils.isNotEmpty(req.getChargeList())) {
                IotAssert.isTrue(
                    activityDiscountRwDs.batchInsert(activityPo.getId(), req.getChargeList()),
                    "创建满送信息失败");
            }
            return;
        }

        final String ActivityUrl = activityUrlPattern
            .replace(activityUrlPatternReplace, activityPo.getId().toString());
        activityPo.setUrl(ActivityUrl);
        IotAssert.isTrue(activityRwDs.updateActivity(activityPo), "新增活动链接失败");

        IotAssert.isTrue(
            activityCouponRwDs.batchInsert(
                req.getCouponDictIdList()
                    .stream()
                    .map(e -> new ActivityCouponPo().setActivityId(activityPo.getId())
                        .setCouponDictId(e))
                    .collect(Collectors.toList())
            ) == req.getCouponDictIdList().size(), "创建关联活动模板失败");

        if (CollectionUtils.isNotEmpty(req.getImageUrlList())) {
            IotAssert.isTrue(activityImageRwDs.batchInsert(
                req.getImageUrlList()
                    .stream()
                    .map(e -> new ActivityImagePo().setUrl(e).setActivityId(activityPo.getId()))
                    .collect(Collectors.toList())
            ) == req.getImageUrlList().size(), "创建活动图片失败");
        }

        if (CollectionUtils.isNotEmpty(req.getRuleParamList())) {
            IotAssert.isTrue(activityCouponRuleRwDs.batchInsert(
                req.getRuleParamList()
                    .stream()
                    .map(e -> new ActivityCouponRulePo()
                        .setActivityId(activityPo.getId())
                        .setTimeFrom(e.getTimeFrom())
                        .setTimeTo(e.getTimeTo())
                        .setRepeatActive(e.getRepeatActive())
                        .setCouponsPerTime(e.getCouponsPerTime())
                        .setMaxAmountPerTime(e.getMaxAmountPerTime())
                        .setTotalAmount(e.getTotalAmount())
                    )
                    .collect(Collectors.toList())
            ) == req.getRuleParamList().size(), "创建规则参数列表失败");
        }
    }

    public ActivityVo getActivityDetail(Long id) {
        IotAssert.isNotNull(id, "请传入活动id");
        ActivityVo ret = activityRoDs.getActivityVo(id);
        IotAssert.isNotNull(ret, "活动不存在: " + id);

        if (CollectionUtils.isNotEmpty(ret.getDictList())) {
            ret.getDictList().stream().forEach(e -> {
                List<SitePo> couponSiteList = couponRoDs.getCouponSiteList(e.getId());
                if (CollectionUtils.isNotEmpty(couponSiteList)) {
                    e.setSiteList(couponSiteList);
                } else {
                    e.setSiteList(couponRoDs.getCouponSiteGidList(e.getId()));
                }
                List<CouponScoreSettingPo> couponScoreSettingList = couponRoDs.getCouponScoreSettingList(
                    e.getId());
                if (CollectionUtils.isNotEmpty(couponScoreSettingList)) {
                    e.setScoreSettingList(couponScoreSettingList);
                }
            });
        }
        if (ret.getType().equals(ActivityType.CHARGE_GIVING)) { // 充赠活动
            ret.setChargeList(activityDiscountRoDs.getById(ret.getId(), Boolean.TRUE));
        }
        if (ActivityType.getFreeActivities().contains(ret.getType())) { // 券信息
            ret.setAlreadyAcquireNum(couponRoDs.getAlreadyAcquireNum(id));
        }

        // 获取规则参数列表
        List<ActivityCouponRulePo> couponRulePoList = activityCouponRuleRoDs.getListByActivityId(
            id);
        if (CollectionUtils.isNotEmpty(couponRulePoList)) {
            ret.setRuleParamList(couponRulePoList);
        }

        // 补充是否允许编辑信息
        List<ActivityPo> activityPoList = activityRoDs.getByName(ret.getName());
        boolean existsLargerId = activityPoList.stream()
            .anyMatch(activityPo -> activityPo.getId() > ret.getId());
        // 存在则不允许编辑
        ret.setAllowEdit(!existsLargerId);

        return ret;
    }

    /**
     * 领券
     *
     * @param activityId
     * @param phone
     * @return
     */
    @Transactional
    public ObjectResponse<AcquireCouponResult> acquireCoupon(Long activityId, String phone,
        Long uid) {

        // STEP 0.先获得行锁(防止券多发)
        ActivityPo po = activityRwDs.getById(activityId, true);

        if (uid == null) {
            IotAssert.isTrue(RegularExpressionUtil.isPhone(phone), "请输入正确手机号");
        }
        ActivityVo activityVo = activityRoDs.getActivityVo(po.getId());
        IotAssert.isNotNull(activityVo, "活动不存在");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(activityVo.getDictList()), "活动未关联券模板");
        IotAssert.isTrue(activityVo.getStatus() == ActivityStatusType.PROCESSING,
            "当前活动无法领券");
        // 获取规则参数列表
        List<ActivityCouponRulePo> couponRulePoList = activityCouponRuleRoDs.getListByActivityId(
            activityId);
        activityVo.setRuleParamList(couponRulePoList);

        // STEP 1.根据activityId 得到举办券活动的商户
        Long commId = activityVo.getCommId();
        Long topCommId = activityVo.getActivityTopCommId();

        // STEP 2.判断手机号是否存在于系统，若不存在则新增
        Long userId = uid == null ? couponTouchUser(commId, topCommId, phone) : uid;
//        ObjectResponse<UserPropVO> jsonObjectRes = userFeignClient.findByPhone(phone, topCommId);
//        if (jsonObjectRes == null || jsonObjectRes.getData() == null) {
//            AddUserParam param = new AddUserParam(topCommId,
//                commId,
//                "86",
//                phone,
//                null,
//                phone,
//                null, null);
//            ObjectResponse<Long> ret = userFeignClient.addUser(param);
//            FeignResponseValidate.check(ret);
//            userId = ret.getData();
//        } else {
//            UserPropVO userPropVO = jsonObjectRes.getData();
//            userId = userPropVO.getUserId();
//        }

        // STEP 3.判断用户是否已领取该活动的券
        if (CollectionUtils.isEmpty(activityVo.getRuleParamList())
            || activityVo.getSendCouponRule() == null) {
            // ruleParamList为空或者未配置发券规则表示旧版本
            // 或者整个时间范围内每用户限领一次
            boolean IsAcquireCounpon = couponRoDs.IsAcquireCounpon(activityId, userId);
            if (IsAcquireCounpon) {
                return RestUtils.buildObjectResponse(AcquireCouponResult.ALREADY_ACQUIRE);// 已经领取过
            }
            Long nums = couponRoDs.getAlreadyAcquireNum(activityId);// 当前活动已领券人数
            if (nums.intValue() >= activityVo.getQuantityAccount()) {
                return RestUtils.buildObjectResponse(
                    AcquireCouponResult.QUANTITY_ACCOUNT_LIMIT_REACHED);// 领券人数已满，活动结束
            }
            // 领券
            this.associatedUserAndCoupon(userId, activityVo, null);

            // STEP 4.领券后人数加1，若等于活动配置的领券人数，则活动结束
            ++nums;
            if (NumberUtils.equals(nums.intValue(), activityVo.getQuantityAccount())) {
                ActivityPo update = new ActivityPo();
                update.setId(po.getId())
                    .setStatus(ActivityStatusType.FINISHED);
                activityRwDs.updateActivity(update);
            }
        } else {
            // 在ruleParamList里找到包含当前时间的
            Date now = new Date();
            List<ActivityCouponRulePo> activityCouponRulePoList = activityVo.getRuleParamList()
                .stream()
                .filter(ruleParam -> !ruleParam.getTimeFrom().after(now) && !ruleParam.getTimeTo()
                    .before(now))
                .toList();
            IotAssert.isTrue(CollectionUtils.isNotEmpty(activityCouponRulePoList),
                "不在活动生效时间内");
            if (CouponSendType.ONCE.equals(
                activityVo.getSendCouponRule())) {
                // 判断是否领过券
                boolean IsAcquireCounpon = couponRoDs.IsAcquireCounpon(activityId, userId);
                if (IsAcquireCounpon) {
                    return RestUtils.buildObjectResponse(AcquireCouponResult.ALREADY_ACQUIRE);// 已经领取过
                }
            }
            // 判断在当前规则id里是否领过券
            // 用于存储第一个未满的规则参数的索引
            int notFullIndex = -1;
            // 假设所有规则都已满
            boolean allFull = true;
            // 用户已经领取的次数
            int userAlreadyAcquiredNum = 0;
            // 所有用户已领取的次数
            long allUserAlreadyAcquiredNum = 0L;

            for (int i = 0; i < activityCouponRulePoList.size(); i++) {
                ActivityCouponRulePo ruleParam = activityCouponRulePoList.get(i);
                boolean isAcquireCoupon = couponRoDs.isAcquireCouponByRuleId(activityId, userId,
                    ruleParam.getId());
                Long nums = couponRoDs.getAlreadyAcquireNumByRuleId(activityId, ruleParam.getId());
                allUserAlreadyAcquiredNum += nums.longValue();
                if (isAcquireCoupon) {
                    // 已经领过，已经领过的数字+1，然后进入下一次循环去查询
                    userAlreadyAcquiredNum++;
                    continue;
                }
                if (nums.intValue() < ruleParam.getMaxAmountPerTime()) {
                    // 记录第一个未满的规则参数的索引
                    notFullIndex = i;
                    // 存在未满的规则参数
                    allFull = false;
                    // 退出循环
                    break;
                }
            }
            if (userAlreadyAcquiredNum == activityCouponRulePoList.size()) {
                // 当前活动生效时间的参数列表都领过了
                return RestUtils.buildObjectResponse(
                    AcquireCouponResult.ALREADY_ACQUIRE); // 账号已领取过
            }
            if (allFull) {
                return RestUtils.buildObjectResponse(
                    AcquireCouponResult.QUANTITY_ACCOUNT_LIMIT_REACHED); // 领券人数已满，活动结束
            }
            // 领券
            this.associatedUserAndCoupon(userId, activityVo,
                activityCouponRulePoList.get(notFullIndex).getId());

            // STEP 4.领券后人数加1，若等于活动配置的领券人数，则活动结束
            allUserAlreadyAcquiredNum++;
            if (allUserAlreadyAcquiredNum == po.getQuantityAccount().longValue()) {
                ActivityPo update = new ActivityPo();
                update.setId(po.getId())
                    .setStatus(ActivityStatusType.FINISHED);
                activityRwDs.updateActivity(update);
            }
        }

        return RestUtils.buildObjectResponse(AcquireCouponResult.SUCCEED);// 领取成功
    }

    /**
     * 优惠券-获取（创建）账户
     *
     * @param commId
     * @param topCommId
     * @param phone
     * @return
     */
    private Long couponTouchUser(Long commId, Long topCommId, String phone) {
        Long userId = 0L;
        ObjectResponse<UserPropVO> jsonObjectRes = userFeignClient.findByPhone(phone, topCommId);
        if (jsonObjectRes == null || jsonObjectRes.getData() == null) {
            AddUserParam param = new AddUserParam(topCommId,
                commId,
                "86",
                phone,
                null,
                phone,
                null, null);
            ObjectResponse<Long> ret = userFeignClient.addUser(param);
            FeignResponseValidate.check(ret);
            userId = ret.getData();
        } else {
            UserPropVO userPropVO = jsonObjectRes.getData();
            userId = userPropVO.getUserId();
        }
        return userId;
    }

    /**
     * 建立客户和券的关系
     *
     * @param userId
     * @param activityVo
     * @param ruleId
     */
    public void associatedUserAndCoupon(final Long userId, ActivityVo activityVo, Long ruleId) {
        activityVo.getDictList().forEach(e -> {
            CouponPo newCoupon = new CouponPo();
            newCoupon.setActivityId(activityVo.getId())
                .setCouponDictId(e.getId())
                .setUserId(userId)
                .setTopCommId(activityVo.getActivityTopCommId())
                .setType(e.getType())
                .setPersonalEnable(e.getPersonalEnable())
                .setPrepayEnable(e.getPrepayEnable())
                .setCommEnable(e.getCommEnable())
                .setWxCreditEnable(e.getWxCreditEnable())
                .setValidType(e.getValidType())
                .setValidRelateDay(e.getValidRelateDay())
                .setConditionAmount(e.getConditionAmount())
                .setShowConditionAmount(e.getShowConditionAmount())
                .setAmount(e.getAmount())
                .setRuleId(ruleId);

            LocalDate now = LocalDate.now();
            if (newCoupon.getValidType() == CouponValidType.FIX) {
                newCoupon.setValidTimeFrom(e.getValidTimeFrom())
                    .setValidTimeTo(e.getValidTimeTo());

                LocalDate timeFrom = e.getValidTimeFrom().toInstant().atZone(ZoneId.systemDefault())
                    .toLocalDate();
                if (now.isEqual(timeFrom) || now.isAfter(timeFrom)) {
                    newCoupon.setStatus(CouponStatusType.ENABLE);
                } else {
                    newCoupon.setStatus(CouponStatusType.DISABLE);
                }

            } else if (newCoupon.getValidType() == CouponValidType.RELATE) {
                newCoupon.setStatus(CouponStatusType.ENABLE)
                    .setValidTimeFrom(DateUtils.toDate(now))
                    .setValidTimeTo(DateUtils.toDate(now.plusDays(newCoupon.getValidRelateDay())));
            }

            List<CouponPo> poList = new ArrayList<>();
            int acquireCount = activityVo.getAcquireCount();
            if (ruleId != null && CollectionUtils.isNotEmpty(activityVo.getRuleParamList())) {
                Optional<Integer> couponsPerTimeOptional = activityVo.getRuleParamList().stream()
                    .filter(
                        ruleParam -> ruleParam.getId() != null && ruleParam.getId().equals(ruleId))
                    .map(ActivityCouponRulePo::getCouponsPerTime)
                    .findFirst();
                acquireCount = couponsPerTimeOptional.orElse(0);
            }
            IotAssert.isTrue(acquireCount >= 0, "领券异常，数量不能小于1");
            for (int i = 0; i < acquireCount; i++) {
                poList.add(newCoupon);
            }
            couponRwDs.insertCoupon(poList);
        });
    }

    /**
     * 新客领券
     *
     * @param param
     * @return
     */
    @Transactional(isolation = Isolation.READ_COMMITTED)
    public ObjectResponse<AcquireCouponResult> newGuestAcquireCoupon(NewGuestAcquireParam param) {
        // TODO: 2023/6/25 @WZ 入参可简化
        Long userId = param.getUserId();
        {
            // 账号校验
            ObjectResponse<CusSampleDto> response = userFeignClient.getCusSampleDtoById(userId);
            FeignResponseValidate.check(response);
            CusSampleDto cusSampleDto = response.getData();

            IotAssert.isTrue(Constant.USER_STATUS_NORMAL == cusSampleDto.getStatus(),
                "领券失败，账号状态异常");

            // 校验客户是否满足新客条件
            Date date = Optional.ofNullable(cusSampleDto.getRegTime())
                .orElse(cusSampleDto.getCreateTime());
            LocalDateTime regTime = DateUtil.date2LocalDateTime(date);
            if (regTime.isBefore(LocalDateTime.now().minusHours(this.newGuestRegTimeLimit))) {
                return RestUtils.buildObjectResponse(AcquireCouponResult.NON_NEW_GUEST);
            }
        }

        // STEP0.查询有效的新人领券活动
        List<Long> availableNewGuestActivities = activityRoDs.getAvailableNewGuestActivity();
        IotAssert.isTrue(CollectionUtils.isNotEmpty(availableNewGuestActivities), "暂无相关活动");

        // STEP1.先获得行锁(防止券多发)
        List<ActivityPo> activityPoList = activityRwDs.getByIdList(availableNewGuestActivities,
            true);

        // <活动ID，可领券人数>
        Map<Long, Integer> validActivityMap = activityPoList.stream()
            .collect(Collectors.toMap(ActivityPo::getId, ActivityPo::getQuantityAccount));

        // STEP2.剔除领券人数已满的活动
        List<ActivityTempDto> activityTempDtos = couponRoDs.getAlreadyAcquireNumList(
            new ArrayList<>(validActivityMap.keySet())); // 获取活动已领券人数
        if (CollectionUtils.isNotEmpty(activityTempDtos)) {
            activityTempDtos.forEach(t -> {
                if (t.getCount() >= validActivityMap.get(t.getActivityId())) {
                    validActivityMap.remove(t.getActivityId());
                }
            });

            if (validActivityMap.size() == 0) {
                return RestUtils.buildObjectResponse(
                    AcquireCouponResult.QUANTITY_ACCOUNT_LIMIT_REACHED);
            }
        }
        // <活动ID，已领券人数>
        Map<Long, Long> acquireNumMap = activityTempDtos.stream()
            .collect(Collectors.toMap(ActivityTempDto::getActivityId, ActivityTempDto::getCount));

        // STEP3.剔除用户已参与的活动
        List<Long> participatedActivities = couponRoDs.getParticipatedActivitiesByUserId(
            userId);
        if (CollectionUtils.isNotEmpty(participatedActivities)) {
            participatedActivities.forEach(validActivityMap::remove);

            if (validActivityMap.size() == 0) {
                return RestUtils.buildObjectResponse(AcquireCouponResult.ALREADY_ACQUIRE);
            }
        }

        List<ActivityVo> activityVoList = activityRoDs.getActivityVoList(
            new ArrayList<>(validActivityMap.keySet()));
        activityVoList.forEach(vo -> {
            // STEP4.领券
            this.associatedUserAndCoupon(userId, vo,
                CollectionUtils.isNotEmpty(vo.getRuleParamList()) ? vo.getRuleParamList().get(0)
                    .getId() : null);

            // STEP5.领券后人数加1，若等于活动配置的领券人数，则活动结束
            int num = acquireNumMap.getOrDefault(vo.getId(), 0L).intValue() + 1;
            if (NumberUtils.equals(num, vo.getQuantityAccount())) {
                ActivityPo update = new ActivityPo();
                update.setId(vo.getId())
                    .setStatus(ActivityStatusType.FINISHED);
                activityRwDs.updateActivity(update);
            }

        });

        return RestUtils.buildObjectResponse(AcquireCouponResult.SUCCEED);// 领取成功
    }

    public ObjectResponse<Long> countByCondition(CouponPo po) {
        return RestUtils.buildObjectResponse(couponRoDs.countByCondition(po));
    }

    public ObjectResponse<CouponBi> couponBi(Long id) {
        IotAssert.isNotNull(id, "请传入活动id");
        CouponBi ret = new CouponBi();
        ActivityPo byId = activityRoDs.getById(id);
        IotAssert.isNotNull(byId, "活动不存在: " + id);
        // 根据活动name查出来所有的历史版本
        List<ActivityPo> activityPoList = activityRoDs.getByName(byId.getName());
        // 筛选出来不大于当前活动id的，找出来当前id以及之前的所有历史版本
        List<ActivityPo> filteredActivityPoList = activityPoList.stream()
            .filter(activityPo -> activityPo.getId() <= id)
            .sorted(Comparator.comparingLong(ActivityPo::getId).reversed())
            .toList();
        List<Long> activityIdList = filteredActivityPoList.stream().map(ActivityPo::getId).toList();
//        ret.setAcquireCount(byId.getAcquireCount())
//            .setQuantityAccount(byId.getQuantityAccount())
//            .setAlreadyAcquireNum(couponRoDs.getAlreadyAcquireNum(id).intValue())
//            .setAlreadyAcquireCouponNum(couponRoDs.getAlreadyAcquireCouponNum(id).intValue())
//            .setUsedCouponNum(couponRoDs.getUsedCouponNum(id).intValue())
//            .setUnusedCouponNum(couponRoDs.getUnusedCouponNum(id).intValue())
//            .setExpiredCouponNum(couponRoDs.getExpiredCouponNum(id).intValue())
//            .setDisableCouponNum(couponRoDs.getDisableCouponNum(id).intValue());
        ret
            // 由于后续版本均是依据第一个版本迭代的，因此领券人次按照id最小的去取值
            .setAcquireCount(
                filteredActivityPoList.get(filteredActivityPoList.size() - 1).getAcquireCount())
            // 已领人次选取所有版本的和
            .setQuantityAccount(activityPoList.stream()
                .mapToInt(ActivityPo::getQuantityAccount)
                .sum())
            // 按ruleId进行统计
            .setAlreadyAcquireNum(
                Math.toIntExact(couponRoDs.getAlreadyAcquireNumListByRuleId(activityIdList).stream()
                    .mapToLong(ActivityTempDto::getCount).sum()))
            .setAlreadyAcquireCouponNum(
                Math.toIntExact(couponRoDs.getAlreadyAcquireCouponNumList(activityIdList).stream()
                    .mapToLong(ActivityTempDto::getCount).sum()))
            .setUsedCouponNum(
                Math.toIntExact(couponRoDs.getUsedCouponNumList(activityIdList).stream()
                    .mapToLong(ActivityTempDto::getCount).sum()))
            .setUnusedCouponNum(
                Math.toIntExact(couponRoDs.getUnusedCouponNumList(activityIdList).stream()
                    .mapToLong(ActivityTempDto::getCount).sum()))
            .setExpiredCouponNum(
                Math.toIntExact(couponRoDs.getExpiredCouponNumList(activityIdList).stream()
                    .mapToLong(ActivityTempDto::getCount).sum()))
            .setDisableCouponNum(
                Math.toIntExact(couponRoDs.getDisableCouponNumList(activityIdList).stream()
                    .mapToLong(ActivityTempDto::getCount).sum()));

        // 后台发券   已领券人次 是根据批次来的
        if (ActivityType.PLATFORM_GIVING.equals(byId.getType())) {
//            ret.setAlreadyAcquireNum(couponRoDs.getPlatformAlreadyAcquireNum(id).intValue());
            ret.setAlreadyAcquireNum(
                Math.toIntExact(couponRoDs.getPlatformAlreadyAcquireNumList(activityIdList).stream()
                    .mapToLong(ActivityTempDto::getCount).sum()));
        }

        return new ObjectResponse<>(ret);
    }

    public ListResponse<CouponVo> userActivityCouponList(CouponSearchParam req) {
        if (req.getActivityId() != null) {
            ActivityPo byId = activityRoDs.getById(req.getActivityId());
            IotAssert.isNotNull(byId, "活动不存在");
            // 先通过名称找到所有历史版本的id列表
            List<ActivityPo> activityPoList = activityRoDs.getByName(byId.getName());
            // 筛选出来不大于当前活动id的，找出来当前id以及之前的所有历史版本
            List<ActivityPo> filteredActivityPoList = activityPoList.stream()
                .filter(activityPo -> activityPo.getId() <= req.getActivityId())
                .sorted(Comparator.comparingLong(ActivityPo::getId).reversed())
                .toList();
            List<Long> activityIdList = filteredActivityPoList.stream().map(ActivityPo::getId)
                .toList();
            // 用activityIdList去查，不需要用activityId去查了
            req.setActivityIdList(activityIdList);
            req.setActivityId(null);
        }
        Page<CouponVo> pageInfo = PageHelper
            .startPage(req.getIndex(), req.getSize(), true, false, null);

        List<CouponVo> list = couponRoDs.findList(req);

        return new ListResponse<>(list, pageInfo.getTotal());

    }

    public ListResponse<ActivityVo> listActivity(ListActivityParam req) {
//        Page<ActivityVo> pageInfo = PageHelper.startPage(req.getIndex(), req.getSize(), true, false, null);

        if (req.getStart() == null || req.getStart() < 0) {
            req.setStart(0L);
        }
        if (req.getSize() == null || req.getSize() < 0) {
            req.setSize(10);
        }

//        req.setIndex((req.getIndex() - 1) * req.getSize());
//        req.setSize(req.getIndex() + req.getSize());

        if (StringUtils.isNotBlank(req.getCouponId())) {
            log.info("转换券id到活动id: {}", req.getCouponId());

            long id = NumberUtils.parseLong(req.getCouponId(), 0);
            CouponPo byId = couponRoDs.getById(id);

            if (byId != null) {
                log.info("转换券id到活动id成功: {} -> {}", req.getCouponId(), byId.getActivityId());
                req.setActivityId(byId.getActivityId());
            } else {
                log.info("转换券id到活动id失败，使用0作为活动号，使出参列表为空");
                req.setActivityId(0L);
            }
        }

        List<ActivityVo> list = activityRoDs.findList(req);
        // 处理是否允许编辑，同名的活动仅最新一条允许编辑，修改后的活动，之前的活动不允许再次编辑
        Map<String, Boolean> allowEditMap = new HashMap<>();
        list.forEach(item -> {
            if (allowEditMap.containsKey(item.getName())) {
                item.setAllowEdit(false);
            } else {
                // 查询结果里没有，去查一次数据库里有没有比当前id大的同名的活动
                // 根据活动name查出来所有的历史版本
                List<ActivityPo> activityPoList = activityRoDs.getByName(item.getName());
                boolean existsLargerId = activityPoList.stream()
                    .anyMatch(activityPo -> activityPo.getId() > item.getId());
                // 存在则不允许编辑
                item.setAllowEdit(!existsLargerId);
                allowEditMap.put(item.getName(), true);
            }
        });
        return new ListResponse<>(list, activityRoDs.findListCount(req));
    }

    public int refreshStatus() {
        return activityRwDs.refreshStatus2Processing() +
            activityRwDs.refreshStatus2Finished();
    }

    public ObjectResponse<DiscountVo> discountInfo(Long commId, BigDecimal amount) {
        ActivityVo activityVo = activityRoDs.getActivityByFrom(commId);
        if (activityVo == null) {
            return RestUtils.buildObjectResponse(null);
        }
        ChargePo discountInfo = activityDiscountRoDs.getDiscountInfo(activityVo.getId(), amount);

        DiscountVo discountVo = new DiscountVo();
        discountVo.setChargeList(activityDiscountRoDs.getById(activityVo.getId(), Boolean.TRUE));

        discountVo.setDiscountAmount(discountInfo != null ? discountInfo.getDiscountAmount() : null)
            .setTimeFrom(activityVo.getTimeFrom())
            .setTimeTo(DateUtil.getPrevDate(activityVo.getTimeTo()))
            .setComment(activityVo.getComment())
            .setActivityId(activityVo.getId());

        return RestUtils.buildObjectResponse(discountVo);
    }

    public ObjectResponse<ActivityVo> hasActivity(Long commId, Long accountType) {
        return RestUtils.buildObjectResponse(activityRoDs.hasActivity(commId, accountType));
    }

    public ObjectResponse<Map<String, Boolean>> hasUserAcquiredCoupon(
        ActivityUserCouponParam param) {
        if (CollectionUtils.isEmpty(param.getPhoneList())) {
            return RestUtils.buildObjectResponse(new HashMap<>());
        }
        // 查看活动是否存在
        ActivityPo activityPo = activityRoDs.getById(param.getActivityId());
        IotAssert.isNotNull(activityPo, "活动不存在: " + activityPo);
        // 拿到名称，然后拿到所有同名的活动（同名表示编辑过，把编辑前的领取信息也需要统计到）
        List<ActivityPo> activityPoList = activityRoDs.getByName(activityPo.getName());
        List<Long> activityIdList = activityPoList.stream().map(ActivityPo::getId)
            .collect(Collectors.toList());
        List<CouponVo> couponVoList = couponRoDs.getByActivityIdListAndUserPhoneList(activityIdList,
            param.getPhoneList());
        if (CollectionUtils.isEmpty(couponVoList)) {
            return RestUtils.buildObjectResponse(new HashMap<>());
        }
        Map<String, Boolean> acquiredCouponResult = new HashMap<>();
        param.getPhoneList().forEach(p -> {
            acquiredCouponResult.put(p, false);
        });
        couponVoList.forEach(couponVo -> {
            if (acquiredCouponResult.containsKey(couponVo.getPhone())) {
                acquiredCouponResult.put(couponVo.getPhone(), true);
            }
        });
        return RestUtils.buildObjectResponse(acquiredCouponResult);
    }
}
