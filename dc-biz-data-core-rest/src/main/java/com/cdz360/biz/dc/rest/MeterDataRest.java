package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.es.vo.MeterRtData;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.MeterDataService;
import com.cdz360.biz.model.trading.meter.param.MeterDataListParam;
import com.cdz360.biz.model.trading.meter.vo.MeterDataVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "电表抄表数据相关接口", description = "电表抄表数据")
@Slf4j
@RestController
@RequestMapping("/dataCore/meterData")
public class MeterDataRest {

    @Autowired
    private MeterDataService meterDataService;

    /**
     * 保存电表上传的实时和历史数据
     * @param siteId 场站id
     * @param gwno 网关编号
     * @param dno 电表编号
     * @param ts 上传时间
     * @param meterRtData 电表相关数据
     * @return
     */
    @Operation(summary = "保存电表上传的实时和历史数据")
    @PostMapping("/saveMeterRtAndHisData")
    public BaseResponse saveMeterRtAndHisData(
        @RequestParam String siteId,
        @RequestParam String gwno,
        @RequestParam String dno,
        @RequestParam String ts,
        @RequestBody MeterRtData meterRtData) {
        log.info(
            ">> 保存电表上传的实时和历史数据.siteId = {}, gwno = {}, dno = {}, ts = {}, meterRtData = {} ",
            siteId, gwno, dno, ts, meterRtData);
        meterDataService.saveMeterRtAndHisData(siteId, gwno, dno, ts, meterRtData);
        return RestUtils.success();
    }

    /**
     * 保存电表上传的实时和历史数据
     * @return
     */
    @Operation(summary = "获取电表抄表数据")
    @PostMapping("/list")
    public ListResponse<MeterDataVo> getMeterDataList(@RequestBody MeterDataListParam param) {
        log.info(
            "获取电表抄表数据.param = {} ", param);
        return meterDataService.getMeterDataList(param);
    }

}
