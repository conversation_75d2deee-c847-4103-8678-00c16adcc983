package com.cdz360.biz.dc.service.ess;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.param.TimeFilter2;
import com.cdz360.base.model.es.vo.EmuDailyFeeFull;
import com.cdz360.base.model.es.vo.EssEquipDailyFee;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ds.ess.ro.data.ds.EssDailyRoDs;
import com.cdz360.biz.ds.ess.ro.data.ds.EssEquipTimelyRoDs;
import com.cdz360.biz.ds.ess.rw.data.ds.EssDailyRwDs;
import com.cdz360.biz.ds.ess.rw.data.ds.EssEquipTimelyRwDs;
import com.cdz360.biz.ds.ess.rw.data.ds.PcsDailyRwDs;
import com.cdz360.biz.ess.model.data.param.DayKwhParam;
import com.cdz360.biz.ess.model.data.po.EssDailyPo;
import com.cdz360.biz.ess.model.data.po.EssEquipTimelyPo;
import com.cdz360.biz.ess.model.data.po.PcsDailyPo;
import com.cdz360.biz.ess.model.data.vo.DayEssDataBi;
import com.cdz360.biz.ess.model.data.vo.DaySiteEssRtDataBi;
import com.cdz360.biz.model.ess.param.ListEssDailyParam;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssDailyDataService {

    @Autowired
    private EssDailyRwDs essDailyRwDs;

    @Autowired
    private EssDailyRoDs essDailyRoDs;

    @Autowired
    private PcsDailyRwDs pcsDailyRwDs;

    @Autowired
    private EssEquipTimelyRoDs essEquipTimelyRoDs;

    @Autowired
    private EssEquipTimelyRwDs essEquipTimelyRwDs;

    @Transactional
    public void saveDailyEmuData(String siteId, EmuDailyFeeFull emuDailyFeeIn) {

        EssDailyPo dailyPo = new EssDailyPo();

        dailyPo.setDno(emuDailyFeeIn.getDno())
            .setSiteId(siteId)
            .setDate(emuDailyFeeIn.getDate())
            .setEnable(true)
            .setInPriceId(emuDailyFeeIn.getPriceId())
            .setOutPriceId(emuDailyFeeIn.getPriceId())

            .setTotalInKwh(emuDailyFeeIn.getTotalInKwh())
            .setTotalOutKwh(emuDailyFeeIn.getTotalOutKwh())

            .setTodayInKwh(emuDailyFeeIn.getInKwh())
            .setTodayOutKwh(emuDailyFeeIn.getOutKwh())

            .setTodayOutFee(emuDailyFeeIn.getInFee())
            .setTodayInFee(emuDailyFeeIn.getOutFee());

        if (emuDailyFeeIn.getInFee() != null && emuDailyFeeIn.getOutFee() != null) {
            dailyPo.setTodayProfit(
                emuDailyFeeIn.getOutFee().subtract(emuDailyFeeIn.getInFee()));
        } else if (emuDailyFeeIn.getOutFee() != null) {
            dailyPo.setTodayProfit(emuDailyFeeIn.getOutFee());
        } else {
            if (null != emuDailyFeeIn.getInFee()) {
                dailyPo.setTodayProfit(BigDecimal.ZERO.subtract(emuDailyFeeIn.getInFee()));
            } else {
                dailyPo.setTodayProfit(BigDecimal.ZERO);
            }
        }
        essDailyRwDs.upsetEssDaily(dailyPo);

        /**
         * key: dno:startTime, value: lock
         */
        Map<String, Boolean> lineLockMap = new HashMap<>();
        ListEssDailyParam param = new ListEssDailyParam();
        LocalDateTime dateTime = emuDailyFeeIn.getDate().atStartOfDay();
        param.setEssDnoList(List.of(emuDailyFeeIn.getDno()))
            .setStartTimeFilter(new TimeFilter2()
                .setStartTime(dateTime)
                .setEndTime(dateTime.plusDays(1)))
            .setSize(9999);
        List<EssEquipTimelyPo> timelyElecList = essEquipTimelyRoDs.getTimelyElecList(param);
        if (CollectionUtils.isNotEmpty(timelyElecList)) {
            lineLockMap = timelyElecList.stream()
                .collect(Collectors.toMap(t -> this.generateLockKey(t.getDno(), t.getStartTime()),
                    t -> Optional.ofNullable(t.getLock()).orElse(false)));
        }

        if (CollectionUtils.isNotEmpty(emuDailyFeeIn.getMeterDailyList())) {
            for (var meter : emuDailyFeeIn.getMeterDailyList()) {
                this.saveEquipTimelyData(lineLockMap, siteId,
                    emuDailyFeeIn.getDno(),
                    meter);
            }
        }
        if (CollectionUtils.isNotEmpty(emuDailyFeeIn.getPcsDailyList())) {
            for (var pcs : emuDailyFeeIn.getPcsDailyList()) {
                this.saveEquipTimelyData(lineLockMap, siteId,
                    emuDailyFeeIn.getDno(),
                    pcs);
            }
        }

//        // PCS数据逻辑
//        saveDailyPcsData(emuDailyFeeIn.getDno(), emuDailyFeeIn.getPcsDailyList());
    }

    private String generateLockKey(String dno, LocalDateTime startTime) {
        return dno + ":" + startTime;
    }

    /**
     * 存储设备分时段明细数据
     */
    private void saveEquipTimelyData(Map<String, Boolean> lineLockMap,
        String siteId, String essDno, EssEquipDailyFee dailyFee) {
        if (CollectionUtils.isEmpty(dailyFee.getItems())) {
            // 明细数据为空,暂时做忽略处理
            return;
        }
        for (var item : dailyFee.getItems()) {
            EssEquipTimelyPo po = new EssEquipTimelyPo();

            LocalDateTime startTime = this.toLocalDateTime(dailyFee.getDate(), item.getStartTime());
            if (BooleanUtils.isTrue(lineLockMap.get(
                this.generateLockKey(dailyFee.getDno(), startTime)))) {
                // 当前行内容已被锁定，内容不可修改
                continue;
            }

            LocalDateTime endTime = this.toLocalDateTime(dailyFee.getDate(), item.getEndTime());
            po.setStartTime(startTime)
                .setEndTime(endTime)
                .setEssDno(essDno)      // 储能柜/ESS/EMU dno
                .setDno(dailyFee.getDno())  // 设备（PCS/电表）dno
                .setSiteId(siteId)
                .setPiName(item.getPiName())
                .setFlowType(item.getType())
                .setInKwh(item.getInKwh() == null ? BigDecimal.ZERO : item.getInKwh())
                .setInFee(item.getInFee() == null ? BigDecimal.ZERO : item.getInFee())
                .setInPrice(item.getInPrice())
                .setInPriceId(item.getPriceId())
                .setTotalInKwh(item.getTotalInKwh())
                .setOutKwh(item.getOutKwh() == null ? BigDecimal.ZERO : item.getOutKwh())
                .setOutFee(item.getOutFee() == null ? BigDecimal.ZERO : item.getOutFee())
                .setOutPrice(item.getOutPrice())
                .setOutPriceId(item.getPriceId())
                .setTotalOutKwh(item.getTotalOutKwh())
                .setEnable(true);

            essEquipTimelyRwDs.addEssEquipTimely(po);
        }

    }

    private LocalDateTime toLocalDateTime(LocalDate date, int minutes) {
        if (minutes > 1440) {    // 避免上报的数据有异常
            log.error("上报的分时段数据异常, 日内minutes超过1440. date= {}, minutes= {}", date,
                minutes);
            minutes = 1440;
        }
        if (minutes == 1440) {   // 跨天到次日 00:00
            return date.plusDays(1)
                .atTime(0, 0, 0);
        } else {
            LocalTime time = LocalTime.of(minutes / 60, minutes % 60, 0);
            return LocalDateTime.of(date, time);
        }
    }

    private void saveDailyPcsData(String essDno, List<EssEquipDailyFee> pcsDailyList) {
        pcsDailyList.stream().map(x -> {
            PcsDailyPo daily = new PcsDailyPo();
            daily.setDno(x.getDno())
                .setEssDno(essDno)
                .setDate(x.getDate())
                .setEnable(true)
                .setInPriceId(x.getPriceId())
                .setOutPriceId(x.getPriceId())
                .setTotalInKwh(x.getTotalInKwh())
                .setTotalOutKwh(x.getTotalOutKwh())
                .setTodayInKwh(x.getInKwh())
                .setTodayOutKwh(x.getOutKwh())
                .setTodayOutFee(x.getInFee())
                .setTodayInFee(x.getOutFee());
            return daily;
        }).forEach(x -> pcsDailyRwDs.upsetPcsDaily(x));
    }

    public Mono<List<DayEssDataBi>> siteRtData7Day(String siteId) {
        if (StringUtils.isBlank(siteId)) {
            throw new DcArgumentException("场站ID不能为空");
        }

        Date toDate = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(toDate);
        calendar.add(Calendar.DAY_OF_MONTH, -8);
        Date fromDate = calendar.getTime();
        return Mono.just(essDailyRoDs.siteRtDataOfDay(siteId, fromDate, toDate));
    }

    public Flux<DaySiteEssRtDataBi> siteDayOfRangeKwh(DayKwhParam param) {
//        if (CollectionUtils.isEmpty(param.getSiteIdList())) {
//            return Flux.fromIterable(List.of());
//        }

        // FIXME: 缺少今天数据
        return Mono.just(essDailyRoDs.siteDayOfRangeKwh(param))
            .flatMapMany(Flux::fromIterable);
    }

}
