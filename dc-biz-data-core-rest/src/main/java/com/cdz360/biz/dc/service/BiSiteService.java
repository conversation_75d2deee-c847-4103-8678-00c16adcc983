package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.model.corp.type.CorpType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.client.AuthCenterFeignClient;
import com.cdz360.biz.ds.trading.ro.site.ds.BiSiteOrderAccountRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.BiSiteOrderRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteDailyBiRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ds.trading.rw.order.ds.BiSiteOrderRwDs;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.BiSiteAccountIncomeRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.BiSiteGcDailyRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.BiSiteOrderAccountRwDs;
import com.cdz360.biz.ds.trading.rw.site.ds.BiSiteOrderHlhtRwDs;
import com.cdz360.biz.model.cus.settlement.param.ListSettlementParam;
import com.cdz360.biz.model.cus.settlement.type.SettlementStatusEnum;
import com.cdz360.biz.model.cus.settlement.vo.SettlementVo;
import com.cdz360.biz.model.trading.bi.dto.SiteAccountProfitDto;
import com.cdz360.biz.model.trading.bi.dto.SiteProfitDto;
import com.cdz360.biz.model.trading.bi.param.AccountSiteIncomeParam;
import com.cdz360.biz.model.trading.order.type.BiDependOnType;
import com.cdz360.biz.model.trading.site.param.ListBiSiteOrderParam;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.po.*;
import com.cdz360.biz.model.trading.site.type.SiteOrderAccountType;
import com.cdz360.biz.utils.feign.settlement.SettlementFeignClient;
import com.cdz360.biz.utils.feign.site.BiSiteOrderFeignClient;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrder;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDataBiVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDataVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDetailBiVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderPowerBiVo;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import java.time.Duration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.stream.Collectors;

/**
 * BiSiteService
 *
 * @since 3/24/2020 10:19 AM
 * <AUTHOR>
 */
@Slf4j
@Service
public class BiSiteService {

    private static final int PAGE_SIZE = 100;
    // 处理线程池
    // 暂时使用 100 个线程
    private static final Executor executor = Executors.newFixedThreadPool(1, new ThreadFactory() {
        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r);
            t.setDaemon(Boolean.TRUE);
            return t;
        }
    });
    @Autowired
    private BiSiteOrderRwDs biSiteOrderRwDs;

    @Autowired
    private BiSiteOrderRoDs biSiteOrderRoDs;

    @Autowired
    private ChargerOrderRwDs chargerOrderDs;
    @Autowired
    private SiteRoDs siteDs;
    //    @Autowired
//    private TradingFeignClient tradingFeignClient;
    @Autowired
    private SiteDailyBiRoDs siteDailyBiRoDs;

    @Autowired
    private BiSiteOrderAccountRwDs biSiteOrderAccountRwDs;

    @Autowired
    private BiSiteOrderAccountRoDs biSiteOrderAccountRoDs;

    @Autowired
    private BiSiteOrderHlhtRwDs biSiteOrderHlhtRwDs;

    @Autowired
    private BiSiteAccountIncomeRwDs biSiteAccountIncomeRwDs;

    @Autowired
    private BiSiteOrderFeignClient biSiteOrderFeignClient;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private SettlementFeignClient settlementFeignClient;
    //    @Async
//    public void asyncHourlyBi(Date date) {
//        hourlyBi(date);
//    }
    private Boolean isWorking = false;
    private Boolean isWorkingDailyAccounting = false;
    @Autowired
    private BiSiteGcDailyRwDs biSiteGcDailyRwDs;

    private static final Integer minElectricity = null;//NOTE 期望将0电量订单也统计在内，所以这里不设定最小值

    private static final Integer maxElectricity = 3000;

    /**
     * 每小时场站统计
     */
    @Transactional
    public void hourlyBi(Date date, BiDependOnType type, String siteId) {
        log.info("订单统计开始: {}", date);
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.clear(Calendar.MILLISECOND);
        calendar.clear(Calendar.SECOND);
        calendar.clear(Calendar.MINUTE);
        Date startTime = calendar.getTime();

        long start = 0L;
        int size = 100;

        Long total = this.siteDs.getActiveSiteCount(null);
        if (total == null || total.longValue() < 1L) {
            log.warn("场站数量为空");
            return;
        }
        List<String> siteIdList = null;
        if (StringUtils.isBlank(siteId)) {
            //pass
            while (start < total) {
                log.debug("start = {}, size = {}", start, size);
                // 所有场站
                //List<String> siteIdList = siteDs.getSiteIdList(start, size);
                //ret = siteIdList.size();
                //log.info("site size = {}", siteIdList.size());

                this.sitesHourlyBi(siteIdList,
                    startTime.toInstant().atOffset(ZoneOffset.of("+8")).toLocalDateTime(),
                    type,
                    minElectricity,
                    maxElectricity,
                    start,
                    size);
                //List<Integer> result = collect.stream().map(CompletableFuture::join).collect(Collectors.toList());

                start = start + size;
            }
        } else {
            log.info("仅统计场站： {}", siteId);
            siteIdList = List.of(siteId);
            this.sitesHourlyBi(siteIdList,
                startTime.toInstant().atOffset(ZoneOffset.of("+8")).toLocalDateTime(),
                type,
                minElectricity,
                maxElectricity,
                null,
                null);
        }

//        // 单个场站处理
//        List<CompletableFuture<Integer>> collect = allSiteIdList.stream().map(
//                siteId -> CompletableFuture.supplyAsync(
//                        () -> this.siteHourlyBi(
//                                siteId,
//                                startTime.toInstant().atOffset(ZoneOffset.of("+8")).toLocalDateTime()), executor))
//                .collect(Collectors.toList());
//
//        List<Integer> result = collect.stream().map(CompletableFuture::join).collect(Collectors.toList());

//        List<Integer> result = allSiteIdList.parallelStream().map(siteId -> this.siteHourlyBi(
//                siteId,
//                startTime.toInstant().atOffset(ZoneOffset.of("+8")).toLocalDateTime()))
//                .collect(Collectors.toList());
        //       log.info("订单统计完成: date = {}, result = {}", date, result.stream().mapToInt(Integer::intValue).sum());
    }

    public void refreshSiteBiByOrderHourly(Date date, String siteId) {
        log.info("开始查找当前小时更新的订单，并刷新场站统计: {}", date);
        Date startTime = DateUtil.getThisHour(date);
        Date endTime = DateUtil.getNextHour(date);

        ChargerOrderParam chargerOrderParam = new ChargerOrderParam();
        chargerOrderParam.setUpdateTimeStart(startTime.toInstant().atOffset(ZoneOffset.of("+8")).toLocalDateTime());
        chargerOrderParam.setUpdateTimeEnd(endTime.toInstant().atOffset(ZoneOffset.of("+8")).toLocalDateTime());

//        chargerOrderParam.setMinElectricity(minElectricity);//NOTE 期望将0电量订单也统计在内，所以这里不设定最小值
        chargerOrderParam.setMaxElectricity(maxElectricity);

        if (StringUtils.isNotBlank(siteId)) {
            log.info("仅统计场站: {}", siteId);
            chargerOrderParam.setStationIds(List.of(siteId));
            this.statisticsBySite(chargerOrderParam);
        } else {
            // 获取所有场站列表遍历处理: [场站数量达到一定数量会有问题，若出现问题考虑调整成(union all)]
            long start = 0L;
            int size = 100;

            ListSiteParam param = new ListSiteParam();
            param.setSize(size);
            List<SitePo> siteList;
            do {
                param.setStart(start);
                final ListResponse<SitePo> siteListRes = this.siteDs.getSiteList(param);
                siteList = siteListRes.getData();
                start = start + siteList.size();
                if (CollectionUtils.isNotEmpty(siteList)) {
                    siteList.forEach(site -> {
                        chargerOrderParam.setStationIds(List.of(site.getId()));
                        this.statisticsBySite(chargerOrderParam);
                    });
                }
            } while (CollectionUtils.isNotEmpty(siteList));
        }
    }

    /**
     * 通过场站统计
     * @param chargerOrderParam
     */
    private void statisticsBySite(ChargerOrderParam chargerOrderParam) {
        List<ChargerOrder> orderList = chargerOrderDs.getBiChargerOrderDetailNonDivision(chargerOrderParam);

        if (CollectionUtils.isNotEmpty(orderList)) {
            Map<String, List<ChargerOrder>> siteMap = orderList.stream()
                    .collect(Collectors.groupingBy(ChargerOrder::getStationId, Collectors.toList()));

//            Map<String, List<ChargerOrder>> remedyMap =
//                    siteMap.values()
//                            .stream()
//                            .flatMap(List::stream)
//                            .collect(Collectors.toList())
//                            .stream()
//                            .collect(Collectors.groupingBy(ChargerOrder::getStationId, Collectors.toList()));

//            remedyMap.keySet()
            Map<String, Set<Date>> remedyDateMap = new HashMap<>();
            siteMap.keySet().stream().forEach(key -> {
                Set<Date> setDate = new HashSet<>();
                siteMap.get(key).stream().forEach(order -> {
                    if (order.getCreateTime() != null) {
                        setDate.add(DateUtil.getThisHour(order.getCreateTime()));
                    }
                    if (order.getPayTime() != null) {
                        setDate.add(DateUtil.getThisHour(order.getPayTime()));
                    }
                    if (order.getChargeStartTime() != null) {
                        setDate.add(DateUtil.getThisHour(new Date(Long.valueOf(order.getChargeStartTime()) * 1000)));
                    }
                    if (order.getChargeEndTime() != null) {
                        setDate.add(DateUtil.getThisHour(new Date(Long.valueOf(order.getChargeEndTime()) * 1000)));
                    }
                    if (order.getStopTime() != null) {
                        setDate.add(DateUtil.getThisHour(order.getStopTime()));
                    }
                });
                remedyDateMap.put(key, setDate);
            });

            this.siteHourlyBiRemedy(remedyDateMap, chargerOrderParam);

        }

    }

    /**
     * 场站数据补正
     *
     * @param remedyMap
     */
    private void siteHourlyBiRemedy(Map<String, Set<Date>> remedyMap,
        ChargerOrderParam chargerOrderParam) {
        log.info("场站订单修正: {}", JsonUtils.toJsonString(remedyMap));
        if (!remedyMap.isEmpty()) {
            List<String> siteIds = remedyMap.keySet()
                    .stream()
                    .filter(key -> CollectionUtils.isNotEmpty(remedyMap.get(key)))
                    .collect(Collectors.toList());
            //this.sitesHourlyBi(siteIds, )
            // 多场站one by one
            // TODO: 先放一放， 后面要改的
            int siteDateSum = siteIds.stream().map(siteId -> {
                List<CompletableFuture<Integer>> collect =
                    remedyMap.get(siteId)
                        .stream()
                        .map(date -> CompletableFuture.supplyAsync(
                            () -> this.sitesHourlyBi(List.of(siteId),
                                date.toInstant()
                                    .atOffset(ZoneOffset.of("+8")).toLocalDateTime(),
                                null,
                                chargerOrderParam.getMinElectricity(),
                                chargerOrderParam.getMaxElectricity(),
                                null,
                                null), executor))
                        .collect(Collectors.toList());

                // 单场站中并发计算
                List<Integer> result = collect.stream().map(CompletableFuture::join).collect(Collectors.toList());

                return result.stream().mapToInt(Integer::intValue).sum();
            }).mapToInt(Integer::intValue).sum();

            log.info("场站订单修正完成: result = {}", siteDateSum);
        }
    }


//    private Integer siteHourlyBi(String siteId, LocalDateTime time) {
//        final List<BiDependOnType> dependOnTypeList = List.of(CREATE_TIME,
//                PAY_TIME,
//                CHARGE_END_TIME,
//                CHARGE_START_TIME);
//
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//
//        List<Integer> result = dependOnTypeList.stream().map(dependOn -> {
//
//            // 统计场站的
//            ChargerOrderParam chargerOrderParam = new ChargerOrderParam();
////            chargerOrderParam.setStatusList(List.of(800, 2000));
//            switch (dependOn) {
//                case CREATE_TIME:
//                    chargerOrderParam.setCreateTimeFrom(time.format(formatter));
//                    chargerOrderParam.setCreateTimeTo(time.plusHours(1).format(formatter));
//                    break;
//                case PAY_TIME:
//                    chargerOrderParam.setPayTimeFrom(time.format(formatter));
//                    chargerOrderParam.setPayTimeTo(time.plusHours(1).format(formatter));
//                    break;
//                case CHARGE_END_TIME:
//                    chargerOrderParam.setChargeEndTimeFrom(String.valueOf(
//                            time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000));
//                    chargerOrderParam.setChargeEndTimeTo(String.valueOf(
//                            time.plusHours(1).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000));
//                    break;
//                case CHARGE_START_TIME:
//                    chargerOrderParam.setChargeStartTimeFrom(String.valueOf(
//                            time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000));
//                    chargerOrderParam.setChargeStartTimeTo(String.valueOf(
//                            time.plusHours(1).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000));
//                    break;
//
//            }
//
//            chargerOrderParam.setStationIds(List.of(siteId));
//            ChargerOrderDetailBiVo biVo = this.getChargerOrderDetail(chargerOrderParam);
//
//            // 有统计信息才入库
//            if (biVo != null &&
//                    biVo.getOrderNum() != null &&
//                    DecimalUtils.gtZero(biVo.getOrderNum().getTotal())) {
//                BiSiteOrderPo leaf = new BiSiteOrderPo();
//                leaf.setSiteId(siteId);
//                leaf.setTime(new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
//                leaf.setDependOn(dependOn);
//
//                Long orderCount = 0L;
//                try {
////                            orderCount = Long.valueOf(biVo.getOrderNum().getTotal());
//                    orderCount = biVo.getOrderNum().getTotal().longValue();
//                } catch (Exception e) {
//                    log.warn("无法转换订单数: {}", JsonUtils.toJsonString(biVo));
//                }
//                leaf.setOrderCount(orderCount);
//
//                leaf.setElecFee(biVo.getElecPriceAmount().getTotal());
//                leaf.setServFee(biVo.getServicePriceAmount().getTotal());
//                leaf.setFee(biVo.getOrderPriceAmount().getTotal());
//                leaf.setElectricity(biVo.getElectricityAmount().getTotal());
//                leaf.setElecTag1(biVo.getElectricityAmount().getSharp());
//                leaf.setElecTag2(biVo.getElectricityAmount().getPeak());
//                leaf.setElecTag3(biVo.getElectricityAmount().getFlat());
//                leaf.setElecTag4(biVo.getElectricityAmount().getValley());
//
//                biSiteOrderRwDs.insertOrUpdate(leaf);
//
//                return 1;
//            } else {
//                log.debug("场站{}在时间{}范围内无订单: {}",
//                        siteId,
//                        new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()),
//                        JsonUtils.toJsonString(biVo));
//            }
//
//            return 0;
//        }).collect(Collectors.toList());
//
//        return result.stream().mapToInt(i -> i).sum();
//    }

    private Integer sitesHourlyBi(List<String> siteIdList,
        LocalDateTime time,
        BiDependOnType type,
        Integer minElectricity,
        Integer maxElectricity,
        Long start,
        Integer size) {
        List<BiDependOnType> dependOnTypeList = new ArrayList<>();
        if (type != null) {
            dependOnTypeList = List.of(type);
        } else {
            dependOnTypeList = List.of(BiDependOnType.CREATE_TIME,
                    BiDependOnType.STOP_TIME,
                    BiDependOnType.PAY_TIME,
                    BiDependOnType.CHARGE_END_TIME,
                    BiDependOnType.CHARGE_START_TIME);
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        List<Integer> result = dependOnTypeList.stream().map(dependOn -> {

            // 统计场站的
            ChargerOrderParam chargerOrderParam = new ChargerOrderParam();

            chargerOrderParam.setMinElectricity(minElectricity);
            chargerOrderParam.setMaxElectricity(maxElectricity);

//            chargerOrderParam.setStatusList(List.of(800, 2000));
            switch (dependOn) {
                case CREATE_TIME:
                    chargerOrderParam.setCreateTimeFrom(time.format(formatter));
                    chargerOrderParam.setCreateTimeTo(time.plusHours(1).format(formatter));
                    break;
                case STOP_TIME:
                    chargerOrderParam.setStopTimeFrom(time.format(formatter));
                    chargerOrderParam.setStopTimeTo(time.plusHours(1).format(formatter));
                    break;
                case PAY_TIME:
                    chargerOrderParam.setPayTimeFrom(time.format(formatter));
                    chargerOrderParam.setPayTimeTo(time.plusHours(1).format(formatter));
                    break;
                case CHARGE_END_TIME:
                    chargerOrderParam.setChargeEndTimeFrom(String.valueOf(
                            time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000));
                    chargerOrderParam.setChargeEndTimeTo(String.valueOf(
                            time.plusHours(1).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000));
                    break;
                case CHARGE_START_TIME:
                    chargerOrderParam.setChargeStartTimeFrom(String.valueOf(
                            time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000));
                    chargerOrderParam.setChargeStartTimeTo(String.valueOf(
                            time.plusHours(1).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli() / 1000));
                    break;

            }
            if (start != null && size != null) {
                chargerOrderParam.setStart(start).setSize(size);
            } else {
                chargerOrderParam.setStationIds(siteIdList);
            }
            List<ChargerOrderDetailBiVo> siteDataList = this.getChargerOrderDetail(chargerOrderParam);
            siteDataList.stream().forEach(biVo -> {

                // 有统计信息才入库
                if (biVo != null &&
                        biVo.getOrderNum() != null &&
                        DecimalUtils.gtZero(biVo.getOrderNum().getTotal())) {
                    BiSiteOrderPo leaf = new BiSiteOrderPo();
                    leaf.setSiteId(biVo.getSiteId());
                    leaf.setTime(new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
                    leaf.setDependOn(dependOn);

                    Long orderCount = 0L;
                    try {
//                            orderCount = Long.valueOf(biVo.getOrderNum().getTotal());
                        orderCount = biVo.getOrderNum().getTotal().longValue();
                    } catch (Exception e) {
                        log.warn("无法转换订单数: {}", JsonUtils.toJsonString(biVo));
                    }
                    leaf.setOrderCount(orderCount);
                    leaf.setPower(biVo.getPower() == null ? 0L : biVo.getPower());
                    leaf.setElecFee(biVo.getElecPriceAmount().getTotal());
                    leaf.setServFee(biVo.getServicePriceAmount().getTotal());
                    leaf.setFee(biVo.getOrderPriceAmount().getTotal());
                    leaf.setElectricity(biVo.getElectricityAmount().getTotal());
                    leaf.setElecTag1(biVo.getElectricityAmount().getSharp());
                    leaf.setElecTag2(biVo.getElectricityAmount().getPeak());
                    leaf.setElecTag3(biVo.getElectricityAmount().getFlat());
                    leaf.setElecTag4(biVo.getElectricityAmount().getValley());

                    leaf.setNoAccountFee(biVo.getNoAccountFee())
                            .setPostSettlementFee(biVo.getPostSettlementFee())
                            .setPreSettlementFee(biVo.getPreSettlementFee())
                            .setCostFee(biVo.getCostFee())
                            .setFreeFee(biVo.getFreeFee());

                    if(DecimalUtils.lte(leaf.getElectricity(), BigDecimal.ZERO)) {
                        // 该时段无有效订单
                        ListBiSiteOrderParam param = new ListBiSiteOrderParam();
                        param.setSiteId(leaf.getSiteId())
                            .setDependOn(dependOn)
                            .setTime(leaf.getTime());
                        final BiSiteOrderPo biSiteOrder = biSiteOrderRoDs.getBiSiteOrder(param);
                        if(biSiteOrder != null) {
                            log.debug("删除统计记录: {}, {}", biSiteOrder.getId(),
                                biSiteOrderRwDs.deleteById(biSiteOrder.getId()));

                            int i = biSiteOrderAccountRwDs.deleteByBiSiteOrderId(leaf.getId());
                            log.info("删除各账户充电旧数据: {}", i);
                        } else {
                            log.debug("0订单，不需要处理");
                        }
                    } else {
                        biSiteOrderRwDs.insertOrUpdate(leaf);

                        // 各账户的统计信息
                        chargerOrderParam.setStationIds(List.of(biVo.getSiteId()));

                        int i = biSiteOrderAccountRwDs.deleteByBiSiteOrderId(leaf.getId());
                        log.info("删除各账户充电旧数据: {}", i);
                        this.accountOrderBi(chargerOrderParam, leaf.getId());
                    }

                    //return 1;
                } else {
                    log.debug("场站{}在时间{}范围内无订单: {}",
                            biVo.getSiteId(),
                            new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()),
                            JsonUtils.toJsonString(biVo));
                }

            });
            return siteDataList.size();
        }).collect(Collectors.toList());

        return result.stream().mapToInt(i -> i).sum();
    }

    public void accountOrderBi(ChargerOrderParam timeRange, Long biSiteOrderId) {

        try {
            // 查询数据
            List<BiSiteOrderAccountPo> poList = chargerOrderDs.getAccountOrderBi(timeRange);
            if (CollectionUtils.isEmpty(poList)) {
                return;
            }

            poList.forEach(po -> po.setBiSiteOrderId(biSiteOrderId));

            biSiteOrderAccountRwDs.batchInsertBiSiteOrderAccount(poList);

            // 互联企业客户统计信息
            Optional<BiSiteOrderAccountPo> first = poList.stream()
                    .filter(po -> SiteOrderAccountType.HLHT_CORP.equals(po.getAccountType()))
                    .findFirst();

            if (first.isPresent()) {
//                Long biAccountId = first.get().getId(); // 批量插入返回Id数据不准确
//                if (null == first.get().getId()) {
                BiSiteOrderAccountPo accountPo = biSiteOrderAccountRoDs.getByBiSiteOrderIdAndAccountType(
                        first.get().getBiSiteOrderId(), first.get().getAccountType());
                if (null == accountPo) {
                    return;
                }

                Long biAccountId = accountPo.getId();
//                }

                List<BiSiteOrderHlhtPo> hlhtPoList = chargerOrderDs.getHlhtAccountOrderBi(timeRange);
                if (CollectionUtils.isEmpty(hlhtPoList)) {
                    return;
                }

//                Long temBiAccountId = biAccountId;
                hlhtPoList.forEach(hlhtPo -> hlhtPo.setBiAccountId(biAccountId));
                biSiteOrderHlhtRwDs.batchInsertBiSiteOrderHlht(hlhtPoList);
            }
        } catch (Exception e) {
            log.error("统计场站账户消费异常: param = {}, biSiteOrderId = {}",
                    JsonUtils.toJsonString(timeRange), biSiteOrderId);
        }
    }

    private List<ChargerOrderDetailBiVo> getChargerOrderDetail(ChargerOrderParam chargerOrderParam) {

        final ObjectResponse<ChargerOrderDataBiVo> block = biSiteOrderFeignClient.getChargerOrderDetailData(
            chargerOrderParam)
            .block(Duration.ofSeconds(50L));

        if(block == null || block.getData() == null) {
            return List.of();
        }
        final ChargerOrderDataBiVo data = block.getData();
//        HashMap<String, Object> searchMap = this.chargerOrderParamToSearchMap(chargerOrderParam);
        List<ChargerOrderDataVo> list = data.getDetail();//chargerOrderDs.getChargerOrderDetail(chargerOrderParam); //尖峰平谷信息
        //
        List<ChargerOrderDataVo> siteDataList = data.getData();//chargerOrderDs.getChargerOrderData(chargerOrderParam); //汇总信息

//        List<ChargerOrderDataVo> sitePowerList = chargerOrderDs.getChargerPowerData(chargerOrderParam); // 功率
        List<ChargerOrderDataVo> sitePowerList = siteDs.getSitePower(chargerOrderParam);
        //log.info("尖峰平谷信息" + JsonUtils.toJsonString(list));
        //log.info("汇总信息信息" + JsonUtils.toJsonString(result));


        //汇总信息
        List<ChargerOrderDetailBiVo> resultList = siteDataList.stream()
                .map(this::toChargerOrderDetailBiVo)
                .collect(Collectors.toList());

        Map<String, Long> powerMap = sitePowerList.stream()
                .filter(o -> StringUtils.isNotBlank(o.getSiteId()))
                .collect(Collectors.toMap(ChargerOrderDataVo::getSiteId,
                        item -> item.getPower() == null ? 0L : item.getPower()));

        Map<String, ChargerOrderDetailBiVo> resultMap = resultList.stream()
                .collect(Collectors.toMap(ChargerOrderDetailBiVo::getSiteId, o -> o));
        list.forEach(o -> {
            ChargerOrderDetailBiVo siteData = resultMap.get(o.getSiteId());
            if (siteData == null) {
                log.info("场站信息有问题.... siteId = {}", o.getSiteId());
                return;
            }
            if (o.getTag().equals("1")) {
                siteData.getOrderNum().setSharp(this.formatData(o.getChargerOrderNumber()));
                siteData.getElectricityAmount().setSharp(this.formatData(o.getOrderElectricityAmount()));
                siteData.getOrderPriceAmount().setSharp(this.formatData(o.getOrderPriceAmount()));
                siteData.getElecPriceAmount().setSharp(this.formatData(o.getElecPriceAmount()));
                siteData.getServicePriceAmount().setSharp(this.formatData(o.getServicePriceAmount()));
            } else if (o.getTag().equals("2")) {
                siteData.getOrderNum().setPeak(this.formatData(o.getChargerOrderNumber()));
                siteData.getElectricityAmount().setPeak(this.formatData(o.getOrderElectricityAmount()));
                siteData.getOrderPriceAmount().setPeak(this.formatData(o.getOrderPriceAmount()));
                siteData.getElecPriceAmount().setPeak(this.formatData(o.getElecPriceAmount()));
                siteData.getServicePriceAmount().setPeak(this.formatData(o.getServicePriceAmount()));
            } else if (o.getTag().equals("3")) {
                siteData.getOrderNum().setFlat(this.formatData(o.getChargerOrderNumber()));
                siteData.getElectricityAmount().setFlat(this.formatData(o.getOrderElectricityAmount()));
                siteData.getOrderPriceAmount().setFlat(this.formatData(o.getOrderPriceAmount()));
                siteData.getElecPriceAmount().setFlat(this.formatData(o.getElecPriceAmount()));
                siteData.getServicePriceAmount().setFlat(this.formatData(o.getServicePriceAmount()));
            } else if (o.getTag().equals("4")) {
                siteData.getOrderNum().setValley(this.formatData(o.getChargerOrderNumber()));
                siteData.getElectricityAmount().setValley(this.formatData(o.getOrderElectricityAmount()));
                siteData.getOrderPriceAmount().setValley(this.formatData(o.getOrderPriceAmount()));
                siteData.getElecPriceAmount().setValley(this.formatData(o.getElecPriceAmount()));
                siteData.getServicePriceAmount().setValley(this.formatData(o.getServicePriceAmount()));
            }
            Long power = powerMap.get(o.getSiteId());
            if (power != null) {
                siteData.setPower(power);
            }
        });

        return resultList;
    }

    private ChargerOrderDetailBiVo toChargerOrderDetailBiVo(ChargerOrderDataVo siteSummary) {
        OrderPowerBiVo orderNum = new OrderPowerBiVo();
        OrderPowerBiVo electricityAmount = new OrderPowerBiVo();
        OrderPowerBiVo orderPriceAmount = new OrderPowerBiVo();
        OrderPowerBiVo elecPriceAmount = new OrderPowerBiVo();
        OrderPowerBiVo servicePriceAmount = new OrderPowerBiVo();
        orderNum.setTotal(formatData(siteSummary.getChargerOrderNumber()));
        electricityAmount.setTotal(this.formatData(siteSummary.getOrderElectricityAmount()));
        orderPriceAmount.setTotal(this.formatData(siteSummary.getOrderPriceAmount()));
        elecPriceAmount.setTotal(this.formatData(siteSummary.getElecPriceAmount()));
        servicePriceAmount.setTotal(this.formatData(siteSummary.getServicePriceAmount()));


        orderNum.setPeak(orderNum.getPeak());
        orderNum.setTotal(orderNum.getTotal());
        orderNum.setValley(orderNum.getValley());
        orderNum.setFlat(orderNum.getFlat());
        orderNum.setSharp(orderNum.getSharp());
        electricityAmount.setSharp(electricityAmount.getSharp());
        electricityAmount.setFlat(electricityAmount.getFlat());
        electricityAmount.setPeak(electricityAmount.getPeak());
        electricityAmount.setValley(electricityAmount.getValley());

        ChargerOrderDetailBiVo chargerOrderDetailBiVo = new ChargerOrderDetailBiVo();
        chargerOrderDetailBiVo.setSiteId(siteSummary.getSiteId());
        chargerOrderDetailBiVo.setOrderNum(orderNum);
        chargerOrderDetailBiVo.setElecPriceAmount(elecPriceAmount);
        chargerOrderDetailBiVo.setElectricityAmount(electricityAmount);
        chargerOrderDetailBiVo.setOrderPriceAmount(orderPriceAmount);
        chargerOrderDetailBiVo.setServicePriceAmount(servicePriceAmount);
        chargerOrderDetailBiVo.setNoAccountFee(siteSummary.getNoAccountFee())
                .setPostSettlementFee(siteSummary.getPostSettlementFee())
                .setPreSettlementFee(siteSummary.getPreSettlementFee())
                .setCostFee(siteSummary.getCostFee())
                .setFreeFee(siteSummary.getFreeFee());

        return chargerOrderDetailBiVo;
    }

    private BigDecimal formatData(BigDecimal in) {
        return in == null ? BigDecimal.ZERO : in;
    }

    private BigDecimal formatData(Long in) {
        return in == null ? BigDecimal.ZERO : BigDecimal.valueOf(in);
    }

    public SiteDailyBiPo getSiteDailyBi(String siteId, Date date) {
        return this.siteDailyBiRoDs.getSiteDailyBi(siteId, date);
    }

    /**
     * 查询是否在工作（计算）状态
     */
    public Boolean getWorkingStatus() {
        return this.isWorking;
    }

    public Boolean getWorkingDailyAccountingStatus() {
        return this.isWorkingDailyAccounting;
    }

    /**
     * 统计(国充)场站每日运营收入
     *
     * @param siteIds
     * @param strStartDate
     * @param strEndDate
     */
    @Async
    public void accountSiteDailyFee(@Nullable List<String> siteIds,
                                       @Nullable String strStartDate,
                                       @Nullable String strEndDate) {
        log.info("统计场站日收入 siteIds = {}, strStartDate = {}, strEndDate = {}",
                siteIds, strStartDate, strEndDate);
        if (isWorkingDailyAccounting) {
            log.warn("正在处理中....忽略重复执行动作");
            return;
        }
        isWorkingDailyAccounting = true;
        try {
            List<Integer> gcTypes = authCenterFeignClient.getGcTypes()
                    .getData();

            List<String> siteIdsX = siteIds;
            if (CollectionUtils.isEmpty(siteIdsX)) {
                siteIdsX = siteDs.getSiteIdList(0, 9999, gcTypes);
            }
            if (StringUtils.isNotBlank(strStartDate) && StringUtils.isNotBlank(strEndDate)) {
                LocalDate startDate = LocalDate.parse(strStartDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                LocalDate endDate = LocalDate.parse(strEndDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                while (!startDate.isAfter(endDate)) {
                    this.accountSiteDailyIncomeDayLoop(siteIdsX, startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    startDate = startDate.plusDays(1);
                }
            } else if (StringUtils.isBlank(strEndDate)) {
                this.accountSiteDailyIncomeDayLoop(siteIdsX, strStartDate);
            } else {
                log.error("参数错误. strStartDate = {}, strEndDate = {}", strStartDate, strEndDate);
                throw new DcArgumentException("参数错误,缺少开始日期");
            }
        } catch (Exception e) {
            log.error("统计场站日收入失败 error = {}", e.getMessage());
        } finally {
            isWorkingDailyAccounting = false;
        }

        log.info("<< 统计场站日收入 siteIds = {}, strStartDate = {}, strEndDate = {}",
                siteIds, strStartDate, strEndDate);
    }

    private void accountSiteDailyIncomeDayLoop(@Nullable List<String> siteIds,
                                               @Nullable String strDate) {
        log.info("siteIds = {}, strDate = {}", siteIds, strDate);
        for (String siteId : siteIds) {
            try {
                ObjectResponse<BiSiteGcDailyPo> res = this.biSiteOrderFeignClient.accountSiteDailyIncome(siteId, strDate)
                        .block(Duration.ofSeconds(50L));
                log.debug("data = {}", res.getData());
                if (res.getData() != null) {
                    this.biSiteGcDailyRwDs.addBiSiteGcDaily(res.getData());
                }
            } catch (Exception e) {
                log.error("统计场站日运营收入失败 siteId = {}, date = {}", siteId, strDate, e);
            }
        }
    }


    /**
     * 计算场站收入，计算后将结果入库
     */
    @Async

    public void accountSiteAccountIncome(@Nullable List<String> siteIds,
                                         @Nullable Integer year,
                                         @Nullable Integer month) {
        log.info("统计场站收入 {}-{}, siteIds = {}", year, month, siteIds);
        if (isWorking) {
            log.warn("正在处理中....忽略重复执行动作");
            return;
        }
        isWorking = true;
        try {
            Calendar calNow = Calendar.getInstance();
            final int yearX = year == null ? calNow.get(Calendar.YEAR) : year;
            final int monthX = month == null ? calNow.get(Calendar.MONTH) + 1 : month;
            long start = 0L;
            int size = 100;
            int siteNum = 999;
            ListSiteParam param = new ListSiteParam();
            param.setGcTypeList(List.of(1, 3, 6, 7, 9, 10, 12))   // 国充场站类型
                    .setTopCommId(34474L)
                    .setStart(start)
                    .setSize(size)
                    .setTotal(false);
            if (CollectionUtils.isNotEmpty(siteIds)) {
                param.setSiteIdList(siteIds);
            }

            while (siteNum >= size) {
                ListResponse<SitePo> sites = this.siteDs.getSiteList(param);
                siteNum = sites.getData().size();
                start = start + siteNum;
                param.setStart(start);

                for (SitePo s : sites.getData()) {
                    AccountSiteIncomeParam accParam = new AccountSiteIncomeParam();
                    accParam.setYear(yearX)
                            .setMonth(monthX)
                            .setSiteId(s.getId());
                    var incomeRes = biSiteOrderFeignClient.accountSiteIncome(accParam)
                            .block(Duration.ofSeconds(50L));
                    SiteProfitDto income = incomeRes.getData();
                    if (income != null && CollectionUtils.isNotEmpty(income.getAccProfits())) {
                        log.info("income = {}", income);
//                        incomeRes.getData().getAccProfits();
                        income.getAccProfits().stream().forEach(i -> biSiteAccountIncomeRwDs.addFromSiteAccountProfitDto(i));
                    }
                }
            }
            this.accountSiteCorpIncome(yearX, monthX);
        } catch (Exception e) {
            log.error("统计场站收入失败 error = {}", e.getMessage());
        } finally {
            isWorking = false;
        }

        log.info("<< 统计场站收入 {}-{}, siteIds = {}", year, month, siteIds);
    }

    private void accountSiteCorpIncome(int year, int month) {
        ListSettlementParam settParam = new ListSettlementParam();
        LocalDate startDate = LocalDate.of(year, month, 1);
        settParam.setSettDate(new TimeFilter());
        settParam.getSettDate().setStartTime(DateUtil.localDateToDate(startDate))
                .setEndTime(DateUtil.localDateToDate(startDate.plusMonths(1)));
        long start = 0L;
        int size = 100;
        int settNum = 999;

        settParam.setCommIdChain("34474")
                .setStatusList(List.of(SettlementStatusEnum.INIT, SettlementStatusEnum.PAID))
                .setCorpType(CorpType.PLATFORM)
                .setStart(start)
                .setSize(size)
                .setTotal(false);
        while (settNum >= size) {
            ListResponse<SettlementVo> settListRes = this.settlementFeignClient.findSettlementList(settParam)
                .block(Duration.ofSeconds(50L));
            settNum = settListRes.getData().size();
            start = start + settNum;
            settParam.setStart(start);

            for (SettlementVo sett : settListRes.getData()) {
                log.info("settlement = {} - {}", sett.getCorpName(), sett.getBillNo());
                ListResponse<SiteAccountProfitDto> res = biSiteOrderFeignClient.accountSitePostPayCorpIncome(sett)
                        .block(Duration.ofSeconds(50L));

                res.getData().stream().forEach(i -> biSiteAccountIncomeRwDs.addFromSiteAccountProfitDto(i));
            }
        }
    }
}