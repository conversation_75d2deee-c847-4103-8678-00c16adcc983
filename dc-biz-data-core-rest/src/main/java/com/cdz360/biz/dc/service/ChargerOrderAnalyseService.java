package com.cdz360.biz.dc.service;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.biz.dc.domain.OrderInMongo;
import com.cdz360.biz.dc.repository.ChargerOrderRepository;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderRoDs;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderRwDs;
import com.cdz360.biz.model.trading.order.po.ChargerOrderPo;
import com.cdz360.biz.utils.feign.order.BiOrderFeignClient;
import com.chargerlinkcar.framework.common.domain.vo.ChargerDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
public class ChargerOrderAnalyseService {

    @Autowired
    private ChargerOrderRepository chargerOrderRepository;

    @Autowired
    private ChargerOrderRoDs chargerOrderRoDs;

    @Autowired
    private ChargerOrderRwDs chargerOrderRwDs;

    @Autowired
    private BiOrderFeignClient biOrderFeignClient;

//    private static final BigDecimal VOLTAGE_TAG = BigDecimal.valueOf(300);

    /**
     * 充电功率阈值. 计算模型为:假设单个模块输出功率为15kw，输出功率低于40%认为点损较大
     */
    @Value("${dc.biz.analyse.charge.kw.min:6}")
    private BigDecimal KW_THRESHOLD;


    public void analyseOrders() {
        int size = 50;
        int times = 1000;    // 最大循环1000次
        this.analyseOrderLoop(size, times)
                .block(Duration.ofMinutes(10L));
    }

    private Mono<Boolean> analyseOrderLoop(final int size, final int times) {
        log.info("size = {}, times = {}", size, times);
        return this.biOrderFeignClient.list4LowKwAnalyse(size)
                .map(res -> {
                    if (CollectionUtils.isEmpty(res.getData())) {
                        return Pair.of(0, times - 1);
                    }
                    log.info("times = {}, orderNoList.size = {}", times, res.getData().size());
                    List<ChargerOrderPo> resultList = res.getData().stream()
                            .map(orderNo -> analyseOrder(orderNo))
                            .collect(Collectors.toList());
                    this.chargerOrderRwDs.batchUpdateOrder(resultList);
                    return Pair.of(res.getData().size(), times - 1);
                })
                .delayElement(Duration.ofSeconds(2L))   // 延迟2秒,用于数据库的主从同步
                .flatMap(p -> {
                    if (p.getFirst() >= size && p.getSecond() > 0) {
                        return analyseOrderLoop(p.getFirst(), p.getSecond());
                    } else {
                        log.info("loop end size = {}, times = {}", p.getFirst(), p.getSecond());
                        return Mono.just(true);
                    }
                });


    }

    public ChargerOrderPo analyseOrder(String orderNo) {
        log.debug("orderNo = {}", orderNo);

        OrderInMongo order = this.chargerOrderRepository.getByOrderNo(orderNo);

        int lowKwDur = (int) this.getLowKwDuration(order);
        ChargerOrderPo ret = new ChargerOrderPo();
        ret.setOrderNo(orderNo)
                .setLowKwDur(lowKwDur);
        return ret;
    }


    /**
     * 查询电流小于阈值的时长
     *
     * @param order
     * @return
     */
    private long getLowKwDuration(OrderInMongo order) {
        log.debug("details.size = {}",
                order == null || order.getDetails() == null ? null : order.getDetails().size());
        if (order == null || order.getDetails() == null) {
            return 0L;
        }
        List<ChargerDetail> sortedList = order.getDetails().stream()
                .sorted((a, b) -> NumberUtils.compareLong(a.getTimestamp(), b.getTimestamp()))
                .collect(Collectors.toList());
        ChargerDetail lastStart = null;
        long totalDur = 0;
        long lastDur = 0;
        for (ChargerDetail dt : sortedList) {
            if (dt.getTimestamp() == null) continue;
            Boolean matchRet = isChargerDetailMatch(dt);
            if (matchRet == null) continue;
            else if (Boolean.TRUE.equals(matchRet)) {
                if (lastStart == null) {
                    lastStart = dt;
                    lastDur = 0;
                } else {
                    lastDur = dt.getTimestamp() - lastStart.getTimestamp();
                }
            } else {
                if (lastStart != null) {
                    lastStart = null;
                    totalDur += lastDur;
                    lastDur = 0;
                }
            }
//            log.debug("time = {}, current = {}, lastDur = {}",
//                    dt.getTimestamp(), dt.getDcCurrentO(), lastDur);
        }
        if (lastDur > 0) {
            totalDur += lastDur;
        }
        log.debug("orderNo = {}, dur = {}", order.getOrderNo(), totalDur);
        return totalDur;
    }

    /**
     * 是否符合电损标准
     *
     * @param dt
     * @return
     */
    private Boolean isChargerDetailMatch(ChargerDetail dt) {
        if (dt.getDcVoltageO() == null || DecimalUtils.isZero(dt.getDcVoltageO())
                || dt.getDcCurrentO() == null
                || dt.getPower() == null) {
            return null;
        }
        return DecimalUtils.lt(dt.getPower(), KW_THRESHOLD);
    }

//    private void analyseOrder(OrderInMongo order) {
//        log.info("orderNo = {}", order.getOrderNo());
//    }
}
