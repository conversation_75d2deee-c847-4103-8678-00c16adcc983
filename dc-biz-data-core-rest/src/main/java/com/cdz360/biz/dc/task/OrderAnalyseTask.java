package com.cdz360.biz.dc.task;

import com.cdz360.biz.dc.service.ChargerOrderAnalyseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class OrderAnalyseTask {
    @Autowired
    private ChargerOrderAnalyseService chargerOrderAnalyseService;

    @Scheduled(initialDelay = 3000, fixedRate = 60 * 1000 * 10)
    public void analyseLowKwOrder() {
        this.chargerOrderAnalyseService.analyseOrders();
    }
}
