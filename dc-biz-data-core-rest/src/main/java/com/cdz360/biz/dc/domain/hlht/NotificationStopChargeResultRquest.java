package com.cdz360.biz.dc.domain.hlht;
//
//import com.alibaba.fastjson.annotation.JSONField;
//import com.fasterxml.jackson.annotation.JsonProperty;
//import lombok.Data;
//
//import java.io.Serializable;
//
///**
// * <AUTHOR>
// *  推送停止充电结果
// * @since 2019-04-26 10:07
// */
//@Data
//public class NotificationStopChargeResultRquest implements Serializable{
//
//    /**
//     * 运营商ID
//     */
//    @JSONField(name = "OperatorID")
//    @JsonProperty(value = "OperatorID")
//    private String operatorId;
//
//    /**
//     * 充电订单号
//     */
//    @JSONField(name = "StartChargeSeq")
//    @JsonProperty(value = "StartChargeSeq")
//    private String startChargeSeq;
//
//    /**
//     * 充电订单状态
//     */
//    @JSONField(name = "StartChargeSeqStat")
//    @JsonProperty(value = "StartChargeSeqStat")
//    private Integer startChargeSeqStat;
//    /**
//     * 充电设备接口编码
//     * */
//    @JSONField(name = "ConnectorID")
//    @JsonProperty(value = "ConnectorID")
//    private String connectorID;
//    /**
//     * 操作结果
//     */
//    @JSONField(name = "SuccStat")
//    @JsonProperty(value = "SuccStat")
//    private Integer succStat;
//
//    /**
//     * 失败原因
//     */
//    @JSONField(name = "FailReason")
//    @JsonProperty(value = "FailReason")
//    private Integer failReason;
//}
