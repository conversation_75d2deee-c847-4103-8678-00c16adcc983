package com.cdz360.biz.dc.rest.ess;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.dc.service.ess.EssDailyTimelyService;
import com.cdz360.biz.ess.model.data.po.EssEquipTimelyPo;
import com.cdz360.biz.model.ess.param.ListEssDailyParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

//@Tag(name = "储能设备数据", description = "储能设备数据")
//@Slf4j
//@RestController
//@RequestMapping("/dataCore/ess/daily")
//public class EssDailyRest {
//
//    @Autowired
//    private EssDailyTimelyService essDailyTimelyService;
//
//    @Operation(summary = "储能设备分时段明细数据")
//    @PostMapping("/timelyDataList")
//    public Mono<ListResponse<EssEquipTimelyPo>> fetchEssTimelyData(
//        @RequestBody ListEssDailyParam param) {
//        log.info("储能设备分时段明细数据: {}", param);
//        return essDailyTimelyService.fetchEssTimelyData(param);
//    }
//xxx
//}
