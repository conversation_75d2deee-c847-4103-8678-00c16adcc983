package com.cdz360.biz.dc.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.dc.service.OssArchiveBizService;
import com.cdz360.biz.dc.service.OssBizService;
import com.cdz360.biz.model.oss.OssStsDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@Tag(name = "文件上传", description = "文件上传")
public class OssController {

    @Autowired
    private OssBizService ossBizService;
    @Autowired
    private OssArchiveBizService ossArchiveBizService;

    @Operation(summary = "获取文件上传的STS信息")
    @GetMapping(value = "/dataCore/oss/getSts")
    public ObjectResponse<OssStsDto> getSts() {
        OssStsDto sts = this.ossBizService.getSts();
        return RestUtils.buildObjectResponse(sts);
    }

    @Operation(summary = "获取文件上传的STS信息")
    @GetMapping(value = "/dataCore/oss/getPrivateSts")
    public ObjectResponse<OssStsDto> getPrivateSts() {
        OssStsDto sts = this.ossBizService.getPrivateSts();
        return RestUtils.buildObjectResponse(sts);
    }


    @Operation(summary = "获取文件上传的STS信息")
    @GetMapping(value = "/dataCore/oss/getArchiveSts")
    public ObjectResponse<OssStsDto> getArchiveSts() {
        OssStsDto sts = this.ossArchiveBizService.getSts();
        return RestUtils.buildObjectResponse(sts);
    }
}
