package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.biz.dc.client.UserFeignClient;
import com.cdz360.biz.ds.trading.rw.order.ds.OrderUserCommRefRwDs;
import com.chargerlinkcar.framework.common.domain.UserCommRef;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrder;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SyncUserCommRefService {

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private OrderUserCommRefRwDs orderUserCommRefRwDs;

    public void insertUserCommRef(ChargerOrder order) {
        try {
            // 查询条件中已添加约束
//            if (order.getDeviceCommercialId() == null ||
//                    order.getCustomerId() == null) {
//                return;
//            }
            if (order.getCustomerId() == null || order.getCustomerId() < 1L) {
                log.info(
                    "订单的cusId为空,不记录充电客户与商户的关联关系. orderNo= {}, order.cusId= {}",
                    order.getOrderNo(), order.getCustomerId());
                return;
            }
            UserCommRef userCommRef = new UserCommRef();
            userCommRef.setCommId(order.getDeviceCommercialId());
            userCommRef.setUid(order.getCustomerId());
            userCommRef.setCreateTime(new Date());
            userCommRef.setUpdateTime(new Date());
            userCommRef.setOpId(0L);
            userCommRef.setOpName("charge");
            BaseResponse res = this.userFeignClient.addUserCommRef(userCommRef);
            FeignResponseValidate.check(res);

            // 追加已处理的记录
            orderUserCommRefRwDs.batchInsert(List.of(order.getOrderNo()));
        } catch (Exception e) {
            log.error("添加商户关系异常: err = {}", e.getMessage(), e);
        }
    }
}
