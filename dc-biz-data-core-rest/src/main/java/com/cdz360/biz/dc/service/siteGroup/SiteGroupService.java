package com.cdz360.biz.dc.service.siteGroup;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.domain.vo.SiteGroupMapVo;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteGroupRoDs;
import com.cdz360.biz.ds.trading.rw.site.ds.SiteGroupSiteRwDs;
import com.cdz360.biz.model.sys.constant.SiteGroupType;
import com.cdz360.biz.model.sys.param.ListSiteGroupParam;
import com.cdz360.biz.model.sys.param.SiteGroupSiteParam;
import com.cdz360.biz.model.sys.param.UpdateSiteGroupSiteParam;
import com.cdz360.biz.model.sys.po.SiteGroupPo;
import com.cdz360.biz.model.sys.vo.SiteGroupSiteInfoVo;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.po.SiteGroupRefPo;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import com.cdz360.biz.utils.feign.auth.AuthSiteGroupFeignClient;
import com.cdz360.data.sync.model.SiteGroup;
import com.cdz360.data.sync.service.DcEventPublisher;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Service
public class SiteGroupService {

    @Autowired
    private DcEventPublisher dcEventPublisher;

    @Autowired
    private SiteGroupSiteRwDs siteGroupSiteRwDs;

    @Autowired
    private SiteGroupRoDs siteGroupRoDs;

    @Autowired
    private AuthSiteGroupFeignClient authSiteGroupFeignClient;

    public Mono<Optional<SiteGroupMapVo>> getSiteGroupMapVo(@NonNull List<String> siteIdList) {
        if (CollectionUtils.isEmpty(siteIdList)) {
            return Mono.just(Optional.empty());
        }
        SiteGroupMapVo resData = new SiteGroupMapVo();

        return Mono.just(siteIdList.stream()
                .filter(StringUtils::isNotBlank).distinct()
                .collect(Collectors.toList()))
            .filter(CollectionUtils::isNotEmpty)
            .map(i -> siteGroupRoDs.getSiteGroupRefList(i)) // 获取场站对应的组
            .filter(CollectionUtils::isNotEmpty)
            .map(refList -> {
                resData.setSiteGidMap(refList.stream().collect(
                    Collectors.groupingBy(SiteGroupRefPo::getSiteId,
                        Collectors.mapping(SiteGroupRefPo::getGid, Collectors.toList()))));
                return refList.stream().map(SiteGroupRefPo::getGid).distinct()
                    .collect(Collectors.toList());
            })
            .filter(CollectionUtils::isNotEmpty)
            .map(gidList -> {
                ListSiteGroupParam req = new ListSiteGroupParam();
                req.setGidList(gidList)
                    .setTypeList(List.of(SiteGroupType.YW));
                req.setEnable(true);
                return req;
            })
            .flatMap(s -> authSiteGroupFeignClient.findSiteGroup(s)) // 获取场站组列表
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .map(t -> {
                resData.setGroupMap(t.stream().collect(Collectors.toMap(
                    SiteGroupPo::getGid, SiteGroupPo::getName,
                    (v1, v2) -> v1)));
                return Optional.of(resData);
            })
            .switchIfEmpty(Mono.just(Optional.empty()));
    }

    public Set<String> findSiteGroupSiteBySiteId(SiteGroupSiteParam param) {
        return siteGroupRoDs.findSiteGroupSiteBySiteId(param);
    }

    public List<SiteGroupSiteInfoVo> findSiteGroupSiteInfo(SiteGroupSiteParam param) {
        return siteGroupRoDs.findSiteGroupSiteInfo(param);
    }

    public List<SiteVo> getSiteListByGidList(SiteGroupSiteParam param) {
        return siteGroupRoDs.getSiteListByGidList(param);
    }

    public SiteGroupSiteInfoVo updateSiteGroupSiteRef(UpdateSiteGroupSiteParam param) {
        IotAssert.isNotBlank(param.getGid(), "场站组ID无效");

        int i;

        // 仅做追加操作
        if (CollectionUtils.isNotEmpty(param.getOnlyAppendSiteIdList())) {
            i = siteGroupSiteRwDs.batchInsert(param.getGid(), param.getOnlyAppendSiteIdList());

            val siteIdList = siteGroupRoDs.getSiteListByGids(
                new ListSiteParam().setGids(List.of(param.getGid())));

            // 同步数据到IOT
            dcEventPublisher.publishSiteGroupInfo(new SiteGroup()
                .setGid(param.getGid())
                .setSiteIdList(siteIdList));

            return siteGroupRoDs.getSiteGroupSiteInfoByGid(param.getGid());
        }

        List<String> siteIdList = Stream.concat(param.getDirectSiteIdList().stream(),
                param.getOfflineSiteIdList().stream())
            .distinct()
            .collect(Collectors.toList());

        // 删除旧数据
        if (CollectionUtils.isNotEmpty(siteIdList)) {
            i = siteGroupSiteRwDs.batchDelete(
                param.getGid(), siteIdList);

            // 增加新数据
            i = siteGroupSiteRwDs.batchInsert(param.getGid(), siteIdList);
        } else {
            i = siteGroupSiteRwDs.deleteByGid(param.getGid());
        }

        // 同步数据到IOT
        dcEventPublisher.publishSiteGroupInfo(new SiteGroup()
            .setGid(param.getGid())
            .setSiteIdList(siteIdList));

        return siteGroupRoDs.getSiteGroupSiteInfoByGid(param.getGid());
    }

    public Long getSiteAmountByGidList(SiteGroupSiteParam param) {
        return siteGroupRoDs.getSiteAmountByGidList(param);
    }
}
