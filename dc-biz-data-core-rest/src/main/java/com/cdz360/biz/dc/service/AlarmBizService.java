package com.cdz360.biz.dc.service;

import com.cdz360.biz.dc.repository.WarningRecordRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 告警数据service层接口实现类
 *
 * <AUTHOR>
 * Create on 2018.8.9 14:48
 */
@Slf4j
@Service
public class AlarmBizService //implements AlarmService
{
    @Autowired
    private WarningRecordRepository wWarningRecordRepository;

    @Autowired
    private AlarmService wWarningRecordService;

    private static final String ALARM_SYS_LEVE_PHONE_DICT_TYPE = "alarmSysLevePhone";

    private static final String ALARM_TRANS_FINSH_PHONE_DICT_TYPE = "alarmTranFinishPhone";






}