package com.cdz360.biz.dc.config;

import com.cdz360.biz.model.common.constant.Constant;
import com.cdz360.data.sync.constant.DcMqConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class MqListenerConfig {

    @Bean
    public Queue syncInfoQueue() {
        return new Queue(Constant.MQ_QUEUE_DATA_CORE_SYNC_INFO, true, false, false);
    }

    @Bean
    DirectExchange exchangeSyncInfo() {
        return new DirectExchange(DcMqConstants.MQ_EXCHANGE_NAME_SYNC, true, false);
    }

    @Bean
    Binding bindingExchangeSyncInfo(@Qualifier("syncInfoQueue") Queue syncInfoQueue,
        @Qualifier("exchangeSyncInfo") DirectExchange exchangeSyncInfo) {

        return BindingBuilder.bind(syncInfoQueue).to(exchangeSyncInfo)
            .with(DcMqConstants.MQ_ROUTING_KEY_SYNC);
    }


    @Bean
    public Queue iotQueue() {
        return new Queue(Constant.MQ_QUEUE_DATA_CORE_IOT, true, false, true);
    }

    @Bean
    DirectExchange exchangeIot() {
        return new DirectExchange(DcMqConstants.MQ_EXCHANGE_NAME_IOT, true, true);
    }

    @Bean
    Binding bindingExchangeIot(@Qualifier("iotQueue") Queue iotQueue,
        @Qualifier("exchangeIot") DirectExchange exchangeIot) {

        return BindingBuilder.bind(iotQueue).to(exchangeIot).with(DcMqConstants.MQ_ROUTING_KEY_IOT);
    }
}
