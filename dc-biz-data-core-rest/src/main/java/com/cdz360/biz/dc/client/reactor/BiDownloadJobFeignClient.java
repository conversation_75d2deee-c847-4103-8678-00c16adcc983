package com.cdz360.biz.dc.client.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.biz.model.download.dto.DownloadJobDto;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_BI,
    fallbackFactory = BiDownloadJobFeignHystrix.class)
public interface BiDownloadJobFeignClient {

    // 文件生成
    @PostMapping(value = "/bi/download/generateFile")
    Mono<BaseResponse> generateFile(@RequestBody DownloadJobDto downloadJobDto);

    // 打印文件生成
    @PostMapping(value = "/bi/download/generatePrintFile")
    Mono<BaseResponse> generatePrintFile(@RequestBody DownloadJobDto downloadJobDto);

    // 生成文件清理
    @GetMapping(value = "/bi/download/clearFile")
    Mono<BaseResponse> clearDownloadFile(@RequestParam("days") Integer days);
}
