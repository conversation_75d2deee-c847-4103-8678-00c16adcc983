package com.cdz360.biz.dc.client.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.iot.param.ListCtrlParam;
import com.cdz360.biz.model.iot.vo.GwInfoVo;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

/**
 * ReactorUserFeignClientHystrixFactory
 *
 * @since 11/3/2020 10:32 AM
 * <AUTHOR>
 */
@Slf4j
@Component
public class ReactorIotPvFeignClientHystrixFactory implements
    FallbackFactory<ReactorIotPvFeignClient> {

    @Override
    public ReactorIotPvFeignClient apply(Throwable throwable) {
        return new ReactorIotPvFeignClient() {
            @Override
            public Mono<ListResponse<GwInfoVo>> findCtrlList(ListCtrlParam param) {
                log.error(
                    "【服务熔断】 获取场站控制器列表. Service = {}, api = findCtrlList. param = {}",
                    DcConstants.KEY_FEIGN_IOT_PV, param);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }
        };
    }

    @Override
    public <V> Function<V, ReactorIotPvFeignClient> compose(
        Function<? super V, ? extends Throwable> before) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER);
        return null;
    }

    @Override
    public <V> Function<Throwable, V> andThen(
        Function<? super ReactorIotPvFeignClient, ? extends V> after) {
        log.error("【服务熔断】。Service = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER);
        return null;
    }
}