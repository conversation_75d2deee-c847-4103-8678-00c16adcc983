package com.cdz360.biz.dc.domain.erp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "ERP登录请求参数")
public class ErpLoginRequest {
    @Schema(description = "登录的用户名")
    private String username;
    @Schema(description = "登录的密码")
    private String password;
    @Schema(description = "登录账套Id")
    private String acctID;
    @Schema(description = "语言ID: 中文2052，英文1033，繁体3076")
    private Integer lcid;
}
