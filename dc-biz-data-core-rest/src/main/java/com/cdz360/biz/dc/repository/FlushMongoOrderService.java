package com.cdz360.biz.dc.repository;

import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.dc.domain.OrderInMongo;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderRoDs;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrder;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.model.Sorts;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * FlushMongoOrderService
 *
 * @since 4/2/2022 4:46 PM
 * <AUTHOR>
 */
@Slf4j
@Repository
public class FlushMongoOrderService {

    @Autowired
    private MongoTemplate template;

    @Autowired
    private ChargerOrderRoDs chargerOrderRoDs;

    private AtomicBoolean runFlag = new AtomicBoolean(false);
    private int runSkip = 0;

    @Async
    public void orderFlushSiteInfo(int size, int skip) {
        synchronized (runFlag) {
            if (runFlag.get()) {
                log.error("正在执行中");
                return;
            }
            runFlag.set(true);
        }
        if(skip >= 0) {
            runSkip = skip;
        }
//        Query q = new Query();
//        long order_info_in_time = template.count(q, "order_info_in_time");

        Date d1 = new Date();
        while(runFlag.get()) {
            FindIterable<Document> itDoc = template.getCollection("order_info_in_time")
                    .find()
                    .sort(Sorts.ascending("_id"))
                    .skip(runSkip)
                    .limit(size);
            MongoCursor<Document> it = itDoc.iterator();
            if(!it.hasNext()) {
                log.info("洗数据执行完成skip: {}", runSkip);
                runFlag.set(false);
                return;
            }
            while (it.hasNext()) {
                Document next = it.next();
                String orderNo = next.getString("orderNo");
                if ((StringUtils.isBlank(next.getString("siteId")) ||
                        StringUtils.isBlank(next.getString("siteName"))) &&
                        StringUtils.isNotBlank(next.getString("orderNo"))) {
                    log.debug("需要更新场站信息: {}", orderNo);
                    ChargerOrder byOrderNo = chargerOrderRoDs.findByOrderNo(orderNo);
                    if(byOrderNo != null &&
                            StringUtils.isNotBlank(byOrderNo.getStationId()) &&
                            StringUtils.isNotBlank(byOrderNo.getStationName())) {
                        this.update(orderNo, byOrderNo.getStationId(), byOrderNo.getStationName());
                    }
                }
            }
            runSkip += size;
            log.info("洗数据 skip: {}", runSkip);
        }
        Date d2 = new Date();
        log.info("洗数据执行停止skip: {}, duration: {}", runSkip, d2.getTime() - d1.getTime());
        runFlag.set(false);

//        log.info("db count: {}", order_info_in_time);
        return;
    }

    public String getSkipInfo() {
        return "skip: " + runSkip + ", runFlag: " + runFlag.get();
    }

    public void setRunFlag(boolean b) {
        this.runFlag.set(b);
    }

    public void update(String orderNo, String siteId, String siteName) {
        Update update = new Update();
        update.set("siteId", siteId)
                .set("siteName", siteName);
        Criteria criteria = Criteria.where("orderNo").is(orderNo);
        UpdateResult result = template.upsert(Query.query(criteria), update, OrderInMongo.class);
        log.debug("<< 订单场站信息更新结果: result={}", result);
    }
}