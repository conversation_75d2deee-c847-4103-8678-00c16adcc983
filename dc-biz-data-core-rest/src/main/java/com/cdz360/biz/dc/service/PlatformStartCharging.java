package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.dc.client.AuthCenterFeignClient;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteGroupRoDs;
import com.chargerlinkcar.framework.common.domain.request.StartChargerRequest;
import com.chargerlinkcar.framework.common.domain.request.StopChargerRequest;
import com.chargerlinkcar.framework.common.domain.vo.CloudChargeVo;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;


public abstract class PlatformStartCharging {

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;
    @Autowired
    private SiteGroupRoDs siteGroupRoDs;

    abstract CloudChargeVo checkChargingQueue(StartChargerRequest chargerRequest);

    abstract Optional<Map<String, String>> checkStopingQueue(
        List<StopChargerRequest> stopRequest);

    /**
     * 扣款账户为企业且企业配置了场站组时，要校验可用场站
     * @param corpId 企业ID
     * @param siteId 要启动充电的场站
     * @return 充电场站对应的场站组
     */
    public List<String> checkCorpGidsAuthority(Long corpId, String siteId) {
        List<String> gids = siteGroupRoDs.getGidsBySiteId(siteId);

        ListResponse<String> gidsResp = authCenterFeignClient.getGidsById(corpId);
        FeignResponseValidate.checkIgnoreData(gidsResp);
        if (CollectionUtils.isNotEmpty(gidsResp.getData())) {
            List<String> corpGids = gidsResp.getData();
            corpGids.retainAll(gids);
            IotAssert.isTrue(CollectionUtils.isNotEmpty(corpGids), "非企业场站组站点");
        }
        return gids;
    }

}
