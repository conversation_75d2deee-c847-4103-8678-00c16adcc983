package com.cdz360.biz.dc.repository;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.model.monitor.dto.WarningRecordPo;
import com.cdz360.biz.model.site.type.AlarmEventTypeEnum;
import com.cdz360.biz.model.site.type.AlarmStatusEnum;
import com.mongodb.client.result.UpdateResult;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

/**
 * 告警数据的dao层实现类
 *
 * <AUTHOR> Create on 2018.8.13 9:48
 */
@Component
@Slf4j
public class WarningRecordRepository {


    @Autowired
    private MongoTemplate mongoTemplate;

    private Map<Integer, String> uploadDevice = new HashMap<>();// 上报设备对应文案

    @PostConstruct
    public void initRepos() {
        Arrays.asList(AlarmEventTypeEnum.values())
            .stream()
            .forEach(e -> uploadDevice.put(e.getValue(), e.getSource()));
    }





    public List<WarningRecordPo> getAlarmByDeviceAndCode(String deviceCode, Integer status,
        String code) {
        Query query = new Query();
        if (null != deviceCode) {
            query.addCriteria(Criteria.where("deviceId").is(deviceCode));
        }
        if (null != status) {
            query.addCriteria(Criteria.where("status").is(status));
        }
        if (null != code) {
            query.addCriteria(Criteria.where("warningCode").is(code));
        }
        return mongoTemplate.find(query, WarningRecordPo.class);
    }


    public List<Long> getAlarmByDeviceCode(String deviceCode, Integer status, String code) {
        List<WarningRecordPo> wWarningRecordList = getAlarmByDeviceAndCode(deviceCode, status,
            code);
        List<Long> recordIdList = new ArrayList<>();
        for (WarningRecordPo wWarningRecord : wWarningRecordList) {
            if (null != wWarningRecord.getWarningId()) {
                recordIdList.add(wWarningRecord.getWarningId());
            }
        }
        return recordIdList;
    }


    public void updateBatchStatusById(List<Long> recordIdList, WarningRecordPo wWarningRecord) {
        if (recordIdList == null || recordIdList.size() == 0) {
            log.error("更新条件recordIdList为空不更新");
            return;
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("warningId").in(recordIdList));
        Update update = new Update();
        if (wWarningRecord.getStatus() != null) {
            //状态为未结束才允许结束
            query.addCriteria(
                Criteria.where("status").is(AlarmStatusEnum.ALARM_STATUS_NOT_END.getValue()));
            update.set("status", wWarningRecord.getStatus());
        }
        if (wWarningRecord.getEndTime() != null) {
            update.set("endTime", this.dual2MongoDate(wWarningRecord.getEndTime()));
        }
        if (wWarningRecord.getUpdateBy() != null) {
            update.set("updateBy", wWarningRecord.getUpdateBy());
        }
        if (wWarningRecord.getError() != null) {
            update.set("error", wWarningRecord.getError());
        }
        if (wWarningRecord.getTemp() != null && wWarningRecord.getTemp() != 0) {
            update.set("temp", wWarningRecord.getTemp());
        }
        update.set("warningUpdateTime", this.dual2MongoDate(new Date()));
        UpdateResult updateResult = mongoTemplate.updateMulti(query, update, WarningRecordPo.class);
        log.info("条件{}修改完成{}", JsonUtils.toJsonString(query),
            updateResult.getMatchedCount());
    }


    public List<String> getAlarmDeviceId(Integer alarmStatus, String alarmType) {
        Query query = new Query();
        query.addCriteria(Criteria.where("status").is(alarmStatus));
        query.addCriteria(Criteria.where("code").is(alarmType));
        List<WarningRecordPo> wWarningRecordList = mongoTemplate.find(query, WarningRecordPo.class);
        List<String> recordIdList = new ArrayList<>();
        for (WarningRecordPo wWarningRecord : wWarningRecordList) {
            if (null != wWarningRecord.getWarningId()) {
                recordIdList.add(wWarningRecord.getDeviceId());
            }
        }
        return recordIdList;
    }


    public List<WarningRecordPo> getWarningRecord(Integer alarmStatus,
        String alarmCode,
        List<String> evseNoList,
        Integer connectorId,
        List<Integer> warningTypeList,
        List<String> sourceNo) {
        Query query = new Query();
        if (null != alarmStatus) {
            query.addCriteria(Criteria.where("status").is(alarmStatus));
        }
        if (null != alarmCode && !"0".equals(alarmCode)) {
            query.addCriteria(Criteria.where("warningCode").is(alarmCode));
        }
        if (null != evseNoList) {
            query.addCriteria(Criteria.where("boxOutFactoryCode").in(evseNoList));
        }
        if (null != connectorId) {
            if (connectorId.intValue() >= 0) {
                query.addCriteria(Criteria.where("connectorId").is(connectorId));
            }
        } else {
            query.addCriteria(Criteria.where("connectorId").exists(false));
        }
        if (warningTypeList != null && warningTypeList.size() > 0) {
            query.addCriteria(Criteria.where("warningType").in(warningTypeList));
        }
        if (null != sourceNo) {
            query.addCriteria(Criteria.where("sourceNo").in(sourceNo));
        }
        log.info(">>getWarningRecord的查询条件Query={}>>>>>>>", query);
        return mongoTemplate.find(query, WarningRecordPo.class);
    }


    public List<WarningRecordPo> getWarningSiteCtrlRecord(Integer alarmStatus,
        String warningCode,
        List<String> ctrlNoList,
        List<Integer> warningTypeList) {
        return this.getWarningRecord(alarmStatus, warningCode, null, -1, warningTypeList,
            ctrlNoList);
    }


    public WarningRecordPo getWarningRecordX(Integer alarmStatus,
        String alarmCode,
        String evseNo,
        Integer connectorId,
        List<Integer> warningTypeList) {
        Query query = new Query();

        //if (null != evseNo) {
        query.addCriteria(Criteria.where("boxOutFactoryCode").is(evseNo));
        //}
        if (null != connectorId) {
            query.addCriteria(Criteria.where("connectorId").is(connectorId));
        } else {
            query.addCriteria(Criteria.where("connectorId").exists(false));
        }
        if (null != alarmCode && !"0".equals(alarmCode)) {
            query.addCriteria(Criteria.where("warningCode").is(alarmCode));
        }
        if (null != alarmStatus) {
            query.addCriteria(Criteria.where("status").is(alarmStatus));
        }
        if (warningTypeList != null && warningTypeList.size() > 0) {
            query.addCriteria(Criteria.where("warningType").in(warningTypeList));
        }
        log.info(">>getWarningRecord的查询条件Query={}>>>>>>>", query);
        return mongoTemplate.findOne(query, WarningRecordPo.class);
    }


    public WarningRecordPo getWarningSiteCtrlRecordX(Integer alarmStatus,
        String warningCode,
        String ctrlNo,
        List<Integer> warningTypeList) {
        return this.getWarningRecordX(alarmStatus, warningCode, ctrlNo, null, warningTypeList);
    }


    public WarningRecordPo getWarningEvseRecordX(Integer alarmStatus, String warningCode,
        String evseNo, List<Integer> warningTypeList) {
        return this.getWarningRecordX(alarmStatus, warningCode, evseNo, 0, warningTypeList);
    }


    public long getWarningRecordCount(Integer alarmStatus,
        String alarmCode,
        List<String> evseNoList,
        Integer connectorId,
        List<Integer> warningTypeList,
        List<String> sourceNo) {
        Query query = new Query();
        if (null != alarmStatus) {
            query.addCriteria(Criteria.where("status").is(alarmStatus));
        }
        if (null != alarmCode && !"0".equals(alarmCode)) {
            query.addCriteria(Criteria.where("warningCode").is(alarmCode));
        }
        if (CollectionUtils.isNotEmpty(evseNoList)) {
            query.addCriteria(Criteria.where("boxOutFactoryCode").in(evseNoList));
        }
        if (null != connectorId) {
            if (connectorId.intValue() >= 0) {
                query.addCriteria(Criteria.where("connectorId").is(connectorId));
            }
        } else {
            query.addCriteria(Criteria.where("connectorId").exists(false));
        }
        if (CollectionUtils.isNotEmpty(warningTypeList)) {
            query.addCriteria(Criteria.where("warningType").in(warningTypeList));
        }
        if (CollectionUtils.isNotEmpty(sourceNo)) {
            query.addCriteria(Criteria.where("sourceNo").in(sourceNo));
        }
        log.info(">>getWarningRecord的查询条件Query={}>>>>>>>", query);
        return mongoTemplate.count(query, WarningRecordPo.class);
    }


    public long getWarningSiteCtrlRecordCount(Integer alarmStatus,
        String warningCode,
        List<String> ctrlNoList,
        List<Integer> warningTypeList) {
        return this.getWarningRecordCount(alarmStatus,
            warningCode,
            null,
            -1,//忽略枪号
            warningTypeList,
            ctrlNoList);

    }


    public List<WarningRecordPo> getSysLeveWarningRecord(Integer status, String warningCode,
        String gwno, String appName) {
        Query query = new Query();
        if (null != status) {
            query.addCriteria(Criteria.where("status").is(status));
        }
        if (null != warningCode && !"0".equals(warningCode)) {
            query.addCriteria(Criteria.where("warningCode").is(warningCode));
        }
        if (StringUtils.isNotBlank(gwno)) {
            query.addCriteria(Criteria.where("gwno").is(gwno));
        }
        if (StringUtils.isNotBlank(appName)) {
            query.addCriteria(Criteria.where("appName").is(appName));
        }
        log.info(">>getGwTimeoutWarningRecord的查询条件Query={}>>>>>>>", query);
        return mongoTemplate.find(query, WarningRecordPo.class);
    }


    public List<WarningRecordPo> evseErrorAlarms(Date startTime, Date endTime, Integer plugId,
        String evseNo, String siteId) {
        log.info(
            ">> 从mongodb中获取告警数据: startTime={},EndTime={},plugId={},evseNo={},siteId={}",
            startTime,
            endTime,
            plugId,
            evseNo,
            siteId);

        // 查询条件
        Criteria criteria = Criteria.where("siteId").is(siteId)
//                .and("boxOutFactoryCode").is(evseNo)
            .and("startTime").gte(startTime).lt(endTime);

        if (plugId != null) {
            criteria = criteria.and("connectorId").is(plugId);
        }

        if (StringUtils.isNotEmpty(evseNo)) {
            criteria = criteria.and("boxOutFactoryCode").is(evseNo);
        }

        log.info("从mongodb中获取告警数据的查询条件: {}", JsonUtils.toJsonString(criteria));


        List<WarningRecordPo> wWarningRecordInMongo = mongoTemplate.find(new Query(criteria),
            WarningRecordPo.class);

        if (CollectionUtils.isEmpty(wWarningRecordInMongo)) {
            log.info("数据不存在.");
        } else {
            log.debug("<< 查询结果: wWarningRecordInMongo = {}",
                JsonUtils.toJsonString(wWarningRecordInMongo));
        }
        return wWarningRecordInMongo;
    }

    public Date dual2MongoDate(Date date) {
        return date;

    }

}
