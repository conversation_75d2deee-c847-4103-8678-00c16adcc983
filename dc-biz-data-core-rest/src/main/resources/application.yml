spring:
  application:
    name: dc-biz-data-core-dev
  config:
    import: "configserver:http://oam-test.iot.renwochong.com"
  profiles:
    active: test01,common,rabbitmq,redis,mongodb,zipkin,jdbc-charger,oss
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    config:
      label: test01
  ribbon:
    ReadTimeout: 60000
    ConnectTimeout: 60000
  jackson:
    default-property-inclusion: non_null
  # 外网访问：本地测试用


management:
  endpoints:
    web:
      exposure:
        include: health,info,refresh

#超时时间10000毫秒 = 10秒(导出数据，需要时间较长，增加超时时间)
ribbon:
  MaxAutoRetries: 0
  MaxAutoRetriesNextServer: 1

eureka:
  instance:
    hostname: localhost
  client:
    registerWithEureka: false
    fetchRegistry: true
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: http://aaa:<EMAIL>:7001/eureka/,http://aaa:<EMAIL>:7001/eureka/

feign:
  hystrix:
    enabled: true

hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 5000
          strategy: SEMAPHORE


server:
  port: 8085

logging:
  level:
    com.cdz360: 'DEBUG'
    com.chargerlinkcar: 'DEBUG'
    org.springframework: 'WARN'
    orr.springframework.data: 'DEBUG'
    org.springframework.cloud: 'WARN'
    org.springframework.cloud.config: 'INFO'
    org.springframework.cloud.netflix: 'INFO'
    feign: 'DEBUG'

  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} - %logger{36}.%M\\(%line\\) - %msg%n"

springdoc:
  packagesToScan: com.cdz360.biz.dc.rest
  swagger-ui:
    path: /swagger-ui.html

dmpUrl: http://**************:8890

mybatis:
  config-location: classpath:mybatis/mybatis-config.xml
  mapper-locations: classpath:mybatis/mapper/*.xml

# 分页配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql


# 亨通ERP对接配置
erp:
  host: https://shek.ik3cloud.com/k3cloud
  login: # 登录
    url: /Kingdee.BOS.WebApi.ServicesStub.AuthService.ValidateUser.common.kdsvc
    username: kingdee
    password: kingdee123@
    acctID: 20200627090547 # 登录账套Id，必传
    lcid: 2052 # 语言ID: 中文2052，英文1033，繁体3076
  save: # 保存表单
    url: /Kingdee.BOS.WebApi.ServicesStub.DynamicFormService.Save.common.kdsvc
    formid: HTGD_projectincome # 表单ID，必传


activity:
  urlPattern: http://renwochong.com/activity/:id
  replace: :id
  newGuest:
    regTimeLimit: 72 # 新客注册时间限制条件，单位小时


bill:
  regList:
    - topCommId: 34474
      corpMchInfoList:
        - corpId: 419
          corpName: 朗新互联企业
          regexFieldIdx: 3
          regex: ^新电途\(上海鼎充:.*\)$
  ordinaryCommList:
    - topCommId: 34474
      name: 国充充电普通商户