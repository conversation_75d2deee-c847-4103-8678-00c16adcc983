package com.cdz360.biz.bi.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.bi.BaseMockTest;
import com.cdz360.biz.model.trading.bi.param.ListChargeOrderBiByVinParam;
import com.cdz360.biz.model.trading.bi.vo.VinBiVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
class OrderBiServiceTest extends BaseMockTest {

    @Autowired
    private OrderBiService orderBiService;

    @Test
    void countVin() {
    }

    @Test
    void exportVinBi() {
    }

    @Test
    void getVinBi() {

        long start = 0;
        int size = 1000;

        ListChargeOrderBiByVinParam param = new ListChargeOrderBiByVinParam();
        param.setCommIdChain("33421");
        param.setTotal(false);

        long st = System.nanoTime();
        long total = 0;
        while (true) {
            param.setStart(start++ * size);
            param.setSize(size);
            ListResponse<VinBiVo> vinBi = orderBiService.getVinBi(param);
            if (CollectionUtils.isEmpty(vinBi.getData())) {
                break;
            }
            total += vinBi.getData().size();
        }

        log.info(" start: {}, size: {}, total: {}, time: {} 秒",
                --start, size, total, (System.nanoTime() - st) / 1000000000);
    }
}