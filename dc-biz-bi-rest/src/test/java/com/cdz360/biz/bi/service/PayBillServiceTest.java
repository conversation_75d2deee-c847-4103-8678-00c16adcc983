package com.cdz360.biz.bi.service;

import com.cdz360.biz.bi.BaseMockTest;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.chargerlinkcar.framework.common.utils.UUIDUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;

@Transactional
@Slf4j
public class PayBillServiceTest extends BaseMockTest {

    private static final String PAY_BILL_FLAG = "PayBill";

    private static final Long[] COMM_ID_LIST_3342 = {33421L, 34477L, 34481L, 34482L, 34484L, 34486L,
        34487L, 34488L, 34492L, 34494L, 34495L, 34498L, 34500L, 34506L, 34507L, 34508L, 34510L,
        34513L, 34514L, 34515L, 34516L};

    private static final PayBillParam SEARCH_PARAM = new PayBillParam();

    @Autowired
    private PayBillService payBillService;

    @Autowired
    private ExcelFileService excelFileService;

    @Test
    public void test_exportExcel() {

        ExcelPosition position = new ExcelPosition();
        position.setSubDir(PAY_BILL_FLAG + new SimpleDateFormat("yyyyMMdd").format(new Date()))
            .setSubFileName(UUIDUtils.getUuid32());

//        String orderId = "2019";
//        String cusName = "180164";
//        String cusPhone = "180164";
//        SEARCH_PARAM.setIndex(1);
//        SEARCH_PARAM.setSize(1);
        SEARCH_PARAM.setCommIdList(Arrays.asList(COMM_ID_LIST_3342));
//        SEARCH_PARAM.setOrderId(orderId);
//        SEARCH_PARAM.setCusName(cusName);
//        SEARCH_PARAM.setCusPhone(cusPhone);

        try {
            payBillService.exportExcelCus(position, SEARCH_PARAM);
        } catch (IOException e) {
            e.printStackTrace();
        }

        while (excelFileService.existsDownFile(position.getSubDir(), position.getSubFileName())) {
            log.info("excel 生成");
        }
    }
}
