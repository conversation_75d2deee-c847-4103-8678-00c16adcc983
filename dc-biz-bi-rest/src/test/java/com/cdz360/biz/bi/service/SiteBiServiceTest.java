package com.cdz360.biz.bi.service;

import com.cdz360.biz.bi.BaseMockTest;
import com.cdz360.biz.bi.service.site.SiteBiService;
import com.cdz360.biz.model.bi.site.OrderElecDivision;
import com.cdz360.biz.model.bi.site.PlugUtilization;
import com.cdz360.biz.model.bi.site.SiteUtilization;
import com.cdz360.biz.model.bi.site.UserOrderElec;
import com.cdz360.biz.model.iot.param.SiteBiParam;
import com.cdz360.biz.model.iot.param.SiteBiTopParam;
import com.cdz360.biz.model.iot.type.SiteBiDependOnOrderTimeType;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.List;

@Slf4j
class SiteBiServiceTest  extends BaseMockTest {

    @Autowired
    private SiteBiService siteBiService;

    @Test
    void test_chargeDivisionBi() {
        long duration = 20;
        SiteBiParam param = new SiteBiParam();
        param.setSiteId("2003207904816285429");
        param.setDependOnTimeType(SiteBiDependOnOrderTimeType.PAY_TIME);
        param.setSampleType(SiteBiSampleType.DAY);
        param.setStartTime(LocalDateTime.now()
//                .with(TemporalAdjusters.firstDayOfMonth())
                .minusDays(duration)
                .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        param.setEndTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());

        List<OrderElecDivision> result = siteBiService.chargeDivisionBi(param);
        log.info("result size = {}", result.size());
        Assert.isTrue(duration + 1 == result.size(), "555555555555");

        param.setSampleType(SiteBiSampleType.HOUR);
        param.setStartTime(LocalDateTime.now()
                .minusHours(duration)
                .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        param.setEndTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        result = siteBiService.chargeDivisionBi(param);
        log.info("result size = {}", result.size());
        Assert.isTrue(duration + 1 == result.size(), "555555555555");

        param.setSampleType(SiteBiSampleType.MONTH);
        param.setStartTime(LocalDateTime.now()
                .minusMonths(duration)
                .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        param.setEndTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        result = siteBiService.chargeDivisionBi(param);
        log.info("result size = {}", result.size());
        Assert.isTrue(duration + 1 == result.size(), "555555555555");
    }

    @Test
    void test_userChargeDivisionBi() {
        SiteBiTopParam param = new SiteBiTopParam();
        param.setSiteId("20190801658377985667290595");
        param.setDependOnTimeType(SiteBiDependOnOrderTimeType.PAY_TIME);
        param.setSampleType(SiteBiSampleType.MONTH);
        param.setStartTime(LocalDateTime.now()
                .with(TemporalAdjusters.firstDayOfMonth())
                .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        param.setEndTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        param.setTopCount(10);

        List<UserOrderElec> result = siteBiService.userChargeDivisionBi(param);
        log.info("result size = {}", result.size());
    }

    @Test
    void test_utilizationBi() {
        long duration = 20;
        SiteBiParam param = new SiteBiParam();
        param.setSiteId("2003207904816285429");
        param.setSampleType(SiteBiSampleType.DAY);
        param.setStartTime(LocalDateTime.now()
//                .with(TemporalAdjusters.firstDayOfMonth())
                .minusDays(duration)
                .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        param.setEndTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());

        List<SiteUtilization> result = siteBiService.utilizationBi(param);
        log.info("result size = {}", result.size());
        Assert.isTrue(duration + 1 == result.size(), "555555555555");

        param.setSampleType(SiteBiSampleType.MONTH);
        param.setStartTime(LocalDateTime.now()
//                .with(TemporalAdjusters.firstDayOfMonth())
                .minusDays(duration)
                .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        param.setEndTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());

        result = siteBiService.utilizationBi(param);
        log.info("result size = {}", result.size());
        Assert.isTrue(duration + 1 == result.size(), "555555555555");

        param.setSampleType(SiteBiSampleType.MONTH);
        param.setStartTime(LocalDateTime.now()
//                .with(TemporalAdjusters.firstDayOfMonth())
                .minusDays(duration)
                .atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
        param.setEndTime(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());

        result = siteBiService.utilizationBi(param);
        log.info("result size = {}", result.size());
        Assert.isTrue(duration + 1 == result.size(), "555555555555");
    }

    @Test
    void test_initUserRate() {
        SiteBiTopParam param = new SiteBiTopParam();
        param.setSiteId("2004096325574525944");
        param.setSampleType(SiteBiSampleType.DAY);
        param.setStartTime(1586793600000L);
        param.setEndTime(1586880000000L);
        param.setTopCount(10);

        List<PlugUtilization> result = siteBiService.plugUtilizationBi(param);
        log.info("result size = {}", result.size());
    }
}