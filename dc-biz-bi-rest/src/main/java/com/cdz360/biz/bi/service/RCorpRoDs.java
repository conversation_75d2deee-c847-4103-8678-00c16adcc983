package com.cdz360.biz.bi.service;

import com.cdz360.biz.bi.mapper.RCorpRoMapper;
import com.cdz360.biz.model.corp.po.RCorpPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class RCorpRoDs {

	@Autowired
	private RCorpRoMapper rCorpRoMapper;

	public RCorpPo getById(Long id) {
		return this.rCorpRoMapper.getById(id);
	}

	public RCorpPo getByUidAndIdChain(Long uid, String idChain) {
		return this.rCorpRoMapper.getByUidAndIdChain(uid, idChain);
	}
}
