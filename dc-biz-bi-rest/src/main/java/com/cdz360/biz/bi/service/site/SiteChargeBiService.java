package com.cdz360.biz.bi.service.site;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.bi.service.OrderBiService;
import com.cdz360.biz.ds.trading.ro.site.ds.BiPlugRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.BiSiteOrderRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.CommRoDs;
import com.cdz360.biz.model.bi.site.ChargeFee;
import com.cdz360.biz.model.bi.site.PlugUtilization;
import com.cdz360.biz.model.bi.site.SiteUtilization;
import com.cdz360.biz.model.bi.site.UserOrderElec;
import com.cdz360.biz.model.common.constant.Constant;
import com.cdz360.biz.model.iot.param.SiteBiParam;
import com.cdz360.biz.model.iot.param.SiteBiTopParam;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import com.cdz360.biz.model.trading.order.type.BiDependOnType;
import com.cdz360.biz.model.trading.site.po.BiPlugPo;
import com.cdz360.biz.model.trading.site.po.BiSiteOrderPo;
import com.cdz360.biz.model.trading.site.po.CommPo;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SiteChargeBiService {

    // 返回前端对打采样点
//    private static final int MAX_SAMPLE_SIZE = Constant.MAX_SAMPLE_SIZE;

    @Autowired
    private BiSiteOrderRoDs biSiteOrderRoDs;

    @Autowired
    private OrderBiService orderBiService;

    @Autowired
    private BiPlugRoDs biPlugRoDs;

    @Autowired
//    private TRCommercialRoDs trCommercialRoDs;
    private CommRoDs commRoDs;

    /**
     * 统计场站的分时电量
     *
     * @param param
     * @return
     */
    public List<ChargeFee> siteChargeBi(SiteBiParam param) {
        // 校验参数
        Optional<String> validResult = this.validParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        // 调整前端入参
        param.resetTime();

        // 库中采样数据
        List<BiSiteOrderPo> biList;
        if (StringUtils.isNotBlank(param.getSiteId())) {
            biList = biSiteOrderRoDs.selectBySiteIdWhen(
                    param.getSiteId(),
                    BiDependOnType.valueOf(param.getDependOnTimeType().name()),
                    new Date(param.getStartTime()), new Date(param.getEndTime()));
        } else {
            CommPo commercialById = commRoDs.getCommById(param.getCommId());
            IotAssert.isNotNull(commercialById, "找不到对应商户");
            IotAssert.isNotBlank(commercialById.getIdChain(), "对应商户idChain不能为空");

            biList = biSiteOrderRoDs.selectByIdChainWhen(
                    commercialById.getIdChain(),
                    BiDependOnType.valueOf(param.getDependOnTimeType().name()),
                    new Date(param.getStartTime()), new Date(param.getEndTime()));

        }

        // 按采样方式整合数据点
        // 注: 仅采样24点数据(24小时)

        if (param.getSampleType() == SiteBiSampleType.HOUR) {
            return this.divisionResult(biList.stream()
                    .map(this::map2Division)
                    .collect(Collectors.toList()), param);
        }

        // 其他采样方式
        List<ChargeFee> result = new ArrayList<>();

        // 按天采样
        // 注: 仅采样24点数据(24天)
        // 按月采样
        // 注: 仅采样24点数据(24月)

        // 调整时间，分组统计整合返回: 前端控制数据时间大小
        Map<Date, List<BiSiteOrderPo>> collect = biList.stream()
                .map(bi -> bi.setTime(this.resetDate(param.getSampleType(), bi.getTime())))
                .collect(Collectors.groupingBy(BiSiteOrderPo::getTime));

        collect.forEach((k, v) -> {
            ChargeFee d = new ChargeFee();
            d.setSiteId(param.getSiteId())
                    .setTime(k);
//            System.out.println(v.stream().map(BiSiteOrderPo::getServFee).reduce(BigDecimal.ZERO, BigDecimal::add));
            d.setElecFee(v.stream().map(BiSiteOrderPo::getElecFee)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            d.setFee(v.stream().map(BiSiteOrderPo::getFee)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            d.setServFee(v.stream().map(BiSiteOrderPo::getServFee)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));

            // 选出分组中最大id的记录，以便获取其功率
            Long power;
            if (StringUtils.isNotBlank(param.getSiteId())) {

                BiSiteOrderPo maxIdRecord = v.stream().reduce(new BiSiteOrderPo(), (x, y) -> x.getId() > y.getId() ? x : y);
                power = maxIdRecord == null ? 0L : maxIdRecord.getPower();
            } else {
                // 查询企业时，需要从各个场站中获取最大值，再做加和，作为统计周期内的商户功率
                Map<String, List<BiSiteOrderPo>> siteBiGroupMap = v.stream()
                        .collect(Collectors.groupingBy(BiSiteOrderPo::getSiteId));

                power = siteBiGroupMap
                        .keySet()
                        .stream()
                        .mapToLong(key -> this.getMaxPower(siteBiGroupMap.get(key)))
                        .sum();
            }

            d.setPower(power);

            if (power == 0L) {
                d.setServFeePerPower(BigDecimal.ZERO);
            } else {
                // 计算站点功率和单kW服务费
                if (SiteBiSampleType.DAY.equals(param.getSampleType())) {
                    if (d.getPower() > 0) {
                        d.setServFeePerPower(d.getServFee()
                                .divide(BigDecimal.valueOf(d.getPower()), RoundingMode.HALF_UP)
                                .setScale(2, RoundingMode.HALF_UP));
                    }
                } else if (SiteBiSampleType.MONTH.equals(param.getSampleType())) {

                    // 获取本月有多少天
                    int dayCountOfMonth = DateUtil.getDayCountInMonth(k);

                    if (d.getPower() > 0) {
                        d.setServFeePerPower(d.getServFee()
                                .divide(BigDecimal.valueOf(d.getPower() * dayCountOfMonth), RoundingMode.HALF_UP)
                                .setScale(2, RoundingMode.HALF_UP));
                    }
                } else {
                    IotAssert.isTrue(false, "暂不支持该种采样时间单位: " + param.getSampleType());
                }
            }


//            int dayCountOfMonth = DateUtil.getDayCountInMonth(k);

//            d.setPower(v.stream().map(BiSiteOrderPo::getPower)
//                    .reduce(0L, (subtotal, element) -> subtotal + element));

//            if(d.getPower() > 0) {
//                d.setServFeePerPower(d.getServFee()
//                        .divide(BigDecimal.valueOf(d.getPower()), RoundingMode.HALF_UP)
//                        .setScale(2, RoundingMode.HALF_UP));
//            }

            result.add(d);
        });

        // 调整排序
        result.sort(Comparator.comparing(ChargeFee::getTime).reversed());
        return this.divisionResult(result, param);
    }

    private Long getMaxPower(List<BiSiteOrderPo> list) {
        long i = 0;
        if (CollectionUtils.isNotEmpty(list)) {
            for (BiSiteOrderPo one : list) {
                if (one.getPower() != null && i < one.getPower()) {
                    i = one.getPower();
                }
            }
        }
        return i;
    }

    private List<ChargeFee> divisionResult(List<ChargeFee> list, SiteBiParam param) {
        List<LocalDateTime> timeList = reviseResult(param);
        if (CollectionUtils.isEmpty(timeList)) {
            throw new DcArgumentException("开始/结束时间不正确");
        }

//        if (timeList.size() < Constant.MAX_SAMPLE_SIZE) {
//            return list;
//        }

        List<ChargeFee> result = new ArrayList<>();
        timeList.stream().limit(Constant.MAX_SAMPLE_SIZE).forEach(time -> {
            Optional<ChargeFee> first = list.stream().filter(d -> d.getTime().toInstant().atOffset(ZoneOffset.of("+8"))
                    .toLocalDateTime().isEqual(time)).findFirst();
            if (first.isPresent()) {
                result.add(first.get());
            } else {
                ChargeFee division = new ChargeFee()
                        .setTime(new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()))
                        .setSiteId(param.getSiteId());
                result.add(division);
            }
        });

        return result;
    }

    private List<SiteUtilization> utilizationResult(List<SiteUtilization> list, SiteBiParam param) {
        List<LocalDateTime> timeList = reviseResult(param);
        if (CollectionUtils.isEmpty(timeList)) {
            throw new DcArgumentException("开始/结束时间不正确");
        }
//        if (timeList.size() < Constant.MAX_SAMPLE_SIZE) {
//            return list;
//        }

        List<SiteUtilization> result = new ArrayList<>();
        timeList.forEach(time -> {
            Optional<SiteUtilization> first = list.stream().filter(d -> d.getDate().toInstant().atOffset(ZoneOffset.of("+8"))
                    .toLocalDateTime().isEqual(time)).findFirst();
            if (first.isPresent()) {
                result.add(first.get());
            } else {
                SiteUtilization utilization = new SiteUtilization()
                        .setDate(new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()))
                        .setSiteId(param.getSiteId());
                result.add(utilization);
            }
        });

        return result;
    }

    /**
     * 调整数据返回，固定长度
     *
     * @return
     */
    private List<LocalDateTime> reviseResult(SiteBiParam param) {
        param.resetTime();

        LocalDateTime start = param.getFromTime().toInstant()
                .atOffset(ZoneOffset.of("+8"))
                .toLocalDateTime();
        LocalDateTime end = param.getToTime().toInstant()
                .atOffset(ZoneOffset.of("+8"))
                .toLocalDateTime();

        List<LocalDateTime> timeList = new ArrayList<>();
        while (start.isBefore(end)) {
            timeList.add(start);
            if (param.getSampleType() == SiteBiSampleType.HOUR) {
                start = start.plusHours(1);
            } else if (param.getSampleType() == SiteBiSampleType.DAY) {
                start = start.plusDays(1);
            } else {
                start = start.plusMonths(1);
            }
        }

        log.info("time size = {}", timeList.size());
        return timeList;
    }

    /**
     * 调整时间点
     *
     * @param sampleType
     * @param time
     * @return
     */
    private Date resetDate(SiteBiSampleType sampleType, Date time) {
        // 分秒都为0
        LocalDateTime local = time.toInstant().atOffset(ZoneOffset.of("+8"))
                .toLocalDateTime().withMinute(0).withSecond(0).withNano(0);

        if (sampleType == SiteBiSampleType.DAY ||
                sampleType == SiteBiSampleType.MONTH) {
            local = local.withHour(0);
        }

        if (sampleType == SiteBiSampleType.MONTH) {
            // 当月第一天
            local = local.with(TemporalAdjusters.firstDayOfMonth());
        }

        return new Date(local.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
    }

    private ChargeFee map2Division(BiSiteOrderPo po) {
        ChargeFee result = new ChargeFee();
        BeanUtils.copyProperties(po, result);
        return result;
    }

    private Optional<String> validParam(SiteBiParam param) {
        if (StringUtils.isBlank(param.getSiteId()) && param.getCommId() == null) {
//            return Optional.of("场站Id没有提供");
            return Optional.of("请提供场站Id或商户Id");
        }

        if (null == param.getStartTime() || null == param.getEndTime()) {
            return Optional.of("开始结束时间没有提供");
        }

        if (param.getStartTime() >= param.getEndTime()) {
            return Optional.of("开始时间大于等于结束时间，不合理");
        }

        if (reviseResult(param).size() > Constant.MAX_SAMPLE_SIZE) {
            return Optional.of("时间分片大于最大值，请传入正确的开始/结束时间");
        }

        return Optional.empty();
    }

    /**
     * 场站用户充电排行榜
     *
     * @param param
     * @return
     */
    public List<UserOrderElec> userChargeDivisionBi(SiteBiTopParam param) {

        // 参数时间
        Optional<String> validResult = this.validParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        if (null == param.getTopCount()) {
            log.info("查询参数无效: top没有提供");
            throw new DcArgumentException("参数有误，请提供top参数");
        }

        // 充值以下时间
        param.resetTime();

        return orderBiService.userChargeDivisionBi(param);
    }

    /**
     * 桩时长利用率
     *
     * @param param
     * @return
     */
    public List<SiteUtilization> utilizationBi(SiteBiParam param) {

        // 参数校验
        Optional<String> validResult = this.validParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        // 充值以下时间
        param.resetTime();

        // 库中采样数据
        List<BiPlugPo> poList = biPlugRoDs.selectBySiteIdWhen(
                param.getSiteId(),
                null, null,
                new Date(param.getStartTime()), new Date(param.getEndTime()));

        // 其他采样方式
        List<SiteUtilization> result = new ArrayList<>();

        // 按天采样
        // 注: 仅采样24点数据(24天)
        // 按月采样
        // 注: 仅采样24点数据(24月)

        // 调整时间，分组统计整合返回: 前端控制数据时间大小
        Map<Date, List<BiPlugPo>> collect = poList.stream()
                .map(po -> po.setDate(this.resetDate(param.getSampleType(), po.getDate())))
                .collect(Collectors.groupingBy(BiPlugPo::getDate));

        collect.forEach((k, v) -> {
            SiteUtilization u = new SiteUtilization();
            u.setSiteId(param.getSiteId())
                    .setDate(k);

            u.setDuration(v.stream().mapToLong(BiPlugPo::getDuration).sum());

            // 有效使用时间
            u.setUseRate(initUserRate(u.getDuration(), v.stream().mapToLong(BiPlugPo::getAvailable).sum()));

            result.add(u);
        });

        // 调整排序
        result.sort(Comparator.comparing(SiteUtilization::getDate));
        return this.utilizationResult(result, param);
    }

    /**
     * 场站桩利用率排行榜
     *
     * @param param
     * @return
     */
    public List<PlugUtilization> plugUtilizationBi(SiteBiTopParam param) {

        // 参数校验
        Optional<String> validResult = this.validParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        if (null == param.getTopCount()) {
            log.info("查询参数无效: 没有查询数据量");
            throw new DcArgumentException("参数有误，请提供");
        }

        // 充值以下时间
        param.resetTime();

        // 库中采样数据
        List<BiPlugPo> poList = biPlugRoDs.selectBySiteIdWhen(
                param.getSiteId(),
                null, null,
                new Date(param.getStartTime()), new Date(param.getEndTime()));


        // 其他采样方式
        List<PlugUtilization> result = new ArrayList<>();

        // 按天采样
        // 注: 仅采样24点数据(24天)
        // 按月采样
        // 注: 仅采样24点数据(24月)

        // 调整时间，分组统计整合返回: 前端控制数据时间大小
        Map<String, List<BiPlugPo>> collect = poList.stream()
                .map(po -> po.setDate(this.resetDate(param.getSampleType(), po.getDate())))
                .collect(Collectors.groupingBy(BiPlugPo::getEvseNo));

        collect.forEach((k, v) -> {
            // 按照枪分组
            Map<Integer, List<BiPlugPo>> plugMap = v.stream().collect(Collectors.groupingBy(BiPlugPo::getPlugId));
            plugMap.forEach((p, l) -> {
                PlugUtilization u = new PlugUtilization();
                u.setSiteId(param.getSiteId())
                        .setEvseNo(k)
                        .setPlugId(p);

                u.setDuration(l.stream().mapToLong(BiPlugPo::getDuration).sum());

                // 有效使用时间
                u.setUseRate(initUserRate(u.getDuration(), v.stream().mapToLong(BiPlugPo::getAvailable).sum()));

                result.add(u);
            });
        });

        // 调整排序
        // README: 返回数据还包含其他信息，在 ant 中赋值
        result.sort(Comparator.comparing(PlugUtilization::getUseRate));
        return result.stream().limit(param.getTopCount()).collect(Collectors.toList());
    }

    public static BigDecimal initUserRate(long duration, long available) {
        if (0 == available) {
            return BigDecimal.ZERO;
        }

        return BigDecimal.valueOf(duration).divide(BigDecimal.valueOf(available), 2, RoundingMode.HALF_UP);
    }

}
