package com.cdz360.biz.bi.service.site;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.type.OrderType;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.bi.domain.vo.OfflineEvseExportVo;
import com.cdz360.biz.bi.feign.DataCoreFeignClient;
import com.cdz360.biz.bi.service.CommOrderBiService;
import com.cdz360.biz.bi.service.CommercialQueryDs;
import com.cdz360.biz.bi.service.ExcelFileService;
import com.cdz360.biz.bi.service.OrderBiService;
import com.cdz360.biz.ds.trading.ro.order.ds.OrderBiRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.BiPlugRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.BiSiteOrderAccountRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.BiSiteOrderHlhtRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.BiSiteOrderRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.CommRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.model.bi.dashboard.ChargeOrderCommBiVo;
import com.cdz360.biz.model.bi.dashboard.CommStatisticBiVo;
import com.cdz360.biz.model.bi.dashboard.SiteAndPlugBiVoEx;
import com.cdz360.biz.model.bi.dashboard.SiteUtilizationTopVo;
import com.cdz360.biz.model.bi.dashboard.SubCommStatisticBiVo;
import com.cdz360.biz.model.bi.site.OrderCount;
import com.cdz360.biz.model.bi.site.PlugUtilization;
import com.cdz360.biz.model.bi.site.SiteUtilization;
import com.cdz360.biz.model.bi.site.UserOrderElec;
import com.cdz360.biz.model.bi.type.OrderByType;
import com.cdz360.biz.model.common.constant.Constant;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.iot.param.SiteBiParam;
import com.cdz360.biz.model.iot.param.SiteBiTopParam;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import com.cdz360.biz.model.iot.type.UtilizationArrowType;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import com.cdz360.biz.model.site.type.SiteStatus;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.hlht.vo.HlhtSiteBiVo;
import com.cdz360.biz.ess.model.data.param.DataBiParam;
import com.cdz360.biz.model.trading.iot.vo.EvseInfoVo;
import com.cdz360.biz.model.trading.order.type.BiDependOnType;
import com.cdz360.biz.model.trading.site.param.ListBiSiteOrderParam;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.po.BiPlugPo;
import com.cdz360.biz.model.trading.site.po.BiSiteElectPo;
import com.cdz360.biz.model.trading.site.po.BiSiteFeePo;
import com.cdz360.biz.model.trading.site.po.BiSiteOrderPo;
import com.cdz360.biz.model.trading.site.po.BiSiteSumPo;
import com.cdz360.biz.model.trading.site.po.BiSiteSummaryElectPo;
import com.cdz360.biz.model.trading.site.po.BiSiteSummaryFeePo;
import com.cdz360.biz.model.trading.site.po.BiSiteSummaryPo;
import com.cdz360.biz.model.trading.site.po.CommPo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.site.type.SiteOrderAccountType;
import com.cdz360.biz.model.trading.site.vo.BiSiteOrderVo;
import com.cdz360.biz.model.trading.site.vo.SiteOrderAccountBi;
import com.cdz360.biz.model.trading.site.vo.SiteOrderAccountData;
import com.cdz360.biz.model.trading.site.vo.SiteOrderBiVo;
import com.cdz360.biz.model.trading.site.vo.SiteOrderHlhtAccountBi;
import com.cdz360.biz.model.trading.site.vo.SiteOrderHlhtAccountData;
import com.cdz360.biz.model.trading.site.vo.TimePowerBiVo;
import com.chargerlinkcar.framework.common.domain.type.DashboardOrderType;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class SiteOrderBiService {

    // 返回前端对打采样点
//    private static final int MAX_SAMPLE_SIZE = Constant.MAX_SAMPLE_SIZE;

    @Autowired
    private BiSiteOrderRoDs biSiteOrderRoDs;

    @Autowired
    private OrderBiService orderBiService;

    @Autowired
    private CommOrderBiService commOrderBiService;

    @Autowired
    private BiPlugRoDs biPlugRoDs;

    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private OrderBiRoDs orderBiRoDs;

    @Autowired
    private CommercialQueryDs commercialQueryDs;

    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;

    @Autowired
//    private TRCommercialRoDs trCommercialRoDs;
    private CommRoDs commRoDs;

    @Autowired
    private BiSiteOrderAccountRoDs biSiteOrderAccountRoDs;

    @Autowired
    private BiSiteOrderHlhtRoDs biSiteOrderHlhtRoDs;
    @Autowired
    private ExcelFileService excelFileService;
    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    public static BigDecimal initUserRate(long duration, long available) {
        if (0 == available) {
            return BigDecimal.ZERO;
        }

        return BigDecimal.valueOf(duration)
            .divide(BigDecimal.valueOf(available), 2, RoundingMode.HALF_UP);
    }

    /**
     * 统计场站的分时电量
     *
     * @param param
     * @return
     */
    public List<OrderCount> siteCountBi(SiteBiParam param) {
        // 校验参数
        Optional<String> validResult = this.validParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        // 调整前端入参
        param.resetTime();

        // 库中采样数据
        List<BiSiteOrderPo> biList;
        List<BiSiteOrderPo> userList;
        Map<Date, List<BiSiteOrderPo>> userCollect = new HashMap<>();
        if (StringUtils.isNotBlank(param.getSiteId())) {
            biList = biSiteOrderRoDs.selectBySiteIdWhen(
                param.getSiteId(),
                BiDependOnType.valueOf(param.getDependOnTimeType().name()),
                new Date(param.getStartTime()), new Date(param.getEndTime()));
            userList = biSiteOrderRoDs.selectUserBySiteIdWhen(
                param.getSiteId(),
                BiDependOnType.valueOf(param.getDependOnTimeType().name()).toString(),
                SiteBiSampleType.valueOf(param.getSampleType().name()).toString(),
                new Date(param.getStartTime()), new Date(param.getEndTime()));
            userCollect = userList.stream()
                .map(bi -> bi.setTime(this.resetDate(param.getSampleType(), bi.getTime())))
                .collect(Collectors.groupingBy(BiSiteOrderPo::getTime));
        } else {
            CommPo commercialById = commRoDs.getCommById(param.getCommId());
            IotAssert.isNotNull(commercialById, "找不到对应商户");
            IotAssert.isNotBlank(commercialById.getIdChain(), "对应商户idChain不能为空");

            biList = biSiteOrderRoDs.selectByIdChainWhen(
                commercialById.getIdChain(),
                BiDependOnType.valueOf(param.getDependOnTimeType().name()),
                new Date(param.getStartTime()), new Date(param.getEndTime()));
        }

        // 按采样方式整合数据点
        // 注: 仅采样24点数据(24小时)
        if (param.getSampleType() == SiteBiSampleType.HOUR) {
            List<OrderCount> result = this.divisionResult(biList.stream()
                .map(this::map2Division)
                .collect(Collectors.toList()), param);

            if (StringUtils.isNotEmpty(param.getSiteId())) {
                for (OrderCount tmp : result) {
                    if (tmp != null && tmp.getTime() != null) {
                        if (userCollect.containsKey(tmp.getTime())) {
                            List<BiSiteOrderPo> biSiteOrderPos = userCollect.get(tmp.getTime());
                            if (CollectionUtils.isNotEmpty(biSiteOrderPos)) {
                                tmp.setUserCount(biSiteOrderPos.get(0).getUserCount());
                            }
                        }
                    }
                }
            }
            return result;
        }

        // 其他采样方式
        List<OrderCount> result = new ArrayList<>();

        // 按天采样
        // 注: 仅采样24点数据(24天)
        // 按月采样
        // 注: 仅采样24点数据(24月)

        // 调整时间，分组统计整合返回: 前端控制数据时间大小
        Map<Date, List<BiSiteOrderPo>> collect = biList.stream()
            .map(bi -> bi.setTime(this.resetDate(param.getSampleType(), bi.getTime())))
            .collect(Collectors.groupingBy(BiSiteOrderPo::getTime));

        //活跃手机号
        Map<Date, List<BiSiteOrderPo>> finalUserCollect = userCollect;
        collect.forEach((k, v) -> {
            OrderCount d = new OrderCount();
            d.setSiteId(param.getSiteId())
                .setTime(k);
            d.setOrderCount(v.stream().mapToLong(BiSiteOrderPo::getOrderCount).sum());

            //活跃用户汇总
            if (StringUtils.isNotEmpty(param.getSiteId())) {
                if (finalUserCollect.containsKey(k)) {
                    List<BiSiteOrderPo> biSiteOrderPos = finalUserCollect.get(k);
                    if (CollectionUtils.isNotEmpty(biSiteOrderPos)) {
                        d.setUserCount(biSiteOrderPos.get(0).getUserCount());
                    }
                }
            }
            //场站活跃用户
            result.add(d);
        });

        // 调整排序
        result.sort(Comparator.comparing(OrderCount::getTime).reversed());
        return this.divisionResult(result, param);
    }

    private List<OrderCount> divisionResult(List<OrderCount> list, SiteBiParam param) {
        List<LocalDateTime> timeList = reviseResult(param);
        if (CollectionUtils.isEmpty(timeList)) {
            throw new DcArgumentException("开始/结束时间不正确");
        }

//        if (timeList.size() < Constant.MAX_SAMPLE_SIZE) {
//            return list;
//        }

        Map<Date, List<OrderCount>> orderMap = list.stream()
            .collect(Collectors.groupingBy(OrderCount::getTime));

        List<OrderCount> result = new ArrayList<>();
        timeList.stream().limit(Constant.MAX_SAMPLE_SIZE).forEach(time -> {

            List<OrderCount> orderCounts = orderMap.get(
                Date.from(time.atZone(ZoneOffset.of("+8")).toInstant()));
            Optional<OrderCount> first;
            if (orderCounts == null) {
                first = Optional.empty();
            } else {
                first = orderCounts.stream()
                    .reduce((x, y) -> {
                        return x.setSiteId("")
                            .setOrderCount((x.getOrderCount() == null ? 0 : x.getOrderCount()) +
                                (y.getOrderCount() == null ? 0 : y.getOrderCount()));
                    });
            }

//            Optional<OrderCount> first = list.stream().filter(d -> d.getTime().toInstant().atOffset(ZoneOffset.of("+8"))
//                    .toLocalDateTime().isEqual(time)).findFirst();
            if (first.isPresent()) {
                result.add(first.get());
            } else {
                OrderCount division = new OrderCount()
                    .setTime(
                        new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()))
                    .setSiteId(param.getSiteId());
                result.add(division);
            }
        });

        return result;
    }

    private List<SiteUtilization> utilizationResult(List<SiteUtilization> list, SiteBiParam param) {
        List<LocalDateTime> timeList = reviseResult(param);
        if (CollectionUtils.isEmpty(timeList)) {
            throw new DcArgumentException("开始/结束时间不正确");
        }
//        if (timeList.size() < Constant.MAX_SAMPLE_SIZE) {
//            return list;
//        }

        List<SiteUtilization> result = new ArrayList<>();
        timeList.forEach(time -> {
            Optional<SiteUtilization> first = list.stream()
                .filter(d -> d.getDate().toInstant().atOffset(ZoneOffset.of("+8"))
                    .toLocalDateTime().isEqual(time)).findFirst();
            if (first.isPresent()) {
                result.add(first.get());
            } else {
                SiteUtilization utilization = new SiteUtilization()
                    .setDate(
                        new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()))
                    .setSiteId(param.getSiteId());
                result.add(utilization);
            }
        });

        return result;
    }

    /**
     * 调整数据返回，固定长度
     *
     * @return
     */
    private List<LocalDateTime> reviseResult(SiteBiParam param) {
        if (param.getFromTime() == null || param.getToTime() == null) {
            param.resetTime();
        }

        LocalDateTime start = param.getFromTime().toInstant()
            .atOffset(ZoneOffset.of("+8"))
            .toLocalDateTime();
        LocalDateTime end = param.getToTime().toInstant()
            .atOffset(ZoneOffset.of("+8"))
            .toLocalDateTime();

        List<LocalDateTime> timeList = new ArrayList<>();
        while (start.isBefore(end)) {
            timeList.add(start);
            if (param.getSampleType() == SiteBiSampleType.HOUR) {
                start = start.plusHours(1);
            } else if (param.getSampleType() == SiteBiSampleType.DAY) {
                start = start.plusDays(1);
            } else {
                start = start.plusMonths(1);
            }
        }

        log.info("time size = {}", timeList.size());
        return timeList;
    }

    /**
     * 调整数据返回，固定长度
     *
     * @return
     */
    private List<String> timeRevise(List<LocalDateTime> timeList, SiteBiSampleType sampleType) {
        DateTimeFormatter formatter;

        switch (sampleType) {
            case HOUR:
                formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH");
                break;
            case DAY:
                formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                break;
            case MONTH:
                formatter = DateTimeFormatter.ofPattern("yyyy-MM");
                break;
            default:
                formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        }

        return timeList.stream().map(formatter::format)
            .collect(Collectors.toList());
    }

    /**
     * 调整时间点
     *
     * @param sampleType
     * @param time
     * @return
     */
    private Date resetDate(SiteBiSampleType sampleType, Date time) {
        // 分秒都为0
        LocalDateTime local = time.toInstant().atOffset(ZoneOffset.of("+8"))
            .toLocalDateTime().withMinute(0).withSecond(0).withNano(0);

        if (sampleType == SiteBiSampleType.DAY ||
            sampleType == SiteBiSampleType.MONTH) {
            local = local.withHour(0);
        }

        if (sampleType == SiteBiSampleType.MONTH) {
            // 当月第一天
            local = local.with(TemporalAdjusters.firstDayOfMonth());
        }

        return new Date(local.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
    }

    private OrderCount map2Division(BiSiteOrderPo po) {
        OrderCount result = new OrderCount();
        BeanUtils.copyProperties(po, result);
        return result;
    }

    private Optional<String> validParam(SiteBiParam param) {
        if (StringUtils.isBlank(param.getSiteId()) && param.getCommId() == null) {
//            return Optional.of("场站Id没有提供");
            return Optional.of("请提供场站Id或商户Id");
        }

        if (null == param.getStartTime() || null == param.getEndTime()) {
            return Optional.of("开始结束时间没有提供");
        }

        if (param.getStartTime() >= param.getEndTime()) {
            return Optional.of("开始时间大于等于结束时间，不合理");
        }

        if (reviseResult(param).size() > Constant.MAX_SAMPLE_SIZE) {
            return Optional.of(
                "时间跨度大于最大值（" + Constant.MAX_SAMPLE_SIZE + "），请选择正确的开始/结束时间");
        }

        return Optional.empty();
    }

    private Optional<String> validBiParam(SiteBiParam param) {

        if (null == param.getBiContent()) {
            return Optional.of("统计类型不能为空");
        }

        if (null == param.getStartTime() || null == param.getEndTime()) {
            return Optional.of("开始结束时间没有提供");
        }

        if (param.getStartTime() >= param.getEndTime()) {
            return Optional.of("开始时间大于等于结束时间，不合理");
        }

        if (reviseResult(param).size() > Constant.MAX_SAMPLE_SIZE) {
            return Optional.of(
                "时间跨度大于最大值（" + Constant.MAX_SAMPLE_SIZE + "），请选择正确的开始/结束时间");
        }

        return Optional.empty();
    }

    /**
     * 场站用户充电排行榜
     *
     * @param param
     * @return
     */
    public List<UserOrderElec> userChargeDivisionBi(SiteBiTopParam param) {

        // 参数时间
        Optional<String> validResult = this.validParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        if (null == param.getTopCount()) {
            log.info("查询参数无效: top没有提供");
            throw new DcArgumentException("参数有误，请提供top参数");
        }

        // 充值以下时间
        param.resetTime();

        return orderBiService.userChargeDivisionBi(param);
    }

    /**
     * 桩时长利用率
     *
     * @param param
     * @return
     */
    public List<SiteUtilization> utilizationBi(SiteBiParam param) {

        // 参数校验
        Optional<String> validResult = this.validParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        // 充值以下时间
        param.resetTime();

        // 库中采样数据
        List<BiPlugPo> poList = biPlugRoDs.selectBySiteIdWhen(
            param.getSiteId(),
            null, null,
            new Date(param.getStartTime()), new Date(param.getEndTime()));

        // 其他采样方式
        List<SiteUtilization> result = new ArrayList<>();

        // 按天采样
        // 注: 仅采样24点数据(24天)
        // 按月采样
        // 注: 仅采样24点数据(24月)

        // 调整时间，分组统计整合返回: 前端控制数据时间大小
        Map<Date, List<BiPlugPo>> collect = poList.stream()
            .map(po -> po.setDate(this.resetDate(param.getSampleType(), po.getDate())))
            .collect(Collectors.groupingBy(BiPlugPo::getDate));

        collect.forEach((k, v) -> {
            SiteUtilization u = new SiteUtilization();
            u.setSiteId(param.getSiteId())
                .setDate(k);

            u.setDuration(v.stream().mapToLong(BiPlugPo::getDuration).sum());

            // 有效使用时间
            u.setUseRate(
                initUserRate(u.getDuration(), v.stream().mapToLong(BiPlugPo::getAvailable).sum()));

            result.add(u);
        });

        // 调整排序
        result.sort(Comparator.comparing(SiteUtilization::getDate));
        return this.utilizationResult(result, param);
    }

    /**
     * 场站桩利用率排行榜
     *
     * @param param
     * @return
     */
    public List<PlugUtilization> plugUtilizationBi(SiteBiTopParam param) {

        // 参数校验
        Optional<String> validResult = this.validParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        if (null == param.getTopCount()) {
            log.info("查询参数无效: 没有查询数据量");
            throw new DcArgumentException("参数有误，请提供");
        }

        // 充值以下时间
        param.resetTime();

        // 库中采样数据
        List<BiPlugPo> poList = biPlugRoDs.selectBySiteIdWhen(
            param.getSiteId(),
            null, null,
            new Date(param.getStartTime()), new Date(param.getEndTime()));

        // 其他采样方式
        List<PlugUtilization> result = new ArrayList<>();

        // 按天采样
        // 注: 仅采样24点数据(24天)
        // 按月采样
        // 注: 仅采样24点数据(24月)

        // 调整时间，分组统计整合返回: 前端控制数据时间大小
        Map<String, List<BiPlugPo>> collect = poList.stream()
            .map(po -> po.setDate(this.resetDate(param.getSampleType(), po.getDate())))
            .collect(Collectors.groupingBy(BiPlugPo::getEvseNo));

        collect.forEach((k, v) -> {
            // 按照枪分组
            Map<Integer, List<BiPlugPo>> plugMap = v.stream()
                .collect(Collectors.groupingBy(BiPlugPo::getPlugId));
            plugMap.forEach((p, l) -> {
                PlugUtilization u = new PlugUtilization();
                u.setSiteId(param.getSiteId())
                    .setEvseNo(k)
                    .setPlugId(p);

                u.setDuration(l.stream().mapToLong(BiPlugPo::getDuration).sum());

                // 有效使用时间
                u.setUseRate(initUserRate(u.getDuration(),
                    v.stream().mapToLong(BiPlugPo::getAvailable).sum()));

                result.add(u);
            });
        });

        // 调整排序
        // README: 返回数据还包含其他信息，在 ant 中赋值
        result.sort(Comparator.comparing(PlugUtilization::getUseRate));
        return result.stream().limit(param.getTopCount()).collect(Collectors.toList());
    }


    /**
     * 按场站分组统计一段时间内的电量,收入,订单数
     *
     * @param param
     * @return
     */
    public List<SiteOrderBiVo> getSiteBiList(SiteBiParam param) {
        if (param.getStart() == null) {
            param.setStart(0L);
        }
        if (param.getSize() == null) {
            param.setSize(10);
        }
        return this.biSiteOrderRoDs.getSiteBiList(param);
    }

    /**
     * 查询最近几天(自然日)的时间汇总功率统计
     *
     * @param commIdChain
     * @param lastDays    1,查今天; 2,查昨天开始; 3,查前天开始
     * @return
     */
    public List<TimePowerBiVo> getTimePowerBiList(@Nullable String commIdChain,
        @Nullable String siteId, int lastDays) {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDate startTime = endTime.plusDays(1 - lastDays).toLocalDate();

        return this.biSiteOrderRoDs.getTimePowerBiList(commIdChain, siteId,
            Date.from(startTime.atStartOfDay(ZoneId.systemDefault()).toInstant()),
            Date.from(endTime.toInstant(ZoneOffset.of("+8")))
        );
    }

    /**
     * 场站报表汇总
     *
     * @param param
     * @return
     */
    public List<BiSiteSumPo> getSummaryListBySite(SiteBiParam param) {
        // 校验参数
        Optional<String> validResult = this.validBiParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        // 调整前端入参
        param.resetTime();
        // 库中采样数据
        List<BiSiteSumPo> biList = biSiteOrderRoDs.getSummaryListBySite(
            param.getSiteIdList(), //站点列表
            BiDependOnType.valueOf(param.getDependOnTimeType().name()), //统计时间类型
            param.getBiContent().getDetail(),
            SiteBiSampleType.valueOf(param.getSampleType().name()).toString(),
            param.getCommIdChain(),
            param.getGids(),
            new Date(param.getStartTime()), new Date(param.getEndTime()),
            0L, 9999, param.getSort(), param.getIncludedHlhtSite());

        return this.divisionResult1(biList, param);
    }

    /**
     * 根据站点汇总
     *
     * @param param
     * @return
     */
    public ListResponse<BiSiteSummaryPo> getBiSiteList(SiteBiParam param) {
        log.info("param = {}", param);
        // 校验参数
        Optional<String> validResult = this.validBiParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        // 调整前端入参
        param.resetTime();
        ListSiteParam siteParam = new ListSiteParam();
        siteParam.setCommIdChain(param.getCommIdChain())
            .setSiteIdList(param.getSiteIdList())
            .setGids(param.getGids())
            .setIncludedHlhtSite(param.getIncludedHlhtSite())
            .setStatusList(List.of(SiteStatus.ONLINE, SiteStatus.OPENING, SiteStatus.UNAVAILABLE))
            .setStart(param.getStart())
            .setSize(param.getSize() == null ? 1000 : param.getSize());
        Long siteCount = siteRoDs.countSite(siteParam);

        List<BiSiteSumPo> biList = biSiteOrderRoDs.getSummaryListBySite(
            param.getSiteIdList(), //站点列表
            BiDependOnType.valueOf(param.getDependOnTimeType().name()), //统计时间类型
            param.getBiContent().getDetail(),
            SiteBiSampleType.valueOf(param.getSampleType().name()).toString(),
            param.getCommIdChain(),
            param.getGids(),
            new Date(param.getStartTime()), new Date(param.getEndTime()),
            param.getStart(), param.getSize(), param.getSort(), param.getIncludedHlhtSite());
        Map<String, List<BiSiteSumPo>> mapList = biList.stream()
            .collect(Collectors.groupingBy(BiSiteSumPo::getName));

        List<BiSiteSummaryPo> biSiteList = new ArrayList<>();
        mapList.forEach((key, value) -> {
            List<BiSiteSumPo> biSiteSum = this.divisionResult1(value, param);
            BiSiteSummaryPo result = new BiSiteSummaryPo();
            result.setBiSummary(biSiteSum);
            result.setName(key);
            result.setTotal(biSiteSum.stream().map(BiSiteSumPo::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
            biSiteList.add(result);
            ;
        });
        List<BiSiteSummaryPo> biSiteLists = new ArrayList<>();

        if (param.getSort().equals("ASC")) {
            biSiteLists = biSiteList.stream()
                .sorted(Comparator.comparing(BiSiteSummaryPo::getTotal))
                .collect(Collectors.toList());
        } else {
            biSiteLists = biSiteList.stream()
                .sorted(Comparator.comparing(BiSiteSummaryPo::getTotal).reversed())
                .collect(Collectors.toList());
        }
        //添加汇总信息
        BiSiteSummaryPo biSummary = new BiSiteSummaryPo();
        List<BiSiteSumPo> result = commOrderBiService.getSummaryByCondition(param);
        BigDecimal totalQuantity = result.stream().map(BiSiteSumPo::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        biSummary.setTotal(totalQuantity);
        biSummary.setBiSummary(result);
        biSummary.setName("accountSummary");
        biSiteLists.add(biSummary);
        return new ListResponse<>(biSiteLists, siteCount);
    }

    private List<BiSiteSumPo> divisionResult1(List<BiSiteSumPo> list, SiteBiParam param) {
        List<LocalDateTime> timeList = reviseResult(param);
        if (CollectionUtils.isEmpty(timeList)) {
            throw new DcArgumentException("开始/结束时间不正确");
        }

//        if (timeList.size() < Constant.MAX_SAMPLE_SIZE) {
//            return list;
//        }
        List<BiSiteSumPo> result = new ArrayList<>();
        if (param.getSampleType().toString().equals("DAY")) {
            timeList.stream().limit(Constant.MAX_SAMPLE_SIZE).forEach(time -> {
                Optional<BiSiteSumPo> first = list.stream().filter(d -> d.getDay() != null)
                    .filter(d -> d.getDay().toInstant().atOffset(ZoneOffset.of("+8"))
                        .toLocalDateTime().isEqual(time)).findFirst();
                if (first.isPresent()) {
                    result.add(first.get());
                } else {
                    BiSiteSumPo division = new BiSiteSumPo()
                        .setDay(new Date(
                            time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()))
                        .setName(list.get(0).getName())
                        .setAmount(BigDecimal.ZERO);

                    result.add(division);
                }
            });
        } else {
            timeList.stream().limit(Constant.MAX_SAMPLE_SIZE).forEach(time -> {
                List<BiSiteSumPo> dataList = list.stream()
                    .filter(d -> d.getMonth() != null)
                    .filter(d -> {
                        LocalDateTime localDateTime = d.getMonth().toInstant()
                            .atOffset(ZoneOffset.of("+8"))
                            .toLocalDateTime();
                        return localDateTime.getYear() == time.getYear() &&
                            localDateTime.getMonthValue() == time.getMonthValue();
                    })
                    .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(dataList)) {
                    BiSiteSumPo data = dataList.get(0);
                    data.setMonth(
                        new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
                    data.setAmount(dataList.stream().map(BiSiteSumPo::getAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                    result.add(data);
                } else {
                    BiSiteSumPo division = new BiSiteSumPo()
                        .setMonth(new Date(
                            time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()))
                        .setName(list.get(0).getName())
                        .setAmount(BigDecimal.ZERO);
                    result.add(division);
                }
            });
        }
        return result;
    }

    private List<BiSiteElectPo> divisionResult2(List<BiSiteElectPo> list, SiteBiParam param) {
        List<LocalDateTime> timeList = reviseResult(param);
        if (CollectionUtils.isEmpty(timeList)) {
            throw new DcArgumentException("开始/结束时间不正确");
        }

//        if (timeList.size() < Constant.MAX_SAMPLE_SIZE) {
//            return list;
//        }
        List<BiSiteElectPo> result = new ArrayList<>();
        if (param.getSampleType().toString().equals("DAY")) {
            timeList.stream().limit(Constant.MAX_SAMPLE_SIZE).forEach(time -> {
                Optional<BiSiteElectPo> first = list.stream().filter(d -> d.getDay() != null)
                    .filter(d -> d.getDay().toInstant().atOffset(ZoneOffset.of("+8"))
                        .toLocalDateTime().isEqual(time)).findFirst();
                if (first.isPresent()) {
                    result.add(first.get());
                } else {
                    BiSiteElectPo division = new BiSiteElectPo()
                        .setDay(new Date(
                            time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
                    result.add(division);
                }
            });
        } else {
            timeList.stream().limit(Constant.MAX_SAMPLE_SIZE).forEach(time -> {
                List<BiSiteElectPo> dataList = list.stream().filter(d -> d.getMonth() != null)
                    .filter(d -> {
                        LocalDateTime localDateTime = d.getMonth().toInstant()
                            .atOffset(ZoneOffset.of("+8"))
                            .toLocalDateTime();
                        return localDateTime.getYear() == time.getYear() &&
                            localDateTime.getMonthValue() == time.getMonthValue();
                    })
                    .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(dataList)) {
                    BiSiteElectPo data = dataList.get(0);
                    data.setMonth(
                        new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
                    data.setElectricity(dataList.stream().map(BiSiteElectPo::getElectricity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                    data.setElecTag1(dataList.stream().map(BiSiteElectPo::getElecTag1)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                    data.setElecTag2(dataList.stream().map(BiSiteElectPo::getElecTag2)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                    data.setElecTag3(dataList.stream().map(BiSiteElectPo::getElecTag3)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                    data.setElecTag4(dataList.stream().map(BiSiteElectPo::getElecTag4)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                    data.setElectricity(dataList.stream().map(BiSiteElectPo::getElectricity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));

                    data.setElectFee(dataList.stream().map(BiSiteElectPo::getElectFee)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                    data.setServeFee(dataList.stream().map(BiSiteElectPo::getServeFee)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                    data.setFee(dataList.stream().map(BiSiteElectPo::getFee)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                    result.add(data);
                } else {
                    BiSiteElectPo division = new BiSiteElectPo()
                        .setMonth(new Date(
                            time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
                    result.add(division);
                }
            });
        }
        return result;
    }

    private List<BiSiteFeePo> divisionResult3(List<BiSiteFeePo> list, SiteBiParam param) {
        List<LocalDateTime> timeList = reviseResult(param);
        if (CollectionUtils.isEmpty(timeList)) {
            throw new DcArgumentException("开始/结束时间不正确");
        }

//        if (timeList.size() < Constant.MAX_SAMPLE_SIZE) {
//            return list;
//        }
        List<BiSiteFeePo> result = new ArrayList<>();
        if (param.getSampleType().toString().equals("DAY")) {
            timeList.stream().limit(Constant.MAX_SAMPLE_SIZE).forEach(time -> {
                Optional<BiSiteFeePo> first = list.stream()
                    .filter(d -> d.getDay().toInstant().atOffset(ZoneOffset.of("+8"))
                        .toLocalDateTime().isEqual(time)).findFirst();
                if (first.isPresent()) {
                    result.add(first.get());
                } else {
                    BiSiteFeePo division = new BiSiteFeePo()
                        .setDay(new Date(
                            time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
                    result.add(division);
                }
            });
        } else {
            timeList.stream().limit(Constant.MAX_SAMPLE_SIZE).forEach(time -> {
                List<BiSiteFeePo> dataList = list.stream()
                    .filter(d -> {
                        LocalDateTime localDateTime = d.getMonth().toInstant()
                            .atOffset(ZoneOffset.of("+8"))
                            .toLocalDateTime();
                        return localDateTime.getYear() == time.getYear() &&
                            localDateTime.getMonthValue() == time.getMonthValue();
                    })
                    .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(dataList)) {
                    BiSiteFeePo data = dataList.get(0);
                    data.setMonth(
                        new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
                    data.setFee(dataList.stream().map(BiSiteFeePo::getFee)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                    data.setElecFee(dataList.stream().map(BiSiteFeePo::getElecFee)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                    data.setServFee(dataList.stream().map(BiSiteFeePo::getServFee)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
                    result.add(data);
                } else {
                    BiSiteFeePo division = new BiSiteFeePo()
                        .setMonth(new Date(
                            time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
                    result.add(division);
                }
            });
        }
        return result;
    }

    public ChargeOrderCommBiVo getLastBi(Integer days, Long commId, String idChain) {

        CommercialSimpleVo comm = commercialQueryDs.getCommById(commId);
        IotAssert.isNotNull(comm, "找不到改商户" + commId);
        IotAssert.isNotBlank(comm.getIdChain(), "商户的idChain不能为空");

        Date endTime = DateUtil.getThisDate(new Date());

        int daysRecently = -30;
        if (days != null) {
            daysRecently = -days.intValue();
        }

        Date startTime = DateUtil.addDate(endTime, daysRecently);

        ChargeOrderCommBiVo lastBi = biSiteOrderRoDs.getLastBi(comm.getIdChain(), startTime,
            endTime);
        return lastBi == null ? new ChargeOrderCommBiVo() : lastBi;
    }

    public Mono<List<CommStatisticBiVo>> chargeDataSample(DataBiParam param) {
        IotAssert.isNotNull(param.getSampleType(), "数据汇总单位不能为空(sampleType)");

        switch (param.getSampleType()) {
            case HOUR:
            case DAY:
            case MONTH:
            case YEAR:
                DataBiParam.rangeDate(param);
                return Mono.just(param)
                    .map(biSiteOrderRoDs::chargeDataSample);
            default:
                log.error("数据汇总单位不支持: {}", param.getSampleType());
                throw new DcArgumentException("数据汇总单位不支持");
        }
    }

    public List<CommStatisticBiVo> getLastCommBi(Integer days, Long commId) {

        String commIdChain = null;
        if (commId != null) {
            CommercialSimpleVo comm = commercialQueryDs.getCommById(commId);
            IotAssert.isNotNull(comm, "找不到该商户" + commId);
            IotAssert.isNotBlank(comm.getIdChain(), "商户的idChain不能为空");
            commIdChain = comm.getIdChain();
        }

        Date endTime = DateUtil.getThisDate(new Date());

        int daysRecently = -30;
        if (days != null) {
            daysRecently = -days.intValue();
        }

        Date startTime = DateUtil.addDate(endTime, daysRecently);

        return biSiteOrderRoDs.getLastCommBi(commIdChain, startTime, endTime);
    }

    public List<SubCommStatisticBiVo> getLastCommTopBi(Integer days,
        Long commId,
//                                                       String idChain,
        DashboardOrderType orderBy) {

        final Date endTime = DateUtil.getThisDate(new Date());

        final int daysRecently = days != null ? days.intValue() : -30;
//        int daysRecently = -30;
//        if(days != null) {
//            daysRecently = days.intValue();
//        }

        final Date startTime = DateUtil.addDate(endTime, daysRecently);

        ListResponse<SiteAndPlugBiVoEx> siteAndPlugStatusSubComm = iotDeviceMgmFeignClient.getSiteAndPlugStatusSubComm(
            commId);
        FeignResponseValidate.check(siteAndPlugStatusSubComm);

        Map<Long, SiteAndPlugBiVoEx> plugBiMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(siteAndPlugStatusSubComm.getData())) {
            siteAndPlugStatusSubComm.getData().stream().forEach(e -> {
                if (plugBiMap.get(e.getCommId()) == null) {
                    plugBiMap.put(e.getCommId(), e);
                } else {
                    log.error("存在重复的商户: {}, {}", e, plugBiMap);
                }
            });
        }

        // 获取相关子商户列表
        List<CommercialSimpleVo> subComm = commercialQueryDs.getCommByPid(commId);

        Map<Long, HlhtSiteBiVo> hlhtBiMap = siteRoDs.getHlhtSiteBiVo(commId, subComm);

        // 根据子商户列表，填充统计信息
        List<SubCommStatisticBiVo> collect = subComm.stream().map(e -> {
                SubCommStatisticBiVo biVoEx = biSiteOrderRoDs.getLastCommBiTop(null, e.getIdChain(),
                    startTime, endTime);
                if (biVoEx == null) {
                    // 返回一个数据全0的商户数据
                    biVoEx = new SubCommStatisticBiVo();
                }
                biVoEx.setCommId(e.getId());
                biVoEx.setCommName(e.getCommName());

                if (plugBiMap.get(e.getId()) != null) {
                    biVoEx.setSiteCount(plugBiMap.get(e.getId()).getSiteCount());
                    biVoEx.setPlugCount(plugBiMap.get(e.getId()).getPlugCount());
                    biVoEx.setPower(plugBiMap.get(e.getId()).getPower());
                }

                return biVoEx;
            }).filter(Objects::nonNull)
            .collect(Collectors.toList());

        if (collect == null) {
            collect = new ArrayList<>();
        }

        // 无论如何，把自己加到列表中
        SubCommStatisticBiVo self = biSiteOrderRoDs.getLastCommBiTop(commId, null, startTime,
            endTime);
        if (self == null) {
            self = new SubCommStatisticBiVo();
        }
        CommercialSimpleVo selfComm = commercialQueryDs.getCommById(commId);
        self.setCommId(selfComm.getId());
        self.setCommName(selfComm.getCommName() + " 直属");
        collect.add(self);

        BigDecimal daysAllHour = new BigDecimal(24 * Math.abs(daysRecently));
        BigDecimal daysAll = new BigDecimal(Math.abs(daysRecently));
        collect.stream().forEach(e -> {
            BigDecimal sumPowerTotal = e.getSumPower().multiply(daysAllHour);
            if (sumPowerTotal.compareTo(BigDecimal.ZERO) > 0) {
                e.setPowerUseRate(
                    e.getElectricity().divide(sumPowerTotal, 4, RoundingMode.HALF_UP));
            }
            BigDecimal sumPower = e.getSumPower().multiply(daysAll);
            if (sumPower.compareTo(BigDecimal.ZERO) > 0) {
                e.setServFeePerPower(e.getServFee().divide(sumPower, 2, RoundingMode.HALF_UP));
            }
        });

        List<SubCommStatisticBiVo> ret = collect;
        Comparator<SubCommStatisticBiVo> c = null;
        if (DashboardOrderType.ELECTRICITY.equals(orderBy)) {
            c = Comparator.comparing(CommStatisticBiVo::getElectricity);
        } else if (DashboardOrderType.CHARGE_FEE.equals(orderBy)) {
            c = Comparator.comparing(CommStatisticBiVo::getFee);
        } else if (DashboardOrderType.UTILIZATION.equals(orderBy)) {
            c = Comparator.comparing(CommStatisticBiVo::getUseRate);
        } else if (DashboardOrderType.POWER_USE_RATE.equals(orderBy)) {
            c = Comparator.comparing(SubCommStatisticBiVo::getPowerUseRate);
        }
        if (c != null) {
            ret = collect.stream()
                .sorted(Comparator.comparing(SubCommStatisticBiVo::getCommId).reversed())
                .sorted(c.reversed())
                .collect(Collectors.toList());
        }

        // 数据填充
        ret.stream().forEach(e -> {
            if (plugBiMap.get(e.getCommId()) != null) {
                e.setSiteCount(plugBiMap.get(e.getCommId()).getSiteCount());
                e.setPlugCount(plugBiMap.get(e.getCommId()).getPlugCount());
                e.setEvseCount(plugBiMap.get(e.getCommId()).getEvseCount());
                e.setPower(plugBiMap.get(e.getCommId()).getPower());
            }

            HlhtSiteBiVo hlhtSiteBiVo = hlhtBiMap.get(e.getCommId());
            if (hlhtSiteBiVo != null) {
                e.setSiteCount(e.getSiteCount() + hlhtSiteBiVo.getSiteCount());
                e.setPlugCount(e.getPlugCount() + hlhtSiteBiVo.getPlugSum());
                e.setEvseCount(e.getEvseCount() + hlhtSiteBiVo.getEvseSum());
                e.setPower(e.getPower().add(hlhtSiteBiVo.getPowerSum()));
            }

            // 设置日均单枪充电时长
            if (!Integer.valueOf(0).equals(e.getPlugCount())) {
                e.setPlugDailyDuration(
                    e.getDurationTotal() / e.getPlugCount() / Math.abs(daysRecently));
            }
        });

        return ret;
    }

    /**
     * 场站充电走势(电量)
     *
     * @param param
     * @return
     */
    public List<BiSiteSummaryElectPo> getBiElectBySiteList(SiteBiParam param) {
        // 校验参数
        Optional<String> validResult = this.validBiParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        // 调整前端入参
        param.resetTime();
        List<BiSiteElectPo> biList = biSiteOrderRoDs.getElectListBySite(
            param.getSiteIdList(), //站点列表
            BiDependOnType.valueOf(param.getDependOnTimeType().name()), //统计时间类型
            param.getBiContent().getDetail(),
            SiteBiSampleType.valueOf(param.getSampleType().name()).toString(),
            param.getCommIdChain(),
            new Date(param.getStartTime()), new Date(param.getEndTime()),
            param.getStart(), param.getSize());

        Map<String, List<BiSiteElectPo>> map = biList.stream()
            .collect(Collectors.groupingBy(BiSiteElectPo::getName));
        List<BiSiteSummaryElectPo> biSiteList = new ArrayList<>();
        map.forEach((k, v) -> {
            BiSiteSummaryElectPo biSite = new BiSiteSummaryElectPo();
            List<BiSiteElectPo> biSiteSum = this.divisionResult2(v, param);
            biSite.setName(k);
            biSite.setBiSummary(biSiteSum);
            biSiteList.add(biSite);
        });
        return biSiteList;
    }

    /**
     * 场站充电走势(消费)
     *
     * @param param
     * @return
     */
    public List<BiSiteSummaryFeePo> getBiFeeBySiteList(SiteBiParam param) {
        // 校验参数
        Optional<String> validResult = this.validBiParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        // 调整前端入参
        param.resetTime();
        //获取可用站点列表
        ListSiteParam siteParam = new ListSiteParam();
        siteParam.setCommIdChain(param.getCommIdChain())
            .setSiteIdList(param.getSiteIdList())
            .setStatusList(List.of(SiteStatus.ONLINE, SiteStatus.OPENING, SiteStatus.UNAVAILABLE))
            .setStart(param.getStart())
            .setSize(param.getSize() == null ? 1000 : param.getSize());
        ListResponse<SitePo> siteResult = siteRoDs.getSiteList(siteParam);
        Long siteCount = siteRoDs.countSite(siteParam);
        List<BiSiteSummaryFeePo> biSiteList = new ArrayList<>();
        if (siteResult.getData() != null) {
            siteResult.getData().forEach(site -> {
                BiSiteSummaryFeePo biSite = new BiSiteSummaryFeePo();
                List<String> siteIdList = new ArrayList<>();
                siteIdList.add(site.getId());
                // 库中采样数据
                List<BiSiteFeePo> biList = biSiteOrderRoDs.getFeeListBySite(
                    siteIdList, //站点列表
                    param.getCorpIdList(), //商户列表
                    BiDependOnType.valueOf(param.getDependOnTimeType().name()), //统计时间类型
                    param.getBiContent().getDetail(),
                    SiteBiSampleType.valueOf(param.getSampleType().name()).toString(),
                    param.getCommIdChain(),
                    new Date(param.getStartTime()), new Date(param.getEndTime()));

//                月份处理
                if (param.getSampleType().toString().equals(SiteBiSampleType.MONTH)) {
                    Map<Date, List<BiSiteFeePo>> collect = biList.stream()
                        .map(
                            bi -> bi.setMonth(this.resetDate(param.getSampleType(), bi.getMonth())))
                        .collect(Collectors.groupingBy(BiSiteFeePo::getMonth));
                    List<BiSiteFeePo> result = new ArrayList<>();
                    collect.forEach((k, v) -> {
                        BiSiteFeePo d = new BiSiteFeePo();
                        d.setMonth(k);
                        result.add(d);
                    });
                    List<BiSiteFeePo> biSiteSum = this.divisionResult3(result, param);
                    biSite.setName(site.getSiteName());
                    biSite.setBiSummary(biSiteSum);
                    biSiteList.add(biSite);
                } else {

                    List<BiSiteFeePo> biSiteSum = this.divisionResult3(biList, param);
                    biSite.setName(site.getSiteName());
                    biSite.setBiSummary(biSiteSum);
                    biSiteList.add(biSite);
                }
            });

            return biSiteList;
        }
        return null;
    }

    /**
     * 昨日充电站利用率排名
     *
     * @param size
     * @param commId
     * @param orderBy
     * @return
     */
    public List<SiteUtilizationTopVo> getUsageRateBoard(Integer size,
        Long commId,
        OrderByType orderByType,
        OrderType orderBy) {
        CommercialSimpleVo comm = commercialQueryDs.getCommById(commId);
        IotAssert.isNotNull(comm, "找不到改商户" + commId);
        IotAssert.isNotBlank(comm.getIdChain(), "商户的idChain不能为空");

        final Date yesterdayEndTime = DateUtil.getThisDate(new Date());

        final Date yesterdayStartTime = DateUtil.addDate(yesterdayEndTime, -1);

        // 当入参是功率利用率排序时，使用java进行排序
        Comparator<SiteUtilizationTopVo> orderMethod = null;
        if (OrderByType.powerUseRate.equals(orderByType)) {
            orderByType = null;
            if (OrderType.asc.equals(orderBy)) {
                orderMethod = Comparator.comparing(SiteUtilizationTopVo::getPowerUseRate);
            } else {
                orderMethod = Comparator.comparing(SiteUtilizationTopVo::getPowerUseRate)
                    .reversed();
            }
        }

//        final OrderByType FinalOrderByType = orderByType;

        List<SiteUtilizationTopVo> yesterday = biPlugRoDs.getUsageRateBoard(comm.getIdChain(),
            yesterdayStartTime,
            yesterdayEndTime,
            null,
            orderByType,
            orderBy);
        // 计算功率利用率，总充电量 ÷ (商户总额定功率×24时)
        yesterday.stream().forEach(e -> {
            e.setPowerUseRate(computePowerUseRate(e));
        });

        // 设置上下箭头
        if (CollectionUtils.isNotEmpty(yesterday)) {

            final Date b4YesterdayEndTime = yesterdayStartTime;
            final Date b4YesterdayStartTime = DateUtil.addDate(b4YesterdayEndTime, -1);

            // 前天的数据
            List<SiteUtilizationTopVo> b4Yesterday = biPlugRoDs.getUsageRateBoard(comm.getIdChain(),
                b4YesterdayStartTime,
                b4YesterdayEndTime,
                null,
                null,
                null);
            b4Yesterday.forEach(e -> e.setPowerUseRate(computePowerUseRate(e)));

            Map<String, SiteUtilizationTopVo> b4YesterdaySiteMap = b4Yesterday.stream()
                .collect(Collectors.toMap(SiteUtilizationTopVo::getSiteId, p -> p));

            yesterday.forEach(e -> {
                SiteUtilizationTopVo b4y = b4YesterdaySiteMap.get(e.getSiteId());

                // 设置箭头
//                BigDecimal b4yRate;
//                BigDecimal yRate;
//                if(/*OrderByType.powerUseRate.equals(FinalOrderByType)*/ FinalOrderByType == null) {
//                    b4yRate = b4y == null ? BigDecimal.ZERO : b4y.getPowerUseRate();
//                    yRate = e == null ? BigDecimal.ZERO : e.getPowerUseRate();
//                } else {
//                    b4yRate = b4y == null ? BigDecimal.ZERO : b4y.getUseRate();
//                    yRate = e == null ? BigDecimal.ZERO : e.getUseRate();
//                }
//                int arrow = yRate.compareTo(b4yRate);

                e.setUseRateArrow(this.getArrow(e, b4y, SiteUtilizationTopVo::getUseRate));

                e.setPowerUseArrow(this.getArrow(e, b4y, SiteUtilizationTopVo::getPowerUseRate));

                //单枪平均时长
                if (e.getPlugCount() == null || e.getPlugCount() <= 0) {
                    e.setDuration(0L);
                } else {
                    e.setDuration(e.getDurationTotal() / e.getPlugCount());
                }
            });
            if (orderMethod != null) {
                return yesterday.stream()
                    .sorted(orderMethod)
                    .limit(size)
                    .collect(Collectors.toList());
            }
        }
        return yesterday.stream().limit(size).collect(Collectors.toList());
    }

    /**
     * 根据比较值，获取箭头类型
     *
     * @param y   昨天数据
     * @param b4y 前天数据
     * @param fx  取值函数，一般是getPowerUseRate | getUseRate
     * @return
     */
    private UtilizationArrowType getArrow(SiteUtilizationTopVo y,
        SiteUtilizationTopVo b4y,
        Function<SiteUtilizationTopVo, BigDecimal> fx) {
        int arrowVal = this.getArrowVal(y, b4y, fx);
        if (arrowVal > 0) {
            return UtilizationArrowType.UP;
        } else if (arrowVal < 0) {
            return UtilizationArrowType.DOWN;
        } else {
            return UtilizationArrowType.FLAT;
        }
    }

    /**
     * 获取箭头比较值
     *
     * @param y   昨天数据
     * @param b4y 前天数据
     * @param fx  取值函数，一般是getPowerUseRate | getUseRate
     * @return
     */
    private int getArrowVal(SiteUtilizationTopVo y,
        SiteUtilizationTopVo b4y,
        Function<SiteUtilizationTopVo, BigDecimal> fx) {
        BigDecimal b4yRate = b4y == null ? BigDecimal.ZERO : fx.apply(b4y);
        BigDecimal yRate = y == null ? BigDecimal.ZERO : fx.apply(y);

        if (b4yRate == null || yRate == null) {
            return 0;
        }
        return yRate.compareTo(b4yRate);
    }

    private BigDecimal computePowerUseRate(SiteUtilizationTopVo e) {
        if (e.getPower() != null &&
            e.getPowerUse() != null &&
            e.getPowerUse().compareTo(BigDecimal.ZERO) > 0) {
            return e.getPower()
                .divide(e.getPowerUse().multiply(BigDecimal.valueOf(24)), 4, RoundingMode.HALF_UP);
        }
        return BigDecimal.ZERO;
    }

    public Mono<ListResponse<BiSiteOrderVo>> findBiSiteOrderList(ListBiSiteOrderParam param) {
        return Mono.just(param)
            .doOnNext(p -> {
                if (null == p.getStart()) {
                    p.setStart(0L);
                }

                if (null == p.getSize() || p.getSize() > 9999) {
                    p.setSize(10);
                }

                // 结束时间加一天
                if (null != p.getToDate()) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(param.getToDate());
                    calendar.add(Calendar.DATE, 1);
                    param.setToDate(calendar.getTime());
                }
            })
            .map(biSiteOrderRoDs::findBiSiteOrderList)
            .doOnNext(siteBiList -> {
                if (Boolean.TRUE.equals(param.getBiAccount())) {
                    // 查询所有
                    ListBiSiteOrderParam p = new ListBiSiteOrderParam()
                        .setCommIdChain(param.getCommIdChain())
                        .setDependOn(param.getDependOn())
                        .setFromDate(param.getFromDate())
                        .setToDate(param.getToDate())
                        .setSiteIdList(siteBiList.stream()
                            .map(BiSiteOrderVo::getSiteId)
                            .collect(Collectors.toList()));

                    p.setStart(param.getStart())
                        .setSize(param.getSize());
                    List<BiSiteOrderVo> biAccountList = biSiteOrderRoDs.findBiAccountList(p);
                    if (CollectionUtils.isNotEmpty(biAccountList)) {
                        // siteId + name 分组
                        Map<String, List<BiSiteOrderVo>> siteMap = biAccountList.stream()
                            .collect(Collectors.groupingBy(BiSiteOrderVo::getSiteId));

                        siteBiList.forEach(siteBi ->
                            siteBi.setBiAccountOrderList(siteMap.get(siteBi.getSiteId())));
                    }
                }
            })
            .map(RestUtils::buildListResponse)
            .doOnNext(res -> {
                if (Boolean.TRUE.equals(param.getTotal())) {
                    ListSiteParam siteParam = new ListSiteParam();

                    siteParam.setCommIdChain(param.getCommIdChain())
                        .setGids(param.getGidList())
                        .setSiteIdList(param.getSiteIdList())
                        .setIncludedHlhtSite(param.getIncludedHlhtSite())
                        .setStatusList(
                            List.of(SiteStatus.ONLINE, SiteStatus.OPENING, SiteStatus.UNAVAILABLE));
                    res.setTotal(siteRoDs.countSite(siteParam));
                }
            });
    }

    public Mono<SiteOrderAccountBi> getBiSiteOrderAccount(SiteBiParam siteBiParam) {
        return Mono.just(siteBiParam)
            .doOnNext(p -> {
                // 参数校验
            })
            .map(biSiteOrderAccountRoDs::getBiSiteOrderAccount)
            .map(dataList -> {
                // 调整时间，分组统计整合返回: 前端控制数据时间大小
                Map<Date, List<SiteOrderAccountData>> collect = dataList.stream()
                    .map(
                        bi -> bi.setTime(this.resetDate(siteBiParam.getSampleType(), bi.getTime())))
                    .collect(Collectors.groupingBy(SiteOrderAccountData::getTime));

                List<SiteOrderAccountData> resultList = new ArrayList<>();
                collect.forEach((k, v) -> {
                    // 按照账户分类统计
                    Map<SiteOrderAccountType, List<SiteOrderAccountData>> accDataMap = v.stream()
                        .collect(Collectors.groupingBy(SiteOrderAccountData::getAccountType));

                    accDataMap.forEach((type, dList) -> {
                        SiteOrderAccountData data = new SiteOrderAccountData();
                        data.setAccountType(type)
                            .setTime(k)
                            .setOrderFee(dList.stream()
                                .map(SiteOrderAccountData::getOrderFee)
                                .reduce(BigDecimal.ZERO, BigDecimal::add))
                            .setOrderCnt(dList.stream()
                                .mapToLong(SiteOrderAccountData::getOrderCnt)
                                .sum());
                        resultList.add(data);
                    });
                });

                return resultList;
            })
            .map(dataList -> {
                // 时间分片
                List<LocalDateTime> timeList = reviseResult(siteBiParam);
                if (CollectionUtils.isEmpty(timeList)) {
                    throw new DcArgumentException("开始/结束时间不正确");
                }

                // 按照账户分类统计
                Map<SiteOrderAccountType, List<SiteOrderAccountData>> accDataMap = dataList.stream()
                    .collect(Collectors.groupingBy(SiteOrderAccountData::getAccountType));

                // 时间分片
                SiteOrderAccountBi accountBi = new SiteOrderAccountBi();
                accountBi.setSiteId(siteBiParam.getSiteId())
                    .setAccData(new HashMap<>());
                for (SiteOrderAccountType type : SiteOrderAccountType.values()) {
                    List<SiteOrderAccountData> list = accDataMap.get(type);
                    accountBi.getAccData()
                        .put(type, divisionResult(null == list ? List.of() : list, timeList));
                }

                // 显示时间格式
                return accountBi.setTimeList(
                    this.timeRevise(timeList, siteBiParam.getSampleType()));
            });
    }

    private List<SiteOrderHlhtAccountData> divisionResult2(
        List<SiteOrderHlhtAccountData> list, List<LocalDateTime> timeList) {
        List<SiteOrderHlhtAccountData> result = new ArrayList<>();
        timeList.stream().limit(Constant.MAX_SAMPLE_SIZE).forEach(time -> {
            Optional<SiteOrderHlhtAccountData> first = list.stream()
                .filter(d -> d.getTime().toInstant().atOffset(ZoneOffset.of("+8"))
                    .toLocalDateTime().isEqual(time))
                .findFirst();
            if (first.isPresent()) {
                result.add(first.get());
            } else {
                SiteOrderHlhtAccountData division = new SiteOrderHlhtAccountData()
//                        .setCorpId()
//                        .setCorpName()
                    .setTime(
                        new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()))
                    .setOrderFee(BigDecimal.ZERO)
                    .setOrderCnt(0L);
                result.add(division);
            }
        });

        return result;
    }

    private List<SiteOrderHlhtAccountData> divisionResult2X(
        List<BiSiteSumPo> corpProfitList, List<LocalDateTime> timeList) {
        List<SiteOrderHlhtAccountData> result = new ArrayList<>();
        timeList.stream().limit(Constant.MAX_SAMPLE_SIZE).forEach(time -> {
            Optional<BiSiteSumPo> first = corpProfitList.stream().filter(d -> d.getDay() != null)
                .filter(d -> d.getDay().toInstant().atOffset(ZoneOffset.of("+8"))
                    .toLocalDateTime().isEqual(time)).findFirst();

            SiteOrderHlhtAccountData division = new SiteOrderHlhtAccountData()
                .setTime(new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()))
                .setOrderFee(BigDecimal.ZERO)
                .setOrderCnt(0L)
                .setCorpProfit(BigDecimal.ZERO);

            first.ifPresentOrElse(
                data -> result.add(division.setCorpProfit(data.getAmount())),
                () -> result.add(division));
        });

        return result;
    }

    private List<SiteOrderHlhtAccountData> divisionResult2XX(
        List<SiteOrderHlhtAccountData> list, List<BiSiteSumPo> corpProfitList,
        List<LocalDateTime> timeList) {
        List<SiteOrderHlhtAccountData> result = new ArrayList<>();
        timeList.stream().limit(Constant.MAX_SAMPLE_SIZE).forEach(time -> {
            Optional<SiteOrderHlhtAccountData> first = list.stream()
                .filter(d -> d.getTime().toInstant().atOffset(ZoneOffset.of("+8"))
                    .toLocalDateTime().isEqual(time))
                .findFirst();

            // 企业充电订单数据
            SiteOrderHlhtAccountData division = first.orElseGet(() -> new SiteOrderHlhtAccountData()
                .setTime(new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()))
                .setOrderFee(BigDecimal.ZERO)
                .setOrderCnt(0L));

            // 企业收益
            corpProfitList.stream().filter(d -> d.getDay() != null)
                .filter(d -> d.getDay().toInstant().atOffset(ZoneOffset.of("+8"))
                    .toLocalDateTime().isEqual(time))
                .findFirst()
                .ifPresentOrElse(
                    data -> result.add(division.setCorpProfit(data.getAmount())),
                    () -> result.add(division));
        });

        return result;
    }

    private List<SiteOrderAccountData> divisionResult(List<SiteOrderAccountData> list,
        List<LocalDateTime> timeList) {
        List<SiteOrderAccountData> result = new ArrayList<>();
        timeList.stream().limit(Constant.MAX_SAMPLE_SIZE).forEach(time -> {
            Optional<SiteOrderAccountData> first = list.stream()
                .filter(d -> d.getTime().toInstant().atOffset(ZoneOffset.of("+8"))
                    .toLocalDateTime().isEqual(time))
                .findFirst();
            if (first.isPresent()) {
                result.add(first.get());
            } else {
                SiteOrderAccountData division = new SiteOrderAccountData()
//                        .setAccountType()
                    .setTime(
                        new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()))
                    .setOrderFee(BigDecimal.ZERO)
                    .setOrderCnt(0L);
                result.add(division);
            }
        });

        return result;
    }

    public Mono<SiteOrderHlhtAccountBi> getBiSiteOrderHlhtAccount(SiteBiParam siteBiParam) {
        return Mono.just(siteBiParam)
            .doOnNext(p -> {
                // 参数校验

                // FIXME: 根据入参部分查询????
            })
            .map(biSiteOrderHlhtRoDs::getBiSiteOrderHlhtAccount)
            .map(dataList -> {
                // 调整时间，分组统计整合返回: 前端控制数据时间大小
                Map<Date, List<SiteOrderHlhtAccountData>> collect = dataList.stream()
                    .map(
                        bi -> bi.setTime(this.resetDate(siteBiParam.getSampleType(), bi.getTime())))
                    .collect(Collectors.groupingBy(SiteOrderHlhtAccountData::getTime));

                List<SiteOrderHlhtAccountData> resultList = new ArrayList<>();
                collect.forEach((k, v) -> {
                    // 按企业客户ID分类统计
                    Map<Long, List<SiteOrderHlhtAccountData>> accDataMap = v.stream()
                        .collect(Collectors.groupingBy(SiteOrderHlhtAccountData::getCorpId));

                    accDataMap.forEach((corpId, dList) -> {
                        SiteOrderHlhtAccountData data = new SiteOrderHlhtAccountData();
                        data.setCorpId(corpId)
                            .setCorpName(dList.get(0).getCorpName())
                            .setTime(k)
                            .setOrderFee(dList.stream()
                                .map(SiteOrderHlhtAccountData::getOrderFee)
                                .reduce(BigDecimal.ZERO, BigDecimal::add))
                            .setOrderCnt(dList.stream()
                                .mapToLong(SiteOrderHlhtAccountData::getOrderCnt)
                                .sum());
                        resultList.add(data);
                    });
                });

                return resultList;
            })
            .map(dataList -> {
                // 时间分片
                List<LocalDateTime> timeList = reviseResult(siteBiParam);
                if (CollectionUtils.isEmpty(timeList)) {
                    throw new DcArgumentException("开始/结束时间不正确");
                }

                // 时间分片
                SiteOrderHlhtAccountBi accountBi = new SiteOrderHlhtAccountBi();
                accountBi.setSiteId(siteBiParam.getSiteId())
                    .setAccData(new HashMap<>());

                if (CollectionUtils.isNotEmpty(dataList)) {
                    // 按企业客户名称分类统计
                    Map<String, List<SiteOrderHlhtAccountData>> accDataMap = dataList.stream()
                        .collect(Collectors.groupingBy(SiteOrderHlhtAccountData::getCorpName));

                    // 获取企业收益
                    List<BiSiteSumPo> corpProfit = biSiteOrderRoDs.hlhtCorpTotalProfit(
                        siteBiParam.setCorpIdList(dataList.stream()
                            .map(SiteOrderHlhtAccountData::getCorpId)
                            .distinct()
                            .collect(Collectors.toList())));

                    // 统计每个企业总收益
                    Map<String, List<BiSiteSumPo>> profitDataMap = corpProfit.stream()
                        .collect(Collectors.groupingBy(BiSiteSumPo::getName));

                    accDataMap.forEach((k, v) -> {
//                            accountBi.getAccData()
//                                    .put(k, divisionResult2(v, timeList));
                        accountBi.getAccData()
                            .put(k, divisionResult2XX(v, profitDataMap.get(k), timeList));
                    });

//                        siteBiParam.setCorpIdList(dataList.stream()
//                                        .map(SiteOrderHlhtAccountData::getCorpId)
//                                        .distinct()
//                                        .collect(Collectors.toList()))
//                                .setBiContent(ReportBiType.TOTAL_PROFIT)
//                                .setStartTime(siteBiParam.getFromTime().getTime())
//                                .setEndTime(siteBiParam.getToTime().getTime())
//                                .setStart(0L)
//                                .setSize(999);
//                        ListResponse<BiSiteSummaryPo> res = commOrderBiService.getBiCorpList(siteBiParam);
//                        List<BiSiteSummaryPo> biCorpList = res.getData();
//                        // 整理企业收益数据结构
//                        Map<Long, List<BiSiteSumPo>> corpDataMap = new HashMap<>();
//                        biCorpList.forEach(e -> {
//                            if(CollectionUtils.isNotEmpty(e.getBiSummary())) {
//                                e.getBiSummary()
//                                        .stream()
//                                        .filter(corpBi -> Objects.nonNull(corpBi.getCorpId()))
//                                        .findFirst()
//                                        .ifPresent(corpBi -> corpDataMap.put(corpBi.getCorpId(), e.getBiSummary()));
//                            }
//                        });
//                        // 企业收益赋值
//                        accountBi.getAccData().forEach((k, v) -> {
//                            if(CollectionUtils.isNotEmpty(v)) {
//                                v.stream()
//                                        .filter(e -> Objects.nonNull(e.getCorpId()))
//                                        .findFirst()
//                                        .ifPresent(e -> {
//                                            List<BiSiteSumPo> biSiteSumPos = corpDataMap.get(e.getCorpId());
//                                            int l1 = 0;
//                                            int l2 = 0;
//                                            while (l1 < v.size() && l2 < biSiteSumPos.size()) {
//                                                v.get(l1).setCorpProfit(biSiteSumPos.get(l2).getAmount());
////                                            Random random = new Random();
////                                            v.get(l1).setCorpProfit(BigDecimal.valueOf(random.nextInt(100)));
//                                                l1++;
//                                                l2++;
//                                            }
//                                        });
//                            }
//                        });
                }

                // 显示时间格式
                return accountBi.setTimeList(
                    this.timeRevise(timeList, siteBiParam.getSampleType()));
            });
    }


    /**
     * 企业管理平台订单导出
     *
     * @param param
     */
    @Async
    public void exportOfflineEvse(ListEvseParam param, ExcelPosition position) {

        excelFileService.exportExcelFile(
            position.getSubDir(),
            position.getSubFileName(),
            OfflineEvseExportVo.class,
            null,
            "脱机桩列表",
            null,
            null,
            null,
            (index, size) -> {
                param.setSize(size);
                param.setStart((long) (index - 1) * size);
                param.setTotal(false);
                ListResponse<EvseInfoVo> res = dataCoreFeignClient.getOfflineEvseList(param);
                if (res == null || res.getData() == null || res.getData().size() == 0) {
                    return null;
                } else {
                    return res.getData().stream().collect(Collectors.toList());
                }
            },
            e ->
                convert(
                    e.stream().map(x -> (EvseInfoVo) x).collect(Collectors.toList())
                ).stream().collect(Collectors.toList())
        );
    }

    private List<OfflineEvseExportVo> convert(List<EvseInfoVo> evseInfoVos) {
        return evseInfoVos.stream().map(e -> {
            OfflineEvseExportVo vo = new OfflineEvseExportVo();
            vo.setEvseNo(e.getEvseNo())
                .setEvseName(e.getName())
                .setSiteName(e.getSiteName())
                .setCommName(e.getSiteCommName())
                .setEvseModel(e.getModelName())
                .setPower(e.getPower())
                .setFirmwareVer(e.getFirmwareVer())
                .setProtocolVer(e.getProtocolVer())
                .setIccid(e.getIccid());
            if (e.getSupplyType() == SupplyType.AC) {
                vo.setSupplyType("交流");
            } else if (e.getSupplyType() == SupplyType.DC) {
                vo.setSupplyType("直流");
            } else if (e.getSupplyType() == SupplyType.BOTH) {
                vo.setSupplyType("交直一体");
            }
            return vo;
        }).collect(Collectors.toList());
    }

}
