package com.cdz360.biz.bi.domain.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import com.cdz360.biz.model.finance.type.TaxType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2019/11/8 15:23
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode
@ToString
@Schema(description = "充电管理平台客户中营运管理导出使用")
public class PayBillExport2Vo implements Serializable {

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "所属商户id")
    private Long commId;

    @ExcelField(title = "订单号", sort = 1)
    @Schema(description = "充值订单号")
    private String orderId;

    @ExcelField(title = "所属客户", sort = 2)
    @Schema(description = "所属客户名称")
    private String cusName;

    @ExcelField(title = "手机号", sort = 3)
    @Schema(description = "所属客户手机号")
    private String cusPhone;

    @ExcelField(title = "客户类型", sort = 4)
    @Schema(description = "客户类型:UNKNOWN(0)-未知,SYS_USER(1)-商户/平台用户,CUSTOMER(2)-C端客户,CORP_USER(3)-企业用户")
    private String userTypeDesc;
//    private UserType userType;

    @ExcelField(title = "充值来源", sort = 5)
    @Schema(description = "充值来源")
    private String sourceTypeDesc;
//    private DepositSourceType sourceType;

    @ExcelField(title = "充值类型", sort = 6)
    @Schema(description = "充值类型. 增加/减少")
    private String flowTypeDesc;
//    private DepositFlowType flowType;

    @ExcelField(title = "充值账户类型", sort = 7)
    @Schema(description = "充值账户类型. 个人账户/商户会员")
    private String accountTypeDesc;
//    private PayAccountType accountType;

    @ExcelField(title = "账户名称", sort = 8)
    @Schema(description = "付款账户名称: 账户类型 + 账户名称(页面查看字段)")
    private String payAccountName;

    @ExcelField(title = "实际金额（元）", sort = 9)
    @Schema(description = "实际金额, 单位'元', 2位小数")
    private BigDecimal amount;

    @ExcelField(title = "赠送金额（元）", sort = 10)
    @Schema(description = "赠送金额, 单位'元', 2位小数")
    private BigDecimal freeAmount;

    @ExcelField(title = "充值前余额（元）", sort = 11)
    @Schema(description = "充值前余额, 单位'元', 2位小数. 含赠送余额")
    private BigDecimal amountBefore;

    @ExcelField(title = "充值后余额（元）", sort = 12)
    @Schema(description = "充值后余额, 单位'元', 2位小数. 含赠送余额")
    private BigDecimal amountAfter;

    @Schema(description = "账户编号. 个人账户为集团商户编号; 商户会员为商户编号")
    private Long accountCode;

    @ExcelField(title = "支付方式", sort = 13)
    @Schema(description = "支付方式")
    private String payChannelDesc;
//    private PayChannel payChannel;

    /**
     * 支付状态：0未支付或未退款，1已支付或已退款，2支付失败或退款失败 3发起支付或退款成功(C端响应)，4发起支付或退款失败(C端响应)，5超时 6余额已耗尽
     */
    @ExcelField(title = "支付状态", sort = 14)
    @Schema(description = "支付状态")
    private String statusDesc;

    @ExcelField(title = "到账账户类型", sort = 15)
    @Schema(description = "到账账户类型")
    private String flowInAccountTypeDesc;
//    private FlowInAccountType flowInAccountType;

    @ExcelField(title = "金额是否耗尽", sort = 16)
    @Schema(description = "金额是否耗尽")
    private String amountIsExhausted;

    @Schema(description = "flowType为OUT_FLOW时,对应的充值单号")
    private String refBillNo;

    @Schema(description = "发票类型(税种)")
    private TaxType taxType;

    /**
     * 创建时间
     */
    @ExcelField(title = "充值时间", sort = 17, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private String createTime;

    @ExcelField(title = "开票状态", sort = 18)
    @Schema(description = "开票状态")
    private String taxStatusDesc;

    @Schema(description = "发票类型")
    private String invoiceTypeDesc;
//    private InvoiceType invoiceType;

    @Schema(description = "税票号")
    private String taxNo;

    @Schema(description = "寄送状态")
    private String expressStatusDesc;
//    private ExpressStatus expressStatus;

    @Schema(description = "物流公司名称")
    private String expressCompany;

    @Schema(description = "物流单号")
    private String expressNo;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;

    /**
     * 第三方交易号
     */
    @Schema(description = "第三方交易号")
    private String tradeNo;

    /**
     * 支付时间
     */
    @Schema(description = "支付时间")
    private Date payTime;

    /**
     * 支付中心回调返回交易类型：1 为支付(pay)，2 为退款(refund)
     */
    @Schema(description = "支付中心回调返回交易类型")
    private Integer notifyType;

    /**
     * 充电订单号（即充即退）
     */
    @Schema(description = "充电订单号（即充即退）")
    private String chargeOrderNo;

    @Schema(description = "支付账户名称")
    private String outAccountName;

    @Schema(description = "支付账户银行名称")
    private String outBankName;

    @Schema(description = "支付账户银行卡号")
    private String outAccountNo;

    @Schema(description = "收款账户名称")
    private String inAccountName;

    @Schema(description = "收款账户银行名称")
    private String inBankName;

    @Schema(description = "收款账户银行卡号")
    private String inAccountNo;

    @Schema(description = "账户流水序号数组,使用逗号分隔")
    private String flowSeqNo;

    @Schema(description = "操作人类型")
    private String opUserTypeDesc;  // UserType
//    private UserType opUserType;  // UserType

    @Schema(description = "操作人ID")
    private Long opUid;

    @ExcelField(title = "操作人", sort = 20)
    @Schema(description = "操作人")
    private String opName;

    @ExcelField(title = "备注", sort = 19)
    @Schema(description = "备注")
    private String remark;
}
