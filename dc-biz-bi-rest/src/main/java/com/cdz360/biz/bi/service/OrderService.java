package com.cdz360.biz.bi.service;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServerException;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.charge.type.HlhtType;
import com.cdz360.base.model.charge.type.OrderAbnormalReason;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.model.charge.type.OrderStopCode;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.bi.domain.vo.ChargerOrderDailyExportVO;
import com.cdz360.biz.ds.trading.ro.iot.ds.BsBoxRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderTimeDivisionRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.PayBillRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.InvoiceRecordRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.model.cus.basic.vo.UserVo;
import com.cdz360.biz.model.cus.vin.param.VINCarNoParam;
import com.cdz360.biz.model.finance.type.TaxStatus;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import com.cdz360.biz.model.order.type.OrderPayType;
import com.cdz360.biz.model.trading.iot.po.BsBoxPo;
import com.cdz360.biz.model.trading.order.dto.LowKwOrderDto;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.param.ListChargerOrderParamX;
import com.cdz360.biz.model.trading.order.po.ChargerOrderTimeDivision;
import com.cdz360.biz.model.trading.order.vo.ChargeOrderCache;
import com.cdz360.biz.model.trading.order.vo.ChargerOrderTimeDivisionSum;
import com.cdz360.biz.model.trading.order.vo.PayInfo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.site.vo.CorpOrderCountVo;
import com.cdz360.biz.utils.feign.his.HisOrderFeignClient;
import com.cdz360.biz.utils.feign.user.CardFeignClient;
import com.cdz360.biz.utils.feign.user.UserFeignClient;
import com.cdz360.data.cache.RedisChargeOrderReadService;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.framework.common.constant.OrderStatus;
import com.chargerlinkcar.framework.common.domain.OrderCountParam;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.domain.type.CardType;
import com.chargerlinkcar.framework.common.domain.type.DivTimeTagType;
import com.chargerlinkcar.framework.common.domain.type.PrePayType;
import com.chargerlinkcar.framework.common.domain.type.ProcessType;
import com.chargerlinkcar.framework.common.domain.vo.Card;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDailyVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDataVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDetailVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderSample;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderPowerVo;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.utils.DateUtils;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.HistoryDataUtils;
import com.chargerlinkcar.framework.common.utils.MyBeanUtils;
import com.chargerlinkcar.framework.common.utils.PlugNoUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class OrderService {

    @Autowired
    private RedisChargeOrderReadService redisChargeOrderReadService;
    @Autowired
    private ChargerOrderTimeDivisionRoDs chargerOrderTimeDivisionRoDs;
    @Autowired
    private CommercialQueryDs commercialQueryDs;
    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private CardFeignClient cardFeignClient;
    @Autowired
    private PayBillRoDs payBillRoDs;
    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private BsBoxRoDs bsBoxRoDs;

    @Autowired
    private ChargerOrderRoDs chargerOrderRoDs;
    @Autowired
    private RedisIotReadService redisIotReadService;
    @Autowired
    private InvoiceRecordRoDs invoiceRecordRoDs;

    @Autowired
    private HistoryDataUtils historyDataUtils;

    @Autowired
    private HisOrderFeignClient hisOrderFeignClient;

    /**
     * 格式化金额
     *
     * @return
     */
    static String formatData(BigDecimal num, int position) {
        //格式化金额、电量、订单数
        DecimalFormat decimalFormat = new DecimalFormat(",###.00");
        DecimalFormat elecFormat = new DecimalFormat(",###.0000");
        DecimalFormat orderFormat = new DecimalFormat(",###");

        int a = num.compareTo(BigDecimal.valueOf(1));
        if (a == 1 || a == 0) { //大于等于1
            if (position == 2) {
                return decimalFormat.format(num);
            } else if (position == 4) {
                return elecFormat.format(num);
            } else {
                return orderFormat.format(num);
            }
        } else {
            return num.toString();
        }
    }

    static String formatLongData(Long num, long defaultVal) {
        return num == null ? String.valueOf(defaultVal) : String.valueOf(num);
    }

    /**
     * 分页查询订单列表 订单来源类型,0:微信渠道;1:APP在线发起充电;2:APP蓝牙发起充电:3:刷卡充电:4:桩上报离线数据 (离线订单 列表  channelId 为4的时候是
     * 离线订单列表。)
     *
     * @param page
     * @return
     */
    @Transactional  // 查询订单列表需要一个session
    public ListResponse<ChargerOrderVo> queryChargeOrderList(ChargerOrderParam chargerOrderParam,
        Page<ChargerOrderVo> page) {

        log.info("[订单查询] 请求参数ChargerOrderParam：{} ", chargerOrderParam);

        HashMap<String, Object> searchMap = this.chargerOrderParamToSearchMap(chargerOrderParam);

        if (null != chargerOrderParam.getCurrent() && null != chargerOrderParam.getSize()) {
            page = PageHelper.startPage(chargerOrderParam.getCurrent(), chargerOrderParam.getSize(),
                false);
        }

        //异常订单查询的是即充即退退款失败
        if (chargerOrderParam.getAbnormal() != null && chargerOrderParam.getAbnormal()
            .equals(OrderAbnormalReason.REFUND_FAIL)) {
            searchMap.put("refundFail", true);
        }
//        log.info("searchMap = {}", JsonUtils.toJsonString(searchMap));
        Page<ChargerOrderVo> list = this.queryOrderList(
            searchMap, chargerOrderParam.getTotal(), page);
        if (CollectionUtils.isEmpty(list)) {
            return new ListResponse<>(list, 0L);
        }

        // 海外平台获取邮箱信息
        Map<Long, UserVo> userMap = new HashMap<>();
        if(chargerOrderParam.getLocale() != null && CollectionUtils.isNotEmpty(list)) {
            List<Long> customerIdList = list.stream().map(ChargerOrderVo::getCustomerId).distinct().toList();
            if (CollectionUtils.isNotEmpty(customerIdList)) {
                ObjectResponse<Map<Long, UserVo>> response = userFeignClient.findInfoByUids(
                    customerIdList).block();
                if (response != null && response.getData() != null) {
                    userMap = response.getData();
                }
            }
        }

        Set<Long> blocPayAccountIdsSet = new HashSet<>();
        Map<Long, UserVo> finalUserMap = userMap;
        list.forEach(o -> {
            // 海外平台添加email
            if (finalUserMap != null && o.getCustomerId() != null
                && finalUserMap.get(o.getCustomerId()) != null) {
                o.setEmail(finalUserMap.get(o.getCustomerId()).getEmail());
            }
            BigDecimal orderFee = o.getOrderPrice() == null ? BigDecimal.ZERO : o.getOrderPrice();
            BigDecimal parkingFee = o.getParkingFee() == null ? BigDecimal.ZERO : o.getParkingFee();
            BigDecimal zftTotalMoney =
                o.getZftTotalMoney() == null ? BigDecimal.ZERO : o.getZftTotalMoney();
            o.setCheckDiffMoney(orderFee.add(parkingFee).subtract(zftTotalMoney));
            if (NumberUtils.equals(o.getStatus(), OrderStatus.ORDER_STATUS_ERROR_CP)
                || NumberUtils.equals(o.getStatus(), OrderStatus.ORDER_STATUS_CHARGING)) {
                ChargeOrderCache cache = redisChargeOrderReadService.getOrder(o.getOrderNo(),
                    ChargeOrderCache.class);
                log.info("queryChargeOrderList OrderCache = {}", JsonUtils.toJsonString(cache));
                if (cache != null) {    // 使用缓存中的信息替换掉数据库查出来的内容
                    if (cache.getPay() != null) {
                        o.setOrderPrice(cache.getPay().getOrderOriginFee());
                        o.setActualPrice(cache.getPay().getOrderFee());

                        o.setElecOriginFee(cache.getPay().getElecOriginFee());
                        o.setElecPrice(cache.getPay().getElecFee());
                        o.setElecActualFee(cache.getPay().getElecFee());

                        o.setServOriginFee(cache.getPay().getServOriginFee());
                        o.setServicePrice(cache.getPay().getServFee());
                        o.setServActualFee(cache.getPay().getServFee());
                    } else {
                        o.setOrderPrice(cache.getOrderPrice());
                        o.setActualPrice(cache.getOrderPrice());

                        o.setElecOriginFee(cache.getElecPrice());
                        o.setElecPrice(cache.getElecPrice());
                        o.setElecActualFee(cache.getElecPrice());

                        o.setServOriginFee(cache.getServicePrice());
                        o.setServicePrice(cache.getServicePrice());
                        o.setServActualFee(cache.getServicePrice());
                    }
//                    o.setOrderPrice(cache.getOrderPrice());
//                    o.setServicePrice(cache.getServicePrice());
//                    o.setElecPrice(cache.getElecPrice());
//                    o.setActualPrice(cache.getActualPrice());
                    o.setOrderElectricity(cache.getOrderElectricity());
                }
            }

            if (o.getDefaultPayType() != null && o.getPayAccountId() != null
                && o.getCustomerId() != null) {
                if (OrderPayType.BLOC.getCode() == o.getDefaultPayType()) {
                    blocPayAccountIdsSet.add(o.getPayAccountId());
                }
            }
        });

        //仅在订单汇总的情况下查询分时计费信息
        if (chargerOrderParam.getType() == null) {
            //填入计算分时计费等信息的汇总
            fillDivTimeSumInfo(list);
            //填入支付退款信息
            fillPayInfo(list);
        }

        //设置账户名称、集团客户名称
        fillAccountNameAndBlocUserName(list, new ArrayList<>(blocPayAccountIdsSet));

        ListResponse<ChargerOrderVo> res = new ListResponse<>(list, list.getTotal());
        return res;

    }

    private HashMap<String, Object> chargerOrderParamToSearchMap(
        ChargerOrderParam chargerOrderParam) {
        HashMap<String, Object> searchMap = new HashMap<>();
        MyBeanUtils.copyBean2Map(searchMap, chargerOrderParam);
        if (CollectionUtils.isNotEmpty(chargerOrderParam.getGids())) {
            searchMap.put("gids", chargerOrderParam.getGids());
        }
        if (StringUtils.isNotBlank(chargerOrderParam.getSiteCommIdChain())
            || CollectionUtils.isNotEmpty(chargerOrderParam.getDeviceCommIdList())) {
            //chargerOrderParam.setCommercialId(null);
            if (chargerOrderParam.getTopCommId() != null
                && chargerOrderParam.getTopCommId() == -1L) {
                // 查订单后门
            } else if (StringUtils.isNotBlank(chargerOrderParam.getSiteCommIdChain())) {
                searchMap.put("commIdChain", chargerOrderParam.getSiteCommIdChain());
            }
//            if (CollectionUtils.isNotEmpty(chargerOrderParam.getDeviceCommIdList())) { // ??没有使用参数
//                chargerOrderParam.setCommIdList(chargerOrderParam.getDeviceCommIdList());
//            }
        }
        if (chargerOrderParam.getOperatorName() != null) {
            searchMap.put("operatorName", chargerOrderParam.getOperatorName());
        }
        //是否包含0电量订单
        if (chargerOrderParam.getIsZeroOrder() != null) {
            searchMap.put("isZeroOrder", chargerOrderParam.getIsZeroOrder());
        }
        //查询异常订单标志,前端有传递
        searchMap.put("type", chargerOrderParam.getType());

        //发票号码查询
        if (chargerOrderParam.getInvoiceNumber() != null
            && chargerOrderParam.getInvoiceNumber().length() > 0) {
            Long invoiceId = invoiceRecordRoDs.queryInvoiceRecordId(
                chargerOrderParam.getInvoiceNumber(), chargerOrderParam.getIsSureInvoiceNumber());
            if (invoiceId != null) {
                searchMap.put("invoicedId", invoiceId);
            } else {
                searchMap.put("invoicedId", -1);
            }

        }
        //扣款账户
        if (chargerOrderParam.getPayAccountTypeList() != null
            && chargerOrderParam.getPayAccountTypeList().size() > 0) {

            // 此处区分即充即退
            List<Long> list = Arrays.stream(PrePayType.values())
                .map(x -> x.getCode().longValue())
                .collect(Collectors.toList());

            // 包含即充即退拆分情况
            boolean b = chargerOrderParam.getPayAccountTypeList().stream()
                .anyMatch(x -> list.contains(x));

            if (b) {
                if (!list.containsAll(chargerOrderParam.getPayAccountTypeList())) {
                    chargerOrderParam.getPayAccountTypeList().add(
                        (long) PayAccountType.PREPAY.getCode());
                }

                // 支付渠道确认
                Map<Integer, Integer> prePayMap = Arrays.stream(PrePayType.values())
                    .collect(Collectors.toMap(PrePayType::getCode, PrePayType::getChannel));
                List<Integer> payChannelList = new ArrayList<>();

                chargerOrderParam.getPayAccountTypeList().forEach(x -> {
                    if (prePayMap.containsKey(x.intValue())) {
                        payChannelList.add(prePayMap.get(x.intValue()));
                    }
                });
                searchMap.put("payChannelList", payChannelList);
                chargerOrderParam.setPayChannelList(payChannelList);
                // 扣款账户调整
                chargerOrderParam.setPayAccountTypeList(
                    chargerOrderParam.getPayAccountTypeList().stream()
                        .filter(x -> !list.contains(x)).collect(
                            Collectors.toList()));
            }
            searchMap.put("payAccountTypeList", chargerOrderParam.getPayAccountTypeList());
        }

        if (CollectionUtils.isNotEmpty(chargerOrderParam.getPayChannelList())) {
            searchMap.put("payChannelList", chargerOrderParam.getPayChannelList());
        }
        // 订单启动方式：管理端手动与批量启动合并为管理端手动，但查询时需兼容
        if (CollectionUtils.isNotEmpty(chargerOrderParam.getOrderTypeList()) &&
            chargerOrderParam.getOrderTypeList()
                .contains(OrderStartType.MGM_WEB_MANUAL.getCode())) {
            chargerOrderParam.getOrderTypeList().add(OrderStartType.MGM_WEB_BATCH.getCode());
        }

        if (CollectionUtils.isNotEmpty(chargerOrderParam.getCorpOrgIds())) {

            searchMap.put("corpOrgId", chargerOrderParam.getCorpOrgIds());

            // 前端不传blocUserId时，需要给个默认的无效值
            searchMap.put("blocUserId", chargerOrderParam.getBlocUserId() == null ?
                Long.MIN_VALUE :
                chargerOrderParam.getBlocUserId());
        }

        if (chargerOrderParam.getStopReason() != null && !StringUtils.equals("",
            chargerOrderParam.getStopReason())
            && !StringUtils.equals("null", chargerOrderParam.getStopReason())) {
            searchMap.put("stopReason", chargerOrderParam.getStopReason());
        }

        if (CollectionUtils.isNotEmpty(chargerOrderParam.getAbnormalList())) {
            searchMap.put("abnormalList", chargerOrderParam.getAbnormalList());
        }

        if (CollectionUtils.isNotEmpty(chargerOrderParam.getProcessTypes())) {
            searchMap.put("processTypes",
                chargerOrderParam.getProcessTypes().stream().map(ProcessType::getCode)
                    .collect(Collectors.toList()));
        }

        String createTimeFrom = chargerOrderParam.getCreateTimeFrom();
        String createTimeTo = chargerOrderParam.getCreateTimeTo();
        if (createTimeFrom != null && !StringUtils.equals("", createTimeFrom)
            && !StringUtils.equals("null", createTimeFrom)) {
            searchMap.put("createTimeFrom",
                DateUtils.timeStampToDate2(createTimeFrom, "yyyy-MM-dd HH:mm:ss"));
        }
        if (createTimeTo != null && !StringUtils.equals("", createTimeTo) && !StringUtils.equals(
            "null", createTimeTo)) {
            searchMap.put("createTimeTo",
                DateUtils.timeStampToDate2(createTimeTo, "yyyy-MM-dd HH:mm:ss"));
        }

        String payTimeFrom = chargerOrderParam.getPayTimeFrom();
        String payTimeTo = chargerOrderParam.getPayTimeTo();
        if (payTimeFrom != null && !StringUtils.equals("", payTimeFrom) && !StringUtils.equals(
            "null", payTimeFrom)) {
            searchMap.put("payTimeFrom",
                DateUtils.timeStampToDate2(payTimeFrom, "yyyy-MM-dd HH:mm:ss"));
        }
        if (payTimeTo != null && !StringUtils.equals("", payTimeTo) && !StringUtils.equals("null",
            payTimeTo)) {
            searchMap.put("payTimeTo",
                DateUtils.timeStampToDate2(payTimeTo, "yyyy-MM-dd HH:mm:ss"));
        }

        String chargeStartTimeFrom = chargerOrderParam.getChargeStartTimeFrom();
        String chargeStartTimeTo = chargerOrderParam.getChargeStartTimeTo();
        if (chargeStartTimeFrom != null && !StringUtils.equals("", chargeStartTimeFrom)
            && !StringUtils.equals("null", chargeStartTimeFrom)) {
            searchMap.put("chargeStartTimeFrom",
                NumberUtils.parseLong(chargeStartTimeFrom, 0) / 1000);
        } else {
            searchMap.put("chargeStartTimeFrom", null);
        }
        if (chargeStartTimeTo != null && !StringUtils.equals("", chargeStartTimeTo)
            && !StringUtils.equals("null", chargeStartTimeTo)) {
            searchMap.put("chargeStartTimeTo", NumberUtils.parseLong(chargeStartTimeTo, 0) / 1000);
        } else {
            searchMap.put("chargeStartTimeTo", null);
        }

        String chargeEndTimeFrom = chargerOrderParam.getChargeEndTimeFrom();
        String chargeEndTimeTo = chargerOrderParam.getChargeEndTimeTo();
        if (chargeEndTimeFrom != null && !StringUtils.equals("", chargeEndTimeFrom)
            && !StringUtils.equals("null", chargeEndTimeFrom)) {
            searchMap.put("chargeEndTimeFrom", NumberUtils.parseLong(chargeEndTimeFrom, 0) / 1000);
        } else {
            searchMap.put("chargeEndTimeFrom", null);
        }
        if (chargeEndTimeTo != null && !StringUtils.equals("", chargeEndTimeTo)
            && !StringUtils.equals("null", chargeEndTimeTo)) {
            searchMap.put("chargeEndTimeTo", NumberUtils.parseLong(chargeEndTimeTo, 0) / 1000);
        } else {
            searchMap.put("chargeEndTimeTo", null);
        }

        String stopTimeFrom = chargerOrderParam.getStopTimeFrom();
        String stopTimeTo = chargerOrderParam.getStopTimeTo();
        if (stopTimeFrom != null && !StringUtils.equals("", stopTimeFrom) && !StringUtils.equals(
            "null", stopTimeFrom)) {
            searchMap.put("stopTimeFrom",
                DateUtils.timeStampToDate2(stopTimeFrom, "yyyy-MM-dd HH:mm:ss"));
        }
        if (stopTimeTo != null && !StringUtils.equals("", stopTimeTo) && !StringUtils.equals("null",
            stopTimeTo)) {
            searchMap.put("stopTimeTo",
                DateUtils.timeStampToDate2(stopTimeTo, "yyyy-MM-dd HH:mm:ss"));
        }

        //集团客户模糊匹配
        if (chargerOrderParam != null && StringUtils.isNotBlank(
            chargerOrderParam.getBlocUserName())) {
            ListResponse<Long> idListResponse = userFeignClient.queryIdsByBlocUserName(
                    chargerOrderParam.getBlocUserName(), chargerOrderParam.getIsSureBlocUserName())
                .block(Duration.ofSeconds(50L));
            if (idListResponse == null
                || idListResponse.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
                throw new DcServerException("集团客户名称模糊匹配失败");
            }
            if (CollectionUtils.isEmpty(idListResponse.getData())) {
                //当没有模糊匹配到时，返回空list
                searchMap.put("noResults", true);
            } else {
                searchMap.put("defaultPayType", OrderPayType.BLOC.getCode());
                List<Set<Long>> payAccountIdSets = split(idListResponse.getData());//可能超过1000个，拆开处理
                searchMap.put("payAccountIdSets", payAccountIdSets);
            }
        }
        //用于在查询异常订单时，多订单状态的条件判断 -wangzheng
        //增加原因：页面查询时，会传多个状态，若此时是查询异常订单且状态包括-500，则在sql中会进行判断（否则会出现异常订单页面订单状态全选查询时数量比实际少）
        if (null != chargerOrderParam.getStatusList()) {
            searchMap.put("includeErrorOrder",
                chargerOrderParam.getStatusList().contains(OrderStatus.ORDER_STATUS_ERROR_CP));
        }
        //枪头编号精确查询
        searchMap.put("plugNo", chargerOrderParam.getPlugNo());
        searchMap.put("payStatus", chargerOrderParam.getPayStatus());
        //扣款账户 账户类型-账户标识
        searchMap.put("payAccountList", chargerOrderParam.getPayAccountList());
        //支付方式 账户类型或账户类型-orderType
        searchMap.put("payTypeList", chargerOrderParam.getPayTypeList());
        searchMap.put("lineNum", chargerOrderParam.getLineNum());
        searchMap.put("includedHlhtSite", chargerOrderParam.getIncludedHlhtSite());
        searchMap.put("checkResult", chargerOrderParam.getCheckResult());
        searchMap.put("biDependOnType", chargerOrderParam.getBiDependOnType());
        searchMap.put("corpId", chargerOrderParam.getCorpId());
        return searchMap;
    }

    private Page<ChargerOrderVo> queryOrderList(HashMap<String, Object> searchMap,
        Boolean fetchTotal,
        Page<ChargerOrderVo> page) {
        log.info("订单查询条件:{}", JsonUtils.toJsonString(searchMap));
        this.chargerOrderRoDs.prepareChargeOrderList();
//        Page<ChargerOrderVo> pageInfo = null;
        Page<ChargerOrderVo> list = null;
        if (searchMap.get("type") != null && searchMap.get("type").equals("abnormal")) {

//            if (page != null) {
//                pageInfo = PageHelper.startPage(page.getPageNum(), page.getPageSize(), false, false,
//                    null);
//            }
            List<String> orderNoList = chargerOrderRoDs.selectAbnormalOrderNoList(searchMap);
            if (CollectionUtils.isNotEmpty(orderNoList)) {
                HashMap<String, Object> param = new HashMap<>();
                param.put("orderNos", orderNoList);
                list = chargerOrderRoDs.selectAbnormalChargeOrderList(param);
                if (Boolean.TRUE.equals(fetchTotal)) {
                    Long total = chargerOrderRoDs.selectAbnormalOrderListCount(searchMap);
                    list.setTotal(total);
                }
            }

        } else {
//            if (page != null) {
//                pageInfo = PageHelper.startPage(page.getPageNum(), page.getPageSize(), false, false,
//                    null);
//            }
            list = chargerOrderRoDs.selectChargeOrderList(searchMap);

            if (Boolean.TRUE.equals(fetchTotal)) {
                Long total = chargerOrderRoDs.selectChargeOrderListCount(searchMap);
                list.setTotal(total);
            }
        }

        log.info("订单查询结果数量 = {}", list == null ? 0 : list.size());
        if (list == null || list.size() < 1) {
            return new Page<ChargerOrderVo>();
        }
        Set<String> vinSet = new HashSet<>();
        Set<String> cardSet = new HashSet<>();
        //Set<String> connectIdSet = new HashSet<>();
        //Set<String> evseNoSet = new HashSet<>();
        Set<String> plugNoSet = new HashSet<>();
        Set<String> orderNoSet = new HashSet<>();
        Set<Long> deviceCommercialIdSet = new HashSet<>();
        Set<String> siteIdSet = new HashSet<>();

        for (ChargerOrderVo chargerOrder : list) {
            String orderVin = chargerOrder.getVin();
            String orderCardNo = chargerOrder.getCardNo();
            String plugNo = PlugNoUtils.formatPlugNo(chargerOrder.getBoxCode(),
                chargerOrder.getConnectorId());
//            String connectId = chargerOrder.getConnectId();
//            String evseNo = chargerOrder.getBoxCode();
            String orderNo1 = chargerOrder.getOrderNo();
            String deviceCommercialId = chargerOrder.getDeviceCommercialId();
            String siteId = chargerOrder.getStationId();

            if (!StringUtils.isBlank(orderVin)) {
                vinSet.add(orderVin);
            }
            if (!StringUtils.isBlank(orderCardNo)) {
                cardSet.add(orderCardNo);
            }
            if (!StringUtils.isBlank(orderNo1)) {
                orderNoSet.add(orderNo1);
            }
            if (!StringUtils.isBlank(deviceCommercialId)) {
                deviceCommercialIdSet.add(Long.parseLong(deviceCommercialId));
            }
            if (!StringUtils.isBlank(siteId)) {
                siteIdSet.add(siteId);
            }
            plugNoSet.add(plugNo);
            // 开票状态
            // 暂时处理: InvoicedId(此字段默认值为0) 若为0则设置 TaxType.NONE - 认为未开票
            chargerOrder.setTaxStatus(
                chargerOrder.getInvoicedId() > 0 ? TaxStatus.YES : TaxStatus.NO);
        }

        Map<String, String> vinsMap = null;
//        Map<String, String> evseNamesMap = new HashMap<>();
//        Map<String, String> plugNoNamesMap = new HashMap<>();
//        Map<String, BigDecimal> kwhMap = null;
        Map<Long, String> deviceCommercialNamesMap = null;
        //Map<String, String> chargerNamesMap = null;
        Map<String, String> siteMap = null;

        if (CollectionUtils.isNotEmpty(vinSet)) {
//        if (vinSet != null && vinSet.size() > 0) {
            VINCarNoParam param = new VINCarNoParam();
            param.setVinList(new ArrayList<>(vinSet));
            if (null != searchMap.get("commIdChain")) {
                param.setCommIdChain(searchMap.get("commIdChain").toString());
            }
            ObjectResponse vinListEntity = userFeignClient.selectCarNoByVins(param)
                .block(Duration.ofSeconds(50L));
            if (vinListEntity != null && vinListEntity.getData() != null) {
                vinsMap = (Map<String, String>) vinListEntity.getData();
            }
        }

//        if (CollectionUtils.isNotEmpty(plugNoSet)) {
//            ListPlugParam param = new ListPlugParam();
//            param.setPlugNoList(new ArrayList<>());
//            param.getPlugNoList().addAll(plugNoSet);
//            ListResponse<PlugVo> plugListRes = iotDeviceMgmFeignClient.getPlugList(param);
//            FeignResponseValidate.check(plugListRes);
//            for (PlugVo p : plugListRes.getData()) {
//                evseNamesMap.put(p.getEvseNo(), p.getEvseName());
//                plugNoNamesMap.put(p.getPlugNo(), p.getName());
//            }
//        }

        // 获取集团客户名
        Map<String, String> rBlocUsersMap = new HashMap<>();
        List<String> cardNoList = list.stream()
            .filter(t1 -> StringUtils.isNotBlank(t1.getCardNo()))
            .map(t -> t.getCardNo()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(cardNoList)) {
            ListResponse<Card> blockUserNameResponse = cardFeignClient.selectBlocUserNameByCardNos(
                    cardNoList)
                .block(Duration.ofSeconds(50L));
            List<Card> cardList = blockUserNameResponse.getData();
            if (CollectionUtils.isNotEmpty(cardList)) {
                for (Card card : cardList) {
                    rBlocUsersMap.put(card.getCardNo(), card.getBlocUserName());
                }
            }
        }

        //获取设备运营商商户的全称
        if (deviceCommercialIdSet != null && deviceCommercialIdSet.size() > 0) {
            List<CommercialSimpleVo> commerials = commercialQueryDs.getCommerialsByCommIds(
                new ArrayList<>(deviceCommercialIdSet));
            if (CollectionUtils.isNotEmpty(commerials)) {
                deviceCommercialNamesMap = commerials.stream().collect(
                    Collectors.toMap(CommercialSimpleVo::getId, CommercialSimpleVo::getCommName,
                        (key1, key2) -> key2));
            }
        }

        //站点名称
        if (siteIdSet != null && siteIdSet.size() > 0) {
            List<SitePo> sites = siteRoDs.findByCondition(null, null, null,
                new ArrayList<>(siteIdSet), null);
            if (sites != null && sites.size() > 0) {
                siteMap = sites.stream().collect(
                    Collectors.toMap(v -> v.getId(), v -> v.getSiteName(), (v1, v2) -> v1));
            }
        }

        Map<String, EvseVo> cacheEvseMap = new HashMap<>();
        Map<String, PlugVo> cachePlugMap = new HashMap<>();
        for (ChargerOrderVo chargerOrder : list) {
            //log.debug("chargerOrder:{}",JsonUtils.toJsonString(list));
            if (vinsMap != null &&
                StringUtils.isNotBlank(chargerOrder.getVin()) &&
                StringUtils.isBlank(chargerOrder.getCarNo())) {
                String carNo = vinsMap.get(chargerOrder.getTopCommId() + chargerOrder.getVin());
                if (StringUtils.isNotBlank(carNo)) {
                    chargerOrder.setCarNo(carNo);
                }
            }

            if (chargerOrder.getBoxCode() != null) {
                if (HlhtType.REVERSE_HLHT.equals(chargerOrder.getHlhtType())) {
                    EvseVo evseVo = cacheEvseMap.get(chargerOrder.getBoxCode());
                    if (null == evseVo) {
                        evseVo = redisIotReadService.getEvseRedisCache(chargerOrder.getBoxCode());
                        cacheEvseMap.put(chargerOrder.getBoxCode(), evseVo);
                    }
                    chargerOrder.setEvseName(evseVo != null ? evseVo.getName() : null);
                } else {
                    EvseVo evseVo = cacheEvseMap.get(chargerOrder.getBoxCode());
                    if (null == evseVo) {
                        evseVo = redisIotReadService.getEvseRedisCache(chargerOrder.getBoxCode());
                        if (evseVo != null) {
                            chargerOrder.setSupplyType(evseVo.getSupplyType());
                            cacheEvseMap.put(chargerOrder.getBoxCode(), evseVo);
                        } else {
                            BsBoxPo bsBox = bsBoxRoDs.getBsBox(chargerOrder.getBoxCode());
                            if (null != bsBox) {
                                evseVo = new EvseVo()
                                    .setEvseNo(chargerOrder.getBoxCode())
                                    .setName(bsBox.getEvseName());

                                if (null != bsBox.getCurrentType()) {
                                    switch (bsBox.getCurrentType()) {
                                        case 0:
                                            evseVo.setSupplyType(SupplyType.AC);
                                            break;
                                        case 1:
                                            evseVo.setSupplyType(SupplyType.DC);
                                            break;
                                        case 2:
                                            evseVo.setSupplyType(SupplyType.BOTH);
                                            break;
                                        default:
                                            evseVo.setSupplyType(SupplyType.UNKNOWN);
                                    }
                                } else {
                                    evseVo.setSupplyType(SupplyType.UNKNOWN);
                                }

                                chargerOrder.setSupplyType(evseVo.getSupplyType());
                                cacheEvseMap.put(chargerOrder.getBoxCode(), evseVo);
                            } else { // end if
                                chargerOrder.setSupplyType(SupplyType.UNKNOWN);
                            }
                        }
                    } else {
                        chargerOrder.setSupplyType(evseVo.getSupplyType());
                    }
//                    chargerOrder.setEvseName(evseNamesMap.get(chargerOrder.getBoxCode()));
                }
            }

            if (rBlocUsersMap != null && StringUtils.isNotBlank(chargerOrder.getCardNo())) {
                chargerOrder.setRblocUserName(rBlocUsersMap.get(chargerOrder.getCardNo()));
            }

            if (deviceCommercialNamesMap != null && !StringUtils.isBlank(
                chargerOrder.getDeviceCommercialId())) {
                chargerOrder.setCommercialFullName(deviceCommercialNamesMap.get(
                    Long.parseLong(chargerOrder.getDeviceCommercialId())));
            }

            String plugNo = PlugNoUtils.formatPlugNo(chargerOrder.getBoxCode(),
                chargerOrder.getConnectorId());
            if (chargerOrder.getConnectorId() != null) {
                if (HlhtType.REVERSE_HLHT.equals(chargerOrder.getHlhtType())) {
                    PlugVo plugVo = cachePlugMap.get(chargerOrder.getPlugNo());
                    if (null == plugVo) {
                        plugVo = redisIotReadService.getPlugRedisCache(chargerOrder.getPlugNo());
                        cachePlugMap.put(chargerOrder.getPlugNo(), plugVo);
                    }
                    chargerOrder.setChargerName(plugVo != null ? plugVo.getName() : "");
                } else {
//                    chargerOrder.setChargerName(plugNoNamesMap.get(plugNo));
                }
            }

            if (siteMap != null && chargerOrder.getStationId() != null) {
                String siteName = siteMap.get(chargerOrder.getStationId());
                if (siteName != null) {
                    chargerOrder.setStationName(siteName);
                }
            }
            chargerOrder.setVin(
                StringUtils.isNotBlank(chargerOrder.getVin()) ? chargerOrder.getVin().toUpperCase()
                    : null);
            /**
             * 转换枪头编号bcCode格式：桩号-枪头序号
             */
            String bcCode = chargerOrder.getBoxCode() + "-" + chargerOrder.getConnectorId();
            chargerOrder.setBcCode(bcCode);

            //设置订单处理类型
            if (chargerOrder.getAbnormal() == null) {
                chargerOrder.setProcessType(ProcessType.NORMAL);
            } else if (chargerOrder.getAbnormal() != null && !chargerOrder.getManual()) {
                chargerOrder.setProcessType(ProcessType.ABNORMAL_UNDO);
            } else if (chargerOrder.getAbnormal() != null && chargerOrder.getManual()) {
                chargerOrder.setProcessType(ProcessType.ABNORMAL_DONE);
            }

            //设置卡类型
            if (chargerOrder.getOrderType() != null
                && OrderStartType.EVSE_OFFLINE_CARD.getCode() == chargerOrder.getOrderType()
                .intValue()) {
                chargerOrder.setCardType(CardType.EMERGENCY);
            } else if (chargerOrder.getOrderType() != null
                && OrderStartType.ONLINE_CARD.getCode() == chargerOrder.getOrderType().intValue()) {
                chargerOrder.setCardType(CardType.ONLINE);
            } else if (chargerOrder.getOrderType() != null
                && OrderStartType.OFFLINE_CARD.getCode() == chargerOrder.getOrderType()
                .intValue()) {
                chargerOrder.setCardType(CardType.OFFLINE_CARD);
            }

            if (chargerOrder.getStopCode() != null
                && chargerOrder.getStopCode().equals(OrderStopCode.C00)
                && chargerOrder.getCompleteCode() != null) {
                // 若订单正常停充，则展示completeCode
                chargerOrder.setStopReason(chargerOrder.getCompleteCode().getDesc());
            }

        }
        // 订单启动方式：管理端手动与批量启动合并为管理端手动，页面兼容显示
        list.stream().forEach(e -> {
            if (e.getOrderType() != null &&
                e.getOrderType().equals(OrderStartType.MGM_WEB_BATCH.getCode())) {
                e.setOrderType(OrderStartType.MGM_WEB_MANUAL.getCode());
            }
        });
        return list;
    }

    /**
     * 获取订单支付信息
     *
     * @param list
     */
    private void fillPayInfo(List<ChargerOrderVo> list) {
        List<String> orderNoList = list.stream().map(ChargerOrderSample::getOrderNo)
            .collect(Collectors.toList());
        Map<String, List<PayInfo>> chargerOrderPayInfoMap =
            payBillRoDs.queryPayInfo(orderNoList);
        if (chargerOrderPayInfoMap != null) {
            list.forEach(o -> {
                List<PayInfo> payInfo = chargerOrderPayInfoMap.get(o.getOrderNo());
                if (payInfo != null && payInfo.size() > 0) {
                    o.setPayOrderNo(payInfo.get(0).getPayOrderNo());
                    o.setRefundOrderNo(payInfo.get(0).getRefundOrderNo());
                }
            });
        }
    }

    private void fillDivTimeSumInfo(List<ChargerOrderVo> list) {
        List<String> orderNoList = list.stream().map(ChargerOrderSample::getOrderNo)
            .collect(Collectors.toList());
        Map<String, ChargerOrderTimeDivisionSum> chargerOrderTimeDivisionSumMap =
            this.queryOrderTimeDivisionMapByOrderNoList(orderNoList);
        if (chargerOrderTimeDivisionSumMap != null) {
            list.forEach(o -> {
                ChargerOrderTimeDivisionSum chargerOrderTimeDivisionSum = chargerOrderTimeDivisionSumMap.get(
                    o.getOrderNo());
                if (chargerOrderTimeDivisionSum != null) {
                    o.setTipElectricity(
                        double2Decimal(chargerOrderTimeDivisionSum.getTipElectricity()));
                    o.setTipElecPrice(
                        double2Decimal(chargerOrderTimeDivisionSum.getTipElecPrice()));
                    o.setTipServicePrice(
                        double2Decimal(chargerOrderTimeDivisionSum.getTipServicePrice()));
                    o.setTipSumPrice(double2Decimal(chargerOrderTimeDivisionSum.getTipSumPrice()));
                    o.setTipElectricUnit(chargerOrderTimeDivisionSum.getTipElectricUnit());
                    o.setTipServiceUnit(chargerOrderTimeDivisionSum.getTipServiceUnit());
                    o.setPeakElectricity(
                        double2Decimal(chargerOrderTimeDivisionSum.getPeakElectricity()));
                    o.setPeakElecPrice(
                        double2Decimal(chargerOrderTimeDivisionSum.getPeakElecPrice()));
                    o.setPeakServicePrice(
                        double2Decimal(chargerOrderTimeDivisionSum.getPeakServicePrice()));
                    o.setPeakSumPrice(
                        double2Decimal(chargerOrderTimeDivisionSum.getPeakSumPrice()));
                    o.setPeakElectricUnit(chargerOrderTimeDivisionSum.getPeakElectricUnit());
                    o.setPeakServiceUnit(chargerOrderTimeDivisionSum.getPeakServiceUnit());
                    o.setFlatElectricity(
                        double2Decimal(chargerOrderTimeDivisionSum.getFlatElectricity()));
                    o.setFlatElecPrice(
                        double2Decimal(chargerOrderTimeDivisionSum.getFlatElecPrice()));
                    o.setFlatServicePrice(
                        double2Decimal(chargerOrderTimeDivisionSum.getFlatServicePrice()));
                    o.setFlatSumPrice(
                        double2Decimal(chargerOrderTimeDivisionSum.getFlatSumPrice()));
                    o.setFlatElectricUnit(chargerOrderTimeDivisionSum.getFlatElectricUnit());
                    o.setFlatServiceUnit(chargerOrderTimeDivisionSum.getFlatServiceUnit());
                    o.setValleyElectricity(
                        double2Decimal(chargerOrderTimeDivisionSum.getValleyElectricity()));
                    o.setValleyElecPrice(
                        double2Decimal(chargerOrderTimeDivisionSum.getValleyElecPrice()));
                    o.setValleyServicePrice(
                        double2Decimal(chargerOrderTimeDivisionSum.getValleyServicePrice()));
                    o.setValleySumPrice(
                        double2Decimal(chargerOrderTimeDivisionSum.getValleySumPrice()));
                    o.setValleyElectricUnit(chargerOrderTimeDivisionSum.getValleyElectricUnit());
                    o.setValleyServiceUnit(chargerOrderTimeDivisionSum.getValleyServiceUnit());
                }
            });
        }
    }

    //填充账户名称
    private void fillAccountNameAndBlocUserName(List<ChargerOrderVo> list,
        List<Long> blocPayAccountIds) {
        Map<String, String> blocNameMap = new HashMap<>();
        if (blocPayAccountIds.size() > 0) {
            ListResponse<RBlocUser> rBlocUserList = userFeignClient.selectRBlocUserIds(
                    blocPayAccountIds)
                .block(Duration.ofSeconds(50L));
            if (rBlocUserList.getData() != null && rBlocUserList.getData().size() > 0) {
                blocNameMap = rBlocUserList.getData().stream().collect(
                    Collectors.toMap(v -> String.valueOf(v.getId()), v -> v.getBlocUserName(),
                        (v1, v2) -> v1));
            }
        }
        //设置账户名称
        Map<String, String> finalBlocNameMap = blocNameMap;
        list.stream().forEach(o -> {
            if (o.getDefaultPayType() != null && o.getPayAccountId() != null
                && o.getCustomerId() != null) {
                if (OrderPayType.PERSON.getCode() == o.getDefaultPayType()) {
                    o.setPayAccountName("个人账户"); // 现在平台统一为个人账户: UG1118-315
                } else if (OrderPayType.BLOC.getCode() == o.getDefaultPayType()) {
                    StringBuilder sb = new StringBuilder();
                    sb.append("企业账户");
                    sb.append("-");
                    sb.append(finalBlocNameMap.get(String.valueOf(o.getPayAccountId())));
                    o.setPayAccountName(sb.toString());
                    o.setBlocUserName(finalBlocNameMap.get(String.valueOf(o.getPayAccountId())));
                } else if (OrderPayType.MERCHANT.getCode() == o.getDefaultPayType()) {
                    CommercialSimpleVo commercial = commercialQueryDs.getCommById(
                        o.getPayAccountId());
                    if (commercial != null) {
                        StringBuilder sb = new StringBuilder();
                        sb.append("商户会员");
                        sb.append("-");
                        sb.append(commercial.getCommName());
                        o.setPayAccountName(sb.toString());
                    }
                } else if (OrderPayType.PREPAY.getCode() == o.getDefaultPayType()) {
                    if (o.getPayChannel() != null) {
                        o.setPayAccountName(PrePayType.valueOfChannel(o.getPayChannel().getCode()));
                    } else {
                        o.setPayAccountName("即充即退");
                    }
                    // 产品新加: issue BUG1118-213
//                    o.setPayAccountName("即充即退");
                } else if (OrderPayType.E_CNY.getCode() == o.getDefaultPayType()) {
                    o.setPayAccountName("亨通数字钱包");
                } else if (PayAccountType.WX_CREDIT.getCode() == o.getDefaultPayType()) {
                    o.setPayAccountName(PayAccountType.WX_CREDIT.getDesc());
                } else if (PayAccountType.ALIPAY_CREDIT.getCode() == o.getDefaultPayType()) {
                    o.setPayAccountName(PayAccountType.ALIPAY_CREDIT.getDesc());
                }
            } else if (o.getDefaultPayType() != null
                && PayAccountType.CREDIT_CARD.getCode() == o.getDefaultPayType()) {
                o.setPayAccountName(PayAccountType.CREDIT_CARD.getDesc());
            }
        });
    }

    public BigDecimal double2Decimal(Double val) {
        if (val == null) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(val).setScale(4, RoundingMode.HALF_UP);
    }

    public Map<String, ChargerOrderTimeDivisionSum> queryOrderTimeDivisionMapByOrderNoList(
        List<String> orderNoList) {
        List<ChargerOrderTimeDivision> chargerOrderTimeDivisions = chargerOrderTimeDivisionRoDs
            .selectListByOrderNoList(orderNoList);
        if (CollectionUtils.isNotEmpty(chargerOrderTimeDivisions)) {
            //按订单分组
            Map<String, List<ChargerOrderTimeDivision>> orderDivMap = chargerOrderTimeDivisions.
                stream().collect(Collectors.groupingBy(ChargerOrderTimeDivision::getOrderNo));

            //按tag 计算
            Map<String, ChargerOrderTimeDivisionSum> sumMap = new HashMap<>();
            orderDivMap.keySet().forEach(orderNo -> {
                List<ChargerOrderTimeDivision> divisions = orderDivMap.get(orderNo).stream()
                    .filter(item -> {
                        return item.getTag() != null;
                    }).collect(Collectors.toList());

                //分时电量 tag-电量
                Map<Integer, Double> electricMap = divisions.stream()
                    .collect(Collectors.groupingBy(ChargerOrderTimeDivision::getTag,
                        Collectors.summingDouble(o -> o.getElectric().doubleValue())));
                //分时电费 tag-电费
                Map<Integer, Double> elecPriceMap = divisions.stream()
                    .collect(Collectors.groupingBy(ChargerOrderTimeDivision::getTag,
                        Collectors.summingDouble(o -> o.getElectricPrice().doubleValue())));
                //分时服务费 tag-服务费
                Map<Integer, Double> servicePriceMap = divisions.stream()
                    .collect(Collectors.groupingBy(ChargerOrderTimeDivision::getTag,
                        Collectors.summingDouble((o -> o.getServicePrice().doubleValue()))));
                //分时合计 tag-合计
                Map<Integer, Double> orderPriceMap = divisions.stream()
                    .collect(Collectors.groupingBy(ChargerOrderTimeDivision::getTag,
                        Collectors.summingDouble((o -> o.getOrderPrice().doubleValue()))));

                //分时电费单价 tag-电费单价
                Map<Integer, BigDecimal> electricUnitMap = new HashMap<>();
                //分时服务单价 tag-服务单价
                Map<Integer, BigDecimal> serviceUnitMap = new HashMap<>();

                Map<Integer, List<ChargerOrderTimeDivision>> tagMap = divisions.stream()
                    .collect(Collectors.groupingBy(ChargerOrderTimeDivision::getTag));
                tagMap.keySet().forEach(tag -> {
                    ChargerOrderTimeDivision timeDivision = tagMap.get(tag).stream().findFirst()
                        .get();
                    int size = tagMap.get(tag).size();
                    BigDecimal serviceUintFee = tagMap.get(tag).stream().filter(Objects::nonNull)
                        .map(ChargerOrderTimeDivision::getServiceUnit).filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal service_unit = serviceUintFee.divide(BigDecimal.valueOf(size), 2,
                        RoundingMode.HALF_UP);
                    serviceUnitMap.put(tag, service_unit);
                    electricUnitMap.put(tag, timeDivision.getElectricUnit());
//                    serviceUnitMap.put(tag, timeDivision.getServiceUnit());
                });

                //计费标签（1：尖峰 2：高峰 3：平峰 4：低谷）
                sumMap.put(orderNo,
                    genChargerOrderTimeDivisionSum(electricMap, elecPriceMap, servicePriceMap,
                        orderPriceMap,
                        electricUnitMap, serviceUnitMap));
            });
            return sumMap;
        }
        return null;
    }

    private ChargerOrderTimeDivisionSum genChargerOrderTimeDivisionSum(
        Map<Integer, Double> electricMap, Map<Integer, Double> elecPriceMap,
        Map<Integer, Double> servicePriceMap, Map<Integer, Double> orderPriceMap,
        Map<Integer, BigDecimal> electricUnitMap, Map<Integer, BigDecimal> serviceUnitMap) {
        ChargerOrderTimeDivisionSum divisionSum = new ChargerOrderTimeDivisionSum();
        divisionSum.setTipElectricity(electricMap.get(DivTimeTagType.TIP.getCode()));
        divisionSum.setTipElecPrice(elecPriceMap.get(DivTimeTagType.TIP.getCode()));
        divisionSum.setTipServicePrice(servicePriceMap.get(DivTimeTagType.TIP.getCode()));
        divisionSum.setTipSumPrice(orderPriceMap.get(DivTimeTagType.TIP.getCode()));
        divisionSum.setTipElectricUnit(electricUnitMap.get(DivTimeTagType.TIP.getCode()));
        divisionSum.setTipServiceUnit(serviceUnitMap.get(DivTimeTagType.TIP.getCode()));

        divisionSum.setPeakElectricity(electricMap.get(DivTimeTagType.PEAK.getCode()));
        divisionSum.setPeakElecPrice(elecPriceMap.get(DivTimeTagType.PEAK.getCode()));
        divisionSum.setPeakServicePrice(servicePriceMap.get(DivTimeTagType.PEAK.getCode()));
        divisionSum.setPeakSumPrice(orderPriceMap.get(DivTimeTagType.PEAK.getCode()));
        divisionSum.setPeakElectricUnit(electricUnitMap.get(DivTimeTagType.PEAK.getCode()));
        divisionSum.setPeakServiceUnit(serviceUnitMap.get(DivTimeTagType.PEAK.getCode()));

        divisionSum.setFlatElectricity(electricMap.get(DivTimeTagType.FLAT.getCode()));
        divisionSum.setFlatElecPrice(elecPriceMap.get(DivTimeTagType.FLAT.getCode()));
        divisionSum.setFlatServicePrice(servicePriceMap.get(DivTimeTagType.FLAT.getCode()));
        divisionSum.setFlatSumPrice(orderPriceMap.get(DivTimeTagType.FLAT.getCode()));
        divisionSum.setFlatElectricUnit(electricUnitMap.get(DivTimeTagType.FLAT.getCode()));
        divisionSum.setFlatServiceUnit(serviceUnitMap.get(DivTimeTagType.FLAT.getCode()));

        divisionSum.setValleyElectricity(electricMap.get(DivTimeTagType.VALLEY.getCode()));
        divisionSum.setValleyElecPrice(elecPriceMap.get(DivTimeTagType.VALLEY.getCode()));
        divisionSum.setValleyServicePrice(servicePriceMap.get(DivTimeTagType.VALLEY.getCode()));
        divisionSum.setValleySumPrice(orderPriceMap.get(DivTimeTagType.VALLEY.getCode()));
        divisionSum.setValleyElectricUnit(electricUnitMap.get(DivTimeTagType.VALLEY.getCode()));
        divisionSum.setValleyServiceUnit(serviceUnitMap.get(DivTimeTagType.VALLEY.getCode()));
        return divisionSum;
    }

    //拆分超过1000的,为sql查询做准备
    private List<Set<Long>> split(List<Long> items) {
        List<Set<Long>> result = new ArrayList<>();
        if (items == null) {
            return result;
        }
        Set<Long> tempSet = new HashSet<>(items);

        Set<Long> set = new HashSet<>();
        result.add(set);
        for (Long item : tempSet) {
            if (set.size() == 1000) {
                set = new HashSet<>();
                result.add(set);
            }
            if (item == null) {
                continue;
            }
            set.add(item);
        }
        return result;
    }


    /**
     * 根据条件查询订单统计数据
     *
     * @param chargerOrderParam
     * @return
     */
    public ObjectResponse<ChargerOrderDataVo> getChargerOrderData(
        ChargerOrderParam chargerOrderParam) {
        log.info("统计订单查询  参数  --> {}", JsonUtils.toJsonString(chargerOrderParam));

        HashMap<String, Object> searchMap = this.chargerOrderParamToSearchMap(chargerOrderParam);
        ObjectResponse<ChargerOrderDataVo> res = RestUtils.buildObjectResponse(
            chargerOrderRoDs.getChargerOrderData(searchMap));
        log.info("统计订单查询  结果  --> {}", JsonUtils.toJsonString(res));
        FeignResponseValidate.check(res);
        return res;
    }

    /**
     * excel导出汇总数据使用
     *
     * @param chargerOrderParam
     * @return
     */
    public ListResponse<ChargerOrderDataVo> exportChargerOrderData(
        ChargerOrderParam chargerOrderParam) {
        HashMap<String, Object> searchMap = this.chargerOrderParamToSearchMap(chargerOrderParam);
        List<ChargerOrderDataVo> list = chargerOrderRoDs.exportChargerOrderData(searchMap);
        log.info("统计订单查询  结果  --> {}", JsonUtils.toJsonString(list));
        list.sort(Comparator.comparing(ChargerOrderDataVo::getTag));
        return new ListResponse<>(list);
    }

    /**
     * 根据查询条件获取excel导出的数据(导出订单excel专用)
     *
     * @return
     */
    @Transactional  // 查询订单列表需要一个session
    public ListResponse<ChargerOrderVo> exportChargeOrderListV2(
        ChargerOrderParam chargerOrderParam) {
        log.info("订单导出查询  参数  --> {}", chargerOrderParam);

        // 分页
//        Page<ChargerOrderVo> page = null;
//        if (null != chargerOrderParam.getCurrent() && null != chargerOrderParam.getSize()) {
//            page = PageHelper.startPage(chargerOrderParam.getCurrent(), chargerOrderParam.getSize(),chargerOrderParam.getTotal());
//        }

        ListResponse<ChargerOrderVo> listRes = queryChargeOrderList(chargerOrderParam, null);

        return listRes;
    }

    /**
     * 根据订单条件查询订单信息（尖峰平谷）
     */
    public ObjectResponse<ChargerOrderDetailVo> getChargerOrderDetail(
        ChargerOrderParam chargerOrderParam) {
        HashMap<String, Object> searchMap = this.chargerOrderParamToSearchMap(chargerOrderParam);
        List<ChargerOrderDataVo> list = chargerOrderRoDs.getChargerOrderDetail(searchMap); //尖峰平谷信息
        //
        ChargerOrderDataVo result = chargerOrderRoDs.getChargerOrderData(searchMap); //汇总信息
        log.info("尖峰平谷信息 {}", JsonUtils.toJsonString(list));
        log.info("汇总信息信息 {}", JsonUtils.toJsonString(result));
        OrderPowerVo orderNum = new OrderPowerVo();
        OrderPowerVo electricityAmount = new OrderPowerVo();
        OrderPowerVo orderPriceAmount = new OrderPowerVo();
        OrderPowerVo elecPriceAmount = new OrderPowerVo();
        OrderPowerVo servicePriceAmount = new OrderPowerVo();

        //汇总信息
        orderNum.setTotal(this.formatLongData(result.getChargerOrderNumber(), 0L));
        electricityAmount.setTotal(this.formatData(
            result.getOrderElectricityAmount() == null ? BigDecimal.ZERO
                : result.getOrderElectricityAmount(), 4));
        orderPriceAmount.setTotal(this.formatData(
            result.getOrderPriceAmount() == null ? BigDecimal.ZERO : result.getOrderPriceAmount(),
            2));
        elecPriceAmount.setTotal(this.formatData(
            result.getElecPriceAmount() == null ? BigDecimal.ZERO : result.getElecPriceAmount(),
            2));
        servicePriceAmount.setTotal(this.formatData(
            result.getServicePriceAmount() == null ? BigDecimal.ZERO
                : result.getServicePriceAmount(), 2));

        list.forEach(o -> {

            if (o.getTag().equals("1")) {
                orderNum.setSharp(this.formatLongData(o.getChargerOrderNumber(), 0L));
                electricityAmount.setSharp(this.formatData(o.getOrderElectricityAmount(), 4));
                orderPriceAmount.setSharp(this.formatData(o.getOrderPriceAmount(), 2));
                elecPriceAmount.setSharp(this.formatData(o.getElecPriceAmount(), 2));
                servicePriceAmount.setSharp(this.formatData(o.getServicePriceAmount(), 2));
            } else if (o.getTag().equals("2")) {
                orderNum.setPeak(this.formatLongData(o.getChargerOrderNumber(), 0L));
                electricityAmount.setPeak(this.formatData(o.getOrderElectricityAmount(), 4));
                orderPriceAmount.setPeak(this.formatData(o.getOrderPriceAmount(), 2));
                elecPriceAmount.setPeak(this.formatData(o.getElecPriceAmount(), 2));
                servicePriceAmount.setPeak(this.formatData(o.getServicePriceAmount(), 2));
            } else if (o.getTag().equals("3")) {
                orderNum.setFlat(this.formatLongData(o.getChargerOrderNumber(), 0L));
                electricityAmount.setFlat(this.formatData(o.getOrderElectricityAmount(), 4));
                orderPriceAmount.setFlat(this.formatData(o.getOrderPriceAmount(), 2));
                elecPriceAmount.setFlat(this.formatData(o.getElecPriceAmount(), 2));
                servicePriceAmount.setFlat(this.formatData(o.getServicePriceAmount(), 2));
            } else if (o.getTag().equals("4")) {
                orderNum.setValley(this.formatLongData(o.getChargerOrderNumber(), 0L));
                electricityAmount.setValley(this.formatData(o.getOrderElectricityAmount(), 4));
                orderPriceAmount.setValley(this.formatData(o.getOrderPriceAmount(), 2));
                elecPriceAmount.setValley(this.formatData(o.getElecPriceAmount(), 2));
                servicePriceAmount.setValley(this.formatData(o.getServicePriceAmount(), 2));
            }
        });

        orderNum.setPeak(orderNum.getPeak().split("\\.")[0]);
        orderNum.setTotal(orderNum.getTotal().split("\\.")[0]);
        orderNum.setValley(orderNum.getValley().split("\\.")[0]);
        orderNum.setFlat(orderNum.getFlat().split("\\.")[0]);
        orderNum.setSharp(orderNum.getSharp().split("\\.")[0]);
        electricityAmount.setSharp(
            electricityAmount.getSharp() == "0.00" ? "0.0000" : electricityAmount.getSharp());
        electricityAmount.setFlat(
            electricityAmount.getFlat() == "0.00" ? "0.0000" : electricityAmount.getFlat());
        electricityAmount.setPeak(
            electricityAmount.getPeak() == "0.00" ? "0.0000" : electricityAmount.getPeak());
        electricityAmount.setValley(
            electricityAmount.getValley() == "0.00" ? "0.0000" : electricityAmount.getValley());

        ChargerOrderDetailVo chargerOrderDetailVo = new ChargerOrderDetailVo();
        chargerOrderDetailVo.setOrderNum(orderNum);
        chargerOrderDetailVo.setElecPriceAmount(elecPriceAmount);
        chargerOrderDetailVo.setElectricityAmount(electricityAmount);
        chargerOrderDetailVo.setOrderPriceAmount(orderPriceAmount);
        chargerOrderDetailVo.setServicePriceAmount(servicePriceAmount);

        return new ObjectResponse<>(chargerOrderDetailVo);
    }

    public List<CorpOrderCountVo> corpOrderCount(OrderCountParam param) {
        List<CorpOrderCountVo> ret = chargerOrderRoDs.corpOrderCount(param);
        if (CollectionUtils.isEmpty(ret)) {
            log.warn("没任何订单信息，此处填上默认值。{}", param);
            return param.getPayAccountIdList()
                .stream()
                .map(e -> {
                    CorpOrderCountVo rets = new CorpOrderCountVo();
                    rets.setPayAccountId(e);
                    rets.setOrderCount(0);
                    rets.setPaidFee(BigDecimal.ZERO);
                    return rets;
                })
                .collect(Collectors.toList());
        }
        return ret;
    }

    public List<ChargerOrderDailyExportVO> chargerOrderDailyVOList(ChargerOrderParam searchParam) {
        HashMap<String, Object> searchMap = this.chargerOrderParamToSearchMap(searchParam);
        List<ChargerOrderDailyVo> chargerOrderDailyVolist;
        List<ChargerOrderDailyVo> zftDailyInFlowList;
        List<ChargerOrderDailyVo> zftDailyOutFlowList;
        if (historyDataUtils.checkListOrderParam(searchParam, true)) {
            chargerOrderDailyVolist = chargerOrderRoDs.chargerOrderDailyList(
                searchMap);//订单数据
            zftDailyInFlowList = chargerOrderRoDs.zftDailyInFlowList(
                searchMap);
            zftDailyOutFlowList = chargerOrderRoDs.zftDailyOutFlowList(
                searchMap);
        } else {
            chargerOrderDailyVolist = hisOrderFeignClient.chargerOrderDailyList(
                    searchParam).doOnNext(FeignResponseValidate::check).map(ListResponse::getData)
                .block(Duration.ofSeconds(50L));

            zftDailyInFlowList = hisOrderFeignClient.zftDailyInFlowList(
                    searchParam).doOnNext(FeignResponseValidate::check).map(ListResponse::getData)
                .block(Duration.ofSeconds(50L));

            zftDailyOutFlowList = hisOrderFeignClient.zftDailyOutFlowList(
                    searchParam)
                .doOnNext(FeignResponseValidate::check)
                .map(ListResponse::getData)
                .block(Duration.ofSeconds(50L));
        }
//        List<ChargerOrderDailyVo> chargerOrderDailyVolist = chargerOrderRoDs.chargerOrderDailyList(
//            searchMap);//订单数据
//        List<ChargerOrderDailyVo> zftDailyInFlowList = chargerOrderRoDs.zftDailyInFlowList(
//            searchMap);
//        List<ChargerOrderDailyVo> zftDailyOutFlowList = chargerOrderRoDs.zftDailyOutFlowList(
//            searchMap);
        Map<String, ChargerOrderDailyExportVO> map = new HashMap<>();
        chargerOrderDailyVolist.forEach(e -> {
            ChargerOrderDailyExportVO chargerOrderDailyExportVO = new ChargerOrderDailyExportVO();
            chargerOrderDailyExportVO.setDailyDay(e.getDailyDay());
            chargerOrderDailyExportVO.setServOriginFee(
                e.getServOriginFee() == null ? BigDecimal.ZERO : e.getServOriginFee());
            chargerOrderDailyExportVO.setServicePrice(
                e.getServicePrice() == null ? BigDecimal.ZERO : e.getServicePrice());
            chargerOrderDailyExportVO.setElecOriginFee(
                e.getElecOriginFee() == null ? BigDecimal.ZERO : e.getElecOriginFee());
            chargerOrderDailyExportVO.setElecPrice(
                e.getElecPrice() == null ? BigDecimal.ZERO : e.getElecPrice());
            chargerOrderDailyExportVO.setOrderPrice(
                e.getOrderPrice() == null ? BigDecimal.ZERO : e.getOrderPrice());
            chargerOrderDailyExportVO.setParkingFee(
                e.getParkingFee() == null ? BigDecimal.ZERO : e.getParkingFee());
            chargerOrderDailyExportVO.setServProfit(
                e.getServProfit() == null ? BigDecimal.ZERO : e.getServProfit());
            chargerOrderDailyExportVO.setElecProfit(
                e.getElecProfit() == null ? BigDecimal.ZERO : e.getElecProfit());
            chargerOrderDailyExportVO.setTotalProfit(
                e.getTotalProfit() == null ? BigDecimal.ZERO : e.getTotalProfit());
            map.put(e.getDailyDay(), chargerOrderDailyExportVO);
        });
        zftDailyInFlowList.forEach(e -> {
            ChargerOrderDailyExportVO chargerOrderDailyExportVO = map.get(e.getDailyDay());
            if (chargerOrderDailyExportVO == null) {
                chargerOrderDailyExportVO = new ChargerOrderDailyExportVO();
            }
            chargerOrderDailyExportVO.setZftTotalMoney(e.getZftTotalMoney());
            map.put(e.getDailyDay(), chargerOrderDailyExportVO);
        });
        zftDailyOutFlowList.forEach(e -> {
            ChargerOrderDailyExportVO chargerOrderDailyExportVO = map.get(e.getDailyDay());
            if (chargerOrderDailyExportVO == null) {
                chargerOrderDailyExportVO = new ChargerOrderDailyExportVO();
            }
            chargerOrderDailyExportVO.setZftTotalMoney(
                (chargerOrderDailyExportVO.getZftTotalMoney() == null ? BigDecimal.ZERO
                    : chargerOrderDailyExportVO.getZftTotalMoney()).subtract(e.getZftTotalMoney()));
            map.put(e.getDailyDay(), chargerOrderDailyExportVO);
        });
        List<ChargerOrderDailyExportVO> chargerOrderDailyExportVOList = new ArrayList<>(
            map.values());
        chargerOrderDailyExportVOList.forEach(e -> {
            e.setCheckDiffMoney(e.getOrderPrice().add(e.getParkingFee())
                .subtract(e.getZftTotalMoney() == null ? BigDecimal.ZERO : e.getZftTotalMoney()));
        });
        return chargerOrderDailyExportVOList.stream()
            .sorted(Comparator.comparing(ChargerOrderDailyExportVO::getDailyDay))
            .collect(Collectors.toList());
    }

    public List<String> list4LowKwAnalyse(int size) {
        return chargerOrderRoDs.list4LowKwAnalyse(size);
    }

    public Mono<ListResponse<ChargerOrderVo>> queryChargeOrderListX(ListChargerOrderParamX param) {
        return Mono.just(param)
            .map(chargerOrderRoDs::queryChargeOrderListX)
            .map(RestUtils::buildListResponse)
            .doOnNext(res -> {
                if (null != param.getTotal() && param.getTotal()) {
                    res.setTotal(chargerOrderRoDs.countChargeOrderX(param));
                }
            });
    }

    /**
     * 查询小电流充电订单列表
     */
    public ListResponse<LowKwOrderDto> getLowKwOrderList(ListChargeOrderParam param) {
        log.info("param = {}", param);
        return this.chargerOrderRoDs.getLowKwOrderList(param);
    }

    /**
     * 查询未支付的信用充订单
     * @return 返回订单号列表
     */
    public List<String> getUnpaidCreditOrderNoList(ListChargeOrderParam param) {
        log.info("param = {}", param);
        return this.chargerOrderRoDs.getUnpaidCreditOrderNoList(param);
    }
}
