package com.cdz360.biz.bi.utils;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.BaseFont;
import freemarker.template.Configuration;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringWriter;
import java.io.Writer;
import java.util.Set;
import java.util.UUID;
import lombok.extern.slf4j.Slf4j;
import no.api.freemarker.java8.Java8ObjectWrapper;
import org.springframework.core.io.ClassPathResource;
import org.xhtmlrenderer.pdf.ITextRenderer;

/**
 * PDF 操作
 */
@Slf4j
public class PdfUtil {

    private static final String PDF_STORE_PATH = File.separator + "tmp" + File.separator + "pdf";
    private static final String HTML_TEMP_PATH = PDF_STORE_PATH + File.separator + "html";
    private static final String HTML_TEMP_SRC_PATH = "freemarker";
    private static final String HTML_TEMP_FONT_SRC_PATH = "freemarker" + File.separator + "fonts";

    private static final String HTML_TEMP_FONT_NAME = "simhei.ttf";

    private static final String HTML_TEMP_COMMON_DIR = "oa";

    private static final Set<String> COMMON_HTML_FILE_NAME = Set.of(
        "oa_common_signet.html",
        "oa_common_payment_plans.html",
        "oa_common_contracts.html",
//        通用控件 👆
        "oa_cnt_prepaid_order.html",
        "oa_cnt_deposit_process.html",
        "oa_cnt_billing_process.html",
        "oa_cnt_recharge_process.html",
        "oa_cnt_other_fee_pay_apply.html",
        "oa_cnt_charge_fee.html",
        "oa_cnt_rent_payment_process.html",
        "invoice_buyer_info_corp_common.html",
        "invoice_buyer_info_per_common.html",
        "invoice_buyer_info_corp_profession.html",
        "oa_cnt_income_site_oa.html",
        "oa_cnt_site_distribution.html",
        "oa_cnt_pay_elec_fee.html",
        "oa_cnt_default.html",
        "oa_audit_reference.html",
        "oa_proc_inst_comment.html",
        "oa_pdf_footer.html",
        "oa_pdf_header.html"
//        上面是新PDF导出模板需要 👆
    );

    // 文件目录
    private String FILE_TARGET_DIR;

    // 临时文件
    private String TEMP_FILE;

    // 目标文件
    private String TARGET_FILE;

    // 模板文件目录
    private String TEMPLATE_HTML_FILE;

    // 模板文件名称
    private String TEMPLATE_HTML_FILE_NAME;

    // 模板数据
    private String htmlCnt;

    private static final Configuration FREE_MARKER_CFG =
        new Configuration(Configuration.VERSION_2_3_0);

    static {
        try {
            // 模板目录
            newDir(HTML_TEMP_PATH);

            FREE_MARKER_CFG.setObjectWrapper(new Java8ObjectWrapper(Configuration.VERSION_2_3_0));
            FREE_MARKER_CFG.setDirectoryForTemplateLoading(new File(HTML_TEMP_PATH));
        } catch (IOException e) {
            log.error("配置HTML模板异常: err = {}", e.getMessage(), e);
        }
    }

    private static void newDir(String dir) {
        File path = new File(dir);
        if (!path.exists()) {
            boolean b = path.mkdirs();
        }
    }

    private PdfUtil() {
    }

    public static PdfUtil builder(String TEMP_DIR, ExcelPosition excelPosition,
        String templateHtmlFile) {
        return new PdfUtil()
            .fileInfo(TEMP_DIR, excelPosition, templateHtmlFile);
    }

    private PdfUtil fileInfo(String TEMP_DIR, ExcelPosition excelPosition,
        String templateHtmlFile) {
        this.FILE_TARGET_DIR = TEMP_DIR + File.separator + excelPosition.getSubDir();
        this.TEMP_FILE = this.FILE_TARGET_DIR + File.separator +
            UUID.randomUUID().toString().replaceAll("-", "") + ".pdf";
        this.TARGET_FILE =
            this.FILE_TARGET_DIR + File.separator + excelPosition.getSubFileName() + ".pdf";
        this.TEMPLATE_HTML_FILE_NAME = templateHtmlFile;
        this.TEMPLATE_HTML_FILE = HTML_TEMP_PATH + File.separator + templateHtmlFile; // 这个为模板文件绝对路径

        // 存放PDF和模板文件目录
        newDir(FILE_TARGET_DIR);
        return this;
    }

    private static void newSrcFile(String resPath, String file) {
        try (InputStream in = new ClassPathResource(resPath).getInputStream();
            FileOutputStream fos = new FileOutputStream(file)) {
            byte[] b = new byte[1024];
            int len;
            while ((len = in.read(b)) != -1) {
                fos.write(b, 0, len);
            }
            log.debug("SRC_FILE: {}", file);
        } catch (Exception e) {
            log.error("拷贝源文件异常: err = {}", e.getMessage(), e);
            throw new DcServiceException("拷贝源文件异常");
        }
    }

    private void commonHtmlTemplate() {
        newDir(HTML_TEMP_PATH + File.separator + HTML_TEMP_COMMON_DIR);
        COMMON_HTML_FILE_NAME.forEach(name -> {
            String postPath = File.separator + HTML_TEMP_COMMON_DIR + File.separator + name;
            String targetPath = HTML_TEMP_PATH + postPath;
            File target = new File(targetPath);
            if (!target.exists()) {
                String resPath = HTML_TEMP_SRC_PATH + postPath;
                newSrcFile(resPath, targetPath);
            }
        });
//        try {
////            File dir = new ClassPathResource(
////                HTML_TEMP_SRC_PATH + File.separator + HTML_TEMP_COMMON_DIR).getFile();
//            File dir = ResourceUtils.getFile(ResourceUtils.CLASSPATH_URL_PREFIX +
//                HTML_TEMP_SRC_PATH + File.separator + HTML_TEMP_COMMON_DIR);
//            if (dir.exists() && dir.listFiles() != null) {
//                Arrays.stream(Objects.requireNonNull(dir.listFiles())).forEach(file -> {
//                    String postPath = HTML_TEMP_COMMON_DIR + File.separator + file.getName();
//                    String targetPath = HTML_TEMP_PATH + File.separator + postPath;
//                    File target = new File(targetPath);
//                    if (!target.exists()) {
//                        String resPath = HTML_TEMP_SRC_PATH + File.separator + postPath;
//                        newSrcFile(resPath, targetPath);
//                    }
//                });
//            }
//
//        } catch (Exception e) {
//            log.error("PDF导出依赖freemarker模板拷贝异常: {}", e.getMessage(), e);
//        }
    }

    private PdfUtil genHtml(Object rootMap) {
        try (Writer out = new StringWriter()) {
            // 公用模板加载
            this.commonHtmlTemplate();

            // 获取模板,并设置编码方式
            File htmlPath = new File(TEMPLATE_HTML_FILE);
            if (!htmlPath.exists()) {
                String resPath = HTML_TEMP_SRC_PATH + File.separator + TEMPLATE_HTML_FILE_NAME;
                newSrcFile(resPath, TEMPLATE_HTML_FILE);
            }

            // 合并数据模型与模板
            FREE_MARKER_CFG.getTemplate(TEMPLATE_HTML_FILE_NAME, "UTF-8")
                .process(rootMap, out); //将合并后的数据和模板写入到流中，这里使用的字符流

            htmlCnt = out.toString();
        } catch (Exception e) {
            log.info("生成HTML异常: {}", e.getMessage(), e);
            throw new DcServiceException("生成HTML异常: " + TEMP_FILE);
        }

        return this;
    }

    public PdfUtil genPdf(Object rootMap) {
        return this.genHtml(rootMap)
            .genPdf();
    }

    private PdfUtil genPdf() {
        try {
            String fontPath = HTML_TEMP_PATH + File.separator + HTML_TEMP_FONT_NAME;
            File font = new File(fontPath);
            if (!font.exists()) {
                String resPath = HTML_TEMP_FONT_SRC_PATH + File.separator + HTML_TEMP_FONT_NAME;
                newSrcFile(resPath, fontPath);
            }

            ITextRenderer render = new ITextRenderer();
            render.getFontResolver()
                .addFont(fontPath, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);

            render.setDocumentFromString(htmlCnt);
            render.layout();
            render.createPDF(new FileOutputStream(TEMP_FILE));
        } catch (DocumentException | IOException e) {
            log.error("生成PDF异常: err= {}", e.getMessage(), e);
            throw new DcServiceException("生成PDF异常: " + TEMP_FILE);
        }

        return this;
    }

    public void write2File() {
        File folder = new File(this.FILE_TARGET_DIR);
        //如果文件夹不存在则创建
        if (!folder.exists()) {
            boolean b = folder.mkdirs();
        }

        boolean b = new File(this.TEMP_FILE).renameTo(new File(this.TARGET_FILE));
    }
}
