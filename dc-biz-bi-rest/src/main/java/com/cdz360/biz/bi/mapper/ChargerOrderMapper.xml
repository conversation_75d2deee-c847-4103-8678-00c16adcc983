<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.bi.mapper.ChargerOrderMapper">
    <resultMap id="SampleResultMap" type="com.chargerlinkcar.framework.common.domain.vo.ChargerOrderSample">
        <result column="topCommId" jdbcType="BIGINT" property="topCommId"/>
        <result column="station_id" jdbcType="VARCHAR" property="stationId"/>
        <result column="station_name" jdbcType="VARCHAR" property="stationName"/>
        <result column="qr_code" jdbcType="VARCHAR" property="qrCode"/>
        <result column="duration" jdbcType="VARCHAR" property="duration"/>
        <result column="order_electricity" jdbcType="BIGINT" property="orderElectricity"/>
        <result column="orderFee" jdbcType="BIGINT" property="orderPrice"/>
        <result column="servFee" jdbcType="BIGINT" property="servicePrice"/>
        <result column="elecFee" jdbcType="BIGINT" property="elecPrice"/>
        <result column="card_no" jdbcType="VARCHAR" property="cardNo" />
        <result column="card_chip_no" jdbcType="VARCHAR" property="cardChipNo" />

        <result column="principal_amount" jdbcType="BIGINT" property="principalAmount"/>
        <result column="charge_start_time" jdbcType="INTEGER" property="chargeStartTime"/>
        <result column="charge_end_time" jdbcType="INTEGER" property="chargeEndTime"/>
        <result column="vin" jdbcType="VARCHAR" property="vin"/>
        <result column="start_soc" jdbcType="VARCHAR" property="startSoc"/>
        <result column="stop_soc" jdbcType="VARCHAR" property="stopSoc"/>
        <result column="stop_reason" jdbcType="VARCHAR" property="stopReason"/>

        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="actual_price" jdbcType="BIGINT" property="actualPrice"/>
        <result column="discount" jdbcType="BIGINT" property="discount"/>
        <!--        <result column="show_id" jdbcType="VARCHAR" property="showId" />-->
        <result column="plugNo" jdbcType="VARCHAR" property="plugNo"/>
        <result column="connect_id" jdbcType="VARCHAR" property="connectId"/>
        <result column="connector_id" jdbcType="VARCHAR" property="connectorId"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="channel_id" jdbcType="INTEGER" property="channelId"/>
        <result column="current_soc" jdbcType="VARCHAR" property="currentSoc"/>
        <result column="mobile_phone" jdbcType="VARCHAR" property="mobilePhone"/>
        <!--        <result column="box_out_factory_code" jdbcType="VARCHAR" property="boxOutFactoryCode" />-->

        <result column="discount_money" jdbcType="BIGINT" property="discountMoney"/>
        <result column="couponAmount" jdbcType="BIGINT" property="couponMoney" />

        <result column="payMode" jdbcType="TINYINT" property="payModes" />
        <result column="payStatus" jdbcType="TINYINT" property="payStatus" />
        <result column="charger_name" jdbcType="VARCHAR" property="chargerName" />
        <result column="abnormal" jdbcType="BIGINT" property="abnormal"/>
        <result column="frozenAmount" jdbcType="BIGINT" property="frozenAmount"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="customer_name" jdbcType="VARCHAR" property="customerName"/>
        <result column="payAccountId" jdbcType="VARCHAR" property="payAccountId"/>
        <result column="defaultPayType" jdbcType="VARCHAR" property="defaultPayType"/>
        <result column="customer_id" jdbcType="BIGINT" property="customerId"/>
    </resultMap>

    <resultMap extends="SampleResultMap" id="ChargerOrderVoMap"
               type="com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo">
        <result column="charger_name" jdbcType="LONGVARCHAR" property="chargerName" />
        <result column="box_name" jdbcType="LONGVARCHAR" property="evseName" />
        <result column="box_code" jdbcType="VARCHAR" property="boxCode" />
        <result column="vehicle_num" jdbcType="VARCHAR" property="vehicleNum" />
        <result column="commercial_name" jdbcType="VARCHAR" property="commercialName" />
    </resultMap>


    <!--在线订单查询抽取出的where-->
    <!--查询异常订单时需包括-500的订单 (若有statusList，则不需要拼接status) -wangzheng-->
    <!--若有statusList且想查异常订单，则需包括abnormal为true -wangzheng-->
    <sql id="ChargerOrderList_whereSql">
        <if test="commIdList != null and commIdList.size()>0  ">
            t1.device_commercial_id IN
            <foreach item="item" index="index" collection="commIdList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="topCommId != null ">
            <![CDATA[ OR (t1.topCommId = #{topCommId}) ]]>
        </if>

        <if test="orderNo != null and orderNo !=''">
            <![CDATA[ AND (t1.order_no LIKE CONCAT('%', #{orderNo}, '%'))]]>
        </if>
        <if test="customerId != null">
            <![CDATA[ AND (t1.customer_id = #{customerId}) ]]>
        </if>
        <if test="status != null and status!=''  ">
            <![CDATA[  AND (t1.status = #{status}) ]]>
        </if>
        <if test="statusList == null and abnormal != null">
            <![CDATA[  AND (t1.status = -500 or abnormal = #{abnormal}) ]]>
        </if>
        <if test="orderStatus != null and orderStatus!=''  ">
            <![CDATA[  AND (t1.order_status = #{orderStatus}) ]]>
        </if>

        <if test="payModes != null and payModes!=''  ">
            <![CDATA[  AND (pay.payMode = #{payModes}) ]]>
        </if>
        <if test="statusList != null and statusList.size()>0  ">
            AND (
            t1.status IN
            <foreach item="item" index="index" collection="statusList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
            <choose>
                <when test="excludeCancelOrder != null and excludeCancelOrder">
                    and t1.order_status != 'CANCEL' )
                </when>
                <when test="abnormal != null">
                    and t1.abnormal = #{abnormal}
                    <if test="includeErrorOrder != null and includeErrorOrder">
                        OR t1.`status` = -500
                    </if>
                    )
                </when>
                <otherwise>
                    )
                </otherwise>
            </choose>
        </if>
        <if test="connectId != null and connectId!=''">
            <![CDATA[ AND (t1.connect_id = #{connectId}) ]]>
        </if>
        <if test="channelIdList != null and channelIdList.size()>0  ">
            AND t1.channel_id IN
            <foreach item="item" index="index" collection="channelIdList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="channelId != null ">
            <![CDATA[ AND (t1.channel_id = #{channelId}) ]]>
        </if>
        <if test="vin != null and vin !='' and vin !='null' ">
            <![CDATA[ AND (t1.vin = #{vin}) ]]>
        </if>
        <if test="beginTime!=null and beginTime!='' and beginTime!='null'" >
            <![CDATA[ and t1.create_time >= #{beginTime} ]]>
        </if>
        <if test="endTime!=null and endTime!='' and endTime!='null'">
            <![CDATA[ and t1.create_time <= #{endTime} ]]>
        </if>
        <if test="cardNo != null and cardNo!='' "><!-- and cardNo !='' -->
            <![CDATA[ AND (t1.card_no LIKE CONCAT('%', #{cardNo}, '%')) ]]>
        </if>

        <if test="stationId != null and stationId !=''">
            <![CDATA[ AND (t1.station_id LIKE CONCAT('%', #{stationId}, '%')) ]]>
        </if>
        <if test="connectorId != null and connectorId !=''">
            <![CDATA[ AND (t1.connector_id LIKE CONCAT('%', #{connectorId}, '%')) ]]>
        </if>

        <if test="stationName != null and stationName !='' ">
            <![CDATA[ AND (t1.station_name LIKE CONCAT('%', #{stationName}, '%')) ]]>
        </if>
        <if test="mobilePhone != null and mobilePhone !=''">
            <![CDATA[ AND (t1.mobile_phone LIKE CONCAT('%', #{mobilePhone}, '%')) ]]>
        </if>
        <if test="customerName != null and customerName !=''">
            <![CDATA[ AND (t1.customer_name LIKE CONCAT('%', #{customerName}, '%')) ]]>
        </if>
        <if test="openOrderId != null and openOrderId!=''">
            <![CDATA[ AND (t1.open_order_id = #{openOrderId}) ]]>
        </if>

        <if test="stationIds != null and stationIds.size()>0  ">
            AND t1.station_id IN
            <foreach item="item" index="index" collection="stationIds"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="orderNos != null and orderNos.size()>0  ">
            AND t1.order_no IN
            <foreach item="item" index="index" collection="orderNos" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="stopReason != null and stopReason !='' and stopReason == '手动结束'">
            <![CDATA[ AND stop_reason = #{stopReason,jdbcType=VARCHAR}]]>
        </if>
        <if test="stopReason != null and stopReason !='' and stopReason != '手动结束'">
            <![CDATA[ AND (stop_reason != '手动结束' or stop_reason is null)]]>
        </if>
        <if test="cardChipNo != null and cardChipNo !=''">
            AND t1.card_chip_no = #{cardChipNo}
        </if>
        <if test="payAccountId != null">
            <![CDATA[ AND t1.payAccountId = #{payAccountId} ]]>
        </if>
        <if test="defaultPayType != null">
            <![CDATA[ AND t1.defaultPayType = #{defaultPayType} ]]>
        </if>
        <if test="lineNum != null and lineNum!=''">
            <![CDATA[ AND (t1.lineNum LIKE CONCAT('%', #{lineNum}, '%')) ]]>
        </if>
    </sql>


    <!-- 订单导出专用去除冗余字段 -->
    <select id="selectChargeOrderListForExcel" parameterType="map"
            resultMap="ChargerOrderVoMap">
        SELECT
        t1.topCommId,
        t1.station_id,
        t1.station_name,
        t1.qr_code qrCode,
        t1.duration duration,
        t1.order_electricity,
        pay.servFee,
        pay.elecFee,
        pay.orderFee,
        t1.actual_price,
        <!--        t1.card_no,-->
        t1.card_chip_no,
        <!--        t1.show_id,-->
        t1.connect_id,
        t1.connector_id,
        t1.status,
        t1.order_status,
        (pay.elecCostFee+pay.servCostFee) as principal_amount,
        t1.create_time,
        t1.channel_id,
        t1.charge_start_time,
        t1.charge_end_time,
        t1.vin,
        t1.current_soc,
        t1.start_soc,
        t1.stop_soc,
        t1.discount_money,
        <!--        t1.coupon_money,-->
        <!--        t1.pay_modes,-->
        <!--        t1.pay_status,-->
        <!--        t1.stop_reason,-->
        <!--        t1.box_out_factory_code,-->
        t1.mobile_phone,
        <!--        t1.commercial_name,-->
        <!--        IFNULL(t1.frozenAmount,0) frozenAmount,-->
        t1.order_no,
        t1.charger_name,
        t1.lineNum,
        t1.box_code,
        t1.plugNo

        FROM
        t_charger_order t1
        left join t_charger_order_pay pay on pay.orderNo = t1.order_no
        <where>
            <include refid="ChargerOrderList_whereSql"/>
        </where>
        ORDER BY t1.create_time DESC

    </select>

</mapper>