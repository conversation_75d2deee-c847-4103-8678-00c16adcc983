package com.cdz360.biz.bi.service;

import com.cdz360.biz.bi.mapper.CommercialQueryMapper;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class CommercialQueryDs {

    // private static final Integer TTL = 10 * 60; // 缓存有效期10分钟
    // Map<Long, MemCache<CommercialSimpleVo>> caches = new ConcurrentHashMap<>();
    @Autowired
    private CommercialQueryMapper commercialQueryMapper;

    public List<CommercialSimpleVo> getCommByPid(Long commId) {
        return commercialQueryMapper.getCommByPid(commId);
    }

    public CommercialSimpleVo getCommById(Long id) {
        return commercialQueryMapper.getCommerial(id);
    }

    public List<CommercialSimpleVo> getCommerialsByCommIds(List<Long> commIds) {
        return commercialQueryMapper.getCommerialsByCommIds(commIds);
    }

    // /**
    //  * 根据ID查询商户信息. 优先从缓存中获取, 缓存时间为10分钟
    //  *
    //  * @param commercialId
    //  * @return
    //  */
    // public CommercialSimpleVo getCommercial(long commercialId) {
    //     MemCache<CommercialSimpleVo> cache = caches.get(commercialId);
    //     if (cache == null) {
    //         // 缓存未命中, 查询数据库
    //         cache = buildCache(commercialId);
    //     } else if (cache.getExpire().before(new Date())) {
    //         // 缓存数据已过期, 重新查询数据库
    //         cache = buildCache(commercialId);
    //     }
    //     return cache != null ? cache.getData() : null;
    // }

    // /**
    //  * 查询ID对应的所有子商户ID列表. 不含 commercialId
    //  *
    //  * @param commercialId
    //  * @return
    //  */
    // public List<Long> listSubCommercialIds(long commercialId) {
    //     return this.commercialQueryMapper.listSubCommercialIds(commercialId);
    // }


//    /**
//     * 查询ID对应的所有子商户ID列表. 含 commercialId
//     *
//     * @param commercialId
//     * @return
//     */
//    public List<Long> listCommercialIds(long commercialId) {
//        List<Long> ret = this.commercialQueryMapper.listSubCommercialIds(commercialId);
//        ret.add(commercialId);
//        return ret;
//    }

    // private MemCache buildCache(long commercialId) {
    //     CommercialSimpleVo commercial = this.commercialQueryMapper.getCommerial(commercialId);
    //     if (commercial == null) {
    //         return null;
    //     }
    //     MemCache cache = new MemCache(commercial, TTL);
    //     caches.put(commercial.getId(), cache);
    //     return cache;
    // }

    // /**
    //  * 递归获取顶级商户，FIXME 可能存在调用栈崩溃的问题
    //  * @param id
    //  * @return
    //  */
    // public CommercialSimpleVo getTopCommercial(Long id) {
    //     log.info("R查找此商户: {}", id);
    //     CommercialSimpleVo rCommercialSimpleVo = commercialQueryMapper.getCommerial(id);
    //     if(rCommercialSimpleVo == null) {
    //         log.error("R找不到对应商户: {}", id);
    //         return null;
    //     }
    //     if(rCommercialSimpleVo.getPid() != null) {
    //         return getTopCommercial(rCommercialSimpleVo.getPid());
    //     } else {
    //         return rCommercialSimpleVo;
    //     }
    // }
    //
    // /**
    //  * 获取商户的上级id集合（含当前商户id）
    //  *
    //  * @param id
    //  * @return
    //  */
    // public List<Long> getCommercialPidList(Long id) {
    //     List<Long> list = new ArrayList<>();
    //     CommercialSimpleVo rCommercialSimpleVo = commercialQueryMapper.getCommerial(id);
    //     if (rCommercialSimpleVo == null) {
    //         log.error("找不到对应商户: {}", id);
    //         return null;
    //     }
    //     while (rCommercialSimpleVo.getPid() != null) {
    //         list.add(rCommercialSimpleVo.getId());
    //         rCommercialSimpleVo = commercialQueryMapper.getCommerial(rCommercialSimpleVo.getPid());
    //     }
    //     return list;
    // }
    //
    // /**
    //  * 查询商户列表信息
    //  *
    //  * @return
    //  */
    // public List<CommercialSimpleVo> getCommercialSimpleVoList() {
    //     List<CommercialSimpleVo> list = commercialQueryMapper.getCommercialSimpleVoList();
    //     return list;
    // }
}
