package com.cdz360.biz.bi.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_TRADING)
public interface TradingFeignClient {

//    @RequestMapping(value = "/api/order/exportChargeOrderListV2",method = RequestMethod.POST)
//    ListResponse<ChargerOrderVo> exportChargeOrderListV2(@RequestBody ChargerOrderParam chargerOrderParam);
//    @RequestMapping(value = "/api/order/exportChargerOrderData",method = RequestMethod.POST)
//    ListResponse<ChargerOrderDataVo> exportChargerOrderData(@RequestBody ChargerOrderParam chargerOrderParam);
//    @RequestMapping(value = "/api/order/getChargerOrderData",method = RequestMethod.POST)
//    ObjectResponse<ChargerOrderDataVo> getChargerOrderData(@RequestBody ChargerOrderParam chargerOrderParam);
//    @RequestMapping(value = "/api/order/queryChargeOrderList", method = RequestMethod.POST)
//    ListResponse<ChargerOrderVo> queryChargeOrderList(@RequestBody ChargerOrderParam searchParam);
}
