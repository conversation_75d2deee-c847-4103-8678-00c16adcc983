package com.cdz360.biz.bi.domain.vo;

import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.biz.model.annotations.ExcelField;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "车辆详情订单导出主体")
@EqualsAndHashCode(callSuper = true)
public class VinExportBodyVo extends BaseObject
    implements Serializable {

    @ExcelField(title = "订单号", sort = 1)
    private String orderNo;

    @ExcelField(title = "所属商户", sort = 2)
    private String commName;

    @ExcelField(title = "站点名称", sort = 3)
    private String siteName;

    @ExcelField(title = "订单来源", sort = 4)
    private String orderChannel;

    @ExcelField(title = "支付方式", sort = 5)
    private String payType;

    @ExcelField(title = "开始时间/结束时间", sort = 6)
    private String chargingTime;

    @ExcelField(title = "充电电量(kW·h)", sort = 7, digits = 4)
    private BigDecimal orderElectric;

    @ExcelField(title = "电费(元)", sort = 8)
    private BigDecimal elecFee;

    @ExcelField(title = "服务费(元)", sort = 9)
    private BigDecimal servFee;

    @ExcelField(title = "订单总金额(元)", sort = 10)
    private BigDecimal orderFee;

    @ExcelField(title = "订单状态", sort = 11)
    private String status;

    @ExcelField(title = "创建时间", sort = 12, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ExcelField(title = "扣款账户", sort = 13)
    private String debtAccount;

}
