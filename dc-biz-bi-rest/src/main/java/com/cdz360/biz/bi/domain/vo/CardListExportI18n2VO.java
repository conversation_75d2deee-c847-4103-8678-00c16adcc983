package com.cdz360.biz.bi.domain.vo;


import com.cdz360.biz.model.annotations.ExcelField;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;


/**
 * 卡片列表查询卡片详情
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "在线卡信息")
public class CardListExportI18n2VO implements Serializable {


    @ExcelField(title = "卡号", i18nTitle = "card.cardNo", sort = 1)
    @Schema(description = "卡号")
    private String cardChipNo;

    @ExcelField(title = "卡名称", i18nTitle = "card.cardName", sort = 2)
    @Schema(description = "卡名称")
    private String cardName;

    @ExcelField(title = "所属商户", i18nTitle = "common.commercialFullName", sort = 3)
    @Schema(description = "所属商户")
    private String commName;

    @ExcelField(title = "客户名称", i18nTitle = "common.userName", sort = 4)
    @Schema(description = "客户名称")
    private String userName;

    @ExcelField(title = "车牌号", i18nTitle = "car.carNo", sort = 5)
    @Schema(description = "车牌号")
    private String carNo;

    @ExcelField(title = "车队", i18nTitle = "car.carDepartWithoutName", sort = 6)
    @Schema(description = "车队")
    private String carDepart;

    @ExcelField(title = "线路", i18nTitle = "car.lineNum", sort = 7)
    @Schema(description = "线路")
    private String lineNum;

    @ExcelField(title = "车辆自编号", i18nTitle = "car.carNum", sort = 8)
    @Schema(description = "车辆自编号")
    private String carNum;

    @ExcelField(title = "指定场站数量", i18nTitle = "card.usableStationCount", sort = 9)
    @Schema(description = "指定场站数量")
    private String usableStationCount;

    @ExcelField(title = "状态", i18nTitle = "common.status", sort = 10)
    @Schema(description = "卡状态：10000未激活，10001已激活，10002卡锁定(已挂失)，10005已失效(黑名单)，10006已过期")
    private String cardStatus;

    @ExcelField(title = "创建时间", i18nTitle = "common.createTime", sort = 11)
    @Schema(description = "创建时间")
    private Date cardCreateDate;
}
