package com.cdz360.biz.bi.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.DateUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.bi.service.ExcelBiService;
import com.cdz360.biz.bi.service.site.SiteBizService;
import com.cdz360.biz.bi.service.site.SiteIncomeExpenseService;
import com.cdz360.biz.bi.service.site.SiteOrderBiService;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteDailyBiRoDs;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.bi.site.ExcelBiPosition;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.iot.param.SiteBiParam;
import com.cdz360.biz.model.oa.vo.OaElecPayIncomeExpenseVo;
import com.cdz360.biz.model.site.vo.SiteIncomeExpenseVo;
import com.cdz360.biz.model.trading.comm.dto.CommSiteEvseCount;
import com.cdz360.biz.model.trading.site.dto.CitySiteNumDto;
import com.cdz360.biz.model.trading.site.dto.DistrictSiteNumDto;
import com.cdz360.biz.model.trading.site.dto.ProvinceSiteNumDto;
import com.cdz360.biz.model.trading.site.param.ListBiSiteOrderParam;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.param.SiteIncomeExpenseParam;
import com.cdz360.biz.model.trading.site.param.SiteReportParam;
import com.cdz360.biz.model.trading.site.po.BiSiteSumPo;
import com.cdz360.biz.model.trading.site.po.BiSiteSummaryPo;
import com.cdz360.biz.model.trading.site.po.SiteDailyBiPo;
import com.cdz360.biz.model.trading.site.vo.BiSiteGcMonthExpenseStatisticVo;
import com.cdz360.biz.model.trading.site.vo.BiSiteGcMonthIncomeVo;
import com.cdz360.biz.model.trading.site.vo.BiSiteOrderVo;
import com.cdz360.biz.model.trading.site.vo.SiteBaseInfoVo;
import com.cdz360.biz.model.trading.site.vo.SiteOrderAccountBi;
import com.cdz360.biz.model.trading.site.vo.SiteOrderBiVo;
import com.cdz360.biz.model.trading.site.vo.SiteOrderHlhtAccountBi;
import com.cdz360.biz.oa.dto.BatchTaskVo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper;
import com.chargerlinkcar.framework.common.utils.UUIDUtils;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.util.Date;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/bi/site")
@Tag(name = "场站相关的统计接口", description = "场站统计")
public class SiteBiRest {

    @Autowired
    private SiteOrderBiService siteOrderBiService;

    @Autowired
    private SiteBizService siteBizService;

    @Autowired
    private ExcelBiService excelBiService;

    @Autowired
    private SiteDailyBiRoDs siteDailyBiRoDs;

    @Autowired
    private SiteIncomeExpenseService siteIncomeExpenseService;

    @Operation(summary = "通过指定时间计算最近六个月的收支汇总信息",
        description = "用于OA电费支付历史台账")
    @GetMapping("/sixMonthIncomeExpense")
    public Mono<ListResponse<SiteIncomeExpenseVo>> siteSixMonthIncomeExpense(
        @ApiParam("场站ID") @RequestParam("siteId") String siteId,
        @ApiParam("指定日期(年月日)") @RequestParam("month")
        @DateTimeFormat(pattern = "yyyy-MM-dd") LocalDate month) {
        log.info("通过指定时间计算最近六个月的收支汇总信息: siteId = {}, month = {}",
            siteId, month);
        return siteIncomeExpenseService.siteSixMonthIncomeExpense(siteId, month)
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "获取有效上期电费支付台账", description = "用于OA电费支付批量审批")
    @PostMapping("/getOaElecPayTaskListBySiteId")
    public Mono<ListResponse<OaElecPayIncomeExpenseVo>> getOaElecPayTaskListBySiteId(
        @RequestBody List<BatchTaskVo> voList) {
        log.info("获取有效上期电费支付台账: voList: {}", JsonUtils.toJsonTimeString(voList));
        return siteIncomeExpenseService.getOaElecPayTaskListBySiteId(voList)
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "获取场站收支按月汇总信息", description = "用于OA电费支付历史台账")
    @PostMapping("/incomeExpenseSumByMonth")
    public Mono<ListResponse<SiteIncomeExpenseVo>> siteIncomeExpenseSumByMonth(
        @RequestBody SiteIncomeExpenseParam param) {
        log.info("获取场站收支按月汇总信息: param = {}", param);
        return siteIncomeExpenseService.siteIncomeExpenseSumByMonth(param)
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "按场站分组统计一段时间内的电量,收入,订单数")
    @PostMapping("/getSiteBiList")
    public ListResponse<SiteOrderBiVo> getSiteBiList(@RequestBody SiteBiParam param) {
        log.info("param = {}", param);
        var bi = this.siteOrderBiService.getSiteBiList(param);
        return RestUtils.buildListResponse(bi);
    }

    @Operation(summary = "按照场站获取一定时间段内电量，费用的统计，充电管理平台报表使用")
    @PostMapping("/getSummaryListBySite")
    public ObjectResponse<BiSiteSummaryPo> getSummaryListBySite(@RequestBody SiteBiParam param) {
        log.info("param = {}", param);
        BiSiteSummaryPo biSummary = new BiSiteSummaryPo();
        List<BiSiteSumPo> result = this.siteOrderBiService.getSummaryListBySite(param);
        BigDecimal totalQuantity = result.stream().map(BiSiteSumPo::getAmount)
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        biSummary.setTotal(totalQuantity);
        biSummary.setBiSummary(result);
        return RestUtils.buildObjectResponse(biSummary);
    }

    @Operation(summary = "汇总场站充电统计")
    @PostMapping("/getBiSiteList")
    public ListResponse<BiSiteSummaryPo> getBiSiteList(@RequestBody SiteBiParam param) {
        log.info("param = {}", param);
        return this.siteOrderBiService.getBiSiteList(param);
    }

    @Operation(summary = "按照当前结果导出查询数据--按照场站汇总")
    @PostMapping("/exportBiSiteList")
    public ObjectResponse<ExcelBiPosition> exportBiSiteList(@RequestBody SiteBiParam param) {
        log.info("param = {}", param);
        throw new DcServiceException("接口已废弃，请联系开发");
//        ExcelBiPosition position = new ExcelBiPosition();
//        position.setSubFileName(UUIDUtils.getUuid32());
//        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
//        log.info("writeTempExcelByChargeOrderList 启动,position:{}", JsonUtils.toJsonString(position));
//        param.setExcelPosition(position);
//        param.setSheetName("场站汇总");
//        excelBiService.writeTempExcelBySiteList(param);
//        return new ObjectResponse<>(position);
    }

    @Operation(summary = "场站充电走势(电量)--尖峰平谷")
    @PostMapping("/exportBiElectBySiteList")
    public ObjectResponse<ExcelBiPosition> exportBiElectBySiteList(@RequestBody SiteBiParam param) {
        log.info("param = {}", param);
        throw new DcServiceException("接口已废弃，请联系开发");
//        ExcelBiPosition position = new ExcelBiPosition();
//        position.setSubFileName(UUIDUtils.getUuid32());
//        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
//        log.info("writeTempExcelByChargeOrderList 启动,position:{}", JsonUtils.toJsonString(position));
//        param.setExcelPosition(position);
//        param.setSheetName("场站充电走势电量汇总");
//        excelBiService.writeTempElectBySiteOrCommList(param, 0);
//        return new ObjectResponse<>(position);
    }

    @Operation(summary = "场站充电走势(消费)--总金额,电费,服务费")
    @PostMapping("/exportBiFeeBySiteList")
    public ObjectResponse<ExcelBiPosition> exportBiFeeBySiteList(@RequestBody SiteBiParam param) {
        log.info("param = {}", param);
        throw new DcServiceException("接口已废弃，请联系开发");
//        ExcelBiPosition position = new ExcelBiPosition();
//        position.setSubFileName(UUIDUtils.getUuid32());
//        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
//        log.info("writeTempExcelByChargeOrderList 启动,position:{}", JsonUtils.toJsonString(position));
//        param.setExcelPosition(position);
//        param.setSheetName("场站充电走势消费汇总");
//        excelBiService.writeTempFeeBySiteOrCommList(param, 0);
//        return new ObjectResponse<>(position);
    }

    @Operation(summary = "按省统计场站数量")
    @PostMapping("/getProvinceSiteNumList")
    public ListResponse<ProvinceSiteNumDto> getProvinceSiteNumList(HttpServletRequest request,
        @RequestBody ListSiteParam param
//                                                                   @RequestParam(value = "includedHlht", required = false) Boolean includedHlht,
//                                                                   @RequestParam(value = "commIdChain", required = false) String commIdChain
    ) {
        log.debug(LoggerHelper.formatEnterLog(request));
        var list = this.siteBizService.getProvinceSiteNumList(param);
        return RestUtils.buildListResponse(list);
    }

    @Operation(summary = "按城市统计场站数量")
    @PostMapping("/getCitySiteNumList")
    public ListResponse<CitySiteNumDto> getCitySiteNumList(HttpServletRequest request,
        @RequestBody ListSiteParam param
//                                                           @RequestParam(value = "includedHlht", required = false) Boolean includedHlht,
//                                                           @RequestParam(value = "commIdChain", required = false) String commIdChain
    ) {
        log.debug(LoggerHelper.formatEnterLog(request));
        var list = this.siteBizService.getCitySiteNumList(param);
        return RestUtils.buildListResponse(list);
    }

    @Operation(summary = "按区/县统计场站数量")
    @PostMapping("/getDistrictSiteNumList")
    public ListResponse<DistrictSiteNumDto> getDistrictSiteNumList(HttpServletRequest request,
        @RequestBody ListSiteParam param) {
        log.debug(LoggerHelper.formatEnterLog(request));
        var list = this.siteBizService.getDistrictSiteNumList(param);
        return RestUtils.buildListResponse(list);
    }


    @Operation(summary = "统计商户下场站数量，桩/枪数量")
    @PostMapping("/getCommSiteEvseCount")
    public ObjectResponse<CommSiteEvseCount> getCommSiteEvseCount(HttpServletRequest request,
        @RequestBody ListSiteParam param
//                                                                  @RequestParam(value = "includedHlht", required = false) Boolean includedHlht,
//                                                                  @RequestParam(value = "commIdChain", required = false) String commIdChain
    ) {
        log.debug(LoggerHelper.formatEnterLog(request));
        var data = this.siteBizService.getCommSiteEvseCount(param);
        log.debug(LoggerHelper.formatLeaveLog(request) + "data = {}", data);
        return RestUtils.buildObjectResponse(data);
    }


    @Operation(summary = "统计商户下场站的功率曲线")
    @GetMapping("/getCommDailyKwLine")
    public ListResponse<Integer> getCommDailyKwLine(
        @RequestParam(value = "commIdChain") String commIdChain,
        @RequestParam(value = "date") String strDate) {
        log.info("commIdChain = {}, date = {}", commIdChain, strDate);
        List<Integer> kwLine = this.siteDailyBiRoDs.getCommDailyKwLine(commIdChain,
            DateUtils.fromYyyyMmDd(strDate));
        return RestUtils.buildListResponse(kwLine);
    }

    @Operation(summary = "获取场站日功率曲线")
    @GetMapping("/getSiteDailyPowerLine")
    public ObjectResponse<SiteDailyBiPo> getSiteDailyPowerLine(
        @RequestParam(name = "siteId") String siteId,
        @RequestParam(name = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        SiteDailyBiPo bi = siteDailyBiRoDs.getSiteDailyBi(siteId, date);
        return RestUtils.buildObjectResponse(bi);
    }

    @Operation(summary = "批量获取场站日功率曲线")
    @GetMapping("/getSiteDailyPowerLineList")
    public ObjectResponse<List<SiteDailyBiPo>> getSiteDailyPowerLineList(
        @RequestParam(name = "siteIds") List<String> siteIds,
        @RequestParam(name = "date") @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        List<SiteDailyBiPo> bi = siteDailyBiRoDs.getSiteDailyBiList(siteIds, date);
        return RestUtils.buildObjectResponse(bi);
    }

    @Operation(summary = "获取场站日功率曲线")
    @GetMapping("/getSiteDailyPowerRangeDate")
    public ListResponse<SiteDailyBiPo> getSiteDailyPowerRangeDate(
        @RequestParam(name = "siteId") String siteId,
        @RequestParam(name = "fromDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date fromDate,
        @RequestParam(name = "toDate") @DateTimeFormat(pattern = "yyyy-MM-dd") Date toDate) {
        List<SiteDailyBiPo> biList = siteDailyBiRoDs.getSiteDailyBiRangeDate(siteId, fromDate,
            toDate);
        return RestUtils.buildListResponse(biList);
    }

    @Schema(description = "场站消费统计")
    @PostMapping(value = "/biSiteOrderList")
    public Mono<ListResponse<BiSiteOrderVo>> findBiSiteOrderList(
        @RequestBody ListBiSiteOrderParam param) {
        log.debug("获取场站消费统计: param = {}", JsonUtils.toJsonString(param));
        return Mono.just(param)
            .flatMap(siteOrderBiService::findBiSiteOrderList);
    }

    @Schema(description = "客群趋势数据获取")
    @PostMapping(value = "/biSiteOrderAccount")
    public Mono<ObjectResponse<SiteOrderAccountBi>> getBiSiteOrderAccount(
        @RequestBody SiteBiParam param) {
        log.debug("客群趋势数据获取: param = {}", JsonUtils.toJsonString(param));
        return Mono.just(param)
            .flatMap(siteOrderBiService::getBiSiteOrderAccount)
            .map(RestUtils::buildObjectResponse);
    }

    @Schema(description = "互联企客趋势数据获取")
    @PostMapping(value = "/biSiteOrderHlhtAccount")
    public Mono<ObjectResponse<SiteOrderHlhtAccountBi>> getBiSiteOrderHlhtAccount(
        @RequestBody SiteBiParam param) {
        log.debug("互联企客趋势数据获取: param = {}", JsonUtils.toJsonString(param));
        return Mono.just(param)
            .flatMap(siteOrderBiService::getBiSiteOrderHlhtAccount)
            .map(RestUtils::buildObjectResponse);
    }

    /**
     * 导出脱机桩列表
     *
     * @param param
     * @return
     */
    @PostMapping("/exportOfflineEvse")
    public ObjectResponse<ExcelPosition> exportOfflineEvse(@RequestBody ListEvseParam param) {
        ExcelPosition position = new ExcelPosition();
        position.setSubFileName(UUIDUtils.getUuid32());
        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
        log.info("exportOfflineEvse start position:{}", JsonUtils.toJsonString(position));
        siteOrderBiService.exportOfflineEvse(param, position);
        return RestUtils.buildObjectResponse(position);
    }

    @GetMapping("/getSiteMonthSurvey")
    public Mono<ListResponse<BiSiteGcMonthIncomeVo>> getSiteMonthSurvey(
        @RequestBody SiteReportParam param) {
        return siteIncomeExpenseService.siteIncomeSumByMonth(param);
    }

    @GetMapping("/getSiteExpenseMonthly")
    public Mono<ListResponse<BiSiteGcMonthExpenseStatisticVo>> getSiteExpenseMonthly(
        @RequestBody SiteReportParam param) {
        return siteIncomeExpenseService.getSiteExpenseMonthly(param);
    }

    @GetMapping("/getSiteBaseInfo")
    public Mono<ObjectResponse<SiteBaseInfoVo>> getSiteBaseInfo(
        @RequestBody SiteReportParam param) {
        return siteIncomeExpenseService.getSiteBaseInfo(param);
    }

}
