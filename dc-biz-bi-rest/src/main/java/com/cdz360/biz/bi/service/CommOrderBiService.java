package com.cdz360.biz.bi.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.ds.trading.ro.site.ds.BiSiteOrderRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.CommRoDs;
import com.cdz360.biz.model.bi.dashboard.ChargeOrderCommBiVo;
import com.cdz360.biz.model.common.constant.Constant;
import com.cdz360.biz.model.iot.param.SiteBiParam;
import com.cdz360.biz.model.iot.type.ReportBiType;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import com.cdz360.biz.model.trading.corp.vo.CorpVo;
import com.cdz360.biz.model.trading.order.type.BiDependOnType;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.po.BiSiteElectPo;
import com.cdz360.biz.model.trading.site.po.BiSiteFeePo;
import com.cdz360.biz.model.trading.site.po.BiSiteSumPo;
import com.cdz360.biz.model.trading.site.po.BiSiteSummaryElectPo;
import com.cdz360.biz.model.trading.site.po.BiSiteSummaryPo;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CommOrderBiService {

    // 返回前端对打采样点
//    private static final int MAX_SAMPLE_SIZE = Constant.MAX_SAMPLE_SIZE;

    @Autowired
    private BiSiteOrderRoDs biSiteOrderRoDs;
    @Autowired
    private CommRoDs commRoDs;


    /**
     * 调整数据返回，固定长度
     *
     * @return
     */
    private List<LocalDateTime> reviseResult(SiteBiParam param) {
        param.resetTime();

        LocalDateTime start = param.getFromTime().toInstant()
                .atOffset(ZoneOffset.of("+8"))
                .toLocalDateTime();
        LocalDateTime end = param.getToTime().toInstant()
                .atOffset(ZoneOffset.of("+8"))
                .toLocalDateTime();

        List<LocalDateTime> timeList = new ArrayList<>();
        while (start.isBefore(end)) {
            timeList.add(start);
            if (param.getSampleType() == SiteBiSampleType.HOUR) {
                start = start.plusHours(1);
            } else if (param.getSampleType() == SiteBiSampleType.DAY) {
                start = start.plusDays(1);
            } else {
                start = start.plusMonths(1);
            }
        }

        log.info("time size = {}", timeList.size());
        return timeList;
    }

    /**
     * 调整时间点
     *
     * @param sampleType
     * @param time
     * @return
     */
    private Date resetDate(SiteBiSampleType sampleType, Date time) {
        // 分秒都为0
        LocalDateTime local = time.toInstant().atOffset(ZoneOffset.of("+8"))
                .toLocalDateTime().withMinute(0).withSecond(0).withNano(0);

        if (sampleType == SiteBiSampleType.DAY ||
                sampleType == SiteBiSampleType.MONTH) {
            local = local.withHour(0);
        }

        if (sampleType == SiteBiSampleType.MONTH) {
            // 当月第一天
            local = local.with(TemporalAdjusters.firstDayOfMonth());
        }

        return new Date(local.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
    }


    private Optional<String> validBiParam(SiteBiParam param) {

        if (null == param.getBiContent()) {
            return Optional.of("统计类型不能为空");
        }

        if (null == param.getStartTime() || null == param.getEndTime()) {
            return Optional.of("开始结束时间没有提供");
        }

        if (param.getStartTime() >= param.getEndTime()) {
            return Optional.of("开始时间大于等于结束时间，不合理");
        }

        if (reviseResult(param).size() > Constant.MAX_SAMPLE_SIZE) {
            return Optional.of("时间跨度大于最大值（" + Constant.MAX_SAMPLE_SIZE + "），请选择正确的开始/结束时间");
        }

        return Optional.empty();
    }

    /**
     * 按照企业汇总充电情况
     *
     * @param param
     * @return
     */
    public ListResponse<BiSiteSummaryPo> getBiCorpList(SiteBiParam param) {
        // 校验参数
        Optional<String> validResult = this.validBiParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }
        // 调整前端入参
        param.resetTime();
        //获取可用企业总数
        ListSiteParam corpParam = new ListSiteParam();
        corpParam.setCommIdChain(param.getCommIdChain())
                .setCorpIdList(param.getCorpIdList())
                .setStart(param.getStart())
                .setSize(param.getSize());
        Long siteCount = commRoDs.countCorp(corpParam);


        //根据查询汇总数据获取企业列表

        List<CorpVo> corpList = biSiteOrderRoDs.getCorpListByCondition(
                param.getCorpIdList(),
                BiDependOnType.valueOf(param.getDependOnTimeType().name()).toString(),
                ReportBiType.valueOf(param.getBiContent().name()).toString(),
                SiteBiSampleType.valueOf(param.getSampleType().name()).toString(),
                param.getCommIdChain(),
                new Date(param.getStartTime()), new Date(param.getEndTime()),
                param.getStart(), param.getSize(), param.getSort());
        List<Long> corpIdList = null;
        if (corpList != null) {
            corpIdList = corpList.stream().map(e -> e.getId()).collect(Collectors.toList());
        }


        List<BiSiteSumPo> biList = biSiteOrderRoDs.getSummaryListByCorp(
                corpIdList,
                BiDependOnType.valueOf(param.getDependOnTimeType().name()).toString(),
                ReportBiType.valueOf(param.getBiContent().name()).toString(),
                SiteBiSampleType.valueOf(param.getSampleType().name()).toString(),
                param.getCommIdChain(),
                new Date(param.getStartTime()), new Date(param.getEndTime()),
                param.getStart(), param.getSize());
        Map<String, List<BiSiteSumPo>> mapList = biList.stream().collect(Collectors.groupingBy(BiSiteSumPo::getName));

        List<BiSiteSummaryPo> biSiteList = new ArrayList<>();
        mapList.forEach((key, value) -> {
            List<BiSiteSumPo> biSiteSum = this.divisionResult(value, param);
            BiSiteSummaryPo result = new BiSiteSummaryPo();
            result.setBiSummary(biSiteSum);
            result.setName(key);
            result.setTotal(biSiteSum.stream().map(BiSiteSumPo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            biSiteList.add(result);
            ;
        });

        List<BiSiteSummaryPo> biSiteLists = new ArrayList<>();

        if (param.getSort().equals("ASC")) {
            biSiteLists = biSiteList.stream().sorted(Comparator.comparing(BiSiteSummaryPo::getTotal)).collect(Collectors.toList());
        } else {
            biSiteLists = biSiteList.stream().sorted(Comparator.comparing(BiSiteSummaryPo::getTotal).reversed()).collect(Collectors.toList());
        }

        BiSiteSummaryPo biSummary = new BiSiteSummaryPo();
        List<BiSiteSumPo> result = getSummaryByCorpCondition(param);
        BigDecimal totalQuantity = result.stream().map(BiSiteSumPo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        biSummary.setTotal(totalQuantity);
        biSummary.setBiSummary(result);
        biSummary.setName("accountSummary");
        biSiteLists.add(biSummary);
        return new ListResponse<>(biSiteLists, siteCount);
    }

    /**
     * 商户汇总
     *
     * @param param
     * @return
     */
    public List<BiSiteSumPo> getSummaryByCondition(SiteBiParam param) {
        // 校验参数
        Optional<String> validResult = this.validBiParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        // 调整前端入参
        param.resetTime();
        // 库中采样数据
        List<BiSiteSumPo> biList = biSiteOrderRoDs.getSummaryByCondition(
                param.getSiteIdList(), //站点列表
                param.getCorpIdList(), //商户列表
                BiDependOnType.valueOf(param.getDependOnTimeType().name()), //统计时间类型
                param.getBiContent().getDetail(),
                SiteBiSampleType.valueOf(param.getSampleType().name()).toString(),
                param.getCommIdChain(),
                param.getGids(),
                new Date(param.getStartTime()),
                new Date(param.getEndTime()),
                param.getIncludedHlhtSite());

        return this.divisionResult(biList, param);
    }

    /**
     * 按照查询条件进行企业汇总
     *
     * @param param
     * @return
     */
    public List<BiSiteSumPo> getSummaryByCorpCondition(SiteBiParam param) {
        // 校验参数
        Optional<String> validResult = this.validBiParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        // 调整前端入参
        param.resetTime();
        // 库中采样数据
        List<BiSiteSumPo> biList = biSiteOrderRoDs.getSummaryByCorpCondition(
                param.getCorpIdList(), //企业列表
                BiDependOnType.valueOf(param.getDependOnTimeType().name()).toString(),
                ReportBiType.valueOf(param.getBiContent().name()).toString(),
                SiteBiSampleType.valueOf(param.getSampleType().name()).toString(),
                param.getCommIdChain(),
                new Date(param.getStartTime()), new Date(param.getEndTime()));

        return this.divisionResult(biList, param);
    }

    /**
     * 按照场站获取汇总详细信息   尖峰平谷   消费
     *
     * @param param
     * @return
     */
    public List<BiSiteElectPo> getSummaryListByCondition(SiteBiParam param) {
        // 校验参数
        Optional<String> validResult = this.validBiParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        // 调整前端入参
        param.resetTime();
        // 库中采样数据
        List<BiSiteElectPo> biList = biSiteOrderRoDs.getSummaryListByCondition(
                param.getSiteIdList(),
                param.getCorpIdList(),
                BiDependOnType.valueOf(param.getDependOnTimeType().name()),
                param.getBiContent().getDetail(),
                SiteBiSampleType.valueOf(param.getSampleType().name()).toString(),
                param.getCommIdChain(),
                new Date(param.getStartTime()), new Date(param.getEndTime()));

        return this.divisionResult2(biList, param);
    }

    /**
     * 按照企业获取汇总详细信息   尖峰平谷  消费
     *
     * @param param
     * @return
     */
    public List<BiSiteElectPo> getSummaryListByCorpCondition(SiteBiParam param) {
        // 校验参数
        Optional<String> validResult = this.validBiParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }
        // 调整前端入参
        param.resetTime();
        // 获取尖峰平谷数据
        List<BiSiteElectPo> biList = biSiteOrderRoDs.getSummaryListByCorpCondition(
                param.getCorpIdList(),
                BiDependOnType.valueOf(param.getDependOnTimeType().name()).toString(),
                SiteBiSampleType.valueOf(param.getSampleType().name()).toString(),
                param.getCommIdChain(),
                new Date(param.getStartTime()), new Date(param.getEndTime()));

        //获取汇总信息
        List<BiSiteElectPo> orderInfo = biSiteOrderRoDs.getSummaryInfoByCorpCondition(
                param.getCorpIdList(),
                BiDependOnType.valueOf(param.getDependOnTimeType().name()).toString(),
                SiteBiSampleType.valueOf(param.getSampleType().name()).toString(),
                param.getCommIdChain(),
                new Date(param.getStartTime()), new Date(param.getEndTime()));

        List<BiSiteElectPo> siteElectList = this.divisionResult2(biList, param);

        if (siteElectList == null) {
            throw new DcArgumentException("数据信息不存在");
        }

        siteElectList.forEach(e -> {
            if (param.getSampleType().equals(SiteBiSampleType.DAY)) {
                Optional<BiSiteElectPo> orderOptional = orderInfo.stream().filter(item -> item.getDay().equals(e.getDay())).findFirst();
                if (orderOptional.isPresent()) {
                    e.setElectFee(orderOptional.get().getElectFee());
                    e.setElectricity(orderOptional.get().getElectricity());
                    e.setFee(orderOptional.get().getFee());
                    e.setServeFee(orderOptional.get().getServeFee());
                }
            } else if (param.getSampleType().equals(SiteBiSampleType.MONTH)) {
                LocalDateTime time = e.getMonth().toInstant().atOffset(ZoneOffset.of("+8"))
                        .toLocalDateTime();

                List<BiSiteElectPo> dataList = orderInfo.stream().filter(d -> {
                    LocalDateTime localDateTime = d.getMonth().toInstant().atOffset(ZoneOffset.of("+8"))
                            .toLocalDateTime();
                    return localDateTime.getYear() == time.getYear() &&
                            localDateTime.getMonthValue() == time.getMonthValue();
                }).collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(dataList)) {
                    e.setElectFee(dataList.stream().map(BiSiteElectPo::getElectFee).reduce(BigDecimal.ZERO, BigDecimal::add));
                    e.setElectricity(dataList.stream().map(BiSiteElectPo::getElectricity).reduce(BigDecimal.ZERO, BigDecimal::add));
                    e.setFee(dataList.stream().map(BiSiteElectPo::getFee).reduce(BigDecimal.ZERO, BigDecimal::add));
                    e.setServeFee(dataList.stream().map(BiSiteElectPo::getServeFee).reduce(BigDecimal.ZERO, BigDecimal::add));
                }
            }
        });

        return siteElectList;
}

    private List<BiSiteSumPo> divisionResult(List<BiSiteSumPo> list, SiteBiParam param) {
        List<LocalDateTime> timeList = reviseResult(param);
        if (CollectionUtils.isEmpty(timeList)) {
            throw new DcArgumentException("开始/结束时间不正确");
        }

//        if (timeList.size() < Constant.MAX_SAMPLE_SIZE) {
//            return list;
//        }
        List<BiSiteSumPo> result = new ArrayList<>();
        if (param.getSampleType().toString().equals("DAY")) {
            timeList.stream().limit(Constant.MAX_SAMPLE_SIZE).forEach(time -> {
                Optional<BiSiteSumPo> first = list.stream().filter(d -> d.getDay() != null)
                        .filter(d -> d.getDay().toInstant().atOffset(ZoneOffset.of("+8"))
                                .toLocalDateTime().isEqual(time)).findFirst();
                if (first.isPresent()) {
                    result.add(first.get());
                } else {
                    BiSiteSumPo division = new BiSiteSumPo()
                            .setDay(new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
                    result.add(division);
                }
            });
        } else {
            timeList.stream().limit(Constant.MAX_SAMPLE_SIZE).forEach(time -> {
                List<BiSiteSumPo> dataList = list.stream().filter(d -> d.getMonth() != null)
                        .filter(d -> {
                            LocalDateTime localDateTime = d.getMonth().toInstant().atOffset(ZoneOffset.of("+8"))
                                    .toLocalDateTime();
                            return localDateTime.getYear() == time.getYear() &&
                                    localDateTime.getMonthValue() == time.getMonthValue();
                        })
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(dataList)) {
                    BiSiteSumPo data = dataList.get(0);
                    data.setMonth(new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
                    data.setAmount(dataList.stream().map(BiSiteSumPo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                    result.add(data);
                } else {
                    BiSiteSumPo division = new BiSiteSumPo()
                            .setMonth(new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
                    result.add(division);
                }
            });
        }
        return result;
    }

    public ChargeOrderCommBiVo getLastBi(Integer days, String idChain) {

        Date endTime = DateUtil.getThisDate(new Date());

        int daysRecently = -30;
        if (days != null) {
            daysRecently = days.intValue();
        }

        Date startTime = DateUtil.addDate(endTime, daysRecently);

        return biSiteOrderRoDs.getLastBi(idChain, startTime, endTime);
    }

    /**
     * 按照企业导出电量汇总  (尖峰平谷)
     *
     * @param param
     * @return
     */
    public List<BiSiteSummaryElectPo> getBiElectByCorpList(SiteBiParam param) {
        // 校验参数
        Optional<String> validResult = this.validBiParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        // 调整前端入参
        param.resetTime();

        List<BiSiteElectPo> biList = biSiteOrderRoDs.getElectListByCorp(
                param.getCorpIdList(), //商户列表
                BiDependOnType.valueOf(param.getDependOnTimeType().name()).toString(), //统计时间类型
                param.getBiContent().getDetail(),
                SiteBiSampleType.valueOf(param.getSampleType().name()).toString(),
                param.getCommIdChain(),
                new Date(param.getStartTime()), new Date(param.getEndTime()),
                param.getStart(), param.getSize());


        Map<String, List<BiSiteElectPo>> map = biList.stream().collect(Collectors.groupingBy(BiSiteElectPo::getName));

        List<BiSiteSummaryElectPo> biSiteList = new ArrayList<>();

        map.forEach((k, v) -> {
            BiSiteSummaryElectPo biSite = new BiSiteSummaryElectPo();
            List<BiSiteElectPo> biSiteSum = this.divisionResult2(v, param);
            biSite.setName(k);
            biSite.setCorpId(v.get(0).getCorpId());
            biSite.setBiSummary(biSiteSum);
            biSiteList.add(biSite);
        });
        biSiteList.forEach(e -> {
            //根据corpId获取按照企业汇总的信息
            List<Long> corpIdList = new ArrayList<>();
            corpIdList.add(e.getCorpId());
            List<BiSiteElectPo> corpInfo = biSiteOrderRoDs.getSummaryInfoByCorpCondition(
                    corpIdList,
                    BiDependOnType.valueOf(param.getDependOnTimeType().name()).toString(),
                    SiteBiSampleType.valueOf(param.getSampleType().name()).toString(),
                    param.getCommIdChain(),
                    new Date(param.getStartTime()), new Date(param.getEndTime()));

            e.getBiSummary().forEach(o -> {
                if (param.getSampleType().equals(SiteBiSampleType.DAY)) {
                    Optional<BiSiteElectPo> orderOptional = corpInfo.stream().filter(item -> item.getDay().equals(o.getDay())).findFirst();
                    if (orderOptional.isPresent()) {
                        o.setElectFee(orderOptional.get().getElectFee());
                        o.setElectricity(orderOptional.get().getElectricity());
                        o.setFee(orderOptional.get().getFee());
                        o.setServeFee(orderOptional.get().getServeFee());
                    }
                } else if (param.getSampleType().equals(SiteBiSampleType.MONTH)) {
                    LocalDateTime time = o.getMonth().toInstant().atOffset(ZoneOffset.of("+8"))
                            .toLocalDateTime();

                    List<BiSiteElectPo> dataList = corpInfo.stream().filter(d -> {
                        LocalDateTime localDateTime = d.getMonth().toInstant().atOffset(ZoneOffset.of("+8"))
                                .toLocalDateTime();
                        return localDateTime.getYear() == time.getYear() &&
                                localDateTime.getMonthValue() == time.getMonthValue();
                    }).collect(Collectors.toList());

                    if (CollectionUtils.isNotEmpty(dataList)) {
                        o.setElectFee(dataList.stream().map(BiSiteElectPo::getElectFee).reduce(BigDecimal.ZERO, BigDecimal::add));
                        o.setElectricity(dataList.stream().map(BiSiteElectPo::getElectricity).reduce(BigDecimal.ZERO, BigDecimal::add));
                        o.setFee(dataList.stream().map(BiSiteElectPo::getFee).reduce(BigDecimal.ZERO, BigDecimal::add));
                        o.setServeFee(dataList.stream().map(BiSiteElectPo::getServeFee).reduce(BigDecimal.ZERO, BigDecimal::add));
                    }
                }
            });
        });

        return biSiteList;
    }

    private List<BiSiteElectPo> divisionResult2(List<BiSiteElectPo> list, SiteBiParam param) {
        List<LocalDateTime> timeList = reviseResult(param);
        if (CollectionUtils.isEmpty(timeList)) {
            throw new DcArgumentException("开始/结束时间不正确");
        }

//        if (timeList.size() < Constant.MAX_SAMPLE_SIZE) {
//            return list;
//        }
        List<BiSiteElectPo> result = new ArrayList<>();
        if (param.getSampleType().toString().equals("DAY")) {
            timeList.stream().limit(Constant.MAX_SAMPLE_SIZE).forEach(time -> {
                Optional<BiSiteElectPo> first = list.stream().filter(d -> d.getDay() != null)
                        .filter(d -> d.getDay().toInstant().atOffset(ZoneOffset.of("+8"))
                                .toLocalDateTime().isEqual(time)).findFirst();
                if (first.isPresent()) {
                    result.add(first.get());
                } else {
                    BiSiteElectPo division = new BiSiteElectPo()
                            .setDay(new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
                    result.add(division);
                }
            });
        } else {
            timeList.stream().limit(Constant.MAX_SAMPLE_SIZE).forEach(time -> {
                List<BiSiteElectPo> dataList = list.stream().filter(d -> d.getMonth() != null)
                        .filter(d -> {
                            LocalDateTime localDateTime = d.getMonth().toInstant().atOffset(ZoneOffset.of("+8"))
                                    .toLocalDateTime();
                            return localDateTime.getYear() == time.getYear() &&
                                    localDateTime.getMonthValue() == time.getMonthValue();
                        })
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(dataList)) {
                    BiSiteElectPo data = dataList.get(0);
                    data.setMonth(new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
                    data.setElectricity(dataList.stream().map(BiSiteElectPo::getElectricity).reduce(BigDecimal.ZERO, BigDecimal::add));
                    data.setElecTag1(dataList.stream().map(BiSiteElectPo::getElecTag1).reduce(BigDecimal.ZERO, BigDecimal::add));
                    data.setElecTag2(dataList.stream().map(BiSiteElectPo::getElecTag2).reduce(BigDecimal.ZERO, BigDecimal::add));
                    data.setElecTag3(dataList.stream().map(BiSiteElectPo::getElecTag3).reduce(BigDecimal.ZERO, BigDecimal::add));
                    data.setElecTag4(dataList.stream().map(BiSiteElectPo::getElecTag4).reduce(BigDecimal.ZERO, BigDecimal::add));
                    data.setElectricity(dataList.stream().map(BiSiteElectPo::getElectricity).reduce(BigDecimal.ZERO, BigDecimal::add));

                    data.setElectFee(dataList.stream().map(BiSiteElectPo::getElectFee).reduce(BigDecimal.ZERO, BigDecimal::add));
                    data.setServeFee(dataList.stream().map(BiSiteElectPo::getServeFee).reduce(BigDecimal.ZERO, BigDecimal::add));
                    data.setFee(dataList.stream().map(BiSiteElectPo::getFee).reduce(BigDecimal.ZERO, BigDecimal::add));
                    result.add(data);
                } else {
                    BiSiteElectPo division = new BiSiteElectPo()
                            .setMonth(new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
                    result.add(division);
                }
            });
        }
        return result;
    }

//    public List<BiSiteSummaryElectPo> getBiFeeByCommList(SiteBiParam param) {
//        // 校验参数
//        Optional<String> validResult = this.validBiParam(param);
//        if (validResult.isPresent()) {
//            log.info("查询参数无效: {}", validResult.get());
//            throw new DcArgumentException(validResult.get());
//        }
//
//        // 调整前端入参
//        param.resetTime();
//        //获取可用站点列表
//        ListSiteParam commParam = new ListSiteParam();
//        commParam.setCommIdChain(param.getCommIdChain())
//                .setCommIdList(param.getCommIdList())
//                .setStart(param.getStart())
//                .setSize(param.getSize() == null ? 1000:param.getSize());
//
//        List<CommPo> commList = commRoDs.getCommList(commParam);
//
//        List<BiSiteSummaryFeePo> biSiteList = new ArrayList<>();
//        if (commList != null) {
//            commList.forEach(comm->{
//                BiSiteSummaryFeePo biSite = new BiSiteSummaryFeePo();
//                // 库中采样数据
//                List<Long> commIdList = new ArrayList<>();
//                commIdList.add(comm.getId());
//                List<BiSiteFeePo> biList = biSiteOrderRoDs.getFeeListBySite(
//                        null, //站点列表
//                        commIdList, //商户列表
//                        BiDependOnType.valueOf(param.getDependOnTimeType().name()), //统计时间类型
//                        param.getBiContent().getDetail(),
//                        SiteBiSampleType.valueOf(param.getSampleType().name()).toString(),
//                        param.getCommIdChain(),
//                        new Date(param.getStartTime()), new Date(param.getEndTime()));
//
////                月份处理
//                if (param.getSampleType().toString().equals(SiteBiSampleType.MONTH)) {
//                    Map<Date, List<BiSiteFeePo>> collect = biList.stream()
//                            .map(bi -> bi.setMonth(this.resetDate(param.getSampleType(), bi.getMonth())))
//                            .collect(Collectors.groupingBy(BiSiteFeePo::getMonth));
//                    List<BiSiteFeePo> result = new ArrayList<>();
//                    collect.forEach((k, v) -> {
//                        BiSiteFeePo d = new BiSiteFeePo();
//                        d.setMonth(k);
//                        result.add(d);
//                    });
//                    List<BiSiteFeePo> biSiteSum = this.divisionResult3(result,param);
//                    biSite.setName(comm.getName());
//                    biSite.setBiSummary(biSiteSum);
//                    biSiteList.add(biSite);
//                } else {
//
//                    List<BiSiteFeePo> biSiteSum = this.divisionResult3(biList,param);
//                    biSite.setName(comm.getName());
//                    biSite.setBiSummary(biSiteSum);
//                    biSiteList.add(biSite);
//                }
//            });
//
//            return biSiteList;
//        }
//        return null;
//    }

    private List<BiSiteFeePo> divisionResult3(List<BiSiteFeePo> list, SiteBiParam param) {
        List<LocalDateTime> timeList = reviseResult(param);
        if (CollectionUtils.isEmpty(timeList)) {
            throw new DcArgumentException("开始/结束时间不正确");
        }

//        if (timeList.size() < Constant.MAX_SAMPLE_SIZE) {
//            return list;
//        }
        List<BiSiteFeePo> result = new ArrayList<>();
        if (param.getSampleType().toString().equals("DAY")) {
            timeList.stream().limit(Constant.MAX_SAMPLE_SIZE).forEach(time -> {
                Optional<BiSiteFeePo> first = list.stream().filter(d -> d.getDay().toInstant().atOffset(ZoneOffset.of("+8"))
                        .toLocalDateTime().isEqual(time)).findFirst();
                if (first.isPresent()) {
                    result.add(first.get());
                } else {
                    BiSiteFeePo division = new BiSiteFeePo()
                            .setDay(new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
                    result.add(division);
                }
            });
        } else {
            timeList.stream().limit(Constant.MAX_SAMPLE_SIZE).forEach(time -> {
                List<BiSiteFeePo> dataList = list.stream()
                        .filter(d -> {
                            LocalDateTime localDateTime = d.getMonth().toInstant().atOffset(ZoneOffset.of("+8"))
                                    .toLocalDateTime();
                            return localDateTime.getYear() == time.getYear() &&
                                    localDateTime.getMonthValue() == time.getMonthValue();
                        })
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(dataList)) {
                    BiSiteFeePo data = dataList.get(0);
                    data.setMonth(new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
                    data.setFee(dataList.stream().map(BiSiteFeePo::getFee).reduce(BigDecimal.ZERO, BigDecimal::add));
                    data.setElecFee(dataList.stream().map(BiSiteFeePo::getElecFee).reduce(BigDecimal.ZERO, BigDecimal::add));
                    data.setServFee(dataList.stream().map(BiSiteFeePo::getServFee).reduce(BigDecimal.ZERO, BigDecimal::add));
                    result.add(data);
                } else {
                    BiSiteFeePo division = new BiSiteFeePo()
                            .setMonth(new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
                    result.add(division);
                }
            });
        }
        return result;
    }

}
