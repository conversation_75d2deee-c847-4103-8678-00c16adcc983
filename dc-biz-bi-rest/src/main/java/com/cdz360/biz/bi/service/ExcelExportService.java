package com.cdz360.biz.bi.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.param.TimeFilter2;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.bi.config.ExportFileConfig;
import com.cdz360.biz.ds.ess.ro.data.ds.EssEquipTimelyRoDs;
import com.cdz360.biz.ds.trading.ro.coupon.ds.ActivityRoDs;
import com.cdz360.biz.ds.trading.ro.coupon.ds.CouponRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.OrderOvertimeParkDivisionRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.OvertimeParkFeeOrderRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteOvertimeParkDivisionRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteOvertimeParkSettingRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ess.model.data.po.EssEquipTimelyPo;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.ess.param.ListEssDailyParam;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.trading.bi.dto.BiExportGroups;
import com.cdz360.biz.model.trading.bi.vo.MeterDataBiVo;
import com.cdz360.biz.model.trading.bi.vo.SiteMeterDataBiVo;
import com.cdz360.biz.model.trading.bi.vo.UserActivityCouponBiVo;
import com.cdz360.biz.model.trading.coupon.param.CouponSearchParam;
import com.cdz360.biz.model.trading.coupon.po.ActivityPo;
import com.cdz360.biz.model.trading.coupon.vo.CouponVo;
import com.cdz360.biz.model.trading.iot.po.EvsePo;
import com.cdz360.biz.model.trading.meter.param.MeterDataListParam;
import com.cdz360.biz.model.trading.meter.param.MeterListParam;
import com.cdz360.biz.model.trading.meter.po.DeviceMeterPo;
import com.cdz360.biz.model.trading.meter.vo.MeterEvseVo;
import com.cdz360.biz.model.trading.meter.vo.SiteMeterVo;
import com.cdz360.biz.model.trading.site.param.ListOvertimeParkFeeOrderParam;
import com.cdz360.biz.model.trading.site.po.OrderOvertimeParkDivisionPo;
import com.cdz360.biz.model.trading.site.po.SiteOvertimeParkDivisionPo;
import com.cdz360.biz.model.trading.site.po.SiteOvertimeParkSettingPo;
import com.cdz360.biz.model.trading.site.vo.EvseElecVo;
import com.cdz360.biz.model.trading.site.vo.OvertimeParkFeeOrderBi;
import com.cdz360.biz.model.trading.site.vo.OvertimeParkFeeOrderVo;
import com.cdz360.biz.model.trading.site.vo.SiteOvertimeParkDivisionVo;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import com.cdz360.biz.utils.feign.iot.DeviceFeignClient;
import com.cdz360.biz.utils.feign.iot.MeterFeignClient;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.DateUtils;
import com.chargerlinkcar.framework.common.utils.ExcelUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class ExcelExportService {

    @Autowired
    private ExportFileConfig exportFileConfig;

    // excel 存放路径
//    @Value("${excel.dir:/tmp/excel}")
//    private String TEMP_DIR;

    @Autowired
    private OvertimeParkFeeOrderRoDs overtimeParkFeeOrderRoDs;

    @Autowired
    private OrderOvertimeParkDivisionRoDs orderOvertimeParkDivisionRoDs;
    @Autowired
    private SiteOvertimeParkSettingRoDs siteOvertimeParkSettingRoDs;
    @Autowired
    private SiteOvertimeParkDivisionRoDs siteOvertimeParkDivisionRoDs;

    @Autowired
    private CouponRoDs couponRoDs;

    @Autowired
    private ActivityRoDs activityRoDs;

    @Autowired
    private MeterFeignClient meterFeignClient;

    @Autowired
    private EssEquipTimelyRoDs essEquipTimelyRoDs;

    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private ChargerOrderRoDs chargerOrderRoDs;

    @Autowired
    private DeviceFeignClient deviceFeignClient;

//    @Async
    public void exportOvertimeParkFeeOrder(ListOvertimeParkFeeOrderParam param) throws Exception {
        log.info("超停收费订单导出: {}", JsonUtils.toJsonString(param.getExcelPosition()));
//        try {
            CompletableFuture<OvertimeParkFeeOrderBi> orderBi =
                    CompletableFuture.supplyAsync(() -> this.overtimeParkFeeOrderRoDs.orderBi(param));

            ExcelUtil.builder(exportFileConfig.getExcelDir(), param.getExcelPosition(), BiExportGroups.OVERTIME_PARK_FEE_ORDER.getName())
                    .addHeader(OvertimeParkFeeOrderBi.class)
                    .appendDataList(List.of(orderBi.get(10, TimeUnit.MINUTES)))
                    .addHeader(OvertimeParkFeeOrderVo.class)
                    .loopAppendData((start, size) -> {
                        param.setStart((long) ((start - 1) * size))
                                .setSize(size);
                        param.setTotal(Boolean.FALSE);

                        ListResponse<OvertimeParkFeeOrderVo> all = this.overtimeParkFeeOrderRoDs.findAll(param);

                        if(CollectionUtils.isNotEmpty(all.getData())) {
                            // 导出占位费计费标准
                            for(OvertimeParkFeeOrderVo one : all.getData()) {
                                final String orderNo = one.getOrderNo();
                                final List<OrderOvertimeParkDivisionPo> parkDivisionRoDsByOrderNo =
                                    orderOvertimeParkDivisionRoDs.getByOrderNo(orderNo);
                                final boolean hasOvertimeDivision = CollectionUtils.isNotEmpty(parkDivisionRoDsByOrderNo);
                                one.setHasOvertimeDivision(hasOvertimeDivision);
                                if(one.getParkingPrice() == null) {
                                    continue;
                                }
                                String overtimePriceComment;
                                if(hasOvertimeDivision) {
                                    final OrderOvertimeParkDivisionPo orderDivisionPo =
                                        parkDivisionRoDsByOrderNo.get(0);

                                    final SiteOvertimeParkDivisionPo siteDivision =
                                        siteOvertimeParkDivisionRoDs.getById(orderDivisionPo.getDivisionId());

                                    final SiteOvertimeParkSettingPo siteSetting =
                                        siteOvertimeParkSettingRoDs.getById(siteDivision.getSettingId());

                                    final List<SiteOvertimeParkDivisionPo> siteAllDivision =
                                        siteOvertimeParkDivisionRoDs.getBySettingId(siteSetting.getId(), null);

                                    // 封顶金额
                                    final BigDecimal maxFee = siteSetting.getMaxFee()
                                        .setScale(2, RoundingMode.HALF_UP);

                                    final Map<Long, List<OrderOvertimeParkDivisionPo>> divisionMap =
                                        parkDivisionRoDsByOrderNo.stream()
                                        .collect(Collectors.groupingBy(
                                            OrderOvertimeParkDivisionPo::getDivisionId,
                                            Collectors.toList()));

                                    final String divisionStringPart = siteAllDivision.stream()
                                        .map(e -> {

                                            SiteOvertimeParkDivisionVo ret = new SiteOvertimeParkDivisionVo();
                                            BeanUtils.copyProperties(e, ret);

                                            final List<OrderOvertimeParkDivisionPo> orderDivision =
                                                divisionMap.get(e.getId());

                                            if (CollectionUtils.isNotEmpty(orderDivision)) {
                                                final BigDecimal feeSum = orderDivision.stream()
                                                    .map(OrderOvertimeParkDivisionPo::getFee)
                                                    .reduce(BigDecimal::add)
                                                    .orElse(BigDecimal.ZERO);
                                                final long durationSum = orderDivision.stream()
                                                    .mapToLong(
                                                        OrderOvertimeParkDivisionPo::getDuration)
                                                    .sum();
                                                ret.setDivisionFee(feeSum)
                                                    .setDuration(durationSum);
                                            } else {
                                                ret.setDivisionFee(BigDecimal.ZERO)
                                                    .setDuration(0L);
                                            }
                                            return ret;
                                        })
                                        .map(e -> {
                                            final String sStr = DateUtil.intToTime(
                                                e.getStartTime());
                                            final String eStr = DateUtil.intToTime(e.getEndTime());
                                            final BigDecimal fee = e.getFee()
                                                .setScale(2, RoundingMode.HALF_UP);
                                            final long duration = e.getDuration();
                                            final BigDecimal feeSum = e.getDivisionFee()
                                                .setScale(2, RoundingMode.HALF_UP);
                                            if (fee.equals(BigDecimal.ZERO)) {
                                                return sStr + "-" + eStr + " 该时段免费";
                                            }
                                            return sStr + "-" + eStr + " " + fee + "元/分钟 占用" +
                                                duration + "分钟 收费" + feeSum + "元";
                                        })
                                        .collect(Collectors.joining("；\n"));

                                    final String maxStringPart =
                                        "单次封顶" + maxFee + "元(单次占位费超过封顶则按封顶收取占位费)；\n";
                                    overtimePriceComment = maxStringPart + divisionStringPart;

                                } else {
                                    overtimePriceComment = "00:00-24:00 " +
                                        one.getParkingPrice().setScale(2, RoundingMode.HALF_UP) +
                                        "/分钟 占用" + one.getDistanceTime() + "分钟 收费" +
                                        one.getParkingFee().setScale(2, RoundingMode.HALF_UP) +
                                        "元";
                                }
                                one.setOvertimePriceComment(overtimePriceComment);
                            }
                        }

                        return new ArrayList<>(all.getData());
                    }, null)
                    .write2File();
            log.info("导出excel完成: {}", JsonUtils.toJsonString(param.getExcelPosition()));
//        } catch (Exception e) {
//            log.error("导出excel异常: {}, err = {}",
//                    JsonUtils.toJsonString(param.getExcelPosition()), e.getMessage(), e);
//        }
    }

    public void exportUserCouponList(CouponSearchParam couponSearchParam, ExcelPosition pos)
        throws Exception {
        log.info("活动券领取用户列表导出: {}, position = {}",
            JsonUtils.toJsonString(couponSearchParam), JsonUtils.toJsonString(pos));
        if (couponSearchParam.getActivityId() != null) {
            ActivityPo byId = activityRoDs.getById(couponSearchParam.getActivityId());
            if (byId != null) {
                // 先通过名称找到所有历史版本的id列表
                List<ActivityPo> activityPoList = activityRoDs.getByName(byId.getName());
                // 筛选出来不大于当前活动id的，找出来当前id以及之前的所有历史版本
                List<ActivityPo> filteredActivityPoList = activityPoList.stream()
                    .filter(activityPo -> activityPo.getId() <= couponSearchParam.getActivityId())
                    .sorted(Comparator.comparingLong(ActivityPo::getId).reversed())
                    .toList();
                List<Long> activityIdList = filteredActivityPoList.stream().map(ActivityPo::getId)
                    .toList();
                // 用activityIdList去查，不需要用activityId去查了
                couponSearchParam.setActivityIdList(activityIdList);
                couponSearchParam.setActivityId(null);
            }

        }
        ExcelUtil.builder(exportFileConfig.getExcelDir(), pos,
                BiExportGroups.USER_COUPON_LIST.getName())
            .addHeader(UserActivityCouponBiVo.class)
            .loopAppendData((start, size) -> {
                couponSearchParam.setSize(size);
                couponSearchParam.setStart((start - 1) * size);
                Page<CouponVo> pageInfo = PageHelper
                    .startPage(start, size, false, false, null);
                List<CouponVo> couponVoList = couponRoDs.findList(couponSearchParam);

                List<UserActivityCouponBiVo> userActivityCouponBiVoList = new ArrayList<UserActivityCouponBiVo>();
                couponVoList.forEach(couponVo -> {
                    UserActivityCouponBiVo userActivityCouponBiVo = new UserActivityCouponBiVo();
                    SimpleDateFormat secondSdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    userActivityCouponBiVo.setId(couponVo.getId())
                        .setActivityName(couponVo.getActivityName())
                        .setSiteName(couponVo.getSiteName())
                        .setAmount(StringUtils.isNotBlank(couponVo.getOrderNo()) ? couponVo.getAmount() : null)
                        .setDictName(couponVo.getDictName())
                        .setCreateDate(
                            couponVo.getCreateTime() != null ? secondSdf.format(couponVo.getCreateTime())
                                : "")
                        .setElecFee(couponVo.getElecFee())
                        .setOpName(couponVo.getOpName())
                        .setPhone(couponVo.getPhone())
                        .setOrderElec(couponVo.getOrderElec())
                        .setOrderNo(couponVo.getOrderNo())
                        .setOrderCreateTime(
                            couponVo.getOrderCreateTime() != null ? secondSdf.format(couponVo.getOrderCreateTime())
                                : "")
                        .setElecOriginFee(couponVo.getElecOriginFee())
                        .setOrderFee(couponVo.getOrderFee())
                        .setServFee(couponVo.getServFee())
                        .setServOriginFee(couponVo.getServOriginFee())
                        .setStatus(couponVo.getStatus().getDesc());

                    switch(couponVo.getValidType()) {
                        case FIX -> {
                            SimpleDateFormat daySdf = new SimpleDateFormat("yyyy-MM-dd");
                            Date validFromTime = couponVo.getValidTimeFrom();
                            Date validToTime = couponVo.getValidTimeTo();
                            if(couponVo.getValidTimeTo() != null) {
                                // 设定，有效期-截至
                                validToTime = DateUtil.getPrevDate(couponVo.getValidTimeTo());
                            } else {
                                // 取当前
                                validToTime = DateUtil.getThisDate(new Date());
                            }
                            userActivityCouponBiVo.setValidTime(daySdf.format(validFromTime) + "-"+daySdf.format(validToTime));
                        }
                        case RELATE -> {
                            if (couponVo.getValidRelateDay() != null) {
                                userActivityCouponBiVo.setValidTime(couponVo.getValidRelateDay() + "天内有效");
                            }
                        }
                    }

                    userActivityCouponBiVoList.add(userActivityCouponBiVo);
                });
                return new ArrayList<>(userActivityCouponBiVoList);
            })
            .write2File();
        log.info("活动券领取用户列表导出excel完成,参数 = {},位置 = {}",
            JsonUtils.toJsonString(couponSearchParam),
            JsonUtils.toJsonString(pos));
    }

    public void exportSiteMeterDataList(MeterDataListParam meterDataListParam, ExcelPosition pos)
        throws IOException {
        log.info("电表抄表数据列表: {}, position = {}",
            JsonUtils.toJsonString(meterDataListParam), JsonUtils.toJsonString(pos));
        // 场站id对应的电表列表
        Map<String, List<String>> siteIdAndDnoListMap = new HashMap<>();
        // 场站id对应的场站信息
        final Map<String, SiteVo> siteIdAndSiteInfoMap = new HashMap<>();
        // dno对应的桩列表
        Map<String, List<String>> dnoAndEvseListMap = new HashMap<>();

        List<String> siteIdList = new ArrayList<>();
        List<String> dnoList = new ArrayList<>();
        Set<String> evseNoSet = new HashSet<>();

        // 首先，按照分页数据在抄表数据里拿到所有的dno列表
        ListEssDailyParam listEssDailyParam = new ListEssDailyParam();

        TimeFilter2 timeFilter = new TimeFilter2();
        // 处理查询的账单时间
        if (meterDataListParam.getStartTimeFilter() != null) {
            timeFilter.setStartTime(
                meterDataListParam.getStartTimeFilter().getStartTime() != null
                    ? DateUtils.getMonthStartTime(
                    meterDataListParam.getStartTimeFilter().getStartTime(), false) : null);
            timeFilter.setEndTime(
                meterDataListParam.getStartTimeFilter().getEndTime() != null
                    ? DateUtils.getMonthEndTime(
                    meterDataListParam.getStartTimeFilter().getEndTime(), false) : null);
            listEssDailyParam.setStartTimeFilter(timeFilter);
        } else {
            timeFilter = null;
        }
        listEssDailyParam.setSiteIdList(meterDataListParam.getSiteIdList());
        listEssDailyParam.setDnos(dnoList);
        listEssDailyParam.setGidList(meterDataListParam.getGidList());
        listEssDailyParam.setStart(0L);
        listEssDailyParam.setSize(999);
        listEssDailyParam.setIdChain(meterDataListParam.getIdChain());
        // 获取到有数据的场站id，dno记录与site绑定才查询，因此之后再去meter表里拿场站绑定的dno
        List<String> elecDataSavedSiteIdList = essEquipTimelyRoDs.getDistinctSiteList(
            listEssDailyParam);

        List<SiteMeterDataBiVo> siteMeterDataBiVoList = new ArrayList<SiteMeterDataBiVo>();
        List<MeterDataBiVo> meterDataBiVoList = new ArrayList<MeterDataBiVo>();

        if (CollectionUtils.isNotEmpty(elecDataSavedSiteIdList)) {

            // 再去meter表看查出来的这些场站里有没有电表,meter表里有的才展示，没有的不展示
            MeterListParam meterListParam = new MeterListParam();
            meterListParam.setSiteIdList(elecDataSavedSiteIdList)
                .setIdChain(meterListParam.getIdChain())
                .setStart(0L)
                .setSize(9999)
                .setTotal(true);
            ListResponse<SiteMeterVo> siteMeterVoListResponse = meterFeignClient.getSiteMeterList(
                meterListParam);
            if (siteMeterVoListResponse != null && CollectionUtils.isNotEmpty(
                siteMeterVoListResponse.getData()) && siteMeterVoListResponse.getTotal() != null &&
                siteMeterVoListResponse.getTotal() > 0) {
                // 处理dno和桩列表
                Map<String, List<MeterEvseVo>> siteIdAndMeterVoMap = siteMeterVoListResponse.getData()
                    .stream()
                    .collect(Collectors.toMap(
                        SiteMeterVo::getSiteId,
                        SiteMeterVo::getMeterEvseList,
                        (existing, replacement) -> existing
                    ));

                siteIdAndMeterVoMap.forEach((siteId, meterEvseVoList) -> {
                    // 填入siteId对应的电表列表
                    siteIdAndDnoListMap.put(siteId,
                        CollectionUtils.isEmpty(meterEvseVoList) ? new ArrayList<>()
                            : meterEvseVoList.stream().map(MeterEvseVo::getDno)
                                .collect(Collectors.toList()));

                    siteIdList.add(siteId);

                    // 填入dno对应的桩列表
                    meterEvseVoList.forEach(meterEvseVo -> {
                        List<String> savedDeviceIdList = new ArrayList<>();
                        if (dnoAndEvseListMap.containsKey(meterEvseVo.getNo())) {
                            savedDeviceIdList = dnoAndEvseListMap.get(meterEvseVo.getNo());
                        }
                        List<String> deviceIdList = meterEvseVo.getDeviceMeterPoList().stream().map(
                            DeviceMeterPo::getDeviceId).toList();
                        savedDeviceIdList.addAll(deviceIdList);
                        dnoAndEvseListMap.put(meterEvseVo.getNo(), savedDeviceIdList);
                        dnoList.add(meterEvseVo.getDno());
                        evseNoSet.addAll(deviceIdList);
                    });

                });
            }

            List<String> evseNoList = evseNoSet.stream().toList();

            // 获取场站相关信息
            List<SiteVo> siteVoList = siteRoDs.getSiteVoBySiteIdList(siteIdList);
            // 场站id和场站信息map
            if (CollectionUtils.isNotEmpty(siteVoList)) {
                siteIdAndSiteInfoMap.putAll(siteVoList.stream()
                    .collect(Collectors.toMap(
                        SiteVo::getId,
                        siteVo -> siteVo,
                        (existing, replacement) -> existing
                    )));
            }

            listEssDailyParam.setSiteIdList(siteIdList);
            listEssDailyParam.setGidList(meterDataListParam.getGidList());
            listEssDailyParam.setDnos(dnoList);
            listEssDailyParam.setIdChain(meterListParam.getIdChain());

            if (CollectionUtils.isNotEmpty(dnoList) && CollectionUtils.isNotEmpty(siteIdList)) {

                Long totalCount = essEquipTimelyRoDs.getDistinctSiteAndMonthListCount(
                    listEssDailyParam);

                if (totalCount != null && totalCount > 0) {
                    long start = 0L;
                    int size = 1000;

                    listEssDailyParam.setSize(size);

                    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");

                    while (start < totalCount) {
                        listEssDailyParam.setStart(start);

                        List<EssEquipTimelyPo> essEquipTimelyPoList = essEquipTimelyRoDs.getSiteMonthElecDataList(
                            listEssDailyParam);
                        // 查出来的电表数据按场站进行分组
                        Map<String, List<EssEquipTimelyPo>> siteIdAndElecDataMap = essEquipTimelyPoList.stream()
                            .collect(Collectors.groupingBy(EssEquipTimelyPo::getSiteId));

                        log.info("桩充电量开始查询");
                        // 把涉及到的所有的桩对应的充电量先查出来，后边再按照电表包含的桩来统计电表上每个月的充电量
                        List<EvseElecVo> evseElecVoList =
                            CollectionUtils.isNotEmpty(evseNoSet)
                                ? chargerOrderRoDs.getElecBySiteAndEvseList(siteIdList, evseNoList,
                                timeFilter)
                                : new ArrayList<>();
                        log.info("桩充电量完成查询");

                        // 桩编号+时间和充电量map
                        Map<String, BigDecimal> evseNoAndElecDataMap = new HashMap<>();
                        if (CollectionUtils.isNotEmpty(evseNoList)) {
                            Map<String, BigDecimal> evseNoAndPowerMapTemp = evseElecVoList.stream()
                                .collect(Collectors.toMap(
                                    evseElecVO -> evseElecVO.getSiteId() + "-"
                                        + evseElecVO.getEvseNo() + "-" + evseElecVO.getMonthDate(),
                                    evseElecVo -> Optional.ofNullable(evseElecVo.getElectricity())
                                        .orElse(BigDecimal.ZERO),
                                    (existingValue, newValue) -> existingValue
                                ));
                            if (!evseNoAndPowerMapTemp.isEmpty()) {
                                evseNoAndElecDataMap.putAll(evseNoAndPowerMapTemp);
                            }
                        }

                        // 根据桩列表查询每个桩的装机功率，然后在使用的时候按照dno分组求和
                        ListEvseParam evseParam = new ListEvseParam();
                        evseParam.setEvseNoList(evseNoList);
                        ListResponse<EvsePo> evsePoListResponse = deviceFeignClient.getEvseList(
                            evseParam).block();
                        // 桩编号和功率map
                        Map<String, Integer> evseNoAndPowerMap = new HashMap<>();
                        if (evsePoListResponse != null && evsePoListResponse.getData() != null) {
                            Map<String, Integer> evseNoAndPowerMapTemp = evsePoListResponse.getData()
                                .stream()
                                .collect(Collectors.toMap(
                                    EvsePo::getEvseId,
                                    evsePo -> Optional.ofNullable(evsePo.getPower()).orElse(0),
                                    (existingValue, newValue) -> existingValue
                                ));
                            if (!evseNoAndPowerMapTemp.isEmpty()) {
                                evseNoAndPowerMap.putAll(evseNoAndPowerMapTemp);
                            }
                        }

                        // 开始计算
                        siteIdAndElecDataMap.forEach((siteId, elecDataList) -> {
                            // 每个电表里（elecDataList）又有多个时间段的，先按时间段分组，然后一个时间段是一个MeterDataBiVo
                            // 按 startTime 分组
                            Map<LocalDateTime, List<EssEquipTimelyPo>> monthElecDataMap = elecDataList.stream()
                                .collect(Collectors.groupingBy(EssEquipTimelyPo::getStartTime));

                            SiteVo siteVo = siteIdAndSiteInfoMap.getOrDefault(siteId, new SiteVo());

                            List<String> siteDnoList = siteIdAndDnoListMap.getOrDefault(siteId,
                                new ArrayList<>());

                            List<String> siteEvseNoList = siteDnoList.stream()
                                .map(dno -> dnoAndEvseListMap.getOrDefault(dno, new ArrayList<>()))
                                .flatMap(List::stream)
                                .toList();
                            // 装机功率
                            final BigDecimal totalPower = siteEvseNoList.stream()
                                .map(evseNo -> evseNoAndPowerMap.getOrDefault(evseNo, 0))
                                .map(BigDecimal::valueOf)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                            Set<String> siteDnoSet = new HashSet<>(siteDnoList);
                            monthElecDataMap.forEach((ts, monthElecDataList) -> {
                                String monthDate = ts.format(formatter);
                                List<EssEquipTimelyPo> siteEssEquipTimelyPoList = monthElecDataList.stream()
                                    .filter(timelyPo -> siteDnoSet.contains(timelyPo.getDno()))
                                    .toList();

                                List<EssEquipTimelyPo> allSiteMonthElecList = siteEssEquipTimelyPoList.stream()
                                    .filter(
                                        essEquipTimelyPo -> essEquipTimelyPo.getPiName()
                                            .equals("总"))
                                    .toList();
                                List<EssEquipTimelyPo> sharpPeakSiteMonthElecList = siteEssEquipTimelyPoList.stream()
                                    .filter(
                                        essEquipTimelyPo -> essEquipTimelyPo.getPiName()
                                            .equals("尖时"))
                                    .toList();
                                List<EssEquipTimelyPo> peakSiteMonthElecList = siteEssEquipTimelyPoList.stream()
                                    .filter(
                                        essEquipTimelyPo -> essEquipTimelyPo.getPiName()
                                            .equals("峰时"))
                                    .toList();
                                List<EssEquipTimelyPo> offPeakSiteMonthElecList = siteEssEquipTimelyPoList.stream()
                                    .filter(
                                        essEquipTimelyPo -> essEquipTimelyPo.getPiName()
                                            .equals("平时"))
                                    .toList();
                                List<EssEquipTimelyPo> valleySiteMonthElecList = siteEssEquipTimelyPoList.stream()
                                    .filter(
                                        essEquipTimelyPo -> essEquipTimelyPo.getPiName()
                                            .equals("谷时"))
                                    .toList();

                                BigDecimal totalElec = siteEvseNoList.stream()
                                    .map(evseNo -> evseNoAndElecDataMap.getOrDefault(
                                        siteId + "-" + evseNo + "-" + monthDate, BigDecimal.ZERO))
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
                                BigDecimal meterElec =
                                    allSiteMonthElecList.stream().map(EssEquipTimelyPo::getInKwh)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                                BigDecimal siteDiscount = meterElec.compareTo(BigDecimal.ZERO) != 0
                                    ? (meterElec.subtract(totalElec)).divide(
                                    meterElec, 4, RoundingMode.HALF_UP) : BigDecimal.ZERO;
                                // 每一个场站每个月的
                                SiteMeterDataBiVo siteMeterDataBiVo = new SiteMeterDataBiVo();
                                siteMeterDataBiVo.setSiteId(siteId)
                                    .setSiteName(siteVo.getSiteName())
                                    .setSiteNo(siteVo.getSiteNo())
                                    .setMonthDate(monthDate)
                                    .setAddress(
                                        siteVo.getProvince() != null && siteVo.getCity() != null
                                            && siteVo.getArea() != null ? siteVo.getProvinceName()
                                            + '/'
                                            + siteVo.getCityName() + '/' + siteVo.getAreaName()
                                            : null)
                                    // 装机功率
                                    .setTotalPower(totalPower.setScale(0, RoundingMode.HALF_UP))
                                    // 订单电量
                                    .setOrderElec(totalElec.setScale(4, RoundingMode.HALF_UP))
                                    .setMeterElec(meterElec)
                                    .setSharpPeakElec(Optional.ofNullable(sharpPeakSiteMonthElecList)
                                        .orElseGet(Collections::emptyList)
                                        .stream()
                                        .filter(Objects::nonNull)
                                        .map(po -> Optional.ofNullable(po.getInKwh()).orElse(BigDecimal.ZERO))
                                        .reduce(BigDecimal.ZERO, BigDecimal::add))
                                    .setPeakElec(
                                        Optional.ofNullable(peakSiteMonthElecList)
                                            .orElseGet(Collections::emptyList)
                                            .stream()
                                            .filter(Objects::nonNull)
                                            .map(po -> Optional.ofNullable(po.getInKwh()).orElse(BigDecimal.ZERO))
                                            .reduce(BigDecimal.ZERO, BigDecimal::add))
                                    .setOffPeakElec(Optional.ofNullable(offPeakSiteMonthElecList)
                                        .orElseGet(Collections::emptyList)
                                        .stream()
                                        .filter(Objects::nonNull)
                                        .map(po -> Optional.ofNullable(po.getInKwh()).orElse(BigDecimal.ZERO))
                                        .reduce(BigDecimal.ZERO, BigDecimal::add))
                                    .setValleyElec(
                                        Optional.ofNullable(valleySiteMonthElecList)
                                            .orElseGet(Collections::emptyList)
                                            .stream()
                                            .filter(Objects::nonNull)
                                            .map(po -> Optional.ofNullable(po.getInKwh()).orElse(BigDecimal.ZERO))
                                            .reduce(BigDecimal.ZERO, BigDecimal::add))
                                    .setDiscount(siteDiscount.compareTo(BigDecimal.ZERO) == 0 ? "0"
                                        : String.valueOf(siteDiscount.multiply(
                                                BigDecimal.valueOf(100))
                                            .setScale(1, RoundingMode.HALF_UP)));
                                siteMeterDataBiVoList.add(siteMeterDataBiVo);

                                // 每个电表的
                                siteDnoList.forEach(dno -> {
                                    List<String> evseDnoList = dnoAndEvseListMap.getOrDefault(dno,
                                        new ArrayList<>());

                                    MeterDataBiVo meterDataBiVo = new MeterDataBiVo();
                                    // 装机功率
                                    BigDecimal dnoPower = evseDnoList.stream()
                                        .map(evseNo -> evseNoAndPowerMap.getOrDefault(evseNo, 0))
                                        .map(BigDecimal::valueOf)
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                                    // 订单电量
                                    BigDecimal dnoElec = evseDnoList.stream()
                                        .map(evseNo -> evseNoAndElecDataMap.getOrDefault(
                                            siteId + "-" + evseNo + "-" + monthDate,
                                            BigDecimal.ZERO))
                                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                                    // 抄表电量
                                    BigDecimal dnoMeterElec = Optional.of(allSiteMonthElecList)
                                        .orElseGet(Collections::emptyList)
                                        .stream()
                                        .filter(timelyPo -> timelyPo.getDno().equals(dno))
                                        .findFirst()
                                        .map(po -> Optional.ofNullable(po.getInKwh()).orElse(BigDecimal.ZERO))
                                        .orElse(BigDecimal.ZERO);

                                    EssEquipTimelyPo allMonthElec = Optional.of(allSiteMonthElecList)
                                        .orElseGet(Collections::emptyList)
                                        .stream()
                                        .filter(timelyPo -> timelyPo.getDno()
                                            .equals(dno))
                                        .findFirst().orElse(new EssEquipTimelyPo());

                                    BigDecimal dnoDiscount =
                                        dnoMeterElec.compareTo(BigDecimal.ZERO) != 0
                                            ? (dnoMeterElec.subtract(dnoElec)).divide(
                                            dnoMeterElec, 3, RoundingMode.HALF_UP)
                                            : BigDecimal.ZERO;

                                    meterDataBiVo.setSiteId(siteId)
                                        .setSiteName(siteVo.getSiteName())
                                        .setSiteNo(siteVo.getSiteNo())
                                        .setMonthDate(monthDate)
                                        .setAddress(
                                            siteVo.getProvince() != null && siteVo.getCity() != null
                                                && siteVo.getArea() != null ?
                                                siteVo.getProvinceName()
                                                    + '/'
                                                    + siteVo.getCityName() + '/'
                                                    + siteVo.getAreaName()
                                                : null)
                                        .setDno(dno)
                                        // 装机功率
                                        .setTotalPower(dnoPower.setScale(0, RoundingMode.HALF_UP))
                                        // 订单电量
                                        .setOrderElec(dnoElec.setScale(4, RoundingMode.HALF_UP))
                                        .setMeterElec(dnoMeterElec)
                                        .setSharpPeakElec(Optional.ofNullable(sharpPeakSiteMonthElecList)
                                            .orElseGet(Collections::emptyList)
                                            .stream()
                                            .filter(timelyPo -> timelyPo.getDno()
                                                .equals(dno))
                                            .findFirst().orElse(new EssEquipTimelyPo()).getInKwh())
                                        .setPeakElec(Optional.ofNullable(peakSiteMonthElecList)
                                            .orElseGet(Collections::emptyList)
                                            .stream()
                                            .filter(timelyPo -> timelyPo.getDno()
                                                .equals(dno))
                                            .findFirst().orElse(new EssEquipTimelyPo()).getInKwh())
                                        .setOffPeakElec(Optional.ofNullable(offPeakSiteMonthElecList)
                                            .orElseGet(Collections::emptyList)
                                            .stream()
                                            .filter(timelyPo -> timelyPo.getDno()
                                                .equals(dno))
                                            .findFirst().orElse(new EssEquipTimelyPo()).getInKwh())
                                        .setValleyElec(Optional.ofNullable(valleySiteMonthElecList)
                                            .orElseGet(Collections::emptyList)
                                            .stream()
                                            .filter(timelyPo -> timelyPo.getDno()
                                                .equals(dno))
                                            .findFirst().orElse(new EssEquipTimelyPo()).getInKwh())
                                        .setDiscount(
                                            dnoDiscount.compareTo(BigDecimal.ZERO) == 0 ? "0"
                                                : String.valueOf(dnoDiscount.multiply(
                                                        BigDecimal.valueOf(100))
                                                    .setScale(1, RoundingMode.HALF_UP)))
                                        .setStartTotalReadingElec(allMonthElec.getTotalInKwh())
                                        .setEndTotalReadingElec(
                                            allMonthElec.getTotalInKwh() != null
                                                ? allMonthElec.getTotalInKwh()
                                                .add(allMonthElec.getInKwh())
                                                : allMonthElec.getInKwh());
                                    meterDataBiVoList.add(meterDataBiVo);
                                });

                            });

                        });

                        start += size;
                    }
                }
            }
        }

        ExcelUtil.builder(exportFileConfig.getExcelDir(), pos,
                BiExportGroups.SITE_METER_DATA_LIST.getName())
            .addHeader(SiteMeterDataBiVo.class)
            .appendDataList(siteMeterDataBiVoList)
            .newSheet("电表抄表数据列表")
            .addHeader(MeterDataBiVo.class)
            .appendDataList(meterDataBiVoList)
            .write2File();
        log.info("电表抄表数据列表导出excel完成,参数 = {},位置 = {}",
            JsonUtils.toJsonString(meterDataListParam),
            JsonUtils.toJsonString(pos));
    }

}
