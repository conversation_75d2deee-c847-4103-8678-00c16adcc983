package com.cdz360.biz.bi.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.bi.service.ExcelFileService;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.chargerlinkcar.framework.common.domain.BillExportParam;
import com.chargerlinkcar.framework.common.domain.CorpListPointLogParam;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.UUIDUtils;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jettison.json.JSONException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 *  订单导出相关
 */
@Slf4j
@RestController
public class OrderExportRest extends BaseController {


    @Autowired
    private ExcelFileService excelFileService;

    @Deprecated(since = "20220301")
    @PostMapping("/api/order/writeTempExcelByChargeOrderList")
    public ObjectResponse<ExcelPosition> writeTempExcelByChargeOrderList(
        @RequestBody ChargerOrderParam searchParam) throws JSONException {
//        throw new DcServiceException("接口已弃用，请联系开发");
        String taskId = UUIDUtils.getUuid();
        log.info("导出订单数据 taskId = {}, param = {}", taskId, searchParam);
        ExcelPosition position = new ExcelPosition();
        position.setSubFileName(UUIDUtils.getUuid32());
        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
        log.info("writeTempExcelByChargeOrderList 启动,position:{}",
            JsonUtils.toJsonString(position));
        searchParam.setExcelPosition(position);
        searchParam.setSheetName("订单汇总");

        if (NumberUtils.equals(23, searchParam.getPlatform())) { //企业账户导出
            excelFileService.writeTempExcelByChargeOrderForCompany(taskId, searchParam);
        } else if (NumberUtils.equals(21, searchParam.getPlatform())) { //管理平台导出
//            searchParam.setSheetName("订单明细");
//            excelFileService.writeTempExcelByChargeOrderList(taskId, searchParam);
            throw new DcServiceException("接口已弃用，请联系开发");
        } else {
            log.warn("不支持的请求 platform: {}", searchParam.getPlatform());
            return RestUtils.serverBusy4ObjectResponse();
        }
        return new ObjectResponse<>(position);
    }

    /**
     * 商户会员订单
     *
     * @param searchParam
     * @return
     */
    @PostMapping("/api/order/exportCommUserOrderList")
    public ObjectResponse<ExcelPosition> exportCommUserOrderList(
        @RequestBody ChargerOrderParam searchParam) {
        throw new DcServiceException("接口已废弃，请联系开发");

//        ExcelPosition position = new ExcelPosition();
//        position.setSubFileName(UUIDUtils.getUuid32());
//        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
//        log.info("writeTempExcelByChargeOrderList 启动,position:{}", JsonUtils.toJsonString(position));
//        searchParam.setExcelPosition(position);
//        searchParam.setSheetName("商户会员订单");
//        excelFileService.exportCommUserOrderList(searchParam);
//
//        return new ObjectResponse<>(position);
    }

    @PostMapping("/api/corp/exportExcelTrade")
    public ObjectResponse<ExcelPosition> exportExcelTrade(
        @RequestBody CorpListPointLogParam searchParam) {
        ExcelPosition position = new ExcelPosition();
        position.setSubFileName(UUIDUtils.getUuid32());
        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
        log.info("exportExcelTrade 启动,position:{}", JsonUtils.toJsonString(position));
        searchParam.setExcelPosition(position);
        searchParam.setSheetName("交易统计");
        excelFileService.writeTempExcelByChargeTradeList(searchParam);
        return new ObjectResponse<>(position);
    }


    @RequestMapping(value = "/api/order/checkExcelFileCompeleted")
    public ObjectResponse<Boolean> checkExcelFileCompeleted(String subDir, String subFileName) {
        log.info("检查文件是否存在subDir:{}, subFileName:{}", subDir, subFileName);
        if (StringUtils.isEmpty(subDir) || StringUtils.isEmpty(subFileName)) {
            throw new DcArgumentException("参数错误");
        }
        boolean exist = excelFileService.existsDownFile(subDir, subFileName);

        return new ObjectResponse<>(exist);
    }

    //下载excel文件
    @RequestMapping(value = "/api/order/download", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void download(String subDir, String subFileName, HttpServletResponse response)
        throws IOException {
        log.info("down文件subDir:{}, subFileName:{}", subDir, subFileName);
        if (StringUtils.isEmpty(subDir)) {
            throw new DcArgumentException("参数错误");
        }
        excelFileService.downFile(subDir, subFileName, response);
    }


    @PostMapping("/api/order/exportSettlementByBillNo")
    public ObjectResponse<ExcelPosition> exportSettlementByBillNo(
        @RequestParam("billNo") String billNo) {
        log.info("导出账单: {}", billNo);
        throw new DcServiceException("接口已废弃，请联系开发");

//        ExcelPosition position = new ExcelPosition();
//        position.setSubFileName(UUIDUtils.getUuid32());
//        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
//        log.info("导出账单启动,position:{}", JsonUtils.toJsonString(position));
//
//        IotAssert.isNotBlank(billNo, "请传入账单号");
//
//        excelFileService.writeTempExcelByBillNo(billNo, position);
//
//        return new ObjectResponse<>(position);
    }

    @PostMapping("/api/order/exportVinOrderList")
    public ObjectResponse<ExcelPosition> exportVinOrderList(
        @RequestBody ChargerOrderParam searchParam) {
        ExcelPosition position = new ExcelPosition();
        position.setSubFileName(UUIDUtils.getUuid32());
        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
        log.info("exportVinOrderList position:{}", JsonUtils.toJsonString(position));
        searchParam.setExcelPosition(position);
        excelFileService.exportVinOrderListX(searchParam);
        return RestUtils.buildObjectResponse(position);
    }

    /**
     * 用于企客对账OA导出至附件
     *
     * @param params
     * @return
     * @throws Exception
     */
    @PostMapping(value = "/api/corp/exportBillExcel")
    public ObjectResponse<BillExportParam> exportBillExcel(@RequestBody BillExportParam params)
        throws Exception {
        log.info("临时账单导出,params={}", JsonUtils.toJsonString(params));
        ExcelPosition position = new ExcelPosition();
        // 账单导出命名
        if (StringUtils.isNotEmpty(params.getBillNo())) {
            position.setSubFileName(params.getBillNo() + "_" + System.currentTimeMillis());
        } else {
            position.setSubFileName("实际结算_" + System.currentTimeMillis());
        }
        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
        return new ObjectResponse<>(excelFileService.exportBillExcel(params, position));
    }

}
