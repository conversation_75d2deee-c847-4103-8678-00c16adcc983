package com.cdz360.biz.bi.service.download.impl;

import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.es.vo.BmsRtData;
import com.cdz360.base.model.es.vo.EssBaseVal;
import com.cdz360.base.model.es.vo.MeterRtData;
import com.cdz360.base.model.es.vo.SensorVal;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.bi.service.download.IFileExport;
import com.cdz360.biz.bi.service.ess.EssBiService;
import com.cdz360.biz.bi.service.oss.CommEssDataService;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.ess.param.ListEquipHisDataParam;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.chargerlinkcar.framework.common.utils.ExcelUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.LineIterator;
import org.apache.commons.io.input.ReversedLinesFileReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class CommEssHistoryDataExportImpl extends
    AbstractFileExport<ListEquipHisDataParam, ExcelPosition>
    implements IFileExport<ListEquipHisDataParam, ExcelPosition> {

    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private EssBiService essBiService;

    @Autowired
    private CommEssDataService commEssDataService;

    @Autowired
    private BatClusterHistoryDataManager batClusterHistoryDataManager;

    @Autowired
    private BatStackHistoryDataManager batStackHistoryDataManager;

    @PostConstruct
    public void init() {
        downloadFileProxy.addProxy(DownloadFunctionType.ESS_HIS_DATA, this);
    }

    @Override
    public Class<ListEquipHisDataParam> paramClazz() {
        return ListEquipHisDataParam.class;
    }

    @Override
    public void genFile(String context, String pos) throws IOException {
        ListEquipHisDataParam hisDataParam = this.convert(context);

        SitePo site = siteRoDs.getSite(hisDataParam.getSiteId());
        if (null == site || StringUtils.isBlank(site.getTimeZone())) {
            log.error("场站ID或时区无效: {}", context);
            return;
        }

        final String tz = site.getTimeZone();
        final String datePost = hisDataParam.getDate().format(FORMAT_yyyy_MM_dd);
        ExcelUtil excel = ExcelUtil.builder(
            exportFileConfig.getExcelDir(), this.convertPos(pos), "历史数据_" + datePost);
        this.getHisData(hisDataParam)
            .filter(Optional::isPresent)
            .map(Optional::get)
            .switchIfEmpty(Mono.just(""))
            .doOnNext(localFile -> {
                if (StringUtils.isNotBlank(localFile)) {
                    try (LineIterator lineIterator = FileUtils.lineIterator(
                        new File(localFile), "utf-8")) {

                        List<String> headerList = new ArrayList<>();
                        switch (hisDataParam.getEquipType()) {
//                            case METER:
//                            case GRID_GATEWAY_METER:
//                            case ESS_GATEWAY_METER:
//                            case PV_INV_GATEWAY_METER:
//                            case EVSE_GATEWAY_METER:
//                            case WIND_ENERGY_GATEWAY_METER:
//                            case LOAD_GATEWAY_METER:
//                            case ESS_INSIDE_METER:
//                            case HIGH_VOLTAGE_SIDE_METER:
//                                headerList.addAll(List.of("日期", "A相电压", "B相电压", "C相电压",
//                                    "A相电流", "B相电流", "C相电流", "频率",
//                                    "A相功率因数", "B相功率因数", "C相功率因数", "总功率因数",
//                                    "A相有功功率", "B相有功功率", "C相有功功率", "总有功功率",
//                                    "A相无功功率", "B相无功功率", "C相无功功率", "总无功功率",
//                                    "A相视在功率", "B相视在功率", "C相视在功率", "总视在功功率",
//                                    "当前有功需量", "入户电量", "出户电量"));
//                                excel.addHeader(headerList);
//                                meterExcelData(dataList, excel);
//                                break;
                            case BMS:
                                headerList.addAll(
                                    List.of("日期", "SOC", "SOH", "单体最高温度", "单体最低温度"));
                                excel.addHeader(headerList);
                                bmsExcelData(lineIterator, excel);
                                break;
                            case BATTERY_STACK:
                                batStackHistoryDataManager.batteryStackExcelData(
                                    localFile, lineIterator, hisDataParam.getDno(), excel);
                                break;
                            case BATTERY_PACK:
                                batClusterHistoryDataManager.batteryClusterExcelData(
                                    localFile, lineIterator, hisDataParam.getDno(), excel);
                                break;
                            default:
                                try (ReversedLinesFileReader reader = new ReversedLinesFileReader(
                                    new File(localFile), Charset.defaultCharset())) {
                                    String line = reader.readLine();
                                    while (null != line) {
                                        JsonNode node = JsonUtils.fromJson(
                                            OssLineSplit.validSplit(line));
                                        if (node == null) {
                                            line = reader.readLine();
                                            continue;
                                        }
                                        JsonNode jsonNode = node.get("sensors");
                                        if (null != jsonNode) {
                                            List<SensorVal> sensorValues = JsonUtils.fromJson(
                                                jsonNode,
                                                new TypeReference<List<SensorVal>>() {
                                                });
                                            headerList.add("日期");
                                            sensorValues.forEach(
                                                kv -> headerList.add(kv.getName()));
                                            excel.addHeader(headerList);
                                            break;
                                        }
                                        line = reader.readLine();
                                    }
                                } catch (Exception e) {
                                    log.error("打开文件失败: {}", e.getMessage(), e);
                                }

                                while (lineIterator.hasNext()) {
                                    JsonNode jsonNode;
                                    try {
                                        jsonNode = JsonUtils.fromJson(
                                            OssLineSplit.validSplit(lineIterator.nextLine()));
                                    } catch (Exception e) {
                                        log.error("解析数据失败: {}", e.getMessage(), e);
                                        continue;
                                    }
                                    if (jsonNode == null) {
                                        continue;
                                    }
                                    ArrayList<Object> valList = new ArrayList<>();
                                    JsonNode node = jsonNode.get("sensors");
                                    if (null != node) {
                                        List<EssBaseVal<BigDecimal>> sensorValues = JsonUtils.fromJson(
                                            node,
                                            new TypeReference<List<EssBaseVal<BigDecimal>>>() {
                                            });

                                        if (CollectionUtils.isNotEmpty(sensorValues)) {
                                            valList.add(null != jsonNode.get("ldt") ?
                                                jsonNode.get("ldt").asText() : "");

                                            Map<String, EssBaseVal<BigDecimal>> nameMap = sensorValues.stream()
                                                .collect(
                                                    Collectors.toMap(EssBaseVal::getName, o -> o));

                                            headerList.subList(1, headerList.size())
                                                .forEach(
                                                    name -> valList.add(nameMap.containsKey(name) ?
                                                        nameMap.get(name).getV() : "--"));
                                        }
                                    }
                                    excel.appendData(valList);
                                }

                                break;
                        }
                    } catch (IOException e) {
                        log.error("文件读取失败: {}", e.getMessage(), e);
                        throw new RuntimeException(e);
                    }
                }
            })
            .flatMap(xx -> this.getHisStatusData(hisDataParam)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .switchIfEmpty(Mono.just(""))
                .doOnNext(localFile -> {
                    if (StringUtils.isNotBlank(localFile)) {
                        final List<String> headerList = new ArrayList<>();
                        final List<List<String>> nodeHeaderList = new ArrayList<>();

                        try (LineIterator lineIterator = FileUtils.lineIterator(
                            new File(localFile), "utf-8")) {

                            switch (hisDataParam.getEquipType()) {
                                case METER:
                                case GRID_GATEWAY_METER:
                                case ESS_GATEWAY_METER:
                                case PV_INV_GATEWAY_METER:
                                case EVSE_GATEWAY_METER:
                                case WIND_ENERGY_GATEWAY_METER:
                                case LOAD_GATEWAY_METER:
                                case ESS_INSIDE_METER:
                                case HIGH_VOLTAGE_SIDE_METER:
                                case BMS:
                                    break;
                                case BATTERY_STACK:
                                    excel.newSheet("状态历史数据_" + datePost);
                                    batStackHistoryDataManager.batteryStackStatusExcelData(tz,
                                        localFile, lineIterator, hisDataParam.getDno(), excel);
                                    break;
                                case BATTERY_PACK:
                                    excel.newSheet("状态历史数据_" + datePost);
                                    batClusterHistoryDataManager.batteryClusterStatusExcelData(tz,
                                        localFile, lineIterator, hisDataParam.getDno(), excel);
                                    break;
                                default:
                                    excel.newSheet("状态历史数据_" + datePost);

                                    while (lineIterator.hasNext()) {
                                        JsonNode x;
                                        try {
                                            x = JsonUtils.fromJson(
                                                OssLineSplit.validSplit(lineIterator.nextLine()));
                                        } catch (Exception e) {
                                            log.error("解析数据失败: {}", e.getMessage(), e);
                                            continue;
                                        }
                                        if (x == null) {
                                            continue;
                                        }
                                        JsonNode jsonNode = x.get("rtInfo");
                                        if (null == jsonNode) {
                                            continue;
                                        }

                                        // 请求头信息
                                        if (CollectionUtils.isEmpty(headerList)) {
                                            nodeHeaderList.addAll(extractHeaderList(jsonNode));
                                            headerList.add("日期");
                                            nodeHeaderList.forEach(headerList::addAll);
                                            excel.addHeader(headerList);
                                        }

                                        ArrayList<Object> valList = new ArrayList<>();
                                        JsonNode node1 = jsonNode.get("texts");
                                        JsonNode node2 = jsonNode.get("signals");
                                        JsonNode node3 = jsonNode.get("cfgs");

                                        long ts = jsonNode.get("ts").asLong();
                                        valList.add(LocalDateTime.ofEpochSecond(
                                                ts, 0, ZoneOffset.of(tz))
                                            .format(FORMAT_yyyy_MM_dd_HH_mm_ss));
                                        extractDataItemList(nodeHeaderList, valList, node1, node2,
                                            node3);

                                        excel.appendData(valList);
                                    }

                                    break;
                            }
                        } catch (IOException e) {
                            log.error("文件读取失败: {}", e.getMessage(), e);
                            throw new RuntimeException(e);
                        }
                    }
                }))
            .doOnNext(xx -> {
                try {
                    excel.write2File();
                } catch (IOException e) {
                    log.error("下载写入文件异常: {}", e.getMessage(), e);
                    throw new DcServiceException(e.getMessage());
                }
            })
            .block(Duration.ofMinutes(2L));
    }

    private List<List<String>> extractHeaderList(JsonNode lastNode) {
        // nodes ->: StringVal SignalVal RegisterRwValue
        JsonNode node1 = lastNode.get("texts");
        JsonNode node2 = lastNode.get("signals");
        JsonNode node3 = lastNode.get("cfgs");
        List<List<String>> result = new ArrayList<>();
        for (JsonNode node : Arrays.asList(node1, node2, node3)) {
            if (null != node) {
                List<String> tmpList = new ArrayList<>();
                JsonUtils.fromJson(node, new TypeReference<List<EssBaseVal<BigDecimal>>>() {
                }).forEach(kv -> tmpList.add(kv.getName()));
                result.add(tmpList);
            }
        }
        return result;
    }

    private void extractDataItemList(
        List<List<String>> nodeHeaderList, ArrayList<Object> valList, JsonNode... nodes) {
        if (null == nodeHeaderList) {
            return;
        }
        // nodes ->: StringVal SignalVal RegisterRwValue
        int idx = 0;
        for (JsonNode node : nodes) {
            if (null != node) {
                List<EssBaseVal<BigDecimal>> kvList = JsonUtils.fromJson(node,
                    new TypeReference<List<EssBaseVal<BigDecimal>>>() {
                    });

                Map<String, EssBaseVal<BigDecimal>> nameMap = kvList.stream()
                    .collect(Collectors.toMap(EssBaseVal::getName, o -> o));

                List<String> headerList = nodeHeaderList.get(idx++);
                headerList.forEach(name -> valList.add(nameMap.containsKey(name) ?
                    nameMap.get(name).getV() : "--"));
            }
        }
    }

    private Mono<Optional<String>> getHisData(ListEquipHisDataParam hisDataParam) {
        if (null != hisDataParam.getEquipType()) {
            switch (hisDataParam.getEquipType()) {
                case PCS:
                    return commEssDataService.getOSSPcsData(hisDataParam.getSiteId(),
                        hisDataParam.getDno(), hisDataParam.getDate().toLocalDate());
                case BMS:
                    return commEssDataService.getOSSBmsData(hisDataParam.getSiteId(),
                        hisDataParam.getDno(), hisDataParam.getDate().toLocalDate());
                case BATTERY_STACK:
                case BATTERY_PACK:
                    return commEssDataService.getOSSBmsData(hisDataParam.getSiteId(),
                        hisDataParam.getBmsDno(), hisDataParam.getDate().toLocalDate());
                case METER:
                case GRID_GATEWAY_METER:
                case ESS_GATEWAY_METER:
                case PV_INV_GATEWAY_METER:
                case EVSE_GATEWAY_METER:
                case WIND_ENERGY_GATEWAY_METER:
                case LOAD_GATEWAY_METER:
                case ESS_INSIDE_METER:
                case HIGH_VOLTAGE_SIDE_METER:
                    return commEssDataService.getOSSMeterData(hisDataParam.getSiteId(),
                        hisDataParam.getDno(), hisDataParam.getDate().toLocalDate());
            }
        }
        return Mono.just(Optional.empty());
    }

    private Mono<Optional<String>> getHisStatusData(ListEquipHisDataParam hisDataParam) {
        if (null != hisDataParam.getEquipType()) {
            switch (hisDataParam.getEquipType()) {
                case PCS:
                    return commEssDataService.getOSSPcsInfo(hisDataParam.getSiteId(),
                        hisDataParam.getDno(), hisDataParam.getDate().toLocalDate());
                case BMS:
                    return commEssDataService.getOSSBmsInfo(hisDataParam.getSiteId(),
                        hisDataParam.getDno(), hisDataParam.getDate().toLocalDate());
                case BATTERY_STACK:
                case BATTERY_PACK:
                    return commEssDataService.getOSSBmsInfo(hisDataParam.getSiteId(),
                        hisDataParam.getBmsDno(), hisDataParam.getDate().toLocalDate());
                case METER:
                case GRID_GATEWAY_METER:
                case ESS_GATEWAY_METER:
                case PV_INV_GATEWAY_METER:
                case EVSE_GATEWAY_METER:
                case WIND_ENERGY_GATEWAY_METER:
                case LOAD_GATEWAY_METER:
                case ESS_INSIDE_METER:
                case HIGH_VOLTAGE_SIDE_METER:
                    return commEssDataService.getOSSMeterInfo(hisDataParam.getSiteId(),
                        hisDataParam.getDno(), hisDataParam.getDate().toLocalDate());
            }
        }
        return Mono.just(Optional.empty());
    }


    private void meterExcelData(List<JsonNode> dataList, ExcelUtil excel) {
//        A相电压	B相电压	C相电压	A相电流	B相电流	C相电流
//        频率	A相功率因数	B相功率因数	C相功率因数	总功率因数
//        A相有功功率	B相有功功率	C相有功功率
//        A相无功功率	B相无功功率	C相无功功率
//        A相视在功率	B相视在功率	C相视在功率
//        总有功功率	总无功功率	总视在功功率
//        入户电量	出户电量	当前有功需量
//      excel.startRowNum(1); // 起始位置设置
        dataList.forEach(jsonNode -> {
            ArrayList<Object> valList = new ArrayList<>();
            MeterRtData meterRtData = JsonUtils.fromJson(jsonNode,
                new TypeReference<MeterRtData>() {
                });
            if (null != meterRtData) {
                valList.add(null != jsonNode.get("ldt") ?
                    jsonNode.get("ldt").asText() : "");

                if (null != meterRtData.getAbc()) {
                    if (null != meterRtData.getAbc().getVoltage()) {
                        valList.add(meterRtData.getAbc().getVoltage().getV1());
                        valList.add(meterRtData.getAbc().getVoltage().getV2());
                        valList.add(meterRtData.getAbc().getVoltage().getV3());
                    } else {
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                    }
                    if (null != meterRtData.getAbc().getCurrent()) {
                        valList.add(meterRtData.getAbc().getCurrent().getV1());
                        valList.add(meterRtData.getAbc().getCurrent().getV2());
                        valList.add(meterRtData.getAbc().getCurrent().getV3());
                    } else {
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                    }
                    if (null != meterRtData.getAbc().getRate()) {
                        valList.add(meterRtData.getAbc().getRate());
                    } else {
                        valList.add("--");
                    }
                    if (null != meterRtData.getAbc().getPf()) {
                        valList.add(meterRtData.getAbc().getPf().getV1());
                        valList.add(meterRtData.getAbc().getPf().getV2());
                        valList.add(meterRtData.getAbc().getPf().getV3());
                        valList.add(meterRtData.getAbc().getPf().getTotal());
                    } else {
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                    }
                    if (null != meterRtData.getAbc().getActivePower()) {
                        valList.add(meterRtData.getAbc().getActivePower().getV1());
                        valList.add(meterRtData.getAbc().getActivePower().getV2());
                        valList.add(meterRtData.getAbc().getActivePower().getV3());
                        valList.add(meterRtData.getAbc().getActivePower().getTotal());
                    } else {
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                    }
                    if (null != meterRtData.getAbc().getReactivePower()) {
                        valList.add(meterRtData.getAbc().getReactivePower().getV1());
                        valList.add(meterRtData.getAbc().getReactivePower().getV2());
                        valList.add(meterRtData.getAbc().getReactivePower().getV3());
                        valList.add(meterRtData.getAbc().getReactivePower().getTotal());
                    } else {
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                    }
                    if (null != meterRtData.getAbc().getApparentPower()) {
                        valList.add(meterRtData.getAbc().getApparentPower().getV1());
                        valList.add(meterRtData.getAbc().getApparentPower().getV2());
                        valList.add(meterRtData.getAbc().getApparentPower().getV3());
                        valList.add(meterRtData.getAbc().getApparentPower().getTotal());
                    } else {
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                    }
                    if (null != meterRtData.getAbc().getActiveDemand()) {
                        valList.add(meterRtData.getAbc().getActiveDemand());
                    } else {
                        valList.add("--");
                    }
                }

                if (null != meterRtData.getKwh()) {
                    if (null != meterRtData.getKwh().getPositive()) {
                        valList.add(meterRtData.getKwh().getPositive().getTotal());
                    } else {
                        valList.add("--");
                    }
                    if (null != meterRtData.getKwh().getNegative()) {
                        valList.add(meterRtData.getKwh().getNegative().getTotal());
                    } else {
                        valList.add("--");
                    }
                }
            }
            excel.appendData(valList);
        });
    }

    private void bmsExcelData(LineIterator lineIterator, ExcelUtil excel) {
//        "日期", "SOC", "SOH", "单体最高温度", "单体最低温度"
        while (lineIterator.hasNext()) {
            JsonNode jsonNode;
            try {
                jsonNode = JsonUtils.fromJson(
                    OssLineSplit.validSplit(lineIterator.nextLine()));
            } catch (Exception e) {
                log.error("解析数据失败: {}", e.getMessage(), e);
                continue;
            }
            if (null == jsonNode) {
                continue;
            }
            ArrayList<Object> valList = new ArrayList<>();
            BmsRtData bmsRtData = JsonUtils.fromJson(jsonNode,
                new TypeReference<BmsRtData>() {
                });

            if (null != bmsRtData) {
                valList.add(null != jsonNode.get("ldt") ?
                    jsonNode.get("ldt").asText() : "");

                if (null != bmsRtData.getSoc()) {
                    valList.add(bmsRtData.getSoc());
                } else {
                    valList.add("--");
                }

                if (null != bmsRtData.getSoh()) {
                    valList.add(bmsRtData.getSoh());
                } else {
                    valList.add("--");
                }

                if (null != bmsRtData.getBatteryTempMax()) {
                    valList.add(bmsRtData.getBatteryTempMax());
                } else {
                    valList.add("--");
                }

                if (null != bmsRtData.getBatteryTempMin()) {
                    valList.add(bmsRtData.getBatteryTempMin());
                } else {
                    valList.add("--");
                }
            }

            excel.appendData(valList);
        }
    }
}
