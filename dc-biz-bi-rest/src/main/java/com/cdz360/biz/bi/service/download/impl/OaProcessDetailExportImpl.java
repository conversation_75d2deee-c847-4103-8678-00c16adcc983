package com.cdz360.biz.bi.service.download.impl;

import com.cdz360.biz.bi.service.OaService;
import com.cdz360.biz.bi.service.download.IFileExport;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.IOException;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class OaProcessDetailExportImpl extends AbstractFileExport<JsonNode, ExcelPosition>
    implements IFileExport<JsonNode, ExcelPosition> {

    @Autowired
    private OaService oaService;

    @PostConstruct
    public void init() {
        this.downloadFileProxy.addProxy(DownloadFunctionType.OA_DETAIL, this);
    }

    @Override
    public Class<JsonNode> paramClazz() {
        return JsonNode.class;
    }

    @Override
    public void genFile(String context, String pos) throws IOException {
        final JsonNode param = this.convert(context);
        JsonNode procInstId = param.get("procInstId");
        JsonNode sysUid = param.get("sysUid");
        IotAssert.isNotNull(procInstId, "流程ID不能为空");
        IotAssert.isNotNull(sysUid, "操作用户ID不能为空");
        this.oaService.exportOaDetail(
            procInstId.asText(), sysUid.asText(), this.convertPos(pos));
    }
}
