package com.cdz360.biz.bi.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.common.param.BaseListParam;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaAnalysisPointParam;
import com.cdz360.biz.model.tj.kc.param.ListTjDailyChargingDurationParam;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisPointWithSiteVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class BizTjFeignHystrix
    implements FallbackFactory<BizTjFeignClient> {

    @Override
    public BizTjFeignClient apply(Throwable throwable) {
        log.error("{}", throwable.getMessage(), throwable);
        return new BizTjFeignClient() {
            @Override
            public Mono<ListResponse<TjAreaAnalysisPointWithSiteVo>> findTjAnalysisPointWithSite(
                ListTjAreaAnalysisPointParam param) {
                log.error(
                    "【服务熔断】: Service = {}, api = findTjAnalysisPointWithSite (获取投建分析划分点(带场站列表信息)), param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, JsonUtils.toJsonString(param));
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }

            @Override
            public Mono<ListResponse<TjDailyChargingDurationPo>> findTjDailyChargingDuration(
                ListTjDailyChargingDurationParam param) {
                log.error(
                    "【服务熔断】 获取日充电时长. Service = {}, api = findTjDailyChargingDuration. param = {}",
                    DcConstants.KEY_FEIGN_DC_BIZ_TJ, param);
                return Mono.just(RestUtils.serverBusy4ListResponse());
            }
        };
    }
}
