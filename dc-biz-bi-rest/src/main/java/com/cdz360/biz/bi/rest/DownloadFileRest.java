package com.cdz360.biz.bi.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.bi.service.DownloadFileService;
import com.cdz360.biz.model.download.dto.DownloadJobDto;
import com.cdz360.biz.model.download.param.DownloadFileParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import jakarta.servlet.http.HttpServletResponse;

@Tag(name = "下载文件相关接口", description = "下载文件相关接口")
@Slf4j
@RestController
public class DownloadFileRest {

    @Autowired
    private DownloadFileService downloadFileService;

    @Operation(summary = "文件生成")
    @PostMapping(value = "/bi/download/generateFile")
    public Mono<BaseResponse> generateFile(@RequestBody DownloadJobDto downloadJobDto) {
        log.info("文件生成: {}", JsonUtils.toJsonString(downloadJobDto));
        downloadFileService.generateFile(downloadJobDto);
        return Mono.just(RestUtils.success());
    }

    @Operation(summary = "打印文件生成")
    @PostMapping(value = "/bi/download/generatePrintFile")
    public Mono<BaseResponse> generatePrintFile(@RequestBody DownloadJobDto downloadJobDto) {
        log.info("打印文件生成: {}", JsonUtils.toJsonString(downloadJobDto));
        Mono<Void> generateFileMono = Mono.fromRunnable(() -> downloadFileService.generateFile(downloadJobDto));
        return generateFileMono
                .then(Mono.just(RestUtils.success()))
                .onErrorResume(error -> Mono.just(RestUtils.fail(2000, error.getMessage())));

//        return Mono.fromRunnable(() -> downloadFileService.generateFile(downloadJobDto))
//                .then(Mono.just(RestUtils.success()))
//                .onErrorResume(error -> Mono.just(RestUtils.fail(2000, error.getMessage())));
    }

    @Operation(summary = "下载文件")
    @PostMapping(value = "/bi/download/downloadFile")
    public void downloadFile(@RequestBody DownloadFileParam param, HttpServletResponse response) {
        downloadFileService.downloadFile(param, response);
    }

    @Operation(summary = "生成文件清理")
    @GetMapping(value = "/bi/download/clearFile")
    public Mono<BaseResponse> clearDownloadFile(
        @Parameter(name = "天") @RequestParam("days") Integer days) {
        return downloadFileService.clearDownloadFile(days);
    }
}
