package com.cdz360.biz.bi.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ds.trading.ro.meter.ds.BiMeterRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.BiPlugRoDs;
import com.cdz360.biz.model.bi.site.MeterReadingBi;
import com.cdz360.biz.model.common.constant.Constant;
import com.cdz360.biz.model.iot.param.SiteBiParam;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import com.cdz360.biz.model.trading.meter.param.MeterListParam;
import com.cdz360.biz.model.trading.meter.po.BiMeterPo;
import com.cdz360.biz.model.trading.meter.po.DeviceMeterPo;
import com.cdz360.biz.model.trading.meter.vo.MeterEvseVo;
import com.cdz360.biz.model.trading.meter.vo.MeterReadingTopVo;
import com.cdz360.biz.model.trading.site.po.BiPlugPo;
import com.cdz360.biz.utils.feign.iot.MeterFeignClient;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * MeterReadingBiService
 *
 * @since 9/27/2020 10:32 AM
 * <AUTHOR>
 */
@Slf4j
@Service
public class MeterReadingBiService {

    @Autowired
    private BiMeterRoDs biMeterRoDs;

    @Autowired
    private MeterFeignClient meterFeignClient;

    @Autowired
    private BiPlugRoDs biPlugRoDs;


    public List<MeterReadingBi> meterReadingBi(SiteBiParam param) {

        // 校验参数
        Optional<String> validResult = this.validParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        // 获取时段内 电表-桩绑定关系
        MeterListParam meterListParam = new MeterListParam();
        meterListParam.setSiteId(param.getSiteId());
        meterListParam.setPowerLoss(param.getPowerLoss());
        ListResponse<MeterEvseVo> meterList = meterFeignClient.getMeterList(meterListParam);
        FeignResponseValidate.check(meterList);
        List<MeterEvseVo> meterEvseVos = meterList.getData();

        // 获取时段内 电表抄表数据
        Date fromTime = new Date(param.getStartTime());
        Date toTime = new Date(param.getEndTime());
        param.setFromTime(fromTime).setToTime(toTime);

        if(Boolean.TRUE.equals(param.getPowerLoss()) && CollectionUtils.isNotEmpty(meterEvseVos)) {
            List<Long> meterIdList = meterEvseVos.stream().map(MeterEvseVo::getId).collect(Collectors.toList());
            log.info("仅关注电表列表: {}", meterIdList);
            param.setMeterIdList(meterIdList);
        }

        List<BiMeterPo> biMeterPos = biMeterRoDs.meterReadingBi(param);

        // 获取时段内 桩充电量数据
        List<BiPlugPo> biPlugPos = biPlugRoDs.selectBySiteIdWhen(param.getSiteId(),
                null,
                null,
                fromTime,
                toTime);

        List<MeterReadingBi> baseList = this.generateBaseList(param.getSampleType(), fromTime, toTime);

        if(CollectionUtils.isNotEmpty(meterEvseVos)) {

            // 得到当前绑定到电表的桩号列表
            List<String> collect = meterEvseVos.stream()
                    .map(MeterEvseVo::getDeviceMeterPoList)
                    .flatMap(e -> e.stream())
                    .map(DeviceMeterPo::getDeviceId)
                    .collect(Collectors.toList());

            // 筛选出关心的桩枪统计列表
            List<BiPlugPo> concernList = biPlugPos.stream()
                    .filter(e -> collect.contains(e.getEvseNo()))
//                    .map(e -> e.setDate(this.resetDate(param.getSampleType(), e.getDate())))
                    .collect(Collectors.toList());

            // 枪头统计分组
            Map<Date, List<BiPlugPo>> groupPlugBiMap = concernList.stream()
//                    .collect(Collectors.groupingBy(BiPlugPo::getDate));
                    .collect(Collectors.groupingBy(e -> this.resetDate(param.getSampleType(), e.getDate())));

            // 电表统计分组
            Map<Date, List<BiMeterPo>> groupMeterBiMap = biMeterPos.stream()
//                    .map(e -> e.setDate(this.resetDate(param.getSampleType(), e.getDate())))
//                    .collect(Collectors.groupingBy(BiMeterPo::getDate));
                    .collect(Collectors.groupingBy(e -> this.resetDate(param.getSampleType(), e.getDate())));

            baseList.stream().forEach(e -> {
                // 分组订单电量
                if(CollectionUtils.isNotEmpty(groupPlugBiMap.get(e.getDate()))) {
                    BigDecimal plugSum =
                            groupPlugBiMap.get(e.getDate())
                                    .stream()
                                    .map(BiPlugPo::getElectricity)
                                    .filter(Objects::nonNull)
                                    .reduce(BigDecimal.ZERO, BigDecimal::add);

                    // 枪实际充电电量
                    e.setOrderElectricity(plugSum);
                }

                // 分组抄表电量
                if(CollectionUtils.isNotEmpty(groupMeterBiMap.get(e.getDate()))) {
                    BigDecimal meterSum = groupMeterBiMap.get(e.getDate())
                            .stream()
                            .map(BiMeterPo::getElectricity)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 读表电量
                    e.setMeterReadingElectricity(meterSum);

                    // 抄表有效日
                    List<Date> validateDate = groupMeterBiMap.get(e.getDate())
                            .stream()
                            .map(BiMeterPo::getDate)
                            .collect(Collectors.toList());

                    BigDecimal plugSum = groupPlugBiMap.get(e.getDate())
                            .stream()
                            .filter(de -> validateDate.contains(de.getDate()))
                            .map(BiPlugPo::getElectricity)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    // 抄表有效日电量
                    e.setMeterOrderElectricity(plugSum);


                } else {
                    // 没有任何抄表信息
                    e.setMeterReadingElectricity(null);
                    e.setMeterOrderElectricity(null);
                }
//                e.setOrderElectricity().setMeterReadingElectricity();
            });


        }

        return baseList;
    }

    public List<MeterReadingTopVo> meterReadingTopBi(SiteBiParam param) {

        // 校验参数
        Optional<String> validResult = this.validParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        // 获取时段内 电表-桩绑定关系
        MeterListParam meterListParam = new MeterListParam();
        meterListParam.setSiteId(param.getSiteId());
        meterListParam.setPowerLoss(param.getPowerLoss());
        ListResponse<MeterEvseVo> meterList = meterFeignClient.getMeterList(meterListParam);
        FeignResponseValidate.check(meterList);
        List<MeterEvseVo> meterEvseVos = meterList.getData();

        // 获取时段内 电表抄表数据
        Date fromTime = new Date(param.getStartTime());
        Date toTime = new Date(param.getEndTime());
        param.setFromTime(fromTime).setToTime(toTime);

        if(Boolean.TRUE.equals(param.getPowerLoss()) && CollectionUtils.isNotEmpty(meterEvseVos)) {
            List<Long> meterIdList = meterEvseVos.stream().map(MeterEvseVo::getId).collect(Collectors.toList());
            log.info("仅关注电表列表: {}", meterIdList);
            param.setMeterIdList(meterIdList);
        }

        List<BiMeterPo> biMeterPos = biMeterRoDs.meterReadingBi(param);

        // 获取时段内 桩充电量数据
        List<BiPlugPo> biPlugPos = biPlugRoDs.selectBySiteIdWhen(param.getSiteId(),
                null,
                null,
                fromTime,
                toTime);

        if(CollectionUtils.isNotEmpty(meterEvseVos)) {

            List<MeterReadingTopVo> collect = meterEvseVos.stream().map(e -> {
                MeterReadingTopVo meterReadingTopVo = new MeterReadingTopVo();
                BeanUtils.copyProperties(e, meterReadingTopVo);
                return meterReadingTopVo;
            }).collect(Collectors.toList());

            // 电表抄表分组
            Map<Long, List<BiMeterPo>> biMeterMap = biMeterPos.stream()
                    .collect(Collectors.groupingBy(BiMeterPo::getMeterId));

            // 桩-枪头电量统计分组
            Map<String, List<BiPlugPo>> groupPlugBiMap = biPlugPos.stream()
                    .collect(Collectors.groupingBy(BiPlugPo::getEvseNo));

            collect.stream().forEach(e -> {

                // 订单电量
                if(CollectionUtils.isNotEmpty(biMeterMap.get(e.getId()))) {
                    BigDecimal meterSum = biMeterMap.get(e.getId())
                            .stream()
                            .map(BiMeterPo::getElectricity)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    e.setMeterReadingElectricity(meterSum);

                    List<Date> validateDate = biMeterMap.get(e.getId())
                            .stream()
                            .map(BiMeterPo::getDate)
                            .collect(Collectors.toList());

                    // 根据有效抄表时间
                    if(CollectionUtils.isNotEmpty(validateDate) && CollectionUtils.isNotEmpty(e.getDeviceMeterPoList())) {
                        BigDecimal sumMeterOrderElectricity = BigDecimal.ZERO;
                        for(DeviceMeterPo one : e.getDeviceMeterPoList()) {
                            if(CollectionUtils.isNotEmpty(groupPlugBiMap.get(one.getDeviceId()))) {
                                // 仅计入有效抄表时间对应的电量
                                sumMeterOrderElectricity = groupPlugBiMap.get(one.getDeviceId())
                                        .stream()
                                        .filter(de -> validateDate.contains(de.getDate()))
                                        .map(BiPlugPo::getElectricity)
                                        .filter(Objects::nonNull)
                                        .reduce(sumMeterOrderElectricity, BigDecimal::add);
                            }
                        }
                        // 每个桩的有效抄表时间对应的电量累计
                        e.setMeterOrderElectricity(sumMeterOrderElectricity);
                    }
                }

                // 抄表电量累计
                if(CollectionUtils.isNotEmpty(e.getDeviceMeterPoList())) {
                    BigDecimal orderSum = BigDecimal.ZERO;
                    for(DeviceMeterPo one : e.getDeviceMeterPoList()) {
                        if(CollectionUtils.isNotEmpty(groupPlugBiMap.get(one.getDeviceId()))) {
                            orderSum = groupPlugBiMap.get(one.getDeviceId())
                                    .stream()
                                    .map(BiPlugPo::getElectricity)
                                    .filter(Objects::nonNull)
                                    .reduce(orderSum, BigDecimal::add);
                        }
                    }
                    e.setOrderElectricity(orderSum);
                }
            });
            return collect;
        } else {
            return List.of();
        }
    }

    private List<MeterReadingBi> generateBaseList(SiteBiSampleType type, Date fromTime, Date toTime) {

        final Date startDate = DateUtil.getThisDate(fromTime);
        final Date stopDate = DateUtil.getThisDate(toTime);

        List<MeterReadingBi> ret =  new ArrayList<>();

        Date curDate = startDate;
        while(curDate.getTime() < stopDate.getTime()) {
            MeterReadingBi meterReadingBi = new MeterReadingBi();

            meterReadingBi.setDate(curDate)/*
                    .setOrderElectricity(BigDecimal.ZERO)
                    .setMeterReadingElectricity(BigDecimal.ZERO)*/;

            if(SiteBiSampleType.MONTH.equals(type)) {
                curDate = DateUtil.getNextMonth(curDate);
            } else {
                curDate = DateUtil.getNextDate(curDate);
            }

            ret.add(meterReadingBi);
        }
        return ret;
    }




    private Optional<String> validParam(SiteBiParam param) {
        if (StringUtils.isBlank(param.getSiteId())) {
            return Optional.of("场站Id没有提供");
        }

        if (null == param.getStartTime() || null == param.getEndTime()) {
            return Optional.of("开始结束时间没有提供");
        }

        if (param.getStartTime() >= param.getEndTime()) {
            return Optional.of("开始时间大于等于结束时间，不合理");
        }

        if (reviseResult(param).size() > Constant.MAX_SAMPLE_SIZE) {
            return Optional.of("时间跨度大于最大值（" + Constant.MAX_SAMPLE_SIZE + "），请选择正确的开始/结束时间");
        }

        return Optional.empty();
    }



    /**
     * 调整数据返回，固定长度
     *
     * @return
     */
    private List<LocalDateTime> reviseResult(SiteBiParam param) {
        param.resetTime();

        LocalDateTime start = param.getFromTime().toInstant()
                .atOffset(ZoneOffset.of("+8"))
                .toLocalDateTime();
        LocalDateTime end = param.getToTime().toInstant()
                .atOffset(ZoneOffset.of("+8"))
                .toLocalDateTime();

        List<LocalDateTime> timeList = new ArrayList<>();
        while (start.isBefore(end)) {
            timeList.add(start);
            if (param.getSampleType() == SiteBiSampleType.HOUR) {
                start = start.plusHours(1);
            } else if (param.getSampleType() == SiteBiSampleType.DAY) {
                start = start.plusDays(1);
            } else {
                start = start.plusMonths(1);
            }
        }

        log.info("time size = {}", timeList.size());
        return timeList;
    }
    /**
     * 调整时间点
     *
     * @param sampleType
     * @param time
     * @return
     */
    private Date resetDate(SiteBiSampleType sampleType, Date time) {
        // 分秒都为0
        LocalDateTime local = time.toInstant().atOffset(ZoneOffset.of("+8"))
                .toLocalDateTime().withMinute(0).withSecond(0).withNano(0);

        if (sampleType == SiteBiSampleType.DAY ||
                sampleType == SiteBiSampleType.MONTH) {
            local = local.withHour(0);
        }

        if (sampleType == SiteBiSampleType.MONTH) {
            // 当月第一天
            local = local.with(TemporalAdjusters.firstDayOfMonth());
        }

        return new Date(local.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
    }

}