package com.cdz360.biz.bi.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.bi.config.ExportFileConfig;
import com.cdz360.biz.ds.trading.ro.invoice.ds.CorpInvoiceRecordRoDs;
import com.cdz360.biz.ds.trading.ro.invoice.ds.InvoiceRecordOrderRefRoDs;
import com.cdz360.biz.ds.trading.ro.invoice.ds.InvoicedRecordContentRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.PayBillRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.InvoiceRecordRoDs;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.cus.settlement.param.ListSettlementParam;
import com.cdz360.biz.model.cus.settlement.type.SettlementStatusEnum;
import com.cdz360.biz.model.cus.settlement.vo.SettlementVo;
import com.cdz360.biz.model.invoice.type.InvoiceChannel;
import com.cdz360.biz.model.invoice.type.InvoicePlatformSource;
import com.cdz360.biz.model.invoice.type.InvoiceType;
import com.cdz360.biz.model.invoice.type.ProductType;
import com.cdz360.biz.model.invoice.vo.InvoiceApplyVo;
import com.cdz360.biz.model.oa.vo.InvoicingContentVo;
import com.cdz360.biz.model.trading.bi.dto.BiExportGroups;
import com.cdz360.biz.model.trading.invoice.dto.CorpInvoiceRecordDto;
import com.cdz360.biz.model.trading.invoice.param.ListCorpInvoiceRecordParam;
import com.cdz360.biz.model.trading.invoice.param.ListInvoiceRecordOrderRefParam;
import com.cdz360.biz.model.trading.invoice.po.InvoiceRecordOrderRefPo;
import com.cdz360.biz.model.trading.invoice.po.InvoicedRecordContentPo;
import com.cdz360.biz.model.trading.order.dto.PayBillInvoiceBi;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.utils.feign.invoice.InvoiceDataCoreFeignClient;
import com.cdz360.biz.utils.feign.invoice.InvoiceFeignClient;
import com.cdz360.biz.utils.feign.settlement.SettlementFeignClient;
import com.chargerlinkcar.framework.common.constant.OrderStatus;
import com.chargerlinkcar.framework.common.domain.invoice.ExportInvoicedRecord;
import com.chargerlinkcar.framework.common.domain.invoice.InvoicedRecordVo;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoicedTemplateSalDTO;
import com.chargerlinkcar.framework.common.domain.invoice.dto.InvoicedTemplateSalDetailDTO;
import com.chargerlinkcar.framework.common.domain.invoice.nsr.NSRInvoice;
import com.chargerlinkcar.framework.common.domain.invoice.nsr.NSRInvoiceDetail;
import com.chargerlinkcar.framework.common.domain.invoice.nsr.NSRInvoiceResult;
import com.chargerlinkcar.framework.common.domain.invoice.param.ListInvoicedRecordParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.ExportCorpInvoicedRecord;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedSalTempRefVo;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedTemplateSalDetailVo;
import com.chargerlinkcar.framework.common.domain.order.CorpInvoiceOrderExportVo;
import com.chargerlinkcar.framework.common.domain.settlement.SettlementExportVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrder;
import com.chargerlinkcar.framework.common.utils.ExcelUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class InvoiceService {

    private static final DateTimeFormatter NO_FORMAT = DateTimeFormatter.ofPattern("yyMMdd");

    @Autowired
    private ExportFileConfig exportFileConfig;

    @Value("${excel.tmp.file:files/NSR_INVOICE_TEMPLATE.xlsx}")
    private String NSF_EXCEL_TEMP_FILE;

//    @Value("${excel.dir:/tmp/excel}")
//    private String TEMP_DIR;

    private static final SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private CorpInvoiceRecordRoDs corpInvoiceRecordRoDs;

    @Autowired
    private PayBillRoDs payBillRoDs;

    @Autowired
    private ChargerOrderRoDs chargerOrderRoDs;

    @Autowired
    private SettlementFeignClient settlementFeignClient;

    @Autowired
    private InvoiceFeignClient invoiceFeignClient;

    @Autowired
    private InvoiceRecordOrderRefRoDs invoiceRecordOrderRefRoDs;

    @Autowired
    private InvoiceDataCoreFeignClient invoiceDataCoreFeignClient;

    @Autowired
    private InvoiceRecordRoDs invoiceRecordRoDs;

    @Autowired
    private InvoicedRecordContentRoDs invoicedRecordContentRoDs;

    //    @Async
    public void exportCorpInvoiceOrder(ExcelPosition position, String applyNo) throws IOException {
        CorpInvoiceRecordDto record = corpInvoiceRecordRoDs.getRecordByApplyNo(applyNo, false);
        if (null == record) {
            log.info("企业开票申请单号无效: applyNo = {}", applyNo);
            return;
        }

        switch (record.getInvoiceWay()) {
            case POST_CHARGER:
                this.postCharger(position, applyNo);
                break;
            case PRE_PAY:
                this.prePay(position, applyNo);
                break;
            case POST_SETTLEMENT:
                this.postSettlement(position, applyNo);
                break;
            default:
                log.error("企业开票不支持开票方式: invoiceWay = {}", record.getInvoiceWay());
        }
    }

    private void postCharger(ExcelPosition position, String applyNo) throws IOException {
        log.info("充电后开票订单导出: {}, applyNo = {}", JsonUtils.toJsonString(position), applyNo);
//        try {
        ListChargeOrderParam param = new ListChargeOrderParam();
        param.setApplyNo(applyNo)
            .setInCorpInvoice(true)
            .setStatusList(List.of(OrderStatus.ORDER_STATUS_RECEIVE_MONEY)); // 已结算的充电订单

        ExcelUtil.builder(exportFileConfig.getExcelDir(), position,
                BiExportGroups.CORP_INVOICE_ORDER.getName())
            .addHeader(CorpInvoiceOrderExportVo.class)
            .loopAppendData((start, size) -> {
                param.setStart((long) ((start - 1) * size))
                    .setSize(size);
                param.setTotal(Boolean.FALSE);

                ListResponse<ChargerOrder> all = chargerOrderRoDs.listChargeOrder(param);
                return new ArrayList<>(all.getData());
            }, list -> new ArrayList<>(list.stream()
                .map(i -> (ChargerOrder) i)
                .map(order -> {
                    CorpInvoiceOrderExportVo vo = new CorpInvoiceOrderExportVo();
                    BeanUtils.copyProperties(order, vo);

                    // 开票金额调整
                    vo.setInvoiceAmount(order.getPrincipalAmount());

                    // 订单状态
                    if (order.getStatus() == OrderStatus.ORDER_STATUS_RECEIVE_MONEY) {
                        vo.setStatus("已结算");
                    } else {
                        vo.setStatus("--");
                    }

                    Long chargeStartTime = order.getChargeStartTime();
                    Date startTime = new Date(chargeStartTime * 1000);
                    Long chargeEndTime = order.getChargeEndTime();
                    Date endTime = new Date(chargeEndTime * 1000);
                    vo.setChargeTime(format.format(startTime) + " - " + format.format(endTime));
                    return vo;
                })
                .collect(Collectors.toList())))
            .write2File();
        log.info("导出excel完成: {}", JsonUtils.toJsonString(position));
//        } catch (Exception e) {
//            log.error("导出excel异常: {}, err = {}",
//                    JsonUtils.toJsonString(position), e.getMessage(), e);
//        }
    }

    private void prePay(ExcelPosition position, String applyNo) throws IOException {
        log.info("充值预开票订单导出: {}, applyNo = {}", JsonUtils.toJsonString(position), applyNo);
//        try {
        PayBillParam param = new PayBillParam();
        param.setApplyNo(applyNo)
            .setInCorpInvoice(true);

        ExcelUtil.builder(exportFileConfig.getExcelDir(), position,
                BiExportGroups.CORP_INVOICE_ORDER.getName())
            .addHeader(PayBillInvoiceBi.class)
            .loopAppendData((start, size) -> {
                param.setStart((long) (start - 1) * size)
                    .setSize(size);
//                        param.setTotal(Boolean.FALSE);

                ListResponse<PayBillInvoiceBi> all = this.payBillRoDs.invoiceBi(param);
                return new ArrayList<>(all.getData());
            }, null)
            .write2File();
        log.info("导出excel完成: {}", JsonUtils.toJsonString(position));
//        } catch (Exception e) {
//            log.error("导出excel异常: {}, err = {}",
//                    JsonUtils.toJsonString(position), e.getMessage(), e);
//        }
    }

    private void postSettlement(ExcelPosition position, String applyNo) throws IOException {
        log.info("账单后开票订单导出: {}, applyNo = {}", JsonUtils.toJsonString(position), applyNo);
//        try {
        ListSettlementParam param = new ListSettlementParam();
        param.setApplyNo(applyNo)
            .setInCorpInvoice(true)
            .setStatusList(List.of(SettlementStatusEnum.PAID));

        if (StringUtils.isNotBlank(param.getApplyNo())) {
            // -> dataCore: 获取开票关联的订单列表
            ListInvoiceRecordOrderRefParam refParam = new ListInvoiceRecordOrderRefParam();
            refParam.setApplyNo(param.getApplyNo())
                .setSk(param.getSk());
            List<InvoiceRecordOrderRefPo> result = invoiceRecordOrderRefRoDs.findList(refParam);

            // 账单列表
            param.setBillNoList(result.stream()
                .map(InvoiceRecordOrderRefPo::getOrderNo).collect(Collectors.toList()));
        }

        ExcelUtil.builder(exportFileConfig.getExcelDir(), position,
                BiExportGroups.CORP_INVOICE_ORDER.getName())
            .addHeader(SettlementExportVo.class)
            .loopAppendData((start, size) -> {
                param.setStart((long) ((start - 1) * size))
                    .setSize(size);
                param.setTotal(Boolean.FALSE);

                ListResponse<SettlementVo> result = settlementFeignClient.findSettlementList(param)
                    .block(Duration.ofSeconds(50L));
                return new ArrayList<>(null == result ? List.of() : result.getData());
            }, list -> new ArrayList<>(list.stream()
                .map(i -> (SettlementVo) i)
                .map(this::map2SettlementExportVo)
                .collect(Collectors.toList())))
            .write2File();
        log.info("导出excel完成: {}", JsonUtils.toJsonString(position));
//        } catch (Exception e) {
//            log.error("导出excel异常: {}, err = {}",
//                    JsonUtils.toJsonString(position), e.getMessage(), e);
//        }
    }

    private SettlementExportVo map2SettlementExportVo(SettlementVo settlementVo) {
        SettlementExportVo vo = new SettlementExportVo();
        vo.setBillNo(settlementVo.getBillNo())
            .setBillName(settlementVo.getBillName())
            .setSubSettlementType(settlementVo.getSubSettlementType().getDesc())
            .setInvoicedAmount(settlementVo.getInvoicedAmount())
            .setSettlementTotalFee(settlementVo.getSettlementTotalFee())
            .setSettlementElecFee(settlementVo.getSettlementElecFee())
            .setSettlementServFee(settlementVo.getSettlementServFee())
            .setCreateTime(settlementVo.getCreateTime());

        switch (settlementVo.getBillStatus()) {
            case INIT:
                vo.setBillStatus("未结算");
                break;
            case PAID:
                vo.setBillStatus("已结算");
                break;
            case CANCEL:
                vo.setBillStatus("作废");
                break;
            default:
                vo.setBillStatus("--");
        }

        return vo;
    }

    //    @Async
    public void exportInvoicedRecord(ExcelPosition position, ListInvoicedRecordParam param)
        throws IOException {
        log.info("C端用户开票记录导出: {}, param = {}", JsonUtils.toJsonString(position),
            JsonUtils.toJsonString(param));
//        try {
        ExcelUtil.builder(exportFileConfig.getExcelDir(), position,
                BiExportGroups.CUS_INVOICE_RECORD.getName())
            .addHeader(ExportInvoicedRecord.class)
            .loopAppendData((start, size) -> {
                param.setStart((long) ((start - 1) * size))
                    .setSize(size);
                param.setTotal(Boolean.FALSE);

                ListResponse<InvoicedRecordVo> result = invoiceFeignClient.getAllInvoicedRecords(
                        param)
                    .block(Duration.ofSeconds(50L));
                return new ArrayList<>(null == result ? List.of() : result.getData());
            }, list -> new ArrayList<>(list.stream()
                .map(i -> (InvoicedRecordVo) i)
                .map(vo -> {
                    ExportInvoicedRecord record = new ExportInvoicedRecord();
                    BeanUtils.copyProperties(vo, record);

                    // invoiceType
                    switch (vo.getInvoiceType()) {
                        case PER_COMMON:
                            record.setInvoiceType("个人普票");
                            break;
                        case ENTER_COMMON:
                            record.setInvoiceType("企业普票");
                            break;
                        case ENTER_PROFESSION:
                            record.setInvoiceType("企业专票");
                            break;
                        default:
                            record.setInvoiceType("未知");
                    }

                    // createdDate / reviewedDate
                    if (null != vo.getCreatedDate()) {
                        record.setCreatedDate(Date.from(vo.getCreatedDate().toInstant()));
                    }
                    if (null != vo.getReviewedDate()) {
                        record.setReviewedDate(Date.from(vo.getReviewedDate().toInstant()));
                    }

                    // invoicedStatus
                    switch (vo.getInvoicedStatus()) {
                        case SUBMITTED:
                            record.setInvoicedStatus("审核中");
                            break;
                        case REVIEWED:
                            record.setInvoicedStatus("开票中");
                            break;
                        case AUDIT_FAILED:
                            record.setInvoicedStatus("审核未通过");
                            break;
                        case INVOICING_FAIL:
                            record.setInvoicedStatus("开票未通过");
                            break;
                        case COMPLETED:
                            record.setInvoicedStatus("已开具");
                            break;
                        case NOT_SUBMITTED:
                            record.setInvoicedStatus("草稿");
                            break;
                        case RED_DASHED:
                        case INVALID:
                            record.setInvoicedStatus("已作废");
                            break;
                        default:
                            record.setInvoicedStatus("未知");
                    }

                    if (vo.getPlatformSource() == InvoicePlatformSource.LANGXIN) {
                        record.setPlatformSource("朗新");
                    } else if (vo.getPlatformSource() == InvoicePlatformSource.HENGDA) {
                        record.setPlatformSource("恒大(星络通)");
                    } else if (vo.getPlatformSource() == InvoicePlatformSource.BAIDU) {
                        record.setPlatformSource("百度");
                    } else {
                        record.setPlatformSource("任我充");
                    }

                    // address
                    record.setAddress(
                        (StringUtils.isBlank(vo.getReceiverProvince()) ? "--"
                            : vo.getReceiverProvince()) +
                            (StringUtils.isBlank(vo.getReceiverCity()) ? "--"
                                : vo.getReceiverCity()) +
                            (StringUtils.isBlank(vo.getReceiverArea()) ? "--"
                                : vo.getReceiverArea()));

                    return record;
                })
                .collect(Collectors.toList())))
            .write2File();
        log.info("导出excel完成: {}", JsonUtils.toJsonString(position));
//        } catch (Exception e) {
//            log.error("导出excel异常: {}, err = {}",
//                    JsonUtils.toJsonString(position), e.getMessage(), e);
//        }
    }

    /**
     * 企业开票申请导出
     *
     * @param position
     * @param param
     */
//    @Async
    public void exportCorpInvoiceRecord(ExcelPosition position, ListCorpInvoiceRecordParam param)
        throws IOException {
        log.info("企业开票申请导出: {}, param = {}", JsonUtils.toJsonString(position),
            JsonUtils.toJsonString(param));
//        try {
        ExcelUtil.builder(exportFileConfig.getExcelDir(), position,
                BiExportGroups.CUS_INVOICE_RECORD.getName())
            .addHeader(ExportCorpInvoicedRecord.class)
            .loopAppendData((start, size) -> {
                param.setStart((long) ((start - 1) * size))
                    .setSize(size);
                param.setTotal(Boolean.FALSE);

                ListResponse<CorpInvoiceRecordDto> result = invoiceDataCoreFeignClient.findCorpInvoiceRecordList(
                        param)
                    .block(Duration.ofSeconds(50L));
                return new ArrayList<>(null == result ? List.of() : result.getData());
            }, list -> new ArrayList<>(list.stream()
                .map(i -> (CorpInvoiceRecordDto) i)
                .map(vo -> {
                    ExportCorpInvoicedRecord record = new ExportCorpInvoicedRecord();
                    BeanUtils.copyProperties(vo, record);
                    //流程状态
                    switch (vo.getStatus()) {
                        case NOT_SUBMITTED:
                            record.setStatus("草稿");
                            break;
                        case SUBMITTED:
                            record.setStatus("审核中");
                            break;
                        case REVIEWED:
                            record.setStatus("开票中");
                            break;
                        case AUDIT_FAILED:
                            record.setStatus("审核未通过");
                            break;
                        case INVOICING_FAIL:
                            record.setStatus("开票未通过");
                            break;
                        case COMPLETED:
                            record.setStatus("已开具");
                            break;
                        case INVALID:
                            record.setStatus("已作废");
                            break;
                        default:
                            record.setStatus("未知");
                    }
                    //开票方式
                    switch (vo.getInvoiceWay()) {
                        case PRE_PAY:
                            record.setInvoiceWay("充值预开票");
                            break;
                        case POST_CHARGER:
                            record.setInvoiceWay("充电后开票");
                            break;
                        case POST_SETTLEMENT:
                            record.setInvoiceWay("后付费账单开票");
                            break;
                        default:
                            record.setInvoiceWay("未知");
                    }
                    //开具方式
                    switch (vo.getChannel()) {
                        case AUTO:
                            record.setChannel("系统自动开具");
                            break;
                        case MANUAL:
                            record.setChannel("后台手动开具");
                            break;
                        default:
                            record.setChannel("未知");
                    }
                    if (vo.getReturnFlag() == null) {
                        record.setChannel("未知");
                    } else {
                        if (vo.getReturnFlag() == 0) {
                            record.setReturnFlag("未回款");
                        } else if (vo.getReturnFlag() == 1) {
                            record.setReturnFlag("已回款");
                        } else {
                            record.setChannel("未知");
                        }
                    }

                    if (CollectionUtils.isNotEmpty(vo.getReturnPlanVoList())) {
                        vo.getReturnPlanVoList().forEach(e -> {
                            String planTitle = e.getPlanTitle();
                            BigDecimal planMoney = e.getPlanMoney();
                            Date planTime = e.getPlanTime();
                            if (planTitle.contains("一")) {
                                record.setFirstReturnMoney(planMoney);
                                record.setFirstReturnTime(planTime);
                            } else if (planTitle.contains("二")) {
                                record.setSecondReturnMoney(planMoney);
                                record.setSecondReturnTime(planTime);
                            } else if (planTitle.contains("三")) {
                                record.setThirdReturnMoney(planMoney);
                                record.setThirdReturnTime(planTime);
                            }
                        });
                    }
                    return record;
                })
                .collect(Collectors.toList())))
            .write2File();
        log.info("导出excel完成: {}", JsonUtils.toJsonString(position));
//        } catch (Exception e) {
//            log.error("导出excel异常: {}, err = {}",
//                    JsonUtils.toJsonString(position), e.getMessage(), e);
//        }
    }

    public void exportCusInvoiceNSR(ExcelPosition position, ListInvoicedRecordParam param)
        throws IOException {

        // 发票明细行信息
        List<NSRInvoiceDetail> detailList = new ArrayList<>();
        List<Long> templateSalIdList = new ArrayList<>();

        Mono<List<InvoicedRecordVo>> recordMono = invoiceFeignClient.getAllInvoicedRecords(param)
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData);
        recordMono.subscribe();
        List<InvoicedRecordVo> records = recordMono.block(Duration.ofSeconds(50L));

        List<Long> idsToAdd = records.stream()
            .flatMap(record -> {
                // 获取 record 的 TempSalId
                Stream<Long> recordId = Optional.ofNullable(record.getTempSalId())
                    .filter(tempSalId -> tempSalId != 0)
                    .map(Stream::of)
                    .orElseGet(Stream::empty);
                return recordId;
            })
            .distinct()
            .collect(Collectors.toList());
        templateSalIdList.addAll(idsToAdd);

        Mono<List<InvoicedTemplateSalDTO>> templateSalInfoMono = Mono.defer(() -> {
            if (templateSalIdList.isEmpty()) {
                return Mono.empty();
            } else {
                return invoiceFeignClient.getCusInvoicedTempSalByIds(templateSalIdList)
                    .doOnNext(FeignResponseValidate::check)
                    .map(ListResponse::getData);
            }
        });
        List<InvoicedTemplateSalDTO> templateSalInfo = templateSalInfoMono
            .block(Duration.ofSeconds(50L));
        List<NSRInvoice> dataList = Mono.zip(Mono.just(records)
                    .flatMap(e -> {
                        final List<Long> ids = e
                            .stream()
                            .map(InvoicedRecordVo::getProductTemp)
                            .filter(Objects::nonNull)
                            .map(InvoicedSalTempRefVo::getId)
                            .filter(Objects::nonNull)
                            .distinct()
                            .collect(Collectors.toList());
                        ids.addAll(templateSalIdList);
                        return invoiceFeignClient.getInvoicedTemplateSalDetailsByIByRefIds(ids)
                            .doOnNext(FeignResponseValidate::check)
                            .map(ListResponse::getData)
                            .map(details -> new CombinedResult(e, details));
                    }),
                templateSalInfo == null ? Mono.empty() : Mono.just(templateSalInfo)
            )
            .map(tuple -> {
                List<InvoicedRecordVo> list = tuple.getT1().getRecords();

                // 商品行信息传输信息 总列表
                final List<InvoicedTemplateSalDetailDTO> tempSalList = tuple.getT1()
                    .getSalDetails();

                List<InvoicedTemplateSalDTO> salList = tuple.getT2();

                // 商品行信息传输信息 分组
                final Map<Long, List<InvoicedTemplateSalDetailDTO>> tempSalMap = tempSalList.stream()
                    .collect(Collectors.groupingBy(InvoicedTemplateSalDetailDTO::getRefId));

                List<NSRInvoice> nsrInvoiceList = new ArrayList<>();

                list.forEach(x -> {
                    // 判断开票申请是否启用
                    if (ObjectUtils.isEmpty(x) || (x.getInvoicedEnabled() != null
                        && !x.getInvoicedEnabled())) {
                        log.error("开票申请为空或未启用,record = {}", x);
                        return;
                    }

                    List<InvoicedTemplateSalDetailVo> detailVoList;
                    InvoicedTemplateSalDTO sal = salList.stream()
                        .filter(salDTO -> Objects.equals(salDTO.getId(),
                            x.getTempSalId()))
                        .findFirst()
                        .orElse(null);
                    IotAssert.isNotNull(sal, "发票主体无效");

                    if (CollectionUtils.isEmpty(sal.getTempRefVoList())) {
                        throw new DcArgumentException("发票主体没有设置模板");
                    }
                    // 手动开具还是自动开具
                    // 新版本不校验有没有移动端开票模版，只校验是不是手动开票
                    if (!sal.getChannel().equals(InvoiceChannel.SEMI)) {
                        log.error("非半自动的发票主体，无法导出,recordId = {}",
                            x.getId());
                        return;
                    }

                    Optional<InvoicedSalTempRefVo> temp = sal.getTempRefVoList().stream()
                        .filter(t ->
                            t.getEnable()).findFirst();
                    if (temp.isEmpty()) {
                        throw new DcArgumentException("发票主体没有设置开票模板");
                    }

                    detailVoList = temp.get().getDetailVoList();
                    if (CollectionUtils.isEmpty(detailVoList)) {
                        throw new DcArgumentException("开票模板商品行没有配置");
                    }

                    NSRInvoice result = new NSRInvoice();
                    if (StringUtils.isNotEmpty(x.getInvoiceDesc())) {
                        result.setRemark(x.getInvoiceDesc().substring(0,
                            Math.min(x.getInvoiceDesc().length(), 24)));
                    }

                    if (StringUtils.isBlank(x.getProcInstId())) {
                        // 处理审批流之外的逻辑，即c端用户申请的发票
                        final Map<ProductType, InvoicingContentVo> detailMap = this.getDetailMap(x);
                        if (detailMap != null && !detailMap.isEmpty()) {
                            // 重选开票模板，从顶级商户开票主体信息搜索出模板
//                            final Optional<InvoicedSalTempRefVo> newTempRef = sal.getTempRefVoList()
//                                .stream()
//                                .filter(e -> x.getProductTemp().getId().equals(e.getId()))
//                                .findFirst();
//
//                            final List<InvoicedTemplateSalDetailVo> newDetailList = newTempRef.map(
//                                    InvoicedSalTempRefDTO::getDetailVoList)
//                                .orElse(detailVoList);
                            // 从接口获取开票模板
                            final List<InvoicedTemplateSalDetailDTO> salDetailList = tempSalMap.get(
                                x.getProductTemp().getId());

                            final List<InvoicedTemplateSalDetailVo> newDetailList =
                                Optional.ofNullable(salDetailList)
                                    .map(e -> e.stream().map(dt -> {
                                        InvoicedTemplateSalDetailVo dtr =
                                            new InvoicedTemplateSalDetailVo();
                                        BeanUtils.copyProperties(dt, dtr);
                                        return dtr;
                                    }).collect(Collectors.toList()))
                                    .orElse(detailVoList);
                            detailList.addAll(detailList(newDetailList, x, detailMap));
                        } else {
                            // 发票明细
                            detailList.addAll(detailList(detailVoList, x));
                        }

                    } else {
                        // 审批流产生开票记录，直接用content表里的进行填充
                        final List<InvoicingContentVo> contentList = this.getContents(x);
                        if (CollectionUtils.isEmpty(contentList)) {
                            log.error("content表里没有该recordId对应的开票内容, recordId = {}",
                                x.getId());
                            return;
                        }

                        List<InvoicingContentVo> amountZeroList = contentList.stream()
                            .filter(
                                content -> content.getFixAmount().compareTo(BigDecimal.ZERO) == 0)
                            .collect(Collectors.toList());
                        if (amountZeroList.size() == contentList.size()) {
                            throw new DcArgumentException("审批流产生的开票内容异常，开票金额全是0");
                        }

                        // 从接口获取开票模板
                        final List<InvoicedTemplateSalDetailDTO> salDetailList = tempSalMap.get(
                            x.getProductTemp().getId());
                        Map<String, String> salCodeAndProductCodeMap = salDetailList.stream()
                            .collect(Collectors.toMap(InvoicedTemplateSalDetailDTO::getCode,
                                InvoicedTemplateSalDetailDTO::getProductCode));
                        if (salCodeAndProductCodeMap.isEmpty()) {
                            log.error("开票申请对应的商品行模版为空，recordId = {}, tempSalId = {}",
                                x.getId(), x.getTempSalId());
                        }
                        for (final InvoicingContentVo contentVo : contentList) {
                            if (contentVo.getFixAmount().compareTo(BigDecimal.ZERO) == 0) {
                                // 金额是0，这条过滤
                                continue;
                            }
                            detailList.add(new NSRInvoiceDetail()
                                .setNo(
                                    "NSR_" + x.getId())
                                .setName(contentVo.getProductName())
                                .setTaxNo(salCodeAndProductCodeMap.get(contentVo.getCode()))
                                .setGgModel(contentVo.getSpec())
                                .setUnit(contentVo.getUnit())
                                .setQuantity(contentVo.getNum())
                                .setUnitPrice(contentVo.getPrice())
                                .setAmount(contentVo.getFixAmount())
                                .setTaxRate(
                                    DecimalUtils.divide100(contentVo.getTaxRate().intValue())
                                        .setScale(2, RoundingMode.HALF_DOWN)));
                        }
                    }

                    result
                        .setNo("NSR_" + x.getId()) // 全票导入流水号
                        .setType(InvoiceType.ENTER_PROFESSION.equals(x.getInvoiceType()) ?
                            "增值税专用发票" : "普通发票")
//                        .setSpecialBizType()
                        .setContainTax("是")
//                        .setNaturalFlag()
                        .setGpfName(x.getInvoiceName())
                        .setGpfNsrFlag(
                            null != x.getInvoiceTin() ? x.getInvoiceTin().toUpperCase() : null)
                        .setGpfAddress(x.getInvoiceAddress())
                        .setGpfPhone(x.getUserPhone())
                        .setGpfBank(x.getInvoiceBank())
                        .setGpfBankAccount(x.getInvoiceAccount())
//                        .setRemark(x.getInvoiceDesc())
                        .setGpfEmail(x.getEmail())
                        .setSkr(sal.getAccepter())
                        .setFhr(sal.getChecker());
                    nsrInvoiceList.add(result);
                });
                return nsrInvoiceList;
            })
            .block(Duration.ofSeconds(50L));

        ExcelUtil.builder(new ClassPathResource(NSF_EXCEL_TEMP_FILE).getInputStream(),
                exportFileConfig.getExcelDir(), position)
            .startRowNum(3) // 索引从0开始
            .appendDataList(dataList, NSRInvoice.class)
            .switchSheet("2-发票明细信息", 3) // 名称是由实际EXCEL得来
            .appendDataList(detailList, NSRInvoiceDetail.class)
            .write2File();
    }

    /**
     * 获取开票内容对应表(按照productType进行分类相加）
     *
     * @param record
     * @return
     */
    private Map<ProductType, InvoicingContentVo> getDetailMap(InvoicedRecordVo record) {

        // 企业开票内容会拆分为多个发票，因此目前仅在t_invoiced_record_content里有多张发票所需的记录
//        if (StringUtils.isNotBlank(record.getApplyNo())) {
//            // 企业申请
//            final CorpInvoiceRecordDto corpInvoiceRecord =
//                corpInvoiceRecordRoDs.getRecordByApplyNo(record.getApplyNo(), false);
//            if (CollectionUtils.isNotEmpty(corpInvoiceRecord.getInvoicingContent())) {
//                final Map<ProductType, InvoicingContentVo> collect = corpInvoiceRecord.getInvoicingContent()
//                    .stream()
//                    .filter(e -> Objects.nonNull(e) && e.getProductType() != null)
//                    .map(e -> (InvoicingContentVo) e)
//                    .collect(
//                        Collectors.toMap(InvoicingContentVo::getProductType,
//                            o -> o,
//                            (o, n) -> n));
//                return collect;
//            }
//        } else {
//            final List<InvoicedRecordContentPo> contentPos =
//                invoicedRecordContentRoDs.getByInvoiceId(record.getId());
//            if (CollectionUtils.isNotEmpty(contentPos)) {
//                return contentPos.stream()
//                    .filter(e -> Objects.nonNull(e) && e.getProductType() != null)
//                    .map(e -> {
//                        InvoicingContentVo ic = new InvoicingContentVo();
//                        BeanUtils.copyProperties(e, ic);
//                        return ic;
//                    })
//                    .collect(
//                        Collectors.toMap(InvoicingContentVo::getProductType,
//                            o -> o,
//                            (o, n) -> n));
//            }
//        }
        final List<InvoicedRecordContentPo> contentPos =
            invoicedRecordContentRoDs.getByInvoiceId(record.getId(), true);
        if (CollectionUtils.isNotEmpty(contentPos)) {
            return contentPos.stream()
                .filter(e -> Objects.nonNull(e) && e.getProductType() != null)
                .map(e -> {
                    InvoicingContentVo ic = new InvoicingContentVo();
                    BeanUtils.copyProperties(e, ic);
                    return ic;
                })
                .collect(
                    Collectors.toMap(InvoicingContentVo::getProductType,
                        o -> o,
                        (o, n) -> n));
        }
        return null;
    }

    /**
     * 获取开票内容对应表
     *
     * @param record
     * @return
     */
    private List<InvoicingContentVo> getContents(InvoicedRecordVo record) {
        final List<InvoicedRecordContentPo> contentPos =
            invoicedRecordContentRoDs.getByInvoiceId(record.getId(), true);
        if (CollectionUtils.isNotEmpty(contentPos)) {
            return contentPos.stream()
                .filter(e -> Objects.nonNull(e) && e.getProductType() != null)
                .map(e -> {
                    InvoicingContentVo ic = new InvoicingContentVo();
                    BeanUtils.copyProperties(e, ic);
                    return ic;
                })
                .collect(
                    Collectors.toList());
        }
        return null;
    }

    /**
     * 重新装饰开票内容
     *
     * @return
     */
    private static List<NSRInvoiceDetail> detailList(
        List<InvoicedTemplateSalDetailVo> detailVoList,
        InvoicedRecordVo record,
        Map<ProductType, InvoicingContentVo> detailMap) {

        final InvoicingContentVo servContent = detailMap.get(ProductType.SERV_ACTUAL_FEE);
        final InvoicingContentVo elecContent = detailMap.get(ProductType.ELEC_ACTUAL_FEE);
        final InvoicingContentVo parkContent = detailMap.get(ProductType.PARK_OUT_FEE);

        // 电费 服务费 停充超时费用分离
        BigDecimal servFee = servContent == null ? BigDecimal.ZERO : servContent.getFixAmount();
        BigDecimal elecFee = elecContent == null ? BigDecimal.ZERO : elecContent.getFixAmount();
        BigDecimal parkFee = parkContent == null ? BigDecimal.ZERO : parkContent.getFixAmount();

        // 服务费优先(费率低)
        BigDecimal total = record.getInvoiceAmount();
        if (DecimalUtils.isZero(servFee) &&
            DecimalUtils.isZero(elecFee) &&
            DecimalUtils.isZero(parkFee)) {
            servFee = total;
        }

        Optional<InvoicedTemplateSalDetailVo> serv = detailVoList.stream()
            .filter(d -> ProductType.SERV_ACTUAL_FEE.equals(d.getProductType())).findFirst();
        Optional<InvoicedTemplateSalDetailVo> elec = detailVoList.stream()
            .filter(d -> ProductType.ELEC_ACTUAL_FEE.equals(d.getProductType())).findFirst();
        Optional<InvoicedTemplateSalDetailVo> park = detailVoList.stream()
            .filter(d -> ProductType.PARK_OUT_FEE.equals(d.getProductType())).findFirst();

        if (serv.isEmpty() && elec.isEmpty() && park.isEmpty()) {
            log.warn("商品行配置: {}", JsonUtils.toJsonString(detailVoList));
            throw new DcArgumentException("商品行配置不支持");
        }

        List<NSRInvoiceDetail> detailList = new ArrayList<>();
        if (DecimalUtils.gtZero(parkFee)) {
            if (park.isPresent() && InvoiceService.contentIsValid(parkContent)) {
                InvoicedTemplateSalDetailVo sph = park.get();
                detailList.add(new NSRInvoiceDetail()
                    .setNo("NSR_" + record.getId())
                    .setName(sph.getProductName())
                    .setTaxNo(sph.getProductCode())
                    .setGgModel(sph.getSpec())
                    .setUnit(sph.getUnit())
                    .setQuantity(parkContent.getNum())
                    .setUnitPrice(parkContent.getFixAmount()
                        .divide(parkContent.getNum(),
                            13,
                            RoundingMode.HALF_UP))
                    .setAmount(parkContent.getFixAmount())
                    .setTaxRate(DecimalUtils.divide100(sph.getTaxRate())
                        .setScale(2, RoundingMode.HALF_DOWN)));
            } else {
                servFee = servFee.add(parkFee);
            }
        }

        if (DecimalUtils.gtZero(elecFee)) {
            if (elec.isPresent() && InvoiceService.contentIsValid(elecContent)) {
                InvoicedTemplateSalDetailVo sph = elec.get();
                detailList.add(new NSRInvoiceDetail()
                    .setNo("NSR_" + record.getId())
                    .setName(sph.getProductName())
                    .setTaxNo(sph.getProductCode())
                    .setGgModel(sph.getSpec())
                    .setUnit(sph.getUnit())
                    .setQuantity(elecContent.getNum())
                    .setUnitPrice(elecContent.getFixAmount()
                        .divide(elecContent.getNum(),
                            13,
                            RoundingMode.HALF_UP))
                    .setAmount(elecContent.getFixAmount())
                    .setTaxRate(DecimalUtils.divide100(sph.getTaxRate())
                        .setScale(2, RoundingMode.HALF_DOWN)));
            } else {
                servFee = servFee.add(elecFee);
            }
        }

        if (DecimalUtils.gtZero(servFee) &&
            serv.isPresent() &&
            InvoiceService.contentIsValid(servContent)) {
            InvoicedTemplateSalDetailVo sph = serv.get();
            detailList.add(new NSRInvoiceDetail()
                .setNo(
                    "NSR_" + record.getId())
                .setName(sph.getProductName())
                .setTaxNo(sph.getProductCode())
                .setGgModel(sph.getSpec())
                .setUnit(sph.getUnit())
                .setQuantity(servContent.getNum())
                .setUnitPrice(servContent.getFixAmount()
                    .divide(servContent.getNum(),
                        13,
                        RoundingMode.HALF_UP))
                .setAmount(servContent.getFixAmount())
                .setTaxRate(DecimalUtils.divide100(sph.getTaxRate())
                    .setScale(2, RoundingMode.HALF_DOWN)));
        }

        return detailList;
    }

    private static boolean contentIsValid(InvoicingContentVo content) {
        return content != null &&
            content.getNum() != null &&
            content.getFixAmount() != null &&
            content.getTaxRate() != null &&
            content.getNum().compareTo(BigDecimal.ZERO) > 0;
    }

    private static List<NSRInvoiceDetail> detailList(
        List<InvoicedTemplateSalDetailVo> detailVoList, InvoicedRecordVo record) {

        // 电费 服务费 停充超时费用分离
        BigDecimal servFee = record.getServActualFee();
        BigDecimal elecFee = record.getElecActualFee();
        BigDecimal parkFee = record.getParkActualFee();

        // 服务费优先(费率低)
        BigDecimal total = record.getInvoiceAmount();
        if (DecimalUtils.isZero(servFee) &&
            DecimalUtils.isZero(elecFee) &&
            DecimalUtils.isZero(parkFee)) {
            servFee = total;
        }

        Optional<InvoicedTemplateSalDetailVo> serv = detailVoList.stream()
            .filter(d -> ProductType.SERV_ACTUAL_FEE.equals(d.getProductType())).findFirst();
        Optional<InvoicedTemplateSalDetailVo> elec = detailVoList.stream()
            .filter(d -> ProductType.ELEC_ACTUAL_FEE.equals(d.getProductType())).findFirst();
        Optional<InvoicedTemplateSalDetailVo> park = detailVoList.stream()
            .filter(d -> ProductType.PARK_OUT_FEE.equals(d.getProductType())).findFirst();

        if (serv.isEmpty() && elec.isEmpty() && park.isEmpty()) {
            log.warn("商品行配置: {}", JsonUtils.toJsonString(detailVoList));
            throw new DcArgumentException("商品行配置不支持");
        }

        List<NSRInvoiceDetail> detailList = new ArrayList<>();
        if (DecimalUtils.gtZero(parkFee)) {
            if (park.isPresent()) {
                InvoicedTemplateSalDetailVo sph = park.get();
                detailList.add(new NSRInvoiceDetail()
                    .setNo("NSR_" + record.getId())
                    .setName(sph.getProductName())
                    .setTaxNo(sph.getProductCode())
                    .setGgModel(sph.getSpec())
                    .setUnit(sph.getUnit())
                    .setQuantity(BigDecimal.ONE)
                    .setUnitPrice(parkFee)
                    .setAmount(parkFee)
                    .setTaxRate(DecimalUtils.divide100(sph.getTaxRate())
                        .setScale(2, RoundingMode.HALF_DOWN)));
            } else {
                servFee = servFee.add(parkFee);
            }
        }

        if (DecimalUtils.gtZero(elecFee)) {
            if (elec.isPresent()) {
                InvoicedTemplateSalDetailVo sph = elec.get();
                detailList.add(new NSRInvoiceDetail()
                    .setNo("NSR_" + record.getId())
                    .setName(sph.getProductName())
                    .setTaxNo(sph.getProductCode())
                    .setGgModel(sph.getSpec())
                    .setUnit(sph.getUnit())
                    .setQuantity(BigDecimal.ONE)
                    .setUnitPrice(elecFee)
                    .setAmount(elecFee)
                    .setTaxRate(DecimalUtils.divide100(sph.getTaxRate())
                        .setScale(2, RoundingMode.HALF_DOWN)));
            } else {
                servFee = servFee.add(elecFee);
            }
        }

        if (DecimalUtils.gtZero(servFee) && serv.isPresent()) {
            InvoicedTemplateSalDetailVo sph = serv.get();
            detailList.add(new NSRInvoiceDetail()
                .setNo(
                    "NSR_" + record.getId())
                .setName(sph.getProductName())
                .setTaxNo(sph.getProductCode())
                .setGgModel(sph.getSpec())
                .setUnit(sph.getUnit())
                .setQuantity(BigDecimal.ONE)
                .setUnitPrice(servFee)
                .setAmount(servFee)
                .setTaxRate(DecimalUtils.divide100(sph.getTaxRate())
                    .setScale(2, RoundingMode.HALF_DOWN)));
        }

        return detailList;
    }

    private class CombinedResult {

        private List<InvoicedRecordVo> records;
        private List<InvoicedTemplateSalDetailDTO> salDetails;

        public CombinedResult(List<InvoicedRecordVo> records,
            List<InvoicedTemplateSalDetailDTO> salDetails) {
            this.records = records;
            this.salDetails = salDetails;
        }

        // Getter 方法
        public List<InvoicedRecordVo> getRecords() {
            return records;
        }

        public List<InvoicedTemplateSalDetailDTO> getSalDetails() {
            return salDetails;
        }
    }
}
