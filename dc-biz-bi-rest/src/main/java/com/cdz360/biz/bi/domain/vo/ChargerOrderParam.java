//package com.chargerlinkcar.core.domain.vo;
//
//
//import com.cdz360.base.model.charge.type.OrderAbnormalReason;
//import com.chargerlinkcar.framework.common.domain.Dict;
//import com.cdz360.model.trading.bi.param.ExcelPosition;
//import com.cdz360.biz.model.order.type.ChargerOrderStatus;
//import io.swagger.v3.oas.annotations.media.Schema;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//import lombok.experimental.Accessors;
//
//import java.util.Date;
//import java.util.List;
//
///**
// *  订单列表查询传参
// * <AUTHOR>
// * @since 2016年9月7日 下午2:09:57
// *
// */
//@Data
//@Accessors(chain = true)
//@EqualsAndHashCode(callSuper = true)
//public class ChargerOrderParam extends SearchParam {
//
//    private static final long serialVersionUID = 1L;
//    /**
//     * 公有云订单ID
//     */
//    protected Long orderId;
//    /**
//     * 公有云订单编号
//     */
//    protected String orderNo;
//    /**
//     * 公有云订单编号
//     */
//    protected List<String> orderNos;
//    /**
//     * 站点ID
//     */
//    protected String stationId;
//
//    /**
//     * 站点ID数组
//     */
//    protected List<String> stationIds;
//
//    /**
//     * 站点名称
//     */
//    protected String stationName;
//
//    /**
//     * 页面充电桩ID(充电强connectId)
//     */
//    protected Long evseId;
//    /**
//     * 订单启动类型
//     */
//    @Schema(description = "订单启动类型")
//    private List<Integer> orderTypeList;
//    /**
//     * 第三方的订单号：app，互联互通
//     */
//    protected String openOrderId;
//
//    /**
//     * 用户ID
//     */
//    protected Long customerId;
//
//    /**
//     * 充电卡号/身份唯一识别号
//     */
//    protected String cardNo;
//
//    /** 订单来源类型,0:微信渠道;1:APP在线发起充电;2:APP蓝牙发起充电:3:刷卡充电:4:桩上报离线数据 */
//    protected Integer channelId;
//
//    /**
//     * 订单金额
//     */
//    protected Long orderPrice;
//
//    /**
//     * 订单电量
//     */
//    protected Long orderElectricity;
//
//    /**
//     * 状态,-500：硬件故障；-40：上报超时；-35：断连超时；-30关电超时；-29：结束充电失败；-20：链接超时；-10：打开电闸超时；-7：开启充电失败，PIN可能错误；-5:系统繁忙；0：订单未激活；100：电闸打开；200：充电中；250：关电闸，充电停止；300：充电完成；800：订单结束；1000：用户已支付；2000：钱已到账
//     */
//    protected Integer status;
//
//    /**
//     * 订单状态
//     */
//    protected ChargerOrderStatus orderStatus;
//
//    /**
//     * 星标标志，默认0，0：非星标；1：星标；
//     */
//    protected Integer starFlag;
//
//    /** 计费模板ID */
//    protected Long priceSchemeId;
//
//    /**
//     * 充电车位ID
//     */
//    protected Long carportId;
//
//    /**
//     * 备注
//     */
//    protected String remark;
//
//    /**
//     * 订单创建时间
//     */
//    protected Date createTime;
//
//    /**
//     * 查询开始时间
//     */
//    protected String beginTime;
//
//    /**
//     * 查询结束时间
//     */
//    protected String endTime;
//
//    /**
//     * 充电接口ID
//     */
//    protected String connectId;
//    /**
//     *  显示字段 内容：出厂编号+evseId+connectorId （枪头id）
//     */
//    private String showId;
//
//    /**
//     * 枪头编号
//     */
//    private String bcCode;
//
//    /**
//     * 枪头编号
//     */
//    private String connectorId;
//    /**
//     * 商户ID
//     */
//    private Long commercialId;
//
//    /**
//     * app商户ID
//     */
//    private Long appCommId;
//
//    /**
//     * 设备商户id
//     */
//    private Long deviceCommId;
//    /**
//     * 用户手机号
//     */
//    private String mobilePhone;
//    /**
//     * 用户名称
//     */
//    private String customerName;
//    private List<Integer> statusList;
//
//    private List<ChargerOrderStatus> orderStatusList;
//
//    /**
//     * 支付方式
//     **/
//    private Integer payModes;
//    /**
//     * 支付状态
//     **/
//    private Integer payStatus;
//
//    /**
//     * 客户订单金额，向客户提供商(客户)收取的钱
//     */
//    protected Long customerPrice;
//
//    /**
//     * 设备运营商ID
//     */
//    protected Long deviceCommercialId;
//
//    /**
//     * 客户运营商名称
//     */
//    private String customerCommercialName;
//    /**
//     * 客户运营商ID
//     */
//    private Long customerCommercialId;
//
//    /**
//     * 车辆Vin码
//     */
//    private String vin;
//    private String cardChipNo;
//    /*
//     * 桩号
//     */
//    private String boxOutFactoryCode;
//    private Integer hlht;
//
//    public String getCustomerName() {
////        if (Base64Util.isBase64(customerName)) {
////            return Base64Util.getFromBase64(customerName);
////        } else {
//        return customerName;
////        }
//    }
//
//    /**商户列表*/
//    private List<Long> commIdList;
//
//    /** 订单来源类型列表,0:微信渠道;1:APP在线发起充电;2:APP蓝牙发起充电:3:刷卡充电:4:桩上报离线数据 */
//    private List<Integer> channelIdList;
//    /**
//     * 按照订单结束时间排序：1:asc 2:desc
//     */
//    private Integer orderByChargeEndTime;
//    /**
//     * 结束原因
//     */
//    private String stopReason;
//
//    private Long merchantId;
//    private List<Long> rBlocUserIds;
//
//    //下载文件类型
//    private String type;
//
//    //    private String subDir;
////
////    private String subFileName;
//    private ExcelPosition excelPosition;
//    private List<Dict> orderSourceLst;
//    private List<Dict> orderStatusLst;
//
//    /**
//     * 1个人账户 2集团授权账户
//     */
//    private Integer defaultPayType;
//
//    /**
//     * 订单异常原因
//     */
//    private OrderAbnormalReason abnormal;
//    /**
//     * 小程序查询时是否排除orderStatus为Cancel的订单
//     */
//    private boolean excludeCancelOrder;
//
//    /**
//     * 账户标识
//     */
//    private Long payAccountId;
//
//    private Long maxIdleTime;//订单超过设定时间未更新 分钟 预设30分钟;异常订单查询
//
//    private Long maxCharingTime;//“充电中”状态超过设定时间未改变 分钟 预设24小时;异常订单查询
//
//    private Long maxStartingTime;//超过设定时间“订单启动”状态未改变 分钟 预设5分钟;异常订单查询
//
//
//    /**
//     * 卡名称
//     */
//    private String cardName;
//    /**
//     * 车牌号
//     */
//    private String carNo;
//    /**
//     * 车辆自编号
//     */
//    private String carNum;
//
//    /**
//     * 线路
//     */
//    private String lineNum;
//
//    /**
//     * 所属集团客户
//     */
//    private String blocUserName;
//
//    /**
//     * 商户列表
//     */
//    private List<Long> deviceCommIdList;
//
//    /**
//     * 桩编号
//     */
//    private String evseNo;
//
//    /**
//     * 查询创建时间-from
//     */
//    protected String createTimeFrom;
//
//    /**
//     * 查询创建时间-to
//     */
//    protected String createTimeTo;
//
//    /**查询支付时间-from
//     */
//    protected String payTimeFrom;
//
//    /**
//     * 查询支付时间-to
//     */
//    protected String payTimeTo;
//
//    /**
//     * 查询充电开始时间-from
//     */
//    protected String chargeStartTimeFrom;
//
//    /**
//     * 查询充电开始时间-to
//     */
//    protected String chargeStartTimeTo;
//
//    /**
//     * 查询充电结束时间-from
//     */
//    protected String chargeEndTimeFrom;
//
//    /**
//     * 查询充电结束时间-to
//     */
//    protected String chargeEndTimeTo;
//
//    private List<String> payAccountList;
//
//    private List<String> payTypeList;
//}
