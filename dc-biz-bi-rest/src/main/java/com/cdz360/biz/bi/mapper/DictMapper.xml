<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.bi.mapper.DictMapper">

	<sql id="dictColumnsList">
		a.id AS "id",
		a.value AS "value",
		a.label AS "label",
		a.type AS "type",
		a.description AS "description"
	</sql>

	<!--根据类型查询字典-->
	<select id="findDictDataByType" resultType="com.chargerlinkcar.framework.common.domain.Dict">
		SELECT
		<include refid="dictColumnsList"/>
		FROM d_card_manager.t_sys_dict a
		WHERE a.type=#{type}
	</select>

</mapper>