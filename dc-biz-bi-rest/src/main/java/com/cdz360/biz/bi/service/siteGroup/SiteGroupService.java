package com.cdz360.biz.bi.service.siteGroup;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteGroupRoDs;
import com.cdz360.biz.model.sys.constant.SiteGroupType;
import com.cdz360.biz.model.sys.param.ListSiteGroupParam;
import com.cdz360.biz.model.sys.po.SiteGroupPo;
import com.cdz360.biz.model.trading.site.po.SiteGroupRefPo;
import com.cdz360.biz.utils.feign.auth.AuthSiteGroupFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Service;

@Service
public class SiteGroupService {

    @Autowired
    private SiteGroupRoDs siteGroupRoDs;
    @Autowired
    private AuthSiteGroupFeignClient authSiteGroupFeignClient;

    public void fillSiteGroupMap(@NonNull final List<String> siteIdList,
        @NonNull Map<String, List<String>> siteGidMap,
        @NonNull Map<String, String> groupMap) {
        if (CollectionUtils.isEmpty(siteIdList)) {
            return;
        }

        List<String> tempList = siteIdList.stream()
            .filter(StringUtils::isNotBlank).distinct()
            .filter(s -> !siteGidMap.containsKey(s)) // 避免资源浪费
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(tempList)) {
            List<SiteGroupRefPo> refList = siteGroupRoDs.getSiteGroupRefList(
                siteIdList); // 获取场站对应的组
            if (CollectionUtils.isNotEmpty(refList)) {
                siteGidMap.putAll(refList.stream().collect(
                    Collectors.groupingBy(SiteGroupRefPo::getSiteId,
                        Collectors.mapping(SiteGroupRefPo::getGid, Collectors.toList()))));

                List<String> gidList = refList.stream().map(SiteGroupRefPo::getGid).distinct()
                    .filter(t -> !groupMap.containsKey(t)) // 避免资源浪费
                    .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(gidList)) {
                    ListSiteGroupParam req = new ListSiteGroupParam();
                    req.setGidList(gidList)
                        .setTypeList(List.of(SiteGroupType.YW));
                    req.setEnable(true);
                    var res = authSiteGroupFeignClient.findSiteGroup(req)
                        .block(Duration.ofSeconds(30L)); // 获取场站组列表
                    FeignResponseValidate.check(res);
                    groupMap.putAll(res.getData().stream().collect(Collectors.toMap(
                        SiteGroupPo::getGid, SiteGroupPo::getName,
                        (v1, v2) -> v1)));
                }
            }
        }
    }

}
