package com.cdz360.biz.bi.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.bi.config.ExportFileConfig;
import com.cdz360.biz.bi.feign.DeviceMgmFeignClient;
import com.cdz360.biz.ds.trading.ro.yw.ds.YwOrderRoDs;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import com.cdz360.biz.model.sim.param.ListSimParam;
import com.cdz360.biz.model.sim.vo.SimVo;
import com.cdz360.biz.model.trading.bi.dto.BiExportGroups;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.pv.vo.ExportPvProfitTrendDayVo;
import com.cdz360.biz.model.trading.pv.vo.ExportPvProfitTrendMonthVo;
import com.cdz360.biz.model.trading.sim.vo.SimExportVo;
import com.chargerlinkcar.framework.common.utils.ExcelUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class SimService {

    @Autowired
    private ExportFileConfig exportFileConfig;

    @Autowired
    private YwOrderRoDs ywOrderRoDs;

    @Autowired
    private DeviceMgmFeignClient deviceMgmFeignClient;

    public void exportSimListExcel(ExcelPosition position, ListSimParam param) throws IOException {
        log.info("SIM卡列表导出到EXCEL: {}", JsonUtils.toJsonString(position));
        ExcelUtil.builder(exportFileConfig.getExcelDir(), position,
                BiExportGroups.SIM_LIST.getName())
            .addHeader(SimExportVo.class)
            .loopAppendData((start, size) -> {
                param.setStart((long) ((start - 1) * size))
                    .setSize(size);
                param.setTotal(Boolean.FALSE);

                ListResponse<SimVo> block = this.deviceMgmFeignClient.getSimList(param);
                List<SimVo> data = new ArrayList<>();
                if (block != null) {
                    data = block.getData();
                }
                return new ArrayList<>(data);
            }, list -> new ArrayList<>(list.stream()
                .map(i -> {
                    SimVo simVo = (SimVo) i;
                    SimExportVo result = new SimExportVo();
                    BeanUtils.copyProperties(simVo, result);
                    // 运营商
                    switch (simVo.getVendor()) {
                        case CM_CT:
                        case CM_PB:
                            result.setVendorName("移动");
                            break;
                        case CU:
                            result.setVendorName("联通");
                            break;
                        case CT:
                            result.setVendorName("电信");
                            break;
                        default:
                            result.setVendorName("未知");
                    }
                    // 在线状态
//                            if (simVo.getSlotStatus().equals(1)) {
//                                result.setStatusName("在线");
//                            } else if (simVo.getSlotStatus().equals(2)) {
//                                result.setStatusName("离线");
//                            } else {
//                                result.setStatusName("--");
//                            }
                    if (simVo.getSlotStatus() != null) {
                        switch (simVo.getSlotStatus()) {
                            case 1:
                                result.setStatusName("在线");
                                break;
                            case 2:
                                result.setStatusName("离线");
                                break;
                            default:
                                result.setStatusName("--");
                        }
                    } else {
                        result.setStatusName("--");
                    }
                    // 卡状态
                    if (simVo.getStatus() != null) {
                        switch (simVo.getStatus()) {
                            case ACTIVATED: // 已激活
                                result.setCardStatus("已激活");
                                break;
                            case ACTIVATION_READY: // 待激活
                                result.setCardStatus("待激活");
                                break;
                            case DEACTIVATED: // 停机
                                result.setCardStatus("停机");
                                break;
                            case INVENTORY: // 库存
                                result.setCardStatus("库存");
                                break;
                            case PURGED: // 已清除
                                result.setCardStatus("已清除");
                                break;
                            case REPLACED: // 已更换
                                result.setCardStatus("已更换");
                                break;
                            case RETIRED: // 已失效
                                result.setCardStatus("已失效");
                                break;
                            case TEST_READY: // 可测试
                                result.setCardStatus("可测试");
                                break;
                            case ONE_WAY_HALT: // 单向停机
                                result.setCardStatus("单向停机");
                                break;
                            case READY_TO_CANCEL: // 预销户
                                result.setCardStatus("预销户");
                                break;
                            case ASSIGNED: // 过户
                                result.setCardStatus("过户");
                                break;
                            case DORMANT: // 休眠
                                result.setCardStatus("休眠");
                                break;
                            default:
                                result.setCardStatus("未知");
                        }
                    } else {
                        result.setCardStatus("未知");
                    }
                    return result;
                })
                .collect(Collectors.toList())))
            .write2File();
    }


    private static Class<?> getHeaderClass(SiteBiSampleType type) {
        if (SiteBiSampleType.MONTH.equals(type)) {
            return ExportPvProfitTrendMonthVo.class;
        }
        return ExportPvProfitTrendDayVo.class;
    }
}
