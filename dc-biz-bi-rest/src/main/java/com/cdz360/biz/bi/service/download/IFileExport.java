package com.cdz360.biz.bi.service.download;

import com.cdz360.biz.model.bi.param.ExcelPosition;
import java.io.IOException;

/**
 * 文件导出
 *
 * @param <P>   接口请求参数实际类
 * @param <POS> 文件导出路径信息，建议使用{@link ExcelPosition}
 */
public interface IFileExport<P, POS extends ExcelPosition> {

    /**
     * 实质接口请求参数Class类
     *
     * @return
     */
    Class<P> paramClazz();

    /**
     * 文件导出路径信息Class类
     *
     * @return
     */
    Class<POS> posClazz();

    /**
     * 文件生成
     *
     * @param context 实质接口请求参数的json内容
     * @param pos     文件物理位置信息json内容
     */
    void genFile(String context, String pos) throws IOException;
}
