package com.cdz360.biz.bi.service.accounting;

import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ds.trading.ro.order.ds.OrderBiRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.GcSiteRoDs;
import com.cdz360.biz.model.trading.order.vo.ChargeOrderBiVo;
import com.cdz360.biz.model.trading.site.po.BiSiteGcDailyPo;
import com.cdz360.biz.model.trading.site.po.GcSitePo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 计算场站每日收入数据
 */
@Slf4j
@Service
public class SiteDailyProfitAccountingService {

    public static final String SPEL_NONE = "none";

    @Autowired
    private GcSiteRoDs gcSiteRoDs;

    @Autowired
    private OrderBiRoDs orderBiRoDs;


    @Transactional(readOnly = true)
    public BiSiteGcDailyPo accountSiteDailyIncome(String siteId, @Nullable String strDate) {
        GcSitePo gcSite = this.gcSiteRoDs.getGcSite(siteId);
        if (gcSite == null) {
            log.error("场站 {} 未配置每日运营收入计算方式,需要完善基础数据", siteId);
            return null;
        }
        LocalDate date;
        if (StringUtils.isBlank(strDate)) {
            date = LocalDate.now().plusDays(-1);
        } else {
            date = LocalDate.parse(strDate, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }
        TimeFilter timeFilter = new TimeFilter();
        timeFilter.setStartTime(Date.from(date.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        timeFilter.setEndTime(Date.from(date.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant()));
        ChargeOrderBiVo obi = orderBiRoDs.getOrderBi(null, siteId, timeFilter, false);
//        log.info("obi = {}", obi);
        BiSiteGcDailyPo result = new BiSiteGcDailyPo();
        result.setSiteId(siteId).setDate(date).setElec(obi.getElec());
        ExpressionParser parser = new SpelExpressionParser();
        result.setFee(this.accountSpel(siteId, parser, gcSite.getDailyIncome(), obi, date, obi.getFee()));   // 日运营收入
        result.setServFee(this.accountSpel(siteId, parser, gcSite.getDailyServIncome(), obi, date, obi.getServFee()));   // 日服务费收入
        return result;
    }

    private BigDecimal accountSpel(String siteId, ExpressionParser parser, String spel, ChargeOrderBiVo obi, LocalDate date, BigDecimal defaultValue) {
//        log.debug("spel = {}", spel);
        if (StringUtils.isBlank(spel)) {
            return defaultValue;
        } else if (SPEL_NONE.equalsIgnoreCase(spel)) {
            return BigDecimal.ZERO;
        }
        try {
            Expression expression = parser.parseExpression(spel);
            StandardEvaluationContext context = new StandardEvaluationContext();
            context.setVariable("elec", obi.getElec()); // 电量
            context.setVariable("originFee", obi.getFee());    // 总金额
            context.setVariable("servOriginFee", obi.getServFee()); // 服务费收入
            context.setVariable("lengthOfMonth", date.lengthOfMonth()); // 月总天数
            Double val = expression.getValue(context, Double.class);
            if (val == null) {
                log.warn("计算场站收入失败. siteId = {}, spel = {}, obi = {}", siteId, spel, obi);
                return null;
            }
//        log.info("val = {}", val);
            return BigDecimal.valueOf(val).setScale(2, RoundingMode.HALF_UP);
        } catch (Exception e) {
            log.warn("计算场站收入失败. siteId = {}, spel = {}, obi = {}", siteId, spel, obi);
            return null;
        }
    }
}
