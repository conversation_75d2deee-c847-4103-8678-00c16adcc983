package com.cdz360.biz.bi.service;
//
//import com.cdz360.base.model.base.dto.ObjectResponse;
//import com.cdz360.base.model.base.exception.DcArgumentException;
//import com.cdz360.base.model.base.type.PayAccountType;
//import com.cdz360.base.utils.CollectionUtils;
//import com.cdz360.base.utils.DecimalUtils;
//import com.cdz360.base.utils.JsonUtils;
//import com.cdz360.biz.bi.domain.vo.OaFieldItem;
//import com.cdz360.biz.bi.feign.DataCoreFeignClient;
//import com.cdz360.biz.model.invoice.dto.OaInvoiceBuyerInfo;
//import com.cdz360.biz.model.invoice.dto.OaInvoiceSellerInfo;
//import com.cdz360.biz.model.invoice.dto.OaInvoiceSphDetail;
//import com.cdz360.biz.model.oa.type.OaHandleNode;
//import com.cdz360.biz.model.oa.vo.OaInvoicedVo;
//import com.cdz360.biz.oa.dto.OaSettJobBillExDto;
//import com.cdz360.biz.oa.dto.OaSettJobBillInfo;
//import com.cdz360.biz.oa.dto.OaSettJobBillPdfDto;
//import com.cdz360.biz.oa.dto.PaymentPlan;
//import com.cdz360.biz.oa.param.OaProcessBaseParam;
//import com.cdz360.biz.utils.feign.data.DataCoreClient;
//import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
//import com.fasterxml.jackson.core.type.TypeReference;
//import io.swagger.v3.oas.annotations.media.Schema;
//import java.math.BigDecimal;
//import java.util.ArrayList;
//import java.util.Comparator;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//import java.util.Optional;
//import java.util.Set;
//import java.util.stream.Collectors;
//import javax.validation.constraints.NotNull;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.BeanUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import reactor.core.publisher.Flux;
//import reactor.core.publisher.Mono;
//
//@Slf4j
//@Service
//@Schema(description = "充电订单发票流程导出明细项构建")
//public class OaExportPdfItemBuilder {
//
//    @Autowired
//    private DataCoreClient dataCoreClient;
//
//    @Autowired
//    private DataCoreFeignClient dataCoreFeignClient;
//
//    @Autowired
//    private InvoicedSalTempRefDs invoicedSalTempRefDs;
//    @Autowired
//    private InvoicedTemplateSalDs invoicedTemplateSalDs;
//
////    private static List<OaFieldItem> newOaFieldItems() {
////        return List.of(
////            new OaFieldItem().setIndex(1).setId("accountType").setType("text").setLabel("帐户类型"),
//////            new OaFieldItem().setIndex(3).setId("phone").setType("text").setLabel("个人手机号"),
////            new OaFieldItem().setIndex(5).setId("invoiceType").setType("text").setLabel("开票种类"),
////            new OaFieldItem().setIndex(7).setId("invoiceTitleId").setType("invoice-title-table")
////                .setLabel("选择抬头"), // 发票抬头
////            new OaFieldItem().setIndex(9).setId("orderBiVo").setType("prepay-order-table")
////                .setLabel("预付订单"),
////            new OaFieldItem().setIndex(11).setId("tempSalId").setType("text").setLabel("开票主体"),
////            new OaFieldItem().setIndex(13).setId("productTempId").setType("text")
////                .setLabel("商品行模板"),
////            new OaFieldItem().setIndex(15).setId("invoicingContent").setType("sph-table")
////                .setLabel("开票内容"), // 实开内容: actualData(应开)
////            new OaFieldItem().setIndex(17).setId("invoiceRemark").setType("empty-label-text"),
////            // 开票内·······容备注
////            new OaFieldItem().setIndex(19).setId("data-difference").setType("data-difference-table")
////                .setLabel("数据比对"), // 实开/应开数据 -- 动态增加列
////            new OaFieldItem().setIndex(21).setId("note").setType("text").setLabel("备注")
////        );
////    }
//
////    private static List<OaFieldItem> newSiteExpendOaField() {
////        return List.of(
////            new OaFieldItem().setIndex(1).setId("siteName").setType("text").setLabel("站点名称"),
////            new OaFieldItem().setIndex(10).setId("siteNo").setType("text").setLabel("站点编码"),
////            new OaFieldItem().setIndex(11).setId("siteRemark").setType("text").setLabel("场站备注"),
////            new OaFieldItem().setIndex(20).setId("customerName").setType("text")
////                .setLabel("分成客户名称"),
////            new OaFieldItem().setIndex(30).setId("bankName").setType("text").setLabel("开户行"),
////            new OaFieldItem().setIndex(40).setId("bankAccount").setType("text")
////                .setLabel("银行账号"),
////            new OaFieldItem().setIndex(50).setId("billDateRange").setType("billDateRange")
////                .setLabel("结算账期"),
////            new OaFieldItem().setIndex(60).setId("payTimeRequirement").setType("text")
////                .setLabel("付款时间要求"),
////            new OaFieldItem().setIndex(100).setId("note").setType("text").setLabel("备注")
////        );
////    }
//
////    private static List<OaFieldItem> newSiteIncomeOaField() {
////        return List.of(
////            new OaFieldItem().setIndex(1).setId("siteName").setType("text").setLabel("站点名称"),
////            new OaFieldItem().setIndex(10).setId("siteNo").setType("text").setLabel("站点编码"),
////            new OaFieldItem().setIndex(20).setId("siteRemark").setType("text").setLabel("场站备注"),
////            new OaFieldItem().setIndex(60).setId("billDateRange").setType("billDateRange")
////                .setLabel("账期日期"),
////            new OaFieldItem().setIndex(100).setId("note").setType("text").setLabel("备注")
////        );
////    }
//
////    private static OaFieldItem fillFieldItem(Map data, OaFieldItem filed) {
////        final Object val = data.get(filed.getId());
////        if (val instanceof String && "text".equals(filed.getType())) {
////            filed.setValue(val);
////        } else if (val instanceof Map && "billDateRange".equals(filed.getType())) {
////            filed.setValue(val);
////        }
////        return filed;
////    }
//
////    /**
////     * 选结算单添加到输出列表
////     *
////     * @param settBillSummary
////     * @param differenceRemark
////     * @param oaFieldItems
////     */
////    private static void extractAndAppendBillList(Object settBillSummary,
////        Object differenceRemark,
////        List<OaFieldItem> oaFieldItems) {
////        // OaSettJobBillInfo
////        if (settBillSummary != null) {
////            final String summaryJson = JsonUtils.toJsonString(settBillSummary);
////            final OaSettJobBillInfo oaSettJobBillInfo = JsonUtils.fromJson(summaryJson,
////                OaSettJobBillInfo.class);
////            log.info("oaSettJobBillInfo: {}", oaSettJobBillInfo);
////
////            // 订单列表
////            final List<OaSettJobBillExDto> billList = oaSettJobBillInfo.getSettJobBillList()
////                .stream()
////                .map(e -> {
////                    OaSettJobBillExDto ret = new OaSettJobBillExDto();
////                    BeanUtils.copyProperties(e, ret);
////                    ret.setSum(e.getElecFee()
////                        .add(e.getParkFee())
////                        .add(e.getServFee()));
////                    return ret;
////                })
////                .collect(Collectors.toList());
////
////            // 平台汇总
////            OaSettJobBillExDto pfSum = new OaSettJobBillExDto();
////            pfSum
////                .setSum(billList.stream()
////                    .map(OaSettJobBillExDto::getSum)
////                    .reduce(BigDecimal.ZERO, BigDecimal::add))
////                .setBillNo("平台汇总")
////                .setElecFee(billList.stream()
////                    .map(OaSettJobBillExDto::getElecFee)
////                    .reduce(BigDecimal.ZERO, BigDecimal::add))
////                .setServFee(billList.stream()
////                    .map(OaSettJobBillExDto::getServFee)
////                    .reduce(BigDecimal.ZERO, BigDecimal::add))
////                .setParkFee(billList.stream()
////                    .map(OaSettJobBillExDto::getParkFee)
////                    .reduce(BigDecimal.ZERO, BigDecimal::add));
////
////            // 实际结算
////            OaSettJobBillExDto actualFee = new OaSettJobBillExDto();
////            actualFee
////                .setSum(DecimalUtils.add(
////                    DecimalUtils.add(oaSettJobBillInfo.getActualFee().getElecFee(),
////                        oaSettJobBillInfo.getActualFee().getServFee()),
////                    oaSettJobBillInfo.getActualFee().getParkFee()))
////                .setBillNo("实际结算")
////                .setElecFee(oaSettJobBillInfo.getActualFee().getElecFee())
////                .setServFee(oaSettJobBillInfo.getActualFee().getServFee())
////                .setParkFee(oaSettJobBillInfo.getActualFee().getParkFee());
////
////            // 差异
////            OaSettJobBillExDto diffJobBill = new OaSettJobBillExDto();
////            diffJobBill.setSum(pfSum.getSum().subtract(actualFee.getSum()))
////                .setBillNo("差异")
////                .setElecFee(pfSum.getElecFee().subtract(actualFee.getElecFee()))
////                .setServFee(pfSum.getServFee().subtract(actualFee.getServFee()))
////                .setParkFee(pfSum.getParkFee().subtract(actualFee.getParkFee()));
////
////            billList.add(pfSum);
////            billList.add(actualFee);
////            billList.add(diffJobBill);
////
////            OaSettJobBillPdfDto oaSettJobBillPdfDto = new OaSettJobBillPdfDto();
////            oaSettJobBillPdfDto.setSettJobBillList(billList)
////                .setDifference(oaSettJobBillInfo.getDifference());
////
////            if (Boolean.TRUE.equals(oaSettJobBillInfo.getDifference()) &&
////                differenceRemark instanceof String) {
////                oaSettJobBillPdfDto.setDifferenceRemark((String) differenceRemark);
////                final OaFieldItem diffNote = new OaFieldItem()
////                    .setIndex(42)
////                    .setId("differenceRemark")
////                    .setType("text")
////                    .setLabel("差异说明")
////                    .setValue(differenceRemark);
////                oaFieldItems.add(diffNote);
////            }
////
////            final OaFieldItem jobBillListItem = new OaFieldItem()
////                .setIndex(41)
////                .setId("settBillSummary")
////                .setType("sett-bill-summary")
////                .setLabel("选结算单")
////                .setValue(oaSettJobBillPdfDto);
////            oaFieldItems.add(jobBillListItem);
////        }
////
////    }
//
////    /**
////     * 经办节点添加到输出列表
////     *
////     * @param data
////     */
////    private void extractAndAppendHandleNodes(Map data, List<OaFieldItem> oaFieldItems,
////        int baseIndex) {
////        final List<OaHandleNode> handleNodes = getAs(data.get("handleNodes"),
////            new TypeReference<>() {
////            });
////        log.info("{}", handleNodes);
////
//////        int baseIndex = 70;
////
////        if (CollectionUtils.isNotEmpty(handleNodes)) {
////            oaFieldItems.add(makeTextOaFieldItem("handleNodes",
////                baseIndex++,
////                "经办节点",
////                handleNodes.stream()
////                    .map(OaHandleNode::getDesc)
////                    .collect(Collectors.joining(", "))));
////
////            if (handleNodes.contains(OaHandleNode.SIGNET)) {
////                oaFieldItems.add(makeTextOaFieldItem("signetCompany",
////                    baseIndex++,
////                    "印章公司名称",
////                    data.get("signetCompany")));
////
////                oaFieldItems.add(makeTextOaFieldItem("signetType",
////                    baseIndex++,
////                    "印章类型",
////                    data.get("signetType")));
////
////                oaFieldItems.add(makeTextOaFieldItem("signetDesc",
////                    baseIndex++,
////                    "其他印章说明",
////                    data.get("signetDesc")));
////            }
////        }
////
////        if (CollectionUtils.isNotEmpty(handleNodes) && handleNodes.contains(OaHandleNode.INVOICE)) {
////
////            final OaInvoiceBuyerInfo invoiceBuyerInfo = getAs(data.get("invoiceBuyerInfo"),
////                OaInvoiceBuyerInfo.class);
////
////            log.info("{}", invoiceBuyerInfo);
////
////            oaFieldItems.add(makeTextOaFieldItem("invoiceType",
////                baseIndex++,
////                "开票种类",
////                invoiceBuyerInfo.getType().getDesc()));
////
////            oaFieldItems.add(makeTextOaFieldItem("invoiceTitleName",
////                baseIndex++,
////                "抬头信息",
////                invoiceBuyerInfo.getName()));
////
////            final OaFieldItem titleTable = new OaFieldItem();
////            oaFieldItems.add(titleTable.setId("invoiceBuyerInfo")
////                .setIndex(baseIndex++)
////                .setLabel(null)
////                .setType("invoiceBuyerInfo")
////                .setValue(invoiceBuyerInfo));
////
////            final OaInvoiceSellerInfo oaInvoiceSellerInfo = getAs(data.get("invoiceSellerInfo"),
////                OaInvoiceSellerInfo.class);
////            log.info("{}", oaInvoiceSellerInfo);
////            final String invoicedTempSalRefName = invoicedSalTempRefDs.getNameById(
////                oaInvoiceSellerInfo.getInvoiceSalTmpRefId());
////            final String invoicedTempSalName = invoicedTemplateSalDs.getNameById(
////                oaInvoiceSellerInfo.getInvoiceTmpSalId());
////
////            oaFieldItems.add(makeTextOaFieldItem("invoicedTempSalName",
////                baseIndex++,
////                "开票主体",
////                invoicedTempSalName));
////
////            oaFieldItems.add(makeTextOaFieldItem("invoicedTempSalRefName",
////                baseIndex++,
////                "商品行模板",
////                invoicedTempSalRefName));
////
////            final List<OaInvoiceSphDetail> sphDetails = getAs(data.get("sphDetails"),
////                new TypeReference<>() {
////                });
////
////            final OaFieldItem invoicingContent = new OaFieldItem();
////            oaFieldItems.add(invoicingContent.setId("sphDetails")
////                .setIndex(baseIndex++)
////                .setLabel("开票内容")
////                .setType("sphDetails")
////                .setValue(sphDetails));
////
////            oaFieldItems.add(makeTextOaFieldItem("invoiceRemark",
////                baseIndex++,
////                "开票备注",
////                data.get("invoiceRemark")));
////
////            if (data.get("paymentPlanStatus") != null) {
////                String paymentPlanStatus = "";
////                if (Boolean.TRUE.equals(data.get("paymentPlanStatus"))) {
////                    paymentPlanStatus = "已回款";
////                } else {
////                    paymentPlanStatus = "未回款";
////                }
////                oaFieldItems.add(makeTextOaFieldItem("paymentPlanStatus",
////                    baseIndex++,
////                    "回款情况",
////                    paymentPlanStatus));
////
////                if (Boolean.FALSE.equals(data.get("paymentPlanStatus"))) {
////                    // 增加回款计划
////                    final List<PaymentPlan> paymentPlans = getAs(data.get("paymentPlans"),
////                        new TypeReference<>() {
////                        });
////                    final OaFieldItem paymentPlansItem = new OaFieldItem();
////                    oaFieldItems.add(paymentPlansItem.setId("paymentPlans")
////                        .setIndex(baseIndex++)
////                        .setLabel("回款计划")
////                        .setType("paymentPlans")
////                        .setValue(paymentPlans));
////                }
////
////            }
////
////
////        }
////    }
//
////    private static OaFieldItem makeTextOaFieldItem(String id, int index, String label, Object val) {
////        final OaFieldItem signetComName = new OaFieldItem();
////        return signetComName.setId(id)
////            .setIndex(index)
////            .setLabel(label)
////            .setType("text")
////            .setValue(val);
////    }
//
////    private static <T> T getAs(Object o, Class<T> clazz) {
////        return JsonUtils.fromJson(JsonUtils.toJsonString(o), clazz);
////    }
////
////    public static <T> T getAs(Object o, TypeReference<T> typereference) {
////        return JsonUtils.fromJson(JsonUtils.toJsonString(o), typereference);
////    }
//
//
////    @SuppressWarnings({"ALL"})
////    public Mono<List<OaFieldItem>> expenseSiteItem(String procInstId, Map data) {
////
////        final ArrayList<OaFieldItem> oaFieldItems = new ArrayList<>(newSiteExpendOaField());
////
////        extractAndAppendBillList(data.get("settBillSummary"), data.get("differenceRemark"),
////            oaFieldItems);
////
////        extractAndAppendHandleNodes(data, oaFieldItems, 70);
////
////        return Flux.fromIterable(oaFieldItems)
////            .map(e -> {
////                return fillFieldItem(data, e);
////            })
////            .sort(Comparator.comparing(OaFieldItem::getIndex))
////            .collectList();
////    }
//
////    @SuppressWarnings({"ALL"})
////    public Mono<List<OaFieldItem>> incomeSiteItem(String procInstId, Map data) {
////
////        final ArrayList<OaFieldItem> oaFieldItems = new ArrayList<>(newSiteIncomeOaField());
////
////        extractAndAppendBillList(data.get("settBillSummary"), data.get("differenceRemark"),
////            oaFieldItems);
////
////        extractAndAppendHandleNodes(data, oaFieldItems, 70);
////
////        return Flux.fromIterable(oaFieldItems)
////            .map(e -> {
////                return fillFieldItem(data, e);
////            })
////            .sort(Comparator.comparing(OaFieldItem::getIndex))
////            .collectList();
////    }
//
////    @SuppressWarnings({"ALL"})
////    public Mono<List<OaFieldItem>> prepayOrderItem(String procInstId, OaProcessBaseParam data) {
////        Map<String, Object> variables = data.getFormVariables();
////        final Map<String, OaInvoicedVo> invoicedInfoMap = new HashMap<>();
////
////        // 账户类型
////        List<OaFieldItem> fieldItems = new ArrayList<>(newOaFieldItems());
////        PayAccountType accountType = PayAccountType.valueOf(variables.get("accountType"));
////        switch (accountType) {
////            case PERSONAL:
////                fieldItems.add(new OaFieldItem()
////                    .setIndex(3).setId("phone").setType("text").setLabel("个人手机号"));
////                break;
////            case COMMERCIAL:
////                fieldItems.add(new OaFieldItem()
////                    .setIndex(2).setId("commName").setType("text").setLabel("账户所属商户"));
////                fieldItems.add(new OaFieldItem()
////                    .setIndex(3).setId("phone").setType("text").setLabel("会员手机号"));
////                break;
////            case CREDIT:
////                fieldItems.add(new OaFieldItem()
////                    .setIndex(2).setId("corpName").setType("text").setLabel("企客名称"));
//////                fieldItems.add(new OaFieldItem()
//////                    .setIndex(3).setId("contact-list").setType("text").setLabel("企客合约"));
//////                fieldItems.add(new OaFieldItem()
//////                    .setIndex(4).setId("phone").setType("text").setLabel("企客摘要"));
////                break;
////            default:
////                throw new DcArgumentException("账号类型不支持");
////        }
////
////        return Flux.fromIterable(fieldItems)
////            .flatMap(x -> {
////                if (variables.containsKey(x.getId())) {
////                    Object value = variables.get(x.getId());
////                    if (null != value) {
////                        switch (x.getId()) {
////                            case "invoiceTitleId":
////                            case "tempSalId":
////                            case "productTempId":
////                                // 通过流程: /oa/prepaidInvoicing/getInvoiceVo?procInstId=b8847f57-e4e8-11ed-ae16-0242ac11000c
////                                if (invoicedInfoMap.containsKey(procInstId)) {
////                                    x.setValue(invoicedInfoMap.get(procInstId));
////                                } else {
////                                    return this.fetchOaProcInstInvoicedInfo(procInstId)
////                                        .map(x::setValue);
////                                }
////                                break;
////                            case "orderBiVo":
////                                x.setValue(prepayOrderBi(value));
////                                break;
////                            case "invoicingContent": // LIST
////                            default:
////                                x.setValue(value);
////                        }
////                    } else {
////                        x.setValue(null);
////                    }
////                    // 👇 独自计算
////                } else if ("data-difference".equals(x.getId())) {
////                    x.setValue(prepayOrderDataDiff(variables));
////                } else {
////                    x.setValue(null);
////                }
////                return Mono.just(x);
////            })
////            .sort(Comparator.comparing(OaFieldItem::getIndex))
////            .collectList();
////    }
//
////    @SuppressWarnings("ALL")
////    private static List<OaFieldItem> prepayOrderBi(Object value) {
////        // 转成List: count, elec, elecFee, elecCostFee, servFee, servCostFee, parkingFee, invoiceFee
////        HashMap<String, Object> vMap = (HashMap<String, Object>) value;
////        List<OaFieldItem> tItems = List.of(
////            new OaFieldItem().setLabel("单位:元").setValue("订单汇总"),
////            new OaFieldItem().setId("count").setLabel("订单数"),
//////                                    new OaFieldItem().setId("elec").setLabel("总电量"),
////            new OaFieldItem().setId("elecFee").setLabel("总电费"),
//////                                    new OaFieldItem().setId("elecCostFee").setLabel("实际电费"),
////            new OaFieldItem().setId("servFee").setLabel("总服务费"),
//////                                    new OaFieldItem().setId("servCostFee").setLabel("实际服务费"),
////            new OaFieldItem().setId("orderFee").setLabel("金额汇总"),
////            new OaFieldItem().setId("invoiceFee").setLabel("可开票金额"),
////            new OaFieldItem().setId("parkingFee").setLabel("总停充超时费")
////        );
////        BigDecimal orderFee = BigDecimal.ZERO;
////        for (OaFieldItem item : tItems) {
////            if (vMap.containsKey(item.getId())) {
////                item.setValue(vMap.get(item.getId()));
////                if (Set.of("elecFee", "servFee").contains(item.getId())) {
////                    orderFee = orderFee.add(
////                        new BigDecimal(vMap.get(item.getId()).toString()));
////                }
////            } else if ("orderFee".equals(item.getId())) { // 有顺序要求
////                item.setValue(orderFee);
////            }
////        }
////        return tItems;
////    }
//
////    @SuppressWarnings("ALL")
////    private static HashMap<String, List<Object>> prepayOrderDataDiff(
////        Map<String, Object> variables) {
////        List invoice = (List) variables.get("invoicingContent");// 实开
////        List order = (List) variables.get("actualData");// 实开
////
////        // 表头
////        HashMap<String, List<Object>> tData = new HashMap<>();
////        tData.put("titles", new ArrayList<>());
////        tData.put("sources", new ArrayList<>());
////        tData.put("invoices", new ArrayList<>());
////        tData.put("diffs", new ArrayList<>());
////        invoice.forEach(i -> {
////            Map<String, Object> item = (Map<String, Object>) i;
//////            tData.get("titles").add(item.get("productType"));
////            tData.get("titles").add(item.get("productName"));
////            tData.get("invoices").add(new BigDecimal(item.get("fixAmount").toString()));
////
////            // 应开
////            Optional has = order.stream()
////                .filter(o -> ((Map<String, Object>) o).get("productType")
////                    .equals(item.get("productType"))).findFirst();
////            if (has.isPresent()) {
////                Object fixAmount = ((Map<String, Object>) has.get()).get("fixAmount");
////                tData.get("sources").add(new BigDecimal(fixAmount.toString()));
////
////                // 差异
////                tData.get("diffs")
////                    .add(new BigDecimal(fixAmount.toString())
////                        .subtract(new BigDecimal(item.get("fixAmount").toString())));
////            }
////        });
////
////        // 总额
////        tData.get("titles").add("总额(元)");
////        tData.get("invoices").add(
////            tData.get("invoices").stream().map(xx -> (BigDecimal) xx)
////                .reduce(BigDecimal.ZERO, BigDecimal::add));
////        tData.get("sources").add(
////            tData.get("sources").stream().map(xx -> (BigDecimal) xx)
////                .reduce(BigDecimal.ZERO, BigDecimal::add));
////        tData.get("diffs").add(
////            tData.get("diffs").stream().map(xx -> (BigDecimal) xx)
////                .reduce(BigDecimal.ZERO, BigDecimal::add));
////
////        return tData;
////    }
//
////    private Mono<OaInvoicedVo> fetchOaProcInstInvoicedInfo(@NotNull final String procInstId) {
////        return this.dataCoreClient.getInvoiceVo4PrepaidProcess(procInstId)
////            .doOnNext(FeignResponseValidate::check)
////            .map(ObjectResponse::getData);
////    }
//}
