<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.bi.mapper.RCorpRoMapper">

	<resultMap id="RESULT_R_CORP_PO" type="com.cdz360.biz.model.corp.po.RCorpPo">
		<id column="id" jdbcType="BIGINT" property="id" />
		<result column="topCommId" jdbcType="BIGINT" property="topCommId" />
		<result column="commId" jdbcType="BIGINT" property="commId" />
		<result column="uid" jdbcType="BIGINT" property="uid" />
		<result column="corpName" jdbcType="VARCHAR" property="corpName" />
		<result column="contactName" jdbcType="VARCHAR" property="contactName" />
		<result column="phone" jdbcType="VARCHAR" property="phone" />
		<result column="settlementType" jdbcType="INTEGER" property="settlementType" />
		<result column="invoiceWay" jdbcType="JAVA_OBJECT" property="invoiceWay" />
		<result column="email" jdbcType="VARCHAR" property="email" />
		<result column="province" jdbcType="VARCHAR" property="province" />
		<result column="city" jdbcType="VARCHAR" property="city" />
		<result column="district" jdbcType="VARCHAR" property="district" />
		<result column="address" jdbcType="VARCHAR" property="address" />
		<result column="account" jdbcType="VARCHAR" property="account" />
		<result column="createTime" jdbcType="TIMESTAMP" property="createTime" />
		<result column="updateTime" jdbcType="TIMESTAMP" property="updateTime" />
		<result column="enable" jdbcType="BOOLEAN" property="enable" />
	</resultMap>

	<select id="getById"
			resultMap="RESULT_R_CORP_PO">	
		select * from t_r_corp where id = #{id}
	</select>

	<select id="getByUidAndIdChain"
			resultMap="RESULT_R_CORP_PO">
		select * from t_r_corp corp
		left join t_r_commercial comm
		on corp.commId = comm.id
		<where>
			corp.uid = #{uid}
			and comm.idChain like concat(#{idChain}, '%')
		</where>
		limit 1
	</select>

</mapper>
