package com.cdz360.biz.bi.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.model.base.type.UserType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.bi.config.ExportFileConfig;
import com.cdz360.biz.bi.domain.vo.PayBillExport2Vo;
import com.cdz360.biz.bi.domain.vo.PayBillExportI18nVo;
import com.cdz360.biz.bi.domain.vo.PayBillExportVo;
import com.cdz360.biz.bi.domain.vo.ZftBillExport2Vo;
import com.cdz360.biz.bi.feign.DataCoreFeignClient;
import com.cdz360.biz.ds.trading.ro.bill.ds.ZftThirdOrderRoDs;
import com.cdz360.biz.model.finance.type.ExpressStatus;
import com.cdz360.biz.model.finance.type.FlowInAccountType;
import com.cdz360.biz.model.finance.type.TaxStatus;
import com.cdz360.biz.model.finance.type.TaxType;
import com.cdz360.biz.model.trading.bi.dto.BiExportGroups;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.bill.dto.ZftThirdOrderDto;
import com.cdz360.biz.model.trading.bill.param.ListZftThirdOrderParam;
import com.cdz360.biz.model.trading.bill.vo.ExportZftThirdOrderTradeVo;
import com.cdz360.biz.model.trading.bill.vo.ExportZftThirdOrderVo;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.cdz360.biz.model.trading.deposit.vo.DepositSourceType;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.model.trading.order.param.ZftBillParam;
import com.cdz360.biz.model.trading.order.type.PayBillStatus;
import com.cdz360.biz.model.trading.order.vo.PayBillVo;
import com.cdz360.biz.model.trading.order.vo.ZftBillVo;
import com.chargerlinkcar.framework.common.utils.ExcelUtil;
import java.util.Locale;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2019/11/8 14:53
 */
@Slf4j
@Service
public class PayBillService //implements IPayBillService
{

    @Autowired
    private ExportFileConfig exportFileConfig;

    // 默认启动金额
//    @Value("${excel.dir:/tmp/excel}")
//    private String TEMP_DIR;

    private static final String PART_FILE_NAME = "part";

    // excel 导出样式
    private static final int EXPORT_TYPE_1 = 1;
    private static final int EXPORT_TYPE_2 = 2;//"财务-充值管理财务"页面

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private ZftThirdOrderRoDs zftThirdOrderRoDs;

    @Autowired
    private MessageSource messageSource;

    //    @Async
    public void exportExcelCus(ExcelPosition position, PayBillParam payBillParam)
        throws IOException {
        this.exportExcel(position, payBillParam, EXPORT_TYPE_1);
    }

    //    @Async
    public void exportExcelManager(ExcelPosition position, PayBillParam payBillParam)
        throws IOException {
        this.exportExcel(position, payBillParam, EXPORT_TYPE_2);
    }

    public void exportExcel(ExcelPosition position, PayBillParam payBillParam, int type)
        throws IOException {
        log.info("充值记录导出: pos={}, param={}", position, payBillParam);

        Locale locale = payBillParam.getLocale();

        if (null == locale) {
            // 非国际版，保留原有逻辑
            ExcelUtil builder = null;
            if (payBillParam.getGroup() == BiExportGroups.FINANCERECHARGEBI) {
                builder = ExcelUtil.builder(exportFileConfig.getExcelDir(), position, "充值信息")
                    .addHeader(PayBillExport2Vo.class);
            } else if (payBillParam.getGroup() == BiExportGroups.ZFTCHARGEBI) {
                builder = ExcelUtil.builder(exportFileConfig.getExcelDir(), position, "对账账单")
                    .addHeader(ZftBillExport2Vo.class);
            } else {
                builder = ExcelUtil.builder(exportFileConfig.getExcelDir(), position, "充值信息")
                    .addHeader(PayBillExportVo.class);
            }
            builder.loopAppendData((start, size) -> {
                    payBillParam.setIndex(start)
                        .setSize(size);
                    ListResponse<PayBillVo> res = dataCoreFeignClient.payBillList(payBillParam);
                    return new ArrayList<>(res.getData());
                }, list -> new ArrayList<>(list.stream()
                    .map(i -> (PayBillVo) i)
                    .map(vo -> this.map2ExportVo(vo, null))
                    .collect(Collectors.toList())))
                .write2File();
        } else {
            // 国际版，仅有一种表头信息
            ExcelUtil builder = ExcelUtil.builder(exportFileConfig.getExcelDir(), position,
                    messageSource.getMessage("充值信息", null, locale))
                .addI18nHeader(PayBillExportI18nVo.class, locale);

            builder.loopAppendData((start, size) -> {
                    payBillParam.setIndex(start)
                        .setSize(size);
                    ListResponse<PayBillVo> res = dataCoreFeignClient.payBillList(payBillParam);
                    return new ArrayList<>(res.getData());
                }, list -> new ArrayList<>(list.stream()
                    .map(i -> (PayBillVo) i)
                    .map(vo -> this.map2ExportVo(vo, locale))
                    .collect(Collectors.toList())))
                .write2File();
        }
        log.info("导出excel完成: {}", JsonUtils.toJsonString(position));

//        // 文件存放目录
//        String dir = exportFileConfig.getExcelDir() + File.separator + position.getSubDir();
//
//        // 临时文件
//        String tmpFilePath = dir + File.separator + position.getSubFileName() + PART_FILE_NAME + ".xlsx";
//
//        // 重定向文件
//        String filePath = dir + File.separator + position.getSubFileName() + ".xlsx";
//
//        // excel
//        ExportExcel exportExcel = null;
//        try {
//            if (payBillParam.getGroup() == BiExportGroups.FINANCERECHARGEBI) {
//                exportExcel = new ExportExcel(null, PayBillExport2Vo.class, "充值信息", null, null);
//            } else if (payBillParam.getGroup() == BiExportGroups.ZFTCHARGEBI){
//                exportExcel = new ExportExcel(null, ZftBillExport2Vo.class, "对账账单", null, null);
//            }else {
//                exportExcel = new ExportExcel(null, PayBillExportVo.class, "充值信息", null, null, payBillParam.getGroup().getCode());
//            }
//        } catch (Exception e) {
//            log.error("{}", e.getMessage(), e);
//            return;
//        }
////        ExportExcel exportExcel = new ExportExcel(
////                null, EXPORT_TYPE_1 == type ? PayBillExportVo.class : PayBillExport2Vo.class, "充值信息");
//
//        // 分页索引
//        int index = 1; // 分页从1开始
//        int size = 1000;
//
//        // 改成分页获取数据
//        while (true) {
//            // 获取订单列表
//            payBillParam.setSize(size);
//            payBillParam.setIndex(index);
//
//            ListResponse<PayBillVo> res = dataCoreFeignClient.payBillList(payBillParam);
//            if (res == null || res.getData() == null || res.getData().size() == 0) {
//                log.warn("获取订单结束.");
//                break;
//            } else {
//                log.info("当前页: index={}", index);
//                // 追加到excel
//                exportExcel.addDataList((index - 1) * size + 1, this.map2ExportVo(res.getData())); // 考虑数据开始行号
//
//                // 页码递增
//                index += 1;
//            }
//        }
//
//        log.info("临时文件: {}, 重定向文件: {}", tmpFilePath, filePath);
//        try {
//            File folder = new File(dir);
//            //如果文件夹不存在则创建
//            if (!folder.exists() && !folder.isDirectory()) {
//                folder.mkdirs();
//            }
//            exportExcel.writeFile(tmpFilePath);
//
//            //重命名:临时文件->最终文件名
//            new File(tmpFilePath).renameTo(new File(filePath));
//        } catch (IOException e) {
//            log.error("充值记录excel生成出错: err={}", e.getMessage(), e);
//        }
//        log.info("充值记录excel生成完成, filePath: {}", filePath);
    }

    /**
     * 直付通订单导出
     *
     * @param position
     * @param payBillParam
     */
//    @Async
    public void exportZftList(ExcelPosition position, ZftBillParam payBillParam)
        throws IOException {
        log.info("直付通记录导出: pos={}, param={}", position, payBillParam);

        ExcelUtil.builder(exportFileConfig.getExcelDir(), position, "交易订单")
            .addHeader(ZftBillExport2Vo.class)
            .loopAppendData((start, size) -> {
                payBillParam.setStart((long) ((start - 1) * size))
                    .setSize(size);
                payBillParam.setTotal(Boolean.FALSE);

                ListResponse<ZftBillVo> res = dataCoreFeignClient.zftBillList(payBillParam);
                return new ArrayList<>(res.getData());
            }, list -> new ArrayList<>(list.stream()
                .map(i -> (ZftBillVo) i)
                .map(this::zftList2ExportVo)
                .collect(Collectors.toList())))
            .write2File();

//        // 文件存放目录
//        String dir = exportFileConfig.getExcelDir() + File.separator + position.getSubDir();
//
//        // 临时文件
//        String tmpFilePath = dir + File.separator + position.getSubFileName() + PART_FILE_NAME + ".xlsx";
//
//        // 重定向文件
//        String filePath = dir + File.separator + position.getSubFileName() + ".xlsx";
//
//        // excel
//        ExportExcel exportExcel = null;
//        try {
//
//            exportExcel = new ExportExcel(null, ZftBillExport2Vo.class, "交易订单", null, null);
//
//        } catch (Exception e) {
//            log.error("{}", e.getMessage(), e);
//            return;
//        }
//
//        // 分页索引
//        Long start = 0L; // 偏移量从0开始
//        int size = 1000;
//
//        // 改成分页获取数据
//        while (true) {
//            // 获取订单列表
//            payBillParam.setSize(size);
//            payBillParam.setStart(start);
//
//            ListResponse<ZftBillVo> res = dataCoreFeignClient.zftBillList(payBillParam);
//            if (res == null || res.getData() == null || res.getData().size() == 0) {
//                log.warn("获取订单结束.");
//                break;
//            } else {
//                log.info("当前偏移量: start={}", start);
//                //通过偏移量计算当前页面
//                int index = (int) (Math.ceil(start/size)+1);
//                log.info("请求页码：index={}",index);
//                exportExcel.addDataList((index - 1) * size + 1, this.zftList2ExportVo(res.getData())); // 考虑数据开始行号
//
//                // 页码递增
//                start += 1000;
//            }
//        }
//        try {
//            File folder = new File(dir);
//            //如果文件夹不存在则创建
//            if (!folder.exists() && !folder.isDirectory()) {
//                folder.mkdirs();
//            }
//            exportExcel.writeFile(tmpFilePath);
//
//            //重命名:临时文件->最终文件名
//            new File(tmpFilePath).renameTo(new File(filePath));
//        } catch (IOException e) {
//            log.error("对账记录excel生成出错: err={}", e.getMessage(), e);
//        }
//        log.info("对账记录excel生成完成, filePath: {}", filePath);
    }

    private List<ZftBillExport2Vo> zftList2ExportVo(List<ZftBillVo> voList) {
        return voList.stream().map(this::zftList2ExportVo).collect(Collectors.toList());
    }

    private ZftBillExport2Vo zftList2ExportVo(ZftBillVo vo) {
        ZftBillExport2Vo exportVo = new ZftBillExport2Vo();

        if (vo.getPayTime() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            exportVo.setPayTime(sdf.format(vo.getPayTime()));
        }

        exportVo.setCheckResult(vo.getCheckResult() != null ? vo.getCheckResult().getDesc() : "--");
        exportVo.setZftName(vo.getZftName());
        exportVo.setCommName(vo.getCommName());
        exportVo.setPhone(vo.getPhone());
        exportVo.setAccountType(vo.getAccountType());
        exportVo.setChargeOrderNo(vo.getChargeOrderNo());
        exportVo.setOrderId(vo.getOrderId());
        exportVo.setPayWay(vo.getPayWay());
        exportVo.setTradeNo(vo.getTradeNo());
        exportVo.setFinancialType(vo.getFinancialType());

        if (DepositFlowType.IN_FLOW.equals(vo.getTradeType())) {
            exportVo.setChargeAmount(vo.getTradeAmount())
                .setRefundAmount(BigDecimal.ZERO)
                .setTradeType("收入");
        } else {
            exportVo.setChargeAmount(BigDecimal.ZERO)
                .setRefundAmount(BigDecimal.ZERO.subtract(vo.getTradeAmount()))
                .setTradeType("支出");
        }

        exportVo.setDailyBillName(vo.getDailyBillName());
//        exportVo.setChargeAmount(vo.getChargeAmount());
//        exportVo.setRefundAmount(vo.getRefundAmount());
//        exportVo.setRealAmount(vo.getChargeAmount().subtract(vo.getRefundAmount()));
        return exportVo;
    }


    private List<PayBillExportVo> map2ExportVo(List<PayBillVo> voList) {
        return voList.stream().map(vo -> this.map2ExportVo(vo, null)).collect(Collectors.toList());
    }

    private PayBillExportVo map2ExportVo(PayBillVo vo, Locale locale) {
        PayBillExportVo exportVo = new PayBillExportVo();
        //BeanUtils.copyProperties(vo, exportVo);
        exportVo.setOrderId(vo.getOrderId()); //订单号
        exportVo.setCusName(vo.getCusName()); //所属客户
        exportVo.setCusPhone(vo.getCusPhone());//手机号
        exportVo.setAmount(vo.getAmount()); //实际金额
        exportVo.setFreeAmount(vo.getFreeAmount());//赠送金额
        exportVo.setAmountBefore(vo.getAmountBefore());//充值前金额
        exportVo.setAmountAfter(vo.getAmountAfter());//充值后金额
        exportVo.setOpName(vo.getOpName());//操作人
        exportVo.setRemark(vo.getRemark());//备注
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        exportVo.setCreateTime(sdf.format(vo.getCreateTime()));//充值时间

        // 到账状态
        // 处理支付状态
        if (null == locale) {
            exportVo.setStatusDesc(PayBillStatus.valueOf(vo.getStatus()).getDesc());
        } else {
            exportVo.setStatusDesc(
                messageSource.getMessage(PayBillStatus.valueOf(vo.getStatus()).getDesc(), null,
                    locale));
        }

        // 转换enum
        if (null != vo.getStatus() && PayBillStatus.EXHAUST == PayBillStatus.valueOf(
            vo.getStatus())) {
            exportVo.setAmountIsExhausted("是");
        } else {
            exportVo.setAmountIsExhausted("否");
        }

        // 处理充值来源
        if (null != vo.getSourceType() && DepositSourceType.UNKNOWN != vo.getSourceType()) {
            exportVo.setSourceTypeDesc(null == locale ? vo.getSourceType().getDesc()
                : messageSource.getMessage(vo.getSourceType().getDesc(), null, locale));
        } else {
            exportVo.setSourceTypeDesc("--");
        }

        // 处理支付方式
        if (null != vo.getPayChannel() && PayChannel.UNKNOWN != vo.getPayChannel()) {
            exportVo.setPayChannelDesc(null == locale ? vo.getPayChannel().getDesc() :
                messageSource.getMessage(vo.getPayChannel().getDesc(), null, locale));
        } else {
            exportVo.setPayChannelDesc(
                null == locale ? "其他" : messageSource.getMessage("其他", null, locale));
        }

        if (null != vo.getAccountType() && PayAccountType.UNKNOWN != vo.getAccountType()) {
            exportVo.setAccountTypeDesc(vo.getAccountType().getDesc());
        } else {
            exportVo.setAccountTypeDesc("--");
        }

        if (StringUtils.isNotBlank(vo.getPayAccountName())) {
            exportVo.setPayAccountName(vo.getPayAccountName());
        } else {
            exportVo.setPayAccountName("--");
        }

        // 处理到账账户
        if (null != vo.getFlowInAccountType()
            && FlowInAccountType.UNKNOWN != vo.getFlowInAccountType()) {
            exportVo.setFlowInAccountTypeDesc(null == locale ? vo.getFlowInAccountType().getDesc()
                : messageSource.getMessage(vo.getFlowInAccountType().getDesc(), null, locale));
        } else {
            exportVo.setFlowInAccountTypeDesc(
                null == locale ? "其他" : messageSource.getMessage("其他", null, locale));
        }

        // 处理充值类型
        if (null != vo.getFlowType() && DepositFlowType.UNKNOWN != vo.getFlowType()) {
            exportVo.setFlowTypeDesc(null == locale ? vo.getFlowType().getDesc()
                : messageSource.getMessage(vo.getFlowType().getDesc(), null, locale));
        } else {
            exportVo.setFlowTypeDesc("--");
        }

        if (null != vo.getInvoiceMode()) {
            exportVo.setInvoiceTypeDesc(vo.getTaxStatus().getDesc());
        } else {
            exportVo.setInvoiceTypeDesc(TaxStatus.NO.getDesc());
        }

        if (null != vo.getTaxType() && TaxType.UNKNOWN != vo.getTaxType()) {
            exportVo.setTaxTypeDesc(vo.getTaxType().getDesc());
        } else {
            exportVo.setTaxTypeDesc("--");
        }

        if (null != vo.getExpressStatus() && ExpressStatus.UNKNOWN != vo.getExpressStatus()) {
            exportVo.setExpressStatusDesc(vo.getExpressStatus().getDesc());
        } else {
            exportVo.setExpressStatusDesc("--");
        }

        if (null != vo.getOpUserType() && UserType.UNKNOWN != vo.getOpUserType()) {
            exportVo.setOpUserTypeDesc(vo.getOpUserType().getDesc());
        } else {
            exportVo.setOpUserTypeDesc("--");
        }

        if (null != vo.getUserType() && UserType.UNKNOWN != vo.getUserType()) {
            exportVo.setUserTypeDesc(this.userTypeToDesc(vo.getUserType()));
        } else {
            exportVo.setUserTypeDesc("--");
        }

        if (null != vo.getTaxStatus()) {
            exportVo.setTaxStatusDesc(vo.getTaxStatus().getDesc());
        } else {
            exportVo.setTaxStatusDesc("--");
        }

        return exportVo;
    }

    private String userTypeToDesc(UserType userType) {
        if (userType == UserType.CUSTOMER) {
            return "普通客户";
        } else if (userType == UserType.CORP_USER) {
            return "企业客户";
        } else {
            return userType.getDesc();
        }
    }

    //    @Async
    public void exportZftThirdOrderList(
        ExcelPosition position, ListZftThirdOrderParam param) throws IOException {
        log.info("对账管理中的对账订单导出: {}", JsonUtils.toJsonString(position));
//        try {
        if (null != param.getCorpId()) {
            ExcelUtil.builder(exportFileConfig.getExcelDir(), position,
                    BiExportGroups.ZFT_THIRD_ORDER_TRADE.getName())
                .addHeader(ExportZftThirdOrderTradeVo.class)
                .loopAppendData((start, size) -> {
                    param.setStart((long) ((start - 1) * size))
                        .setSize(size);
                    param.setTotal(Boolean.FALSE);

                    List<ZftThirdOrderDto> all = this.zftThirdOrderRoDs.findAllZftThirdOrder(param);
                    return new ArrayList<>(all);
                }, list -> new ArrayList<>(list.stream()
                    .map(i -> (ZftThirdOrderDto) i)
                    .map(this::dtoMap2TradeVo)
                    .collect(Collectors.toList())))
                .write2File();
        } else {
            ExcelUtil.builder(exportFileConfig.getExcelDir(), position,
                    BiExportGroups.ZFT_THIRD_ORDER.getName())
                .addHeader(ExportZftThirdOrderVo.class)
                .loopAppendData((start, size) -> {
                    param.setStart((long) ((start - 1) * size))
                        .setSize(size);
                    param.setTotal(Boolean.FALSE);

                    List<ZftThirdOrderDto> all = this.zftThirdOrderRoDs.findAllZftThirdOrder(param);
                    return new ArrayList<>(all);
                }, list -> new ArrayList<>(list.stream()
                    .map(i -> (ZftThirdOrderDto) i)
                    .map(this::dtoMap2Vo)
                    .collect(Collectors.toList())))
                .write2File();
        }
        log.info("导出excel完成: {}", JsonUtils.toJsonString(position));
//        } catch (Exception e) {
//            log.error("导出excel异常: {}, err = {}",
//                    JsonUtils.toJsonString(position), e.getMessage(), e);
//        }
    }

//    private List<ExportZftThirdOrderVo> dtoMap2Vo(List<ZftThirdOrderDto> dtoList) {
//        return dtoList.stream()
//                .map(this::dtoMap2Vo)
//                .collect(Collectors.toList());
//    }

    private ExportZftThirdOrderVo dtoMap2Vo(ZftThirdOrderDto dto) {
        ExportZftThirdOrderVo vo = new ExportZftThirdOrderVo();
        vo.setCheckResult(dto.getCheckResult().getDesc())
            .setZftName(dto.getZftName())
            .setDailyBillName(dto.getDailyBillName())
            .setPlatformNo(dto.getPlatformNo())
            .setTradeTime(dto.getTradeTime())
            .setZftCommName(dto.getZftCommName())
            .setChannel(dto.getChannel().getDesc());

        if (DepositFlowType.IN_FLOW.equals(dto.getTradeType())) {
            vo.setTradeType("收入")
                .setInAmount(dto.getTradeAmount())
                .setOutAmount(BigDecimal.ZERO);
        } else {
            vo.setTradeType("支出")
                .setInAmount(BigDecimal.ZERO)
                .setOutAmount(BigDecimal.ZERO.subtract(dto.getTradeAmount()));
        }
        return vo;
    }

    private ExportZftThirdOrderTradeVo dtoMap2TradeVo(ZftThirdOrderDto dto) {
        ExportZftThirdOrderTradeVo vo = new ExportZftThirdOrderTradeVo();
        vo.setCheckResult(dto.getCheckResult().getDesc())
            .setPlatformNo(dto.getPlatformNo())
            .setChannel("支付宝")
            .setTradeTime(dto.getTradeTime())
            .setOpenOrderId(dto.getOpenOrderId());

        if (DepositFlowType.IN_FLOW.equals(dto.getTradeType())) {
            vo.setTradeType("收入")
                .setInAmount(dto.getTradeAmount())
                .setOutAmount(BigDecimal.ZERO);
        } else {
            vo.setTradeType("支出")
                .setInAmount(BigDecimal.ZERO)
                .setOutAmount(BigDecimal.ZERO.subtract(dto.getTradeAmount()));
        }
        return vo;
    }
}
