package com.cdz360.biz.bi.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.bi.config.ExportFileConfig;
import com.cdz360.biz.bi.feign.reactor.BizTjFeignClient;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.tj.kc.param.ListTjDailyChargingDurationParam;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo;
import com.cdz360.biz.model.tj.kc.vo.TjDailyChargingDurationExportVo;
import com.cdz360.biz.model.trading.bi.dto.BiExportGroups;
import com.chargerlinkcar.framework.common.utils.ExcelUtil;
import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TjDailyChargingDurationService {

    @Autowired
    private ExportFileConfig exportFileConfig;

    @Autowired
    private BizTjFeignClient tjFeignClient;

    public void exportTjDailyChargingDurationListExcel(ExcelPosition position, ListTjDailyChargingDurationParam param) throws IOException {
        log.info("日充电时长列表导出到EXCEL: {}", JsonUtils.toJsonString(position));
        AtomicInteger idx = new AtomicInteger();
        ExcelUtil.builder(exportFileConfig.getExcelDir(), position,
                BiExportGroups.TJ_DAILY_CHARGING_DURATION_LIST.getName())
            .addHeader(TjDailyChargingDurationExportVo.class)
            .loopAppendData((start, size) -> {
                param.setStart((long) ((start - 1) * size))
                    .setSize(size);
                param.setTotal(Boolean.FALSE);

                ListResponse<TjDailyChargingDurationPo> block = this.tjFeignClient.findTjDailyChargingDuration(param)
                    .block(Duration.ofSeconds(50L));
                List<TjDailyChargingDurationPo> data = new ArrayList<>();
                if (block != null) {
                    data = block.getData();
                }
                return new ArrayList<>(data);
            }, list -> new ArrayList<>(list.stream()
                .map(i -> {
                    TjDailyChargingDurationPo tjDailyChargingDurationPo = (TjDailyChargingDurationPo) i;
                    TjDailyChargingDurationExportVo result = new TjDailyChargingDurationExportVo();
                    BeanUtils.copyProperties(tjDailyChargingDurationPo, result);
                    result.setIdx(idx.getAndIncrement());
                    return result;
                })
                .collect(Collectors.toList())))
            .write2File();
    }

}
