package com.cdz360.biz.bi.domain.vo;

import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.biz.model.annotations.ExcelField;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "车辆详情订单导出头部")
@EqualsAndHashCode(callSuper = true)
public class VinExportHeaderVo extends BaseObject {

    @ExcelField(title = "VIN码", sort = 1)
    private String vin;

    @ExcelField(title = "车牌号", sort = 2)
    private String carNo;

    @ExcelField(title = "车队", sort = 3)
    private String carDepart;

    @ExcelField(title = "路线", sort = 4)
    private String lineNum;

    @ExcelField(title = "车辆自编号", sort = 5)
    private String carNum;

    @ExcelField(title = "品牌", sort = 6)
    private String brand;

    @ExcelField(title = "型号", sort = 7)
    private String model;

    @ExcelField(title = "车长(米)", sort = 8)
    private BigDecimal carLength;

    @ExcelField(title = "年份", sort = 9)
    private String year;

}




