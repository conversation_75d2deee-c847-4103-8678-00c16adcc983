package com.cdz360.biz.bi.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.bi.service.OrderService;
import com.cdz360.biz.model.trading.order.dto.LowKwOrderDto;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.param.ListChargerOrderParamX;
import com.cdz360.biz.model.trading.order.po.ChargerOrderPo;
import com.cdz360.biz.model.trading.site.vo.CorpOrderCountVo;
import com.chargerlinkcar.framework.common.domain.OrderCountParam;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDataVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDetailVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import io.swagger.annotations.ApiOperation;
import io.swagger.v3.oas.annotations.Operation;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
//@Observed
@RestController
public class OrderRest {

    @Autowired
    private OrderService orderService;

    /**
     * 订单列表 分页page： current 页码  size 每页显示数量
     *
     * @return
     */
    @PostMapping("/bi/order/queryChargeOrderList")
    public ListResponse<ChargerOrderVo> queryChargeOrderList(
        @RequestBody ChargerOrderParam chargerOrderParam) {
        return biQueryChargeOrderList(chargerOrderParam);
    }

    /**
     * 订单列表 分页page： current 页码  size 每页显示数量 用于bi统计图的订单查询
     *
     * @return
     */
    @PostMapping("/bi/order/biQueryChargeOrderList")
    public ListResponse<ChargerOrderVo> biQueryChargeOrderList(
        @RequestBody ChargerOrderParam chargerOrderParam) {
        log.debug("param = {}", chargerOrderParam);
//        Page<ChargerOrderVo> page = new Page<>(chargerOrderParam.getCurrent(), chargerOrderParam.getSize(),
//                chargerOrderParam.getTotal());
        return orderService.queryChargeOrderList(chargerOrderParam, null);
    }

    /**
     * 根据条件查询订单统计数据
     *
     * @param chargerOrderParam
     * @return
     */
    @PostMapping("/bi/order/getChargerOrderData")
    public ObjectResponse<ChargerOrderDataVo> getChargerOrderData(
        @RequestBody ChargerOrderParam chargerOrderParam) {
        ObjectResponse<ChargerOrderDataVo> res = orderService.getChargerOrderData(
            chargerOrderParam);
        return res;
    }

    /**
     * excel汇总数据导出使用
     *
     * @param chargerOrderParam
     * @return
     */
    @PostMapping("/bi/order/exportChargerOrderData")
    public ListResponse<ChargerOrderDataVo> exportChargerOrderData(
        @RequestBody ChargerOrderParam chargerOrderParam) {
        //调用服务查询
        ListResponse<ChargerOrderDataVo> res = orderService.exportChargerOrderData(
            chargerOrderParam);
        return res;
    }

    /**
     * 导出excel用
     *
     * @param chargerOrderParam
     * @return
     */
    @Operation(summary = "导出excel用")
    @PostMapping("/bi/order/exportChargeOrderListV2")
    public ListResponse<ChargerOrderVo> exportChargeOrderListV2(
        @RequestBody ChargerOrderParam chargerOrderParam) {
        return orderService.exportChargeOrderListV2(chargerOrderParam);
    }

    /**
     * 根据条件查出订单统计数据（包含尖峰平谷）
     */
    @PostMapping("/bi/order/getChargerOrderDetail")
    public ObjectResponse<ChargerOrderDetailVo> getChargerOrderDetail(
        @RequestBody ChargerOrderParam chargerOrderParam) {
        //调用服务查询
        log.debug("param = {}", chargerOrderParam);
        ObjectResponse<ChargerOrderDetailVo> res = orderService.getChargerOrderDetail(
            chargerOrderParam);
        log.debug("res = {}", res);
        return res;
    }

    /**
     * 根据条件查出企业授信待支付订单数目
     */
    @PostMapping(value = "/bi/order/corpOrderCount")
    ListResponse<CorpOrderCountVo> corpOrderCount(@RequestBody OrderCountParam param) {
        log.debug("param = {}", param);
        List<CorpOrderCountVo> res = orderService.corpOrderCount(param);
        log.debug("res = {}", res);
        return new ListResponse<>(res);
    }


    /**
     * 查询最近 n 个 lowKwDur 为 null 的订单号
     *
     * @param size 查询的数据量
     * @return 返回订单号
     */
    @GetMapping(value = "/bi/order/list4LowKwAnalyse")
    public ListResponse<String> list4LowKwAnalyse(@RequestParam int size) {
        List<String> list = orderService.list4LowKwAnalyse(size);
        return RestUtils.buildListResponse(list);
    }


    @ApiOperation("查询充电订单列表(充电订单页面专用接口，不能他用)")
    // 查询充电订单列表(充电订单页面专用接口，不能他用)
    @PostMapping(value = "/bi/order/queryChargeOrder")
    public Mono<ListResponse<ChargerOrderVo>> queryChargeOrderListX(ListChargerOrderParamX param) {
        return this.orderService.queryChargeOrderListX(param);
    }

    @PostMapping("/bi/order/getLowKwOrderList")
    public ListResponse<LowKwOrderDto> getLowKwOrderList(@RequestBody ListChargeOrderParam param) {
        ListResponse<LowKwOrderDto> res = orderService.getLowKwOrderList(param);
        return res;
    }

    /**
     * 查询未支付的信用充订单
     * @return 返回订单号列表
     */
    @PostMapping("/bi/order/getUnpaidCreditOrderNoList")
    public ListResponse<String> getUnpaidCreditOrderNoList(
        @RequestBody ListChargeOrderParam param) {
        List<String> orderNoList = orderService.getUnpaidCreditOrderNoList(param);
        return RestUtils.buildListResponse(orderNoList);
    }
}
