package com.cdz360.biz.bi.domain.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class ChargerOrderDailyExportVO implements Serializable {

    @ExcelField(title = "日期", sort = 1)
    private String dailyDay;

    @ExcelField(title = "标准服务费(元)", sort = 2)
    private BigDecimal servOriginFee;

    @ExcelField(title = "总服务费(元)", sort = 3)
    private BigDecimal servicePrice;

    @ExcelField(title = "标准电费(元)", sort = 4)
    private BigDecimal elecOriginFee;

    @ExcelField(title = "总电费(元)", sort = 5)
    private BigDecimal elecPrice;

    @ExcelField(title = "充电总金额(元)", sort = 6)
    private BigDecimal orderPrice;

    @ExcelField(title = "停充超时费(元)", sort = 7)
    private BigDecimal parkingFee;

    @ExcelField(title = "服务费收益(元)", sort = 8)
    private BigDecimal servProfit;

    @ExcelField(title = "电费收益(元)", sort = 9)
    private BigDecimal elecProfit;

    @ExcelField(title = "总收益(元)", sort = 10)
    private BigDecimal totalProfit;

    @ExcelField(title = "直付收款(元)", sort = 11)
    private BigDecimal zftTotalMoney;

    @ExcelField(title = "对账差额(元)", sort = 12)
    private BigDecimal checkDiffMoney;
}
