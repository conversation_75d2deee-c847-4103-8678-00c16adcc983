package com.cdz360.biz.bi.feign;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.order.param.ListSettlementOrderParam;
import com.cdz360.biz.model.trading.file.po.OssFilePo;
import com.cdz360.biz.model.trading.invoice.dto.InvoiceRecordOrderRefDto;
import com.cdz360.biz.model.trading.invoice.param.ListInvoiceRecordOrderRefParam;
import com.cdz360.biz.model.trading.iot.vo.EvseInfoVo;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.model.trading.order.param.ZftBillParam;
import com.cdz360.biz.model.trading.order.vo.PayBillVo;
import com.cdz360.biz.model.trading.order.vo.SettlementOrderVo;
import com.cdz360.biz.model.trading.order.vo.ZftBillVo;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceRecordDetail;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderSite;
import com.chargerlinkcar.framework.common.domain.vo.OrderBiVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderTimeShareBiVo;
import com.chargerlinkcar.framework.common.domain.vo.SiteInMongoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2019/11/6 15:24
 */
@Slf4j
@Component
public class HystrixDataCoreFeignClientFactory implements FallbackFactory<DataCoreFeignClient> {
    @Override
    public DataCoreFeignClient create(Throwable cause) {
        return new DataCoreFeignClient() {
            @Override
            public ListResponse<PayBillVo> payBillList(PayBillParam payBillParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<ZftBillVo> zftBillList(ZftBillParam payBillParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SettlementOrderVo> getSettlementOrderList(ListSettlementOrderParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<EvseInfoVo> getOfflineEvseList(ListEvseParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<CorpInvoiceRecordDetail> corpInvoiceRecordDetail(String applyNo) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<ChargerOrderSite> chargerOrderGroupBySite(
                ListChargeOrderParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<ChargerOrderSite> recordOrderGroupBySite(
                ListInvoiceRecordOrderRefParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<OrderTimeShareBiVo> chargerOrderGroupByTimeShareFee(
                ListChargeOrderParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<OrderTimeShareBiVo> recordOrderGroupByTimeShareFee(
                ListInvoiceRecordOrderRefParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<OrderBiVo> invoiceOrderBiForCorp(PayBillParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<InvoiceRecordOrderRefDto> getInvoiceRecordOrderList(
                ListInvoiceRecordOrderRefParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<OssFilePo> addFile(OssFilePo param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SiteInMongoVo> getSiteListFromMongo(ListSiteParam param) {
                return RestUtils.serverBusy4ListResponse();
            }
        };
    }
}
