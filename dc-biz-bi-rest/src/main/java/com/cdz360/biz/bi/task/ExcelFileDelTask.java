package com.cdz360.biz.bi.task;

import com.cdz360.biz.bi.service.ExcelFileService;
import com.cdz360.biz.bi.service.PdfService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> 删除tmp目录下的文件
 * @since 2019/8/6
 **/
@Slf4j
@Component
public class ExcelFileDelTask {

    @Autowired
    private ExcelFileService excelFileService;
    @Autowired
    private PdfService pdfService;


    @Scheduled(cron = "0 0 4 * * ?")
    //@Scheduled(cron = "* * * * * ?")
    public void scheduled() {
        log.info("=====>>>>>定时清理excel文件  {}", System.currentTimeMillis());
        excelFileService.cleanExcelFiles();
        log.info("=====>>>>>定时清理pdf文件  {}", System.currentTimeMillis());
        pdfService.cleanPdfFiles();
    }
}
