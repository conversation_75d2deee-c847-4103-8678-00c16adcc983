package com.cdz360.biz.bi.service.download.impl;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.bi.service.download.IFileExport;
import com.cdz360.biz.bi.service.ess.EssBiService;
import com.cdz360.biz.ess.model.data.po.EssEquipTimelyPo;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.ess.param.ListEssDailyParam;
import com.cdz360.biz.model.ess.vo.ExTimelyElecVo;
import com.chargerlinkcar.framework.common.utils.DateUtils;
import com.chargerlinkcar.framework.common.utils.ExcelUtil;
import java.io.IOException;
import java.time.Duration;
import java.util.List;
import java.util.stream.Collectors;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CommEssTimelyElecExportImpl extends
    AbstractFileExport<ListEssDailyParam, ExcelPosition>
    implements IFileExport<ListEssDailyParam, ExcelPosition> {

    @Autowired
    private EssBiService essBiService;

    @PostConstruct
    public void init() {
        downloadFileProxy.addProxy(DownloadFunctionType.ESS_TIMELY_ELEC, this);
    }

    @Override
    public Class<ListEssDailyParam> paramClazz() {
        return ListEssDailyParam.class;
    }

    @Override
    public void genFile(String context, String pos) throws IOException {
        ListEssDailyParam timelyParam = this.convert(context);
        timelyParam.setTotal(false);
        ExcelUtil.builder(exportFileConfig.getExcelDir(), this.convertPos(pos), "分时段明细")
            .addHeader(ExTimelyElecVo.class)
            .loopAppendData(100, (start, size) -> {
                timelyParam.setStart((long) ((start - 1) * size))
                    .setSize(size);

                List<EssEquipTimelyPo> data = essBiService.getTimelyElecList(
                    timelyParam).map(ListResponse::getData)
                    .block(Duration.ofMinutes(2L));

                if (null == data) {
                    return List.of();
                }

                return data.stream().map(x -> {
                        ExTimelyElecVo temp = new ExTimelyElecVo();
                        BeanUtils.copyProperties(x, temp);
                        temp.setDate(DateUtils.toDate(x.getStartTime().toLocalDate()))
                            .setFromTime(DateUtils.toDate(x.getStartTime()))
                            .setToTime(DateUtils.toDate(x.getEndTime()));
                        return temp;
                    })
                    .collect(Collectors.toList());
            })
            .write2File();
    }
}
