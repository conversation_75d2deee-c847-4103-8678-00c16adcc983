package com.cdz360.biz.bi.service.site;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ds.trading.ro.order.ds.BiSiteFinanceMonthRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.BiSiteMonthExpenseRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.BiSiteMonthIncomeRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.BiSiteOrderRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteOaDefaultConfigRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.model.oa.constant.OaConstants;
import com.cdz360.biz.model.oa.vo.OaElecPayIncomeExpenseVo;
import com.cdz360.biz.model.site.vo.BiSiteExpenseSumByMonthVo;
import com.cdz360.biz.model.site.vo.BiSiteIncomeSumByMonthVo;
import com.cdz360.biz.model.site.vo.SiteIncomeExpenseVo;
import com.cdz360.biz.model.trading.order.type.BiDependOnType;
import com.cdz360.biz.model.trading.profit.sett.type.ProfitCfgTimeTarget;
import com.cdz360.biz.model.trading.site.param.SettJobProfitBiParam;
import com.cdz360.biz.model.trading.site.param.SiteIncomeExpenseParam;
import com.cdz360.biz.model.trading.site.param.SiteReportParam;
import com.cdz360.biz.model.trading.site.po.SiteOaDefaultConfigPo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.site.vo.BiSiteGcMonthExpenseStatisticVo;
import com.cdz360.biz.model.trading.site.vo.BiSiteGcMonthIncomeVo;
import com.cdz360.biz.model.trading.site.vo.SettJobPowerUseRateVo;
import com.cdz360.biz.model.trading.site.vo.SiteBaseInfoVo;
import com.cdz360.biz.oa.dto.BatchTaskVo;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class SiteIncomeExpenseService {

    private static final DateTimeFormatter TIME_FORMATTER =
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private BiSiteOrderRoDs biSiteOrderRoDs;

    @Autowired
    private ChargerOrderRoDs chargerOrderRoDs;

    @Autowired
    private BiSiteFinanceMonthRoDs biSiteFinanceMonthRoDs;

    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private BiSiteMonthIncomeRoDs siteMonthIncomeRoDs;

    @Autowired
    private BiSiteMonthExpenseRoDs siteMonthExpenseRoDs;

    @Autowired
    private SiteOaDefaultConfigRoDs siteOaDefaultConfigRoDs;

    public Mono<List<SiteIncomeExpenseVo>> siteSixMonthIncomeExpense(
        String siteId, LocalDate month) {
        IotAssert.isNotBlank(siteId, "场站ID不能为空");
        IotAssert.isNotNull(month, "请指定月份信息");

        val site = siteRoDs.getSite(siteId);
        IotAssert.isNotNull(site, "场站ID无效");

        val param = new SiteIncomeExpenseParam()
            .setSiteId(siteId)
            .setMonthList(new ArrayList<>(6));

        // 计算近六个月
        val last = month.with(TemporalAdjusters.firstDayOfMonth());
        param.getMonthList().add(last);
        for (int i = 1; i <= 5; i++) {
            param.getMonthList().add(last.minusMonths(i));
        }
        return this.siteIncomeExpenseSumByMonth(param);
    }

    public Mono<List<OaElecPayIncomeExpenseVo>> getOaElecPayTaskListBySiteId(List<BatchTaskVo> voList) {
        IotAssert.isTrue(CollectionUtils.isNotEmpty(voList), "voList不能为空");
        List<String> distinctList = voList.stream().map(BatchTaskVo::getSiteId).distinct()
            .collect(Collectors.toList());

        var siteOaDefaultConfigList = siteOaDefaultConfigRoDs.findByCondition(distinctList,
            OaConstants.PD_ELEC_PAY);
        Map<String, Boolean> siteDiscountShowMap = siteOaDefaultConfigList.stream()
            .collect(Collectors.toMap(SiteOaDefaultConfigPo::getSiteId, e -> {
                return Optional.ofNullable(e.getDefaultValue()).map(x -> {
                    return x.stream().filter(o -> o.getLabel()
                            .equals(OaConstants.OA_ELEC_PAY_DEFAULT_LABEL_DISCOUNT_SHOW))
                        .map(o -> Boolean.parseBoolean(o.getValue())).findFirst().orElse(false);
                }).orElse(false);
            }));

        return Mono.just(voList)
            .flatMapMany(Flux::fromIterable)
            .flatMap(taskVo -> {
                String siteId = taskVo.getSiteId();
                IotAssert.isNotBlank(siteId, "场站ID不能为空");

                val site = siteRoDs.getSite(siteId);
                IotAssert.isNotNull(site, "场站ID无效");

                val param = new SiteIncomeExpenseParam()
                    .setSiteId(siteId)
                    .setMonthList(new ArrayList<>(1));

                // 先确定本月，根据本月确定上一个存在有效数据月
                LocalDate maxBillDate = taskVo.getMaxBillDate();
                LocalDate currentMonth = maxBillDate
                    .with(TemporalAdjusters.firstDayOfMonth());
                // param.getMonthList().add(currentMonth); // 不需要本月的数据

                // 查找最近一个有数据的月份（向前查找最多12个月）
                LocalDate searchMonth = currentMonth.minusMonths(1);
                boolean foundValidMonth = false;
                for (int i = 1; i <= 12 && !foundValidMonth; i++) {
                    // 创建临时参数用于查询单个月份
                    val tempParam = new SiteIncomeExpenseParam().setSiteId(siteId)
                        .setMonthList(List.of(searchMonth));

                    // 查询该月份的收入和支出数据
                    val incomeData = siteMonthIncomeRoDs.siteIncomeSumByMonth(tempParam);
                    val expenseData = siteMonthExpenseRoDs.siteExpenseSumByMonth(tempParam);

                    // 如果该月份有收入或支出数据，则认为是有效数据月
                    boolean valid = incomeData.stream().anyMatch(
                        e -> DecimalUtils.gtZero(e.getElec()) || DecimalUtils.gtZero(e.getElecFee())
                            || DecimalUtils.gtZero(e.getServFee()));
                    valid = valid || expenseData.stream().anyMatch(
                        e -> DecimalUtils.gtZero(e.getFee()) || DecimalUtils.gtZero(e.getElecFee())
                            || DecimalUtils.gtZero(e.getNumber()));
                    if (valid) {
                        param.getMonthList().add(searchMonth);
                        foundValidMonth = true;
                    } else {
                        searchMonth = searchMonth.minusMonths(1);
                    }
                }

                Date from = DateUtil.timeStr2Date(taskVo.getBillStartDate());
                Date end = DateUtil.timeStr2Date(taskVo.getBillEndDate());
                SettJobPowerUseRateVo settVo = biSiteOrderRoDs.getSettJobPowerUseRateVo(siteId,
                    BiDependOnType.STOP_TIME, from, end);

                log.info("siteIncomeExpenseSumByMonth param = {}", JsonUtils.toJsonTimeString(param));
                return this.siteIncomeExpenseSumByMonth(param).map(e -> {
                    OaElecPayIncomeExpenseVo result = new OaElecPayIncomeExpenseVo();
                    result.setSiteId(siteId);
                    result.setMaxBillDate(maxBillDate);
                    result.setDefaultShowDiscount(siteDiscountShowMap.get(siteId));

                    final Date online =
                        site.getOnlineDate() != null ? site.getOnlineDate() : new Date();
                    DateUtil.calcDuration(from.before(online) ? online : from, end)
                        .ifPresent(x -> result.setCurrPeriodDays(x.toDays()));

                    if (settVo != null) {
                        result.setCurrPeriodPower(settVo.getPower());
                    }
                    if (CollectionUtils.isNotEmpty(e)) {
                        result.setSiteIncomeExpenseVo(e.get(0));
                    }
                    return result;
                });
            })
            .collectList();
    }

    public Mono<List<SiteIncomeExpenseVo>> siteIncomeExpenseSumByMonth(
        SiteIncomeExpenseParam param) {
        final SitePo site = siteRoDs.getSite(param.getSiteId()); // 获取场站上线时间
        IotAssert.isNotNull(site, "场站ID无效");
        final Date online = site.getOnlineDate() != null ? site.getOnlineDate() : new Date();

        return Mono.zip(Mono.just(siteMonthIncomeRoDs.siteIncomeSumByMonth(param)),
                Mono.just(siteMonthExpenseRoDs.siteExpenseSumByMonth(param)))
            .map(t -> {
                val income = t.getT1().stream()
                    .collect(Collectors.toMap(BiSiteIncomeSumByMonthVo::getMonth, o -> o));
                val expense = t.getT2().stream()
                    .collect(Collectors.toMap(BiSiteExpenseSumByMonthVo::getMonth, o -> o));

                return param.getMonthList().stream().map(month -> {
                    val incomeVo = income.get(month);
                    val expenseVo = expense.get(month);

                    // 场站当月效率
                    BigDecimal efficiency = BigDecimal.ZERO;
                    SettJobPowerUseRateVo power = biSiteOrderRoDs.settJobPowerUseRate(
                        new SettJobProfitBiParam()
                            .setSiteId(param.getSiteId())
                            .setTimeTarget(ProfitCfgTimeTarget.STOP_TIME)
                            .setMonth(month));
                    if (null != power && null != power.getElec() && null != power.getPower()) {
                        LocalDateTime from = month.atStartOfDay();
                        LocalDateTime middle = online.toInstant().atOffset(ZoneOffset.of("+8"))
                            .toLocalDateTime();
                        long len = Duration.between(from.isBefore(middle) ? middle : from,
                            month.plusMonths(1).atStartOfDay()).toDays();

                        // 月充电量 / （ 有效功率 * 天数 * 24 ）
                        if (len > 0 && DecimalUtils.gtZero(power.getPower())) {
                            efficiency = power.getElec()
                                .divide(power.getPower()
                                    .multiply(new BigDecimal(len * 24)), 6, RoundingMode.HALF_DOWN);
                        }
                    }

                    return new SiteIncomeExpenseVo()
                        .setMonth(month)
                        .setSiteId(param.getSiteId())
                        .setEfficiency(efficiency)
                        .setIncome((incomeVo == null ?
                            new BiSiteIncomeSumByMonthVo() : incomeVo)) // 收入
                        .setExpense(expenseVo == null ?
                            new BiSiteExpenseSumByMonthVo() : expenseVo); // 支出
                }).collect(Collectors.toList());
            });
    }

    public Mono<ListResponse<BiSiteGcMonthIncomeVo>> siteIncomeSumByMonth(SiteReportParam param) {
        return Mono.just(siteMonthIncomeRoDs.siteIncomeSumByMonth(param))
            .map(RestUtils::buildListResponse);
    }

    public Mono<ListResponse<BiSiteGcMonthExpenseStatisticVo>> getSiteExpenseMonthly(
        SiteReportParam param) {
        return Mono.just(siteMonthExpenseRoDs.siteExpenseMonthly(param))
            .map(RestUtils::buildListResponse);
    }

    public Mono<ObjectResponse<SiteBaseInfoVo>> getSiteBaseInfo(
        SiteReportParam param) {
        return Mono.just(param)
            .map(e -> {
                return Optional.ofNullable(siteRoDs.getSiteBaseInfoVo(param))
                    .map(ei -> {
                        final BigDecimal allElecDataBySiteId = chargerOrderRoDs.getAllElecDataBySiteId(
                            param);
                        return ei.setElecTotal(allElecDataBySiteId);
                    })
//                    .map(ei -> {
//                        final BigDecimal elecSum = siteMonthIncomeRoDs.elecMonthly(param);
//                        return ei.setElecGap(ei.getElecTotal().subtract(elecSum));
//                    })
                    .orElse(new SiteBaseInfoVo());
            })
            .map(RestUtils::buildObjectResponse);
    }
}
