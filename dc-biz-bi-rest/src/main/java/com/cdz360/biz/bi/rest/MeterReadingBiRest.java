package com.cdz360.biz.bi.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.bi.service.ExcelBiService;
import com.cdz360.biz.bi.service.MeterReadingBiService;
import com.cdz360.biz.model.bi.site.ExcelBiPosition;
import com.cdz360.biz.model.bi.site.MeterReadingBi;
import com.cdz360.biz.model.iot.param.MeterRecordBiParam;
import com.cdz360.biz.model.iot.param.SiteBiParam;
import com.cdz360.biz.model.trading.meter.vo.MeterReadingTopVo;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * MeterReadingBiRest
 *
 * @since 9/25/2020 7:14 PM
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/bi/meter")
@Tag(name = "商户相关的统计接口", description = "按照商户统计订单")
public class MeterReadingBiRest {


    @Autowired
    private MeterReadingBiService meterReadingBiService;

    @Autowired
    private ExcelBiService excelBiService;

    @PostMapping(value = "/meterReadingBi")
    ListResponse<MeterReadingBi> meterReadingBi(@RequestBody SiteBiParam siteBiTopParam) {
        log.info("抄表电损柱状图: {}", JsonUtils.toJsonString(siteBiTopParam));

        return new ListResponse<>(meterReadingBiService.meterReadingBi(siteBiTopParam));
    }

    @PostMapping(value = "/meterReadingTopBi")
    ListResponse<MeterReadingTopVo> meterReadingTopBi(@RequestBody SiteBiParam siteBiTopParam) {
        log.info("抄表电损电表排行: {}", JsonUtils.toJsonString(siteBiTopParam));

        return new ListResponse<>(meterReadingBiService.meterReadingTopBi(siteBiTopParam));
    }

    @PostMapping(value = "/exportBiSiteMeterRecord")
    ObjectResponse<ExcelBiPosition> exportBiSiteMeterRecord(@RequestBody MeterRecordBiParam param) {
        throw new DcServiceException("接口已废弃，请联系开发");
//        log.info("param = {}", param);
//        ExcelBiPosition position = new ExcelBiPosition();
//        position.setSubFileName(UUIDUtils.getUuid32());
//        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
//        log.info("exportBiSiteMeterRecord 启动,position:{}", JsonUtils.toJsonString(position));
//        param.setExcelPosition(position);
//        param.setSheetName("抄表统计");
//        excelBiService.exportBiSiteMeterRecordSync(param);
//        return new ObjectResponse<>(position);
    }

}