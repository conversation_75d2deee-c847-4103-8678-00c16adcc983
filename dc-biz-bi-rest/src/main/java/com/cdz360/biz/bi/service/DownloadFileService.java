package com.cdz360.biz.bi.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.bi.config.ExportFileConfig;
import com.cdz360.biz.bi.service.download.DownloadFileProxy;
import com.cdz360.biz.ds.trading.ro.download.ds.DownloadJobRoDs;
import com.cdz360.biz.model.download.dto.DownloadJobDto;
import com.cdz360.biz.model.download.param.DownloadFileParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Comparator;
import java.util.stream.Stream;

@Slf4j
@Service
public class DownloadFileService {

    private final static DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");

    @Autowired
    private ExportFileConfig exportFileConfig;

    @Autowired
    private DownloadJobRoDs downloadJobRoDs;

    @Autowired
    private ExcelFileService excelFileService;

    @Autowired
    private PdfService pdfService;

    @Autowired
    private DownloadFileProxy downloadFileProxy;

    @Async
    public void generateFile(DownloadJobDto downloadJobDto) {
        downloadFileProxy.generate(downloadJobDto);
    }

    public void downloadFile(DownloadFileParam param, HttpServletResponse response) {
        String subDir = param.getSubDir();
        String subFileName = param.getSubFileName();
        log.info("down文件subDir:{}, subFileName:{}", subDir, subFileName);
        if (StringUtils.isEmpty(subDir)) {
            throw new DcArgumentException("参数错误");
        }
        if (DownloadFileType.EXCEL_XLSX.equals(param.getFileType())) {
            excelFileService.downFile(subDir, subFileName, response);
        } else if (DownloadFileType.PDF.equals(param.getFileType())) {
            pdfService.downFile(subDir, subFileName, response);
        }
    }

    public Mono<BaseResponse> clearDownloadFile(Integer days) {
        LocalDate beforeDate = LocalDate.now().minusDays(days);
        this.clearFile(exportFileConfig.excelDownloadExtDir(), beforeDate);
        this.clearFile(exportFileConfig.pdfDownloadExtDir(), beforeDate);
        return Mono.just(RestUtils.success());
    }

    private void clearFile(String path, LocalDate endDate) {
        try {
            File dir = new File(path);
            File[] needDel = dir.listFiles(File::isDirectory);
            if (null == needDel) {
                return;
            }
            log.info(">> 清除文件: path = {}", path);
            Arrays.stream(needDel)
                .filter(file -> LocalDate.parse(file.getName(), DATE_FORMATTER).isBefore(endDate))
                .forEach(file -> {
                    try (Stream<Path> walk = Files.walk(Paths.get(file.getPath()))) {
                        walk.sorted(Comparator.reverseOrder())
                            .forEach(p -> {
                                try {
                                    Files.delete(p);
                                } catch (IOException e) {
                                    log.warn("无法删除的路径: path = {}, err = {}", p,
                                        e.getMessage(), e);
                                }
                            });
                    } catch (Exception e) {
                        log.error("子文件夹命名不合规范, filename = {}", file.getName());
                    }
                });
            log.info("<< 清除文件: path = {}", path);
        } catch (Exception e) {
            log.error("清理文件异常: path = {}, endDate = {}, err = {}",
                path, endDate, e.getMessage(), e);
        }
    }
}
