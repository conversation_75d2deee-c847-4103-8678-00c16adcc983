package com.cdz360.biz.bi.service.ess;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.model.base.param.TimeFilter2;
import com.cdz360.base.model.base.type.SummaryType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.bi.config.ExportFileConfig;
import com.cdz360.biz.ds.ess.ro.data.ds.EssDailyRoDs;
import com.cdz360.biz.ds.ess.ro.data.ds.EssEquipTimelyRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ess.model.data.param.DayKwhParam;
import com.cdz360.biz.ess.model.data.param.EssBaseParam;
import com.cdz360.biz.ess.model.data.po.EssEquipTimelyPo;
import com.cdz360.biz.ess.model.data.vo.DaySiteEssRtDataBi;
import com.cdz360.biz.ess.model.data.vo.EssDataBi;
import com.cdz360.biz.ess.model.data.vo.EssEquipDailyElecVo;
import com.cdz360.biz.ess.model.dto.EssEquipDailyElecBiDto;
import com.cdz360.biz.ess.model.dto.EssEquipDailyElecBiDto.Kv;
import com.cdz360.biz.ess.model.dto.EssTimelyDataDto;
import com.cdz360.biz.ess.model.param.SummaryParam;
import com.cdz360.biz.ess.model.vo.EssDataSummary;
import com.cdz360.biz.ess.model.vo.EssRecentData;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.ess.param.ListEssDailyParam;
import com.cdz360.biz.model.iot.param.SiteBiParam;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import com.cdz360.biz.model.trading.bi.dto.BiExportGroups;
import com.cdz360.biz.model.trading.comm.dto.CommSiteEssCount;
import com.cdz360.biz.model.trading.ess.vo.DayEssDataBi;
import com.cdz360.biz.model.trading.ess.vo.ExportEssDataBiVo;
import com.cdz360.biz.model.trading.ess.vo.ExportExportEssDataBiDayVo;
import com.cdz360.biz.model.trading.ess.vo.ExportExportEssDataBiMonthVo;
import com.cdz360.biz.utils.feign.iot.DeviceFeignClient;
import com.chargerlinkcar.framework.common.feign.CommercialFeignClient;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.DateUtils;
import com.chargerlinkcar.framework.common.utils.ExcelUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class EssBiService {

    private static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM");

    @Autowired
    private DeviceFeignClient deviceFeignClient;

    @Autowired
    private ExportFileConfig exportFileConfig;

    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private EssDailyRoDs essDailyRoDs;

    @Autowired
    private CommercialFeignClient commercialFeignClient;

    //    @Value("${excel.dir:/tmp/excel}")
//    private String TEMP_DIR;
    @Autowired
    private EssEquipTimelyRoDs essEquipTimelyRoDs;

    private static Class<?> getHeaderClass(SiteBiSampleType type) {
        if (SiteBiSampleType.MONTH.equals(type)) {
            return ExportExportEssDataBiMonthVo.class;
        }
        return ExportExportEssDataBiDayVo.class;
    }

    public void exportSiteEssBiExcel(ExcelPosition position, SiteBiParam biParam) {
        this.siteEssBi(biParam)
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .doOnNext(data -> {
                try {
                    this.exportSiteEssBiExcel(position, biParam.getSampleType(), data);
                } catch (IOException e) {
                    log.error("导出excel异常: {}, err = {}",
                        JsonUtils.toJsonString(position), e.getMessage(), e);
                    throw new DcServiceException(e.getMessage());
                }
            })
            .block(Duration.ofSeconds(50L));
    }

    //    @Async
    public void exportSiteEssBiExcel(ExcelPosition position, SiteBiSampleType sampleType,
        List<DayEssDataBi> data)
        throws IOException {
        log.info("场站各天汇总数据到Excel position: {}", position);
//        try {
        ExcelUtil.builder(exportFileConfig.getExcelDir(), position,
                BiExportGroups.SITE_ESS_BI.getName())
            .addHeader(EssBiService.getHeaderClass(sampleType))
            .loopAppendData((start, size) -> {

                if (start >= 2) {
                    return List.of();
                }
                return new ArrayList<>(data);
            }, list -> {
                log.debug("{}", list);
                return list.stream()
                    .map(i -> {
                        DayEssDataBi vo = (DayEssDataBi) i;
                        ExportEssDataBiVo result;
                        if (SiteBiSampleType.MONTH.equals(sampleType)) {
                            result = new ExportExportEssDataBiMonthVo();
                        } else {
                            result = new ExportExportEssDataBiDayVo();
                        }
                        BeanUtils.copyProperties(vo, result);

                        Date time = DateUtils.toDate(vo.getDate());
                        result.setTime(time);

                        return result.fillNull();
                    }).collect(Collectors.toList());
            })
            .write2File();
        log.info("导出excel完成: {}", JsonUtils.toJsonString(position));
//        } catch (Exception e) {
//            log.error("导出excel异常: {}, err = {}",
//                    JsonUtils.toJsonString(position), e.getMessage(), e);
//        }
    }

    public Mono<ListResponse<DayEssDataBi>> siteEssBi(SiteBiParam param) {
        if (null == param.getSampleType()) {
            throw new DcArgumentException("请选择采样时间类型");
        }

        if (null == param.getStartTime()) {
            throw new DcArgumentException("请选择开始时间");
        }

        if (null == param.getEndTime()) {
            throw new DcArgumentException("请选择结束时间");
        }

//        if (StringUtils.isBlank(param.getSiteId())) {
//            throw new DcArgumentException("请提供场站ID");
//        }

        if (SiteBiSampleType.DAY.equals(param.getSampleType())) {
            return this.daySiteEssBi(new DayKwhParam().setSiteIdList(List.of(param.getSiteId()))
                .setDate(new TimeFilter()
                    .setStartTime(new Date(param.getStartTime()))
                    .setEndTime(new Date(param.getEndTime()))));
        } else if (SiteBiSampleType.MONTH.equals(param.getSampleType())) {
            // 开始时间调整
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(new Date(param.getStartTime()));
            calendar.set(Calendar.DAY_OF_MONTH, 1);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            Date startTime = calendar.getTime();

            // 结束时间调整
            calendar.setTime(new Date(param.getEndTime()));
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            Date endTime = calendar.getTime();

            return this.monthSiteEssBi(new DayKwhParam().setSiteIdList(List.of(param.getSiteId()))
                .setDate(new TimeFilter()
                    .setStartTime(startTime)
                    .setEndTime(endTime)));
        } else {
            throw new DcArgumentException("采样时间类型不支持");
        }
    }

    public Mono<ListResponse<DayEssDataBi>> daySiteEssBi(DayKwhParam param) {
        if (null == param.getDate() ||
            null == param.getDate().getStartTime() ||
            null == param.getDate().getEndTime()) {
            throw new DcArgumentException("请指定时间范围");
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(param.getDate().getEndTime());
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        param.getDate().setEndTime(calendar.getTime());

        if (CollectionUtils.isEmpty(param.getSiteIdList())) {
            throw new DcArgumentException("请选择场站");
        }

        return deviceFeignClient.siteDayOfRangeKwh(param)
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .flatMapMany(Flux::fromIterable)
            .collect(Collectors.toMap(DaySiteEssRtDataBi::getDate, o -> o, (o1, o2) -> {
                o1.setProfit(o1.getProfit().add(o2.getProfit()));
                o1.setInFee(o1.getInFee().add(o2.getInFee()));
                o1.setInKwh(o1.getInKwh().add(o2.getInKwh()));
                o1.setOutFee(o1.getOutFee().add(o2.getOutFee()));
                o1.setOutKwh(o1.getOutKwh().add(o2.getOutKwh()));
                return o1;
            }))
            .flatMap(dateMap -> Flux.fromIterable(
                    DateUtil.rangeDate(
                        DateUtil.dateToLocalDate(param.getDate().getStartTime()),
                        DateUtil.dateToLocalDate(param.getDate().getEndTime())))
                .map(date -> {
                    DayEssDataBi result = new DayEssDataBi();
                    DaySiteEssRtDataBi data = dateMap.get(date);
                    if (data != null) {
                        BeanUtils.copyProperties(data, result);
                    }
                    return result.setDate(date);
                })
                .collectList())
            .map(RestUtils::buildListResponse);
    }

    public Mono<ListResponse<DayEssDataBi>> monthSiteEssBi(DayKwhParam param) {
        if (null == param.getDate() ||
            null == param.getDate().getStartTime() ||
            null == param.getDate().getEndTime()) {
            throw new DcArgumentException("请指定时间范围");
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(param.getDate().getEndTime());
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        param.getDate().setEndTime(calendar.getTime());

        if (CollectionUtils.isEmpty(param.getSiteIdList())) {
            throw new DcArgumentException("请选择场站");
        }

        return deviceFeignClient.siteDayOfRangeKwh(param)
            .doOnNext(FeignResponseValidate::check)
            .map(ListResponse::getData)
            .flatMapMany(Flux::fromIterable)
            .collect(
                Collectors.toMap(o -> o.getDate().format(MONTH_FORMATTER), o -> o, (o1, o2) -> {
                    o1.setProfit(o1.getProfit().add(o2.getProfit()));
                    o1.setInFee(o1.getInFee().add(o2.getInFee()));
                    o1.setInKwh(o1.getInKwh().add(o2.getInKwh()));
                    o1.setOutFee(o1.getOutFee().add(o2.getOutFee()));
                    o1.setOutKwh(o1.getOutKwh().add(o2.getOutKwh()));
                    return o1;
                }))
            .flatMap(dateMap -> Flux.fromIterable(
                    DateUtil.rangeMonthDate(
                        DateUtil.dateToLocalDate(param.getDate().getStartTime()),
                        DateUtil.dateToLocalDate(param.getDate().getEndTime())))
                .map(date -> {
                    DayEssDataBi result = new DayEssDataBi();
                    DaySiteEssRtDataBi data = dateMap.get(date.format(MONTH_FORMATTER));
                    if (data != null) {
                        BeanUtils.copyProperties(data, result);
                        data.setDate(date);
                    }
                    return result.setDate(date);
                })
                .collectList())
            .map(RestUtils::buildListResponse);
    }

    /**
     * 统计商户下储能站数量、装机功率、容量
     */
//    @Transactional
    public EssDataSummary getCommSiteEssCount(EssBaseParam paramIn) {
        EssDataSummary result = new EssDataSummary();
        String commIdChain = null;
        List<String> gids = paramIn.getGids();
        if (CollectionUtils.isEmpty(gids)) {
            commIdChain = paramIn.getCommIdChain();
        }
        CommSiteEssCount siteCnt = this.siteRoDs.getCommSiteEssCount(commIdChain, null, gids);
        result.setSiteNum(siteCnt.getSiteNum())
            .setPower(siteCnt.getPower())
            .setCapacity(siteCnt.getCapacity());

        DayKwhParam kwhParam = new DayKwhParam();
        kwhParam.setGids(gids)
            .setCommIdChain(commIdChain);
        EssDataBi rtData = essDailyRoDs.rtDataOfTotal(kwhParam);
        result.setInKwh(rtData.getInKwh())
            .setOutKwh(rtData.getOutKwh())
            .setProfit(rtData.getProfit());

        return result;
    }

    /**
     * 储能收益统计, 昨日/本月/上月/总收益
     */
//    @Transactional
    public EssRecentData getProfitSummary(EssBaseParam paramIn) {
        EssRecentData result = new EssRecentData();
        String commIdChain = null;
        List<String> gids = paramIn.getGids();
        if (CollectionUtils.isNotEmpty(gids)) {
            commIdChain = paramIn.getCommIdChain();
        }
        DayKwhParam param = new DayKwhParam();
        param.setGids(gids)
            .setCommIdChain(commIdChain)
            .setTime(paramIn.getTime());
        EssDataBi yesterday = this.essDailyRoDs.rtDataOfYesterday(param);
        param.setYear(paramIn.getTime().getYear())
            .setMonth(paramIn.getTime().getMonthValue());
        EssDataBi thisMonth = this.essDailyRoDs.rtDataOfMonth(param);
        LocalDateTime lastMonthDate = paramIn.getTime().minusMonths(1L);
        param.setYear(lastMonthDate.getYear())
            .setMonth(lastMonthDate.getMonthValue());
        EssDataBi lastMonth = this.essDailyRoDs.rtDataOfMonth(param);
        EssDataBi total = this.essDailyRoDs.rtDataOfTotal(param);
        result.setYesterday(yesterday.getProfit())
            .setThisMonth(thisMonth.getProfit())
            .setLastMonth(lastMonth.getProfit())
            .setTotal(total.getProfit());
        return result;
    }

    /**
     * 按日/月统计储能充电/放电/收益数据, 主要用于图表
     */
    @Transactional(readOnly = true)
    public List<EssTimelyDataDto> getEssTimelyDataList(SummaryParam param) {
        List<EssTimelyDataDto> result;
        if (SummaryType.DAY == param.getSummaryType()) {
            result = this.essDailyRoDs.getDailyDataList(param);
        } else if (SummaryType.MONTH == param.getSummaryType()) {
            result = this.essDailyRoDs.getMonthlyDataList(param);
        } else {
            log.warn("参数错误，时间类型不支持");
            result = new ArrayList<>();
        }
        return result;
    }

    public Mono<ListResponse<EssEquipTimelyPo>> getTimelyElecList(ListEssDailyParam param) {
        param.setSizeIfNull(10, 9999);
        return Mono.just(param)
            .map(this.essEquipTimelyRoDs::getTimelyElecList)
            .map(RestUtils::buildListResponse)
            .doOnNext(res -> {
                if (null != param.getTotal() && param.getTotal()) {
                    res.setTotal(this.essEquipTimelyRoDs.count(param));
                } else {
                    res.setTotal(0L);
                }
            });
    }

    public List<EssEquipDailyElecBiDto> getDailyElecBiList(ListEssDailyParam param) {
        if (param.getStartTimeFilter() == null) {
            param.setStartTimeFilter(TimeFilter2.lastMonth());
        }
        List<EssEquipDailyElecVo> dailyItems = this.essEquipTimelyRoDs.getDailyElecBiList(param);
        log.debug("dailyItems.length = {}", dailyItems == null ? null : dailyItems.size());

        List<EssEquipDailyElecBiDto> result = this.calcElecBiList(param, dailyItems,
            endDate -> endDate.minusDays(1));
        return result;
    }

    private List<EssEquipDailyElecBiDto> calcElecBiList(ListEssDailyParam param,
        List<EssEquipDailyElecVo> items, EndDateCalc endDateCalc) {
        List<String> piNames = this.essEquipTimelyRoDs.getPiNameList(param);    // 时段名称列表
        log.debug("piNames = {}", JsonUtils.toJsonString(piNames));
        log.debug("items.length = {}", items == null ? null : items.size());
        if (CollectionUtils.isEmpty(items)) {
            return null;
        }

        // 时间分组
        Map<LocalDate, List<EssEquipDailyElecVo>> dateBiMap = items.stream()
            .collect(Collectors.groupingBy(EssEquipDailyElecVo::getDate));

        List<EssEquipDailyElecBiDto> result = new ArrayList<>();
        dateBiMap.forEach((date, data) -> {
            EssEquipDailyElecBiDto tmpItem = new EssEquipDailyElecBiDto().setDate(date);

            Map<String, List<EssEquipDailyElecVo>> piNameMap = data.stream()
                .peek(x -> {
                    if (x.getPiName() == null) {
                        x.setPiName("");
                    }
                }).collect(Collectors.groupingBy(EssEquipDailyElecVo::getPiName));

            // 时段类型
            List<Kv> inVals = piNames.stream().map(piName -> {
                if (piNameMap.containsKey(piName)) {
                    BigDecimal inKwh = piNameMap.get(piName).stream()
                        .map(EssEquipTimelyPo::getInKwh).filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal inFee = piNameMap.get(piName).stream()
                        .filter(x -> null != x.getInKwh() &&
                            DecimalUtils.gtZero(x.getInKwh())
                            && null != x.getInFee())
                        .map(EssEquipTimelyPo::getInFee)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    return new Kv(piName, inKwh, inFee);
                } else {
                    return new Kv(piName, BigDecimal.ZERO,
                        BigDecimal.ZERO);
                }
            }).collect(Collectors.toList());
            List<Kv> outVals = piNames.stream().map(piName -> {
                if (piNameMap.containsKey(piName)) {
                    BigDecimal outKwh = piNameMap.get(piName).stream()
                        .map(EssEquipTimelyPo::getOutKwh).filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal outFee = piNameMap.get(piName).stream()
                        .filter(x -> null != x.getOutKwh() &&
                            DecimalUtils.gtZero(x.getOutKwh())
                            && null != x.getOutFee())
                        .map(EssEquipTimelyPo::getOutFee)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                    return new Kv(piName, outKwh, outFee);
                } else {
                    return new Kv(piName, BigDecimal.ZERO,
                        BigDecimal.ZERO);
                }
            }).collect(Collectors.toList());
            tmpItem.setInVals(inVals).setOutVals(outVals);

            // 当天总量
            BigDecimal todayInKwh = data.stream().map(EssEquipTimelyPo::getInKwh)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal todayInFee = data.stream()
                .filter(x -> null != x.getInKwh() &&
                    DecimalUtils.gtZero(x.getInKwh())
                    && null != x.getInFee())
                .map(EssEquipTimelyPo::getInFee)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal todayOutKwh = data.stream().map(EssEquipTimelyPo::getOutKwh)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal todayOutFee = data.stream()
                .filter(x -> null != x.getOutKwh() &&
                    DecimalUtils.gtZero(x.getOutKwh())
                    && null != x.getOutFee())
                .map(EssEquipTimelyPo::getOutFee)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
            tmpItem.setTodayInKwh(todayInKwh)
                .setTodayInFee(todayInFee)
                .setTodayOutKwh(todayOutKwh)
                .setTodayOutFee(todayOutFee);

            // 汇总总量
            BigDecimal totalInKwh = data.stream().map(EssEquipTimelyPo::getTotalInKwh)
                .filter(Objects::nonNull)
                .min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
            BigDecimal totalOutKwh = data.stream().map(EssEquipTimelyPo::getTotalOutKwh)
                .filter(Objects::nonNull)
                .min(BigDecimal::compareTo).orElse(BigDecimal.ZERO);
            tmpItem.setTotalInKwh(totalInKwh).setTotalOutKwh(totalOutKwh);

            result.add(tmpItem);
        });

        // 数据库取到的总充/放电量为时段开始电量数据，将时段开始电量+当日/月电量后=时段结束总充/放电量
        this.calcTotalInOutKwh(result);

        return result.stream()
            .sorted((l, r) -> r.getDate().compareTo(l.getDate()))
            .collect(Collectors.toList());

//        items = items.stream()
//            .sorted((l, r) -> r.getDate().compareTo(l.getDate()))
//            .collect(Collectors.toList());
//        List<EssEquipDailyElecBiDto> result = new ArrayList<>();
//        LocalDate endDate = items.get(0).getDate();
//        LocalDate startDate = param.getStartTimeFilter().getStartTime().toLocalDate();
//        log.info("startDate = {}", startDate);
//        log.info("endDate = {}", endDate);
//
//        int loopIdx = 9999; // 避免死循环
//        var itemIter = items.iterator();
//        EssEquipDailyElecBiDto lastDateBi = null;
//        while (itemIter.hasNext() && loopIdx > 0) {
//            EssEquipDailyElecVo item = itemIter.next();
//            if (!item.getDate().isAfter(endDate)) {
//                lastDateBi = null;  // 这天已处理完
//                endDate = endDateCalc.nextEndDate(endDate); // endDate.minusDays(1);
//            }
//
//            if (lastDateBi == null) {
//                lastDateBi = new EssEquipDailyElecBiDto();
//                lastDateBi.setDate(endDate)
//                    .setTodayInKwh(BigDecimal.ZERO)
//                    .setTodayInFee(BigDecimal.ZERO)
//                    .setTodayOutKwh(BigDecimal.ZERO)
//                    .setTodayOutFee(BigDecimal.ZERO);
////                log.info("date = {}", endDate);
//
//                for (String piName : piNames) {
//                    lastDateBi.getInVals()
//                        .add(new EssEquipDailyElecBiDto.Kv(piName, BigDecimal.ZERO,
//                            BigDecimal.ZERO));
//                    lastDateBi.getOutVals()
//                        .add(new EssEquipDailyElecBiDto.Kv(piName, BigDecimal.ZERO,
//                            BigDecimal.ZERO));
//                }
//                result.add(lastDateBi);
//            }
//            if (item.getInKwh() != null && DecimalUtils.gtZero(item.getInKwh())) {
//                var inKv = lastDateBi.getInVals().stream()
//                    .filter(x -> StringUtils.equals(x.getK(), item.getPiName()))
//                    .findFirst().get();
//                inKv.setKwh(inKv.getKwh().add(item.getInKwh()));
//                inKv.setFee(inKv.getFee().add(item.getInFee()));
//                lastDateBi.setTodayInKwh(lastDateBi.getTodayInKwh().add(item.getInKwh()));
//                if (item.getInFee() != null) {
//                    lastDateBi.setTodayInFee(lastDateBi.getTodayInFee().add(item.getInFee()));
//                }
//            }
//
//            if (item.getOutKwh() != null && DecimalUtils.gtZero(item.getOutKwh())) {
//                var outKv = lastDateBi.getOutVals().stream()
//                    .filter(x -> StringUtils.equals(x.getK(), item.getPiName()))
//                    .findFirst().get();
//                outKv.setKwh(outKv.getKwh().add(item.getOutKwh()));
//                outKv.setFee(outKv.getFee().add(item.getOutFee()));
//                lastDateBi.setTodayOutKwh(lastDateBi.getTodayOutKwh().add(item.getOutKwh()));
//                if (item.getOutFee() != null) {
//                    lastDateBi.setTodayOutFee(lastDateBi.getTodayOutFee().add(item.getOutFee()));
//                }
//            }
//            this.fillTotalInKwh(item, lastDateBi);
//            this.fillTotalOutKwh(item, lastDateBi);
//
//            loopIdx--;
//        }
//
//        // 数据库取到的总充/放电量为时段开始电量数据，将时段开始电量+当日/月电量后=时段结束总充/放电量
//        this.calcTotalInOutKwh(result);
//
//        return result;
    }

    /**
     * 填充总充电电量
     */
    private void fillTotalInKwh(EssEquipDailyElecVo item, EssEquipDailyElecBiDto lastDateBi) {
        if (lastDateBi.getTotalInKwh() == null) {
            lastDateBi.setTotalInKwh(item.getTotalInKwh());
        } else {
            lastDateBi.setTotalInKwh(
                DecimalUtils.min(lastDateBi.getTotalInKwh(), item.getTotalInKwh()));
        }
    }

    /**
     * 填充总放电电量
     */
    private void fillTotalOutKwh(EssEquipDailyElecVo item, EssEquipDailyElecBiDto lastDateBi) {
        if (lastDateBi.getTotalOutKwh() == null) {
            lastDateBi.setTotalOutKwh(item.getTotalOutKwh());
        } else {
            lastDateBi.setTotalOutKwh(
                DecimalUtils.min(lastDateBi.getTotalOutKwh(), item.getTotalOutKwh()));
        }
    }

    /**
     * 数据库取到的总充/放电量为时段开始电量数据，将时段开始电量+当日/月电量后=时段结束总充/放电量
     */
    private void calcTotalInOutKwh(List<EssEquipDailyElecBiDto> dataList) {
        if (dataList == null) {
            return;
        }
        dataList.stream().forEach(d -> {
            if (d.getTodayInKwh() != null && d.getTotalInKwh() != null) {
                d.setTotalInKwh(d.getTotalInKwh().add(d.getTodayInKwh()));
            }
            if (d.getTodayOutKwh() != null && d.getTotalOutKwh() != null) {
                d.setTotalOutKwh(d.getTotalOutKwh().add(d.getTodayOutKwh()));
            }
        });
    }

    public List<EssEquipDailyElecBiDto> getMonthlyElecBiList(ListEssDailyParam param) {
        if (param.getStartTimeFilter() == null) {
            param.setStartTimeFilter(TimeFilter2.lastNMonth(12));
        }
        List<EssEquipDailyElecVo> items = this.essEquipTimelyRoDs.getMonthlyElecBiList(param);
        log.debug("items.length = {}", items == null ? null : items.size());

        List<EssEquipDailyElecBiDto> result = this.calcElecBiList(param, items,
            endDate -> endDate.minusMonths(1));
        return result;
    }

    interface EndDateCalc {

        LocalDate nextEndDate(LocalDate dateIn);
    }

}
