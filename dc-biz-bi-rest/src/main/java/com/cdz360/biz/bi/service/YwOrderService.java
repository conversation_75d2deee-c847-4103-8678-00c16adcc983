package com.cdz360.biz.bi.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.bi.config.ExportFileConfig;
import com.cdz360.biz.bi.feign.AuthCenterFeignClient;
import com.cdz360.biz.bi.service.siteGroup.SiteGroupService;
import com.cdz360.biz.ds.trading.ro.yw.ds.SiteInspectionRecordRoDs;
import com.cdz360.biz.ds.trading.ro.yw.ds.YwOrderRoDs;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import com.cdz360.biz.model.trading.bi.dto.BiExportGroups;
import com.cdz360.biz.model.trading.iot.param.PvProfitTrendParam;
import com.cdz360.biz.model.trading.iot.po.EvsePo;
import com.cdz360.biz.model.trading.iot.vo.PvRtDataPowerProfit;
import com.cdz360.biz.model.trading.pv.vo.ExportPvProfitTrendDayVo;
import com.cdz360.biz.model.trading.pv.vo.ExportPvProfitTrendMonthVo;
import com.cdz360.biz.model.trading.pv.vo.ExportPvProfitTrendVo;
import com.cdz360.biz.model.trading.site.dto.InspectionRecordDto;
import com.cdz360.biz.model.trading.site.param.RecordParam;
import com.cdz360.biz.model.trading.site.vo.ExportInspectionRecordVo;
import com.cdz360.biz.model.trading.yw.dto.Goods;
import com.cdz360.biz.model.trading.yw.param.ListYwOrderParam;
import com.cdz360.biz.model.trading.yw.vo.ExportYwOrderListVo;
import com.cdz360.biz.model.trading.yw.vo.YwOrderVo;
import com.cdz360.biz.utils.feign.iot.DeviceFeignClient;
import com.cdz360.biz.utils.feign.iot.GtiFeignClient;
import com.chargerlinkcar.framework.common.utils.ExcelFieldFormatter;
import com.chargerlinkcar.framework.common.utils.ExcelUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.fasterxml.jackson.core.type.TypeReference;
import java.io.IOException;
import java.io.Serializable;
import java.lang.reflect.Type;
import java.time.Duration;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class YwOrderService {

    @Autowired
    private ExportFileConfig exportFileConfig;

//    @Value("${excel.dir:/tmp/excel}")
//    private String TEMP_DIR;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private DeviceFeignClient deviceFeignClient;

    @Autowired
    private YwOrderRoDs ywOrderRoDs;
    @Autowired
    private SiteGroupService siteGroupService;

    @Autowired
    private SiteInspectionRecordRoDs inspectionRecordRoDs;

    @Autowired
    private GtiFeignClient gtiFeignClient;

    private static Class<?> getHeaderClass(SiteBiSampleType type) {
        if (SiteBiSampleType.MONTH.equals(type)) {
            return ExportPvProfitTrendMonthVo.class;
        }
        return ExportPvProfitTrendDayVo.class;
    }

    //    @Async
    public void exportYwOrderExcel(ExcelPosition position, ListYwOrderParam param)
        throws IOException {
        log.info("运维工单列表导出到EXCEL: {}", JsonUtils.toJsonString(position));
        Map<String, EvsePo> evsePoMap = new HashMap<>();
        AtomicReference<Map<String, List<String>>> siteGidMapRef = new AtomicReference<>(
            new HashMap<>());
        AtomicReference<Map<String, String>> groupMapRef = new AtomicReference<>(new HashMap<>());

//        try {
        ExcelUtil.builder(exportFileConfig.getExcelDir(), position,
                BiExportGroups.YW_ORDER.getName())
            .addHeader(ExportYwOrderListVo.class)
            .loopAppendData((start, size) -> {
                param.setStart((long) ((start - 1) * size))
                    .setSize(size);
                param.setTotal(Boolean.FALSE);

                List<YwOrderVo> all = this.ywOrderRoDs.findAll(param);

                this.fillHashMapWithYwOrder(all, siteGidMapRef.get(), groupMapRef.get(), evsePoMap);

                return new ArrayList<>(all);
            }, list -> new ArrayList<>(
                this.ywOrderListHandle(evsePoMap, siteGidMapRef.get(), groupMapRef.get(), list)))
            .write2File();
//            log.info("导出excel完成: {}", JsonUtils.toJsonString(position));
//        } catch (Exception e) {
//            log.error("导出excel异常: {}, err = {}",
//                    JsonUtils.toJsonString(position), e.getMessage(), e);
//        }
    }

    private void fillHashMapWithYwOrder(final List<YwOrderVo> voList,
        Map<String, List<String>> siteGidMap,
        Map<String, String> groupMap, Map<String, EvsePo> evsePoMap) {
        List<String> siteIdList = voList.stream()
            .map(YwOrderVo::getSiteId)
            .collect(Collectors.toList());
        siteGroupService.fillSiteGroupMap(siteIdList, siteGidMap, groupMap);

        List<String> evseNoList = voList.stream()
            .filter(e -> CollectionUtils.isNotEmpty(e.getEvseNoList()))
            .flatMap(t -> t.getEvseNoList().stream()).distinct()
            .filter(t -> !evsePoMap.containsKey(t)) // 避免资源浪费
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(evseNoList)) {
            ListResponse<EvsePo> response = deviceFeignClient.getEvseList(
                new ListEvseParam().setEvseNoList(evseNoList))
                .block(Duration.ofSeconds(50L));
            FeignResponseValidate.check(response);
            evsePoMap.putAll(response.getData().stream()
                .collect(Collectors.toMap(EvsePo::getEvseId, o -> o)));
        }
    }

    private List<ExportYwOrderListVo> ywOrderListHandle(Map<String, EvsePo> evsePoMap,
        Map<String, List<String>> siteGidMap,
        Map<String, String> groupMap,
        List<Serializable> list) {
        List<ExportYwOrderListVo> result = new ArrayList<>();

        for (Serializable i : list) {
            YwOrderVo vo = (YwOrderVo) i;
            ExportYwOrderListVo exportVo = new ExportYwOrderListVo();
            BeanUtils.copyProperties(vo, exportVo);

            // 是否远程解决
            if (null != vo.getRemote()) {
                exportVo.setRemote(vo.getRemote() ? "是" : "否");
            }

            // 状态
            if (null != vo.getOrderStatus()) {
                exportVo.setOrderStatus(vo.getOrderStatus().getDesc());
            }

            siteGidMap.getOrDefault(vo.getSiteId(), List.of()).stream()
                .filter(Objects::nonNull)
                .filter(t -> StringUtils.isNotBlank(groupMap.get(t)))
                .findFirst().ifPresent(s -> {
                    exportVo.setSiteGidName(groupMap.get(s));
                });

            if (CollectionUtils.isNotEmpty(vo.getGoods())) {
                exportVo.setReplacedDevice("是");

                StringBuilder sb = new StringBuilder();
                String s = JsonUtils.toJsonString(vo.getGoods());
                List<Goods> goods = JsonUtils.fromJson(s, new TypeReference<List<Goods>>() {
                    @Override
                    public Type getType() {
                        return super.getType();
                    }
                });
//                    List<Goods> goods = JSON.parseArray(s, Goods.class);
                goods.forEach(e -> {
                    sb.append(e.getEvseName()).append("\r\n");
                    if (StringUtils.isNotBlank(e.getGTypeName())) {
                        sb.append(e.getGTypeName()).append("\r\n");
                    }
                    if (StringUtils.isNotBlank(e.getOldTypeCode()) || StringUtils.isNotBlank(
                        e.getOldFullModel())) {
                        sb.append("旧（").append(e.getOldTypeCode()).append(",")
                            .append(e.getOldFullModel()).append("）\r\n");
                    }
                    if (StringUtils.isNotBlank(e.getNewTypeCode()) || StringUtils.isNotBlank(
                        e.getNewFullModel())) {
                        sb.append("新（").append(e.getNewTypeCode()).append(",")
                            .append(e.getNewFullModel()).append("）\r\n");
                    }
                });
                exportVo.setGoodsStr(sb.toString());

            } else {
                exportVo.setReplacedDevice("否");
                exportVo.setGoodsStr("--");
            }

            exportVo.setYwDurationStr(ExcelFieldFormatter.ywDurationConvert(vo.getYwDuration()));

            if (CollectionUtils.isNotEmpty(vo.getEvseNoList())) {
                vo.getEvseNoList().stream().filter(StringUtils::isNotBlank).map(evsePoMap::get)
                    .filter(Objects::nonNull).forEach(t -> {
                        ExportYwOrderListVo tempExportVo = new ExportYwOrderListVo();
                        BeanUtils.copyProperties(exportVo, tempExportVo);
                        tempExportVo.setEvseStr(
                                StringUtils.isNotBlank(t.getName()) ? (t.getName() + " " + t.getEvseId())
                                    : t.getEvseId())
                            .setPhysicalNo(t.getPhysicalNo())
                            .setSupplyStr(t.supplyFormat())
                            .setModel(t.getModel())
                            .setPower(t.getPower())
                            .setProduceDate(t.getProduceDate())
                            .setExpireDate(t.getExpireDate());
                        result.add(tempExportVo);
                    });
            } else {
                result.add(exportVo);
            }
        }
        return result;
    }

    //    @Async
    public void exportInspectionRecExcel(ExcelPosition position, RecordParam param) {
        log.info("巡检工单列表导出到EXCEL: {}", JsonUtils.toJsonString(position));
        try {
//            final Map<Long, String> nameMap = new HashMap<>();

            boolean empty = false;
            if (StringUtils.isNotBlank(param.getRummager())) {
                ListResponse<SysUserVo> temp = authCenterFeignClient.findByName(param.getRummager(),
                    Boolean.FALSE);
                FeignResponseValidate.check(temp);
                if (CollectionUtils.isEmpty(temp.getData())) {
                    empty = true;
                } else {
                    param.setOpUidList(temp.getData().stream()
                        .map(SysUserVo::getId)
                        .collect(Collectors.toList()));
                }
            }
            Map<Long, SysUserVo> sysUserVoMap = new HashMap<>();
            AtomicReference<Map<String, List<String>>> siteGidMapRef = new AtomicReference<>(
                new HashMap<>());
            AtomicReference<Map<String, String>> groupMapRef = new AtomicReference<>(
                new HashMap<>());

            BiFunction<Integer, Integer, List<Serializable>> fetch = empty ?
                (start, size) -> List.of() : (start, size) -> {
                param.setStart(((long) (start - 1) * size))
                    .setSize(size);
                param.setTotal(Boolean.FALSE);

                List<InspectionRecordDto> all = this.inspectionRecordRoDs.getRecords(param);

                this.fillHashMapWithInspection(all, sysUserVoMap, siteGidMapRef.get(),
                    groupMapRef.get());

                all.forEach(e -> {
                    SysUserVo sysUserVo = sysUserVoMap.get(e.getOpUid());
                    if (sysUserVo != null) {
                        e.setRummager(sysUserVo.getName());
                    }
                    SysUserVo sysUserVo2 = sysUserVoMap.get(e.getQcUid());
                    if (sysUserVo2 != null) {
                        e.setQcUserName(sysUserVo2.getName());
                    }

                    siteGidMapRef.get().getOrDefault(e.getSiteId(), List.of()).stream()
                        .filter(Objects::nonNull)
                        .filter(t -> StringUtils.isNotBlank(groupMapRef.get().get(t)))
                        .findFirst().ifPresent(s -> {
                            e.setSiteGidName(groupMapRef.get().get(s));
                        });
                });

                return new ArrayList<>(all);
            };

            ExcelUtil.builder(exportFileConfig.getExcelDir(), position,
                    BiExportGroups.INSPECTION_RECORD.getName())
                .addHeader(ExportInspectionRecordVo.class)
                .loopAppendData(fetch, list -> new ArrayList<>(list.stream()
                    .map(i -> {
                        InspectionRecordDto recordDto = (InspectionRecordDto) i;
                        ExportInspectionRecordVo result = new ExportInspectionRecordVo();
                        BeanUtils.copyProperties(recordDto, result);

                        // 状态调整
                        if (null != recordDto.getStatus()) {
                            switch (recordDto.getStatus()) {
                                case INIT:
                                    result.setStatus("待处理");
                                    break;
                                case TO_BE_INSPECTED:
                                    result.setStatus("待质检");
                                    break;
                                case PASS:
                                    result.setStatus("已完成");
                                    break;
                                case FAIL:
                                    result.setStatus("不合格");
                                    break;
                                case CANCEL:
                                    result.setStatus("已取消");
                                    break;
                                case DELETED:
                                    result.setStatus("已删除");
                                    break;
                            }
                        }
                        return result;
                    })
                    .collect(Collectors.toList())))
                .write2File();
            log.info("导出excel完成: {}", JsonUtils.toJsonString(position));
        } catch (Exception e) {
            log.error("导出excel异常: {}, err = {}",
                JsonUtils.toJsonString(position), e.getMessage(), e);
        }
    }

    private void fillHashMapWithInspection(final List<InspectionRecordDto> dtoList,
        Map<Long, SysUserVo> sysUserVoMap,
        Map<String, List<String>> siteGidMap, Map<String, String> groupMap) {

        // 巡检人
        Set<Long> idList = new HashSet<>();
        dtoList.forEach(e -> {
            idList.add(e.getOpUid());
            idList.add(e.getQcUid());
        });

        var temp = idList.stream().filter(s -> !sysUserVoMap.containsKey(s)).collect(
            Collectors.toList()); // 避免资源浪费
        if (CollectionUtils.isNotEmpty(temp)) {
            ListResponse<SysUserVo> response = authCenterFeignClient.querySysUserByIds(
                new ArrayList<>(idList));
            FeignResponseValidate.check(response);
            sysUserVoMap.putAll(response.getData().stream()
                .collect(Collectors.toMap(SysUserVo::getId, o -> o)));
        }

        List<String> siteIdList = dtoList.stream()
            .map(InspectionRecordDto::getSiteId)
            .collect(Collectors.toList());
        siteGroupService.fillSiteGroupMap(siteIdList, siteGidMap, groupMap);

    }

    //    @Async
    public void exportPowerProfitTrendExcel(ExcelPosition position, PvProfitTrendParam param)
        throws IOException {
        log.info("电量收益趋势到EXCEL: {}", JsonUtils.toJsonString(position));
//        try {
        ExcelUtil.builder(exportFileConfig.getExcelDir(), position,
                BiExportGroups.PV_ELEC_FEE_PROFIT_TREND.getName())
            .addHeader(YwOrderService.getHeaderClass(param.getSampleType()))
            .loopAppendData((start, size) -> {
                param.setStart((long) ((start - 1) * size))
                    .setSize(size);
                param.setTotal(Boolean.FALSE);

                if (start >= 2) {
                    return List.of();
                }

                ListResponse<PvRtDataPowerProfit> block = gtiFeignClient.powerProfitTrend(param)
                    .block(Duration.ofSeconds(90L));
                FeignResponseValidate.check(block);
                List<PvRtDataPowerProfit> all = block.getData();//this.ywOrderRoDs.findAll(param);
                return new ArrayList<>(all);
            }, list -> {
                log.debug("{}", list);
                List<ExportPvProfitTrendVo> exportPvProfitTrendVos = list.stream()
                    .map(i -> {
                        PvRtDataPowerProfit orderVo = (PvRtDataPowerProfit) i;
                        ExportPvProfitTrendVo result;
                        if (SiteBiSampleType.MONTH.equals(param.getSampleType())) {
                            result = new ExportPvProfitTrendMonthVo();
                        } else {
                            result = new ExportPvProfitTrendDayVo();
                        }
                        BeanUtils.copyProperties(orderVo, result);

                        Date time = new Date(orderVo.getTime()
                            .atZone(ZoneId.systemDefault())
                            .toInstant()
                            .toEpochMilli());
                        result.setTime(time);

                        return result;
                    })
                    .collect(Collectors.toList());
                return new ArrayList<>(exportPvProfitTrendVos);
            })
            .write2File();
        log.info("导出excel完成: {}", JsonUtils.toJsonString(position));
//        } catch (Exception e) {
//            log.error("导出excel异常: {}, err = {}",
//                    JsonUtils.toJsonString(position), e.getMessage(), e);
//        }
    }
}
