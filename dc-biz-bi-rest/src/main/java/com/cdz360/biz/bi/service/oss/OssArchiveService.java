package com.cdz360.biz.bi.service.oss;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.GetObjectRequest;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.model.props.AliyunOssArchiveProperties;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Optional;
import java.util.zip.GZIPInputStream;
import jakarta.annotation.Nullable;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service(value = "essOssArchiveService")
public class OssArchiveService {

    @Value("${oss.local.tempPath:/tmp}")
    private String tempFilePath;

    @Autowired
    private AliyunOssArchiveProperties ossProperties;

    private OSS ossClient;

    /**
     * 初始化
     */
    @PostConstruct
    public void init() {
        try {
            if (ossProperties.getRegionId() == null) {
                log.warn("未配置oss信息");
                return;
            }
            log.info("OSS-STS初始化信息：regionId:{},  accessKeyId:{}, accessKeySecret:{}",
                ossProperties.getRegionId(), ossProperties.getAccessKeyId(),
                ossProperties.getAccessKeySecret());

            ossClient = new OSSClientBuilder().build(ossProperties.getEndpoint(),
                ossProperties.getAccessKeyId(),
                ossProperties.getAccessKeySecret());

        } catch (Exception e) {
            log.error("初始化阿里云OSS失败. error = {}", e.getMessage(), e);
        }
    }

    public Mono<Optional<String>> getOSSDeviceRtData(String siteId, String dno,
        @Nullable Long essEquipId, String deviceType, LocalDate date) {
        return getOSSDeviceRtFile(siteId, dno, essEquipId, deviceType, date, DataType.RT_DATA);
    }

    public Mono<Optional<String>> getOSSDeviceRtInfo(String siteId, String dno,
        @Nullable Long essEquipId, String deviceType, LocalDate date) {
        return getOSSDeviceRtFile(siteId, dno, essEquipId, deviceType, date, DataType.RT_INFO);
    }

    public Mono<Optional<String>> getOSSDeviceRtFile(String siteId, String dno,
        @Nullable Long essEquipId, String deviceType, LocalDate date, DataType dataType) {
        return Mono.just(siteId)
            .map(no -> {
                String key = formatDeviceRtDataFileKey(
                    date, siteId, dno, essEquipId, deviceType, dataType);

                // 缺少清理逻辑
                String localFile = tempFilePath + "/" + ossProperties.getBucketName() + "/" + key;
                File file = new File(localFile);
                String newFile = tempFilePath + "/" + ossProperties.getBucketName() + "/"
                    + file.getName().replaceAll(".gzip", "");
                if (file.exists()) {
                    return Optional.of(newFile);
                }

                if (!file.getParentFile().exists()) {
                    boolean b = file.getParentFile().mkdirs();
                }

                boolean exist = ossClient.doesObjectExist(ossProperties.getBucketName(), key);
                if (!exist) {
                    return Optional.empty();
                }

                // 将文件下载到本地
                try {
                    GetObjectRequest request = new GetObjectRequest(
                        ossProperties.getBucketName(), key);
                    ossClient.getObject(request, new File(localFile));

                    // 解压到本地
                    decompressGzipFile(file, newFile);

                    return Optional.of(newFile);
                } catch (Exception ex) {
                    log.error("下载文件异常: {}", ex.getMessage(), ex);
                }

                return Optional.empty();
//                return Optional.of(ossClient.getObject(ossProperties.getBucketName(), key));
            });
    }

    private static void decompressGzipFile(File gzipFile, String newFile) {
        try {
            FileInputStream fis = new FileInputStream(gzipFile);
            GZIPInputStream gis = new GZIPInputStream(fis);
            FileOutputStream fos = new FileOutputStream(newFile);
            byte[] buffer = new byte[1024];
            int len;
            while ((len = gis.read(buffer)) != -1) {
                fos.write(buffer, 0, len);
            }

            fos.close();
            gis.close();
        } catch (IOException e) {
            log.error("解压文件异常: {}", e.getMessage(), e);
        }
    }

    private String formatDeviceRtDataFileKey(LocalDate date, String siteId, String dno,
        @Nullable Long essEquipId, String deviceType, DataType dataType) {
        return date.format(DateTimeFormatter.ofPattern("yyyy")) +
            "/" +
            date.format(DateTimeFormatter.ofPattern("MM")) +
            "/" +
            date.format(DateTimeFormatter.ofPattern("dd")) +
            this.typeFormat(deviceType) +
            (StringUtils.isBlank(siteId) ? "" : siteId + "/") +
            (date.format(DateTimeFormatter.ofPattern("yyyyMMdd")) + "_" + deviceType
                + dataType.getType() + dno).toLowerCase() +
            ("ESS".equals(deviceType) && (essEquipId != null && essEquipId > 0) ? ("_" + essEquipId)
                : "") +
            ".log.gzip";
    }

    private String typeFormat(String type) {
        if (type == null) {
            return "";
        } else if (List.of("HI", "PCS", "BMS", "METER").contains(type)) {
            return "/essData/";
        } else {
            return "";
        }
    }

    @Getter
    private enum DataType {
        RT_INFO("_info_"),
        RT_DATA("_data_");

        private final String type;

        DataType(String type) {
            this.type = type;
        }
    }
}
