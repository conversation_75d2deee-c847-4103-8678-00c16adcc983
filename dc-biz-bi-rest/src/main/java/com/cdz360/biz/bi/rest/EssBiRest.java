package com.cdz360.biz.bi.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.bi.service.ess.EssBiService;
import com.cdz360.biz.ess.model.data.param.EssBaseParam;
import com.cdz360.biz.ess.model.data.po.EssEquipTimelyPo;
import com.cdz360.biz.ess.model.data.vo.DayEssDataBi;
import com.cdz360.biz.ess.model.dto.EssEquipDailyElecBiDto;
import com.cdz360.biz.ess.model.dto.EssTimelyDataDto;
import com.cdz360.biz.ess.model.param.SummaryParam;
import com.cdz360.biz.ess.model.vo.EssDataSummary;
import com.cdz360.biz.ess.model.vo.EssRecentData;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.ess.param.ListEssDailyParam;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.text.SimpleDateFormat;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "储能相关接口")
public class EssBiRest {

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMdd");

    @Autowired
    private EssBiService essBiService;


    @Operation(summary = "场站各天汇总数据到Excel", description = "场站各天汇总数据到Excel")
    @PostMapping(value = "/bi/ess/exportSiteEssBiExcel")
    public Mono<ObjectResponse<ExcelPosition>> exportSiteEssBiExcel(
        @RequestParam("sampleType") SiteBiSampleType sampleType,
        @RequestBody List<DayEssDataBi> data) {
        log.info("场站各天汇总数据到Excel data.size = {}", data.size());
        throw new DcServiceException("接口已废弃，请联系开发");
    }

    @Operation(summary = "储能商户汇总", description = "用于地图页面的汇总数据")
    @PostMapping("/bi/ess/commSummary")
    public Mono<ObjectResponse<EssDataSummary>> getEssCommSummary(
        @RequestBody EssBaseParam param) {
        log.info("获取储能商户数据汇总, param = {}", param);
        return Mono.just(
            RestUtils.buildObjectResponse(this.essBiService.getCommSiteEssCount(param)));
    }

    @Operation(summary = "储能收益统计", description = "昨日/本月/上月收益")
    @PostMapping("/bi/ess/getProfitSummary")
    public Mono<ObjectResponse<EssRecentData>> getProfitSummary(@RequestBody EssBaseParam param) {
//        final long uid = EssRestUtils.getAppCusInfo(request).getCusId();
        log.info("获取储能收益统计, param = {}", param);
        return Mono.just(
            RestUtils.buildObjectResponse(this.essBiService.getProfitSummary(param)));
    }

    @Operation(summary = "储能数据统计", description = "按日/月统计储能充电/放电/收益数据, 主要用于图表")
    @PostMapping("/bi/ess/getEssTimelyDataList")
    public Mono<ListResponse<EssTimelyDataDto>> getEssTimelyDataList(
        @RequestBody SummaryParam param) {
//        final long uid = EssRestUtils.getAppCusInfo(request).getCusId();
        log.info("获取储能数据统计, param = {}", param);
        return Mono.just(
            RestUtils.buildListResponse(this.essBiService.getEssTimelyDataList(param)));
    }


    @Operation(summary = "储能设备分时段充/放电量明细数据")
    @PostMapping("/bi/ess/getTimelyElecList")
    public Mono<ListResponse<EssEquipTimelyPo>> getTimelyElecList(
        @RequestBody ListEssDailyParam param) {
        log.info("储能设备分时段明细数据: {}", param);
        return essBiService.getTimelyElecList(param);
    }

    @Operation(summary = "按日统计各时段充放电量数据")
    @PostMapping("/bi/ess/getDailyElecBiList")
    public Mono<ListResponse<EssEquipDailyElecBiDto>> getDailyElecBiList(
        @RequestBody ListEssDailyParam param) {
        log.info("按日统计各时段充放电量数据: {}", param);

        var list = essBiService.getDailyElecBiList(param);
        return Mono.just(RestUtils.buildListResponse(list));
    }

    @Operation(summary = "按月统计各时段充放电量数据")
    @PostMapping("/bi/ess/getMonthlyElecBiList")
    public Mono<ListResponse<EssEquipDailyElecBiDto>> getMonthlyElecBiList(
        @RequestBody ListEssDailyParam param) {
        log.info("按月统计各时段充放电量数据: {}", param);

        var list = essBiService.getMonthlyElecBiList(param);
        return Mono.just(RestUtils.buildListResponse(list));
    }

}


