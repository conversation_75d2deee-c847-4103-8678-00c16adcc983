package com.cdz360.biz.bi.domain.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 *  订单列表导出VO
 * @since 2016年9月18日 下午2:36:25
 */
@Data
public class CommUserOrderExportVO implements Serializable {

    /**
     * 订单号
     **/
    @ExcelField(title = "订单编号", sort = 1)
    private String orderNo;

    @ExcelField(title = "所属商户", sort = 2)
    private String commercialFullName;

    /**
     * 充电站名称
     **/
    @ExcelField(title = "站点名称", sort = 3)
    private String stationName;

    /**
     * 充电卡号
     **/
    @ExcelField(title = "卡号", sort = 4)
    private String cardChipNo;

    /**
     * 车辆VIN码
     **/
    @ExcelField(title = "VIN码", sort = 5)
    private String vin;

    /**
     * 车牌号
     **/
    @ExcelField(title = "车牌号", sort = 6)
    private String carNo;

    /**
     * 订单来源
     **/
    @ExcelField(title = "来源", sort = 7)
    private String source;

    /**
     * 支付方式
     **/
    @ExcelField(title = "支付方式", sort = 8)
    private String payModes;

    /**
     * 应付金额
     **/
    @ExcelField(title = "应付金额(代金币)", sort = 9)
    private BigDecimal actualPrice;

    /**
     * 充电开始时间/充电结束时间
     **/
    @ExcelField(title = "充电开始时间/充电结束时间", sort = 10)
    private String chargeTime;

    /**
     * 订单状态
     **/
    @ExcelField(title = "订单状态", sort = 11)
    private String orderStatus;
}
