package com.cdz360.biz.bi.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.bi.config.ExportFileConfig;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.PayBillRoDs;
import com.cdz360.biz.model.settle.param.ListClearingRecordParam;
import com.cdz360.biz.model.settle.po.SettClearingRecord;
import com.cdz360.biz.model.trading.bi.dto.BiExportGroups;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.model.trading.order.po.PayBillPo;
import com.cdz360.biz.model.trading.settle.vo.ChargeOrderExportVO;
import com.cdz360.biz.model.trading.settle.vo.ConsumeOrderExportVO;
import com.cdz360.biz.utils.feign.sett.SettleServerFeignClient;
import com.chargerlinkcar.framework.common.utils.ExcelUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SettRecordService {

    @Autowired
    private ExportFileConfig exportFileConfig;

//    @Value("${excel.dir:/tmp/excel}")
//    private String TEMP_DIR;

    @Autowired
    private ChargerOrderRoDs chargerOrderRoDs;

    @Autowired
    private PayBillRoDs payBillRoDs;

    @Autowired
    private SettleServerFeignClient settleServerFeignClient;

    @Async
    public void exportSettRecExcel(String settleId, ExcelPosition position) {
        log.info("商户结算单明细导出Excel: {}, settleId = {}", JsonUtils.toJsonString(position),
            settleId);
        try {
            ListClearingRecordParam param = new ListClearingRecordParam();
            param.setSettleId(settleId);

            ExcelUtil excelUtil = ExcelUtil.builder(exportFileConfig.getExcelDir(), position,
                    BiExportGroups.COMM_SETT_REC_CONSUME.getName())
                .addHeader(ConsumeOrderExportVO.class,
                    BiExportGroups.COMM_SETT_REC_CONSUME.getCode())
                .newSheet(BiExportGroups.COMM_SETT_REC_CHARGE.getName(), ChargeOrderExportVO.class)
                .newSheet(BiExportGroups.COMM_SETT_REC_SERVER.getName(),
                    ConsumeOrderExportVO.class,
                    BiExportGroups.COMM_SETT_REC_SERVER.getCode());

            // 遍历查询数据
            BiFunction<Integer, Integer, List<SettClearingRecord>> fetch =
                (start, size) -> {
                    param.setStart(((long) (start - 1) * size))
                        .setSize(size);
                    param.setTotal(Boolean.FALSE);

                    return settleServerFeignClient.searchClearRec(param)
                        .doOnNext(FeignResponseValidate::check)
                        .map(ListResponse::getData)
                        .block(Duration.ofSeconds(50L));
                };

            int index = 1; // 分页从1开始
            int size = 1000;

            // 改成分页获取数据
            while (true) {
                List<SettClearingRecord> dataList = fetch.apply(index, size);
                log.info("当前页: index={}, size={}, size = {}", index, size, dataList.size());
                if (CollectionUtils.isEmpty(dataList)) {
                    break;
                } else {
                    // 获取订单信息
                    Map<Integer, List<SettClearingRecord>> collect = dataList.stream()
                        .collect(Collectors.groupingBy(SettClearingRecord::getClearingMode));

                    this.dealChargeRec(excelUtil, collect.getOrDefault(1, List.of())); // 充值订单
                    this.dealConsumeRec(excelUtil, collect.getOrDefault(2, List.of())); // 充电订单

                    if (dataList.size() < size) {
                        break;
                    }

                    index += 1;

                    // 10w条数据约束
                    IotAssert.isTrue(index <= 100,
                        "已超出最大订单导出条数（导出订单条数最多10万条）！减少订单导出条数后，再导出。");
                }
            }

            excelUtil.write2File();
            log.info("导出excel完成: {}", JsonUtils.toJsonString(position));
        } catch (Exception e) {
            log.error("导出excel异常: {}, err = {}",
                JsonUtils.toJsonString(position), e.getMessage(), e);
        }
    }

    private void dealConsumeRec(ExcelUtil excelUtil, List<SettClearingRecord> consumeRec) {
        if (CollectionUtils.isEmpty(consumeRec)) {
            return;
        }

        List<SettClearingRecord> srcRecList = consumeRec.stream()
            .filter(i -> StringUtils.isNotBlank(i.getBizOrderNo()))
            .collect(Collectors.toList());

        List<String> chargeOrderNoList = srcRecList.stream()
            .filter(i -> StringUtils.isNotBlank(i.getBizOrderNo()))
            .map(SettClearingRecord::getBizOrderNo)
            .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(chargeOrderNoList)) {
            boolean serverFee = consumeRec.get(0).getPlatformElecPrice() != null;
            boolean consumeFee = consumeRec.get(0).getElecSettleAmount() != null ||
                consumeRec.get(0).getServSettleAmount() != null;

            Map<String, ConsumeOrderExportVO> chargerOrderPoMap =
                chargerOrderRoDs.findClearOrder(chargeOrderNoList)
                    .stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(ConsumeOrderExportVO::getOrderNo, o -> o));

            // 消费清分订单
            if (consumeFee) {
                List<ConsumeOrderExportVO> collect = srcRecList.stream()
                    .map(rec -> {
                        String orderNo = rec.getBizOrderNo();
                        ConsumeOrderExportVO orderPo = chargerOrderPoMap.get(orderNo);
                        ConsumeOrderExportVO result = new ConsumeOrderExportVO();

                        if (null != orderPo) {
                            BeanUtils.copyProperties(orderPo, result);
                        } else {
                            result.setOrderNo(orderNo);
                        }
                        return result.setTotalClearFee(rec.getTotalFee())
                            .setElecSettleAmount(rec.getElecSettleAmount())
                            .setElecShareRate(rec.getElecShareRate())
                            .setElecShareRateFee(rec.getElecShareRateFee())
                            .setServSettleAmount(rec.getServSettleAmount())
                            .setServShareRate(rec.getServShareRate())
                            .setServShareRateFee(rec.getServShareRateFee());
                    })
                    .collect(Collectors.toList());
                excelUtil.appendDataList(BiExportGroups.COMM_SETT_REC_CONSUME.getName(), collect);
            }

            if (serverFee) { // 平台服务费订单
                List<ConsumeOrderExportVO> servList = srcRecList.stream()
                    .map(rec -> {
                        String orderNo = rec.getBizOrderNo();
                        ConsumeOrderExportVO orderPo = chargerOrderPoMap.get(orderNo);

                        ConsumeOrderExportVO result = new ConsumeOrderExportVO();
                        if (null != orderPo) {
                            BeanUtils.copyProperties(orderPo, result);
                        } else {
                            result.setOrderNo(orderNo);
                        }
                        return result
                            .setPlatformSettleElec(rec.getPlatformSettleElec())
                            .setPlatformElecPrice(rec.getPlatformElecPrice())
                            .setPlatformElecPriceFee(rec.getPlatformElecPriceFee());
                    })
                    .collect(Collectors.toList());
                excelUtil.appendDataList(BiExportGroups.COMM_SETT_REC_SERVER.getName(), servList);
            }
        }
    }

    private void dealChargeRec(ExcelUtil excelUtil, List<SettClearingRecord> chargeRec) {
        if (CollectionUtils.isEmpty(chargeRec)) {
            return;
        }

        List<SettClearingRecord> srcRecList = chargeRec.stream()
            .filter(i -> StringUtils.isNotBlank(i.getBizTradeNo()))
            .collect(Collectors.toList());

        List<String> consumeOrderNoList = srcRecList.stream()
            .filter(i -> StringUtils.isNotBlank(i.getBizTradeNo()))
            .map(SettClearingRecord::getBizTradeNo)
            .collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(consumeOrderNoList)) {
            // 相关字段操作接口: /orderList
            PayBillParam param = new PayBillParam();
            param.setOrderIdList(consumeOrderNoList);
            Map<String, PayBillPo> payBillPoMap = payBillRoDs.payBillList(param)
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(PayBillPo::getOrderId, o -> o));

            // 充值清分订单
            List<ChargeOrderExportVO> collect = srcRecList.stream()
                .map(rec -> {
                    String orderNo = rec.getBizTradeNo();
                    PayBillPo orderPo = payBillPoMap.get(orderNo);

                    ChargeOrderExportVO result = new ChargeOrderExportVO();
                    if (null != orderPo) {
                        result.setCostAmount(orderPo.getAmount())
                            .setFreeAmount(orderPo.getFreeAmount())
                            .setTotalAmount(orderPo.getAmount().add(orderPo.getFreeAmount()))
                            .setCusName(orderPo.getCusName())
                            .setCusPhone(orderPo.getCusPhone())
                            .setAccountType(orderPo.getAccountType())
                            .setPayChannel(orderPo.getPayChannel());
                    }
                    return result
                        .setOrderNo(orderNo)
                        .setShareRate(rec.getShareRate())
                        .setShareRateFee(rec.getShareRateFee());
                })
                .collect(Collectors.toList());
            excelUtil.appendDataList(BiExportGroups.COMM_SETT_REC_CHARGE.getName(), collect);
        }
    }
}
