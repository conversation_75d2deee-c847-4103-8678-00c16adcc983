package com.cdz360.biz.bi.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.bi.config.ExportFileConfig;
import com.cdz360.biz.bi.domain.vo.CardListExport2VO;
import com.cdz360.biz.bi.domain.vo.CardListExportI18n2VO;
import com.cdz360.biz.bi.domain.vo.CardListOnCorpExport2VO;
import com.cdz360.biz.bi.domain.vo.VinExport2Vo;
import com.cdz360.biz.bi.domain.vo.VinExportI18n2Vo;
import com.cdz360.biz.bi.domain.vo.VinOnCorpExport2Vo;
import com.cdz360.biz.bi.feign.TradingFeignClient;
import com.cdz360.biz.model.cus.card.vo.CardListdetailVO;
import com.cdz360.biz.model.cus.vin.param.VinSearchParam;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.chargerlinkcar.framework.common.domain.param.CardSearchParam;
import com.chargerlinkcar.framework.common.domain.param.CorpCardRequest;
import com.chargerlinkcar.framework.common.domain.vo.VinDto;
import com.chargerlinkcar.framework.common.domain.vo.VinParam;
import com.chargerlinkcar.framework.common.feign.UserFeignClient;
import com.chargerlinkcar.framework.common.utils.ExcelUtil;
import com.chargerlinkcar.framework.common.utils.ExportExcel;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 *  //导出订单excel
 * @since 2019/8/20
 **/
@Slf4j
@Service
public class CardExportService //implements IExcelFileService
{

    private static final String CHARSET = "utf-8";

    @Autowired
    private ExportFileConfig exportFileConfig;

    // 默认启动金额
//    @Value("${excel.dir:/tmp/excel}")
//    private String TEMP_DIR;

    private String PART_FILE_NAME = "part";

    @Value("${resourceOwnerId:}")
    private String TEMP;

    @Value("${excel.tmp.file:files/bill_tmpl.xlsx}")
    private String EXCEL_TEMP_FILE;

    // 模板文件的路径(账单)
    private static String TEMP_FILE_PATH = null;

    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private TradingFeignClient tradingFeignClient;

    @Autowired
    private MessageSource messageSource;


    /**
     * [实际数据]和[写入数据]必须implements Serializable
     *
     * @param subDir       文件存放子目录
     * @param subFileName  文件名
     * @param clazz        输出到excel的行记录
     * @param group
     * @param sheetName
     * @param templateFile 模板文件
     * @param locale       语言环境，未设置为null
     * @param f            lambda, 获取[实际数据]
     * @param ft           lambda, 将f获取的[实际数据]转换成[写入数据]
     */
    public void exportExcelFile(
        String subDir,
        String subFileName,
        Class clazz,
        Integer group,
        String sheetName,
        String templateFile,
        List<String> filterList,
        Locale locale,
        Function<List<Serializable>, Integer, Integer> f,
        FunctionTrans<List<Serializable>, List<Serializable>> ft) {

        synchronized (this) {
            log.info("exportExcelFile 开始作业");
//            //先清除临时文件夹中的文件
//            deleteFile(searchParam.getSubDir(), searchParam.getType());

            // 文件存放目录
            String dir = exportFileConfig.getExcelDir() + File.separator + subDir;

            // 临时文件
            String tmpFilePath = dir + File.separator + subFileName + PART_FILE_NAME + ".xlsx";

            // 重定向文件
            String filePath = dir + File.separator + subFileName + ".xlsx";

            // excel
            ExportExcel exportExcel = null;
            try {
                if (group != null) {
                    if (null != locale) {
                        exportExcel = new ExportExcel(null, clazz, sheetName, templateFile,
                            filterList, locale, group);
                    } else {
                        exportExcel = new ExportExcel(null, clazz, sheetName, templateFile,
                            filterList,
                            group);
                    }
                } else {
                    if (null != locale) {
                        exportExcel = new ExportExcel(null, clazz, sheetName, templateFile,
                            filterList, locale);
                    } else {
                        exportExcel = new ExportExcel(null, clazz, sheetName, templateFile,
                            filterList);
                    }
                }
            } catch (Exception e) {
                log.error("{}", e.getMessage(), e);
                return;
            }

            // 分页索引
            int index = 1; // 分页从1开始
            int size = 1000;

            // 改成分页获取数据
            while (true) {
                // System.out.println("作业中");
                List<Serializable> serializables = f.apply(index, size);
                if (CollectionUtils.isEmpty(serializables)) {
                    log.warn("获取订单结束.");
                    break;
                } else {
                    log.info("当前页: index={}, size={}", index, size);
                    // 追加到excel
                    List<Serializable> tmp = ft.apply(serializables);
                    log.info("请求数据 size = {}", tmp.size());
//                    log.info("tmp: {}", JsonUtils.toJsonString(tmp));
                    exportExcel.addDataList((index - 1) * size + 1, tmp); // 考虑数据开始行号
                    if (tmp.size() < size) {
                        log.info("获取订单结束：size={}", tmp.size());
                        break;
                    }
                    // 页码递增
                    index += 1;
                    // 10w条数据约束
                    IotAssert.isTrue(index <= 100,
                        "已超出最大订单导出条数（导出订单条数最多10万条）！减少订单导出条数后，再导出。");

                }
            }
            log.info("临时文件: {}, 重定向文件: {}", tmpFilePath, filePath);
            try {
                File folder = new File(dir);
                //如果文件夹不存在则创建
                if (!folder.exists()) {
                    folder.mkdirs();
                }
                exportExcel.writeFile(tmpFilePath);

                //重命名:临时文件->最终文件名
                FileUtils.copyFile(new File(tmpFilePath), new File(filePath));
//                new File(tmpFilePath).renameTo(new File(filePath));
            } catch (IOException e) {
                log.error("<< 写订单excel出错: error={},{}", e.getMessage(), e);
            }
            log.info("<< 写订单结束excel,filePath: {}", filePath);
        }
    }

    /**
     * 商户会员订单
     *
     * @param searchParam
     */
//    @Async
    public void exportCardForManage(CardSearchParam searchParam, ExcelPosition position)
        throws IOException {
        if (null == searchParam.getLocale()) {
            // 非国际版，保留原有逻辑
            ExcelUtil.builder(exportFileConfig.getExcelDir(), position, "在线卡信息")
                .addHeader(CardListExport2VO.class)
                .loopAppendData((start, size) -> {
                    ListResponse<CardListdetailVO> res = userFeignClient.queryCards(
                        "--", start, size, searchParam); // token 值后续不使用，使用"--"替换
                    return new ArrayList<>(res.getData());
                }, list -> new ArrayList<>(list.stream()
                    .map(i -> (CardListdetailVO) i)
                    .map(vo -> {
                        CardListExport2VO exportVo = new CardListExport2VO();
                        exportVo.setCardChipNo(vo.getCardChipNo());
                        exportVo.setCardName(vo.getCardName());
                        exportVo.setCommName(vo.getCommName());
                        exportVo.setUserName(vo.getUserName());
                        exportVo.setCarNo(vo.getCarNo());
                        exportVo.setCarNum(vo.getCarNum());
                        exportVo.setCarDepart(vo.getCarDepart());
                        exportVo.setLineNum(vo.getLineNum());
                        exportVo.setUsableStationCount(vo.getUsableStationCount().toString());
                        exportVo.setCorpName(vo.getCorpName());
                        exportVo.setDebitAccountName(vo.getDebitAccountName());
                        exportVo.setCardCreateDate(vo.getCardCreateDate());
                        exportVo.setCardStatus(this.getCardStatus(vo.getCardStatus()));
                        return exportVo;
                    })
                    .collect(Collectors.toList())))
                .write2File();
        } else {
            // 国际版，单独处理
            Map<String, String> cardStatusMap = new HashMap<>();
            ExcelUtil.builder(exportFileConfig.getExcelDir(), position,
                    messageSource.getMessage("在线卡信息", null, searchParam.getLocale()))
                .addI18nHeader(CardListExportI18n2VO.class, searchParam.getLocale())
                .loopAppendData((start, size) -> {
                    ListResponse<CardListdetailVO> res = userFeignClient.queryCards(
                        "--", start, size, searchParam); // token 值后续不使用，使用"--"替换
                    return new ArrayList<>(res.getData());
                }, list -> new ArrayList<>(list.stream()
                    .map(i -> (CardListdetailVO) i)
                    .map(vo -> {
                        CardListExport2VO exportVo = new CardListExport2VO();
                        exportVo.setCardChipNo(vo.getCardChipNo());
                        exportVo.setCardName(vo.getCardName());
                        exportVo.setCommName(vo.getCommName());
                        exportVo.setUserName(vo.getUserName());
                        exportVo.setCarNo(vo.getCarNo());
                        exportVo.setCarNum(vo.getCarNum());
                        exportVo.setCarDepart(vo.getCarDepart());
                        exportVo.setLineNum(vo.getLineNum());
                        exportVo.setUsableStationCount(vo.getUsableStationCount().toString());
                        exportVo.setCorpName(vo.getCorpName());
                        exportVo.setDebitAccountName(vo.getDebitAccountName());
                        exportVo.setCardCreateDate(vo.getCardCreateDate());
                        if (!cardStatusMap.containsKey(vo.getCardStatus())) {
                            String status = messageSource.getMessage(
                                this.getCardStatus(vo.getCardStatus()),
                                null, searchParam.getLocale());
                            cardStatusMap.put(vo.getCardStatus(), status);
                            exportVo.setCardStatus(status);
                        } else {
                            exportVo.setCardStatus(cardStatusMap.get(vo.getCardStatus()));
                        }
                        return exportVo;
                    })
                    .collect(Collectors.toList())))
                .write2File();
        }

//        exportExcelFile(
//                position.getSubDir(),
//                position.getSubFileName(),
//                CardListExport2VO.class,
//                null,
//                "在线卡信息",
//                null,
//                null,
//                (index, size) -> {
//                    ListResponse<CardListdetailVO> res = userFeignClient.queryCards(token, index,size,searchParam);
//                    if (res == null || res.getData() == null || res.getData().size() == 0) {
//                        return null;
//                    } else {
//                        return res.getData().stream().collect(Collectors.toList());
//                    }
//                },
//                e -> transCardListForManage(e.stream().map(x -> (CardListdetailVO) x).collect(Collectors.toList())
//                        ).stream().collect(Collectors.toList())
//        );
    }

    /**
     * 企业平台在线卡导出
     *
     * @param searchParam
     */
    @Async
    public void exportCardForCorp(CorpCardRequest searchParam, ExcelPosition position) {
        exportExcelFile(
            position.getSubDir(),
            position.getSubFileName(),
            CardListOnCorpExport2VO.class,
            null,
            "在线卡信息",
            null,
            null,
            null,
            (index, size) -> {
                searchParam.setRows(size);
                searchParam.setPage(index);
                ListResponse<CardListdetailVO> res = userFeignClient.queryOnlineCardsByPageOnCorp(
                    searchParam);
                if (res == null || res.getData() == null || res.getData().size() == 0) {
                    return null;
                } else {
                    return res.getData().stream().collect(Collectors.toList());
                }
            },
            e -> transCardListForCorp(
                e.stream().map(x -> (CardListdetailVO) x).collect(Collectors.toList())
            ).stream().collect(Collectors.toList())
        );
    }

    /**
     * 管理平台VIN码导出
     *
     * @param searchParam
     */
//    @Async
    public void exportVinForManage(ExcelPosition position, VinSearchParam searchParam) {
        if (null == searchParam.getLocale()) {
            // 非国际版，保留原有逻辑
            exportExcelFile(
                position.getSubDir(),
                position.getSubFileName(),
                VinExport2Vo.class,
                null,
                "VIN码信息",
                null,
                null,
                null,
                (index, size) -> {
                    searchParam.setSize(size);
                    searchParam.setStart((long) ((index - 1) * size));
                    ListResponse<VinDto> res = userFeignClient.select(searchParam);
                    if (res == null || res.getData() == null || res.getData().size() == 0) {
                        return null;
                    } else {
                        return res.getData().stream().collect(Collectors.toList());
                    }
                },
                e ->
                    transVinListForManage(
                        e.stream().map(x -> (VinDto) x).collect(Collectors.toList())
                        , null).stream().collect(Collectors.toList())
            );
        } else {
            // 国际版，单独处理
            exportExcelFile(
                position.getSubDir(),
                position.getSubFileName(),
                VinExportI18n2Vo.class,
                null,
                messageSource.getMessage("VIN码信息", null, searchParam.getLocale()),
                null,
                null,
                searchParam.getLocale(),
                (index, size) -> {
                    searchParam.setSize(size);
                    searchParam.setStart((long) ((index - 1) * size));
                    ListResponse<VinDto> res = userFeignClient.select(searchParam);
                    if (res == null || res.getData() == null || res.getData().size() == 0) {
                        return null;
                    } else {
                        return res.getData().stream().collect(Collectors.toList());
                    }
                },
                e ->
                    transVinListForManage(
                        e.stream().map(x -> (VinDto) x).collect(Collectors.toList())
                        , searchParam.getLocale()).stream().collect(Collectors.toList())
            );
        }
    }

    /**
     * 企业平台导出VIN
     *
     * @param position
     * @param searchParam
     */
    @Async
    public void exportVinForCorp(ExcelPosition position, VinParam searchParam) {
        exportExcelFile(
            position.getSubDir(),
            position.getSubFileName(),
            VinOnCorpExport2Vo.class,
            null,
            "VIN码信息",
            null,
            null,
            null,
            (index, size) -> {
                searchParam.setRows(size);
                searchParam.setPage(index);
                ListResponse<VinDto> res = userFeignClient.selectselectVinOnCorpOnCorp(searchParam);
                if (res == null || res.getData() == null || res.getData().size() == 0) {
                    return null;
                } else {
                    return res.getData().stream().collect(Collectors.toList());
                }
            },
            e -> transVinListForCorp(
                e.stream().map(x -> (VinDto) x).collect(Collectors.toList())).stream()
                .collect(Collectors.toList())
        );
    }

    private List<VinOnCorpExport2Vo> transVinListForCorp(List<VinDto> vinList) {

        List<VinOnCorpExport2Vo> vinExport2VoList = new ArrayList<>();
        vinList.forEach(vo -> {
            VinOnCorpExport2Vo exportVo = new VinOnCorpExport2Vo();
            exportVo.setVin(vo.getVin());
            exportVo.setCarNo(vo.getCarNo());
            exportVo.setCarDepart(vo.getCarDepart());
            exportVo.setLineNum(vo.getLineNum());
            exportVo.setCarNum(vo.getCarNum());
            exportVo.setCorpOrgName(vo.getCorpOrgName());
            exportVo.setMobile(vo.getMobile());
            exportVo.setUsableStationCount(vo.getUsableStationCount().toString());
            exportVo.setStatus(vo.getStatus().equals(1) ? "有效" : "停用");
            exportVo.setCreateTime(vo.getCreateTime());
            vinExport2VoList.add(exportVo);
        });
        return vinExport2VoList;
    }

    private List<VinExport2Vo> transVinListForManage(List<VinDto> vinList, Locale locale) {

        List<VinExport2Vo> vinExport2VoList = new ArrayList<>();
        String authOpeningStatus = null == locale ? "开启"
            : messageSource.getMessage("vin.auth.status.opening", null, locale);
        String authClosingStatus = null == locale
            ? "关闭"
            : messageSource.getMessage("vin.auth.status.closing", null, locale);
        vinList.forEach(vo -> {
            VinExport2Vo exportVo = new VinExport2Vo();
            exportVo.setVin(vo.getVin());
            exportVo.setSubCommName(vo.getSubCommName());
            exportVo.setUserName(vo.getUserName());
            exportVo.setCarNo(vo.getCarNo());
            exportVo.setCarDepart(vo.getCarDepart());
            exportVo.setLineNum(vo.getLineNum());
            exportVo.setCarNum(vo.getCarNum());
            exportVo.setUsableStationCount(vo.getUsableStationCount().toString());
            exportVo.setDebitAccountName(vo.getDebitAccountName());
            exportVo.setCorpName(vo.getCorpName());
//            exportVo.setAuthStatus(
//                (vo.getAuthSiteAmount() != null && vo.getAuthSiteAmount().compareTo(0) > 0) ? "开启"
//                    : "关闭");
            // 设置本地认证
            exportVo.setAuthStatus(
                (vo.getAuthSiteAmount() != null && vo.getAuthSiteAmount().compareTo(0) > 0)
                    ? authOpeningStatus
                    : authClosingStatus);
//            exportVo.setStatus(vo.getStatus().equals(1)?"有效":"停用");
            exportVo.setCreateTime(vo.getCreateTime());
            vinExport2VoList.add(exportVo);
        });
        return vinExport2VoList;
    }

    /**
     * 管理平台在线卡导出
     *
     * @return
     */
    private List<CardListExport2VO> transCardListForManage(List<CardListdetailVO> cardList) {

        List<CardListExport2VO> cardListExport2VOList = new ArrayList<>();
        cardList.forEach(vo -> {
            CardListExport2VO exportVo = new CardListExport2VO();
            exportVo.setCardChipNo(vo.getCardChipNo());
            exportVo.setCardName(vo.getCardName());
            exportVo.setCommName(vo.getCommName());
            exportVo.setUserName(vo.getUserName());
            exportVo.setCarNo(vo.getCarNo());
            exportVo.setCarNum(vo.getCarNum());
            exportVo.setCarDepart(vo.getCarDepart());
            exportVo.setLineNum(vo.getLineNum());
            exportVo.setUsableStationCount(vo.getUsableStationCount().toString());
            exportVo.setCorpName(vo.getCorpName());
            exportVo.setDebitAccountName(vo.getDebitAccountName());
            exportVo.setCardCreateDate(vo.getCardCreateDate());
            exportVo.setCardStatus(this.getCardStatus(vo.getCardStatus()));
            cardListExport2VOList.add(exportVo);
        });
        return cardListExport2VOList;
    }

    private List<CardListOnCorpExport2VO> transCardListForCorp(List<CardListdetailVO> cardList) {

        List<CardListOnCorpExport2VO> cardListExport2VOList = new ArrayList<>();
        cardList.forEach(vo -> {
            CardListOnCorpExport2VO exportVo = new CardListOnCorpExport2VO();
            exportVo.setCardChipNo(vo.getCardChipNo());
            exportVo.setCardName(vo.getCardName());
            exportVo.setCarNo(vo.getCarNo());
            exportVo.setCarDepart(vo.getCarDepart());
            exportVo.setLineNum(vo.getLineNum());
            exportVo.setCarNum(vo.getCarNum());
            exportVo.setCorpOrgName(vo.getCorpOrgName());
            exportVo.setCorpPhone(vo.getCorpPhone());
            exportVo.setUsableStationCount(vo.getUsableStationCount().toString());
            exportVo.setCardStatus(this.getCardStatus(vo.getCardStatus()));
            exportVo.setActivationDate(vo.getActivationDate());
            cardListExport2VOList.add(exportVo);
        });
        return cardListExport2VOList;
    }

    public String getCardStatus(String status) {
        String cardStatus = "";
        switch (status) {
            case "10000":
                cardStatus = "未激活";
                break;
            case "10001":
                cardStatus = "已激活";
                break;
            case "10002":
                // 和界面显示保持一致，将“卡锁定”改为“已挂失”
                cardStatus = "已挂失";
                break;
            case "10005":
                cardStatus = "已失效";
                break;
            case "10006":
                cardStatus = "已过期";
                break;
            default:
                cardStatus = "未知";
        }
        return cardStatus;

    }

    @FunctionalInterface
    interface Function<Uno, Dos, Tres> {

        Uno apply(Dos dos, Tres tres);
    }


    @FunctionalInterface
    interface FunctionTrans<Uno, Dos> {

        Uno apply(Dos dos);
    }
}
