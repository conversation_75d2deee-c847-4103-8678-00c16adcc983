package com.cdz360.biz.bi.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.bi.service.CardExportService;
import com.cdz360.biz.model.cus.vin.param.VinSearchParam;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.chargerlinkcar.framework.common.domain.param.CardSearchParam;
import com.chargerlinkcar.framework.common.domain.param.CorpCardRequest;
import com.chargerlinkcar.framework.common.domain.vo.VinParam;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.UUIDUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 *  订单导出相关
 */
@Slf4j
@RestController
public class CardExportRest extends BaseController {


    @Autowired
    private CardExportService cardExportService;

    /**
     * 管理平台在线卡导出
     *
     * @param param
     * @return
     */
    @PostMapping("/api/card/exportCardForManage")
    public ObjectResponse<ExcelPosition> exportCardForManage(
        @RequestParam("token") String token,
        @RequestBody CardSearchParam param
    ) {
        throw new DcServiceException("接口已废弃，请联系开发");
//        ExcelPosition position = new ExcelPosition();
//        position.setSubFileName(UUIDUtils.getUuid32());
//        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
//        log.info("writeTempExcelByChargeOrderList 启动,position:{}", JsonUtils.toJsonString(position));
//        cardExportService.exportCardForManage(token,param, position);
//
//        return new ObjectResponse<>(position);
    }

    /**
     * 企业平台在线卡导出
     *
     * @return
     */
    @PostMapping("/api/card/exportCardForCorp")
    public ObjectResponse<ExcelPosition> exportCardForCorp(@RequestBody CorpCardRequest param) {
        ExcelPosition position = new ExcelPosition();
        position.setSubFileName(UUIDUtils.getUuid32());
        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
        log.info("writeTempExcelByChargeOrderList 启动,position:{}",
            JsonUtils.toJsonString(position));
        cardExportService.exportCardForCorp(param, position);

        return new ObjectResponse<>(position);
    }

    /**
     * 管理平台导出VIN
     *
     * @param param
     * @return
     */
    @PostMapping("/api/vin/exportVinForManage")
    public ObjectResponse<ExcelPosition> exportVinForManage(@RequestBody VinSearchParam param) {
        throw new DcServiceException("接口已废弃，请联系开发");

//        ExcelPosition position = new ExcelPosition();
//        position.setSubFileName(UUIDUtils.getUuid32());
//        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
//        log.info("writeTempExcelByChargeOrderList 启动,position:{}", JsonUtils.toJsonString(position));
//        cardExportService.exportVinForManage(position,param);
//        return new ObjectResponse<>(position);
    }

    /**
     * 企业平台导出VIN
     *
     * @param param
     * @return
     */
    @PostMapping("/api/vin/exportVinForCorp")
    public ObjectResponse<ExcelPosition> exportVinForCorp(@RequestBody VinParam param) {
        ExcelPosition position = new ExcelPosition();
        position.setSubFileName(UUIDUtils.getUuid32());
        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
        log.info("writeTempExcelByChargeOrderList 启动,position:{}",
            JsonUtils.toJsonString(position));
        cardExportService.exportVinForCorp(position, param);
        return new ObjectResponse<>(position);
    }

}
