//package com.cdz360.biz.bi.domain;
//
//import com.chargerlinkcar.framework.common.domain.BasePage;
//import org.hibernate.validator.constraints.Length;
//
//import javax.validation.constraints.NotNull;
//
///**
// * 字典管理Entity
// *
// * <AUTHOR>
// * @version  2018-10-13
// */
//public class Dict extends BasePage {
//
//    private static final long serialVersionUID = 1L;
//
//    /**
//     * 编号
//     */
//    private Long id;
//
//    /**
//     * 数据值
//     */
//    private String value;
//
//    /**
//     * 标签名
//     */
//    private String label;
//
//    /**
//     * 类型
//     */
//    private String type;
//
//    /**
//     * 描述
//     */
//    private String description;
//
//
//    public Dict() {
//        super();
//    }
//
//
//    @NotNull(message = "编号不能为空")
//    @Length(min = 0, max = 64, message = "编号长度必须介于 0 和 64 之间")
//
//    public Long getId() {
//        return id;
//    }
//
//    public void setId(Long id) {
//        this.id = id;
//    }
//
//
//    @NotNull(message = "数据值不能为空")
//    @Length(min = 0, max = 100, message = "数据值长度必须介于 0 和 100 之间")
//
//    public String getValue() {
//        return value;
//    }
//
//    public void setValue(String value) {
//        this.value = value;
//    }
//
//
//    @NotNull(message = "标签名不能为空")
//    @Length(min = 0, max = 100, message = "标签名长度必须介于 0 和 100 之间")
//
//    public String getLabel() {
//        return label;
//    }
//
//    public void setLabel(String label) {
//        this.label = label;
//    }
//
//
//    @NotNull(message = "类型不能为空")
//    @Length(min = 0, max = 100, message = "类型长度必须介于 0 和 100 之间")
//
//    public String getType() {
//        return type;
//    }
//
//    public void setType(String type) {
//        this.type = type;
//    }
//
//
//    @NotNull(message = "描述不能为空")
//    @Length(min = 0, max = 100, message = "描述长度必须介于 0 和 100 之间")
//    public String getDescription() {
//        return description;
//    }
//
//    public void setDescription(String description) {
//        this.description = description;
//    }
//
//
//}