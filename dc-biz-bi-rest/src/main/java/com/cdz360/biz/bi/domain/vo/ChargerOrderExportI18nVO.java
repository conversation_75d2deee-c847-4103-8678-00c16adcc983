package com.cdz360.biz.bi.domain.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR> 订单列表导出VO
 * @since 2016年9月18日 下午2:36:25
 */
@Data
public class ChargerOrderExportI18nVO implements Serializable {

    /**
     * 订单号
     **/
    @ExcelField(title = "订单编号", i18nTitle = "charger.order.orderNo", sort = 1)
    private String orderNo;

//    /**
//     * 关联账单号
//     **/
//    @ExcelField(title = "关联账单号", i18nTitle = "charger.order.billNo", sort = 1)
//    private String billNo;

    /**
     * 订单状态
     **/
    @ExcelField(title = "订单状态", i18nTitle = "charger.order.orderStatus", sort = 2)
//    private String statusName;
    private String orderStatus;


    /**
     * 创建时间
     **/
    @ExcelField(title = "创建时间", i18nTitle = "common.createTime", sort = 3)
    private String createTime;

    /**
     * 手机号
     **/
    @ExcelField(title = "手机号", i18nTitle = "common.phone", sort = 4)
//    private String phone;
    private String mobilePhone;

    @ExcelField(title = "邮箱", i18nTitle = "common.email", sort = 4)
    private String email;

    /**
     * 客户名
     **/
    @ExcelField(title = "客户名", i18nTitle = "common.accountName", sort = 4)
//    private String phone;
    private String customerName;


    /**
     * 启动方式 newFeild
     **/
    @ExcelField(title = "启动方式", i18nTitle = "charger.order.bootMode", sort = 5)
//    private String orderTypeName;
    private String orderType;


    /**
     * 订单来源
     **/
    @ExcelField(title = "来源", i18nTitle = "charger.order.source", sort = 6)
//    private String channelName;
    private String source;

    /**
     * 充电卡号
     **/
    @ExcelField(title = "卡号", i18nTitle = "card.cardNo", sort = 7)
//    private String cardNo;
    private String cardChipNo;

    //9 卡类型 newFeild
    @ExcelField(title = "卡类型", i18nTitle = "charger.order.cardType", sort = 8)
//    private String cardTypeName;
    private String cardType;

    //9 卡名称
    @ExcelField(title = "卡名称", i18nTitle = "card.cardName", sort = 9)
    private String cardName;

    /**
     * 车队名称
     */
    @ExcelField(title = "车队名称", i18nTitle = "car.carDepart", sort = 10)
    private String carDepart;

    /**
     * 线路
     */
    @ExcelField(title = "线路", i18nTitle = "car.lineNum", sort = 11)
    private String lineNum;

    /**
     * 车牌号
     */
    @ExcelField(title = "车牌号", i18nTitle = "car.carNo", sort = 12)
    private String carNo;
    //12 车辆自编号
    @ExcelField(title = "车辆自编号", i18nTitle = "car.carNum", sort = 13)
    private String carNum;

    /**
     * 车辆VIN码
     **/
    @ExcelField(title = "VIN码", i18nTitle = "vin.vinCode", sort = 14)
    private String vin;
    @ExcelField(title = "品牌", i18nTitle = "charger.order.brand", sort = 15)
    private String brand;

    @ExcelField(title = "型号", i18nTitle = "charger.order.model", sort = 16)
    private String model;

    @ExcelField(title = "辅电(V)", i18nTitle = "charger.order.bmsVoltage", sort = 17)
    private Integer bmsVoltage;

    @ExcelField(title = "国标", i18nTitle = "charger.order.gbVer", sort = 18)
    private String gbVer;

    //13 所属商户
    @ExcelField(title = "所属商户", i18nTitle = "common.commercialFullName", sort = 19)
    private String commercialFullName;

    /**
     * 充电站名称
     **/
    @ExcelField(title = "站点名称", i18nTitle = "charger.order.stationName", sort = 20)
    private String stationName;

    /**
     * 站点编号
     **/
    @ExcelField(title = "站点编号", i18nTitle = "charger.order.stationCode", sort = 21)
    private String siteNo;

    //15 电桩编号
    @ExcelField(title = "电桩编号", i18nTitle = "charger.order.evseCode", sort = 22)
    private String boxCode;

    @ExcelField(title = "枪头编号", i18nTitle = "charger.order.connectorNo", sort = 23)
    private String plugNo;

    //16 电桩名称
    @ExcelField(title = "电桩名称", i18nTitle = "charger.order.evseName", sort = 24)
    private String evseName;

    /**
     * 枪头名称
     **/
    @ExcelField(title = "枪头名称", i18nTitle = "charger.order.connectorName", sort = 25)
    private String chargerName;

    //18 枪头标识
    @ExcelField(title = "枪头标识", i18nTitle = "charger.order.connectorId", sort = 26)
    private String connectorId;


    @ExcelField(title = "电桩类型", i18nTitle = "charger.order.supplyType", sort = 27)
    private String supplyType;


    @ExcelField(title = "尖时电量", i18nTitle = "charger.order.tipElectricity", sort = 28, digits = 4)
    private BigDecimal tipElectricity;//
    @ExcelField(title = "尖时服务费", i18nTitle = "charger.order.tipServicePrice", sort = 29, digits = 4)
    private BigDecimal tipServicePrice;//
    @ExcelField(title = "尖时服务费单价", i18nTitle = "charger.order.tipServiceUnit", sort = 30, digits = 4)
    private BigDecimal tipServiceUnit;
    @ExcelField(title = "尖时电费", i18nTitle = "charger.order.tipElecPrice", sort = 31, digits = 4)
    private BigDecimal tipElecPrice;//
    @ExcelField(title = "尖时电费单价", i18nTitle = "charger.order.tipElectricUnit", sort = 32, digits = 4)
    private BigDecimal tipElectricUnit;//
    @ExcelField(title = "尖时合计费用", i18nTitle = "charger.order.tipSumPrice", sort = 33, digits = 4)
    private BigDecimal tipSumPrice;//
    @ExcelField(title = "峰时电量", i18nTitle = "charger.order.peakElectricity", sort = 34, digits = 4)
    private BigDecimal peakElectricity;//
    @ExcelField(title = "峰时服务费", i18nTitle = "charger.order.peakServicePrice", sort = 35, digits = 4)
    private BigDecimal peakServicePrice;//
    @ExcelField(title = "峰时服务费单价", i18nTitle = "charger.order.peakServiceUnit", sort = 36, digits = 4)
    private BigDecimal peakServiceUnit;
    @ExcelField(title = "峰时电费", i18nTitle = "charger.order.peakElecPrice", sort = 37, digits = 4)
    private BigDecimal peakElecPrice;//
    @ExcelField(title = "峰时电费单价", i18nTitle = "charger.order.peakElectricUnit", sort = 38, digits = 4)
    private BigDecimal peakElectricUnit;
    @ExcelField(title = "峰时合计费用", i18nTitle = "charger.order.peakSumPrice", sort = 39, digits = 4)
    private BigDecimal peakSumPrice;//
    @ExcelField(title = "平时电量", i18nTitle = "charger.order.flatElectricity", sort = 40, digits = 4)
    private BigDecimal flatElectricity;//
    @ExcelField(title = "平时服务费", i18nTitle = "charger.order.flatServicePrice", sort = 41, digits = 4)
    private BigDecimal flatServicePrice;//
    @ExcelField(title = "平时服务费单价", i18nTitle = "charger.order.flatServiceUnit", sort = 42, digits = 4)
    private BigDecimal flatServiceUnit;
    @ExcelField(title = "平时电费", i18nTitle = "charger.order.flatElecPrice", sort = 43, digits = 4)
    private BigDecimal flatElecPrice;//
    @ExcelField(title = "平时电费单价", i18nTitle = "charger.order.flatElectricUnit", sort = 44, digits = 4)
    private BigDecimal flatElectricUnit;
    @ExcelField(title = "平时合计费用", i18nTitle = "charger.order.flatSumPrice", sort = 45, digits = 4)
    private BigDecimal flatSumPrice;//
    @ExcelField(title = "谷时电量", i18nTitle = "charger.order.valleyElectricity", sort = 46, digits = 4)
    private BigDecimal valleyElectricity;
    @ExcelField(title = "谷时服务费", i18nTitle = "charger.order.valleyServicePrice", sort = 47, digits = 4)
    private BigDecimal valleyServicePrice;
    @ExcelField(title = "谷时服务费单价", i18nTitle = "charger.order.valleyServiceUnit", sort = 48, digits = 4)
    private BigDecimal valleyServiceUnit;
    @ExcelField(title = "谷时电费", i18nTitle = "charger.order.valleyElecPrice", sort = 49, digits = 4)
    private BigDecimal valleyElecPrice;
    @ExcelField(title = "谷时电费单价", i18nTitle = "charger.order.valleyElectricUnit", sort = 50, digits = 4)
    private BigDecimal valleyElectricUnit;
    @ExcelField(title = "谷时合计费用", i18nTitle = "charger.order.valleySumPrice", sort = 51, digits = 4)
    private BigDecimal valleySumPrice;

    /**
     * 订单电量
     **/
    @ExcelField(title = "总电量（kWh）", i18nTitle = "charger.order.orderElectricity", sort = 52, digits = 4)
//    private Double orderElectricity;
    private BigDecimal orderElectricity;

    @ExcelField(title = "标准服务费（元）", i18nTitle = "charger.order.servOriginFee", sort = 53, digits = 4)
//    private Double servicePrice;
    private BigDecimal servOriginFee;
    /**
     * 总服务费
     */
    @ExcelField(title = "总服务费（元）", i18nTitle = "charger.order.servicePrice", sort = 54, digits = 4)
//    private Double servicePrice;
    private BigDecimal servicePrice;

    @ExcelField(title = "标准电费（元）", i18nTitle = "charger.order.elecOriginFee", sort = 55, digits = 4)
//    private Double elecPrice;
    private BigDecimal elecOriginFee;
    /**
     * 电费
     */
    @ExcelField(title = "总电费（元）", i18nTitle = "charger.order.elecPrice", sort = 56, digits = 4)
//    private Double elecPrice;
    private BigDecimal elecPrice;


    /**
     * 订单总金额
     **/
    @ExcelField(title = "订单总金额（元）", i18nTitle = "charger.order.totalPrice", sort = 57)
//    private Double orderPrice;
    private BigDecimal orderPrice;

    /**
     * 电损金额
     */
    @ExcelField(title = "电损金额（元）", i18nTitle = "charger.order.discount", sort = 58)
    private BigDecimal discount;
    /**
     * 实际金额消费 newFeild
     */
    @ExcelField(title = "实际金额消费（元）", i18nTitle = "charger.order.principalAmount", sort = 59)
//    private BigDecimal actualPrice;
    private BigDecimal principalAmount;
//    @Schema(description = "本金(实际收入): 单位,元")
//    private BigDecimal principalAmount;

//    private BigDecimal actualPrice;

    /**
     * 赠送金额消费 newFeild
     */
    @ExcelField(title = "赠送金额消费（元）", i18nTitle = "charger.order.freeGoldAmount", sort = 60)
//    private BigDecimal actualSubsidyAmount;
    private BigDecimal freeGoldAmount;


    @ExcelField(title = "支付时间", i18nTitle = "charger.order.payTime", sort = 61)
    private String payTime;

    /**
     * 支付单号
     **/
    @ExcelField(title = "支付单号", i18nTitle = "charger.order.payOrderNo", sort = 62)
    private String payOrderNo;

    /**
     * 退款单号
     **/
    @ExcelField(title = "退款单号", i18nTitle = "charger.order.refundOrderNo", sort = 63)
    private String refundOrderNo;

    /**
     * 订单处理类型 newFeild
     */
    @ExcelField(title = "订单处理类型", i18nTitle = "charger.order.processType", sort = 64)
//    private String processTypeName;
    private String processType;
    /**
     * 充电开始时间
     **/
    @ExcelField(title = "充电开始时间", i18nTitle = "charger.order.chargeStartTime", sort = 65)
    private String chargeStartTime;
    /**
     * 充电结束时间
     **/
    @ExcelField(title = "充电结束时间", i18nTitle = "charger.order.chargeEndTime", sort = 66)
    private String chargeEndTime;
    /**
     * 上传时间
     **/
    @ExcelField(title = "上传时间", i18nTitle = "charger.order.uploadTime", sort = 67)
    private String stopTime;


    /**
     * 电表开始读数
     **/
    @ExcelField(title = "电表开始读数", i18nTitle = "charger.order.startElectricity", sort = 68, digits = 4)
    private BigDecimal startElectricity;
    /**
     * 电表结束读数
     **/
    @ExcelField(title = "电表结束读数", i18nTitle = "charger.order.endElectricity", sort = 69, digits = 4)
    private BigDecimal endElectricity;

    /**
     * 订单时长
     **/
    @ExcelField(title = "订单时长", i18nTitle = "charger.order.duration", sort = 70)
    private String duration;

    /**
     * SOC
     **/
//    @ExcelField(title = "SOC", i18nTitle = "charger.order.orderNo", sort = 28)
//    private String socField;

    @ExcelField(title = "充电前SOC(%)", i18nTitle = "charger.order.startSoc", sort = 71)
    private Long startSoc;

    @ExcelField(title = "充电后SOC(%)", i18nTitle = "charger.order.stopSoc", sort = 72)
    private Long stopSoc;

    //23 结束原因
    @ExcelField(title = "停充原因", i18nTitle = "charger.order.stopReason", sort = 73)
    private String stopReason;

    //23 异常原因 newFeild
    @ExcelField(title = "异常原因", i18nTitle = "charger.order.abnormal", sort = 74)
//    private String abnormalName;
    private String abnormal;

    /**
     * 订单状态
     */
    private Integer status;
    private Long stationId;

    /**
     * 1、C端用户显示：用户昵称（用户ID） 2、桩主管理员（公有云端账号开启充电）：桩主账号（桩主的UID）
     */
    private String customerId;
    //private String mobilePhone;
    /**
     * 订单来源类型,0:微信渠道;1:APP在线发起充电;2:APP蓝牙发起充电:3:刷卡充电:4:桩上报离线数据
     */
    private String channelId;
    /**
     * 支付方式
     **/
    private String payModes;
    /**
     * 支付状态
     **/
    private String payStatus;
    /**
     * 电桩型号
     **/
    private String evseModel;
}
