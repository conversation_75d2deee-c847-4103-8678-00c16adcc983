package com.cdz360.biz.bi.service.site;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.bi.service.OrderBiService;
import com.cdz360.biz.ds.trading.ro.site.ds.BiPlugRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.BiSiteOrderRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.CommRoDs;
import com.cdz360.biz.model.bi.site.OrderElecDivision;
import com.cdz360.biz.model.bi.site.PlugErrorCount;
import com.cdz360.biz.model.bi.site.PlugUtilization;
import com.cdz360.biz.model.bi.site.SiteErrorCount;
import com.cdz360.biz.model.bi.site.SiteUtilization;
import com.cdz360.biz.model.bi.site.UserOrderElec;
import com.cdz360.biz.model.bi.type.OrderByType;
import com.cdz360.biz.model.common.constant.Constant;
import com.cdz360.biz.model.iot.param.SiteBiParam;
import com.cdz360.biz.model.iot.param.SiteBiTopParam;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import com.cdz360.biz.model.trading.order.type.BiDependOnType;
import com.cdz360.biz.model.trading.site.po.BiPlugPo;
import com.cdz360.biz.model.trading.site.po.BiSiteOrderPo;
import com.cdz360.biz.model.trading.site.po.CommPo;
import com.cdz360.biz.model.trading.site.vo.PlugPowerUseRateVo;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SiteBiService {

    // 返回前端最大采样点
//    private static final int MAX_SAMPLE_SIZE = Constant.MAX_SAMPLE_SIZE;

    @Autowired
    private BiSiteOrderRoDs biSiteOrderRoDs;

    @Autowired
    private OrderBiService orderBiService;

    @Autowired
    private BiPlugRoDs biPlugRoDs;

    @Autowired
//    private TRCommercialRoDs trCommercialRoDs;
    private CommRoDs commRoDs;


    /**
     * 统计场站的分时电量
     *
     * @param param
     * @return
     */
    public List<OrderElecDivision> chargeDivisionBi(SiteBiParam param) {
        // 校验参数
        Optional<String> validResult = this.validParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        // 调整前端入参
        param.resetTime();

        // 库中采样数据
        List<BiSiteOrderPo> biList;
        if (StringUtils.isNotBlank(param.getSiteId())) {
            biList = biSiteOrderRoDs.selectBySiteIdWhen(
                param.getSiteId(),
                BiDependOnType.valueOf(param.getDependOnTimeType().name()),
                new Date(param.getStartTime()), new Date(param.getEndTime()));
        } else {
            CommPo commercialById = commRoDs.getCommById(param.getCommId());
            IotAssert.isNotNull(commercialById, "找不到对应商户");
            IotAssert.isNotBlank(commercialById.getIdChain(), "对应商户idChain不能为空");

            biList = biSiteOrderRoDs.selectByIdChainWhen(
                commercialById.getIdChain(),
                BiDependOnType.valueOf(param.getDependOnTimeType().name()),
                new Date(param.getStartTime()), new Date(param.getEndTime()));
        }

        //获取站点总功率
//        BigDecimal power = biSiteOrderRoDs.getPowerBySiteId(param.getSiteId());

        // 按采样方式整合数据点
        // 注: 仅采样24点数据(24小时)
        if (param.getSampleType() == SiteBiSampleType.HOUR) {
            return this.divisionResult(biList.stream()
                .map(this::map2Division)
                .collect(Collectors.toList()), param);
        }

        // 其他采样方式
        List<OrderElecDivision> result = new ArrayList<>();

        // 按天采样
        // 注: 仅采样24点数据(24天)
        // 按月采样
        // 注: 仅采样24点数据(24月)

        // 调整时间，分组统计整合返回: 前端控制数据时间大小
        Map<Date, List<BiSiteOrderPo>> collect = biList.stream()
            .map(bi -> bi.setTime(this.resetDate(param.getSampleType(), bi.getTime())))
            .collect(Collectors.groupingBy(BiSiteOrderPo::getTime));

        collect.forEach((k, v) -> {
            OrderElecDivision d = new OrderElecDivision();
            d.setSiteId(param.getSiteId())
                .setTime(k);

            d.setElectricity(v.stream().map(BiSiteOrderPo::getElectricity)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
            d.setElecTag4(v.stream().map(BiSiteOrderPo::getElecTag4)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
            d.setElecTag3(v.stream().map(BiSiteOrderPo::getElecTag3)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
            d.setElecTag2(v.stream().map(BiSiteOrderPo::getElecTag2)
                .reduce(BigDecimal.ZERO, BigDecimal::add));
            d.setElecTag1(v.stream().map(BiSiteOrderPo::getElecTag1)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

            // 选出分组中最大id的记录，以便获取其功率
            BiSiteOrderPo maxIdRecord = v.stream()
                .reduce(new BiSiteOrderPo(), (x, y) -> x.getId() > y.getId() ? x : y);
            BigDecimal power =
                maxIdRecord == null ? BigDecimal.ZERO : BigDecimal.valueOf(maxIdRecord.getPower());

            //计算站点功率利用率
            if (power.compareTo(BigDecimal.ZERO) == 0) {
                d.setPowerRated(BigDecimal.ZERO);
            } else {
                if (SiteBiSampleType.DAY.equals(param.getSampleType())) {
                    d.setPowerRated(d.getElectricity()
                        .divide(power.multiply(new BigDecimal(24)), 4, RoundingMode.HALF_UP));
//                    d.setPower(power);
                } else if (SiteBiSampleType.MONTH.equals(param.getSampleType())) {

                    // 获取本月有多少天
//                    Calendar calendar = Calendar.getInstance();
//                    calendar.setTime(k);
//                    calendar.add(Calendar.MONTH, 1);
//                    calendar.add(Calendar.DATE, -1);
                    int dayCountOfMonth = DateUtil.getDayCountInMonth(
                        k);// calendar.get(Calendar.DATE);

                    // 当前月特殊处理
                    Calendar todayCalender = Calendar.getInstance();
                    Calendar kCalender = Calendar.getInstance();
                    kCalender.setTime(k);
                    if (todayCalender.get(Calendar.YEAR) == kCalender.get(Calendar.YEAR)
                        && todayCalender.get(Calendar.MONTH) == kCalender.get(Calendar.MONTH)) {
                        dayCountOfMonth = todayCalender.get(Calendar.DAY_OF_MONTH);
                    }

                    d.setPowerRated(d.getElectricity()
                        .divide(power.multiply(new BigDecimal(24 * dayCountOfMonth)),
                            4, RoundingMode.HALF_UP));
//                    d.setPower(power).setDayCount(Long.valueOf(dayCountOfMonth));
                } else {
                    IotAssert.isTrue(false, "暂不支持该种采样时间单位: " + param.getSampleType());
                }
            }

            result.add(d);
        });

        // 调整排序
        result.sort(Comparator.comparing(OrderElecDivision::getTime).reversed());
        return this.divisionResult(result, param);
    }

    private List<OrderElecDivision> divisionResult(List<OrderElecDivision> list,
        SiteBiParam param) {
        List<LocalDateTime> timeList = reviseResult(param);
        if (CollectionUtils.isEmpty(timeList)) {
            throw new DcArgumentException("开始/结束时间不正确");
        }

//        if (timeList.size() < Constant.MAX_SAMPLE_SIZE) {
//            return list;
//        }

        List<OrderElecDivision> result = new ArrayList<>();
        timeList.stream().limit(Constant.MAX_SAMPLE_SIZE).forEach(time -> {
            Optional<OrderElecDivision> first = list.stream()
                .filter(d -> d.getTime().toInstant().atOffset(ZoneOffset.of("+8"))
                    .toLocalDateTime().isEqual(time)).findFirst();
            if (first.isPresent()) {
                result.add(first.get());
            } else {
                OrderElecDivision division = new OrderElecDivision()
                    .setTime(
                        new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()))
                    .setSiteId(param.getSiteId());
                result.add(division);
            }
        });

        return result;
    }

    private List<SiteUtilization> utilizationResult(List<SiteUtilization> list, SiteBiParam param) {
        List<LocalDateTime> timeList = reviseResult(param);
        if (CollectionUtils.isEmpty(timeList)) {
            throw new DcArgumentException("开始/结束时间不正确");
        }
//        if (timeList.size() < Constant.MAX_SAMPLE_SIZE) {
//            return list;
//        }

        List<SiteUtilization> result = new ArrayList<>();
        timeList.forEach(time -> {
            Optional<SiteUtilization> first = list.stream()
                .filter(d -> d.getDate().toInstant().atOffset(ZoneOffset.of("+8"))
                    .toLocalDateTime().isEqual(time)).findFirst();
            if (first.isPresent()) {
                result.add(first.get());
            } else {
                SiteUtilization utilization = new SiteUtilization()
                    .setDate(
                        new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()))
                    .setSiteId(param.getSiteId());
                result.add(utilization);
            }
        });

        return result;
    }

    /**
     * 调整数据返回，固定长度
     *
     * @return
     */
    private List<LocalDateTime> reviseResult(SiteBiParam param) {
        param.resetTime();

        LocalDateTime start = param.getFromTime().toInstant()
            .atOffset(ZoneOffset.of("+8"))
            .toLocalDateTime();
        LocalDateTime end = param.getToTime().toInstant()
            .atOffset(ZoneOffset.of("+8"))
            .toLocalDateTime();

        List<LocalDateTime> timeList = new ArrayList<>();
        while (start.isBefore(end)) {
            timeList.add(start);
            if (param.getSampleType() == SiteBiSampleType.HOUR) {
                start = start.plusHours(1);
            } else if (param.getSampleType() == SiteBiSampleType.DAY) {
                start = start.plusDays(1);
            } else {
                start = start.plusMonths(1);
            }
        }

        log.info("time size = {}", timeList.size());
        return timeList;
    }

    /**
     * 调整时间点
     *
     * @param sampleType
     * @param time
     * @return
     */
    private Date resetDate(SiteBiSampleType sampleType, Date time) {
        // 分秒都为0
        LocalDateTime local = time.toInstant().atOffset(ZoneOffset.of("+8"))
            .toLocalDateTime().withMinute(0).withSecond(0).withNano(0);

        if (sampleType == SiteBiSampleType.DAY ||
            sampleType == SiteBiSampleType.MONTH) {
            local = local.withHour(0);
        }

        if (sampleType == SiteBiSampleType.MONTH) {
            // 当月第一天
            local = local.with(TemporalAdjusters.firstDayOfMonth());
        }

        return new Date(local.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
    }

    private OrderElecDivision map2Division(BiSiteOrderPo po) {
        OrderElecDivision result = new OrderElecDivision();
        BeanUtils.copyProperties(po, result);
        return result;
    }

    private Optional<String> validParam(SiteBiParam param) {
        if (StringUtils.isBlank(param.getSiteId()) && param.getCommId() == null) {
//            return Optional.of("场站Id没有提供");
            return Optional.of("请提供场站Id或商户Id");
        }

        if (null == param.getStartTime() || null == param.getEndTime()) {
            return Optional.of("开始结束时间没有提供");
        }

        if (param.getStartTime() >= param.getEndTime()) {
            return Optional.of("开始时间大于等于结束时间，不合理");
        }

        if (reviseResult(param).size() > Constant.MAX_SAMPLE_SIZE) {
            return Optional.of("时间分片大于最大值，请传入正确的开始/结束时间");
        }

        return Optional.empty();
    }

    /**
     * 场站用户充电排行榜
     *
     * @param param
     * @return
     */
    public List<UserOrderElec> userChargeDivisionBi(SiteBiTopParam param) {

        // 参数时间
        Optional<String> validResult = this.validParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        if (null == param.getTopCount()) {
            log.info("查询参数无效: top没有提供");
            throw new DcArgumentException("参数有误，请提供top参数");
        }

        // 充值以下时间
        param.resetTime();

        return orderBiService.userChargeDivisionBi(param);
    }

    /**
     * 桩时长利用率
     *
     * @param param
     * @return
     */
    public List<SiteUtilization> utilizationBi(SiteBiParam param) {

        // 参数校验
        Optional<String> validResult = this.validParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        // 重置以下时间
        param.resetTime();

        // 库中采样数据
        List<BiPlugPo> poList;
        List<SiteUtilization> powerRateList;

        // 按月采样时，计算月对应天数
        Map<Date, Integer> dayCountOfMonthMap = new HashMap<>();
        if (SiteBiSampleType.MONTH.equals(param.getSampleType())) {
            List<LocalDateTime> timeList = reviseResult(param);
            timeList.stream().map(e -> {
                Instant instant = e.atZone(ZoneId.systemDefault()).toInstant();
                return Date.from(instant);
            }).forEach(e -> dayCountOfMonthMap.put(e, DateUtil.getDayCountInMonth(e)));
        }

        if (StringUtils.isNotBlank(param.getSiteId())) {
            poList = biPlugRoDs.selectBySiteIdWhen(
                param.getSiteId(),
                null, null,
                new Date(param.getStartTime()), new Date(param.getEndTime()));

            powerRateList = biSiteOrderRoDs.getSitePowerUseRate(param.getSiteId(),
                SiteBiSampleType.MONTH.equals(param.getSampleType()),
                new Date(param.getStartTime()),
                new Date(param.getEndTime()));

        } else {
            CommPo commercialById = commRoDs.getCommById(param.getCommId());
            IotAssert.isNotNull(commercialById, "找不到对应商户");
            IotAssert.isNotBlank(commercialById.getIdChain(), "对应商户idChain不能为空");

            List<SiteUtilization> sitePowerUseRateByIdChain = biSiteOrderRoDs.getSitePowerUseRateByIdChain(
                commercialById.getIdChain(),
                SiteBiSampleType.MONTH.equals(param.getSampleType()),
                new Date(param.getStartTime()),
                new Date(param.getEndTime()));

            log.info("{}", sitePowerUseRateByIdChain);

            // 分组统计, 将计算压力释放到DB
            List<SiteUtilization> utilizationBiFromDb = this.getUtilizationBiFromDb(
                commercialById.getIdChain(), param);

            Map<Date, SiteUtilization> powerUseRateMap = new HashMap<>();
            sitePowerUseRateByIdChain.stream().forEach(e -> powerUseRateMap.put(e.getDate(), e));

            utilizationBiFromDb.stream().forEach(e -> this.setPowerInfo(e,
                powerUseRateMap.get(e.getDate()),
                dayCountOfMonthMap,
                e.getDate(),
                param.getSampleType()));

            return utilizationBiFromDb;
//            poList = biPlugRoDs.selectByIdChainWhen(
//                    commercialById.getIdChain(),
//                    null, null,
//                    new Date(param.getStartTime()), new Date(param.getEndTime()));

        }

        // 其他采样方式
        List<SiteUtilization> result = new ArrayList<>();

        // 按天采样
        // 注: 仅采样24点数据(24天)
        // 按月采样
        // 注: 仅采样24点数据(24月)

        // 调整时间，分组统计整合返回: 前端控制数据时间大小
        Map<Date, List<BiPlugPo>> collect = poList.stream()
            .map(po -> po.setDate(this.resetDate(param.getSampleType(), po.getDate())))
            .collect(Collectors.groupingBy(BiPlugPo::getDate));

        // 从功率利用率列表中获取map
        Map<Date, SiteUtilization> powerUseRateMap = new HashMap<>();
        powerRateList.stream().forEach(e -> powerUseRateMap.put(e.getDate(), e));

        collect.forEach((k, v) -> {
            SiteUtilization u = new SiteUtilization();
            u.setSiteId(param.getSiteId())
                .setDate(k);

            u.setDuration(v.stream().mapToLong(BiPlugPo::getDuration).sum());

            // 有效使用时间
            u.setUseRate(
                initUserRate(u.getDuration(), v.stream().mapToLong(BiPlugPo::getAvailable).sum()));

            // 单个枪头平均使用时长（秒）
            u.setPlugUseTime(u.getDuration() / v.size());

            SiteUtilization siteUtilization = powerUseRateMap.get(k);
            this.setPowerInfo(u, siteUtilization, dayCountOfMonthMap, k, param.getSampleType());
            if (false && siteUtilization != null) {
//                if(siteUtilization.getPowerUseRate() != null) {
//                    u.setPowerUseRate(siteUtilization.getPowerUseRate().setScale(4, BigDecimal.ROUND_HALF_UP));
//                }
                // 设置功率利用率
                if (siteUtilization != null && siteUtilization.getDailyPower() != null) {
                    BigDecimal dailyPower = BigDecimal.valueOf(siteUtilization.getDailyPower());
                    BigDecimal rateFactor;
//                    Long durationFactor;
                    if (SiteBiSampleType.MONTH.equals(param.getSampleType())) {
                        // 月计算
                        rateFactor = BigDecimal.valueOf(24 * dayCountOfMonthMap.get(k));
//                        durationFactor = dayCountOfMonthMap.get(k).longValue();
                    } else {
                        // 日计算
                        rateFactor = BigDecimal.valueOf(24);
//                        durationFactor = 1L;
                    }
                    if (dailyPower.equals(BigDecimal.ZERO)) {
                        u.setPowerUseRate(BigDecimal.ZERO);
                    } else {
                        u.setPowerUseRate(
                            siteUtilization.getElectricitySum()
                                .divide(rateFactor.multiply(dailyPower), 4, RoundingMode.HALF_UP));
                    }
//                                    .divide(dailyPower, 4, RoundingMode.HALF_UP));
//                    u.setFactor(rateFactor);

                    u.setDurationPerPower(
                        u.getPowerUseRate().multiply(BigDecimal.valueOf(86400)).longValue());
                }

                u.setDailyPower(siteUtilization.getDailyPower())
                    .setDailyPowerId(siteUtilization.getDailyPowerId())
                    .setElectricitySum(siteUtilization.getElectricitySum());
//                if(siteUtilization.getPowerSum() != null && siteUtilization.getPowerSum().compareTo(BigDecimal.ZERO) > 0) {
//                    u.setDurationPerPower(
//                            BigDecimal.valueOf(siteUtilization.getPowerSum().longValue())
//                            .divide(siteUtilization.getPowerSum())
//                            .setScale(0, RoundingMode.HALF_UP)
//                            .longValue()
//                    );
//                    u.setPowerSum(siteUtilization.getPowerSum());
//                }
            }

            result.add(u);
        });

        // 调整排序
        result.sort(Comparator.comparing(SiteUtilization::getDate));
        return this.utilizationResult(result, param);
    }

    /**
     * 填写功率相关字段
     *
     * @param target             填写目标
     * @param siteUtilization
     * @param dayCountOfMonthMap
     * @param date
     * @param sampleType
     */
    private void setPowerInfo(SiteUtilization target,
        SiteUtilization siteUtilization,
        Map<Date, Integer> dayCountOfMonthMap,
        Date date,
        SiteBiSampleType sampleType) {
        if (siteUtilization != null) {
//                if(siteUtilization.getPowerUseRate() != null) {
//                    u.setPowerUseRate(siteUtilization.getPowerUseRate().setScale(4, BigDecimal.ROUND_HALF_UP));
//                }
            // 设置功率利用率
            if (/*siteUtilization != null &&*/ siteUtilization.getDailyPower() != null) {
                BigDecimal dailyPower = BigDecimal.valueOf(siteUtilization.getDailyPower());
                BigDecimal rateFactor;
//                    Long durationFactor;
                if (SiteBiSampleType.MONTH.equals(sampleType)) {
                    // 月计算
                    rateFactor = BigDecimal.valueOf(24 * dayCountOfMonthMap.get(date));
//                        durationFactor = dayCountOfMonthMap.get(k).longValue();
                } else {
                    // 日计算
                    rateFactor = BigDecimal.valueOf(24);
//                        durationFactor = 1L;
                }
                if (dailyPower.equals(BigDecimal.ZERO)) {
                    target.setPowerUseRate(BigDecimal.ZERO);
                } else {
                    target.setPowerUseRate(
                        siteUtilization.getElectricitySum()
                            .divide(rateFactor.multiply(dailyPower), 4, RoundingMode.HALF_UP));
                }
//                                    .divide(dailyPower, 4, RoundingMode.HALF_UP));
//                    u.setFactor(rateFactor);

                target.setDurationPerPower(
                    target.getPowerUseRate().multiply(BigDecimal.valueOf(86400)).longValue());
            }

            target.setDailyPower(siteUtilization.getDailyPower())
                .setDailyPowerId(siteUtilization.getDailyPowerId())
                .setElectricitySum(siteUtilization.getElectricitySum());
//                if(siteUtilization.getPowerSum() != null && siteUtilization.getPowerSum().compareTo(BigDecimal.ZERO) > 0) {
//                    u.setDurationPerPower(
//                            BigDecimal.valueOf(siteUtilization.getPowerSum().longValue())
//                            .divide(siteUtilization.getPowerSum())
//                            .setScale(0, RoundingMode.HALF_UP)
//                            .longValue()
//                    );
//                    u.setPowerSum(siteUtilization.getPowerSum());
//                }
        }

    }

    /**
     * 分组统计, 将计算压力释放到DB
     *
     * @param idChain
     * @param param
     * @return
     */
    private List<SiteUtilization> getUtilizationBiFromDb(String idChain, SiteBiParam param) {
        IotAssert.isNotBlank(idChain, "请传入idChain");
        List<SiteUtilization> result = biPlugRoDs.getUtilizationBiFromDb(idChain,
            new Date(param.getStartTime()),
            new Date(param.getEndTime()),
            SiteBiSampleType.MONTH.compareTo(param.getSampleType()));

        result.sort(Comparator.comparing(SiteUtilization::getDate));
        return this.utilizationResult(result, param);
    }

    /**
     * 场站桩利用率排行榜
     *
     * @param param
     * @return
     */
    public List<PlugUtilization> plugUtilizationBi(SiteBiTopParam param) {

        // 参数校验
        Optional<String> validResult = this.validParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        if (null == param.getTopCount()) {
            log.info("查询参数无效: 没有查询数据量");
            throw new DcArgumentException("参数有误，请提供");
        }

        // 充值以下时间
        param.resetTime();

        // 库中采样数据
        List<BiPlugPo> poList = biPlugRoDs.selectBySiteIdWhen(
            param.getSiteId(),
            null, null,
            new Date(param.getStartTime()), new Date(param.getEndTime()));

        List<PlugPowerUseRateVo> powerRateList = biPlugRoDs.getSitePowerUseRate(
            param.getSiteId(),
            null, null,
            new Date(param.getStartTime()), new Date(param.getEndTime()));

        Map<String, PlugPowerUseRateVo> plugPowerUseRateVoMap = powerRateList.stream()
            .collect(Collectors.toMap(PlugPowerUseRateVo::getEvseNo, o -> o));

        // 其他采样方式
        List<PlugUtilization> result = new ArrayList<>();

        // 按天采样
        // 注: 仅采样24点数据(24天)
        // 按月采样
        // 注: 仅采样24点数据(24月)

        // 调整时间，分组统计整合返回: 前端控制数据时间大小
        Map<String, List<BiPlugPo>> collect = poList.stream()
            .map(po -> po.setDate(this.resetDate(param.getSampleType(), po.getDate())))
            .collect(Collectors.groupingBy(BiPlugPo::getEvseNo));

        collect.forEach((k, v) -> {
            // 按照枪分组
            Map<Integer, List<BiPlugPo>> plugMap = v.stream()
                .collect(Collectors.groupingBy(BiPlugPo::getPlugId));
            plugMap.forEach((p, l) -> {
                PlugUtilization u = new PlugUtilization();
                u.setSiteId(param.getSiteId())
                    .setEvseNo(k)
                    .setPlugId(p);

                u.setDuration(l.stream().mapToLong(BiPlugPo::getDuration).sum());

                // 有效使用时间
                u.setUseRate(initUserRate(u.getDuration(),
                    l.stream().mapToLong(BiPlugPo::getAvailable).sum()));

                if (plugPowerUseRateVoMap.get(k) != null) {
                    u.setPowerUseRate(plugPowerUseRateVoMap.get(k)
                        .getPowerUseRate()
                        .setScale(4, RoundingMode.HALF_UP));
                }

                result.add(u);
            });
        });

        // 调整排序
        // README: 返回数据还包含其他信息，在 ant 中赋值
        if (OrderByType.useRate.equals(param.getOrderByType())) {
            result.sort(Comparator.comparing(PlugUtilization::getUseRate).reversed());
        } else {
            result.sort(Comparator.comparing(PlugUtilization::getPowerUseRate).reversed());
        }
        return result.stream().limit(param.getTopCount()).collect(Collectors.toList());
    }

    public static BigDecimal initUserRate(long duration, long available) {
        if (0 == available) {
            return BigDecimal.ZERO;
        }

        return BigDecimal.valueOf(duration)
            .divide(BigDecimal.valueOf(available), 4, RoundingMode.HALF_UP);
    }

//    public static void main(String[] args) {
//        BigDecimal used = initUserRate(34622, 86400);
//        System.out.println(used);
//    }

    public List<SiteErrorCount> breakdownBi(SiteBiParam param) {
        // 参数校验
        Optional<String> validResult = this.validParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        IotAssert.isTrue(param.getSampleType() != SiteBiSampleType.HOUR,
            "桩异常统计不支持小时筛选");

        // 调整前端入参
        param.resetTime();

        // 库中采样数据
        Integer plugId = 1;//故障次数和离线次数，当plugId为1时使用
        List<BiPlugPo> poList = biPlugRoDs.selectBySiteIdWhen(param.getSiteId(), null, plugId,
            param.getFromTime(), param.getToTime());

        List<SiteErrorCount> result = new ArrayList<>();
        // 按天采样
        // 注: 仅采样24点数据(24天)
        // 按月采样
        // 注: 仅采样24点数据(24月)

        // 调整时间，分组统计整合返回: 前端控制数据时间大小
        Map<Date, List<BiPlugPo>> collect = poList.stream()
            .map(bi -> bi.setDate(this.resetDate(param.getSampleType(), bi.getDate())))
            .collect(Collectors.groupingBy(BiPlugPo::getDate));

        collect.forEach((k, v) -> {
            SiteErrorCount temp = new SiteErrorCount();
            temp.setSiteId(param.getSiteId())
                .setDate(k);

            // type = 0 桩信息，type=1 逆变器信息 type=2 储能ESS告警信息
            temp.setErrorCount(
                v.stream().filter(e -> e.getType().equals(0L)).mapToLong(BiPlugPo::getErrorCount)
                    .sum());
            temp.setOfflineCount(v.stream().mapToLong(BiPlugPo::getOfflineCount).sum());
            temp.setAlarmCount(
                v.stream().filter(e -> e.getType().equals(1L)).mapToLong(BiPlugPo::getErrorCount)
                    .sum());
            temp.setEssAlarmCount(
                v.stream().filter(e -> e.getType().equals(2L)).mapToLong(BiPlugPo::getErrorCount)
                    .sum());
            result.add(temp);
        });

        result.sort(Comparator.comparing(SiteErrorCount::getDate));
        return this.breakdownBiResult(result, param);
    }

    public List<SiteErrorCount> commEssSiteBreakdownBi(SiteBiParam param) {
        // 参数校验
        Optional<String> validResult = this.validParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        IotAssert.isTrue(param.getSampleType() != SiteBiSampleType.HOUR,
            "桩异常统计不支持小时筛选");

        // 调整前端入参
        param.resetTime();

        // 库中采样数据
        Integer plugId = 1;//故障次数和离线次数，当plugId为1时使用
        List<BiPlugPo> poList = biPlugRoDs.selectCommEssBySiteIdWhen(
            param.getSiteId(), null, param.getFromTime(), param.getToTime());

        List<SiteErrorCount> result = new ArrayList<>();
        // 按天采样
        // 注: 仅采样24点数据(24天)
        // 按月采样
        // 注: 仅采样24点数据(24月)

        // 调整时间，分组统计整合返回: 前端控制数据时间大小
        Map<Date, List<BiPlugPo>> collect = poList.stream()
            .map(bi -> bi.setDate(this.resetDate(param.getSampleType(), bi.getDate())))
            .collect(Collectors.groupingBy(BiPlugPo::getDate));

        collect.forEach((k, v) -> {
            SiteErrorCount temp = new SiteErrorCount();
            temp.setSiteId(param.getSiteId())
                .setDate(k);

            // type = 0 桩信息，type=1 逆变器信息 type=2 储能ESS告警信息
//            temp.setErrorCount(
//                v.stream().filter(e -> e.getType().equals(0L)).mapToLong(BiPlugPo::getErrorCount)
//                    .sum());
//            temp.setOfflineCount(v.stream().mapToLong(BiPlugPo::getOfflineCount).sum());
//            temp.setAlarmCount(
//                v.stream().filter(e -> e.getType().equals(1L)).mapToLong(BiPlugPo::getErrorCount)
//                    .sum());
            temp.setEssAlarmCount(
                v.stream().filter(e -> e.getType() > 2).mapToLong(BiPlugPo::getErrorCount)
                    .sum());
            result.add(temp);
        });

        result.sort(Comparator.comparing(SiteErrorCount::getDate));
        return this.breakdownBiResult(result, param);
    }

    private List<SiteErrorCount> breakdownBiResult(List<SiteErrorCount> list, SiteBiParam param) {
        List<LocalDateTime> timeList = reviseResult(param);
        if (CollectionUtils.isEmpty(timeList)) {
            throw new DcArgumentException("开始/结束时间不正确");
        }
//        if (timeList.size() < Constant.MAX_SAMPLE_SIZE) {
//            return list;
//        }

        List<SiteErrorCount> result = new ArrayList<>();
        timeList.stream().limit(Constant.MAX_SAMPLE_SIZE).forEach(time -> {
            Optional<SiteErrorCount> first = list.stream()
                .filter(d -> d.getDate().toInstant().atOffset(ZoneOffset.of("+8"))
                    .toLocalDateTime().isEqual(time)).findFirst();
            if (first.isPresent()) {
                result.add(first.get());
            } else {
                SiteErrorCount siteErrorCount = new SiteErrorCount()
                    .setDate(
                        new Date(time.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()))
                    .setSiteId(param.getSiteId());
                result.add(siteErrorCount);
            }
        });
        return result;
    }

    public List<PlugErrorCount> plugBreakdownBi(SiteBiTopParam param) {
        // 参数校验
        Optional<String> validResult = this.validParam(param);
        if (validResult.isPresent()) {
            log.info("查询参数无效: {}", validResult.get());
            throw new DcArgumentException(validResult.get());
        }

        IotAssert.isTrue(param.getSampleType() != SiteBiSampleType.HOUR,
            "桩异常统计不支持小时筛选");
        IotAssert.isNotNull(param.getBreakDownType(), "请传入枪异常类型");

        // 调整前端入参
        param.resetTime();

        // 库中采样数据
        Integer plugId = 1;//故障次数，当plugId为1时使用
        return biPlugRoDs.selectPlugErrorCount(param.getSiteId(), plugId,
            param.getFromTime(), param.getToTime(), param.getTopCount(),
            param.getBreakDownType().name());
    }


}
