package com.cdz360.biz.bi.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.bi.service.SettRecordService;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.chargerlinkcar.framework.common.utils.UUIDUtils;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import java.text.SimpleDateFormat;
import java.util.Date;

@Tag(name = "商户结算单相关接口", description = "商户结算单相关接口")
@Slf4j
@RestController
public class SettRecordRest {

    @Autowired
    private SettRecordService settRecordService;

    @Operation(summary = "商户结算单明细导出Excel")
    @PostMapping(value = "/bi/sett/record/exportExcel")
    public Mono<ObjectResponse<ExcelPosition>> exportSettRecExcel(
        @Parameter(name = "结算单编号", required = true)
        @RequestParam(value = "settleId") String settleId) {
        log.info("商户结算单明细导出Excel: settleId = {}", settleId);
        return Mono.just(settleId)
            .map(no -> new ExcelPosition()
                .setSubFileName(UUIDUtils.getUuid32())
                .setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date())))
            .doOnNext(pos -> settRecordService.exportSettRecExcel(settleId, pos))
            .map(RestUtils::buildObjectResponse);
    }
}
