package com.cdz360.biz.bi.mapper;


import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface ChargerOrderMapper {

//    /**
//     * 根据条件查询订单统计数据
//     *
//     * @param chargerOrderParam
//     * @return
//     */
//    ChargerOrderDataVo getChargerOrderData(ChargerOrderParam chargerOrderParam);

    /**
     * 订单导出专用
     *
     * @param map
     * @return
     */
    List<ChargerOrderVo> selectChargeOrderListForExcel(Map<String, Object> map);
}