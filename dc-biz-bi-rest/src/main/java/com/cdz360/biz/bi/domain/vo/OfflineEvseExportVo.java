package com.cdz360.biz.bi.domain.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import java.io.Serializable;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OfflineEvseExportVo implements Serializable {

    @ExcelField(title = "电桩编号", sort = 1)
    private String evseNo;

    @ExcelField(title = "电桩名称", sort = 2)
    private String evseName;

    @ExcelField(title = "所属站点", sort = 3)
    private String siteName;

    @ExcelField(title = "所属商户", sort = 4)
    private String commName;

    @ExcelField(title = "电桩型号", sort = 5)
    private String evseModel;

    @ExcelField(title = "电流类型", sort = 6)
    private String supplyType;

    @ExcelField(title = "额定功率(kW)", sort = 7)
    private Integer power;

    @ExcelField(title = "软件版本", sort = 8)
    private String firmwareVer;

    @ExcelField(title = "桩协议", sort = 9)
    private Integer protocolVer;

    @ExcelField(title = "SIM卡号", sort = 10)
    private String iccid;

}
