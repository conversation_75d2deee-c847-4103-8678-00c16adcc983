package com.cdz360.biz.bi.domain.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode
@ToString
@Schema(description = "充电管理平台-直付到账对账订单")
public class ZftBillExport2Vo implements Serializable {

    @ExcelField(title = "对账结果", sort = 0)
    @Schema(description = "支付平台对账结果,FULL_MATCH(完全匹配); AMOUNT_NOT_MATCH(金额不匹配); NO_NOT_MATCH(未找到对应订单); OTHERS(其他)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String checkResult;

    @ExcelField(title = "交易时间", sort = 1)
    @Schema(description = "操作时间")
    private String payTime;

    @ExcelField(title = "直付商家", sort = 2)
    @Schema(description = "直付商家")
    private String zftName;

    @ExcelField(title = "所属商户", sort = 3)
    @Schema(description = "所属商户")
    private String commName;

    @ExcelField(title = "用户手机号", sort = 4)
    @Schema(description = "用户手机号")
    private String phone;

    @ExcelField(title = "账户名称", sort = 5)
    @Schema(description = "账户名称")
    private String accountType;

    @ExcelField(title = "充电订单号", sort = 5)
    @Schema(description = "充电订单号")
    private String chargeOrderNo;

    @ExcelField(title = "平台订单号", sort = 6)
    @Schema(description = "平台订单号")
    private String orderId;

    @ExcelField(title = "交易渠道", sort = 7)
    @Schema(description = "交易渠道")
    private String payWay;

    @ExcelField(title = "微信/支付宝订单号", sort = 8)
    @Schema(description = "微信/支付宝订单号")
    private String tradeNo;

    @ExcelField(title = "财务类型", sort = 8)
    @Schema(description = "财务类型")
    private String financialType;

    @ExcelField(title = "交易金额(元)", sort = 10)
    @Schema(description = "收支类型: IN_FLOW(收入), OUT_FLOW(支出)",
        format = "java.lang.Integer")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String tradeType;

    @ExcelField(title = "入账(元)", sort = 13)
    @Schema(description = "入账(元)")
    private BigDecimal chargeAmount;

    @ExcelField(title = "退款(元)", sort = 15)
    @Schema(description = "退款(元)")
    private BigDecimal refundAmount;

//    @ExcelField(title = "实收(元)", sort = 8)
//    @Schema(description = "实收(元)")
//    private BigDecimal realAmount;

    @ExcelField(title = "交易金额(元)", sort = 20)
    @Schema(description = "关联支付平台账单名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dailyBillName;
}
