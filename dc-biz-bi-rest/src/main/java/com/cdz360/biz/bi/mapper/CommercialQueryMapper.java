package com.cdz360.biz.bi.mapper;

import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * CommercialQueryMapper
 *
 * @since 7/24/2019 11:04 AM
 * <AUTHOR>
 */

@Mapper
public interface CommercialQueryMapper {

    /**
     * 获取该商户信息
     *
     * @param id
     * @return
     */
    CommercialSimpleVo getCommerial(@Param("id") long id);

//    /**
//     * 查询ID对应的所有子商户ID列表
//     * @param id
//     * @return
//     */
//    List<Long> listSubCommercialIds(@Param("id") long id);

    /**
     * 查询商户列表信息
     */
    List<CommercialSimpleVo> getCommercialSimpleVoList();

    /**
     * 获取该商户下一级商户信息
     *
     * @param commId
     * @return
     */
    List<CommercialSimpleVo> getCommByPid(@Param("commId") long commId);

    List<CommercialSimpleVo> getCommerialsByCommIds(@Param("commIds") List<Long> commIds);

}