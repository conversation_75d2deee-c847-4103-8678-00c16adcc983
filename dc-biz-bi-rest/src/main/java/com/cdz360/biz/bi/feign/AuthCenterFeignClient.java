package com.cdz360.biz.bi.feign;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.user.po.SysUserPo;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 权限中心用户操作的FeignClient
 */
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_AUTH, fallbackFactory = HystrixAuthCenterClientFactory.class)
public interface AuthCenterFeignClient {

    /**
     * 根据token获取用户id
     *
     * @param token
     */
    @RequestMapping(value = "/api/info/token", method = RequestMethod.GET)
    ObjectResponse<JsonNode> getCurrentUser(@RequestParam(value = "token") String token);

    /**
     * 根据id集合查询用户信息
     *
     * @param idList
     */
    @PostMapping(value = "/data/users/querySysUserByIds")
    ListResponse<SysUserVo> querySysUserByIds(@RequestBody List<Long> idList);

    @GetMapping(value = "/data/users/findByName")
    ListResponse<SysUserVo> findByName(@RequestParam(name = "name") String name,
        @RequestParam(name = "accurateQuery", defaultValue = "true")
        Boolean accurateQuery);

    @PostMapping("/api/sys/user/getByUserNameAndPlatform")
    ObjectResponse<SysUserPo> getByUserNameAndPlatform(
        @RequestParam(value = "username") String username,
        @RequestParam(value = "platform") AppClientType platform);

    @GetMapping("/api/corp/getCorp")
    ObjectResponse<CorpPo> getCorp(@RequestParam(value = "corpId") Long corpId);
}
