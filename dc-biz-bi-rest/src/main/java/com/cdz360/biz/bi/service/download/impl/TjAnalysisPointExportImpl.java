package com.cdz360.biz.bi.service.download.impl;

import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.bi.feign.reactor.BizTjFeignClient;
import com.cdz360.biz.bi.service.download.IFileExport;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaAnalysisPointParam;
import com.cdz360.biz.model.tj.kc.vo.SiteWithinTjVo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisPointWithSiteVo;
import com.cdz360.biz.model.tj.kc.vo.TjCompetitorSiteVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveySimpleVo;
import com.chargerlinkcar.framework.common.domain.invoice.nsr.ExAnalysisPoint;
import com.chargerlinkcar.framework.common.utils.ExcelUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TjAnalysisPointExportImpl extends
    AbstractFileExport<ListTjAreaAnalysisPointParam, ExcelPosition>
    implements IFileExport<ListTjAreaAnalysisPointParam, ExcelPosition> {

    @Value("${excel.tmp.file:files/ANALYSIS_POINT_TEMP_FILE.xlsx}")
    private String ANALYSIS_POINT_TEMP_FILE;

    @Autowired
    private BizTjFeignClient bizTjFeignClient;

    @PostConstruct
    public void init() {
        downloadFileProxy.addProxy(DownloadFunctionType.TJ_ANALYSIS_POINT, this);
    }

    @Override
    public Class<ListTjAreaAnalysisPointParam> paramClazz() {
        return ListTjAreaAnalysisPointParam.class;
    }

    @SuppressWarnings("all")
    @Override
    public void genFile(String context, String pos) throws IOException {
        ListTjAreaAnalysisPointParam pointParam = this.convert(context);
        pointParam.setTotal(false);
        ExcelUtil.builder(new ClassPathResource(ANALYSIS_POINT_TEMP_FILE).getInputStream(),
                exportFileConfig.getExcelDir(), this.convertPos(pos))
            .startRowNum(2) // 索引从2开始
            .initExcelFieldList(ExAnalysisPoint.class)
            .loopAppendData(100, (start, size) -> {
                pointParam.setStart((long) ((start - 1) * size))
                    .setSize(size);

                List<TjAreaAnalysisPointWithSiteVo> data =
                    bizTjFeignClient.findTjAnalysisPointWithSite(pointParam)
                        .map(FeignResponseValidate::checkReturn)
                        .block(Duration.ofSeconds(50L));

                return data.stream().map(this::toExAnalysisPoint)
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());
            })
            .write2File();
    }

    private ArrayList<ExAnalysisPoint> toExAnalysisPoint(TjAreaAnalysisPointWithSiteVo x) {
        List<TjCompetitorSiteVo> petSites = x.getCompetitorSiteVos().stream()
            .sorted(Comparator.comparing(TjCompetitorSiteVo::getCompetitorId))
            .collect(Collectors.toList());
        List<TjSurveySimpleVo> surveySites = x.getSurveySimpleVos();
        List<SiteWithinTjVo> operateSites = x.getSiteWithinTjVos();

        int len = Stream.of(petSites.size(), surveySites.size(), operateSites.size())
            .max(Comparator.comparingInt(o -> o))
            .get();

        ArrayList<ExAnalysisPoint> result = new ArrayList<>(len);
        if (len == 0) {
            ExAnalysisPoint tmp = new ExAnalysisPoint()
                .setAnalysisPointNum(x.getNum())
                .setAnalysisPointStatus(x.getStatus());

            if (StringUtils.isNotBlank(x.getAddress())) {
                tmp.setAnalysisPointAddress(x.getAddress());
            } else {
                tmp.setAnalysisPointAddress("未知");
            }

            result.add(tmp);
        } else {
            for (int i = 0; i < len; i++) {
                ExAnalysisPoint tmp = new ExAnalysisPoint()
                    .setAnalysisPointNum(x.getNum())
                    .setAnalysisPointAddress(x.getAddress())
                    .setAnalysisPointStatus(x.getStatus());

                if (StringUtils.isNotBlank(x.getAddress())) {
                    tmp.setAnalysisPointAddress(x.getAddress());
                } else {
                    tmp.setAnalysisPointAddress("未知");
                }

                if (i < petSites.size()) {
                    TjCompetitorSiteVo pet = petSites.get(i);
                    tmp.setPetName(pet.getCompetitorName())
                        .setPetSiteName(pet.getName())
                        .setPetSiteAddress(pet.getAddress())
                        .setPetSitePower(pet.getPower());

                    tmp.setPetSiteDeviceInfo(
                        "直流：" + (pet.getDcEvseNum() == null ? "--"
                            : pet.getDcEvseNum())
                            + "/" + (pet.getDcPlugNum() == null ? "--"
                            : pet.getDcPlugNum())
                            + System.lineSeparator()
                            + "交流：" + (pet.getAcEvseNum() == null ? "--"
                            : pet.getAcEvseNum())
                            + "/" + (pet.getAcPlugNum() == null ? "--"
                            : pet.getAcPlugNum()));
                }

                if (i < surveySites.size()) {
                    TjSurveySimpleVo survey = surveySites.get(i);
                    tmp.setSurveySiteName(survey.getSiteName())
                        .setSurveySiteAddress(survey.getAddress());
                }

                if (i < operateSites.size()) {
                    SiteWithinTjVo operate = operateSites.get(i);
                    tmp.setOperateSiteName(operate.getName())
                        .setOperateSiteAddress(operate.getAddress());

                    tmp.setOperateSiteDeviceInfo(
                        "直流：" + (operate.getDcEvseNum() == null ? "--"
                            : operate.getDcEvseNum())
                            + "/" + (operate.getDcPlugNum() == null ? "--"
                            : operate.getDcPlugNum())
                            + System.lineSeparator()
                            + "交流：" + (operate.getAcEvseNum() == null ? "--"
                            : operate.getAcEvseNum())
                            + "/" + (operate.getAcPlugNum() == null ? "--"
                            : operate.getAcPlugNum()));

                    Integer dcPower = operate.getDcPower();
                    Integer acPower = operate.getAcPower();
                    tmp.setOperateSitePower(
                        new BigDecimal(dcPower == null ? 0 : dcPower)
                            .add(new BigDecimal(acPower == null ? 0 : acPower)));
                }

                result.add(tmp);
            }
        }

        return result;
    }
}
