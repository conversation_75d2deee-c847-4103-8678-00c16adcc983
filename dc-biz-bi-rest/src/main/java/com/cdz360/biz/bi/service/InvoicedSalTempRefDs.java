package com.cdz360.biz.bi.service;

import com.cdz360.biz.bi.mapper.InvoicedSalTempRefMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * InvoicedSalTempRefDs
 *  开票商品行模板
 * @since 5/31/2023 2:33 PM
 * <AUTHOR>
 */
@Slf4j
@Service
public class InvoicedSalTempRefDs {
    @Autowired
    private InvoicedSalTempRefMapper mapper;

    public String getNameById(Long id) {
        return mapper.getNameById(id);
    }

}