package com.cdz360.biz.bi.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.type.OrderType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.bi.service.site.SiteOrderBiService;
import com.cdz360.biz.model.bi.dashboard.ChargeOrderCommBiVo;
import com.cdz360.biz.model.bi.dashboard.CommStatisticBiVo;
import com.cdz360.biz.model.bi.dashboard.SiteUtilizationTopVo;
import com.cdz360.biz.model.bi.dashboard.SubCommStatisticBiVo;
import com.cdz360.biz.model.bi.type.OrderByType;
import com.cdz360.biz.ess.model.data.param.DataBiParam;
import com.chargerlinkcar.framework.common.domain.type.DashboardOrderType;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;


/**
 * BiDashboardRest
 *
 * @since 6/23/2020 9:46 AM
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/biDashboard")
public class BiDashboardRest {

    @Autowired
    private SiteOrderBiService siteOrderBiService;

    @GetMapping("/getLastBi")
    @Operation( summary = "数据统计和快速入口-近30天充电订单统计")
    public ObjectResponse<ChargeOrderCommBiVo> getLastBi(@RequestParam(value = "days", required = false) Integer days,
                                                         @RequestParam("commId") Long commId,
                                                         @RequestParam("idChain") String idChain) {
        log.debug("数据统计和快速入口-近30天充电订单统计: {}, {}, {}", days, commId, idChain);
        return new ObjectResponse(siteOrderBiService.getLastBi(days, commId, idChain));
    }

    @Operation( summary = "充电数据采集")
    @PostMapping(value = "/chargeDataSample")
    public Mono<ListResponse<CommStatisticBiVo>> chargeDataSample(@RequestBody DataBiParam param) {
        log.info("充电数据采集: param = {}", JsonUtils.toJsonString(param));
        return siteOrderBiService.chargeDataSample(param)
                .map(RestUtils::buildListResponse);
    }

    @GetMapping("/getLastCommBi")
    @Operation( summary = "数据统计和快速入口-近 30 天营收数据")
    public ListResponse<CommStatisticBiVo> getLastCommBi(@RequestParam(value = "days", required = false) Integer days,
                                                         @RequestParam(value = "commId", required = false) Long commId) {
        log.debug("数据统计和快速入口-近 30 天营收数据: {}, {}", days, commId);
        return new ListResponse(siteOrderBiService.getLastCommBi(days, commId));
    }

    @GetMapping("/getLastCommTopBi")
    @Operation( summary = "数据统计和快速入口-近 30 天营收数据对比")
    public ListResponse<SubCommStatisticBiVo> getLastCommTopBi(@RequestParam(value = "days", required = false) Integer days,
                                                               @RequestParam("commId") Long commId,
//                                                               @RequestParam("idChain") String idChain,
                                                               @RequestParam("sort") DashboardOrderType orderBy) {
        log.debug("数据统计和快速入口-近 30 天营收数据对比: days-{}, commId-{}", days, commId);
        return new ListResponse(siteOrderBiService.getLastCommTopBi(days, commId, /*idChain, */orderBy));
    }

    @GetMapping("/getUsageRateBoard")
    @Operation( summary = "昨日充电站利用率排名")
    public ListResponse<SiteUtilizationTopVo> getUsageRateBoard(@RequestParam("size") Integer size,
                                                                @RequestParam(value = "commId") Long commId,
                                                                @RequestParam("orderByType") OrderByType orderByType,
                                                                @RequestParam("orderBy") OrderType orderBy) {
        log.debug("昨日充电站利用率排名: size-{}, commId-{}, orderBy-{}", size, commId, orderBy);
        return new ListResponse(siteOrderBiService.getUsageRateBoard(size, commId, orderByType, orderBy));
    }
}