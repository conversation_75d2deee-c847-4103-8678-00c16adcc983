package com.cdz360.biz.bi.service.oss;

import java.time.LocalDate;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class CommEssDataService {

    @Autowired
    private OssArchiveService essOssArchiveService;

    public Mono<Optional<String>> getOSSPcsData(
        String siteId, String dno, LocalDate date) {
        return essOssArchiveService.getOSSDeviceRtData(
                siteId, dno, 0L, "PCS", date)
            .filter(Optional::isPresent);
    }

    public Mono<Optional<String>> getOSSPcsInfo(
        String siteId, String dno, LocalDate date) {
        return essOssArchiveService.getOSSDeviceRtInfo(
                siteId, dno, 0L, "PCS", date)
            .filter(Optional::isPresent);
    }

    public Mono<Optional<String>> getOSSBmsData(
        String siteId, String dno, LocalDate date) {
        return essOssArchiveService.getOSSDeviceRtData(
                siteId, dno, 0L, "BMS", date)
            .filter(Optional::isPresent);
    }

    public Mono<Optional<String>> getOSSBmsInfo(
        String siteId, String dno, LocalDate date) {
        return essOssArchiveService.getOSSDeviceRtInfo(
                siteId, dno, 0L, "BMS", date)
            .filter(Optional::isPresent);
    }

    public Mono<Optional<String>> getOSSMeterData(
        String siteId, String dno, LocalDate date) {
        return essOssArchiveService.getOSSDeviceRtData(
                siteId, dno, 0L, "METER", date)
            .filter(Optional::isPresent);
    }

    public Mono<Optional<String>> getOSSMeterInfo(
        String siteId, String dno, LocalDate date) {
        return essOssArchiveService.getOSSDeviceRtInfo(
                siteId, dno, 0L, "METER", date)
            .filter(Optional::isPresent);
    }

//    private static <T> LineEssRtData<T> lineData(String line, Class<T> tClass) {
//        String[] split = line.split(" \\| ");
//        if (split.length == 2) {
//            LineEssRtData<T> rtData = new LineEssRtData<T>();
////            rtData.setTime(LocalDateTime.parse(split[0], DateUtils.fmtYyyyMmDdHhMmSsLdt));
//
//            if (String.class.equals(tClass)) {
//                rtData.setRtData((T) split[1]);
//            } else {
//                rtData.setRtData(JsonUtils.fromJson(split[1], tClass));
//            }
//            return rtData;
//        }
//
//        return null;
//    }

}
