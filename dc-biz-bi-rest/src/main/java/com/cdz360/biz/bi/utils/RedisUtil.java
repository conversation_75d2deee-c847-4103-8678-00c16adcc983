package com.cdz360.biz.bi.utils;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.trading.order.vo.ChargeOrderCache;
import com.cdz360.data.cache.RedisChargeOrderReadService;
import com.cdz360.data.cache.utils.RedisKeyGenerator;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Slf4j
@Repository
public class RedisUtil {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private RedisChargeOrderReadService redisChargeOrderReadService;
    @Autowired
    private RedisTemplate redisTemplate;

    public List<ChargeOrderCache> getOrderVoList(List<String> orderNos) {
        List<ChargeOrderCache> result = new ArrayList<>();
        orderNos = orderNos.stream().map(RedisKeyGenerator::genChargerOrderInfoKey)
            .collect(
                Collectors.toList());
        List list = redisTemplate.opsForValue().multiGet(orderNos);
        if (CollectionUtils.isEmpty(list)) {
            return result;
        }
        for (Object item : list) {
            LinkedHashMap map = (LinkedHashMap) item;
            if (map == null) {
                continue;
            }
            result.add(JsonUtils.fromJson(JsonUtils.toJsonString(map), ChargeOrderCache.class));
        }
        return result;
    }
}
