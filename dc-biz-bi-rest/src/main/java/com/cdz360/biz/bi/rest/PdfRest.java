package com.cdz360.biz.bi.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.bi.service.PdfService;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.itextpdf.text.DocumentException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
@RestController
public class PdfRest {

    @Autowired
    private PdfService pdfService;

    @RequestMapping(value = "/bi/pdf/checkFileCompeleted")
    public ObjectResponse<Boolean> checkFileCompeleted(
        @RequestParam(value = "subDir") String subDir,
        @RequestParam(value = "subFileName") String subFileName) {
        log.info("检查文件是否存在subDir:{}, subFileName:{}", subDir, subFileName);
        if (StringUtils.isEmpty(subDir) || StringUtils.isEmpty(subFileName)) {
            throw new DcArgumentException("参数错误");
        }
        boolean exist = pdfService.existsDownFile(subDir, subFileName);

        return RestUtils.buildObjectResponse(exist);
    }

    //下载pdf文件
    @RequestMapping(value = "/bi/pdf/download", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON_UTF8_VALUE)
    public void download(String subDir, String subFileName, HttpServletResponse response)
        throws IOException {
        log.info("down文件subDir:{}, subFileName:{}", subDir, subFileName);
        if (StringUtils.isEmpty(subDir)) {
            throw new DcArgumentException("参数错误");
        }
        pdfService.downFile(subDir, subFileName, response);
    }


    /**
     * 导出巡检详情
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/bi/pdf/exportSiteInspection")
    public ObjectResponse<ExcelPosition> exportSiteInspection(@RequestParam("id") Long id)
        throws IOException, DocumentException {
        log.info("recordExport id: {}", id);
        throw new DcServiceException("接口已废弃，请联系开发");
//        return pdfService.exportSiteInspection(id);
    }

    @Operation(summary = "导出运维工单详情")
    @GetMapping(value = "/bi/pdf/exportYwOrder")
    public Mono<ObjectResponse<ExcelPosition>> exportYwOrder(
        @Parameter(name = "运维工单编号", required = true)
        @RequestParam("ywOrderNo") String ywOrderNo) {
        log.info("导出运维工单详情PDF: ywOrderNo = {}", ywOrderNo);
        throw new DcServiceException("接口已废弃，请联系开发");
//        return Mono.just(ywOrderNo)
//                .map(no -> new ExcelPosition()
//                            .setSubFileName(UUIDUtils.getUuid32())
//                            .setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date())))
//                .doOnNext(pos -> pdfService.exportYwOrder(ywOrderNo, pos))
//                .map(RestUtils::buildObjectResponse);
    }
}
