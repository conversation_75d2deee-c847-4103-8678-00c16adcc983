package com.cdz360.biz.bi.domain.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 *  订单尖峰平谷导出VO
 * @since 2016年9月18日 下午2:36:25
 */
@Data
public class ChargerOrderDetailVO implements Serializable {

    /****/
    @ExcelField(title = "    ", sort = 1)
    private String tag;
    /**
     * 订单总数(条)
     **/
    @ExcelField(title = "订单总数(条)", sort = 2, digits = 0)
    private BigDecimal chargerOrderNumber;

    /**
     * 订单总金额(元)
     **/
    @ExcelField(title = "订单总金额(元)", sort = 3)
    private BigDecimal orderPriceAmount;

    /**
     * 总电费(元)
     **/
    @ExcelField(title = "总电费(元)", sort = 4)
    private BigDecimal elecPriceAmount;

    /**
     * 总服务费(元)
     **/
    @ExcelField(title = "总服务费(元)", sort = 5)
    private BigDecimal servicePriceAmount;

    /**
     * 订单总电量(kW·h)
     **/
    @ExcelField(title = "订单总电量(kW·h)", sort = 6, digits = 4)
    private BigDecimal orderElectricityAmount;

}
