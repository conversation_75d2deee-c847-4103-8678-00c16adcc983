package com.cdz360.biz.bi.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.bi.service.InvoiceService;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.invoice.param.ListCorpInvoiceRecordParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.ListInvoicedRecordParam;
import com.chargerlinkcar.framework.common.utils.UUIDUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.text.SimpleDateFormat;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "发票excel导出涉及接口")
@Slf4j
@RestController
public class InvoiceRest {

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMdd");
//    private static final String INVOICE_FLAG = "invoice";

    @Autowired
    private InvoiceService invoiceService;

    private static String excelSubDir() {
        return DATE_FORMAT.format(new Date());
    }

    @Deprecated(since = "20230328")
    @Operation(summary = "测试导出NSR模板数据")
    @PostMapping(value = "/bi/invoice/exportCusInvoice")
    public Mono<ObjectResponse<ExcelPosition>> exportCusInvoice(
        @RequestBody ListInvoicedRecordParam param) {
        log.debug("测试导出NSR模板数据: {}", param);
        ExcelPosition position = new ExcelPosition();
        position.setSubDir(excelSubDir())
            .setSubFileName(UUIDUtils.getUuid32());
        try {
            invoiceService.exportCusInvoiceNSR(position, param);
        } catch (Exception e) {
            // nothing to do
            log.error("调用异常: {}", e.getMessage(), e);
        }
        return Mono.just(position)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "企业开票涉及的订单导出")
    @PostMapping(value = "/bi/invoice/exportCorpInvoiceOrder")
    public Mono<ObjectResponse<ExcelPosition>> exportCorpInvoiceOrder(
        @Parameter(name = "申请单号", required = true) @RequestParam("applyNo") String applyNo) {
        log.debug("企业开票涉及的订单导出: applyNo = {}", applyNo);
        throw new DcServiceException("接口已废弃，请联系开发");
//        // excel生成位置信息
//        ExcelPosition position = new ExcelPosition();
//        position.setSubDir(excelSubDir())
//                .setSubFileName(UUIDUtils.getUuid32());
//
//        // 异步调用
//        invoiceService.exportCorpInvoiceOrder(position, applyNo);
//        return Mono.just(position)
//                .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "C端用户开票记录导出")
    @PostMapping(value = "/bi/invoice/exportInvoicedRecord")
    public Mono<ObjectResponse<ExcelPosition>> exportInvoicedRecord(
        @RequestBody ListInvoicedRecordParam param) {
        log.debug("C端用户开票记录导出: param = {}", JsonUtils.toJsonString(param));
        throw new DcServiceException("接口已废弃，请联系开发");
//        // excel生成位置信息
//        ExcelPosition position = new ExcelPosition();
//        position.setSubDir(excelSubDir())
//                .setSubFileName(UUIDUtils.getUuid32());
//
//        invoiceService.exportInvoicedRecord(position, param);
//        return Mono.just(position)
//                .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "企业开票申请导出")
    @PostMapping(value = "/bi/invoice/exportCorpInvoiceRecord")
    public Mono<ObjectResponse<ExcelPosition>> exportCorpInvoiceRecord(
        @RequestBody ListCorpInvoiceRecordParam param) {
        log.debug("企业开票申请导出: param = {}", JsonUtils.toJsonString(param));
        throw new DcServiceException("接口已废弃，请联系开发");

//        // excel生成位置信息
//        ExcelPosition position = new ExcelPosition();
//        position.setSubDir(excelSubDir())
//                .setSubFileName(UUIDUtils.getUuid32());
//
//        invoiceService.exportCorpInvoiceRecord(position, param);
//        return Mono.just(position)
//                .map(RestUtils::buildObjectResponse);
    }
}
