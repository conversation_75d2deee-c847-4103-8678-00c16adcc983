package com.cdz360.biz.bi.domain;

/**
 * <AUTHOR>
 *  订单基础信息表
 * @since 2018/11/22 11:29
 */
//@Data
//public class ChargerOrder implements Serializable{
//
//    private static final long serialVersionUID = 1L;
//
//    /**
//     * 公有云订单编号
//     */
//    private Long orderId;
//
//    private Long topCommId;
//    /**
//     * 商户ID
//     */
//    //private Long commercialId;
//    /**
//     * 站点ID
//     */
//    private String stationId;
//
//    /**
//     * 枪头好"序列号-枪头序号"
//     */
//    private String bcCode;
//    /**
//     * 站点ID
//     */
//    private String stationIdStr;
//
//    /**
//     * 互联互通订单号
//     */
//    private String openOrderId;
//
//    /**
//     * 用户ID
//     */
//    private Long customerId;
//
//    /**
//     * 设备运营商名称
//     */
//    private String commercialName;
//
//    /**
//     * 客户运营商ID
//     */
//    //private Long customerCommercialId;
//
//    /**
//     * 客户运营商名称
//     */
//    private String customerCommercialName;
//
//    /**
//     * 充电卡号/身份唯一识别号
//     */
//    private String cardNo;
//
//    /**
//     * 订单来源类型,0:微信渠道;1:APP在线发起充电;2:APP蓝牙发起充电:3:刷卡充电:4:桩上报离线数据5定时充电6手动开启7曹操专车开启
//     */
//    private Integer channelId;
//
//    /**
//     * 订单类型，0:未知，1：平台，2：互联互通，3：第三方，301：曹操专车
//     */
//    private Integer orderType;
//
//    /**
//     * 订单金额
//     */
//    private Integer orderPrice;
//
//    /**
//     * 服务费
//     */
//    private Integer servicePrice;
//
//    /**
//     * 电费
//     */
//    private Integer elecPrice;
//
//    /**
//     * 人工调整后金额
//     */
//    private Integer manualPrice;
//
//    /**
//     * 客户实付金额
//     */
//    private Integer actualPrice;
//
////    /**
////     * 结算方式，0:未知，1：后付费，2：预付费，3：先付费（固定收费：不退还费用），4：先付费（实时计费：退还费用）
////     */
////    private Integer clearingMode;
//
//    /**
//     * 订单电量
//     */
//    private Integer orderElectricity;
//
//    /**
//     * 状态,-500：硬件故障；-40：上报超时；-35：断连超时；-30关电超时；
//     * -29：结束充电失败；-20：链接超时；-10：打开电闸超时；-7：开启充电失败，PIN可能错误；-5:系统繁忙；
//     * 0：订单未激活；100：电闸打开；200：充电中；250：关电闸，充电停止；
//     * 300：充电完成；800：订单结束；1000：用户已支付；2000：钱已到账
//     */
//    private Integer status;
//
////    /**
////     * 星标标志，默认0，0：非星标；1：星标；
////     */
////    private Integer starFlag;
//
//    /**
//     * 计费模板ID
//     */
//    private Long priceSchemeId;
//
//    /**
//     * 充电车位ID
//     */
////    private Long carportId;
//
//
//    /**
//     * 车位名称
//     */
////    private String carportName;
//
//    /**
//     * 备注
//     */
//    private String remark;
//
//    /**
//     * 订单创建时间
//     */
//    private Date createTime;
//
//    /**
//     * 订单结束时间
//     */
//    private Date stopTime;
//
//    /**
//     * 订单修改时间
//     */
//    private Date updateTime;
//
//    /**
//     * CORE订单ID
//     */
////    private Long coreOrderId;
//
//    /**
//     * 硬件流水号
//     */
//    private String tradeNo;
//
//    /**
//     * 厂商盒子类型
//     */
//    private Integer boxType;
//
//    /**
//     * 显示字段 内容：出厂编号+evseId+connectorId
//     */
//    private String showId;
//
//    /**
//     * 充电接口(枪)编号
//     */
//    private String connectId;
//
//    /**
//     * 充电开始时间
//     */
//    private Integer chargeStartTime;
//
//    /**
//     * 充电结束时间
//     */
//    private Integer chargeEndTime;
//
//    /**
//     * 充电开始电量
//     */
//    private Integer startElectricity;
//
//    /**
//     * 充电结束电量
//     */
//    private Integer endElectricity;
//
//    /**
//     * 订单来源类型,0:微信渠道;1:APP在线发起充电;2:APP蓝牙发起充电:3:刷卡充电:4:桩上报离线数据
//     */
//    private Integer type;
//
//    /**
//     * 充电二维码后四位更换1位蓝牙位，1位网络位，2位pin码
//
//     */
////    private String pin;
//
//    /**
//     * platformId区分app或者微信或者其他平台发起的充电的回传标示
//     */
//    private String platformId;
//
////    /**
////     * 充电记录
////     */
////    private String statusChangeLog;
////    /**
////     * 人工修改充电记录
////     */
////    private String statusChangeLogManual;
//
//    /**
//     * 站点名称
//     */
//    private String stationName;
//    /**
//     * 手机号码
//     */
//    private String mobilePhone;
//
//    /**
//     * 支付方式
//     */
//    private Integer payModes;
//
//    /**
//     * 支付状态 1：待支付；2：已支付；3：支付失败
//     */
//    private Integer payStatus;
//
//    /**
//     * 客户名称
//     */
//    private String customerName;
//
//
//
//    /**
//     * 互联互通运营商ID
//     */
//    private String openOperatorId;
//
//    /**
//     * 预付金额
//     */
//    private Integer prepayAmount;
//
//    /**
//     * 是否桩端计费  0否，1是
//     */
//    private Integer calcPriceByCharger;
//
//    /**
//     * 向客户运营商(客户)收取的金额
//     */
//    private Integer customerPrice;
//
//    /**
//     * 设备运营商ID
//     */
//    private Long deviceCommercialId;
//
//    /**
//     * 预充电时长 单位秒
//     */
//    private Integer preDuration;
//
//    /**
//     * core推送发起充电超时，通知第三方结束订单标志 0:正常 其他异常
//     */
//    private Integer timeoutStatus;
//
//    /**
//     * 物业子账号id
//     */
////    private Long merchantId;
//
//    /**
//     * 本金
//     */
//    private Integer principalAmount;
//
//    /**
//     * 赠送金
//     */
//    private Integer freeGoldAmount;
//
//    /**
//     * 盒子编码
//     */
//    private String boxCode;
//
//    /**
//     * 盒子出厂编码
//     */
////    private String boxOutFactoryCode;
//
//    /**
//     * 充电时长
//     */
//    private String duration;
//
//    /**
//     * 活动Id
//     */
//    private Long activityId;
//
//    /**
//     * 客户端版本号
//     */
//    private String version;
//
//    /**
//     * 实时功率
//     */
//    private String rtPower;
//
//    /**
//     * 客户实际消费功率与客户选择的功率对比 0：相等 1：不等
//     */
//    private String templatePowerIsChanged;
//
//    /**
//     * 订单异常结束原因
//     */
//    private String exceptionReason;
//
//    /**
//     * 插座二维码
//     */
//    private String qrCode;
//
//    /**
//     * 插座序列号
//     */
//    private String connectorId;
//
//
//    /**
//     * 上报订单的设备类型
//     */
////    private String orderDiviceType;
//
//    /**
//     * 充电是否提前结束标示
//     */
//    private Integer endUnexpected;
//
//    /**
//     * 实际补贴金额(单位：分)
//     */
//    private Integer actualSubsidyAmount;
//
//    /**
//     * 补贴是否分润(0:不参与分润  1:参与分润 )
//     */
//    private Integer subsidyShareProfitStatus;
//
//    /**
//     * 补贴类型(1:固定补贴 )
//     */
//    private Integer subsidyType;
//
//    /**
//     * 补贴时长(单位：分)
//     */
//    private String subsidyDuration;
//
//    /**
//     * 车辆Vin码
//     */
//    private String vin;
//
//    /**
//     * 开始充点前电量百分比
//     */
//    private String startSoc;
//
//    /**
//     * 结束充电后电量百分比
//     */
//    private String stopSoc;
//
//    /**
//     * 结束原因
//     */
//    private String stopReason;
//
//    /**
//     * 当前电量百分比
//     */
//    private String currentSoc;
//
//    /**
//     * 单次充电优惠金额--等级优惠金额(单位分)
//     */
//    private Long discountMoney;
//
//    /**
//     * 优惠券金额（单位分）
//     */
//    private Long couponMoney;
//
//    /**
//     * 当前数据的记录创建时间
//     */
//    private Date createDateTime;
//
////    private String setOrderPriceRemark;
//
//    /**
//     * 支付时间
//     */
//    private String payTime;
//
//    /**
//     * 枪头名称
//     */
//    private String chargerName;
//
//
////    public ChargerOrder() {
////    }
////
////    public ChargerOrder(Long orderId, String cardNo, Integer channelId, String pin, String platformId, String openOrderId) {
////        this.orderId = orderId;
////        this.cardNo = cardNo;
////        this.channelId = channelId;
////        this.pin = pin;
////        this.platformId = platformId;
////        this.openOrderId = openOrderId;
////    }
//
////    /**
////     * @param statusDateLong
////     * @param remark
////     *  设置订单状态日志
////     * @showdocUrl:
////     */
////    public void setChargerStatusChangeLog(JSONObject remark, Long statusDateLong) {
////        List<Object> listObj = new ArrayList<>();
////        String statusChargerLog = this.getStatusChangeLog();
////        JSONObject jsonObject = new JSONObject();
////        jsonObject.put("time", statusDateLong);
////
////        if (remark != null) {
////            jsonObject.put("remark", remark);
////            //停止充电不添加状态
////            if (remark.get("orderStatus") != "stop") {
////                jsonObject.put("status", this.getStatus());
////            }
////        } else {
////            jsonObject.put("status", this.getStatus());
////        }
////
////
////        if (statusChargerLog == null) {
////            listObj.add(jsonObject);
////        } else {
////            listObj = (List<Object>) JSONObject.parse(statusChargerLog);// 将日志转化成 list 集合
////            listObj.add(jsonObject);
////        }
////        setStatusChangeLog(JsonUtils.toJsonString(listObj));
////    }
//}
