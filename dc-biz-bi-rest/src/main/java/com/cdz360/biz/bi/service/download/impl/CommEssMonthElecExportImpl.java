package com.cdz360.biz.bi.service.download.impl;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.bi.feign.DeviceMgmFeignClient;
import com.cdz360.biz.bi.service.download.IFileExport;
import com.cdz360.biz.bi.service.ess.EssBiService;
import com.cdz360.biz.ess.model.dto.EssEquipDailyElecBiDto;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.ess.param.ListEssDailyParam;
import com.cdz360.biz.model.ess.vo.EssEquipVo;
import com.cdz360.biz.model.iot.param.ListCtrlParam;
import com.chargerlinkcar.framework.common.utils.ExcelUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class CommEssMonthElecExportImpl extends
    AbstractFileExport<ListEssDailyParam, ExcelPosition>
    implements IFileExport<ListEssDailyParam, ExcelPosition> {

    @Autowired
    private EssBiService essBiService;

    @Autowired
    private DeviceMgmFeignClient deviceMgmFeignClient;

    @PostConstruct
    public void init() {
        downloadFileProxy.addProxy(DownloadFunctionType.ESS_MONTH_ELEC, this);
    }

    @Override
    public Class<ListEssDailyParam> paramClazz() {
        return ListEssDailyParam.class;
    }

    @Override
    public void genFile(String context, String pos) throws IOException {
        ListEssDailyParam timelyParam = this.convert(context);
        timelyParam.setTotal(false);

        List<EssEquipDailyElecBiDto> data = essBiService.getMonthlyElecBiList(timelyParam);

        ListResponse<EssEquipVo> equipListRes = deviceMgmFeignClient.findEquipList(
            new ListCtrlParam().setSiteIdList(timelyParam.getSiteIdList())
                .setDnoList(timelyParam.getDnos()));
        FeignResponseValidate.check(equipListRes);

        EssEquipVo equip = CollectionUtils.isNotEmpty(equipListRes.getData()) ?
            equipListRes.getData().get(0) : null;

        List<String> inLabels = List.of();
        List<String> outLabels = List.of();
        if (CollectionUtils.isNotEmpty(data)) {
            if (CollectionUtils.isNotEmpty(data.get(0).getInVals())) {
                inLabels = data.get(0).getInVals().stream()
                    .map(x -> List.of(x.getK() + "电量", x.getK() + "金额"))
                    .flatMap(List::stream).distinct()
                    .collect(Collectors.toList());
                inLabels.addAll(List.of("总电量", "总金额"));
            }
            if (CollectionUtils.isNotEmpty(data.get(0).getOutVals())) {
                outLabels = data.get(0).getOutVals().stream()
                    .map(x -> List.of(x.getK() + "电量", x.getK() + "金额"))
                    .flatMap(List::stream).distinct()
                    .collect(Collectors.toList());
                outLabels.addAll(List.of("总电量", "总金额"));
            }
        }

        ExcelUtil excel = ExcelUtil.builder(exportFileConfig.getExcelDir(), this.convertPos(pos),
            "每月汇总").essBiHeader(inLabels, outLabels);

        String equipName = equip != null ? equip.getName() : null;
        data.forEach(x -> {
            ArrayList<Object> valList = new ArrayList<>();
            valList.add(equipName);
            valList.add(x.getDate());

            BigDecimal todayInKwh = null;
            if (CollectionUtils.isNotEmpty(x.getInVals())) {
                x.getInVals().forEach(in -> {
                    valList.add(in.getKwh());
                    valList.add(in.getFee());
                });
                valList.add(x.getTodayInKwh());
                valList.add(x.getTodayInFee());
                
                todayInKwh = x.getTodayInKwh();
            }

            BigDecimal todayOutKwh = null;
            if (CollectionUtils.isNotEmpty(x.getOutVals())) {
                x.getOutVals().forEach(out -> {
                    valList.add(out.getKwh());
                    valList.add(out.getFee());
                });
                valList.add(x.getTodayOutKwh());
                valList.add(x.getTodayOutFee());

                todayOutKwh = x.getTodayOutKwh();
            }

            if (null != todayInKwh && null != todayOutKwh) {
                valList.add(todayOutKwh.divide(todayInKwh, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal(100))); // 效率计算: 反向总电量 / 正向总电量 x 100, 取2位小数
            }

            valList.add(x.getTotalInKwh());
            valList.add(x.getTotalOutKwh());
            excel.appendData(valList);
        });
        excel.write2File();
    }
}
