package com.cdz360.biz.bi.service.site;

import com.cdz360.biz.bi.service.site.SiteAccountProfitBiService;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.model.cus.settlement.vo.SettlementVo;
import com.cdz360.biz.model.settle.type.SettAccountType;
import com.cdz360.biz.model.trading.bi.dto.SiteAccountProfitDto;
import com.cdz360.biz.model.trading.bi.dto.SiteProfitDto;
import com.cdz360.biz.model.trading.bi.param.AccountSiteIncomeParam;
import com.cdz360.biz.model.trading.site.po.SitePo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * 计算场站运营收入
 */
@Slf4j
@Service
public class SiteProfitBiService {

    @Autowired
    private SiteAccountProfitBiService siteAccountProfitBiService;

    @Autowired
    private SiteRoDs siteRoDs;

    public SiteProfitDto accountSiteIncome(AccountSiteIncomeParam param) {
        SitePo site = siteRoDs.getSite(param.getSiteId());
        int year = param.getYear();
        int month = param.getMonth();
        SiteProfitDto result = new SiteProfitDto();
        result.setSiteId(param.getSiteId());
        result.setMonth(LocalDate.of(year, month, 1));
        // 平台余额
        List<SiteAccountProfitDto> personal = siteAccountProfitBiService.getSiteAccountProfit(site, year, month, SettAccountType.PERSONAL, null, null);
        result.getAccProfits().addAll(personal);

        // 即充即退
        List<SiteAccountProfitDto> prepay = siteAccountProfitBiService.getSiteAccountProfit(site, year, month, SettAccountType.PREPAY, null, null);
        result.getAccProfits().addAll(prepay);

        // 未结算
        List<SiteAccountProfitDto> noPay = siteAccountProfitBiService.getSiteAccountProfit(site, year, month, SettAccountType.UNKNOWN, null, null);
        result.getAccProfits().addAll(noPay);

        // 商户会员
        List<SiteAccountProfitDto> commPay = siteAccountProfitBiService.getSiteAccountProfit(site, year, month, SettAccountType.COMMERCIAL, null, null);
        result.getAccProfits().addAll(commPay);

        // 互联互通
        List<SiteAccountProfitDto> hlht = siteAccountProfitBiService.getSiteAccountProfit(site, year, month, SettAccountType.HLHT_CORP, null, null);
        result.getAccProfits().addAll(hlht);

        // 企业客户
        List<SiteAccountProfitDto> corp = siteAccountProfitBiService.getSiteAccountProfit(site, year, month, SettAccountType.NORMAL_CORP, null, null);
        result.getAccProfits().addAll(corp);

        return result;
    }

    public List<SiteAccountProfitDto> accountSitePostPayCorpIncome(SettlementVo sett) {
        return siteAccountProfitBiService.getSitePostPayCorpIncome(sett);
    }
}
