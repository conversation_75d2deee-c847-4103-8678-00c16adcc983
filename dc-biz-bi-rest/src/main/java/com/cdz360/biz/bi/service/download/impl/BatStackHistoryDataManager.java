package com.cdz360.biz.bi.service.download.impl;

import static com.cdz360.biz.bi.service.download.impl.AbstractFileExport.FORMAT_yyyy_MM_dd_HH_mm_ss;

import com.cdz360.base.model.es.vo.BmsRtData;
import com.cdz360.base.model.es.vo.BmsRtInfo;
import com.cdz360.base.model.es.vo.BmsStackRtData;
import com.cdz360.base.model.es.vo.BmsStackRtInfo;
import com.cdz360.base.model.es.vo.EssBaseVal;
import com.cdz360.base.model.es.vo.RegisterRwValue;
import com.cdz360.base.model.es.vo.SignalVal;
import com.cdz360.base.model.es.vo.StringVal;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.chargerlinkcar.framework.common.utils.ExcelUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.File;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.LineIterator;
import org.apache.commons.io.input.ReversedLinesFileReader;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BatStackHistoryDataManager {

    private List<List<String>> extractHeaderList(BmsStackRtInfo data) {
        List<List<String>> result = new ArrayList<>();
        List<StringVal> node1 = data.getTexts();
        if (null != node1) {
            List<String> tmpList = new ArrayList<>();
            node1.forEach(kv -> tmpList.add(kv.getName()));
            result.add(tmpList);
        }

        List<SignalVal> node2 = data.getSignals();
        if (null != node2) {
            List<String> tmpList = new ArrayList<>();
            node2.forEach(kv -> tmpList.add(kv.getName()));
            result.add(tmpList);
        }

        List<RegisterRwValue> node3 = data.getCfgs();
        if (null != node3) {
            List<String> tmpList = new ArrayList<>();
            node3.forEach(kv -> tmpList.add(kv.getName()));
            result.add(tmpList);
        }

        return result;
    }

    public void batteryStackStatusExcelData(final String tz, final String localFile,
        LineIterator lineIterator, String dno, ExcelUtil excel) {
        // 请求头信息
        List<String> headerList = new ArrayList<>();
        final List<List<String>> nodeHeaderList = new ArrayList<>();
        try (ReversedLinesFileReader reader = new ReversedLinesFileReader(
            new File(localFile), Charset.defaultCharset())) {
            String line = reader.readLine();
            while (null != line) {
                JsonNode node = JsonUtils.fromJson(OssLineSplit.validSplit(line));
                if (null == node) {
                    line = reader.readLine();
                    continue;
                }

                JsonNode jsonNode = node.get("rtInfo");
                if (null == jsonNode) {
                    line = reader.readLine();
                    continue;
                }

                BmsRtInfo bms = JsonUtils.fromJson(jsonNode,
                    new TypeReference<BmsRtInfo>() {
                    });
                if (null == bms) {
                    line = reader.readLine();
                    continue;
                }

                Optional<BmsStackRtInfo> clusterRtData = bms.getStackInfoList().stream()
                    .filter(x -> dno.equals(x.getDno())).findFirst();
                if (clusterRtData.isPresent()) {
                    BmsStackRtInfo data = clusterRtData.get();

                    if (CollectionUtils.isNotEmpty(data.getTexts()) ||
                        CollectionUtils.isNotEmpty(data.getSignals()) ||
                        CollectionUtils.isNotEmpty(data.getCfgs())) {
                        nodeHeaderList.addAll(extractHeaderList(data));
                        headerList.add("日期");
                        nodeHeaderList.forEach(headerList::addAll);
                        break;
                    }
                }
                line = reader.readLine();
            }
        } catch (Exception e) {
            log.error("打开文件失败: {}", e.getMessage(), e);
        }
        excel.addHeader(headerList);

        // 数据提取
        while (lineIterator.hasNext()) {
            JsonNode x = JsonUtils.fromJson(OssLineSplit.validSplit(lineIterator.nextLine()));
            if (x == null) {
                continue;
            }
            JsonNode jsonNode = x.get("rtInfo");
            if (null == jsonNode) {
                continue;
            }

            BmsRtInfo bms = JsonUtils.fromJson(jsonNode, new TypeReference<BmsRtInfo>() {
            });
            if (null == bms) {
                continue;
            }

            Optional<BmsStackRtInfo> clusterRtData = bms.getStackInfoList().stream()
                .filter(stack -> dno.equals(stack.getDno())).findFirst();
            if (clusterRtData.isPresent()) {
                BmsStackRtInfo data = clusterRtData.get();

                ArrayList<Object> valList = new ArrayList<>();
                long ts = bms.getTs();
                valList.add(LocalDateTime.ofEpochSecond(
                        ts, 0, ZoneOffset.of(tz))
                    .format(FORMAT_yyyy_MM_dd_HH_mm_ss));

                List<StringVal> node1 = data.getTexts();
                int idx = 0;
                if (null != node1) {
                    Map<String, StringVal> nameMap = node1.stream()
                        .collect(Collectors.toMap(StringVal::getName, o -> o));

                    nodeHeaderList.get(idx++)
                        .forEach(name -> valList.add(nameMap.containsKey(name) ?
                            nameMap.get(name).getV() : "--"));
                }

                List<SignalVal> node2 = data.getSignals();
                if (null != node2) {
                    Map<String, SignalVal> nameMap = node2.stream()
                        .collect(Collectors.toMap(SignalVal::getName, o -> o));

                    nodeHeaderList.get(idx++)
                        .forEach(name -> valList.add(nameMap.containsKey(name) ?
                            nameMap.get(name).getV() : "--"));
                }

                List<RegisterRwValue> node3 = data.getCfgs();
                if (null != node3) {
                    Map<String, RegisterRwValue> nameMap = node3.stream()
                        .collect(Collectors.toMap(RegisterRwValue::getName, o -> o));

                    nodeHeaderList.get(idx++)
                        .forEach(name -> valList.add(nameMap.containsKey(name) ?
                            nameMap.get(name).getV() : "--"));
                }

                excel.appendData(valList);
            }
        }
    }


    public void batteryStackExcelData(
        String localFile, LineIterator lineIterator, String dno, ExcelUtil excel) {
        // 请求头信息
        List<String> headerList = new ArrayList<>();
        List<String> sensorHeaders = new ArrayList<>();

        try (ReversedLinesFileReader reader = new ReversedLinesFileReader(
            new File(localFile), StandardCharsets.UTF_8)) {
            String line = reader.readLine();
            while (null != line) {
                BmsRtData bms = JsonUtils.fromJson(OssLineSplit.validSplit(line),
                    new TypeReference<BmsRtData>() {
                    });
                if (null == bms) {
                    line = reader.readLine();
                    continue;
                }

                Optional<BmsStackRtData> stackRtData = bms.getStackDataList().stream()
                    .filter(x -> dno.equals(x.getDno())).findFirst();
                if (stackRtData.isPresent()) {
                    BmsStackRtData data = stackRtData.get();
                    if (CollectionUtils.isNotEmpty(data.getSensors())) {
                        headerList.add("日期");

                        headerList.addAll(List.of("SOC", "SOH"));
                        headerList.addAll(
                            List.of(
                                "单体最高电压", "单体最高电压簇号", "单体最高电压电池号",
                                "单体最高电压电芯号",
                                "单体最低电压", "单体最低电压簇号", "单体最低电压电池号",
                                "单体最低电压电芯号",
                                "单体最高温度", "单体最高温度簇号", "单体最高温度电池号",
                                "单体最高温度电芯号",
                                "单体最低温度", "单体最低温度簇号", "单体最低温度电池号",
                                "单体最低温度电芯号"));

                        data.getSensors().forEach(kv -> sensorHeaders.add(kv.getName()));
                        headerList.addAll(sensorHeaders);
                        break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("打开文件失败: {}", e.getMessage(), e);
        }
        excel.addHeader(headerList);

        batteryStackExcelDataX(sensorHeaders, lineIterator, dno, excel);
    }

    private void batteryStackExcelDataX(
        List<String> headerList, LineIterator lineIterator, String dno, ExcelUtil excel) {
        while (lineIterator.hasNext()) {
            JsonNode jsonNode = JsonUtils.fromJson(
                OssLineSplit.validSplit(lineIterator.nextLine()));
            if (jsonNode == null) {
                continue;
            }
//        dataList.forEach(jsonNode -> {
            ArrayList<Object> valList = new ArrayList<>();
            BmsRtData bms = JsonUtils.fromJson(jsonNode,
                new TypeReference<BmsRtData>() {
                });

            if (null != bms) {
                Optional<BmsStackRtData> stackRtData = bms.getStackDataList().stream()
                    .filter(x -> dno.equals(x.getDno())).findFirst();
                if (stackRtData.isPresent()) {
                    BmsStackRtData data = stackRtData.get();

                    valList.add(null != jsonNode.get("ldt") ?
                        jsonNode.get("ldt").asText() : "");

                    Map<String, EssBaseVal<BigDecimal>> nameMap = data.getSensors().stream()
                        .collect(Collectors.toMap(EssBaseVal::getName, o -> o));

//                    SOC
                    if (null != data.getSoc()) {
                        valList.add(data.getSoc());
                    } else {
                        valList.add("--");
                    }

//                    SOH
                    if (null != data.getSoh()) {
                        valList.add(data.getSoh());
                    } else {
                        valList.add("--");
                    }

//                        单体最高电压	单体最高电压簇号	单体最高电压电池号 单体最高电压电芯号
                    if (null != data.getMaxBatteryVoltage()) {
                        valList.add(data.getMaxBatteryVoltage().getVal());
                        valList.add(data.getMaxBatteryVoltage().getBundleId());
                        valList.add(data.getMaxBatteryVoltage().getLmuId());
                        valList.add(data.getMaxBatteryVoltage().getInboxId());
                    } else {
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                    }
//                        单体最低电压	单体最低电压簇号	单体最低电压电池号	单体最低电压电芯号
                    if (null != data.getMinBatteryVoltage()) {
                        valList.add(data.getMinBatteryVoltage().getVal());
                        valList.add(data.getMinBatteryVoltage().getBundleId());
                        valList.add(data.getMinBatteryVoltage().getLmuId());
                        valList.add(data.getMinBatteryVoltage().getInboxId());
                    } else {
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                    }
//                        单体最高温度	单体最高温度簇号	单体最高温度电池号	单体最高温度电芯号
                    if (null != data.getMaxBatteryTemp()) {
                        valList.add(data.getMaxBatteryTemp().getVal());
                        valList.add(data.getMaxBatteryTemp().getBundleId());
                        valList.add(data.getMaxBatteryTemp().getLmuId());
                        valList.add(data.getMaxBatteryTemp().getInboxId());
                    } else {
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                    }
//                        单体最低温度	单体最低温度簇号	单体最低温度电池号	单体最低温度电芯号
                    if (null != data.getMinBatteryTemp()) {
                        valList.add(data.getMinBatteryTemp().getVal());
                        valList.add(data.getMinBatteryTemp().getBundleId());
                        valList.add(data.getMinBatteryTemp().getLmuId());
                        valList.add(data.getMinBatteryTemp().getInboxId());
                    } else {
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                    }

                    headerList.forEach(name -> valList.add(nameMap.containsKey(name) ?
                        nameMap.get(name).getV() : "--"));

                    excel.appendData(valList);
                }
            }
        }
    }

}
