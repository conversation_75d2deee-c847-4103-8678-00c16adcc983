package com.cdz360.biz.bi.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "generate.file")
public class ExportFileConfig {
    private String pdfDir = "/opt/export_file/pdf";
    private String excelDir = "/opt/export_file/excel";

    private String downloadExtDir = "download_job";

    public String pdfDownloadExtDir() {
        return pdfDir + downloadExtDir;
    }

    public String excelDownloadExtDir() {
        return excelDir + "/" + downloadExtDir;
    }
}
