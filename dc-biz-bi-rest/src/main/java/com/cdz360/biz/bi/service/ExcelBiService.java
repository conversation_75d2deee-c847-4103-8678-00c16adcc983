package com.cdz360.biz.bi.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.bi.config.ExportFileConfig;
import com.cdz360.biz.bi.feign.TradingFeignClient;
import com.cdz360.biz.bi.service.site.SiteOrderBiService;
import com.cdz360.biz.model.iot.param.MeterRecordBiParam;
import com.cdz360.biz.model.iot.param.SiteBiParam;
import com.cdz360.biz.model.iot.type.ReportBiType;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import com.cdz360.biz.model.trading.site.po.BiSiteElectPo;
import com.cdz360.biz.model.trading.site.po.BiSiteMeterSummaryDto;
import com.cdz360.biz.model.trading.site.po.BiSiteSummaryElectPo;
import com.cdz360.biz.model.trading.site.po.BiSiteSummaryPo;
import com.cdz360.biz.utils.feign.iot.MeterFeignClient;
import com.chargerlinkcar.framework.common.utils.ExportExcel;
import com.chargerlinkcar.framework.common.utils.ExportExcelBi;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import jakarta.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 *  //导出订单excel
 * @since 2019/8/20
 **/
@Slf4j
@Service
public class ExcelBiService //implements IExcelFileService
{

    private static final String CHARSET = "utf-8";

    @Autowired
    private ExportFileConfig exportFileConfig;

    // 默认启动金额
//    @Value("${excel.dir:/tmp/excel}")
//    private String TEMP_DIR;

    private String PART_FILE_NAME = "part";

    @Autowired
    private TradingFeignClient tradingFeignClient;
    @Autowired
    private DictDs dictService;

    @Autowired
    private SiteOrderBiService siteOrderBiService;

    @Autowired
    private CommOrderBiService commOrderBiService;

    @Autowired
    private MeterFeignClient meterFeignClient;

    @FunctionalInterface
    interface Function<Uno, Dos, Tres> {
        Uno apply(Dos dos, Tres tres);
    }

    @FunctionalInterface
    interface FunctionTrans<Uno, Dos> {
        Uno apply(Dos dos);
    }

    /**
     * [实际数据]和[写入数据]必须implements Serializable
     *
     * @param subDir      文件存放子目录
     * @param subFileName 文件名
     * @param clazz       输出到excel的行记录
     * @param f           lambda, 获取[实际数据]
     * @param ft          lambda, 将f获取的[实际数据]转换成[写入数据]
     */
    public void exportExcelFile(
            String subDir,
            String subFileName,
            Class clazz,
            Integer group,
            String sheetName,
            List<String> filterList,
            Function<List<Serializable>, Integer, Integer> f,
            FunctionTrans<List<Serializable>, List<Serializable>> ft) {

        synchronized (this) {
            log.info("exportExcelFile 开始作业");
//            //先清除临时文件夹中的文件
//            deleteFile(searchParam.getSubDir(), searchParam.getType());

            // 文件存放目录
            String dir = exportFileConfig.getExcelDir() + File.separator + subDir;

            // 临时文件
            String tmpFilePath = dir + File.separator + subFileName + PART_FILE_NAME + ".xlsx";

            // 重定向文件
            String filePath = dir + File.separator + subFileName + ".xlsx";

            // excel
            ExportExcel exportExcel = null;
            try {
                if (group != null) {
                    exportExcel = new ExportExcel(null, clazz, sheetName, null, filterList, group);
                } else {
                    exportExcel = new ExportExcel(null, clazz, sheetName, null, filterList);
                }
            } catch (Exception e) {
                log.error("{}", e.getMessage(), e);
                return;
            }

            // 分页索引
            int index = 1; // 分页从1开始
            int size = 1000;

            // 改成分页获取数据
            while (true) {
                // System.out.println("作业中");
                List<Serializable> serializables = f.apply(index, size);
                if (CollectionUtils.isEmpty(serializables)) {
                    log.warn("获取订单结束.");
                    break;
                } else {
                    log.info("当前页: index={}, size={}", index, size);
                    // 追加到excel
                    List<Serializable> tmp = ft.apply(serializables);
                    log.info("请求数据 size = {}", tmp.size());
//                    log.info("tmp: {}", JsonUtils.toJsonString(tmp));
                    exportExcel.addDataList((index - 1) * size + 1, tmp); // 考虑数据开始行号
                    if (tmp.size() < size) {
                        log.info("获取订单结束：size={}", tmp.size());
                        break;
                    }
                    // 页码递增
                    index += 1;
//                    log.info("index: {}", index);
//                    // 10w条数据约束
//                    if (100 == index) {
//                        log.info("已超出最大订单导出条数");
//                        throw new DcServiceException("已超出最大订单导出条数（导出订单条数最多10万条）！减少订单导出条数后，再导出。");
//                    }
                    // 10w条数据约束
                    IotAssert.isTrue(index < 100,
                            "已超出最大订单导出条数（导出订单条数最多10万条）！减少订单导出条数后，再导出。");

                }
            }
            log.info("临时文件: {}, 重定向文件: {}", tmpFilePath, filePath);
            try {
                File folder = new File(dir);
                //如果文件夹不存在则创建
                if (!folder.exists()) {
                    folder.mkdirs();
                }
                exportExcel.writeFile(tmpFilePath);

                //重命名:临时文件->最终文件名
                FileUtils.copyFile(new File(tmpFilePath), new File(filePath));
//                new File(tmpFilePath).renameTo(new File(filePath));
            } catch (IOException e) {
                log.error("<< 写订单excel出错: error={},{}", e.getMessage(), e);
            }
            log.info("<< 写订单结束excel,filePath: {}", filePath);
        }
    }

    /**
     * [实际数据]和[写入数据]必须implements Serializable
     *
     * @param subDir      文件存放子目录
     * @param subFileName 文件名
     * @param headerList   头部信息
     * @param siteSummaryList 数据信息
     */
    public void exportExcelFiles(
            String subDir,
            String subFileName,
            List<String> headerList,
            String sheetName,
            List<BiSiteSummaryPo> siteSummaryList,
            int digits) {

            log.info("exportExcelFile 开始作业");

            // 文件存放目录
            String dir = exportFileConfig.getExcelDir() + File.separator + subDir;

            // 临时文件
            String tmpFilePath = dir + File.separator + subFileName + PART_FILE_NAME + ".xlsx";

            // 重定向文件
            String filePath = dir + File.separator + subFileName + ".xlsx";

            // excel
            ExportExcelBi exportExcel = new ExportExcelBi(null,  headerList, sheetName,  0);

            //组织数据写入文件
            AtomicInteger ordinal = new AtomicInteger(2);
            siteSummaryList.forEach(e->{
                List<Object> data = new ArrayList<>();
                //写入汇总数据
                if (e.getName() == "accountSummary") {
                    data.add("汇总");
                    data.add(e.getTotal());
                    e.getBiSummary().forEach(o->{
                        data.add(o.getAmount());
                    });
                    exportExcel.addDataBi(1, data,digits); // 第一行写入汇总数据
                } else {
                    data.add(e.getName());
                    data.add(e.getTotal());
                    e.getBiSummary().forEach(o->{
                        data.add(o.getAmount());
                    });
                    exportExcel.addDataBi(ordinal.getAndIncrement(), data,digits); // 考虑数据开始行号
                }
            });
            try {
                File folder = new File(dir);
                //如果文件夹不存在则创建
                if (!folder.exists()) {
                    folder.mkdirs();
                }
                exportExcel.writeFile(tmpFilePath);

                //重命名:临时文件->最终文件名
                FileUtils.copyFile(new File(tmpFilePath), new File(filePath));
//                new File(tmpFilePath).renameTo(new File(filePath));
            } catch (IOException e) {
                log.error("<< 写订单excel出错: error={},{}", e.getMessage(), e);
            }
            log.info("<< 写订单结束excel,filePath: {}", filePath);
//        }
    }

    /**
     * 电表抄表导出
     * [实际数据]和[写入数据]必须implements Serializable
     *
     * @param subDir      文件存放子目录
     * @param subFileName 文件名
     * @param headerList   头部信息
     * @param siteSummaryList 数据信息
     */
    public void exportMeterRecordExcelFiles(
            String subDir,
            String subFileName,
            List<String> headerList,
            String sheetName,
            List<BiSiteMeterSummaryDto> siteSummaryList,
            int digits) {

            log.info("exportExcelFile 开始作业");

            // 文件存放目录
            String dir = exportFileConfig.getExcelDir() + File.separator + subDir;

            // 临时文件
            String tmpFilePath = dir + File.separator + subFileName + PART_FILE_NAME + ".xlsx";

            // 重定向文件
            String filePath = dir + File.separator + subFileName + ".xlsx";

            // excel
            ExportExcelBi exportExcel = new ExportExcelBi(null,  headerList, sheetName,  0);

            //组织数据写入文件
            AtomicInteger ordinal = new AtomicInteger(1);
            siteSummaryList.forEach(e->{
                List<Object> data = new ArrayList<>();
                //写入汇总数据
//                if (e.getName() == "accountSummary") {
//                    data.add("汇总");
//                    data.add(e.getTotal());
//                    e.getBiSummary().forEach(o->{
//                        data.add(o.getAmount());
//                    });
//                    exportExcel.addDataBi(1, data,digits); // 第一行写入汇总数据
//                } else {
                    data.add(e.getMeterName());
                    data.add(e.getParamType().getDesc() + "(kW·h)");
                    e.getBiSummaryList().forEach(o->{
                        data.add(o.getAmount());
                    });
                    exportExcel.addDataBi(ordinal.getAndIncrement(), data, digits); // 考虑数据开始行号
//                }
            });
            try {
                File folder = new File(dir);
                //如果文件夹不存在则创建
                if (!folder.exists()) {
                    folder.mkdirs();
                }
                exportExcel.writeFile(tmpFilePath);

                //重命名:临时文件->最终文件名
                FileUtils.copyFile(new File(tmpFilePath), new File(filePath));
//                new File(tmpFilePath).renameTo(new File(filePath));
            } catch (IOException e) {
                log.error("<< 写订单excel出错: error={},{}", e.getMessage(), e);
            }
            log.info("<< 写订单结束excel,filePath: {}", filePath);
//        }
    }

    /**
     * 场站充电走势(电量)
     * @param subDir
     * @param subFileName
     * @param headerList
     * @param sheetName
     */
    public void exportExcelFile(
            String subDir,
            String subFileName,
            List<String> headerList,
            String sheetName,
            SiteBiParam param,
            int isMerchant,
            List<BiSiteElectPo> summaryDetail) throws IOException {

        log.info("exportExcelFile 开始作业");

        // 文件存放目录
        String dir = exportFileConfig.getExcelDir() + File.separator + subDir;

        // 临时文件
        String tmpFilePath = dir + File.separator + subFileName + PART_FILE_NAME + ".xlsx";

        // 重定向文件
        String filePath = dir + File.separator + subFileName + ".xlsx";

        // excel
        ExportExcelBi exportExcel = new ExportExcelBi(null,  headerList, sheetName,  0);

        //组织数据写入文件
        AtomicInteger ordinal = new AtomicInteger(1);

        //写入汇总信息
        List<Object> electricityAmount = new ArrayList<>();
        List<Object> electTag1Amount = new ArrayList<>();
        List<Object> electTag2Amount = new ArrayList<>();
        List<Object> electTag3Amount = new ArrayList<>();
        List<Object> electTag4Amount = new ArrayList<>();
        //设置名称
        electricityAmount.add("汇总");electTag1Amount.add("汇总");electTag2Amount.add("汇总");electTag3Amount.add("汇总");electTag4Amount.add("汇总");
        electricityAmount.add("总电量");electTag1Amount.add("尖");electTag2Amount.add("峰");electTag3Amount.add("平");electTag4Amount.add("谷");
        //设置各个段汇总
        electricityAmount.add(summaryDetail.stream().map(BiSiteElectPo::getElectricity).reduce(BigDecimal::add).get());
        electTag1Amount.add(summaryDetail.stream().map(BiSiteElectPo::getElecTag1).reduce(BigDecimal::add).get());
        electTag2Amount.add(summaryDetail.stream().map(BiSiteElectPo::getElecTag2).reduce(BigDecimal::add).get());
        electTag3Amount.add(summaryDetail.stream().map(BiSiteElectPo::getElecTag3).reduce(BigDecimal::add).get());
        electTag4Amount.add(summaryDetail.stream().map(BiSiteElectPo::getElecTag4).reduce(BigDecimal::add).get());
        summaryDetail.forEach(e->{
            electricityAmount.add(e.getElectricity());
            electTag1Amount.add(e.getElecTag1());
            electTag2Amount.add(e.getElecTag2());
            electTag3Amount.add(e.getElecTag3());
            electTag4Amount.add(e.getElecTag4());
        });
        //汇总信息写入文件
        exportExcel.addDataElectBi(ordinal.getAndIncrement(),electricityAmount,4,1);
        //写入尖时电量
        exportExcel.addDataElectBi(ordinal.getAndIncrement(),electTag1Amount,4,0);
        //写入峰时电量
        exportExcel.addDataElectBi(ordinal.getAndIncrement(),electTag2Amount,4,0);
        //写入平时电量
        exportExcel.addDataElectBi(ordinal.getAndIncrement(),electTag3Amount,4,0);
        //写入谷时电量
        exportExcel.addDataElectBi(ordinal.getAndIncrement(),electTag4Amount,4,0);
        Long start = 0L ;
        while (true) {
            List<BiSiteSummaryElectPo> siteSummaryList = null;
//            param.setStart(start).setSize(100);

            if (isMerchant == 0) {
                siteSummaryList = siteOrderBiService.getBiElectBySiteList(param);
            } else {
                siteSummaryList = commOrderBiService.getBiElectByCorpList(param);
            }
            if (siteSummaryList == null || siteSummaryList.size() == 0) {
                break;
            }
            siteSummaryList.forEach(e->{

                //写入汇总数据
                List<Object> electricity = new ArrayList<>();
                List<Object> electTag1 = new ArrayList<>();
                List<Object> electTag2 = new ArrayList<>();
                List<Object> electTag3 = new ArrayList<>();
                List<Object> electTag4 = new ArrayList<>();

                //设置名称
                electricity.add(e.getName());
                electTag1.add(e.getName());
                electTag2.add(e.getName());
                electTag3.add(e.getName());
                electTag4.add(e.getName());

                //设置汇总信息
                electricity.add("总电量");
                electTag1.add("尖");
                electTag2.add("峰");
                electTag3.add("平");
                electTag4.add("谷");

                //汇总
                electricity.add(e.getBiSummary().stream().map(BiSiteElectPo::getElectricity).reduce(BigDecimal::add).get());
                electTag1.add(e.getBiSummary().stream().map(BiSiteElectPo::getElecTag1).reduce(BigDecimal::add).get());
                electTag2.add(e.getBiSummary().stream().map(BiSiteElectPo::getElecTag2).reduce(BigDecimal::add).get());
                electTag3.add(e.getBiSummary().stream().map(BiSiteElectPo::getElecTag3).reduce(BigDecimal::add).get());
                electTag4.add(e.getBiSummary().stream().map(BiSiteElectPo::getElecTag4).reduce(BigDecimal::add).get());

                //每日数据添加
                e.getBiSummary().forEach(o->{
                    electricity.add(o.getElectricity());
                    electTag1.add(o.getElecTag1());
                    electTag2.add(o.getElecTag2());
                    electTag3.add(o.getElecTag3());
                    electTag4.add(o.getElecTag4());
                });

                //写入总电量
                exportExcel.addDataElectBi(ordinal.getAndIncrement(),electricity,4,1);
                //写入尖时电量
                exportExcel.addDataElectBi(ordinal.getAndIncrement(),electTag1,4,0);
                //写入峰时电量
                exportExcel.addDataElectBi(ordinal.getAndIncrement(),electTag2,4,0);
                //写入平时电量
                exportExcel.addDataElectBi(ordinal.getAndIncrement(),electTag3,4,0);
                //写入谷时电量
                exportExcel.addDataElectBi(ordinal.getAndIncrement(),electTag4,4,0);
            });
            break;
//            //按照场站汇总 一次读取结束
//            if (isMerchant == 0) {
//                break;
//            }
//            if (siteSummaryList.size() < 100) {
//                break;
//            }
//            start +=100;
        }

        //写入结束 调整汉字列的宽度
        int[] columnList = new int[]{0,1};
        exportExcel.setAutoColumnWidth(columnList);

//        try {
            File folder = new File(dir);
            //如果文件夹不存在则创建
            if (!folder.exists()) {
                final boolean b = folder.mkdirs();
                log.debug("创建: {}", b);
            }
            exportExcel.writeFile(tmpFilePath);

            //重命名:临时文件->最终文件名
            FileUtils.copyFile(new File(tmpFilePath), new File(filePath));
//            new File(tmpFilePath).renameTo(new File(filePath));
//        } catch (IOException e) {
//            log.error("<< 写订单excel出错: error={},{}", e.getMessage(), e);
//        }
//        log.info("<< 写订单结束excel,filePath: {}", filePath);
//        }
    }

    public void exportFeeExcelFile(
            String subDir,
            String subFileName,
            List<String> headerList,
            String sheetName,
            SiteBiParam param,
            int isMerchant,
            List<BiSiteElectPo> summaryDetail) throws IOException {

        log.info("exportExcelFile 开始作业");

        // 文件存放目录
        String dir = exportFileConfig.getExcelDir() + File.separator + subDir;

        // 临时文件
        String tmpFilePath = dir + File.separator + subFileName + PART_FILE_NAME + ".xlsx";

        // 重定向文件
        String filePath = dir + File.separator + subFileName + ".xlsx";

        // excel
        ExportExcelBi exportExcel = new ExportExcelBi(null,  headerList, sheetName,  0);

        //组织数据写入文件
        AtomicInteger ordinal = new AtomicInteger(1);

        //写入汇总信息
        List<Object> fee = new ArrayList<>();
        List<Object> electFee = new ArrayList<>();
        List<Object> serveFee = new ArrayList<>();
        fee.add("汇总");electFee.add("汇总");serveFee.add("汇总");
        fee.add("总金额");electFee.add("电费");serveFee.add("服务费");

        fee.add(summaryDetail.stream().map(BiSiteElectPo::getFee).reduce(BigDecimal::add).get());
        electFee.add(summaryDetail.stream().map(BiSiteElectPo::getElectFee).reduce(BigDecimal::add).get());
        serveFee.add(summaryDetail.stream().map(BiSiteElectPo::getServeFee).reduce(BigDecimal::add).get());

        summaryDetail.forEach(e->{
            fee.add(e.getFee());electFee.add(e.getElectFee());serveFee.add(e.getServeFee());
        });

        //写入汇总
        //写入总金额
        exportExcel.addDataFeeBi(ordinal.getAndIncrement(),fee,2,1);
        //写入电费
        exportExcel.addDataFeeBi(ordinal.getAndIncrement(),electFee,2,0);
        //写入服务费
        exportExcel.addDataFeeBi(ordinal.getAndIncrement(),serveFee,2,0);

        int start = 0;
        while (true) {
//            param.setStart((long) start).setSize(100);
            List<BiSiteSummaryElectPo> siteSummaryList = null;
            if (isMerchant == 1) {
                siteSummaryList = commOrderBiService.getBiElectByCorpList(param);
            } else {
                siteSummaryList = siteOrderBiService.getBiElectBySiteList(param);
            }
            if (siteSummaryList == null || siteSummaryList.size() == 0) {
                break;
            }
            siteSummaryList.forEach(e -> {

                //写入汇总数据
                List<Object> feeAmount = new ArrayList<>();
                List<Object> electFeeAmount = new ArrayList<>();
                List<Object> serveFeeAmount = new ArrayList<>();

                //设置名称
                feeAmount.add(e.getName());
                electFeeAmount.add(e.getName());
                serveFeeAmount.add(e.getName());

                //设置汇总信息
                feeAmount.add("总金额");
                electFeeAmount.add("电费");
                serveFeeAmount.add("服务费");

                //汇总
                feeAmount.add(e.getBiSummary().stream().map(BiSiteElectPo::getFee).reduce(BigDecimal::add).get());
                electFeeAmount.add(e.getBiSummary().stream().map(BiSiteElectPo::getElectFee).reduce(BigDecimal::add).get());
                serveFeeAmount.add(e.getBiSummary().stream().map(BiSiteElectPo::getServeFee).reduce(BigDecimal::add).get());


                //每日数据添加
                e.getBiSummary().forEach(o -> {
                    feeAmount.add(o.getFee());
                    electFeeAmount.add(o.getElectFee());
                    serveFeeAmount.add(o.getServeFee());
                });
                //写入总金额
                exportExcel.addDataFeeBi(ordinal.getAndIncrement(), feeAmount, 2, 1);
                //写入电费
                exportExcel.addDataFeeBi(ordinal.getAndIncrement(), electFeeAmount, 2, 0);
                //写入服务费
                exportExcel.addDataFeeBi(ordinal.getAndIncrement(), serveFeeAmount, 2, 0);
            });
            break;
//            if (isMerchant == 0) {
//                break;
//            }
//
//            if (siteSummaryList.size()<100) {
//                break;
//            }
//            start += 100;
        }
        //写入结束调整汉字列宽度
        int[] columnList = new int[]{0,1};
        exportExcel.setAutoColumnWidth(columnList);
//        try {
            File folder = new File(dir);
            //如果文件夹不存在则创建
            if (!folder.exists()) {
                folder.mkdirs();
            }
            exportExcel.writeFile(tmpFilePath);

            //重命名:临时文件->最终文件名
        FileUtils.copyFile(new File(tmpFilePath), new File(filePath));
//            new File(tmpFilePath).renameTo(new File(filePath));
//        } catch (IOException e) {
//            log.error("<< 写订单excel出错: error={},{}", e.getMessage(), e);
//        }
//        log.info("<< 写订单结束excel,filePath: {}", filePath);
//        }
    }

    /**
     * 根据站点名称导出汇总信息
     * @param param
     */
//    @Async
    public void writeTempExcelBySiteList(SiteBiParam param) {

        //根据param条件  获取汇总信息
        param.setSize(9999);
        ListResponse<BiSiteSummaryPo> siteSummaryList = siteOrderBiService.getBiSiteList(param);

       //组织头部信息
        List<String> headerList = new ArrayList<>();
        headerList.add("站点名称");

        //根据查询汇总类型  判断保留小数位
        Integer[]contentArr = new Integer[] {5,6,7};
        List<Integer> list = Arrays.asList(contentArr);
        int digits = 4;
        if (list.contains(param.getBiContent().getCode()) ){
            digits = 2;
            headerList.add("累计之和（元）");
        } else {
            headerList.add("累计之和（kW·h)");
        }
        siteSummaryList.getData().subList(0,1).forEach(e->{
            e.getBiSummary().forEach(bi->{
                if (param.getSampleType().equals(SiteBiSampleType.DAY)) {

                    SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM-dd");
                    headerList.add(sdf.format(bi.getDay()));
                } else {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM");
                    headerList.add(sdf.format(bi.getMonth()));
                }
                return ;
            });
        });

        exportExcelFiles(
                param.getExcelPosition().getSubDir(),
                param.getExcelPosition().getSubFileName(),
                headerList,
                param.getSheetName(),
                siteSummaryList.getData(),
                digits
        );
    }

    public void exportBiSiteMeterRecordSync(MeterRecordBiParam param) {
        param.setSize(9999);
        param.setStart(0L);
//        ListResponse<BiSiteSummaryPo> siteSummaryList = siteOrderBiService.getBiSiteList(param);
        ListResponse<BiSiteMeterSummaryDto> biSiteMeterRecord = meterFeignClient.getBiSiteMeterRecord(param);
        FeignResponseValidate.check(biSiteMeterRecord);
        IotAssert.isTrue(CollectionUtils.isNotEmpty(biSiteMeterRecord.getData()), "无电表抄表数据");

        this.exportBiSiteMeterRecord(param, biSiteMeterRecord);
    }

    /**
     * 根据站点名称导出汇总信息
     * @param param
     */
//    @Async
    private void exportBiSiteMeterRecord(MeterRecordBiParam param, ListResponse<BiSiteMeterSummaryDto> biSiteMeterRecord) {

        //根据param条件  获取汇总信息
        param.setSize(9999);
        param.setStart(0L);
//        ListResponse<BiSiteSummaryPo> siteSummaryList = siteOrderBiService.getBiSiteList(param);
//        ListResponse<BiSiteMeterSummaryDto> biSiteMeterRecord = meterFeignClient.getBiSiteMeterRecord(param);
        FeignResponseValidate.check(biSiteMeterRecord);
//        ListResponse<BiSiteMeterSummaryDto> siteSummaryList = biSiteMeterRecord.getData();

        IotAssert.isTrue(CollectionUtils.isNotEmpty(biSiteMeterRecord.getData()), "无电表抄表数据");
        //组织头部信息
        List<String> headerList = new ArrayList<>();
        headerList.add("电表名称");
        headerList.add("电表参数");

        //根据查询汇总类型  判断保留小数位
//        Integer[]contentArr = new Integer[] {5,6,7};
//        List<Integer> list = Arrays.asList(contentArr);
        int digits = 4;
//        if (list.contains(param.getBiContent().getCode()) ){
//            digits = 2;
//            headerList.add("累计之和（元）");
//        } else {
//            headerList.add("累计之和（kW·h)");
//        }
        biSiteMeterRecord.getData().subList(0,1).forEach(e->{
            e.getBiSummaryList().forEach(bi->{
                if (param.getSampleType().equals(SiteBiSampleType.DAY)) {
                    String europeanDatePattern = "yyyy-MM-dd";
                    DateTimeFormatter europeanDateFormatter = DateTimeFormatter.ofPattern(europeanDatePattern);
                    headerList.add(bi.getDay().format(europeanDateFormatter));
                } else {
                    String europeanDatePattern = "yyyy-MM";
                    DateTimeFormatter europeanDateFormatter = DateTimeFormatter.ofPattern(europeanDatePattern);
                    headerList.add(bi.getMonth().format(europeanDateFormatter));
                }
                return ;
            });
        });

        this.exportMeterRecordExcelFiles(
                param.getExcelPosition().getSubDir(),
                param.getExcelPosition().getSubFileName(),
                headerList,
                param.getSheetName(),
                biSiteMeterRecord.getData(),
                digits
        );
    }

    /**
     * 根据企业导出汇总信息
     * @param param
     */
    @Async
    public void writeTempExcelByCorpList(SiteBiParam param) {

        //根据param条件  获取汇总信息
        param.setSize(9999).setStart(0L);
        ListResponse<BiSiteSummaryPo> siteSummaryList = commOrderBiService.getBiCorpList(param);
        //组织头部信息
        List<String> headerList = new ArrayList<>();
        headerList.add("企业名称");
        //根据查询汇总类型  判断保留小数位,头部信息
        Integer[]contentArr = new Integer[] {5,6,7};
        List<Integer> list = Arrays.asList(contentArr);

        int digits = 4;
        if (list.contains(param.getBiContent().getCode()) ){
            digits = 2;
            headerList.add("累计之和（元）");
        } else {
            headerList.add("累计之和（kW·h)");
        }

        siteSummaryList.getData().subList(0,1).forEach(e->{
            e.getBiSummary().forEach(bi->{
                if (param.getSampleType().equals(SiteBiSampleType.DAY)) {

                    SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM-dd");
                    headerList.add(sdf.format(bi.getDay()));
                } else {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM");
                    headerList.add(sdf.format(bi.getMonth()));
                }
                return ;
            });
        });


        exportExcelFiles(
                param.getExcelPosition().getSubDir(),
                param.getExcelPosition().getSubFileName(),
                headerList,
                param.getSheetName(),
                siteSummaryList.getData(),
                digits
        );
    }

    /**
     * 企业、场站充电走势(电量)
     * @param param
     */
//    @Async
    public void writeTempElectBySiteOrCommList(SiteBiParam param, int isMerchant) throws IOException {

        //以下条件暂时使用
        param.setBiContent(ReportBiType.ELECT_AMOUNT);

        List<String> headerList = new ArrayList<>();
        List<BiSiteSummaryElectPo> result = null;
        List<BiSiteElectPo> summaryDetail = null;
        if (isMerchant == 1) {
            headerList.add("企业名称");
            summaryDetail = commOrderBiService.getSummaryListByCorpCondition(param);

        } else {
            headerList.add("站点名称");
            summaryDetail = commOrderBiService.getSummaryListByCondition(param);
        }
        headerList.add("");
        headerList.add("总计（kW·h)");

        summaryDetail.forEach(e->{
                if (param.getSampleType().equals(SiteBiSampleType.DAY)) {

                    SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM-dd");
                    headerList.add(sdf.format(e.getDay()));
                } else {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM");
                    headerList.add(sdf.format(e.getMonth()));
                }
        });

        exportExcelFile(
                param.getExcelPosition().getSubDir(),
                param.getExcelPosition().getSubFileName(),
                headerList,
                param.getSheetName(),
                param,
                isMerchant,
                summaryDetail
        );
    }

    /**
     * 商户、场站充电走势(消费)
     * @param param
     */
//    @Async
    public void writeTempFeeBySiteOrCommList(SiteBiParam param, int isMerchant) throws IOException {

        //根据param条件  获取汇总信息

        //以下条件暂时使用
        param.setBiContent(ReportBiType.ELECT_AMOUNT);


        List<String> headerList = new ArrayList<>();

        List<BiSiteElectPo> summaryDetail = null;
        List<BiSiteSummaryElectPo> result = null;

        if (isMerchant == 1) {
            headerList.add("企业名称");
            summaryDetail = commOrderBiService.getSummaryListByCorpCondition(param);
//            result = commOrderBiService.getBiElectByCorpList(param);
        } else {
            headerList.add("站点名称");
            summaryDetail = commOrderBiService.getSummaryListByCondition(param);
//            result = siteOrderBiService.getBiElectBySiteList(param);
        }

        headerList.add("");
        headerList.add("总计（元）");
        summaryDetail.forEach(e->{
                if (param.getSampleType().equals(SiteBiSampleType.DAY)) {

                    SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM-dd");
                    headerList.add(sdf.format(e.getDay()));
                } else {
                    SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM");
                    headerList.add(sdf.format(e.getMonth()));
                }
        });


        exportFeeExcelFile(
                param.getExcelPosition().getSubDir(),
                param.getExcelPosition().getSubFileName(),
                headerList,
                param.getSheetName(),
                param,
                isMerchant,
                summaryDetail
        );
    }

    public boolean existsDownFile(String subDir, String type, String subFileName) {
        //String fileName = DownloadType.OFFLINE_ORDER.getCode().equals(type) ? DownloadType.OFFLINE_ORDER.getName() + ".xlsx" : DownloadType.ONLINE_ORDER.getName() + ".xlsx";
        String fileName = subFileName + ".xlsx";
        String dir = exportFileConfig.getExcelDir() + File.separator + subDir;
        String filePath = dir + File.separator + fileName;

        log.info("判断existsDownFile, filePath:{}", filePath);

        File file1 = new File(filePath);
        return file1.exists();
    }

    private void deleteFile(File file) {
        if (file.isDirectory()) {
            //递归删除文件夹下所有文件
            File[] files = file.listFiles();
            for (File f : files) {
                deleteFile(f);
            }

            //删除文件夹自己
            if (file.listFiles().length == 0) {
                log.info("删除文件夹：[{}]", file);
                file.delete();
            }
        } else {
            // 如果是文件,就直接删除自己
            log.info("删除文件：[{}]", file);
            file.delete();
        }
    }


    public void cleanExcelFiles() {
        log.info("开始删除文件...");
        long startTime = System.currentTimeMillis();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

        //删除子文件夹
        File deleteFile = new File(exportFileConfig.getExcelDir());
        File[] deleteFiles = deleteFile.listFiles();
        for (File file : deleteFiles) {
            boolean needDel = false;
            try {
                Date fileCreateDate = sdf.parse(file.getName());
                needDel = sdf.parse(sdf.format(new Date())).compareTo(fileCreateDate) > 0;
            } catch (ParseException e) {
                log.error("子文件夹命名不合规范", file.getName());
                needDel = true;
            }
            //删除今天之前的文件夹及其子文件
            if (needDel) {
                deleteFile(file);
            }
        }
        log.info("删除文件结束,总耗时：[{}]毫秒", System.currentTimeMillis() - startTime);
    }


    public void downFile(String subDir, String type, String subFileName, HttpServletResponse response) {
        if (existsDownFile(subDir, type, subFileName)) {
            //String fileName = DownloadType.OFFLINE_ORDER.getCode().equals(type) ? DownloadType.OFFLINE_ORDER.getName() + ".xlsx" : DownloadType.ONLINE_ORDER.getName() + ".xlsx";
            String fileName = subFileName + ".xlsx";
            String dir = exportFileConfig.getExcelDir() + File.separator + subDir;
            String filePath = dir + File.separator + fileName;
            log.info("downFile, filePath:{}", filePath);
            try {
                response.setContentType("application/vnd..ms-excel");
                response.setHeader("content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, CHARSET));
                //读取指定路径下面的文件
                InputStream in = new FileInputStream(filePath);
                OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
                //创建存放文件内容的数组
                byte[] buff = new byte[1024];
                //所读取的内容使用n来接收
                int n;
                //当没有读取完时,继续读取,循环
                while ((n = in.read(buff)) != -1) {
                    //将字节数组的数据全部写入到输出流中
                    outputStream.write(buff, 0, n);
                }
                //强制将缓存区的数据进行输出
                outputStream.flush();
                //关流
                outputStream.close();
                in.close();
            } catch (Exception e) {
                log.error("导出订单excel失败");
            }
        } else {
            log.error("excel文件不存在");
        }
    }
}
