package com.cdz360.biz.bi;

import com.netflix.discovery.EurekaClient;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import reactivefeign.spring.config.EnableReactiveFeignClients;
import reactor.core.publisher.Hooks;

@EnableDiscoveryClient
@SpringBootApplication
@EnableAutoConfiguration(exclude = {FreeMarkerAutoConfiguration.class})
@EnableReactiveFeignClients(basePackages = {
    "com.cdz360.biz.utils.feign.*",
    "com.cdz360.biz.bi.feign.reactor"
})
@EnableFeignClients(basePackages = {
    "com.cdz360.biz.bi.feign",
    "com.chargerlinkcar.framework.common",
    "com.cdz360.biz.utils.feign.iot"
})
@Slf4j
@MapperScan(basePackages = {"com.cdz360.biz.bi.mapper", "com.cdz360.biz.ds.trading.ro.**.mapper",
    "com.cdz360.biz.ds.ess.**.mapper"})
@ComponentScan(basePackages = {
    "com.chargerlinkcar.core",
    "com.chargerlinkcar.framework.common",
    "com.cdz360.biz.bi",
    "com.cdz360.data.cache",
    "com.cdz360.biz.ds.trading.ro",
    "com.cdz360.biz.ds.ess",
    "com.cdz360.biz.utils.config",
    "com.cdz360.biz.utils.feign.iot",
    "com.cdz360.biz.utils.service",
    "com.cdz360.biz.model.props"
})
@EnableAsync
@EnableScheduling
public class BizBiMain {

    @Autowired
    private EurekaClient discoveryClient;

    /**
     * 主函数
     */
    public static void main(String[] args) {
        Hooks.enableAutomaticContextPropagation();  // 输出调用链traceId
        SpringApplication.run(BizBiMain.class, args);
    }


    @PreDestroy
    public void destroy() {
        log.info("going to shutdown");

        //DiscoveryManager.getInstance().shutdownComponent();
        discoveryClient.shutdown();
        log.info("eureka de-registered.....");
    }
}
