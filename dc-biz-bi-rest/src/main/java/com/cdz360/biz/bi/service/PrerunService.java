package com.cdz360.biz.bi.service;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.bi.config.ExportFileConfig;
import com.cdz360.biz.bi.service.siteGroup.SiteGroupService;
import com.cdz360.biz.ds.trading.ro.prerun.ds.PrerunCheckRoDs;
import com.cdz360.biz.ds.trading.ro.prerun.ds.PrerunEvseRoDs;
import com.cdz360.biz.ds.trading.ro.prerun.ds.PrerunRoDs;
import com.cdz360.biz.model.trading.bi.dto.BiExportGroups;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.prerun.param.PrerunSearchParam;
import com.cdz360.biz.model.trading.prerun.po.PrerunCheckPo;
import com.cdz360.biz.model.trading.prerun.po.PrerunEvsePo;
import com.cdz360.biz.model.trading.prerun.po.PrerunPo;
import com.cdz360.biz.model.trading.prerun.vo.ExportPrerunRecordVo;
import com.cdz360.biz.model.trading.prerun.vo.PrerunVo;
import com.cdz360.biz.model.trading.site.type.SiteGcType;
import com.chargerlinkcar.framework.common.utils.ExcelUtil;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * PrerunService
 *
 * @since 6/29/2022 4:09 PM
 * <AUTHOR>
 */
@Slf4j
@Service
public class PrerunService {

    @Autowired
    private ExportFileConfig exportFileConfig;

    @Autowired
    private PrerunRoDs prerunRoDs;
    @Autowired
    private SiteGroupService siteGroupService;

    @Autowired
    private PrerunCheckRoDs prerunCheckRoDs;

    @Autowired
    private PrerunEvseRoDs prerunEvseRoDs;

    public void exportPrerunList(PrerunSearchParam param, ExcelPosition position) {
        log.info("调试工单列表导出到EXCEL: {}", JsonUtils.toJsonString(position));
        try {
            AtomicReference<Map<String, List<String>>> siteGidMapRef = new AtomicReference<>(
                new HashMap<>());
            AtomicReference<Map<String, String>> groupMapRef = new AtomicReference<>(
                new HashMap<>());
            boolean empty = false;

            BiFunction<Integer, Integer, List<Serializable>> fetch = empty ?
                (start, size) -> List.of() : (start, size) -> {
                param.setStart(((long) (start - 1) * size))
                    .setSize(size);
                param.setTotal(Boolean.FALSE);

                final List<PrerunVo> all = prerunRoDs.searchPrerunList(param);

                List<String> siteIdList = all.stream().map(PrerunPo::getSiteId)
                    .collect(Collectors.toList());
                siteGroupService.fillSiteGroupMap(siteIdList, siteGidMapRef.get(),
                    groupMapRef.get());

                return new ArrayList<>(all);
            };

            ExcelUtil.builder(exportFileConfig.getExcelDir(), position,
                    BiExportGroups.PRERUN_LIST.getName())
                .addHeader(ExportPrerunRecordVo.class)
                .loopAppendData(fetch, list -> new ArrayList<>(list.stream()
                    .map(i -> {
                        PrerunVo recordVo = (PrerunVo) i;

                        final PrerunCheckPo prerunCheckPo = prerunCheckRoDs.getLatestByPrerunId(
                            recordVo.getId());

                        final List<PrerunEvsePo> prerunEvsePos = prerunEvseRoDs.getByPrerunId(
                            recordVo.getId());

                        List<ExportPrerunRecordVo> result = new ArrayList<>();

                        if (CollectionUtils.isNotEmpty(prerunEvsePos)) {
                            result = prerunEvsePos.stream().map(evse -> {
                                ExportPrerunRecordVo ret = new ExportPrerunRecordVo();
                                BeanUtils.copyProperties(evse, ret);
                                BeanUtils.copyProperties(recordVo, ret);

                                siteGidMapRef.get().getOrDefault(recordVo.getSiteId(), List.of())
                                    .stream()
                                    .filter(Objects::nonNull)
                                    .filter(t -> StringUtils.isNotBlank(groupMapRef.get().get(t)))
                                    .findFirst().ifPresent(s -> {
                                        ret.setSiteGidName(groupMapRef.get().get(s));
                                    });

                                if (prerunCheckPo != null) {
                                    ret.setCheckerComment(prerunCheckPo.getComment())
                                        .setCheckerName(prerunCheckPo.getOpName());
                                }
                                if (recordVo.getGcType() != null) {
                                    ret.setGcType(
                                        SiteGcType.valueOf(recordVo.getGcType()).getDesc());
                                }
                                if (evse.getSupply() != null) {
                                    switch (evse.getSupply()) {
                                        case UNKNOWN:
                                            ret.setSupply("未知");
                                            break;
                                        case AC:
                                            ret.setSupply("交流");
                                            break;
                                        case DC:
                                            ret.setSupply("直流");
                                            break;
                                        case BOTH:
                                            ret.setSupply("交直流");
                                            break;
                                    }
                                } else {
                                    ret.setSupply("未知");
                                }
                                if (evse.getFault() != null) {
                                    ret.setFault(evse.getFault() ? "是" : "否");
                                }
                                if (recordVo.getStatus() != null) {
                                    ret.setStatus(recordVo.getStatus().getDesc());
                                }
                                return ret;
                            }).collect(Collectors.toList());
                        }
                        return result;
                    })
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList())))
                .write2File();
            log.info("导出excel完成: {}", JsonUtils.toJsonString(position));
        } catch (Exception e) {
            log.error("导出excel异常: {}, err = {}",
                JsonUtils.toJsonString(position), e.getMessage(), e);
        }
    }
}