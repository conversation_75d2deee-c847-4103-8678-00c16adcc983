package com.cdz360.biz.bi.domain.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2019/5/15 14:29
 */
@Data
@Accessors(chain = true)
public class VinExportI18n2Vo implements Serializable {

    @ExcelField(title = "VIN码", i18nTitle = "vin.vinCode", sort = 1)
    @Schema(description = "VIN码")
    private String vin;

    @ExcelField(title = "所属商户", i18nTitle = "common.commercialFullName", sort = 2)
    @Schema(description = "所属商户")
    private String subCommName;

    @ExcelField(title = "客户名称", i18nTitle = "common.userName", sort = 3)
    @Schema(description = "客户名称")
    private String userName;

    @ExcelField(title = "车牌号", i18nTitle = "car.carNo", sort = 4)
    @Schema(description = "车牌号")
    private String carNo;

    @ExcelField(title = "车队", i18nTitle = "car.carDepartWithoutName", sort = 5)
    @Schema(description = "车队")
    private String carDepart;

    @ExcelField(title = "线路", i18nTitle = "car.lineNum", sort = 6)
    @Schema(description = "线路")
    private String lineNum;

    @ExcelField(title = "车辆自编号", i18nTitle = "car.carNum", sort = 7)
    @Schema(description = "车辆自编号")
    private String carNum;

    @ExcelField(title = "在线认证可用站点数", i18nTitle = "vin.usableStationCount", sort = 8)
    @Schema(description = "指定站点数量")
    private String usableStationCount;


    @ExcelField(title = "本地认证", i18nTitle = "vin.authStatus", sort = 9)
    @Schema(description = "本地认证")
    private String authStatus;


    @ExcelField(title = "创建时间", i18nTitle = "common.createTime", sort = 10)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}