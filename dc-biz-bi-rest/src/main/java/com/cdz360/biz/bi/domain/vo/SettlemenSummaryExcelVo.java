package com.cdz360.biz.bi.domain.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import java.math.BigDecimal;
import lombok.Data;

/**
 * SettlemenSummaryExcelVo
 *
 * @since 7/3/2020 2:49 PM
 * <AUTHOR>
 */
@Data
public class SettlemenSummaryExcelVo {

    @ExcelField(title = "场站名称", patternReplace = "PT_SITE_NAME")
    private String siteName;

    @ExcelField(title = "结算方案", patternReplace = "PT_SUB_SETTLEMENT")
    private String subSettlement;

    @ExcelField(title = "电费总金额", patternReplace = "PT_ELEC_TOTAL_FEE")
    private BigDecimal elecTotalFee;

    @ExcelField(title = "结算总电量", patternReplace = "PT_ORDER_KWH")
    private BigDecimal orderKwh;

    @ExcelField(title = "客户名称", patternReplace = "PT_CUS_NAME")
    private String cusName;

    @ExcelField(title = "场站编号", patternReplace = "PT_SITE_NUM")
    private String siteNum;

    @ExcelField(title = "保底电量", patternReplace = "PT_GUARANTEE_KWH")
    private String guaranteeKwh;

    @ExcelField(title = "服务费总金额", patternReplace = "PT_SERV_TOTAL_FEE")
    private BigDecimal servTotalFee;

    @ExcelField(title = "结算总金额", patternReplace = "PT_SETTLEMENT_TOTAL_FEE")
    private BigDecimal settlementTotalFee;

    @ExcelField(title = "其他费用", patternReplace = "PT_OTHER_FEE")
    private String settlementOtherFee;

    @ExcelField(title = "结算周期", patternReplace = "PT_TIME")
    private String settlementDate;

    @ExcelField(title = "销方名称", patternReplace = "SALE_NAME")
    private String saleName;

    @ExcelField(title = "销方银行名称", patternReplace = "SALE_BANK")
    private String saleBank;

    @ExcelField(title = "销方银行账号", patternReplace = "SALE_ACCOUNT")
    private String saleAccount;

    @ExcelField(title = "购方名称", patternReplace = "PAY_NAME")
    private String name;

    @ExcelField(title = "购方地址", patternReplace = "PAY_ADDRESS")
    private String address;


}