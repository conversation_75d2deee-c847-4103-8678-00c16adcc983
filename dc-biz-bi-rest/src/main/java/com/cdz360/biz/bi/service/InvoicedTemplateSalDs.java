package com.cdz360.biz.bi.service;

import com.cdz360.biz.bi.mapper.InvoicedTemplateSalMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * InvoicedTemplateSalDs
 *  发票主体
 * @since 5/31/2023 2:27 PM
 * <AUTHOR>
 */
@Slf4j
@Service
public class InvoicedTemplateSalDs {
    @Autowired
    private InvoicedTemplateSalMapper mapper;

    public String getNameById(Long id) {
        return mapper.getNameById(id);
    }
}