package com.cdz360.biz.bi.service.download.impl;

import static com.cdz360.biz.bi.service.download.impl.AbstractFileExport.FORMAT_yyyy_MM_dd_HH_mm_ss;

import com.cdz360.base.model.es.vo.BmsBundleRtData;
import com.cdz360.base.model.es.vo.BmsBundleRtInfo;
import com.cdz360.base.model.es.vo.BmsRtData;
import com.cdz360.base.model.es.vo.BmsRtInfo;
import com.cdz360.base.model.es.vo.BmsStackRtData;
import com.cdz360.base.model.es.vo.BmsStackRtInfo;
import com.cdz360.base.model.es.vo.EssBaseVal;
import com.cdz360.base.model.es.vo.RegisterRwValue;
import com.cdz360.base.model.es.vo.SignalVal;
import com.cdz360.base.model.es.vo.StringVal;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.chargerlinkcar.framework.common.utils.ExcelUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import java.io.File;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.LineIterator;
import org.apache.commons.io.input.ReversedLinesFileReader;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class BatClusterHistoryDataManager {

    private List<List<String>> extractHeaderList(BmsBundleRtInfo data) {
        List<List<String>> result = new ArrayList<>();
        List<StringVal> node1 = data.getTexts();
        if (null != node1) {
            List<String> tmpList = new ArrayList<>();
            node1.forEach(kv -> tmpList.add(kv.getName()));
            result.add(tmpList);
        }

        List<SignalVal> node2 = data.getSignals();
        if (null != node2) {
            List<String> tmpList = new ArrayList<>();
            node2.forEach(kv -> tmpList.add(kv.getName()));
            result.add(tmpList);
        }

        List<RegisterRwValue> node3 = data.getCfgs();
        if (null != node3) {
            List<String> tmpList = new ArrayList<>();
            node3.forEach(kv -> tmpList.add(kv.getName()));
            result.add(tmpList);
        }

        return result;
    }

    public void batteryClusterStatusExcelData(final String tz, final String localFile,
        LineIterator lineIterator, String dno, ExcelUtil excel) {
        // 请求头信息
        List<String> headerList = new ArrayList<>();
        final List<List<String>> nodeHeaderList = new ArrayList<>();
        try (ReversedLinesFileReader reader = new ReversedLinesFileReader(
            new File(localFile), Charset.defaultCharset())) {
            String line = reader.readLine();
            while (null != line) {
                JsonNode node = JsonUtils.fromJson(OssLineSplit.validSplit(line));
                if (null == node) {
                    line = reader.readLine();
                    continue;
                }

                JsonNode jsonNode = node.get("rtInfo");
                if (null == jsonNode) {
                    line = reader.readLine();
                    continue;
                }

                BmsRtInfo bms = JsonUtils.fromJson(jsonNode,
                    new TypeReference<BmsRtInfo>() {
                    });
                if (null == bms) {
                    line = reader.readLine();
                    continue;
                }

                Optional<BmsBundleRtInfo> clusterRtData = bms.getStackInfoList().stream()
                    .map(BmsStackRtInfo::getBundleList)
                    .flatMap(Collection::stream)
                    .filter(x -> dno.equals(x.getDno())).findFirst();
                if (clusterRtData.isPresent()) {
                    BmsBundleRtInfo data = clusterRtData.get();

                    if (CollectionUtils.isNotEmpty(data.getTexts()) ||
                        CollectionUtils.isNotEmpty(data.getSignals()) ||
                        CollectionUtils.isNotEmpty(data.getCfgs())) {
                        nodeHeaderList.addAll(extractHeaderList(data));
                        headerList.add("日期");
                        nodeHeaderList.forEach(headerList::addAll);
                        break;
                    }
                }
                line = reader.readLine();
            }
        } catch (Exception e) {
            log.error("打开文件失败: {}", e.getMessage(), e);
        }
        excel.addHeader(headerList);

        // 数据提取
        while (lineIterator.hasNext()) {
            JsonNode x = JsonUtils.fromJson(OssLineSplit.validSplit(lineIterator.nextLine()));
            if (null == x) {
                continue;
            }
//        dataList.forEach(x -> {
            JsonNode jsonNode = x.get("rtInfo");
            if (null == jsonNode) {
                continue;
            }

            BmsRtInfo bms = JsonUtils.fromJson(jsonNode, new TypeReference<BmsRtInfo>() {
            });
            if (null == bms) {
                continue;
            }

            Optional<BmsBundleRtInfo> clusterRtData = bms.getStackInfoList().stream()
                .map(BmsStackRtInfo::getBundleList)
                .flatMap(Collection::stream)
                .filter(cluster -> dno.equals(cluster.getDno())).findFirst();
            if (clusterRtData.isPresent()) {
                BmsBundleRtInfo data = clusterRtData.get();

                ArrayList<Object> valList = new ArrayList<>();
                long ts = bms.getTs();
                valList.add(LocalDateTime.ofEpochSecond(
                        ts, 0, ZoneOffset.of(tz))
                    .format(FORMAT_yyyy_MM_dd_HH_mm_ss));

                List<StringVal> node1 = data.getTexts();
                int idx = 0;
                if (null != node1) {
                    Map<String, StringVal> nameMap = node1.stream()
                        .collect(Collectors.toMap(StringVal::getName, o -> o));

                    nodeHeaderList.get(idx++)
                        .forEach(name -> valList.add(nameMap.containsKey(name) ?
                            nameMap.get(name).getV() : "--"));
                }

                List<SignalVal> node2 = data.getSignals();
                if (null != node2) {
                    Map<String, SignalVal> nameMap = node2.stream()
                        .collect(Collectors.toMap(SignalVal::getName, o -> o));

                    nodeHeaderList.get(idx++)
                        .forEach(name -> valList.add(nameMap.containsKey(name) ?
                            nameMap.get(name).getV() : "--"));
                }

                List<RegisterRwValue> node3 = data.getCfgs();
                if (null != node3) {
                    Map<String, RegisterRwValue> nameMap = node3.stream()
                        .collect(Collectors.toMap(RegisterRwValue::getName, o -> o));

                    nodeHeaderList.get(idx++)
                        .forEach(name -> valList.add(nameMap.containsKey(name) ?
                            nameMap.get(name).getV() : "--"));
                }

                excel.appendData(valList);
            }
        }
    }

    public void batteryClusterExcelData(
        String localFile, LineIterator lineIterator, String dno, ExcelUtil excel) {
        // 请求头信息
        List<String> headerList = new ArrayList<>();
        final List<List<String>> nodeHeaderList = new ArrayList<>();
        try (ReversedLinesFileReader reader = new ReversedLinesFileReader(
            new File(localFile), Charset.defaultCharset())) {
            String line = reader.readLine();
            while (null != line) {
                BmsRtData bms = JsonUtils.fromJson(OssLineSplit.validSplit(line),
                    new TypeReference<BmsRtData>() {
                    });
                if (null == bms) {
                    line = reader.readLine();
                    continue;
                }

                Optional<BmsBundleRtData> clusterRtData = bms.getStackDataList().stream()
                    .map(BmsStackRtData::getBundleDataList)
                    .flatMap(Collection::stream)
                    .filter(x -> dno.equals(x.getDno())).findFirst();
                if (clusterRtData.isPresent()) {
                    BmsBundleRtData data = clusterRtData.get();
                    if (CollectionUtils.isNotEmpty(data.getSensors())) {
                        headerList.add("日期");

                        headerList.addAll(List.of("SOC", "SOH"));
                        headerList.addAll(List.of(
                            "单体最高电压", "单体最高电压簇号", "单体最高电压电池号",
                            "单体最高电压电芯号",
                            "单体最低电压", "单体最低电压簇号", "单体最低电压电池号",
                            "单体最低电压电芯号",
                            "单体最高温度", "单体最高温度簇号", "单体最高温度电池号",
                            "单体最高温度电芯号",
                            "单体最低温度", "单体最低温度簇号", "单体最低温度电池号",
                            "单体最低温度电芯号"));

                        List<String> sensorHeaders = new ArrayList<>();
                        data.getSensors().forEach(kv -> sensorHeaders.add(kv.getName()));
                        nodeHeaderList.add(sensorHeaders);

                        List<String> tempHeaders = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(data.getBatteryTemps())) {
                            for (int idx = 0; idx < data.getBatteryTemps().size(); idx++) {
                                tempHeaders.add("温度" + idx);
                            }
                            nodeHeaderList.add(tempHeaders);
                        }

                        List<String> vHeaders = new ArrayList<>();
                        if (CollectionUtils.isNotEmpty(data.getBatteryVoltages())) {
                            for (int idx = 0; idx < data.getBatteryVoltages().size(); idx++) {
                                vHeaders.add("电压" + idx);
                            }
                            nodeHeaderList.add(vHeaders);
                        }

                        nodeHeaderList.forEach(headerList::addAll);
                        break;
                    }
                }

                line = reader.readLine();
            }
        } catch (Exception e) {
            log.error("打开文件失败: {}", e.getMessage(), e);
        }
        excel.addHeader(headerList);

        batteryClusterExcelDataX(nodeHeaderList, lineIterator, dno, excel);
    }

    private void batteryClusterExcelDataX(
        List<List<String>> nodeHeaderList, LineIterator lineIterator, String dno, ExcelUtil excel) {
        while (lineIterator.hasNext()) {
            JsonNode jsonNode = JsonUtils.fromJson(
                OssLineSplit.validSplit(lineIterator.nextLine()));
            if (null == jsonNode) {
                continue;
            }
            ArrayList<Object> valList = new ArrayList<>();
            BmsRtData bms = JsonUtils.fromJson(jsonNode, new TypeReference<BmsRtData>() {
            });

            if (null != bms) {
                Optional<BmsBundleRtData> clusterRtData = bms.getStackDataList().stream()
                    .map(BmsStackRtData::getBundleDataList)
                    .flatMap(Collection::stream)
                    .filter(x -> dno.equals(x.getDno())).findFirst();
                if (clusterRtData.isPresent()) {
                    BmsBundleRtData data = clusterRtData.get();

                    valList.add(null != jsonNode.get("ldt") ?
                        jsonNode.get("ldt").asText() : "");

//                    SOC
                    if (null != data.getSoc()) {
                        valList.add(data.getSoc());
                    } else {
                        valList.add("--");
                    }

//                    SOH
                    if (null != data.getSoh()) {
                        valList.add(data.getSoh());
                    } else {
                        valList.add("--");
                    }

//                        单体最高电压	单体最高电压簇号	单体最高电压电池号 单体最高电压电芯号
                    if (null != data.getMaxBatteryVoltage()) {
                        valList.add(data.getMaxBatteryVoltage().getVal());
                        valList.add(data.getMaxBatteryVoltage().getBmuId());
                        valList.add(data.getMaxBatteryVoltage().getLmuId());
                        valList.add(data.getMaxBatteryVoltage().getInboxId());
                    } else {
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                    }
//                        单体最低电压	单体最低电压簇号	单体最低电压电池号	单体最低电压电芯号
                    if (null != data.getMinBatteryVoltage()) {
                        valList.add(data.getMinBatteryVoltage().getVal());
                        valList.add(data.getMinBatteryVoltage().getBmuId());
                        valList.add(data.getMinBatteryVoltage().getLmuId());
                        valList.add(data.getMinBatteryVoltage().getInboxId());
                    } else {
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                    }
//                        单体最高温度	单体最高温度簇号	单体最高温度电池号	单体最高温度电芯号
                    if (null != data.getMaxBatteryTemp()) {
                        valList.add(data.getMaxBatteryTemp().getVal());
                        valList.add(data.getMaxBatteryTemp().getBmuId());
                        valList.add(data.getMaxBatteryTemp().getLmuId());
                        valList.add(data.getMaxBatteryTemp().getInboxId());
                    } else {
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                    }
//                        单体最低温度	单体最低温度簇号	单体最低温度电池号	单体最低温度电芯号
                    if (null != data.getMinBatteryTemp()) {
                        valList.add(data.getMinBatteryTemp().getVal());
                        valList.add(data.getMinBatteryTemp().getBmuId());
                        valList.add(data.getMinBatteryTemp().getLmuId());
                        valList.add(data.getMinBatteryTemp().getInboxId());
                    } else {
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                        valList.add("--");
                    }

                    Map<String, EssBaseVal<BigDecimal>> nameMap = data.getSensors().stream()
                        .collect(Collectors.toMap(EssBaseVal::getName, o -> o));

                    nodeHeaderList.get(0).forEach(name -> valList.add(nameMap.containsKey(name) ?
                        nameMap.get(name).getV() : "--"));

                    // 温度
                    if (nodeHeaderList.size() > 1 &&
                        CollectionUtils.isNotEmpty(nodeHeaderList.get(1))) {
                        for (int idx = 0; idx < nodeHeaderList.get(1).size(); idx++) {
                            if (CollectionUtils.isNotEmpty(data.getBatteryTemps()) &&
                                idx < data.getBatteryTemps().size()) {
                                valList.add(data.getBatteryTemps().get(idx));
                            } else {
                                valList.add("--");
                            }
                        }
                    }

                    // 电压
                    if (nodeHeaderList.size() > 2 &&
                        CollectionUtils.isNotEmpty(nodeHeaderList.get(2))) {
                        for (int idx = 0; idx < nodeHeaderList.get(2).size(); idx++) {
                            if (CollectionUtils.isNotEmpty(data.getBatteryVoltages()) &&
                                idx < data.getBatteryVoltages().size()) {
                                valList.add(data.getBatteryVoltages().get(idx));
                            } else {
                                valList.add("--");
                            }
                        }
                    }

                    excel.appendData(valList);
                }
            }
        }
    }

}
