package com.cdz360.biz.bi.service;

import com.cdz360.biz.bi.mapper.DictMapper;
import com.chargerlinkcar.framework.common.domain.Dict;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class DictDs {
    @Autowired
    private DictMapper dictMapper;

    //
//    @Override
//    public Dict get(Long id) {
//        //获取数据库数据
//        Dict dict = dictMapper.get(id);
//        return dict;
//    }

    //
//    @Override
//    public List<Dict> findList(Dict dict) {
//        //获取数据库数据
//        List<Dict> dictList = dictMapper.findList(dict);
//        //设置缓存数据
//        return dictList;
//    }

    //
//
//    @Override
//    public int save(Dict dict) {
//        //保存数据库记录
//        return dictMapper.insert(dict);
//    }
//
//    //
////    @Override
//    public int update(Dict dict) {
//        return dictMapper.update(dict);
//    }
//
//    //
////    @Override
//    public void delete(Dict dict) {
//        //删除数据库记录
//        dictMapper.delete(dict);
//    }
//
//    //
////    @Override
//    public PaginationEntity queryPage(Dict dict) {
//        log.info("#####字典表分页查询:{}#####", JsonUtils.toJsonString(dict));
//        Page<Dict> pageInfo = PageHelper.startPage(dict.getIndex(), dict.getSize(), true, false, null);
//        //根据条件查询数据库
//        List<Dict> dictList = dictMapper.findList(dict);
//        return new PaginationEntity<>(dictList, pageInfo.getTotal());
//    }
//
//    //
////    @Override
//    public PictureConfig getPictureConfig() {
//        return dictMapper.getPictureConfig();
//    }

    //
//    @Override
    public List<Dict> findDictDataByType(String type) {
        List<Dict> dict = dictMapper.findDictDataByType(type);
        return dict;
    }
}
