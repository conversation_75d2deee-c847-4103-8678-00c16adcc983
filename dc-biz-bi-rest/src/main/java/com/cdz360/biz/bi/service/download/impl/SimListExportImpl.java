package com.cdz360.biz.bi.service.download.impl;

import com.cdz360.biz.bi.service.SimService;
import com.cdz360.biz.bi.service.download.IFileExport;
import com.cdz360.biz.model.sim.param.ListSimParam;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import java.io.IOException;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SimListExportImpl extends AbstractFileExport<ListSimParam, ExcelPosition>
    implements IFileExport<ListSimParam, ExcelPosition> {

    @Autowired
    private SimService simService;

    @PostConstruct
    public void init() {
        downloadFileProxy.addProxy(DownloadFunctionType.SIM_LIST, this);
    }

    @Override
    public Class<ListSimParam> paramClazz() {
        return ListSimParam.class;
    }

    @Override
    public void genFile(String context, String pos) throws IOException {
        this.simService.exportSimListExcel(this.convertPos(pos), this.convert(context));
    }
}
