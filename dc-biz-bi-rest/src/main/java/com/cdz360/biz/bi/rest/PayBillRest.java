package com.cdz360.biz.bi.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.biz.bi.service.PayBillService;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.bill.param.ListZftThirdOrderParam;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.model.trading.order.param.ZftBillParam;
import com.chargerlinkcar.framework.common.utils.LoggerHelper;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

import jakarta.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @since 2019/11/8 14:43
 */
@Slf4j
@RestController
@Tag(name = "充值信息查询接口", description = "充值查询")
public class PayBillRest {

//    private static final String PAY_BILL_FLAG = "PayBill";

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMdd");

    @Autowired
    private PayBillService payBillService;

    private static String excelSubDir() {
        return DATE_FORMAT.format(new Date());
    }

    /**
     * 充值信息列表excel导出
     *
     * @param request
     * @param payBillParam
     * @return
     */
    @Operation(summary = "充值信息列表excel导出")
    @PostMapping("/api/paybill/exportExcel")
    public ObjectResponse<ExcelPosition> exportExcelCus(
        HttpServletRequest request,
        @RequestBody PayBillParam payBillParam) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param={}", payBillParam);
        throw new DcServiceException("接口已废弃，请联系开发");
//        // excel生成位置信息
//        ExcelPosition position = new ExcelPosition();
//        position.setSubDir(excelSubDir())
//                .setSubFileName(UUIDUtils.getUuid32());
//
//        // 异步调用
//        payBillService.exportExcelCus(position, payBillParam);
//        return new ObjectResponse<>(position);
    }

    /**
     * 充值信息列表excel导出
     *
     * @param request
     * @param payBillParam
     * @return
     */
    @Operation(summary = "充值信息列表excel导出(运营管理导出使用)")
    @PostMapping("/api/paybill/exportExcelManager")
    public ObjectResponse<ExcelPosition> exportExcelManager(
        HttpServletRequest request,
        @RequestBody PayBillParam payBillParam) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param={}", payBillParam);
        throw new DcServiceException("接口已废弃，请联系开发");

//        // excel生成位置信息
//        ExcelPosition position = new ExcelPosition();
//        position.setSubDir(excelSubDir())
//                .setSubFileName(UUIDUtils.getUuid32());
//
//        // 异步调用
//        payBillService.exportExcelManager(position, payBillParam);
//        return new ObjectResponse<>(position);
    }

    /**
     * 直付通订单导出
     *
     * @param request
     * @param payBillParam
     * @return
     */
    @Operation(summary = "直付通列表excel导出(运营管理导出使用)")
    @PostMapping("/api/paybill/exportZftList")
    public ObjectResponse<ExcelPosition> exportZftList(
        HttpServletRequest request,
        @RequestBody ZftBillParam payBillParam) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param={}", payBillParam);
        throw new DcServiceException("接口已废弃，请联系开发");
//        // excel生成位置信息
//        ExcelPosition position = new ExcelPosition();
//        position.setSubDir(excelSubDir())
//                .setSubFileName(UUIDUtils.getUuid32());
//
//        // 异步调用
//        payBillService.exportZftList(position, payBillParam);
//        return new ObjectResponse<>(position);
    }

    @Operation(summary = "对账管理中的对账订单导出", description = "充电管理平台")
    @PostMapping("/api/paybill/exportZftThirdOrderList")
    public Mono<ObjectResponse<ExcelPosition>> exportZftThirdOrderList(
        HttpServletRequest request,
        @RequestBody ListZftThirdOrderParam param) {
        log.info("对账管理中的对账订单导出: {}, param = {}",
            LoggerHelper.formatEnterLog(request, false), param);
        throw new DcServiceException("接口已废弃，请联系开发");
//        // excel生成位置信息
//        ExcelPosition position = new ExcelPosition();
//        position.setSubDir(excelSubDir())
//                .setSubFileName(UUIDUtils.getUuid32());
//
//        // 异步调用
//        payBillService.exportZftThirdOrderList(position, param);
//        return Mono.just(position)
//                .map(RestUtils::buildObjectResponse);
    }

}
