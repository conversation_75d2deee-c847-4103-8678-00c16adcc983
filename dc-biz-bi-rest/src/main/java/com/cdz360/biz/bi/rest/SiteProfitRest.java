package com.cdz360.biz.bi.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.bi.service.site.SiteProfitBiService;
import com.cdz360.biz.bi.service.accounting.SiteDailyProfitAccountingService;
import com.cdz360.biz.model.cus.settlement.vo.SettlementVo;
import com.cdz360.biz.model.trading.bi.dto.SiteAccountProfitDto;
import com.cdz360.biz.model.trading.bi.dto.SiteProfitDto;
import com.cdz360.biz.model.trading.bi.param.AccountSiteIncomeParam;
import com.cdz360.biz.model.trading.site.po.BiSiteGcDailyPo;
import com.chargerlinkcar.framework.common.utils.LoggerHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Nullable;
import jakarta.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/bi/site/profit")
@Tag(name = "场站收益相关的统计接口", description = "场站收益统计")
public class SiteProfitRest {

    @Autowired
    private SiteProfitBiService siteProfitBiService;

    @Autowired
    private SiteDailyProfitAccountingService siteDailyProfitAccountingService;

    @Operation(summary = "按月统计场站收入")
    @PostMapping("/accountSiteIncome")
    public ObjectResponse<SiteProfitDto> accountSiteIncome(HttpServletRequest request,
                                                           @RequestBody AccountSiteIncomeParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param = {}", param);
        var ret = siteProfitBiService.accountSiteIncome(param);
        return RestUtils.buildObjectResponse(ret);
    }


    @Operation(summary = "通过后付费账单统计场站月度收入")
    @PostMapping("/accountSitePostPayCorpIncome")
    public ListResponse<SiteAccountProfitDto> accountSitePostPayCorpIncome(HttpServletRequest request,
                                                                           @RequestBody SettlementVo sett) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " sett = {}", sett);
        var ret = siteProfitBiService.accountSitePostPayCorpIncome(sett);
        return RestUtils.buildListResponse(ret);
    }

    @Operation(summary = "计算(国充)场站每日运营收入数据")
    @PostMapping("/accountSiteDailyIncome")
    public ObjectResponse<BiSiteGcDailyPo> accountSiteDailyIncome(HttpServletRequest request,
                                                                  @RequestParam(value = "siteId") String siteId,
                                                                  @RequestParam(value = "date", required = false) @Nullable String date) {
        log.info(LoggerHelper.formatEnterLog(request, true));
        var ret = siteDailyProfitAccountingService.accountSiteDailyIncome(siteId, date);
        return RestUtils.buildObjectResponse(ret);
    }

}
