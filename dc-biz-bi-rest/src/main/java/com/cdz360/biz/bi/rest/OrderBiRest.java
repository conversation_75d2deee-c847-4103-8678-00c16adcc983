package com.cdz360.biz.bi.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.bi.service.OrderBiService;
import com.cdz360.biz.bi.service.site.SiteBiService;
import com.cdz360.biz.bi.service.site.SiteChargeBiService;
import com.cdz360.biz.bi.service.site.SiteOrderBiService;
import com.cdz360.biz.model.bi.site.ChargeFee;
import com.cdz360.biz.model.bi.site.OrderCount;
import com.cdz360.biz.model.bi.site.OrderElecDivision;
import com.cdz360.biz.model.bi.site.PlugErrorCount;
import com.cdz360.biz.model.bi.site.PlugUtilization;
import com.cdz360.biz.model.bi.site.SiteErrorCount;
import com.cdz360.biz.model.bi.site.SiteUtilization;
import com.cdz360.biz.model.bi.site.UserOrderCount;
import com.cdz360.biz.model.bi.site.UserOrderElec;
import com.cdz360.biz.model.bi.site.UserOrderFee;
import com.cdz360.biz.model.iot.param.SiteBiParam;
import com.cdz360.biz.model.iot.param.SiteBiTopParam;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import com.cdz360.biz.model.trading.bi.dto.ChargerOrderBiDto;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.bi.param.ListBiCommercialParam;
import com.cdz360.biz.model.trading.bi.param.ListBiSiteParam;
import com.cdz360.biz.model.trading.bi.param.ListChargeOrderBiByVinParam;
import com.cdz360.biz.model.trading.bi.param.WarningBiParam;
import com.cdz360.biz.model.trading.bi.vo.CommercialBiVo;
import com.cdz360.biz.model.trading.bi.vo.SiteBiVo;
import com.cdz360.biz.model.trading.bi.vo.VinBiVo;
import com.cdz360.biz.model.trading.cus.vo.CusOrderBiVo;
import com.cdz360.biz.model.trading.order.dto.GeoOrderBiDto;
import com.cdz360.biz.model.trading.order.dto.OrderStartTypeBiDto;
import com.cdz360.biz.model.trading.order.vo.ChargeOrderBiVo;
import com.cdz360.biz.model.trading.site.vo.SiteOrderAccountData;
import com.cdz360.biz.model.trading.site.vo.SiteOrderBiVo;
import com.cdz360.biz.model.trading.site.vo.TimePowerBiVo;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDataBiVo;
import com.chargerlinkcar.framework.common.feign.DeviceMonitorFeignClient;
import com.chargerlinkcar.framework.common.utils.LoggerHelper;
import com.chargerlinkcar.framework.common.utils.UUIDUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import jakarta.servlet.http.HttpServletRequest;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/bi/order")
@Tag(name = "管理平台-报表相关接口", description = "管理平台-报表相关接口")
public class OrderBiRest {

    @Autowired
    private OrderBiService orderBiService;


    @Autowired
    private SiteBiService siteBiService;

    @Autowired
    private SiteChargeBiService siteChargeBiService;

    @Autowired
    private SiteOrderBiService siteOrderBiService;

    @Autowired
    private DeviceMonitorFeignClient deviceMonitorFeignClient;


    @Operation(summary = "根据车辆统计充电数据(用于企业平台)")
    @PostMapping(value = "/getBiListByVin", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListByVin(
        HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        ListResponse<ChargerOrderBiDto> res = orderBiService.getBiListResponseByVin(param);
//        log.info("res: {}", JsonUtils.toJsonString(res));
        return res;
    }

    @Operation(summary = "根据车辆统计充电数据(用于企业平台),修改后")
    @PostMapping(value = "/getBiOrderListByVin", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiOrderListByVin(
        HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info("企业平台按照VIN码统计订单信息，param = {}",
            JsonUtils.toJsonString(param));
        return orderBiService.getBiOrderListByVin(param);
    }

    @Operation(summary = "根据车辆统计充电数据详情(用于企业平台)")
    @PostMapping(value = "/getBiListDetailByVin", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListDetailByVin(
        HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        return orderBiService.getBiListDetailResponseByVin(param);
    }

    @Operation(summary = "导出车辆充电数据(用于企业平台)")
    @PostMapping(value = "/exportBiListByVin")
    public ObjectResponse<ExcelPosition> exportBiListByVin(HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        Long total = orderBiService.getBiListByVinCount(param);
        if (total > 100000) {
            throw new DcServiceException("数据量超过10万条,无法导出");
        }
        ExcelPosition position = new ExcelPosition();
        position.setSubFileName(UUIDUtils.getUuid32());
        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
        log.info("exportExcelTrade 启动,position:{}", JsonUtils.toJsonString(position));
        param.setExcelPosition(position);
        orderBiService.exportBiListByVin(param);
        return new ObjectResponse<>(position);
    }

    @Operation(summary = "根据企业客户统计充电数据(用于充电平台)")
    @PostMapping(value = "/getBiListByCorpCus", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListByCorpCus(HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        ListResponse<ChargerOrderBiDto> res = orderBiService.getBiListResponseByCorpCus(param);
//        log.info("res: {}", JsonUtils.toJsonString(res));
        return res;
    }

    @Operation(summary = "根据企业客户统计充电数据详情(用于充电平台)")
    @PostMapping(value = "/getBiListDetailByCorpCus", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListDetailByCorpCus(HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        return orderBiService.getBiListDetailResponseByCorpCus(param);
    }

    @Operation(summary = "导出企业客户充电数据(用于充电平台)")
    @PostMapping(value = "/exportBiListByCorpCus", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ObjectResponse<ExcelPosition> exportBiListByCorpCus(HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        throw new DcServiceException("接口已废弃，请联系开发");

//        Long total = orderBiService.getBiListByCorpCusCount(param);
//        if (total > 100000) {
//            throw new DcServiceException("数据量超过10万条,无法导出");
//        }
//        ExcelPosition position = new ExcelPosition();
//        position.setSubFileName(UUIDUtils.getUuid32());
//        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
//        log.info("exportExcelTrade 启动,position:{}", JsonUtils.toJsonString(position));
//        param.setExcelPosition(position);
//        orderBiService.exportBiListByCorpCus(param);
//        return new ObjectResponse<>(position);
    }

    @Operation(summary = "根据企业授信客户统计充电数据(用于企业平台)")
    @PostMapping(value = "/getBiListByCorpCreditCus", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListByCorpCreditCus(HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        ListResponse<ChargerOrderBiDto> res = orderBiService.getBiListResponseByCorpCreditCus(
            param);
//        log.info("res: {}", JsonUtils.toJsonString(res));
        return res;
    }

    @Operation(summary = "根据企业授信客户统计充电数据(用于企业平台)")
    @PostMapping(value = "/getBiListDetailByCorpCreditCus", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListDetailByCorpCreditCus(
        HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        return orderBiService.getBiListDetailResponseByCorpCreditCus(param);
    }

    @Operation(summary = "导出企业授信客户充电数据(用于企业平台)")
    @PostMapping(value = "/exportBiListByCorpCreditCus", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ObjectResponse<ExcelPosition> exportBiListByCorpCreditCus(HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        Long total = orderBiService.getBiListByCorpCreditCusCount(param);
        if (total > 100000) {
            throw new DcServiceException("数据量超过10万条,无法导出");
        }
        ExcelPosition position = new ExcelPosition();
        position.setSubFileName(UUIDUtils.getUuid32());
        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
        log.info("exportExcelTrade 启动,position:{}", JsonUtils.toJsonString(position));
        param.setExcelPosition(position);
        orderBiService.exportBiListByCorpCreditCus(param);
        return new ObjectResponse<>(position);
    }

    @Operation(summary = "根据在线卡统计充电数据(用于充电平台)")
    @PostMapping(value = "/getBiListByOnlineCard", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListByOnlineCard(HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        ListResponse<ChargerOrderBiDto> res = orderBiService.getBiListResponseByOnlineCard(param);
//        log.info("res: {}", JsonUtils.toJsonString(res));
        return res;
    }

    @Operation(summary = "根据在线卡统计充电数据详情(用于充电平台)")
    @PostMapping(value = "/getBiListDetailByOnlineCard", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListDetailByOnlineCard(HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        return orderBiService.getBiListDetailResponseByOnlineCard(param);
    }

    @Operation(summary = "导出在线卡充电数据(用于充电平台)")
    @PostMapping(value = "/exportBiListByOnlineCard", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ObjectResponse<ExcelPosition> exportBiListByOnlineCard(HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        throw new DcServiceException("接口已废弃，请联系开发");

//        Long total = orderBiService.getBiListByOnlineCardCount(param);
//        if (total > 100000) {
//            throw new DcServiceException("数据量超过10万条,无法导出");
//        }
//        ExcelPosition position = new ExcelPosition();
//        position.setSubFileName(UUIDUtils.getUuid32());
//        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
//        log.info("exportExcelTrade 启动,position:{}", JsonUtils.toJsonString(position));
//        param.setExcelPosition(position);
//        orderBiService.exportBiListByOnlineCard(param);
//        return new ObjectResponse<>(position);
    }

    @Operation(summary = "根据在线卡统计充电数据(用于企业平台)")
    @PostMapping(value = "/getBiListByOnlineCardOnCorp", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListByOnlineCardOnCorp(HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        ListResponse<ChargerOrderBiDto> res = orderBiService.getBiListResponseByOnlineCardOnCorp(
            param);
//        log.info("res: {}", JsonUtils.toJsonString(res));
        return res;
    }

    @Operation(summary = "根据在线卡统计充电数据详情(用于企业平台)")
    @PostMapping(value = "/getBiListDetailByOnlineCardOnCorp", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListDetailByOnlineCardOnCorp(
        HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        return orderBiService.getBiListDetailResponseByOnlineCardOnCorp(param);
    }

    @Operation(summary = "导出在线卡充电数据(用于企业平台)")
    @PostMapping(value = "/exportBiListByOnlineCardOnCorp", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ObjectResponse<ExcelPosition> exportBiListByOnlineCardOnCorp(HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        Long total = orderBiService.getBiListByOnlineCardOnCorpCount(param);
        if (total > 100000) {
            throw new DcServiceException("数据量超过10万条,无法导出");
        }
        ExcelPosition position = new ExcelPosition();
        position.setSubFileName(UUIDUtils.getUuid32());
        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
        log.info("exportExcelTrade 启动,position:{}", JsonUtils.toJsonString(position));
        param.setExcelPosition(position);
        orderBiService.exportBiListByOnlineCardOnCorp(param);
        return new ObjectResponse<>(position);
    }

    @Operation(summary = "根据紧急卡统计充电数据(用于充电平台)")
    @PostMapping(value = "/getBiListByEmergencyCard", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListByEmergencyCard(HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        ListResponse<ChargerOrderBiDto> res = orderBiService.getBiListResponseByEmergencyCard(
            param);
//        log.info("res: {}", JsonUtils.toJsonString(res));
        return res;
    }

    @Operation(summary = "导出紧急卡充电数据(用于充电平台)")
    @PostMapping(value = "/exportBiListByEmergencyCard", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ObjectResponse<ExcelPosition> exportBiListByEmergencyCard(HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        throw new DcServiceException("接口已废弃，请联系开发");
//        Long total = orderBiService.getBiListByEmergencyCardCount(param);
//        if (total > 100000) {
//            throw new DcServiceException("数据量超过10万条,无法导出");
//        }
//        ExcelPosition position = new ExcelPosition();
//        position.setSubFileName(UUIDUtils.getUuid32());
//        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
//        log.info("exportExcelTrade 启动,position:{}", JsonUtils.toJsonString(position));
//        param.setExcelPosition(position);
//        orderBiService.exportBiListResponseByEmergencyCard(param);
//        return new ObjectResponse<>(position);
    }

    @Operation(summary = "根据组织统计充电数据(用于企业平台)")
    @PostMapping(value = "/getBiListByOrg", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListByOrg(HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        ListResponse<ChargerOrderBiDto> res = orderBiService.getBiListResponseByOrg(param);
//        log.info("res: {}", JsonUtils.toJsonString(res));
        return res;
    }

    @Operation(summary = "根据组织统计充电数据(用于企业平台)")
    @PostMapping(value = "/getBiListDetailByOrg", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargerOrderBiDto> getBiListDetailByOrg(HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        return orderBiService.getBiListDetailResponseByOrg(param);
    }

    @Operation(summary = "导出组织充电数据(用于企业平台)")
    @PostMapping(value = "/exportBiListByOrg", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ObjectResponse<ExcelPosition> exportBiListByOrg(HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        Long total = orderBiService.getBiListByOrgCount(param);
        if (total > 100000) {
            throw new DcServiceException("数据量超过10万条,无法导出");
        }
        ExcelPosition position = new ExcelPosition();
        position.setSubFileName(UUIDUtils.getUuid32());
        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
        log.info("exportExcelTrade 启动,position:{}", JsonUtils.toJsonString(position));
        param.setExcelPosition(position);
        orderBiService.exportBiListResponseByOrg(param);
        return new ObjectResponse<>(position);
    }

    @Operation(summary = "根据条件导出告警列表")
    @PostMapping(value = "/exportWarningBiList", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ObjectResponse<ExcelPosition> exportWarningBiList(HttpServletRequest request,
        @RequestBody WarningBiParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param = {}",
            JsonUtils.toJsonString(param));
        throw new DcServiceException("接口已废弃，请联系开发");

//        // 首先检查数据量是否超过10万条
//        BaseResponse response = deviceMonitorFeignClient.checkWarningBiListCount(param);
//        FeignResponseValidate.check(response);
//
//        ExcelPosition position = new ExcelPosition();
//        position.setSubFileName(UUIDUtils.getUuid32());
//        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
//        log.info("exportExcelTrade 启动,position:{}", JsonUtils.toJsonString(position));
//
//        orderBiService.exportWarningBiList(param, position);
//        return new ObjectResponse<>(position);
    }

    @PostMapping(value = "/orderCountBi", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<OrderCount> orderCountBi(@RequestBody SiteBiParam siteBiParam) {
        log.info("查询场站订单量信息: {}", JsonUtils.toJsonString(siteBiParam));
        List<OrderCount> result = siteOrderBiService.siteCountBi(siteBiParam);
        return RestUtils.buildListResponse(result);
    }

    @PostMapping(value = "/userOrderCountBi", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<UserOrderCount> userOrderCountBi(
        @RequestBody SiteBiTopParam siteBiTopParam) {
        log.info("客户订单数排行榜获取: {}", JsonUtils.toJsonString(siteBiTopParam));
        return orderBiService.getUserOrderCountBi(siteBiTopParam);
    }

    /**
     * 充电消费柱状数据获取
     *
     * @param siteBiParam
     * @return
     */
    @PostMapping(value = "/chargeFeeBi", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<ChargeFee> chargeFeeBi(@RequestBody SiteBiParam siteBiParam) {
        log.info("查询场站充电消费信息: {}", JsonUtils.toJsonString(siteBiParam));
        List<ChargeFee> result = siteChargeBiService.siteChargeBi(siteBiParam);
        return RestUtils.buildListResponse(result);
    }

    /**
     * 充电消费排行榜
     *
     * @param siteBiTopParam
     * @return
     */
    @PostMapping(value = "/userChargeFeeBi", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ListResponse<UserOrderFee> userChargeFeeBi(@RequestBody SiteBiTopParam siteBiTopParam) {
        log.info("客户充电消费排行榜获取: {}", JsonUtils.toJsonString(siteBiTopParam));
        return orderBiService.getUserOrderChargeBi(siteBiTopParam);
    }

    /**
     * 统计场站的分时电量
     *
     * @param request
     * @param param
     * @return
     */
    @PostMapping(value = "/chargeDivisionBi")
    public ListResponse<OrderElecDivision> chargeDivisionBi(
        HttpServletRequest request,
        @RequestBody SiteBiParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) +
            " param = " + JsonUtils.toJsonString(param));
        List<OrderElecDivision> result = siteBiService.chargeDivisionBi(param);
        log.info("size = {}", result.size());
        return RestUtils.buildListResponse(result);
    }

    /**
     * 该场站用户充电排行榜
     *
     * @param request
     * @param param
     * @return
     */
    @PostMapping(value = "/userChargeDivisionBi")
    public ListResponse<UserOrderElec> userChargeDivisionBi(
        HttpServletRequest request,
        @RequestBody SiteBiTopParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) +
            " param = " + JsonUtils.toJsonString(param));
        List<UserOrderElec> result = siteBiService.userChargeDivisionBi(param);
        log.info("size = {}", result.size());
        return RestUtils.buildListResponse(result);
    }

    /**
     * 桩时长利用率
     *
     * @param request
     * @param param
     * @return
     */
    @PostMapping(value = "/utilizationBi")
    public ListResponse<SiteUtilization> utilizationBi(
        HttpServletRequest request,
        @RequestBody SiteBiParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) +
            " param = " + JsonUtils.toJsonString(param));
        List<SiteUtilization> result = siteBiService.utilizationBi(param);
        log.info("size = {}", result.size());
        return RestUtils.buildListResponse(result);
    }

    /**
     * 场站桩利用率排行榜
     *
     * @param request
     * @param param
     * @return
     */
    @PostMapping(value = "/plugUtilizationBi")
    public ListResponse<PlugUtilization> plugUtilizationBi(
        HttpServletRequest request,
        @RequestBody SiteBiTopParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) +
            " param = " + JsonUtils.toJsonString(param));
        List<PlugUtilization> result = siteBiService.plugUtilizationBi(param);
        log.info("size = {}", result.size());
        return RestUtils.buildListResponse(result);
    }

    /**
     * 枪故障次数折线图数据获取
     *
     * @param request
     * @param param
     * @return
     */
    @PostMapping(value = "/breakdownBi")
    public ListResponse<SiteErrorCount> breakdownBi(
        HttpServletRequest request,
        @RequestBody SiteBiParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) +
            " param = " + JsonUtils.toJsonString(param));
        List<SiteErrorCount> result = siteBiService.breakdownBi(param);
        log.info("size = {}", result.size());
        return RestUtils.buildListResponse(result);
    }

    /**
     * 枪故障次数折线图数据获取
     *
     * @param request
     * @param param
     * @return
     */
    @PostMapping(value = "/commSite/breakdownBi")
    public ListResponse<SiteErrorCount> commEssSiteBreakdownBi(
        HttpServletRequest request, @RequestBody SiteBiParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) +
            " param = " + JsonUtils.toJsonString(param));
        List<SiteErrorCount> result = siteBiService.commEssSiteBreakdownBi(param);
        log.info("size = {}", result.size());
        return RestUtils.buildListResponse(result);
    }

    /**
     * 枪故障次数排行榜获取
     *
     * @param request
     * @param param
     * @return
     */
    @PostMapping(value = "/plugBreakdownBi")
    public ListResponse<PlugErrorCount> plugBreakdownBi(
        HttpServletRequest request,
        @RequestBody SiteBiTopParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) +
            " param = " + JsonUtils.toJsonString(param));
        List<PlugErrorCount> result = siteBiService.plugBreakdownBi(param);
        log.info("size = {}", result.size());
        return RestUtils.buildListResponse(result);
    }


    /**
     * 获取历史(总)充电统计数据
     *
     * @param commIdChain
     * @return
     */
    @PostMapping("/getOrderBi")
    public Mono<ObjectResponse<ChargeOrderBiVo>> getOrderBi(
        @RequestParam(required = false) String commIdChain,
        @RequestParam(required = false) String siteId,
        @RequestParam(required = false) List<Integer> bizTypeList) {
        return this.orderBiService.getOrderBi(commIdChain, siteId, bizTypeList)
            .map(RestUtils::buildObjectResponse);
    }

    /**
     * 查询最近几天(自然日)的时间汇总功率统计
     *
     * @param commIdChain
     * @param lastDays    1,查今天; 2,查昨天开始; 3,查前天开始
     * @return
     */
    @GetMapping("/getTimePowerBiList")
    public ListResponse<TimePowerBiVo> getTimePowerBiList(
        @RequestParam(required = false) String commIdChain,
        @RequestParam(value = "siteId", required = false) String siteId,
        @RequestParam(value = "lastDays") int lastDays) {
        var bi = this.siteOrderBiService.getTimePowerBiList(commIdChain, siteId, lastDays);
        return RestUtils.buildListResponse(bi);
    }


    @PostMapping("/getOrderStartTypeBiList7")
    @Operation(summary = "获取过去7天的充电启动方式统计")
    public ListResponse<OrderStartTypeBiDto> getOrderStartTypeBiList7(
        @RequestParam(required = false) Long topCommId,
        @RequestParam(required = false) String commIdChain) {
        var list = this.orderBiService.getOrderStartTypeBiList7(topCommId, commIdChain);
        return RestUtils.buildListResponse(list);
    }


    @PostMapping("/getActiveCusCount7")
    @Operation(summary = "过去7天的活跃用户数统计")
    public ObjectResponse<Long> getActiveCusCount7(@RequestParam(required = false) Long topCommId,
        @RequestParam(required = false) String commIdChain) {
        var count = this.orderBiService.getActiveCusCount7(topCommId, commIdChain);
        log.debug("过去7天的活跃用户数 = {}", count);
        return RestUtils.buildObjectResponse(count);
    }


    @PostMapping("/getTimeGroupingOrderBiList")
    @Operation(summary = "按时间分组统计")
    public ListResponse<SiteOrderBiVo> getTimeGroupingOrderBiList(
        @RequestParam(value = "timeType", required = false) SiteBiSampleType timeType,
        @RequestParam(value = "siteId", required = false) String siteId,
        @RequestParam(value = "commIdChain", required = false) String commIdChain) {
        var list = this.orderBiService.getTimeGroupingOrderBiList(timeType, siteId, commIdChain);
        return RestUtils.buildListResponse(list);
    }


    @PostMapping("/getDateGroupingPlugUsageBiList")
    @Operation(summary = "按日期分组统计枪头使用量数据")
    public ListResponse<SiteUtilization> getDateGroupingPlugUsageBiList(@RequestParam int days,
        @RequestParam(required = false) String commIdChain) {
        var list = this.orderBiService.getDateGroupingPlugUsageBiList(days, commIdChain);
        return RestUtils.buildListResponse(list);
    }

    @PostMapping("/getAccTypeGroupingFeeBiList")
    @Operation(summary = "按客户类型统计7/30日充电订单")
    public ObjectResponse<CusOrderBiVo> getAccTypeGroupingFeeBiList(
        @RequestParam(required = false) String commIdChain) {
        var result = this.orderBiService.getAccTypeGroupingFeeBiList(commIdChain);
        return RestUtils.buildObjectResponse(result);
    }


    @PostMapping("/getGeoOrderBi")
    @Operation(summary = "统计城市的今天,本周,本月,今年充电数据")
    public ObjectResponse<GeoOrderBiDto> getGeoOrderBi(
        @RequestParam(required = false) String provinceCode,
        @RequestParam(required = false) String cityCode,
        @RequestParam(required = false) String siteId,
        @RequestParam(required = false) String commIdChain) {
        var city = this.orderBiService.getGeoOrderBi(provinceCode, cityCode, siteId, commIdChain);
        return RestUtils.buildObjectResponse(city);
    }

    @PostMapping("/getVinBi")
    @Operation(summary = "车辆(VIN)订单汇总")
    public ListResponse<VinBiVo> getVinBi(
        HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false)
            + " param = {}", JsonUtils.toJsonString(param));
        return this.orderBiService.getVinBi(param);
    }

    @PostMapping("/getVinBiDetail")
    @Operation(summary = "车辆(VIN)订单汇总详情")
    public ListResponse<VinBiVo> getVinBiDetail(
        HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false)
            + " param = {}", JsonUtils.toJsonString(param));
        return this.orderBiService.getVinBiDetail(param);
    }

    @Operation(summary = "导出车辆(VIN)订单汇总")
    @PostMapping(value = "/exportVinBi", consumes = MediaType.APPLICATION_JSON_VALUE)
    public ObjectResponse<ExcelPosition> exportVinBi(
        HttpServletRequest request,
        @RequestBody ListChargeOrderBiByVinParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false)
            + " param = {}", JsonUtils.toJsonString(param));
        throw new DcServiceException("接口已废弃，请联系开发");

//        Long total = this.orderBiService.countVin(param);
//        if (total > 100000) {
//            throw new DcServiceException("数据量超过10万条,无法导出");
//        }

//        ExcelPosition position = new ExcelPosition();
//        position.setSubFileName(UUIDUtils.getUuid32());
//        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
//        log.info("excel position:{}", JsonUtils.toJsonString(position));
//
//        param.setExcelPosition(position);
//        orderBiService.exportVinBi(param);
//        return new ObjectResponse<>(position);
    }

    @Operation(summary = "商户充电订单汇总")
    @PostMapping(value = "/getCommBi")
    public ListResponse<CommercialBiVo> getCommBi(
        HttpServletRequest request, @RequestBody ListBiCommercialParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false)
            + " param = {}", JsonUtils.toJsonString(param));
        ListResponse<CommercialBiVo> commBi = this.orderBiService.getCommBi(param);
        log.debug("log : result = {}", commBi.getData().size());
        return commBi;
    }

//    @Operation(summary = "导出商户充电订单汇总")
//    @PostMapping(value = "/exportCommBi", consumes = MediaType.APPLICATION_JSON_VALUE)
//    public ObjectResponse<ExcelPosition> exportCommBi(
//            HttpServletRequest request,
//            @RequestBody ListBiCommercialParam param) {
//        log.info(LoggerHelper.formatEnterLog(request, false)
//                + " param = {}", JsonUtils.toJsonString(param));
//        throw new DcServiceException("接口已废弃，请联系开发");
////        Long total = this.orderBiService.countCommercial(param);
////        if (total > 100000) {
////            throw new DcServiceException("数据量超过10万条,无法导出");
////        }
//
////        ExcelPosition position = new ExcelPosition();
////        position.setSubFileName(UUIDUtils.getUuid32());
////        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
////        log.info("excel position:{}", JsonUtils.toJsonString(position));
////
////        param.setExcelPosition(position);
////        orderBiService.exportCommBi(param);
////        return new ObjectResponse<>(position);
//    }

    @Operation(summary = "场站充电订单汇总")
    @PostMapping(value = "/getSiteBi")
    public ListResponse<SiteBiVo> getSiteBi(
        HttpServletRequest request, @RequestBody ListBiSiteParam param) {
        log.info(LoggerHelper.formatEnterLog(request, false) +
            " param = {}", JsonUtils.toJsonString(param));
        return this.orderBiService.getSiteBi(param);
    }

//    @Operation(summary = "导出场站充电订单汇总")
//    @PostMapping(value = "/exportSiteBi", consumes = MediaType.APPLICATION_JSON_VALUE)
//    public ObjectResponse<ExcelPosition> exportSiteBi(
//            HttpServletRequest request,
//            @RequestBody ListBiSiteParam param) {
//        log.info(LoggerHelper.formatEnterLog(request, false)
//                + " param = {}", JsonUtils.toJsonString(param));
//        throw new DcServiceException("接口已废弃，请联系开发");
//
////        Long total = this.orderBiService.countSite(param);
////        if (total > 100000) {
////            throw new DcServiceException("数据量超过10万条,无法导出");
////        }
//
////        ExcelPosition position = new ExcelPosition();
////        position.setSubFileName(UUIDUtils.getUuid32());
////        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
////        log.info("excel position:{}", JsonUtils.toJsonString(position));
////
////        param.setExcelPosition(position);
////        orderBiService.exportSiteBi(param);
////        return new ObjectResponse<>(position);
//    }

    @Operation(summary = "近n天充电用户统计(按上传时间统计，不含当天)")
    @GetMapping(value = "/orderAccountBi")
    public Mono<ListResponse<SiteOrderAccountData>> getOrderAccountBi(
        @RequestParam(value = "days") Integer days,
        @RequestParam(value = "siteId", required = false) String siteId) {
        log.debug("充电用户统计: days = {}, siteId = {}", days, siteId);
        return orderBiService.getOrderAccountBi(days, siteId);
    }

    @Operation(summary = "查询订单信息")
    @PostMapping(value = "/getChargerOrderDetailData")
    public Mono<ObjectResponse<ChargerOrderDataBiVo>> getChargerOrderDetailData(
        @RequestBody ChargerOrderParam chargerOrderParam) {
        log.debug("查询订单信息: chargerOrderParam = {}", chargerOrderParam);
        return orderBiService.getChargerOrderDetailData(chargerOrderParam);
    }

    @Operation(summary = "查询场站当前功率")
    @PostMapping(value = "/getCurrentPowerBySiteIds")
    public ObjectResponse<Map<String, BigDecimal>> getCurrentPowerBySiteIds(
        @RequestBody ListBiSiteParam param
    ) {
        log.debug("查询场站当前功率的场站列表 = {}", param.getSiteIdList());
        return orderBiService.getCurrentPowerBySiteIds(param.getSiteIdList())
            .block(Duration.ofSeconds(50L));
    }
}
