package com.cdz360.biz.bi.feign;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.user.po.SysUserPo;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * Created by Administrator on 2018/12/12.
 */
@Slf4j
@Component
public class HystrixAuthCenterClientFactory implements FallbackFactory<AuthCenterFeignClient> {

    @Override
    public AuthCenterFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_AUTH,
            throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_AUTH,
            throwable.getStackTrace());

        return new AuthCenterFeignClient() {

            @Override
            public ObjectResponse<JsonNode> getCurrentUser(String token) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SysUserVo> querySysUserByIds(List<Long> idList) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SysUserVo> findByName(String name, Boolean accurateQuery) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<SysUserPo> getByUserNameAndPlatform(String username,
                AppClientType platform) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<CorpPo> getCorp(Long corpId) {
                return RestUtils.serverBusy4ObjectResponse();
            }
        };
    }
}
