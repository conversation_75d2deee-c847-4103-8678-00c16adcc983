package com.cdz360.biz.bi.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.chargerlinkcar.framework.common.domain.invoice.InvoicedRecordVo;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceInfoParam;
import com.chargerlinkcar.framework.common.domain.invoice.param.ListInvoicedRecordParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceInfoVo;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedModelVo;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedTemplateSalDetailVo;
import com.chargerlinkcar.framework.common.domain.invoice.vo.InvoicedTemplateSalVo;
import java.util.List;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;


@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_INVOICE, fallbackFactory = InvoiceFeignClientHystrixFactory.class)
public interface InvoiceFeignClient {

    @GetMapping("/api/invoice/getInvoicedTempSal")
    ObjectResponse<InvoicedTemplateSalVo> getInvoicedTempSal(
            @RequestParam(value = "commId") Long commId,
            @RequestParam(value = "saleTin") String saleTin);

    @PostMapping(value = "/api/invoice/getCorpInvoiceDetail")
    ObjectResponse<CorpInvoiceInfoVo> getCorpInvoiceDetail(@RequestBody CorpInvoiceInfoParam param);


    @GetMapping(value = "/api/invoiced-temp-sal-dtlV4/byInvoiceType")
    ListResponse<InvoicedTemplateSalDetailVo> getInvoicedTemplateSalDetailsByInvoiceType(
            @RequestParam(value = "invoiceType") String invoiceType,
            @RequestParam(value = "commercialId") Long commercialId,
            @RequestParam(value = "page") int page,
            @RequestParam(value = "size") int size);



    @RequestMapping(value = "/api/invoiced-recordsV4/signForbiddenExportToInvoice", method = RequestMethod.POST)
    BaseResponse signForbiddenExportToInvoice(
            @RequestBody List<String> orderNoList, @RequestParam(value = "token") String token);

    @PostMapping(value = "/api/invoiced-recordsV4")
    ListResponse<InvoicedRecordVo> getAllInvoicedRecords(@RequestBody ListInvoicedRecordParam param);

    @GetMapping(value = "/api/invoiced-records-userV4")
    ListResponse<InvoicedRecordVo> getAllInvoicedRecordsByUserId(
            @RequestParam(value = "userId") Long userId,
            @RequestParam(value = "page") int page,
            @RequestParam(value = "size") int size);

    @RequestMapping(value = "/api/invoiced-recordsV4/getInvoicedRecordsByUserId", method = RequestMethod.GET)
    ListResponse<InvoicedRecordVo> getAllInvoicedRecordsByUserIdAndStatus(@RequestParam(value = "userId") Long userId,
                                                                          @RequestParam(value = "invoicedStatus") String invoicedStatus,
                                                                          @RequestParam(value = "page") int page,
                                                                          @RequestParam(value = "size") int size);





    @RequestMapping(value = "/api/invoiced-modelsV4/getDefaultInvoicedModel", method = RequestMethod.GET)
    ObjectResponse<InvoicedModelVo> getDefaultInvoicedModel(@RequestParam(value = "userId") Long userId);




}
