package com.cdz360.biz.bi.service;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.bi.config.ExportFileConfig;
import com.cdz360.biz.ds.trading.ro.rover.ds.SiteRoverRoDs;
import com.cdz360.biz.model.trading.bi.dto.BiExportGroups;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.rover.param.RoverSearchParam;
import com.cdz360.biz.model.trading.rover.vo.ExportRoverRecordVo;
import com.cdz360.biz.model.trading.rover.vo.SiteRoverVo;
import com.chargerlinkcar.framework.common.utils.ExcelUtil;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * RoverService
 *
 * @since 8/1/2022 3:11 PM
 * <AUTHOR>
 */
@Slf4j
@Service
public class RoverService {

    @Autowired
    private SiteRoverRoDs siteRoverRoDs;

    @Autowired
    private ExportFileConfig exportFileConfig;


    public void exportRoverList(RoverSearchParam param, ExcelPosition position) {
        log.info("调试工单列表导出到EXCEL: {}", JsonUtils.toJsonString(position));
        try {
            boolean empty = false;
            BiFunction<Integer, Integer, List<Serializable>> fetch = empty ?
                (start, size) -> List.of() : (start, size) -> {
                param.setStart(((long) (start - 1) * size))
                    .setSize(size);
                param.setTotal(Boolean.FALSE);

                final List<SiteRoverVo> siteRoverVos = siteRoverRoDs.searchRoverList(param);
                return new ArrayList<>(siteRoverVos);
            };

            ExcelUtil.builder(exportFileConfig.getExcelDir(), position,
                    BiExportGroups.ROVER_LIST.getName())
                .addHeader(ExportRoverRecordVo.class)
                .loopAppendData(fetch, list -> new ArrayList<>(list.stream()
                    .map(i -> {
                        SiteRoverVo recordVo = (SiteRoverVo) i;
                        ExportRoverRecordVo ret = new ExportRoverRecordVo();
                        BeanUtils.copyProperties(recordVo, ret);
                        ret.setStatus(
                            recordVo.getStatus() == null ? "--" : recordVo.getStatus().getDesc());
                        return ret;
                    })
                    .collect(Collectors.toList())))
                .write2File();
            log.info("导出excel完成: {}", JsonUtils.toJsonString(position));
        } catch (Exception e) {
            log.error("导出excel异常: {}, err = {}",
                JsonUtils.toJsonString(position), e.getMessage(), e);
        }
    }
}