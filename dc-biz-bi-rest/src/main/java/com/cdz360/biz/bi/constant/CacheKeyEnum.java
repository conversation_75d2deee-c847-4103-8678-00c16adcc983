//package com.cdz360.biz.bi.constant;
//
//
///**
// * <AUTHOR>
// * 缓存对象的key值对应的枚举，使用缓存时必须在此声明key，然后通过key获取value
// * 如果不设置timeout，默认不超时
// */
//public enum CacheKeyEnum {
//    /**
//     * token——用户信息关联，set存储，key为token，value为用户{@link com.mamcharge.user.demo.domain.single.AppUser}
//     */
//    TOKEN_REL_USER_INFO("mammoth_user:", "token_rel_user_info:", 24 * 60 * 60);
//
//    /**
//     * key值前缀
//     */
//    private String prefix;
//    /**
//     * key值
//     */
//    private String key;
//    /**
//     * 超时时间（秒），默认永久有效
//     */
//    private Integer timeout;
//
//    CacheKeyEnum(String key) {
//        this.key = key;
//    }
//
//    CacheKeyEnum(String prefix, String key) {
//        this.prefix = prefix;
//        this.key = key;
//    }
//
//    CacheKeyEnum(String key, Integer timeout) {
//        this.key = key;
//        this.timeout = timeout;
//    }
//
//    CacheKeyEnum(String prefix, String key, Integer timeout) {
//        this.prefix = prefix;
//        this.key = key;
//        this.timeout = timeout;
//    }
//
//    public String getKeyAndPrefix() {
//        if (prefix == null) {
//            return key;
//        }
//        return prefix + key;
//    }
//
//    public void setPrefix(String prefix) {
//        this.prefix = prefix;
//    }
//
//    public void setKey(String key) {
//        this.key = key;
//    }
//
//    public Integer getTimeout() {
//        return timeout;
//    }
//
//    public void setTimeout(Integer timeout) {
//        this.timeout = timeout;
//    }
//}
