package com.cdz360.biz.bi.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.model.ess.vo.EssEquipVo;
import com.cdz360.biz.model.iot.param.ListCtrlParam;
import com.cdz360.biz.model.sim.param.ListSimParam;
import com.cdz360.biz.model.sim.vo.SimVo;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, fallbackFactory = DeviceMgmFeignHystrix.class)
public interface DeviceMgmFeignClient {

    // 获取SIM卡信息
    @PostMapping(value = "/device/mgm/sim/getList")
    ListResponse<SimVo> getSimList(@RequestBody ListSimParam param);


    @Operation(summary = "获取光储Ess下挂载的所有设备")
    @PostMapping(value = "/device/mgm/ess/findEquipList")
    ListResponse<EssEquipVo> findEquipList(@RequestBody ListCtrlParam param);

}
