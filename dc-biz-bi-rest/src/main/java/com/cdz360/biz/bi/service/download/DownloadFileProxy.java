package com.cdz360.biz.bi.service.download;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.biz.bi.service.BalanceApplicationBiService;
import com.cdz360.biz.bi.service.CardExportService;
import com.cdz360.biz.bi.service.ExcelBiService;
import com.cdz360.biz.bi.service.ExcelExportService;
import com.cdz360.biz.bi.service.ExcelFileService;
import com.cdz360.biz.bi.service.InvoiceService;
import com.cdz360.biz.bi.service.OaService;
import com.cdz360.biz.bi.service.OrderBiService;
import com.cdz360.biz.bi.service.PayBillService;
import com.cdz360.biz.bi.service.PdfService;
import com.cdz360.biz.bi.service.PrerunService;
import com.cdz360.biz.bi.service.RoverService;
import com.cdz360.biz.bi.service.SimService;
import com.cdz360.biz.bi.service.YwOrderService;
import com.cdz360.biz.bi.service.ess.EssBiService;
import com.cdz360.biz.ds.cus.ro.balance.param.BalanceApplicationParam;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.bi.site.ExcelBiPosition;
import com.cdz360.biz.model.cus.vin.param.VinSearchParam;
import com.cdz360.biz.model.download.dto.DownloadJobDto;
import com.cdz360.biz.model.download.param.GenerateResult;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.download.type.DownloadJobStatus;
import com.cdz360.biz.model.iot.param.MeterRecordBiParam;
import com.cdz360.biz.model.iot.param.SiteBiParam;
import com.cdz360.biz.model.trading.bi.param.ListBiCommercialParam;
import com.cdz360.biz.model.trading.bi.param.ListBiSiteParam;
import com.cdz360.biz.model.trading.bi.param.ListChargeOrderBiByVinParam;
import com.cdz360.biz.model.trading.bi.param.WarningBiParam;
import com.cdz360.biz.model.trading.bill.param.ListZftThirdOrderParam;
import com.cdz360.biz.model.trading.coupon.param.CouponSearchParam;
import com.cdz360.biz.model.trading.invoice.param.ListCorpInvoiceRecordParam;
import com.cdz360.biz.model.trading.iot.param.PvProfitTrendParam;
import com.cdz360.biz.model.trading.meter.param.MeterDataListParam;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.model.trading.order.param.ZftBillParam;
import com.cdz360.biz.model.trading.prerun.param.PrerunSearchParam;
import com.cdz360.biz.model.trading.rover.param.RoverSearchParam;
import com.cdz360.biz.model.trading.site.param.ListOvertimeParkFeeOrderParam;
import com.cdz360.biz.model.trading.site.param.RecordParam;
import com.cdz360.biz.model.trading.yw.param.ListYwOrderParam;
import com.cdz360.biz.oa.param.ListTaskParam;
import com.cdz360.biz.utils.feign.download.DownloadJobDataCoreClient;
import com.chargerlinkcar.framework.common.domain.invoice.param.ListInvoicedRecordParam;
import com.chargerlinkcar.framework.common.domain.param.CardSearchParam;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DownloadFileProxy {

    private Map<DownloadFunctionType, IFileExport<?, ?>> PROXY_FACTORY = new HashMap<>();

    @Autowired
    private ExcelExportService excelExportService;

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private PdfService pdfService;

    @Autowired
    private PrerunService prerunService;

    @Autowired
    private RoverService roverService;

    @Autowired
    private ExcelBiService excelBiService;

    @Autowired
    private ExcelFileService excelFileService;

    @Autowired
    private YwOrderService ywOrderService;

    @Autowired
    private EssBiService essBiService;

    @Autowired
    private PayBillService payBillService;

    @Autowired
    private OrderBiService orderBiService;

    @Autowired
    private BalanceApplicationBiService balanceApplicationBiService;

    @Autowired
    private CardExportService cardExportService;

    @Autowired
    private DownloadJobDataCoreClient downloadJobDataCoreClient;

    @Autowired
    private SimService simService;

    @Autowired
    private OaService oaService;

    @Autowired
    private MessageSource messageSource;

    public synchronized void addProxy(DownloadFunctionType type, IFileExport<?, ?> export) {
        this.PROXY_FACTORY.put(type, export);
    }

    // FIXME: 代码实现需要调整
    // 调整成获取指定 spring 注入对象执行
    public void generate(DownloadJobDto downloadJobDto) {
        DownloadFunctionType functionMap = downloadJobDto.getFunctionMap();
        String reqParam = downloadJobDto.getReqParam();
        String filePosition = downloadJobDto.getFilePosition();
        ExcelPosition pos = null;
        DownloadJobStatus result = DownloadJobStatus.COMPLETED;
        try {
            switch (functionMap) {
                case VIN_ORDER:
                    ChargerOrderParam carOrderParam = JsonUtils.fromJson(reqParam,
                        ChargerOrderParam.class);
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    carOrderParam.setExcelPosition(pos);
                    excelFileService.exportVinOrderList(carOrderParam);
                    break;
                case ESS_BI_TREND:
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    SiteBiParam essTrendParam = JsonUtils.fromJson(reqParam, SiteBiParam.class);
                    essBiService.exportSiteEssBiExcel(pos, essTrendParam);
                    break;
                case PV_POWER_PROFIT_TREND:
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    PvProfitTrendParam pvTrendParam = JsonUtils.fromJson(reqParam,
                        PvProfitTrendParam.class);
                    ywOrderService.exportPowerProfitTrendExcel(pos, pvTrendParam);
                    break;
                case SETTLEMENT_ORDER:
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    excelFileService.writeTempExcelByBillNo(
                        JsonUtils.fromJson(reqParam).get("billNo").asText(), pos);
                    break;
                case CORP_INVOICE_ORDER:
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    invoiceService.exportCorpInvoiceOrder(
                        pos, JsonUtils.fromJson(reqParam).get("applyNo").asText());
                    break;
                case VIN_RECORD:
                    VinSearchParam vinParam = JsonUtils.fromJson(reqParam, VinSearchParam.class);
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    cardExportService.exportVinForManage(pos, vinParam);
                    break;
                case COMM_USER_ORDER_RECORD:
                    ChargerOrderParam searchParam = JsonUtils.fromJson(reqParam,
                        ChargerOrderParam.class);
                    searchParam.setExcelPosition(
                        JsonUtils.fromJson(filePosition, ExcelPosition.class));
                    searchParam.setSheetName("商户会员订单");
                    excelFileService.exportCommUserOrderList(searchParam);
                    break;
                case CARD_RECORD:
                    CardSearchParam cardParam = JsonUtils.fromJson(reqParam, CardSearchParam.class);
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    cardExportService.exportCardForManage(cardParam, pos);
                    break;
                case ACCOUNT_BALANCE_APPLY:
                    BalanceApplicationParam applyParam = JsonUtils.fromJson(reqParam,
                        BalanceApplicationParam.class);
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    balanceApplicationBiService.exportExcel(pos, applyParam);
                    break;
                case INVOICED_RECORD:
                    ListInvoicedRecordParam recParam = JsonUtils.fromJson(reqParam,
                        ListInvoicedRecordParam.class);
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    invoiceService.exportInvoicedRecord(pos, recParam);
                    break;
                case NSR_INVOICED_RECORD:
                    ListInvoicedRecordParam nsrRecParam = JsonUtils.fromJson(reqParam,
                        ListInvoicedRecordParam.class);
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    invoiceService.exportCusInvoiceNSR(pos, nsrRecParam);
                    break;
                case CORP_INVOICED_RECORD:
                    ListCorpInvoiceRecordParam corpRecParam = JsonUtils.fromJson(reqParam,
                        ListCorpInvoiceRecordParam.class);
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    invoiceService.exportCorpInvoiceRecord(pos, corpRecParam);
                    break;
                case PAY_BILL_RECORD:
                    PayBillParam payBillParam = JsonUtils.fromJson(reqParam, PayBillParam.class);
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    payBillService.exportExcelManager(pos, payBillParam);
                    break;
                case PAY_BILL_RECORD_CUS:
                    payBillParam = JsonUtils.fromJson(reqParam, PayBillParam.class);
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    payBillService.exportExcelCus(pos, payBillParam);
                    break;
                case FEE_BI_CORP_TOTAL:
                    SiteBiParam feeCorpParam = JsonUtils.fromJson(reqParam, SiteBiParam.class);
                    feeCorpParam.setExcelPosition(
                        JsonUtils.fromJson(filePosition, ExcelBiPosition.class));
                    feeCorpParam.setSheetName("企业充电走势消费汇总");
                    excelBiService.writeTempFeeBySiteOrCommList(feeCorpParam, 1);
                    break;
                case KWH_BI_CORP_TOTAL:
                    SiteBiParam kwhCorpParam = JsonUtils.fromJson(reqParam, SiteBiParam.class);
                    kwhCorpParam.setExcelPosition(
                        JsonUtils.fromJson(filePosition, ExcelBiPosition.class));
                    kwhCorpParam.setSheetName("企业充电走势电量汇总");
                    excelBiService.writeTempElectBySiteOrCommList(kwhCorpParam, 1);
                    break;
                case KWH_BI_CORP:
                    kwhCorpParam = JsonUtils.fromJson(reqParam, SiteBiParam.class);
                    kwhCorpParam.setExcelPosition(
                        JsonUtils.fromJson(filePosition, ExcelBiPosition.class));
                    kwhCorpParam.setSheetName("企业汇总");
                    excelBiService.writeTempExcelByCorpList(kwhCorpParam);
                    break;
                case FEE_BI_SITE_TOTAL:
                    SiteBiParam feeSiteParam = JsonUtils.fromJson(reqParam, SiteBiParam.class);
                    feeSiteParam.setExcelPosition(
                        JsonUtils.fromJson(filePosition, ExcelBiPosition.class));
                    feeSiteParam.setSheetName("场站充电走势消费汇总");
                    excelBiService.writeTempFeeBySiteOrCommList(feeSiteParam, 0);
                    break;
                case KWH_BI_SITE_TOTAL:
                    SiteBiParam kwhSiteParam = JsonUtils.fromJson(reqParam, SiteBiParam.class);
                    kwhSiteParam.setExcelPosition(
                        JsonUtils.fromJson(filePosition, ExcelBiPosition.class));
                    kwhSiteParam.setSheetName("场站充电走势电量汇总");
                    excelBiService.writeTempElectBySiteOrCommList(kwhSiteParam, 0);
                    break;
                case KWH_BI_SITE:
                    kwhSiteParam = JsonUtils.fromJson(reqParam, SiteBiParam.class);
                    kwhSiteParam.setExcelPosition(
                        JsonUtils.fromJson(filePosition, ExcelBiPosition.class));
                    kwhSiteParam.setSheetName("场站汇总");
                    excelBiService.writeTempExcelBySiteList(kwhSiteParam);
                    break;
                case ORDER_DATA_BI_COMM:
                    ListBiCommercialParam commParam = JsonUtils.fromJson(reqParam,
                        ListBiCommercialParam.class);
                    commParam.setExcelPosition(
                        JsonUtils.fromJson(filePosition, ExcelPosition.class));
                    this.orderBiService.exportCommBi(commParam);
                    break;
                case ORDER_DATA_BI_SITE:
                    ListBiSiteParam siteParam = JsonUtils.fromJson(reqParam, ListBiSiteParam.class);
                    siteParam.setExcelPosition(
                        JsonUtils.fromJson(filePosition, ExcelPosition.class));
                    this.orderBiService.exportSiteBi(siteParam);
                    break;
                case ORDER_DATA_BI_VIN:
                    ListChargeOrderBiByVinParam vinOrderParam = JsonUtils.fromJson(reqParam,
                        ListChargeOrderBiByVinParam.class);
                    vinOrderParam.setExcelPosition(
                        JsonUtils.fromJson(filePosition, ExcelPosition.class));
                    this.orderBiService.exportVinBi(vinOrderParam);
                    break;
                case ORDER_DATA_BI_CORP:
                    ListChargeOrderBiByVinParam corpOrderParam = JsonUtils.fromJson(reqParam,
                        ListChargeOrderBiByVinParam.class);
                    corpOrderParam.setExcelPosition(
                        JsonUtils.fromJson(filePosition, ExcelPosition.class));
                    orderBiService.exportBiListByCorpCus(corpOrderParam);
                    break;
                case ORDER_DATA_BI_ONLINE_CARD:
                    ListChargeOrderBiByVinParam onlineCardOrderParam = JsonUtils.fromJson(reqParam,
                        ListChargeOrderBiByVinParam.class);
                    onlineCardOrderParam.setExcelPosition(
                        JsonUtils.fromJson(filePosition, ExcelPosition.class));
                    orderBiService.exportBiListByOnlineCard(onlineCardOrderParam);
                    break;
                case ORDER_DATA_BI_EMERGENCY_CARD:
                    ListChargeOrderBiByVinParam emergencyCardOrderParam = JsonUtils.fromJson(
                        reqParam, ListChargeOrderBiByVinParam.class);
                    emergencyCardOrderParam.setExcelPosition(
                        JsonUtils.fromJson(filePosition, ExcelPosition.class));
                    this.orderBiService.exportBiListResponseByEmergencyCard(
                        emergencyCardOrderParam);
                    break;
                case WARNING_DETAIL:
                    WarningBiParam warningParam = JsonUtils.fromJson(reqParam,
                        WarningBiParam.class);
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    this.orderBiService.exportWarningBiList(warningParam, pos);
                    break;
                case PAY_BILL_ZFT_THIRD:
                    ListZftThirdOrderParam thirdOrderParam = JsonUtils.fromJson(reqParam,
                        ListZftThirdOrderParam.class);
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    this.payBillService.exportZftThirdOrderList(pos, thirdOrderParam);
                    break;
                case PAY_BILL_ZFT:
                    ZftBillParam zftBillParam = JsonUtils.fromJson(reqParam, ZftBillParam.class);
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    this.payBillService.exportZftList(pos, zftBillParam);
                    break;
                case XJ_ORDER_DETAIL:
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    this.pdfService.recordExport(
                        JsonUtils.fromJson(reqParam).get("recordId").asLong(), pos);
                    break;
                case XJ_ORDER:
                    RecordParam xjParam = JsonUtils.fromJson(reqParam, RecordParam.class);
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    this.ywOrderService.exportInspectionRecExcel(pos, xjParam);
                    break;
                case YW_ORDER_DETAIL:
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    this.pdfService.exportYwOrder(
                        JsonUtils.fromJson(reqParam).get("ywOrderNo").asText(), pos);
                    break;
                case PRERUN_DETAIL:
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    this.pdfService.exportPrerunDetail(
                        JsonUtils.fromJson(reqParam).get("prerunId").asLong(), pos);
                    break;
                case CORP_INVOICE_DETAIL:
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    this.pdfService.exportCorpInvoiceDetail(
                        JsonUtils.fromJson(reqParam).get("applyNo").asText(), pos);
                    break;
//                case OA_DETAIL:
//                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
//                    log.info("pos={}", pos);
//                    this.oaService.exportOaDetail(
//                        JsonUtils.fromJson(reqParam).get("procInstId").asText(),
//                        JsonUtils.fromJson(reqParam).get("sysUid").asText(), pos);
//                    break;
                case PRERUN_LIST:
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    this.prerunService.exportPrerunList(
                        JsonUtils.fromJson(JsonUtils.fromJson(reqParam).get("param").asText(),
                            PrerunSearchParam.class), pos);
                    break;
                case ROVER_DETAIL:
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    this.pdfService.exportRoverDetail(
                        JsonUtils.fromJson(reqParam).get("roverId").asLong(), pos);
                    break;
                case ROVER_LIST:
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    this.roverService.exportRoverList(
                        JsonUtils.fromJson(JsonUtils.fromJson(reqParam).get("param").asText(),
                            RoverSearchParam.class), pos);
                    break;
                case OA_LIST:
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    this.oaService.exportOaList(
                        JsonUtils.fromJson(JsonUtils.fromJson(reqParam),
                            ListTaskParam.class), pos);
                    break;
                case YW_ORDER:
                    ListYwOrderParam ywParam = JsonUtils.fromJson(reqParam, ListYwOrderParam.class);
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    this.ywOrderService.exportYwOrderExcel(pos, ywParam);
                    break;
                case SITE_METER_BI:
                    MeterRecordBiParam meterParam = JsonUtils.fromJson(reqParam,
                        MeterRecordBiParam.class);
                    meterParam.setExcelPosition(
                        JsonUtils.fromJson(filePosition, ExcelBiPosition.class));
                    meterParam.setSheetName("抄表统计");
                    this.excelBiService.exportBiSiteMeterRecordSync(meterParam);
                    break;
                case OVERTIME_PARK_FEE_ORDER:
                    ListOvertimeParkFeeOrderParam param = JsonUtils.fromJson(reqParam,
                        ListOvertimeParkFeeOrderParam.class);
                    param.setExcelPosition(JsonUtils.fromJson(filePosition, ExcelPosition.class));
                    this.excelExportService.exportOvertimeParkFeeOrder(param);
                    break;
                case CHARGER_ORDER:
                    ChargerOrderParam orderParam = JsonUtils.fromJson(reqParam,
                        ChargerOrderParam.class);
                    orderParam.setExcelPosition(
                        JsonUtils.fromJson(filePosition, ExcelPosition.class));
                    String taskId = orderParam.getExcelPosition().getSubFileName();
                    if (NumberUtils.equals(23, orderParam.getPlatform())) { //企业账户导出
                        orderParam.setSheetName("订单汇总");
                        excelFileService.writeTempExcelByChargeOrderForCompany(taskId, orderParam);
                    } else if (NumberUtils.equals(21, orderParam.getPlatform())) { //管理平台导出
//                        orderParam.setSheetName("订单明细");
                        orderParam.setSheetName(null == orderParam.getLocale() ? "订单明细"
                            : messageSource.getMessage("订单明细", null, orderParam.getLocale()));
                        excelFileService.writeTempExcelByChargeOrderList(taskId, orderParam);
                    } else {
                        log.warn("不支持的请求 platform: {}", orderParam.getPlatform());
                    }
                    break;
                case USER_COUPON_LIST:
                    CouponSearchParam couponSearchParam = JsonUtils.fromJson(reqParam,
                        CouponSearchParam.class);
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    this.excelExportService.exportUserCouponList(couponSearchParam, pos);
                    break;
                case SITE_METER_DATA_LIST:
                    MeterDataListParam meterDataListParam = JsonUtils.fromJson(reqParam,
                        MeterDataListParam.class);
                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
                    this.excelExportService.exportSiteMeterDataList(meterDataListParam, pos);
                    break;
//                case SIM_LIST:
//                    ListSimParam simParam = JsonUtils.fromJson(reqParam, ListSimParam.class);
//                    pos = JsonUtils.fromJson(filePosition, ExcelPosition.class);
//                    this.simService.exportSimListExcel(pos, simParam);
//                    break;
                default:
                    // FIXME: 新添加的下载需要参考新的实现方式，有疑问找@Nathan
                    if (PROXY_FACTORY.containsKey(functionMap)) {
                        PROXY_FACTORY.get(functionMap).genFile(reqParam, filePosition);
                    } else {
                        log.error("不识别下载类型: {}", functionMap);
                        result = DownloadJobStatus.CANCEL;
                    }
            }
        } catch (Exception e) {
            log.error("下载文件生成异常: err = {}", e.getMessage(), e);
            result = DownloadJobStatus.PROCESSING_FAIL;
        }

        // 反馈
        downloadJobDataCoreClient.notifyGenerateResult(
                new GenerateResult().setId(downloadJobDto.getId()).setStatus(result))
            .subscribe();
    }
}
