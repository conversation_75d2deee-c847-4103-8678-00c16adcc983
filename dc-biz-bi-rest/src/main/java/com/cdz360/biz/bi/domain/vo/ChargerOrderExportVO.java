package com.cdz360.biz.bi.domain.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 *  订单列表导出VO
 * @since 2016年9月18日 下午2:36:25
 */
@Data
public class ChargerOrderExportVO implements Serializable {

    /**
     * 订单号
     **/
    @ExcelField(title = "订单编号", sort = 1)
    private String orderNo;

    /**
     * 关联账单号
     **/
    @ExcelField(title = "关联账单号", sort = 1)
    private String billNo;

    /**
     * 互联互通订单号
     **/
    @ExcelField(title = "互联互通订单号", sort = 2)
    private String openOrderId;

    /**
     * 支付单号
     **/
    @ExcelField(title = "支付单号", sort = 2)
    private String payOrderNo;

    /**
     * 退款单号
     **/
    @ExcelField(title = "退款单号", sort = 2)
    private String refundOrderNo;

    // 互联互通类型: 0,非互联互通订单; 1,正向互联互通; 2,反向互联互通"
    @ExcelField(title = "互联互通类型", sort = 2)
    private String hlhtType;

    /**
     * 订单状态
     **/
    @ExcelField(title = "订单状态", sort = 3)
//    private String statusName;
    private String orderStatus;

    /**
     * 创建时间
     **/
    @ExcelField(title = "创建时间", sort = 4)
    private String createTime;

    /**
     * 手机号
     **/
    @ExcelField(title = "手机号", sort = 5)
//    private String phone;
    private String mobilePhone;

    @ExcelField(title = "邮箱", sort = 5)
    private String email;

    /**
     * 客户名
     **/
    @ExcelField(title = "客户名", sort = 5)
//    private String phone;
    private String customerName;

    //5 扣款账户
    @ExcelField(title = "扣款账户", sort = 6)
    private String payAccountName;

    //6 所属集团客户
    @ExcelField(title = "所属企业客户", sort = 7)
    private String blocUserName;

    /**
     * 订单来源
     **/
    @ExcelField(title = "来源", sort = 8)
//    private String channelName;
    private String source;

    /**
     * 启动方式 newFeild
     **/
    @ExcelField(title = "启动方式", sort = 9)
//    private String orderTypeName;
    private String orderType;

    /**
     * 充电卡号
     **/
    @ExcelField(title = "卡号", sort = 10)
//    private String cardNo;
    private String cardChipNo;

    //9 卡类型 newFeild
    @ExcelField(title = "卡类型", sort = 11)
//    private String cardTypeName;
    private String cardType;

    //9 卡名称
    @ExcelField(title = "卡名称", sort = 12)
    private String cardName;

    /**
     * 车辆VIN码
     **/
    @ExcelField(title = "VIN码", sort = 13)
    private String vin;

    /**
     * 车队名称
     */
    @ExcelField(title = "车队名称", sort = 14)
    private String carDepart;

    /**
     * 车牌号
     */
    @ExcelField(title = "车牌号", sort = 15)
    private String carNo;

    /**
     * 线路
     */
    @ExcelField(title = "线路", sort = 16)
    private String lineNum;

    //12 车辆自编号
    @ExcelField(title = "车辆自编号", sort = 17)
    private String carNum;

    @ExcelField(title = "品牌", sort = 17)
    private String brand;

    @ExcelField(title = "型号", sort = 17)
    private String model;

    //    @ExcelField(title = "车长", sort = 17)
//    private BigDecimal carLength;
//
//    @ExcelField(title = "年份", sort = 17)
//    private String year;
//
    @ExcelField(title = "辅电(V)", sort = 17)
    private Integer bmsVoltage;

    @ExcelField(title = "国标", sort = 17)
    private String gbVer;

    //13 所属商户
    @ExcelField(title = "所属商户", sort = 18)
    private String commercialFullName;

    /**
     * 充电站名称
     **/
    @ExcelField(title = "站点名称", sort = 19)
    private String stationName;

    /**
     * 站点编号
     **/
    @ExcelField(title = "站点编号", sort = 19)
    private String siteNo;

    //15 电桩编号
    @ExcelField(title = "电桩编号", sort = 20)
    private String boxCode;

    //16 电桩名称
    @ExcelField(title = "电桩名称", sort = 21)
    private String evseName;

    @ExcelField(title = "电桩类型", sort = 21)
    private String supplyType;

    @ExcelField(title = "枪头编号", sort = 22)
    private String plugNo;
    /**
     * 枪头名称
     **/
    @ExcelField(title = "枪头名称", sort = 22)
    private String chargerName;

    //18 枪头标识
    @ExcelField(title = "枪头标识", sort = 23)
    private String connectorId;

    /**
     * 充电开始时间
     **/
    @ExcelField(title = "充电开始时间", sort = 24)
    private String chargeStartTime;
    /**
     * 充电结束时间
     **/
    @ExcelField(title = "充电结束时间", sort = 25)
    private String chargeEndTime;
    /**
     * 上传时间
     **/
    @ExcelField(title = "上传时间", sort = 26)
    private String stopTime;
    /**
     * 订单时长
     **/
    @ExcelField(title = "订单时长", sort = 27)
    private String duration;

    /**
     * SOC
     **/
//    @ExcelField(title = "SOC", sort = 28)
//    private String socField;

    @ExcelField(title = "充电前SOC(%)", sort = 28)
    private Long startSoc;

    @ExcelField(title = "充电后SOC(%)", sort = 28)
    private Long stopSoc;

    //23 结束原因
    @ExcelField(title = "停充原因", sort = 29)
    private String stopReason;

    //23 异常原因 newFeild
    @ExcelField(title = "异常原因", sort = 30)
//    private String abnormalName;
    private String abnormal;

    @ExcelField(title = "尖时电量", sort = 31, digits = 4)
    private BigDecimal tipElectricity;//
    @ExcelField(title = "尖时服务费", sort = 32, digits = 4)
    private BigDecimal tipServicePrice;//
    @ExcelField(title = "尖时服务费单价", sort = 32, digits = 4)
    private BigDecimal tipServiceUnit;
    @ExcelField(title = "尖时电费", sort = 33, digits = 4)
    private BigDecimal tipElecPrice;//
    @ExcelField(title = "尖时电费单价", sort = 33, digits = 4)
    private BigDecimal tipElectricUnit;//
    @ExcelField(title = "尖时合计费用", sort = 34, digits = 4)
    private BigDecimal tipSumPrice;//
    @ExcelField(title = "峰时电量", sort = 35, digits = 4)
    private BigDecimal peakElectricity;//
    @ExcelField(title = "峰时服务费", sort = 36, digits = 4)
    private BigDecimal peakServicePrice;//
    @ExcelField(title = "峰时服务费单价", sort = 36, digits = 4)
    private BigDecimal peakServiceUnit;
    @ExcelField(title = "峰时电费", sort = 37, digits = 4)
    private BigDecimal peakElecPrice;//
    @ExcelField(title = "峰时电费单价", sort = 37, digits = 4)
    private BigDecimal peakElectricUnit;
    @ExcelField(title = "峰时合计费用", sort = 38, digits = 4)
    private BigDecimal peakSumPrice;//
    @ExcelField(title = "平时电量", sort = 39, digits = 4)
    private BigDecimal flatElectricity;//
    @ExcelField(title = "平时服务费", sort = 40, digits = 4)
    private BigDecimal flatServicePrice;//
    @ExcelField(title = "平时服务费单价", sort = 40, digits = 4)
    private BigDecimal flatServiceUnit;
    @ExcelField(title = "平时电费", sort = 41, digits = 4)
    private BigDecimal flatElecPrice;//
    @ExcelField(title = "平时电费单价", sort = 41, digits = 4)
    private BigDecimal flatElectricUnit;
    @ExcelField(title = "平时合计费用", sort = 42, digits = 4)
    private BigDecimal flatSumPrice;//
    @ExcelField(title = "谷时电量", sort = 43, digits = 4)
    private BigDecimal valleyElectricity;
    @ExcelField(title = "谷时服务费", sort = 44, digits = 4)
    private BigDecimal valleyServicePrice;
    @ExcelField(title = "谷时服务费单价", sort = 44, digits = 4)
    private BigDecimal valleyServiceUnit;
    @ExcelField(title = "谷时电费", sort = 45, digits = 4)
    private BigDecimal valleyElecPrice;
    @ExcelField(title = "谷时电费单价", sort = 45, digits = 4)
    private BigDecimal valleyElectricUnit;
    @ExcelField(title = "谷时合计费用", sort = 46, digits = 4)
    private BigDecimal valleySumPrice;

    /**
     * 订单电量
     **/
    @ExcelField(title = "总电量（kWh）", sort = 47, digits = 4)
//    private Double orderElectricity;
    private BigDecimal orderElectricity;

    @ExcelField(title = "标准服务费（元）", sort = 48, digits = 4)
//    private Double servicePrice;
    private BigDecimal servOriginFee;
    /**
     * 总服务费
     */
    @ExcelField(title = "总服务费（元）", sort = 48, digits = 4)
//    private Double servicePrice;
    private BigDecimal servicePrice;

    @ExcelField(title = "标准电费（元）", sort = 49, digits = 4)
//    private Double elecPrice;
    private BigDecimal elecOriginFee;
    /**
     * 电费
     */
    @ExcelField(title = "总电费（元）", sort = 49, digits = 4)
//    private Double elecPrice;
    private BigDecimal elecPrice;
    /**
     * 电损金额
     */
    @ExcelField(title = "电损金额（元）", sort = 50)
    private BigDecimal discount;
    /**
     * 实际金额消费 newFeild
     */
    @ExcelField(title = "实际金额消费（元）", sort = 51)
//    private BigDecimal actualPrice;
    private BigDecimal principalAmount;
//    @Schema(description = "本金(实际收入): 单位,元")
//    private BigDecimal principalAmount;

//    private BigDecimal actualPrice;

    /**
     * 赠送金额消费 newFeild
     */
    @ExcelField(title = "赠送金额消费（元）", sort = 52)
//    private BigDecimal actualSubsidyAmount;
    private BigDecimal freeGoldAmount;

    @ExcelField(title = "未开票金额（元）", sort = 53)
//    private BigDecimal invoiceAmount;
    private BigDecimal uninvoiceAmount;

    @ExcelField(title = "已开票金额（元）", sort = 54)
    private BigDecimal invoicedAmount;

    @ExcelField(title = "不可开票金额（元）", sort = 55)
    //private BigDecimal uninvoiceAmount;
    private BigDecimal freeAmount;

    /**
     * 订单总金额
     **/
    @ExcelField(title = "订单总金额（元）", sort = 56)
//    private Double orderPrice;
    private BigDecimal orderPrice;

    @ExcelField(title = "优惠券号", sort = 57)
//    private BigDecimal actualSubsidyAmount;
    private Long couponId;

    @ExcelField(title = "优惠券抵扣金额", sort = 57)
    private BigDecimal couponAmount;

    @ExcelField(title = "积分优惠金额", sort = 57)
    private BigDecimal scoreAmount;

    @ExcelField(title = "农行优惠金额", sort = 57)
    private BigDecimal abcCouponAmount;


    @ExcelField(title = "支付时间", sort = 57)
    private String payTime;

    /**
     * 订单处理类型 newFeild
     */
    @ExcelField(title = "订单处理类型", sort = 58)
//    private String processTypeName;
    private String processType;
    /**
     * 结算类型 newFeild
     */
    @ExcelField(title = "结算类型", sort = 59)
//    private String settlementTypeName;
    private String settlementType;

    /**
     * 电表开始读数
     **/
    @ExcelField(title = "电表开始读数", sort = 61, digits = 4)
    private BigDecimal startElectricity;
    /**
     * 电表结束读数
     **/
    @ExcelField(title = "电表结束读数", sort = 62, digits = 4)
    private BigDecimal endElectricity;

    @ExcelField(title = "停充超时费（元）", sort = 70)
    private BigDecimal parkingFee;

    @ExcelField(title = "电费收益（元）", sort = 71)
    private BigDecimal elecProfit;

    @ExcelField(title = "服务费收益（元）", sort = 72)
    private BigDecimal servProfit;

    @ExcelField(title = "总收益（元）", sort = 73)
    private BigDecimal totalProfit;

    @ExcelField(title = "对账结果", sort = 74)
    private String checkResult;

    @ExcelField(title = "直付收款（元）", sort = 75)
    private BigDecimal zftTotalMoney;

    @ExcelField(title = "对账差额（元）", sort = 76)
    private BigDecimal checkDiffMoney;

//     /**
//      * 枪头id
//      **/
//     @ExcelField(title = "枪头编号", sort = 16)
//     private String bcCode;

    /**
     * 订单状态
     */
    private Integer status;
    private Long stationId;

    /**
     * 1、C端用户显示：用户昵称（用户ID） 2、桩主管理员（公有云端账号开启充电）：桩主账号（桩主的UID）
     */
    private String customerId;
    //private String mobilePhone;
    /**
     * 订单来源类型,0:微信渠道;1:APP在线发起充电;2:APP蓝牙发起充电:3:刷卡充电:4:桩上报离线数据
     */
    private String channelId;
    /**
     * 支付方式
     **/
    private String payModes;
    /**
     * 支付状态
     **/
    private String payStatus;
    /**
     * 电桩型号
     **/
    private String evseModel;
}
