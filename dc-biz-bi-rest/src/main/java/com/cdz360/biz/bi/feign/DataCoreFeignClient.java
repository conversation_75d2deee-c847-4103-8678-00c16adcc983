package com.cdz360.biz.bi.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.order.param.ListSettlementOrderParam;
import com.cdz360.biz.model.trading.file.po.OssFilePo;
import com.cdz360.biz.model.trading.invoice.dto.InvoiceRecordOrderRefDto;
import com.cdz360.biz.model.trading.invoice.param.ListInvoiceRecordOrderRefParam;
import com.cdz360.biz.model.trading.iot.vo.EvseInfoVo;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.model.trading.order.param.ZftBillParam;
import com.cdz360.biz.model.trading.order.vo.PayBillVo;
import com.cdz360.biz.model.trading.order.vo.SettlementOrderVo;
import com.cdz360.biz.model.trading.order.vo.ZftBillVo;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceRecordDetail;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderSite;
import com.chargerlinkcar.framework.common.domain.vo.OrderBiVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderTimeShareBiVo;
import com.chargerlinkcar.framework.common.domain.vo.SiteInMongoVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE)
@Component
public interface DataCoreFeignClient {

    /**
     * 获取充值记录列表
     *
     * @param payBillParam
     * @return
     */
    @PostMapping("/dataCore/paybill/orderList")
    ListResponse<PayBillVo> payBillList(@RequestBody PayBillParam payBillParam);

    @PostMapping("/dataCore/paybill/getZftBillList")
    ListResponse<ZftBillVo> zftBillList(@RequestBody ZftBillParam payBillParam);

    /**
     * 获取企业客户的充电订单列表
     * @param param
     * @return
     */
    @PostMapping(value = "/dataCore/order/getSettlementOrderList")
    ListResponse<SettlementOrderVo> getSettlementOrderList(@RequestBody ListSettlementOrderParam param);

    /**
     * 获取脱机桩列表
     * @param param
     * @return
     */
    @PostMapping("/dataCore/evseManager/getOfflineEvseList")
    ListResponse<EvseInfoVo> getOfflineEvseList(@RequestBody ListEvseParam param);

    @GetMapping(value = "/dataCore/invoice/corpInvoiceRecordDetail")
    ObjectResponse<CorpInvoiceRecordDetail> corpInvoiceRecordDetail(
        @RequestParam(value = "applyNo") String applyNo);

    @PostMapping(value = "/dataCore/order/chargerOrderGroupBySite")
    ListResponse<ChargerOrderSite> chargerOrderGroupBySite(@RequestBody ListChargeOrderParam param);

    @PostMapping(value = "/dataCore/invoice/recordOrderGroupBySite")
    ListResponse<ChargerOrderSite> recordOrderGroupBySite(
        @RequestBody ListInvoiceRecordOrderRefParam param);

    @PostMapping(value = "/dataCore/order/chargerOrderGroupByTimeShareFee")
    ListResponse<OrderTimeShareBiVo> chargerOrderGroupByTimeShareFee(
        @RequestBody ListChargeOrderParam param);

    @PostMapping(value = "/dataCore/invoice/recordOrderGroupByTimeShareFee")
    ListResponse<OrderTimeShareBiVo> recordOrderGroupByTimeShareFee(
        @RequestBody ListInvoiceRecordOrderRefParam param);

    @PostMapping("/dataCore/paybill/invoiceOrderBiForCorp")
    ObjectResponse<OrderBiVo> invoiceOrderBiForCorp(@RequestBody PayBillParam param);

    @PostMapping(value = "/dataCore/invoice/getInvoiceRecordOrderList")
    ListResponse<InvoiceRecordOrderRefDto> getInvoiceRecordOrderList(
        @RequestBody ListInvoiceRecordOrderRefParam param);

    @PostMapping("/dataCore/file/addFile")
    ObjectResponse<OssFilePo> addFile(@RequestBody OssFilePo param);


    @PostMapping("/dataCore/site/getSiteListFromMongo")
    ListResponse<SiteInMongoVo> getSiteListFromMongo(@RequestBody ListSiteParam param);
}
