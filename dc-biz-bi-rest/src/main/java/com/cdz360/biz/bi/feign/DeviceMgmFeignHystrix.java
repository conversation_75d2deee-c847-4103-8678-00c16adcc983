package com.cdz360.biz.bi.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.ess.vo.EssEquipVo;
import com.cdz360.biz.model.iot.param.ListCtrlParam;
import com.cdz360.biz.model.sim.param.ListSimParam;
import com.cdz360.biz.model.sim.vo.SimVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactivefeign.FallbackFactory;

@Slf4j
@Component
public class DeviceMgmFeignHystrix implements FallbackFactory<DeviceMgmFeignClient> {

    @Override
    public DeviceMgmFeignClient apply(Throwable throwable) {
        log.error("err = {}", throwable.getMessage(), throwable);
        return new DeviceMgmFeignClient() {
            @Override
            public ListResponse<SimVo> getSimList(ListSimParam param) {
                log.error("【服务熔断】。Service = {}, api = getSimList (获取SIM卡信息), param = {}",
                    DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, param);
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<EssEquipVo> findEquipList(ListCtrlParam param) {
                log.error("服务[{}]接口熔断 - 获取设备列表, param = {}",
                    DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, param);
                return RestUtils.serverBusy4ListResponse();
            }

        };
    }
}
