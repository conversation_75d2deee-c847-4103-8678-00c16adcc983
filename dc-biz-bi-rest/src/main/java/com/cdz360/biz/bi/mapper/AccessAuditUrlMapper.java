package com.cdz360.biz.bi.mapper;

import com.cdz360.biz.bi.domain.AccessAuditUrl;
import com.github.pagehelper.Page;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AccessAuditUrlMapper {

    /**
     * 通过id删除 访问审计url
     * @param id
     * @return
     */
    int deleteById(@Param("id")Long id);

    /**
     * 添加 访问审计url
     * @param record
     * @return
     */
    int insertSelective(AccessAuditUrl record);

    /**
     * 通过id查询 访问审计url
     * @param id
     * @return
     */
    AccessAuditUrl selectById(@Param("id")Long id);

    /**
     * 通过url查询 访问审计url
     * @param url
     * @return
     */
    AccessAuditUrl selectByUrl(@Param("url")String url);

    /**
     * 通过status查询 访问审计url列表（status为空时，查询所有 访问审计url）
     * @param status
     * @return
     */
    List<AccessAuditUrl> queryAccessAuditUrls(@Param(value = "status")Integer status);

    /**
     * 通过status分页查询 访问审计url列表（status为空时，查询所有 访问审计url）
     * @param status
     * @return
     */
    Page<AccessAuditUrl> queryPageAccessAuditUrls(@Param(value = "status")Integer status);


    /**
     * 更新 访问审计url
     * @param record
     * @return
     */
    int updateByIdSelective(AccessAuditUrl record);




    // List<ChargerOrder> selectAll();

}