package com.cdz360.biz.bi.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.biz.bi.service.BalanceApplicationBiService;
import com.cdz360.biz.ds.cus.ro.balance.param.BalanceApplicationParam;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.chargerlinkcar.framework.common.utils.LoggerHelper;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * BalanceApplicationBiRest
 *
 * @since 7/6/2021 11:10 AM
 * <AUTHOR>
 */
@Slf4j
@RestController
@Tag(name = "充值申请的导出接口", description = "充值申请")
public class BalanceApplicationBiRest {

    private static final String BALANCE_APPLICATION = "balanceApplication";

    @Autowired
    private BalanceApplicationBiService balanceApplicationBiService;

    private static String excelSubDir() {
        return BALANCE_APPLICATION + new SimpleDateFormat("yyyyMMdd").format(new Date());
    }

    /**
     * 充值信息列表excel导出
     *
     * @param request
     * @param balanceApplicationParam
     * @return
     */
    @Operation(summary = "充值信息列表excel导出(运营管理导出使用)")
    @PostMapping("/bi/balanceApplication/exportExcel")
    public ObjectResponse<ExcelPosition> exportExcel(
        HttpServletRequest request,
        @RequestBody BalanceApplicationParam balanceApplicationParam) {
        log.info(LoggerHelper.formatEnterLog(request, false) + " param={}",
            balanceApplicationParam);
        throw new DcServiceException("接口已废弃，请联系开发");
//        // excel生成位置信息
//        ExcelPosition position = new ExcelPosition();
//        position.setSubDir(excelSubDir())
//                .setSubFileName(UUIDUtils.getUuid32());
//
//        // 异步调用
//        balanceApplicationBiService.exportExcel(position, balanceApplicationParam);
//        return new ObjectResponse<>(position);
    }
}