package com.cdz360.biz.bi.domain.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class ChargerOrderDailyExportI18nVO implements Serializable {

    @ExcelField(title = "日期", i18nTitle = "common.date", sort = 1)
    private String dailyDay;

    @ExcelField(title = "标准服务费(元)", i18nTitle = "charger.order.standardServiceCosts", sort = 2)
    private BigDecimal servOriginFee;

    @ExcelField(title = "总服务费(元)", i18nTitle = "charger.order.totalServiceCosts", sort = 3)
    private BigDecimal servicePrice;

    @ExcelField(title = "标准电费(元)", i18nTitle = "charger.order.standardElecCosts", sort = 4)
    private BigDecimal elecOriginFee;

    @ExcelField(title = "总电费(元)", i18nTitle = "charger.order.totalElecCosts", sort = 5)
    private BigDecimal elecPrice;

    @ExcelField(title = "充电总金额(元)", i18nTitle = "charger.order.totalAmountOfCharging", sort = 6)
    private BigDecimal orderPrice;
}
