package com.cdz360.biz.bi.domain.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * VinDto
 *
 * @since 2019/5/15 14:29
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class VinOnCorpExport2Vo implements Serializable {

    @ExcelField(title = "VIN码", sort = 1)
    @Schema(description = "VIN码")
    private String vin;

    @ExcelField(title = "车牌号", sort = 2)
    @Schema(description = "车牌号")
    private String carNo;

    @ExcelField(title = "车队", sort = 3)
    @Schema(description = "车队")
    private String carDepart;

    @ExcelField(title = "线路", sort = 4)
    @Schema(description = "线路")
    private String lineNum;

    @ExcelField(title = "车辆自编号", sort = 5)
    @Schema(description = "车辆自编号")
    private String carNum;

    @ExcelField(title = "组织名称", sort = 6)
    @Schema(description = "组织名称")
    private String corpOrgName;

    @ExcelField(title = "手机号", sort = 7)
    @Schema(description = "手机号")
    private String mobile;

    @ExcelField(title = "指定站点数量", sort = 8)
    @Schema(description = "指定站点数量")
    private String usableStationCount;


    @ExcelField(title = "状态", sort = 9)
    @Schema(description = "状态")
    private String status;


    @ExcelField(title = "创建时间", sort = 10)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}