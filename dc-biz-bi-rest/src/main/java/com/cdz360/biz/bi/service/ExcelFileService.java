package com.cdz360.biz.bi.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.bi.config.ExportFileConfig;
import com.cdz360.biz.bi.domain.vo.ChargerOrderDailyExportI18nVO;
import com.cdz360.biz.bi.domain.vo.ChargerOrderDailyExportVO;
import com.cdz360.biz.bi.domain.vo.ChargerOrderDetailI18nVO;
import com.cdz360.biz.bi.domain.vo.ChargerOrderDetailVO;
import com.cdz360.biz.bi.domain.vo.ChargerOrderExportI18nVO;
import com.cdz360.biz.bi.domain.vo.ChargerOrderExportVO;
import com.cdz360.biz.bi.domain.vo.CommUserOrderExportVO;
import com.cdz360.biz.bi.domain.vo.PointLogExportVo;
import com.cdz360.biz.bi.domain.vo.SettlemenSummaryExcelVo;
import com.cdz360.biz.bi.domain.vo.SettlementOrderExcelVo;
import com.cdz360.biz.bi.domain.vo.VinExportBodyVo;
import com.cdz360.biz.bi.domain.vo.VinExportHeaderVo;
import com.cdz360.biz.bi.feign.AuthCenterFeignClient;
import com.cdz360.biz.bi.feign.DataCoreFeignClient;
import com.cdz360.biz.bi.feign.InvoiceFeignClient;
import com.cdz360.biz.bi.mapper.ChargerOrderMapper;
import com.cdz360.biz.bi.utils.OSSClientUtil;
import com.cdz360.biz.model.FileItem;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.settlement.param.ListSettlementParam;
import com.cdz360.biz.model.cus.settlement.type.GuaranteeWay;
import com.cdz360.biz.model.cus.settlement.vo.SettlementVo;
import com.cdz360.biz.model.finance.type.OrderType;
import com.cdz360.biz.model.finance.type.TaxStatus;
import com.cdz360.biz.model.invoice.type.InvoiceType;
import com.cdz360.biz.model.order.param.ListSettlementOrderParam;
import com.cdz360.biz.model.order.type.GbVerType;
import com.cdz360.biz.model.order.type.OrderPayType;
import com.cdz360.biz.model.trading.bi.dto.BiExportGroups;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.cdz360.biz.model.trading.deposit.vo.DepositSourceType;
import com.cdz360.biz.model.trading.order.vo.SettlementOrderVo;
import com.cdz360.biz.utils.feign.his.HisOrderFeignClient;
import com.cdz360.biz.utils.service.OssService;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.domain.BillExportParam;
import com.chargerlinkcar.framework.common.domain.CorpListPointLogParam;
import com.chargerlinkcar.framework.common.domain.Dict;
import com.chargerlinkcar.framework.common.domain.PointLog;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceInfoParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceInfoVo;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.domain.type.CardType;
import com.chargerlinkcar.framework.common.domain.type.ProcessType;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDataVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.VinDto;
import com.chargerlinkcar.framework.common.feign.UserFeignClient;
import com.chargerlinkcar.framework.common.service.DcCusBalanceService;
import com.chargerlinkcar.framework.common.utils.DateUtils;
import com.chargerlinkcar.framework.common.utils.ExcelUtil;
import com.chargerlinkcar.framework.common.utils.ExportExcel;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.HistoryDataUtils;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import jakarta.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.Serializable;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> //导出订单excel
 * @since 2019/8/20
 **/
@Slf4j
@Service
public class ExcelFileService //implements IExcelFileService
{

    private static final String CHARSET = "utf-8";
    // 模板文件的路径(账单)
    private static String TEMP_FILE_PATH = null;

    // 默认启动金额
//    @Value("${excel.dir:/tmp/excel}")
//    private String TEMP_DIR;
    @Autowired
    private ExportFileConfig exportFileConfig;
    private String PART_FILE_NAME = "part";
    @Value("${resourceOwnerId:}")
    private String TEMP;
    @Value("${excel.tmp.file:files/bill_tmpl.xlsx}")
    private String EXCEL_TEMP_FILE;
    @Value("${excel.tmp.file:files/bill_oa_tmpl.xlsx}")
    private String OA_EXCEL_TEMP_FILE;
    @Autowired
    private UserFeignClient userFeignClient;
    //    @Autowired
//    private TradingFeignClient tradingFeignClient;
    @Autowired
    private OrderService orderService;
    @Autowired
    private DcCusBalanceService dcCusBalanceService;
    @Autowired
    private ChargerOrderMapper chargerOrderMapper;
    //    @Autowired
//    private CommercialQueryDs commercialQueryDs;
    @Autowired
    private DictDs dictService;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;
    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;
    @Autowired
    private OSSClientUtil ossClientUtil;

    @Autowired
    private OssService ossService;

    @Autowired
    private InvoiceFeignClient invoiceFeignClient;

    @Autowired
    private HistoryDataUtils historyDataUtils;
    @Autowired
    private HisOrderFeignClient hisOrderFeignClient;

    @Autowired
    private MessageSource messageSource;

    /**
     * [实际数据]和[写入数据]必须implements Serializable
     *
     * @param subDir       文件存放子目录
     * @param subFileName  文件名
     * @param clazz        输出到excel的行记录
     * @param group
     * @param sheetName
     * @param templateFile 模板文件
     * @param locale       语言环境，未设置为null
     * @param f            lambda, 获取[实际数据]
     * @param ft           lambda, 将f获取的[实际数据]转换成[写入数据]
     */
    public void exportExcelFile(
        String subDir,
        String subFileName,
        Class clazz,
        Integer group,
        String sheetName,
        String templateFile,
        List<String> filterList,
        Locale locale,
        Function<List<Serializable>, Integer, Integer> f,
        FunctionTrans<List<Serializable>, List<Serializable>> ft) {

        synchronized (this) {
            log.info("exportExcelFile 开始作业");
//            //先清除临时文件夹中的文件
//            deleteFile(searchParam.getSubDir(), searchParam.getType());

            // 文件存放目录
            String dir = exportFileConfig.getExcelDir() + File.separator + subDir;

            // 临时文件
            String tmpFilePath = dir + File.separator + subFileName + PART_FILE_NAME + ".xlsx";

            // 重定向文件
            String filePath = dir + File.separator + subFileName + ".xlsx";

            // excel
            ExportExcel exportExcel = null;
            try {
                if (group != null) {
                    if (null != locale) {
                        exportExcel = new ExportExcel(null, clazz, sheetName, templateFile,
                            filterList, locale, group);
                    } else {
                        exportExcel = new ExportExcel(null, clazz, sheetName, templateFile,
                            filterList,
                            group);
                    }
                } else {
                    if (null != locale) {
                        exportExcel = new ExportExcel(null, clazz, sheetName, templateFile,
                            filterList, locale);
                    } else {
                        exportExcel = new ExportExcel(null, clazz, sheetName, templateFile,
                            filterList);
                    }
                }
            } catch (Exception e) {
                log.error("{}", e.getMessage(), e);
                return;
            }

            // 分页索引
            int index = 1; // 分页从1开始
            int size = 10000;

            // 改成分页获取数据
            while (true) {
                // System.out.println("作业中");
                List<Serializable> serializables = f.apply(index, size);
                if (CollectionUtils.isEmpty(serializables)) {
                    log.warn("获取订单结束.");
                    break;
                } else {
                    log.info("当前页: index={}, size={}", index, size);
                    // 追加到excel
                    List<Serializable> tmp = ft.apply(serializables);
                    log.info("请求数据 size = {}", tmp.size());
//                    log.info("tmp: {}", JsonUtils.toJsonString(tmp));
                    exportExcel.addDataList((index - 1) * size + 1, tmp); // 考虑数据开始行号
                    if (tmp.size() < size) {
                        log.info("获取订单结束：size={}", tmp.size());
                        break;
                    }
                    // 页码递增
                    index += 1;
//                    log.info("index: {}", index);
//                    // 10w条数据约束
//                    if (100 == index) {
//                        log.info("已超出最大订单导出条数");
//                        throw new DcServiceException("已超出最大订单导出条数（导出订单条数最多10万条）！减少订单导出条数后，再导出。");
//                    }
                    // 10w条数据约束
                    IotAssert.isTrue(index <= 100,
                        "已超出最大订单导出条数（导出订单条数最多10万条）！减少订单导出条数后，再导出。");

                }
            }
            log.info("临时文件: {}, 重定向文件: {}", tmpFilePath, filePath);
            try {
                File folder = new File(dir);
                //如果文件夹不存在则创建
                if (!folder.exists()) {
                    folder.mkdirs();
                }
                exportExcel.writeFile(tmpFilePath);

                //重命名:临时文件->最终文件名
                FileUtils.copyFile(new File(tmpFilePath), new File(filePath));
//                new File(tmpFilePath).renameTo(new File(filePath));
            } catch (IOException e) {
                log.error("<< 写订单excel出错: error={},{}", e.getMessage(), e);
            }
            log.info("<< 写订单结束excel,filePath: {}", filePath);
        }
    }

//    /**
//     * [实际数据]和[写入数据]必须implements Serializable
//     *
//     * @param subDir      文件存放子目录
//     * @param subFileName 文件名
//     * @param clazz       输出到excel的行记录
//     * @param headerList
//     * @param f           lambda, 获取[实际数据]
//     * @param ft          lambda, 将f获取的[实际数据]转换成[写入数据]
//     */
//    public void exportExcelFiles(
//            String subDir,
//            String subFileName,
//            Class clazz,
//            Integer group,
//            String sheetName,
//            List<String> filter,
//            List<ChargerOrderDetailVO> headerList,
//            Function<List<Serializable>, Integer, Integer> f,
//            FunctionTrans<List<Serializable>, List<Serializable>> ft) {
//
//        synchronized (this) {
//            log.info("exportExcelFile 开始作业");
////            //先清除临时文件夹中的文件
////            deleteFile(searchParam.getSubDir(), searchParam.getType());
//
//            // 文件存放目录
//            String dir = exportFileConfig.getExcelDir() + File.separator + subDir;
//
//            // 临时文件
//            String tmpFilePath = dir + File.separator + subFileName + PART_FILE_NAME + ".xlsx";
//
//            // 重定向文件
//            String filePath = dir + File.separator + subFileName + ".xlsx";
//
//            // excel
//            ExportExcelDetail exportExcel = new ExportExcelDetail(null, clazz, sheetName, filter, 0);
//
//            // 分页索引
//            int index = 1; // 分页从1开始
//            int size = 10000;
//            // 改成分页获取数据
//            while (true) {
//                List<Serializable> serializables = f.apply(index, size);
//                if (CollectionUtils.isEmpty(serializables)) {
//                    log.warn("获取订单结束.");
//                    break;
//                } else {
//                    log.info("当前页: index={}, size={}", index, size);
//                    // 追加到excel
//                    List<Serializable> tmp = ft.apply(serializables);
////                    log.info("ft.apply done size = {}", tmp.size());
////                    log.info("tmp: {}", JsonUtils.toJsonString(tmp));
//                    exportExcel.addDataList((index - 1) * size+1, tmp); // 考虑数据开始行号
//
////                    log.info(" exportExcel.addDataList done ");
//                    // 页码递增
//                    index += 1;
////                    log.info("index: {}", index);
////                    // 10w条数据约束
////                    if (100 == index) {
////                        log.info("已超出最大订单导出条数");
////                        throw new DcServiceException("已超出最大订单导出条数（导出订单条数最多10万条）！减少订单导出条数后，再导出。");
////                    }
//                    // 10w条数据约束
//                    IotAssert.isTrue(index < 100,
//                            "已超出最大订单导出条数（导出订单条数最多10万条）！减少订单导出条数后，再导出。");
//
//                }
//            }
//
//            //写入汇总信息
//            exportExcel.setSheet("统计汇总");
//            exportExcel.addHeader(ChargerOrderDetailVO.class, 1);  //定义对象
//            exportExcel.addData(1, headerList);   //追加数据
//            exportExcel.setHeader(0);     //重新定义头部信息
//
//            try {
//                File folder = new File(dir);
//                //如果文件夹不存在则创建
//                if (!folder.exists() && !folder.isDirectory()) {
//                    folder.mkdirs();
//                }
//                exportExcel.writeFile(tmpFilePath);
//
//                //重命名:临时文件->最终文件名
//                new File(tmpFilePath).renameTo(new File(filePath));
//            } catch (IOException e) {
//                log.error("<< 写订单excel出错: error={},{}", e.getMessage(), e);
//            }
//            log.info("<< 写订单结束excel,filePath: {}", filePath);
//        }
//    }

    /**
     * 订单导出，包含汇总信息等条件
     *
     * @param searchParam
     */
//    @Async
    public void writeTempExcelByChargeOrderList(String taskId, ChargerOrderParam searchParam)
        throws IOException {
        log.info("导出订单数据开始 taskId = {},searchParam = {}", taskId,
            JsonUtils.toJsonString(searchParam));

        ListResponse<ChargerOrderDataVo> re; // 数据汇总信息
        ObjectResponse<ChargerOrderDataVo> info; // 总计信息

        re = historyDataUtils.checkListOrderParam(searchParam, true)
            ? orderService.exportChargerOrderData(searchParam)
            : hisOrderFeignClient.exportChargerOrderData(searchParam)
                .doOnNext(FeignResponseValidate::check)
                .block(Duration.ofSeconds(50L));

        info = historyDataUtils.checkListOrderParam(searchParam, true)
            ? orderService.getChargerOrderData(searchParam)
            : hisOrderFeignClient.getChargerOrderData(searchParam)
                .doOnNext(FeignResponseValidate::check)
                .block(Duration.ofSeconds(50L));

        //获取导出数据的汇总信息
//        ListResponse<ChargerOrderDataVo> re = orderService.exportChargerOrderData(searchParam);
        //总计信息
//        ObjectResponse<ChargerOrderDataVo> info = orderService.getChargerOrderData(searchParam);

        List<ChargerOrderDetailVO> chargerOrderDetailVOList = new ArrayList<>();

        if (re.getData().size() == 0) {
            for (int i = 0; i < 5; i++) {
                ChargerOrderDataVo chargerDetail = new ChargerOrderDataVo();
                chargerDetail.setTag(String.valueOf(i));
                chargerDetail.setServicePriceAmount(BigDecimal.ZERO);
                chargerDetail.setOrderPriceAmount(BigDecimal.ZERO);
                chargerDetail.setOrderElectricityAmount(BigDecimal.ZERO);
                chargerDetail.setElecPriceAmount(BigDecimal.ZERO);
                chargerDetail.setChargerOrderNumber(0L);
                re.getData().add(chargerDetail);
            }
        }

        //数组小于5
        if (re.getData().size() < 5) {
            ArrayList<String> tagInfo = new ArrayList<>();
            String[] tagList = new String[]{"0", "1", "2", "3", "4"};
            re.getData().forEach(v -> {
                tagInfo.add(v.getTag());
            });
            //判断字符串是否在arrayList中
            for (String tag : tagList) {
                if (!tagInfo.contains(tag)) {
                    ChargerOrderDataVo chargerDetail = new ChargerOrderDataVo();
                    chargerDetail.setTag(tag);
                    chargerDetail.setServicePriceAmount(BigDecimal.ZERO);
                    chargerDetail.setOrderPriceAmount(BigDecimal.ZERO);
                    chargerDetail.setOrderElectricityAmount(BigDecimal.ZERO);
                    chargerDetail.setElecPriceAmount(BigDecimal.ZERO);
                    chargerDetail.setChargerOrderNumber(0L);
                    re.getData().add(chargerDetail);
                }
            }
        }
        //数据排序
        List<ChargerOrderDataVo> newList = re.getData().stream()
            .sorted(Comparator.comparing(ChargerOrderDataVo::getTag)).collect(Collectors.toList());

        String summaryMsg = null == searchParam.getLocale() ? "汇总"
            : messageSource.getMessage("charger.order.summary", null,
                searchParam.getLocale());
        String sharpMsg = null == searchParam.getLocale() ? "尖时"
            : messageSource.getMessage("charger.order.sharpTime", null,
                searchParam.getLocale());
        String peakMsg = null == searchParam.getLocale() ? "峰时"
            : messageSource.getMessage("charger.order.peakTime", null,
                searchParam.getLocale());
        String shoulderMsg = null == searchParam.getLocale() ? "平时"
            : messageSource.getMessage("charger.order.shoulderTime", null,
                searchParam.getLocale());
        String offPeakMsg = null == searchParam.getLocale() ? "谷时"
            : messageSource.getMessage("charger.order.offPeakTime", null,
                searchParam.getLocale());
        newList.forEach(vo -> {
            ChargerOrderDetailVO v = new ChargerOrderDetailVO();
            switch (vo.getTag()) {
                case "0":
                    // 汇总
                    v.setTag(summaryMsg);
                    break;
                case "1":
                    // 尖时
                    v.setTag(sharpMsg);
                    break;
                case "2":
                    // 峰时
                    v.setTag(peakMsg);
                    break;
                case "3":
                    // 平时
                    v.setTag(shoulderMsg);
                    break;
                case "4":
                    // 谷时
                    v.setTag(offPeakMsg);
                    break;
            }

            // v.getTag().equals("汇总")
            if (v.getTag().equals(summaryMsg)) { //订单汇总信息
                v.setChargerOrderNumber(new BigDecimal(info.getData().getChargerOrderNumber()));
                v.setElecPriceAmount(info.getData().getElecPriceAmount() == null ? BigDecimal.ZERO
                    : info.getData().getElecPriceAmount());
                v.setOrderElectricityAmount(
                    info.getData().getOrderElectricityAmount() == null ? BigDecimal.ZERO
                        : info.getData().getOrderElectricityAmount());
                v.setOrderPriceAmount(info.getData().getOrderPriceAmount() == null ? BigDecimal.ZERO
                    : info.getData().getOrderPriceAmount());
                v.setServicePriceAmount(
                    info.getData().getServicePriceAmount() == null ? BigDecimal.ZERO
                        : info.getData().getServicePriceAmount());
            } else {
                v.setChargerOrderNumber(
                    new BigDecimal(vo.getChargerOrderNumber().toString().split("\\.")[0]));
                v.setElecPriceAmount(vo.getElecPriceAmount());
                v.setOrderElectricityAmount(vo.getOrderElectricityAmount());
                v.setOrderPriceAmount(vo.getOrderPriceAmount());
                v.setServicePriceAmount(vo.getServicePriceAmount());
            }

            chargerOrderDetailVOList.add(v);
        });

        //判断筛选字段中是否存在freeGoldAmount
        List<String> filter = new ArrayList<>(searchParam.getFilter());
        // 临时  前端不可开票金额
        if (filter.contains("cannotInvoiceAmount")) {
            filter.add("freeAmount");
        }

        if (null == searchParam.getLocale()) {
            // 没拿到语言信息，默认中文版保留原有逻辑
            if (searchParam.getIsDailySummary() != null && searchParam.getIsDailySummary()) {
                List<ChargerOrderDailyExportVO> chargerOrderDailyExportVOList = orderService.chargerOrderDailyVOList(
                    searchParam);
                ExcelUtil.builder(exportFileConfig.getExcelDir(), searchParam.getExcelPosition(),
                        "统计汇总")
                    .addHeader(ChargerOrderDetailVO.class)
                    .appendDataList(chargerOrderDetailVOList)
                    .newSheet("每日汇总", ChargerOrderDailyExportVO.class)
                    .appendDataList(chargerOrderDailyExportVOList)
                    .newSheet(searchParam.getSheetName(), ChargerOrderExportVO.class, filter)
                    .loopAppendData((page, size) -> {
                        searchParam.setSize(size);
                        searchParam.setCurrent(page);
                        searchParam.setTotal(false);
                        ListResponse<ChargerOrderVo> res =
                            historyDataUtils.checkListOrderParam(searchParam, true)
                                ? orderService.exportChargeOrderListV2(
                                searchParam)
                                : hisOrderFeignClient.exportChargeOrderListV2(searchParam)
                                    .doOnNext(FeignResponseValidate::check)
                                    .block(Duration.ofSeconds(50L));
//                    ListResponse<ChargerOrderVo> res = orderService.exportChargeOrderListV2(
//                        searchParam);
                        return new ArrayList<>(res.getData());
                    }, list -> new ArrayList<>(transChargerOrder(
                        list.stream().map(x -> (ChargerOrderVo) x).collect(Collectors.toList()),
                        searchParam.getOrderSourceLst(),
                        searchParam.getOrderStatusLst(),
                        null
                    )))
                    .write2File();
            } else {
                ExcelUtil.builder(exportFileConfig.getExcelDir(), searchParam.getExcelPosition(),
                        "统计汇总")
                    .addHeader(ChargerOrderDetailVO.class)
                    .appendDataList(chargerOrderDetailVOList)
                    .newSheet(searchParam.getSheetName(), ChargerOrderExportVO.class, filter)
                    .loopAppendData((page, size) -> {
                        searchParam.setSize(size);
                        searchParam.setCurrent(page);
                        searchParam.setTotal(false);
                        ListResponse<ChargerOrderVo> res =
                            historyDataUtils.checkListOrderParam(searchParam, true)
                                ? orderService.exportChargeOrderListV2(
                                searchParam)
                                : hisOrderFeignClient.exportChargeOrderListV2(searchParam)
                                    .doOnNext(FeignResponseValidate::check)
                                    .block(Duration.ofSeconds(50L));
//                    ListResponse<ChargerOrderVo> res = orderService.exportChargeOrderListV2(
//                        searchParam);
                        return new ArrayList<>(res.getData());
                    }, list -> new ArrayList<>(transChargerOrder(
                        list.stream().map(x -> (ChargerOrderVo) x).collect(Collectors.toList()),
                        searchParam.getOrderSourceLst(),
                        searchParam.getOrderStatusLst(),
                        null
                    )))
                    .write2File();
            }
        } else {
            // 海外版单独处理
            String summaryStatisticsMsg = messageSource.getMessage(
                "charger.order.summaryStatistics",
                null,
                searchParam.getLocale());
            if (searchParam.getIsDailySummary() != null && searchParam.getIsDailySummary()) {
                List<ChargerOrderDailyExportVO> chargerOrderDailyExportVOList = orderService.chargerOrderDailyVOList(
                    searchParam);
                String dailySummaryMsg = messageSource.getMessage("charger.order.dailySummary",
                    null,
                    searchParam.getLocale());

                List<String> a = new ArrayList<>();
                ExcelUtil.builder(exportFileConfig.getExcelDir(), searchParam.getExcelPosition(),
//                    "统计汇总")
                        summaryStatisticsMsg)
                    .addI18nHeader(ChargerOrderDetailI18nVO.class, searchParam.getLocale())
                    .appendDataList(chargerOrderDetailVOList)
//                .newSheet("每日汇总", ChargerOrderDailyExportVO.class)
                    .newI18nSheet(dailySummaryMsg, ChargerOrderDailyExportI18nVO.class,
                        searchParam.getLocale())
                    .appendDataList(chargerOrderDailyExportVOList)
                    .newI18nSheet(searchParam.getSheetName(), ChargerOrderExportI18nVO.class,
                        filter,
                        searchParam.getLocale())
                    .loopAppendData((page, size) -> {
                        searchParam.setSize(size);
                        searchParam.setCurrent(page);
                        searchParam.setTotal(false);
                        ListResponse<ChargerOrderVo> res =
                            historyDataUtils.checkListOrderParam(searchParam, true)
                                ? orderService.exportChargeOrderListV2(
                                searchParam)
                                : hisOrderFeignClient.exportChargeOrderListV2(searchParam)
                                    .doOnNext(FeignResponseValidate::check)
                                    .block(Duration.ofSeconds(50L));
//                    ListResponse<ChargerOrderVo> res = orderService.exportChargeOrderListV2(
//                        searchParam);
                        return new ArrayList<>(res.getData());
                    }, list -> new ArrayList<>(transChargerOrder(
                        list.stream().map(x -> (ChargerOrderVo) x).collect(Collectors.toList()),
                        searchParam.getOrderSourceLst(),
                        searchParam.getOrderStatusLst(),
                        searchParam.getLocale()
                    )))
                    .write2File();
            } else {
                ExcelUtil.builder(exportFileConfig.getExcelDir(), searchParam.getExcelPosition(),
                        // "统计汇总")
                        summaryStatisticsMsg)
                    .addI18nHeader(ChargerOrderDetailI18nVO.class, searchParam.getLocale())
                    .appendDataList(chargerOrderDetailVOList)
                    .newI18nSheet(searchParam.getSheetName(), ChargerOrderExportI18nVO.class,
                        filter,
                        searchParam.getLocale())
                    .loopAppendData((page, size) -> {
                        searchParam.setSize(size);
                        searchParam.setCurrent(page);
                        searchParam.setTotal(false);
                        ListResponse<ChargerOrderVo> res =
                            historyDataUtils.checkListOrderParam(searchParam, true)
                                ? orderService.exportChargeOrderListV2(
                                searchParam)
                                : hisOrderFeignClient.exportChargeOrderListV2(searchParam)
                                    .doOnNext(FeignResponseValidate::check)
                                    .block(Duration.ofSeconds(50L));
//                    ListResponse<ChargerOrderVo> res = orderService.exportChargeOrderListV2(
//                        searchParam);
                        return new ArrayList<>(res.getData());
                    }, list -> new ArrayList<>(transChargerOrder(
                        list.stream().map(x -> (ChargerOrderVo) x).collect(Collectors.toList()),
                        searchParam.getOrderSourceLst(),
                        searchParam.getOrderStatusLst(),
                        searchParam.getLocale()
                    )))
                    .write2File();
            }
        }

//        exportExcelFiles(
//                searchParam.getExcelPosition().getSubDir(),
//                searchParam.getExcelPosition().getSubFileName(),
//                ChargerOrderExportVO.class,
//                null,
//                searchParam.getSheetName(),
//                filter,
//                chargerOrderDetailVOList,
//                (index, size) -> {
//                    searchParam.setSize(size);
//                    searchParam.setCurrent(index);
//                    searchParam.setTotal(false);
//                    ListResponse<ChargerOrderVo> res = orderService.exportChargeOrderListV2(searchParam);
//                    if (res == null || res.getData() == null || res.getData().size() == 0) {
//                        return null;
//                    } else {
//                        return res.getData().stream().collect(Collectors.toList());
//                    }
//                },
//                e ->
//                        transChargerOrder(
//                                e.stream().map(x -> (ChargerOrderVo) x).collect(Collectors.toList()),
//                                searchParam.getOrderSourceLst(),
//                                searchParam.getOrderStatusLst()
//                        ).stream().collect(Collectors.toList())
//        );
        log.info("导出订单数据结束 taskId = {}", taskId);
    }

    /**
     * 商户会员订单
     *
     * @param searchParam
     */
//    @Async
    public void exportCommUserOrderList(ChargerOrderParam searchParam) {
        exportExcelFile(
            searchParam.getExcelPosition().getSubDir(),
            searchParam.getExcelPosition().getSubFileName(),
            CommUserOrderExportVO.class,
            null,
            searchParam.getSheetName(),
            null,
            null,
            null,
            (index, size) -> {
                searchParam.setSize(size);
                searchParam.setCurrent(index);
                ListResponse<ChargerOrderVo> res = orderService.queryChargeOrderList(searchParam,
                    null);
                if (res == null || res.getData() == null || res.getData().size() == 0) {
                    return null;
                } else {
                    return res.getData().stream().collect(Collectors.toList());
                }
            },
            e ->
                transCommUserOrder(
                    e.stream().map(x -> (ChargerOrderVo) x).collect(Collectors.toList()),
                    searchParam.getOrderSourceLst(),
                    searchParam.getOrderStatusLst(),
                    searchParam.getPayModesLst()
                ).stream().collect(Collectors.toList())
        );
    }

    /**
     * 企业管理平台订单导出
     *
     * @param searchParam
     */
    @Async
    public void writeTempExcelByChargeOrderForCompany(String taskId,
        ChargerOrderParam searchParam) {
        log.info("导出订单数据开始 taskId = {}", taskId);
        try {
            ExcelUtil.builder(exportFileConfig.getExcelDir(), searchParam.getExcelPosition(),
                    searchParam.getSheetName())
                .addHeader(ChargerOrderExportVO.class, searchParam.getFilter())
                .loopAppendData((page, size) -> {
                    searchParam.setSize(size);
                    searchParam.setCurrent(page);
                    searchParam.setTotal(false);

                    ListResponse<ChargerOrderVo> res = orderService.exportChargeOrderListV2(
                        searchParam);
                    return new ArrayList<>(res.getData());
                }, list -> new ArrayList<>(transChargerOrder(
                    list.stream().map(x -> (ChargerOrderVo) x).collect(Collectors.toList()),
                    searchParam.getOrderSourceLst(),
                    searchParam.getOrderStatusLst(),
                    null
                )))
                .write2File();
        } catch (Exception e) {
            log.error("企业订单导出异常: err = {}", e.getMessage(), e);
        }

//        exportExcelFile(
//                searchParam.getExcelPosition().getSubDir(),
//                searchParam.getExcelPosition().getSubFileName(),
//                ChargerOrderExportVO.class,
//                null,
//                searchParam.getSheetName(),
//                null,
//                searchParam.getFilter(),
//                (index, size) -> {
//                    searchParam.setSize(size);
//                    searchParam.setCurrent(index);
//                    searchParam.setTotal(false);
//                    ListResponse<ChargerOrderVo> res = orderService.exportChargeOrderListV2(searchParam);
//                    if (res == null || res.getData() == null || res.getData().size() == 0) {
//                        return null;
//                    } else {
//                        return res.getData().stream().collect(Collectors.toList());
//                    }
//                },
//                e ->
//                        transChargerOrder(
//                                e.stream().map(x -> (ChargerOrderVo) x).collect(Collectors.toList()),
//                                searchParam.getOrderSourceLst(),
//                                searchParam.getOrderStatusLst()
//                        ).stream().collect(Collectors.toList())
//        );
//        log.info("导出订单数据结束 taskId = {}", taskId);
    }

    @Async
    public void writeTempExcelByChargeTradeList(CorpListPointLogParam searchParam) {
        searchParam.setPayAccountType(PayAccountType.CORP);
        exportExcelFile(
            searchParam.getExcelPosition().getSubDir(),
            searchParam.getExcelPosition().getSubFileName(),
            PointLogExportVo.class,
            null,
            searchParam.getSheetName(),
            null,
            null,
            null,
            (index, size) -> {
//                    searchParam.setSize(size);
//                    searchParam.setStart(index.longValue());
//                    Page<PointLog> page = new Page<>();
//                    page.setPageSize(size);
//                    page.setPageNum(index);

                long start = index == 0 ? 0 : (index - 1) * size;
                searchParam.setStart(start);
                searchParam.setSize(size);

                ListResponse<PointLog> res = dcCusBalanceService.listPointLog(searchParam);

                if (res == null || res.getData() == null || res.getData().size() == 0) {
                    return null;
                } else {
                    return res.getData().stream().collect(Collectors.toList());
                }
            },
            e ->
                transPointLog(
                    e.stream().map(x -> (PointLog) x).collect(Collectors.toList()),
                    null,
                    null
                ).stream().collect(Collectors.toList())
        );
    }

    private SettlemenSummaryExcelVo convertSettlementVo2SettlemenSummaryExcelVo(
        SettlementVo settlementVo) {
        SettlemenSummaryExcelVo settlemenSummaryVo = new SettlemenSummaryExcelVo();

        settlemenSummaryVo.setSiteName(
            org.apache.commons.lang.StringUtils.join(settlementVo.getSiteNameList(), ","));
        settlemenSummaryVo.setSubSettlement(settlementVo.getSubSettlementType().getDesc());
        settlemenSummaryVo.setElecTotalFee(settlementVo.getSettlementElecFee());

        settlemenSummaryVo.setOrderKwh(settlementVo.getOrderKwh());
        settlemenSummaryVo.setCusName(settlementVo.getCorpName());
        settlemenSummaryVo.setSiteNum(
            org.apache.commons.lang.StringUtils.join(settlementVo.getSiteNoList(), ","));

        if (settlementVo.getGuaranteeWay() == null ||
            settlementVo.getGuaranteeWay() == GuaranteeWay.NOT_ENABLED) {
            settlemenSummaryVo.setGuaranteeKwh("未开启");
        } else if (settlementVo.getGuaranteeWay() == GuaranteeWay.INACTIVE) {
            settlemenSummaryVo.setGuaranteeKwh("未激活");
        } else {
            settlemenSummaryVo.setGuaranteeKwh("已激活");
        }

        settlemenSummaryVo.setServTotalFee(settlementVo.getSettlementServFee());
        settlemenSummaryVo.setSettlementTotalFee(settlementVo.getSettlementTotalFee());
        return settlemenSummaryVo;
    }

    private SettlemenSummaryExcelVo convertSettlementVo2SettlemenSummaryExcelVoForOa(
        SettlementVo settlementVo, List<String> siteNameList, List<String> siteNoList,
        List<String> dateList) {
        SettlemenSummaryExcelVo settlemenSummaryVo = new SettlemenSummaryExcelVo();

        settlemenSummaryVo.setSiteName(org.apache.commons.lang.StringUtils.join(siteNameList, ","));
        // 保底电量，结算方案置空
        settlemenSummaryVo.setSubSettlement("");
        settlemenSummaryVo.setGuaranteeKwh("");

        settlemenSummaryVo.setOrderKwh(settlementVo.getOrderKwh());
        settlemenSummaryVo.setCusName(settlementVo.getCorpName());
        settlemenSummaryVo.setSiteNum(org.apache.commons.lang.StringUtils.join(siteNoList, ","));

        settlemenSummaryVo.setElecTotalFee(settlementVo.getSettlementElecFee());
        settlemenSummaryVo.setServTotalFee(settlementVo.getSettlementServFee());
        settlemenSummaryVo.setSettlementTotalFee(settlementVo.getSettlementTotalFee());
        if (settlementVo.getSettlementOtherFee() != null) {
            settlemenSummaryVo.setSettlementOtherFee(
                DecimalUtils.gt(settlementVo.getSettlementOtherFee(), BigDecimal.ZERO) ? "+"
                    + settlementVo.getSettlementOtherFee()
                    : settlementVo.getSettlementOtherFee().toString()); // 结算其他费用
        } else {
            settlemenSummaryVo.setSettlementOtherFee("");
        }

        settlemenSummaryVo.setName(
            StringUtils.isNotEmpty(settlementVo.getName()) ? settlementVo.getName()
                : "/");
        settlemenSummaryVo.setAddress(StringUtils.isNotEmpty(settlementVo.getAddress())
            ? settlementVo.getAddress() : "/");
        settlemenSummaryVo.setSaleName(StringUtils.isNotEmpty(settlementVo.getSaleName())
            ? settlementVo.getSaleName() : "/");
        settlemenSummaryVo.setSaleBank(StringUtils.isNotEmpty(settlementVo.getSaleBank())
            ? settlementVo.getSaleBank() : "/");
        settlemenSummaryVo.setSaleAccount(
            StringUtils.isNotEmpty(settlementVo.getSaleAccount())
                ? settlementVo.getSaleAccount() : "/");

        settlemenSummaryVo.setSettlementDate(
            org.apache.commons.lang.StringUtils.join(dateList, ",")); // 结算周期
        return settlemenSummaryVo;
    }

    private SettlementOrderExcelVo convertChargerOrder2SettlementOrderExcelVo(
        SettlementOrderVo in) {
        SettlementOrderExcelVo out = new SettlementOrderExcelVo();
        BeanUtils.copyProperties(in, out);

        if (in.getStatus() != null) {
            if (in.getStatus() == 2000) {
                out.setOrderStatus("已支付");
            } else if (in.getStatus() == 800) {
                out.setOrderStatus("充电完成");
            } else if (in.getStatus() == 200) {
                out.setOrderStatus("充电中");
            } else if (in.getStatus() == -500) {
                out.setOrderStatus("充电故障");
            } else if (in.getStatus() == 0) {
                out.setOrderStatus("订单启动中");
            }
        }

        out.setOrderPrice(in.getOrderPrice());
        out.setElecPrice(in.getElecPrice());
        out.setServicePrice(in.getServPrice());
        out.setOrderElectricity(in.getOrderElec());
        out.setElecTag1(in.getKwhJian());
        out.setElecTag2(in.getKwhFeng());
        out.setElecTag3(in.getKwhPing());
        out.setElecTag4(in.getKwhGu());

        out.setMobilePhone(in.getCusPhone());
        out.setStationName(in.getSiteName());

        if (in.getOrderType() != null) {
            if (in.getOrderType() == OrderStartType.MGM_WEB_MANUAL ||
                in.getOrderType() == OrderStartType.MGM_WEB_BATCH) {
                out.setOrderType("后台启动");
            } else {
                out.setOrderType(in.getOrderType().getDesc());
            }
        }

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (in.getChargeStartTime() != null) {
            out.setChargeTime(
                format.format(new Date(in.getChargeStartTime() * 1000L)) + "-" +
                    format.format(new Date(in.getChargeEndTime() * 1000L)));
        }

        return out;
    }

    /**
     * 企客对账 附件导出
     *
     * @param params
     * @param position
     * @throws Exception
     */
    public BillExportParam exportBillExcel(BillExportParam params, ExcelPosition position)
        throws Exception {
        List<String> billNoList;
        if (StringUtils.isNotEmpty(params.getBillNo())) {
            billNoList = List.of(params.getBillNo());
        } else {
            billNoList = params.getBillNoList();
        }
        // 实际结算信息  billNoList可以为空
        List<String> siteNameList = new ArrayList<>();
        List<String> siteNoList = new ArrayList<>();
        List<String> dateList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(billNoList)) {
            ListResponse<SettlementVo> settlementRes = userFeignClient.getSettlementByBillNoList(
                new ListSettlementParam().setBillNoList(billNoList));
            FeignResponseValidate.check(settlementRes);
            siteNameList = settlementRes.getData().stream().map(e -> e.getSiteNameList())
                .filter(CollectionUtils::isNotEmpty).flatMap(e -> e.stream()).distinct()
                .collect(Collectors.toList());
            siteNoList = settlementRes.getData().stream().map(e -> e.getSiteNoList())
                .filter(CollectionUtils::isNotEmpty).flatMap(e -> e.stream()).distinct()
                .collect(Collectors.toList());
            dateList = settlementRes.getData().stream()
                .filter(e -> e.getSettStartDateDay() != null && e.getSettEndDateDay() != null)
                .map(e -> {
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy.MM.dd");
                    return dateFormat.format(e.getSettStartDateDay()) + "-" + dateFormat.format(
                        e.getSettEndDateDay());
                }).collect(Collectors.toList());
        }

        // 文件存放目录
        String dir = exportFileConfig.getExcelDir() + File.separator + position.getSubDir();
        log.debug("临时文件目录: {}", dir);

        // 半成品模板文件
        final String tmpFilePathTmpl =
            dir + File.separator + position.getSubFileName() + PART_FILE_NAME + "_summy_oa_.xlsx";
        log.debug("半成品模板文件: {}", tmpFilePathTmpl);
        IotAssert.isNotNull(params.getCorpId(), "企业信息不能为空");
        ObjectResponse<CorpPo> corpInfo = authCenterFeignClient.getCorp(params.getCorpId());
        FeignResponseValidate.check(corpInfo);

        final Long corpId = params.getCorpId();
        // 账单实际结算金额
        SettlementVo vo = new SettlementVo();
        vo.setCorpName(corpInfo.getData().getBlocUserName())
            .setOrderKwh(params.getElec())
            .setSettlementElecFee(params.getElecFee())
            .setSettlementServFee(params.getServFee())
            .setSettlementTotalFee(params.getSettlementFee())
            .setSettlementOtherFee(params.getOtherFee());

        // 企业开票信息
        ObjectResponse<CorpInvoiceInfoVo> corpInvoiceInfo = userFeignClient.getCorpInvoiceInfo(
            corpId);
        FeignResponseValidate.check(corpInvoiceInfo);

        // 企业开票主体信息
        CorpInvoiceInfoParam invoiceParam = new CorpInvoiceInfoParam();
        invoiceParam.setUid(corpInvoiceInfo.getData().getUid())
            .setTempSalId(corpInvoiceInfo.getData().getTempSalId())
            .setProductTempId(corpInvoiceInfo.getData().getProductTempId());
        ObjectResponse<CorpInvoiceInfoVo> tempSal = invoiceFeignClient.getCorpInvoiceDetail(
            invoiceParam);
        FeignResponseValidate.check(tempSal);

        vo.setName(tempSal.getData().getName());
        vo.setSaleName(tempSal.getData().getSaleName());
        vo.setSaleBank(tempSal.getData().getSaleBank());
        vo.setSaleAccount(tempSal.getData().getSaleAccount());
        vo.setAddress(InvoiceType.ENTER_PROFESSION.equals(tempSal.getData().getInvoiceType())
            ? tempSal.getData().getAddress() : "");

        SettlemenSummaryExcelVo settlemenSummaryExcelVo = this.convertSettlementVo2SettlemenSummaryExcelVoForOa(
            vo, siteNameList, siteNoList, dateList);

        TEMP_FILE_PATH = exportFileConfig.getExcelDir() + File.separator + OA_EXCEL_TEMP_FILE;
        if (!new File(TEMP_FILE_PATH).exists()) {
            // 获取文件流，临时保存文件
            ClassPathResource resource = new ClassPathResource(OA_EXCEL_TEMP_FILE);
            InputStream in = resource.getInputStream();

            File folder = new File(exportFileConfig.getExcelDir() + File.separator + "files/");
            if (!folder.exists()) {
                boolean b = folder.mkdirs();
                log.debug("创建目录: {}, {}", folder.getPath(), b);
            }

            FileOutputStream fos = new FileOutputStream(TEMP_FILE_PATH);
            byte[] b = new byte[1024];
            int len;
            while ((len = in.read(b)) != -1) {
                fos.write(b, 0, len); // 只写入实际读取的字节数
            }
            in.close();
            fos.close(); // 保存数据

            log.debug("EXCEL_TEMP_FILE: {}", OA_EXCEL_TEMP_FILE);
        }

        log.debug("模板文件的路径(账单): {}", TEMP_FILE_PATH);

        ExportExcel exportExcel = new ExportExcel(null,
            SettlemenSummaryExcelVo.class,
            "汇总",
            TEMP_FILE_PATH,
            null);
        log.debug(">> 开始写入汇总信息: {}", JsonUtils.toJsonString(settlemenSummaryExcelVo));
        exportExcel.replaceDataForRead(settlemenSummaryExcelVo);
        exportExcel.writeFileForRead(dir, tmpFilePathTmpl);
        log.debug("<< 完成写入汇总信息: {}");
        // 重定向文件
        String filePath = dir + File.separator + position.getSubFileName() + ".xlsx";
        // 账单汇总编辑  可能不存在账单号
        if (CollectionUtils.isNotEmpty(billNoList)) {
            List<String> finalBillNoList = billNoList;
            this.exportExcelFile(position.getSubDir(),
                position.getSubFileName(),
                SettlementOrderExcelVo.class,
                null,
                "订单明细",
                tmpFilePathTmpl,
                null,
                null,
                (start, size) -> {

                    ListSettlementOrderParam param = new ListSettlementOrderParam();
                    param.setCorpId(corpId)
                        .setBillNoList(finalBillNoList)
                        .setTotal(true)
                        .setSize(size)
                        .setStart((start - 1) * size.longValue());
                    ListResponse<SettlementOrderVo> settlementOrderList = dataCoreFeignClient.getSettlementOrderList(
                        param);
                    if (settlementOrderList == null ||
                        settlementOrderList.getStatus() != ResultConstant.RES_SUCCESS_CODE ||
                        CollectionUtils.isEmpty(settlementOrderList.getData())) {
                        return null;
                    } else {
                        return new ArrayList<>(settlementOrderList.getData());
                    }
                },
                (e) -> e.stream()
                    .map(el ->
                        this.convertChargerOrder2SettlementOrderExcelVo((SettlementOrderVo) el))
                    .collect(Collectors.toList())
            );
        } else {
            // 临时文件
            String tmpFilePath = dir + File.separator + position.getSubFileName() + PART_FILE_NAME
                + "_summy_oa_.xlsx";
            try {
                File folder = new File(dir);
                //如果文件夹不存在则创建
                if (!folder.exists()) {
                    folder.mkdirs();
                }
                //重命名:临时文件->最终文件名
                FileUtils.copyFile(new File(tmpFilePath), new File(filePath));
            } catch (IOException e) {
                log.error("<< 写订单excel出错: error={},{}", e.getMessage(), e);
            }
        }
        // 上传oss
        FileItem ossFile = ossService.oaUploadFile(
            "oa", params.getOaKey(), filePath, position.getSubFileName() + ".xlsx");
        params.setFile(ossFile);
//        ObjectResponse<OssFilePo> ossFilePoObjectResponse = ossClientUtil.uploadFile(filePath);
//        FeignResponseValidate.check(ossFilePoObjectResponse);
//        params.setFileName(ossFilePoObjectResponse.getData().getFileName());
//        params.setFileUrl(ossFilePoObjectResponse.getData().getPath());
        return params;
    }

    //    @Async
    public void writeTempExcelByBillNo(String billNo, ExcelPosition position) throws Exception {

        log.info("账单excel导出: {}, {}", billNo, position);

        ObjectResponse<SettlementVo> settlementRes = userFeignClient.getSettlementByBillNo(billNo);
        FeignResponseValidate.check(settlementRes);

        // 文件存放目录
        String dir = exportFileConfig.getExcelDir() + File.separator + position.getSubDir();
        log.debug("临时文件目录: {}", dir);

        // 半成品模板文件
        final String tmpFilePathTmpl =
            dir + File.separator + position.getSubFileName() + PART_FILE_NAME + "_summy_.xlsx";
        log.debug("半成品模板文件: {}", tmpFilePathTmpl);
//        final String tmpFilePath = dir + File.separator + position.getSubFileName() + PART_FILE_NAME + ".xlsx";
//
//        // 重定向文件
//        final String filePath = dir + File.separator + position.getSubFileName() + ".xlsx";

        SettlementVo settlementVo = settlementRes.getData();
        final Long corpId = settlementVo.getCorpId();

        SettlemenSummaryExcelVo settlemenSummaryExcelVo = this.convertSettlementVo2SettlemenSummaryExcelVo(
            settlementVo);
//        try {
        TEMP_FILE_PATH = exportFileConfig.getExcelDir() + File.separator + EXCEL_TEMP_FILE;
        if (!new File(TEMP_FILE_PATH).exists()) {
            // 获取文件流，临时保存文件
            ClassPathResource resource = new ClassPathResource(EXCEL_TEMP_FILE);
            InputStream in = resource.getInputStream();

            File folder = new File(exportFileConfig.getExcelDir() + File.separator + "files/");
            if (!folder.exists()) {
                boolean b = folder.mkdirs();
                log.debug("创建目录: {}, {}", folder.getPath(), b);
            }

            FileOutputStream fos = new FileOutputStream(TEMP_FILE_PATH);
            byte[] b = new byte[1024];
            while ((in.read(b)) != -1) {
                fos.write(b);// 写入数据
            }
            in.close();
            fos.close(); // 保存数据

            log.debug("EXCEL_TEMP_FILE: {}", EXCEL_TEMP_FILE);
        }

//            if (null == TEMP_FILE_PATH || !new File(TEMP_FILE_PATH).exists()) {
//                log.error("已初始化的模板文件不存在: {}", TEMP_FILE_PATH);
//                return;
//            }

        log.debug("模板文件的路径(账单): {}", TEMP_FILE_PATH);

        ExportExcel exportExcel = new ExportExcel(null,
            SettlemenSummaryExcelVo.class,
            "汇总",
            TEMP_FILE_PATH,
            null);
        log.debug(">> 开始写入汇总信息: {}", JsonUtils.toJsonString(settlemenSummaryExcelVo));
        exportExcel.replaceDataForRead(settlemenSummaryExcelVo);
        exportExcel.writeFileForRead(dir, tmpFilePathTmpl);
        log.debug("<< 完成写入汇总信息: {}");

        this.exportExcelFile(position.getSubDir(),
            position.getSubFileName(),
            SettlementOrderExcelVo.class,
            null,
            "订单明细",
            tmpFilePathTmpl,
            null,
            null,
            (start, size) -> {

                ListSettlementOrderParam param = new ListSettlementOrderParam();
                param.setCorpId(corpId)
                    .setBillNoList(List.of(billNo))
//                    .setTotal(true)
                    .setSize(size)
                    .setStart((start - 1) * size.longValue());
                ListResponse<SettlementOrderVo> settlementOrderList = dataCoreFeignClient.getSettlementOrderList(
                    param);
                if (settlementOrderList == null ||
                    settlementOrderList.getStatus() != ResultConstant.RES_SUCCESS_CODE ||
                    CollectionUtils.isEmpty(settlementOrderList.getData())) {
                    return null;
                } else {
                    return new ArrayList<>(settlementOrderList.getData());
                }
            },
            (e) -> e.stream()
                .map(el ->
                    this.convertChargerOrder2SettlementOrderExcelVo((SettlementOrderVo) el))
                .collect(Collectors.toList())
        );
//        } catch (Exception e) {
//            log.error("导出异常: err = {}", e.getMessage(), e);
//        }
    }

    /**
     * 对象转换
     *
     * @param pointLogList
     * @param arg1
     * @param arg2
     * @return
     */
    private List<PointLogExportVo> transPointLog(List<PointLog> pointLogList, Object arg1,
        Object arg2) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        return pointLogList.stream().map(e -> {
            PointLogExportVo ret = new PointLogExportVo();
            ret.setOrderNo(e.getOrderNo());
            ret.setOperator(
                e.getType() == null ? null : DepositFlowType.valueOf(e.getType()).getDesc());
            ret.setOperatorType(
                e.getOrderType() == null ? "其他" : OrderType.valueOf(e.getOrderType()).getDesc());
            ret.setCreateTime(dateFormat.format(e.getCreateTime()));
            ret.setAmount(e.getDelta());
            ret.setRealAmount(e.getDeltaCost());
            ret.setFreeAmount(e.getDelta().subtract(e.getDeltaCost()));
            ret.setPoint(e.getAvailable());

            if (e.getSourceType() != null) {
                ret.setSourceType(DepositSourceType.valueOf(e.getSourceType()).getDesc());
            }

            if (e.getPayType() != null) {
                ret.setPayType(PayChannel.valueOf(e.getPayType()).getDesc());
            }
            return ret;
        }).collect(Collectors.toList());
    }

    //
//    public Boolean checkExportCountOver(ChargerOrderParam chargerOrderParam) {
//        log.info("检查订单导出数据条数查询  参数  -->" + JsonUtils.toJsonString(chargerOrderParam));
//
//        //查询条件中有commercialId的话，查询该商户下的商户及子商户列表commIdList，并覆盖原commIdList
//        if (chargerOrderParam.getCommercialId() != null) {
//            List<Long> commIdList = commercialQueryDs.listCommercialIds(chargerOrderParam.getCommercialId());
////            ListResponse<Long> jsonObjectCommIdList = merchantFeignClient.getCommIdListByCommId(chargerOrderParam.getCommercialId());
////            FeignResponseValidate.check(jsonObjectCommIdList);
////
////            List<Long> commIdList = jsonObjectCommIdList.getData();
//
//            log.info("commIdList:::" + JsonUtils.toJsonString(commIdList));
//
//            if (CollectionUtils.isEmpty(commIdList)) {
//                throw new DcServiceException("获取当前商户及子商户列表失败");
//            }
//            chargerOrderParam.setCommIdList(commIdList);
//            chargerOrderParam.setCommercialId(null);
//            log.info("订单导出查询  参数  -->" + JsonUtils.toJsonString(chargerOrderParam));
//        }
//
//        List<ChargerOrderVo> list = new ArrayList<>();
//
//        HashMap<String, Object> searchMap = new HashMap<>();
//        MyBeanUtils.copyBean2Map(searchMap, chargerOrderParam);
//
//        String beginTime = chargerOrderParam.getBeginTime();
//        String endTime = chargerOrderParam.getEndTime();
//
//        if (beginTime != null && !StringUtils.equals("", beginTime) && !StringUtils.equals("null", beginTime)) {
//            searchMap.put("beginTime", DateUtils.timeStampToDate2(beginTime, "yyyy-MM-dd HH:mm:ss"));
//            chargerOrderParam.setBeginTime(DateUtils.timeStampToDate2(beginTime, "yyyy-MM-dd HH:mm:ss"));
//        }
//        if (endTime != null && !StringUtils.equals("", endTime) && !StringUtils.equals("null", endTime)) {
//            searchMap.put("endTime", DateUtils.timeStampToDate2(endTime, "yyyy-MM-dd HH:mm:ss"));
//            chargerOrderParam.setEndTime(DateUtils.timeStampToDate2(endTime, "yyyy-MM-dd HH:mm:ss"));
//        }
//
//        if (StringUtils.isNotBlank(chargerOrderParam.getCardChipNo())) {
//            searchMap.put("cardChipNo", chargerOrderParam.getCardChipNo());
//        }
//        if (chargerOrderParam.getOrderNos() != null && chargerOrderParam.getOrderNos().size() > 0) {
//            searchMap.put("orderNos", chargerOrderParam.getOrderNos());
//        }
//        if (chargerOrderParam.getChannelIdList() != null && chargerOrderParam.getChannelIdList().size() > 0) {
//            searchMap.put("channelIdList", chargerOrderParam.getChannelIdList());
//        }
//        if (chargerOrderParam != null && chargerOrderParam.getMerchantId() != null) {
//            ListResponse<Long> idListResponse = userFeignClient.queryIdsByBlocUserId(chargerOrderParam.getMerchantId());
//            FeignResponseValidate.check(idListResponse);
//            searchMap.put("rBlocUserIds", idListResponse.getData());
//        }
//
//        //扣款账户 账户类型-账户标识
//        searchMap.put("payAccountList", chargerOrderParam.getPayAccountList());
////        if (CollectionUtils.isNotEmpty(chargerOrderParam.getPayAccountList())) {
////            List<PayAccount> payAccountList = new ArrayList<>();
////            chargerOrderParam.getPayAccountList().forEach(o -> {
////                PayAccount payAccount = new PayAccount();
////                if (o == null) {
////                    return;
////                }
////                if (o.contains("-")) {
////                    String[] payAccountCode=o.split("\\-");
////                    if (payAccountCode.length < 2) {
////                        return;
////                    }
////                    try {
////                        if ("4".equals(payAccountCode[0])) {//即充即退账户
////                            payAccount.setPayAccountOrderType(Integer.parseInt(payAccountCode[1]));
////                        } else {
////                            payAccount.setPayAccountId(Long.parseLong(payAccountCode[1]));
////                        }
////                        payAccount.setDefaultPayType(Integer.parseInt(payAccountCode[0]));
////                    } catch (ClassCastException e) {
////                        return;
////                    }
////                    if (payAccount != null) {
////                        payAccountList.add(payAccount);
////                    }
////                }
////            });
////            searchMap.put("payAccountList", payAccountList);
////        }
//        //支付方式 账户类型或账户类型-orderType
//        searchMap.put("payTypeList", chargerOrderParam.getPayTypeList());

    /// /        if (CollectionUtils.isNotEmpty(chargerOrderParam.getPayTypeList())) { /
    /// List<PayAccount> payTypeList = new ArrayList<>(); /
    /// chargerOrderParam.getPayTypeList().forEach(o -> { /                PayAccount payAccount =
    /// new PayAccount(); /                if (o == null) { /                    return; / } / if
    /// (o.contains("-")) { /                    String[] payTypeCode=o.split("\\-"); / if
    /// (payTypeCode.length < 2) { / return; /                    } / if
    /// ("4".equals(payTypeCode[0])) {//即充即退账户 /
    /// payAccount.setPayAccountOrderType(Integer.parseInt(payTypeCode[1])); /                    }
    /// else { /                        return; /                    } /
    /// payAccount.setDefaultPayType(Integer.parseInt(payTypeCode[0])); /                } else { /
    /// payAccount.setDefaultPayType(Integer.parseInt(o)); /                } / if (payAccount !=
    /// null) { /                    payTypeList.add(payAccount); / } /            }); /
    /// searchMap.put("payTypeList", payTypeList); / }
//        searchMap.put("lineNum", chargerOrderParam.getLineNum());
//
//        log.info("订单查询条件:{}", searchMap);
//
//        ChargerOrderDataVo chargerOrderDataVo = chargerOrderMapper.getChargerOrderData(chargerOrderParam);
//        String count = chargerOrderDataVo.getChargerOrderNumber();//查询记录的总数
//        return Integer.parseInt(count) >= 100000;
//    }
    private List<ChargerOrderDetailVO> transChargerOrderDetail(
        List<ChargerOrderDataVo> chargerOrderVoList) {

        List<ChargerOrderDetailVO> chargerOrderDetailVOList = new ArrayList<>();

        chargerOrderVoList.forEach(vo -> {
            ChargerOrderDetailVO v = new ChargerOrderDetailVO();
            switch (vo.getTag()) {
                case "0":
                    v.setTag("汇总");
                    break;
                case "1":
                    v.setTag("尖时");
                    break;
                case "2":
                    v.setTag("峰时");
                    break;
                case "3":
                    v.setTag("平时");
                    break;
                case "4":
                    v.setTag("谷时");
                    break;
            }
            v.setChargerOrderNumber(new BigDecimal(vo.getChargerOrderNumber().toString()));
            v.setElecPriceAmount(vo.getElecPriceAmount());
            v.setOrderElectricityAmount(vo.getOrderElectricityAmount());
            v.setOrderPriceAmount(vo.getOrderPriceAmount());
            v.setServicePriceAmount(vo.getServicePriceAmount());
            chargerOrderDetailVOList.add(v);
        });
        log.info("返回数据" + JsonUtils.toJsonString(chargerOrderDetailVOList));
        return chargerOrderDetailVOList;
    }

    /**
     * 将ChargerOrderVo对象转换成ChargerOrderExportVO对象
     *
     * @param chargerOrderVoList
     * @return
     */
    private List<ChargerOrderExportVO> transChargerOrder(List<ChargerOrderVo> chargerOrderVoList,
        List<Dict> orderSourceLst, List<Dict> orderStatusLst, Locale locale) {

//        List<Dict> orderSourceLst = dictService.findDictDataByType("orderSource").getData();
//        List<Dict> orderStatusLst = dictService.findDictDataByType("orderStatus").getData();
        final Map<String, String> orderSourceMap = new HashMap<>();
        final Map<String, String> orderSourceI18nMap = new HashMap<>();
        final Map<String, String> orderStatusMap = new HashMap<>();
        final Map<String, String> orderStatusI18nMap = new HashMap<>();
        final Map<String, String> orderStartTypesMap = new HashMap<>();
        final Map<String, String> orderStartTypesI18nMap = new HashMap<>();
        final Map<String, String> orderAbnormalsMap = new HashMap<>();
        final Map<String, String> orderAbnormalsI18nMap = new HashMap<>();
        final Map<String, String> settlementTypesMap = new HashMap<>();
        if (orderSourceLst != null && !orderSourceLst.isEmpty()) {
            for (Dict dict : orderSourceLst) {
                orderSourceMap.put(dict.getValue(), dict.getLabel());

                if (!orderSourceI18nMap.containsKey(dict.getValue())) {
                    orderSourceI18nMap.put(dict.getValue(),
                        messageSource.getMessage(dict.getLabel() + "", null,
                            locale));
                }
            }
        }
        log.info("orderSourceMap: {}", JsonUtils.toJsonString(orderSourceMap));
        if (orderStatusLst != null && !orderStatusLst.isEmpty()) {
            for (Dict dict : orderStatusLst) {
                orderStatusMap.put(dict.getValue(), dict.getLabel());

                if (!orderStatusI18nMap.containsKey(dict.getValue())) {
                    orderStatusI18nMap.put(dict.getValue(),
                        messageSource.getMessage(dict.getLabel() + "", null, locale));
                }
            }
        }
        log.info("orderStatusMap: {}", JsonUtils.toJsonString(orderStatusMap));
        List<Dict> orderStartTypes = dictService.findDictDataByType("orderStartType");
        if (orderStartTypes != null && !orderStartTypes.isEmpty()) {
            for (Dict dict : orderStartTypes) {
                orderStartTypesMap.put(dict.getValue(), dict.getLabel());

                if (!orderStartTypesI18nMap.containsKey(dict.getValue())) {
                    orderStartTypesI18nMap.put(dict.getValue(),
                        messageSource.getMessage(dict.getLabel(), null, locale));
                }
            }
        }

        List<Dict> orderAbnormals = dictService.findDictDataByType("orderAbnormalReason");
        if (orderAbnormals != null && !orderAbnormals.isEmpty()) {
            for (Dict dict : orderAbnormals) {
                orderAbnormalsMap.put(dict.getValue(), dict.getLabel());

                if (!orderAbnormalsI18nMap.containsKey(dict.getValue())) {
                    orderAbnormalsI18nMap.put(dict.getValue(), messageSource.getMessage(
                        dict.getLabel(), null,
                        locale));
                }
            }
        }

        List<Dict> orderSettlements = dictService.findDictDataByType("orderSettlementType");
        if (orderSettlements != null && !orderSettlements.isEmpty()) {
            for (Dict dict : orderSettlements) {
                settlementTypesMap.put(dict.getValue(), dict.getLabel());
            }
        }
//        log.info("settlementTypesMap: {}", JsonUtils.toJsonString(settlementTypesMap));

        List<ChargerOrderExportVO> chargerOrderExportVOList = new ArrayList<>();
//        log.info("chargerOrderVoList.size = {}", chargerOrderVoList == null ? null : chargerOrderVoList.size());
        chargerOrderVoList.forEach(vo -> {
            ChargerOrderExportVO v = new ChargerOrderExportVO();
            v.setOrderNo(vo.getOrderNo());
            v.setBillNo(vo.getBillNo());
            v.setCouponId(vo.getCouponId());
            v.setCouponAmount(vo.getCouponAmount());
            v.setScoreAmount(vo.getScoreAmount());
            v.setAbcCouponAmount(vo.getAbcCouponAmount());
            v.setPayOrderNo(vo.getPayOrderNo());
            v.setRefundOrderNo(vo.getRefundOrderNo());
            v.setStationName(vo.getStationName());
            //v.setBcCode(vo.getBcCode());
            v.setPlugNo(vo.getPlugNo());
            v.setChargerName(vo.getChargerName());
            v.setCardChipNo(vo.getCardChipNo());
            v.setVin(vo.getVin());
            v.setMobilePhone(vo.getMobilePhone());
            v.setEmail(vo.getEmail());
            v.setCustomerName(vo.getCustomerName());
            v.setCarNo(vo.getCarNo());
            if (vo.getChargeStartTime() != null) {
                v.setChargeStartTime(
                    DateUtils.timeStampToDate(vo.getChargeStartTime() + "", "yyyy-MM-dd HH:mm:ss"));
            }
            if (vo.getChargeEndTime() != null) {
                v.setChargeEndTime(
                    DateUtils.timeStampToDate(vo.getChargeEndTime() + "", "yyyy-MM-dd HH:mm:ss"));
            }
            if (StringUtils.isNotBlank(vo.getDuration())) {
                try {
                    v.setDuration(DateUtils.secToTime(
                        (int) Math.round(Double.parseDouble(vo.getDuration()))));
                } catch (NumberFormatException e) {
//                    log.info("message: {}", e.getMessage(), e);
                    v.setDuration("00:00:00");
                }
            }
//            log.info("duration: {}", v.getDuration());
//            v.setOrderElectricity(vo.getOrderElectricity() == null ? null : new Double(vo.getOrderElectricity()) / 100);
            v.setOrderElectricity(
                vo.getOrderElectricity() == null ? null : vo.getOrderElectricity());
//            Long startSoc = vo.getStartSoc() != null ? vo.getStartSoc() : "--";
//            Long stopSoc = vo.getStopSoc() ? vo.getStopSoc() : "--";
//            v.setSocField(startSoc + "%-" + stopSoc + "%");
            if (StringUtils.isNotBlank(vo.getStartSoc())) {
                v.setStartSoc(Long.parseLong(vo.getStartSoc()));
            }

            if (StringUtils.isNotBlank(vo.getStopSoc())) {
                v.setStopSoc(Long.parseLong(vo.getStopSoc()));
            }
            v.setElecOriginFee(vo.getElecOriginFee() == null ? null : vo.getElecOriginFee());
            v.setServOriginFee(vo.getServOriginFee() == null ? null : vo.getServOriginFee());

            v.setElecPrice(vo.getElecPrice() == null ? null : vo.getElecPrice());
            v.setDiscount(vo.getDiscount() == null ? null : vo.getDiscount());
            v.setServicePrice(vo.getServicePrice() == null ? null : vo.getServicePrice());
            v.setOrderPrice(vo.getOrderPrice() == null ? null : vo.getOrderPrice());
            //v.setActualPrice(vo.getActualPrice() == null ? null : new Double(vo.getActualPrice()) / 100);
//            v.setSource(orderSourceMap.get(vo.getChannelId() + ""));
//            v.setOrderStatus(orderStatusMap.get(vo.getStatus() + ""));
            // 中文unicode映射多语言
            // 处理来源
            v.setSource(null == locale ? orderSourceMap.get(vo.getChannelId() + "")
                : orderSourceI18nMap.get(vo.getChannelId() + ""));
            // 处理订单状态
            v.setOrderStatus(null == locale ? orderStatusMap.get(vo.getStatus() + "")
                : orderStatusI18nMap.get(vo.getStatus() + ""));
//            log.info("getChannelId: {}", vo.getChannelId());
//            log.info("getStatus: {}", vo.getStatus());
            v.setCreateTime(DateUtils.getStringDate(vo.getCreateTime()));
            v.setStopTime(DateUtils.getStringDate(vo.getStopTime()));

            v.setPayAccountName(vo.getPayAccountName());
//             处理扣款账户（海外版已拿掉该字段，因此注释掉）
//            if (vo.getPayAccountName() != null) {
//                v.setPayAccountName(null == locale ? vo.getPayAccountName()
//                    : messageSource.getMessage(vo.getPayAccountName(), null, locale));
//            }
            v.setBlocUserName(vo.getBlocUserName());
            v.setCardName(vo.getCardName());
            v.setCarDepart(vo.getCarDepart());
            v.setCarNum(vo.getCarNum());
            v.setLineNum(vo.getLineNum());
            v.setBrand(vo.getBrand());
            v.setModel(vo.getModel());
            v.setBmsVoltage(vo.getBmsVoltage());
            v.setGbVer(
                vo.getGbVer() != null ? GbVerType.getValueByCode(vo.getGbVer()).getValue() : null);
            v.setCommercialFullName(vo.getCommercialFullName());
            v.setBoxCode(vo.getBoxCode());
            v.setEvseName(vo.getEvseName());
            v.setConnectorId(vo.getConnectorId());
//            v.setStopReason(vo.getStopReason());
            // 处理停充原因
            if (vo.getStopReason() != null) {
                v.setStopReason(null == locale ? vo.getStopReason()
                    : messageSource.getMessage(vo.getStopReason(), null, locale));
            }
            v.setSiteNo(vo.getSiteNo());
            v.setTipElectricity(vo.getTipElectricity() == null ? null : vo.getTipElectricity());
            v.setTipServicePrice(vo.getTipServicePrice() == null ? null : vo.getTipServicePrice());
            v.setTipServiceUnit(vo.getTipServiceUnit());
            v.setTipElecPrice(vo.getTipElecPrice() == null ? null : vo.getTipElecPrice());
            v.setTipElectricUnit(vo.getTipElectricUnit());
            v.setTipSumPrice(vo.getTipSumPrice() == null ? null : vo.getTipSumPrice());
            v.setPeakElectricity(vo.getPeakElectricity() == null ? null : vo.getPeakElectricity());
            v.setPeakServicePrice(
                vo.getPeakServicePrice() == null ? null : vo.getPeakServicePrice());
            v.setPeakServiceUnit(vo.getPeakServiceUnit());
            v.setPeakElecPrice(vo.getPeakElecPrice() == null ? null : vo.getPeakElecPrice());
            v.setPeakElectricUnit(vo.getPeakElectricUnit());
            v.setPeakSumPrice(vo.getPeakSumPrice() == null ? null : vo.getPeakSumPrice());
            v.setFlatElectricity(vo.getFlatElectricity() == null ? null : vo.getFlatElectricity());
            v.setFlatServicePrice(
                vo.getFlatServicePrice() == null ? null : vo.getFlatServicePrice());
            v.setFlatServiceUnit(vo.getFlatServiceUnit());
            v.setFlatElecPrice(vo.getFlatElecPrice() == null ? null : vo.getFlatElecPrice());
            v.setFlatElectricUnit(vo.getFlatElectricUnit());
            v.setFlatSumPrice(vo.getFlatSumPrice() == null ? null : vo.getFlatSumPrice());
            v.setValleyElectricity(
                vo.getValleyElectricity() == null ? null : vo.getValleyElectricity());
            v.setValleyServicePrice(
                vo.getValleyServicePrice() == null ? null : vo.getValleyServicePrice());
            v.setValleyServiceUnit(vo.getValleyServiceUnit());
            v.setValleyElecPrice(vo.getValleyElecPrice() == null ? null : vo.getValleyElecPrice());
            v.setValleyElectricUnit(vo.getValleyElectricUnit());
            v.setValleySumPrice(vo.getValleySumPrice() == null ? null : vo.getValleySumPrice());
            v.setPayTime(DateUtils.getStringDate(vo.getPayTime()));
            v.setStartElectricity(vo.getStartElectricity());
            v.setEndElectricity(vo.getEndElectricity());
            v.setOpenOrderId(vo.getOpenOrderId());
//            v.setOrderType(vo.getOrderType() == null ? null
//                : orderStartTypesMap.get(String.valueOf(vo.getOrderType())));
            // 处理启动方式
            if (vo.getOrderType() != null) {
                v.setOrderType(
                    null == locale ? orderStartTypesMap.get(String.valueOf(vo.getOrderType())) :
                        orderStartTypesI18nMap.get(String.valueOf(vo.getOrderType())));
            }

            CardType cardType = vo.getCardType();
            v.setCardType(cardType == null ? null : cardType.getName());
//            v.setAbnormal(vo.getAbnormal() == null ? null
//                : orderAbnormalsMap.get(String.valueOf(vo.getAbnormal().getCode())));
            // 处理异常原因
            if (vo.getAbnormal() != null
                && orderAbnormalsMap.get(String.valueOf(vo.getAbnormal().getCode())) != null) {
                v.setAbnormal(null == locale ? orderAbnormalsMap.get(
                    String.valueOf(vo.getAbnormal().getCode()))
                    : orderAbnormalsI18nMap.get(String.valueOf(vo.getAbnormal().getCode())));
            } else {
                v.setAbnormal(null == vo.getAbnormal() ? null
                    : orderAbnormalsMap.get(String.valueOf(vo.getAbnormal().getCode())));
            }

            v.setPrincipalAmount(vo.getActualPrice());
//            log.info("principalAmount: {}", vo.getPrincipalAmount());
            v.setPrincipalAmount(vo.getPrincipalAmount());
//            log.info("freeGoldAmount: {}", vo.getFreeGoldAmount());

            v.setFreeGoldAmount(vo.getFreeGoldAmount());  //不可开票金额
            v.setFreeAmount(vo.getFreeGoldAmount());    //赠送金额消费

            if (vo.getTaxStatus() == TaxStatus.NO) {
                v.setUninvoiceAmount(vo.getInvoiceAmount());
                v.setInvoicedAmount(vo.getPrincipalAmount() == null ?
                    BigDecimal.ZERO : vo.getInvoiceAmount() == null ? vo.getPrincipalAmount()
                    : vo.getPrincipalAmount().subtract(vo.getInvoiceAmount()));
            } else { // 已开票
                v.setUninvoiceAmount(BigDecimal.ZERO);
                v.setInvoicedAmount(vo.getPrincipalAmount());
            }
//            v.setUninvoiceAmount(vo.getFreeGoldAmount());
            ProcessType processType = vo.getProcessType();
//            v.setProcessType(processType == null ? null : processType.getName());
            // 处理订单处理类型
            if (vo.getProcessType() != null) {
                v.setProcessType(null == locale ? processType.getName()
                    : messageSource.getMessage(processType.getName(), null, locale));
            }
//            log.info("settlementType: {}", vo.getSettlementType());
//            log.info("settlementType.code: {}", vo.getSettlementType().getCode());
            v.setSettlementType(vo.getSettlementType() == null ? null
                : settlementTypesMap.get(String.valueOf(vo.getSettlementType().getCode())));
            v.setParkingFee(vo.getParkingFee());

            // 互联互通类型: [显示文案和枚举文案不一致]
            switch (vo.getHlhtType()) {
                case REVERSE_HLHT:
                    v.setHlhtType("互联互通请求方");
                    break;
                case POSITIVE_HLHT:
                    v.setHlhtType("互联互通接收方");
                    break;
                default:
                    v.setHlhtType("非互联互通订单");
            }

            // 充电桩类型
            if (null != vo.getSupplyType()) {
                switch (vo.getSupplyType()) {
                    case DC:
                        v.setSupplyType("直流");
                        break;
                    case AC:
                        v.setSupplyType("交流");
                        break;
                    default:
                        v.setSupplyType("未知");
                }
            } else {
                v.setSupplyType("未知");
            }

            v.setElecProfit(vo.getElecProfit());
            v.setServProfit(vo.getServProfit());
            v.setTotalProfit(vo.getTotalProfit());
            v.setCheckResult(vo.getCheckResult() == null ? null : vo.getCheckResult().getDesc());
            v.setZftTotalMoney(
                vo.getZftTotalMoney() == null ? BigDecimal.ZERO : vo.getZftTotalMoney());
            BigDecimal orderFee = vo.getOrderPrice() == null ? BigDecimal.ZERO : vo.getOrderPrice();
            BigDecimal parkingFee =
                vo.getParkingFee() == null ? BigDecimal.ZERO : vo.getParkingFee();
            BigDecimal zftTotalMoney =
                vo.getZftTotalMoney() == null ? BigDecimal.ZERO : vo.getZftTotalMoney();
            v.setCheckDiffMoney(orderFee.add(parkingFee).subtract(zftTotalMoney));

            chargerOrderExportVOList.add(v);
        });
        log.info("chargerOrderExportVOList.size = {}",
            chargerOrderExportVOList == null ? null : chargerOrderExportVOList.size());
        return chargerOrderExportVOList;
    }

    /**
     * 商户会员订单格式化
     *
     * @param chargerOrderVoList
     * @param orderSourceLst
     * @param orderStatusLst
     * @return
     */
    private List<CommUserOrderExportVO> transCommUserOrder(List<ChargerOrderVo> chargerOrderVoList,
        List<Dict> orderSourceLst,
        List<Dict> orderStatusLst, List<Dict> payModesList) {
        final Map<String, String> orderSourceMap = new HashMap<>();
        final Map<String, String> orderStatusMap = new HashMap<>();
        final Map<String, String> payModesMap = new HashMap<>();

        if (orderSourceLst != null && !orderSourceLst.isEmpty()) {
            for (Dict dict : orderSourceLst) {
                orderSourceMap.put(dict.getValue(), dict.getLabel());
            }
        }

        if (orderStatusLst != null && !orderStatusLst.isEmpty()) {
            for (Dict dict : orderStatusLst) {
                orderStatusMap.put(dict.getValue(), dict.getLabel());
            }
        }

        if (payModesList != null && !payModesList.isEmpty()) {
            for (Dict dict : payModesList) {
                payModesMap.put(dict.getValue(), dict.getLabel());
            }
        }

        log.info("测试", payModesList);
        List<CommUserOrderExportVO> chargerOrderExportVOList = new ArrayList<>();
        chargerOrderVoList.forEach(vo -> {
            CommUserOrderExportVO v = new CommUserOrderExportVO();
            v.setOrderNo(vo.getOrderNo());
            v.setStationName(vo.getStationName());
            v.setCommercialFullName(vo.getCommercialFullName());
            v.setCardChipNo(vo.getCardChipNo());
            v.setVin(vo.getVin());

            v.setCarNo(vo.getCarNo());
            String startTime = "", endTime = "";
            if (vo.getChargeStartTime() != null) {
                startTime = DateUtils.timeStampToDate(vo.getChargeStartTime() + "",
                    "yyyy-MM-dd HH:mm:ss");
            }
            if (vo.getChargeEndTime() != null) {
                endTime = DateUtils.timeStampToDate(vo.getChargeEndTime() + "",
                    "yyyy-MM-dd HH:mm:ss");
            }

            v.setChargeTime(startTime + "/" + endTime);
            v.setActualPrice(vo.getActualPrice());
            v.setSource(orderSourceMap.get(vo.getChannelId() + ""));
            v.setOrderStatus(orderStatusMap.get(vo.getStatus() + ""));
            v.setPayModes(payModesMap.get(vo.getPayModes() + ""));
            chargerOrderExportVOList.add(v);
        });
        return chargerOrderExportVOList;
    }

//     /**
//      * 根据查询条件获取excel导出的数据(导出订单excel专用)
//      *
//      * @return
//      */
//     private List<ChargerOrderVo> exportChargeOrderList(ChargerOrderParam chargerOrderParam) {
//         log.info("订单导出查询  参数  -->" + JsonUtils.toJsonString(chargerOrderParam));
//
//         //查询条件中有commercialId的话，查询该商户下的商户及子商户列表commIdList，并覆盖原commIdList
//         if (chargerOrderParam.getCommercialId() != null) {
//
//             List<Long> commIdList = commercialQueryDs.listCommercialIds(chargerOrderParam.getCommercialId());
// //            ListResponse<Long> jsonObjectCommIdList = merchantFeignClient.getCommIdListByCommId(chargerOrderParam.getCommercialId());
// //            FeignResponseValidate.check(jsonObjectCommIdList);
// //            List<Long> commIdList = jsonObjectCommIdList.getData();
//
//             log.info("commIdList:::" + JsonUtils.toJsonString(commIdList));
//
//             if (CollectionUtils.isEmpty(commIdList)) {
//                 throw new DcServiceException("获取当前商户及子商户列表失败");
//             }
//             chargerOrderParam.setCommIdList(commIdList);
//             chargerOrderParam.setCommercialId(null);
//             log.info("订单导出查询  参数  -->" + JsonUtils.toJsonString(chargerOrderParam));
//         }
//
//         List<ChargerOrderVo> list = new ArrayList<>();
//
//         HashMap<String, Object> searchMap = new HashMap<>();
//         MyBeanUtils.copyBean2Map(searchMap, chargerOrderParam);
//
//         String beginTime = chargerOrderParam.getBeginTime();
//         String endTime = chargerOrderParam.getEndTime();
//
//         if (beginTime != null && !StringUtils.equals("", beginTime) && !StringUtils.equals("null", beginTime)) {
//             searchMap.put("beginTime", DateUtils.timeStampToDate2(beginTime, "yyyy-MM-dd HH:mm:ss"));
//             chargerOrderParam.setBeginTime(DateUtils.timeStampToDate2(beginTime, "yyyy-MM-dd HH:mm:ss"));
//         }
//         if (endTime != null && !StringUtils.equals("", endTime) && !StringUtils.equals("null", endTime)) {
//             searchMap.put("endTime", DateUtils.timeStampToDate2(endTime, "yyyy-MM-dd HH:mm:ss"));
//             chargerOrderParam.setEndTime(DateUtils.timeStampToDate2(endTime, "yyyy-MM-dd HH:mm:ss"));
//         }
//
//         if (StringUtils.isNotBlank(chargerOrderParam.getCardChipNo())) {
//             searchMap.put("cardChipNo", chargerOrderParam.getCardChipNo());
//         }
//         if (chargerOrderParam.getOrderNos() != null && chargerOrderParam.getOrderNos().size() > 0) {
//             searchMap.put("orderNos", chargerOrderParam.getOrderNos());
//         }
//         if (chargerOrderParam.getChannelIdList() != null && chargerOrderParam.getChannelIdList().size() > 0) {
//             searchMap.put("channelIdList", chargerOrderParam.getChannelIdList());
//         }
//         if (chargerOrderParam != null && chargerOrderParam.getMerchantId() != null) {
//             ListResponse<Long> idListResponse = userFeignClient.queryIdsByBlocUserId(chargerOrderParam.getMerchantId());
//             FeignResponseValidate.check(idListResponse);
//             searchMap.put("rBlocUserIds", idListResponse.getData());
//         }
//         log.info("订单查询条件:{}", searchMap);
//
//         ChargerOrderDataVo chargerOrderDataVo = chargerOrderMapper.getChargerOrderData(chargerOrderParam);
//         String count = chargerOrderDataVo.getChargerOrderNumber();//查询记录的总数
//         int totalCount = 0;
//         try {
//             totalCount = Integer.parseInt(count);
//         } catch (NumberFormatException e) {
//             throw new DcServiceException("系统错误");
//         }
//
//         if (totalCount > 100000) {
//             throw new DcServiceException("已超出最大订单导出条数（导出订单条数最多10万条）！减少订单导出条数后，再导出。");
//         }
//
//         try {
//             list = getChargerOrderVo2(totalCount, searchMap);
//         } catch (Exception e) {
//             throw new DcServiceException("系统错误");
//         }
//
//         Set<String> vinSet = new HashSet<>();
//         Set<String> cardSet = new HashSet<>();
//         Set<String> connectIdSet = new HashSet<>();
//         Set<String> boxCodeSet = new HashSet<>();
//         Set<String> orderNoSet = new HashSet<>();
//         Set<Long> commercialIdSet = new HashSet<>();
//
//         for (ChargerOrderVo chargerOrder : list) {
//             String orderVin = chargerOrder.getVin();
//             String orderCardNo = chargerOrder.getCardNo();
//             String connectId = chargerOrder.getConnectId();
//             String boxCode = chargerOrder.getBoxCode();
//             String orderNo1 = chargerOrder.getOrderNo();
//             Long commercialId = chargerOrder.getCommercialId();
//
//             if (!StringUtils.isBlank(orderVin)) {
//                 vinSet.add(orderVin);
//             }
//             if (!StringUtils.isBlank(orderCardNo)) {
//                 cardSet.add(orderCardNo);
//             }
//             if (!StringUtils.isBlank(connectId)) {
//                 connectIdSet.add(connectId);
//             }
//             if (!StringUtils.isBlank(boxCode)) {
//                 boxCodeSet.add(boxCode);
//             }
//             if (!StringUtils.isBlank(orderNo1)) {
//                 orderNoSet.add(orderNo1);
//             }
//             if (commercialId != null) {
//                 commercialIdSet.add(commercialId);
//             }
//         }
//
//         Map<String, String> vinsMap = null;
// //        Map<String, String> chargerNamesMap = null;
//         log.info("vinSet~~~~~~~~~~~~~:{}", vinSet.size());
//         if (vinSet != null && vinSet.size() > 0) {
//             ObjectResponse vinListEntity = userFeignClient.selectCarNoByVins(new ArrayList<>(vinSet));
//             if (vinListEntity != null && vinListEntity.getData() != null) {
//                 vinsMap = (Map<String, String>) vinListEntity.getData();
//             }
//         }
//
//         log.info("commercialIdSet~~~~~~~~~~~~~:{}", commercialIdSet.size());
// //        if (commercialIdSet != null && commercialIdSet.size() > 0) {
// //            ObjectResponse<Map<String, String>> chargerNamesEntity = bsChargerFeginClient.selectChargerNamesByBusinessIds(new ArrayList<>(commercialIdSet));
// //            if (chargerNamesEntity != null && chargerNamesEntity.getData() != null) {
// //                chargerNamesMap = chargerNamesEntity.getData();
// //            }
// //        }
//
//         for (ChargerOrderVo chargerOrder : list) {
//             if (vinsMap != null && chargerOrder.getVin() != null) {
//                 chargerOrder.setCarNo(vinsMap.get(chargerOrder.getVin()));
//             }
//
// //            if (chargerNamesMap != null && chargerOrder.getConnectId() != null) {
// //                chargerOrder.setChargerName(chargerNamesMap.get(chargerOrder.getConnectId()));
// //            }
//
//             /**
//              * 转换枪头编号bcCode格式：桩号-枪头序号
//              */
//             String bcCode = chargerOrder.getBoxOutFactoryCode() + "-" + chargerOrder.getConnectorId();
//             chargerOrder.setBcCode(bcCode);
//         }
//
//         return list;
//     }

    // /**
    //  * 查询订单数据
    //  *
    //  * @param totalCount
    //  * @param searchMap
    //  * @return
    //  * @throws Exception
    //  */
    // public List<ChargerOrderVo> getChargerOrderVo2(int totalCount, Map<String, Object> searchMap) throws Exception {
    //     return chargerOrderMapper.selectChargeOrderListForExcel(searchMap);
    //
    // }

    @Async
    public void exportVinOrderListX(ChargerOrderParam param) {
        log.info("exportVinOrderList param: {}", param);
        try {
            this.exportVinOrderList(param);
        } catch (Exception e) {
            log.error("导出excel异常: {}, err = {}",
                JsonUtils.toJsonString(param.getExcelPosition()), e.getMessage(), e);
        }
    }

    public void exportVinOrderList(ChargerOrderParam param) throws IOException {
//        log.info("exportVinOrderList param: {}", param);
//        try {
        ObjectResponse<VinDto> vinDtoObjectResponse = userFeignClient.selectByVin(param.getVin(),
            param.getTopCommId());
        FeignResponseValidate.check(vinDtoObjectResponse);
        VinDto vinDto = vinDtoObjectResponse.getData();
        VinExportHeaderVo headerVo = new VinExportHeaderVo();
//            headerVo.setVin(vinDto.getVin())
//                    .setCarNo(vinDto.getCarNo())
//                    .setCarDepart(vinDto.getCarDepart())
//                    .setLineNum(vinDto.getLineNum())
//                    .setCarNum(vinDto.getCarNum())
//                    .setBrand(vinDto.getBrand())
//                    .setModel(vinDto.getModel())
//                    .setCarLength(vinDto.getCarLength())
//                    .setYear(vinDto.getYear());
        BeanUtils.copyProperties(vinDto, headerVo);

        ExcelUtil.builder(exportFileConfig.getExcelDir(), param.getExcelPosition(),
                BiExportGroups.VIN_ORDER.getName())
            .addHeader(VinExportHeaderVo.class)
            .appendDataList(List.of(headerVo))
            .addHeader(VinExportBodyVo.class)
            .loopAppendData((index, size) -> {
                    param.setSize(size);
                    param.setCurrent(index);
                    ListResponse<ChargerOrderVo> res = orderService.exportChargeOrderListV2(param);
                    return new ArrayList<>(res.getData());
                },
                e -> new ArrayList<>(this.convert(
                    e.stream().map(x -> (ChargerOrderVo) x).collect(Collectors.toList()),
                    param.getOrderSourceLst(),
                    param.getOrderStatusLst(),
                    param.getPayTypeDictList())))
            .write2File();
        log.info("导出excel完成: {}", JsonUtils.toJsonString(param.getExcelPosition()));
//        } catch (Exception e) {
//            log.error("导出excel异常: {}, err = {}",
//                    JsonUtils.toJsonString(param.getExcelPosition()), e.getMessage(), e);
//        }
    }

    private List<VinExportBodyVo> convert(List<ChargerOrderVo> chargerOrderVoList,
        List<Dict> orderSourceLst,
        List<Dict> orderStatusLst,
        List<Dict> payTypeLst) {
        final Map<String, String> orderSourceMap = new HashMap<>();
        final Map<String, String> orderStatusMap = new HashMap<>();
        final Map<String, String> payTypeMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(orderSourceLst)) {
            for (Dict dict : orderSourceLst) {
                orderSourceMap.put(dict.getValue(), dict.getLabel());
            }
        }
        log.info("orderSourceMap: {}", JsonUtils.toJsonString(orderSourceMap));
        if (CollectionUtils.isNotEmpty(orderStatusLst)) {
            for (Dict dict : orderStatusLst) {
                orderStatusMap.put(dict.getValue(), dict.getLabel());
            }
        }
        log.info("orderStatusMap: {}", JsonUtils.toJsonString(orderStatusMap));
        if (CollectionUtils.isNotEmpty(payTypeLst)) {
            for (Dict dict : payTypeLst) {
                payTypeMap.put(dict.getValue(), dict.getLabel());
            }
        }
        log.info("payTypeMap: {}", JsonUtils.toJsonString(payTypeMap));

        List<VinExportBodyVo> res = new ArrayList<>();

        chargerOrderVoList.forEach(e -> {
            VinExportBodyVo v = new VinExportBodyVo();
            v.setOrderNo(e.getOrderNo())
                .setCommName(e.getCommercialName())
                .setSiteName(e.getStationName())
                .setOrderChannel(orderSourceMap.get(e.getChannelId() + ""))
                .setPayType(payTypeMap.get(
                    NumberUtils.equals(OrderPayType.PREPAY.getCode(), e.getDefaultPayType())
                        ? (e.getDefaultPayType() + "-" + e.getOrderType())
                        : e.getDefaultPayType() + ""
                ))
                .setChargingTime(
                    "开始时间: " + (e.getChargeStartTime() != null ?
                        DateUtils.timeStampToDate(e.getChargeStartTime() + "",
                            "yyyy-MM-dd HH:mm:ss")
                        : "--")
                        + "\n"
                        + "结束时间: " + (e.getChargeEndTime() != null ?
                        DateUtils.timeStampToDate(e.getChargeEndTime() + "", "yyyy-MM-dd HH:mm:ss")
                        : "--")
                )
                .setOrderElectric(e.getOrderElectricity())
                .setElecFee(e.getElecPrice())
                .setServFee(e.getServicePrice())
                .setOrderFee(e.getOrderPrice())
                .setStatus(orderStatusMap.get(e.getStatus() + ""))
                .setCreateTime(e.getCreateTime())
                .setDebtAccount(e.getPayAccountName());
            res.add(v);
        });

        return res;
    }


    public boolean existsDownFile(String subDir, String subFileName) {
        //String fileName = DownloadType.OFFLINE_ORDER.getCode().equals(type) ? DownloadType.OFFLINE_ORDER.getName() + ".xlsx" : DownloadType.ONLINE_ORDER.getName() + ".xlsx";
        String fileName = subFileName + ".xlsx";
        String dir = exportFileConfig.getExcelDir() + File.separator + subDir;
        String filePath = dir + File.separator + fileName;

        log.info("判断existsDownFile, filePath:{}", filePath);

        File file1 = new File(filePath);
        return file1.exists();
    }

    private void deleteFile(File file) {
        if (file.isDirectory()) {
            //递归删除文件夹下所有文件
            File[] files = file.listFiles();
            for (File f : files) {
                deleteFile(f);
            }

            //删除文件夹自己
            if (file.listFiles().length == 0) {
                log.info("删除文件夹：[{}]", file);
                file.delete();
            }
        } else {
            // 如果是文件,就直接删除自己
            log.info("删除文件：[{}]", file);
            file.delete();
        }
    }

//    public void exportExcelFile(ChargeOrderBiDs.Function<List<Serializable>, Long, Integer> f) {
//        long start = 0;
//        int size = 1000;
//        while (true) {
//            List<Serializable> serializables = f.apply(start, size);
//            if (CollectionUtils.isEmpty(serializables)) {
//                log.info("获取报表数据结束.");
//                break;
//            } else {
//
//                start += 1000;
//            }
//        }
//
//    }

    public void cleanExcelFiles() {
        log.info("开始删除文件...");
        long startTime = System.currentTimeMillis();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

        //删除子文件夹
        File deleteFile = new File(exportFileConfig.getExcelDir());
        File[] deleteFiles = deleteFile.listFiles();
        if (null == deleteFiles) {
            return;
        }

        for (File file : deleteFiles) {
            if (exportFileConfig.getDownloadExtDir().equals(file.getName())) {
                continue; // 下载任务单独清理
            }

            boolean needDel = false;
            try {
                Date fileCreateDate = sdf.parse(file.getName());
                needDel = sdf.parse(sdf.format(new Date())).compareTo(fileCreateDate) > 0;
            } catch (ParseException e) {
                log.error("子文件夹命名不合规范, filename = {}", file.getName());
                needDel = true;
            }
            //删除今天之前的文件夹及其子文件
            if (needDel) {
                deleteFile(file);
            }
        }
        log.info("删除文件结束,总耗时：[{}]毫秒", System.currentTimeMillis() - startTime);
    }

    public void downFile(String subDir, String subFileName, HttpServletResponse response) {
        if (existsDownFile(subDir, subFileName)) {
            //String fileName = DownloadType.OFFLINE_ORDER.getCode().equals(type) ? DownloadType.OFFLINE_ORDER.getName() + ".xlsx" : DownloadType.ONLINE_ORDER.getName() + ".xlsx";
            String fileName = subFileName + ".xlsx";
            String dir = exportFileConfig.getExcelDir() + File.separator + subDir;
            String filePath = dir + File.separator + fileName;
            log.info("downFile, filePath:{}", filePath);
            try {
                response.setContentType("application/vnd..ms-excel");
                response.setHeader("content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(fileName, CHARSET));
                //读取指定路径下面的文件
                InputStream in = new FileInputStream(filePath);
                OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
                //创建存放文件内容的数组
                byte[] buff = new byte[1024];
                //所读取的内容使用n来接收
                int n;
                //当没有读取完时,继续读取,循环
                while ((n = in.read(buff)) != -1) {
                    //将字节数组的数据全部写入到输出流中
                    outputStream.write(buff, 0, n);
                }
                //强制将缓存区的数据进行输出
                outputStream.flush();
                //关流
                outputStream.close();
                in.close();
            } catch (Exception e) {
                log.error("导出订单excel失败");
            }
        } else {
            log.error("excel文件不存在");
        }
    }


    @FunctionalInterface
    public interface Function<Uno, Dos, Tres> {

        Uno apply(Dos dos, Tres tres);
    }


    @FunctionalInterface
    public interface FunctionTrans<Uno, Dos> {

        Uno apply(Dos dos);
    }
}
