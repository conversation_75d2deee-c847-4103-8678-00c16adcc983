package com.cdz360.biz.bi.domain.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 *  订单尖峰平谷导出VO
 * @since 2016年9月18日 下午2:36:25
 */
@Data
public class ChargerOrderDetailI18nVO implements Serializable {

    /****/
    @ExcelField(title = "    ", sort = 1)
    private String tag;
    /**
     * 订单总数(条)
     **/
    @ExcelField(title = "订单总数(条)", i18nTitle = "charger.order.totalNumber", sort = 2, digits = 0)
    private BigDecimal chargerOrderNumber;

    /**
     * 订单总金额(元)
     **/
    @ExcelField(title = "订单总金额(元)", i18nTitle = "charger.order.totalPrice", sort = 3)
    private BigDecimal orderPriceAmount;

    /**
     * 总电费(元)
     **/
    @ExcelField(title = "总电费(元)", i18nTitle = "charger.order.totalElecCosts", sort = 4)
    private BigDecimal elecPriceAmount;

    /**
     * 总服务费(元)
     **/
    @ExcelField(title = "总服务费(元)", i18nTitle = "charger.order.totalServiceCosts", sort = 5)
    private BigDecimal servicePriceAmount;

    /**
     * 订单总电量(kW·h)
     **/
    @ExcelField(title = "订单总电量(kW·h)", i18nTitle = "charger.order.totalOrderPower", sort = 6, digits = 4)
    private BigDecimal orderElectricityAmount;

}
