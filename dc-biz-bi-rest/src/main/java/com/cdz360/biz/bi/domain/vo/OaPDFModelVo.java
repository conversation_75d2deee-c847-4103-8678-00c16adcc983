package com.cdz360.biz.bi.domain.vo;

import com.cdz360.biz.model.site.vo.SiteIncomeExpenseVo;
import com.cdz360.biz.oa.vo.OaTaskCommentVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "流程PDF导出使用模型")
@Data
@Accessors(chain = true)
public class OaPDFModelVo {

    @Schema(description = "流程ID")
    @JsonInclude(Include.NON_EMPTY)
    private String oaId;

    @Schema(description = "流程定义KEY")
    @JsonInclude(Include.NON_EMPTY)
    private String oaDefKey;

    @Schema(description = "流程名称")
    @JsonInclude(Include.NON_EMPTY)
    private String oaName;

    // 流程实例附言信息
    @Schema(description = "流程实例附言信息")
    @JsonInclude(Include.NON_NULL)
    private List<OaTaskCommentVo> commentList;

    // 历史台账信息
    @Schema(description = "流程历史台账信息(需要根据流程决定是否需要)")
    @JsonInclude(Include.NON_NULL)
    private List<SiteIncomeExpenseVo> historyLedgerList;

    // 是否显示电损
    @Schema(description = "是否显示电损")
    @JsonInclude(Include.NON_NULL)
    private Boolean showLossRate;

    @Deprecated(since = "20230801")
    @Schema(description = "表单字段数据")
    @JsonInclude(Include.NON_NULL)
    private List<OaFieldItem> fieldItems;

    @Schema(description = "表单数据")
    @JsonInclude(Include.NON_NULL)
    private Map<String, Object> formData;
}
