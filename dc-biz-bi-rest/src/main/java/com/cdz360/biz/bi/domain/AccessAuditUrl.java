package com.cdz360.biz.bi.domain;

import com.cdz360.base.utils.JsonUtils;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
public class AccessAuditUrl implements Serializable {

    /**
     * t_access_audit_url
     */
    private static final long serialVersionUID = 1L;
    /**
     * 主键唯一索引
     */
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 0:禁用;1:启用
     */
    private Integer status;
    /**
     * 访问地址
     */
    private String url;
    /**
     *
     */
    private String createBy;
    /**
     * 修改人账号account
     */
    private String updateBy;
    /**
     *
     */
    private Date createTime;
    /**
     *
     */
    private Date updateTime;
    /**
     * 日志类型
     */
    private String type;
    /**
     * 分类
     */
    private String cate;
    /**
     * 子分类
     */
    private String subCate;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}