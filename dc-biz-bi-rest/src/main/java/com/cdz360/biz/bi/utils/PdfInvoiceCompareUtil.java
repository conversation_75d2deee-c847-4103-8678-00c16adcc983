package com.cdz360.biz.bi.utils;

import com.cdz360.biz.model.oa.vo.InvoicingContentVo;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

/**
 * PdfInvoiceCompareUtil
 *
 * @since 4/26/2023 4:43 PM
 * <AUTHOR>
 */
@Slf4j
public class PdfInvoiceCompareUtil {

    /**
     *
     * @param actualData 应开
     * @param invoicingContent 实开
     * @return
     */
    public static PdfInvoiceCompareSection generate(
        List<InvoicingContentVo> actualData,
        List<InvoicingContentVo> invoicingContent) {

        final Set<String> mainSet = actualData.stream()
            .map(InvoicingContentVo::getCode)
            .collect(Collectors.toSet());
        final Set<String> subSet = invoicingContent.stream()
            .map(InvoicingContentVo::getCode)
            .collect(Collectors.toSet());

        final Set<String> intersectSet = new HashSet<>(mainSet);
        intersectSet.retainAll(subSet);

        // 交集处理
        final List<InvoicingContentVo> actualDataTrim = actualData.stream()
            .filter(e -> intersectSet.contains(e.getCode()))
            .collect(Collectors.toList());
        final List<InvoicingContentVo> invoicingContentTrim = invoicingContent.stream()
            .filter(e -> intersectSet.contains(e.getCode()))
            .collect(Collectors.toList());

        final List<String> headers = actualDataTrim.stream()
            .map(InvoicingContentVo::getProductName)
            .collect(Collectors.toList());

        final List<BigDecimal> actualDataCell = actualDataTrim.stream()
            .map(InvoicingContentVo::getFixAmount)
            .map(e -> e != null ? e : BigDecimal.ZERO)
            .collect(Collectors.toList());
        final List<BigDecimal> invoicingContentCell = invoicingContentTrim.stream()
            .map(InvoicingContentVo::getFixAmount)
            .map(e -> e != null ? e : BigDecimal.ZERO)
            .collect(Collectors.toList());

        int i = 0;
        final List<BigDecimal> diff = new ArrayList<>();
        while(i < actualDataTrim.size()) {
            final BigDecimal actual = actualDataCell.get(i);
            final BigDecimal invoicing = invoicingContentCell.get(i);
            diff.add(actual.subtract(invoicing));
            i++;
        }

        return PdfInvoiceCompareSection.of(headers,
            actualDataCell,
            invoicingContentCell,
            diff);
    }
}