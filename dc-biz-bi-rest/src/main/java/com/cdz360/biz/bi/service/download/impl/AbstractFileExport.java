package com.cdz360.biz.bi.service.download.impl;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.bi.config.ExportFileConfig;
import com.cdz360.biz.bi.service.download.DownloadFileProxy;
import com.cdz360.biz.bi.service.download.IFileExport;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import java.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;

public abstract class AbstractFileExport<P, POS extends ExcelPosition>
    implements IFileExport<P, POS> {

    protected static final DateTimeFormatter FORMAT_yyyy_MM_dd =
        DateTimeFormatter.ofPattern("yyyy-MM-dd");

    protected static final DateTimeFormatter FORMAT_yyyy_MM_dd_HH_mm_ss =
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    protected ExportFileConfig exportFileConfig;

    @Autowired
    protected DownloadFileProxy downloadFileProxy;

    protected P convert(String context) {
        return JsonUtils.fromJson(context, this.paramClazz());
    }

    protected POS convertPos(String pos) {
        return JsonUtils.fromJson(pos, this.posClazz());
    }

    @Override
    public Class<POS> posClazz() {
        return (Class<POS>) ExcelPosition.class;
    }
}
