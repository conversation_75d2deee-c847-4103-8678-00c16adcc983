package com.cdz360.biz.bi.utils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;

/**
 *  pdf数据比对，仅包含实际数据
 * @since 4/26/2023 4:36 PM
 * <AUTHOR>
 */
@Data
public class PdfInvoiceCompareSection {
    private List<String> headers;//表头
    List<BigDecimal> actualData;//应开
    List<BigDecimal> invoicingContent;//实开
    List<BigDecimal> diff;//差异

    public static PdfInvoiceCompareSection of(List<String> headers,
        List<BigDecimal> actualData,
        List<BigDecimal> invoicingContent,
        List<BigDecimal> diff) {
        return new PdfInvoiceCompareSection(headers,
            actualData,
            invoicingContent,
            diff);
    }

    private PdfInvoiceCompareSection(List<String> headers,
        List<BigDecimal> actualData,
        List<BigDecimal> invoicingContent,
        List<BigDecimal> diff) {

        final List<String> headersTotal = new ArrayList<>(headers);
        headersTotal.add("总额(元)");
        this.headers = headersTotal;

        this.actualData = sumAndNewList(actualData);
        this.invoicingContent = sumAndNewList(invoicingContent);
        this.diff = sumAndNewList(diff);
    }

    private static List<BigDecimal> sumAndNewList(List<BigDecimal> in) {
        final BigDecimal sum = in.stream()
            .reduce(BigDecimal.ZERO, BigDecimal::add);
        final List<BigDecimal> out = new ArrayList<>(in);
        out.add(sum);
        return out;
    }

    public boolean isEmpty() {
        return headers == null || headers.isEmpty();
    }
}