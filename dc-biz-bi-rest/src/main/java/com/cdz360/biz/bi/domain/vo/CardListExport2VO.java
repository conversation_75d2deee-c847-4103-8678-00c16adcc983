package com.cdz360.biz.bi.domain.vo;


import com.cdz360.biz.model.annotations.ExcelField;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;


/**
 * 卡片列表查询卡片详情
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "在线卡信息")
public class CardListExport2VO implements Serializable {


    @ExcelField(title = "卡号", sort = 1)
    @Schema(description = "卡号")
    private String cardChipNo;

    @ExcelField(title = "卡名称", sort = 2)
    @Schema(description = "卡名称")
    private String cardName;

    @ExcelField(title = "所属商户", sort = 3)
    @Schema(description = "所属商户")
    private String commName;

    @ExcelField(title = "客户名称", sort = 4)
    @Schema(description = "客户名称")
    private String userName;

    @ExcelField(title = "车牌号", sort = 5)
    @Schema(description = "车牌号")
    private String carNo;

    @ExcelField(title = "车队", sort = 6)
    @Schema(description = "车队")
    private String carDepart;

    @ExcelField(title = "线路", sort = 7)
    @Schema(description = "线路")
    private String lineNum;

    @ExcelField(title = "车辆自编号", sort = 8)
    @Schema(description = "车辆自编号")
    private String carNum;

    @ExcelField(title = "指定场站数量", sort = 9)
    @Schema(description = "指定场站数量")
    private String usableStationCount;

    @ExcelField(title = "所属企业客户", sort = 10)
    @Schema(description = "所属企业客户")
    private String corpName;

    @ExcelField(title = "扣款账户", sort = 11)
    @Schema(description = "扣款账户")
    private String debitAccountName;

    @ExcelField(title = "状态", sort = 12)
    @Schema(description = "卡状态：10000未激活，10001已激活，10002卡锁定(已挂失)，10005已失效(黑名单)，10006已过期")
    private String cardStatus;

    @ExcelField(title = "创建时间", sort = 13)
    @Schema(description = "创建时间")
    private Date cardCreateDate;
}
