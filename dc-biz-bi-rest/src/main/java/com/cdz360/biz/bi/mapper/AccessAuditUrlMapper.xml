<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.bi.mapper.AccessAuditUrlMapper">
  <!--  <resultMap id="BaseResultMap" type="com.chargerlinkcar.core.domain.AccessAuditUrl">-->
  <!--    <id column="id" jdbcType="BIGINT" property="id" />-->
  <!--    <result column="name" jdbcType="VARCHAR" property="name" />-->
  <!--    <result column="status" jdbcType="INTEGER" property="status" />-->
  <!--    <result column="url" jdbcType="VARCHAR" property="url" />-->
  <!--    <result column="create_by" jdbcType="VARCHAR" property="createBy" />-->
  <!--    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />-->
  <!--    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />-->
  <!--    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />-->
  <!--    <result column="type" jdbcType="VARCHAR" property="type" />-->
  <!--    <result column="cate" jdbcType="VARCHAR" property="cate" />-->
  <!--    <result column="sub_cate" jdbcType="VARCHAR" property="subCate" />-->
  <!--  </resultMap>-->
  <!--  <sql id="Base_Column_List">-->
  <!--    id, name, status, url, create_by, update_by, create_time, update_time,type,-->
  <!--      cate, sub_cate-->
  <!--  </sql>-->
  <!--  <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">-->
  <!--    select-->
  <!--    <include refid="Base_Column_List" />-->
  <!--    from t_access_audit_url-->
  <!--    where id = #{id,jdbcType=BIGINT}-->
  <!--  </select>-->

  <!--  <select id="selectByUrl" parameterType="java.lang.String" resultMap="BaseResultMap">-->
  <!--    select-->
  <!--    <include refid="Base_Column_List" />-->
  <!--    from t_access_audit_url-->
  <!--    where url = #{url,jdbcType=VARCHAR}-->
  <!--  </select>-->


  <!--  <select id="queryAccessAuditUrls" parameterType="java.lang.Integer" resultMap="BaseResultMap">-->
  <!--    select-->
  <!--    <include refid="Base_Column_List" />-->
  <!--    from t_access_audit_url-->
  <!--    <where>-->
  <!--      <if test="status != null">-->
  <!--        status = #{status,jdbcType=INTEGER}-->
  <!--      </if>-->

  <!--    </where>-->
  <!--  </select>-->
  <!--  <select id="queryPageAccessAuditUrls" parameterType="java.lang.Integer" resultMap="BaseResultMap">-->
  <!--    select-->
  <!--    <include refid="Base_Column_List" />-->
  <!--    from t_access_audit_url-->
  <!--    <where>-->
  <!--      status = #{status,jdbcType=INTEGER}-->
  <!--    </where>-->
  <!--  </select>-->
  <!--  <delete id="deleteById" parameterType="java.lang.Long">-->
  <!--    delete from t_access_audit_url-->
  <!--    where id = #{id,jdbcType=BIGINT}-->
  <!--  </delete>-->
  <!--  <insert id="insertSelective" parameterType="com.chargerlinkcar.core.domain.AccessAuditUrl">-->
  <!--    insert into t_access_audit_url-->
  <!--    <trim prefix="(" suffix=")" suffixOverrides=",">-->
  <!--      <if test="id != null">-->
  <!--        id,-->
  <!--      </if>-->
  <!--      <if test="name != null">-->
  <!--        name,-->
  <!--      </if>-->
  <!--      <if test="status != null">-->
  <!--        status,-->
  <!--      </if>-->
  <!--      <if test="url != null">-->
  <!--        url,-->
  <!--      </if>-->
  <!--      <if test="createBy != null">-->
  <!--        create_by,-->
  <!--      </if>-->
  <!--      <if test="updateBy != null">-->
  <!--        update_by,-->
  <!--      </if>-->
  <!--      <if test="createTime != null">-->
  <!--        create_time,-->
  <!--      </if>-->
  <!--      <if test="updateTime != null">-->
  <!--        update_time,-->
  <!--      </if>-->
  <!--      <if test="type != null">-->
  <!--        type,-->
  <!--      </if>-->
  <!--      <if test="cate != null">-->
  <!--        cate,-->
  <!--      </if>-->
  <!--      <if test="subCate != null">-->
  <!--        sub_cate,-->
  <!--      </if>-->
  <!--    </trim>-->
  <!--    <trim prefix="values (" suffix=")" suffixOverrides=",">-->
  <!--      <if test="id != null">-->
  <!--        #{id,jdbcType=BIGINT},-->
  <!--      </if>-->
  <!--      <if test="name != null">-->
  <!--        #{name,jdbcType=VARCHAR},-->
  <!--      </if>-->
  <!--      <if test="status != null">-->
  <!--        #{status,jdbcType=BIT},-->
  <!--      </if>-->
  <!--      <if test="url != null">-->
  <!--        #{url,jdbcType=VARCHAR},-->
  <!--      </if>-->
  <!--      <if test="createBy != null">-->
  <!--        #{createBy,jdbcType=VARCHAR},-->
  <!--      </if>-->
  <!--      <if test="updateBy != null">-->
  <!--        #{updateBy,jdbcType=VARCHAR},-->
  <!--      </if>-->
  <!--      <if test="createTime != null">-->
  <!--        #{createTime,jdbcType=TIMESTAMP},-->
  <!--      </if>-->
  <!--      <if test="updateTime != null">-->
  <!--        #{updateTime,jdbcType=TIMESTAMP},-->
  <!--      </if>-->
  <!--      <if test="type != null">-->
  <!--        #{type,jdbcType=VARCHAR},-->
  <!--      </if>-->
  <!--      <if test="cate != null">-->
  <!--        #{cate,jdbcType=VARCHAR},-->
  <!--      </if>-->
  <!--      <if test="subCate != null">-->
  <!--        #{subCate,jdbcType=VARCHAR},-->
  <!--      </if>-->
  <!--    </trim>-->
  <!--  </insert>-->
  <!--  <update id="updateByIdSelective" parameterType="com.chargerlinkcar.core.domain.AccessAuditUrl">-->
  <!--    update t_access_audit_url-->
  <!--    <set>-->
  <!--      <if test="name != null">-->
  <!--        name = #{name,jdbcType=VARCHAR},-->
  <!--      </if>-->
  <!--      <if test="status != null">-->
  <!--        status = #{status,jdbcType=BIT},-->
  <!--      </if>-->
  <!--      <if test="url != null">-->
  <!--        url = #{url,jdbcType=VARCHAR},-->
  <!--      </if>-->
  <!--      <if test="createBy != null">-->
  <!--        create_by = #{createBy,jdbcType=VARCHAR},-->
  <!--      </if>-->
  <!--      <if test="updateBy != null">-->
  <!--        update_by = #{updateBy,jdbcType=VARCHAR},-->
  <!--      </if>-->
  <!--      <if test="createTime != null">-->
  <!--        create_time = #{createTime,jdbcType=TIMESTAMP},-->
  <!--      </if>-->
  <!--      <if test="updateTime != null">-->
  <!--        update_time = #{updateTime,jdbcType=TIMESTAMP},-->
  <!--      </if>-->
  <!--      <if test="type != null">-->
  <!--        type = #{type,jdbcType=VARCHAR},-->
  <!--      </if>-->
  <!--      <if test="cate != null">-->
  <!--        cate = #{cate,jdbcType=VARCHAR},-->
  <!--      </if>-->
  <!--      <if test="subCate != null">-->
  <!--        sub_cate = #{subCate,jdbcType=VARCHAR},-->
  <!--      </if>-->
  <!--    </set>-->
  <!--    where id = #{id,jdbcType=BIGINT}-->
  <!--  </update>-->
</mapper>