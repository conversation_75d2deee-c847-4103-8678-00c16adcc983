package com.cdz360.biz.bi.service.site;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.model.finance.type.BizType;
import com.cdz360.biz.model.trading.comm.dto.CommSiteEvseCount;
import com.cdz360.biz.model.trading.site.dto.CitySiteNumDto;
import com.cdz360.biz.model.trading.site.dto.DistrictSiteNumDto;
import com.cdz360.biz.model.trading.site.dto.ProvinceSiteNumDto;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class SiteBizService {

    private final List<Integer> defaultBizTypeList = Stream.of(BizType.UNKNOWN, BizType.SELF,
            BizType.NON_SELF)
        .map(BizType::getCode).collect(Collectors.toList());

    @Autowired
    private SiteRoDs siteRoDs;


    /**
     * 按省统计场站数量
     *
     * @return
     */
    public List<ProvinceSiteNumDto> getProvinceSiteNumList(ListSiteParam param) {
        if (CollectionUtils.isEmpty(param.getBizTypeList())) {
            param.setBizTypeList(defaultBizTypeList);
        }
        return this.siteRoDs.getProvinceSiteNumList(param.getIncludedHlhtSite(),
            param.getCommIdChain(),
            param.getBizTypeList(),
            param.getSiteIdList(),
            param.getGids());
    }

    /**
     * 按城市统计场站数量
     *
     * @return
     */
    public List<CitySiteNumDto> getCitySiteNumList(ListSiteParam param) {
        if (CollectionUtils.isEmpty(param.getBizTypeList())) {
            param.setBizTypeList(defaultBizTypeList);
        }
        return this.siteRoDs.getCitySiteNumList(param.getIncludedHlhtSite(),
            param.getCommIdChain(),
            param.getBizTypeList(),
            param.getSiteIdList(),
            param.getGids());
    }

    /**
     * 按城市统计场站数量
     *
     * @return
     */
    public List<DistrictSiteNumDto> getDistrictSiteNumList(ListSiteParam param) {
        if (CollectionUtils.isEmpty(param.getBizTypeList())) {
            param.setBizTypeList(defaultBizTypeList);
        }
        return this.siteRoDs.getDistrictSiteNumList(param.getIncludedHlhtSite(),
            param.getCommIdChain(),
            param.getBizTypeList(),
            param.getSiteIdList(),
            param.getGids());
    }

    /**
     * 统计商户下场站数量，桩/枪数量
     *
     * @return
     */
    public CommSiteEvseCount getCommSiteEvseCount(ListSiteParam param) {
        return this.siteRoDs.getCommSiteEvseCount(param.getIncludedHlhtSite(),
            param.getCommIdChain(),
            param.getSiteIdList(),
            param.getGids());
    }



}
