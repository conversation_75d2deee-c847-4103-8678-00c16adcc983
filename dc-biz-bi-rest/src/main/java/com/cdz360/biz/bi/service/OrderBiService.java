package com.cdz360.biz.bi.service;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.model.base.type.ChargeOrderStatus;
import com.cdz360.base.model.base.type.EvseBizStatus;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.bi.feign.DataCoreFeignClient;
import com.cdz360.biz.bi.utils.RedisUtil;
import com.cdz360.biz.ds.trading.ro.geo.ds.CityRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.OrderBiRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.BiPlugRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.BiSiteOrderAccountRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.BiSiteOrderRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.CommRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteTotalBiRoDs;
import com.cdz360.biz.ess.model.dto.EssEquipAlarmLangDto;
import com.cdz360.biz.ess.model.param.ListAlarmLangParam;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.bi.site.OrderCount;
import com.cdz360.biz.model.bi.site.SiteUtilization;
import com.cdz360.biz.model.bi.site.UserOrderCount;
import com.cdz360.biz.model.bi.site.UserOrderElec;
import com.cdz360.biz.model.bi.site.UserOrderFee;
import com.cdz360.biz.model.finance.type.BizType;
import com.cdz360.biz.model.iot.dto.EvseDto;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.iot.param.SiteBiParam;
import com.cdz360.biz.model.iot.param.SiteBiTopParam;
import com.cdz360.biz.model.iot.type.SiteBiDependOnOrderTimeType;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import com.cdz360.biz.model.merchant.param.ListCommercialParam;
import com.cdz360.biz.model.site.type.AlarmEventTypeEnum;
import com.cdz360.biz.model.site.type.AlarmStatusEnum;
import com.cdz360.biz.model.site.type.SiteStatus;
import com.cdz360.biz.model.trading.bi.dto.BiExportGroups;
import com.cdz360.biz.model.trading.bi.dto.ChargerOrderBiDto;
import com.cdz360.biz.model.trading.bi.param.ListBiCommercialParam;
import com.cdz360.biz.model.trading.bi.param.ListBiSiteParam;
import com.cdz360.biz.model.trading.bi.param.ListChargeOrderBiByVinParam;
import com.cdz360.biz.model.trading.bi.param.WarningBiParam;
import com.cdz360.biz.model.trading.bi.vo.CommercialBiVo;
import com.cdz360.biz.model.trading.bi.vo.SiteBiVo;
import com.cdz360.biz.model.trading.bi.vo.VinBiVo;
import com.cdz360.biz.model.trading.bi.warning.WarningBiDto;
import com.cdz360.biz.model.trading.bi.warning.WarningBiI18nDto;
import com.cdz360.biz.model.trading.cus.vo.CusOrderBiVo;
import com.cdz360.biz.model.trading.order.dto.GeoOrderBiDto;
import com.cdz360.biz.model.trading.order.dto.OrderAccountTypeBiDto;
import com.cdz360.biz.model.trading.order.dto.OrderStartTypeBiDto;
import com.cdz360.biz.model.trading.order.dto.OrderThinBiDto;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.type.BiDependOnType;
import com.cdz360.biz.model.trading.order.vo.ChargeOrderBiVo;
import com.cdz360.biz.model.trading.order.vo.ChargeOrderCache;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.po.BiSiteOrderPo;
import com.cdz360.biz.model.trading.site.vo.SiteOrderAccountData;
import com.cdz360.biz.model.trading.site.vo.SiteOrderBiVo;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.framework.common.domain.WWarningRecord;
import com.chargerlinkcar.framework.common.domain.param.ChargerOrderParam;
import com.chargerlinkcar.framework.common.domain.type.CardType;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDataBiVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderDataVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderSample;
import com.chargerlinkcar.framework.common.domain.vo.SiteInMongoVo;
import com.chargerlinkcar.framework.common.domain.vo.VinDto;
import com.chargerlinkcar.framework.common.feign.DeviceMonitorFeignClient;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.feign.UserFeignClient;
import com.chargerlinkcar.framework.common.utils.BiUtils;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.DateUtils;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoField;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class OrderBiService {

    private static final int MAX_SAMPLE_COUNT = 100; // 最大输出日期个数


    @Autowired
    private OrderBiRoDs orderBiRoDs;

    @Autowired
    private SiteTotalBiRoDs siteTotalBiRoDs;

    @Autowired
    private BiPlugRoDs biPlugRoDs;

    @Autowired
    private ChargerOrderRoDs chargerOrderRoDs;

    @Autowired
    private ExcelFileService excelFileService;

    @Autowired
    private BiSiteOrderRoDs biSiteOrderRoDs;
    @Autowired
    private BiSiteOrderAccountRoDs biSiteOrderAccountRoDs;
    @Autowired
    private DeviceMonitorFeignClient deviceMonitorFeignClient;

    @Autowired
    private CityRoDs cityRoDs;

    @Autowired
//    private TRCommercialRoDs trCommercialRoDs;
    private CommRoDs commRoDs;

    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private RedisIotReadService redisIotReadService;

    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private UserFeignClient userFeignClient;

    @Transactional(readOnly = true)
    public ListResponse<ChargerOrderBiDto> getBiListResponseByVin(
        ListChargeOrderBiByVinParam param) {
        IotAssert.isTrue(param.getTopCommId() != null && param.getTopCommId() > 0,
            "集团商户ID为空");
        IotAssert.isTrue(param.getStart() != null && param.getStart() >= 0
            && param.getSize() != null && param.getSize() > 0, "分页参数ID为空");
        List<ChargerOrderBiDto> list = this.getBiListByVin(param);
        Long total = this.getBiListByVinCount(param);
        ListResponse<ChargerOrderBiDto> res = new ListResponse<>(list, total);
        return res;
    }

    /**
     * 企业平台，按照VIN获取订单汇总信息,替换原来的操作  getBiListResponseByVin
     */
    @Transactional(readOnly = true)
    public ListResponse<ChargerOrderBiDto> getBiOrderListByVin(
        ListChargeOrderBiByVinParam param) {
        IotAssert.isTrue(param.getTopCommId() != null && param.getTopCommId() > 0,
            "集团商户ID为空");
        IotAssert.isTrue(param.getStart() != null && param.getStart() >= 0
            && param.getSize() != null && param.getSize() > 0, "分页参数ID为空");

        // TODO: 获取所有vin列表
        ListResponse<VinDto> response = userFeignClient.selectVinListForCorp(param);
        FeignResponseValidate.check(response);
        if (response.getData().isEmpty()) {
            return RestUtils.buildListResponse(new ArrayList<>());
        }

        // TODO:  分页获取充电信息
        List<String> vinCodeList = response.getData().stream().map(VinDto::getVin)
            .distinct().collect(Collectors.toList());
        List<Long> userIdList = response.getData().stream().map(VinDto::getRBlocUserId)
            .distinct().collect(Collectors.toList());

        param.setVinList(vinCodeList);
        if (CollectionUtils.isNotEmpty(userIdList)) {
            param.setCusIdList(userIdList);
        }
        List<ChargerOrderBiDto> result = orderBiRoDs.getBiListByVinAndUser(param);
        if (CollectionUtils.isEmpty(result)) {
            return RestUtils.buildListResponse(new ArrayList<>());
        }

        // TODO: 获取充电分时信息,--只查询订单数据获取的vin
        param.setVinList(
            result.stream().map(ChargerOrderBiDto::getVin).distinct().collect(Collectors.toList()));
        List<ChargerOrderBiDto> divisionList = orderBiRoDs.getBiDivisionListByVinAndUser(
            param);

        // TODO： 拼接结果
        Map<String, VinDto> vinMap = response.getData().stream()
            .collect(Collectors.toMap(VinDto::getVin, o -> o));
        Map<String, ChargerOrderBiDto> divisionMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(divisionList)) {
            divisionMap = divisionList.stream()
                .collect(Collectors.toMap(ChargerOrderBiDto::getVin, o -> o));
        }

        Map<String, ChargerOrderBiDto> finalDivisionMap = divisionMap;
        result.forEach(item -> {
            // 卡信息
            if (vinMap.containsKey(item.getVin())) {
                VinDto vinDto = vinMap.get(item.getVin());
                item.setVin(vinDto.getVin())
                    .setVinId(vinDto.getId())
                    .setCarDepart(vinDto.getCarDepart())
                    .setCarLineNum(vinDto.getLineNum())
                    .setCarNum(vinDto.getCarNum())
                    .setCarNo(vinDto.getCarNo())
                    .setCorpOrgId(vinDto.getCorpOrgId())
                    .setCorpOrgName(vinDto.getCorpOrgName())
                    .setCusId(vinDto.getRBlocUserId())
                    .setCusPhone(vinDto.getMobile())
                    .setCusName(vinDto.getUserName());
            }

            // 分时信息
            if (finalDivisionMap.containsKey(item.getVin())) {
                ChargerOrderBiDto divisonInfo = finalDivisionMap.get(item.getVin());
                // 尖
                item.setT1Duration(divisonInfo.getT1Duration())
                    .setT1Elec(divisonInfo.getT1Elec())
                    .setT1ElecFee(divisonInfo.getT1ElecFee())
                    .setT1ServFee(divisonInfo.getT1ServFee())
                    .setT1OrderFee(divisonInfo.getT1OrderFee())
                    // 峰
                    .setT2Duration(divisonInfo.getT2Duration())
                    .setT2Elec(divisonInfo.getT2Elec())
                    .setT2ElecFee(divisonInfo.getT2ElecFee())
                    .setT2ServFee(divisonInfo.getT2ServFee())
                    .setT2OrderFee(divisonInfo.getT2OrderFee())
                    // 平
                    .setT3Duration(divisonInfo.getT3Duration())
                    .setT3Elec(divisonInfo.getT3Elec())
                    .setT3ElecFee(divisonInfo.getT3ElecFee())
                    .setT3ServFee(divisonInfo.getT3ServFee())
                    .setT3OrderFee(divisonInfo.getT3OrderFee())
                    // 谷
                    .setT4Duration(divisonInfo.getT4Duration())
                    .setT4Elec(divisonInfo.getT4Elec())
                    .setT4ElecFee(divisonInfo.getT4ElecFee())
                    .setT4ServFee(divisonInfo.getT4ServFee())
                    .setT4OrderFee(divisonInfo.getT4OrderFee());
            }
        });

        // TODO   查询汇总信息
        param.setVinList(vinCodeList);
        return RestUtils.buildListResponse(result, orderBiRoDs.getCountByVinAndUser(param));
    }

    @Transactional(readOnly = true)
    public ListResponse<ChargerOrderBiDto> getBiListDetailResponseByVin(
        ListChargeOrderBiByVinParam param) {
        IotAssert.isTrue(param.getTopCommId() != null && param.getTopCommId() > 0,
            "无法获取登录信息");
        IotAssert.isNotNull(param.getVinId(), "vinId不能为空");
//        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getSiteIdList()), "siteIdList不能为空");

        List<ChargerOrderBiDto> list = this.getBiListDetailByVin(param);
        return RestUtils.buildListResponse(list);
    }

    @Async
    public void exportBiListByVin(ListChargeOrderBiByVinParam param) {

        param.setVinId(null).setVinExact(null); // 设置为空,查出全部详情
        Map<Long, List<ChargerOrderBiDto>> stringBiDtoMap = this.getBiListDetailByVin(param)
            .stream()
            .collect(Collectors.groupingBy(ChargerOrderBiDto::getVinId, Collectors.toList()));

        excelFileService.exportExcelFile(
            param.getExcelPosition().getSubDir(),
            param.getExcelPosition().getSubFileName(),
            ChargerOrderBiDto.class,
            BiExportGroups.VINBI.getCode(),
            BiExportGroups.VINBI.getName(),
            null,
            param.getFilter(),
            null,
            (start, size) -> {
                param.setStart((long) ((start - 1) * size))
                    .setSize(size);
                return this.getBiOrderListByVin(param).getData().stream()
                    .collect(Collectors.toList());
//                return this.getBiListByVin(param).stream().collect(Collectors.toList());
            },
            (e) -> e.stream().map(x -> {
                ChargerOrderBiDto biDto = (ChargerOrderBiDto) x;
                if (DecimalUtils.isZero(biDto.getOrderCount())) {
                    return List.of(biDto);
                }
                //若订单数不为0，则追加下拉详情
                List<ChargerOrderBiDto> res = new ArrayList<>();
                res.add(biDto);
                List<ChargerOrderBiDto> detail = stringBiDtoMap.get(biDto.getVinId());
                if (CollectionUtils.isNotEmpty(detail)) {
//                        detail.clearSomeFields(BiExportGroups.VINBI);
                    res.addAll(detail);
                }
                return res;
            }).flatMap(List::stream).collect(Collectors.toList())
        );
    }

    public List<ChargerOrderBiDto> getBiListByVin(ListChargeOrderBiByVinParam param) {
        return this.orderBiRoDs.getBiListByVin(param);
    }

    public Long getBiListByVinCount(ListChargeOrderBiByVinParam param) {
        return this.orderBiRoDs.getBiListByVinCount(param);
    }

    public List<ChargerOrderBiDto> getBiListDetailByVin(ListChargeOrderBiByVinParam param) {
        return this.orderBiRoDs.getBiListDetailByVin(param);
    }

    @Transactional(readOnly = true)
    public ListResponse<ChargerOrderBiDto> getBiListResponseByCorpCus(
        ListChargeOrderBiByVinParam param) {
        //参数检查
        this.listChargeOrderBiByVinParamCheck(param);
        List<ChargerOrderBiDto> list = this.getBiListByCorpCus(param);
        Long total = this.getBiListByCorpCusCount(param);
        ListResponse<ChargerOrderBiDto> res = new ListResponse<>(list, total);
        return res;
    }

    @Transactional(readOnly = true)
    public ListResponse<ChargerOrderBiDto> getBiListDetailResponseByCorpCus(
        ListChargeOrderBiByVinParam param) {
        IotAssert.isTrue(param.getTopCommId() != null && param.getTopCommId() > 0
            && StringUtils.isNotBlank(param.getCommIdChain()), "无法获取登录信息");
        IotAssert.isNotNull(param.getCorpCusId(), "corpCusId不能为空");
//        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getSiteIdList()), "siteIdList不能为空");

        List<ChargerOrderBiDto> list = this.getBiListDetailByCorpCus(param);
        return RestUtils.buildListResponse(list);
    }

    //    @Async
    public void exportBiListByCorpCus(ListChargeOrderBiByVinParam param) {

        param.setCorpCusId(null); // 设置为空,查出全部详情
        Map<Long, List<ChargerOrderBiDto>> stringBiDtoMap = this.getBiListDetailByCorpCus(param)
            .stream()
            .collect(Collectors.groupingBy(ChargerOrderBiDto::getCorpCusId, Collectors.toList()));

        excelFileService.exportExcelFile(
            param.getExcelPosition().getSubDir(),
            param.getExcelPosition().getSubFileName(),
            ChargerOrderBiDto.class,
            BiExportGroups.CORPCUSBI.getCode(),
            BiExportGroups.CORPCUSBI.getName(),
            null,
            param.getFilter(),
            null,
            (start, size) -> {
                param.setStart((long) ((start - 1) * size))
                    .setSize(size);
                return this.getBiListByCorpCus(param).stream().collect(Collectors.toList());
            },
            (e) -> e.stream().map(x -> {
                ChargerOrderBiDto biDto = (ChargerOrderBiDto) x;
                if (DecimalUtils.isZero(biDto.getOrderCount())) {
                    return List.of(biDto);
                }
                //若订单数不为0，则追加下拉详情
                List<ChargerOrderBiDto> res = new ArrayList<>();
                res.add(biDto);
                List<ChargerOrderBiDto> detail = stringBiDtoMap.get(biDto.getCorpCusId());
                if (detail != null) {
//                        detail.clearSomeFields(BiExportGroups.CORPCUSBI);
                    res.addAll(detail);
                }
                return res;
            }).flatMap(List::stream).collect(Collectors.toList())
        );
    }

    public List<ChargerOrderBiDto> getBiListByCorpCus(ListChargeOrderBiByVinParam param) {
        return this.orderBiRoDs.getBiListByCorpCus(param);
    }

    public Long getBiListByCorpCusCount(ListChargeOrderBiByVinParam param) {
        return this.orderBiRoDs.getBiListByCorpCusCount(param);
    }

    public List<ChargerOrderBiDto> getBiListDetailByCorpCus(ListChargeOrderBiByVinParam param) {
        return this.orderBiRoDs.getBiListDetailByCorpCus(param);
    }

    @Transactional(readOnly = true)
    public ListResponse<ChargerOrderBiDto> getBiListResponseByCorpCreditCus(
        ListChargeOrderBiByVinParam param) {
        //参数检查
        this.listChargeOrderBiByVinParamCheck(param);
        List<ChargerOrderBiDto> list = this.getBiListByCorpCreditCus(param);
        Long total = this.getBiListByCorpCreditCusCount(param);
        ListResponse<ChargerOrderBiDto> res = new ListResponse<>(list, total);
        return res;
    }

    @Transactional(readOnly = true)
    public ListResponse<ChargerOrderBiDto> getBiListDetailResponseByCorpCreditCus(
        ListChargeOrderBiByVinParam param) {
        IotAssert.isTrue(param.getTopCommId() != null && param.getTopCommId() > 0,
            "无法获取登录信息");
        IotAssert.isNotNull(param.getCusUserId(), "cusUserId不能为空");
//        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getSiteIdList()), "siteIdList不能为空");

        List<ChargerOrderBiDto> list = this.getBiListDetailByCorpCreditCus(param);
        return RestUtils.buildListResponse(list);
    }

    @Async
    public void exportBiListByCorpCreditCus(ListChargeOrderBiByVinParam param) {

        param.setCusUserId(null); // 设置为空,查出全部详情
        Map<Long, List<ChargerOrderBiDto>> stringBiDtoMap = this.getBiListDetailByCorpCreditCus(
                param).stream()
            .collect(Collectors.groupingBy(ChargerOrderBiDto::getRBlocUserId, Collectors.toList()));

        excelFileService.exportExcelFile(
            param.getExcelPosition().getSubDir(),
            param.getExcelPosition().getSubFileName(),
            ChargerOrderBiDto.class,
            BiExportGroups.CORPCREDITCUSBI.getCode(),
            BiExportGroups.CORPCREDITCUSBI.getName(),
            null,
            param.getFilter(),
            null,
            (start, size) -> {
                param.setStart((long) ((start - 1) * size))
                    .setSize(size);
                return this.getBiListByCorpCreditCus(param).stream().collect(Collectors.toList());
            },
            (e) -> e.stream().map(x -> {
                ChargerOrderBiDto biDto = (ChargerOrderBiDto) x;
                if (DecimalUtils.isZero(biDto.getOrderCount())) {
                    return List.of(biDto);
                }
                //若订单数不为0，则追加下拉详情
                List<ChargerOrderBiDto> res = new ArrayList<>();
                res.add(biDto);
                List<ChargerOrderBiDto> detail = stringBiDtoMap.get(biDto.getRBlocUserId());
                if (detail != null) {
//                        detail.clearSomeFields(BiExportGroups.CORPCREDITCUSBI);
                    res.addAll(detail);
                }
                return res;
            }).flatMap(List::stream).collect(Collectors.toList())
        );
    }

    public List<ChargerOrderBiDto> getBiListByCorpCreditCus(ListChargeOrderBiByVinParam param) {
        return this.orderBiRoDs.getBiListByCorpCreditCus(param);
    }

    public Long getBiListByCorpCreditCusCount(ListChargeOrderBiByVinParam param) {
        return this.orderBiRoDs.getBiListByCorpCreditCusCount(param);
    }

    public List<ChargerOrderBiDto> getBiListDetailByCorpCreditCus(
        ListChargeOrderBiByVinParam param) {
        return this.orderBiRoDs.getBiListDetailByCorpCreditCus(param);
    }

    @Transactional(readOnly = true)
    public ListResponse<ChargerOrderBiDto> getBiListResponseByOnlineCard(
        ListChargeOrderBiByVinParam param) {
        //参数检查
        this.listChargeOrderBiByVinParamCheck(param);
        List<ChargerOrderBiDto> list = this.getBiListByOnlineCard(param);
        Long total = this.getBiListByOnlineCardCount(param);
        ListResponse<ChargerOrderBiDto> res = new ListResponse<>(list, total);
        return res;
    }

    @Transactional(readOnly = true)
    public ListResponse<ChargerOrderBiDto> getBiListDetailResponseByOnlineCard(
        ListChargeOrderBiByVinParam param) {
        IotAssert.isTrue(param.getTopCommId() != null && param.getTopCommId() > 0
            && StringUtils.isNotBlank(param.getCommIdChain()), "无法获取登录信息");
        IotAssert.isNotNull(param.getCardId(), "cardId不能为空");
//        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getSiteIdList()), "siteIdList不能为空");

        List<ChargerOrderBiDto> list = this.getBiListDetailByOnlineCard(param);
        return RestUtils.buildListResponse(list);
    }

    //    @Async
    public void exportBiListByOnlineCard(ListChargeOrderBiByVinParam param) {

        param.setCardId(null); // 设置为空,查出全部详情
        Map<Long, List<ChargerOrderBiDto>> stringBiDtoMap = this.getBiListDetailByOnlineCard(param)
            .stream()
            .collect(Collectors.groupingBy(ChargerOrderBiDto::getCardId, Collectors.toList()));

        excelFileService.exportExcelFile(
            param.getExcelPosition().getSubDir(),
            param.getExcelPosition().getSubFileName(),
            ChargerOrderBiDto.class,
            BiExportGroups.ONLINECARDBI.getCode(),
            BiExportGroups.ONLINECARDBI.getName(),
            null,
            param.getFilter(),
            null,
            (start, size) -> {
                param.setStart((long) ((start - 1) * size))
                    .setSize(size);
                return this.getBiListByOnlineCard(param).stream().collect(Collectors.toList());
            },
            (e) -> e.stream().map(x -> {
                ChargerOrderBiDto biDto = (ChargerOrderBiDto) x;
                if (DecimalUtils.isZero(biDto.getOrderCount())) {
                    return List.of(biDto);
                }
                //若订单数不为0，则追加下拉详情
                List<ChargerOrderBiDto> res = new ArrayList<>();
                res.add(biDto);
                List<ChargerOrderBiDto> detail = stringBiDtoMap.get(biDto.getCardId());
                if (detail != null) {
//                            detail.clearSomeFields(BiExportGroups.ONLINECARDBI);
                    res.addAll(detail);
                }
                return res;
            }).flatMap(List::stream).collect(Collectors.toList())
        );
    }

    public List<ChargerOrderBiDto> getBiListByOnlineCard(ListChargeOrderBiByVinParam param) {
        param.setCardType(Integer.valueOf(CardType.ONLINE.getCode()));
        return this.orderBiRoDs.getBiListByOnlineCard(param);
    }

    public Long getBiListByOnlineCardCount(ListChargeOrderBiByVinParam param) {
        param.setCardType(Integer.valueOf(CardType.ONLINE.getCode()));
        return this.orderBiRoDs.getBiListByOnlineCardCount(param);
    }

    public List<ChargerOrderBiDto> getBiListDetailByOnlineCard(ListChargeOrderBiByVinParam param) {
        param.setCardType(Integer.valueOf(CardType.ONLINE.getCode()));
        return this.orderBiRoDs.getBiListDetailByOnlineCard(param);
    }

    @Transactional(readOnly = true)
    public ListResponse<ChargerOrderBiDto> getBiListResponseByOnlineCardOnCorp(
        ListChargeOrderBiByVinParam param) {
        //参数检查
        this.listChargeOrderBiByVinParamCheck(param);
        List<ChargerOrderBiDto> list = this.getBiListByOnlineCardOnCorp(param);
        Long total = this.getBiListByOnlineCardOnCorpCount(param);
        ListResponse<ChargerOrderBiDto> res = new ListResponse<>(list, total);
        return res;
    }

    @Transactional(readOnly = true)
    public ListResponse<ChargerOrderBiDto> getBiListDetailResponseByOnlineCardOnCorp(
        ListChargeOrderBiByVinParam param) {
        IotAssert.isTrue(param.getTopCommId() != null && param.getTopCommId() > 0
            && CollectionUtils.isNotEmpty(param.getCorpOrgIds()), "无法获取登录信息");
        IotAssert.isNotNull(param.getCardId(), "cardId不能为空");
//        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getSiteIdList()), "siteIdList不能为空");

        List<ChargerOrderBiDto> list = this.getBiListDetailByOnlineCardOnCorp(param);
        return RestUtils.buildListResponse(list);
    }

    @Async
    public void exportBiListByOnlineCardOnCorp(ListChargeOrderBiByVinParam param) {

        param.setCardId(null); // 设置为空,查出全部详情
        Map<Long, List<ChargerOrderBiDto>> stringBiDtoMap = this.getBiListDetailByOnlineCardOnCorp(
                param).stream()
            .collect(Collectors.groupingBy(ChargerOrderBiDto::getCardId, Collectors.toList()));

        excelFileService.exportExcelFile(
            param.getExcelPosition().getSubDir(),
            param.getExcelPosition().getSubFileName(),
            ChargerOrderBiDto.class,
            BiExportGroups.ONLINECARDBIONCORP.getCode(),
            BiExportGroups.ONLINECARDBIONCORP.getName(),
            null,
            param.getFilter(),
            null,
            (start, size) -> {
                param.setStart((long) ((start - 1) * size))
                    .setSize(size);
                return this.getBiListByOnlineCardOnCorp(param).stream()
                    .collect(Collectors.toList());
            },
            (e) -> e.stream().map(x -> {
                ChargerOrderBiDto biDto = (ChargerOrderBiDto) x;
                if (DecimalUtils.isZero(biDto.getOrderCount())) {
                    return List.of(biDto);
                }
                //若订单数不为0，则追加下拉详情
                List<ChargerOrderBiDto> res = new ArrayList<>();
                res.add(biDto);
                List<ChargerOrderBiDto> detail = stringBiDtoMap.get(biDto.getCardId());
                if (detail != null) {
//                        detail.clearSomeFields(BiExportGroups.ONLINECARDBIONCORP);
                    res.addAll(detail);
                }
                return res;
            }).flatMap(List::stream).collect(Collectors.toList())
        );
    }

    public List<ChargerOrderBiDto> getBiListByOnlineCardOnCorp(ListChargeOrderBiByVinParam param) {
        param.setCardType(Integer.valueOf(CardType.ONLINE.getCode()));
        return this.orderBiRoDs.getBiListByOnlineCardOnCorp(param);
    }

    public Long getBiListByOnlineCardOnCorpCount(ListChargeOrderBiByVinParam param) {
        param.setCardType(Integer.valueOf(CardType.ONLINE.getCode()));
        return this.orderBiRoDs.getBiListByOnlineCardOnCorpCount(param);
    }

    public List<ChargerOrderBiDto> getBiListDetailByOnlineCardOnCorp(
        ListChargeOrderBiByVinParam param) {
        param.setCardType(Integer.valueOf(CardType.ONLINE.getCode()));
        return this.orderBiRoDs.getBiListDetailByOnlineCardOnCorp(param);
    }

    @Transactional(readOnly = true)
    public ListResponse<ChargerOrderBiDto> getBiListResponseByEmergencyCard(
        ListChargeOrderBiByVinParam param) {
        //参数检查
        this.listChargeOrderBiByVinParamCheck(param);
        List<ChargerOrderBiDto> list = this.getBiListByEmergencyCard(param);
        Long total = this.getBiListByEmergencyCardCount(param);
        ListResponse<ChargerOrderBiDto> res = new ListResponse<>(list, total);
        return res;
    }

    //    @Async
    public void exportBiListResponseByEmergencyCard(ListChargeOrderBiByVinParam param) {
        excelFileService.exportExcelFile(
            param.getExcelPosition().getSubDir(),
            param.getExcelPosition().getSubFileName(),
            ChargerOrderBiDto.class,
            BiExportGroups.EMERGENCYCARDBI.getCode(),
            BiExportGroups.EMERGENCYCARDBI.getName(),
            null,
            param.getFilter(),
            null,
            (start, size) -> {
                param.setStart((long) ((start - 1) * size))
                    .setSize(size);
                return this.getBiListByEmergencyCard(param).stream().collect(Collectors.toList());
            },
            (e) -> e.stream().map(x -> (ChargerOrderBiDto) x).collect(Collectors.toList())
        );
    }

    public List<ChargerOrderBiDto> getBiListByEmergencyCard(ListChargeOrderBiByVinParam param) {
        param.setCardType(Integer.valueOf(CardType.EMERGENCY.getCode()));
        return this.orderBiRoDs.getBiListByEmergencyCard(param);
    }

    public Long getBiListByEmergencyCardCount(ListChargeOrderBiByVinParam param) {
        param.setCardType(Integer.valueOf(CardType.EMERGENCY.getCode()));
        return this.orderBiRoDs.getBiListByEmergencyCardCount(param);
    }

    @Transactional(readOnly = true)
    public ListResponse<ChargerOrderBiDto> getBiListResponseByOrg(
        ListChargeOrderBiByVinParam param) {
        //参数检查
        this.listChargeOrderBiByVinParamCheck(param);
        List<ChargerOrderBiDto> list = this.getBiListByOrg(param);
        Long total = this.getBiListByOrgCount(param);
        ListResponse<ChargerOrderBiDto> res = new ListResponse<>(list, total);
        return res;
    }

    @Transactional(readOnly = true)
    public ListResponse<ChargerOrderBiDto> getBiListDetailResponseByOrg(
        ListChargeOrderBiByVinParam param) {
        IotAssert.isTrue(param.getTopCommId() != null && param.getTopCommId() > 0,
            "无法获取登录信息");
//        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getSiteIdList()), "siteIdList不能为空");

        List<ChargerOrderBiDto> list = this.getBiListDetailByOrg(param);
        return RestUtils.buildListResponse(list);
    }

    @Async
    public void exportBiListResponseByOrg(ListChargeOrderBiByVinParam param) {

        Map<Long, List<ChargerOrderBiDto>> stringBiDtoMap = this.getBiListDetailByOrg(param)
            .stream()
            .collect(Collectors.groupingBy(ChargerOrderBiDto::getCorpOrgId, Collectors.toList()));

        excelFileService.exportExcelFile(
            param.getExcelPosition().getSubDir(),
            param.getExcelPosition().getSubFileName(),
            ChargerOrderBiDto.class,
            BiExportGroups.ORGBI.getCode(),
            BiExportGroups.ORGBI.getName(),
            null,
            param.getFilter(),
            null,
            (start, size) -> {
                param.setStart((long) ((start - 1) * size))
                    .setSize(size);
                return this.getBiListByOrg(param).stream().collect(Collectors.toList());
            },
            (e) -> e.stream().map(x -> {
                ChargerOrderBiDto biDto = (ChargerOrderBiDto) x;
                if (DecimalUtils.isZero(biDto.getOrderCount())) {
                    return List.of(biDto);
                }
                //若订单数不为0，则追加下拉详情
                List<ChargerOrderBiDto> res = new ArrayList<>();
                res.add(biDto);
                List<ChargerOrderBiDto> detail = stringBiDtoMap.get(biDto.getCorpOrgId());
                if (detail != null) {
//                            detail.clearSomeFields(BiExportGroups.ORGBI);
                    res.addAll(detail);
                }
                return res;
            }).flatMap(List::stream).collect(Collectors.toList())
        );
    }

    public List<ChargerOrderBiDto> getBiListByOrg(ListChargeOrderBiByVinParam param) {
        return this.orderBiRoDs.getBiListByOrg(param);
    }

    public Long getBiListByOrgCount(ListChargeOrderBiByVinParam param) {
        return this.orderBiRoDs.getBiListByOrgCount(param);
    }

    public List<ChargerOrderBiDto> getBiListDetailByOrg(ListChargeOrderBiByVinParam param) {
        return this.orderBiRoDs.getBiListDetailByOrg(param);
    }

    //    @Async
    public void exportWarningBiList(WarningBiParam param, ExcelPosition position) {
        if (null == param.getLocale()) {
            // 非国际版，保留原有逻辑
            excelFileService.exportExcelFile(
                position.getSubDir(),
                position.getSubFileName(),
                WarningBiDto.class,
                BiExportGroups.WARNINGBI.getCode(),
                BiExportGroups.WARNINGBI.getName(),
                null,
                null,
                null,
                (start, size) -> {
                    param.setStart((long) ((start - 1) * size))
                        .setSize(size);
                    ListResponse<WWarningRecord> response = deviceMonitorFeignClient.getWarningBiList(
                        param);
                    if (response == null
                        || response.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
                        return null;
                    }
                    return response.getData().stream().collect(Collectors.toList());
                },
                (e) -> {
                    Pair<Map<String, SiteStatus>, Map<String, EvseBizStatus>> mapPair = this.querySiteStatusAndEvseStatus(
                        e);
                    return e.stream()
                        .map(x -> this.mapWarningBiDto((WWarningRecord) x, mapPair.getLeft(),
                            mapPair.getRight()))
                        .collect(Collectors.toList());
                }
            );
        } else {
            // 国际版，单独处理
            // 获取语言
            Map<String, EssEquipAlarmLangDto> codeMap;
            ListAlarmLangParam listAlarmLangParam = new ListAlarmLangParam()
                .setLang(param.getLocale().getLanguage())
                .setEquipTypes(List.of(EssEquipType.EVSE));
            ListResponse<EssEquipAlarmLangDto> codeResponse = iotDeviceMgmFeignClient.getAlarmLangList(
                listAlarmLangParam);
            FeignResponseValidate.check(codeResponse);
            codeMap = codeResponse.getData().stream()
                .collect(Collectors.toMap(EssEquipAlarmLangDto::getCode, o -> o));

            excelFileService.exportExcelFile(
                position.getSubDir(),
                position.getSubFileName(),
                WarningBiI18nDto.class,
                BiExportGroups.WARNINGBI.getCode(),
//                BiExportGroups.WARNINGBI.getName(),
                messageSource.getMessage(BiExportGroups.WARNINGBI.getName(), null,
                    param.getLocale()),
                null,
                null,
                param.getLocale(),
                (start, size) -> {
                    param.setStart((long) ((start - 1) * size))
                        .setSize(size);
                    ListResponse<WWarningRecord> response = deviceMonitorFeignClient.getWarningBiList(
                        param);
                    if (response == null
                        || response.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
                        return null;
                    }
                    return response.getData().stream().collect(Collectors.toList());
                },
                (e) -> {
                    Pair<Map<String, SiteStatus>, Map<String, EvseBizStatus>> mapPair = this.querySiteStatusAndEvseStatus(
                        e);
                    return e.stream()
                        .map(x -> this.mapWarningBiI18nDto((WWarningRecord) x, mapPair.getLeft(),
                            mapPair.getRight(), param.getLocale(), codeMap))
                        .collect(Collectors.toList());
                }
            );
        }

    }

    /**
     * 获取场站   桩 状态
     *
     * @param recordList
     */
    private Pair<Map<String, SiteStatus>, Map<String, EvseBizStatus>> querySiteStatusAndEvseStatus(
        List<Serializable> recordList) {
        Map<String, SiteStatus> siteStatusMap = new HashMap<>();
        Map<String, EvseBizStatus> evseStatusMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(recordList)) {
            List<WWarningRecord> records = recordList.stream().map(x -> (WWarningRecord) x)
                .collect(Collectors.toList());

            List<String> siteIdList = records.stream().map(WWarningRecord::getSiteId)
                .filter(StringUtils::isNotEmpty).distinct()
                .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(siteIdList)) {
                ListSiteParam listSiteParam = new ListSiteParam();
                listSiteParam.setSiteIdList(siteIdList);
                listSiteParam.setStatusList(
                    List.of(SiteStatus.UNKNOWN, SiteStatus.ONLINE, SiteStatus.OPENING,
                        SiteStatus.UNAVAILABLE));
                ListResponse<SiteInMongoVo> response = dataCoreFeignClient.getSiteListFromMongo(
                    listSiteParam);
                FeignResponseValidate.check(response);
                siteStatusMap = response.getData().stream()
                    .collect(Collectors.toMap(SiteInMongoVo::getId, SiteInMongoVo::getStatus));
            }

            List<String> evseNoList = records.stream().map(WWarningRecord::getDeviceId)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(evseNoList)) {
                ListEvseParam listEvseParam = new ListEvseParam();
                listEvseParam.setEvseNoList(evseNoList);
                ListResponse<EvseDto> evseResponse = iotDeviceMgmFeignClient.getEvseList(
                    listEvseParam);
                FeignResponseValidate.check(evseResponse);
                List<EvseDto> evseList = evseResponse.getData();
                evseStatusMap = evseList.stream().filter(
                        x -> StringUtils.isNotEmpty(x.getEvseId()) && x.getBizStatus() != null)
                    .collect(Collectors.toMap(EvseDto::getEvseId, EvseDto::getBizStatus));
            }
        }
        return Pair.of(siteStatusMap, evseStatusMap);
    }

    private WarningBiDto mapWarningBiDto(WWarningRecord e, Map<String, SiteStatus> siteStatusMap,
        Map<String, EvseBizStatus> evseStatusMap) {
        WarningBiDto dto = new WarningBiDto();
        dto.setWarningId(e.getWarningId())
            .setStartTime(e.getStartTime())
            .setEndTime(e.getEndTime())
            .setBoxOutFactoryCode(e.getBoxOutFactoryCode())
            .setPlugIdx(e.getConnectorId())
            .setSourceNo(e.getSourceNo())
            .setSiteId(e.getSiteId())
            .setSiteName(e.getSiteName())
            .setWarningType(AlarmEventTypeEnum.valueOf(e.getWarningType()))
            .setWarningCode(e.getWarningCode())
            .setStatus(AlarmStatusEnum.valueOf(e.getStatus()))
            .setWarningName(e.getWarningName())
            .setWarningInstructions(e.getWarningInstructions())
            .setDuration(formatAlarmDuration(e));

        // 场站状态
        if (siteStatusMap != null && siteStatusMap.containsKey(e.getSiteId())) {
            dto.setSiteStatusStr(siteStatusMap.get(e.getSiteId()).getDesc());
        }

        // 桩状态
        if (evseStatusMap != null && evseStatusMap.containsKey(e.getDeviceId())) {
            dto.setEvseStatus(
                EvseBizStatus.valueOf(evseStatusMap.get(e.getDeviceId())).getDesc());
        }
        return dto;
    }

    private WarningBiI18nDto mapWarningBiI18nDto(WWarningRecord e,
        Map<String, SiteStatus> siteStatusMap,
        Map<String, EvseBizStatus> evseStatusMap, Locale locale,
        Map<String, EssEquipAlarmLangDto> codeMap) {
        WarningBiI18nDto dto = new WarningBiI18nDto();
        dto.setWarningId(e.getWarningId())
            .setStartTime(e.getStartTime())
            .setEndTime(e.getEndTime())
            .setBoxOutFactoryCode(e.getBoxOutFactoryCode().replaceFirst("充电桩\n", ""))
            .setPlugIdx(e.getConnectorId())
            .setSiteId(e.getSiteId())
            .setSiteName(e.getSiteName());
        // 处理告警状态
        dto.setWarningStatus(null == locale ? AlarmStatusEnum.valueOf(e.getStatus()).getLabel()
            : messageSource.getMessage(AlarmStatusEnum.valueOf(e.getStatus()).getLabel(), null,
                locale));
        // 告警名称   详细说明  数据库中提取
        if (null != codeMap && codeMap.containsKey(e.getWarningCode())) {
            dto.setWarningName(codeMap.get(e.getWarningCode()).getName());
            dto.setWarningInstructions(codeMap.get(e.getWarningCode()).getPrompt());
        } else {
            dto.setWarningName(e.getWarningName());
            dto.setWarningInstructions(e.getWarningInstructions());
        }
//        dto.setWarningName(
//            null == locale || StringUtils.isBlank(e.getWarningName()) ? e.getWarningName()
//                : messageSource.getMessage(e.getWarningName(), null,
//                    locale));
        // 处理补充说明
//        dto.setWarningInstructions(null == locale || StringUtils.isBlank(e.getWarningInstructions())
//            ? e.getWarningInstructions()
//            : messageSource.getMessage(e.getWarningInstructions(), null,
//                locale));
        return dto;
    }

    /**
     * 计算故障时长
     *
     * @param val
     * @return
     */
    private String formatAlarmDuration(WWarningRecord val) {
        // 已结束  但是没有结束时间
        if (val.getStatus() != 0 && val.getEndTime() == null) {
            return "--";
        }
        Instant start = val.getStartTime().toInstant();
        Instant end =
            (val.getStatus() != 0 && val.getEndTime() != null) ? val.getEndTime().toInstant()
                : Instant.now();

        Duration duration = Duration.between(start, end);
        long days = duration.toDays();
        long hours = duration.minusDays(days).toHours();
        long minutes = duration.minusDays(days).minusHours(hours).toMinutes();
        StringBuilder sb = new StringBuilder();
        if (days > 0) {
            return sb.append(days).append("天").toString();
        }
        if (hours > 0) {
            return sb.append(hours).append("小时").toString();
        }
        return sb.append(minutes > 0 ? minutes : 1).append("分钟").toString();
    }

    /**
     * 参数检查
     *
     * @param param
     */
    private void listChargeOrderBiByVinParamCheck(ListChargeOrderBiByVinParam param) {
        IotAssert.isTrue(param.getTopCommId() != null && param.getTopCommId() > 0,
            "集团商户ID为空");
        IotAssert.isTrue(param.getStart() != null && param.getStart() >= 0
            && param.getSize() != null && param.getSize() > 0, "分页参数ID为空");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getCommIds())
            || StringUtils.isNotBlank(param.getCommIdChain())
            || CollectionUtils.isNotEmpty(param.getGids())
            || CollectionUtils.isNotEmpty(param.getCorpOrgIds()), "无法获取登录信息");
    }


    public ListResponse<UserOrderCount> getUserOrderCountBi(SiteBiTopParam siteBiTopParam) {
        List<UserOrderCount> ret = biSiteOrderRoDs.selectUserOrderTop(siteBiTopParam);
        return new ListResponse<>(ret);
    }

    /**
     * 获取充电排行榜
     *
     * @param siteBiTopParam
     * @return
     */
    public ListResponse<UserOrderFee> getUserOrderChargeBi(SiteBiTopParam siteBiTopParam) {
        List<UserOrderFee> ret = biSiteOrderRoDs.selectUserChargeTop(siteBiTopParam);
        return new ListResponse<>(ret);
    }

    /**
     * @param siteBiParam
     * @return
     */
    public ListResponse<OrderCount> getOrderCount(SiteBiParam siteBiParam) {
        IotAssert.isNotNull(siteBiParam, "查询参数不能为空");

        String siteId = siteBiParam.getSiteId();
        BiDependOnType biDependOnType = convertType(siteBiParam.getDependOnTimeType());
        Date startTime = new Date(siteBiParam.getStartTime());
        Date endTime = new Date(siteBiParam.getEndTime());

        IotAssert.isNotNull(siteId, "场站id不能为空");
        IotAssert.isNotNull(biDependOnType, "查询时间节点不能为空");
        IotAssert.isNotNull(startTime, "开始时间不能为空");
        IotAssert.isNotNull(endTime, "结束时间不能为空");
        IotAssert.isNotNull(siteBiParam.getSampleType(), "采样时间不能为空");
        IotAssert.isTrue(siteBiParam.getStartTime() < siteBiParam.getEndTime(),
            "开始时间必须小于结束时间");

        List<Date> dateList =
            getDateRange(siteBiParam.getSampleType(), siteBiParam.getStartTime(),
                siteBiParam.getEndTime());

        IotAssert.isTrue(dateList.size() <= MAX_SAMPLE_COUNT,
            "超出最大采样数: " + MAX_SAMPLE_COUNT);

        List<BiSiteOrderPo> siteOrderBiList = biSiteOrderRoDs.selectBySiteIdWhen(siteId,
            biDependOnType,
            startTime,
            endTime);

        Map<Date, BiSiteOrderPo> biSiteMap =
            siteOrderBiList.stream().collect(Collectors.toMap(BiSiteOrderPo::getTime, e -> e));

        List<OrderCount> ret =
            dateList.stream().map(e -> {
                OrderCount orderCount = new OrderCount();
                orderCount.setSiteId(siteBiParam.getSiteId()).setTime(e);

                BiSiteOrderPo biSiteOrderPo = biSiteMap.get(e);

                if (biSiteOrderPo == null) {
                    orderCount.setId(0L).setOrderCount(0L);
                } else {
                    orderCount.setId(biSiteOrderPo.getId())
                        .setOrderCount(biSiteOrderPo.getOrderCount());
                }
                return orderCount;
            }).collect(Collectors.toList());

//        if(SiteBiSampleType.HOUR.equals(siteBiParam.getSampleType())) {
//            siteOrderBiList.stream().map(e -> {
//                OrderCount orderCount = new OrderCount();
//
//            })
//        }

        return new ListResponse<>(ret);
    }

    private List<Date> getDateRange(SiteBiSampleType sample, Long startTime, Long endTime) {

        IotAssert.isTrue(startTime < endTime, "开始时间必须小于结束时间");

        int timeField = -1;
        switch (sample) {
            case HOUR:
                timeField = Calendar.HOUR;
                break;
            case DAY:
                timeField = Calendar.DATE;
                break;
            case MONTH:
                timeField = Calendar.MONTH;
                break;
        }

        if (timeField == -1) {
            return new ArrayList<>();
        }

        List<Date> ret = new ArrayList<>();
        long currentTime = startTime;

        Calendar calendar = Calendar.getInstance();
        while (currentTime < endTime.longValue()) {
            Date date = new Date(currentTime);
            ret.add(date);

            calendar.setTime(date);
            calendar.add(timeField, 1);

            currentTime = calendar.getTime().getTime();
        }

        return ret;

    }

    private BiDependOnType convertType(SiteBiDependOnOrderTimeType siteBiDependOnOrderTimeType) {
        switch (siteBiDependOnOrderTimeType) {
            case CREATE_TIME:
                return BiDependOnType.CREATE_TIME;
            case STOP_TIME:
                return BiDependOnType.STOP_TIME;
            case CHARGE_START_TIME:
                return BiDependOnType.CHARGE_START_TIME;
            case CHARGE_END_TIME:
                return BiDependOnType.CHARGE_END_TIME;
            case PAY_TIME:
                return BiDependOnType.PAY_TIME;
            default:
                return null;
        }
    }

    public List<UserOrderElec> userChargeDivisionBi(SiteBiTopParam param) {
        return biSiteOrderRoDs.userChargeDivisionBi(param);
    }

    public Mono<ChargeOrderBiVo> getOrderBi(@Nullable String commIdChain, @Nullable String siteId,
        @Nullable List<Integer> bizTypeList) {
        boolean containHlhtFlag = false;
        if (CollectionUtils.isNotEmpty(bizTypeList)) {
            if (bizTypeList.contains(BizType.HLHT.getCode())) {
                containHlhtFlag = true;
            }
        }
        boolean finContainHlhtFlag = containHlhtFlag;
        return Mono.zip(
                Mono.just(
                    this.siteTotalBiRoDs.orderBiOfCommChain(commIdChain, siteId, bizTypeList)),
//                Mono.just(
//                    this.orderBiRoDs.getOrderBiOfCommChain(commIdChain, siteId, null, bizTypeList)),
                Mono.just(LocalDate.now())
                    .map(x -> {
                        TimeFilter stopTimeFilter = new TimeFilter();
                        stopTimeFilter.setStartTime(DateUtil.localDateToDate(x))
                            .setEndTime(DateUtil.localDateToDate(x.plusDays(1)));
                        return this.orderBiRoDs.getOrderBi(commIdChain, siteId, stopTimeFilter,
                            finContainHlhtFlag);
                    }))
            .map(tuple -> {
                ChargeOrderBiVo res = tuple.getT1();
                ChargeOrderBiVo todayData = tuple.getT2();
                res.setTodayOrderNum(todayData.getOrderNum())
                    .setTodayElec(todayData.getElec())
                    .setTodayFee(todayData.getFee())
                    .setTodayServFee(todayData.getServFee());
                return res;
            });
    }

    /**
     * 获取过去7天的充电启动方式统计
     *
     * @return
     */
    public List<OrderStartTypeBiDto> getOrderStartTypeBiList7(Long topCommId, String commIdChain) {
        TimeFilter stopTimeFilter = new TimeFilter();
        stopTimeFilter.setStartTime(DateUtils.toDate(LocalDateTime.now().plusDays(-7)))
            .setEndTime(new Date());
        ListChargeOrderParam param = new ListChargeOrderParam();
        param.setTopCommId(topCommId)
            .setCommIdChain(commIdChain)
            .setStopTimeFilter(stopTimeFilter);
        return this.chargerOrderRoDs.getOrderStartTypeBiList(param);
    }


    /**
     * 过去7天的用户统计
     */
    public Long getActiveCusCount7(Long topCommId, String commIdChain) {
        TimeFilter stopTimeFilter = new TimeFilter();
        stopTimeFilter.setStartTime(DateUtils.toDate(LocalDateTime.now().plusDays(-7)))
            .setEndTime(new Date());
        ListChargeOrderParam orderParam = new ListChargeOrderParam();
        orderParam.setStopTimeFilter(stopTimeFilter)
            .setTopCommId(topCommId)
            .setCommIdChain(commIdChain);
        return this.chargerOrderRoDs.getCusCount(orderParam);
    }


    /**
     * 按时间分组统计
     *
     * @param timeType
     * @param commIdChain
     * @return
     */
    public List<SiteOrderBiVo> getTimeGroupingOrderBiList(SiteBiSampleType timeType, String siteId,
        String commIdChain) {
        log.debug("timeType = {}, siteId = {}, commIdChain = {}", timeType, siteId, commIdChain);
        SiteBiParam param = new SiteBiParam();
        param.setIncludedHlhtSite(false)
            .setSiteId(siteId)
            .setSampleType(timeType)
            .setCommIdChain(commIdChain);
        LocalDateTime startTime = null;
        LocalDate startDate = null;
        if (SiteBiSampleType.MONTH == timeType) {
            startDate = LocalDate.now().withDayOfMonth(1).minusMonths(12);
            param.setFromTime(DateUtils.toDate(startDate))
                .setToTime(new Date());
        } else if (SiteBiSampleType.WEEKLY == timeType) {
            startDate = LocalDate.now().with(ChronoField.DAY_OF_WEEK, 1).minusWeeks(16);
            param.setFromTime(DateUtils.toDate(startDate))
                .setToTime(new Date());
        } else if (SiteBiSampleType.DAY == timeType) {
            startDate = LocalDate.now().minusDays(31);
            param.setFromTime(DateUtils.toDate(startDate))
                .setToTime(new Date());
        } else {
            param.setSampleType(SiteBiSampleType.HOUR);
            startTime = LocalDateTime.now().plusHours(-25);
            param.setFromTime(DateUtils.toDate(startTime))
                .setToTime(new Date());
        }
        List<SiteOrderBiVo> list = biSiteOrderRoDs.getTimeGroupingSiteBiList(param);
        if (SiteBiSampleType.MONTH == param.getSampleType()) {
            list = BiUtils.buildMonthlyList(startDate, LocalDate.now(),
                list, (s) -> this.buildSiteOrderBi(param.getSampleType(), s));
        } else if (SiteBiSampleType.WEEKLY == param.getSampleType()) {
            list = BiUtils.buildWeeklyList(startDate, LocalDate.now(),
                list, (s) -> this.buildSiteOrderBi(param.getSampleType(), s));
        } else if (SiteBiSampleType.DAY == param.getSampleType()) {
            list = BiUtils.buildDailyList(startDate, LocalDate.now(),
                list, (s) -> this.buildSiteOrderBi(param.getSampleType(), s));
        } else {
            list = BiUtils.buildHourlyList(startTime, LocalDateTime.now(),
                list, (s) -> this.buildSiteOrderBi(param.getSampleType(), s));
        }
        return list;
    }


    private SiteOrderBiVo buildSiteOrderBi(SiteBiSampleType timeType, String time) {
        SiteOrderBiVo vo = new SiteOrderBiVo();
        vo.setTimeType(timeType)
            .setTime(time)
            .setElec(BigDecimal.ZERO)
            .setElecFee(BigDecimal.ZERO)
            .setServFee(BigDecimal.ZERO)
            .setOrderNum(0L);
        return vo;
    }

    /**
     * 按日期分组统计枪头使用量数据
     *
     * @param days
     * @param commIdChain
     */
    public List<SiteUtilization> getDateGroupingPlugUsageBiList(int days, String commIdChain) {
        LocalDate startDate = LocalDate.now().minusDays(days);
        SiteBiParam param = new SiteBiParam();
        param.setFromTime(DateUtils.toDate(startDate))
            .setToTime(new Date())
            .setCommIdChain(commIdChain);
        List<SiteUtilization> list = this.biPlugRoDs.getDateGroupingPlugUsageBiList(param);
        list = BiUtils.buildDailyList(startDate,
            LocalDate.now(),
            list,
            (s) -> {
                SiteUtilization vo = new SiteUtilization();
                vo.setDate(DateUtil.str2Date(s));
                return vo;
            });
        return list;
    }


    /**
     * 按客户类型统计7/30日充电订单
     *
     * @param commIdChain
     * @return
     */
    @Transactional
    public CusOrderBiVo getAccTypeGroupingFeeBiList(String commIdChain) {
        CusOrderBiVo result = new CusOrderBiVo();
        result.setCorpOrderFee7(BigDecimal.ZERO)
            .setPersonalOrderFee7(BigDecimal.ZERO)
            .setPersonalOrderFee30(BigDecimal.ZERO)
            .setCorpOrderFee30(BigDecimal.ZERO);

        // 获取7日统计
        TimeFilter stopTimeFilter = new TimeFilter();
        stopTimeFilter.setStartTime(DateUtils.toDate(LocalDate.now().minusDays(7)))
            .setEndTime(new Date());
        ListChargeOrderParam param = new ListChargeOrderParam();
        param.setStopTimeFilter(stopTimeFilter)
            .setCommIdChain(commIdChain);
        List<OrderAccountTypeBiDto> list7Days = this.chargerOrderRoDs.getAccTypeGroupingFeeBiList(
            param);
        for (OrderAccountTypeBiDto item : list7Days) {
            if (PayAccountType.CREDIT == item.getAccType()
                || PayAccountType.CORP == item.getAccType()) {
                BigDecimal fee = result.getCorpOrderFee7()
                    .add(item.getElecFee())
                    .add(item.getServFee());
                result.setCorpOrderFee7(fee);
            } else {
                BigDecimal fee = result.getPersonalOrderFee7()
                    .add(item.getElecFee())
                    .add(item.getServFee());
                result.setPersonalOrderFee7(fee);
            }
        }

        // 获取30日统计
        stopTimeFilter.setStartTime(DateUtils.toDate(LocalDate.now().minusDays(30)))
            .setEndTime(new Date());
        param.setStopTimeFilter(stopTimeFilter);
        List<OrderAccountTypeBiDto> list30Days = this.chargerOrderRoDs.getAccTypeGroupingFeeBiList(
            param);
        for (OrderAccountTypeBiDto item : list30Days) {
            if (PayAccountType.CREDIT == item.getAccType()
                || PayAccountType.CORP == item.getAccType()) {
                BigDecimal fee = result.getCorpOrderFee30()
                    .add(item.getElecFee())
                    .add(item.getServFee());
                result.setCorpOrderFee30(fee);
            } else {
                BigDecimal fee = result.getPersonalOrderFee30()
                    .add(item.getElecFee())
                    .add(item.getServFee());
                result.setPersonalOrderFee30(fee);
            }
        }
        return result;
    }

    /**
     * 统计省/市/场站的今天,本周,本月,今年充电数据
     *
     * @param cityCode
     * @param commIdChain
     * @return
     */
    public GeoOrderBiDto getGeoOrderBi(String provinceCode,
        String cityCode,
        String siteId,
        String commIdChain) {
        //CitySiteBiVo city = this.cityRoDs.getCitySiteBi(cityCode, commIdChain);

        TimeFilter stopTimeFilter = new TimeFilter();
        ListChargeOrderParam param = new ListChargeOrderParam();

        param.setStopTimeFilter(stopTimeFilter)
            .setCommIdChain(commIdChain);
        if (StringUtils.isNotBlank(provinceCode)) {
            param.setProvinceCodeList(List.of(provinceCode));
        }
        if (StringUtils.isNotBlank(cityCode)) {
            param.setCityCodeList(List.of(cityCode));
        }
        if (StringUtils.isNotBlank(siteId)) {
            param.setSiteIdList(List.of(siteId));
        }

        List<SiteOrderBiVo> list = new ArrayList<>();
        // 今日数据
        stopTimeFilter.setStartTime(DateUtils.toDate(LocalDate.now()))
            .setEndTime(new Date());
        log.debug("param = {}", param);
        OrderThinBiDto bi = this.chargerOrderRoDs.getOrderThinBi(param);
        SiteOrderBiVo todayBiResult = new SiteOrderBiVo();
        todayBiResult.setTimeType(SiteBiSampleType.DAY)
            .setOrderNum(bi.getOrderNum())
            .setElec(bi.getElec())
            .setElecFee(bi.getElecFee())
            .setServFee(bi.getServFee());
        list.add(todayBiResult);

        // 本周数据
        stopTimeFilter.setStartTime(DateUtils.getFirstDayOfWeek03(LocalDate.now()));
        bi = this.chargerOrderRoDs.getOrderThinBi(param);
        SiteOrderBiVo week = new SiteOrderBiVo();
        week.setTimeType(SiteBiSampleType.WEEKLY)
            .setOrderNum(bi.getOrderNum())
            .setElec(bi.getElec())
            .setElecFee(bi.getElecFee())
            .setServFee(bi.getServFee());
        list.add(week);

        // 本月数据
        stopTimeFilter.setStartTime(DateUtils.toDate(LocalDate.now().withDayOfMonth(1)));
        bi = this.chargerOrderRoDs.getOrderThinBi(param);
        SiteOrderBiVo month = new SiteOrderBiVo();
        month.setTimeType(SiteBiSampleType.MONTH)
            .setOrderNum(bi.getOrderNum())
            .setElec(bi.getElec())
            .setElecFee(bi.getElecFee())
            .setServFee(bi.getServFee());
        list.add(month);

        // 今年数据
        stopTimeFilter.setStartTime(DateUtils.toDate(LocalDate.now().withDayOfYear(1)));
        bi = this.chargerOrderRoDs.getOrderThinBi(param);
        SiteOrderBiVo year = new SiteOrderBiVo();
        year.setTimeType(SiteBiSampleType.YEAR)
            .setOrderNum(bi.getOrderNum())
            .setElec(bi.getElec())
            .setElecFee(bi.getElecFee())
            .setServFee(bi.getServFee());
        list.add(year);

        GeoOrderBiDto result = new GeoOrderBiDto();
        result//.setCityCode(city.getCityCode())
            //  .setCityName(city.getCityName())
            // .setSiteNum(city.getSiteNum())
            .setOrderBi(list);
        return result;
    }

    public Long countVin(ListChargeOrderBiByVinParam param) {
        if (StringUtils.isBlank(param.getCommIdChain())) {
            throw new DcArgumentException("请提供商户Id链");
        }

        return this.orderBiRoDs.countVin(param);
    }

    //    @Async
    public void exportVinBi(ListChargeOrderBiByVinParam param) {

        param.setVinId(null).setVinExact(null); // 设置为空,查出全部详情
        Map<String, List<VinBiVo>> stringVinBiVoMap = orderBiRoDs.getVinBiDetail(param).stream()
            .collect(Collectors.groupingBy(VinBiVo::getVin, Collectors.toList()));

        excelFileService.exportExcelFile(
            param.getExcelPosition().getSubDir(),
            param.getExcelPosition().getSubFileName(),
            VinBiVo.class,
            BiExportGroups.VIN_BI.getCode(),
            BiExportGroups.VIN_BI.getName(),
            null,
            param.getFilter(),
            null,
            (start, size) -> {
                param.setStart((long) ((start - 1) * size))
                    .setSize(size);
                param.setTotal(Boolean.FALSE);

                ListResponse<VinBiVo> vinBi = this.getVinBi(param);
                return new ArrayList<>(vinBi.getData());
            },
            (e) -> e.stream().map(x -> {
                VinBiVo vinBiVo = (VinBiVo) x;
                if (DecimalUtils.isZero(vinBiVo.getOrderCount())) {
                    return List.of(vinBiVo);
                }
                //若订单数不为0，则追加下拉详情
                List<VinBiVo> res = new ArrayList<>();
                res.add(vinBiVo);
                List<VinBiVo> detail = stringVinBiVoMap.get(vinBiVo.getVin());
                if (CollectionUtils.isNotEmpty(detail)) {
//                        detail.clearSomeFields();
                    res.addAll(detail);
                }
                return res;
            }).flatMap(List::stream).collect(Collectors.toList())
        );
    }

    @Transactional(readOnly = true)
    public ListResponse<VinBiVo> getVinBi(ListChargeOrderBiByVinParam param) {
//        if (StringUtils.isBlank(param.getCommIdChain())) {
//            throw new DcArgumentException("请提供商户Id链");
//        }
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getGids()) || StringUtils.isNotBlank(
            param.getCommIdChain()), "请求信息不完整");

//        Long total = this.orderBiRoDs.countVin(param);
//        if (null == total || total <= 0) {
//            return RestUtils.buildListResponse(new ArrayList<>(), 0);
//        }

//        try {
        Long total = null;
        if (Boolean.TRUE.equals(param.getTotal())) {
//                CompletableFuture<Long> future = CompletableFuture.supplyAsync(
//                        () -> );

            total = this.orderBiRoDs.countVin(param);
        }
        List<VinBiVo> list = this.orderBiRoDs.getBiListOfVin02(param);

        return new ListResponse<>(list, total);
//        } catch (InterruptedException | ExecutionException | TimeoutException e) {
//            log.warn("查询统计出错: err = {}", e.getMessage(), e);
//        }

//        return RestUtils.buildListResponse(new ArrayList<>(), 0);
    }

    public ListResponse<VinBiVo> getVinBiDetail(ListChargeOrderBiByVinParam param) {
        IotAssert.isTrue(StringUtils.isNotBlank(param.getCommIdChain()) ||
            CollectionUtils.isNotEmpty(param.getGids()), "请提供商户Id链");
        IotAssert.isNotNull(param.getVinExact(), "vinExact不能为空");
//        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getSiteIdList()), "siteIdList不能为空");

        return RestUtils.buildListResponse(orderBiRoDs.getVinBiDetail(param));
    }

    public Long countCommercial(ListBiCommercialParam param) {
        ListCommercialParam listCommParam = new ListCommercialParam();
        BeanUtils.copyProperties(param, listCommParam);
        listCommParam.setCommIdList(param.getCommIds());
        return this.commRoDs.getCount(listCommParam);
    }

    public ListResponse<CommercialBiVo> getCommBi(ListBiCommercialParam param) {
        if (StringUtils.isBlank(param.getCommIdChain())) {
            throw new DcArgumentException("请提供商户Id链");
        }

        if (param.getCommIds() != null) {
            param.setCommIdList(param.getCommIds());
        }

//        // 统计所有商户
//        Long total = 0L;
//        if (param.getTotal()) {
//            total = this.countCommercial(param);
//            if (total <= 0) {
//                return RestUtils.buildListResponse(new ArrayList<>(), 0);
//            }
//        }

        try {
            Long total = 0L;
            List<CommercialBiVo> list = new ArrayList<>();
            if (param.getTotal() != null && param.getTotal()) {
                CompletableFuture<Long> future = CompletableFuture.supplyAsync(
                    () -> this.countCommercial(param));
                list = this.orderBiRoDs.getBiListOfComm(param);
                total = future.get(10, TimeUnit.MINUTES);

            } else {
                list = this.orderBiRoDs.getBiListOfComm(param);
            }

            return RestUtils.buildListResponse(list, total == null ? 0 : total);
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            log.warn("查询统计出错: err = {}", e.getMessage(), e);
        }

        return RestUtils.buildListResponse(new ArrayList<>(), 0);
    }

    //    @Async
    public void exportCommBi(ListBiCommercialParam param) {
        excelFileService.exportExcelFile(
            param.getExcelPosition().getSubDir(),
            param.getExcelPosition().getSubFileName(),
            CommercialBiVo.class,
            BiExportGroups.COMM_BI.getCode(),
            BiExportGroups.COMM_BI.getName(),
            null,
            param.getFilter(),
            null,
            (start, size) -> {
                param.setStart((long) ((start - 1) * size))
                    .setSize(size);
                param.setTotal(Boolean.FALSE);

                ListResponse<CommercialBiVo> commBi = this.getCommBi(param);
                return new ArrayList<>(commBi.getData());
            },
            (e) -> e.stream().map(x -> (CommercialBiVo) x).collect(Collectors.toList())
        );
    }

    public Long countSite(ListBiSiteParam param) {
        ListSiteParam listSiteParam = new ListSiteParam();
        BeanUtils.copyProperties(param, listSiteParam);

        if (null != param.getProvince()) {
            listSiteParam.setProvinceCode(String.valueOf(param.getProvince()));
        }

        if (null != param.getCity()) {
            listSiteParam.setCityCode(String.valueOf(param.getCity()));
        }

        return this.siteRoDs.countSite(listSiteParam);
    }

    public ListResponse<SiteBiVo> getSiteBi(ListBiSiteParam param) {
//        if (StringUtils.isBlank(param.getCommIdChain())) {
//            throw new DcArgumentException("请提供商户Id链");
//        }
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getGids()) || StringUtils.isNotBlank(
            param.getCommIdChain()), "参数信息不完整");

//        // 获取场站列表
//        Long total = 0L;
//        if (param.getTotal()) {
//            total = this.countSite(param);
//            if (total <= 0) {
//                return RestUtils.buildListResponse(new ArrayList<>(), 0);
//            }
//        }

        try {
            if (CollectionUtils.isEmpty(param.getStatusList())) {
                param.setStatusList(
                    List.of(SiteStatus.OPENING, SiteStatus.ONLINE, SiteStatus.UNAVAILABLE));
            }

            // 默认查询状态
            Long total = 0L;
            List<SiteBiVo> list = new ArrayList<>();
            if (param.getTotal() != null && param.getTotal()) {
                CompletableFuture<Long> future = CompletableFuture.supplyAsync(
                    () -> this.countSite(param));
                list = this.orderBiRoDs.getBiListOfSite(param);
                total = future.get(10, TimeUnit.MINUTES);
            } else {
                list = this.orderBiRoDs.getBiListOfSite(param);
            }

            return RestUtils.buildListResponse(list, total == null ? 0 : total);
        } catch (InterruptedException | ExecutionException | TimeoutException e) {
            log.warn("查询统计出错: err = {}", e.getMessage(), e);
        }

        return RestUtils.buildListResponse(new ArrayList<>(), 0);
    }

    //    @Async
    public void exportSiteBi(ListBiSiteParam param) {
        excelFileService.exportExcelFile(
            param.getExcelPosition().getSubDir(),
            param.getExcelPosition().getSubFileName(),
            SiteBiVo.class,
            BiExportGroups.SITE_BI.getCode(),
            BiExportGroups.SITE_BI.getName(),
            null,
            param.getFilter(),
            null,
            (start, size) -> {
                param.setStart((long) ((start - 1) * size))
                    .setSize(size);
                param.setTotal(Boolean.FALSE);

                ListResponse<SiteBiVo> siteBi = this.getSiteBi(param);
                return new ArrayList<>(siteBi.getData());
            },
            (e) -> e.stream().map(x -> (SiteBiVo) x).collect(Collectors.toList())
        );
    }

    public Mono<ListResponse<SiteOrderAccountData>> getOrderAccountBi(Integer days, String siteId) {
        IotAssert.isNotNull(days, "天数不能为空");
        return Mono.just(days)
            .map(e -> {
                Date toTime = DateUtil.localDateToDate(LocalDate.now());
                Date fromTime = DateUtil.localDateToDate(LocalDate.now().minusDays(days));

                SiteBiParam siteBiParam = new SiteBiParam();
                siteBiParam.setDependOnTimeType(SiteBiDependOnOrderTimeType.STOP_TIME)
                    .setSiteId(siteId)
                    .setFromTime(fromTime)
                    .setToTime(toTime);
                return biSiteOrderAccountRoDs.getBiOrderAccount(siteBiParam);
            })
            .map(RestUtils::buildListResponse);
    }

    public Mono<ObjectResponse<ChargerOrderDataBiVo>> getChargerOrderDetailData(
        ChargerOrderParam chargerOrderParam) {
        IotAssert.isNotNull(chargerOrderParam, "查询参数不能为空");
        return Mono.just(chargerOrderParam)
            .map(e -> {
                final List<ChargerOrderDataVo> detail = chargerOrderRoDs.getChargerBiOrderDetail(
                    chargerOrderParam);
                final List<ChargerOrderDataVo> data = chargerOrderRoDs.getChargerBiOrderData(
                    chargerOrderParam);
                ChargerOrderDataBiVo ret = new ChargerOrderDataBiVo();
                return ret.setDetail(detail).setData(data);
            })
            .map(RestUtils::buildObjectResponse);
    }

    public Mono<ObjectResponse<Map<String, BigDecimal>>> getCurrentPowerBySiteIds(
        List<String> siteIdList) {
        IotAssert.isNotNull(siteIdList, "查询参数不能为空");
        return Mono.just(siteIdList)
            .map(e -> {
                // 固定取4个状态
                List<ChargeOrderStatus> statuses = new ArrayList<>();
                statuses.add(ChargeOrderStatus.STARTING);
                statuses.add(ChargeOrderStatus.START);
                statuses.add(ChargeOrderStatus.STOPPING);
                statuses.add(ChargeOrderStatus.ERROR);
                // 获取充电中的 <场站ID, List<订单号>>
                List<ChargerOrderSample> chargingOrderNoBySiteIds =
                    chargerOrderRoDs.getChargingOrderNoBySiteIds(statuses, e);
                Map<String, List<String>> siteIdAndOrderNo = chargingOrderNoBySiteIds.stream()
                    .collect(
                        Collectors.groupingBy(ChargerOrderSample::getStationId,
                            Collectors.mapping(ChargerOrderSample::getOrderNo,
                                Collectors.toList())));
                // 分组后去根据订单号到redis中获取总功率
                Map<String, BigDecimal> result = new HashMap<>();
                siteIdAndOrderNo.forEach((key, value) -> {
                    List<ChargeOrderCache> orderVoList = redisUtil.getOrderVoList(value);
                    BigDecimal decimal = BigDecimal.ZERO;
                    for (ChargeOrderCache cache : orderVoList) {
                        BigDecimal power =
                            cache.getPower() != null ? cache.getPower() : BigDecimal.ZERO;
                        decimal = decimal.add(power);
                    }
                    result.put(key, decimal);
                });
                log.debug("根据OrderNo从Redis中批量获取电量数据结果 = {}", result);
                return result;
            })
            .map(RestUtils::buildObjectResponse);
    }
}
