package com.cdz360.biz.bi.service.download.impl;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.bi.service.download.IFileExport;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.parts.param.ListPartsParamEx;
import com.cdz360.biz.model.parts.vo.PartsVo;
import com.cdz360.biz.model.parts.vo.PartsWithVo;
import com.cdz360.biz.model.sys.constant.SiteGroupType;
import com.cdz360.biz.model.sys.dto.UserOwnerSiteGroupDto;
import com.cdz360.biz.model.sys.param.ListSiteGroupParam;
import com.cdz360.biz.model.sys.po.SiteGroupPo;
import com.cdz360.biz.model.sys.vo.SiteGroupVo;
import com.cdz360.biz.model.trading.bi.dto.BiExportGroups;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.utils.feign.auth.AuthSiteGroupFeignClient;
import com.cdz360.biz.utils.feign.auth.AuthSysUserFeignClient;
import com.cdz360.biz.utils.feign.iot.DevicePartsFeignClient;
import com.chargerlinkcar.framework.common.utils.ExcelUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class PartsListExportImpl extends AbstractFileExport<ListPartsParamEx, ExcelPosition>
    implements IFileExport<ListPartsParamEx, ExcelPosition> {

    @Autowired
    private AuthSysUserFeignClient sysUserFeignClient;

    @Autowired
    private AuthSiteGroupFeignClient authSiteGroupFeignClient;

    @Autowired
    private DevicePartsFeignClient devicePartsFeignClient;

    @PostConstruct
    public void init() {
        this.downloadFileProxy.addProxy(DownloadFunctionType.PARTS_LIST, this);
    }

    @Override
    public Class<ListPartsParamEx> paramClazz() {
        return ListPartsParamEx.class;
    }

    private List<PartsWithVo> request(final ListPartsParamEx param, int start, int size) {
        param.setStart(((long) (start - 1) * size))
            .setSize(size);
        param.setTotal(Boolean.FALSE);

        ListSiteGroupParam sgParam = new ListSiteGroupParam()
            .setTypeList(List.of(SiteGroupType.YW)); // 仅关注运维
        List<PartsWithVo> block = this.devicePartsFeignClient.findParts(param)
            .doOnNext(FeignResponseValidate::check)
            .flatMap(res -> {
                Set<Long> uidSet = new HashSet<>();
                Set<Long> uidSetX = new HashSet<>();
                for (PartsVo item : res.getData()) {
                    if (null != item.getCreateBy()) {
                        uidSet.add(item.getCreateBy());
                    }

                    if (null != item.getApplyBy()) {
                        uidSet.add(item.getApplyBy());
                    }

                    if (null != item.getStorageUid()) {
                        uidSetX.add(item.getStorageUid());
                    }
                }

                sgParam.setUidList(new ArrayList<>(uidSetX));
                return Mono.zip(Mono.just(uidSetX).filter(CollectionUtils::isNotEmpty)
                            .flatMap(x -> authSiteGroupFeignClient.userOwnerSiteGroup(sgParam))
                            .doOnNext(FeignResponseValidate::check)
                            .map(ListResponse::getData)
                            .switchIfEmpty(Mono.just(List.of())), // 所属场站组
                        Mono.just(uidSet).filter(CollectionUtils::isNotEmpty)
                            .flatMap(x -> sysUserFeignClient.getSysUserByIdList(
                                new ArrayList<>(x)))
                            .doOnNext(FeignResponseValidate::check)
                            .map(ListResponse::getData)
                            .switchIfEmpty(Mono.just(List.of()))) // 用户名称调整显示
                    .map(data -> {
                        final List<UserOwnerSiteGroupDto> userGroupList = data.getT1();
                        final List<SysUserVo> userList = data.getT2();
                        // 用户场站组调整
                        final Map<Long, List<SiteGroupVo>> uidGroupMap = userGroupList.stream()
                            .collect(Collectors.toMap(UserOwnerSiteGroupDto::getUid,
                                UserOwnerSiteGroupDto::getGroupVoList));

                        // 用户名称调整
                        final Map<Long, String> uidMap = userList.stream()
                            .collect(Collectors.toMap(SysUserVo::getId, SysUserVo::getName));

                        for (PartsWithVo x : res.getData()) {
                            if (null != x.getCreateBy() && uidMap.containsKey(x.getCreateBy())) {
                                x.setCreateByName(uidMap.get(x.getCreateBy()));
                            }
                            if (null != x.getApplyBy() && uidMap.containsKey(x.getApplyBy())) {
                                x.setApplyByName(uidMap.get(x.getApplyBy()));
                            }

                            if (null != x.getStorageUid() &&
                                uidGroupMap.containsKey(x.getStorageUid())) {
                                final List<SiteGroupVo> list = uidGroupMap.get(
                                    x.getStorageUid());
                                x.setSiteGroupList(list);
                                x.setGidList(list.stream().map(SiteGroupPo::getGid).collect(
                                    Collectors.toList()));
                                x.setGroupNameList(list.stream().map(SiteGroupPo::getName).collect(
                                    Collectors.toList()));
                            }
                        }
                        return res;
                    });
            })
            .map(ListResponse::getData)
            .block(Duration.ofMinutes(2L));
        return block != null ? block : List.of();
    }

    @Override
    public void genFile(String context, String pos) throws IOException {
        final ListPartsParamEx param = this.convert(context);
        param.setTotal(false); // 不需要查询总数
        final ExcelPosition position = this.convertPos(pos);

        log.info("物料列表导出到EXCEL: {}", position);
        ExcelUtil.builder(exportFileConfig.getExcelDir(), position,
                BiExportGroups.PARTS_LIST.getName())
            .addHeader(PartsWithVo.class)
            .loopAppendData((start, size) -> new ArrayList<>(this.request(param, start, size)))
            .write2File();
    }
}
