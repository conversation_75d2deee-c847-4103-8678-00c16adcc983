package com.cdz360.biz.bi.feign.reactor;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaAnalysisPointParam;
import com.cdz360.biz.model.tj.kc.param.ListTjDailyChargingDurationParam;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisPointWithSiteVo;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

@ReactiveFeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_TJ,
    fallbackFactory = BizTjFeignHystrix.class)
public interface BizTjFeignClient {

    // 获取投建分析划分点(带场站列表信息)
    @PostMapping("/tj/area/analysis/point/findTjAnalysisPointWithSite")
    Mono<ListResponse<TjAreaAnalysisPointWithSiteVo>> findTjAnalysisPointWithSite(
        @RequestBody ListTjAreaAnalysisPointParam param);

    @PostMapping("/tj/financialData/findTjDailyChargingDuration")
    Mono<ListResponse<TjDailyChargingDurationPo>> findTjDailyChargingDuration(
        @RequestBody ListTjDailyChargingDurationParam param);
}
