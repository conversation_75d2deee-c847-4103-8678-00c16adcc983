package com.cdz360.biz.bi.service;

import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.UserType;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.model.corp.type.CorpType;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.bi.config.ExportFileConfig;
import com.cdz360.biz.bi.feign.AuthCenterFeignClient;
import com.cdz360.biz.bi.feign.DataCoreFeignClient;
import com.cdz360.biz.bi.feign.InvoiceFeignClient;
import com.cdz360.biz.bi.utils.PdfUtil;
import com.cdz360.biz.ds.trading.ro.prerun.ds.PrerunAssetImgRoDs;
import com.cdz360.biz.ds.trading.ro.prerun.ds.PrerunCheckRoDs;
import com.cdz360.biz.ds.trading.ro.prerun.ds.PrerunEvseRoDs;
import com.cdz360.biz.ds.trading.ro.prerun.ds.PrerunRoDs;
import com.cdz360.biz.ds.trading.ro.rover.ds.SiteRoverAssetRoDs;
import com.cdz360.biz.ds.trading.ro.rover.ds.SiteRoverRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ds.trading.ro.yw.ds.SiteInspectionRecordRoDs;
import com.cdz360.biz.ds.trading.ro.yw.ds.YwOrderRoDs;
import com.cdz360.biz.model.FileItem;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.settlement.param.ListSettlementParam;
import com.cdz360.biz.model.cus.settlement.type.SettlementStatusEnum;
import com.cdz360.biz.model.cus.user.po.SysUserPo;
import com.cdz360.biz.model.cus.vo.SysUserVo;
import com.cdz360.biz.model.finance.type.TaxStatus;
import com.cdz360.biz.model.invoice.type.InvoicedStatus;
import com.cdz360.biz.model.invoice.type.ProductType;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.iot.param.ListGtiParam;
import com.cdz360.biz.model.iot.vo.GtiVo;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.cdz360.biz.ess.model.param.ListEssParam;
import com.cdz360.biz.model.trading.ess.vo.EssVo;
import com.cdz360.biz.model.trading.invoice.dto.InvoiceRecordOrderRefDto;
import com.cdz360.biz.model.trading.invoice.param.ListInvoiceRecordOrderRefParam;
import com.cdz360.biz.model.trading.iot.vo.EvseInfoVo;
import com.cdz360.biz.model.trading.order.param.ListChargeOrderParam;
import com.cdz360.biz.model.trading.order.param.PayBillParam;
import com.cdz360.biz.model.trading.prerun.dto.PrerunMixDto;
import com.cdz360.biz.model.trading.rover.po.SiteRoverAssetPo;
import com.cdz360.biz.model.trading.rover.type.RoverAssertType;
import com.cdz360.biz.model.trading.rover.vo.RoverPdfExportVo;
import com.cdz360.biz.model.trading.rover.vo.SiteRoverVo;
import com.cdz360.biz.model.trading.site.po.SiteInspectionRecordPo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.site.type.SiteGcType;
import com.cdz360.biz.model.trading.site.type.SiteInspectionType;
import com.cdz360.biz.model.trading.yw.dto.Goods;
import com.cdz360.biz.model.trading.yw.type.YwType;
import com.cdz360.biz.model.trading.yw.vo.YwOrderVo;
import com.cdz360.biz.utils.feign.auth.AuthSysUserFeignClient;
import com.cdz360.biz.utils.feign.data.DeviceDataCoreClient;
import com.cdz360.biz.utils.feign.iot.DeviceFeignClient;
import com.cdz360.biz.utils.feign.iot.GtiFeignClient;
import com.cdz360.biz.utils.feign.iot.IotPvFeignClient;
import com.cdz360.biz.utils.feign.oa.OaRechargeClient;
import com.cdz360.biz.utils.feign.order.DataCoreOrderFeignClient;
import com.cdz360.biz.utils.feign.user.UserFeignClient;
import com.cdz360.biz.utils.service.OssService;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.framework.common.constant.OrderStatus;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceInfoParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceInfoVo;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceRecordDetail;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceRecordExport;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderSite;
import com.chargerlinkcar.framework.common.domain.vo.OrderBiVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderTimeShareBiVo;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.fasterxml.jackson.core.type.TypeReference;
import com.itextpdf.text.DocumentException;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class PdfService {

    @Autowired
    private ExportFileConfig exportFileConfig;

    //    @Value("${pdf.dir:/tmp/pdf}")
//    private String TEMP_DIR;
    private static final String CHARSET = "utf-8";

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyyMMdd");

    @Autowired
    private SiteInspectionRecordRoDs recordRoDs;
    @Autowired
    private SiteRoDs siteRoDs;
    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;
    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;
    @Autowired
    private IotPvFeignClient iotPvFeignClient;

    @Autowired
    private DeviceFeignClient deviceFeignClient;

    @Autowired
    private GtiFeignClient gtiFeignClient;

    @Autowired
    private YwOrderRoDs ywOrderRoDs;

    @Autowired
    private RedisIotReadService redisIotReadService;

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private PrerunRoDs prerunRoDs;

    @Autowired
    private PrerunEvseRoDs prerunEvseRoDs;

    @Autowired
    private PrerunAssetImgRoDs prerunAssetImgRoDs;

    @Autowired
    private PrerunCheckRoDs prerunCheckRoDs;

    @Autowired
    private SiteRoverRoDs siteRoverRoDs;

    @Autowired
    private SiteRoverAssetRoDs siteRoverAssetRoDs;

    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;

    @Autowired
    private InvoiceFeignClient invoiceFeignClient;

    @Autowired
    private DataCoreOrderFeignClient dataCoreOrderFeignClient;

    @Autowired
    private OaRechargeClient oaRechargeClient;

    @Autowired
    private AuthSysUserFeignClient authSysUserFeignClient;

    @Autowired
    private DeviceDataCoreClient deviceDataCoreClient;
    @Autowired
    private OssService ossService;

    public void cleanPdfFiles() {
        log.info("开始删除文件...");
        long startTime = System.currentTimeMillis();
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");

        //删除子文件夹
        File deleteFile = new File(exportFileConfig.getPdfDir());
        File[] deleteFiles = deleteFile.listFiles();
        if (null == deleteFiles) {
            return;
        }

        for (File file : deleteFiles) {
            if (exportFileConfig.getDownloadExtDir().equals(file.getName())) {
                continue; // 下载任务单独清理
            }

            boolean needDel = false;
            try {
                Date fileCreateDate = DATE_FORMAT.parse(file.getName());
                needDel =
                    DATE_FORMAT.parse(DATE_FORMAT.format(new Date())).compareTo(fileCreateDate) > 0;
            } catch (ParseException e) {
                log.error("子文件夹命名不合规范, filename = {}", file.getName());
//                needDel = false;
            }
            //删除今天之前的文件夹及其子文件
            if (needDel) {
                deleteFile(file);
            }
        }
        log.info("删除文件结束,总耗时：[{}]毫秒", System.currentTimeMillis() - startTime);
    }

    private void deleteFile(File file) {
        if (file.isDirectory()) {
            //递归删除文件夹下所有文件
            File[] files = file.listFiles();
            for (File f : files) {
                deleteFile(f);
            }

            //删除文件夹自己
            if (file.listFiles().length == 0) {
                log.info("删除文件夹：[{}]", file);
                file.delete();
            }
        } else {
            // 如果是文件,就直接删除自己
            log.info("删除文件：[{}]", file);
            file.delete();
        }
    }

    public boolean existsDownFile(String subDir, String subFileName) {
        String fileName = subFileName + ".pdf";
        String dir = exportFileConfig.getPdfDir() + File.separator + subDir;
        String filePath = dir + File.separator + fileName;

        log.info("判断existsDownFile, filePath:{}", filePath);

        File file1 = new File(filePath);
        return file1.exists();
    }

    public void downFile(String subDir, String subFileName, HttpServletResponse response) {
        if (existsDownFile(subDir, subFileName)) {
            String fileName = subFileName + ".pdf";
            String dir = exportFileConfig.getPdfDir() + File.separator + subDir;
            String filePath = dir + File.separator + fileName;
            log.info("target file: {}", filePath + fileName);
            try {
                response.setContentType("application/pdf");
                response.setHeader("content-Disposition",
                    "attachment;filename=" + URLEncoder.encode(fileName, CHARSET));
                //读取指定路径下面的文件
                InputStream in = new FileInputStream(filePath);
                OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
                //创建存放文件内容的数组
                byte[] buff = new byte[1024];
                //所读取的内容使用n来接收
                int n;
                //当没有读取完时,继续读取,循环
                while ((n = in.read(buff)) != -1) {
                    //将字节数组的数据全部写入到输出流中
                    outputStream.write(buff, 0, n);
                }
                //强制将缓存区的数据进行输出
                outputStream.flush();
                //关流
                outputStream.close();
                in.close();
            } catch (Exception e) {
                log.error("导出订单excel失败");
            }
        } else {
            log.error("excel文件不存在");
        }
    }


    /**
     * 导出巡检详情
     *
     * @param id
     */
    public void recordExport(Long id, ExcelPosition position)
        throws IOException, DocumentException {
        final String HTML = "siteInspectionDetail.html";
        final String HTML_PV = "siteInspectionDetail_pv.html";
        final String HTML_ESS = "siteInspectionDetail_ess.html";

        SiteInspectionRecordPo recordPo = recordRoDs.getById(id);
        SitePo sitePo = siteRoDs.getSite(recordPo.getSiteId());

        Map<Long, SysUserVo> sysUserVoMap = new HashMap<>();
        List<Long> ids = new ArrayList<>();
        if (recordPo.getOpUid() != null) {
            ids.add(recordPo.getOpUid());
        }
        if (recordPo.getQcUid() != null) {
            ids.add(recordPo.getQcUid());
        }
        if (CollectionUtils.isNotEmpty(ids)) {
            ListResponse<SysUserVo> response = authCenterFeignClient.querySysUserByIds(ids);
            FeignResponseValidate.check(response);
            sysUserVoMap = response.getData().stream()
                .collect(Collectors.toMap(SysUserVo::getId, o -> o));
        }

        if (CollectionUtils.isNotEmpty(recordPo.getPhotos())) {
            recordPo.setPhotos(JsonUtils.fromJson(
                JsonUtils.toJsonString(recordPo.getPhotos()),
                new TypeReference<List<FileItem>>() {
                }));
            ossService.refreshFileUrl(recordPo.getPhotos());
        }

        Map<String, Object> data = new HashMap();

        data.put("siteName", sitePo.getSiteName());
        data.put("siteAddress", sitePo.getAddress());
        data.put("siteTypeDesc", SiteGcType.valueOf(sitePo.getGcType()).getDesc());
        data.put("No", recordPo.getNo());
        SysUserVo opUser = sysUserVoMap.get(recordPo.getOpUid());
        data.put("opName", opUser != null ? opUser.getName() : "");
        data.put("reportTime", recordPo.getReportTime());
        SysUserVo qcUser = sysUserVoMap.get(recordPo.getQcUid());
        data.put("qcName", qcUser != null ? qcUser.getName() : "");
        data.put("evseReport", recordPo.getEvseReport());
        data.put("envReport", recordPo.getEnvReport());
        data.put("essReport", recordPo.getEssReport());
        data.put("pvReport", recordPo.getPvReport());
        data.put("photos", recordPo.getPhotos());
        data.put("dcEvseNum", sitePo.getDcEvseNum());
        data.put("acEvseNum", sitePo.getAcEvseNum());
        data.put("remark", recordPo.getRemark());
        data.put("isPerfect", recordPo.getIsPerfect());
        data.put("advice", recordPo.getAdvice());
        data.put("signImage", recordPo.getSignImage());

        if (recordPo.getInspectionType() == SiteInspectionType.CHARGE.getCode()) {
            ListEvseParam param = new ListEvseParam();
            param.setSiteIdList(List.of(sitePo.getId()));
            ListResponse<EvseInfoVo> listResponse = iotDeviceMgmFeignClient.getEvseInfoList(param);
            FeignResponseValidate.checkIgnoreData(listResponse);
            List<EvseInfoVo> evseInfoVoList = listResponse.getData();
            if (CollectionUtils.isNotEmpty(evseInfoVoList)) {
                data.put("evseList", evseInfoVoList);
            }
//            HtmlToPdfFreeMarkerUtil.createPdf(data, position, HTML);
        }
        if (recordPo.getInspectionType() == SiteInspectionType.PV.getCode()) {
            ListGtiParam param = new ListGtiParam();
            param.setSiteId(sitePo.getId());
            param.setTotal(true);
            ListResponse<GtiVo> listResponse = gtiFeignClient.findGtiList(param)
                .block(Duration.ofSeconds(50L));
            FeignResponseValidate.checkIgnoreData(listResponse);
            List<GtiVo> gtiVoList = listResponse.getData();
            if (CollectionUtils.isNotEmpty(gtiVoList)) {
                data.put("gtiVoList", gtiVoList);
                data.put("pvNum", gtiVoList.size());
            } else {
                data.put("pvNum", 0);
            }
//            HtmlToPdfFreeMarkerUtil.createPdf(data, position, HTML_PV);
        }
        if (recordPo.getInspectionType() == SiteInspectionType.ESS.getCode()) {
            ListEssParam param = new ListEssParam();
            param.setSiteIdList(List.of(sitePo.getId()));
            param.setTotal(true);
            ListResponse<EssVo> listResponse = deviceFeignClient.findEssList(param)
                .block(Duration.ofSeconds(50L));
            FeignResponseValidate.checkIgnoreData(listResponse);
            List<EssVo> essVoList = listResponse.getData();
            if (CollectionUtils.isNotEmpty(essVoList)) {
                data.put("essVoList", essVoList);
                data.put("essNum", essVoList.size());
            } else {
                data.put("essNum", 0);
            }
//            HtmlToPdfFreeMarkerUtil.createPdf(data, position, HTML_ESS);
        }

        String templateHtmlFile = HTML;
        if (recordPo.getInspectionType() == YwType.PV.getCode()) {
            templateHtmlFile = HTML_PV;
        }
        if (recordPo.getInspectionType() == YwType.ESS.getCode()) {
            templateHtmlFile = HTML_ESS;
        }
        PdfUtil.builder(exportFileConfig.getPdfDir(), position, templateHtmlFile)
            .genPdf(data)
            .write2File();
    }

    //    @Async
    public void exportYwOrder(String ywOrderNo, ExcelPosition pos) {
        log.info("导出运维工单: ywOrderNo = {}, pos = {}", ywOrderNo, pos.getSubFileName());

//        YwOrderVo vo = ywOrderRoDs.getByYwOrderNo(ywOrderNo);
//        if (null == vo) {
//            throw new DcArgumentException("运维工单编号无效");
//        }
//
//        // 桩名称列表
//        List<EvseVo> evseList = redisIotReadService.getEvseList(vo.getEvseNoList());
//        vo.setEvseNameList(evseList.stream()
//                .map(evse -> StringUtils.isBlank(evse.getName()) ? evse.getEvseNo() : evse.getName() + " " + evse.getEvseNo())
//                .collect(Collectors.toList()));
//
//        // 创建者联系电话
//        ListResponse<SysUserVo> res = authCenterFeignClient.querySysUserByIds(List.of(vo.getCreateOpUid()));
//        FeignResponseValidate.check(res);
//        if (CollectionUtils.isNotEmpty(res.getData())) {
//            vo.setCreateUserPhone(res.getData().get(0).getPhone());
//        }
//
//        PdfUtil.builder(TEMP_DIR, pos, "yw_order_detail.html")
//                .genHtml(vo)
//                .genPdf()
//                .write2File();

        Mono.just(ywOrderNo)
            .map(ywOrderRoDs::getByYwOrderNo)
            .doOnNext(vo -> {
                // 桩名称列表
                if (CollectionUtils.isNotEmpty(vo.getEvseNoList())) {
                    List<EvseVo> evseList = redisIotReadService.getEvseList(vo.getEvseNoList());
                    vo.setEvseNameList(evseList.stream()
                        .map(evse -> StringUtils.isBlank(evse.getName()) ?
                            evse.getEvseNo() : evse.getName() + " " + evse.getEvseNo())
                        .collect(Collectors.toList()));
                }

                // 创建者联系电话
                ListResponse<SysUserVo> res = authCenterFeignClient
                    .querySysUserByIds(List.of(vo.getCreateOpUid()));
                FeignResponseValidate.check(res);
                if (CollectionUtils.isNotEmpty(res.getData())) {
                    vo.setCreateUserPhone(res.getData().get(0).getPhone());
                }
            })
            .flatMap(vo -> {
                // 获取创建人的手机号(联系方式)
                Mono<YwOrderVo> mono = Mono.just(vo);
                if (UserType.CUSTOMER.equals(vo.getCreateOpType())) {
                    mono = mono.flatMap(
                            v -> userFeignClient.queryUserByUidAndCommId(vo.getCreateOpUid(), null))
                        .doOnNext(res -> FeignResponseValidate.check(res))
                        .doOnNext(res -> vo.setCreateUserPhone(res.getData().getPhone()))
                        .map(res -> vo);
                } else {
                    mono = mono.doOnNext(v -> {
                        ListResponse<SysUserVo> res = authCenterFeignClient
                            .querySysUserByIds(List.of(vo.getCreateOpUid()));
                        FeignResponseValidate.check(res);
                        if (CollectionUtils.isNotEmpty(res.getData())) {
                            vo.setCreateUserPhone(res.getData().get(0).getPhone());
                        }
                    });
                }

                return mono;
            })
            .doOnNext(vo -> {
                if (vo.getYwType() == YwType.PV.getCode()) {
                    ListGtiParam param = new ListGtiParam();
                    param.setSiteId(vo.getSiteId());
                    param.setTotal(true);
                    ListResponse<GtiVo> listResponse = gtiFeignClient.findGtiList(param)
                        .block(Duration.ofSeconds(30L));
                    FeignResponseValidate.checkIgnoreData(listResponse);
                    List<GtiVo> gtiVoList = listResponse.getData();
                    if (CollectionUtils.isNotEmpty(gtiVoList)) {
                        List<String> vendorList = new ArrayList<>();
                        gtiVoList.forEach(e -> {
                            if (!vendorList.contains(e.getVendor().getDesc())) {
                                e.getVendor().getDesc();
                            }
                        });
                        if (CollectionUtils.isNotEmpty(vendorList)) {
                            AtomicReference<String> pvGwVendorName = new AtomicReference<>("");
                            vendorList.forEach(e -> {
                                pvGwVendorName.set(pvGwVendorName + e + ",");
                            });
                            vo.setPvGwVendorName(pvGwVendorName.get()
                                .substring(0, pvGwVendorName.get().length() - 1));
                        } else {
                            vo.setPvGwVendorName("--");
                        }
                    } else {
                        vo.setPvGwVendorName("--");
                    }
                }
                if (vo.getYwType() == YwType.ESS.getCode()) {
                    ListEssParam param = new ListEssParam();
                    param.setSiteIdList(List.of(vo.getSiteId()));
                    param.setTotal(true);
                    ListResponse<EssVo> listResponse = deviceFeignClient.findEssList(param)
                        .block(Duration.ofSeconds(30L));
                    FeignResponseValidate.checkIgnoreData(listResponse);
                    List<EssVo> essVoList = listResponse.getData();
                    if (CollectionUtils.isNotEmpty(essVoList)) {
                        vo.setEssNum(essVoList.size());
                    } else {
                        vo.setEssNum(0);
                    }
                }
                if (vo.getYwType() == YwType.CHARGE.getCode() && CollectionUtils
                    .isNotEmpty(vo.getGoods())) {
                    String s = JsonUtils.toJsonString(vo.getGoods());
                    List<Goods> goods = JsonUtils.toJsonList(s);    // JSON.parseArray(s, Goods.class);
                    // 兼容老版本更换器件,不存在操作类型
                    List<Goods> collect1 = goods.stream().filter(e -> e.getOpType() == null)
                        .collect(Collectors.toList());
                    StringBuilder sb = new StringBuilder();
                    if (CollectionUtils.isNotEmpty(collect1)) {
                        goods.forEach(e -> {
                            if (StringUtils.isNotEmpty(e.getName())) {
                                sb.append(e.getName()).append(e.getNum()).append("个")
                                    .append("<br/>");
                            }
                        });
                        // 老版本电源模块，不存在器件名称
                        List<Goods> collect = goods.stream()
                            .filter(e -> StringUtils.isEmpty(e.getName()))
                            .collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(collect) && collect.size() > 0) {
                            sb.append("高压直流充电模块").append(collect.size()).append("个")
                                .append("<br/>");
                        }
                        vo.setGoodsStr(sb.toString());
                    } else { // 新版存在操作类型
                        vo.setGoodsStr(this.formatOp(goods));
                    }
                } else {
                    vo.setGoodsStr("--");
                }

                // 获取图片地址
                if (CollectionUtils.isNotEmpty(vo.getImages())) {
                    vo.setImages(JsonUtils.fromJson(
                        JsonUtils.toJsonString(vo.getImages()),
                        new TypeReference<List<FileItem>>() {
                        }));
                    if (vo.getImages().get(0).getUrl() == null || StringUtils.isBlank(
                        vo.getImages().get(0).getUrl())) {
                        ossService.refreshFileUrl(vo.getImages());
                    }
                }

                // 获取故障图片地址
                if (CollectionUtils.isNotEmpty(vo.getFaultImages())) {
                    List<String> faultImages = new ArrayList<>();
                    List<FileItem> fileItemList = new ArrayList<>();
                    if (!vo.getFaultImages().get(0).startsWith("http://") && !vo.getFaultImages()
                        .get(0)
                        .startsWith("https://")) {
                        for (String faultImage : vo.getFaultImages()) {
                            FileItem fileItem = JsonUtils.fromJson(faultImage, FileItem.class);
                            fileItemList.add(fileItem);
                        }
                        ossService.refreshFileUrl(fileItemList);
                        fileItemList.forEach(fileItem -> {
                            faultImages.add(fileItem.getUrl());
                        });
                        vo.setFaultImages(faultImages);
                    }
                }
            })

            .doOnNext(data -> {
                String templateHtmlFile = "yw_order_detail.html";
                if (data.getYwType() == YwType.PV.getCode()) {
                    templateHtmlFile = "yw_order_detail_pv.html";
                }
                if (data.getYwType() == YwType.ESS.getCode()) {
                    templateHtmlFile = "yw_order_detail_ess.html";
                }
                PdfUtil.builder(exportFileConfig.getPdfDir(), pos, templateHtmlFile)
                    .genPdf(data)
                    .write2File();
            })
            .block(Duration.ofSeconds(50L));
//                .subscribe(res -> log.info("结束pdf生成: {}", pos.getSubFileName()));
    }

    private String formatOp(List<Goods> goods) {
        StringBuilder sb = new StringBuilder();
        Map<String, List<Goods>> map = goods.stream()
            .collect(Collectors.groupingBy(Goods::getEvseName));
        map.forEach((k, v) -> {
            sb.append(String.format("%s", k)).append("<br/>");
            v.forEach(e -> {
                sb.append(String.format("%s器件", e.getOpType().getDesc())).append("<br/>");
                if (StringUtils.isNotEmpty(e.getNewTypeCode())) {
                    sb.append(
                            String.format("新器件: %s;%s", e.getNewTypeCode(), e.getNewFullModel()))
                        .append("<br/>");
                }
                if (StringUtils.isNotEmpty(e.getOldTypeCode())) {
                    sb.append(
                            String.format("旧器件: %s;%s", e.getOldTypeCode(), e.getOldFullModel()))
                        .append("<br/>");
                }
                if (StringUtils.isNotEmpty(e.getOldDeviceNo())) {
                    sb.append(String.format("旧模块编号:%s", e.getOldDeviceNo())).append("<br/>");
                }
                if (StringUtils.isNotEmpty(e.getNewDeviceNo())) {
                    sb.append(String.format("新模块编号:%s", e.getNewDeviceNo())).append("<br/>");
                }
                if (e.getIdx() != null) {
                    sb.append(String.format("槽位号:%d", e.getIdx())).append("<br/>");
                }
            });
            sb.append("<br/>");
        });
        return sb.toString();
    }

    private static String getThinUrl(String url) {
        if (url == null || !url.contains("?")) {
            return url;
        } else {
            String[] strs = url.split("\\?");
            if (strs.length > 0) {
                return strs[0];
            } else {
                return null;
            }
        }
    }

    public void exportPrerunDetail(Long prerunId, ExcelPosition pos) {
        log.info("导出调试工单: prerunId = {}, pos = {}", prerunId, pos.getSubFileName());

//        YwOrderVo vo = ywOrderRoDs.getByYwOrderNo(ywOrderNo);
//        if (null == vo) {
//            throw new DcArgumentException("运维工单编号无效");
//        }
//
//        // 桩名称列表
//        List<EvseVo> evseList = redisIotReadService.getEvseList(vo.getEvseNoList());
//        vo.setEvseNameList(evseList.stream()
//                .map(evse -> StringUtils.isBlank(evse.getName()) ? evse.getEvseNo() : evse.getName() + " " + evse.getEvseNo())
//                .collect(Collectors.toList()));
//
//        // 创建者联系电话
//        ListResponse<SysUserVo> res = authCenterFeignClient.querySysUserByIds(List.of(vo.getCreateOpUid()));
//        FeignResponseValidate.check(res);
//        if (CollectionUtils.isNotEmpty(res.getData())) {
//            vo.setCreateUserPhone(res.getData().get(0).getPhone());
//        }
//
//        PdfUtil.builder(TEMP_DIR, pos, "yw_order_detail.html")
//                .genHtml(vo)
//                .genPdf()
//                .write2File();

        Mono.just(new PrerunMixDto())
            .doOnNext(e -> e.setPrerun(prerunRoDs.getById(prerunId)))
            .doOnNext(e -> {
//                Optional.ofNullable(e.getPrerun()).ifPresent(l -> {
//                    if(l.getSiteImages() != null) {
//                        l.getSiteImages().forEach(one -> {
//                            one.setUrl(getThinUrl(one.getUrl()));
//                        });
//                    }
//                });
            })
            .doOnNext(e -> e.setPrerunAssetImgPoList(prerunAssetImgRoDs.getByPrerunId(prerunId)))
            .doOnNext(e -> {
                Optional.ofNullable(e.getPrerunAssetImgPoList()).ifPresent(l -> {
                    l.stream().forEach(one -> {
                        one.setUrl(getThinUrl(one.getUrl()));
                    });
                });
            })
            .doOnNext(e -> e.setPrerunEvsePoList(prerunEvseRoDs.getByPrerunId(prerunId)))
            .doOnNext(e -> e.setPrerunCheckPo(prerunCheckRoDs.getLatestByPrerunId(prerunId)))
            .doOnNext(data -> {
                String templateHtmlFile = "prerun_detail.html";
                PdfUtil.builder(exportFileConfig.getPdfDir(), pos, templateHtmlFile)
                    .genPdf(data)
                    .write2File();
            })
            .block(Duration.ofSeconds(50L));
//                .subscribe(res -> log.info("结束pdf生成: {}", pos.getSubFileName()));
    }



//    public String formatData(BigDecimal num, int position) {
//        if (num == null) {
//            num = BigDecimal.ZERO;
//        }
//        //格式化金额、电量、订单数
//        DecimalFormat decimalFormat = new DecimalFormat(",###.00");
//        DecimalFormat elecFormat = new DecimalFormat(",###.0000");
//        DecimalFormat orderFormat = new DecimalFormat(",###");
//
//        DecimalFormat df = new DecimalFormat("0.00#");
//        DecimalFormat df1 = new DecimalFormat("0.0000#");
//
//        if (DecimalUtils.gte(num, BigDecimal.ONE) || DecimalUtils.lte(num,
//            BigDecimal.valueOf(-1))) { //大于等于1
//            if (position == 2) {
//                return decimalFormat.format(num);
//            } else if (position == 4) {
//                return elecFormat.format(num);
//            } else {
//                return orderFormat.format(num);
//            }
//        } else {
//            if (position == 2) {
//                return df.format(num);
//            } else if (position == 4) {
//                return df1.format(num);
//            } else {
//                return num.toString();
//            }
//
//        }
//    }
//
//    public Map<String, Object> objectToMap(Object obj) {
//        Map<String, Object> map = new HashMap<>();
//        if (obj == null) {
//            return map;
//        }
//        Class clazz = obj.getClass();
//        Field[] fields = clazz.getDeclaredFields();
//        try {
//            for (Field field : fields) {
//                field.setAccessible(true);
//                map.put(field.getName(), field.get(obj));
//            }
//        } catch (Exception e) {
//            log.info("message={}", e.getMessage());
//        }
//        return map;
//    }
//
//    // 充值申请 信息
//    public Mono<ObjectResponse<BalanceApplicationVo>> getBalanceApplyById(Long id) {
//        return userFeignClient.getBalanceApplyById(id)
//            .doOnNext(FeignResponseValidate::check)
//            .flatMap(x -> {
//                if (null != x.getData().getApplierId()) {
//                    return authSysUserFeignClient.getSysUserByIdList(
//                            List.of(x.getData().getApplierId()))
//                        .doOnNext(FeignResponseValidate::check)
//                        .map(sysUser -> {
//                            x.getData().setApplierName(sysUser.getData().get(0).getName());
//                            return x;
//                        });
//                }
//                return Mono.just(x);
//            })
//            .flatMap(x -> {
//                if (null != x.getData().getRefundOrderId()) {
//                    return deviceDataCoreClient.tkView(x.getData().getRefundOrderId())
//                        .doOnNext(FeignResponseValidate::check)
//                        .map(payBillVoObjectResponse -> {
//                            x.getData().setPayTime(payBillVoObjectResponse.getData().getPayTime())
//                                .setPayChannel(payBillVoObjectResponse.getData().getPayChannel());
//                            return x;
//                        });
//                }
//                return Mono.just(x);
//            });
//    }


    public void exportCorpInvoiceDetail(String applyNo, ExcelPosition pos) {
        log.info("导出企业开票详情: ywOrderNo = {}, pos = {}", applyNo, pos.getSubFileName());

        Mono.just(dataCoreFeignClient.corpInvoiceRecordDetail(applyNo))
            .doOnNext(FeignResponseValidate::check)
            .map(res -> res.getData())
            .doOnNext(p -> {
                CorpInvoiceInfoParam param = new CorpInvoiceInfoParam();
                param.setUid(p.getUid())
                    .setTempSalId(p.getTempSalId())
                    .setProductTempId(p.getProductTempId());
                ObjectResponse<CorpInvoiceInfoVo> tempSal = invoiceFeignClient
                    .getCorpInvoiceDetail(param);
                FeignResponseValidate.check(tempSal);

                this.initCorpInvoiceInfo(p, tempSal.getData());

                // 图片格式
                if (CollectionUtils.isNotEmpty(p.getImages())) {
                    p.setImages(
                        p.getImages().stream().map(e -> {
                            if (e.contains("?")) {
                                return e.substring(0, e.indexOf("?"));
                            }
                            return e;
                        }).collect(
                            Collectors.toList()));
                }
                if (CollectionUtils.isNotEmpty(p.getUrlList())) {
                    p.setUrlList(
                        p.getUrlList().stream().map(e -> {
                            if (e.contains("?")) {
                                return e.substring(0, e.indexOf("?"));
                            }
                            return e;
                        }).collect(
                            Collectors.toList()));
                }
                // 获取审核人姓名
                if (StringUtils.isNotBlank(p.getAuditName())) {
                    ObjectResponse<SysUserPo> sysUserRes = authCenterFeignClient
                        .getByUserNameAndPlatform(
                            p.getAuditName(), AppClientType.MGM_WEB);
                    FeignResponseValidate.checkIgnoreData(sysUserRes);

                    SysUserPo sysUser = sysUserRes.getData();
                    if (null != sysUser) {
                        p.setAuditFullName(sysUser.getName());
                    }
                }
            })
            .flatMap(p -> { // 订单分布
                List<ChargerOrderSite> siteOrderData = new ArrayList<>();
                if (InvoicingMode.POST_CHARGER.equals(p.getInvoiceWay())) {
                    ListChargeOrderParam params = new ListChargeOrderParam();
                    params.setApplyNo(p.getApplyNo())
                        .setCorpId(p.getCorpId())
                        .setInCorpInvoice(Boolean.TRUE)
                        .setStatusList(List.of(OrderStatus.ORDER_STATUS_RECEIVE_MONEY)); // 已结算的充电订单
                    ObjectResponse<CorpPo> corpRes = authCenterFeignClient.getCorp(p.getCorpId());
                    FeignResponseValidate.check(corpRes);
                    if (CorpType.HLHT == corpRes.getData().getType()
                        && params.getSettlementType() == null) {
                        params.setSettlementType(SettlementType.PARTNER);
                    }

                    ListResponse<ChargerOrderSite> chargerOrderSiteListResponse = dataCoreFeignClient
                        .chargerOrderGroupBySite(params);
                    FeignResponseValidate.check(chargerOrderSiteListResponse);
                    siteOrderData = chargerOrderSiteListResponse.getData();
                } else {
                    ListInvoiceRecordOrderRefParam refParam = new ListInvoiceRecordOrderRefParam();
                    refParam.setApplyNo(p.getApplyNo());
                    ListResponse<ChargerOrderSite> chargerOrderSiteListResponse = dataCoreFeignClient
                        .recordOrderGroupBySite(refParam);
                    FeignResponseValidate.check(chargerOrderSiteListResponse);
                    siteOrderData = chargerOrderSiteListResponse.getData();
                }
                CorpInvoiceRecordExport exportData = new CorpInvoiceRecordExport();
                BeanUtils.copyProperties(p, exportData);
                exportData.setSiteOrderData(siteOrderData);
                return Mono.just(exportData);
            })
            .doOnNext(p -> {  // 订单原价
                List<OrderTimeShareBiVo> orderTimeShareBi = new ArrayList<>();
                if (InvoicingMode.POST_CHARGER.equals(p.getInvoiceWay())) {
                    ListChargeOrderParam params = new ListChargeOrderParam();
                    params.setApplyNo(p.getApplyNo())
                        .setCorpId(p.getCorpId())
                        .setInCorpInvoice(Boolean.TRUE)
                        .setStatusList(List.of(OrderStatus.ORDER_STATUS_RECEIVE_MONEY)); // 已结算的充电订单

                    ObjectResponse<CorpPo> corpRes = this.authCenterFeignClient
                        .getCorp(p.getCorpId());
                    FeignResponseValidate.check(corpRes);
                    if (CorpType.HLHT == corpRes.getData().getType()
                        && params.getSettlementType() == null) {
                        params.setSettlementType(SettlementType.PARTNER);
                    }

                    ListResponse<OrderTimeShareBiVo> orderTimeShareBiVoListResponse = dataCoreFeignClient
                        .chargerOrderGroupByTimeShareFee(params);
                    FeignResponseValidate.check(orderTimeShareBiVoListResponse);
                    orderTimeShareBi = orderTimeShareBiVoListResponse.getData();
                } else if (InvoicingMode.POST_SETTLEMENT.equals(p.getInvoiceWay())) {

                    ListInvoiceRecordOrderRefParam refParam = new ListInvoiceRecordOrderRefParam();
                    refParam.setApplyNo(p.getApplyNo());
                    ListResponse<OrderTimeShareBiVo> orderTimeShareBiVoListResponse = dataCoreFeignClient
                        .recordOrderGroupByTimeShareFee(refParam);
                    FeignResponseValidate.check(orderTimeShareBiVoListResponse);
                    orderTimeShareBi = orderTimeShareBiVoListResponse.getData();
                }
                p.setOrderTimeShareBi(orderTimeShareBi);
            })
            .doOnNext(p -> {
                OrderBiVo orderBiData = new OrderBiVo();
                if (InvoicingMode.PRE_PAY.equals(p.getInvoiceWay())) { // 预充值开票
                    PayBillParam billParam = new PayBillParam();
                    billParam.setApplyNo(p.getApplyNo());
                    billParam.setInCorpInvoice(Boolean.TRUE);
                    billParam.setFlowTypeList(List.of(DepositFlowType.IN_FLOW));
                    billParam.setAccountTypeList(List.of(PayAccountType.CORP)); // 查询企业客户的充值
                    billParam.setTaxStatus(List.of(TaxStatus.NO, TaxStatus.YES)); // 未开票
                    ObjectResponse<OrderBiVo> orderBiVoObjectResponse = dataCoreFeignClient
                        .invoiceOrderBiForCorp(billParam);
                    FeignResponseValidate.check(orderBiVoObjectResponse);
                    orderBiData = orderBiVoObjectResponse.getData();

                } else if (InvoicingMode.POST_CHARGER.equals(p.getInvoiceWay())) { // 充电后开票
                    ListChargeOrderParam params = new ListChargeOrderParam();
                    params.setApplyNo(p.getApplyNo());
                    ObjectResponse<OrderBiVo> block = dataCoreOrderFeignClient
                        .includeChargerOrderBi(params)
                        .block(Duration.ofSeconds(50L));
                    FeignResponseValidate.check(block);
                    orderBiData = block.getData();

                } else if (InvoicingMode.POST_SETTLEMENT.equals(p.getInvoiceWay())) { // 后付费账单开票
                    ListSettlementParam param = new ListSettlementParam();
                    param.setInCorpInvoice(Boolean.TRUE);
                    param.setStatusList(List.of(SettlementStatusEnum.PAID));

                    ListResponse<InvoiceRecordOrderRefDto> res = null;
                    if (StringUtils.isNotBlank(p.getApplyNo())) {
                        param.setApplyNo(p.getApplyNo());
                        // -> dataCore: 获取开票关联的订单列表
                        ListInvoiceRecordOrderRefParam refParam = new ListInvoiceRecordOrderRefParam();
                        refParam.setApplyNo(p.getApplyNo())
                            .setTotal(param.getTotal())
                            .setSize(param.getSize())
                            .setStart(param.getStart())
                            .setSk(param.getSk());
                        res = dataCoreFeignClient.getInvoiceRecordOrderList(refParam);
                        FeignResponseValidate.check(res);

                        // 开票关联订单为空，则返回空列表
                        if (CollectionUtils.isNotEmpty(res.getData())) {
                            param.setBillNoList(res.getData().stream()
                                .map(InvoiceRecordOrderRefDto::getOrderNo)
                                .collect(Collectors.toList()));
                            ObjectResponse<OrderBiVo> block = userFeignClient
                                .settlementBiForCorp(param)
                                .block(Duration.ofSeconds(50L));
                            FeignResponseValidate.check(block);
                            orderBiData = block.getData();
                        }

                    }
                }
                p.setOrderBiData(orderBiData);
            })
            .doOnNext(p -> {
                if (InvoicingMode.POST_SETTLEMENT.equals(p.getInvoiceWay())) { // 后付费账单
                    ListSettlementParam param = new ListSettlementParam();
                    param.setCorpId(p.getCorpId())
                        .setApplyNo(p.getApplyNo())
                        .setInCorpInvoice(Boolean.TRUE)
                        .setStatusList(List.of(SettlementStatusEnum.PAID));

                    ListResponse<InvoiceRecordOrderRefDto> res = null;
                    ListInvoiceRecordOrderRefParam refParam = new ListInvoiceRecordOrderRefParam();
                    refParam.setApplyNo(param.getApplyNo())
                        .setTotal(param.getTotal())
                        .setSize(param.getSize())
                        .setStart(param.getStart())
                        .setSk(param.getSk());
                    res = dataCoreFeignClient.getInvoiceRecordOrderList(refParam);
                    FeignResponseValidate.check(res);

                    if (CollectionUtils.isNotEmpty(res.getData())) {
                        // 账单列表
                        param.setBillNoList(res.getData().stream()
                            .map(InvoiceRecordOrderRefDto::getOrderNo)
                            .collect(Collectors.toList()));
                        ObjectResponse<OrderBiVo> block = userFeignClient
                            .settlementBiForCorp(param)
                            .block(Duration.ofSeconds(50L));
                        FeignResponseValidate.check(block);
                        p.setSettleBiData(block.getData());
                    }
                }
            })
            .doOnNext(data -> {
                String templateHtmlFile = "corp_invoice_detail.html";
                log.info("企业开票导出内容,data={}", JsonUtils.toJsonString(data));
                PdfUtil.builder(exportFileConfig.getPdfDir(), pos, templateHtmlFile)
                    .genPdf(data)
                    .write2File();
            })
            .block(Duration.ofSeconds(50L));
    }

    private void initCorpInvoiceInfo(CorpInvoiceRecordDetail detail,
        CorpInvoiceInfoVo corpInvoiceInfo) {
        detail.setTempRefVo(corpInvoiceInfo.getTempRefVo())
            .setName(corpInvoiceInfo.getName())
            .setEmail(corpInvoiceInfo.getEmail())
            .setTin(corpInvoiceInfo.getTin())
            .setAddress(corpInvoiceInfo.getAddress())
            .setTel(corpInvoiceInfo.getTel())
            .setBank(corpInvoiceInfo.getBank())
            .setBankAccount(corpInvoiceInfo.getBankAccount())
            .setReceiverName(corpInvoiceInfo.getReceiverName())
            .setReceiverMobilePhone(corpInvoiceInfo.getReceiverMobilePhone())
            .setReceiverProvince(corpInvoiceInfo.getReceiverProvince())
            .setReceiverCity(corpInvoiceInfo.getReceiverCity())
            .setReceiverArea(corpInvoiceInfo.getReceiverArea())
            .setReceiverAddress(corpInvoiceInfo.getReceiverAddress())
            .setUid(corpInvoiceInfo.getUid())
            .setChannel(corpInvoiceInfo.getChannel());

        // 开票主体信息
        detail.setSaleName(corpInvoiceInfo.getSaleName());
        detail.setProductTempName(corpInvoiceInfo.getTempRefVo().getName());

        // 审核状态
        List<InvoicedStatus> statusList = List
            .of(InvoicedStatus.SUBMITTED, InvoicedStatus.NOT_SUBMITTED);
        if (detail.getAuditTime() != null) {
            if (detail.getStatus() == InvoicedStatus.AUDIT_FAILED) {
                detail.setAuditResult(false);
            } else if (statusList.contains(detail.getStatus())) {
                detail.setAuditResult(null);
            } else {
                detail.setAuditResult(true);
            }
        }

        // 商品行开票金额信息
        detail.getTempRefVo().getDetailVoList().forEach(vo -> {
            if (vo.getProductType() == ProductType.SERV_ACTUAL_FEE) {
                vo.setAmount(detail.getActualServFee())
                    .setFixAmount(detail.getFixServFee());
            } else if (vo.getProductType() == ProductType.ELEC_ACTUAL_FEE) {
                vo.setAmount(detail.getActualElecFee())
                    .setFixAmount(detail.getFixElecFee());
            } else if (vo.getProductType() == ProductType.TECH_SERV_FEE) {
                vo.setAmount(detail.getActualTechServFee())
                    .setFixAmount(detail.getFixTechServFee());
            } else {
                vo.setAmount(detail.getTotalFee())
                    .setFixAmount(detail.getFixTotalFee());
            }
        });
    }

    public void exportRoverDetail(long roverId, ExcelPosition pos) {
        log.info("导出运营巡检: roverId = {}, pos = {}", roverId, pos.getSubFileName());
        Mono.just(roverId)
            .map(e -> {
                    final SiteRoverVo byId = siteRoverRoDs.getById(roverId);
                    IotAssert.isNotNull(byId, "巡查信息不存在");
                    final List<SiteRoverAssetPo> byRoverId = siteRoverAssetRoDs.getByRoverId(roverId);
                    Map<RoverAssertType, SiteRoverAssetPo> assetMap = new HashMap<>();
                    if (CollectionUtils.isNotEmpty(byRoverId)) {
                        assetMap = byRoverId.stream()
                            .collect(Collectors.toMap(SiteRoverAssetPo::getType, o -> o, (o, n) -> n));
                    }
                    Map<RoverAssertType, SiteRoverAssetPo> finalAssetMap = assetMap;
                    List<SiteRoverAssetPo> AllTypes =
                        List.of(RoverAssertType.EVSE,
                                RoverAssertType.TRANS,
                                RoverAssertType.RAIN,
                                RoverAssertType.CAMERA,
                                RoverAssertType.LIGHT,
                                RoverAssertType.FIRE,
                                RoverAssertType.FLOOR,
                                RoverAssertType.PERCEPTION,
                                RoverAssertType.WASHROOM,
                                RoverAssertType.RESTROOM)
                            .stream()
                            .map(ie -> getForDisplay(finalAssetMap.get(ie), ie))
                            .collect(Collectors.toList());

                    RoverPdfExportVo pdfPrintItem = new RoverPdfExportVo();

                    // 刷新图片
                    if (CollectionUtils.isNotEmpty(AllTypes)) {
                        AllTypes.stream().forEach(i -> {
                            if (CollectionUtils.isNotEmpty(i.getImages())) {
                                i.setImages(JsonUtils.fromJson(
                                    JsonUtils.toJsonString(i.getImages()),
                                    new TypeReference<List<FileItem>>() {
                                    }));
                                ossService.refreshFileUrl(i.getImages());
                            }
                        });
                    }
                    pdfPrintItem.setPo1(AllTypes.get(0));
                    pdfPrintItem.setPos1(AllTypes.subList(1, 6));
                    pdfPrintItem.setPo2(AllTypes.get(6));
                    pdfPrintItem.setPos2(AllTypes.subList(7, 10));
                    pdfPrintItem.setVo(byId);
                    return pdfPrintItem;

                }
            )
            .doOnNext(e -> {
                String templateHtmlFile = "rover_detail.html";
                PdfUtil.builder(exportFileConfig.getPdfDir(), pos, templateHtmlFile)
                    .genPdf(e)
                    .write2File();
            }).block(Duration.ofSeconds(50L));
    }

    private SiteRoverAssetPo getForDisplay(SiteRoverAssetPo po, RoverAssertType type) {
        if (po == null) {
            SiteRoverAssetPo ret = new SiteRoverAssetPo();
            return ret.setFault(0).setType(type);
        }
        if (po.getFault() == null) {
            po.setFault(0);
        }
        return po;
    }
}
