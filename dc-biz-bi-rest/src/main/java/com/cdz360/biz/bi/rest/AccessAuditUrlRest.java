package com.cdz360.biz.bi.rest;

import com.chargerlinkcar.framework.common.rest.BaseController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 访问审计url Rest
 *
 * <AUTHOR>
 * @version  2018-03-06
 */
@RestController
@RequestMapping(value = "/api/accessAuditUrl")
@Slf4j
public class AccessAuditUrlRest extends BaseController {

//    @Autowired
//    private IAccessAuditUrlService accessAuditUrlService;
//
//    /**
//     * 根据id查询 访问审计url
//     *
//     * @param id
//     * @return
//     */
//    @GetMapping("/get")
//    public ObjectResponse<AccessAuditUrl> get(@RequestParam("id") Long id) {
//        log.error("deprecated api");
//        log.info("根据id查询 访问审计url。 id: {}", id);
//
//        ObjectResponse<AccessAuditUrl> resultEntity = accessAuditUrlService.selectById(id);
//
//
//        log.info("根据id查询 访问审计url。 id: {}, data: {}", id, resultEntity.getData());
//
//        return resultEntity;
//    }
//
//    /**
//     * 同步 访问审计url 所有数据到redis
//     *
//     * @return
//     */
//    @RequestMapping("/synchronizeAllUrlToRedis")
//    public ListResponse<AccessAuditUrl> synchronizeToRedis(Integer status) {
//        log.error("deprecated api");
//        log.info("同步 访问审计url 所有数据到redis。 id: {}", status);
//
//        ListResponse<AccessAuditUrl> resultEntity = accessAuditUrlService.synchronizeAllUrlToRedis(status);
//
//        log.info("同步 访问审计url 所有数据到redis。 id: {}, data: {}", status, resultEntity.getData());
//
//        return resultEntity;
//    }
//
//    /**
//     * 通过status分页查询 访问审计url列表
//     *
//     * @param status
//     * @return
//     */
//    @RequestMapping("/queryPage")
//    public ListResponse<AccessAuditUrl> queryPage(HttpServletRequest request, Integer status) {
//        log.error("deprecated api");
//
//        log.info("通过status分页查询 访问审计url列表。 status: {}", status);
//
//        Page<AccessAuditUrl> page = getPage(request);
//
//        ListResponse<AccessAuditUrl> resultEntity = accessAuditUrlService.queryPageAccessAuditUrls(page, status);
//
//        log.info("通过status分页查询 访问审计url列表。 status: {}, data: {}", status, JsonUtils.toJsonString(resultEntity.getData()));
//
//        return resultEntity;
//    }
//
//    /**
//     * 添加 访问审计url
//     *
//     * @param accessAuditUrl
//     * @return
//     */
//    @PostMapping("/add")
//    public ObjectResponse add(HttpServletRequest request,@RequestBody AccessAuditUrl accessAuditUrl) {
//        log.error("deprecated api");
//        CommercialSample commercialSample = this.getCommercialSample(request);
//        Long id = commercialSample.getId();
//        accessAuditUrl.setCreateBy(String.valueOf(id));
//        accessAuditUrl.setUpdateBy(String.valueOf(id));
//        ObjectResponse resultEntity = accessAuditUrlService.add(accessAuditUrl);
//        return resultEntity;
//    }
//
//    /**
//     * 更新 访问审计url
//     *
//     * @param accessAuditUrl
//     * @return
//     */
//    @PostMapping("/update")
//    public ObjectResponse update(HttpServletRequest request,@RequestBody AccessAuditUrl accessAuditUrl) {
//        log.error("deprecated api");
//        CommercialSample commercialSample = this.getCommercialSample(request);
//        Long id = commercialSample.getId();
//        accessAuditUrl.setUpdateBy(String.valueOf(id));
//        ObjectResponse resultEntity = accessAuditUrlService.update(accessAuditUrl);
//        return resultEntity;
//    }
//
//    /**
//     * 删除 访问审计url
//     *
//     * @param id
//     * @return
//     */
//    @PostMapping("/delete")
//    public ObjectResponse delete(@RequestParam("id") Long id) {
//        log.error("deprecated api");
//        ObjectResponse resultEntity = accessAuditUrlService.deleteById(id);
//
//        return resultEntity;
//    }
}