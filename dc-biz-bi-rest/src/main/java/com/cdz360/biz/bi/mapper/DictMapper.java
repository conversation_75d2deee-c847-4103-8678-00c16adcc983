/**
 * * Copyright &copy; 2015-2020 <a href="https://gitee.com/JeeHuangBingGui/jeeSpringCloud">JeeSpringCloud</a> All rights reserved..
 */
package com.cdz360.biz.bi.mapper;

import com.chargerlinkcar.framework.common.domain.Dict;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 字典管理DAO接口
 *
 * <AUTHOR>
 * @version  2018-10-13
 */
@Mapper
public interface DictMapper {

//    Dict get(Long id);
//
//    List<Dict> findList(Dict dict);
//
//    int insert(Dict dict);
//
//    int update(Dict dict);
//
//    void delete(Dict dict);
//
//    PictureConfig getPictureConfig();

    //
    List<Dict> findDictDataByType(String type);

}