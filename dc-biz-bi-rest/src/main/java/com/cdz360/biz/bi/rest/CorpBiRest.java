package com.cdz360.biz.bi.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.biz.bi.service.CommOrderBiService;
import com.cdz360.biz.bi.service.ExcelBiService;
import com.cdz360.biz.model.bi.site.ExcelBiPosition;
import com.cdz360.biz.model.iot.param.SiteBiParam;
import com.cdz360.biz.model.trading.site.po.BiSiteSummaryPo;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/bi/corp")
@Tag(name = "商户相关的统计接口", description = "按照商户统计订单")
public class CorpBiRest {

    @Autowired
    private CommOrderBiService commOrderBiService;

    @Autowired
    private ExcelBiService excelBiService;



    @Operation( summary = "按照企业汇总订单统计")
    @PostMapping("/getBiCorpList")
    public ListResponse<BiSiteSummaryPo> getBiCorpList(@RequestBody SiteBiParam param) {
        log.info("param = {}", param);
        return this.commOrderBiService.getBiCorpList(param);
    }

    @Operation( summary = "按照当前结果导出查询数据--按照企业汇总")
    @PostMapping("/exportBiCorpList")
    public ObjectResponse<ExcelBiPosition> exportBiCorpList(@RequestBody SiteBiParam param) {
        log.info("param = {}", param);
        throw new DcServiceException("接口已废弃，请联系开发");
//        ExcelBiPosition position = new ExcelBiPosition();
//        position.setSubFileName(UUIDUtils.getUuid32());
//        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
//        log.info("writeTempExcelByChargeOrderList 启动,position:{}", JsonUtils.toJsonString(position));
//        param.setExcelPosition(position);
//        param.setSheetName("企业汇总");
//        excelBiService.writeTempExcelByCorpList(param);
//        return new ObjectResponse<>(position);
    }

    @Operation( summary = "企业充电走势(电量)--尖峰平谷")
    @PostMapping("/exportBiElectByCorpList")
    public ObjectResponse<ExcelBiPosition> exportBiElectByCorpList(@RequestBody SiteBiParam param) {
        log.info("param = {}", param);
        throw new DcServiceException("接口已废弃，请联系开发");
//        ExcelBiPosition position = new ExcelBiPosition();
//        position.setSubFileName(UUIDUtils.getUuid32());
//        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
//        log.info("writeTempExcelByChargeOrderList 启动,position:{}", JsonUtils.toJsonString(position));
//        param.setExcelPosition(position);
//        param.setSheetName("企业充电走势电量汇总");
//        excelBiService.writeTempElectBySiteOrCommList(param, 1);
//        return new ObjectResponse<>(position);
    }

    @Operation( summary = "企业充电走势(消费)--总金额,电费,服务费")
    @PostMapping("/exportBiFeeByCorpList")
    public ObjectResponse<ExcelBiPosition> exportBiFeeByCorpList(@RequestBody SiteBiParam param) {
        log.info("param = {}", param);
        throw new DcServiceException("接口已废弃，请联系开发");
//        ExcelBiPosition position = new ExcelBiPosition();
//        position.setSubFileName(UUIDUtils.getUuid32());
//        position.setSubDir(new SimpleDateFormat("yyyyMMdd").format(new Date()));
//        log.info("writeTempExcelByChargeOrderList 启动,position:{}", JsonUtils.toJsonString(position));
//        param.setExcelPosition(position);
//        param.setSheetName("企业充电走势消费汇总");
//        excelBiService.writeTempFeeBySiteOrCommList(param,1);
//        return new ObjectResponse<>(position);
    }


}
