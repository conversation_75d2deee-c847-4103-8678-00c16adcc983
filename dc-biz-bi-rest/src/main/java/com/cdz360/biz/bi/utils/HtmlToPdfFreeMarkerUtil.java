package com.cdz360.biz.bi.utils;

import com.cdz360.biz.model.iot.dto.EvseDto;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.trading.site.po.EnvReport;
import com.cdz360.biz.model.trading.site.po.EvseReport;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.pdf.BaseFont;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.xhtmlrenderer.pdf.ITextFontResolver;
import org.xhtmlrenderer.pdf.ITextRenderer;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;

@Slf4j
public class HtmlToPdfFreeMarkerUtil {

    private static final String PDF_STORE_PATH = File.separator + "tmp" + File.separator + "pdf";
    private static final String HTML_PATH = PDF_STORE_PATH + File.separator + "html"; // 模板本地存放路径
    private static final String FONT_PATH = PDF_STORE_PATH + File.separator + "fonts"; // 字体本地存放路径
    private static final String FONT_NAME = "simhei.ttf"; // 字体名称
//    private static final String FONT_REMOTE_URL = "https://dc-sz-1.oss-cn-shenzhen.aliyuncs.com/pdf/simhei.ttf"; // 字体远程地址


    private static Configuration freemarkerCfg = null;

    static {
        freemarkerCfg = new Configuration();
        try {
            log.info("init");
            File htmlPath = new File(HTML_PATH);
            if (!htmlPath.exists()) {
                htmlPath.mkdirs();
                log.debug("mkdir htmlPath: {}", HTML_PATH);
            }
            //freemarker的模板目录
            freemarkerCfg.setDirectoryForTemplateLoading(new File(HTML_PATH));
        } catch (Exception e) {
            log.info("FreeMarkerUtil init fail: {}", e.getMessage(), e);
        }
    }


    /**
     * 主函数
     */
    public static void main(String[] args) throws IOException, DocumentException {

        {
            final String DEST = "HelloWorld_CN_HTML02222222222222";
            final String HTML = "siteInspectionDetail.html";
            Map<String, Object> data = new HashMap();

            data.put("siteName", "田子坊充电站");
            data.put("siteAddress", "上海市田子坊街85号");
            data.put("siteTypeDesc", "自营");
            data.put("No", "1888801");
            data.put("opName", "上杉胡");
            data.put("reportTime", new Date());
            data.put("qcName", "上杉胡");

            EvseReport evseReport = new EvseReport();

            EvseReport.Base sign = new EvseReport.Base();
            sign.setNormal(false)
                .setEvseNoList(List.of("12312451", "6546515135"))
                .setRemark("展示牌损坏");
            evseReport.setIsSignBoardNormal(null);
            evseReport.setIsEvseAppearanceNormal(sign);
            evseReport.setIsPlugLineNormal(sign);
            evseReport.setIsEvseShuntNormal(sign);
            evseReport.setIsInternalClean(sign);
            evseReport.setIsCoolingNormal(sign);
            evseReport.setIsDoorLockNormal(sign);
            evseReport.setIsRemoteFeaturesNormal(sign);
            evseReport.setIsElectrovalenceNormal(sign);
            evseReport.setIsRechargeRecordNormal(sign);
            evseReport.setIsWorksProperly(sign);

            data.put("evseReport", evseReport);

            EnvReport envReport = new EnvReport();
            envReport.setIsHydrops(true);
            envReport.setIsPipeBlockage(true);
            envReport.setIsLightingNormal(true);
            envReport.setIsLogoEyeCatching(false);
            envReport.setIsExtinguisherNormal(true);
            envReport.setIsSandBoxDry(true);
            envReport.setIsMonitorNormal(false);
            envReport.setIsDoorNormal(true);
            envReport.setIsSignoNormal(false);
            data.put("envReport", envReport);

            List<String> photos = new ArrayList<>();
            photos.add("https://fourth-platform.oss-cn-shanghai.aliyuncs.com/1555925871304.jpg");
            photos.add("https://fourth-platform.oss-cn-shanghai.aliyuncs.com/1555942030572.jpg");
            photos.add("https://fourth-platform.oss-cn-shanghai.aliyuncs.com/1555984244811.png");
            data.put("photos", photos);

            data.put("dcEvseNum", 10);
            data.put("acEvseNum", 10);

            EvseDto evseDto = new EvseDto();
            evseDto.setName("1号桩")
                .setEvseId("124125125552")
                .setPower(85)
                .setProduceDate(new Date())
                .setFirmwareVer("56")
                .setPhysicalNo("1234124242");

            EvseDto evseDto2 = new EvseDto();
            evseDto2.setName("2号桩")
                .setEvseId("1241251268782")
                .setPower(85)
                .setProduceDate(new Date())
                .setFirmwareVer("56")
                .setPhysicalNo("12341787878");

            EvseDto evseDto3 = new EvseDto();
            evseDto3.setName("3号桩")
                .setEvseId("12412512145566")
                .setPower(85)
                .setProduceDate(new Date())
                .setFirmwareVer("58")
                .setPhysicalNo("12341787444444");
            data.put("evseList", List.of(evseDto, evseDto2, evseDto3));

            ExcelPosition position = new ExcelPosition();
            position.setSubDir("123")
                .setSubFileName(DEST);
            HtmlToPdfFreeMarkerUtil.createPdf(data, position, HTML);
        }

//        {
//            HtmlToPdfFreeMarkerUtil.downloadFile("https://dc-sz-1.oss-cn-shenzhen.aliyuncs.com/bill/simhei.ttf",
//                    "/tmp/font", "simhei.ttf");
//        }
    }


    public static void createPdf(Map<String, Object> data, ExcelPosition position, String htmlTmp)
        throws IOException, DocumentException {
        log.info("data: {}, position: {}, htmlTmp: {}", data != null ? data.size() : null, position,
            htmlTmp);

        String content = freeMarkerRender(data, htmlTmp);

        ITextRenderer render = new ITextRenderer();
        ITextFontResolver fontResolver = render.getFontResolver();
        File fontFolder = new File(FONT_PATH);
        //如果文件夹不存在则创建
        if (!fontFolder.exists()) {
            fontFolder.mkdirs();
        }
        String fontPathStr = FONT_PATH + File.separator + FONT_NAME;
        File fontPath = new File(fontPathStr);
        if (!fontPath.exists()) {
            // 获取文件流
            ClassPathResource resource = new ClassPathResource(
                "freemarker/fonts" + File.separator + FONT_NAME);
            InputStream in = resource.getInputStream();

            downLoad(in, FONT_NAME, FONT_PATH);
        }
        fontResolver.addFont(fontPathStr, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
        // 解析html生成pdf
        render.setDocumentFromString(content);
        //解决图片相对路径的问题
//        render.getSharedContext().setBaseURL(LOGO_PATH);
        render.layout();
        // 文件存放目录
        String dir = PDF_STORE_PATH + File.separator + position.getSubDir();

        // 临时文件
        String tmpFilePath = dir + File.separator + position.getSubFileName() + "PART.pdf";

        // 重定向文件
        String filePath = dir + File.separator + position.getSubFileName() + ".pdf";

        File folder = new File(dir);
        //如果文件夹不存在则创建
        if (!folder.exists()) {
            folder.mkdirs();
        }
        //生成PDF
        render.createPDF(new FileOutputStream(tmpFilePath));
        //重命名:临时文件->最终文件名
        File finalFile = new File(filePath);
        if (finalFile.exists()) {
            finalFile.delete();
        }
        new File(tmpFilePath).renameTo(finalFile);
    }

    /**
     * freemarker渲染html
     */
    public static String freeMarkerRender(Map<String, Object> data, String htmlTmp) {
        Writer out = new StringWriter();
        try {
            // 获取模板,并设置编码方式
            log.info("freemarkerCfg: {}, htmlTmp: {}", freemarkerCfg, htmlTmp);
            String htmlPathStr = HTML_PATH + File.separator + htmlTmp;
            File htmlPath = new File(htmlPathStr);
            if (!htmlPath.exists()) {
                // 获取文件流
                ClassPathResource resource = new ClassPathResource("freemarker/" + htmlTmp);
                InputStream inputStream = resource.getInputStream();

                downLoad(inputStream, htmlTmp, HTML_PATH);
            }
            Template template = freemarkerCfg.getTemplate(htmlTmp);
            template.setEncoding("UTF-8");
            // 合并数据模型与模板
            template.process(data, out); //将合并后的数据和模板写入到流中，这里使用的字符流
            out.flush();
            return out.toString();
        } catch (Exception e) {
            log.info("catch error: {}", e.getMessage(), e);
        } finally {
            try {
                out.close();
            } catch (IOException ex) {
                log.info("finally error: {}", ex.getMessage(), ex);
            }
        }
        return null;
    }

    /**
     * 从网络Url中下载文件
     *
     * @param urlStr   远程URL
     * @param fileName 保存至本地时的文件名称
     * @param savePath 保存至本地时的文件路径
     * @return
     */
    public static String downLoadFromUrl(String urlStr, String fileName, String savePath) {
        try {
            log.info("param: {}, {}, {}", urlStr, fileName, savePath);
            URL url = new URL(urlStr);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            // 设置超时间为3秒
            conn.setConnectTimeout(3 * 1000);
            // 防止屏蔽程序抓取而返回403错误
            conn.setRequestProperty("User-Agent",
                "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");

            // 得到输入流
            InputStream inputStream = conn.getInputStream();
            downLoad(inputStream, fileName, savePath);

            return savePath + File.separator + fileName;
        } catch (Exception e) {
            log.error("catch msg: {}", e.getMessage(), e);
        }
        return "";

    }

    /**
     * 下载至本地
     *
     * @param inputStream
     * @param fileName    保存至本地时的文件名称
     * @param savePath    保存至本地时的文件路径
     * @throws IOException
     */
    public static void downLoad(InputStream inputStream, String fileName, String savePath)
        throws IOException {
        byte[] buffer = new byte[1024];
        int len = 0;
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        while ((len = inputStream.read(buffer)) != -1) {
            bos.write(buffer, 0, len);
        }
        bos.close();
        // 文件保存位置
        File saveDir = new File(savePath);
        log.info("boo: {}, boo2: {}", !saveDir.exists(), !saveDir.isDirectory());
        if (!saveDir.exists() && saveDir.isDirectory()) {
            saveDir.mkdir();
            log.info("mkdir");
        }
        File file = new File(saveDir + File.separator + fileName);
        log.info("exist: {}", file.exists());
        FileOutputStream fos = new FileOutputStream(file);
        fos.write(bos.toByteArray());
        if (fos != null) {
            fos.close();
        }
        if (inputStream != null) {
            inputStream.close();
        }
        log.info("download success: {}", fileName);
    }


}
