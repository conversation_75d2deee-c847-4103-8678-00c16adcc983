package com.cdz360.biz.bi.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.bi.config.ExportFileConfig;
import com.cdz360.biz.bi.domain.vo.OaPDFModelVo;
import com.cdz360.biz.bi.feign.AuthCenterFeignClient;
import com.cdz360.biz.bi.utils.PdfUtil;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.corp.po.RCorpPo;
import com.cdz360.biz.model.cus.balance.vo.BalanceApplicationVo;
import com.cdz360.biz.model.cus.settlement.param.ListSettlementParam;
import com.cdz360.biz.model.invoice.dto.OaActualData;
import com.cdz360.biz.model.invoice.dto.OaInvoiceBuyerInfo;
import com.cdz360.biz.model.invoice.dto.OaInvoiceSellerInfo;
import com.cdz360.biz.model.invoice.dto.OaReceiver;
import com.cdz360.biz.model.merchant.dto.SiteTinyDto;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import com.cdz360.biz.model.oa.constant.OaConstants;
import com.cdz360.biz.model.oa.param.BillingParam.Summary;
import com.cdz360.biz.model.oa.param.BillingParam.SummaryIndex;
import com.cdz360.biz.model.oa.param.PrepaidInvoicingEditParam;
import com.cdz360.biz.model.oa.type.OaHandleNode;
import com.cdz360.biz.model.oa.vo.OaStatisticsInfo;
import com.cdz360.biz.model.site.po.PriceTemplatePo;
import com.cdz360.biz.model.site.vo.SiteIncomeExpenseVo;
import com.cdz360.biz.model.trading.bi.dto.BiExportGroups;
import com.cdz360.biz.model.trading.rover.vo.ExportOaVo;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.po.SiteOaDefaultConfigItem;
import com.cdz360.biz.model.trading.site.po.SiteOaDefaultConfigPo;
import com.cdz360.biz.oa.dto.BillingOaDto;
import com.cdz360.biz.oa.dto.ChargerOrderFormDto;
import com.cdz360.biz.oa.dto.CorpCheckBillFormDto;
import com.cdz360.biz.oa.dto.ExpenseSiteFormDto;
import com.cdz360.biz.oa.dto.IncomeSiteFormDto;
import com.cdz360.biz.oa.dto.OaChargerOrderDto;
import com.cdz360.biz.oa.dto.OaPayBillDto;
import com.cdz360.biz.oa.dto.OaPostSettleBillInfo;
import com.cdz360.biz.oa.dto.OaSettlementBillDto;
import com.cdz360.biz.oa.dto.PayBillFormDto;
import com.cdz360.biz.oa.dto.PaymentPlan;
import com.cdz360.biz.oa.dto.SmartStrategyElecPrice;
import com.cdz360.biz.oa.dto.TargetPriceSchemeInfo;
import com.cdz360.biz.oa.param.DepositInvoiceParam;
import com.cdz360.biz.oa.param.ListTaskParam;
import com.cdz360.biz.oa.vo.CorpInvoiceInfoVo;
import com.cdz360.biz.oa.vo.OaRechargeInfoVo;
import com.cdz360.biz.oa.vo.OaTaskVo;
import com.cdz360.biz.utils.feign.auth.AuthSysUserFeignClient;
import com.cdz360.biz.utils.feign.data.DataCoreClient;
import com.cdz360.biz.utils.feign.data.DeviceDataCoreClient;
import com.cdz360.biz.utils.feign.invoice.InvoiceFeignClient;
import com.cdz360.biz.utils.feign.oa.OaRechargeClient;
import com.cdz360.biz.utils.feign.order.DataCoreOrderFeignClient;
import com.cdz360.biz.utils.feign.site.BiSiteOrderFeignClient;
import com.cdz360.biz.utils.feign.user.UserCorpFeignClient;
import com.cdz360.biz.utils.feign.user.UserFeignClient;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.framework.common.domain.invoice.param.CorpInvoiceInfoParam;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceRecordDetail;
import com.chargerlinkcar.framework.common.utils.ExcelUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;


@Slf4j
@Service
public class OaService {

    private static final DateTimeFormatter FORMAT_yyyy_MM_dd =
        DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter FORMAT_yyyy_MM_dd_HH_mm_ss =
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private SiteRoDs siteRoDs;

    @Autowired
    private InvoicedSalTempRefDs invoicedSalTempRefDs;
    @Autowired
    private InvoicedTemplateSalDs invoicedTemplateSalDs;

    @Autowired
    private BiSiteOrderFeignClient biSiteOrderFeignClient;

    @Autowired
    private ExportFileConfig exportFileConfig;

    @Autowired
    private OaRechargeClient oaRechargeClient;
    @Autowired
    private DeviceDataCoreClient deviceDataCoreClient;
    @Autowired
    private RedisIotReadService redisIotReadService;
    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private AuthSysUserFeignClient authSysUserFeignClient;
    @Autowired
    private UserCorpFeignClient userCorpFeignClient;
    @Autowired
    private InvoiceFeignClient invoiceFeignClient;
    @Autowired
    private DataCoreOrderFeignClient dataCoreOrderFeignClient;
    @Autowired
    private DataCoreClient dataCoreClient;

    @Autowired
    private CommercialQueryDs commercialQueryDs;

    @Autowired
    private RCorpRoDs rCorpRoDs;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

//    @Autowired
//    private OaExportPdfItemBuilder oaExportPdfItemBuilder;

    public void exportOaDetail(String procInstId, String sysUid, ExcelPosition pos) {
        log.info("导出OA详情: procInstId = {}, pos = {}", procInstId, pos.getSubFileName());
        oaRechargeClient.processDetail(procInstId, sysUid)
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .flatMap(data -> {
                List<String> newApply = List.of(
                    OaConstants.PD_KEY_PREPAID_ORDER_INVOICING,
                    OaConstants.PD_KEY_DEPOSIT_PROCESS,
                    OaConstants.PD_KEY_BILLING,
                    OaConstants.PD_KEY_RECHARGE,
                    OaConstants.PD_KEY_OTHER_FEE_PAY_APPLY,
                    OaConstants.PD_KEY_CHARGE_FEE,
                    OaConstants.PD_KEY_RENT_PAYMENT,
                    OaConstants.PD_ELEC_PAY,
                    OaConstants.PD_KEY_INCOME_SITE_OA,
                    OaConstants.PD_KEY_EXPENSE_SITE_OA
                );

                final String htmlFile = "oa_detail_pdf_temp.html";
                if (newApply.contains(data.getProcessDefinitionKey())) {
                    return this.generalFormDataX(data)
                        .doOnNext(d ->
                            PdfUtil.builder(exportFileConfig.getPdfDir(), pos, htmlFile)
                                .genPdf(d)
                                .write2File());
                } else {
                    PdfUtil.builder(exportFileConfig.getPdfDir(), pos, htmlFile)
                        .genPdf(data)
                        .write2File();
                }
                return Mono.just(data);
            })
            .block(Duration.ofSeconds(50L));
    }

    @SuppressWarnings("all")
    // 通用表单数据
    public Mono<OaPDFModelVo> generalFormDataX(OaRechargeInfoVo val) {
        OaPDFModelVo result = new OaPDFModelVo();
        result.setOaId(val.getProcessInstanceId())
            .setOaDefKey(val.getProcessDefinitionKey())
            .setOaName(val.getProcessDefinitionName())
            .setCommentList(val.getCommentList());

        Map formInfo = val.getFormInfo();
        switch (result.getOaDefKey()) {
            case OaConstants.PD_KEY_PREPAID_ORDER_INVOICING:
                ChargerOrderFormDto formM = JsonUtils.fromJson(
                    JsonUtils.toJsonString(val.getFormInfo().get("formVariables")),
                    ChargerOrderFormDto.class);

                // 原来数据结构
                PrepaidInvoicingEditParam srcData = JsonUtils.fromJson(
                    JsonUtils.toJsonString(val.getFormInfo().get("formVariables")),
                    PrepaidInvoicingEditParam.class);

                // 充电列表选择
                if (CollectionUtils.isNotEmpty(srcData.getBillInvoiceVoList())) {
                    formM.setChargerOrderList(srcData.getBillInvoiceVoList().stream()
                        .map(x -> {
                            return new OaChargerOrderDto()
                                .setOrderNo(x.getOrderNo())
                                .setInvoiceAmount(x.getInvoiceAmount());
                        }).collect(Collectors.toList()));
                }

//                InvoiceRecordParam invRec = new InvoiceRecordParam();    // 发票，订单开票暂时只支持一张

                // 商品行信息
//                List<OaInvoiceSphDetail> sphDetailsY = new ArrayList<>();
//                if (CollectionUtils.isNotEmpty(srcData.getInvoiceRecords())) {
//                    sphDetailsY = srcData.getInvoiceRecords().stream()
//                        .map(x -> {
//                            Optional<InvoicingContentVo> first = srcData.getActualData().stream()
//                                .filter(k -> null != k &&
//                                    null != k.getProductType() &&
//                                    k.getProductType().equals(x.getProductType()))
//                                .findFirst();
//                            return new OaInvoiceSphDetail()
//                                .setTaxRate(x.getTaxRate().intValue())
//                                .setProductType(x.getProductType())
//                                .setProductName(x.getProductName())
//                                .setAmount(first.isPresent() ?
//                                    first.get().getFixAmount() : BigDecimal.ZERO)
//                                .setFixAmount(x.getFixAmount())
//                                .setSpec(x.getSpec())
//                                .setUnit(x.getUnit())
//                                .setCount(x.getNum().intValue());
//                        })
//                        .collect(Collectors.toList());
//                }
//                formM.setSphDetails(sphDetailsY);
                formM.setInvoiceRecords(srcData.getInvoiceRecords());

//                List<OaInvoiceSphDetail> actualDataY = new ArrayList<>();
//                if (CollectionUtils.isNotEmpty(srcData.getActualData())) {
//                    Map<String, List<OaInvoiceSphDetail>> stringListMap = sphDetailsY.stream().collect(Collectors.groupingBy(OaInvoiceSphDetail::getProductName));
//                    srcData.getActualData().stream()
//                            .forEach(x -> {
//                                List<OaInvoiceSphDetail> oaInvoiceSphDetails = stringListMap.get(x.getProductName());
//                                BigDecimal fixAmount;
//                                if (Objects.isNull(oaInvoiceSphDetails) || oaInvoiceSphDetails.size() < 1) {
//                                    fixAmount = BigDecimal.ZERO;
//                                } else {
//                                    fixAmount  = oaInvoiceSphDetails.stream().map(OaInvoiceSphDetail::getFixAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
//                                }
//                                OaInvoiceSphDetail oaInvoiceSphDetail = new OaInvoiceSphDetail()
//                                        .setTaxRate(x.getTaxRate().intValue())
//                                        .setProductType(x.getProductType())
//                                        .setProductName(x.getProductName())
//                                        .setAmount(x.getFixAmount())
//                                        .setFixAmount(fixAmount)
//                                        .setSpec(x.getSpec())
//                                        .setUnit(x.getUnit())
//                                        .setCount(x.getNum().intValue());
//                                actualDataY.add(oaInvoiceSphDetail);
//                            });
//                }
                formM.setActualData(srcData.getActualData());

                // 发票信息
                return this.dataCoreClient.getInvoiceVo4PrepaidProcess(
                        val.getProcessInstanceId())
                    .doOnNext(FeignResponseValidate::check)
                    .map(ObjectResponse::getData)
                    .map(x -> {
                        // (购方)发票抬头
                        formM.setInvoiceBuyerInfo(new OaInvoiceBuyerInfo()
                            .setType(formM.getInvoiceType())
                            .setTin(x.getTin())
                            .setName(x.getName())
                            .setAddress(x.getAddress())
                            .setEmail(x.getEmail())
                            .setPhone(x.getTel())
                            .setBankName(x.getBank())
                            .setBankAccount(x.getBankAccount())
                            .setReceiver(new OaReceiver()
                                .setName(x.getReceiverName())
                                .setPhone(x.getReceiverMobilePhone())
                                .setProvinceName(x.getReceiverProvince())
                                .setCityName(x.getReceiverCity())
                                .setAreaName(x.getReceiverArea())
                                .setAddress(x.getReceiverAddress())));

                        Map mapY = formM.toMap();
                        // 显示附加信息-开票主体
                        mapY.put("invoicedTempSalName", x.getSaleName());
                        mapY.put("invoicedTempSalChannel", x.getChannel());
                        mapY.put("invoicedTempSalRefName", x.getProductTempName());
                        result.setFormData(mapY);
                        return result;
                    });
            case OaConstants.PD_KEY_DEPOSIT_PROCESS:
                PayBillFormDto formZ = JsonUtils.fromJson(
                    JsonUtils.toJsonString(val.getFormInfo().get("formVariables")),
                    PayBillFormDto.class);

                // 原来数据结构
                DepositInvoiceParam deposit = JsonUtils.fromJson(
                    JsonUtils.toJsonString(val.getFormInfo().get("formVariables")),
                    DepositInvoiceParam.class);

                // 充值列表选择
                if (CollectionUtils.isNotEmpty(deposit.getPayBillChoiceList())) {
                    formZ.setPayBillList(deposit.getPayBillChoiceList().stream()
                        .map(x -> {
                            return new OaPayBillDto()
                                .setOrderId(x.getOrderId())
                                .setAmount(x.getAmount())
                                .setFreeAmount(x.getFreeAmount())
                                .setInvoiceAmount(x.getCanInvoiceAmount())
                                .setPayTime(x.getPayTime())
                                .setOutAccountName(x.getOutAccountName())
                                .setProcInstId(x.getProcInstId());
                        }).collect(Collectors.toList()));
                }

                // (购方)发票抬头
                CorpInvoiceInfoVo buyer = deposit.getCorpInvoiceInfoVo();
                if (null != buyer) {
                    formZ.setInvoiceBuyerInfo(new OaInvoiceBuyerInfo()
                        .setType(buyer.getInvoiceType())
                        .setTin(buyer.getTin())
                        .setName(buyer.getName())
                        .setAddress(buyer.getAddress())
                        .setEmail(buyer.getEmail())
                        .setPhone(buyer.getTel())
                        .setBankName(buyer.getBank())
                        .setBankAccount(buyer.getBankAccount())
                        .setReceiver(new OaReceiver()
                            .setName(buyer.getReceiverName())
                            .setPhone(buyer.getReceiverMobilePhone())
                            .setProvinceName(buyer.getReceiverProvince())
                            .setCityName(buyer.getReceiverCity())
                            .setAreaName(buyer.getReceiverArea())
                            .setAddress(buyer.getReceiverAddress())));
                }

                // 开票主体(销方)
                new OaInvoiceSellerInfo()
                    .setInvoiceTmpSalId(deposit.getTempSalId())
                    .setInvoiceSalTmpRefId(deposit.getProductTempId());

//                // 商品行信息
//                List<OaInvoiceSphDetail> sphDetailsX = new ArrayList<>();
//                if (CollectionUtils.isNotEmpty(deposit.getInvoiceRecords())) {
//                    sphDetailsX = deposit.getInvoiceRecords().get(0)    // 充值开票暂不支持多张票，先按第一张做兼容处理
//                        .getContents().stream()
//                        .map(x -> {
//                            Optional<InvoiceCompareVo> first =
//                                CollectionUtils.isNotEmpty(deposit.getActualData()) ?
//                                    deposit.getActualData().stream()
//                                        .filter(k -> null != k &&
//                                            null != k.getProductType() &&
//                                            k.getProductType().equals(x.getProductType()))
//                                        .findFirst()
//                                    : Optional.empty();
//                            return new OaInvoiceSphDetail()
//                                .setTaxRate(x.getTaxRate().intValue())
//                                .setProductType(x.getProductType())
//                                .setProductName(x.getProductName())
//                                .setAmount(first.isPresent() ?
//                                    first.get().getFixAmount() : BigDecimal.ZERO)
//                                .setFixAmount(x.getFixAmount())
//                                .setSpec(x.getSpec())
//                                .setUnit(x.getUnit())
//                                .setCount(x.getNum().intValue());
//                        })
//                        .collect(Collectors.toList());
//                }
                formZ.setInvoiceRecords(deposit.getInvoiceRecords());

//                List<InvoiceCompareVo> actualDataX = new ArrayList<>();
//                if (CollectionUtils.isNotEmpty(deposit.getActualData())) {
//                    Map<String, List<OaInvoiceSphDetail>> stringListMap = sphDetailsX.stream()
//                        .collect(Collectors.groupingBy(OaInvoiceSphDetail::getProductName));
//                    deposit.getActualData().stream()
//                        .forEach(x -> {
//                            List<OaInvoiceSphDetail> oaInvoiceSphDetails = stringListMap.get(
//                                x.getProductName());
//                            BigDecimal fixAmount;
//                            if (Objects.isNull(oaInvoiceSphDetails)
//                                || oaInvoiceSphDetails.size() < 1) {
//                                fixAmount = BigDecimal.ZERO;
//                            } else {
//                                fixAmount = oaInvoiceSphDetails.stream()
//                                    .map(OaInvoiceSphDetail::getFixAmount)
//                                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//                            }
//                            InvoiceCompareVo oaInvoiceSphDetail = new InvoiceCompareVo()
////                                        .setTaxRate(x.getTaxRate().intValue())
//                                .setProductType(x.getProductType())
//                                .setProductName(x.getProductName())
//                                .setAmount(x.getFixAmount())
//                                .setFixAmount(fixAmount);
////                                        .setSpec(x.getSpec())
////                                        .setUnit(x.getUnit())
////                                        .setCount(x.getNum().intValue())
////                                        ;
//                            actualDataX.add(oaInvoiceSphDetail);
//                        });
//                }
                formZ.setActualData(deposit.getActualData());

                formZ.setInvoiceRemark(deposit.getInvoicingRemark()); // 发票备注

                Map mapX = formZ.toMap();
                // 显示附加信息-开票主体
                mapX.put("invoicedTempSalName", deposit.getSaleName());
                mapX.put("invoicedTempSalRefName", deposit.getProductTempName()); // 商品行名称
                result.setFormData(mapX);
                return Mono.just(result);
            case OaConstants.PD_KEY_BILLING:
                CorpCheckBillFormDto formT;
                BillingOaDto billInfoT;
                Object formVariables = val.getFormInfo().get("formVariables");
                try {
                    formT = JsonUtils.fromJson(
                        JsonUtils.toJsonString(formVariables),
                        CorpCheckBillFormDto.class);

                    // 原来数据结构
                    billInfoT = JsonUtils.fromJson(
                        JsonUtils.toJsonString(formVariables),
                        BillingOaDto.class);
                } catch (Exception e) {
                    HashMap hm = JsonUtils.fromJson(
                        JsonUtils.toJsonString(formVariables),
                        HashMap.class);
                    hm.remove("attachmentLink");

                    formT = JsonUtils.fromJson(
                        JsonUtils.toJsonString(hm),
                        CorpCheckBillFormDto.class);

                    // 原来数据结构
                    billInfoT = JsonUtils.fromJson(
                        JsonUtils.toJsonString(hm),
                        BillingOaDto.class);
                }

                BillingOaDto billInfo = billInfoT;
                CorpCheckBillFormDto formY = formT;
                formY.setHandleNodes(new ArrayList<>())
                    .setBillDateRange(new TimeFilter()
                        .setStartTime(billInfo.getSettlementStart())
                        .setEndTime(billInfo.getSettlementEnd()))
                    .setSettBillSummary(new OaPostSettleBillInfo()
                        .setDifference(billInfo.getDiscrepant()))
                    .setDifferenceRemark(billInfo.getDiscrepantDesc());

                // 企业信息
//                Mono<CorpPo> corpMono = authSysUserFeignClient.getCorp(formY.getCorpId())
//                    .doOnNext(FeignResponseValidate::check)
//                    .map(ObjectResponse::getData);

                // 原结算单信息
                Mono<List<OaSettlementBillDto>> settleBillListMono = Mono.justOrEmpty(
                        billInfo.getBillNoList())
                    .flatMap(list -> userFeignClient.findSettlementList(
                        new ListSettlementParam().setBillNoList(list)))
                    .doOnNext(FeignResponseValidate::check)
                    .map(ListResponse::getData)
                    .switchIfEmpty(Mono.just(new ArrayList<>()))
                    .map(list -> list.stream().map(x -> new OaSettlementBillDto()
                            .setBillName(x.getBillName())
                            .setBillNo(x.getBillNo())
                            .setElec(x.getOrderKwh())
                            .setStartDateDay(x.getSettStartDateDay())
                            .setEndDateDay(x.getSettEndDateDay())
                            .setTotalFee(x.getSettlementTotalFee())
                            .setElecFee(x.getSettlementElecFee())
                            .setServFee(x.getSettlementServFee())
                            .setOtherFee(x.getSettlementOtherFee())
                            .setSiteNameList(x.getSiteNameList())
                            .setSiteNoList(x.getSiteNoList()))
                        .collect(Collectors.toList()));

                // 用印
                if (Boolean.TRUE.equals(billInfo.getUseSignet())) {
                    formY.getHandleNodes().add(OaHandleNode.SIGNET);
                    formY.setSignetCompany(billInfo.getSignetCompany())
                        .setSignetType(billInfo.getSignetType())
                        .setSignetDesc(billInfo.getSignetDesc());
                }

                // 开票
                Mono<CorpInvoiceRecordDetail> invoiceRecMono = null;
                if (Boolean.TRUE.equals(billInfo.getInvoice())) {
                    formY.getHandleNodes().add(OaHandleNode.INVOICE);

                    invoiceRecMono = dataCoreOrderFeignClient.corpInvoiceRecordDetail(
                            billInfo.getApplyNo())
                        .doOnNext(FeignResponseValidate::check)
                        .map(ObjectResponse::getData);
                } else {
                    invoiceRecMono = Mono.just(new CorpInvoiceRecordDetail());
                }

//                this.getBillData(val);
                return Mono.zip(settleBillListMono, invoiceRecMono)
                    .flatMap(t -> {
                        List<OaSettlementBillDto> settleBillList = t.getT1();
                        CorpInvoiceRecordDetail invoiceRec = t.getT2();

                        // 实际数据信息
                        Summary actualInfo = billInfo.getSummaryData().stream()
                            .filter(i -> SummaryIndex.ACTUAL_ROW.equals(i.getIndex()))
                            .findFirst()
                            .orElseGet(Summary::new);
                        OaActualData actualData = new OaActualData()
                            .setElec(actualInfo.getElec())
                            .setOtherFee(actualInfo.getOtherFee());
                        actualData.setServFee(actualInfo.getServFee())
                            .setElecFee(actualInfo.getElecFee());

                        // 实际结算账单信息
                        List<OaSettlementBillDto> actualSettleBillList = billInfo.getSummaryData()
                            .stream()
                            .filter(i -> SummaryIndex.BILL_ROW.equals(i.getIndex()))
                            .map(x -> {
                                return new OaSettlementBillDto()
                                    .setBillName(x.getName())
                                    .setBillNo(x.getBillNo())
                                    .setStartDateDay(x.getSettStartDateDay())
                                    .setEndDateDay(x.getSettEndDateDay())
                                    .setElec(x.getElec())
                                    .setTotalFee(x.getSettlementFee())
                                    .setElecFee(x.getElecFee())
                                    .setServFee(x.getServFee())
                                    .setOtherFee(x.getOtherFee());
                            })
                            .collect(Collectors.toList());

                        // 回款计划
                        if (null != billInfo.getReturnFlag()) {
                            formY.setPaymentPlanStatus(billInfo.getReturnFlag());
                            if (Boolean.FALSE.equals(formY.getPaymentPlanStatus()) &&
                                CollectionUtils.isNotEmpty(billInfo.getReturnPlanVoList())) {
                                formY.setPaymentPlans(billInfo.getReturnPlanVoList().stream()
                                    .filter(x -> x.getIndex() != 4)
                                    .map(x -> new PaymentPlan()
                                        .setOrder(x.getIndex())
                                        .setAmount(x.getAmount())
                                        .setTime(x.getTime())).collect(Collectors.toList()));
                            }
                        }

                        formY.setSettleBillList(settleBillList)
                            .getSettBillSummary()
                            .setSettleBillList(actualSettleBillList)
                            .setActualData(actualData);

                        // 发票信息
                        if (invoiceRec.getProductTempId() != null) {
                            CorpInvoiceInfoParam param = new CorpInvoiceInfoParam();
                            param.setUid(invoiceRec.getUid())
                                .setTempSalId(invoiceRec.getTempSalId())
                                .setProductTempId(invoiceRec.getProductTempId());

                            return invoiceFeignClient.getCorpInvoiceDetail(param)
                                .doOnNext(FeignResponseValidate::check)
                                .map(ObjectResponse::getData)
                                .map(invoice -> {
                                    // (购方)发票抬头
                                    formY.setInvoiceBuyerInfo(new OaInvoiceBuyerInfo()
                                        .setType(invoice.getInvoiceType())
                                        .setTin(invoice.getTin())
                                        .setName(invoice.getName())
                                        .setAddress(invoice.getAddress())
                                        .setEmail(invoice.getEmail())
                                        .setPhone(invoice.getTel())
                                        .setBankName(invoice.getBank())
                                        .setBankAccount(invoice.getBankAccount())
                                        .setReceiver(new OaReceiver()
                                            .setName(invoice.getReceiverName())
                                            .setPhone(invoice.getReceiverMobilePhone())
                                            .setProvinceName(invoice.getReceiverProvince())
                                            .setCityName(invoice.getReceiverCity())
                                            .setAreaName(invoice.getReceiverArea())
                                            .setAddress(invoice.getReceiverAddress())));

//                                    List<InvoiceRecordParam> invoiceRecordParams = new ArrayList<>();
//                                    List<InvoiceContentParam> compareContents = new ArrayList<>();
//                                    if (CollectionUtils.isNotEmpty(billInfo.getInvoiceList())) {
//                                        InvoiceRecordParam invoiceRecord = new InvoiceRecordParam();
//                                        invoiceRecord.setRemark(billInfo.getInvoiceRemark());
//                                        List<InvoiceContentParam> contents = billInfo.getInvoiceList()
//                                            .stream()
//                                            .map(x -> {
//                                                return new InvoiceContentParam()
//                                                    .setCode(x.getCode())
//                                                    .setFixAmount(x.getAmount())
//                                                    .setProductType(x.getType())
//                                                    .setProductName(x.getName())
//                                                    .setSpec(x.getSpec())
//                                                    .setNum(x.getNumber())
//                                                    .setPrice(x.getUnitPrice())
//                                                    .setTaxRate(x.getTaxRate())
//                                                    .setUnit(x.getUnit());
//                                            })
//                                            .collect(Collectors.toList());
//                                        invoiceRecord.setContents(contents);
//                                        invoiceRecordParams.add(invoiceRecord);
//                                        compareContents.addAll(contents);
//                                    }
//                                    formY.setInvoiceRecords(invoiceRecordParams);

//                                    List<InvoiceCompareVo> actual = new ArrayList<>();
//                                    if (CollectionUtils.isNotEmpty(billInfo.getActualData())) {
//                                        Map<String, List<InvoiceContentParam>> stringListMap = compareContents.stream()
//                                            .collect(Collectors.groupingBy(
//                                                InvoiceContentParam::getProductName));
//                                        billInfo.getActualData().stream()
//                                            .forEach(x -> {
//                                                List<InvoiceContentParam> oaInvoiceSphDetails = stringListMap.get(
//                                                    x.getProductName());
//                                                BigDecimal fixAmount;
//                                                if (Objects.isNull(oaInvoiceSphDetails)
//                                                    || oaInvoiceSphDetails.size() < 1) {
//                                                    fixAmount = BigDecimal.ZERO;
//                                                } else {
//                                                    fixAmount = oaInvoiceSphDetails.stream()
//                                                        .map(InvoiceContentParam::getFixAmount)
//                                                        .reduce(BigDecimal.ZERO, BigDecimal::add);
//                                                }
//                                                InvoiceCompareVo oaInvoiceSphDetail = new InvoiceCompareVo()
////                                                            .setTaxRate(x.getTaxRate().intValue())
//                                                    .setProductType(x.getProductType())
//                                                    .setProductName(x.getProductName())
//                                                    .setAmount(x.getFixAmount())
//                                                    .setFixAmount(fixAmount);
////                                                            .setSpec(x.getSpec())
////                                                            .setUnit(x.getUnit())
////                                                            .setCount(x.getNum().intValue());
//                                                actual.add(oaInvoiceSphDetail);
//                                            });
//                                    }
//                                    formY.setActualData(actual);

                                    formY.setInvoiceWay(invoiceRec.getInvoiceWay());
//                                    BeanMap map = BeanMap.create(formY);
//                                    HashMap map = JsonUtils.fromJson(
//                                        JsonUtils.toJsonString(formY), HashMap.class);
                                    Map map = formY.toMap();
                                    // 显示附加信息-开票主体
                                    map.put("invoicedTempSalName", invoice.getSaleName());
                                    result.setFormData(map);
                                    return result;
                                });
                        }

                        result.setFormData(BeanMap.create(formY));
                        return Mono.just(result);
                    });
            case OaConstants.PD_KEY_RECHARGE:
                return this.getBalanceApplyById(Long.valueOf(formInfo.get("dataId").toString()))
                    .map(ObjectResponse::getData)
                    .map(x -> {
                        HashMap<String, Object> m = new HashMap<>(
                            (HashMap) formInfo.get("formVariables"));

                        BeanMap data = BeanMap.create(x);
                        data.keySet().forEach(key -> {
                            if (null != data.get(key)) { // 去掉null
                                m.put(key.toString(), data.get(key));
                            }
                        });

                        formInfo.putAll(m);
                        result.setFormData(formInfo);
                        return result;
                    });
            case OaConstants.PD_KEY_CHARGE_FEE:
                Map tmpFormInfo;
                if (formInfo.containsKey("formVariables")) { // 旧版本数据接口兼容
                    tmpFormInfo = JsonUtils.fromJson(
                        JsonUtils.toJsonString(formInfo.get("formVariables")), HashMap.class);
                    if (tmpFormInfo.containsKey("exeDate") &&
                        tmpFormInfo.get("exeDate").toString().contains("T")) {
                        SimpleDateFormat format = new SimpleDateFormat(
                            "yyyy-MM-dd'T'HH:mm:ss.SSSX");
                        try {
                            tmpFormInfo.put("exeDate",
                                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(
                                    format.parse(tmpFormInfo.get("exeDate").toString())));
                        } catch (Exception e) {
                            log.warn("解析时间异常: {}", e.getMessage(), e);
                        }
                    }

                    //补充次轮模板数据
                    if (tmpFormInfo.containsKey("exeDate2") &&
                        tmpFormInfo.get("exeDate2").toString().contains("T")) {
                        SimpleDateFormat format = new SimpleDateFormat(
                            "yyyy-MM-dd'T'HH:mm:ss.SSSX");
                        try {
                            tmpFormInfo.put("exeDate2",
                                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(
                                    format.parse(tmpFormInfo.get("exeDate2").toString())));
                        } catch (Exception e) {
                            log.warn("解析时间异常: {}", e.getMessage(), e);
                        }
                    }
                } else {
                    tmpFormInfo = formInfo;
                }
                if (formInfo.containsKey("timeBasedElecPrice")) {
                    tmpFormInfo.put("timeBasedElecPrice",
                        JsonUtils.fromJson(formInfo.get("timeBasedElecPrice").toString(),
                            SmartStrategyElecPrice.class));
                }
                if (formInfo.containsKey("timeBasedElecPrice2")) {
                    tmpFormInfo.put("timeBasedElecPrice2",
                        JsonUtils.fromJson(formInfo.get("timeBasedElecPrice2").toString(),
                            SmartStrategyElecPrice.class));
                }

                // 获取到所有的计费模版
                List<Long> priceTemplateIdList = new ArrayList<>();
                List<TargetPriceSchemeInfo> smartPriceList;
                if (formInfo.containsKey("smartInfoList")) {
                    ObjectMapper mapper = new ObjectMapper();
                    smartPriceList = mapper.convertValue(formInfo.get("smartInfoList"),
                        new TypeReference<List<TargetPriceSchemeInfo>>() {
                        });
                    if (smartPriceList != null) {
                        priceTemplateIdList.addAll(smartPriceList.stream()
                            .map(TargetPriceSchemeInfo::getPriceSchemeId).distinct().toList());
                    }
                } else {
                    smartPriceList = new ArrayList<>();
                }
                List<TargetPriceSchemeInfo> smartPriceList2;
                ObjectMapper mapper = new ObjectMapper();
                if (formInfo.containsKey("smartInfoList2")) {
                    smartPriceList2 = mapper.convertValue(formInfo.get("smartInfoList2"),
                        new TypeReference<List<TargetPriceSchemeInfo>>() {
                        });
                    if (smartPriceList2 != null) {
                        priceTemplateIdList.addAll(smartPriceList2.stream()
                            .map(TargetPriceSchemeInfo::getPriceSchemeId).distinct().toList());
                    }
                } else {
                    smartPriceList2 = new ArrayList<>();
                }
                List<TargetPriceSchemeInfo> targetInfoList;
                if (formInfo.containsKey("targetInfoList")) {
                    targetInfoList = mapper.convertValue(formInfo.get("targetInfoList"),
                        new TypeReference<List<TargetPriceSchemeInfo>>() {
                        });
                    if (targetInfoList != null) {
                        priceTemplateIdList.addAll(targetInfoList.stream()
                            .map(TargetPriceSchemeInfo::getPriceSchemeId).distinct().toList());
                    }
                } else {
                    targetInfoList = new ArrayList<>();
                }

                List<TargetPriceSchemeInfo> targetInfoList2 = new ArrayList<>();
                if (formInfo.containsKey("targetInfoList2")) {
                    targetInfoList2 = mapper.convertValue(formInfo.get("targetInfoList"),
                        new TypeReference<List<TargetPriceSchemeInfo>>() {
                        });
                    if (targetInfoList2 != null) {
                        priceTemplateIdList.addAll(targetInfoList2.stream()
                            .map(TargetPriceSchemeInfo::getPriceSchemeId).distinct().toList());
                    }
                }

                if (formInfo.get("priceSchemeId") != null) {
                    priceTemplateIdList.add(
                        Long.valueOf(String.valueOf(formInfo.get("priceSchemeId"))));
                }
                if (formInfo.get("acPriceSchemeId") != null) {
                    priceTemplateIdList.add(
                        Long.valueOf(String.valueOf(formInfo.get("acPriceSchemeId"))));
                }
                if (formInfo.get("dcPriceSchemeId") != null) {
                    priceTemplateIdList.add(
                        Long.valueOf(String.valueOf(formInfo.get("dcPriceSchemeId"))));
                }
                if (formInfo.get("priceSchemeId2") != null) {
                    priceTemplateIdList.add(
                        Long.valueOf(String.valueOf(formInfo.get("priceSchemeId2"))));
                }
                if (formInfo.get("acPriceSchemeId2") != null) {
                    priceTemplateIdList.add(
                        Long.valueOf(String.valueOf(formInfo.get("acPriceSchemeId2"))));
                }
                if (formInfo.get("dcPriceSchemeId2") != null) {
                    priceTemplateIdList.add(
                        Long.valueOf(String.valueOf(formInfo.get("dcPriceSchemeId2"))));
                }
                // 处理一轮普通，二轮智能的情况
                // 一轮和二轮都只有一个模版
                Long smartInfo2PriceId;
                if (formInfo.get("smartInfo2") != null) {
                    Map<String, Object> smartInfo2PriceTemplate = mapper.convertValue(
                        formInfo.get("smartInfo2"),
                        new TypeReference<Map<String, Object>>() {
                        });
                    if (smartInfo2PriceTemplate != null
                        && smartInfo2PriceTemplate.get("priceSchemeId") != null
                        && smartInfo2PriceTemplate.get("priceSchemeId") instanceof Number) {
                        smartInfo2PriceId = Long.valueOf(
                            String.valueOf(smartInfo2PriceTemplate.get("priceSchemeId")));
                        priceTemplateIdList.add(smartInfo2PriceId);
                    } else {
                        smartInfo2PriceId = 0L;
                    }
                } else {
                    smartInfo2PriceId = 0L;
                }
                // 一轮和二轮都有两个模版
                Long smartInfo2ACPriceId;
                if (formInfo.get("smartInfo2AC") != null) {
                    Map<String, Object> smartInfo2ACPriceTemplate = mapper.convertValue(
                        formInfo.get("smartInfo2AC"),
                        new TypeReference<Map<String, Object>>() {
                        });
                    if (smartInfo2ACPriceTemplate != null
                        && smartInfo2ACPriceTemplate.get("priceSchemeId") != null
                        && smartInfo2ACPriceTemplate.get("priceSchemeId") instanceof Number) {
                        smartInfo2ACPriceId = Long.valueOf(
                            String.valueOf(smartInfo2ACPriceTemplate.get("priceSchemeId")));
                        priceTemplateIdList.add(smartInfo2ACPriceId);
                    } else {
                        smartInfo2ACPriceId = 0L;
                    }
                } else {
                    smartInfo2ACPriceId = 0L;
                }
                Long smartInfo2DCPriceId;
                if (formInfo.get("smartInfo2DC") != null) {
                    Map<String, Object> smartInfo2DCPriceTemplate = mapper.convertValue(
                        formInfo.get("smartInfo2DC"),
                        new TypeReference<Map<String, Object>>() {
                        });
                    if (smartInfo2DCPriceTemplate != null
                        && smartInfo2DCPriceTemplate.get("priceSchemeId") != null
                        && smartInfo2DCPriceTemplate.get("priceSchemeId") instanceof Number) {
                        smartInfo2DCPriceId = Long.valueOf(
                            String.valueOf(smartInfo2DCPriceTemplate.get("priceSchemeId")));
                        priceTemplateIdList.add(smartInfo2DCPriceId);
                    } else {
                        smartInfo2DCPriceId = 0L;
                    }
                } else {
                    smartInfo2DCPriceId = 0L;
                }

                // 计费模板信息
                if (CollectionUtils.isEmpty(priceTemplateIdList)) {
                    result.setFormData(tmpFormInfo);
                    log.info("电价下发流程数据：{}", result);
                    return Mono.just(result);
                }
                return deviceDataCoreClient.getTimeBasedPriceSchemaList(priceTemplateIdList)
                    .doOnNext(FeignResponseValidate::check)
                    .map(response -> {
                        if (response instanceof ListResponse) {
                            return ((ListResponse<?>) response).getData();
                        }
                        return new ArrayList<>();
                    })
//                return deviceDataCoreClient.getPriceSchema(
//                        Long.valueOf(String.valueOf(tmpFormInfo.get("priceSchemeId")))) // 价格模板信息
//                    .doOnNext(FeignResponseValidate::check)
//                    .map(ObjectResponse::getData)
                    .map(x -> {
                        List<PriceTemplatePo> t7 = (List<PriceTemplatePo>) x;
//                        tmpFormInfo.put("priceSchemeInfo", x);

                        // 调整前和调整后的计费信息，最外层key：场站名称or枪头名称
                        // 中间层，smartPriceSchemeList-调整后，targetPriceSchemeList-调整前，内层key是DC、AC or ALL，标识交直流或者整体
                        Map<String, Map<String, Map<String, PriceTemplatePo>>> priceTemplateInfo = new HashMap<>();

                        // 桩信息列表
                        if (!tmpFormInfo.containsKey("targetInfoList")) {
                            if (null != tmpFormInfo.get("evseNoList")) {
                                var evseNoList = (List<String>) tmpFormInfo.get("evseNoList");
                                List<EvseVo> evseList = redisIotReadService.getEvseList(evseNoList);
                                tmpFormInfo.put("targetInfoList", evseList.stream()
                                    .map(evse -> new TargetPriceSchemeInfo()
                                        .setNo(evse.getEvseNo())
                                        .setName(evse.getName()))
                                    .collect(Collectors.toList()));
                            } else if (null == tmpFormInfo.get("siteNameList")) {
                                var siteIdList = (List<String>) tmpFormInfo.get("siteIdList");
                                if (CollectionUtils.isNotEmpty(siteIdList)) {
                                    tmpFormInfo.put("siteNameList",
                                        siteRoDs.getSiteTinyList(new ListSiteParam()
                                                .setSiteIdList(siteIdList)).stream()
                                            .map(SiteTinyDto::getSiteName)
                                            .collect(Collectors.toList()));
                                }
                            }
                        }
                        if (!tmpFormInfo.containsKey("targetInfoList2")) {
                            if (null != tmpFormInfo.get("evseNoList2")) {
                                var evseNoList = (List<String>) tmpFormInfo.get("evseNoList2");
                                List<EvseVo> evseList = redisIotReadService.getEvseList(evseNoList);
                                tmpFormInfo.put("targetInfoList2", evseList.stream()
                                    .map(evse -> new TargetPriceSchemeInfo()
                                        .setNo(evse.getEvseNo())
                                        .setName(evse.getName()))
                                    .collect(Collectors.toList()));
                            }
                        }

                        // 调整前，targetInfoList就够了，不需要再处理targetInfoList2
                        // 调整前后的名称和计费模版Id map
                        Map<String, Long> smartSiteNameAndACPriceIdMap = new HashMap<>();
                        Map<String, Long> smartSiteNameAndDCPriceIdMap = new HashMap<>();
                        Map<String, Long> smartSiteNameAndAllPriceIdMap = new HashMap<>();
                        Map<String, Long> smartSiteNameAndACPriceIdMap2 = new HashMap<>();
                        Map<String, Long> smartSiteNameAndDCPriceIdMap2 = new HashMap<>();
                        Map<String, Long> smartSiteNameAndAllPriceIdMap2 = new HashMap<>();

                        Map<String, Long> targetSiteNameAndACPriceIdMap = new HashMap<>();
                        Map<String, Long> targetSiteNameAndDCPriceIdMap = new HashMap<>();
                        Map<String, Long> targetSiteNameAndAllPriceIdMap = new HashMap<>();
                        // 非智能策略用的
                        Map<String, Long> siteNameAndPriceIdMap = new HashMap<>();
                        Map<String, Long> siteNameAndACPriceIdMap = new HashMap<>();
                        Map<String, Long> siteNameAndDCPriceIdMap = new HashMap<>();
                        Map<String, Long> siteNameAndPriceIdMap2 = new HashMap<>();
                        Map<String, Long> siteNameAndACPriceIdMap2 = new HashMap<>();
                        Map<String, Long> siteNameAndDCPriceIdMap2 = new HashMap<>();
                        if (CollectionUtils.isNotEmpty(targetInfoList)) {
                            targetInfoList.forEach(targetInfo -> {
                                priceTemplateInfo.put(targetInfo.getName(), new HashMap<>());
                                if (SupplyType.AC.equals(targetInfo.getTemplateType())) {
                                    targetSiteNameAndACPriceIdMap.put(targetInfo.getName(),
                                        targetInfo.getPriceSchemeId());
                                }
                                if (SupplyType.DC.equals(targetInfo.getTemplateType())) {
                                    targetSiteNameAndDCPriceIdMap.put(targetInfo.getName(),
                                        targetInfo.getPriceSchemeId());
                                }
                                if (SupplyType.BOTH.equals(targetInfo.getTemplateType())) {
                                    if (targetSiteNameAndDCPriceIdMap.containsKey(
                                        targetInfo.getName())) {
                                        targetSiteNameAndACPriceIdMap.put(targetInfo.getName(),
                                            targetInfo.getPriceSchemeId());
                                    } else {
                                        targetSiteNameAndDCPriceIdMap.put(targetInfo.getName(),
                                            targetInfo.getPriceSchemeId());
                                    }
                                }
                            });
                        }

                        if (targetSiteNameAndDCPriceIdMap.equals(
                            targetSiteNameAndACPriceIdMap)) {
                            // 如果ac和dc拿到的计费模版一致，认为是整站
                            targetSiteNameAndAllPriceIdMap.putAll(
                                targetSiteNameAndDCPriceIdMap);
                            targetSiteNameAndDCPriceIdMap.clear();
                            targetSiteNameAndACPriceIdMap.clear();
                        } else if (targetSiteNameAndDCPriceIdMap.isEmpty()
                            || targetSiteNameAndACPriceIdMap.isEmpty()) {
                            // 不一致，ac和dc有任一个为空，也可以认为是整站
                            targetSiteNameAndAllPriceIdMap.putAll(
                                targetSiteNameAndDCPriceIdMap);
                            targetSiteNameAndAllPriceIdMap.putAll(
                                targetSiteNameAndACPriceIdMap);
                            targetSiteNameAndDCPriceIdMap.clear();
                            targetSiteNameAndACPriceIdMap.clear();
                        }

                        if (CollectionUtils.isNotEmpty(smartPriceList)) {
                            smartPriceList.forEach(targetInfo -> {
                                if (SupplyType.AC.equals(targetInfo.getTemplateType())) {
                                    smartSiteNameAndACPriceIdMap.put(targetInfo.getName(),
                                        targetInfo.getPriceSchemeId());
                                }
                                if (SupplyType.DC.equals(targetInfo.getTemplateType())) {
                                    smartSiteNameAndDCPriceIdMap.put(targetInfo.getName(),
                                        targetInfo.getPriceSchemeId());
                                }
                                if (SupplyType.BOTH.equals(targetInfo.getTemplateType())) {
                                    if (smartSiteNameAndDCPriceIdMap.containsKey(
                                        targetInfo.getName())) {
                                        smartSiteNameAndACPriceIdMap.put(targetInfo.getName(),
                                            targetInfo.getPriceSchemeId());
                                    } else {
                                        smartSiteNameAndDCPriceIdMap.put(targetInfo.getName(),
                                            targetInfo.getPriceSchemeId());
                                    }
                                }
                            });
                        }

                        if (CollectionUtils.isNotEmpty(smartPriceList2)) {
                            smartPriceList2.forEach(targetInfo -> {
                                if (SupplyType.AC.equals(targetInfo.getTemplateType())) {
                                    smartSiteNameAndACPriceIdMap2.put(targetInfo.getName(),
                                        targetInfo.getPriceSchemeId());
                                }
                                if (SupplyType.DC.equals(targetInfo.getTemplateType())) {
                                    smartSiteNameAndDCPriceIdMap2.put(targetInfo.getName(),
                                        targetInfo.getPriceSchemeId());
                                }
                                if (SupplyType.BOTH.equals(targetInfo.getTemplateType())) {
                                    if (smartSiteNameAndDCPriceIdMap2.containsKey(
                                        targetInfo.getName())) {
                                        smartSiteNameAndACPriceIdMap2.put(targetInfo.getName(),
                                            targetInfo.getPriceSchemeId());
                                    } else {
                                        smartSiteNameAndDCPriceIdMap2.put(targetInfo.getName(),
                                            targetInfo.getPriceSchemeId());
                                    }
                                }
                            });
                        }

                        // 处理非智能的，计费模版
                        if (formInfo.get("priceSchemeId") != null) {
                            targetInfoList.forEach(targetInfo -> {
                                siteNameAndPriceIdMap.put(targetInfo.getName(),
                                    Long.valueOf(String.valueOf(formInfo.get("priceSchemeId"))));
                            });
                        }
                        if (formInfo.get("acPriceSchemeId") != null) {
                            targetInfoList.forEach(targetInfo -> {
                                siteNameAndACPriceIdMap.put(targetInfo.getName(),
                                    Long.valueOf(String.valueOf(formInfo.get("acPriceSchemeId"))));
                            });
                        }
                        if (formInfo.get("dcPriceSchemeId") != null) {
                            targetInfoList.forEach(targetInfo -> {
                                siteNameAndDCPriceIdMap.put(targetInfo.getName(),
                                    Long.valueOf(String.valueOf(formInfo.get("dcPriceSchemeId"))));
                            });
                        }
                        if (formInfo.get("priceSchemeId2") != null) {
                            targetInfoList.forEach(targetInfo -> {
                                siteNameAndPriceIdMap2.put(targetInfo.getName(),
                                    Long.valueOf(String.valueOf(formInfo.get("priceSchemeId2"))));
                            });
                        }
                        if (formInfo.get("acPriceSchemeId2") != null) {
                            targetInfoList.forEach(targetInfo -> {
                                siteNameAndACPriceIdMap2.put(targetInfo.getName(),
                                    Long.valueOf(String.valueOf(formInfo.get("acPriceSchemeId2"))));
                            });
                        }
                        if (formInfo.get("dcPriceSchemeId2") != null) {
                            targetInfoList.forEach(targetInfo -> {
                                siteNameAndDCPriceIdMap2.put(targetInfo.getName(),
                                    Long.valueOf(String.valueOf(formInfo.get("dcPriceSchemeId2"))));
                            });
                        }

                        // 处理一轮普通，二轮智能的情况
                        // 只有一个模版，此时处理二轮默认都是整站，给siteNameAndPriceIdMap2里写
                        if (smartInfo2PriceId != null && smartInfo2PriceId > 0L) {
                            targetInfoList.forEach(targetInfo -> {
                                siteNameAndPriceIdMap2.put(targetInfo.getName(), smartInfo2PriceId);
                            });
                        }
                        // 一轮和二轮都有两个模版，按照AC和DC单独填入
                        if (smartInfo2ACPriceId != null && smartInfo2ACPriceId > 0L) {
                            targetInfoList.forEach(targetInfo -> {
                                siteNameAndACPriceIdMap2.put(targetInfo.getName(),
                                    smartInfo2ACPriceId);
                            });
                        }
                        if (smartInfo2DCPriceId != null && smartInfo2DCPriceId > 0L) {
                            targetInfoList.forEach(targetInfo -> {
                                siteNameAndDCPriceIdMap2.put(targetInfo.getName(),
                                    smartInfo2DCPriceId);
                            });
                        }

                        // 处理好了所有的场站列表，接下来填充调整前和调整后的计费信息
                        //中间层，智能策略：smartPriceScheme-一轮调整后,smartPriceScheme2-二轮调整后
                        // 普通计费模版：priceTemplate-一轮调整后,priceTemplate2-二轮调整后
                        // targetPriceScheme-调整前
                        LinkedHashMap<String, Map<String, PriceTemplatePo>> siteAndTargetInfoMap = new LinkedHashMap<>();
                        //内层key是DC、AC or ALL，标识交直流或者整体
                        Map<String, PriceTemplatePo> smartInfoMap = new HashMap<>();
                        Map<String, PriceTemplatePo> smartInfoMap2 = new HashMap<>();
                        Map<String, PriceTemplatePo> targetInfoMap = new HashMap<>();
                        Map<String, PriceTemplatePo> priceTemplateMap = new HashMap<>();
                        Map<String, PriceTemplatePo> priceTemplateMap2 = new HashMap<>();
                        priceTemplateInfo.keySet().forEach(siteName -> {
                            Long targetACPriceId = targetSiteNameAndACPriceIdMap.get(siteName);
                            Long targetDCPriceId = targetSiteNameAndDCPriceIdMap.get(siteName);
                            Long targetAllPriceId = targetSiteNameAndAllPriceIdMap.get(siteName);

                            Long smartACPriceId = smartSiteNameAndACPriceIdMap.get(siteName);
                            Long smartDCPriceId = smartSiteNameAndDCPriceIdMap.get(siteName);
                            Long smartAllPriceId = smartSiteNameAndAllPriceIdMap.get(siteName);
                            Long smartACPriceId2 = smartSiteNameAndACPriceIdMap2.get(siteName);
                            Long smartDCPriceId2 = smartSiteNameAndDCPriceIdMap2.get(siteName);
                            Long smartAllPriceId2 = smartSiteNameAndAllPriceIdMap2.get(siteName);

                            Long priceTemplateId = siteNameAndPriceIdMap.get(siteName);
                            Long priceACTemplateId = siteNameAndACPriceIdMap.get(siteName);
                            Long priceDCTemplateId = siteNameAndDCPriceIdMap.get(siteName);
                            Long priceTemplateId2 = siteNameAndPriceIdMap2.get(siteName);
                            Long priceACTemplateId2 = siteNameAndACPriceIdMap2.get(siteName);
                            Long priceDCTemplateId2 = siteNameAndDCPriceIdMap2.get(siteName);

                            // 调整前
                            Optional<PriceTemplatePo> targetACPriceTemplate = Optional.ofNullable(
                                    targetACPriceId)
                                .flatMap(id -> t7.stream()
                                    .filter(
                                        priceTemplatePo -> priceTemplatePo.getId()
                                            .equals(targetACPriceId))
                                    .findFirst());
                            Optional<PriceTemplatePo> targetDCPriceTemplate = Optional.ofNullable(
                                    targetDCPriceId)
                                .flatMap(id -> t7.stream()
                                    .filter(
                                        priceTemplatePo -> priceTemplatePo.getId()
                                            .equals(targetDCPriceId))
                                    .findFirst());
                            Optional<PriceTemplatePo> targetAllPriceTemplate = Optional.ofNullable(
                                    targetAllPriceId)
                                .flatMap(id -> t7.stream()
                                    .filter(
                                        priceTemplatePo -> priceTemplatePo.getId()
                                            .equals(targetAllPriceId))
                                    .findFirst());

                            // 智能策略一二轮调整后
                            Optional<PriceTemplatePo> smartPriceACTemplate = Optional.ofNullable(
                                    smartACPriceId)
                                .flatMap(id -> t7.stream()
                                    .filter(
                                        priceTemplatePo -> priceTemplatePo.getId()
                                            .equals(smartACPriceId))
                                    .findFirst());
                            Optional<PriceTemplatePo> smartPriceDCTemplate = Optional.ofNullable(
                                    smartDCPriceId)
                                .flatMap(id -> t7.stream()
                                    .filter(
                                        priceTemplatePo -> priceTemplatePo.getId()
                                            .equals(smartDCPriceId))
                                    .findFirst());
                            Optional<PriceTemplatePo> smartPriceALLTemplate = Optional.ofNullable(
                                    smartAllPriceId)
                                .flatMap(id -> t7.stream()
                                    .filter(
                                        priceTemplatePo -> priceTemplatePo.getId()
                                            .equals(smartAllPriceId))
                                    .findFirst());
                            Optional<PriceTemplatePo> smartPriceACTemplate2 = Optional.ofNullable(
                                    smartACPriceId2)
                                .flatMap(id -> t7.stream()
                                    .filter(
                                        priceTemplatePo -> priceTemplatePo.getId()
                                            .equals(smartACPriceId2))
                                    .findFirst());
                            Optional<PriceTemplatePo> smartPriceDCTemplate2 = Optional.ofNullable(
                                    smartDCPriceId2)
                                .flatMap(id -> t7.stream()
                                    .filter(
                                        priceTemplatePo -> priceTemplatePo.getId()
                                            .equals(smartDCPriceId2))
                                    .findFirst());
                            Optional<PriceTemplatePo> smartPriceALLTemplate2 = Optional.ofNullable(
                                    smartAllPriceId2)
                                .flatMap(id -> t7.stream()
                                    .filter(
                                        priceTemplatePo -> priceTemplatePo.getId()
                                            .equals(smartAllPriceId2))
                                    .findFirst());

                            // 普通计费模版一二轮调整后
                            Optional<PriceTemplatePo> priceACTemplate = Optional.ofNullable(
                                    priceACTemplateId)
                                .flatMap(id -> t7.stream()
                                    .filter(
                                        priceTemplatePo -> priceTemplatePo.getId()
                                            .equals(priceACTemplateId))
                                    .findFirst());
                            Optional<PriceTemplatePo> priceDCTemplate = Optional.ofNullable(
                                    priceDCTemplateId)
                                .flatMap(id -> t7.stream()
                                    .filter(
                                        priceTemplatePo -> priceTemplatePo.getId()
                                            .equals(priceDCTemplateId))
                                    .findFirst());
                            Optional<PriceTemplatePo> priceALLTemplate = Optional.ofNullable(
                                    priceTemplateId)
                                .flatMap(id -> t7.stream()
                                    .filter(
                                        priceTemplatePo -> priceTemplatePo.getId()
                                            .equals(priceTemplateId))
                                    .findFirst());
                            Optional<PriceTemplatePo> priceACTemplate2 = Optional.ofNullable(
                                    priceACTemplateId2)
                                .flatMap(id -> t7.stream()
                                    .filter(
                                        priceTemplatePo -> priceTemplatePo.getId()
                                            .equals(priceACTemplateId2))
                                    .findFirst());
                            Optional<PriceTemplatePo> priceDCTemplate2 = Optional.ofNullable(
                                    priceDCTemplateId2)
                                .flatMap(id -> t7.stream()
                                    .filter(
                                        priceTemplatePo -> priceTemplatePo.getId()
                                            .equals(priceDCTemplateId2))
                                    .findFirst());
                            Optional<PriceTemplatePo> priceALLTemplate2 = Optional.ofNullable(
                                    priceTemplateId2)
                                .flatMap(id -> t7.stream()
                                    .filter(
                                        priceTemplatePo -> priceTemplatePo.getId()
                                            .equals(priceTemplateId2))
                                    .findFirst());

                            if (targetACPriceTemplate != null
                                && targetACPriceTemplate.isPresent()) {
                                targetInfoMap.put("AC", targetACPriceTemplate.get());
                            }
                            if (targetDCPriceTemplate != null
                                && targetDCPriceTemplate.isPresent()) {
                                targetInfoMap.put("DC", targetDCPriceTemplate.get());
                            }
                            if (targetAllPriceTemplate != null
                                && targetAllPriceTemplate.isPresent()) {
                                targetInfoMap.put("ALL", targetAllPriceTemplate.get());
                            }

                            if (smartPriceACTemplate != null
                                && smartPriceACTemplate.isPresent()) {
                                smartInfoMap.put("AC", smartPriceACTemplate.get());
                            }
                            if (smartPriceDCTemplate != null
                                && smartPriceDCTemplate.isPresent()) {
                                smartInfoMap.put("DC", smartPriceDCTemplate.get());
                            }
                            if (smartPriceALLTemplate != null
                                && smartPriceALLTemplate.isPresent()) {
                                smartInfoMap.put("ALL", smartPriceALLTemplate.get());
                            }
                            if (priceACTemplate != null
                                && priceACTemplate.isPresent()) {
                                priceTemplateMap.put("AC", priceACTemplate.get());
                            }
                            if (priceDCTemplate != null
                                && priceDCTemplate.isPresent()) {
                                priceTemplateMap.put("DC", priceDCTemplate.get());
                            }
                            if (priceALLTemplate != null
                                && priceALLTemplate.isPresent()) {
                                priceTemplateMap.put("ALL", priceALLTemplate.get());
                            }

                            if (smartPriceACTemplate2 != null
                                && smartPriceACTemplate2.isPresent()) {
                                smartInfoMap2.put("AC", smartPriceACTemplate2.get());
                            }
                            if (smartPriceDCTemplate2 != null
                                && smartPriceDCTemplate2.isPresent()) {
                                smartInfoMap2.put("DC", smartPriceDCTemplate2.get());
                            }
                            if (smartPriceALLTemplate2 != null
                                && smartPriceALLTemplate2.isPresent()) {
                                smartInfoMap2.put("ALL", smartPriceALLTemplate2.get());
                            }
                            if (priceACTemplate2 != null
                                && priceACTemplate2.isPresent()) {
                                priceTemplateMap2.put("AC", priceACTemplate2.get());
                            }
                            if (priceDCTemplate2 != null
                                && priceDCTemplate2.isPresent()) {
                                priceTemplateMap2.put("DC", priceDCTemplate2.get());
                            }
                            if (priceALLTemplate2 != null
                                && priceALLTemplate2.isPresent()) {
                                priceTemplateMap2.put("ALL", priceALLTemplate2.get());
                            }

                            siteAndTargetInfoMap.put("targetPriceScheme", targetInfoMap);
                            if (!smartInfoMap.isEmpty()) {
                                siteAndTargetInfoMap.put("smartPriceScheme", smartInfoMap);
                            }
                            if (!smartInfoMap2.isEmpty()) {
                                siteAndTargetInfoMap.put("smartPriceScheme2", smartInfoMap2);
                            }
                            if (!priceTemplateMap.isEmpty()) {
                                siteAndTargetInfoMap.put("priceTemplate", priceTemplateMap);
                            }
                            if (!priceTemplateMap2.isEmpty()) {
                                siteAndTargetInfoMap.put("priceTemplate2", priceTemplateMap2);
                            }
                            priceTemplateInfo.put(siteName, siteAndTargetInfoMap);
                        });

                        if (!priceTemplateInfo.isEmpty()) {
                            tmpFormInfo.put("priceTemplateInfo", priceTemplateInfo);
                        }

                        result.setFormData(tmpFormInfo);
                        log.info("电价下发流程数据：{}", result);
                        return result;
                    });
            case OaConstants.PD_KEY_RENT_PAYMENT:
                if (formInfo.containsKey("站点名称")) {
                    formInfo.put("feeSettDate", formInfo.get("租金结算日期"));
                    formInfo.put("payCompanyName", formInfo.get("付款公司名称"));
                    formInfo.put("supplierName", formInfo.get("供应商名称"));
                    formInfo.put("bankName", formInfo.get("开户行"));
                    formInfo.put("bankAccount", formInfo.get("账号"));
                    formInfo.put("uploadInvoice", formInfo.get("发票是否上传"));
                    formInfo.put("payAmount",
                        new BigDecimal(formInfo.getOrDefault("租金金额", "0").toString()));
                }

                formInfo.put("feeSettDate",
                    JsonUtils.fromJson(formInfo.get("feeSettDate").toString(), ArrayList.class));
                result.setFormData(formInfo);

                // 场站名称
                Object siteId = formInfo.get("站点名称");
                if (null == siteId) {
                    siteId = formInfo.get("siteId");
                }

                // 账期
                LocalDate month;
                if (null != val.getEndTime()) {
                    month = val.getCreateTime().toInstant().atZone(ZoneId.systemDefault())
                        .toLocalDate();
                } else {
                    Object billDate = formInfo.get("租金结算日期");
                    if (null == billDate) {
                        billDate = formInfo.get("feeSettDate");
                    }

                    if (billDate instanceof ArrayList) {
                        month = LocalDateTime.parse((String) ((ArrayList) billDate).get(1),
                                FORMAT_yyyy_MM_dd_HH_mm_ss)
                            .toLocalDate();
                    } else {
                        ArrayList rangeList = JsonUtils.fromJson(billDate.toString(),
                            ArrayList.class);
                        if (rangeList.get(1).toString().length() > 10) {
                            month = LocalDateTime.parse(
                                    rangeList.get(1).toString(),
                                    FORMAT_yyyy_MM_dd_HH_mm_ss)
                                .toLocalDate();
                        } else {
                            month = LocalDate.parse(
                                rangeList.get(1).toString(),
                                FORMAT_yyyy_MM_dd);
                        }
                    }
                }

                return this.oaReference(result, siteId.toString(), month);
            case OaConstants.PD_KEY_INCOME_SITE_OA:
                // 场站名称
                IncomeSiteFormDto formX = JsonUtils.fromJson(
                    JsonUtils.toJsonString(formInfo), IncomeSiteFormDto.class);

                // 发票相关信息
                if (null != formX.getInvoiceSellerInfo()) {
                    final String invoicedTempSalName = invoicedTemplateSalDs.getNameById(
                        formX.getInvoiceSellerInfo().getInvoiceTmpSalId());
//                    final String invoicedTempSalRefName = invoicedSalTempRefDs.getNameById(
//                        formX.getInvoiceSellerInfo().getInvoiceSalTmpRefId());
                    formInfo.put("invoicedTempSalName", invoicedTempSalName); // 发票主体名称
//                    formInfo.put("invoicedTempSalRefName", invoicedTempSalRefName); // 商品行名称
                }
                result.setFormData(formInfo);

                siteId = null;
                if (CollectionUtils.isNotEmpty(formX.getSiteIdList())) {
                    if (formX.getSiteIdList().size() == 1) {
                        siteId = formX.getSiteIdList().get(0);
                    }
                } else {
                    siteId = formX.getSiteId();
                }

                if (null == siteId) {
                    result.setShowLossRate(false);
                    result.setHistoryLedgerList(null);
                    return Mono.just(result);
                }

                return this.oaReference(result, siteId.toString(),
                    formX.getBillDateRange().getEndTime()
                        .toInstant().atZone(ZoneOffset.systemDefault())
                        .toLocalDate());
            case OaConstants.PD_KEY_EXPENSE_SITE_OA:
                result.setFormData(formInfo);

                // 场站名称
                ExpenseSiteFormDto form = JsonUtils.fromJson(
                    JsonUtils.toJsonString(formInfo), ExpenseSiteFormDto.class);

                siteId = null;
                if (CollectionUtils.isNotEmpty(form.getSiteIdList())) {
                    if (form.getSiteIdList().size() == 1) {
                        siteId = form.getSiteIdList().get(0);
                    }
                } else {
                    siteId = form.getSiteId();
                }

                if (null == siteId) {
                    result.setShowLossRate(false);
                    result.setHistoryLedgerList(null);
                    return Mono.just(result);
                }

                return this.oaReference(result, siteId.toString(),
                    form.getBillDateRange().getEndTime()
                        .toInstant().atZone(ZoneOffset.systemDefault())
                        .toLocalDate());
            case OaConstants.PD_KEY_OTHER_FEE_PAY_APPLY:
                formInfo.put("feeSettDate",
                    JsonUtils.fromJson(formInfo.get("feeSettDate").toString(), ArrayList.class));
                result.setFormData(formInfo);

                // 场站名称
                siteId = formInfo.get("siteId");

                // 账期
//                LocalDate month;
                Object billDate = formInfo.get("feeSettDate");
                if (billDate instanceof ArrayList) {
                    month = LocalDateTime.parse((String) ((ArrayList) billDate).get(1),
                            FORMAT_yyyy_MM_dd_HH_mm_ss)
                        .toLocalDate();
                } else {
                    month = LocalDate.parse(
                        JsonUtils.fromJson(billDate.toString(), ArrayList.class)
                            .get(1).toString(),
                        FORMAT_yyyy_MM_dd);
                }

                return this.oaReference(result, siteId.toString(), month);
            case OaConstants.PD_ELEC_PAY:
                // 历史台账: /ant/api/siteBi/sixMonthIncomeExpense
//                Map formInfo = val.getFormInfo();

                if (formInfo.containsKey("statistics")) {
                    formInfo.put("statistics",
                        JsonUtils.fromJson(formInfo.get("statistics").toString(),
                            OaStatisticsInfo.class));
                    formInfo.put("billElec", new BigDecimal(formInfo.get("billElec").toString()));
                    formInfo.put("billAmount",
                        new BigDecimal(formInfo.get("billAmount").toString()));
                    result.setFormData(formInfo);
                } else { // 旧版数据兼容 👇
                    formInfo.put("billDate",
                        JsonUtils.fromJson(formInfo.get("账期日期").toString(), ArrayList.class));
                    formInfo.put("meterAccount", formInfo.get("电表户号"));
                    formInfo.put("autoDebit", formInfo.get("自动扣款"));
                    formInfo.put("payCompanyName", formInfo.get("付款公司名称"));
                    formInfo.put("supplierName", formInfo.get("供应商名称"));
                    formInfo.put("bankName", formInfo.get("开户行"));
                    formInfo.put("bankAccount", formInfo.get("账号"));
                    formInfo.put("billElec", formInfo.get("电费单电量"));
                    formInfo.put("billAmount", formInfo.get("电费单金额"));
                    formInfo.put("payTimeFinal", formInfo.get("最后付款日期"));

                    // 平台数据统计调整
                    OaStatisticsInfo.PlatformData platform = OaStatisticsInfo.PlatformData
                        .builder(formInfo.get("平台统计").toString());
                    OaStatisticsInfo.DiscountData discount = JsonUtils.fromJson(
                        formInfo.get("电损计算").toString(),
                        OaStatisticsInfo.DiscountData.class);

                    formInfo.put("statistics",
                        new OaStatisticsInfo().setDiscount(discount).setPlatform(platform));
                    result.setFormData(formInfo);
                }

                if (formInfo.get("meterData") == null || formInfo.get("meterData").equals("[]")) {
                    formInfo.put("meterData", null);
                } else {
                    formInfo.put("meterData",
                        JsonUtils.toJsonList(formInfo.get("meterData").toString()));
                }

                // 场站名称
                siteId = formInfo.get("站点名称");
                if (null == siteId) {
                    siteId = formInfo.get("siteId");
                }

                // 账期
//                LocalDate month;
                if (null != val.getEndTime()) {
                    month = val.getCreateTime().toInstant().atZone(ZoneId.systemDefault())
                        .toLocalDate();
                } else {
                    billDate = formInfo.get("账期日期");
                    if (null == billDate) {
                        billDate = formInfo.get("billDate");
                    }

                    if (billDate instanceof ArrayList) {
                        month = LocalDateTime.parse((String) ((ArrayList) billDate).get(1),
                                FORMAT_yyyy_MM_dd_HH_mm_ss)
                            .toLocalDate();
                    } else {
                        String date = JsonUtils.fromJson(billDate.toString(), ArrayList.class)
                            .get(0).toString();
                        if (date.contains(" ")) {
                            month = LocalDateTime.parse(date, FORMAT_yyyy_MM_dd_HH_mm_ss)
                                .toLocalDate();
                        } else {
                            month = LocalDate.parse(date, FORMAT_yyyy_MM_dd);
                        }
                    }
                }

                return this.oaReference(result, siteId.toString(), month);
            default:
                throw new DcServiceException("无效流程");
        }
    }

    private Mono<OaPDFModelVo> oaReference(OaPDFModelVo result, String siteId, LocalDate month) {
        return Mono.zip(
                biSiteOrderFeignClient.siteSixMonthIncomeExpense(siteId, month)
                    .doOnNext(FeignResponseValidate::check)
                    .map(ListResponse::getData),
                dataCoreClient.getOaDefaultValue(siteId, result.getOaDefKey())
                    .doOnNext(FeignResponseValidate::checkIgnoreData))
            .map(x -> {
                List<SiteIncomeExpenseVo> list = x.getT1();
                ObjectResponse<SiteOaDefaultConfigPo> oaSetting = x.getT2();

                result.setShowLossRate(false);
                if (null != oaSetting.getData() &&
                    CollectionUtils.isNotEmpty(oaSetting.getData().getDefaultValue())) {
                    Optional<SiteOaDefaultConfigItem> first = oaSetting.getData()
                        .getDefaultValue().stream().filter(s ->
                            null != s.getLabel() && "电损显示".equals(s.getLabel()))
                        .findFirst();
                    if (first.isPresent() &&
                        StringUtils.isNotBlank(first.get().getValue())) {
                        result.setShowLossRate(
                            Boolean.parseBoolean(first.get().getValue()));
                    }
                }
                result.setHistoryLedgerList(list);
                return result;
            });
    }

    public Mono<ObjectResponse<BalanceApplicationVo>> getBalanceApplyById(Long id) {
        return userFeignClient.getBalanceApplyById(id)
            .doOnNext(FeignResponseValidate::check)
            .flatMap(x -> {
                if (null != x.getData().getApplierId()) {
                    return authSysUserFeignClient.getSysUserByIdList(
                            List.of(x.getData().getApplierId()))
                        .doOnNext(FeignResponseValidate::check)
                        .map(sysUser -> {
                            x.getData().setApplierName(sysUser.getData().get(0).getName());
                            return x;
                        });
                }
                return Mono.just(x);
            })
            .flatMap(x -> {
                if (PayAccountType.CORP.equals(x.getData().getAccountType())) {
                    // 企业客户，获取摘要
                    final Long accountCode = x.getData().getAccountCode();
                    final Long userId = x.getData().getUserId();
                    final CommercialSimpleVo commById = commercialQueryDs.getCommById(accountCode);
                    if (commById != null) {
                        final String idChain = commById.getIdChain();
                        final RCorpPo corpPo = rCorpRoDs.getByUidAndIdChain(userId,
                            idChain);
                        if (corpPo != null) {
                            return authSysUserFeignClient.getCorp(corpPo.getId())
                                .doOnNext(FeignResponseValidate::check)
                                .map(corpUser -> {
                                    if (StringUtils.isNotEmpty(corpUser.getData().getDigest())) {
                                        x.getData().setCorpDigest(corpUser.getData().getDigest());
                                    }
                                    return x;
                                });
                        }
                    }
                }
                return Mono.just(x);
            })
            .flatMap(x -> {
                if (null != x.getData().getRefundOrderId()) {
                    return deviceDataCoreClient.tkView(x.getData().getRefundOrderId())
                        .doOnNext(FeignResponseValidate::check)
                        .map(payBillVoObjectResponse -> {
                            if (null != payBillVoObjectResponse.getData().getPayTime()) {
                                x.getData()
                                    .setPayTime(payBillVoObjectResponse.getData().getPayTime())
                                    .setPayChannel(
                                        payBillVoObjectResponse.getData().getPayChannel());
                            }
                            return x;
                        });
                }
                return Mono.just(x);
            });
    }


    public void exportOaList(ListTaskParam param, ExcelPosition position) throws IOException {
        log.info("OA列表导出: {}", JsonUtils.toJsonString(param));
        ExcelUtil.builder(exportFileConfig.getExcelDir(), position,
                BiExportGroups.OA_LIST.getName())
            .addHeader(ExportOaVo.class)
            .loopAppendData((start, size) -> {
                param.setStart((long) ((start - 1) * size))
                    .setSize(size);
                param.setTotal(Boolean.FALSE);

                ListResponse<OaTaskVo> all = this.oaRechargeClient.getAssigneeTasks(param)
                    .block(Duration.ofSeconds(50L));
                List<ExportOaVo> list = new ArrayList<>();
                if (all != null && CollectionUtils.isNotEmpty(all.getData())) {
                    list = all.getData().stream().map(e -> {
                        ExportOaVo exportOaVo = new ExportOaVo();
                        exportOaVo.setProcessInstanceId(e.getProcessInstanceId().substring(0, 8))
                            .setOaName(e.getOaName())
                            .setSummary(this.getSummary(e));

                        return exportOaVo;
                    }).collect(Collectors.toList());
                }
                return new ArrayList<>(list);
            }, null)
            .write2File();
    }

    public String getSummary(OaTaskVo data) {
        String summary = null;
        switch (data.getOaKey()) {
            case "recharge_process":
                break;
            case "charge_fee":
                break;
            case "elec_payment_apply":
                break;
            case "pay_elec_fee":
                break;
        }
        return summary;
    }
}