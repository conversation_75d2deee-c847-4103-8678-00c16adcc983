<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.bi.mapper.CommercialQueryMapper">

    <select id="getCommerial" resultType="com.cdz360.biz.model.merchant.vo.CommercialSimpleVo">
        select
        id as id,
        pid as pid,
        topCommId,
        comm_level as commLevel,
        merchants as merchants,
        comm_type as commType,
        comm_name as commName,
        short_name as shortName,
        phone,
        idChain
        from t_r_commercial
        where id=#{id}
    </select>


    <!--    <select id="listSubCommercialIds" resultType="java.lang.Long">-->
    <!--        select l3.id from t_r_commercial as l3 where l3.status = 1 and l3.pid in (-->
    <!--            select l2.id from t_r_commercial as l2 where l2.status = 1 and l2.pid = #{id}-->
    <!--        )-->
    <!--        union-->
    <!--        select l2.id from t_r_commercial as l2 where l2.status = 1 and l2.pid = #{id}-->
    <!--    </select>-->

    <select id="getCommercialSimpleVoList" resultType="com.cdz360.biz.model.merchant.vo.CommercialSimpleVo">
        select
        id as id,
        pid as pid,
        topCommId,
        comm_level as commLevel,
        merchants as merchants,
        comm_type as commType,
        comm_name as commName,
        short_name as shortName,
        phone,
        idChain
        from t_r_commercial
    </select>

    <select id="getCommByPid" resultType="com.cdz360.biz.model.merchant.vo.CommercialSimpleVo">
        select
        id as id,
        pid as pid,
        topCommId,
        comm_level as commLevel,
        merchants as merchants,
        comm_type as commType,
        comm_name as commName,
        short_name as shortName,
        phone,
        idChain
        from t_r_commercial
        where pid=#{commId}
        and status = 1
    </select>

    <select id="getCommerialsByCommIds" parameterType="java.util.List"
            resultType="com.cdz360.biz.model.merchant.vo.CommercialSimpleVo">
        select
        id as id,
        pid as pid,
        topCommId,
        comm_level as commLevel,
        merchants as merchants,
        comm_type as commType,
        comm_name as commName,
        short_name as shortName,
        idChain
        from t_r_commercial
        where id in
        <foreach item="item" index="index" collection="commIds"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>