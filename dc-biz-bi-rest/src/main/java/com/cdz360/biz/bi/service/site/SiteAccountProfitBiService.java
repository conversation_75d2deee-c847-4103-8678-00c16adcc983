package com.cdz360.biz.bi.service.site;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.model.corp.type.CorpType;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.biz.ds.trading.ro.comm.ds.CommercialRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.OrderBiRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.BiSiteOrderAccountRoDs;
import com.cdz360.biz.model.cus.corp.param.ListCorpParam;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.corp.vo.CorpVo;
import com.cdz360.biz.model.cus.settlement.type.SettlementStatusEnum;
import com.cdz360.biz.model.cus.settlement.vo.SettlementVo;
import com.cdz360.biz.model.merchant.dto.CommercialDto;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import com.cdz360.biz.model.settle.type.SettAccountType;
import com.cdz360.biz.model.trading.bi.dto.SiteAccountProfitDto;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.model.trading.site.vo.SiteIncomeVo;
import com.cdz360.biz.model.trading.site.vo.SiteMonthElecVo;
import com.chargerlinkcar.framework.common.feign.CorpFeignClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 按账户类型计算场站运营收入
 */
@Slf4j
@Service
public class SiteAccountProfitBiService {

    @Autowired
    private BiSiteOrderAccountRoDs biSiteOrderAccountRoDs;

    @Autowired
    private CommercialRoDs commercialRoDs;

    @Autowired
    private ChargerOrderRoDs chargerOrderRoDs;


    @Autowired
    private OrderBiRoDs orderBiRoDs;

    @Autowired
    private CorpFeignClient corpFeignClient;

    public List<SiteAccountProfitDto> getSiteAccountProfit(SitePo site, int year, int month,
                                                           SettAccountType accType,
                                                           @Nullable CorpPo corp,
                                                           @Nullable CommercialDto comm) {
        log.info("site = {} {}, month = {}-{}, accType = {}, corp = {}, comm = {}",
                site.getId(), site.getSiteName(),
                year, month, accType,
                corp == null ? "" : corp.getCorpName(),
                comm == null ? "" : comm.getCommName());
        LocalDate ym = LocalDate.of(year, month, 1);
        TimeFilter timeFilter = new TimeFilter();
        timeFilter.setStartTime(Date.from(ym.atStartOfDay(ZoneId.systemDefault()).toInstant()));
        timeFilter.setEndTime(Date.from(ym.plusMonths(1).atStartOfDay(ZoneId.systemDefault()).toInstant()));
        SiteAccountProfitDto profit = null;
        List<SiteAccountProfitDto> result = new ArrayList<>();
        if (SettAccountType.PERSONAL == accType) {
            profit = getSitePersonalProfit(site, timeFilter);
        } else if (SettAccountType.PREPAY == accType) {
            profit = getSitePrepayProfit(site, timeFilter);
        } else if (SettAccountType.HLHT_CORP == accType) {
            result = getSiteHlhtProfit(site, timeFilter);
        } else if (SettAccountType.NORMAL_CORP == accType) {
            result = getSiteCorpProfit(site, timeFilter);
        } else if (SettAccountType.COMMERCIAL == accType) {
            result = getSiteCommProfit(site, timeFilter);
        } else if (SettAccountType.UNKNOWN == accType) {
            profit = getSiteNoPayIncome(site, timeFilter);
        } else {
            log.error("不识别的账户类型 accType = {}", accType);
            return result;
        }
        if (profit != null) {
            result.add(profit);
        }
        result.stream().forEach(p -> p.setSiteId(site.getId()).setMonth(ym));
        return result;
    }

    public List<SiteAccountProfitDto> getSitePostPayCorpIncome(SettlementVo sett) {
        // 使用帐单号，按场站、分月统计电量
        List<SiteMonthElecVo> siteElecList = this.orderBiRoDs.getSiteMonthElecBiByBillNo(sett.getBillNo());
        if (CollectionUtils.isEmpty(siteElecList)) {
            return List.of();
        }
        List<SiteAccountProfitDto> result = new ArrayList<>();

//        BigDecimal elec = sett.getOrderKwh();
//        BigDecimal elecOriginFee = sett.getSettlementElecFee();
        BigDecimal elecCostFee = sett.getSettlementElecFee();
        BigDecimal servCostFee = sett.getSettlementServFee();
        BigDecimal orderElecProfit = sett.getOrderElecProfit();
        BigDecimal orderServProfit = sett.getOrderServProfit();
        for (int i = 0; i < siteElecList.size() - 1; i++) {
            SiteMonthElecVo se = siteElecList.get(i);
            SiteAccountProfitDto sa = new SiteAccountProfitDto();
            sa.setSiteId(se.getSiteId())
                    .setMonth(LocalDate.of(se.getYear(), se.getMonth(), 1))
                    .setAccountType(PayAccountType.CORP)
                    .setSettlementType(sett.getSettlementType())
                    .setAccountId(sett.getCorpId())
                    .setAccountName(sett.getCorpName())
                    .setBillNo(sett.getBillNo())
                    .setMonth(LocalDate.of(se.getYear(), se.getMonth(), 1))
                    .setElec(se.getElec())
                    .setElecOriginFee(se.getElecOriginFee())
                    .setElecCostFee(se.getElec().multiply(sett.getSettlementElecFee()).divide(sett.getOrderKwh(), 2, RoundingMode.HALF_UP))
                    .setServCostFee(se.getElec().multiply(sett.getSettlementServFee()).divide(sett.getOrderKwh(), 2, RoundingMode.HALF_UP))
                    .setOrderElecProfit(se.getElec().multiply(sett.getOrderElecProfit()).divide(sett.getOrderKwh(), 2, RoundingMode.HALF_UP))
                    .setOrderServProfit(se.getElec().multiply(sett.getOrderServProfit()).divide(sett.getOrderKwh(), 2, RoundingMode.HALF_UP));
            if (SettlementStatusEnum.INIT == sett.getBillStatus()) {
                // 帐单未结算状态，收入记负
                sa.setElecCostFee(BigDecimal.ZERO)
                        .setServCostFee(BigDecimal.ZERO)
                        .setOrderElecProfit(BigDecimal.ZERO)
                        .setOrderElecProfit(BigDecimal.ZERO);
            }
//            elec = subtract(elec, sa.getElec());
//            elecOriginFee = subtract(elecOriginFee, sa.getElecOriginFee());
            elecCostFee = subtract(elecCostFee, sa.getElecCostFee());
            servCostFee = subtract(servCostFee, sa.getServCostFee());
            orderElecProfit = subtract(orderElecProfit, sa.getOrderElecProfit());
            orderServProfit = subtract(orderServProfit, sa.getOrderServProfit());
            result.add(sa);
        }

        if (siteElecList.size() > 0) {
            SiteMonthElecVo lastSe = siteElecList.get(siteElecList.size() - 1);
            SiteAccountProfitDto lastSa = new SiteAccountProfitDto();
            lastSa.setSiteId(lastSe.getSiteId())
                    .setMonth(LocalDate.of(lastSe.getYear(), lastSe.getMonth(), 1))
                    .setAccountType(PayAccountType.CORP)
                    .setSettlementType(sett.getSettlementType())
                    .setAccountId(sett.getCorpId())
                    .setAccountName(sett.getCorpName())
                    .setBillNo(sett.getBillNo())
                    .setMonth(LocalDate.of(lastSe.getYear(), lastSe.getMonth(), 1))
                    .setElec(lastSe.getElec())
                    .setElecOriginFee(lastSe.getElecOriginFee())
                    .setElecCostFee(elecCostFee)
                    .setServCostFee(servCostFee)
                    .setOrderElecProfit(orderElecProfit)
                    .setOrderServProfit(orderServProfit);
            if (SettlementStatusEnum.INIT == sett.getBillStatus()) {
                // 帐单未结算状态，收入记负
                lastSa.setElecCostFee(BigDecimal.ZERO)
                        .setServCostFee(BigDecimal.ZERO)
                        .setOrderElecProfit(BigDecimal.ZERO)
                        .setOrderElecProfit(BigDecimal.ZERO);
            }
            result.add(lastSa);
        }
        return result;
    }

    /**
     * a - b = c， 如果 c < 0, 返回0
     */
    private BigDecimal subtract(BigDecimal a, BigDecimal b) {
        BigDecimal rt = a.subtract(b);
        if (DecimalUtils.ltZero(rt)) {
            rt = BigDecimal.ZERO;
        }
        return rt;
    }

    /**
     * 统计使用平台余额账户充电的收入
     */
    private SiteAccountProfitDto getSitePersonalProfit(SitePo site, TimeFilter payTime) {
        SiteIncomeVo income = this.biSiteOrderAccountRoDs.getSiteIncomeByAccount(site.getId(), payTime, PayAccountType.PERSONAL);
        if (income == null
                || income.getElec() == null
                || DecimalUtils.lteZero(income.getElec())) {
            return null;
        }
        SiteAccountProfitDto result = new SiteAccountProfitDto();
        result.setAccountType(PayAccountType.PERSONAL)
                .setSettlementType(SettlementType.BALANCE)
                .setAccountId(0L)
                .setAccountName("平台余额")
                .setBillNo("")
                .setElec(income.getElec())
                .setElecOriginFee(income.getElecOriginFee())
                .setElecCostFee(income.getElecCostFee())
                .setServCostFee(income.getServCostFee())
                .setOrderElecProfit(income.getElecCostFee())
                .setOrderServProfit(income.getServCostFee());
        return result;
    }

    /**
     * 统计使用即充即退充电的收入
     */
    private SiteAccountProfitDto getSitePrepayProfit(SitePo site, TimeFilter payTime) {

        SiteIncomeVo income = this.biSiteOrderAccountRoDs.getSiteIncomeByAccount(site.getId(), payTime, PayAccountType.PREPAY);
        if (income == null
                || income.getElec() == null
                || DecimalUtils.lteZero(income.getElec())) {
            return null;
        }
        SiteAccountProfitDto result = new SiteAccountProfitDto();
        result.setAccountType(PayAccountType.PREPAY)
                .setSettlementType(SettlementType.BALANCE)
                .setAccountId(0L)
                .setAccountName("即充即退")
                .setBillNo("")
                .setElec(income.getElec())
                .setElecOriginFee(income.getElecOriginFee())
                .setElecCostFee(income.getElecCostFee())
                .setServCostFee(income.getServCostFee())
                .setOrderElecProfit(income.getElecCostFee())
                .setOrderServProfit(income.getServCostFee());
        return result;
    }


    /**
     * 统计使用互联互通充电的收入
     */
    private List<SiteAccountProfitDto> getSiteHlhtProfit(SitePo site, TimeFilter stopTime) {
        List<SiteIncomeVo> incomeList = this.biSiteOrderAccountRoDs.getSiteIncomeByHlht(site.getId(), stopTime);
        return incomeList.stream()
                .map(income -> {
                    CorpPo corp = corpFeignClient.getCorp(income.getAccountId())
                            .getData();
                    SiteAccountProfitDto profit = new SiteAccountProfitDto();
                    profit.setAccountType(PayAccountType.CORP)
                            .setSettlementType(SettlementType.UNKNOWN)
                            .setAccountId(income.getAccountId())
                            .setAccountName(income.getAccountName())
                            .setBillNo("")
                            .setCorpType(CorpType.HLHT)
                            .setElec(income.getElec())
                            .setElecOriginFee(income.getElecOriginFee())
                            .setElecCostFee(income.getElecProfit())
                            .setServCostFee(income.getServProfit())
                            .setOrderElecProfit(income.getElecProfit())
                            .setOrderServProfit(income.getServProfit());
                    if (corp != null) {
                        profit.setAccountName(corp.getCorpName())
                                .setSettlementType(corp.getSettlementType());
                    }
                    return profit;
                }).collect(Collectors.toList());
    }

    /**
     * 统计使用企业账户充电的收入
     */
    private List<SiteAccountProfitDto> getSiteCorpProfit(SitePo site, TimeFilter stopTime) {
        // 根据订单获取充电企业列表
        List<Long> corpIds = chargerOrderRoDs.getCorpIdList(site.getId(), stopTime);
        if (CollectionUtils.isEmpty(corpIds)) {
            return List.of();
        }

        // 获取企业信息，并分类为预付/后付
        ListCorpParam corpListParam = new ListCorpParam();
        corpListParam.setIdList(corpIds);
        ListResponse<CorpVo> corpListRes = this.corpFeignClient.getCorpList(corpListParam);
        if (corpListRes == null || CollectionUtils.isEmpty(corpListRes.getData())) {
            log.error("获取企业列表失败,无法按场站统计企业充电收益. corpIdList = {}", corpIds);
            return List.of();
        } else if (corpIds.size() != corpListRes.getData().size()) {
            log.error("企业数量不一致,无法正确计算场站企业充电收益. corpIdList = {}", corpIds);
            // 先继续，计算可查到的企业的收益情况
        } else {
            log.info("有充电的企业： {}",
                    corpListRes.getData()
                            .stream().map(c -> c.getCorpName() + " - " + c.getId())
                            .collect(Collectors.toList()));
        }
        List<CorpVo> prepayCorp = new ArrayList<>();    // 预付费企业
        List<CorpVo> postPayCorp = new ArrayList<>();   // 后付费企业
        for (CorpVo corp : corpListRes.getData()) {
            if (CorpType.PLATFORM != corp.getType()) {
                log.error("企业 {} / {} 不是平台企业. corp.type = {}", corp.getCorpName(), corp.getId(), corp.getType());
            } else if (SettlementType.BALANCE == corp.getSettlementType()) {    // 其余都算后付费
                prepayCorp.add(corp);
            } else {
                postPayCorp.add(corp);
            }
        }
        List<SiteAccountProfitDto> result = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(prepayCorp)) {
            result.addAll(this.getSitePrepayCorpProfit(site, stopTime, prepayCorp));
        }
        if (CollectionUtils.isNotEmpty(postPayCorp)) {
            result.addAll(this.getSitePostPayCorpProfit(site, stopTime, prepayCorp));
        }
        return result;
    }

    /**
     * 统计使用企业账户充电的收入 -- 预付费企业
     */
    private List<SiteAccountProfitDto> getSitePrepayCorpProfit(SitePo site, TimeFilter stopTime, List<CorpVo> corpList) {
        List<Long> corpIdList = corpList.stream().map(c -> c.getId()).collect(Collectors.toList());
        Map<Long, CorpVo> corpMap = corpList.stream().collect(Collectors.toMap(CorpVo::getId, o -> o));
        List<SiteIncomeVo> incomeList = this.biSiteOrderAccountRoDs.getSiteIncomeByCorp(site.getId(), stopTime, corpIdList);
        return incomeList.stream()
                .map(income -> {
                    SiteAccountProfitDto profit = new SiteAccountProfitDto();
                    profit.setAccountType(PayAccountType.CORP)
                            .setSettlementType(SettlementType.BALANCE)
                            .setAccountId(income.getAccountId())
                            .setAccountName(income.getAccountName())
                            .setBillNo("")
                            .setElec(income.getElec())
                            .setElecOriginFee(income.getElecOriginFee())
                            .setElecCostFee(income.getElecCostFee())
                            .setServCostFee(income.getServCostFee())
                            .setOrderElecProfit(income.getElecCostFee())
                            .setOrderServProfit(income.getServCostFee());
                    CorpVo corp = corpMap.get(income.getAccountId());
                    if (corp != null) {
                        profit.setAccountName(corp.getCorpName());
                    }
                    return profit;
                }).collect(Collectors.toList());

    }

    /**
     * 统计使用企业账户充电的收入 -- 后付费企业
     */
    private List<SiteAccountProfitDto> getSitePostPayCorpProfit(SitePo site, TimeFilter stopTime, List<CorpVo> corpList) {
        return List.of();
    }

    /**
     * 统计使用商户会员户充电的收入
     */
    private List<SiteAccountProfitDto> getSiteCommProfit(SitePo site, TimeFilter payTime) {
        List<SiteIncomeVo> incomeList = this.biSiteOrderAccountRoDs.getSiteIncomeByCommercial(site.getId(), payTime);
        return incomeList.stream()
                .map(income -> {
                    CommercialSimpleVo comm = commercialRoDs.getCommercial(income.getAccountId());
                    SiteAccountProfitDto profit = new SiteAccountProfitDto();
                    profit.setAccountType(PayAccountType.COMMERCIAL)
                            .setSettlementType(SettlementType.BALANCE)
                            .setAccountId(income.getAccountId())
                            .setBillNo("")
                            .setElec(income.getElec())
                            .setElecOriginFee(income.getElecOriginFee())
                            .setElecCostFee(income.getElecCostFee())
                            .setServCostFee(income.getServCostFee())
                            .setOrderElecProfit(income.getElecCostFee())
                            .setOrderServProfit(income.getServCostFee());
                    if (comm != null) {
                        profit.setAccountName(comm.getShortName());
                    } else {
                        profit.setAccountName("");
                    }
                    return profit;
                }).collect(Collectors.toList());
    }

    private SiteAccountProfitDto getSiteNoPayIncome(SitePo site, TimeFilter stopTime) {
        SiteIncomeVo income = this.biSiteOrderAccountRoDs.getSiteIncomeNoPay(site.getId(), stopTime);
        if (income == null
                || income.getElec() == null
                || DecimalUtils.lteZero(income.getElec())) {
            return null;
        }
        SiteAccountProfitDto result = new SiteAccountProfitDto();
        result.setAccountType(PayAccountType.UNKNOWN)
                .setSettlementType(SettlementType.UNKNOWN)
                .setAccountId(0L)
                .setAccountName("未结算")
                .setBillNo("")
                .setElec(income.getElec())
                .setElecOriginFee(income.getElecOriginFee())
                .setElecCostFee(BigDecimal.ZERO)
                .setServCostFee(BigDecimal.ZERO)
                .setOrderElecProfit(BigDecimal.ZERO)
                .setOrderServProfit(BigDecimal.ZERO);
        return result;
    }
}
