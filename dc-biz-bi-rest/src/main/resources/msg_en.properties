common.date=Date
common.createTime=Creat time
common.phone=Phone
common.email=Email
common.accountName=Account name
common.endTime=End time
common.commercialFullName=Owner
common.userName=Client name
common.status=Status
charger.order.summary=Summary
charger.order.sharpTime=Sharp time
charger.order.peakTime=Peak time
charger.order.shoulderTime=Shoulder time
charger.order.offPeakTime=Off-peak time
charger.order.summaryStatistics=Statistical summary
charger.order.dailySummary=Daily summary
charger.order.totalNumber=Total number of orders(Orders)
charger.order.totalPrice=Total order amount(VND)
charger.order.totalElecCosts=Total electricity costs(VND)
charger.order.totalServiceCosts=Total service fee(VND)
charger.order.totalOrderPower=Total order power(kW·h)
charger.order.standardServiceCosts=Standard service fee(VND)
charger.order.standardElecCosts=Standard electricity fee(VND)
charger.order.totalAmountOfCharging=Total amount of charging(VND)
\u8BA2\u5355\u660E\u7EC6=Order details
charger.order.orderNo=Order number
charger.order.billNo=Bill number
charger.order.payOrderNo=Payment Order Number
charger.order.refundOrderNo=Refund Order Number
charger.order.orderStatus=Order status
charger.order.payAccountName=Debit account
charger.order.source=Source
charger.order.bootMode=Boot mode
charger.order.cardType=Card type
charger.order.brand=Brand
charger.order.model=Model
charger.order.bmsVoltage=Auxiliary power supply(V)
charger.order.gbVer=GB/T
charger.order.stationName=Station name
charger.order.stationCode=Station code
charger.order.evseCode=Charger code
charger.order.evseName=Charger name
charger.order.supplyType=Charger type
charger.order.connectorNo=Connector code
charger.order.connectorName=Connector name
charger.order.connectorId=Connector mark
charger.order.chargeStartTime=Charging start from
charger.order.chargeEndTime=Charging start to
charger.order.uploadTime=Upload time
charger.order.duration=Order Duration
charger.order.startSoc=Pre-charge SOC(%)
charger.order.stopSoc=Post-charge SOC(%)
charger.order.stopReason=Reason for stopping charging
charger.order.abnormal=Reason for abnormality
charger.order.tipElectricity=Electricity costs(Sharp time)
charger.order.tipServicePrice=Service fee(Sharp time)
charger.order.tipServiceUnit=Unit cost of services(Sharp time)
charger.order.tipElecPrice=Electricity costs(Sharp time)
charger.order.tipElectricUnit=Unit cost of electricity (Sharp time)
charger.order.tipSumPrice=Total costs(Sharp time)
charger.order.peakElectricity=Quantity of electricity(Peak time)
charger.order.peakServicePrice=Service fee (Peak time)
charger.order.peakServiceUnit=Electricity costs (Peak time)
charger.order.peakElecPrice=Electricity costs (Peak time)
charger.order.peakElectricUnit=Unit cost of electricity (Peak time)
charger.order.peakSumPrice=Total costs(Peak time)
charger.order.flatElectricity=Quantity of electricity(Shoulder time)
charger.order.flatServicePrice=Service fee(Shoulder time)
charger.order.flatServiceUnit=Unit cost of services(Shoulder time)
charger.order.flatElecPrice=Electricity costs(Shoulder time)
charger.order.flatElectricUnit=Unit cost of electricity(Shoulder time)
charger.order.flatSumPrice=Total costs(Shoulder time)
charger.order.valleyElectricity=Quantity of electricity(Off-peak time)
charger.order.valleyServicePrice=Service fee(Off-peak time)
charger.order.valleyServiceUnit=Unit cost of services(Off-peak time)
charger.order.valleyElecPrice=Electricity costs(Off-peak time)
charger.order.valleyElectricUnit=Unit cost of electricity(Off-peak time)
charger.order.valleySumPrice=Total costs(Off-peak time)
charger.order.orderElectricity=Total power(kW·h)
charger.order.servOriginFee=Standard service fee(VND)
charger.order.servicePrice=Total service fee(VND)
charger.order.elecOriginFee=Standard electricity costs(VND)
charger.order.elecPrice=Total electricity costs(VND)
charger.order.discount=Amount of electric loss(VND)
charger.order.principalAmount=Actual amount consumption(VND)
charger.order.freeGoldAmount=Gift amount consumption(VND)
charger.order.payTime=Payment time
charger.order.processType=Order Processing Type
charger.order.startElectricity=The meter begins to read
charger.order.endElectricity=End of meter reading
#订单导出map获取的相关字段start
#来源-其他
\u5176\u4ed6=Others
#来源-设备触发
\u8bbe\u5907\u89e6\u53d1=Local trigger order
#来源-用户应用
\u7528\u6237\u5e94\u7528=User application
#订单状态-订单未激活
\u8ba2\u5355\u672a\u6fc0\u6d3b=Order not activated
#订单状态-充电中
\u5145\u7535\u4e2d=Charging
#订单状态-待支付
\u5f85\u652f\u4ed8=To be paid
#订单状态-已结算
\u5df2\u7ed3\u7b97=Settled
#订单状态-离线中
\u79bb\u7ebf\u4e2d=Off-line
#扣款账户-个人
\u4e2a\u4eba=Personage
#启动方式-设备端自主
\u8bbe\u5907\u7aef\u81ea\u4e3b=Operated from charger
#启动方式-在线卡鉴权
\u5728\u7ebf\u5361\u9274\u6743=Card validity verification
#启动方式-VIN识别鉴权
\u0056\u0049\u004e\u8bc6\u522b\u9274\u6743=VIN validity verification
#启动方式-VIN白名单
\u0056\u0049\u004e\u767d\u540d\u5355=VIN whitelist
#异常原因-心跳超时
\u5fc3\u8df3\u8d85\u65f6=Heartbeat timeout
#异常原因-订单更新超时
\u8ba2\u5355\u66f4\u65b0\u8d85\u65f6=Order update timeout
#异常原因-订单电量越限
\u8ba2\u5355\u7535\u91cf\u8d8a\u9650=Order electricity over limit
#异常原因-订单金额越限
\u8ba2\u5355\u91d1\u989d\u8d8a\u9650=Order amount over limit
#异常原因-充电中超时
\u5145\u7535\u4e2d\u8d85\u65f6=Time out while charging
#异常原因-启动中超时
\u542f\u52a8\u4e2d\u8d85\u65f6=Time out while starting
#异常原因-充电中停用
\u5145\u7535\u4e2d\u505c\u7528=Stop while charging
#异常原因-即充即退退款失败
\u5373\u5145\u5373\u9000\u9000\u6b3e\u5931\u8d25=Immediate charge and refund failure
#异常原因-支付失败
\u652f\u4ed8\u5931\u8d25=Payment failed
#异常原因-订单冻结金额异常
\u8ba2\u5355\u51bb\u7ed3\u91d1\u989d\u5f02\u5e38=Order Freeze Amount Abnormal
#异常原因-账户异常
\u8d26\u6237\u5f02\u5e38=Account abnormal
#异常原因-订单电量异常
\u8ba2\u5355\u7535\u91cf\u5f02\u5e38=Abnormal Order Power
#异常原因-订单金额异常
\u8ba2\u5355\u91d1\u989d\u5f02\u5e38=Abnormal Order Amount
#订单处理类型-正常处理
\u6b63\u5e38\u5904\u7406=Normal handling
#订单处理类型-异常手工未处理
\u5f02\u5e38\u624b\u5de5\u672a\u5904\u7406=Exception to be handled
#订单处理类型-异常手工处理
\u5f02\u5e38\u624b\u5de5\u5904\u7406=Exception manual handling
#停充原因-正常停充
\u6b63\u5e38\u505c\u5145=Normal shutdown
#停充原因-异常停充
\u5f02\u5e38\u505c\u5145=Abnormal shutdown
#停充原因-充满
\u5145\u6ee1=Be filled with
#停充原因-桩端手动停充-停充按钮
\u6869\u7aef\u624b\u52a8\u505c\u5145-\u505c\u5145\u6309\u94ae=Pile end manual stop charging - Stop charging button
#停充原因-用户停充
\u7528\u6237\u505c\u5145=Customer discharge
#停充原因-急停故障
\u6025\u505c\u6545\u969c=Scram fault
#停充原因-接收超时BCL
\u63a5\u6536\u8d85\u65f6\u0042\u0043\u004c=Received timeout BCL
#停充原因-未知
\u672a\u77e5=Unknown
#停充原因-接收超时BRM
\u63a5\u6536\u8d85\u65f6\u0042\u0052\u004d=Receive timeout BRM
#停充原因-K1K2 前后电压不一致
\u004b\u0031\u004b\u0032\u0020\u524d\u540e\u7535\u538b\u4e0d\u4e00\u81f4=Voltage inconsistency between K1 and K2
#订单导出map获取的相关字段end
waring.warningNo=Alarm number
warning.startTime=Time of Occurrence
warning.warningDeviceNo=Device number(Name)
warning.plugIdx=Charging connector serial number
warning.siteName=Alerting station
warning.waringName=Name of alarm
warning.remark=Remark
waring.status=Status
#告警状态-未结束
\u672a\u7ed3\u675f=Alarming
#告警状态-自动结束
\u81ea\u52a8\u7ed3\u675f=Alarm end automatically
#告警状态-手动结束
\u624b\u52a8\u7ed3\u675f=Alarm end manually
#告警原因-桩离线
\u6869\u79bb\u7ebf=Charger offline
#告警原因-充电机检测到充电电压异常
\u5145\u7535\u673a\u68c0\u6d4b\u5230\u5145\u7535\u7535\u538b\u5f02\u5e38=The charging voltage is abnormal. Procedure
#告警原因-交流-S2开关异常
\u4ea4\u6d41\u002d\u0053\u0032\u5f00\u5173\u5f02\u5e38=The AC -S2 switch is abnormal
#告警原因-交流-枪连接异常
\u4ea4\u6d41\u002d\u67aa\u8fde\u63a5\u5f02\u5e38=Ac - gun connection abnormal
#告警原因-绝缘检测表通讯故障
\u7edd\u7f18\u68c0\u6d4b\u8868\u901a\u8baf\u6545\u969c=The communication of the insulation detection table is faulty
#告警原因-UI-安全管理板通讯中断
\u0055\u0049\u002d\u5b89\u5168\u7ba1\u7406\u677f\u901a\u8baf\u4e2d\u65ad=The communication between the UI-security management board is interrupted
#告警原因-连接器故障(导引电路检测到故障)- CC1信号
\u8fde\u63a5\u5668\u6545\u969c\u0028\u5bfc\u5f15\u7535\u8def\u68c0\u6d4b\u5230\u6545\u969c\u0029\u002d\u0020\u0043\u0043\u0031\u4fe1\u53f7=Connector failure (fault detected by guidance circuit)- CC1 signal
#告警原因-连接器故障(导引电路检测到故障)-枪过温
\u8fde\u63a5\u5668\u6545\u969c\u0028\u5bfc\u5f15\u7535\u8def\u68c0\u6d4b\u5230\u6545\u969c\u0029\u002d\u67aa\u8fc7\u6e29=Connector failure (fault detected by guidance circuit)- Gun overtemperature
#告警原因-电池电压过低
\u7535\u6c60\u7535\u538b\u8fc7\u4f4e=The battery voltage is too low. Procedure
#告警备注-停止当前充电，允许后续充电
\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535=Stop current charging to allow subsequent charging
#告警备注-离线原因：上电重启
\u79bb\u7ebf\u539f\u56e0\uff1a\u4e0a\u7535\u91cd\u542f=Offline cause: The system is powered on and restarted
#告警备注-停止当前充电，允许后续充电，提醒用户检查车辆状况
\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u63d0\u9192\u7528\u6237\u68c0\u67e5\u8f66\u8f86\u72b6\u51b5=Stop the current charge, allow subsequent charging, and remind the user to check the condition of the vehicle
#告警备注-停止当前充电，允许后续充电，通知运维检查
\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u901a\u77e5\u8fd0\u7ef4\u68c0\u67e5=Stop current charging, allow subsequent charging, and notify O&M check
#告警备注-停止当前充电，允许后续充电，提示用户枪连接异常
\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u63d0\u793a\u7528\u6237\u67aa\u8fde\u63a5\u5f02\u5e38=Stop current charging and allow subsequent charging, prompting the user that the gun connection is abnormal
#告警备注-停止当前充电，允许后续充电，提示用户待枪冷却后再次尝试充电
\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u63d0\u793a\u7528\u6237\u5f85\u67aa\u51b7\u5374\u540e\u518d\u6b21\u5c1d\u8bd5\u5145\u7535=Stop the current charge and allow subsequent charging, prompting the user to try charging again after the gun cools
#告警备注-BCP 报文字节数异常或BCP 报文电池电压数值越界
\u0042\u0043\u0050\u0020\u62a5\u6587\u5b57\u8282\u6570\u5f02\u5e38\u6216\u0042\u0043\u0050\u0020\u62a5\u6587\u7535\u6c60\u7535\u538b\u6570\u503c\u8d8a\u754c=The number of BCP bytes is abnormal or the battery voltage of the BCP packet exceeds the threshold
#告警备注-交流-CP电压采样异常
\u4ea4\u6d41\u002d\u0043\u0050\u7535\u538b\u91c7\u6837\u5f02\u5e38=The AC-CP voltage sampling is abnormal
#告警备注-交流-DWIN通讯故障
\u4ea4\u6d41\u002d\u0044\u0057\u0049\u004e\u901a\u8baf\u6545\u969c=The AC-DWIN communication is faulty
#告警备注-交流-交流接触器
\u4ea4\u6d41\u002d\u4ea4\u6d41\u63a5\u89e6\u5668=AC - AC contactor
#告警备注-交流-交流欠压
\u4ea4\u6d41\u002d\u4ea4\u6d41\u6b20\u538b=AC - AC undervoltage
#告警备注-交流-交流过压
\u4ea4\u6d41\u002d\u4ea4\u6d41\u8fc7\u538b=AC - AC overpressure
#告警备注-交流-充电电流为0
\u4ea4\u6d41\u002d\u5145\u7535\u7535\u6d41\u4e3a\u0030=AC - Charge current is 0
#告警备注-交流-急停故障
\u4ea4\u6d41\u002d\u6025\u505c\u6545\u969c=AC - Scram failure
#告警备注-交流-枪低温
\u4ea4\u6d41\u002d\u67aa\u4f4e\u6e29=AC - Gun cold
#告警备注-交流-枪体高温
\u4ea4\u6d41\u002d\u67aa\u4f53\u9ad8\u6e29=AC - Gun body heat
#告警备注-交流-枪温度传感器故障
\u4ea4\u6d41\u002d\u67aa\u6e29\u5ea6\u4f20\u611f\u5668\u6545\u969c=AC - Gun temperature sensor is faulty
#告警备注-交流-枪过流
\u4ea4\u6d41\u002d\u67aa\u8fc7\u6d41=AC - Gun overshoot
#告警备注-交流-枪高温
\u4ea4\u6d41\u002d\u67aa\u9ad8\u6e29=AC - Gun heat
#告警备注-交流-桩温度传感器故障
\u4ea4\u6d41\u002d\u6869\u6e29\u5ea6\u4f20\u611f\u5668\u6545\u969c=AC - Pile temperature sensor is faulty
#告警备注-交流-电表通讯
\u4ea4\u6d41\u002d\u7535\u8868\u901a\u8baf=AC - meter communication
#告警备注-交流-读卡器
\u4ea4\u6d41\u002d\u8bfb\u5361\u5668=AC - Card reader
#告警备注-交流-门禁
\u4ea4\u6d41\u002d\u95e8\u7981=AC - Access control
#告警备注-停止当前充电，允许后续充电，提示用户拔枪重新尝试充电
\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u63d0\u793a\u7528\u6237\u62d4\u67aa\u91cd\u65b0\u5c1d\u8bd5\u5145\u7535=Stop the current charging, allow subsequent charging, and prompt the user to pull out the gun and try charging again
#告警备注-停止当前充电，允许后续充电，提示用户枪锁异常
\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u63d0\u793a\u7528\u6237\u67aa\u9501\u5f02\u5e38=Stop current charging and allow subsequent charging, prompting the user that the gun lock is abnormal
#告警备注-停止当前充电，允许后续充电，提示用户检查车辆状态
\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u63d0\u793a\u7528\u6237\u68c0\u67e5\u8f66\u8f86\u72b6\u6001=Stop current charging, allow subsequent charging, and prompt the user to check vehicle status
#告警备注-停止当前充电，允许后续充电，提示用户重新插枪充电
\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u63d0\u793a\u7528\u6237\u91cd\u65b0\u63d2\u67aa\u5145\u7535=Stop current charging, allow subsequent charging, and prompt the user to re-plug the gun to charge
#告警备注-停止当前充电，故障恢复后允许后续充电，长时间未恢复则通知运维检查桩体
\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u6545\u969c\u6062\u590d\u540e\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u957f\u65f6\u95f4\u672a\u6062\u590d\u5219\u901a\u77e5\u8fd0\u7ef4\u68c0\u67e5\u6869\u4f53=Stop the current charging, allow subsequent charging after fault recovery, and notify O&M to check the pile if it is not recovered for a long time
#告警备注-充电中有电压，电流持续3分钟小于1A
\u5145\u7535\u4e2d\u6709\u7535\u538b\uff0c\u7535\u6d41\u6301\u7eed\u0033\u5206\u949f\u5c0f\u4e8e\u0031\u0041=There is voltage in charging, and the current lasts less than 1A for 3 minutes
#告警备注-充电中桩掉电
\u5145\u7535\u4e2d\u6869\u6389\u7535=The charging pile is powered off
#告警备注-判断BHM报文最高允许充电总电压低于充电机最低输出电压
\u5224\u65ad\u0042\u0048\u004d\u62a5\u6587\u6700\u9ad8\u5141\u8bb8\u5145\u7535\u603b\u7535\u538b\u4f4e\u4e8e\u5145\u7535\u673a\u6700\u4f4e\u8f93\u51fa\u7535\u538b=The maximum allowable total charging voltage of the BHM packet is lower than the minimum output voltage of the charger
#告警备注-告警通知运维，不影响充电
\u544a\u8b66\u901a\u77e5\u8fd0\u7ef4\uff0c\u4e0d\u5f71\u54cd\u5145\u7535=The alarm notifies O&M and does not affect charging
#告警备注-告警，不影响充电
\u544a\u8b66\uff0c\u4e0d\u5f71\u54cd\u5145\u7535=The alarm does not affect charging
#告警备注-故障枪停充, 不影响下次充电
\u6545\u969c\u67aa\u505c\u5145\u002c\u0020\u4e0d\u5f71\u54cd\u4e0b\u6b21\u5145\u7535=The fault gun stops charging, does not affect the next charging
#告警备注-故障枪停充并禁用，其他枪不受影响
\u6545\u969c\u67aa\u505c\u5145\u5e76\u7981\u7528\uff0c\u5176\u4ed6\u67aa\u4e0d\u53d7\u5f71\u54cd=The faulty gun stops charging and is disabled. Other guns are not affected
#告警备注-故障枪停充，不影响下次充电
\u6545\u969c\u67aa\u505c\u5145\uff0c\u4e0d\u5f71\u54cd\u4e0b\u6b21\u5145\u7535=The fault gun stops charging, does not affect the next charging
#告警备注-故障枪（挂载在故障 UI 板下的枪）停用，故障恢复后可充电
\u6545\u969c\u67aa\uff08\u6302\u8f7d\u5728\u6545\u969c\u0020\u0055\u0049\u0020\u677f\u4e0b\u7684\u67aa\uff09\u505c\u7528\uff0c\u6545\u969c\u6062\u590d\u540e\u53ef\u5145\u7535=The fault gun (the gun mounted under the fault UI board) is deactivated and can be charged after fault recovery
#告警备注-整桩停充，所有枪、弓禁用
\u6574\u6869\u505c\u5145\uff0c\u6240\u6709\u67aa\u3001\u5f13\u7981\u7528=The whole pile is stopped, and all guns and bows are banned
#告警备注-整桩停充，所有枪禁用
\u6574\u6869\u505c\u5145\uff0c\u6240\u6709\u67aa\u7981\u7528=The whole pile is shut down. All guns are disabled
#告警备注-禁止充电，通知运维检查桩体
\u7981\u6b62\u5145\u7535\uff0c\u901a\u77e5\u8fd0\u7ef4\u68c0\u67e5\u6869\u4f53=No charging, inform operation and maintenance to check the pile
#告警备注-禁用充电功能，通知运维检查
\u7981\u7528\u5145\u7535\u529f\u80fd\uff0c\u901a\u77e5\u8fd0\u7ef4\u68c0\u67e5=Disable the charging function and notify O&M check
#告警备注-离线原因：其它
\u79bb\u7ebf\u539f\u56e0\uff1a\u5176\u5b83=Offline Cause: Other
#告警备注-离线原因：失去连接
\u79bb\u7ebf\u539f\u56e0\uff1a\u5931\u53bb\u8fde\u63a5=Offline Cause: The connection is disconnected
#告警备注-离线原因：报文签名不匹配
\u79bb\u7ebf\u539f\u56e0\uff1a\u62a5\u6587\u7b7e\u540d\u4e0d\u5339\u914d=Offline Cause: The packet signature does not match
#告警备注-预充电阶段检测到K1K2外侧电压低于充电机最低输出电压15V
\u9884\u5145\u7535\u9636\u6bb5\u68c0\u6d4b\u5230\u004b\u0031\u004b\u0032\u5916\u4fa7\u7535\u538b\u4f4e\u4e8e\u5145\u7535\u673a\u6700\u4f4e\u8f93\u51fa\u7535\u538b\u0031\u0035\u0056=In the pre-charging stage, the outer voltage of K1K2 is detected to be lower than 15V, the lowest output voltage of the charger
#告警备注-预充电阶段检测到K1K2外侧电压高于充电机最高输出电压15V
\u9884\u5145\u7535\u9636\u6bb5\u68c0\u6d4b\u5230\u004b\u0031\u004b\u0032\u5916\u4fa7\u7535\u538b\u9ad8\u4e8e\u5145\u7535\u673a\u6700\u9ad8\u8f93\u51fa\u7535\u538b\u0031\u0035\u0056=The outer voltage of K1K2 detected in the pre-charging stage is higher than the maximum output voltage of 15V of the charger
#告警明细
\u544a\u8b66\u660e\u7ec6=Alarm detail
car.carDepart=Fleet name
car.carNo=Plate number
car.lineNum=Line
car.carNum=VID
car.carDepartWithoutName=Fleet
#在线卡信息
\u5728\u7ebf\u5361\u4fe1\u606f=Online card information
card.cardNo=Card number
card.cardName=Card name
card.usableStationCount=Specified Station Count
#卡片状态-未激活
\u672a\u6fc0\u6d3b=Inactive
#卡片状态-已激活
\u5df2\u6fc0\u6d3b=Activated
#卡片状态-卡锁定
\u5361\u9501\u5b9a=Card Blocking
#卡片状态-已挂失
\u5df2\u6302\u5931=Reported lost
#卡片状态-已失效
\u5df2\u5931\u6548=Invalid
#卡片状态-已过期
\u5df2\u8fc7\u671f=Expired
#VIN码信息
\u0056\u0049\u004e\u7801\u4fe1\u606f=VIN code information
vin.auth.status.opening=On
vin.auth.status.closing=Off
vin.vinCode=VIN
#在线认证可用站点数
vin.usableStationCount=Authorized stations（VIN）
#本地认证
vin.authStatus=Local Authentication
#充值信息
\u5145\u503c\u4fe1\u606f=Top-up info
payBill.orderNumber=Order number
payBill.source=Top-up by
payBill.flowType=Top-up type
payBill.actualAmount=Actual amount(VND)
payBill.freeAmount=Gift amount(VND)
payBill.amountBefore=Current balance(VND)
payBill.amountAfter=Updated balance(VND)
payBill.payAccountName=Account name
payBill.payChannelDesc=Payment method
payBill.status=Payment status
payBill.account=Receiving account
payBill.rechargeTime=Top-up time
payBill.operatorName=Operator
#充值来源-平台充值(后台操作)
\u5e73\u53f0\u5145\u503c\u0028\u540e\u53f0\u64cd\u4f5c\u0029=Platform top up (Backend operation)
#充值类型-充值
\u5145\u503c=Top up
#充值类型-减少
\u51cf\u5c11=Reduce
#支付方式-银行卡支付
\u94f6\u884c\u5361\u652f\u4ed8=Pay by card
#支付状态-已支付
\u5df2\u652f\u4ed8=Paid
#支付状态-未支付
\u672a\u652f\u4ed8=Unpaid
