<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Title</title>
    <style>
        body {
            font-family: SimHei;
        }

        .position1 {
            padding-left: 10px;
            height: 125px;
        }

        .position2 {
            text-align: right;
            padding-right: 180px;
        }

        .comment {
            height: 150px;
        }

        .h-50 {
            height: 50px;
        }

        .conclusion {
            height: 150px;
        }

        table {
            border-collapse: collapse;
            border-spacing: 0;
        }

        .header img {
            width: 385px;
        }

        .smallHeader {
            background-color: #acbeee;
            height: 30px;
        }
        .signImage {
            width: 50px;
            height:20px;
            display: inline-block;
        }
    </style>
</head>

<body>
<!--    <div class="header">-->
<!--        <img src="https://dc-sz-1.oss-cn-shenzhen.aliyuncs.com/pdf/logo.png" />-->
<!--    </div>-->
    <table border="1" style="width: 100%;">
        <tr>
            <td align="center" colspan="16" style="height: 50px;">
                <#if prerun.siteName??>${prerun.siteName}开通调试报告</#if>
            </td>
        </tr>
        <tr>
            <td align="center" rowspan="3" style="height: 50px;width:100px;">站点基本信息</td>
            <td align="center" style="height: 50px;width:100px;">站点地址</td>
            <td align="center" style="height: 50px;">
                <#if prerun.siteAddress??>
                    ${prerun.siteAddress}
                </#if>
            </td>
            <td align="center" style="height: 50px;width:100px;">站点性质</td>
            <td align="center" style="height: 50px;">
                <#if prerun.gcType == 0>未知
                <#elseif prerun.gcType == 1>投建运营
                <#elseif prerun.gcType == 2>以租代售
                <#elseif prerun.gcType == 3>纯租赁
                <#elseif prerun.gcType == 4>EPC+O
                <#elseif prerun.gcType == 5>销售的代收代付
                <#elseif prerun.gcType == 6>代运营
                <#elseif prerun.gcType == 7>委托运营
            </#if>
            </td>
        </tr>
        <tr>
            <td align="center" rowspan="1" style="height: 50px;">开通调试工单</td>
            <td align="center" style="height: 50px;">${prerun.prerunNo}</td>
            <td align="center" style="height: 50px;">开通调试日期</td>
            <td align="center" style="height: 50px;">
                <#if prerun.prerunTime??>
                    ${prerun.prerunTime?string.yyyy}-${prerun.prerunTime?string.MM}-${prerun.prerunTime?string.dd}
                </#if>
            </td>
        </tr>
        <tr>
            <td align="center" rowspan="1" style="height: 50px;">开通调试人</td>
            <td align="center" style="height: 50px;">
                <#if prerun.prerunnerName??>
                    ${prerun.prerunnerName}
                </#if>
            </td>
            <td align="center" style="height: 50px;">质检人</td>
            <td align="center" style="height: 50px;">
                <#if prerunCheckPo?? && prerunCheckPo.opName??>
                    ${prerunCheckPo.opName}
                </#if>
            </td>
        </tr>
        <tr>
            <td align="center" rowspan="3" style="height: 50px;width:100px;">联系人信息</td>
            <td align="center" style="height: 50px;width:100px;">工程交付人/电话</td>
            <td align="center" style="height: 50px;">
                <#if prerun?? && prerun.deliverName??>
                    ${prerun.deliverName}
                </#if>/
                <#if prerun?? && prerun.deliverPhone??>
                    ${prerun.deliverPhone}
                </#if>
            </td>
            <td align="center" style="height: 50px;width:100px;">桩群信息表提供人/电话</td>
            <td align="center" style="height: 50px;">
                <#if prerun?? && prerun.infoSupplyName??>
                    ${prerun.infoSupplyName}
                </#if>/
                <#if prerun?? && prerun.infoSupplyPhone??>
                    ${prerun.infoSupplyPhone}
                </#if>
            </td>
        </tr>
        <tr>
            <td align="center" rowspan="1" style="height: 50px;">客户联系人/电话</td>
            <td align="center" style="height: 50px;">
                <#if prerun?? && prerun.clientName??>
                    ${prerun.clientName}
                </#if>/
                    <#if prerun?? && prerun.clientPhone??>
                    ${prerun.clientPhone}
                </#if>
        </td>
            <td align="center" style="height: 50px;">站点负责人/电话</td>
            <td align="center" style="height: 50px;">
                <#if prerun?? && prerun.masterName??>
                    ${prerun.masterName}
                </#if>/
                <#if prerun?? && prerun.masterPhone??>
                    ${prerun.masterPhone}
                </#if>
            </td>
        </tr>
        <tr>
            <td align="center" rowspan="1" style="height: 50px;">销售/运营负责人/电话</td>
            <td align="center" style="height: 50px;">
                <#if prerun?? && prerun.salesName??>
                    ${prerun.salesName}
                </#if>/
                <#if prerun?? && prerun.salesPhone??>
                    ${prerun.salesPhone}
                </#if>
            </td>
            <td align="center" style="height: 50px;">运维联系人/电话</td>
            <td align="center" style="height: 50px;">
                <#if prerun?? && prerun.maintainName??>
                    ${prerun.maintainName}
                </#if>/
                <#if prerun?? && prerun.maintainPhone??>
                    ${prerun.maintainPhone}
                </#if>
            </td>
        </tr>
        <tr>
            <td align="center" style="height: 50px;">平台接入信息</td>
            <td align="center" style="height: 50px;">是否接入平台</td>
            <td align="center" style="height: 50px;">
                <#if prerun?? && prerun.platformConn??>
                    <#if prerun.platformConn>
                        是
                    <#else>
                        否
                    </#if>
                </#if>
            </td>
            <td align="center" style="height: 50px;">充电方式</td>
            <td align="center" style="height: 50px;">
                <#if prerun?? && prerun.chargeTypes??>
                ${prerun.chargeTypes?map(chargeType -> chargeType.desc)?join("+")}
                </#if>
            </td>
        </tr>
        <tr>
            <td align="center" style="height: 50px;">设备信息</td>
            <td align="center" colspan="4">
                <table border="1" style="width: 100%;">
                    <#if prerunEvsePoList??>
                        <#assign indexNo = 1>
                    <tr class="smallHeader">
                        <td align="center" rowspan="1" style="height: 50px;width:100px;">序号</td>
                        <td align="center" colspan="6">桩信息</td>
                    </tr>
                        <#list prerunEvsePoList as evse>
                            <tr>
                                <td align="center" rowspan="4">${indexNo}</td>
                                <#assign indexNo++>
                                <td align="center" rowspan="" style="width:100px;">充电桩编号</td>
                                <td align="center" colspan="">
                                    <#if evse.evseNo??>
                                        ${evse.evseNo}
                                    </#if>
                                </td>
                                <td align="center" rowspan="" style="width:100px;">充电桩名称</td>
                                <td align="center" colspan="">
                                    <#if evse.evseName??>
                                        ${evse.evseName}
                                    </#if>
                                </td>
                                <td align="center" rowspan="" style="width:100px;">充电桩型号</td>
                                <td align="center" colspan="">
                                    <#if evse.model??>
                                        ${evse.model}
                                    </#if>
                                </td>
                            </tr>
                            <tr>
                                <td align="center" rowspan="">充电桩类型</td>
                                <td align="center" colspan="">
                                    <#if evse.supply == "AC">交流
                                        <#elseif evse.supply == "DC">直流
                                        <#elseif evse.supply == "BOTH">交直流
                                        <#else>未知
                                    </#if>
                                </td>
                                <td align="center" rowspan="">充电桩功率</td>
                                <td align="center" colspan="">
                                    <#if evse.power??>
                                        ${evse.power}
                                    </#if>
                                </td>
                                <td align="center" rowspan="">模块型号</td>
                                <td align="center" colspan="">
                                    <#if evse.moduleType??>
                                        ${evse.moduleType}
                                    </#if>
                                </td>
                            </tr>
                            <tr>
                                <td align="center" rowspan="">铭牌编号</td>
                                <td align="center" colspan="">
                                    <#if evse.physicalNo??>
                                        ${evse.physicalNo}
                                    </#if>
                                </td>
                                <td align="center" rowspan="">出厂日期</td>
                                <td align="center" colspan="">
                                    <#if evse.produceDate??>
                                        ${evse.produceDate?string.yyyy}-${evse.produceDate?string.MM}-${evse.produceDate?string.dd}
                                    </#if>
                                </td>
                                <td align="center" rowspan="">软件版本</td>
                                <td align="center" colspan="">
                                    <#if evse.firmwareVer??>
                                        ${evse.firmwareVer}
                                    </#if>
                                </td>
                            </tr>
                            <tr>
                                <td align="center" rowspan="">质保期限</td>
                                <td align="center" colspan="">
                                    <#if evse.expireDate??>
                                        ${evse.expireDate?string.yyyy}-${evse.expireDate?string.MM}-${evse.expireDate?string.dd}
                                    </#if>
                                </td>
                                <td align="center" rowspan="">数量</td>
                                <td align="center" colspan="">1</td>
                                <td align="center" rowspan="">备注</td>
                                <td align="center" colspan="">
                                    <#if evse.comment??>
                                        ${evse.comment}
                                    </#if>
                                </td>
                            </tr>
                        </#list>
                    </#if>
                </table>
            </td>
        </tr>
        <tr>
            <td align="center" style="height: 50px;">配套设施信息</td>
            <td align="center" colspan="4">
                <table border="1" style="width: 100%;">
                    <tr class="smallHeader">
                        <td align="center" rowspan="1" style="height: 50px;width:100px;">序号</td>
                        <td align="center" rowspan="" style="width:100px;">设备名称</td>
                        <td align="center" colspan="5">图片</td>
                    </tr>
                    <#macro showImage imageType imageList>
                        <#if imageList?? && 0 < imageList?size>
                            <#assign subList = imageList?filter(x -> x.type?? && x.type == imageType)>
                            <#if 0 < subList?size>
                                <td align="left" colspan="5">
                                <#list subList as img>
                                    <img src="${img.url}" width="100px" height="100px" />
                                </#list>
                                </td>
                            <#else>
                                <td align="center" colspan="5">
                                无
                                </td>
                            </#if>
                        <#else>
                            <td align="center" colspan="5">
                            无
                            </td>
                        </#if>
                    </#macro>
                    <#assign myHash = [{ "type": "FIRE", "label": "消防设施" },{ "type": "RAIN", "label": "监控" },{ "type": "CAMERA", "label": "雨棚" },{ "type": "GATE", "label": "道闸" },{ "type": "TRANS", "label": "充电桩箱变" }]>
                    <#assign assetIndexNo = 1>
                    <#list myHash as k>
                        <tr>
                            <td align="center" rowspan="1" style="width:100px;">${assetIndexNo}</td>
                            <#assign assetIndexNo++>
                            <td align="center" rowspan="" style="width:100px;">${k.label}</td>
                            <@showImage imageType="${k.type}" imageList=prerunAssetImgPoList>
                            </@showImage>
                        </tr>
                    </#list>
                </table>
            </td>
        </tr>
        <tr>
            <td align="center"  colspan="1">场站图片</td>
            <td colspan="4">
                <#if prerun.scenePic??>
                    <#list prerun.scenePic as img>
                        <img src="${img}" width="100px" height="100px" />
                    </#list>
                </#if>
            </td>
        </tr>
        <tr>
            <td class="comment" colspan="16">
                <div style="width: 100%;height: 100%;">
                    <div class="position1">
                        客户评价及建议：
                        <div>
                            <#if prerun.clientRank?? && prerun.clientRank == 1>满意;
                            <#elseif prerun.clientRank?? && prerun.clientRank == 2>不满意;
                        </#if>
                        <#if prerun.clientComment??>
                        ${prerun.clientComment}
                    </#if>
                </div>
                </div>
                <div class="position2">
                    客户签字： <#if prerun.clientSignPic??><img src="${prerun.clientSignPic}" class="signImage"/></#if>
                </div>
                </div>
            </td>
        </tr>
        <tr>
            <td class="conclusion" colspan="16">
                <div style="width: 100%;height: 100%;">
                    <div class="position1">
                        结论：
                    </div>
                    <div class="position2">
                        开通调试人：
                    </div>
                </div>
            </td>
        </tr>
    </table>
    </body>

</html>