<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Title</title>
  <style>
    body {
      font-family: SimHei;
      font-size: 14px;
      color: #606266;
      margin: 30px auto;
    }

    .header img {
      width: 385px;
    }

    .title {
      font-size: 14px;
      color: #666;
      font-weight: 500;
      padding: 10px 15px;
      position: relative;
      width: 100%;
      font-weight: 700;
    }

    .title:before {
      content: "";
      position: absolute;
      width: 4px;
      height: 16px;
      background: #3296fa;
      left: 0;
      top: 10px;
    }

    table {
      border-collapse: collapse;
      border-spacing: 0;
      page-break-inside:auto;
      -fs-table-paginate:paginate;
    }
    tr {
        page-break-inside:avoid;
        page-break-after:auto;
    }
    table td {
      line-height: 30px;
    }
    .table {
      width: 85%;
    }
    .table td {
      border: 1px solid #EBEEF5;
      text-align: center;
      line-height: 30px;
    }

    .table tr:first-child {
      font-weight: bold;
      background: #f4f4f4;
    }
  </style>
</head>
<body>
<!--<div class="header">-->
<!--  <img src="https://dc-sz-1.oss-cn-shenzhen.aliyuncs.com/pdf/logo.png" />-->
<!--</div>-->
<div class="title">基本信息</div>
<table style="width:98%;margin:auto">
  <tr>
    <td align="right" width="100">企业名称：</td>
    <td>${corpName}</td>
    <td align="right" width="100">开票方式：</td>
    <td>
      <#if invoiceWay == 'PRE_PAY'>预付费充值开票
      <#elseif invoiceWay == 'POST_CHARGER'>预付费充电开票
      <#elseif invoiceWay == 'POST_SETTLEMENT'>后付费账单开票
      <#else>--
    </#if>
    </td>
  </tr>
  <tr>
    <td align="right" width="100">创建人：</td>
    <td>${createOpName}</td>
    <td align="right" width="100">创建时间：</td>
    <td>${createTime?string('yyyy-MM-dd HH:mm:ss')}</td>
  </tr>
  <tr>
    <td align="right" width="100">开票主体：</td>
    <td>${saleName}</td>
    <td align="right" width="100">商品行模板：</td>
    <td>${productTempName}</td>
  </tr>
</table>
<table style="width:98%;margin:15px auto">
  <tr>
    <td align="right" valign="top" width="100">开票内容：</td>
    <td>
      <table class="table" >
        <tr>
          <td>名称</td>
          <td>规格</td>
          <td>单位</td>
          <td>税率（%）</td>
          <td>数量</td>
          <td>单价</td>
          <td>实开金额（元）</td>
        </tr>
        <#if invoicingContent??>
        <#list invoicingContent as tmp>
        <tr>
          <td>${tmp.productName}</td>
          <td>${tmp.spec}</td>
          <td>${tmp.unit}</td>
          <td>${tmp.taxRate}</td>
          <td>
            <#if tmp.num??>
            ${tmp.num}
            </#if>
          </td>
          <td>
            <#if tmp.price??>
            ${tmp.price?string('0.000000')}
            </#if>
          </td>
          <td>${tmp.fixAmount}</td>
        </tr>
      </#list>
      <#elseif tempRefVo.detailVoList??>
            <#list tempRefVo.detailVoList as tmp>
            <tr>
              <td>${tmp.productName}</td>
              <td>${tmp.spec}</td>
              <td>${tmp.unit}</td>
              <td>${tmp.taxRate}</td>
              <td>
                <#if tmp.num??>
                ${tmp.num}
              </#if>
              </td>
              <td>
                <#if tmp.price??>
                ${tmp.price?string('0.000000')}
              </#if>
              </td>
              <td>${tmp.fixAmount}</td>
            </tr>
          </#list>
        <#else>
        <tr>
          <td colspan="5">暂无数据</td>
        </tr>
        </#if>
      </table>
      <table class="table" style="margin-top:20px">
        <tr>
          <td>汇总</td>
          <td>电费（元）</td>
          <td>服务费（元）</td>
          <td>停车费（元）</td>
          <td>技术服务费（元）</td>
          <td>总额（元）</td>
        </tr>
        <tr>
          <td>应开</td>
          <td>${actualElecFee}</td>
          <td>${actualServFee}</td>
          <td>--</td>
          <td>--</td>
          <td>${totalFee}</td>
        </tr>
        <tr>
          <td>实开</td>
          <td>${fixElecFee}</td>
          <td>${fixServFee}</td>
          <td>0</td>
          <td>${fixTechServFee}</td>
          <td>${fixTotalFee}</td>
        </tr>
      </table>
    </td>
  </tr>
</table>

<table style="width:98%;margin:auto">
  <tr>
    <td align="right" width="100">开票备注：</td>
    <td colspan="3">
      <#if invoicingRemark??>${invoicingRemark}
      <#else>--
    </#if>
    </td>
  </tr>
</table>
<table style="width:98%;margin:15px auto">
  <tr>
    <td align="right" valign="top" width="100">开票订单统计：</td>
    <td>
      <table class="table">
        <tr>
          <td>订单数</td>
          <td>订单总额</td>
          <td>可开票金额</td>
        </tr>
        <#if orderBiData??>
        <td>${orderBiData.orderAmount}</td>
        <td>${orderBiData.totalAmount}</td>
        <td>${orderBiData.invoiceAmount}</td>
        <#else>
        <tr>
        <td colspan="4">暂无数据</td>
        </tr>
        </#if>
      </table>
    </td>
  </tr>
</table>

<table style="width:98%;margin:15px auto">
  <tr>
    <td align="right" valign="top" width="100">订单分布：</td>
    <td>
      <table class="table">
        <tr>
          <td>站点</td>
          <td>运营属性</td>
          <td>订单数</td>
        </tr>
        <#if siteOrderData??>
        <#list siteOrderData as tmp>
          <tr>
            <td>${tmp.siteName}</td>
            <td>
              <#if tmp.gcType == 0>未知
              <#elseif tmp.gcType == 1>投建运营
              <#elseif tmp.gcType == 2>以租代售
              <#elseif tmp.gcType == 3>纯租赁
              <#elseif tmp.gcType == 4>EPC+O
              <#elseif tmp.gcType == 5>销售的代收代付
              <#elseif tmp.gcType == 6>代运营
              <#elseif tmp.gcType == 7>委托运营
              <#elseif tmp.gcType == 8>销售场站
              <#elseif tmp.gcType == 9>BOT
              <#elseif tmp.gcType == 10>收购
              <#elseif tmp.gcType == 11>停止运营
              <#elseif tmp.gcType == 12>合作运营
              <#elseif tmp.gcType == 13>销售+直付通
              <#elseif tmp.gcType == 14>测试
            </#if>
            </td>
            <td>${tmp.amount}</td>
          </tr>
      </#list>
        <#else>
        <tr>
        <td colspan="3">暂无数据</td>
        </tr>
        </#if>
      </table>
    </td>
  </tr>
</table>

<#if invoiceWay=='POST_SETTLEMENT'>
<table style="width:98%;margin:15px auto">
  <tr>
    <td align="right" valign="top" width="100">账单信息：</td>
    <td>
      <table class="table">
        <tr>
          <td>账单电费(元)</td>
          <td>账单服务费(元)</td>
          <td>账单总额(元)</td>
        </tr>
        <#if settleBiData??>
        <td>${settleBiData.totalElecAmount}</td>
        <td>${settleBiData.totalServAmount}</td>
        <td>${settleBiData.totalAmount}</td>
        <#else>
        <tr>
          <td colspan="4">暂无数据</td>
        </tr>
      </#if>
</table>
</td>
</tr>
</table>
</#if>

<#if invoiceWay!='UNKNOWN' && invoiceWay!='PRE_PAY'>
<table style="width:98%;margin:15px auto">
  <tr>
    <td align="right" valign="top" width="100">订单原价：</td>
    <td>
      <table class="table">
        <tr>
          <td></td>
          <td>总</td>
          <td>尖</td>
          <td>峰</td>
          <td>平</td>
          <td>谷</td>
        </tr>
        <#if orderTimeShareBi??>
        <#list orderTimeShareBi as tmp>
        <tr>
          <td>${tmp.title}</td>
          <td>${tmp.total}</td>
          <td>${tmp.jian}</td>
          <td>${tmp.feng}</td>
          <td>${tmp.ping}</td>
          <td>${tmp.gu}</td>
        </tr>
        </#list>
        <#else>
        <tr>
          <td colspan="5">暂无数据</td>
        </tr>
        </#if>
      </table>
    </td>
  </tr>
</table>
</#if>
<table style="width:98%;margin:15px auto">
  <tr>
    <td align="right" valign="top" width="100">回款情况:</td>
    <td>
      <#if returnFlag??>
        <#if returnFlag == 0>未回款
        <#elseif returnFlag == 1>已回款
        </#if>
        <#else>--
    </#if>
    </td>
  </tr>
</table>

<table style="width:98%;margin:15px auto">
  <tr>
    <td align="right" valign="top" width="100">回款计划：</td>
    <td>
      <table class="table">
        <tr>
          <td></td>
          <td>回款金额（元）</td>
          <td>回款时间</td>
        </tr>
        <#if returnPlanVoList??>
        <#list returnPlanVoList as tmp>
          <tr>
            <td>${tmp.planTitle}</td>
            <td>
              <#if tmp.planMoney??>
                ${tmp.planMoney}
              </#if>
            </td>
            <td>
              <#if tmp.planTime??>
                ${tmp.planTime?string('yyyy-MM-dd HH:mm:ss')}
              </#if>
            </td>
          </tr>
        </#list>
        <#else>
        <tr>
        <td colspan="3">暂无数据</td>
        </tr>
        </#if>
      </table>
    </td>
  </tr>
</table>

<table style="width:98%;margin:15px auto">
  <tr>
    <td align="right" valign="top" width="100">上传图片：</td>
    <td>
      <#if images??>
      <#list images as img>
      <img src="${img}" width="100px" height="100px" style="margin-left: 10px"/>
    </#list>
  </#if>
    </td>
  </tr>
  <tr>
    <td align="right" valign="top" width="100">申请备注：</td>
    <td>
      <#if applyRemark??>${applyRemark}
      <#else>--
    </#if>
    </td>
  </tr>
</table>

<div class="title">抬头信息</div>

<#if invoiceType == 'ENTER_PROFESSION'>
<table style="width:98%;margin:auto">
  <tr>
    <td align="right" width="100">开票种类：</td>
    <td>企业专票</td>
    <td align="right" width="100">抬头名称：</td>
    <td>${invoiceName}</td>
  </tr>
  <tr>
    <td align="right" width="100">税号：</td>
    <td>
      <#if tin??>${tin}
      <#else>--
    </#if>
    </td>
    <td align="right" width="100">开户银行：</td>
    <td>
      <#if bank??>${bank}
      <#else>--
    </#if>
    </td>
  </tr>
  <tr>
    <td align="right" width="100">银行账号：</td>
    <td>
      <#if bankAccount??>${bankAccount}
      <#else>--
    </#if>
    </td>
    <td align="right" width="100">企业地址：</td>
    <td>
      <#if address??>${address}
      <#else>--
    </#if>
    </td>
  </tr>
  <tr>
    <td align="right" width="100">企业电话：</td>
    <td>
      <#if tel??>${tel}
      <#else>--
    </#if>
    </td>
    <td align="right" width="100">收件人姓名：</td>
    <td>
      <#if receiverName??>${receiverName}
      <#else>--
    </#if>
    </td>
  </tr>
  <tr>
    <td align="right" width="100">收件人电话：</td>
    <td>
      <#if receiverMobilePhone??>${receiverMobilePhone}
      <#else>--
    </#if>
    </td>
    <td align="right" width="100">收件人地址：</td>
    <td>
      ${receiverProvince}${receiverCity}${receiverArea}${receiverAddress}
    </td>
  </tr>
</table>
</#if>

<#if invoiceType == 'ENTER_COMMON'>
<table style="width:98%;margin:auto">
  <tr>
    <td align="right" width="100">开票种类：</td>
    <td>企业普票</td>
    <td align="right" width="100">抬头名称：</td>
    <td>${invoiceName}</td>
  </tr>
  <tr>
    <td align="right" width="100">税号：</td>
    <td>
      <#if tin??>${tin}
      <#else>--
    </#if>
    </td>
    <td align="right" width="100">电子邮箱：</td>
    <td>
      <#if email??>${email}
      <#else>--
    </#if>
    </td>
  </tr>
</table>
</#if>

<#if invoiceType == 'PER_COMMON'>
<table style="width:98%;margin:auto">
  <tr>
    <td align="right" width="100">开票种类：</td>
    <td>个人普票</td>
    <td align="right" width="100">抬头名称：</td>
    <td>${invoiceName}</td>
  </tr>
  <tr>
    <td align="right" width="100">电子邮箱：</td>
    <td>
      <#if email??>${email}
      <#else>--
      </#if>
    </td>
  </tr>
</table>
</#if>
<div class="title">审核信息</div>
<table style="width:98%;margin:auto">
  <tr>
    <td align="right" width="100">审核人：</td>
    <td>
      <#if auditName??>${auditName}
      <#else>--
      </#if>
    </td>
    <td align="right" width="100">审核时间：</td>
    <td>
      <#if auditTime??>${auditTime?string('yyyy-MM-dd HH:mm:ss')}
      <#else>--
      </#if>
    </td>
  </tr>
  <tr>
    <td align="right" width="100">审核人姓名：</td>
    <td>
      <#if auditFullName??>${auditFullName}
      <#else>--
    </#if>
    </td>
    <td align="right" width="100">审核结果：</td>
    <td>
      <#if auditResult??>
        <#if auditResult>通过
        <#else>不通过
        </#if>
      <#else>
      --
    </#if>
    </td>
  </tr>
  <tr>
    <td align="right" width="100">审核备注：</td>
    <td>
      <#if auditRemark??>${auditRemark}
      <#else>--
      </#if>
    </td>
  </tr>
</table>
<div class="title">开票结果</div>
<table style="width:98%;margin:auto">
  <tr>
    <td align="right" width="100">开票结果：</td>
    <td>
      <#if status == 'NOT_SUBMITTED'>草稿
      <#elseif status == 'SUBMITTED'>审核中
      <#elseif status == 'AUDIT_FAILED'>审核未通过
      <#elseif status == 'REVIEWED'>开票中
      <#elseif status == 'INVOICING_FAIL'>开票未通过
      <#elseif status == 'COMPLETED'>已开具
      <#elseif status == 'INVALID'>已作废
      <#else>--
    </#if>
    </td>
    <td align="right" width="100">开具方式：</td>
    <td>
      <#if channel??>
          <#if channel == 'AUTO'>系统自动开具
          <#else>后台手动开具
          </#if>
      <#else>--
    </#if>
    </td>
  </tr>
  <tr>
    <td align="right" width="100">发票号码：</td>
    <td>
      <#if invoiceNumber??>${invoiceNumber}
      <#else>--
    </#if>
    </td>
    <td align="right" width="100">发票代码：</td>
    <td>
      <#if invoiceCode??>${invoiceCode}
      <#else>--
    </#if>
    </td>
  </tr>
  <tr>
    <td align="right" width="100">附属信息：</td>
    <td>
      <#if issuedRemark??>${issuedRemark}
      <#else>--
    </#if>
    </td>
    <td align="right" width="100">失败原因：</td>
    <td>
      <#if failRemark??>${failRemark}
      <#else>--
    </#if>
    </td>
  </tr>
  <tr>
    <td align="right" width="100">发票文件：</td>
    <td colspan="">
      <#if urlList??>
        <#list urlList as img>
        <img src="${img}" width="100px" height="100px" style="margin-left: 10px"/>
        </#list>
      </#if>
    </td>
  </tr>
</table>
</body>
</html>