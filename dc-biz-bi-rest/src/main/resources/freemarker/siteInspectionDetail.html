<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Title</title>
    <style>
        body {
            font-family: SimHei;
        }

        .position1 {
            padding-left: 10px;
            height: 125px;
        }

        .position2 {
            text-align: right;
            padding-right: 180px;
            height: 10px;
        }

        .comment {
            height: 150px;
        }

        .conclusion {
            height: 150px;
        }

        table {
            border-collapse: collapse;
            border-spacing: 0;
        }

        .header img {
            width: 385px;
        }

        .smallHeader {
            background-color: #acbeee;
            height: 30px;
        }

        .vertical>p {
            width: 20px;
            display: inline-block;
            writing-mode: vertical-rl;
            -webkit-writing-mode: vertical-rl;
            -ms-writing-mode: vertical-rl;
        }
        .signImage {
            width: 50px;
            height:20px;
            display: inline-block;
        }
    </style>
</head>

<body>
<!--    <div class="header">-->
<!--        <img src="https://dc-sz-1.oss-cn-shenzhen.aliyuncs.com/pdf/logo.png" />-->
<!--    </div>-->
    <table border="1" style="width: 100%;">
        <tr>
            <td align="center" colspan="8" style="height: 50px;">${siteName}巡检报告</td>
        </tr>
        <tr>
            <td align="left" colspan="4">场站地址：${siteAddress}</td>
            <td align="left" colspan="4">场站性质：${siteTypeDesc}</td>
        </tr>
        <tr>
            <td align="left" colspan="2">巡检单号：${No}</td>
            <td align="left" colspan="2">巡检人：${opName}</td>
            <#if reportTime??>
                <td align="left" colspan="2">巡检时间：${reportTime?string("yyyy-MM-dd")}</td>
                <#else>
                    <td align="left" colspan="2">巡检时间：</td>
            </#if>
            <td align="left" colspan="2">质检人：${qcName}</td>
        </tr>
    </table>
    <table border="1" style="width: 100%;">
        <tr class="smallHeader">
            <td align="center" colspan="2">
                巡检<br/>项目
            </td>
            <td align="center" colspan="3">
                技术要求
            </td>
            <td align="center" colspan="1" style="width: 40px;">
                是否合格
            </td>
            <td align="center" colspan="1">
                问题桩
            </td>
            <td align="center" colspan="1">
                备注
            </td>
        </tr>
        <tr>
            <td rowspan="11" align="center" class="vertical">
                <p>常规检查</p>
            </td>
            <td rowspan="11"></td>
            <td colspan="3">
                充电桩、终端、安全指示牌及电价公示牌等是否正常
            </td>
            <td align="center">
                <#if evseReport.isSignBoardNormal?? && evseReport.isSignBoardNormal.normal??>
                    <#if evseReport.isSignBoardNormal.normal>
                        是
                        <#else>
                            <span style="color: red; font-weight: bold;">否</span>
                    </#if>
                    <#else>
                        --
                </#if>
            </td>
            <td>
                <#if evseReport.isSignBoardNormal??>
                    <#list evseReport.isSignBoardNormal.evseNoList as evseNo>
                        ${evseNo}
                    </#list>
                </#if>
            </td>
            <td>
                <#if evseReport.isSignBoardNormal??>
                    ${evseReport.isSignBoardNormal.remark?default('')}
                </#if>
            </td>
        </tr>
        <tr>
            <td colspan="3">
                整机外表面平整、无明显锈蚀、起泡、划伤等不良现象， 封堵齐全
            </td>
            <td align="center">
                <#if evseReport.isEvseAppearanceNormal?? && evseReport.isEvseAppearanceNormal.normal??>
                    <#if evseReport.isEvseAppearanceNormal.normal>
                        是
                        <#else>
                            <span style="color: red; font-weight: bold;">否</span>
                    </#if>
                    <#else>
                        --
                </#if>
            </td>
            <td>
                <#if evseReport.isEvseAppearanceNormal??>
                    <#list evseReport.isEvseAppearanceNormal.evseNoList as evseNo>
                        ${evseNo}
                    </#list>
                </#if>
            </td>
            <td>
                <#if evseReport.isEvseAppearanceNormal??>
                    ${evseReport.isEvseAppearanceNormal.remark?default('')}
                </#if>
            </td>
        </tr>
        <tr>
            <td colspan="3">
                枪线无变形弯曲、破皮、枪锁功能正常
            </td>
            <td align="center">
                <#if evseReport.isPlugLineNormal?? && evseReport.isPlugLineNormal.normal??>
                    <#if evseReport.isPlugLineNormal.normal>
                        是
                        <#else>
                            <span style="color: red; font-weight: bold;">否</span>
                    </#if>
                    <#else>
                        --
                </#if>
            </td>
            <td>
                <#if evseReport.isPlugLineNormal??>
                    <#list evseReport.isPlugLineNormal.evseNoList as evseNo>
                        ${evseNo}
                    </#list>
                </#if>
            </td>
            <td>
                <#if evseReport.isPlugLineNormal??>
                    ${evseReport.isPlugLineNormal.remark?default('')}
                </#if>
            </td>
        </tr>
        <tr>
            <td colspan="3">
                充电桩分流器、接触器无烧蚀老化痕迹
            </td>
            <td align="center">
                <#if evseReport.isEvseShuntNormal?? && evseReport.isEvseShuntNormal.normal??>
                    <#if evseReport.isEvseShuntNormal.normal>
                        是
                        <#else>
                            <span style="color: red; font-weight: bold;">否</span>
                    </#if>
                    <#else>
                        --
                </#if>
            </td>
            <td>
                <#if evseReport.isEvseShuntNormal??>
                    <#list evseReport.isEvseShuntNormal.evseNoList as evseNo>
                        ${evseNo}
                    </#list>
                </#if>
            </td>
            <td>
                <#if evseReport.isEvseShuntNormal??>
                    ${evseReport.isEvseShuntNormal.remark?default('')}
                </#if>
            </td>
        </tr>
        <tr>
            <td colspan="3">
                机箱内清洁无杂物
            </td>
            <td align="center">
                <#if evseReport.isInternalClean?? && evseReport.isInternalClean.normal??>
                    <#if evseReport.isInternalClean.normal>
                        是
                        <#else>
                            <span style="color: red; font-weight: bold;">否</span>
                    </#if>
                    <#else>
                        --
                </#if>
            </td>
            <td>
                <#if evseReport.isInternalClean??>
                    <#list evseReport.isInternalClean.evseNoList as evseNo>
                        ${evseNo}
                    </#list>
                </#if>
            </td>
            <td>
                <#if evseReport.isInternalClean??>
                    ${evseReport.isInternalClean.remark?default('')}
                </#if>
            </td>
        </tr>
        <tr>
            <td colspan="3">
                散热正常
            </td>
            <td align="center">
                <#if evseReport.isCoolingNormal?? && evseReport.isCoolingNormal.normal??>
                    <#if evseReport.isCoolingNormal.normal>
                        是
                        <#else>
                            <span style="color: red; font-weight: bold;">否</span>
                    </#if>
                    <#else>
                        --
                </#if>
            </td>
            <td>
                <#if evseReport.isCoolingNormal??>
                    <#list evseReport.isCoolingNormal.evseNoList as evseNo>
                        ${evseNo}
                    </#list>
                </#if>
            </td>
            <td>
                <#if evseReport.isCoolingNormal??>
                    ${evseReport.isCoolingNormal.remark?default('')}
                </#if>
            </td>
        </tr>
        <tr>
            <td colspan="3">
                机柜前门、后门开关自如、锁紧可靠
            </td>
            <td align="center">
                <#if evseReport.isDoorLockNormal?? && evseReport.isDoorLockNormal.normal??>
                    <#if evseReport.isDoorLockNormal.normal>
                        是
                        <#else>
                            <span style="color: red; font-weight: bold;">否</span>
                    </#if>
                    <#else>
                        --
                </#if>
            </td>
            <td>
                <#if evseReport.isDoorLockNormal??>
                    <#list evseReport.isDoorLockNormal.evseNoList as evseNo>
                        ${evseNo}
                    </#list>
                </#if>
            </td>
            <td>
                <#if evseReport.isDoorLockNormal??>
                    ${evseReport.isDoorLockNormal.remark?default('')}
                </#if>
            </td>
        </tr>
        <tr>
            <td colspan="3">
                远程功能开启是否正常，后台监控无离线情况
            </td>
            <td align="center">
                <#if evseReport.isRemoteFeaturesNormal?? && evseReport.isRemoteFeaturesNormal.normal??>
                    <#if evseReport.isRemoteFeaturesNormal.normal>
                        是
                        <#else>
                            <span style="color: red; font-weight: bold;">否</span>
                    </#if>
                    <#else>
                        --
                </#if>
            </td>
            <td>
                <#if evseReport.isRemoteFeaturesNormal??>
                    <#list evseReport.isRemoteFeaturesNormal.evseNoList as evseNo>
                        ${evseNo}
                    </#list>
                </#if>
            </td>
            <td>
                <#if evseReport.isRemoteFeaturesNormal??>
                    ${evseReport.isRemoteFeaturesNormal.remark?default('')}
                </#if>
            </td>
        </tr>
        <tr>
            <td colspan="3">
                电价参数一级充电时电价是否正常
            </td>
            <td align="center">
                <#if evseReport.isElectrovalenceNormal?? && evseReport.isElectrovalenceNormal.normal??>
                    <#if evseReport.isElectrovalenceNormal.normal>
                        是
                        <#else>
                            <span style="color: red; font-weight: bold;">否</span>
                    </#if>
                    <#else>
                        --
                </#if>
            </td>
            <td>
                <#if evseReport.isElectrovalenceNormal??>
                    <#list evseReport.isElectrovalenceNormal.evseNoList as evseNo>
                        ${evseNo}
                    </#list>
                </#if>
            </td>
            <td>
                <#if evseReport.isElectrovalenceNormal??>
                    ${evseReport.isElectrovalenceNormal.remark?default('')}
                </#if>
            </td>
        </tr>
        <tr>
            <td colspan="3">
                充电记录无丢失及异常故障代码
            </td>
            <td align="center">
                <#if evseReport.isRechargeRecordNormal?? && evseReport.isRechargeRecordNormal.normal??>
                    <#if evseReport.isRechargeRecordNormal.normal>
                        是
                        <#else>
                            <span style="color: red; font-weight: bold;">否</span>
                    </#if>
                    <#else>
                        --
                </#if>
            </td>
            <td>
                <#if evseReport.isRechargeRecordNormal??>
                    <#list evseReport.isRechargeRecordNormal.evseNoList as evseNo>
                        ${evseNo}
                    </#list>
                </#if>
            </td>
            <td>
                <#if evseReport.isRechargeRecordNormal??>
                    ${evseReport.isRechargeRecordNormal.remark?default('')}
                </#if>
            </td>
        </tr>
        <tr>
            <td colspan="3">
                抽查检测APP、微信扫码、刷卡充电，充电桩能正常响应
            </td>
            <td align="center">
                <#if evseReport.isWorksProperly?? && evseReport.isWorksProperly.normal??>
                    <#if evseReport.isWorksProperly.normal>
                        是
                        <#else>
                            <span style="color: red; font-weight: bold;">否</span>
                    </#if>
                    <#else>
                        --
                </#if>
            </td>
            <td>
                <#if evseReport.isWorksProperly??>
                    <#list evseReport.isWorksProperly.evseNoList as evseNo>
                        ${evseNo}
                    </#list>
                </#if>
            </td>
            <td>
                <#if evseReport.isWorksProperly??>
                    ${evseReport.isWorksProperly.remark?default('')}
                </#if>
            </td>
        </tr>
        <tr>
            <td rowspan="9" align="center" class="vertical">
                <p>环境安全</p>
            </td>
            <td align="center" rowspan="2" class="vertical">
                <p>排水</p>
            </td>
            <td colspan="3">
                电缆沟是否有积水现象，场站地面基础是否有塌陷、下沉、积水情况
            </td>
            <td align="center">
                <#if envReport.isHydrops??>
                    <#if envReport.isHydrops>
                        是
                        <#else>
                            <span style="color: red; font-weight: bold;">否</span>
                    </#if>
                    <#else>
                        无
                </#if>
            </td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td colspan="3">
                排水管道井、沟渠有无堵塞
            </td>
            <td align="center">
                <#if envReport.isPipeBlockage??>
                    <#if envReport.isPipeBlockage>
                        是
                        <#else>
                            <span style="color: red; font-weight: bold;">否</span>
                    </#if>
                    <#else>
                        无
                </#if>
            </td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td align="center" class="vertical">
                <p>照明</p>
            </td>
            <td colspan="3">
                场站照明运行是否正常
            </td>
            <td align="center">
                <#if envReport.isLightingNormal??>
                    <#if envReport.isLightingNormal>
                        是
                        <#else>
                            <span style="color: red; font-weight: bold;">否</span>
                    </#if>
                    <#else>
                        无
                </#if>
            </td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td align="center" rowspan="3" class="vertical">
                <p>消防</p>
            </td>
            <td colspan="3">
                消防警示标志是否完整醒目
            </td>
            <td align="center">
                <#if envReport.isLogoEyeCatching??>
                    <#if envReport.isLogoEyeCatching>
                        是
                        <#else>
                            <span style="color: red; font-weight: bold;">否</span>
                    </#if>
                    <#else>
                        无
                </#if>
            </td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td colspan="3">
                灭火器是否在有效期内、月检卡是否正常
            </td>
            <td align="center">
                <#if envReport.isExtinguisherNormal??>
                    <#if envReport.isExtinguisherNormal>
                        是
                        <#else>
                            <span style="color: red; font-weight: bold;">否</span>
                    </#if>
                    <#else>
                        无
                </#if>
            </td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td colspan="3">
                消防沙箱砂体是否干燥、充足
            </td>
            <td align="center">
                <#if envReport.isSandBoxDry??>
                    <#if envReport.isSandBoxDry>
                        是
                        <#else>
                            <span style="color: red; font-weight: bold;">否</span>
                    </#if>
                    <#else>
                        无
                </#if>
            </td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td align="center" class="vertical">
                <p>监控</p>
            </td>
            <td colspan="3">
                监控摄像系统是否安装运行正常
            </td>
            <td align="center">
                <#if envReport.isMonitorNormal??>
                    <#if envReport.isMonitorNormal>
                        是
                        <#else>
                            <span style="color: red; font-weight: bold;">否</span>
                    </#if>
                    <#else>
                        无
                </#if>
            </td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td align="center" rowspan="2" class="vertical">
                <p>门禁</p>
            </td>
            <td colspan="3">
                移动伸缩门运行是否正常
            </td>
            <td align="center">
                <#if envReport.isDoorNormal??>
                    <#if envReport.isDoorNormal>
                        是
                        <#else>
                            <span style="color: red; font-weight: bold;">否</span>
                    </#if>
                    <#else>
                        无
                </#if>
            </td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td colspan="3">
                道闸是否起降正常
            </td>
            <td align="center">
                <#if envReport.isSignoNormal??>
                    <#if envReport.isSignoNormal>
                        是
                        <#else>
                            <span style="color: red; font-weight: bold;">否</span>
                    </#if>
                    <#else>
                        无
                </#if>
            </td>
            <td></td>
            <td></td>
        </tr>
        <tr>
            <td align="center" colspan="2">
                <p>备注</p>
            </td>
            <td colspan="6">
                <#if remark??>${remark}
                <#else>--
                </#if>
            </td>
        </tr>
        <tr>
            <td align="center" class="vertical">
                <p>场站照片</p>
            </td>
            <td></td>
            <td colspan="6">
                <#if photos??>
                    <#list photos as photo>
                        <img src="${photo.url}" width="100px" height="100px" />
                    </#list>
                </#if>
            </td>
        </tr>
    </table>

    <table border="1" style="width: 100%;margin-top: 30px;">
        <tr class="smallHeader">
            <td align="center" colspan="6">
                ${siteName}设备信息
            </td>
        </tr>
        <tr>
            <td colspan="2">
                直流桩数量: ${dcEvseNum}(台)
            </td>
            <td colspan="2">
                交流桩数量: ${acEvseNum}(台)
            </td>
            <td colspan="2">
                总数量: ${dcEvseNum + acEvseNum}(台)
            </td>
        </tr>
        <tr>
            <td align="center">
                设备名称
            </td>
            <td align="center">
                设备编号
            </td>
            <td align="center">
                规格功率(kW)
            </td>
            <td align="center">
                生产日期
            </td>
            <td align="center">
                软件程序版本号
            </td>
            <td align="center">
                产品编号
            </td>
        </tr>
        <#if evseList??>
            <#list evseList as evse>
                <tr>
                    <td>${evse.name!}</td>
                    <td>${evse.evseNo!}</td>
                    <td>${evse.power!}</td>
                    <#if evse.produceDate??>
                        <td>${(evse.produceDate)?string("yyyy-MM-dd")}</td>
                        <#else>
                            <td>--</td>
                    </#if>
                    <td>${evse.firmwareVer!}</td>
                    <td>${evse.physicalNo!"--"}</td>
                </tr>
            </#list>
        <#else>
            <tr>
                <td align="center">无</td>
                <td align="center">无</td>
                <td align="center">无</td>
                <td align="center">无</td>
                <td align="center">无</td>
                <td align="center">无</td>
            </tr>
        </#if>
        <tr>
            <td class="comment" colspan="6">
                <div style="width: 100%;height: 100%;">
                    <div class="position1">
                        客户评价及建议：
                        <div>
                            <#if isPerfect == 1>满意;
                                <#elseif isPerfect == 2>不满意;
                            </#if>
                            <#if advice??>
                                ${advice}
                            </#if>
                        </div>
                    </div>
                    <div class="position2">
                        客户签字：<img src="${signImage}" class="signImage" />
                    </div>
                </div>
            </td>
        </tr>
        <tr>
            <td class="conclusion" colspan="6">
                <div style="width: 100%;height: 100%;">
                    <div class="position1">
                        结论：
                    </div>
                    <div class="position2">
                        巡检人：
                    </div>
                </div>
            </td>
        </tr>
    </table>
</body>

</html>