<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Title</title>
    <style>
        body {
            font-family: SimHei;
        }

        .position1 {
            padding-left: 10px;
            height: 125px;
        }

        .position2 {
            text-align: right;
            padding-right: 180px;
        }

        .comment {
            height: 150px;
        }

        .h-50 {
            height: 50px;
        }

        .conclusion {
            height: 150px;
        }

        table {
            border-collapse: collapse;
            border-spacing: 0;
        }

        .header img {
            width: 385px;
        }

        .smallHeader {
            background-color: #acbeee;
            height: 30px;
        }
        .signImage {
            width: 50px;
            height:20px;
            display: inline-block;
        }
    </style>
</head>

<body>
<!--    <div class="header">-->
<!--        <img src="https://dc-sz-1.oss-cn-shenzhen.aliyuncs.com/pdf/logo.png" />-->
<!--    </div>-->
    <table border="1" style="width: 100%;">
        <tr>
            <td align="center" colspan="16" style="height: 50px;">${siteName}维修报告</td>
        </tr>
        <tr>
            <td align="left" colspan="8">场站地址：${siteAddress}</td>
            <td align="left" colspan="8">场站性质：
                <#if siteGcType == 0>未知
                <#elseif siteGcType == 1>投建运营
                <#elseif siteGcType == 2>以租代售
                <#elseif siteGcType == 3>纯租赁
                <#elseif siteGcType == 4>EPC+O
                <#elseif siteGcType == 5>销售的代收代付
                <#elseif siteGcType == 6>代运营
                <#elseif siteGcType == 7>委托运营
                </#if>
            </td>
        </tr>
        <tr>
            <td align="left" colspan="4" style="width: 200px;">维修单号：${ywOrderNo}</td>
            <td align="left" colspan="4">维修人：${maintName}</td>
            <#if maintTime??>
                <td align="left" colspan="4">维修时间：${maintTime?string('yyyy-MM-dd')}</td>
            <#else>
                <td align="left" colspan="4">维修时间：</td>
            </#if>
            <td align="left" colspan="4">质检人：${qcName}</td>
        </tr>
        <tr>
            <td rowspan="2" colspan="2" align="left">
                <p>故障描述</p>
            </td>
            <td class="h-50" align="left" colspan="6">文字</td>
            <td class="h-50" align="left" colspan="8">${faultDesc}
                <#if orderNo?? && orderNo != "">
                <span style="margin-left: 10px;">关联订单号: ${orderNo}</span>
                </#if>
            </td>
        </tr>
        <tr>
            <td align="left" colspan="6">图片</td>
            <td colspan="8">
                <#if faultImages??>
                    <#list faultImages as img>
                        <img src="${img}" width="100px" height="100px" />
                    </#list>
                </#if>
            </td>
        </tr>
        <tr>
            <td rowspan="4" colspan="2" align="left">
                <p>工作对象</p>
            </td>
            <td align="left" colspan="6">故障桩</td>
            <td align="left" colspan="8">
                <#if evseNameList??>
                    <#list evseNameList as evseName>
                    <div>${evseName};</div>
                    </#list>
                </#if>
            </td>
        </tr>
        <tr>
            <td align="left" colspan="6">是否过保质期</td>
            <td align="left" colspan="8">
                <#if overExpireDate??>
                    <#if overExpireDate>是
                    <#else><span style="color: red; font-weight: bold;">否</span>
                    </#if>
                <#else>
                    --
                </#if>
            </td>
        </tr>
        <tr>
            <td align="left" colspan="6">工单来源</td>
            <td align="left" colspan="8">
                <#if sourceType??>
                    <#if sourceType.code == 2>C端客户
                    <#elseif sourceType.code == 20>商户
                    <#elseif sourceType.code == 23>运维
                    <#elseif sourceType.code == 30>客服
                    <#elseif sourceType.code == 99>其他
                    <#else>未知
                    </#if>
                <#else>--
                </#if>
            </td>
        </tr>
        <tr>
            <td align="left" colspan="6">联系方式</td>
            <td align="left" colspan="8">${createUserPhone}</td>
        </tr>
        <tr>
            <td rowspan="5" colspan="2" align="left">
                <p>车辆信息</p>
            </td>
            <td align="left" colspan="6">车辆品牌</td>
            <td align="left" colspan="8">
                <#if carInfo?? && carInfo.brand??>${carInfo.brand}
                <#else>--
                </#if>
            </td>
        </tr>
        <tr>
            <td align="left" colspan="6">车辆型号</td>
            <td align="left" colspan="8">
                <#if carInfo?? && carInfo.model??>${carInfo.model}
                <#else>--
                </#if>
            </td>
        </tr>
        <tr>
            <td align="left" colspan="6">需求电压（V）</td>
            <td align="left" colspan="8">
                <#if carInfo?? && carInfo.needVoltage??>${carInfo.needVoltage}
                <#else>--
                </#if>
            </td>
        </tr>
        <tr>
            <td align="left" colspan="6">需求电流（V）</td>
            <td align="left" colspan="8">
                <#if carInfo?? && carInfo.needCurrent??>${carInfo.needCurrent}
                <#else>--
                </#if>
            </td>
        </tr>
        <tr>
            <td align="left" colspan="6">其他充电桩是否正常</td>
            <td align="left" colspan="8">
                <#if carInfo?? && carInfo.otherEvse??>
                    <#if carInfo.otherEvse>是
                    <#else><span style="color: red; font-weight: bold;">否</span>
                    </#if>
                <#else>--
                </#if>
            </td>
        </tr>
        <tr>
            <td class="h-50" align="left" colspan="2">可能原因</td>
            <td class="h-50" align="left" colspan="14">${faultReason}</td>
        </tr>
        <tr>
            <td class="h-50" align="left" colspan="2">检查步骤</td>
            <td class="h-50" align="left" colspan="14">${checkStep}</td>
        </tr>
        <tr>
            <td class="h-50" align="left" colspan="2">处理措施<br />及结果</td>
            <td class="h-50" align="left" colspan="14">${dealProcess}</td>
        </tr>
        <tr>
            <td align="left" colspan="2">更换器件</td>
            <td colspan="14">
                ${goodsStr}
            </td>
        </tr>
        <tr>
            <td align="left"  colspan="2">维修图片</td>
            <td colspan="14">
                <#if images??>
                    <#list images as img>
                        <img src="${img.url}" width="100px" height="100px" />
                    </#list>
                </#if>
            </td>
        </tr>
        <tr>
            <td class="comment" colspan="16">
                <div style="width: 100%;height: 100%;">
                    <div class="position1">
                        客户评价及建议：
                        <div>
                            <#if isPerfect == 1>满意;
                                <#elseif isPerfect == 2>不满意;
                            </#if>
                            <#if advice??>
                                ${advice}
                            </#if>
                        </div>
                    </div>
                    <div class="position2">
                        客户签字： <img src="${signImage}"  class="signImage"/>
                    </div>
                </div>
            </td>
        </tr>
        <tr>
            <td class="conclusion" colspan="16">
                <div style="width: 100%;height: 100%; padding-bottom: 10px;">
                    <div class="position1">
                        结论：
                    </div>
                    <div class="position2">
                        维修人：
                    </div>
                </div>
            </td>
        </tr>
    </table>
</body>

</html>