<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <title>${ oaName!'--' }</title>
  <style>
      body {
          margin: 0;
          padding: 0;
          font-family: SimHei;
          width: 100%;
      }
      table {
          width: 100%;
          border-right: 1px solid #cfcece;
          border-bottom: 1px solid #cfcece;
      }
      table th {
          border-left: 1px solid #cfcece;
          border-top: 1px solid #cfcece;
      }

      table td {
          border-left: 1px solid #cfcece;
          border-top: 1px solid #cfcece;
      }

      td, th {
          font-size: 1em;
          padding: 4px 2px;
          color: #000;
      }

      th {
          font-size: 1.1em;
          text-align: left;
          padding-top: 5px;
          padding-bottom: 4px;
      }

      .t-title {
        font-size: 1.2em;
        background-color: #f1f1f1;
        padding-top: 16px;
        padding-bottom: 16px;
      }

      .th_title {
        color: #7f7f7f;
      }
      .th_title_bg {
        color: #7f7f7f;
        background-color: #f1f1f1;
      }

      .item_label {
        width: 80px;
      }
      .item_label_2 {
        width: 100px;
      }
      .center {
        text-align: center;
      }
      .font-size-18 {
        font-size: 18px !important;
      }
      .font-size-16 {
        font-size: 16px !important;
      }
      .font-size-14 {
        font-size: 14px !important;
      }
      .font-size-12 {
        font-size: 12px !important;
      }
      .font-size-10 {
        font-size: 10px !important;
      }
      .color_7f7f7f {
        color: #7f7f7f;
      }
      .list_non_data {
        color: #7f7f7f;
        font-style: italic;
        text-align: center;
      }
      .delete-line {
        text-decoration: line-through;
      }
      .font-bold {
        font-weight: bold;
      }
      .work-break {
        word-break: break-all;
        word-wrap: break-word;
      }
  </style>
</head>
<body>
<!-- 文档标题: 流程名称 -->
<#include "./oa/oa_pdf_header.html">

<!-- 申请内容 -->
<#if oaDefKey=="pay_elec_fee">
<#include "./oa/oa_cnt_pay_elec_fee.html">
<#elseif oaDefKey=="site_distribution">
<#include "./oa/oa_cnt_site_distribution.html">
<#elseif oaDefKey=="income_site_oa">
<#include "./oa/oa_cnt_income_site_oa.html">
<#elseif oaDefKey=="rent_payment_process">
<#include "./oa/oa_cnt_rent_payment_process.html">
<#elseif oaDefKey=="charge_fee">
<#include "./oa/oa_cnt_charge_fee.html">
<#elseif oaDefKey=="other_fee_pay_apply">
<#include "./oa/oa_cnt_other_fee_pay_apply.html">
<#elseif oaDefKey=="recharge_process">
<#include "./oa/oa_cnt_recharge_process.html">
<#elseif oaDefKey=="billing_process">
<#include "./oa/oa_cnt_billing_process.html">
<#elseif oaDefKey=="deposit_process">
<#include "./oa/oa_cnt_deposit_process.html">
<#elseif oaDefKey=="prepaid_order_invoicing_process">
<#include "./oa/oa_cnt_prepaid_order.html">
<#else>
<#include "./oa/oa_cnt_default.html">
</#if>

<!-- 申请参考 -->
<#assign referenceKeys = ["other_fee_pay_apply", "pay_elec_fee","site_distribution","income_site_oa","rent_payment_process"]>
<#if referenceKeys?seq_contains(oaDefKey)>
<#include "./oa/oa_audit_reference.html">
</#if>

<!-- 处理人意见/附件 -->
<#include "./oa/oa_proc_inst_comment.html">

<!-- 文档脚注: 流程ID -->
<#include "./oa/oa_pdf_footer.html">
</body>
</html>