<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Title</title>
    <style>
        body {
            font-family: SimHei;
        }

        .position1 {
            padding-left: 10px;
            height: 125px;
        }

        .position2 {
            text-align: right;
            padding-right: 180px;
            height: 10px;
        }

        .comment {
            height: 150px;
        }

        .conclusion {
            height: 150px;
        }

        table {
            border-collapse: collapse;
            border-spacing: 0;
        }

        .header img {
            width: 385px;
        }

        .smallHeader {
            background-color: #acbeee;
            height: 30px;
        }

        .vertical>p {
            width: 20px;
            display: inline-block;
            writing-mode: vertical-rl;
            -webkit-writing-mode: vertical-rl;
            -ms-writing-mode: vertical-rl;
        }
        .signImage {
            width: 50px;
            height:20px;
            display: inline-block;
        }
    </style>
</head>

<body>
<!--    <div class="header">-->
<!--        <img src="https://dc-sz-1.oss-cn-shenzhen.aliyuncs.com/pdf/logo.png" />-->
<!--    </div>-->
    <table border="1" style="width: 100%;">
        <tr>
            <td align="center" colspan="8" style="height: 50px;">${siteName}巡检报告</td>
        </tr>
        <tr>
            <td align="left" colspan="4">场站地址：${siteAddress}</td>
        </tr>
        <tr>
            <td align="left" colspan="2">巡检单号：${No}</td>
            <td align="left" colspan="2">巡检人：${opName}</td>
            <#if reportTime??>
                <td align="left" colspan="2">巡检时间：${reportTime?string("yyyy-MM-dd")}</td>
                <#else>
                    <td align="left" colspan="2">巡检时间：</td>
            </#if>
            <td align="left" colspan="2">质检人：${qcName}</td>
        </tr>
    </table>
    <table border="1" style="width: 100%;">
        <tr class="smallHeader">
            <td align="center" colspan="2">
                巡检<br/>项目
            </td>
            <td align="center" colspan="3">
                技术要求
            </td>
            <td align="center" colspan="1" style="width: 40px;">
                是否合格
            </td>
            <td align="center" colspan="1">
                备注
            </td>
        </tr>
        <tr>
            <td rowspan="11" align="center" class="vertical" colspan="2">
                <p>设备常规检查</p>
            </td>
            <td colspan="3">
                EMS 正常
            </td>
            <td align="center">
                <#if essReport.isEMSNormal?? && essReport.isEMSNormal.normal??>
                    <#if essReport.isEMSNormal.normal>
                        是
                        <#else>
                            <span style="color: red; font-weight: bold;">否</span>
                    </#if>
                    <#else>
                        --
                </#if>
            </td>
            <td>
                <#if essReport.isEMSNormal??>
                    ${essReport.isEMSNormal.remark?default('')}
                </#if>
            </td>
        </tr>
        <tr>
            <td colspan="3">
                PCS 正常
            </td>
            <td align="center">
                <#if essReport.isPCSNormal?? && essReport.isPCSNormal.normal??>
                    <#if essReport.isPCSNormal.normal>
                        是
                        <#else>
                            <span style="color: red; font-weight: bold;">否</span>
                    </#if>
                    <#else>
                        --
                </#if>
            </td>
            <td>
                <#if essReport.isPCSNormal??>
                    ${essReport.isPCSNormal.remark?default('')}
                </#if>
            </td>
        </tr>
        <tr>
            <td colspan="3">
                集控正常
            </td>
            <td align="center">
                <#if essReport.isCentralControlNormal?? && essReport.isCentralControlNormal.normal??>
                    <#if essReport.isCentralControlNormal.normal>
                        是
                        <#else>
                            <span style="color: red; font-weight: bold;">否</span>
                    </#if>
                    <#else>
                        --
                </#if>
            </td>
            <td>
                <#if essReport.isCentralControlNormal??>
                    ${essReport.isCentralControlNormal.remark?default('')}
                </#if>
            </td>
        </tr>
        <tr>
            <td colspan="3">
                电池堆正常
            </td>
            <td align="center">
                <#if essReport.isBatteryStackNormal?? && essReport.isBatteryStackNormal.normal??>
                <#if essReport.isBatteryStackNormal.normal>
                是
                <#else>
                <span style="color: red; font-weight: bold;">否</span>
            </#if>
            <#else>
            --
        </#if>
        </td>
        <td>
            <#if essReport.isBatteryStackNormal??>
            ${essReport.isBatteryStackNormal.remark?default('')}
        </#if>
        </td>
        </tr>
        <tr>
            <td colspan="3">
                电池簇正常
            </td>
            <td align="center">
                <#if essReport.isBatteryClusterNormal?? && essReport.isBatteryClusterNormal.normal??>
                <#if essReport.isBatteryClusterNormal.normal>
                是
                <#else>
                <span style="color: red; font-weight: bold;">否</span>
            </#if>
            <#else>
            --
        </#if>
        </td>
        <td>
            <#if essReport.isBatteryClusterNormal??>
            ${essReport.isBatteryClusterNormal.remark?default('')}
        </#if>
        </td>
        </tr>
        <tr>
            <td colspan="3">
                关口电表正常
            </td>
            <td align="center">
                <#if essReport.isGridGatewayMeterNormal?? && essReport.isGridGatewayMeterNormal.normal??>
                <#if essReport.isGridGatewayMeterNormal.normal>
                是
                <#else>
                <span style="color: red; font-weight: bold;">否</span>
            </#if>
            <#else>
            --
        </#if>
        </td>
        <td>
            <#if essReport.isGridGatewayMeterNormal??>
            ${essReport.isGridGatewayMeterNormal.remark?default('')}
        </#if>
        </td>
        </tr>
        <tr>
            <td colspan="3">
                并网点电表正常
            </td>
            <td align="center">
                <#if essReport.isEssGatewayMeterNormal?? && essReport.isEssGatewayMeterNormal.normal??>
                <#if essReport.isEssGatewayMeterNormal.normal>
                是
                <#else>
                <span style="color: red; font-weight: bold;">否</span>
            </#if>
            <#else>
            --
        </#if>
        </td>
        <td>
            <#if essReport.isEssGatewayMeterNormal??>
            ${essReport.isEssGatewayMeterNormal.remark?default('')}
        </#if>
        </td>
        </tr>
        <tr>
            <td colspan="3">
                高压侧用电电表正常
            </td>
            <td align="center">
                <#if essReport.isHighVoltageSideMeterNormal?? && essReport.isHighVoltageSideMeterNormal.normal??>
                <#if essReport.isHighVoltageSideMeterNormal.normal>
                是
                <#else>
                <span style="color: red; font-weight: bold;">否</span>
            </#if>
            <#else>
            --
        </#if>
        </td>
        <td>
            <#if essReport.isHighVoltageSideMeterNormal??>
            ${essReport.isHighVoltageSideMeterNormal.remark?default('')}
        </#if>
        </td>
        </tr>
        <tr>
            <td colspan="3">
                内部用电电表正常
            </td>
            <td align="center">
                <#if essReport.isEssInsideMeterNormal?? && essReport.isEssInsideMeterNormal.normal??>
                <#if essReport.isEssInsideMeterNormal.normal>
                是
                <#else>
                <span style="color: red; font-weight: bold;">否</span>
            </#if>
            <#else>
            --
        </#if>
        </td>
        <td>
            <#if essReport.isEssInsideMeterNormal??>
            ${essReport.isEssInsideMeterNormal.remark?default('')}
        </#if>
        </td>
        </tr>
        <tr>
            <td colspan="3">
                空调正常
            </td>
            <td align="center">
                <#if essReport.isAirConditionNormal?? && essReport.isAirConditionNormal.normal??>
                <#if essReport.isAirConditionNormal.normal>
                是
                <#else>
                <span style="color: red; font-weight: bold;">否</span>
            </#if>
            <#else>
            --
        </#if>
        </td>
        <td>
            <#if essReport.isAirConditionNormal??>
            ${essReport.isAirConditionNormal.remark?default('')}
        </#if>
        </td>
        </tr>
        <tr>
            <td colspan="3">
                消防正常
            </td>
            <td align="center">
                <#if essReport.isFireFightingNormal?? && essReport.isFireFightingNormal.normal??>
                <#if essReport.isFireFightingNormal.normal>
                是
                <#else>
                <span style="color: red; font-weight: bold;">否</span>
            </#if>
            <#else>
            --
        </#if>
        </td>
        <td>
            <#if essReport.isFireFightingNormal??>
            ${essReport.isFireFightingNormal.remark?default('')}
        </#if>
        </td>
        </tr>
        <tr>
            <td align="center" colspan="2">
                <p>备注</p>
            </td>
            <td colspan="6">
                <#if remark??>${remark}
                <#else>--
                </#if>
            </td>
        </tr>
        <tr>
            <td align="center" class="vertical">
                <p>场站照片</p>
            </td>
            <td colspan="6">
                <#if photos??>
                    <#list photos as photo>
                        <img src="${photo.url}" width="100px" height="100px" />
                    </#list>
                </#if>
            </td>
        </tr>
    </table>

    <table border="1" style="width: 100%;margin-top: 30px;">
        <tr class="smallHeader">
            <td align="center" colspan="6">
                ${siteName}设备信息
            </td>
        </tr>
        <tr>
            <td colspan="5">
                设备数量: ${essNum}(台)
            </td>
        </tr>
        <tr>
            <td align="center">
                设备类型
            </td>
            <td align="center">
                设备名称
            </td>
            <td align="center">
                编号/地址
            </td>
            <td align="center">
                设备状态
            </td>
            <td align="center">
                品牌
            </td>
        </tr>
        <#if essVoList??>
            <#list essVoList as essVo>
                <tr>
                    <td>储能ESS</td>
                    <td>${essVo.name!}</td>
                    <td>${essVo.gwName!}</td>
                    <#if essVo.status??>
                        <td>
                            <#if essVo.status == 0>未知
                            <#elseif essVo.status == 1>在线
                            <#elseif essVo.status == 2>离线
                            <#elseif essVo.status == 99>下线
                            </#if>
                        </td>
                        <#else>
                        <td>未知</td>
                    </#if>
                    <#if essVo.vendor??>
                        <td>
                            <#if essVo.vendor == 'HT_ESS'>亨通ESS
                            <#else>未知
                        </#if>
                        </td>
                    <#else>
                        <td>未知</td>
                    </#if>
                </tr>
            </#list>
        <#else>
            <tr>
                <td align="center">无</td>
                <td align="center">无</td>
                <td align="center">无</td>
                <td align="center">无</td>
                <td align="center">无</td>
            </tr>
        </#if>
        <tr>
            <td class="comment" colspan="6">
                <div style="width: 100%;height: 100%;">
                    <div class="position1">
                        客户评价及建议：
                        <div>
                            <#if isPerfect == 1>满意;
                                <#elseif isPerfect == 2>不满意;
                            </#if>
                            <#if advice??>
                                ${advice}
                            </#if>
                        </div>
                    </div>
                    <div class="position2">
                        客户签字：<img src="${signImage}" class="signImage" />
                    </div>
                </div>
            </td>
        </tr>
        <tr>
            <td class="conclusion" colspan="6">
                <div style="width: 100%;height: 100%;">
                    <div class="position1">
                        结论：
                    </div>
                    <div class="position2">
                        巡检人：
                    </div>
                </div>
            </td>
        </tr>
    </table>
</body>

</html>