<table cellspacing="0">
  <tr>
    <th class="t-title" colspan="6">申请内容</th>
  </tr>
  <tr>
    <td class="th_title item_label">站点名称</td>
    <td colspan="5" class="font-size-12" style="width: 80px;">${formData.siteName!'--'}</td>
  </tr>
  <tr>
    <td class="th_title item_label">场站概述</td>
    <td colspan="5" class="font-size-12" style="width: 80px;">${formData.siteRemark!'--'}</td>
  </tr>
  <tr>
    <td class="th_title item_label">账期日期</td>
    <td colspan="2" class="font-size-12" style="width: 80px;">${formData.billDate?join(" ~ ")}</td>
    <td class="th_title item_label">自动扣款</td>
    <td colspan="2" class="font-size-12" style="width: 80px;">
      <#if ['是', '否']?seq_contains(formData.autoDebit)>
      ${formData.autoDebit!'--'}
      <#else>
      ${(formData.autoDebit!false)?string('是', '否')}
      </#if>
    </td>
  </tr>
  <tr>
    <td class="th_title item_label">付款公司</td>
    <td colspan="2" class="font-size-12" style="width: 80px;">${formData.payCompanyName!'--'}</td>
    <td class="th_title item_label">电表户号</td>
    <td colspan="2" class="font-size-12" style="width: 80px;">${formData.meterAccount!'--'}</td>
  </tr>
  <tr>
    <td class="th_title item_label">供应商</td>
    <td colspan="5" class="font-size-12" style="width: 80px;">${formData.supplierName!'--'}</td>
  </tr>
  <tr>
    <td class="th_title item_label">开户银行</td>
    <td colspan="2" class="font-size-12" style="width: 80px;">${formData.bankName!'--'}</td>
    <td class="th_title item_label">银行账号</td>
    <td colspan="2" class="font-size-12" style="width: 80px;">${formData.bankAccount!'--'}</td>
  </tr>
  <tr>
    <td class="th_title item_label">备注说明</td>
    <td colspan="5" class="font-size-12" style="width: 80px;">
      <#attempt><#outputformat 'XML'>${formData.note!'--'}</#outputformat><#recover>Failed</#attempt>
    </td>
  </tr>
</table>

<!-- 平台统计信息 -->
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title">平台统计</td>
    <td colspan="5" class="font-size-12" style="width: 80px;">
      本数据以账期日期（开始日期0点至结束日期24点）、订单上传时间为基准；部分站点电费按实际核查联/电费单金额结算，单位：度、元、元/度，%
    </td>
  </tr>
  <tr>
    <td class="th_title_bg">总电量</td>
    <td class="th_title_bg">总电费</td>
    <td class="th_title_bg">总服务费</td>
    <td class="th_title_bg">总金额</td>
    <td class="th_title_bg">实际电费单价</td>
    <td class="th_title_bg">实际服务费单价</td>
  </tr>
  <tr>
    <#assign tmpPlatform=formData.statistics.platform >
    <td class="font-size-12" style="width: 80px;">
      ${(tmpPlatform.totalElec!0)?string(',##0.0000')}
    </td>
    <td class="font-size-12" style="width: 80px;">
      ${(tmpPlatform.totalElecFee!0)?string(',##0.00')}
    </td>
    <td class="font-size-12" style="width: 80px;">
      ${(tmpPlatform.totalServFee!0)?string(',##0.00')}
    </td>
    <td class="font-size-12" style="width: 80px;">
      ${(tmpPlatform.totalFee!0)?string(',##0.00')}
    </td>
    <td class="font-size-12" style="width: 80px;">
      <#if tmpPlatform.totalElec?? && (tmpPlatform.totalElec gt 0)>
      ${((formData.statistics.discount.billFee!0) / tmpPlatform.totalElec)?string(',##0.0000')}
      <#elseif tmpPlatform.totalElec??>0
      <#else>--</#if>
    </td>
    <td class="font-size-12" style="width: 80px;">
      <#if tmpPlatform.totalElec?? && (tmpPlatform.totalElec gt 0)>
      ${((formData.statistics.discount.actualServFee!0) / tmpPlatform.totalElec)?string(',##0.0000')}
      <#elseif tmpPlatform.totalElec??>0
      <#else>--</#if>
    </td>
  </tr>
  <tr>
    <td class="th_title_bg">电费单电量</td>
    <td class="th_title_bg">电费单金额</td>
    <td class="th_title_bg">实际服务费</td>
    <td class="th_title_bg">电损</td>
    <td class="th_title_bg">核查联电费均价</td>
    <td class="th_title_bg">完成订单数</td>
  </tr>
  <tr>
    <#assign tmpDiscount=formData.statistics.discount >
    <td class="font-size-12" style="width: 80px;">
      ${(tmpDiscount.billElec!0)?string(',##0.0000')}
    </td>
    <td class="font-size-12" style="width: 80px;">
      ${(tmpDiscount.billFee!0)?string(',##0.00')}
    </td>
    <td class="font-size-12" style="width: 80px;">
      ${(tmpDiscount.actualServFee!0)?string(',##0.00')}
    </td>
    <td class="font-size-12" style="width: 80px;">
      <#if showLossRate?? && showLossRate>
      ${(tmpDiscount.discount!0)?string(',##0.00')}
      <#else><span>--</span></#if>
    </td>
    <td class="font-size-12" style="width: 80px;">
      <#if tmpDiscount.billElec?? && (tmpDiscount.billElec gt 0)>
      ${((tmpDiscount.billFee!0) / tmpDiscount.billElec)?string(',##0.0000')}
      <#elseif tmpDiscount.billElec??>0
      <#else>--</#if>
    </td>
    <td class="font-size-12" style="width: 80px;">
      ${(formData.statistics.platform.orderNum!0)?string(',##0')}
    </td>
  </tr>
</table>

<!-- 抄表统计信息 -->
<#if formData.meterData?? && formData.meterData?has_content>
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title" colspan="12">抄表统计</td>
  </tr>
  <tr>
    <td class="th_title_bg">月份</td>
    <td class="th_title_bg">表号</td>
    <td class="th_title_bg">抄表电量(kWh)</td>
    <td class="th_title_bg">尖电量(kWh)</td>
    <td class="th_title_bg">峰电量(kWh)</td>
    <td class="th_title_bg">平电量(kWh)</td>
    <td class="th_title_bg">谷电量(kWh)</td>
    <td class="th_title_bg">开始总读数(kWh)</td>
    <td class="th_title_bg">结束总读数(kWh)</td>
<!--    <td class="th_title_bg">订单电量(kWh)</td>-->
<!--    <td class="th_title_bg">电损(%)</td>-->
  </tr>
  <#list formData.meterData as meterData>
  <tr>
    <td class="font-size-12" style="width: 80px;">
      ${meterData.monthDate}
    </td>
    <td class="font-size-12" style="width: 80px;">
      ${meterData.dno}
    </td>
    <td class="font-size-12" style="width: 80px;">
      ${(meterData.meterElec!0)?string(',##0.00')}
    </td>
    <td class="font-size-12" style="width: 80px;">
      ${(meterData.sharpPeakElec!0)?string(',##0.00')}
    </td>
    <td class="font-size-12" style="width: 80px;">
      ${(meterData.peakElec!0)?string(',##0.00')}
    </td>
    <td class="font-size-12" style="width: 80px;">
      ${(meterData.offPeakElec!0)?string(',##0.00')}
    </td>
    <td class="font-size-12" style="width: 80px;">
      ${(meterData.valleyElec!0)?string(',##0.00')}
    </td>
    <td class="font-size-12" style="width: 80px;">
      <#if meterData.startTotalReadingElec?? && (meterData.startTotalReadingElec gt 0)>
      ${((meterData.startTotalReadingElec!0)?string(',##0.00'))}
    </#if>
    </td>
    <td class="font-size-12" style="width: 80px;">
      <#if meterData.endTotalReadingElec?? && (meterData.endTotalReadingElec gt 0)>
      ${((meterData.endTotalReadingElec!0)?string(',##0.00'))}
    </#if>
    </td>
<!--    <td class="font-size-12" style="width: 80px;">-->
<!--      ${(meterData.orderElec!0)?string(',##0.0000')}-->
<!--    </td>-->
<!--    <td class="font-size-12" style="width: 80px;">-->
<!--      ${((meterData.discount?number!0) * 100)?string(',##0.0')}-->
<!--    </td>-->
  </tr>
</#list>
</table>
</#if>

<!-- 电费单信息 -->
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title">电费单电量</td>
    <td class="font-size-12" style="width: 80px;">${(formData.billElec!0)?string(',##0.0000')}</td>
    <td class="th_title">电费单金额</td>
    <td class="font-size-12" style="width: 80px;">${(formData.billAmount!0)?string(',##0.00')}</td>
    <td class="th_title">最后付款日期</td>
    <td class="font-size-12" style="width: 80px;">${formData.payTimeFinal!'--'}</td>
  </tr>
</table>