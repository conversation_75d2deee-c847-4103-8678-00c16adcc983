<table cellspacing="0">
  <tr>
    <th class="t-title" colspan="6">申请内容</th>
  </tr>
  <tr>
    <td class="th_title item_label">账户类型</td>
    <td colspan="2" class="font-size-12">
      <#assign accountTypeMap={
      "CORP": "企业账户",
      "PERSONAL": "个人账户",
      "COMMERCIAL": "商户会员"
      }>
      ${accountTypeMap[formData.accountType]!'--'}
    </td>
    <td class="th_title item_label">
      <#if formData.accountType=='CORP'>企业名称
      <#else>手机号</#if>
    </td>
    <td colspan="2" class="font-size-12">
      <#if formData.accountType=='CORP'>${formData.corpName!'--'}
      <#else>${formData.phone!'--'}</#if>
    </td>
  </tr>
  <#if formData.accountType=='CORP'>
  <tr>
    <td class="th_title item_label">企客摘要</td>
    <td colspan="5" class="font-size-12">
      ${formData.corpDigest!'--'}
    </td>
  </tr>
  </#if>
  <#if ['CORP', 'COMMERCIAL']?seq_contains(formData.accountType)>
  <tr>
    <td class="th_title item_label">所属商户</td>
    <td colspan="5" class="font-size-12">
      ${formData.commName!'--'}
    </td>
  </tr>
  </#if>
  <tr>
    <td class="th_title" style="width: 80px">备注说明</td>
    <td colspan="5" class="font-size-12">
      <#attempt><#outputformat 'XML'>${formData.note!'--'}</#outputformat><#recover>Failed</#attempt>
    </td>
  </tr>
</table>

<!-- 生效合约 -->
<#assign loc_contract_title="生效合约：">
<#include "./oa_common_contracts.html">

<!-- 是否开票 -->
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title item_label">开票种类</td>
    <td class="font-size-12" colspan="2">
      <#if formData.invoiceBuyerInfo.type??>
      <#switch formData.invoiceBuyerInfo.type>
      <#case "PER_COMMON">个人普票<#break>
      <#case "ENTER_COMMON">企业普票<#break>
      <#case "ENTER_PROFESSION">企业专票<#break>
      <#default>未知
      </#switch>
      <#else>--</#if>
    </td>
  </tr>
  <tr>
    <td class="th_title item_label" colspan="3">抬头信息:</td>
  </tr>
</table>
<!-- 发票模板 -->
<table cellspacing="0" style="margin: -1px 0;">
  <#if formData.invoiceBuyerInfo.type == 'ENTER_COMMON'>
    <#include "./invoice_buyer_info_corp_common.html">
  <#elseif formData.invoiceBuyerInfo.type == 'PER_COMMON'>
    <#include "./invoice_buyer_info_per_common.html">
  <#elseif formData.invoiceBuyerInfo.type == 'ENTER_PROFESSION'>
    <#include "./invoice_buyer_info_corp_profession.html">
  </#if>
</table>

<!-- 平台账单信息/结算信息信息 -->
<#assign amountSum=0 freeAmountSum=0 invoiceAmountSum=0>
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title" colspan="6">充值单：</td>
  </tr>
  <tr>
    <td class="th_title_bg">订单号</td>
    <td class="th_title_bg">支付账户</td>
    <td class="th_title_bg">实际金额</td>
    <td class="th_title_bg">赠送金额</td>
    <td class="th_title_bg">可开票金额</td>
    <td class="th_title_bg">充值时间</td>
    <td class="th_title_bg">申请单号</td>
  </tr>
  <#if formData.payBillList?? && (formData.payBillList?size>0)>
    <#list formData.payBillList as bill>
      <#assign amountSum=amountSum+(bill.amount!0)
            freeAmountSum=freeAmountSum+(bill.freeAmount!0)
            invoiceAmountSum=invoiceAmountSum+(bill.invoiceAmount!0)>
      <tr>
        <td class="font-size-12">${bill.orderId!'--'}</td>
        <td class="font-size-12">${bill.outAccountName!'--'}</td>
        <td class="font-size-12">${(bill.amount!0)?string(',##0.00')}</td>
        <td class="font-size-12">${(bill.freeAmount!0)?string(',##0.00')}</td>
        <td class="font-size-12">${(bill.invoiceAmount!0)?string(',##0.00')}</td>
        <td class="font-size-12">
          ${bill.payTime?datetime?string('yyyy-MM-dd HH:mm')}
        </td>
        <td class="font-size-12">
          <#if bill.procInstId??>
          ${(bill.procInstId!'')[0..7]?upper_case}
          <#else>--</#if>
        </td>
      </tr>
    </#list>
    <tr>
      <td class="font-size-12">合计</td>
      <td class="font-size-12">--</td>
      <td class="font-size-12">${amountSum?string(',##0.00')}</td>
      <td class="font-size-12">${freeAmountSum?string(',##0.00')}</td>
      <td class="font-size-12">${invoiceAmountSum?string(',##0.00')}</td>
      <td class="font-size-12">--</td>
      <td class="font-size-12">--</td>
    </tr>
  <#else>
  <tr>
    <td class="list_non_data" colspan="6">暂无数据</td>
  </tr>
  </#if>
</table>

<!-- 发票信息 -->
<#assign actualServFee=0 actualElecFee=0>
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title item_label">开票主体</td>
    <td class="font-size-12" colspan="4">
      ${formData.invoicedTempSalName!'--'}
    </td>
  </tr>
  <tr>
    <td class="th_title item_label">商品模板</td>
    <td class="font-size-12" colspan="4">
      ${formData.invoicedTempSalRefName!'--'}
    </td>
  </tr>
  <tr>
    <td class="th_title" colspan="5">开票内容：</td>
  </tr>
  <#list formData.invoiceRecords as invRec>
    <#if invRec.contents?? && (invRec.contents?size > 0)>
    <tr>
      <td class="th_title_bg">名称</td>
      <td class="th_title_bg">规格</td>
      <td class="th_title_bg">单位</td>
      <td class="th_title_bg">税率（%）</td>
      <td class="th_title_bg">数量</td>
      <td class="th_title_bg">单价</td>
      <td class="th_title_bg">开票金额（元）</td>
    </tr>
    <#list invRec.contents as sph>
    <tr>
      <td class="font-size-12">${sph.productName!'--'}</td>
      <td class="font-size-12">${sph.spec!'--'}</td>
      <td class="font-size-12">${sph.unit!'--'}</td>
      <td class="font-size-12">${sph.taxRate!0}</td>
      <td class="font-size-12">${sph.num!0}</td>
      <td class="font-size-12">
        <#if sph.num?? && (sph.num > 0)>
          ${((sph.fixAmount!0) / (sph.num!0))?string(',##0.00')}
          <#else>--
        </#if>
      </td>
      <td class="font-size-12">
        <#if sph.productType?? && (sph.productType=='SERV_ACTUAL_FEE')>
        <#assign actualServFee=actualServFee+(sph.fixAmount!0)>
        <#elseif sph.productType?? && (sph.productType=='ELEC_ACTUAL_FEE')>
        <#assign actualElecFee=actualElecFee+(sph.fixAmount!0)>
        </#if>
        ${(sph.fixAmount!0)?string(',##0.00')}
      </td>
    </tr>
    </#list>
    </#if>
    <tr>
      <td class="th_title item_label">票面备注</td>
      <td class="font-size-12" colspan="4">
        ${invRec.remark!'--'}
      </td>
    </tr>
  <tr><td></td></tr>
  </#list>  <!-- formData.invoiceRecords -->
</table>
<table cellspacing="0">
</table>

<!-- 数据对比 -->
<#assign firstSum=0 secondSum=0>
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title" colspan="${formData.actualData?size + 2}">数据对比：</td>
  </tr>
  <tr>
    <td class="th_title_bg">单位：元</td>
    <#list formData.actualData as sph>
    <td class="th_title_bg">${sph.productName!'-/-'}</td>
    </#list>
  <td class="th_title_bg">总额（元）</td>
  </tr>
  <tr>
    <td class="font-size-12">应开</td>
    <#list formData.actualData as sph>
    <#assign firstSum=firstSum + (sph.amount!0)>
    <td class="font-size-12">${(sph.amount!0)?string(',##0.00')}</td>
    </#list>
    <td class="font-size-12">${firstSum?string(',##0.00')}</td>
  </tr>
  <tr>
    <td class="font-size-12">实开</td>
    <#list formData.actualData as sph>
    <#assign secondSum=secondSum + (sph.fixAmount!0)>
    <td class="font-size-12">${(sph.fixAmount!0)?string(',##0.00')}</td>
    </#list>
    <td class="font-size-12">${secondSum?string(',##0.00')}</td>
  </tr>
  <tr>
    <td class="font-size-12">差异</td>
    <#list formData.actualData as sph>
    <td class="font-size-12">
      ${((sph.amount!0) - (sph.fixAmount!0))?string(',##0.00')}
    </td>
    </#list>
    <td class="font-size-12">
      ${(firstSum - secondSum)?string(',##0.00')}
    </td>
  </tr>
</table>

<!-- 是否用印 -->
<#if formData.handleNodes?? && formData.handleNodes?seq_contains('SIGNET')>
<#include "./oa_common_signet.html">
</#if>
