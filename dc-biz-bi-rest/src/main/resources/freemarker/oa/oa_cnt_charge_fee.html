<table cellspacing="0">
  <tr>
    <th class="t-title" colspan="8">申请内容</th>
  </tr>
  <tr>
    <td class="th_title item_label">下发站点</td>
    <td colspan="6" class="font-size-12">${formData.siteNameList?join('/')}</td>
  </tr>
  <tr>
    <td class="th_title item_label">操作对象</td>
    <td colspan="6" class="font-size-12">
      <#assign opTargetMap={
      "SITE": "整个站点(统一计费)", "EVSE": "个别充电桩", "SITE_PART": "整站(场站交直流计费分离)"
      }>
      ${opTargetMap[formData.target]}
    </td>
  </tr>
  <tr>
    <td class="th_title item_label">价格策略</td>
    <td colspan="6" class="font-size-12">
      <#if formData.smartPrice?has_content>
        <#if formData.smartPrice>
            智能策略
          <#else>
          计费模版
        </#if>
      </#if>
    </td>
  </tr>
  <#if formData.smartStrategy??>
    <tr>
      <td class="th_title item_label">智能策略</td>
      <td colspan="6" class="font-size-12">
        <#assign smartStrategyeMap={
        "singlePrice": "电价调整 - 总价不变",
        "fixTotalFeeTimeBasedPrice": "电价调整 - 总价不变 - 峰平谷",
        "fixServFee": "电价调整 - 服务费不变",
        "fixServFeeTimeBasedPrice": "电价调整 - 服务费不变 - 峰平谷",
        "fixElecFee": "电价调整 - 电费不变",
        "fixElecFeeTimeBasedPrice": "电价调整 - 电费不变 - 峰平谷"
        }>
        ${smartStrategyeMap[formData.smartStrategy]}
      </td>
    </tr>
  </#if>
  <#if (formData.smartStrategy??) && (formData.elecPrice??)>
    <tr>
      <td class="th_title item_label">新电价</td>
      <td colspan="6" class="font-size-12">
        ${formData.elecPrice}
      </td>
    </tr>
  </#if>
</table>

<#if (formData.smartStrategy??) && (formData.timeBasedElecPrice??)>
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td colspan="4" class="th_title item_label">新电价</td>
  </tr>
  <tr>
    <td class="th_title">尖</td>
    <td class="th_title">峰</td>
    <td class="th_title">平</td>
    <td class="th_title">谷</td>
  </tr>
  <tr>
    <td class="font-size-12">${(formData.timeBasedElecPrice.sharpPeakElecPrice!0)?string(',##0.0000')}</td>
    <td class="font-size-12">${(formData.timeBasedElecPrice.peakElecPrice!0)?string(',##0.0000')}</td>
    <td class="font-size-12">${(formData.timeBasedElecPrice.offPeakElecPrice!0)?string(',##0.0000')}</td>
    <td class="font-size-12">${(formData.timeBasedElecPrice.valleyElecPrice!0)?string(',##0.0000')}</td>
  </tr>
</table>
</#if>

<table style="margin-top: 15px" cellspacing="0">
  <#if formData.target?? && formData.target=='EVSE'>
  <tr>
    <td class="th_title item_label">选择桩</td>
    <td colspan="4" class="font-size-12">
      <#assign conEvseName=''>
      <#list formData.targetInfoList as target>
      <#assign targetName = target.name!'--'>
      <#assign conEvseName=conEvseName+'/'+targetName>
      </#list>
      ${conEvseName?remove_beginning('/')}
    </td>
  </tr>
  </#if>
  <tr>
    <td class="th_title item_label">下发时间</td>
    <td colspan="4" class="font-size-12">
      <#assign exeTimeMap={
      "IN_TIME": "立即", "SPECIAL_DATE": "指定日期", "SPECIAL_TIME": "指定时刻"
      }>
      ${exeTimeMap[formData.exeTime]}
      <#if formData.exeDate??>
      （${formData.exeDate!'--'}）
      </#if>
    </td>
  </tr>
</table>

<#if formData.round?? && formData.round==2>
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title item_label">次轮价格策略</td>
    <td colspan="6" class="font-size-12">
      <#if formData.smartPrice2?has_content>
        <#if formData.smartPrice2>
          智能策略
        <#else>
          计费模版
        </#if>
    </#if>
    </td>
  </tr>
  <#if formData.smartStrategy2??>
  <tr>
    <td class="th_title item_label">次轮智能策略</td>
    <td colspan="6" class="font-size-12">
      <#assign smartStrategyeMap={
      "singlePrice": "电价调整 - 总价不变",
      "fixTotalFeeTimeBasedPrice": "电价调整 - 总价不变 - 峰平谷",
      "fixServFee": "电价调整 - 服务费不变",
      "fixServFeeTimeBasedPrice": "电价调整 - 服务费不变 - 峰平谷",
      "fixElecFee": "电价调整 - 电费不变",
      "fixElecFeeTimeBasedPrice": "电价调整 - 电费不变 - 峰平谷"
      }>
      ${smartStrategyeMap[formData.smartStrategy2]}
    </td>
  </tr>
  </#if>
  <#if (formData.smartStrategy2??) && (formData.elecPrice2??)>
    <tr>
      <td class="th_title item_label">次轮新电价</td>
      <td colspan="6" class="font-size-12">
        ${formData.elecPrice2}
      </td>
    </tr>
  </#if>
</table>

<#if (formData.smartStrategy2??) && (formData.timeBasedElecPrice2??)>
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td colspan="4" class="th_title item_label">次轮新电价</td>
  </tr>
  <tr>
    <td class="th_title">尖</td>
    <td class="th_title">峰</td>
    <td class="th_title">平</td>
    <td class="th_title">谷</td>
  </tr>
  <tr>
    <td class="font-size-12">${(formData.timeBasedElecPrice2.sharpPeakElecPrice!0)?string(',##0.0000')}</td>
    <td class="font-size-12">${(formData.timeBasedElecPrice2.peakElecPrice!0)?string(',##0.0000')}</td>
    <td class="font-size-12">${(formData.timeBasedElecPrice2.offPeakElecPrice!0)?string(',##0.0000')}</td>
    <td class="font-size-12">${(formData.timeBasedElecPrice2.valleyElecPrice!0)?string(',##0.0000')}</td>
  </tr>
</table>
</#if>

<#if (formData.acPriceSchemeInfo2??) && (formData.acPriceSchemeInfo2.priceItemList??) && (formData.acPriceSchemeInfo2.priceItemList?size > 0)>
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title item_label">次轮交流模板</td>
    <td colspan="4" class="font-size-12">${formData.acPriceSchemeInfo2.name!'--'}</td>
  </tr>
  <tr>
    <td class="th_title_bg"></td>
    <td class="th_title_bg">时段</td>
    <td class="th_title_bg">电费（元）</td>
    <td class="th_title_bg">服务费（元）</td>
    <td class="th_title_bg">总价（元）</td>
  </tr>
  <#assign categoryMap={
  "PRICE_TAG_JIAN": "尖时", "PRICE_TAG_FENG": "峰时", "PRICE_TAG_PING": "平时", "PRICE_TAG_GU": "谷时"
  }>
  <#list formData.acPriceSchemeInfo2.priceItemList as item>
  <tr>
    <td class="font-size-12">
      ${categoryMap[item.category]}
    </td>
    <td class="font-size-12">
      ${item.startTime + ' ~ ' + item.stopTime}
    </td>
    <td class="font-size-12">
      ${(item.elecPrice!0)?string(',##0.0000')}
    </td>
    <td class="font-size-12">
      ${(item.servPrice!0)?string(',##0.0000')}
    </td>
    <td class="font-size-12">
      ${((item.elecPrice!0) + (item.servPrice!0))?string(',##0.0000')}
    </td>
  </tr>
  </#list>
</table>
</#if>

<#if (formData.priceSchemeInfo2??) && (formData.priceSchemeInfo2.priceItemList??) && (formData.priceSchemeInfo2.priceItemList?size > 0)>
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title item_label">次轮计费模板</td>
    <td colspan="4" class="font-size-12">${formData.priceSchemeInfo2.name!'--'}</td>
  </tr>
  <tr>
    <td class="th_title_bg"></td>
    <td class="th_title_bg">时段</td>
    <td class="th_title_bg">电费（元）</td>
    <td class="th_title_bg">服务费（元）</td>
    <td class="th_title_bg">总价（元）</td>
  </tr>
  <#assign categoryMap={
  "PRICE_TAG_JIAN": "尖时", "PRICE_TAG_FENG": "峰时", "PRICE_TAG_PING": "平时", "PRICE_TAG_GU": "谷时"
  }>
  <#list formData.priceSchemeInfo2.priceItemList as item>
  <tr>
    <td class="font-size-12">
      ${categoryMap[item.category]}
    </td>
    <td class="font-size-12">
      ${item.startTime + ' ~ ' + item.stopTime}
    </td>
    <td class="font-size-12">
      ${(item.elecPrice!0)?string(',##0.0000')}
    </td>
    <td class="font-size-12">
      ${(item.servPrice!0)?string(',##0.0000')}
    </td>
    <td class="font-size-12">
      ${((item.elecPrice!0) + (item.servPrice!0))?string(',##0.0000')}
    </td>
  </tr>
  </#list>
</table>
</#if>


<#if formData.target?? && formData.target=='EVSE'>
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title item_label">次轮选择桩</td>
    <td colspan="4" class="font-size-12">
      <#assign conEvseName=''>
      <#if formData.targetIngoList2??>
        <#list formData.targetInfoList2 as target>
          <#assign targetName = target.name!'--'>
          <#assign conEvseName=conEvseName+'/'+targetName>
        </#list>
      </#if>
    ${conEvseName?remove_beginning('/')}
    </td>
  </tr>
</table>
</#if>

<#if formData.round?? && formData.round==2>
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title item_label">次轮下发时间</td>
    <td colspan="4" class="font-size-12">
      <#assign exeTimeMap={
      "IN_TIME": "立即", "SPECIAL_DATE": "指定日期", "SPECIAL_TIME": "指定时刻"
      }>
      ${exeTimeMap[formData.exeTime2]}
      <#if formData.exeDate2??>
      （${formData.exeDate2!'--'}）
    </#if>
    </td>
  </tr>
</table>
</#if>

</#if>

<#if formData.priceTemplateInfo?? && formData.priceTemplateInfo?has_content>
<#list formData.priceTemplateInfo?keys as stationName>
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <#if formData.evseNoList?? && formData.evseNoList?has_content>
      <td class="th_title item_label">桩名称</td>
    <#else>
      <td class="th_title item_label">站点名称</td>
    </#if>
    <td colspan="6" class="font-size-12">${stationName}</td>
  </tr>
</table>


<#-- 遍历中间层 -->
<#list formData.priceTemplateInfo[stationName]?keys as typeKey>
<table style="margin-top: 15px" cellspacing="0">
  <#if typeKey == "targetPriceScheme">
    <tr>
      <td colspan="5"  class="th_title item_label">调整前</td>
    </tr>
  </#if>
  <#if typeKey == "smartPriceScheme" || typeKey == "priceTemplate">
    <tr>
      <td colspan="5" class="th_title item_label">调整后</td>
    </tr>
  </#if>
  <#if typeKey == "smartPriceScheme2" || typeKey == "priceTemplate2">
    <tr>
      <td colspan="5" class="th_title item_label">次轮调整后</td>
    </tr>
  </#if>
  <#-- 遍历内层 -->
  <#list formData.priceTemplateInfo[stationName][typeKey]?keys as subTypeKey>
    <tr>
      <#if subTypeKey == "AC">
        <td colspan="5" class="font-size-12">交流计费模版</td>
      </#if>
    </tr>
    <tr>
      <#if subTypeKey == "DC">
        <td colspan="5" class="font-size-12">直流计费模版</td>
      </#if>
    </tr>
    <tr>
      <#if subTypeKey == "ALL">
        <td colspan="5" class="font-size-12">整站</td>
      </#if>
    </tr>
    <tr>
      <td class="th_title_bg"></td>
      <td class="th_title_bg">时段</td>
      <td class="th_title_bg">电费（元）</td>
      <td class="th_title_bg">服务费（元）</td>
      <td class="th_title_bg">总价（元）</td>
    </tr>
    <#assign categoryMap={
    "PRICE_TAG_JIAN": "尖时", "PRICE_TAG_FENG": "峰时", "PRICE_TAG_PING": "平时", "PRICE_TAG_GU": "谷时"
    }>
    <#list formData.priceTemplateInfo[stationName][typeKey][subTypeKey].priceItemList as item>
      <tr>
        <td class="font-size-12">
          ${categoryMap[item.category]}
        </td>
        <td class="font-size-12">
          ${item.startTime + ' ~ ' + item.stopTime}
        </td>
        <td class="font-size-12">
          ${(item.elecPrice!0)?string(',##0.0000')}
        </td>
        <td class="font-size-12">
          ${(item.servPrice!0)?string(',##0.0000')}
        </td>
        <td class="font-size-12">
          ${((item.elecPrice!0) + (item.servPrice!0))?string(',##0.0000')}
        </td>
      </tr>
    </#list>
  </#list>

</table>
</#list>
</#list>
</#if>

<table style="margin-top: 15px" cellspacing="0">
<tr>
  <td class="th_title item_label">备注说明</td>
  <td colspan="5" class="font-size-12">
    <#attempt><#outputformat 'XML'>${formData.note!'--'}</#outputformat><#recover>Failed</#attempt>
</td>
</tr>
</table>