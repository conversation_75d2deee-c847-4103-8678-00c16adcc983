<table style="margin-top: 30px" cellspacing="0">
  <tr>
    <th class="t-title" colspan="4">处理人意见/附件</th>
  </tr>
  <tr>
    <td class="th_title">处理人</td>
    <td class="th_title">时间</td>
    <td class="th_title">操作/备注</td>
    <td class="th_title">附件</td>
  </tr>
  <#list commentList as item>
    <tr>
      <td class="font-size-12" style="width: 80px;">
        ${item.OName!'--'}
      </td>
      <td class="font-size-12" style="width: 110px;">
        ${item.time?string('yyyy-MM-dd HH:mm')}
      </td>
      <td class="font-size-12" style="word-break: break-all;word-wrap: break-word;">
        <#if item.op??>
          <#switch item.op>
            <#case "submit">提交<#break>
            <#case "comment">留言<#break>
            <#case "reject">驳回<#break>
            <#case "pass">通过<#break>
            <#case "sign">加签<#break>
            <#case "end">结束<#break>
            <#default>--<#break>
          </#switch>
          <#if item.note??>
            <#attempt><#outputformat 'XML'>,${item.note}</#outputformat><#recover>Failed</#attempt>
          </#if>
        </#if>
      </td>
      <td class="font-size-12" style="width: 200px;word-break: break-all;word-wrap: break-word;">
        <#if (item.attachmentList)?? && (item.attachmentList?size>0)>
          <#list item.attachmentList as attach >
            <#if ((attach.flag!0)>0)>
              <div class="delete-line">
            <#else>
              <div>
            </#if>
            <#if attach?? && attach.originName??>
              <#attempt><#outputformat 'XML'>${attach.originName}</#outputformat><#recover>Failed</#attempt>
            <#elseif attach?? && attach.refName??>
              <#attempt><#outputformat 'XML'>${attach.refName}</#outputformat><#recover>Failed</#attempt>
            <#else>
              附件${attach_index+1}
            </#if>
            </div>
          </#list>
        </#if>
      </td>
    </tr>
  </#list>
</table>