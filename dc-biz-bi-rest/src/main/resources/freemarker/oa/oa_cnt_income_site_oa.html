<table cellspacing="0">
  <tr>
    <th class="t-title" colspan="6">申请内容</th>
  </tr>
  <tr>
    <td class="th_title item_label">站点名称</td>
    <td colspan="5" class="font-size-12">
      <#if formData.siteNameList??>
        ${formData.siteNameList?join(',')}
      <#else>
        ${formData.siteName!'--'}
      </#if>
    </td>
  </tr>
  <tr>
    <td class="th_title item_label">场站概述</td>
    <td colspan="5" class="font-size-12">${formData.siteRemark!'--'}</td>
  </tr>
  <tr>
    <td class="th_title item_label">结算账期</td>
    <td colspan="5" class="font-size-12">
      <#if formData.billDateRange??>
      ${formData.billDateRange.startTime!'--'}
      ~
      ${formData.billDateRange.endTime!'--'}
      <#else>--</#if>
    </td>
  </tr>
  <tr>
    <td class="th_title item_label">备注说明</td>
    <td colspan="5" class="font-size-12">
      <#attempt><#outputformat 'XML'>${formData.note!'--'}</#outputformat><#recover>Failed</#attempt>
    </td>
  </tr>
</table>

<!-- 运营合约 -->
<#assign loc_contract_title="运营合约：">
<#include "./oa_common_contracts.html">

<!-- 平台统计信息 -->
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title" colspan="5">结算信息：</td>
  </tr>
  <tr>
    <td class="th_title_bg"></td>
    <td class="th_title_bg">电费</td>
    <td class="th_title_bg">服务费</td>
    <td class="th_title_bg">停车超时费</td>
    <td class="th_title_bg">结算总金额</td>
  </tr>
  <#assign elecFeeSum=0 servFeeSum=0 parkFeeSum=0>
  <#if formData.settBillSummary?? && formData.settBillSummary.settJobBillList??>
  <#list formData.settBillSummary.settJobBillList as bill>
  <tr>
    <td class="font-size-12" style="width: 40px">
      ${bill.billNo!'--'}
    </td>
    <td class="font-size-12">
      <#assign elecFeeSum=elecFeeSum+(bill.elecFee!0)>
      ${(bill.elecFee!0)?string(',##0.00')}
    </td>
    <td class="font-size-12">
      <#assign servFeeSum=servFeeSum+(bill.servFee!0)>
      ${(bill.servFee!0)?string(',##0.00')}
    </td>
    <td class="font-size-12">
      <#assign parkFeeSum=parkFeeSum+(bill.parkFee!0)>
      ${(bill.parkFee!0)?string(',##0.00')}
    </td>
    <td class="font-size-12">
      ${((bill.elecFee!0) + (bill.servFee!0) + (bill.parkFee!0))?string(',##0.00')}
    </td>
  </tr>
  </#list>
  </#if>
  <tr>
    <td class="font-size-12">平台汇总</td>
    <td class="font-size-12">
      ${(elecFeeSum!0)?string(',##0.00')}
    </td>
    <td class="font-size-12">
      ${(servFeeSum!0)?string(',##0.00')}
    </td>
    <td class="font-size-12">
      ${(parkFeeSum!0)?string(',##0.00')}
    </td>
    <td class="font-size-12">
      ${((elecFeeSum!0) + (servFeeSum!0) + (parkFeeSum!0))?string(',##0.00')}
    </td>
  </tr>
  <#assign tmpActualFee=formData.settBillSummary.actualFee>
  <tr>
    <td class="font-size-12 font-bold">实际结算</td>
    <td class="font-size-12 font-bold">
      ${(tmpActualFee.elecFee!0)?string(',##0.00')}
    </td>
    <td class="font-size-12 font-bold">
      ${(tmpActualFee.servFee!0)?string(',##0.00')}
    </td>
    <td class="font-size-12 font-bold">
      ${(tmpActualFee.parkFee!0)?string(',##0.00')}
    </td>
    <td class="font-size-12 font-bold">
      ${((tmpActualFee.elecFee!0) + (tmpActualFee.servFee!0) + (tmpActualFee.parkFee!0))?string(',##0.00')}
    </td>
  </tr>
  <#assign elecFeeDiff=elecFeeSum - (tmpActualFee.elecFee!0)
  servFeeDiff=servFeeSum - (tmpActualFee.servFee!0)
  parkFeeDiff=parkFeeSum - (tmpActualFee.parkFee!0) >
  <tr>
    <td class="font-size-12">差异</td>
    <td class="font-size-12">
      ${elecFeeDiff?string(',##0.00')}
    </td>
    <td class="font-size-12">
      ${servFeeDiff?string(',##0.00')}
    </td>
    <td class="font-size-12">
      ${parkFeeDiff?string(',##0.00')}
    </td>
    <td class="font-size-12">
      ${(elecFeeDiff + servFeeDiff + parkFeeDiff)?string(',##0.00')}
    </td>
  </tr>
  <tr>
    <td class="th_title item_label" colspan="1" style="width: 80px">差异说明</td>
    <td class="th_title" colspan="4">
      ${formData.differenceRemark!'--'}
    </td>
  </tr>
</table>

<!-- 是否用印 -->
<#if formData.handleNodes?? && formData.handleNodes?seq_contains('SIGNET')>
<#include "./oa_common_signet.html">
</#if>

<!-- 是否开票 -->
<#if formData.handleNodes?? && formData.handleNodes?seq_contains('INVOICE')>
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title item_label">是否开票</td>
    <td class="font-size-12">
      ${formData.handleNodes?seq_contains('INVOICE')?string('是', '否')}
    </td>
    <td class="th_title item_label">开票种类</td>
    <td class="font-size-12" colspan="2">
      <#if formData.invoiceType??>
        <#switch formData.invoiceType>
        <#case "PER_COMMON">个人普票<#break>
        <#case "ENTER_COMMON">企业普票<#break>
        <#case "ENTER_PROFESSION">企业专票<#break>
        <#default>未知
      </#switch>
      <#else>--</#if>
    </td>
  </tr>
  <tr>
    <td class="th_title item_label">抬头信息</td>
    <td class="font-size-12" colspan="4">
      ${formData.invoiceBuyerInfo.name!'--'}
    </td>
  </tr>
</table>
<!-- 发票模板 -->
<table cellspacing="0" style="margin: -1px 0;">
  <#if formData.invoiceType == 'ENTER_COMMON'>
    <#include "./invoice_buyer_info_corp_common.html">
  <#elseif formData.invoiceType == 'PER_COMMON'>
    <#include "./invoice_buyer_info_per_common.html">
  <#elseif formData.invoiceType == 'ENTER_PROFESSION'>
    <#include "./invoice_buyer_info_corp_profession.html">
  </#if>
</table>
<!-- 发票信息 -->
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title item_label">开票主体</td>
    <td class="font-size-12" colspan="4">
      ${formData.invoicedTempSalName!'--'}
    </td>
  </tr>
  <tr>
    <td class="th_title item_label">商品模板</td>
    <td class="font-size-12" colspan="4">
      ${formData.invoicedTempSalRefName!'--'}
    </td>
  </tr>
  <tr>
    <td class="th_title" colspan="5">开票内容：</td>
  </tr>
  <#list formData.invoiceRecords as invRec>
    <#if invRec.contents?? && (invRec.contents?size > 0)>
    <tr>
      <td class="th_title_bg">名称</td>
      <td class="th_title_bg">规格</td>
      <td class="th_title_bg">单位</td>
      <td class="th_title_bg">税率（%）</td>
      <td class="th_title_bg">数量</td>
      <td class="th_title_bg">单价</td>
      <td class="th_title_bg">开票金额（元）</td>
    </tr>
  <#list invRec.contents as sph>
  <tr>
    <td class="font-size-12">${sph.productName!'--'}</td>
    <td class="font-size-12">${sph.spec!'--'}</td>
    <td class="font-size-12">${sph.unit!'--'}</td>
    <td class="font-size-12">${sph.taxRate!0}</td>
    <td class="font-size-12">${sph.num!0}</td>
    <td class="font-size-12">
      <#if sph.num?? && (sph.num > 0)>
        ${((sph.fixAmount!0) / (sph.num!0))?string(',##0.00')}
        <#else>--
      </#if>
    </td>
    <td class="font-size-12">${(sph.fixAmount!0)?string(',##0.00')}</td>
  </tr>
  </#list>
  </#if>
  <tr>
    <td class="th_title item_label">票面备注</td>
    <td class="font-size-12" colspan="4">
      ${invRec.remark!'--'}
    </td>
  </tr>
  <tr><td></td></tr>
  </#list>  <!-- formData.invoiceRecords -->
</table>
<!-- 数据对比 -->
<#assign firstSum=0 secondSum=0>
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title" colspan="${formData.actualData?size + 2}">数据对比：</td>
  </tr>
  <tr>
    <td class="th_title_bg">单位：元</td>
    <#list formData.actualData as sph>
    <td class="th_title_bg">${sph.productName!'-/-'}</td>
  </#list>
  <td class="th_title_bg">总额（元）</td>
  </tr>
  <tr>
    <td class="font-size-12">应开</td>
    <#list formData.actualData as sph>
    <#assign firstSum=firstSum + (sph.amount!0)>
    <td class="font-size-12">${(sph.amount!0)?string(',##0.00')}</td>
  </#list>
  <td class="font-size-12">${firstSum?string(',##0.00')}</td>
  </tr>
  <tr>
    <td class="font-size-12">实开</td>
    <#list formData.actualData as sph>
    <#assign secondSum=secondSum + (sph.fixAmount!0)>
    <td class="font-size-12">${(sph.fixAmount!0)?string(',##0.00')}</td>
  </#list>
  <td class="font-size-12">${secondSum?string(',##0.00')}</td>
  </tr>
  <tr>
    <td class="font-size-12">差异</td>
    <#list formData.actualData as sph>
    <td class="font-size-12">
      ${((sph.amount!0) - (sph.fixAmount!0))?string(',##0.00')}
    </td>
  </#list>
  <td class="font-size-12">
    ${(firstSum - secondSum)?string(',##0.00')}
  </td>
  </tr>
</table>
</#if>

<!-- 回款信息 -->
<#if formData.handleNodes?? && formData.handleNodes?seq_contains('INVOICE')>
<#include "./oa_common_payment_plans.html">
</#if>