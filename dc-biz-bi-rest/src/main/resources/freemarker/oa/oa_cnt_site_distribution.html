<table cellspacing="0">
  <tr>
    <th class="t-title" colspan="6">申请内容</th>
  </tr>
  <tr>
    <td class="th_title item_label">站点名称</td>
    <td colspan="5" class="font-size-12">
      <#if formData.siteNameList??>
      ${formData.siteNameList?join(',')}
      <#else>
      ${formData.siteName!'--'}
      </#if>
    </td>
  </tr>
  <tr>
    <td class="th_title item_label">场站概述</td>
    <td colspan="5" class="font-size-12">${formData.siteRemark!'--'}</td>
  </tr>
  <tr>
    <td class="th_title item_label">客户名称</td>
    <td colspan="2" class="font-size-12">${formData.customerName!'--'}</td>
    <td class="th_title item_label">开户银行</td>
    <td colspan="2" class="font-size-12">${formData.bankName!'--'}</td>
  </tr>
  <tr>
    <td class="th_title item_label">银行账号</td>
    <td colspan="2" class="font-size-12">
      ${formData.bankAccount!'--'}
    </td>
    <td class="th_title item_label">结算账期</td>
    <td colspan="2" class="font-size-12">
      <#if formData.billDateRange??>
      ${formData.billDateRange.startTime!'--'}
      ~
      ${formData.billDateRange.endTime!'--'}
      <#else>--</#if>
    </td>
  </tr>
  <tr>
    <td class="th_title item_label">备注说明</td>
    <td colspan="5" class="font-size-12">
      <#attempt><#outputformat 'XML'>${formData.note!'--'}</#outputformat><#recover>Failed</#attempt>
    </td>
  </tr>
</table>

<!-- 付款信息 -->
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title item_label">付款公司</td>
    <td class="font-size-12">
      ${formData.payCompanyName!'--'}
    </td>
    <td class="th_title item_label">付款金额</td>
    <td class="font-size-12" colspan="2">
      <#if formData.settBillSummary?? && formData.settBillSummary.actualFee??>
      <#assign tmpActualFee=formData.settBillSummary.actualFee>
      ${((tmpActualFee.elecFee!0) + (tmpActualFee.servFee!0) + (tmpActualFee.parkFee!0))?string(',##0.00')}
      <#else>--</#if>
    </td>
  </tr>
</table>

<!-- 运营合约 -->
<#assign loc_contract_title="运营合约：">
<#include "./oa_common_contracts.html">

<!-- 平台统计信息 -->
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title" colspan="5">结算信息：</td>
  </tr>
  <tr>
    <td class="th_title_bg"></td>
    <td class="th_title_bg">电费</td>
    <td class="th_title_bg">服务费</td>
    <td class="th_title_bg">停车超时费</td>
    <td class="th_title_bg">结算总金额</td>
  </tr>
  <#assign elecFeeSum=0 servFeeSum=0 parkFeeSum=0>
  <#if formData.settBillSummary?? && formData.settBillSummary.settJobBillList??>
  <#list formData.settBillSummary.settJobBillList as bill>
  <tr>
    <td class="font-size-12" style="width: 40px">
      ${bill.billNo!'--'}
    </td>
    <td class="font-size-12">
      <#assign elecFeeSum=elecFeeSum+(bill.elecFee!0)>
      ${(bill.elecFee!0)?string(',##0.00')}
    </td>
    <td class="font-size-12">
      <#assign servFeeSum=servFeeSum+(bill.servFee!0)>
      ${(bill.servFee!0)?string(',##0.00')}
    </td>
    <td class="font-size-12">
      <#assign parkFeeSum=parkFeeSum+(bill.parkFee!0)>
      ${(bill.parkFee!0)?string(',##0.00')}
    </td>
    <td class="font-size-12">
      ${((bill.elecFee!0) + (bill.servFee!0) + (bill.parkFee!0))?string(',##0.00')}
    </td>
  </tr>
  </#list>
  </#if>
  <tr>
    <td class="font-size-12">平台汇总</td>
    <td class="font-size-12">
      ${(elecFeeSum!0)?string(',##0.00')}
    </td>
    <td class="font-size-12">
      ${(servFeeSum!0)?string(',##0.00')}
    </td>
    <td class="font-size-12">
      ${(parkFeeSum!0)?string(',##0.00')}
    </td>
    <td class="font-size-12">
      ${((elecFeeSum!0) + (servFeeSum!0) + (parkFeeSum!0))?string(',##0.00')}
    </td>
  </tr>
  <#assign tmpActualFee=formData.settBillSummary.actualFee>
  <tr>
    <td class="font-size-12 font-bold">实际结算</td>
    <td class="font-size-12 font-bold">
      ${(tmpActualFee.elecFee!0)?string(',##0.00')}
    </td>
    <td class="font-size-12 font-bold">
      ${(tmpActualFee.servFee!0)?string(',##0.00')}
    </td>
    <td class="font-size-12 font-bold">
      ${(tmpActualFee.parkFee!0)?string(',##0.00')}
    </td>
    <td class="font-size-12 font-bold">
      ${((tmpActualFee.elecFee!0) + (tmpActualFee.servFee!0) + (tmpActualFee.parkFee!0))?string(',##0.00')}
    </td>
  </tr>
  <#assign elecFeeDiff=elecFeeSum - (tmpActualFee.elecFee!0)
  servFeeDiff=servFeeSum - (tmpActualFee.servFee!0)
  parkFeeDiff=parkFeeSum - (tmpActualFee.parkFee!0) >
  <tr>
    <td class="font-size-12">差异</td>
    <td class="font-size-12">
      ${elecFeeDiff?string(',##0.00')}
    </td>
    <td class="font-size-12">
      ${servFeeDiff?string(',##0.00')}
    </td>
    <td class="font-size-12">
      ${parkFeeDiff?string(',##0.00')}
    </td>
    <td class="font-size-12">
      ${(elecFeeDiff + servFeeDiff + parkFeeDiff)?string(',##0.00')}
    </td>
  </tr>
  <tr>
    <td class="th_title" colspan="1">差异说明</td>
    <td class="th_title" colspan="4">
      ${formData.differenceRemark!'--'}
    </td>
  </tr>
</table>

<!-- 是否用印 -->
<#if formData.handleNodes?? && formData.handleNodes?seq_contains('SIGNET')>
<#include "./oa_common_signet.html">
</#if>