<table cellspacing="0">
  <tr>
    <th class="t-title" colspan="6">申请内容</th>
  </tr>
  <tr>
    <!-- 不显示申请状态
    <td class="th_title item_label">申请状态</td>
    <td colspan="2" class="font-size-12">
      <#assign applyStatusMap={
      "NEW": "待初审",
      "RECHECK": "待复审",
      "CHECK_FAILED": "审核失败",
      "FINISH": "已完成",
      "REJECT": "复核失败",
      "PASSED": "已复核"
      }>
      ${applyStatusMap[formData.status]}
    </td>
    -->
    <td class="th_title item_label">充值类型</td>
    <td colspan="2" class="font-size-12">
      <#assign flowTypeMap={
      "IN_FLOW": "充值",
      "OUT_FLOW": "减少"
      }>
      ${flowTypeMap[formData.flowType]!'--'}
    </td>
    <td class="th_title item_label">申请人</td>
    <td colspan="2" class="font-size-12">${formData.applierName!'--'}</td>
  </tr>
  <tr>
    <td class="th_title item_label">申请时间</td>
    <td colspan="5" class="font-size-12">${formData.createTime?datetime?string('yyyy/MM/dd HH:mm:ss')}</td>
  </tr>
  <tr>
    <td class="th_title item_label">账户手机号</td>
    <td colspan="2" class="font-size-12">${formData.phone!'--'}</td>
    <td class="th_title item_label">账户类型</td>
    <td colspan="2" class="font-size-12">
      <#assign accountTypeMap={
      "CORP": "企业账户",
      "PERSONAL": "个人账户",
      "COMMERCIAL": "商户会员"
      }>
      ${accountTypeMap[formData.accountType]!'--'}
    </td>
  </tr>
  <tr>
    <td class="th_title item_label">账户名称</td>
    <td colspan="5" class="font-size-12">
      ${accountTypeMap[formData.accountType]!'--'}
      -
      <#if formData.accountType?? && formData.accountType == 'COMMERCIAL'>
      ${formData.commName!'--'}
      <#else>
      ${formData.username!'--'}
      </#if>
    </td>
  </tr>
  <tr>
    <td class="th_title item_label">充值单号</td>
    <td colspan="5" class="font-size-12">${formData.orderId!'--'}</td>
  </tr>
  <#if formData.accountType=='CORP'>
  <tr>
    <td class="th_title item_label">企客摘要</td>
    <td colspan="5" class="font-size-12">${formData.corpDigest!'--'}</td>
  </tr>
</#if>
</table>

<!-- 运营合约 -->
<#if formData.accountType?? && formData.accountType=='CORP'>
<#assign loc_contract_title="运营合约：">
<#include "./oa_common_contracts.html">
</#if>

<!-- 金额信息 -->
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title" colspan="5">金额：</td>
  </tr>
  <tr>
    <td class="th_title_bg">变更总金额（元）</td>
    <td class="th_title_bg">实际金额（元）</td>
    <td class="th_title_bg">赠送金额（元）</td>
  </tr>
  <tr>
    <td class="font-size-12">
      <#if formData.flowType == 'OUT_FLOW'>-<#else>+</#if>
      ${((formData.amount!0)+(formData.freeAmount!0))?string(',##0.00')}
    </td>
    <td class="font-size-12">
      ${(formData.amount!0)?string(',##0.00')}
    </td>
    <td class="font-size-12">
      ${(formData.freeAmount!0)?string(",##0.00")}
    </td>
  </tr>
</table>

<!-- 退款信息 -->
<#if formData.flowType?? && formData.flowType=='OUT_FLOW'>
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title_bg">退款平台</td>
    <td class="th_title_bg">退款单号</td>
    <td class="th_title_bg">退款时间</td>
  </tr>
  <tr>
    <td class="font-size-12">
      <#assign payChannelMap={
      "ALIPAY": "支付宝",
      "WXPAY": "微信支付",
      "BANK_CARD": "银行卡支付",
      "BUSINESS_ACCOUNT": "对公转账",
      "DIGICCY_ACCOUNT": "数字货币",
      "WX_CREDIT": "微信信用充",
      "ALIPAY_CREDIT": "支付宝信用充",
      "ABC_BANK": "农行账户",
      "CCB_ECNY_BANK": "建设银行数字人民币",
      "OTHER": "其他"
      }>
      ${payChannelMap[formData.payChannel!'']!'--'}
    </td>
    <td class="font-size-12">${formData.refundOrderId!'--'}</td>
    <td class="font-size-12">
      <#if formData.payTime??>
      ${formData.payTime?datetime?string('yyyy/MM/dd HH:mm:ss')}
      <#else>--</#if>
    </td>
  </tr>
</table>
</#if>

<!-- 支付信息 -->
<#if formData.flowType?? && formData.flowType=='IN_FLOW'>
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title item_label_2">支付方式</td>
    <td class="font-size-12">
      <#assign payChannelMap={
      "ALIPAY": "支付宝",
      "WXPAY": "微信支付",
      "BANK_CARD": "银行卡支付",
      "BUSINESS_ACCOUNT": "对公转账",
      "DIGICCY_ACCOUNT": "数字货币",
      "WX_CREDIT": "微信信用充",
      "ALIPAY_CREDIT": "支付宝信用充",
      "ABC_BANK": "农行账户",
      "CCB_ECNY_BANK": "建设银行数字人民币",
      "OTHER": "其他"
      }>
      ${payChannelMap[formData.payChannel!'']!'--'}
    </td>
    <td class="th_title item_label_2">支付账户</td>
    <td class="font-size-12">${formData.outAccountName!'--'}</td>
  </tr>
  <tr>
    <td class="th_title item_label_2">支付银行</td>
    <td class="font-size-12">${formData.outBankName!'--'}</td>
    <td class="th_title item_label_2">支付银行账号</td>
    <td class="font-size-12">${formData.outAccountNo!'--'}</td>
  </tr>
</table>
</#if>

<!-- 收款方信息 -->
<#if formData.flowType?? && formData.flowType=='IN_FLOW'>
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title item_label_2">收款账户类型</td>
    <td class="font-size-12">
      <#assign flowInAccountTypeMap={
      "ALIPAY": "支付宝账户",
      "TENPAY": "微信账户",
      "BANK_CARD": "银行卡账户",
      "BUSINESS_ACCOUNT": "对公账户",
      "DIGICCY_ACCOUNT": "数字货币",
      "ABC_BANK": "农行账户",
      "CCB_ECNY_BANK": "建设银行数字人民币"
      }>
      ${flowInAccountTypeMap[formData.flowInAccountType!'']!'--'}
    </td>
    <td class="th_title item_label_2">收款账户</td>
    <td class="font-size-12">${formData.inAccountName!'--'}</td>
  </tr>
  <tr>
    <td class="th_title item_label_2">收款银行</td>
    <td class="font-size-12">${formData.inBankName!'--'}</td>
    <td class="th_title item_label_2">收款银行账号</td>
    <td class="font-size-12">${formData.inAccountNo!'--'}</td>
  </tr>
</table>
</#if>

<!-- 开票信息 -->
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title_bg">开票状态</td>
    <td class="th_title_bg">开票种类</td>
    <td class="th_title_bg">发票号码</td>
  </tr>
  <tr>
    <td class="font-size-12">
      <#assign taxStatusMap={
      "CANT": "不可开票",
      "NO": "未开票",
      "PART": "部分开票",
      "YES": "已开票"
      }>
      ${taxStatusMap[formData.taxStatus]!'--'}
    </td>
    <td class="font-size-12">
      <#assign taxTypeMap={
      "NORMAL_TAX": "个人普通发票",
      "PREPAY_TAX": "企业普通发票",
      "SPECIAL_VAT": "企业专业发票"
      }>
      ${taxTypeMap[formData.taxType!'']!'--'}
    </td>
    <td class="font-size-12">${formData.taxNo!'--'}</td>
  </tr>
</table>

<!-- 回款计划 -->
<#include "./oa_common_payment_plans.html">