<#if formData.contracts?? && (formData.contracts?size > 0)>
<#assign contractTypeMap=
{"1": { "label": "每期金额(元)", "value": "rentAmount" },
"8": { "label": "分成约定", "value": "shareAgreement" },
"13": { "label": "价格约定", "value": "priceAgreement" }
}>
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title" colspan="2">${loc_contract_title}</td>
  </tr>
  <tr>
    <td class="th_title_bg">合约名称</td>
    <#assign abstractDetail={}>
    <#list formData.contracts as contract>
    <#if contract.contractType?? && contractTypeMap?keys?seq_contains(contract.contractType)>
      <#assign abstractDetail=abstractDetail+{contractTypeMap[contract.contractType].label:1}>
    </#if>
    </#list>
    <td class="th_title_bg">${abstractDetail?keys?join("/")}</td>
  </tr>
  <#list formData.contracts as contract>
  <tr>
    <td class="font-size-12">
      <#if contract.files?? && (contract.files?size > 0)>
      <#list contract.files as file>
      <div>${file.refName!'--'}</div>
    </#list>
    <#else>--</#if>
    </td>
    <td class="font-size-12">
      <#if contract.detail?? && contract.contractType?? && contractTypeMap?keys?seq_contains(contract.contractType)>
      <#attempt><#outputformat 'XML'>
        ${contract.detail[contractTypeMap[contract.contractType].value]!'--'}
      </#outputformat><#recover>Failed</#attempt>
      <#else>--</#if>
    </td>
  </tr>
  </#list>
</table>
</#if>