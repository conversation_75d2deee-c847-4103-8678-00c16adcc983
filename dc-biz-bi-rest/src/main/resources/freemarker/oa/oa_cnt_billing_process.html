<table cellspacing="0">
  <tr>
    <th class="t-title" colspan="6">申请内容</th>
  </tr>
  <tr>
    <td class="th_title" style="width: 80px">企业名称</td>
    <td colspan="5" class="font-size-12">${formData.corpName!'--'}</td>
  </tr>
  <tr>
    <td class="th_title" style="width: 80px">企客摘要</td>
    <td colspan="5" class="font-size-12">${formData.corpDigest!'--'}</td>
  </tr>
  <tr>
    <td class="th_title" style="width: 80px">关联站点</td>
    <td colspan="5" class="font-size-12">
      <#if formData.settleBillList?? && (formData.settleBillList?size>0)>
        <#assign siteNameJoin=''>
        <#list formData.settleBillList as sett>
          <#if sett.siteNameList?? && (sett.siteNameList?size>0)>
            <#assign siteNameJoin=siteNameJoin+(sett.siteNameList?join('/'))>
          </#if>
        </#list>
        ${siteNameJoin}
      <#else>--</#if>
    </td>
  </tr>
  <tr>
    <td class="th_title" style="width: 80px">账期日期</td>
    <td colspan="5" class="font-size-12">
      <#if formData.billDateRange?? && formData.billDateRange.startTime?? && formData.billDateRange.endTime??>
      ${formData.billDateRange.startTime?datetime?string('yyyy-MM-dd HH:mm')}
      ~
      ${formData.billDateRange.endTime?datetime?string('yyyy-MM-dd HH:mm')}
      <#else>--</#if>
    </td>
  </tr>
  <tr>
    <td class="th_title" style="width: 80px">备注说明</td>
    <td colspan="5" class="font-size-12">
      <#attempt><#outputformat 'XML'>${formData.note!'--'}</#outputformat><#recover>Failed</#attempt>
    </td>
  </tr>
</table>

<!-- 生效合约 -->
<#assign loc_contract_title="生效合约：">
<#include "./oa_common_contracts.html">

<!-- 平台账单信息/结算信息信息 -->
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title" colspan="6">平台账单：</td>
  </tr>
  <tr>
    <td class="th_title_bg">结算单名称</td>
    <td class="th_title_bg">结算周期</td>
    <td class="th_title_bg">电量（kW·h）</td>
    <td class="th_title_bg">电费（元）</td>
    <td class="th_title_bg">服务费（元）</td>
    <td class="th_title_bg">结算金额（元）</td>
  </tr>
  <#if formData.settleBillList?? && (formData.settleBillList?size>0)>
    <#list formData.settleBillList as sett>
      <tr>
        <td class="font-size-12">${sett.billName!'--'}</td>
        <td class="font-size-12">
          <span>${sett.startDateDay?date?string('MM-dd')}</span>
          ~
          <span>${sett.endDateDay?date?string('MM-dd')}</span>
        </td>
        <td class="font-size-12">${(sett.elec!0)?string(',##0.0000')}</td>
        <td class="font-size-12">${(sett.elecFee!0)?string(',##0.00')}</td>
        <td class="font-size-12">${(sett.servFee!0)?string(',##0.00')}</td>
        <td class="font-size-12">${(sett.totalFee!0)?string(',##0.00')}</td>
      </tr>
    </#list>
  <#else>
  <tr>
    <td class="list_non_data" colspan="6">暂无数据</td>
  </tr>
  </#if>
  <tr>
    <td class="th_title" colspan="6">结算信息：</td>
  </tr>
  <tr>
    <td class="th_title_bg" rowspan="2">
      <div>名称</div><div>(账单号)</div>
    </td>
    <td class="th_title_bg" rowspan="2">总电量(kW·h)</td>
    <td class="th_title_bg" rowspan="2">总电费(元)</td>
    <td class="th_title_bg" colspan="2">合计为总服务费</td>
    <td class="th_title_bg" rowspan="2">结算金额(元)</td>
  </tr>
  <tr>
    <td class="th_title_bg">服务费(元)</td>
    <td class="th_title_bg">其它费用(元)</td>
  </tr>
  <#assign elecSum=0 elecFeeSum=0 servFeeSum=0 otherFeeSum=0>
  <#if formData.settBillSummary?? && formData.settBillSummary.settleBillList??>
  <#list formData.settBillSummary.settleBillList as bill>
  <tr>
    <td class="font-size-12" style="width: 40px">
      <div>${bill.billName!'--'}</div>
      <div>${bill.billNo!'--'}</div>
    </td>
    <td class="font-size-12">
      <#assign elecSum=elecSum+(bill.elec!0)>
      ${(bill.elec!0)?string(',##0.0000')}
    </td>
    <td class="font-size-12">
      <#assign elecFeeSum=elecFeeSum+(bill.elecFee!0)>
      ${(bill.elecFee!0)?string(',##0.00')}
    </td>
    <td class="font-size-12">
      <#assign servFeeSum=servFeeSum+(bill.servFee!0)>
      ${(bill.servFee!0)?string(',##0.00')}
    </td>
    <td class="font-size-12">
      <#assign otherFeeSum=otherFeeSum+(bill.otherFee!0)>
      ${(bill.otherFee!0)?string(',##0.00')}
    </td>
    <td class="font-size-12">
      ${((bill.elecFee!0) + (bill.servFee!0) + (bill.otherFee!0))?string(',##0.00')}
    </td>
  </tr>
  </#list>
  </#if>
  <#assign tmpActualData=formData.settBillSummary.actualData>
  <tr>
    <td class="font-size-12  font-bold">实际结算</td>
    <td class="font-size-12  font-bold">
      ${(tmpActualData.elec!0)?string(',##0.0000')}
    </td>
    <td class="font-size-12 font-bold">
      ${(tmpActualData.elecFee!0)?string(',##0.00')}
    </td>
    <td class="font-size-12 font-bold">
      ${(tmpActualData.servFee!0)?string(',##0.00')}
    </td>
    <td class="font-size-12 font-bold">
      ${(tmpActualData.otherFee!0)?string(',##0.00')}
    </td>
    <td class="font-size-12 font-bold">
      <#assign tmpSum=(tmpActualData.elecFee!0) + (tmpActualData.servFee!0) + (tmpActualData.otherFee!0)>
      ${tmpSum?string(',##0.00')}
    </td>
  </tr>
  <tr>
    <td class="font-size-12">平台汇总</td>
    <td class="font-size-12">
      ${(elecSum!0)?string(',##0.0000')}
    </td>
    <td class="font-size-12">
      ${(elecFeeSum!0)?string(',##0.00')}
    </td>
    <td class="font-size-12">
      ${(servFeeSum!0)?string(',##0.00')}
    </td>
    <td class="font-size-12">
      ${(otherFeeSum!0)?string(',##0.00')}
    </td>
    <td class="font-size-12">
      ${((elecFeeSum!0) + (servFeeSum!0) + (otherFeeSum!0))?string(',##0.00')}
    </td>
  </tr>
  <#assign elecDiff=(tmpActualData.elec!0) - elecSum
  elecFeeDiff=(tmpActualData.elecFee!0) - elecFeeSum
  servFeeDiff=(tmpActualData.servFee!0) - servFeeSum
  otherFeeDiff=(tmpActualData.otherFee!0) - otherFeeSum >
  <tr>
    <td class="font-size-12">差异</td>
    <td class="font-size-12">
      <#if elecDiff gt 0>${elecDiff?string(',##0.0000')}
      <#else>0
      </#if>
    </td>
    <td class="font-size-12">
      <#if elecFeeDiff gt 0>${elecFeeDiff?string(',##0.00')}
      <#else>0
      </#if>
    </td>
    <td class="font-size-12">
      <#if servFeeDiff gt 0>${servFeeDiff?string(',##0.00')}
      <#else>0
      </#if>
    </td>
    <td class="font-size-12">
      <#if otherFeeDiff gt 0>${otherFeeDiff?string(',##0.00')}
      <#else>0
      </#if>
    </td>
    <td class="font-size-12">
      <#if (elecFeeDiff + servFeeDiff + otherFeeDiff) gt 0>
        ${(elecFeeDiff + servFeeDiff + otherFeeDiff)?string(',##0.00')}
      <#else>0
      </#if>
    </td>
  </tr>
  <tr>
    <td class="th_title item_label" colspan="1" style="width: 80px">差异说明</td>
    <td class="th_title" colspan="5">
      ${formData.differenceRemark!'--'}
    </td>
  </tr>
</table>

<!-- 是否用印 -->
<#if formData.handleNodes?? && formData.handleNodes?seq_contains('SIGNET')>
<#include "./oa_common_signet.html">
</#if>

<!-- 是否开票 -->
<#if formData.handleNodes?? && formData.handleNodes?seq_contains('INVOICE')>
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title item_label">是否开票</td>
    <td class="font-size-12">
      ${formData.handleNodes?seq_contains('INVOICE')?string('是', '否')}
    </td>
    <td class="th_title item_label">开票方式</td>
    <td class="font-size-12" colspan="2">
      <#if formData.invoiceWay??>
        <#switch formData.invoiceWay>
        <#case "PRE_PAY">充值预开票<#break>
        <#case "POST_CHARGER">充电后开票<#break>
        <#case "POST_SETTLEMENT">后付费账单开票<#break>
        <#default>未知
      </#switch>
      <#else>--</#if>
    </td>
  </tr>
  <tr>
    <td class="th_title item_label">抬头信息</td>
    <td class="font-size-12" colspan="4">
      ${formData.invoiceBuyerInfo.name!'--'}
    </td>
  </tr>
</table>
<!-- 发票模板 -->
<table cellspacing="0" style="margin: -1px 0;">
  <#if formData.invoiceBuyerInfo.type == 'ENTER_COMMON'>
    <#include "./invoice_buyer_info_corp_common.html">
  <#elseif formData.invoiceBuyerInfo.type == 'PER_COMMON'>
    <#include "./invoice_buyer_info_per_common.html">
  <#elseif formData.invoiceBuyerInfo.type == 'ENTER_PROFESSION'>
    <#include "./invoice_buyer_info_corp_profession.html">
  </#if>
</table>
<!-- 发票信息 -->
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title item_label">开票主体</td>
    <td class="font-size-12" colspan="4">
      ${formData.invoicedTempSalName!'--'}
    </td>
  </tr>
  <tr>
    <td class="th_title item_label">商品模板</td>
    <td class="font-size-12" colspan="4">
      ${formData.invoicedTempSalRefName!'--'}
    </td>
  </tr>
  <tr>
    <td class="th_title" colspan="5">开票内容：</td>
  </tr>
  <#list formData.invoiceRecords as invRec>
    <#if invRec.contents?? && (invRec.contents?size > 0)>
    <tr>
      <td class="th_title_bg">名称</td>
      <td class="th_title_bg">规格</td>
      <td class="th_title_bg">单位</td>
      <td class="th_title_bg">税率（%）</td>
      <td class="th_title_bg">数量</td>
      <td class="th_title_bg">单价</td>
      <td class="th_title_bg">开票金额（元）</td>
    </tr>
  <#list invRec.contents as sph>
  <tr>
    <td class="font-size-12">${sph.productName!'--'}</td>
    <td class="font-size-12">${sph.spec!'--'}</td>
    <td class="font-size-12">${sph.unit!'--'}</td>
    <td class="font-size-12">${sph.taxRate!0}</td>
    <td class="font-size-12">${sph.num!0}</td>
    <td class="font-size-12">
      <#if sph.num?? && (sph.num > 0)>
        ${((sph.fixAmount!0) / (sph.num!0))?string(',##0.00')}
        <#else>--
    </#if>
    </td>
    <td class="font-size-12">${(sph.fixAmount!0)?string(',##0.00')}</td>
  </tr>
  </#list>
  </#if>
  <tr>
    <td class="th_title item_label">票面备注</td>
    <td class="font-size-12" colspan="4">
      ${invRec.remark!'--'}
    </td>
  </tr>
  <tr><td></td></tr>
  </#list>  <!-- formData.invoiceRecords -->
</table>
<!-- 数据对比 -->
<#assign firstSum=0 secondSum=0>
<table style="margin-top: 15px" cellspacing="0">
  <tr>
    <td class="th_title" colspan="${formData.actualData?size + 2}">数据对比：</td>
  </tr>
  <tr>
    <td class="th_title_bg">单位：元</td>
    <#list formData.actualData as sph>
    <td class="th_title_bg">${sph.productName!'-/-'}</td>
  </#list>
  <td class="th_title_bg">总额（元）</td>
  </tr>
  <tr>
    <td class="font-size-12">应开</td>
    <#list formData.actualData as sph>
    <#assign firstSum=firstSum + (sph.amount!0)>
    <td class="font-size-12">${(sph.amount!0)?string(',##0.00')}</td>
  </#list>
  <td class="font-size-12">${firstSum?string(',##0.00')}</td>
  </tr>
  <tr>
    <td class="font-size-12">实开</td>
    <#list formData.actualData as sph>
    <#assign secondSum=secondSum + (sph.fixAmount!0)>
    <td class="font-size-12">${(sph.fixAmount!0)?string(',##0.00')}</td>
  </#list>
  <td class="font-size-12">${secondSum?string(',##0.00')}</td>
  </tr>
  <tr>
    <td class="font-size-12">差异</td>
    <#list formData.actualData as sph>
    <td class="font-size-12">
      ${((sph.amount!0) - (sph.fixAmount!0))?string(',##0.00')}
    </td>
  </#list>
  <td class="font-size-12">
    ${(firstSum - secondSum)?string(',##0.00')}
  </td>
  </tr>
</table>
</#if>

<!-- 回款信息 -->
<#if formData.handleNodes?? && formData.handleNodes?seq_contains('INVOICE')>
<#include "./oa_common_payment_plans.html">
</#if>