<#if historyLedgerList?? && (historyLedgerList?size > 0)>
<#setting number_format="0.000000">
<table style="margin-top: 30px" cellspacing="0">
  <tr>
    <th class="t-title" colspan="19">审核参考</th>
  </tr>
  <tr>
    <th class="th_title font-size-16" colspan="19">历史台账</th>
  </tr>
  <tr>
    <td class="th_title_bg center font-size-14" rowspan="2">账期</td>
    <td class="th_title_bg center font-size-14" rowspan="2">
      <div style="width: 60px !important;">充电收入</div>
      <div class="font-size-12">(万元)</div>
    </td>
    <td class="th_title_bg center font-size-14" colspan="9">支出(万元)</td>
    <td class="th_title_bg center font-size-14" colspan="2">利润(万元)</td>
    <td class="th_title_bg center font-size-14" colspan="4">电量(万度)</td>
  </tr>
  <tr>
    <td class="th_title_bg center font-size-14">平台电费</td>
    <td class="th_title_bg center font-size-14">实缴电费</td>
    <td class="th_title_bg center font-size-14">租金</td>
    <td class="th_title_bg center font-size-14">分成</td>
    <td class="th_title_bg center font-size-14">劳务</td>
    <td class="th_title_bg center font-size-14">引流</td>
    <td class="th_title_bg center font-size-14">运维</td>
    <td class="th_title_bg center font-size-14">折旧</td>
    <td class="th_title_bg center font-size-14">其他</td>
    <td class="th_title_bg center font-size-14">服务费</td>
    <td class="th_title_bg center font-size-14">净服务费</td>
    <td class="th_title_bg center font-size-14">订单电量</td>
    <td class="th_title_bg center font-size-14">
      <div>效率</div>
      <div class="font-size-12">(%)</div>
    </td>
    <td class="th_title_bg center font-size-14">电费账单</td>
    <td class="th_title_bg center font-size-14">
      <div>电损</div>
      <div class="font-size-12">(%)</div>
    </td>
  </tr>
  <#assign incomeSum=0 elecFeeSum=0 exElecFeeSum=0 rentFeeSum=0
    divideFeeSum=0 laborFeeSum=0 attractFeeSum=0 opsFeeSum=0 depreciationFeeSum=0
    otherFeeSum=0 servFeeSum=0 netServFeeSum=0 elecSum=0 efficiencySum=0 numberSum=0>
  <#list historyLedgerList as item>
    <#assign income=0 elecFee=0 exElecFee=0 servFee=0
      rentFee=0 divideFee=0 laborFee=0 attractFee=0 elec=0 number=0><!-- 临时变量 -->
    <#if item_index % 2 == 0>
    <tr>
    <#else>
    <tr style="background-color: #f1f1f1">
    </#if>
      <td class="center font-size-12" style="width: 40px !important;">
        ${item.month.format('MM月')}
      </td>
      <td class="center font-size-12" style="width: 60px !important;">
        <#if item.income?? && (item.income.elecFee?? || item.income.servFee??)>
        <#assign income=(((item.income.elecFee!0) + (item.income.servFee!0)) / 10000)
          incomeSum=incomeSum+income>
        <#if income != 0>
        ${income?string(',##0.00')}
        <#else>0</#if>
        <#else>0</#if>
      </td><!-- 充电收入: income.elecFee + income.servFee -->
      <td class="center font-size-12" style="width: 40px;">
        <#if item.income?? && item.income.elecFee??>
        <#assign elecFee=(item.income.elecFee / 10000)
        elecFeeSum=elecFeeSum+elecFee>
        <#if elecFee != 0>
          ${elecFee?string(',##0.00')}
        <#else>0</#if>
        <#else>0</#if>
      </td><!-- 平台电费: income.elecFee -->
      <td class="center font-size-12" style="width: 40px;">
        <#if item.expense?? && item.expense.elecFee??>
        <#assign exElecFee=(item.expense.elecFee / 10000)
        exElecFeeSum=exElecFeeSum+exElecFee>
        <#if exElecFee != 0>
        ${exElecFee?string(',##0.00')}
        <#else>0</#if>
        <#else>0</#if>
      </td><!-- 实缴电费: expense.elecFee -->
      <td class="center font-size-12" style="width: 40px;">
        <#if item.expense?? && item.expense.rentFee??>
        <#assign rentFee=(item.expense.rentFee / 10000)
        rentFeeSum=rentFeeSum+rentFee>
        <#if rentFee != 0>
        ${rentFee?string(',##0.00')}
        <#else>0</#if>
        <#else>0</#if>
      </td><!-- 租金: expense.rentFee -->
      <td class="center font-size-12" style="width: 40px;">
        <#if item.expense?? && item.expense.divideFee??>
        <#assign divideFee=(item.expense.divideFee / 10000)
        divideFeeSum=divideFeeSum+divideFee>
        <#if divideFee != 0>
        ${divideFee?string(',##0.00')}
        <#else>0</#if>
        <#else>0</#if>
      </td><!-- 分成: expense.divideFee -->
      <td class="center font-size-12" style="width: 40px;">
        <#if item.expense?? && item.expense.laborFee??>
        <#assign laborFee=(item.expense.laborFee / 10000)
        laborFeeSum=laborFeeSum+laborFee>
        <#if laborFee != 0>
        ${laborFee?string(',##0.00')}
        <#else>0</#if>
        <#else>0</#if>
      </td><!-- 劳务: expense.laborFee -->
      <td class="center font-size-12" style="width: 40px;">
        <#if item.expense?? && item.expense.attractFee??>
        <#assign attractFee=(item.expense.attractFee / 10000)
        attractFeeSum=attractFeeSum+attractFee>
        <#if attractFee != 0>
        ${attractFee?string(',##0.00')}
        <#else>0</#if>
        <#else>0</#if>
      </td><!-- 引流: expense.attractFee -->
      <td class="center font-size-12" style="width: 40px;">
        <#if item.expense?? && item.expense.opsFee??>
        <#assign opsFee=(item.expense.opsFee / 10000)
        opsFeeSum=opsFeeSum+opsFee>
        <#if opsFee != 0>
        ${opsFee?string(',##0.00')}
        <#else>0</#if>
        <#else>0</#if>
      </td><!-- 运维: expense.opsFee -->
      <td class="center font-size-12" style="width: 40px;">
        <#if item.expense?? && item.expense.depreciationFee??>
        <#assign depreciationFee=(item.expense.depreciationFee / 10000)
        depreciationFeeSum=depreciationFeeSum+depreciationFee>
        <#if depreciationFee != 0>
        ${depreciationFee?string(',##0.00')}
        <#else>0</#if>
        <#else>0</#if>
      </td><!-- 折旧: expense.depreciationFee -->
      <td class="center font-size-12" style="width: 40px;">
        <#if item.expense?? && item.expense.otherFee??>
        <#assign otherFee=(item.expense.otherFee / 10000)
        otherFeeSum=otherFeeSum+otherFee>
        <#if otherFee != 0>
        ${otherFee?string(',##0.00')}
        <#else>0</#if>
        <#else>0</#if>
      </td><!-- 其他: expense.otherFee -->
      <td class="center font-size-12" style="width: 40px;">
        <#if exElecFee?? && (exElecFee?number > 0)>
          <#assign servFee=(income?number - exElecFee?number)
          servFeeSum=servFeeSum+servFee>
          <#if servFee != 0>
          ${servFee?string(',##0.00')}
          <#else>0</#if>
        <#else>
          <#assign servFee=(income?number - elecFee?number)
          servFeeSum=servFeeSum+servFee?number>
          <#if servFee != 0>
          ${servFee?string(',##0.00')}
          <#else>0</#if>
        </#if>
      </td><!-- 服务费: 充电收入 - 电费（优先选择实缴电费、为空时选择平台电费） -->
      <td class="center font-size-12" style="width: 40px;">
        <#assign netServFee=(servFee - (rentFee + divideFee + laborFee + attractFee))
        netServFeeSum=netServFeeSum+netServFee>
        <#if netServFee != 0>
        ${netServFee?string(',##0.00')}
        <#else>0</#if>
      </td><!-- 净服务费: 充电收入 - 支出（电费+租金+分成+劳务+引流） -->
      <td class="center font-size-12" style="width: 40px;">
        <#if item.income?? && item.income.elec??>
        <#assign elec=(item.income.elec / 10000)
        elecSum=elecSum+elec>
        <#if elec != 0>
        ${elec?string(',##0.00')}
        <#else>0</#if>
        <#else>0</#if>
      </td><!-- 订单电量: elec -->
      <td class="center font-size-12" style="width: 40px;">
        <#if item.efficiency??>
        <#assign efficiency=100*(item.efficiency!0) efficiencySum=efficiencySum+efficiency>
        <#if efficiency != 0>
        ${efficiency?string(',##0.0')}
        <#else>0</#if>
        <#else>0</#if>
      </td><!-- 效率: efficiency -->
      <td class="center font-size-12" style="width: 40px;">
        <#if item.expense?? && item.expense.number??>
        <#assign number=(item.expense.number / 10000)
        numberSum=numberSum+number>
        <#if number != 0>
        ${number?string(',##0.00')}
        <#else>0</#if>
        <#else>0</#if>
      </td><!-- 电费账单: expense.number -->
      <td class="center font-size-12" style="width: 40px;">
        <#if showLossRate?? && showLossRate>
          <#if number?? && (number?number > 0)>
          ${((number?number - elec?number) / number?number * 100)?string(',##0.0')}
          <#else>0</#if>
        <#else><span>--</span></#if>
      </td><!-- 电损:（电费账单 - 订单电量）/ 电费账单 -->
    </tr>
  </#list>
  <tr>
    <td class="center font-size-12" style="width: 40px;max-width: 40px;">小计</td>
    <td class="center font-size-12" style="width: 60px;max-width: 60px;">
      <#if incomeSum != 0>
      ${incomeSum?string(',##0.00')}
      <#else>0</#if>
    </td>
    <td class="center font-size-12" style="width: 40px;max-width: 40px;">
      <#if elecFeeSum != 0>
      ${elecFeeSum?string(',##0.00')}
      <#else>0</#if>
    </td>
    <td class="center font-size-12" style="width: 40px;max-width: 40px;">
      <#if exElecFeeSum != 0>
      ${exElecFeeSum?string(',##0.00')}
      <#else>0</#if>
    </td>
    <td class="center font-size-12" style="width: 40px;max-width: 40px;">
      <#if rentFeeSum != 0>
      ${rentFeeSum?string(',##0.00')}
      <#else>0</#if>
    </td>
    <td class="center font-size-12" style="width: 40px;max-width: 40px;">
      <#if divideFeeSum != 0>
      ${divideFeeSum?string(',##0.00')}
      <#else>0</#if>
    </td>
    <td class="center font-size-12" style="width: 40px;max-width: 40px;">
      <#if laborFeeSum != 0>
      ${laborFeeSum?string(',##0.00')}
      <#else>0</#if>
    </td>
    <td class="center font-size-12" style="width: 40px;max-width: 40px;">
      <#if attractFeeSum != 0>
      ${attractFeeSum?string(',##0.00')}
      <#else>0</#if>
    </td>
    <td class="center font-size-12" style="width: 40px;max-width: 40px;">
      <#if opsFeeSum != 0>
      ${opsFeeSum?string(',##0.00')}
      <#else>0</#if>
    </td>
    <td class="center font-size-12" style="width: 40px;max-width: 40px;">
      <#if depreciationFeeSum != 0>
      ${depreciationFeeSum?string(',##0.00')}
      <#else>0</#if>
    </td>
    <td class="center font-size-12" style="width: 40px;max-width: 40px;">
      <#if otherFeeSum != 0>
      ${otherFeeSum?string(',##0.00')}
      <#else>0</#if>
    </td>
    <td class="center font-size-12" style="width: 40px;max-width: 40px;">
      <#if servFeeSum != 0>
      ${servFeeSum?string(',##0.00')}
      <#else>0</#if>
    </td>
    <td class="center font-size-12" style="width: 40px;max-width: 40px;">
      <#if netServFeeSum != 0>
      ${netServFeeSum?string(',##0.00')}
      <#else>0</#if>
    </td>
    <td class="center font-size-12" style="width: 40px;max-width: 40px;">
      <#if elecSum != 0>
      ${elecSum?string(',##0.00')}
      <#else>0</#if>
    </td>
    <td class="center font-size-12" style="width: 40px;max-width: 40px;">
      <#if efficiencySum != 0>
      ${(efficiencySum / 6)?string(',##0.0')}
      <#else>0</#if>
    </td>
    <td class="center font-size-12" style="width: 40px;max-width: 40px;">
      <#if numberSum != 0>
      ${numberSum?string(',##0.00')}
      <#else>0</#if>
    </td>
    <td class="center font-size-12" style="width: 40px;max-width: 40px;">
      <#if showLossRate?? && showLossRate>
        <#if numberSum?? && (numberSum > 0)>
        ${((numberSum - elecSum) / numberSum * 100)?string(',##0.0')}
        <#else>0</#if>
      <#else><span>--</span></#if>
    </td>
  </tr>
  <tr>
    <td class="color_7f7f7f font-size-10" colspan="17">
      <p>说明：</p>
      <p>
        <span>充电收入：后付费客户为账单金额、其它为充电订单金额</span>
        <span style="margin-left: 60px">服务费 = 充电收入 - 电费（优先选择实缴电费、为空时选择平台电费）</span>
      </p>
      <p>
        <span>平台电费：后付费客户为账单电费、其它为充电订单电费</span>
        <span style="margin-left: 60px">净服务费 = 充电收入 - 支出（电费+租金+分成+劳务+引流）</span>
      </p>
      <p>
        <span>订单电量：后付费客户为账单电量、其它为充电订单电量</span>
        <span style="margin-left: 60px">效率 = 订单电量/功率/当月天数/24</span>
      </p>
      <p>电损 =（电费账单 - 订单电量）/ 电费账单</p>
    </td>
  </tr>
</table>
</#if>