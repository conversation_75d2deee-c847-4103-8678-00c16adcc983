<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8" />
    <title>Title</title>
    <style>
        body {
            font-family: SimHei;
        }

        table {
            border-collapse: collapse;
            border-spacing: 0;
        }

        .header img {
            width: 385px;
        }

        .center_cell {
            text-align: center;
        }
    </style>
</head>

<body>
<!--    <div class="header">-->
<!--        <img src="https://dc-sz-1.oss-cn-shenzhen.aliyuncs.com/pdf/logo.png" />-->
<!--    </div>-->
    <table border="1" style="width: 100%;">
        <tr>
            <td align="center" colspan="24" style="height: 50px;">
                <#if vo?? && vo.siteName??>${vo.siteName}</#if>运营巡检报告
            </td>
        </tr>
        <tr>
            <td colspan="16">场站地址：<#if vo?? && vo.siteAddress??>${vo.siteAddress}</#if></td>
            <td colspan="8">巡检单号：<#if vo?? && vo.no??>${vo.no}</#if></td>
        </tr>
        <tr>
            <td colspan="12">巡检人：<#if vo?? && vo.roverName??>${vo.roverName}</#if></td>
            <td colspan="4">巡检时间：
                <#if vo?? && vo.roverTime??>
                    ${vo.roverTime?string.yyyy}-${vo.roverTime?string.MM}-${vo.roverTime?string.dd}
                </#if>
            </td>
            <td colspan="6">评分人：<#if vo?? && vo.raterName??>${vo.raterName}</#if></td>
            <td colspan="2">分值：<#if vo?? && vo.rank??>${vo.rank}</#if></td>
        </tr>
        <tr style="background-color: #acbeee;">
            <td class="center_cell" colspan="3">巡检项目</td>
            <td class="center_cell" colspan="12">要求</td>
            <td class="center_cell" colspan="1">是否正常</td>
            <td class="center_cell" colspan="6">照片/异常描述</td>
            <td class="center_cell" colspan="2">处理</td>
        </tr>
        <tr>
            <td class="center_cell" colspan="1" rowspan="6" style="width: 10px">场站资产</td>
            <td class="center_cell" colspan="2">充电桩</td>
            <td class="" colspan="12">充电桩设备齐全、 完好、 运行正常</td>
            <#assign statusText = "无">
            <#assign statusColor = "black">
            <#if po1?? && po1.fault??>
                <#if po1.fault == 1>
                    <#assign statusText = "正常">
                <#elseif po1.fault == 2>
                    <#assign statusText = "异常">
                    <#assign statusColor = "red">
                </#if>
            </#if>
            <td class="center_cell" colspan="2" style="color:${statusColor}">
                ${statusText}
            </td>
            <td class="" colspan="5">
                <#if po1?? && po1.faultDesc??>
                    ${po1.faultDesc}<br />
                </#if>
                <#if po1?? && po1.images??>
                    <#list po1.images as image>
                        <img src="${image.url}" width="100px" height="100px" />
                    </#list>
                </#if>
            </td>
            <td class="" colspan="2">
                <#if po1?? && po1.fixDesc??>
                    ${po1.fixDesc}
                </#if>
            </td>
        </tr>
        <#if pos1??>
        <#list pos1 as pos1Item>
            <tr>
                <td class="center_cell" colspan="2">
                    <#if pos1Item?? && pos1Item.type??>${pos1Item.type.desc}</#if>
                </td>
                <td class="" colspan="12">
                    <#if pos1Item?? && pos1Item.type??>${pos1Item.type.requirement}</#if>
                </td>
                <#assign statusText = "无">
                <#assign statusColor = "black">
                <#if pos1Item?? && pos1Item.fault??>
                    <#if pos1Item.fault == 1>
                        <#assign statusText = "正常">
                        <#elseif pos1Item.fault == 2>
                        <#assign statusText = "异常">
                        <#assign statusColor = "red">
                    </#if>
                </#if>
                <td class="center_cell" colspan="2" style="color:${statusColor}">
                    ${statusText}
                </td>
                <td class="" colspan="5">
                    <#if pos1Item?? && pos1Item.faultDesc??>
                        ${pos1Item.faultDesc}<br />
                    </#if>
                    <#if pos1Item?? && pos1Item.images??>
                        <#list pos1Item.images as image>
                            <img src="${image.url}" width="100px" height="100px" />
                        </#list>
                    </#if>
                </td>
                <td class="" colspan="2">
                    <#if pos1Item?? && pos1Item.fixDesc??>
                        ${pos1Item.fixDesc}
                    </#if>
                </td>
            </tr>
        </#list>
        </#if>
        <tr>
            <td class="center_cell" colspan="1" rowspan="4" style="width: 10px">场站环境</td>
            <td class="center_cell" colspan="2">场站地面</td>
            <td class="" colspan="12">场站地面整洁、 无杂物</td>
            <#assign statusText = "无">
            <#assign statusColor = "black">
            <#if po2?? && po2.fault??>
                <#if po2.fault == 1>
                    <#assign statusText = "正常">
                    <#elseif po2.fault == 2>
                    <#assign statusText = "异常">
                    <#assign statusColor = "red">
                </#if>
            </#if>
            <td class="center_cell" colspan="2" style="color:${statusColor}">
                ${statusText}
            </td>
            <td class="" colspan="5">
                <#if po2?? && po2.faultDesc??>
                ${po2.faultDesc}<br />
            </#if>
            <#if po2?? && po2.images??>
            <#list po2.images as image>
            <img src="${image.url}" width="100px" height="100px" />
            </#list>
            </#if>
            </td>
            <td class="" colspan="2">
                <#if po2?? && po2.fixDesc??>
                ${po2.fixDesc}
            </#if>
            </td>
        </tr>
        <#if pos2??>
        <#list pos2 as pos2Item>
        <tr>
            <td class="center_cell" colspan="2">
                <#if pos2Item?? && pos2Item.type??>${pos2Item.type.desc}</#if>
            </td>
            <td class="" colspan="12">
                <#if pos2Item?? && pos2Item.type??>${pos2Item.type.requirement}</#if>
            </td>
            <#assign statusText = "无">
            <#assign statusColor = "black">
            <#if pos2Item?? && pos2Item.fault??>
                <#if pos2Item.fault == 1>
                    <#assign statusText = "正常">
                    <#elseif pos2Item.fault == 2>
                    <#assign statusText = "异常">
                    <#assign statusColor = "red">
                </#if>
            </#if>
            <td class="center_cell" colspan="2" style="color:${statusColor}">
                ${statusText}
            </td>
            <td class="" colspan="5">
                <#if pos2Item?? && pos2Item.faultDesc??>
                ${pos2Item.faultDesc}<br />
            </#if>
            <#if pos2Item?? && pos2Item.images??>
            <#list pos2Item.images as image>
            <img src="${image.url}" width="100px" height="100px" />
            </#list>
            </#if>
            </td>
            <td class="" colspan="2">
                <#if pos2Item?? && pos2Item.fixDesc??>
                ${pos2Item.fixDesc}
            </#if>
            </td>
        </tr>
        </#list>
        </#if>
        <tr>
            <td class="center_cell" style="width: 10px">备注</td>
            <td class="" colspan="23">
                <#if vo?? && vo.comment??>
                    ${vo.comment}
                </#if>
            </td>
        </tr>
        <tr>
            <td class="center_cell" style="width: 10px">评分人/分值</td>
            <td class="" colspan="23">
                <#if vo?? && vo.raterUid??>
                    <#if vo?? && vo.raterName??>
                        ${vo.raterName}/${vo.rank}分
                        <#if vo?? && vo.rankComment??>
                            ${vo.rankComment}
                        </#if>
                    </#if>
                <#else>
                    --
                </#if>
            </td>
        </tr>
    </table>
    </body>

</html>