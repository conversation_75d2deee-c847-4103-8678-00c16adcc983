common.date=\u65E5\u671F
common.createTime=\u521b\u5efa\u65f6\u95f4
common.phone=\u624b\u673a\u53f7
common.email=\u90ae\u7bb1
common.accountName=\u5ba2\u6237\u540d
common.endTime=\u7ed3\u675f\u65f6\u95f4
common.commercialFullName=\u6240\u5c5e\u5546\u6237
common.userName=\u5ba2\u6237\u540d\u79f0
common.status=\u72b6\u6001
charger.order.summary=\u6c47\u603b
charger.order.sharpTime=\u5c16\u65f6
charger.order.peakTime=\u5CF0\u65F6
charger.order.shoulderTime=\u5E73\u65F6
charger.order.offPeakTime=\u8C37\u65F6
charger.order.summaryStatistics=\u8ba2\u5355\u6c47\u603b
charger.order.dailySummary=\u6BCF\u65E5\u6C40\u603B
charger.order.totalNumber=\u8BA2\u5355\u603B\u6570\u0028\u6761\u0029
charger.order.totalPrice=\u8ba2\u5355\u603b\u91d1\u989d\u0028\u5143\u0029
charger.order.totalElecCosts=\u603b\u7535\u8d39\u0028\u5143\u0029
charger.order.totalServiceCosts=\u603b\u670d\u52a1\u8d39\u0028\u5143\u0029
charger.order.totalOrderPower=\u8ba2\u5355\u603b\u7535\u91cf\u0028\u006b\u0057\u00b7\u0068\u0029
charger.order.standardServiceCosts=\u6807\u51c6\u670d\u52a1\u8d39\u0028\u5143\u0029
charger.order.standardElecCosts=\u6807\u51c6\u7535\u8d39\u0028\u5143\u0029
charger.order.totalAmountOfCharging=\u5145\u7535\u603b\u91d1\u989d\u0028\u5143\u0029
\u8BA2\u5355\u660E\u7EC6=\u8BA2\u5355\u660E\u7EC6
charger.order.orderNo=\u8ba2\u5355\u7f16\u53f7
charger.order.billNo=\u5173\u8054\u8d26\u5355\u53f7
charger.order.payOrderNo=\u652f\u4ed8\u5355\u53f7
charger.order.refundOrderNo=\u9000\u6b3e\u5355\u53f7
charger.order.orderStatus=\u8ba2\u5355\u72b6\u6001
charger.order.payAccountName=\u6263\u6b3e\u8d26\u6237
charger.order.source=\u6765\u6e90
charger.order.bootMode=\u542f\u52a8\u65b9\u5f0f
charger.order.cardType=\u5361\u7c7b\u578b
charger.order.brand=\u54c1\u724c
charger.order.model=\u578b\u53f7
charger.order.bmsVoltage=\u8f85\u7535\u0028\u0056\u0029
charger.order.gbVer=\u56fd\u6807
charger.order.stationName=\u7ad9\u70b9\u540d\u79f0
charger.order.stationCode=\u7ad9\u70b9\u7f16\u53f7
charger.order.evseCode=\u7535\u6869\u7f16\u53f7
charger.order.evseName=\u7535\u6869\u540d\u79f0
charger.order.supplyType=\u7535\u6869\u7c7b\u578b
charger.order.connectorNo=\u67aa\u5934\u7f16\u53f7
charger.order.connectorName=\u67aa\u5934\u540d\u79f0
charger.order.connectorId=\u67aa\u5934\u6807\u8bc6
charger.order.chargeStartTime=\u5145\u7535\u5f00\u59cb\u65f6\u95f4
charger.order.chargeEndTime=\u5145\u7535\u7ed3\u675f\u65f6\u95f4
charger.order.uploadTime=\u4e0a\u4f20\u65f6\u95f4
charger.order.duration=\u8ba2\u5355\u65f6\u957f
charger.order.startSoc=\u5145\u7535\u524d\u0053\u004f\u0043\u0028\u0025\u0029
charger.order.stopSoc=\u5145\u7535\u540e\u0053\u004f\u0043\u0028\u0025\u0029
charger.order.stopReason=\u505c\u5145\u539f\u56e0
charger.order.abnormal=\u5f02\u5e38\u539f\u56e0
charger.order.tipElectricity=\u5c16\u65f6\u7535\u91cf
charger.order.tipServicePrice=\u5c16\u65f6\u670d\u52a1\u8d39
charger.order.tipServiceUnit=\u5c16\u65f6\u670d\u52a1\u8d39\u5355\u4ef7
charger.order.tipElecPrice=\u5c16\u65f6\u7535\u8d39
charger.order.tipElectricUnit=\u5c16\u65f6\u7535\u8d39\u5355\u4ef7
charger.order.tipSumPrice=\u5c16\u65f6\u5408\u8ba1\u8d39\u7528
charger.order.peakElectricity=\u5cf0\u65f6\u7535\u91cf
charger.order.peakServicePrice=\u5cf0\u65f6\u670d\u52a1\u8d39
charger.order.peakServiceUnit=\u5cf0\u65f6\u670d\u52a1\u8d39\u5355\u4ef7
charger.order.peakElecPrice=\u5cf0\u65f6\u7535\u8d39
charger.order.peakElectricUnit=\u5cf0\u65f6\u7535\u8d39\u5355\u4ef7
charger.order.peakSumPrice=\u5cf0\u65f6\u5408\u8ba1\u8d39\u7528
charger.order.flatElectricity=\u5e73\u65f6\u7535\u91cf
charger.order.flatServicePrice=\u5e73\u65f6\u670d\u52a1\u8d39
charger.order.flatServiceUnit=\u5e73\u65f6\u670d\u52a1\u8d39\u5355\u4ef7
charger.order.flatElecPrice=\u5e73\u65f6\u7535\u8d39
charger.order.flatElectricUnit=\u5e73\u65f6\u7535\u8d39\u5355\u4ef7
charger.order.flatSumPrice=\u5e73\u65f6\u5408\u8ba1\u8d39\u7528
charger.order.valleyElectricity=\u8c37\u65f6\u7535\u91cf
charger.order.valleyServicePrice=\u8c37\u65f6\u670d\u52a1\u8d39
charger.order.valleyServiceUnit=\u8c37\u65f6\u670d\u52a1\u8d39\u5355\u4ef7
charger.order.valleyElecPrice=\u8c37\u65f6\u7535\u8d39
charger.order.valleyElectricUnit=\u8c37\u65f6\u7535\u8d39\u5355\u4ef7
charger.order.valleySumPrice=\u8c37\u65f6\u5408\u8ba1\u8d39\u7528
charger.order.orderElectricity=\u603b\u7535\u91cf\uff08\u006b\u0057\u0068\uff09
charger.order.servOriginFee=\u6807\u51c6\u670d\u52a1\u8d39\uFF08\u5143\uFF09
charger.order.servicePrice=\u603b\u670d\u52a1\u8d39\uFF08\u5143\uFF09
charger.order.elecOriginFee=\u6807\u51c6\u7535\u8d39\uFF08\u5143\uFF09
charger.order.elecPrice=\u603b\u7535\u8d39\uFF08\u5143\uFF09
charger.order.discount=\u7535\u635f\u91d1\u989d\uFF08\u5143\uFF09
charger.order.principalAmount=\u5b9e\u9645\u91d1\u989d\u6d88\u8d39\uFF08\u5143\uFF09
charger.order.freeGoldAmount=\u8d60\u9001\u91d1\u989d\u6d88\u8d39\uFF08\u5143\uFF09
charger.order.payTime=\u652f\u4ed8\u65f6\u95f4
charger.order.processType=\u8ba2\u5355\u5904\u7406\u7c7b\u578b
charger.order.startElectricity=\u7535\u8868\u5f00\u59cb\u8bfb\u6570
charger.order.endElectricity=\u7535\u8868\u7ed3\u675f\u8bfb\u6570
#订单导出map获取的相关字段start
#来源-其他
\u5176\u4ed6=\u5176\u4ed6
#来源-设备触发
\u8bbe\u5907\u89e6\u53d1=\u8bbe\u5907\u89e6\u53d1
#来源-用户应用
\u7528\u6237\u5e94\u7528=\u7528\u6237\u5e94\u7528
#订单状态-订单未激活
\u8ba2\u5355\u672a\u6fc0\u6d3b=\u8ba2\u5355\u672a\u6fc0\u6d3b
#订单状态-充电中
\u5145\u7535\u4e2d=\u5145\u7535\u4e2d
#订单状态-待支付
\u5f85\u652f\u4ed8=\u5f85\u652f\u4ed8
#订单状态-已结算
\u5df2\u7ed3\u7b97=\u5df2\u7ed3\u7b97
#订单状态-离线中
\u79bb\u7ebf\u4e2d=\u79bb\u7ebf\u4e2d
#扣款账户-个人
\u4e2a\u4eba=\u4e2a\u4eba
#启动方式-设备端自主
\u8bbe\u5907\u7aef\u81ea\u4e3b=\u8bbe\u5907\u7aef\u81ea\u4e3b
#启动方式-在线卡鉴权
\u5728\u7ebf\u5361\u9274\u6743=\u5728\u7ebf\u5361\u9274\u6743
#启动方式-VIN识别鉴权
\u0056\u0049\u004e\u8bc6\u522b\u9274\u6743=\u0056\u0049\u004e\u8bc6\u522b\u9274\u6743
#启动方式-VIN白名单
\u0056\u0049\u004e\u767d\u540d\u5355=\u0056\u0049\u004e\u767d\u540d\u5355
#异常原因-心跳超时
\u5fc3\u8df3\u8d85\u65f6=\u5fc3\u8df3\u8d85\u65f6
#异常原因-订单更新超时
\u8ba2\u5355\u66f4\u65b0\u8d85\u65f6=\u8ba2\u5355\u66f4\u65b0\u8d85\u65f6
#异常原因-订单电量越限
\u8ba2\u5355\u7535\u91cf\u8d8a\u9650=\u8ba2\u5355\u7535\u91cf\u8d8a\u9650
#异常原因-订单金额越限
\u8ba2\u5355\u91d1\u989d\u8d8a\u9650=\u8ba2\u5355\u91d1\u989d\u8d8a\u9650
#异常原因-充电中超时
\u5145\u7535\u4e2d\u8d85\u65f6=\u5145\u7535\u4e2d\u8d85\u65f6
#异常原因-启动中超时
\u542f\u52a8\u4e2d\u8d85\u65f6=\u542f\u52a8\u4e2d\u8d85\u65f6
#异常原因-充电中停用
\u5145\u7535\u4e2d\u505c\u7528=\u5145\u7535\u4e2d\u505c\u7528
#异常原因-即充即退退款失败
\u5373\u5145\u5373\u9000\u9000\u6b3e\u5931\u8d25=\u5373\u5145\u5373\u9000\u9000\u6b3e\u5931\u8d25
#异常原因-支付失败
\u652f\u4ed8\u5931\u8d25=\u652f\u4ed8\u5931\u8d25
#异常原因-订单冻结金额异常
\u8ba2\u5355\u51bb\u7ed3\u91d1\u989d\u5f02\u5e38=\u8ba2\u5355\u51bb\u7ed3\u91d1\u989d\u5f02\u5e38
#异常原因-账户异常
\u8d26\u6237\u5f02\u5e38=\u8d26\u6237\u5f02\u5e38
#异常原因-订单电量异常
\u8ba2\u5355\u7535\u91cf\u5f02\u5e38=\u8ba2\u5355\u7535\u91cf\u5f02\u5e38
#异常原因-订单金额异常
\u8ba2\u5355\u91d1\u989d\u5f02\u5e38=\u8ba2\u5355\u91d1\u989d\u5f02\u5e38
#订单处理类型-正常处理
\u6b63\u5e38\u5904\u7406=\u6b63\u5e38\u5904\u7406
#订单处理类型-异常手工未处理
\u5f02\u5e38\u624b\u5de5\u672a\u5904\u7406=\u5f02\u5e38\u624b\u5de5\u672a\u5904\u7406
#订单处理类型-异常手工处理
\u5f02\u5e38\u624b\u5de5\u5904\u7406=\u5f02\u5e38\u624b\u5de5\u5904\u7406
#停充原因-正常停充
\u6b63\u5e38\u505c\u5145=\u6b63\u5e38\u505c\u5145
#停充原因-异常停充
\u5f02\u5e38\u505c\u5145=\u5f02\u5e38\u505c\u5145
#停充原因-充满
\u5145\u6ee1=\u5145\u6ee1
#停充原因-桩端手动停充-停充按钮
\u6869\u7aef\u624b\u52a8\u505c\u5145-\u505c\u5145\u6309\u94ae=\u6869\u7aef\u624b\u52a8\u505c\u5145-\u505c\u5145\u6309\u94ae
#停充原因-用户停充
\u7528\u6237\u505c\u5145=\u7528\u6237\u505c\u5145
#停充原因-急停故障
\u6025\u505c\u6545\u969c=\u6025\u505c\u6545\u969c
#停充原因-接收超时BCL
\u63a5\u6536\u8d85\u65f6\u0042\u0043\u004c=\u63a5\u6536\u8d85\u65f6\u0042\u0043\u004c
#停充原因-未知
\u672a\u77e5=\u672a\u77e5
#停充原因-接收超时BRM
\u63a5\u6536\u8d85\u65f6\u0042\u0052\u004d=\u63a5\u6536\u8d85\u65f6\u0042\u0052\u004d
#停充原因-K1K2 前后电压不一致
\u004b\u0031\u004b\u0032\u0020\u524d\u540e\u7535\u538b\u4e0d\u4e00\u81f4=\u004b\u0031\u004b\u0032\u0020\u524d\u540e\u7535\u538b\u4e0d\u4e00\u81f4
#订单导出map获取的相关字段end
waring.warningNo=\u544a\u8b66\u7f16\u53f7
warning.startTime=\u53d1\u751f\u65f6\u95f4
warning.warningDeviceNo=\u8bbe\u5907\u7f16\u53f7\u0028\u540d\u79f0\u0029
warning.plugIdx=\u67aa\u5934\u5e8f\u53f7
warning.siteName=\u544a\u8b66\u6240\u5c5e\u573a\u7ad9
warning.waringName=\u544a\u8b66\u540d\u79f0
warning.remark=\u8865\u5145\u8bf4\u660e
waring.status=\u544a\u8b66\u72b6\u6001
#告警状态-未结束
\u672a\u7ed3\u675f=\u672a\u7ed3\u675f
#告警状态-自动结束
\u81ea\u52a8\u7ed3\u675f=\u81ea\u52a8\u7ed3\u675f
#告警状态-手动结束
\u624b\u52a8\u7ed3\u675f=\u624b\u52a8\u7ed3\u675f
#告警原因-桩离线
\u6869\u79bb\u7ebf=\u6869\u79bb\u7ebf
#告警原因-充电机检测到充电电压异常
\u5145\u7535\u673a\u68c0\u6d4b\u5230\u5145\u7535\u7535\u538b\u5f02\u5e38=\u5145\u7535\u673a\u68c0\u6d4b\u5230\u5145\u7535\u7535\u538b\u5f02\u5e38
#告警原因-交流-S2开关异常
\u4ea4\u6d41\u002d\u0053\u0032\u5f00\u5173\u5f02\u5e38=\u4ea4\u6d41\u002d\u0053\u0032\u5f00\u5173\u5f02\u5e38
#告警原因-交流-枪连接异常
\u4ea4\u6d41\u002d\u67aa\u8fde\u63a5\u5f02\u5e38=\u4ea4\u6d41\u002d\u67aa\u8fde\u63a5\u5f02\u5e38
#告警原因-绝缘检测表通讯故障
\u7edd\u7f18\u68c0\u6d4b\u8868\u901a\u8baf\u6545\u969c=\u7edd\u7f18\u68c0\u6d4b\u8868\u901a\u8baf\u6545\u969c
#告警原因-UI-安全管理板通讯中断
\u0055\u0049\u002d\u5b89\u5168\u7ba1\u7406\u677f\u901a\u8baf\u4e2d\u65ad=\u0055\u0049\u002d\u5b89\u5168\u7ba1\u7406\u677f\u901a\u8baf\u4e2d\u65ad
#告警原因-连接器故障(导引电路检测到故障)- CC1信号
\u8fde\u63a5\u5668\u6545\u969c\u0028\u5bfc\u5f15\u7535\u8def\u68c0\u6d4b\u5230\u6545\u969c\u0029\u002d\u0020\u0043\u0043\u0031\u4fe1\u53f7=\u8fde\u63a5\u5668\u6545\u969c\u0028\u5bfc\u5f15\u7535\u8def\u68c0\u6d4b\u5230\u6545\u969c\u0029\u002d\u0020\u0043\u0043\u0031\u4fe1\u53f7
#告警原因-连接器故障(导引电路检测到故障)-枪过温
\u8fde\u63a5\u5668\u6545\u969c\u0028\u5bfc\u5f15\u7535\u8def\u68c0\u6d4b\u5230\u6545\u969c\u0029\u002d\u67aa\u8fc7\u6e29=\u8fde\u63a5\u5668\u6545\u969c\u0028\u5bfc\u5f15\u7535\u8def\u68c0\u6d4b\u5230\u6545\u969c\u0029\u002d\u67aa\u8fc7\u6e29
#告警原因-电池电压过低
\u7535\u6c60\u7535\u538b\u8fc7\u4f4e=\u7535\u6c60\u7535\u538b\u8fc7\u4f4e
#告警备注-停止当前充电，允许后续充电
\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535=\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535
#告警备注-离线原因：上电重启
\u79bb\u7ebf\u539f\u56e0\uff1a\u4e0a\u7535\u91cd\u542f=\u79bb\u7ebf\u539f\u56e0\uff1a\u4e0a\u7535\u91cd\u542f
#告警备注-停止当前充电，允许后续充电，提醒用户检查车辆状况
\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u63d0\u9192\u7528\u6237\u68c0\u67e5\u8f66\u8f86\u72b6\u51b5=\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u63d0\u9192\u7528\u6237\u68c0\u67e5\u8f66\u8f86\u72b6\u51b5
#告警备注-停止当前充电，允许后续充电，通知运维检查
\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u901a\u77e5\u8fd0\u7ef4\u68c0\u67e5=\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u901a\u77e5\u8fd0\u7ef4\u68c0\u67e5
#告警备注-停止当前充电，允许后续充电，提示用户枪连接异常
\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u63d0\u793a\u7528\u6237\u67aa\u8fde\u63a5\u5f02\u5e38=\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u63d0\u793a\u7528\u6237\u67aa\u8fde\u63a5\u5f02\u5e38
#告警备注-停止当前充电，允许后续充电，提示用户待枪冷却后再次尝试充电
\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u63d0\u793a\u7528\u6237\u5f85\u67aa\u51b7\u5374\u540e\u518d\u6b21\u5c1d\u8bd5\u5145\u7535=\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u63d0\u793a\u7528\u6237\u5f85\u67aa\u51b7\u5374\u540e\u518d\u6b21\u5c1d\u8bd5\u5145\u7535
#告警备注-BCP 报文字节数异常或BCP 报文电池电压数值越界
\u0042\u0043\u0050\u0020\u62a5\u6587\u5b57\u8282\u6570\u5f02\u5e38\u6216\u0042\u0043\u0050\u0020\u62a5\u6587\u7535\u6c60\u7535\u538b\u6570\u503c\u8d8a\u754c=\u0042\u0043\u0050\u0020\u62a5\u6587\u5b57\u8282\u6570\u5f02\u5e38\u6216\u0042\u0043\u0050\u0020\u62a5\u6587\u7535\u6c60\u7535\u538b\u6570\u503c\u8d8a\u754c
#告警备注-交流-CP电压采样异常
\u4ea4\u6d41\u002d\u0043\u0050\u7535\u538b\u91c7\u6837\u5f02\u5e38=\u4ea4\u6d41\u002d\u0043\u0050\u7535\u538b\u91c7\u6837\u5f02\u5e38
#告警备注-交流-DWIN通讯故障
\u4ea4\u6d41\u002d\u0044\u0057\u0049\u004e\u901a\u8baf\u6545\u969c=\u4ea4\u6d41\u002d\u0044\u0057\u0049\u004e\u901a\u8baf\u6545\u969c
#告警备注-交流-交流接触器
\u4ea4\u6d41\u002d\u4ea4\u6d41\u63a5\u89e6\u5668=\u4ea4\u6d41\u002d\u4ea4\u6d41\u63a5\u89e6\u5668
#告警备注-交流-交流欠压
\u4ea4\u6d41\u002d\u4ea4\u6d41\u6b20\u538b=\u4ea4\u6d41\u002d\u4ea4\u6d41\u6b20\u538b
#告警备注-交流-交流过压
\u4ea4\u6d41\u002d\u4ea4\u6d41\u8fc7\u538b=\u4ea4\u6d41\u002d\u4ea4\u6d41\u8fc7\u538b
#告警备注-交流-充电电流为0
\u4ea4\u6d41\u002d\u5145\u7535\u7535\u6d41\u4e3a\u0030=\u4ea4\u6d41\u002d\u5145\u7535\u7535\u6d41\u4e3a\u0030
#告警备注-交流-急停故障
\u4ea4\u6d41\u002d\u6025\u505c\u6545\u969c=\u4ea4\u6d41\u002d\u6025\u505c\u6545\u969c
#告警备注-交流-枪低温
\u4ea4\u6d41\u002d\u67aa\u4f4e\u6e29=\u4ea4\u6d41\u002d\u67aa\u4f4e\u6e29
#告警备注-交流-枪体高温
\u4ea4\u6d41\u002d\u67aa\u4f53\u9ad8\u6e29=\u4ea4\u6d41\u002d\u67aa\u4f53\u9ad8\u6e29
#告警备注-交流-枪温度传感器故障
\u4ea4\u6d41\u002d\u67aa\u6e29\u5ea6\u4f20\u611f\u5668\u6545\u969c=\u4ea4\u6d41\u002d\u67aa\u6e29\u5ea6\u4f20\u611f\u5668\u6545\u969c
#告警备注-交流-枪过流
\u4ea4\u6d41\u002d\u67aa\u8fc7\u6d41=\u4ea4\u6d41\u002d\u67aa\u8fc7\u6d41
#告警备注-交流-枪高温
\u4ea4\u6d41\u002d\u67aa\u9ad8\u6e29=\u4ea4\u6d41\u002d\u67aa\u9ad8\u6e29
#告警备注-交流-桩温度传感器故障
\u4ea4\u6d41\u002d\u6869\u6e29\u5ea6\u4f20\u611f\u5668\u6545\u969c=\u4ea4\u6d41\u002d\u6869\u6e29\u5ea6\u4f20\u611f\u5668\u6545\u969c
#告警备注-交流-电表通讯
\u4ea4\u6d41\u002d\u7535\u8868\u901a\u8baf=\u4ea4\u6d41\u002d\u7535\u8868\u901a\u8baf
#告警备注-交流-读卡器
\u4ea4\u6d41\u002d\u8bfb\u5361\u5668=\u4ea4\u6d41\u002d\u8bfb\u5361\u5668
#告警备注-交流-门禁
\u4ea4\u6d41\u002d\u95e8\u7981=\u4ea4\u6d41\u002d\u95e8\u7981
#告警备注-停止当前充电，允许后续充电，提示用户拔枪重新尝试充电
\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u63d0\u793a\u7528\u6237\u62d4\u67aa\u91cd\u65b0\u5c1d\u8bd5\u5145\u7535=\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u63d0\u793a\u7528\u6237\u62d4\u67aa\u91cd\u65b0\u5c1d\u8bd5\u5145\u7535
#告警备注-停止当前充电，允许后续充电，提示用户枪锁异常
\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u63d0\u793a\u7528\u6237\u67aa\u9501\u5f02\u5e38=\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u63d0\u793a\u7528\u6237\u67aa\u9501\u5f02\u5e38
#告警备注-停止当前充电，允许后续充电，提示用户检查车辆状态
\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u63d0\u793a\u7528\u6237\u68c0\u67e5\u8f66\u8f86\u72b6\u6001=\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u63d0\u793a\u7528\u6237\u68c0\u67e5\u8f66\u8f86\u72b6\u6001
#告警备注-停止当前充电，允许后续充电，提示用户重新插枪充电
\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u63d0\u793a\u7528\u6237\u91cd\u65b0\u63d2\u67aa\u5145\u7535=\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u63d0\u793a\u7528\u6237\u91cd\u65b0\u63d2\u67aa\u5145\u7535
#告警备注-停止当前充电，故障恢复后允许后续充电，长时间未恢复则通知运维检查桩体
\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u6545\u969c\u6062\u590d\u540e\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u957f\u65f6\u95f4\u672a\u6062\u590d\u5219\u901a\u77e5\u8fd0\u7ef4\u68c0\u67e5\u6869\u4f53=\u505c\u6b62\u5f53\u524d\u5145\u7535\uff0c\u6545\u969c\u6062\u590d\u540e\u5141\u8bb8\u540e\u7eed\u5145\u7535\uff0c\u957f\u65f6\u95f4\u672a\u6062\u590d\u5219\u901a\u77e5\u8fd0\u7ef4\u68c0\u67e5\u6869\u4f53
#告警备注-充电中有电压，电流持续3分钟小于1A
\u5145\u7535\u4e2d\u6709\u7535\u538b\uff0c\u7535\u6d41\u6301\u7eed\u0033\u5206\u949f\u5c0f\u4e8e\u0031\u0041=\u5145\u7535\u4e2d\u6709\u7535\u538b\uff0c\u7535\u6d41\u6301\u7eed\u0033\u5206\u949f\u5c0f\u4e8e\u0031\u0041
#告警备注-充电中桩掉电
\u5145\u7535\u4e2d\u6869\u6389\u7535=\u5145\u7535\u4e2d\u6869\u6389\u7535
#告警备注-判断BHM报文最高允许充电总电压低于充电机最低输出电压
\u5224\u65ad\u0042\u0048\u004d\u62a5\u6587\u6700\u9ad8\u5141\u8bb8\u5145\u7535\u603b\u7535\u538b\u4f4e\u4e8e\u5145\u7535\u673a\u6700\u4f4e\u8f93\u51fa\u7535\u538b=\u5224\u65ad\u0042\u0048\u004d\u62a5\u6587\u6700\u9ad8\u5141\u8bb8\u5145\u7535\u603b\u7535\u538b\u4f4e\u4e8e\u5145\u7535\u673a\u6700\u4f4e\u8f93\u51fa\u7535\u538b
#告警备注-告警通知运维，不影响充电
\u544a\u8b66\u901a\u77e5\u8fd0\u7ef4\uff0c\u4e0d\u5f71\u54cd\u5145\u7535=\u544a\u8b66\u901a\u77e5\u8fd0\u7ef4\uff0c\u4e0d\u5f71\u54cd\u5145\u7535
#告警备注-告警，不影响充电
\u544a\u8b66\uff0c\u4e0d\u5f71\u54cd\u5145\u7535=\u544a\u8b66\uff0c\u4e0d\u5f71\u54cd\u5145\u7535
#告警备注-故障枪停充, 不影响下次充电
\u6545\u969c\u67aa\u505c\u5145\u002c\u0020\u4e0d\u5f71\u54cd\u4e0b\u6b21\u5145\u7535=\u6545\u969c\u67aa\u505c\u5145\u002c\u0020\u4e0d\u5f71\u54cd\u4e0b\u6b21\u5145\u7535
#告警备注-故障枪停充并禁用，其他枪不受影响
\u6545\u969c\u67aa\u505c\u5145\u5e76\u7981\u7528\uff0c\u5176\u4ed6\u67aa\u4e0d\u53d7\u5f71\u54cd=\u6545\u969c\u67aa\u505c\u5145\u5e76\u7981\u7528\uff0c\u5176\u4ed6\u67aa\u4e0d\u53d7\u5f71\u54cd
#告警备注-故障枪停充，不影响下次充电
\u6545\u969c\u67aa\u505c\u5145\uff0c\u4e0d\u5f71\u54cd\u4e0b\u6b21\u5145\u7535=\u6545\u969c\u67aa\u505c\u5145\uff0c\u4e0d\u5f71\u54cd\u4e0b\u6b21\u5145\u7535
#告警备注-故障枪（挂载在故障 UI 板下的枪）停用，故障恢复后可充电
\u6545\u969c\u67aa\uff08\u6302\u8f7d\u5728\u6545\u969c\u0020\u0055\u0049\u0020\u677f\u4e0b\u7684\u67aa\uff09\u505c\u7528\uff0c\u6545\u969c\u6062\u590d\u540e\u53ef\u5145\u7535=\u6545\u969c\u67aa\uff08\u6302\u8f7d\u5728\u6545\u969c\u0020\u0055\u0049\u0020\u677f\u4e0b\u7684\u67aa\uff09\u505c\u7528\uff0c\u6545\u969c\u6062\u590d\u540e\u53ef\u5145\u7535
#告警备注-整桩停充，所有枪、弓禁用
\u6574\u6869\u505c\u5145\uff0c\u6240\u6709\u67aa\u3001\u5f13\u7981\u7528=\u6574\u6869\u505c\u5145\uff0c\u6240\u6709\u67aa\u3001\u5f13\u7981\u7528
#告警备注-整桩停充，所有枪禁用
\u6574\u6869\u505c\u5145\uff0c\u6240\u6709\u67aa\u7981\u7528=\u6574\u6869\u505c\u5145\uff0c\u6240\u6709\u67aa\u7981\u7528
#告警备注-禁止充电，通知运维检查桩体
\u7981\u6b62\u5145\u7535\uff0c\u901a\u77e5\u8fd0\u7ef4\u68c0\u67e5\u6869\u4f53=\u7981\u6b62\u5145\u7535\uff0c\u901a\u77e5\u8fd0\u7ef4\u68c0\u67e5\u6869\u4f53
#告警备注-禁用充电功能，通知运维检查
\u7981\u7528\u5145\u7535\u529f\u80fd\uff0c\u901a\u77e5\u8fd0\u7ef4\u68c0\u67e5=\u7981\u7528\u5145\u7535\u529f\u80fd\uff0c\u901a\u77e5\u8fd0\u7ef4\u68c0\u67e5
#告警备注-离线原因：其它
\u79bb\u7ebf\u539f\u56e0\uff1a\u5176\u5b83=\u79bb\u7ebf\u539f\u56e0\uff1a\u5176\u5b83
#告警备注-离线原因：失去连接
\u79bb\u7ebf\u539f\u56e0\uff1a\u5931\u53bb\u8fde\u63a5=\u79bb\u7ebf\u539f\u56e0\uff1a\u5931\u53bb\u8fde\u63a5
#告警备注-离线原因：报文签名不匹配
\u79bb\u7ebf\u539f\u56e0\uff1a\u62a5\u6587\u7b7e\u540d\u4e0d\u5339\u914d=\u79bb\u7ebf\u539f\u56e0\uff1a\u62a5\u6587\u7b7e\u540d\u4e0d\u5339\u914d
#告警备注-预充电阶段检测到K1K2外侧电压低于充电机最低输出电压15V
\u9884\u5145\u7535\u9636\u6bb5\u68c0\u6d4b\u5230\u004b\u0031\u004b\u0032\u5916\u4fa7\u7535\u538b\u4f4e\u4e8e\u5145\u7535\u673a\u6700\u4f4e\u8f93\u51fa\u7535\u538b\u0031\u0035\u0056=\u9884\u5145\u7535\u9636\u6bb5\u68c0\u6d4b\u5230\u004b\u0031\u004b\u0032\u5916\u4fa7\u7535\u538b\u4f4e\u4e8e\u5145\u7535\u673a\u6700\u4f4e\u8f93\u51fa\u7535\u538b\u0031\u0035\u0056
#告警备注-预充电阶段检测到K1K2外侧电压高于充电机最高输出电压15V
\u9884\u5145\u7535\u9636\u6bb5\u68c0\u6d4b\u5230\u004b\u0031\u004b\u0032\u5916\u4fa7\u7535\u538b\u9ad8\u4e8e\u5145\u7535\u673a\u6700\u9ad8\u8f93\u51fa\u7535\u538b\u0031\u0035\u0056=\u9884\u5145\u7535\u9636\u6bb5\u68c0\u6d4b\u5230\u004b\u0031\u004b\u0032\u5916\u4fa7\u7535\u538b\u9ad8\u4e8e\u5145\u7535\u673a\u6700\u9ad8\u8f93\u51fa\u7535\u538b\u0031\u0035\u0056
#告警明细
\u544a\u8b66\u660e\u7ec6=\u544a\u8b66\u660e\u7ec6
car.carDepart=\u8f66\u961f\u540d\u79f0
car.carNo=\u8f66\u724c\u53f7
car.lineNum=\u7ebf\u8def
car.carNum=\u8f66\u8f86\u81ea\u7f16\u53f7
car.carDepartWithoutName=\u8f66\u961f
#在线卡信息
\u5728\u7ebf\u5361\u4fe1\u606f=\u5728\u7ebf\u5361\u4fe1\u606f
card.cardNo=\u5361\u53f7
card.cardName=\u5361\u540d\u79f0
card.usableStationCount=\u6307\u5b9a\u573a\u7ad9\u6570\u91cf
#卡片状态-未激活
\u672a\u6fc0\u6d3b=\u672a\u6fc0\u6d3b
#卡片状态-已激活
\u5df2\u6fc0\u6d3b=\u5df2\u6fc0\u6d3b
#卡片状态-卡锁定
\u5361\u9501\u5b9a=\u5361\u9501\u5b9a
#卡片状态-已挂失
\u5df2\u6302\u5931=\u5df2\u6302\u5931
#卡片状态-已失效
\u5df2\u5931\u6548=\u5df2\u5931\u6548
#卡片状态-已过期
\u5df2\u8fc7\u671f=\u5df2\u8fc7\u671f
#VIN码信息
\u0056\u0049\u004e\u7801\u4fe1\u606f=\u0056\u0049\u004e\u7801\u4fe1\u606f
vin.auth.status.opening=\u5f00\u542f
vin.auth.status.closing=\u5173\u95ed
vin.vinCode=\u0056\u0049\u004e\u7801
#VIN-在线认证可用站点数
vin.usableStationCount=\u5728\u7ebf\u8ba4\u8bc1\u53ef\u7528\u7ad9\u70b9\u6570
#本地认证
vin.authStatus=\u672c\u5730\u8ba4\u8bc1
#充值信息
\u5145\u503c\u4fe1\u606f=\u5145\u503c\u4fe1\u606f
payBill.orderNumber=\u8ba2\u5355\u53f7
payBill.source=\u5145\u503c\u6765\u6e90
payBill.flowType=\u5145\u503c\u7c7b\u578b
payBill.actualAmount=\u5b9e\u9645\u91d1\u989d\uff08\u5143\uff09
payBill.freeAmount=\u8d60\u9001\u91d1\u989d\uff08\u5143\uff09
payBill.amountBefore=\u5145\u503c\u524d\u4f59\u989d\uff08\u5143\uff09
payBill.amountAfter=\u5145\u503c\u540e\u4f59\u989d\uff08\u5143\uff09
payBill.payAccountName=\u8d26\u6237\u540d\u79f0
payBill.payChannelDesc=\u652f\u4ed8\u65b9\u5f0f
payBill.status=\u652f\u4ed8\u72b6\u6001
payBill.account=\u5230\u8d26\u8d26\u6237
payBill.rechargeTime=\u5145\u503c\u65f6\u95f4
payBill.operatorName=\u64cd\u4f5c\u4eba
#充值来源-平台充值(后台操作)
\u5e73\u53f0\u5145\u503c\u0028\u540e\u53f0\u64cd\u4f5c\u0029=\u5e73\u53f0\u5145\u503c\u0028\u540e\u53f0\u64cd\u4f5c\u0029
#充值类型-充值
\u5145\u503c=\u5145\u503c
#充值类型-减少
\u51cf\u5c11=\u51cf\u5c11
#支付方式-银行卡支付
\u94f6\u884c\u5361\u652f\u4ed8=\u94f6\u884c\u5361\u652f\u4ed8
#支付状态-已支付
\u5df2\u652f\u4ed8=\u5df2\u652f\u4ed8
#支付状态-未支付
\u672a\u652f\u4ed8=\u672a\u652f\u4ed8